#!/bin/bash

# Script de desenvolvimento para SmartEnergy Docker

COMPOSE_FILE="docker/docker-compose.linux.yml"

show_help() {
    echo "SmartEnergy Docker - Script de Desenvolvimento"
    echo ""
    echo "Uso: ./dev.sh [comando]"
    echo ""
    echo "Comandos disponíveis:"
    echo "  start       - Iniciar ambiente completo"
    echo "  stop        - Parar ambiente"
    echo "  restart     - Reiniciar ambiente"
    echo "  build       - Reconstruir aplicação"
    echo "  logs        - Ver logs em tempo real"
    echo "  status      - Status dos containers"
    echo "  shell       - Acessar shell da aplicação"
    echo "  db          - Acessar SQL Server"
    echo "  backup      - Fazer backup do banco"
    echo "  restore     - Restaurar backup do banco"
    echo "  clean       - Limpar containers e volumes"
    echo "  update      - Atualizar e reconstruir"
    echo "  help        - Mostrar esta ajuda"
    echo ""
}

start_env() {
    echo "🚀 Iniciando ambiente de desenvolvimento..."
    docker-compose -f $COMPOSE_FILE up -d
    echo "✅ Ambiente iniciado!"
    echo "🌐 Aplicação: http://localhost:8080"
    echo "🌐 Nginx: http://localhost"
}

stop_env() {
    echo "🛑 Parando ambiente..."
    docker-compose -f $COMPOSE_FILE down
    echo "✅ Ambiente parado!"
}

restart_env() {
    echo "🔄 Reiniciando ambiente..."
    docker-compose -f $COMPOSE_FILE restart
    echo "✅ Ambiente reiniciado!"
}

build_app() {
    echo "🔨 Reconstruindo aplicação..."
    docker-compose -f $COMPOSE_FILE build --no-cache smartenergy-app
    docker-compose -f $COMPOSE_FILE up -d smartenergy-app
    echo "✅ Aplicação reconstruída!"
}

show_logs() {
    echo "📋 Mostrando logs (Ctrl+C para sair)..."
    docker-compose -f $COMPOSE_FILE logs -f
}

show_status() {
    echo "📊 Status dos containers:"
    docker-compose -f $COMPOSE_FILE ps
    echo ""
    echo "💾 Uso de recursos:"
    docker stats --no-stream
}

access_shell() {
    echo "🐚 Acessando shell da aplicação..."
    docker exec -it smartenergy-app bash
}

access_db() {
    echo "🗄️  Acessando SQL Server..."
    docker exec -it smartenergy-sqlserver /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P 'SmartEnergy123!'
}

backup_db() {
    echo "💾 Fazendo backup do banco de dados..."
    mkdir -p backup
    BACKUP_FILE="backup/smartenergy_$(date +%Y%m%d_%H%M%S).bak"
    
    docker exec smartenergy-sqlserver /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P 'SmartEnergy123!' \
        -Q "BACKUP DATABASE SmartEnergyDB TO DISK = '/var/opt/mssql/backup/smartenergy.bak'"
    
    docker cp smartenergy-sqlserver:/var/opt/mssql/backup/smartenergy.bak $BACKUP_FILE
    
    echo "✅ Backup criado: $BACKUP_FILE"
}

restore_db() {
    echo "🔄 Restaurando backup do banco de dados..."
    
    if [ -z "$1" ]; then
        echo "❌ Especifique o arquivo de backup:"
        echo "   ./dev.sh restore backup/smartenergy_20231201_120000.bak"
        return 1
    fi
    
    if [ ! -f "$1" ]; then
        echo "❌ Arquivo de backup não encontrado: $1"
        return 1
    fi
    
    docker cp "$1" smartenergy-sqlserver:/var/opt/mssql/backup/restore.bak
    
    docker exec smartenergy-sqlserver /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P 'SmartEnergy123!' \
        -Q "RESTORE DATABASE SmartEnergyDB FROM DISK = '/var/opt/mssql/backup/restore.bak' WITH REPLACE"
    
    echo "✅ Backup restaurado!"
}

clean_env() {
    echo "🧹 Limpando ambiente..."
    
    read -p "⚠️  Isso removerá todos os containers, volumes e dados. Continuar? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose -f $COMPOSE_FILE down -v --rmi all
        docker system prune -f
        echo "✅ Ambiente limpo!"
    else
        echo "❌ Operação cancelada."
    fi
}

update_env() {
    echo "🔄 Atualizando ambiente..."
    
    # Parar ambiente
    docker-compose -f $COMPOSE_FILE down
    
    # Atualizar código (se estiver em git)
    if [ -d ".git" ]; then
        echo "📥 Atualizando código..."
        git pull
    fi
    
    # Reconstruir e iniciar
    docker-compose -f $COMPOSE_FILE build --no-cache
    docker-compose -f $COMPOSE_FILE up -d
    
    echo "✅ Ambiente atualizado!"
}

# Processar comando
case "$1" in
    start)
        start_env
        ;;
    stop)
        stop_env
        ;;
    restart)
        restart_env
        ;;
    build)
        build_app
        ;;
    logs)
        show_logs
        ;;
    status)
        show_status
        ;;
    shell)
        access_shell
        ;;
    db)
        access_db
        ;;
    backup)
        backup_db
        ;;
    restore)
        restore_db "$2"
        ;;
    clean)
        clean_env
        ;;
    update)
        update_env
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "❌ Comando inválido: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
