#!/bin/bash

# Script principal para gerenciar o ambiente Docker do SmartEnergy

DOCKER_DIR="docker"
COMPOSE_FILE_LINUX="$DOCKER_DIR/docker-compose.linux.yml"
COMPOSE_FILE_WINDOWS="$DOCKER_DIR/docker-compose.yml"

# Detectar sistema operacional
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        COMPOSE_FILE="$COMPOSE_FILE_LINUX"
        OS_TYPE="Linux"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
        COMPOSE_FILE="$COMPOSE_FILE_WINDOWS"
        OS_TYPE="Windows"
    else
        COMPOSE_FILE="$COMPOSE_FILE_LINUX"
        OS_TYPE="Linux (padrão)"
    fi
}

show_help() {
    echo "SmartEnergy Docker - Gerenciador de Ambiente"
    echo ""
    echo "Uso: ./docker.sh [comando]"
    echo ""
    echo "Comandos disponíveis:"
    echo "  init        - Inicializar ambiente (primeira vez)"
    echo "  start       - Iniciar ambiente"
    echo "  stop        - Parar ambiente"
    echo "  restart     - Reiniciar ambiente"
    echo "  build       - Reconstruir aplicação"
    echo "  logs        - Ver logs em tempo real"
    echo "  status      - Status dos containers"
    echo "  shell       - Acessar shell da aplicação"
    echo "  db          - Acessar SQL Server"
    echo "  backup      - Fazer backup do banco"
    echo "  restore     - Restaurar backup do banco"
    echo "  clean       - Limpar containers e volumes"
    echo "  update      - Atualizar e reconstruir"
    echo "  help        - Mostrar esta ajuda"
    echo ""
    echo "Sistema detectado: $OS_TYPE"
    echo "Arquivo compose: $COMPOSE_FILE"
    echo ""
}

init_env() {
    echo "🚀 Inicializando ambiente Docker do SmartEnergy..."
    
    # Verificar dependências
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker não está instalado. Por favor, instale o Docker primeiro."
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        echo "❌ Docker Compose não está instalado. Por favor, instale o Docker Compose primeiro."
        exit 1
    fi

    # Criar diretórios necessários
    echo "📁 Criando diretórios necessários..."
    mkdir -p data/sqlserver
    mkdir -p data/redis
    mkdir -p logs/app
    mkdir -p logs/nginx
    mkdir -p backup
    mkdir -p $DOCKER_DIR/nginx/ssl

    # Copiar configuração do Web.config para Docker
    if [ -f "SmartEnergy/Web.config" ] && [ ! -f "SmartEnergy/Web.config.original" ]; then
        echo "💾 Fazendo backup do Web.config original..."
        cp SmartEnergy/Web.config SmartEnergy/Web.config.original
    fi

    if [ -f "SmartEnergy/Web.Docker.config" ]; then
        echo "🔧 Aplicando configuração Docker..."
        cp SmartEnergy/Web.Docker.config SmartEnergy/Web.config
    fi

    echo "✅ Ambiente inicializado!"
    echo ""
    echo "📋 Próximos passos:"
    echo "   1. Execute: ./docker.sh start"
    echo "   2. Aguarde a inicialização completa"
    echo "   3. Acesse: http://localhost:8080"
    echo ""
}

start_env() {
    echo "🚀 Iniciando ambiente Docker..."
    echo "Sistema: $OS_TYPE"
    
    # Parar containers existentes
    docker-compose -f $COMPOSE_FILE down 2>/dev/null || true
    
    # Iniciar containers
    docker-compose -f $COMPOSE_FILE up -d
    
    if [ $? -eq 0 ]; then
        echo "✅ Ambiente iniciado com sucesso!"
        echo ""
        echo "🌐 Acesse a aplicação em:"
        echo "   - Aplicação: http://localhost:8080"
        echo "   - Nginx (proxy): http://localhost"
        echo "   - SQL Server: localhost:1433"
        echo "   - Redis: localhost:6379"
        echo ""
        echo "📊 Para ver logs: ./docker.sh logs"
    else
        echo "❌ Erro ao iniciar ambiente. Verificando logs..."
        docker-compose -f $COMPOSE_FILE logs
    fi
}

stop_env() {
    echo "🛑 Parando ambiente Docker..."
    docker-compose -f $COMPOSE_FILE down
    echo "✅ Ambiente parado!"
}

restart_env() {
    echo "🔄 Reiniciando ambiente Docker..."
    docker-compose -f $COMPOSE_FILE restart
    echo "✅ Ambiente reiniciado!"
}

build_app() {
    echo "🔨 Reconstruindo aplicação..."
    docker-compose -f $COMPOSE_FILE build --no-cache smartenergy-app
    docker-compose -f $COMPOSE_FILE up -d smartenergy-app
    echo "✅ Aplicação reconstruída!"
}

show_logs() {
    echo "📋 Mostrando logs (Ctrl+C para sair)..."
    docker-compose -f $COMPOSE_FILE logs -f
}

show_status() {
    echo "📊 Status dos containers:"
    docker-compose -f $COMPOSE_FILE ps
    echo ""
    echo "💾 Uso de recursos:"
    docker stats --no-stream
}

access_shell() {
    echo "🐚 Acessando shell da aplicação..."
    docker exec -it smartenergy-app bash
}

access_db() {
    echo "🗄️  Acessando SQL Server..."
    docker exec -it smartenergy-sqlserver /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P 'SmartEnergy123!'
}

backup_db() {
    echo "💾 Fazendo backup do banco de dados..."
    mkdir -p backup
    BACKUP_FILE="backup/smartenergy_$(date +%Y%m%d_%H%M%S).bak"
    
    docker exec smartenergy-sqlserver /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P 'SmartEnergy123!' \
        -Q "BACKUP DATABASE SmartEnergyDB TO DISK = '/var/opt/mssql/backup/smartenergy.bak'"
    
    docker cp smartenergy-sqlserver:/var/opt/mssql/backup/smartenergy.bak $BACKUP_FILE
    
    echo "✅ Backup criado: $BACKUP_FILE"
}

restore_db() {
    echo "🔄 Restaurando backup do banco de dados..."
    
    if [ -z "$1" ]; then
        echo "❌ Especifique o arquivo de backup:"
        echo "   ./docker.sh restore backup/smartenergy_20231201_120000.bak"
        return 1
    fi
    
    if [ ! -f "$1" ]; then
        echo "❌ Arquivo de backup não encontrado: $1"
        return 1
    fi
    
    docker cp "$1" smartenergy-sqlserver:/var/opt/mssql/backup/restore.bak
    
    docker exec smartenergy-sqlserver /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P 'SmartEnergy123!' \
        -Q "RESTORE DATABASE SmartEnergyDB FROM DISK = '/var/opt/mssql/backup/restore.bak' WITH REPLACE"
    
    echo "✅ Backup restaurado!"
}

clean_env() {
    echo "🧹 Limpando ambiente Docker..."
    
    read -p "⚠️  Isso removerá todos os containers, volumes e dados. Continuar? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose -f $COMPOSE_FILE down -v --rmi all
        docker system prune -f
        
        # Restaurar Web.config original
        if [ -f "SmartEnergy/Web.config.original" ]; then
            echo "🔧 Restaurando Web.config original..."
            cp SmartEnergy/Web.config.original SmartEnergy/Web.config
        fi
        
        echo "✅ Ambiente limpo!"
    else
        echo "❌ Operação cancelada."
    fi
}

update_env() {
    echo "🔄 Atualizando ambiente Docker..."
    
    # Parar ambiente
    docker-compose -f $COMPOSE_FILE down
    
    # Atualizar código (se estiver em git)
    if [ -d ".git" ]; then
        echo "📥 Atualizando código..."
        git pull
    fi
    
    # Reconstruir e iniciar
    docker-compose -f $COMPOSE_FILE build --no-cache
    docker-compose -f $COMPOSE_FILE up -d
    
    echo "✅ Ambiente atualizado!"
}

# Detectar sistema operacional
detect_os

# Processar comando
case "$1" in
    init)
        init_env
        ;;
    start)
        start_env
        ;;
    stop)
        stop_env
        ;;
    restart)
        restart_env
        ;;
    build)
        build_app
        ;;
    logs)
        show_logs
        ;;
    status)
        show_status
        ;;
    shell)
        access_shell
        ;;
    db)
        access_db
        ;;
    backup)
        backup_db
        ;;
    restore)
        restore_db "$2"
        ;;
    clean)
        clean_env
        ;;
    update)
        update_env
        ;;
    help|--help|-h|"")
        show_help
        ;;
    *)
        echo "❌ Comando inválido: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
