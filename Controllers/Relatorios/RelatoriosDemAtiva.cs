﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {
        // Relatorio Diario 
        // 0 -> Demanda Ativa e Fator de Potencia
        // 1 -> Demanda Reativa
        // 2 -> Consumo Ativo e Consumo Reativo
        // 3 -> <PERSON><PERSON> de Potencia (horario)
        // 4 -> <PERSON><PERSON> <PERSON> (Demanda Media / Demanda Maxima)
        // 5 -> Demanda nao utilizada
        // 6 -> Consumo Ativo segundo mercado livre
        // 7 -> <PERSON><PERSON> de Utilizacao (Demanda Media / Contrato)
        // 8 -> Consumo Ativo x Meta para Supervisao Mensal do Pao de Acucar
	    // 9 -> KPI para Economia no Consumo de Energia
        // 10 -> Energia Reativa Excedente
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_RelatDiario", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_RelatDiario(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, int id_simulacao_cenario, ref DATAHORA pdata_equipo, ref RELAT_DIARIO prelatorio, ref RELAT_DIARIO_ANALISE panalise, ref RELAT_DIARIO prelatorio_sim, ref RELAT_DIARIO_ANALISE panalise_sim);

        // Relatorio Semanal
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_RelatSemanal", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_RelatSemanal(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, int id_simulacao_cenario, ref DATAHORA pdata_equipo, ref RELAT_SEMANAL prelatorio, ref RELAT_SEMANAL_ANALISE panalise, ref RELAT_SEMANAL prelatorio_sim, ref RELAT_SEMANAL_ANALISE panalise_sim);

        // Relatorio Mensal
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_RelatMensal", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_RelatMensal(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, int id_simulacao_cenario, ref DATAHORA pdata_equipo, ref RELAT_MENSAL prelatorio, ref RELAT_MENSAL_ANALISE panalise, ref RELAT_MENSAL prelatorio_sim, ref RELAT_MENSAL_ANALISE panalise_sim);

        // Relatorio Anual
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_RelatAnual", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_RelatAnual(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, int id_simulacao_cenario, ref DATAHORA pdata_equipo, ref RELAT_ANUAL prelatorio, ref RELAT_ANUAL_ANALISE panalise, ref RELAT_ANUAL prelatorio_sim, ref RELAT_ANUAL_ANALISE panalise_sim);


        //
        // DEMANDA ATIVA
        //

        // GET: Relatorio Demanda Ativa
        public ActionResult Relat_Dem_Ativa(int IDCliente, int IDMedicao, int tipo_arquivo = 0, string Data = null)
        {
            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Relat_Dem_Ativa");

            // data
            if( Data != null )
            {
                // pega data
                DateTime dateValue = DateTime.Parse(Data);

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Relat_Data", dateValue);

                // salva cookie tipoperiodo para diario
                CookieStore.SalvaCookie_Int("Relat_TipoPeriodo", 0);
            }

            // relatorio demanda ativa
            return (Relat_Grafico_Show(IDCliente, IDMedicao, 0, tipo_arquivo));
        }

        // Demanda Ativa Diario
        private void Dem_Ativa_Diario(int IDCliente, int IDMedicao, DATAHORA data_hora, int TipoRelat = 0)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_DIARIO relatorio = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise = new RELAT_DIARIO_ANALISE();

            RELAT_DIARIO relatorio_sim = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise_sim = new RELAT_DIARIO_ANALISE();

            RELAT_DIARIO relatorio_rtv = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise_rtv = new RELAT_DIARIO_ANALISE();

            RELAT_DIARIO relatorio_rtv_sim = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise_rtv_sim = new RELAT_DIARIO_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao  de cenário
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // calcula valores
            retorno = SmCalcDB_Energia_RelatDiario((char)0, ref config_interface, (char)0, IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_DIARIO(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   0, 15, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno Atv {0}", retorno));
            }

            // verifica se reativo
            if( TipoRelat == 1 )
            {
                // calcula valores
                retorno = SmCalcDB_Energia_RelatDiario((char)0, ref config_interface, (char)1, IDSimulacaoCenario, ref data_hora, ref relatorio_rtv, ref analise_rtv, ref relatorio_rtv_sim, ref analise_rtv_sim);
                FuncoesRelat.DivisaoUnidade_RELAT_DIARIO(ref relatorio_rtv, ref analise_rtv, medicao.IDTipoUnidadePotencia);

                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno Rtv {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var DemandaAtv = new double[98];
            var DemandaRtv = new double[98];
            var FatPot = new double[98];
            var Periodo = new int[98];
            var Contrato = new double[98];
            var Tolerancia = new double[98];
            var Datas = new string[98];
            var DatasN = new DateTime[98];
            var Horas = new string[98];

            // graficos simulacao
            var DemandaAtv_Sim = new double[98];
            var DemandaRtv_Sim = new double[98];
            var FatPot_Sim = new double[98];
            var Periodo_Sim = new int[98];
            var Contrato_Sim = new double[98];
            var Tolerancia_Sim = new double[98];
            var Datas_Sim = new string[98];
            var DatasN_Sim = new DateTime[98];
            var Horas_Sim = new string[98];

            double Dem_max_grafico = 0.0;
            double FatPot_min = 1.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 15min para a primeira barra
            strData = strData.AddMinutes(-15);

            int i = 0;
            int j = 0;

            for (i = 0; i < 98; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Horas[i] = strData.ToString("HH:mm");

                // formata label simulacao
                Datas_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Sim[i] = strData;
                Horas_Sim[i] = strData.ToString("HH:mm");

                // proximo quinze minutos
                strData = strData.AddMinutes(15);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    DemandaAtv[i] = relatorio.registro[0].valor1;
                    DemandaRtv[i] = relatorio.registro[0].valor2;
                    FatPot[i] = Funcoes_FatPot.CalculaFatPot(DemandaAtv[i], DemandaRtv[i]);
                    Periodo[i] = relatorio.registro[0].periodo;
                    Contrato[i] = relatorio.contrato[0];
                    Tolerancia[i] = Contrato[i] * (1.0 + (analise.analise_valor1.tolerancia[1] / 100.0));

                    // zera simulacao
                    DemandaAtv_Sim[i] = relatorio_sim.registro[0].valor1;
                    DemandaRtv_Sim[i] = relatorio_sim.registro[0].valor2;
                    FatPot_Sim[i] = Funcoes_FatPot.CalculaFatPot(DemandaAtv_Sim[i], DemandaRtv_Sim[i]);
                    Periodo_Sim[i] = relatorio_sim.registro[0].periodo;
                    Contrato_Sim[i] = relatorio_sim.contrato[0];
                    Tolerancia_Sim[i] = Contrato_Sim[i] * (1.0 + (analise_sim.analise_valor1.tolerancia[1] / 100.0));
                }

                // verifica se ultima barra
                if (i == 97)
                {
                    // zera
                    DemandaAtv[i] = relatorio.registro[95].valor1;
                    DemandaRtv[i] = relatorio.registro[95].valor2;
                    FatPot[i] = Funcoes_FatPot.CalculaFatPot(DemandaAtv[i], DemandaRtv[i]);
                    Periodo[i] = relatorio.registro[95].periodo;
                    Contrato[i] = relatorio.contrato[95];
                    Tolerancia[i] = Contrato[i] * (1.0 + (analise.analise_valor1.tolerancia[1] / 100.0));

                    // zera simulacao
                    DemandaAtv_Sim[i] = relatorio_sim.registro[95].valor1;
                    DemandaRtv_Sim[i] = relatorio_sim.registro[95].valor2;
                    FatPot_Sim[i] = Funcoes_FatPot.CalculaFatPot(DemandaAtv_Sim[i], DemandaRtv_Sim[i]);
                    Periodo_Sim[i] = relatorio_sim.registro[95].periodo;
                    Contrato_Sim[i] = relatorio_sim.contrato[95];
                    Tolerancia_Sim[i] = Contrato_Sim[i] * (1.0 + (analise_sim.analise_valor1.tolerancia[1] / 100.0));
                }

                if (i >= 1 && i <= 96)
                {
                    // copia
                    j = i - 1;

                    DemandaAtv[i] = relatorio.registro[j].valor1;
                    DemandaRtv[i] = relatorio.registro[j].valor2;
                    FatPot[i] = Funcoes_FatPot.CalculaFatPot(DemandaAtv[i], DemandaRtv[i]);
                    Periodo[i] = relatorio.registro[j].periodo;
                    Contrato[i] = relatorio.contrato[j];

                    // copia simulacao
                    DemandaAtv_Sim[i] = relatorio_sim.registro[j].valor1;
                    DemandaRtv_Sim[i] = relatorio_sim.registro[j].valor2;
                    FatPot_Sim[i] = Funcoes_FatPot.CalculaFatPot(DemandaAtv_Sim[i], DemandaRtv_Sim[i]);
                    Periodo_Sim[i] = relatorio_sim.registro[j].periodo;
                    Contrato_Sim[i] = relatorio_sim.contrato[j];

                    // tolearancia
                    if (relatorio.registro[j].periodo == 0)
                    {
                        Tolerancia[i] = Contrato[i] * (1.0 + (analise.analise_valor1.tolerancia[0] / 100.0));
                    }
                    else
                    {
                        Tolerancia[i] = Contrato[i] * (1.0 + (analise.analise_valor1.tolerancia[1] / 100.0));
                    }

                    // tolearancia simulacao
                    if (relatorio_sim.registro[j].periodo == 0)
                    {
                        Tolerancia_Sim[i] = Contrato_Sim[i] * (1.0 + (analise_sim.analise_valor1.tolerancia[0] / 100.0));
                    }
                    else
                    {
                        Tolerancia_Sim[i] = Contrato_Sim[i] * (1.0 + (analise_sim.analise_valor1.tolerancia[1] / 100.0));
                    }

                    // verifica se sem registro
                    if (relatorio.registro[j].periodo == 3)
                    {
                        DemandaAtv[i] = 0.0;
                        DemandaRtv[i] = 0.0;
                        FatPot[i] = 1.0;
                    }

                    // verifica se sem registro simulacao
                    if (relatorio_sim.registro[j].periodo == 3)
                    {
                        DemandaAtv_Sim[i] = 0.0;
                        DemandaRtv_Sim[i] = 0.0;
                        FatPot_Sim[i] = 1.0;
                    }

                    if( TipoRelat == 0 )
                    {
                        // verifica demanda maxima
                        if (DemandaAtv[i] > Dem_max_grafico)
                            Dem_max_grafico = DemandaAtv[i];

                        if (Contrato[i] > Dem_max_grafico)
                            Dem_max_grafico = Contrato[i];

                        if (Tolerancia[i] > Dem_max_grafico)
                            Dem_max_grafico = Tolerancia[i];

                        // verifica demanda maxima simulacao
                        if (DemandaAtv_Sim[i] > Dem_max_grafico)
                            Dem_max_grafico = DemandaAtv_Sim[i];

                        if (Contrato_Sim[i] > Dem_max_grafico)
                            Dem_max_grafico = Contrato[i];

                        if (Tolerancia_Sim[i] > Dem_max_grafico)
                            Dem_max_grafico = Tolerancia_Sim[i];
                    }
                    else
                    {
                        // verifica demanda maxima
                        if (DemandaRtv[i] > Dem_max_grafico)
                            Dem_max_grafico = DemandaRtv[i];

                        // verifica demanda maxima simulacao
                        if (DemandaRtv_Sim[i] > Dem_max_grafico)
                            Dem_max_grafico = DemandaRtv_Sim[i];
                    }

                    // verifica fatpot minimo
                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPot[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPot[i]);

                    // verifica fatpot minimo simulacao
                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPot_Sim[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPot_Sim[i]);
                }
            }

            Dem_max_grafico = Dem_max_grafico * 1.1;

            if (Dem_max_grafico < 1.0)
            {
                Dem_max_grafico = 1.0;
            }

            ViewBag.DemMaxGrafico = Dem_max_grafico;

            if (FatPot_min > 0.92)
                FatPot_min = 0.92;

            FatPot_min = FatPot_min - (FatPot_min * 0.01);

            ViewBag.FatPotMin = FatPot_min;

            ViewBag.DemandaAtv = DemandaAtv;
            ViewBag.DemandaRtv = DemandaRtv;
            ViewBag.FatPot = FatPot;
            ViewBag.Periodo = Periodo;
            ViewBag.Contrato = Contrato;
            ViewBag.Tolerancia = Tolerancia;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Horas = Horas;

            // simulacao
            ViewBag.DemandaAtv_Sim = DemandaAtv_Sim;
            ViewBag.DemandaRtv_Sim = DemandaRtv_Sim;
            ViewBag.FatPot_Sim = FatPot_Sim;
            ViewBag.Periodo_Sim = Periodo_Sim;
            ViewBag.Contrato_Sim = Contrato_Sim;
            ViewBag.Tolerancia_Sim = Tolerancia_Sim;
            ViewBag.Datas_Sim = Datas_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;
            ViewBag.Horas_Sim = Horas_Sim;

            // nome do relatorio
            if (TipoRelat == 0)
            {
                ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.DemandaAtiva;
            }
            else
            {
                ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.DemandaReativa;

                // copia analise
                analise.analise_valor1 = analise_rtv.analise_valor1;

                // copia analise simulacao
                analise_sim.analise_valor1 = analise_rtv_sim.analise_valor1;
            }

            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Diario;

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:d}", DataAtual);

            // demanda maxima ponta
            ViewBag.Dem_MaxP = string.Format("{0:#,##0.0}", analise.analise_valor1.maximo[0]);
            DateTime Dem_MaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max[0]);
            if (Dem_MaxP_DataHora.Year != 2000)
                ViewBag.Dem_MaxP_DataHora = string.Format("{0:HH:mm}", Dem_MaxP_DataHora);
            else
                ViewBag.Dem_MaxP_DataHora = "--:--";
            ViewBag.Dem_MaxP_DataHoraN = Dem_MaxP_DataHora;

            // demanda maxima ponta simulacao
            ViewBag.Dem_MaxP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.maximo[0]);
            DateTime Dem_MaxP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor1.datahora_max[0]);
            if (Dem_MaxP_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxP_DataHora_Sim = string.Format("{0:HH:mm}", Dem_MaxP_DataHora_Sim);
            else
                ViewBag.Dem_MaxP_DataHora_Sim = "--:--";
            ViewBag.Dem_MaxP_DataHoraN_Sim = Dem_MaxP_DataHora_Sim;

            // demanda maxima fora ponta indutivo
            ViewBag.Dem_MaxFPI = string.Format("{0:#,##0.0}", analise.analise_valor1.maximo[1]);
            DateTime Dem_MaxFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max[1]);
            if (Dem_MaxFPI_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPI_DataHora = string.Format("{0:HH:mm}", Dem_MaxFPI_DataHora);
            else
                ViewBag.Dem_MaxFPI_DataHora = "--:--";
            ViewBag.Dem_MaxFPI_DataHoraN = Dem_MaxFPI_DataHora;

            // demanda maxima fora ponta indutivo simulacao 
            ViewBag.Dem_MaxFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.maximo[1]);
            DateTime Dem_MaxFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor1.datahora_max[1]);
            if (Dem_MaxFPI_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxFPI_DataHora_Sim = string.Format("{0:HH:mm}", Dem_MaxFPI_DataHora_Sim);
            else
                ViewBag.Dem_MaxFPI_DataHora_Sim = "--:--";
            ViewBag.Dem_MaxFPI_DataHoraN_Sim = Dem_MaxFPI_DataHora_Sim;

            // demanda maxima fora ponta capacitivo
            ViewBag.Dem_MaxFPC = string.Format("{0:#,##0.0}", analise.analise_valor1.maximo[2]);
            DateTime Dem_MaxFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max[2]);
            if (Dem_MaxFPC_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPC_DataHora = string.Format("{0:HH:mm}", Dem_MaxFPC_DataHora);
            else
                ViewBag.Dem_MaxFPC_DataHora = "--:--";
            ViewBag.Dem_MaxFPC_DataHoraN = Dem_MaxFPC_DataHora;

            // demanda maxima fora ponta capacitivo simulacao
            ViewBag.Dem_MaxFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.maximo[2]);
            DateTime Dem_MaxFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor1.datahora_max[2]);
            if (Dem_MaxFPC_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPC_DataHora_Sim = string.Format("{0:HH:mm}", Dem_MaxFPC_DataHora_Sim);
            else
                ViewBag.Dem_MaxFPC_DataHora_Sim = "--:--";
            ViewBag.Dem_MaxFPC_DataHoraN_Sim = Dem_MaxFPC_DataHora_Sim;

            // demanda minima ponta
            ViewBag.Dem_MinP = string.Format("{0:#,##0.0}", analise.analise_valor1.minimo[0]);
            DateTime Dem_MinP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_min[0]);
            if (Dem_MinP_DataHora.Year != 2000)
                ViewBag.Dem_MinP_DataHora = string.Format("{0:HH:mm}", Dem_MinP_DataHora);
            else
                ViewBag.Dem_MinP_DataHora = "--:--";
            ViewBag.Dem_MinP_DataHoraN = Dem_MinP_DataHora;
            
            // demanda minima ponta simulacao
            ViewBag.Dem_MinP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.minimo[0]);
            DateTime Dem_MinP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor1.datahora_min[0]);
            if (Dem_MinP_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MinP_DataHora_Sim = string.Format("{0:HH:mm}", Dem_MinP_DataHora_Sim);
            else
                ViewBag.Dem_MinP_DataHora_Sim = "--:--";
            ViewBag.Dem_MinP_DataHoraN_Sim = Dem_MinP_DataHora_Sim;

            // demanda minima fora ponta indutivo
            ViewBag.Dem_MinFPI = string.Format("{0:#,##0.0}", analise.analise_valor1.minimo[1]);
            DateTime Dem_MinFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_min[1]);
            if (Dem_MinFPI_DataHora.Year != 2000)
                ViewBag.Dem_MinFPI_DataHora = string.Format("{0:HH:mm}", Dem_MinFPI_DataHora);
            else
                ViewBag.Dem_MinFPI_DataHora = "--:--";
            ViewBag.Dem_MinFPI_DataHoraN = Dem_MinFPI_DataHora;

            // demanda minima fora ponta indutivo simulacao
            ViewBag.Dem_MinFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.minimo[1]);
            DateTime Dem_MinFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor1.datahora_min[1]);
            if (Dem_MinFPI_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MinFPI_DataHora_Sim = string.Format("{0:HH:mm}", Dem_MinFPI_DataHora_Sim);
            else
                ViewBag.Dem_MinFPI_DataHora_Sim = "--:--";
            ViewBag.Dem_MinFPI_DataHoraN_Sim = Dem_MinFPI_DataHora_Sim;

            // demanda minima fora ponta capacitivo
            ViewBag.Dem_MinFPC = string.Format("{0:#,##0.0}", analise.analise_valor1.minimo[2]);
            DateTime Dem_MinFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_min[2]);
            if (Dem_MinFPC_DataHora.Year != 2000)
                ViewBag.Dem_MinFPC_DataHora = string.Format("{0:HH:mm}", Dem_MinFPC_DataHora);
            else
                ViewBag.Dem_MinFPC_DataHora = "--:--";
            ViewBag.Dem_MinFPC_DataHoraN = Dem_MinFPC_DataHora;

            // demanda minima fora ponta capacitivo simulacao
            ViewBag.Dem_MinFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.minimo[2]);
            DateTime Dem_MinFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor1.datahora_min[2]);
            if (Dem_MinFPC_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MinFPC_DataHora_Sim = string.Format("{0:HH:mm}", Dem_MinFPC_DataHora_Sim);
            else
                ViewBag.Dem_MinFPC_DataHora_Sim = "--:--";
            ViewBag.Dem_MinFPC_DataHoraN_Sim = Dem_MinFPC_DataHora_Sim;

            // demanda média 
            ViewBag.Dem_MedP = string.Format("{0:#,##0.0}", analise.analise_valor1.medio[0]);
            ViewBag.Dem_MedFPI = string.Format("{0:#,##0.0}", analise.analise_valor1.medio[1]);
            ViewBag.Dem_MedFPC = string.Format("{0:#,##0.0}", analise.analise_valor1.medio[2]);

            // demanda média simulacao
            ViewBag.Dem_MedP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.medio[0]);
            ViewBag.Dem_MedFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.medio[1]);
            ViewBag.Dem_MedFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.medio[2]);

            // contratos de demanda 
            ViewBag.Dem_ContratoP = string.Format("{0:#,##0}", analise.analise_valor1.referencia[0]);
            ViewBag.Dem_ContratoFP = string.Format("{0:#,##0}", analise.analise_valor1.referencia[1]);

            // contratos de demanda simulacao
            ViewBag.Dem_ContratoP_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor1.referencia[0]);
            ViewBag.Dem_ContratoFP_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor1.referencia[1]);

            // tolerancia de contrato 
            ViewBag.Tol_ContratoP = string.Format("{0:0}", analise.analise_valor1.tolerancia[0]);
            ViewBag.Tol_ContratoFP = string.Format("{0:0}", analise.analise_valor1.tolerancia[1]);

            // tolerancia de contrato simulacao
            ViewBag.Tol_ContratoP_Sim = string.Format("{0:0}", analise_sim.analise_valor1.tolerancia[0]);
            ViewBag.Tol_ContratoFP_Sim = string.Format("{0:0}", analise_sim.analise_valor1.tolerancia[1]);

            // fator de potencia Mais Indutivo - fora ponta capacitivo
            ViewBag.FatPot_MaisIndFPC = string.Format("{0:0.000}", analise.analise_valor2.maximo[2]);
            DateTime FatPot_MaisIndFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor2.datahora_max[2]);
            if (FatPot_MaisIndFPC_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndFPC_DataHora = string.Format("{0:HH:mm}", FatPot_MaisIndFPC_DataHora);
            else
                ViewBag.FatPot_MaisIndFPC_DataHora = "--:--";
            ViewBag.FatPot_MaisIndFPC_DataHoraN = FatPot_MaisIndFPC_DataHora;

            // fator de potencia Mais Indutivo - fora ponta capacitivo simulacao 
            ViewBag.FatPot_MaisIndFPC_Sim = string.Format("{0:0.000}", analise_sim.analise_valor2.maximo[2]);
            DateTime FatPot_MaisIndFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor2.datahora_max[2]);
            if (FatPot_MaisIndFPC_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndFPC_DataHora_Sim = string.Format("{0:HH:mm}", FatPot_MaisIndFPC_DataHora_Sim);
            else
                ViewBag.FatPot_MaisIndFPC_DataHora_Sim = "--:--";
            ViewBag.FatPot_MaisIndFPC_DataHoraN_Sim = FatPot_MaisIndFPC_DataHora_Sim;

            // fator de potencia Mais Indutivo - fora ponta indutivo
            ViewBag.FatPot_MaisIndFPI = string.Format("{0:0.000}", analise.analise_valor2.maximo[1]);
            DateTime FatPot_MaisIndFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor2.datahora_max[1]);
            if (FatPot_MaisIndFPI_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndFPI_DataHora = string.Format("{0:HH:mm}", FatPot_MaisIndFPI_DataHora);
            else
                ViewBag.FatPot_MaisIndFPI_DataHora = "--:--";
            ViewBag.FatPot_MaisIndFPI_DataHoraN = FatPot_MaisIndFPI_DataHora;

            // fator de potencia Mais Indutivo - fora ponta indutivo simulacao
            ViewBag.FatPot_MaisIndFPI_Sim = string.Format("{0:0.000}", analise_sim.analise_valor2.maximo[1]);
            DateTime FatPot_MaisIndFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor2.datahora_max[1]);
            if (FatPot_MaisIndFPI_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisIndFPI_DataHora_Sim = string.Format("{0:HH:mm}", FatPot_MaisIndFPI_DataHora_Sim);
            else
                ViewBag.FatPot_MaisIndFPI_DataHora_Sim = "--:--";
            ViewBag.FatPot_MaisIndFPI_DataHoraN_Sim = FatPot_MaisIndFPI_DataHora_Sim;

            // fator de potencia Mais Indutivo - ponta
            ViewBag.FatPot_MaisIndP = string.Format("{0:0.000}", analise.analise_valor2.maximo[0]);
            DateTime FatPot_MaisIndP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor2.datahora_max[0]);
            if (FatPot_MaisIndP_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndP_DataHora = string.Format("{0:HH:mm}", FatPot_MaisIndP_DataHora);
            else
                ViewBag.FatPot_MaisIndP_DataHora = "--:--";
            ViewBag.FatPot_MaisIndP_DataHoraN = FatPot_MaisIndP_DataHora;

            // fator de potencia Mais Indutivo - ponta
            ViewBag.FatPot_MaisIndP_Sim = string.Format("{0:0.000}", analise_sim.analise_valor2.maximo[0]);
            DateTime FatPot_MaisIndP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor2.datahora_max[0]);
            if (FatPot_MaisIndP_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisIndP_DataHora_Sim = string.Format("{0:HH:mm}", FatPot_MaisIndP_DataHora_Sim);
            else
                ViewBag.FatPot_MaisIndP_DataHora_Sim = "--:--";
            ViewBag.FatPot_MaisIndP_DataHoraN_Sim = FatPot_MaisIndP_DataHora_Sim;

            // fator de potencia Mais Capacitivo - fora ponta capacitivo
            ViewBag.FatPot_MaisCapFPC = string.Format("{0:0.000}", analise.analise_valor2.minimo[2]);
            DateTime FatPot_MaisCapFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor2.datahora_min[2]);
            if (FatPot_MaisCapFPC_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapFPC_DataHora = string.Format("{0:HH:mm}", FatPot_MaisCapFPC_DataHora);
            else
                ViewBag.FatPot_MaisCapFPC_DataHora = "--:--";
            ViewBag.FatPot_MaisCapFPC_DataHoraN = FatPot_MaisCapFPC_DataHora;

            // fator de potencia Mais Capacitivo - fora ponta capacitivo simulacao
            ViewBag.FatPot_MaisCapFPC_Sim = string.Format("{0:0.000}", analise_sim.analise_valor2.minimo[2]);
            DateTime FatPot_MaisCapFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor2.datahora_min[2]);
            if (FatPot_MaisCapFPC_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisCapFPC_DataHora_Sim = string.Format("{0:HH:mm}", FatPot_MaisCapFPC_DataHora_Sim);
            else
                ViewBag.FatPot_MaisCapFPC_DataHora_Sim = "--:--";
            ViewBag.FatPot_MaisCapFPC_DataHoraN_Sim = FatPot_MaisCapFPC_DataHora_Sim;

            // fator de potencia Mais Capacitivo - fora ponta indutivo
            ViewBag.FatPot_MaisCapFPI = string.Format("{0:0.000}", analise.analise_valor2.minimo[1]);
            DateTime FatPot_MaisCapFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor2.datahora_min[1]);
            if (FatPot_MaisCapFPI_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapFPI_DataHora = string.Format("{0:HH:mm}", FatPot_MaisCapFPI_DataHora);
            else
                ViewBag.FatPot_MaisCapFPI_DataHora = "--:--";
            ViewBag.FatPot_MaisCapFPI_DataHoraN = FatPot_MaisCapFPI_DataHora;

            // fator de potencia Mais Capacitivo - fora ponta indutivo simulacao
            ViewBag.FatPot_MaisCapFPI_Sim = string.Format("{0:0.000}", analise_sim.analise_valor2.minimo[1]);
            DateTime FatPot_MaisCapFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor2.datahora_min[1]);
            if (FatPot_MaisCapFPI_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisCapFPI_DataHora_Sim = string.Format("{0:HH:mm}", FatPot_MaisCapFPI_DataHora_Sim);
            else
                ViewBag.FatPot_MaisCapFPI_DataHora_Sim = "--:--";
            ViewBag.FatPot_MaisCapFPI_DataHoraN_Sim = FatPot_MaisCapFPI_DataHora_Sim;

            // fator de potencia Mais Capacitivo - ponta
            ViewBag.FatPot_MaisCapP = string.Format("{0:0.000}", analise.analise_valor2.minimo[0]);
            DateTime FatPot_MaisCapP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor2.datahora_min[0]);
            if (FatPot_MaisCapP_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapP_DataHora = string.Format("{0:HH:mm}", FatPot_MaisCapP_DataHora);
            else
                ViewBag.FatPot_MaisCapP_DataHora = "--:--";
            ViewBag.FatPot_MaisCapP_DataHoraN = FatPot_MaisCapP_DataHora;

            // fator de potencia Mais Capacitivo - ponta simulacao
            ViewBag.FatPot_MaisCapP_Sim = string.Format("{0:0.000}", analise_sim.analise_valor2.minimo[0]);
            DateTime FatPot_MaisCapP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor2.datahora_min[0]);
            if (FatPot_MaisCapP_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisCapP_DataHora_Sim = string.Format("{0:HH:mm}", FatPot_MaisCapP_DataHora_Sim);
            else
                ViewBag.FatPot_MaisCapP_DataHora_Sim = "--:--";
            ViewBag.FatPot_MaisCapP_DataHoraN_Sim = FatPot_MaisCapP_DataHora_Sim;

            // fator de potencia No Periodo
            ViewBag.FatPot_NoPeriodoP = string.Format("{0:0.000}", analise.analise_valor2.medio[0]);
            ViewBag.FatPot_NoPeriodoFPI = string.Format("{0:0.000}", analise.analise_valor2.medio[1]);
            ViewBag.FatPot_NoPeriodoFPC = string.Format("{0:0.000}", analise.analise_valor2.medio[2]);

            // fator de potencia No Periodo simulacao
            ViewBag.FatPot_NoPeriodoP_Sim = string.Format("{0:0.000}", analise_sim.analise_valor2.medio[0]);
            ViewBag.FatPot_NoPeriodoFPI_Sim = string.Format("{0:0.000}", analise_sim.analise_valor2.medio[1]);
            ViewBag.FatPot_NoPeriodoFPC_Sim = string.Format("{0:0.000}", analise_sim.analise_valor2.medio[2]);

            // referencia
            ViewBag.RefenciaFPC = string.Format("{0:0.000}", -0.92);
            ViewBag.RefenciaFPI = string.Format("{0:0.000}", 0.92);

            // consumo
            ViewBag.ConsP = string.Format("{0:#,##0}", analise.analise_valor1.consumo[0]);
            ViewBag.ConsFPI = string.Format("{0:#,##0}", analise.analise_valor1.consumo[1]);
            ViewBag.ConsFPC = string.Format("{0:#,##0}", analise.analise_valor1.consumo[2]);
            ViewBag.ConsTotal = string.Format("{0:#,##0}", analise.analise_valor1.consumo[0] + analise.analise_valor1.consumo[1] + analise.analise_valor1.consumo[2]);

            // consumo simulacao 
            ViewBag.ConsP_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor1.consumo[0]);
            ViewBag.ConsFPI_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor1.consumo[1]);
            ViewBag.ConsFPC_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor1.consumo[2]);
            ViewBag.ConsTotal_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor1.consumo[0] + analise_sim.analise_valor1.consumo[1] + analise_sim.analise_valor1.consumo[2]);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(1);

            ViewBag.data_hora_ini = data_hora_ini;

            EventosPeriodo(IDCliente, IDMedicao, data_hora_ini, data_hora_fim);

            return;
        }

        // Demanda Ativa Diario XLS
        private HSSFWorkbook Dem_Ativa_Diario_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = {"Data e Hora","Período","Demanda Ativa","Demanda Reativa","Fator de Potência"};
    
            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);
    
            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 97; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _datahoraStyle);

                // periodo
                numeroCelulaXLS(row, 1, ViewBag.Periodo[i], _intCellStyle);

                // demanda ativa
                numeroCelulaXLS(row, 2, ViewBag.DemandaAtv[i], _1CellStyle);

                // demanda reativa
                numeroCelulaXLS(row, 3, ViewBag.DemandaRtv[i], _1CellStyle);

                // fator de potencia
                numeroCelulaXLS(row, 4, ViewBag.FatPot[i], _3CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Demanda Ativa", "Relatório Diário", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Demanda", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row,    0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row,   5, double.Parse(ViewBag.Dem_MaxP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN, _datahoraStyle);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row,  0, "Média", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP), _1CellStyle);

            // demanda minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row,    0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MinFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MinFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MinFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MinFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MinP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MinP_DataHoraN, _datahoraStyle);

            // contrato
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row,  0, "Contrato", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_ContratoFP), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_ContratoFP), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_ContratoP), _1CellStyle);

            // tolerancia
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row,  0, "Tolerância (%)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Tol_ContratoFP), _intCellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Tol_ContratoFP), _intCellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Tol_ContratoP), _intCellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // cabecalho capacitivo e indutivo
            string[] cab_fatpot = { "Fator de Potência", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_fatpot, rowIndex++);

            // fatpot mais capacitivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row,    0, "Mais Capacitivo", _negritoCellStyle);
            numeroCelulaXLS(row,   1, double.Parse(ViewBag.FatPot_MaisCapFPC), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisCapFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row,   3, double.Parse(ViewBag.FatPot_MaisCapFPI), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisCapFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisCapP), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisCapP_DataHoraN, _datahoraStyle);

            // fatpot mais indutivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row,    0, "Mais Indutivo", _negritoCellStyle);
            numeroCelulaXLS(row,   1, double.Parse(ViewBag.FatPot_MaisIndFPC), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisIndFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row,   3, double.Parse(ViewBag.FatPot_MaisIndFPI), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisIndFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisIndP), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisIndP_DataHoraN, _datahoraStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // cabecalho ponta, fora de ponta e total
            string[] cab_cons = { "Consumo", "Fora de Ponta Capacitivo", " ", "Fora de Ponta Indutivo", " ", "Ponta", " ", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // consumo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row,  0, "Consumo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.ConsFPC), _intCellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.ConsFPI), _intCellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.ConsP), _intCellStyle);
            numeroCelulaXLS(row, 7, double.Parse(ViewBag.ConsTotal), _intCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Demanda Ativa Diario XLS simulacao
        private HSSFWorkbook Dem_Ativa_Diario_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho simulacao
            string[] simulacao = {"Relatório de Demanda Ativa - Simulação"};

            // simulação 
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas do titulo simulacao
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 4));

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Período", "Demanda Ativa", "Demanda Reativa", "Fator de Potência" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 97; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[i], _datahoraStyle);

                // periodo
                numeroCelulaXLS(row, 1, ViewBag.Periodo_Sim[i], _intCellStyle);

                // demanda ativa
                numeroCelulaXLS(row, 2, ViewBag.DemandaAtv_Sim[i], _1CellStyle);

                // demanda reativa
                numeroCelulaXLS(row, 3, ViewBag.DemandaRtv_Sim[i], _1CellStyle);

                // fator de potencia
                numeroCelulaXLS(row, 4, ViewBag.FatPot_Sim[i], _3CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Demanda Ativa - Simulação", "Relatório Diário", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Demanda", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN_Sim, _datahoraStyle);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Média", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP_Sim), _1CellStyle);

            // demanda minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MinFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MinFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MinFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MinFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MinP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MinP_DataHoraN_Sim, _datahoraStyle);

            // contrato
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Contrato", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_ContratoFP_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_ContratoFP_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_ContratoP_Sim), _1CellStyle);

            // tolerancia
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Tolerância (%)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Tol_ContratoFP_Sim), _intCellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Tol_ContratoFP_Sim), _intCellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Tol_ContratoP_Sim), _intCellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // cabecalho capacitivo e indutivo
            string[] cab_fatpot = { "Fator de Potência", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_fatpot, rowIndex++);

            // fatpot mais capacitivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Capacitivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisCapFPC_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisCapFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisCapFPI_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisCapFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisCapP_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisCapP_DataHoraN_Sim, _datahoraStyle);

            // fatpot mais indutivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Indutivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisIndFPC_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisIndFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisIndFPI_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisIndFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisIndP_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisIndP_DataHoraN_Sim, _datahoraStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // cabecalho ponta, fora de ponta e total
            string[] cab_cons = { "Consumo", "Fora de Ponta Capacitivo", " ", "Fora de Ponta Indutivo", " ", "Ponta", " ", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // consumo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Consumo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.ConsFPC_Sim), _intCellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.ConsFPI_Sim), _intCellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.ConsP_Sim), _intCellStyle);
            numeroCelulaXLS(row, 7, double.Parse(ViewBag.ConsTotal_Sim), _intCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Demanda Ativa Semanal
        private void Dem_Ativa_Semanal(int IDCliente, int IDMedicao, DATAHORA data_hora, int TipoRelat = 0)
        {
            // funcao relatorio
            int retorno;
            
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_SEMANAL relatorio = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise = new RELAT_SEMANAL_ANALISE();

            RELAT_SEMANAL relatorio_sim = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise_sim = new RELAT_SEMANAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao  de cenário
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // calcula valores
            retorno = SmCalcDB_Energia_RelatSemanal((char)0, ref config_interface, (char)TipoRelat, IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_SEMANAL(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   0, 15, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno Atv {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var Demanda = new double[7, 98];
            var Periodo = new int[7, 98];
            var Datas = new string[98];
            var DatasN = new DateTime[7, 98];
            var Dias = new string[7];
            var Horas = new string[98];

            // grafico simulacao 
            var Demanda_Sim = new double[7, 98];
            var Periodo_Sim = new int[7, 98];
            var Datas_Sim = new string[98];
            var DatasN_Sim = new DateTime[7, 98];
            var Dias_Sim = new string[7];
            var Horas_Sim = new string[98];

            DateTime inicio = new DateTime();
            DateTime fim = new DateTime();

            double Dem_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 15min para a primeira barra
            strData = strData.AddMinutes(-15);

            int i = 0;
            int j = 0;
            int k = 0;

            for (i = 0; i < 98; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                Horas[i] = strData.ToString("HH:mm");

                // formata label
                Datas_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                Horas_Sim[i] = strData.ToString("HH:mm");

                // percorre dias
                for (k = 0; k < 7; k++)
                {
                    // data e hora para excel
                    DatasN[k, i] = strData.AddDays(k);

                    // data e hora para excel simulacao
                    DatasN_Sim[k, i] = strData.AddDays(k);

                    // guarda inicio e fim
                    if (i == 0 && k == 0)
                    {
                        inicio = strData;
                        fim = inicio.AddDays(6);
                    }

                    // dia da semana
                    if (i == 1)
                    {
                        Dias[k] = string.Format("{0:d}", strData.AddDays(k));

                        // simulacao
                        Dias_Sim[k] = string.Format("{0:d}", strData.AddDays(k));
                    }

                    // verifica se primeira ou ultima barra
                    if (i == 0)
                    {
                        // zera
                        Demanda[k, i] = relatorio.registro[0].valor[k];
                        Periodo[k, i] = relatorio.registro[0].periodo[k];

                        // zera simulacao
                        Demanda_Sim[k, i] = relatorio_sim.registro[0].valor[k];
                        Periodo_Sim[k, i] = relatorio_sim.registro[0].periodo[k];
                    }

                    if (i == 97)
                    {
                        // zera
                        Demanda[k, i] = relatorio.registro[95].valor[k];
                        Periodo[k, i] = relatorio.registro[95].periodo[k];

                        // zera simulacao
                        Demanda_Sim[k, i] = relatorio_sim.registro[95].valor[k];
                        Periodo_Sim[k, i] = relatorio_sim.registro[95].periodo[k];
                    }

                    if (i >= 1 && i <= 96)
                    {
                        // copia
                        j = i - 1;

                        Demanda[k, i] = relatorio.registro[j].valor[k];
                        Periodo[k, i] = relatorio.registro[j].periodo[k];

                        // simulacao 
                        Demanda_Sim[k, i] = relatorio_sim.registro[j].valor[k];
                        Periodo_Sim[k, i] = relatorio_sim.registro[j].periodo[k];

                        // verifica se sem registro
                        if (relatorio.registro[j].periodo[k] == 3)
                        {
                            Demanda[k, i] = 0.0;
                        }

                        // verifica demanda maxima
                        if (Demanda[k, i] > Dem_max_grafico)
                            Dem_max_grafico = Demanda[k, i];

                        // verifica demanda maxima simulacao
                        if (Demanda_Sim[k, i] > Dem_max_grafico)
                            Dem_max_grafico = Demanda_Sim[k, i];
                    }
                }

                // proximo quinze minutos
                strData = strData.AddMinutes(15);
            }

            // verifica demanda maxima - referencia
            if (analise.analise_valor.referencia[0] > Dem_max_grafico)
                Dem_max_grafico = analise.analise_valor.referencia[0];

            if (analise.analise_valor.referencia[1] > Dem_max_grafico)
                Dem_max_grafico = analise.analise_valor.referencia[1];

            // verifica demanda maxima - referencia simulacao
            if (analise_sim.analise_valor.referencia[0] > Dem_max_grafico)
                Dem_max_grafico = analise_sim.analise_valor.referencia[0];

            if (analise_sim.analise_valor.referencia[1] > Dem_max_grafico)
                Dem_max_grafico = analise_sim.analise_valor.referencia[1];

            Dem_max_grafico = Dem_max_grafico * 1.1;

            if (Dem_max_grafico < 1.0)
            {
                Dem_max_grafico = 1.0;
            }

            ViewBag.DemMaxGrafico = Dem_max_grafico;

            // copia dados grafico 
            ViewBag.Demanda = Demanda;
            ViewBag.Periodo = Periodo;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;
            ViewBag.Horas = Horas;

            // copia dados grafico simulacao 
            ViewBag.Demanda_Sim = Demanda_Sim;
            ViewBag.Periodo_Sim = Periodo_Sim;
            ViewBag.Datas_Sim = Datas_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;
            ViewBag.Dias_Sim = Dias_Sim;
            ViewBag.Horas_Sim = Horas_Sim;

            // nome do relatorio
            if (TipoRelat == 0)
                ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.DemandaAtiva;
            else
                ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.DemandaReativa;

            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Semanal;

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);
            ViewBag.DataAtualN = inicio;
            ViewBag.DataFinalN = fim;
            ViewBag.DataTextoAtual = string.Format("{0:d} - {1:d}", inicio, fim);

            // demanda maxima ponta
            ViewBag.Dem_MaxP = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[0]);
            DateTime Dem_MaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (Dem_MaxP_DataHora.Year != 2000)
                ViewBag.Dem_MaxP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MaxP_DataHora, Dem_MaxP_DataHora);
            else
                ViewBag.Dem_MaxP_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxP_DataHoraN = Dem_MaxP_DataHora;

            // demanda maxima ponta simulacao
            ViewBag.Dem_MaxP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[0]);
            DateTime Dem_MaxP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[0]);
            if (Dem_MaxP_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxP_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", Dem_MaxP_DataHora_Sim, Dem_MaxP_DataHora_Sim);
            else
                ViewBag.Dem_MaxP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MaxP_DataHoraN_Sim = Dem_MaxP_DataHora_Sim;

            // demanda maxima fora ponta indutivo 
            ViewBag.Dem_MaxFPI = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[1]);
            DateTime Dem_MaxFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[1]);
            if (Dem_MaxFPI_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPI_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MaxFPI_DataHora, Dem_MaxFPI_DataHora);
            else
                ViewBag.Dem_MaxFPI_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxFPI_DataHoraN = Dem_MaxFPI_DataHora;

            // demanda maxima fora ponta indutivo simulacao
            ViewBag.Dem_MaxFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[1]);
            DateTime Dem_MaxFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[1]);
            if (Dem_MaxFPI_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxFPI_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", Dem_MaxFPI_DataHora_Sim, Dem_MaxFPI_DataHora_Sim);
            else
                ViewBag.Dem_MaxFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MaxFPI_DataHoraN_Sim = Dem_MaxFPI_DataHora_Sim;

            // demanda maxima fora ponta capacitivo 
            ViewBag.Dem_MaxFPC = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[2]);
            DateTime Dem_MaxFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[2]);
            if (Dem_MaxFPC_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPC_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MaxFPC_DataHora, Dem_MaxFPC_DataHora);
            else
                ViewBag.Dem_MaxFPC_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxFPC_DataHoraN = Dem_MaxFPC_DataHora;

            // demanda maxima fora ponta capacitivo simulacao
            ViewBag.Dem_MaxFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[2]);
            DateTime Dem_MaxFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[2]);
            if (Dem_MaxFPC_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxFPC_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", Dem_MaxFPC_DataHora_Sim, Dem_MaxFPC_DataHora_Sim);
            else
                ViewBag.Dem_MaxFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MaxFPC_DataHoraN_Sim = Dem_MaxFPC_DataHora_Sim;
            
            // demanda minima ponta
            ViewBag.Dem_MinP = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[0]);
            DateTime Dem_MinP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[0]);
            if (Dem_MinP_DataHora.Year != 2000)
                ViewBag.Dem_MinP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MinP_DataHora, Dem_MinP_DataHora);
            else
                ViewBag.Dem_MinP_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MinP_DataHoraN = Dem_MinP_DataHora;

            // demanda minima ponta simulacao
            ViewBag.Dem_MinP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.minimo[0]);
            DateTime Dem_MinP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[0]);
            if (Dem_MinP_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MinP_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", Dem_MinP_DataHora_Sim, Dem_MinP_DataHora_Sim);
            else
                ViewBag.Dem_MinP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MinP_DataHoraN_Sim = Dem_MinP_DataHora_Sim;

            // demanda minima fora ponta indutivo
            ViewBag.Dem_MinFPI = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[1]);
            DateTime Dem_MinFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[1]);
            if (Dem_MinFPI_DataHora.Year != 2000)
                ViewBag.Dem_MinFPI_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MinFPI_DataHora, Dem_MinFPI_DataHora);
            else
                ViewBag.Dem_MinFPI_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MinFPI_DataHoraN = Dem_MinFPI_DataHora;

            // demanda minima fora ponta indutivo simulacao
            ViewBag.Dem_MinFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.minimo[1]);
            DateTime Dem_MinFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[1]);
            if (Dem_MinFPI_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MinFPI_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", Dem_MinFPI_DataHora_Sim, Dem_MinFPI_DataHora_Sim);
            else
                ViewBag.Dem_MinFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MinFPI_DataHoraN_Sim = Dem_MinFPI_DataHora_Sim;

            // demanda minima fora ponta capacitivo 
            ViewBag.Dem_MinFPC = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[2]);
            DateTime Dem_MinFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[2]);
            if (Dem_MinFPC_DataHora.Year != 2000)
                ViewBag.Dem_MinFPC_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MinFPC_DataHora, Dem_MinFPC_DataHora);
            else
                ViewBag.Dem_MinFPC_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MinFPC_DataHoraN = Dem_MinFPC_DataHora;

            // demanda minima fora ponta capacitivo simulacao
            ViewBag.Dem_MinFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.minimo[2]);
            DateTime Dem_MinFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[2]);
            if (Dem_MinFPC_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MinFPC_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", Dem_MinFPC_DataHora_Sim, Dem_MinFPC_DataHora_Sim);
            else
                ViewBag.Dem_MinFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MinFPC_DataHoraN_Sim = Dem_MinFPC_DataHora_Sim;

            // media 
            ViewBag.Dem_MedP = string.Format("{0:#,##0.0}", analise.analise_valor.medio[0]);
            ViewBag.Dem_MedFPI = string.Format("{0:#,##0.0}", analise.analise_valor.medio[1]);
            ViewBag.Dem_MedFPC = string.Format("{0:#,##0.0}", analise.analise_valor.medio[2]);

            // media simulacao
            ViewBag.Dem_MedP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[0]);
            ViewBag.Dem_MedFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[1]);
            ViewBag.Dem_MedFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[2]);

            // contrato
            ViewBag.Dem_ContratoP = string.Format("{0:#,##0}", analise.analise_valor.referencia[0]);
            ViewBag.ContratoP = analise.analise_valor.referencia[0];
            ViewBag.Dem_ContratoFP = string.Format("{0:#,##0}", analise.analise_valor.referencia[1]);
            ViewBag.ContratoFP = analise.analise_valor.referencia[1];

            // contrato simulacao
            ViewBag.Dem_ContratoP_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor.referencia[0]);
            ViewBag.ContratoP_Sim = analise_sim.analise_valor.referencia[0];
            ViewBag.Dem_ContratoFP_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor.referencia[1]);
            ViewBag.ContratoFP_Sim = analise_sim.analise_valor.referencia[1];

            // tolerancia
            ViewBag.Tol_ContratoP = string.Format("{0:0}", analise.analise_valor.tolerancia[0]);
            ViewBag.ToleranciaP = analise.analise_valor.referencia[0] * (1.0 + (analise.analise_valor.tolerancia[0] / 100.0));
            ViewBag.Tol_ContratoFP = string.Format("{0:0}", analise.analise_valor.tolerancia[1]);
            ViewBag.ToleranciaFP = analise.analise_valor.referencia[1] * (1.0 + (analise.analise_valor.tolerancia[1] / 100.0));

            // tolerancia simulacao
            ViewBag.Tol_ContratoP_Sim = string.Format("{0:0}", analise_sim.analise_valor.tolerancia[0]);
            ViewBag.ToleranciaP_Sim = analise_sim.analise_valor.referencia[0] * (1.0 + (analise_sim.analise_valor.tolerancia[0] / 100.0));
            ViewBag.Tol_ContratoFP_Sim = string.Format("{0:0}", analise_sim.analise_valor.tolerancia[1]);
            ViewBag.ToleranciaFP_Sim = analise_sim.analise_valor.referencia[1] * (1.0 + (analise_sim.analise_valor.tolerancia[1] / 100.0));

            // consumo
            ViewBag.ConsP = string.Format("{0:#,##0}", analise.analise_valor.consumo[0]);
            ViewBag.ConsFPI = string.Format("{0:#,##0}", analise.analise_valor.consumo[1]);
            ViewBag.ConsFPC = string.Format("{0:#,##0}", analise.analise_valor.consumo[2]);
            ViewBag.ConsTotal = string.Format("{0:#,##0}", analise.analise_valor.consumo[0] + analise.analise_valor.consumo[1] + analise.analise_valor.consumo[2]);

            // consumo simulacao
            ViewBag.ConsP_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor.consumo[0]);
            ViewBag.ConsFPI_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor.consumo[1]);
            ViewBag.ConsFPC_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor.consumo[2]);
            ViewBag.ConsTotal_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor.consumo[0] + analise_sim.analise_valor.consumo[1] + analise_sim.analise_valor.consumo[2]);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(inicio.Year, inicio.Month, inicio.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(7);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Demanda Ativa Semanal XLS
        private HSSFWorkbook Dem_Ativa_Semanal_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // dias da semana
            int k = 0;
            int i;
            IRow row;
            ISheet sheet;
            int rowIndex;

            for (k = 0; k < 7; k++)
            {
                // cria planilha
                sheet = workbook.CreateSheet(string.Format("{0:dddd}", ViewBag.DatasN[k, 0]));

                // cabecalho
                string[] cabecalho = { "Data e Hora", "Período", "Demanda Ativa" };

                // adiciona cabecalho
                rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

                // percorre valores
                for (i = 1; i < 97; i++)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // data e hora
                    datahoraCelulaXLS(row, 0, ViewBag.DatasN[k, i], _datahoraStyle);

                    // periodo
                    numeroCelulaXLS(row, 1, ViewBag.Periodo[k, i], _intCellStyle);

                    // demanda ativa
                    numeroCelulaXLS(row, 2, ViewBag.Demanda[k, i], _1CellStyle);

                    // proxima
                    rowIndex++;
                }

                // largura de cada coluna
                for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                    sheet.SetColumnWidth(i, 6000);
            }

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Demanda Ativa", "Relatório Semanal", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Demanda", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN, _datahoraStyle);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Média", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP), _1CellStyle);

            // demanda minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MinFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MinFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MinFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MinFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MinP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MinP_DataHoraN, _datahoraStyle);

            // contrato
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Contrato", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_ContratoFP), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_ContratoFP), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_ContratoP), _1CellStyle);

            // tolerancia
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Tolerância (%)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Tol_ContratoFP), _intCellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Tol_ContratoFP), _intCellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Tol_ContratoP), _intCellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // cabecalho ponta, fora de ponta e total
            string[] cab_cons = { "Consumo", "Fora de Ponta Capacitivo", " ", "Fora de Ponta Indutivo", " ", "Ponta", " ", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // consumo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Consumo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.ConsFPC), _intCellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.ConsFPI), _intCellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.ConsP), _intCellStyle);
            numeroCelulaXLS(row, 7, double.Parse(ViewBag.ConsTotal), _intCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Demanda Ativa Semanal XLS simulacao
        private HSSFWorkbook Dem_Ativa_Semanal_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // dias da semana
            int k = 0;
            int i;
            IRow row;
            ISheet sheet;
            int rowIndex;

            for (k = 0; k < 7; k++)
            {
                // cria planilha
                sheet = workbook.CreateSheet(string.Format("{0:dddd}", ViewBag.DatasN[k, 0]));

                // cabecalho simulacao
                string[] simulacao = { "Relatório de Demanda Ativa - Simulação" };

                // simulação 
                rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

                // mescla celulas do titulo simulacao
                sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 2));

                // cabecalho
                string[] cabecalho = { "Data e Hora", "Período", "Demanda Ativa" };

                // adiciona cabecalho
                rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

                // percorre valores
                for (i = 1; i < 97; i++)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // data e hora
                    datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[k, i], _datahoraStyle);

                    // periodo
                    numeroCelulaXLS(row, 1, ViewBag.Periodo_Sim[k, i], _intCellStyle);

                    // demanda ativa
                    numeroCelulaXLS(row, 2, ViewBag.Demanda_Sim[k, i], _1CellStyle);

                    // proxima
                    rowIndex++;
                }

                // largura de cada coluna
                for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                    sheet.SetColumnWidth(i, 6000);
            }

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Demanda Ativa - Simulação", "Relatório Semanal", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Demanda", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN_Sim, _datahoraStyle);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Média", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP_Sim), _1CellStyle);

            // demanda minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MinFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MinFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MinFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MinFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MinP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MinP_DataHoraN_Sim, _datahoraStyle);

            // contrato
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Contrato", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_ContratoFP_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_ContratoFP_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_ContratoP_Sim), _1CellStyle);

            // tolerancia
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Tolerância (%)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Tol_ContratoFP_Sim), _intCellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Tol_ContratoFP_Sim), _intCellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Tol_ContratoP_Sim), _intCellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // cabecalho ponta, fora de ponta e total
            string[] cab_cons = { "Consumo", "Fora de Ponta Capacitivo", " ", "Fora de Ponta Indutivo", " ", "Ponta", " ", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // consumo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Consumo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.ConsFPC_Sim), _intCellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.ConsFPI_Sim), _intCellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.ConsP_Sim), _intCellStyle);
            numeroCelulaXLS(row, 7, double.Parse(ViewBag.ConsTotal_Sim), _intCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Demanda Ativa Mensal
        private void Dem_Ativa_Mensal(int IDCliente, int IDMedicao, DATAHORA data_hora, int TipoRelat = 0)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_MENSAL relatorio = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise = new RELAT_MENSAL_ANALISE();

            RELAT_MENSAL relatorio_sim = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise_sim = new RELAT_MENSAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao  de cenário
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // calcula valores
            retorno = SmCalcDB_Energia_RelatMensal((char)0, ref config_interface, (char)TipoRelat, IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_MENSAL(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // numero de dias do mes
            int NumDiasMes = relatorio.num_dias;

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[NumDiasMes - 1].datahora.data.ano,
                                   (int)relatorio.registro[NumDiasMes - 1].datahora.data.mes,
                                   1, 0, 15, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var DemandaP = new double[42];
            var DemandaFPI = new double[42];
            var DemandaFPC = new double[42];
            var ContratoP = new double[42];
            var ContratoFP = new double[42];
            var ToleranciaP = new double[42];
            var ToleranciaFP = new double[42];
            var Datas = new string[42];
            var DatasN = new DateTime[42];
            var Dias = new string[42];

            // grafico simulacao
            var DemandaP_Sim = new double[42];
            var DemandaFPI_Sim = new double[42];
            var DemandaFPC_Sim = new double[42];
            var ContratoP_Sim = new double[42];
            var ContratoFP_Sim = new double[42];
            var ToleranciaP_Sim = new double[42];
            var ToleranciaFP_Sim = new double[42];
            var Datas_Sim = new string[42];
            var DatasN_Sim = new DateTime[42];
            var Dias_Sim = new string[42];

            double Dem_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = strData.ToString("dd/MM");

                // formata label simulacao
                Datas_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Sim[i] = strData;
                Dias_Sim[i] = strData.ToString("dd/MM");

                // proximo dia
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    DemandaP[i] = relatorio.registro[0].valor[0];
                    DemandaFPI[i] = relatorio.registro[0].valor[1];
                    DemandaFPC[i] = relatorio.registro[0].valor[2];
                    ContratoP[i] = relatorio.contrato_ponta[0];
                    ContratoFP[i] = relatorio.contrato_fponta[0];
                    ToleranciaP[i] = ContratoP[i] * (1.0 + (analise.analise_valor.tolerancia[0] / 100.0));
                    ToleranciaFP[i] = ContratoFP[i] * (1.0 + (analise.analise_valor.tolerancia[1] / 100.0));

                    // zera simulacao
                    DemandaP_Sim[i] = relatorio_sim.registro[0].valor[0];
                    DemandaFPI_Sim[i] = relatorio_sim.registro[0].valor[1];
                    DemandaFPC_Sim[i] = relatorio_sim.registro[0].valor[2];
                    ContratoP_Sim[i] = relatorio_sim.contrato_ponta[0];
                    ContratoFP_Sim[i] = relatorio_sim.contrato_fponta[0];
                    ToleranciaP_Sim[i] = ContratoP_Sim[i] * (1.0 + (analise_sim.analise_valor.tolerancia[0] / 100.0));
                    ToleranciaFP_Sim[i] = ContratoFP_Sim[i] * (1.0 + (analise_sim.analise_valor.tolerancia[1] / 100.0));
                }

                if (i == (NumDiasMes + 1))
                {
                    // zera 
                    DemandaP[i] = relatorio.registro[NumDiasMes - 1].valor[0];
                    DemandaFPI[i] = relatorio.registro[NumDiasMes - 1].valor[1];
                    DemandaFPC[i] = relatorio.registro[NumDiasMes - 1].valor[2];
                    ContratoP[i] = relatorio.contrato_ponta[NumDiasMes - 1];
                    ContratoFP[i] = relatorio.contrato_fponta[NumDiasMes - 1];
                    ToleranciaP[i] = ContratoP[i] * (1.0 + (analise.analise_valor.tolerancia[0] / 100.0));
                    ToleranciaFP[i] = ContratoFP[i] * (1.0 + (analise.analise_valor.tolerancia[1] / 100.0));

                    // zera simulacao
                    DemandaP_Sim[i] = relatorio_sim.registro[NumDiasMes - 1].valor[0];
                    DemandaFPI_Sim[i] = relatorio_sim.registro[NumDiasMes - 1].valor[1];
                    DemandaFPC_Sim[i] = relatorio_sim.registro[NumDiasMes - 1].valor[2];
                    ContratoP_Sim[i] = relatorio_sim.contrato_ponta[NumDiasMes - 1];
                    ContratoFP_Sim[i] = relatorio_sim.contrato_fponta[NumDiasMes - 1];
                    ToleranciaP_Sim[i] = ContratoP_Sim[i] * (1.0 + (analise_sim.analise_valor.tolerancia[0] / 100.0));
                    ToleranciaFP_Sim[i] = ContratoFP_Sim[i] * (1.0 + (analise_sim.analise_valor.tolerancia[1] / 100.0));
                }

                if (i >= 1 && i <= NumDiasMes)
                {
                    // copia
                    j = i - 1;

                    DemandaP[i] = relatorio.registro[j].valor[0];
                    DemandaFPI[i] = relatorio.registro[j].valor[1];
                    DemandaFPC[i] = relatorio.registro[j].valor[2];
                    ContratoP[i] = relatorio.contrato_ponta[j];
                    ContratoFP[i] = relatorio.contrato_fponta[j];
                    ToleranciaP[i] = ContratoP[i] * (1.0 + (analise.analise_valor.tolerancia[0] / 100.0));
                    ToleranciaFP[i] = ContratoFP[i] * (1.0 + (analise.analise_valor.tolerancia[1] / 100.0));

                    // copia simulacao
                    DemandaP_Sim[i] = relatorio_sim.registro[j].valor[0];
                    DemandaFPI_Sim[i] = relatorio_sim.registro[j].valor[1];
                    DemandaFPC_Sim[i] = relatorio_sim.registro[j].valor[2];
                    ContratoP_Sim[i] = relatorio_sim.contrato_ponta[j];
                    ContratoFP_Sim[i] = relatorio_sim.contrato_fponta[j];
                    ToleranciaP_Sim[i] = ContratoP_Sim[i] * (1.0 + (analise_sim.analise_valor.tolerancia[0] / 100.0));
                    ToleranciaFP_Sim[i] = ContratoFP_Sim[i] * (1.0 + (analise_sim.analise_valor.tolerancia[1] / 100.0));

                    // verifica demanda maxima
                    if (DemandaP[i] > Dem_max_grafico)
                        Dem_max_grafico = DemandaP[i];

                    if (DemandaFPI[i] > Dem_max_grafico)
                        Dem_max_grafico = DemandaFPI[i];

                    if (DemandaFPC[i] > Dem_max_grafico)
                        Dem_max_grafico = DemandaFPC[i];

                    if (ContratoP[i] > Dem_max_grafico)
                        Dem_max_grafico = ContratoP[i];

                    if (ToleranciaP[i] > Dem_max_grafico)
                        Dem_max_grafico = ToleranciaP[i];

                    if (ContratoFP[i] > Dem_max_grafico)
                        Dem_max_grafico = ContratoFP[i];

                    if (ToleranciaFP[i] > Dem_max_grafico)
                        Dem_max_grafico = ToleranciaFP[i];

                    // verifica demanda maxima simulacao
                    if (DemandaP_Sim[i] > Dem_max_grafico)
                        Dem_max_grafico = DemandaP_Sim[i];

                    if (DemandaFPI_Sim[i] > Dem_max_grafico)
                        Dem_max_grafico = DemandaFPI_Sim[i];

                    if (DemandaFPC_Sim[i] > Dem_max_grafico)
                        Dem_max_grafico = DemandaFPC_Sim[i];

                    if (ContratoP_Sim[i] > Dem_max_grafico)
                        Dem_max_grafico = ContratoP_Sim[i];

                    if (ToleranciaP_Sim[i] > Dem_max_grafico)
                        Dem_max_grafico = ToleranciaP_Sim[i];

                    if (ContratoFP_Sim[i] > Dem_max_grafico)
                        Dem_max_grafico = ContratoFP_Sim[i];

                    if (ToleranciaFP_Sim[i] > Dem_max_grafico)
                        Dem_max_grafico = ToleranciaFP_Sim[i];
                }
            }

            Dem_max_grafico = Dem_max_grafico * 1.1;

            if (Dem_max_grafico < 1.0)
            {
                Dem_max_grafico = 1.0;
            }

            ViewBag.DemMaxGrafico = Dem_max_grafico;

            // dados gráfico 
            ViewBag.DemandaP = DemandaP;
            ViewBag.DemandaFPI = DemandaFPI;
            ViewBag.DemandaFPC = DemandaFPC;
            ViewBag.ContratoP = ContratoP;
            ViewBag.ContratoFP = ContratoFP;
            ViewBag.ToleranciaP = ToleranciaP;
            ViewBag.ToleranciaFP = ToleranciaFP;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            // dados gráfico 
            ViewBag.DemandaP_Sim = DemandaP_Sim;
            ViewBag.DemandaFPI_Sim = DemandaFPI_Sim;
            ViewBag.DemandaFPC_Sim = DemandaFPC_Sim;
            ViewBag.ContratoP_Sim = ContratoP_Sim;
            ViewBag.ContratoFP_Sim = ContratoFP_Sim;
            ViewBag.ToleranciaP_Sim = ToleranciaP_Sim;
            ViewBag.ToleranciaFP_Sim = ToleranciaFP_Sim;
            ViewBag.Datas_Sim = Datas_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;
            ViewBag.Dias_Sim = Dias_Sim;

            // nome do relatorio
            if (TipoRelat == 0)
                ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.DemandaAtiva;
            else
                ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.DemandaReativa;

            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Mensal;

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:Y}", DataAtual);

            ViewBag.NumDiasMes = NumDiasMes;

            // demanda maxima ponta
            ViewBag.Dem_MaxP = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[0]);
            DateTime Dem_MaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (Dem_MaxP_DataHora.Year != 2000)
                ViewBag.Dem_MaxP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MaxP_DataHora, Dem_MaxP_DataHora);
            else
                ViewBag.Dem_MaxP_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxP_DataHoraN = Dem_MaxP_DataHora;

            // demanda maxima ponta simulacao
            ViewBag.Dem_MaxP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[0]);
            DateTime Dem_MaxP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[0]);
            if (Dem_MaxP_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxP_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", Dem_MaxP_DataHora_Sim, Dem_MaxP_DataHora_Sim);
            else
                ViewBag.Dem_MaxP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MaxP_DataHoraN_Sim = Dem_MaxP_DataHora_Sim;

            // demanda maxima fora ponta indutivo 
            ViewBag.Dem_MaxFPI = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[1]);
            DateTime Dem_MaxFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[1]);
            if (Dem_MaxFPI_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPI_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MaxFPI_DataHora, Dem_MaxFPI_DataHora);
            else
                ViewBag.Dem_MaxFPI_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxFPI_DataHoraN = Dem_MaxFPI_DataHora;

            // demanda maxima fora ponta indutivo simulacao
            ViewBag.Dem_MaxFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[1]);
            DateTime Dem_MaxFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[1]);
            if (Dem_MaxFPI_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxFPI_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", Dem_MaxFPI_DataHora_Sim, Dem_MaxFPI_DataHora_Sim);
            else
                ViewBag.Dem_MaxFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MaxFPI_DataHoraN_Sim = Dem_MaxFPI_DataHora_Sim;

            // demanda maxima fora ponta capacitivo
            ViewBag.Dem_MaxFPC = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[2]);
            DateTime Dem_MaxFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[2]);
            if (Dem_MaxFPC_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPC_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MaxFPC_DataHora, Dem_MaxFPC_DataHora);
            else
                ViewBag.Dem_MaxFPC_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxFPC_DataHoraN = Dem_MaxFPC_DataHora;

            // demanda maxima fora ponta capacitivo simulacao 
            ViewBag.Dem_MaxFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[2]);
            DateTime Dem_MaxFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[2]);
            if (Dem_MaxFPC_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxFPC_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", Dem_MaxFPC_DataHora_Sim, Dem_MaxFPC_DataHora_Sim);
            else
                ViewBag.Dem_MaxFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MaxFPC_DataHoraN_Sim = Dem_MaxFPC_DataHora_Sim;

            // demanda minima ponta 
            ViewBag.Dem_MinP = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[0]);
            DateTime Dem_MinP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[0]);
            if (Dem_MinP_DataHora.Year != 2000)
                ViewBag.Dem_MinP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MinP_DataHora, Dem_MinP_DataHora);
            else
                ViewBag.Dem_MinP_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MinP_DataHoraN = Dem_MinP_DataHora;

            // demanda minima ponta simulacao
            ViewBag.Dem_MinP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.minimo[0]);
            DateTime Dem_MinP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[0]);
            if (Dem_MinP_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MinP_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", Dem_MinP_DataHora_Sim, Dem_MinP_DataHora_Sim);
            else
                ViewBag.Dem_MinP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MinP_DataHoraN_Sim = Dem_MinP_DataHora_Sim;

            // demanda minima fora ponta indutivo
            ViewBag.Dem_MinFPI = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[1]);
            DateTime Dem_MinFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[1]);
            if (Dem_MinFPI_DataHora.Year != 2000)
                ViewBag.Dem_MinFPI_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MinFPI_DataHora, Dem_MinFPI_DataHora);
            else
                ViewBag.Dem_MinFPI_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MinFPI_DataHoraN = Dem_MinFPI_DataHora;

            // demanda minima fora ponta indutivo simulacao
            ViewBag.Dem_MinFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.minimo[1]);
            DateTime Dem_MinFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[1]);
            if (Dem_MinFPI_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MinFPI_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", Dem_MinFPI_DataHora_Sim, Dem_MinFPI_DataHora_Sim);
            else
                ViewBag.Dem_MinFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MinFPI_DataHoraN_Sim = Dem_MinFPI_DataHora_Sim;

            // demanda minima fora ponta capacitivo
            ViewBag.Dem_MinFPC = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[2]);
            DateTime Dem_MinFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[2]);
            if (Dem_MinFPC_DataHora.Year != 2000)
                ViewBag.Dem_MinFPC_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MinFPC_DataHora, Dem_MinFPC_DataHora);
            else
                ViewBag.Dem_MinFPC_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MinFPC_DataHoraN = Dem_MinFPC_DataHora;

            // demanda minima fora ponta capacitivo simulacao
            ViewBag.Dem_MinFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.minimo[2]);
            DateTime Dem_MinFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[2]);
            if (Dem_MinFPC_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MinFPC_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", Dem_MinFPC_DataHora_Sim, Dem_MinFPC_DataHora_Sim);
            else
                ViewBag.Dem_MinFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MinFPC_DataHoraN_Sim = Dem_MinFPC_DataHora_Sim;

            // media 
            ViewBag.Dem_MedP = string.Format("{0:#,##0.0}", analise.analise_valor.medio[0]);
            ViewBag.Dem_MedFPI = string.Format("{0:#,##0.0}", analise.analise_valor.medio[1]);
            ViewBag.Dem_MedFPC = string.Format("{0:#,##0.0}", analise.analise_valor.medio[2]);

            // media demanda
            ViewBag.Dem_MedP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[0]);
            ViewBag.Dem_MedFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[1]);
            ViewBag.Dem_MedFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[2]);

            // contrato 
            ViewBag.Dem_ContratoP = string.Format("{0:#,##0}", analise.analise_valor.referencia[0]);
            ViewBag.Dem_ContratoFP = string.Format("{0:#,##0}", analise.analise_valor.referencia[1]);

            // contrato simulacao
            ViewBag.Dem_ContratoP_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor.referencia[0]);
            ViewBag.Dem_ContratoFP_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor.referencia[1]);

            // tolerancia
            ViewBag.Tol_ContratoP = string.Format("{0:0}", analise.analise_valor.tolerancia[0]);
            ViewBag.Tol_ContratoFP = string.Format("{0:0}", analise.analise_valor.tolerancia[1]);

            // tolerancia simulacao
            ViewBag.Tol_ContratoP_Sim = string.Format("{0:0}", analise_sim.analise_valor.tolerancia[0]);
            ViewBag.Tol_ContratoFP_Sim = string.Format("{0:0}", analise_sim.analise_valor.tolerancia[1]);

            // consumo
            ViewBag.ConsP = string.Format("{0:#,##0}", analise.analise_valor.consumo[0]);
            ViewBag.ConsFPI = string.Format("{0:#,##0}", analise.analise_valor.consumo[1]);
            ViewBag.ConsFPC = string.Format("{0:#,##0}", analise.analise_valor.consumo[2]);
            ViewBag.ConsTotal = string.Format("{0:#,##0}", analise.analise_valor.consumo[3]);

            // consumo simulacao
            ViewBag.ConsP_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor.consumo[0]);
            ViewBag.ConsFPI_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor.consumo[1]);
            ViewBag.ConsFPC_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor.consumo[2]);
            ViewBag.ConsTotal_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor.consumo[3]);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Demanda Ativa Mensal XLS
        private HSSFWorkbook Dem_Ativa_Mensal_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < (ViewBag.NumDiasMes + 1); i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // demanda fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.DemandaFPC[i], _1CellStyle);

                // demanda fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.DemandaFPI[i], _1CellStyle);

                // demanda ponta
                numeroCelulaXLS(row, 3, ViewBag.DemandaP[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Demanda Ativa", "Relatório Mensal", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Demanda", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN, _datahoraStyle);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Média", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP), _1CellStyle);

            // demanda minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MinFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MinFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MinFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MinFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MinP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MinP_DataHoraN, _datahoraStyle);

            // contrato
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Contrato", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_ContratoFP), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_ContratoFP), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_ContratoP), _1CellStyle);

            // tolerancia
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Tolerância (%)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Tol_ContratoFP), _intCellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Tol_ContratoFP), _intCellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Tol_ContratoP), _intCellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // cabecalho ponta, fora de ponta e total
            string[] cab_cons = { "Consumo", "Fora de Ponta Capacitivo", " ", "Fora de Ponta Indutivo", " ", "Ponta", " ", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // consumo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Consumo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.ConsFPC), _intCellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.ConsFPI), _intCellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.ConsP), _intCellStyle);
            numeroCelulaXLS(row, 7, double.Parse(ViewBag.ConsTotal), _intCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Demanda Ativa Mensal XLS simulacao
        private HSSFWorkbook Dem_Ativa_Mensal_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho simulacao
            string[] simulacao = { "Relatório de Demanda Ativa - Simulação" };

            // simulação 
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas do titulo simulacao
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 3));

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < (ViewBag.NumDiasMes + 1); i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[i], _dataStyle);

                // demanda fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.DemandaFPC_Sim[i], _1CellStyle);

                // demanda fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.DemandaFPI_Sim[i], _1CellStyle);

                // demanda ponta
                numeroCelulaXLS(row, 3, ViewBag.DemandaP_Sim[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Demanda Ativa - Simulação", "Relatório Mensal", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Demanda", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN_Sim, _datahoraStyle);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Média", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP_Sim), _1CellStyle);

            // demanda minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MinFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MinFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MinFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MinFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MinP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MinP_DataHoraN_Sim, _datahoraStyle);

            // contrato
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Contrato", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_ContratoFP_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_ContratoFP_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_ContratoP_Sim), _1CellStyle);

            // tolerancia
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Tolerância (%)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Tol_ContratoFP_Sim), _intCellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Tol_ContratoFP_Sim), _intCellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Tol_ContratoP_Sim), _intCellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // cabecalho ponta, fora de ponta e total
            string[] cab_cons = { "Consumo", "Fora de Ponta Capacitivo", " ", "Fora de Ponta Indutivo", " ", "Ponta", " ", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // consumo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Consumo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.ConsFPC_Sim), _intCellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.ConsFPI_Sim), _intCellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.ConsP_Sim), _intCellStyle);
            numeroCelulaXLS(row, 7, double.Parse(ViewBag.ConsTotal_Sim), _intCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Demanda Ativa Anual
        private void Dem_Ativa_Anual(int IDCliente, int IDMedicao, DATAHORA data_hora, int TipoRelat = 0)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_ANUAL relatorio = new RELAT_ANUAL();
            RELAT_ANUAL_ANALISE analise = new RELAT_ANUAL_ANALISE();

            RELAT_ANUAL relatorio_sim = new RELAT_ANUAL();
            RELAT_ANUAL_ANALISE analise_sim = new RELAT_ANUAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao  de cenário
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // calcula valores
            retorno = SmCalcDB_Energia_RelatAnual((char)0, ref config_interface, (char)TipoRelat, IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_ANUAL(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   1, 1, 0, 15, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var DemandaP = new double[14];
            var DemandaFPI = new double[14];
            var DemandaFPC = new double[14];
            var ContratoFP = new double[14];
            var ContratoP = new double[14];
            var ToleranciaFP = new double[14];
            var ToleranciaP = new double[14];
            var Datas = new string[14];
            var DatasN = new DateTime[14];
            var Meses = new string[14];

            // grafico simulacao
            var DemandaP_Sim = new double[14];
            var DemandaFPI_Sim = new double[14];
            var DemandaFPC_Sim = new double[14];
            var ContratoFP_Sim = new double[14];
            var ContratoP_Sim = new double[14];
            var ToleranciaFP_Sim = new double[14];
            var ToleranciaP_Sim = new double[14];
            var Datas_Sim = new string[14];
            var DatasN_Sim = new DateTime[14];
            var Meses_Sim = new string[14];

            double Dem_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 mes para a primeira barra
            strData = strData.AddMonths(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 14; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Meses[i] = string.Format("{0:MMMM}", strData);

                // formata label simulacao
                Datas_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Sim[i] = strData;
                Meses_Sim[i] = string.Format("{0:MMMM}", strData);

                // proximo mes
                strData = strData.AddMonths(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    DemandaP[i] = relatorio.registro[0].valor[0];
                    DemandaFPI[i] = relatorio.registro[0].valor[1];
                    DemandaFPC[i] = relatorio.registro[0].valor[2];
                    ContratoP[i] = relatorio.contrato_ponta[0];
                    ContratoFP[i] = relatorio.contrato_fponta[0];
                    ToleranciaP[i] = ContratoP[i] * (1.0 + (analise.analise_valor.tolerancia[0] / 100.0));
                    ToleranciaFP[i] = ContratoFP[i] * (1.0 + (analise.analise_valor.tolerancia[1] / 100.0));

                    // zera simulacao
                    DemandaP_Sim[i] = relatorio_sim.registro[0].valor[0];
                    DemandaFPI_Sim[i] = relatorio_sim.registro[0].valor[1];
                    DemandaFPC_Sim[i] = relatorio_sim.registro[0].valor[2];
                    ContratoP_Sim[i] = relatorio_sim.contrato_ponta[0];
                    ContratoFP_Sim[i] = relatorio_sim.contrato_fponta[0];
                    ToleranciaP_Sim[i] = ContratoP_Sim[i] * (1.0 + (analise_sim.analise_valor.tolerancia[0] / 100.0));
                    ToleranciaFP_Sim[i] = ContratoFP_Sim[i] * (1.0 + (analise_sim.analise_valor.tolerancia[1] / 100.0));
                }

                if (i == 13)
                {
                    // zera
                    DemandaP[i] = relatorio.registro[11].valor[0];
                    DemandaFPI[i] = relatorio.registro[11].valor[1];
                    DemandaFPC[i] = relatorio.registro[11].valor[2];
                    ContratoP[i] = relatorio.contrato_ponta[11];
                    ContratoFP[i] = relatorio.contrato_fponta[11];
                    ToleranciaP[i] = ContratoP[i] * (1.0 + (analise.analise_valor.tolerancia[0] / 100.0));
                    ToleranciaFP[i] = ContratoFP[i] * (1.0 + (analise.analise_valor.tolerancia[1] / 100.0));

                    // zera simulacao
                    DemandaP_Sim[i] = relatorio_sim.registro[11].valor[0];
                    DemandaFPI_Sim[i] = relatorio_sim.registro[11].valor[1];
                    DemandaFPC_Sim[i] = relatorio_sim.registro[11].valor[2];
                    ContratoP_Sim[i] = relatorio_sim.contrato_ponta[11];
                    ContratoFP_Sim[i] = relatorio_sim.contrato_fponta[11];
                    ToleranciaP_Sim[i] = ContratoP_Sim[i] * (1.0 + (analise_sim.analise_valor.tolerancia[0] / 100.0));
                    ToleranciaFP_Sim[i] = ContratoFP_Sim[i] * (1.0 + (analise_sim.analise_valor.tolerancia[1] / 100.0));
                }

                if (i >= 1 && i <= 12)
                {
                    // copia
                    j = i - 1;

                    DemandaP[i] = relatorio.registro[j].valor[0];
                    DemandaFPI[i] = relatorio.registro[j].valor[1];
                    DemandaFPC[i] = relatorio.registro[j].valor[2];
                    ContratoP[i] = relatorio.contrato_ponta[j];
                    ContratoFP[i] = relatorio.contrato_fponta[j];
                    ToleranciaP[i] = ContratoP[i] * (1.0 + (analise.analise_valor.tolerancia[0] / 100.0));
                    ToleranciaFP[i] = ContratoFP[i] * (1.0 + (analise.analise_valor.tolerancia[1] / 100.0));

                    // copia simulacao
                    DemandaP_Sim[i] = relatorio_sim.registro[j].valor[0];
                    DemandaFPI_Sim[i] = relatorio_sim.registro[j].valor[1];
                    DemandaFPC_Sim[i] = relatorio_sim.registro[j].valor[2];
                    ContratoP_Sim[i] = relatorio_sim.contrato_ponta[j];
                    ContratoFP_Sim[i] = relatorio_sim.contrato_fponta[j];
                    ToleranciaP_Sim[i] = ContratoP_Sim[i] * (1.0 + (analise_sim.analise_valor.tolerancia[0] / 100.0));
                    ToleranciaFP_Sim[i] = ContratoFP_Sim[i] * (1.0 + (analise_sim.analise_valor.tolerancia[1] / 100.0));

                    // verifica demanda maxima
                    if (DemandaP[i] > Dem_max_grafico)
                        Dem_max_grafico = DemandaP[i];

                    if (DemandaFPI[i] > Dem_max_grafico)
                        Dem_max_grafico = DemandaFPI[i];

                    if (DemandaFPC[i] > Dem_max_grafico)
                        Dem_max_grafico = DemandaFPC[i];

                    if (ToleranciaP[i] > Dem_max_grafico)
                        Dem_max_grafico = ToleranciaP[i];

                    if (ToleranciaFP[i] > Dem_max_grafico)
                        Dem_max_grafico = ToleranciaFP[i];

                    // verifica demanda simulacao
                    if (DemandaP_Sim[i] > Dem_max_grafico)
                        Dem_max_grafico = DemandaP_Sim[i];

                    if (DemandaFPI_Sim[i] > Dem_max_grafico)
                        Dem_max_grafico = DemandaFPI_Sim[i];

                    if (DemandaFPC_Sim[i] > Dem_max_grafico)
                        Dem_max_grafico = DemandaFPC_Sim[i];

                    if (ToleranciaP_Sim[i] > Dem_max_grafico)
                        Dem_max_grafico = ToleranciaP_Sim[i];

                    if (ToleranciaFP_Sim[i] > Dem_max_grafico)
                        Dem_max_grafico = ToleranciaFP_Sim[i];
                }
            }

            Dem_max_grafico = Dem_max_grafico * 1.1;

            if (Dem_max_grafico < 1.0)
            {
                Dem_max_grafico = 1.0;
            }

            ViewBag.DemMaxGrafico = Dem_max_grafico;

            ViewBag.DemandaP = DemandaP;
            ViewBag.DemandaFPI = DemandaFPI;
            ViewBag.DemandaFPC = DemandaFPC;
            ViewBag.ContratoP = ContratoP;
            ViewBag.ContratoFP = ContratoFP;
            ViewBag.ToleranciaP = ToleranciaP;
            ViewBag.ToleranciaFP = ToleranciaFP;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Meses = Meses;

            // simulacao
            ViewBag.DemandaP_Sim = DemandaP_Sim;
            ViewBag.DemandaFPI_Sim = DemandaFPI_Sim;
            ViewBag.DemandaFPC_Sim = DemandaFPC_Sim;
            ViewBag.ContratoP_Sim = ContratoP_Sim;
            ViewBag.ContratoFP_Sim = ContratoFP_Sim;
            ViewBag.ToleranciaP_Sim = ToleranciaP_Sim;
            ViewBag.ToleranciaFP_Sim = ToleranciaFP_Sim;
            ViewBag.Datas_Sim = Datas_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;
            ViewBag.Meses_Sim = Meses_Sim;

            // nome do relatorio
            if (TipoRelat == 0)
                ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.DemandaAtiva;
            else
                ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.DemandaReativa;

            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Anual;

            // data atual
            ViewBag.DataAtual = string.Format("{0:yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:yyyy}", DataAtual);

            // demanda maxima ponta
            ViewBag.Dem_MaxP = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[0]);
            DateTime Dem_MaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (Dem_MaxP_DataHora.Year != 2000)
                ViewBag.Dem_MaxP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MaxP_DataHora, Dem_MaxP_DataHora);
            else
                ViewBag.Dem_MaxP_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxP_DataHoraN = Dem_MaxP_DataHora;

            // demanda maxima ponta simulacao
            ViewBag.Dem_MaxP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[0]);
            DateTime Dem_MaxP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[0]);
            if (Dem_MaxP_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxP_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", Dem_MaxP_DataHora_Sim, Dem_MaxP_DataHora_Sim);
            else
                ViewBag.Dem_MaxP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MaxP_DataHoraN_Sim = Dem_MaxP_DataHora_Sim;

            // demanda maxima fora ponta indutivo 
            ViewBag.Dem_MaxFPI = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[1]);
            DateTime Dem_MaxFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[1]);
            if (Dem_MaxFPI_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPI_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MaxFPI_DataHora, Dem_MaxFPI_DataHora);
            else
                ViewBag.Dem_MaxFPI_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxFPI_DataHoraN = Dem_MaxFPI_DataHora;

            // demanda maxima fora ponta indutivo  simulacao
            ViewBag.Dem_MaxFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[1]);
            DateTime Dem_MaxFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[1]);
            if (Dem_MaxFPI_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxFPI_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", Dem_MaxFPI_DataHora_Sim, Dem_MaxFPI_DataHora_Sim);
            else
                ViewBag.Dem_MaxFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MaxFPI_DataHoraN_Sim = Dem_MaxFPI_DataHora_Sim;

            // demanda maxima fora ponta capacitivo 
            ViewBag.Dem_MaxFPC = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[2]);
            DateTime Dem_MaxFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[2]);
            if (Dem_MaxFPC_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPC_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MaxFPC_DataHora, Dem_MaxFPC_DataHora);
            else
                ViewBag.Dem_MaxFPC_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxFPC_DataHoraN = Dem_MaxFPC_DataHora;

            // demanda maxima fora ponta capacitivo simulacao
            ViewBag.Dem_MaxFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[2]);
            DateTime Dem_MaxFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[2]);
            if (Dem_MaxFPC_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxFPC_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", Dem_MaxFPC_DataHora_Sim, Dem_MaxFPC_DataHora_Sim);
            else
                ViewBag.Dem_MaxFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MaxFPC_DataHoraN_Sim = Dem_MaxFPC_DataHora_Sim;

            // demanda minima ponta
            ViewBag.Dem_MinP = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[0]);
            DateTime Dem_MinP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[0]);
            if (Dem_MinP_DataHora.Year != 2000)
                ViewBag.Dem_MinP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MinP_DataHora, Dem_MinP_DataHora);
            else
                ViewBag.Dem_MinP_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MinP_DataHoraN = Dem_MinP_DataHora;

            // demanda minima ponta simulacao
            ViewBag.Dem_MinP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.minimo[0]);
            DateTime Dem_MinP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[0]);
            if (Dem_MinP_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MinP_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", Dem_MinP_DataHora_Sim, Dem_MinP_DataHora_Sim);
            else
                ViewBag.Dem_MinP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MinP_DataHoraN_Sim = Dem_MinP_DataHora_Sim;

            // demanda minima fora ponta indutivo
            ViewBag.Dem_MinFPI = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[1]);
            DateTime Dem_MinFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[1]);
            if (Dem_MinFPI_DataHora.Year != 2000)
                ViewBag.Dem_MinFPI_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MinFPI_DataHora, Dem_MinFPI_DataHora);
            else
                ViewBag.Dem_MinFPI_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MinFPI_DataHoraN = Dem_MinFPI_DataHora;

            // demanda minima fora ponta indutivo simulacao
            ViewBag.Dem_MinFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.minimo[1]);
            DateTime Dem_MinFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[1]);
            if (Dem_MinFPI_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MinFPI_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", Dem_MinFPI_DataHora_Sim, Dem_MinFPI_DataHora_Sim);
            else
                ViewBag.Dem_MinFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MinFPI_DataHoraN_Sim = Dem_MinFPI_DataHora_Sim;

            // demanda minima fora ponta capacitivo 
            ViewBag.Dem_MinFPC = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[2]);
            DateTime Dem_MinFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[2]);
            if (Dem_MinFPC_DataHora.Year != 2000)
                ViewBag.Dem_MinFPC_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MinFPC_DataHora, Dem_MinFPC_DataHora);
            else
                ViewBag.Dem_MinFPC_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MinFPC_DataHoraN = Dem_MinFPC_DataHora;

            // demanda minima fora ponta capacitivo simulacao
            ViewBag.Dem_MinFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.minimo[2]);
            DateTime Dem_MinFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[2]);
            if (Dem_MinFPC_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MinFPC_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", Dem_MinFPC_DataHora_Sim, Dem_MinFPC_DataHora_Sim);
            else
                ViewBag.Dem_MinFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MinFPC_DataHoraN_Sim = Dem_MinFPC_DataHora_Sim;

            // demanda media 
            ViewBag.Dem_MedP = string.Format("{0:#,##0.0}", analise.analise_valor.medio[0]);
            ViewBag.Dem_MedFPI = string.Format("{0:#,##0.0}", analise.analise_valor.medio[1]);
            ViewBag.Dem_MedFPC = string.Format("{0:#,##0.0}", analise.analise_valor.medio[2]);

            // demanda media simulacao
            ViewBag.Dem_MedP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[0]);
            ViewBag.Dem_MedFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[1]);
            ViewBag.Dem_MedFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[2]);

            // tolerancia
            ViewBag.Tol_ContratoP = string.Format("{0:0}", analise.analise_valor.tolerancia[0]);
            ViewBag.Tol_ContratoFP = string.Format("{0:0}", analise.analise_valor.tolerancia[1]);

            // tolerancia simulacao
            ViewBag.Tol_ContratoP_Sim = string.Format("{0:0}", analise_sim.analise_valor.tolerancia[0]);
            ViewBag.Tol_ContratoFP_Sim = string.Format("{0:0}", analise_sim.analise_valor.tolerancia[1]);

            // consumo
            ViewBag.ConsP = string.Format("{0:#,##0}", analise.analise_valor.consumo[0]);
            ViewBag.ConsFPI = string.Format("{0:#,##0}", analise.analise_valor.consumo[1]);
            ViewBag.ConsFPC = string.Format("{0:#,##0}", analise.analise_valor.consumo[2]);
            ViewBag.ConsTotal = string.Format("{0:#,##0}", analise.analise_valor.consumo[3]);

            // consumo simulacao
            ViewBag.ConsP_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor.consumo[0]);
            ViewBag.ConsFPI_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor.consumo[1]);
            ViewBag.ConsFPC_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor.consumo[2]);
            ViewBag.ConsTotal_Sim = string.Format("{0:#,##0}", analise_sim.analise_valor.consumo[3]);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddYears(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Demanda Ativa Anual XLS
        private HSSFWorkbook Dem_Ativa_Anual_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 13; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // demanda fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.DemandaFPC[i], _1CellStyle);

                // demanda fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.DemandaFPI[i], _1CellStyle);

                // demanda ponta
                numeroCelulaXLS(row, 3, ViewBag.DemandaP[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Demanda Ativa", "Relatório Anual", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Demanda", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN, _datahoraStyle);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Média", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP), _1CellStyle);

            // demanda minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MinFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MinFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MinFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MinFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MinP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MinP_DataHoraN, _datahoraStyle);

            // contrato 
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Contrato", _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.ContratoFP[1], _1CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.ContratoFP[1], _1CellStyle);
            numeroCelulaXLS(row, 5, ViewBag.ContratoP[1], _1CellStyle);

            // tolerancia 
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Tolerância (%)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Tol_ContratoFP), _intCellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Tol_ContratoFP), _intCellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Tol_ContratoP), _intCellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // cabecalho ponta, fora de ponta e total
            string[] cab_cons = { "Consumo", "Fora de Ponta Capacitivo", " ", "Fora de Ponta Indutivo", " ", "Ponta", " ", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // consumo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Consumo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.ConsFPC), _intCellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.ConsFPI), _intCellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.ConsP), _intCellStyle);
            numeroCelulaXLS(row, 7, double.Parse(ViewBag.ConsTotal), _intCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Demanda Ativa Anual XLS Simulacao
        private HSSFWorkbook Dem_Ativa_Anual_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho simulacao
            string[] simulacao = { "Relatório de Demanda Ativa - Simulação" };

            // simulação 
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas do titulo simulacao
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 3));

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 13; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // demanda fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.DemandaFPC_Sim[i], _1CellStyle);

                // demanda fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.DemandaFPI_Sim[i], _1CellStyle);

                // demanda ponta
                numeroCelulaXLS(row, 3, ViewBag.DemandaP_Sim[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Demanda Ativa - Simulação", "Relatório Anual", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Demanda", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN_Sim, _datahoraStyle);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Média", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP_Sim), _1CellStyle);

            // demanda minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MinFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MinFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MinFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MinFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MinP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MinP_DataHoraN_Sim, _datahoraStyle);

            // contrato 
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Contrato", _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.ContratoFP_Sim[1], _1CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.ContratoFP_Sim[1], _1CellStyle);
            numeroCelulaXLS(row, 5, ViewBag.ContratoP_Sim[1], _1CellStyle);

            // tolerancia 
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Tolerância (%)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Tol_ContratoFP_Sim), _intCellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Tol_ContratoFP_Sim), _intCellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Tol_ContratoP_Sim), _intCellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // cabecalho ponta, fora de ponta e total
            string[] cab_cons = { "Consumo", "Fora de Ponta Capacitivo", " ", "Fora de Ponta Indutivo", " ", "Ponta", " ", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // consumo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Consumo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.ConsFPC_Sim), _intCellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.ConsFPI_Sim), _intCellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.ConsP_Sim), _intCellStyle);
            numeroCelulaXLS(row, 7, double.Parse(ViewBag.ConsTotal_Sim), _intCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }


        // Demanda Ativa/Reativa/Fator de Potencia exportar XLS
        private HSSFWorkbook Dem_exportar_XLS(int IDCliente, int IDMedicao, DateTime DataIni, DateTime DataFim)
        {
            // datas
            ViewBag.DataAtualN = DataIni;
            ViewBag.DataFinalN = DataFim;

            // calcula
            EN_Metodos enMetodos = new EN_Metodos();
            List<EN_Dominio> enRegistros = enMetodos.ListarTodosPeriodo(IDCliente, IDMedicao, DataIni, DataFim);

            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Período", "Demanda Ativa", "Demanda Reativa", "Fator de Potência" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            if( enRegistros != null )
            {
                // percorre valores
                foreach(EN_Dominio registro in enRegistros)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // data e hora
                    datahoraCelulaXLS(row, 0, registro.DataHora, _datahoraStyle);

                    // periodo
                    numeroCelulaXLS(row, 1, registro.Periodo, _intCellStyle);

                    // demanda ativa
                    numeroCelulaXLS(row, 2, registro.Ativo, _1CellStyle);

                    // demanda reativa
                    numeroCelulaXLS(row, 3, registro.Reativo, _1CellStyle);

                    // fator de potencia
                    double fatpot = 1.0;

                    if( registro.Ativo != 0.0 )
                    {
                        double aux1 = registro.Reativo / registro.Ativo;
                        fatpot = 1.0 / Math.Sqrt(1.0 + (aux1 * aux1));

                        fatpot *= (registro.Reativo < 0.0) ? -1.0 : 1.0;
                    }

                    numeroCelulaXLS(row, 4, fatpot, _3CellStyle);

                    // proxima
                    rowIndex++;
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Demanda" , "", _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(9).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }
    }
}