﻿using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {
        // GET: Distribuição de Consumo - Configuracao
        public ActionResult DistribuicaoConsumo_Configuracao(int IDCliente)
        {
            // tela de ajuda - Distribuicao de Consumo
            CookieStore.SalvaCookie_String("PaginaAjuda", "DistribuicaoCons_Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le distribuicoes de consumo
            DistribuicaoConsumoMetodos distribuicaoMetodos = new DistribuicaoConsumoMetodos();
            List<DistribuicaoConsumoDominio> listaDistribuicao = distribuicaoMetodos.ListarPorIDCliente(IDCliente);

            return View(listaDistribuicao);
        }

        // GET: Distribuição de Consumo - Editar
        public ActionResult DistribuicaoConsumo_Editar(int IDDistribuicaoConsumo)
        {
            int IDCliente = 0;

            // tela de ajuda - Distribuicao de Consumo
            CookieStore.SalvaCookie_String("PaginaAjuda", "DistribuicaoCons_Editar");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "DistribuicaoConsumo");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes 
            Permissoes();

            // verifica se adicionando
            DistribuicaoConsumoDominio distribuicao = new DistribuicaoConsumoDominio();
            if (IDDistribuicaoConsumo == 0)
            {
                // zera com default
                distribuicao.IDCliente = ViewBag._IDCliente;
                distribuicao.Nome = "";

                // IDCliente
                IDCliente = ViewBag._IDCliente;
            }
            else
            {
                // le distribuicao de consumo
                DistribuicaoConsumoMetodos distribuicaoMetodos = new DistribuicaoConsumoMetodos();
                distribuicao = distribuicaoMetodos.ListarPorId(IDDistribuicaoConsumo);

                // IDCliente
                IDCliente = distribuicao.IDCliente;
            }

            // le grupos
            DistribuicaoConsumoGruposMedMetodos distribuicaoGrupoMetodos = new DistribuicaoConsumoGruposMedMetodos();
            List<DistribuicaoConsumoGruposMedDominio> distribuicaoGrupos = distribuicaoGrupoMetodos.ListarPorIDDistribuicao(distribuicao.IDDistribuicaoConsumo);

            // cria lista de grupos desta distribuicao
            List<int> ConfigGrupoList_Distribuicao = new List<int>();

            foreach (DistribuicaoConsumoGruposMedDominio distribuicaoGrupo in distribuicaoGrupos)
            {
                ConfigGrupoList_Distribuicao.Add(distribuicaoGrupo.IDGrupoMedicoes);
            }

            // le grupos configurados para a lista
            GrupoMedicoesMetodos gruposMetodos = new GrupoMedicoesMetodos();
            List<GrupoMedicoesDominio> grupos = new List<GrupoMedicoesDominio>();

            List<GrupoMedicoesDominio> grupos_utilizados = new List<GrupoMedicoesDominio>();
            List<GrupoMedicoesDominio> grupos_nao_utilizados = new List<GrupoMedicoesDominio>();

            if (ConfigGrupoList_Distribuicao != null)
            {
                // le grupos no cliente
                grupos = gruposMetodos.ListarPorIDCliente(IDCliente);

                // seleciona grupos
                int contador;

                // percorre lista
                if (grupos != null)
                {
                    for (contador = 0; contador < grupos.Count(); contador++)
                    {
                        // verifica se existe lista de grupos
                        if (ConfigGrupoList_Distribuicao != null)
                        {
                            // verifica se grupo esta habilitado para a distribuicao
                            if (ConfigGrupoList_Distribuicao.Contains(grupos[contador].IDGrupoMedicoes))
                            {
                                // copia grupo na lista de utilizados
                                grupos_utilizados.Add(grupos[contador]);
                            }
                            else
                            {
                                // copia grupo na lista de nao utilizados
                                grupos_nao_utilizados.Add(grupos[contador]);
                            }
                        }
                        else
                        {
                            // copia grupo na lista de nao utilizados
                            grupos_nao_utilizados.Add(grupos[contador]);
                        }
                    }
                }
            }

            ViewBag.Grupos = grupos_utilizados;
            ViewBag.Grupos2 = grupos_nao_utilizados;
            ViewBag.DistribuicaoGrupos = distribuicaoGrupos;

            return View(distribuicao);
        }

        // POST: Distribuição de Consumo - Salvar
        [HttpPost]
        public ActionResult DistribuicaoConsumo_Salvar(DistribuicaoConsumoDominio distribuicao, List<DistribuicaoConsumoGruposMedDominio> distribuicaoGrupos)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                IDDistribuicaoConsumo = distribuicao.IDDistribuicaoConsumo,
                erro = ""
            };

            // verifica se existe outra distribuição com o mesmo nome
            DistribuicaoConsumoMetodos distribuicaoMetodos = new DistribuicaoConsumoMetodos();

            if (distribuicaoMetodos.VerificarDuplicidade(distribuicao))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    IDDistribuicaoConsumo = distribuicao.IDDistribuicaoConsumo,
                    erro = "Distribuição de Consumo existente."
                };
            }

            else
            {
                // salva distribuição
                distribuicaoMetodos.Salvar(distribuicao);

                // pego distribuicao novamente (pois pode ter sido insercao)
                DistribuicaoConsumoDominio novaDistribuicao = new DistribuicaoConsumoDominio();

                // verifica se alterando
                if (distribuicao.IDDistribuicaoConsumo > 0)
                {
                    // caso alterando apenas copia do DB
                    novaDistribuicao = distribuicaoMetodos.ListarPorId(distribuicao.IDDistribuicaoConsumo);
                }

                else
                {
                    // caso inserindo faz leitura por IDCliente e Nome para pegar novo ID
                    novaDistribuicao = distribuicaoMetodos.ListarPorNomeCliente(distribuicao.IDCliente, distribuicao.Nome);
                }


                returnedData = new
                {
                    status = "OK",
                    IDDistribuicaoConsumo = novaDistribuicao.IDDistribuicaoConsumo,
                    erro = ""
                };

                // salva distribuicao de consumo grupo
                if (novaDistribuicao != null)
                {
                    DistribuicaoConsumoGruposMedMetodos distribuicaoGruposMetodos = new DistribuicaoConsumoGruposMedMetodos();

                    // lista grupo de medicoes atual 
                    List<DistribuicaoConsumoGruposMedDominio> distribuicaoGruposAntiga = distribuicaoGruposMetodos.ListarPorIDDistribuicao(distribuicao.IDDistribuicaoConsumo);

                    // salva distribuicao grupo
                    if (distribuicaoGrupos != null)
                    {
                        // percorre lista e salva
                        foreach (DistribuicaoConsumoGruposMedDominio distribuicaoGrupo in distribuicaoGrupos)
                        {
                            // verifica se adicionando
                            if (distribuicaoGrupo.IDDistribuicaoConsumoGruposMed == 0)
                            {
                                // caso adicionando preenche campos que faltam com default
                                distribuicaoGrupo.IDDistribuicaoConsumo = novaDistribuicao.IDDistribuicaoConsumo;
                            }

                            // salva grupo
                            distribuicaoGruposMetodos.Salvar(distribuicaoGrupo);
                        }
                    }
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Distribuição de Consumo - Excluir
        public ActionResult DistribuicaoConsumo_Excluir(int IDDistribuicaoConsumo)
        {
            // apaga a distribuição
            DistribuicaoConsumoMetodos distribuicaoMetodos = new DistribuicaoConsumoMetodos();
            distribuicaoMetodos.Excluir(IDDistribuicaoConsumo);

            // apaga grupos de medicoes
            DistribuicaoConsumoGruposMedMetodos distribuicaoGruposMetodos = new DistribuicaoConsumoGruposMedMetodos();
            distribuicaoGruposMetodos.ExcluirPorDistribuicao(IDDistribuicaoConsumo);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}