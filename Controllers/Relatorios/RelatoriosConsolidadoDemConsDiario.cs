﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {

        //
        // CONSOLIDADO DIARIO DEMANDA e CONSUMO
        //

        // GET: Relatorio Consolidado Diario - Demanda e Consumo 
        public ActionResult Relat_Consolidado_DemCons_Diario(int IDCliente)
        {
            // tipo do relatorio
            int TipoRelat = 52;
            int TipoConsolidado = 0;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Relat_Consolidado_Diario");
            CookieStore.SalvaCookie_Int("Relat_Tipo", TipoRelat);

            // le cookies
            LeCookies_SmartEnergy();

            // ano atual
            DateTime datahora_ultima = DateTime.Now;
            DATAHORA data_hora = new DATAHORA();

            // le cookie datahora (atual ou inicial)
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            // verifica se existe datahora no cookie
            if (datahora_cookie.Year != 2000)
            {
                // copia data do cookie para ultima
                datahora_ultima = datahora_cookie;
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_ultima);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", datahora_ultima);

            // tipo do relatorio
            ViewBag.TipoRelat = TipoRelat;
            ViewBag.TipoConsolidado = TipoConsolidado;

            // seta para diario
            ViewBag.Relat_TipoPeriodo = 0;

            // valores
            List<ConsolidadoDiario> consolidados = new List<ConsolidadoDiario>();
            ViewBag.consolidados = consolidados;

            // lista de erros
            var listaErros = new List<string>();

            // erros
            ViewBag.listaErros = listaErros;

            return View();
        }

        // GET: Relatorio Grafico
        private ActionResult Relat_Consolidado_Diario_Show(int IDCliente, int tipo_arquivo = 0)
        {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           // tipo do relatorio
            int TipoRelat = TIPO_RELAT.ConsolidadoEnergia_Diario;

            // salva cookie 
            CookieStore.SalvaCookie_Int("Relat_Tipo", TipoRelat);

            // le cookies
            LeCookies_SmartEnergy();

            // ano atual
            DateTime datahora_ultima = DateTime.Now;
            DATAHORA data_hora = new DATAHORA();

            // le cookie datahora (atual ou inicial)
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            // verifica se existe datahora no cookie
            if (datahora_cookie.Year != 2000)
            {
                // copia data do cookie para ultima
                datahora_ultima = datahora_cookie;
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_ultima);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // tipo do relatorio
            ViewBag.TipoRelat = TipoRelat;

            // tipo consolidado
            string TipoConsolidadoStr = CookieStore.LeCookie_String("Relat_TipoConsolidado");
            int TipoConsolidado = 0;

            // verifica se foi mudado o tab
            if(TipoConsolidadoStr.Length == 8)
            {
                string aux_str = TipoConsolidadoStr.Substring(7, 1);
                TipoConsolidado = int.Parse(aux_str) - 1;
            }

            ViewBag.TipoConsolidado = TipoConsolidado;

            // View do relatorio
            string viewRelatorio = "_Consolidado_DemCons_Diario";

            // calcula
            Calc_Consolidado_Diario(IDCliente, data_hora);


            // verifica se PDF
            if (tipo_arquivo == 1)
            {
                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.pdf", viewRelatorio, IDCliente, data_atual);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Landscape,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna nome do arquivo
                var returnedData = new
                {
                    nomeArquivo = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // verifica se XLS
            if (tipo_arquivo == 2)
            {
                // Planilha Excel
                var workbook = new HSSFWorkbook();

                // monta XLS
                workbook = Consolidado_Diario_XLS(IDCliente);

                // gera identificador unico aonde o arquivo sera guardado
                string handle = Guid.NewGuid().ToString();

                using (MemoryStream memoryStream = new MemoryStream())
                {
                    workbook.Write(memoryStream);
                    memoryStream.Position = 0;
                    Session[handle] = memoryStream.ToArray();
                }

                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.xls", viewRelatorio, IDCliente, data_atual);

                // retorna nome do arquivo
                var returnedData = new
                {
                    FileGuid = handle,
                    FileName = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // verifica se EMAIL
            if (tipo_arquivo == 3)
            {
                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.pdf", viewRelatorio, IDCliente, data_atual);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Landscape,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna 
                ViewBag.caminhoCompleto = caminhoCompleto;
                var returnedData = new
                {
                    caminhoCompleto = caminhoCompleto
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            return View();
        }

        // Calcula Consolidado Diario
        private void Calc_Consolidado_Diario(int IDCliente, DATAHORA datahora)
        {
            // teste
            LogMessage_Consolidado_Diario(IDCliente, "--------------------------------------------------------------------------");
            LogMessage_Consolidado_Diario(IDCliente, string.Format("Inicia Consolidado Diário - IDCliente [{0:000000}] - {1:d}", IDCliente, datahora));

            // le cookies
            LeCookies_SmartEnergy();

            // le medicoes
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicoesMetodos.ListarTodosUnidade(IDCliente, ViewBag._ConfigMed);

            List<ConsolidadoDiario> consolidados = new List<ConsolidadoDiario>();

            // lista de erros
            var listaErros = new List<string>();

            // percorre medicoes
            if( medicoes != null )
            {
                // teste
                LogMessage_Consolidado_Diario(IDCliente, string.Format("Número Medições [{0}]", medicoes.Count));
                LogMessage_Consolidado_Diario(IDCliente, "--------------------------------------------------------------------------");

                int contador = 0;

                foreach (MedicoesDominio medicao in medicoes)
                {
                    // teste
                    LogMessage_Consolidado_Diario(IDCliente, string.Format("Medição [{0:000000}] - Tipo [{1}] - {2} de {3}", medicao.IDMedicao, medicao.IDTipoMedicao, contador, medicoes.Count));
                    contador++;

                    // verifica se nao eh energia
                    if ( medicao.IDTipoMedicao != 0 && medicao.IDTipoMedicao != 1 )
                    {
                        continue;
                    }

                    // calcula valores
                    EN_Metodos ENMetodos = new EN_Metodos();
                    DateTime data = Funcoes_Converte.ConverteDataHora2DateTime(datahora);
                    EN_ConsolidadoDiario relatorio = ENMetodos.ListarConsolidadoDiario(medicao.IDCliente, medicao.IDMedicao, data);

                    // teste
                    LogMessage_Consolidado_Diario(IDCliente, string.Format("Medição [{0:000000}] - Retorno [{1}]", medicao.IDMedicao, 0));

                    // coloca na lista
                    ConsolidadoDiario consolidado = new ConsolidadoDiario();

                    // zera estrutura
                    for (int i = 0; i < 96; i++)
                    {
                        ConsolidadoDiarioValor temp = new ConsolidadoDiarioValor();

                        temp.DemandaAtiva = 0.0;
                        temp.DemandaReativa = 0.0;
                        temp.FatPot = 1.0;
                        temp.Periodo15min = 3;

                        temp.ConsumoAtivo = 0.0;
                        temp.ConsumoReativo = 0.0;
                        temp.FatPotHora = 1.0;
                        temp.Periodo1hora = 3;

                        consolidado.ConsolidadoDiarioValor[i] = temp;
                    }

                    // copia
                    if( relatorio != null )
                    {
                        // copia estrutura
                        for (int i = 0; i < 96; i++)
                        {
                            // demanda
                            consolidado.ConsolidadoDiarioValor[i].DemandaAtiva = relatorio.DemandaAtiva[i];
                            consolidado.ConsolidadoDiarioValor[i].DemandaReativa = relatorio.DemandaReativa[i];
                            consolidado.ConsolidadoDiarioValor[i].FatPot = relatorio.FatPot[i];
                            consolidado.ConsolidadoDiarioValor[i].Periodo15min = relatorio.Periodo15min[i];
                        }

                        for (int i = 0; i < 24; i++)
                        {
                            // consumo
                            consolidado.ConsolidadoDiarioValor[i].ConsumoAtivo = relatorio.ConsumoAtivo[i];
                            consolidado.ConsolidadoDiarioValor[i].ConsumoReativo = relatorio.ConsumoReativo[i];
                            consolidado.ConsolidadoDiarioValor[i].FatPotHora = relatorio.FatPotHora[i];
                            consolidado.ConsolidadoDiarioValor[i].Periodo1hora = relatorio.Periodo1hora[i];
                        }
                    }

                    consolidado.IDMedicao = medicao.IDMedicao;
                    consolidado.Nome = medicao.Nome;
                    consolidado.NomeUnidade = medicao.NomeUnidade;

                    consolidados.Add(consolidado);
                }
            }

            // teste
            LogMessage_Consolidado_Diario(IDCliente, "--------------------------------------------------------------------------");
            LogMessage_Consolidado_Diario(IDCliente, "Fim");
            LogMessage_Consolidado_Diario(IDCliente, "--------------------------------------------------------------------------");

            // valores
            ViewBag.consolidados = consolidados;

            // erros
            ViewBag.listaErros = listaErros;

            // converte data e hora
            DateTime DataAtual = Funcoes_Converte.ConverteDataHora2DateTime(datahora);

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:d}", DataAtual);

            // tipo do relatorio
            ViewBag.TipoRelat = 52;

            // seta para diario
            ViewBag.Relat_TipoPeriodo = 0;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.ConsolidadoDiario;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Diario;

            return;
        }

        // Consolidado Diario XLS
        private HSSFWorkbook Consolidado_Diario_XLS(int IDCliente)
        {
            // cria excel
            HSSFWorkbook workbook = new HSSFWorkbook();

            // planilhas
            Consolidado_Diario_Planilha(workbook, 0);
            Consolidado_Diario_Planilha(workbook, 1);

            // retorna planilha
            return workbook;
        }

        // Consolidado Diario Planilha
        private void Consolidado_Diario_Planilha(HSSFWorkbook workbook, int TipoConsolidado)
        {
            List<ConsolidadoDiario> consolidados = ViewBag.consolidados;

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _datahorafullStyle = criaEstiloXLS(workbook, 13);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            // cria planilha
            string str_planilha = "";

            switch (TipoConsolidado)
            {
                case 0:
                    str_planilha = "Consumo Total (kWh)";
                    break;

                case 1:
                    str_planilha = "Demanda Ativa (kW)";
                    break;
            }

            ISheet sheet = workbook.CreateSheet(str_planilha);
            int rowIndex = 0;

            // verifica se demanda
            if (TipoConsolidado >= 1)
            {
                // cabecalho
                string[] cabecalho = { "ID", "Medições", "Unidades", "00:15", "00:30", "00:45", "01:00", 
                                                                     "01:15", "01:30", "01:45", "02:00",   
                                                                     "02:15", "02:30", "02:45", "03:00",   
                                                                     "03:15", "03:30", "03:45", "04:00",   
                                                                     "04:15", "04:30", "04:45", "05:00",   
                                                                     "05:15", "05:30", "05:45", "06:00",   
                                                                     "06:15", "06:30", "06:45", "07:00",   
                                                                     "07:15", "07:30", "07:45", "08:00",   
                                                                     "08:15", "08:30", "08:45", "09:00",   
                                                                     "09:15", "09:30", "09:45", "10:00",   
                                                                     "10:15", "10:30", "10:45", "11:00",   
                                                                     "11:15", "11:30", "11:45", "12:00",   
                                                                     "12:15", "12:30", "12:45", "13:00",   
                                                                     "13:15", "13:30", "13:45", "14:00",   
                                                                     "14:15", "14:30", "14:45", "15:00",   
                                                                     "15:15", "15:30", "15:45", "16:00",   
                                                                     "16:15", "16:30", "16:45", "17:00",   
                                                                     "17:15", "17:30", "17:45", "18:00",   
                                                                     "18:15", "18:30", "18:45", "19:00",   
                                                                     "19:15", "19:30", "19:45", "20:00",   
                                                                     "20:15", "20:30", "20:45", "21:00",   
                                                                     "21:15", "21:30", "21:45", "22:00",   
                                                                     "22:15", "22:30", "22:45", "23:00",   
                                                                     "23:15", "23:30", "23:45", "00:00" };

                // adiciona cabecalho
                rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);
            }
            else
            {
                // cabecalho
                string[] cabecalho = { "ID", "Medições", "Unidades", "01:00", "02:00", "03:00", "04:00", "05:00" , "06:00" , "07:00" , "08:00" , "09:00" , "10:00" , "11:00" , "12:00",
                                                                     "13:00", "14:00", "15:00", "16:00", "17:00" , "18:00" , "19:00" , "20:00" , "21:00" , "22:00" , "23:00" , "00:00" };

                // adiciona cabecalho
                rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);
            }

            // adiciona linhas
            int i;
            IRow row;

            if (consolidados != null)
            {
                foreach (ConsolidadoDiario consolidado in consolidados)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // IDMedicao
                    numeroCelulaXLS(row, 0, consolidado.IDMedicao, _intCellStyle);

                    // medicao
                    textoCelulaXLS(row, 1, consolidado.Nome);

                    // unidade
                    textoCelulaXLS(row, 2, consolidado.NomeUnidade);

                    // verifica se demanda
                    if (TipoConsolidado >= 1)
                    {
                        // registros
                        for (i = 0; i < 96; i++)
                        {
                            // valor
                            numeroCelulaXLS(row, i + 3, consolidado.ConsolidadoDiarioValor[i].DemandaAtiva, _1CellStyle);
                        }
                    }
                    else
                    {
                        // registros
                        for (i = 0; i < 24; i++)
                        {
                            // valor
                            numeroCelulaXLS(row, i + 3, consolidado.ConsolidadoDiarioValor[i].ConsumoAtivo, _intCellStyle);
                        }
                    }

                    // proxima
                    rowIndex++;
                }
            }

            // largura de cada coluna
            sheet.SetColumnWidth(0, 6000);
            for (i = 1; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 4000);

            return;
        }

        private void LogMessage_Consolidado_Diario(int IDCliente, string msg)
        {
            // arquivo
            string arquivo = string.Format("_{0:000000}", IDCliente);

            // log
            Funcoes_Log.Mensagem("Consolidado_Diario", msg, arquivo);
        }
    }
}