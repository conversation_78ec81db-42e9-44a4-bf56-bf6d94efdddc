﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {
        // Relatorio Consolidado Mensal
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_ResumoMensal", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_ResumoMensal(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref INFO_MEDICAO pinfo_medicao, ref DATAHORA pdatahora, ref HORA pturno_inicio, ref HORA pturno_fim, ref RESUMO_CONSOLIDADO_MENSAL pconsolidado);

        //
        // CONSOLIDADO MENSAL DEMANDA e CONSUMO
        //

        // GET: Relatorio Consolidado Mensal - Demanda e Consumo
        public ActionResult Relat_Consolidado_DemCons_Mensal(int IDCliente)
        {
            // tipo do relatorio
            int TipoRelat = 51;
            int TipoConsolidado = 0;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Relat_Consolidado_Mensal");
            CookieStore.SalvaCookie_Int("Relat_Tipo", TipoRelat);

            // le cookies
            LeCookies_SmartEnergy();

            // mes atual
            DateTime datahora_ultima = DateTime.Now;
            DATAHORA data_hora = new DATAHORA();

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_ultima);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", datahora_ultima);



            // le cookie turno
            DateTime turnoIni_cookie = CookieStore.LeCookie_Datahora("Consolidado_TurnoIni");
            DateTime turnoFim_cookie = CookieStore.LeCookie_Datahora("Consolidado_TurnoFim");

            // verifica se tem turno
            if (turnoIni_cookie.Hour == 0 && turnoIni_cookie.Minute == 0 && turnoFim_cookie.Hour == 0 && turnoFim_cookie.Minute == 0)
            {
                turnoIni_cookie = new DateTime(2000, 1, 1, 0, 0, 0);
                turnoFim_cookie = new DateTime(2000, 1, 1, 6, 0, 0);

                // salva cookie
                CookieStore.SalvaCookie_Datahora("Consolidado_TurnoIni", turnoIni_cookie);
                CookieStore.SalvaCookie_Datahora("Consolidado_TurnoFim", turnoFim_cookie);
            }

            // turno
            ViewBag.TurnoIni = string.Format("{0:HH}:{1:mm}", turnoIni_cookie, turnoIni_cookie);
            ViewBag.TurnoFim = string.Format("{0:HH}:{1:mm}", turnoFim_cookie, turnoFim_cookie);



            // tipo do relatorio
            ViewBag.TipoRelat = TipoRelat;
            ViewBag.TipoConsolidado = TipoConsolidado;

            // seta para mensal
            ViewBag.Relat_TipoPeriodo = 2;

            // valores
            List<ConsolidadoMensal> consolidados = new List<ConsolidadoMensal>();
            ViewBag.consolidados = consolidados;

            // numero de dias
            int NumDias = (int)DateTime.DaysInMonth((int)data_hora.data.ano, (int)data_hora.data.mes);
            ViewBag.NumDias = NumDias;

            // lista de erros
            var listaErros = new List<string>();

            // erros
            ViewBag.listaErros = listaErros;

            return View();
        }

        // GET: Relatorio 
        private ActionResult Relat_Consolidado_Mensal_Show(int IDCliente, int tipo_arquivo = 0)
        {
            // tipo do relatorio
            int TipoRelat = 51;

            // salva cookie 
            CookieStore.SalvaCookie_Int("Relat_Tipo", TipoRelat);

            // le cookies
            LeCookies_SmartEnergy();

            // ano atual
            DateTime datahora_ultima = DateTime.Now;
            DATAHORA data_hora = new DATAHORA();

            // le cookie datahora (atual ou inicial)
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            // verifica se existe datahora no cookie
            if (datahora_cookie.Year != 2000)
            {
                // copia data do cookie para ultima
                datahora_ultima = datahora_cookie;
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_ultima);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // tipo do relatorio
            ViewBag.TipoRelat = TipoRelat;

            // tipo consolidado
            string TipoConsolidadoStr = CookieStore.LeCookie_String("Relat_TipoConsolidado");
            int TipoConsolidado = 0;

            // verifica se foi mudado o tab
            if(TipoConsolidadoStr.Length == 8)
            {
                string aux_str = TipoConsolidadoStr.Substring(7, 1);
                TipoConsolidado = int.Parse(aux_str) - 1;
            }

            ViewBag.TipoConsolidado = TipoConsolidado;

            // View do relatorio
            string viewRelatorio = "_Consolidado_DemCons_Mensal";


            // le cookie turno
            DateTime turnoIni_cookie = CookieStore.LeCookie_Datahora("Consolidado_TurnoIni");
            DATAHORA turno_ini = new DATAHORA();

            DateTime turnoFim_cookie = CookieStore.LeCookie_Datahora("Consolidado_TurnoFim");
            DATAHORA turno_fim = new DATAHORA();

            // verifica se tem turno
            if (turnoIni_cookie.Hour == 0 && turnoIni_cookie.Minute == 0 && turnoFim_cookie.Hour == 0 && turnoFim_cookie.Minute == 0)
            {
                turnoIni_cookie = new DateTime(2000, 1, 1, 0, 0, 0);
                turnoFim_cookie = new DateTime(2000, 1, 1, 6, 0, 0);

                // salva cookie
                CookieStore.SalvaCookie_Datahora("Consolidado_TurnoIni", turnoIni_cookie);
                CookieStore.SalvaCookie_Datahora("Consolidado_TurnoFim", turnoFim_cookie);
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref turno_ini, turnoIni_cookie);
            Funcoes_Converte.ConverteDateTime2DataHora(ref turno_fim, turnoFim_cookie);


            // calcula
            Calc_Consolidado_Mensal(IDCliente, data_hora, turno_ini, turno_fim);

            // verifica se PDF
            if (tipo_arquivo == 1)
            {
                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.pdf", viewRelatorio, IDCliente, data_atual);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Landscape,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna nome do arquivo
                var returnedData = new
                {
                    nomeArquivo = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // verifica se XLS
            if (tipo_arquivo == 2)
            {
                // Planilha Excel
                var workbook = new HSSFWorkbook();

                // monta XLS
                workbook = Consolidado_Mensal_XLS(IDCliente);

                // gera identificador unico aonde o arquivo sera guardado
                string handle = Guid.NewGuid().ToString();

                using (MemoryStream memoryStream = new MemoryStream())
                {
                    workbook.Write(memoryStream);
                    memoryStream.Position = 0;
                    Session[handle] = memoryStream.ToArray();
                }

                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.xls", viewRelatorio, IDCliente, data_atual);

                // retorna nome do arquivo
                var returnedData = new
                {
                    FileGuid = handle,
                    FileName = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // verifica se EMAIL
            if (tipo_arquivo == 3)
            {
                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.pdf", viewRelatorio, IDCliente, data_atual);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Landscape,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna 
                ViewBag.caminhoCompleto = caminhoCompleto;
                var returnedData = new
                {
                    caminhoCompleto = caminhoCompleto
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            return View();
        }

        // Calcula Consolidado Mensal
        private void Calc_Consolidado_Mensal(int IDCliente, DATAHORA datahora, DATAHORA turno_inicio, DATAHORA turno_fim)
        {
            // teste
            LogMessage_Consolidado_Mensal(IDCliente, "--------------------------------------------------------------------------");
            LogMessage_Consolidado_Mensal(IDCliente, string.Format("Inicia Consolidado Mensal - IDCliente [{0:000000}] - {1:d}", IDCliente, datahora));

            // funcao relatorio
            int retorno;

            // le cookies
            LeCookies_SmartEnergy();

            // le medicoes
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicoesMetodos.ListarTodosUnidade(IDCliente, ViewBag._ConfigMed);

            // estruturas
            RESUMO_CONSOLIDADO_MENSAL resumo_consolidado = new RESUMO_CONSOLIDADO_MENSAL();
            INFO_MEDICAO info_medicao = new INFO_MEDICAO();
            List<ConsolidadoMensal> consolidados = new List<ConsolidadoMensal>();

            // lista de erros
            var listaErros = new List<string>();

            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_gateway = 0;

            // percorre medicoes
            if( medicoes != null )
            {
                // teste
                LogMessage_Consolidado_Mensal(IDCliente, string.Format("Número Medições [{0}]", medicoes.Count));
                LogMessage_Consolidado_Mensal(IDCliente, "--------------------------------------------------------------------------");

                int contador = 0;

                foreach (MedicoesDominio medicao in medicoes)
                {
                    // teste
                    LogMessage_Consolidado_Mensal(IDCliente, string.Format("Medição [{0:000000}] - Tipo [{1}] - {2} de {3}", medicao.IDMedicao, medicao.IDTipoMedicao, contador, medicoes.Count));
                    contador++;

                    // verifica se nao eh energia
                    if ( medicao.IDTipoMedicao != 0 && medicao.IDTipoMedicao != 1 )
                    {
                        continue;
                    }

                    // preenche solicitacao
                    config_interface.sweb.id_medicao = medicao.IDMedicao;

                    // tipo medicao
                    info_medicao.IDTipoMedicao = (short)medicao.IDTipoMedicao;

                    // formula - converter o string para byte[]
                    info_medicao.Formula = new byte[206];
                    byte[] formula = Encoding.ASCII.GetBytes(medicao.Formula);

                    for (int i=0; i < 206; i++)
                    {
                        if( i < formula.Length )
                            info_medicao.Formula[i] = formula[i];
                        else
                            info_medicao.Formula[i] = 0;
                    }

                    // calcula valores
                    retorno = SmCalcDB_Energia_ResumoMensal((char)0, ref config_interface, ref info_medicao, ref datahora, ref turno_inicio.hora, ref turno_fim.hora, ref resumo_consolidado);

                    // teste
                    LogMessage_Consolidado_Mensal(IDCliente, string.Format("Medição [{0:000000}] - Retorno [{1}]", medicao.IDMedicao, retorno));

                    if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
                    {
                        if (retorno > 0)
                            listaErros.Add(string.Format("Medição {0} com Retorno {1}", medicao.Nome, retorno));
                    }

                    // coloca na lista
                    ConsolidadoMensal consolidado = new ConsolidadoMensal();

                    // copia estrutura
                    for (int i = 0; i < 31; i++)
                    {
                        ConsolidadoMensalValor valor = new ConsolidadoMensalValor();

                        valor.ConsumoTotal = resumo_consolidado.ConsumoTotal[i];
                        valor.ConsumoP = resumo_consolidado.ConsumoP[i];
                        valor.ConsumoFP = resumo_consolidado.ConsumoFP[i];
                        valor.ConsumoTurno = resumo_consolidado.ConsumoTurno[i];
                        valor.DemandaP = resumo_consolidado.Dem_MaxP[i];
                        valor.DataDemandaP = Funcoes_Converte.ConverteDataHora2DateTime(resumo_consolidado.Dem_MaxP_DataHora[i]);
                        valor.DemandaFP = resumo_consolidado.Dem_MaxFP[i];
                        valor.DataDemandaFP = Funcoes_Converte.ConverteDataHora2DateTime(resumo_consolidado.Dem_MaxFP_DataHora[i]);

                        consolidado.ConsolidadoMensalValor[i] = valor;
                    }

                    consolidado.IDMedicao = medicao.IDMedicao;
                    consolidado.Nome = medicao.Nome;
                    consolidado.NomeUnidade = medicao.NomeUnidade;

                    consolidado.NumDias = resumo_consolidado.NumDias;

                    // demanda do turno
                    consolidado.Dem_Turno_Media = resumo_consolidado.Dem_Turno_Media;
                    consolidado.Dem_Turno_RestoDia = resumo_consolidado.Dem_Turno_RestoDia;
                    consolidado.Dem_Turno_Variacao = resumo_consolidado.Dem_Turno_Variacao;

                    consolidados.Add(consolidado);
                }
            }

            // teste
            LogMessage_Consolidado_Mensal(IDCliente, "--------------------------------------------------------------------------");
            LogMessage_Consolidado_Mensal(IDCliente, "Fim");
            LogMessage_Consolidado_Mensal(IDCliente, "--------------------------------------------------------------------------");

            // valores
            ViewBag.consolidados = consolidados;

            // erros
            ViewBag.listaErros = listaErros;

            // converte data e hora
            DateTime DataAtual = Funcoes_Converte.ConverteDataHora2DateTime(datahora);

            // numero de dias
            int NumDias = (int)DateTime.DaysInMonth((int)datahora.data.ano, (int)datahora.data.mes);
            ViewBag.NumDias = NumDias;

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:MM/yyyy}", DataAtual);


            // le cookie turno
            DateTime turnoIni_cookie = CookieStore.LeCookie_Datahora("Consolidado_TurnoIni");
            DateTime turnoFim_cookie = CookieStore.LeCookie_Datahora("Consolidado_TurnoFim");

            // verifica se tem turno
            if (turnoIni_cookie.Hour == 0 && turnoIni_cookie.Minute == 0 && turnoFim_cookie.Hour == 0 && turnoFim_cookie.Minute == 0)
            {
                turnoIni_cookie = new DateTime(2000, 1, 1, 0, 0, 0);
                turnoFim_cookie = new DateTime(2000, 1, 1, 6, 0, 0);
            }

            // turno
            ViewBag.TurnoIni = string.Format("{0:HH}:{1:mm}", turnoIni_cookie, turnoIni_cookie);
            ViewBag.TurnoFim = string.Format("{0:HH}:{1:mm}", turnoFim_cookie, turnoFim_cookie);

            // tipo do relatorio
            ViewBag.TipoRelat = 51;

            // seta para mensal
            ViewBag.Relat_TipoPeriodo = 2;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.ConsolidadoMensal;
            ViewBag.PeriodoRelat = string.Format("Turno: {0} - {1}", ViewBag.TurnoIni, ViewBag.TurnoFim);

            return;
        }

        // Consolidado Mensal XLS
        private HSSFWorkbook Consolidado_Mensal_XLS(int IDCliente)
        {
            // cria excel
            HSSFWorkbook workbook = new HSSFWorkbook();

            // planilhas
            Consolidado_Mensal_Planilha(workbook, 0);
            Consolidado_Mensal_Planilha(workbook, 1);
            Consolidado_Mensal_Planilha(workbook, 2);
            Consolidado_Mensal_Planilha(workbook, 3);
            Consolidado_Mensal_Planilha(workbook, 4);
            Consolidado_Mensal_Planilha(workbook, 5);

            // retorna planilha
            return workbook;
        }

        // Consolidado Mensal Planilha
        private void Consolidado_Mensal_Planilha(HSSFWorkbook workbook, int TipoConsolidado)
        {
            List<ConsolidadoMensal> consolidados = ViewBag.consolidados;
            int NumDias = ViewBag.NumDias;

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _datahorafullStyle = criaEstiloXLS(workbook, 13);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            // cria planilha
            string str_planilha = "";

            switch (TipoConsolidado)
            {
                case 0:
                    str_planilha = "Consumo Total (kWh)";
                    break;

                case 1:
                    str_planilha = "Consumo Ponta (kWh)";
                    break;

                case 2:
                    str_planilha = "Consumo Fora de Ponta (kWh)";
                    break;

                case 3:
                    str_planilha = "Demanda Ponta (kW)";
                    break;

                case 4:
                    str_planilha = "Demanda Fora de Ponta (kW)";
                    break;

                case 5:
                    str_planilha = "Consumo do Turno (kWh)";
                    break;
            }

            ISheet sheet = workbook.CreateSheet(str_planilha);
            int rowIndex = 0;

            // verifica se demanda
            if (TipoConsolidado >= 3 && TipoConsolidado <= 4)
            {
                // cabecalho
                string[] cabecalho = new string[65];

                cabecalho[0] = "ID";
                cabecalho[1] = "Medições";
                cabecalho[2] = "Unidades";

                // dias
                for (int i = 0; i < 31; i++)
                {
                    string str_valor = "---";

                    if (i < NumDias)
                    {
                        str_valor = string.Format("{0:00}/{1}", i+1, ViewBag.DataAtual);
                    }

                    cabecalho[i*2+3] = str_valor;
                    cabecalho[i*2+4] = " ";
                }
                
                // adiciona cabecalho
                rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);
            }
            else
            {
                // cabecalho
                string[] cabecalho = new string[34];

                cabecalho[0] = "ID";
                cabecalho[1] = "Medições";
                cabecalho[2] = "Unidades";

                // dias
                for (int i = 0; i < 31; i++)
                {
                    string str_valor = "---";

                    if (i < NumDias)
                    {
                        str_valor = string.Format("{0:00}/{1}", i + 1, ViewBag.DataAtual);
                    }

                    cabecalho[i + 3] = str_valor;
                }

                // adiciona cabecalho
                rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);
            }

            // adiciona linhas
            IRow row;

            if (consolidados != null)
            {
                foreach (ConsolidadoMensal consolidado in consolidados)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // IDMedicao
                    numeroCelulaXLS(row, 0, consolidado.IDMedicao, _intCellStyle);

                    // medicao
                    textoCelulaXLS(row, 1, consolidado.Nome);

                    // unidade
                    textoCelulaXLS(row, 2, consolidado.NomeUnidade);

                    // dias
                    for (int i = 0; i < 31; i++)
                    {
                        double valor = 0.0;
                        DateTime datahora = new DateTime(2000, 1, 1, 0, 0, 0);

                        if (i < NumDias)
                        {
                            switch (TipoConsolidado)
                            {
                                case 0:
                                    if (consolidado.ConsolidadoMensalValor[i].DataDemandaP.Year != 2000 || consolidado.ConsolidadoMensalValor[i].DataDemandaFP.Year != 2000)
                                    {
                                        valor = consolidado.ConsolidadoMensalValor[i].ConsumoTotal;
                                    }
                                    break;

                                case 1:
                                    if (consolidado.ConsolidadoMensalValor[i].DataDemandaP.Year != 2000)
                                    {
                                        valor = consolidado.ConsolidadoMensalValor[i].ConsumoP;
                                    }
                                    break;

                                case 2:
                                    if (consolidado.ConsolidadoMensalValor[i].DataDemandaFP.Year != 2000)
                                    {
                                        valor = consolidado.ConsolidadoMensalValor[i].ConsumoFP;
                                    }
                                    break;

                                case 3:
                                    if (consolidado.ConsolidadoMensalValor[i].DataDemandaP.Year != 2000)
                                    {
                                        valor = consolidado.ConsolidadoMensalValor[i].DemandaP;
                                        datahora = consolidado.ConsolidadoMensalValor[i].DataDemandaP;
                                    }
                                    break;

                                case 4:
                                    if (consolidado.ConsolidadoMensalValor[i].DataDemandaFP.Year != 2000)
                                    {
                                        valor = consolidado.ConsolidadoMensalValor[i].DemandaFP;
                                        datahora = consolidado.ConsolidadoMensalValor[i].DataDemandaFP;
                                    }
                                    break;

                                case 5:
                                    if (consolidado.ConsolidadoMensalValor[i].DataDemandaP.Year != 2000 || consolidado.ConsolidadoMensalValor[i].DataDemandaFP.Year != 2000)
                                    {
                                        valor = consolidado.ConsolidadoMensalValor[i].ConsumoTurno;
                                    }
                                    break;
                            }
                        }

                        // verifica se demanda
                        if (TipoConsolidado >= 3 && TipoConsolidado <= 4)
                        {
                            // valor
                            numeroCelulaXLS(row, i * 2 + 3, valor, _1CellStyle);

                            // data e hora
                            datahoraCelulaXLS(row, i * 2 + 4, datahora, _datahoraStyle);
                        }
                        else
                        {
                            // valor
                            numeroCelulaXLS(row, i + 3, valor, _intCellStyle);
                        }
                    }

                    // proxima
                    rowIndex++;
                }
            }

            // largura de cada coluna
            sheet.SetColumnWidth(0, 6000);
            for (int i = 1; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 4000);
            
            return;
        }

        private void LogMessage_Consolidado_Mensal(int IDCliente, string msg)
        {
            // arquivo
            string arquivo = string.Format("_{0:000000}", IDCliente);

            // log
            Funcoes_Log.Mensagem("Consolidado_Mensal", msg, arquivo);
        }
    }
}