﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {
        //
        // CONSOLIDADO ANUAL ANALOGICAS
        //

        // GET: Relatorio Consolidado Anual - Analogicas 
        public ActionResult Relat_Consolidado_EA_Anual(int IDCliente)
        {
            // tipo do relatorio
            int TipoRelat = TIPO_RELAT.ConsolidadoEA_Anual;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Relat_Consolidado_Anual");
            CookieStore.SalvaCookie_Int("Relat_Tipo", TipoRelat);

            // le cookies
            LeCookies_SmartEnergy();

            // ano atual
            DateTime datahora_ultima = DateTime.Now;
            DATAHORA data_hora = new DATAHORA();

            // le cookie datahora (atual ou inicial)
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            // verifica se existe datahora no cookie
            if (datahora_cookie.Year != 2000)
            {
                // copia data do cookie para ultima
                datahora_ultima = datahora_cookie;
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_ultima);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // data atual
            ViewBag.DataAtual = string.Format("{0:yyyy}", datahora_ultima);

            // tipo do relatorio
            ViewBag.TipoRelat = TipoRelat;

            // seta para anual
            ViewBag.Relat_TipoPeriodo = TIPO_RELAT_PERIODO.Anual;

            // valores
            List<ConsolidadoAnual> consolidados = new List<ConsolidadoAnual>();
            ViewBag.consolidados = consolidados;

            // lista de erros
            var listaErros = new List<string>();

            // erros
            ViewBag.listaErros = listaErros;

            return View();
        }

        // GET: Relatorio Grafico
        private ActionResult Relat_Consolidado_EA_Anual_Show(int IDCliente, int tipo_arquivo = 0)
        {
            // tipo do relatorio
            int TipoRelat = TIPO_RELAT.ConsolidadoEA_Anual;

            // salva cookie 
            CookieStore.SalvaCookie_Int("Relat_Tipo", TipoRelat);

            // le cookies
            LeCookies_SmartEnergy();

            // ano atual
            DateTime datahora_ultima = DateTime.Now;
            DATAHORA data_hora = new DATAHORA();

            // le cookie datahora (atual ou inicial)
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            // verifica se existe datahora no cookie
            if (datahora_cookie.Year != 2000)
            {
                // copia data do cookie para ultima
                datahora_ultima = datahora_cookie;
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_ultima);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // tipo do relatorio
            ViewBag.TipoRelat = TipoRelat;

            // View do relatorio
            string viewRelatorio = "_Consolidado_EA_Anual";

            // calcula
            Calc_Consolidado_EA_Anual(IDCliente, data_hora);


            // verifica se PDF
            if (tipo_arquivo == 1)
            {
                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.pdf", viewRelatorio, IDCliente, data_atual);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Landscape,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna nome do arquivo
                var returnedData = new
                {
                    nomeArquivo = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // verifica se XLS
            if (tipo_arquivo == 2)
            {
                // Planilha Excel
                var workbook = new HSSFWorkbook();

                // monta XLS
                workbook = Consolidado_EA_Anual_XLS(IDCliente);

                // gera identificador unico aonde o arquivo sera guardado
                string handle = Guid.NewGuid().ToString();

                using (MemoryStream memoryStream = new MemoryStream())
                {
                    workbook.Write(memoryStream);
                    memoryStream.Position = 0;
                    Session[handle] = memoryStream.ToArray();
                }

                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.xls", viewRelatorio, IDCliente, data_atual);

                // retorna nome do arquivo
                var returnedData = new
                {
                    FileGuid = handle,
                    FileName = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // verifica se EMAIL
            if (tipo_arquivo == 3)
            {
                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.pdf", viewRelatorio, IDCliente, data_atual);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Portrait,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna 
                ViewBag.caminhoCompleto = caminhoCompleto;
                var returnedData = new
                {
                    caminhoCompleto = caminhoCompleto
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            return View();
        }

        // Calcula Consolidado Anual - Analogicas
        private void Calc_Consolidado_EA_Anual(int IDCliente, DATAHORA datahora)
        {

            // le cookies
            LeCookies_SmartEnergy();

            // le medicoes
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicoesMetodos.ListarTodosUnidade(IDCliente, ViewBag._ConfigMed);

            List<ConsolidadoAnual> consolidados = new List<ConsolidadoAnual>();

            // lista de erros
            var listaErros = new List<string>();

            // percorre medicoes
            if (medicoes != null)
            {
                foreach (MedicoesDominio medicao in medicoes)
                {
                    // verifica se nao eh analogica
                    if (medicao.IDTipoMedicao != TIPO_MEDICAO.ENTRADA_ANALOGICA && medicao.IDTipoMedicao != TIPO_MEDICAO.EA_FORMULA)
                    {
                        continue;
                    }

                    // calcula valores
                    GG_Metodos GGMetodos = new GG_Metodos();
                    DateTime data = Funcoes_Converte.ConverteDataHora2DateTime(datahora);
                    GG_ConsolidadoAnual relatorio = GGMetodos.ListarConsolidadoAnual(medicao.IDCliente, medicao.IDMedicao, data);

                    // coloca na lista
                    ConsolidadoAnual consolidado = new ConsolidadoAnual();

                    // zera estrutura
                    for (int i = 0; i < 12; i++)
                    {
                        ConsolidadoAnualValor temp = new ConsolidadoAnualValor();

                        temp.VMax = 0.0;
                        temp.VMed = 0.0;
                        temp.VMin = 0.0;
                        temp.TemRegistro = false;

                        consolidado.ConsolidadoAnualValor[i] = temp;
                    }

                    // copia
                    if (relatorio != null)
                    {
                        // copia estrutura
                        for (int i = 0; i < 12; i++)
                        {
                            // maximo - minimo - medio
                            consolidado.ConsolidadoAnualValor[i].VMax = relatorio.VMax[i];
                            consolidado.ConsolidadoAnualValor[i].VMed = relatorio.VMed[i];
                            consolidado.ConsolidadoAnualValor[i].VMin = relatorio.VMin[i];
                            consolidado.ConsolidadoAnualValor[i].TemRegistro = relatorio.TemRegistro[i];
                        }
                    }

                    // numero de dias
                    consolidado.IDMedicao = medicao.IDMedicao;
                    consolidado.Nome = medicao.Nome;
                    consolidado.NomeUnidade = medicao.NomeUnidade;

                    consolidados.Add(consolidado);

                    // unidade de grandeza
                    ViewBag.UnidadeGrandeza = medicao.UnidadeGrandeza;
                }
            }

            // valores
            ViewBag.consolidados = consolidados;

            // erros
            ViewBag.listaErros = listaErros;

            // converte data e hora
            DateTime DataAtual = Funcoes_Converte.ConverteDataHora2DateTime(datahora);

            // data atual
            ViewBag.DataAtual = string.Format("{0:yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:yyyy}", DataAtual);

            // tipo do relatorio
            ViewBag.TipoRelat = TIPO_RELAT.ConsolidadoEA_Anual;

            // seta para anual
            ViewBag.Relat_TipoPeriodo = TIPO_RELAT_PERIODO.Anual;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.ConsolidadoAnual;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Anual;

            return;
        }

        // Consolidado Anual XLS
        private HSSFWorkbook Consolidado_EA_Anual_XLS(int IDCliente)
        {
            // cria excel
            HSSFWorkbook workbook = new HSSFWorkbook();

            // planilhas
            Consolidado_EA_Anual_Planilha(workbook);

            // retorna planilha
            return workbook;
        }

        // Consolidado Anual Planilha
        private void Consolidado_EA_Anual_Planilha(HSSFWorkbook workbook)
        {
            List<ConsolidadoAnual> consolidados = ViewBag.consolidados;

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _datahorafullStyle = criaEstiloXLS(workbook, 13);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            // cria planilha
            string str_planilha = "Consolidado Anual Analógicas";

            ISheet sheet = workbook.CreateSheet(str_planilha);
            int rowIndex = 0;

            // cabecalho
            string[] cabecalho = { "ID", "Medições", "Unidades", "Descrição", "Jan " + ViewBag.DataAtual, "Fev " + ViewBag.DataAtual, "Mar " + ViewBag.DataAtual, "Abr " + ViewBag.DataAtual, "Mai " + ViewBag.DataAtual, "Jun " + ViewBag.DataAtual, "Jul " + ViewBag.DataAtual, "Ago " + ViewBag.DataAtual, "Set " + ViewBag.DataAtual, "Out " + ViewBag.DataAtual, "Nov " + ViewBag.DataAtual, "Dez " + ViewBag.DataAtual };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            IRow row;

            if (consolidados != null)
            {
                foreach (ConsolidadoAnual consolidado in consolidados)
                {
                    // lista medicoes
                    MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                    MedicoesDominio medicao = medicoesMetodos.ListarPorId(consolidado.IDMedicao);

                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // IDMedicao
                    numeroCelulaXLS(row, 0, consolidado.IDMedicao, _intCellStyle);

                    // medicao
                    textoCelulaXLS(row, 1, consolidado.Nome);

                    // unidade
                    textoCelulaXLS(row, 2, consolidado.NomeUnidade);

                    // medio
                    textoCelulaXLS(row, 3, string.Format("Médio ({0})", medicao.UnidadeGrandeza));

                    // meses
                    for (int i = 0; i < 12; i++)
                    {
                        double valor_med = 0.0;

                        valor_med = consolidado.ConsolidadoAnualValor[i].VMed;

                        // valor
                        numeroCelulaXLS(row, i + 4, valor_med, _intCellStyle);
                    }


                    // adiciona linha
                    row = sheet.CreateRow(rowIndex + 1);

                    // maximo
                    textoCelulaXLS(row, 3, string.Format("Máximo ({0})", medicao.UnidadeGrandeza));

                    // meses
                    for (int i = 0; i < 12; i++)
                    {
                        double valor_max = 0.0;

                        valor_max = consolidado.ConsolidadoAnualValor[i].VMax;

                        // valor
                        numeroCelulaXLS(row, i + 4, valor_max, _intCellStyle);
                    }

                    // adiciona linha
                    row = sheet.CreateRow(rowIndex + 2);

                    // minimo
                    textoCelulaXLS(row, 3, string.Format("Mínimo ({0})", medicao.UnidadeGrandeza));

                    // meses
                    for (int i = 0; i < 12; i++)
                    {
                        double valor_min = 0.0;

                        valor_min = consolidado.ConsolidadoAnualValor[i].VMin;

                        // valor
                        numeroCelulaXLS(row, i + 4, valor_min, _intCellStyle);
                    }

                    // proxima
                    rowIndex += 3;

                }

                // mescla tres celulas para o nome da medicao e tres celulas para o nome da unidade
                for (int i = 0; i < consolidados.Count; i++)
                {
                    sheet.AddMergedRegion(new CellRangeAddress(i * 3 + 1, i * 3 + 3, 0, 0));
                    sheet.AddMergedRegion(new CellRangeAddress(i * 3 + 1, i * 3 + 3, 1, 1));
                    sheet.AddMergedRegion(new CellRangeAddress(i * 3 + 1, i * 3 + 3, 2, 2));
                }

                // largura de cada coluna
                sheet.SetColumnWidth(0, 6000);
                for (int i = 1; i < sheet.GetRow(0).LastCellNum; i++)
                    sheet.SetColumnWidth(i, 4000);

                return;
            }
        }
    }
}