﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {        
        //
        // CONSOLIDADO DIARIO UTILIDADES
        //

        // GET: Relatorio Consolidado Diario - Utilidades 
        public ActionResult Relat_Consolidado_Utilidades_Diario(int IDCliente)
        {
            // tipo do relatorio
            int TipoRelat = TIPO_RELAT.ConsolidadoUtilidades_Diario;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Relat_Consolidado_Diario");
            CookieStore.SalvaCookie_Int("Relat_Tipo", TipoRelat);

            // le cookies
            LeCookies_SmartEnergy();

            // ano atual
            DateTime datahora_ultima = DateTime.Now;
            DATAHORA data_hora = new DATAHORA();

            // le cookie datahora (atual ou inicial)
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            // verifica se existe datahora no cookie
            if (datahora_cookie.Year != 2000)
            {
                // copia data do cookie para ultima
                datahora_ultima = datahora_cookie;
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_ultima);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);
            
            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", datahora_ultima);

            // tipo do relatorio
            ViewBag.TipoRelat = TipoRelat;
            
            // seta para diario
            ViewBag.Relat_TipoPeriodo = TIPO_RELAT_PERIODO.Diario;
            
            // valores
            List<ConsolidadoDiario> consolidados = new List<ConsolidadoDiario>();
            ViewBag.consolidados = consolidados;

            // lista de erros
            var listaErros = new List<string>();

            // erros
            ViewBag.listaErros = listaErros;

            return View();
        }

        // GET: Relatorio Grafico
        private ActionResult Relat_Consolidado_Utilidades_Diario_Show(int IDCliente, int tipo_arquivo = 0)
        {
            // tipo do relatorio
            int TipoRelat = TIPO_RELAT.ConsolidadoUtilidades_Diario;

            // salva cookie 
            CookieStore.SalvaCookie_Int("Relat_Tipo", TipoRelat);

            // le cookies
            LeCookies_SmartEnergy();

            // ano atual
            DateTime datahora_ultima = DateTime.Now;
            DATAHORA data_hora = new DATAHORA();

            // le cookie datahora (atual ou inicial)
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            // verifica se existe datahora no cookie
            if (datahora_cookie.Year != 2000)
            {
                // copia data do cookie para ultima
                datahora_ultima = datahora_cookie;
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_ultima);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // tipo do relatorio
            ViewBag.TipoRelat = TipoRelat;

            // View do relatorio
            string viewRelatorio = "_Consolidado_Utilidades_Diario";

            // calcula
            Calc_Consolidado_Utilidades_Diario(IDCliente, data_hora);


            // verifica se PDF
            if (tipo_arquivo == 1)
            {
                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.pdf", viewRelatorio, IDCliente, data_atual);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Landscape,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna nome do arquivo
                var returnedData = new
                {
                    nomeArquivo = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // verifica se XLS
            if (tipo_arquivo == 2)
            {
                // Planilha Excel
                var workbook = new HSSFWorkbook();

                // monta XLS
                workbook = Consolidado_Utilidades_Diario_XLS(IDCliente);

                // gera identificador unico aonde o arquivo sera guardado
                string handle = Guid.NewGuid().ToString();

                using (MemoryStream memoryStream = new MemoryStream())
                {
                    workbook.Write(memoryStream);
                    memoryStream.Position = 0;
                    Session[handle] = memoryStream.ToArray();
                }

                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.xls", viewRelatorio, IDCliente, data_atual);

                // retorna nome do arquivo
                var returnedData = new
                {
                    FileGuid = handle,
                    FileName = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // verifica se EMAIL
            if (tipo_arquivo == 3)
            {
                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.pdf", viewRelatorio, IDCliente, data_atual);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Portrait,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna 
                ViewBag.caminhoCompleto = caminhoCompleto;
                var returnedData = new
                {
                    caminhoCompleto = caminhoCompleto
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            return View();
        }

        // Calcula Consolidado Diario - Utilidades
        private void Calc_Consolidado_Utilidades_Diario(int IDCliente, DATAHORA datahora)
        {

            // le cookies
            LeCookies_SmartEnergy();

            // le medicoes
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicoesMetodos.ListarTodosUnidade(IDCliente, ViewBag._ConfigMed);

            List<ConsolidadoDiario> consolidados = new List<ConsolidadoDiario>();

            // lista de erros
            var listaErros = new List<string>();

            // percorre medicoes
            if (medicoes != null)
            {
                foreach (MedicoesDominio medicao in medicoes)
                {
                    // verifica se nao eh utilidade
                    if (medicao.IDTipoMedicao != TIPO_MEDICAO.UTILIDADES && medicao.IDTipoMedicao != TIPO_MEDICAO.UTILIDADES_FORMULA)
                    {
                        continue;
                    }

                    // calcula valores
                    GG_Metodos GGMetodos = new GG_Metodos();
                    DateTime data = Funcoes_Converte.ConverteDataHora2DateTime(datahora);
                    GG_ConsolidadoDiario relatorio = GGMetodos.ListarConsolidadoDiario(medicao.IDCliente, medicao.IDMedicao, data);

                    // coloca na lista
                    ConsolidadoDiario consolidado = new ConsolidadoDiario();

                    // zera estrutura
                    for (int i = 0; i < 24; i++)
                    {
                        ConsolidadoDiarioValor temp = new ConsolidadoDiarioValor();
                        
                        temp.VMax = 0.0;
                        temp.VMed = 0.0;
                        temp.VMin = 0.0;
                        temp.TemRegistro = false;
                        
                        consolidado.ConsolidadoDiarioValor[i] = temp;
                    }


                    // copia
                    if (relatorio != null)
                    {
                        // copia estrutura
                        for (int i = 0; i < 24; i++)
                        {
                            // maximo - minimo - medio
                            consolidado.ConsolidadoDiarioValor[i].VMax = relatorio.VMax[i];
                            consolidado.ConsolidadoDiarioValor[i].VMed = relatorio.VMed[i];
                            consolidado.ConsolidadoDiarioValor[i].VMin = relatorio.VMin[i];
                            consolidado.ConsolidadoDiarioValor[i].TemRegistro = relatorio.TemRegistro[i];
                        }
                    }

                    consolidado.IDMedicao = medicao.IDMedicao;
                    consolidado.Nome = medicao.Nome;
                    consolidado.NomeUnidade = medicao.NomeUnidade;

                    consolidados.Add(consolidado);

                    // unidade de grandeza
                    ViewBag.UnidadeGrandeza = medicao.UnidadeGrandeza;
                }
            }

            // valores
            ViewBag.consolidados = consolidados;

            // erros
            ViewBag.listaErros = listaErros;

            // converte data e hora
            DateTime DataAtual = Funcoes_Converte.ConverteDataHora2DateTime(datahora);

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:d}", DataAtual);

            // tipo do relatorio
            ViewBag.TipoRelat = TIPO_RELAT.ConsolidadoUtilidades_Diario;

            // seta para diario
            ViewBag.Relat_TipoPeriodo = TIPO_RELAT_PERIODO.Diario;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.ConsolidadoDiario;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Diario;

            return;
        }

        // Consolidado Diario XLS
        private HSSFWorkbook Consolidado_Utilidades_Diario_XLS(int IDCliente)
        {
            // cria excel
            HSSFWorkbook workbook = new HSSFWorkbook();

            // planilhas
            Consolidado_Utilidades_Diario_Planilha(workbook);

            // retorna planilha
            return workbook;
        }

        // Consolidado Diario Planilha
        private void Consolidado_Utilidades_Diario_Planilha(HSSFWorkbook workbook)
        {
            List<ConsolidadoDiario> consolidados = ViewBag.consolidados;

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _datahorafullStyle = criaEstiloXLS(workbook, 13);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            // cria planilha
            string str_planilha = "Consolidado Diário Utilidades";

            ISheet sheet = workbook.CreateSheet(str_planilha);
            int rowIndex = 0;

            // cabecalho
            string[] cabecalho = { "ID", "Medições", "Unidades", "Unidade de Medida", "01:00", "02:00", "03:00", "04:00", "05:00" , "06:00" , "07:00" , "08:00" , "09:00" , "10:00" , "11:00" , "12:00",
                                                                                      "13:00", "14:00", "15:00", "16:00", "17:00" , "18:00" , "19:00" , "20:00" , "21:00" , "22:00" , "23:00" , "00:00" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);
            
            // adiciona linhas
            int i;
            IRow row;

            if (consolidados != null)
            {
                foreach (ConsolidadoDiario consolidado in consolidados)
                {
                    // lista medicoes
                    MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                    MedicoesDominio medicao = medicoesMetodos.ListarPorId(consolidado.IDMedicao);

                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // IDMedicao
                    numeroCelulaXLS(row, 0, consolidado.IDMedicao, _intCellStyle);

                    // medicao
                    textoCelulaXLS(row, 1, string.Format("{0} ({1})", consolidado.Nome, medicao.UnidadeGrandeza));

                    // unidade
                    textoCelulaXLS(row, 2, consolidado.NomeUnidade);

                    // grandeza
                    textoCelulaXLS(row, 3, string.Format("{0}", medicao.UnidadeGrandeza));

                    // registros
                    for (i = 0; i < 24; i++)
                    {
                        // valor
                        numeroCelulaXLS(row, i + 4, consolidado.ConsolidadoDiarioValor[i].VMed, _intCellStyle);
                    }                 

                    // proxima medicao
                    rowIndex ++;                                        
                }
                
            }

            // largura de cada coluna
            sheet.SetColumnWidth(0, 6000);
            for (i = 1; i < sheet.GetRow(0).LastCellNum; i++)
            {
                sheet.SetColumnWidth(i, 4000);
            }

            sheet.SetColumnWidth(2, 6000);

            return;
        }
    }
}
