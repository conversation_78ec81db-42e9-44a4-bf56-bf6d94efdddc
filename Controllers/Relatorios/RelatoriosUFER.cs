﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {
        //
        // UFER Energia Reativa Excedente
        //

        // GET: Relatorio UFER
        public ActionResult Relat_UFER(int IDCliente, int IDMedicao)
        {
            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Relat_UFER");

            // relatorio UFER
            return (Relat_Grafico_Show(IDCliente, IDMedicao, TIPO_RELAT.UFER));
        }

        // UFER Diario
        private void UFER_Diario(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_DIARIO relatorio = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise = new RELAT_DIARIO_ANALISE();

            RELAT_DIARIO relatorio_sim = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise_sim = new RELAT_DIARIO_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // calcula valores
            retorno = SmCalcDB_Energia_RelatDiario((char)0, ref config_interface, (char)10, (char)IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_DIARIO(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var UFER = new double[26];
            var FatPot = new double[26];
            var Periodo = new int[26];
            var Dias = new string[26];
            var DatasN = new DateTime[26];
            var Horas = new string[26];

            // grafico simulacao
            var UFER_Sim = new double[26];
            var FatPot_Sim = new double[26];
            var Periodo_Sim = new int[26];
            var Dias_Sim = new string[26];
            var DatasN_Sim = new DateTime[26];

            double UFER_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Horas[i] = strData.ToString("HH:mm");

                // formata label simulacao
                Dias_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Sim[i] = strData;

                // proxima hora
                strData = strData.AddHours(1);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    UFER[i] = relatorio.registro[0].valor1;
                    FatPot[i] = relatorio.registro[0].valor2;
                    Periodo[i] = relatorio.registro[0].periodo;

                    // zera simulacao 
                    UFER_Sim[i] = relatorio_sim.registro[0].valor1;
                    FatPot_Sim[i] = relatorio_sim.registro[0].valor2;
                    Periodo_Sim[i] = relatorio_sim.registro[0].periodo;
                }

                // verifica se ultima barra
                if (i == 25)
                {
                    // zera
                    UFER[i] = relatorio.registro[23].valor1;
                    FatPot[i] = relatorio.registro[23].valor2;
                    Periodo[i] = relatorio.registro[23].periodo;

                    // zera simulacao
                    UFER_Sim[i] = relatorio_sim.registro[23].valor1;
                    FatPot_Sim[i] = relatorio_sim.registro[23].valor2;
                    Periodo_Sim[i] = relatorio_sim.registro[23].periodo;
                }

                if (i >= 1 && i <= 24)
                {
                    // copia
                    j = i - 1;

                    UFER[i] = relatorio.registro[j].valor1;
                    FatPot[i] = relatorio.registro[j].valor2;
                    Periodo[i] = relatorio.registro[j].periodo;

                    // copia simulacao 
                    UFER_Sim[i] = relatorio_sim.registro[j].valor1;
                    FatPot_Sim[i] = relatorio_sim.registro[j].valor2;
                    Periodo_Sim[i] = relatorio_sim.registro[j].periodo;

                    // verifica se sem registro
                    if (relatorio.registro[j].periodo == 3)
                    {
                        UFER[i] = 0.0;
                    }

                    // verifica se sem registro simulacao
                    if (relatorio_sim.registro[j].periodo == 3)
                    {
                        UFER_Sim[i] = 0.0;
                    }

                    // verifica UFER maximo
                    if (UFER[i] > UFER_max_grafico)
                        UFER_max_grafico = UFER[i];

                    // verifica UFER maximo simulacao
                    if (UFER_Sim[i] > UFER_max_grafico)
                        UFER_max_grafico = UFER_Sim[i];
                }
            }

            UFER_max_grafico = UFER_max_grafico * 1.1;

            if (UFER_max_grafico == 0.0)
            {
                UFER_max_grafico = 10.0;
            }

            ViewBag.UFERMaxGrafico = UFER_max_grafico;

            ViewBag.UFER = UFER;
            ViewBag.FatPot = FatPot;
            ViewBag.Periodo = Periodo;
            ViewBag.Dias = Dias;
            ViewBag.DatasN = DatasN;
            ViewBag.Horas = Horas;

            // simulacao
            ViewBag.UFER_Sim = UFER_Sim;
            ViewBag.FatPot_Sim = FatPot_Sim;
            ViewBag.Periodo_Sim = Periodo_Sim;
            ViewBag.Dias_Sim = Dias_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.UFER;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Diario;

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:d}", DataAtual);

            // UFER
            double UFER_Total_P = analise.analise_valor1.medio[0];
            double UFER_Total_FPI = analise.analise_valor1.medio[1];
            double UFER_Total_FPC = analise.analise_valor1.medio[2];
            double UFER_Total = analise.analise_valor1.medio[0] + analise.analise_valor1.medio[1] + analise.analise_valor1.medio[2];

            // UFER Simulacao
            double UFER_Total_P_Sim = analise_sim.analise_valor1.medio[0];
            double UFER_Total_FPI_Sim = analise_sim.analise_valor1.medio[1];
            double UFER_Total_FPC_Sim = analise_sim.analise_valor1.medio[2];
            double UFER_Total_Sim = analise_sim.analise_valor1.medio[0] + analise_sim.analise_valor1.medio[1] + analise_sim.analise_valor1.medio[2];

            // total ponta 
            ViewBag.UFER_Total_P = string.Format("{0:#,##0.0}", UFER_Total_P);
            ViewBag.UFER_Total_PN = UFER_Total_P;

            // total ponta simulacao
            ViewBag.UFER_Total_P_Sim = string.Format("{0:#,##0.0}", UFER_Total_P_Sim);
            ViewBag.UFER_Total_PN_Sim = UFER_Total_P_Sim;

            // total fora ponta indutivo 
            ViewBag.UFER_Total_FPI = string.Format("{0:#,##0.0}", UFER_Total_FPI);
            ViewBag.UFER_Total_FPIN = UFER_Total_FPI;

            // total fora ponta indutivo simulacao
            ViewBag.UFER_Total_FPI_Sim = string.Format("{0:#,##0.0}", UFER_Total_FPI_Sim);
            ViewBag.UFER_Total_FPIN_Sim = UFER_Total_FPI_Sim;

            // total fora ponta capacitivo
            ViewBag.UFER_Total_FPC = string.Format("{0:#,##0.0}", UFER_Total_FPC);
            ViewBag.UFER_Total_FPCN = UFER_Total_FPC;

            // total fora ponta capacitivo simulacao
            ViewBag.UFER_Total_FPC_Sim = string.Format("{0:#,##0.0}", UFER_Total_FPC_Sim);
            ViewBag.UFER_Total_FPCN_Sim = UFER_Total_FPC_Sim;

            // total
            ViewBag.UFER_Total = string.Format("{0:#,##0.0}", UFER_Total);
            ViewBag.UFER_TotalN = UFER_Total;

            // total simulacao
            ViewBag.UFER_Total_Sim = string.Format("{0:#,##0.0}", UFER_Total_Sim);
            ViewBag.UFER_TotalN_Sim = UFER_Total_Sim;

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(1);

            ViewBag.data_hora_ini = data_hora_ini;

            EventosPeriodo(IDCliente, IDMedicao, data_hora_ini, data_hora_fim);

            return;
        }

        // UFER Diario XLS
        private HSSFWorkbook UFER_Diario_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string str_UFER = string.Format("UFER ({0})", ViewBag.UnidadeUFER);
            string[] cabecalho = { "Data e Hora", "Período", str_UFER, "Fator de Potência" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 25; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _datahoraStyle);

                // periodo
                numeroCelulaXLS(row, 1, ViewBag.Periodo[i], _intCellStyle);

                // UFER
                numeroCelulaXLS(row, 2, ViewBag.UFER[i], _1CellStyle);

                // Fator de Potência
                numeroCelulaXLS(row, 3, ViewBag.FatPot[i], _3CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Energia Reativa Excedente [UFER]", "Relatório Diário", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string[] cab_UFER = { "UFER", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_UFER, rowIndex++);

            // UFER
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, str_UFER, _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.UFER_Total_FPCN, _1CellStyle);
            numeroCelulaXLS(row, 2, ViewBag.UFER_Total_FPIN, _1CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.UFER_Total_PN, _1CellStyle);
            numeroCelulaXLS(row, 4, ViewBag.UFER_TotalN, _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }


        // UFER Diario XLS Simulacao
        private HSSFWorkbook UFER_Diario_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // simulacao 
            string[] simulacao = { "Energia Reativa Excedente [UFER] - Simulação" };

            // cabecalho simulacao 
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas para titulo simulacao 
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 3));

            // cabecalho
            string str_UFER = string.Format("UFER ({0})", ViewBag.UnidadeUFER);
            string[] cabecalho = { "Data e Hora", "Período", str_UFER, "Fator de Potência" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 25; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[i], _datahoraStyle);

                // periodo
                numeroCelulaXLS(row, 1, ViewBag.Periodo_Sim[i], _intCellStyle);

                // UFER
                numeroCelulaXLS(row, 2, ViewBag.UFER_Sim[i], _1CellStyle);

                // Fator de Potência
                numeroCelulaXLS(row, 3, ViewBag.FatPot_Sim[i], _3CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Energia Reativa Excedente [UFER] - Simulação", "Relatório Diário", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string[] cab_UFER = { "UFER", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_UFER, rowIndex++);

            // UFER
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, str_UFER, _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.UFER_Total_FPCN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 2, ViewBag.UFER_Total_FPIN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.UFER_Total_PN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 4, ViewBag.UFER_TotalN_Sim, _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // UFER Semanal
        private void UFER_Semanal(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_SEMANAL relatorio = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise = new RELAT_SEMANAL_ANALISE();

            RELAT_SEMANAL relatorio_sim = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise_sim = new RELAT_SEMANAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // calcula valores
            retorno = SmCalcDB_Energia_RelatSemanal((char)0, ref config_interface, (char)10, (char)IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_SEMANAL(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // maior UFER
            double maior_UFER = analise.analise_valor.consumo[0] + analise.analise_valor.consumo[1];

            // grafico
            var UFER_P = new double[7];
            var UFER_FPI = new double[7];
            var UFER_FPC = new double[7];
            var Datas = new string[7];
            var DatasN = new DateTime[7];
            var Dias = new string[7];

            // grafico simulacao
            var UFER_P_Sim = new double[7];
            var UFER_FPI_Sim = new double[7];
            var UFER_FPC_Sim = new double[7];
            var Datas_Sim = new string[7];
            var DatasN_Sim = new DateTime[7];

            DateTime inicio = new DateTime();
            DateTime fim = new DateTime();

            double UFER_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            int dia_semana = 0;

            for (dia_semana = 0; dia_semana < 7; dia_semana++)
            {
                // formata label
                Datas[dia_semana] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[dia_semana] = strData;
                Dias[dia_semana] = string.Format("{0:d}", strData);

                // formata label simulacao
                Datas_Sim[dia_semana] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Sim[dia_semana] = strData;

                // guarda inicio
                if (dia_semana == 0)
                {
                    inicio = strData;
                }

                // guarda fim
                if (dia_semana == 6)
                {
                    fim = strData;
                }

                // proximo dias
                strData = strData.AddDays(1);


                // zera
                UFER_P[dia_semana] = 0.0;
                UFER_FPI[dia_semana] = 0.0;
                UFER_FPC[dia_semana] = 0.0;

                // zera simulacao 
                UFER_P_Sim[dia_semana] = 0.0;
                UFER_FPI_Sim[dia_semana] = 0.0;
                UFER_FPC_Sim[dia_semana] = 0.0;

                // percorre horas
                for (int hora = 0; hora < 24; hora++)
                {
                    // periodo
                    switch (relatorio.registro[hora].periodo[dia_semana])
                    {
                        case (char)0:   // ponta
                            UFER_P[dia_semana] += relatorio.registro[hora].valor[dia_semana];
                            break;

                        case (char)1:   // fora de ponta indutivo  
                            UFER_FPI[dia_semana] += relatorio.registro[hora].valor[dia_semana];
                            break;

                        case (char)2:   // fora de ponta capacitivo
                            UFER_FPC[dia_semana] += relatorio.registro[hora].valor[dia_semana];
                            break;
                    }
                }

                // percorre horas simulacao
                for (int hora = 0; hora < 24; hora++)
                {
                    // periodo
                    switch (relatorio.registro[hora].periodo[dia_semana])
                    {
                        case (char)0:   // ponta
                            UFER_P_Sim[dia_semana] += relatorio_sim.registro[hora].valor[dia_semana];
                            break;

                        case (char)1:   // fora de ponta indutivo  
                            UFER_FPI_Sim[dia_semana] += relatorio_sim.registro[hora].valor[dia_semana];
                            break;

                        case (char)2:   // fora de ponta capacitivo
                            UFER_FPC_Sim[dia_semana] += relatorio_sim.registro[hora].valor[dia_semana];
                            break;
                    }
                }

                // verifica UFER maximo
                if ((UFER_P[dia_semana] + UFER_FPI[dia_semana] + UFER_FPC[dia_semana]) > UFER_max_grafico)
                    UFER_max_grafico = (UFER_P[dia_semana] + UFER_FPI[dia_semana] + UFER_FPC[dia_semana]);

                // verifica UFER maximo simulacao
                if ((UFER_P_Sim[dia_semana] + UFER_FPI_Sim[dia_semana] + UFER_FPC_Sim[dia_semana]) > UFER_max_grafico)
                    UFER_max_grafico = (UFER_P_Sim[dia_semana] + UFER_FPI_Sim[dia_semana] + UFER_FPC_Sim[dia_semana]);
            }

            UFER_max_grafico = UFER_max_grafico * 1.1;

            if (UFER_max_grafico == 0.0)
            {
                UFER_max_grafico = 10.0;
            }

            ViewBag.UFERMaxGrafico = UFER_max_grafico;

            // grafico
            ViewBag.UFER_P = UFER_P;
            ViewBag.UFER_FPI = UFER_FPI;
            ViewBag.UFER_FPC = UFER_FPC;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            // grafico simulacao
            ViewBag.UFER_P_Sim = UFER_P_Sim;
            ViewBag.UFER_FPI_Sim = UFER_FPI_Sim;
            ViewBag.UFER_FPC_Sim = UFER_FPC_Sim;
            ViewBag.Datas_Sim = Datas_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.UFER;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Semanal;

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);
            ViewBag.DataAtualN = inicio;
            ViewBag.DataFinalN = fim;
            ViewBag.DataTextoAtual = string.Format("{0:d} - {1:d} ", inicio, fim);

            // UFER
            double UFER_Total_P = analise.analise_valor.medio[0];
            double UFER_Total_FPI = analise.analise_valor.medio[1];
            double UFER_Total_FPC = analise.analise_valor.medio[2];
            double UFER_Total = (analise.analise_valor.medio[0] + analise.analise_valor.medio[1] + analise.analise_valor.medio[2]);

            // UFER Simulacao
            double UFER_Total_P_Sim = analise_sim.analise_valor.medio[0];
            double UFER_Total_FPI_Sim = analise_sim.analise_valor.medio[1];
            double UFER_Total_FPC_Sim = analise_sim.analise_valor.medio[2];
            double UFER_Total_Sim = (analise_sim.analise_valor.medio[0] + analise_sim.analise_valor.medio[1] + analise_sim.analise_valor.medio[2]);

            // total ponta
            ViewBag.UFER_Total_P = string.Format("{0:#,##0.0}", UFER_Total_P);
            ViewBag.UFER_Total_PN = UFER_Total_P;

            // total ponta simulacao
            ViewBag.UFER_Total_P_Sim = string.Format("{0:#,##0.0}", UFER_Total_P_Sim);
            ViewBag.UFER_Total_PN_Sim = UFER_Total_P_Sim;

            // total fora ponta indutivo
            ViewBag.UFER_Total_FPI = string.Format("{0:#,##0.0}", UFER_Total_FPI);
            ViewBag.UFER_Total_FPIN = UFER_Total_FPI;

            // total fora ponta indutivo simulacao
            ViewBag.UFER_Total_FPI_Sim = string.Format("{0:#,##0.0}", UFER_Total_FPI_Sim);
            ViewBag.UFER_Total_FPIN_Sim = UFER_Total_FPI_Sim;

            // total fora ponta capacitivo
            ViewBag.UFER_Total_FPC = string.Format("{0:#,##0.0}", UFER_Total_FPC);
            ViewBag.UFER_Total_FPCN = UFER_Total_FPC;

            // total fora ponta capacitivo simulacao
            ViewBag.UFER_Total_FPC_Sim = string.Format("{0:#,##0.0}", UFER_Total_FPC_Sim);
            ViewBag.UFER_Total_FPCN_Sim = UFER_Total_FPC_Sim;

            // total
            ViewBag.UFER_Total = string.Format("{0:#,##0.0}", UFER_Total);
            ViewBag.UFER_TotalN = UFER_Total;

            // total simulacao
            ViewBag.UFER_Total_Sim = string.Format("{0:#,##0.0}", UFER_Total_Sim);
            ViewBag.UFER_TotalN_Sim = UFER_Total_Sim;

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(inicio.Year, inicio.Month, inicio.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(7);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // UFER Semanal XLS
        private HSSFWorkbook UFER_Semanal_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string str_fpc = string.Format("Fora de Ponta Capacitivo ({0})", ViewBag.UnidadeUFER);
            string str_fpi = string.Format("Fora de Ponta Indutivo ({0})", ViewBag.UnidadeUFER);
            string str_ponta = string.Format("Ponta ({0})", ViewBag.UnidadeUFER);
            string str_total = string.Format("Total ({0})", ViewBag.UnidadeUFER);
            string[] cabecalho = { "Semana", "Data e Hora", str_fpc, str_fpi, str_ponta, str_total };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 0; i < 7; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // semana
                textoCelulaXLS(row, 0, string.Format("{0:dddd}",ViewBag.DatasN[i]));

                // data e hora
                datahoraCelulaXLS(row, 1, ViewBag.DatasN[i], _dataStyle);

                // UFER fora de ponta capacitivo
                numeroCelulaXLS(row, 2, ViewBag.UFER_FPC[i], _1CellStyle);

                // UFER fora de ponta indutivo
                numeroCelulaXLS(row, 3, ViewBag.UFER_FPI[i], _1CellStyle);

                // UFER ponta
                numeroCelulaXLS(row, 4, ViewBag.UFER_P[i], _1CellStyle);

                // UFER total
                numeroCelulaXLS(row, 5, ViewBag.UFER_P[i] + ViewBag.UFER_FPI[i] + ViewBag.UFER_FPC[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Energia Reativa Excedente [UFER]", "Relatório Semanal", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string str_UFER = string.Format("UFER ({0})", ViewBag.UnidadeUFER);
            string[] cab_UFER = { "UFER", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_UFER, rowIndex++);

            // UFER
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, str_UFER, _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.UFER_Total_FPCN, _1CellStyle);
            numeroCelulaXLS(row, 2, ViewBag.UFER_Total_FPIN, _1CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.UFER_Total_PN, _1CellStyle);
            numeroCelulaXLS(row, 4, ViewBag.UFER_TotalN, _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // UFER Semanal XLS
        private HSSFWorkbook UFER_Semanal_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // simulacao 
            string[] simulacao = { "Energia Reativa Excedente [UFER] - Simulação" };

            // cabecalho simulacao 
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas para titulo simulacao 
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 5));

            // cabecalho
            string str_fpc = string.Format("Fora de Ponta Capacitivo ({0})", ViewBag.UnidadeUFER);
            string str_fpi = string.Format("Fora de Ponta Indutivo ({0})", ViewBag.UnidadeUFER);
            string str_ponta = string.Format("Ponta ({0})", ViewBag.UnidadeUFER);
            string str_total = string.Format("Total ({0})", ViewBag.UnidadeUFER);
            string[] cabecalho = { "Semana", "Data e Hora", str_fpc, str_fpi, str_ponta, str_total };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 0; i < 7; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // semana
                textoCelulaXLS(row, 0, string.Format("{0:dddd}", ViewBag.DatasN[i]));

                // data e hora
                datahoraCelulaXLS(row, 1, ViewBag.DatasN_Sim[i], _dataStyle);

                // UFER fora de ponta capacitivo
                numeroCelulaXLS(row, 2, ViewBag.UFER_FPC_Sim[i], _1CellStyle);

                // UFER fora de ponta indutivo
                numeroCelulaXLS(row, 3, ViewBag.UFER_FPI_Sim[i], _1CellStyle);

                // UFER ponta
                numeroCelulaXLS(row, 4, ViewBag.UFER_P_Sim[i], _1CellStyle);

                // UFER total
                numeroCelulaXLS(row, 5, ViewBag.UFER_P_Sim[i] + ViewBag.UFER_FPI_Sim[i] + ViewBag.UFER_FPC_Sim[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Energia Reativa Excedente [UFER] - Simulação", "Relatório Semanal", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string str_UFER = string.Format("UFER ({0})", ViewBag.UnidadeUFER);
            string[] cab_UFER = { "UFER", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_UFER, rowIndex++);

            // UFER
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, str_UFER, _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.UFER_Total_FPCN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 2, ViewBag.UFER_Total_FPIN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.UFER_Total_PN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 4, ViewBag.UFER_TotalN_Sim, _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // UFER Mensal
        private void UFER_Mensal(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_MENSAL relatorio = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise = new RELAT_MENSAL_ANALISE();

            RELAT_MENSAL relatorio_sim = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise_sim = new RELAT_MENSAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // calcula valores
            retorno = SmCalcDB_Energia_RelatMensal((char)0, ref config_interface, (char)10, (char)IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_MENSAL(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // numero de dias do mes
            int NumDiasMes = relatorio.num_dias;

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[NumDiasMes - 1].datahora.data.ano,
                                   (int)relatorio.registro[NumDiasMes - 1].datahora.data.mes,
                                   1, 1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // maior UFER
            double maior_UFER = analise.analise_valor.consumo[0] + analise.analise_valor.consumo[1];

            // grafico
            var UFER_P = new double[42];
            var UFER_FPI = new double[42];
            var UFER_FPC = new double[42];
            var Datas = new string[42];
            var DatasN = new DateTime[42];
            var Dias = new string[42];

            // grafico simulacao
            var UFER_P_Sim = new double[42];
            var UFER_FPI_Sim = new double[42];
            var UFER_FPC_Sim = new double[42];
            var Datas_Sim = new string[42];
            var DatasN_Sim = new DateTime[42];

            double UFER_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = strData.ToString("dd/MM");

                // formata label simulacao
                Datas_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Sim[i] = strData;

                // proximo dia
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    UFER_P[i] = relatorio.registro[0].valor[0];
                    UFER_FPI[i] = relatorio.registro[0].valor[1];
                    UFER_FPC[i] = relatorio.registro[0].valor[2];

                    // zera simulacao 
                    UFER_P_Sim[i] = relatorio_sim.registro[0].valor[0];
                    UFER_FPI_Sim[i] = relatorio_sim.registro[0].valor[1];
                    UFER_FPC_Sim[i] = relatorio_sim.registro[0].valor[2];
                }

                if (i == (NumDiasMes + 1))
                {
                    // zera
                    UFER_P[i] = relatorio.registro[NumDiasMes - 1].valor[0];
                    UFER_FPI[i] = relatorio.registro[NumDiasMes - 1].valor[1];
                    UFER_FPC[i] = relatorio.registro[NumDiasMes - 1].valor[2];

                    // zera simulacao
                    UFER_P_Sim[i] = relatorio_sim.registro[NumDiasMes - 1].valor[0];
                    UFER_FPI_Sim[i] = relatorio_sim.registro[NumDiasMes - 1].valor[1];
                    UFER_FPC_Sim[i] = relatorio_sim.registro[NumDiasMes - 1].valor[2];
                }

                if (i >= 1 && i <= NumDiasMes)
                {
                    // copia
                    j = i - 1;
                   
                    UFER_P[i] = relatorio.registro[j].valor[0];
                    UFER_FPI[i] = relatorio.registro[j].valor[1];
                    UFER_FPC[i] = relatorio.registro[j].valor[2];

                    // copia simulacao
                    UFER_P_Sim[i] = relatorio_sim.registro[j].valor[0];
                    UFER_FPI_Sim[i] = relatorio_sim.registro[j].valor[1];
                    UFER_FPC_Sim[i] = relatorio_sim.registro[j].valor[2];

                    // verifica UFER maximo
                    if ((UFER_P[i] + UFER_FPI[i] + UFER_FPC[i]) > UFER_max_grafico)
                        UFER_max_grafico = (UFER_P[i] + UFER_FPI[i] + UFER_FPC[i]);

                    // verifica UFER maximo simulacao
                    if ((UFER_P_Sim[i] + UFER_FPI_Sim[i] + UFER_FPC_Sim[i]) > UFER_max_grafico)
                        UFER_max_grafico = (UFER_P_Sim[i] + UFER_FPI_Sim[i] + UFER_FPC_Sim[i]);
                }
            }

            UFER_max_grafico = UFER_max_grafico * 1.1;

            if (UFER_max_grafico == 0.0)
            {
                UFER_max_grafico = 10.0;
            }

            ViewBag.UFERMaxGrafico = UFER_max_grafico;

            // grafico
            ViewBag.UFER_P = UFER_P;
            ViewBag.UFER_FPI = UFER_FPI;
            ViewBag.UFER_FPC = UFER_FPC;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            // grafico simulacao
            ViewBag.UFER_P_Sim = UFER_P_Sim;
            ViewBag.UFER_FPI_Sim = UFER_FPI_Sim;
            ViewBag.UFER_FPC_Sim = UFER_FPC_Sim;
            ViewBag.Datas_Sim = Datas_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;           

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.UFER;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Mensal;

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:y}", DataAtual);

            ViewBag.NumDiasMes = NumDiasMes;

            // UFER
            double UFER_Total_P = analise.analise_valor.medio[0];
            double UFER_Total_FPI = analise.analise_valor.medio[1];
            double UFER_Total_FPC = analise.analise_valor.medio[2];
            double UFER_Total = (analise.analise_valor.medio[0] + analise.analise_valor.medio[1] + analise.analise_valor.medio[2]);

            // UFER simulacao
            double UFER_Total_P_Sim = analise_sim.analise_valor.medio[0];
            double UFER_Total_FPI_Sim = analise_sim.analise_valor.medio[1];
            double UFER_Total_FPC_Sim = analise_sim.analise_valor.medio[2];
            double UFER_Total_Sim = (analise_sim.analise_valor.medio[0] + analise_sim.analise_valor.medio[1] + analise_sim.analise_valor.medio[2]);

            // total ponta 
            ViewBag.UFER_Total_P = string.Format("{0:#,##0.0}", UFER_Total_P);
            ViewBag.UFER_Total_PN = UFER_Total_P;

            // total ponta simulacao
            ViewBag.UFER_Total_P_Sim = string.Format("{0:#,##0.0}", UFER_Total_P_Sim);
            ViewBag.UFER_Total_PN_Sim = UFER_Total_P_Sim;

            // total fora ponta indutivo
            ViewBag.UFER_Total_FPI = string.Format("{0:#,##0.0}", UFER_Total_FPI);
            ViewBag.UFER_Total_FPIN = UFER_Total_FPI;

            // total fora ponta indutivo simulacao
            ViewBag.UFER_Total_FPI_Sim = string.Format("{0:#,##0.0}", UFER_Total_FPI_Sim);
            ViewBag.UFER_Total_FPIN_Sim = UFER_Total_FPI_Sim;

            // total fora ponta capacitivo
            ViewBag.UFER_Total_FPC = string.Format("{0:#,##0.0}", UFER_Total_FPC);
            ViewBag.UFER_Total_FPCN = UFER_Total_FPC;

            // total fora ponta capacitivo simulacao 
            ViewBag.UFER_Total_FPC_Sim = string.Format("{0:#,##0.0}", UFER_Total_FPC_Sim);
            ViewBag.UFER_Total_FPCN_Sim = UFER_Total_FPC_Sim;

            // total 
            ViewBag.UFER_Total = string.Format("{0:#,##0.0}", UFER_Total);
            ViewBag.UFER_TotalN = UFER_Total;

            // total simulacao
            ViewBag.UFER_Total_Sim = string.Format("{0:#,##0.0}", UFER_Total_Sim);
            ViewBag.UFER_TotalN_Sim = UFER_Total_Sim;

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // UFER Mensal XLS
        private HSSFWorkbook UFER_Mensal_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string str_fpc = string.Format("Fora de Ponta Capacitivo ({0})", ViewBag.UnidadeUFER);
            string str_fpi = string.Format("Fora de Ponta Indutivo ({0})", ViewBag.UnidadeUFER);
            string str_ponta = string.Format("Ponta ({0})", ViewBag.UnidadeUFER);
            string str_total = string.Format("Total ({0})", ViewBag.UnidadeUFER);
            string[] cabecalho = { "Data", str_fpc, str_fpi, str_ponta, str_total };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < (ViewBag.NumDiasMes+1); i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // UFER fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.UFER_FPC[i], _1CellStyle);

                // UFER fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.UFER_FPI[i], _1CellStyle);

                // UFER ponta
                numeroCelulaXLS(row, 3, ViewBag.UFER_P[i], _1CellStyle);

                // UFER total
                numeroCelulaXLS(row, 4, ViewBag.UFER_P[i] + ViewBag.UFER_FPI[i] + ViewBag.UFER_FPC[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Energia Reativa Excedente [UFER]", "Relatório Mensal", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string str_UFER = string.Format("UFER ({0})", ViewBag.UnidadeUFER);
            string[] cab_UFER = { "UFER", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_UFER, rowIndex++);

            // UFER
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, str_UFER, _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.UFER_Total_FPCN, _1CellStyle);
            numeroCelulaXLS(row, 2, ViewBag.UFER_Total_FPIN, _1CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.UFER_Total_PN, _1CellStyle);
            numeroCelulaXLS(row, 4, ViewBag.UFER_TotalN, _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // UFER Mensal XLS Simulacao
        private HSSFWorkbook UFER_Mensal_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // simulacao 
            string[] simulacao = { "Energia Reativa Excedente [UFER] - Simulação" };

            // cabecalho simulacao 
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas para titulo simulacao 
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 4));

            // cabecalho
            string str_fpc = string.Format("Fora de Ponta Capacitivo ({0})", ViewBag.UnidadeUFER);
            string str_fpi = string.Format("Fora de Ponta Indutivo ({0})", ViewBag.UnidadeUFER);
            string str_ponta = string.Format("Ponta ({0})", ViewBag.UnidadeUFER);
            string str_total = string.Format("Total ({0})", ViewBag.UnidadeUFER);
            string[] cabecalho = { "Data", str_fpc, str_fpi, str_ponta, str_total };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < (ViewBag.NumDiasMes + 1); i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[i], _dataStyle);

                // UFER fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.UFER_FPC_Sim[i], _1CellStyle);

                // UFER fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.UFER_FPI_Sim[i], _1CellStyle);

                // UFER ponta
                numeroCelulaXLS(row, 3, ViewBag.UFER_P_Sim[i], _1CellStyle);

                // UFER total
                numeroCelulaXLS(row, 4, ViewBag.UFER_P_Sim[i] + ViewBag.UFER_FPI_Sim[i] + ViewBag.UFER_FPC_Sim[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Energia Reativa Excedente [UFER] - Simulação", "Relatório Mensal", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string str_UFER = string.Format("UFER ({0})", ViewBag.UnidadeUFER);
            string[] cab_UFER = { "UFER", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_UFER, rowIndex++);

            // UFER
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, str_UFER, _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.UFER_Total_FPCN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 2, ViewBag.UFER_Total_FPIN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.UFER_Total_PN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 4, ViewBag.UFER_TotalN_Sim, _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // UFER Anual
        private void UFER_Anual(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_ANUAL relatorio = new RELAT_ANUAL();
            RELAT_ANUAL_ANALISE analise = new RELAT_ANUAL_ANALISE();

            RELAT_ANUAL relatorio_sim = new RELAT_ANUAL();
            RELAT_ANUAL_ANALISE analise_sim = new RELAT_ANUAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // calcula valores
            retorno = SmCalcDB_Energia_RelatAnual((char)0, ref config_interface, (char)10, (char)IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_ANUAL(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   1, 1, 1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // maior UFER
            double maior_UFER = analise.analise_valor.consumo[0] + analise.analise_valor.consumo[1];

            // grafico
            var UFER_P = new double[14];
            var UFER_FPI = new double[14];
            var UFER_FPC = new double[14];
            var Datas = new string[14];
            var DatasN = new DateTime[14];
            var Meses = new string[14];

            // grafico simulacao
            var UFER_P_Sim = new double[14];
            var UFER_FPI_Sim = new double[14];
            var UFER_FPC_Sim = new double[14];
            var Datas_Sim = new string[14];
            var DatasN_Sim = new DateTime[14];

            double UFER_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 mes para a primeira barra
            strData = strData.AddMonths(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 14; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Meses[i] = string.Format("{0:MMMM}", strData);

                // formata label simulacao
                Datas_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Sim[i] = strData;

                // proximo mes
                strData = strData.AddMonths(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    UFER_P[i] = relatorio.registro[0].valor[0];
                    UFER_FPI[i] = relatorio.registro[0].valor[1];
                    UFER_FPC[i] = relatorio.registro[0].valor[2];

                    // zera simulacao
                    UFER_P_Sim[i] = relatorio_sim.registro[0].valor[0];
                    UFER_FPI_Sim[i] = relatorio_sim.registro[0].valor[1];
                    UFER_FPC_Sim[i] = relatorio_sim.registro[0].valor[2];
                }

                if (i == 13)
                {
                    // zera
                    UFER_P[i] = relatorio.registro[11].valor[0];
                    UFER_FPI[i] = relatorio.registro[11].valor[1];
                    UFER_FPC[i] = relatorio.registro[11].valor[2];

                    // zera simulacao
                    UFER_P_Sim[i] = relatorio_sim.registro[11].valor[0];
                    UFER_FPI_Sim[i] = relatorio_sim.registro[11].valor[1];
                    UFER_FPC_Sim[i] = relatorio_sim.registro[11].valor[2];
                }

                if (i >= 1 && i <= 12)
                {
                    // copia
                    j = i - 1;

                    UFER_P[i] = relatorio.registro[j].valor[0];
                    UFER_FPI[i] = relatorio.registro[j].valor[1];
                    UFER_FPC[i] = relatorio.registro[j].valor[2];

                    // copia simulacao 
                    UFER_P_Sim[i] = relatorio_sim.registro[j].valor[0];
                    UFER_FPI_Sim[i] = relatorio_sim.registro[j].valor[1];
                    UFER_FPC_Sim[i] = relatorio_sim.registro[j].valor[2];

                    // verifica UFER maximo
                    if ((UFER_P[i] + UFER_FPI[i] + UFER_FPC[i]) > UFER_max_grafico)
                        UFER_max_grafico = (UFER_P[i] + UFER_FPI[i] + UFER_FPC[i]);

                    // verifica UFER maximo simulacao
                    if ((UFER_P_Sim[i] + UFER_FPI_Sim[i] + UFER_FPC_Sim[i]) > UFER_max_grafico)
                        UFER_max_grafico = (UFER_P_Sim[i] + UFER_FPI_Sim[i] + UFER_FPC_Sim[i]);
                }
            }

            UFER_max_grafico = UFER_max_grafico * 1.1;

            if (UFER_max_grafico == 0.0)
            {
                UFER_max_grafico = 10.0;
            }

            ViewBag.UFERMaxGrafico = UFER_max_grafico;

            // grafico
            ViewBag.UFER_P = UFER_P;
            ViewBag.UFER_FPI = UFER_FPI;
            ViewBag.UFER_FPC = UFER_FPC;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Meses = Meses;

            // grafico simulacao
            ViewBag.UFER_P_Sim = UFER_P_Sim;
            ViewBag.UFER_FPI_Sim = UFER_FPI_Sim;
            ViewBag.UFER_FPC_Sim = UFER_FPC_Sim;
            ViewBag.Datas_Sim = Datas_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.UFER;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Anual;

            // data atual
            ViewBag.DataAtual = string.Format("{0:yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:yyyy}", DataAtual);

            // UFER
            double UFER_Total_P = analise.analise_valor.medio[0];
            double UFER_Total_FPI = analise.analise_valor.medio[1];
            double UFER_Total_FPC = analise.analise_valor.medio[2];
            double UFER_Total = (analise.analise_valor.medio[0] + analise.analise_valor.medio[1] + analise.analise_valor.medio[2]);

            // UFER simulacao
            double UFER_Total_P_Sim = analise_sim.analise_valor.medio[0];
            double UFER_Total_FPI_Sim = analise_sim.analise_valor.medio[1];
            double UFER_Total_FPC_Sim = analise_sim.analise_valor.medio[2];
            double UFER_Total_Sim = (analise_sim.analise_valor.medio[0] + analise_sim.analise_valor.medio[1] + analise_sim.analise_valor.medio[2]);

            // total ponta 
            ViewBag.UFER_Total_P = string.Format("{0:#,##0.0}", UFER_Total_P);
            ViewBag.UFER_Total_PN = UFER_Total_P;

            // total ponta simulacao
            ViewBag.UFER_Total_P_Sim = string.Format("{0:#,##0.0}", UFER_Total_P_Sim);
            ViewBag.UFER_Total_PN_Sim = UFER_Total_P_Sim;

            // total fora ponta indutivo 
            ViewBag.UFER_Total_FPI = string.Format("{0:#,##0.0}", UFER_Total_FPI);
            ViewBag.UFER_Total_FPIN = UFER_Total_FPI;

            // total fora ponta indutivo simulacao
            ViewBag.UFER_Total_FPI_Sim = string.Format("{0:#,##0.0}", UFER_Total_FPI_Sim);
            ViewBag.UFER_Total_FPIN_Sim = UFER_Total_FPI_Sim;

            // total fora ponta capacitivo 
            ViewBag.UFER_Total_FPC = string.Format("{0:#,##0.0}", UFER_Total_FPC);
            ViewBag.UFER_Total_FPCN = UFER_Total_FPC;

            // total fora ponta capacitivo simulacao
            ViewBag.UFER_Total_FPC_Sim = string.Format("{0:#,##0.0}", UFER_Total_FPC_Sim);
            ViewBag.UFER_Total_FPCN_Sim = UFER_Total_FPC_Sim;

            // total
            ViewBag.UFER_Total = string.Format("{0:#,##0.0}", UFER_Total);
            ViewBag.UFER_TotalN = UFER_Total;

            // total simulacao
            ViewBag.UFER_Total_Sim = string.Format("{0:#,##0.0}", UFER_Total_Sim);
            ViewBag.UFER_TotalN_Sim = UFER_Total_Sim;

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddYears(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // UFER Anual XLS
        private HSSFWorkbook UFER_Anual_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string str_fpc = string.Format("Fora de Ponta Capacitivo ({0})", ViewBag.UnidadeUFER);
            string str_fpi = string.Format("Fora de Ponta Indutivo ({0})", ViewBag.UnidadeUFER);
            string str_ponta = string.Format("Ponta ({0})", ViewBag.UnidadeUFER);
            string str_total = string.Format("Total ({0})", ViewBag.UnidadeUFER);
            string[] cabecalho = { "Data", str_fpc, str_fpi, str_ponta, str_total };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 13; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // UFER fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.UFER_FPC[i], _1CellStyle);

                // UFER fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.UFER_FPI[i], _1CellStyle);

                // UFER ponta
                numeroCelulaXLS(row, 3, ViewBag.UFER_P[i], _1CellStyle);

                // UFER total
                numeroCelulaXLS(row, 4, ViewBag.UFER_P[i] + ViewBag.UFER_FPI[i] + ViewBag.UFER_FPC[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Energia Reativa Excedente [UFER]", "Relatório Anual", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string str_UFER = string.Format("UFER ({0})", ViewBag.UnidadeUFER);
            string[] cab_UFER = { "UFER", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_UFER, rowIndex++);

            // UFER
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, str_UFER, _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.UFER_Total_FPCN, _1CellStyle);
            numeroCelulaXLS(row, 2, ViewBag.UFER_Total_FPIN, _1CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.UFER_Total_PN, _1CellStyle);
            numeroCelulaXLS(row, 4, ViewBag.UFER_TotalN, _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }        
        
        // UFER Anual XLS Simulacao
        private HSSFWorkbook UFER_Anual_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // simulacao 
            string[] simulacao = { "Energia Reativa Excedente [UFER] - Simulação" };

            // cabecalho simulacao 
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas para titulo simulacao 
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 4));

            // cabecalho
            string str_fpc = string.Format("Fora de Ponta Capacitivo ({0})", ViewBag.UnidadeUFER);
            string str_fpi = string.Format("Fora de Ponta Indutivo ({0})", ViewBag.UnidadeUFER);
            string str_ponta = string.Format("Ponta ({0})", ViewBag.UnidadeUFER);
            string str_total = string.Format("Total ({0})", ViewBag.UnidadeUFER);
            string[] cabecalho = { "Data", str_fpc, str_fpi, str_ponta, str_total };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 13; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[i], _dataStyle);

                // UFER fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.UFER_FPC_Sim[i], _1CellStyle);

                // UFER fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.UFER_FPI_Sim[i], _1CellStyle);

                // UFER ponta
                numeroCelulaXLS(row, 3, ViewBag.UFER_P_Sim[i], _1CellStyle);

                // UFER total
                numeroCelulaXLS(row, 4, ViewBag.UFER_P_Sim[i] + ViewBag.UFER_FPI_Sim[i] + ViewBag.UFER_FPC_Sim[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Energia Reativa Excedente [UFER] - Simulação", "Relatório Anual", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string str_UFER = string.Format("UFER ({0})", ViewBag.UnidadeUFER);
            string[] cab_UFER = { "UFER", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_UFER, rowIndex++);

            // UFER
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, str_UFER, _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.UFER_Total_FPCN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 2, ViewBag.UFER_Total_FPIN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.UFER_Total_PN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 4, ViewBag.UFER_TotalN_Sim, _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }
    }
}