﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {
        // Relatorio Diario
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_EA_RelatDiario", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_EA_RelatDiario(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, ref DATAHORA pdata_equipo, ref RELAT_EA_DIARIO prelatorio, ref RELAT_EA_DIARIO_ANALISE panalise, ref GRA<PERSON>EZ<PERSON> pgrandeza);

        // Relatorio Semanal
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_EA_RelatSemanal", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_EA_RelatSemanal(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, ref DATAHORA pdata_equipo, ref RELAT_EA_SEMANAL prelatorio, ref RELAT_EA_SEMANAL_ANALISE panalise, ref GRANDEZA pgrandeza);

        // Relatorio Mensal
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_EA_RelatMensal", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_EA_RelatMensal(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, ref DATAHORA pdata_equipo, ref RELAT_EA_MENSAL prelatorio, ref RELAT_EA_MENSAL_ANALISE panalise, ref GRANDEZA pgrandeza);

        // Relatorio Anual
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_EA_RelatAnual", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_EA_RelatAnual(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, ref DATAHORA pdata_equipo, ref RELAT_EA_ANUAL prelatorio, ref RELAT_EA_ANUAL_ANALISE panalise, ref GRANDEZA pgrandeza);


        //
        // ENTRADAS ANALOGICAS
        //

        // GET: Relatorio Entradas Analogicas
        public ActionResult Relat_EA(int IDCliente, int IDMedicao)
        {
            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Relat_EA");

            // relatorio entradas analogicas
            return (Relat_Grafico_Show(IDCliente, IDMedicao, 30));
        }

        // Entradas Analogicas Diario
        private void EA_Diario(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_EA_DIARIO relatorio = new RELAT_EA_DIARIO();
            RELAT_EA_DIARIO_ANALISE analise = new RELAT_EA_DIARIO_ANALISE();
            GRANDEZA grandeza = new GRANDEZA();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // calcula valores
            retorno = SmCalcDB_EA_RelatDiario((char)0, ref config_interface, (char)0, ref data_hora, ref relatorio, ref analise, ref grandeza);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var VMax = new double[26];
            var VMed = new double[26];
            var VMin = new double[26];
            var Dias = new string[26];
            var DatasN = new DateTime[26];
            var Horas = new string[26];

            double Valor_min = 0.0;
            double Valor_max = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Horas[i] = strData.ToString("HH:mm");

                // proxima hora
                strData = strData.AddHours(1);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    VMax[i] = relatorio.registro[1].vmax;
                    VMed[i] = relatorio.registro[1].vmed;
                    VMin[i] = relatorio.registro[1].vmin;

                    // minimo e maximo
                    Valor_min = VMin[i];
                    Valor_max = VMax[i];
                }

                // verifica se ultima barra
                if (i == 25)
                {
                    // zera
                    VMax[i] = relatorio.registro[23].vmax;
                    VMed[i] = relatorio.registro[23].vmed;
                    VMin[i] = relatorio.registro[23].vmin;
                }

                if (i >= 1 && i <= 24)
                {
                    // copia
                    j = i - 1;

                    VMax[i] = relatorio.registro[j].vmax;
                    VMed[i] = relatorio.registro[j].vmed;
                    VMin[i] = relatorio.registro[j].vmin;

                    // verifica minimo
                    if (VMax[i] < Valor_min)
                        Valor_min = VMax[i];

                    if (VMed[i] < Valor_min)
                        Valor_min = VMed[i];

                    if (VMin[i] < Valor_min)
                        Valor_min = VMin[i];

                    // verifica maximo
                    if (VMin[i] > Valor_max)
                        Valor_max = VMin[i];

                    if (VMed[i] > Valor_max)
                        Valor_max = VMed[i];

                    if (VMax[i] > Valor_max)
                        Valor_max = VMax[i];
                }
            }

            // valor máximo (5% e arredonda para cima)
            Valor_max = Valor_max + (Math.Abs(Valor_max) * 0.05);
            Valor_max = Math.Ceiling(Valor_max);

            // valor mínimo (5% e arredonda para baixo)
            Valor_min = Valor_min - (Math.Abs(Valor_min) * 0.05);
            Valor_min = Math.Floor(Valor_min);

            // copia valores
            ViewBag.ValorMax = Valor_max;
            ViewBag.ValorMin = Valor_min;

            ViewBag.VMax = VMax;
            ViewBag.VMed = VMed;
            ViewBag.VMin = VMin;
            ViewBag.Dias = Dias;
            ViewBag.DatasN = DatasN;
            ViewBag.Horas = Horas;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.EA;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Diario;

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:d}", DataAtual);

            // valores
            ViewBag.VMaxDia = string.Format("{0:#,##0.00}", analise.analise_valor1.maximo[0]);
            DateTime VMaxDia_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max[0]);
            if (VMaxDia_DataHora.Year != 2000)
                ViewBag.VMaxDia_DataHora = string.Format("{0:HH:mm}", VMaxDia_DataHora);
            else
                ViewBag.VMaxDia_DataHora = "--:--";
            ViewBag.VMaxDia_DataHoraN = VMaxDia_DataHora;

            ViewBag.VMinDia = string.Format("{0:#,##0.00}", analise.analise_valor1.minimo[0]);
            DateTime VMinDia_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_min[0]);
            if (VMinDia_DataHora.Year != 2000)
                ViewBag.VMinDia_DataHora = string.Format("{0:HH:mm}", VMinDia_DataHora);
            else
                ViewBag.VMinDia_DataHora = "--:--";
            ViewBag.VMinDia_DataHoraN = VMinDia_DataHora;

            ViewBag.VMedDia = string.Format("{0:#,##0.00}", analise.analise_valor1.medio[0]);

            ViewBag.NomeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.nome);
            ViewBag.UnidadeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.unidade);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(1);

            ViewBag.data_hora_ini = data_hora_ini;

            EventosPeriodo(IDCliente, IDMedicao, data_hora_ini, data_hora_fim);

            return;
        }

        // Entradas Analogicas Diario XLS
        private HSSFWorkbook EA_Diario_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // grandeza
            string str_grandeza = string.Format("{0} ({1})", ViewBag.NomeGrandeza, ViewBag.UnidadeGrandeza);

            // cabecalho
            string str_min = string.Format("Mínimo ({0})", ViewBag.UnidadeGrandeza);
            string str_med = string.Format("Médio ({0})", ViewBag.UnidadeGrandeza);
            string str_max = string.Format("Máximo ({0})", ViewBag.UnidadeGrandeza);
            string[] cabecalho = { "Data e Hora", str_min, str_med, str_max };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 25; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _datahoraStyle);

                // minimo
                numeroCelulaXLS(row, 1, ViewBag.VMin[i], _2CellStyle);

                // medio
                numeroCelulaXLS(row, 2, ViewBag.VMed[i], _2CellStyle);

                // maximo
                numeroCelulaXLS(row, 3, ViewBag.VMax[i], _2CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Analógicas", "Relatório Diário", _negritoCellStyle);

            // cabecalho
            string[] cab_cons = { "", str_grandeza, "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // maximo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máximo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMaxDia), _2CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.VMaxDia_DataHoraN, _datahoraStyle);

            // medio
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Médio", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMedDia), _2CellStyle);

            // minimo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínimo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMinDia), _2CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.VMinDia_DataHoraN, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }

        // Entradas Analogicas Semanal
        private void EA_Semanal(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_EA_SEMANAL relatorio = new RELAT_EA_SEMANAL();
            RELAT_EA_SEMANAL_ANALISE analise = new RELAT_EA_SEMANAL_ANALISE();
            GRANDEZA grandeza = new GRANDEZA();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // calcula valores
            retorno = SmCalcDB_EA_RelatSemanal((char)0, ref config_interface, (char)0, ref data_hora, ref relatorio, ref analise, ref grandeza);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var VMax = new double[7, 26];
            var VMed = new double[7, 26];
            var VMin = new double[7, 26];
            var Datas = new string[26];
            var DatasN = new DateTime[7, 26];
            var Dias = new string[7];
            var Horas = new string[26];

            DateTime inicio = new DateTime();
            DateTime fim = new DateTime();

            double Valor_min = 0.0;
            double Valor_max = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;
            int k = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                Horas[i] = strData.ToString("HH:mm");

                // percorre dias
                for (k = 0; k < 7; k++)
                {
                    // data e hora para excel
                    DatasN[k, i] = strData.AddDays(k);

                    // guarda inicio e fim
                    if (i == 0 && k == 0)
                    {
                        inicio = strData;
                        fim = inicio.AddDays(6);

                        // minimo e maximo
                        Valor_min = relatorio.registro[0].vmin[k];
                        Valor_max = relatorio.registro[0].vmax[k];
                    }

                    // dia da semana
                    if (i == 1)
                    {
                        Dias[k] = string.Format("{0:d}", strData.AddDays(k));
                    }

                    // verifica se primeira ou ultima barra
                    if (i == 0)
                    {
                        // zera
                        VMax[k, i] = relatorio.registro[0].vmax[k];
                        VMed[k, i] = relatorio.registro[0].vmed[k];
                        VMin[k, i] = relatorio.registro[0].vmin[k];
                    }

                    if (i == 25)
                    {
                        // zera
                        VMax[k, i] = relatorio.registro[23].vmax[k];
                        VMed[k, i] = relatorio.registro[23].vmed[k];
                        VMin[k, i] = relatorio.registro[23].vmin[k];
                    }

                    if (i >= 1 && i <= 24)
                    {
                        // copia
                        j = i - 1;

                        VMax[k, i] = relatorio.registro[j].vmax[k];
                        VMed[k, i] = relatorio.registro[j].vmed[k];
                        VMin[k, i] = relatorio.registro[j].vmin[k];

                        // verifica minimo
                        if (VMax[k, i] < Valor_min)
                            Valor_min = VMax[k, i];

                        if (VMed[k, i] < Valor_min)
                            Valor_min = VMed[k, i];

                        if (VMin[k, i] < Valor_min)
                            Valor_min = VMin[k, i];

                        // verifica maximo
                        if (VMin[k, i] > Valor_max)
                            Valor_max = VMin[k, i];

                        if (VMed[k, i] > Valor_max)
                            Valor_max = VMed[k, i];

                        if (VMax[k, i] > Valor_max)
                            Valor_max = VMax[k, i];
                    }
                }

                // proxima hora
                strData = strData.AddHours(1);
            }

            // valor máximo (5% e arredonda para cima)
            Valor_max = Valor_max + (Math.Abs(Valor_max) * 0.05);
            Valor_max = Math.Ceiling(Valor_max);

            // valor mínimo (5% e arredonda para baixo)
            Valor_min = Valor_min - (Math.Abs(Valor_min) * 0.05);
            Valor_min = Math.Floor(Valor_min);

            // copia valores
            ViewBag.ValorMax = Valor_max;
            ViewBag.ValorMin = Valor_min;

            ViewBag.VMax = VMax;
            ViewBag.VMed = VMed;
            ViewBag.VMin = VMin;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;
            ViewBag.Horas = Horas;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.EA;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Semanal;

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);
            ViewBag.DataAtualN = inicio;
            ViewBag.DataFinalN = fim;
            ViewBag.DataTextoAtual = string.Format("{0:d} - {1:d}", inicio, fim);

            // valores
            ViewBag.VMaxSemana = string.Format("{0:#,##0.00}", analise.analise_valor.maximo[0]);
            DateTime VMaxSemana_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (VMaxSemana_DataHora.Year != 2000)
                ViewBag.VMaxSemana_DataHora = string.Format("{0:d} {1:HH:mm}", VMaxSemana_DataHora, VMaxSemana_DataHora);
            else
                ViewBag.VMaxSemana_DataHora = "--/--/---- --:--";
            ViewBag.VMaxSemana_DataHoraN = VMaxSemana_DataHora;

            ViewBag.VMinSemana = string.Format("{0:#,##0.00}", analise.analise_valor.minimo[0]);
            DateTime VMinSemana_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[0]);
            if (VMinSemana_DataHora.Year != 2000)
                ViewBag.VMinSemana_DataHora = string.Format("{0:d} {1:HH:mm}", VMinSemana_DataHora, VMinSemana_DataHora);
            else
                ViewBag.VMinSemana_DataHora = "--/--/---- --:--";
            ViewBag.VMinSemana_DataHoraN = VMinSemana_DataHora;

            ViewBag.VMedSemana = string.Format("{0:#,##0.00}", analise.analise_valor.medio[0]);

            ViewBag.NomeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.nome);
            ViewBag.UnidadeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.unidade);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(inicio.Year, inicio.Month, inicio.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(7);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Entradas Analogicas Semanal XLS
        private HSSFWorkbook EA_Semanal_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // grandeza
            string str_grandeza = string.Format("{0} ({1})", ViewBag.NomeGrandeza, ViewBag.UnidadeGrandeza);

            // dias da semana
            int k = 0;
            int i;
            IRow row;
            ISheet sheet;
            int rowIndex;

            for (k = 0; k < 7; k++)
            {
                // cria planilha
                sheet = workbook.CreateSheet(string.Format("{0:dddd}", ViewBag.DatasN[k, 0]));

                // cabecalho
                string str_min = string.Format("Mínimo ({0})", ViewBag.UnidadeGrandeza);
                string str_med = string.Format("Médio ({0})", ViewBag.UnidadeGrandeza);
                string str_max = string.Format("Máximo ({0})", ViewBag.UnidadeGrandeza);
                string[] cabecalho = { "Data e Hora", str_min, str_med, str_max };

                // adiciona cabecalho
                rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

                // percorre valores
                for (i = 1; i < 25; i++)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // data e hora
                    datahoraCelulaXLS(row, 0, ViewBag.DatasN[k, i], _datahoraStyle);

                    // minimo
                    numeroCelulaXLS(row, 1, ViewBag.VMin[k, i], _2CellStyle);

                    // medio
                    numeroCelulaXLS(row, 2, ViewBag.VMed[k, i], _2CellStyle);

                    // maximo
                    numeroCelulaXLS(row, 3, ViewBag.VMax[k, i], _2CellStyle);

                    // proxima
                    rowIndex++;
                }

                // largura de cada coluna
                for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                    sheet.SetColumnWidth(i, 6000);
            }

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Analógicas", "Relatório Semanal", _negritoCellStyle);

            // cabecalho
            string[] cab_cons = { "", str_grandeza, "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // maximo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máximo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMaxSemana), _2CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.VMaxSemana_DataHoraN, _datahoraStyle);

            // medio
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Médio", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMedSemana), _2CellStyle);

            // minimo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínimo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMinSemana), _2CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.VMinSemana_DataHoraN, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }

        // Entradas Analogicas Mensal
        private void EA_Mensal(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_EA_MENSAL relatorio = new RELAT_EA_MENSAL();
            RELAT_EA_MENSAL_ANALISE analise = new RELAT_EA_MENSAL_ANALISE();
            GRANDEZA grandeza = new GRANDEZA();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // calcula valores
            retorno = SmCalcDB_EA_RelatMensal((char)0, ref config_interface, (char)0, ref data_hora, ref relatorio, ref analise, ref grandeza);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   1, 1, 0, 0);

            int NumDiasMes = relatorio.num_dias;

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var VMax = new double[33];
            var VMed = new double[33];
            var VMin = new double[33];
            var Datas = new string[33];
            var DatasN = new DateTime[33];
            var Dias = new string[33];

            double Valor_min = 0.0;
            double Valor_max = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = string.Format("{0:0}", strData.Day);

                // proximo dias
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    VMax[i] = relatorio.registro[0].vmax;
                    VMed[i] = relatorio.registro[0].vmed;
                    VMin[i] = relatorio.registro[0].vmin;

                    // minimo e maximo
                    Valor_min = VMin[i];
                    Valor_max = VMax[i];
                }

                if (i == (NumDiasMes + 1))
                {
                    // zera
                    VMax[i] = relatorio.registro[NumDiasMes - 1].vmax;
                    VMed[i] = relatorio.registro[NumDiasMes - 1].vmed;
                    VMin[i] = relatorio.registro[NumDiasMes - 1].vmin;
                }

                if (i >= 1 && i <= NumDiasMes)
                {
                    // copia
                    j = i - 1;

                    VMax[i] = relatorio.registro[j].vmax;
                    VMed[i] = relatorio.registro[j].vmed;
                    VMin[i] = relatorio.registro[j].vmin;

                    // verifica minimo
                    if (VMax[i] < Valor_min)
                        Valor_min = VMax[i];

                    if (VMed[i] < Valor_min)
                        Valor_min = VMed[i];

                    if (VMin[i] < Valor_min)
                        Valor_min = VMin[i];

                    // verifica maximo
                    if (VMin[i] > Valor_max)
                        Valor_max = VMin[i];

                    if (VMed[i] > Valor_max)
                        Valor_max = VMed[i];

                    if (VMax[i] > Valor_max)
                        Valor_max = VMax[i];
                }
            }

            // valor máximo (10% e arredonda para cima)
            Valor_max = Valor_max + (Math.Abs(Valor_max) * 0.1);
            Valor_max = Math.Ceiling(Valor_max);

            // valor mínimo (10% e arredonda para baixo)
            Valor_min = Valor_min - (Math.Abs(Valor_min) * 0.1);
            Valor_min = Math.Floor(Valor_min);

            // copia valores
            ViewBag.ValorMax = Valor_max;
            ViewBag.ValorMin = Valor_min;

            ViewBag.VMax = VMax;
            ViewBag.VMed = VMed;
            ViewBag.VMin = VMin;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.EA;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Mensal;

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:y}", DataAtual);

            ViewBag.NumDiasMes = NumDiasMes;

            // valores
            ViewBag.VMaxMes = string.Format("{0:#,##0.00}", analise.analise_valor.maximo[0]);
            DateTime VMaxMes_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (VMaxMes_DataHora.Year != 2000)
                ViewBag.VMaxMes_DataHora = string.Format("{0:d} {1:HH:mm}", VMaxMes_DataHora, VMaxMes_DataHora);
            else
                ViewBag.VMaxMes_DataHora = "--/--/---- --:--";
            ViewBag.VMaxMes_DataHoraN = VMaxMes_DataHora;

            ViewBag.VMinMes = string.Format("{0:#,##0.00}", analise.analise_valor.minimo[0]);
            DateTime VMinMes_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[0]);
            if (VMinMes_DataHora.Year != 2000)
                ViewBag.VMinMes_DataHora = string.Format("{0:d} {1:HH:mm}", VMinMes_DataHora, VMinMes_DataHora);
            else
                ViewBag.VMinMes_DataHora = "--/--/---- --:--";
            ViewBag.VMinMes_DataHoraN = VMinMes_DataHora;

            ViewBag.VMedMes = string.Format("{0:#,##0.00}", analise.analise_valor.medio[0]);

            ViewBag.NomeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.nome);
            ViewBag.UnidadeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.unidade);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Entradas Analogicas Mensal XLS
        private HSSFWorkbook EA_Mensal_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // grandeza
            string str_grandeza = string.Format("{0} ({1})", ViewBag.NomeGrandeza, ViewBag.UnidadeGrandeza);

            // cabecalho
            string str_min = string.Format("Mínimo ({0})", ViewBag.UnidadeGrandeza);
            string str_med = string.Format("Médio ({0})", ViewBag.UnidadeGrandeza);
            string str_max = string.Format("Máximo ({0})", ViewBag.UnidadeGrandeza);
            string[] cabecalho = { "Data e Hora", str_min, str_med, str_max };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < (ViewBag.NumDiasMes + 1); i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // minimo
                numeroCelulaXLS(row, 1, ViewBag.VMin[i], _2CellStyle);

                // medio
                numeroCelulaXLS(row, 2, ViewBag.VMed[i], _2CellStyle);

                // maximo
                numeroCelulaXLS(row, 3, ViewBag.VMax[i], _2CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Analógicas", "Relatório Mensal", _negritoCellStyle);

            // cabecalho
            string[] cab_cons = { "", str_grandeza, "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // maximo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máximo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMaxMes), _2CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.VMaxMes_DataHoraN, _datahoraStyle);

            // medio
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Médio", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMedMes), _2CellStyle);

            // minimo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínimo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMinMes), _2CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.VMinMes_DataHoraN, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }

        // Entradas Analogicas Anual
        private void EA_Anual(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_EA_ANUAL relatorio = new RELAT_EA_ANUAL();
            RELAT_EA_ANUAL_ANALISE analise = new RELAT_EA_ANUAL_ANALISE();
            GRANDEZA grandeza = new GRANDEZA();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // calcula valores
            retorno = SmCalcDB_EA_RelatAnual((char)0, ref config_interface, (char)0, ref data_hora, ref relatorio, ref analise, ref grandeza);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   1, 1, 1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var VMax = new double[14];
            var VMed = new double[14];
            var VMin = new double[14];
            var Datas = new string[14];
            var DatasN = new DateTime[14];
            var Meses = new string[14];

            double Valor_min = 0.0;
            double Valor_max = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 mes para a primeira barra
            strData = strData.AddMonths(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 14; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Meses[i] = string.Format("{0:MMMM}", strData);

                // proximo mes
                strData = strData.AddMonths(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    VMax[i] = relatorio.registro[0].vmax;
                    VMed[i] = relatorio.registro[0].vmed;
                    VMin[i] = relatorio.registro[0].vmin;

                    // minimo e maximo
                    Valor_min = VMin[i];
                    Valor_max = VMax[i];
                }

                if (i == 13)
                {
                    // zera
                    VMax[i] = relatorio.registro[11].vmax;
                    VMed[i] = relatorio.registro[11].vmed;
                    VMin[i] = relatorio.registro[11].vmin;
                }

                if (i >= 1 && i <= 12)
                {
                    // copia
                    j = i - 1;

                    VMax[i] = relatorio.registro[j].vmax;
                    VMed[i] = relatorio.registro[j].vmed;
                    VMin[i] = relatorio.registro[j].vmin;

                    // verifica minimo
                    if (VMax[i] < Valor_min)
                        Valor_min = VMax[i];

                    if (VMed[i] < Valor_min)
                        Valor_min = VMed[i];

                    if (VMin[i] < Valor_min)
                        Valor_min = VMin[i];

                    // verifica maximo
                    if (VMin[i] > Valor_max)
                        Valor_max = VMin[i];

                    if (VMed[i] > Valor_max)
                        Valor_max = VMed[i];

                    if (VMax[i] > Valor_max)
                        Valor_max = VMax[i];
                }
            }

            // valor máximo (10% e arredonda para cima)
            Valor_max = Valor_max + (Math.Abs(Valor_max) * 0.1);
            Valor_max = Math.Ceiling(Valor_max);

            // valor mínimo (10% e arredonda para baixo)
            Valor_min = Valor_min - (Math.Abs(Valor_min) * 0.1);
            Valor_min = Math.Floor(Valor_min);

            // copia valores
            ViewBag.ValorMax = Valor_max;
            ViewBag.ValorMin = Valor_min;

            ViewBag.VMax = VMax;
            ViewBag.VMed = VMed;
            ViewBag.VMin = VMin;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Meses = Meses;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.EA;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Anual;

            // data atual
            ViewBag.DataAtual = string.Format("{0:yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:yyyy}", DataAtual);

            // valores
            ViewBag.VMaxAno = string.Format("{0:#,##0.00}", analise.analise_valor.maximo[0]);
            DateTime VMaxAno_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (VMaxAno_DataHora.Year != 2000)
                ViewBag.VMaxAno_DataHora = string.Format("{0:d} {1:HH:mm}", VMaxAno_DataHora, VMaxAno_DataHora);
            else
                ViewBag.VMaxAno_DataHora = "--/--/---- --:--";
            ViewBag.VMaxAno_DataHoraN = VMaxAno_DataHora;

            ViewBag.VMinAno = string.Format("{0:#,##0.00}", analise.analise_valor.minimo[0]);
            DateTime VMinAno_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[0]);
            if (VMinAno_DataHora.Year != 2000)
                ViewBag.VMinAno_DataHora = string.Format("{0:d} {1:HH:mm}", VMinAno_DataHora, VMinAno_DataHora);
            else
                ViewBag.VMinAno_DataHora = "--/--/---- --:--";
            ViewBag.VMinAno_DataHoraN = VMinAno_DataHora;

            ViewBag.VMedAno = string.Format("{0:#,##0.00}", analise.analise_valor.medio[0]);

            ViewBag.NomeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.nome);
            ViewBag.UnidadeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.unidade);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddYears(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Entradas Analogicas Anual XLS
        private HSSFWorkbook EA_Anual_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // grandeza
            string str_grandeza = string.Format("{0} ({1})", ViewBag.NomeGrandeza, ViewBag.UnidadeGrandeza);

            // cabecalho
            string str_min = string.Format("Mínimo ({0})", ViewBag.UnidadeGrandeza);
            string str_med = string.Format("Médio ({0})", ViewBag.UnidadeGrandeza);
            string str_max = string.Format("Máximo ({0})", ViewBag.UnidadeGrandeza);
            string[] cabecalho = { "Data e Hora", str_min, str_med, str_max };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 13; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // minimo
                numeroCelulaXLS(row, 1, ViewBag.VMin[i], _2CellStyle);

                // medio
                numeroCelulaXLS(row, 2, ViewBag.VMed[i], _2CellStyle);

                // maximo
                numeroCelulaXLS(row, 3, ViewBag.VMax[i], _2CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Analógicas", "Relatório Anual", _negritoCellStyle);

            // cabecalho
            string[] cab_cons = { "", str_grandeza, "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // maximo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máximo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMaxAno), _2CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.VMaxAno_DataHoraN, _datahoraStyle);

            // medio
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Médio", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMedAno), _2CellStyle);

            // minimo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínimo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMinAno), _2CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.VMinAno_DataHoraN, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }

        // Entradas Analogicas exportar XLS
        private HSSFWorkbook EA_exportar_XLS(int IDCliente, int IDMedicao, DateTime DataIni, DateTime DataFim)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // datas
            ViewBag.DataAtualN = DataIni;
            ViewBag.DataFinalN = DataFim;

            // medicao
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);

            // calcula
            GG_Metodos ggMetodos = new GG_Metodos();
            List<GG_Dominio> ggRegistros = ggMetodos.ListarTodosPeriodo(IDCliente, IDMedicao, DataIni, DataFim);

            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", string.Format("Médio ({0})", medicao.UnidadeGrandeza), string.Format("Mínimo ({0})", medicao.UnidadeGrandeza), string.Format("Máximo ({0})", medicao.UnidadeGrandeza) };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            if (ggRegistros != null)
            {
                // percorre valores
                foreach (GG_Dominio registro in ggRegistros)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // data e hora
                    datahoraCelulaXLS(row, 0, registro.DataHora, _datahoraStyle);

                    // medio
                    numeroCelulaXLS(row, 1, registro.VMed, _2CellStyle);

                    // minimo
                    numeroCelulaXLS(row, 2, registro.VMin, _2CellStyle);

                    // maximo
                    numeroCelulaXLS(row, 3, registro.VMax, _2CellStyle);

                    // proxima
                    rowIndex++;
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Entrada Analógica", "", _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(9).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }
    }
}
