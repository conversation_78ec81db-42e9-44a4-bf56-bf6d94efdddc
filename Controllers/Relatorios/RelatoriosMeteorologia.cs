﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {
        //
        // MEDIÇÕES METEOROLOGIA
        //

        // GET: Relatorio Meteorologia
        public ActionResult Relat_Meteorologia(int IDCliente, int IDMedicao)
        {
            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Relat_Meteorologia");

            // relatorio Medicoes Meteorologia
            return (Relat_Grafico_Show(IDCliente, IDMedicao, 70));
        }

        // Meteorologia Diario
        private void Meteorologia_Diario(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            // converte data hora 
            DateTime DataRelat = Funcoes_Converte.ConverteDataHora2DateTime(data_hora);            

            // lista relatorio
            HistoricoWeatherMetodos weatherMetodos = new HistoricoWeatherMetodos();
            RELAT_METEOROLOGIA_DIARIO relatorio = new RELAT_METEOROLOGIA_DIARIO();
            RELAT_METEOROLOGIA_DIARIO_ANALISE analise = new RELAT_METEOROLOGIA_DIARIO_ANALISE();

            retorno = weatherMetodos.Calc_Metrologia_Diario(IDMedicao, DataRelat, ref relatorio, ref analise);

            // data atual
            DateTime DataAtual = new DateTime(relatorio.registro[0].DataHora.Year,
                                     relatorio.registro[0].DataHora.Month,
                                     relatorio.registro[0].DataHora.Day,
                                     1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var Temp = new double[26];
            var Minima = new double[26];
            var Maxima = new double[26];
            var Dias = new string[26];
            var DatasN = new DateTime[26];
            var Horas = new string[26];            

            // tooltip
            var Umidade = new double[26];
            var Pressao = new double[26];
            var Tempo_Cod = new int[26];
            var Vento_Direcao = new int[26];
            var Vento_Velocidade = new double[26];

            double Valor_min = 0.0;
            double Valor_max = 0.0;

            // registros 08h / 14h / 20h
            ViewBag.horariosRelevantes = analise.horariosRelevantes;
            int[] icones = new int[4];

            for (int indice = 0; indice < 4; indice++)
            {
                // inicia zerado
                icones[indice] = 0;

                // copia codigo do clima
                icones[indice] = analise.horariosRelevantes[indice].Tempo_Cod;
            }

            ViewBag.Icones = icones;
            
            // valores
            DateTime strData = new DateTime(relatorio.registro[0].DataHora.Year, relatorio.registro[0].DataHora.Month, relatorio.registro[0].DataHora.Day, relatorio.registro[0].DataHora.Hour, relatorio.registro[0].DataHora.Minute, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Horas[i] = strData.ToString("HH:mm");

                // proxima hora
                strData = strData.AddHours(1);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    Temp[0] = relatorio.registro[0].Temp_Atual;
                    Minima[0] = relatorio.registro[0].Temp_Min;
                    Maxima[0] = relatorio.registro[0].Temp_Max;
                    Umidade[0] = relatorio.registro[0].Umidade;
                    Pressao[0] = relatorio.registro[0].Pressao;
                    Tempo_Cod[0] = relatorio.registro[0].Tempo_Cod;
                    Vento_Direcao[0] = relatorio.registro[0].Vento_Direcao;
                    Vento_Velocidade[0] = relatorio.registro[0].Vento_Velocidade;

                    // minimo e maximo
                    Valor_min = Minima[0];
                    Valor_max = Maxima[0];
                }

                // verifica se ultima barra
                if (i == 25)
                {
                    // zera
                    Temp[25] = relatorio.registro[23].Temp_Atual;
                    Minima[25] = relatorio.registro[23].Temp_Min;
                    Maxima[25] = relatorio.registro[23].Temp_Max;
                    Umidade[25] = relatorio.registro[23].Umidade;
                    Pressao[25] = relatorio.registro[23].Pressao;
                    Tempo_Cod[25] = relatorio.registro[23].Tempo_Cod;
                    Vento_Direcao[25] = relatorio.registro[23].Vento_Direcao;
                    Vento_Velocidade[25] = relatorio.registro[23].Vento_Velocidade;
                }

                if (i >= 1 && i <= 24)
                {
                    // copia
                    j = i - 1;

                    Temp[i] = relatorio.registro[j].Temp_Atual;
                    Minima[i] = relatorio.registro[j].Temp_Min;
                    Maxima[i] = relatorio.registro[j].Temp_Max;
                    Umidade[i] = relatorio.registro[j].Umidade;
                    Pressao[i] = relatorio.registro[j].Pressao;
                    Tempo_Cod[i] = relatorio.registro[j].Tempo_Cod;
                    Vento_Direcao[i] = relatorio.registro[j].Vento_Direcao;
                    Vento_Velocidade[i] = relatorio.registro[j].Vento_Velocidade;

                    // verifica minimo
                    if (Maxima[i] < Valor_min)
                        Valor_min = Maxima[i];

                    if (Temp[i] < Valor_min)
                        Valor_min = Temp[i];

                    if (Minima[i] < Valor_min)
                        Valor_min = Minima[i];

                    // verifica maximo
                    if (Minima[i] > Valor_max)
                        Valor_max = Minima[i];

                    if (Temp[i] > Valor_max)
                        Valor_max = Temp[i];

                    if (Maxima[i] > Valor_max)
                        Valor_max = Temp[i];
                }
            }

            // valor máximo (5% e arredonda para cima)
            Valor_max = Valor_max + (Math.Abs(Valor_max) * 0.05);
            Valor_max = Math.Ceiling(Valor_max);

            // valor mínimo (5% e arredonda para baixo)
            Valor_min = Valor_min - (Math.Abs(Valor_min) * 0.05);
            Valor_min = Math.Floor(Valor_min);

            // copia valores
            ViewBag.ValorMax = Valor_max;
            ViewBag.ValorMin = Valor_min;
            ViewBag.Temp = Temp;
            ViewBag.Minima = Minima;
            ViewBag.Maxima = Maxima;
            ViewBag.Dias = Dias;
            ViewBag.DatasN = DatasN;
            ViewBag.Horas = Horas;

            // tooltip
            ViewBag.Umidade = Umidade;
            ViewBag.Pressao = Pressao;
            ViewBag.Tempo_Cod = Tempo_Cod;
            ViewBag.Vento_Direcao = Vento_Direcao;
            ViewBag.Vento_Velocidade = Vento_Velocidade;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.Meteorologia;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Diario;

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:d}", DataAtual);

            // valores
            ViewBag.VMaxDia = string.Format("{0:0}", analise.Valor_Max);
            DateTime VMaxDia_DataHora = analise.Valor_MaxDataHora;
            if (VMaxDia_DataHora.Year != 2000)
                ViewBag.VMaxDia_DataHora = string.Format("{0:HH:mm}", VMaxDia_DataHora);
            else
                ViewBag.VMaxDia_DataHora = "--:--";
            ViewBag.VMaxDia_DataHoraN = VMaxDia_DataHora;

            ViewBag.VMinDia = string.Format("{0:0}", analise.Valor_Min);
            DateTime VMinDia_DataHora = analise.Valor_MinDataHora;
            if (VMinDia_DataHora.Year != 2000)
                ViewBag.VMinDia_DataHora = string.Format("{0:HH:mm}", VMinDia_DataHora);
            else
                ViewBag.VMinDia_DataHora = "--:--";
            ViewBag.VMinDia_DataHoraN = VMinDia_DataHora;

            ViewBag.VMedDia = string.Format("{0:0}", analise.Valor_Media);

            ViewBag.NomeGrandeza = "Temperatura";
            ViewBag.UnidadeGrandeza = "°C";

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(1);

            ViewBag.data_hora_ini = data_hora_ini;

            EventosPeriodo(IDCliente, IDMedicao, data_hora_ini, data_hora_fim);

            return;
        }


        // Meteorologia Diario XLS
        private HSSFWorkbook Meteorologia_Diario_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // grandeza
            string str_grandeza = string.Format("{0} ({1})", ViewBag.NomeGrandeza, ViewBag.UnidadeGrandeza);

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Temperatura (°C)", "Umidade (%)", "Pressão (hPa)", "Velocidade do Vento (Km/h)", "Direção do Vento", "Clima" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            string sem_registros = "---";

            // percorre valores
            for (i = 1; i < 25; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _datahoraStyle);

                if (ViewBag.Tempo_Cod[i] < 0)
                {
                    textoCelulaXLS(row, 1, sem_registros);
                    textoCelulaXLS(row, 2, sem_registros);
                    textoCelulaXLS(row, 3, sem_registros);
                    textoCelulaXLS(row, 4, sem_registros);
                    textoCelulaXLS(row, 5, sem_registros);
                    textoCelulaXLS(row, 6, sem_registros);
                }
                else
                {
                    // temperatura
                    numeroCelulaXLS(row, 1, ViewBag.Temp[i], _intCellStyle);

                    // umidade
                    numeroCelulaXLS(row, 2, ViewBag.Umidade[i], _intCellStyle);

                    // pressao
                    numeroCelulaXLS(row, 3, ViewBag.Pressao[i], _intCellStyle);

                    // velocidade vento
                    numeroCelulaXLS(row, 4, ViewBag.Vento_Velocidade[i], _intCellStyle);

                    // direcao vento
                    string[] nome_Direcao = { "Norte", "Norte - Nordeste", "Nordeste", "Este - Nordeste", "Leste", "Este - Sudeste", "Sudeste", "Sul - Sudeste", "Sul", "Sul - Sudoeste", "Sudoeste", "Oeste - Sudoeste", "Oeste", "Oeste - Noroeste", "Noroeste", "Norte - Noroeste" };
                    if (ViewBag.Vento_Direcao[i] >= 0)
                        textoCelulaXLS(row, 5, nome_Direcao[ViewBag.Vento_Direcao[i]]);
                    else
                        textoCelulaXLS(row, 5, sem_registros);

                    // clima
                    string[] nome_Clima = { "Céu Claro", "Noite Clara", "Parcialmente Nublado", "Parcialmente Nublado", "Nublado", "Chuvoso", "Granizo", "Neve", "Vento", "Nevoeiro" };
                    if (ViewBag.Vento_Direcao[i] >= 0)
                        textoCelulaXLS(row, 6, nome_Clima[ViewBag.Tempo_Cod[i]]);
                    else
                        textoCelulaXLS(row, 6, sem_registros);
                }

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            sheet.SetColumnWidth(4, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Meteorologia", "Relatório Diário", _negritoCellStyle);

            // cabecalho
            string[] cab_cons = { "", str_grandeza, "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // maximo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Temperatura Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMaxDia), _intCellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.VMaxDia_DataHoraN, _datahoraStyle);

            // medio
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Temperatura Média", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMedDia), _intCellStyle);

            // minimo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Temperatura Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMinDia), _intCellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.VMinDia_DataHoraN, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }


        // Meteorologia Semanal 
        private void Meteorologia_Semanal(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            // converte data hora 
            DateTime DataRelat = Funcoes_Converte.ConverteDataHora2DateTime(data_hora);            

            // lista relatorio
            HistoricoWeatherMetodos weatherMetodos = new HistoricoWeatherMetodos();
            RELAT_METEOROLOGIA_SEMANAL relatorio = new RELAT_METEOROLOGIA_SEMANAL();
            RELAT_METEOROLOGIA_SEMANAL_ANALISE analise = new RELAT_METEOROLOGIA_SEMANAL_ANALISE();

            // calcula valores
            retorno = weatherMetodos.Calc_Metrologia_Semanal(IDMedicao, DataRelat, ref relatorio, ref analise);

            // data atual
            DateTime DataAtual = new DateTime(relatorio.registro[0].DataHora[0].Year,
                                     relatorio.registro[0].DataHora[0].Month,
                                     relatorio.registro[0].DataHora[0].Day,
                                     1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var Temp = new double[7, 26];
            var Minima = new double[7, 26];
            var Maxima = new double[7, 26];
            var Dias = new string[7];
            var Datas = new string[26];
            var DatasN = new DateTime[7, 26];
            var Horas = new string[26];

            // utiliza codigo para analisar 'sem registros'
            var Tempo_Cod = new int[7, 26];

            DateTime inicio = new DateTime();
            DateTime fim = new DateTime();

            double Valor_min = 0.0;
            double Valor_max = 0.0;

            // valores
            DateTime strData = new DateTime(relatorio.registro[0].DataHora[0].Year, relatorio.registro[0].DataHora[0].Month, relatorio.registro[0].DataHora[0].Day, relatorio.registro[0].DataHora[0].Hour, relatorio.registro[0].DataHora[0].Minute, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;
            int k = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                Horas[i] = strData.ToString("HH:mm");

                // percorre dias
                for (k = 0; k < 7; k++)
                {
                    // data e hora para excel
                    DatasN[k, i] = strData.AddDays(k);

                    // guarda inicio e fim
                    if (i == 0 && k == 0)
                    {
                        inicio = strData;
                        fim = inicio.AddDays(6);

                        // minimo e maximo
                        Valor_min = relatorio.registro[0].Temp_Min[k];
                        Valor_max = relatorio.registro[0].Temp_Max[k];
                    }

                    // dia da semana
                    if (i == 1)
                    {
                        Dias[k] = string.Format("{0:d}", strData.AddDays(k));
                    }

                    // verifica se primeira ou ultima barra
                    if (i == 0)
                    {
                        // zera
                        Temp[k, i] = relatorio.registro[0].Temp_Atual[k];
                        Tempo_Cod[k, i] = relatorio.registro[0].Tempo_Cod[k];
                    }

                    if (i == 25)
                    {
                        // zera
                        Temp[k, i] = relatorio.registro[23].Temp_Atual[k];
                        Tempo_Cod[k, i] = relatorio.registro[23].Tempo_Cod[k];
                    }

                    if (i >= 1 && i <= 24)
                    {
                        // copia
                        j = i - 1;

                        Temp[k, i] = relatorio.registro[j].Temp_Atual[k];
                        Tempo_Cod[k, i] = relatorio.registro[j].Tempo_Cod[k];

                        // verifica minimo
                        if (Maxima[k, i] < Valor_min)
                            Valor_min = Maxima[k, i];

                        if (Temp[k, i] < Valor_min)
                            Valor_min = Temp[k, i];

                        if (Minima[k, i] < Valor_min)
                            Valor_min = Minima[k, i];

                        // verifica maximo
                        if (Minima[k, i] > Valor_max)
                            Valor_max = Minima[k, i];

                        if (Temp[k, i] > Valor_max)
                            Valor_max = Temp[k, i];

                        if (Maxima[k, i] > Valor_max)
                            Valor_max = Maxima[k, i];
                    }
                }

                // proxima hora
                strData = strData.AddHours(1);
            }

            // valor máximo (5% e arredonda para cima)
            Valor_max = Valor_max + (Math.Abs(Valor_max) * 0.05);
            Valor_max = Math.Ceiling(Valor_max);

            // valor mínimo (5% e arredonda para baixo)
            Valor_min = Valor_min - (Math.Abs(Valor_min) * 0.05);
            Valor_min = Math.Floor(Valor_min);

            // copia valores
            ViewBag.ValorMax = Valor_max;
            ViewBag.ValorMin = Valor_min;

            ViewBag.Temp = Temp;
            ViewBag.Tempo_Cod = Tempo_Cod;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;
            ViewBag.Horas = Horas;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.Meteorologia;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Semanal;

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);
            ViewBag.DataAtualN = inicio;
            ViewBag.DataFinalN = fim;
            ViewBag.DataTextoAtual = string.Format("{0:d} - {1:d}", inicio, fim);

            // valores
            ViewBag.VMaxSemana = string.Format("{0:0}", analise.Valor_Max);
            DateTime VMaxSemana_DataHora = analise.Valor_MaxDataHora;
            if (VMaxSemana_DataHora.Year != 2000)
                ViewBag.VMaxSemana_DataHora = string.Format("{0:d} {1:HH:mm}", VMaxSemana_DataHora, VMaxSemana_DataHora);
            else
                ViewBag.VMaxSemana_DataHora = "--/--/---- --:--";
            ViewBag.VMaxSemana_DataHoraN = VMaxSemana_DataHora;

            ViewBag.VMinSemana = string.Format("{0:0}", analise.Valor_Min);
            DateTime VMinSemana_DataHora = analise.Valor_MinDataHora;
            if (VMinSemana_DataHora.Year != 2000)
                ViewBag.VMinSemana_DataHora = string.Format("{0:d} {1:HH:mm}", VMinSemana_DataHora, VMinSemana_DataHora);
            else
                ViewBag.VMinSemana_DataHora = "--/--/---- --:--";
            ViewBag.VMinSemana_DataHoraN = VMinSemana_DataHora;

            ViewBag.VMedSemana = string.Format("{0:0}", analise.Valor_Media);

            ViewBag.NomeGrandeza = "Temperatura";
            ViewBag.UnidadeGrandeza = "°C";

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(inicio.Year, inicio.Month, inicio.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(7);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Meteorologia Semanal XLS
        private HSSFWorkbook Meteorologia_Semanal_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // grandeza
            string str_grandeza = string.Format("{0} ({1})", ViewBag.NomeGrandeza, ViewBag.UnidadeGrandeza);

            // dias da semana
            int k = 0;
            int i;
            IRow row;
            ISheet sheet;
            int rowIndex;

            for (k = 0; k < 7; k++)
            {
                // cria planilha
                sheet = workbook.CreateSheet(string.Format("{0:dddd}", ViewBag.DatasN[k, 0]));

                // cabecalho
                string temp = string.Format("Temperatura ({0})", ViewBag.UnidadeGrandeza);
                string[] cabecalho = { "Data e Hora", temp };

                // adiciona cabecalho
                rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

                string sem_registros = "---";

                // percorre valores
                for (i = 1; i < 25; i++)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);


                    // data e hora
                    datahoraCelulaXLS(row, 0, ViewBag.DatasN[k, i], _datahoraStyle);

                    // temperatura
                    if (ViewBag.Tempo_Cod[k, i] < 0)
                    {
                        textoCelulaXLS(row, 1, sem_registros);
                    }
                    else
                    {
                        numeroCelulaXLS(row, 1, ViewBag.Temp[k, i], _intCellStyle);
                    }                    

                    // proxima
                    rowIndex++;
                }

                // largura de cada coluna
                for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                    sheet.SetColumnWidth(i, 6000);
            }

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Meteorologia", "Relatório Semanal", _negritoCellStyle);

            // cabecalho
            string[] cab_cons = { "", str_grandeza, "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // maximo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMaxSemana), _intCellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.VMaxSemana_DataHoraN, _datahoraStyle);

            // medio
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Média", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMedSemana), _intCellStyle);

            // minimo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMinSemana), _intCellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.VMinSemana_DataHoraN, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }

        // Meteorologia Mensal
        private void Meteorologia_Mensal(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            // converte data hora 
            DateTime DataRelat = Funcoes_Converte.ConverteDataHora2DateTime(data_hora);

            // lista relatorio
            HistoricoWeatherMetodos weatherMetodos = new HistoricoWeatherMetodos();
            RELAT_METEOROLOGIA_MENSAL relatorio = new RELAT_METEOROLOGIA_MENSAL();
            RELAT_METEOROLOGIA_MENSAL_ANALISE analise = new RELAT_METEOROLOGIA_MENSAL_ANALISE();

            retorno = weatherMetodos.Calc_Metrologia_Mensal(IDMedicao, DataRelat, ref relatorio, ref analise);

            // data atual
            DateTime DataAtual = new DateTime(relatorio.registro[0].DataHora.Year,
                                     relatorio.registro[0].DataHora.Month,
                                     relatorio.registro[0].DataHora.Day,
                                     1, 0, 0);

            int NumDiasMes = relatorio.NumDias;

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var Temp_Max = new double[33];
            var Temp_Med = new double[33];
            var Temp_Min = new double[33];
            var Possui_Registros = new bool[33];
            var Clima_Madrugada = new int[33];
            var Clima_Manha = new int[33];
            var Clima_Tarde = new int[33];
            var Clima_Noite = new int[33];
            var Datas = new string[33];
            var DatasN = new DateTime[33];
            var Dias = new string[33];

            double Valor_min = 0.0;
            double Valor_max = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].DataHora.Year,
                                   (int)relatorio.registro[0].DataHora.Month,
                                   (int)relatorio.registro[0].DataHora.Day,
                                   (int)relatorio.registro[0].DataHora.Hour,
                                   (int)relatorio.registro[0].DataHora.Minute, 0);

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = string.Format("{0:0}", strData.Day);

                // proximo dias
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    Temp_Max[i] = relatorio.registro[0].Temp_Max;
                    Temp_Med[i] = relatorio.registro[0].Temp_Med;
                    Temp_Min[i] = relatorio.registro[0].Temp_Min;
                    Possui_Registros[i] = relatorio.registro[0].Possui_Registros;
                    Clima_Madrugada[i] = relatorio.registro[0].Clima_Madrugada;
                    Clima_Manha[i] = relatorio.registro[0].Clima_Manha;
                    Clima_Tarde[i] = relatorio.registro[0].Clima_Tarde;
                    Clima_Noite[i] = relatorio.registro[0].Clima_Noite;

                    // minimo e maximo
                    Valor_min = Temp_Min[i];
                    Valor_max = Temp_Max[i];
                }

                if (i == (NumDiasMes + 1))
                {
                    // zera
                    Temp_Max[i] = relatorio.registro[NumDiasMes - 1].Temp_Max;
                    Temp_Med[i] = relatorio.registro[NumDiasMes - 1].Temp_Med;
                    Temp_Min[i] = relatorio.registro[NumDiasMes - 1].Temp_Min;
                    Possui_Registros[i] = relatorio.registro[NumDiasMes - 1].Possui_Registros;
                    Clima_Madrugada[i] = relatorio.registro[NumDiasMes - 1].Clima_Madrugada;
                    Clima_Manha[i] = relatorio.registro[NumDiasMes - 1].Clima_Manha;
                    Clima_Tarde[i] = relatorio.registro[NumDiasMes - 1].Clima_Tarde;
                    Clima_Noite[i] = relatorio.registro[NumDiasMes - 1].Clima_Noite;
                }

                if (i >= 1 && i <= NumDiasMes)
                {
                    // copia
                    j = i - 1;

                    // zera
                    Temp_Max[i] = relatorio.registro[j].Temp_Max;
                    Temp_Med[i] = relatorio.registro[j].Temp_Med;
                    Temp_Min[i] = relatorio.registro[j].Temp_Min;
                    Possui_Registros[i] = relatorio.registro[j].Possui_Registros;
                    Clima_Madrugada[i] = relatorio.registro[j].Clima_Madrugada;
                    Clima_Manha[i] = relatorio.registro[j].Clima_Manha;
                    Clima_Tarde[i] = relatorio.registro[j].Clima_Tarde;
                    Clima_Noite[i] = relatorio.registro[j].Clima_Noite;

                    // verifica minimo
                    if (Temp_Max[i] < Valor_min)
                        Valor_min = Temp_Max[i];

                    if (Temp_Med[i] < Valor_min)
                        Valor_min = Temp_Med[i];

                    if (Temp_Min[i] < Valor_min)
                        Valor_min = Temp_Min[i];

                    // verifica maximo
                    if (Temp_Min[i] > Valor_max)
                        Valor_max = Temp_Min[i];

                    if (Temp_Med[i] > Valor_max)
                        Valor_max = Temp_Med[i];

                    if (Temp_Max[i] > Valor_max)
                        Valor_max = Temp_Max[i];
                }
            }

            // valor máximo (10% e arredonda para cima)
            Valor_max = Valor_max + (Math.Abs(Valor_max) * 0.1);
            Valor_max = Math.Ceiling(Valor_max);

            // valor mínimo (10% e arredonda para baixo)
            Valor_min = Valor_min - (Math.Abs(Valor_min) * 0.1);
            Valor_min = Math.Floor(Valor_min);

            // copia valores
            ViewBag.ValorMax = Valor_max;
            ViewBag.ValorMin = Valor_min;

            ViewBag.TempMax = Temp_Max;
            ViewBag.TempMed = Temp_Med;
            ViewBag.TempMin = Temp_Min;
            ViewBag.PossuiRegistros = Possui_Registros;
            ViewBag.ClimaMadrugada = Clima_Madrugada;
            ViewBag.ClimaManha = Clima_Manha;
            ViewBag.ClimaTarde = Clima_Tarde;
            ViewBag.ClimaNoite = Clima_Noite;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.Meteorologia;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Mensal;

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:y}", DataAtual);

            ViewBag.NumDiasMes = NumDiasMes;

            // valores
            ViewBag.VMaxMes = string.Format("{0:0}", analise.Valor_Max);
            DateTime VMaxMes_DataHora = analise.Valor_MaxDataHora;
            if (VMaxMes_DataHora.Year != 2000)
                ViewBag.VMaxMes_DataHora = string.Format("{0:d} {1:HH:mm}", VMaxMes_DataHora, VMaxMes_DataHora);
            else
                ViewBag.VMaxMes_DataHora = "--/--/---- --:--";
            ViewBag.VMaxMes_DataHoraN = VMaxMes_DataHora;

            ViewBag.VMinMes = string.Format("{0:0}", analise.Valor_Min);
            DateTime VMinMes_DataHora = analise.Valor_MinDataHora;
            if (VMinMes_DataHora.Year != 2000)
                ViewBag.VMinMes_DataHora = string.Format("{0:d} {1:HH:mm}", VMinMes_DataHora, VMinMes_DataHora);
            else
                ViewBag.VMinMes_DataHora = "--/--/---- --:--";
            ViewBag.VMinMes_DataHoraN = VMinMes_DataHora;

            ViewBag.VMedMes = string.Format("{0:0}", analise.Valor_Media);

            ViewBag.NomeGrandeza = "Temperatura";
            ViewBag.UnidadeGrandeza = "°C";

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Meteorologia Mensal XLS
        private HSSFWorkbook Meteorologia_Mensal_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // grandeza
            string str_grandeza = string.Format("{0} ({1})", ViewBag.NomeGrandeza, ViewBag.UnidadeGrandeza);

            // cabecalho
            string str_min = string.Format("Mínima ({0})", ViewBag.UnidadeGrandeza);
            string str_med = string.Format("Média ({0})", ViewBag.UnidadeGrandeza);
            string str_max = string.Format("Máxima ({0})", ViewBag.UnidadeGrandeza);
            string[] cabecalho = { "Data e Hora", str_min, str_med, str_max };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            string sem_registros = "---";

            // percorre valores
            for (i = 1; i < (ViewBag.NumDiasMes + 1); i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                if (ViewBag.PossuiRegistros[i] == false)
                {
                    textoCelulaXLS(row, 1, sem_registros);
                    textoCelulaXLS(row, 2, sem_registros);
                    textoCelulaXLS(row, 3, sem_registros);
                }

                else
                {
                    // minimo
                    numeroCelulaXLS(row, 1, ViewBag.TempMin[i], _intCellStyle);

                    // medio
                    numeroCelulaXLS(row, 2, ViewBag.TempMed[i], _intCellStyle);

                    // maximo
                    numeroCelulaXLS(row, 3, ViewBag.TempMax[i], _intCellStyle);
                }

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Meteorologia", "Relatório Mensal", _negritoCellStyle);

            // cabecalho
            string[] cab_cons = { "", str_grandeza, "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // maximo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMaxMes), _intCellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.VMaxMes_DataHoraN, _datahoraStyle);

            // medio
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Média", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMedMes), _intCellStyle);

            // minimo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMinMes), _intCellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.VMinMes_DataHoraN, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }

        // Meteorologia Anual
        private void Meteorologia_Anual(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            // converte data hora 
            DateTime DataRelat = Funcoes_Converte.ConverteDataHora2DateTime(data_hora);

            // lista relatorio
            HistoricoWeatherMetodos weatherMetodos = new HistoricoWeatherMetodos();
            RELAT_METEOROLOGIA_ANUAL relatorio = new RELAT_METEOROLOGIA_ANUAL();
            RELAT_METEOROLOGIA_ANUAL_ANALISE analise = new RELAT_METEOROLOGIA_ANUAL_ANALISE();

            retorno = weatherMetodos.Calc_Metrologia_Anual(IDMedicao, DataRelat, ref relatorio, ref analise);

            // data atual
            DateTime DataAtual = new DateTime(relatorio.registro[0].DataHora.Year,
                                     relatorio.registro[0].DataHora.Month,
                                     relatorio.registro[0].DataHora.Day,
                                     1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var Temp_Max = new double[14];
            var Temp_Med = new double[14];
            var Temp_Min = new double[14];
            var Possui_Registros = new bool[14];
            var Datas = new string[14];
            var DatasN = new DateTime[14];
            var Meses = new string[14];

            double Valor_min = 0.0;
            double Valor_max = 0.0;

            // valores
            DateTime strData = new DateTime(relatorio.registro[0].DataHora.Year, relatorio.registro[0].DataHora.Month, relatorio.registro[0].DataHora.Day, relatorio.registro[0].DataHora.Hour, relatorio.registro[0].DataHora.Minute, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddMonths(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 14; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Meses[i] = string.Format("{0:MMMM}", strData);

                // proximo mes
                strData = strData.AddMonths(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    Temp_Max[i] = relatorio.registro[0].Temp_Max;
                    Temp_Med[i] = relatorio.registro[0].Temp_Med;
                    Temp_Min[i] = relatorio.registro[0].Temp_Min;
                    Possui_Registros[i] = relatorio.registro[0].Possui_Registros;

                    // minimo e maximo
                    Valor_min = Temp_Min[i];
                    Valor_max = Temp_Max[i];
                }

                if (i == 13)
                {
                    // zera
                    Temp_Max[i] = relatorio.registro[11].Temp_Max;
                    Temp_Med[i] = relatorio.registro[11].Temp_Med;
                    Temp_Min[i] = relatorio.registro[11].Temp_Min;
                    Possui_Registros[i] = relatorio.registro[11].Possui_Registros;
                }

                if (i >= 1 && i <= 12)
                {
                    // copia
                    j = i - 1;

                    Temp_Max[i] = relatorio.registro[j].Temp_Max;
                    Temp_Med[i] = relatorio.registro[j].Temp_Med;
                    Temp_Min[i] = relatorio.registro[j].Temp_Min;
                    Possui_Registros[i] = relatorio.registro[j].Possui_Registros;

                    // verifica minimo
                    if (Temp_Max[i] < Valor_min)
                        Valor_min = Temp_Max[i];

                    if (Temp_Med[i] < Valor_min)
                        Valor_min = Temp_Med[i];

                    if (Temp_Min[i] < Valor_min)
                        Valor_min = Temp_Min[i];

                    // verifica maximo
                    if (Temp_Min[i] > Valor_max)
                        Valor_max = Temp_Min[i];

                    if (Temp_Med[i] > Valor_max)
                        Valor_max = Temp_Med[i];

                    if (Temp_Max[i] > Valor_max)
                        Valor_max = Temp_Max[i];
                }
            }

            // valor máximo (10% e arredonda para cima)
            Valor_max = Valor_max + (Math.Abs(Valor_max) * 0.1);
            Valor_max = Math.Ceiling(Valor_max);

            // valor mínimo (10% e arredonda para baixo)
            Valor_min = Valor_min - (Math.Abs(Valor_min) * 0.1);
            Valor_min = Math.Floor(Valor_min);

            // copia valores
            ViewBag.ValorMax = Valor_max;
            ViewBag.ValorMin = Valor_min;

            ViewBag.TempMax = Temp_Max;
            ViewBag.TempMed = Temp_Med;
            ViewBag.TempMin = Temp_Min;
            ViewBag.PossuiRegistros = Possui_Registros;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Meses = Meses;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.Meteorologia;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Anual;

            // data atual
            ViewBag.DataAtual = string.Format("{0:yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:yyyy}", DataAtual);

            // valores
            ViewBag.VMaxAno = string.Format("{0:0}", analise.Valor_Max);
            DateTime VMaxAno_DataHora = analise.Valor_MaxDataHora;
            if (VMaxAno_DataHora.Year != 2000)
                ViewBag.VMaxAno_DataHora = string.Format("{0:d} {1:HH:mm}", VMaxAno_DataHora, VMaxAno_DataHora);
            else
                ViewBag.VMaxAno_DataHora = "--/--/---- --:--";
            ViewBag.VMaxAno_DataHoraN = VMaxAno_DataHora;

            ViewBag.VMinAno = string.Format("{0:0}", analise.Valor_Min);
            DateTime VMinAno_DataHora = analise.Valor_MinDataHora;
            if (VMinAno_DataHora.Year != 2000)
                ViewBag.VMinAno_DataHora = string.Format("{0:d} {1:HH:mm}", VMinAno_DataHora, VMinAno_DataHora);
            else
                ViewBag.VMinAno_DataHora = "--/--/---- --:--";
            ViewBag.VMinAno_DataHoraN = VMinAno_DataHora;

            ViewBag.VMedAno = string.Format("{0:0}", analise.Valor_Media);

            ViewBag.NomeGrandeza = "Temperatura";
            ViewBag.UnidadeGrandeza = "°C";

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddYears(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Meteorologia Anual XLS
        private HSSFWorkbook Meteorologia_Anual_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // grandeza
            string str_grandeza = string.Format("{0} ({1})", ViewBag.NomeGrandeza, ViewBag.UnidadeGrandeza);

            // cabecalho
            string str_min = string.Format("Mínima ({0})", ViewBag.UnidadeGrandeza);
            string str_med = string.Format("Média ({0})", ViewBag.UnidadeGrandeza);
            string str_max = string.Format("Máxima ({0})", ViewBag.UnidadeGrandeza);
            string[] cabecalho = { "Data e Hora", str_min, str_med, str_max };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            string sem_registros = "---";

            // percorre valores
            for (i = 1; i < 13; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                if (ViewBag.PossuiRegistros[i] == false)
                {
                    textoCelulaXLS(row, 1, sem_registros);
                    textoCelulaXLS(row, 2, sem_registros);
                    textoCelulaXLS(row, 3, sem_registros);
                }
                else
                {
                    // minimo
                    numeroCelulaXLS(row, 1, ViewBag.TempMin[i], _intCellStyle);

                    // medio
                    numeroCelulaXLS(row, 2, ViewBag.TempMed[i], _intCellStyle);

                    // maximo
                    numeroCelulaXLS(row, 3, ViewBag.TempMax[i], _intCellStyle);
                }

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Meteorologia", "Relatório Anual", _negritoCellStyle);

            // cabecalho
            string[] cab_cons = { "", str_grandeza, "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // maximo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMaxAno), _intCellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.VMaxAno_DataHoraN, _datahoraStyle);

            // medio
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Média", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMedAno), _intCellStyle);

            // minimo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.VMinAno), _intCellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.VMinAno_DataHoraN, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }

        // Meteorologia exportar XLS
        private HSSFWorkbook Meteorologia_exportar_XLS(int IDCliente, int IDMedicao, DateTime DataIni, DateTime DataFim)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // datas
            ViewBag.DataAtualN = DataIni;
            ViewBag.DataFinalN = DataFim;

            // medicao
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);

            // calcula
            HistoricoWeatherMetodos weatherMetodos = new HistoricoWeatherMetodos();

            // Lista registros
            List<HistoricoWeatherDominio> registros = weatherMetodos.ListarPeriodo(medicao.IDCidade, DataIni, DataFim);

            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Temperatura (°C)", "Umidade (%)", "Pressão (hPa)", "Velocidade do Vento (Km/h)", "Direção do Vento", "Clima" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            if (registros != null)
            {
                // percorre valores
                foreach (HistoricoWeatherDominio registro in registros)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // data e hora
                    datahoraCelulaXLS(row, 0, registro.DataHora, _datahoraStyle);

                    // temperatura
                    numeroCelulaXLS(row, 1, registro.Temp_Atual, _intCellStyle);

                    // umidade
                    numeroCelulaXLS(row, 2, registro.Umidade, _intCellStyle);

                    // pressao
                    numeroCelulaXLS(row, 3, registro.Pressao, _intCellStyle);

                    // velocidade vento
                    numeroCelulaXLS(row, 4, registro.Vento_Velocidade, _intCellStyle);

                    // direcao vento
                    string[] nome_Direcao = { "Norte", "Norte - Nordeste", "Nordeste", "Este - Nordeste", "Leste", "Este - Sudeste", "Sudeste", "Sul - Sudeste", "Sul", "Sul - Sudoeste", "Sudoeste", "Oeste - Sudoeste", "Oeste", "Oeste - Noroeste", "Noroeste", "Norte - Noroeste" };
                    if (registro.Vento_Direcao >= 0)
                        textoCelulaXLS(row, 5, nome_Direcao[registro.Vento_Direcao]);
                    else
                        textoCelulaXLS(row, 5, "-");

                    // clima
                    string[] nome_Clima = { "Céu Claro", "Noite Clara", "Parcialmente Nublado", "Parcialmente Nublado", "Nublado", "Chuvoso", "Granizo", "Neve", "Vento", "Nevoeiro" };
                    if (registro.Vento_Direcao >= 0)
                        textoCelulaXLS(row, 6, nome_Clima[registro.Tempo_Cod]);
                    else
                        textoCelulaXLS(row, 6, "-");

                    // proxima
                    rowIndex++;
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            sheet.SetColumnWidth(6, 8000);


            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Meteorologia", "", _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(9).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }
    }
}
