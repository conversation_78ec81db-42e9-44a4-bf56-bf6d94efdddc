﻿using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class AnalisesController
    {
        
        // GET: Distribuição de Consumo GruposMed - Excluir
        public ActionResult DistribuicaoConsumoGruposMed_Excluir(int IDDistribuicaoConsumoGruposMed)
        {
            // apaga a distribuição GruposMed
            DistribuicaoConsumoGruposMedMetodos distribuicaoGruposMetodos = new DistribuicaoConsumoGruposMedMetodos();
            distribuicaoGruposMetodos.Excluir(IDDistribuicaoConsumoGruposMed);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}
