﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {
        //
        // DEMANDA REATIVA
        //

        // GET: Relatorio Demanda Reativa
        public ActionResult Relat_Dem_Reativa(int IDCliente, int IDMedicao, int tipo_arquivo = 0)
        {
            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Relat_Dem_Reativa");

            // relatorio demanda reativa
            return (Relat_Grafico_Show(IDCliente, IDMedicao, 1, tipo_arquivo));
        }

        // Demanda Reativa Diario XLS
        private HSSFWorkbook Dem_Reativa_Diario_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = {"Data e Hora","Período","Demanda Ativa","Demanda Reativa","Fator de Potência"};
    
            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);
    
            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 97; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _datahoraStyle);

                // periodo
                numeroCelulaXLS(row, 1, ViewBag.Periodo[i], _intCellStyle);

                // demanda ativa
                numeroCelulaXLS(row, 2, ViewBag.DemandaAtv[i], _1CellStyle);

                // demanda reativa
                numeroCelulaXLS(row, 3, ViewBag.DemandaRtv[i], _1CellStyle);

                // fator de potencia
                numeroCelulaXLS(row, 4, ViewBag.FatPot[i], _3CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Demanda Reativa", "Relatório Diário", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Demanda", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN, _datahoraStyle);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Média", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP), _1CellStyle);

            // demanda minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MinFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MinFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MinFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MinFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MinP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MinP_DataHoraN, _datahoraStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // cabecalho capacitivo e indutivo
            string[] cab_fatpot = { "Fator de Potência", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_fatpot, rowIndex++);

            // fatpot mais capacitivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Capacitivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisCapFPC), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisCapFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisCapFPI), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisCapFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisCapP), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisCapP_DataHoraN, _datahoraStyle);

            // fatpot mais indutivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Indutivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisIndFPC), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisIndFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisIndFPI), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisIndFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisIndP), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisIndP_DataHoraN, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }


        // Demanda Reativa Diario XLS simulacao
        private HSSFWorkbook Dem_Reativa_Diario_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho simulacao
            string[] simulacao = { "Relatório de Demanda Reativa - Simulação" };

            // simulação 
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas do titulo simulacao
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 4));

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Período", "Demanda Ativa", "Demanda Reativa", "Fator de Potência" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 97; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[i], _datahoraStyle);

                // periodo
                numeroCelulaXLS(row, 1, ViewBag.Periodo_Sim[i], _intCellStyle);

                // demanda ativa
                numeroCelulaXLS(row, 2, ViewBag.DemandaAtv_Sim[i], _1CellStyle);

                // demanda reativa
                numeroCelulaXLS(row, 3, ViewBag.DemandaRtv_Sim[i], _1CellStyle);

                // fator de potencia
                numeroCelulaXLS(row, 4, ViewBag.FatPot_Sim[i], _3CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Demanda Reativa - Simulação", "Relatório Diário", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Demanda", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN_Sim, _datahoraStyle);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Média", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP_Sim), _1CellStyle);

            // demanda minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MinFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MinFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MinFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MinFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MinP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MinP_DataHoraN_Sim, _datahoraStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // cabecalho capacitivo e indutivo
            string[] cab_fatpot = { "Fator de Potência", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_fatpot, rowIndex++);

            // fatpot mais capacitivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Capacitivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisCapFPC_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisCapFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisCapFPI_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisCapFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisCapP_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisCapP_DataHoraN_Sim, _datahoraStyle);

            // fatpot mais indutivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Indutivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisIndFPC_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisIndFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisIndFPI_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisIndFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisIndP_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisIndP_DataHoraN_Sim, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Demanda Reativa Semanal XLS
        private HSSFWorkbook Dem_Reativa_Semanal_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // dias da semana
            int k = 0;
            int i;
            IRow row;
            ISheet sheet;
            int rowIndex;

            for (k = 0; k < 7; k++)
            {
                // cria planilha
                sheet = workbook.CreateSheet(string.Format("{0:dddd}", ViewBag.DatasN[k, 0]));

                // cabecalho
                string[] cabecalho = { "Data e Hora", "Período", "Demanda Reativa" };

                // adiciona cabecalho
                rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 0);

                // percorre valores
                for (i = 1; i < 97; i++)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // data e hora
                    datahoraCelulaXLS(row, 0, ViewBag.DatasN[k, i], _datahoraStyle);

                    // periodo
                    numeroCelulaXLS(row, 1, ViewBag.Periodo[k, i], _intCellStyle);

                    // demanda reativa
                    numeroCelulaXLS(row, 2, ViewBag.Demanda[k, i], _1CellStyle);

                    // proxima
                    rowIndex++;
                }

                // largura de cada coluna
                for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                    sheet.SetColumnWidth(i, 6000);
            }

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Demanda Reativa", "Relatório Semanal", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Demanda", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN, _datahoraStyle);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Média", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP), _1CellStyle);

            // demanda minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MinFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MinFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MinFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MinFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MinP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MinP_DataHoraN, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Demanda Reativa Semanal XLS Simulacao
        private HSSFWorkbook Dem_Reativa_Semanal_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // dias da semana
            int k = 0;
            int i;
            IRow row;
            ISheet sheet;
            int rowIndex;

            for (k = 0; k < 7; k++)
            {
                // cria planilha
                sheet = workbook.CreateSheet(string.Format("{0:dddd}", ViewBag.DatasN[k, 0]));

                // cabecalho simulacao
                string[] simulacao = { "Relatório de Demanda Reativa - Simulação" };

                // adiciona cabecalho
                rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

                // mescla celulas do titulo simulacao
                sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 2));

                // cabecalho
                string[] cabecalho = { "Data e Hora", "Período", "Demanda Reativa" };

                // adiciona cabecalho
                rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

                // percorre valores
                for (i = 1; i < 97; i++)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // data e hora
                    datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[k, i], _datahoraStyle);

                    // periodo
                    numeroCelulaXLS(row, 1, ViewBag.Periodo_Sim[k, i], _intCellStyle);

                    // demanda reativa
                    numeroCelulaXLS(row, 2, ViewBag.Demanda_Sim[k, i], _1CellStyle);

                    // proxima
                    rowIndex++;
                }

                // largura de cada coluna
                for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                    sheet.SetColumnWidth(i, 6000);
            }

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Demanda Reativa - Simulação", "Relatório Semanal", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Demanda", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN_Sim, _datahoraStyle);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Média", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP_Sim), _1CellStyle);

            // demanda minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MinFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MinFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MinFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MinFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MinP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MinP_DataHoraN_Sim, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Demanda Reativa Mensal XLS
        private HSSFWorkbook Dem_Reativa_Mensal_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < (ViewBag.NumDiasMes + 1); i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // demanda fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.DemandaFPC[i], _1CellStyle);

                // demanda fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.DemandaFPI[i], _1CellStyle);

                // demanda ponta
                numeroCelulaXLS(row, 3, ViewBag.DemandaP[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Demanda Reativa", "Relatório Mensal", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Demanda", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN, _datahoraStyle);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Média", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP), _1CellStyle);

            // demanda minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MinFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MinFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MinFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MinFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MinP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MinP_DataHoraN, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Demanda Reativa Mensal XLS Simulacao
        private HSSFWorkbook Dem_Reativa_Mensal_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho simulacao
            string[] simulacao = { "Relatório de Demanda Reativa - Simulação" };

            // simulação 
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas do titulo simulacao
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 3));

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < (ViewBag.NumDiasMes + 1); i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[i], _dataStyle);

                // demanda fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.DemandaFPC_Sim[i], _1CellStyle);

                // demanda fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.DemandaFPI_Sim[i], _1CellStyle);

                // demanda ponta
                numeroCelulaXLS(row, 3, ViewBag.DemandaP_Sim[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Demanda Reativa - Simulação", "Relatório Mensal", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Demanda", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN_Sim, _datahoraStyle);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Média", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP_Sim), _1CellStyle);

            // demanda minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MinFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MinFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MinFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MinFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MinP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MinP_DataHoraN_Sim, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Demanda Reativa Anual XLS
        private HSSFWorkbook Dem_Reativa_Anual_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 13; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // demanda fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.DemandaFPC[i], _1CellStyle);

                // demanda fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.DemandaFPI[i], _1CellStyle);

                // demanda ponta
                numeroCelulaXLS(row, 3, ViewBag.DemandaP[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Demanda Reativa", "Relatório Anual", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Demanda", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN, _datahoraStyle);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Média", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP), _1CellStyle);

            // demanda minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MinFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MinFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MinFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MinFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MinP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MinP_DataHoraN, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Demanda Reativa Anual XLS Simulacao
        private HSSFWorkbook Dem_Reativa_Anual_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho simulacao
            string[] simulacao = { "Relatório de Demanda Reativa - Simulação" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas do titulo simulacao
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 3));

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 13; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[i], _dataStyle);

                // demanda fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.DemandaFPC_Sim[i], _1CellStyle);

                // demanda fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.DemandaFPI_Sim[i], _1CellStyle);

                // demanda ponta
                numeroCelulaXLS(row, 3, ViewBag.DemandaP_Sim[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Demanda Reativa - Simulação", "Relatório Anual", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Demanda", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN_Sim, _datahoraStyle);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Média", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP_Sim), _1CellStyle);

            // demanda minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MinFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MinFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MinFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MinFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MinP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MinP_DataHoraN_Sim, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }
    }
}