﻿using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {

        //
        // CONTROLE DE SAZONALIDADE
        //

        // GET: Relatorio Controle Sazonalidade
        public ActionResult Relat_ControleSazonalidade(int IDCliente, int IDMedicao)
        {
            // tipo do relatorio
            int TipoRelat = TIPO_RELAT.ControleSazonalidade;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Contato");

            // salva cookie 
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("Relat_Tipo", TipoRelat);

            // le cookies
            LeCookies_SmartEnergy();

            // mes atual
            DateTime datahora_inicio = DateTime.Now;

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_inicio);

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", datahora_inicio);


            // tipo do relatorio
            ViewBag.TipoRelat = TipoRelat;

            // seta para mensal
            ViewBag.Relat_TipoPeriodo = TIPO_RELAT_PERIODO.Mensal;

            // valores
            CONTROLE_SAZONALIDADE controle_sazonalidade = new CONTROLE_SAZONALIDADE();
            ViewBag.controle_sazonalidade = controle_sazonalidade;


            // lista de erros
            var listaErros = new List<string>();

            // erros
            ViewBag.listaErros = listaErros;

            return View();
        }


        // POST: Controle Sazonalidade EMAIL
        [HttpPost]
        public async Task<ActionResult> Relat_ControleSazonalidade_EMAIL(string destino, string assunto, List<CONTROLE_SAZONALIDADE_CONSUMO_EDITADO> consumos_editados)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // calcula
            Relat_ControleSazonalidade_Show(IDCliente, IDMedicao, consumos_editados);


            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Relat_ControleSazonalidade_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDMedicao, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_ControleSazonalidade_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Landscape,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(5, 5, 5, 5),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "RelatEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.NomeRelat", ViewBag.NomeRelat);
            message = message.Replace("ViewBag.PeriodoRelat", ViewBag.PeriodoRelat);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.GrupoNome", ViewBag.GrupoNome);
            message = message.Replace("ViewBag.UnidadeNome", ViewBag.UnidadeNome);
            message = message.Replace("ViewBag.MedicaoNome", ViewBag.MedicaoNome);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // POST: Controle de Sazonalidade PDF
        [HttpPost]
        public ActionResult Relat_ControleSazonalidade_PDF(List<CONTROLE_SAZONALIDADE_CONSUMO_EDITADO> consumos_editados)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // calcula
            Relat_ControleSazonalidade_Show(IDCliente, IDMedicao, consumos_editados);


            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Relat_ControleSazonalidade_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDMedicao, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_ControleSazonalidade_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Landscape,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(5, 5, 5, 5),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        // POST: Controle de Sazonalidade Atualizar
        [HttpPost]
        public PartialViewResult _ControleSazonalidade_Atualizar(int Navegacao, string Data, List<CONTROLE_SAZONALIDADE_CONSUMO_EDITADO> consumos_editados = null)
        {
            // caso forcar data
            if (Navegacao == 100)
            {
                // pega data
                DateTime dateValue = DateTime.Parse(Data);
                DateTime dateValueFim = new DateTime();

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Relat_Data", dateValue);

                // faco sempre o periodo de 1 dia
                dateValueFim = dateValue.AddDays(1);

                // salva cookie datahora (final)
                CookieStore.SalvaCookie_Datahora("Relat_DataFim", dateValueFim);
            }

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;
            int IDGateway = ViewBag._IDGateway;
            int IDEmpresa = ViewBag._IDEmpresa;
            int IDTipoGateway = ViewBag._IDTipoGateway;
            int TipoPeriodo = ViewBag.Relat_TipoPeriodo;

            // tipo de acesso do usuario
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // unidade de potencia
            UnidadePotencia(IDMedicao);

            // le cookie datahora
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");
            DATAHORA data_hora = new DATAHORA();

            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Relat_DataFim");
            DATAHORA data_hora_fim = new DATAHORA();

            // verifica se fim eh menor que inicio
            if (datahora_cookie_fim < datahora_cookie)
            {
                // periodo de pelo menos 1 dia
                datahora_cookie_fim = datahora_cookie.AddDays(1);
            }

            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -3:    // mes anterior

                    datahora_cookie = datahora_cookie.AddMonths(-1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(-1);
                    break;

                case 3:    // mes seguinte

                    datahora_cookie = datahora_cookie.AddMonths(1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(1);
                    break;

                case -4:    // ano anterior

                    datahora_cookie = datahora_cookie.AddYears(-1);
                    datahora_cookie_fim = datahora_cookie_fim.AddYears(-1);
                    break;

                case 4:    // ano seguinte

                    datahora_cookie = datahora_cookie.AddYears(1);
                    datahora_cookie_fim = datahora_cookie_fim.AddYears(1);
                    break;

                case 10:    // ultimo

                    // le supervisao da medicao
                    var medicoesMetodos = new SupervMedicoesMetodos();
                    var listaMedicoes = medicoesMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

                    // dia atual
                    datahora_cookie = DateTime.Parse(listaMedicoes.DataHora);

                    // periodo de 1 dia
                    datahora_cookie_fim = datahora_cookie.AddDays(1);

                    break;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_cookie);

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_cookie);

            // salva cookie datahora (final)
            CookieStore.SalvaCookie_Datahora("Relat_DataFim", datahora_cookie_fim);

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora_fim, datahora_cookie_fim);

            // tipo do relatorio
            ViewBag.TipoRelat = TIPO_RELAT.ControleSazonalidade;

            // tela de ajuda - relatorios controle sazonalidade
            string PaginaAjuda = "Contato";

            CookieStore.SalvaCookie_String("PaginaAjuda", PaginaAjuda);
            ViewBag.PaginaAjuda = PaginaAjuda;


            // Controle de Sazonalidade
            Calc_ControleSazonalidade(IDMedicao, datahora_cookie, consumos_editados);


            return PartialView();
        }

        // GET: Controle de Sazonalidade Show 
        private ActionResult Relat_ControleSazonalidade_Show(int IDCliente, int IDMedicao, List<CONTROLE_SAZONALIDADE_CONSUMO_EDITADO> consumos_editados = null)
        {
            // tipo do relatorio
            int TipoRelat = TIPO_RELAT.ControleSazonalidade;

            // le supervisao da medicao
            SupervMedicoesMetodos medicoesMetodos = new SupervMedicoesMetodos();
            SupervMedicoesDominio listaMedicoes = medicoesMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

            // salva cookie 
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", listaMedicoes.IDTipoMedicao_MD);
            CookieStore.SalvaCookie_String("MedicaoNome", listaMedicoes.NMED);
            CookieStore.SalvaCookie_String("UnidadeNome", listaMedicoes.NUN);
            CookieStore.SalvaCookie_String("GrupoNome", listaMedicoes.NGU);
            CookieStore.SalvaCookie_Int("_IDGateway", listaMedicoes.IDGW_GW);
            CookieStore.SalvaCookie_Int("IDTipoGateway", listaMedicoes.IDTipoGateway_GW);
            CookieStore.SalvaCookie_Int("Relat_Tipo", TipoRelat);

            int IDGateway = listaMedicoes.IDGW_GW;
            int IDTipoGateway = listaMedicoes.IDTipoGateway_GW;

            // le cookies
            LeCookies_SmartEnergy();

            // data atual
            DateTime datahora_primeira = DateTime.Now;

            // le cookie datahora (atual ou inicial)
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            // verifica se existe datahora no cookie
            if (datahora_cookie.Year != 2000)
            {
                // copia data do cookie para ultima
                datahora_primeira = datahora_cookie;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_primeira);


            // calcula
            Calc_ControleSazonalidade(IDMedicao, datahora_primeira, consumos_editados);


            return View();
        }

        // Calcula Controle Sazonalidade
        private void Calc_ControleSazonalidade(int IDMedicao, DateTime DataAtual, List<CONTROLE_SAZONALIDADE_CONSUMO_EDITADO> consumos_editados = null)
        {
            // funcao relatorio
            int retorno;

            // le cookies
            LeCookies_SmartEnergy();

            // simulação
            int IDSimulacaoCenario = 0;

            // le medicoes
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // estruturas
            CONTROLE_SAZONALIDADE controle_sazonalidade = new CONTROLE_SAZONALIDADE();

            // data de hoje
            DateTime DataHoje = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 0, 0);

            // data atual (data da solicitação do relatório)
            // Ex. DataAtual (solicitada) = 26/Março/2021 torna DataAtual = Março/2021 (dia não é usado)
            controle_sazonalidade.DataAtual = new DateTime(DataAtual.Year, DataAtual.Month, 1, 0, 0, 0);

            // data inicial (data inicial do relatório - 12 meses)
            // Ex. DataInicial = Março/2020 (dia não é usado)
            controle_sazonalidade.DataInicial = controle_sazonalidade.DataAtual.AddYears(-1);

            // data final (data final do relatório - 12 meses)
            // Ex. DataFinal = Fevereiro/2021 (dia não é usado)
            controle_sazonalidade.DataFinal = controle_sazonalidade.DataAtual.AddMonths(-1);

            // lista de erros
            var listaErros = new List<string>();

            // percorre medicoes
            if (medicao != null)
            {
                // verifica se nao eh energia
                if( medicao.IDTipoMedicao == TIPO_MEDICAO.ENERGIA || medicao.IDTipoMedicao == TIPO_MEDICAO.ENERGIA_FORMULA )
                {
                    //
                    // Sincroniza datas do relatório caso a medição contemple o benefício
                    //

                    // verifica se medição contempla benefício
                    if (medicao.IDTipoSazonalidade > 0)
                    {
                        // como medição contempla benefício, sincronizamos data atual para o mês configurado do início da vigência da sazonalidade

                        // data atual (data da solicitação do relatório)
                        // Ex. DataAtual (solicitada) = 26/Março/2021 torna DataAtual = Março/2021 (dia não é usado)
                        controle_sazonalidade.DataAtual = new DateTime(DataAtual.Year, DataAtual.Month, 1, 0, 0, 0);

                        // data inicial (data inicial do relatório - 12 meses)
                        // Ex. vigência é Maio - IDTipoSazonalidade contém o número do mês (1 a 12)
                        // Ex. DataInicial = Maio/2020 (dia não é usado)
                        controle_sazonalidade.DataInicial = new DateTime(DataAtual.Year, medicao.IDTipoSazonalidade, 1, 0, 0, 0);

                        // verifica se a data inicial está depois da data atual
                        if (controle_sazonalidade.DataInicial > controle_sazonalidade.DataAtual)
                        {
                            // data inicial esta depois da data atual, então tiro 1 ano
                            controle_sazonalidade.DataInicial = controle_sazonalidade.DataInicial.AddYears(-1);
                        }

                        // data final (data final do relatório - 12 meses)
                        // Ex. DataFina = Abril/2021 (dia não é usado)
                        controle_sazonalidade.DataFinal = controle_sazonalidade.DataInicial.AddMonths(11);
                    }

                    //
                    // Calcula consumos/demanda/excedente reativo
                    //

                    // preenche solicitacao
                    CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
                    config_interface.sweb.id_cliente = medicao.IDCliente;
                    config_interface.sweb.id_gateway = medicao.IDGateway;
                    config_interface.sweb.id_medicao = medicao.IDMedicao;

                    // data inicial e final do primeiro mês
                    DateTime datahora_ini = new DateTime(controle_sazonalidade.DataInicial.Year, controle_sazonalidade.DataInicial.Month, 1, 0, 0, 0);
                    DateTime datahora_fim = datahora_ini.AddMonths(1);

                    // percorre meses e calcula a fatura
                    int i = 0;
                    for (i = 0; i < 12; i++)
                    {
                        // data inicial e final do mês atual
                        DATAHORA dh_ini = new DATAHORA();
                        DATAHORA dh_fim = new DATAHORA();

                        // converte para DateHora
                        Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, datahora_ini);
                        Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, datahora_fim);

                        // resultado mês
                        CONTROLE_SAZONALIDADE_MES resultado_mes = new CONTROLE_SAZONALIDADE_MES();

                        // fatura
                        RESULT_ENERGIA_FATURA faturaDLL = new RESULT_ENERGIA_FATURA();
                        RESULT_ENERGIA_FATURA faturaDLL_sim = new RESULT_ENERGIA_FATURA();

                        // calcula valores fatura (sem simulação)
                        // -2 - erro: tipo de interface indefinida
	                    // -1 - erro: estrutura tarifaria inexistente
	                    //  0 - OK e com dados
	                    //  1 - erro: nao foi possivel abrir banco de dados
	                    //  2 - erro: nao existem dados
	                    //  3 - erro: intervalo menos de 1 dia
	                    //  4 - erro: leitura da configuracao da medicao
	                    //  5 - erro: leitura da configuracao da gateway
	                    //  6 - erro: leitura do historico de contrato de demanda
	                    //  7 - erro: tarifas THS
	                    //  8 - erro: tarifa pis/cofins
	                    //  9 - erro: nao existem bandeiras tarifarias
                        retorno = SmCalcDB_Energia_Fatura((char)0, ref config_interface, IDSimulacaoCenario, (char)medicao.IDContratoMedicao, (char)medicao.IDEstruturaTarifaria, (char)0, ref dh_ini, ref dh_fim, ref faturaDLL, ref faturaDLL_sim);

                        // verifica se é o mês de hoje
                        if (datahora_ini.Month == DataHoje.Month && datahora_ini.Year == DataHoje.Year)
                        {
                            // caso mês de hoje, calculo projetada (sem simulação)
                            SmCalcDB_Energia_Fatura((char)0, ref config_interface, IDSimulacaoCenario, (char)medicao.IDContratoMedicao, (char)medicao.IDEstruturaTarifaria, (char)1, ref dh_ini, ref dh_fim, ref faturaDLL, ref faturaDLL_sim);

                            // 10 - aviso: mês de hoje
                            retorno = 10;
                        }

                        // data
                        resultado_mes.Data = datahora_ini;
                        resultado_mes.retorno = retorno;

                        // consumos
                        resultado_mes.ConsumoP = faturaDLL.consumo_p_tusd[CF.REG];
                        resultado_mes.ConsumoFP = faturaDLL.consumo_fpi_tusd[CF.REG] + faturaDLL.consumo_fpc_tusd[CF.REG];
                        resultado_mes.ConsumoTotal = faturaDLL.consumo_total_tusd[CF.REG];

                        if (resultado_mes.ConsumoTotal > 0.0)
                        {
                            resultado_mes.PorcentagemConsumoP = (resultado_mes.ConsumoP / resultado_mes.ConsumoTotal) * 100.0;
                        }
                        else
                        {
                            resultado_mes.PorcentagemConsumoP = 0.0;
                        }

                        // demanda contrato (utilizo ponta)
                        resultado_mes.Dem_Contrato = faturaDLL.contrato_dem_p;

                        // demanda máxima
                        if ((faturaDLL.demanda_fpi[CF.REG] > faturaDLL.demanda_fpc[CF.REG]) && (faturaDLL.demanda_fpi[CF.REG] > faturaDLL.demanda_p[CF.REG]))
                        {
                            resultado_mes.Dem_Max = faturaDLL.demanda_fpi[CF.REG];
                        }
                        else
                        {
                            if (faturaDLL.demanda_fpc[CF.REG] > faturaDLL.demanda_p[CF.REG])
                            {
                                resultado_mes.Dem_Max = faturaDLL.demanda_fpc[CF.REG];
                            }
                            else
                            {
                                resultado_mes.Dem_Max = faturaDLL.demanda_p[CF.REG];
                            }
                        }

                        // multa excedente reativo ponta (R$) - FER e FDR
                        resultado_mes.Multa_UFER_P = faturaDLL.fer_p[CF.TOT] + faturaDLL.fdr_p[CF.TOT];

                        // multa excedente reativo fora de ponta (R$) - FER e FDR
                        resultado_mes.Multa_UFER_FP = faturaDLL.fer_fpi[CF.TOT] + faturaDLL.fer_fpc[CF.TOT] + faturaDLL.fdr_fp[CF.TOT];

                        // coloca na estrutura
                        controle_sazonalidade.Meses.Add(resultado_mes);

                        // próximo mês
                        datahora_ini = datahora_ini.AddMonths(1);
                        datahora_fim = datahora_ini.AddMonths(1);
                    }

                    //
                    // Cálculo da sazonalidade
                    //
                    CalculoSazonalidade(controle_sazonalidade.Meses, ref controle_sazonalidade.calculo, medicao.IDTipoSazonalidade);

                    //
                    // Análise para sugestão de consumos para atingir 20%
                    //
                    AnaliseSugestaoConsumo(IDMedicao, ref controle_sazonalidade, consumos_editados);
                }
            }

            // valores
            ViewBag.controle_sazonalidade = controle_sazonalidade;

            // erros
            ViewBag.listaErros = listaErros;

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:MMM/yyyy} - {1:MMM/yyyy}", controle_sazonalidade.DataInicial, controle_sazonalidade.DataFinal);


            // tipo do relatorio
            ViewBag.TipoRelat = 64;

            // seta para mensal
            ViewBag.Relat_TipoPeriodo = 2;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.MenuTexts.MenuLateralControleSazonalidade;
            ViewBag.PeriodoRelat = "";

            return;
        }


        // análise sugestão para atingir 20%
        public void AnaliseSugestaoConsumo(int IDMedicao, ref CONTROLE_SAZONALIDADE controle_sazonalidade, List<CONTROLE_SAZONALIDADE_CONSUMO_EDITADO> consumos_editados)
        {

            //
            // Análise para sugestão de consumos para atingir 20%
            //

            // teste
            LogMessage("--------------------------------------------------------------------------");
            LogMessage(string.Format("Inicia Analise - IDMedicao [{0:000000}]", IDMedicao));

            // data de hoje
            DateTime DataHoje = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 0, 0);

            // verifica se tem meses não consolidados e precisam de sugestão para atingir o benefício
            if (controle_sazonalidade.DataFinal < DataHoje)
            {
                // Não existem meses futuros para sugerir consumo
                controle_sazonalidade.calculo.Sugestao = 0.0;
                controle_sazonalidade.calculo.DentroBeneficio_Sugestao = false;
                controle_sazonalidade.calculo.PorcentagemBeneficio_Sugestao = 0.0;
                controle_sazonalidade.calculo.Status_Sugestao = 1;


                // teste
                LogMessage("não tem meses para sugerir consumo (todos consolidados)");

                // não tem meses para sugerir consumo (todos consolidados)
                return;
            }

            // 
            // Metodologia
            //
            // 1) Adoto como sugestão o maior consumo e sugestão anterior ZERO.
            // 2) Preencho os ConsumoTotal dos meses de hoje e próximos com a sugestão ou consumos editados pelo usuário.
            // 3) Faço o calculo da sazonalidade.
            // 4) Analiso a porcentagem do benefício:
            //    - A porcentagem é exatamente 20%? 
            //      SIM: Encontrei a sugestão ideal para manter os 20%. Preencho a estrutura final, sinalizo que é sugestão e retorno.
            //    - A porcentagem é menor que 20%? 
            //      SIM: Significa que tenho que aumentar a sugestão. Dobro a sugestão e vou para o passo (2).
            //    - A porcentagem é maior que 20%? 
            //      SIM: Significa que tenho que diminuir a sugestão. Subtraio a sugestão atual pela anterior e divido por 2. Esta é a nova sugestão e vou para o passo 2.
            //

            // 1) Adoto como sugestão o maior consumo e sugestão anterior ZERO.
            double sugestao = controle_sazonalidade.calculo.Maiores4Consumos[0].ConsumoTotal;
            double sugestao_anterior = 0.0;


            // teste
            LogMessage(string.Format("Sugestão Inicial: {0:0.0000}", sugestao));


            // numero de passagens
            controle_sazonalidade.calculo.NumPassagensSugestao = 0;

            // loop de busca pela sugestão
            while (true)
            {
                // incrementa numero de passagens
                controle_sazonalidade.calculo.NumPassagensSugestao++;

                // teste
                LogMessage(string.Format("Passagem: {0}", controle_sazonalidade.calculo.NumPassagensSugestao));

                // verifica se estrapolou passagens
                if (controle_sazonalidade.calculo.NumPassagensSugestao > 200)
                {
                    // Não foi possível calcular sugestão
                    controle_sazonalidade.calculo.Sugestao = 0.0;
                    controle_sazonalidade.calculo.DentroBeneficio_Sugestao = false;
                    controle_sazonalidade.calculo.PorcentagemBeneficio_Sugestao = 0.0;
                    controle_sazonalidade.calculo.Status_Sugestao = 3;


                    // teste
                    LogMessage("Fim, pois estrapolou passagens");
                    LogMessage("--------------------------------------------------------------------------");

                    // não apresento sugestão
                    return;
                }

                // 2) Preencho os ConsumoTotal dos meses de hoje e próximos com a sugestão ou consumos editados pelo usuário.
                CONTROLE_SAZONALIDADE_CALCULO calculo = new CONTROLE_SAZONALIDADE_CALCULO();

                List<CONTROLE_SAZONALIDADE_MES> MesesClone = new List<CONTROLE_SAZONALIDADE_MES>();

                foreach (CONTROLE_SAZONALIDADE_MES mes in controle_sazonalidade.Meses)
                {
                    CONTROLE_SAZONALIDADE_MES novo = new CONTROLE_SAZONALIDADE_MES();
                    novo = mes;

                    // verifica se mes futuro
                    if (novo.Data > DataHoje)
                    {
                        // sugestão
                        novo.ConsumoTotal = sugestao;

                        // indica que é consumo sugerido
                        novo.retorno = 11;

                        // verifica se usuário editou consumos dos meses futuros
                        if (consumos_editados != null)
                        {
                            if (consumos_editados.Count == 12)
                            {
                                // percorre consumos e encontra mes
                                foreach (CONTROLE_SAZONALIDADE_CONSUMO_EDITADO editado in consumos_editados)
                                {
                                    // verifica se é o mês/ano do cálculo e se tem consumo editado
                                    if ((editado.Mes == novo.Data.Month && editado.Ano == novo.Data.Year) && (editado.ConsumoTotal > 0.0 && editado.retorno == 12))
                                    {
                                        // consumo editado
                                        novo.ConsumoTotal = editado.ConsumoTotal;

                                        // indica que é consumo editado
                                        novo.retorno = 12;

                                        // encontrou
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    // adiciono
                    MesesClone.Add(novo);
                }

                // percorre clone e verifica se existem meses para sugerir
                bool existem_meses_para_sugerir = false;

                foreach (CONTROLE_SAZONALIDADE_MES mes in MesesClone)
                {
                    // verifica se mês disponível para sugestão
                    if (mes.retorno == 11)
                    {
                        existem_meses_para_sugerir = true;
                        break;
                    }
                }

                // verifica se não existe mês disponível para sugestão
                if (!existem_meses_para_sugerir)
                {
                    // Faço o calculo da sazonalidade, pois provavelmente alguns meses são editados pelo usuário
                    CalculoSazonalidade(MesesClone, ref calculo, 0, true);

                    // copio meses
                    controle_sazonalidade.Meses = MesesClone;

                    // benefício sugestão
                    controle_sazonalidade.calculo.Sugestao = 0.0;
                    controle_sazonalidade.calculo.DentroBeneficio_Sugestao = calculo.DentroBeneficio;
                    controle_sazonalidade.calculo.PorcentagemBeneficio_Sugestao = calculo.PorcentagemBeneficio;
                    controle_sazonalidade.calculo.Status_Sugestao = 2;


                    // teste
                    LogMessage(string.Format("Não existem meses disponíveis para Sugestão: [{0:0.0000} %]", calculo.PorcentagemBeneficio));
                    LogMessage("--------------------------------------------------------------------------");


                    // encontrou sugestão
                    break;
                }


                // 3) Faço o calculo da sazonalidade da sugestão.
                CalculoSazonalidade(MesesClone, ref calculo, 0, true);

                // teste
                LogMessage(string.Format("Porcentagem Beneficio: {0:0.0000}  [{1:0.0000} %]", sugestao, calculo.PorcentagemBeneficio));


                // 4) Analiso a porcentagem do benefício:
                //    - A porcentagem é exatamente 20%? 
                //      SIM: Encontrei a sugestão ideal para manter os 20%. Preencho a estrutura final, sinalizo que é sugestão e retorno.
                if (ExatamenteDentroBeneficio(calculo.PorcentagemBeneficio))
                {
                    // copio meses
                    controle_sazonalidade.Meses = MesesClone;

                    // benefício sugestão
                    controle_sazonalidade.calculo.Sugestao = sugestao;
                    controle_sazonalidade.calculo.DentroBeneficio_Sugestao = calculo.DentroBeneficio;
                    controle_sazonalidade.calculo.PorcentagemBeneficio_Sugestao = calculo.PorcentagemBeneficio;
                    controle_sazonalidade.calculo.Status_Sugestao = 0;


                    // teste
                    LogMessage(string.Format("Encontrou Sugestão: {0:0.0000}  [{1:0.0000} %]", sugestao, calculo.PorcentagemBeneficio));
                    LogMessage("--------------------------------------------------------------------------");


                    // encontrou sugestão
                    break;
                }

                //    - A porcentagem é maior que 20%? 
                //      SIM: Significa que tenho que aumentar a sugestão. Dobro a sugestão e vou para o passo (2).
                if (calculo.PorcentagemBeneficio > 20.0)
                {
                    // salto
                    double salto = ((sugestao - sugestao_anterior) * 2.0) + sugestao_anterior;

                    // teste
                    LogMessage(string.Format("> 20%: salto[{0:0.0000}] | anterior[{1:0.0000}] | sugestao[{2:0.0000}]", salto, sugestao_anterior, sugestao));


                    // sugestão anterior
                    sugestao_anterior = sugestao;

                    // dobro a sugestão
                    sugestao = salto;
                }

                //    - A porcentagem é menor que 20%? 
                //      SIM: Significa que tenho que diminuir a sugestão. Subtraio a sugestão atual pela anterior e divido por 2. Esta é a nova sugestão e vou para o passo 2.
                if (calculo.PorcentagemBeneficio < 20.0)
                {
                    // Subtraio a sugestão atual pela anterior e divido por 2
                    sugestao = ((sugestao - sugestao_anterior) / 2.0) + sugestao_anterior;

                    // teste
                    LogMessage(string.Format("< 20%: anterior[{0:0.0000}] | sugestao[{1:0.0000}]", sugestao_anterior, sugestao));

                }
            }

            // fim
            return;
        }


        // calculo da sazonalidade
        public double CalculoSazonalidade(List<CONTROLE_SAZONALIDADE_MES> Meses, ref CONTROLE_SAZONALIDADE_CALCULO calculo, int IDTipoSazonalidade, bool sugestao = false)
        {
            //
            // Cálculo da sazonalidade
            //

            // data de hoje
            DateTime DataHoje = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 0, 0);

            // menores
            List<CONTROLE_SAZONALIDADE_MES> menores = new List<CONTROLE_SAZONALIDADE_MES>();

            // copia
            foreach (CONTROLE_SAZONALIDADE_MES mes in Meses)
            {
                CONTROLE_SAZONALIDADE_MES novo = new CONTROLE_SAZONALIDADE_MES();
                novo.Data = mes.Data;
                novo.ConsumoTotal = mes.ConsumoTotal;
                menores.Add(novo);
            }

            int i = 0;

            // inicia soma dos 4 menores
            calculo.SomaMenores4Consumos = 0.0;

            if (menores != null)
            {
                // ordenar do menor para o maior 
                menores.Sort((ct1, ct2) => ct1.ConsumoTotal.CompareTo(ct2.ConsumoTotal));

                // busca 4 menores
                foreach (CONTROLE_SAZONALIDADE_MES menor in menores)
                {
                    // verifica se tem consumo
                    if (menor.ConsumoTotal > 0)
                    {
                        // não considero o consumo do mês de hoje se NÃO for sugestão, pois ainda não está consolidado
                        if (sugestao || !(menor.Data.Month == DataHoje.Month && menor.Data.Year == DataHoje.Year))
                        {
                            CONTROLE_SAZONALIDADE_CONSUMOS menor_tmp = new CONTROLE_SAZONALIDADE_CONSUMOS();

                            // copia consumo
                            menor_tmp.Data = menor.Data;
                            menor_tmp.ConsumoTotal = menor.ConsumoTotal;
                            calculo.Menores4Consumos.Add(menor_tmp);

                            // soma
                            calculo.SomaMenores4Consumos += menor.ConsumoTotal;

                            // proximo
                            i++;

                            // verifica se atingiu 4
                            if (i >= 4)
                            {
                                break;
                            }
                        }
                    }
                }
            }

            // maiores
            List<CONTROLE_SAZONALIDADE_MES> maiores = new List<CONTROLE_SAZONALIDADE_MES>();

            // copia
            foreach (CONTROLE_SAZONALIDADE_MES mes in Meses)
            {
                CONTROLE_SAZONALIDADE_MES novo = new CONTROLE_SAZONALIDADE_MES();
                novo.Data = mes.Data;
                novo.ConsumoTotal = mes.ConsumoTotal;
                maiores.Add(novo);
            }

            i = 0;

            // inicia soma dos 4 maiores
            calculo.SomaMaiores4Consumos = 0.0;
            calculo.SomaMaiores4Consumos_20porc = 0.0;

            if (maiores != null)
            {
                // ordenar do maior para o menor 
                maiores.Sort((ct1, ct2) => ct2.ConsumoTotal.CompareTo(ct1.ConsumoTotal));

                // busca 4 maiores
                foreach (CONTROLE_SAZONALIDADE_MES maior in maiores)
                {
                    // verifica se tem consumo
                    if (maior.ConsumoTotal > 0)
                    {
                        // não considero o consumo do mês de hoje se não for sugestão, pois ainda não está consolidado
                        if (sugestao || !(maior.Data.Month == DataHoje.Month && maior.Data.Year == DataHoje.Year))
                        {
                            CONTROLE_SAZONALIDADE_CONSUMOS maior_tmp = new CONTROLE_SAZONALIDADE_CONSUMOS();

                            // copia consumo
                            maior_tmp.Data = maior.Data;
                            maior_tmp.ConsumoTotal = maior.ConsumoTotal;
                            calculo.Maiores4Consumos.Add(maior_tmp);

                            // soma
                            calculo.SomaMaiores4Consumos += maior.ConsumoTotal;

                            // proximo
                            i++;

                            // verifica se atingiu 4
                            if (i >= 4)
                            {
                                break;
                            }
                        }
                    }
                }

                // 20% da soma dos 4 maiores
                calculo.SomaMaiores4Consumos_20porc = calculo.SomaMaiores4Consumos * 0.2;
            }

            // beneficio
            calculo.PorcentagemBeneficio = 0.0;
            calculo.DentroBeneficio = false;
            calculo.PossuiBeneficio = IDTipoSazonalidade;

            if (calculo.SomaMaiores4Consumos > 0.0)
            {
                // porcentagem do benefício
                calculo.PorcentagemBeneficio = (calculo.SomaMenores4Consumos / calculo.SomaMaiores4Consumos) * 100.0;

                // verifica se abaixo de 20% e portanto dentro do benefício
                if (calculo.PorcentagemBeneficio <= 20)
                {
                    calculo.DentroBeneficio = true;
                }
            }

            // retorna porcentagem do beneficio
            return (calculo.PorcentagemBeneficio);
        }

        // número de meses entre 2 datas
        public int GetMonthDifference(DateTime startDate, DateTime endDate)
        {
            int monthsApart = 12 * (startDate.Year - endDate.Year) + startDate.Month - endDate.Month;
            return Math.Abs(monthsApart);
        }

        // verifica se exatamente dentro do benefício (+/- 0.01%)
        public bool ExatamenteDentroBeneficio(double PorcentagemBeneficio)
        {
            // verifica se exatamente dentro do benefício (+/- 0.01%)
            if (PorcentagemBeneficio >= 19.99 && PorcentagemBeneficio <= 20.00 )
            {
                // dentro do beneficio
                return (true);

            }

            // fora do benefício
            return (false);
        }

        // GET: Obter Consumo
        public JsonResult ObterConsumo(int Mes, int Ano)
        {
            // data inicial
            DateTime datahora_ini = new DateTime(Ano, Mes, 1, 0, 0, 0);

            // ano passado
            datahora_ini = datahora_ini.AddYears(-1);

            // data final
            DateTime datahora_fim = datahora_ini.AddMonths(1);


            // le cookies
            LeCookies_SmartEnergy();

            // simulação
            int IDSimulacaoCenario = 0;

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // consumo
            double consumoTotal = 0.0;

            // retorno em erro
            int retorno = -100;

            // le medicoes
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicoesMetodos.ListarPorId(IDMedicao);

            if (medicao != null)
            {
                // preenche solicitacao
                CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
                config_interface.sweb.id_cliente = medicao.IDCliente;
                config_interface.sweb.id_gateway = medicao.IDGateway;
                config_interface.sweb.id_medicao = medicao.IDMedicao;

                // data inicial e final do mês 
                DATAHORA dh_ini = new DATAHORA();
                DATAHORA dh_fim = new DATAHORA();

                // converte para DateHora
                Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, datahora_ini);
                Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, datahora_fim);

                // fatura
                RESULT_ENERGIA_FATURA faturaDLL = new RESULT_ENERGIA_FATURA();
                RESULT_ENERGIA_FATURA faturaDLL_sim = new RESULT_ENERGIA_FATURA();

                // calcula valores fatura (sem simulação)
                // -2 - erro: tipo de interface indefinida
                // -1 - erro: estrutura tarifaria inexistente
                //  0 - OK e com dados
                //  1 - erro: nao foi possivel abrir banco de dados
                //  2 - erro: nao existem dados
                //  3 - erro: intervalo menos de 1 dia
                //  4 - erro: leitura da configuracao da medicao
                //  5 - erro: leitura da configuracao da gateway
                //  6 - erro: leitura do historico de contrato de demanda
                //  7 - erro: tarifas THS
                //  8 - erro: tarifa pis/cofins
                //  9 - erro: nao existem bandeiras tarifarias
                retorno = SmCalcDB_Energia_Fatura((char)0, ref config_interface, IDSimulacaoCenario, (char)medicao.IDContratoMedicao, (char)medicao.IDEstruturaTarifaria, (char)0, ref dh_ini, ref dh_fim, ref faturaDLL, ref faturaDLL_sim);

                // consumo total
                consumoTotal = faturaDLL.consumo_total_tusd[CF.REG];
            }

            // mes e ano
            string mesAno = string.Format("{0:MMM/yyyy}", datahora_ini);

            // consumo total
            string consumoTotal_str = string.Format("{0:#,##0.00}", consumoTotal);

            // prepara retorno em JSON
            var returnedData = new
            {
                status = retorno,
                mesAno = mesAno,
                consumoTotal = consumoTotal_str
            };

            // retorna o valor em JSON
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }
    }
}