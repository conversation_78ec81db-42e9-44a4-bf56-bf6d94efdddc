﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {
        //
        // FATOR DE CARGA
        //

        // GET: Relatorio Fator de Carga
        public ActionResult Relat_FatCarga(int IDCliente, int IDMedicao)
        {
            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Relat_FatCarga");

            // relatorio fator de carga
            return (Relat_Grafico_Show(IDCliente, IDMedicao, 4));
        }

        // Fator de Carga Diario
        private void FatCarga_Diario(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_DIARIO relatorio = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise = new RELAT_DIARIO_ANALISE();

            RELAT_DIARIO relatorio_sim = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise_sim = new RELAT_DIARIO_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // calcula valores
            retorno = SmCalcDB_Energia_RelatDiario((char)0, ref config_interface, (char)4, (char)IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   0, 15, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var FatCarga = new double[3];

            // simulacao
            var FatCarga_Sim = new double[3];

            double FatCarga_max_grafico = 0.0;

            int i = 0;

            for (i = 0; i < 3; i++)
            {
                // copia
                FatCarga[i] = relatorio.registro[i].valor1;

                // simulacao 
                FatCarga_Sim[i] = relatorio_sim.registro[i].valor1;

                // verifica fator de carga maximo
                if (FatCarga[i] > FatCarga_max_grafico)
                    FatCarga_max_grafico = FatCarga[i];

                // verifica fator de carga maximo simulacao
                if (FatCarga_Sim[i] > FatCarga_max_grafico)
                    FatCarga_max_grafico = FatCarga_Sim[i];
            }

            FatCarga_max_grafico = FatCarga_max_grafico * 1.1;

            ViewBag.FatCargaMaxGrafico = FatCarga_max_grafico;

            ViewBag.FatCarga = FatCarga;

            // simulacao
            ViewBag.FatCarga_Sim = FatCarga_Sim;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.FatorCarga;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Diario;

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:d}", DataAtual);

            // fator de carga demanda maxima ponta
            ViewBag.Dem_MaxP = string.Format("{0:#,##0.0}", analise.analise_valor1.maximo[0]);
            DateTime Dem_MaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max[0]);
            if (Dem_MaxP_DataHora.Year != 2000)
                ViewBag.Dem_MaxP_DataHora = String.Format("{0:d} {1:HH:mm}", Dem_MaxP_DataHora, Dem_MaxP_DataHora);
            else
                ViewBag.Dem_MaxP_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxP_DataHoraN = Dem_MaxP_DataHora;

            // fator de carga demanda maxima ponta simulacao
            ViewBag.Dem_MaxP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.maximo[0]);
            DateTime Dem_MaxP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor1.datahora_max[0]);
            if (Dem_MaxP_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxP_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", Dem_MaxP_DataHora_Sim, Dem_MaxP_DataHora_Sim);
            else
                ViewBag.Dem_MaxP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MaxP_DataHoraN_Sim = Dem_MaxP_DataHora_Sim;

            // demanda maxima fora ponta indutivo
            ViewBag.Dem_MaxFPI = string.Format("{0:#,##0.0}", analise.analise_valor1.maximo[1]);
            DateTime Dem_MaxFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max[1]);
            if (Dem_MaxFPI_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPI_DataHora = String.Format("{0:d} {1:HH:mm}", Dem_MaxFPI_DataHora, Dem_MaxFPI_DataHora);
            else
                ViewBag.Dem_MaxFPI_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxFPI_DataHoraN = Dem_MaxFPI_DataHora;

            // demanda maxima fora ponta indutivo simulacao
            ViewBag.Dem_MaxFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.maximo[1]);
            DateTime Dem_MaxFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor1.datahora_max[1]);
            if (Dem_MaxFPI_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxFPI_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", Dem_MaxFPI_DataHora_Sim, Dem_MaxFPI_DataHora_Sim);
            else
                ViewBag.Dem_MaxFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MaxFPI_DataHoraN_Sim = Dem_MaxFPI_DataHora_Sim;

            // demanda maxima fora ponta capacitivo
            ViewBag.Dem_MaxFPC = string.Format("{0:#,##0.0}", analise.analise_valor1.maximo[2]);
            DateTime Dem_MaxFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max[2]);
            if (Dem_MaxFPC_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPC_DataHora = String.Format("{0:d} {1:HH:mm}", Dem_MaxFPC_DataHora, Dem_MaxFPC_DataHora);
            else
                ViewBag.Dem_MaxFPC_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxFPC_DataHoraN = Dem_MaxFPC_DataHora;

            // demanda maxima fora ponta capacitivo simulacao
            ViewBag.Dem_MaxFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.maximo[2]);
            DateTime Dem_MaxFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor1.datahora_max[2]);
            if (Dem_MaxFPC_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxFPC_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", Dem_MaxFPC_DataHora_Sim, Dem_MaxFPC_DataHora_Sim);
            else
                ViewBag.Dem_MaxFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MaxFPC_DataHoraN_Sim = Dem_MaxFPC_DataHora_Sim;

            ViewBag.Dem_MedP = string.Format("{0:#,##0.0}", analise.analise_valor1.medio[0]);
            ViewBag.Dem_MedFPI = string.Format("{0:#,##0.0}", analise.analise_valor1.medio[1]);
            ViewBag.Dem_MedFPC = string.Format("{0:#,##0.0}", analise.analise_valor1.medio[2]);

            ViewBag.FatCargaP = string.Format("{0:0.0}", analise.analise_valor1.minimo[0]);
            ViewBag.FatCargaFPI = string.Format("{0:0.0}", analise.analise_valor1.minimo[1]);
            ViewBag.FatCargaFPC = string.Format("{0:0.0}", analise.analise_valor1.minimo[2]);

            // simulação
            ViewBag.Dem_MedP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.medio[0]);
            ViewBag.Dem_MedFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.medio[1]);
            ViewBag.Dem_MedFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.medio[2]);

            ViewBag.FatCargaP_Sim = string.Format("{0:0.0}", analise_sim.analise_valor1.minimo[0]);
            ViewBag.FatCargaFPI_Sim = string.Format("{0:0.0}", analise_sim.analise_valor1.minimo[1]);
            ViewBag.FatCargaFPC_Sim = string.Format("{0:0.0}", analise_sim.analise_valor1.minimo[2]);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(1);

            ViewBag.data_hora_ini = data_hora_ini;

            EventosPeriodo(IDCliente, IDMedicao, data_hora_ini, data_hora_fim);

            return;
        }

        // Fator de Carga Diario XLS
        private HSSFWorkbook FatCarga_Diario_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // RESUMO
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            int rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Carga", "Relatório Diário", _negritoCellStyle);

            // cabecalho ponta, fora de ponta 
            string[] cab_cons = { "", "Fora de Ponta Capacitivo", "", "Fora de Ponta Indutivo", "", "Ponta", "", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // fator de carga
            IRow row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Fator de Carga (%)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatCargaFPC), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatCargaFPI), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatCargaP), _1CellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Demanda Média (kW)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP), _1CellStyle);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Demanda Máxima (kW)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN, _datahoraStyle);

            // largura de cada coluna
            for (int i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Fator de Carga Diario XLS simulacao
        private HSSFWorkbook FatCarga_Diario_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // RESUMO
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            int rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Carga - Simulação", "Relatório Diário", _negritoCellStyle);

            // cabecalho ponta, fora de ponta 
            string[] cab_cons = { "", "Fora de Ponta Capacitivo", "", "Fora de Ponta Indutivo", "", "Ponta", "", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // fator de carga
            IRow row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Fator de Carga (%)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatCargaFPC_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatCargaFPI_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatCargaP_Sim), _1CellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Demanda Média (kW)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP_Sim), _1CellStyle);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Demanda Máxima (kW)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN_Sim, _datahoraStyle);

            // largura de cada coluna
            for (int i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Fator de Carga Semanal
        private void FatCarga_Semanal(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_SEMANAL relatorio = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise = new RELAT_SEMANAL_ANALISE();

            RELAT_SEMANAL relatorio_sim = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise_sim = new RELAT_SEMANAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // calcula valores
            retorno = SmCalcDB_Energia_RelatSemanal((char)0, ref config_interface, (char)4, (char)IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   0, 15, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var FatCargaP = new double[9];
            var FatCargaFPI = new double[9];
            var FatCargaFPC = new double[9];
            var Datas = new string[9];
            var DatasN = new DateTime[9];
            var Dias = new string[9];

            // grafico simulacao
            var FatCargaP_Sim = new double[9];
            var FatCargaFPI_Sim = new double[9];
            var FatCargaFPC_Sim = new double[9];
            var Datas_Sim = new string[9];
            var DatasN_Sim = new DateTime[9];

            DateTime inicio = new DateTime();
            DateTime fim = new DateTime();

            double FatCarga_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 9; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = string.Format("{0:d}", strData);

                // formata label simulacao
                Datas_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Sim[i] = strData;

                // guarda inicio
                if (i == 1)
                {
                    inicio = strData;
                }

                // guarda fim
                if (i == 7)
                {
                    fim = strData;
                }

                // proximo dia
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    FatCargaP[i] = relatorio.registro[0].valor[0];
                    FatCargaFPI[i] = relatorio.registro[0].valor[1];
                    FatCargaFPC[i] = relatorio.registro[0].valor[2];

                    // zera simulacao
                    FatCargaP_Sim[i] = relatorio_sim.registro[0].valor[0];
                    FatCargaFPI_Sim[i] = relatorio_sim.registro[0].valor[1];
                    FatCargaFPC_Sim[i] = relatorio_sim.registro[0].valor[2];
                }

                if (i == 8)
                {
                    // zera
                    FatCargaP[i] = relatorio.registro[6].valor[0];
                    FatCargaFPI[i] = relatorio.registro[6].valor[1];
                    FatCargaFPC[i] = relatorio.registro[6].valor[2];

                    // zera simulacao
                    FatCargaP_Sim[i] = relatorio_sim.registro[6].valor[0];
                    FatCargaFPI_Sim[i] = relatorio_sim.registro[6].valor[1];
                    FatCargaFPC_Sim[i] = relatorio_sim.registro[6].valor[2];
                }

                if (i >= 1 && i <= 7)
                {
                    // copia
                    j = i - 1;

                    FatCargaP[i] = relatorio.registro[j].valor[0];
                    FatCargaFPI[i] = relatorio.registro[j].valor[1];
                    FatCargaFPC[i] = relatorio.registro[j].valor[2];

                    // simulação
                    FatCargaP_Sim[i] = relatorio_sim.registro[j].valor[0];
                    FatCargaFPI_Sim[i] = relatorio_sim.registro[j].valor[1];
                    FatCargaFPC_Sim[i] = relatorio_sim.registro[j].valor[2];

                    // verifica fator de carga maximo
                    if (FatCargaP[i] > FatCarga_max_grafico)
                        FatCarga_max_grafico = FatCargaP[i];

                    if (FatCargaFPI[i] > FatCarga_max_grafico)
                        FatCarga_max_grafico = FatCargaFPI[i];

                    if (FatCargaFPC[i] > FatCarga_max_grafico)
                        FatCarga_max_grafico = FatCargaFPC[i];

                    // verifica fator de carga maximo simulacao
                    if (FatCargaP_Sim[i] > FatCarga_max_grafico)
                        FatCarga_max_grafico = FatCargaP_Sim[i];

                    if (FatCargaFPI_Sim[i] > FatCarga_max_grafico)
                        FatCarga_max_grafico = FatCargaFPI_Sim[i];

                    if (FatCargaFPC_Sim[i] > FatCarga_max_grafico)
                        FatCarga_max_grafico = FatCargaFPC_Sim[i];
                }
            }

            FatCarga_max_grafico = FatCarga_max_grafico * 1.1;

            ViewBag.FatCargaMaxGrafico = FatCarga_max_grafico;

            ViewBag.FatCargaP = FatCargaP;
            ViewBag.FatCargaFPI = FatCargaFPI;
            ViewBag.FatCargaFPC = FatCargaFPC;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            // simulacao
            ViewBag.FatCargaP_Sim = FatCargaP_Sim;
            ViewBag.FatCargaFPI_Sim = FatCargaFPI_Sim;
            ViewBag.FatCargaFPC_Sim = FatCargaFPC_Sim;
            ViewBag.Datas_Sim = Datas_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.FatorCarga;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Semanal;

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);
            ViewBag.DataAtualN = inicio;
            ViewBag.DataFinalN = fim;
            ViewBag.DataTextoAtual = string.Format("{0:d} - {1:d} ", inicio, fim);

            // fator de carga demanda maxima ponta
            ViewBag.Dem_MaxP = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[0]);
            DateTime Dem_MaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (Dem_MaxP_DataHora.Year != 2000)
                ViewBag.Dem_MaxP_DataHora = String.Format("{0:d} {1:HH:mm}", Dem_MaxP_DataHora, Dem_MaxP_DataHora);
            else
                ViewBag.Dem_MaxP_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxP_DataHoraN = Dem_MaxP_DataHora;

            // fator de carga demanda maxima ponta simulacao
            ViewBag.Dem_MaxP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[0]);
            DateTime Dem_MaxP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[0]);
            if (Dem_MaxP_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxP_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", Dem_MaxP_DataHora_Sim, Dem_MaxP_DataHora_Sim);
            else
                ViewBag.Dem_MaxP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MaxP_DataHoraN_Sim = Dem_MaxP_DataHora_Sim;

            // demanda maxima fora ponta indutiva
            ViewBag.Dem_MaxFPI = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[1]);
            DateTime Dem_MaxFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[1]);
            if (Dem_MaxFPI_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPI_DataHora = String.Format("{0:d} {1:HH:mm}", Dem_MaxFPI_DataHora, Dem_MaxFPI_DataHora);
            else
                ViewBag.Dem_MaxFPI_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxFPI_DataHoraN = Dem_MaxFPI_DataHora;

            // demanda maxima fora ponta indutiva simulacao
            ViewBag.Dem_MaxFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[1]);
            DateTime Dem_MaxFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[1]);
            if (Dem_MaxFPI_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxFPI_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", Dem_MaxFPI_DataHora_Sim, Dem_MaxFPI_DataHora_Sim);
            else
                ViewBag.Dem_MaxFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MaxFPI_DataHoraN_Sim = Dem_MaxFPI_DataHora_Sim;

            // demanda maxima fora ponta capacitivo 
            ViewBag.Dem_MaxFPC = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[2]);
            DateTime Dem_MaxFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[2]);
            if (Dem_MaxFPC_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPC_DataHora = String.Format("{0:d} {1:HH:mm}", Dem_MaxFPC_DataHora, Dem_MaxFPC_DataHora);
            else
                ViewBag.Dem_MaxFPC_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxFPC_DataHoraN = Dem_MaxFPC_DataHora;

            // demanda maxima fora ponta capacitivo simulacao
            ViewBag.Dem_MaxFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[2]);
            DateTime Dem_MaxFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[2]);
            if (Dem_MaxFPC_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxFPC_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", Dem_MaxFPC_DataHora_Sim, Dem_MaxFPC_DataHora_Sim);
            else
                ViewBag.Dem_MaxFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MaxFPC_DataHoraN_Sim = Dem_MaxFPC_DataHora_Sim;

            ViewBag.Dem_MedP = string.Format("{0:#,##0.0}", analise.analise_valor.medio[0]);
            ViewBag.Dem_MedFPI = string.Format("{0:#,##0.0}", analise.analise_valor.medio[1]);
            ViewBag.Dem_MedFPC = string.Format("{0:#,##0.0}", analise.analise_valor.medio[2]);

            ViewBag.FatCargaP_Semana = string.Format("{0:0.0}", analise.analise_valor.minimo[0]);
            ViewBag.FatCargaFPI_Semana = string.Format("{0:0.0}", analise.analise_valor.minimo[1]);
            ViewBag.FatCargaFPC_Semana = string.Format("{0:0.0}", analise.analise_valor.minimo[2]);

            // simulacao
            ViewBag.Dem_MedP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[0]);
            ViewBag.Dem_MedFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[1]);
            ViewBag.Dem_MedFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[2]);

            ViewBag.FatCargaP_Semana_Sim = string.Format("{0:0.0}", analise_sim.analise_valor.minimo[0]);
            ViewBag.FatCargaFPI_Semana_Sim = string.Format("{0:0.0}", analise_sim.analise_valor.minimo[1]);
            ViewBag.FatCargaFPC_Semana_Sim = string.Format("{0:0.0}", analise_sim.analise_valor.minimo[2]);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(inicio.Year, inicio.Month, inicio.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(7);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Fator de Carga Semanal XLS
        private HSSFWorkbook FatCarga_Semanal_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Semana", "Data e Hora", "Fora de Ponta Capacitivo (%)", "Fora de Ponta Indutivo (%)", "Ponta (%)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 8; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // semana
                textoCelulaXLS(row, 0, string.Format("{0:dddd}", ViewBag.DatasN[i]));

                // data e hora
                datahoraCelulaXLS(row, 1, ViewBag.DatasN[i], _dataStyle);

                // fator de carga fora de ponta capacitivo
                numeroCelulaXLS(row, 2, ViewBag.FatCargaFPC[i], _1CellStyle);

                // fator de carga fora de ponta indutivo
                numeroCelulaXLS(row, 3, ViewBag.FatCargaFPI[i], _1CellStyle);

                // fator de carga ponta
                numeroCelulaXLS(row, 4, ViewBag.FatCargaP[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Carga", "Relatório Semanal", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string[] cab_cons = { "", "Fora de Ponta Capacitivo", "", "Fora de Ponta Indutivo", "", "Ponta", "", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // fator de carga
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Fator de Carga (%)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatCargaFPC_Semana), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatCargaFPI_Semana), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatCargaP_Semana), _1CellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Demanda Média (kW)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP), _1CellStyle);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Demanda Máxima (kW)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Fator de Carga Semanal XLS simulacao
        private HSSFWorkbook FatCarga_Semanal_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // simulacao 
            string[] simulacao = { "Fator de Carga - Simulação" };

            // simulação 
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas do titulo simulacao
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 4));

            // cabecalho
            string[] cabecalho = { "Semana", "Data e Hora", "Fora de Ponta Capacitivo (%)", "Fora de Ponta Indutivo (%)", "Ponta (%)" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 8; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // semana
                textoCelulaXLS(row, 0, string.Format("{0:dddd}", ViewBag.DatasN[i]));

                // data e hora
                datahoraCelulaXLS(row, 1, ViewBag.DatasN_Sim[i], _dataStyle);

                // fator de carga fora de ponta capacitivo
                numeroCelulaXLS(row, 2, ViewBag.FatCargaFPC_Sim[i], _1CellStyle);

                // fator de carga fora de ponta indutivo
                numeroCelulaXLS(row, 3, ViewBag.FatCargaFPI_Sim[i], _1CellStyle);

                // fator de carga ponta
                numeroCelulaXLS(row, 4, ViewBag.FatCargaP_Sim[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Carga - Simulação", "Relatório Semanal", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string[] cab_cons = { "", "Fora de Ponta Capacitivo", "", "Fora de Ponta Indutivo", "", "Ponta", "", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // fator de carga
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Fator de Carga (%)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatCargaFPC_Semana_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatCargaFPI_Semana_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatCargaP_Semana_Sim), _1CellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Demanda Média (kW)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP_Sim), _1CellStyle);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Demanda Máxima (kW)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN_Sim, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Fator de Carga Mensal
        private void FatCarga_Mensal(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_MENSAL relatorio = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise = new RELAT_MENSAL_ANALISE();

            RELAT_MENSAL relatorio_sim = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise_sim = new RELAT_MENSAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // calcula valores
            retorno = SmCalcDB_Energia_RelatMensal((char)0, ref config_interface, (char)4, (char)IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);

            // numero de dias do mes
            int NumDiasMes = relatorio.num_dias;

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[NumDiasMes - 1].datahora.data.ano,
                                   (int)relatorio.registro[NumDiasMes - 1].datahora.data.mes,
                                   1, 0, 15, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var FatCargaP = new double[42];
            var FatCargaFPI = new double[42];
            var FatCargaFPC = new double[42];
            var Datas = new string[42];
            var DatasN = new DateTime[42];
            var Dias = new string[42];

            // grafico simulacao
            var FatCargaP_Sim = new double[42];
            var FatCargaFPI_Sim = new double[42];
            var FatCargaFPC_Sim = new double[42];
            var Datas_Sim = new string[42];
            var DatasN_Sim = new DateTime[42];

            double FatCarga_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = strData.ToString("dd/MM");

                // formata label simulacao
                Datas_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Sim[i] = strData;

                // proximo dias
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    FatCargaP[i] = relatorio.registro[0].valor[0];
                    FatCargaFPI[i] = relatorio.registro[0].valor[1];
                    FatCargaFPC[i] = relatorio.registro[0].valor[2];

                    // zera simulacao
                    FatCargaP_Sim[i] = relatorio_sim.registro[0].valor[0];
                    FatCargaFPI_Sim[i] = relatorio_sim.registro[0].valor[1];
                    FatCargaFPC_Sim[i] = relatorio_sim.registro[0].valor[2];
                }

                if (i == (NumDiasMes + 1))
                {
                    // zera
                    FatCargaP[i] = relatorio.registro[NumDiasMes - 1].valor[0];
                    FatCargaFPI[i] = relatorio.registro[NumDiasMes - 1].valor[1];
                    FatCargaFPC[i] = relatorio.registro[NumDiasMes - 1].valor[2];

                    // zera simulacao
                    FatCargaP_Sim[i] = relatorio_sim.registro[NumDiasMes - 1].valor[0];
                    FatCargaFPI_Sim[i] = relatorio_sim.registro[NumDiasMes - 1].valor[1];
                    FatCargaFPC_Sim[i] = relatorio_sim.registro[NumDiasMes - 1].valor[2];
                }

                if (i >= 1 && i <= NumDiasMes)
                {
                    // copia
                    j = i - 1;

                    FatCargaP[i] = relatorio.registro[j].valor[0];
                    FatCargaFPI[i] = relatorio.registro[j].valor[1];
                    FatCargaFPC[i] = relatorio.registro[j].valor[2];

                    // simulacao
                    FatCargaP_Sim[i] = relatorio_sim.registro[j].valor[0];
                    FatCargaFPI_Sim[i] = relatorio_sim.registro[j].valor[1];
                    FatCargaFPC_Sim[i] = relatorio_sim.registro[j].valor[2];

                    // verifica fator de carga maximo
                    if (FatCargaP[i] > FatCarga_max_grafico)
                        FatCarga_max_grafico = FatCargaP[i];

                    if (FatCargaFPI[i] > FatCarga_max_grafico)
                        FatCarga_max_grafico = FatCargaFPI[i];

                    if (FatCargaFPC[i] > FatCarga_max_grafico)
                        FatCarga_max_grafico = FatCargaFPC[i];

                    // verifica fator de carga maximo simulacao
                    if (FatCargaP_Sim[i] > FatCarga_max_grafico)
                        FatCarga_max_grafico = FatCargaP_Sim[i];

                    if (FatCargaFPI_Sim[i] > FatCarga_max_grafico)
                        FatCarga_max_grafico = FatCargaFPI_Sim[i];

                    if (FatCargaFPC_Sim[i] > FatCarga_max_grafico)
                        FatCarga_max_grafico = FatCargaFPC_Sim[i];
                }
            }

            FatCarga_max_grafico = FatCarga_max_grafico * 1.1;

            ViewBag.FatCargaMaxGrafico = FatCarga_max_grafico;

            ViewBag.FatCargaP = FatCargaP;
            ViewBag.FatCargaFPI = FatCargaFPI;
            ViewBag.FatCargaFPC = FatCargaFPC;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            // simulacao
            ViewBag.FatCargaP_Sim = FatCargaP_Sim;
            ViewBag.FatCargaFPI_Sim = FatCargaFPI_Sim;
            ViewBag.FatCargaFPC_Sim = FatCargaFPC_Sim;
            ViewBag.Datas_Sim = Datas_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.FatorCarga;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Mensal;

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:y}", DataAtual);

            ViewBag.NumDiasMes = NumDiasMes;

            // fator de carga demanda maxima ponta
            ViewBag.Dem_MaxP = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[0]);
            DateTime Dem_MaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (Dem_MaxP_DataHora.Year != 2000)
                ViewBag.Dem_MaxP_DataHora = String.Format("{0:d} {1:HH:mm}", Dem_MaxP_DataHora, Dem_MaxP_DataHora);
            else
                ViewBag.Dem_MaxP_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxP_DataHoraN = Dem_MaxP_DataHora;

            // fator de carga demanda maxima ponta simulacao
            ViewBag.Dem_MaxP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[0]);
            DateTime Dem_MaxP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[0]);
            if (Dem_MaxP_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxP_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", Dem_MaxP_DataHora_Sim, Dem_MaxP_DataHora_Sim);
            else
                ViewBag.Dem_MaxP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MaxP_DataHoraN_Sim = Dem_MaxP_DataHora_Sim;

            // demanda maxima fora de ponta indutivo
            ViewBag.Dem_MaxFPI = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[1]);
            DateTime Dem_MaxFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[1]);
            if (Dem_MaxFPI_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPI_DataHora = String.Format("{0:d} {1:HH:mm}", Dem_MaxFPI_DataHora, Dem_MaxFPI_DataHora);
            else
                ViewBag.Dem_MaxFPI_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxFPI_DataHoraN = Dem_MaxFPI_DataHora;

            // demanda maxima fora de ponta indutivo simulacao
            ViewBag.Dem_MaxFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[1]);
            DateTime Dem_MaxFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[1]);
            if (Dem_MaxFPI_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxFPI_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", Dem_MaxFPI_DataHora_Sim, Dem_MaxFPI_DataHora_Sim);
            else
                ViewBag.Dem_MaxFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MaxFPI_DataHoraN_Sim = Dem_MaxFPI_DataHora_Sim;

            // demanda maxima fora ponta capacitivo 
            ViewBag.Dem_MaxFPC = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[2]);
            DateTime Dem_MaxFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[2]);
            if (Dem_MaxFPC_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPC_DataHora = String.Format("{0:d} {1:HH:mm}", Dem_MaxFPC_DataHora, Dem_MaxFPC_DataHora);
            else
                ViewBag.Dem_MaxFPC_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxFPC_DataHoraN = Dem_MaxFPC_DataHora;

            // demanda maxima fora ponta capacitivo simulacao
            ViewBag.Dem_MaxFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[2]);
            DateTime Dem_MaxFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[2]);
            if (Dem_MaxFPC_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxFPC_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", Dem_MaxFPC_DataHora_Sim, Dem_MaxFPC_DataHora_Sim);
            else
                ViewBag.Dem_MaxFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MaxFPC_DataHoraN_Sim = Dem_MaxFPC_DataHora_Sim;

            ViewBag.Dem_MedP = string.Format("{0:#,##0.0}", analise.analise_valor.medio[0]);
            ViewBag.Dem_MedFPI = string.Format("{0:#,##0.0}", analise.analise_valor.medio[1]);
            ViewBag.Dem_MedFPC = string.Format("{0:#,##0.0}", analise.analise_valor.medio[2]);

            ViewBag.FatCargaP_Mes = string.Format("{0:0.0}", analise.analise_valor.minimo[0]);
            ViewBag.FatCargaFPI_Mes = string.Format("{0:0.0}", analise.analise_valor.minimo[1]);
            ViewBag.FatCargaFPC_Mes = string.Format("{0:0.0}", analise.analise_valor.minimo[2]);

            // simulacao
            ViewBag.Dem_MedP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[0]);
            ViewBag.Dem_MedFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[1]);
            ViewBag.Dem_MedFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[2]);

            ViewBag.FatCargaP_Mes_Sim = string.Format("{0:0.0}", analise_sim.analise_valor.minimo[0]);
            ViewBag.FatCargaFPI_Mes_Sim = string.Format("{0:0.0}", analise_sim.analise_valor.minimo[1]);
            ViewBag.FatCargaFPC_Mes_Sim = string.Format("{0:0.0}", analise_sim.analise_valor.minimo[2]);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Fator de Carga Mensal XLS
        private HSSFWorkbook FatCarga_Mensal_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo (%)", "Fora de Ponta Indutivo (%)", "Ponta (%)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < (ViewBag.NumDiasMes + 1); i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // consumo fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.FatCargaFPC[i], _1CellStyle);

                // consumo fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.FatCargaFPI[i], _1CellStyle);

                // consumo ponta
                numeroCelulaXLS(row, 3, ViewBag.FatCargaP[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Carga", "Relatório Mensal", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string[] cab_cons = { "", "Fora de Ponta Capacitivo", "", "Fora de Ponta Indutivo", "", "Ponta", "", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // fator de carga
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Fator de Carga (%)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatCargaFPC_Mes), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatCargaFPI_Mes), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatCargaP_Mes), _1CellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Demanda Média (kW)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP), _1CellStyle);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Demanda Máxima (kW)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Fator de Carga Mensal XLS simulacao
        private HSSFWorkbook FatCarga_Mensal_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho simulacao
            string[] simulacao = { "Fator de Carga - Simulação" };

            // simulação 
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas do titulo simulacao
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 3));

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo (%)", "Fora de Ponta Indutivo (%)", "Ponta (%)" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < (ViewBag.NumDiasMes + 1); i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[i], _dataStyle);

                // consumo fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.FatCargaFPC_Sim[i], _1CellStyle);

                // consumo fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.FatCargaFPI_Sim[i], _1CellStyle);

                // consumo ponta
                numeroCelulaXLS(row, 3, ViewBag.FatCargaP_Sim[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Carga - Simulação", "Relatório Mensal", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string[] cab_cons = { "", "Fora de Ponta Capacitivo", "", "Fora de Ponta Indutivo", "", "Ponta", "", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // fator de carga
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Fator de Carga (%)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatCargaFPC_Mes_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatCargaFPI_Mes_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatCargaP_Mes_Sim), _1CellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Demanda Média (kW)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP_Sim), _1CellStyle);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Demanda Máxima (kW)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN_Sim, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Fator de Carga Anual
        private void FatCarga_Anual(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_ANUAL relatorio = new RELAT_ANUAL();
            RELAT_ANUAL_ANALISE analise = new RELAT_ANUAL_ANALISE();

            RELAT_ANUAL relatorio_sim = new RELAT_ANUAL();
            RELAT_ANUAL_ANALISE analise_sim = new RELAT_ANUAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // calcula valores
            retorno = SmCalcDB_Energia_RelatAnual((char)0, ref config_interface, (char)4, (char)IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   1, 1, 0, 15, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var FatCargaP = new double[14];
            var FatCargaFPI = new double[14];
            var FatCargaFPC = new double[14];
            var Datas = new string[14];
            var DatasN = new DateTime[14];
            var Meses = new string[14];

            // grafico simulacao
            var FatCargaP_Sim = new double[14];
            var FatCargaFPI_Sim = new double[14];
            var FatCargaFPC_Sim = new double[14];
            var Datas_Sim = new string[14];
            var DatasN_Sim = new DateTime[14];

            double FatCarga_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 mes para a primeira barra
            strData = strData.AddMonths(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 14; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Meses[i] = string.Format("{0:MMMM}", strData);

                // formata label simulacao
                Datas_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Sim[i] = strData;

                // proximo mes
                strData = strData.AddMonths(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    FatCargaP[i] = relatorio.registro[0].valor[0];
                    FatCargaFPI[i] = relatorio.registro[0].valor[1];
                    FatCargaFPC[i] = relatorio.registro[0].valor[2];

                    // zera simulacao
                    FatCargaP_Sim[i] = relatorio_sim.registro[0].valor[0];
                    FatCargaFPI_Sim[i] = relatorio_sim.registro[0].valor[1];
                    FatCargaFPC_Sim[i] = relatorio_sim.registro[0].valor[2];
                }

                if (i == 13)
                {
                    // zera
                    FatCargaP[i] = relatorio.registro[11].valor[0];
                    FatCargaFPI[i] = relatorio.registro[11].valor[1];
                    FatCargaFPC[i] = relatorio.registro[11].valor[2];

                    // zera simulacao
                    FatCargaP_Sim[i] = relatorio_sim.registro[11].valor[0];
                    FatCargaFPI_Sim[i] = relatorio_sim.registro[11].valor[1];
                    FatCargaFPC_Sim[i] = relatorio_sim.registro[11].valor[2];
                }

                if (i >= 1 && i <= 12)
                {
                    // copia
                    j = i - 1;

                    FatCargaP[i] = relatorio.registro[j].valor[0];
                    FatCargaFPI[i] = relatorio.registro[j].valor[1];
                    FatCargaFPC[i] = relatorio.registro[j].valor[2];

                    // simulacao
                    FatCargaP_Sim[i] = relatorio_sim.registro[j].valor[0];
                    FatCargaFPI_Sim[i] = relatorio_sim.registro[j].valor[1];
                    FatCargaFPC_Sim[i] = relatorio_sim.registro[j].valor[2];

                    // verifica fator de carga maximo
                    if (FatCargaP[i] > FatCarga_max_grafico)
                        FatCarga_max_grafico = FatCargaP[i];

                    if (FatCargaFPI[i] > FatCarga_max_grafico)
                        FatCarga_max_grafico = FatCargaFPI[i];

                    if (FatCargaFPC[i] > FatCarga_max_grafico)
                        FatCarga_max_grafico = FatCargaFPC[i];

                    // verifica fator de carga maximo simulacao
                    if (FatCargaP_Sim[i] > FatCarga_max_grafico)
                        FatCarga_max_grafico = FatCargaP_Sim[i];

                    if (FatCargaFPI_Sim[i] > FatCarga_max_grafico)
                        FatCarga_max_grafico = FatCargaFPI_Sim[i];

                    if (FatCargaFPC_Sim[i] > FatCarga_max_grafico)
                        FatCarga_max_grafico = FatCargaFPC_Sim[i];
                }
            }

            FatCarga_max_grafico = FatCarga_max_grafico * 1.1;

            ViewBag.FatCargaMaxGrafico = FatCarga_max_grafico;

            ViewBag.FatCargaP = FatCargaP;
            ViewBag.FatCargaFPI = FatCargaFPI;
            ViewBag.FatCargaFPC = FatCargaFPC;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Meses = Meses;

            // simulacao
            ViewBag.FatCargaP_Sim = FatCargaP_Sim;
            ViewBag.FatCargaFPI_Sim = FatCargaFPI_Sim;
            ViewBag.FatCargaFPC_Sim = FatCargaFPC_Sim;
            ViewBag.Datas_Sim = Datas_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.FatorCarga;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Anual;

            // data atual
            ViewBag.DataAtual = string.Format("{0:yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:yyyy}", DataAtual);

            // fator de carga demanda maxima ponta
            ViewBag.Dem_MaxP = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[0]);
            DateTime Dem_MaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (Dem_MaxP_DataHora.Year != 2000)
                ViewBag.Dem_MaxP_DataHora = String.Format("{0:d} {1:HH:mm}", Dem_MaxP_DataHora, Dem_MaxP_DataHora);
            else
                ViewBag.Dem_MaxP_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxP_DataHoraN = Dem_MaxP_DataHora;

            // fator de carga demanda maxima ponta simulacao
            ViewBag.Dem_MaxP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[0]);
            DateTime Dem_MaxP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[0]);
            if (Dem_MaxP_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxP_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", Dem_MaxP_DataHora_Sim, Dem_MaxP_DataHora_Sim);
            else
                ViewBag.Dem_MaxP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MaxP_DataHoraN_Sim = Dem_MaxP_DataHora_Sim;

            // demanda maxima fora ponta indutivo
            ViewBag.Dem_MaxFPI = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[1]);
            DateTime Dem_MaxFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[1]);
            if (Dem_MaxFPI_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPI_DataHora = String.Format("{0:d} {1:HH:mm}", Dem_MaxFPI_DataHora, Dem_MaxFPI_DataHora);
            else
                ViewBag.Dem_MaxFPI_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxFPI_DataHoraN = Dem_MaxFPI_DataHora;

            // demanda maxima fora ponta indutivo simulacao
            ViewBag.Dem_MaxFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[1]);
            DateTime Dem_MaxFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[1]);
            if (Dem_MaxFPI_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxFPI_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", Dem_MaxFPI_DataHora_Sim, Dem_MaxFPI_DataHora_Sim);
            else
                ViewBag.Dem_MaxFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MaxFPI_DataHoraN_Sim = Dem_MaxFPI_DataHora_Sim;

            // demanda maxima fora ponta capacitivo
            ViewBag.Dem_MaxFPC = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[2]);
            DateTime Dem_MaxFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[2]);
            if (Dem_MaxFPC_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPC_DataHora = String.Format("{0:d} {1:HH:mm}", Dem_MaxFPC_DataHora, Dem_MaxFPC_DataHora);
            else
                ViewBag.Dem_MaxFPC_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxFPC_DataHoraN = Dem_MaxFPC_DataHora;

            // demanda maxima fora ponta capacitivo simulacao
            ViewBag.Dem_MaxFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[2]);
            DateTime Dem_MaxFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[2]);
            if (Dem_MaxFPC_DataHora_Sim.Year != 2000)
                ViewBag.Dem_MaxFPC_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", Dem_MaxFPC_DataHora_Sim, Dem_MaxFPC_DataHora_Sim);
            else
                ViewBag.Dem_MaxFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.Dem_MaxFPC_DataHoraN_Sim = Dem_MaxFPC_DataHora_Sim;

            ViewBag.Dem_MedP = string.Format("{0:#,##0.0}", analise.analise_valor.medio[0]);
            ViewBag.Dem_MedFPI = string.Format("{0:#,##0.0}", analise.analise_valor.medio[1]);
            ViewBag.Dem_MedFPC = string.Format("{0:#,##0.0}", analise.analise_valor.medio[2]);

            ViewBag.FatCargaP_Ano = string.Format("{0:0.0}", analise.analise_valor.minimo[0]);
            ViewBag.FatCargaFPI_Ano = string.Format("{0:0.0}", analise.analise_valor.minimo[1]);
            ViewBag.FatCargaFPC_Ano = string.Format("{0:0.0}", analise.analise_valor.minimo[2]);

            // simulacao
            ViewBag.Dem_MedP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[0]);
            ViewBag.Dem_MedFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[1]);
            ViewBag.Dem_MedFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[2]);

            ViewBag.FatCargaP_Ano_Sim = string.Format("{0:0.0}", analise_sim.analise_valor.minimo[0]);
            ViewBag.FatCargaFPI_Ano_Sim = string.Format("{0:0.0}", analise_sim.analise_valor.minimo[1]);
            ViewBag.FatCargaFPC_Ano_Sim = string.Format("{0:0.0}", analise_sim.analise_valor.minimo[2]);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddYears(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Fator de Carga Anual XLS
        private HSSFWorkbook FatCarga_Anual_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo (%)", "Fora de Ponta Indutivo (%)", "Ponta (%)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 13; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // consumo fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.FatCargaFPC[i], _1CellStyle);

                // consumo fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.FatCargaFPI[i], _1CellStyle);

                // consumo ponta
                numeroCelulaXLS(row, 3, ViewBag.FatCargaP[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Carga", "Relatório Anual", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string[] cab_cons = { "", "Fora de Ponta Capacitivo", "", "Fora de Ponta Indutivo", "", "Ponta", "", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // fator de carga
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Fator de Carga (%)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatCargaFPC_Ano), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatCargaFPI_Ano), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatCargaP_Ano), _1CellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Demanda Média (kW)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP), _1CellStyle);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Demanda Máxima (kW)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Fator de Carga Anual XLS simulacao
        private HSSFWorkbook FatCarga_Anual_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho simulacao
            string[] simulacao = { "Fator de Carga - Simulação" };

            // simulação 
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas do titulo simulacao
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 4));

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo (%)", "Fora de Ponta Indutivo (%)", "Ponta (%)" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 13; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[i], _dataStyle);

                // consumo fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.FatCargaFPC_Sim[i], _1CellStyle);

                // consumo fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.FatCargaFPI_Sim[i], _1CellStyle);

                // consumo ponta
                numeroCelulaXLS(row, 3, ViewBag.FatCargaP_Sim[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Carga - Simulação", "Relatório Anual", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string[] cab_cons = { "", "Fora de Ponta Capacitivo", "", "Fora de Ponta Indutivo", "", "Ponta", "", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // fator de carga
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Fator de Carga (%)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatCargaFPC_Ano_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatCargaFPI_Ano_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatCargaP_Ano_Sim), _1CellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // demanda media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Demanda Média (kW)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MedFPC_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MedFPI_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MedP_Sim), _1CellStyle);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Demanda Máxima (kW)", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Dem_MaxFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Dem_MaxFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.Dem_MaxFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.Dem_MaxFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.Dem_MaxP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.Dem_MaxP_DataHoraN_Sim, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }
    }
}