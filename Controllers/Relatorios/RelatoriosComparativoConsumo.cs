﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;
using System.Text.RegularExpressions;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class RelatoriosController
    {

        // GET: Relatorio Comparativo Consumo
        public ActionResult Relat_Comparativo_Consumo(int IDCliente)
        {
            // comparativo de consumo
            return (Relat_Comparativo_Consumo_Show(IDCliente));
        }

        // GET: Relatorio Comparativo Consumo
        private ActionResult Relat_Comparativo_Consumo_Show(int IDCliente)
        {
            // tela de ajuda - comparativo consumo
            CookieStore.SalvaCookie_String("<PERSON><PERSON>a<PERSON>ju<PERSON>", "Contato");

            // salva cookie 
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Relat_Comparativo_Consumo");

            // le cookies
            LeCookies_SmartEnergy();

            // período 1
            DateTime periodo1_ini = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 0, 0);
            DateTime periodo1_fim = periodo1_ini.AddMonths(1);

            // período 2
            DateTime periodo2_ini = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 0, 0);
            DateTime periodo2_fim = periodo2_ini.AddMonths(1);

            // le cookie datahora
            DateTime periodo1_ini_cookie = CookieStore.LeCookie_Datahora("Periodo1_Ini");
            DateTime periodo1_fim_cookie = CookieStore.LeCookie_Datahora("Periodo1_Fim");
            DateTime periodo2_ini_cookie = CookieStore.LeCookie_Datahora("Periodo2_Ini");
            DateTime periodo2_fim_cookie = CookieStore.LeCookie_Datahora("Periodo2_Fim");

            // verifica se existe datahora no cookie
            if (periodo1_ini_cookie.Year != 2000 && periodo1_fim_cookie.Year != 2000)
            {
                // copia data do cookie 
                periodo1_ini = periodo1_ini_cookie;
                periodo1_fim = periodo1_fim_cookie;
            }

            // verifica se existe datahora no cookie
            if (periodo2_ini_cookie.Year != 2000 && periodo2_fim_cookie.Year != 2000)
            {
                // copia data do cookie 
                periodo2_ini = periodo2_ini_cookie;
                periodo2_fim = periodo2_fim_cookie;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Periodo1_Ini", periodo1_ini);
            CookieStore.SalvaCookie_Datahora("Periodo1_Fim", periodo1_fim);
            CookieStore.SalvaCookie_Datahora("Periodo2_Ini", periodo2_ini);
            CookieStore.SalvaCookie_Datahora("Periodo2_Fim", periodo2_fim);

            // data e hora atual
            ViewBag.Data_Periodo1_Ini = string.Format("{0:d}", periodo1_ini);
            ViewBag.Hora_Periodo1_Ini = string.Format("{0:HH:mm}", periodo1_ini);
            ViewBag.Data_Periodo1_Fim = string.Format("{0:d}", periodo1_fim);
            ViewBag.Hora_Periodo1_Fim = string.Format("{0:HH:mm}", periodo1_fim);

            ViewBag.Data_Periodo2_Ini = string.Format("{0:d}", periodo2_ini);
            ViewBag.Hora_Periodo2_Ini = string.Format("{0:HH:mm}", periodo2_ini);
            ViewBag.Data_Periodo2_Fim = string.Format("{0:d}", periodo2_fim);
            ViewBag.Hora_Periodo2_Fim = string.Format("{0:HH:mm}", periodo2_fim);

            ViewBag.DataTextoAtual_Periodo1_Ini = string.Format("{0:d} {1:HH:mm}", periodo1_ini, periodo1_ini);
            ViewBag.DataTextoAtual_Periodo1_Fim = string.Format("{0:d} {1:HH:mm}", periodo1_fim, periodo1_fim);

            ViewBag.DataTextoAtual_Periodo2_Ini = string.Format("{0:d} {1:HH:mm}", periodo2_ini, periodo2_ini);
            ViewBag.DataTextoAtual_Periodo2_Fim = string.Format("{0:d} {1:HH:mm}", periodo2_fim, periodo2_fim);

            ViewBag.DataAtualN_Periodo1_Ini = periodo1_ini;
            ViewBag.DataAtualN_Periodo1_Fim = periodo1_fim;

            ViewBag.DataAtualN_Periodo2_Ini = periodo1_ini;
            ViewBag.DataAtualN_Periodo2_Fim = periodo1_fim;


            // le grupos de medicoes
            GrupoMedicoesMetodos medGrupoMetodos = new GrupoMedicoesMetodos();
            List<GrupoMedicoesDominio> medGrupos = medGrupoMetodos.ListarPorIDCliente(IDCliente);
            ViewBag.medGrupos = medGrupos;

            // grupo de medições selecionado
            int IDGrupoMedicoes_selecionado = CookieStore.LeCookie_Int("GrupoMedicoes");

            if (IDGrupoMedicoes_selecionado < 0)
            {
                IDGrupoMedicoes_selecionado = 0;
            }

            ViewBag.IDGrupoMedicoes_selecionado = IDGrupoMedicoes_selecionado;
            CookieStore.SalvaCookie_Int("GrupoMedicoes", IDGrupoMedicoes_selecionado);


            // resultado
            List<COMPARATIVO_CONSUMO> comparativo_consumo_resultado = new List<COMPARATIVO_CONSUMO>();
            ViewBag.comparativo_consumo_resultado = comparativo_consumo_resultado;

            return View();
        }

        // GET: Comparativo Consumo Atualizar
        public ActionResult Relat_Comparativo_Consumo_Atualizar(int GrupoMedicoes, string Periodo1_Ini, string Periodo1_Fim, string Periodo2_Ini, string Periodo2_Fim)
        {
            // pega data
            DateTime periodo1_ini = DateTime.Parse(Periodo1_Ini);
            DateTime periodo1_fim = DateTime.Parse(Periodo1_Fim);
            DateTime periodo2_ini = DateTime.Parse(Periodo2_Ini);
            DateTime periodo2_fim = DateTime.Parse(Periodo2_Fim);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Periodo1_Ini", periodo1_ini);
            CookieStore.SalvaCookie_Datahora("Periodo1_Fim", periodo1_fim);
            CookieStore.SalvaCookie_Datahora("Periodo2_Ini", periodo2_ini);
            CookieStore.SalvaCookie_Datahora("Periodo2_Fim", periodo2_fim);

            // GrupoMedicoes
            CookieStore.SalvaCookie_Int("GrupoMedicoes", GrupoMedicoes);

            // le cookies
            LeCookies_SmartEnergy();


            // data e hora atual
            ViewBag.Data_Periodo1_Ini = string.Format("{0:d}", periodo1_ini);
            ViewBag.Hora_Periodo1_Ini = string.Format("{0:HH:mm}", periodo1_ini);
            ViewBag.Data_Periodo1_Fim = string.Format("{0:d}", periodo1_fim);
            ViewBag.Hora_Periodo1_Fim = string.Format("{0:HH:mm}", periodo1_fim);

            ViewBag.Data_Periodo2_Ini = string.Format("{0:d}", periodo2_ini);
            ViewBag.Hora_Periodo2_Ini = string.Format("{0:HH:mm}", periodo2_ini);
            ViewBag.Data_Periodo2_Fim = string.Format("{0:d}", periodo2_fim);
            ViewBag.Hora_Periodo2_Fim = string.Format("{0:HH:mm}", periodo2_fim);

            ViewBag.DataTextoAtual_Periodo1_Ini = string.Format("{0:d} {1:HH:mm}", periodo1_ini, periodo1_ini);
            ViewBag.DataTextoAtual_Periodo1_Fim = string.Format("{0:d} {1:HH:mm}", periodo1_fim, periodo1_fim);

            ViewBag.DataTextoAtual_Periodo2_Ini = string.Format("{0:d} {1:HH:mm}", periodo2_ini, periodo2_ini);
            ViewBag.DataTextoAtual_Periodo2_Fim = string.Format("{0:d} {1:HH:mm}", periodo2_fim, periodo2_fim);

            ViewBag.DataAtualN_Periodo1_Ini = periodo1_ini;
            ViewBag.DataAtualN_Periodo1_Fim = periodo1_fim;

            ViewBag.DataAtualN_Periodo2_Ini = periodo1_ini;
            ViewBag.DataAtualN_Periodo2_Fim = periodo1_fim;

            // retorna status
            return Json(0, JsonRequestBehavior.AllowGet);
        }

        // GET: Comparativo Consumo Print
        public ActionResult Relat_Comparativo_Consumo_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Relat_Comparativo_Consumo_Show(IDCliente);

            // resultado
            ViewBag.comparativo_consumo_resultado = lista_comparativo_consumo.Keys.Contains(id) ? lista_comparativo_consumo[id] : null;

            // imprime
            return View();
        }

        // GET: Comparativo Consumo EMAIL
        public async Task<ActionResult> Relat_Comparativo_Consumo_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Relat_Comparativo_Consumo_Show(IDCliente);

            // resultado
            ViewBag.comparativo_consumo_resultado = lista_comparativo_consumo.Keys.Contains(id) ? lista_comparativo_consumo[id] : null;

            // data e hora
            DateTime data_atual = DateTime.Now;

            // tiro caracteres especiais do nome
            Regex regex = new Regex("[^a-zA-Z0-9]");
            string novo_nome = regex.Replace(cliente.Nome, "");

            // nome do arquivo
            string nomeArquivo = string.Format("ComparativoConsumo_{0}_ID{1:000000}.pdf", novo_nome, IDCliente);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_Comparativo_Consumo_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Landscape,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "ComparativoConsumoEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual_Periodo1_Ini", ViewBag.DataTextoAtual_Periodo1_Ini);
            message = message.Replace("ViewBag.DataTextoAtual_Periodo1_Fim", ViewBag.DataTextoAtual_Periodo1_Fim);
            message = message.Replace("ViewBag.DataTextoAtual_Periodo2_Ini", ViewBag.DataTextoAtual_Periodo2_Ini);
            message = message.Replace("ViewBag.DataTextoAtual_Periodo2_Fim", ViewBag.DataTextoAtual_Periodo2_Fim);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Comparativo Consumo PDF
        public ActionResult Relat_Comparativo_Consumo_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Relat_Comparativo_Consumo_Show(IDCliente);

            // resultado
            ViewBag.comparativo_consumo_resultado = lista_comparativo_consumo.Keys.Contains(id) ? lista_comparativo_consumo[id] : null;

            // data e hora
            DateTime data_atual = DateTime.Now;

            // tiro caracteres especiais do nome
            Regex regex = new Regex("[^a-zA-Z0-9]");
            string novo_nome = regex.Replace(cliente.Nome, "");

            // nome do arquivo
            string nomeArquivo = string.Format("ComparativoConsumo_{0}_ID{1:000000}.pdf", novo_nome, IDCliente);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_Comparativo_Consumo_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Landscape,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Comparativo Consumo XLS
        public ActionResult Relat_Comparativo_Consumo_XLS()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Relat_Comparativo_Consumo_Show(IDCliente);

            // resultado
            ViewBag.comparativo_consumo_resultado = lista_comparativo_consumo.Keys.Contains(id) ? lista_comparativo_consumo[id] : null;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_Comparativo_Consumo(IDCliente);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // tiro caracteres especiais do nome
            Regex regex = new Regex("[^a-zA-Z0-9]");
            string novo_nome = regex.Replace(cliente.Nome, "");

            // nome do arquivo
            string nomeArquivo = string.Format("ComparativoConsumo_{0}_ID{1:000000}.xls", novo_nome, IDCliente);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Comparativo Consumo XLS Download
        [HttpGet]
        public virtual ActionResult Relat_Comparativo_Consumo_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }


        // estruturas
        private static IDictionary<Guid, int> tasks_comparativo_consumo = new Dictionary<Guid, int>();
        private List<COMPARATIVO_CONSUMO> lista_comparativo_consumo_tmp = new List<COMPARATIVO_CONSUMO>();
        private static IDictionary<Guid, List<COMPARATIVO_CONSUMO>> lista_comparativo_consumo = new Dictionary<Guid, List<COMPARATIVO_CONSUMO>>();

        public ActionResult Relat_Comparativo_Consumo_IniciaCalculo()
        {
            // taskID
            Guid taskId = Guid.NewGuid();
            tasks_comparativo_consumo.Add(taskId, 0);

            // salva cookie
            CookieStore.SalvaCookie_GUID("taskID", taskId);

            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // grupo medições
            int GrupoMedicoes = CookieStore.LeCookie_Int("GrupoMedicoes");

            // le cookie datahora
            DateTime periodo1_ini = CookieStore.LeCookie_Datahora("Periodo1_Ini");
            DateTime periodo1_fim = CookieStore.LeCookie_Datahora("Periodo1_Fim");
            DateTime periodo2_ini = CookieStore.LeCookie_Datahora("Periodo2_Ini");
            DateTime periodo2_fim = CookieStore.LeCookie_Datahora("Periodo2_Fim");


            // limpa lista
            lista_comparativo_consumo_tmp.Clear();

            // task
            Task.Factory.StartNew(() =>
            {
                // le medicoes
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                List<MedicoesDominio> medicoes = medicoesMetodos.ListarTodos(IDCliente, ViewBag._ConfigMed);

                // grupo medições
                List<GrupoMedicoesMedDominio> grupoMeds = new List<GrupoMedicoesMedDominio>();

                // lista medições
                List<MedicoesDominio> listaMedicoes = new List<MedicoesDominio>();

                // percorre medicoes
                if (medicoes != null)
                {
                    // verifica se grupo de medições selecionado
                    if (GrupoMedicoes > 0)
                    {
                        // lê medições do grupo
                        GrupoMedicoesMedMetodos grupoMedMetodos = new GrupoMedicoesMedMetodos();
                        grupoMeds = grupoMedMetodos.ListarPorIDGrupoMedicoes(GrupoMedicoes);
                    }

                    // percorre medicoes
                    foreach (MedicoesDominio medicao in medicoes)
                    {
                        // adicionar
                        bool adicionar = false;

                        // verifica se grupo de medições selecionado
                        if (GrupoMedicoes > 0 && grupoMeds != null)
                        {
                            // verifica se medição está no grupo
                            GrupoMedicoesMedDominio med = grupoMeds.Find(x => x.IDMedicao == medicao.IDMedicao);

                            // verifica se achou
                            if (med != null)
                            {
                                if (med.IDMedicao == medicao.IDMedicao)
                                {
                                    // adicionar
                                    adicionar = true;
                                }
                            }
                        }
                        else
                        {
                            // adicionar
                            adicionar = true;
                        }

                        // verifica se deve adicionar
                        if (adicionar)
                        {
                            listaMedicoes.Add(medicao);
                        }
                    }
                }

                // barra de progresso
                int total = 0;
                int atual = 0;
                
                // percorre medicoes
                if (listaMedicoes != null)
                {
                    // barra de progresso
                    total = listaMedicoes.Count();

                    // percorre medicoes
                    foreach (MedicoesDominio medicao in listaMedicoes)
                    {
                        // update task progress
                        double progresso = ((double)atual / (double)total) * 100;
                        tasks_comparativo_consumo[taskId] = (int)progresso;
                        atual++;

                        // verifica se nao eh energia
                        if (medicao.IDTipoMedicao != TIPO_MEDICAO.ENERGIA && medicao.IDTipoMedicao != TIPO_MEDICAO.ENERGIA_FORMULA)
                        {
                            continue;
                        }

                        // calcula consumo período 1
                        EN_Metodos enMetodos = new EN_Metodos();
                        double consumo_periodo1 = enMetodos.ConsumoTotalPeriodo(medicao.IDCliente, medicao.IDMedicao, periodo1_ini, periodo1_fim);

                        // calcula consumo período 2
                        double consumo_periodo2 = enMetodos.ConsumoTotalPeriodo(medicao.IDCliente, medicao.IDMedicao, periodo2_ini, periodo2_fim);


                        // coloca resultado na lista temporaria
                        COMPARATIVO_CONSUMO comparativo_consumo = new COMPARATIVO_CONSUMO();
                        comparativo_consumo.IDMedicao = medicao.IDMedicao;
                        comparativo_consumo.IDTipoMedicao = medicao.IDTipoMedicao;
                        comparativo_consumo.Nome_Medicao = medicao.Nome;
                        comparativo_consumo.consumo_periodo1 = consumo_periodo1;
                        comparativo_consumo.consumo_periodo2 = consumo_periodo2;
                        comparativo_consumo.variacao = 0;
                        comparativo_consumo.retorno = 0;

                        // variação
                        if (consumo_periodo1 > 0.0 && consumo_periodo2 > 0.0)
                        {
                            comparativo_consumo.variacao = ((consumo_periodo2 - consumo_periodo1) / consumo_periodo1) * 100;
                        }
                       
                        // coloca na lista
                        lista_comparativo_consumo_tmp.Add(comparativo_consumo);
                    }
                }

                // coloca resultado na lista
                lista_comparativo_consumo.Add(taskId, new List<COMPARATIVO_CONSUMO>(lista_comparativo_consumo_tmp));

                // terminou
                tasks_comparativo_consumo.Remove(taskId);
            });

            // retorna status
            return Json(taskId, JsonRequestBehavior.AllowGet);
        }

        public ActionResult Relat_Comparativo_Consumo_Progress(Guid id)
        {
            return Json(tasks_comparativo_consumo.Keys.Contains(id) ? tasks_comparativo_consumo[id] : 100, JsonRequestBehavior.AllowGet);
        }

        public PartialViewResult _Comparativo_Consumo(Guid id)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // resultado
            ViewBag.comparativo_consumo_resultado = lista_comparativo_consumo.Keys.Contains(id) ? lista_comparativo_consumo[id] : null;

            return PartialView();
        }


        // Comparativo Consumo XLS
        private HSSFWorkbook XLS_Comparativo_Consumo(int IDCliente)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);


            //
            // MEDICOES
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Comparativo de Consumo");

            // cabecalho
            string[] cabecalho = { "ID", "Medições", "Período 1 (kWh)", "Período 2 (kWh)", "Variação (%)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            List<COMPARATIVO_CONSUMO> comparativo_consumo_resultado = ViewBag.comparativo_consumo_resultado;
            int i;
            IRow row;

            // percorre valores
            if (comparativo_consumo_resultado != null)
            {
                foreach (COMPARATIVO_CONSUMO med in comparativo_consumo_resultado)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex++);

                    // medicao
                    numeroCelulaXLS(row, 0, med.IDMedicao, _intCellStyle);
                    textoCelulaXLS(row, 1, med.Nome_Medicao);
                    numeroCelulaXLS(row, 2, med.consumo_periodo1, _1CellStyle);
                    numeroCelulaXLS(row, 3, med.consumo_periodo2, _1CellStyle);
                    numeroCelulaXLS(row, 4, med.variacao, _1CellStyle);
                }
            }

            // largura de cada coluna
            sheet.SetColumnWidth(0, 2000);
            for (i = 1; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS_ComparativoConsumo(workbook, sheet, _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(4).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }
    }
}