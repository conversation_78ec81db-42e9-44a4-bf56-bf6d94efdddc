﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {
        //
        // EVENTOS DE USUARIO
        //

        // GET: Relatorio Eventos Usuario
        public ActionResult Relat_EventosUsuario(int IDCliente)
        {
            // verifica se tem cliente
            if (IDCliente > 0)
            {
                // salva cookie
                CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);

                // le contrato cliente
                ClientesMetodos clienteMetodos = new ClientesMetodos();
                ClientesDominio cli = clienteMetodos.ListarPorId(IDCliente);
                CookieStore.SalvaCookie_Int("IDTipoContrato", cli.IDTipoContrato);
                CookieStore.SalvaCookie_Int("IDTipoSupervisao", cli.IDTipoSupervisao);
                CookieStore.SalvaCookie_String("Nome_GrupoUnidades", cli.Nome_GrupoUnidades);
                CookieStore.SalvaCookie_String("Nome_Unidades", cli.Nome_Unidades);
                CookieStore.SalvaCookie_Int("IDTipoGrafico_Demanda", cli.IDTipoGrafico_Demanda);
            }
            else
            {
                // inicialmente cliente nao selecionado
                CookieStore.SalvaCookie_Int("_IDCliente", -10);
                CookieStore.SalvaCookie_Int("IDTipoContrato", -10);
                CookieStore.SalvaCookie_Int("IDTipoSupervisao", 0);
                CookieStore.SalvaCookie_String("Nome_GrupoUnidades", "Grupo de Unidades");
                CookieStore.SalvaCookie_String("Nome_Unidades", "Unidades");
                CookieStore.SalvaCookie_Int("IDTipoGrafico_Demanda", 0);
            }

            CookieStore.DeleteCookie("Relat_IDUsuario");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Relat_EventosUsuario");

            // relatorio eventos
            return (Relat_EventosUsuario_Show(IDCliente));
        }

        // GET: Relatorio Eventos Usuario
        private ActionResult Relat_EventosUsuario_Show(int IDCliente, int tipo_arquivo = 0)
        {
            // tipo do relatorio
            int TipoRelat = 11;

            // tela de ajuda - relatorios eventos usuario
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie 
            CookieStore.SalvaCookie_Int("Relat_Tipo", TipoRelat);

            // le cookies
            LeCookies_SmartEnergy();

            // data atual
            DateTime ts = DateTime.Now;
            DateTime datahora_ultima = new DateTime(ts.Year, ts.Month, 1, 0, 0, 0);
            DATAHORA data_hora = new DATAHORA();
            DATAHORA data_hora_fim = new DATAHORA();

            // le cookie datahora (atual ou inicial)
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            // verifica se existe datahora no cookie
            if (datahora_cookie.Year != 2000)
            {
                // copia data do cookie para ultima
                datahora_ultima = datahora_cookie;
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_ultima);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // le cookie datahora (final)
            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Relat_DataFim");

            // verifica se existe datahora no cookie
            if (datahora_cookie_fim.Year == 2000)
            {
                // copia data do cookie 
                datahora_cookie_fim = new DateTime(ts.Year, ts.Month, 1, 0, 0, 0);
                datahora_cookie_fim = datahora_cookie_fim.AddMonths(1);
            }

            // verifica se fim eh menor que inicio
            if (datahora_cookie_fim <= datahora_ultima)
            {
                // periodo de pelo menos 1 dia
                datahora_cookie_fim = datahora_ultima.AddDays(1);
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora_fim, datahora_cookie_fim);

            // salva cookie datahora (final)
            CookieStore.SalvaCookie_Datahora("Relat_DataFim", datahora_cookie_fim);

            // tipo do relatorio
            ViewBag.TipoRelat = TipoRelat;

            // seta para diario
            ViewBag.Relat_TipoPeriodo = 0;

            // View do relatorio
            string viewRelatorio = "_EventosUsuario";

            //
            // le eventos
            //

            // Tipo acesso
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // le eventos
            EventosUsuario(IDCliente, data_hora, data_hora_fim, IDTipoAcesso, IDConsultor);


            // verifica se PDF
            if (tipo_arquivo == 1)
            {
                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.pdf", viewRelatorio, IDCliente, data_atual);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Portrait,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna nome do arquivo
                var returnedData = new
                {
                    nomeArquivo = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // verifica se XLS
            if (tipo_arquivo == 2)
            {
                // Planilha Excel
                var workbook = new HSSFWorkbook();

                // monta XLS
                workbook = EventosUsuario_XLS(IDCliente);

                // gera identificador unico aonde o arquivo sera guardado
                string handle = Guid.NewGuid().ToString();

                using (MemoryStream memoryStream = new MemoryStream())
                {
                    workbook.Write(memoryStream);
                    memoryStream.Position = 0;
                    Session[handle] = memoryStream.ToArray();
                }

                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.xls", viewRelatorio, IDCliente, data_atual);

                // retorna nome do arquivo
                var returnedData = new
                {
                    FileGuid = handle,
                    FileName = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // verifica se EMAIL
            if (tipo_arquivo == 3)
            {
                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.pdf", viewRelatorio, IDCliente, data_atual);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Portrait,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna 
                ViewBag.caminhoCompleto = caminhoCompleto;
                var returnedData = new
                {
                    caminhoCompleto = caminhoCompleto
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            return View();
        }

        // Eventos
        private void EventosUsuario(int IDCliente, DATAHORA data_hora_ini, DATAHORA data_hora_fim, int IDTipoAcesso, int IDConsultor) 
        {
            // le tipos eventos
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listaTiposEventos = listatiposMetodos.ListarTodos("TipoEventosUsuario",true,0);
            ViewBag.listatiposEventos = listaTiposEventos;

            // converte data e hora
            DateTime DataAtual = Funcoes_Converte.ConverteDataHora2DateTime(data_hora_ini);
            DateTime DataIni = Funcoes_Converte.ConverteDataHora2DateTime(data_hora_ini);
            DateTime DataFim = Funcoes_Converte.ConverteDataHora2DateTime(data_hora_fim);

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.EventosUsuario;
            ViewBag.PeriodoRelat = " ";

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);

            // data e hora atual
            ViewBag.DataIni = string.Format("{0:d}", DataIni);
            ViewBag.HoraIni = string.Format("{0:HH:mm:ss}", DataIni);
            ViewBag.DataFim = string.Format("{0:d}", DataFim);
            ViewBag.HoraFim = string.Format("{0:HH:mm:ss}", DataFim);

            ViewBag.DataAtualN = DataIni;
            ViewBag.DataFinalN = DataFim;

            ViewBag.DataTextoAtualIni = string.Format("{0:d} {1:HH:mm:ss}", DataIni, DataIni);
            ViewBag.DataTextoAtualFim = string.Format("{0:d} {1:HH:mm:ss}", DataFim, DataFim);

            // lista de usuarios do cliente
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            List<UsuarioDominio> listaUsuarios = new List<UsuarioDominio>();

            // lista de usuarios gestores (todos)
            List<UsuarioDominio> listaGestores = usuarioMetodos.ListarTodosGestores();

            // lista de clientes (todos)
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clienteMetodos.ListarTodos();

            // caso cliente selecionado
            if (IDCliente > 0)
            {
                // cliente
                ClientesDominio cliente = clienteMetodos.ListarPorId(IDCliente);

                // lista de usuarios do cliente
                listaUsuarios = usuarioMetodos.ListarPorIDCliente(IDCliente);
            }
            else
            {
                // verifica se é GESTAL
                if (isUser.isGESTAL(IDTipoAcesso))
                {
                    // lista de usuarios admins (todos)
                    List<UsuarioDominio> listaUsuarios_Admins = usuarioMetodos.ListarAdmins();

                    // lista de usuarios gestores (todos)
                    listaUsuarios = usuarioMetodos.ListarTodosGestores();

                    // insere admins
                    foreach (UsuarioDominio usuario in listaUsuarios_Admins)
                    {
                        // adiciona
                        listaUsuarios.Add(usuario);
                    }
                }

                // verifica se é gestor administrador
                if (IDTipoAcesso == TIPO_ACESSO.CONSULTOR || IDTipoAcesso == TIPO_ACESSO.CONSULTOR_ADMIN)
                {
                    // lista de usuarios gestores (IDConsultor)
                    listaUsuarios = usuarioMetodos.ListarTodosGestores(IDConsultor);
                }
            }


            // lista tipos de acesso
            List<ListaTiposDominio> listatiposAcesso = listatiposMetodos.ListarTodos("TipoAcesso");
            ViewBag.listaTipoAcesso = listatiposAcesso;

            // coloca Tipo Acesso nos usuários
            if (listaUsuarios != null)
            {
                foreach (UsuarioDominio user in listaUsuarios)
                {
                    // nome do usuario
                    user.NomeUsuario = string.Format("[{0:000000}] {1}", user.IDUsuario, user.NomeUsuario);

                    // nome do cliente e gestor
                    user.NomeCliente = "---";
                    user.NomeGestor = "---";

                    // gestor
                    UsuarioDominio gestor = new UsuarioDominio();

                    // verifica tipo acesso do usuário
                    switch (user.IDTipoAcesso)
                    {
                        case TIPO_ACESSO.GESTAL_ADMIN:
                        case TIPO_ACESSO.GESTAL_SUPORTE:
                        case TIPO_ACESSO.GESTAL_VENDAS:
                        case TIPO_ACESSO.GESTAL_PRODUCAO:
                            user.NomeCliente = "GESTAL";
                            user.NomeGestor = "GESTAL";
                            break;

                        case TIPO_ACESSO.CONSULTOR:
                        case TIPO_ACESSO.CONSULTOR_ADMIN:
                        case TIPO_ACESSO.CONSULTOR_OPER:

                            // gestor
                            gestor = listaGestores.Find(x => x.IDUsuario == user.IDCliente);

                            if (gestor != null)
                            {
                                user.NomeGestor = gestor.NomeUsuario;
                            }
                            break;

                        case TIPO_ACESSO.CLIENTE_ADMIN:
                        case TIPO_ACESSO.CLIENTE_OPER:

                            // cliente
                            ClientesDominio cliente = listaClientes.Find(x => x.IDCliente == user.IDCliente);

                            if (cliente != null)
                            {
                                user.NomeCliente = cliente.Fantasia;

                                // gestor
                                gestor = listaGestores.Find(x => x.IDUsuario == cliente.IDConsultor);

                                if (gestor != null)
                                {
                                    user.NomeGestor = gestor.NomeUsuario;
                                }
                            }

                            break;
                    }
                }
            }

            // lista de usuarios do cliente
            ViewBag.listaUsuarios = listaUsuarios;

            // le eventos de TODOS usuarios
            UsuarioEventoMetodos eventosMetodos = new UsuarioEventoMetodos();
            List<UsuarioEventoDominio> listaEventos = eventosMetodos.ListarPorId(0, DataIni, DataFim);

            // lista de eventos
            List<EventosUsuarioDescricao> listaEventosDescricao = new List<EventosUsuarioDescricao>();

            // verifica se tem eventos
            if (listaEventos != null)
            {
                foreach(UsuarioEventoDominio evento in listaEventos)
                {
                    // encontra usuario do evento
                    List<UsuarioDominio> user = listaUsuarios.FindAll(x => x.IDUsuario == evento.IDUsuario);

                    if( user.Count <= 0 )
                    {
                        continue;
                    }

                    // eventos
                    EventosUsuarioDescricao eventoDescricao = new EventosUsuarioDescricao();

                    // data e hora
                    eventoDescricao.DataHoraN = evento.DataHora;
                    eventoDescricao.DataHora = string.Format("{0:d} {1:HH:mm:ss}", evento.DataHora, evento.DataHora);
                    eventoDescricao.DataHora_Sort = String.Format("{0:yyyyMMddHHmmss}", evento.DataHora);

                    // IDUsuario
                    eventoDescricao.IDUsuario = evento.IDUsuario;
                    eventoDescricao.NomeUsuario = user[0].NomeUsuario;
                    eventoDescricao.IDTipoAcesso = user[0].IDTipoAcesso;

                    // tipo acesso
                    ListaTiposDominio tipo_acesso = listatiposAcesso.Find(x => x.ID == user[0].IDTipoAcesso);
                    eventoDescricao.NomeTipoAcesso = "---";

                    if (tipo_acesso != null)
                    {
                        eventoDescricao.NomeTipoAcesso = tipo_acesso.Descricao;
                    }

                    // Nome do cliente e gestor
                    eventoDescricao.NomeCliente = user[0].NomeCliente;
                    eventoDescricao.NomeGestor = user[0].NomeGestor;
                    
                    // descricao
                    string evento_texto = "";

                    switch(evento.Evento)
                    {
                        case USUARIO_EVENTO.LOGIN:
                            evento_texto = "Login";
                            eventoDescricao.Tipo = 1;
                            break;

                        case USUARIO_EVENTO.LOGOUT:
                            evento_texto = "Logout";
                            eventoDescricao.Tipo = 1;
                            break;

                        case USUARIO_EVENTO.ERRO_SENHA:
                            evento_texto = string.Format("Erro de Login (Senha Incorreta - Tentativa {0}/5)", evento.Aux1);
                            eventoDescricao.Tipo = 1;
                            break;

                        case USUARIO_EVENTO.ERRO_EXPIRADO:
                            evento_texto = "Erro de Login (Senha Expirada)";
                            eventoDescricao.Tipo = 1;
                            break;

                        case USUARIO_EVENTO.ERRO_BLOQUEIO:
                            evento_texto = "Erro de Login (Usuário Bloqueado)";
                            eventoDescricao.Tipo = 1;
                            break;

                        case USUARIO_EVENTO.ERRO_TENTATIVAS:
                            evento_texto = "Erro de Login (Bloqueado por Excesso de Tentativas)";
                            eventoDescricao.Tipo = 1;
                            break;

                        case USUARIO_EVENTO.ALTERA_CONFIG:
                            evento_texto = "Alterou Configuração";
                            eventoDescricao.Tipo = 2;
                            break;

                        case USUARIO_EVENTO.EXCLUI_CONFIG:
                            evento_texto = "Excluiu Configuração";
                            eventoDescricao.Tipo = 2;
                            break;

                        case USUARIO_EVENTO.INSERE_CONFIG:
                            evento_texto = "Adicionou Configuração";
                            eventoDescricao.Tipo = 2;
                            break;

                        case USUARIO_EVENTO.MANUT_CONSTANTE:
                            evento_texto = "Manutenção Constante";
                            eventoDescricao.Tipo = 3;
                            break;

                        case USUARIO_EVENTO.OPERACAO:
                            evento_texto = "Operação";
                            eventoDescricao.Tipo = 3;
                            break;

                    }

                    if ( evento.Evento >= USUARIO_EVENTO.ALTERA_CONFIG && evento.Evento <= USUARIO_EVENTO.INSERE_CONFIG)
                    {
                        switch(evento.Aux1)
                        {
                            case TABELA_CONFIG.CLIENTE:
                                evento_texto += string.Format(" [Cliente: ID {0:000000}]", evento.Aux2);
                                break;

                            case TABELA_CONFIG.EMPRESA:
                                evento_texto += string.Format(" [Agente/Filial: ID {0:000000}]", evento.Aux2);
                                break;

                            case TABELA_CONFIG.GATEWAY:
                                evento_texto += string.Format(" [Gateway: ID {0:000000}]", evento.Aux2);
                                break;

                            case TABELA_CONFIG.GRUPO:
                                evento_texto += string.Format(" [Grupo de Unidades: ID {0:000000}]", evento.Aux2);
                                break;

                            case TABELA_CONFIG.UNIDADE:
                                evento_texto += string.Format(" [Unidade: ID {0:000000}]", evento.Aux2);
                                break;

                            case TABELA_CONFIG.MEDICAO:
                                evento_texto += string.Format(" [Medição: ID {0:000000}]", evento.Aux2);
                                break;

                            case TABELA_CONFIG.USUARIO:
                                evento_texto += string.Format(" [Usuário: ID {0:000000}]", evento.Aux2);
                                break;

                            case TABELA_CONFIG.RANKING:
                                evento_texto += string.Format(" [Ranking: ID {0:000000}]", evento.Aux2);
                                break;

                            case TABELA_CONFIG.GRUPO_USUARIO:
                                evento_texto += string.Format(" [Grupo Usuário: ID {0:000000}]", evento.Aux2);
                                break;

                            case TABELA_CONFIG.GRUPO_MEDICOES:
                                evento_texto += string.Format(" [Grupo Medições: ID {0:000000}]", evento.Aux2);
                                break;

                            default:
                                evento_texto += string.Format(" [Desconhecido: ID {0}]", evento.Aux2);
                                break;
                  
                        }
                    }

                    if (evento.Evento == USUARIO_EVENTO.OPERACAO)
                    {
                        switch (evento.Aux1)
                        {
                            case TABELA_OPERACAO.ATUALIZACAO_FIRMWARE:
                                evento_texto += string.Format(" Atualização Firmware [Gateway: ID {0:000000}]", evento.Aux2);
                                break;

                            case TABELA_OPERACAO.CORRIGIR_FALHA_UPLOAD:
                                evento_texto += string.Format(" Corrigir Falha Upload [Gateway: ID {0:000000}]", evento.Aux2);
                                break;

                            case TABELA_OPERACAO.LIMPAR_HISTORICO:
                                evento_texto += string.Format(" Limpar Histórico [Gateway: ID {0:000000}]", evento.Aux2);
                                break;

                            case TABELA_OPERACAO.ENVIO_DADOS:
                                evento_texto += string.Format(" Envio Dados [Gateway: ID {0:000000}]", evento.Aux2);
                                break;

                            case TABELA_OPERACAO.REBOOT:
                                evento_texto += string.Format(" Reboot [Gateway: ID {0:000000}]", evento.Aux2);
                                break;

                            default:
                                evento_texto += string.Format(" [Desconhecido: ID {0}]", evento.Aux2);
                                break;
                        }
                    }

                    eventoDescricao.Descricao = evento_texto;

                    // evento e valor
                    eventoDescricao.Evento = evento.Evento;
                    eventoDescricao.Valor = evento.Aux1;

                    // insere na lista
                    listaEventosDescricao.Add(eventoDescricao);
                }
            }

            // copia lista de eventos
            ViewBag.listaEventosDescricao = listaEventosDescricao;

            // lista de erros
            var listaErros = new List<string>();
            ViewBag.listaErros = listaErros;

            return;
        }

        // Eventos XLS
        private HSSFWorkbook EventosUsuario_XLS(int IDCliente)
        {

            // cookie de filtro
            string lista_selecao = "/" + CookieStore.LeCookie_String("Relat_TipoEvento") + "/";
            List<int> FiltroList = null;

            // copia filtros para lista
            if (!String.IsNullOrEmpty(lista_selecao))
            {
                FiltroList = lista_selecao.Split('/')
                    .Select(possibleIntegerAsString =>
                    {
                        int parsedInteger = 0;
                        bool isInteger = int.TryParse(possibleIntegerAsString, out parsedInteger);
                        return new { isInteger, parsedInteger };
                    })
                    .Where(tryParseResult => tryParseResult.isInteger)
                    .Select(tryParseResult => tryParseResult.parsedInteger)
                    .ToList();
            }

            // cookie usuarios
            int IDUsuario = 0;

            if (ViewBag.Relat_IDUsuario != null)
            {
                IDUsuario = ViewBag.Relat_IDUsuario;
            }


            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _datahorafullStyle = criaEstiloXLS(workbook, 13);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Eventos dos Usuários");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Usuários", "Código", "Evento" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // lista de eventos
            List<EventosUsuarioDescricao> listaEventosDescricao = ViewBag.listaEventosDescricao;

            // percorre eventos
            foreach (EventosUsuarioDescricao evento in listaEventosDescricao)
            {
                // verifica se NAO pertence ao filtro
                if (FiltroList != null)
                {
                    if (FiltroList.Count > 0)
                    {
                        if (!FiltroList.Contains(evento.Tipo))
                        {
                            continue;
                        }
                    }
                }

                // verifica se NAO pertence ao usurio
                if (IDUsuario > 0)
                {
                    if (evento.IDUsuario != IDUsuario)
                    {
                        continue;
                    }
                }

                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, evento.DataHoraN, _datahorafullStyle);

                // evento
                textoCelulaXLS(row, 1, evento.NomeUsuario);

                // codigo
                numeroCelulaXLS(row, 2, evento.Evento, _intCellStyle);

                // evento
                textoCelulaXLS(row, 3, evento.Descricao);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            sheet.SetColumnWidth(0, 6000);
            sheet.SetColumnWidth(1, 6000);
            sheet.SetColumnWidth(2, 4000);
            sheet.SetColumnWidth(3, 18000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoEventosUsuariosXLS(workbook, sheet, "Eventos dos Usuários", _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(4).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }
    }
}