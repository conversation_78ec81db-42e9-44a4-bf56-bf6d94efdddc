﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {
        // Relatorio Consolidado Anual
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_ResumoAnual", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_ResumoAnual(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref INFO_MEDICAO pinfo_medicao, ref DATAHORA pdatahora, ref RESUMO_CONSOLIDADO_ANUAL pconsolidado);

        //
        // CONSOLIDADO ANUAL DEMANDA e CONSUMO
        //

        // GET: Relatorio Consolidado Anual - Demanda e Consumo
        public ActionResult Relat_Consolidado_DemCons_Anual(int IDCliente)
        {
            // tipo do relatorio
            int TipoRelat = 50;
            int TipoConsolidado = 0;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Relat_Consolidado_Anual");
            CookieStore.SalvaCookie_Int("Relat_Tipo", TipoRelat);

            // le cookies
            LeCookies_SmartEnergy();

            // ano atual
            DateTime datahora_ultima = DateTime.Now;
            DATAHORA data_hora = new DATAHORA();

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_ultima);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // data atual
            ViewBag.DataAtual = string.Format("{0:yyyy}", datahora_ultima);

            // tipo do relatorio
            ViewBag.TipoRelat = TipoRelat;
            ViewBag.TipoConsolidado = TipoConsolidado;
            
            // seta para anual
            ViewBag.Relat_TipoPeriodo = TIPO_RELAT_PERIODO.Anual;

            // valores
            List<ConsolidadoAnual> consolidados = new List<ConsolidadoAnual>();
            ViewBag.consolidados = consolidados;

            // lista de erros
            var listaErros = new List<string>();

            // erros
            ViewBag.listaErros = listaErros;

            return View();
        }

        // GET: Relatorio Grafico
        private ActionResult Relat_Consolidado_Anual_Show(int IDCliente, int tipo_arquivo = 0)
        {
            // tipo do relatorio
            int TipoRelat = 50;

            // salva cookie 
            CookieStore.SalvaCookie_Int("Relat_Tipo", TipoRelat);

            // le cookies
            LeCookies_SmartEnergy();

            // ano atual
            DateTime datahora_ultima = DateTime.Now;
            DATAHORA data_hora = new DATAHORA();

            // le cookie datahora (atual ou inicial)
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            // verifica se existe datahora no cookie
            if (datahora_cookie.Year != 2000)
            {
                // copia data do cookie para ultima
                datahora_ultima = datahora_cookie;
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_ultima);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // tipo do relatorio
            ViewBag.TipoRelat = TipoRelat;

            // tipo consolidado
            string TipoConsolidadoStr = CookieStore.LeCookie_String("Relat_TipoConsolidado");
            int TipoConsolidado = 0;

            // verifica se foi mudado o tab
            if(TipoConsolidadoStr.Length == 8)
            {
                string aux_str = TipoConsolidadoStr.Substring(7, 1);
                TipoConsolidado = int.Parse(aux_str) - 1;
            }

            ViewBag.TipoConsolidado = TipoConsolidado;

            // View do relatorio
            string viewRelatorio = "_Consolidado_DemCons_Anual";

            // calcula
            Calc_Consolidado_Anual(IDCliente, data_hora);


            // verifica se PDF
            if (tipo_arquivo == 1)
            {
                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.pdf", viewRelatorio, IDCliente, data_atual);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Landscape,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna nome do arquivo
                var returnedData = new
                {
                    nomeArquivo = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // verifica se XLS
            if (tipo_arquivo == 2)
            {
                // Planilha Excel
                var workbook = new HSSFWorkbook();

                // monta XLS
                workbook = Consolidado_Anual_XLS(IDCliente);

                // gera identificador unico aonde o arquivo sera guardado
                string handle = Guid.NewGuid().ToString();

                using (MemoryStream memoryStream = new MemoryStream())
                {
                    workbook.Write(memoryStream);
                    memoryStream.Position = 0;
                    Session[handle] = memoryStream.ToArray();
                }

                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.xls", viewRelatorio, IDCliente, data_atual);

                // retorna nome do arquivo
                var returnedData = new
                {
                    FileGuid = handle,
                    FileName = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // verifica se EMAIL
            if (tipo_arquivo == 3)
            {
                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.pdf", viewRelatorio, IDCliente, data_atual);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Landscape,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna 
                ViewBag.caminhoCompleto = caminhoCompleto;
                var returnedData = new
                {
                    caminhoCompleto = caminhoCompleto
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            return View();
        }

        // Calcula Consolidado Anual
        private void Calc_Consolidado_Anual(int IDCliente, DATAHORA datahora)
        {
            // teste
            LogMessage_Consolidado_Anual(IDCliente, "--------------------------------------------------------------------------");
            LogMessage_Consolidado_Anual(IDCliente, string.Format("Inicia Consolidado Anual - IDCliente [{0:000000}] - {1:d}", IDCliente, datahora));

            // funcao relatorio
            int retorno;

            // le cookies
            LeCookies_SmartEnergy();

            // le medicoes
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicoesMetodos.ListarTodosUnidade(IDCliente, ViewBag._ConfigMed);

            // estruturas
            RESUMO_CONSOLIDADO_ANUAL resumo_consolidado = new RESUMO_CONSOLIDADO_ANUAL();
            INFO_MEDICAO info_medicao = new INFO_MEDICAO();
            List<ConsolidadoAnual> consolidados = new List<ConsolidadoAnual>();

            // lista de erros
            var listaErros = new List<string>();

            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_gateway = 0;

            // percorre medicoes
            if( medicoes != null )
            {
                // teste
                LogMessage_Consolidado_Anual(IDCliente, string.Format("Número Medições [{0}]", medicoes.Count));
                LogMessage_Consolidado_Anual(IDCliente, "--------------------------------------------------------------------------");

                int contador = 0;

                foreach (MedicoesDominio medicao in medicoes)
                {
                    // teste
                    LogMessage_Consolidado_Anual(IDCliente, string.Format("Medição [{0:000000}] - Tipo [{1}] - {2} de {3}", medicao.IDMedicao, medicao.IDTipoMedicao, contador, medicoes.Count));
                    contador++;

                    // verifica se nao eh energia
                    if ( medicao.IDTipoMedicao != 0 && medicao.IDTipoMedicao != 1 )
                    {
                        continue;
                    }

                    // preenche solicitacao
                    config_interface.sweb.id_medicao = medicao.IDMedicao;

                    // tipo medicao
                    info_medicao.IDTipoMedicao = (short)medicao.IDTipoMedicao;

                    // formula - converter o string para byte[]
                    info_medicao.Formula = new byte[206];
                    byte[] formula = Encoding.ASCII.GetBytes(medicao.Formula);

                    for (int i=0; i < 206; i++)
                    {
                        if( i < formula.Length )
                            info_medicao.Formula[i] = formula[i];
                        else
                            info_medicao.Formula[i] = 0;
                    }

                    // calcula valores
                    retorno = SmCalcDB_Energia_ResumoAnual((char)0, ref config_interface, ref info_medicao, ref datahora, ref resumo_consolidado);

                    // teste
                    LogMessage_Consolidado_Anual(IDCliente, string.Format("Medição [{0:000000}] - Retorno [{1}]", medicao.IDMedicao, retorno));

                    if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
                    {
                        if (retorno > 0)
                            listaErros.Add(string.Format("Medição {0} com Retorno {1}", medicao.Nome, retorno));
                    }

                    // coloca na lista
                    ConsolidadoAnual consolidado = new ConsolidadoAnual();

                    // copia estrutura
                    for (int i = 0; i < 12; i++)
                    {
                        ConsolidadoAnualValor valor = new ConsolidadoAnualValor();

                        valor.ConsumoTotal = resumo_consolidado.ConsumoTotal[i];
                        valor.ConsumoP = resumo_consolidado.ConsumoP[i];
                        valor.ConsumoFP = resumo_consolidado.ConsumoFP[i];
                        valor.DemandaP = resumo_consolidado.Dem_MaxP[i];
                        valor.DataDemandaP = Funcoes_Converte.ConverteDataHora2DateTime(resumo_consolidado.Dem_MaxP_DataHora[i]);
                        valor.DemandaFP = resumo_consolidado.Dem_MaxFP[i];
                        valor.DataDemandaFP = Funcoes_Converte.ConverteDataHora2DateTime(resumo_consolidado.Dem_MaxFP_DataHora[i]);

                        consolidado.ConsolidadoAnualValor[i] = valor;
                    }

                    consolidado.IDMedicao = medicao.IDMedicao;
                    consolidado.Nome = medicao.Nome;
                    consolidado.NomeUnidade = medicao.NomeUnidade;

                    consolidados.Add(consolidado);
                }
            }

            // teste
            LogMessage_Consolidado_Anual(IDCliente, "--------------------------------------------------------------------------");
            LogMessage_Consolidado_Anual(IDCliente, "Fim");
            LogMessage_Consolidado_Anual(IDCliente, "--------------------------------------------------------------------------");

            // valores
            ViewBag.consolidados = consolidados;

            // erros
            ViewBag.listaErros = listaErros;

            // converte data e hora
            DateTime DataAtual = Funcoes_Converte.ConverteDataHora2DateTime(datahora);

            // data atual
            ViewBag.DataAtual = string.Format("{0:yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:yyyy}", DataAtual);

            // tipo do relatorio
            ViewBag.TipoRelat = 50;

            // seta para anual
            ViewBag.Relat_TipoPeriodo = 3;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.ConsolidadoAnual;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Anual;
            
            return;
        }

        // Consolidado Anual XLS
        private HSSFWorkbook Consolidado_Anual_XLS(int IDCliente)
        {
            // cria excel
            HSSFWorkbook workbook = new HSSFWorkbook();

            // planilhas
            Consolidado_Anual_Planilha(workbook, 0);
            Consolidado_Anual_Planilha(workbook, 1);
            Consolidado_Anual_Planilha(workbook, 2);
            Consolidado_Anual_Planilha(workbook, 3);
            Consolidado_Anual_Planilha(workbook, 4);

            // retorna planilha
            return workbook;
        }

        // Consolidado Anual Planilha
        private void Consolidado_Anual_Planilha(HSSFWorkbook workbook, int TipoConsolidado)
        {
            List<ConsolidadoAnual> consolidados = ViewBag.consolidados;

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _datahorafullStyle = criaEstiloXLS(workbook, 13);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            // cria planilha
            string str_planilha = "";

            switch (TipoConsolidado)
            {
                case 0:
                    str_planilha = "Consumo Total (kWh)";
                    break;

                case 1:
                    str_planilha = "Consumo Ponta (kWh)";
                    break;

                case 2:
                    str_planilha = "Consumo Fora de Ponta (kWh)";
                    break;

                case 3:
                    str_planilha = "Demanda Ponta (kW)";
                    break;

                case 4:
                    str_planilha = "Demanda Fora de Ponta (kW)";
                    break;
            }

            ISheet sheet = workbook.CreateSheet(str_planilha);
            int rowIndex = 0;

            // verifica se demanda
            if (TipoConsolidado >= 3)
            {
                // cabecalho
                string[] cabecalho = { "ID", "Medições", "Unidades", "Jan " + ViewBag.DataAtual, " ", "Fev " + ViewBag.DataAtual, " ", "Mar " + ViewBag.DataAtual, " ", "Abr " + ViewBag.DataAtual, " ", "Mai " + ViewBag.DataAtual, " ", "Jun " + ViewBag.DataAtual, " ", "Jul " + ViewBag.DataAtual, " ", "Ago " + ViewBag.DataAtual, " ", "Set " + ViewBag.DataAtual, " ", "Out " + ViewBag.DataAtual, " ", "Nov " + ViewBag.DataAtual, " ", "Dez " + ViewBag.DataAtual, " " };

                // adiciona cabecalho
                rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);
            }
            else
            {
                // cabecalho
                string[] cabecalho = { "ID", "Medições", "Unidades", "Jan " + ViewBag.DataAtual, "Fev " + ViewBag.DataAtual, "Mar " + ViewBag.DataAtual, "Abr " + ViewBag.DataAtual, "Mai " + ViewBag.DataAtual, "Jun " + ViewBag.DataAtual, "Jul " + ViewBag.DataAtual, "Ago " + ViewBag.DataAtual, "Set " + ViewBag.DataAtual, "Out " + ViewBag.DataAtual, "Nov " + ViewBag.DataAtual, "Dez " + ViewBag.DataAtual };

                // adiciona cabecalho
                rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);
            }

            // adiciona linhas
            int i;
            IRow row;

            if (consolidados != null)
            {
                foreach (ConsolidadoAnual consolidado in consolidados)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // IDMedicao
                    numeroCelulaXLS(row, 0, consolidado.IDMedicao, _intCellStyle);

                    // medicao
                    textoCelulaXLS(row, 1, consolidado.Nome);

                    // unidade
                    textoCelulaXLS(row, 2, consolidado.NomeUnidade);

                    // meses
                    for (i = 0; i < 12; i++)
                    {
                        double valor = 0.0;
                        DateTime datahora = new DateTime(2000,1,1,0,0,0);

                        switch (TipoConsolidado)
                        {
                            case 0:
                                if (consolidado.ConsolidadoAnualValor[i].DataDemandaP.Year != 2000 || consolidado.ConsolidadoAnualValor[i].DataDemandaFP.Year != 2000)
                                {
                                    valor = consolidado.ConsolidadoAnualValor[i].ConsumoTotal;
                                }
                                break;

                            case 1:
                                if (consolidado.ConsolidadoAnualValor[i].DataDemandaP.Year != 2000)
                                {
                                    valor = consolidado.ConsolidadoAnualValor[i].ConsumoP;
                                }
                                break;

                            case 2:
                                if (consolidado.ConsolidadoAnualValor[i].DataDemandaFP.Year != 2000)
                                {
                                    valor = consolidado.ConsolidadoAnualValor[i].ConsumoFP;
                                }
                                break;

                            case 3:
                                if (consolidado.ConsolidadoAnualValor[i].DataDemandaP.Year != 2000)
                                {
                                    valor = consolidado.ConsolidadoAnualValor[i].DemandaP;
                                    datahora = consolidado.ConsolidadoAnualValor[i].DataDemandaP;
                                }
                                break;

                            case 4:
                                if (consolidado.ConsolidadoAnualValor[i].DataDemandaFP.Year != 2000)
                                {
                                    valor = consolidado.ConsolidadoAnualValor[i].DemandaFP;
                                    datahora = consolidado.ConsolidadoAnualValor[i].DataDemandaFP;
                                }
                                break;
                        }

                        // verifica se demanda
                        if (TipoConsolidado >= 3)
                        {
                            // valor
                            numeroCelulaXLS(row, i * 2 + 3, valor, _1CellStyle);

                            // data e hora
                            datahoraCelulaXLS(row, i * 2 + 4, datahora, _datahoraStyle);
                        }
                        else
                        {
                            // valor
                            numeroCelulaXLS(row, i + 3, valor, _intCellStyle);
                        }
                    }

                    // proxima
                    rowIndex++;
                }
            }

            // largura de cada coluna
            sheet.SetColumnWidth(0, 6000);
            for (i = 1; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 4000);

            return;
        }

        private void LogMessage_Consolidado_Anual(int IDCliente, string msg)
        {
            // arquivo
            string arquivo = string.Format("_{0:000000}", IDCliente);

            // log
            Funcoes_Log.Mensagem("Consolidado_Anual", msg, arquivo);
        }
    }
}