﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {
        // Relatorio Diario
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Util_RelatDiario", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Util_RelatDiario(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, ref DATAHORA pdata_equipo, ref RELAT_UTIL_DIARIO prelatorio, ref RELAT_UTIL_DIARIO_ANALISE panalise, ref GRANDEZA pgrandeza);

        // Relatorio Semanal
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Util_RelatSemanal", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Util_RelatSemanal(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, ref DATAHORA pdata_equipo, ref RELAT_UTIL_SEMANAL prelatorio, ref RELAT_UTIL_SEMANAL_ANALISE panalise, ref GRANDEZA pgrandeza);

        // Relatorio Mensal
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Util_RelatMensal", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Util_RelatMensal(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, ref DATAHORA pdata_equipo, ref RELAT_UTIL_MENSAL prelatorio, ref RELAT_UTIL_MENSAL_ANALISE panalise, ref GRANDEZA pgrandeza);

        // Relatorio Anual
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Util_RelatAnual", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Util_RelatAnual(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, ref DATAHORA pdata_equipo, ref RELAT_UTIL_ANUAL prelatorio, ref RELAT_UTIL_ANUAL_ANALISE panalise, ref GRANDEZA pgrandeza);


        //
        // UTILIDADES
        //

        // GET: Relatorio Utilidades
        public ActionResult Relat_Utilidades(int IDCliente, int IDMedicao)
        {
            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Relat_Utilidades");

            // relatorio utilidades
            return (Relat_Grafico_Show(IDCliente, IDMedicao, 20));
        }

        // Utilidades Diario
        private void Utilidades_Diario(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_UTIL_DIARIO relatorio = new RELAT_UTIL_DIARIO();
            RELAT_UTIL_DIARIO_ANALISE analise = new RELAT_UTIL_DIARIO_ANALISE();
            GRANDEZA grandeza = new GRANDEZA();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // calcula valores
            retorno = SmCalcDB_Util_RelatDiario((char)0, ref config_interface, (char)0, ref data_hora, ref relatorio, ref analise, ref grandeza);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var Consumo = new double[26];
            var Dias = new string[26];
            var DatasN = new DateTime[26];
            var Horas = new string[26];

            double Consumo_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Horas[i] = strData.ToString("HH:mm");

                // proxima hora
                strData = strData.AddHours(1);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    Consumo[i] = relatorio.registro[0].consumo;
                }

                // verifica se ultima barra
                if (i == 25)
                {
                    // zera
                    Consumo[i] = relatorio.registro[23].consumo;
                }

                if (i >= 1 && i <= 24)
                {
                    // copia
                    j = i - 1;

                    Consumo[i] = relatorio.registro[j].consumo;

                    // verifica consumo maximo
                    if (Consumo[i] > Consumo_max_grafico)
                        Consumo_max_grafico = Consumo[i];
                }
            }

            Consumo_max_grafico = Consumo_max_grafico * 1.1;

            ViewBag.ConsMaxGrafico = Consumo_max_grafico;

            ViewBag.Consumo = Consumo;
            ViewBag.Dias = Dias;
            ViewBag.DatasN = DatasN;
            ViewBag.Horas = Horas;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.Utilidades;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Diario;

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:d}", DataAtual);

            // consumo
            ViewBag.Cons_Max = string.Format("{0:#,##0.00}", analise.analise_valor1.maximo[0]);
            DateTime Cons_Max_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max[0]);
            if (Cons_Max_DataHora.Year != 2000)
                ViewBag.Cons_Max_DataHora = string.Format("{0:HH:mm}", Cons_Max_DataHora);
            else
                ViewBag.Cons_Max_DataHora = "--:--";
            ViewBag.Cons_Max_DataHoraN = Cons_Max_DataHora;

            ViewBag.Cons_Med = string.Format("{0:#,##0.00}", analise.analise_valor1.medio[0]);
            ViewBag.Cons_Total = string.Format("{0:#,##0.00}", analise.analise_valor1.consumo[0]);

            ViewBag.NomeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.nome);
            ViewBag.UnidadeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.unidade);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(1);

            ViewBag.data_hora_ini = data_hora_ini;

            EventosPeriodo(IDCliente, IDMedicao, data_hora_ini, data_hora_fim);

            return;
        }

        // Utilidades Diario XLS
        private HSSFWorkbook Utilidades_Diario_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // grandeza
            string str_grandeza = string.Format("{0} ({1})", ViewBag.NomeGrandeza, ViewBag.UnidadeGrandeza);

            // cabecalho
            string[] cabecalho = { "Data e Hora", str_grandeza };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 25; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _datahoraStyle);

                // consumo
                numeroCelulaXLS(row, 1, ViewBag.Consumo[i], _2CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Utilidades", "Relatório Diário", _negritoCellStyle);

            // cabecalho
            string[] cab_cons = { "", str_grandeza, "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // maximo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máximo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Cons_Max), _2CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Cons_Max_DataHoraN, _datahoraStyle);

            // medio
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Médio", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Cons_Med), _2CellStyle);

            // total
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Total", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Cons_Total), _2CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }

        // Utilidades Semanal
        private void Utilidades_Semanal(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_UTIL_SEMANAL relatorio = new RELAT_UTIL_SEMANAL();
            RELAT_UTIL_SEMANAL_ANALISE analise = new RELAT_UTIL_SEMANAL_ANALISE();
            GRANDEZA grandeza = new GRANDEZA();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // calcula valores
            retorno = SmCalcDB_Util_RelatSemanal((char)0, ref config_interface, (char)0, ref data_hora, ref relatorio, ref analise, ref grandeza);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var Consumo = new double[7, 26];
            var Datas = new string[26];
            var DatasN = new DateTime[7, 26];
            var Dias = new string[7];
            var Horas = new string[26];

            DateTime inicio = new DateTime();
            DateTime fim = new DateTime();

            double Consumo_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;
            int k = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                Horas[i] = strData.ToString("HH:mm");

                // percorre dias
                for (k = 0; k < 7; k++)
                {
                    // data e hora para excel
                    DatasN[k, i] = strData.AddDays(k);

                    // guarda inicio e fim
                    if (i == 0 && k == 0)
                    {
                        inicio = strData;
                        fim = inicio.AddDays(6);
                    }

                    // dia da semana
                    if (i == 1)
                    {
                        Dias[k] = string.Format("{0:d}", strData.AddDays(k));
                    }

                    // verifica se primeira ou ultima barra
                    if (i == 0)
                    {
                        // zera
                        Consumo[k, i] = relatorio.registro[0].consumo[k];
                    }

                    if (i == 25)
                    {
                        // zera
                        Consumo[k, i] = relatorio.registro[23].consumo[k];
                    }

                    if (i >= 1 && i <= 24)
                    {
                        // copia
                        j = i - 1;

                        Consumo[k, i] = relatorio.registro[j].consumo[k];

                        // verifica consumo maximo
                        if (Consumo[k, i] > Consumo_max_grafico)
                            Consumo_max_grafico = Consumo[k, i];
                    }
                }

                // proxima hora
                strData = strData.AddHours(1);
            }

            Consumo_max_grafico = Consumo_max_grafico * 1.1;

            ViewBag.ConsMaxGrafico = Consumo_max_grafico;

            ViewBag.Consumo = Consumo;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;
            ViewBag.Horas = Horas;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.Utilidades;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Semanal;

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);
            ViewBag.DataAtualN = inicio;
            ViewBag.DataFinalN = fim;
            ViewBag.DataTextoAtual = string.Format("{0:d} - {1:d}", inicio, fim);

            // consumo
            ViewBag.Cons_Max = string.Format("{0:#,##0.00}", analise.analise_valor.maximo[0]);
            DateTime Cons_Max_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (Cons_Max_DataHora.Year != 2000)
                ViewBag.Cons_Max_DataHora = string.Format("{0:d} {1:HH:mm}", Cons_Max_DataHora, Cons_Max_DataHora);
            else
                ViewBag.Cons_Max_DataHora = "--/--/---- --:--";
            ViewBag.Cons_Max_DataHoraN = Cons_Max_DataHora;

            ViewBag.Cons_Med = string.Format("{0:#,##0.00}", analise.analise_valor.medio[0]);
            ViewBag.Cons_Total = string.Format("{0:#,##0.00}", analise.analise_valor.consumo[0]);

            ViewBag.NomeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.nome);
            ViewBag.UnidadeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.unidade);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(inicio.Year, inicio.Month, inicio.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(7);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Utilidades Semanal XLS
        private HSSFWorkbook Utilidades_Semanal_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // grandeza
            string str_grandeza = string.Format("{0} ({1})", ViewBag.NomeGrandeza, ViewBag.UnidadeGrandeza);

            // dias da semana
            int k = 0;
            int i;
            IRow row;
            ISheet sheet;
            int rowIndex;

            for (k = 0; k < 7; k++)
            {
                // cria planilha
                sheet = workbook.CreateSheet(string.Format("{0:dddd}", ViewBag.DatasN[k, 0]));

                // cabecalho
                string[] cabecalho = { "Data e Hora", str_grandeza };

                // adiciona cabecalho
                rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

                // percorre valores
                for (i = 1; i < 25; i++)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // data e hora
                    datahoraCelulaXLS(row, 0, ViewBag.DatasN[k, i], _datahoraStyle);

                    // consumo
                    numeroCelulaXLS(row, 1, ViewBag.Consumo[k, i], _2CellStyle);

                    // proxima
                    rowIndex++;
                }

                // largura de cada coluna
                for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                    sheet.SetColumnWidth(i, 6000);
            }

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Utilidades", "Relatório Semanal", _negritoCellStyle);

            // cabecalho
            string[] cab_cons = { "", str_grandeza, "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // maximo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máximo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Cons_Max), _2CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Cons_Max_DataHoraN, _datahoraStyle);

            // medio
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Médio", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Cons_Med), _2CellStyle);

            // total
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Total", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Cons_Total), _2CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }

        // Utilidades Mensal
        private void Utilidades_Mensal(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_UTIL_MENSAL relatorio = new RELAT_UTIL_MENSAL();
            RELAT_UTIL_MENSAL_ANALISE analise = new RELAT_UTIL_MENSAL_ANALISE();
            GRANDEZA grandeza = new GRANDEZA();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // calcula valores
            retorno = SmCalcDB_Util_RelatMensal((char)0, ref config_interface, (char)0, ref data_hora, ref relatorio, ref analise, ref grandeza);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   1, 1, 0, 0);

            int NumDiasMes = relatorio.num_dias;

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var Consumo = new double[33];
            var Datas = new string[33];
            var DatasN = new DateTime[33];
            var Dias = new string[33];

            double Consumo_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = string.Format("{0:0}", strData.Day);

                // proximo dias
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    Consumo[i] = relatorio.registro[0].consumo;
                }

                if (i == (NumDiasMes + 1))
                {
                    // zera
                    Consumo[i] = relatorio.registro[NumDiasMes - 1].consumo;
                }

                if (i >= 1 && i <= NumDiasMes)
                {
                    // copia
                    j = i - 1;

                    Consumo[i] = relatorio.registro[j].consumo;

                    // verifica consumo maximo
                    if (Consumo[i] > Consumo_max_grafico)
                        Consumo_max_grafico = Consumo[i];
                }
            }

            Consumo_max_grafico = Consumo_max_grafico * 1.1;

            ViewBag.ConsMaxGrafico = Consumo_max_grafico;

            ViewBag.Consumo = Consumo;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.Utilidades;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Mensal;

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:y}", DataAtual);

            ViewBag.NumDiasMes = NumDiasMes;

            // consumo
            ViewBag.Cons_Max = string.Format("{0:#,##0.00}", analise.analise_valor.maximo[0]);
            DateTime Cons_Max_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (Cons_Max_DataHora.Year != 2000)
                ViewBag.Cons_Max_DataHora = string.Format("{0:d} {1:HH:mm}", Cons_Max_DataHora, Cons_Max_DataHora);
            else
                ViewBag.Cons_Max_DataHora = "--/--/---- --:--";
            ViewBag.Cons_Max_DataHoraN = Cons_Max_DataHora;

            ViewBag.Cons_Med = string.Format("{0:#,##0.00}", analise.analise_valor.medio[0]);
            ViewBag.Cons_Total = string.Format("{0:#,##0.00}", analise.analise_valor.consumo[0]);

            ViewBag.NomeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.nome);
            ViewBag.UnidadeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.unidade);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Utilidades Mensal XLS
        private HSSFWorkbook Utilidades_Mensal_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // grandeza
            string str_grandeza = string.Format("{0} ({1})", ViewBag.NomeGrandeza, ViewBag.UnidadeGrandeza);

            // cabecalho
            string[] cabecalho = { "Data e Hora", str_grandeza };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < (ViewBag.NumDiasMes + 1); i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // consumo 
                numeroCelulaXLS(row, 1, ViewBag.Consumo[i], _2CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Utilidades", "Relatório Mensal", _negritoCellStyle);

            // cabecalho
            string[] cab_cons = { "", str_grandeza, "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // maximo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máximo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Cons_Max), _2CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Cons_Max_DataHoraN, _datahoraStyle);

            // medio
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Médio", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Cons_Med), _2CellStyle);

            // total
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Total", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Cons_Total), _2CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }

        // Utilidades Anual
        private void Utilidades_Anual(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_UTIL_ANUAL relatorio = new RELAT_UTIL_ANUAL();
            RELAT_UTIL_ANUAL_ANALISE analise = new RELAT_UTIL_ANUAL_ANALISE();
            GRANDEZA grandeza = new GRANDEZA();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // calcula valores
            retorno = SmCalcDB_Util_RelatAnual((char)0, ref config_interface, (char)0, ref data_hora, ref relatorio, ref analise, ref grandeza);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   1, 1, 1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var Consumo = new double[14];
            var Datas = new string[14];
            var DatasN = new DateTime[14];
            var Meses = new string[14];

            double Consumo_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 mes para a primeira barra
            strData = strData.AddMonths(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 14; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Meses[i] = string.Format("{0:MMMM}", strData);

                // proximo mes
                strData = strData.AddMonths(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    Consumo[i] = relatorio.registro[0].consumo;
                }

                if (i == 13)
                {
                    // zera
                    Consumo[i] = relatorio.registro[11].consumo;
                }

                if (i >= 1 && i <= 12)
                {
                    // copia
                    j = i - 1;

                    Consumo[i] = relatorio.registro[j].consumo;

                    // verifica consumo maximo
                    if (Consumo[i] > Consumo_max_grafico)
                        Consumo_max_grafico = Consumo[i];
                }
            }

            Consumo_max_grafico = Consumo_max_grafico * 1.1;

            ViewBag.ConsMaxGrafico = Consumo_max_grafico;

            ViewBag.Consumo = Consumo;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Meses = Meses;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.Utilidades;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Anual;

            // data atual
            ViewBag.DataAtual = string.Format("{0:yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:yyyy}", DataAtual);

            // consumo
            ViewBag.Cons_Max = string.Format("{0:#,##0.00}", analise.analise_valor.maximo[0]);
            DateTime Cons_Max_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (Cons_Max_DataHora.Year != 2000)
                ViewBag.Cons_Max_DataHora = string.Format("{0:d} {1:HH:mm}", Cons_Max_DataHora, Cons_Max_DataHora);
            else
                ViewBag.Cons_Max_DataHora = "--/--/---- --:--";
            ViewBag.Cons_Max_DataHoraN = Cons_Max_DataHora;

            ViewBag.Cons_Med = string.Format("{0:#,##0.00}", analise.analise_valor.medio[0]);
            ViewBag.Cons_Total = string.Format("{0:#,##0.00}", analise.analise_valor.consumo[0]);

            ViewBag.NomeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.nome);
            ViewBag.UnidadeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.unidade);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddYears(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Utilidades Anual XLS
        private HSSFWorkbook Utilidades_Anual_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // grandeza
            string str_grandeza = string.Format("{0} ({1})", ViewBag.NomeGrandeza, ViewBag.UnidadeGrandeza);

            // cabecalho
            string[] cabecalho = { "Data e Hora", str_grandeza };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 13; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // consumo 
                numeroCelulaXLS(row, 1, ViewBag.Consumo[i], _2CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Utilidades", "Relatório Anual", _negritoCellStyle);

            // cabecalho
            string[] cab_cons = { "", str_grandeza, "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // maximo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máximo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Cons_Max), _2CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.Cons_Max_DataHoraN, _datahoraStyle);

            // medio
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Médio", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Cons_Med), _2CellStyle);

            // total
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Total", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.Cons_Total), _2CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }

        // Utilidades exportar XLS
        private HSSFWorkbook Util_exportar_XLS(int IDCliente, int IDMedicao, DateTime DataIni, DateTime DataFim)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // datas
            ViewBag.DataAtualN = DataIni;
            ViewBag.DataFinalN = DataFim;

            // medicao
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);

            // calcula
            GG_Metodos ggMetodos = new GG_Metodos();
            List<GG_Dominio> ggRegistros = ggMetodos.ListarTodosPeriodo(IDCliente, IDMedicao, DataIni, DataFim);

            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", string.Format("{0} ({1})", medicao.NomeGrandeza, medicao.UnidadeGrandeza) };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            if( ggRegistros != null )
            {
                // percorre valores
                foreach(GG_Dominio registro in ggRegistros)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // data e hora
                    datahoraCelulaXLS(row, 0, registro.DataHora, _datahoraStyle);

                    // utilidades
                    numeroCelulaXLS(row, 1, registro.VMed, _2CellStyle);

                    // proxima
                    rowIndex++;
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Utilidades" , "", _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(9).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }
    }
}
