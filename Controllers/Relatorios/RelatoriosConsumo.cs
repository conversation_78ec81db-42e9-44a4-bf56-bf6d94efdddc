﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {
        //
        // CONSUMO
        //

        // GET: Relatorio Consumo
        public ActionResult Relat_Consumo(int IDCliente, int IDMedicao)
        {
            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Relat_Consumo");

            // relatorio consumo
            return (Relat_Grafico_Show(IDCliente, IDMedicao, 2));
        }

        // Consumo Diario
        private void Consumo_Diario(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_DIARIO relatorio = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise = new RELAT_DIARIO_ANALISE();

            RELAT_DIARIO relatorio_sim = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise_sim = new RELAT_DIARIO_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao de cenario 
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // calcula valores
            retorno = SmCalcDB_Energia_RelatDiario((char)0, ref config_interface, (char)2, (char)IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_DIARIO(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var Consumo = new double[26];
            var Periodo = new int[26];
            var Dias = new string[26];
            var DatasN = new DateTime[26];
            var Horas = new string[26];

            // grafico simulacao
            var Consumo_Sim = new double[26];
            var Periodo_Sim = new int[26];
            var Dias_Sim = new string[26];
            var DatasN_Sim = new DateTime[26];
            var Horas_Sim = new string[26];

            double Consumo_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Horas[i] = strData.ToString("HH:mm");

                // formata label
                Dias_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Sim[i] = strData;
                Horas_Sim[i] = strData.ToString("HH:mm");

                // proxima hora
                strData = strData.AddHours(1);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    Consumo[i] = relatorio.registro[0].valor1;
                    Periodo[i] = relatorio.registro[0].periodo;

                    // zera
                    Consumo_Sim[i] = relatorio_sim.registro[0].valor1;
                    Periodo_Sim[i] = relatorio_sim.registro[0].periodo;
                }

                // verifica se ultima barra
                if (i == 25)
                {
                    // zera
                    Consumo[i] = relatorio.registro[23].valor1;
                    Periodo[i] = relatorio.registro[23].periodo;

                    // zera
                    Consumo_Sim[i] = relatorio_sim.registro[23].valor1;
                    Periodo_Sim[i] = relatorio_sim.registro[23].periodo;
                }

                if (i >= 1 && i <= 24)
                {
                    // copia
                    j = i - 1;

                    Consumo[i] = relatorio.registro[j].valor1;
                    Periodo[i] = relatorio.registro[j].periodo;

                    // verifica se sem registro
                    if (relatorio.registro[j].periodo == 3)
                    {
                        Consumo[i] = 0.0;
                    }

                    // verifica consumo maximo
                    if (Consumo[i] > Consumo_max_grafico)
                        Consumo_max_grafico = Consumo[i];

                    // copia simulacao
                    Consumo_Sim[i] = relatorio_sim.registro[j].valor1;
                    Periodo_Sim[i] = relatorio_sim.registro[j].periodo;

                    // verifica se sem registro simulacao
                    if (relatorio_sim.registro[j].periodo == 3)
                    {
                        Consumo_Sim[i] = 0.0;
                    }

                    // verifica consumo maximo
                    if (Consumo_Sim[i] > Consumo_max_grafico)
                        Consumo_max_grafico = Consumo_Sim[i];
                }
            }

            Consumo_max_grafico = Consumo_max_grafico * 1.1;

            if (Consumo_max_grafico == 0.0)
            {
                Consumo_max_grafico = 10.0;
            }

            ViewBag.ConsMaxGrafico = Consumo_max_grafico;

            ViewBag.Consumo = Consumo;
            ViewBag.Periodo = Periodo;
            ViewBag.Dias = Dias;
            ViewBag.DatasN = DatasN;
            ViewBag.Horas = Horas;

            // simulacao
            ViewBag.Consumo_Sim = Consumo_Sim;
            ViewBag.Periodo_Sim = Periodo_Sim;
            ViewBag.Dias_Sim = Dias_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;
            ViewBag.Horas_Sim = Horas_Sim;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.Consumo;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Diario;

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:d}", DataAtual);

            // consumo
            double ConsP = analise.analise_valor1.consumo[0];
            double ConsFPI = analise.analise_valor1.consumo[1];
            double ConsFPC = analise.analise_valor1.consumo[2];
            double ConsTotal = analise.analise_valor1.consumo[0] + analise.analise_valor1.consumo[1] + analise.analise_valor1.consumo[2];

            // consumo simulacao
            double ConsP_Sim = analise_sim.analise_valor1.consumo[0];
            double ConsFPI_Sim = analise_sim.analise_valor1.consumo[1];
            double ConsFPC_Sim = analise_sim.analise_valor1.consumo[2];
            double ConsTotal_Sim = analise_sim.analise_valor1.consumo[0] + analise_sim.analise_valor1.consumo[1] + analise_sim.analise_valor1.consumo[2];

            // consumo ponta
            if (ConsP < 100.0 )
            {
                ViewBag.ConsP = string.Format("{0:#,##0.0}", ConsP);
            }
            else
            {
                ViewBag.ConsP = string.Format("{0:#,##0}", ConsP);
            }
            ViewBag.ConsPN = ConsP;

            // consumo ponta simulacao
            if (ConsP_Sim < 100.0)
            {
                ViewBag.ConsP_Sim = string.Format("{0:#,##0.0}", ConsP_Sim);
            }
            else
            {
                ViewBag.ConsP_Sim = string.Format("{0:#,##0}", ConsP_Sim);
            }
            ViewBag.ConsPN_Sim = ConsP_Sim;

            // consumo fora ponta indutivo
            if (ConsFPI < 100.0)
            {
                ViewBag.ConsFPI = string.Format("{0:#,##0.0}", ConsFPI);
            }
            else
            {
                ViewBag.ConsFPI = string.Format("{0:#,##0}", ConsFPI);
            }
            ViewBag.ConsFPIN = ConsFPI;

            // consumo fora ponta indutivo simulacao
            if (ConsFPI_Sim < 100.0)
            {
                ViewBag.ConsFPI_Sim = string.Format("{0:#,##0.0}", ConsFPI_Sim);
            }
            else
            {
                ViewBag.ConsFPI_Sim = string.Format("{0:#,##0}", ConsFPI_Sim);
            }
            ViewBag.ConsFPIN_Sim = ConsFPI_Sim;

            // consumo fora ponta capacitivo
            if (ConsFPC < 100.0)
            {
                ViewBag.ConsFPC = string.Format("{0:#,##0.0}", ConsFPC);
            }
            else
            {
                ViewBag.ConsFPC = string.Format("{0:#,##0}", ConsFPC);
            }
            ViewBag.ConsFPCN = ConsFPC;


            // consumo fora ponta capacitivo simulacao
            if (ConsFPC_Sim < 100.0)
            {
                ViewBag.ConsFPC_Sim = string.Format("{0:#,##0.0}", ConsFPC_Sim);
            }
            else
            {
                ViewBag.ConsFPC_Sim = string.Format("{0:#,##0}", ConsFPC_Sim);
            }
            ViewBag.ConsFPCN_Sim = ConsFPC_Sim;

            // consumo total 
            if (ConsTotal < 100.0)
            {
                ViewBag.ConsTotal = string.Format("{0:#,##0.0}", ConsTotal);
            }
            else
            {
                ViewBag.ConsTotal = string.Format("{0:#,##0}", ConsTotal);
            }
            ViewBag.ConsTotalN = ConsTotal;

            // consumo total simulacao
            if (ConsTotal_Sim < 100.0)
            {
                ViewBag.ConsTotal_Sim = string.Format("{0:#,##0.0}", ConsTotal_Sim);
            }
            else
            {
                ViewBag.ConsTotal_Sim = string.Format("{0:#,##0}", ConsTotal_Sim);
            }
            ViewBag.ConsTotalN_Sim = ConsTotal_Sim;

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(1);

            ViewBag.data_hora_ini = data_hora_ini;

            EventosPeriodo(IDCliente, IDMedicao, data_hora_ini, data_hora_fim);

            return;
        }

        // Consumo Diario XLS
        private HSSFWorkbook Consumo_Diario_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Período", "Consumo" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 25; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _datahoraStyle);

                // periodo
                numeroCelulaXLS(row, 1, ViewBag.Periodo[i], _intCellStyle);

                // consumo
                numeroCelulaXLS(row, 2, ViewBag.Consumo[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Consumo", "Relatório Diário", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string[] cab_cons = { "Consumo", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // consumo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Consumo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.ConsFPCN, _1CellStyle);
            numeroCelulaXLS(row, 2, ViewBag.ConsFPIN, _1CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.ConsPN, _1CellStyle);
            numeroCelulaXLS(row, 4, ViewBag.ConsTotalN, _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Consumo Diario Simulacao XLS
        private HSSFWorkbook Consumo_Diario_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho simulacao
            string[] simulacao = { "Relatório de Consumo - Simulação" };

            // simulação 
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas do titulo simulacao
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 2));

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Período", "Consumo" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 25; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[i], _datahoraStyle);

                // periodo
                numeroCelulaXLS(row, 1, ViewBag.Periodo_Sim[i], _intCellStyle);

                // consumo
                numeroCelulaXLS(row, 2, ViewBag.Consumo_Sim[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Consumo - Simulação", "Relatório Diário", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string[] cab_cons = { "Consumo", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // consumo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Consumo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.ConsFPCN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 2, ViewBag.ConsFPIN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.ConsPN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 4, ViewBag.ConsTotalN_Sim, _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Consumo Semanal
        private void Consumo_Semanal(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_SEMANAL relatorio = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise = new RELAT_SEMANAL_ANALISE();

            RELAT_SEMANAL relatorio_sim = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise_sim = new RELAT_SEMANAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao  de cenário
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // calcula valores
            retorno = SmCalcDB_Energia_RelatSemanal((char)0, ref config_interface, (char)2, (char)IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_SEMANAL(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // maior consumo
            double maior_consumo = analise.analise_valor.consumo[0] + analise.analise_valor.consumo[1];

            // grafico
            var ConsumoP = new double[9];
            var ConsumoFPI = new double[9];
            var ConsumoFPC = new double[9];
            var Datas = new string[9];
            var DatasN = new DateTime[9];
            var Dias = new string[9];

            // grafico simulacao
            var ConsumoP_Sim = new double[9];
            var ConsumoFPI_Sim = new double[9];
            var ConsumoFPC_Sim = new double[9];
            var Datas_Sim = new string[9];
            var DatasN_Sim = new DateTime[9];
            var Dias_Sim = new string[9];

            DateTime inicio = new DateTime();
            DateTime fim = new DateTime();

            double Consumo_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 9; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = string.Format("{0:d}", strData);

                // formata label simulacao
                Datas_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Sim[i] = strData;
                Dias_Sim[i] = string.Format("{0:d}", strData);

                // guarda inicio
                if (i == 1)
                {
                    inicio = strData;
                }

                // guarda fim
                if (i == 7)
                {
                    fim = strData;
                }

                // proximo dias
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    ConsumoP[i] = relatorio.registro[0].valor[0];
                    ConsumoFPI[i] = relatorio.registro[0].valor[1];
                    ConsumoFPC[i] = relatorio.registro[0].valor[2];

                    // zera simulacao
                    ConsumoP_Sim[i] = relatorio_sim.registro[0].valor[0];
                    ConsumoFPI_Sim[i] = relatorio_sim.registro[0].valor[1];
                    ConsumoFPC_Sim[i] = relatorio_sim.registro[0].valor[2];
                }

                if (i == 8)
                {
                    // zera
                    ConsumoP[i] = relatorio.registro[6].valor[0];
                    ConsumoFPI[i] = relatorio.registro[6].valor[1];
                    ConsumoFPC[i] = relatorio.registro[6].valor[2];

                    // zera simulacao
                    ConsumoP_Sim[i] = relatorio_sim.registro[6].valor[0];
                    ConsumoFPI_Sim[i] = relatorio_sim.registro[6].valor[1];
                    ConsumoFPC_Sim[i] = relatorio_sim.registro[6].valor[2];
                }

                if (i >= 1 && i <= 7)
                {
                    // copia
                    j = i - 1;

                    ConsumoP[i] = relatorio.registro[j].valor[0];
                    ConsumoFPI[i] = relatorio.registro[j].valor[1];
                    ConsumoFPC[i] = relatorio.registro[j].valor[2];

                    // verifica consumo maximo
                    if ((ConsumoP[i] + ConsumoFPI[i] + ConsumoFPC[i]) > Consumo_max_grafico)
                        Consumo_max_grafico = (ConsumoP[i] + ConsumoFPI[i] + ConsumoFPC[i]);

                    // copia simulacao
                    ConsumoP_Sim[i] = relatorio_sim.registro[j].valor[0];
                    ConsumoFPI_Sim[i] = relatorio_sim.registro[j].valor[1];
                    ConsumoFPC_Sim[i] = relatorio_sim.registro[j].valor[2];

                    // verifica consumo maximo
                    if ((ConsumoP_Sim[i] + ConsumoFPI_Sim[i] + ConsumoFPC_Sim[i]) > Consumo_max_grafico)
                        Consumo_max_grafico = (ConsumoP_Sim[i] + ConsumoFPI_Sim[i] + ConsumoFPC_Sim[i]);
                }
            }

            Consumo_max_grafico = Consumo_max_grafico * 1.1;

            if (Consumo_max_grafico == 0.0)
            {
                Consumo_max_grafico = 10.0;
            }

            ViewBag.ConsMaxGrafico = Consumo_max_grafico;

            ViewBag.ConsumoP = ConsumoP;
            ViewBag.ConsumoFPI = ConsumoFPI;
            ViewBag.ConsumoFPC = ConsumoFPC;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            // simulacao
            ViewBag.ConsumoP_Sim = ConsumoP_Sim;
            ViewBag.ConsumoFPI_Sim = ConsumoFPI_Sim;
            ViewBag.ConsumoFPC_Sim = ConsumoFPC_Sim;
            ViewBag.Datas_Sim = Datas_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;
            ViewBag.Dias_Sim = Dias_Sim;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.MenuTexts.MenuLateralConsumo;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Semanal;

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);
            ViewBag.DataAtualN = inicio;
            ViewBag.DataFinalN = fim;
            ViewBag.DataTextoAtual = string.Format("{0:d} - {1:d} ", inicio, fim);

            // consumo
            double ConsP = analise.analise_valor.consumo[0];
            double ConsFPI = analise.analise_valor.consumo[1];
            double ConsFPC = analise.analise_valor.consumo[2];
            double ConsTotal = (analise.analise_valor.consumo[0] + analise.analise_valor.consumo[1] + analise.analise_valor.consumo[2]);

            // consumo simulacao
            double ConsP_Sim = analise_sim.analise_valor.consumo[0];
            double ConsFPI_Sim = analise_sim.analise_valor.consumo[1];
            double ConsFPC_Sim = analise_sim.analise_valor.consumo[2];
            double ConsTotal_Sim = (analise_sim.analise_valor.consumo[0] + analise_sim.analise_valor.consumo[1] + analise_sim.analise_valor.consumo[2]);


            // consumo ponta
            if (ConsP < 100.0)
            {
                ViewBag.ConsP = string.Format("{0:#,##0.0}", ConsP);
            }
            else
            {
                ViewBag.ConsP = string.Format("{0:#,##0}", ConsP);
            }
            ViewBag.ConsPN = ConsP;

            // consumo ponta simulacao
            if (ConsP_Sim < 100.0)
            {
                ViewBag.ConsP_Sim = string.Format("{0:#,##0.0}", ConsP_Sim);
            }
            else
            {
                ViewBag.ConsP_Sim = string.Format("{0:#,##0}", ConsP_Sim);
            }
            ViewBag.ConsPN_Sim = ConsP_Sim;

            // consumo fora ponta indutivo
            if (ConsFPI < 100.0)
            {
                ViewBag.ConsFPI = string.Format("{0:#,##0.0}", ConsFPI);
            }
            else
            {
                ViewBag.ConsFPI = string.Format("{0:#,##0}", ConsFPI);
            }
            ViewBag.ConsFPIN = ConsFPI;

            // consumo fora ponta indutivo simulacao
            if (ConsFPI_Sim < 100.0)
            {
                ViewBag.ConsFPI_Sim = string.Format("{0:#,##0.0}", ConsFPI_Sim);
            }
            else
            {
                ViewBag.ConsFPI_Sim = string.Format("{0:#,##0}", ConsFPI_Sim);
            }
            ViewBag.ConsFPIN_Sim = ConsFPI_Sim;

            // consumo fora ponta capacitivo
            if (ConsFPC < 100.0)
            {
                ViewBag.ConsFPC = string.Format("{0:#,##0.0}", ConsFPC);
            }
            else
            {
                ViewBag.ConsFPC = string.Format("{0:#,##0}", ConsFPC);
            }
            ViewBag.ConsFPCN = ConsFPC;

            // consumo fora ponta capacitivo simulacao 
            if (ConsFPC_Sim < 100.0)
            {
                ViewBag.ConsFPC_Sim = string.Format("{0:#,##0.0}", ConsFPC_Sim);
            }
            else
            {
                ViewBag.ConsFPC_Sim = string.Format("{0:#,##0}", ConsFPC_Sim);
            }
            ViewBag.ConsFPCN_Sim = ConsFPC_Sim;

            // consumo total
            if (ConsTotal < 100.0)
            {
                ViewBag.ConsTotal = string.Format("{0:#,##0.0}", ConsTotal);
            }
            else
            {
                ViewBag.ConsTotal = string.Format("{0:#,##0}", ConsTotal);
            }
            ViewBag.ConsTotalN = ConsTotal;

            // consumo total simulacao 
            if (ConsTotal_Sim < 100.0)
            {
                ViewBag.ConsTotal_Sim = string.Format("{0:#,##0.0}", ConsTotal_Sim);
            }
            else
            {
                ViewBag.ConsTotal_Sim = string.Format("{0:#,##0}", ConsTotal_Sim);
            }
            ViewBag.ConsTotalN_Sim = ConsTotal_Sim;

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(inicio.Year, inicio.Month, inicio.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(7);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Consumo Semanal XLS
        private HSSFWorkbook Consumo_Semanal_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string str_fpc = string.Format("Fora de Ponta Capacitivo ({0})", ViewBag.UnidadeConsumo);
            string str_fpi = string.Format("Fora de Ponta Indutivo ({0})", ViewBag.UnidadeConsumo);
            string str_ponta = string.Format("Ponta ({0})", ViewBag.UnidadeConsumo);
            string str_total = string.Format("Total ({0})", ViewBag.UnidadeConsumo);
            string[] cabecalho = { "Semana", "Data e Hora", str_fpc, str_fpi, str_ponta, str_total };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 8; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // semana
                textoCelulaXLS(row, 0, string.Format("{0:dddd}",ViewBag.DatasN[i]));

                // data e hora
                datahoraCelulaXLS(row, 1, ViewBag.DatasN[i], _dataStyle);

                // consumo fora de ponta capacitivo
                numeroCelulaXLS(row, 2, ViewBag.ConsumoFPC[i], _1CellStyle);

                // consumo fora de ponta indutivo
                numeroCelulaXLS(row, 3, ViewBag.ConsumoFPI[i], _1CellStyle);

                // consumo ponta
                numeroCelulaXLS(row, 4, ViewBag.ConsumoP[i], _1CellStyle);

                // consumo total
                numeroCelulaXLS(row, 5, ViewBag.ConsumoP[i] + ViewBag.ConsumoFPI[i] + ViewBag.ConsumoFPC[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Consumo", "Relatório Semanal", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string str_consumo = string.Format("Consumo ({0})", ViewBag.UnidadeConsumo);
            string[] cab_cons = { str_consumo, "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // consumo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Consumo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.ConsFPCN, _1CellStyle);
            numeroCelulaXLS(row, 2, ViewBag.ConsFPIN, _1CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.ConsPN, _1CellStyle);
            numeroCelulaXLS(row, 4, ViewBag.ConsTotalN, _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Consumo Semanal XLS
        private HSSFWorkbook Consumo_Semanal_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho simulacao
            string[] simulacao = { "Relatório de Consumo - Simulação" };

            // simulação 
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas do titulo simulacao
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 5));

            // cabecalho
            string str_fpc = string.Format("Fora de Ponta Capacitivo ({0})", ViewBag.UnidadeConsumo);
            string str_fpi = string.Format("Fora de Ponta Indutivo ({0})", ViewBag.UnidadeConsumo);
            string str_ponta = string.Format("Ponta ({0})", ViewBag.UnidadeConsumo);
            string str_total = string.Format("Total ({0})", ViewBag.UnidadeConsumo);
            string[] cabecalho = { "Semana", "Data e Hora", str_fpc, str_fpi, str_ponta, str_total };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 8; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // semana
                textoCelulaXLS(row, 0, string.Format("{0:dddd}", ViewBag.DatasN_Sim[i]));

                // data e hora
                datahoraCelulaXLS(row, 1, ViewBag.DatasN_Sim[i], _dataStyle);

                // consumo fora de ponta capacitivo
                numeroCelulaXLS(row, 2, ViewBag.ConsumoFPC_Sim[i], _1CellStyle);

                // consumo fora de ponta indutivo
                numeroCelulaXLS(row, 3, ViewBag.ConsumoFPI_Sim[i], _1CellStyle);

                // consumo ponta
                numeroCelulaXLS(row, 4, ViewBag.ConsumoP_Sim[i], _1CellStyle);

                // consumo total
                numeroCelulaXLS(row, 5, ViewBag.ConsumoP_Sim[i] + ViewBag.ConsumoFPI_Sim[i] + ViewBag.ConsumoFPC_Sim[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Consumo - Simulação", "Relatório Semanal", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string str_consumo = string.Format("Consumo ({0})", ViewBag.UnidadeConsumo);
            string[] cab_cons = { str_consumo, "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // consumo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Consumo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.ConsFPCN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 2, ViewBag.ConsFPIN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.ConsPN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 4, ViewBag.ConsTotalN_Sim, _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Consumo Mensal
        private void Consumo_Mensal(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_MENSAL relatorio = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise = new RELAT_MENSAL_ANALISE();

            RELAT_MENSAL relatorio_sim = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise_sim = new RELAT_MENSAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao  de cenário
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // calcula valores
            retorno = SmCalcDB_Energia_RelatMensal((char)0, ref config_interface, (char)2, (char)IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_MENSAL(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // numero de dias do mes
            int NumDiasMes = relatorio.num_dias;

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[NumDiasMes - 1].datahora.data.ano,
                                   (int)relatorio.registro[NumDiasMes - 1].datahora.data.mes,
                                   1, 1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // maior consumo
            double maior_consumo = analise.analise_valor.consumo[0] + analise.analise_valor.consumo[1];

            // grafico
            var ConsumoP = new double[42];
            var ConsumoFPI = new double[42];
            var ConsumoFPC = new double[42];
            var Datas = new string[42];
            var DatasN = new DateTime[42];
            var Dias = new string[42];

            // grafico simulacao
            var ConsumoP_Sim = new double[42];
            var ConsumoFPI_Sim = new double[42];
            var ConsumoFPC_Sim = new double[42];
            var Datas_Sim = new string[42];
            var DatasN_Sim = new DateTime[42];
            var Dias_Sim = new string[42];

            double Consumo_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = strData.ToString("dd/MM");

                // formata label simulacao 
                Datas_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Sim[i] = strData;
                Dias_Sim[i] = strData.ToString("dd/MM");

                // proximo dia
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    ConsumoP[i] = relatorio.registro[0].valor[0];
                    ConsumoFPI[i] = relatorio.registro[0].valor[1];
                    ConsumoFPC[i] = relatorio.registro[0].valor[2];

                    // zera simulacao
                    ConsumoP_Sim[i] = relatorio_sim.registro[0].valor[0];
                    ConsumoFPI_Sim[i] = relatorio_sim.registro[0].valor[1];
                    ConsumoFPC_Sim[i] = relatorio_sim.registro[0].valor[2];
                }

                if (i == (NumDiasMes + 1))
                {
                    // zera
                    ConsumoP_Sim[i] = relatorio_sim.registro[NumDiasMes - 1].valor[0];
                    ConsumoFPI_Sim[i] = relatorio_sim.registro[NumDiasMes - 1].valor[1];
                    ConsumoFPC_Sim[i] = relatorio_sim.registro[NumDiasMes - 1].valor[2];
                }

                if (i >= 1 && i <= NumDiasMes)
                {
                    // copia
                    j = i - 1;

                    ConsumoP[i] = relatorio.registro[j].valor[0];
                    ConsumoFPI[i] = relatorio.registro[j].valor[1];
                    ConsumoFPC[i] = relatorio.registro[j].valor[2];

                    // verifica consumo maximo
                    if ((ConsumoP[i] + ConsumoFPI[i] + ConsumoFPC[i]) > Consumo_max_grafico)
                        Consumo_max_grafico = (ConsumoP[i] + ConsumoFPI[i] + ConsumoFPC[i]);

                    // copia simulacao 
                    ConsumoP_Sim[i] = relatorio_sim.registro[j].valor[0];
                    ConsumoFPI_Sim[i] = relatorio_sim.registro[j].valor[1];
                    ConsumoFPC_Sim[i] = relatorio_sim.registro[j].valor[2];

                    // verifica consumo maximo
                    if ((ConsumoP_Sim[i] + ConsumoFPI_Sim[i] + ConsumoFPC_Sim[i]) > Consumo_max_grafico)
                        Consumo_max_grafico = (ConsumoP_Sim[i] + ConsumoFPI_Sim[i] + ConsumoFPC_Sim[i]);
                }
            }

            Consumo_max_grafico = Consumo_max_grafico * 1.1;

            if (Consumo_max_grafico == 0.0)
            {
                Consumo_max_grafico = 10.0;
            }

            ViewBag.ConsMaxGrafico = Consumo_max_grafico;

            ViewBag.ConsumoP = ConsumoP;
            ViewBag.ConsumoFPI = ConsumoFPI;
            ViewBag.ConsumoFPC = ConsumoFPC;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            // simulacao
            ViewBag.ConsumoP_Sim = ConsumoP_Sim;
            ViewBag.ConsumoFPI_Sim = ConsumoFPI_Sim;
            ViewBag.ConsumoFPC_Sim = ConsumoFPC_Sim;
            ViewBag.Datas_Sim = Datas_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;
            ViewBag.Dias_Sim = Dias_Sim;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.MenuTexts.MenuLateralConsumo;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Mensal;

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:y}", DataAtual);

            ViewBag.NumDiasMes = NumDiasMes;

            // consumo
            double ConsP = analise.analise_valor.consumo[0];
            double ConsFPI = analise.analise_valor.consumo[1];
            double ConsFPC = analise.analise_valor.consumo[2];
            double ConsTotal = (analise.analise_valor.consumo[0] + analise.analise_valor.consumo[1] + analise.analise_valor.consumo[2]);

            // consumo simulacao
            double ConsP_Sim = analise_sim.analise_valor.consumo[0];
            double ConsFPI_Sim = analise_sim.analise_valor.consumo[1];
            double ConsFPC_Sim = analise_sim.analise_valor.consumo[2];
            double ConsTotal_Sim = (analise_sim.analise_valor.consumo[0] + analise_sim.analise_valor.consumo[1] + analise_sim.analise_valor.consumo[2]);

            // consumo ponta
            if (ConsP < 100.0)
            {
                ViewBag.ConsP = string.Format("{0:#,##0.0}", ConsP);
            }
            else
            {
                ViewBag.ConsP = string.Format("{0:#,##0}", ConsP);
            }
            ViewBag.ConsPN = ConsP;

            // consumo ponta simulacao 
            if (ConsP_Sim < 100.0)
            {
                ViewBag.ConsP_Sim = string.Format("{0:#,##0.0}", ConsP_Sim);
            }
            else
            {
                ViewBag.ConsP_Sim = string.Format("{0:#,##0}", ConsP_Sim);
            }
            ViewBag.ConsPN_Sim = ConsP_Sim;

            // consumo fora ponta indutivo
            if (ConsFPI < 100.0)
            {
                ViewBag.ConsFPI = string.Format("{0:#,##0.0}", ConsFPI);
            }
            else
            {
                ViewBag.ConsFPI = string.Format("{0:#,##0}", ConsFPI);
            }
            ViewBag.ConsFPIN = ConsFPI;

            // consumo fora ponta indutivo simulacao
            if (ConsFPI_Sim < 100.0)
            {
                ViewBag.ConsFPI_Sim = string.Format("{0:#,##0.0}", ConsFPI_Sim);
            }
            else
            {
                ViewBag.ConsFPI_Sim = string.Format("{0:#,##0}", ConsFPI_Sim);
            }
            ViewBag.ConsFPIN_Sim = ConsFPI_Sim;

            // consumo fora ponta capacitivo
            if (ConsFPC < 100.0)
            {
                ViewBag.ConsFPC = string.Format("{0:#,##0.0}", ConsFPC);
            }
            else
            {
                ViewBag.ConsFPC = string.Format("{0:#,##0}", ConsFPC);
            }
            ViewBag.ConsFPCN = ConsFPC;

            // consumo fora ponta capacitivo simulacao
            if (ConsFPC_Sim < 100.0)
            {
                ViewBag.ConsFPC_Sim = string.Format("{0:#,##0.0}", ConsFPC_Sim);
            }
            else
            {
                ViewBag.ConsFPC_Sim = string.Format("{0:#,##0}", ConsFPC_Sim);
            }
            ViewBag.ConsFPCN_Sim = ConsFPC_Sim;

            // consumo total 
            if (ConsTotal < 100.0)
            {
                ViewBag.ConsTotal = string.Format("{0:#,##0.0}", ConsTotal);
            }
            else
            {
                ViewBag.ConsTotal = string.Format("{0:#,##0}", ConsTotal);
            }
            ViewBag.ConsTotalN = ConsTotal;

            // consumo total simulacao
            if (ConsTotal_Sim < 100.0)
            {
                ViewBag.ConsTotal_Sim = string.Format("{0:#,##0.0}", ConsTotal_Sim);
            }
            else
            {
                ViewBag.ConsTotal_Sim = string.Format("{0:#,##0}", ConsTotal_Sim);
            }
            ViewBag.ConsTotalN_Sim = ConsTotal_Sim;

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Consumo Mensal XLS
        private HSSFWorkbook Consumo_Mensal_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string str_fpc = string.Format("Fora de Ponta Capacitivo ({0})", ViewBag.UnidadeConsumo);
            string str_fpi = string.Format("Fora de Ponta Indutivo ({0})", ViewBag.UnidadeConsumo);
            string str_ponta = string.Format("Ponta ({0})", ViewBag.UnidadeConsumo);
            string str_total = string.Format("Total ({0})", ViewBag.UnidadeConsumo);
            string[] cabecalho = { "Data", str_fpc, str_fpi, str_ponta, str_total };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < (ViewBag.NumDiasMes+1); i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // consumo fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.ConsumoFPC[i], _1CellStyle);

                // consumo fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.ConsumoFPI[i], _1CellStyle);

                // consumo ponta
                numeroCelulaXLS(row, 3, ViewBag.ConsumoP[i], _1CellStyle);

                // consumo total
                numeroCelulaXLS(row, 4, ViewBag.ConsumoP[i] + ViewBag.ConsumoFPI[i] + ViewBag.ConsumoFPC[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Consumo", "Relatório Mensal", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string str_consumo = string.Format("Consumo ({0})", ViewBag.UnidadeConsumo);
            string[] cab_cons = { str_consumo, "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // consumo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Consumo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.ConsFPCN, _1CellStyle);
            numeroCelulaXLS(row, 2, ViewBag.ConsFPIN, _1CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.ConsPN, _1CellStyle);
            numeroCelulaXLS(row, 4, ViewBag.ConsTotalN, _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Consumo Mensal XLS Simulacao
        private HSSFWorkbook Consumo_Mensal_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho simulacao
            string[] simulacao = { "Relatório de Consumo - Simulação" };

            // simulação 
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas do titulo simulacao
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 5));

            // cabecalho
            string str_fpc = string.Format("Fora de Ponta Capacitivo ({0})", ViewBag.UnidadeConsumo);
            string str_fpi = string.Format("Fora de Ponta Indutivo ({0})", ViewBag.UnidadeConsumo);
            string str_ponta = string.Format("Ponta ({0})", ViewBag.UnidadeConsumo);
            string str_total = string.Format("Total ({0})", ViewBag.UnidadeConsumo);
            string[] cabecalho = { "Data", str_fpc, str_fpi, str_ponta, str_total };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < (ViewBag.NumDiasMes + 1); i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[i], _dataStyle);

                // consumo fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.ConsumoFPC_Sim[i], _1CellStyle);

                // consumo fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.ConsumoFPI_Sim[i], _1CellStyle);

                // consumo ponta
                numeroCelulaXLS(row, 3, ViewBag.ConsumoP_Sim[i], _1CellStyle);

                // consumo total
                numeroCelulaXLS(row, 4, ViewBag.ConsumoP_Sim[i] + ViewBag.ConsumoFPI_Sim[i] + ViewBag.ConsumoFPC_Sim[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Consumo - Simulação", "Relatório Mensal", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string str_consumo = string.Format("Consumo ({0})", ViewBag.UnidadeConsumo);
            string[] cab_cons = { str_consumo, "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // consumo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Consumo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.ConsFPCN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 2, ViewBag.ConsFPIN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.ConsPN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 4, ViewBag.ConsTotalN_Sim, _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Consumo Anual
        private void Consumo_Anual(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_ANUAL relatorio = new RELAT_ANUAL();
            RELAT_ANUAL_ANALISE analise = new RELAT_ANUAL_ANALISE();

            RELAT_ANUAL relatorio_sim = new RELAT_ANUAL();
            RELAT_ANUAL_ANALISE analise_sim = new RELAT_ANUAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // calcula valores
            retorno = SmCalcDB_Energia_RelatAnual((char)0, ref config_interface, (char)2, (char)IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_ANUAL(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   1, 1, 1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // maior consumo
            double maior_consumo = analise.analise_valor.consumo[0] + analise.analise_valor.consumo[1];

            // grafico
            var ConsumoP = new double[14];
            var ConsumoFPI = new double[14];
            var ConsumoFPC = new double[14];
            var Datas = new string[14];
            var DatasN = new DateTime[14];
            var Meses = new string[14];

            // grafico simulacao
            var ConsumoP_Sim = new double[14];
            var ConsumoFPI_Sim = new double[14];
            var ConsumoFPC_Sim = new double[14];
            var Datas_Sim = new string[14];
            var DatasN_Sim = new DateTime[14];
            var Meses_Sim = new string[14];

            double Consumo_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 mes para a primeira barra
            strData = strData.AddMonths(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 14; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Meses[i] = string.Format("{0:MMMM}", strData);

                // formata label simulacao
                Datas_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Sim[i] = strData;
                Meses_Sim[i] = string.Format("{0:MMMM}", strData);

                // proximo mes
                strData = strData.AddMonths(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    ConsumoP[i] = relatorio.registro[0].valor[0];
                    ConsumoFPI[i] = relatorio.registro[0].valor[1];
                    ConsumoFPC[i] = relatorio.registro[0].valor[2];

                    // zera simulacao
                    ConsumoP_Sim[i] = relatorio_sim.registro[0].valor[0];
                    ConsumoFPI_Sim[i] = relatorio_sim.registro[0].valor[1];
                    ConsumoFPC_Sim[i] = relatorio_sim.registro[0].valor[2];
                }

                if (i == 13)
                {
                    // zera
                    ConsumoP[i] = relatorio.registro[11].valor[0];
                    ConsumoFPI[i] = relatorio.registro[11].valor[1];
                    ConsumoFPC[i] = relatorio.registro[11].valor[2];

                    // zera simulacao 
                    ConsumoP_Sim[i] = relatorio_sim.registro[11].valor[0];
                    ConsumoFPI_Sim[i] = relatorio_sim.registro[11].valor[1];
                    ConsumoFPC_Sim[i] = relatorio_sim.registro[11].valor[2];
                }

                if (i >= 1 && i <= 12)
                {
                    // copia
                    j = i - 1;

                    ConsumoP[i] = relatorio.registro[j].valor[0];
                    ConsumoFPI[i] = relatorio.registro[j].valor[1];
                    ConsumoFPC[i] = relatorio.registro[j].valor[2];

                    // verifica consumo maximo
                    if ((ConsumoP[i] + ConsumoFPI[i] + ConsumoFPC[i]) > Consumo_max_grafico)
                        Consumo_max_grafico = (ConsumoP[i] + ConsumoFPI[i] + ConsumoFPC[i]);

                    // copia simulacao 
                    ConsumoP_Sim[i] = relatorio_sim.registro[j].valor[0];
                    ConsumoFPI_Sim[i] = relatorio_sim.registro[j].valor[1];
                    ConsumoFPC_Sim[i] = relatorio_sim.registro[j].valor[2];

                    // verifica consumo maximo
                    if ((ConsumoP_Sim[i] + ConsumoFPI_Sim[i] + ConsumoFPC_Sim[i]) > Consumo_max_grafico)
                        Consumo_max_grafico = (ConsumoP_Sim[i] + ConsumoFPI_Sim[i] + ConsumoFPC_Sim[i]);
                }
            }

            Consumo_max_grafico = Consumo_max_grafico * 1.1;

            if (Consumo_max_grafico == 0.0)
            {
                Consumo_max_grafico = 10.0;
            }

            ViewBag.ConsMaxGrafico = Consumo_max_grafico;

            ViewBag.ConsumoP = ConsumoP;
            ViewBag.ConsumoFPI = ConsumoFPI;
            ViewBag.ConsumoFPC = ConsumoFPC;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Meses = Meses;

            // simulacao
            ViewBag.ConsumoP_Sim = ConsumoP_Sim;
            ViewBag.ConsumoFPI_Sim = ConsumoFPI_Sim;
            ViewBag.ConsumoFPC_Sim = ConsumoFPC_Sim;
            ViewBag.Datas_Sim = Datas_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;
            ViewBag.Meses_Sim = Meses_Sim;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.MenuTexts.MenuLateralConsumo;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Anual;

            // data atual
            ViewBag.DataAtual = string.Format("{0:yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:yyyy}", DataAtual);

            // consumo
            double ConsP = analise.analise_valor.consumo[0];
            double ConsFPI = analise.analise_valor.consumo[1];
            double ConsFPC = analise.analise_valor.consumo[2];
            double ConsTotal = (analise.analise_valor.consumo[0] + analise.analise_valor.consumo[1] + analise.analise_valor.consumo[2]);

            // consumo simulacao
            double ConsP_Sim = analise_sim.analise_valor.consumo[0];
            double ConsFPI_Sim = analise_sim.analise_valor.consumo[1];
            double ConsFPC_Sim = analise_sim.analise_valor.consumo[2];
            double ConsTotal_Sim = (analise_sim.analise_valor.consumo[0] + analise_sim.analise_valor.consumo[1] + analise_sim.analise_valor.consumo[2]);

            // consumo ponta 
            if (ConsP < 100.0)
            {
                ViewBag.ConsP = string.Format("{0:#,##0.0}", ConsP);
            }
            else
            {
                ViewBag.ConsP = string.Format("{0:#,##0}", ConsP);
            }
            ViewBag.ConsPN = ConsP;

            // consumo ponta simulacao
            if (ConsP_Sim < 100.0)
            {
                ViewBag.ConsP_Sim = string.Format("{0:#,##0.0}", ConsP_Sim);
            }
            else
            {
                ViewBag.ConsP_Sim = string.Format("{0:#,##0}", ConsP_Sim);
            }
            ViewBag.ConsPN_Sim = ConsP_Sim;

            // consumo fora ponta indutivo 
            if (ConsFPI < 100.0)
            {
                ViewBag.ConsFPI = string.Format("{0:#,##0.0}", ConsFPI);
            }
            else
            {
                ViewBag.ConsFPI = string.Format("{0:#,##0}", ConsFPI);
            }
            ViewBag.ConsFPIN = ConsFPI;

            // consumo fora ponta indutivo simulacao
            if (ConsFPI_Sim < 100.0)
            {
                ViewBag.ConsFPI_Sim = string.Format("{0:#,##0.0}", ConsFPI_Sim);
            }
            else
            {
                ViewBag.ConsFPI_Sim = string.Format("{0:#,##0}", ConsFPI_Sim);
            }
            ViewBag.ConsFPIN_Sim = ConsFPI_Sim;

            // consumo fora ponta capacitivo
            if (ConsFPC < 100.0)
            {
                ViewBag.ConsFPC = string.Format("{0:#,##0.0}", ConsFPC);
            }
            else
            {
                ViewBag.ConsFPC = string.Format("{0:#,##0}", ConsFPC);
            }
            ViewBag.ConsFPCN = ConsFPC;

            // consumo fora ponta capacitivo simulacao
            if (ConsFPC_Sim < 100.0)
            {
                ViewBag.ConsFPC_Sim = string.Format("{0:#,##0.0}", ConsFPC_Sim);
            }
            else
            {
                ViewBag.ConsFPC_Sim = string.Format("{0:#,##0}", ConsFPC_Sim);
            }
            ViewBag.ConsFPCN_Sim = ConsFPC_Sim;

            // consumo total 
            if (ConsTotal < 100.0)
            {
                ViewBag.ConsTotal = string.Format("{0:#,##0.0}", ConsTotal);
            }
            else
            {
                ViewBag.ConsTotal = string.Format("{0:#,##0}", ConsTotal);
            }
            ViewBag.ConsTotalN = ConsTotal;

            // consumo total simulacao
            if (ConsTotal_Sim < 100.0)
            {
                ViewBag.ConsTotal_Sim = string.Format("{0:#,##0.0}", ConsTotal_Sim);
            }
            else
            {
                ViewBag.ConsTotal_Sim = string.Format("{0:#,##0}", ConsTotal_Sim);
            }
            ViewBag.ConsTotalN_Sim = ConsTotal_Sim;

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddYears(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Consumo Anual XLS
        private HSSFWorkbook Consumo_Anual_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string str_fpc = string.Format("Fora de Ponta Capacitivo ({0})", ViewBag.UnidadeConsumo);
            string str_fpi = string.Format("Fora de Ponta Indutivo ({0})", ViewBag.UnidadeConsumo);
            string str_ponta = string.Format("Ponta ({0})", ViewBag.UnidadeConsumo);
            string str_total = string.Format("Total ({0})", ViewBag.UnidadeConsumo);
            string[] cabecalho = { "Data", str_fpc, str_fpi, str_ponta, str_total };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 13; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // consumo fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.ConsumoFPC[i], _1CellStyle);

                // consumo fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.ConsumoFPI[i], _1CellStyle);

                // consumo ponta
                numeroCelulaXLS(row, 3, ViewBag.ConsumoP[i], _1CellStyle);

                // consumo total
                numeroCelulaXLS(row, 4, ViewBag.ConsumoP[i] + ViewBag.ConsumoFPI[i] + ViewBag.ConsumoFPC[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Consumo", "Relatório Anual", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string str_consumo = string.Format("Consumo ({0})", ViewBag.UnidadeConsumo);
            string[] cab_cons = { str_consumo, "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // consumo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Consumo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.ConsFPCN, _1CellStyle);
            numeroCelulaXLS(row, 2, ViewBag.ConsFPIN, _1CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.ConsPN, _1CellStyle);
            numeroCelulaXLS(row, 4, ViewBag.ConsTotalN, _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Consumo Anual XLS Simulacao
        private HSSFWorkbook Consumo_Anual_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho simulacao
            string[] simulacao = { "Relatório de Consumo - Simulação" };

            // simulação 
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas do titulo simulacao
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 4));

            // cabecalho
            string str_fpc = string.Format("Fora de Ponta Capacitivo ({0})", ViewBag.UnidadeConsumo);
            string str_fpi = string.Format("Fora de Ponta Indutivo ({0})", ViewBag.UnidadeConsumo);
            string str_ponta = string.Format("Ponta ({0})", ViewBag.UnidadeConsumo);
            string str_total = string.Format("Total ({0})", ViewBag.UnidadeConsumo);
            string[] cabecalho = { "Data", str_fpc, str_fpi, str_ponta, str_total };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 13; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[i], _dataStyle);

                // consumo fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.ConsumoFPC_Sim[i], _1CellStyle);

                // consumo fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.ConsumoFPI_Sim[i], _1CellStyle);

                // consumo ponta
                numeroCelulaXLS(row, 3, ViewBag.ConsumoP_Sim[i], _1CellStyle);

                // consumo total
                numeroCelulaXLS(row, 4, ViewBag.ConsumoP_Sim[i] + ViewBag.ConsumoFPI_Sim[i] + ViewBag.ConsumoFPC_Sim[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Consumo - Simulação", "Relatório Anual", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string str_consumo = string.Format("Consumo ({0})", ViewBag.UnidadeConsumo);
            string[] cab_cons = { str_consumo, "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // consumo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Consumo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.ConsFPCN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 2, ViewBag.ConsFPIN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.ConsPN_Sim, _1CellStyle);
            numeroCelulaXLS(row, 4, ViewBag.ConsTotalN_Sim, _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Consumo Ativo/Reativo/Fator de Potencia exportar XLS
        private HSSFWorkbook Cons_exportar_XLS(int IDCliente, int IDMedicao, DateTime DataIni, DateTime DataFim)
        {
            // datas
            ViewBag.DataAtualN = DataIni;
            ViewBag.DataFinalN = DataFim;

            // medicao
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);

            // deslocamento inicial da medicao
            int min_deslocado = 0;

            DateTime DataIniExportar = new DateTime(DataIni.Year, DataIni.Month, DataIni.Day, 0, 15, 0);
            DateTime DataFimExportar = DataFim;

            if( medicao != null )
            {
                // deslocamento inicial da medicao
                // desloca somente em hora

                // verificar com o Fabio sobre esse deslocamento que acredito nao esteja correto
                // deslocamente - hora do grafico
                // 00 - 00:00            01
                // 01 - 00:15            01
                // 02 - 00:30            01
                // 03 - 00:45            02
                // 04 - 01:00            02
                // 05 - 01:15            02
                // 06 - 01:30            02
                // 07 - 01:45            03
                // 08 - 02:00            03
                // 09 - 02:15            03
                // 10 - 02:30            03
                min_deslocado = (((medicao.IDDeslocamento+1) / 4) * 4) * 15;

                // data a ser utilizada com deslocamento do span
                DataIniExportar = DataIniExportar.AddMinutes(min_deslocado);
                DataFimExportar = DataFimExportar.AddMinutes(min_deslocado);

                // forca dizer q nao tem deslocamento
                min_deslocado = 0;
            }

            // calcula
            EN_Metodos enMetodos = new EN_Metodos();
            List<EN_Dominio> enRegistros = enMetodos.ListarTodosPeriodo(IDCliente, IDMedicao, DataIniExportar, DataFimExportar);

            // cria excel
            HSSFWorkbook workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            ISheet sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Período", "Consumo Ativo", "Consumo Reativo", "Fator de Potência" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;

            if (enRegistros != null)
            {
                DateTime data_ant = new DateTime(2000,1,1,0,0,0);
                int indice_ant = 100;
                int indice = 0;

                // valores
                double cons_ativo = 0.0;
                double cons_reativo = 0.0;
                int periodo = 0;

                // percorre valores
                foreach (EN_Dominio registro in enRegistros)
                {
                    // calcula o indice da hora
                    DateTime data_atual = registro.DataHora;

                    int h = data_atual.Hour;
                    int m = data_atual.Minute;

                    if( h == 0 && m <= min_deslocado )
                    {
                        indice = 23;
                    }
                    else
                    {
                        if( m == min_deslocado )
                        {
                            indice = (h - 1) * 4;
                        }
                        else
                        {
                            indice = (h * 4) + ((m - min_deslocado) / 15);
                        }

                        indice = indice / 4;
                    }

                    // verifica se primeiro indice dessa hora
                    if (indice_ant == 100)
                    {
                        indice_ant = indice;

                        cons_ativo = 0.0;
                        cons_reativo = 0.0;

                        periodo = registro.Periodo;

                        data_ant = data_atual;
                    }

                    // verifica se mudou data
                    bool mudou_data = false;

                    if (data_ant.Day != data_atual.Day || data_ant.Month != data_atual.Month || data_ant.Year != data_atual.Year)
                    {
                        // se for meia noite nao considero mudanca de data
                        if( !(data_atual.Hour == 0 && data_atual.Minute == 0) )
                        {
                            mudou_data = true;
                        }
                    }

                    // verifica se mudou de hora
                    if( indice_ant != indice || mudou_data )
                    {
                        //
                        // coloca na planilha
                        //

                        // adiciona linha
                        AdicionaLinha_Consumo(workbook, sheet, rowIndex, data_ant, indice_ant, min_deslocado, cons_ativo, cons_reativo, periodo, _datahoraStyle, _intCellStyle, _1CellStyle, _3CellStyle, _negritoCellStyle);

                        // proxima
                        rowIndex++;

                        //
                        // inicia indices
                        //
                        indice_ant = indice;

                        cons_ativo = 0.0;
                        cons_reativo = 0.0;

                        periodo = registro.Periodo;
                    }

                    // soma consumo
                    cons_ativo += registro.Ativo;
                    cons_reativo += registro.Reativo;

                    // copia data
                    data_ant = data_atual;
                }

                // verifica se tem ultima hora
                if( cons_ativo != 0.0 )
                {
                    // adiciona linha
                    AdicionaLinha_Consumo(workbook, sheet, rowIndex, data_ant, indice_ant, min_deslocado, cons_ativo, cons_reativo, periodo, _datahoraStyle, _intCellStyle, _1CellStyle, _3CellStyle, _negritoCellStyle);
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Consumo", "", _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(9).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }

        void AdicionaLinha_Consumo(HSSFWorkbook workbook, ISheet sheet, int rowIndex, DateTime data_ant, int indice_ant, int min_deslocado, double cons_ativo, double cons_reativo, int periodo, ICellStyle _datahoraStyle, ICellStyle _intCellStyle, ICellStyle _1CellStyle, ICellStyle _3CellStyle, ICellStyle _negritoCellStyle)
        {
            IRow row;

            // adiciona linha
            row = sheet.CreateRow(rowIndex);

            // data e hora
            DateTime DataHoraAtual = data_ant;

            if (indice_ant == 23)
            {
                DataHoraAtual = new DateTime(data_ant.Year, data_ant.Month, data_ant.Day, 0, 0, 0);
            }
            else
            {
                DataHoraAtual = new DateTime(data_ant.Year, data_ant.Month, data_ant.Day, indice_ant + 1, 0, 0);
            }

            // desloca
            DataHoraAtual = DataHoraAtual.AddMinutes(min_deslocado);

            datahoraCelulaXLS(row, 0, DataHoraAtual, _datahoraStyle);

            // periodo
            numeroCelulaXLS(row, 1, periodo, _intCellStyle);

            // demanda ativa
            numeroCelulaXLS(row, 2, cons_ativo / 4.0, _intCellStyle);

            // demanda reativa
            numeroCelulaXLS(row, 3, cons_reativo / 4.0, _intCellStyle);

            // fator de potencia
            double fatpot = 1.0;

            if (cons_ativo != 0.0)
            {
                double aux1 = cons_reativo / cons_ativo;
                fatpot = 1.0 / Math.Sqrt(1.0 + (aux1 * aux1));

                fatpot *= (cons_reativo < 0.0) ? -1.0 : 1.0;
            }

            numeroCelulaXLS(row, 4, fatpot, _3CellStyle);

            return;
        }
    }
}