﻿using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class DownloadController : Controller
    {
        // permissoes
        private void Permissoes()
        {
            // permissoes
            // 0 - permissao de Admin: ve e escreve em tudo
            // 1 - permissao de Gerente: ve tudo e escreve parte
            // 2 - permissao de Operador: ve tudo e nao pode escrever
            // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)
            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
            // 5 - permissao de Consultor: ve tudo e escreve parte 
            // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte 
            int IDTipoAcesso = ViewBag._IDTipoAcesso;
            int IDConsultor = ViewBag._IDConsultor;

            switch (IDTipoAcesso)
            {
                case TIPO_ACESSO.MASTER:            // master
                case TIPO_ACESSO.GESTAL_ADMIN:      // admin

                    ViewBag.Permissao = PERMISSOES.ADMIN;
                    break;

                case TIPO_ACESSO.CONSULTOR:         // consultor
                case TIPO_ACESSO.CONSULTOR_ADMIN:   // consultor - administrador

                    ViewBag.Permissao = PERMISSOES.CONSULTOR;

                    // verifica se CPFL
                    if (IDConsultor == CLIENTES_ESPECIAIS.CPFL)
                    {
                        // CPFL é um super consultor
                        ViewBag.Permissao = PERMISSOES.SUPER_CONSULTOR;
                    }
                    break;

                case TIPO_ACESSO.CLIENTE_ADMIN:     // cliente - administrador

                    ViewBag.Permissao = PERMISSOES.GERENTE;
                    break;

                default:
                case TIPO_ACESSO.CLIENTE_OPER:      // operador
                case TIPO_ACESSO.GESTAL_VENDAS:     // vendas
                case TIPO_ACESSO.CONSULTOR_OPER:    // consultor - operador
                case TIPO_ACESSO.DEMONSTRACAO:      // demo

                    ViewBag.Permissao = PERMISSOES.OPERADOR;
                    break;

                case TIPO_ACESSO.GESTAL_PRODUCAO:   // producao

                    ViewBag.Permissao = PERMISSOES.PRODUCAO;
                    break;

                case TIPO_ACESSO.GESTAL_SUPORTE:    // suporte

                    ViewBag.Permissao = PERMISSOES.SUPORTE;
                    break;
            }
        }
    }
}