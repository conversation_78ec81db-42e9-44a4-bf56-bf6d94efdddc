﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

using NPOI.HSSF.UserModel;
using NPOI.XSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.HSSF.Util;
using NPOI.POIFS.FileSystem;
using NPOI.HPSF;

namespace SmartEnergy.Controllers
{
    public partial class DownloadController
    {
        // cabecalho tabela XLS
        private int cabecalhoTabelaXLS(HSSFWorkbook workbook, ISheet sheet, string[] cabecalho, int linha = 0)
        {
            // estilo do cabecalho
            var cabecalhoCellStyle = workbook.CreateCellStyle();

            // borda superior e inferior
            cabecalhoCellStyle.BorderTop = BorderStyle.Thin;
            cabecalhoCellStyle.BorderBottom = BorderStyle.Thin;

            // alinhamento centro
            cabecalhoCellStyle.Alignment = HorizontalAlignment.Center;

            // negrito
            var cabecalhoFont = workbook.CreateFont();
            cabecalhoFont.Boldweight = (short)FontBoldWeight.Bold;
            cabecalhoCellStyle.SetFont(cabecalhoFont);

            // adiciona cabecalho
            var rowIndex = linha;
            var row = sheet.CreateRow(rowIndex++);

            // adiciona itens do cabecalho
            int conta_item = 0;

            foreach (string item in cabecalho)
            {
                // data e hora
                var cell = row.CreateCell(conta_item);
                cell.SetCellValue(item);
                cell.CellStyle = cabecalhoCellStyle;

                // proximo
                conta_item++;
            }

            return (rowIndex);
        }

        // cabecalho resumo XLS
        private int cabecalhoResumoXLS(HSSFWorkbook workbook, ISheet sheet, string relatorio, string periodicidade, ICellStyle _negritoCellStyle)
        {
            int rowIndex = 0;

            // cria estilos
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _datahorafullStyle = criaEstiloXLS(workbook, 13);

            // cliente
            var row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Cliente", _negritoCellStyle);
            textoCelulaXLS(row, 1, ViewBag.ClienteNome);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // grupo de unidades
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Grupo de Unidades", _negritoCellStyle);
            textoCelulaXLS(row, 1, ViewBag.GrupoNome);

            // unidade
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Unidade", _negritoCellStyle);
            textoCelulaXLS(row, 1, ViewBag.UnidadeNome);

            // medicao
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Medição", _negritoCellStyle);
            textoCelulaXLS(row, 1, ViewBag.MedicaoNome);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // relatorio
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Relatório", _negritoCellStyle);
            textoCelulaXLS(row, 1, relatorio);

            // periodicidade
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "", _negritoCellStyle);
            textoCelulaXLS(row, 1, periodicidade);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // verifica se cabecalho de eventos
            if (periodicidade == "")
            {
                // data
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Data", _negritoCellStyle);
                datahoraCelulaXLS(row, 1, ViewBag.DataAtualN, _datahorafullStyle);
                datahoraCelulaXLS(row, 2, ViewBag.DataFinalN, _datahorafullStyle);
            }
            else
            {
                // data
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Data", _negritoCellStyle);
                datahoraCelulaXLS(row, 1, ViewBag.DataAtualN, _dataStyle);

                if (ViewBag.DataFinalN != null)
                    datahoraCelulaXLS(row, 2, ViewBag.DataFinalN, _dataStyle);
            }

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            return (rowIndex);
        }

        private int cabecalhoResumoEventosUsuariosXLS(HSSFWorkbook workbook, ISheet sheet, string relatorio, ICellStyle _negritoCellStyle)
        {
            int rowIndex = 0;

            // cria estilos
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _datahorafullStyle = criaEstiloXLS(workbook, 13);

            // cliente
            var row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Cliente", _negritoCellStyle);
            textoCelulaXLS(row, 1, ViewBag.ClienteNome);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // relatorio
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Relatório", _negritoCellStyle);
            textoCelulaXLS(row, 1, relatorio);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // data
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Data", _negritoCellStyle);
            datahoraCelulaXLS(row, 1, ViewBag.DataAtualN, _datahorafullStyle);
            datahoraCelulaXLS(row, 2, ViewBag.DataFinalN, _datahorafullStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            return (rowIndex);
        }

        // cria estilo XLS
        private ICellStyle criaEstiloXLS(HSSFWorkbook workbook, int tipo_estilo)
        {
            // cria estilo
            ICellStyle estilo = null;
            estilo = workbook.CreateCellStyle();

            // cria estilo
            switch (tipo_estilo)
            {
                default:
                case 0:     // inteiro
                    estilo.DataFormat = workbook.CreateDataFormat().GetFormat("#,##0");
                    break;

                case 1:     // 1 casa decimal
                    estilo.DataFormat = workbook.CreateDataFormat().GetFormat("#,##0.0");
                    break;

                case 2:     // 2 casa decimal
                    estilo.DataFormat = workbook.CreateDataFormat().GetFormat("#,##0.00");
                    break;

                case 3:     // 3 casas decimais
                    estilo.DataFormat = workbook.CreateDataFormat().GetFormat("#,##0.000");
                    break;

                case 6:     // 6 casas decimais
                    estilo.DataFormat = workbook.CreateDataFormat().GetFormat("#,##0.000000");
                    break;

                case 4:     // inteiro
                    estilo.DataFormat = workbook.CreateDataFormat().GetFormat("0");
                    break;

                case 10:    // data e hora (DD/MM/AAAA HH:MM)
                    estilo.DataFormat = workbook.CreateDataFormat().GetFormat("dd/MM/yyyy HH:mm");
                    break;

                case 11:    // data (DD/MM/AAAA)
                    estilo.DataFormat = workbook.CreateDataFormat().GetFormat("dd/MM/yyyy");
                    break;

                case 12:    // hora (HH:MM)
                    estilo.DataFormat = workbook.CreateDataFormat().GetFormat("HH:mm");
                    break;

                case 13:    // data e hora (DD/MM/AAAA HH:MM:SS)
                    estilo.DataFormat = workbook.CreateDataFormat().GetFormat("dd/MM/yyyy HH:mm:ss");
                    break;

                case 20:    // texto negrito
                    var fonte = workbook.CreateFont();
                    fonte.Boldweight = (short)FontBoldWeight.Bold;
                    estilo.SetFont(fonte);
                    break;
            }

            // retorna estilo
            return (estilo);
        }

        // formata celula data e hora XLS
        private ICell datahoraCelulaXLS(IRow linha, int coluna, DateTime valor, ICellStyle estilo)
        {
            // cria celular
            var celula = linha.CreateCell(coluna);

            // tipo
            celula.SetCellType(CellType.Numeric);

            // valor
            celula.SetCellValue(valor);

            // aplica estilo
            celula.CellStyle = estilo;

            // retorna celula
            return (celula);
        }

        // formata celula numero XLS
        private ICell numeroCelulaXLS(IRow linha, int coluna, double valor, ICellStyle estilo)
        {
            // cria celular
            var celula = linha.CreateCell(coluna);

            // tipo
            celula.SetCellType(CellType.Numeric);

            // valor
            celula.SetCellValue(valor);

            // aplica estilo
            celula.CellStyle = estilo;

            // retorna celula
            return (celula);
        }

        // formata celula texto XLS
        private ICell textoCelulaXLS(IRow linha, int coluna, string valor, ICellStyle estilo = null)
        {
            // cria celular
            var celula = linha.CreateCell(coluna);

            // tipo
            celula.SetCellType(CellType.String);

            // valor
            celula.SetCellValue(valor);

            // aplica estilo
            if(estilo != null)
                celula.CellStyle = estilo;

            // retorna celula
            return (celula);
        }
    }
}