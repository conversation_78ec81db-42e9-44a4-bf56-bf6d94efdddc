﻿using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class DownloadController
    {
        // GET: Exportar Utilização da Demanda (JBS)
        public ActionResult Download_UtilizacaoDemanda(int IDCliente, int TipoDownload = 0)
        {
            // tela de ajuda - download
            CookieStore.SalvaCookie_String("PaginaAjuda", "Download_Demanda");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Download");

            // le cookies
            LeCookies_SmartEnergy();

            // data inicio
            DateTime data_hora_ini = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 15, 0);

            // data fim
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);
            data_hora_fim = data_hora_fim.AddMinutes(-15);

            // le cookie datahora
            DateTime data_hora_ini_cookie = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime data_hora_fim_cookie = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // verifica se existe datahora no cookie
            if (data_hora_ini_cookie.Year != 2000 && data_hora_fim_cookie.Year != 2000)
            {
                // copia data do cookie 
                data_hora_ini = data_hora_ini_cookie;
                data_hora_fim = data_hora_fim_cookie;
            }

            // converte
            DATAHORA dh_ini = new DATAHORA();
            DATAHORA dh_fim = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, data_hora_ini);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, data_hora_fim);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", data_hora_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", data_hora_fim);

            // data e hora atual
            ViewBag.DataIni = string.Format("{0:d}", data_hora_ini);
            ViewBag.HoraIni = string.Format("{0:HH:mm}", data_hora_ini);
            ViewBag.DataFim = string.Format("{0:d}", data_hora_fim);
            ViewBag.HoraFim = string.Format("{0:HH:mm}", data_hora_fim);

            ViewBag.DataTextoAtualIni = string.Format("{0:d} {1:HH:mm}", data_hora_ini, data_hora_ini);
            ViewBag.DataTextoAtualFim = string.Format("{0:d} {1:HH:mm}", data_hora_fim, data_hora_fim);

            ViewBag.DataAtualN = data_hora_ini;
            ViewBag.DataFinalN = data_hora_fim;

            // lista de medicoes habilitadas do usuario
            List<int> ConfigMedicaoList_Usuario = new List<int>();
            ConfigMedicaoList_Usuario = ViewBag._ConfigMed;

            // verifica se existe lista de medicoes
            if (ConfigMedicaoList_Usuario != null)
            {
                if (ConfigMedicaoList_Usuario.Count == 0)
                {
                    ConfigMedicaoList_Usuario = null;
                }
            }

            // le medicoes configuradas do grupo para lista
            CliGateGrupoUnidMedicoesMetodos medicoesMetodos = new CliGateGrupoUnidMedicoesMetodos();
            List<CliGateGrupoUnidMedicoesDominio> listaMedicoes = new List<CliGateGrupoUnidMedicoesDominio>();

            // le medicoes de energia habilitadas para o usuario no cliente
            listaMedicoes = medicoesMetodos.ListarPorIDCliente(IDCliente, ConfigMedicaoList_Usuario, 0);

            ViewBag.listaMedicoes = listaMedicoes;

            return View();
        }


        // estruturas
        private static IDictionary<Guid, int> tasks_utilizacaoDemanda = new Dictionary<Guid, int>();

        public ActionResult Download_UtilizacaoDemanda_Inicia(string DataIni, string DataFim, List<GrupoMedicoesMedDominio> Medicoes)
        {
            // pega data
            DateTime dateValueIni = DateTime.Parse(DataIni);
            DateTime dateValueFim = DateTime.Parse(DataFim);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", dateValueIni);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", dateValueFim);


            // taskID
            Guid taskId = Guid.NewGuid();
            tasks_utilizacaoDemanda.Add(taskId, 0);

            // salva cookie
            CookieStore.SalvaCookie_GUID("taskID", taskId);

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;

            // lista de erros
            var listaErros = new List<string>();

            // lista de medicoes habilitadas do usuario
            List<int> ConfigMedicaoList_Usuario = new List<int>();
            ConfigMedicaoList_Usuario = ViewBag._ConfigMed;

            // verifica se existe lista de medicoes
            if (ConfigMedicaoList_Usuario != null)
            {
                if (ConfigMedicaoList_Usuario.Count == 0)
                {
                    ConfigMedicaoList_Usuario = null;
                }
            }

            // le medicoes configuradas do grupo para lista
            CliGateGrupoUnidMedicoesMetodos medicoesMetodos = new CliGateGrupoUnidMedicoesMetodos();
            List<CliGateGrupoUnidMedicoesDominio> listaMedicoes = new List<CliGateGrupoUnidMedicoesDominio>();

            // le medicoes de energia habilitadas para o usuario no cliente
            listaMedicoes = medicoesMetodos.ListarPorIDCliente(IDCliente, ConfigMedicaoList_Usuario, 0);

            // nome do arquivo
            string nomeArquivo = string.Format("UtilizacaoDemanda_{0}_{1:yyyyMMddHHmm}_{2:yyyyMMddHHmm}.xlsx", IDCliente, dateValueIni, dateValueFim);


            // task
            Task.Factory.StartNew(() =>
            {
                // barra de progresso
                int total = 0;
                int atual = 0;

                // percorre medicoes
                if (listaMedicoes != null)
                {
                    //
                    // Cria planilha
                    //

                    // barra de progresso
                    total = Medicoes.Count;

                    // Planilha Excel
                    XSSFWorkbook workbook = new XSSFWorkbook();

                    // Cabeçalho
                    ISheet sheet = UtilizacaoDem_Cabecalho_XLSX(ref workbook);

                    // indice
                    int rowIndex = 1;

                    // percorre medicoes
                    foreach (GrupoMedicoesMedDominio medicao in Medicoes)
                    {
                        // encontra medicao na lista
                        CliGateGrupoUnidMedicoesDominio med = listaMedicoes.Find(x => x.IDMedicao == medicao.IDMedicao);

                        if (med == null)
                        {
                            continue;
                        }

                        // update task progress
                        atual++;
                        double progresso = ((double)atual / (double)total) * 100;
                        tasks_utilizacaoDemanda[taskId] = (int)progresso;

                        // lê configuração da medição
                        MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                        MedicoesDominio med_config = medicaoMetodos.ListarPorId(med.IDMedicao);

                        if (med_config != null)
                        {
                            // Insere dados da medição na planilha XLSX
                            UtilizacaoDem_Medicao_XLSX(med, med_config, dateValueIni, dateValueFim, ref workbook, ref sheet, ref rowIndex);
                        }
                    }

                    // Aba Resumo da planilha XLSX
                    UtilizacaoDem_Resumo_XLSX(ref workbook, ref sheet);

                    //
                    // Salva planilha
                    //

                    // diretorio
                    string pathForSaving = Server.MapPath("~/Temp");

                    // nome do arquivo completo
                    string nomeArquivoPath = Path.Combine(pathForSaving, nomeArquivo);

                    // coloca planilha no arquivo
                    using (FileStream fs = new FileStream(nomeArquivoPath, FileMode.Create, FileAccess.Write))
                    {
                        workbook.Write(fs);
                    } 
                }

                // terminou
                tasks_utilizacaoDemanda.Remove(taskId);
            });

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = taskId,
                FileName = nomeArquivo
            };

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        public ActionResult Download_UtilizacaoDemanda_Progress(Guid id)
        {
            return Json(tasks_utilizacaoDemanda.Keys.Contains(id) ? tasks_utilizacaoDemanda[id] : 101, JsonRequestBehavior.AllowGet);
        }

        // GET: Medicao XLSX Download
        [HttpGet]
        public virtual ActionResult UtilizacaoDemanda_XLSX_Download(string fileName)
        {
            // le arquivo Excel
            string fullPath = Path.Combine(Server.MapPath("~/Temp"), fileName);
            return File(fullPath, "application/vnd.ms-excel", fileName);
        }


        // Cabecalho da planilha XLSX
        private ISheet UtilizacaoDem_Cabecalho_XLSX(ref XSSFWorkbook workbook)
        {
            // cria planilha
            ISheet sheet = workbook.CreateSheet("Demanda Ativa (kW)");

            // cabecalho
            string[] cabecalho = { "Data", "IDMedicao", "Medições", "Unidades", 
                                   "00:15", "00:30", "00:45", "01:00", 
                                   "01:15", "01:30", "01:45", "02:00", 
                                   "02:15", "02:30", "02:45", "03:00", 
                                   "03:15", "03:30", "03:45", "04:00", 
                                   "04:15", "04:30", "04:45", "05:00", 
                                   "05:15", "05:30", "05:45", "06:00", 
                                   "06:15", "06:30", "06:45", "07:00", 
                                   "07:15", "07:30", "07:45", "08:00", 
                                   "08:15", "08:30", "08:45", "09:00", 
                                   "09:15", "09:30", "09:45", "10:00", 
                                   "10:15", "10:30", "10:45", "11:00", 
                                   "11:15", "11:30", "11:45", "12:00", 
                                   "12:15", "12:30", "12:45", "13:00", 
                                   "13:15", "13:30", "13:45", "14:00", 
                                   "14:15", "14:30", "14:45", "15:00", 
                                   "15:15", "15:30", "15:45", "16:00", 
                                   "16:15", "16:30", "16:45", "17:00", 
                                   "17:15", "17:30", "17:45", "18:00", 
                                   "18:15", "18:30", "18:45", "19:00", 
                                   "19:15", "19:30", "19:45", "20:00", 
                                   "20:15", "20:30", "20:45", "21:00", 
                                   "21:15", "21:30", "21:45", "22:00", 
                                   "22:15", "22:30", "22:45", "23:00", 
                                   "23:15", "23:30", "23:45", "00:00", 
                                   "Início Ponta", "Final Ponta", 
                                   "Máx. Ponta", "Máx. Fora de Ponta",
                                   "Mín. Ponta", "Mín. Fora de Ponta",
                                   "Dia", "Dia da Semana"
                                 };

            // adiciona cabecalho
            cabecalhoTabelaXLSX(workbook, sheet, cabecalho);

            return (sheet);
        }

        // Insere dados da medição na planilha XLSX
        private void UtilizacaoDem_Medicao_XLSX(CliGateGrupoUnidMedicoesDominio med, MedicoesDominio med_config, DateTime DataIni, DateTime DataFim, ref XSSFWorkbook workbook, ref ISheet sheet, ref int rowIndex)
        {
            // datas
            ViewBag.DataAtualN = DataIni;
            ViewBag.DataFinalN = DataFim;

            // calcula
            EN_Metodos enMetodos = new EN_Metodos();
            List<EN_Dominio> enRegistros = enMetodos.ListarTodosPeriodo(med.IDCliente, med.IDMedicao, DataIni, DataFim);

            // cria estilos
            ICellStyle _dataStyle = criaEstiloXLSX(workbook, 11);
            ICellStyle _datahoraStyle = criaEstiloXLSX(workbook, 10);
            ICellStyle _horaStyle = criaEstiloXLSX(workbook, 12);
            ICellStyle _intCellStyle = criaEstiloXLSX(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLSX(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLSX(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLSX(workbook, 20);


            //
            // CALCULA 
            //

            // inicializa dia
            DateTime data_hora_atual = new DateTime(DataIni.Year, DataIni.Month, DataIni.Day, 0, 15, 0);

            // percorre dias
            while (data_hora_atual <= DataFim)
            {
                // estruturas
                EN_Dominio[] demandas = new EN_Dominio[96];

                bool achou_max_min_p = false;
                bool achou_max_min_fp = false;
                double demanda_min_p = 0.0;
                double demanda_min_fp = 0.0;
                double demanda_max_p = 0.0;
                double demanda_max_fp = 0.0;

                // preenche estrutura
                for (int i = 0; i < 96; i++)
                {
                    EN_Dominio valor = new EN_Dominio();

                    valor.DataHora = new DateTime(data_hora_atual.Year, data_hora_atual.Month, data_hora_atual.Day, data_hora_atual.Hour, data_hora_atual.Minute, 0);
                    valor.Ativo = 0.0;
                    valor.Reativo = 0.0;
                    valor.Periodo = 0;

                    if (enRegistros != null)
                    {
                        // encontra registro
                        EN_Dominio registro = enRegistros.Find(x => x.DataHora == data_hora_atual);

                        if (registro != null)
                        {
                            // copia registro
                            valor.Ativo = registro.Ativo;
                            valor.Reativo = registro.Reativo;
                            valor.Periodo = registro.Periodo;

                            // verifica se ponta 
                            if (registro.Periodo == 0)
                            {
                                // verifica se achou máximo e mínimo
                                if (!achou_max_min_p)
                                {
                                    demanda_min_p = registro.Ativo;
                                    demanda_max_p = registro.Ativo;

                                    achou_max_min_p = true;
                                }

                                // minimo ponta
                                if (registro.Ativo < demanda_min_p)
                                {
                                    demanda_min_p = registro.Ativo;
                                }

                                // máximo ponta
                                if (registro.Ativo > demanda_max_p)
                                {
                                    demanda_max_p = registro.Ativo;
                                }
                            }
                            else
                            {
                                // verifica se achou máximo e mínimo
                                if (!achou_max_min_fp)
                                {
                                    demanda_min_fp = registro.Ativo;
                                    demanda_max_fp = registro.Ativo;

                                    achou_max_min_fp = true;
                                }

                                // minimo fora de ponta
                                if (registro.Ativo < demanda_min_fp)
                                {
                                    demanda_min_fp = registro.Ativo;
                                }

                                // máximo fora de ponta
                                if (registro.Ativo > demanda_max_fp)
                                {
                                    demanda_max_fp = registro.Ativo;
                                }
                            }
                        }
                    }

                    // copia
                    demandas[i] = valor;

                    // próximo 15 minutos
                    data_hora_atual = data_hora_atual.AddMinutes(15);
                }


                //
                // TABELA
                //

                // adiciona linha
                IRow row = sheet.CreateRow(rowIndex);
                int col = 0;

                // data (usa primeiro registro do dia)
                datahoraCelulaXLSX(row, col++, demandas[0].DataHora, _dataStyle);

                // IDMedicao
                numeroCelulaXLSX(row, col++, med.IDMedicao, _intCellStyle);

                // Nome da medição
                textoCelulaXLSX(row, col++, med.NomeMedicao);

                // Nome da unidade
                textoCelulaXLSX(row, col++, med.NomeUnidade);

                // demandas
                for (int i = 0; i < 96; i++)
                {
                    // demanda ativa
                    numeroCelulaXLSX(row, col++, demandas[i].Ativo, _1CellStyle);
                }

                // horário inicio ponta
                textoCelulaXLSX(row, col++, med_config.InicioP);

                // horário final ponta
                textoCelulaXLSX(row, col++, med_config.FimP);

                // máximo ponta
                numeroCelulaXLSX(row, col++, demanda_max_p, _1CellStyle);

                // máximo fora de ponta
                numeroCelulaXLSX(row, col++, demanda_max_fp, _1CellStyle);

                // mínimo ponta
                numeroCelulaXLSX(row, col++, demanda_min_p, _1CellStyle);

                // mínimo fora de ponta
                numeroCelulaXLSX(row, col++, demanda_min_fp, _1CellStyle);

                // dia da semana (usa primeiro registro do dia)
                int dia_semana = (int)demandas[0].DataHora.DayOfWeek + 1;
                numeroCelulaXLSX(row, col++, dia_semana, _intCellStyle);
                textoCelulaXLSX(row, col++, demandas[0].DataHora.ToString("dddd", new CultureInfo("pt-BR")));

                // proxima
                rowIndex++;
            }

            // retorna 
            return;
        }

        // Aba Resumo da planilha XLSX
        private void UtilizacaoDem_Resumo_XLSX(ref XSSFWorkbook workbook, ref ISheet sheet)
        {
            // largura de cada coluna
            for (int i = 0; i < sheet.GetRow(0).LastCellNum; i++)
            {
                if (i == 2 || i == 3)
                {
                    sheet.SetColumnWidth(i, 6000);
                }
                else
                {
                    sheet.SetColumnWidth(i, 3000);
                }
            }

            return;
        }
    }
}