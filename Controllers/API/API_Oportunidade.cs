﻿using System;
using System.Collections.Generic;
using System.Text;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class APIController
    {
        // Oportunidade
        // https://www.smartenergy.com.br/API/Oportunidade?Key=1234567890123456&Oportunidade=1&Medicao=1&Inicio=20/06/2018&Fim=20/06/2018

        // Key = API key do usuário
        // Oportunidade = IDOportunidade
        // Medicao = IDMedicao
        // Inicio = data e hora inicial no formato 'DD/MM/YYYY'
        // Fim = data e hora final no formato 'DD/MM/YYYY'

        // Status
        // 0  - OK
        // 1  - Erro: Senha ou Datas nulas
        // 2  - Erro: Chave incorreta
        // 3  - Erro: Usuário inexistente
        // 4  - Erro: Medição inexistente
        // 5  - Erro: Usuário não pertence ao cliente da medição
        // 6  - Erro: Cliente inexistente
        // 7  - Erro: Usuário não tem acesso ao cliente
        // 8  - Erro: DataIni incorreta
        // 9  - Erro: DataFim incorreta
        // 10 - Erro: Sem registro
        // 11 - Erro: DataIni não é múltiplo de 15
        // 12 - Erro: DataFim não é múltiplo de 15
        // 14 - Erro: Intervalo maior que 365 dias
        // 20 - Erro: Ultrapassou limite diário de requisições
        // 21 - Erro: Usuário bloqueado
        // 22 - Erro: Exceção
        // 23 - Erro: Oportunidade inexistente

        public void Oportunidade(string Key, string Oportunidade, string Medicao, string Inicio, string Fim)
        {
            // Inicializa variáveis
            int IDCliente = 0;
            int IDMedicao = 0;
            int IDOportunidade = 0;
            DateTime DataIni = new DateTime(2000, 1, 1, 0, 0, 0);
            DateTime DataFim = new DateTime(2000, 1, 1, 0, 0, 0);

            try
            {
                // IDOportunidade
                int aux_IDOportunidade = 0;

                if (int.TryParse(Oportunidade, out aux_IDOportunidade))
                {
                    IDOportunidade = aux_IDOportunidade;
                }

                // IDMedicao
                int aux_IDMedicao = 0;

                if (int.TryParse(Medicao, out aux_IDMedicao))
                {
                    IDMedicao = aux_IDMedicao;
                }

                //
                // Parâmetros
                //
                if (Key == null || Inicio == null || Fim == null)
                {
                    // Log - [1] key ou Datas nulas
                    API_Log(Key, "Oportunidade", 0, IDMedicao, DataIni, DataFim, 1);

                    // erro
                    API_Erro(1, "Faltam Parâmetros");
                    return;
                }

                //
                // DATAS
                //
                if (!DateTime.TryParse(Inicio, out DataIni))
                {
                    // erro
                    DataIni = new DateTime(2000, 1, 1, 0, 0, 0);

                    // Log - [8] DataIni incorreta
                    API_Log(Key, "Oportunidade", 0, IDMedicao, DataIni, DataFim, 8);

                    // erro
                    API_Erro(8, string.Format("Data Inicial Incorreta ({0})", Inicio));
                    return;

                }

                if (!DateTime.TryParse(Fim, out DataFim))
                {
                    // erro
                    DataFim = new DateTime(2000, 1, 1, 0, 0, 0);

                    // Log - [9] DataFim incorreta
                    API_Log(Key, "Oportunidade", 0, IDMedicao, DataIni, DataFim, 9);

                    // erro
                    API_Erro(9, string.Format("Data Final Incorreta ({0})", Fim));
                    return;
                }


                //
                // Verifica se maior que 365 dias
                //
                TimeSpan diferenca = DataFim - DataIni;

                if (diferenca.TotalDays > 365)
                {
                    // Log - [14] Intervalo maior que 365 dias
                    API_Log(Key, "Oportunidade", 0, IDMedicao, DataIni, DataFim, 14);

                    // erro
                    API_Erro(14, string.Format("Intervalo maior que 365 dias: ({0:0.0} dias)", diferenca.TotalDays));
                    return;
                }


                //
                // USUARIO
                //

                // le usuario
                UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
                UsuarioDominio usuario = usuarioMetodos.ListarPorAPI_Key(Key);

                if (usuario != null)
                {
                    // verifica se key eh correta
                    if (usuario.API_Key != Key)
                    {
                        // Log - [2] chave incorreta
                        API_Log(Key, "Oportunidade", 0, 0, DataIni, DataFim, 2);

                        // erro
                        API_Erro(2, string.Format("Chave Incorreta ({0})", Key));
                        return;
                    }
                }
                else
                {
                    // Log - [3] usuário inexistente
                    API_Log(Key, "Oportunidade", 0, IDMedicao, DataIni, DataFim, 3);

                    // erro
                    API_Erro(3, string.Format("Usuário Incorreto ({0})", Key));
                    return;
                }

                // verifica se usuário bloqueado
                if (usuario.Bloqueio == TIPO_BLOQUEIO.TempoAPI || usuario.Bloqueio == TIPO_BLOQUEIO.Generico)
                {
                    // Log - [21] usuário bloqueado
                    API_Log(Key, "Oportunidade", 0, IDMedicao, DataIni, DataFim, 21);

                    // erro
                    API_Erro(21, string.Format("Usuário Bloqueado ({0})", usuario.IDUsuario));
                    return;
                }

                //
                // NÚMERO DE REQUISIÇÕES
                //
                API_LogMetodos logMetodos = new API_LogMetodos();
                int NumRequisicoes = logMetodos.NumRequisicoes("Oportunidade", usuario.IDUsuario, IDMedicao);

                // verifica se passou do limite diário
                if (NumRequisicoes >= usuario.API_Limite)
                {
                    // Log - [20] Ultrapassou limite diário de requisições
                    API_Log(Key, "Oportunidade", 0, IDMedicao, DataIni, DataFim, 20);

                    // erro
                    API_Erro(20, string.Format("Ultrapassou limite diário de requisições ({0})", NumRequisicoes));
                    return;
                }

                //
                // MEDICAO
                //

                if (IDMedicao == 0)
                {
                    // Log - [4] medição inexistente
                    API_Log(Key, "Oportunidade", usuario.IDUsuario, IDMedicao, DataIni, DataFim, 4);

                    // erro
                    API_Erro(4, string.Format("Medição Incorreta ({0})", IDMedicao));
                    return;
                }

                // le medicao
                MedicoesMetodos medMetodos = new MedicoesMetodos();
                MedicoesDominio med = medMetodos.ListarPorId(IDMedicao);

                if (med == null)
                {
                    // Log - [4] medição inexistente
                    API_Log(Key, "Oportunidade", usuario.IDUsuario, IDMedicao, DataIni, DataFim, 4);

                    // erro
                    API_Erro(4, string.Format("Medição Incorreta ({0})", IDMedicao));
                    return;
                }

                // cliente
                IDCliente = med.IDCliente;

                // verifica se usuario pertence a este cliente
                if (usuario.IDTipoAcesso == TIPO_ACESSO.CLIENTE_ADMIN || usuario.IDTipoAcesso == TIPO_ACESSO.CLIENTE_OPER || usuario.IDTipoAcesso == TIPO_ACESSO.DEMONSTRACAO)
                {
                    if (usuario.IDCliente != IDCliente)
                    {
                        // Log - [5] usuário não pertence ao cliente da medição
                        API_Log(Key, "Oportunidade", usuario.IDUsuario, IDMedicao, DataIni, DataFim, 5);

                        // erro
                        API_Erro(5, string.Format("Usuário ({0}) não pertence ao cliente ({1})", usuario.IDUsuario, usuario.IDCliente));
                        return;
                    }
                }
                else if (usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR || usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR_ADMIN || usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR_OPER)
                {
                    ClientesMetodos clienteMetodos = new ClientesMetodos();
                    List<ClientesDominio> clientes = clienteMetodos.ListarPorIDConsultor(usuario.IDCliente, 1);

                    if (clientes == null)
                    {
                        // Log - [6] cliente inexistente
                        API_Log(Key, "Oportunidade", usuario.IDUsuario, IDMedicao, DataIni, DataFim, 6);

                        // erro
                        API_Erro(6, string.Format("Cliente inexistente ({0})", usuario.IDCliente));
                        return;
                    }

                    int index = clientes.FindIndex(x => x.IDCliente == IDCliente);
                    if (index < 0)
                    {
                        // Log - [7] usuário não tem acesso ao cliente
                        API_Log(Key, "Oportunidade", usuario.IDUsuario, IDMedicao, DataIni, DataFim, 7);

                        // erro
                        API_Erro(7, string.Format("Usuário ({0}) não tem acesso ao cliente ({1})", usuario.IDUsuario, usuario.IDCliente));
                        return;
                    }
                }


                //
                // DADOS
                //

                bool retorno = false;

                switch (IDOportunidade)
                {
                    case 1:

                        // redução de contrato
                        retorno = Calcula_Oportunidade_ReducaoContrato(IDCliente, IDMedicao, DataIni, DataFim);
                        break;

                    default:

                        // Log - [23] oportunidade inexistente
                        API_Log(Key, "Oportunidade", usuario.IDUsuario, IDMedicao, DataIni, DataFim, 23);

                        // erro
                        API_Erro(23, string.Format("Oportunidade Incorreta ({0})", IDOportunidade));
                        return;
                }

                // Log do Historico - OK
                API_Log(Key, "Oportunidade", usuario.IDUsuario, IDMedicao, DataIni, DataFim, 0);
                return;
            }
            catch (Exception ex)
            {
                // Log - [22] exceção
                API_Log(Key, "Oportunidade", 0, IDMedicao, DataIni, DataFim, 22);

                // erro
                API_Erro(22, ex.Message);
                return;
            }
        }


        // Oportunidade Redução Contrato e Multas
        public class Oportunidade_ReducaoContrato
        {
            public int IDOportunidade { get; set; }
            public string NomeOportunidade { get; set; }
            public int IDCliente { get; set; }
            public string NomeCliente { get; set; }
            public int IDMedicao { get; set; }
            public string NomeMedicao { get; set; }
            public DateTime DataInicio { get; set; }
            public DateTime DataFim { get; set; }
            public Dados_ReducaoContrato Dados { get; set; }
        }

        public class Dados_ReducaoContrato
        {
            public string EstruturaTarifaria { get; set; }
            public string Distribuidora { get; set; }
            public double DemandaContratoP { get; set; }
            public double DemandaRegistradaP { get; set; }
            public double DiferencaP { get; set; }
            public double FatorUtilizacaoP { get; set; }
            public double DemandaContratoFP { get; set; }
            public double DemandaRegistradaFP { get; set; }
            public double DiferencaFP { get; set; }
            public double FatorUtilizacaoFP { get; set; }
            public double? OportunidadeP { get; set; }      // Campo opcional para IDOportunidade = 1
            public double? OportunidadeFP { get; set; }     // Campo opcional para IDOportunidade = 1
            public double? MultaP { get; set; }             // Campo opcional para IDOportunidade = 2
            public double? MultaFP { get; set; }            // Campo opcional para IDOportunidade = 2
        }

        public bool Calcula_Oportunidade_ReducaoContrato(int IDCliente, int IDMedicao, DateTime DataIni, DateTime DataFim)
        {

            //
            // Calcula oportunidade
            //
            Oportunidade_ReducaoContrato oportunidade = new Oportunidade_ReducaoContrato
            {
                IDOportunidade = 1,
                NomeOportunidade = "Redução de Contratos",
                IDCliente = 1,
                NomeCliente = "Smart Energy",
                IDMedicao = 1,
                NomeMedicao = "Medição São Paulo",
                DataInicio = new DateTime(2024, 12, 1),
                DataFim = new DateTime(2024, 12, 31),
                Dados = new Dados_ReducaoContrato
                {
                    EstruturaTarifaria = "Cativo THS Azul",
                    Distribuidora = "ENEL SP",
                    DemandaContratoP = 300.0,
                    DemandaRegistradaP = 267.0,
                    DiferencaP = -33.0,
                    FatorUtilizacaoP = 89.0,
                    DemandaContratoFP = 550.0,
                    DemandaRegistradaFP = 490.0,
                    DiferencaFP = -60.0,
                    FatorUtilizacaoFP = 89.1,
                    OportunidadeP = 951.72,
                    OportunidadeFP = 1023.0
                }
            };


            // JSON
            StringBuilder jsonBuilder = new StringBuilder();
            IFormatProvider iFormatProvider = new System.Globalization.CultureInfo("en-US");

            jsonBuilder.Append("{");
            jsonBuilder.AppendFormat("\"IDOportunidade\": {0},", oportunidade.IDOportunidade);
            jsonBuilder.AppendFormat("\"NomeOportunidade\": \"{0}\",", oportunidade.NomeOportunidade);
            jsonBuilder.AppendFormat("\"IDCliente\": {0},", oportunidade.IDCliente);
            jsonBuilder.AppendFormat("\"NomeCliente\": \"{0}\",", oportunidade.NomeCliente);
            jsonBuilder.AppendFormat("\"IDMedicao\": {0},", oportunidade.IDMedicao);
            jsonBuilder.AppendFormat("\"NomeMedicao\": \"{0}\",", oportunidade.NomeMedicao);
            jsonBuilder.AppendFormat("\"DataInicio\": \"{0:yyyy-MM-dd}\",", oportunidade.DataInicio);
            jsonBuilder.AppendFormat("\"DataFim\": \"{0:yyyy-MM-dd}\",", oportunidade.DataFim);

            jsonBuilder.Append("\"Dados\": {");
            jsonBuilder.AppendFormat("\"EstruturaTarifaria\": \"{0}\",", oportunidade.Dados.EstruturaTarifaria);
            jsonBuilder.AppendFormat("\"Distribuidora\": \"{0}\",", oportunidade.Dados.Distribuidora);
            jsonBuilder.AppendFormat("\"DemandaContratoP\": {0},", oportunidade.Dados.DemandaContratoP);
            jsonBuilder.AppendFormat("\"DemandaRegistradaP\": {0},", oportunidade.Dados.DemandaRegistradaP);
            jsonBuilder.AppendFormat("\"DiferencaP\": {0},", oportunidade.Dados.DiferencaP);
            jsonBuilder.AppendFormat("\"FatorUtilizacaoP\": {0},", oportunidade.Dados.FatorUtilizacaoP);
            jsonBuilder.AppendFormat("\"DemandaContratoFP\": {0},", oportunidade.Dados.DemandaContratoFP);
            jsonBuilder.AppendFormat("\"DemandaRegistradaFP\": {0},", oportunidade.Dados.DemandaRegistradaFP);
            jsonBuilder.AppendFormat("\"DiferencaFP\": {0},", oportunidade.Dados.DiferencaFP);
            jsonBuilder.AppendFormat("\"FatorUtilizacaoFP\": {0}", oportunidade.Dados.FatorUtilizacaoFP);

            // Campos opcionais
            if (oportunidade.Dados.OportunidadeP.HasValue)
                jsonBuilder.AppendFormat(",\"OportunidadeP\": {0}", oportunidade.Dados.OportunidadeP);
            if (oportunidade.Dados.OportunidadeFP.HasValue)
                jsonBuilder.AppendFormat(",\"OportunidadeFP\": {0}", oportunidade.Dados.OportunidadeFP);
            if (oportunidade.Dados.MultaP.HasValue)
                jsonBuilder.AppendFormat(",\"MultaP\": {0}", oportunidade.Dados.MultaP);
            if (oportunidade.Dados.MultaFP.HasValue)
                jsonBuilder.AppendFormat(",\"MultaFP\": {0}", oportunidade.Dados.MultaFP);

            jsonBuilder.Append("}"); // Fecha "Dados"
            jsonBuilder.Append("}"); // Fecha JSON principal


            // ContentType
            Response.ContentType = "application/json";

            Response.BinaryWrite(Encoding.UTF8.GetBytes(jsonBuilder.ToString()));
            Response.End();

            // ok
            return (true);
        }

    }
}