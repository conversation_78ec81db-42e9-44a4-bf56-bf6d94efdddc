﻿using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Text;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class APIController
    {
        // Fatura das Medições do usuário (somente ENERGIA)
        // http://www.smartenergy.com.br/API/MedicoesFatura?Key=1234567890123456&Inicio=20/06/2018 00:00&Fim=20/06/2018 23:45&TipoData=0&Formato=XML

        // Key = API key do usuário
        // Inicio = data e hora inicial no formato 'DD/MM/YYYY hh:mm'
        // Fim = data e hora final no formato 'DD/MM/YYYY hh:mm'
        // TipoData = tipo da utilização da data (0 - usa as datas de fechamento que pertencem a data Inicio || 1 - usa as datas exatas de Inicio e Fim)
        // Formato = tipo do arquivo de retorno: 'XML' ou 'JSON' (default XML)

        // Status
        // 0  - OK
        // 1  - Erro: Key ou Datas nulas
        // 2  - Erro: Chave incorreta
        // 3  - Erro: Usuário inexistente
        // 4  - Erro: Usuário não é Cliente ou Gestor
        // 5  - Erro: Medicoes inexistentes
        // 6  - Erro: Cliente inexistente
        // 7  - Erro: ---
        // 8  - Erro: DataIni incorreta
        // 9  - Erro: DataFim incorreta
        // 10 - Erro: Sem registro
        // 11 - Erro: ---
        // 12 - Erro: ---
        // 20 - Erro: Ultrapassou limite diário de requisições
        // 21 - Erro: Usuário bloqueado

        public class MedicoesFaturaAPI
        {
            public int IDCliente { get; set; }
            public string NomeCliente { get; set; }

            public int IDMedicao { get; set; }
            public string NomeMedicao { get; set; }
            public string Referencia { get; set; }

            public DateTime DataIni { get; set; }
            public DateTime DataFim { get; set; }

            public string Resultado { get; set; }

            public string ContratoMedicao { get; set; }
            public string NomeDistribuidora { get; set; }
            public double Constante { get; set; }

            public double contrato_dem_p { get; set; }
            public double contrato_dem_fp { get; set; }

            public double demanda_p { get; set; }
            public double demanda_fp { get; set; }

            public double ultr_demanda_p { get; set; }
            public double ultr_demanda_fp { get; set; }

            public double consumo_p { get; set; }
            public double consumo_fp { get; set; }
            public double consumo_total { get; set; }

            public double Total_Multa_Dem { get; set; }
            public double Total_Multa_FatPot { get; set; }

            public double total { get; set; }
        }

        // fatura de energia eletrica
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_Fatura", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_Fatura(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, int id_simulacao, char tipo_contrato, char IDEstTarifaria, char EhProjetada, ref DATAHORA pdatahora_ini, ref DATAHORA pdatahora_fim, ref RESULT_ENERGIA_FATURA pfatura, ref RESULT_ENERGIA_FATURA pfatura_sim);

        // atualiza fechamentos
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_AtualizaFechamentos", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_AtualizaFechamentos(char tipo_interface, ref CONFIG_INTERFACE cfg_interface);


        // Fatura das Medições do usuário
        public void MedicoesFatura(string Key, string Inicio, string Fim, int TipoData = 0, string Formato = "XML")
        {
            // Inicializa variáveis
            DateTime DataIni = new DateTime(2000, 1, 1, 0, 0, 0);
            DateTime DataFim = new DateTime(2000, 1, 1, 0, 0, 0);

            //
            // Parâmetros
            //
            if (Key == null || Inicio == null || Fim == null)
            {
                // Log - [1] key ou Datas nulas
                API_Log(Key, "MedicoesFatura", 0, 0, DataIni, DataFim, 1);

                // erro
                API_Erro(1, "Faltam Parâmetros", Formato);
                return;
            }

            //
            // DATAS
            //
            if (!DateTime.TryParse(Inicio, out DataIni))
            {
                // erro
                DataIni = new DateTime(2000, 1, 1, 0, 0, 0);

                // Log - [8] DataIni incorreta
                API_Log(Key, "MedicoesFatura", 0, 0, DataIni, DataFim, 8);

                // erro
                API_Erro(8, string.Format("Data Inicial Incorreta ({0})", Inicio), Formato);
                return;
            }

            if (!DateTime.TryParse(Fim, out DataFim))
            {
                // erro
                DataFim = new DateTime(2000, 1, 1, 0, 0, 0);

                // Log - [9] DataFim incorreta
                API_Log(Key, "MedicoesFatura", 0, 0, DataIni, DataFim, 9);

                // erro
                API_Erro(9, string.Format("Data Final Incorreta ({0})", Fim), Formato);
                return;
            }

            //
            // USUARIO
            //

            // le usuario
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = usuarioMetodos.ListarPorAPI_Key(Key);

            if (usuario != null)
            {
                // verifica se key eh correta
                if (usuario.API_Key != Key)
                {
                    // Log - [2] chave incorreta
                    API_Log(Key, "MedicoesFatura", 0, 0, DataIni, DataFim, 2);

                    // erro
                    API_Erro(2, string.Format("Chave Incorreta ({0})", Key), Formato);
                    return;
                }
            }
            else
            {
                // Log - [3] usuário inexistente
                API_Log(Key, "MedicoesFatura", 0, 0, DataIni, DataFim, 3);

                // erro
                API_Erro(3, string.Format("Usuário Incorreto ({0})", Key), Formato);
                return;
            }

            // verifica se usuário bloqueado
            if (usuario.Bloqueio == TIPO_BLOQUEIO.TempoAPI || usuario.Bloqueio == TIPO_BLOQUEIO.Generico)
            {
                // Log - [21] usuário bloqueado
                API_Log(Key, "MedicoesFatura", 0, 0, DataIni, DataFim, 21);

                // erro
                API_Erro(21, string.Format("Usuário Bloqueado ({0})", usuario.IDUsuario), Formato);
                return;
            }

            //
            // NÚMERO DE REQUISIÇÕES
            //
            API_LogMetodos logMetodos = new API_LogMetodos();
            int NumRequisicoes = logMetodos.NumRequisicoes("MedicoesFatura", usuario.IDUsuario);

            // verifica se passou do limite diário
            if (NumRequisicoes >= usuario.API_Limite)
            {
                // Log - [20] Ultrapassou limite diário de requisições
                API_Log(Key, "MedicoesFatura", 0, 0, DataIni, DataFim, 20);

                // erro
                API_Erro(20, string.Format("Ultrapassou limite diário de requisições ({0})", NumRequisicoes), Formato);
                return;
            }

            //
            // MEDICOES
            //

            // verifica se Cliente
            if (usuario.IDTipoAcesso == TIPO_ACESSO.CLIENTE_ADMIN || usuario.IDTipoAcesso == TIPO_ACESSO.CLIENTE_OPER || usuario.IDTipoAcesso == TIPO_ACESSO.DEMONSTRACAO)
            {
                // medições
                MedicoesFatura_Cliente(usuario, DataIni, DataFim, TipoData, Formato);

                // Log - OK
                API_Log(Key, "MedicoesFatura", usuario.IDUsuario, 0, DataIni, DataFim, 0);
                return;
            }

            // verifica se Gestor
            if (usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR || usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR_ADMIN || usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR_OPER)
            {
                // medições
                MedicoesFatura_Gestor(usuario, DataIni, DataFim, TipoData, Formato);

                // Log - OK
                API_Log(Key, "MedicoesFatura", usuario.IDUsuario, 0, DataIni, DataFim, 0);
                return;
            }

            // Log - [4] Usuário não é Cliente ou Gestor
            API_Log(Key, "MedicoesFatura", usuario.IDUsuario, 0, DataIni, DataFim, 4);

            // erro
            API_Erro(4, string.Format("Usuário não é Cliente ou Gestor ({0})", Key), Formato);
            return;
        }


        public void MedicoesFatura_Cliente(UsuarioDominio usuario, DateTime DataIni, DateTime DataFim, int TipoData, string Formato = "XML")
        {

            // IDCliente
            int IDCliente = usuario.IDCliente;

            // cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cliente = clienteMetodos.ListarPorId(IDCliente);

            if (cliente == null)
            {
                // Log - [6] Cliente inexistente
                API_Log(usuario.API_Key, "MedicoesFatura", usuario.IDUsuario, 0, DataIni, DataFim, 6);

                // erro
                API_Erro(6, "Sem Cliente", Formato);
                return;
            }

            // lista de medições
            List<MedicoesFaturaAPI> medicoesAPI = new List<MedicoesFaturaAPI>();

            // medicoes habilitadas
            List<int> ConfigMedList = new List<int>();

            // leio medicoes habilitadas deste usuario
            UsuarioMedicaoMetodos usuariomedicaoMetodos = new UsuarioMedicaoMetodos();
            List<UsuarioMedicaoDominio> usuariosmedicoes = usuariomedicaoMetodos.ListarTodasMedicoesIDUsuario(usuario.IDUsuario);

            // verifica se tem medicao
            if (usuariosmedicoes == null)
            {
                // Log - [5] Medicoes inexistentes
                API_Log(usuario.API_Key, "MedicoesFatura", usuario.IDUsuario, 0, DataIni, DataFim, 5);

                // erro
                API_Erro(5, "Sem Medições", Formato);
                return;
            }

            // copia medições habilitadas
            foreach (UsuarioMedicaoDominio usuarioMed in usuariosmedicoes)
            {
                ConfigMedList.Add(usuarioMed.IDMedicao);
            }

            // medições dos clientes do cliente
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicaoMetodos.ListarTodos(cliente.IDCliente, (ConfigMedList.Count == 0 ? null : ConfigMedList), " ORDER BY nome");

            if (medicoes == null)
            {
                // Log - [5] Medicoes inexistentes
                API_Log(usuario.API_Key, "MedicoesFatura", usuario.IDUsuario, 0, DataIni, DataFim, 5);

                // erro
                API_Erro(5, "Sem Medições", Formato);
                return;
            }

            // calculo das faturas
            MedicoesFatura_Calculo(cliente, medicoes, ref medicoesAPI, DataIni, DataFim, TipoData);

            // lista de medições
            MedicoesFatura_Lista(medicoesAPI, Formato);

            return;
        }

        public void MedicoesFatura_Gestor(UsuarioDominio usuario, DateTime DataIni, DateTime DataFim, int TipoData, string Formato = "XML")
        {

            // IDConsultor
            int IDConsultor = usuario.IDCliente;

            // clientes ativos do gestor
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> clientes = clienteMetodos.ListarPorIDConsultor(IDConsultor, 1);

            if (clientes == null)
            {
                // Log - [6] Clientes inexistentes
                API_Log(usuario.API_Key, "MedicoesFatura", usuario.IDUsuario, 0, DataIni, DataFim, 6);

                // erro
                API_Erro(6, "Sem Clientes", Formato);
                return;
            }

            // lista de medições
            List<MedicoesFaturaAPI> medicoesAPI = new List<MedicoesFaturaAPI>();

            // medicoes habilitadas
            List<int> ConfigMedList = new List<int>();

            // verifica se consultor operador
            if (usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR_OPER)
            {
                // leio medicoes habilitadas deste usuario
                UsuarioMedicaoMetodos usuariomedicaoMetodos = new UsuarioMedicaoMetodos();
                List<UsuarioMedicaoDominio> usuariosmedicoes = usuariomedicaoMetodos.ListarTodasMedicoesIDUsuario(usuario.IDUsuario);

                // verifica se tem medicao
                if (usuariosmedicoes == null)
                {
                    // Log - [5] Medicoes inexistentes
                    API_Log(usuario.API_Key, "MedicoesFatura", usuario.IDUsuario, 0, DataIni, DataFim, 5);

                    // erro
                    API_Erro(5, "Sem Medições", Formato);
                    return;
                }

                // copia medições habilitadas
                foreach (UsuarioMedicaoDominio usuarioMed in usuariosmedicoes)
                {
                    ConfigMedList.Add(usuarioMed.IDMedicao);
                }
            }

            // percorre clientes
            foreach (ClientesDominio cliente in clientes)
            {
                // medições dos clientes do gestor
                MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                List<MedicoesDominio> medicoes = medicaoMetodos.ListarTodos(cliente.IDCliente, (ConfigMedList.Count == 0 ? null : ConfigMedList), " ORDER BY nome");

                if (medicoes == null)
                {
                    // ignora
                    continue;
                }

                // calculo das faturas
                MedicoesFatura_Calculo(cliente, medicoes, ref medicoesAPI, DataIni, DataFim, TipoData);
            }

            // verifica se tem medições
            if (medicoesAPI.Count == 0)
            {
                // Log - [5] Medicoes inexistentes
                API_Log(usuario.API_Key, "MedicoesFatura", usuario.IDUsuario, 0, DataIni, DataFim, 5);

                // erro
                API_Erro(5, "Sem Medições", Formato);
                return;
            }
            else
            {
                // lista de medições
                MedicoesFatura_Lista(medicoesAPI, Formato);
            }

            return;
        }

        public void MedicoesFatura_Calculo(ClientesDominio cliente, List<MedicoesDominio> medicoes, ref List<MedicoesFaturaAPI> medicoesAPI, DateTime DataIni, DateTime DataFim, int TipoData)
        {
            // le tipos distribuidora
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposDistribuidora = listatiposMetodos.ListarTodos("AgentesDistribuidora");

            // percorre medicoes
            foreach (MedicoesDominio medicao in medicoes)
            {
                //
                // Verifica se EN
                //
                if (medicao.IDTipoMedicao != TIPO_MEDICAO.ENERGIA && medicao.IDTipoMedicao != TIPO_MEDICAO.ENERGIA_FORMULA)
                {
                    // não é EN, ignoro medição
                    continue;
                }

                //
                // Datas de início e fim da fatura
                //

                // inicialmente tem fechamento
                bool tem_fechamento = true;

                // verifica se deve usar fechamento
                if (TipoData == 0)
                {
                    // verifica se medicao real
                    if (medicao.IDTipoMedicao == TIPO_MEDICAO.ENERGIA)
                    {
                        // atualiza fechamentos
                        CONFIG_INTERFACE config_interface_fecha = new CONFIG_INTERFACE();

                        config_interface_fecha.sweb.id_cliente = medicao.IDCliente;
                        config_interface_fecha.sweb.id_medicao = medicao.IDMedicao;
                        config_interface_fecha.sweb.id_gateway = medicao.IDGateway;

                        SmCalcDB_AtualizaFechamentos((char)0, ref config_interface_fecha);
                    }

                    // encontra datas do fechamento do mês solicitado
                    FechamentosMetodos fechamentosMetodos = new FechamentosMetodos();
                    tem_fechamento = fechamentosMetodos.DatasFechamento(medicao.IDGateway, DataIni, ref DataIni, ref DataFim);
                }

                // converte datas
                DATAHORA dh_ini = new DATAHORA();
                DATAHORA dh_fim = new DATAHORA();
                Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, DataIni);
                Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, DataFim);


                //
                // Calcula fatura
                //

                // simulação
                int IDSimulacaoCenario = 0;

                // preenche solicitacao
                CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
                config_interface.sweb.id_cliente = cliente.IDCliente;
                config_interface.sweb.id_medicao = medicao.IDMedicao;
                config_interface.sweb.id_gateway = medicao.IDGateway;

                // estrutura
                RESULT_ENERGIA_FATURA fatura = new RESULT_ENERGIA_FATURA();
                RESULT_ENERGIA_FATURA fatura_sim = new RESULT_ENERGIA_FATURA();

                // demanda historica
                fatura.demanda_hist = 0.0;

                // calcula valores fatura (sem simulação)
                int retorno = SmCalcDB_Energia_Fatura((char)0, ref config_interface, IDSimulacaoCenario, (char)medicao.IDContratoMedicao, (char)medicao.IDEstruturaTarifaria, (char)0, ref dh_ini, ref dh_fim, ref fatura, ref fatura_sim);


                //
                // Preenche estrutura API
                //

                // medicao API
                MedicoesFaturaAPI medicaoAPI = new MedicoesFaturaAPI();

                medicaoAPI.IDCliente = cliente.IDCliente;
                medicaoAPI.NomeCliente = cliente.Nome;
                medicaoAPI.IDMedicao = medicao.IDMedicao;
                medicaoAPI.NomeMedicao = medicao.Nome;
                medicaoAPI.Referencia = medicao.Referencia;

                // resultado a princípio ok
                string DescricaoErro = "OK";

                // data início e fim
                medicaoAPI.DataIni = DataIni;
                medicaoAPI.DataFim = DataFim;

                // estrutura tarifária
                medicaoAPI.ContratoMedicao = "Cativo";

                switch (medicao.IDContratoMedicao)
                {
                    case 1:
                        medicaoAPI.ContratoMedicao = "Livre";
                        break;

                    case 2:
                        medicaoAPI.ContratoMedicao = "Rural / Irrigantes";
                        break;
                }

                if (medicao.IDEstruturaTarifaria == 0)
                {
                    medicaoAPI.ContratoMedicao = medicaoAPI.ContratoMedicao + " - THS Azul";
                }

                if (medicao.IDEstruturaTarifaria == 1)
                {
                    medicaoAPI.ContratoMedicao = medicaoAPI.ContratoMedicao + " - THS Verde";
                }

                // distribuidora
                ListaTiposDominio tipodistribuidora = listatiposDistribuidora.Find(item => item.ID == medicao.IDAgenteDistribuidora);
                if (tipodistribuidora != null)
                {
                    medicaoAPI.NomeDistribuidora = tipodistribuidora.Descricao;
                }
                else
                {
                    medicaoAPI.NomeDistribuidora = "---";
                }

                // constante
                EN_K_Metodos constanteMetodos = new EN_K_Metodos();
                EN_K_Dominio constante = constanteMetodos.UltimaConstante(medicao.IDCliente, medicao.IDMedicao);
                medicaoAPI.Constante = 0.0;

                if (constante != null)
                {
                    medicaoAPI.Constante = constante.Constante;
                }

                // verifica se houve erro no cálculo da fatura
                if (retorno != 0)
                {
                    switch (retorno)
                    {
                        case -1:    // estrutura tarifaria inexistente
                            DescricaoErro = "Estrutura Tarifária inexistente";
                            break;

                        case 1:     // nao foi possivel abrir banco de dados
                            DescricaoErro = string.Format("Banco de Dados EN_{0:000000} inexistente", medicao.IDMedicao);
                            break;

                        case 2:     // nao existem dados
                            DescricaoErro = string.Format("Não existem dados em EN_{0:000000}", medicao.IDMedicao);
                            break;

                        case 3:     // intervalo menos de 1 dia
                            DescricaoErro = "Intervalo menor que 1 dia";
                            break;

                        case 4:     // leitura da configuracao da medicao
                            DescricaoErro = "Leitura do Histórico do Contrato de Demanda";
                            break;

                        case 5:     // leitura do historico de contrato de demanda
                            DescricaoErro = "Leitura do Histórico do Contrato de Demanda";
                            break;

                        case 6:     // tarifas THS
                            DescricaoErro = "Leitura da Tarifa THS";
                            break;

                        case 7:     // tarifa pis/cofins
                            DescricaoErro = "Leitura da Tarifa PIS/COFINSD";
                            break;

                        case 8:     // nao existem bandeiras tarifarias
                            DescricaoErro = "Não existem Bandeiras Tarifárias";
                            break;

                        case 9:     // leitura da configuracao do feriado
                            DescricaoErro = "Leitura da Configuracao do Feriado";
                            break;
                    }

                    // erro
                    medicaoAPI.Resultado = DescricaoErro;

                    // zera resultado
                    medicaoAPI.contrato_dem_p = 0.0;
                    medicaoAPI.contrato_dem_fp = 0.0;
                    medicaoAPI.demanda_p = 0.0;
                    medicaoAPI.demanda_fp = 0.0;
                    medicaoAPI.ultr_demanda_p = 0.0;
                    medicaoAPI.ultr_demanda_fp = 0.0;
                    medicaoAPI.consumo_p = 0.0;
                    medicaoAPI.consumo_fp = 0.0;
                    medicaoAPI.consumo_total = 0.0;
                    medicaoAPI.Total_Multa_Dem = 0.0;
                    medicaoAPI.Total_Multa_FatPot = 0.0;
                    medicaoAPI.total = 0.0;
                }
                else
                {
                    // verifica se teve fechamento
                    if (!tem_fechamento)
                    {
                        DescricaoErro = "Não possui Fechamentos";
                    }

                    if (medicao.IDTipoMedicao == TIPO_MEDICAO.ENERGIA_FORMULA)
                    {
                        DescricaoErro = "Fórmula";
                    }
                    else
                    {
                        // analise da medição
                        EN_Metodos enMetodos = new EN_Metodos();
                        int analise = enMetodos.VerificarPeriodo(medicao.IDCliente, medicao.IDMedicao, DataIni, DataFim);

                        switch (analise)
                        {
                            case 1:     // 1 - ERRO, nao contem nenhum registro
                                DescricaoErro = "Não contém registros de demanda";
                                break;

                            case 2:     // 2 - ERRO, nao contem a quantidade de registros correta
                                DescricaoErro = "Quantidade de registros incorreta";
                                break;

                            case 3:     // 2 - ERRO, possui registros com zero
                                DescricaoErro = "Possui demanda com valor em zero";
                                break;
                        }
                    }

                    // contrato de demanda
                    medicaoAPI.contrato_dem_p = fatura.contrato_dem_p;
                    medicaoAPI.contrato_dem_fp = fatura.contrato_dem_fp;

                    // demanda registrada
                    medicaoAPI.demanda_p = fatura.demanda_p[CF.REG];

                    if (fatura.demanda_fpi[CF.REG] > fatura.demanda_fpc[CF.REG])
                    {
                        medicaoAPI.demanda_fp = fatura.demanda_fpi[CF.REG];
                    }
                    else
                    {
                        medicaoAPI.demanda_fp = fatura.demanda_fpc[CF.REG];
                    }

                    // ultrapassagem de demanda 
                    medicaoAPI.ultr_demanda_p = fatura.ultr_demanda_p[CF.FAT];
                    medicaoAPI.ultr_demanda_fp = fatura.ultr_demanda_fp[CF.FAT];

                    // consumo
                    medicaoAPI.consumo_p = fatura.consumo_p_tusd[CF.REG];
                    medicaoAPI.consumo_fp = fatura.consumo_fpi_tusd[CF.REG] + fatura.consumo_fpc_tusd[CF.REG];
                    medicaoAPI.consumo_total = fatura.consumo_total_tusd[CF.REG];

                    // multas
                    medicaoAPI.Total_Multa_Dem = fatura.ultr_demanda_p[CF.TOT] + fatura.ultr_demanda_fp[CF.TOT] +
                                            fatura.ultr_demanda_pe_p[CF.TOT] + fatura.ultr_demanda_pe_fp[CF.TOT];

                    medicaoAPI.Total_Multa_FatPot = fatura.fdr_p[CF.TOT] + fatura.fdr_fp[CF.TOT] +
                                            fatura.fdr_pe_p[CF.TOT] + fatura.fdr_pe_fp[CF.TOT] +
                                            fatura.fer_p[CF.TOT] + fatura.fer_fpi[CF.TOT] + fatura.fer_fpc[CF.TOT];

                    // total
                    medicaoAPI.total = fatura.total;
                }

                // resultado
                medicaoAPI.Resultado = DescricaoErro;


                //
                // Coloca na lista
                //
                medicoesAPI.Add(medicaoAPI);
            }

            return;
        }

        public void MedicoesFatura_Lista(List<MedicoesFaturaAPI> medicoesAPI, string Formato = "XML")
        {

            // XML ou JSON
            StringBuilder xmlBuilder = new StringBuilder();
            IFormatProvider iFormatProvider = new System.Globalization.CultureInfo("en-US");

            // verifica se JSON
            if (Formato.ToUpper() == "JSON")
            {
                // inicia
                xmlBuilder.Append("{");

                // cabecalho
                xmlBuilder.Append("\"Cabecalho\": ");
                xmlBuilder.Append("[\"IDCliente\",\"NomeCliente\",\"IDMedicao\",\"NomeMedicao\",\"N_Ativo\",\"Resultado\",\"Inicio\",\"Fim\",\"EstruturaTarifaria\",\"Distribuidora\",\"Constante\",\"DemandaContrato_P\",\"DemandaContrato_FP\",\"Demanda_P\",\"Demanda_FP\",\"DemandaUltr_P\",\"DemandaUltr_FP\",\"Consumo_P\",\"Consumo_FP\",\"Consumo\",\"MultaDemanda\",\"MultaFatPot\",\"Total\"],");

                // medições
                xmlBuilder.Append("\"Medicoes\": ");
                xmlBuilder.Append("[");

                // percorre medições
                int conta = 0;

                foreach (MedicoesFaturaAPI medicaoAPI in medicoesAPI)
                {
                    xmlBuilder.Append("[");
                    xmlBuilder.Append(string.Format("\"{0:000000}\",", medicaoAPI.IDCliente));
                    xmlBuilder.Append(string.Format("\"{0}\",", medicaoAPI.NomeCliente));
                    xmlBuilder.Append(string.Format("\"{0:000000}\",", medicaoAPI.IDMedicao));
                    xmlBuilder.Append(string.Format("\"{0}\",", medicaoAPI.NomeMedicao));
                    xmlBuilder.Append(string.Format("\"{0}\",", medicaoAPI.Referencia));
                    xmlBuilder.Append(string.Format("\"{0}\",", medicaoAPI.Resultado));
                    xmlBuilder.Append(string.Format("\"{0:g}\",", medicaoAPI.DataIni));
                    xmlBuilder.Append(string.Format("\"{0:g}\",", medicaoAPI.DataFim));
                    xmlBuilder.Append(string.Format("\"{0}\",", medicaoAPI.ContratoMedicao));
                    xmlBuilder.Append(string.Format("\"{0}\",", medicaoAPI.NomeDistribuidora));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.00000},", medicaoAPI.Constante));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.0},", medicaoAPI.contrato_dem_p));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.0},", medicaoAPI.contrato_dem_fp));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000},", medicaoAPI.demanda_p));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000},", medicaoAPI.demanda_fp));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000},", medicaoAPI.ultr_demanda_p));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000},", medicaoAPI.ultr_demanda_fp));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000},", medicaoAPI.consumo_p));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000},", medicaoAPI.consumo_fp));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000},", medicaoAPI.consumo_total));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.00},", medicaoAPI.Total_Multa_Dem));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.00},", medicaoAPI.Total_Multa_FatPot));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.00}", medicaoAPI.total));

                    // verifica se ultimo
                    if (conta < (medicoesAPI.Count - 1))
                    {
                        // coloca virgula
                        xmlBuilder.Append("],");
                    }
                    else
                    {
                        xmlBuilder.Append("]");
                    }

                    // proximo
                    conta++;
                }

                // finaliza
                xmlBuilder.Append("]");
                xmlBuilder.Append("}");

                // ContentType
                Response.ContentType = "application/json";
            }
            else
            {
                // monta XML
                xmlBuilder.Append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
                xmlBuilder.Append("<Medicoes>");

                // percorre medições
                foreach (MedicoesFaturaAPI medicaoAPI in medicoesAPI)
                {
                    xmlBuilder.Append(string.Format("<Medicao IDMedicao=\"{0:000000}\">", medicaoAPI.IDMedicao));

                    xmlBuilder.Append("<IDCliente>");
                    xmlBuilder.Append(string.Format("{0:000000}", medicaoAPI.IDCliente));
                    xmlBuilder.Append("</IDCliente>");

                    xmlBuilder.Append("<NomeCliente>");
                    xmlBuilder.Append(medicaoAPI.NomeCliente);
                    xmlBuilder.Append("</NomeCliente>");

                    xmlBuilder.Append("<IDMedicao>");
                    xmlBuilder.Append(string.Format("{0:000000}", medicaoAPI.IDMedicao));
                    xmlBuilder.Append("</IDMedicao>");

                    xmlBuilder.Append("<NomeMedicao>");
                    xmlBuilder.Append(medicaoAPI.NomeMedicao);
                    xmlBuilder.Append("</NomeMedicao>");

                    xmlBuilder.Append("<N_Ativo>");
                    xmlBuilder.Append(medicaoAPI.Referencia);
                    xmlBuilder.Append("</N_Ativo>");

                    xmlBuilder.Append("<Resultado>");
                    xmlBuilder.Append(medicaoAPI.Resultado);
                    xmlBuilder.Append("</Resultado>");

                    xmlBuilder.Append("<Inicio>");
                    xmlBuilder.Append(string.Format("{0:g}", medicaoAPI.DataIni));
                    xmlBuilder.Append("</Inicio>");

                    xmlBuilder.Append("<Fim>");
                    xmlBuilder.Append(string.Format("{0:g}", medicaoAPI.DataFim));
                    xmlBuilder.Append("</Fim>");

                    xmlBuilder.Append("<EstruturaTarifaria>");
                    xmlBuilder.Append(medicaoAPI.ContratoMedicao);
                    xmlBuilder.Append("</EstruturaTarifaria>");

                    xmlBuilder.Append("<Distribuidora>");
                    xmlBuilder.Append(medicaoAPI.NomeDistribuidora);
                    xmlBuilder.Append("</Distribuidora>");

                    xmlBuilder.Append("<Constante>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.00000}", medicaoAPI.Constante));
                    xmlBuilder.Append("</Constante>");

                    xmlBuilder.Append("<DemandaContrato_P>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.0}", medicaoAPI.contrato_dem_p));
                    xmlBuilder.Append("</DemandaContrato_P>");

                    xmlBuilder.Append("<DemandaContrato_FP>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.0}", medicaoAPI.contrato_dem_fp));
                    xmlBuilder.Append("</DemandaContrato_FP>");

                    xmlBuilder.Append("<Demanda_P>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000}", medicaoAPI.demanda_p));
                    xmlBuilder.Append("</Demanda_P>");

                    xmlBuilder.Append("<Demanda_FP>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000}", medicaoAPI.demanda_fp));
                    xmlBuilder.Append("</Demanda_FP>");

                    xmlBuilder.Append("<DemandaUltr_P>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000}", medicaoAPI.ultr_demanda_p));
                    xmlBuilder.Append("</DemandaUltr_P>");

                    xmlBuilder.Append("<DemandaUltr_FP>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000}", medicaoAPI.ultr_demanda_fp));
                    xmlBuilder.Append("</DemandaUltr_FP>");

                    xmlBuilder.Append("<Consumo_P>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000}", medicaoAPI.consumo_p));
                    xmlBuilder.Append("</Consumo_P>");

                    xmlBuilder.Append("<Consumo_FP>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000}", medicaoAPI.consumo_fp));
                    xmlBuilder.Append("</Consumo_FP>");

                    xmlBuilder.Append("<Consumo>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000}", medicaoAPI.consumo_total));
                    xmlBuilder.Append("</Consumo>");

                    xmlBuilder.Append("<MultaDemanda>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.00}", medicaoAPI.Total_Multa_Dem));
                    xmlBuilder.Append("</MultaDemanda>");

                    xmlBuilder.Append("<MultaFatPot>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.00}", medicaoAPI.Total_Multa_FatPot));
                    xmlBuilder.Append("</MultaFatPot>");

                    xmlBuilder.Append("<Total>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.00}", medicaoAPI.total));
                    xmlBuilder.Append("</Total>");

                    xmlBuilder.Append("</Medicao>");
                }

                xmlBuilder.Append("</Medicoes>");

                // ContentType
                Response.ContentType = "text/xml";
            }

            Response.BinaryWrite(Encoding.UTF8.GetBytes(xmlBuilder.ToString()));
            Response.End();

            return;
        }
    }
}
