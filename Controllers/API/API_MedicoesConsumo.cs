﻿using System;
using System.Collections.Generic;
using System.Text;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class APIController
    {
        // Consumo totalizado das Medições do usuário (somente ENERGIA)
        // http://www.smartenergy.com.br/API/MedicoesConsumo?Key=1234567890123456&Inicio=20/06/2018 00:00&Fim=20/06/2018 23:45&Formato=XML

        // Key = API key do usuário
        // Inicio = data e hora inicial no formato 'DD/MM/YYYY hh:mm'
        // Fim = data e hora final no formato 'DD/MM/YYYY hh:mm'
        // Formato = tipo do arquivo de retorno: 'XML' ou 'JSON' (default XML)

        // Status
        // 0  - OK
        // 1  - Erro: Key ou Datas nulas
        // 2  - Erro: Chave incorreta
        // 3  - Erro: Usuário inexistente
        // 4  - Erro: Usuário não é Cliente ou Gestor
        // 5  - Erro: Medicoes inexistentes
        // 6  - Erro: Cliente inexistente
        // 7  - Erro: ---
        // 8  - Erro: DataIni incorreta
        // 9  - Erro: DataFim incorreta
        // 10 - Erro: Sem registro
        // 11 - Erro: ---
        // 12 - Erro: ---
        // 20 - Erro: Ultrapassou limite diário de requisições
        // 21 - Erro: Usuário bloqueado

        public class MedicoesConsumoAPI
        {
            public int IDCliente { get; set; }
            public string NomeCliente { get; set; }

            public int IDMedicao { get; set; }
            public string NomeMedicao { get; set; }
            public string Referencia { get; set; }

            public double ConsumoTotal { get; set; }
        }


        // Consumo totalizado das Medições do usuário
        public void MedicoesConsumo(string Key, string Inicio, string Fim, string Formato = "XML")
        {
            // Inicializa variáveis
            DateTime DataIni = new DateTime(2000, 1, 1, 0, 0, 0);
            DateTime DataFim = new DateTime(2000, 1, 1, 0, 0, 0);

            //
            // Parâmetros
            //
            if (Key == null || Inicio == null || Fim == null)
            {
                // Log - [1] key ou Datas nulas
                API_Log(Key, "MedicoesConsumo", 0, 0, DataIni, DataFim, 1);

                // erro
                API_Erro(1, "Faltam Parâmetros", Formato);
                return;
            }

            //
            // DATAS
            //
            if (!DateTime.TryParse(Inicio, out DataIni))
            {
                // erro
                DataIni = new DateTime(2000, 1, 1, 0, 0, 0);

                // Log - [8] DataIni incorreta
                API_Log(Key, "MedicoesConsumo", 0, 0, DataIni, DataFim, 8);

                // erro
                API_Erro(8, string.Format("Data Inicial Incorreta ({0})", Inicio), Formato);
                return;
            }

            if (!DateTime.TryParse(Fim, out DataFim))
            {
                // erro
                DataFim = new DateTime(2000, 1, 1, 0, 0, 0);

                // Log - [9] DataFim incorreta
                API_Log(Key, "MedicoesConsumo", 0, 0, DataIni, DataFim, 9);

                // erro
                API_Erro(9, string.Format("Data Final Incorreta ({0})", Fim), Formato);
                return;
            }

            //
            // USUARIO
            //

            // le usuario
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = usuarioMetodos.ListarPorAPI_Key(Key);

            if (usuario != null)
            {
                // verifica se key eh correta
                if (usuario.API_Key != Key)
                {
                    // Log - [2] chave incorreta
                    API_Log(Key, "MedicoesConsumo", 0, 0, DataIni, DataFim, 2);

                    // erro
                    API_Erro(2, string.Format("Chave Incorreta ({0})", Key), Formato);
                    return;
                }
            }
            else
            {
                // Log - [3] usuário inexistente
                API_Log(Key, "MedicoesConsumo", 0, 0, DataIni, DataFim, 3);

                // erro
                API_Erro(3, string.Format("Usuário Incorreto ({0})", Key), Formato);
                return;
            }

            // verifica se usuário bloqueado
            if (usuario.Bloqueio == TIPO_BLOQUEIO.TempoAPI || usuario.Bloqueio == TIPO_BLOQUEIO.Generico)
            {
                // Log - [21] usuário bloqueado
                API_Log(Key, "MedicoesConsumo", 0, 0, DataIni, DataFim, 21);

                // erro
                API_Erro(21, string.Format("Usuário Bloqueado ({0})", usuario.IDUsuario), Formato);
                return;
            }

            //
            // NÚMERO DE REQUISIÇÕES
            //
            API_LogMetodos logMetodos = new API_LogMetodos();
            int NumRequisicoes = logMetodos.NumRequisicoes("MedicoesConsumo", usuario.IDUsuario);

            // verifica se passou do limite diário
            if (NumRequisicoes >= usuario.API_Limite)
            {
                // Log - [20] Ultrapassou limite diário de requisições
                API_Log(Key, "MedicoesConsumo", 0, 0, DataIni, DataFim, 20);

                // erro
                API_Erro(20, string.Format("Ultrapassou limite diário de requisições ({0})", NumRequisicoes), Formato);
                return;
            }

            //
            // MEDICOES
            //

            // verifica se Cliente
            if (usuario.IDTipoAcesso == TIPO_ACESSO.CLIENTE_ADMIN || usuario.IDTipoAcesso == TIPO_ACESSO.CLIENTE_OPER || usuario.IDTipoAcesso == TIPO_ACESSO.DEMONSTRACAO)
            {
                // medições
                MedicoesConsumo_Cliente(usuario, DataIni, DataFim, Formato);

                // Log - OK
                API_Log(Key, "MedicoesConsumo", usuario.IDUsuario, 0, DataIni, DataFim, 0);
                return;
            }

            // verifica se Gestor
            if (usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR || usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR_ADMIN || usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR_OPER)
            {
                // medições
                MedicoesConsumo_Gestor(usuario, DataIni, DataFim, Formato);

                // Log - OK
                API_Log(Key, "MedicoesConsumo", usuario.IDUsuario, 0, DataIni, DataFim, 0);
                return;
            }

            // verifica se Admin
            if (usuario.IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || usuario.IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE || usuario.IDTipoAcesso == TIPO_ACESSO.GESTAL_VENDAS)
            {
                // medições
                MedicoesConsumo_Gestor(usuario, DataIni, DataFim, Formato);

                // Log - OK
                API_Log(Key, "MedicoesConsumo", usuario.IDUsuario, 0, DataIni, DataFim, 0);
                return;
            }

            // Log - [4] Usuário não é Cliente ou Gestor
            API_Log(Key, "MedicoesConsumo", usuario.IDUsuario, 0, DataIni, DataFim, 4);

            // erro
            API_Erro(4, string.Format("Usuário não é Cliente ou Gestor ({0})", Key), Formato);
            return;
        }


        public void MedicoesConsumo_Cliente(UsuarioDominio usuario, DateTime DataIni, DateTime DataFim, string Formato = "XML")
        {

            // IDCliente
            int IDCliente = usuario.IDCliente;

            // cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cliente = clienteMetodos.ListarPorId(IDCliente);

            if (cliente == null)
            {
                // Log - [6] Cliente inexistente
                API_Log(usuario.API_Key, "MedicoesConsumo", usuario.IDUsuario, 0, DataIni, DataFim, 6);

                // erro
                API_Erro(6, "Sem Cliente", Formato);
                return;
            }

            // lista de medições
            List<MedicoesConsumoAPI> medicoesAPI = new List<MedicoesConsumoAPI>();

            // medicoes habilitadas
            List<int> ConfigMedList = new List<int>();

            // leio medicoes habilitadas deste usuario
            UsuarioMedicaoMetodos usuariomedicaoMetodos = new UsuarioMedicaoMetodos();
            List<UsuarioMedicaoDominio> usuariosmedicoes = usuariomedicaoMetodos.ListarTodasMedicoesIDUsuario(usuario.IDUsuario);

            // verifica se tem medicao
            if (usuariosmedicoes == null)
            {
                // Log - [5] Medicoes inexistentes
                API_Log(usuario.API_Key, "MedicoesConsumo", usuario.IDUsuario, 0, DataIni, DataFim, 5);

                // erro
                API_Erro(5, "Sem Medições", Formato);
                return;
            }

            // copia medições habilitadas
            foreach (UsuarioMedicaoDominio usuarioMed in usuariosmedicoes)
            {
                ConfigMedList.Add(usuarioMed.IDMedicao);
            }

            // medições dos clientes do cliente
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicaoMetodos.ListarTodos(cliente.IDCliente, (ConfigMedList.Count == 0 ? null : ConfigMedList), " ORDER BY nome");

            if (medicoes == null)
            {
                // Log - [5] Medicoes inexistentes
                API_Log(usuario.API_Key, "MedicoesConsumo", usuario.IDUsuario, 0, DataIni, DataFim, 5);

                // erro
                API_Erro(5, "Sem Medições", Formato);
                return;
            }

            // percorre medicoes
            foreach (MedicoesDominio medicao in medicoes)
            {
                // verifica se EN
                if (medicao.IDTipoMedicao != TIPO_MEDICAO.ENERGIA && medicao.IDTipoMedicao != TIPO_MEDICAO.ENERGIA_FORMULA)
                {
                    // não é EN, ignoro medição
                    continue;
                }

                // medicao API
                MedicoesConsumoAPI medicaoAPI = new MedicoesConsumoAPI();

                medicaoAPI.IDCliente = cliente.IDCliente;
                medicaoAPI.NomeCliente = cliente.Nome;
                medicaoAPI.IDMedicao = medicao.IDMedicao;
                medicaoAPI.NomeMedicao = medicao.Nome;
                medicaoAPI.Referencia = medicao.Referencia;

                // calcula consumo
                EN_Metodos enMetodos = new EN_Metodos();
                medicaoAPI.ConsumoTotal = enMetodos.ConsumoTotalPeriodo(cliente.IDCliente, medicao.IDMedicao, DataIni, DataFim);

                // coloca na lista
                medicoesAPI.Add(medicaoAPI);
            }

            // lista de medições
            MedicoesConsumo_Lista(medicoesAPI, Formato);

            return;
        }

        public void MedicoesConsumo_Gestor(UsuarioDominio usuario, DateTime DataIni, DateTime DataFim, string Formato = "XML")
        {

            // IDConsultor
            int IDConsultor = usuario.IDCliente;

            // clientes ativos do gestor
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> clientes = clienteMetodos.ListarPorIDConsultor(IDConsultor, 1);

            if (clientes == null)
            {
                // Log - [6] Clientes inexistentes
                API_Log(usuario.API_Key, "MedicoesConsumo", usuario.IDUsuario, 0, DataIni, DataFim, 6);

                // erro
                API_Erro(6, "Sem Clientes", Formato);
                return;
            }

            // lista de medições
            List<MedicoesConsumoAPI> medicoesAPI = new List<MedicoesConsumoAPI>();

            // medicoes habilitadas
            List<int> ConfigMedList = new List<int>();

            // verifica se consultor operador
            if (usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR_OPER)
            {
                // leio medicoes habilitadas deste usuario
                UsuarioMedicaoMetodos usuariomedicaoMetodos = new UsuarioMedicaoMetodos();
                List<UsuarioMedicaoDominio> usuariosmedicoes = usuariomedicaoMetodos.ListarTodasMedicoesIDUsuario(usuario.IDUsuario);

                // verifica se tem medicao
                if (usuariosmedicoes == null)
                {
                    // Log - [5] Medicoes inexistentes
                    API_Log(usuario.API_Key, "MedicoesConsumo", usuario.IDUsuario, 0, DataIni, DataFim, 5);

                    // erro
                    API_Erro(5, "Sem Medições", Formato);
                    return;
                }

                // copia medições habilitadas
                foreach (UsuarioMedicaoDominio usuarioMed in usuariosmedicoes)
                {
                    ConfigMedList.Add(usuarioMed.IDMedicao);
                }
            }

            // percorre clientes
            foreach (ClientesDominio cliente in clientes)
            {
                // medições dos clientes do gestor
                MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                List<MedicoesDominio> medicoes = medicaoMetodos.ListarTodos(cliente.IDCliente, (ConfigMedList.Count == 0 ? null : ConfigMedList), " ORDER BY nome");

                if (medicoes == null)
                {
                    // ignora
                    continue;
                }

                // percorre medicoes
                foreach (MedicoesDominio medicao in medicoes)
                {
                    // verifica se EN
                    if (medicao.IDTipoMedicao != TIPO_MEDICAO.ENERGIA && medicao.IDTipoMedicao != TIPO_MEDICAO.ENERGIA_FORMULA)
                    {
                        // não é EN, ignoro medição
                        continue;
                    }

                    // medicao API
                    MedicoesConsumoAPI medicaoAPI = new MedicoesConsumoAPI();

                    medicaoAPI.IDCliente = cliente.IDCliente;
                    medicaoAPI.NomeCliente = cliente.Nome;
                    medicaoAPI.IDMedicao = medicao.IDMedicao;
                    medicaoAPI.NomeMedicao = medicao.Nome;
                    medicaoAPI.Referencia = medicao.Referencia;

                    // calcula consumo
                    EN_Metodos enMetodos = new EN_Metodos();
                    medicaoAPI.ConsumoTotal = enMetodos.ConsumoTotalPeriodo(cliente.IDCliente, medicao.IDMedicao, DataIni, DataFim);

                    // coloca na lista
                    medicoesAPI.Add(medicaoAPI);
                }
            }

            // verifica se tem medições
            if (medicoesAPI.Count == 0)
            {
                // Log - [5] Medicoes inexistentes
                API_Log(usuario.API_Key, "MedicoesConsumo", usuario.IDUsuario, 0, DataIni, DataFim, 5);

                // erro
                API_Erro(5, "Sem Medições", Formato);
                return;
            }
            else
            {
                // lista de medições
                MedicoesConsumo_Lista(medicoesAPI, Formato);
            }

            return;
        }

        public void MedicoesConsumo_Lista(List<MedicoesConsumoAPI> medicoesAPI, string Formato = "XML")
        {

            // XML ou JSON
            StringBuilder xmlBuilder = new StringBuilder();
            IFormatProvider iFormatProvider = new System.Globalization.CultureInfo("en-US");

            // verifica se JSON
            if (Formato.ToUpper() == "JSON")
            {
                // inicia
                xmlBuilder.Append("{");

                // cabecalho
                xmlBuilder.Append("\"Cabecalho\": ");
                xmlBuilder.Append("[\"IDCliente\",\"NomeCliente\",\"IDMedicao\",\"NomeMedicao\",\"N_Ativo\",\"Consumo\"],");

                // medições
                xmlBuilder.Append("\"Medicoes\": ");
                xmlBuilder.Append("[");

                // percorre medições
                int conta = 0;

                foreach (MedicoesConsumoAPI medicaoAPI in medicoesAPI)
                {
                    xmlBuilder.Append("[");
                    xmlBuilder.Append(string.Format("\"{0:000000}\",", medicaoAPI.IDCliente));
                    xmlBuilder.Append(string.Format("\"{0}\",", medicaoAPI.NomeCliente));
                    xmlBuilder.Append(string.Format("\"{0:000000}\",", medicaoAPI.IDMedicao));
                    xmlBuilder.Append(string.Format("\"{0}\",", medicaoAPI.NomeMedicao));
                    xmlBuilder.Append(string.Format("\"{0}\",", medicaoAPI.Referencia));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000}", medicaoAPI.ConsumoTotal));

                    // verifica se ultimo
                    if (conta < (medicoesAPI.Count - 1))
                    {
                        // coloca virgula
                        xmlBuilder.Append("],");
                    }
                    else
                    {
                        xmlBuilder.Append("]");
                    }

                    // proximo
                    conta++;
                }

                // finaliza
                xmlBuilder.Append("]");
                xmlBuilder.Append("}");

                // ContentType
                Response.ContentType = "application/json";
            }
            else
            {
                // monta XML
                xmlBuilder.Append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
                xmlBuilder.Append("<Medicoes>");

                // percorre medições
                foreach (MedicoesConsumoAPI medicaoAPI in medicoesAPI)
                {
                    xmlBuilder.Append(string.Format("<Medicao IDMedicao=\"{0:000000}\">", medicaoAPI.IDMedicao));

                    xmlBuilder.Append("<IDCliente>");
                    xmlBuilder.Append(string.Format("{0:000000}", medicaoAPI.IDCliente));
                    xmlBuilder.Append("</IDCliente>");

                    xmlBuilder.Append("<NomeCliente>");
                    xmlBuilder.Append(medicaoAPI.NomeCliente);
                    xmlBuilder.Append("</NomeCliente>");

                    xmlBuilder.Append("<IDMedicao>");
                    xmlBuilder.Append(string.Format("{0:000000}", medicaoAPI.IDMedicao));
                    xmlBuilder.Append("</IDMedicao>");

                    xmlBuilder.Append("<NomeMedicao>");
                    xmlBuilder.Append(medicaoAPI.NomeMedicao);
                    xmlBuilder.Append("</NomeMedicao>");

                    xmlBuilder.Append("<N_Ativo>");
                    xmlBuilder.Append(medicaoAPI.Referencia);
                    xmlBuilder.Append("</N_Ativo>");

                    xmlBuilder.Append("<Consumo>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000}", medicaoAPI.ConsumoTotal));
                    xmlBuilder.Append("</Consumo>");

                    xmlBuilder.Append("</Medicao>");
                }

                xmlBuilder.Append("</Medicoes>");

                // ContentType
                Response.ContentType = "text/xml";
            }

            Response.BinaryWrite(Encoding.UTF8.GetBytes(xmlBuilder.ToString()));
            Response.End();

            return;
        }
    }
}
