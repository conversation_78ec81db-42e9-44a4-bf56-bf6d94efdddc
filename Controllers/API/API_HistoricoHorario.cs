﻿using System;
using System.Collections.Generic;
using System.Text;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class APIController
    {
        // Histórico da medição horário (somente ENERGIA)
        // https://www.smartenergy.com.br/API/HistoricoHorario?Key=1234567890123456&Medicao=1&Inicio=20/06/2018 00:00&Fim=20/06/2018 23:45&Formato=XML

        // Key = API key do usuário
        // Medicao = IDMedicao
        // Inicio = data e hora inicial no formato 'DD/MM/YYYY hh:mm'
        // Fim = data e hora final no formato 'DD/MM/YYYY hh:mm'
        // Formato = tipo do arquivo de retorno: 'XML' ou 'JSON' (default XML)

        // Status
        // 0  - OK
        // 1  - Erro: Senha ou Datas nulas
        // 2  - Erro: Chave incorreta
        // 3  - Erro: Usuário inexistente
        // 4  - Erro: Medição inexistente
        // 5  - Erro: Usuário não pertence ao cliente da medição
        // 6  - Erro: Cliente inexistente
        // 7  - Erro: Usuário não tem acesso ao cliente
        // 8  - Erro: DataIni incorreta
        // 9  - Erro: DataFim incorreta
        // 10 - Erro: Sem registro
        // 11 - Erro: DataIni não é múltiplo de 15
        // 12 - Erro: DataFim não é múltiplo de 15
        // 13 - Erro: Medição não é de Energia
        // 14 - Erro: Intervalo maior que 365 dias
        // 20 - Erro: Ultrapassou limite diário de requisições
        // 21 - Erro: Usuário bloqueado

        public class HistoricoHorarioAPI
        {
            public DateTime DataHora { get; set; }

            public double ConsumoAtivo { get; set; }
            public double ConsumoReativo { get; set; }
            public double FatorPotencia { get; set; }

            public int Periodo { get; set; }
        }


        public void HistoricoHorario(string Key, int Medicao, string Inicio, string Fim, string Formato = "XML")
        {
            // Inicializa variáveis
            int IDCliente = 0;
            int IDMedicao = Medicao;
            DateTime DataIni = new DateTime(2000, 1, 1, 0, 0, 0);
            DateTime DataFim = new DateTime(2000, 1, 1, 0, 0, 0);

            //
            // Parâmetros
            //
            if (Key == null || Inicio == null || Fim == null)
            {
                // Log - [1] key ou Datas nulas
                API_Log(Key, "HistoricoHorario", 0, IDMedicao, DataIni, DataFim, 1);

                // erro
                API_Erro(1, "Faltam Parâmetros", Formato);
                return;
            }

            //
            // DATAS
            //
            if (!DateTime.TryParse(Inicio, out DataIni))
            {
                // erro
                DataIni = new DateTime(2000, 1, 1, 0, 0, 0);

                // Log - [8] DataIni incorreta
                API_Log(Key, "HistoricoHorario", 0, IDMedicao, DataIni, DataFim, 8);

                // erro
                API_Erro(8, string.Format("Data Inicial Incorreta ({0})", Inicio), Formato);
                return;

            }

            if (!DateTime.TryParse(Fim, out DataFim))
            {
                // erro
                DataFim = new DateTime(2000, 1, 1, 0, 0, 0);

                // Log - [9] DataFim incorreta
                API_Log(Key, "HistoricoHorario", 0, IDMedicao, DataIni, DataFim, 9);

                // erro
                API_Erro(9, string.Format("Data Final Incorreta ({0})", Fim), Formato);
                return;
            }

            //
            // Verifica se horários são multiplos de 15 (+/-1) minutos
            //
            // Não aceito solicitação que não seja multipo de 15 (+/-1) minutos exatos.
            // Faço isso pois clientes criam robôs para coleta de dados de minuto a minuto.
            if (!((DataIni.Minute == 59 || DataIni.Minute == 0 || DataIni.Minute == 1 ||
                   DataIni.Minute == 14 || DataIni.Minute == 15 || DataIni.Minute == 16 ||
                   DataIni.Minute == 29 || DataIni.Minute == 30 || DataIni.Minute == 31 ||
                   DataIni.Minute == 44 || DataIni.Minute == 45 || DataIni.Minute == 46)))
            {
                // Log - [11] DataIni não é múltiplo de 15
                API_Log(Key, "HistoricoHorario", 0, IDMedicao, DataIni, DataFim, 11);

                // erro
                API_Erro(11, string.Format("Horário Inicial deve ser múltiplo exato de 15 minutos (00, 15, 30 ou 45): ({0:T})", DataIni), Formato);
                return;
            }

            if (!((DataFim.Minute == 59 || DataFim.Minute == 0 || DataFim.Minute == 1 ||
                   DataFim.Minute == 14 || DataFim.Minute == 15 || DataFim.Minute == 16 ||
                   DataFim.Minute == 29 || DataFim.Minute == 30 || DataFim.Minute == 31 ||
                   DataFim.Minute == 44 || DataFim.Minute == 45 || DataFim.Minute == 46)))
            {
                // Log - [12] DataFimi não é múltiplo de 15
                API_Log(Key, "HistoricoHorario", 0, IDMedicao, DataIni, DataFim, 12);

                // erro
                API_Erro(12, string.Format("Horário Final deve ser múltiplo exato de 15 minutos (00, 15, 30 ou 45): ({0:T})", DataFim), Formato);
                return;
            }

            //
            // Verifica se maior que 365 dias
            //
            TimeSpan diferenca = DataFim - DataIni;

            if (diferenca.TotalDays > 365)
            {
                // Log - [14] Intervalo maior que 365 dias
                API_Log(Key, "Historico", 0, IDMedicao, DataIni, DataFim, 14);

                // erro
                API_Erro(14, string.Format("Intervalo maior que 365 dias: ({0.0} dias)", diferenca.TotalDays), Formato);
                return;
            }

            //
            // USUARIO
            //

            // le usuario
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = usuarioMetodos.ListarPorAPI_Key(Key);

            if (usuario != null)
            {
                // verifica se key eh correta
                if (usuario.API_Key != Key)
                {
                    // Log - [2] chave incorreta
                    API_Log(Key, "HistoricoHorario", 0, 0, DataIni, DataFim, 2);

                    // erro
                    API_Erro(2, string.Format("Chave Incorreta ({0})", Key), Formato);
                    return;
                }
            }
            else
            {
                // Log - [3] usuário inexistente
                API_Log(Key, "HistoricoHorario", 0, IDMedicao, DataIni, DataFim, 3);

                // erro
                API_Erro(3, string.Format("Usuário Incorreto ({0})", Key), Formato);
                return;
            }

            // verifica se usuário bloqueado
            if (usuario.Bloqueio == TIPO_BLOQUEIO.TempoAPI || usuario.Bloqueio == TIPO_BLOQUEIO.Generico)
            {
                // Log - [21] usuário bloqueado
                API_Log(Key, "HistoricoHorario", 0, IDMedicao, DataIni, DataFim, 21);

                // erro
                API_Erro(21, string.Format("Usuário Bloqueado ({0})", usuario.IDUsuario), Formato);
                return;
            }

            //
            // NÚMERO DE REQUISIÇÕES
            //
            API_LogMetodos logMetodos = new API_LogMetodos();
            int NumRequisicoes = logMetodos.NumRequisicoes("HistoricoHorario", usuario.IDUsuario, IDMedicao);

            // verifica se passou do limite diário
            if (NumRequisicoes >= usuario.API_Limite)
            {
                // Log - [20] Ultrapassou limite diário de requisições
                API_Log(Key, "HistoricoHorario", 0, IDMedicao, DataIni, DataFim, 20);

                // erro
                API_Erro(20, string.Format("Ultrapassou limite diário de requisições ({0})", NumRequisicoes), Formato);
                return;
            }

            //
            // MEDICAO
            //

            // le medicao
            MedicoesMetodos medMetodos = new MedicoesMetodos();
            MedicoesDominio med = medMetodos.ListarPorId(IDMedicao);

            if (med == null)
            {
                // Log - [4] medição inexistente
                API_Log(Key, "HistoricoHorario", usuario.IDUsuario, IDMedicao, DataIni, DataFim, 4);

                // erro
                API_Erro(4, string.Format("Medição Incorreta ({0})", IDMedicao), Formato);
                return;
            }

            // verifica se medição de energia
            if (med.IDTipoMedicao != TIPO_MEDICAO.ENERGIA && med.IDTipoMedicao != TIPO_MEDICAO.ENERGIA_FORMULA)
            {
                // Log - [13] Medição não é de Energia
                API_Log(Key, "HistoricoHorario", usuario.IDUsuario, IDMedicao, DataIni, DataFim, 13);

                // erro
                API_Erro(13, string.Format("Medição não é de Energia ({0})", IDMedicao), Formato);
                return;
            }

            // cliente
            IDCliente = med.IDCliente;

            // verifica se usuario pertence a este cliente
            if (usuario.IDTipoAcesso == TIPO_ACESSO.CLIENTE_ADMIN || usuario.IDTipoAcesso == TIPO_ACESSO.CLIENTE_OPER || usuario.IDTipoAcesso == TIPO_ACESSO.DEMONSTRACAO)
            {
                if (usuario.IDCliente != IDCliente)
                {
                    // Log - [5] usuário não pertence ao cliente da medição
                    API_Log(Key, "HistoricoHorario", usuario.IDUsuario, IDMedicao, DataIni, DataFim, 5);

                    // erro
                    API_Erro(5, string.Format("Usuário ({0}) não pertence ao cliente ({1})", usuario.IDUsuario, usuario.IDCliente), Formato);
                    return;
                }
            }
            else if (usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR || usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR_ADMIN || usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR_OPER)
            {
                ClientesMetodos clienteMetodos = new ClientesMetodos();
                List<ClientesDominio> clientes = clienteMetodos.ListarPorIDConsultor(usuario.IDCliente, 1);

                if( clientes == null )
                {
                    // Log - [6] cliente inexistente
                    API_Log(Key, "HistoricoHorario", usuario.IDUsuario, IDMedicao, DataIni, DataFim, 6);

                    // erro
                    API_Erro(6, string.Format("Cliente inexistente ({0})", usuario.IDCliente), Formato);
                    return;
                }

                int index = clientes.FindIndex(x => x.IDCliente == IDCliente);
                if (index < 0) 
                {
                    // Log - [7] usuário não tem acesso ao cliente
                    API_Log(Key, "HistoricoHorario", usuario.IDUsuario, IDMedicao, DataIni, DataFim, 7);

                    // erro
                    API_Erro(7, string.Format("Usuário ({0}) não tem acesso ao cliente ({1})", usuario.IDUsuario, usuario.IDCliente), Formato);
                    return;
                }
            }


            //
            // REGISTROS
            //
            bool retorno = HistoricoHorario_EN(IDCliente, IDMedicao, DataIni, DataFim, Formato); 

            // verifica se erro nos registros
            if (!retorno)
            {
                // Log do SolicitaBD - [10] Sem registro
                API_Log(Key, "HistoricoHorario", usuario.IDUsuario, IDMedicao, DataIni, DataFim, 10);

                // erro
                API_Erro(10, "Sem Registros", Formato);
                return;
            }

            // Log do SolicitaBD - OK
            API_Log(Key, "HistoricoHorario", usuario.IDUsuario, IDMedicao, DataIni, DataFim, 0);
            return;
        }

        public bool HistoricoHorario_EN(int IDCliente, int IDMedicao, DateTime DataIni, DateTime DataFim, string Formato = "XML")
        {
            StringBuilder xmlBuilder = new StringBuilder();
            IFormatProvider iFormatProvider = new System.Globalization.CultureInfo("en-US");

            //
            // REGISTROS
            //

            // hora inicial considero a hora caso for minuto cheio
            DateTime DataIni_correta = new DateTime(DataIni.Year, DataIni.Month, DataIni.Day, DataIni.Hour, DataIni.Minute, 0);

            // verifico se minuto cheio
            if (DataIni.Minute == 0)
            {
                // acerto para minuto 15 desta hora para ser considerada
                DataIni_correta = DataIni.AddMinutes(-45);
            }

            EN_Metodos enMetodos = new EN_Metodos();
            List<EN_Dominio> enRegistros = enMetodos.ListarTodosPeriodo(IDCliente, IDMedicao, DataIni_correta, DataFim);

            // verifica se tem registros
            if (enRegistros == null)
            {
                // erro
                return(false);
            }

            if (enRegistros.Count == 0)
            {
                // erro
                return (false);
            }

            // calcula consumo e fator de potencia horario
            List<HistoricoHorarioAPI> historicoHorario = new List<HistoricoHorarioAPI>();

            // indice hora anterior
            int indice_hora = -1;
            int indice_hora_ant = -1;

            // registro
            DateTime DataHora = new DateTime(2000, 1, 1, 0, 0, 0);
            double consumoAtivo = 0.0;
            double consumoReativo = 0.0;
            int Periodo = 0;
            
            // percorre registros
            foreach (EN_Dominio reg in enRegistros)
            {
                // calcula indice da hora (tiro 15 minutos para hora ser o indice, Ex: 14:45-15=14:30, portanto indice 14 | 01:00-15=00:45, portanto indice 0)
                DateTime horaAux = reg.DataHora.AddMinutes(-15);

                // indice hora
                indice_hora = horaAux.Hour;

                // verifica se mudou hora, portanto fecho consumos
                if (indice_hora != indice_hora_ant)
                {
                    // verifica se primeira hora
                    if (indice_hora_ant != -1)
                    {
                        // registro hora correta
                        HistoricoHorarioAPI novaHora = new HistoricoHorarioAPI();
                        novaHora.DataHora = new DateTime(DataHora.Year, DataHora.Month, DataHora.Day, DataHora.Hour, 0, 0);
                        novaHora.DataHora = novaHora.DataHora.AddHours(1);

                        // fecho consumos
                        novaHora.ConsumoAtivo = consumoAtivo / 4.0;
                        novaHora.ConsumoReativo = consumoReativo / 4.0;

                        // fator de potencia horario
                        novaHora.FatorPotencia = Funcoes_FatPot.CalculaFatPot(novaHora.ConsumoAtivo, novaHora.ConsumoReativo);

                        // período
                        novaHora.Periodo = Periodo;

                        // adiciono no histórico horário
                        historicoHorario.Add(novaHora);
                    }
                    
                    // zero consumos
                    consumoAtivo = 0.0;
                    consumoReativo = 0.0;
                    
                    // copio indice para anterior
                    indice_hora_ant = indice_hora;

                    // data hora do registro horario atual
                    DataHora = new DateTime(horaAux.Year, horaAux.Month, horaAux.Day, horaAux.Hour, 0, 0);
                }

                // atualizo consumo
                consumoAtivo += reg.Ativo;
                consumoReativo += reg.Reativo;
                Periodo = reg.Periodo;
            }

            // verifica ainda não fechou o consumo desta última hora
            if (DataHora.Year != 2000)
            {
                // registro hora correta
                HistoricoHorarioAPI novaHora = new HistoricoHorarioAPI();
                novaHora.DataHora = new DateTime(DataHora.Year, DataHora.Month, DataHora.Day, DataHora.Hour, 0, 0);
                novaHora.DataHora = novaHora.DataHora.AddHours(1);

                // fecho consumos
                novaHora.ConsumoAtivo = consumoAtivo / 4.0;
                novaHora.ConsumoReativo = consumoReativo / 4.0;

                // fator de potencia horario
                novaHora.FatorPotencia = Funcoes_FatPot.CalculaFatPot(novaHora.ConsumoAtivo, novaHora.ConsumoReativo);

                // período
                novaHora.Periodo = Periodo;

                // adiciono no histórico horário
                historicoHorario.Add(novaHora);
            }


            // verifica se JSON
            if (Formato.ToUpper() == "JSON")
            {
                // inicia
                xmlBuilder.Append("{");

                // medicao
                xmlBuilder.Append(string.Format("\"Medicao\": {0},", IDMedicao));

                // cabecalho
                xmlBuilder.Append("\"Cabecalho\": ");
                xmlBuilder.Append("[\"DataHora\",\"ConsumoAtivo\",\"ConsumoReativo\",\"FatorPotencia\",\"Periodo\"],");

                // registros
                xmlBuilder.Append("\"Registros\": ");
                xmlBuilder.Append("[");

                // registros
                int conta = 0;

                foreach (HistoricoHorarioAPI reg in historicoHorario)
                {
                    xmlBuilder.Append("[");
                    xmlBuilder.Append(string.Format("\"{0:g}\",", reg.DataHora));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000},", reg.ConsumoAtivo));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000},", reg.ConsumoReativo));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000},", reg.FatorPotencia));

                    switch (reg.Periodo)
                    {
                        default:
                            xmlBuilder.Append(string.Format("\"{0}\"", reg.Periodo));
                            break;

                        case 0:
                            xmlBuilder.Append("\"P\"");
                            break;

                        case 1:
                            xmlBuilder.Append("\"FPI\"");
                            break;

                        case 2:
                            xmlBuilder.Append("\"FPC\"");
                            break;
                    }

                    // verifica se ultimo
                    if (conta < (historicoHorario.Count - 1))
                    {
                        // coloca virgula
                        xmlBuilder.Append("],");
                    }
                    else
                    {
                        xmlBuilder.Append("]");
                    }

                    // proximo
                    conta++;
                }

                // finaliza
                xmlBuilder.Append("]");
                xmlBuilder.Append("}");

                // ContentType
                Response.ContentType = "application/json";

                Response.BinaryWrite(Encoding.UTF8.GetBytes(xmlBuilder.ToString()));
                Response.End();
            }
            else
            {
                // monta XML
                xmlBuilder.Append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
                xmlBuilder.Append(string.Format("<Medicao medicaoID=\"{0:000000}\">", IDMedicao));

                // registros
                foreach (HistoricoHorarioAPI reg in historicoHorario)
                {
                    xmlBuilder.Append(string.Format("<Registro registroID=\"{0:g}\">", reg.DataHora));

                    xmlBuilder.Append("<ConsumoAtivo>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000}", reg.ConsumoAtivo));
                    xmlBuilder.Append("</ConsumoAtivo>");

                    xmlBuilder.Append("<ConsumoReativo>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000}", reg.ConsumoReativo));
                    xmlBuilder.Append("</ConsumoReativo>");

                    xmlBuilder.Append("<FatorPotencia>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.000}", reg.FatorPotencia));
                    xmlBuilder.Append("</FatorPotencia>");

                    xmlBuilder.Append("<Periodo>");
                    switch (reg.Periodo)
                    {
                        default:
                            xmlBuilder.Append(string.Format("{0}", reg.Periodo));
                            break;

                        case 0:
                            xmlBuilder.Append("P");
                            break;

                        case 1:
                            xmlBuilder.Append("FPI");
                            break;

                        case 2:
                            xmlBuilder.Append("FPC");
                            break;
                    }
                    xmlBuilder.Append("</Periodo>");

                    xmlBuilder.Append("</Registro>");
                }

                xmlBuilder.Append("</Medicao>");

                // ContentType
                Response.ContentType = "text/xml";

                Response.BinaryWrite(Encoding.UTF8.GetBytes(xmlBuilder.ToString()));
                Response.End();
            }

            // ok
            return (true);
        }
    }
}