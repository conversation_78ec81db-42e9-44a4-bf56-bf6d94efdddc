﻿using System;
using System.Collections.Generic;
using System.Text;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class APIController
    {
        // Medições do Gestor
        // http://www.smartenergy.com.br/API/Medicoes?login=1793&senha=Gestal123&arquivo=XML

        // Medições do Cliente
        // http://www.smartenergy.com.br/API/Medicoes?login=1789&senha=Gestal123&arquivo=XML

        // login = IDUsuario
        // senha = senha do usuario
        // arquivo = tipo do arquivo (XML ou JSON)

        // Status
        // 0  - OK
        // 1  - Erro: Key ou Datas nulas
        // 2  - Erro: Chave incorreta
        // 3  - Erro: Usuário inexistente
        // 4  - Erro: Usuário não é Cliente ou Gestor
        // 5  - Erro: Medicoes inexistentes
        // 6  - Erro: Cliente inexistente
        // 7  - Erro: ---
        // 8  - Erro: ---
        // 9  - Erro: ---
        // 10 - Erro: ---
        // 11 - Erro: ---
        // 12 - Erro: ---

        public class MedicoesAPI
        {
            public int IDCliente { get; set; }
            public string NomeCliente { get; set; }
            public string FantasiaCliente { get; set; }

            public int IDMedicao { get; set; }
            public string NomeMedicao { get; set; }
            public int IDTipoMedicao { get; set; }
        }


        public void Medicoes(int login, string senha, string arquivo = "XML")
        {
            // Inicializa variáveis
            DateTime Data = new DateTime(2000, 1, 1, 0, 0, 0);

            //
            // Parâmetros
            //
            if (senha == null)
            {
                // Log do SolicitaBD - [1] senha ou Datas nulas
                API_Log("", "Medicoes", login, 0, Data, Data, 1);

                // erro
                API_Erro(1, "Faltam Parâmetros", arquivo);
                return;
            }

            //
            // USUARIO
            //

            // le usuario
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = usuarioMetodos.ListarPorId(login);

            if (usuario != null)
            {
                // verifica se senha eh correta
                if (usuario.Senha != senha)
                {
                    // Log do SolicitaBD - [2] senha diferente da senha do usuário
                    API_Log("", "Medicoes", login, 0, Data, Data, 2);

                    // erro
                    API_Erro(2, string.Format("Usuário Incorreto ({0})", login), arquivo);
                    return;
                }
            }
            else
            {
                // Log do SolicitaBD - [3] usuário inexistente
                API_Log("", "Medicoes", login, 0, Data, Data, 3);

                // erro
                API_Erro(3, string.Format("Usuário Incorreto ({0})", login), arquivo);
                return;
            }

            // verifica se Cliente
            if (usuario.IDTipoAcesso == TIPO_ACESSO.CLIENTE_ADMIN || usuario.IDTipoAcesso == TIPO_ACESSO.CLIENTE_OPER || usuario.IDTipoAcesso == TIPO_ACESSO.DEMONSTRACAO)
            {
                // lista de medições
                Medicoes_Cliente(usuario, arquivo);

                // Log - OK
                API_Log("", "Medicoes", login, 0, Data, Data, 0);
                return;
            }

            // verifica se Gestor
            if (usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR || usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR_ADMIN || usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR_OPER)
            {
                // lista de medições
                Medicoes_Gestor(usuario, arquivo);

                // Log - OK
                API_Log("", "Medicoes", login, 0, Data, Data, 0);
                return;
            }

            // verifica se Admin
            if (usuario.IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || usuario.IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE || usuario.IDTipoAcesso == TIPO_ACESSO.GESTAL_VENDAS)
            {
                // lista de medições
                Medicoes_Gestor(usuario, arquivo);

                // Log - OK
                API_Log("", "Medicoes", login, 0, Data, Data, 0);
                return;
            }

            // Log - [4] Usuário não é Cliente ou Gestor
            API_Log("", "Medicoes", login, 0, Data, Data, 4);

            // erro
            API_Erro(4, string.Format("Usuário não é Cliente ou Gestor ({0})", login), arquivo);
            return;
        }

        public void Medicoes_Cliente(UsuarioDominio usuario, string arquivo = "XML")
        {
            // Inicializa variáveis
            DateTime Data = new DateTime(2000, 1, 1, 0, 0, 0);

            // IDCliente
            int IDCliente = usuario.IDCliente;

            // cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cliente = clienteMetodos.ListarPorId(IDCliente);

            if (cliente == null)
            {
                // Log - [6] Cliente inexistente
                API_Log("", "Medicoes", usuario.IDUsuario, 0, Data, Data, 6);

                // erro
                API_Erro(6, "Sem Cliente", arquivo);
                return;
            }

            // lista de medições
            List<MedicoesAPI> medicoesAPI = new List<MedicoesAPI>();

            // medições dos clientes do gestor
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicaoMetodos.ListarPorIDCliente(cliente.IDCliente);

            if (medicoes == null)
            {
                // Log - [5] Medicoes inexistentes
                API_Log("", "Medicoes", usuario.IDUsuario, 0, Data, Data, 5);

                // erro
                API_Erro(5, "Sem Medições", arquivo);
                return;
            }

            // percorre medicoes
            foreach (MedicoesDominio medicao in medicoes)
            {
                // medicao API
                MedicoesAPI medicaoAPI = new MedicoesAPI();

                medicaoAPI.IDCliente = cliente.IDCliente;
                medicaoAPI.NomeCliente = cliente.Nome;
                medicaoAPI.FantasiaCliente = cliente.Fantasia;
                medicaoAPI.IDMedicao = medicao.IDMedicao;
                medicaoAPI.NomeMedicao = medicao.Nome;
                medicaoAPI.IDTipoMedicao = medicao.IDTipoMedicao;

                // coloca na lista
                medicoesAPI.Add(medicaoAPI);
            }

            // lista de medições
            Medicoes_Lista(medicoesAPI, arquivo);

            return;
        }

        public void Medicoes_Gestor(UsuarioDominio usuario, string arquivo = "XML")
        {
            // Inicializa variáveis
            DateTime Data = new DateTime(2000, 1, 1, 0, 0, 0);

            // IDConsultor
            int IDConsultor = usuario.IDCliente;

            // clientes ativos do gestor
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> clientes = clienteMetodos.ListarPorIDConsultor(IDConsultor, 1);

            if (clientes == null)
            {
                // Log - [6] Cliente inexistente
                API_Log("", "Medicoes", usuario.IDUsuario, 0, Data, Data, 6);

                // erro
                API_Erro(6, "Sem Clientes", arquivo);
                return;
            }

            // lista de medições
            List<MedicoesAPI> medicoesAPI = new List<MedicoesAPI>();

            // percorre clientes
            foreach (ClientesDominio cliente in clientes)
            {
                // medições dos clientes do gestor
                MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                List<MedicoesDominio> medicoes = medicaoMetodos.ListarPorIDCliente(cliente.IDCliente);

                if (medicoes == null)
                {
                    // ignora
                    continue;
                }

                // percorre medicoes
                foreach (MedicoesDominio medicao in medicoes)
                {
                    // medicao API
                    MedicoesAPI medicaoAPI = new MedicoesAPI();

                    medicaoAPI.IDCliente = cliente.IDCliente;
                    medicaoAPI.NomeCliente = cliente.Nome;
                    medicaoAPI.FantasiaCliente = cliente.Fantasia;
                    medicaoAPI.IDMedicao = medicao.IDMedicao;
                    medicaoAPI.NomeMedicao = medicao.Nome;
                    medicaoAPI.IDTipoMedicao = medicao.IDTipoMedicao;

                    // coloca na lista
                    medicoesAPI.Add(medicaoAPI);
                }
            }

            // lista de medições
            Medicoes_Lista(medicoesAPI, arquivo);

            return;
        }

        public void Medicoes_Lista(List<MedicoesAPI> medicoesAPI, string arquivo = "XML")
        {

            // XML ou JSON
            StringBuilder xmlBuilder = new StringBuilder();
            IFormatProvider iFormatProvider = new System.Globalization.CultureInfo("en-US");

            // verifica se JSON
            if (arquivo.ToUpper() == "JSON")
            {
                // inicia
                xmlBuilder.Append("{");

                // gestor
                xmlBuilder.Append("\"Medicao\": ");
                xmlBuilder.Append("[\"IDCliente\",\"NomeCliente\",\"IDMedicao\",\"NomeMedicao\",\"TipoMedicao\"],");

                // medições
                xmlBuilder.Append("\"Medicoes\": ");
                xmlBuilder.Append("[");

                // percorre medições
                int conta = 0;

                foreach (MedicoesAPI medicaoAPI in medicoesAPI)
                {
                    xmlBuilder.Append("[");
                    xmlBuilder.Append(string.Format("\"{0:000000}\",", medicaoAPI.IDCliente));
                    xmlBuilder.Append(string.Format("\"{0}\",", medicaoAPI.NomeCliente));
                    xmlBuilder.Append(string.Format("\"{0:000000}\",", medicaoAPI.IDMedicao));
                    xmlBuilder.Append(string.Format("\"{0}\",", medicaoAPI.NomeMedicao));

                    switch (medicaoAPI.IDTipoMedicao)
                    {
                        default:
                            xmlBuilder.Append(string.Format("\"{0}\"", medicaoAPI.IDTipoMedicao));
                            break;

                        case TIPO_MEDICAO.ENERGIA:
                            xmlBuilder.Append("\"Energia\"");
                            break;

                        case TIPO_MEDICAO.ENERGIA_FORMULA:
                            xmlBuilder.Append("\"Fórmula Energia\"");
                            break;

                        case TIPO_MEDICAO.UTILIDADES:
                            xmlBuilder.Append("\"Utilidades\"");
                            break;

                        case TIPO_MEDICAO.UTILIDADES_FORMULA:
                            xmlBuilder.Append("\"Fórmula Utilidades\"");
                            break;

                        case TIPO_MEDICAO.ENTRADA_ANALOGICA:
                            xmlBuilder.Append("\"Entrada Analógica\"");
                            break;

                        case TIPO_MEDICAO.EA_FORMULA:
                            xmlBuilder.Append("\"Fórmula Entrada Analógica\"");
                            break;

                        case TIPO_MEDICAO.CICLOMETRO:
                            xmlBuilder.Append("\"Ciclômetro\"");
                            break;

                        case TIPO_MEDICAO.METEOROLOGIA:
                            xmlBuilder.Append("\"Meteorologia\"");
                            break;
                    }

                    // verifica se ultimo
                    if (conta < (medicoesAPI.Count - 1))
                    {
                        // coloca virgula
                        xmlBuilder.Append("],");
                    }
                    else
                    {
                        xmlBuilder.Append("]");
                    }

                    // proximo
                    conta++;
                }

                // finaliza
                xmlBuilder.Append("]");
                xmlBuilder.Append("}");

                // ContentType
                Response.ContentType = "application/json";
            }
            else
            {
                // monta XML
                xmlBuilder.Append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
                xmlBuilder.Append("<Medicoes>");

                // percorre medições
                foreach (MedicoesAPI medicaoAPI in medicoesAPI)
                {
                    xmlBuilder.Append(string.Format("<Medicao IDMedicao=\"{0:000000}\">", medicaoAPI.IDMedicao));

                    xmlBuilder.Append("<IDCliente>");
                    xmlBuilder.Append(string.Format("{0:000000}", medicaoAPI.IDCliente));
                    xmlBuilder.Append("</IDCliente>");

                    xmlBuilder.Append("<NomeCliente>");
                    xmlBuilder.Append(medicaoAPI.NomeCliente);
                    xmlBuilder.Append("</NomeCliente>");

                    xmlBuilder.Append("<IDMedicao>");
                    xmlBuilder.Append(string.Format("{0:000000}", medicaoAPI.IDMedicao));
                    xmlBuilder.Append("</IDMedicao>");

                    xmlBuilder.Append("<NomeMedicao>");
                    xmlBuilder.Append(medicaoAPI.NomeMedicao);
                    xmlBuilder.Append("</NomeMedicao>");

                    xmlBuilder.Append("<TipoMedicao>");
                    switch (medicaoAPI.IDTipoMedicao)
                    {
                        default:
                            xmlBuilder.Append(string.Format("{0}", medicaoAPI.IDTipoMedicao));
                            break;

                        case TIPO_MEDICAO.ENERGIA:
                            xmlBuilder.Append("Energia");
                            break;

                        case TIPO_MEDICAO.ENERGIA_FORMULA:
                            xmlBuilder.Append("Fórmula Energia");
                            break;

                        case TIPO_MEDICAO.UTILIDADES:
                            xmlBuilder.Append("Utilidades");
                            break;

                        case TIPO_MEDICAO.UTILIDADES_FORMULA:
                            xmlBuilder.Append("Fórmula Utilidades");
                            break;

                        case TIPO_MEDICAO.ENTRADA_ANALOGICA:
                            xmlBuilder.Append("Entrada Analógica");
                            break;

                        case TIPO_MEDICAO.EA_FORMULA:
                            xmlBuilder.Append("Fórmula Entrada Analógica");
                            break;

                        case TIPO_MEDICAO.CICLOMETRO:
                            xmlBuilder.Append("Ciclômetro");
                            break;

                        case TIPO_MEDICAO.METEOROLOGIA:
                            xmlBuilder.Append("Meteorologia");
                            break;
                    }
                    xmlBuilder.Append("</TipoMedicao>");

                    xmlBuilder.Append("</Medicao>");
                }

                xmlBuilder.Append("</Medicoes>");

                // ContentType
                Response.ContentType = "text/xml";
            }

            Response.BinaryWrite(Encoding.UTF8.GetBytes(xmlBuilder.ToString()));
            Response.End();

            return;
        }
    }
}
