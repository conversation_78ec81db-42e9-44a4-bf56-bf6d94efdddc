﻿using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class MercadoLivreController : Controller
    {

        // GET: Tarifas de Mercado Livre
        public ActionResult TarifasMercadoLivre(int IDMedicao)
        {
            // tela de ajuda - tarifas
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // inicialmente redireciona para pagina Historico de Tarifas da Medicao
            string caminho = string.Format("~/MercadoLivre/TarifasMercadoLivre_Historico?IDReferencia={0}&TipoTarifa=0", IDMedicao);

            // verifica se IDMedicao esta selecionada
            if( IDMedicao > 0 )
            {
                // le medicao
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                MedicoesDominio medicao = medicoesMetodos.ListarPorId(IDMedicao);

                // verifica se existe
                if( medicao != null )
                {
                    if( medicao.IDMedicao == IDMedicao )
                    {
                        // verifica configuracao das tarifas de mercado livre
                        if( medicao.IDGrupoTarifaMercadoLivre == 0 )
                        {
                            // historico de tarifas de mercado livre da medicao
                            caminho = string.Format("~/MercadoLivre/TarifasMercadoLivre_Historico?IDReferencia={0}&TipoTarifa=0", IDMedicao);
                        }
                        else
                        {
                            // historico de tarifas de mercado livre do grupo de tarifas
                            caminho = string.Format("~/MercadoLivre/TarifasMercadoLivre_Historico?IDReferencia={0}&TipoTarifa=1", medicao.IDGrupoTarifaMercadoLivre);
                        }
                    }
                }
            }
            
            // redireciona pagina
            return Redirect(caminho);
        }

        // permissoes
        private void Permissoes()
        {
            // permissoes
            // 0 - permissao de Admin: ve e escreve em tudo
            // 1 - permissao de Gerente: ve tudo e escreve parte
            // 2 - permissao de Operador: ve tudo e nao pode escrever
            // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)
            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
            // 5 - permissao de Consultor: ve tudo e escreve parte 
            // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte 
            int IDTipoAcesso = ViewBag._IDTipoAcesso;
            int IDConsultor = ViewBag._IDConsultor;

            switch (IDTipoAcesso)
            {
                case TIPO_ACESSO.MASTER:            // master
                case TIPO_ACESSO.GESTAL_ADMIN:      // admin

                    ViewBag.Permissao = PERMISSOES.ADMIN;
                    break;

                case TIPO_ACESSO.CONSULTOR:         // consultor
                case TIPO_ACESSO.CONSULTOR_ADMIN:   // consultor - administrador

                    ViewBag.Permissao = PERMISSOES.CONSULTOR;

                    // verifica se CPFL
                    if (IDConsultor == CLIENTES_ESPECIAIS.CPFL)
                    {
                        // CPFL é um super consultor
                        ViewBag.Permissao = PERMISSOES.SUPER_CONSULTOR;
                    }
                    break;

                case TIPO_ACESSO.CLIENTE_ADMIN:     // cliente - administrador

                    ViewBag.Permissao = PERMISSOES.GERENTE;
                    break;

                default:
                case TIPO_ACESSO.CLIENTE_OPER:      // operador
                case TIPO_ACESSO.GESTAL_VENDAS:     // vendas
                case TIPO_ACESSO.CONSULTOR_OPER:    // consultor - operador
                case TIPO_ACESSO.DEMONSTRACAO:      // demo

                    ViewBag.Permissao = PERMISSOES.OPERADOR;
                    break;

                case TIPO_ACESSO.GESTAL_PRODUCAO:   // producao

                    ViewBag.Permissao = PERMISSOES.PRODUCAO;
                    break;

                case TIPO_ACESSO.GESTAL_SUPORTE:    // suporte

                    ViewBag.Permissao = PERMISSOES.SUPORTE;
                    break;
            }
        }
    }
}