﻿using System;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class MercadoLivreController
    {
        // GET: Tarifas de Mercado Livre - Historico
        public ActionResult TarifasMercadoLivre_Historico(int IDReferencia, int TipoTarifa)
        {
            // ID
            // caso a medicao tenha o seu historico, IDReferencia = IDMedicao
            // caso a medicao utilize um grupo de tarifas, IDReferencia = IDGrupoTarifas

            // Pagina ajuda - Mercado Livre
            CookieStore.SalvaCookie_String("PaginaAjuda", "MercadoLivre_Historico");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "MercadoLivre_Historico");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le historicoMercadoLivre
            HistoricoMercadoLivreMetodos historicoMetodos = new HistoricoMercadoLivreMetodos();
            var listaHistorico = historicoMetodos.ListarPorIDReferencia(IDReferencia, TipoTarifa);
            ViewBag.HistoricoMercadoLivre = listaHistorico;

            // IDReferencia e TipoTarifa
            ViewBag.IDReferencia = IDReferencia;
            ViewBag.TipoTarifa = TipoTarifa;

            // nome do grupo de tarifas
            string NomeGrupoTarifas = "Histórico de Tarifas da própria Medição";

            if(TipoTarifa == 1)
            {
                // le grupo
                TarifasMercadoLivreGruposMetodos gruposMetodos = new TarifasMercadoLivreGruposMetodos();
                TarifasMercadoLivreGruposDominio grupo = gruposMetodos.ListarPorId(IDReferencia);

                NomeGrupoTarifas = "Grupo Inexistente";

                if( grupo != null )
                {
                    NomeGrupoTarifas = "Grupo de Tarifas - " + grupo.Nome;
                }
            }

            ViewBag.NomeGrupoTarifas = NomeGrupoTarifas;

            return View();
        }

        // GET: Tarifas de Mercado Livre - Historico - Editar
        public ActionResult TarifasMercadoLivre_Historico_Editar(int IDMercadoLivre, int IDReferencia, int TipoTarifa)
        {
            // pagina anterior
            ViewBag.returnUrl = Request.UrlReferrer;

            // ID IDMercadoLivre
            ViewBag.IDMercadoLivre = IDMercadoLivre;

            // Pagina ajuda - Mercado Livre
            CookieStore.SalvaCookie_String("PaginaAjuda", "MercadoLivre_HistoricoEditar");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // verifica se adicionando
            HistoricoMercadoLivreDominio historico = new HistoricoMercadoLivreDominio();
            if (IDMercadoLivre == 0)
            {
                // zera com default
                historico.IDMercadoLivre = 0;
                historico.IDCliente = ViewBag._IDCliente;
                historico.IDReferencia = IDReferencia;
                historico.TipoTarifa = TipoTarifa;
                historico.Data = DateTime.Now;
                historico.DataTexto = historico.Data.ToString("d");
                historico.EncargosConexao = 0.0;
                historico.DescontoDemanda = 0.0;
                historico.DescontoConsumo_P = 0.0;
                historico.DescontoConsumo_FP = 0.0;
                historico.TarifaConsumoML_P = 0.0;
                historico.TarifaConsumoML_FP = 0.0;
                historico.TarifaDevec_P = 0.0;
                historico.TarifaDevec_FP = 0.0;
                historico.SubvencaoTarifaria = false;
                historico.SubvencaoPisCofins = true;
                historico.EncargoCovid = false;
                historico.TarifaCovid_P = 0.0;
                historico.TarifaCovid_FP = 0.0;
            }
            else
            {
                // le historicoMercadoLivre
                HistoricoMercadoLivreMetodos historicoMetodos = new HistoricoMercadoLivreMetodos();
                historico = historicoMetodos.ListarPorID(IDMercadoLivre);
            }

            return View(historico);
        }

        // POST: Tarifas de Mercado Livre - Historico - Salvar
        [HttpPost]
        public ActionResult TarifasMercadoLivre_Historico_Salvar(HistoricoMercadoLivreDominio historico)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // parse data
            historico.Data = DateTime.Parse(historico.DataTexto);

            // verifica se existe outro historico com a mesma data
            HistoricoMercadoLivreMetodos historicoMetodos = new HistoricoMercadoLivreMetodos();
            if (historicoMetodos.VerificarDuplicidade(historico))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Desconto existente."
                };
            }
            else
            {
                // salva historicoMercadoLivre
                historicoMetodos.Salvar(historico);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Tarifas de Mercado Livre - Historico - Excluir
        public ActionResult TarifasMercadoLivre_Historico_Excluir(int IDMercadoLivre)
        {
            // apaga o historicoMercadoLivre
            HistoricoMercadoLivreMetodos historicoMetodos = new HistoricoMercadoLivreMetodos();
            historicoMetodos.Excluir(IDMercadoLivre);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}