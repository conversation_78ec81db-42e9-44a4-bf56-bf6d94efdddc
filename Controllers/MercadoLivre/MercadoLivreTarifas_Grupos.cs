﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class MercadoLivreController
    {
        // GET: Tarifas de Mercado Livre - Grupos
        public ActionResult TarifasMercadoLivre_Grupos(int IDCliente)
        {
            // tela de ajuda - tarifas
            CookieStore.SalvaCookie_String("PaginaAjuda", "MercadoLivre_Grupos");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le grupos
            TarifasMercadoLivreGruposMetodos gruposMetodos = new TarifasMercadoLivreGruposMetodos();
            List<TarifasMercadoLivreGruposDominio> listaGrupos = gruposMetodos.ListarPorIDCliente(IDCliente);

            return View(listaGrupos);
        }

        // GET: Tarifas de Mercado Livre - Grupos - Editar
        public ActionResult TarifasMercadoLivre_Grupos_Editar(int IDGrupoTarifas)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // verifica se adicionando
            TarifasMercadoLivreGruposDominio grupo = new TarifasMercadoLivreGruposDominio();
            if (IDGrupoTarifas == 0)
            {
                // zera grupo com default
                grupo.IDGrupoTarifas = 0;
                grupo.IDCliente = ViewBag._IDCliente;
            }
            else
            {
                // le grupo
                TarifasMercadoLivreGruposMetodos grupoMetodos = new TarifasMercadoLivreGruposMetodos();
                grupo = grupoMetodos.ListarPorId(IDGrupoTarifas);
            }

            return View(grupo);
        }

        // POST: Tarifas de Mercado Livre - Grupos - Salvar
        [HttpPost]
        public ActionResult TarifasMercadoLivre_Grupos_Salvar(TarifasMercadoLivreGruposDominio grupo)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro grupo com o mesmo nome
            TarifasMercadoLivreGruposMetodos grupoMetodos = new TarifasMercadoLivreGruposMetodos();
            if (grupoMetodos.VerificarDuplicidade(grupo))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Grupo existente."
                };
            }
            else
            {
                // salva grupo
                grupoMetodos.Salvar(grupo);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Tarifas de Mercado Livre - Grupos - Excluir
        public ActionResult TarifasMercadoLivre_Grupos_Excluir(int IDGrupoTarifas)
        {
            // exclui grupo
            TarifasMercadoLivreGruposMetodos grupoMetodos = new TarifasMercadoLivreGruposMetodos();
            grupoMetodos.Excluir(IDGrupoTarifas);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}