﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Web.Hosting;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{

    public partial class CPFLController : Controller
    {
        // Estatísticas
        DateTime DataHoraAtual = DateTime.Now;


        // clientes
        List<ClientesDominio> clientes = new List<ClientesDominio>();

        // empresas
        List<EmpresasDominio> empresas = new List<EmpresasDominio>();

        // comercializadoras
        List<AgentesDominio> comercializadoras = new List<AgentesDominio>();

        // tipos fonte
        List<ListaTiposDominio> listatiposFonte = new List<ListaTiposDominio>();

        // tipos SubSistema
        List<ListaTiposDominio> listatiposSubSistema = new List<ListaTiposDominio>();

        // le tipos índice de reajuste
        List<ListaTiposDominio> listatiposIndiceReajuste = new List<ListaTiposDominio>();

        // tipos carteira
        List<ListaTiposDominio> listatiposCarteira = new List<ListaTiposDominio>();


        // Listas utilizadas - Contrato de Energia
        private void PreparaListas_ContratoEnergia()
        {
            // le comercializadoras
            AgentesMetodos agentesMetodos = new AgentesMetodos();
            comercializadoras.Clear();
            comercializadoras = agentesMetodos.ListarPorComercializadora();

            // le tipos fonte
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            listatiposFonte.Clear();
            listatiposFonte = listatiposMetodos.ListarTodos("TipoFonte", false);

            // le tipos SubSistema
            listatiposSubSistema.Clear();
            listatiposSubSistema = listatiposMetodos.ListarTodos("TipoSubSistema", false);

            // le tipos índice de reajuste
            listatiposIndiceReajuste.Clear();
            listatiposIndiceReajuste = listatiposMetodos.ListarTodos("TipoIndiceReajuste", false);

            // le clientes da CPFL - ativos
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            clientes.Clear();
            clientes = clientesMetodos.ListarPorIDConsultor(CLIENTES_ESPECIAIS.CPFL, 1);

            // le empresas dos clientes da CPFL
            EmpresasMetodos empresasMetodos = new EmpresasMetodos();
            empresas.Clear();

            // percorre clientes
            if (clientes != null)
            {
                foreach (ClientesDominio cliente in clientes)
                {
                    // le empresas do cliente
                    List<EmpresasDominio> empresas_lidas = empresasMetodos.ListarPorIDCliente(cliente.IDCliente);

                    if (empresas_lidas != null)
                    {
                        // percorre empresas
                        foreach (EmpresasDominio empresa in empresas_lidas)
                        {
                            // monto CNPJ Razão Social
                            empresa.CNPJ_RazaoSocial = string.Format("{0} [{1}]", empresa.CNPJ, empresa.RazaoSocial);

                            // medição da unidade consumidora
                            MedicoesDominio medicao = new MedicoesDominio();
                            GatewaysDominio gateway = new GatewaysDominio();
                            empresa.IDMedicao_UnidadeConsumidora = Encontra_IDMedicao_UnidadeConsumidora_Principal(empresa.IDEmpresa, ref medicao, ref gateway);

                            if (medicao.IDMedicao > 0)
                            {
                                empresa.NomeMedicao = string.Format("[{0}] {1}", medicao.IDMedicao, medicao.Nome);
                                empresa.IDSubSistema = medicao.IDSubSistema;
                            }

                            // adiciona na lista das empresas
                            empresas.Add(empresa);
                        }
                    }
                }
            }

            return;
        }

        // encontra IDMedicao da unidade consumidora da empresa
        private int Encontra_IDMedicao_UnidadeConsumidora_Principal(int IDEmpresa, ref MedicoesDominio medicao, ref GatewaysDominio gateway)
        {
            // IDMedicao unidade consumidora
            int IDMedicao_UnidadeConsumidora = 0;

            medicao.Nome = "---";
            medicao.IDMedicao = 0;
            medicao.IDSubSistema = 0;
            medicao.IDGateway = 0;

            gateway.IDGateway = 0;

            // leio gateways atreladas a esta empresa
            GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
            List<GatewaysDominio> gateways = gatewayMetodos.ListarPorIDEmpresa(IDEmpresa);

            if (gateways != null)
            {
                // percorre gateways e encontra primeira medição de energia marcada como unidade consumidora
                foreach (GatewaysDominio gate in gateways)
                {
                    // filtro, somente medições principais e em ordem de número de medição interna
                    string order = "AND IDCategoriaMedicao = 0 ORDER BY NumMedGateway";

                    // leio medições da gateway
                    MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                    List<MedicoesDominio> medicoes = medicaoMetodos.ListarPorIDGateway(gate.IDGateway, order);

                    if (medicoes != null)
                    {
                        // percorre medições e encontra primeira medição de energia marcada como unidade consumidora
                        foreach (MedicoesDominio med in medicoes)
                        {
                            // verifica se medição de energia elétrica e medição 0
                            //if (med.IDTipoMedicao == TIPO_MEDICAO.ENERGIA && med.NumMedGateway == 0)

                            // verifica se medição de energia elétrica
                            if (med.IDTipoMedicao == TIPO_MEDICAO.ENERGIA)
                            {
                                // copia
                                medicao = med;
                                gateway = gate;

                                // achou
                                IDMedicao_UnidadeConsumidora = med.IDMedicao;
                                break;
                            }
                        }
                    }

                    // verifica se achou
                    if (IDMedicao_UnidadeConsumidora > 0)
                    {
                        // interrompe busca
                        break;
                    }
                }
            }

            return (IDMedicao_UnidadeConsumidora);
        }


        // Listas utilizadas - Contrato de Gestão
        private void PreparaListas_ContratoGestao()
        {

            // le tipos carteira
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            listatiposCarteira.Clear();
            listatiposCarteira = listatiposMetodos.ListarTodos("TipoCarteira", false);

            // le tipos índice de reajuste
            listatiposIndiceReajuste.Clear();
            listatiposIndiceReajuste = listatiposMetodos.ListarTodos("TipoIndiceReajuste", false);

            // le clientes da CPFL - ativos
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            clientes.Clear();
            clientes = clientesMetodos.ListarPorIDConsultor(CLIENTES_ESPECIAIS.CPFL, 1);

            // le empresas dos clientes da CPFL
            EmpresasMetodos empresasMetodos = new EmpresasMetodos();
            empresas.Clear();

            // percorre clientes
            if (clientes != null)
            {
                foreach (ClientesDominio cliente in clientes)
                {
                    // le empresas do cliente
                    List<EmpresasDominio> empresas_lidas = empresasMetodos.ListarPorIDCliente(cliente.IDCliente);

                    if (empresas_lidas != null)
                    {
                        // percorre empresas
                        foreach (EmpresasDominio empresa in empresas_lidas)
                        {
                            // monto CNPJ Razão Social
                            empresa.CNPJ_RazaoSocial = string.Format("{0} [{1}]", empresa.CNPJ, empresa.RazaoSocial);

                            // medição da unidade consumidora
                            MedicoesDominio medicao = new MedicoesDominio();
                            GatewaysDominio gateway = new GatewaysDominio();
                            empresa.IDMedicao_UnidadeConsumidora = Encontra_IDMedicao_UnidadeConsumidora_Principal(empresa.IDEmpresa, ref medicao, ref gateway);

                            if (medicao.IDMedicao > 0)
                            {
                                empresa.NomeMedicao = string.Format("[{0}] {1}", medicao.IDMedicao, medicao.Nome);
                                empresa.IDSubSistema = medicao.IDSubSistema;
                            }

                            // adiciona na lista das empresas
                            empresas.Add(empresa);
                        }
                    }
                }
            }

            return;
        }


        // Listas utilizadas - Provisionamento Semanal
        private void PreparaListas_ProvisionamentoSemanal()
        {
            // le comercializadoras
            AgentesMetodos agentesMetodos = new AgentesMetodos();
            comercializadoras.Clear();
            comercializadoras = agentesMetodos.ListarPorComercializadora();
            ViewBag.listaTipoComercializadoras = comercializadoras;

            // le tipos contrato medicao
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposContratoMedicao = listatiposMetodos.ListarTodos("TipoContratoMedicao");
            ViewBag.listaTipoContratoMedicao = listatiposContratoMedicao;

            // le tipos estrutura tarifaria
            List<ListaTiposDominio> listatiposEstruturaTarifaria = listatiposMetodos.ListarTodos("TipoEstruturaTarifaria");
            ViewBag.listaTipoEstruturaTarifaria = listatiposEstruturaTarifaria;

            // le clientes da CPFL - ativos
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            clientes.Clear();
            clientes = clientesMetodos.ListarPorIDConsultor(CLIENTES_ESPECIAIS.CPFL, 1);

            return;
        }



        // GET: EnviaAlertas_CPFL
        public ActionResult EnviaAlertas_CPFL()
        {
            // log
            LogMessage("----------------------------------------------------------------------------------------");
            LogMessage("EnviaAlertas_CPFL 1.00");

            // estatisticas
            DateTime DataHoraInicio = DateTime.Now;

            // retorna status
            var returnedData = new
            {
                status = true
            };


            //
            // Contrato Energia - Início da Vigência
            //
            ContratoEnergia_InicioVigencia();

            //
            // Contrato Energia - Fim da Vigência
            //
            ContratoEnergia_FimVigencia();

            //
            // Contrato Energia - Finalizou
            //
            ContratoEnergia_Finalizou();

            //
            // Contrato Energia - Reajuste de Preço
            //
            ContratoEnergia_ReajustePreco();

            //
            // Contrato Gestão - Fim da Vigência
            //
            ContratoGestao_FimVigencia();

            //
            // Contrato Gestão - Reajuste de Preço
            //
            ContratoGestao_ReajustePreco();

            //
            // Provisionamento Semanal
            //
            ProvisionamentoSemanal();


            //
            // Estatística
            //

            // diferença de tempo
            DateTime datahora_agora = System.DateTime.Now;
            TimeSpan diff = datahora_agora.Subtract(DataHoraInicio);

            string mensagem = string.Format("Tempo de Processamento {0:00}'{1:00}\"{2:000}", diff.Minutes, diff.Seconds, diff.Milliseconds);

            // envia email estatistica
            EnviaEmailEstatistica(mensagem);

            // log
            LogMessage(mensagem);
            LogMessage("----------------------------------------------------------------------------------------");
           

            // relatorio
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // envia email estatistica
        private void EnviaEmailEstatistica(string mensagem)
        {
            // assunto
            string assunto = "[Smart Energy] Envio de Alertas CPFL";

            // envia EMAIL
            var emailTemplate = "EnviaRelatoriosEstatisticaEmail";

            var message = EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.UsuarioNome", "Junior");
            message = message.Replace("ViewBag.LogoConsultor", "LogoSmartEnergy.png");
            message = message.Replace("ViewBag.Mensagem", mensagem);

            EmailServices.SendEmail("<EMAIL>", assunto, message, "", "", null);

            // log
            LogMessage("----------------------------------------------------------------------------------------");
            LogMessage("Enviou email estatísticas");

            return;
        }


        public static string EMailTemplate(string template)
        {
            var templateFilePath = HostingEnvironment.MapPath("~/Content/emailTemplates/") + template + ".cshtml";
            StreamReader objstreamreaderfile = new StreamReader(templateFilePath);
            var body = objstreamreaderfile.ReadToEnd();
            objstreamreaderfile.Close();
            return body;
        }


        private void LogMessage(string msg, string arquivo = null)
        {
            // nome de arquivo padrao
            string nome_arquivo = "LogEnviaAlertas_CPFL_" + String.Format("{0:yyyyMMdd}", DateTime.Today) + ".txt";

            // verifica se tem nome de arquivo
            if (arquivo != null)
            {
                nome_arquivo = arquivo;
            }

            // subdiretorio LOG
            string exeRuntimeDirectory = Server.MapPath("~/EnviaRelatorios");
            string subDirectory = System.IO.Path.Combine(exeRuntimeDirectory, "Log");
            if (!System.IO.Directory.Exists(subDirectory))
            {
                // Output directory does not exist, so create it.
                System.IO.Directory.CreateDirectory(subDirectory);
            }

            // caminho completo
            string path = System.IO.Path.Combine(subDirectory, nome_arquivo);

            // abre arquivo
            System.IO.StreamWriter sw = System.IO.File.AppendText(path);

            try
            {
                // diferença de tempo
                DateTime datahora_agora = System.DateTime.Now;
                TimeSpan diff = datahora_agora.Subtract(DataHoraAtual);
                DataHoraAtual = datahora_agora;

                // grava log
                string logLine = System.String.Format(
                    "{0:dd/MM/yyyy HH:mm:ss.fff}: [{1:00}\"{2:000}] {3}", datahora_agora, diff.Seconds, diff.Milliseconds, msg);
                sw.WriteLine(logLine);
                sw.Flush();
            }
            finally
            {
                sw.Close();
            }
        }

    }
}
