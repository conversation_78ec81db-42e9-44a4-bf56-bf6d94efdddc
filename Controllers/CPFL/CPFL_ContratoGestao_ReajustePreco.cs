﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{

    public partial class CPFLController
    {

        // Contrato Gestão - Reajuste de Preço
        private void ContratoGestao_ReajustePreco()
        {
            //
            // Alerta Reajuste de Preço Contrato Gestão
            //
            // Alerta o gestor quando algum contrato VIGENTE tiver reajuste de preço no mês de referência.
            //
            // Para o gestor é enviado em anexo uma planilha com os contratos de todos os clientes que estão para ter reajuste de preço este mês.
            //
            // Esta rotina é executada todo dia 1, 5, 10, 15, 20, 25 e 30 às 7am.
            //

            // log
            LogMessage("----------------------------------------------------------------------------------------");
            LogMessage(">> Alerta Reajuste de Preço Contrato de Gestão");

            // data atual
            DateTime hoje = System.DateTime.Now;
            DateTime dataAtual = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 0, 0);

            // verifica se é dia de verificar o reajuste
            int dia = dataAtual.Day;

            if (dia != 1 && dia != 5 && dia != 10 && dia != 15 && dia != 20 && dia != 25 && dia != 30)
            {
                // não é dia para verificar
                LogMessage(">> Hoje não deve verificar Reajuste de Preço Contrato de Gestão");
                return;
            }


            // Listas utilizadas
            PreparaListas_ContratoGestao();


            // verifica se existe clientes e empresas
            if (clientes != null && empresas != null)
            {
                // contrato a reajustar do Gestor
                List<EmpresasDominio> contratosReajustar_Gestor = new List<EmpresasDominio>();

                // percorre clientes
                foreach (ClientesDominio cliente in clientes)
                {
                    // percorre contratos
                    foreach (EmpresasDominio contrato in empresas)
                    {
                        //
                        // Verifica se deve analisar o contrato
                        //

                        // verifica se este contrato é deste cliente
                        if (contrato.IDCliente != cliente.IDCliente)
                        {
                            // próximo contrato
                            continue;
                        }

                        // verifica se contrato é vigente
                        if (contrato.Contrato_Status != TIPO_CONTRATO_STATUS.Vigente)
                        {
                            // próximo contrato
                            continue;
                        }

                        // verifica se contrato reajusta este mes
                        if (contrato.Reajuste_Mes != dataAtual.Month)
                        {
                            // próximo contrato
                            continue;
                        }

                        // insiro na lista de contratos com reajuste do Gestor
                        contratosReajustar_Gestor.Add(contrato);
                    }
                }

                // verifica se existe contrato com reajuste de preço
                if (contratosReajustar_Gestor.Count > 0)
                {
                    // Contrato Gestão - Reajuste de Preço - Enviar Alerta ao Gestor
                    ContratoGestao_ReajustePreco_Gestor(contratosReajustar_Gestor);
                }
            }
        }

        // Contrato Gestão - Reajuste de Preço - Enviar Alerta ao Gestor
        private void ContratoGestao_ReajustePreco_Gestor(List<EmpresasDominio> contratosReajustar_Gestor)
        {
            // data atual
            DateTime hoje = System.DateTime.Now;
            DateTime dataAtual = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 0, 0);

            //
            // Prepara planilha de contratos
            //

            // buffer
            byte[] dataBuffer;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            // gera planilha de contratos
            workbook = ContratoGestao_ReajustePreco_XLS(contratosReajustar_Gestor, dataAtual);

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                dataBuffer = memoryStream.ToArray();
            }


            //
            // Usuários a enviar
            //

            // usuários a enviar
            List<UsuarioDominio> usuarios_enviar = new List<UsuarioDominio>();

            // usuários do cliente
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            List<UsuarioDominio> usuarios = usuarioMetodos.ListarOperadoresConsultor(CLIENTES_ESPECIAIS.CPFL);

            if (usuarios != null)
            {
                // percorre usuarios
                foreach (UsuarioDominio usuario in usuarios)
                {
                    // verifica se usuário deseja receber alerta
                    if (usuario.Gestao_Contrato == 1)
                    {
                        usuarios_enviar.Add(usuario);
                    }
                }
            }


            //
            // ENVIA EMAIL PARA USUARIOS
            //

            // log
            LogMessage(">> Enviando Contratos para os Gestores");

            // envia email para todos destinatários simultaneamente
            ContratoGestao_ReajustePreco_Gestor_EnviaBulkEmail(usuarios_enviar, dataAtual, "ContratosGestao_ReajustePreco.xls", "application/vnd.ms-excel", dataBuffer);

            return;
        }


        // envia email para vários destinatários - gestor
        private void ContratoGestao_ReajustePreco_Gestor_EnviaBulkEmail(List<UsuarioDominio> usuarios, DateTime dataAtual, string attachmentName, string attachmentType, byte[] attachment)
        {
            // verifica se tem usuarios
            if (usuarios == null)
            {
                // log
                LogMessage(">> Sem usuários para enviar");

                return;
            }

            if (usuarios.Count <= 0)
            {
                // log
                LogMessage(">> Sem usuários para enviar");

                return;
            }


            // assunto
            string assunto = "[Smart Energy] Reajuste de Preço dos Contratos de Gestão";

            // envia EMAIL
            var emailTemplate = "ContratoGestaoReajustePrecoGestorEmail_CPFL";

            var message = EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.UsuarioNome", "Prezado Gestor");

            // Mes reajuste
            CultureInfo culture = new CultureInfo("pt-BR");
            string NomeMes = dataAtual.ToString("MMMM", culture);
            message = message.Replace("ViewBag.Mes", string.Format("{0} de {1:yyyy}", NomeMes, dataAtual));

            // lista de emails
            List<string> emails = new List<string>();

            // percorre usuários
            foreach (UsuarioDominio usuario in usuarios)
            {
                // coloca na lista
                emails.Add(usuario.Email);
            }

            // envia
            EmailServices.SendBulkEmail(emails, assunto, message, attachmentName, attachmentType, attachment);

            // log
            foreach (UsuarioDominio usuario in usuarios)
            {
                // log
                LogMessage(string.Format(">>>> Enviou email para usuário [{0:000000}] {1}", usuario.IDUsuario, usuario.Email));
            }

            return;
        }


        // Contrato Gestão - Reajuste de Preço - planilha XLS
        private HSSFWorkbook ContratoGestao_ReajustePreco_XLS(List<EmpresasDominio> contratos, DateTime dataAtual)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);


            //
            // CONTRATOS
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Contratos de Gestão");

            // cabecalho
            string[] cabecalho = { "Sigla CCEE", "Carteira", "Valor Base (R$)", "Data Base", "Reajuste", "Indexador", "Valor Reajustado (R$)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            IRow row;

            // percorre contratos
            foreach (EmpresasDominio contrato in contratos)
            {
                //
                // Descrições
                //

                // Carteira
                string NomeCarteira = "";

                // carteira
                ListaTiposDominio carteira = listatiposCarteira.Find(e => e.ID == contrato.Carteira);

                if (carteira != null)
                {
                    // Carteira
                    NomeCarteira = carteira.Descricao;
                }

                // Indexador
                string NomeIndexador = "";

                // indexador
                ListaTiposDominio indexador = listatiposIndiceReajuste.Find(e => e.ID == contrato.Reajuste_Indice);

                if (indexador != null)
                {
                    // Indexador
                    NomeIndexador = indexador.Descricao;
                }


                //
                // Planilha
                //

                // adiciona linha
                row = sheet.CreateRow(rowIndex++);

                // SiglaCCEE
                textoCelulaXLS(row, 0, contrato.SiglaCCEE);

                // Carteira
                textoCelulaXLS(row, 1, NomeCarteira);

                // Valor base
                numeroCelulaXLS(row, 2, contrato.Base_Valor, _2CellStyle);

                // Data Base
                textoCelulaXLS(row, 3, string.Format("{0:MM/yyyy}", contrato.Base_Data));

                // Mes reajuste
                CultureInfo culture = new CultureInfo("pt-BR");
                string NomeMes = dataAtual.ToString("MMMM", culture);
                textoCelulaXLS(row, 4, NomeMes);

                // Indexador
                textoCelulaXLS(row, 5, NomeIndexador);

                // Valor Reajustado
                numeroCelulaXLS(row, 6, contrato.Reajuste_Valor, _2CellStyle);
            }

            // largura de cada coluna
            for (int i = 0; i < sheet.GetRow(0).LastCellNum; i++)
            {
                sheet.SetColumnWidth(i, 6000);
            }

            // retorna planilha
            return workbook;
        }
    }
}
