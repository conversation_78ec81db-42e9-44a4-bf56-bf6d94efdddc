﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{

    public partial class CPFLController
    {

        // Contrato Energia - Reajuste de Preço
        private void ContratoEnergia_ReajustePreco()
        {
            //
            // Alerta Reajuste de Preço Contrato Energia
            //
            // Alerta o gestor quando algum contrato VIGENTE tiver reajuste de preço no mês de referência.
            //
            // Para o gestor é enviado em anexo uma planilha com os contratos de todos os clientes que estão para ter reajuste de preço este mês.
            //
            // Esta rotina é executada todo dia 1, 5, 10, 15, 20, 25 e 30 às 7am.
            //

            // log
            LogMessage("----------------------------------------------------------------------------------------");
            LogMessage(">> Alerta Reajuste de Preço Contrato de Energia");

            // data atual
            DateTime hoje = System.DateTime.Now;
            DateTime dataAtual = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 0, 0);

            // verifica se é dia de verificar o reajuste
            int dia = dataAtual.Day;

            if (dia != 1 && dia != 5 && dia != 10 && dia != 15 && dia != 20 && dia != 25 && dia != 30)
            {
                // não é dia para verificar
                LogMessage(">> Hoje não deve verificar Reajuste de Preço Contrato de Energia");
                return;
            }


            // Listas utilizadas
            PreparaListas_ContratoEnergia();


            // verifica se existe clientes e empresas
            if (clientes != null && empresas != null)
            {
                // contrato a reajustar do Gestor
                List<ContratosCCEEDominio> contratosReajustar_Gestor = new List<ContratosCCEEDominio>();

                // percorre clientes
                foreach (ClientesDominio cliente in clientes)
                {
                    // contratos energia Vigentes
                    ContratosCCEEMetodos contratosMetodos = new ContratosCCEEMetodos();
                    List<ContratosCCEEDominio> contratos = contratosMetodos.ListarPorRejustePreco(cliente.IDCliente, dataAtual.Month);

                    if (contratos != null)
                    {
                        // percorre contratos
                        foreach (ContratosCCEEDominio contrato in contratos)
                        {
                            //
                            // Atualizo estrutura com informações
                            //

                            if (contrato.IDEmpresa > 0)
                            {
                                // empresa
                                EmpresasDominio empresa = empresas.Find(e => e.IDEmpresa == contrato.IDEmpresa);

                                if (empresa != null)
                                {
                                    // SiglaCCEE
                                    contrato.SiglaCCEE = empresa.SiglaCCEE;
                                }
                            }

                            // insiro na lista de contratos com reajuste do Gestor
                            contratosReajustar_Gestor.Add(contrato);
                        }
                    }
                }

                // verifica se existe contrato com reajuste de preço
                if (contratosReajustar_Gestor.Count > 0)
                {
                    // Contrato Energia - Reajuste de Preço - Enviar Alerta ao Gestor
                    ContratoEnergia_ReajustePreco_Gestor(contratosReajustar_Gestor);
                }
            }
        }

        // Contrato Energia - Reajuste de Preço - Enviar Alerta ao Gestor
        private void ContratoEnergia_ReajustePreco_Gestor(List<ContratosCCEEDominio> contratosReajustar_Gestor)
        {
            // data atual
            DateTime hoje = System.DateTime.Now;
            DateTime dataAtual = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 0, 0);

            //
            // Prepara planilha de contratos
            //

            // buffer
            byte[] dataBuffer;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            // gera planilha de contratos
            workbook = ContratoEnergia_ReajustePreco_XLS(contratosReajustar_Gestor, dataAtual);

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                dataBuffer = memoryStream.ToArray();
            }


            //
            // Usuários a enviar
            //

            // usuários a enviar
            List<UsuarioDominio> usuarios_enviar = new List<UsuarioDominio>();

            // usuários do cliente
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            List<UsuarioDominio> usuarios = usuarioMetodos.ListarOperadoresConsultor(CLIENTES_ESPECIAIS.CPFL);

            if (usuarios != null)
            {
                // percorre usuarios
                foreach (UsuarioDominio usuario in usuarios)
                {
                    // verifica se usuário deseja receber alerta
                    if (usuario.Gestao_Energia == 1)
                    {
                        usuarios_enviar.Add(usuario);
                    }
                }
            }


            //
            // ENVIA EMAIL PARA USUARIOS
            //

            // log
            LogMessage(">> Enviando Contratos para os Gestores");

            // envia email para todos destinatários simultaneamente
            ContratoEnergia_ReajustePreco_Gestor_EnviaBulkEmail(usuarios_enviar, dataAtual, "ContratosEnergia_ReajustePreco.xls", "application/vnd.ms-excel", dataBuffer);

            return;
        }


        // envia email para vários destinatários - gestor
        private void ContratoEnergia_ReajustePreco_Gestor_EnviaBulkEmail(List<UsuarioDominio> usuarios, DateTime dataAtual, string attachmentName, string attachmentType, byte[] attachment)
        {
            // verifica se tem usuarios
            if (usuarios == null)
            {
                // log
                LogMessage(">> Sem usuários para enviar");

                return;
            }

            if (usuarios.Count <= 0)
            {
                // log
                LogMessage(">> Sem usuários para enviar");

                return;
            }


            // assunto
            string assunto = "[Smart Energy] Reajuste de Preço dos Contratos de Energia";

            // envia EMAIL
            var emailTemplate = "ContratoEnergiaReajustePrecoGestorEmail_CPFL";

            var message = EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.UsuarioNome", "Prezado Gestor");

            // Mes reajuste
            CultureInfo culture = new CultureInfo("pt-BR");
            string NomeMes = dataAtual.ToString("MMMM", culture);
            message = message.Replace("ViewBag.Mes", string.Format("{0} de {1:yyyy}", NomeMes, dataAtual));

            // lista de emails
            List<string> emails = new List<string>();

            // percorre usuários
            foreach (UsuarioDominio usuario in usuarios)
            {
                // coloca na lista
                emails.Add(usuario.Email);
            }

            // envia
            EmailServices.SendBulkEmail(emails, assunto, message, attachmentName, attachmentType, attachment);

            // log
            foreach (UsuarioDominio usuario in usuarios)
            {
                // log
                LogMessage(string.Format(">>>> Enviou email para usuário [{0:000000}] {1}", usuario.IDUsuario, usuario.Email));
            }

            return;
        }


        // Contrato Energia - Reajuste de Preço - planilha XLS
        private HSSFWorkbook ContratoEnergia_ReajustePreco_XLS(List<ContratosCCEEDominio> contratos, DateTime dataAtual)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);


            //
            // CONTRATOS
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Contratos de Energia");

            // cabecalho
            string[] cabecalho = { "Sigla CCEE", "Código Contrato", "Comercializadora", "Preço Base (R$)", "Data Base", "Reajuste", "Indexador" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            IRow row;

            // percorre contratos
            foreach (ContratosCCEEDominio contrato in contratos)
            {
                //
                // Descrições
                //

                // Comercializadora
                string NomeComercializadora = "";

                // comercializadora
                AgentesDominio comercializadora = comercializadoras.Find(e => e.IDAgente == contrato.IDComercializadora);

                if (comercializadora != null)
                {
                    // Comercializadora
                    NomeComercializadora = comercializadora.Nome;
                }

                // Indexador
                string NomeIndexador = "";

                // indexador
                ListaTiposDominio indexador = listatiposIndiceReajuste.Find(e => e.ID == contrato.Reajuste_Indice);

                if (indexador != null)
                {
                    // Indexador
                    NomeIndexador = indexador.Descricao;
                }

                // Preço base
                double preco_base = 0.0;

                if (contrato.Base_Preco_Flat)
                {
                    preco_base = contrato.Base_Preco;
                }
                else
                {
                    // busca preço base do mês
                    ContratosCCEE_PrecoBaseMetodos precoBaseMetodos = new ContratosCCEE_PrecoBaseMetodos();
                    ContratosCCEE_PrecoBaseDominio precoBase = precoBaseMetodos.ListarPorMes(contrato.IDContratoCCEE, dataAtual);

                    if (precoBase != null)
                    {
                        preco_base = precoBase.Base_Preco;
                    }
                }


                //
                // Planilha
                //

                // adiciona linha
                row = sheet.CreateRow(rowIndex++);

                // SiglaCCEE
                textoCelulaXLS(row, 0, contrato.SiglaCCEE);

                // Código Contrato
                textoCelulaXLS(row, 1, contrato.Contrato_Codigo);

                // Comercializadora
                textoCelulaXLS(row, 2, NomeComercializadora);

                // Preço base
                numeroCelulaXLS(row, 3, contrato.Base_Preco, _2CellStyle);

                // Data Base
                textoCelulaXLS(row, 4, string.Format("{0:MM/yyyy}", contrato.Base_Data));

                // Mes reajuste
                CultureInfo culture = new CultureInfo("pt-BR");
                string NomeMes = dataAtual.ToString("MMMM", culture);
                textoCelulaXLS(row, 5, NomeMes);

                // Indexador
                textoCelulaXLS(row, 6, NomeIndexador);
            }

            // largura de cada coluna
            for (int i = 0; i < sheet.GetRow(0).LastCellNum; i++)
            {
                sheet.SetColumnWidth(i, 6000);
            }

            // retorna planilha
            return workbook;
        }
    }
}
