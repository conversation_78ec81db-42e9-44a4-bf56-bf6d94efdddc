﻿using System;
using System.Collections.Generic;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{

    public partial class CPFLController
    {

        //
        // PROVISIONAMENTO SEMANAL TIPO 3
        //
        //
        // Tipo 3 = N contratos para N unidades consumidoras
        //

        // Calcula Provisionamento Semanal Tipo 3
        private void Calc_Provisionamento_Semanal_Tipo3(int IDEmpresa, DateTime DataAtual, List<ContratosCCEEDominio> contratosEnergia)
        {

            //
            // Prepara Provisionamento Semanal
            //
            Provisionamento_Tipo3 provisionamento = new Provisionamento_Tipo3();

            // prepara provisionamento semanal
            PreparaProvisionamentoSemanal_Tipo3(IDEmpresa, DataAtual, ref provisionamento);


            //
            // Calcula Provisionamento Semanal
            //

            // calcula provisionamento
            Calc_Tipo3(IDEmpresa, ref provisionamento, contratosEnergia);


            //
            // Resultado
            //

            ViewBag.ClienteNome = contratosEnergia[0].NomeCliente;
            ViewBag.RazaoSocial = contratosEnergia[0].RazaoSocial;
            ViewBag.SiglaCCEE = contratosEnergia[0].SiglaCCEE;
            ViewBag.Contrato_Codigo = "";
            ViewBag.DataTextoAtual = string.Format("{0:MMMM/yyyy}", DataAtual);

            return;
        }


        // Prepara Provisionamento Semanal Tipo 3
        private void PreparaProvisionamentoSemanal_Tipo3(int IDEmpresa, DateTime DataAtual, ref Provisionamento_Tipo3 provisionamento)
        {

            // provisionamento semanal
            provisionamento.IDEmpresa = IDEmpresa;
            provisionamento.Data = DataAtual;
            provisionamento.Data_Texto = string.Format("{0:dd/MM/yyyy}", DataAtual);

            return;
        }


        // Calcula Provisionamento Semanal Tipo3
        private void Calc_Tipo3(int IDEmpresa, ref Provisionamento_Tipo3 provisionamento, List<ContratosCCEEDominio> contratosEnergia)
        {
            //
            // Primeiro contrato será utilizado como base
            //
            ContratosCCEEDominio contrato_energia = contratosEnergia[0];

            //
            // Unidades Consumidoras do Contrato
            //
            List<Provisionamento_UnidadesConsumidoras> unidadesConsumidoras = new List<Provisionamento_UnidadesConsumidoras>();


            //
            // Unidades consumidoras do contrato (inclusive o garantidor)
            //

            // garantidor
            Provisionamento_UnidadesConsumidoras unidade = new Provisionamento_UnidadesConsumidoras();

            // IDUnidadeConsumidora
            int IDUnidadeConsumidora = 1;

            // IDComercializadora
            unidade.IDComercializadora = contrato_energia.IDComercializadora;

            // preenche info da unidade consumidora
            if (InfoUnidadeConsumidora(ref unidade, contrato_energia.IDEmpresa))
            {
                // IDUnidadeConsumidora
                unidade.IDUnidadeConsumidora = IDUnidadeConsumidora++;

                // percentual de carga
                unidade.PercentualCarga_Possui = contrato_energia.PercentualCarga_Possui;
                unidade.PercentualCarga_Valor = contrato_energia.PercentualCarga_Valor;

                // adiona na lista de unidades consumidoras
                unidadesConsumidoras.Add(unidade);
            }

            // unidades atendidas pelo contrato
            ContratosCCEE_UnidadesAtendidasMetodos unidadesAtendidasMetodos = new ContratosCCEE_UnidadesAtendidasMetodos();
            List<ContratosCCEE_UnidadesAtendidasDominio> unidadesAtendidas = unidadesAtendidasMetodos.ListarPorId(contrato_energia.IDContratoCCEE);

            if (unidadesAtendidas != null)
            {
                foreach (ContratosCCEE_UnidadesAtendidasDominio unidadeAtendida in unidadesAtendidas)
                {
                    // unidade consumidora
                    Provisionamento_UnidadesConsumidoras unidConsumidora = new Provisionamento_UnidadesConsumidoras();

                    // IDComercializadora
                    unidConsumidora.IDComercializadora = contrato_energia.IDComercializadora;

                    // preenche info da unidade consumidora
                    if (InfoUnidadeConsumidora(ref unidConsumidora, unidadeAtendida.IDEmpresa))
                    {
                        // IDUnidadeConsumidora
                        unidConsumidora.IDUnidadeConsumidora = IDUnidadeConsumidora++;

                        // percentual de carga
                        unidConsumidora.PercentualCarga_Possui = contrato_energia.PercentualCarga_Possui;
                        unidConsumidora.PercentualCarga_Valor = unidadeAtendida.PercentualCarga_Valor;

                        // adiona na lista de unidades consumidoras
                        unidadesConsumidoras.Add(unidConsumidora);
                    }
                }
            }


            //
            // Analise Contrato x Consumo
            //
            AnaliseContratoConsumo_Tipo3(ref provisionamento, contratosEnergia, unidadesConsumidoras);


            //
            // Resultado
            //

            ViewBag.ProvisionamentoSemanal = provisionamento;
            ViewBag.unidadesConsumidoras = unidadesConsumidoras;

            return;
        }


        // Analise Contrato x Consumo
        private void AnaliseContratoConsumo_Tipo3(ref Provisionamento_Tipo3 provisionamento, List<ContratosCCEEDominio> contratosEnergia, List<Provisionamento_UnidadesConsumidoras> unidadesConsumidoras)
        {
            //
            // Primeiro contrato será utilizado como base
            //
            ContratosCCEEDominio contrato_energia = contratosEnergia[0];

            //
            // Consumos Consolidado e Estimado do Mês
            //
            EN_ConsolidadoMensal consumo_mensal_total = new EN_ConsolidadoMensal();
            ConsumoConsolidadoEstimado_Mes(provisionamento.Data, contrato_energia, ref consumo_mensal_total, unidadesConsumidoras);

            //
            // Consumos Consolidado 12 meses
            //
            ConsumoConsolidado_12meses(provisionamento.Data, contratosEnergia, consumo_mensal_total, unidadesConsumidoras);

            //
            // Analise Contrato x Consumo
            //

            // Consumo Consolidado e Estimado
            if (consumo_mensal_total != null)
            {
                // consumo consolidado
                provisionamento.Consumo_Consolidado = consumo_mensal_total.Consumo_Consolidado;

                // consumo estimado
                provisionamento.Consumo_Estimado = consumo_mensal_total.Consumo_Estimado;
            }

            // Consumo Total Bruto
            provisionamento.Consumo_Total_Bruto = provisionamento.Consumo_Consolidado + provisionamento.Consumo_Estimado;

            // Perdas
            provisionamento.Perdas = contrato_energia.Perdas;

            // Previsão de Consumo
            provisionamento.Previsao_Consumo = provisionamento.Consumo_Total_Bruto * (1.0 + (provisionamento.Perdas / 100.0));

            // Total de Contratos
            provisionamento.Volume_Contratado = 0;
            provisionamento.Total_Contrato = 0;

            // necessidade de contratação total
            double Necessidade_Contratacao_Total = 0.0;

            // contador de contratos
            int contaContrato = 0;

            // percorre contratos
            foreach (ContratosCCEEDominio contrato in contratosEnergia)
            {
                // provisionamento do contrato
                Provisionamento_Tipo1_Tipo2 prov = new Provisionamento_Tipo1_Tipo2();

                // informações gerais
                prov.IDEmpresa = contrato_energia.IDEmpresa;
                prov.IDContratoCCEE = contrato.IDContratoCCEE;
                prov.Contrato_Codigo = contrato.Contrato_Codigo;
                prov.Data = provisionamento.Data;
                prov.Data_Texto = provisionamento.Data_Texto;

                // verifica se possui percentual de carga
                if (contrato.PercentualCarga_Possui)
                {
                    // consumos
                    prov.Consumo_Consolidado = 0.0;
                    prov.Consumo_Estimado = 0.0;
                    prov.Consumo_Total_Bruto = 0.0;

                    // percorre unidades consumidoras
                    foreach (Provisionamento_UnidadesConsumidoras unid in unidadesConsumidoras)
                    {
                        // verifica se unidade consumidora esta neste contrato
                        double PercentualCarga_Valor = PercentualCargaUnidadeConsumidora(contrato, unid.IDMedicao);

                        // totaliza no consumo
                        prov.Consumo_Consolidado += unid.consumoMensal.Consumo_Consolidado * (PercentualCarga_Valor / 100.0);
                        prov.Consumo_Estimado += unid.consumoMensal.Consumo_Estimado * (PercentualCarga_Valor / 100.0);
                        //prov.Consumo_Total_Bruto += unid.consumoMensal.Consumo_Total_Bruto * (PercentualCarga_Valor / 100.0);
                    }
                }
                else
                {
                    // não tem percentual de carga, portanto uso o total das unidades consumidoras
                    prov.Consumo_Consolidado = provisionamento.Consumo_Consolidado;
                    prov.Consumo_Estimado = provisionamento.Consumo_Estimado;
                    //prov.Consumo_Total_Bruto = provisionamento.Consumo_Total_Bruto;
                }

                // consumo total bruto
                prov.Consumo_Total_Bruto = prov.Consumo_Consolidado + prov.Consumo_Estimado;

                // Perdas
                prov.Perdas = contrato.Perdas;

                // Previsão de Consumo
                prov.Previsao_Consumo = prov.Consumo_Total_Bruto * (1.0 + (prov.Perdas / 100.0));

                // PROINFA
                Calcula_PROINFA(ref prov, contrato, unidadesConsumidoras);

                // Necessidade de contratação
                prov.Necessidade_Contratacao = prov.Previsao_Consumo;

                if (prov.Proinfa_Abate)
                {
                    prov.Necessidade_Contratacao -= prov.PROINFA;
                }
                else
                {
                    prov.PROINFA = 0.0;
                }

                // verifica se é o segundo contrato em diante e se deve considerar
                if (contaContrato > 0 && contrato.Considera == 1)
                {
                    // subtrai a somatória da necessidade de contratação dos contratos anteriores
                    prov.Necessidade_Contratacao -= Necessidade_Contratacao_Total;
                }

                // Contrato da Sazo Mensal
                ContratosCCEE_SazonalizacaoMetodos sazonalizacaoMetodos = new ContratosCCEE_SazonalizacaoMetodos();
                prov.Contrato = sazonalizacaoMetodos.SazonalizacaoMes(contrato.IDContratoCCEE, prov.Data);

                // Take Mínimo
                prov.Take_Minimo = prov.Contrato * (contrato.Flexibilidade_Minima / 100.0);

                // Take Máximo
                prov.Take_Maximo = prov.Contrato * (contrato.Flexibilidade_Maxima / 100.0);

                // Volume Contratado, Total Contrato e Resultado
                prov.Volume_Contratado = prov.Necessidade_Contratacao;
                prov.Total_Contrato = prov.Necessidade_Contratacao;
                prov.Resultado = 0.0;

                // verifica se menor que a flexibilidade mínima
                if (prov.Necessidade_Contratacao < prov.Take_Minimo)
                {
                    prov.Volume_Contratado = prov.Take_Minimo;
                    prov.Total_Contrato = prov.Take_Minimo + prov.PROINFA;
                    prov.Resultado = prov.Take_Minimo - prov.Necessidade_Contratacao;
                }

                // verifica se maior que a flexibilidade máxima
                if (prov.Necessidade_Contratacao > prov.Take_Maximo)
                {
                    prov.Volume_Contratado = prov.Take_Maximo;
                    prov.Total_Contrato = prov.Take_Maximo + prov.PROINFA;
                    prov.Resultado = prov.Take_Maximo - prov.Necessidade_Contratacao;
                }

                // verifica se deve considerar
                if (contrato.Considera == 1)
                {
                    // somatório da necessidade de contratação dos contratos
                    Necessidade_Contratacao_Total += prov.Total_Contrato;
                }

                // Total de Contratos
                provisionamento.Volume_Contratado += prov.Volume_Contratado;
                provisionamento.Total_Contrato += prov.Total_Contrato;

                // unidades consumidoras para uso no rateio
                foreach (Provisionamento_UnidadesConsumidoras unid in unidadesConsumidoras)
                {
                    Provisionamento_UnidadesConsumidoras u = new Provisionamento_UnidadesConsumidoras();
                    u.IDUnidadeConsumidora = unid.IDUnidadeConsumidora;
                    u.IDCliente = unid.IDCliente;
                    u.IDEmpresa = unid.IDEmpresa;
                    u.IDMedicao = unid.IDMedicao;
                    u.IDGateway = unid.IDGateway;

                    u.RazaoSocial = unid.RazaoSocial;
                    u.SiglaCCEE = unid.SiglaCCEE;
                    u.CNPJ = unid.CNPJ;
                    u.CNPJ_RazaoSocial = unid.CNPJ_RazaoSocial;

                    u.PontoMedicao = unid.PontoMedicao;
                    u.IDSubSistema = unid.IDSubSistema;
                    u.IDContratoMedicao = unid.IDContratoMedicao;
                    u.IDEstruturaTarifaria = unid.IDEstruturaTarifaria;

                    u.IDComercializadora = unid.IDComercializadora;

                    // consumo consolidado e estimado
                    //u.consumoMensal = unid.consumoMensal;

                    // verifica se possui percentual de carga
                    if (contrato.PercentualCarga_Possui)
                    {
                        // verifica se unidade consumidora esta neste contrato
                        double PercentualCarga_Valor = PercentualCargaUnidadeConsumidora(contrato, unid.IDMedicao);

                        // consumos
                        u.consumoMensal.Consumo_Consolidado = unid.consumoMensal.Consumo_Consolidado * (PercentualCarga_Valor / 100.0);
                        u.consumoMensal.Consumo_Estimado = unid.consumoMensal.Consumo_Estimado * (PercentualCarga_Valor / 100.0);
                        //u.consumoMensal.Consumo_Total_Bruto = unid.consumoMensal.Consumo_Total_Bruto * (PercentualCarga_Valor / 100.0);
                    }
                    else
                    {
                        // não tem percentual de carga, portanto uso o total das unidades consumidoras
                        u.consumoMensal.Consumo_Consolidado = unid.consumoMensal.Consumo_Consolidado;
                        u.consumoMensal.Consumo_Estimado = unid.consumoMensal.Consumo_Estimado;
                        //u.consumoMensal.Consumo_Total_Bruto = unid.consumoMensal.Consumo_Total_Bruto;
                    }

                    // consumo total bruto
                    u.consumoMensal.Consumo_Total_Bruto = u.consumoMensal.Consumo_Consolidado + u.consumoMensal.Consumo_Estimado;

                    // rateio
                    u.Consumo_Porc = unid.Consumo_Porc;
                    u.Sobra_Deficit = unid.Sobra_Deficit;
                    u.Volume_Faturado = unid.Volume_Faturado;
                    u.Preco_Energia = unid.Preco_Energia;
                    u.PLD = unid.PLD;
                    u.TotalFaturamento_LP = unid.TotalFaturamento_LP;
                    u.TotalFaturamento_CP = unid.TotalFaturamento_CP;
                    u.TotalCusto_Energia = unid.TotalCusto_Energia;

                    // fatura uso de rede (distribuidora)
                    u.Fatura_Distribuidora_Valor = unid.Fatura_Distribuidora_Valor;

                    // fatura curto prazo (comercializadora)
                    u.Fatura_CurtoPrazo_Quantidade = unid.Fatura_CurtoPrazo_Quantidade;
                    u.Fatura_CurtoPrazo_PLD = unid.Fatura_CurtoPrazo_PLD;
                    u.Fatura_CurtoPrazo_Valor = unid.Fatura_CurtoPrazo_Valor;

                    // fatura longo prazo (comercializadora)
                    u.Fatura_LongoPrazo_Quantidade = unid.Fatura_LongoPrazo_Quantidade;
                    u.Fatura_LongoPrazo_PrecoEnergia = unid.Fatura_LongoPrazo_PrecoEnergia;
                    u.Fatura_LongoPrazo_Valor = unid.Fatura_LongoPrazo_Valor;

                    prov.unidadesConsumidoras.Add(u);
                }

                // insere provisionamento do contrato no provisionamento principal
                provisionamento.por_contrato.Add(prov);

                // próximo contrato
                contaContrato++;
            }


            // Resultado
            // Caso negativo = Consumo ACIMA do Volume Contratado
            // Caso positivo = Consumo ABAIXO do Volume Contratado
            // Caso zero = Consumo DENTRO do Volume Contratado
            provisionamento.Resultado = provisionamento.Total_Contrato - provisionamento.Previsao_Consumo;



            //
            // Rateio
            //
            Calcula_Rateio_Tipo3(ref provisionamento, contratosEnergia, ref unidadesConsumidoras);


            //
            // Fatura
            //
            Calcula_Fatura_Tipo3(provisionamento, ref unidadesConsumidoras);


            //
            // Resultado
            //

            ViewBag.PrevisaoConsumo = provisionamento.Previsao_Consumo;
            ViewBag.Contrato = provisionamento.Total_Contrato;

            double MaxGrafico = (provisionamento.Previsao_Consumo > provisionamento.Total_Contrato) ? provisionamento.Previsao_Consumo : provisionamento.Total_Contrato;
            MaxGrafico *= 1.1;
            ViewBag.MaxGrafico = MaxGrafico;

            return;
        }

        // Calcula Rateio
        private void Calcula_Rateio_Tipo3(ref Provisionamento_Tipo3 provisionamento, List<ContratosCCEEDominio> contratosEnergia, ref List<Provisionamento_UnidadesConsumidoras> unidadesConsumidoras)
        {
            //
            // PLD
            //
            HistoricoPLDMetodos pldMetodos = new HistoricoPLDMetodos();
            HistoricoPLDDominio pld = pldMetodos.ListarPorMes(provisionamento.Data);


            //
            // Provisionamentos por contrato
            //

            // provisionamento
            provisionamento.TotalFaturamento_LP = 0.0;
            provisionamento.TotalFaturamento_CP = 0.0;
            provisionamento.TotalCusto_Energia = 0.0;

            double PLD = 0.0;

            // percorre provisionamentos por contrato
            foreach (Provisionamento_Tipo1_Tipo2 prov in provisionamento.por_contrato)
            {
                // le contrato
                ContratosCCEEDominio contrato_energia = contratosEnergia.Find(c => c.IDContratoCCEE == prov.IDContratoCCEE);

                if (contrato_energia == null)
                {
                    continue;
                }

                // provisionamento por contrato
                prov.TotalFaturamento_LP = 0.0;
                prov.TotalFaturamento_CP = 0.0;
                prov.TotalCusto_Energia = 0.0;

                // percorre unidades consumidoras
                foreach (Provisionamento_UnidadesConsumidoras unidConsumidora in prov.unidadesConsumidoras)
                {
                    // porcentagem do consumo total
                    unidConsumidora.Consumo_Porc = 0.0;

                    if (prov.Consumo_Total_Bruto != 0.0)
                    {
                        unidConsumidora.Consumo_Porc = (unidConsumidora.consumoMensal.Consumo_Total_Bruto / prov.Consumo_Total_Bruto) * 100.0;
                    }

                    // Total Contrato
                    unidConsumidora.Volume_Faturado = prov.Volume_Contratado * (unidConsumidora.Consumo_Porc / 100.0);

                    // Preço Energia
                    unidConsumidora.Preco_Energia = contrato_energia.Reajuste_Preco;

                    // Sobra / Deficit
                    unidConsumidora.Sobra_Deficit = prov.Resultado * (unidConsumidora.Consumo_Porc / 100.0);

                    // PLD
                    unidConsumidora.PLD = 0.0;

                    switch (unidConsumidora.IDSubSistema)
                    {
                        case 0:     // Sudeste/Centro-Oeste
                            unidConsumidora.PLD = pld.SE_CO;
                            break;

                        case 1:     // Sul
                            unidConsumidora.PLD = pld.S;
                            break;

                        case 2:     // Nordeste
                            unidConsumidora.PLD = pld.NE;
                            break;

                        case 3:     // Norte
                            unidConsumidora.PLD = pld.N;
                            break;

                        case 4:     // Fora do SIN
                            unidConsumidora.PLD = pld.Fora_SIN;
                            break;
                    }

                    // PLD
                    PLD = unidConsumidora.PLD;

                    // Total Faturamento LP
                    unidConsumidora.TotalFaturamento_LP = unidConsumidora.Volume_Faturado * unidConsumidora.Preco_Energia;

                    // Total Faturamento CP
                    unidConsumidora.TotalFaturamento_CP = Math.Abs(unidConsumidora.Sobra_Deficit) * unidConsumidora.PLD;

                    // Total Custo Energia
                    unidConsumidora.TotalCusto_Energia = 0.0;

                    // caso tiver sobras, subtrair CP do LP
                    if (unidConsumidora.Sobra_Deficit > 0.0)
                    {
                        unidConsumidora.TotalCusto_Energia = unidConsumidora.TotalFaturamento_LP - unidConsumidora.TotalFaturamento_CP;
                    }

                    // caso tiver deficit, somar CP com LP
                    if (unidConsumidora.Sobra_Deficit < 0.0)
                    {
                        unidConsumidora.TotalCusto_Energia = unidConsumidora.TotalFaturamento_LP + unidConsumidora.TotalFaturamento_CP;
                    }

                    // provisionamento por contrato
                    prov.TotalFaturamento_LP += unidConsumidora.TotalFaturamento_LP;
                    prov.TotalFaturamento_CP += unidConsumidora.TotalFaturamento_CP;
                    prov.TotalCusto_Energia += unidConsumidora.TotalCusto_Energia;

                    // provisionamento
                    provisionamento.TotalFaturamento_LP += unidConsumidora.TotalFaturamento_LP;
                    provisionamento.TotalFaturamento_CP += unidConsumidora.TotalFaturamento_CP;
                    provisionamento.TotalCusto_Energia += unidConsumidora.TotalCusto_Energia;

                    // encontra unidade consumidora
                    Provisionamento_UnidadesConsumidoras unid = unidadesConsumidoras.Find(u => u.IDUnidadeConsumidora == unidConsumidora.IDUnidadeConsumidora);

                    if (unid != null)
                    {
                        unid.TotalCusto_Energia += unidConsumidora.TotalCusto_Energia;
                    }
                }
            }

            // verifica se tem apenas uma unidade consumidora para recalcular o total 
            if (unidadesConsumidoras.Count == 1)
            {
                // provisionamento
                provisionamento.TotalFaturamento_CP = provisionamento.Resultado * PLD;
                provisionamento.TotalCusto_Energia = provisionamento.TotalFaturamento_LP + provisionamento.TotalFaturamento_CP;

                // caso tiver sobras, subtrair CP do LP
                if (provisionamento.Resultado > 0.0)
                {
                    provisionamento.TotalCusto_Energia = provisionamento.TotalFaturamento_LP - provisionamento.TotalFaturamento_CP;
                }

                // caso tiver deficit, somar CP com LP
                if (provisionamento.Resultado < 0.0)
                {
                    provisionamento.TotalCusto_Energia = provisionamento.TotalFaturamento_LP + provisionamento.TotalFaturamento_CP;
                }
            }

            return;
        }

        // Calcula Fatura
        private void Calcula_Fatura_Tipo3(Provisionamento_Tipo3 provisionamento, ref List<Provisionamento_UnidadesConsumidoras> unidadesConsumidoras)
        {
            // PLD
            HistoricoPLDMetodos pldMetodos = new HistoricoPLDMetodos();
            HistoricoPLDDominio pld = pldMetodos.ListarPorMes(provisionamento.Data);

            // data inicio e fim do mês atual
            DATAHORA dh_ini = new DATAHORA();
            DateTime dataini = new DateTime(provisionamento.Data.Year, provisionamento.Data.Month, 1, 0, 0, 0);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, dataini);

            DATAHORA dh_fim = new DATAHORA();
            DateTime datafim = dataini.AddMonths(1);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, datafim);


            // percorre unidades consumidoras
            foreach (Provisionamento_UnidadesConsumidoras unidConsumidora in unidadesConsumidoras)
            {
                //
                // Fatura uso de rede (distribuidora)
                //

                // simulação
                int IDSimulacaoCenario = 0;

                CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
                RESULT_ENERGIA_FATURA faturaDLL = new RESULT_ENERGIA_FATURA();
                RESULT_ENERGIA_FATURA faturaDLL_sim = new RESULT_ENERGIA_FATURA();

                // preenche solicitacao
                config_interface.sweb.id_cliente = unidConsumidora.IDCliente;
                config_interface.sweb.id_medicao = unidConsumidora.IDMedicao;
                config_interface.sweb.id_gateway = unidConsumidora.IDGateway;

                // calcula valores fatura projetada (mesmo sendo consolidado) (sem simulação)
                int retorno = SmCalcDB_Energia_Fatura((char)0, ref config_interface, IDSimulacaoCenario, (char)unidConsumidora.IDContratoMedicao, (char)unidConsumidora.IDEstruturaTarifaria, (char)1, ref dh_ini, ref dh_fim, ref faturaDLL, ref faturaDLL_sim);

                // total da fatura
                unidConsumidora.Fatura_Distribuidora_Valor = faturaDLL.total;


                //
                // Fatura Curto Prazo (comercializadora)
                //

                // fatura curto prazo
                unidConsumidora.Fatura_CurtoPrazo_PLD = unidConsumidora.PLD;
                unidConsumidora.Fatura_CurtoPrazo_Quantidade = unidConsumidora.Sobra_Deficit;
                unidConsumidora.Fatura_CurtoPrazo_Valor = Math.Abs(unidConsumidora.Fatura_CurtoPrazo_Quantidade) * unidConsumidora.Fatura_CurtoPrazo_PLD;

                //
                // Fatura Longo Prazo (comercializadora)
                //

                // fatura longo prazo
                unidConsumidora.Fatura_LongoPrazo_Quantidade = unidConsumidora.Volume_Faturado;
                unidConsumidora.Fatura_LongoPrazo_PrecoEnergia = unidConsumidora.Preco_Energia;
                unidConsumidora.Fatura_LongoPrazo_Valor = unidConsumidora.Fatura_LongoPrazo_Quantidade * unidConsumidora.Fatura_LongoPrazo_PrecoEnergia;
            }

            return;
        }


        private double PercentualCargaUnidadeConsumidora(ContratosCCEEDominio contrato_energia, int IDMedicao)
        {
            double PercentualCarga_Valor = 0.0;


            // verifica se a unidade garantidora é a medição desejada
            if (contrato_energia.IDMedicao == IDMedicao)
            {
                // copia percentual de carga
                PercentualCarga_Valor = contrato_energia.PercentualCarga_Valor;
            }
            else
            {
                // unidades atendidas pelo contrato
                ContratosCCEE_UnidadesAtendidasMetodos unidadesAtendidasMetodos = new ContratosCCEE_UnidadesAtendidasMetodos();
                List<ContratosCCEE_UnidadesAtendidasDominio> unidadesAtendidas = unidadesAtendidasMetodos.ListarPorId(contrato_energia.IDContratoCCEE);

                if (unidadesAtendidas != null)
                {
                    foreach (ContratosCCEE_UnidadesAtendidasDominio unidadeAtendida in unidadesAtendidas)
                    {
                        // unidade consumidora
                        Provisionamento_UnidadesConsumidoras unidConsumidora = new Provisionamento_UnidadesConsumidoras();

                        // preenche info da unidade consumidora
                        if (InfoUnidadeConsumidora(ref unidConsumidora, unidadeAtendida.IDEmpresa))
                        {
                            // verifica se a unidade atendida é a medição desejada
                            if (unidConsumidora.IDMedicao == IDMedicao)
                            {
                                // percentual de carga
                                PercentualCarga_Valor = unidadeAtendida.PercentualCarga_Valor;

                                // achou
                                break;
                            }
                        }
                    }
                }
            }

            // retorna valor
            return (PercentualCarga_Valor);
        }
    }
}