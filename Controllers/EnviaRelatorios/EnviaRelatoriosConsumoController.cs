﻿using System;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{

    public partial class EnviaRelatoriosController
    {
        // Consumo Diario
        private int Consumo_Diario(CliGrupoUnidMedicoesDominio medicao, DateTime dataRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_DIARIO relatorio = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise = new RELAT_DIARIO_ANALISE();

            RELAT_DIARIO relatorio_sim = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise_sim = new RELAT_DIARIO_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = medicao.IDMedicao;

            // simulacao
            int TipoSimula = 0;

            // converte data hora
            DATAHORA data_hora = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, dataRelat);

            // calcula valores
            retorno = SmCalcDB_Energia_RelatDiario((char)0, ref config_interface, (char)2, (char)TipoSimula, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_DIARIO(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            ViewBag.Retorno = retorno;

            // verifica se erro
            if (retorno != 0)
            {
                // erro
                // -2 - erro : tipo de interface indefinida
                // -1 - erro : tipo de relatorio indefinido
                // 0  - ok e com dados
                // 1  - erro : nao foi possivel abrir o banco de dados
                // 2  - erro : nao existem dados
                // 3  - erro : leitura da configuracao da medicao
                // 4  - erro : leitura das tarifas de energia
                return (retorno);
            }

            // grafico
            var Consumo = new double[26];
            var Periodo = new int[26];
            var Dias = new string[26];
            var DatasN = new DateTime[26];
            var Horas = new string[26];

            double Consumo_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Horas[i] = strData.ToString("HH:mm");

                // proxima hora
                strData = strData.AddHours(1);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    Consumo[i] = relatorio.registro[0].valor1;
                    Periodo[i] = relatorio.registro[0].periodo;
                }

                // verifica se ultima barra
                if (i == 25)
                {
                    // zera
                    Consumo[i] = relatorio.registro[23].valor1;
                    Periodo[i] = relatorio.registro[23].periodo;
                }

                if (i >= 1 && i <= 24)
                {
                    // copia
                    j = i - 1;

                    Consumo[i] = relatorio.registro[j].valor1;
                    Periodo[i] = relatorio.registro[j].periodo;

                    // verifica se sem registro
                    if (relatorio.registro[j].periodo == 3)
                    {
                        Consumo[i] = 0.0;
                    }

                    // verifica consumo maximo
                    if (Consumo[i] > Consumo_max_grafico)
                        Consumo_max_grafico = Consumo[i];
                }
            }

            Consumo_max_grafico = Consumo_max_grafico * 1.1;

            if (Consumo_max_grafico == 0.0)
            {
                Consumo_max_grafico = 10.0;
            }

            ViewBag.ConsMaxGrafico = Consumo_max_grafico;

            ViewBag.Consumo = Consumo;
            ViewBag.Periodo = Periodo;
            ViewBag.Dias = Dias;
            ViewBag.DatasN = DatasN;
            ViewBag.Horas = Horas;

            // nome do relatorio
            ViewBag.NomeRelat = string.Format("Consumo");
            ViewBag.PeriodoRelat = string.Format("Diário");

            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", dataRelat);
            ViewBag.DataAtualN = dataRelat;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", dataRelat);

            // consumo
            double ConsP = analise.analise_valor1.consumo[0];
            double ConsFPI = analise.analise_valor1.consumo[1];
            double ConsFPC = analise.analise_valor1.consumo[2];
            double ConsTotal = analise.analise_valor1.consumo[0] + analise.analise_valor1.consumo[1] + analise.analise_valor1.consumo[2];

            if (ConsP < 100.0)
            {
                ViewBag.ConsP = string.Format("{0:#,##0.0}", ConsP);
            }
            else
            {
                ViewBag.ConsP = string.Format("{0:#,##0}", ConsP);
            }
            ViewBag.ConsPN = ConsP;

            if (ConsFPI < 100.0)
            {
                ViewBag.ConsFPI = string.Format("{0:#,##0.0}", ConsFPI);
            }
            else
            {
                ViewBag.ConsFPI = string.Format("{0:#,##0}", ConsFPI);
            }
            ViewBag.ConsFPIN = ConsFPI;

            if (ConsFPC < 100.0)
            {
                ViewBag.ConsFPC = string.Format("{0:#,##0.0}", ConsFPC);
            }
            else
            {
                ViewBag.ConsFPC = string.Format("{0:#,##0}", ConsFPC);
            }
            ViewBag.ConsFPCN = ConsFPC;

            if (ConsTotal < 100.0)
            {
                ViewBag.ConsTotal = string.Format("{0:#,##0.0}", ConsTotal);
            }
            else
            {
                ViewBag.ConsTotal = string.Format("{0:#,##0}", ConsTotal);
            }
            ViewBag.ConsTotalN = ConsTotal;

            return(0);
        }

        // Consumo Semanal
        private int Consumo_Semanal(CliGrupoUnidMedicoesDominio medicao, DateTime dataRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_SEMANAL relatorio = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise = new RELAT_SEMANAL_ANALISE();

            RELAT_SEMANAL relatorio_sim = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise_sim = new RELAT_SEMANAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = medicao.IDMedicao;

            // simulacao
            int TipoSimula = 0;

            // converte data hora
            DATAHORA data_hora = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, dataRelat);

            // calcula valores
            retorno = SmCalcDB_Energia_RelatSemanal((char)0, ref config_interface, (char)2, (char)TipoSimula, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_SEMANAL(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            ViewBag.Retorno = retorno;

            // verifica se erro
            if (retorno != 0)
            {
                // erro
                // -2 - erro : tipo de interface indefinida
                // -1 - erro : tipo de relatorio indefinido
                // 0  - ok e com dados
                // 1  - erro : nao foi possivel abrir o banco de dados
                // 2  - erro : nao existem dados
                // 3  - erro : leitura da configuracao da medicao
                // 4  - erro : leitura das tarifas de energia
                return (retorno);
            }

            // maior consumo
            double maior_consumo = analise.analise_valor.consumo[0] + analise.analise_valor.consumo[1];

            // grafico
            var ConsumoP = new double[9];
            var ConsumoFPI = new double[9];
            var ConsumoFPC = new double[9];
            var Datas = new string[9];
            var DatasN = new DateTime[9];
            var Dias = new string[9];

            DateTime inicio = new DateTime();
            DateTime fim = new DateTime();

            double Consumo_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 9; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = string.Format("{0:d}", strData);

                // guarda inicio
                if (i == 1)
                {
                    inicio = strData;
                }

                // guarda fim
                if (i == 7)
                {
                    fim = strData;
                }

                // proximo dias
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    ConsumoP[i] = relatorio.registro[0].valor[0];
                    ConsumoFPI[i] = relatorio.registro[0].valor[1];
                    ConsumoFPC[i] = relatorio.registro[0].valor[2];
                }

                if (i == 8)
                {
                    // zera
                    ConsumoP[i] = relatorio.registro[6].valor[0];
                    ConsumoFPI[i] = relatorio.registro[6].valor[1];
                    ConsumoFPC[i] = relatorio.registro[6].valor[2];
                }

                if (i >= 1 && i <= 7)
                {
                    // copia
                    j = i - 1;

                    ConsumoP[i] = relatorio.registro[j].valor[0];
                    ConsumoFPI[i] = relatorio.registro[j].valor[1];
                    ConsumoFPC[i] = relatorio.registro[j].valor[2];

                    // verifica consumo maximo
                    if ((ConsumoP[i] + ConsumoFPI[i] + ConsumoFPC[i]) > Consumo_max_grafico)
                        Consumo_max_grafico = (ConsumoP[i] + ConsumoFPI[i] + ConsumoFPC[i]);
                }
            }

            Consumo_max_grafico = Consumo_max_grafico * 1.1;

            if (Consumo_max_grafico == 0.0)
            {
                Consumo_max_grafico = 10.0;
            }

            ViewBag.ConsMaxGrafico = Consumo_max_grafico;

            ViewBag.ConsumoP = ConsumoP;
            ViewBag.ConsumoFPI = ConsumoFPI;
            ViewBag.ConsumoFPC = ConsumoFPC;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            // nome do relatorio
            ViewBag.NomeRelat = string.Format("Consumo");
            ViewBag.PeriodoRelat = string.Format("Semanal");

            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", dataRelat);
            ViewBag.DataAtualN = inicio;
            ViewBag.DataFinalN = fim;
            ViewBag.DataTextoAtual = string.Format("{0:d} - {1:d} ", inicio, fim);

            // consumo
            double ConsP = analise.analise_valor.consumo[0];
            double ConsFPI = analise.analise_valor.consumo[1];
            double ConsFPC = analise.analise_valor.consumo[2];
            double ConsTotal = (analise.analise_valor.consumo[0] + analise.analise_valor.consumo[1] + analise.analise_valor.consumo[2]);

            if (ConsP < 100.0)
            {
                ViewBag.ConsP = string.Format("{0:#,##0.0}", ConsP);
            }
            else
            {
                ViewBag.ConsP = string.Format("{0:#,##0}", ConsP);
            }
            ViewBag.ConsPN = ConsP;

            if (ConsFPI < 100.0)
            {
                ViewBag.ConsFPI = string.Format("{0:#,##0.0}", ConsFPI);
            }
            else
            {
                ViewBag.ConsFPI = string.Format("{0:#,##0}", ConsFPI);
            }
            ViewBag.ConsFPIN = ConsFPI;

            if (ConsFPC < 100.0)
            {
                ViewBag.ConsFPC = string.Format("{0:#,##0.0}", ConsFPC);
            }
            else
            {
                ViewBag.ConsFPC = string.Format("{0:#,##0}", ConsFPC);
            }
            ViewBag.ConsFPCN = ConsFPC;

            if (ConsTotal < 100.0)
            {
                ViewBag.ConsTotal = string.Format("{0:#,##0.0}", ConsTotal);
            }
            else
            {
                ViewBag.ConsTotal = string.Format("{0:#,##0}", ConsTotal);
            }
            ViewBag.ConsTotalN = ConsTotal;

            return(0);
        }

        // Consumo Mensal
        private int Consumo_Mensal(CliGrupoUnidMedicoesDominio medicao, DateTime dataRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_MENSAL relatorio = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise = new RELAT_MENSAL_ANALISE();

            RELAT_MENSAL relatorio_sim = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise_sim = new RELAT_MENSAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = medicao.IDMedicao;

            // simulacao
            int TipoSimula = 0;

            // converte data hora
            DATAHORA data_hora = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, dataRelat);

            // calcula valores
            retorno = SmCalcDB_Energia_RelatMensal((char)0, ref config_interface, (char)2, (char)TipoSimula, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_MENSAL(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // numero de dias do mes
            int NumDiasMes = relatorio.num_dias;

            ViewBag.Retorno = retorno;

            // verifica se erro
            if (retorno != 0)
            {
                // erro
                // -2 - erro : tipo de interface indefinida
                // -1 - erro : tipo de relatorio indefinido
                // 0  - ok e com dados
                // 1  - erro : nao foi possivel abrir o banco de dados
                // 2  - erro : nao existem dados
                // 3  - erro : leitura da configuracao da medicao
                // 4  - erro : leitura das tarifas de energia
                return (retorno);
            }

            // maior consumo
            double maior_consumo = analise.analise_valor.consumo[0] + analise.analise_valor.consumo[1];

            // grafico
            var ConsumoP = new double[42];
            var ConsumoFPI = new double[42];
            var ConsumoFPC = new double[42];
            var Datas = new string[42];
            var DatasN = new DateTime[42];
            var Dias = new string[42];

            double Consumo_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = strData.ToString("dd/MM");

                // proximo dias
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    ConsumoP[i] = relatorio.registro[0].valor[0];
                    ConsumoFPI[i] = relatorio.registro[0].valor[1];
                    ConsumoFPC[i] = relatorio.registro[0].valor[2];
                }

                if (i == (NumDiasMes + 1))
                {
                    // zera
                    ConsumoP[i] = relatorio.registro[NumDiasMes - 1].valor[0];
                    ConsumoFPI[i] = relatorio.registro[NumDiasMes - 1].valor[1];
                    ConsumoFPC[i] = relatorio.registro[NumDiasMes - 1].valor[2];
                }

                if (i >= 1 && i <= NumDiasMes)
                {
                    // copia
                    j = i - 1;

                    ConsumoP[i] = relatorio.registro[j].valor[0];
                    ConsumoFPI[i] = relatorio.registro[j].valor[1];
                    ConsumoFPC[i] = relatorio.registro[j].valor[2];

                    // verifica consumo maximo
                    if ((ConsumoP[i] + ConsumoFPI[i] + ConsumoFPC[i]) > Consumo_max_grafico)
                        Consumo_max_grafico = (ConsumoP[i] + ConsumoFPI[i] + ConsumoFPC[i]);
                }
            }

            Consumo_max_grafico = Consumo_max_grafico * 1.1;

            if (Consumo_max_grafico == 0.0)
            {
                Consumo_max_grafico = 10.0;
            }

            ViewBag.ConsMaxGrafico = Consumo_max_grafico;

            ViewBag.ConsumoP = ConsumoP;
            ViewBag.ConsumoFPI = ConsumoFPI;
            ViewBag.ConsumoFPC = ConsumoFPC;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            // nome do relatorio
            ViewBag.NomeRelat = string.Format("Consumo");
            ViewBag.PeriodoRelat = string.Format("Mensal");

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", dataRelat);
            ViewBag.DataAtualN = dataRelat;
            ViewBag.DataTextoAtual = string.Format("{0:y}", dataRelat);

            ViewBag.NumDiasMes = NumDiasMes;

            // consumo
            double ConsP = analise.analise_valor.consumo[0];
            double ConsFPI = analise.analise_valor.consumo[1];
            double ConsFPC = analise.analise_valor.consumo[2];
            double ConsTotal = (analise.analise_valor.consumo[0] + analise.analise_valor.consumo[1] + analise.analise_valor.consumo[2]);

            if (ConsP < 100.0)
            {
                ViewBag.ConsP = string.Format("{0:#,##0.0}", ConsP);
            }
            else
            {
                ViewBag.ConsP = string.Format("{0:#,##0}", ConsP);
            }
            ViewBag.ConsPN = ConsP;

            if (ConsFPI < 100.0)
            {
                ViewBag.ConsFPI = string.Format("{0:#,##0.0}", ConsFPI);
            }
            else
            {
                ViewBag.ConsFPI = string.Format("{0:#,##0}", ConsFPI);
            }
            ViewBag.ConsFPIN = ConsFPI;

            if (ConsFPC < 100.0)
            {
                ViewBag.ConsFPC = string.Format("{0:#,##0.0}", ConsFPC);
            }
            else
            {
                ViewBag.ConsFPC = string.Format("{0:#,##0}", ConsFPC);
            }
            ViewBag.ConsFPCN = ConsFPC;

            if (ConsTotal < 100.0)
            {
                ViewBag.ConsTotal = string.Format("{0:#,##0.0}", ConsTotal);
            }
            else
            {
                ViewBag.ConsTotal = string.Format("{0:#,##0}", ConsTotal);
            }
            ViewBag.ConsTotalN = ConsTotal;

            return(0);
        }
    }
}
