﻿using System;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{

    public partial class EnviaRelatoriosController
    {
        // Fator de Potência Diario
        private int FatPot_Diario(CliGrupoUnidMedicoesDominio medicao, DateTime dataRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_DIARIO relatorio = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise = new RELAT_DIARIO_ANALISE();

            RELAT_DIARIO relatorio_sim = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise_sim = new RELAT_DIARIO_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = medicao.IDMedicao;

            // simulacao
            int TipoSimula = 0;

            // converte data hora
            DATAHORA data_hora = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, dataRelat);

            // calcula valores
            retorno = SmCalcDB_Energia_RelatDiario((char)0, ref config_interface, (char)3, (char)TipoSimula, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);

            ViewBag.Retorno = retorno;

            // verifica se erro
            if (retorno != 0)
            {
                // erro
                // -2 - erro : tipo de interface indefinida
                // -1 - erro : tipo de relatorio indefinido
                // 0  - ok e com dados
                // 1  - erro : nao foi possivel abrir o banco de dados
                // 2  - erro : nao existem dados
                // 3  - erro : leitura da configuracao da medicao
                // 4  - erro : leitura das tarifas de energia
                return (retorno);
            }

            // grafico
            var FatPot = new double[26];
            var Periodo = new int[26];
            var Dias = new string[26];
            var DatasN = new DateTime[26];
            var Horas = new string[26];

            double FatPot_min = 1.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Horas[i] = strData.ToString("HH:mm");

                // proxima hora
                strData = strData.AddHours(1);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    FatPot[i] = relatorio.registro[0].valor1;
                    Periodo[i] = relatorio.registro[0].periodo;
                }

                // verifica se ultima barra
                if (i == 25)
                {
                    // zera
                    FatPot[i] = relatorio.registro[23].valor1;
                    Periodo[i] = relatorio.registro[23].periodo;
                }

                if (i >= 1 && i <= 24)
                {
                    // copia
                    j = i - 1;

                    FatPot[i] = relatorio.registro[j].valor1;
                    Periodo[i] = relatorio.registro[j].periodo;

                    // verifica se sem registro
                    if (relatorio.registro[j].periodo == 3)
                    {
                        FatPot[i] = 1.0;
                    }

                    // verifica fatpot minimo
                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPot[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPot[i]);
                }
            }

            if (FatPot_min > 0.92)
                FatPot_min = 0.92;

            FatPot_min = FatPot_min - (FatPot_min * 0.01);

            ViewBag.FatPotMin = FatPot_min;

            ViewBag.FatPot = FatPot;
            ViewBag.Periodo = Periodo;
            ViewBag.Dias = Dias;
            ViewBag.DatasN = DatasN;
            ViewBag.Horas = Horas;

            // nome do relatorio
            ViewBag.NomeRelat = string.Format("Fator de Potência");
            ViewBag.PeriodoRelat = string.Format("Diário");

            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", dataRelat);
            ViewBag.DataAtualN = dataRelat;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", dataRelat);

            // fator de potencia Mais Indutivo
            ViewBag.FatPot_MaisIndFPC = string.Format("{0:0.000}", analise.analise_valor1.maximo[2]);
            DateTime FatPot_MaisIndFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max[2]);
            if (FatPot_MaisIndFPC_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndFPC_DataHora = string.Format("{0:t}", FatPot_MaisIndFPC_DataHora);
            else
                ViewBag.FatPot_MaisIndFPC_DataHora = "--:--";
            ViewBag.FatPot_MaisIndFPC_DataHoraN = FatPot_MaisIndFPC_DataHora;

            ViewBag.FatPot_MaisIndFPI = string.Format("{0:0.000}", analise.analise_valor1.maximo[1]);
            DateTime FatPot_MaisIndFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max[1]);
            if (FatPot_MaisIndFPI_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndFPI_DataHora = string.Format("{0:t}", FatPot_MaisIndFPI_DataHora);
            else
                ViewBag.FatPot_MaisIndFPI_DataHora = "--:--";
            ViewBag.FatPot_MaisIndFPI_DataHoraN = FatPot_MaisIndFPI_DataHora;

            ViewBag.FatPot_MaisIndP = string.Format("{0:0.000}", analise.analise_valor1.maximo[0]);
            DateTime FatPot_MaisIndP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max[0]);
            if (FatPot_MaisIndP_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndP_DataHora = string.Format("{0:t}", FatPot_MaisIndP_DataHora);
            else
                ViewBag.FatPot_MaisIndP_DataHora = "--:--";
            ViewBag.FatPot_MaisIndP_DataHoraN = FatPot_MaisIndP_DataHora;

            // fator de potencia Mais Capacitivo
            ViewBag.FatPot_MaisCapFPC = string.Format("{0:0.000}", analise.analise_valor1.minimo[2]);
            DateTime FatPot_MaisCapFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_min[2]);
            if (FatPot_MaisCapFPC_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapFPC_DataHora = string.Format("{0:t}", FatPot_MaisCapFPC_DataHora);
            else
                ViewBag.FatPot_MaisCapFPC_DataHora = "--:--";
            ViewBag.FatPot_MaisCapFPC_DataHoraN = FatPot_MaisCapFPC_DataHora;

            ViewBag.FatPot_MaisCapFPI = string.Format("{0:0.000}", analise.analise_valor1.minimo[1]);
            DateTime FatPot_MaisCapFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_min[1]);
            if (FatPot_MaisCapFPI_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapFPI_DataHora = string.Format("{0:t}", FatPot_MaisCapFPI_DataHora);
            else
                ViewBag.FatPot_MaisCapFPI_DataHora = "--:--";
            ViewBag.FatPot_MaisCapFPI_DataHoraN = FatPot_MaisCapFPI_DataHora;

            ViewBag.FatPot_MaisCapP = string.Format("{0:0.000}", analise.analise_valor1.minimo[0]);
            DateTime FatPot_MaisCapP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_min[0]);
            if (FatPot_MaisCapP_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapP_DataHora = string.Format("{0:t}", FatPot_MaisCapP_DataHora);
            else
                ViewBag.FatPot_MaisCapP_DataHora = "--:--";
            ViewBag.FatPot_MaisCapP_DataHoraN = FatPot_MaisCapP_DataHora;

            // fator de potencia No Periodo
            ViewBag.FatPot_NoPeriodoP = string.Format("{0:0.000}", analise.analise_valor1.medio[0]);
            ViewBag.FatPot_NoPeriodoFPI = string.Format("{0:0.000}", analise.analise_valor1.medio[1]);
            ViewBag.FatPot_NoPeriodoFPC = string.Format("{0:0.000}", analise.analise_valor1.medio[2]);

            // referencia
            ViewBag.RefenciaFPC = string.Format("{0:0.000}", -0.92);
            ViewBag.RefenciaFPI = string.Format("{0:0.000}", 0.92);

            return(0);
        }

        // Fator de Potência Semanal
        private int FatPot_Semanal(CliGrupoUnidMedicoesDominio medicao, DateTime dataRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_SEMANAL relatorio = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise = new RELAT_SEMANAL_ANALISE();

            RELAT_SEMANAL relatorio_sim = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise_sim = new RELAT_SEMANAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = medicao.IDMedicao;

            // simulacao
            int TipoSimula = 0;

            // converte data hora
            DATAHORA data_hora = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, dataRelat);

            // calcula valores
            retorno = SmCalcDB_Energia_RelatSemanal((char)0, ref config_interface, (char)3, (char)TipoSimula, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);

            ViewBag.Retorno = retorno;

            // verifica se erro
            if (retorno != 0)
            {
                // erro
                // -2 - erro : tipo de interface indefinida
                // -1 - erro : tipo de relatorio indefinido
                // 0  - ok e com dados
                // 1  - erro : nao foi possivel abrir o banco de dados
                // 2  - erro : nao existem dados
                // 3  - erro : leitura da configuracao da medicao
                // 4  - erro : leitura das tarifas de energia
                return (retorno);
            }

            // grafico
            var FatPot = new double[7, 26];
            var Periodo = new int[7, 26];
            var Datas = new string[26];
            var DatasN = new DateTime[7, 26];
            var Dias = new string[7];
            var Horas = new string[26];

            DateTime inicio = new DateTime();
            DateTime fim = new DateTime();

            double FatPot_min = 1.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;
            int k = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                Horas[i] = strData.ToString("HH:mm");

                // percorre dias
                for (k = 0; k < 7; k++)
                {
                    // data e hora para excel
                    DatasN[k, i] = strData.AddDays(k);

                    // guarda inicio e fim
                    if (i == 0 && k == 0)
                    {
                        inicio = strData;
                        fim = inicio.AddDays(6);
                    }

                    // dia da semana
                    if (i == 1)
                    {
                        Dias[k] = string.Format("{0:d}", strData.AddDays(k));
                    }

                    // verifica se primeira ou ultima barra
                    if (i == 0)
                    {
                        // zera
                        FatPot[k, i] = relatorio.registro[0].valor[k];
                        Periodo[k, i] = relatorio.registro[0].periodo[k];
                    }

                    if (i == 25)
                    {
                        // zera
                        FatPot[k, i] = relatorio.registro[23].valor[k];
                        Periodo[k, i] = relatorio.registro[23].periodo[k];
                    }

                    if (i >= 1 && i <= 24)
                    {
                        // copia
                        j = i - 1;

                        FatPot[k, i] = relatorio.registro[j].valor[k];
                        Periodo[k, i] = relatorio.registro[j].periodo[k];

                        // verifica se sem registro
                        if (relatorio.registro[j].periodo[k] == 3)
                        {
                            FatPot[k, i] = 1.0;
                        }

                        // verifica fatpot minimo
                        if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPot[k, i]), FatPot_min) > 0)
                            FatPot_min = Math.Abs(FatPot[k, i]);
                    }
                }

                // proxima hora
                strData = strData.AddHours(1);
            }

            if (FatPot_min > 0.92)
                FatPot_min = 0.92;

            FatPot_min = FatPot_min - (FatPot_min * 0.01);

            ViewBag.FatPotMin = FatPot_min;

            ViewBag.FatPot = FatPot;
            ViewBag.Periodo = Periodo;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;
            ViewBag.Horas = Horas;

            // nome do relatorio
            ViewBag.NomeRelat = string.Format("Fator de Potência");
            ViewBag.PeriodoRelat = string.Format("Semanal");

            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", dataRelat);
            ViewBag.DataAtualN = inicio;
            ViewBag.DataFinalN = fim;
            ViewBag.DataTextoAtual = string.Format("{0:d} - {1:d}", inicio, fim);

            // fator de potencia Mais Indutivo
            ViewBag.FatPot_MaisIndFPC = string.Format("{0:0.000}", analise.analise_valor.maximo[2]);
            DateTime FatPot_MaisIndFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[2]);
            if (FatPot_MaisIndFPC_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndFPC_DataHora = string.Format("{0:g}", FatPot_MaisIndFPC_DataHora);
            else
                ViewBag.FatPot_MaisIndFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndFPC_DataHoraN = FatPot_MaisIndFPC_DataHora;

            ViewBag.FatPot_MaisIndFPI = string.Format("{0:0.000}", analise.analise_valor.maximo[1]);
            DateTime FatPot_MaisIndFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[1]);
            if (FatPot_MaisIndFPI_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndFPI_DataHora = string.Format("{0:g}", FatPot_MaisIndFPI_DataHora);
            else
                ViewBag.FatPot_MaisIndFPI_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndFPI_DataHoraN = FatPot_MaisIndFPI_DataHora;

            ViewBag.FatPot_MaisIndP = string.Format("{0:0.000}", analise.analise_valor.maximo[0]);
            DateTime FatPot_MaisIndP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (FatPot_MaisIndP_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndP_DataHora = string.Format("{0:g}", FatPot_MaisIndP_DataHora);
            else
                ViewBag.FatPot_MaisIndP_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndP_DataHoraN = FatPot_MaisIndP_DataHora;

            // fator de potencia Mais Capacitivo
            ViewBag.FatPot_MaisCapFPC = string.Format("{0:0.000}", analise.analise_valor.minimo[2]);
            DateTime FatPot_MaisCapFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[2]);
            if (FatPot_MaisCapFPC_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapFPC_DataHora = string.Format("{0:g}", FatPot_MaisCapFPC_DataHora);
            else
                ViewBag.FatPot_MaisCapFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapFPC_DataHoraN = FatPot_MaisCapFPC_DataHora;

            ViewBag.FatPot_MaisCapFPI = string.Format("{0:0.000}", analise.analise_valor.minimo[1]);
            DateTime FatPot_MaisCapFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[1]);
            if (FatPot_MaisCapFPI_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapFPI_DataHora = string.Format("{0:g}", FatPot_MaisCapFPI_DataHora);
            else
                ViewBag.FatPot_MaisCapFPI_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapFPI_DataHoraN = FatPot_MaisCapFPI_DataHora;

            ViewBag.FatPot_MaisCapP = string.Format("{0:0.000}", analise.analise_valor.minimo[0]);
            DateTime FatPot_MaisCapP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[0]);
            if (FatPot_MaisCapP_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapP_DataHora = string.Format("{0:g}", FatPot_MaisCapP_DataHora);
            else
                ViewBag.FatPot_MaisCapP_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapP_DataHoraN = FatPot_MaisCapP_DataHora;

            // fator de potencia No Periodo
            ViewBag.FatPot_NoPeriodoP = string.Format("{0:0.000}", analise.analise_valor.medio[0]);
            ViewBag.FatPot_NoPeriodoFPI = string.Format("{0:0.000}", analise.analise_valor.medio[1]);
            ViewBag.FatPot_NoPeriodoFPC = string.Format("{0:0.000}", analise.analise_valor.medio[2]);

            // referencia
            ViewBag.RefenciaFPC = string.Format("{0:0.000}", -0.92);
            ViewBag.RefenciaFPI = string.Format("{0:0.000}", 0.92);

            return(0);
        }

        // Fator de Potência Mensal
        private int FatPot_Mensal(CliGrupoUnidMedicoesDominio medicao, DateTime dataRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_MENSAL relatorio = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise = new RELAT_MENSAL_ANALISE();

            RELAT_MENSAL relatorio_sim = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise_sim = new RELAT_MENSAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = medicao.IDMedicao;

            // simulacao
            int TipoSimula = 0;

            // converte data hora
            DATAHORA data_hora = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, dataRelat);

            // calcula valores
            retorno = SmCalcDB_Energia_RelatMensal((char)0, ref config_interface, (char)3, (char)TipoSimula, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);

            // numero de dias do mes
            int NumDiasMes = relatorio.num_dias;

            ViewBag.Retorno = retorno;

            // verifica se erro
            if (retorno != 0)
            {
                // erro
                // -2 - erro : tipo de interface indefinida
                // -1 - erro : tipo de relatorio indefinido
                // 0  - ok e com dados
                // 1  - erro : nao foi possivel abrir o banco de dados
                // 2  - erro : nao existem dados
                // 3  - erro : leitura da configuracao da medicao
                // 4  - erro : leitura das tarifas de energia
                return (retorno);
            }

            // grafico
            var FatPotP = new double[42];
            var FatPotInd = new double[42];
            var FatPotCap = new double[42];
            var Datas = new string[42];
            var DatasN = new DateTime[42];
            var Dias = new string[42];

            double FatPot_min = 1.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = strData.ToString("dd/MM");

                // proximo dia
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    FatPotP[i] = relatorio.registro[0].valor[0];
                    FatPotInd[i] = relatorio.registro[0].valor[1];
                    FatPotCap[i] = relatorio.registro[0].valor[2];
                }

                if (i == (NumDiasMes + 1))
                {
                    // zera
                    FatPotP[i] = relatorio.registro[NumDiasMes - 1].valor[0];
                    FatPotInd[i] = relatorio.registro[NumDiasMes - 1].valor[1];
                    FatPotCap[i] = relatorio.registro[NumDiasMes - 1].valor[2];
                }

                if (i >= 1 && i <= NumDiasMes)
                {
                    // copia
                    j = i - 1;

                    FatPotP[i] = relatorio.registro[j].valor[0];
                    FatPotInd[i] = relatorio.registro[j].valor[1];
                    FatPotCap[i] = relatorio.registro[j].valor[2];

                    // verifica fatpot minimo
                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPotP[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPotP[i]);

                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPotInd[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPotInd[i]);

                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPotCap[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPotCap[i]);
                }
            }

            if (FatPot_min > 0.92)
                FatPot_min = 0.92;

            FatPot_min = FatPot_min - (FatPot_min * 0.01);

            ViewBag.FatPotMin = FatPot_min;

            ViewBag.FatPotP = FatPotP;
            ViewBag.FatPotInd = FatPotInd;
            ViewBag.FatPotCap = FatPotCap;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            // nome do relatorio
            ViewBag.NomeRelat = string.Format("Fator de Potência");
            ViewBag.PeriodoRelat = string.Format("Mensal");

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", dataRelat);
            ViewBag.DataAtualN = dataRelat;
            ViewBag.DataTextoAtual = string.Format("{0:Y}", dataRelat);

            ViewBag.NumDiasMes = NumDiasMes;

            // fator de potencia Mais Indutivo
            ViewBag.FatPot_MaisIndFPC = string.Format("{0:0.000}", analise.analise_valor.maximo[2]);
            DateTime FatPot_MaisIndFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[2]);
            if (FatPot_MaisIndFPC_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndFPC_DataHora = string.Format("{0:g}", FatPot_MaisIndFPC_DataHora);
            else
                ViewBag.FatPot_MaisIndFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndFPC_DataHoraN = FatPot_MaisIndFPC_DataHora;

            ViewBag.FatPot_MaisIndFPI = string.Format("{0:0.000}", analise.analise_valor.maximo[1]);
            DateTime FatPot_MaisIndFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[1]);
            if (FatPot_MaisIndFPI_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndFPI_DataHora = string.Format("{0:g}", FatPot_MaisIndFPI_DataHora);
            else
                ViewBag.FatPot_MaisIndFPI_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndFPI_DataHoraN = FatPot_MaisIndFPI_DataHora;

            ViewBag.FatPot_MaisIndP = string.Format("{0:0.000}", analise.analise_valor.maximo[0]);
            DateTime FatPot_MaisIndP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (FatPot_MaisIndP_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndP_DataHora = string.Format("{0:g}", FatPot_MaisIndP_DataHora);
            else
                ViewBag.FatPot_MaisIndP_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndP_DataHoraN = FatPot_MaisIndP_DataHora;

            // fator de potencia Mais Capacitivo
            ViewBag.FatPot_MaisCapFPC = string.Format("{0:0.000}", analise.analise_valor.minimo[2]);
            DateTime FatPot_MaisCapFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[2]);
            if (FatPot_MaisCapFPC_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapFPC_DataHora = string.Format("{0:g}", FatPot_MaisCapFPC_DataHora);
            else
                ViewBag.FatPot_MaisCapFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapFPC_DataHoraN = FatPot_MaisCapFPC_DataHora;

            ViewBag.FatPot_MaisCapFPI = string.Format("{0:0.000}", analise.analise_valor.minimo[1]);
            DateTime FatPot_MaisCapFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[1]);
            if (FatPot_MaisCapFPI_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapFPI_DataHora = string.Format("{0:g}", FatPot_MaisCapFPI_DataHora);
            else
                ViewBag.FatPot_MaisCapFPI_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapFPI_DataHoraN = FatPot_MaisCapFPI_DataHora;

            ViewBag.FatPot_MaisCapP = string.Format("{0:0.000}", analise.analise_valor.minimo[0]);
            DateTime FatPot_MaisCapP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[0]);
            if (FatPot_MaisCapP_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapP_DataHora = string.Format("{0:g}", FatPot_MaisCapP_DataHora);
            else
                ViewBag.FatPot_MaisCapP_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapP_DataHoraN = FatPot_MaisCapP_DataHora;

            // fator de potencia No Periodo
            ViewBag.FatPot_NoPeriodoP = string.Format("{0:0.000}", analise.analise_valor.medio[0]);
            ViewBag.FatPot_NoPeriodoFPI = string.Format("{0:0.000}", analise.analise_valor.medio[1]);
            ViewBag.FatPot_NoPeriodoFPC = string.Format("{0:0.000}", analise.analise_valor.medio[2]);

            // referencia
            ViewBag.RefenciaFPC = string.Format("{0:0.000}", -0.92);
            ViewBag.RefenciaFPI = string.Format("{0:0.000}", 0.92);

            return(0);
        }
    }
}
