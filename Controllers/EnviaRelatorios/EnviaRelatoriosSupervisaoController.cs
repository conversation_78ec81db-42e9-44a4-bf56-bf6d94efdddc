﻿using System;
using System.Globalization;
using System.Linq;
using System.Runtime.InteropServices;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{

    public partial class EnviaRelatoriosController
    {
        // supervisao
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_SupervMedicao_Energia", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_SupervMedicao_Energia(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref DATAHORA pdata_equipo, ref ENERGIA_SUPERVMEDICAO psuperv);


        // Supervisão Energia
        private int Supervisao_Energia(CliGrupoUnidMedicoesDominio medicao, DateTime dataRelat)
        {
            // funcao de supervisao
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            ENERGIA_SUPERVMEDICAO superv = new ENERGIA_SUPERVMEDICAO();

            // preenche solicitacao
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = medicao.IDMedicao;
            config_interface.sweb.id_gateway = medicao.IDGateway;

            // converte data hora
            DATAHORA data_hora = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, dataRelat);

            // calcula valores
            retorno = SmCalcDB_SupervMedicao_Energia((char)0, ref config_interface, ref data_hora, ref superv);
            FuncoesSuperv.DivisaoUnidade_SUPERVMEDICAO(ref superv, medicao.IDTipoUnidadePotencia);

            // verifica se erro
            if (retorno != 0 && retorno != 3)
            {
                // erro
	            // -2 - erro : tipo de interface invalida ou desconhecida
	            // -1 - erro : ultima data e hora nao encontrada
	            //  0 - OK e com dados
		        //  1 - erro : ao abrir banco de dados
		        //  2 - erro : nao existem dados
		        //  3 - OK, mas falta registros
                return (retorno);
            }

            // calcula tolerancia
            double Tol_ContratoP = superv.Dem_ContratoP * (1.0 + (superv.Tol_ContratoP / 100.0));
            double Tol_ContratoFP = superv.Dem_ContratoFP * (1.0 + (superv.Tol_ContratoFP / 100.0));

            // converte data e hora
            DateTime DataHora_Atual = Funcoes_Converte.ConverteDataHora2DateTime(superv.DataAtual);

            DateTime Dem_Dia_DemMaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dem_Dia.DemMaxP_DataHora);
            DateTime Dem_Dia_DemMaxFP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dem_Dia.DemMaxFP_DataHora);
            DateTime Dem_Mes_DemMaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dem_Mes.DemMaxP_DataHora);
            DateTime Dem_Mes_DemMaxFP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dem_Mes.DemMaxFP_DataHora);
            DateTime Dem_MesAnt_DemMaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dem_MesAnt.DemMaxP_DataHora);
            DateTime Dem_MesAnt_DemMaxFP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dem_MesAnt.DemMaxFP_DataHora);

            DateTime FatPot_Dia_FatPotFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.FatPot_Dia.FatPotFPC_DataHora);
            DateTime FatPot_Dia_FatPotFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.FatPot_Dia.FatPotFPI_DataHora);
            DateTime FatPot_Mes_FatPotFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.FatPot_Mes.FatPotFPC_DataHora);
            DateTime FatPot_Mes_FatPotFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.FatPot_Mes.FatPotFPI_DataHora);
            DateTime FatPot_MesAnt_FatPotFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.FatPot_MesAnt.FatPotFPC_DataHora);
            DateTime FatPot_MesAnt_FatPotFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.FatPot_MesAnt.FatPotFPI_DataHora);

            // nome do relatorio
            ViewBag.NomeRelat = string.Format("Supervisão de Energia Elétrica");
            ViewBag.PeriodoRelat = string.Format("");

            // datas
            ViewBag.DataAtual = string.Format("{0:d}", DataHora_Atual);
            ViewBag.DiaAtual = string.Format("{0:d}", DataHora_Atual);
            ViewBag.MesAtual = string.Format("{0:Y}", DataHora_Atual);
            ViewBag.MesAnterior1 = string.Format("{0:Y}", DataHora_Atual.AddMonths(-1));

            //
            // HORARIO DE CONSUMO
            //

            // copia configuração Horário de Consumo
            DateTime HoraIniRef = DateTime.ParseExact(medicao.Funcionamento_Inicio, "HH:mm", CultureInfo.InvariantCulture);
            DateTime HoraFimRef = DateTime.ParseExact(medicao.Funcionamento_Fim, "HH:mm", CultureInfo.InvariantCulture);
            double DemandaResidual = medicao.Funcionamento_DemMin;

            double DesperdicioConsumoP = 0.0;
            double DesperdicioConsumoFP = 0.0;
            double DesperdicioMonetizacao = 0.0;

            //
            // GRAFICO DEMANDA
            //

            // grafico de demanda
            var Dias_Demanda = new string[98];
            var Demanda = new double[98];
            var Periodo = new double[98];
            var Contrato = new double[98];
            var Tolerancia = new double[98];

            double Dem_max = 0.0;

            // valores
            DateTime strData = Funcoes_Converte.ConverteDataHora2DateTime(superv.Demanda_DataHora[0]);

            // volta 15min para a primeira barra
            strData = strData.AddMinutes(-15);

            int i = 0;
            int j = 0;

            for (i = 0; i < 98; i++)
            {
                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    Demanda[i] = superv.Demanda[0];
                    Periodo[i] = superv.Periodo[0];
                    Contrato[i] = superv.Contrato[0];
                    Tolerancia[i] = superv.Contrato[0] * (1.0 + (superv.Tol_ContratoFP / 100.0));
                }

                if (i == 97)
                {
                    // zera
                    Demanda[i] = superv.Demanda[95];
                    Periodo[i] = superv.Periodo[95];
                    Contrato[i] = superv.Contrato[95];
                    Tolerancia[i] = superv.Contrato[95] * (1.0 + (superv.Tol_ContratoFP / 100.0));
                }

                if (i >= 1 && i <= 96)
                {
                    // copia
                    j = i - 1;

                    Demanda[i] = superv.Demanda[j];
                    Periodo[i] = superv.Periodo[j];
                    Contrato[i] = superv.Contrato[j];

                    if (Periodo[i] == 0)
                    {
                        Tolerancia[i] = superv.Contrato[j] * (1.0 + (superv.Tol_ContratoP / 100.0));
                    }
                    else
                    {
                        Tolerancia[i] = superv.Contrato[j] * (1.0 + (superv.Tol_ContratoFP / 100.0));
                    }

                    // verifica se sem registro
                    if (Periodo[i] == 3)
                    {
                        Demanda[i] = 0.0;
                    }

                    // verifica demanda maxima
                    if (Demanda[i] > Dem_max)
                        Dem_max = Demanda[i];

                    if (Contrato[i] > Dem_max)
                        Dem_max = Contrato[i];

                    if (Tolerancia[i] > Dem_max)
                        Dem_max = Tolerancia[i];

                    //
                    // HORÁRIO DE CONSUMO
                    //

                    // verifica se fora do horário de funcionamento
                    if ((strData.Hour * 60 + strData.Minute < HoraIniRef.Hour * 60 + HoraIniRef.Minute) ||
                        (strData.Hour * 60 + strData.Minute > HoraFimRef.Hour * 60 + HoraFimRef.Minute))
                    {
                        // verifica se demanda maior que residual
                        if (superv.Demanda[j] > DemandaResidual)
                        {
                            // verifica periodo
                            if (superv.Periodo[j] == PERIODO.P)
                            {
                                DesperdicioConsumoP += superv.Demanda[j] - DemandaResidual;
                            }
                            else
                            {
                                DesperdicioConsumoFP += superv.Demanda[j] - DemandaResidual;
                            }
                        }
                    }
                }

                //
                // HORÁRIO
                //

                // formata label
                Dias_Demanda[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proximo quinze minutos
                strData = strData.AddMinutes(15);
            }

            Dem_max = Dem_max * 1.1;

            if (Dem_max < 1.0)
            {
                Dem_max = 1.0;
            }

            ViewBag.DemMaxGrafico = Dem_max;

            ViewBag.DemandaAtv = Demanda;
            ViewBag.Periodo = Periodo;
            ViewBag.Contrato = Contrato;
            ViewBag.Tolerancia = Tolerancia;
            ViewBag.Dias_Demanda = Dias_Demanda;

            //
            // GRAFICO FATOR DE POTENCIA
            //

            // grafico de fator de potencia
            var Dias_FatPot = new string[26];
            var FatPot = new double[26];
            var Posto = new double[26];

            double FatPot_min = 1.0;

            // valores
            strData = Funcoes_Converte.ConverteDataHora2DateTime(superv.FatPot_DataHora[0]);

            // volta 1hora para a primeira barra
            strData = strData.AddHours(-1);

            i = 0;
            j = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Dias_FatPot[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proxima hora
                strData = strData.AddHours(1);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    FatPot[i] = 1.0;
                    Posto[i] = 3;
                }

                // verifica se ultima barra
                if (i == 25)
                {
                    // zera
                    FatPot[i] = 1.0;
                    Posto[i] = 3;
                }

                if (i >= 1 && i <= 24)
                {
                    // copia
                    j = i - 1;

                    FatPot[i] = superv.FatPot[j];
                    Posto[i] = superv.Posto[j];

                    // verifica se sem registro
                    if (Posto[i] == 3 || FatPot[i] == 0.0)
                    {
                        FatPot[i] = 1.0;
                    }

                    // verifica fatpot minimo
                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPot[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPot[i]);
                }
            }

            if (FatPot_min > 0.92)
                FatPot_min = 0.92;

            FatPot_min = FatPot_min - (FatPot_min * 0.01);

            ViewBag.FatPotMin = FatPot_min;

            ViewBag.FatPot = FatPot;
            ViewBag.Posto = Posto;
            ViewBag.Dias_FatPot = Dias_FatPot;

            //
            // DEMANDA
            //

            // demanda
            ViewBag.Dem_Dia_DemMaxP = string.Format("{0:#,##0.0}", superv.Dem_Dia.DemMaxP);
            if (Dem_Dia_DemMaxP_DataHora.Year != 2000)
                ViewBag.Dem_Dia_DemMaxP_DataHora = string.Format("{0:g}", Dem_Dia_DemMaxP_DataHora);
            else
                ViewBag.Dem_Dia_DemMaxP_DataHora = "--/--/---- --:--";

            ViewBag.Dem_Dia_DemMaxFP = string.Format("{0:#,##0.0}", superv.Dem_Dia.DemMaxFP);
            if (Dem_Dia_DemMaxFP_DataHora.Year != 2000)
                ViewBag.Dem_Dia_DemMaxFP_DataHora = string.Format("{0:g}", Dem_Dia_DemMaxFP_DataHora);
            else
                ViewBag.Dem_Dia_DemMaxFP_DataHora = "--/--/---- --:--";

            ViewBag.Dem_Mes_DemMaxP = string.Format("{0:#,##0.0}", superv.Dem_Mes.DemMaxP);
            if (Dem_Mes_DemMaxP_DataHora.Year != 2000)
                ViewBag.Dem_Mes_DemMaxP_DataHora = string.Format("{0:g}", Dem_Mes_DemMaxP_DataHora);
            else
                ViewBag.Dem_Mes_DemMaxP_DataHora = "--/--/---- --:--";

            ViewBag.Dem_Mes_DemMaxFP = string.Format("{0:#,##0.0}", superv.Dem_Mes.DemMaxFP);
            if (Dem_Mes_DemMaxFP_DataHora.Year != 2000)
                ViewBag.Dem_Mes_DemMaxFP_DataHora = string.Format("{0:g}", Dem_Mes_DemMaxFP_DataHora);
            else
                ViewBag.Dem_Mes_DemMaxFP_DataHora = "--/--/---- --:--";

            ViewBag.Dem_MesAnt_DemMaxP = string.Format("{0:#,##0.0}", superv.Dem_MesAnt.DemMaxP);
            if (Dem_MesAnt_DemMaxP_DataHora.Year != 2000)
                ViewBag.Dem_MesAnt_DemMaxP_DataHora = string.Format("{0:g}", Dem_MesAnt_DemMaxP_DataHora);
            else
                ViewBag.Dem_MesAnt_DemMaxP_DataHora = "--/--/---- --:--";

            ViewBag.Dem_MesAnt_DemMaxFP = string.Format("{0:#,##0.0}", superv.Dem_MesAnt.DemMaxFP);
            if (Dem_MesAnt_DemMaxFP_DataHora.Year != 2000)
                ViewBag.Dem_MesAnt_DemMaxFP_DataHora = string.Format("{0:g}", Dem_MesAnt_DemMaxFP_DataHora);
            else
                ViewBag.Dem_MesAnt_DemMaxFP_DataHora = "--/--/---- --:--";

            ViewBag.Dem_ContratoP = superv.Dem_ContratoP;
            ViewBag.Dem_ContratoFP = superv.Dem_ContratoFP;

            ViewBag.Tol_ContratoP = superv.Tol_ContratoP;
            ViewBag.Tol_ContratoFP = superv.Tol_ContratoFP;

            // verifico se houve ultrapassagem
            double A = superv.Dem_Mes.DemMaxP;
            double B = Tol_ContratoP;
            double Dem_Mes_UltrapassagemP = ((A > B) && (B > 0.0)) ? superv.Dem_Mes.DemMaxP : 0.0;

            ViewBag.Dem_Mes_UltrapassagemP = Dem_Mes_UltrapassagemP;

            A = superv.Dem_Mes.DemMaxFP;
            B = Tol_ContratoFP;
            double Dem_Mes_UltrapassagemFP = ((A > B) && (B > 0.0)) ? superv.Dem_Mes.DemMaxFP : 0.0;

            ViewBag.Dem_Mes_UltrapassagemFP = Dem_Mes_UltrapassagemFP;

            //
            // CONSUMO
            //

            // consumo

            // encontro o maior consumo
            double Cons_Dia_ConsumoTotal = superv.Cons_Dia.ConsumoP + superv.Cons_Dia.ConsumoFP;
            double Cons_Mes_ConsumoTotal = superv.Cons_Mes.ConsumoP + superv.Cons_Mes.ConsumoFP;
            double Cons_MesProj_ConsumoTotal = superv.Cons_MesProj.ConsumoP + superv.Cons_MesProj.ConsumoFP;
            double Cons_MesAnt_ConsumoTotal = superv.Cons_MesAnt.ConsumoP + superv.Cons_MesAnt.ConsumoFP;

            double maior_consumo = Cons_Dia_ConsumoTotal;

            if (Cons_Mes_ConsumoTotal > maior_consumo)
                maior_consumo = Cons_Mes_ConsumoTotal;

            if (Cons_MesProj_ConsumoTotal > maior_consumo)
                maior_consumo = Cons_MesProj_ConsumoTotal;

            if (Cons_MesAnt_ConsumoTotal > maior_consumo)
                maior_consumo = Cons_MesAnt_ConsumoTotal;

            ViewBag.Cons_Dia_ConsumoP = Funcoes_SmartEnergy.FormatValor(superv.Cons_Dia.ConsumoP);
            ViewBag.Cons_Dia_ConsumoFP = Funcoes_SmartEnergy.FormatValor(superv.Cons_Dia.ConsumoFP);
            ViewBag.Cons_Dia_ConsumoTotal = Funcoes_SmartEnergy.FormatValor(Cons_Dia_ConsumoTotal);

            ViewBag.Cons_Mes_ConsumoP = Funcoes_SmartEnergy.FormatValor(superv.Cons_Mes.ConsumoP);
            ViewBag.Cons_Mes_ConsumoFP = Funcoes_SmartEnergy.FormatValor(superv.Cons_Mes.ConsumoFP);
            ViewBag.Cons_Mes_ConsumoTotal = Funcoes_SmartEnergy.FormatValor(Cons_Mes_ConsumoTotal);

            ViewBag.Cons_MesProj_ConsumoP = Funcoes_SmartEnergy.FormatValor(superv.Cons_MesProj.ConsumoP);
            ViewBag.Cons_MesProj_ConsumoFP = Funcoes_SmartEnergy.FormatValor(superv.Cons_MesProj.ConsumoFP);
            ViewBag.Cons_MesProj_ConsumoTotal = Funcoes_SmartEnergy.FormatValor(Cons_MesProj_ConsumoTotal);

            ViewBag.Cons_MesAnt_ConsumoP = Funcoes_SmartEnergy.FormatValor(superv.Cons_MesAnt.ConsumoP);
            ViewBag.Cons_MesAnt_ConsumoFP = Funcoes_SmartEnergy.FormatValor(superv.Cons_MesAnt.ConsumoFP);
            ViewBag.Cons_MesAnt_ConsumoTotal = Funcoes_SmartEnergy.FormatValor(Cons_MesAnt_ConsumoTotal);

            // diferenca em % entre total Anterior e Projetado
            A = Cons_MesAnt_ConsumoTotal;
            B = Cons_MesProj_ConsumoTotal;
            double PorcAntProj = (A == 0.0) ? 0.0 : ((B - A) / A) * 100.0;

            ViewBag.Cons_MesProj_PorcAntN = PorcAntProj;
            ViewBag.Cons_MesProj_PorcAnt = string.Format("{0:0.0} %", PorcAntProj);

            //
            // FATOR DE POTENCIA
            //

            // fator de potencia
            ViewBag.FatPot_Dia_FatPotFPC = string.Format("{0:0.000}", superv.FatPot_Dia.FatPotFPC);
            if (FatPot_Dia_FatPotFPC_DataHora.Year != 2000)
                ViewBag.FatPot_Dia_FatPotFPC_DataHora = string.Format("{0:g}", FatPot_Dia_FatPotFPC_DataHora);
            else
                ViewBag.FatPot_Dia_FatPotFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_Dia_FatPotFPI = string.Format("{0:0.000}", superv.FatPot_Dia.FatPotFPI);
            if (FatPot_Dia_FatPotFPI_DataHora.Year != 2000)
                ViewBag.FatPot_Dia_FatPotFPI_DataHora = string.Format("{0:g}", FatPot_Dia_FatPotFPI_DataHora);
            else
                ViewBag.FatPot_Dia_FatPotFPI_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_Mes_FatPotFPC = string.Format("{0:0.000}", superv.FatPot_Mes.FatPotFPC);
            if (FatPot_Mes_FatPotFPC_DataHora.Year != 2000)
                ViewBag.FatPot_Mes_FatPotFPC_DataHora = string.Format("{0:g}", FatPot_Mes_FatPotFPC_DataHora);
            else
                ViewBag.FatPot_Mes_FatPotFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_Mes_FatPotFPI = string.Format("{0:0.000}", superv.FatPot_Mes.FatPotFPI);
            if (FatPot_Mes_FatPotFPI_DataHora.Year != 2000)
                ViewBag.FatPot_Mes_FatPotFPI_DataHora = string.Format("{0:g}", FatPot_Mes_FatPotFPI_DataHora);
            else
                ViewBag.FatPot_Mes_FatPotFPI_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MesAnt_FatPotFPC = string.Format("{0:0.000}", superv.FatPot_MesAnt.FatPotFPC);
            if (FatPot_MesAnt_FatPotFPC_DataHora.Year != 2000)
                ViewBag.FatPot_MesAnt_FatPotFPC_DataHora = string.Format("{0:g}", FatPot_MesAnt_FatPotFPC_DataHora);
            else
                ViewBag.FatPot_MesAnt_FatPotFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MesAnt_FatPotFPI = string.Format("{0:0.000}", superv.FatPot_MesAnt.FatPotFPI);
            if (FatPot_MesAnt_FatPotFPI_DataHora.Year != 2000)
                ViewBag.FatPot_MesAnt_FatPotFPI_DataHora = string.Format("{0:g}", FatPot_MesAnt_FatPotFPI_DataHora);
            else
                ViewBag.FatPot_MesAnt_FatPotFPI_DataHora = "--/--/---- --:--";

            ViewBag.FatPot_Referencia = string.Format("{0:0.000}", 0.92);

            //
            // HORÁRIO DE CONSUMO (Desperdício)
            //

            // transforma em consumo
            DesperdicioConsumoP /= 4.0;
            DesperdicioConsumoFP /= 4.0;

            // verifica se existe despedicio
            if (DesperdicioConsumoP > 0.0 || DesperdicioConsumoFP > 0.0)
            {
                // lê tarifa de consumo
                double Tarcp = 0.0;
                double Tarcf = 0.0;

                // tarifa azul
                if (medicao.IDEstruturaTarifaria == TIPOESTR_TAR.THS_AZUL)
                {
                    Tarifas_AZMetodos tarifas_AZMetodos = new Tarifas_AZMetodos();
                    Tarifas_AZDominio tarifa_azul = new Tarifas_AZDominio();
                    tarifa_azul = tarifas_AZMetodos.ListarMaisRecenteData(medicao.IDAgenteDistribuidora, medicao.IDTipoSubgrupo, DataHora_Atual);

                    // verifica se encontrou tarifa
                    if (tarifa_azul != null)
                    {
                        // lê tarifa de consumo
                        Tarcp = tarifa_azul.Tarcp;
                        Tarcf = tarifa_azul.Tarcf;
                    }
                }

                // tarifa verde 
                if (medicao.IDEstruturaTarifaria == TIPOESTR_TAR.THS_VERDE)
                {
                    Tarifas_VDMetodos tarifas_VDMetodos = new Tarifas_VDMetodos();
                    Tarifas_VDDominio tarifa_verde = new Tarifas_VDDominio();
                    tarifa_verde = tarifas_VDMetodos.ListarMaisRecenteData(medicao.IDAgenteDistribuidora, medicao.IDTipoSubgrupo, DataHora_Atual);

                    // verifica se encontrou tarifa
                    if (tarifa_verde != null)
                    {
                        // lê tarifa de consumo
                        Tarcp = tarifa_verde.Tarcp;
                        Tarcf = tarifa_verde.Tarcf;
                    }
                }

                // monetiza desperdicio
                DesperdicioMonetizacao = DesperdicioConsumoFP * (Tarcf / 1000.0) + DesperdicioConsumoP * (Tarcp / 1000.0);
            }

            ViewBag.HorarioConsumo = string.Format("{0:t} - {1:t}", HoraIniRef, HoraFimRef);
            ViewBag.DemandaResidual = string.Format("{0:#,##0.0}", DemandaResidual); 

            ViewBag.DesperdicioConsumo = Funcoes_SmartEnergy.FormatValor(DesperdicioConsumoP + DesperdicioConsumoFP);
            ViewBag.DesperdicioMonetizacao = string.Format("{0:C}", DesperdicioMonetizacao);


            //
            // GATEWAY
            //

            // le supervisao da medicao
            SupervMedicoesMetodos medicoesMetodos = new SupervMedicoesMetodos();
            SupervMedicoesDominio supervMedicao = medicoesMetodos.ListarPorIDMedicao(medicao.IDCliente, medicao.IDMedicao).First();

            // gateway
            ViewBag.GatewayNome = supervMedicao.NGW;
            ViewBag.GatewayModelo = supervMedicao.Modelo_GW + " (" + supervMedicao.Versao_GW + ")";
            ViewBag.GatewayAtualizacao = supervMedicao.DataHoraAtualizacao;
            ViewBag.GatewayDataEq = supervMedicao.DataHoraEq;

            // sinal
            int pos = supervMedicao.Status_GW.IndexOf("SQ=");

            if (pos >= 0)
            {
                string str = supervMedicao.Status_GW.Substring(pos + 3);
                double sinal = double.Parse(str);

                if (sinal >= 99.0)
                {
                    sinal = 0.0;
                }
                else
                {
                    sinal = (sinal / 31.0) * 100.0;
                }

                if (sinal == 0.0)
                {
                    supervMedicao.Status_GW = "-";
                }
                else
                {
                    supervMedicao.Status_GW = string.Format("Sinal {0:0}%", sinal);
                }
            }
            else
            {
                supervMedicao.Status_GW = "-";
            }

            ViewBag.GatewayStatus = supervMedicao.Status_GW;

            return(0);
        }
    }
}
