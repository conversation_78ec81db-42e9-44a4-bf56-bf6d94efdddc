﻿using System;
using System.Runtime.InteropServices;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{

    public partial class EnviaRelatoriosController
    {
        // Relatorio Diario
        // 0 -> Demanda Ativa e Fator de Potencia
        // 1 -> Demanda Reativa
        // 2 -> Consumo Ativo e Consumo Reativo
        // 3 -> <PERSON><PERSON> <PERSON> Potencia (horario)
        // 4 -> <PERSON><PERSON> <PERSON> (Demanda Media / Demanda Maxima)
        // 5 -> Demanda nao utilizada
        // 6 -> Consumo Ativo segundo mercado livre
        // 7 -> <PERSON><PERSON> de Utilizacao (Demanda Media / Contrato)
        // 8 -> Consumo Ativo x Meta para Supervisao Mensal do Pao de Acucar
        // 9 -> KPI para Economia no Consumo de Energia
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_RelatDiario", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_RelatDiario(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, char simula, ref DATAHORA pdata_equipo, ref RELAT_DIARIO prelatorio, ref RELAT_DIARIO_ANALISE panalise, ref RELAT_DIARIO prelatorio_sim, ref RELAT_DIARIO_ANALISE panalise_sim);

        // Relatorio Semanal
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_RelatSemanal", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_RelatSemanal(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, char simula, ref DATAHORA pdata_equipo, ref RELAT_SEMANAL prelatorio, ref RELAT_SEMANAL_ANALISE panalise, ref RELAT_SEMANAL prelatorio_sim, ref RELAT_SEMANAL_ANALISE panalise_sim);

        // Relatorio Mensal
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_RelatMensal", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_RelatMensal(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, char simula, ref DATAHORA pdata_equipo, ref RELAT_MENSAL prelatorio, ref RELAT_MENSAL_ANALISE panalise, ref RELAT_MENSAL prelatorio_sim, ref RELAT_MENSAL_ANALISE panalise_sim);


        // Demanda Ativa Diario
        private int Dem_Ativa_Diario(CliGrupoUnidMedicoesDominio medicao, DateTime dataRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_DIARIO relatorio = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise = new RELAT_DIARIO_ANALISE();

            RELAT_DIARIO relatorio_sim = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise_sim = new RELAT_DIARIO_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = medicao.IDMedicao;

            // simulacao
            int TipoSimula = 0;

            // converte data hora
            DATAHORA data_hora = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, dataRelat);

            // calcula valores
            retorno = SmCalcDB_Energia_RelatDiario((char)0, ref config_interface, (char)0, (char)TipoSimula, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_DIARIO(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            ViewBag.Retorno = retorno;

            // verifica se erro
            if (retorno != 0)
            {
                // erro
                // -2 - erro : tipo de interface indefinida
                // -1 - erro : tipo de relatorio indefinido
	            // 0  - ok e com dados
	            // 1  - erro : nao foi possivel abrir o banco de dados
	            // 2  - erro : nao existem dados
	            // 3  - erro : leitura da configuracao da medicao
	            // 4  - erro : leitura das tarifas de energia
                return (retorno);
            }

            // grafico
            var DemandaAtv = new double[98];
            var DemandaRtv = new double[98];
            var FatPot = new double[98];
            var Periodo = new int[98];
            var Contrato = new double[98];
            var Tolerancia = new double[98];
            var Datas = new string[98];
            var DatasN = new DateTime[98];
            var Horas = new string[98];

            double Dem_max_grafico = 0.0;
            double FatPot_min = 1.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 15min para a primeira barra
            strData = strData.AddMinutes(-15);

            int i = 0;
            int j = 0;

            for (i = 0; i < 98; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Horas[i] = strData.ToString("HH:mm");

                // proximo quinze minutos
                strData = strData.AddMinutes(15);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    DemandaAtv[i] = relatorio.registro[0].valor1;
                    DemandaRtv[i] = relatorio.registro[0].valor2;
                    FatPot[i] = Funcoes_FatPot.CalculaFatPot(DemandaAtv[i], DemandaRtv[i]);
                    Periodo[i] = relatorio.registro[0].periodo;
                    Contrato[i] = relatorio.contrato[0];
                    Tolerancia[i] = Contrato[i] * (1.0 + (analise.analise_valor1.tolerancia[1] / 100.0));
                }

                // verifica se ultima barra
                if (i == 97)
                {
                    // zera
                    DemandaAtv[i] = relatorio.registro[95].valor1;
                    DemandaRtv[i] = relatorio.registro[95].valor2;
                    FatPot[i] = Funcoes_FatPot.CalculaFatPot(DemandaAtv[i], DemandaRtv[i]);
                    Periodo[i] = relatorio.registro[95].periodo;
                    Contrato[i] = relatorio.contrato[95];
                    Tolerancia[i] = Contrato[i] * (1.0 + (analise.analise_valor1.tolerancia[1] / 100.0));
                }

                if (i >= 1 && i <= 96)
                {
                    // copia
                    j = i - 1;

                    DemandaAtv[i] = relatorio.registro[j].valor1;
                    DemandaRtv[i] = relatorio.registro[j].valor2;
                    FatPot[i] = Funcoes_FatPot.CalculaFatPot(DemandaAtv[i], DemandaRtv[i]);
                    Periodo[i] = relatorio.registro[j].periodo;
                    Contrato[i] = relatorio.contrato[j];

                    if (relatorio.registro[j].periodo == 0)
                    {
                        Tolerancia[i] = Contrato[i] * (1.0 + (analise.analise_valor1.tolerancia[0] / 100.0));
                    }
                    else
                    {
                        Tolerancia[i] = Contrato[i] * (1.0 + (analise.analise_valor1.tolerancia[1] / 100.0));
                    }

                    // verifica se sem registro
                    if (relatorio.registro[j].periodo == 3)
                    {
                        DemandaAtv[i] = 0.0;
                        DemandaRtv[i] = 0.0;
                        FatPot[i] = 1.0;
                    }

                    // verifica demanda maxima
                    if (DemandaAtv[i] > Dem_max_grafico)
                        Dem_max_grafico = DemandaAtv[i];

                    if (Contrato[i] > Dem_max_grafico)
                        Dem_max_grafico = Contrato[i];

                    if (Tolerancia[i] > Dem_max_grafico)
                        Dem_max_grafico = Tolerancia[i];

                    // verifica fatpot minimo
                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPot[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPot[i]);
                }
            }

            Dem_max_grafico = Dem_max_grafico * 1.1;

            if (Dem_max_grafico < 1.0)
            {
                Dem_max_grafico = 1.0;
            }

            ViewBag.DemMaxGrafico = Dem_max_grafico;

            if (FatPot_min > 0.92)
                FatPot_min = 0.92;

            FatPot_min = FatPot_min - (FatPot_min * 0.01);

            ViewBag.FatPotMin = FatPot_min;

            ViewBag.DemandaAtv = DemandaAtv;
            ViewBag.DemandaRtv = DemandaRtv;
            ViewBag.FatPot = FatPot;
            ViewBag.Periodo = Periodo;
            ViewBag.Contrato = Contrato;
            ViewBag.Tolerancia = Tolerancia;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Horas = Horas;

            // nome do relatorio
            ViewBag.NomeRelat = string.Format("Demanda Ativa");
            ViewBag.PeriodoRelat = string.Format("Diário");

            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", dataRelat);
            ViewBag.DataAtualN = dataRelat;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", dataRelat);

            // demanda
            ViewBag.Dem_MaxP = string.Format("{0:#,##0.0}", analise.analise_valor1.maximo[0]);
            DateTime Dem_MaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max[0]);
            if (Dem_MaxP_DataHora.Year != 2000)
                ViewBag.Dem_MaxP_DataHora = string.Format("{0:t}", Dem_MaxP_DataHora);
            else
                ViewBag.Dem_MaxP_DataHora = "--:--";
            ViewBag.Dem_MaxP_DataHoraN = Dem_MaxP_DataHora;

            ViewBag.Dem_MaxFPI = string.Format("{0:#,##0.0}", analise.analise_valor1.maximo[1]);
            DateTime Dem_MaxFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max[1]);
            if (Dem_MaxFPI_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPI_DataHora = string.Format("{0:t}", Dem_MaxFPI_DataHora);
            else
                ViewBag.Dem_MaxFPI_DataHora = "--:--";
            ViewBag.Dem_MaxFPI_DataHoraN = Dem_MaxFPI_DataHora;

            ViewBag.Dem_MaxFPC = string.Format("{0:#,##0.0}", analise.analise_valor1.maximo[2]);
            DateTime Dem_MaxFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max[2]);
            if (Dem_MaxFPC_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPC_DataHora = string.Format("{0:t}", Dem_MaxFPC_DataHora);
            else
                ViewBag.Dem_MaxFPC_DataHora = "--:--";
            ViewBag.Dem_MaxFPC_DataHoraN = Dem_MaxFPC_DataHora;

            ViewBag.Dem_MinP = string.Format("{0:#,##0.0}", analise.analise_valor1.minimo[0]);
            DateTime Dem_MinP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_min[0]);
            if (Dem_MinP_DataHora.Year != 2000)
                ViewBag.Dem_MinP_DataHora = string.Format("{0:t}", Dem_MinP_DataHora);
            else
                ViewBag.Dem_MinP_DataHora = "--:--";
            ViewBag.Dem_MinP_DataHoraN = Dem_MinP_DataHora;

            ViewBag.Dem_MinFPI = string.Format("{0:#,##0.0}", analise.analise_valor1.minimo[1]);
            DateTime Dem_MinFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_min[1]);
            if (Dem_MinFPI_DataHora.Year != 2000)
                ViewBag.Dem_MinFPI_DataHora = string.Format("{0:t}", Dem_MinFPI_DataHora);
            else
                ViewBag.Dem_MinFPI_DataHora = "--:--";
            ViewBag.Dem_MinFPI_DataHoraN = Dem_MinFPI_DataHora;

            ViewBag.Dem_MinFPC = string.Format("{0:#,##0.0}", analise.analise_valor1.minimo[2]);
            DateTime Dem_MinFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_min[2]);
            if (Dem_MinFPC_DataHora.Year != 2000)
                ViewBag.Dem_MinFPC_DataHora = string.Format("{0:t}", Dem_MinFPC_DataHora);
            else
                ViewBag.Dem_MinFPC_DataHora = "--:--";
            ViewBag.Dem_MinFPC_DataHoraN = Dem_MinFPC_DataHora;

            ViewBag.Dem_MedP = string.Format("{0:#,##0.0}", analise.analise_valor1.medio[0]);
            ViewBag.Dem_MedFPI = string.Format("{0:#,##0.0}", analise.analise_valor1.medio[1]);
            ViewBag.Dem_MedFPC = string.Format("{0:#,##0.0}", analise.analise_valor1.medio[2]);

            ViewBag.Dem_ContratoP = string.Format("{0:#,##0}", analise.analise_valor1.referencia[0]);
            ViewBag.Dem_ContratoFP = string.Format("{0:#,##0}", analise.analise_valor1.referencia[1]);

            ViewBag.Tol_ContratoP = string.Format("{0:0}", analise.analise_valor1.tolerancia[0]);
            ViewBag.Tol_ContratoFP = string.Format("{0:0}", analise.analise_valor1.tolerancia[1]);

            // fator de potencia Mais Indutivo
            ViewBag.FatPot_MaisIndFPC = string.Format("{0:0.000}", analise.analise_valor2.maximo[2]);
            DateTime FatPot_MaisIndFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor2.datahora_max[2]);
            if (FatPot_MaisIndFPC_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndFPC_DataHora = string.Format("{0:t}", FatPot_MaisIndFPC_DataHora);
            else
                ViewBag.FatPot_MaisIndFPC_DataHora = "--:--";
            ViewBag.FatPot_MaisIndFPC_DataHoraN = FatPot_MaisIndFPC_DataHora;

            ViewBag.FatPot_MaisIndFPI = string.Format("{0:0.000}", analise.analise_valor2.maximo[1]);
            DateTime FatPot_MaisIndFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor2.datahora_max[1]);
            if (FatPot_MaisIndFPI_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndFPI_DataHora = string.Format("{0:t}", FatPot_MaisIndFPI_DataHora);
            else
                ViewBag.FatPot_MaisIndFPI_DataHora = "--:--";
            ViewBag.FatPot_MaisIndFPI_DataHoraN = FatPot_MaisIndFPI_DataHora;

            ViewBag.FatPot_MaisIndP = string.Format("{0:0.000}", analise.analise_valor2.maximo[0]);
            DateTime FatPot_MaisIndP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor2.datahora_max[0]);
            if (FatPot_MaisIndP_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndP_DataHora = string.Format("{0:t}", FatPot_MaisIndP_DataHora);
            else
                ViewBag.FatPot_MaisIndP_DataHora = "--:--";
            ViewBag.FatPot_MaisIndP_DataHoraN = FatPot_MaisIndP_DataHora;

            // fator de potencia Mais Capacitivo
            ViewBag.FatPot_MaisCapFPC = string.Format("{0:0.000}", analise.analise_valor2.minimo[2]);
            DateTime FatPot_MaisCapFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor2.datahora_min[2]);
            if (FatPot_MaisCapFPC_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapFPC_DataHora = string.Format("{0:t}", FatPot_MaisCapFPC_DataHora);
            else
                ViewBag.FatPot_MaisCapFPC_DataHora = "--:--";
            ViewBag.FatPot_MaisCapFPC_DataHoraN = FatPot_MaisCapFPC_DataHora;

            ViewBag.FatPot_MaisCapFPI = string.Format("{0:0.000}", analise.analise_valor2.minimo[1]);
            DateTime FatPot_MaisCapFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor2.datahora_min[1]);
            if (FatPot_MaisCapFPI_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapFPI_DataHora = string.Format("{0:t}", FatPot_MaisCapFPI_DataHora);
            else
                ViewBag.FatPot_MaisCapFPI_DataHora = "--:--";
            ViewBag.FatPot_MaisCapFPI_DataHoraN = FatPot_MaisCapFPI_DataHora;

            ViewBag.FatPot_MaisCapP = string.Format("{0:0.000}", analise.analise_valor2.minimo[0]);
            DateTime FatPot_MaisCapP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor2.datahora_min[0]);
            if (FatPot_MaisCapP_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapP_DataHora = string.Format("{0:t}", FatPot_MaisCapP_DataHora);
            else
                ViewBag.FatPot_MaisCapP_DataHora = "--:--";
            ViewBag.FatPot_MaisCapP_DataHoraN = FatPot_MaisCapP_DataHora;

            // fator de potencia No Periodo
            ViewBag.FatPot_NoPeriodoP = string.Format("{0:0.000}", analise.analise_valor2.medio[0]);
            ViewBag.FatPot_NoPeriodoFPI = string.Format("{0:0.000}", analise.analise_valor2.medio[1]);
            ViewBag.FatPot_NoPeriodoFPC = string.Format("{0:0.000}", analise.analise_valor2.medio[2]);

            // referencia
            ViewBag.RefenciaFPC = string.Format("{0:0.000}", -0.92);
            ViewBag.RefenciaFPI = string.Format("{0:0.000}", 0.92);

            // consumo
            ViewBag.ConsP = string.Format("{0:#,##0}", analise.analise_valor1.consumo[0]);
            ViewBag.ConsFPI = string.Format("{0:#,##0}", analise.analise_valor1.consumo[1]);
            ViewBag.ConsFPC = string.Format("{0:#,##0}", analise.analise_valor1.consumo[2]);
            ViewBag.ConsTotal = string.Format("{0:#,##0}", analise.analise_valor1.consumo[0] + analise.analise_valor1.consumo[1] + analise.analise_valor1.consumo[2]);

            return(0);
        }


        // Demanda Ativa Semanal
        private int Dem_Ativa_Semanal(CliGrupoUnidMedicoesDominio medicao, DateTime dataRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_SEMANAL relatorio = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise = new RELAT_SEMANAL_ANALISE();

            RELAT_SEMANAL relatorio_sim = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise_sim = new RELAT_SEMANAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = medicao.IDMedicao;

            // simulacao
            int TipoSimula = 0;

            // converte data hora
            DATAHORA data_hora = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, dataRelat);

            // calcula valores
            retorno = SmCalcDB_Energia_RelatSemanal((char)0, ref config_interface, (char)0, (char)TipoSimula, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_SEMANAL(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            ViewBag.Retorno = retorno;

            // verifica se erro
            if (retorno != 0)
            {
                // erro
                // -2 - erro : tipo de interface indefinida
                // -1 - erro : tipo de relatorio indefinido
                // 0  - ok e com dados
                // 1  - erro : nao foi possivel abrir o banco de dados
                // 2  - erro : nao existem dados
                // 3  - erro : leitura da configuracao da medicao
                // 4  - erro : leitura das tarifas de energia
                return (retorno);
            }

            // grafico
            var Demanda = new double[7, 98];
            var Periodo = new int[7, 98];
            var Datas = new string[98];
            var DatasN = new DateTime[7, 98];
            var Dias = new string[7];
            var Horas = new string[98];

            DateTime inicio = new DateTime();
            DateTime fim = new DateTime();

            double Dem_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 15min para a primeira barra
            strData = strData.AddMinutes(-15);

            int i = 0;
            int j = 0;
            int k = 0;

            for (i = 0; i < 98; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                Horas[i] = strData.ToString("HH:mm");

                // percorre dias
                for (k = 0; k < 7; k++)
                {
                    // data e hora para excel
                    DatasN[k, i] = strData.AddDays(k);

                    // guarda inicio e fim
                    if (i == 0 && k == 0)
                    {
                        inicio = strData;
                        fim = inicio.AddDays(6);
                    }

                    // dia da semana
                    if (i == 1)
                    {
                        Dias[k] = string.Format("{0:d}", strData.AddDays(k));
                    }

                    // verifica se primeira ou ultima barra
                    if (i == 0)
                    {
                        // zera
                        Demanda[k, i] = relatorio.registro[0].valor[k];
                        Periodo[k, i] = relatorio.registro[0].periodo[k];
                    }

                    if (i == 97)
                    {
                        // zera
                        Demanda[k, i] = relatorio.registro[95].valor[k];
                        Periodo[k, i] = relatorio.registro[95].periodo[k];
                    }

                    if (i >= 1 && i <= 96)
                    {
                        // copia
                        j = i - 1;

                        Demanda[k, i] = relatorio.registro[j].valor[k];
                        Periodo[k, i] = relatorio.registro[j].periodo[k];

                        // verifica se sem registro
                        if (relatorio.registro[j].periodo[k] == 3)
                        {
                            Demanda[k, i] = 0.0;
                        }

                        // verifica demanda maxima
                        if (Demanda[k, i] > Dem_max_grafico)
                            Dem_max_grafico = Demanda[k, i];
                    }
                }

                // proximo quinze minutos
                strData = strData.AddMinutes(15);
            }

            if (analise.analise_valor.referencia[0] > Dem_max_grafico)
                Dem_max_grafico = analise.analise_valor.referencia[0];

            if (analise.analise_valor.referencia[1] > Dem_max_grafico)
                Dem_max_grafico = analise.analise_valor.referencia[1];

            Dem_max_grafico = Dem_max_grafico * 1.1;

            if (Dem_max_grafico < 1.0)
            {
                Dem_max_grafico = 1.0;
            }

            ViewBag.DemMaxGrafico = Dem_max_grafico;

            ViewBag.Demanda = Demanda;
            ViewBag.Periodo = Periodo;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;
            ViewBag.Horas = Horas;

            // nome do relatorio
            ViewBag.NomeRelat = string.Format("Demanda Ativa");
            ViewBag.PeriodoRelat = string.Format("Semanal");

            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", dataRelat);
            ViewBag.DataAtualN = inicio;
            ViewBag.DataFinalN = fim;
            ViewBag.DataTextoAtual = string.Format("{0:d} - {1:d}", inicio, fim);

            // demanda
            ViewBag.Dem_MaxP = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[0]);
            DateTime Dem_MaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (Dem_MaxP_DataHora.Year != 2000)
                ViewBag.Dem_MaxP_DataHora = string.Format("{0:g}", Dem_MaxP_DataHora);
            else
                ViewBag.Dem_MaxP_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxP_DataHoraN = Dem_MaxP_DataHora;

            ViewBag.Dem_MaxFPI = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[1]);
            DateTime Dem_MaxFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[1]);
            if (Dem_MaxFPI_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPI_DataHora = string.Format("{0:g}", Dem_MaxFPI_DataHora);
            else
                ViewBag.Dem_MaxFPI_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxFPI_DataHoraN = Dem_MaxFPI_DataHora;

            ViewBag.Dem_MaxFPC = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[2]);
            DateTime Dem_MaxFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[2]);
            if (Dem_MaxFPC_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPC_DataHora = string.Format("{0:g}", Dem_MaxFPC_DataHora);
            else
                ViewBag.Dem_MaxFPC_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxFPC_DataHoraN = Dem_MaxFPC_DataHora;

            ViewBag.Dem_MinP = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[0]);
            DateTime Dem_MinP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[0]);
            if (Dem_MinP_DataHora.Year != 2000)
                ViewBag.Dem_MinP_DataHora = string.Format("{0:g}", Dem_MinP_DataHora);
            else
                ViewBag.Dem_MinP_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MinP_DataHoraN = Dem_MinP_DataHora;

            ViewBag.Dem_MinFPI = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[1]);
            DateTime Dem_MinFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[1]);
            if (Dem_MinFPI_DataHora.Year != 2000)
                ViewBag.Dem_MinFPI_DataHora = string.Format("{0:g}", Dem_MinFPI_DataHora);
            else
                ViewBag.Dem_MinFPI_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MinFPI_DataHoraN = Dem_MinFPI_DataHora;

            ViewBag.Dem_MinFPC = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[2]);
            DateTime Dem_MinFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[2]);
            if (Dem_MinFPC_DataHora.Year != 2000)
                ViewBag.Dem_MinFPC_DataHora = string.Format("{0:g}", Dem_MinFPC_DataHora);
            else
                ViewBag.Dem_MinFPC_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MinFPC_DataHoraN = Dem_MinFPC_DataHora;

            ViewBag.Dem_MedP = string.Format("{0:#,##0.0}", analise.analise_valor.medio[0]);
            ViewBag.Dem_MedFPI = string.Format("{0:#,##0.0}", analise.analise_valor.medio[1]);
            ViewBag.Dem_MedFPC = string.Format("{0:#,##0.0}", analise.analise_valor.medio[2]);

            ViewBag.Dem_ContratoP = string.Format("{0:#,##0}", analise.analise_valor.referencia[0]);
            ViewBag.ContratoP = analise.analise_valor.referencia[0];
            ViewBag.Dem_ContratoFP = string.Format("{0:#,##0}", analise.analise_valor.referencia[1]);
            ViewBag.ContratoFP = analise.analise_valor.referencia[1];

            ViewBag.Tol_ContratoP = string.Format("{0:0}", analise.analise_valor.tolerancia[0]);
            ViewBag.ToleranciaP = analise.analise_valor.referencia[0] * (1.0 + (analise.analise_valor.tolerancia[0] / 100.0));
            ViewBag.Tol_ContratoFP = string.Format("{0:0}", analise.analise_valor.tolerancia[1]);
            ViewBag.ToleranciaFP = analise.analise_valor.referencia[1] * (1.0 + (analise.analise_valor.tolerancia[1] / 100.0));

            // consumo
            ViewBag.ConsP = string.Format("{0:#,##0}", analise.analise_valor.consumo[0]);
            ViewBag.ConsFPI = string.Format("{0:#,##0}", analise.analise_valor.consumo[1]);
            ViewBag.ConsFPC = string.Format("{0:#,##0}", analise.analise_valor.consumo[2]);
            ViewBag.ConsTotal = string.Format("{0:#,##0}", analise.analise_valor.consumo[0] + analise.analise_valor.consumo[1] + analise.analise_valor.consumo[2]);

            return(0);
        }

        // Demanda Ativa Mensal
        private int Dem_Ativa_Mensal(CliGrupoUnidMedicoesDominio medicao, DateTime dataRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_MENSAL relatorio = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise = new RELAT_MENSAL_ANALISE();

            RELAT_MENSAL relatorio_sim = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise_sim = new RELAT_MENSAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = medicao.IDMedicao;

            // simulacao
            int TipoSimula = 0;

            // converte data hora
            DATAHORA data_hora = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, dataRelat);

            // calcula valores
            retorno = SmCalcDB_Energia_RelatMensal((char)0, ref config_interface, (char)0, (char)TipoSimula, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_MENSAL(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // numero de dias do mes
            int NumDiasMes = relatorio.num_dias;

            ViewBag.Retorno = retorno;

            // verifica se erro
            if (retorno != 0)
            {
                // erro
                // -2 - erro : tipo de interface indefinida
                // -1 - erro : tipo de relatorio indefinido
                // 0  - ok e com dados
                // 1  - erro : nao foi possivel abrir o banco de dados
                // 2  - erro : nao existem dados
                // 3  - erro : leitura da configuracao da medicao
                // 4  - erro : leitura das tarifas de energia
                return (retorno);
            }

            // grafico
            var DemandaP = new double[42];
            var DemandaFPI = new double[42];
            var DemandaFPC = new double[42];
            var ContratoP = new double[42];
            var ContratoFP = new double[42];
            var ToleranciaP = new double[42];
            var ToleranciaFP = new double[42];
            var Datas = new string[42];
            var DatasN = new DateTime[42];
            var Dias = new string[42];

            double Dem_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = strData.ToString("dd/MM");

                // proximo dia
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    DemandaP[i] = relatorio.registro[0].valor[0];
                    DemandaFPI[i] = relatorio.registro[0].valor[1];
                    DemandaFPC[i] = relatorio.registro[0].valor[2];
                    ContratoP[i] = relatorio.contrato_ponta[0];
                    ContratoFP[i] = relatorio.contrato_fponta[0];
                    ToleranciaP[i] = ContratoP[i] * (1.0 + (analise.analise_valor.tolerancia[0] / 100.0));
                    ToleranciaFP[i] = ContratoFP[i] * (1.0 + (analise.analise_valor.tolerancia[1] / 100.0));
                }

                if (i == (NumDiasMes + 1))
                {
                    // zera
                    DemandaP[i] = relatorio.registro[NumDiasMes - 1].valor[0];
                    DemandaFPI[i] = relatorio.registro[NumDiasMes - 1].valor[1];
                    DemandaFPC[i] = relatorio.registro[NumDiasMes - 1].valor[2];
                    ContratoP[i] = relatorio.contrato_ponta[NumDiasMes - 1];
                    ContratoFP[i] = relatorio.contrato_fponta[NumDiasMes - 1];
                    ToleranciaP[i] = ContratoP[i] * (1.0 + (analise.analise_valor.tolerancia[0] / 100.0));
                    ToleranciaFP[i] = ContratoFP[i] * (1.0 + (analise.analise_valor.tolerancia[1] / 100.0));
                }

                if (i >= 1 && i <= NumDiasMes)
                {
                    // copia
                    j = i - 1;

                    DemandaP[i] = relatorio.registro[j].valor[0];
                    DemandaFPI[i] = relatorio.registro[j].valor[1];
                    DemandaFPC[i] = relatorio.registro[j].valor[2];
                    ContratoP[i] = relatorio.contrato_ponta[j];
                    ContratoFP[i] = relatorio.contrato_fponta[j];
                    ToleranciaP[i] = ContratoP[i] * (1.0 + (analise.analise_valor.tolerancia[0] / 100.0));
                    ToleranciaFP[i] = ContratoFP[i] * (1.0 + (analise.analise_valor.tolerancia[1] / 100.0));

                    // verifica demanda maxima
                    if (DemandaP[i] > Dem_max_grafico)
                        Dem_max_grafico = DemandaP[i];

                    if (DemandaFPI[i] > Dem_max_grafico)
                        Dem_max_grafico = DemandaFPI[i];

                    if (DemandaFPC[i] > Dem_max_grafico)
                        Dem_max_grafico = DemandaFPC[i];

                    if (ContratoP[i] > Dem_max_grafico)
                        Dem_max_grafico = ContratoP[i];

                    if (ToleranciaP[i] > Dem_max_grafico)
                        Dem_max_grafico = ToleranciaP[i];

                    if (ContratoFP[i] > Dem_max_grafico)
                        Dem_max_grafico = ContratoFP[i];

                    if (ToleranciaFP[i] > Dem_max_grafico)
                        Dem_max_grafico = ToleranciaFP[i];
                }
            }

            Dem_max_grafico = Dem_max_grafico * 1.1;

            if (Dem_max_grafico < 1.0)
            {
                Dem_max_grafico = 1.0;
            }

            ViewBag.DemMaxGrafico = Dem_max_grafico;

            ViewBag.DemandaP = DemandaP;
            ViewBag.DemandaFPI = DemandaFPI;
            ViewBag.DemandaFPC = DemandaFPC;
            ViewBag.ContratoP = ContratoP;
            ViewBag.ContratoFP = ContratoFP;
            ViewBag.ToleranciaP = ToleranciaP;
            ViewBag.ToleranciaFP = ToleranciaFP;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            // nome do relatorio
            ViewBag.NomeRelat = string.Format("Demanda Ativa");
            ViewBag.PeriodoRelat = string.Format("Mensal");

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", dataRelat);
            ViewBag.DataAtualN = dataRelat;
            ViewBag.DataTextoAtual = string.Format("{0:Y}", dataRelat);

            ViewBag.NumDiasMes = NumDiasMes;

            // demanda
            ViewBag.Dem_MaxP = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[0]);
            DateTime Dem_MaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (Dem_MaxP_DataHora.Year != 2000)
                ViewBag.Dem_MaxP_DataHora = string.Format("{0:g}", Dem_MaxP_DataHora);
            else
                ViewBag.Dem_MaxP_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxP_DataHoraN = Dem_MaxP_DataHora;

            ViewBag.Dem_MaxFPI = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[1]);
            DateTime Dem_MaxFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[1]);
            if (Dem_MaxFPI_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPI_DataHora = string.Format("{0:g}", Dem_MaxFPI_DataHora);
            else
                ViewBag.Dem_MaxFPI_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxFPI_DataHoraN = Dem_MaxFPI_DataHora;

            ViewBag.Dem_MaxFPC = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[2]);
            DateTime Dem_MaxFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[2]);
            if (Dem_MaxFPC_DataHora.Year != 2000)
                ViewBag.Dem_MaxFPC_DataHora = string.Format("{0:g}", Dem_MaxFPC_DataHora);
            else
                ViewBag.Dem_MaxFPC_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MaxFPC_DataHoraN = Dem_MaxFPC_DataHora;

            ViewBag.Dem_MinP = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[0]);
            DateTime Dem_MinP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[0]);
            if (Dem_MinP_DataHora.Year != 2000)
                ViewBag.Dem_MinP_DataHora = string.Format("{0:g}", Dem_MinP_DataHora);
            else
                ViewBag.Dem_MinP_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MinP_DataHoraN = Dem_MinP_DataHora;

            ViewBag.Dem_MinFPI = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[1]);
            DateTime Dem_MinFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[1]);
            if (Dem_MinFPI_DataHora.Year != 2000)
                ViewBag.Dem_MinFPI_DataHora = string.Format("{0:g}", Dem_MinFPI_DataHora);
            else
                ViewBag.Dem_MinFPI_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MinFPI_DataHoraN = Dem_MinFPI_DataHora;

            ViewBag.Dem_MinFPC = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[2]);
            DateTime Dem_MinFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[2]);
            if (Dem_MinFPC_DataHora.Year != 2000)
                ViewBag.Dem_MinFPC_DataHora = string.Format("{0:g}", Dem_MinFPC_DataHora);
            else
                ViewBag.Dem_MinFPC_DataHora = "--/--/---- --:--";
            ViewBag.Dem_MinFPC_DataHoraN = Dem_MinFPC_DataHora;

            ViewBag.Dem_MedP = string.Format("{0:#,##0.0}", analise.analise_valor.medio[0]);
            ViewBag.Dem_MedFPI = string.Format("{0:#,##0.0}", analise.analise_valor.medio[1]);
            ViewBag.Dem_MedFPC = string.Format("{0:#,##0.0}", analise.analise_valor.medio[2]);

            ViewBag.Dem_ContratoP = string.Format("{0:#,##0}", analise.analise_valor.referencia[0]);
            ViewBag.Dem_ContratoFP = string.Format("{0:#,##0}", analise.analise_valor.referencia[1]);

            ViewBag.Tol_ContratoP = string.Format("{0:0}", analise.analise_valor.tolerancia[0]);
            ViewBag.Tol_ContratoFP = string.Format("{0:0}", analise.analise_valor.tolerancia[1]);

            // consumo
            ViewBag.ConsP = string.Format("{0:#,##0}", analise.analise_valor.consumo[0]);
            ViewBag.ConsFPI = string.Format("{0:#,##0}", analise.analise_valor.consumo[1]);
            ViewBag.ConsFPC = string.Format("{0:#,##0}", analise.analise_valor.consumo[2]);
            ViewBag.ConsTotal = string.Format("{0:#,##0}", analise.analise_valor.consumo[3]);

            return(0);
        }
    }
}
