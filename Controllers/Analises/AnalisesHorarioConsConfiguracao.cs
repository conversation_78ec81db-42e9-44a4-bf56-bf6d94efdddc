﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class AnalisesController
    {
        // GET: AnaliseHorarioConsumo - Configuracao
        public ActionResult AnaliseHorarioCons_Configuracao(int IDCliente)
        {
            // tela de ajuda - AnaliseHorarioConsumo
            CookieStore.SalvaCookie_String("PaginaAjuda", "Analise_HorarioCons_Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le analises do horario de consumo
            AnaliseHorarioConsMetodos analiseMetodos = new AnaliseHorarioConsMetodos();
            List<AnaliseHorarioConsDominio> listaAnalises = analiseMetodos.ListarPorIDCliente(IDCliente);

            return View(listaAnalises);
        }

        // GET: AnaliseHorarioConsumo - Editar
        public ActionResult AnaliseHorarioCons_Editar(int IDAnaliseHorarioCons)
        {
            int IDCliente = 0;

            // tela de ajuda - AnaliseHorarioConsumo
            CookieStore.SalvaCookie_String("PaginaAjuda", "Analise_HorarioCons_Editar");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "AnaliseHorarioCons");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes 
            Permissoes();

            // verifica se adicionando
            AnaliseHorarioConsDominio analise = new AnaliseHorarioConsDominio();
            if (IDAnaliseHorarioCons == 0)
            {
                // zera analise horario de consumo com default
                analise.IDCliente = ViewBag._IDCliente;
                analise.Nome = "";

                // IDCliente
                IDCliente = ViewBag._IDCliente;
            }
            else
            {
                // le analise horario de consumo
                AnaliseHorarioConsMetodos analiseMetodos = new AnaliseHorarioConsMetodos();
                analise = analiseMetodos.ListarPorId(IDAnaliseHorarioCons);

                // IDCliente
                IDCliente = analise.IDCliente;
            }

            // lista de medicoes habilitadas para o usuario
            List<int> ConfigMedicaoList_Usuario = new List<int>();
            ConfigMedicaoList_Usuario = ViewBag._ConfigMed;

            // verifica se existe lista medicoes
            if (ConfigMedicaoList_Usuario != null)
            {
                if (ConfigMedicaoList_Usuario.Count() == 0)
                {
                    ConfigMedicaoList_Usuario = null;
                }
            }

            // le medicoes da analise horario de consumo
            AnaliseHorarioConsGruposMedMetodos analiseGrupoMetodos = new AnaliseHorarioConsGruposMedMetodos();
            List<AnaliseHorarioConsGruposMedDominio> analiseGrupos = analiseGrupoMetodos.ListarPorIDAnalise(analise.IDAnaliseHorarioCons);

            // cria lista de medicoes desta analise
            List<int> ConfigMedicaoList_Analise = new List<int>();

            if (analiseGrupos != null)
            {
                foreach (AnaliseHorarioConsGruposMedDominio analiseGrupo in analiseGrupos)
                {
                    // le medicao
                    MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                    MedicoesDominio medicaoConf = medicaoMetodos.ListarPorId(analiseGrupo.IDMedicao);

                    if (medicaoConf != null)
                    {
                        // copia configuração Horário de Consumo
                        analiseGrupo.HoraIniRef = DateTime.ParseExact(medicaoConf.Funcionamento_Inicio, "HH:mm", CultureInfo.InvariantCulture);
                        analiseGrupo.HoraFimRef = DateTime.ParseExact(medicaoConf.Funcionamento_Fim, "HH:mm", CultureInfo.InvariantCulture);
                        analiseGrupo.DemandaResidual = medicaoConf.Funcionamento_DemMin;
                    }

                    ConfigMedicaoList_Analise.Add(analiseGrupo.IDMedicao);
                }
            }

            // le medicoes configuradas para a lista
            CliGateGrupoUnidMedicoesMetodos medicoesMetodos = new CliGateGrupoUnidMedicoesMetodos();
            List<CliGateGrupoUnidMedicoesDominio> medicoes = new List<CliGateGrupoUnidMedicoesDominio>();

            List<CliGateGrupoUnidMedicoesDominio> medicoes_utilizadas = new List<CliGateGrupoUnidMedicoesDominio>();
            List<CliGateGrupoUnidMedicoesDominio> medicoes_nao_utilizadas = new List<CliGateGrupoUnidMedicoesDominio>();

            if (ConfigMedicaoList_Analise != null)
            {
                // le medicoes de energia habilitadas para o usuario no cliente
                medicoes = medicoesMetodos.ListarPorIDCliente(IDCliente, ConfigMedicaoList_Usuario);

                // seleciona medicoes
                int contador;

                // percorre lista
                if (medicoes != null)
                {
                    for (contador = 0; contador < medicoes.Count(); contador++)
                    {
                        if (medicoes[contador].IDTipoMedicao == TIPO_MEDICAO.ENERGIA || medicoes[contador].IDTipoMedicao == TIPO_MEDICAO.ENERGIA_FORMULA)
                        {
                            // verifica se existe lista de medicoes da analise
                            if (ConfigMedicaoList_Analise != null)
                            {
                                // verifica se medicao esta habilitada para a analise
                                if (ConfigMedicaoList_Analise.Contains(medicoes[contador].IDMedicao))
                                {
                                    // copia medicao na lista de utilizada
                                    medicoes_utilizadas.Add(medicoes[contador]);
                                }
                                else
                                {
                                    // copia medicao na lista de nao utilizadas
                                    medicoes_nao_utilizadas.Add(medicoes[contador]);
                                }
                            }
                            else
                            {
                                // copia medicao na lista de nao utilizadas
                                medicoes_nao_utilizadas.Add(medicoes[contador]);
                            }
                        }
                    }
                }
            }

            ViewBag.Medicoes = medicoes_utilizadas;
            ViewBag.Medicoes2 = medicoes_nao_utilizadas;
            ViewBag.AnaliseGrupos = analiseGrupos;

            return View(analise);
        }

        // POST: AnaliseHorarioConsumo - Salvar
        [HttpPost]
        public ActionResult AnaliseHorarioCons_Salvar(AnaliseHorarioConsDominio analise, List<AnaliseHorarioConsGruposMedDominio> analiseGrupos)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                IDAnaliseHorarioCons = analise.IDAnaliseHorarioCons,
                erro = ""
            };

            // verifica se existe outra analise com o mesmo nome
            AnaliseHorarioConsMetodos analiseMetodos = new AnaliseHorarioConsMetodos();

            if (analiseMetodos.VerificarDuplicidade(analise))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    IDAnaliseHorarioCons = analise.IDAnaliseHorarioCons,
                    erro = "Análise do Horário de Consumo existente."
                };
            }

            else
            {
                // salva analise
                analiseMetodos.Salvar(analise);

                // pego IDAnaliseHorarioCons novamente (pois pode ter sido insercao)
                AnaliseHorarioConsDominio novaAnalise = new AnaliseHorarioConsDominio();

                // verifica se alterando
                if (analise.IDAnaliseHorarioCons > 0)
                {
                    // caso alterando apenas copia do DB
                    novaAnalise = analiseMetodos.ListarPorId(analise.IDAnaliseHorarioCons);
                }

                else
                {
                    // caso inserindo faz leitura por IDCliente e Nome para pegar novo ID
                    novaAnalise = analiseMetodos.ListarPorNomeCliente(analise.IDCliente, analise.Nome);
                }


                returnedData = new
                {
                    status = "OK",
                    IDAnaliseHorarioCons = novaAnalise.IDAnaliseHorarioCons,
                    erro = ""
                };

                // salva analise horario de consumo grupo
                if (novaAnalise != null)
                {
                    AnaliseHorarioConsGruposMedMetodos analiseGruposMetodos = new AnaliseHorarioConsGruposMedMetodos();

                    // lista grupo de medicoes atual 
                    List<AnaliseHorarioConsGruposMedDominio> analiseGruposAntiga = analiseGruposMetodos.ListarPorIDAnalise(analise.IDAnaliseHorarioCons);

                    // salva analise grupo
                    if (analiseGrupos != null)
                    {
                        // percorre lista e salva
                        foreach (AnaliseHorarioConsGruposMedDominio analiseGrupo in analiseGrupos)
                        {
                            // verifica se adicionando
                            if (analiseGrupo.IDAnaliseHorarioConsGruposMed == 0)
                            {
                                // caso adicionando preenche campos que faltam com default
                                analiseGrupo.IDAnaliseHorarioCons = novaAnalise.IDAnaliseHorarioCons;
                                analiseGrupo.CalcResidualAuto = false;
                                analiseGrupo.DemandaResidual = 0.0;
                                analiseGrupo.HoraIniRef = new DateTime(2000, 1, 1, 8, 0, 0);
                                analiseGrupo.HoraFimRef = new DateTime(2000, 1, 1, 18, 0, 0);
                            }

                            // salva grupo
                            analiseGruposMetodos.Salvar(analiseGrupo);
                        }
                    }
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: AnaliseHorarioConsumo - Excluir
        public ActionResult AnaliseHorarioCons_Excluir(int IDAnaliseHorarioCons)
        {
            // apaga a analise
            AnaliseHorarioConsMetodos analiseMetodos = new AnaliseHorarioConsMetodos();
            analiseMetodos.Excluir(IDAnaliseHorarioCons);

            // lista grupos de medicoes
            AnaliseHorarioConsGruposMedMetodos analiseGruposMetodos = new AnaliseHorarioConsGruposMedMetodos();
            List<AnaliseHorarioConsGruposMedDominio> analiseGrupos = analiseGruposMetodos.ListarPorIDAnalise(IDAnaliseHorarioCons);

            // apaga grupos de EA
            AnaliseHorarioConsGruposMedEAMetodos analiseGruposEAMetodos = new AnaliseHorarioConsGruposMedEAMetodos();
            foreach (AnaliseHorarioConsGruposMedDominio analiseGrupo in analiseGrupos)
            {
                analiseGruposEAMetodos.Excluir(analiseGrupo.IDAnaliseHorarioConsGruposMed);
            }

            // apaga grupos de medicoes
            analiseGruposMetodos.ExcluirPorAnalise(IDAnaliseHorarioCons);


            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}