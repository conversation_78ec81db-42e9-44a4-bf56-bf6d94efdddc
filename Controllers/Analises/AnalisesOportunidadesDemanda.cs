﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class AnalisesController
    {

        // GET: Oportunidades Demanda
        public ActionResult Oportunidades_Demanda(int IDCliente, int tipo_arquivo = 0)
        {
            // relatório consolidado de oportunidades de demanda 
            return (Oportunidades_Demanda_Show(IDCliente, tipo_arquivo));
        }

        // GET: Oportunidades Demanda 
        private ActionResult Oportunidades_Demanda_Show(int IDCliente, int tipo_arquivo = 0)
        {
            // tela de ajuda - fatura
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie 
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Analises");

            // le cookies
            LeCookies_SmartEnergy();

            // data inicio
            DateTime data_hora_ini = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 0, 0);

            // data fim
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            // le cookie datahora
            DateTime data_hora_ini_cookie = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime data_hora_fim_cookie = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // verifica se existe datahora no cookie
            if (data_hora_ini_cookie.Year != 2000 && data_hora_fim_cookie.Year != 2000)
            {
                // copia data do cookie 
                data_hora_ini = data_hora_ini_cookie;
                data_hora_fim = data_hora_fim_cookie;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", data_hora_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", data_hora_fim);

            // data e hora atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", data_hora_ini);
            ViewBag.DataTextoAtual = string.Format("{0:MMMM/yyyy}", data_hora_ini);

            ViewBag.DataAtualN = data_hora_ini;
            ViewBag.DataFinalN = data_hora_fim;

            // preenche observacoes
            List<ObservacaoMedicaoDominio> observacoes = new List<ObservacaoMedicaoDominio>();
            ViewBag.Observacoes = observacoes;

            // le tags
            List<ObservacaoTagsDominio> tags = new List<ObservacaoTagsDominio>();
            ViewBag.tags = tags;

            // observacao
            ObservacaoMedicaoDominio observacao = new ObservacaoMedicaoDominio();
            ViewBag.observacao = observacao;

            // tipo acesso
            ListaTiposMetodos listaMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listaTipoAcesso = listaMetodos.ListarTodos("TipoAcesso");
            ViewBag.listaTipoAcesso = listaTipoAcesso;

            // tipo oportunidades
            string TipoOportunidadesStr = CookieStore.LeCookie_String("Analise_TipoOportunidades");
            int TipoOportunidades = 0;

            // verifica se foi mudado o tab
            if (TipoOportunidadesStr.Length == 8)
            {
                string aux_str = TipoOportunidadesStr.Substring(7, 1);
                TipoOportunidades = int.Parse(aux_str) - 1;
            }

            ViewBag.TipoOportunidades = TipoOportunidades;

            return View();
        }

        // GET: Oportunidades Demanda - Atualizar
        public ActionResult Oportunidades_Demanda_Atualizar(int Navegacao, string Data)
        {
            // caso forcar data
            if (Navegacao == 100)
            {
                // pega data
                DateTime dateValueIni = DateTime.Parse(Data);

                // data inicio
                DateTime data_hora_ini = new DateTime(dateValueIni.Year, dateValueIni.Month, 1, 0, 0, 0);

                // data fim
                DateTime data_hora_fim = data_hora_ini.AddMonths(1);

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Fatura_DataIni", data_hora_ini);
                CookieStore.SalvaCookie_Datahora("Fatura_DataFim", data_hora_fim);
            }

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;

            // le cookie datahora
            DateTime datahora_cookie_ini = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -3:    // mes anterior

                    datahora_cookie_ini = datahora_cookie_ini.AddMonths(-1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(-1);
                    break;

                case 3:    // mes seguinte

                    datahora_cookie_ini = datahora_cookie_ini.AddMonths(1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(1);
                    break;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", datahora_cookie_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", datahora_cookie_fim);

            // data e hora atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", datahora_cookie_ini);
            ViewBag.DataTextoAtual = string.Format("{0:MMMM/yyyy}", datahora_cookie_ini);

            ViewBag.DataAtualN = datahora_cookie_ini;
            ViewBag.DataFinalN = datahora_cookie_fim;

            //
            // COMENTÁRIOS
            //

            // preenche observacoes
            List<ObservacaoMedicaoDominio> observacoes = new List<ObservacaoMedicaoDominio>();
            ViewBag.Observacoes = observacoes;

            // le tags
            List<ObservacaoTagsDominio> tags = new List<ObservacaoTagsDominio>();
            ViewBag.tags = tags;

            // observacao
            ObservacaoMedicaoDominio observacao = new ObservacaoMedicaoDominio();
            ViewBag.observacao = observacao;

            // tipo acesso
            ListaTiposMetodos listaMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listaTipoAcesso = listaMetodos.ListarTodos("TipoAcesso");
            ViewBag.listaTipoAcesso = listaTipoAcesso;

            // tipo oportunidades
            string TipoOportunidadesStr = CookieStore.LeCookie_String("Analise_TipoOportunidades");
            int TipoOportunidades = 0;

            // verifica se foi mudado o tab
            if (TipoOportunidadesStr.Length == 8)
            {
                string aux_str = TipoOportunidadesStr.Substring(7, 1);
                TipoOportunidades = int.Parse(aux_str) - 1;
            }

            ViewBag.TipoOportunidades = TipoOportunidades;


            // retorna status
            return Json(0, JsonRequestBehavior.AllowGet);
        }

        // GET: Oportunidades Demanda - Print
        public ActionResult Oportunidades_Demanda_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Oportunidades_Demanda_Show(IDCliente);

            // resultado
            ViewBag.lista_oportunidades = lista_oportunidades.Keys.Contains(id) ? lista_oportunidades[id] : null;
            ViewBag.lista_oportunidades_multa = lista_oportunidades_multa.Keys.Contains(id) ? lista_oportunidades_multa[id] : null;

            // imprime
            return View();
        }

        // GET: Oportunidades de Demanda - EMAIL
        public async Task<ActionResult> Oportunidades_Demanda_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Oportunidades_Demanda_Show(IDCliente);

            // resultado
            ViewBag.lista_oportunidades = lista_oportunidades.Keys.Contains(id) ? lista_oportunidades[id] : null;
            ViewBag.lista_oportunidades_multa = lista_oportunidades_multa.Keys.Contains(id) ? lista_oportunidades_multa[id] : null;

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Oportunidades_Demanda_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDCliente, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_Oportunidades_Demanda_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Landscape,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "OportunidadesDemandaEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Oportunidades Demanda - PDF
        public ActionResult Oportunidades_Demanda_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Oportunidades_Demanda_Show(IDCliente);

            // resultado
            ViewBag.lista_oportunidades = lista_oportunidades.Keys.Contains(id) ? lista_oportunidades[id] : null;
            ViewBag.lista_oportunidades_multa = lista_oportunidades_multa.Keys.Contains(id) ? lista_oportunidades_multa[id] : null;

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Oportunidades_Demanda_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDCliente, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_Oportunidades_Demanda_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Landscape,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Oportunidades de Demanda - XLS
        public ActionResult Oportunidades_Demanda_XLS()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Oportunidades_Demanda_Show(IDCliente);

            // resultado
            ViewBag.lista_oportunidades = lista_oportunidades.Keys.Contains(id) ? lista_oportunidades[id] : null;
            ViewBag.lista_oportunidades_multa = lista_oportunidades_multa.Keys.Contains(id) ? lista_oportunidades_multa[id] : null;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_Oportunidades_Demanda(IDCliente);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Oportunidades_Demanda_{0:000000}_{1:yyyyMMddHHmm}.xls", IDCliente, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Oportunidades de Demanda - XLS Download
        [HttpGet]
        public virtual ActionResult Oportunidades_Demanda_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // estruturas
        private static IDictionary<Guid, int> tasks_oportunidades = new Dictionary<Guid, int>();
        private List<OPORTUNIDADES_DEMANDA> lista_oportunidades_tmp = new List<OPORTUNIDADES_DEMANDA>();
        private List<OPORTUNIDADES_DEMANDA> lista_oportunidades_multa_tmp = new List<OPORTUNIDADES_DEMANDA>();
        private static IDictionary<Guid, List<OPORTUNIDADES_DEMANDA>> lista_oportunidades = new Dictionary<Guid, List<OPORTUNIDADES_DEMANDA>>();
        private static IDictionary<Guid, List<OPORTUNIDADES_DEMANDA>> lista_oportunidades_multa = new Dictionary<Guid, List<OPORTUNIDADES_DEMANDA>>();

        public ActionResult Oportunidades_Demanda_IniciaCalculo()
        {
            // taskID
            Guid taskId = Guid.NewGuid();
            tasks_oportunidades.Add(taskId, 0);

            // salva cookie
            CookieStore.SalvaCookie_GUID("taskID", taskId);

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;

            // le cookie datahora
            DateTime datahora_cookie_ini = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Fatura_DataFim");


            // limpa listas
            lista_oportunidades_tmp.Clear();
            lista_oportunidades_multa_tmp.Clear();

            // task
            Task.Factory.StartNew(() =>
            {
                // le medicoes
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                List<MedicoesDominio> medicoes = medicoesMetodos.ListarTodos(IDCliente, ViewBag._ConfigMed);

                // lista distribuidora 
                AgentesDistribuidoraMetodos distribuidorasMetodos = new AgentesDistribuidoraMetodos();
                AgentesDistribuidoraDominio distribuidora = new AgentesDistribuidoraDominio();

                // contrato
                HistoricoContratosMetodos contratosMetodos = new HistoricoContratosMetodos();
                HistoricoContratosDominio contrato = new HistoricoContratosDominio();

                // demanda maxima
                EN_Metodos enMetodos = new EN_Metodos();

                // lista de erros
                var listaErros = new List<string>();

                // barra de progresso
                int total = 0;
                int atual = 0;

                // percorre medicoes
                if (medicoes != null)
                {
                    // barra de progresso
                    total = medicoes.Count();

                    // percorre medicoes
                    foreach (MedicoesDominio medicao in medicoes)
                    {
                        // update task progress
                        double progresso = ((double)atual / (double)total) * 100;
                        tasks_oportunidades[taskId] = (int)progresso;
                        atual++;

                        // verifica se nao eh energia real ou medição principal (unidade consumidora)
                        if (medicao.IDTipoMedicao != TIPO_MEDICAO.ENERGIA || medicao.IDCategoriaMedicao != CATEGORIA_MEDICAO.PRINCIPAL)
                        {
                            continue;
                        }

                        //
                        // OPORTUNIDADES DE REDUÇÃO DE CONTRATO
                        //

                        // coloca resultado na lista temporaria
                        OPORTUNIDADES_DEMANDA oportunidade = new OPORTUNIDADES_DEMANDA();
                        oportunidade.IDMedicao = medicao.IDMedicao;
                        oportunidade.Nome_Medicao = medicao.Nome;
                        oportunidade.IDEstruturaTarifaria = medicao.IDEstruturaTarifaria;
                        oportunidade.IDAgenteDistribuidora = medicao.IDAgenteDistribuidora;
                        oportunidade.IDTipoSubGrupo = medicao.IDTipoSubgrupo;
                        oportunidade.Contrato = "---";

                        oportunidade.DemandaRegistradaP = 0;
                        oportunidade.DemandaRegistradaFP = 0;

                        oportunidade.DiferencaDemandaP = 0.0;
                        oportunidade.DiferencaDemandaFP = 0.0;
                        oportunidade.FatorUtilizacaoP = 0.0;
                        oportunidade.FatorUtilizacaoFP = 0.0;

                        oportunidade.OportunidadeP = 0.0;
                        oportunidade.OportunidadeFP = 0.0;

                        // inicialmente copia valores de contrato da medicao
                        oportunidade.ContratoDemP = medicao.ContratoDemP;
                        oportunidade.ContratoDemFP = medicao.ContratoDemFP;
                        oportunidade.ToleranciaP = medicao.ToleranciaDemP;
                        oportunidade.ToleranciaFP = medicao.ToleranciaDemFP;

                        //
                        // OPORTUNIDADES DE ECONOMIA EM MULTAS
                        //

                        // coloca resultado na lista temporaria
                        OPORTUNIDADES_DEMANDA oportunidade_multa = new OPORTUNIDADES_DEMANDA();
                        oportunidade_multa.IDMedicao = medicao.IDMedicao;
                        oportunidade_multa.Nome_Medicao = medicao.Nome;
                        oportunidade_multa.IDEstruturaTarifaria = medicao.IDEstruturaTarifaria;
                        oportunidade_multa.IDAgenteDistribuidora = medicao.IDAgenteDistribuidora;
                        oportunidade_multa.IDTipoSubGrupo = medicao.IDTipoSubgrupo;
                        oportunidade_multa.Contrato = "---";

                        oportunidade_multa.DemandaRegistradaP = 0;
                        oportunidade_multa.DemandaRegistradaFP = 0;

                        oportunidade_multa.DiferencaDemandaP = 0.0;
                        oportunidade_multa.DiferencaDemandaFP = 0.0;
                        oportunidade.FatorUtilizacaoP = 0.0;
                        oportunidade.FatorUtilizacaoFP = 0.0; ;

                        oportunidade_multa.OportunidadeP = 0.0;
                        oportunidade_multa.OportunidadeFP = 0.0;

                        // inicialmente copia valores de contrato da medicao
                        oportunidade_multa.ContratoDemP = medicao.ContratoDemP;
                        oportunidade_multa.ContratoDemFP = medicao.ContratoDemFP;
                        oportunidade_multa.ToleranciaP = medicao.ToleranciaDemP;
                        oportunidade_multa.ToleranciaFP = medicao.ToleranciaDemFP;

                        // distribuidora
                        distribuidora = distribuidorasMetodos.ListarPorId(oportunidade.IDAgenteDistribuidora);
                        oportunidade.Nome_Distribuidora = distribuidora.Nome;
                        oportunidade_multa.Nome_Distribuidora = distribuidora.Nome;

                        // verifica se configurado é Cativo Azul
                        if (medicao.IDContratoMedicao == TIPOCONTRATO.CATIVO && medicao.IDEstruturaTarifaria == TIPOESTR_TAR.THS_AZUL)
                        {
                            oportunidade.Contrato = "Cativo \n THS Azul";
                            oportunidade_multa.Contrato = "Cativo \n THS Azul";
                        }

                        // verifica se configurado é Cativo Verde
                        if (medicao.IDContratoMedicao == TIPOCONTRATO.CATIVO && medicao.IDEstruturaTarifaria == TIPOESTR_TAR.THS_VERDE)
                        {
                            oportunidade.Contrato = "Cativo \n THS Verde";
                            oportunidade_multa.Contrato = "Cativo \n THS Verde";
                        }

                        // verifica se configurado é Livre Azul
                        if (medicao.IDContratoMedicao == TIPOCONTRATO.LIVRE && medicao.IDEstruturaTarifaria == TIPOESTR_TAR.THS_AZUL)
                        {
                            oportunidade.Contrato = "Livre \n THS Azul";
                            oportunidade_multa.Contrato = "Livre \n THS Azul";
                        }

                        // verifica se configurado é Livre Verde
                        if (medicao.IDContratoMedicao == TIPOCONTRATO.LIVRE && medicao.IDEstruturaTarifaria == TIPOESTR_TAR.THS_VERDE)
                        {
                            oportunidade.Contrato = "Livre \n THS Verde";
                            oportunidade_multa.Contrato = "Livre \n THS Verde";
                        }

                        //
                        // Oportunidade de Redução do Contrato de Demanda
                        //

                        // Calcula a diferença entre a demanda contratada e a demanda máxima registrada
                        // Se o percentual da demanda registrada do período em relação a demanda contratada do período for menor que 95%, calcula o valor da diferença em R$
                        // Diferença entre registrada e contrada do período * Tarifa do período     

                        // busca vigente mais proximo do mes analisado 
                        contrato = contratosMetodos.ListarPorIDMedicaoMaisRecenteData(medicao.IDMedicao, datahora_cookie_ini);

                        // copia valor de contrato demanda ponta 
                        if (contrato != null && contrato.ContratoDemP > 0)
                        {
                            oportunidade.ContratoDemP = contrato.ContratoDemP;
                            oportunidade_multa.ContratoDemP = contrato.ContratoDemP;
                        }

                        // copia valor de contrato demanda fora ponta
                        if (contrato != null && contrato.ContratoDemFP > 0)
                        {
                            if (medicao.IDEstruturaTarifaria == TIPOESTR_TAR.THS_AZUL)
                            {
                                oportunidade.ContratoDemFP = contrato.ContratoDemFP;
                                oportunidade_multa.ContratoDemFP = contrato.ContratoDemFP;

                                // demanda maxima registrada ponta
                                oportunidade.DemandaRegistradaP = (int)enMetodos.DemandaMaximaPeriodo(medicao.IDCliente, medicao.IDMedicao, datahora_cookie_ini, datahora_cookie_fim, 0);
                                oportunidade_multa.DemandaRegistradaP = (int)enMetodos.DemandaMaximaPeriodo(medicao.IDCliente, medicao.IDMedicao, datahora_cookie_ini, datahora_cookie_fim, 0);

                                // demanda maxima registrada fora ponta
                                oportunidade.DemandaRegistradaFP = (int)enMetodos.DemandaMaximaPeriodo(medicao.IDCliente, medicao.IDMedicao, datahora_cookie_ini, datahora_cookie_fim, 1);
                                oportunidade_multa.DemandaRegistradaFP = (int)enMetodos.DemandaMaximaPeriodo(medicao.IDCliente, medicao.IDMedicao, datahora_cookie_ini, datahora_cookie_fim, 1);
                            }

                            if (medicao.IDEstruturaTarifaria == TIPOESTR_TAR.THS_VERDE)
                            {
                                oportunidade.ContratoDemFP = contrato.ContratoDemP;
                                oportunidade_multa.ContratoDemFP = contrato.ContratoDemP;

                                // demanda maxima registrada ponta
                                oportunidade.DemandaRegistradaP = (int)enMetodos.DemandaMaximaPeriodo(medicao.IDCliente, medicao.IDMedicao, datahora_cookie_ini, datahora_cookie_fim);
                                oportunidade_multa.DemandaRegistradaP = (int)enMetodos.DemandaMaximaPeriodo(medicao.IDCliente, medicao.IDMedicao, datahora_cookie_ini, datahora_cookie_fim);
                            }
                        }

                        // diferença demanda ponta
                        oportunidade.DiferencaDemandaP = oportunidade.DemandaRegistradaP - oportunidade.ContratoDemP;
                        oportunidade_multa.DiferencaDemandaP = oportunidade_multa.DemandaRegistradaP - oportunidade_multa.ContratoDemP;

                        // diferença demanda fora de ponta
                        oportunidade.DiferencaDemandaFP = oportunidade.DemandaRegistradaFP - oportunidade.ContratoDemFP;
                        oportunidade_multa.DiferencaDemandaFP = oportunidade_multa.DemandaRegistradaFP - oportunidade_multa.ContratoDemFP;

                        // fator de utilização ponta
                        if (oportunidade.ContratoDemP != 0)
                        {
                            oportunidade.FatorUtilizacaoP = ((double)oportunidade.DemandaRegistradaP / (double)oportunidade.ContratoDemP) * 100;
                            oportunidade_multa.FatorUtilizacaoP = ((double)oportunidade_multa.DemandaRegistradaP / (double)oportunidade_multa.ContratoDemP) * 100;
                        }

                        // fator de utilização fora ponta
                        if (oportunidade.ContratoDemFP != 0)
                        {
                            oportunidade.FatorUtilizacaoFP = ((double)oportunidade.DemandaRegistradaFP / (double)oportunidade.ContratoDemFP) * 100;
                            oportunidade_multa.FatorUtilizacaoFP = ((double)oportunidade.DemandaRegistradaFP / (double)oportunidade_multa.ContratoDemFP) * 100;
                        }

                        // tarifa azul
                        if (medicao.IDEstruturaTarifaria == TIPOESTR_TAR.THS_AZUL)
                        {
                            Tarifas_AZMetodos tarifas_AZMetodos = new Tarifas_AZMetodos();
                            Tarifas_AZDominio tarifa_azul = new Tarifas_AZDominio();
                            tarifa_azul = tarifas_AZMetodos.ListarMaisRecenteData(medicao.IDAgenteDistribuidora, medicao.IDTipoSubgrupo, datahora_cookie_fim);

                            // verifica se encontrou tarifa
                            if (tarifa_azul != null)
                            {
                                // calcula oportunidade de redução de contrato 
                                oportunidade.OportunidadeP = Math.Abs(oportunidade.DiferencaDemandaP) * tarifa_azul.Tardp;
                                oportunidade.OportunidadeFP = Math.Abs(oportunidade.DiferencaDemandaFP) * tarifa_azul.Tardf;

                                // verifica se ultrapassou tolerancia ponta
                                if (oportunidade_multa.FatorUtilizacaoP - 100 > medicao.ToleranciaDemP)
                                {
                                    // calcula oportunidade de economia em multa ponta
                                    oportunidade_multa.OportunidadeP = oportunidade_multa.DiferencaDemandaP * tarifa_azul.Tardp_u;
                                }

                                // verifica se ultrapassou tolerancia fora ponta
                                if (oportunidade_multa.FatorUtilizacaoFP - 100 > medicao.ToleranciaDemFP)
                                {
                                    // calcula oportunidade de economia em multa fora ponta
                                    oportunidade_multa.OportunidadeFP = oportunidade_multa.DiferencaDemandaFP * tarifa_azul.Tardf_u;
                                }
                            }
                        }

                        // tarifa verde 
                        if (medicao.IDEstruturaTarifaria == TIPOESTR_TAR.THS_VERDE)
                        {
                            Tarifas_VDMetodos tarifas_VDMetodos = new Tarifas_VDMetodos();
                            Tarifas_VDDominio tarifa_verde = new Tarifas_VDDominio();
                            tarifa_verde = tarifas_VDMetodos.ListarMaisRecenteData(medicao.IDAgenteDistribuidora, medicao.IDTipoSubgrupo, datahora_cookie_fim);

                            // verifica se encontrou tarifa
                            if (tarifa_verde != null)
                            {
                                // calcula oportunidade de redução de contrato 
                                oportunidade.OportunidadeP = Math.Abs(oportunidade.DiferencaDemandaP) * tarifa_verde.Tard;

                                if (oportunidade_multa.ContratoDemP != 0)
                                {
                                    // verifica se ultrapassou tolerancia ponta
                                    if (oportunidade_multa.FatorUtilizacaoP - 100 > medicao.ToleranciaDemP)
                                    {
                                        // calcula oportunidade de economia em multa ponta
                                        oportunidade_multa.OportunidadeP = oportunidade_multa.DiferencaDemandaP * tarifa_verde.Tard_u;
                                    }
                                }
                            }
                        }

                        // 
                        // OBSERVACOES (COMENTÁRIOS) 
                        //

                        // preenche valores na estrutura
                        oportunidade.IDObservacao = 0;
                        oportunidade.Observacao_Texto = "";
                        oportunidade.Observacao_DataHora = new DateTime(2000, 1, 1, 0, 0, 0);
                        oportunidade.Observacao_Badge = "info";
                        oportunidade.Observacao_IDTag = 0;
                        oportunidade.Observacao_Tag = "";

                        oportunidade_multa.IDObservacao = 0;
                        oportunidade_multa.Observacao_Texto = "";
                        oportunidade_multa.Observacao_DataHora = new DateTime(2000, 1, 1, 0, 0, 0);
                        oportunidade_multa.Observacao_Badge = "info";
                        oportunidade_multa.Observacao_IDTag = 0;
                        oportunidade_multa.Observacao_Tag = "";

                        // observacao
                        ObservacaoMedicaoMetodos obsMetodos = new ObservacaoMedicaoMetodos();

                        // lista observacao mais recente do mes analisado e passa dados para estrutura
                        ObservacaoMedicaoDominio obs = obsMetodos.ListarPorPeriodo(medicao.IDMedicao, datahora_cookie_ini, datahora_cookie_fim)
                            .OrderByDescending(x => x.DataHora)
                            .FirstOrDefault();

                        // tipos acesso
                        ListaTiposMetodos listaMetodos = new ListaTiposMetodos();

                        // le tags
                        List<ObservacaoTagsDominio> tags = listaMetodos.ListarTodos_ObservacaoTags();

                        // verifica se tem observacao 
                        if (obs != null && obs.IDObservacao > 0)
                        {
                            oportunidade.IDObservacao = obs.IDObservacao;
                            oportunidade.Observacao_Texto = obs.Observacao;
                            oportunidade.Observacao_IDTag = obs.IDTag;
                            oportunidade.Observacao_DataHora = obs.DataHora;
                            if (tags.Exists(x => x.IDTag == obs.IDTag)) oportunidade.Observacao_Tag = tags.Find(x => x.IDTag == obs.IDTag).Descricao;

                            // badge
                            oportunidade.Observacao_Badge = "primary";

                            if (obs.IDTag > 0 && obs.IDTag < 100)
                                oportunidade.Observacao_Badge = "danger";

                            if (obs.IDTag >= 100 && obs.IDTag < 200)
                                oportunidade.Observacao_Badge = "warning";

                            oportunidade_multa.IDObservacao = obs.IDObservacao;
                            oportunidade_multa.Observacao_Texto = obs.Observacao;
                            oportunidade_multa.Observacao_IDTag = obs.IDTag;
                            oportunidade_multa.Observacao_DataHora = obs.DataHora;
                            if (tags.Exists(x => x.IDTag == obs.IDTag)) oportunidade_multa.Observacao_Tag = tags.Find(x => x.IDTag == obs.IDTag).Descricao;

                            // badge
                            oportunidade_multa.Observacao_Badge = "primary";

                            if (obs.IDTag > 0 && obs.IDTag < 100)
                                oportunidade_multa.Observacao_Badge = "danger";

                            if (obs.IDTag >= 100 && obs.IDTag < 200)
                                oportunidade_multa.Observacao_Badge = "warning";
                        }

                        // coloca resultado na lista temporária
                        lista_oportunidades_tmp.Add(oportunidade);

                        // coloca resultado na lista temporaria
                        lista_oportunidades_multa_tmp.Add(oportunidade_multa);
                    }
                }

                // coloca resultado na lista
                lista_oportunidades.Add(taskId, new List<OPORTUNIDADES_DEMANDA>(lista_oportunidades_tmp));

                // coloca resultado na lista
                lista_oportunidades_multa.Add(taskId, new List<OPORTUNIDADES_DEMANDA>(lista_oportunidades_multa_tmp));

                // terminou
                tasks_oportunidades.Remove(taskId);
            });

            // retorna status
            return Json(taskId, JsonRequestBehavior.AllowGet);
        }

        public ActionResult Oportunidades_Demanda_Progress(Guid id)
        {
            return Json(tasks_oportunidades.Keys.Contains(id) ? tasks_oportunidades[id] : 100, JsonRequestBehavior.AllowGet);
        }

        public PartialViewResult _Oportunidades_Demanda(Guid id)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // resultado
            ViewBag.lista_oportunidades = lista_oportunidades.Keys.Contains(id) ? lista_oportunidades[id] : null;

            // resultado
            ViewBag.lista_oportunidades_multa = lista_oportunidades_multa.Keys.Contains(id) ? lista_oportunidades_multa[id] : null;

            return PartialView();
        }

        // Oportunidades Demanda - XLS
        private HSSFWorkbook XLS_Oportunidades_Demanda(int IDCliente)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            ICellStyle _textoBorderCellStyle = criaEstiloXLS(workbook, 100, true);
            ICellStyle _1CellBorderStyle = criaEstiloXLS(workbook, 1, true);
            ICellStyle _2CellBorderStyle = criaEstiloXLS(workbook, 2, true);

            // verde
            ICellStyle _1CellStyleVerde = criaEstiloXLS(workbook, 1, false, true, IndexedColors.White.Index, IndexedColors.Green.Index);

            // vermelho 
            ICellStyle _1CellStyleVermelho = criaEstiloXLS(workbook, 1, false, true, IndexedColors.White.Index, IndexedColors.Red.Index);

            // amarelo
            ICellStyle _1CellStyleAmarelo = criaEstiloXLS(workbook, 1, false, true, IndexedColors.Grey80Percent.Index, IndexedColors.Yellow.Index);

            //
            // MEDICOES
            //

            // adiciona linhas
            List<OPORTUNIDADES_DEMANDA> oportunidades_resultado = ViewBag.lista_oportunidades;            

            // cria planilha
            var sheet = workbook.CreateSheet("Redução de Contratos");
            int i;
            IRow row;
            int rowIndex = 0;

            // total 
            row = sheet.CreateRow(rowIndex);

            // total
            textoCelulaXLS(row, 9, "Total Oportunidades Demandas Contratadas (R$)", _negritoCellStyle);

            // mescla celulas
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 9, 10));

            // total
            double total_oportunidadesP = 0.0;
            double total_oportunidadesFP = 0.0;

            if (oportunidades_resultado != null)
            {
                total_oportunidadesP = oportunidades_resultado.Where(x => x.ContratoDemP > 0 && (((double)x.DemandaRegistradaP / (double)x.ContratoDemP) < 0.95) && (((double)x.DemandaRegistradaP / (double)x.ContratoDemP) > 0.0)).Sum(x => x.OportunidadeP);
                total_oportunidadesFP = oportunidades_resultado.Where(x => x.ContratoDemFP > 0 && (((double)x.DemandaRegistradaFP / (double)x.ContratoDemFP) < 0.95) && (((double)x.DemandaRegistradaFP / (double)x.ContratoDemFP) > 0.0)).Sum(x => x.OportunidadeFP);
            }

            // total ponta 
            numeroCelulaXLS(row, 11, total_oportunidadesP, _2CellStyle);

            // total fponta 
            numeroCelulaXLS(row, 12, total_oportunidadesFP, _2CellStyle);

            // cabecalho
            string[] cabecalho = { "Medições", "Estrutura Tarifária", "Distribuidora", "Demanda Contrato Ponta (kW)", "Demanda Registrada Ponta (kW)", "Diferença Ponta (kW)", "Fator de Utilização Ponta (%)", "Demanda Contrato Fora Ponta (kW)", "Demanda Registrada Fora Ponta (kW)", "Diferença Fora Ponta (kW)", "Fator de Utilização Fora Ponta (%)", "Oportunidade Ponta (R$)", "Oportunidade Fora Ponta (R$)", "Observações" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // percorre valores
            if (oportunidades_resultado != null)
            {
                foreach (OPORTUNIDADES_DEMANDA oportunidade in oportunidades_resultado)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex++);

                    // largura linha
                    int linha = 300;

                    // medicao
                    textoCelulaXLS(row, 0, oportunidade.Nome_Medicao);

                    //
                    // Oportunidades
                    //

                    // diferenca percentual 
                    bool apresentaOportunidadeP = true;
                    bool apresentaOportunidadeFP = true;

                    if (oportunidade.FatorUtilizacaoP == 0.0)
                    {
                        apresentaOportunidadeP = false;
                    }

                    if (oportunidade.FatorUtilizacaoFP == 0.0)
                    {
                        apresentaOportunidadeFP = false;
                    }

                    textoCelulaXLS(row, 1, oportunidade.Contrato);
                    textoCelulaXLS(row, 2, oportunidade.Nome_Distribuidora);

                    // verifica se tem registro
                    if (oportunidade.DemandaRegistradaP == 0 && oportunidade.DemandaRegistradaFP == 0)
                    {
                        apresentaOportunidadeP = false;
                        apresentaOportunidadeFP = false;

                        textoCelulaXLS(row, 3, "---");
                        textoCelulaXLS(row, 4, "---");
                        textoCelulaXLS(row, 5, "---");
                        textoCelulaXLS(row, 6, "---");
                        textoCelulaXLS(row, 7, "---");
                        textoCelulaXLS(row, 8, "---");
                        textoCelulaXLS(row, 9, "---");
                        textoCelulaXLS(row, 10, "---");
                    }

                    else
                    {
                        numeroCelulaXLS(row, 3, oportunidade.ContratoDemP, _1CellStyle);
                        numeroCelulaXLS(row, 4, oportunidade.DemandaRegistradaP, _1CellStyle);
                        numeroCelulaXLS(row, 5, oportunidade.DiferencaDemandaP, _1CellStyle);

                        // verifica cor porcentagem
                        if (oportunidade.FatorUtilizacaoP >= 95.0)
                        {
                            numeroCelulaXLS(row, 6, oportunidade.FatorUtilizacaoP, _1CellStyleVerde);
                            apresentaOportunidadeP = false;
                        }
                        else if (oportunidade.FatorUtilizacaoP >= 90.0 && oportunidade.FatorUtilizacaoP < 95.0)
                        {
                            numeroCelulaXLS(row, 6, oportunidade.FatorUtilizacaoP, _1CellStyleAmarelo);
                        }
                        else if (oportunidade.FatorUtilizacaoP > 0 && oportunidade.FatorUtilizacaoP < 90.0)
                        {
                            numeroCelulaXLS(row, 6, oportunidade.FatorUtilizacaoP, _1CellStyleVermelho);
                        }
                        else
                        {
                            numeroCelulaXLS(row, 6, oportunidade.FatorUtilizacaoP, _1CellStyle);
                        }

                        if (oportunidade.IDEstruturaTarifaria == TIPOESTR_TAR.THS_VERDE)
                        {
                            apresentaOportunidadeFP = false;

                            textoCelulaXLS(row, 7, "---");
                            textoCelulaXLS(row, 8, "---");
                            textoCelulaXLS(row, 9, "---");
                            textoCelulaXLS(row, 10, "---");
                        }

                        else
                        {                            
                            numeroCelulaXLS(row, 7, oportunidade.ContratoDemFP, _1CellStyle);
                            numeroCelulaXLS(row, 8, oportunidade.DemandaRegistradaFP, _1CellStyle);
                            numeroCelulaXLS(row, 9, oportunidade.DiferencaDemandaFP, _1CellStyle);

                            // verifica cor porcentagem
                            if (oportunidade.FatorUtilizacaoFP >= 95.0)
                            {
                                numeroCelulaXLS(row, 10, oportunidade.FatorUtilizacaoFP, _1CellStyleVerde);
                                apresentaOportunidadeFP = false;
                            }
                            else if (oportunidade.FatorUtilizacaoFP >= 90.0 && oportunidade.FatorUtilizacaoFP < 95.0)
                            {
                                numeroCelulaXLS(row, 10, oportunidade.FatorUtilizacaoFP, _1CellStyleAmarelo);
                            }
                            else if (oportunidade.FatorUtilizacaoFP > 0 && oportunidade.FatorUtilizacaoFP < 90.0)
                            {
                                numeroCelulaXLS(row, 10, oportunidade.FatorUtilizacaoFP, _1CellStyleVermelho);
                            }
                            else
                            {
                                numeroCelulaXLS(row, 10, oportunidade.FatorUtilizacaoFP, _1CellStyle);
                            }
                        }
                    }


                    if (apresentaOportunidadeP) numeroCelulaXLS(row, 11, oportunidade.OportunidadeP, _2CellStyle);
                    else textoCelulaXLS(row, 11, "---");

                    if (apresentaOportunidadeFP) numeroCelulaXLS(row, 12, oportunidade.OportunidadeFP, _2CellStyle);
                    else textoCelulaXLS(row, 12, "---");

                    // le cookie datahora
                    DateTime datahora_cookie_ini = CookieStore.LeCookie_Datahora("Fatura_DataIni");
                    DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Fatura_DataFim");

                    // observacoes do mes
                    ObservacaoMedicaoMetodos obsMetodos = new ObservacaoMedicaoMetodos();
                    List<ObservacaoMedicaoDominio> observacoes = obsMetodos.ListarPorPeriodo(oportunidade.IDMedicao, datahora_cookie_ini, datahora_cookie_fim);

                    // string
                    string textoObs = "";

                    // verifica se tem observacao
                    if (observacoes != null && observacoes.Count > 0)
                    {
                        foreach (ObservacaoMedicaoDominio obs in observacoes)
                        {
                            // verifica se nao eh a primeira obs
                            if (observacoes.IndexOf(obs) > 0)
                            {
                                // quebra linha
                                textoObs += "\n \n";

                                // aumenta largura da linha
                                linha += 450;
                            }

                            // adiciona texto observação
                            textoObs += string.Format("{0:d} - {1}", obs.DataHora, obs.Observacao);
                        }
                    }

                    textoCelulaXLS(row, 13, textoObs);

                    row.Height = (short)linha;
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // MULTAS
            //

            List<OPORTUNIDADES_DEMANDA> oportunidades_multa_resultado = ViewBag.lista_oportunidades_multa;

            rowIndex = 0;

            // cria planilha
            sheet = workbook.CreateSheet("Multas");

            // total 
            row = sheet.CreateRow(rowIndex);

            // total
            textoCelulaXLS(row, 10, "Total Multas (R$)", _negritoCellStyle);

            // total
            total_oportunidadesP = 0.0;
            total_oportunidadesFP = 0.0;

            if (oportunidades_multa_resultado != null)
            {
                total_oportunidadesP = oportunidades_multa_resultado.Sum(x => x.OportunidadeP);
                total_oportunidadesFP = oportunidades_multa_resultado.Sum(x => x.OportunidadeFP);
            }

            // total ponta 
            numeroCelulaXLS(row, 11, total_oportunidadesP, _2CellStyle);

            // total fponta 
            numeroCelulaXLS(row, 12, total_oportunidadesFP, _2CellStyle);

            // cabecalho
            string[] cabecalho_multas = { "Medições", "Estrutura Tarifária", "Distribuidora", "Demanda Contrato Ponta (kW)", "Demanda Registrada Ponta (kW)", "Diferença Ponta (kW)", "Fator de Utilização Ponta (%)", "Demanda Contrato Fora Ponta (kW)", "Demanda Registrada Fora Ponta (kW)", "Diferença Fora Ponta (kW)", "Fator de Utilização Fora Ponta (%)", "Multa Ponta (R$)", "Multa Fora Ponta (R$)", "Observações" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho_multas, 1);

            // percorre valores
            if (oportunidades_multa_resultado != null)
            {
                foreach (OPORTUNIDADES_DEMANDA oportunidade_multa in oportunidades_multa_resultado)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex++);

                    // largura linha
                    int linha = 300;

                    // medicao
                    textoCelulaXLS(row, 0, oportunidade_multa.Nome_Medicao);

                    //
                    // Oportunidades
                    //

                    // diferenca percentual 
                    bool apresentaOportunidadeP = true;
                    bool apresentaOportunidadeFP = true;

                    if (oportunidade_multa.FatorUtilizacaoP == 0.0)
                    {
                        apresentaOportunidadeP = false;
                    }

                    if (oportunidade_multa.FatorUtilizacaoFP == 0.0)
                    {
                        apresentaOportunidadeFP = false;
                    }

                    textoCelulaXLS(row, 1, oportunidade_multa.Contrato);
                    textoCelulaXLS(row, 2, oportunidade_multa.Nome_Distribuidora);

                    // verifica se tem registro
                    if (oportunidade_multa.DemandaRegistradaP == 0 && oportunidade_multa.DemandaRegistradaFP == 0)
                    {
                        apresentaOportunidadeP = false;
                        apresentaOportunidadeFP = false;

                        textoCelulaXLS(row, 3, "---");
                        textoCelulaXLS(row, 4, "---");
                        textoCelulaXLS(row, 5, "---");
                        textoCelulaXLS(row, 6, "---");
                        textoCelulaXLS(row, 7, "---");
                        textoCelulaXLS(row, 8, "---");
                        textoCelulaXLS(row, 9, "---");
                        textoCelulaXLS(row, 10, "---");
                    }

                    else
                    {
                        numeroCelulaXLS(row, 3, oportunidade_multa.ContratoDemP, _1CellStyle);
                        numeroCelulaXLS(row, 4, oportunidade_multa.DemandaRegistradaP, _1CellStyle);
                        numeroCelulaXLS(row, 5, oportunidade_multa.DiferencaDemandaP, _1CellStyle);

                        // verifica cor porcentagem
                        if (oportunidade_multa.FatorUtilizacaoP > 0 && oportunidade_multa.FatorUtilizacaoP <= 100.0)
                        {
                            numeroCelulaXLS(row, 6, oportunidade_multa.FatorUtilizacaoP, _1CellStyleVerde);
                            apresentaOportunidadeP = false;
                        }
                        else if (oportunidade_multa.FatorUtilizacaoP > 100.0 && oportunidade_multa.FatorUtilizacaoP <= 100.0 + oportunidade_multa.ToleranciaP)
                        {
                            numeroCelulaXLS(row, 6, oportunidade_multa.FatorUtilizacaoP, _1CellStyleAmarelo);
                            apresentaOportunidadeP = false;
                        }
                        else if (oportunidade_multa.FatorUtilizacaoP > 100.0 + oportunidade_multa.ToleranciaP)
                        {
                            numeroCelulaXLS(row, 6, oportunidade_multa.FatorUtilizacaoP, _1CellStyleVermelho);
                        }
                        else
                        {
                            numeroCelulaXLS(row, 6, oportunidade_multa.FatorUtilizacaoP, _1CellStyle);
                        }

                        if (oportunidade_multa.IDEstruturaTarifaria == TIPOESTR_TAR.THS_VERDE)
                        {
                            apresentaOportunidadeFP = false;

                            textoCelulaXLS(row, 7, "---");
                            textoCelulaXLS(row, 8, "---");
                            textoCelulaXLS(row, 9, "---");
                            textoCelulaXLS(row, 10, "---");
                        }

                        else
                        {                            
                            numeroCelulaXLS(row, 7, oportunidade_multa.ContratoDemFP, _1CellStyle);
                            numeroCelulaXLS(row, 8, oportunidade_multa.DemandaRegistradaFP, _1CellStyle);
                            numeroCelulaXLS(row, 9, oportunidade_multa.DiferencaDemandaFP, _1CellStyle);

                            // verifica cor porcentagem
                            if (oportunidade_multa.FatorUtilizacaoFP > 0 && oportunidade_multa.FatorUtilizacaoFP <= 100.0)
                            {
                                numeroCelulaXLS(row, 10, oportunidade_multa.FatorUtilizacaoFP, _1CellStyleVerde);
                                apresentaOportunidadeFP = false;
                            }
                            else if (oportunidade_multa.FatorUtilizacaoFP > 100.0 && oportunidade_multa.FatorUtilizacaoFP <= 100.0 + oportunidade_multa.ToleranciaFP)
                            {
                                numeroCelulaXLS(row, 10, oportunidade_multa.FatorUtilizacaoFP, _1CellStyleAmarelo);
                                apresentaOportunidadeFP = false;
                            }
                            else if (oportunidade_multa.FatorUtilizacaoFP > 100.0 + oportunidade_multa.ToleranciaFP)
                            {
                                numeroCelulaXLS(row, 10, oportunidade_multa.FatorUtilizacaoFP, _1CellStyleVermelho);
                            }
                            else
                            {
                                numeroCelulaXLS(row, 10, oportunidade_multa.FatorUtilizacaoFP, _1CellStyle);
                            }
                        }

                    }


                    if (apresentaOportunidadeP) numeroCelulaXLS(row, 11, oportunidade_multa.OportunidadeP, _2CellStyle);
                    else textoCelulaXLS(row, 11, "---");

                    if (apresentaOportunidadeFP) numeroCelulaXLS(row, 12, oportunidade_multa.OportunidadeFP, _2CellStyle);
                    else textoCelulaXLS(row, 12, "---");

                    // le cookie datahora
                    DateTime datahora_cookie_ini = CookieStore.LeCookie_Datahora("Fatura_DataIni");
                    DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Fatura_DataFim");

                    // observacoes do mes
                    ObservacaoMedicaoMetodos obsMetodos = new ObservacaoMedicaoMetodos();
                    List<ObservacaoMedicaoDominio> observacoes = obsMetodos.ListarPorPeriodo(oportunidade_multa.IDMedicao, datahora_cookie_ini, datahora_cookie_fim);

                    // string
                    string textoObs = "";

                    // verifica se tem observacao
                    if (observacoes != null && observacoes.Count > 0)
                    {
                        foreach (ObservacaoMedicaoDominio obs in observacoes)
                        {
                            // verifica se nao eh a primeira obs
                            if (observacoes.IndexOf(obs) > 0)
                            {
                                // quebra linha
                                textoObs += "\n \n";

                                // aumenta largura da linha
                                linha += 450;
                            }

                            // adiciona texto observação
                            textoObs += string.Format("{0:d} - {1}", obs.DataHora, obs.Observacao);
                        }
                    }

                    textoCelulaXLS(row, 13, textoObs);

                    row.Height = (short)linha;
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS_OportunidadesDemanda(workbook, sheet, _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(4).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            sheet.SetColumnWidth(1, 9000);

            // retorna planilha
            return workbook;
        }

        // GET: Observacoes Medicao
        public PartialViewResult _ObservacoesMedicao(int IDMedicao)
        {
            // le cookie datahora
            DateTime datahora_cookie_ini = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // tipos acesso
            ListaTiposMetodos listaMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listaTipoAcesso = listaMetodos.ListarTodos("TipoAcesso");

            ViewBag.listaTipoAcesso = listaTipoAcesso;

            // le tags
            List<ObservacaoTagsDominio> tags = listaMetodos.ListarTodos_ObservacaoTags();

            ViewBag.tags = tags;

            // observacao
            ObservacaoMedicaoDominio observacao = new ObservacaoMedicaoDominio();

            ViewBag.observacao = observacao;
            ViewBag.IDMedSelecionado = IDMedicao;

            // observacoes
            ObservacaoMedicaoMetodos observacaoMetodos = new ObservacaoMedicaoMetodos();
            List<ObservacaoMedicaoDominio> observacoes_lidas = observacaoMetodos.ListarPorPeriodo(IDMedicao, datahora_cookie_ini, datahora_cookie_fim);
            List<ObservacaoMedicaoDominio> observacoes = new List<ObservacaoMedicaoDominio>();

            // le observacoes
            if (observacoes_lidas != null)
            {
                // percorre observacoes
                foreach (ObservacaoMedicaoDominio obs in observacoes_lidas)
                {
                    bool adicionar = false;

                    // caso for admin GESTAL pode ver todas observacoes (inseridas pela GESTAL, consultores ou clientes)
                    if (isUser.isGESTAL(IDTipoAcesso))
                    {
                        // permite ele ver
                        adicionar = true;
                    }

                    // caso usuario atual for consultor pode ver apenas as de consultor e dos clientes
                    if (isUser.isConsultor(IDTipoAcesso))
                    {
                        // verifica se mensagem eh de consultor ou se de cliente habilitado para visualizar
                        if (isUser.isConsultor(obs.IDTipoAcesso) || obs.ClienteVisualiza)
                        {
                            adicionar = true;
                        }
                    }

                    // caso usuario atual for cliente pode ver apenas se habilitado para visualizar
                    if (isUser.isCliente(IDTipoAcesso))
                    {
                        // verifica se cliente habilitado para visualizar
                        if (obs.ClienteVisualiza)
                        {
                            adicionar = true;
                        }
                    }

                    // adiciona na lista
                    if (adicionar)
                    {
                        // adiona na lista
                        observacoes.Add(obs);
                    }
                }
            }

            ViewBag.Observacoes = observacoes;

            return PartialView("~/Views/Supervisao/_ObservacoesMedicao.cshtml");
        }
    }
}
