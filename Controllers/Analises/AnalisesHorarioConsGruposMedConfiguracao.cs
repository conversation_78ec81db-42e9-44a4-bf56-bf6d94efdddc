﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class AnalisesController
    {

        
        // GET: AnaliseHorarioConsumoGruposMed - Editar
        public ActionResult AnaliseHorarioConsGruposMed_Editar(int IDAnaliseHorarioCons, int IDMedicao)
        {

            // tela de ajuda - AnaliseHorarioConsumo
            CookieStore.SalvaCookie_String("PaginaAjuda", "Analise_HorarioCons_MedEditar");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "AnaliseHorarioConsumo");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes 
            Permisso<PERSON>();

            // le analise
            AnaliseHorarioConsMetodos analiseMetodos = new AnaliseHorarioConsMetodos();
            AnaliseHorarioConsDominio analise = analiseMetodos.ListarPorId(IDAnaliseHorarioCons);

            // copia nome da analise
            ViewBag.Analise = analise;

            // le grupo medicao
            AnaliseHorarioConsGruposMedMetodos analiseGruposMetodos = new AnaliseHorarioConsGruposMedMetodos();
            AnaliseHorarioConsGruposMedDominio analiseGrupoMed = analiseGruposMetodos.ListarPorIDAnaliseIDMedicao(IDAnaliseHorarioCons, IDMedicao);        

            // le medicao
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);

            if (medicao != null)
            {
                // copia configuração Horário de Consumo
                analiseGrupoMed.HoraIniRef = DateTime.ParseExact(medicao.Funcionamento_Inicio, "HH:mm", CultureInfo.InvariantCulture); 
                analiseGrupoMed.HoraFimRef = DateTime.ParseExact(medicao.Funcionamento_Fim, "HH:mm", CultureInfo.InvariantCulture); 
                analiseGrupoMed.DemandaResidual = medicao.Funcionamento_DemMin;
            }


            // lista de medicoes habilitadas para o usuario
            List<int> ConfigMedicaoList_Usuario = new List<int>();
            ConfigMedicaoList_Usuario = ViewBag._ConfigMed;

            // verifica se existe lista medicoes
            if (ConfigMedicaoList_Usuario != null)
            {
                if (ConfigMedicaoList_Usuario.Count() == 0)
                {
                    ConfigMedicaoList_Usuario = null;
                }
            }
            
            // le medicoes analogicas da análise
            AnaliseHorarioConsGruposMedEAMetodos analiseGrupoEAMetodos = new AnaliseHorarioConsGruposMedEAMetodos();
            List<AnaliseHorarioConsGruposMedEADominio> analiseGruposEA = analiseGrupoEAMetodos.ListarPorIDAnaliseGruposMed(analiseGrupoMed.IDAnaliseHorarioConsGruposMed);
            
            // cria lista de analogicas dessa medicao
            List<int> ConfigMedicaoList_MedEA = new List<int>();

            foreach (AnaliseHorarioConsGruposMedEADominio analiseGrupoEA in analiseGruposEA)
            {
                ConfigMedicaoList_MedEA.Add(analiseGrupoEA.IDMedicao);
            }
            
            // le medicoes configuradas para a lista            
            CliGateGrupoUnidMedicoesMetodos medicoesMetodos = new CliGateGrupoUnidMedicoesMetodos();
            List<CliGateGrupoUnidMedicoesDominio> medicoes = new List<CliGateGrupoUnidMedicoesDominio>();

            List<CliGateGrupoUnidMedicoesDominio> medicoes_utilizadas = new List<CliGateGrupoUnidMedicoesDominio>();
            List<CliGateGrupoUnidMedicoesDominio> medicoes_nao_utilizadas = new List<CliGateGrupoUnidMedicoesDominio>();

            // medição que está sendo configurada
            CliGateGrupoUnidMedicoesDominio medAnalise = new CliGateGrupoUnidMedicoesDominio();
           
            if (ConfigMedicaoList_MedEA != null)
            {
                // le medicoes de analogicas habilitadas para este cliente
                medicoes = medicoesMetodos.ListarPorIDCliente(analise.IDCliente, ConfigMedicaoList_Usuario);

                // seleciona medicoes
                int contador;

                // percorre lista
                if (medicoes != null)
                {
                    for (contador = 0; contador < medicoes.Count(); contador ++)
                    {
                        // aproveito loop para encontrar a medicao que esta sendo configurada
                        if (medicoes[contador].IDMedicao == analiseGrupoMed.IDMedicao)
                        { 
                            // copia medicao
                            medAnalise = medicoes[contador];
                        }

                        // verifica se eh analogica
                        if (medicoes[contador].IDTipoMedicao == 3)
                        {
                            // verifica se existe lista de medicoes de analogicas
                            if (ConfigMedicaoList_MedEA != null)
                            {
                                // verifica se medicao esta habilitada no grupo
                                if (ConfigMedicaoList_MedEA.Contains(medicoes[contador].IDMedicao))
                                {
                                    // copia medicao na lista de utilizada
                                    medicoes_utilizadas.Add(medicoes[contador]);
                                }
                                else
                                {
                                    // copia medicoes da mesma unidade na lista de nao utilizadas
                                    medicoes_nao_utilizadas.Add(medicoes[contador]);

                                }
                            }
                            else
                            {
                                // copia medicao na lista de nao utilizadas
                                medicoes_nao_utilizadas.Add(medicoes[contador]);
                            }
                        }
                    }
                }
            }

            // copia medicao
            ViewBag.MedAnalise = medAnalise;

            ViewBag.Medicoes = medicoes_utilizadas;
            ViewBag.Medicoes2 = medicoes_nao_utilizadas;
            ViewBag.analiseGruposEA = analiseGruposEA;
            return View(analiseGrupoMed);
        }
        
        
        // POST: AnaliseHorarioConsumo - Salvar
        [HttpPost]
        public ActionResult AnaliseHorarioConsGruposMed_Salvar(AnaliseHorarioConsGruposMedDominio analiseGruposMed, List<AnaliseHorarioConsGruposMedEADominio> analiseGruposMedEA)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };
            
            // verifica se existe outro grupo de medicao
            AnaliseHorarioConsGruposMedMetodos analiseGruposMedMetodos = new AnaliseHorarioConsGruposMedMetodos();
            if (analiseGruposMedMetodos.VerificarDuplicidade(analiseGruposMed))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Análise do Horário de Consumo existente."
                };
            }

            if ((analiseGruposMed.HoraIniRef.Hour*60 + analiseGruposMed.HoraIniRef.Minute) >= (analiseGruposMed.HoraFimRef.Hour*60 + analiseGruposMed.HoraFimRef.Minute))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Horário Início Referência deve ser menor que o Horário Fim Referência."
                };
            }

            else
            {
                // salva medicao da analise
                analiseGruposMedMetodos.Salvar(analiseGruposMed);

                // atualiza horario de consumo na configuração da medição
                MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                medicaoMetodos.Atualizar_HorarioConsumo(analiseGruposMed.IDMedicao, analiseGruposMed.HoraIniRef, analiseGruposMed.HoraFimRef, analiseGruposMed.DemandaResidual);

                // pego IDAnaliseHorarioGruposMedCons novamente (pois pode ter sido insercao)
                AnaliseHorarioConsGruposMedDominio novaAnaliseGruposMed = new AnaliseHorarioConsGruposMedDominio();

                // verifica se alterando
                if (analiseGruposMed.IDAnaliseHorarioConsGruposMed > 0)
                {
                    // caso alterando apenas copia do DB
                    novaAnaliseGruposMed = analiseGruposMedMetodos.ListarPorId(analiseGruposMed.IDAnaliseHorarioConsGruposMed);
                }

                else
                {
                    // caso inserindo faz leitura por IDAnaliseHorarioCons e IDMedicao para pegar novo ID
                    novaAnaliseGruposMed = analiseGruposMedMetodos.ListarPorIDAnaliseIDMedicao(analiseGruposMed.IDAnaliseHorarioCons, analiseGruposMed.IDMedicao);
                }

                // salva analise grupo de medicoes analogicas
                if (novaAnaliseGruposMed != null)
                {
                    AnaliseHorarioConsGruposMedEAMetodos analiseGruposEAMetodos = new AnaliseHorarioConsGruposMedEAMetodos();

                    // exclui todas as medicoes desta analise
                    analiseGruposEAMetodos.Excluir(analiseGruposMed.IDAnaliseHorarioConsGruposMed);

                    // salva analise grupo
                    if (analiseGruposMedEA != null)
                    {
                        // percorre lista e salva
                        foreach (AnaliseHorarioConsGruposMedEADominio analiseGrupoMedEA in analiseGruposMedEA)
                        {
                            // preenche campos que faltam com default
                            analiseGrupoMedEA.IDAnaliseHorarioConsGruposMed = novaAnaliseGruposMed.IDAnaliseHorarioConsGruposMed;

                            // salva
                            analiseGruposEAMetodos.Salvar(analiseGrupoMedEA);
                        }
                    }
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: AnaliseHorarioConsumo - Excluir
        public ActionResult AnaliseHorarioConsGruposMed_Excluir(int IDAnaliseHorarioConsGruposMed)
        {
            // apaga a analise
            AnaliseHorarioConsGruposMedMetodos analiseGruposMetodos = new AnaliseHorarioConsGruposMedMetodos();
            analiseGruposMetodos.Excluir(IDAnaliseHorarioConsGruposMed);

            AnaliseHorarioConsGruposMedEAMetodos analiseGruposEAMetodos = new AnaliseHorarioConsGruposMedEAMetodos();
            analiseGruposEAMetodos.Excluir(IDAnaliseHorarioConsGruposMed);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}
