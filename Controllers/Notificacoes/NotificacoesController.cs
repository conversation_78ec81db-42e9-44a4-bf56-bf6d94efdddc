﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class NotificacoesController : Controller
    {

        public ActionResult NotificacaoSend()
        {
            // tela de ajuda - mensagem
            ViewBag.PaginaAjuda = "Contato";

            // le cookies
            LeCookies_SmartEnergy();

            // nome cliente
            int IDCliente = ViewBag._IDCliente;
            string NomeCliente = "";

            ClientesMetodos clientesMetodos = new ClientesMetodos();
            ClientesDominio cliente = clientesMetodos.ListarPorId(IDCliente);
            if(cliente != null)
            {
                NomeCliente = cliente.Nome;
            }
            ViewBag.NomeCliente = NomeCliente;

            return View();
        }

        // GET: EnviarNotificacao
        [ValidateInput(false)]
        public ActionResult EnviarNotificacao(int EnviarPara, string Titulo, string Notificacao)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // data e hora atual
            DateTime datahora = DateTime.Now;


            // enviar notificação para usuarios
            UsuarioNotificacaoMetodos notificacaoMetodos = new UsuarioNotificacaoMetodos();
            UsuarioNotificacaoDominio notificacao = new UsuarioNotificacaoDominio();

            // preenche
            notificacao.IDUsuarioNotificacao = 0;                       // insere notificação
            notificacao.DataHora = datahora;                            // data e hora
            notificacao.IDCliente = 0;                                  // indica que é notificação genérica
            notificacao.IDGateway = 0;                                  // indica que é notificação genérica
            notificacao.IDMedicao = 0;                                  // indica que é notificação genérica
            notificacao.Titulo = Titulo;                                // título
            notificacao.Notificacao = Notificacao;                      // notificação
            notificacao.Status = 0;                                     // notificação ativa

            // verifica se é para enviar para TODOS
            if (EnviarPara == 1)
            {
                // envia notificação para todos os usuários
                notificacao.IDUsuario = 0;
                notificacaoMetodos.Salvar(notificacao);
            }
            else
            {
                // usuarios
                UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
                List<UsuarioDominio> usuarios = usuarioMetodos.ListarEnviarMensagem(EnviarPara, IDCliente);

                if (usuarios != null)
                {
                    // percorre usuarios
                    foreach (UsuarioDominio usuario in usuarios)
                    {
                        // envia notificação para o usuário
                        notificacao.IDUsuario = usuario.IDUsuario;
                        notificacaoMetodos.Salvar(notificacao);
                    }
                }
            }

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

    }
}