﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class EditorRelatController : Controller
    {
        // GET: Editor Relatórios modo Editar - Usuario
        public ActionResult ER_Superv_Editar(int IDUsuario, int IDEditorRelatGrupo = 0)
        {
            // tela de ajuda - editor relatorio configuracao
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // le cookies
            LeCookies_SmartEnergy();

            // le cliente 
            int IDCliente = ViewBag._IDCliente;
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            ClientesDominio cliente = clientesMetodos.ListarPorId(IDCliente);

            // copia nome 
            ViewBag.ClienteNome = cliente.Fantasia;

            // modo editar
            CookieStore.SalvaCookie_Bool("ER_Editar", true);

            // IDReferencia
            int IDReferencia = IDUsuario;
            int TipoReferencia = DB_REFERENCIA.IDUsuario;

            if (IDEditorRelatGrupo > 0)
            {
                IDReferencia = IDEditorRelatGrupo;
                TipoReferencia = DB_REFERENCIA.IDDashboardGrupo;
            }
            else
            {
                // le usuário
                UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
                UsuarioDominio usuario = usuarioMetodos.ListarPorId(IDUsuario);

                // verifica se usuário quer editor relatorio dele ou do modelo
                if (usuario != null)
                {
                    if (usuario.IDEditorRelatGrupo > 0)
                    {
                        IDReferencia = usuario.IDEditorRelatGrupo;
                        TipoReferencia = DB_REFERENCIA.IDDashboardGrupo;
                    }

                    // atualiza IDEditorRelatGrupo
                    IDEditorRelatGrupo = usuario.IDEditorRelatGrupo;
                }
            }

            // atualiza cookie
            CookieStore.SalvaCookie_Int("IDEditorRelatGrupo", IDEditorRelatGrupo);
            ViewBag._IDEditorRelatGrupo = IDEditorRelatGrupo;

            // nome do grupo de pagina
            string NomeEditorRelatGrupo = "";

            if (IDEditorRelatGrupo > 0)
            {
                // le grupo
                EditorRelatGrupoMetodos grupoMetodos = new EditorRelatGrupoMetodos();
                EditorRelatGrupoDominio grupo = grupoMetodos.ListarPorId(IDEditorRelatGrupo);

                if (grupo != null)
                {
                    NomeEditorRelatGrupo = grupo.Nome;
                }
            }

            ViewBag.NomeEditorRelatGrupo = NomeEditorRelatGrupo;

            return View(ER_Superv_Prepara(IDReferencia, TipoReferencia));
        }

        // GET: Editor Relatorio Excluir 
        public JsonResult _ER_Excluir(int IDEditorRelat)
        {
            // apaga editor relatorio
            var editorRelatMetodos = new EditorRelatMetodos();

            // Altero editor para modelo vazio
            EditorRelatDominio novoVazio = editorRelatMetodos.ListarPorId(IDEditorRelat);
            novoVazio.IDTipoEditorRelat = 0;
            novoVazio.Title = "";
            novoVazio.IDMedicao = 0;

            editorRelatMetodos.Salvar(novoVazio);

            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // GET: Editor Relatório Editar Modal
        public PartialViewResult _ER_Editar(int IDEditorRelat, int IDEditorRelatPage = 0)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // grupo editor relatorio
            int IDEditorRelatGrupo = ViewBag._IDEditorRelatGrupo;

            // metodos
            EditorRelatMetodos editorRelatMetodos = new EditorRelatMetodos();
            EditorRelatPageMetodos editorRelatPageMetodos = new EditorRelatPageMetodos();
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            ClientesMetodos clientesMetodos = new ClientesMetodos();

            // editor relatorio
            EditorRelatDominio itemEditorRelat = new EditorRelatDominio();

            // verifica se eh edicao
            if (IDEditorRelat > 0)
            {
                // le editor relatorio
                itemEditorRelat = editorRelatMetodos.ListarPorId(IDEditorRelat);

                // leio pagina do editor relatorio
                EditorRelatPageDominio itemEditorRelatPage = editorRelatPageMetodos.ListarPorIdReferenciaPagina(itemEditorRelat.IDReferencia, itemEditorRelat.TipoReferencia, itemEditorRelat.IDEditorRelatPage);

                // nome da pagina
                itemEditorRelat.PageTitle = itemEditorRelatPage.Title;

                if (itemEditorRelat.IDMedicao > 0)
                {
                    // le configuracao medicao
                    var medicao = medicoesMetodos.ListarPorId(itemEditorRelat.IDMedicao);

                    // nome da medicao
                    ViewBag.NomeMedicao = medicao.Nome;
                    ViewBag._IDTipoMedicao = medicao.IDTipoMedicao;

                    // le configuracao cliente
                    ClientesDominio cliente = clientesMetodos.ListarPorId(medicao.IDCliente);

                    // nome do cliente
                    ViewBag.NomeCliente = cliente.Nome;
                }
                else
                {
                    // nome da medicao
                    ViewBag.NomeMedicao = "Utilizar a Medição Atual";
                    ViewBag._IDTipoMedicao = -10;

                    // nome do cliente
                    ViewBag.NomeCliente = "Utilizar o Cliente Atual";
                }
            }
            else
            {
                // zera editor relatorio
                itemEditorRelat.IDEditorRelat = 0;

                // referência
                int IDReferencia = ViewBag._IDUsuario;
                int TipoReferencia = DB_REFERENCIA.IDUsuario;

                // verifica se esta usando grupo
                if (IDEditorRelatGrupo > 0)
                {
                    IDReferencia = IDEditorRelatGrupo;
                    TipoReferencia = DB_REFERENCIA.IDDashboardGrupo;
                }

                itemEditorRelat.IDReferencia = IDReferencia;
                itemEditorRelat.TipoReferencia = TipoReferencia;

                itemEditorRelat.IDTipoEditorRelat = 0;

                itemEditorRelat.IDMedicao = 0;

                if (itemEditorRelat.IDMedicao > 0)
                {
                    // le configuracao medicao
                    MedicoesDominio medicao = medicoesMetodos.ListarPorId(itemEditorRelat.IDMedicao);

                    // nome da medicao
                    ViewBag.NomeMedicao = medicao.Nome;
                    ViewBag._IDTipoMedicao = medicao.IDTipoMedicao;

                    // le configuracao cliente
                    ClientesDominio cliente = clientesMetodos.ListarPorId(medicao.IDCliente);

                    // nome do cliente
                    ViewBag.NomeCliente = cliente.Nome;
                }
                else
                {
                    // nome da medicao
                    ViewBag.NomeMedicao = "Utilizar a Medição Atual";
                    ViewBag._IDTipoMedicao = -10;

                    // nome do cliente
                    ViewBag.NomeCliente = "Utilizar o Cliente Atual";
                }

                itemEditorRelat.IDEditorRelatPage = IDEditorRelatPage;

                List<EditorRelatDominio> listaEditorRelat = editorRelatMetodos.ListarPorPagina(IDEditorRelatPage);

                int linha_atual = -1;
                int coluna_atual = -1;

                foreach (EditorRelatDominio editorRelat in listaEditorRelat)
                {
                    // primeira passada
                    if (linha_atual < 0)
                    {
                        linha_atual = editorRelat.RowNumber;
                        coluna_atual = editorRelat.ColumnNumber;
                    }

                    // verifica se mudou de linha
                    if (linha_atual != editorRelat.RowNumber)
                    {
                        // mudou de linha, verifico se tem espaco na linha anterior
                        if (coluna_atual < 3)
                        {
                            // tem espaco, coloco editor relatorio aqui
                            editorRelat.RowNumber = linha_atual;
                            editorRelat.ColumnNumber = coluna_atual + 1;

                            break;
                        }
                    }

                    // copia
                    linha_atual = editorRelat.RowNumber;
                    coluna_atual = editorRelat.ColumnNumber;
                }

                itemEditorRelat.Title = "";
                itemEditorRelat.PageTitle = "";
            }

            //
            // Lista de clientes e medicoes
            //
            var GrupoUnidMedicoesMetodos = new CliGateGrupoUnidMedicoesMetodos();

            // tipo acesso
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // verifica se cliente gerente ou operador
            if (isUser.isCliente(IDTipoAcesso))
            {
                // lista de todas medicoes habilitadas para o usuario
                List<int> ConfigMedicaoList = ViewBag._ConfigMed;

                // le todas medicoes para lista
                var GrupoUnidMedicoes = GrupoUnidMedicoesMetodos.ListarPorIDCliente(ViewBag._IDCliente, ConfigMedicaoList);
                ViewBag.GrupoUnidMedicoes = GrupoUnidMedicoes;
            }
            // verifica se consultor
            else if (isUser.isConsultor(IDTipoAcesso))
            {
                // lista de medicoes
                List<CliGateGrupoUnidMedicoesDominio> GrupoUnidMedicoes = new List<CliGateGrupoUnidMedicoesDominio>();

                // verifica se esta editando grupos de paginas para algum cliente
                if (IDEditorRelatGrupo > 0 && IDCliente > 0)
                {
                    // todas as medições do cliente
                    List<CliGateGrupoUnidMedicoesDominio> medicoes3 = GrupoUnidMedicoesMetodos.ListarPorIDCliente(IDCliente);

                    foreach (CliGateGrupoUnidMedicoesDominio medicao in medicoes3)
                    {
                        // coloca na lista
                        GrupoUnidMedicoes.Add(medicao);
                    }
                }
                else
                {
                    // lista de todas cliente e medicoes habilitadas para o usuario
                    List<int> ConfigClienteList = ViewBag._ConfigCli;
                    List<int> ConfigMedicaoList = ViewBag._ConfigMed;

                    // verifica se tem lista de cliente
                    if (ConfigClienteList != null)
                    {
                        // percorre lista de clientes
                        foreach (int id_cliente in ConfigClienteList)
                        {
                            // verifica se não é do cliente atual
                            if (id_cliente != IDCliente)
                            {
                                continue;
                            }

                            // le medicoes para lista
                            List<CliGateGrupoUnidMedicoesDominio> medicoes3 = new List<CliGateGrupoUnidMedicoesDominio>();

                            // verifica se operador
                            if (IDTipoAcesso == TIPO_ACESSO.CONSULTOR_OPER)
                            {
                                // somente as medições habilitadas
                                medicoes3 = GrupoUnidMedicoesMetodos.ListarPorIDCliente(id_cliente, ConfigMedicaoList);
                            }
                            else
                            {
                                // todas as medições do cliente
                                medicoes3 = GrupoUnidMedicoesMetodos.ListarPorIDCliente(id_cliente);
                            }

                            foreach (CliGateGrupoUnidMedicoesDominio medicao in medicoes3)
                            {
                                // coloca na lista
                                GrupoUnidMedicoes.Add(medicao);
                            }
                        }
                    }
                }

                ViewBag.GrupoUnidMedicoes = GrupoUnidMedicoes;
            }
            else
            {
                // le todas medicoes para lista
                var GrupoUnidMedicoes = GrupoUnidMedicoesMetodos.ListarPorIDCliente(IDCliente);
                ViewBag.GrupoUnidMedicoes = GrupoUnidMedicoes;
            }

            return PartialView(itemEditorRelat);
        }

        // POST: Editor Relatorio Editar Salvar
        [HttpPost]
        public ActionResult _ER_Editar(EditorRelatDominio editorRelat)
        {

            // salva editor relatorio
            var editorRelatMetodos = new EditorRelatMetodos();

            // verifica se adicionando
            if (editorRelat.IDEditorRelat == 0)
            {
                // lista editores atuais
                List<EditorRelatDominio> listaEditores = editorRelatMetodos.ListarPorPagina(editorRelat.IDEditorRelatPage);

                // encontrou posicao
                bool encontrou = false;


                for (int coluna = 0; coluna < 2; )
                {
                    for (int linha = 0; linha < 3; linha++)
                    {
                        foreach (EditorRelatDominio editor in listaEditores)
                        {
                            // encerra loop se encontrou
                            if (encontrou)
                            {
                                break;
                            }

                            // verifica se vazio
                            if (editor.IDTipoEditorRelat != 0)
                            {
                                continue;
                            }

                            if (editor.RowNumber == linha && editor.ColumnNumber == coluna)
                            {
                                // copio posicao 
                                editorRelat.RowNumber = editor.RowNumber;
                                editorRelat.ColumnNumber = editor.ColumnNumber;

                                // exclui vazio
                                editorRelatMetodos.Excluir(editor.IDEditorRelat);

                                // encerra loop
                                encontrou = true;
                            }
                        }


                    }
                    coluna++;
                }
            }

            editorRelatMetodos.Salvar(editorRelat);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData);
        }

        [HttpPost]
        public JsonResult SalvarPosicoes(List<EditorRelatPosicaoDominio> editorRelats)
        {
            var editorRelatMetodos = new EditorRelatMetodos();

            // percorre lista de paginas
            foreach (var erpos in editorRelats)
            {
                // salva
                editorRelatMetodos.SalvarPosicao(erpos);
            }

            string result = "OK";
            return Json(result, JsonRequestBehavior.AllowGet);
        }


        //
        // Pagina
        //

        // movimenta pagina para esquerda ou direita
        public JsonResult MoverPagina(int IDEditorRelatPage, int direcao)
        {
            // metodos
            EditorRelatPageMetodos editorRelatPageMetodos = new EditorRelatPageMetodos();

            // ler EditorRelatPage do IDEditorRelatPage
            EditorRelatPageDominio editorRelatPage = editorRelatPageMetodos.ListarPorId(IDEditorRelatPage);

            // pegar PageNumber
            int PageNumber = editorRelatPage.PageNumber;
            int PageNumberNovo = 0;

            // descobre quantos EditorRelatorioPage existem
            List<EditorRelatPageDominio> listaEditorRelatPage = editorRelatPageMetodos.ListarPorIdReferencia(editorRelatPage.IDReferencia, editorRelatPage.TipoReferencia);

            int NumEditorRelatPage = listaEditorRelatPage.Count;

            // subtrair ou somar dependendo da direcao
            // 0 - esquerda - subtrair
            // 1 - direita - somar
            if (direcao == 0)
            {
                // verifica se primeiro
                if (PageNumber == 0)
                {
                    // nao faco nada
                    return Json("OK", JsonRequestBehavior.AllowGet);
                }

                // subtrai
                PageNumberNovo = PageNumber - 1;
            }
            else
            {
                // verifica se ultimo
                if (PageNumber == (NumEditorRelatPage - 1))
                {
                    // nao faco nada
                    return Json("OK", JsonRequestBehavior.AllowGet);
                }

                // soma
                PageNumberNovo = PageNumber + 1;
            }

            // procurar EditorRelatPage do PageNumber
            EditorRelatPageDominio editorRelatPageOriginal = editorRelatPageMetodos.ListarPorPagenumber(editorRelatPage.IDReferencia, editorRelatPage.TipoReferencia, PageNumberNovo);

            // troco os PageNumber
            editorRelatPageOriginal.PageNumber = PageNumber;
            editorRelatPage.PageNumber = PageNumberNovo;

            // salvo mudanca
            editorRelatPageMetodos.Salvar(editorRelatPageOriginal);
            editorRelatPageMetodos.Salvar(editorRelatPage);

            // numero da pagina
            CookieStore.SalvaCookie_Int("PageNumberEditorRelat", PageNumberNovo);
            ViewBag.PageNumber = PageNumber;

            // retorno
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // adiciona pagina
        public JsonResult AdicionarPagina()
        {
            // metodos
            EditorRelatPageMetodos editorRelatPageMetodos = new EditorRelatPageMetodos();
            EditorRelatMetodos editorRelatMetodos = new EditorRelatMetodos();

            // le cookie IDCliente e IDUsuario
            int IDCliente = CookieStore.LeCookie_Int("_IDCliente");
            int IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");
            int IDEditorRelatGrupo = CookieStore.LeCookie_Int("IDEditorRelatGrupo");

            // referência
            int IDReferencia = IDUsuario;
            int TipoReferencia = DB_REFERENCIA.IDUsuario;

            // verifica se esta usando grupo
            if (IDEditorRelatGrupo > 0)
            {
                IDReferencia = IDEditorRelatGrupo;
                TipoReferencia = DB_REFERENCIA.IDDashboardGrupo;
            }

            // descobre quantos EditorRelatPage existem
            List<EditorRelatPageDominio> listaEditorRelatPage = editorRelatPageMetodos.ListarPorIdReferencia(IDReferencia, TipoReferencia);

            int NumEditorRelatPage = listaEditorRelatPage.Count;

            // adiciona pagina4
            EditorRelatPageDominio editorRelatPageNovo = new EditorRelatPageDominio();

            editorRelatPageNovo.IDEditorRelatPage = 0;
            editorRelatPageNovo.IDCliente = IDCliente;
            editorRelatPageNovo.IDReferencia = IDReferencia;
            editorRelatPageNovo.TipoReferencia = TipoReferencia;
            editorRelatPageNovo.PageNumber = NumEditorRelatPage;
            editorRelatPageNovo.Title = string.Format("Página {0}", NumEditorRelatPage + 1);

            editorRelatPageMetodos.Salvar(editorRelatPageNovo);

            // pego pagina que foi salva
            editorRelatPageNovo = editorRelatPageMetodos.ListarPorPagenumber(IDReferencia, TipoReferencia, NumEditorRelatPage);

            // crio 6 paineis vazios
            for (int i = 0; i < 2; i++)
            {
                for (int j = 0; j < 3; j++)
                {
                    EditorRelatDominio editorRelat = new EditorRelatDominio();

                    editorRelat.IDTipoEditorRelat = 0;
                    editorRelat.PageTitle = editorRelatPageNovo.Title;
                    editorRelat.IDEditorRelatPage = editorRelatPageNovo.IDEditorRelatPage;
                    editorRelat.Title = "";
                    editorRelat.TipoReferencia = TipoReferencia;
                    editorRelat.IDReferencia = IDReferencia;

                    // linha
                    editorRelat.RowNumber = j;

                    // coluna 
                    editorRelat.ColumnNumber = i;

                    editorRelatMetodos.Salvar(editorRelat);
                }
            }

            // numero da pagina
            CookieStore.SalvaCookie_Int("PageNumberEditorRelat", NumEditorRelatPage);
            ViewBag.PageNumber = NumEditorRelatPage;

            // retorno
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // apaga pagina
        public JsonResult ExcluirPagina(int IDEditorRelatPage)
        {
            // metodos
            EditorRelatMetodos editorRelatMetodos = new EditorRelatMetodos();
            EditorRelatPageMetodos editorRelatPageMetodos = new EditorRelatPageMetodos();

            // ler EditorRelatPage do IDEditorRelatPage
            EditorRelatPageDominio editorRelatPage = editorRelatPageMetodos.ListarPorId(IDEditorRelatPage);

            // apaga pagina
            editorRelatPageMetodos.Excluir(IDEditorRelatPage);

            // apaga editor relatorio
            editorRelatMetodos.ExcluirTodosPagina(IDEditorRelatPage);

            // reordena paginas
            editorRelatPageMetodos.ReordenaPaginas(editorRelatPage.IDReferencia, editorRelatPage.TipoReferencia);

            // numero da pagina
            CookieStore.SalvaCookie_Int("PageNumberEditorRelat", 0);
            ViewBag.PageNumber = 0;

            // retorno
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // GET: Pagina Editar Modal
        public PartialViewResult _Pagina_Editar(int IDEditorRelatPage)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // metodos
            EditorRelatPageMetodos editorRelatPageMetodos = new EditorRelatPageMetodos();

            // editor relatorio
            EditorRelatPageDominio itemEditorRelatPage = new EditorRelatPageDominio();

            // verifica se eh edicao
            if (IDEditorRelatPage > 0)
            {
                // le editor relatorio
                itemEditorRelatPage = editorRelatPageMetodos.ListarPorId(IDEditorRelatPage);
            }
            else
            {
                // le cookie IDCliente e IDUsuario
                int IDCliente = CookieStore.LeCookie_Int("_IDCliente");
                int IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");
                int IDEditorRelatGrupo = CookieStore.LeCookie_Int("IDEditorRelatGrupo");

                // referência
                int IDReferencia = IDUsuario;
                int TipoReferencia = DB_REFERENCIA.IDUsuario;

                // verifica se esta usando grupo
                if (IDEditorRelatGrupo > 0)
                {
                    IDReferencia = IDEditorRelatGrupo;
                    TipoReferencia = DB_REFERENCIA.IDDashboardGrupo;
                }

                // descobre quantos EditorRelatPage existem
                List<EditorRelatPageDominio> listaEditorRelatPage = editorRelatPageMetodos.ListarPorIdReferencia(IDReferencia, TipoReferencia);

                int NumEditorRelatPage = listaEditorRelatPage.Count;

                // nova página
                itemEditorRelatPage.IDEditorRelatPage = 0;
                itemEditorRelatPage.IDCliente = IDCliente;
                itemEditorRelatPage.IDReferencia = IDReferencia;
                itemEditorRelatPage.TipoReferencia = TipoReferencia;
                itemEditorRelatPage.PageNumber = NumEditorRelatPage;
                itemEditorRelatPage.Title = string.Format("Página {0}", NumEditorRelatPage + 1);
            }

            return PartialView(itemEditorRelatPage);
        }

        // POST: Páginas Editar Salvar
        [HttpPost]
        public ActionResult _Pagina_Editar(EditorRelatPageDominio editorRelatPage)
        {
            // salva editor relatorio page
            EditorRelatPageMetodos editorRelatPageMetodos = new EditorRelatPageMetodos();
            editorRelatPageMetodos.Salvar(editorRelatPage);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData);
        }
    }
}