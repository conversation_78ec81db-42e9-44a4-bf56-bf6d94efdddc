﻿using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Hosting;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class EditorRelatController : Controller
    {
        // GET: Editor Relatorio PDF
        public ActionResult ER_PDF(string NumPaginasImprimir = "")
        {

            // verifica paginas
            if (NumPaginasImprimir != "")
            {
                string[] paginasImprimir = NumPaginasImprimir.Split(',');

                if (paginasImprimir.Count() > 0)
                {
                    // converte parta inteiros
                    int[] paginasImprimirInt = Array.ConvertAll(paginasImprimir, int.Parse);

                    // ordena
                    paginasImprimirInt = paginasImprimirInt.OrderBy(i => i).ToArray();

                    // paginas a imprimir
                    ViewBag.PaginasImprimir = paginasImprimirInt;
                }
            }

            // data atual
            DateTime datahora_ultima = DateTime.Now;

            // le cookie datahora
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            if (datahora_cookie.Year != 2000)
            {
                datahora_ultima = datahora_cookie;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // le cookies
            LeCookies_SmartEnergy();
           
            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDEditorRelatGrupo
            int IDEditorRelatGrupo = ViewBag._IDEditorRelatGrupo;

            // prepara listas
            Editor_EditorRelatPageDominio editor = ER_Superv_Prepara(IDEditorRelatGrupo, 1);

            // retorna listas
            ViewBag.Editor = editor;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // nome do grupo de paineis
            string NomeEditorRelatGrupo = "";

            // le grupo
            EditorRelatGrupoMetodos grupoMetodos = new EditorRelatGrupoMetodos();
            EditorRelatGrupoDominio grupo = new EditorRelatGrupoDominio();

            if (IDEditorRelatGrupo > 0)
            {
                // le grupo               
                grupo = grupoMetodos.ListarPorId(IDEditorRelatGrupo);

                if (grupo != null)
                {
                    NomeEditorRelatGrupo = grupo.Nome;
                }
            }

            ViewBag.NomeGrupo = NomeEditorRelatGrupo;

            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:D}", datahora_ultima);

            // nome do arquivo
            string nomeArquivo = string.Format("{0}_{1}_{2:yyyyMMddHHmm}.pdf", NomeEditorRelatGrupo, cliente.Nome, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                        // sinaliza que é pdf 
            CookieStore.SalvaCookie_Bool("ER_Print", false);

            // gera PDF
            string viewPartial = "_ER_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        // GET: Programacao Horaria EMAIL
        public async Task<ActionResult> ER_EMAIL(string destino, string assunto, string Data, string NumPaginasImprimir = "")
        {
            // verifica paginas
            if (NumPaginasImprimir != "")
            {
                string[] paginasImprimir = NumPaginasImprimir.Split(',');

                if (paginasImprimir.Count() > 0)
                {
                    // converte parta inteiros
                    int[] paginasImprimirInt = Array.ConvertAll(paginasImprimir, int.Parse);

                    // ordena
                    paginasImprimirInt = paginasImprimirInt.OrderBy(i => i).ToArray();

                    // paginas a imprimir
                    ViewBag.PaginasImprimir = paginasImprimirInt;
                }
            }

            // data atual
            DateTime datahora_ultima = DateTime.Now;

            // le cookie datahora
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            if (datahora_cookie.Year != 2000)
            {
                datahora_ultima = datahora_cookie;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDEditorRelatGrupo
            int IDEditorRelatGrupo = ViewBag._IDEditorRelatGrupo;

            // prepara listas
            Editor_EditorRelatPageDominio editor = ER_Superv_Prepara(IDEditorRelatGrupo, 1);

            // retorna listas
            ViewBag.Editor = editor;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // nome do grupo de paineis
            string NomeEditorRelatGrupo = "";

            // le grupo
            EditorRelatGrupoMetodos grupoMetodos = new EditorRelatGrupoMetodos();
            EditorRelatGrupoDominio grupo = new EditorRelatGrupoDominio();

            if (IDEditorRelatGrupo > 0)
            {
                // le grupo               
                grupo = grupoMetodos.ListarPorId(IDEditorRelatGrupo);

                if (grupo != null)
                {
                    NomeEditorRelatGrupo = grupo.Nome;
                }
            }

            ViewBag.NomeGrupo = NomeEditorRelatGrupo;

                            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:D}", datahora_ultima);

            // nome do arquivo
            string nomeArquivo = string.Format("{0}_{1}_{2:yyyyMMddHHmm}.pdf", NomeEditorRelatGrupo, cliente.Nome, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // sinaliza que é pdf 
            CookieStore.SalvaCookie_Bool("ER_Print", false);

            // gera PDF
            string viewPartial = "_ER_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "ModeloImpressaoEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.NomeGrupo", ViewBag.NomeGrupo);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // email template
        public static async Task<string> EMailTemplate(string template)
        {
            var templateFilePath = HostingEnvironment.MapPath("~/Content/emailTemplates/") + template + ".cshtml";
            StreamReader objstreamreaderfile = new StreamReader(templateFilePath);
            var body = await objstreamreaderfile.ReadToEndAsync();
            objstreamreaderfile.Close();
            return body;
        }

        // GET: Editor Relatorios Print
        public ActionResult ER_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:D}", data_atual);

            // IDEditorRelatGrupo
            int IDEditorRelatGrupo = ViewBag._IDEditorRelatGrupo;

            // le grupo
            EditorRelatGrupoMetodos grupoMetodos = new EditorRelatGrupoMetodos();
            EditorRelatGrupoDominio grupo = new EditorRelatGrupoDominio();

            string NomeEditorRelatGrupo = "";

            if (IDEditorRelatGrupo > 0)
            {
                // le grupo               
                grupo = grupoMetodos.ListarPorId(IDEditorRelatGrupo);

                if (grupo != null)
                {
                    NomeEditorRelatGrupo = grupo.Nome;
                }
            }

            ViewBag.NomeGrupo = NomeEditorRelatGrupo;

            // prepara listas
            Editor_EditorRelatPageDominio editor = ER_Superv_Prepara(IDEditorRelatGrupo, 1);

            // retorna listas
            ViewBag.Editor = editor;

            // sinaliza que é print 
            CookieStore.SalvaCookie_Bool("ER_Print", true);

            // imprime
            return View();
        }
    }
}