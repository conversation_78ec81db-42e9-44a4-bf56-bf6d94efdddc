﻿using System;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class EditorRelatController : Controller
    {
        private void ER_Atualizar_Demanda_Media_Mes(int IDEditorRelat, int IDMedicao, DateTime Data)
        {
            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);
                        
            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("ER_Editar");
            ViewBag.Editar = editar_cookie;
            
            // calcula dados do grafico caso nao seja edicao
            if (!editar_cookie)
            {
                Calc_Demanda_Media_Mes(medicao.IDCliente, IDMedicao, Data);
            }

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", Data);

            // nome da medicao
            ViewBag.NomeMedicao = medicao.Nome;

            // data atual
            string IDEditorRelatTxt = string.Format("et{0}", IDEditorRelat);
            ViewBag.IDEditorRelatTxt = IDEditorRelatTxt;

            return;
        }

        // Demanda Media Mes PDF
        public ActionResult _ER_Demanda_Media_Mes_PDF(EditorRelatDominio editorRelat)
        {
            // le medicao 
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicoesMetodos.ListarPorId(editorRelat.IDMedicao);

            // nome medicao
            ViewBag.NomeMedicao = medicao.Nome;

            // le cookies
            LeCookies_SmartEnergy();

            // DateTime
            DateTime Data = DateTime.Now;

            // le cookie datahora
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            if (datahora_cookie.Year != 2000)
            {
                Data = datahora_cookie;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", Data);

            // IDCliente
            int IDCliente = ViewBag._IDCliente;


            Calc_Demanda_Media_Mes(IDCliente, editorRelat.IDMedicao, Data);

            return PartialView(editorRelat);
        }

        // Calcula
        public void Calc_Demanda_Media_Mes(int IDCliente, int IDMedicao, DateTime Data)
        {
            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // desconsiderar fim de semana 
            bool desconsiderarFds = false;

            // consultor JBS desconsidera os finais de semana para calculo
            if (IDConsultor == CLIENTES_ESPECIAIS.GESTOR_JBS)
            {
                desconsiderarFds = true;
            }

            // retorno inicialmente sem registros
            int retorno = 2;

            // resultado
            EN_Metodos enMetodos = new EN_Metodos();
            EN_DemandaMediaMes result = enMetodos.DemandaMediaMes(IDCliente, IDMedicao, Data, desconsiderarFds);

            // se possui registro retorno ok
            if (result.PossuiRegistros) retorno = 0;

            //
            // GRAFICO DEMANDA MEDIA - DIÁRIO
            //

            // grafico demanda media
            var DemandaMedia = new double[98];
            var PeriodoMedia = new int[98];
            var Contrato = new double[98];
            var Tolerancia = new double[98];
            var Datas = new string[98];
            var DatasN = new DateTime[98];
            var Horas = new string[98];

            double Dem_max_grafico = 0.0;

            // valores 
            DateTime strData = new DateTime(Data.Year, Data.Month, Data.Day, 0, 15, 0);

            // volta 15 min para primeira barra
            strData = strData.AddMinutes(-15);

            int i = 0;
            int j = 0;

            for (i = 0; i < 98; i++)
            {
                // formata label 
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Horas[i] = strData.ToString("HH:mm");

                // proximos 15 minutos 
                strData = strData.AddMinutes(15);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera 
                    DemandaMedia[i] = result.DemandaMedia[0];
                    PeriodoMedia[i] = result.PeriodoMedia[0];
                    Contrato[i] = result.ContratoFP;
                    Tolerancia[i] = result.ToleranciaFP;
                }

                // verifica se ultima barra 
                if (i == 97)
                {
                    // zera 
                    DemandaMedia[i] = result.PeriodoMedia[95];
                    PeriodoMedia[i] = result.PeriodoMedia[95];
                    Contrato[i] = result.ContratoFP;
                    Tolerancia[i] = result.ToleranciaFP;
                }

                if (i >= 1 && i <= 96)
                {
                    // copia 
                    j = i - 1;

                    DemandaMedia[i] = result.DemandaMedia[j];
                    PeriodoMedia[i] = result.PeriodoMedia[j];

                    // tolerancia 
                    if (result.PeriodoMedia[j] == 0)
                    {
                        Contrato[i] = result.ContratoP;
                        Tolerancia[i] = result.ToleranciaP;
                    }
                    else
                    {
                        Contrato[i] = result.ContratoFP;
                        Tolerancia[i] = result.ToleranciaFP;
                    }

                    // verifica se sem registro
                    if (result.PeriodoMedia[j] == 3)
                    {
                        DemandaMedia[i] = 0.0;
                    }

                    // verifica demanda maxima 
                    if (DemandaMedia[i] > Dem_max_grafico)
                        Dem_max_grafico = DemandaMedia[i];

                    if (Contrato[i] > Dem_max_grafico)
                        Dem_max_grafico = Contrato[i];

                    if (Tolerancia[i] > Dem_max_grafico)
                        Dem_max_grafico = Tolerancia[i];
                }
            }

            Dem_max_grafico = Dem_max_grafico * 1.1;

            if (Dem_max_grafico < 1.0)
            {
                Dem_max_grafico = 1.0;
            }

            ViewBag.DemMaxGrafico = Dem_max_grafico;

            // copia valore do grafico para view 
            ViewBag.DemandaMedia = DemandaMedia;
            ViewBag.PeriodoMedia = PeriodoMedia;
            ViewBag.Contrato = Contrato;
            ViewBag.Tolerancia = Tolerancia;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Horas = Horas;
            ViewBag.Retorno = retorno;

            // nome - painel jbs demanda
            ViewBag.NomeRelat = string.Format("Demanda Média");

            // contrato mês ponta 
            ViewBag.Contrato_MesP = string.Format("{0:#,##0.0}", result.ContratoP);

            // contrato mês fora ponta 
            ViewBag.Contrato_MesFP = string.Format("{0:#,##0.0}", result.ContratoFP);

            // tolerancia mês ponta 
            ViewBag.ToleranciaP_Mes = string.Format("{0:#,##0.0}", result.ToleranciaP);

            // tolerancia mês fora ponta 
            ViewBag.ToleranciaFP_Mes = string.Format("{0:#,##0.0}", result.ToleranciaFP);
        }
    }
}