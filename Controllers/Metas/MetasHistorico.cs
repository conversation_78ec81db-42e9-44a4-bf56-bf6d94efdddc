﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class MetasController
    {
        // GET: Metas - Medicoes
        public ActionResult Metas_Medicoes(int IDCliente)
        {
            // tela de ajuda - Metas
            CookieStore.SalvaCookie_String("PaginaAjuda", "Metas_Medicoes");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le medições
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            List<MedicoesDominio> listaMedicoes = medicoesMetodos.ListarTodos(IDCliente, ViewBag._ConfigMed);

            // crio lista MetasMedicoes
            List<MetasMedicoesDominio> listaMedicoesMeta = new List<MetasMedicoesDominio>();

            // preencher lista MetasMedicoes
            if( listaMedicoes != null )
            {
                // percorre lista
                foreach(MedicoesDominio medicao in listaMedicoes)
                {
                    // verifico se eh energia
                    if( medicao.IDTipoMedicao == 0 || medicao.IDTipoMedicao == 1 )
                    {
                        // preencho temporario
                        MetasMedicoesDominio temp = new MetasMedicoesDominio();

                        temp.IDCliente = medicao.IDCliente;
                        temp.IDMedicao = medicao.IDMedicao;
                        temp.Nome = medicao.Nome;
                        temp.NumRegistros = 0;

                        // numero de registros
                        MetasConsumoMetodos metasMetodos = new MetasConsumoMetodos();
                        temp.NumRegistros = metasMetodos.NumRegistros(medicao.IDMedicao);

                        // adiciona na lista
                        listaMedicoesMeta.Add(temp);
                    }
                }
            }

            return View(listaMedicoesMeta);
        }

        public ActionResult Metas_Historico(int IDMedicao)
        {
            // tela de ajuda - Metas
            CookieStore.SalvaCookie_String("PaginaAjuda", "Metas_Historico");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes 
            Permissoes();

            // le medicao
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le metas 
            MetasConsumoMetodos metasMetodos = new MetasConsumoMetodos();
            List<MetasConsumoDominio> listaMetas = metasMetodos.ListarPorIDMedicao(IDMedicao);

            ViewBag._IDCliente = medicao.IDCliente;
            ViewBag.IDMedicao = medicao.IDMedicao;
            ViewBag.NomeMedicao = medicao.Nome;

            return View(listaMetas);
        }

        // GET: Meta - Historico - Editar
        public ActionResult Metas_Historico_Editar(int IDMeta, int IDMedicao)
        {
            // tela de ajuda - Metas
            CookieStore.SalvaCookie_String("PaginaAjuda", "Metas_HistoricoEditar");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le medicao
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicoesMetodos.ListarPorId(IDMedicao);

            ViewBag.NomeMedicao = medicao.Nome;

            MetasConsumoDominio temp = new MetasConsumoDominio();

            // verifica se adicionando
            if (IDMeta == 0)
            {
                // zera com default
                temp.ID = 0;
                temp.MetaKWhP = 0;
                temp.MetaKWhF = 0;
                temp.MetaFatura = 0;
                temp.Ini = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 0, 0);
                temp.Fim = temp.Ini.AddMonths(1);
                temp.IDMedicao = IDMedicao;
                temp.IDCliente = medicao.IDCliente;
            }
            else
            {
                MetasConsumoMetodos metasMetodos = new MetasConsumoMetodos();
                temp = metasMetodos.ListarPorID(IDMeta);
            }
            return View(temp);
        }

        // POST: Metas - Historico - Salvar
        [HttpPost]
        public ActionResult Metas_Historico_Salvar(MetasConsumoDominio temp)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro registro historico com a mesma data
            MetasConsumoMetodos metasMetodos = new MetasConsumoMetodos();
            if (metasMetodos.VerificarDuplicidade(temp))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Data existente."
                };
            }
            else
            {
                // ajusta data inicial
                temp.Ini = new DateTime(temp.Ini.Year, temp.Ini.Month, temp.Ini.Day, 0, 0, 0);

                // ajusta data final 
                temp.Fim = new DateTime(temp.Fim.Year, temp.Fim.Month, temp.Fim.Day, 0, 0, 0);

                // salva historico
                metasMetodos.Salvar(temp);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Meta - Historico - Excluir
        public ActionResult Metas_Historico_Excluir(int IDMeta)
        {
            // historico KPI
            MetasConsumoMetodos metasMetodos = new MetasConsumoMetodos();
            metasMetodos.Excluir(IDMeta);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}