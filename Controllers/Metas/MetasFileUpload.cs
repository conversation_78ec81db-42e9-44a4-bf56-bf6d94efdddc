﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.OleDb;
using System.IO;
using System.Web;
using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class MetasController
    {
        #region Actions

        /// <summary>
        /// Uploads the file.
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public virtual ActionResult UploadFile()
        {
            // arquivo enviado
            HttpPostedFileBase myFile = Request.Files["fileupload"];

            // inicia variaveis
            bool isUploaded = false;
            string message = "Envio falhou";

            if (myFile != null && myFile.ContentLength != 0)
            {
                string pathForSaving = Server.MapPath("~/HistMetas");
                if (this.CreateFolderIfNeeded(pathForSaving))
                {
                    try
                    {
                        // nome do arquivo
                        string nome_arq = myFile.FileName;

                        // nome do caminho e arquivo
                        string caminho_arq = Path.Combine(pathForSaving, myFile.FileName);

                        // extensão através dos ultimos 4 caracteres
                        string extensao_arq = nome_arq.Substring(nome_arq.Length - 4).ToLower();

                        // verifica se arquivo existe e apaga
                        FileInfo file = new FileInfo(caminho_arq);

                        if (file.Exists)
                        {
                            // deleta
                            file.Delete();
                        }

                        // salva arquivo
                        myFile.SaveAs(caminho_arq);

                        // flag erro
                        bool OcorreuErro = false;
                        string MensagemErro = "Formato não permitido";

                        // historico
                        List<MetasConsumoDominio> HistMetas = new List<MetasConsumoDominio>();

                        // trata arquivo EXCEL
                        if (extensao_arq == ".xls" || extensao_arq == "xlsx")
                        {
                            // ler registros
                            HistMetas = LerExcel(caminho_arq, ref MensagemErro);

                            if (HistMetas == null || MensagemErro != "OK")
                            {
                                // falha
                                OcorreuErro = true;
                                message = string.Format("Envio falhou: {0}", MensagemErro);
                            }
                        }
                        else
                        {
                            // flag erro
                            OcorreuErro = true;
                            MensagemErro = "Formato não permitido";
                        }

                        // verifica se nao ocorreu erro de leitura
                        if (!OcorreuErro)
                        {
                            // enviado, lido e atualizado BD com sucesso
                            isUploaded = true;
                            message = "Arquivo enviado com sucesso!";
                        }
                    }
                    catch (Exception ex)
                    {
                        // falha
                        message = string.Format("Envio falhou: {0}", ex.Message);
                    }
                }
            }
            return Json(new { isUploaded = isUploaded, message = message }, "text/html");
        }

        public List<MetasConsumoDominio> LerExcel(string caminho_arq, ref string MensagemErro)
        {
            // conexao
            // baixar o provedor de acesso Excel 2010 em "https://www.microsoft.com/en-us/download/details.aspx?id=13255"
            // AccessDatabaseEngine_X64.exe
            // configurando no SQL SERVER https://www.dirceuresende.com/blog/sql-server-como-instalar-os-drivers-microsoft-ace-oledb-12-0-e-microsoft-jet-oledb-4-0/
            OleDbConnection _olecon;
            OleDbCommand _oleCmd;
            //String _StringConexao = String.Format(@"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={0};Extended Properties='Excel 12.0;HDR=YES;ReadOnly=False';", caminho_arq);
            String _StringConexao = String.Format(@"Provider=Microsoft.JET.OLEDB.4.0;Data Source={0};Extended Properties='Excel 8.0;HDR=YES;';", caminho_arq);
            _olecon = new OleDbConnection(_StringConexao);
            _oleCmd = new OleDbCommand();

            // historico de metas
            MetasConsumoMetodos metasMetodos = new MetasConsumoMetodos();
            List<MetasConsumoDominio> metasHist = new List<MetasConsumoDominio>();

            // linha convertida
            int linha = -1;
            int coluna = -1;

            // le Excel
            try
            {
                // conecta
                _olecon.Open();
                _oleCmd.Connection = _olecon;
                _oleCmd.CommandType = CommandType.Text;

                // le excel
                _oleCmd.CommandText = "SELECT * FROM [Metas$]";
                OleDbDataReader reader = _oleCmd.ExecuteReader();

                // IDCliente
                linha = 0;

                while (reader.Read())
                {
                    try
                    {
                        // le enquanto tem dados na coluna
                        for (int count = 0; count < (reader.FieldCount - 2) / 3; count++)
                        {
                            // le registro
                            MetasConsumoDominio meta = new MetasConsumoDominio();
                            meta.ID = 0;

                            // IDCliente
                            coluna = 0;
                            meta.IDCliente = Convert.ToInt32(reader.GetDouble(0));

                            // IDMedicao
                            coluna = 1;
                            meta.IDMedicao = Convert.ToInt32(reader.GetDouble(1));

                            // Data Inicial e Final
                            coluna = (count * 3) + 2;
                            meta.Ini = reader.GetDateTime(coluna);
                            meta.Fim = meta.Ini.AddMonths(1);

                            // Consumo
                            coluna = (count * 3) + 3;
                            meta.MetaKWhP = Convert.ToInt32(reader.GetDouble(coluna));

                            // Fatura
                            coluna = (count * 3) + 4;
                            meta.MetaFatura = reader.GetDouble(coluna);

                            // insere na lista
                            metasHist.Add(meta);

                            // exclui se existir
                            metasMetodos.ExcluirData(meta.IDMedicao, meta.Ini, meta.Fim);

                            // salva
                            metasMetodos.Salvar(meta);
                        }
                    }
                    catch
                    { }

                    // proxima linha
                    linha++;
                }

                reader.Close();

                // termina
                if (_oleCmd != null)
                {
                    _oleCmd.Parameters.Clear();
                    _oleCmd.Dispose();
                }
                _oleCmd = null;

                if (_olecon != null)
                {
                    if (_olecon.State == ConnectionState.Open)
                        _olecon.Close();
                    _olecon.Dispose();
                }
                _olecon = null;

            }
            catch (Exception ex)
            {
                // termina
                if (_oleCmd != null)
                {
                    _oleCmd.Parameters.Clear();
                    _oleCmd.Dispose();
                }
                _oleCmd = null;

                if (_olecon != null)
                {
                    if (_olecon.State == ConnectionState.Open)
                        _olecon.Close();
                    _olecon.Dispose();
                }
                _olecon = null;
                
                // erro caso iniciou leitura do excel
                if (linha >= 0 && coluna >= 0)
                {  
                    MensagemErro = ex.Message + string.Format(" [Linha {0} | Coluna {1}]", linha + 2, coluna + 1);
                    return (null);
                }

                // erro
                MensagemErro = ex.Message;
                return (null);
            }
            
            // ok
            MensagemErro = "OK";
            return (metasHist);
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Creates the folder if needed.
        /// </summary>
        /// <param name="path">The path.</param>
        /// <returns></returns>
        private bool CreateFolderIfNeeded(string path)
        {
            bool result = true;
            if (!Directory.Exists(path))
            {
                try
                {
                    Directory.CreateDirectory(path);
                }
                catch (Exception)
                {
                    /*TODO: You must process this exception.*/
                    result = false;
                }
            }
            return result;
        }

        #endregion
    }
}