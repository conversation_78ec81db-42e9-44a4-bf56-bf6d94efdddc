﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class MensagensController : Controller
    {
        // GET: Mensagens
        public ActionResult InBox()
        {
            // tela de ajuda - mensagens inbox
            ViewBag.PaginaAjuda = "Contato";

            // le cookies
            LeCookies_SmartEnergy();

            // IDUsuario
            int IDUsuario = ViewBag._IDUsuario;

            // le mensagens do usuario
            UsuarioMensagemMetodos usuarioMensagemMetodos = new UsuarioMensagemMetodos();
            List<UsuarioMensagens> usuarioMensagens = usuarioMensagemMetodos.MensagensUsuario(IDUsuario);

            // mensagens nao lidas
            int NumMensagensNaoLidas = usuarioMensagemMetodos.NumMensagens_NaoLidas(IDUsuario);
            ViewBag.NumMensagensNaoLidas = NumMensagensNaoLidas;

            return View(usuarioMensagens);
        }

        // GET: Mensagem - Excluir
        public ActionResult Mensagem_Excluir(int IDUsuarioMensagem)
        {
            // apaga a mensagem
            UsuarioMensagemMetodos usuarioMensagemMetodos = new UsuarioMensagemMetodos();
            usuarioMensagemMetodos.Excluir(IDUsuarioMensagem);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        public ActionResult EmailView(int IDUsuarioMensagem)
        {
            // tela de ajuda - mensagem
            ViewBag.PaginaAjuda = "Contato";

            // le cookies
            LeCookies_SmartEnergy();

            // IDUsuario
            int IDUsuario = ViewBag._IDUsuario;

            // le mensagens do usuario
            UsuarioMensagemMetodos usuarioMensagemMetodos = new UsuarioMensagemMetodos();
            UsuarioMensagens usuarioMensagem = usuarioMensagemMetodos.MensagemUsuario(IDUsuarioMensagem);

            return View(usuarioMensagem);
        }

        // Listas utilizadas
        private void PreparaListas_Mensagem()
        {
            // le tipos remente
            ListaTiposMetodos listaMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposRemetente = listaMetodos.ListarTodos("TipoMensagemRemetente", true, 0);
            ViewBag.listatiposRemetente = listatiposRemetente;

            // le tipos tag
            List<MensagemTagsDominio> listaTags = listaMetodos.ListarTodos_MensagemTags();
            ViewBag.listaTags = listaTags;

            return;
        }

        public ActionResult EmailSend()
        {
            // tela de ajuda - mensagem
            ViewBag.PaginaAjuda = "Contato";

            // le cookies
            LeCookies_SmartEnergy();

            // prepara listas
            PreparaListas_Mensagem();

            // nome cliente
            int IDCliente = ViewBag._IDCliente;
            string NomeCliente = "";

            ClientesMetodos clientesMetodos = new ClientesMetodos();
            ClientesDominio cliente = clientesMetodos.ListarPorId(IDCliente);
            if(cliente != null)
            {
                NomeCliente = cliente.Nome;
            }
            ViewBag.NomeCliente = NomeCliente;

            return View();
        }

        // GET: EnviarMensagem
        [ValidateInput(false)]
        public ActionResult EnviarMensagem(int EnviarPara, int IDRemetente, string Assunto, string Texto, int IDTag)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // data e hora atual
            DateTime datahora = DateTime.Now;

            // prepara mensagem
            UsuarioMensagemTextoDominio mensagemTexto = new UsuarioMensagemTextoDominio();

            mensagemTexto.IDMensagem = 0;
            mensagemTexto.DataHora = datahora;
            mensagemTexto.IDRemetente = IDRemetente;
            mensagemTexto.Assunto = Assunto;
            mensagemTexto.Texto = Texto;
            mensagemTexto.Attachment = "";
            mensagemTexto.IDTag = IDTag;

            // salvar mensagem
            UsuarioMensagemTextoMetodos usuarioTextoMetodos = new UsuarioMensagemTextoMetodos();
            usuarioTextoMetodos.Salvar(mensagemTexto);

            // pegar IDMensagem pelo assunto e datahora
            UsuarioMensagemTextoDominio msgTexto = usuarioTextoMetodos.ListarPorAssuntoDataHora(Assunto, mensagemTexto.DataHora);

            if( msgTexto != null )
            {
                if(msgTexto.IDMensagem > 0)
                {
                    // enviar mensagem para usuarios
                    UsuarioMensagemMetodos usuarioMensagemMetodos = new UsuarioMensagemMetodos();
                    UsuarioMensagemDominio mensagem = new UsuarioMensagemDominio();

                    // preenche
                    mensagem.IDUsuarioMensagem = 0;
                    mensagem.IDMensagem = msgTexto.IDMensagem;
                    mensagem.DataHoraStatus = mensagemTexto.DataHora;
                    mensagem.IDStatus = 0;

                    // usuarios
                    UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
                    List<UsuarioDominio> usuarios = usuarioMetodos.ListarEnviarMensagem(EnviarPara, IDCliente);

                    if( usuarios != null )
                    {
                        // percorre usuarios
                        foreach(UsuarioDominio usuario in usuarios)
                        {
                            // envia mensagem
                            mensagem.IDUsuario = usuario.IDUsuario;
                            usuarioMensagemMetodos.Salvar(mensagem);
                        }
                    }
                }
            }

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

    }
}