﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SuporteController
    {
        public ActionResult BuscaGatewayProblemas()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // informações
            int IDGateway = ViewBag._IDGateway;

            string NomeGateway = "";
            string NomeCliente = "";

            int NumArquivosProblemas = 0;
            int NumArquivosGateway = 0;

            if (IDGateway > 0)
            {
                // busca nome da gateway e do cliente
                GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
                GatewaysDominio gateway = gatewayMetodos.ListarPorId(IDGateway);

                if (gateway != null)
                {
                    NomeGateway = string.Format("[{0:000000}] {1}", gateway.IDGateway, gateway.Nome);

                    // busca cliente
                    ClientesMetodos clienteMetodos = new ClientesMetodos();
                    ClientesDominio cliente = clienteMetodos.ListarPorId(gateway.IDGateway);

                    if (cliente != null)
                    {
                        NomeCliente = string.Format("[{0:000000}] {1}", cliente.IDCliente, cliente.Nome);
                    }
                }
            }
            else
            {
                IDGateway = 0;
            }

            // caminho Recebidos\Problemas
            string CaminhoRecebidos = ConfigurationManager.AppSettings["Recebidos"];
            string CaminhoProblemas = CaminhoRecebidos + "\\Problemas\\";

            // arquivos em Problemas
            string[] nomeArquivos = Directory.GetFiles(CaminhoProblemas, "*.*");
            NumArquivosProblemas = nomeArquivos.Length;

            // informações
            ViewBag.IDGateway_Search = IDGateway;
            ViewBag.NomeGateway_Search = NomeGateway;
            ViewBag.NomeCliente_Search = NomeCliente;

            // arquivos problemas
            List<ArquivoProblemas> arquivosGateway = new List<ArquivoProblemas>();
            ViewBag.ArquivosGateway = arquivosGateway;

            ViewBag.NumArquivosProblemas = NumArquivosProblemas;
            ViewBag.NumArquivosGateway = NumArquivosGateway;

            return View();
        }

        // GET: Resultado busca
        public PartialViewResult _BuscaGatewayProblemas_Resultado(int IDGateway)
        {            
            // lista de arquivos
            List<ArquivoProblemas> arquivosGateway = new List<ArquivoProblemas>();

            // nome da gateway e cliente
            string NomeGateway = "";
            string NomeCliente = "";

            int NumArquivosProblemas = 0;
            int NumArquivosGateway = 0;

            // verifica IDGateway
            if (IDGateway > 0)
            {
                // busca nome da gateway e do cliente
                GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
                GatewaysDominio gateway = gatewayMetodos.ListarPorId(IDGateway);

                if (gateway != null)
                {
                    NomeGateway = string.Format("[{0:000000}] {1}", gateway.IDGateway, gateway.Nome);

                    // busca cliente
                    ClientesMetodos clienteMetodos = new ClientesMetodos();
                    ClientesDominio cliente = clienteMetodos.ListarPorId(gateway.IDCliente);

                    if (cliente != null)
                    {
                        NomeCliente = string.Format("[{0:000000}] {1}", cliente.IDCliente, cliente.Nome);
                    }
                }
            }
            else
            {
                IDGateway = 0;
            }

            //
            // arquivos em Problemas
            //

            // caminho Recebidos\Problemas
            string CaminhoRecebidos = ConfigurationManager.AppSettings["Recebidos"];
            string CaminhoProblemas = CaminhoRecebidos + "\\Problemas\\";

            // arquivos em Problemas
            string[] nomeArquivos = Directory.GetFiles(CaminhoProblemas, "*.*");
            NumArquivosProblemas = nomeArquivos.Length;

            //
            // arquivos em Problemas da Gateway
            //

            // máscara de busca
            string buscaGateway = "*.*";

            if (IDGateway > 0)
            {
                // máscara de busca com gateway
                buscaGateway = string.Format("*_{0:000000}_*", IDGateway);
            }

            // arquivos em Problemas da Gateway
            nomeArquivos = Directory.GetFiles(CaminhoProblemas, buscaGateway);
            NumArquivosGateway = nomeArquivos.Length;

            // lista arquivos da Gateway
            foreach (string nomeArquivo in nomeArquivos)
            {
                // informações do arquivo
                ArquivoProblemas arquivo = new ArquivoProblemas();

                FileInfo info = new FileInfo(nomeArquivo);
                arquivo.info = info;

                // separa nome do arquivo e motivo
                string[] nomearq_motivo = info.Name.Split('-');

                arquivo.NomeArquivo = nomearq_motivo[0];
                arquivo.Motivo = nomearq_motivo[1];

                // separa IDGateway
                string[] nomearq = nomearq_motivo[0].Split('_');

                int IDGateway_arq = 0;

                if (int.TryParse(nomearq[1], out IDGateway_arq) == false)
                {
                    // erro
                    IDGateway_arq = 0;
                }

                // verifica IDGateway
                if (IDGateway_arq > 0)
                {
                    // busca nome da gateway e do cliente
                    GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
                    GatewaysDominio gateway = gatewayMetodos.ListarPorId(IDGateway_arq);

                    if (gateway != null)
                    {
                        arquivo.NomeGateway = string.Format("[{0:000000}] {1}", gateway.IDGateway, gateway.Nome);

                        // busca cliente
                        ClientesMetodos clienteMetodos = new ClientesMetodos();
                        ClientesDominio cliente = clienteMetodos.ListarPorId(gateway.IDCliente);

                        if (cliente != null)
                        {
                            arquivo.NomeCliente = string.Format("[{0:000000}] {1}", cliente.IDCliente, cliente.Nome);
                        }
                    }
                }
                else
                {
                    IDGateway_arq = 0;
                }

                // IDGateway
                arquivo.IDGateway = IDGateway_arq;

                // insere na lista
                arquivosGateway.Add(arquivo);
            }

            // informações
            ViewBag.IDGateway_Search = IDGateway;
            ViewBag.NomeGateway_Search = NomeGateway;
            ViewBag.NomeCliente_Search = NomeCliente;

            // arquivos recebidos
            ViewBag.ArquivosGateway = arquivosGateway;

            ViewBag.NumArquivosProblemas = NumArquivosProblemas;
            ViewBag.NumArquivosGateway = NumArquivosGateway;

            return PartialView();
        }

        [HttpGet]
        public ActionResult Download(string arquivo)
        {
            // caminho Recebidos\Problemas
            string CaminhoRecebidos = ConfigurationManager.AppSettings["Recebidos"];
            string CaminhoProblemas = CaminhoRecebidos + "\\Problemas\\";

            // informações do arquivo
            string nomeArquivo = CaminhoProblemas + arquivo;
            FileInfo info = new FileInfo(nomeArquivo);

            // retorna o arquivo
            return File(info.FullName, "text/plain", arquivo);
        }

        // GET: Reprocessar
        public ActionResult Reprocessar(string arquivo, string nome)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // copia o arquivo para Recebidos, alterando o nome
            try
            {
                // caminho Recebidos\Problemas
                string CaminhoRecebidos = ConfigurationManager.AppSettings["Recebidos"];
                string CaminhoProblemas = CaminhoRecebidos + "\\Problemas\\";

                // informações do arquivo
                string nomeArquivo = CaminhoProblemas + arquivo;
                FileInfo info = new FileInfo(nomeArquivo);

                // destino
                string destino = CaminhoRecebidos + nome;

                // move arquivo
                info.CopyTo(destino);
            }
            catch (Exception)
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Erro ao copiar para Recebidos."
                };
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Excluir
        public ActionResult Excluir(string arquivo)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // exclui o arquivo
            try
            {
                // caminho Recebidos\Problemas
                string CaminhoRecebidos = ConfigurationManager.AppSettings["Recebidos"];
                string CaminhoProblemas = CaminhoRecebidos + "\\Problemas\\";

                // informações do arquivo
                string nomeArquivo = CaminhoProblemas + arquivo;
                FileInfo info = new FileInfo(nomeArquivo);

                // exclui o arquivo
                info.Delete();
            }
            catch (Exception)
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Erro ao excluir."
                };
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }
    }
}
