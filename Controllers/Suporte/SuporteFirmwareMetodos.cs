﻿////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// [Classe] FirmwareMetodos
//
// Métodos do gerenciamento dos arquivos de firmware
//
// CopiarFirmware()             : Copiar arquivo de firmware para subdiretório de update e o renomeio com o IDGateway
// Excluir()                    : Excluir arquivo do firmware
// ListarPorIDTipoGateway()     : Listar todos os firmwares por tipo de gateway
// IdentificaFirmware()         : Identifica firmware pelo nome do arquivo
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public class FirmwareMetodos
    {
        // Copia arquivo de firmware para subdiretório de update e o renomeio com o IDGateway
        public bool CopiarFirmware(int IDGateway, int versao_int)
        {
            // Formato do nome do arquivo de firmware
            // SmartGateE_VVV_LL_S.dat
            //
            // SmartGateE: Modelo da gateway (Ex. Smart Gate E)
            // VVV: Versão do firmware (Ex. 100) - 100 até 9999
            // LL : Biblioteca dos drivers (Ex. 01) - 01 até 99
            // S  : Status da Versão - 0 para Produção e 1-9 para Betas

            // versão (formato VVVLLS, EX 100010 = versão 100 / biblioteca 01 / produção)
            string versao_txt = versao_int.ToString();

            // verifica se possui digitos mínimos
            if (versao_txt.Length < 6)
            {
                // erro
                return (false);
            }

            // versão, lib e status
            string versao = "000";
            string lib = "00";
            string status = "0";

            // pega versão, lib e status
            Funcoes_GateE.ParseVersao(versao_int, ref versao, ref lib, ref status);

            // caminho Firmware origem
            string caminhoOrigem = ConfigurationManager.AppSettings["Firmware"];

            // nome do arquivo origem
            string nomeArquivoOrigem = string.Format("SmartGateE_{0}_{1}_{2}.dat", versao, lib, status);

            // nome do caminho e arquivo origem
            string nomeArquivoOrigemCompleto = Path.Combine(caminhoOrigem, nomeArquivoOrigem);

            // Formato do nome do arquivo de firmware destino
            // SmartGateE_001234.dat
            //
            // tipo da gateway = SmartGateE
            // IDGateway = 001234

            // caminho Firmware destino
            string caminhoDestino = ConfigurationManager.AppSettings["Firmware_Update"];

            // nome do arquivo destino
            string nomeArquivoDestino = string.Format("SmartGateE_GT{0:000000}.dat", IDGateway);

            // nome do caminho e arquivo destino
            string nomeArquivoDestinoCompleto = Path.Combine(caminhoDestino, nomeArquivoDestino);

            //
            // Copia arquivo
            //

            try
            {
                // copia arquivo, alterando o nome
                File.Copy(nomeArquivoOrigemCompleto, nomeArquivoDestinoCompleto, true);
            }
            catch
            {
                // erro
                return (false);
            }

            // ok
            return (true);
        }

        // Mover arquivo de firmware do subdiretório Temp para produção
        public bool MoverFirmware_Temp2Producao(string nomeArquivo)
        {
            //
            // Move arquivo
            //

            try
            {
                // caminho origem e destino Firmware 
                string caminhoDestino = ConfigurationManager.AppSettings["Firmware"];
                string caminhoOrigem = Path.Combine(caminhoDestino, "Temp");

                // nome do caminho e arquivo origem
                string nomeArquivoOrigemCompleto = Path.Combine(caminhoOrigem, nomeArquivo);

                // nome do caminho e arquivo destino
                string nomeArquivoDestinoCompleto = Path.Combine(caminhoDestino, nomeArquivo);

                // verifica se arquivo origem existe
                if (File.Exists(nomeArquivoOrigemCompleto))
                {
                    // exclui arquivo (se existir)
                    if (File.Exists(nomeArquivoDestinoCompleto))
                    {
                        File.Delete(nomeArquivoDestinoCompleto);
                    }

                    // move arquivo
                    File.Move(nomeArquivoOrigemCompleto, nomeArquivoDestinoCompleto);
                }
            }
            catch
            {
                // erro
                return (false);
            }

            // ok
            return (true);
        }

        // Excluir arquivo do firmware
        public bool Excluir(string nomeArquivo)
        {
            try
            {
                // caminho Firmware
                string caminhoFirmware = ConfigurationManager.AppSettings["Firmware"];

                // nome do caminho e arquivo
                string nomeArquivoCompleto = Path.Combine(caminhoFirmware, nomeArquivo);

                // exclui arquivo
                File.Delete(nomeArquivoCompleto);
            }
            catch (Exception)
            {
                // erro
                return (false);
            }

            // ok
            return (true);
        }

        // Listar todos os firmwares por tipo de gateway
        public List<FirmwareDominio> ListarPorIDTipoGateway(int IDTipoGateway, string excluir_versao = "000000")
        {
            // lista Firmware
            List<FirmwareDominio> listaFirmware = new List<FirmwareDominio>();

            try
            {
                // caminho Firmware
                string caminhoFirmware = ConfigurationManager.AppSettings["Firmware"];

                // lista arquivos em Firmware
                DirectoryInfo infoDiretorio = new DirectoryInfo(caminhoFirmware);
                FileInfo[] listaArquivos = infoDiretorio.GetFiles("*.dat").OrderBy(f => f.Name).ToArray();

                // verifica se possui arquivos
                if (listaArquivos != null)
                {
                    // percorre arquivos
                    foreach (FileInfo infoArquivo in listaArquivos)
                    {
                        // identifica firmware
                        FirmwareDominio firmware = IdentificaFirmware(infoArquivo.Name);

                        // verifica se gateway valida
                        if (firmware.IDTipoGateway > 0)
                        {
                            // verifica se todos
                            if (IDTipoGateway == 0)
                            {
                                // adiciona na lista
                                listaFirmware.Add(firmware);
                            }
                            else
                            {
                                // verifica se é o tipo desejado
                                if (firmware.IDTipoGateway == IDTipoGateway)
                                {
                                    // verifica se a versão é diferente da versão a ser excluída da lista
                                    if (firmware.VersaoCompleta != excluir_versao)
                                    {
                                        // adiciona na lista
                                        listaFirmware.Add(firmware);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch
            { }

            return listaFirmware;
        }

        // Identifica firmware pelo nome do arquivo
        public FirmwareDominio IdentificaFirmware(string nomeArquivo)
        {
            // Formato do nome do arquivo de firmware
            // SmartGateE_VVV_LL_S.dat
            //
            // SmartGateE: Modelo da gateway (Ex. SmartGate E)
            // VVV: Versão do firmware (Ex. 100) - 100 até 9999
            // LL : Biblioteca dos drivers (Ex. 01) - 01 até 99
            // S  : Status da Versão - 0 para Produção e 1-9 para Betas

            // nome do arquivo
            string nomeArquivoSemExtensao = Path.GetFileNameWithoutExtension(nomeArquivo);

            // separa
            string[] partes = nomeArquivoSemExtensao.Split('_');

            // firmware
            FirmwareDominio firmware = new FirmwareDominio();
            firmware.Arquivo = nomeArquivo;
            firmware.IDTipoGateway = 0;
            firmware.VersaoCompleta = "000000";
            firmware.Versao = "0";
            firmware.Lib = "0";
            firmware.Status = "0";
            firmware.Versao_txt = "0.00";

            // verifica se tem quatro partes
            if (partes.Length != 4)
            {
                // erro
                return (firmware);
            }

            // verifica se tem modelo, versão, biblioteca e status
            if (partes[0].Count() == 0 || partes[1].Count() == 0 || partes[2].Count() == 0 || partes[3].Count() == 0)
            {
                // erro
                return (firmware);
            }

            // tipo da gateway
            switch (partes[0].ToUpper())
            {
                case "SMARTGATEE":      // SmartGate E
                    firmware.IDTipoGateway = TIPO_GATEWAY.GATE_E;
                    break;

                case "SMARTGATEX":      // SmartGate X
                    firmware.IDTipoGateway = TIPO_GATEWAY.GATE_X_422;
                    break;

                default:
                    firmware.IDTipoGateway = 0;
                    break;
            }

            // versão
            firmware.Versao = partes[1];

            // biblioeteca
            firmware.Lib = partes[2];

            // status
            firmware.Status = partes[3];

            // versão texto
            firmware.Versao_txt = Funcoes_GateE.ParseVersao(firmware.Versao, firmware.Lib, firmware.Status);

            // versão completa
            firmware.VersaoCompleta = firmware.Versao + firmware.Lib + firmware.Status;

            // ok
            return (firmware);
        }
    }
}
