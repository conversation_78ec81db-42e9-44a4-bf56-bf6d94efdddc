﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SuporteController
    {
        public ActionResult GatewaysBateriaFraca()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // leio lista de gestores
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            List<UsuarioDominio> gestores = usuarioMetodos.ListarTodosConsultores();

            // leio lista de supervisao gateways
            SupervGatewaysMetodos gatewaysMetodos = new SupervGatewaysMetodos();
            List<SupervGatewaysDominio> GatewaysSupervListTodos = new List<SupervGatewaysDominio>();
            GatewaysSupervListTodos = gatewaysMetodos.ListarTodos(100);

            // leio lista de supervisao bateria fraca
            SupervGatewaysBateriaFracaMetodos gatewaysBateriaFracaMetodos = new SupervGatewaysBateriaFracaMetodos();
            List<SupervGatewaysBateriaFracaDominio> GatewaysBateriaFracaList = new List<SupervGatewaysBateriaFracaDominio>();
            GatewaysBateriaFracaList = gatewaysBateriaFracaMetodos.ListarTodos();

            // verifica se existe
            if (GatewaysSupervListTodos != null && GatewaysBateriaFracaList != null)
            {
                // analiso os atrasos
                foreach (SupervGatewaysBateriaFracaDominio gatewayBateriaFraca in GatewaysBateriaFracaList)
                {
                    //
                    // Bateria Fraca
                    //

                    // encontra gateway
                    SupervGatewaysDominio gateway = GatewaysSupervListTodos.Find(x => x.IDGateway == gatewayBateriaFraca.IDGateway);
                    gatewayBateriaFraca.IDCliente = 0;
                    gatewayBateriaFraca.NomeCliente = "---";
                    gatewayBateriaFraca.IDConsultor = 0;
                    gatewayBateriaFraca.NomeConsultor = "---";
                    gatewayBateriaFraca.Nome = "---";
                    gatewayBateriaFraca.Fantasia = "---";
                    gatewayBateriaFraca.ModeloEq = "---";
                    gatewayBateriaFraca.VersaoEq = "---";

                    if (gateway != null)
                    {
                        // copia dados de supervisão
                        gatewayBateriaFraca.IDCliente = gateway.IDCliente;
                        gatewayBateriaFraca.NomeCliente = gateway.NomeCliente;
                        gatewayBateriaFraca.IDConsultor = gateway.IDConsultor;
                        gatewayBateriaFraca.NomeConsultor = gateway.NomeConsultor;
                        gatewayBateriaFraca.Nome = gateway.Nome;
                        gatewayBateriaFraca.Fantasia = gateway.Fantasia;
                        gatewayBateriaFraca.ModeloEq = gateway.ModeloEq;
                        gatewayBateriaFraca.VersaoEq = gateway.VersaoEq;

                        // nome do gestor
                        UsuarioDominio gestor = gestores.Find(x => x.IDUsuario == gatewayBateriaFraca.IDConsultor);

                        if (gestor != null)
                        {
                            gatewayBateriaFraca.NomeConsultor = gestor.NomeUsuario;
                        }
                        else
                        {
                            gatewayBateriaFraca.NomeConsultor = "---";
                        }
                    }
                }
            }

            // lista de gateways com bateria fraca
            ViewBag.GatewaysBateriaFracaList = GatewaysBateriaFracaList;


            // observacao tags
            List<ObservacaoTagsDominio> tags = new List<ObservacaoTagsDominio>();
            ViewBag.tags = tags;

            // observacao
            ObservacaoGatewayDominio observacao = new ObservacaoGatewayDominio();
            int IDGatewaySelecionado = 0;

            ViewBag.observacao = observacao;
            ViewBag.IDGatewaySelecionado = IDGatewaySelecionado;

            // observacoes
            List<ObservacaoGatewayDominio> observacoes = new List<ObservacaoGatewayDominio>();
            ViewBag.Observacoes = observacoes;

            return View(GatewaysBateriaFracaList);
        }
    }
}