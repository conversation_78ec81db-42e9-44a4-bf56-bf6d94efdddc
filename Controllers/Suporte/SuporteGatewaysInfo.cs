﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SuporteController
    {
        public ActionResult GatewaysInfo()
        {
            // tela de ajuda - gateways info
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCLiente;

            // tipo de acesso do usuario
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // IDUsuario
            int IDConsultor = ViewBag._IDConsultor;

            // lista de medicoes
            List<int> ConfigMedList = ViewBag._ConfigMed;

            // lista de gateways
            List<int> GatewaysListInfo = new List<int>();
            List<SupervGatewaysDominio> GatewaysSupervListInfo = new List<SupervGatewaysDominio>();

            // le configuracao das medicoes do cliente atual
            var medicoesMetodos = new MedicoesMetodos();
            var medicoes = new List<MedicoesDominio>();
            medicoes = medicoesMetodos.ListarPorIDCliente(IDCliente);

            // com o ConfigMedList monto a lista de gateways do usuario
            int contador;
            int IDGateway = -1;

            // percorre lista
            for (contador = 0; contador < medicoes.Count(); contador++)
            {
                // inicialmente nao tem medicao
                IDGateway = -1;

                // verifica se usuario eh cliente gerente ou operador
                if ((IDTipoAcesso == TIPO_ACESSO.CLIENTE_ADMIN || IDTipoAcesso == TIPO_ACESSO.CLIENTE_OPER || IDTipoAcesso == TIPO_ACESSO.DEMONSTRACAO) && ConfigMedList != null)
                {
                    // verifica se medicao esta habilitada para o usuario
                    if (ConfigMedList.Contains(medicoes[contador].IDMedicao))
                    {
                        // pego IDGateway
                        IDGateway = medicoes[contador].IDGateway;
                    }
                }
                else
                {
                    // pego IDGateway
                    IDGateway = medicoes[contador].IDGateway;
                }

                // verifico se tem gateway para inserir
                if (IDGateway > 0)
                {
                    // verifico se gateway ja existe na lista
                    if (GatewaysListInfo.Contains(IDGateway))
                    {
                        // indico q nao tem gateway
                        IDGateway = -1;
                    }
                }

                // verifico novamente se tem gateway para inserir
                if (IDGateway > 0)
                {
                    // leio supervisao e configuracao da gateway
                    SupervGatewaysMetodos gatewaysMetodos = new SupervGatewaysMetodos();
                    SupervGatewaysDominio gateway = new SupervGatewaysDominio();
                    gateway = gatewaysMetodos.ListarPorIDGateway(IDGateway);

                    // verifica se existe supervisao
                    if (gateway != null)
                    {
                        // verifica se existe
                        if (gateway.IDCliente > 0)
                        {
                            // leio remotas da gateways
                            GatewayRemotasMetodos remotasMetodos = new GatewayRemotasMetodos();
                            List<GatewayRemotasDominio> RemotasList = remotasMetodos.ListarTodasMedicoesIDGateway(gateway.IDGateway);

                            // percorro remotas
                            if (RemotasList != null)
                            {
                                foreach (GatewayRemotasDominio remota in RemotasList)
                                {
                                    // verifica se origem pelo log de eventos
                                    if (remota.OrigemStatus == 0)
                                    {
                                        // verifico ultimo evento na remota
                                        EV_Metodos eventoMetodos = new EV_Metodos();
                                        EV_Dominio evento = eventoMetodos.ListarMaisRecenteRemota(remota.IDCliente, remota.IDGateway, remota.Remota);

                                        // verifica se tem evento
                                        if (evento != null)
                                        {
                                            // atualiza status (0 - Erro Comunicacao ||| 1 = Comunicacao OK)
                                            remota.Status = (evento.Valor >= 65536) ? 1 : 0;
                                            remota.DataStatus = evento.DataHora;
                                            remota.DataStatusTexto = remota.DataStatus.ToString("g");

                                            // salva
                                            remotasMetodos.Alterar(remota);
                                        }
                                    }
                                }
                            }

                            // copia
                            gateway.RemotasList = RemotasList;

                            // data e hora
                            DateTime data_hora_hoje = DateTime.Now;
                            DateTime data_hora_atualizacao = gateway.DataHora;

                            // IP
                            int pos = gateway.StatusEq.IndexOf("IP=");

                            if (pos >= 0)
                            {
                                int pos2 = gateway.StatusEq.IndexOf(' ');

                                if (pos2 > 3)
                                {
                                    string str = gateway.StatusEq.Substring(pos + 3, gateway.StatusEq.IndexOf(' ') - 3);
                                    gateway.IP = str;
                                }
                                else
                                {
                                    gateway.IP = "-";
                                }
                            }
                            else
                            {
                                gateway.IP = "-";
                            }

                            // sinal
                            pos = gateway.StatusEq.IndexOf("SQ=");
                            double sinal = -1.0;
                            gateway.Sinal = 0;

                            if (pos >= 0)
                            {
                                string str = gateway.StatusEq.Substring(pos + 3);
                                sinal = double.Parse(str);

                                if (sinal >= 99.0)
                                {
                                    sinal = 0.0;
                                }
                                else
                                {
                                    sinal = (sinal / 31.0) * 100.0;
                                }
                            }

                            // copia sinal
                            if (sinal > 0.0)
                            {
                                // copia sinal
                                gateway.Sinal = (int)sinal;
                            }

                            // inicialmente normal
                            //gateway.Status = 0;     // normal
                            gateway.StatusTexto = "Gateway Atualização Normal";

                            // status da gateway
                            switch (gateway.IDTipoTempo)
                            {
                                case TIPO_TEMPO_GATEWAY.GatewayBloqueada:                   // 0 - gateway bloqueada
                                    //gateway.Status = 1;     // anormal
                                    gateway.StatusTexto = "Gateway Bloqueada";
                                    break;

                                case TIPO_TEMPO_GATEWAY.Ignorar_StartUpNaoRealizado:        // 1 - ignorar - start up nao realizado
                                    //gateway.Status = 1;     // anormal
                                    gateway.StatusTexto = "Start-up não realizado";
                                    break;

                                case TIPO_TEMPO_GATEWAY.Ignorar_PendenciaCliente:           // 2 - ignorar - pendencia do cliente
                                    //gateway.Status = 1;     // anormal
                                    gateway.StatusTexto = "Pendência do Cliente";
                                    break;

                                case TIPO_TEMPO_GATEWAY.SCDE:                               // 3 - ignorar - SCDE
                                    //gateway.Status = 1;     // anormal
                                    gateway.StatusTexto = "SCDE";
                                    break;

                                case TIPO_TEMPO_GATEWAY.Envio_15minutos:                    // 12 - a cada 15 minutos

                                    // verifica se atraso maior que 6 horas
                                    if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(6, 0, 0))
                                    {
                                        //gateway.Status = 1;     // anormal
                                        gateway.StatusTexto = "Gateway Atualização Atrasada";
                                    }

                                    break;

                                case TIPO_TEMPO_GATEWAY.Envio_hora:                         // 13 - a cada hora

                                    // verifica se atraso maior que 6 horas
                                    if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(6, 0, 0))
                                    {
                                        //gateway.Status = 1;     // anormal
                                        gateway.StatusTexto = "Gateway Atualização Atrasada";
                                    }

                                    break;

                                case TIPO_TEMPO_GATEWAY.Envio_diariamente:                  // 14 - diariamente

                                    // verifica se atraso maior que 2 dias
                                    if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(48, 0, 0))
                                    {
                                        //gateway.Status = 1;     // anormal
                                        gateway.StatusTexto = "Gateway Atualização Atrasada";
                                    }

                                    break;
                            }

                            // verifica os eventos caso a supervisao nao esta em atraso
                            if (gateway.Status == "0")
                            {
                                EV_Metodos evMetodos = new EV_Metodos();
                                EV_Dominio evento = evMetodos.ListarMaisRecente(IDCliente, IDGateway);

                                if (evento != null)
                                {
                                    // verifica se atraso maior que 2 dias
                                    if ((data_hora_hoje - evento.DataHora) > new TimeSpan(48, 0, 0))
                                    {
                                        //gateway.Status = 1;     // anormal
                                        gateway.StatusTexto = "Gateway Atualização Atrasada (Eventos)";
                                    }
                                }
                            }

                            // ICC
                            if (gateway.ICC.Length == 0)
                            {
                                gateway.ICC = "-";
                            }

                            // MOD
                            if (gateway.MOD.Length == 0)
                            {
                                gateway.MOD = "-";
                            }

                            // Operadora
                            if (gateway.OP.Length == 0)
                            {
                                gateway.OP = "-";
                            }

                        }
                    }
                    else
                    {
                        // gateway nao existe
                        gateway = new SupervGatewaysDominio();
                        gateway.IDCliente = 0;
                    }

                    // verifica se gateway NAO existe
                    if (gateway.IDCliente <= 0)
                    {
                        // data e hora
                        DateTime data_hora_atualizacao = new DateTime(2000, 1, 1, 0, 0, 0);

                        gateway.IDCliente = IDCliente;
                        gateway.IDGateway = IDGateway;
                        gateway.Nome = string.Format("ID Gateway {0}", IDGateway);
                        gateway.Fantasia = string.Format("ID Gateway {0}", IDGateway);
                        gateway.DataHora = data_hora_atualizacao;
                        gateway.DataEq = data_hora_atualizacao;
                        gateway.StatusEq = " ";
                        //gateway.Status = 1;     // anormal
                        gateway.StatusTexto = "Sem supervisão";
                    }

                    // coloca na lista
                    GatewaysListInfo.Add(IDGateway);
                    GatewaysSupervListInfo.Add(gateway);
                }
            }

            // copia lista
            ViewBag.SupervListInfo = GatewaysSupervListInfo;

            return View();
        }

        public ActionResult GatewayInfo(int IDGateway)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // leio supervisao gateway
            SupervGatewaysMetodos gatewaysMetodos = new SupervGatewaysMetodos();
            SupervGatewaysDominio gateway = new SupervGatewaysDominio();
            gateway = gatewaysMetodos.ListarPorIDGateway(IDGateway);

            // valores
            ViewBag.GatewayID = IDGateway;
            ViewBag.GatewayNome = gateway.Nome;
            ViewBag.GatewayModelo = String.Format("{0} ({1})", gateway.ModeloEq, gateway.VersaoEq);
            ViewBag.GatewayStatus = gateway.StatusEq;
            ViewBag.GatewayAtualizacao = String.Format("{0:G}", gateway.DataHora);
            ViewBag.GatewayDataHora = String.Format("{0:G}", gateway.DataEq);
            ViewBag.GatewayICC = gateway.ICC;
            ViewBag.GatewayOP = gateway.OP;
            ViewBag.GatewayIMEI = gateway.IMEI;
            ViewBag.GatewayMOD = gateway.MOD;
            ViewBag.GatewayFAB = gateway.FAB;

            // primeira e ultima data para analise (7 dias)
            DateTime UltDataHora = gateway.DataHora;
            DateTime PrimDataHora = UltDataHora.AddDays(-7);

            // leio lista de supervisao 
            List<SUPDominio> listaSUP = new List<SUPDominio>();
            listaSUP = gatewaysMetodos.ListarEntreDatas(gateway.IDCliente, IDGateway, PrimDataHora, UltDataHora);
            ViewBag.ListaSup = listaSUP;

            // grafico
            var Sinal = new int[100];
            var IP = new string[100];
            var DataHoraSinal = new string[100];
            int numRegistrosSinal = 0;

            if (listaSUP != null)
            {
                // numero de registros
                numRegistrosSinal = (listaSUP.Count > 100) ? 100 : listaSUP.Count;

                // valores
                for (int i = 0; i < numRegistrosSinal; i++)
                {
                    // copia valores invertendo ordem
                    DataHoraSinal[i] = listaSUP[i].DataHora.ToString("yyyy-MM-dd HH:mm:ss");
                    Sinal[i] = listaSUP[i].Sinal;
                    IP[i] = listaSUP[i].IP;
                }
            }

            ViewBag.DataHoraSinal = DataHoraSinal;
            ViewBag.Sinal = Sinal;
            ViewBag.IP = IP;
            ViewBag.numRegistrosSinal = numRegistrosSinal;


            // eventos de falha durante o periodo
            var Eventos = new string[100];
            var DataHoraEvento = new string[100];
            int numRegistrosEvento = 0;

            EV_Metodos eventoMetodos = new EV_Metodos();
            List<EV_Dominio> eventos = eventoMetodos.ListarFalhaEnvioDados(gateway.IDCliente, gateway.IDGateway, PrimDataHora, UltDataHora);

            // lista de eventos
            List<EventosDescricao> listaEventosDescricao = new List<EventosDescricao>();

            if (listaSUP != null)
            {
                // numero de registros
                numRegistrosEvento = (eventos.Count > 100) ? 100 : eventos.Count;

                // preenche solicitacao
                CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
                config_interface.sweb.id_cliente = IDCliente;
                config_interface.sweb.id_gateway = IDGateway;
            }

            ViewBag.DataHoraEvento = DataHoraEvento;
            ViewBag.Eventos = Eventos;
            ViewBag.numRegistrosEvento = numRegistrosEvento;
            ViewBag.listaEventosDescricao = listaEventosDescricao;

            return View();
        }

    }
}