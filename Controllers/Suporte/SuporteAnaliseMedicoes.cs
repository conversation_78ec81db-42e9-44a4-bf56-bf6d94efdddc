﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Web.Mvc;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SuporteController
    {
        public ActionResult AnaliseMedicoes()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // medições em atraso
            int MedicoesAtraso_Total = 0;
            int MedicoesAtraso_3dias = 0;
            int MedicoesAtraso_3_7dias = 0;
            int MedicoesAtraso_7_23dias = 0;
            int MedicoesAtraso_23dias = 0;
            int MedicoesAtraso_1ano = 0;
            int MedicoesAtraso_NuncaMediram = 0;

            // leio lista de supervisao medições
            MedicoesSupervMetodos medicoesMetodos = new MedicoesSupervMetodos();
            List<MedicoesSupervDominio> MedicoesAtrasoList = new List<MedicoesSupervDominio>();
            List<MedicoesSupervDominio> MedicoesAtraso = new List<MedicoesSupervDominio>();
            MedicoesAtraso = medicoesMetodos.ListarEmAtraso();

            // verifica se existe
            if (MedicoesAtraso != null)
            {
                // analiso os atrasos
                foreach (MedicoesSupervDominio medicao in MedicoesAtraso)
                {
                    //
                    // Atraso de Atualizacao
                    //

                    // total
                    MedicoesAtraso_Total++;

                    // verifica tipo de atraso
                    switch (medicao.TipoAtraso)
                    {
                        case 1: // atraso menor que 3 dias
                            MedicoesAtraso_3dias++;
                            break;

                        case 2: // atraso 3 a 7 dias
                            MedicoesAtraso_3_7dias++;
                            break;

                        case 3: // atraso 7 a 23 dias
                            MedicoesAtraso_7_23dias++;
                            break;

                        case 4: // atraso maior que 23 dias
                            MedicoesAtraso_23dias++;
                            break;

                        case 5: // atraso maior que 1 ano
                            MedicoesAtraso_1ano++;
                            break;

                        case 6: // nunca mediram
                            MedicoesAtraso_NuncaMediram++;
                            break;
                    }

                    // verifica se SmartGate E
                    if (medicao.IDTipoGateway == TIPO_GATEWAY.GATE_E)
                    {
                        // converte gateE
                        medicao.infoGeral = Converte_GateE(medicao);
                    }
                    else
                    {
                        medicao.infoGeral.status_CODI = "-";
                    }

                    // copia para a lista final
                    MedicoesAtrasoList.Add(medicao);
                }
            }

            // lista de medições em atraso
            ViewBag.MedicoesAtrasoList = MedicoesAtrasoList;

            // gateways em atraso
            ViewBag.MedicoesAtraso_Total = MedicoesAtraso_Total;
            ViewBag.MedicoesAtraso_3dias = MedicoesAtraso_3dias;
            ViewBag.MedicoesAtraso_3_7dias = MedicoesAtraso_3_7dias;
            ViewBag.MedicoesAtraso_7_23dias = MedicoesAtraso_7_23dias;
            ViewBag.MedicoesAtraso_23dias = MedicoesAtraso_23dias;
            ViewBag.MedicoesAtraso_1ano = MedicoesAtraso_1ano;
            ViewBag.MedicoesAtraso_NuncaMediram = MedicoesAtraso_NuncaMediram;

            return View(MedicoesAtrasoList);
        }

        // converte supervisão GateE
        public GateE_InfoGeral_Dominio Converte_GateE(MedicoesSupervDominio medicao)
        {
            // InfoGeral
            GateE_InfoGeral_Dominio infoGeral = new GateE_InfoGeral_Dominio();

            // converte em UInt32
            UInt32 eq32_cfg = Converte_Uint32(medicao.GateE_eq32_cfg);
            UInt32 rmIO_cfg = Converte_Uint32(medicao.GateE_rmIO_cfg);
            UInt32 rmLr_cfg = Converte_Uint32(medicao.GateE_rmLr_cfg);
            UInt32 mdEN_cfg = Converte_Uint32(medicao.GateE_mdEN_cfg);
            UInt32 mdUT_cfg = Converte_Uint32(medicao.GateE_mdUT_cfg);
            UInt32 mdAN_cfg = Converte_Uint32(medicao.GateE_mdAN_cfg);
            UInt32 mdCY_cfg = Converte_Uint32(medicao.GateE_mdCY_cfg);

            UInt32 eq32_nok = Converte_Uint32(medicao.GateE_eq32_nok);
            UInt32 rmIO_nok = Converte_Uint32(medicao.GateE_rmIO_nok);
            UInt32 rmLr_nok = Converte_Uint32(medicao.GateE_rmLr_nok);
            UInt32 mdEN_nok_pul = Converte_Uint32(medicao.GateE_mdEN_nok_pul);
            UInt32 mdUT_nok_pul = Converte_Uint32(medicao.GateE_mdUT_nok_pul);
            UInt32 mdAN_nok_sin = Converte_Uint32(medicao.GateE_mdAN_nok_sin);
            UInt32 mdCY_nok_pul = Converte_Uint32(medicao.GateE_mdCY_nok_pul);

            UInt32 mdEN_nok_rem = Converte_Uint32(medicao.GateE_mdEN_nok_rem);
            UInt32 mdUT_nok_rem = Converte_Uint32(medicao.GateE_mdUT_nok_rem);
            UInt32 mdAN_nok_rem = Converte_Uint32(medicao.GateE_mdAN_nok_rem);
            UInt32 mdCY_nok_rem = Converte_Uint32(medicao.GateE_mdCY_nok_rem);

            // converte em bool
            for (int i = 0; i < 32; i++)
            {
                infoGeral.eq32[i] = new GateE_InfoGeral_Status();
                infoGeral.rmIO[i] = new GateE_InfoGeral_Status();
                infoGeral.rmLr[i] = new GateE_InfoGeral_Status();
                infoGeral.mdEN[i] = new GateE_InfoGeral_Status();
                infoGeral.mdUT[i] = new GateE_InfoGeral_Status();
                infoGeral.mdAN[i] = new GateE_InfoGeral_Status();
                infoGeral.mdCY[i] = new GateE_InfoGeral_Status();

                infoGeral.eq32[i].cfg = Funcoes_Bit.IsBitSet_UInt32(eq32_cfg, i);
                infoGeral.rmIO[i].cfg = Funcoes_Bit.IsBitSet_UInt32(rmIO_cfg, i);
                infoGeral.rmLr[i].cfg = Funcoes_Bit.IsBitSet_UInt32(rmLr_cfg, i);
                infoGeral.mdEN[i].cfg = Funcoes_Bit.IsBitSet_UInt32(mdEN_cfg, i);
                infoGeral.mdUT[i].cfg = Funcoes_Bit.IsBitSet_UInt32(mdUT_cfg, i);
                infoGeral.mdAN[i].cfg = Funcoes_Bit.IsBitSet_UInt32(mdAN_cfg, i);
                infoGeral.mdCY[i].cfg = Funcoes_Bit.IsBitSet_UInt32(mdCY_cfg, i);

                infoGeral.eq32[i].nok_sin = Funcoes_Bit.IsBitSet_UInt32(eq32_nok, i);
                infoGeral.rmIO[i].nok_sin = Funcoes_Bit.IsBitSet_UInt32(rmIO_nok, i);
                infoGeral.rmLr[i].nok_sin = Funcoes_Bit.IsBitSet_UInt32(rmLr_nok, i);
                infoGeral.mdEN[i].nok_sin = Funcoes_Bit.IsBitSet_UInt32(mdEN_nok_pul, i);
                infoGeral.mdUT[i].nok_sin = Funcoes_Bit.IsBitSet_UInt32(mdUT_nok_pul, i);
                infoGeral.mdAN[i].nok_sin = Funcoes_Bit.IsBitSet_UInt32(mdAN_nok_sin, i);
                infoGeral.mdCY[i].nok_sin = Funcoes_Bit.IsBitSet_UInt32(mdCY_nok_pul, i);

                infoGeral.mdEN[i].nok_rem = Funcoes_Bit.IsBitSet_UInt32(mdEN_nok_rem, i);
                infoGeral.mdUT[i].nok_rem = Funcoes_Bit.IsBitSet_UInt32(mdUT_nok_rem, i);
                infoGeral.mdAN[i].nok_rem = Funcoes_Bit.IsBitSet_UInt32(mdAN_nok_rem, i);
                infoGeral.mdCY[i].nok_rem = Funcoes_Bit.IsBitSet_UInt32(mdCY_nok_rem, i);
            }


            // resets
            if (!int.TryParse(medicao.GateE_qtReset, out infoGeral.qtReset))
            {
                // erro
                infoGeral.qtReset = 0;
            }

            // interface IoT
            if (!int.TryParse(medicao.GateE_IF_IoT, out infoGeral.IF_IoT))
            {
                // erro
                infoGeral.IF_IoT = 0;
            }

            // GSM
            infoGeral.gsm_TEC = medicao.GSM_TEC;
            infoGeral.gsm_OP = medicao.OP;

            // sinal GSM
            if (!int.TryParse(medicao.GateE_SQ_gsm, out infoGeral.SQ_gsm))
            {
                // erro
                infoGeral.SQ_gsm = 0;
            }

            // sinal Wi-Fi
            if (!int.TryParse(medicao.GateE_SQ_wifi, out infoGeral.SQ_wifi))
            {
                // erro
                infoGeral.SQ_wifi = 0;
            }


            // sinal
            int sinal = 0;

            // verifica se Wi-Fi ou GSM
            if (infoGeral.IF_IoT == GateE_TIPO_INTERFACE_IOT.SIMCARD)
            {
                // GSM
                sinal = ((infoGeral.SQ_gsm * 100) / 31);
                infoGeral.status_GSM_WIFI = (infoGeral.eq32[(int)GateE_EQ32.if4GM1].nok_sin) ? string.Format("{0} (Sem Conexão)", infoGeral.gsm_OP) : string.Format("{0} (Sinal {1}%)", infoGeral.gsm_OP, sinal);
            }
            else
            {
                // Wi-Fi
                sinal = ((infoGeral.SQ_wifi * 100) / 31);
                infoGeral.status_GSM_WIFI = (infoGeral.eq32[(int)GateE_EQ32.cxWIFI].nok_sin) ? "Sem Conexão" : string.Format("Sinal {0}%", sinal);
            }


            // status da Gateway
            infoGeral.status_Gateway = MontaDescricao_StatusGateway(infoGeral);

            // interfaces
            infoGeral.status_Interfaces = Monta_StatusInterfaces(infoGeral);

            // remotas I/O
            infoGeral.status_RemotasIO = Monta_StatusRemotas_IO(infoGeral);

            // remotas Lora
            infoGeral.status_RemotasLora = Monta_StatusRemotas_Lora(infoGeral);

            //  medições de energia
            infoGeral.status_MedicoesEnergia = Monta_StatusMedicoes_Energia(infoGeral);

            //  medições de utilidades
            infoGeral.status_MedicoesUtilidades = Monta_StatusMedicoes_Utilidades(infoGeral);

            //  medições analógicas
            infoGeral.status_MedicoesAnalogicas = Monta_StatusMedicoes_Analogicas(infoGeral);

            //  medições ciclômetro
            infoGeral.status_MedicoesCiclometro = Monta_StatusMedicoes_Ciclometro(infoGeral);

            // CODI
            if (infoGeral.eq32[(int)GateE_EQ32.CODI].nok_sin)
            {
                infoGeral.status_CODI = "ERRO";
            }
            else
            {
                infoGeral.status_CODI = (infoGeral.codi_ext == 0) ? "Protocolo Normal" : "Protocolo Estendido";
            }

            // supervisão
            return (infoGeral);
        }

        // converte string em UIn32
        private UInt32 Converte_Uint32(string valor_string)
        {
            // verifica se string válida
            if (string.IsNullOrWhiteSpace(valor_string))
            {
                // erro
                return (0);
            }

            // converte
            if (UInt32.TryParse(valor_string, NumberStyles.HexNumber, CultureInfo.InvariantCulture, out uint resultado))
            {
                // valor
                return (resultado);
            }

            // erro
            return (0);
        }

        // monta status da gateway
        private List<string> MontaDescricao_StatusGateway(GateE_InfoGeral_Dominio infoGeral)
        {
            // nome das medições
            GateE_MedEner_Metodos medicaoMetodos = new GateE_MedEner_Metodos();
            List<GateE_MedEner_Dominio> medicoes = medicaoMetodos.ListarPorIDGateway(infoGeral.IDGateway, STATUS_CFG.ATUAL);

            // lista de status
            List<string> lista = new List<string>();

            // percorre os bits a procura do bit setado
            for (short _bit = 0; _bit < 32; _bit++)
            {
                // verifica o bit setado em "1"
                if (infoGeral.eq32[_bit].nok_sin)
                {
                    // descrição do status
                    string descricao = string.Empty;

                    // verifica item
                    switch ((GateE_EQ32)_bit)
                    {
                        case GateE_EQ32.if4GM1:
                            descricao = "Erro na conexão IoT - Interface Modem: Fase 1";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.ifBTOO:
                            descricao = "Erro na interface Bluetooth da Gateway";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.ifWIFI:
                            descricao = "Erro na conexão IoT - Interface WiFi: Fase 1";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.ifLORA:
                            descricao = "Erro na interface Lora";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.if485:
                            descricao = "Erro na interface RS 485";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.eq32_05:
                            descricao = "Erro: GT_NOK_05";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.eq32_06:
                            descricao = "Erro: GT_NOK_06";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.SIM:
                            descricao = "Erro na conexão IoT - SIM Card: Fase 2";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.rgOP:
                            descricao = "Erro na conexão IoT - Registro: Fase 3";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.cxWIFI:
                            descricao = "Erro na conexão IoT - Registro : Fase 3";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.skIOT:
                            descricao = "Erro na conexão IoT - Socket : Fase 4";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.VIN:
                            descricao = "Tensão fora da normalidade";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.VBAT:
                            descricao = "Tensão da Bateria fora da normalidade";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.TBAT:
                            descricao = "Temperatura da Bateria fora da normalidade";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.mdEN:

                            // percorre os bits do status da medicao de energia
                            for (short _b = 0; _b < 3; _b++)
                            {
                                // verifica o bit setado em "1"
                                if (infoGeral.mdEN[_b].nok_sin)
                                {
                                    // nome default
                                    string nome = string.Format("[{0}] Medição {0}", _b);

                                    // verifica se existe nome no banco de dados
                                    GateE_MedEner_Dominio medicao = medicoes.Find(m => m.NumMedicaoGateway == _b);
                                    if (medicao != null)
                                    {
                                        nome = string.Format("[{0}] {1}", _b, medicao.Descricao);
                                    }

                                    // monta a descricao com a medicao 
                                    descricao = string.Format("Medição de Energia em ZERO: {0}", nome);
                                    lista.Add(descricao);
                                }
                            }
                            break;

                        case GateE_EQ32.mdUT:

                            // percorre os bits do status da medicao de utilidades
                            for (short _b = 0; _b < 3; _b++)
                            {
                                // verifica o bit setado em "1"
                                if (infoGeral.mdUT[_b].nok_sin)
                                {
                                    // monta a descricao com a medicao 
                                    descricao = string.Format("Medição de Utilidades em ZERO: [{0}] Medição {0}", _b);
                                    lista.Add(descricao);
                                }
                            }
                            break;

                        case GateE_EQ32.mdAN:

                            // percorre os bits do status da medicao de analogicas
                            for (short _b = 0; _b < 3; _b++)
                            {
                                // verifica o bit setado em "1"
                                if (infoGeral.mdAN[_b].nok_sin)
                                {
                                    // monta a descricao com a medicao 
                                    descricao = string.Format("Medição Analógica em ZERO: [{0}] Medição {0}", _b);
                                    lista.Add(descricao);
                                }
                            }
                            break;

                        case GateE_EQ32.mdCY:

                            // percorre os bits do status da medicao de ciclometro
                            for (short _b = 0; _b < 3; _b++)
                            {
                                // verifica o bit setado em "1"
                                if (infoGeral.mdCY[_b].nok_sin)
                                {
                                    // monta a descricao com a medicao 
                                    descricao = string.Format("Medição Ciclômetro em ZERO: [{0}] Medição {0}", _b);
                                    lista.Add(descricao);
                                }
                            }
                            break;

                        case GateE_EQ32.rmIO:

                            // percorre os bits do status das remotas
                            for (short _b = 0; _b < 32; _b++)
                            {
                                // verifica o bit setado em "1"
                                if (infoGeral.rmIO[_b].nok_sin)
                                {
                                    // monta a descricao com a remota com erro
                                    descricao = string.Format("Erro na remota I/O: Remota {0}", _b);
                                    lista.Add(descricao);
                                }
                            }
                            break;

                        case GateE_EQ32.rmLORA:

                            // percorre os bits do status das remotas
                            for (short _b = 0; _b < 32; _b++)
                            {
                                // verifica o bit setado em "1"
                                if (infoGeral.rmLr[_b].nok_sin)
                                {
                                    // monta a descricao com a remota com erro
                                    descricao = string.Format("Erro na remota LORA: Remota {0}", _b);
                                    lista.Add(descricao);
                                }
                            }
                            break;

                        case GateE_EQ32.CODI:
                            descricao = "Erro no Medidor de Faturamento";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.eq32_21:
                            descricao = "Erro: GT_NOK_21";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.eq32_22:
                            descricao = "Erro: GT_NOK_22";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.eq32_23:
                            descricao = "Erro: GT_NOK_23";
                            lista.Add(descricao);
                            break;

                        default:
                            descricao = string.Format("Erro Desconhecido [{0}]", _bit);
                            lista.Add(descricao);
                            break;
                    }
                }
            }

            // ordena
            lista.Sort();

            // retorna lista
            return lista;
        }

        // monta status das interfaces
        private List<int> Monta_StatusInterfaces(GateE_InfoGeral_Dominio infoGeral)
        {
            // lista de status
            List<int> lista = new List<int>();

            // bluetooth
            int status_ifBTOO = 0;
            if (infoGeral.eq32[(int)GateE_EQ32.ifBTOO].cfg)
            {
                status_ifBTOO = (infoGeral.eq32[(int)GateE_EQ32.ifBTOO].nok_sin) ? 2 : 1;
            }
            lista.Add(status_ifBTOO);

            // GSM
            int status_if4GM1 = 0;
            if (infoGeral.eq32[(int)GateE_EQ32.if4GM1].cfg)
            {
                status_if4GM1 = (infoGeral.eq32[(int)GateE_EQ32.if4GM1].nok_sin) ? 2 : 1;
            }
            lista.Add(status_if4GM1);

            // Wi-Fi
            int status_ifWIFI = 0;
            if (infoGeral.eq32[(int)GateE_EQ32.ifWIFI].cfg)
            {
                status_ifWIFI = (infoGeral.eq32[(int)GateE_EQ32.ifWIFI].nok_sin) ? 2 : 1;
            }
            lista.Add(status_ifWIFI);

            // LORA
            int status_ifLORA = 0;
            if (infoGeral.eq32[(int)GateE_EQ32.ifLORA].cfg)
            {
                status_ifLORA = (infoGeral.eq32[(int)GateE_EQ32.ifLORA].nok_sin) ? 2 : 1;
            }
            lista.Add(status_ifLORA);

            // RS485
            int status_if485 = 0;
            if (infoGeral.eq32[(int)GateE_EQ32.if485].cfg)
            {
                status_if485 = (infoGeral.eq32[(int)GateE_EQ32.if485].nok_sin) ? 2 : 1;
            }
            lista.Add(status_if485);

            // retorna lista
            return lista;
        }

        // monta status das remotas I/O
        private List<int> Monta_StatusRemotas_IO(GateE_InfoGeral_Dominio infoGeral)
        {
            // lista de status
            List<int> lista = new List<int>();

            // a principio nenhuma remota configurada
            bool remota_configurada = false;

            // remotas I/O
            for (int i = 0; i < 32; i++)
            {
                // inicialmente remota não configurada
                int status = GateE_STATUS_REMOTA_SINAL.NAO_CONFIGURADO;

                // verifica se remota configurada
                if (infoGeral.rmIO[i].cfg)
                {
                    // status
                    status = (infoGeral.rmIO[i].nok_rem) ? GateE_STATUS_REMOTA_SINAL.FALHA_REMOTA : GateE_STATUS_REMOTA_SINAL.OK;

                    // remota configurada
                    remota_configurada = true;
                }

                lista.Add(status);
            }

            // verifica se não tem remota configurada
            if (!remota_configurada)
            {
                // apago lista
                lista.Clear();
            }

            // retorna lista
            return lista;
        }

        // monta status das remotas Lora
        private List<int> Monta_StatusRemotas_Lora(GateE_InfoGeral_Dominio infoGeral)
        {
            // lista de status
            List<int> lista = new List<int>();

            // a principio nenhuma remota configurada
            bool remota_configurada = false;

            // remotas Lora
            for (int i = 0; i < 32; i++)
            {
                // inicialmente remota não configurada
                int status = GateE_STATUS_REMOTA_SINAL.NAO_CONFIGURADO;

                // verifica se remota configurada
                if (infoGeral.rmLr[i].cfg)
                {
                    // status
                    status = (infoGeral.rmLr[i].nok_rem) ? GateE_STATUS_REMOTA_SINAL.FALHA_REMOTA : GateE_STATUS_REMOTA_SINAL.OK;

                    // remota configurada
                    remota_configurada = true;
                }

                lista.Add(status);
            }

            // verifica se não tem remota configurada
            if (!remota_configurada)
            {
                // apago lista
                lista.Clear();
            }

            // retorna lista
            return lista;
        }

        // monta status das medições de energia
        private List<GateE_InfoGeral_Medicao> Monta_StatusMedicoes_Energia(GateE_InfoGeral_Dominio infoGeral)
        {
            // lista de status
            List<GateE_InfoGeral_Medicao> lista = new List<GateE_InfoGeral_Medicao>();

            // a principio nenhuma medição configurada
            bool medicao_configurada = false;

            // nome das medições
            GateE_MedEner_Metodos medicaoMetodos = new GateE_MedEner_Metodos();
            List<GateE_MedEner_Dominio> medicoes = medicaoMetodos.ListarPorIDGateway(infoGeral.IDGateway, STATUS_CFG.ATUAL);

            // medições
            for (int i = 0; i < 3; i++)
            {
                // inicialmente medição não configurada
                GateE_InfoGeral_Medicao status = new GateE_InfoGeral_Medicao();
                status.status = GateE_STATUS_REMOTA_SINAL.NAO_CONFIGURADO;
                status.nome = string.Format("[{0}] Medição {0}", i);

                // verifica se existe nome no banco de dados
                GateE_MedEner_Dominio medicao = medicoes.Find(m => m.NumMedicaoGateway == i);
                if (medicao != null)
                {
                    status.nome = string.Format("[{0}] {1}", i, medicao.Descricao);
                }

                // verifica se medição configurada
                if (infoGeral.mdEN[i].cfg)
                {
                    // verifica se remota está ok
                    if (infoGeral.mdEN[i].nok_rem)
                    {
                        // erro de remota (CODI ou remota)
                        status.status = GateE_STATUS_REMOTA_SINAL.FALHA_REMOTA;
                    }
                    else
                    {
                        // verifica se sinal está ok
                        if (infoGeral.mdEN[i].nok_sin)
                        {
                            // erro de sinal
                            status.status = GateE_STATUS_REMOTA_SINAL.FALHA_SINAL;
                        }
                        else
                        {
                            // ok
                            status.status = GateE_STATUS_REMOTA_SINAL.OK;
                        }
                    }

                    // medição configurada
                    medicao_configurada = true;
                }
                else
                {
                    // medição não configurada
                    status.status = GateE_STATUS_REMOTA_SINAL.NAO_CONFIGURADO;
                }

                lista.Add(status);
            }

            // verifica se não tem medição configurada
            if (!medicao_configurada)
            {
                // apago lista
                lista.Clear();
            }

            // retorna lista
            return lista;
        }

        // monta status das medições de utilidades
        private List<GateE_InfoGeral_Medicao> Monta_StatusMedicoes_Utilidades(GateE_InfoGeral_Dominio infoGeral)
        {
            // lista de status
            List<GateE_InfoGeral_Medicao> lista = new List<GateE_InfoGeral_Medicao>();

            // a principio nenhuma medição configurada
            bool medicao_configurada = false;

            // medições
            for (int i = 0; i < 3; i++)
            {
                // inicialmente medição não configurada
                GateE_InfoGeral_Medicao status = new GateE_InfoGeral_Medicao();
                status.status = GateE_STATUS_REMOTA_SINAL.NAO_CONFIGURADO;
                status.nome = string.Format("[{0}] Medição {0}", i);

                // verifica se medição configurada
                if (infoGeral.mdUT[i].cfg)
                {
                    // verifica se remota está ok
                    if (infoGeral.mdUT[i].nok_rem)
                    {
                        // erro de remota (CODI ou remota)
                        status.status = GateE_STATUS_REMOTA_SINAL.FALHA_REMOTA;
                    }
                    else
                    {
                        // verifica se sinal está ok
                        if (infoGeral.mdUT[i].nok_sin)
                        {
                            // erro de sinal
                            status.status = GateE_STATUS_REMOTA_SINAL.FALHA_SINAL;
                        }
                        else
                        {
                            // ok
                            status.status = GateE_STATUS_REMOTA_SINAL.OK;
                        }
                    }

                    // medição configurada
                    medicao_configurada = true;
                }
                else
                {
                    // medição não configurada
                    status.status = GateE_STATUS_REMOTA_SINAL.NAO_CONFIGURADO;
                }

                lista.Add(status);
            }

            // verifica se não tem medição configurada
            if (!medicao_configurada)
            {
                // apago lista
                lista.Clear();
            }

            // retorna lista
            return lista;
        }

        // monta status das medições analógicas
        private List<GateE_InfoGeral_Medicao> Monta_StatusMedicoes_Analogicas(GateE_InfoGeral_Dominio infoGeral)
        {
            // lista de status
            List<GateE_InfoGeral_Medicao> lista = new List<GateE_InfoGeral_Medicao>();

            // a principio nenhuma medição configurada
            bool medicao_configurada = false;

            // medições
            for (int i = 0; i < 3; i++)
            {
                // inicialmente medição não configurada
                GateE_InfoGeral_Medicao status = new GateE_InfoGeral_Medicao();
                status.status = GateE_STATUS_REMOTA_SINAL.NAO_CONFIGURADO;
                status.nome = string.Format("[{0}] Medição {0}", i);

                // verifica se medição configurada
                if (infoGeral.mdAN[i].cfg)
                {
                    // verifica se remota está ok
                    if (infoGeral.mdAN[i].nok_rem)
                    {
                        // erro de remota (CODI ou remota)
                        status.status = GateE_STATUS_REMOTA_SINAL.FALHA_REMOTA;
                    }
                    else
                    {
                        // verifica se sinal está ok
                        if (infoGeral.mdAN[i].nok_sin)
                        {
                            // erro de sinal
                            status.status = GateE_STATUS_REMOTA_SINAL.FALHA_SINAL;
                        }
                        else
                        {
                            // ok
                            status.status = GateE_STATUS_REMOTA_SINAL.OK;
                        }
                    }

                    // medição configurada
                    medicao_configurada = true;
                }
                else
                {
                    // medição não configurada
                    status.status = GateE_STATUS_REMOTA_SINAL.NAO_CONFIGURADO;
                }

                lista.Add(status);
            }

            // verifica se não tem medição configurada
            if (!medicao_configurada)
            {
                // apago lista
                lista.Clear();
            }

            // retorna lista
            return lista;
        }

        // monta status das medições ciclômetro
        private List<GateE_InfoGeral_Medicao> Monta_StatusMedicoes_Ciclometro(GateE_InfoGeral_Dominio infoGeral)
        {
            // lista de status
            List<GateE_InfoGeral_Medicao> lista = new List<GateE_InfoGeral_Medicao>();

            // a principio nenhuma medição configurada
            bool medicao_configurada = false;

            // medições
            for (int i = 0; i < 3; i++)
            {
                // inicialmente medição não configurada
                GateE_InfoGeral_Medicao status = new GateE_InfoGeral_Medicao();
                status.status = GateE_STATUS_REMOTA_SINAL.NAO_CONFIGURADO;
                status.nome = string.Format("[{0}] Medição {0}", i);

                // verifica se medição configurada
                if (infoGeral.mdCY[i].cfg)
                {
                    // verifica se remota está ok
                    if (infoGeral.mdCY[i].nok_rem)
                    {
                        // erro de remota (CODI ou remota)
                        status.status = GateE_STATUS_REMOTA_SINAL.FALHA_REMOTA;
                    }
                    else
                    {
                        // verifica se sinal está ok
                        if (infoGeral.mdCY[i].nok_sin)
                        {
                            // erro de sinal
                            status.status = GateE_STATUS_REMOTA_SINAL.FALHA_SINAL;
                        }
                        else
                        {
                            // ok
                            status.status = GateE_STATUS_REMOTA_SINAL.OK;
                        }
                    }

                    // medição configurada
                    medicao_configurada = true;
                }
                else
                {
                    // medição não configurada
                    status.status = GateE_STATUS_REMOTA_SINAL.NAO_CONFIGURADO;
                }

                lista.Add(status);
            }

            // verifica se não tem medição configurada
            if (!medicao_configurada)
            {
                // apago lista
                lista.Clear();
            }

            // retorna lista
            return lista;
        }
    }
}