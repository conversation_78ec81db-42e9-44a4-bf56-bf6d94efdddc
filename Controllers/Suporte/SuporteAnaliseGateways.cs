﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SuporteController
    {
        public ActionResult AnaliseGateways()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // caminho Recebidos
            string CaminhoRecebidos = ConfigurationManager.AppSettings["Recebidos"];

            // numero de arquivos em Recebidos
            string[] list = Directory.GetFiles(CaminhoRecebidos, "*.*");
            ViewBag.ArquivosRecebidos = list.Length;

            // gateway em atraso
            int GatewaysAtraso_Total = 0;
            int GatewaysAtraso_3dias = 0;
            int GatewaysAtraso_3_7dias = 0;
            int GatewaysAtraso_7_23dias = 0;
            int GatewaysAtraso_23dias = 0;
            int GatewaysRelogioErrado = 0;
            int GatewaysFaltaArquivo = 0;

            // leio lista de gestores
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            List<UsuarioDominio> gestores = usuarioMetodos.ListarTodosConsultores();

            // leio lista de supervisao gateways
            SupervGatewaysMetodos gatewaysMetodos = new SupervGatewaysMetodos();
            List<SupervGatewaysDominio> GatewaysAtrasoList = new List<SupervGatewaysDominio>();
            List<SupervGatewaysDominio> GatewaysSupervListTodos = new List<SupervGatewaysDominio>();
            GatewaysSupervListTodos = gatewaysMetodos.ListarEmAtraso();

            // verifica se existe
            if (GatewaysSupervListTodos != null)
            {
                // analiso os atrasos
                foreach (SupervGatewaysDominio gateway in GatewaysSupervListTodos)
                {
                    // separa o status
                    string[] separa_Status = gateway.Status.Split('/');

                    // a principio sem falha
                    int TipoAtrasoAtualizacao = 0;
                    int TipoAtrasoRelogio = 0;

                    // verifica se o status eh nulo ou vazio e se retornou indice de duas strings 
                    if (!String.IsNullOrEmpty(gateway.Status) && separa_Status.Length == 2)
                    {
                        // separa o IDHist da atualizacao do Tipo de atraso
                        string[] separa_TipoAtrasoAtualizacao = separa_Status[0].Split(' ');

                        // verifica se retornou indice de duas strings
                        if (separa_TipoAtrasoAtualizacao.Length == 2)
                        {
                            // verifica se o tipo de atraso nao eh nulo ou vazio
                            if (!String.IsNullOrEmpty(separa_TipoAtrasoAtualizacao[1]))
                            {
                                // copio o tipo de atraso
                                TipoAtrasoAtualizacao = int.Parse(separa_TipoAtrasoAtualizacao[1]);
                            }
                        }

                        // separa o IDHist do relogio do Tipo de atraso
                        string[] separa_TipoAtrasoRelogio = separa_Status[1].Split(' ');

                        // verifica se retornou indice de duas strings
                        if (separa_TipoAtrasoRelogio.Length == 2)
                        {
                            // verifica se o tipo de atraso nao eh nulo ou vazio
                            if (!String.IsNullOrEmpty(separa_TipoAtrasoRelogio[1]))
                            {
                                // copio o tipo de atraso
                                TipoAtrasoRelogio = int.Parse(separa_TipoAtrasoRelogio[1]);
                            }
                        }
                    }

                    //
                    // Atraso de Atualizacao
                    //

                    SupervGatewaysDominio gateway_AtrasoAtualizacao = new SupervGatewaysDominio(gateway);

                    // nome do gestor
                    UsuarioDominio gestor = gestores.Find(x => x.IDUsuario == gateway.IDConsultor);

                    if (gestor != null)
                    {
                        gateway_AtrasoAtualizacao.NomeConsultor = gestor.NomeUsuario;
                    }
                    else
                    {
                        gateway_AtrasoAtualizacao.NomeConsultor = "---";
                    }

                    // copia
                    gateway_AtrasoAtualizacao.TipoAtraso = TipoAtrasoAtualizacao;

                    // verifica tipo de atraso
                    switch (gateway_AtrasoAtualizacao.TipoAtraso)
                    {
                        case 1: // atraso menor que 3 dias
                            GatewaysAtraso_3dias++;

                            // total
                            GatewaysAtraso_Total++;

                            break;

                        case 2: // atraso 3 a 7 dias
                            GatewaysAtraso_3_7dias++;

                            // total
                            GatewaysAtraso_Total++;

                            break;

                        case 3: // atraso 7 a 23 dias
                            GatewaysAtraso_7_23dias++;

                            // total
                            GatewaysAtraso_Total++;

                            break;

                        case 4: // atraso maior que 23 dias
                            GatewaysAtraso_23dias++;

                            // total
                            GatewaysAtraso_Total++;

                            break;

                        case 6: // faltam arquivos (nao recebeu arquivo de eventos)
                            GatewaysFaltaArquivo++;

                            break;
                    }

                    // inclui atraso
                    GatewaysAtrasoList.Add(gateway_AtrasoAtualizacao);

                    //
                    // Atraso de Relogio
                    //

                    SupervGatewaysDominio gateway_AtrasoRelogio = new SupervGatewaysDominio(gateway);

                    // nome do gestor
                    gestor = gestores.Find(x => x.IDUsuario == gateway.IDConsultor);

                    if (gestor != null)
                    {
                        gateway_AtrasoRelogio.NomeConsultor = gestor.NomeUsuario;
                    }
                    else
                    {
                        gateway_AtrasoRelogio.NomeConsultor = "---";
                    }

                    // copia
                    gateway_AtrasoRelogio.TipoAtraso = TipoAtrasoRelogio;

                    if (gateway_AtrasoRelogio.TipoAtraso == 5)
                    {
                        // inclui gateway
                        GatewaysAtrasoList.Add(gateway_AtrasoRelogio);

                        // relogio da gateway em atraso
                        GatewaysRelogioErrado++;
                    }
                }
            }

            // lista degateways em atraso
            ViewBag.GatewaysAtrasoList = GatewaysAtrasoList;

            // gateways em atraso
            ViewBag.GatewaysAtraso = GatewaysAtraso_Total;
            ViewBag.GatewaysAtraso_3dias = GatewaysAtraso_3dias;
            ViewBag.GatewaysAtraso_3_7dias = GatewaysAtraso_3_7dias;
            ViewBag.GatewaysAtraso_7_23dias = GatewaysAtraso_7_23dias;
            ViewBag.GatewaysAtraso_23dias = GatewaysAtraso_23dias;

            ViewBag.GatewaysRelogioErrado = GatewaysRelogioErrado;
            ViewBag.GatewaysFaltaArquivo = GatewaysFaltaArquivo;


            // observacao tags
            List<ObservacaoTagsDominio> tags = new List<ObservacaoTagsDominio>();
            ViewBag.tags = tags;

            // observacao
            ObservacaoGatewayDominio observacao = new ObservacaoGatewayDominio();
            int IDGatewaySelecionado = 0;

            ViewBag.observacao = observacao;
            ViewBag.IDGatewaySelecionado = IDGatewaySelecionado;

            // observacoes
            List<ObservacaoGatewayDominio> observacoes = new List<ObservacaoGatewayDominio>();
            ViewBag.Observacoes = observacoes;

            return View(GatewaysAtrasoList);
        }

        public ActionResult AnaliseGatewaysHist(int IDGateway)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // listo historico de falhas
            AnaliseGatewaysHistMetodos analiseGatewaysMetodos = new AnaliseGatewaysHistMetodos();
            List<AnaliseGatewaysHistDominio> analiseGatewaysHist = analiseGatewaysMetodos.ListarPorIDGateway(IDGateway);

            // total de falhas dos ultimos 3 meses 
            TimeSpan tresMeses = new TimeSpan(90, 0, 0, 0);
            int falhas_Total = analiseGatewaysHist.Count(x => (DateTime.Now - x.DataFim <= tresMeses));

            // pego dados gateway
            SupervGatewaysMetodos supervisaoMetodos = new SupervGatewaysMetodos();
            SupervGatewaysDominio supervisaoGateways = supervisaoMetodos.ListarPorIDGateway(IDGateway);

            ViewBag.IDGateway = IDGateway;
            ViewBag.NomeGateway = supervisaoGateways.Nome;
            ViewBag.FalhasTotal = falhas_Total;

            return View(analiseGatewaysHist);
        }
    }
}