﻿using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SuporteController
    {
        public ActionResult BuscaGatewayRecebidos()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // informações
            int IDGateway = ViewBag._IDGateway;

            string NomeGateway = "";
            string NomeCliente = "";

            string DataHoraPrimeiroArquivo = "";

            int NumArquivosRecebidos = 0;
            int NumArquivosGateway = 0;

            if (IDGateway > 0)
            {
                // busca nome da gateway e do cliente
                GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
                GatewaysDominio gateway = gatewayMetodos.ListarPorId(IDGateway);

                if (gateway != null)
                {
                    NomeGateway = string.Format("[{0:000000}] {1}", gateway.IDGateway, gateway.Nome);

                    // busca cliente
                    ClientesMetodos clienteMetodos = new ClientesMetodos();
                    ClientesDominio cliente = clienteMetodos.ListarPorId(gateway.IDGateway);

                    if (cliente != null)
                    {
                        NomeCliente = string.Format("[{0:000000}] {1}", cliente.IDCliente, cliente.Nome);
                    }
                }
            }
            else
            {
                IDGateway = 0;
            }

            // caminho Recebidos
            string CaminhoRecebidos = ConfigurationManager.AppSettings["Recebidos"];

            // numero de arquivos em Recebidos
            string[] nomeArquivos = Directory.GetFiles(CaminhoRecebidos, "*.*");
            NumArquivosRecebidos = nomeArquivos.Length;

            // informações
            ViewBag.IDGateway_Search = IDGateway;
            ViewBag.NomeGateway_Search = NomeGateway;
            ViewBag.NomeCliente_Search = NomeCliente;

            // arquivos recebidos
            List<FileInfo> arquivosGateway = new List<FileInfo>();
            ViewBag.ArquivosGateway = arquivosGateway;

            ViewBag.DataHoraPrimeiroArquivo = DataHoraPrimeiroArquivo;

            ViewBag.NumArquivosRecebidos = NumArquivosRecebidos;
            ViewBag.NumArquivosGateway = NumArquivosGateway;

            return View();
        }

        // GET: Resultado busca
        public PartialViewResult _BuscaGatewayRecebidos_Resultado(int IDGateway)
        {            
            // lista de arquivos
            List<FileInfo> arquivosGateway = new List<FileInfo>();

            // nome da gateway e cliente
            string NomeGateway = "";
            string NomeCliente = "";

            string DataHoraPrimeiroArquivo = "";

            int NumArquivosRecebidos = 0;
            int NumArquivosGateway = 0;

            // verifica IDGateway
            if (IDGateway > 0)
            {
                // busca nome da gateway e do cliente
                GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
                GatewaysDominio gateway = gatewayMetodos.ListarPorId(IDGateway);

                if (gateway != null)
                {
                    NomeGateway = string.Format("[{0:000000}] {1}", gateway.IDGateway, gateway.Nome);

                    // busca cliente
                    ClientesMetodos clienteMetodos = new ClientesMetodos();
                    ClientesDominio cliente = clienteMetodos.ListarPorId(gateway.IDCliente);

                    if (cliente != null)
                    {
                        NomeCliente = string.Format("[{0:000000}] {1}", cliente.IDCliente, cliente.Nome);
                    }
                }
            }
            else
            {
                IDGateway = 0;
            }

            // verifica gateway
            if (IDGateway > 0)
            {
                //
                // arquivos em Recebidos
                //

                // deplementado pois esta lento
                /*
                DirectoryInfo info = new DirectoryInfo(@"C:\GESTAL\BatchProcessing\Files\GESTAL\Files\Recebidos");
                FileInfo[] files = info.GetFiles().OrderBy(f => f.LastWriteTime.Year <= 1601 ? f.CreationTime : f.LastWriteTime).ToArray();

                // número de arquivos recebidos
                NumArquivosRecebidos = files.Length;

                // lista arquivos em Recebidos
                foreach (FileInfo file in files)
                {
                    // verifica se tem bytes
                    if (file.Length > 0)
                    {
                        // primeiro arquivo na fila que tem dados
                        DataHoraPrimeiroArquivo = string.Format("{0:g}", file.LastWriteTime);
                        break;
                    }
                }
                */

                // caminho Recebidos
                string CaminhoRecebidos = ConfigurationManager.AppSettings["Recebidos"];

                // arquivos em Recebidos
                string[] nomeArquivos = Directory.GetFiles(CaminhoRecebidos, "*.*");
                NumArquivosRecebidos = nomeArquivos.Length;

                //
                // arquivos em Recebidos da Gateway
                //

                // máscara de busca
                string buscaGateway = string.Format("*_{0:000000}_*", IDGateway);

                // arquivos em Recebidos da Gateway
                nomeArquivos = Directory.GetFiles(CaminhoRecebidos, buscaGateway);
                NumArquivosGateway = nomeArquivos.Length;

                // lista arquivos da Gateway
                foreach (string nomeArquivo in nomeArquivos)
                {
                    // informações do arquivo
                    FileInfo arquivo = new FileInfo(nomeArquivo);

                    // insere na lista
                    arquivosGateway.Add(arquivo);
                }
            }

            // informações
            ViewBag.IDGateway_Search = IDGateway;
            ViewBag.NomeGateway_Search = NomeGateway;
            ViewBag.NomeCliente_Search = NomeCliente;

            // arquivos recebidos
            ViewBag.ArquivosGateway = arquivosGateway;

            ViewBag.DataHoraPrimeiroArquivo = DataHoraPrimeiroArquivo;

            ViewBag.NumArquivosRecebidos = NumArquivosRecebidos;
            ViewBag.NumArquivosGateway = NumArquivosGateway;

            return PartialView();
        }
    }
}
