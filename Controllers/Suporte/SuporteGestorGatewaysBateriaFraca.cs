﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SuporteController
    {
        public ActionResult GestorGatewaysBateriaFraca()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;
            int IDConsultor = ViewBag._IDConsultor;

            // leio lista de gestores
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            List<UsuarioDominio> gestores = usuarioMetodos.ListarTodosConsultores();

            // leio lista de supervisao gateways
            SupervGatewaysMetodos gatewaysMetodos = new SupervGatewaysMetodos();
            List<SupervGatewaysDominio> GatewaysBateriaFracaList = new List<SupervGatewaysDominio>();
            List<SupervGatewaysDominio> GatewaysSupervListTodos = new List<SupervGatewaysDominio>();
            GatewaysSupervListTodos = gatewaysMetodos.ListarBateriaFraca(IDConsultor);

            // verifica se existe
            if (GatewaysSupervListTodos != null)
            {
                // analiso os atrasos
                foreach (SupervGatewaysDominio gateway in GatewaysSupervListTodos)
                {
                    //
                    // Bateria Fraca
                    //

                    SupervGatewaysDominio gateway_BateriaFraca = new SupervGatewaysDominio(gateway);

                    // nome do gestor
                    UsuarioDominio gestor = gestores.Find(x => x.IDUsuario == gateway.IDConsultor);

                    if (gestor != null)
                    {
                        gateway_BateriaFraca.NomeConsultor = gestor.NomeUsuario;
                    }
                    else
                    {
                        gateway_BateriaFraca.NomeConsultor = "---";
                    }


                    // campos BAT
                    string Rt = "0";
                    string Ty = "0";
                    string Di = "0";
                    string St = "0";
                    string In = "01/01/2000";
                    string Fi = "01/01/2000";

                    // le subcampos do BAT
                    string[] subcampos_BAT = gateway.BAT.Split(' ');

                    for (int i = 0; i < subcampos_BAT.Length; i++)
                    {
                        // le identificadores dos subcampos
                        string[] identificadores_BAT = subcampos_BAT[i].Split('=');

                        if (identificadores_BAT.Length >= 2)
                        {
                            switch (identificadores_BAT[0])
                            {
                                case "Rt":    // BAT - subcampo Rt (número de resets)
                                    Rt = identificadores_BAT[1];
                                    break;

                                case "Ty":    // BAT - subcampo Ty (tipo da bateria)
                                    Ty = identificadores_BAT[1];
                                    break;

                                case "Di":    // BAT - subcampo Di (dias em bateria)
                                    Di = identificadores_BAT[1];
                                    break;

                                case "St":    // BAT - subcampo St (status - 0:bateria OK - 1:bateria fraca)
                                    St = identificadores_BAT[1];
                                    break;

                                case "In":    // BAT - subcampo In (data do coldboot)
                                    In = identificadores_BAT[1];
                                    break;

                                case "Fi":    // BAT - subcampo Fi (data de início de bateria no status fraco)
                                    Fi = identificadores_BAT[1];
                                    break;
                            }
                        }
                    }

                    // Número de resets
                    gateway_BateriaFraca.NumResets = Convert.ToInt32(Rt);

                    // Dias em bateria
                    gateway_BateriaFraca.DiasEmBateria = Convert.ToInt32(Di);

                    // data inicio bateria ok
                    gateway_BateriaFraca.InicioBateriaOK = DateTime.ParseExact(In, "dd/MM/yyyy", CultureInfo.CreateSpecificCulture("pt-BR"));

                    // data inicio bateria fraca
                    gateway_BateriaFraca.InicioBateriaFraca = DateTime.ParseExact(Fi, "dd/MM/yyyy", CultureInfo.CreateSpecificCulture("pt-BR"));

                    // inclui 
                    GatewaysBateriaFracaList.Add(gateway_BateriaFraca);
                }
            }

            // lista de gateways com bateria fraca
            ViewBag.GatewaysBateriaFracaList = GatewaysBateriaFracaList;


            // observacao tags
            List<ObservacaoTagsDominio> tags = new List<ObservacaoTagsDominio>();
            ViewBag.tags = tags;

            // observacao
            ObservacaoGatewayDominio observacao = new ObservacaoGatewayDominio();
            int IDGatewaySelecionado = 0;

            ViewBag.observacao = observacao;
            ViewBag.IDGatewaySelecionado = IDGatewaySelecionado;

            // observacoes
            List<ObservacaoGatewayDominio> observacoes = new List<ObservacaoGatewayDominio>();
            ViewBag.Observacoes = observacoes;

            return View(GatewaysBateriaFracaList);
        }
    }
}