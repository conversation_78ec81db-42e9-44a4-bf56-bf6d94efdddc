﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SuporteController
    {
        // GET: Configuração Firmwares
        public ActionResult Firmwares()
        {
            // tela de ajuda - gateways
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Producao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_Firmwares();

            // le firmwares
            FirmwaresMetodos fwMetodos = new FirmwaresMetodos();
            List<FirmwaresDominio> listaFirmwares = fwMetodos.ListarTodos();

            return View(listaFirmwares);
        }

        // GET: Configuração Firmwares - Editar
        public ActionResult Firmwares_Editar(int IDFirmware)
        {
            // tela de ajuda  
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // pagina anterior
            ViewBag.returnUrl = Request.UrlReferrer;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Producao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // drivers
            List<Firmwares_DriversDominio> listaDrv = new List<Firmwares_DriversDominio>();

            // verifica se adicionando
            FirmwaresDominio fw = new FirmwaresDominio();
            if (IDFirmware == 0)
            {
                // zera com default
                fw.IDFirmware = 0;
                fw.IDTipoGateway = TIPO_GATEWAY.GATE_E;
                fw.Versao = 0;
                fw.Lib = 0;
                fw.IDTipoFirmwareStatus = 1;
                fw.Criacao = DateTime.Now;
                fw.CriacaoTexto = fw.Criacao.ToString("d");
                fw.Coldboot = false;
                fw.NumDrivers = 0;
                fw.Arquivo = "";
                fw.NotasRevisao = "";
            }
            else
            {
                // lê firmware
                FirmwaresMetodos fwMetodos = new FirmwaresMetodos();
                fw = fwMetodos.ListarPorIDFirmware(IDFirmware);

                // lê drivers deste firware
                Firmwares_DriversMetodos fwDrvMetodos = new Firmwares_DriversMetodos();
                listaDrv = fwDrvMetodos.ListarPorIDFirmware(IDFirmware);
            }

            // cria lista de drivers deste firmware
            List<int> ConfigDriverList_Grupo = new List<int>();

            foreach (Firmwares_DriversDominio drv in listaDrv)
            {
                ConfigDriverList_Grupo.Add(drv.IDDriver);
            }

            // le drivers configurados do firmware para lista
            DriversMetodos drvMetodos = new DriversMetodos();
            List<DriversDominio> drivers = new List<DriversDominio>();

            List<DriversDominio> drivers_utilizados = new List<DriversDominio>();
            List<DriversDominio> drivers_nao_utilizados = new List<DriversDominio>();

            if (ConfigDriverList_Grupo != null)
            {
                // le todos os drivers
                drivers = drvMetodos.ListarTodos();

                // seleciona drivers 
                int contador;

                // percorre lista
                if (drivers != null)
                {
                    for (contador = 0; contador < drivers.Count(); contador++)
                    {
                        // verifica se drivers esta habilitado para o firmware
                        if (ConfigDriverList_Grupo.Contains(drivers[contador].IDDriver))
                        {
                            // copia drivers na lista de utilizados
                            drivers_utilizados.Add(drivers[contador]);
                        }
                        else
                        {
                            // copia driver na lista de nao utilizados
                            drivers_nao_utilizados.Add(drivers[contador]);
                        }
                    }
                }
            }

            ViewBag.Drivers = drivers_utilizados;
            ViewBag.Drivers2 = drivers_nao_utilizados;

            // prepara listas
            PreparaListas_Firmwares();

            return View(fw);
        }

        // Listas utilizadas
        private void PreparaListas_Firmwares()
        {
            // le tipos gateway
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposGateway = listatiposMetodos.ListarTodos("TipoGateway");
            ViewBag.listaTipoGateway = listatiposGateway;

            // le tipos status
            List<ListaTiposDominio> listaFirmwareStatus = listatiposMetodos.ListarTodos("TipoFirmwareStatus", true);
            ViewBag.listaFirmwareStatus = listaFirmwareStatus;

            return;
        }

        // POST: Configuração Firmwares - Salvar
        [HttpPost]
        public ActionResult Firmwares_Salvar(FirmwaresDominio fw, List<Firmwares_DriversDominio> fwDrvs)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro com a mesma versão
            FirmwaresMetodos fwMetodos = new FirmwaresMetodos();
            if (fwMetodos.VerificarDuplicidade(fw))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Firmware existente."
                };
            }
            else
            {
                // movo arquivo de temporário para produção
                FirmwareMetodos firmwareMetodos = new FirmwareMetodos();
                firmwareMetodos.MoverFirmware_Temp2Producao(fw.Arquivo);

                // parse criação
                fw.Criacao = DateTime.Parse(fw.CriacaoTexto); 

                // salva firmware
                fwMetodos.Salvar(fw);

                // verifica integridade
                int retorno = fwMetodos.VerificarIntegridade(fw);

                switch (retorno)
                {
                    case 1:
                        // erro
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "Firmware não adicionado."
                        };

                        // retorna status
                        return Json(returnedData);

                    case 2:
                        // erro
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "IDFirmware adicionado com valor alto.<br><br>FAVOR AVISAR O DESENVOLVIMENTO ANTES DE PROSSEGUIR !!!"
                        };

                        // retorna status
                        return Json(returnedData);
                }


                // pego IDFirmware novamente (pois pode ter sido inserção)
                FirmwaresDominio fw_novo = fwMetodos.ListarPorVersao(fw);

                // verifica se salvou
                if (fw_novo != null)
                {
                    // evento 
                    UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();

                    if (fw_novo.IDFirmware > 0)
                    {
                        usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.FIRMWARE, fw_novo.IDFirmware);
                    }
                    else
                    {
                        usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.FIRMWARE, fw_novo.IDFirmware);
                    }

                    // salva drivers deste firmware
                    Firmwares_DriversMetodos fwDrvMetodos = new Firmwares_DriversMetodos();

                    // exclui todos os drivers deste firmware
                    fwDrvMetodos.ExcluirPorIDFirmware(fw_novo.IDFirmware);

                    // salva drivers
                    if (fwDrvs != null)
                    {
                        // percorre lista e salva
                        foreach (Firmwares_DriversDominio drv in fwDrvs)
                        {
                            // salva
                            drv.IDFirmware = fw_novo.IDFirmware;
                            fwDrvMetodos.Salvar(drv);
                        }
                    }
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Firmwares - Excluir
        public ActionResult Firmwares_Excluir(int IDFirmware)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // configuração do firmware 
            FirmwaresMetodos fwMetodos = new FirmwaresMetodos();
            FirmwaresDominio fw = fwMetodos.ListarPorIDFirmware(IDFirmware);

            if (fw == null)
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Configuração do firmware inexistente."
                };

                // retorna status
                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // exclui o arquivo do firmware
            FirmwareMetodos firmwareMetodos = new FirmwareMetodos();
            firmwareMetodos.Excluir(fw.Arquivo);

            // apaga o firmware
            fwMetodos.ExcluirPorIDFirmware(IDFirmware);

            Firmwares_DriversMetodos drvMetodos = new Firmwares_DriversMetodos();
            drvMetodos.ExcluirPorIDFirmware(IDFirmware);

            // evento 
            UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.EXCLUI_CONFIG, TABELA_CONFIG.FIRMWARE, IDFirmware);

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Obter drivers padrão
        public JsonResult ObterDriversPadrao()
        {
            // lê drivers padrão
            Firmwares_DriversMetodos fwDrvMetodos = new Firmwares_DriversMetodos();
            List<Firmwares_DriversDominio> listaDrv = fwDrvMetodos.ListarPorIDFirmware(0);

            // cria lista de drivers 
            List<int> ConfigDriverList_Grupo = new List<int>();

            foreach (Firmwares_DriversDominio drv in listaDrv)
            {
                ConfigDriverList_Grupo.Add(drv.IDDriver);
            }

            // le drivers configurados do firmware para lista
            DriversMetodos drvMetodos = new DriversMetodos();
            List<DriversDominio> drivers = new List<DriversDominio>();

            List<DriversDominio> drivers_padrao = new List<DriversDominio>();

            if (ConfigDriverList_Grupo != null)
            {
                // le todos os drivers
                drivers = drvMetodos.ListarTodos();

                // seleciona drivers 
                int contador;

                // percorre lista
                if (drivers != null)
                {
                    for (contador = 0; contador < drivers.Count(); contador++)
                    {
                        // verifica se drivers esta habilitado para o firmware
                        if (ConfigDriverList_Grupo.Contains(drivers[contador].IDDriver))
                        {
                            // copia drivers na lista de utilizados
                            drivers_padrao.Add(drivers[contador]);
                        }
                    }
                }
            }

            ViewBag.DriversPadrao = drivers_padrao;

            // retorna o valor em JSON
            return Json(drivers_padrao, JsonRequestBehavior.AllowGet);
        }

     
        // POST: Configuração Firmwares - Upload do firmware
        [HttpPost]
        public virtual ActionResult Firmwares_UploadFile()
        {
            // arquivo enviado
            HttpPostedFileBase myFile = Request.Files["fileupload_Firmware"];

            // inicia variaveis
            bool isUploaded = false;
            string message = "Envio falhou";

            FirmwareDominio firmware = new FirmwareDominio();
            firmware.Arquivo = "";
            firmware.IDTipoGateway = 0;
            firmware.Versao = "0";
            firmware.Lib = "0";
            firmware.Status = "0";
            firmware.Versao_txt = "0.00";
            DateTime criacao = new DateTime(2000, 1, 1, 0, 0, 0);

            // verifica arquivo
            if (myFile != null && myFile.ContentLength != 0)
            {
                // caminho Firmware temporário
                string pathForSaving = Path.Combine(ConfigurationManager.AppSettings["Firmware"], "Temp");

                try
                {
                    // nome do arquivo
                    firmware.Arquivo = myFile.FileName;

                    // nome do caminho e arquivo
                    string caminho_arq = Path.Combine(pathForSaving, myFile.FileName);

                    // extensão através dos ultimos 4 caracteres
                    string extensao_arq = firmware.Arquivo.Substring(firmware.Arquivo.Length - 4).ToLower();


                    // identifica firmware
                    FirmwareMetodos firmwareMetodos = new FirmwareMetodos();
                    firmware = firmwareMetodos.IdentificaFirmware(firmware.Arquivo);

                    // trata arquivo DAT
                    if (extensao_arq == ".dat" && firmware.IDTipoGateway > 0)
                    {
                        // verifica se arquivo existe e apaga
                        FileInfo file = new FileInfo(caminho_arq);

                        if (file.Exists)
                        {
                            // deleta
                            file.Delete();
                        }

                        // salva arquivo
                        myFile.SaveAs(caminho_arq);

                        // criação
                        criacao = file.CreationTime;

                        // enviado e salvo
                        isUploaded = true;
                        message = "O firmware foi salvo.";
                    }
                    else
                    {
                        // erro
                        isUploaded = false;
                        message = string.Format("Formato incorreto [{0}]", firmware.Arquivo);

                        if (extensao_arq != ".dat")
                        {
                            message = string.Format("Extensão não permitida [{0}]", firmware.Arquivo);
                        }
                    }
                }
                catch (Exception ex)
                {
                    // falha
                    isUploaded = false;
                    message = string.Format("Envio falhou: {0}", ex.Message);
                }
            }

            // retorno
            var returnedData = new
            {
                isUploaded = isUploaded,
                message = message,
                arquivo = firmware.Arquivo,
                IDTipoGateway = firmware.IDTipoGateway,
                versao = int.Parse(firmware.Versao),
                lib = int.Parse(firmware.Lib),
                status = int.Parse(firmware.Status),
                criacao = string.Format("d")
            };

            // retorna status
            return Json(returnedData, "text/html");
        }
    }
}