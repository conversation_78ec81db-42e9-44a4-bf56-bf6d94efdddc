﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Web;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SuporteController
    {
        // GET: Configuração Drivers
        public ActionResult Drivers()
        {
            // tela de ajuda - gateways
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Producao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_Drivers();

            // le drivers
            DriversMetodos drvMetodos = new DriversMetodos();
            List<DriversDominio> listaDrivers = drvMetodos.ListarTodos();

            return View(listaDrivers);
        }

        // GET: Configuração Drivers - Editar
        public ActionResult Drivers_Editar(int IDDriver)
        {
            // tela de ajuda  
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // pagina anterior
            ViewBag.returnUrl = Request.UrlReferrer;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Producao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // verifica se adicionando
            DriversDominio drv = new DriversDominio();
            if (IDDriver == 0)
            {
                // zera com default
                drv.IDDriver = 0;
                drv.Nome = "";
                drv.IDTipoFabricante = 0;
                drv.Descricao = "";
                drv.drvAd = 0;
                drv.Manual1 = "";
                drv.Manual2 = "";
            }
            else
            {
                // lê driver
                DriversMetodos drvMetodos = new DriversMetodos();
                drv = drvMetodos.ListarPorIDDriver(IDDriver);
            }

            // prepara listas
            PreparaListas_Drivers();

            return View(drv);
        }

        // Listas utilizadas
        private void PreparaListas_Drivers()
        {
            // le tipos status
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listaFabricantes = listatiposMetodos.ListarTodos("TipoFabricante", true);
            ViewBag.listaFabricantes = listaFabricantes;

            return;
        }

        // POST: Configuração Driver - Salvar
        [HttpPost]
        public ActionResult Drivers_Salvar(DriversDominio drv)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro com o mesmo nome
            DriversMetodos drvMetodos = new DriversMetodos();
            if (drvMetodos.VerificarDuplicidade(drv))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = string.Format("Driver {0} existente.", drv.Nome)
                };
            }
            else
            {
                // salva driver
                drvMetodos.Salvar(drv);

                // verifica integridade
                int retorno = drvMetodos.VerificarIntegridade(drv);

                switch (retorno)
                {
                    case 1:
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "Driver não adicionado."
                        };
                        break;

                    case 2:
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "IDDriver adicionado com valor alto.<br><br>FAVOR AVISAR O DESENVOLVIMENTO ANTES DE PROSSEGUIR !!!"
                        };
                        break;
                }


                // pego IDDriver novamente (pois pode ter sido inserção)
                DriversDominio drv_novo = drvMetodos.ListarPorNome_Fabricante(drv.Nome, drv.IDTipoFabricante);

                // verifica se salvou
                if (drv_novo != null)
                {
                    // evento 
                    UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();

                    if (drv.IDDriver > 0)
                    {
                        usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.DRIVER, drv.IDDriver);
                    }
                    else
                    {
                        usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.DRIVER, drv.IDDriver);
                    }
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Driver - Excluir
        public ActionResult Drivers_Excluir(int IDDriver)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // apaga o driver
            DriversMetodos drvMetodos = new DriversMetodos();
            drvMetodos.ExcluirPorIDDriver(IDDriver);

            // evento 
            UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.EXCLUI_CONFIG, TABELA_CONFIG.DRIVER, IDDriver);

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        public virtual ActionResult UploadFile_Manual1()
        {
            // upload manual
            return (UploadFile_Manual("fileupload_Manual1"));
        }

        [HttpPost]
        public virtual ActionResult UploadFile_Manual2()
        {
            // upload manual
            return (UploadFile_Manual("fileupload_Manual2"));
        }

        private ActionResult UploadFile_Manual(string manual)
        {
            // arquivo enviado
            HttpPostedFileBase myFile = Request.Files[manual];

            // inicia variaveis
            bool isUploaded = false;
            string message = "Envio falhou";
            string nome_arq = "";

            if (myFile != null && myFile.ContentLength != 0)
            {
                // caminho Firmware
                string pathForSaving = Server.MapPath("~/Manuais");

                try
                {
                    // nome do arquivo
                    nome_arq = myFile.FileName;

                    // nome do caminho e arquivo
                    string caminho_arq = Path.Combine(pathForSaving, myFile.FileName);

                    // extensão através dos ultimos 4 caracteres
                    string extensao_arq = nome_arq.Substring(nome_arq.Length - 4).ToLower();


                    // trata arquivo PDF
                    if (extensao_arq == ".pdf")
                    {
                        // verifica se arquivo existe
                        FileInfo file = new FileInfo(caminho_arq);

                        if (file.Exists)
                        {
                            // erro
                            message = string.Format("Manual [{0}] já existe", nome_arq);
                        }
                        else
                        {
                            // salva arquivo
                            myFile.SaveAs(caminho_arq);

                            // enviado e salvo
                            isUploaded = true;
                            message = "O manual foi salvo.";
                        }
                    }
                    else
                    {
                        // erro
                        message = string.Format("Extensão não permitida [{0}]", nome_arq);
                    }
                }
                catch (Exception ex)
                {
                    // falha
                    isUploaded = false;
                    message = string.Format("Envio falhou: {0}", ex.Message);
                }
            }

            // retorno
            var returnedData = new
            {
                isUploaded = isUploaded,
                message = message,
                arquivo = nome_arq
            };

            // retorna status
            return Json(returnedData, "text/html");
        }
    }
}