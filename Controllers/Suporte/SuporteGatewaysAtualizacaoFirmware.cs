﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SuporteController
    {
        public ActionResult GatewaysAtualizacaoFirmware()
        {
            // tela de ajuda - gateways info
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // le cookies
            LeCookies_SmartEnergy();

            // data e hora atual
            DateTime datahora = DateTime.Now;

            ViewBag.Data = string.Format("{0:d}", datahora);
            ViewBag.Hora = string.Format("{0:HH:mm}", datahora);

            // le firmwares
            FirmwareMetodos firmwareMetodos = new FirmwareMetodos();
            List<FirmwareDominio> listaFirmware = firmwareMetodos.ListarPorIDTipoGateway(TIPO_GATEWAY.GATE_E);
            ViewBag.listaFirmware = listaFirmware;

            // leio lista de supervisao gateways - todas gateways 'SmartGate E' por ordem de cliente/gateway
            SupervGatewaysMetodos gatewaysMetodos = new SupervGatewaysMetodos();
            List<SupervGatewaysDominio> GatewaysSupervListTodos = gatewaysMetodos.ListarPorTipoGateway(TIPO_GATEWAY.GATE_E);

            return View(GatewaysSupervListTodos);
        }

        // GET: Processo de atualização de firmware
        public ActionResult Firmware_Atualizar_IniciaProcesso(List<int> gateways, string versaoSolicitada, string datahora)
        {
            //
            // Insere solicitação de atualização na lista de updates
            //

            // le cookies
            LeCookies_SmartEnergy();

            // IDUsuario
            int IDUsuario = ViewBag._IDUsuario;

            // email do usuário que irá receber resultado da atualização
            string email = "---";

            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = usuarioMetodos.ListarPorId(IDUsuario);

            if (usuario != null)
            {
                email = usuario.Email;
            }

            // converte versão solicitada
            int VersaoSolicitada = int.Parse(versaoSolicitada);

            // data hora
            DateTime programacaoDataHora = new DateTime(2000, 1, 1, 0, 0, 0);

            if (!DateTime.TryParse(datahora, out programacaoDataHora))
            {
                // executa imediatamente
                programacaoDataHora = new DateTime(2000, 1, 1, 0, 0, 0);
            }

            // verifica se existe lista de gateway
            if (gateways == null)
            {
                // erro
                var returnedErro = new
                {
                    status = "ERRO",
                    erro = "Lista de gateways inexistente."
                };

                // retorna erro
                return Json(returnedErro, JsonRequestBehavior.AllowGet);
            }

            // percorre gateways
            foreach (int IDGateway in gateways)
            {
                // inicia update
                ListaUpdatesMetodos updateMetodos = new ListaUpdatesMetodos();
                updateMetodos.Inicia(IDGateway, IDUsuario, 0, VersaoSolicitada, programacaoDataHora);
            }

            // retorno
            var returnedData = new
            {
                status = "OK",
                email = email
            };

            // retorna ok
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }
    }
}