﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class MonitoracaoController
    {
        // GET: Supervisao Gestor
        public ActionResult Gestor(int IDConsultor = 4915)
        {
            // tela cheia
            ViewBag._DashboardTelaCheia = true;
            CookieStore.SalvaCookie_Bool("DashboardTelaCheia", true);

            // IDConsultor
            CookieStore.SalvaCookie_Int("_IDConsultor", IDConsultor);

            // tema do gestor
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio gestor = usuarioMetodos.ListarPorId(IDConsultor);
            int IDTema = 0;

            if (gestor != null)
            {
                // tema
                IDTema = gestor.IDTema;
            }

            // tema
            ListaTiposMetodos temaMetodos = new ListaTiposMetodos();
            TemaDominio tema = temaMetodos.ListarPorId_Tema(IDTema);

            CookieStore.SalvaCookie_Int("Tema", IDTema);
            CookieStore.SalvaCookie_String("background", tema.background);
            CookieStore.SalvaCookie_String("background_hover", tema.background_hover);
            CookieStore.SalvaCookie_String("background_nav_header", tema.background_nav_header);
            CookieStore.SalvaCookie_String("background_panel_title", tema.background_panel_title);

            // calcula
            Calc_Gestor();

            return View();
        }

        // GET: Atualizar
        public PartialViewResult _Gestor_Atualizar()
        {
            // calcula
            Calc_Gestor();

            return PartialView();
        }

        public void Calc_Gestor()
        {
            // IDConsultor
            int IDConsultor = CookieStore.LeCookie_Int("_IDConsultor");

            // leio TODOS clientes deste consultor - ativos
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            List<ClientesDominio> clientes = clientesMetodos.ListarPorIDConsultor(IDConsultor, 1);

            // numero de clientes
            int NumClientes = clientes.Count;

            // numero de medicoes e gateways
            int NumGateways = 0;
            int NumMedicoes = 0;

            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            SupervGatewaysMetodos supGatewaysMetodos = new SupervGatewaysMetodos();

            if (clientes != null)
            {
                foreach (ClientesDominio cliente in clientes)
                {
                    // numero de gateways
                    List<GatewaysDominio> gateways = gatewaysMetodos.ListarPorIDCliente(cliente.IDCliente);

                    int GatewaysAtraso = 0;

                    if( gateways != null )
                    {
                        NumGateways += gateways.Count;

                        foreach(GatewaysDominio gateway in gateways)
                        {
                            // leio lista de supervisao gateways
                            SupervGatewaysDominio gatewaySuperv = supGatewaysMetodos.ListarPorIDGateway(gateway.IDGateway);

                            if( gatewaySuperv != null )
                            {
                                // data e hora
                                DateTime data_hora_hoje = DateTime.Now;
                                DateTime data_hora_atualizacao = gatewaySuperv.DataHora;

                                // verifica status
                                switch (gatewaySuperv.IDTipoTempo)
                                {
                                    case TIPO_TEMPO_GATEWAY.GatewayBloqueada:                       // 0 - gateway bloqueada
                                    case TIPO_TEMPO_GATEWAY.Ignorar_StartUpNaoRealizado:            // 1 - ignorar - start up nao realizado
                                    case TIPO_TEMPO_GATEWAY.Ignorar_PendenciaCliente:               // 2 - ignorar - pendencia do cliente
                                    case TIPO_TEMPO_GATEWAY.SCDE:                                   // 3 - ignorar - SCDE
                                        break;

                                    case TIPO_TEMPO_GATEWAY.Envio_15minutos:                        // 12 - a cada 15 minutos

                                        // verifica se atraso maior que 6 horas
                                        if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(6, 0, 0))
                                        {
                                            // total
                                            GatewaysAtraso++;
                                        }

                                        break;

                                    case TIPO_TEMPO_GATEWAY.Envio_hora:                             // 13 - a cada hora

                                        // verifica se atraso maior que 6 horas
                                        if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(6, 0, 0))
                                        {
                                            // total
                                            GatewaysAtraso++;
                                        }

                                        break;

                                    case TIPO_TEMPO_GATEWAY.Envio_diariamente:                      // 14 - diariamente

                                        // verifica se atraso maior que 2 dias
                                        if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(48, 0, 0))
                                        {
                                            // total
                                            GatewaysAtraso++;
                                        }

                                        break;

                                    case TIPO_TEMPO_GATEWAY.Envio_semanalmente:                     // 15 - semanalmente

                                        // verifica se atraso maior que 8 dias
                                        if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(192, 0, 0))
                                        {
                                            // total
                                            GatewaysAtraso++;
                                        }

                                        break;

                                    case TIPO_TEMPO_GATEWAY.Envio_mensalmente:                      // 16 - mensalmente

                                        // verifica se atraso maior que 32 dias
                                        if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(768, 0, 0))
                                        {
                                            // total
                                            GatewaysAtraso++;
                                        }

                                        break;
                                }
                            }
                        }
                    }

                    // numero de gateways em atraso
                    cliente.NumGateways = GatewaysAtraso;

                    // numero de medicoes
                    List<MedicoesDominio> medicoes = medicoesMetodos.ListarPorIDCliente(cliente.IDCliente, 1);

                    if (medicoes != null)
                    {
                        NumMedicoes += medicoes.Count;
                    }
                }
            }

            ViewBag.ListaClientes = clientes;
            ViewBag.NumClientes = NumClientes;
            ViewBag.NumGateways = NumGateways;
            ViewBag.NumMedicoes = NumMedicoes;

            return;
        }
    }
}