﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.ServiceProcess;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class MonitoracaoController : Controller
    {
        // GET: Inicio
        public ActionResult Inicio()
        {
            // tela cheia
            ViewBag._DashboardTelaCheia = true;
            CookieStore.SalvaCookie_Bool("DashboardTelaCheia", true);

            // tema
            ListaTiposMetodos temaMetodos = new ListaTiposMetodos();
            TemaDominio tema = temaMetodos.ListarPorId_Tema(0);

            CookieStore.SalvaCookie_Int("Tema", tema.IDTema);
            CookieStore.SalvaCookie_String("background", tema.background);
            CookieStore.SalvaCookie_String("background_hover", tema.background_hover);
            CookieStore.SalvaCookie_String("background_nav_header", tema.background_nav_header);
            CookieStore.SalvaCookie_String("background_panel_title", tema.background_panel_title);

            // calcula
            Calc_Inicio();

            return View();
        }

        // GET: Atualizar
        public PartialViewResult _Inicio_Atualizar()
        {
            // calcula
            Calc_Inicio();

            return PartialView();
        }

        public void Calc_Inicio()
        {

            //
            // Arquivos Recebidos
            //

            // caminho Recebidos
            string CaminhoRecebidos = ConfigurationManager.AppSettings["Recebidos"];

            // numero de arquivos em Recebidos
            var list = Directory.GetFiles(CaminhoRecebidos, "*.*");
            ViewBag.ArquivosRecebidos = list.Length;


            //
            // Gateways em Atraso
            //

            // gateway em atraso
            int GatewaysAtraso_Total = 0;

            // leio lista de supervisao gateways
            SupervGatewaysMetodos gatewaysMetodos = new SupervGatewaysMetodos();
            List<SupervGatewaysDominio> GatewaysSupervListTodos = gatewaysMetodos.ListarTodos();

            // verifica se existe
            if (GatewaysSupervListTodos != null)
            {
                // analiso os atrasos
                foreach (SupervGatewaysDominio gateway in GatewaysSupervListTodos)
                {
                    // verifico gateway se esta em atraso pelo tempo de envio configurado
                    bool gateway_em_atraso = false;

                    // data e hora
                    DateTime data_hora_hoje = DateTime.Now;
                    DateTime data_hora_atualizacao = gateway.DataHora;

                    // verifica status
                    switch (gateway.IDTipoTempo)
                    {
                        case TIPO_TEMPO_GATEWAY.GatewayBloqueada:                           // 0 - gateway bloqueada
                        case TIPO_TEMPO_GATEWAY.Ignorar_StartUpNaoRealizado:                // 1 - ignorar - start up nao realizado
                        case TIPO_TEMPO_GATEWAY.Ignorar_PendenciaCliente:                   // 2 - ignorar - pendencia do cliente
                        case TIPO_TEMPO_GATEWAY.SCDE:                                       // 3 - ignorar - SCDE

                            gateway_em_atraso = false;
                            break;

                        case TIPO_TEMPO_GATEWAY.Envio_15minutos:                            // 12 - a cada 15 minutos

                            // verifica se atraso maior que 6 horas
                            if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(6, 0, 0))
                            {
                                // gateway em atraso
                                gateway_em_atraso = true;
                            }

                            break;

                        case TIPO_TEMPO_GATEWAY.Envio_hora:                                 // 13 - a cada hora

                            // verifica se atraso maior que 6 horas
                            if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(6, 0, 0))
                            {
                                // gateway em atraso
                                gateway_em_atraso = true;
                            }

                            break;

                        case TIPO_TEMPO_GATEWAY.Envio_diariamente:                          // 14 - diariamente

                            // verifica se atraso maior que 2 dias
                            if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(48, 0, 0))
                            {
                                // gateway em atraso
                                gateway_em_atraso = true;
                            }

                            break;

                        case TIPO_TEMPO_GATEWAY.Envio_semanalmente:                         // 15 - semanalmente

                            // verifica se atraso maior que 8 dias
                            if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(192, 0, 0))
                            {
                                // gateway em atraso
                                gateway_em_atraso = true;
                            }

                            break;

                        case TIPO_TEMPO_GATEWAY.Envio_mensalmente:                          // 16 - mensalmente

                            // verifica se atraso maior que 32 dias
                            if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(768, 0, 0))
                            {
                                // gateway em atraso
                                gateway_em_atraso = true;
                            }

                            break;
                    }

                    // verifica se gateway em atraso
                    if (gateway_em_atraso)
                    {
                        // total
                        GatewaysAtraso_Total++;
                    }
                }

            }

            // gateways em atraso
            ViewBag.GatewaysAtraso = GatewaysAtraso_Total;


            //
            // Gateways Bateria Fraca
            //

            // número de gateways bateria fraca
            SupervGatewaysBateriaFracaMetodos gatewaysBateriaFracaMetodos = new SupervGatewaysBateriaFracaMetodos();
            int NumGatewaysBateriaFraca = gatewaysBateriaFracaMetodos.NumGatewaysBateriaFraca();
            ViewBag.NumGatewaysBateriaFraca = NumGatewaysBateriaFraca;


            //
            // Status de processos
            //

            // verifica Broker (Mosquitto)
            //string aProcName = "mosquitto";

            // verifica Broker (Artemis)
            bool status_Broker = false;
            string aProcName = "artemis-broker-0.0.0.0";
            if (verificarServicoAtivo(aProcName))
            {
                // serviço running
                status_Broker = true;
            }

            ViewBag.status_Broker = status_Broker;

            // verifica SmartMQTT
            bool status_SmartMQTT = false;
            aProcName = "SmartMQTT";
            if (verificarProcessoAtivo(aProcName))
            {
                // programa em execução
                status_SmartMQTT = true;
            }
            ViewBag.status_SmartMQTT = status_SmartMQTT;

            // API Artemis Broker - listAllConsumers
            ArtemisBroker artemis = new ArtemisBroker();
            bool status_SmEnergy = artemis.Consumer_Conectado("SmEnergy");
            bool status_SmUpload = artemis.Consumer_Conectado("SmUpload");
            bool status_SmUpdate = artemis.Consumer_Conectado(ConfigurationManager.AppSettings["SmUpdate_Topico_Subscribe"]);

            ViewBag.status_SmEnergy = status_SmEnergy;
            ViewBag.status_SmUpload = status_SmUpload;
            ViewBag.status_SmUpdate = status_SmUpdate;


            //
            // Gateways Startup nao realizado
            //

            // gateway startup nao realizado com atualizacao < 2 dias
            int GatewaysStartup_Atualizado = 0;

            // leio lista de supervisao gateways
            GatewaysSupervListTodos = gatewaysMetodos.ListarTodos(1);

            // verifica se existe
            if (GatewaysSupervListTodos != null)
            {
                // analiso os atrasos
                foreach (SupervGatewaysDominio gateway in GatewaysSupervListTodos)
                {
                    // diferenca
                    TimeSpan diferenca = DateTime.Now - gateway.DataHora;

                    // verifica se atraso menor que 2 dias
                    if (diferenca.Days <= 2)
                    {
                        // total
                        GatewaysStartup_Atualizado++;
                    }
                }
            }

            // gateways em atraso
            ViewBag.GatewaysStartup_Atualizado = GatewaysStartup_Atualizado;

            //
            // Clientes que nao se logaram nos ultimos 3 meses
            //

            // clientes sem logar
            int ClientesSemLogar_Total = 0;

            // usuarios 
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();

            // clientes
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> clientes = clienteMetodos.ListarTodos();

            if (clientes != null)
            {
                foreach (ClientesDominio cliente in clientes)
                {
                    // numero de usuarios do cliente
                    int num_usuarios = usuarioMetodos.NumUsuarios(cliente.IDCliente);

                    // usuarios sem logar
                    List<UsuarioDominio> usuarios = usuarioMetodos.ListarPorQuemNaoLogou(cliente.IDCliente);

                    if (usuarios != null)
                    {
                        // verifica se todos os usuarios nao se logaram
                        if (usuarios.Count > 0 && num_usuarios == usuarios.Count)
                        {
                            // incrementa
                            ClientesSemLogar_Total++;
                        }
                    }
                }
            }

            // usuarios sem logar
            ViewBag.ClientesSemLogar_Total = ClientesSemLogar_Total;


            //
            // Estatisticas de Processamento - Graficos
            //
            GraficoProcessamento();


            //
            // Estatisticas SmartEnergy
            //

            // numero de clientes
            int NumClientes = clienteMetodos.NumClientes();
            ViewBag.NumClientes = NumClientes;

            // numero de gateways
            GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
            int NumGateways = gatewayMetodos.NumGateways();
            ViewBag.NumGateways = NumGateways;

            // % gateways em atraso
            double GatewaysAtraso_Porc = 0.0;
            if (NumGateways > 0)
            {
                GatewaysAtraso_Porc = (GatewaysAtraso_Total / (double)NumGateways) * 100.0;
            }
            ViewBag.GatewaysAtraso_Porc = GatewaysAtraso_Porc;

            // numero de medicoes
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            int NumMedicoes = medicaoMetodos.NumMedicoes();
            ViewBag.NumMedicoes = NumMedicoes;

            // estatisticas SmartEnergy
            StatsSmartEnergyMetodos statsSmartEnergyMetodos = new StatsSmartEnergyMetodos();
            List<StatsSmartEnergyDominio> statsSmartEnergy = statsSmartEnergyMetodos.Ultimos90Dias();

            int NumClientes_90dias = 0;
            int NumGateways_90dias = 0;
            int NumMedicoes_90dias = 0;
            int NumDias_90dias = 0;

            if (statsSmartEnergy != null)
            {
                if (statsSmartEnergy.Count > 0)
                {
                    NumClientes_90dias = statsSmartEnergy[0].num_clientes;
                    NumGateways_90dias = statsSmartEnergy[0].num_gateways;
                    NumMedicoes_90dias = statsSmartEnergy[0].num_medicoes;

                    TimeSpan diferenca = DateTime.Now - statsSmartEnergy[0].DataHora;
                    NumDias_90dias = diferenca.Days;
                }
            }

            ViewBag.NumDias_90dias = NumDias_90dias;
            ViewBag.NumClientes_90dias = NumClientes - NumClientes_90dias;
            ViewBag.NumGateways_90dias = NumGateways - NumGateways_90dias;
            ViewBag.NumMedicoes_90dias = NumMedicoes - NumMedicoes_90dias;

            return;
        }

        private bool verificarProcessoAtivo(string nomeProcesso)
        {
            try
            {
                // Cria um array de processos - Lembrar de importar o namespace System.Diagnostics
                Process[] listaProcessos = Process.GetProcessesByName(nomeProcesso);

                if (listaProcessos != null)
                {
                    // número de processos
                    int NumProcessos = listaProcessos.Count();

                    // verifica se tem algum processo
                    if (NumProcessos > 0)
                    {
                        // log
                        LogMessage(string.Format("[{0}] Processo OK", nomeProcesso));

                        // encontrou processo
                        return true;
                    }
                }

                // log
                LogMessage(string.Format("[{0}] ERRO: Não encontrou processo", nomeProcesso));

                // não encontrou o processo, retorna false
                return false;
            }
            catch (Exception ex)
            {
                // log
                LogMessage(string.Format("[{0}] ERRO: Excessão em 'verificarProcessoAtivo' [{1}]", nomeProcesso, ex.Message));

                // não encontrou o processo, retorna false
                return false;
            }
        }

        private bool verificarServicoAtivo(string nomeServico)
        {
            // status
            string status = "Desconhecido";

            try
            {
                // Verifica servico - Lembrar de importar o namespace System.ServiceProcess
                ServiceController sc = new ServiceController(nomeServico);

                if (sc != null)
                {
                    // verifica status
                    switch (sc.Status)
                    {
                        case ServiceControllerStatus.Running:

                            // log
                            LogMessage(string.Format("[{0}] Serviço Running", nomeServico.Substring(0, 9)));

                            // serviço running
                            return true;

                        case ServiceControllerStatus.Stopped:
                            status = "Stopped";
                            break;

                        case ServiceControllerStatus.Paused:
                            status = "Paused";
                            break;

                        case ServiceControllerStatus.StopPending:
                            status = "Stopping";
                            break;

                        case ServiceControllerStatus.StartPending:
                            status = "Starting";
                            break;

                        default:
                            status = "Status Changing";
                            break;
                    }
                }

                // log
                LogMessage(string.Format("[{0}] ERRO: Serviço {1}", nomeServico.Substring(0, 9), status));

                // serviço não running, retorna false
                return false;
            }
            catch (Exception ex)
            {
                // log
                LogMessage(string.Format("[{0}] ERRO: Excessão em 'verificarServicoAtivo' [{1}]", nomeServico.Substring(0, 9), ex.Message));

                // serviço não running, retorna false
                return false;
            }
        }

        private void LogMessage(string msg)
        {
            // log
            Funcoes_Log.Mensagem("SmartEnergy_LogProcessos", string.Format("[Monitoracao_Inicio] {0}", msg));
        }

        // GET: Grafico processados 
        public PartialViewResult _Inicio_GraficoProcessados(int Navegacao = 0)
        {

            //
            // Estatisticas de Processamento - Graficos
            //
            GraficoProcessamento();

            return PartialView();
        }

        private void GraficoProcessamento()
        {

            //
            // Estatisticas de Processamento FTP - Dia
            //

            // le cookie datahora (atual ou inicial)
            DateTime datahora_cookie = DateTime.Now; 

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", datahora_cookie);

            // dia da semana
            string day = new CultureInfo("pt-BR").DateTimeFormat.GetDayName(datahora_cookie.DayOfWeek);
            string diaExtenso = char.ToUpper(day[0]) + day.Substring(1);
            ViewBag.DiaSemanaAtual = diaExtenso;


            // estatistica de processamento - hoje
            StatsProcessamentoMetodos statsMetodos = new StatsProcessamentoMetodos();
            List<StatsProcessamentoDominio> stats_hoje_FTP = statsMetodos.Dia(datahora_cookie);

            // medias
            List<StatsProcessamentoDominio> stats_medias_FTP = statsMetodos.Medias(datahora_cookie);

            int i = 0;

            if (stats_hoje_FTP != null)
            {
                // grafico
                var NumArquivos = new double[290];
                var NumArquivos_Processados = new double[290];
                var NumArquivos_Problemas = new double[290];
                var NumArquivos_Media = new double[290];
                var NumArquivos_Tempo = new double[290];
                var Datas = new string[290];

                double NumArquivos_max = 0.0;

                // valores
                DateTime hoje = datahora_cookie;
                DateTime strDataIni = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 5, 0);

                // volta 10min para a primeira barra
                strDataIni = strDataIni.AddMinutes(-5);

                for (i = 0; i < 290; i++)
                {
                    // formata label
                    Datas[i] = strDataIni.ToString("yyyy-MM-dd HH:mm:ss");

                    // zera valor
                    NumArquivos[i] = 0;
                    NumArquivos_Processados[i] = 0;
                    NumArquivos_Problemas[i] = 0;
                    NumArquivos_Media[i] = 0;
                    NumArquivos_Tempo[i] = 0;

                    // busca horario
                    DateTime strDataFim = strDataIni.AddMinutes(5);
                    List<StatsProcessamentoDominio> inside = stats_hoje_FTP.Where(item => (item.DataHora >= strDataIni && item.DataHora < strDataFim)).ToList();

                    if (inside != null)
                    {
                        foreach (StatsProcessamentoDominio stat in inside)
                        {
                            NumArquivos[i] += stat.num_arquivos;
                            NumArquivos_Processados[i] += stat.num_processados;
                            NumArquivos_Problemas[i] += stat.num_problemas;
                            NumArquivos_Tempo[i] += stat.tempo_processamento;
                        }
                    }

                    // busca media
                    if (stats_medias_FTP != null)
                    {
                        List<StatsProcessamentoDominio> inside_media = stats_medias_FTP.Where(item => (item.DataHora >= strDataIni && item.DataHora < strDataFim)).ToList();

                        if (inside_media != null)
                        {
                            if (inside_media.Count > 0)
                            {
                                NumArquivos_Media[i] = inside_media[0].num_arquivos;
                            }
                        }
                    }

                    // proximo cinco minutos
                    strDataIni = strDataIni.AddMinutes(5);

                    // verifica valor maximo
                    if (NumArquivos[i] > NumArquivos_max)
                        NumArquivos_max = NumArquivos[i];

                    if (NumArquivos_Media[i] > NumArquivos_max)
                        NumArquivos_max = NumArquivos_Media[i];
                }

                NumArquivos_max = NumArquivos_max * 1.1;

                if (NumArquivos_max < 1.0)
                {
                    NumArquivos_max = 10.0;
                }

                ViewBag.NumArquivos_max = NumArquivos_max;

                ViewBag.NumArquivos = NumArquivos;
                ViewBag.NumArquivos_Processados = NumArquivos_Processados;
                ViewBag.NumArquivos_Media = NumArquivos_Media;
                ViewBag.NumArquivos_Problemas = NumArquivos_Problemas;
                ViewBag.NumArquivos_Tempo = NumArquivos_Tempo;
                ViewBag.Datas = Datas;

            }



            //
            // Estatisticas de Processamento MQTT - Dia
            //

            // estatistica de processamento - hoje
            StatsProcessamentoMQTTMetodos statsMQTTMetodos = new StatsProcessamentoMQTTMetodos();
            List<StatsProcessamentoMQTTDominio> stats_hoje_MQTT = statsMQTTMetodos.Dia(datahora_cookie);

            // medias
            List<StatsProcessamentoMQTTDominio> stats_medias_MQTT = statsMQTTMetodos.Medias(datahora_cookie);

            i = 0;

            if (stats_hoje_MQTT != null)
            {
                // grafico
                var NumUploads = new double[290];
                var NumUploads_Processados = new double[290];
                var NumUploads_Problemas = new double[290];
                var NumUploads_Media = new double[290];
                var DatasUploads = new string[290];

                double NumUploads_max = 0.0;

                // valores
                DateTime hoje = datahora_cookie;
                DateTime strDataIni = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 5, 0);

                // volta 10min para a primeira barra
                strDataIni = strDataIni.AddMinutes(-5);

                for (i = 0; i < 290; i++)
                {
                    // formata label
                    DatasUploads[i] = strDataIni.ToString("yyyy-MM-dd HH:mm:ss");

                    // zera valor
                    NumUploads[i] = 0;
                    NumUploads_Processados[i] = 0;
                    NumUploads_Problemas[i] = 0;
                    NumUploads_Media[i] = 0;

                    // busca horario
                    DateTime strDataFim = strDataIni.AddMinutes(5);
                    List<StatsProcessamentoMQTTDominio> inside = stats_hoje_MQTT.Where(item => (item.DataHora >= strDataIni && item.DataHora < strDataFim)).ToList();

                    if (inside != null)
                    {
                        foreach (StatsProcessamentoMQTTDominio stat in inside)
                        {
                            NumUploads[i] += stat.num_arquivos;
                            NumUploads_Processados[i] += stat.num_processados;
                            NumUploads_Problemas[i] += stat.num_problemas;
                        }
                    }

                    // busca media
                    if (stats_medias_MQTT != null)
                    {
                        List<StatsProcessamentoMQTTDominio> inside_media = stats_medias_MQTT.Where(item => (item.DataHora >= strDataIni && item.DataHora < strDataFim)).ToList();

                        if (inside_media != null)
                        {
                            if (inside_media.Count > 0)
                            {
                                NumUploads_Media[i] = inside_media[0].num_arquivos;
                            }
                        }
                    }

                    // proximo cinco minutos
                    strDataIni = strDataIni.AddMinutes(5);

                    // verifica valor maximo
                    if (NumUploads[i] > NumUploads_max)
                        NumUploads_max = NumUploads[i];

                    if (NumUploads_Media[i] > NumUploads_max)
                        NumUploads_max = NumUploads_Media[i];
                }

                NumUploads_max = NumUploads_max * 1.1;

                if (NumUploads_max < 1.0)
                {
                    NumUploads_max = 10.0;
                }

                ViewBag.NumUploads_max = NumUploads_max;

                ViewBag.NumUploads = NumUploads;
                ViewBag.NumUploads_Processados = NumUploads_Processados;
                ViewBag.NumUploads_Media = NumUploads_Media;
                ViewBag.NumUploads_Problemas = NumUploads_Problemas;
                ViewBag.DatasUploads = DatasUploads;

            }



            //
            // Estatisticas de Processamento - Mes
            //

            // mes atual
            ViewBag.MesAtual = string.Format("{0:y}", datahora_cookie);

            // grafico mes
            var NumArquivos_Mes = new double[33];
            var NumArquivos_Problemas_Mes = new double[33];
            var NumUploads_Mes = new double[33];
            var NumUploads_Problemas_Mes = new double[33];
            var Datas_Mes = new string[33];

            double NumArquivos_max_Mes = 0.0;

            // valores
            DateTime hoje_Mes = datahora_cookie;
            DateTime strDataIni_Mes = new DateTime(hoje_Mes.Year, hoje_Mes.Month, 1, 0, 10, 0);

            // volta 1 dia para a primeira barra
            strDataIni_Mes = strDataIni_Mes.AddDays(-1);

            // numero de dias do mes
            int NumDiasMes = DateTime.DaysInMonth(hoje_Mes.Year, hoje_Mes.Month);


            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // formata label
                Datas_Mes[i] = strDataIni_Mes.ToString("yyyy-MM-dd HH:mm:ss");

                // zera valor
                NumArquivos_Mes[i] = 0;
                NumArquivos_Problemas_Mes[i] = 0;
                NumUploads_Mes[i] = 0;
                NumUploads_Problemas_Mes[i] = 0;

                // le dia FTP
                StatsProcessamentoTotal stats_dia = statsMetodos.TotalDia(strDataIni_Mes);
                NumArquivos_Mes[i] = stats_dia.num_arquivos;
                NumArquivos_Problemas_Mes[i] = stats_dia.num_problemas;

                // le dia MQTT
                StatsProcessamentoTotal statsMQTT_dia = statsMQTTMetodos.TotalDia(strDataIni_Mes);
                NumUploads_Mes[i] = statsMQTT_dia.num_arquivos;
                NumUploads_Problemas_Mes[i] = statsMQTT_dia.num_problemas;

                // proximo dia
                strDataIni_Mes = strDataIni_Mes.AddDays(1);

                // verifica valor maximo
                if ( (NumArquivos_Mes[i] + NumUploads_Mes[i]) > NumArquivos_max_Mes)
                    NumArquivos_max_Mes = NumArquivos_Mes[i] + NumUploads_Mes[i];
            }

            NumArquivos_max_Mes = NumArquivos_max_Mes * 1.1;

            if (NumArquivos_max_Mes < 1.0)
            {
                NumArquivos_max_Mes = 10.0;
            }

            ViewBag.NumArquivos_max_Mes = NumArquivos_max_Mes;

            ViewBag.NumArquivos_Mes = NumArquivos_Mes;
            ViewBag.NumArquivos_Problemas_Mes = NumArquivos_Problemas_Mes;
            ViewBag.NumUploads_Mes = NumUploads_Mes;
            ViewBag.NumUploads_Problemas_Mes = NumUploads_Problemas_Mes;
            ViewBag.Datas_Mes = Datas_Mes;
            ViewBag.NumDiasMes = NumDiasMes;


            //
            // Estatisticas de Páginas de Acesso - Dia
            //

            // estatistica de página de acesso - hoje
            HistoricoPaginasVisitadasMetodos paginasMetodos = new HistoricoPaginasVisitadasMetodos();
            List<int> acessos = paginasMetodos.Dia(datahora_cookie);

            i = 0;

            if (acessos != null)
            {
                // grafico
                var NumAcessos = new double[24];
                var DatasAcessos = new string[24];

                double NumAcessos_max = 0.0;

                // valores
                DateTime hoje = datahora_cookie;
                DateTime strDataIni = new DateTime(hoje.Year, hoje.Month, hoje.Day, 1, 0, 0);

                // volta uma hora para a primeira barra
                strDataIni = strDataIni.AddHours(-1);

                for (i = 0; i < 24; i++)
                {
                    // formata label
                    DatasAcessos[i] = strDataIni.ToString("yyyy-MM-dd HH:mm:ss");

                    // zera valor
                    NumAcessos[i] = acessos[i];

                    // proximo hora
                    strDataIni = strDataIni.AddHours(1);

                    // verifica valor maximo
                    if (NumAcessos[i] > NumAcessos_max && (i > 0 && i < 23))
                    {
                        NumAcessos_max = NumAcessos[i];
                    }
                }

                NumAcessos_max = NumAcessos_max * 1.1;

                if (NumAcessos_max < 1.0)
                {
                    NumAcessos_max = 10.0;
                }

                ViewBag.NumAcessos_max = NumAcessos_max;

                ViewBag.NumAcessos = NumAcessos;
                ViewBag.DatasAcessos = DatasAcessos;
            }


            //
            // Estatisticas de Páginas de Acesso - Mes
            //

            // grafico mes
            var NumAcessos_Mes = new double[33];

            double NumAcessos_max_Mes = 0.0;

            // valores
            strDataIni_Mes = new DateTime(hoje_Mes.Year, hoje_Mes.Month, 1, 0, 10, 0);

            // volta 1 dia para a primeira barra
            strDataIni_Mes = strDataIni_Mes.AddDays(-1);

            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // zera valor
                NumAcessos_Mes[i] = 0;

                // le dia FTP
                NumAcessos_Mes[i] = paginasMetodos.TotalDia(strDataIni_Mes);

                // proximo dia
                strDataIni_Mes = strDataIni_Mes.AddDays(1);

                // verifica valor maximo
                if (NumAcessos_Mes[i] > NumAcessos_max_Mes && (i > 0 && i < (NumDiasMes + 2) - 1))
                {
                    NumAcessos_max_Mes = NumAcessos_Mes[i];
                }
            }

            NumAcessos_max_Mes = NumAcessos_max_Mes * 1.1;

            if (NumAcessos_max_Mes < 1.0)
            {
                NumAcessos_max_Mes = 10.0;
            }

            ViewBag.NumAcessos_max_Mes = NumAcessos_max_Mes;

            ViewBag.NumAcessos_Mes = NumAcessos_Mes;

            return;
        }

        // GET: Recebidos
        public ActionResult Recebidos()
        {
            // tela cheia
            ViewBag._DashboardTelaCheia = true;
            CookieStore.SalvaCookie_Bool("DashboardTelaCheia", true);

            // calcula
            Calc_Recebidos();

            return View();
        }

        // GET: Atualizar
        public PartialViewResult _Recebidos_Atualizar()
        {
            // calcula
            Calc_Recebidos();

            return PartialView();
        }

        public void Calc_Recebidos()
        {
            //
            // Arquivos Recebidos
            //

            // caminho Recebidos
            string CaminhoRecebidos = ConfigurationManager.AppSettings["Recebidos"];

            // numero de arquivos em Recebidos
            string[] list = Directory.GetFiles(CaminhoRecebidos, "*.*");
            ViewBag.ArquivosRecebidos = list.Length;


            //
            // Status de processos
            //

            // verifica Broker (Mosquitto)
            //string aProcName = "mosquitto";

            // verifica Broker (Artemis)
            bool status_Broker = false;
            string aProcName = "artemis-broker-0.0.0.0";
            if (verificarServicoAtivo(aProcName))
            {
                // serviço running
                status_Broker = true;
            }

            ViewBag.status_Broker = status_Broker;

            // verifica SmartMQTT
            bool status_SmartMQTT = false;
            aProcName = "SmartMQTT";
            if (verificarProcessoAtivo(aProcName))
            {
                // programa em execução
                status_SmartMQTT = true;
            }
            ViewBag.status_SmartMQTT = status_SmartMQTT;

            // API Artemis Broker - listAllConsumers
            ArtemisBroker artemis = new ArtemisBroker();
            bool status_SmEnergy = artemis.Consumer_Conectado("SmEnergy");
            bool status_SmUpload = artemis.Consumer_Conectado("SmUpload");
            bool status_SmUpdate = artemis.Consumer_Conectado(ConfigurationManager.AppSettings["SmUpdate_Topico_Subscribe"]);

            ViewBag.status_SmEnergy = status_SmEnergy;
            ViewBag.status_SmUpload = status_SmUpload;
            ViewBag.status_SmUpdate = status_SmUpdate;

            return;
        }
    }
}