﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using SmartEnergyLib.Cripto;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public class MigracaoController : Controller
    {
        // GET: Migracao
        public ActionResult Migracao()
        {
            // ajuda
            ViewBag.PaginaAjuda = "Contato";

            return View();
        }


        public ActionResult Migracao_Medicoes1()
        {
            // ajuda
            ViewBag.PaginaAjuda = "Contato";

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

        /*
            // percorre medicoes
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicaoMetodos.ListarTodos();

            foreach (MedicoesDominio medicao in medicoes)
            {
                medicao.IDCicloMes = 0;
                medicao.IDRegraICMS = 0;

                if(medicao.IDSubgrupo >= 0 && medicao.IDSubgrupo <= 5)
                {
                    medicao.IDEstruturaTarifaria = 0;
                    medicao.IDTipoSubgrupo = medicao.IDSubgrupo + 1;
                }

                if (medicao.IDSubgrupo >= 6 && medicao.IDSubgrupo <= 8)
                {
                    medicao.IDEstruturaTarifaria = 1;
                    medicao.IDTipoSubgrupo = medicao.IDSubgrupo - 2;
                }

                if (medicao.IDSubgrupo >= 9 && medicao.IDSubgrupo <= 12)
                {
                    medicao.IDEstruturaTarifaria = 2;
                    medicao.IDTipoSubgrupo = medicao.IDSubgrupo - 5;
                }

                medicaoMetodos.Salvar(medicao);
            }
       */
 
            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }



        public ActionResult Migracao_Medicoes2()
        {
            // ajuda
            ViewBag.PaginaAjuda = "Contato";

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

        /*
            // percorre medicoes
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicaoMetodos.ListarTodos();

            foreach (MedicoesDominio medicao in medicoes)
            {
                // usa default
                medicao.IDCalculoICMS = 0;
                medicao.InicioP = "18:00";
                medicao.FimP = "21:00";
                medicao.DomP = false; medicao.SegP = true; medicao.TerP = true; medicao.QuaP = true; medicao.QuiP = true; medicao.SexP = true; medicao.SabP = false; medicao.FerP = false;
                medicao.InicioPC = "00:00";
                medicao.FimPC = "06:00";
                medicao.DomPC = true; medicao.SegPC = true; medicao.TerPC = true; medicao.QuaPC = true; medicao.QuiPC = true; medicao.SexPC = true; medicao.SabPC = true; medicao.FerPC = true;
                medicao.IDEstado = 26;

                // gateway
                int IDGateway = 0;

                // verifica se energia
                if (medicao.IDTipoMedicao == 0)
                {
                    // gateway
                    IDGateway = medicao.IDGateway;
                }

                // verifica se formula
                if (medicao.IDTipoMedicao == 1)
                {
                    // pega primeira medicao da formula
                    int primeira_medicao = medicaoMetodos.PrimeiraMedicao(medicao.Formula);

                    if( primeira_medicao > 0 )
                    {
                        MedicoesDominio medicao_formula = medicaoMetodos.ListarPorId(primeira_medicao);

                        if( medicao_formula != null )
                        {
                            // gateway
                            IDGateway = medicao_formula.IDGateway;
                        }
                    }
                }

                // verifica se encontrou gateway
                if (IDGateway > 0)
                {
                    // gateway
                    GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
                    GatewaysDominio gateway = gatewayMetodos.ListarPorId(IDGateway);

                    if( gateway != null )
                    {
                        medicao.IDGateway = gateway.IDGateway;
                        medicao.IDCalculoICMS = gateway.IDCalculoICMS;
                        medicao.InicioP = gateway.InicioP;
                        medicao.FimP = gateway.FimP;
                        medicao.DomP = gateway.DomP; medicao.SegP = gateway.SegP; medicao.TerP = gateway.TerP; medicao.QuaP = gateway.QuaP; medicao.QuiP = gateway.QuiP; medicao.SexP = gateway.SexP; medicao.SabP = gateway.SabP; medicao.FerP = gateway.FerP;
                        medicao.InicioPC = gateway.InicioPC;
                        medicao.FimPC = gateway.FimPC;
                        medicao.DomPC = gateway.DomPC; medicao.SegPC = gateway.SegPC; medicao.TerPC = gateway.TerPC; medicao.QuaPC = gateway.QuaPC; medicao.QuiPC = gateway.QuiPC; medicao.SexPC = gateway.SexPC; medicao.SabPC = gateway.SabPC; medicao.FerPC = gateway.FerPC;
                        medicao.IDEstado = gateway.IDEstado;
                    }
                }

                medicaoMetodos.Salvar(medicao);
            }
        */

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        public ActionResult Migracao_Medicoes3()
        {
            // ajuda
            ViewBag.PaginaAjuda = "Contato";

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };


            // percorre medicoes
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicaoMetodos.ListarTodos();

            foreach (MedicoesDominio medicao in medicoes)
            {
                // principal
                medicao.IDCategoriaMedicao = 0;     

                // verifica se numero da medição interna é diferente de zero e se é energia elétrica
                if(medicao.NumMedGateway != 0 && medicao.IDTipoMedicao == 0)
                {
                    // setor
                    medicao.IDCategoriaMedicao = 1;     
                }

                medicaoMetodos.Salvar(medicao);
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }



        public ActionResult Migracao_Medicoes4()
        {
            // ajuda
            ViewBag.PaginaAjuda = "Contato";

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            /*
             * 
            // percorre medicoes
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicaoMetodos.ListarTodos();

            foreach (MedicoesDominio medicao in medicoes)
            {
                // verifica se é estação meteorológica
                if (medicao.IDTipoMedicao == TIPOS_MEDICAO.meteorologia)
                {
                    // medição meteorologia já usa IDCidade, não precisa atualizar
                    continue;
                }

                // gateway
                int IDGateway = medicao.IDGateway;

                // verifica se formula
                if (medicao.IDTipoMedicao == TIPOS_MEDICAO.formula || medicao.IDTipoMedicao == TIPOS_MEDICAO.utilidades_formula || medicao.IDTipoMedicao == TIPOS_MEDICAO.ea_formula)
                {
                    // pega primeira medicao da formula
                    int primeira_medicao = medicaoMetodos.PrimeiraMedicao(medicao.Formula);

                    if (primeira_medicao > 0)
                    {
                        MedicoesDominio medicao_formula = medicaoMetodos.ListarPorId(primeira_medicao);

                        if (medicao_formula != null)
                        {
                            // gateway
                            IDGateway = medicao_formula.IDGateway;
                        }
                    }
                }

                // verifica se encontrou gateway
                if (IDGateway > 0)
                {
                    // gateway
                    GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
                    GatewaysDominio gateway = gatewayMetodos.ListarPorId(IDGateway);

                    if (gateway != null)
                    {
                        // le empresa desta gateway
                        EmpresasMetodos empresaMetodos = new EmpresasMetodos();
                        EmpresasDominio empresa = empresaMetodos.ListarPorId(gateway.IDEmpresa);

                        if (empresa != null)
                        {
                            // cidade e estado
                            medicao.IDGateway = gateway.IDGateway;
                            medicao.IDEstado = empresa.IDEstado;
                            medicao.IDCidade = empresa.IDCidade;

                            // atualiza
                            medicaoMetodos.Atualizar_Cidade_Estado(medicao.IDMedicao, medicao.IDEstado, medicao.IDCidade);
                        }
                    }
                }
            }
            */

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }



        public ActionResult Migracao_Contratos()
        {
            // ajuda
            ViewBag.PaginaAjuda = "Contato";

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

        /*
            // percorre medicoes
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicaoMetodos.ListarTodos();

            foreach (MedicoesDominio medicao in medicoes)
            {
                // verifica se contrato fixo
                if( medicao.IDRegraContrato == 0 )
                {
                    // historico de contrato
                    HistoricoContratosMetodos contratoMetodos = new HistoricoContratosMetodos();
                    HistoricoContratosDominio contrato = new HistoricoContratosDominio();

                    // preenche contrato
                    contrato.IDContrato = 0;
                    contrato.IDCliente = medicao.IDCliente;
                    contrato.IDMedicao = medicao.IDMedicao;
                    contrato.Data = new DateTime(2000,1,1,0,0,0);
                    contrato.ContratoDemP = medicao.ContratoDemPU;
                    contrato.ContratoDemFP = medicao.ContratoDemFPU;

                    // insere contrato
                    contratoMetodos.Salvar(contrato);
                }
            }
        */
          
            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }



        public ActionResult Migracao_ICMS()
        {
            // ajuda
            ViewBag.PaginaAjuda = "Contato";

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

        /*
            // percorre medicoes
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicaoMetodos.ListarTodos();

            foreach (MedicoesDominio medicao in medicoes)
            {
                // verifica se energia
                if (medicao.IDTipoMedicao != 0 && medicao.IDTipoMedicao != 1)
                    continue;

                // pega valor do ICMS do estado
                ICMSMetodos icmsMetodos = new ICMSMetodos();
                List<ICMSDominio> icms = icmsMetodos.ListarTodos(medicao.IDEstado);

                double valorICMS = 18.0;

                if( icms != null )
                {
                    if( icms.Count > 0 )
                    {
                        valorICMS = icms[0].valorICMS;
                    }
                }

                // historico de ICMS
                HistoricoICMSMetodos historicoMetodos = new HistoricoICMSMetodos();
                HistoricoICMSDominio historico = new HistoricoICMSDominio();

                // preenche historico
                historico.IDICMS = 0;
                historico.IDCliente = medicao.IDCliente;
                historico.IDMedicao = medicao.IDMedicao;
                historico.Data = new DateTime(2000, 1, 1, 0, 0, 0);
                historico.valorICMS = valorICMS;

                // insere contrato
                historicoMetodos.Salvar(historico);
            }
        */
          
            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        public ActionResult Migracao_Usuarios()
        {
            // ajuda
            ViewBag.PaginaAjuda = "Contato";

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

        /*
            // cria tabela
            UsuarioMedicaoMetodos usuarioMedicaoMetodos = new UsuarioMedicaoMetodos();
            usuarioMedicaoMetodos.CriarTabela();

            // apaga todos
            usuarioMedicaoMetodos.ExcluirTodos();

            // usuarios
            UsuarioMetodos usuariosMetodos = new UsuarioMetodos();
            List<UsuarioDominio> usuarios = usuariosMetodos.ListarTodos();

            foreach(UsuarioDominio usuario in usuarios)
            {
                // verifica se administrador GESTAL
                if (usuario.IDTipoAcesso == 0 || usuario.IDTipoAcesso == 4 || usuario.IDTipoAcesso == 5 || usuario.IDTipoAcesso == 6)
                {


                }

                // verifica se consultor
                if (usuario.IDTipoAcesso == 3)
                {


                }

                // verifica se cliente
                if (usuario.IDTipoAcesso == 1 || usuario.IDTipoAcesso == 2)
                {
                    // cria lista de medicoes habilitadas no usuario - ConfigMed
                    List<int> ConfigMedList = null;
                    List<int> ConfigMedListEmail = null;

                    // copia medicoes habilitadas na lista
                    if (!String.IsNullOrEmpty(usuario.ConfigMed))
                    {
                        ConfigMedList = usuario.ConfigMed.Split('/')
                            .Select(possibleIntegerAsString =>
                            {
                                int parsedInteger = 0;
                                bool isInteger = int.TryParse(possibleIntegerAsString, out parsedInteger);
                                return new { isInteger, parsedInteger };
                            })
                            .Where(tryParseResult => tryParseResult.isInteger)
                            .Select(tryParseResult => tryParseResult.parsedInteger)
                            .ToList();
                    }

                    // copia medicoes habilitadas para email na lista
                    if (!String.IsNullOrEmpty(usuario.ConfigMedEmail))
                    {
                        ConfigMedListEmail = usuario.ConfigMedEmail.Split('/')
                            .Select(possibleIntegerAsString =>
                            {
                                int parsedInteger = 0;
                                bool isInteger = int.TryParse(possibleIntegerAsString, out parsedInteger);
                                return new { isInteger, parsedInteger };
                            })
                            .Where(tryParseResult => tryParseResult.isInteger)
                            .Select(tryParseResult => tryParseResult.parsedInteger)
                            .ToList();
                    }

                    // verifica se tem medicoes habilitadas
                    if (ConfigMedList != null)
                    {
                        // percorre lista de medicoes habilitadas no usuario
                        foreach (int IDMedicao in ConfigMedList)
                        {
                            // le cliente e gateway da medicao
                            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                            MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);

                            if (medicao != null)
                            {
                                // preenche campos
                                UsuarioMedicaoDominio usuarioMedicao = new UsuarioMedicaoDominio();

                                usuarioMedicao.IDUsuario = usuario.IDUsuario;
                                usuarioMedicao.IDCliente = medicao.IDCliente;
                                usuarioMedicao.IDMedicao = IDMedicao;
                                usuarioMedicao.IDGateway = medicao.IDGateway;

                                usuarioMedicao.RelatDiario = false;
                                usuarioMedicao.RelatSemanal = false;
                                usuarioMedicao.RelatMensal = false;
                                usuarioMedicao.RelatRanking = false;
                                usuarioMedicao.MsgGeren = false;
                                usuarioMedicao.MsgProcessa = false;

                                // verifica se tem medicoes habilitadas para email
                                if (ConfigMedListEmail != null)
                                {
                                    // verifica se medicao esta presente
                                    if (ConfigMedListEmail.Contains(IDMedicao))
                                    {
                                        // verifico os flags de relatorio periodico
                                        if (usuario.DemAtv == 1 || usuario.FatPot == 1 || usuario.Consumo == 1 || usuario.Supervisao == 1)
                                        {
                                            // diario
                                            if (usuario.Diario == 1)
                                                usuarioMedicao.RelatDiario = true;

                                            // semanal
                                            if (usuario.Semanal == 1)
                                                usuarioMedicao.RelatSemanal = true;

                                            // mensal
                                            if (usuario.Mensal == 1)
                                                usuarioMedicao.RelatMensal = true;
                                        }

                                        // verifico o flag de relatorio ranking
                                        if (usuario.Ranking == 1)
                                        {
                                            // ranking
                                            usuarioMedicao.RelatRanking = true;
                                        }

                                        // verifico os flags de mensagens de gerenciamento
                                        if (usuario.MsgCliMed_Dem == 1 || usuario.MsgCliMed_FatPot == 1 || usuario.MsgCliMed_Cons == 1)
                                        {
                                            // mensagens de gerenciamento proveniente da medicao (dem alta, baixa, fat pot, cons alto, baixo)
                                            usuarioMedicao.MsgGeren = true;
                                        }

                                        // verifico os flags de processamento
                                        if (usuario.MsgCliEve_FEner == 1 || usuario.MsgCliEve_RepDem == 1 || usuario.MsgCliEve_Alarm == 1)
                                        {
                                            // mensagens de gerenciamento proveniente do processamento dos arquivos recebidos
                                            usuarioMedicao.MsgProcessa = true;
                                        }
                                    }
                                }

                                // salva
                                usuarioMedicaoMetodos.Salvar(usuarioMedicao);
                            }
                        }
                    }
                }
            }
        */
          
            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        public ActionResult Migracao_Tarifas()
        {
            // ajuda
            ViewBag.PaginaAjuda = "Contato";

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

        /*
            // apaga todas as tarifas na tabela nova
            Tarifas_AZMetodos tarifaAZMetodos = new Tarifas_AZMetodos();
            tarifaAZMetodos.ExcluirTodos();

            Tarifas_VDMetodos tarifaVDMetodos = new Tarifas_VDMetodos();
            tarifaVDMetodos.ExcluirTodos();

            Tarifas_CVMetodos tarifaCVMetodos = new Tarifas_CVMetodos();
            tarifaCVMetodos.ExcluirTodos();

            // le tipos distribuidora
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposDistribuidora = listatiposMetodos.ListarTodos("TipoDistribuidora", false);

            // percorre distribuidoras
            foreach (ListaTiposDominio distribuidora in listatiposDistribuidora)
            {
                // percorre subgrupos
                int subgrupo;

                // leio tarifas, datas e resolucoes do AZ_A1 (sera a referencia)
                List<Tarifas_AZDominio> tarifas_AZ_A1 = tarifaAZMetodos.ListarPorIdAntigo(distribuidora.ID, 1);

                // verifica se existe
                if( tarifas_AZ_A1 != null )
                {
                    // percorre tarifas referencia
                    foreach (Tarifas_AZDominio tarifa_AZ_A1 in tarifas_AZ_A1)
                    {
                        //
                        // AZ
                        //

                        // subgrupos A1 / A2 / A3 / A3a / A4 / AS
                        for (subgrupo = 1; subgrupo <= 6; subgrupo++)
                        {
                            // le tarifa da resolucao na tabela antiga
                            Tarifas_AZDominio tarifa = tarifaAZMetodos.ListarPorResolucaoAntigo(distribuidora.ID, subgrupo, tarifa_AZ_A1.Resolucao);

                            // salva na tabela nova
                            if (tarifa != null)
                            {
                                // usa a data de referencia (pois ocorreu bug e tarifas estao com data errada)
                                tarifa.Data = tarifa_AZ_A1.Data;

                                // insere
                                tarifaAZMetodos.Salvar(tarifa);
                            }
                        }

                        //
                        // VD
                        //

                        // subgrupos A3a / A4 / AS
                        for (subgrupo = 4; subgrupo <= 6; subgrupo++)
                        {
                            // le tarifa da resolucao na tabela antiga
                            Tarifas_VDDominio tarifa = tarifaVDMetodos.ListarPorResolucaoAntigo(distribuidora.ID, subgrupo, tarifa_AZ_A1.Resolucao);

                            // salva na tabela nova
                            if (tarifa != null)
                            {
                                // usa a data de referencia (pois ocorreu bug e tarifas estao com data errada)
                                tarifa.Data = tarifa_AZ_A1.Data;

                                // insere
                                tarifaVDMetodos.Salvar(tarifa);
                            }
                        }

                        //
                        // CV
                        //

                        // subgrupos A3a / A4 / AS / B3
                        for (subgrupo = 4; subgrupo <= 7; subgrupo++)
                        {
                            // le tarifa da resolucao na tabela antiga
                            Tarifas_CVDominio tarifa = tarifaCVMetodos.ListarPorResolucaoAntigo(distribuidora.ID, subgrupo, tarifa_AZ_A1.Resolucao);

                            // salva na tabela nova
                            if (tarifa != null)
                            {
                                // usa a data de referencia (pois ocorreu bug e tarifas estao com data errada)
                                tarifa.Data = tarifa_AZ_A1.Data;

                                // insere
                                tarifaCVMetodos.Salvar(tarifa);
                            }
                        }
                    }
                }
            }
        */
          
            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }





        public ActionResult Migracao_ApagaUsuariosMedicao()
        {
            // ajuda
            ViewBag.PaginaAjuda = "Contato";

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

        /*
            // periodo
            DateTime dateFim = DateTime.Now;
            DateTime dateIni = dateFim.AddDays(-60);

            // executa operacao
            UsuarioEventoMetodos usuarioEventoMetodos = new UsuarioEventoMetodos();
            List<UsuarioEventoDominio> eventos = usuarioEventoMetodos.ListarPorQuemLogou(dateIni, dateFim);

            // lista de usuarios
            UsuarioMetodos usuariosMetodos = new UsuarioMetodos();
            List<UsuarioDominio> usuarios = usuariosMetodos.ListarTodos();

            List<UsuarioLista> usuariosLista = new List<UsuarioLista>();

            UsuarioMedicaoMetodos usuariosMedicaoMetodos = new UsuarioMedicaoMetodos();

            // cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();

            // encontro usuarios e ocorrencias de login
            foreach (UsuarioEventoDominio evento in eventos)
            {
                // verifica se login
                if (evento.Evento == 0)
                {
                    bool existe = false;

                    // procura usuario na lista
                    foreach (UsuarioLista user in usuariosLista)
                    {
                        if (user.IDUsuario == evento.IDUsuario)
                        {
                            // ja existe, soma no contador
                            user.contador++;
                            existe = true;
                            break;
                        }
                    }

                    if (!existe)
                    {
                        // nao existe, coloco na lista
                        UsuarioLista usuarioLista = new UsuarioLista();

                        usuarioLista.contador = 1;
                        usuarioLista.IDUsuario = evento.IDUsuario;
                        usuariosLista.Add(usuarioLista);
                    }
                }
            }

            // percorre lista de usuarios 
            foreach (UsuarioDominio usuario in usuarios)
            {
                // verifica se usuario logou
                bool logou = false;

                // percorre lista de usuarios encontrados
                foreach (UsuarioLista user in usuariosLista)
                {
                    // verifica se tem na lista
                    if( user.IDUsuario == usuario.IDUsuario)
                    {
                        logou = true;
                        break;
                    }
                }

                // verifica se NAO logou
                if(!logou)
                {
                    // apaga
                    usuariosMedicaoMetodos.Excluir(usuario.IDUsuario);
                }
            }
        */
          
            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        public ActionResult Migracao_AtualizaUsuariosMedicao()
        {
            // ajuda
            ViewBag.PaginaAjuda = "Contato";

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

        /*
            // periodo
            DateTime dateFim = DateTime.Now;
            DateTime dateIni = dateFim.AddDays(-60);

            // executa operacao
            UsuarioEventoMetodos usuarioEventoMetodos = new UsuarioEventoMetodos();
            List<UsuarioEventoDominio> eventos = usuarioEventoMetodos.ListarPorQuemLogou(dateIni, dateFim);

            // lista de usuarios
            UsuarioMetodos usuariosMetodos = new UsuarioMetodos();
            List<UsuarioDominio> usuarios = usuariosMetodos.ListarTodos();

            List<UsuarioLista> usuariosLista = new List<UsuarioLista>();

            UsuarioMedicaoMetodos usuariosMedicaoMetodos = new UsuarioMedicaoMetodos();

            // cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();

            // encontro usuarios e ocorrencias de login
            foreach (UsuarioEventoDominio evento in eventos)
            {
                // verifica se login
                if (evento.Evento == 0)
                {
                    bool existe = false;

                    // procura usuario na lista
                    foreach (UsuarioLista user in usuariosLista)
                    {
                        if (user.IDUsuario == evento.IDUsuario)
                        {
                            // ja existe, soma no contador
                            user.contador++;
                            existe = true;
                            break;
                        }
                    }

                    if (!existe)
                    {
                        // nao existe, coloco na lista
                        UsuarioLista usuarioLista = new UsuarioLista();

                        usuarioLista.contador = 1;
                        usuarioLista.IDUsuario = evento.IDUsuario;
                        usuariosLista.Add(usuarioLista);
                    }
                }
            }

            // percorre lista de usuarios 
            foreach (UsuarioDominio usuario in usuarios)
            {
                // verifica se usuario logou
                bool logou = false;

                // verifica se tem na lista
                if (usuario.IDTipoAcesso == 1 || usuario.IDTipoAcesso == 2)
                {
                    // percorre lista de usuarios encontrados
                    foreach (UsuarioLista user in usuariosLista)
                    {
                        // verifica se tem na lista
                        if (user.IDUsuario == usuario.IDUsuario)
                        {
                            logou = true;
                            break;
                        }
                    }
                }

                // verifica se logou
                if (logou)
                {
                    // atualiza
                    usuariosMedicaoMetodos.AtualizarUsuariosMedicoes(usuario.IDUsuario);
                }
            }
        */

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }




        public ActionResult Migracao_CriarEmpresaPrincipal()
        {
            // ajuda
            ViewBag.PaginaAjuda = "Contato";

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

        /*
            // cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> clientes = clienteMetodos.ListarTodos();

            // percorro clientes
            foreach (ClientesDominio cliente in clientes)
            {
                // leio empresas do cliente
                EmpresasMetodos empresaMetodos = new EmpresasMetodos();
                List<EmpresasDominio> empresas = empresaMetodos.ListarPorIDCliente(cliente.IDCliente);

                if (empresas != null)
                {
                    // verifica se tem pelo menos uma empresa
                    if (empresas.Count > 0)
                    {
                        // crio empresa
                        EmpresasDominio empresa = new EmpresasDominio();

                        // copio primeira empresa
                        empresa = empresas[0];

                        // mudo tipo para principal
                        empresa.IDTipoEmpresa = TIPO_EMPRESA.Principal;

                        // altero empresa
                        empresaMetodos.Salvar(empresa);
                    }
                }
            }
            */
            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }




        public ActionResult Migracao_UsuariosView()
        {
            // ajuda
            ViewBag.PaginaAjuda = "Contato";

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            /*
            //
            // COMERC
            // 
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> clientes = clienteMetodos.ListarPorIDConsultor(CLIENTES_ESPECIAIS.COMERC);

            UsuarioMetodos usuariosMetodos = new UsuarioMetodos();
            List<UsuarioDominio> usuarios = new List<UsuarioDominio>();

            if (clientes != null)
            {
                // percorre clientes do gestor
                foreach(ClientesDominio cliente in clientes)
                {
                    // usuarios
                    usuarios = usuariosMetodos.ListarPorIDCliente(cliente.IDCliente);

                    if (usuarios != null)
                    {
                        // percorre usuarios do cliente
                        foreach(UsuarioDominio usuario in usuarios)
                        {
                            // modifica
                            usuario.View_Financas = 0;
                            usuario.View_Rateio = 0;

                            if (usuario.IDTipoAcesso == TIPO_ACESSO.CLIENTE_OPER)
                            {
                                usuario.View_Analises = 0;
                                usuario.View_Metas = 0;
                                usuario.View_Configuracao = 0;
                            }

                            usuario.View_KPI = 1;
                            usuario.View_Ranking = 1;

                            // salva usuário
                            usuariosMetodos.Salvar(usuario);
                        }
                    }
                }
            }

            //
            // BRMALLS
            // 
            clientes = clienteMetodos.ListarPorIDConsultor(CLIENTES_ESPECIAIS.BRMALLS);

            if (clientes != null)
            {
                // percorre clientes do gestor
                foreach (ClientesDominio cliente in clientes)
                {
                    // usuarios
                    usuarios = usuariosMetodos.ListarPorIDCliente(cliente.IDCliente);

                    if (usuarios != null)
                    {
                        // percorre usuarios do cliente
                        foreach (UsuarioDominio usuario in usuarios)
                        {
                            // modifica
                            usuario.View_Financas = 0;

                            if (usuario.IDTipoAcesso == TIPO_ACESSO.CLIENTE_OPER)
                            {
                                usuario.View_Analises = 0;
                                usuario.View_Metas = 0;
                                usuario.View_Configuracao = 0;
                            }

                            usuario.View_Rateio = 1;
                            usuario.View_KPI = 1;
                            usuario.View_Ranking = 1;

                            // salva usuário
                            usuariosMetodos.Salvar(usuario);
                        }
                    }
                }
            }

            //
            // RETHA
            // 
            clientes = clienteMetodos.ListarPorIDConsultor(CLIENTES_ESPECIAIS.RETHA);

            if (clientes != null)
            {
                // percorre clientes do gestor
                foreach (ClientesDominio cliente in clientes)
                {
                    // usuarios
                    usuarios = usuariosMetodos.ListarPorIDCliente(cliente.IDCliente);

                    if (usuarios != null)
                    {
                        // percorre usuarios do cliente
                        foreach (UsuarioDominio usuario in usuarios)
                        {
                            // modifica
                            usuario.View_Financas = 0;
                            usuario.View_Rateio = 0;
                            usuario.View_Ranking = 0;
                            usuario.View_Metas = 0;

                            if (usuario.IDTipoAcesso == TIPO_ACESSO.CLIENTE_OPER)
                            {
                                usuario.View_Analises = 0;
                                usuario.View_Configuracao = 0;
                            }

                            usuario.View_KPI = 1;

                            // salva usuário
                            usuariosMetodos.Salvar(usuario);
                        }
                    }
                }
            }

            //
            // CPFL
            // 
            clientes = clienteMetodos.ListarPorIDConsultor(CLIENTES_ESPECIAIS.CPFL);

            if (clientes != null)
            {
                // percorre clientes do gestor
                foreach (ClientesDominio cliente in clientes)
                {
                    // usuarios
                    usuarios = usuariosMetodos.ListarPorIDCliente(cliente.IDCliente);

                    if (usuarios != null)
                    {
                        // percorre usuarios do cliente
                        foreach (UsuarioDominio usuario in usuarios)
                        {
                            // modifica
                            usuario.View_Financas = 0;
                            usuario.View_Analises = 0;
                            usuario.View_Metas = 0;
                            usuario.View_Configuracao = 0;
                            usuario.View_Rateio = 0;
                            usuario.View_KPI = 0;
                            usuario.View_Ranking = 0;

                            // salva usuário
                            usuariosMetodos.Salvar(usuario);
                        }
                    }
                }
            }

            //
            // ARAGUAIA
            //

            usuarios = usuariosMetodos.ListarPorIDCliente(CLIENTES_ESPECIAIS.ARAGUAIA);

            if (usuarios != null)
            {
                // percorre usuarios do cliente
                foreach (UsuarioDominio usuario in usuarios)
                {
                    // modifica
                    usuario.View_Financas = 0;
                    usuario.View_Rateio = 0;

                    if (usuario.IDTipoAcesso == TIPO_ACESSO.CLIENTE_OPER)
                    {
                        usuario.View_Analises = 0;
                        usuario.View_Metas = 0;
                        usuario.View_Configuracao = 0;
                    }

                    usuario.View_KPI = 1;
                    usuario.View_Ranking = 1;

                    // salva usuário
                    usuariosMetodos.Salvar(usuario);
                }
            }

            //
            // SERBOM
            //

            usuarios = usuariosMetodos.ListarPorIDCliente(CLIENTES_ESPECIAIS.SERBOM);

            if (usuarios != null)
            {
                // percorre usuarios do cliente
                foreach (UsuarioDominio usuario in usuarios)
                {
                    // modifica
                    usuario.View_Financas = 0;
                    usuario.View_Rateio = 0;
                    usuario.View_Analises = 0;
                    usuario.View_Metas = 0;
                    usuario.View_Configuracao = 0;
                    usuario.View_KPI = 0;
                    usuario.View_Ranking = 0;

                    // salva usuário
                    usuariosMetodos.Salvar(usuario);
                }
            }
            */
            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }




        public ActionResult Migracao_Contratos2()
        {
            // ajuda
            ViewBag.PaginaAjuda = "Contato";

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };
            /*
            // percorre medicoes
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicaoMetodos.ListarTodos();

            HistoricoContratosMetodos contratosMetodos = new HistoricoContratosMetodos();

            foreach (MedicoesDominio medicao in medicoes)
            {
                // verifica se energia eletrica
                if (medicao.IDTipoMedicao == 0 || medicao.IDTipoMedicao == 1)
                {
                    // leio contrato mais recente
                    HistoricoContratosDominio histContrato = contratosMetodos.ListarPorIDMedicaoMaisRecente(medicao.IDMedicao);

                    // atualiza contrato na medição
                    if (histContrato != null)
                    {
                        medicaoMetodos.Atualizar_ContratosDemanda(medicao.IDMedicao, histContrato.ContratoDemP, histContrato.ContratoDemFP);
                    }
                }
            }
            */
            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }






        public ActionResult Migracao_Usuarios_CtrEmailAdm()
        {
            // ajuda
            ViewBag.PaginaAjuda = "Contato";

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };
            /*
            // leio CtrEmailAdm
            CtrEmailAdmMetodos ctrEmailAdmMetodos = new CtrEmailAdmMetodos();
            List<CtrEmailAdmDominio> ctrEmailAdms = ctrEmailAdmMetodos.ListarTodos();

            if (ctrEmailAdms != null)
            {
                // percorre adms
                foreach (CtrEmailAdmDominio ctrEmailAdm in ctrEmailAdms)
                {
                    // leio usuario
                    UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
                    UsuarioDominio usuario = usuarioMetodos.ListarPorId(ctrEmailAdm.Administrador);

                    // verifica se existe
                    if (usuario != null)
                    {
                        // verifica se IDUsuario válido
                        if (usuario.IDUsuario > 0)
                        {
                            // copio lista de medições
                            usuario.ConfigGrupoEmail = ctrEmailAdm.Grupo;
                            usuario.ConfigUnidEmail = ctrEmailAdm.Unid;
                            usuario.ConfigMedEmail = ctrEmailAdm.Medicao;

                            // salva usuário para atualizar
                            usuarioMetodos.Salvar(usuario);
                        }
                        else
                        {
                            // não tem usuario, excluo admin
                            ctrEmailAdmMetodos.Excluir(ctrEmailAdm.Administrador);
                        }
                    }
                    else
                    {
                        // não tem usuario, excluo admin
                        ctrEmailAdmMetodos.Excluir(ctrEmailAdm.Administrador);
                    }
                }
            }
            */
            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }




        public ActionResult Migracao_Usuarios_UtilAnalCiclo()
        {
            // ajuda
            ViewBag.PaginaAjuda = "Contato";

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            /*
            // leio usuarios
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            List<UsuarioDominio> usuarios = usuarioMetodos.ListarTodos();

            if (usuarios != null)
            {
                // percorre usuários
                foreach (UsuarioDominio usuario in usuarios)
                {
                    // verifica se usuário quer relatório
                    if (usuario.Diario == 1 || usuario.Semanal == 1 || usuario.Mensal == 1)
                    {
                        if (usuario.DemAtv == 1)
                        {
                            // alterou flag
                            bool alterou_flag = false;

                            // lista de medições
                            List<int> ConfigMedList = null;

                            // copia medicoes para lista
                            if (!String.IsNullOrEmpty(usuario.ConfigMedEmail))
                            {
                                ConfigMedList = usuario.ConfigMedEmail.Split('/')
                                    .Select(possibleIntegerAsString =>
                                    {
                                        int parsedInteger = 0;
                                        bool isInteger = int.TryParse(possibleIntegerAsString, out parsedInteger);
                                        return new { isInteger, parsedInteger };
                                    })
                                    .Where(tryParseResult => tryParseResult.isInteger)
                                    .Select(tryParseResult => tryParseResult.parsedInteger)
                                    .ToList();
                            }

                            // verifica se tem medições
                            if (ConfigMedList != null)
                            {
                                // percorre medicoes
                                foreach (int IDMedicao in ConfigMedList)
                                {
                                    // le medicao
                                    MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                                    MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);

                                    if (medicao != null)
                                    {
                                        // verifica se utilidades / analógica / ciclometro
                                        if (medicao.IDTipoMedicao == TIPO_MEDICAO.UTILIDADES ||
                                            medicao.IDTipoMedicao == TIPO_MEDICAO.UTILIDADES_FORMULA ||
                                            medicao.IDTipoMedicao == TIPO_MEDICAO.ENTRADA_ANALOGICA ||
                                            medicao.IDTipoMedicao == TIPO_MEDICAO.CICLOMETRO)
                                        {
                                            // alterou flag
                                            alterou_flag = true;
                                        }
                                    }
                                }
                            }

                            // verifica se alterou flag
                            if (alterou_flag)
                            {
                                // seto flag
                                usuario.Util_Anal_Ciclo = 1;

                                // salva usuario
                                usuarioMetodos.Salvar(usuario);
                            }
                        }
                    }
                }
            }
            */

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        public ActionResult Migracao_SenhasUsadas()
        {
            // ajuda
            ViewBag.PaginaAjuda = "Contato";

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            /*
            // usuarios
            UsuarioMetodos usuariosMetodos = new UsuarioMetodos();
            List<UsuarioDominio> usuarios = usuariosMetodos.ListarTodos();

            foreach(UsuarioDominio usuario in usuarios)
            {
                // salva senha usada
                SenhasUsadasMetodos senhasUsadasMetodos = new SenhasUsadasMetodos();
                senhasUsadasMetodos.Inserir(usuario.IDUsuario, usuario.Senha);
            }
            */
            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }



        public ActionResult Migracao_PISCOFINS_Mes()
        {
            // ajuda
            ViewBag.PaginaAjuda = "Contato";

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            /*
            // percorre medicoes
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicaoMetodos.ListarTodos();

            foreach (MedicoesDominio medicao in medicoes)
            {
                // mês vigente
                medicao.PISCOFINS_Mes = 0;      

                // lê agente distribuidora
                AgentesDistribuidoraMetodos distribuidorasMetodos = new AgentesDistribuidoraMetodos();
                AgentesDistribuidoraDominio distribuidora = distribuidorasMetodos.ListarPorId(medicao.IDAgenteDistribuidora);

                if (distribuidora != null)
                {
                    if (distribuidora.PISCOFINS_Mes != 0)
                    {
                        // próximo vigente
                        medicao.PISCOFINS_Mes = 1;

                        // diferente do default, salvo medição
                        medicaoMetodos.Salvar(medicao);
                    }
                }
            }
            */
            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }



        public ActionResult Migracao_HorarioConsumo()
        {
            // ajuda
            ViewBag.PaginaAjuda = "Contato";

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            /*
            // le medicoes da analise de horario de consumo
            AnaliseHorarioConsGruposMedMetodos analiseGruposMetodos = new AnaliseHorarioConsGruposMedMetodos();
            List<AnaliseHorarioConsGruposMedDominio> analiseGrupoMed = analiseGruposMetodos.ListarTodos();

            // percorre medicoes
            foreach (AnaliseHorarioConsGruposMedDominio med in analiseGrupoMed)
            {
                // le configuracao da medicao
                MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                MedicoesDominio medicaoConf = medicaoMetodos.ListarPorId(med.IDMedicao);

                if (medicaoConf != null)
                {
                    // verifica se não é automático ou se tem demanda residual
                    if (med.DemandaResidual > 0.0 && !med.CalcResidualAutoCheck)
                    {
                        // atualiza medição com parametros da analise de horario de consumo
                        medicaoMetodos.Atualizar_HorarioConsumo(med.IDMedicao, med.HoraIniRef, med.HoraFimRef, med.DemandaResidual);
                    }

                    // verifica se não tem demanda residual
                    if (med.DemandaResidual == 0.0)
                    {
                        // copia configuração Horário de Consumo
                        med.HoraIniRef = DateTime.ParseExact(medicaoConf.Funcionamento_Inicio, "HH:mm", CultureInfo.InvariantCulture); 
                        med.HoraFimRef = DateTime.ParseExact(medicaoConf.Funcionamento_Fim, "HH:mm", CultureInfo.InvariantCulture); 
                        med.DemandaResidual = medicaoConf.Funcionamento_DemMin;

                        // atualiza parametros Demanda Residual e horario de consumo
                        analiseGruposMetodos.Atualiza_DemandaResidual(med.IDMedicao, med.HoraIniRef, med.HoraFimRef, med.DemandaResidual);
                    }
                }
            }
            */
            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }






        public ActionResult Migracao_EnderecoGateway()
        {
            // ajuda
            ViewBag.PaginaAjuda = "Contato";

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };


            // leio supervisao e configuracao da gateway
            SupervGatewaysMetodos gatewaysMetodos = new SupervGatewaysMetodos();
            List<SupervGatewaysDominio> gateways = gatewaysMetodos.ListarTodos(100);

            // percorre gateway
            foreach (SupervGatewaysDominio gateway in gateways)
            {
                // separa
                List<string> campos = gateway.StatusEq.Split(' ').ToList();

                if (campos != null)
                {
                    foreach (string campo in campos)
                    {
                        // verifica se endereço
                        int pos = campo.IndexOf("Ad=");

                        if (pos >= 0)
                        {
                            string str = campo.Substring(pos + 3);

                            if (str != null)
                            {
                                int endereco = 0;

                                if (int.TryParse(str, out endereco))
                                {
                                    if (endereco > 0 && endereco <= 255)
                                    {
                                        GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
                                        gatewayMetodos.AtualizaEndereco(gateway.IDGateway, endereco);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }








        public ActionResult Migracao_Usuarios_Criptografar()
        {
            // ajuda
            ViewBag.PaginaAjuda = "Contato";

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };


            // criptograda usuarios
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            usuarioMetodos.Criptografa();

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }






        private static void LogMessage(string msg, string arquivo = null)
        {
            // nome de arquivo padrao
            string nome_arquivo = "C:\\Temp\\Migracao_" + String.Format("{0:yyyyMMdd}", DateTime.Today) + ".txt";

            // verifica se tem nome de arquivo
            if (arquivo != null)
            {
                nome_arquivo = arquivo;
            }

            System.IO.StreamWriter sw = System.IO.File.AppendText(nome_arquivo);

            try
            {
                string logLine = System.String.Format(
                    "{0:G}: {1}", System.DateTime.Now, msg);
                sw.WriteLine(logLine);
                sw.Flush();
            }
            finally
            {
                sw.Close();
            }
        }
    }
}