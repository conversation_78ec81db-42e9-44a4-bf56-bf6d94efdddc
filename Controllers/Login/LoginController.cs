﻿////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// [Classe] LoginController
//
// Funções de Login
//
// Login() [GET]                  : Página de Login Smart Energy
// Login() [POST]                 : Login de usuário
// LoginExterno() [POST]          : Login de usuário Externo acesso pelo 'login.asp' e páginas de clientes que possuam login personalidado
// LoginInterno() [GET]           : Login interno através do IDUsuario
// VerificaLogin()                : Verifica se existe usuário com o login e senha
// Logout() [POST]                : Executa o logout
// Bloqueio() [GET]               : Verificar bloqueio de usuario
// LimparCookies()                : Limpar cookies
// AlteraIdioma() [GET]           : Altera o Idioma
// EsqueciSenha() [POST]          : Envia a senha para o email do usuário
// SolicitarDemo() [POST]         : Envia formulário de solicitação de demo para GESTAL
// EnviaEmailErroLogin()          : Envia email de Erro de Login para GESTAL
// EnviaEmailErroSenha()          : Envia email de Erro de Senha para GESTAL
// EnviaEmailBloqueioLogin()      : Envia email Login Bloqueado para GESTAL
// EnviaEmailSenhaExpirou()       : Envia email Senha Expirou para usuário
// ConfirmarEmail() [GET]         : Confirmação do email
// EnviarConfirmacaoEmail() [GET] : Enviar confirmação de Email
//
// EMailTemplate()                : Template do email
// OnException()                  : Tratamento de exceções
// GenerateRandomString()         : Gera string com conteúdo randomico
// CleanUpBase64String()          : Limpa string com ocnteúdo randomico
// GenerateRandomString_Numbers() : Gera string com números randomico
// GetIPLocal()                   : Obtem IP Local
// GetIPCliente()                 : Obtem IP do Cliente
// CheckAddBinPath()              : Insere diretório BIN no ambiente
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Web.Hosting;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;
using SmartEnergy.Controllers.Login;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public class LoginController : Controller
    {
        // login
        private string nome_login = "";



        //
        // [GET] /Login/Login
        // Página de Login Smart Energy
        [AllowAnonymous]
        [OutputCache(NoStore = true, Duration = 0, VaryByParam = "None")] 
        public ActionResult Login(string returnUrl, int status = TIPO_STATUS_LOGIN.Sucesso)
        {
            //
            // Site em Manutenção
            //

            // verifica se site esta em manutenção
            // "false" = não está em manutenção
            // "true"  = está em manutenção
            string site_em_manutencao = ConfigurationManager.AppSettings["SiteEmManutencao"];
            string MensagemManutencao = ConfigurationManager.AppSettings["MensagemManutencao"];
            bool SiteEmManutencao = false;

            if (site_em_manutencao == "true")
            {
                // site em manutencao
                SiteEmManutencao = true;
            }

            ViewBag.SiteEmManutencao = SiteEmManutencao;
            ViewBag.MensagemManutencao = MensagemManutencao;


            // status para apresentar mensagens na tela de login
            ViewBag.ReturnUrl = returnUrl;
            ViewBag.Status = status;

            // inicialmente login ok
            int StatusLogin = TIPO_STATUS_LOGIN.Sucesso;

            // erro de login ou senha
            if (status == TIPO_STATUS_LOGIN.ErroLoginSenha)
            {
                StatusLogin = TIPO_STATUS_LOGIN.ErroLoginSenha;
            }

            // email confirmado
            if (status == TIPO_STATUS_LOGIN.EmailConfirmado)
            {
                StatusLogin = TIPO_STATUS_LOGIN.EmailConfirmado;
            }

            // copia status
            ViewBag.StatusLogin = StatusLogin;

            return View();
        }

        //
        // [POST] /Login/Login
        // Login de usuário
        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> Login(LoginViewModel model, string returnUrl)
        {
            //
            // Preparação
            //

            // log Junior
            nome_login = model.UserName;

            LogMessage_Junior("_____________________________________________________________________________________________________________________");
            LogMessage_Junior("Início");
            LogMessage_Junior("");

            // caminho a ser direcionado
            string caminho = "";
            int IDUsuario = 0;
            int StatusLogin = TIPO_STATUS_LOGIN.ErroLoginSenha;

            // usuário
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();

            //
            // Site em Manutenção
            //

            // verifica se site esta em manutenção
            // "false" = não está em manutenção
            // "true"  = está em manutenção
            string MensagemManutencao = ConfigurationManager.AppSettings["MensagemManutencao"];
            bool SiteEmManutencao = (ConfigurationManager.AppSettings["SiteEmManutencao"] == "true") ? true : false;

            ViewBag.SiteEmManutencao = SiteEmManutencao;
            ViewBag.MensagemManutencao = MensagemManutencao;

            //
            // Login
            //

            // verifica se entradas válidas
            if (ModelState.IsValid)
            {

                // log Junior
                LogMessage_Junior("Login 1");


                //
                // Verifica Recaptcha
                //
                // Google reCaptcha
                // https://www.google.com/recaptcha/admin 
                // <EMAIL> (Borges#190)
                //
                // SmartEnergyV2
                //

#if !DEBUG

                // chaves
                string PublicKey = "";
                string PrivateKey = "";
                reCaptcha.ReadReCaptchaKeys(ref PublicKey, ref PrivateKey);

                string response = Request.Form["g-recaptcha-response"];
                WebClient cliente = new WebClient();
                string resultado = cliente.DownloadString(string.Format("https://www.google.com/recaptcha/api/siteverify?secret={0}&response={1}", PrivateKey, response));
                JObject obj = JObject.Parse(resultado);

                var status = (bool)obj.SelectToken("success");

                if (!status)
                {
                    // erro senha invalida
                    ModelState.AddModelError("", "reCAPTCHA inválido");

                    // captcha inválido
                    StatusLogin = TIPO_STATUS_LOGIN.CaptchaInvalido;
                    ViewBag.StatusLogin = StatusLogin;

                    // recaptcha inválido
                    return View(model);
                }

#endif

                // log Junior
                LogMessage_Junior("Login 2");

                //
                // Verifica Login
                //
                int retorno = VerificaLogin(model, ref caminho, ref IDUsuario);

                // usuário
                UsuarioDominio usuario = usuarioMetodos.ListarPorId(IDUsuario);

                // log Junior
                LogMessage_Junior("Login 3");

                // login OK
                if (retorno == TIPO_ERRO_LOGIN.Sucesso)
                {
                    // salva pagina de retorno para logout
                    CookieStore.SalvaCookie_String("SiteInicio", model.SiteInicio);

                    // login OK
                    StatusLogin = TIPO_STATUS_LOGIN.Sucesso;
                    ViewBag.StatusLogin = StatusLogin;

                    // log Junior
                    LogMessage_Junior("Login 4");

                    // pagina inicial do Smart Energy
                    return Redirect(caminho);
                }

                // log Junior
                LogMessage_Junior("Login 5");

                // login conhecido e senha desconhecida
                if (retorno == TIPO_ERRO_LOGIN.ErroSenha)
                {
                    // erro senha invalida
                    if (usuario.TentativasLogin == 4)
                    {
                        ModelState.AddModelError("", "Senha Incorreta, resta uma tentativa");
                    }
                    else
                    {
                        ModelState.AddModelError("", string.Format("Senha Incorreta, restam {0} tentativas", 5 - usuario.TentativasLogin));
                    }

                    // erro senha invalida
                    StatusLogin = TIPO_STATUS_LOGIN.ErroLoginSenha;
                    ViewBag.StatusLogin = StatusLogin;

                    // envia email erro de senha para admin da GESTAL
                    await EnviaEmailErroSenha(model.UserName, model.Password);

                    // erro senha invalida
                    return View(model);
                }

                // consultor sem cliente
                if (retorno == TIPO_ERRO_LOGIN.ErroGestor)
                {
                    // erro configuração do gestor
                    ModelState.AddModelError("", "Erro na configuração do Gestor");

                    // consultor sem cliente
                    StatusLogin = TIPO_STATUS_LOGIN.ErroGestor;
                    ViewBag.StatusLogin = StatusLogin;

                    // consultor sem cliente, apresenta tela de login
                    return View(model);
                }

                // senha expirou
                if (retorno == TIPO_ERRO_LOGIN.SenhaExpirou)
                {
                    // envia email senha expirada
                    if (usuario != null)
                    {
                        await EnviaEmailSenhaExpirou(usuario.NomeUsuario, usuario.Senha, usuario.Email, usuario.ExpirarEm);
                    }

                    // senha expirou
                    ModelState.AddModelError("", @SmartEnergy.Resources.LoginTexts.LoginSenhaExpirou);

                    // senha expirou
                    StatusLogin = TIPO_STATUS_LOGIN.SenhaExpirou;
                    ViewBag.StatusLogin = StatusLogin;

                    // senha expirou, apresenta tela de login
                    return View(model);
                }

                // confirmação de email
                if (retorno == TIPO_ERRO_LOGIN.EmailConfirmacao)
                {
                    // confirmacao de email
                    ModelState.AddModelError("", @SmartEnergy.Resources.LoginTexts.LoginEmailConfirmacao);

                    // confirmacao de email
                    StatusLogin = TIPO_STATUS_LOGIN.EmailConfirmacao;
                    ViewBag.StatusLogin = StatusLogin;

                    // pagina login
                    return View(model);
                }

                // usuário bloqueado (financeiro)
                if (retorno == TIPO_ERRO_LOGIN.UsuarioBloqueadoFinanceiro)
                {
                    // envia email bloqueio
                    if (usuario != null)
                    {
                        await EnviaEmailBloqueioLogin(usuario.NomeUsuario, usuario.Login, IDUsuario);
                    }

                    // confirmacao de email
                    ModelState.AddModelError("", @SmartEnergy.Resources.LoginTexts.LoginUsuarioBloqueado);

                    // usuário bloqueado (financeiro)
                    StatusLogin = TIPO_STATUS_LOGIN.UsuarioBloqueadoFinanceiro;
                    ViewBag.StatusLogin = StatusLogin;

                    // pagina login
                    return View(model);
                }

                // usuário excedeu tentativas de login
                if (retorno == TIPO_ERRO_LOGIN.ExcedeuTentativas)
                {
                    // envia email tentativas login
                    if (usuario != null)
                    {
                        await EnviaEmailTentativasLogin(usuario.NomeUsuario, usuario.Email);
                    }

                    // excedeu tentativas
                    ModelState.AddModelError("", @SmartEnergy.Resources.LoginTexts.LoginExcedeuTentativas);

                    // usuário bloqueado  (excedeu tentativas) 
                    StatusLogin = TIPO_STATUS_LOGIN.ExcedeuTentativas;
                    ViewBag.StatusLogin = StatusLogin;

                    // pagina login
                    return View(model);
                }

                // usuário bloqueado (suporte)
                if (retorno == TIPO_ERRO_LOGIN.UsuarioBloqueadoSuporte)
                {
                    // envia email bloqueio
                    if (usuario != null)
                    {
                        await EnviaEmailBloqueioLogin(usuario.NomeUsuario, usuario.Login, IDUsuario);
                    }

                    // usuário bloqueado
                    ModelState.AddModelError("", @SmartEnergy.Resources.LoginTexts.LoginUsuarioBloqueado);

                    // usuário bloqueado (suporte)
                    StatusLogin = TIPO_STATUS_LOGIN.UsuarioBloqueadoSuporte;
                    ViewBag.StatusLogin = StatusLogin;

                    // pagina login
                    return View(model);
                }

                // envia email erro de login para admin da GESTAL
                if (model.UserName.Length > 0 && model.Password.Length > 0)
                {
                    await EnviaEmailErroLogin(model.UserName, model.Password, retorno);
                }
            }

            // erro senha invalida
            ModelState.AddModelError("", @SmartEnergy.Resources.LoginTexts.LoginSenhaInvalida);

            // erro login
            StatusLogin = TIPO_STATUS_LOGIN.ErroLoginSenha;
            ViewBag.StatusLogin = StatusLogin;

            // log Junior
            LogMessage_Junior("Login 6");

            // ocorreu erro, apresenta tela de login
            return View(model);
        }

        
        //
        // [POST] /Login/LoginExterno
        // Login de usuário Externo acesso pelo 'login.asp' e páginas de clientes que possuam login personalidado
        // Não possui AntiForgeryToken e nem reCAPTCHA
        // Em caso de erro, é redirecionado para a página de Login Smart Energy em vez da página personalisada 
        [HttpPost]
        [AllowAnonymous]
        public async Task<ActionResult> LoginExterno(LoginViewModel model, string returnUrl)
        {
            //
            // Preparação
            //

            // caminho a ser direcionado
            string caminho = "";
            int IDUsuario = 0;
            int StatusLogin = TIPO_STATUS_LOGIN.ErroLoginSenha;

            // usuário
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();

            //
            // Site em Manutenção
            //

            // verifica se site esta em manutenção
            // "false" = não está em manutenção
            // "true"  = está em manutenção
            string MensagemManutencao = ConfigurationManager.AppSettings["MensagemManutencao"];
            bool SiteEmManutencao = (ConfigurationManager.AppSettings["SiteEmManutencao"] == "true") ? true : false;

            ViewBag.SiteEmManutencao = SiteEmManutencao;
            ViewBag.MensagemManutencao = MensagemManutencao;

            // idioma português
            Thread.CurrentThread.CurrentCulture = CultureInfo.CreateSpecificCulture("pt-BR");
            Thread.CurrentThread.CurrentUICulture = new CultureInfo("pt-BR");
            HttpCookie cookie = new HttpCookie("Language");
            cookie.Value = "pt-BR";
            Response.Cookies.Add(cookie);


            //
            // Login
            //

            // verifica se entradas válidas
            if (ModelState.IsValid)
            {
                //
                // Verifica Login
                //
                int retorno = VerificaLogin(model, ref caminho, ref IDUsuario);

                // login OK
                if (retorno == TIPO_ERRO_LOGIN.Sucesso)
                {
                    // salva pagina de retorno para logout
                    CookieStore.SalvaCookie_String("SiteInicio", model.SiteInicio);

                    // login OK
                    StatusLogin = TIPO_STATUS_LOGIN.Sucesso;
                    ViewBag.StatusLogin = StatusLogin;

                    // pagina inicial do Smart Energy
                    return Redirect(caminho);
                }

                // login conhecido e senha desconhecida
                if (retorno == TIPO_ERRO_LOGIN.ErroSenha)
                {
                    // usuário
                    UsuarioDominio usuario = usuarioMetodos.ListarPorId(IDUsuario);

                    // erro senha invalida
                    ModelState.AddModelError("", string.Format("Senha Incorreta, restam {0} tentativas", usuario.TentativasLogin - 5));

                    // erro senha invalida
                    StatusLogin = TIPO_STATUS_LOGIN.ErroLoginSenha;
                    ViewBag.StatusLogin = StatusLogin;

                    // envia email erro de senha para admin da GESTAL
                    await EnviaEmailErroSenha(model.UserName, model.Password);

                    // erro senha invalida
                    return View(model);
                }

                // consultor sem cliente
                if (retorno == TIPO_ERRO_LOGIN.ErroGestor)
                {
                    // erro configuração do gestor
                    ModelState.AddModelError("", "Erro na configuração do Gestor");

                    // consultor sem cliente
                    StatusLogin = TIPO_STATUS_LOGIN.ErroGestor;
                    ViewBag.StatusLogin = StatusLogin;

                    // consultor sem cliente, apresenta tela de login
                    return (Redirect("~/Login/Login"));
                }

                // senha expirou
                if (retorno == TIPO_ERRO_LOGIN.SenhaExpirou)
                {
                    // verifica login
                    UsuarioDominio usuario = usuarioMetodos.ListarPorId(IDUsuario);

                    // envia email senha expirada
                    if (usuario != null)
                    {
                        await EnviaEmailSenhaExpirou(usuario.NomeUsuario, usuario.Senha, usuario.Email, usuario.ExpirarEm);
                    }

                    // senha expirou
                    ModelState.AddModelError("", @SmartEnergy.Resources.LoginTexts.LoginSenhaExpirou);

                    // senha expirou
                    StatusLogin = TIPO_STATUS_LOGIN.SenhaExpirou;
                    ViewBag.StatusLogin = StatusLogin;

                    // senha expirou, apresenta tela de login
                    return (Redirect("~/Login/Login"));
                }

                // confirmação de email
                if (retorno == TIPO_ERRO_LOGIN.EmailConfirmacao)
                {
                    // confirmacao de email
                    ModelState.AddModelError("", @SmartEnergy.Resources.LoginTexts.LoginEmailConfirmacao);

                    // confirmacao de email
                    StatusLogin = TIPO_STATUS_LOGIN.EmailConfirmacao;
                    ViewBag.StatusLogin = StatusLogin;

                    // pagina login
                    return (Redirect("~/Login/Login"));
                }

                // usuário bloqueado (financeiro)
                if (retorno == TIPO_ERRO_LOGIN.UsuarioBloqueadoFinanceiro)
                {
                    // usuário
                    UsuarioDominio usuario = usuarioMetodos.ListarPorId(IDUsuario);

                    // envia email bloqueio
                    if (usuario != null)
                    {
                        await EnviaEmailBloqueioLogin(usuario.NomeUsuario, usuario.Login, IDUsuario);
                    }

                    // confirmacao de email
                    ModelState.AddModelError("", @SmartEnergy.Resources.LoginTexts.LoginUsuarioBloqueado);

                    // usuário bloqueado (financeiro)
                    StatusLogin = TIPO_STATUS_LOGIN.UsuarioBloqueadoFinanceiro;
                    ViewBag.StatusLogin = StatusLogin;

                    // pagina login
                    return (Redirect("~/Login/Login"));
                }

                // usuário excedeu tentativas de login
                if (retorno == TIPO_ERRO_LOGIN.ExcedeuTentativas)
                {
                    // usuário
                    UsuarioDominio usuario = usuarioMetodos.ListarPorId(IDUsuario);

                    // envia email tentativas login
                    if (usuario != null)
                    {
                        await EnviaEmailTentativasLogin(usuario.NomeUsuario, usuario.Email);
                    }

                    // excedeu tentativas
                    ModelState.AddModelError("", @SmartEnergy.Resources.LoginTexts.LoginExcedeuTentativas);

                    // usuário bloqueado (excedeu tentativas)
                    StatusLogin = TIPO_STATUS_LOGIN.ExcedeuTentativas;
                    ViewBag.StatusLogin = StatusLogin;

                    // pagina login
                    return View(model);
                }

                // usuário bloqueado (suporte)
                if (retorno == TIPO_ERRO_LOGIN.UsuarioBloqueadoSuporte)
                {
                    // usuário bloqueado
                    ModelState.AddModelError("", @SmartEnergy.Resources.LoginTexts.LoginUsuarioBloqueado);

                    // usuário bloqueado (suporte)
                    StatusLogin = TIPO_STATUS_LOGIN.UsuarioBloqueadoSuporte;
                    ViewBag.StatusLogin = StatusLogin;

                    // pagina login
                    return View(model);
                }

                // envia email erro de login para admin da GESTAL
                if (model.UserName.Length > 0 && model.Password.Length > 0)
                {
                    await EnviaEmailErroLogin(model.UserName, model.Password, retorno);
                }
            }

            // erro senha invalida
            ModelState.AddModelError("", @SmartEnergy.Resources.LoginTexts.LoginSenhaInvalida);

            // erro login
            StatusLogin = TIPO_STATUS_LOGIN.ErroLoginSenha;
            ViewBag.StatusLogin = StatusLogin;

            // ocorreu erro, apresenta tela de login
            return (Redirect("~/Login/Login"));
        }


        // [GET] /Login/LoginInterno
        // Login interno
        public ActionResult LoginInterno(int IDUsuario, int Acesso)
        {
            // status login
            int StatusLogin = TIPO_STATUS_LOGIN.ErroLoginSenha;

            // procura usuario
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = usuarioMetodos.ListarPorId(IDUsuario);

            // verifica se achou
            if (usuario != null)
            {
                // verifica se usuário confirma o tipo acesso
                if (usuario.IDTipoAcesso == Acesso)
                {
                    // verifica se não é GESTAL
                    if (Acesso != TIPO_ACESSO.GESTAL_ADMIN && Acesso != TIPO_ACESSO.GESTAL_SUPORTE && Acesso != TIPO_ACESSO.GESTAL_VENDAS && Acesso != TIPO_ACESSO.GESTAL_PRODUCAO)
                    {
                        LoginViewModel model = new LoginViewModel();

                        model.UserName = usuario.Login;
                        model.Password = usuario.Senha;
                        model.Externo = false;
                        model.SiteInicio = "https://www.smartenergy.com.br/";

                        // caminho a ser direcionado
                        string caminho = "";

                        // verifica login
                        int retorno = VerificaLogin(model, ref caminho, ref IDUsuario);

                        // pagina inicial do Smart Energy
                        if (caminho.Length > 0)
                        {
                            // login OK
                            StatusLogin = TIPO_STATUS_LOGIN.Sucesso;
                            ViewBag.StatusLogin = StatusLogin;

                            return Redirect(caminho);
                        }
                    }
                }
            }

            // erro login
            StatusLogin = TIPO_STATUS_LOGIN.ErroLoginSenha;
            ViewBag.StatusLogin = StatusLogin;

            return (Redirect("~/Login/Login"));
        }


        // verifica se existe usuário com o login e senha
        private int VerificaLogin(LoginViewModel model, ref string caminho, ref int IDUsuario)
        {
            // verifica se entradas válidas
            if (ModelState.IsValid)
            {
                // log Junior
                LogMessage_Junior("Login A");

                // verifica se tem login
                if (model.UserName != null)
                {
                    if (string.IsNullOrEmpty(model.UserName))
                    {
                        // ocorreu erro de login
                        return (TIPO_ERRO_LOGIN.ErroLoginSenha);
                    }
                }

                // verifica se tem senha
                if (model.Password != null)
                {
                    if (string.IsNullOrEmpty(model.Password))
                    {
                        // ocorreu erro de login
                        return (TIPO_ERRO_LOGIN.ErroLoginSenha);
                    }
                }


                // evento login
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();

                // IP usuario
                string IP_Cliente = GetIPCliente();

                // lista de clientes habilitados para o usuario
                string ConfigCli = "";
                List<int> ConfigCliList = new List<int>();

                // lista de medicoes habilitadas para o usuario
                string ConfigMed = "";
                List<int> ConfigMedList = new List<int>();

                // verifica login
                UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
                UsuarioDominio usuario = usuarioMetodos.VerificaLogin(model.UserName, model.Password);

                // log Junior
                LogMessage_Junior("Login B");

                // login ok
                if (usuario != null)
                {
                    //
                    // VERIFICA SE USUÁRIO BLOQUEADO
                    //

                    // bloqueio = 1 (alerta usuário para entrar em contato e libera acesso)
                    // bloqueio = 3 (bloqueia API e alerta usuário para entrar em contato e libera acesso)

                    // bloqueio = 2 (não permite entrar na plataforma)
                    if (usuario.Bloqueio == TIPO_BLOQUEIO.Financeiro)
                    {
                        // IDUsuario
                        IDUsuario = usuario.IDUsuario;

                        // evento login bloqueado
                        usuarioEventoMetodo.InserirEvento_IDUsuario(usuario.IDUsuario, USUARIO_EVENTO.ERRO_BLOQUEIO);

                        // atualiza ultimo login (mesmo sem sucesso)
                        usuarioMetodos.Atualiza_UltimoLogin(usuario.IDUsuario, IP_Cliente);

                        // usuário bloqueado (financeiro)
                        return (TIPO_ERRO_LOGIN.UsuarioBloqueadoFinanceiro);
                    }

                    // bloqueio = 4 (alerta usuário sobre bloqueio temporário e não permite entrar na plataforma caso tempo de tentativa não terminou)
                    if (usuario.Bloqueio == TIPO_BLOQUEIO.TentativasLogin)
                    {
                        // IDUsuario
                        IDUsuario = usuario.IDUsuario;

                        // verifica se último login faz mais de 1 minuto
                        TimeSpan diferenca = DateTime.Now - usuario.UltimoLogin;

                        // verifica se ainda em tempo de bloqueio
                        if (diferenca.TotalSeconds <= 60)
                        {
                            // evento login bloqueado
                            usuarioEventoMetodo.InserirEvento_IDUsuario(usuario.IDUsuario, USUARIO_EVENTO.ERRO_TENTATIVAS);

                            // atualiza ultimo login (mesmo sem sucesso)
                            usuarioMetodos.Atualiza_UltimoLogin(usuario.IDUsuario, IP_Cliente);

                            // usuário bloqueado (excesso tentativas)
                            return (TIPO_ERRO_LOGIN.ExcedeuTentativas);
                        }

                        // tempo de bloqueio terminou, desbloqueio usuario zerando tentativas
                        usuarioMetodos.ZeraTentativasLogin(usuario);
                    }

                    // bloqueio = 5 (alerta usuário para entrar em contato com suporte e não permite entrar na plataforma)
                    if (usuario.Bloqueio == TIPO_BLOQUEIO.Generico)
                    {
                        // IDUsuario
                        IDUsuario = usuario.IDUsuario;

                        // evento login bloqueado
                        usuarioEventoMetodo.InserirEvento_IDUsuario(usuario.IDUsuario, USUARIO_EVENTO.ERRO_BLOQUEIO);

                        // atualiza ultimo login (mesmo sem sucesso)
                        usuarioMetodos.Atualiza_UltimoLogin(usuario.IDUsuario, IP_Cliente);

                        // usuário bloqueado (suporte)
                        return (TIPO_ERRO_LOGIN.UsuarioBloqueadoSuporte);
                    }

                    //
                    // VERIFICA SE SENHA EXPIROU
                    //

                    // verifica se senha expirou
                    DateTime agora = DateTime.Now;

                    if (usuario.ExpirarMeses > 0 && usuario.ExpirarEm < agora)
                    {
                        // IDUsuario
                        IDUsuario = usuario.IDUsuario;

                        // gera parte randomica
                        string randomico = GenerateRandomString(7);
                        usuario.Senha = "x2" + randomico + "k";

                        // alterar a senha
                        usuarioMetodos.AlterarSenha(usuario);

                        // evento senha expirou
                        usuarioEventoMetodo.InserirEvento_IDUsuario(usuario.IDUsuario, USUARIO_EVENTO.ERRO_EXPIRADO);

                        // atualiza ultimo login (mesmo sem sucesso)
                        usuarioMetodos.Atualiza_UltimoLogin(usuario.IDUsuario, IP_Cliente);

                        // senha expirou
                        return (TIPO_ERRO_LOGIN.SenhaExpirou);
                    }


                    //
                    // VERIFICA SE EMAIL CONFIRMACAO
                    //

                    if (usuario.EmailConfirmado == 0)
                    {
                        // email confirmacao
                        return (TIPO_ERRO_LOGIN.EmailConfirmacao);
                    }


                    //
                    // VERIFICA SE É EMAIL GESTAL
                    //

                    if (isUser.isGESTAL(usuario.IDTipoAcesso))
                    {
                        // verifica se email NAO eh da GESTAL
                        string email = usuario.Email.ToUpper();

                        if (!email.Contains("@GESTAL.COM") && !email.Contains("@COMERC.COM.BR"))
                        {
                            // login esta OK, mas email NÃO é da GESTAL
                            return (usuario.IDUsuario);
                        }
                    }

                    // log Junior
                    LogMessage_Junior("Login C");

                    //
                    // ATUALIZA CLIENTES E MEDICOES HABILITADOS
                    //
                    // Verifico se eh a primeira vez que este usuario entra no site checando se tem registros em UsuariosMedicoes.
                    // Se nao tiver registros, uso o campo CONFIGMED / CONFIGMEDEMAIL e flags de recebimento de email em Usuarios para criar as medicoes habilitadas.
                    // Se tiver, uso o UsuariosMedicos para atualizar o campo CONFIGMED / CONFIGMEDEMAIL e flags de recebimento de email em Usuarios
                    UsuarioMedicaoMetodos usuariomedicaoMetodos = new UsuarioMedicaoMetodos();

                    if (isUser.isCliente(usuario.IDTipoAcesso))
                    {
                        usuariomedicaoMetodos.AtualizarUsuariosMedicoes(usuario.IDUsuario);
                    }

                    // log Junior
                    LogMessage_Junior("Login D");

                    //
                    // CLIENTES HABILITADOS
                    //

                    // verifica se consultor - operador
                    if (usuario.IDTipoAcesso == ISUSER.Consultor_Oper)
                    {
                        // leio medicoes habilitadas deste usuario
                        List<UsuarioMedicaoDominio> usuariosmedicoes = usuariomedicaoMetodos.ListarTodasMedicoesIDUsuario(usuario.IDUsuario);

                        // verifica se tem medicao
                        if (usuariosmedicoes.Count > 0)
                        {
                            // percorre todas as medicoes e monta string
                            foreach (UsuarioMedicaoDominio usuariomedicao in usuariosmedicoes)
                            {
                                // verifica se IDCliente NAO existe na lista
                                if (!ConfigCliList.Contains(usuariomedicao.IDCliente))
                                {
                                    // copia cliente na lista
                                    ConfigCliList.Add(usuariomedicao.IDCliente);

                                    ConfigCli = ConfigCli + "/" + usuariomedicao.IDCliente.ToString();
                                }
                            }

                            // fecha barra
                            ConfigCli = ConfigCli + "/";
                        }
                        else
                        {
                            ConfigCli = "/-10/";
                        }
                    }

                    // verifica se consultor - administrador ou se não tem medição selecionada para consultor - operador
                    if (usuario.IDTipoAcesso == ISUSER.Consultor || usuario.IDTipoAcesso == ISUSER.Consultor_Admin || ConfigCli == "/-10/")
                    {
                        // log Junior
                        LogMessage_Junior("Login E");

                        // zera
                        ConfigCli = "";

                        // leio TODOS clientes deste consultor - todos
                        ClientesMetodos clientesMetodos = new ClientesMetodos();
                        List<ClientesDominio> clientes = clientesMetodos.ListarPorIDConsultor(usuario.IDCliente, 0);

                        // verifica se tem cliente
                        if (clientes.Count > 0)
                        {
                            // percorre todos os clientes e monta string
                            foreach (ClientesDominio cliente in clientes)
                            {
                                ConfigCli = ConfigCli + "/" + cliente.IDCliente.ToString();
                            }

                            // fecha barra
                            ConfigCli = ConfigCli + "/";
                        }
                        else
                        {
                            ConfigCli = "/-10/";
                        }


                        // log Junior
                        LogMessage_Junior("Login F");

                    }

                    // verifica se consultor e não tem cliente
                    if (isUser.isConsultor(usuario.IDTipoAcesso) && (ConfigCli == "/-10/" || ConfigCli == ""))
                    {
                        // ocorreu erro de login
                        return (TIPO_ERRO_LOGIN.ErroGestor);
                    }

                    //
                    // MEDICOES HABILITADAS
                    //
                    int IDMedicao_primeira = 0;

                    // verifica se cliente / consultor - operador / demo
                    if (isUser.isCliente(usuario.IDTipoAcesso) || usuario.IDTipoAcesso == ISUSER.Consultor_Oper || usuario.IDTipoAcesso == ISUSER.Demo)
                    {
                        // log Junior
                        LogMessage_Junior("Login G");

                        // leio medicoes habilitadas deste usuario
                        List<UsuarioMedicaoDominio> usuariosmedicoes = usuariomedicaoMetodos.ListarTodasMedicoesIDUsuario(usuario.IDUsuario);

                        // verifica se tem medicao
                        if (usuariosmedicoes.Count > 0)
                        {
                            // percorre todas as medicoes e monta string
                            foreach (UsuarioMedicaoDominio usuariomedicao in usuariosmedicoes)
                            {
                                // guardo primeira medicao habilitada
                                if (IDMedicao_primeira == 0)
                                {
                                    IDMedicao_primeira = usuariomedicao.IDMedicao;
                                }

                                // verifica se IDMedicao NAO existe na lista
                                if (!ConfigMedList.Contains(usuariomedicao.IDMedicao))
                                {
                                    // copia cliente na lista
                                    ConfigMedList.Add(usuariomedicao.IDMedicao);
                                }

                                // medicao habilitada
                                ConfigMed = ConfigMed + "/" + usuariomedicao.IDMedicao.ToString();
                            }

                            // fecha barra
                            ConfigMed = ConfigMed + "/";
                        }
                    }

                    // log Junior
                    LogMessage_Junior("Login H");

                    // insere diretório BIN no ambiente
                    CheckAddBinPath();

                    // verifica se não possui segundo fator
                    if (usuario.IDTipoSegundoFator != TIPO_SEGUNDO_FATOR.Email)
                    {
                        // indica que esta logado
                        System.Web.Security.FormsAuthentication.SetAuthCookie(usuario.Apelido, false);
                    }

                    // idioma e cultura
                    ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
                    List<IdiomaDominio> listaTiposIdioma = listatiposMetodos.ListarTodos_Idioma();

                    if (listaTiposIdioma != null)
                    {
                        // encontra culture
                        IdiomaDominio idioma = listaTiposIdioma.Find(x => x.IDIdioma == usuario.IDIdioma);

                        if (idioma != null)
                        {
                            Thread.CurrentThread.CurrentCulture = CultureInfo.CreateSpecificCulture(idioma.Culture);
                            Thread.CurrentThread.CurrentUICulture = new CultureInfo(idioma.Culture);

                            HttpCookie cookie = new HttpCookie("Language");
                            cookie.Value = idioma.Culture;
                            Response.Cookies.Add(cookie);
                        }
                    }


                    // log Junior
                    LogMessage_Junior("Login I");


                    // salva cookie USUARIO
                    CookieStore.SalvaCookie_Int("_IDUsuario", usuario.IDUsuario);
                    CookieStore.SalvaCookie_Int("IDTipoAcesso", usuario.IDTipoAcesso);
                    CookieStore.SalvaCookie_String("NomeUsuario", usuario.NomeUsuario);
                    CookieStore.SalvaCookie_String("EmailUsuario", usuario.Email);
                    CookieStore.SalvaCookie_Int("BloqueioUsuario", usuario.Bloqueio);
                    CookieStore.DeleteCookie("BloqueioTempo");
                    CookieStore.SalvaCookie_Int("IDDashboardGrupo", usuario.IDDashboardGrupo);
                    CookieStore.SalvaCookie_Int("View_Analises", usuario.View_Analises);
                    CookieStore.SalvaCookie_Int("View_Financas", usuario.View_Financas);
                    CookieStore.SalvaCookie_Int("View_KPI", usuario.View_KPI);
                    CookieStore.SalvaCookie_Int("View_Ranking", usuario.View_Ranking);
                    CookieStore.SalvaCookie_Int("View_Rateio", usuario.View_Rateio);
                    CookieStore.SalvaCookie_Int("View_Metas", usuario.View_Metas);
                    CookieStore.SalvaCookie_Int("View_Configuracao", usuario.View_Configuracao);
                    CookieStore.SalvaCookie_Int("View_ConfiguracaoRemota", usuario.View_ConfiguracaoRemota);
                    CookieStore.SalvaCookie_Int("View_PerfilUsuario", usuario.View_PerfilUsuario);

                    CookieStore.SalvaCookie_String("ConfigMed", ConfigMed);
                    CookieStore.SalvaCookie_String("ConfigCli", ConfigCli);
                    CookieStore.SalvaCookie_Bool("DashboardTelaCheia", false);
                    CookieStore.SalvaCookie_Bool("DB_Editar", false);
                    CookieStore.SalvaCookie_Bool("DB_Excluir_DataHora", true);

                    CookieStore.SalvaCookie_Bool("ER_Excluir_DataHora", true);
                    CookieStore.SalvaCookie_Bool("ER_Editar", false);

                    // insere ja os cookies invalidos
                    CookieStore.SalvaCookie_Int("_IDMedicao", -10);
                    CookieStore.SalvaCookie_Int("IDTipoMedicao", -10);
                    CookieStore.SalvaCookie_Int("_IDEmpresa", -10);
                    CookieStore.SalvaCookie_Int("_IDGateway", -10);
                    CookieStore.SalvaCookie_Int("IDTipoGateway", -10);
                    CookieStore.SalvaCookie_String("TipoPaginaAtual", "Supervisao");
                    CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");
                    CookieStore.SalvaCookie_Int("Relat_Tipo", 0);
                    CookieStore.SalvaCookie_Int("Relat_TipoPeriodo", 0);
                    CookieStore.SalvaCookie_Int("KPI_Tipo", 0);
                    CookieStore.SalvaCookie_Int("_IDKPI", 0);
                    CookieStore.SalvaCookie_Int("_IDRanking", 0);
                    CookieStore.SalvaCookie_Int("_IDRateio", 0);
                    CookieStore.SalvaCookie_Int("_IDRamoAtividade", 0);
                    CookieStore.SalvaCookie_Int("_IDDistribuicaoCons", 0);
                    CookieStore.SalvaCookie_Int("_IDSimulacaoCenario", 0);
                    CookieStore.SalvaCookie_String("_NomeSimulacaoCenario", "");
                    CookieStore.SalvaCookie_Bool("AplicaSimulacao", false);

                    // apaga cookies nao necessarios
                    CookieStore.DeleteCookie("Analise_TipoOportunidades");
                    CookieStore.DeleteCookie("Relat_Data");
                    CookieStore.DeleteCookie("Relat_DataFim");
                    CookieStore.DeleteCookie("Relat_SemanalDomingo");
                    CookieStore.DeleteCookie("Relat_SemanalSegunda");
                    CookieStore.DeleteCookie("Relat_SemanalTerca");
                    CookieStore.DeleteCookie("Relat_SemanalQuarta");
                    CookieStore.DeleteCookie("Relat_SemanalQuinta");
                    CookieStore.DeleteCookie("Relat_SemanalSexta");
                    CookieStore.DeleteCookie("Relat_SemanalSabado");
                    CookieStore.DeleteCookie("Relat_SemanalMax");
                    CookieStore.DeleteCookie("Relat_SemanalMed");
                    CookieStore.DeleteCookie("Relat_SemanalMin");
                    CookieStore.DeleteCookie("Relat_Search");
                    CookieStore.DeleteCookie("Relat_SortedCol");
                    CookieStore.DeleteCookie("Relat_SortedDir");
                    CookieStore.DeleteCookie("Relat_TipoEvento");
                    CookieStore.DeleteCookie("Relat_IDUsuario");
                    CookieStore.DeleteCookie("Relat_TipoConsolidado");
                    CookieStore.DeleteCookie("Fatura_DataIni");
                    CookieStore.DeleteCookie("Fatura_DataFim");
                    CookieStore.DeleteCookie("Fatura_TipoContrato");
                    CookieStore.DeleteCookie("Fatura_TipoFatura");
                    CookieStore.DeleteCookie("Fatura_DemandaHistorica");
                    CookieStore.DeleteCookie("Fatura_TipoData");
                    CookieStore.DeleteCookie("Consolidado_TurnoIni");
                    CookieStore.DeleteCookie("Consolidado_TurnoFim");
                    CookieStore.DeleteCookie("GrupoMedicoes");
                    CookieStore.DeleteCookie("Periodo1_Ini");
                    CookieStore.DeleteCookie("Periodo1_Fim");
                    CookieStore.DeleteCookie("Periodo2_Ini");
                    CookieStore.DeleteCookie("Periodo2_Fim");
                    CookieStore.DeleteCookie("PageNumberDashboard");
                    CookieStore.DeleteCookie("PageNumberEditorRelat");
                    CookieStore.DeleteCookie("MedicaoEditar_Tab");
                    CookieStore.DeleteCookie("Manut_DataIni");
                    CookieStore.DeleteCookie("Manut_DataFim");
                    CookieStore.DeleteCookie("taskID");
                    CookieStore.DeleteCookie("Home_Data");

                    CookieStore.DeleteCookie("UnidadeGrandeza");
                    CookieStore.DeleteCookie("NomeGrandeza");


                    // log Junior
                    LogMessage_Junior("Login J");



                    // evento login com sucesso
                    usuarioEventoMetodo.InserirEvento_IDUsuario(usuario.IDUsuario, USUARIO_EVENTO.LOGIN);

                    // atualiza ultimo login
                    usuarioMetodos.Atualiza_UltimoLogin(usuario.IDUsuario, IP_Cliente);


                    // log Junior
                    LogMessage_Junior("Login K");


                    // zero
                    usuarioMetodos.ZeraTentativasLogin(usuario);

                    // logomarca
                    string logomarca = "LogoSmartEnergy85_transp.png";

                    // inicialmente cliente nao selecionado
                    CookieStore.SalvaCookie_Int("_IDCliente", -10);
                    CookieStore.SalvaCookie_Int("IDTipoContrato", -10);
                    CookieStore.SalvaCookie_Int("IDTipoSupervisao", 0);
                    CookieStore.SalvaCookie_Int("IDConsultor", -10);
                    CookieStore.SalvaCookie_String("Nome_GrupoUnidades", "Grupo de Unidades");
                    CookieStore.SalvaCookie_String("Nome_Unidades", "Unidades");
                    CookieStore.SalvaCookie_Int("IDTipoGrafico_Demanda", 0);

                    // log Junior
                    LogMessage_Junior("Login L");

                    // caminho
                    caminho = "~/Supervisao/Clientes";

                    // verifica se cliente / demo
                    if (isUser.isCliente(usuario.IDTipoAcesso) || usuario.IDTipoAcesso == ISUSER.Demo)
                    {
                        // log Junior
                        LogMessage_Junior("Login M");


                        // usa cliente do usuario
                        CookieStore.SalvaCookie_Int("_IDCliente", usuario.IDCliente);

                        // le cliente
                        ClientesMetodos clienteMetodos = new ClientesMetodos();
                        ClientesDominio cli = clienteMetodos.ListarPorId(usuario.IDCliente);
                        CookieStore.SalvaCookie_Int("IDTipoContrato", cli.IDTipoContrato);
                        CookieStore.SalvaCookie_Int("IDTipoSupervisao", cli.IDTipoSupervisao);
                        CookieStore.SalvaCookie_String("Nome_GrupoUnidades", cli.Nome_GrupoUnidades);
                        CookieStore.SalvaCookie_String("Nome_Unidades", cli.Nome_Unidades);
                        CookieStore.SalvaCookie_Int("IDTipoGrafico_Demanda", cli.IDTipoGrafico_Demanda);

                        // verifico se é demo diferente da GESTAL
                        if (usuario.IDTipoAcesso == ISUSER.Demo && usuario.IDUsuario != 2)
                        {
                            // busco gestor deste demo
                            ConsultoresMetodos gestorMetodos = new ConsultoresMetodos();
                            ConsultoresDominio gestor_demo = gestorMetodos.ListarPorDemo_IDUsuario(usuario.IDUsuario);

                            if (gestor_demo != null)
                            {
                                // forço cliente ser de consultor que esta em demo
                                cli.IDConsultor = gestor_demo.IDConsultor;
                            }
                            else
                            {
                                // caso não achou consultor com demo, forço o consultor ser o próprio demo
                                cli.IDConsultor = usuario.IDUsuario;
                            }
                        }

                        // verifica se cliente pertence a algum consultor
                        if (cli.IDConsultor > 0)
                        {
                            // IDConsultor
                            CookieStore.SalvaCookie_Int("IDConsultor", cli.IDConsultor);

                            // encontra logomarca
                            UsuarioDominio consultor_usuario = usuarioMetodos.ListarPorId(cli.IDConsultor);

                            // verifica se achou e se eh consultor
                            if (consultor_usuario != null)
                            {
                                // verifica se eh consultor
                                if (consultor_usuario.IDTipoAcesso == ISUSER.Consultor || consultor_usuario.IDTipoAcesso == ISUSER.Demo)
                                {
                                    logomarca = consultor_usuario.LogoConsult;
                                }
                            }
                        }

                        // leio todas as medições do cliente
                        MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                        List<MedicoesDominio> medicoes = medicoesMetodos.ListarPorIDCliente_Energia_OrdemMedInterna(usuario.IDCliente);

                        if (medicoes != null)
                        {
                            if (medicoes.Count > 0)
                            {
                                // medicao unidade consumidora
                                MedicoesDominio medicao_unidadeConsumidora = new MedicoesDominio();
                                medicao_unidadeConsumidora.IDMedicao = 0;

                                // verifica se lista de medições habilitadas esta vazia
                                if (ConfigMed == "")
                                {
                                    // pego a primeira
                                    medicao_unidadeConsumidora = medicoes[0];
                                }
                                else
                                {
                                    // procuro medição que este usuário está habilitado
                                    foreach (MedicoesDominio med in medicoes)
                                    {
                                        // verifica se IDMedicao existe na lista
                                        if (ConfigMedList.Contains(med.IDMedicao))
                                        {
                                            // pego a primeira
                                            medicao_unidadeConsumidora = med;
                                            break;
                                        }
                                    }
                                }

                                // verifica se achou unidade consumidora
                                if (medicao_unidadeConsumidora.IDMedicao > 0)
                                {
                                    // leio gateway
                                    GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
                                    GatewaysDominio gateway_unidadeConsumidora = gatewayMetodos.ListarPorId(medicao_unidadeConsumidora.IDGateway);

                                    if (gateway_unidadeConsumidora != null)
                                    {
                                        // salva cookie medicao
                                        CookieStore.SalvaCookie_Int("_IDMedicao", medicao_unidadeConsumidora.IDMedicao);
                                        CookieStore.SalvaCookie_Int("IDTipoMedicao", medicao_unidadeConsumidora.IDTipoMedicao);
                                        CookieStore.SalvaCookie_Int("_IDGateway", gateway_unidadeConsumidora.IDGateway);
                                        CookieStore.SalvaCookie_Int("IDTipoGateway", gateway_unidadeConsumidora.IDTipoGateway);
                                    }
                                }
                            }
                        }

                        // caminho
                        caminho = "~/Supervisao/Medicoes?IDCliente=" + usuario.IDCliente.ToString();
                    }

                    // verifica se consultor
                    if (isUser.isConsultor(usuario.IDTipoAcesso))
                    {
                        // log Junior
                        LogMessage_Junior("Login N");


                        // IDConsultor eh o usuario consultor
                        CookieStore.SalvaCookie_Int("IDConsultor", usuario.IDCliente);

                        // encontra logomarca
                        UsuarioDominio consultor_usuario = usuarioMetodos.ListarPorId(usuario.IDCliente);

                        // verifica se achou e se eh consultor
                        if (consultor_usuario != null)
                        {
                            // verifica se eh consultor
                            if (consultor_usuario.IDTipoAcesso == ISUSER.Consultor)
                            {
                                logomarca = consultor_usuario.LogoConsult;
                            }
                        }

                        // verifica se consultor possui apenas 1 cliente
                        ClientesMetodos clienteMetodos = new ClientesMetodos();
                        List<ClientesDominio> clientes = clienteMetodos.ListarPorIDConsultor(usuario.IDCliente, 1);

                        if (clientes != null)
                        {
                            // verifica se tem 1 cliente
                            if (clientes.Count == 1)
                            {
                                // copia cliente único
                                ClientesDominio cli = clientes[0];
                                CookieStore.SalvaCookie_Int("IDTipoContrato", cli.IDTipoContrato);
                                CookieStore.SalvaCookie_Int("IDTipoSupervisao", cli.IDTipoSupervisao);
                                CookieStore.SalvaCookie_String("Nome_GrupoUnidades", cli.Nome_GrupoUnidades);
                                CookieStore.SalvaCookie_String("Nome_Unidades", cli.Nome_Unidades);
                                CookieStore.SalvaCookie_Int("IDTipoGrafico_Demanda", cli.IDTipoGrafico_Demanda);

                                // caminho
                                caminho = "~/Supervisao/Medicoes?IDCliente=" + cli.IDCliente.ToString();
                            }
                        }
                    }

                    // verifica logomarca
                    if (logomarca == null || logomarca.Length == 0)
                    {
                        // logomarca
                        logomarca = "LogoSmartEnergy85_transp.png";
                    }

                    // logomarca
                    CookieStore.SalvaCookie_String("LogoConsultor", logomarca);

                    // leio todos dashboards do usuario
                    DashboardMetodos dashboardMetodos = new DashboardMetodos();
                    List<DashboardDominio> listaDashboards = new List<DashboardDominio>();

                    int IDReferencia = usuario.IDUsuario;
                    int TipoReferencia = DB_REFERENCIA.IDUsuario;

                    if (usuario.IDDashboardGrupo > 0)
                    {
                        IDReferencia = usuario.IDDashboardGrupo;
                        TipoReferencia = DB_REFERENCIA.IDDashboardGrupo;
                    }

                    listaDashboards = dashboardMetodos.ListarPorIdReferencia(IDReferencia, TipoReferencia);

                    if (listaDashboards.Count() > 0)
                    {
                        // verifica se dashboard do usuário ou modelo de grupo
                        if (usuario.IDDashboardGrupo == 0)
                        {
                            // caminho
                            caminho = "~/Dashboard/DB_Superv?Editavel=0&IDUsuario=" + usuario.IDUsuario.ToString();
                        }
                        else
                        {
                            // caminho
                            caminho = "~/Dashboard/DB_Superv?Editavel=0&IDUsuario=0&IDDashboardGrupo=" + usuario.IDDashboardGrupo.ToString();
                        }
                    }

                    // log Junior
                    LogMessage_Junior("Login O");


                    // verifico se admin da GESTAL
                    if (isUser.isGESTAL(usuario.IDTipoAcesso))
                    {
                        // Usa cliente e medicao demo
                        int IDCliente = 1;
                        int IDMedicao = 1;

                        // le supervisao da medicao
                        var medicoesMetodos = new SupervMedicoesMetodos();
                        var listaMedicoes = medicoesMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

                        // salva cookie cliente
                        CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);

                        // le contrato cliente
                        ClientesMetodos clienteMetodos = new ClientesMetodos();
                        ClientesDominio cli = clienteMetodos.ListarPorId(IDCliente);
                        CookieStore.SalvaCookie_Int("IDTipoContrato", cli.IDTipoContrato);
                        CookieStore.SalvaCookie_Int("IDTipoSupervisao", cli.IDTipoSupervisao);
                        CookieStore.SalvaCookie_String("Nome_GrupoUnidades", cli.Nome_GrupoUnidades);
                        CookieStore.SalvaCookie_String("Nome_Unidades", cli.Nome_Unidades);
                        CookieStore.SalvaCookie_Int("IDTipoGrafico_Demanda", cli.IDTipoGrafico_Demanda);

                        // salva cookie medicao
                        CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
                        CookieStore.SalvaCookie_Int("IDTipoMedicao", listaMedicoes.IDTipoMedicao_MD);
                        CookieStore.SalvaCookie_String("TipoPaginaAtual", "Supervisao");
                        CookieStore.SalvaCookie_Int("_IDGateway", listaMedicoes.IDGW_GW);
                        CookieStore.SalvaCookie_Int("IDTipoGateway", listaMedicoes.IDTipoGateway_GW);

                        // caminho
                        caminho = "~/Home/Inicio_Admin";


                        // log Junior
                        LogMessage_Junior("Login P");


                    }

                    // verifica se apresenta introducao
                    if (usuario.ShowIntro == 1 || usuario.IDTipoAcesso == ISUSER.Demo)
                    {
                        // caminho
                        caminho = "~/Navegacao/ShowIntro";
                    }

                    // verifica se apresenta politica de privacidade
                    if (usuario.AceiteLGPD == 0 && usuario.IDTipoAcesso != ISUSER.Demo)
                    {
                        // caminho
                        caminho = "~/Navegacao/ShowLGPD";
                    }

                    // verifica se senha eh ainda a alterada pela expiracao
                    if (Funcoes_Senha.Expiracao(usuario.Senha))
                    {
                        // caminho
                        caminho = "~/Navegacao/MudarSenha?IDUsuario=" + usuario.IDUsuario.ToString();
                    }

                    // verifica se senha ainda é MUDAR
                    if (Funcoes_Senha.NovoUsuario(usuario.Senha))
                    {
                        // caminho
                        caminho = "~/Navegacao/MudarSenha?IDUsuario=" + usuario.IDUsuario.ToString();
                    }

                    // verifica se segundo fator
                    if (usuario.IDTipoSegundoFator == TIPO_SEGUNDO_FATOR.Email)
                    {
                        // salva caminho
                        CookieStore.SalvaCookie_String("Caminho", caminho);

                        // caminho
                        caminho = "~/Navegacao/ShowSegundoFator";
                    }

                    // salva cookie ShowIntro
                    CookieStore.SalvaCookie_Int("ShowIntro", usuario.ShowIntro);


                    // verifica se consultor
                    int IDConsultor = CookieStore.LeCookie_Int("IDConsultor");

                    // tema do gestor
                    UsuarioDominio gestor = usuarioMetodos.ListarPorId(IDConsultor);
                    int IDTema = 0;

                    if (gestor != null)
                    {
                        // tema
                        IDTema = gestor.IDTema;
                    }

                    // log Junior
                    LogMessage_Junior("Login Q");


                    // tema
                    ListaTiposMetodos temaMetodos = new ListaTiposMetodos();
                    TemaDominio tema = temaMetodos.ListarPorId_Tema(IDTema);

                    CookieStore.SalvaCookie_Int("Tema", IDTema);
                    CookieStore.SalvaCookie_String("background", tema.background);
                    CookieStore.SalvaCookie_String("background_hover", tema.background_hover);
                    CookieStore.SalvaCookie_String("background_nav_header", tema.background_nav_header);
                    CookieStore.SalvaCookie_String("background_panel_title", tema.background_panel_title);

                    // registrar que foi feito login com sucesso nas páginas visitadas
                    HistoricoPaginasVisitadasDominio historico = new HistoricoPaginasVisitadasDominio();

                    // data hora
                    historico.DataHora = DateTime.Now;

                    // IDUsuario
                    historico.IDUsuario = usuario.IDUsuario;

                    // IDCliente
                    historico.IDCliente = usuario.IDCliente;

                    // página visitada
                    historico.URL = "/login";

                    // salva página
                    HistoricoPaginasVisitadasMetodos historicoMetodos = new HistoricoPaginasVisitadasMetodos();
                    historicoMetodos.Inserir(historico);

                    // log Junior
                    LogMessage_Junior("Login R");

                    // login ok
                    return (TIPO_ERRO_LOGIN.Sucesso);
                }
                else
                {
                    // login ou senha incorretos

                    // verifico se existe usuario com o login
                    usuario = usuarioMetodos.VerificaLogin(model.UserName);

                    // UserName ok
                    if (usuario != null)
                    {
                        // IDUsuario
                        IDUsuario = usuario.IDUsuario;

                        // atualiza ultimo login (mesmo sem sucesso)
                        usuarioMetodos.Atualiza_UltimoLogin(usuario.IDUsuario, IP_Cliente);

                        // incrementa tentativa de login
                        if (usuarioMetodos.IncrementaTentativasLogin(usuario))
                        {
                            // evento login bloqueado
                            usuarioEventoMetodo.InserirEvento_IDUsuario(usuario.IDUsuario, USUARIO_EVENTO.ERRO_TENTATIVAS);

                            // excedeu tentativas
                            return (TIPO_ERRO_LOGIN.ExcedeuTentativas);
                        }
                        else
                        {
                            // evento login com senha errada
                            usuarioEventoMetodo.InserirEvento_IDUsuario(usuario.IDUsuario, USUARIO_EVENTO.ERRO_SENHA, usuario.TentativasLogin + 1);

                            // login conhecido e senha desconhecida
                            return (TIPO_ERRO_LOGIN.ErroSenha);
                        }
                    }
                }
            }

            // ocorreu erro de login
            return (TIPO_ERRO_LOGIN.ErroLoginSenha);
        }

        //
        // POST: /Login/Logout
        // Executa o logout
        public ActionResult Logout()
        {
            // redireciona para o site de retorno
            string siteinicio = CookieStore.LeCookie_String("SiteInicio");

            // IDUsuario
            int IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");

            // evento logout
            UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
            usuarioEventoMetodo.InserirEvento_IDUsuario(IDUsuario, USUARIO_EVENTO.LOGOUT);

            // logout
            System.Web.Security.FormsAuthentication.SignOut();
            Session.Abandon();

            // limpar cookies
            LimparCookies();

            return new RedirectResult(siteinicio);
        }

        // GET: Verificar bloqueio de usuario
        public ActionResult Bloqueio()
        {
            // site início
            string siteinicio = CookieStore.LeCookie_String("SiteInicio");

            // retorno
            var returnedData = new
            {
                status = "OK",
                SiteInicio = siteinicio
            };

            // IDUsuario
            int IDUsuario = CookieStore.LeCookie_Int("_IDUsuario"); 

            // verifica usuario
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = usuarioMetodos.ListarPorId(IDUsuario);

            // verifica usuário 
            if (usuario != null)
            {
                // verifica se usuário liberado
                if (usuario.Bloqueio == TIPO_BLOQUEIO.Desbloqueado || usuario.Bloqueio == TIPO_BLOQUEIO.Tempo || usuario.Bloqueio == TIPO_BLOQUEIO.TempoAPI)
                {
                    // permito continuar
                    return Json(returnedData, JsonRequestBehavior.AllowGet);
                }
            }

            // evento logout
            UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
            usuarioEventoMetodo.InserirEvento_IDUsuario(IDUsuario, USUARIO_EVENTO.LOGOUT);

            // logout
            System.Web.Security.FormsAuthentication.SignOut();
            Session.Abandon();

            // limpar cookies
            LimparCookies();

            // retorno
            returnedData = new
            {
                status = "ERRO",
                SiteInicio = siteinicio
            };

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // limpar cookies
        private void LimparCookies()
        {
            // limpa cookies
            string sessionId = Session.SessionID;

            // lista de todos os cookies
            foreach (string cookieName in Request.Cookies.AllKeys)
            {
                HttpCookie cookie = Request.Cookies[cookieName];
                if (cookie != null)
                {
                    // coloco data de expiração anterior a agora para fechar
                    cookie.Expires = DateTime.Now.AddDays(-1);
                    Response.Cookies.Add(cookie);
                }
            }

            return;
        }

        //
        // [GET] /Login/AlteraIdioma
        // Alterar o idioma
        // O usuario deve configurar o seu Internet Explorer e Windows para a cultura desejada (inserir cultura/idioma)
        // Idiomas permitidos:
        // pt-BR : Português Brasil
        // es-AR : Espanhol Argentina
        // en    : Inglês
        [AllowAnonymous]
        public ActionResult AlteraIdioma(String LanguageAbbrevation)
        {
            // cultura/idioma
            if (LanguageAbbrevation != null)
            {
                Thread.CurrentThread.CurrentCulture = CultureInfo.CreateSpecificCulture(LanguageAbbrevation);
                Thread.CurrentThread.CurrentUICulture = new CultureInfo(LanguageAbbrevation);
            }

            HttpCookie cookie = new HttpCookie("Language");
            cookie.Value = LanguageAbbrevation;
            Response.Cookies.Add(cookie);


            // verifica se site esta em manutenção
            // "false" = não está em manutenção
            // "true"  = está em manutenção
            bool SiteEmManutencao = (ConfigurationManager.AppSettings["SiteEmManutencao"] == "true") ? true : false; ;
            string MensagemManutencao = ConfigurationManager.AppSettings["MensagemManutencao"];

            ViewBag.SiteEmManutencao = SiteEmManutencao;
            ViewBag.MensagemManutencao = MensagemManutencao;

            // login
            return View("Login");
        }


        //
        // [POST] /Login/EsqueciSenha
        // Envia a senha para o email do usuário
        [HttpPost]
        [AllowAnonymous]
        public async Task<ActionResult> EsqueciSenha(string LoginEsqueciSenha, string EmailEsqueciSenha)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            //
            // Verifica Recaptcha
            //
            // Google reCaptcha
            // https://www.google.com/recaptcha/admin 
            // <EMAIL> (Borges#190)
            //
            // SmartEnergyV2
            //
            // chaves
            string PublicKey = "";
            string PrivateKey = "";
            reCaptcha.ReadReCaptchaKeys(ref PublicKey, ref PrivateKey);

            var response = Request["g-recaptcha-response"];
            var cliente = new WebClient();
            var resultado = cliente.DownloadString(string.Format("https://www.google.com/recaptcha/api/siteverify?secret={0}&response={1}", PrivateKey, response));
            var obj = JObject.Parse(resultado);

            var status = (bool)obj.SelectToken("success");

            if (!status)
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "reCAPTCHA inválido."
                };

                // retorna status
                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            //
            // Formulário
            //

            // procura usuario com o login
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = usuarioMetodos.ListarPorLogin(LoginEsqueciSenha, EmailEsqueciSenha);

            if( usuario != null )
            {
                if( usuario.IDUsuario > 0 )
                {
                    // emails
                    string[] emails = { usuario.Email };

                    // assunto
                    string assunto = "Senha Smart Energy";

                    // envia EMAIL
                    var emailTemplate = "EsqueciSenhaEmail";
                    var message = await EMailTemplate(emailTemplate);
                    message = message.Replace("ViewBag.NomeUsuario", usuario.NomeUsuario);
                    message = message.Replace("ViewBag.Senha", usuario.Senha);
                    await EmailServices.SendBulkEmailAsync(emails, assunto, message, null);
                }
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        //
        // [POST] /Login/SolicitarDemo
        // Envia formulário de solicitação de demo para GESTAL
        [HttpPost]
        [AllowAnonymous]
        public async Task<ActionResult> SolicitarDemo(string NomeDemo, string EmpresaDemo, string EmailDemo, string TelefoneDemo)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            //
            // Verifica Recaptcha
            //
            // Google reCaptcha
            // https://www.google.com/recaptcha/admin 
            // <EMAIL> (Borges#190)
            //
            // SmartEnergyV2
            //
            // chaves
            string PublicKey = "";
            string PrivateKey = "";
            reCaptcha.ReadReCaptchaKeys(ref PublicKey, ref PrivateKey);

            var response = Request.Form["g-recaptcha-response"];
            var cliente = new WebClient();
            var resultado = cliente.DownloadString(string.Format("https://www.google.com/recaptcha/api/siteverify?secret={0}&response={1}", PrivateKey, response));
            var obj = JObject.Parse(resultado);

            var status = (bool)obj.SelectToken("success");

            if (!status)
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "reCAPTCHA inválido."
                };

                // retorna status
                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            //
            // Formulário
            //

            // emails
            string[] emails = { EmailDemo, "<EMAIL>" };

            // assunto
            string assunto = "Demonstração Smart Energy";

            // envia EMAIL
            var emailTemplate = "SolicitarDemoEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.Nome", NomeDemo);
            message = message.Replace("ViewBag.Empresa", EmpresaDemo);
            message = message.Replace("ViewBag.Email", EmailDemo);
            message = message.Replace("ViewBag.Telefone", TelefoneDemo);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, null);

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        // Envia email de Erro de Login para GESTAL
        public async Task<int> EnviaEmailErroLogin(string login, string senha, int IDUsuario = -10)
        {
            // IP usuario
            string IP_Cliente = GetIPCliente();
            string IP_Local = GetIPLocal();

            System.Web.HttpBrowserCapabilitiesBase browser = Request.Browser;
            string info = "Browser Capabilities:<br>"
                + "Type = " + browser.Type + "<br>"
                + "Name = " + browser.Browser + "<br>"
                + "Version = " + browser.Version + "<br>"
                + "Major Version = " + browser.MajorVersion + "<br>"
                + "Minor Version = " + browser.MinorVersion + "<br>"
                + "Platform = " + browser.Platform + "<br>"
                + "Supports JavaScript Version = " + browser["JavaScriptVersion"] + "<br>";

            // emails
            string[] emails = { "<EMAIL>" };

            // assunto
            string assunto = "[Smart Energy] Erro de Login";

            // envia EMAIL
            var emailTemplate = "ErroLoginEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.Login", login);
            message = message.Replace("ViewBag.Senha", senha);
            message = message.Replace("ViewBag.IP_Cliente", IP_Cliente);
            message = message.Replace("ViewBag.IP_Local", IP_Local);
            message = message.Replace("ViewBag.info", info);

            // usuario ADMIN
            string texto_admin = "";
            if( IDUsuario >= 0 )
            {
                texto_admin = string.Format("Usuário se logou como ADMIN e não tem Email da GESTAL - IDUsuario [{0}]", IDUsuario);
            }
            message = message.Replace("ViewBag.TextoAdmin", texto_admin);

            await EmailServices.SendBulkEmailAsync(emails, assunto, message, null);

            // retorna status
            return(0);
        }

        // Envia email de Erro de Senha para GESTAL
        public async Task<int> EnviaEmailErroSenha(string login, string senha)
        {
            // IP usuario
            string IP_Cliente = GetIPCliente();
            string IP_Local = GetIPLocal();

            System.Web.HttpBrowserCapabilitiesBase browser = Request.Browser;
            string info = "Browser Capabilities:<br>"
                + "Type = " + browser.Type + "<br>"
                + "Name = " + browser.Browser + "<br>"
                + "Version = " + browser.Version + "<br>"
                + "Major Version = " + browser.MajorVersion + "<br>"
                + "Minor Version = " + browser.MinorVersion + "<br>"
                + "Platform = " + browser.Platform + "<br>"
                + "Supports JavaScript Version = " + browser["JavaScriptVersion"] + "<br>";

            // emails
            string[] emails = { "<EMAIL>" };

            // assunto
            string assunto = "[Smart Energy] Erro de Senha";

            // envia EMAIL
            var emailTemplate = "ErroLoginEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.Login", login);
            message = message.Replace("ViewBag.Senha", senha);
            message = message.Replace("ViewBag.IP_Cliente", IP_Cliente);
            message = message.Replace("ViewBag.IP_Local", IP_Local);
            message = message.Replace("ViewBag.info", info);
            message = message.Replace("ViewBag.TextoAdmin", "");

            await EmailServices.SendBulkEmailAsync(emails, assunto, message, null);

            // retorna status
            return (0);
        }

        // Envia email de Login bloqueado para GESTAL
        public async Task<int> EnviaEmailBloqueioLogin(string nomeusuario, string login, int IDUsuario)
        {
            // emails
            string[] emails = { "<EMAIL>" };

            // assunto
            string assunto = "[Smart Energy] Login Bloqueado";

            // envia EMAIL
            var emailTemplate = "LoginBloqueadoEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.Login", login);
            message = message.Replace("ViewBag.Nome", nomeusuario);
            message = message.Replace("ViewBag.IDUsuario", IDUsuario.ToString());

            await EmailServices.SendBulkEmailAsync(emails, assunto, message, null);

            // retorna status
            return (0);
        }

        // Envia email Tentativas de login para usuário
        public async Task<int> EnviaEmailTentativasLogin(string Nome, string Email)
        {

            // emails
            string[] emails = { Email, "<EMAIL>" };

            // assunto
            string assunto = "[Smart Energy] Excedeu número de tentativas de Login";

            // envia EMAIL
            var emailTemplate = "LoginTentativasEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.Nome", Nome);

            await EmailServices.SendBulkEmailAsync(emails, assunto, message, null);

            // retorna status
            return (0);
        }

        // Envia email Senha Expirou para usuário
        public async Task<int> EnviaEmailSenhaExpirou(string Nome, string Senha, string Email, DateTime ExpirarEm)
        {
            // emails
            string[] emails = { Email };

            // assunto
            string assunto = "[Smart Energy] Senha Expirou";

            // envia EMAIL
            var emailTemplate = "SenhaExpirouEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.Nome", Nome);
            message = message.Replace("ViewBag.Senha", Senha);
            message = message.Replace("ViewBag.ExpirarEm", string.Format("{0:dd/MM/yyyy}", ExpirarEm));

            await EmailServices.SendBulkEmailAsync(emails, assunto, message, null);

            // retorna status
            return (0);
        }


        //
        // [GET] /Login/ConfirmarEmail
        // Confirmação do email
        [AllowAnonymous]
        public ActionResult ConfirmarEmail(int ID, string Codigo)
        {
            // verifica o usuario
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = usuarioMetodos.ListarPorId(ID);

            if (usuario != null)
            {
                if (usuario.CodigoAtivacao == Codigo)
                {
                    // liberar usuário
                    usuarioMetodos.Atualiza_EmailConfirmado(ID);
                }
            }

            // email confirmado
            int StatusLogin = 5;
            ViewBag.StatusLogin = StatusLogin;

            // pagina login
            return Redirect("~/Login/Login?Status=5");
        }


        //
        // [GET] /Login/EnviarConfirmacaoEmail
        // Enviar confirmação de Email
        [AllowAnonymous]
        public async Task<ActionResult> EnviarConfirmacaoEmail(string LoginReenviarConfirmacao, string EmailReenviarConfirmacao)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };


            //
            // Formulário
            //

            // procura usuario com o login
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = usuarioMetodos.ListarPorLogin(LoginReenviarConfirmacao, EmailReenviarConfirmacao);

            if (usuario != null)
            {
                if (usuario.IDUsuario > 0)
                {
                    // cria código de ativação
                    string CodigoAtivacao = GenerateRandomString(15);

                    // novo código de ativação
                    usuarioMetodos.Atualiza_CodigoAtivacao(usuario.IDUsuario, CodigoAtivacao);

                    // assunto
                    string assunto = "[Smart Energy] Confirmação de Email";

                    // envia EMAIL
                    var emailTemplate = "ConfirmacaoEmail";

                    var message = await EMailTemplate(emailTemplate);
                    message = message.Replace("ViewBag.Nome", usuario.NomeUsuario);
                    message = message.Replace("ViewBag.IDUsuario", usuario.IDUsuario.ToString());
                    message = message.Replace("ViewBag.CodigoAtivacao", CodigoAtivacao);
                    await EmailServices.SendEmailAsync(usuario.Email, assunto, message, null);
                }
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        // template do email
        public static async Task<string> EMailTemplate(string template)
        {
            // caminho do template
            var templateFilePath = HostingEnvironment.MapPath("~/Content/emailTemplates/") + template + ".cshtml";

            // formata corpo do email
            StreamReader objstreamreaderfile = new StreamReader(templateFilePath);
            var body = await objstreamreaderfile.ReadToEndAsync();
            objstreamreaderfile.Close();

            // retorno corpo do email
            return body;
        }

        // tratamento de exceções
        protected override void OnException(ExceptionContext filterContext)
        {
            base.OnException(filterContext);

            var action = filterContext.RequestContext.RouteData.Values["action"] as string;
            var controller = filterContext.RequestContext.RouteData.Values["controller"] as string;

            if ((filterContext.Exception is HttpAntiForgeryException) &&
                action == "Login" &&
                controller == "Login" &&
                filterContext.RequestContext.HttpContext.User != null &&
                filterContext.RequestContext.HttpContext.User.Identity.IsAuthenticated)
            {
                filterContext.ExceptionHandled = true;

                // redirect/show error/whatever?
                filterContext.Result = new RedirectResult("/Login/Login?Status=1");
            }
        }


        // Gera string com conteúdo randomico
        public static string GenerateRandomString(int length)
        {
            // cria criptografia
            var numArray = new byte[length];
            new RNGCryptoServiceProvider().GetBytes(numArray);

            // retorna strig com conteúdo randomico
            return CleanUpBase64String(Convert.ToBase64String(numArray), length);
        }

        // Limpa string com ocnteúdo randomico
        private static string CleanUpBase64String(string input, int maxLength)
        {
            // não permite alguns caracteres 
            input = input.Replace("-", "");
            input = input.Replace("=", "");
            input = input.Replace("/", "");
            input = input.Replace("+", "");
            input = input.Replace(" ", "");

            while (input.Length < maxLength)
                input = input + GenerateRandomString(maxLength);

            // somente letra maiúscula
            return input.Length <= maxLength ? input.ToUpper() : input.ToUpper().Substring(0, maxLength);
        }

        // Gera string com números randomico
        public static string GenerateRandomString_Numbers(int length)
        {
            // gera string com números aleatórios
            Random random = new Random();
            string numeros = "";

            for (int i = 0; i < length; i++)
            {
                numeros += random.Next(0, 10).ToString();
            }

            return numeros;
        }

        // Obtem IP Local
        public string GetIPLocal()
        {
            string IPEndereco = "";

            IPHostEntry Host = default(IPHostEntry);
            string Hostname = null;
            Hostname = System.Environment.MachineName;
            Host = Dns.GetHostEntry(Hostname);
            foreach (IPAddress IP in Host.AddressList)
            {
                if (IP.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                {
                    IPEndereco = Convert.ToString(IP);
                }
            }
            return IPEndereco;

        }

        // Obtem IP do Cliente
        public string GetIPCliente()
        {
            // IP usuario
            string IP_Cliente = "";
            IP_Cliente = Request.ServerVariables["HTTP_X_FORWARDED_FOR"];
            if (IP_Cliente == null)
                IP_Cliente = Request.ServerVariables["REMOTE_ADDR"];

            return (IP_Cliente);
        }


        // Insere diretório BIN no ambiente
        public static void CheckAddBinPath()
        {
            // find path to 'bin' folder
            var binPath = Path.Combine(new string[] { AppDomain.CurrentDomain.BaseDirectory, "bin" });

            // get current search path from environment
            var path = Environment.GetEnvironmentVariable("PATH") ?? "";

            // add 'bin' folder to search path if not already present
            if (!path.Split(Path.PathSeparator).Contains(binPath, StringComparer.CurrentCultureIgnoreCase))
            {
                path = string.Join(Path.PathSeparator.ToString(), new string[] { path, binPath });
                Environment.SetEnvironmentVariable("PATH", path);
            }
        }

        // log Junior
        public void LogMessage_Junior(string msg)
        {
            // log
            if (nome_login.ToLower() == "junior")
            {
                Funcoes_Log.Mensagem("Junior", msg);
            }
        }
    }
}

