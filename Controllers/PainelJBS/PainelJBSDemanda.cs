﻿using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Hosting;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class PainelJBSController : Controller
    {
        //
        // PAINEL JBS DEMANDA
        //

        // GET: Painel JBS Demanda 
        public ActionResult PainelJBS_Demanda(int IDCliente, int IDMedicao)
        {
            // le supervisao da medicao
            SupervMedicoesMetodos medicoesMetodos = new SupervMedicoesMetodos();
            SupervMedicoesDominio listaMedicoes = medicoesMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

            // salva cookie 
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", listaMedicoes.IDTipoMedicao_MD);
            CookieStore.SalvaCookie_String("MedicaoNome", listaMedicoes.NMED);
            CookieStore.SalvaCookie_String("UnidadeNome", listaMedicoes.NUN);
            CookieStore.SalvaCookie_String("GrupoNome", listaMedicoes.NGU);

            // tela de ajuda - Painel JBS Demanda
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "PainelJBS_Demanda");

            LeCookies_SmartEnergy();

            // data inicio
            DateTime data_ini = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 0, 0);

            // data fim
            DateTime data_fim = data_ini.AddMonths(1);

            // data referencia 
            DateTime data_ref = data_ini.AddMonths(-1);

            // le cookie datahora
            DateTime data_ini_cookie = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime data_fim_cookie = CookieStore.LeCookie_Datahora("Fatura_DataFim");
            DateTime data_ref_cookie = CookieStore.LeCookie_Datahora("Relat_DataFim");

            // verifica se existe datahora no cookie
            if (data_ini_cookie.Year != 2000 && data_fim_cookie.Year != 2000)
            {
                // copia data do cookie 
                data_ini = data_ini_cookie;
                data_fim = data_fim_cookie;
            }

            // verifica se existe datahora no cookie 
            if (data_ref_cookie.Year != 2000)
            {
                data_ref = data_ref_cookie;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", data_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", data_fim);
            CookieStore.SalvaCookie_Datahora("Relat_DataFim", data_ref);

            // data inicio 
            ViewBag.DataIni = string.Format("{0:dd/MM/yyyy}", data_ini);

            // data fim
            ViewBag.DataFim = string.Format("{0:dd/MM/yyyy}", data_fim);

            // data referencia 
            ViewBag.DataRef = string.Format("{0:MM/yyyy}", data_ref);

            // data referencia texto
            ViewBag.DataRefTexto = string.Format("{0:y}", data_ref);

            // le cookie dias da semana
            string dias_semana = CookieStore.LeCookie_String("Dias_Semana");

            // dias da semana
            bool domingo = false;
            bool segunda = false;
            bool terca = false;
            bool quarta = false;
            bool quinta = false;
            bool sexta = false;
            bool sabado = false;

            // verifica string do cookie
            if (String.IsNullOrEmpty(dias_semana))
            {
                // inicialmente se estiver vazia preenche segunda a sexta
                dias_semana = "1/2/3/4/5";
            }

            // divide string
            string[] dias_semana_aux = dias_semana.Split('/');
            List<int> dias_semana_calc = new List<int>();

            // percorre array
            if (dias_semana_aux.Count() > 0)
            {
                // domingo
                if (dias_semana_aux.Contains("0"))
                {
                    domingo = true;
                    dias_semana_calc.Add(0);
                }
                // segunda
                if (dias_semana_aux.Contains("1"))
                {
                    segunda = true;
                    dias_semana_calc.Add(1);
                }
                // terca
                if (dias_semana_aux.Contains("2"))
                {
                    terca = true;
                    dias_semana_calc.Add(2);
                }
                // quarta
                if (dias_semana_aux.Contains("3"))
                {
                    quarta = true;
                    dias_semana_calc.Add(3);
                }
                // quinta
                if (dias_semana_aux.Contains("4"))
                {
                    quinta = true;
                    dias_semana_calc.Add(4);
                }
                // sexta
                if (dias_semana_aux.Contains("5"))
                {
                    sexta = true;
                    dias_semana_calc.Add(5);
                }
                // sabado
                if (dias_semana_aux.Contains("6"))
                {
                    sabado = true;
                    dias_semana_calc.Add(6);
                }
            }

            // salva cookie
            CookieStore.SalvaCookie_String("Dias_Semana", dias_semana);

            // passa valores para view
            ViewBag.Dom = domingo;
            ViewBag.Seg = segunda;
            ViewBag.Ter = terca;
            ViewBag.Qua = quarta;
            ViewBag.Qui = quinta;
            ViewBag.Sex = sexta;
            ViewBag.Sab = sabado;

            // lista de erros
            var listaErros = new List<string>();

            // erros
            ViewBag.listaErros = listaErros;

            return View();
        }

        public PartialViewResult _PainelJBS_Demanda_Atualizar(int IDCliente, int IDMedicao, int Navegacao, string DataIni, string DataFim, string DataRef)
        {
            // calcula 
            PainelJBS_Demanda_Show(IDCliente, IDMedicao, Navegacao, DataIni, DataFim, DataRef);

            return PartialView();
        }

        // GET: Painel JBS Demanda Print
        public ActionResult PainelJBS_Demanda_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // sinaliza que é print 
            CookieStore.SalvaCookie_Bool("ER_Print", true);

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str_ini = string.Format("{0:g}", DateTime.Now);

            // calcula 
            PainelJBS_Demanda_Show(IDCliente, IDMedicao, 0, data_str_ini, data_str_ini, data_str_ini);

            // imprime
            return View();
        }


        // GET: Painel JBS Demanda EMAIL
        public async Task<ActionResult> PainelJBS_Demanda_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data ini 
            string data_str = string.Format("{0:g}", DateTime.Now);

            // sinaliza que é pdf 
            CookieStore.SalvaCookie_Bool("ER_Print", false);

            // calcula
            PainelJBS_Demanda_Show(IDCliente, IDMedicao, 0, data_str, data_str, data_str);

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("PainelJBS_Demanda_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDMedicao, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_PainelJBS_Demanda_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = false
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "PainelJBSDemandaEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.MedicaoNome", ViewBag.MedicaoNome);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // email template
        public static async Task<string> EMailTemplate(string template)
        {
            var templateFilePath = HostingEnvironment.MapPath("~/Content/emailTemplates/") + template + ".cshtml";
            StreamReader objstreamreaderfile = new StreamReader(templateFilePath);
            var body = await objstreamreaderfile.ReadToEndAsync();
            objstreamreaderfile.Close();
            return body;
        }

        // GET: Painel JBS Demanda PDF
        public ActionResult PainelJBS_Demanda_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // sinaliza que é pdf 
            CookieStore.SalvaCookie_Bool("ER_Print", false);

            // calcula
            PainelJBS_Demanda_Show(IDCliente, IDMedicao, 0, data_str, data_str, data_str);

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("PainelJBS_Demanda_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDMedicao, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_PainelJBS_Demanda_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = false
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Painel JBS Demanda
        public void PainelJBS_Demanda_Show(int IDCliente, int IDMedicao, int Navegacao, string DataIni, string DataFim, string DataRef)
        {
            // le supervisao da medicao
            SupervMedicoesMetodos medicoesMetodos = new SupervMedicoesMetodos();
            SupervMedicoesDominio listaMedicoes = medicoesMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

            // salva cookie 
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", listaMedicoes.IDTipoMedicao_MD);
            CookieStore.SalvaCookie_String("MedicaoNome", listaMedicoes.NMED);
            CookieStore.SalvaCookie_String("UnidadeNome", listaMedicoes.NUN);
            CookieStore.SalvaCookie_String("GrupoNome", listaMedicoes.NGU);

            if (Navegacao == 100)
            {
                // pega data
                DateTime dateValueIni = DateTime.Parse(DataIni);
                DateTime dateValueFim = DateTime.Parse(DataFim);
                DateTime dateValueRef = DateTime.Parse(DataRef);

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Fatura_DataIni", dateValueIni);
                CookieStore.SalvaCookie_Datahora("Fatura_DataFim", dateValueFim);
                CookieStore.SalvaCookie_Datahora("Relat_DataFim", dateValueRef);
            }

            // le cookies
            LeCookies_SmartEnergy();

            // hora atual
            DateTime data_ini = DateTime.Now;
            DateTime data_fim = data_ini.AddMonths(1);
            DateTime data_ref = data_ini.AddMonths(-1);

            // le cookie datahora
            DateTime datahora_cookie_ini = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Fatura_DataFim");
            DateTime datahora_cookie_ref = CookieStore.LeCookie_Datahora("Relat_DataFim");

            // verifica se existe datahora no cookie
            if (datahora_cookie_ini.Year != 2000)
            {
                // copia data do cookie para ultima
                data_ini = datahora_cookie_ini;
            }

            // verifica se existe datahora no cookie
            if (datahora_cookie_fim.Year != 2000)
            {
                // copia data do cookie para ultima
                data_fim = datahora_cookie_fim;
            }

            // verifica se existe datahora no cookie
            if (datahora_cookie_ref.Year != 2000)
            {
                // copia data do cookie para ultima
                data_ref = datahora_cookie_ref;
            }

            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -3:    // dia anterior

                    data_ini = data_ini.AddDays(-1);
                    data_fim = data_fim.AddDays(-1);
                    break;

                case 3:    // dia seguinte

                    data_ini = data_ini.AddDays(1);
                    data_fim = data_fim.AddDays(1);
                    break;

                case -4:    // mes anterior

                    data_ini = data_ini.AddMonths(-1);
                    data_fim = data_fim.AddMonths(-1);
                    break;

                case 4:    // mes seguinte

                    data_ini = data_ini.AddMonths(1);
                    data_fim = data_fim.AddMonths(1);
                    break;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", data_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", data_fim);
            CookieStore.SalvaCookie_Datahora("Relat_Data", data_ref);

            // data inicio 
            ViewBag.DataIni = string.Format("{0:dd/MM/yyyy}", data_ini);

            // data fim
            ViewBag.DataFim = string.Format("{0:dd/MM/yyyy}", data_fim);

            ViewBag.DataTextoIni = string.Format("{0:D}", data_ini);
            ViewBag.DataTextoFim = string.Format("{0:D}", data_fim);

            // data referencia
            ViewBag.DataRef = string.Format("{0:MM/yyyy}", data_ref);

            // data referencia texto
            ViewBag.DataRefTexto = string.Format("{0:y}", data_ref);

            // data referencia texto PDF
            ViewBag.DataRefTextoPDF = string.Format("{0:MM/yyyy}", data_ref);

            // le cookie dias da semana
            string dias_semana = CookieStore.LeCookie_String("Dias_Semana");

            // dias da semana
            bool domingo = false;
            bool segunda = false;
            bool terca = false;
            bool quarta = false;
            bool quinta = false;
            bool sexta = false;
            bool sabado = false;

            // verifica string do cookie
            if (dias_semana == null)
            {
                // se estiver vazia preenche segunda a sexta
                dias_semana = "1/2/3/4/5";
            }

            // divide string
            string[] dias_semana_aux = dias_semana.Split('/');
            List<int> dias_semana_calc = new List<int>();

            // percorre array
            if (dias_semana_aux.Count() > 0)
            {
                // domingo
                if (dias_semana_aux.Contains("0"))
                {
                    domingo = true;
                    dias_semana_calc.Add(0);
                }
                // segunda
                if (dias_semana_aux.Contains("1"))
                {
                    segunda = true;
                    dias_semana_calc.Add(1);
                }
                // terca
                if (dias_semana_aux.Contains("2"))
                {
                    terca = true;
                    dias_semana_calc.Add(2);
                }
                // quarta
                if (dias_semana_aux.Contains("3"))
                {
                    quarta = true;
                    dias_semana_calc.Add(3);
                }
                // quinta
                if (dias_semana_aux.Contains("4"))
                {
                    quinta = true;
                    dias_semana_calc.Add(4);
                }
                // sexta
                if (dias_semana_aux.Contains("5"))
                {
                    sexta = true;
                    dias_semana_calc.Add(5);
                }
                // sabado
                if (dias_semana_aux.Contains("6"))
                {
                    sabado = true;
                    dias_semana_calc.Add(6);
                }
            }

            // salva cookie
            CookieStore.SalvaCookie_String("Dias_Semana", dias_semana);

            // passa valores para views
            ViewBag.Dom = domingo;
            ViewBag.Seg = segunda;
            ViewBag.Ter = terca;
            ViewBag.Qua = quarta;
            ViewBag.Qui = quinta;
            ViewBag.Sex = sexta;
            ViewBag.Sab = sabado;

            // funcao relatorio
            int retorno;

            // resultado
            PAINEL_JBS_DEM_MED_MIN_MAX painel = new PAINEL_JBS_DEM_MED_MIN_MAX();

            // calcula
            retorno = PainelJBS_Calcular_DemMedMinMax(IDCliente, IDMedicao, data_ini, data_fim, data_ref, dias_semana_calc, ref painel);

            // converte data e hora
            DateTime DataAtual = painel.Data;

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno Atv {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            //
            // GRAFICO DEMANDA MEDIA - DIÁRIO
            //

            // grafico demanda media
            var DemandaMedia = new double[98];
            var PeriodoMedia = new int[98];
            var Contrato = new double[98];
            var Tolerancia = new double[98];
            var Datas = new string[98];
            var DatasN = new DateTime[98];
            var Horas = new string[98];

            double Dem_max_grafico = 0.0;

            // valores 
            DateTime strData = painel.Data;

            // volta 15 min para primeira barra
            strData = strData.AddMinutes(-15);

            int i = 0;
            int j = 0;

            for (i = 0; i < 98; i++)
            {
                // formata label 
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Horas[i] = strData.ToString("HH:mm");

                // proximos 15 minutos 
                strData = strData.AddMinutes(15);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera 
                    DemandaMedia[i] = painel.DemandaMedia[0];
                    PeriodoMedia[i] = painel.PeriodoMedia[0];
                    Contrato[i] = painel.ContratoFP;
                    Tolerancia[i] = painel.ToleranciaFP;
                }

                // verifica se ultima barra 
                if (i == 97)
                {
                    // zera 
                    DemandaMedia[i] = painel.PeriodoMedia[95];
                    PeriodoMedia[i] = painel.PeriodoMedia[95];
                    Contrato[i] = painel.ContratoFP;
                    Tolerancia[i] = painel.ToleranciaFP;
                }

                if (i >= 1 && i <= 96)
                {
                    // copia 
                    j = i - 1;

                    DemandaMedia[i] = painel.DemandaMedia[j];
                    PeriodoMedia[i] = painel.PeriodoMedia[j];

                    // tolerancia 
                    if (painel.PeriodoMedia[j] == 0)
                    {
                        Contrato[i] = painel.ContratoP;
                        Tolerancia[i] = painel.ToleranciaP;
                    }
                    else
                    {
                        Contrato[i] = painel.ContratoFP;
                        Tolerancia[i] = painel.ToleranciaFP;
                    }

                    // verifica se sem registro
                    if (painel.PeriodoMedia[j] == 3)
                    {
                        DemandaMedia[i] = 0.0;
                    }

                    // verifica demanda maxima 
                    if (DemandaMedia[i] > Dem_max_grafico)
                        Dem_max_grafico = DemandaMedia[i];

                    if (Contrato[i] > Dem_max_grafico)
                        Dem_max_grafico = Contrato[i];

                    if (Tolerancia[i] > Dem_max_grafico)
                        Dem_max_grafico = Tolerancia[i];
                }
            }

            Dem_max_grafico = Dem_max_grafico * 1.1;

            if (Dem_max_grafico < 1.0)
            {
                Dem_max_grafico = 1.0;
            }

            ViewBag.DemMaxGrafico = Dem_max_grafico;

            // copia valore do grafico para view 
            ViewBag.DemandaMedia = DemandaMedia;
            ViewBag.PeriodoMedia = PeriodoMedia;
            ViewBag.Contrato = Contrato;
            ViewBag.Tolerancia = Tolerancia;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Horas = Horas;

            //
            // GRAFICO DEMANDA MAXIMA/MINIMA - MENSAL
            //

            // grafico demanda minima 
            var DemandaMinP = new double[62];
            var DemandaMinFP = new double[62];

            // grafico demanda maxima
            var DemandaMaxP = new double[62];
            var DemandaMaxFP = new double[62];

            // valores grafico mensal 
            var ContratoP = new double[62];
            var ContratoFP = new double[62];
            var ToleranciaP = new double[62];
            var ToleranciaFP = new double[62];
            var Datas_Mes = new string[62];
            var DatasN_Mes = new DateTime[62];
            var Dias = new string[62];
            var PossuiRegistrosP = new bool[62];
            var PossuiRegistrosFP = new bool[62];

            double Dem_max_grafico_P = 0.0;
            double Dem_max_grafico_FP = 0.0;

            // numero total de dias
            int NumDiasMes = (int)(data_fim - data_ini).TotalDays;

            // valores 
            DateTime strDataMensal = painel.Data;

            // volta 1 dia para a primeira barra 
            strDataMensal = strDataMensal.AddDays(-1);

            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // inicia dia da semana
                PossuiRegistrosP[i] = true;
                PossuiRegistrosFP[i] = true;

                // formata label 
                Datas_Mes[i] = strDataMensal.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Mes[i] = strDataMensal;
                Dias[i] = strDataMensal.ToString("dd/MM");

                // proximo dia 
                strDataMensal = strDataMensal.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    DemandaMinP[i] = painel.DemandaMinP[0];
                    DemandaMinFP[i] = painel.DemandaMinFP[0];
                    DemandaMaxP[i] = painel.DemandaMaxP[0];
                    DemandaMaxFP[i] = painel.DemandaMaxFP[0];
                    PossuiRegistrosP[i] = painel.PossuiRegistroP[0];
                    PossuiRegistrosFP[i] = painel.PossuiRegistroFP[0];
                    ContratoP[i] = painel.ContratoP;
                    ContratoFP[i] = painel.ContratoFP;
                    ToleranciaP[i] = painel.ToleranciaP;
                    ToleranciaFP[i] = painel.ToleranciaFP;
                }

                if (i == (NumDiasMes + 1))
                {
                    // zera 
                    DemandaMinP[i] = painel.DemandaMinP[NumDiasMes - 1];
                    DemandaMinFP[i] = painel.DemandaMinFP[NumDiasMes - 1];
                    DemandaMaxP[i] = painel.DemandaMaxP[NumDiasMes - 1];
                    DemandaMaxFP[i] = painel.DemandaMaxFP[NumDiasMes - 1];
                    PossuiRegistrosP[i] = painel.PossuiRegistroP[NumDiasMes - 1];
                    PossuiRegistrosFP[i] = painel.PossuiRegistroFP[NumDiasMes - 1];
                    ContratoP[i] = painel.ContratoP;
                    ContratoFP[i] = painel.ContratoFP;
                    ToleranciaP[i] = painel.ToleranciaP;
                    ToleranciaFP[i] = painel.ToleranciaFP;
                }

                if (i >= 1 && i <= NumDiasMes)
                {
                    // copia 
                    j = i - 1;

                    DemandaMinP[i] = painel.DemandaMinP[j];
                    DemandaMinFP[i] = painel.DemandaMinFP[j];
                    DemandaMaxP[i] = painel.DemandaMaxP[j];
                    DemandaMaxFP[i] = painel.DemandaMaxFP[j];
                    ContratoP[i] = painel.ContratoP;
                    ContratoFP[i] = painel.ContratoFP;
                    ToleranciaP[i] = painel.ToleranciaP;
                    ToleranciaFP[i] = painel.ToleranciaFP;

                    // verifica demanda maxima ponta
                    if (Dem_max_grafico_P < DemandaMinP[i])
                        Dem_max_grafico_P = DemandaMinP[i];

                    if (Dem_max_grafico_P < DemandaMaxP[i])
                        Dem_max_grafico_P = DemandaMaxP[i];

                    if (Dem_max_grafico_P < painel.ContratoP)
                        Dem_max_grafico_P = painel.ContratoP;

                    if (Dem_max_grafico_P < painel.ToleranciaP)
                        Dem_max_grafico_P = painel.ToleranciaP;

                    // verifica demanda maxima fora ponta 
                    if (Dem_max_grafico_FP < DemandaMinFP[i])
                        Dem_max_grafico_FP = DemandaMinFP[i];

                    if (Dem_max_grafico_FP < DemandaMaxFP[i])
                        Dem_max_grafico_FP = DemandaMaxFP[i];

                    if (Dem_max_grafico_FP < painel.ToleranciaFP)
                        Dem_max_grafico_FP = painel.ToleranciaFP;

                    // verifica se possui registro ponta
                    if (!painel.PossuiRegistroP[j])
                    {
                        PossuiRegistrosP[i] = false;
                    }

                    // verifica se possui registro fora ponta
                    if (!painel.PossuiRegistroFP[j])
                    {
                        PossuiRegistrosFP[i] = false;
                    }
                }
            }

            Dem_max_grafico_P = Dem_max_grafico_P * 1.1;

            if (Dem_max_grafico_P < 1.0)
            {
                Dem_max_grafico_P = 1.0;
            }

            ViewBag.DemMaxGraficoP = Dem_max_grafico_P;

            Dem_max_grafico_FP = Dem_max_grafico_FP * 1.1;

            if (Dem_max_grafico_FP < 1.0)
            {
                Dem_max_grafico_FP = 1.0;
            }

            ViewBag.DemMaxGraficoFP = Dem_max_grafico_FP;

            // dados gráfico 
            ViewBag.DemandaMinP = DemandaMinP;
            ViewBag.DemandaMinFP = DemandaMinFP;
            ViewBag.DemandaMaxP = DemandaMaxP;
            ViewBag.DemandaMaxFP = DemandaMaxFP;
            ViewBag.ContratoP = ContratoP;
            ViewBag.ContratoFP = ContratoFP;
            ViewBag.ToleranciaP = ToleranciaP;
            ViewBag.ToleranciaFP = ToleranciaFP;
            ViewBag.Datas_Mes = Datas_Mes;
            ViewBag.DatasN_Mes = DatasN_Mes;
            ViewBag.Dias = Dias;
            ViewBag.PossuiRegistrosP = PossuiRegistrosP;
            ViewBag.PossuiRegistrosFP = PossuiRegistrosFP;
            ViewBag.NumDiasMes = NumDiasMes;

            // nome - painel jbs demanda
            ViewBag.NomeRelat = string.Format("Painel JBS Demanda");

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;

            // demanda minima mes  ponta
            ViewBag.DemandaMinP_Mes = string.Format("{0:#,##0.0}", painel.DemandaMinP_Mes);

            // demanda minima mes fora ponta 
            ViewBag.DemandaMinFP_Mes = string.Format("{0:#,##0.0}", painel.DemandaMinFP_Mes);

            // demanda maxima mes ponta 
            ViewBag.DemandaMaxP_Mes = string.Format("{0:#,##0.0}", painel.DemandaMaxP_Mes);

            // demanda maxima mes fora ponta 
            ViewBag.DemandaMaxFP_Mes = string.Format("{0:#,##0.0}", painel.DemandaMaxFP_Mes);

            // demanda maxima mes referencia ponta
            ViewBag.DemandaMaxP_MesRef = string.Format("{0:#,##0.0}", painel.DemandaMaxP_MesRef);

            // demanda maxima mes referencia fora ponta
            ViewBag.DemandaMaxFP_MesRef = string.Format("{0:#,##0.0}", painel.DemandaMaxFP_MesRef);

            // contrato mês ponta 
            ViewBag.Contrato_MesP = string.Format("{0:#,##0.0}", painel.ContratoP);

            // contrato mês fora ponta 
            ViewBag.Contrato_MesFP = string.Format("{0:#,##0.0}", painel.ContratoFP);

            // tolerancia mês ponta 
            ViewBag.ToleranciaP_Mes = string.Format("{0:#,##0.0}", painel.ToleranciaP);

            // tolerancia mês fora ponta 
            ViewBag.ToleranciaFP_Mes = string.Format("{0:#,##0.0}", painel.ToleranciaFP);

            // tolerancia percentual mês ponta 
            ViewBag.ToleranciaP_Mes_Percentual = string.Format("{0:0}%", painel.ToleranciaP_Percentual);

            // tolerancia percentual mês fora ponta 
            ViewBag.ToleranciaFP_Mes_Percentual = string.Format("{0:0}%", painel.ToleranciaFP_Percentual);

            // diferenca ponta
            ViewBag.DiferencaP_Mes = string.Format("{0:#,##0.0}", painel.DiferencaP_Mes);

            // diferenca fora ponta
            ViewBag.DiferencaFP_Mes = string.Format("{0:#,##0.0}", painel.DiferencaFP_Mes);

            // diferenca ponta mes referencia
            ViewBag.DiferencaP_MesRef = string.Format("{0:#,##0.0}", painel.DiferencaP_MesRef);

            // diferenca fora ponta mes referencia
            ViewBag.DiferencaFP_MesRef = string.Format("{0:#,##0.0}", painel.DiferencaFP_MesRef);

            return;
        }
    }
}