﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class PainelJBSController
    {

        // Calcula Demanda Média/Mínima/Máxima
        private int PainelJBS_Calcular_DemMedMinMax(int IDCliente, int IDMedicao, DateTime DataIni, DateTime DataFim, DateTime DataRef, List<int> DiasSemana, ref PAINEL_JBS_DEM_MED_MIN_MAX resultado)
        {
            // verifica se estrutura existe
            if (resultado == null)
            {
                // erro
                return (-1);
            }           

            // data inicial e final (mês)
            DataIni = new DateTime(DataIni.Year, DataIni.Month, DataIni.Day, 0, 15, 0);
            DataFim = new DateTime(DataFim.Year, DataFim.Month, DataFim.Day, 0, 0, 0).AddDays(1);

            // data inicial e final (mês referência)
            DateTime DataIniRef = new DateTime(DataRef.Year, DataRef.Month, 1, 0, 15, 0);
            DateTime DataFimRef = DataIniRef.AddMonths(1).AddMinutes(-15);

            // medicao
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);

            if (medicao == null)
            {
                // erro
                return (-1);
            }

            // ler registros do mês
            EN_Metodos enMetodos = new EN_Metodos();
            List<EN_Dominio> registros = enMetodos.ListarTodosPeriodo(IDCliente, IDMedicao, DataIni, DataFim, medicao);

            

            if (registros == null)
            {
                // erro
                return (-1);
            }

            // resultado
            PAINEL_JBS_DEM_MED_MIN_MAX result = new PAINEL_JBS_DEM_MED_MIN_MAX();

            result.IDCliente = IDCliente;
            result.IDMedicao = IDMedicao;
            result.Data = DataIni;

            // medias
            int[] NumRegMedia = new int[96];
            int[] PeriodoMedia = new int[96];

            // zera
            for (int i = 0; i < 96; i++)
            {
                result.DemandaMedia[i] = 0.0;
                NumRegMedia[i] = 0;
                result.PeriodoMedia[i] = -1;
            }

            // mínimos e máximo
            for (int i = 0; i < 62; i++)
            {
                result.DemandaMinP[i] = 0.0;
                result.DemandaMaxP[i] = 0.0;

                result.DemandaMinFP[i] = 0.0;
                result.DemandaMaxFP[i] = 0.0;

                result.PossuiRegistroP[i] = false;
                result.PossuiRegistroFP[i] = false;
            }

            // percorres registros
            foreach (EN_Dominio reg in registros)
            {
                //
                // Desloca dia do registro
                //
                // O dia do registro é deslocado 15min pois o registro indica o fim do intervalor de medição, ou seja,
                // o registro das 10:45, indica a demanda das 10:00:01 até 10:45:00
                // Por conta disso o registro das 00:00 é na verdade a demanda do dia anterior e portanto o dia da semana anterior também.
                DateTime DataHora_reg = reg.DataHora.AddMinutes(-15);

                //
                // Verifica dia da semana
                //

                // verifica se dia da semanha habilitado
                int dia_semana = (int)DataHora_reg.DayOfWeek;


                if (!DiasSemana.Contains(dia_semana))
                {
                    // ignora dia da semana
                    continue;
                }


                //
                // Demanda Média por intervalo de 15min
                //

                // calcula indice hora deslocado em 15min. (0 até 95)
                // 00:00 = 0
                // 00:15 = 1
                // ...
                // 23:45 = 95
                int indice_hora = (DataHora_reg.Hour * 4) + (DataHora_reg.Minute / 15);

                // coloca na media
                result.DemandaMedia[indice_hora] += reg.Ativo;
                NumRegMedia[indice_hora] += 1;

                if (result.PeriodoMedia[indice_hora] < 0 || reg.Periodo == 0)
                {
                    result.PeriodoMedia[indice_hora] = reg.Periodo;
                }
            }

            // Calcula média
            for (int i = 0; i < 96; i++)
            {
                if (NumRegMedia[i] > 0)
                {
                    result.DemandaMedia[i] = result.DemandaMedia[i] / NumRegMedia[i];
                }
            }

            //
            // Demanda Mínima e Máxima por dia
            //

            if (registros.Count > 0)
            {
                // calcula numero de dias 
                int total_dias = (int)((DataFim - DataIni).TotalDays);

                // inicio do dia
                DateTime inicio_dia = DataIni;

                // fim dia
                DateTime fim_dia = inicio_dia.AddDays(1).AddMinutes(-15);

                for (int indice_dia = 0; indice_dia < total_dias; indice_dia++)
                {
                    // verifica se tem registro no dia ponta
                    if (registros.Exists(registro => registro.DataHora >= inicio_dia && registro.DataHora <= fim_dia && registro.Periodo == 0 && DiasSemana.Contains((int)inicio_dia.DayOfWeek)))
                    {
                        // verifica se ainda nao encontrou
                        if (!result.PossuiRegistroP[indice_dia])
                        {
                            result.DemandaMinP[indice_dia] = registros
                                    .Where(registro => registro.DataHora >= inicio_dia && registro.DataHora <= fim_dia && registro.Periodo == 0 && DiasSemana.Contains((int)inicio_dia.DayOfWeek))
                                    .Min(registro => registro.Ativo);

                            result.DemandaMaxP[indice_dia] = registros
                                                            .Where(registro => registro.DataHora >= inicio_dia && registro.DataHora <= fim_dia && registro.Periodo == 0 && DiasSemana.Contains((int)inicio_dia.DayOfWeek))
                                                            .Max(registro => registro.Ativo);
                        }

                        result.PossuiRegistroP[indice_dia] = true;
                    }

                    // verifica se tem registro no dia fora ponta
                    if (registros.Exists(registro => registro.DataHora >= inicio_dia && registro.DataHora <= fim_dia && registro.Periodo != 0 && DiasSemana.Contains((int)inicio_dia.DayOfWeek)))
                    {
                        if (!result.PossuiRegistroFP[indice_dia])
                        {
                            result.DemandaMinFP[indice_dia] = registros
                                                            .Where(registro => registro.DataHora >= inicio_dia && registro.DataHora <= fim_dia && registro.Periodo != 0 && DiasSemana.Contains((int)inicio_dia.DayOfWeek))
                                                            .Min(registro => registro.Ativo);

                            result.DemandaMaxFP[indice_dia] = registros
                                                            .Where(registro => registro.DataHora >= inicio_dia && registro.DataHora <= fim_dia && registro.Periodo != 0 && DiasSemana.Contains((int)inicio_dia.DayOfWeek))
                                                            .Max(registro => registro.Ativo);
                        }


                        result.PossuiRegistroFP[indice_dia] = true;
                    }

                    // proximo dia
                    inicio_dia = inicio_dia.AddDays(1);
                    fim_dia = fim_dia.AddDays(1);
                }
            }

            // Mínima e Máxima
            // verifica se existe registro horario ponta ignorando os fins de semana
            if (registros.Exists(registro => registro.Periodo == 0 && DiasSemana.Contains((int)registro.DataHora.DayOfWeek)))
            {
                // minimo ponta
                result.DemandaMinP_Mes = registros
                        .Where(registro => registro.Periodo == 0 && DiasSemana.Contains((int)registro.DataHora.DayOfWeek))
                        .Min(registro => registro.Ativo);

                // maximo ponta
                result.DemandaMaxP_Mes = registros
                        .Where(registro => registro.Periodo == 0 && DiasSemana.Contains((int)registro.DataHora.DayOfWeek))
                        .Max(registro => registro.Ativo);
            }


            // verifica se existe registro horario fora ponta ignorando os fins de semana
            if (registros.Exists(registro => registro.Periodo == 1 || registro.Periodo == 2 && DiasSemana.Contains((int)registro.DataHora.DayOfWeek)))
            {
                // minimo fora ponta
                result.DemandaMinFP_Mes = registros
                        .Where(registro => registro.Periodo == 1 || registro.Periodo == 2 && DiasSemana.Contains((int)registro.DataHora.DayOfWeek))
                        .Min(registro => registro.Ativo);

                // maximo fora ponta
                result.DemandaMaxFP_Mes = registros
                        .Where(registro => registro.Periodo == 1 || registro.Periodo == 2 && DiasSemana.Contains((int)registro.DataHora.DayOfWeek))
                        .Max(registro => registro.Ativo);
            }

            // contrato
            HistoricoContratosMetodos historicoMetodos = new HistoricoContratosMetodos();
            HistoricoContratosDominio contrato = historicoMetodos.ListarPorIDMedicaoMaisRecenteData(IDMedicao, DataIni);



            if (contrato != null)
            {
                result.ContratoP = contrato.ContratoDemP;
                result.ToleranciaP = (1.0 + (medicao.ToleranciaDemP / 100.0)) * contrato.ContratoDemP;

                result.ContratoFP = contrato.ContratoDemFP;
                result.ToleranciaFP = (1.0 + (medicao.ToleranciaDemFP / 100.0)) * contrato.ContratoDemFP;

                // tolerancia percentual 
                result.ToleranciaP_Percentual = medicao.ToleranciaDemP;
                result.ToleranciaFP_Percentual = medicao.ToleranciaDemFP;
            }

            // diferença entre contrato e máximo
            result.DiferencaP_Mes = result.ToleranciaP - result.DemandaMaxP_Mes;
            result.DiferencaFP_Mes = result.ToleranciaFP - result.DemandaMaxFP_Mes;

            // maximo mes referencia ponta
            result.DemandaMaxP_MesRef = enMetodos.DemandaMaximaPeriodo(IDCliente, IDMedicao, DataIniRef, DataFimRef, 0);

            // maximo mes referencia fora ponta
            result.DemandaMaxFP_MesRef = enMetodos.DemandaMaximaPeriodo(IDCliente, IDMedicao, DataIniRef, DataFimRef, 1);

            // diferenca mes de referencia
            result.DiferencaFP_MesRef = result.ToleranciaFP - result.DemandaMaxFP_MesRef;
            result.DiferencaP_MesRef = result.ToleranciaP - result.DemandaMaxP_MesRef;

            // copia resultado
            resultado = result;

            if (registros.Count <= 0)
            {
                // sem registros
                return (2);
            }

            // ok
            return (0);
        }


        // Calcula Multas
        private int PainelJBS_Calcular_Multas(int IDCliente, int IDMedicao, DateTime Data, ref PAINEL_JBS_MULTAS resultado)
        {
            // verifica se estrutura existe
            if (resultado == null)
            {
                // erro
                return (-1);
            }


            // data inicial e final (mês)
            DateTime DataIni = new DateTime(Data.Year, Data.Month, Data.Day, 0, 15, 0);
            DateTime DataFim = DataIni.AddDays(1);
            DataFim = DataFim.AddMinutes(-15);

            // medicao
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);

            if (medicao == null)
            {
                // erro
                return (-1);
            }

            // estrutura de resultado
            PAINEL_JBS_MULTAS result = new PAINEL_JBS_MULTAS();

            result.IDCliente = IDCliente;
            result.IDMedicao = IDMedicao;
            result.NomeMedicao = medicao.Nome;
            result.Data = DataIni;
            result.Multa_Demanda = 0.0;
            result.Multa_FatPot = 0.0;
            result.Total = 0.0;




            // copia resultado
            resultado = result;

            // ok
            return (0);
        }
    }
}