﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{

    public partial class PainelJBSController
    {
        // GET: Tabela Medicoes
        public PartialViewResult _TabelaMedicoes()
        {
            // em caso de alterar tabela medicoes, apago tipo fatura
            CookieStore.DeleteCookie("Fatura_TipoContrato");
            CookieStore.DeleteCookie("Fatura_TipoFatura");

            // le cookies
            LeCookies_SmartEnergy();

            // IDs
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;
            int IDTipoMedicao = ViewBag._IDTipoMedicao;
            int IDTipoSupervisao = ViewBag._IDTipoSupervisao;
            string TipoPaginaAtual = "";
            List<int> ConfigMedList = ViewBag._ConfigMed;

            if (ViewBag.TipoPaginaAtual != null)
            {
                TipoPaginaAtual = ViewBag.TipoPaginaAtual;
            }

            // le medicoes
            SupervMedicoesMetodos medicoesMetodos = new SupervMedicoesMetodos();
            List<SupervMedicoesDominio> medicoes = medicoesMetodos.ListarPorIDCliente(IDCliente, IDTipoSupervisao, IDUsuario, IDTipoAcesso, ConfigMedList, false);

            // somente medições de energia
            List<SupervMedicoesDominio> medicoes_energia = new List<SupervMedicoesDominio>();

            // percorre medicoes
            if (medicoes != null)
            {
                foreach(SupervMedicoesDominio medicao in medicoes)
                {
                    if (medicao.IDTipoMedicao_MD == TIPO_MEDICAO.ENERGIA)
                    {
                        // coloca na lista
                        medicoes_energia.Add(medicao);
                    }
                }
            }

            ViewBag.TabelaMedicoes = medicoes_energia;

            return PartialView();
        }
    }
}