﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class PainelJBSController
    {
        // fatura de energia eletrica
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_Fatura", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_Fatura(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, int id_simulacao, char tipo_contrato, char IDEstTarifaria, char <PERSON>h<PERSON><PERSON><PERSON><PERSON>, ref DATAHOR<PERSON> pdatahora_ini, ref DATAHOR<PERSON> pdatahora_fim, ref RESULT_ENERGIA_FATURA pfatura, ref RESULT_ENERGIA_FATURA pfatura_sim);


        // GET: Painel JBS Multas 
        public ActionResult PainelJBS_Multas(int IDCliente, int tipo_arquivo = 0)
        {
            // Painel JBS Multas 
            return (PainelJBS_Multas_Show(IDCliente, tipo_arquivo));
        }

        // GET: Painel JBS Multas 
        private ActionResult PainelJBS_Multas_Show(int IDCliente, int tipo_arquivo = 0)
        {
            // tela de ajuda - Painel JBS Multas
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "PainelJBS_Multas");

            // le cookies
            LeCookies_SmartEnergy();

            // data inicio
            DateTime data_hora_ini = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 0, 0);

            // data fim
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            // le cookie datahora
            DateTime data_hora_ini_cookie = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime data_hora_fim_cookie = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // verifica se existe datahora no cookie
            if (data_hora_ini_cookie.Year != 2000 && data_hora_fim_cookie.Year != 2000)
            {
                // copia data do cookie 
                data_hora_ini = data_hora_ini_cookie;
                data_hora_fim = data_hora_fim_cookie;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", data_hora_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", data_hora_fim);

            // data e hora atual
            ViewBag.DataIni = string.Format("{0:d}", data_hora_ini);
            ViewBag.HoraIni = string.Format("{0:HH:mm}", data_hora_ini);
            ViewBag.DataFim = string.Format("{0:d}", data_hora_fim);
            ViewBag.HoraFim = string.Format("{0:HH:mm}", data_hora_fim);

            ViewBag.DataTextoAtualIni = string.Format("{0:d} {1:HH:mm}", data_hora_ini, data_hora_ini);
            ViewBag.DataTextoAtualFim = string.Format("{0:d} {1:HH:mm}", data_hora_fim, data_hora_fim);

            ViewBag.DataAtualN = data_hora_ini;
            ViewBag.DataFinalN = data_hora_fim;

            return View();
        }

        // GET: Painel JBS Multas - Atualizar
        public ActionResult PainelJBS_Multas_Atualizar(int Navegacao, string DataIni, string DataFim)
        {
            // caso forcar data
            if (Navegacao == 100)
            {
                // pega data
                DateTime dateValueIni = DateTime.Parse(DataIni);
                DateTime dateValueFim = DateTime.Parse(DataFim);

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Fatura_DataIni", dateValueIni);
                CookieStore.SalvaCookie_Datahora("Fatura_DataFim", dateValueFim);
            }

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;

            // le cookie datahora
            DateTime datahora_cookie_ini = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -3:    // mes anterior

                    datahora_cookie_ini = datahora_cookie_ini.AddMonths(-1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(-1);
                    break;

                case 3:    // mes seguinte

                    datahora_cookie_ini = datahora_cookie_ini.AddMonths(1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(1);
                    break;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", datahora_cookie_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", datahora_cookie_fim);

            // data e hora atual
            ViewBag.DataIni = string.Format("{0:d}", datahora_cookie_ini);
            ViewBag.HoraIni = string.Format("{0:HH:mm}", datahora_cookie_ini);
            ViewBag.DataFim = string.Format("{0:d}", datahora_cookie_fim);
            ViewBag.HoraFim = string.Format("{0:HH:mm}", datahora_cookie_fim);

            ViewBag.DataTextoAtualIni = string.Format("{0:d} {1:HH:mm:ss}", datahora_cookie_ini, datahora_cookie_ini);
            ViewBag.DataTextoAtualFim = string.Format("{0:d} {1:HH:mm:ss}", datahora_cookie_fim, datahora_cookie_fim);

            ViewBag.DataAtualN = datahora_cookie_ini;
            ViewBag.DataFinalN = datahora_cookie_fim;

            // retorna status
            return Json(0, JsonRequestBehavior.AllowGet);
        }

        // GET: Painel JBS Multas Print
        public ActionResult PainelJBS_Multas_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            PainelJBS_Multas_Show(IDCliente);

            // resultado
            ViewBag.multas_resultado = lista_multas.Keys.Contains(id) ? lista_multas[id] : null;

            // imprime
            return View();
        }

        // GET: Painel JBS Multas EMAIL
        public async Task<ActionResult> PainelJBS_Multas_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            PainelJBS_Multas_Show(IDCliente);

            // resultado
            ViewBag.multas_resultado = lista_multas.Keys.Contains(id) ? lista_multas[id] : null;

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Multas_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDCliente, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_PainelJBS_Multas_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "PainelJBSMultasEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Painel JBS Multas PDF
        public ActionResult PainelJBS_Multas_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            PainelJBS_Multas_Show(IDCliente);

            // resultado
            ViewBag.multas_resultado = lista_multas.Keys.Contains(id) ? lista_multas[id] : null;

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Multas_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDCliente, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_PainelJBS_Multas_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Painel JBS Multas XLS
        public ActionResult PainelJBS_Multas_XLS()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            PainelJBS_Multas_Show(IDCliente);

            // resultado
            ViewBag.multas_resultado = lista_multas.Keys.Contains(id) ? lista_multas[id] : null;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_Multas(IDCliente);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Multas_{0:000000}_{1:yyyyMMddHHmm}.xls", IDCliente, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Painel JBS Multas XLS Download
        [HttpGet]
        public virtual ActionResult PainelJBS_Multas_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }


        // estruturas
        private static IDictionary<Guid, int> tasks_multas = new Dictionary<Guid, int>();
        private List<FATURA_RESUMIDA> lista_multas_tmp = new List<FATURA_RESUMIDA>();
        private static IDictionary<Guid, List<FATURA_RESUMIDA>> lista_multas = new Dictionary<Guid, List<FATURA_RESUMIDA>>();

        public ActionResult PainelJBS_Multas_IniciaCalculo()
        {
            // taskID
            Guid taskId = Guid.NewGuid();
            tasks_multas.Add(taskId, 0);

            // salva cookie
            CookieStore.SalvaCookie_GUID("taskID", taskId);

            // le cookies
            LeCookies_SmartEnergy();

            // simulação
            int IDSimulacaoCenario = 0;

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // lista de medicoes habilitadas do usuario
            List<int> ConfigMedicaoList_Usuario = new List<int>();
            ConfigMedicaoList_Usuario = ViewBag._ConfigMed;

            // verifica se existe lista de medicoes
            if (ConfigMedicaoList_Usuario != null)
            {
                if (ConfigMedicaoList_Usuario.Count == 0)
                {
                    ConfigMedicaoList_Usuario = null;
                }
            }

            // le cookie datahora
            DateTime datahora_ini = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime datahora_fim = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // limpa lista
            lista_multas_tmp.Clear();

            // task
            Task.Factory.StartNew(() =>
            {
                // le medicoes
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                List<MedicoesDominio> medicoes = medicoesMetodos.ListarPrincipais(IDCliente);

                // estruturas

                // lista de erros
                var listaErros = new List<string>();

                // preenche solicitacao
                CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
                config_interface.sweb.id_cliente = IDCliente;

                // converte
                DATAHORA dh_ini = new DATAHORA();
                DATAHORA dh_fim = new DATAHORA();
                Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, datahora_ini);
                Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, datahora_fim);

                // estrutura
                RESULT_ENERGIA_FATURA fatura = new RESULT_ENERGIA_FATURA();
                RESULT_ENERGIA_FATURA fatura_sim = new RESULT_ENERGIA_FATURA();

                // barra de progresso
                int total = 0;
                int atual = 0;

                // percorre medicoes
                if (medicoes != null)
                {
                    // barra de progresso
                    total = medicoes.Count();

                    // percorre medicoes
                    foreach (MedicoesDominio medicao in medicoes)
                    {
                        // update task progress
                        double progresso = ((double)atual / (double)total) * 100;
                        tasks_multas[taskId] = (int)progresso;
                        atual++;

                        // verifica se existe lista de medicoes
                        if (ConfigMedicaoList_Usuario != null)
                        {
                            // verifica se medicao NAO esta habilitada para o usuario
                            if (!ConfigMedicaoList_Usuario.Contains(medicao.IDMedicao))
                            {
                                // pula medicao
                                continue;
                            }
                        }

                        // verifica se nao eh energia ou medição principal (unidade consumidora)
                        if (medicao.IDTipoMedicao != TIPO_MEDICAO.ENERGIA || medicao.IDCategoriaMedicao != CATEGORIA_MEDICAO.PRINCIPAL)
                        {
                            continue;
                        }

                        // preenche solicitacao
                        config_interface.sweb.id_medicao = medicao.IDMedicao;
                        config_interface.sweb.id_gateway = medicao.IDGateway;

                        // demanda historica
                        fatura.demanda_hist = 0.0;

                        // calcula valores fatura (sem simulação)
                        int retorno = SmCalcDB_Energia_Fatura((char)0, ref config_interface, IDSimulacaoCenario, (char)medicao.IDContratoMedicao, (char)medicao.IDEstruturaTarifaria, (char)0, ref dh_ini, ref dh_fim, ref fatura, ref fatura_sim);

                        // coloca resultado na lista temporaria
                        FATURA_RESUMIDA fatura_resumida = new FATURA_RESUMIDA();
                        fatura_resumida.IDMedicao = medicao.IDMedicao;
                        fatura_resumida.Nome_Medicao = medicao.Nome;
                        fatura_resumida.IDEstruturaTarifaria = medicao.IDEstruturaTarifaria;
                        fatura_resumida.retorno = retorno;
                        fatura_resumida.fatura = fatura;

                        // coloca no temporario
                        lista_multas_tmp.Add(fatura_resumida);
                    }
                }

                // coloca resultado na lista
                lista_multas.Add(taskId, new List<FATURA_RESUMIDA>(lista_multas_tmp));

                // terminou
                tasks_multas.Remove(taskId);
            });

            // retorna status
            return Json(taskId, JsonRequestBehavior.AllowGet);
        }

        public ActionResult PainelJBS_Multas_Progress(Guid id)
        {
            return Json(tasks_multas.Keys.Contains(id) ? tasks_multas[id] : 100, JsonRequestBehavior.AllowGet);
        }

        public PartialViewResult _PainelJBS_Multas(Guid id)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // resultado
            ViewBag.multas_resultado = lista_multas.Keys.Contains(id) ? lista_multas[id] : null;

            return PartialView();
        }


        // Painel JBS Multas XLS
        private HSSFWorkbook XLS_Multas(int IDCliente)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            ICellStyle _textoBorderCellStyle = criaEstiloXLS(workbook, 100, true);
            ICellStyle _1CellBorderStyle = criaEstiloXLS(workbook, 1, true);
            ICellStyle _2CellBorderStyle = criaEstiloXLS(workbook, 2, true);

            //
            // TOTAIS
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Multas");

            // cabecalho
            string[] cabecalhoTotal = { "", "Total Multas Fator de Potência (R$)", "Total Multas Demanda (R$)", "Total de Multas (R$)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalhoTotal);

            // adiciona linhas
            List<FATURA_RESUMIDA> multas_resultado = ViewBag.multas_resultado;
            int i;
            IRow row;

            // calcula totais
            double Total_Multa_FatPot = 0.0;
            double Total_Multa_Dem = 0.0;
            double Total_Multas = 0.0;

            if (multas_resultado != null)
            {
                foreach (FATURA_RESUMIDA fat in multas_resultado)
                {
                    if (fat.retorno == 0)
                    {
                        if (fat.IDEstruturaTarifaria == 0)
                        {
                            Total_Multa_FatPot += fat.fatura.fdr_p[CF.TOT] + fat.fatura.fdr_fp[CF.TOT] +
                                                  fat.fatura.fdr_pe_p[CF.TOT] + fat.fatura.fdr_pe_fp[CF.TOT] +
                                                  fat.fatura.fer_p[CF.TOT] + fat.fatura.fer_fpi[CF.TOT] + fat.fatura.fer_fpc[CF.TOT];

                            Total_Multa_Dem += fat.fatura.ultr_demanda_p[CF.TOT] + fat.fatura.ultr_demanda_fp[CF.TOT] +
                                               fat.fatura.ultr_demanda_pe_p[CF.TOT] + fat.fatura.ultr_demanda_pe_fp[CF.TOT];
                        }
                        else
                        {
                            Total_Multa_FatPot += fat.fatura.fdr_p[CF.TOT] + fat.fatura.fdr_pe_p[CF.TOT] +
                                                  fat.fatura.fer_p[CF.TOT] + fat.fatura.fer_fpi[CF.TOT] + fat.fatura.fer_fpc[CF.TOT];

                            Total_Multa_Dem += fat.fatura.ultr_demanda_p[CF.TOT] + fat.fatura.ultr_demanda_pe_p[CF.TOT];
                        }
                    }
                }

                Total_Multas = Total_Multa_FatPot + Total_Multa_Dem;
            }

            // adiciona linha
            row = sheet.CreateRow(rowIndex++);

            // total
            textoCelulaXLS(row, 0, "Total", _negritoCellStyle);

            // totais
            numeroCelulaXLS(row, 1, Total_Multa_FatPot, _2CellStyle);
            numeroCelulaXLS(row, 2, Total_Multa_Dem, _2CellStyle);
            numeroCelulaXLS(row, 3, Total_Multas, _2CellStyle);

            // pula linhas
            row = sheet.CreateRow(rowIndex++);
            row = sheet.CreateRow(rowIndex++);

            //
            // MEDICOES
            //

            // cabecalho
            string[] cabecalho = { "Medições", "Multas Fator de Potência (R$)", "Multas Demanda (R$)", "Total de Multas (R$)" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, rowIndex);

            // percorre valores
            if (multas_resultado != null)
            {
                foreach (FATURA_RESUMIDA fat in multas_resultado)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex++);

                    // medicao
                    textoCelulaXLS(row, 0, fat.Nome_Medicao);

                    if (fat.retorno != 0)
                    {
                        textoCelulaXLS(row, 1, "---");
                        textoCelulaXLS(row, 2, "---");
                        textoCelulaXLS(row, 3, "---");
                    }
                    else
                    {
                        if (fat.IDEstruturaTarifaria == 0)
                        {
                            Total_Multa_FatPot = fat.fatura.fdr_p[CF.TOT] + fat.fatura.fdr_fp[CF.TOT] +
                                                 fat.fatura.fdr_pe_p[CF.TOT] + fat.fatura.fdr_pe_fp[CF.TOT] +
                                                 fat.fatura.fer_p[CF.TOT] + fat.fatura.fer_fpi[CF.TOT] + fat.fatura.fer_fpc[CF.TOT];

                            Total_Multa_Dem = fat.fatura.ultr_demanda_p[CF.TOT] + fat.fatura.ultr_demanda_fp[CF.TOT] +
                                                   fat.fatura.ultr_demanda_pe_p[CF.TOT] + fat.fatura.ultr_demanda_pe_fp[CF.TOT];

                            numeroCelulaXLS(row, 1, Total_Multa_FatPot, _2CellStyle);
                            numeroCelulaXLS(row, 2, Total_Multa_Dem, _2CellStyle);
                            numeroCelulaXLS(row, 3, Total_Multa_FatPot + Total_Multa_Dem, _2CellStyle);
                        }
                        else
                        {
                            Total_Multa_FatPot = fat.fatura.fdr_p[CF.TOT] + fat.fatura.fdr_pe_p[CF.TOT] +
                                                 fat.fatura.fer_p[CF.TOT] + fat.fatura.fer_fpi[CF.TOT] + fat.fatura.fer_fpc[CF.TOT];

                            Total_Multa_Dem = fat.fatura.ultr_demanda_p[CF.TOT] + fat.fatura.ultr_demanda_pe_p[CF.TOT];

                            numeroCelulaXLS(row, 1, Total_Multa_FatPot, _2CellStyle);
                            numeroCelulaXLS(row, 2, Total_Multa_Dem, _2CellStyle);
                            numeroCelulaXLS(row, 3, Total_Multa_FatPot + Total_Multa_Dem, _2CellStyle);
                        }
                    }
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 9000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS_Multas(workbook, sheet, _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(4).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }
    }
}