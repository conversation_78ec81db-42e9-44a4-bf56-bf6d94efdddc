﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SupervisaoController
    {

        // GET: Medicao_Meteorologia
        public unsafe ActionResult Medicao_Meteorologia(int IDCliente, int IDMedicao)
        {
            // tela de ajuda - supervisao medicao atual
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie cliente
            CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);

            // le contrato cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cli = clienteMetodos.ListarPorId(IDCliente);
            CookieStore.SalvaCookie_Int("IDTipoContrato", cli.IDTipoContrato);

            // le configuracao da medicao
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);

            // salva cookie medicao
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", medicao.IDTipoMedicao);
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Supervisao");
            CookieStore.SalvaCookie_Int("_IDGateway", -10);
            CookieStore.SalvaCookie_Int("IDTipoGateway", -10);

            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            
            // funcao de supervisao
            HistoricoWeatherMetodos historicoMetodos = new HistoricoWeatherMetodos();
            METEOROLOGIA_SUPERVMEDICAO superv = historicoMetodos.Supervisao(medicao.IDCidade);
           


            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (superv.Data_Atual.Year == 2000)
                    listaErros.Add("Data Inexistente");
            }

            @ViewBag.listaErros = listaErros;


            // datas
            ViewBag.DiaAtual = string.Format("{0:d}", superv.Data_Atual);
            ViewBag.MesAtual = string.Format("{0:Y}", superv.Data_Atual);
            ViewBag.MesAnterior = string.Format("{0:Y}", superv.Data_Atual.AddMonths(-1));

            ViewBag.NomeGrandeza = "Temperatura";
            ViewBag.UnidadeGrandeza = "°C";


            // le Cidade/Estado
            string CidadeNome = "---";
            string EstadoNome = "---";

            CidadeEstadoPaisMetodos cidadeMetodos = new CidadeEstadoPaisMetodos();
            CidadesDominio cidade = cidadeMetodos.CidadePorId(medicao.IDCidade);

            if (cidade != null)
            {
                // cidade
                CidadeNome = cidade.Nome;

                // le Estado
                EstadosDominio estado = cidadeMetodos.EstadoPorId(cidade.IDEstado);
                EstadoNome = estado.Nome;
            }

            ViewBag.CidadeNome = CidadeNome;
            ViewBag.EstadoNome = EstadoNome;


            // graficos
            var Dias = new string[26];
            var Maxima = new double[26];
            var Media = new double[26];
            var Minima = new double[26];

            var Umidade = new double[26];
            var Pressao = new double[26];
            var Vento_Direcao = new int[26];
            var Vento_Velocidade = new double[26];
            var Tempo_Cod = new int[26];


            double Valor_min = 0.0;
            double Valor_max = 0.0;

            // valores
            DateTime strData = superv.Data_Atual;

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proxima hora
                strData = strData.AddHours(1);

                // verifica se primeira barra
                if (i == 0)
                {
                    // copia do primeiro real
                    Media[0] = superv.registros[0].Temp_Atual;
                    Minima[0] = superv.registros[0].Temp_Min;
                    Maxima[0] = superv.registros[0].Temp_Max;

                    Umidade[0] = superv.registros[0].Umidade;
                    Pressao[0] = superv.registros[0].Pressao;
                    Vento_Direcao[0] = superv.registros[0].Vento_Direcao;
                    Vento_Velocidade[0] = superv.registros[0].Vento_Velocidade;
                    Tempo_Cod[0] = superv.registros[0].Tempo_Cod;

                    // minimo e maximo
                    Valor_min = Minima[0];
                    Valor_max = Maxima[0];
                }

                // verifica se ultima barra
                if (i == 25)
                {
                    // copia do ultimo real
                    Media[25] = superv.registros[23].Temp_Atual;
                    Minima[25] = superv.registros[23].Temp_Min;
                    Maxima[25] = superv.registros[23].Temp_Max;

                    Umidade[25] = superv.registros[23].Umidade;
                    Pressao[25] = superv.registros[23].Pressao;
                    Vento_Direcao[25] = superv.registros[23].Vento_Direcao;
                    Vento_Velocidade[25] = superv.registros[23].Vento_Velocidade;
                    Tempo_Cod[25] = superv.registros[23].Tempo_Cod;
                }

                // restante
                if (i > 0 && i < 25)
                {
                    // copia
                    j = i - 1;

                    Media[i] = superv.registros[j].Temp_Atual;
                    Minima[i] = superv.registros[j].Temp_Min;
                    Maxima[i] = superv.registros[j].Temp_Max;

                    Umidade[i] = superv.registros[j].Umidade;
                    Pressao[i] = superv.registros[j].Pressao;
                    Vento_Direcao[i] = superv.registros[j].Vento_Direcao;
                    Vento_Velocidade[i] = superv.registros[j].Vento_Velocidade;
                    Tempo_Cod[i] = superv.registros[j].Tempo_Cod;

                    // verifica minimo
                    if (Maxima[i] < Valor_min)
                        Valor_min = Maxima[i];

                    if (Media[i] < Valor_min)
                        Valor_min = Media[i];

                    if (Minima[i] < Valor_min)
                        Valor_min = Minima[i];

                    // verifica maximo
                    if (Minima[i] > Valor_max)
                        Valor_max = Minima[i];

                    if (Media[i] > Valor_max)
                        Valor_max = Media[i];

                    if (Maxima[i] > Valor_max)
                        Valor_max = Maxima[i];
                }
            }

            // valor máximo (5% e arredonda para cima)
            Valor_max = Valor_max + (Math.Abs(Valor_max) * 0.05);
            Valor_max = Math.Ceiling(Valor_max);

            // valor mínimo (5% e arredonda para baixo)
            Valor_min = Valor_min - (Math.Abs(Valor_min) * 0.05);
            Valor_min = Math.Floor(Valor_min);

            // copia valores
            ViewBag.ValorMax = Valor_max;
            ViewBag.ValorMin = Valor_min;

            ViewBag.Media = Media;
            ViewBag.Minima = Minima;
            ViewBag.Maxima = Maxima;
            ViewBag.Umidade = Umidade;
            ViewBag.Pressao = Pressao;
            ViewBag.Vento_Direcao = Vento_Direcao;
            ViewBag.Vento_Velocidade = Vento_Velocidade;
            ViewBag.Tempo_Cod = Tempo_Cod;
            ViewBag.Dias = Dias;

            // dia atual
            ViewBag.Dia_Atual_Data = string.Format("{0:M}", superv.Dia_Atual.Data);

            ViewBag.Dia_Atual_DataHora = string.Format("{0:d} {1:HH:mm}", superv.DataHora_Ultimo, superv.DataHora_Ultimo);
            ViewBag.Dia_Atual_Atual = string.Format("{0:0}", superv.Dia_Atual.Valor_Atual);
            ViewBag.Dia_Atual_Umidade = string.Format("{0:0}", superv.Dia_Atual.Umidade_Atual);
            ViewBag.Dia_Atual_Tempo_Cod = superv.Dia_Atual.Tempo_Cod;

            ViewBag.Dia_Atual_Media = string.Format("{0:0}", superv.Dia_Atual.Valor_Media);
            ViewBag.Dia_Atual_Min = string.Format("{0:0}", superv.Dia_Atual.Valor_Min);
            ViewBag.Dia_Atual_MinDataHora = string.Format("{0:t}", superv.Dia_Atual.Valor_MinDataHora);
            ViewBag.Dia_Atual_Max = string.Format("{0:0}", superv.Dia_Atual.Valor_Max);
            ViewBag.Dia_Atual_MaxDataHora = string.Format("{0:t}", superv.Dia_Atual.Valor_MaxDataHora);

            // mes atual
            ViewBag.Mes_Atual_Data = string.Format("{0:Y}", superv.Mes_Atual.Data);
            ViewBag.Mes_Atual_DataMes = string.Format("{0:MMMM}", superv.Mes_Atual.Data);

            if (superv.Mes_Atual.possui_registros)
            {
                ViewBag.Mes_Atual_Media = string.Format("{0:0}", superv.Mes_Atual.Valor_Media);
                ViewBag.Mes_Atual_Min = string.Format("{0:0}", superv.Mes_Atual.Valor_Min);
                ViewBag.Mes_Atual_MinDataHora = string.Format("{0:d} {1:HH:mm}", superv.Mes_Atual.Valor_MinDataHora, superv.Mes_Atual.Valor_MinDataHora);
                ViewBag.Mes_Atual_Max = string.Format("{0:0}", superv.Mes_Atual.Valor_Max);
                ViewBag.Mes_Atual_MaxDataHora = string.Format("{0:d} {1:HH:mm}", superv.Mes_Atual.Valor_MaxDataHora, superv.Mes_Atual.Valor_MaxDataHora);
            }
            else
            {
                ViewBag.Mes_Atual_Media = "---";
                ViewBag.Mes_Atual_Min = "---";
                ViewBag.Mes_Atual_MinDataHora = "--/--/---- --:--";
                ViewBag.Mes_Atual_Max = "---";
                ViewBag.Mes_Atual_MaxDataHora = "--/--/---- --:--";
            }

            ViewBag.Mes_Atual_MinN = superv.Mes_Atual.Valor_Min;
            ViewBag.Mes_Atual_MediaN = superv.Mes_Atual.Valor_Media;
            ViewBag.Mes_Atual_MaxN = superv.Mes_Atual.Valor_Max;

            // mes anterior 1
            ViewBag.Mes_Anterior1_Data = string.Format("{0:Y}", superv.Mes_Anterior1.Data);
            ViewBag.Mes_Anterior1_DataMes = string.Format("{0:MMMM}", superv.Mes_Anterior1.Data);

            if (superv.Mes_Anterior1.possui_registros)
            {
                ViewBag.Mes_Anterior1_Media = string.Format("{0:0}", superv.Mes_Anterior1.Valor_Media);
                ViewBag.Mes_Anterior1_Min = string.Format("{0:0}", superv.Mes_Anterior1.Valor_Min);
                ViewBag.Mes_Anterior1_MinDataHora = string.Format("{0:d} {1:HH:mm}", superv.Mes_Anterior1.Valor_MinDataHora, superv.Mes_Anterior1.Valor_MinDataHora);
                ViewBag.Mes_Anterior1_Max = string.Format("{0:0}", superv.Mes_Anterior1.Valor_Max);
                ViewBag.Mes_Anterior1_MaxDataHora = string.Format("{0:d} {1:HH:mm}", superv.Mes_Anterior1.Valor_MaxDataHora, superv.Mes_Anterior1.Valor_MaxDataHora);
            }
            else
            {
                ViewBag.Mes_Anterior1_Media = "---";
                ViewBag.Mes_Anterior1_Min = "---";
                ViewBag.Mes_Anterior1_MinDataHora = "--/--/---- --:--";
                ViewBag.Mes_Anterior1_Max = "---";
                ViewBag.Mes_Anterior1_MaxDataHora = "--/--/---- --:--";
            }

            ViewBag.Mes_Anterior1_MinN = superv.Mes_Anterior1.Valor_Min;
            ViewBag.Mes_Anterior1_MediaN = superv.Mes_Anterior1.Valor_Media;
            ViewBag.Mes_Anterior1_MaxN = superv.Mes_Anterior1.Valor_Max;

            // mes anterior 2
            ViewBag.Mes_Anterior2_Data = string.Format("{0:Y}", superv.Mes_Anterior2.Data);
            ViewBag.Mes_Anterior2_DataMes = string.Format("{0:MMMM}", superv.Mes_Anterior2.Data);

            if (superv.Mes_Anterior1.possui_registros)
            {
                ViewBag.Mes_Anterior2_Media = string.Format("{0:0}", superv.Mes_Anterior2.Valor_Media);
                ViewBag.Mes_Anterior2_Min = string.Format("{0:0}", superv.Mes_Anterior2.Valor_Min);
                ViewBag.Mes_Anterior2_MinDataHora = string.Format("{0:d} {1:HH:mm}", superv.Mes_Anterior2.Valor_MinDataHora, superv.Mes_Anterior2.Valor_MinDataHora);
                ViewBag.Mes_Anterior2_Max = string.Format("{0:0}", superv.Mes_Anterior2.Valor_Max);
                ViewBag.Mes_Anterior2_MaxDataHora = string.Format("{0:d} {1:HH:mm}", superv.Mes_Anterior2.Valor_MaxDataHora, superv.Mes_Anterior2.Valor_MaxDataHora);
            }
            else
            {
                ViewBag.Mes_Anterior2_Media = "---";
                ViewBag.Mes_Anterior2_Min = "---";
                ViewBag.Mes_Anterior2_MinDataHora = "--/--/---- --:--";
                ViewBag.Mes_Anterior2_Max = "---";
                ViewBag.Mes_Anterior2_MaxDataHora = "--/--/---- --:--";
            }

            ViewBag.Mes_Anterior2_MinN = superv.Mes_Anterior2.Valor_Min;
            ViewBag.Mes_Anterior2_MediaN = superv.Mes_Anterior2.Valor_Media;
            ViewBag.Mes_Anterior2_MaxN = superv.Mes_Anterior2.Valor_Max;


            // previsao
            if (superv.previsao != null)
            {
                ViewBag.DataHora_0 = string.Format("{0:d}", superv.previsao.DataHora_0);
                ViewBag.DiaSemana_0 = string.Format("{0:dddd}", superv.previsao.DataHora_0);
                ViewBag.Tempo_Cod_0 = superv.previsao.Tempo_Cod_0;
                ViewBag.Temp_Min_0 = string.Format("{0:0}", superv.previsao.Temp_Min_0);
                ViewBag.Temp_Max_0 = string.Format("{0:0}", superv.previsao.Temp_Max_0);

                ViewBag.DataHora_1 = string.Format("{0:d}", superv.previsao.DataHora_1);
                ViewBag.DiaSemana_1 = string.Format("{0:dddd}", superv.previsao.DataHora_1);
                ViewBag.Tempo_Cod_1 = superv.previsao.Tempo_Cod_1;
                ViewBag.Temp_Min_1 = string.Format("{0:0}", superv.previsao.Temp_Min_1);
                ViewBag.Temp_Max_1 = string.Format("{0:0}", superv.previsao.Temp_Max_1);

                ViewBag.DataHora_2 = string.Format("{0:d}", superv.previsao.DataHora_2);
                ViewBag.DiaSemana_2 = string.Format("{0:dddd}", superv.previsao.DataHora_2);
                ViewBag.Tempo_Cod_2 = superv.previsao.Tempo_Cod_2;
                ViewBag.Temp_Min_2 = string.Format("{0:0}", superv.previsao.Temp_Min_2);
                ViewBag.Temp_Max_2 = string.Format("{0:0}", superv.previsao.Temp_Max_2);

                ViewBag.DataHora_3 = string.Format("{0:d}", superv.previsao.DataHora_3);
                ViewBag.DiaSemana_3 = string.Format("{0:dddd}", superv.previsao.DataHora_3);
                ViewBag.Tempo_Cod_3 = superv.previsao.Tempo_Cod_3;
                ViewBag.Temp_Min_3 = string.Format("{0:0}", superv.previsao.Temp_Min_3);
                ViewBag.Temp_Max_3 = string.Format("{0:0}", superv.previsao.Temp_Max_3);
            }
            else
            {
                ViewBag.DataHora_0 = "--/--/----";
                ViewBag.DiaSemana_0 = "";
                ViewBag.Tempo_Cod_0 = 0;
                ViewBag.Temp_Min_0 = "---";
                ViewBag.Temp_Max_0 = "---";

                ViewBag.DataHora_1 = "--/--/----";
                ViewBag.DiaSemana_1 = "";
                ViewBag.Tempo_Cod_1 = 0;
                ViewBag.Temp_Min_1 = "---";
                ViewBag.Temp_Max_1 = "---";

                ViewBag.DataHora_2 = "--/--/----";
                ViewBag.DiaSemana_2 = "";
                ViewBag.Tempo_Cod_2 = 0;
                ViewBag.Temp_Min_2 = "---";
                ViewBag.Temp_Max_2 = "---";

                ViewBag.DataHora_3 = "--/--/----";
                ViewBag.DiaSemana_3 = "";
                ViewBag.Tempo_Cod_3 = 0;
                ViewBag.Temp_Min_3 = "---";
                ViewBag.Temp_Max_3 = "---";
            }

            return View();
        }
    }
}