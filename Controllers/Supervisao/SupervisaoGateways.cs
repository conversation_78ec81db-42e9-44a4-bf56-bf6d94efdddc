﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SupervisaoController
    {
        public ActionResult Gateways()
        {
            // tela de ajuda - supervisao gateways
            CookieStore.SalvaCookie_String("PaginaAjuda", "Supervisao_Gateways");

            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // tipo de acesso do usuario
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // IDUsuario
            int IDConsultor = ViewBag._IDConsultor;

            // lista de medicoes
            List<int> ConfigMedList = ViewBag._ConfigMed;

            // lista de gateways
            List<int> GatewaysList = new List<int>();
            List<SupervGatewaysDominio> GatewaysSupervList = new List<SupervGatewaysDominio>();

            // le configuracao das medicoes do cliente atual
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = new List<MedicoesDominio>();
            medicoes = medicoesMetodos.ListarPorIDCliente(IDCliente);

            // com o ConfigMedList monto a lista de gateways do usuario
            int contador;
            int IDGateway = -1;

            // percorre lista
            for (contador = 0; contador < medicoes.Count(); contador++)
            {
                // inicialmente nao tem medicao
                IDGateway = -1;

                // verifica se usuario eh cliente gerente ou operador
                if ((IDTipoAcesso == TIPO_ACESSO.CLIENTE_ADMIN || IDTipoAcesso == TIPO_ACESSO.CLIENTE_OPER || IDTipoAcesso == TIPO_ACESSO.DEMONSTRACAO) && ConfigMedList != null)
                {
                    // verifica se medicao esta habilitada para o usuario
                    if (ConfigMedList.Contains(medicoes[contador].IDMedicao))
                    {
                        // pego IDGateway
                        IDGateway = medicoes[contador].IDGateway;
                    }
                }
                else
                {
                    // pego IDGateway
                    IDGateway = medicoes[contador].IDGateway;
                }

                // verifico se tem gateway para inserir
                if (IDGateway > 0)
                {
                    // verifico se gateway ja existe na lista
                    if (GatewaysList.Contains(IDGateway))
                    {
                        // indico q nao tem gateway
                        IDGateway = -1;
                    }
                }

                // verifico novamente se tem gateway para inserir
                if (IDGateway > 0)
                {
                    // leio supervisao e configuracao da gateway
                    SupervGatewaysMetodos gatewaysMetodos = new SupervGatewaysMetodos();
                    SupervGatewaysDominio gateway = new SupervGatewaysDominio();
                    gateway = gatewaysMetodos.ListarPorIDGateway(IDGateway);

                    // verifica se existe supervisao
                    if( gateway != null )
                    {
                        // verifica se existe
                        if (gateway.IDCliente > 0)
                        {
                            // leio remotas da gateways
                            GatewayRemotasMetodos remotasMetodos = new GatewayRemotasMetodos();
                            List<GatewayRemotasDominio> RemotasList = remotasMetodos.ListarTodasMedicoesIDGateway(gateway.IDGateway);

                            // percorro remotas
                            if( RemotasList != null )
                            {
                                foreach(GatewayRemotasDominio remota in RemotasList)
                                {
                                    // verifica se origem pelo log de eventos
                                    if( remota.OrigemStatus == 0 )
                                    {
                                        // verifico ultimo evento na remota
                                        EV_Metodos eventoMetodos = new EV_Metodos();
                                        EV_Dominio evento = eventoMetodos.ListarMaisRecenteRemota(remota.IDCliente, remota.IDGateway, remota.Remota);

                                        // verifica se tem evento
                                        if( evento != null )
                                        {
                                            // atualiza status (0 - Erro Comunicacao ||| 1 = Comunicacao OK)
                                            remota.IDCliente = gateway.IDCliente;
                                            remota.Status = (evento.Valor >= 65536) ? 1 : 0;
                                            remota.DataStatus = evento.DataHora;
                                            remota.DataStatusTexto = remota.DataStatus.ToString("g");

                                            // salva
                                            remotasMetodos.Alterar(remota);
                                        }
                                    }
                                }
                            }

                            // copia
                            gateway.RemotasList = RemotasList;

                            // data e hora
                            DateTime data_hora_hoje = DateTime.Now;
                            DateTime data_hora_atualizacao = gateway.DataHora;

                            // IP
                            int pos = gateway.StatusEq.IndexOf("IP=");

                            if (pos >= 0)
                            {
                                int pos2 = gateway.StatusEq.IndexOf(' ');

                                if (pos2 > 3)
                                {
                                    string str = gateway.StatusEq.Substring(pos + 3, gateway.StatusEq.IndexOf(' ') - 3);
                                    gateway.IP = str;
                                }
                                else
                                {
                                    gateway.IP = "-";
                                }
                            }
                            else
                            {
                                gateway.IP = "-";
                            }

                            // sinal
                            pos = gateway.StatusEq.IndexOf("SQ=");
                            double sinal = -1.0;
                            gateway.Sinal = 0;

                            if (pos >= 0)
                            {
                                string str = gateway.StatusEq.Substring(pos + 3);

                                if (double.TryParse(str, out sinal))
                                {
                                    if (sinal >= 99.0)
                                    {
                                        sinal = 0.0;
                                    }
                                    else
                                    {
                                        sinal = (sinal / 31.0) * 100.0;
                                    }
                                }
                                else
                                {
                                    sinal = 0.0;
                                }
                            }

                            // copia sinal
                            if (sinal > 0.0)
                            {
                                // copia sinal
                                gateway.Sinal = (int)sinal;
                            }

                            // caso sinal for menor que 50%, faço analise dos eventos
                            gateway.NumFalhas = 0;

                            if (sinal > 0.0 && gateway.IDTipoTempo > 10)
                            {
                                // numero de falhas em 7 dias (antes do ultimo envio de dados)
                                DateTime PrimDataHora = gateway.DataHora.AddDays(-7);

                                EV_Metodos eventoMetodos = new EV_Metodos();
                                gateway.NumFalhas = eventoMetodos.NumFalhaEnvioDados(gateway.IDCliente, gateway.IDGateway, PrimDataHora, gateway.DataHora);
                            }

                            // caso for cliente, pego somente sinal
                            // exceto consultor GreenYellow (4477) que pode ver os IPs
                            if ((IDTipoAcesso == 1 || IDTipoAcesso == 2 || IDTipoAcesso == 3 || IDTipoAcesso == 7 || IDTipoAcesso == 8 || IDTipoAcesso == 10) && !(IDConsultor == 4477 && isUser.isConsultor(IDTipoAcesso)))
                            {
                                gateway.StatusEq = string.Format("{0}", gateway.Sinal);

                                if (gateway.IP.Length > 3)
                                {
                                    string modificada = gateway.IP.Substring(0, gateway.IP.Length - 3);
                                    gateway.IP = modificada + "***";
                                }
                            }

                            // inicialmente normal
                            gateway.Status = "0";     // normal
                            gateway.StatusTexto = "Gateway Atualização Normal";

                            // status da gateway
                            switch (gateway.IDTipoTempo)
                            {
                                case TIPO_TEMPO_GATEWAY.GatewayBloqueada:                           // 0 - gateway bloqueada
                                    gateway.Status = "1";     // anormal
                                    gateway.StatusTexto = "Gateway Bloqueada";
                                    break;

                                case TIPO_TEMPO_GATEWAY.Ignorar_StartUpNaoRealizado:                // 1 - ignorar - start up nao realizado
                                    gateway.Status = "1";     // anormal
                                    gateway.StatusTexto = "Start-up não realizado";
                                    break;

                                case TIPO_TEMPO_GATEWAY.Ignorar_PendenciaCliente:                   // 2 - ignorar - pendencia do cliente
                                    gateway.Status = "1";     // anormal
                                    gateway.StatusTexto = "Pendência do Cliente";
                                    break;

                                case TIPO_TEMPO_GATEWAY.SCDE:                                       // 3 - ignorar - SCDE
                                    gateway.Status = "1";     // anormal
                                    gateway.StatusTexto = "SCDE";
                                    break;

                                case TIPO_TEMPO_GATEWAY.Envio_15minutos:                            // 12 - a cada 15 minutos

                                    // verifica se atraso maior que 6 horas
                                    if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(6, 0, 0))
                                    {
                                        gateway.Status = "1";     // anormal
                                        gateway.StatusTexto = "Gateway Atualização Atrasada";
                                    }

                                    break;

                                case TIPO_TEMPO_GATEWAY.Envio_hora:                                 // 13 - a cada hora

                                    // verifica se atraso maior que 6 horas
                                    if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(6, 0, 0))
                                    {
                                        gateway.Status = "1";     // anormal
                                        gateway.StatusTexto = "Gateway Atualização Atrasada";
                                    }

                                    break;

                                case TIPO_TEMPO_GATEWAY.Envio_diariamente:                          // 14 - diariamente

                                    // verifica se atraso maior que 2 dias
                                    if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(48, 0, 0))
                                    {
                                        gateway.Status = "1";     // anormal
                                        gateway.StatusTexto = "Gateway Atualização Atrasada";
                                    }

                                    break;

                                case TIPO_TEMPO_GATEWAY.Envio_semanalmente:                         // 15 - semanalmente

                                    // verifica se atraso maior que 8 dias
                                    if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(192, 0, 0))
                                    {
                                        gateway.Status = "1";     // anormal
                                        gateway.StatusTexto = "Gateway Atualização Atrasada";
                                    }

                                    break;

                                case TIPO_TEMPO_GATEWAY.Envio_mensalmente:                          // 16 - mensalmente

                                    // verifica se atraso maior que 32 dias
                                    if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(768, 0, 0))
                                    {
                                        gateway.Status = "1";     // anormal
                                        gateway.StatusTexto = "Gateway Atualização Atrasada";
                                    }

                                    break;
                            }

                            // verifica os eventos caso a supervisao nao esta em atraso
                            if (gateway.Status == "0")
                            {
                                EV_Metodos evMetodos = new EV_Metodos();
                                EV_Dominio evento = evMetodos.ListarMaisRecente(IDCliente, IDGateway);

                                if (evento != null)
                                {
                                    // verifica se atraso maior que 2 dias
                                    if ((data_hora_hoje - evento.DataHora) > new TimeSpan(48, 0, 0))
                                    {
                                        gateway.Status = "1";     // anormal
                                        gateway.StatusTexto = "Gateway Atualização Atrasada (Eventos)";
                                    }
                                }
                            }

                            //
                            // OBSERVACOES
                            //

                            // observacao tags
                            List<ObservacaoTagsDominio> tags = new List<ObservacaoTagsDominio>();
                            ViewBag.tags = tags;

                            // observacao
                            ObservacaoGatewayDominio observacao = new ObservacaoGatewayDominio();
                            int IDGatewaySelecionado = 0;

                            ViewBag.observacao = observacao;
                            ViewBag.IDGatewaySelecionado = IDGatewaySelecionado;

                            // observacoes
                            ObservacaoGatewayMetodos observacoesMetodos = new ObservacaoGatewayMetodos();
                            List<ObservacaoGatewayDominio> observacoes = new List<ObservacaoGatewayDominio>();
                            List<ObservacaoGatewayDominio> observacoesGateway = observacoesMetodos.ListarTodosGateway(gateway.IDGateway);
                            ViewBag.Observacoes = observacoes;
                        }
                    }
                    else
                    {
                        // gateway nao existe
                        gateway = new SupervGatewaysDominio();
                        gateway.IDCliente = 0;
                    }

                    // verifica se gateway NAO existe
                    if( gateway.IDCliente <= 0 )
                    {
                        // data e hora
                        DateTime data_hora_atualizacao = new DateTime(2000, 1, 1, 0, 0, 0);

                        gateway.IDCliente = IDCliente;
                        gateway.IDGateway = IDGateway;
                        gateway.Nome = string.Format("ID Gateway {0}", IDGateway);
                        gateway.Fantasia = string.Format("ID Gateway {0}", IDGateway);
                        gateway.DataHora = data_hora_atualizacao;
                        gateway.DataEq = data_hora_atualizacao;
                        gateway.StatusEq = " ";
                        gateway.Status = "1";     // anormal
                        gateway.StatusTexto = "Sem supervisão";
                    }

                    // coloca na lista
                    GatewaysList.Add(IDGateway);
                    GatewaysSupervList.Add(gateway);
                }
            }

            // copia lista
            ViewBag.SupervList = GatewaysSupervList;

            return View();
        }
    }
}