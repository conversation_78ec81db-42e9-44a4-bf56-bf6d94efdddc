﻿#define PRODUCAO


using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;



namespace SmartEnergy.Controllers
{
    // resultado do teste
    public class GateE_TesteCampo_Resultado
    {
        // gate E
        public int IDCliente_E { get; set; }
        public int IDGateway_E { get; set; }
        public int IDMedicao_E { get; set; }
        public string NomeGateway_E { get; set; }
        public string NomeMedicao_E { get; set; }
        public DateTime UltimaDataHora_E { get; set; }

        // gate X
        public int IDCliente_X { get; set; }
        public int IDGateway_X { get; set; }
        public int IDMedicao_X { get; set; }
        public string NomeGateway_X { get; set; }
        public string NomeMedicao_X { get; set; }
        public DateTime UltimaDataHora_X { get; set; }

        // resultado análise
        public DateTime inicio { get; set; }
        public DateTime fim { get; set; }

        public int numRegistros_E { get; set; }
        public double consumoAtv_P_E { get; set; }
        public double consumoAtv_FPI_E { get; set; }
        public double consumoAtv_FPC_E { get; set; }
        public double consumoRtv_P_E { get; set; }
        public double consumoRtv_FPI_E { get; set; }
        public double consumoRtv_FPC_E { get; set; }

        public int NumTiposEventos_E { get; set; }
        public List<TiposEventosDescricao> listaTiposEventos_E { get; set; }
        public List<EventosDescricao> listaEventosDescricao_E { get; set; }

        public int numRegistros_X { get; set; }
        public double consumoAtv_P_X { get; set; }
        public double consumoAtv_FPI_X { get; set; }
        public double consumoAtv_FPC_X { get; set; }
        public double consumoRtv_P_X { get; set; }
        public double consumoRtv_FPI_X { get; set; }
        public double consumoRtv_FPC_X { get; set; }

        public int NumTiposEventos_X { get; set; }
        public List<TiposEventosDescricao> listaTiposEventos_X { get; set; }
        public List<EventosDescricao> listaEventosDescricao_X { get; set; }

        public List<GateE_TesteCampo_Anormalidade> anormalidades { get; set; }
    }

    // anormalidade
    public class GateE_TesteCampo_Anormalidade
    {
        public DateTime DataHora { get; set; }
        public string descricao { get; set; }
        public double gateE { get; set; }
        public double gateX { get; set; }
    }


    [Authorize]
    public partial class SupervisaoController
    {


#if TESTE

        // IDCliente
        int IDCliente_GateE = 5;
        int IDCliente_GateX = 1;

        // relação IDGateway entre GateE e GateX para teste de campo
        int[,] IDGateway_GateE_GateX = new int[,]
        {
            {5848,5534},    // GateE IDGateway[005937] IDMedicao[022703] - GateX IDGateway[001240] IDMedicao[002403] - IDCliente[000305]
            {5848,5534},    // GateE IDGateway[005937] IDMedicao[022703] - GateX IDGateway[001240] IDMedicao[002403] - IDCliente[000305]
            {5848,5534},    // GateE IDGateway[005937] IDMedicao[022703] - GateX IDGateway[001240] IDMedicao[002403] - IDCliente[000305]
            {5848,5534}     // GateE IDGateway[005937] IDMedicao[022703] - GateX IDGateway[001240] IDMedicao[002403] - IDCliente[000305]
        };

        // relação IDMedicao entre GateE e GateX para teste de campo
        int[,] IDMedicao_GateE_GateX = new int[,]
        {
            {22250,21117},   // GateE IDGateway[005937] IDMedicao[022703] - GateX IDGateway[001240] IDMedicao[002403] - IDCliente[000305]
            {22250,21117},   // GateE IDGateway[005937] IDMedicao[022703] - GateX IDGateway[001240] IDMedicao[002403] - IDCliente[000305]
            {22250,21117},   // GateE IDGateway[005937] IDMedicao[022703] - GateX IDGateway[001240] IDMedicao[002403] - IDCliente[000305]
            {22250,21117}    // GateE IDGateway[005937] IDMedicao[022703] - GateX IDGateway[001240] IDMedicao[002403] - IDCliente[000305]
        };

#else

        // IDCliente
        int IDCliente_GateE = 1446;
        int IDCliente_GateX = 305;

        // relação IDGateway entre GateE e GateX para teste de campo
        int[,] IDGateway_GateE_GateX = new int[,]
        {
            {5937,1240},    // GateE IDGateway[005937] IDMedicao[022703] - GateX IDGateway[001240] IDMedicao[002403] - IDCliente[000305]
            {5938,1329},    // GateE IDGateway[005938] IDMedicao[022704] - GateX IDGateway[001329] IDMedicao[002601] - IDCliente[000305]
            {5939,1259},    // GateE IDGateway[005939] IDMedicao[022705] - GateX IDGateway[001259] IDMedicao[002422] - IDCliente[000305]
            {5940,1483}     // GateE IDGateway[005940] IDMedicao[022706] - GateX IDGateway[001483] IDMedicao[002831] - IDCliente[000305]
        };

        // relação IDMedicao entre GateE e GateX para teste de campo
        int[,] IDMedicao_GateE_GateX = new int[,]
        {
            {22703,2403},   // GateE IDGateway[005937] IDMedicao[022703] - GateX IDGateway[001240] IDMedicao[002403] - IDCliente[000305]
            {22704,2601},   // GateE IDGateway[005938] IDMedicao[022704] - GateX IDGateway[001329] IDMedicao[002601] - IDCliente[000305]
            {22705,2422},   // GateE IDGateway[005939] IDMedicao[022705] - GateX IDGateway[001259] IDMedicao[002422] - IDCliente[000305]
            {22706,2831}    // GateE IDGateway[005940] IDMedicao[022706] - GateX IDGateway[001483] IDMedicao[002831] - IDCliente[000305]
        };

#endif


        // GET: SupervisaoGateE_TesteCampo
        public ActionResult SupervisaoGateE_TesteCampo()
        {
            // tela de ajuda - supervisao gateways
            CookieStore.SalvaCookie_String("PaginaAjuda", "Supervisao_Gateways");

            // le cookies
            LeCookies_SmartEnergy();

            // preenche default
            List<GateE_TesteCampo_Resultado> resultado = new List<GateE_TesteCampo_Resultado>();

            // percorre gateways
            for (int i = 0; i < 4; i++)
            {
                // default
                GateE_TesteCampo_Resultado result = PreencheDefault(i);

                // coloca na lista
                resultado.Add(result);
            }

            return View(resultado);
        }

        // GET: SupervisaoGateE_TesteCampo Resultado
        public ActionResult SupervisaoGateE_TesteCampo_Resultado(int IDGateway, int Navegacao = 100, string inicio = "2000-01-01", string fim = "2000-01-01")
        {
            // tela de ajuda - supervisao gateways
            CookieStore.SalvaCookie_String("PaginaAjuda", "Supervisao_Gateways");

            // le cookies
            LeCookies_SmartEnergy();

            // prepara listas
            int indice = SupervisaoGateE_TesteCampo_PreparaLista(IDGateway);

            // resultado
            GateE_TesteCampo_Resultado resultado = PreencheDefault(indice);

            // período da análise
            resultado.inicio = DateTime.Parse(inicio);
            resultado.fim = DateTime.Parse(fim);

            // verifica se todo o período
            if (resultado.inicio.Year == 2000)
            {
                // período da análise
                resultado.inicio = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 0, 15, 0);
                resultado.fim = resultado.inicio.AddDays(1);
            }

            // verifica se invertido
            if (resultado.inicio > resultado.fim)
            {
                // inverte
                DateTime temporario = resultado.inicio;
                resultado.inicio = resultado.fim;
                resultado.fim = temporario;
            }

            // período
            TimeSpan periodo = resultado.fim - resultado.inicio;

            // navegação
            switch (Navegacao)
            {
                case -3:    // período anterior

                    // período anterior
                    resultado.inicio -= periodo;
                    resultado.fim -= periodo;
                    break;

                case 3:    // período seguinte

                    // período seguinte
                    resultado.inicio += periodo;
                    resultado.fim += periodo;
                    break;
            }

            // data e hora atual
            ViewBag.DataIni = string.Format("{0:d}", resultado.inicio);
            ViewBag.HoraIni = string.Format("{0:HH}:{1:mm}", resultado.inicio, resultado.inicio);
            ViewBag.DataFim = string.Format("{0:d}", resultado.fim);
            ViewBag.HoraFim = string.Format("{0:HH}:{1:mm}", resultado.fim, resultado.fim);


            // calcula consumos e anômalos
            SupervisaoGateE_TesteCampo_Consumo(ref resultado);

            // eventos do gateE
            SupervisaoGateE_TesteCampo_Eventos(TIPO_GATEWAY.GATE_E, ref resultado);

            // eventos do gateX
            SupervisaoGateE_TesteCampo_Eventos(TIPO_GATEWAY.GATE_X_422, ref resultado);

            return View(resultado);
        }


        // prepara listas
        private int SupervisaoGateE_TesteCampo_PreparaLista(int IDGateway)
        {
            // GateE em teste de campo
            List<ListaTiposDominio> listaGateE = new List<ListaTiposDominio>();

            // gateway
            GatewaysMetodos gatewayMetodos = new GatewaysMetodos();

            // percorre GateE
            for (int i = 0; i < 4; i++)
            {
                // lê gateway
                GatewaysDominio gateway = gatewayMetodos.ListarPorId(IDGateway_GateE_GateX[i, 0]);

                if (gateway != null)
                {
                    ListaTiposDominio tipo = new ListaTiposDominio();
                    tipo.ID = IDGateway_GateE_GateX[i, 0];
                    tipo.Descricao = string.Format("GateE [{0:000000}] {1}", gateway.IDGateway, gateway.Nome);

                    // coloca na lista
                    listaGateE.Add(tipo);
                }
            }

            // lista GateE
            ViewBag.listaGateE = listaGateE;

            // indice
            int indice = 0;

            // encontra indice
            for (int i = 0; i < 4; i++)
            {
                // verifica se é o gateE desejado
                if (IDGateway == IDGateway_GateE_GateX[i, 0])
                {
                    // achou
                    indice = i;

                    break;
                }
            }

            return (indice);
        }

        // calcula consumos, anormalidades e gráficos
        private void SupervisaoGateE_TesteCampo_Consumo(ref GateE_TesteCampo_Resultado resultado)
        {
            // lê registros do gateE
            EN_Metodos enMetodos = new EN_Metodos();
            List<EN_Dominio> registros_gateE = enMetodos.ListarTodosPeriodo(resultado.IDCliente_E, resultado.IDMedicao_E, resultado.inicio, resultado.fim);

            // número de registros no período
            resultado.numRegistros_E = registros_gateE.Count;

            // percorre registros
            foreach (EN_Dominio registro in registros_gateE)
            {
                switch (registro.Periodo)
                {
                    case PERIODO.P:
                        resultado.consumoAtv_P_E += registro.Ativo;
                        resultado.consumoRtv_P_E += registro.Reativo;
                        break;

                    case PERIODO.FPI:
                        resultado.consumoAtv_FPI_E += registro.Ativo;
                        resultado.consumoRtv_FPI_E += registro.Reativo;
                        break;

                    case PERIODO.FPC:
                        resultado.consumoAtv_FPC_E += registro.Ativo;
                        resultado.consumoRtv_FPC_E += registro.Reativo;
                        break;
                }
            }

            // consumo
            resultado.consumoAtv_P_E /= 4.0;
            resultado.consumoAtv_FPI_E /= 4.0;
            resultado.consumoAtv_FPC_E /= 4.0;
            resultado.consumoRtv_P_E /= 4.0;
            resultado.consumoRtv_FPI_E /= 4.0;
            resultado.consumoRtv_FPC_E /= 4.0;


            // lê registros do gateX
            List<EN_Dominio> registros_gateX = enMetodos.ListarTodosPeriodo(resultado.IDCliente_X, resultado.IDMedicao_X, resultado.inicio, resultado.fim);

            // número de registros no período
            resultado.numRegistros_X = registros_gateX.Count;

            // percorre registros
            foreach (EN_Dominio registro in registros_gateX)
            {
                switch (registro.Periodo)
                {
                    case PERIODO.P:
                        resultado.consumoAtv_P_X += registro.Ativo;
                        resultado.consumoRtv_P_X += registro.Reativo;
                        break;

                    case PERIODO.FPI:
                        resultado.consumoAtv_FPI_X += registro.Ativo;
                        resultado.consumoRtv_FPI_X += registro.Reativo;
                        break;

                    case PERIODO.FPC:
                        resultado.consumoAtv_FPC_X += registro.Ativo;
                        resultado.consumoRtv_FPC_X += registro.Reativo;
                        break;
                }
            }

            // consumo
            resultado.consumoAtv_P_X /= 4.0;
            resultado.consumoAtv_FPI_X /= 4.0;
            resultado.consumoAtv_FPC_X /= 4.0;
            resultado.consumoRtv_P_X /= 4.0;
            resultado.consumoRtv_FPI_X /= 4.0;
            resultado.consumoRtv_FPC_X /= 4.0;


            // anormalidades
            resultado.anormalidades = new List<GateE_TesteCampo_Anormalidade>();

            // percorre registros gateE
            foreach (EN_Dominio registroE in registros_gateE)
            {
                // busca registro (data e hora no gateX
                EN_Dominio registroX = registros_gateX.Find(x => x.DataHora == registroE.DataHora);

                // anormalidade
                GateE_TesteCampo_Anormalidade anormalidade = new GateE_TesteCampo_Anormalidade();
                anormalidade.DataHora = registroE.DataHora;
                anormalidade.descricao = "";
                anormalidade.gateE = 0;
                anormalidade.gateX = 0;

                if (registroX != null)
                {
                    // verifico demanda ativa
                    if (SupervisaoGateE_TesteCampo_CompararValores(registroE.Ativo, registroX.Ativo))
                    {
                        anormalidade.descricao = "Demanda Ativa gateE <> gateX";
                        anormalidade.gateE = registroE.Ativo;
                        anormalidade.gateX = registroX.Ativo;
                    }

                    // verifico demanda reativa
                    if (SupervisaoGateE_TesteCampo_CompararValores(registroE.Reativo, registroX.Reativo))
                    {
                        anormalidade.descricao = "Demanda Reativa gateE <> gateX";
                        anormalidade.gateE = registroE.Reativo;
                        anormalidade.gateX = registroX.Reativo;
                    }

                    // verifico período
                    if (registroE.Periodo != registroX.Periodo)
                    {
                        anormalidade.descricao = "Período gateE <> gateX";
                        anormalidade.gateE = registroE.Periodo;
                        anormalidade.gateX = registroX.Periodo;
                    }
                }
                else
                {
                    anormalidade.descricao = "Registro gateE não encontrado no gateX";
                    anormalidade.gateE = registroE.Ativo;
                    anormalidade.gateX = 0;
                }

                // verifica se tem anormalida
                if (anormalidade.descricao.Length > 0)
                {
                    resultado.anormalidades.Add(anormalidade);
                }
            }



            // grafico de demanda gateE
            var Dias_Demanda_E = new string[resultado.numRegistros_E];
            var DemandaAtv_E = new double[resultado.numRegistros_E];
            var DemandaRtv_E = new double[resultado.numRegistros_E];
            var Periodo_E = new double[resultado.numRegistros_E];

            double Dem_max_E = 0.0;

            int i = 0;

            for (i = 0; i < resultado.numRegistros_E; i++)
            {
                // formata label
                Dias_Demanda_E[i] = registros_gateE[i].DataHora.ToString("yyyy-MM-dd HH:mm:ss");

                DemandaAtv_E[i] = registros_gateE[i].Ativo;
                DemandaRtv_E[i] = registros_gateE[i].Reativo;
                Periodo_E[i] = registros_gateE[i].Periodo;

                // verifica demanda maxima
                if (DemandaAtv_E[i] > Dem_max_E)
                    Dem_max_E = DemandaAtv_E[i];
            }

            Dem_max_E = Dem_max_E * 1.1;

            if (Dem_max_E < 1.0)
            {
                Dem_max_E = 1.0;
            }

            ViewBag.DemMax_E = Dem_max_E;

            ViewBag.DemandaAtv_E = DemandaAtv_E;
            ViewBag.DemandaRtv_E = DemandaRtv_E;
            ViewBag.Periodo_E = Periodo_E;
            ViewBag.Dias_Demanda_E = Dias_Demanda_E;
            ViewBag.numRegistros_E = resultado.numRegistros_E;


            // grafico de demanda gateX
            var Dias_Demanda_X = new string[resultado.numRegistros_X];
            var DemandaAtv_X = new double[resultado.numRegistros_X];
            var DemandaRtv_X = new double[resultado.numRegistros_X];
            var Periodo_X = new double[resultado.numRegistros_X];

            double Dem_max_X = 0.0;

            for (i = 0; i < resultado.numRegistros_X; i++)
            {
                // formata label
                Dias_Demanda_X[i] = registros_gateX[i].DataHora.ToString("yyyy-MM-dd HH:mm:ss");

                DemandaAtv_X[i] = registros_gateX[i].Ativo;
                DemandaRtv_X[i] = registros_gateX[i].Reativo;
                Periodo_X[i] = registros_gateX[i].Periodo;

                // verifica demanda maxima
                if (DemandaAtv_X[i] > Dem_max_X)
                    Dem_max_X = DemandaAtv_X[i];
            }

            Dem_max_X = Dem_max_X * 1.1;

            if (Dem_max_X < 1.0)
            {
                Dem_max_X = 1.0;
            }

            ViewBag.DemMax_X = Dem_max_X;

            ViewBag.DemandaAtv_X = DemandaAtv_X;
            ViewBag.DemandaRtv_X = DemandaRtv_X;
            ViewBag.Periodo_X = Periodo_X;
            ViewBag.Dias_Demanda_X = Dias_Demanda_X;
            ViewBag.numRegistros_X = resultado.numRegistros_X;

            return;
        }

        // compara valores
        public static bool SupervisaoGateE_TesteCampo_CompararValores(double gateE, double gateX)
        {
            // calcula diferença
            double diferencaPercentual = Math.Abs((gateE - gateX) / gateE) * 100;

            // verifica se diferença grande
            if (diferencaPercentual > 0.5)
            {
                // diferença grande
                return true;
            }

            // diferença tolerável
            return false;
        }


        // eventos
        private void SupervisaoGateE_TesteCampo_Eventos(int IDTipoGateway, ref GateE_TesteCampo_Resultado resultado)
        {
            // a princípio é o gate X
            int IDCliente = resultado.IDCliente_X;
            int IDGateway = resultado.IDGateway_X;

            if (IDTipoGateway == TIPO_GATEWAY.GATE_E)
            {
                IDCliente = resultado.IDCliente_E;
                IDGateway = resultado.IDGateway_E;
            }

            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_gateway = IDGateway;

            // tipos de eventos
            TIPOS_EVENTO tipos_evento = new TIPOS_EVENTO();
            char retorno = SmCalcDB_Eventos_Tipos((char)0, (char)IDTipoGateway, ref tipos_evento);

            // lista tipos de eventos
            List<TiposEventosDescricao> listaTiposEventos = new List<TiposEventosDescricao>();

            // verifica se tem eventos
            if (tipos_evento.num_tipos > 0)
            {
                // percorre lista
                int contador = 0;

                for (contador = 0; contador < tipos_evento.num_tipos; contador++)
                {
                    string str_aux;
                    TiposEventosDescricao tipoeventoDescricao = new TiposEventosDescricao();

                    // tipo
                    tipoeventoDescricao.Tipo = tipos_evento.tipos[contador].tipo;

                    // codigos
                    List<int> CodigosList = null;

                    // Converter o byte[] para String
                    str_aux = Encoding.GetEncoding("ISO-8859-1").GetString(tipos_evento.tipos[contador].codigos);

                    // remove \0
                    str_aux = str_aux.Replace("\0", "");

                    // copia codigos para lista
                    if (!String.IsNullOrEmpty(str_aux))
                    {
                        CodigosList = str_aux.Split('/')
                            .Select(possibleIntegerAsString =>
                            {
                                int parsedInteger = 0;
                                bool isInteger = int.TryParse(possibleIntegerAsString, out parsedInteger);
                                return new { isInteger, parsedInteger };
                            })
                            .Where(tryParseResult => tryParseResult.isInteger)
                            .Select(tryParseResult => tryParseResult.parsedInteger)
                            .ToList();
                    }

                    // copia lista de codigos
                    tipoeventoDescricao.CodigosList = CodigosList;

                    // descricao

                    // Converter o byte[] para String
                    str_aux = Encoding.GetEncoding("ISO-8859-1").GetString(tipos_evento.tipos[contador].descricao);

                    // remove \0
                    str_aux = str_aux.Replace("\0", "");

                    // copia descricao evento
                    tipoeventoDescricao.Descricao = str_aux;

                    // adiciona tipo evento tratado
                    listaTiposEventos.Add(tipoeventoDescricao);
                }
            }

            // copia resultado
            if (IDTipoGateway == TIPO_GATEWAY.GATE_E)
            {
                resultado.NumTiposEventos_E = tipos_evento.num_tipos;
                resultado.listaTiposEventos_E = listaTiposEventos;
            }

            if (IDTipoGateway == TIPO_GATEWAY.GATE_X_422)
            {
                resultado.NumTiposEventos_X = tipos_evento.num_tipos;
                resultado.listaTiposEventos_X = listaTiposEventos;
            }


            // le eventos
            EV_Metodos eventosMetodos = new EV_Metodos();
            List<EV_Dominio> listaEventos = eventosMetodos.ListarPorPeriodo(IDCliente, IDGateway, resultado.inicio, resultado.fim);

            // lista de eventos
            List<EventosDescricao> listaEventosDescricao = new List<EventosDescricao>();

            // verifica se tem eventos
            if (listaEventos.Count() > 0)
            {
                // percorre lista
                int contador = 0;

                for (contador = 0; contador < listaEventos.Count(); contador++)
                {
                    EVENTO evento = new EVENTO();
                    EV_Dominio item_evento = new EV_Dominio();
                    EventosDescricao eventoDescricao = new EventosDescricao();
                    string descricao;

                    // copia evento
                    item_evento = listaEventos[contador];

                    // converte para tipo evento
                    Funcoes_Converte.ConverteDateTime2DataHora(ref evento.datahora, item_evento.DataHora);
                    evento.codigo = (short)item_evento.Evento;
                    evento.valor = item_evento.Valor;

                    // pega descricao
                    SmCalcDB_Eventos_Descricao((char)0, ref config_interface, (char)IDTipoGateway, ref evento);

                    // Converter o byte[] para String
                    descricao = Encoding.GetEncoding("ISO-8859-1").GetString(evento.descricao);

                    // remove \0
                    descricao = descricao.Replace("\0", "");

                    // copia descricao evento
                    eventoDescricao.Descricao = descricao;

                    // copia data evento
                    eventoDescricao.DataHora = string.Format("{0:d} {1:HH:mm:ss}", item_evento.DataHora, item_evento.DataHora);

                    // data e hora para sort
                    eventoDescricao.DataHora_Sort = String.Format("{0:yyyyMMddHHmmssfff}", item_evento.DataHora);

                    // percorre lista de tipos de eventos
                    eventoDescricao.Tipo = 0;

                    for (int contador2 = 0; contador2 < listaTiposEventos.Count(); contador2++)
                    {
                        // verifica se codigo esta na lista
                        if (listaTiposEventos[contador2].CodigosList.Contains(evento.codigo))
                        {
                            // pego codigo
                            eventoDescricao.Tipo = listaTiposEventos[contador2].Tipo;
                        }
                    }

                    // verifica se tipo UPLOAD
                    if (eventoDescricao.Descricao.Contains("Enviou arquivo com sucesso"))
                    {
                        // não adiciona evento tratado
                    }
                    else
                    {
                        // adiciona evento tratado
                        listaEventosDescricao.Add(eventoDescricao);
                    }
                }
            }

            // copia resultado
            if (IDTipoGateway == TIPO_GATEWAY.GATE_E)
            {
                resultado.listaEventosDescricao_E = listaEventosDescricao;
            }

            if (IDTipoGateway == TIPO_GATEWAY.GATE_X_422)
            {
                resultado.listaEventosDescricao_X = listaEventosDescricao;
            }

            return;
        }

        // preenche default
        private GateE_TesteCampo_Resultado PreencheDefault(int indice)
        {
            // le supervisao das medicoes
            MedicoesSupervMetodos medicoesMetodos = new MedicoesSupervMetodos();

            // supervisão da medição (gate E)
            MedicoesSupervDominio medicao_gateE = medicoesMetodos.ListarPorIDMedicao(IDMedicao_GateE_GateX[indice, 0]);

            // supervisão da medição (gate X)
            MedicoesSupervDominio medicao_gateX = medicoesMetodos.ListarPorIDMedicao(IDMedicao_GateE_GateX[indice, 1]);

            // resultado
            GateE_TesteCampo_Resultado result = new GateE_TesteCampo_Resultado();
            result.IDCliente_E = IDCliente_GateE;
            result.IDGateway_E = IDGateway_GateE_GateX[indice, 0];
            result.IDMedicao_E = IDMedicao_GateE_GateX[indice, 0];
            result.NomeGateway_E = string.Format("GateE [{0:000000}]", result.IDGateway_E);
            result.NomeMedicao_E = string.Format("GateE [{0:000000}]", result.IDMedicao_E);
            result.UltimaDataHora_E = new DateTime(2000, 1, 1, 0, 0, 0);

            result.IDCliente_X = IDCliente_GateX;
            result.IDGateway_X = IDGateway_GateE_GateX[indice, 1];
            result.IDMedicao_X = IDMedicao_GateE_GateX[indice, 1];
            result.NomeGateway_X = string.Format("GateX [{0:000000}]", result.IDGateway_X);
            result.NomeMedicao_X = string.Format("GateX [{0:000000}]", result.IDMedicao_X);
            result.UltimaDataHora_X = new DateTime(2000, 1, 1, 0, 0, 0);

            if (medicao_gateE != null)
            {
                result.NomeGateway_E = medicao_gateE.NomeGateway;
                result.NomeMedicao_E = medicao_gateE.NomeMedicao;
                result.UltimaDataHora_E = medicao_gateE.DataHoraValor;
            }

            if (medicao_gateX != null)
            {
                result.NomeGateway_X = medicao_gateX.NomeGateway;
                result.NomeMedicao_X = medicao_gateX.NomeMedicao;
                result.UltimaDataHora_X = medicao_gateX.DataHoraValor;
            }

            // retorna resultado
            return (result);
        }
    }
}