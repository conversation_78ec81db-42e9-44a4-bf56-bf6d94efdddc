﻿using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SupervisaoController : Controller
    {
        public ActionResult Teste_SmartMQTT()
        {
            // tela de ajuda - teste SmartMQTT
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // le cookies
            LeCookies_SmartEnergy();

            //
            // TESTE
            //

            //
            // solicita programação saídas digitais
            //
            /*
            int IDGateway = 4001;
              
            // comunicação MQTT
            SmartMQTT smartMQTT = new SmartMQTT();

            smartMQTT.SmartMQTT_Solicita(IDGateway, FUNCOES_SOL.PROGR_SD_SOLICITA);

            // copia apenas as programadas
            List<GateX_SD_Dominio> sd_programadas = new List<GateX_SD_Dominio>();

            foreach (GateX_SD_Dominio sd in smartMQTT.gateX_SD_programacao)
            {
                // verifica se programado
                if (sd.Programado)
                {
                    GateX_SD_Dominio tmp = new GateX_SD_Dominio();
                    tmp = sd;

                    sd_programadas.Add(tmp);
                }
            }
            */


            //
            // envia programação saídas digitais
            //
            /*
            int IDGateway = 4001;
            List<GateX_SD_Dominio> progr_sd = new List<GateX_SD_Dominio>();

            // comunicação MQTT
            SmartMQTT smartMQTT = new SmartMQTT();

            // saídas digitais com valores default
            smartMQTT.SaidasDigitais_Programacao_Default(IDGateway, ref progr_sd);

            // preenche com valores teste
            progr_sd[0].IDGateway = IDGateway;
            progr_sd[0].Programado = true;
            progr_sd[0].NumSaidaGateway = 0;
            progr_sd[0].Descricao = "Teste SD 0";
            progr_sd[0].RegEvTmr = true;
            progr_sd[0].redeIO = 0;
            progr_sd[0].NumRemota = 5;
            progr_sd[0].drvAd = 0;
            progr_sd[0].LogInv = false;
            progr_sd[0].P_Mnl = 0;
            progr_sd[0].F_Mnl = 0;
            progr_sd[0].P_MxD = 0;
            progr_sd[0].F_MxD = 0;
            progr_sd[0].F_MxD_ini = new DateTime(2000, 1, 1, 0, 0, 0);
            progr_sd[0].TrMnD = 0;
            progr_sd[0].TrMxD = 0;
            progr_sd[0].TrMnL = 0;
            progr_sd[0].TrDyD = 0;
            progr_sd[0].TrDyL = 0;
            progr_sd[0].TariD = 0;
            progr_sd[0].MdFM2D = 255;
            progr_sd[0].EeRetr = 255;
            progr_sd[0].TAlmRetr = 0;
            progr_sd[0].CtrPj = 255;
            progr_sd[0].kW = 0;
            progr_sd[0].kWiMdEN = 255;
            progr_sd[0].PriPj = 0;
            progr_sd[0].CtrFP = 255;
            progr_sd[0].kVArC = 0;
            progr_sd[0].PriFP = 0;
            progr_sd[0].CtrMd = 255;
            progr_sd[0].MdDes = 0;
            progr_sd[0].MdLig = 0;
            progr_sd[0].CtrAc = 255;
            progr_sd[0].AcDes = 0;
            progr_sd[0].CtrAn = 255;
            progr_sd[0].AnDes = 0;
            progr_sd[0].AnLig = 0;
            progr_sd[0].CmdAlm = 0;
            progr_sd[0].CmdAlmTy = 0;
            progr_sd[0].CmdAlmAx = 0;
            progr_sd[0].Ee2Act = 255;
            progr_sd[0].HabRem = false;

            progr_sd[5].IDGateway = IDGateway;
            progr_sd[5].Programado = true;
            progr_sd[5].NumSaidaGateway = 5;
            progr_sd[5].Descricao = "Teste SD 5";
            progr_sd[5].RegEvTmr = false;
            progr_sd[5].redeIO = 0;
            progr_sd[5].NumRemota = 6;
            progr_sd[5].drvAd = 0;
            progr_sd[5].LogInv = false;
            progr_sd[5].P_Mnl = 0;
            progr_sd[5].F_Mnl = 0;
            progr_sd[5].P_MxD = 0;
            progr_sd[5].F_MxD = 0;
            progr_sd[5].F_MxD_ini = new DateTime(2000, 1, 1, 0, 0, 0);
            progr_sd[5].TrMnD = 0;
            progr_sd[5].TrMxD = 0;
            progr_sd[5].TrMnL = 0;
            progr_sd[5].TrDyD = 0;
            progr_sd[5].TrDyL = 0;
            progr_sd[5].TariD = 0;
            progr_sd[5].MdFM2D = 255;
            progr_sd[5].EeRetr = 255;
            progr_sd[5].TAlmRetr = 0;
            progr_sd[5].CtrPj = 255;
            progr_sd[5].kW = 0;
            progr_sd[5].kWiMdEN = 255;
            progr_sd[5].PriPj = 0;
            progr_sd[5].CtrFP = 255;
            progr_sd[5].kVArC = 0;
            progr_sd[5].PriFP = 0;
            progr_sd[5].CtrMd = 255;
            progr_sd[5].MdDes = 0;
            progr_sd[5].MdLig = 0;
            progr_sd[5].CtrAc = 255;
            progr_sd[5].AcDes = 0;
            progr_sd[5].CtrAn = 255;
            progr_sd[5].AnDes = 0;
            progr_sd[5].AnLig = 0;
            progr_sd[5].CmdAlm = 0;
            progr_sd[5].CmdAlmTy = 0;
            progr_sd[5].CmdAlmAx = 0;
            progr_sd[5].Ee2Act = 255;
            progr_sd[5].HabRem = false;


            // copia para lista de envio
            smartMQTT.gateX_SD_programacao = progr_sd;

            // envia programação horária
            smartMQTT.SmartMQTT_Solicita(IDGateway, FUNCOES_SOL.PROGR_SD_ENVIA);

            // verifica se enviou
            if (!smartMQTT.status_SmartMQTT_OK)
            {
                // erro
                Console.WriteLine("erro no envio");
            }

            // fim
            IDGateway = 4001;
            */






            //
            // solicita lista das medições de energia programadas
            //
            /*
            int IDGateway = 4001;

            // comunicação MQTT
            SmartMQTT smartMQTT = new SmartMQTT();

            smartMQTT.SmartMQTT_Solicita(IDGateway, FUNCOES_SOL.PROGR_LISTA_MED_EN);

            // copia apenas as programadas
            List<GateX_MedEner_Lista_Dominio> lista_programadas = new List<GateX_MedEner_Lista_Dominio>();

            foreach (GateX_MedEner_Lista_Dominio med in smartMQTT.gateX_MedEner_lista)
            {
                // verifica se programado
                if (med.Programado)
                {
                    GateX_MedEner_Lista_Dominio tmp = new GateX_MedEner_Lista_Dominio();
                    tmp = med;

                    lista_programadas.Add(tmp);
                }
            }
            */

            //
            // solicita lista das entradas digitais programadas
            //
            /*
            int IDGateway = 4001;

            // comunicação MQTT
            SmartMQTT smartMQTT = new SmartMQTT();

            smartMQTT.SmartMQTT_Solicita(IDGateway, FUNCOES_SOL.PROGR_LISTA_ED);

            // copia apenas as programadas
            List<GateX_ED_Lista_Dominio> lista_ed_programadas = new List<GateX_ED_Lista_Dominio>();

            foreach (GateX_ED_Lista_Dominio ed in smartMQTT.gateX_ED_lista)
            {
                // verifica se programado
                if (ed.Programado)
                {
                    GateX_ED_Lista_Dominio tmp = new GateX_ED_Lista_Dominio();
                    tmp = ed;

                    lista_ed_programadas.Add(tmp);
                }
            }
            */

            //
            // solicita lista das saídas digitais programadas
            //
            /*
            int IDGateway = 4001;
              
            // comunicação MQTT
            SmartMQTT smartMQTT = new SmartMQTT();
              
            smartMQTT.SmartMQTT_Solicita(IDGateway, FUNCOES_SOL.PROGR_LISTA_SD);

            // copia apenas as programadas
            List<GateX_SD_Lista_Dominio> lista_sd_programadas = new List<GateX_SD_Lista_Dominio>();

            foreach (GateX_SD_Lista_Dominio sd in smartMQTT.gateX_SD_lista)
            {
                // verifica se programado
                if (sd.Programado)
                {
                    GateX_SD_Lista_Dominio tmp = new GateX_SD_Lista_Dominio();
                    tmp = sd;

                    lista_sd_programadas.Add(tmp);
                }
            }
            */

            //
            // solicita programação horária
            //
            /*
            int IDGateway = 4001;
              
            // comunicação MQTT
            SmartMQTT smartMQTT = new SmartMQTT();

            smartMQTT.SmartMQTT_Solicita(IDGateway, FUNCOES_SOL.PROGR_HORARIA_SOLICITA);

            // copia apenas as programadas
            List<GateX_ProgramacaoHorariaDominio> ph_programadas = new List<GateX_ProgramacaoHorariaDominio>();

            foreach (GateX_ProgramacaoHorariaDominio ph in smartMQTT.gateX_programacao_horaria)
            {
                // verifica se programado
                if (ph.Programado)
                {
                    GateX_ProgramacaoHorariaDominio tmp = new GateX_ProgramacaoHorariaDominio();
                    tmp = ph;

                    ph_programadas.Add(tmp);
                }
            }
            */

            //
            // envia programação horária
            //
            /*
            int IDGateway = 4001;
            List<GateX_ProgramacaoHorariaDominio> progr_ph = new List<GateX_ProgramacaoHorariaDominio>();

            // comunicação MQTT
            SmartMQTT smartMQTT = new SmartMQTT();

            // programação horária com valores default
            smartMQTT.ProgramacaoHoraria_Default(IDGateway, ref progr_ph);

            // preenche com valores teste
            progr_ph[0].Programado = true;
            progr_ph[0].NumProgramacaoGateway = 0;
            progr_ph[0].NumSaida = 1;
            progr_ph[0].HoraLiga = new DateTime(2000, 1, 1, 12, 15, 0);
            progr_ph[0].HoraDesliga = new DateTime(2000, 1, 1, 18, 00, 0);
            progr_ph[0].DiaSemana_Nenhum = false;
            progr_ph[0].DiaSemana_Domingo = false;
            progr_ph[0].DiaSemana_Segunda = true;
            progr_ph[0].DiaSemana_Terca = true;
            progr_ph[0].DiaSemana_Quarta = true;
            progr_ph[0].DiaSemana_Quinta = true;
            progr_ph[0].DiaSemana_Sexta = true;
            progr_ph[0].DiaSemana_Sabado = false;
            progr_ph[0].DiaSemana_Feriado = false;
            progr_ph[0].DiaSemana_Especial = false;

            progr_ph[5].Programado = true;
            progr_ph[5].NumProgramacaoGateway = 5;
            progr_ph[5].NumSaida = 2;
            progr_ph[5].HoraLiga = new DateTime(2000, 1, 1, 11, 30, 0);
            progr_ph[5].HoraDesliga = new DateTime(2000, 1, 1, 17, 30, 0);
            progr_ph[5].DiaSemana_Nenhum = false;
            progr_ph[5].DiaSemana_Domingo = false;
            progr_ph[5].DiaSemana_Segunda = true;
            progr_ph[5].DiaSemana_Terca = true;
            progr_ph[5].DiaSemana_Quarta = true;
            progr_ph[5].DiaSemana_Quinta = true;
            progr_ph[5].DiaSemana_Sexta = true;
            progr_ph[5].DiaSemana_Sabado = false;
            progr_ph[5].DiaSemana_Feriado = false;
            progr_ph[5].DiaSemana_Especial = false;

            // copia para lista de envio
            smartMQTT.gateX_programacao_horaria = progr_ph;

            // envia programação horária
            smartMQTT.SmartMQTT_Solicita(IDGateway, FUNCOES_SOL.PROGR_HORARIA_ENVIA);

            // verifica se enviou
            if (!smartMQTT.status_SmartMQTT_OK)
            {
                // erro
                Console.WriteLine("erro no envio");
            }
            */

            //
            ///////


            return View();
        }
    }
}