﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{

    [Authorize]
    public partial class SupervisaoController
    {
        public ActionResult Gateways_Servidor_IoT()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // leio lista de supervisao gateways
            SupervGatewaysMetodos gatewaysMetodos = new SupervGatewaysMetodos();
            List<SupervGatewaysDominio> GatewaysMQTTList = new List<SupervGatewaysDominio>();
            List<SupervGatewaysDominio> GatewaysSupervList = new List<SupervGatewaysDominio>();
            GatewaysSupervList = gatewaysMetodos.ListarTodos();

            // verifica se existe
            if (GatewaysSupervList != null)
            {
                // API Artemis Broker - listAllConsumers
                ArtemisBroker artemis = new ArtemisBroker();
                List<listAllConsumers_Gateway> gatewaysMQTT = artemis.ListaTodos_Consumers();

                if (gatewaysMQTT != null)
                {
                    // percorre lista
                    foreach (SupervGatewaysDominio gateway in GatewaysSupervList)
                    {
                        // a principio gateway não esta conectada no IoT
                        gateway.GatewayConectada_IoT = false;

                        // verifica se gateway possui IoT
                        if (gateway.IDTipoGateway == TIPO_GATEWAY.GATE_X_421 || gateway.IDTipoGateway == TIPO_GATEWAY.GATE_X_422 || gateway.IDTipoGateway == TIPO_GATEWAY.GATE_E)
                        {
                            // verifica se gateway esta na lista de gateways conectadas no IoT
                            listAllConsumers_Gateway gatewayMQTT = gatewaysMQTT.Find(x => x.IDGateway == gateway.IDGateway);

                            if (gatewayMQTT != null)
                            {
                                // troca data/hora do equipamento pela data/hora da criação da conexão
                                gateway.DataEq = gatewayMQTT.creationTime;

                                // gateway conectada no IoT
                                gateway.GatewayConectada_IoT = true;
                            }

                            // IP
                            int pos = gateway.StatusEq.IndexOf("IP=");

                            if (pos >= 0)
                            {
                                int pos2 = gateway.StatusEq.IndexOf(' ');

                                if (pos2 > 3)
                                {
                                    string str = gateway.StatusEq.Substring(pos + 3, gateway.StatusEq.IndexOf(' ') - 3);
                                    gateway.IP = str;
                                }
                                else
                                {
                                    gateway.IP = "-";
                                }
                            }
                            else
                            {
                                gateway.IP = "-";
                            }

                            // sinal
                            pos = gateway.StatusEq.IndexOf("SQ=");
                            double sinal = -1.0;
                            gateway.Sinal = 0;

                            if (pos >= 0)
                            {
                                string str = gateway.StatusEq.Substring(pos + 3);
                                sinal = double.Parse(str);

                                if (sinal >= 99.0)
                                {
                                    sinal = 0.0;
                                }
                                else
                                {
                                    sinal = (sinal / 31.0) * 100.0;
                                }
                            }

                            // copia sinal
                            if (sinal > 0.0)
                            {
                                // copia sinal
                                gateway.Sinal = (int)sinal;
                            }

                            // coloca na lista
                            GatewaysMQTTList.Add(gateway);
                        }
                    }
                }
            }

            return View(GatewaysMQTTList);
        }
    }
}

