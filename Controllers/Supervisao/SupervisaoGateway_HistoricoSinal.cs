﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SupervisaoController
    {
        public ActionResult Gateway_HistoricoSinal(int IDGateway)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // leio supervisao gateway
            SupervGatewaysMetodos gatewaysMetodos = new SupervGatewaysMetodos();
            SupervGatewaysDominio gateway = new SupervGatewaysDominio();
            gateway = gatewaysMetodos.ListarPorIDGateway(IDGateway);

            // valores
            ViewBag.GatewayID = IDGateway;
            ViewBag.GatewayNome = gateway.Nome;
            ViewBag.GatewayModelo = String.Format("{0} ({1})", gateway.ModeloEq, gateway.VersaoEq);
            ViewBag.GatewayStatus = gateway.StatusEq;
            ViewBag.GatewayAtualizacao = string.Format("{0:d} {1:HH:mm:ss}", gateway.DataHora, gateway.DataHora);
            ViewBag.GatewayDataHora = string.Format("{0:d} {1:HH:mm:ss}", gateway.DataEq, gateway.DataEq);

            // primeira e ultima data para analise (7 dias)
            DateTime UltDataHora = gateway.DataHora;
            DateTime PrimDataHora = UltDataHora.AddDays(-7);

            // leio lista de supervisao 
            List<SUPDominio> listaSUP = new List<SUPDominio>();
            listaSUP = gatewaysMetodos.ListarEntreDatas(gateway.IDCliente, IDGateway, PrimDataHora, UltDataHora);
            ViewBag.ListaSup = listaSUP;

            // grafico
            var Sinal = new int[100];
            var IP = new string[100];
            var DataHoraSinal = new string[100];
            int numRegistrosSinal = 0;

            if( listaSUP != null )
            {
                // numero de registros
                numRegistrosSinal = (listaSUP.Count > 100) ? 100 : listaSUP.Count;

                // valores
                for (int i = 0; i < numRegistrosSinal; i++)
                {
                    // copia valores invertendo ordem
                    DataHoraSinal[i] = listaSUP[i].DataHora.ToString("yyyy-MM-dd HH:mm:ss");
                    Sinal[i] = listaSUP[i].Sinal;
                    IP[i] = listaSUP[i].IP;
                }
            }

            ViewBag.DataHoraSinal = DataHoraSinal;
            ViewBag.Sinal = Sinal;
            ViewBag.IP = IP;
            ViewBag.numRegistrosSinal = numRegistrosSinal;


            // eventos de falha durante o periodo
            var Eventos = new string[100];
            var DataHoraEvento = new string[100];
            int numRegistrosEvento = 0;

            EV_Metodos eventoMetodos = new EV_Metodos();
            List<EV_Dominio> eventos = eventoMetodos.ListarFalhaEnvioDados(gateway.IDCliente, gateway.IDGateway, PrimDataHora, UltDataHora);

            // lista de eventos
            List<EventosDescricao> listaEventosDescricao = new List<EventosDescricao>();

            if (listaSUP != null)
            {
                // numero de registros
                numRegistrosEvento = (eventos.Count > 100) ? 100 : eventos.Count;

                // preenche solicitacao
                CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
                config_interface.sweb.id_cliente = IDCliente;
                config_interface.sweb.id_gateway = IDGateway;

                // valores
                for (int i = 0; i < numRegistrosEvento; i++)
                {
                    EVENTO evento = new EVENTO();
                    EV_Dominio item_evento = new EV_Dominio();
                    EventosDescricao eventoDescricao = new EventosDescricao();
                    string descricao;

                    // copia evento
                    item_evento = eventos[i];

                    // converte para tipo evento
                    Funcoes_Converte.ConverteDateTime2DataHora(ref evento.datahora, item_evento.DataHora);
                    evento.codigo = (short)item_evento.Evento;
                    evento.valor = item_evento.Valor;

                    // pega descricao
                    SmCalcDB_Eventos_Descricao((char)0, ref config_interface, (char)gateway.IDTipoGateway, ref evento);

                    // Converter o byte[] para String
                    descricao = Encoding.GetEncoding("ISO-8859-1").GetString(evento.descricao);

                    // remove \0
                    descricao = descricao.Replace("\0", "");

                    // copia descricao evento
                    eventoDescricao.Descricao = descricao;

                    // data e hora evento
                    eventoDescricao.DataHoraN = item_evento.DataHora;

                    // evento e valor
                    eventoDescricao.Evento = item_evento.Evento;
                    eventoDescricao.Valor = item_evento.Valor;

                    // copia data e hora evento
                    eventoDescricao.DataHora = string.Format("{0:d} {1:HH:mm:ss}", item_evento.DataHora, item_evento.DataHora);

                    // data e hora para sort
                    eventoDescricao.DataHora_Sort = String.Format("{0:yyyyMMddHHmmss}", item_evento.DataHora);

                    // tipo de eventos
                    eventoDescricao.Tipo = 8;

                    // adiciona evento tratado
                    listaEventosDescricao.Add(eventoDescricao);

                    // copia valores
                    DataHoraEvento[i] = item_evento.DataHora.ToString("yyyy-MM-dd HH:mm:ss");
                    Eventos[i] = descricao;
                }
            }

            ViewBag.DataHoraEvento = DataHoraEvento;
            ViewBag.Eventos = Eventos;
            ViewBag.numRegistrosEvento = numRegistrosEvento;

            ViewBag.listaEventosDescricao = listaEventosDescricao;

            return View();
        }
    }
}