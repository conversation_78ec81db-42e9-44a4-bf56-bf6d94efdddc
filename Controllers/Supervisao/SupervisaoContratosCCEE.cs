﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SupervisaoController
    {
        // GET: Supervisao ContratosCCEE
        public ActionResult ContratosCCEE(int IDCliente = 0)
        {
            // tela de ajuda - contratos CCEE
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "ContratosCCEE");

            // le cookies
            LeCookies_SmartEnergy();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // le clientes do consultor - ativos
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clienteMetodos.ListarPorIDConsultor(IDConsultor, 1);

            // calcula 
            Calc_Quantidades(listaClientes);

            // le empresas
            EmpresasMetodos empresasMetodos = new EmpresasMetodos();
            List<EmpresasDominio> listaEmpresas = empresasMetodos.ListarTodos();

            // le contratos CCEE vigentes do cliente
            ContratosCCEEMetodos contratosMetodos = new ContratosCCEEMetodos();
            List<ContratosCCEEDominio> listaContratos = contratosMetodos.ListarPorVigentes(IDCliente);

            // número de contratos vigentes
            int NumContratosVigentes = 0;

            // percorre contratos para resgatar nome dos clientes e empresas
            if (listaContratos != null)
            {
                foreach (ContratosCCEEDominio contrato in listaContratos)
                {
                    // nome e logo do cliente
                    ClientesDominio cliente = listaClientes.Find(c => c.IDCliente == contrato.IDCliente);

                    if (cliente != null)
                    {
                        contrato.NomeCliente = cliente.Nome;
                        contrato.Logo = cliente.Logo;
                    }

                    // SiglaCCEE da empresa
                    EmpresasDominio empresa = listaEmpresas.Find(e => e.IDEmpresa == contrato.IDEmpresa);

                    if (empresa != null)
                    {
                        contrato.RazaoSocial = empresa.RazaoSocial;
                        contrato.SiglaCCEE = empresa.SiglaCCEE;
                        contrato.CNPJ = empresa.CNPJ;
                        contrato.IDEstado = empresa.IDEstado;
                        contrato.IDCidade = empresa.IDCidade;
                    }

                    // verifica se contrato está vigente
                    if (contrato.Contrato_Status == 1)
                    {
                        NumContratosVigentes++;
                    }
                }
            }

            // número de contratos vigentes
            ViewBag.NumContratosVigentes = NumContratosVigentes;

            return View(listaContratos);
        }

        public void Calc_Quantidades(List<ClientesDominio> clientes)
        {
            // numero de clientes
            int NumClientes_total = 0;

            // numero de medicoes e gateways
            int NumGateways_total = 0;
            int NumMedicoes_total = 0;

            if (clientes != null)
            {
                foreach (ClientesDominio cliente in clientes)
                {
                    // menos o cliente zero
                    if (cliente.IDCliente == 0)
                    {
                        continue;
                    }

                    // numero de clientes
                    NumClientes_total++;

                    // numero de medicoes com este cliente
                    MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                    int NumMedicoes = medicoesMetodos.NumMedicoesClienteTipo(cliente.IDCliente, 0);
                    NumMedicoes_total += NumMedicoes;

                    // numero de gateways com este cliente
                    int NumGateways = medicoesMetodos.NumGatewaysCliente(cliente.IDCliente, 0);
                    NumGateways_total += NumGateways;
                }
            }

            ViewBag.ListaClientes = clientes;
            ViewBag.NumClientes = NumClientes_total;
            ViewBag.NumGateways = NumGateways_total;
            ViewBag.NumMedicoes = NumMedicoes_total;

            return;
        }
    }
}