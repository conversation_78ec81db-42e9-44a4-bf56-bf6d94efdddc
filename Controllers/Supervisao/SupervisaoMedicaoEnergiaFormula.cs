﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SupervisaoController
    {

        // GET: Medicao_Energia_Formula
        public unsafe ActionResult Medicao_Energia_Formula(int IDCliente, int IDMedicao)
        {
            // tela de ajuda - supervisao medicao atual
            CookieStore.SalvaCookie_String("PaginaAjuda", "Supervisao_Energia");

            // le supervisao da medicao
            SupervMedicoesMetodos medicoesMetodos = new SupervMedicoesMetodos();
            SupervMedicoesDominio listaMedicoes = medicoesMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

            // salva cookie cliente
            CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);

            // le contrato cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cli = clienteMetodos.ListarPorId(IDCliente);
            CookieStore.SalvaCookie_Int("IDTipoContrato", cli.IDTipoContrato);

            // salva cookie medicao
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", listaMedicoes.IDTipoMedicao_MD);
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Supervisao");
            CookieStore.SalvaCookie_Int("_IDGateway", listaMedicoes.IDGW_GW);
            CookieStore.SalvaCookie_Int("IDTipoGateway", listaMedicoes.IDTipoGateway_GW);

            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // protege datas
            if( listaMedicoes.DataHoraAtualizacao == null )
            {
                listaMedicoes.DataHoraAtualizacao = "01/01/2000 00:00:00";
            }

            if (listaMedicoes.DataHoraEq == null)
            {
                listaMedicoes.DataHoraEq = "01/01/2000 00:00:00";
            }

            // funcao de supervisao
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            DATAHORA data_hora = new DATAHORA();
            ENERGIA_SUPERVMEDICAO superv = new ENERGIA_SUPERVMEDICAO();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = listaMedicoes.IDGW_GW;

            // forca pegar ultimo dia
            data_hora.data.dia = (char)1;
            data_hora.data.mes = (char)1;
            data_hora.data.ano = 2000;
            data_hora.hora.hora = (char)0;
            data_hora.hora.min = (char)0;
            data_hora.hora.seg = (char)0;

            //
            // SUPERVISAO
            //

            // calcula valores
            retorno =  SmCalcDB_SupervMedicao_Energia((char)0, ref config_interface, ref data_hora, ref superv);

            // lista de erros
            var listaErros = new List<string>();

            if( isUser.isGESTAL(ViewBag._IDTipoAcesso) )
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Função [retorno {0}]", retorno));
                if (superv.flag_dia_atual > 0)
                    listaErros.Add(string.Format("Dia Atual [{0}]", Funcoes_SmartEnergy.ErroRelatorio((int)superv.flag_dia_atual)));
                if (superv.flag_mes_atual > 0)
                    listaErros.Add(string.Format("Mês Atual [{0}]", Funcoes_SmartEnergy.ErroRelatorio((int)superv.flag_mes_atual)));
                if (superv.flag_mes_proj > 0)
                    listaErros.Add(string.Format("Mês Projetado [{0}]", Funcoes_SmartEnergy.ErroRelatorio((int)superv.flag_mes_proj)));
                if (superv.flag_anterior1 > 0)
                    listaErros.Add(string.Format("Mês Anterior 1 [{0}]", Funcoes_SmartEnergy.ErroRelatorio((int)superv.flag_anterior1)));
                if (superv.flag_anterior2 > 0)
                    listaErros.Add(string.Format("Mês Anterior 2 [{0}]", Funcoes_SmartEnergy.ErroRelatorio((int)superv.flag_anterior2)));
            }

            // calcula tolerancia
            double Tol_ContratoP = superv.Dem_ContratoP * (1.0 + (superv.Tol_ContratoP / 100.0));
            double Tol_ContratoFP = superv.Dem_ContratoFP * (1.0 + (superv.Tol_ContratoFP / 100.0));

            // converte data e hora
            DateTime DataHora_Atual = Funcoes_Converte.ConverteDataHora2DateTime(superv.DataAtual);
            DateTime DataHora_Ultimo = Funcoes_Converte.ConverteDataHora2DateTime(superv.DataHora_Ultimo);

            DateTime Dem_Dia_DemMaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dem_Dia.DemMaxP_DataHora);
            DateTime Dem_Dia_DemMaxFP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dem_Dia.DemMaxFP_DataHora);
            DateTime Dem_Mes_DemMaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dem_Mes.DemMaxP_DataHora);
            DateTime Dem_Mes_DemMaxFP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dem_Mes.DemMaxFP_DataHora);
            DateTime Dem_MesAnt_DemMaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dem_MesAnt.DemMaxP_DataHora);
            DateTime Dem_MesAnt_DemMaxFP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dem_MesAnt.DemMaxFP_DataHora);

            DateTime FatPot_Dia_FatPotFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.FatPot_Dia.FatPotFPC_DataHora);
            DateTime FatPot_Dia_FatPotFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.FatPot_Dia.FatPotFPI_DataHora);
            DateTime FatPot_Mes_FatPotFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.FatPot_Mes.FatPotFPC_DataHora);
            DateTime FatPot_Mes_FatPotFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.FatPot_Mes.FatPotFPI_DataHora);
            DateTime FatPot_MesAnt_FatPotFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.FatPot_MesAnt.FatPotFPC_DataHora);
            DateTime FatPot_MesAnt_FatPotFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.FatPot_MesAnt.FatPotFPI_DataHora);

            DateTime Fatura_Atual_Data = Funcoes_Converte.ConverteData2DateTime(superv.Fatura_Atual.Data, 1);
            DateTime Fatura_AtualProj_Data = Funcoes_Converte.ConverteData2DateTime(superv.Fatura_Atual.Data, 1);
            DateTime Fatura_Anterior1_Data = Funcoes_Converte.ConverteData2DateTime(superv.Fatura_Anterior1.Data, 1);
            DateTime Fatura_Anterior2_Data = Funcoes_Converte.ConverteData2DateTime(superv.Fatura_Anterior2.Data, 1);

            // datas
            ViewBag.DiaAtual = string.Format("{0:d}", DataHora_Atual);
            ViewBag.MesAtual = string.Format("{0:Y}", DataHora_Atual);
            ViewBag.MesAnterior1 = string.Format("{0:Y}", DataHora_Atual.AddMonths(-1));
            ViewBag.MesAnterior2 = string.Format("{0:Y}", DataHora_Atual.AddMonths(-2));

            // grafico de demanda
            var Dias_Demanda = new string[98];
            var Demanda = new double[98];
            var Periodo = new double[98];
            var Contrato = new double[98];
            var Tolerancia = new double[98];

            double Dem_max = 0.0;

            // valores
            DateTime strData = Funcoes_Converte.ConverteDataHora2DateTime(superv.Demanda_DataHora[0]);

            // volta 15min para a primeira barra
            strData = strData.AddMinutes(-15);

            int i = 0;
            int j = 0;

            for (i = 0; i < 98; i++)
            {
                // formata label
                Dias_Demanda[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proximo quinze minutos
                strData = strData.AddMinutes(15);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    Demanda[i] = superv.Demanda[0];
                    Periodo[i] = superv.Periodo[0] ;
                    Contrato[i] = superv.Contrato[0];
                    Tolerancia[i] = superv.Contrato[0] * (1.0 + (superv.Tol_ContratoFP / 100.0));
                }

                if (i == 97)
                {
                    // zera
                    Demanda[i] = superv.Demanda[95];
                    Periodo[i] = superv.Periodo[95];
                    Contrato[i] = superv.Contrato[95];
                    Tolerancia[i] = superv.Contrato[95] * (1.0 + (superv.Tol_ContratoFP / 100.0));
                }

                if (i >= 1 && i <= 96)
                {
                    // copia
                    j = i - 1;

                    Demanda[i] = superv.Demanda[j];
                    Periodo[i] = superv.Periodo[j];
                    Contrato[i] = superv.Contrato[j];

                    if (Periodo[i] == 0)
                    {
                        Tolerancia[i] = superv.Contrato[j] * (1.0 + (superv.Tol_ContratoP / 100.0));
                    }
                    else
                    {
                        Tolerancia[i] = superv.Contrato[j] * (1.0 + (superv.Tol_ContratoFP / 100.0));
                    }

                    // verifica se sem registro
                    if (Periodo[i] == 3)
                    {
                        Demanda[i] = 0.0;
                    }

                    // verifica demanda maxima
                    if (Demanda[i] > Dem_max)
                        Dem_max = Demanda[i];

                    if (Contrato[i] > Dem_max)
                        Dem_max = Contrato[i];

                    if (Tolerancia[i] > Dem_max)
                        Dem_max = Tolerancia[i];
                }
            }

            Dem_max = Dem_max * 1.1;

            if(Dem_max < 1.0)
            {
                Dem_max = 1.0;
            }

            ViewBag.DemMax = Dem_max;

            ViewBag.Demanda = Demanda;
            ViewBag.Periodo = Periodo;
            ViewBag.Contrato = Contrato;
            ViewBag.Tolerancia = Tolerancia;
            ViewBag.Dias_Demanda = Dias_Demanda;

            // grafico de fator de potencia
            var Dias_FatPot = new string[26];
            var FatPot = new double[26];
            var Posto = new double[26];

            double FatPot_min = 1.0;

            // valores
            strData = Funcoes_Converte.ConverteDataHora2DateTime(superv.FatPot_DataHora[0]);

            // volta 1hora para a primeira barra
            strData = strData.AddHours(-1);

            i = 0;
            j = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Dias_FatPot[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proxima hora
                strData = strData.AddHours(1);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    FatPot[i] = 1.0;
                    Posto[i] = 3;
                }

                // verifica se ultima barra
                if (i == 25)
                {
                    // zera
                    FatPot[i] = 1.0;
                    Posto[i] = 3;
                }

                if (i >= 1 && i <= 24)
                {
                    // copia
                    j = i - 1;

                    FatPot[i] = superv.FatPot[j];
                    Posto[i] = superv.Posto[j];

                    // verifica se sem registro
                    if (Posto[i] == 3 || FatPot[i] == 0.0)
                    {
                        FatPot[i] = 1.0;
                    }

                    // verifica fatpot minimo
                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPot[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPot[i]);
                }
            }

            if (FatPot_min > 0.92)
                FatPot_min = 0.92;

            FatPot_min = FatPot_min - (FatPot_min * 0.01);

            ViewBag.FatPotMin = FatPot_min;

            ViewBag.FatPot = FatPot;
            ViewBag.Posto = Posto;
            ViewBag.Dias_FatPot = Dias_FatPot;

            // demanda
            ViewBag.Dem_Dia_DemMaxP = string.Format("{0:#,##0.0}", superv.Dem_Dia.DemMaxP);
            if (Dem_Dia_DemMaxP_DataHora.Year != 2000)
                ViewBag.Dem_Dia_DemMaxP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_Dia_DemMaxP_DataHora, Dem_Dia_DemMaxP_DataHora);
            else
                ViewBag.Dem_Dia_DemMaxP_DataHora = "--/--/---- --:--";

            ViewBag.Dem_Dia_DemMaxFP = string.Format("{0:#,##0.0}", superv.Dem_Dia.DemMaxFP);
            if (Dem_Dia_DemMaxFP_DataHora.Year != 2000)
                ViewBag.Dem_Dia_DemMaxFP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_Dia_DemMaxFP_DataHora, Dem_Dia_DemMaxFP_DataHora);
            else
                ViewBag.Dem_Dia_DemMaxFP_DataHora = "--/--/---- --:--";

            ViewBag.Dem_Mes_DemMaxP = string.Format("{0:#,##0.0}", superv.Dem_Mes.DemMaxP);
            if (Dem_Mes_DemMaxP_DataHora.Year != 2000)
                ViewBag.Dem_Mes_DemMaxP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_Mes_DemMaxP_DataHora, Dem_Mes_DemMaxP_DataHora);
            else
                ViewBag.Dem_Mes_DemMaxP_DataHora = "--/--/---- --:--";

            ViewBag.Dem_Mes_DemMaxFP = string.Format("{0:#,##0.0}", superv.Dem_Mes.DemMaxFP);
            if (Dem_Mes_DemMaxFP_DataHora.Year != 2000)
                ViewBag.Dem_Mes_DemMaxFP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_Mes_DemMaxFP_DataHora, Dem_Mes_DemMaxFP_DataHora);
            else
                ViewBag.Dem_Mes_DemMaxFP_DataHora = "--/--/---- --:--";

            ViewBag.Dem_MesAnt_DemMaxP = string.Format("{0:#,##0.0}", superv.Dem_MesAnt.DemMaxP);
            if (Dem_MesAnt_DemMaxP_DataHora.Year != 2000)
                ViewBag.Dem_MesAnt_DemMaxP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MesAnt_DemMaxP_DataHora, Dem_MesAnt_DemMaxP_DataHora);
            else
                ViewBag.Dem_MesAnt_DemMaxP_DataHora = "--/--/---- --:--";

            ViewBag.Dem_MesAnt_DemMaxFP = string.Format("{0:#,##0.0}", superv.Dem_MesAnt.DemMaxFP);
            if (Dem_MesAnt_DemMaxFP_DataHora.Year != 2000)
                ViewBag.Dem_MesAnt_DemMaxFP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MesAnt_DemMaxFP_DataHora, Dem_MesAnt_DemMaxFP_DataHora);
            else
                ViewBag.Dem_MesAnt_DemMaxFP_DataHora = "--/--/---- --:--";

            ViewBag.Dem_ContratoP = superv.Dem_ContratoP;
            ViewBag.Dem_ContratoFP = superv.Dem_ContratoFP;

            ViewBag.Tol_ContratoP = superv.Tol_ContratoP;
            ViewBag.Tol_ContratoFP = superv.Tol_ContratoFP;

            // verifico se houve ultrapassagem
            double A = superv.Dem_Mes.DemMaxP;
            double B = Tol_ContratoP;
            double Dem_Mes_UltrapassagemP = (A > B) ? superv.Dem_Mes.DemMaxP : 0.0;

            ViewBag.Dem_Mes_UltrapassagemP = Dem_Mes_UltrapassagemP;

            A = superv.Dem_Mes.DemMaxFP;
            B = Tol_ContratoFP;
            double Dem_Mes_UltrapassagemFP = (A > B) ? superv.Dem_Mes.DemMaxFP : 0.0;

            ViewBag.Dem_Mes_UltrapassagemFP = Dem_Mes_UltrapassagemFP;

            // consumo

            // encontro o maior consumo
            double Cons_Dia_ConsumoTotal = superv.Cons_Dia.ConsumoP + superv.Cons_Dia.ConsumoFP;
            double Cons_Mes_ConsumoTotal = superv.Cons_Mes.ConsumoP + superv.Cons_Mes.ConsumoFP;
            double Cons_MesProj_ConsumoTotal = superv.Cons_MesProj.ConsumoP + superv.Cons_MesProj.ConsumoFP;
            double Cons_MesAnt_ConsumoTotal = superv.Cons_MesAnt.ConsumoP + superv.Cons_MesAnt.ConsumoFP;

            double maior_consumo = Cons_Dia_ConsumoTotal;

            if (Cons_Mes_ConsumoTotal > maior_consumo) 
                maior_consumo = Cons_Mes_ConsumoTotal;

            if (Cons_MesProj_ConsumoTotal > maior_consumo)
                maior_consumo = Cons_MesProj_ConsumoTotal;

            if (Cons_MesAnt_ConsumoTotal > maior_consumo)
                maior_consumo = Cons_MesAnt_ConsumoTotal;

            string unidade_consumo = "kWh";
            double divisao_consumo = 1.0;

            ViewBag.UnidadeConsumo = unidade_consumo;

            ViewBag.Cons_Dia_ConsumoP = Funcoes_SmartEnergy.FormatValor(superv.Cons_Dia.ConsumoP / divisao_consumo);
            ViewBag.Cons_Dia_ConsumoFP = Funcoes_SmartEnergy.FormatValor(superv.Cons_Dia.ConsumoFP / divisao_consumo);
            ViewBag.Cons_Dia_ConsumoTotal = Funcoes_SmartEnergy.FormatValor(Cons_Dia_ConsumoTotal / divisao_consumo);

            ViewBag.Cons_Mes_ConsumoP = Funcoes_SmartEnergy.FormatValor(superv.Cons_Mes.ConsumoP / divisao_consumo);
            ViewBag.Cons_Mes_ConsumoFP = Funcoes_SmartEnergy.FormatValor(superv.Cons_Mes.ConsumoFP / divisao_consumo);
            ViewBag.Cons_Mes_ConsumoTotal = Funcoes_SmartEnergy.FormatValor(Cons_Mes_ConsumoTotal / divisao_consumo);

            ViewBag.Cons_MesProj_ConsumoP = Funcoes_SmartEnergy.FormatValor(superv.Cons_MesProj.ConsumoP / divisao_consumo);
            ViewBag.Cons_MesProj_ConsumoFP = Funcoes_SmartEnergy.FormatValor(superv.Cons_MesProj.ConsumoFP / divisao_consumo);
            ViewBag.Cons_MesProj_ConsumoTotal = Funcoes_SmartEnergy.FormatValor(Cons_MesProj_ConsumoTotal / divisao_consumo);

            ViewBag.Cons_MesAnt_ConsumoP = Funcoes_SmartEnergy.FormatValor(superv.Cons_MesAnt.ConsumoP / divisao_consumo);
            ViewBag.Cons_MesAnt_ConsumoFP = Funcoes_SmartEnergy.FormatValor(superv.Cons_MesAnt.ConsumoFP / divisao_consumo);
            ViewBag.Cons_MesAnt_ConsumoTotal = Funcoes_SmartEnergy.FormatValor(Cons_MesAnt_ConsumoTotal / divisao_consumo);

            // diferenca em % entre total Anterior e Projetado
            A = Cons_MesAnt_ConsumoTotal;
            B = Cons_MesProj_ConsumoTotal;
            double PorcAntProj = (A == 0.0) ? 0.0 : ((B - A) / A) * 100.0;

            ViewBag.Cons_MesProj_PorcAntN = PorcAntProj;
            ViewBag.Cons_MesProj_PorcAnt = string.Format("{0:0.0} %", PorcAntProj);

            // fator de potencia
            ViewBag.FatPot_Dia_FatPotFPC = string.Format("{0:0.000}", superv.FatPot_Dia.FatPotFPC);
            if (FatPot_Dia_FatPotFPC_DataHora.Year != 2000)
                ViewBag.FatPot_Dia_FatPotFPC_DataHora = string.Format("{0:d} {1:HH:mm}", FatPot_Dia_FatPotFPC_DataHora, FatPot_Dia_FatPotFPC_DataHora);
            else
                ViewBag.FatPot_Dia_FatPotFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_Dia_FatPotFPI = string.Format("{0:0.000}", superv.FatPot_Dia.FatPotFPI);
            if (FatPot_Dia_FatPotFPI_DataHora.Year != 2000)
                ViewBag.FatPot_Dia_FatPotFPI_DataHora = string.Format("{0:d} {1:HH:mm}", FatPot_Dia_FatPotFPI_DataHora, FatPot_Dia_FatPotFPI_DataHora);
            else
                ViewBag.FatPot_Dia_FatPotFPI_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_Mes_FatPotFPC = string.Format("{0:0.000}", superv.FatPot_Mes.FatPotFPC);
            if (FatPot_Mes_FatPotFPC_DataHora.Year != 2000)
                ViewBag.FatPot_Mes_FatPotFPC_DataHora = string.Format("{0:d} {1:HH:mm}", FatPot_Mes_FatPotFPC_DataHora, FatPot_Mes_FatPotFPC_DataHora);
            else
                ViewBag.FatPot_Mes_FatPotFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_Mes_FatPotFPI = string.Format("{0:0.000}", superv.FatPot_Mes.FatPotFPI);
            if (FatPot_Mes_FatPotFPI_DataHora.Year != 2000)
                ViewBag.FatPot_Mes_FatPotFPI_DataHora = string.Format("{0:d} {1:HH:mm}", FatPot_Mes_FatPotFPI_DataHora, FatPot_Mes_FatPotFPI_DataHora);
            else
                ViewBag.FatPot_Mes_FatPotFPI_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MesAnt_FatPotFPC = string.Format("{0:0.000}", superv.FatPot_MesAnt.FatPotFPC);
            if (FatPot_MesAnt_FatPotFPC_DataHora.Year != 2000)
                ViewBag.FatPot_MesAnt_FatPotFPC_DataHora = string.Format("{0:d} {1:HH:mm}", FatPot_MesAnt_FatPotFPC_DataHora, FatPot_MesAnt_FatPotFPC_DataHora);
            else
                ViewBag.FatPot_MesAnt_FatPotFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MesAnt_FatPotFPI = string.Format("{0:0.000}", superv.FatPot_MesAnt.FatPotFPI);
            if (FatPot_MesAnt_FatPotFPI_DataHora.Year != 2000)
                ViewBag.FatPot_MesAnt_FatPotFPI_DataHora = string.Format("{0:d} {1:HH:mm}", FatPot_MesAnt_FatPotFPI_DataHora, FatPot_MesAnt_FatPotFPI_DataHora);
            else
                ViewBag.FatPot_MesAnt_FatPotFPI_DataHora = "--/--/---- --:--";

            ViewBag.FatPot_Referencia = string.Format("{0:0.000}", 0.92);

            // faturas
            if (Fatura_Atual_Data.Year != 2000)
                ViewBag.Fatura_Atual_Data = string.Format("{0:Y}", Fatura_Atual_Data);
            else
                ViewBag.Fatura_Atual_Data = "--------";
            ViewBag.Fatura_Atual_Bandeira = Funcoes_SmartEnergy.BandeiraCor(superv.Fatura_Atual.Bandeira);
            ViewBag.Fatura_Atual_BandeiraTexto = Funcoes_SmartEnergy.BandeiraTexto(superv.Fatura_Atual.Bandeira);
            ViewBag.Fatura_Atual_ValorTotal = string.Format("{0:C}", superv.Fatura_Atual.ValorTotal);
            ViewBag.Fatura_Atual_ConsumoTotal = string.Format("{0:#,##0}", superv.Fatura_Atual.ConsumoTotal);
            ViewBag.Fatura_Atual_CustoMedio = string.Format("{0:C} / MWh", superv.Fatura_Atual.CustoMedio);

            if (Fatura_AtualProj_Data.Year != 2000)
                ViewBag.Fatura_AtualProj_Data = string.Format("{0:Y}", Fatura_AtualProj_Data);
            else
                ViewBag.Fatura_AtualProj_Data = "--------";
            ViewBag.Fatura_AtualProj_Bandeira = Funcoes_SmartEnergy.BandeiraCor(superv.Fatura_AtualProj.Bandeira);
            ViewBag.Fatura_AtualProj_BandeiraTexto = Funcoes_SmartEnergy.BandeiraTexto(superv.Fatura_AtualProj.Bandeira);
            ViewBag.Fatura_AtualProj_ValorTotal = string.Format("{0:C}", superv.Fatura_AtualProj.ValorTotal);
            ViewBag.Fatura_AtualProj_ConsumoTotal = string.Format("{0:#,##0}", superv.Fatura_AtualProj.ConsumoTotal);
            ViewBag.Fatura_AtualProj_CustoMedio = string.Format("{0:C} / MWh", superv.Fatura_AtualProj.CustoMedio);

            ViewBag.Fatura_PorcAnt1ProjN = superv.Fatura_PorcAnt1Proj;
            ViewBag.Fatura_PorcAnt1Proj = string.Format("{0:0.0} %", superv.Fatura_PorcAnt1Proj);

            if (Fatura_Anterior1_Data.Year != 2000)
                ViewBag.Fatura_Anterior1_Data = string.Format("{0:Y}", Fatura_Anterior1_Data);
            else
                ViewBag.Fatura_Anterior1_Data = "--------";
            ViewBag.Fatura_Anterior1_Bandeira = Funcoes_SmartEnergy.BandeiraCor(superv.Fatura_Anterior1.Bandeira);
            ViewBag.Fatura_Anterior1_BandeiraTexto = Funcoes_SmartEnergy.BandeiraTexto(superv.Fatura_Anterior1.Bandeira);
            ViewBag.Fatura_Anterior1_ValorTotal = string.Format("{0:C}", superv.Fatura_Anterior1.ValorTotal);
            ViewBag.Fatura_Anterior1_ConsumoTotal = string.Format("{0:#,##0}", superv.Fatura_Anterior1.ConsumoTotal);
            ViewBag.Fatura_Anterior1_CustoMedio = string.Format("{0:C} / MWh", superv.Fatura_Anterior1.CustoMedio);

            ViewBag.Fatura_PorcAnt2Ant1N = superv.Fatura_PorcAnt2Ant1;
            ViewBag.Fatura_PorcAnt2Ant1 = string.Format("{0:0.0} %", superv.Fatura_PorcAnt2Ant1);

            if (Fatura_Anterior2_Data.Year != 2000)
                ViewBag.Fatura_Anterior2_Data = string.Format("{0:Y}", Fatura_Anterior2_Data);
            else
                ViewBag.Fatura_Anterior2_Data = "--------";
            ViewBag.Fatura_Anterior2_Bandeira = Funcoes_SmartEnergy.BandeiraCor(superv.Fatura_Anterior2.Bandeira);
            ViewBag.Fatura_Anterior2_BandeiraTexto = Funcoes_SmartEnergy.BandeiraTexto(superv.Fatura_Anterior2.Bandeira);
            ViewBag.Fatura_Anterior2_ValorTotal = string.Format("{0:C}", superv.Fatura_Anterior2.ValorTotal);
            ViewBag.Fatura_Anterior2_ConsumoTotal = string.Format("{0:#,##0}", superv.Fatura_Anterior2.ConsumoTotal);
            ViewBag.Fatura_Anterior2_CustoMedio = string.Format("{0:C} / MWh", superv.Fatura_Anterior2.CustoMedio);

            // grafico pizza
            ViewBag.Fatura_Total = superv.Fatura_Atual.ValorTotal;
            ViewBag.Porc_Demanda = superv.Fatura_AtualProj.Porc_Demanda;
            ViewBag.Porc_Consumo = superv.Fatura_AtualProj.Porc_Consumo;
            ViewBag.Porc_Imposto = superv.Fatura_AtualProj.Porc_Imposto;
            ViewBag.Porc_Multa = superv.Fatura_AtualProj.Porc_Multa;

            // lista de erros
            if (retorno > 0)
                listaErros.Add(string.Format("Retorno SmCalcDB_Eventos_Descricao {0}", retorno));

            @ViewBag.listaErros = listaErros;

            // formula
            ViewBag.Formula = listaMedicoes.Formula;
            ViewBag.GatewayAtualizacao = string.Format("{0:d} {1:HH:mm}", DataHora_Ultimo, DataHora_Ultimo);

            return View();
        }
    }
}