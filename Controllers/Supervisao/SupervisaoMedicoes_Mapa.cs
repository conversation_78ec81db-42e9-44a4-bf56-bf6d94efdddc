﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;
using SmartEnergyLib.AppleWeatherKit;
using SmartEnergyLib.AppleWeatherKit.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SupervisaoController
    {
        // GET: Supervisao Medicoes Mapa
        public ActionResult Medicoes_Mapa(int IDCliente)
        {
            // verifica se é cliente
            if (IDCliente > 0)
            {
                // salva cookie
                CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);

                // le contrato cliente
                ClientesMetodos clienteMetodos = new ClientesMetodos();
                ClientesDominio cli = clienteMetodos.ListarPorId(IDCliente);
                CookieStore.SalvaCookie_Int("IDTipoContrato", cli.IDTipoContrato);
                CookieStore.SalvaCookie_Int("IDTipoSupervisao", cli.IDTipoSupervisao);
                CookieStore.SalvaCookie_String("Nome_GrupoUnidades", cli.Nome_GrupoUnidades);
                CookieStore.SalvaCookie_String("Nome_Unidades", cli.Nome_Unidades);
                CookieStore.SalvaCookie_Int("IDTipoGrafico_Demanda", cli.IDTipoGrafico_Demanda);
            }

            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Supervisao");

            // le cookies
            LeCookies_SmartEnergy();

            // tela de ajuda - supervisao normal
            CookieStore.SalvaCookie_String("PaginaAjuda", "Supervisao_Medicoes");

            // le cookies
            LeCookies_SmartEnergy();

            // lista de empresas
            List<SupervEmpresasMapaDominio> listaEmpresas = new List<SupervEmpresasMapaDominio>();

            // verifica se é para apresentar todos os clientes do gestor
            if (IDCliente == 0)
            {
                // lista de clientes
                List<int> ConfigCliList = ViewBag._ConfigCli;

                // le clientes - somente ativos
                ClientesMetodos clientesMetodos = new ClientesMetodos();
                List<ClientesDominio> listaClientes = clientesMetodos.ListarTodos(ConfigCliList, 1);

                // percorre clientes
                foreach (ClientesDominio cliente in listaClientes)
                {
                    // adiciona empresas do cliente na lista
                    AdicionaEmpresasCliente(cliente.IDCliente, ref listaEmpresas);
                }
            }
            else
            {
                // adiciona empresas do cliente na lista
                AdicionaEmpresasCliente(IDCliente, ref listaEmpresas);
            }

            // lista de empresas
            ViewBag.listaEmpresas = listaEmpresas;
            ViewBag.listaEmpresas_tamanho = listaEmpresas.Count;

            return View();
        }


        // adiciona empresas do cliente na lista
        private void AdicionaEmpresasCliente(int IDCliente, ref List<SupervEmpresasMapaDominio> listaEmpresas)
        {
            // tipo de acesso do usuario
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // lista de medicoes
            List<int> ConfigMedList = ViewBag._ConfigMed;

            // le supervisao das medicoes
            SupervMedicoesMapaMetodos medicoesMetodos = new SupervMedicoesMapaMetodos();
            List<SupervMedicoesMapaDominio> listaMedicoes = medicoesMetodos.ListarPorIDCliente(IDCliente, IDTipoAcesso, ConfigMedList);

            // percorre medições habilitadas para o usuário
            foreach (SupervMedicoesMapaDominio medicao in listaMedicoes)
            {
                // analisa alarmes da medição
                string Status_Texto = "";
                medicao.Status = AnalisaAlarmesMedicao(medicao, ref Status_Texto);
                medicao.Status_Texto = Status_Texto;

                // verifica se empresa já esta na lista
                SupervEmpresasMapaDominio empresa = listaEmpresas.Find(item => item.IDEmpresa == medicao.IDEmpresa);

                // não esta na lista, adiciono
                if (empresa == null)
                {
                    SupervEmpresasMapaDominio nova_empresa = new SupervEmpresasMapaDominio();

                    nova_empresa.IDEmpresa = medicao.IDEmpresa;
                    nova_empresa.NomeEmpresa = medicao.NomeEmpresa;

                    if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE || IDTipoAcesso == TIPO_ACESSO.GESTAL_VENDAS)
                    {
                        nova_empresa.NomeEmpresa = string.Format("[{0}] {1}\n{2}", medicao.IDCliente, medicao.NomeCliente, medicao.NomeEmpresa);
                    }

                    if (IDTipoAcesso == TIPO_ACESSO.CONSULTOR || IDTipoAcesso == TIPO_ACESSO.CONSULTOR_ADMIN || IDTipoAcesso == TIPO_ACESSO.CONSULTOR_OPER)
                    {
                        nova_empresa.NomeEmpresa = string.Format("{0}\n{1}", medicao.NomeCliente, medicao.NomeEmpresa);
                    }

                    nova_empresa.Latitude = medicao.Latitude;
                    nova_empresa.Longitude = medicao.Longitude;
                    nova_empresa.EnderecoCompleto = medicao.EnderecoCompleto;
                    nova_empresa.Status = medicao.Status;

                    // adiciona
                    listaEmpresas.Add(nova_empresa);
                }
                else
                {
                    // verifica status da medição
                    if (medicao.Status > 0)
                    {
                        empresa.Status = medicao.Status;
                    }
                }
            }

            return;
        }


        //
        // EMPRESAS
        //

        // GET: Obter supervisão das medições da empresa
        public JsonResult ObterSupervMedicoesEmpresa(int IDEmpresa)
        {

            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // lista de medicoes habilitadas
            List<int> ConfigMedList = ViewBag._ConfigMed;

            // lista de medicoes
            List<SupervMedicoesMapaDominio> listaMedicoesNova = new List<SupervMedicoesMapaDominio>();

            // le supervisao das medicoes
            SupervMedicoesMapaMetodos medicoesMetodos = new SupervMedicoesMapaMetodos();
            List<SupervMedicoesMapaDominio> listaMedicoes = medicoesMetodos.ListarPorIDEmpresa(IDEmpresa, IDTipoAcesso, ConfigMedList);

            // verifica se NÃO possui medições
            if (listaMedicoes == null)
            {
                // le empresa
                EmpresasMetodos empresaMetodos = new EmpresasMetodos();
                EmpresasDominio empresa = empresaMetodos.ListarPorId(IDEmpresa);

                // verifica
                if (empresa != null)
                {
                    SupervMedicoesMapaDominio medicao = new SupervMedicoesMapaDominio();

                    medicao.IDEmpresa = IDEmpresa;
                    medicao.NomeEmpresa = empresa.SiglaCCEE;
                    medicao.Logo = empresa.Logo;
                    medicao.IDMedicao = 0;

                    listaMedicoesNova.Add(medicao);
                }
            }
            else
            {
                // percorre medições habilitadas para o usuário
                foreach (SupervMedicoesMapaDominio medicao in listaMedicoes)
                {
                    // analisa alarmes da medição
                    string Status_Texto = "";
                    medicao.Status = AnalisaAlarmesMedicao(medicao, ref Status_Texto);
                    medicao.Status_Texto = Status_Texto;

                    // meteorologia
                    HistoricoWeatherMetodos weatherMetodos = new HistoricoWeatherMetodos();
                    HistoricoWeatherDominio meteorologia = weatherMetodos.MaisRecente(medicao.IDCidade);

                    if (meteorologia != null)
                    {
                        if (meteorologia.DataHora.Year > 2000)
                        {
                            // temperatura
                            medicao.Temp_Atual = meteorologia.Temp_Atual;

                            // icon
                            medicao.Tempo_Cod = meteorologia.Tempo_Cod;
                        }
                    }
                    else
                    {
                        // temperatura
                        medicao.Temp_Atual = 0.0;

                        // icon
                        medicao.Tempo_Cod = -1;
                    }
                }

                // copia
                listaMedicoesNova = listaMedicoes;
            }

            // retorna o valor em JSON
            return Json(listaMedicoesNova, JsonRequestBehavior.AllowGet);
        }

        //
        // STATUS
        //

        // analisa alarmes da medição
        private int AnalisaAlarmesMedicao(SupervMedicoesMapaDominio medicao, ref string Status_Texto)
        {
            int Status = 0;

            // verifica se envio de arquivo em atraso

            // data e hora
            DateTime data_hora_hoje = DateTime.Now;
            DateTime data_hora_atualizacao = medicao.DataHora;

            // diferenca
            TimeSpan diferenca = data_hora_hoje - data_hora_atualizacao;

            // verifica se atraso maior que 2 dias
            if (diferenca > new TimeSpan(48, 0, 0))
            {
                // gateway em atraso
                Status = 1;
                Status_Texto = string.Format("Atualização em Atraso [{0:g}]", data_hora_atualizacao);
            }

            //
            // DEMO
            //
            if (medicao.IDMedicao == 1)
            {
                // horário
                DateTime datahora_ultrapassagem = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 14, 45, 0);

                Status = 1;
                Status_Texto = string.Format("Ultrapassagem de Demanda [{0:g} - 1458 kW]", datahora_ultrapassagem);
            }

            // retorn alarme
            return (Status);
        }
    }
}