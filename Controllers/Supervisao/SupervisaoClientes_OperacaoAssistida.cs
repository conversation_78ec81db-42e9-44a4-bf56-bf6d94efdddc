﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SupervisaoController
    {
        // GET: Supervisao Clientes Operacao Assistida
        public ActionResult Clientes_OperacaoAssistida()
        {
            // tela de ajuda - supervisao de clientes
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Supervisao");

            // le cookies
            LeCookies_SmartEnergy();

            // lista de clientes
            List<int> ConfigCliList = ViewBag._ConfigCli;

            // le clientes - somente ativos
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = new List<ClientesDominio>();

            // verifica se consultor
            int IDConsultor = ViewBag._IDConsultor;

            if (IDConsultor > 0)
            {
                // lista de clientes do consultor - somente ativos
                listaClientes = clientesMetodos.ListarTodos(ConfigCliList, 1);
            }
            else
            {
                // lista de clientes - somente operação assistida
                listaClientes = clientesMetodos.ListarOperacaoAssistida();
            }


            // numero de clientes
            int NumClientes_total = 0;

            // numero de medicoes e gateways
            int NumGateways_total = 0;
            int NumMedicoesFisicas_total = 0;
            int NumMedicoesVirtuais_total = 0;

            // percorre clientes e descobre numero de medicoes associadas
            foreach (ClientesDominio cliente in listaClientes)
            {
                // menos o cliente zero
                if (cliente.IDCliente == 0)
                {
                    continue;
                }

                // número de clientes
                NumClientes_total++;

                // numero de empresas com este cliente (CPFL - Unidades Consumidoras)
                EmpresasMetodos empresasMetodos = new EmpresasMetodos();
                int NumEmpresas = empresasMetodos.NumEmpresasCliente(cliente.IDCliente);
                cliente.NumEmpresas = NumEmpresas;

                // numero de medicoes REAIS com este cliente
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                int NumMedicoesFisicas = medicoesMetodos.NumMedicoesClienteTipo(cliente.IDCliente, 1);
                NumMedicoesFisicas_total += NumMedicoesFisicas;
                cliente.NumMedicoes = NumMedicoesFisicas;

                // numero de medicoes VIRTUAIS com este cliente
                int NumMedicoesVirtuais = medicoesMetodos.NumMedicoesClienteTipo(cliente.IDCliente, 2);
                NumMedicoesVirtuais_total += NumMedicoesVirtuais;

                // numero de gateways com este cliente
                int NumGateways = medicoesMetodos.NumGatewaysCliente(cliente.IDCliente, 0);
                NumGateways_total += NumGateways;
                cliente.NumGateways = NumGateways;
            }

            ViewBag.NumClientes = NumClientes_total;
            ViewBag.NumGateways = NumGateways_total;
            ViewBag.NumMedicoesFisicas = NumMedicoesFisicas_total;
            ViewBag.NumMedicoesVirtuais = NumMedicoesVirtuais_total;

            // le agentes
            EmpresasMetodos empresaMetodos = new EmpresasMetodos();
            List<EmpresasDominio> agentesCCEE = empresaMetodos.ListarPorAgenteCCEE(0);
            ViewBag.listaAgentesCCEE = agentesCCEE;

            return View(listaClientes);
        }
    }
}