﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SupervisaoController
    {
        // GET: Supervisao Medicoes
        public ActionResult Medicoes(int IDCliente)
        {
            // salva cookie
            CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);

            // le contrato cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cli = clienteMetodos.ListarPorId(IDCliente);
            CookieStore.SalvaCookie_Int("IDTipoContrato", cli.IDTipoContrato);
            CookieStore.SalvaCookie_Int("IDTipoSupervisao", cli.IDTipoSupervisao);
            CookieStore.SalvaCookie_String("Nome_GrupoUnidades", cli.Nome_GrupoUnidades);
            CookieStore.SalvaCookie_String("Nome_Unidades", cli.Nome_Unidades);
            CookieStore.SalvaCookie_Int("IDTipoGrafico_Demanda", cli.IDTipoGrafico_Demanda);

            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Supervisao");

            // le cookies
            LeCookies_SmartEnergy();

            // consultor
            int IDConsultor = ViewBag._IDConsultor;

            // verifica se supervisao normal
            if (cli.IDTipoSupervisao == 0)
            {
                // verifica se CPFL
                if (IDConsultor == CLIENTES_ESPECIAIS.CPFL)
                {
                    // tela de ajuda - supervisao CPFL
                    CookieStore.SalvaCookie_String("PaginaAjuda", "Supervisao_Medicoes_CPFL");
                }
                else
                {
                    // tela de ajuda - supervisao normal
                    CookieStore.SalvaCookie_String("PaginaAjuda", "Supervisao_Medicoes");
                }
            }
            else
            {
                // tela de ajuda - supervisao meta
                CookieStore.SalvaCookie_String("PaginaAjuda", "Supervisao_Medicoes_MetaConsumo");
            }

            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // lista de medicoes
            List<int> ConfigMedList = ViewBag._ConfigMed;

            // le supervisao das medicoes
            MedicoesSupervMetodos medicoesMetodos = new MedicoesSupervMetodos();
            List<MedicoesSupervDominio> listaMedicoes = medicoesMetodos.ListarPorIDCliente(IDCliente, cli.IDTipoSupervisao, IDTipoAcesso, ConfigMedList);

            // verifica se medicao esta selecionada
            int IDMedicao = ViewBag._IDMedicao;
            int IDGateway = ViewBag._IDGateway;
            bool encontrou = false;

            if(IDMedicao > 0 && listaMedicoes != null)
            {
                // verifica se medicao pertence ao cliente selecionado
                foreach (MedicoesSupervDominio medicao in listaMedicoes)
                {
                    if(medicao.IDMedicao == IDMedicao)
                    {
                        // medicao pertence ao cliente selecionado
                        encontrou = true;
                        break;
                    }
                }
            }

            // verifica se nao encontrou
            if( !encontrou)
            {
                CookieStore.SalvaCookie_Int("_IDMedicao", -10);
                CookieStore.SalvaCookie_Int("_IDGateway", -10);
                CookieStore.SalvaCookie_Int("_IDGateway", -10);
                CookieStore.SalvaCookie_Int("IDTipoGateway", -10);

                // le cookies
                LeCookies_SmartEnergy();
            }

            // observacao
            List<ObservacaoTagsDominio> tags = new List<ObservacaoTagsDominio>();
            ViewBag.tags = tags;

            // observacao
            ObservacaoMedicaoDominio observacao = new ObservacaoMedicaoDominio();
            int IDMedSelecionado = 0;

            ViewBag.observacao = observacao;
            ViewBag.IDMedSelecionado = IDMedSelecionado;

            // observacoes
            List<ObservacaoMedicaoDominio> observacoes = new List<ObservacaoMedicaoDominio>();
            ViewBag.Observacoes = observacoes;


            // verifica se supervisao meta
            if (cli.IDTipoSupervisao == 1)
            {
                return View("../Supervisao/Medicoes_Meta", listaMedicoes);
            }

            return View(listaMedicoes);
        }

        // GET: Observacoes Medicao
        public PartialViewResult _ObservacoesMedicao(int IDMedicao)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // tipos acesso
            ListaTiposMetodos listaMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listaTipoAcesso = listaMetodos.ListarTodos("TipoAcesso");

            ViewBag.listaTipoAcesso = listaTipoAcesso;

            // le tags
            List<ObservacaoTagsDominio> tags = listaMetodos.ListarTodos_ObservacaoTags();

            ViewBag.tags = tags;

            // observacao
            ObservacaoMedicaoDominio observacao = new ObservacaoMedicaoDominio();

            ViewBag.observacao = observacao;
            ViewBag.IDMedSelecionado = IDMedicao;

            // observacoes
            ObservacaoMedicaoMetodos observacaoMetodos = new ObservacaoMedicaoMetodos();
            List<ObservacaoMedicaoDominio> observacoes_lidas = observacaoMetodos.ListarTodosMedicao(IDMedicao);
            List<ObservacaoMedicaoDominio> observacoes = new List<ObservacaoMedicaoDominio>();

            // le observacoes
            if (observacoes_lidas != null)
            {
                // percorre observacoes
                foreach (ObservacaoMedicaoDominio obs in observacoes_lidas)
                {
                    bool adicionar = false;

                    // caso for admin GESTAL pode ver todas observacoes (inseridas pela GESTAL, consultores ou clientes)
                    if (isUser.isGESTAL(IDTipoAcesso))
                    {
                        // permite ele ver
                        adicionar = true;
                    }

                    // caso usuario atual for consultor pode ver apenas as de consultor e dos clientes
                    if (isUser.isConsultor(IDTipoAcesso))
                    {
                        // verifica se mensagem eh de consultor ou se de cliente habilitado para visualizar
                        if( isUser.isConsultor(obs.IDTipoAcesso) || obs.ClienteVisualiza)
                        {
                            adicionar = true;
                        }
                    }

                    // caso usuario atual for cliente pode ver apenas se habilitado para visualizar
                    if (isUser.isCliente(IDTipoAcesso))
                    {
                        // verifica se cliente habilitado para visualizar
                        if (obs.ClienteVisualiza)
                        {
                            adicionar = true;
                        }
                    }

                    // adiciona na lista
                    if( adicionar )
                    {
                        // adiona na lista
                        observacoes.Add(obs);
                    }
                }
            }

            ViewBag.Observacoes = observacoes;

            return PartialView();
        }

        // GET: Observacao Medicao
        public PartialViewResult _ObservacaoMedicao(int IDMedicao, int IDObservacao)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // le tags
            ListaTiposMetodos listaMetodos = new ListaTiposMetodos();
            List<ObservacaoTagsDominio> tags = listaMetodos.ListarTodos_ObservacaoTags();

            ViewBag.tags = tags;

            // observacao
            ObservacaoMedicaoDominio observacao = new ObservacaoMedicaoDominio();

            if( IDObservacao > 0 )
            {
                // le observacao
                ObservacaoMedicaoMetodos observacaoMetodos = new ObservacaoMedicaoMetodos();
                ObservacaoMedicaoDominio observacao_lido = observacaoMetodos.ListarPorId(IDObservacao);

                if (observacao_lido == null)
                {
                    observacao.IDObservacao = 0;
                    observacao.IDMedicao = IDMedicao;
                    observacao.DataHora = DateTime.Now;
                    observacao.IDTag = 0;
                    observacao.IDTipoAcesso = IDTipoAcesso;
                    observacao.ClienteVisualiza = false;
                    observacao.Observacao = "";
                }
                else
                {
                    observacao = observacao_lido;
                }
            }
            else
            {
                // novo
                observacao.IDObservacao = 0;
                observacao.IDMedicao = IDMedicao;
                observacao.DataHora = DateTime.Now;
                observacao.IDTag = 0;
                observacao.IDTipoAcesso = IDTipoAcesso;
                observacao.ClienteVisualiza = false;
                observacao.Observacao = "";
            }

            // caso usuario atual for cliente
            if (isUser.isCliente(IDTipoAcesso))
            {
                // cliente sempre visualiza se o usuario for o cliente
                observacao.ClienteVisualiza = true;
            }

            ViewBag.observacao = observacao;
            ViewBag.IDMedSelecionado = IDMedicao;

            return PartialView();
        }

        // GET: Observacao Medicao - Salvar
        public ActionResult ObservacaoMedicao_Salvar(int IDObservacao, int IDMedicao, int IDTag, bool ClienteVisualiza, string ObservacaoData, string ObservacaoTexto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // verifica se tem medicao
            if( IDMedicao > 0 )
            {
                // pega data
                DateTime dateValue = DateTime.Parse(ObservacaoData);

                ObservacaoMedicaoMetodos observacaoMetodos = new ObservacaoMedicaoMetodos();
                ObservacaoMedicaoDominio observacao = new ObservacaoMedicaoDominio();

                // preenche
                observacao.IDObservacao = IDObservacao;
                observacao.IDMedicao = IDMedicao;
                observacao.IDTag = IDTag;
                observacao.DataHora = dateValue;
                observacao.IDTipoAcesso = IDTipoAcesso;
                observacao.ClienteVisualiza = ClienteVisualiza;
                observacao.Observacao = ObservacaoTexto;

                // salva a observacao
                observacaoMetodos.Salvar(observacao);
            }

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // GET: Observacao Medicao - Excluir
        public ActionResult ObservacaoMedicao_Excluir(int IDObservacao)
        {
            // excluir
            ObservacaoMedicaoMetodos observacaoMetodos = new ObservacaoMedicaoMetodos();
            observacaoMetodos.Excluir(IDObservacao);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // GET: Observacao Medicao - Resolvido
        public ActionResult ObservacaoMedicao_Resolvido(int IDObservacao)
        {
            // excluir
            ObservacaoMedicaoMetodos observacaoMetodos = new ObservacaoMedicaoMetodos();
            observacaoMetodos.Resolvido(IDObservacao);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}