﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SupervisaoController
    {

        // GET: Medicao_EA_Formula
        public unsafe ActionResult Medicao_EA_Formula(int IDCliente, int IDMedicao)
        {
            // tela de ajuda - supervisao medicao atual
            CookieStore.SalvaCookie_String("PaginaAjuda", "Supervisao_Analogicas");

            // le supervisao da medicao
            var medicoesMetodos = new SupervMedicoesMetodos();
            var listaMedicoes = medicoesMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

            // salva cookie cliente
            CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);

            // le contrato cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cli = clienteMetodos.ListarPorId(IDCliente);
            CookieStore.SalvaCookie_Int("IDTipoContrato", cli.IDTipoContrato);

            // salva cookie medicao
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", listaMedicoes.IDTipoMedicao_MD);
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Supervisao");

            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // protege datas
            if (listaMedicoes.DataHoraAtualizacao == null)
            {
                listaMedicoes.DataHoraAtualizacao = "01/01/2000 00:00:00";
            }

            if (listaMedicoes.DataHoraEq == null)
            {
                listaMedicoes.DataHoraEq = "01/01/2000 00:00:00";
            }

            // dia atual
            DateTime datahora_ultima = DateTime.Parse(listaMedicoes.DataHoraAtualizacao);

            // funcao de supervisao
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            DATAHORA data_hora = new DATAHORA();
            EA_SUPERVMEDICAO superv = new EA_SUPERVMEDICAO();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = listaMedicoes.IDGW_GW;

            data_hora.data.dia = (char)datahora_ultima.Day;
            data_hora.data.mes = (char)datahora_ultima.Month;
            data_hora.data.ano = (short)datahora_ultima.Year;
            data_hora.hora.hora = (char)datahora_ultima.Hour;
            data_hora.hora.min = (char)datahora_ultima.Minute;
            data_hora.hora.seg = (char)datahora_ultima.Second;

            // caso for muito cedo, pego do dia anterior
            if (datahora_ultima.Hour < 7 && IDTipoAcesso != 4)
            {
                datahora_ultima = datahora_ultima.AddDays(-1);

                data_hora.data.dia = (char)datahora_ultima.Day;
                data_hora.data.mes = (char)datahora_ultima.Month;
                data_hora.data.ano = (short)datahora_ultima.Year;
                data_hora.hora.hora = (char)23;
                data_hora.hora.min = (char)45;
                data_hora.hora.seg = (char)0;
            }

            // calcula valores
            retorno = SmCalcDB_SupervMedicao_EA((char)0, ref config_interface, ref data_hora, ref superv);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Função [retorno {0}]", retorno));
                if (superv.flag_mes_atual > 0)
                    listaErros.Add(string.Format("Mês Atual [{0}]", Funcoes_SmartEnergy.ErroRelatorio((int)superv.flag_mes_atual)));
                if (superv.flag_anterior1 > 0)
                    listaErros.Add(string.Format("Mês Anterior 1 [{0}]", Funcoes_SmartEnergy.ErroRelatorio((int)superv.flag_anterior1)));
                if (superv.flag_anterior2 > 0)
                    listaErros.Add(string.Format("Mês Anterior 2 [{0}]", Funcoes_SmartEnergy.ErroRelatorio((int)superv.flag_anterior2)));
            }

            @ViewBag.listaErros = listaErros;

            // converte data e hora
            DateTime DataHora_Atual = Funcoes_Converte.ConverteDataHora2DateTime(superv.DataAtual);
            DateTime DataHora_Ultimo = Funcoes_Converte.ConverteDataHora2DateTime(superv.DataHora_Ultimo);
            DateTime Dia_Atual_Data = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dia_Atual.Data);
            DateTime Dia_Atual_MinDataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dia_Atual.Valor_MinDataHora);
            DateTime Dia_Atual_MaxDataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dia_Atual.Valor_MaxDataHora);
            DateTime Mes_Atual_Data = Funcoes_Converte.ConverteDataHora2DateTime(superv.Mes_Atual.Data);
            DateTime Mes_Atual_MinDataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Mes_Atual.Valor_MinDataHora);
            DateTime Mes_Atual_MaxDataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Mes_Atual.Valor_MaxDataHora);
            DateTime Mes_Anterior1_Data = Funcoes_Converte.ConverteDataHora2DateTime(superv.Mes_Anterior1.Data);
            DateTime Mes_Anterior1_MinDataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Mes_Anterior1.Valor_MinDataHora);
            DateTime Mes_Anterior1_MaxDataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Mes_Anterior1.Valor_MaxDataHora);
            DateTime Mes_Anterior2_Data = Funcoes_Converte.ConverteDataHora2DateTime(superv.Mes_Anterior2.Data);
            DateTime Mes_Anterior2_MinDataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Mes_Anterior2.Valor_MinDataHora);
            DateTime Mes_Anterior2_MaxDataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Mes_Anterior2.Valor_MaxDataHora);

            // datas
            ViewBag.DiaAtual = string.Format("{0:d}", DataHora_Atual);
            ViewBag.MesAtual = string.Format("{0:Y}", DataHora_Atual);
            ViewBag.MesAnterior = string.Format("{0:Y}", DataHora_Atual.AddMonths(-1));

            ViewBag.NomeGrandeza = listaMedicoes.NomeGrandeza;
            ViewBag.UnidadeGrandeza = listaMedicoes.UnidadeGrandeza;

            // graficos
            var Dias = new string[26];
            var Maxima = new double[26];
            var Media = new double[26];
            var Minima = new double[26];

            double Valor_min = 0.0;
            double Valor_max = 0.0;

            // valores
            DateTime strData = Funcoes_Converte.ConverteData2DateTime(superv.DataHora[0].data, (int)superv.DataHora[0].hora.hora, (int)superv.DataHora[0].hora.min);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proxima hora
                strData = strData.AddHours(1);

                // verifica se primeira barra
                if (i == 0)
                {
                    // copia do primeiro real
                    Media[0] = superv.Media[0];
                    Minima[0] = superv.Minima[0];
                    Maxima[0] = superv.Maxima[0];

                    // minimo e maximo
                    Valor_min = Minima[0];
                    Valor_max = Maxima[0];
                }

                // verifica se ultima barra
                if (i == 25)
                {
                    // copia do ultimo real
                    Media[25] = superv.Media[23];
                    Minima[25] = superv.Minima[23];
                    Maxima[25] = superv.Maxima[23];
                }

                // restante
                if (i > 0 && i < 25)
                {
                    // copia
                    j = i - 1;

                    Maxima[i] = superv.Maxima[j];
                    Media[i] = superv.Media[j];
                    Minima[i] = superv.Minima[j];

                    // verifica minimo
                    if (Maxima[i] < Valor_min)
                        Valor_min = Maxima[i];

                    if (Media[i] < Valor_min)
                        Valor_min = Media[i];

                    if (Minima[i] < Valor_min)
                        Valor_min = Minima[i];

                    // verifica maximo
                    if (Minima[i] > Valor_max)
                        Valor_max = Minima[i];

                    if (Media[i] > Valor_max)
                        Valor_max = Media[i];

                    if (Maxima[i] > Valor_max)
                        Valor_max = Maxima[i];
                }
            }

            // valor máximo (5% e arredonda para cima)
            Valor_max = Valor_max + (Math.Abs(Valor_max) * 0.05);
            Valor_max = Math.Ceiling(Valor_max);

            // valor mínimo (5% e arredonda para baixo)
            Valor_min = Valor_min - (Math.Abs(Valor_min) * 0.05);
            Valor_min = Math.Floor(Valor_min);

            // copia valores
            ViewBag.ValorMax = Valor_max;
            ViewBag.ValorMin = Valor_min;

            ViewBag.Media = Media;
            ViewBag.Minima = Minima;
            ViewBag.Maxima = Maxima;
            ViewBag.Dias = Dias;

            // dia atual
            ViewBag.Dia_Atual_Data = string.Format("{0:M}", Dia_Atual_Data);
            ViewBag.Dia_Atual_Media = string.Format("{0:#,##0.00}", superv.Dia_Atual.Valor_Media);
            ViewBag.Dia_Atual_Min = string.Format("{0:#,##0.00}", superv.Dia_Atual.Valor_Min);
            ViewBag.Dia_Atual_MinDataHora = string.Format("{0:t}", Dia_Atual_MinDataHora);
            ViewBag.Dia_Atual_Max = string.Format("{0:#,##0.00}", superv.Dia_Atual.Valor_Max);
            ViewBag.Dia_Atual_MaxDataHora = string.Format("{0:t}", Dia_Atual_MaxDataHora);

            // mes atual
            ViewBag.Mes_Atual_Data = string.Format("{0:Y}", Mes_Atual_Data);
            ViewBag.Mes_Atual_DataMes = string.Format("{0:MMMM}", Mes_Atual_Data);
            ViewBag.Mes_Atual_Media = string.Format("{0:#,##0.00}", superv.Mes_Atual.Valor_Media);
            ViewBag.Mes_Atual_Min = string.Format("{0:#,##0.00}", superv.Mes_Atual.Valor_Min);
            ViewBag.Mes_Atual_MinDataHora = string.Format("{0:d} {1:HH:mm}", Mes_Atual_MinDataHora, Mes_Atual_MinDataHora);
            ViewBag.Mes_Atual_Max = string.Format("{0:#,##0.00}", superv.Mes_Atual.Valor_Max);
            ViewBag.Mes_Atual_MaxDataHora = string.Format("{0:d} {1:HH:mm}", Mes_Atual_MaxDataHora, Mes_Atual_MaxDataHora);

            ViewBag.Mes_Atual_MinN = superv.Mes_Atual.Valor_Min;
            ViewBag.Mes_Atual_MediaN = superv.Mes_Atual.Valor_Media;
            ViewBag.Mes_Atual_MaxN = superv.Mes_Atual.Valor_Max;

            // mes anterior 1
            ViewBag.Mes_Anterior1_Data = string.Format("{0:Y}", Mes_Anterior1_Data);
            ViewBag.Mes_Anterior1_DataMes = string.Format("{0:MMMM}", Mes_Anterior1_Data);
            ViewBag.Mes_Anterior1_Media = string.Format("{0:#,##0.00}", superv.Mes_Anterior1.Valor_Media);
            ViewBag.Mes_Anterior1_Min = string.Format("{0:#,##0.00}", superv.Mes_Anterior1.Valor_Min);
            ViewBag.Mes_Anterior1_MinDataHora = string.Format("{0:d} {1:HH:mm}", Mes_Anterior1_MinDataHora, Mes_Anterior1_MinDataHora);
            ViewBag.Mes_Anterior1_Max = string.Format("{0:#,##0.00}", superv.Mes_Anterior1.Valor_Max);
            ViewBag.Mes_Anterior1_MaxDataHora = string.Format("{0:d} {1:HH:mm}", Mes_Anterior1_MaxDataHora, Mes_Anterior1_MaxDataHora);

            ViewBag.Mes_Anterior1_MinN = superv.Mes_Anterior1.Valor_Min;
            ViewBag.Mes_Anterior1_MediaN = superv.Mes_Anterior1.Valor_Media;
            ViewBag.Mes_Anterior1_MaxN = superv.Mes_Anterior1.Valor_Max;

            // mes anterior 2
            ViewBag.Mes_Anterior2_Data = string.Format("{0:Y}", Mes_Anterior2_Data);
            ViewBag.Mes_Anterior2_DataMes = string.Format("{0:MMMM}", Mes_Anterior2_Data);
            ViewBag.Mes_Anterior2_Media = string.Format("{0:#,##0.00}", superv.Mes_Anterior2.Valor_Media);
            ViewBag.Mes_Anterior2_Min = string.Format("{0:#,##0.00}", superv.Mes_Anterior2.Valor_Min);
            ViewBag.Mes_Anterior2_MinDataHora = string.Format("{0:d} {1:HH:mm}", Mes_Anterior2_MinDataHora, Mes_Anterior2_MinDataHora);
            ViewBag.Mes_Anterior2_Max = string.Format("{0:#,##0.00}", superv.Mes_Anterior2.Valor_Max);
            ViewBag.Mes_Anterior2_MaxDataHora = string.Format("{0:d} {1:HH:mm}", Mes_Anterior2_MaxDataHora, Mes_Anterior2_MaxDataHora);

            ViewBag.Mes_Anterior2_MinN = superv.Mes_Anterior2.Valor_Min;
            ViewBag.Mes_Anterior2_MediaN = superv.Mes_Anterior2.Valor_Media;
            ViewBag.Mes_Anterior2_MaxN = superv.Mes_Anterior2.Valor_Max;

            // formula
            ViewBag.Formula = listaMedicoes.Formula;
            ViewBag.GatewayAtualizacao = string.Format("{0:d} {1:HH:mm}", DataHora_Ultimo, DataHora_Ultimo);

            return View();
        }
    }
}