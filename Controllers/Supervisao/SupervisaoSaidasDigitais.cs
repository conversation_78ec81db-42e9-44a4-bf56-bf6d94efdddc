﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SupervisaoController
    {
        
        // GET: Supervisão Saídas Digitais
        public ActionResult SaidasDigitais(int IDGateway, int IDMedicao = 0)
        {
            // caso tiver IDMedicao, utilizo ela para encontrar o IDGateway
            if (IDMedicao > 0)
            {
                // le gateway associada na medicao
                MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);

                // utilizo o IDGateway da medição
                IDGateway = medicao.IDGateway;
            }

            // tela de ajuda - supervisao de saidas digitais
            CookieStore.SalvaCookie_String("PaginaAjuda", "Supervisao_SaidasDigitais");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Supervisao");

            // le cookies
            LeCookies_SmartEnergy();

            // leio gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            //
            // IoT
            //

            // API Artemis Broker - verifica se gateway conectada no IoT
            ArtemisBroker artemis = new ArtemisBroker();
            gateway.GatewayConectada_IoT = artemis.Gateway_Conectada(gateway.IDGateway);
            ViewBag.GatewayConectada_IoT = gateway.GatewayConectada_IoT;

            // saida digitais
            List<SaidasDigitaisDominio> listaSaidas = new List<SaidasDigitaisDominio>();

            // preenche saidas digitais
            Converte_SupervSD(IDGateway, ref listaSaidas);

            // valores
            ViewBag.GatewayID = IDGateway;
            ViewBag.GatewayNome = gateway.Nome;
            ViewBag.GatewayConectada_IoT = gateway.GatewayConectada_IoT;
            ViewBag.listaSaidas = listaSaidas;

            return View();
        }


        //
        // SOLICITA ESTADO DAS SAÍDAS DIGITAIS
        //

        public PartialViewResult _SaidasDigitais_Resultado(int IDGateway)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // saida digitais
            List<SaidasDigitaisDominio> listaSaidas = new List<SaidasDigitaisDominio>();

            // preenche saidas digitais com banco de dados
            Converte_SupervSD(IDGateway, ref listaSaidas);

            // leio gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            if (gateway != null)
            {
                //
                // IoT
                //

                // API Artemis Broker - verifica se gateway conectada no IoT
                ArtemisBroker artemis = new ArtemisBroker();
                gateway.GatewayConectada_IoT = artemis.Gateway_Conectada(gateway.IDGateway);
                ViewBag.GatewayConectada_IoT = gateway.GatewayConectada_IoT;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // solicita programação das saídas
                    smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_SD, SMCOM_TIPO_SOL.SOLICITA);

                    // verifica se existe backup da configuração
                    GateX_SD_Metodos sdMetodos = new GateX_SD_Metodos();
                    List<GateX_SD_Dominio> prgs = sdMetodos.ListarProgramadosPorIDGateway(IDGateway);

                    if (prgs.Count == 0)
                    {
                        // atualiza backup
                        sdMetodos.InserirLista(IDGateway, smartCom.gateX.SD, STATUS_CFG.ATUAL);
                    }

                    // solicita supervisão das saídas digitais
                    smartCom.Solicitar(SMCOM_FUNCOES_SOL.SUPERV_SD, SMCOM_TIPO_SOL.SOLICITA);

                    // copia resultado
                    listaSaidas = smartCom.gateX.SD_supervisao;
                }
            }

            // resultado
            ViewBag.listaSaidas = listaSaidas;

            return PartialView();
        }

        //
        // COMANDO NA SAÍDA DIGITAL
        //

        // GET: Solicita comando na saída digital
        public JsonResult SaidasDigitais_SolicitaComando(int IDGateway, int NumSaidaGateway, int Comando)
        {
            // le cookies
            LeCookies_SmartEnergy();

            //
            // Comando na saída digital do SmartGate X
            //

            // função de solicitação
            int funcaoSolicitacao = 0;

            switch (Comando)
            {
                case COMANDO_SD.AUTOMATICO:     // comando automático
                    funcaoSolicitacao = SMCOM_FUNCOES_SOL.COMANDO_AUTOMATICO;
                    break;

                case COMANDO_SD.MANUAL_DESLIGA: // comando manual desliga
                    funcaoSolicitacao = SMCOM_FUNCOES_SOL.COMANDO_MANUAL_DESLIGA;
                    break;

                case COMANDO_SD.MANUAL_LIGA:    // comando manual liga
                    funcaoSolicitacao = SMCOM_FUNCOES_SOL.COMANDO_MANUAL_LIGA;
                    break;
            }

            // leio gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            if (gateway != null)
            {
                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // número da saída digital desejada
                    smartCom.gateX.NumSaidaGateway = NumSaidaGateway;

                    // comando desejado
                    smartCom.gateX.ComandoSaidaGateway = Comando;

                    // comando na saída digital
                    smartCom.Solicitar(funcaoSolicitacao, SMCOM_TIPO_SOL.ENVIA);

                    // resultado
                    return Json(smartCom.gateX.estado_comando, JsonRequestBehavior.AllowGet);
                }
            }

            // resultado erro
            return Json(0, JsonRequestBehavior.AllowGet);
        }


        //
        // FUNÇÕES AUXILIARES
        //

        private void Converte_SupervSD(int IDGateway, ref List<SaidasDigitaisDominio> listaSaidas)
        {
            // le saida banco de dados
            GateX_SD_Metodos sdMetodos = new GateX_SD_Metodos();
            List<GateX_SD_Dominio> saidas = sdMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.ATUAL);

            // limpa lista
            listaSaidas.Clear();

            // atualiza
            foreach (GateX_SD_Dominio sd in saidas)
            {
                // verifica se programado
                if (sd.Programado)
                {
                    SaidasDigitaisDominio saida = new SaidasDigitaisDominio();

                    saida.IDCliente = 0;
                    saida.IDGateway = sd.IDGateway;
                    saida.Descricao = sd.Descricao;
                    saida.NumSaidaGateway = sd.NumSaidaGateway;

                    saida.Programado = true;
                    saida.status = STATUS_SD.DESCONHECIDO;
                    saida.cAss = 0;
                    saida.rAss = 0;

                    listaSaidas.Add(saida);
                }
            }

            return;
        }

        private void AlteraEstadoSaidas(ref List<SaidasDigitaisDominio> saidas, int novo_estado)
        {
            // percorre as saidas e seta novo estado
            if (saidas != null)
            {
                foreach (SaidasDigitaisDominio sd in saidas)
                {
                    // novo estado
                    sd.status = novo_estado;
                }
            }

            return;
        }
    }
}