﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SupervisaoController
    {
        // supervisao PDA
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_PDA_SupervMetaDiario", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_PDA_SupervMetaDiario(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref DATAHORA pdata_equipo, ref PDA_SUPMETA_DIARIO pmeta_diario);

        // Relatorio Diario
        // 0 -> Demanda Ativa e Fator de Potencia
        // 1 -> Demanda Reativa
        // 2 -> Consumo Ativo e Consumo Reativo
        // 3 -> <PERSON>or de Potencia (horario)
        // 4 -> <PERSON><PERSON> (Demanda Media / Demanda Maxima)
        // 5 -> Demanda nao utilizada
        // 6 -> Consumo Ativo segundo mercado livre
        // 7 -> Fator de Utilizacao (Demanda Media / Contrato)
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_RelatDiario", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_RelatDiario(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, char simula, ref DATAHORA pdata_equipo, ref RELAT_DIARIO prelatorio, ref RELAT_DIARIO_ANALISE panalise, ref RELAT_DIARIO prelatorio_sim, ref RELAT_DIARIO_ANALISE panalise_sim);

        // Relatorio Mensal
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_RelatMensal", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_RelatMensal(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, char simula, ref DATAHORA pdata_equipo, ref RELAT_MENSAL prelatorio, ref RELAT_MENSAL_ANALISE panalise, ref RELAT_MENSAL prelatorio_sim, ref RELAT_MENSAL_ANALISE panalise_sim);


        // GET: Medicao_Energia PDA
        public unsafe ActionResult Medicao_Energia_PDA(int IDCliente, int IDMedicao)
        {
            // tela de ajuda - supervisao medicao atual
            CookieStore.SalvaCookie_String("PaginaAjuda", "Supervisao_Energia_MetaConsumo");

            // le supervisao da medicao
            SupervMedicoesMetodos medicoesMetodos = new SupervMedicoesMetodos();
            SupervMedicoesDominio listaMedicoes = medicoesMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

            // le registro mais recente 
            EN_Metodos enMetodos = new EN_Metodos();
            EN_Dominio mais_recente = enMetodos.ListarMaisRecente(IDCliente, IDMedicao);

            // salva cookie cliente
            CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);

            // le contrato cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cli = clienteMetodos.ListarPorId(IDCliente);
            CookieStore.SalvaCookie_Int("IDTipoContrato", cli.IDTipoContrato);

            // salva cookie medicao
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", listaMedicoes.IDTipoMedicao_MD);
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Supervisao");
            CookieStore.SalvaCookie_Int("_IDGateway", listaMedicoes.IDGW_GW);
            CookieStore.SalvaCookie_Int("IDTipoGateway", listaMedicoes.IDTipoGateway_GW);

            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // protege datas
            if( listaMedicoes.DataHoraAtualizacao == null )
            {
                listaMedicoes.DataHoraAtualizacao = "01/01/2000 00:00:00";
            }

            if (listaMedicoes.DataHoraEq == null)
            {
                listaMedicoes.DataHoraEq = "01/01/2000 00:00:00";
            }

            // dia atual
            DateTime datahora_ultima = DateTime.Parse(listaMedicoes.DataHoraAtualizacao);

            if (mais_recente != null)
            {
                // caso leu ultimo registro EN, uso ele como última data em vez da data de atualização
                datahora_ultima = mais_recente.DataHora;
            }

            // funcao de supervisao
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            DATAHORA data_hora = new DATAHORA();
            PDA_SUPMETA_DIARIO supervmeta = new PDA_SUPMETA_DIARIO();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = listaMedicoes.IDGW_GW;

            data_hora.data.dia = (char)datahora_ultima.Day;
            data_hora.data.mes = (char)datahora_ultima.Month;
            data_hora.data.ano = (short)datahora_ultima.Year;
            data_hora.hora.hora = (char)datahora_ultima.Hour;
            data_hora.hora.min = (char)datahora_ultima.Minute;
            data_hora.hora.seg = (char)datahora_ultima.Second;

            // caso for muito cedo e se nao for producao, pego do dia anterior
            if (datahora_ultima.Hour < 7 && IDTipoAcesso != 4)
            {
                datahora_ultima = datahora_ultima.AddDays(-1);

                data_hora.data.dia = (char)datahora_ultima.Day;
                data_hora.data.mes = (char)datahora_ultima.Month;
                data_hora.data.ano = (short)datahora_ultima.Year;
                data_hora.hora.hora = (char)23;
                data_hora.hora.min = (char)45;
                data_hora.hora.seg = (char)0;
            }

            // atualizo fechamentos
            CONFIG_INTERFACE config_interface_fechamento = new CONFIG_INTERFACE();
            config_interface_fechamento.sweb.id_cliente = IDCliente;
            config_interface_fechamento.sweb.id_medicao = IDMedicao;
            config_interface_fechamento.sweb.id_gateway = listaMedicoes.IDGW_GW;

            // verifica se deve utilizar outra gateway
            if (listaMedicoes.IDGateway_Fechamentos > 0)
            {
                config_interface_fechamento.sweb.id_gateway = listaMedicoes.IDGateway_Fechamentos;
            }

            retorno = SmCalcDB_AtualizaFechamentos((char)0, ref config_interface_fechamento);

            //
            // SUPERVISAO
            //

            // calcula valores
            retorno = SmCalcDB_PDA_SupervMetaDiario((char)0, ref config_interface, ref data_hora, ref supervmeta);

            // lista de erros
            var listaErros = new List<string>();

            if( isUser.isGESTAL(ViewBag._IDTipoAcesso) )
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("SmCalcDB_PDA_SupervMetaDiario [retorno {0}]", retorno));
            }

            // data atual
            ViewBag.Cons_Dia_P = string.Format("{0:#,##0}", supervmeta.ConsP_DataAtual);
            ViewBag.Meta_Dia_P = string.Format("{0:#,##0}", supervmeta.MetaP_DataAtual);
            ViewBag.Porc_Dia_P = string.Format("{0:0.0}", supervmeta.PorcP_DataAtual);

            ViewBag.Cons_Dia_FP = string.Format("{0:#,##0}", supervmeta.ConsFP_DataAtual);
            ViewBag.Meta_Dia_FP = string.Format("{0:#,##0}", supervmeta.MetaFP_DataAtual);
            ViewBag.Porc_Dia_FP = string.Format("{0:0.0}", supervmeta.PorcFP_DataAtual);

            ViewBag.Cons_Dia_T = string.Format("{0:#,##0}", supervmeta.ConsT_DataAtual);
            ViewBag.Meta_Dia_T = string.Format("{0:#,##0}", supervmeta.MetaT_DataAtual);
            ViewBag.Porc_Dia_T = string.Format("{0:0.0}", supervmeta.PorcT_DataAtual);

            // mes atual
            ViewBag.Cons_Mes_P = string.Format("{0:#,##0}", supervmeta.ConsP_Mes);
            ViewBag.Meta_Mes_P = string.Format("{0:#,##0}", supervmeta.MetaP_Mes);
            ViewBag.Porc_Mes_P = string.Format("{0:0.0}", supervmeta.PorcP_Mes);

            ViewBag.Cons_Mes_FP = string.Format("{0:#,##0}", supervmeta.ConsFP_Mes);
            ViewBag.Meta_Mes_FP = string.Format("{0:#,##0}", supervmeta.MetaFP_Mes);
            ViewBag.Porc_Mes_FP = string.Format("{0:0.0}", supervmeta.PorcFP_Mes);

            ViewBag.Cons_Mes_T = string.Format("{0:#,##0}", supervmeta.ConsT_Mes);
            ViewBag.Meta_Mes_T = string.Format("{0:#,##0}", supervmeta.MetaT_Mes);
            ViewBag.Porc_Mes_T = string.Format("{0:0.0}", supervmeta.PorcT_Mes);

            // projetado
            ViewBag.Cons_Proj_P = string.Format("{0:#,##0}", supervmeta.ProjP_Mes);
            ViewBag.Porc_Proj_P = string.Format("{0:0.0}", supervmeta.PorcProjP_Mes);

            ViewBag.Cons_Proj_FP = string.Format("{0:#,##0}", supervmeta.ProjFP_Mes);
            ViewBag.Porc_Proj_FP = string.Format("{0:0.0}", supervmeta.PorcProjFP_Mes);

            ViewBag.Cons_Proj_T = string.Format("{0:#,##0}", supervmeta.ProjT_Mes);
            ViewBag.Porc_Proj_T = string.Format("{0:0.0}", supervmeta.PorcProjT_Mes);

            // notificacoes

            double PorcT_Mes_Ultrapassagem = (supervmeta.PorcT_Mes > 100.0) ? (supervmeta.PorcT_Mes-100.0) : 0.0;
            ViewBag.PorcT_Mes_Ultrapassagem = PorcT_Mes_Ultrapassagem;

            bool meta_normal = true;
            string texto1 = "";
            string texto2 = "";

            if (supervmeta.PorcProjT_Mes > 100.0)
            {
                meta_normal = false;
                texto1 = string.Format("O consumo Projetado está maior que a Meta de Consumo em {0:0.0}%", supervmeta.PorcProjT_Mes - 100.0);
                texto2 = "Favor tomar as ações necessárias para que o consumo fique abaixo da meta.";
            }
            else
            {
                if (supervmeta.PorcT_Mes > 100.0)
                {
                    meta_normal = false;
                    texto1 = string.Format("O consumo Acumulado está maior que a Meta de Consumo em {0:0.0}%", supervmeta.PorcT_Mes - 100.0);
                    texto2 = "Rever a operação da unidade para não agravar o consumo e no próximo mês fique dentro da meta estipulada.";
                }
                else
                {
                    if( supervmeta.PorcT_DataAtual > 100.0 )
                    {
                        meta_normal = false;
                        texto1 = string.Format("O consumo Acumulado do dia foi maior que a Meta de Consumo em {0:0.0}%", supervmeta.PorcT_DataAtual - 100.0);
                        texto2 = "Favor verificar o consumo da unidade, pois a projeção de consumo irá ultrapassar a meta.";
                    }
                }
            }

            if( supervmeta.MetaT_DataAtual == 0.0 && supervmeta.MetaT_Mes == 0.0 )
            {
                meta_normal = false;
                texto1 = "O valor da Meta de Consumo esta em zero.";
                texto2 = "Favor verificar o cadastro das metas da medição para o período.";
            }

            if( meta_normal )
            {
                texto1 = "Os consumos Acumulado e Projetado estão menores que a Meta de Consumo.";
                texto2 = "Continuar abaixo da meta para representar uma economia no fechamento do mês.";
            }

            ViewBag.Texto1 = texto1;
            ViewBag.Texto2 = texto2;
            ViewBag.MetaNormal = meta_normal;

            //
            // DEMANDA
            //

            ENERGIA_SUPERVMEDICAO superv = new ENERGIA_SUPERVMEDICAO();

            // calcula valores
            retorno = SmCalcDB_SupervMedicao_Energia((char)0, ref config_interface, ref data_hora, ref superv);

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Superv [retorno {0}]", retorno));
                if (superv.flag_dia_atual > 0)
                    listaErros.Add(string.Format("Dia Atual [{0}]", Funcoes_SmartEnergy.ErroRelatorio((int)superv.flag_dia_atual)));
                if (superv.flag_mes_atual > 0)
                    listaErros.Add(string.Format("Mês Atual [{0}]", Funcoes_SmartEnergy.ErroRelatorio((int)superv.flag_mes_atual)));
                if (superv.flag_mes_proj > 0)
                    listaErros.Add(string.Format("Mês Projetado [{0}]", Funcoes_SmartEnergy.ErroRelatorio((int)superv.flag_mes_proj)));
                if (superv.flag_anterior1 > 0)
                    listaErros.Add(string.Format("Mês Anterior 1 [{0}]", Funcoes_SmartEnergy.ErroRelatorio((int)superv.flag_anterior1)));
                if (superv.flag_anterior2 > 0)
                    listaErros.Add(string.Format("Mês Anterior 2 [{0}]", Funcoes_SmartEnergy.ErroRelatorio((int)superv.flag_anterior2)));
            }

            // calcula tolerancia
            double Tol_ContratoP = superv.Dem_ContratoP * (1.0 + (superv.Tol_ContratoP / 100.0));
            double Tol_ContratoFP = superv.Dem_ContratoFP * (1.0 + (superv.Tol_ContratoFP / 100.0));

            // converte data e hora
            DateTime DataHora_Atual = Funcoes_Converte.ConverteDataHora2DateTime(superv.DataAtual);

            DateTime Dem_Dia_DemMaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dem_Dia.DemMaxP_DataHora);
            DateTime Dem_Dia_DemMaxFP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dem_Dia.DemMaxFP_DataHora);
            DateTime Dem_Mes_DemMaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dem_Mes.DemMaxP_DataHora);
            DateTime Dem_Mes_DemMaxFP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dem_Mes.DemMaxFP_DataHora);
            DateTime Dem_MesAnt_DemMaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dem_MesAnt.DemMaxP_DataHora);
            DateTime Dem_MesAnt_DemMaxFP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dem_MesAnt.DemMaxFP_DataHora);

            // datas
            ViewBag.DiaAtual = string.Format("{0:d}", DataHora_Atual);
            ViewBag.MesAtual = string.Format("{0:Y}", DataHora_Atual);
            ViewBag.MesAnterior1 = string.Format("{0:Y}", DataHora_Atual.AddMonths(-1));

            // grafico de demanda
            var Dias_Demanda = new string[98];
            var Demanda = new double[98];
            var Periodo = new double[98];
            var Contrato = new double[98];
            var Tolerancia = new double[98];

            double Dem_max = 0.0;

            // valores
            DateTime strData = Funcoes_Converte.ConverteDataHora2DateTime(superv.Demanda_DataHora[0]);

            // volta 15min para a primeira barra
            strData = strData.AddMinutes(-15);

            int i = 0;
            int j = 0;

            for (i = 0; i < 98; i++)
            {
                // formata label
                Dias_Demanda[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proximo quinze minutos
                strData = strData.AddMinutes(15);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    Demanda[i] = superv.Demanda[0];
                    Periodo[i] = superv.Periodo[0];
                    Contrato[i] = superv.Contrato[0];
                    Tolerancia[i] = superv.Contrato[0] * (1.0 + (superv.Tol_ContratoFP / 100.0));
                }

                if (i == 97)
                {
                    // zera
                    Demanda[i] = superv.Demanda[95];
                    Periodo[i] = superv.Periodo[95];
                    Contrato[i] = superv.Contrato[95];
                    Tolerancia[i] = superv.Contrato[95] * (1.0 + (superv.Tol_ContratoFP / 100.0));
                }

                if (i >= 1 && i <= 96)
                {
                    // copia
                    j = i - 1;

                    Demanda[i] = superv.Demanda[j];
                    Periodo[i] = superv.Periodo[j];
                    Contrato[i] = superv.Contrato[j];

                    if (Periodo[i] == 0)
                    {
                        Tolerancia[i] = superv.Contrato[j] * (1.0 + (superv.Tol_ContratoP / 100.0));
                    }
                    else
                    {
                        Tolerancia[i] = superv.Contrato[j] * (1.0 + (superv.Tol_ContratoFP / 100.0));
                    }

                    // verifica se sem registro
                    if (Periodo[i] == 3)
                    {
                        Demanda[i] = 0.0;
                    }

                    // verifica demanda maxima
                    if (Demanda[i] > Dem_max)
                        Dem_max = Demanda[i];

                    if (Contrato[i] > Dem_max)
                        Dem_max = Contrato[i];

                    if (Tolerancia[i] > Dem_max)
                        Dem_max = Tolerancia[i];
                }
            }

            Dem_max = Dem_max * 1.1;

            if (Dem_max < 1.0)
            {
                Dem_max = 1.0;
            }

            ViewBag.DemMax = Dem_max;

            ViewBag.Demanda = Demanda;
            ViewBag.Periodo = Periodo;
            ViewBag.Contrato = Contrato;
            ViewBag.Tolerancia = Tolerancia;
            ViewBag.Dias_Demanda = Dias_Demanda;

            // demanda
            ViewBag.Dem_Dia_DemMaxP = string.Format("{0:#,##0.0}", superv.Dem_Dia.DemMaxP);
            if (Dem_Dia_DemMaxP_DataHora.Year != 2000)
                ViewBag.Dem_Dia_DemMaxP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_Dia_DemMaxP_DataHora, Dem_Dia_DemMaxP_DataHora);
            else
                ViewBag.Dem_Dia_DemMaxP_DataHora = "--/--/---- --:--";

            ViewBag.Dem_Dia_DemMaxFP = string.Format("{0:#,##0.0}", superv.Dem_Dia.DemMaxFP);
            if (Dem_Dia_DemMaxFP_DataHora.Year != 2000)
                ViewBag.Dem_Dia_DemMaxFP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_Dia_DemMaxFP_DataHora, Dem_Dia_DemMaxFP_DataHora);
            else
                ViewBag.Dem_Dia_DemMaxFP_DataHora = "--/--/---- --:--";

            ViewBag.Dem_Mes_DemMaxP = string.Format("{0:#,##0.0}", superv.Dem_Mes.DemMaxP);
            if (Dem_Mes_DemMaxP_DataHora.Year != 2000)
                ViewBag.Dem_Mes_DemMaxP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_Mes_DemMaxP_DataHora, Dem_Mes_DemMaxP_DataHora);
            else
                ViewBag.Dem_Mes_DemMaxP_DataHora = "--/--/---- --:--";

            ViewBag.Dem_Mes_DemMaxFP = string.Format("{0:#,##0.0}", superv.Dem_Mes.DemMaxFP);
            if (Dem_Mes_DemMaxFP_DataHora.Year != 2000)
                ViewBag.Dem_Mes_DemMaxFP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_Mes_DemMaxFP_DataHora, Dem_Mes_DemMaxFP_DataHora);
            else
                ViewBag.Dem_Mes_DemMaxFP_DataHora = "--/--/---- --:--";

            ViewBag.Dem_MesAnt_DemMaxP = string.Format("{0:#,##0.0}", superv.Dem_MesAnt.DemMaxP);
            if (Dem_MesAnt_DemMaxP_DataHora.Year != 2000)
                ViewBag.Dem_MesAnt_DemMaxP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MesAnt_DemMaxP_DataHora, Dem_MesAnt_DemMaxP_DataHora);
            else
                ViewBag.Dem_MesAnt_DemMaxP_DataHora = "--/--/---- --:--";

            ViewBag.Dem_MesAnt_DemMaxFP = string.Format("{0:#,##0.0}", superv.Dem_MesAnt.DemMaxFP);
            if (Dem_MesAnt_DemMaxFP_DataHora.Year != 2000)
                ViewBag.Dem_MesAnt_DemMaxFP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MesAnt_DemMaxFP_DataHora, Dem_MesAnt_DemMaxFP_DataHora);
            else
                ViewBag.Dem_MesAnt_DemMaxFP_DataHora = "--/--/---- --:--";

            ViewBag.Dem_ContratoP = superv.Dem_ContratoP;
            ViewBag.Dem_ContratoFP = superv.Dem_ContratoFP;

            ViewBag.Tol_ContratoP = superv.Tol_ContratoP;
            ViewBag.Tol_ContratoFP = superv.Tol_ContratoFP;

            // verifico se houve ultrapassagem
            double A = superv.Dem_Mes.DemMaxP;
            double B = Tol_ContratoP;
            double Dem_Mes_UltrapassagemP = (A > B) ? superv.Dem_Mes.DemMaxP : 0.0;

            ViewBag.Dem_Mes_UltrapassagemP = Dem_Mes_UltrapassagemP;

            A = superv.Dem_Mes.DemMaxFP;
            B = Tol_ContratoFP;
            double Dem_Mes_UltrapassagemFP = (A > B) ? superv.Dem_Mes.DemMaxFP : 0.0;

            ViewBag.Dem_Mes_UltrapassagemFP = Dem_Mes_UltrapassagemFP;

            //
            // CONSUMO
            //

            RELAT_MENSAL relatorio = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise = new RELAT_MENSAL_ANALISE();

            RELAT_MENSAL relatorio_sim = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise_sim = new RELAT_MENSAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int TipoSimula = 0;

            // calcula valores
            retorno = SmCalcDB_Energia_RelatMensal((char)0, ref config_interface, (char)2, (char)TipoSimula, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);

            // numero de dias do mes
            int NumDiasMes = relatorio.num_dias;

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[NumDiasMes - 1].datahora.data.ano,
                                   (int)relatorio.registro[NumDiasMes - 1].datahora.data.mes,
                                   1, 1, 0, 0);

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Consumo [retorno {0}]", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // maior consumo
            string unidade_consumo = "kWh";
            double divisao_consumo = 1.0;

            ViewBag.UnidadeConsumo = unidade_consumo;

            // grafico
            var ConsumoP = new double[42];
            var ConsumoFPI = new double[42];
            var ConsumoFPC = new double[42];
            var Meta = new double[42];
            var Datas = new string[42];
            var DatasN = new DateTime[42];
            var Dias = new string[42];

            double Consumo_max_grafico = 0.0;

            // valores
            strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            i = 0;
            j = 0;

            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = strData.ToString("dd/MM");

                // proximo dias
                strData = strData.AddDays(1);

                // meta
                Meta[i] = supervmeta.MetaT_DataAtual / divisao_consumo;

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    ConsumoP[i] = relatorio.registro[0].valor[0] / divisao_consumo;
                    ConsumoFPI[i] = relatorio.registro[0].valor[1] / divisao_consumo;
                    ConsumoFPC[i] = relatorio.registro[0].valor[2] / divisao_consumo;
                }

                if (i == (NumDiasMes + 1))
                {
                    // zera
                    ConsumoP[i] = relatorio.registro[NumDiasMes - 1].valor[0] / divisao_consumo;
                    ConsumoFPI[i] = relatorio.registro[NumDiasMes - 1].valor[1] / divisao_consumo;
                    ConsumoFPC[i] = relatorio.registro[NumDiasMes - 1].valor[2] / divisao_consumo;
                }

                if (i >= 1 && i <= NumDiasMes)
                {
                    // copia
                    j = i - 1;

                    ConsumoP[i] = relatorio.registro[j].valor[0] / divisao_consumo;
                    ConsumoFPI[i] = relatorio.registro[j].valor[1] / divisao_consumo;
                    ConsumoFPC[i] = relatorio.registro[j].valor[2] / divisao_consumo;

                    // verifica consumo maximo
                    if ((ConsumoP[i] + ConsumoFPI[i] + ConsumoFPC[i]) > Consumo_max_grafico)
                        Consumo_max_grafico = (ConsumoP[i] + ConsumoFPI[i] + ConsumoFPC[i]);

                    if (Meta[i] > Consumo_max_grafico)
                        Consumo_max_grafico = Meta[i];
                }
            }

            Consumo_max_grafico = Consumo_max_grafico * 1.1;

            if (Consumo_max_grafico == 0.0)
            {
                Consumo_max_grafico = 10.0;
            }

            ViewBag.ConsMaxGrafico = Consumo_max_grafico;

            ViewBag.ConsumoP = ConsumoP;
            ViewBag.ConsumoFPI = ConsumoFPI;
            ViewBag.ConsumoFPC = ConsumoFPC;
            ViewBag.Meta = Meta;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            // nome do relatorio
            ViewBag.NomeRelat = string.Format("Consumo");
            ViewBag.PeriodoRelat = string.Format("Mensal");

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:y}", DataAtual);

            ViewBag.NumDiasMes = NumDiasMes;

            // consumo
            double ConsP = analise.analise_valor.consumo[0] / divisao_consumo;
            double ConsFPI = analise.analise_valor.consumo[1] / divisao_consumo;
            double ConsFPC = analise.analise_valor.consumo[2] / divisao_consumo;
            double ConsTotal = (ConsP + ConsFPI + ConsFPC);

            if (ConsP < 100.0)
            {
                ViewBag.ConsP = string.Format("{0:#,##0.0}", ConsP);
            }
            else
            {
                ViewBag.ConsP = string.Format("{0:#,##0}", ConsP);
            }
            ViewBag.ConsPN = ConsP;

            if (ConsFPI < 100.0)
            {
                ViewBag.ConsFPI = string.Format("{0:#,##0.0}", ConsFPI);
            }
            else
            {
                ViewBag.ConsFPI = string.Format("{0:#,##0}", ConsFPI);
            }
            ViewBag.ConsFPIN = ConsFPI;

            if (ConsFPC < 100.0)
            {
                ViewBag.ConsFPC = string.Format("{0:#,##0.0}", ConsFPC);
            }
            else
            {
                ViewBag.ConsFPC = string.Format("{0:#,##0}", ConsFPC);
            }
            ViewBag.ConsFPCN = ConsFPC;

            if (ConsTotal < 100.0)
            {
                ViewBag.ConsTotal = string.Format("{0:#,##0.0}", ConsTotal);
            }
            else
            {
                ViewBag.ConsTotal = string.Format("{0:#,##0}", ConsTotal);
            }
            ViewBag.ConsTotalN = ConsTotal;

            //
            // EVENTOS
            //

            // funcao relatorio eventos
            retorno = 0;

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_gateway = listaMedicoes.IDGW_GW;

            // converte data e hora
            DateTime DataAtualEv = Funcoes_Converte.ConverteData2DateTime(data_hora.data);

            // tipos de eventos
            TIPOS_EVENTO tipos_evento = new TIPOS_EVENTO();
            retorno = SmCalcDB_Eventos_Tipos((char)0, (char)listaMedicoes.IDTipoGateway_GW, ref tipos_evento);

            // lista tipos de eventos
            var listaTiposEventos = new List<TiposEventosDescricao>();

            // verifica se tem eventos
            if (tipos_evento.num_tipos > 0)
            {
                // percorre lista
                int contador = 0;

                for (contador = 0; contador < tipos_evento.num_tipos; contador++)
                {
                    string str_aux;
                    TiposEventosDescricao tipoeventoDescricao = new TiposEventosDescricao();

                    // tipo
                    tipoeventoDescricao.Tipo = tipos_evento.tipos[contador].tipo;

                    // codigos
                    List<int> CodigosList = null;

                    // Converter o byte[] para String
                    str_aux = Encoding.GetEncoding("ISO-8859-1").GetString(tipos_evento.tipos[contador].codigos);

                    // remove \0
                    str_aux = str_aux.Replace("\0", "");

                    // copia codigos para lista
                    if (!String.IsNullOrEmpty(str_aux))
                    {
                        CodigosList = str_aux.Split('/')
                            .Select(possibleIntegerAsString =>
                            {
                                int parsedInteger = 0;
                                bool isInteger = int.TryParse(possibleIntegerAsString, out parsedInteger);
                                return new { isInteger, parsedInteger };
                            })
                            .Where(tryParseResult => tryParseResult.isInteger)
                            .Select(tryParseResult => tryParseResult.parsedInteger)
                            .ToList();
                    }

                    // copia lista de codigos
                    tipoeventoDescricao.CodigosList = CodigosList;

                    // descricao

                    // Converter o byte[] para String
                    str_aux = Encoding.GetEncoding("ISO-8859-1").GetString(tipos_evento.tipos[contador].descricao);

                    // remove \0
                    str_aux = str_aux.Replace("\0", "");

                    // copia descricao evento
                    tipoeventoDescricao.Descricao = str_aux;

                    // adiciona tipo evento tratado
                    listaTiposEventos.Add(tipoeventoDescricao);
                }
            }

            // copia lista de tipos de eventos
            ViewBag.NumTiposEventos = tipos_evento.num_tipos;
            ViewBag.listaTiposEventosDescricao = listaTiposEventos;

            // le eventos
            var eventosMetodos = new EV_Metodos();
            var listaEventos = eventosMetodos.ListarPorDia(IDCliente, listaMedicoes.IDGW_GW, DataAtualEv, 500);

            // lista de eventos
            var listaEventosDescricao = new List<EventosDescricao>();

            // verifica se tem eventons
            if (listaEventos.Count() > 0)
            {
                // percorre lista
                int contador = 0;

                for (contador = 0; contador < listaEventos.Count(); contador++)
                {
                    EVENTO evento = new EVENTO();
                    EV_Dominio item_evento = new EV_Dominio();
                    EventosDescricao eventoDescricao = new EventosDescricao();
                    string descricao;

                    // copia evento
                    item_evento = listaEventos[contador];

                    // converte para tipo evento
                    Funcoes_Converte.ConverteDateTime2DataHora(ref evento.datahora, item_evento.DataHora);
                    evento.codigo = (short)item_evento.Evento;
                    evento.valor = item_evento.Valor;

                    // pega descricao
                    SmCalcDB_Eventos_Descricao((char)0, ref config_interface, (char)listaMedicoes.IDTipoGateway_GW, ref evento);
                    
                    // Converter o byte[] para String
                    descricao = Encoding.GetEncoding("ISO-8859-1").GetString(evento.descricao);

                    // remove \0
                    descricao = descricao.Replace("\0", "");

                    // copia descricao evento
                    eventoDescricao.Descricao = descricao;

                    // copia data evento
                    eventoDescricao.DataHora = string.Format("{0:d} {1:HH:mm:ss}", item_evento.DataHora, item_evento.DataHora);

                    // data e hora para sort
                    eventoDescricao.DataHora_Sort = String.Format("{0:yyyyMMddHHmmssfff}", item_evento.DataHora);

                    // percorre lista de tipos de eventos
                    eventoDescricao.Tipo = 0;

                    for (int contador2 = 0; contador2 < listaTiposEventos.Count(); contador2++)
                    {
                        // verifica se codigo esta na lista
                        if (listaTiposEventos[contador2].CodigosList.Contains(evento.codigo))
                        {
                            // pego codigo
                            eventoDescricao.Tipo = listaTiposEventos[contador2].Tipo;
                        }
                    }

                    // verifica se tipo UPLOAD
                    if (eventoDescricao.Tipo == 8)
                    {
                        if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
                        {
                            // adiciona evento tratado
                            listaEventosDescricao.Add(eventoDescricao);
                        }
                    }
                    else
                    {
                        // adiciona evento tratado
                        listaEventosDescricao.Add(eventoDescricao);
                    }
                }
            }

            // copia lista de eventos
            @ViewBag.listaEventosDescricao = listaEventosDescricao;

            //
            // GATEWAY
            //

            // lista de erros
            @ViewBag.listaErros = listaErros;

            // gateway
            ViewBag.GatewayNome = listaMedicoes.NGW;
            ViewBag.GatewayModelo = listaMedicoes.Modelo_GW + " (" + listaMedicoes.Versao_GW + ")";
            ViewBag.GatewayAtualizacao = listaMedicoes.DataHoraAtualizacao;
            ViewBag.GatewayDataEq = listaMedicoes.DataHoraEq;

            // caso for cliente, pego somente sinal
            if (IDTipoAcesso == 1 || IDTipoAcesso == 2 || IDTipoAcesso == 3 || IDTipoAcesso == 7 || IDTipoAcesso == 8 || IDTipoAcesso == 10)
            {
                int pos = listaMedicoes.Status_GW.IndexOf("SQ=");

                if (pos >= 0)
                {
                    string str = listaMedicoes.Status_GW.Substring(pos+3);
                    double sinal = double.Parse(str);

                    if( sinal >= 99.0)
                    {
                        sinal = 0.0;
                    }
                    else
                    {
                        sinal = (sinal / 31.0) * 100.0;
                    }

                    listaMedicoes.Status_GW = string.Format("Nível do Sinal {0:0}%", sinal);
                }
                else
                {
                    listaMedicoes.Status_GW = "-";
                }
            }

            ViewBag.GatewayStatus = listaMedicoes.Status_GW;

            // constante
            EN_K_Metodos constanteMetodos = new EN_K_Metodos();
            EN_K_Dominio constante = constanteMetodos.UltimaConstante(IDCliente, IDMedicao);
            double valor_constante = 0.0;

            if( constante != null )
            {
                valor_constante = constante.Constante;
            }

            ViewBag.Constante = string.Format("{0:0.00000}", valor_constante);

            // verifica se producao
            if (IDTipoAcesso == 4)
            {
                return View("../Supervisao/Medicao_Energia_Prod");
            }

            return View();
        }
    }
}