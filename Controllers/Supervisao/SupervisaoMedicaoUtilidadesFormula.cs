﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SupervisaoController
    {

        // GET: Medicao_Utilidades_Formula
        public unsafe ActionResult Medicao_Utilidades_Formula(int IDCliente, int IDMedicao)
        {
            // tela de ajuda - supervisao medicao atual
            CookieStore.SalvaCookie_String("PaginaAjuda", "Supervisao_Utilidades");

            // le supervisao da medicao
            var medicoesMetodos = new SupervMedicoesMetodos();
            var listaMedicoes = medicoesMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

            // salva cookie cliente
            CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);

            // le contrato cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cli = clienteMetodos.ListarPorId(IDCliente);
            CookieStore.SalvaCookie_Int("IDTipoContrato", cli.IDTipoContrato);

            // salva cookie medicao
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", listaMedicoes.IDTipoMedicao_MD);
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Supervisao");

            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // protege datas
            if (listaMedicoes.DataHoraAtualizacao == null)
            {
                listaMedicoes.DataHoraAtualizacao = "01/01/2000 00:00:00";
            }

            if (listaMedicoes.DataHoraEq == null)
            {
                listaMedicoes.DataHoraEq = "01/01/2000 00:00:00";
            }

            // dia atual
            DateTime datahora_ultima = DateTime.Parse(listaMedicoes.DataHoraAtualizacao);

            // funcao de supervisao
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            DATAHORA data_hora = new DATAHORA();
            UTIL_SUPERVMEDICAO superv = new UTIL_SUPERVMEDICAO();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = listaMedicoes.IDGW_GW;

            data_hora.data.dia = (char)datahora_ultima.Day;
            data_hora.data.mes = (char)datahora_ultima.Month;
            data_hora.data.ano = (short)datahora_ultima.Year;
            data_hora.hora.hora = (char)datahora_ultima.Hour;
            data_hora.hora.min = (char)datahora_ultima.Minute;
            data_hora.hora.seg = (char)datahora_ultima.Second;

            // caso for muito cedo, pego do dia anterior
            if (datahora_ultima.Hour < 12 && IDTipoAcesso != 4)
            {
                datahora_ultima = datahora_ultima.AddDays(-1);

                data_hora.data.dia = (char)datahora_ultima.Day;
                data_hora.data.mes = (char)datahora_ultima.Month;
                data_hora.data.ano = (short)datahora_ultima.Year;
                data_hora.hora.hora = (char)23;
                data_hora.hora.min = (char)45;
                data_hora.hora.seg = (char)0;
            }

            // calcula valores
            retorno = SmCalcDB_SupervMedicao_Util((char)0, ref config_interface, ref data_hora, ref superv);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Função [retorno {0}]", retorno));
                if (superv.flag_dia_atual > 0)
                    listaErros.Add(string.Format("Dia Atual [{0}]", Funcoes_SmartEnergy.ErroRelatorio((int)superv.flag_dia_atual)));
                if (superv.flag_mes_atual > 0)
                    listaErros.Add(string.Format("Mês Atual [{0}]", Funcoes_SmartEnergy.ErroRelatorio((int)superv.flag_mes_atual)));
                if (superv.flag_mes_anterior1 > 0)
                    listaErros.Add(string.Format("Mês Anterior [{0}]", Funcoes_SmartEnergy.ErroRelatorio((int)superv.flag_mes_anterior1)));
                if (superv.flag_fatura_atual > 0)
                    listaErros.Add(string.Format("Fatura Mês Atual [{0}]", Funcoes_SmartEnergy.ErroRelatorio((int)superv.flag_fatura_atual)));
                if (superv.flag_fatura_anterior1 > 0)
                    listaErros.Add(string.Format("Fatura Mês Anterior 1 [{0}]", Funcoes_SmartEnergy.ErroRelatorio((int)superv.flag_fatura_anterior1)));
                if (superv.flag_fatura_anterior2 > 0)
                    listaErros.Add(string.Format("Fatura Mês Anterior 2 [{0}]", Funcoes_SmartEnergy.ErroRelatorio((int)superv.flag_fatura_anterior2)));
            }

            @ViewBag.listaErros = listaErros;

            // converte data e hora
            DateTime DataHora_Atual = Funcoes_Converte.ConverteDataHora2DateTime(superv.DataAtual);
            DateTime DataHora_Ultimo = Funcoes_Converte.ConverteDataHora2DateTime(superv.DataHora_Ultimo);

            DateTime DiaAtual_Data = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dia_Atual.Data);
            DateTime DiaAtual_MaxDataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Dia_Atual.Valor_MaxDataHora);

            DateTime MesAtual_Data = Funcoes_Converte.ConverteDataHora2DateTime(superv.Mes_Atual.Data);
            DateTime MesAtual_MaxDataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Mes_Atual.Valor_MaxDataHora);

            DateTime MesAnt1_Data = Funcoes_Converte.ConverteDataHora2DateTime(superv.Mes_Anterior1.Data);
            DateTime MesAnt1_MaxDataHora = Funcoes_Converte.ConverteDataHora2DateTime(superv.Mes_Anterior1.Valor_MaxDataHora);

            DateTime Fatura_Atual_Data = Funcoes_Converte.ConverteData2DateTime(superv.Fatura_Atual.Data.data, 1);
            DateTime Fatura_Anterior1_Data = Funcoes_Converte.ConverteData2DateTime(superv.Fatura_Anterior1.Data.data, 1);
            DateTime Fatura_Anterior2_Data = Funcoes_Converte.ConverteData2DateTime(superv.Fatura_Anterior2.Data.data, 1);

            // datas
            ViewBag.DiaAtual = string.Format("{0:d}", DataHora_Atual);
            ViewBag.MesAtual = string.Format("{0:Y}", DataHora_Atual);
            ViewBag.MesAnterior = string.Format("{0:Y}", DataHora_Atual.AddMonths(-1));

            ViewBag.NomeGrandeza = listaMedicoes.NomeGrandeza;
            ViewBag.UnidadeGrandeza = listaMedicoes.UnidadeGrandeza;

            ViewBag.IDAgenteDistribuidora = listaMedicoes.IDAgenteDistribuidora;

            // graficos
            var Dias = new string[26];
            var Valor = new double[26];

            double Valor_min = 0.0;
            double Valor_max = 0.0;

            // valores
            DateTime strData = Funcoes_Converte.ConverteData2DateTime(superv.DataHora[0].data, (int)superv.DataHora[0].hora.hora, (int)superv.DataHora[0].hora.min);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proxima hora
                strData = strData.AddHours(1);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    Valor[i] = superv.Consumo[0];
                }

                // verifica se ultima barra
                if (i == 25)
                {
                    // zera
                    Valor[i] = superv.Consumo[23];
                }

                // restante
                if (i > 0 && i < 25)
                {
                    // copia
                    j = i - 1;

                    Valor[i] = superv.Consumo[j];

                    // verifica valor minimo
                    if (Valor[i] < Valor_min)
                        Valor_min = Valor[i];

                    // verifica valor maximo
                    if (Valor[i] > Valor_max)
                        Valor_max = Valor[i];
                }
            }

            // grafico comeca em zero caso minimo maior que zero
            if (Valor_min > 0.0)
            {
                Valor_min = 0.0;
            }

            Valor_min = Valor_min * 1.1;
            Valor_max = Valor_max * 1.1;

            ViewBag.Valor_min = Valor_min;
            ViewBag.Valor_max = Valor_max;

            ViewBag.Valor = Valor;
            ViewBag.Dias = Dias;

            // consumo
            ViewBag.DiaAtual_Data = string.Format("{0:d}", DiaAtual_Data);
            ViewBag.DiaAtual_Valor_Max = string.Format("{0:#,##0.00}", superv.Dia_Atual.Valor_Max);
            ViewBag.DiaAtual_MaxDataHora = string.Format("{0:d} {1:HH:mm}", DiaAtual_MaxDataHora, DiaAtual_MaxDataHora);
            ViewBag.DiaAtual_Valor_Medio = string.Format("{0:#,##0.00}", superv.Dia_Atual.Valor_Medio);
            ViewBag.DiaAtual_Valor_Total = string.Format("{0:#,##0.00}", superv.Dia_Atual.Valor_Total);

            ViewBag.MesAtual_Data = string.Format("{0:Y}", MesAtual_Data);
            ViewBag.MesAtual_Valor_Max = string.Format("{0:#,##0.00}", superv.Mes_Atual.Valor_Max);
            ViewBag.MesAtual_MaxDataHora = string.Format("{0:d} {1:HH:mm}", MesAtual_MaxDataHora, MesAtual_MaxDataHora);
            ViewBag.MesAtual_Valor_Medio = string.Format("{0:#,##0.00}", superv.Mes_Atual.Valor_Medio);
            ViewBag.MesAtual_Valor_Total = string.Format("{0:#,##0.00}", superv.Mes_Atual.Valor_Total);

            ViewBag.MesAnt1_Data = string.Format("{0:Y}", MesAnt1_Data);
            ViewBag.MesAnt1_Valor_Max = string.Format("{0:#,##0.00}", superv.Mes_Anterior1.Valor_Max);
            ViewBag.MesAnt1_MaxDataHora = string.Format("{0:d} {1:HH:mm}", MesAnt1_MaxDataHora, MesAnt1_MaxDataHora);
            ViewBag.MesAnt1_Valor_Medio = string.Format("{0:#,##0.00}", superv.Mes_Anterior1.Valor_Medio);
            ViewBag.MesAnt1_Valor_Total = string.Format("{0:#,##0.00}", superv.Mes_Anterior1.Valor_Total);

            // diferenca em % entre total Anterior e Atual
            double A = superv.Mes_Anterior1.Valor_Total;
            double B = superv.Mes_Atual.Valor_Total;
            double PorcAntAtual = (A == 0.0) ? 0.0 : ((B - A) / A) * 100.0;

            ViewBag.Valor_Total_PorcAntN = PorcAntAtual;
            ViewBag.Valor_Total_PorcAnt = string.Format("{0:0.0} %", PorcAntAtual);

            // faturas
            ViewBag.Fatura_Atual_Data = string.Format("{0:Y}", Fatura_Atual_Data);
            ViewBag.Fatura_Atual_DataMes = string.Format("{0:MMMM}", Fatura_Atual_Data);
            ViewBag.Fatura_Atual_ValorTotal = string.Format("{0:C}", superv.Fatura_Atual.ValorTotal);
            ViewBag.Fatura_Atual_ValorTotalN = superv.Fatura_Atual.ValorTotal;
            ViewBag.Fatura_Atual_CustoMedio = string.Format("{0:C} / m3", superv.Fatura_Atual.CustoMedio);

            ViewBag.Fatura_PorcAtualAnt1N = superv.Fatura_PorcAtualAnt1;
            ViewBag.Fatura_PorcAtualAnt1 = string.Format("{0:#,##0.0} %", superv.Fatura_PorcAtualAnt1);

            ViewBag.Fatura_Anterior1_Data = string.Format("{0:Y}", Fatura_Anterior1_Data);
            ViewBag.Fatura_Anterior1_DataMes = string.Format("{0:MMMM}", Fatura_Anterior1_Data);
            ViewBag.Fatura_Anterior1_ValorTotal = string.Format("{0:C}", superv.Fatura_Anterior1.ValorTotal);
            ViewBag.Fatura_Anterior1_ValorTotalN = superv.Fatura_Anterior1.ValorTotal;
            ViewBag.Fatura_Anterior1_CustoMedio = string.Format("{0:C} / m3", superv.Fatura_Anterior1.CustoMedio);

            ViewBag.Fatura_PorcAnt1Ant2N = superv.Fatura_PorcAnt1Ant2;
            ViewBag.Fatura_PorcAnt1Ant2 = string.Format("{0:#,##0.0} %", superv.Fatura_PorcAnt1Ant2);

            ViewBag.Fatura_Anterior2_Data = string.Format("{0:Y}", Fatura_Anterior2_Data);
            ViewBag.Fatura_Anterior2_DataMes = string.Format("{0:MMMM}", Fatura_Anterior2_Data);
            ViewBag.Fatura_Anterior2_ValorTotal = string.Format("{0:C}", superv.Fatura_Anterior2.ValorTotal);
            ViewBag.Fatura_Anterior2_ValorTotalN = superv.Fatura_Anterior2.ValorTotal;
            ViewBag.Fatura_Anterior2_CustoMedio = string.Format("{0:C} / m3", superv.Fatura_Anterior2.CustoMedio);

            // formula
            ViewBag.Formula = listaMedicoes.Formula;
            ViewBag.GatewayAtualizacao = string.Format("{0:d} {1:HH:mm}", DataHora_Ultimo, DataHora_Ultimo);

            return View();
        }
    }
}