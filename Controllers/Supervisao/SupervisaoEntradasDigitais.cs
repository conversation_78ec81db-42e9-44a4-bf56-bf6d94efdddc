﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SupervisaoController
    {

        // GET: Supervisão Entradas Digitais
        public ActionResult EntradasDigitais(int IDGateway, int IDMedicao = 0)
        {
            // caso tiver IDMedicao, utilizo ela para encontrar o IDGateway
            if (IDMedicao > 0)
            {
                // le gateway associada na medicao
                MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);

                // utilizo o IDGateway da medição
                IDGateway = medicao.IDGateway;
            }

            // tela de ajuda - supervisao de entradas digitais
            CookieStore.SalvaCookie_String("PaginaAjuda", "Supervisao_EntradasDigitais");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Supervisao");

            // le cookies
            LeCookies_SmartEnergy();

            // leio gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            //
            // IoT
            //

            // API Artemis Broker - verifica se gateway conectada no IoT
            ArtemisBroker artemis = new ArtemisBroker();
            gateway.GatewayConectada_IoT = artemis.Gateway_Conectada(gateway.IDGateway);
            ViewBag.GatewayConectada_IoT = gateway.GatewayConectada_IoT;

            // entradas digitais
            List<EntradasDigitaisDominio> listaEntradas = new List<EntradasDigitaisDominio>();

            // preenche entradas digitais
            Converte_SupervED(IDGateway, ref listaEntradas);

            // valores
            ViewBag.GatewayID = IDGateway;
            ViewBag.GatewayNome = gateway.Nome;
            ViewBag.GatewayConectada_IoT = gateway.GatewayConectada_IoT;
            ViewBag.listaEntradas = listaEntradas;

            return View();
        }

        //
        // SOLICITA ESTADO DAS ENTRADAS DIGITAIS
        //

        public PartialViewResult _EntradasDigitais_Resultado(int IDGateway)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // entradas digitais
            List<EntradasDigitaisDominio> listaEntradas = new List<EntradasDigitaisDominio>();

            // preenche entradas digitais
            Converte_SupervED(IDGateway, ref listaEntradas);

            // leio gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            if (gateway != null)
            {
                //
                // IoT
                //

                // API Artemis Broker - verifica se gateway conectada no IoT
                ArtemisBroker artemis = new ArtemisBroker();
                gateway.GatewayConectada_IoT = artemis.Gateway_Conectada(gateway.IDGateway);
                ViewBag.GatewayConectada_IoT = gateway.GatewayConectada_IoT;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // conexão com gateway
                if (smartCom.Conectar())
                {
                    // solicita programação das entradas
                    smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_ED, SMCOM_TIPO_SOL.SOLICITA);

                    // verifica se existe backup da configuração
                    GateX_ED_Metodos edMetodos = new GateX_ED_Metodos();
                    List<GateX_ED_Dominio> prgs = edMetodos.ListarProgramadosPorIDGateway(IDGateway);

                    if (prgs.Count == 0)
                    {
                        // atualiza backup
                        edMetodos.InserirLista(IDGateway, smartCom.gateX.ED, STATUS_CFG.ATUAL);
                    }

                    // solicita supervisão das entradas digitais
                    smartCom.Solicitar(SMCOM_FUNCOES_SOL.SUPERV_ED, SMCOM_TIPO_SOL.SOLICITA);

                    // copia resultado
                    listaEntradas = smartCom.gateX.ED_supervisao;
                }
            }

            // resultado
            ViewBag.listaEntradas = listaEntradas;

            return PartialView();
        }

        //
        // FUNÇÕES AUXILIARES
        //

        private void Converte_SupervED(int IDGateway, ref List<EntradasDigitaisDominio> listaEntradas)
        {
            // le entradas banco de dados
            GateX_ED_Metodos edMetodos = new GateX_ED_Metodos();
            List<GateX_ED_Dominio> entradas = edMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.ATUAL);

            // limpa lista
            listaEntradas.Clear();

            // atualiza
            foreach (GateX_ED_Dominio ed in entradas)
            {
                // verifica se programado
                if (ed.Programado)
                {
                    EntradasDigitaisDominio entrada = new EntradasDigitaisDominio();
                    entrada.IDCliente = 0;
                    entrada.IDGateway = ed.IDGateway;
                    entrada.Descricao = ed.Descricao;
                    entrada.Ativou = ed.Ativo;
                    entrada.Desativou = ed.Inativo;
                    entrada.NumEntradaGateway = ed.NumEntradaGateway;

                    entrada.Programado = true;
                    entrada.status = STATUS_ED.DESCONHECIDO;

                    listaEntradas.Add(entrada);
                }
            }

            return;
        }

        private void AlteraEstadoEntradas(ref List<EntradasDigitaisDominio> entradas, int novo_estado)
        {
            // percorre as entradas e seta novo estado
            if (entradas != null)
            {
                foreach (EntradasDigitaisDominio ed in entradas)
                {
                    // novo estado
                    ed.status = novo_estado;
                }
            }

            return;
        }
    }
}