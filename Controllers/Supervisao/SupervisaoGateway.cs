﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SupervisaoController
    {
        public ActionResult Gateway(int IDGateway)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // leio supervisao gateway
            SupervGatewaysMetodos gatewaysMetodos = new SupervGatewaysMetodos();
            SupervGatewaysDominio gateway = new SupervGatewaysDominio();
            gateway = gatewaysMetodos.ListarPorIDGateway(IDGateway);

            if (gateway != null)
            {
                // verifica se cliente diferente
                if (IDCliente != gateway.IDCliente)
                {
                    CookieStore.SalvaCookie_Int("_IDCliente", gateway.IDCliente);

                    // le contrato cliente
                    ClientesMetodos clienteMetodos = new ClientesMetodos();
                    ClientesDominio cli = clienteMetodos.ListarPorId(gateway.IDCliente);
                    CookieStore.SalvaCookie_Int("IDTipoContrato", cli.IDTipoContrato);

                    // le cookies
                    LeCookies_SmartEnergy();
                }

                // verifica se é SmartGateE
                if (gateway.IDTipoGateway == TIPO_GATEWAY.GATE_E)
                {
                    // número de série
                    NumeroSerieMetodos numSerieMetodos = new NumeroSerieMetodos();
                    NumeroSerieDominio numSerie = numSerieMetodos.ListarPorId(gateway.IDNumeroSerie);

                    if (numSerie != null)
                    {
                        // copia número de série
                        gateway.NS_Gateway = numSerie.NS_Gateway;
                    }
                    else
                    {
                        gateway.NS_Gateway = "-";
                    }
                }
            }

            // valores
            ViewBag.GatewayID = IDGateway;
            ViewBag.GatewayNome = gateway.Nome;
            ViewBag.GatewayModelo = string.Format("{0} ({1})", gateway.ModeloEq, gateway.VersaoEq);
            ViewBag.GatewayTipo = gateway.IDTipoGateway;
            ViewBag.GatewayNS = gateway.NS_Gateway;

            ViewBag.GatewayStatus = gateway.StatusEq;
            ViewBag.GatewayIP = gateway.IP;

            string operadora = (gateway.OP.Length > 0) ? gateway.OP : "---";
            ViewBag.GatewayOperadora = operadora;

            int sinal = gateway.Sinal;
            string gatewaySinal = "---";

            if (sinal > 0)
            {
                string SinalTexto = "[Sinal Fraco]";

                if (sinal >= 38 && sinal < 50)
                {
                    SinalTexto = "[Sinal Médio Fraco]";
                }

                if (sinal >= 50 && sinal < 68)
                {
                    SinalTexto = "[Sinal Médio]";
                }

                if (sinal >= 68 && sinal <= 100)
                {
                    SinalTexto = "[Sinal Forte]";
                }

                if (sinal >= 100)
                {
                    SinalTexto = "";
                }

                gatewaySinal = string.Format("{0}% {1}", gateway.Sinal, SinalTexto);
            }
            ViewBag.GatewaySinal = gatewaySinal;

            ViewBag.GatewayEndereco = gateway.EnderecoLocal;

            ViewBag.GatewayAtualizacao = string.Format("{0:d} {1:HH:mm:ss}", gateway.DataHora, gateway.DataHora);
            ViewBag.GatewayDataEq = string.Format("{0:d} {1:HH:mm:ss}", gateway.DataEq, gateway.DataEq);

            // verifica se é SmartGateE
            if (gateway.IDTipoGateway == TIPO_GATEWAY.GATE_E)
            {
                // receber versão
                GateE_InfoGeral_Receber(IDGateway);
            }

            return View();
        }

        // InfoGeral - Receber
        private void GateE_InfoGeral_Receber(int IDGateway)
        {
            // InfoGeral
            GateE_InfoGeral_Dominio infoGeral = new GateE_InfoGeral_Dominio();

            // SmartMQTT
            SmartMQTT smartMQTT = new SmartMQTT(IDGateway);
            smartMQTT.gateE_InfoGeral_Default(IDGateway, ref infoGeral);

            // status
            bool solicitaSuperv = false;

            // comunicação MQTT
            SmartCom smartCom = new SmartCom(IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita InfoGeral
                if (solicitaSuperv = smartCom.Solicitar(SMCOM_FUNCOES_SOL.INFO_GERAL, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // copia InfoGeral recebida
                    infoGeral = smartCom.gateE.InfoGeral;
                }
            }

            // copia novo status da solicitacao para a view
            ViewBag.SolicitaSuperv = solicitaSuperv;

            // verifica se recebeu infogeral
            if (solicitaSuperv)
            {
                // sinal
                int sinal = 0;

                // verifica se Wi-Fi ou GSM
                if (infoGeral.IF_IoT == GateE_TIPO_INTERFACE_IOT.SIMCARD)
                {
                    // GSM
                    sinal = ((infoGeral.SQ_gsm * 100) / 31);
                    infoGeral.status_GSM_WIFI = (infoGeral.eq32[(int)GateE_EQ32.if4GM1].nok_sin) ? string.Format("{0} (Sem Conexão)", infoGeral.gsm_OP) : string.Format("{0} (Sinal {1}%)", infoGeral.gsm_OP, sinal);
                }
                else
                {
                    // Wi-Fi
                    sinal = ((infoGeral.SQ_wifi * 100) / 31);
                    infoGeral.status_GSM_WIFI = (infoGeral.eq32[(int)GateE_EQ32.cxWIFI].nok_sin) ? "Sem Conexão" : string.Format("Sinal {0}%", sinal);
                }

                // sinal GSM/WIFI em indice (0: 0% / 1: 1%-25% / 2: 26%-60% / 3: 61%-89% / 4: 90%-100%)
                if (sinal == 0)
                {
                    infoGeral.sinal_GSM_WIFI = 0;
                }
                else if (sinal >= 1 && sinal <= 25)
                {
                    infoGeral.sinal_GSM_WIFI = 1;
                }
                else if (sinal >= 26 && sinal <= 60)
                {
                    infoGeral.sinal_GSM_WIFI = 2;
                }
                else if (sinal >= 61 && sinal <= 89)
                {
                    infoGeral.sinal_GSM_WIFI = 3;
                }
                else if (sinal >= 90)
                {
                    infoGeral.sinal_GSM_WIFI = 4;
                }

                // OTA
                infoGeral.status_OTA = "---";

                if (infoGeral.OTA)
                {
                    infoGeral.status_OTA = string.Format("{0:0.0}%", infoGeral.OTA_dwl);
                }

                // status da Gateway
                infoGeral.status_Gateway = MontaDescricao_StatusGateway(infoGeral);

                // interfaces
                infoGeral.status_Interfaces = Monta_StatusInterfaces(infoGeral);

                // remotas I/O
                infoGeral.status_RemotasIO = Monta_StatusRemotas_IO(infoGeral);

                // remotas Lora
                infoGeral.status_RemotasLora = Monta_StatusRemotas_Lora(infoGeral);

                //  medições de energia
                infoGeral.status_MedicoesEnergia = Monta_StatusMedicoes_Energia(infoGeral);

                //  medições de utilidades
                infoGeral.status_MedicoesUtilidades = Monta_StatusMedicoes_Utilidades(infoGeral);

                //  medições analógicas
                infoGeral.status_MedicoesAnalogicas = Monta_StatusMedicoes_Analogicas(infoGeral);

                //  medições ciclômetro
                infoGeral.status_MedicoesCiclometro = Monta_StatusMedicoes_Ciclometro(infoGeral);

                // CODI
                if (infoGeral.eq32[(int)GateE_EQ32.CODI].nok_sin)
                {
                    infoGeral.status_CODI = "ERRO";
                }
                else
                {
                    infoGeral.status_CODI = (infoGeral.codi_ext == 0) ? "Protocolo Normal" : "Protocolo Estendido";
                }
            }

            // copia para a view
            ViewBag.infoGeral = infoGeral;

            // retorna versão
            return;
        }

        // monta status da gateway
        private List<string> MontaDescricao_StatusGateway(GateE_InfoGeral_Dominio infoGeral)
        {
            // nome das medições
            GateE_MedEner_Metodos medicaoMetodos = new GateE_MedEner_Metodos();
            List<GateE_MedEner_Dominio> medicoes = medicaoMetodos.ListarPorIDGateway(infoGeral.IDGateway, STATUS_CFG.ATUAL);

            // lista de status
            List<string> lista = new List<string>();

            // percorre os bits a procura do bit setado
            for (short _bit = 0; _bit < 32; _bit++)
            {
                // verifica o bit setado em "1"
                if (infoGeral.eq32[_bit].nok_sin)
                {
                    // descrição do status
                    string descricao = string.Empty;

                    // verifica item
                    switch ((GateE_EQ32)_bit)
                    {
                        case GateE_EQ32.if4GM1:
                            descricao = "Erro na conexão IoT - Interface Modem: Fase 1";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.ifBTOO:
                            descricao = "Erro na interface Bluetooth da Gateway";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.ifWIFI:
                            descricao = "Erro na conexão IoT - Interface WiFi: Fase 1";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.ifLORA:
                            descricao = "Erro na interface Lora";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.if485:
                            descricao = "Erro na interface RS 485";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.eq32_05:
                            descricao = "Erro: GT_NOK_05";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.eq32_06:
                            descricao = "Erro: GT_NOK_06";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.SIM:
                            descricao = "Erro na conexão IoT - SIM Card: Fase 2";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.rgOP:
                            descricao = "Erro na conexão IoT - Registro: Fase 3";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.cxWIFI:
                            descricao = "Erro na conexão IoT - Registro : Fase 3";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.skIOT:
                            descricao = "Erro na conexão IoT - Socket : Fase 4";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.VIN:
                            descricao = "Tensão fora da normalidade";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.VBAT:
                            descricao = "Tensão da Bateria fora da normalidade";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.TBAT:
                            descricao = "Temperatura da Bateria fora da normalidade";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.mdEN:

                            // percorre os bits do status da medicao de energia
                            for (short _b = 0; _b < 3; _b++)
                            {
                                // verifica o bit setado em "1"
                                if (infoGeral.mdEN[_b].nok_sin)
                                {
                                    // nome default
                                    string nome = string.Format("[{0}] Medição {0}", _b);

                                    // verifica se existe nome no banco de dados
                                    GateE_MedEner_Dominio medicao = medicoes.Find(m => m.NumMedicaoGateway == _b);
                                    if (medicao != null)
                                    {
                                        nome = string.Format("[{0}] {1}", _b, medicao.Descricao);
                                    }

                                    // monta a descricao com a medicao 
                                    descricao = string.Format("Medição de Energia em ZERO: {0}", nome);
                                    lista.Add(descricao);
                                }
                            }
                            break;

                        case GateE_EQ32.mdUT:

                            // percorre os bits do status da medicao de utilidades
                            for (short _b = 0; _b < 3; _b++)
                            {
                                // verifica o bit setado em "1"
                                if (infoGeral.mdUT[_b].nok_sin)
                                {
                                    // monta a descricao com a medicao 
                                    descricao = string.Format("Medição de Utilidades em ZERO: [{0}] Medição {0}", _b);
                                    lista.Add(descricao);
                                }
                            }
                            break;

                        case GateE_EQ32.mdAN:

                            // percorre os bits do status da medicao de analogicas
                            for (short _b = 0; _b < 3; _b++)
                            {
                                // verifica o bit setado em "1"
                                if (infoGeral.mdAN[_b].nok_sin)
                                {
                                    // monta a descricao com a medicao 
                                    descricao = string.Format("Medição Analógica em ZERO: [{0}] Medição {0}", _b);
                                    lista.Add(descricao);
                                }
                            }
                            break;

                        case GateE_EQ32.mdCY:

                            // percorre os bits do status da medicao de ciclometro
                            for (short _b = 0; _b < 3; _b++)
                            {
                                // verifica o bit setado em "1"
                                if (infoGeral.mdCY[_b].nok_sin)
                                {
                                    // monta a descricao com a medicao 
                                    descricao = string.Format("Medição Ciclômetro em ZERO: [{0}] Medição {0}", _b);
                                    lista.Add(descricao);
                                }
                            }
                            break;

                        case GateE_EQ32.rmIO:

                            // percorre os bits do status das remotas
                            for (short _b = 0; _b < 32; _b++)
                            {
                                // verifica o bit setado em "1"
                                if (infoGeral.rmIO[_b].nok_sin)
                                {
                                    // monta a descricao com a remota com erro
                                    descricao = string.Format("Erro na remota I/O: Remota {0}", _b);
                                    lista.Add(descricao);
                                }
                            }
                            break;

                        case GateE_EQ32.rmLORA:

                            // percorre os bits do status das remotas
                            for (short _b = 0; _b < 32; _b++)
                            {
                                // verifica o bit setado em "1"
                                if (infoGeral.rmLr[_b].nok_sin)
                                {
                                    // monta a descricao com a remota com erro
                                    descricao = string.Format("Erro na remota LORA: Remota {0}", _b);
                                    lista.Add(descricao);
                                }
                            }
                            break;

                        case GateE_EQ32.CODI:
                            descricao = "Erro no Medidor de Faturamento";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.eq32_21:
                            descricao = "Erro: GT_NOK_21";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.eq32_22:
                            descricao = "Erro: GT_NOK_22";
                            lista.Add(descricao);
                            break;

                        case GateE_EQ32.eq32_23:
                            descricao = "Erro: GT_NOK_23";
                            lista.Add(descricao);
                            break;

                        default:
                            descricao = string.Format("Erro Desconhecido [{0}]", _bit);
                            lista.Add(descricao);
                            break;
                    }
                }
            }

            // ordena
            lista.Sort();

            // retorna lista
            return lista;
        }

        // monta status das interfaces
        private List<int> Monta_StatusInterfaces(GateE_InfoGeral_Dominio infoGeral)
        {
            // lista de status
            List<int> lista = new List<int>();

            // bluetooth
            int status_ifBTOO = 0;
            if (infoGeral.eq32[(int)GateE_EQ32.ifBTOO].cfg)
            {
                status_ifBTOO = (infoGeral.eq32[(int)GateE_EQ32.ifBTOO].nok_sin) ? 2 : 1;
            }
            lista.Add(status_ifBTOO);

            // GSM
            int status_if4GM1 = 0;
            if (infoGeral.eq32[(int)GateE_EQ32.if4GM1].cfg)
            {
                status_if4GM1 = (infoGeral.eq32[(int)GateE_EQ32.if4GM1].nok_sin) ? 2 : 1;
            }
            lista.Add(status_if4GM1);

            // Wi-Fi
            int status_ifWIFI = 0;
            if (infoGeral.eq32[(int)GateE_EQ32.ifWIFI].cfg)
            {
                status_ifWIFI = (infoGeral.eq32[(int)GateE_EQ32.ifWIFI].nok_sin) ? 2 : 1;
            }
            lista.Add(status_ifWIFI);

            // LORA
            int status_ifLORA = 0;
            if (infoGeral.eq32[(int)GateE_EQ32.ifLORA].cfg)
            {
                status_ifLORA = (infoGeral.eq32[(int)GateE_EQ32.ifLORA].nok_sin) ? 2 : 1;
            }
            lista.Add(status_ifLORA);

            // RS485
            int status_if485 = 0;
            if (infoGeral.eq32[(int)GateE_EQ32.if485].cfg)
            {
                status_if485 = (infoGeral.eq32[(int)GateE_EQ32.if485].nok_sin) ? 2 : 1;
            }
            lista.Add(status_if485);

            // retorna lista
            return lista;
        }

        // monta status das remotas I/O
        private List<int> Monta_StatusRemotas_IO(GateE_InfoGeral_Dominio infoGeral)
        {
            // lista de status
            List<int> lista = new List<int>();

            // a principio nenhuma remota configurada
            bool remota_configurada = false;

            // remotas I/O
            for (int i = 0; i < 32; i++)
            {
                // inicialmente remota não configurada
                int status = GateE_STATUS_REMOTA_SINAL.NAO_CONFIGURADO;

                // verifica se remota configurada
                if (infoGeral.rmIO[i].cfg)
                {
                    // status
                    status = (infoGeral.rmIO[i].nok_rem) ? GateE_STATUS_REMOTA_SINAL.FALHA_REMOTA : GateE_STATUS_REMOTA_SINAL.OK;

                    // remota configurada
                    remota_configurada = true;
                }

                lista.Add(status);
            }

            // verifica se não tem remota configurada
            if (!remota_configurada)
            {
                // apago lista
                lista.Clear();
            }

            // retorna lista
            return lista;
        }

        // monta status das remotas Lora
        private List<int> Monta_StatusRemotas_Lora(GateE_InfoGeral_Dominio infoGeral)
        {
            // lista de status
            List<int> lista = new List<int>();

            // a principio nenhuma remota configurada
            bool remota_configurada = false;

            // remotas Lora
            for (int i = 0; i < 32; i++)
            {
                // inicialmente remota não configurada
                int status = GateE_STATUS_REMOTA_SINAL.NAO_CONFIGURADO;

                // verifica se remota configurada
                if (infoGeral.rmLr[i].cfg)
                {
                    // status
                    status = (infoGeral.rmLr[i].nok_rem) ? GateE_STATUS_REMOTA_SINAL.FALHA_REMOTA : GateE_STATUS_REMOTA_SINAL.OK;

                    // remota configurada
                    remota_configurada = true;
                }

                lista.Add(status);
            }

            // verifica se não tem remota configurada
            if (!remota_configurada)
            {
                // apago lista
                lista.Clear();
            }

            // retorna lista
            return lista;
        }

        // monta status das medições de energia
        private List<GateE_InfoGeral_Medicao> Monta_StatusMedicoes_Energia(GateE_InfoGeral_Dominio infoGeral)
        {
            // lista de status
            List<GateE_InfoGeral_Medicao> lista = new List<GateE_InfoGeral_Medicao>();

            // a principio nenhuma medição configurada
            bool medicao_configurada = false;

            // nome das medições
            GateE_MedEner_Metodos medicaoMetodos = new GateE_MedEner_Metodos();
            List<GateE_MedEner_Dominio> medicoes = medicaoMetodos.ListarPorIDGateway(infoGeral.IDGateway, STATUS_CFG.ATUAL);

            // medições
            for (int i = 0; i < 3; i++)
            {
                // inicialmente medição não configurada
                GateE_InfoGeral_Medicao status = new GateE_InfoGeral_Medicao();
                status.status = GateE_STATUS_REMOTA_SINAL.NAO_CONFIGURADO;
                status.nome = string.Format("[{0}] Medição {0}", i);

                // verifica se existe nome no banco de dados
                GateE_MedEner_Dominio medicao = medicoes.Find(m => m.NumMedicaoGateway == i);
                if (medicao != null)
                {
                    status.nome = string.Format("[{0}] {1}", i, medicao.Descricao);
                }

                // verifica se medição configurada
                if (infoGeral.mdEN[i].cfg)
                {
                    // verifica se remota está ok
                    if (infoGeral.mdEN[i].nok_rem)
                    {
                        // erro de remota (CODI ou remota)
                        status.status = GateE_STATUS_REMOTA_SINAL.FALHA_REMOTA;
                    }
                    else
                    {
                        // verifica se sinal está ok
                        if (infoGeral.mdEN[i].nok_sin)
                        {
                            // erro de sinal
                            status.status = GateE_STATUS_REMOTA_SINAL.FALHA_SINAL;
                        }
                        else
                        {
                            // ok
                            status.status = GateE_STATUS_REMOTA_SINAL.OK;
                        }
                    }

                    // medição configurada
                    medicao_configurada = true;
                }
                else
                {
                    // medição não configurada
                    status.status = GateE_STATUS_REMOTA_SINAL.NAO_CONFIGURADO;
                }

                lista.Add(status);
            }

            // verifica se não tem medição configurada
            if (!medicao_configurada)
            {
                // apago lista
                lista.Clear();
            }

            // retorna lista
            return lista;
        }

        // monta status das medições de utilidades
        private List<GateE_InfoGeral_Medicao> Monta_StatusMedicoes_Utilidades(GateE_InfoGeral_Dominio infoGeral)
        {
            // lista de status
            List<GateE_InfoGeral_Medicao> lista = new List<GateE_InfoGeral_Medicao>();

            // a principio nenhuma medição configurada
            bool medicao_configurada = false;

            // medições
            for (int i = 0; i < 3; i++)
            {
                // inicialmente medição não configurada
                GateE_InfoGeral_Medicao status = new GateE_InfoGeral_Medicao();
                status.status = GateE_STATUS_REMOTA_SINAL.NAO_CONFIGURADO;
                status.nome = string.Format("[{0}] Medição {0}", i);

                // verifica se medição configurada
                if (infoGeral.mdUT[i].cfg)
                {
                    // verifica se remota está ok
                    if (infoGeral.mdUT[i].nok_rem)
                    {
                        // erro de remota (CODI ou remota)
                        status.status = GateE_STATUS_REMOTA_SINAL.FALHA_REMOTA;
                    }
                    else
                    {
                        // verifica se sinal está ok
                        if (infoGeral.mdUT[i].nok_sin)
                        {
                            // erro de sinal
                            status.status = GateE_STATUS_REMOTA_SINAL.FALHA_SINAL;
                        }
                        else
                        {
                            // ok
                            status.status = GateE_STATUS_REMOTA_SINAL.OK;
                        }
                    }

                    // medição configurada
                    medicao_configurada = true;
                }
                else
                {
                    // medição não configurada
                    status.status = GateE_STATUS_REMOTA_SINAL.NAO_CONFIGURADO;
                }

                lista.Add(status);
            }

            // verifica se não tem medição configurada
            if (!medicao_configurada)
            {
                // apago lista
                lista.Clear();
            }

            // retorna lista
            return lista;
        }

        // monta status das medições analógicas
        private List<GateE_InfoGeral_Medicao> Monta_StatusMedicoes_Analogicas(GateE_InfoGeral_Dominio infoGeral)
        {
            // lista de status
            List<GateE_InfoGeral_Medicao> lista = new List<GateE_InfoGeral_Medicao>();

            // a principio nenhuma medição configurada
            bool medicao_configurada = false;

            // medições
            for (int i = 0; i < 3; i++)
            {
                // inicialmente medição não configurada
                GateE_InfoGeral_Medicao status = new GateE_InfoGeral_Medicao();
                status.status = GateE_STATUS_REMOTA_SINAL.NAO_CONFIGURADO;
                status.nome = string.Format("[{0}] Medição {0}", i);

                // verifica se medição configurada
                if (infoGeral.mdAN[i].cfg)
                {
                    // verifica se remota está ok
                    if (infoGeral.mdAN[i].nok_rem)
                    {
                        // erro de remota (CODI ou remota)
                        status.status = GateE_STATUS_REMOTA_SINAL.FALHA_REMOTA;
                    }
                    else
                    {
                        // verifica se sinal está ok
                        if (infoGeral.mdAN[i].nok_sin)
                        {
                            // erro de sinal
                            status.status = GateE_STATUS_REMOTA_SINAL.FALHA_SINAL;
                        }
                        else
                        {
                            // ok
                            status.status = GateE_STATUS_REMOTA_SINAL.OK;
                        }
                    }

                    // medição configurada
                    medicao_configurada = true;
                }
                else
                {
                    // medição não configurada
                    status.status = GateE_STATUS_REMOTA_SINAL.NAO_CONFIGURADO;
                }

                lista.Add(status);
            }

            // verifica se não tem medição configurada
            if (!medicao_configurada)
            {
                // apago lista
                lista.Clear();
            }

            // retorna lista
            return lista;
        }

        // monta status das medições ciclômetro
        private List<GateE_InfoGeral_Medicao> Monta_StatusMedicoes_Ciclometro(GateE_InfoGeral_Dominio infoGeral)
        {
            // lista de status
            List<GateE_InfoGeral_Medicao> lista = new List<GateE_InfoGeral_Medicao>();

            // a principio nenhuma medição configurada
            bool medicao_configurada = false;

            // medições
            for (int i = 0; i < 3; i++)
            {
                // inicialmente medição não configurada
                GateE_InfoGeral_Medicao status = new GateE_InfoGeral_Medicao();
                status.status = GateE_STATUS_REMOTA_SINAL.NAO_CONFIGURADO;
                status.nome = string.Format("[{0}] Medição {0}", i);

                // verifica se medição configurada
                if (infoGeral.mdCY[i].cfg)
                {
                    // verifica se remota está ok
                    if (infoGeral.mdCY[i].nok_rem)
                    {
                        // erro de remota (CODI ou remota)
                        status.status = GateE_STATUS_REMOTA_SINAL.FALHA_REMOTA;
                    }
                    else
                    {
                        // verifica se sinal está ok
                        if (infoGeral.mdCY[i].nok_sin)
                        {
                            // erro de sinal
                            status.status = GateE_STATUS_REMOTA_SINAL.FALHA_SINAL;
                        }
                        else
                        {
                            // ok
                            status.status = GateE_STATUS_REMOTA_SINAL.OK;
                        }
                    }

                    // medição configurada
                    medicao_configurada = true;
                }
                else
                {
                    // medição não configurada
                    status.status = GateE_STATUS_REMOTA_SINAL.NAO_CONFIGURADO;
                }

                lista.Add(status);
            }

            // verifica se não tem medição configurada
            if (!medicao_configurada)
            {
                // apago lista
                lista.Clear();
            }

            // retorna lista
            return lista;
        }
    }
}