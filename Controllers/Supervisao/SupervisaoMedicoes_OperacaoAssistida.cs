﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class SupervisaoController
    {
        // GET: Supervisao Medicoes Operacao Assistida
        public ActionResult Medicoes_OperacaoAssistida(int IDCliente)
        {
            // salva cookie
            CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);

            // le cookies
            LeCookies_SmartEnergy();

            // mes atual
            DateTime datahora_ultima = DateTime.Now;
            DATAHORA data_hora = new DATAHORA();

            // le cookie datahora
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            // verifica se existe datahora no cookie
            if (datahora_cookie.Year != 2000)
            {
                // copia data do cookie 
                datahora_ultima = datahora_cookie;
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_ultima);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", datahora_ultima);
            ViewBag.DataAtualN = datahora_ultima;
            ViewBag.DataTextoAtual = string.Format("{0:MM/yyyy}", datahora_ultima);

            // medicoes 
            List<OperacaoAssistida> medicoes_operacaoassistida = new List<OperacaoAssistida>();
            ViewBag.medicoes = medicoes_operacaoassistida;

            // numero de dias
            int NumDias = (int)DateTime.DaysInMonth((int)data_hora.data.ano, (int)data_hora.data.mes);
            ViewBag.NumDias = NumDias;

            return View();
        }

        // GET: Supervisao Medicoes Operacao Assistida Atualizar
        public PartialViewResult _Medicoes_OperacaoAssistida_Atualizar(int Navegacao, string Data)
        {
            // caso forcar data
            if (Navegacao == 100)
            {
                // pega data
                DateTime dateValue = DateTime.Parse(Data);

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Relat_Data", dateValue);
            }

            // tela de ajuda - supervisao 
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;

            // le cookie datahora
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");
            DATAHORA data_hora = new DATAHORA();


            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -3:    // mes anterior

                    datahora_cookie = datahora_cookie.AddMonths(-1);
                    break;

                case 3:    // mes seguinte

                    datahora_cookie = datahora_cookie.AddMonths(1);
                    break;

                case -4:    // ano anterior

                    datahora_cookie = datahora_cookie.AddYears(-1);
                    break;

                case 4:    // ano seguinte

                    datahora_cookie = datahora_cookie.AddYears(1);
                    break;

                case 10:    // ultimo

                    // dia atual
                    datahora_cookie = DateTime.Now;

                    break;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_cookie);

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_cookie);

            // status
            Status_OperacaoAssistida(IDCliente, data_hora);

            return PartialView();
        }

        // Status Operacao Assistida
        private void Status_OperacaoAssistida(int IDCliente, DATAHORA datahora)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // lista de medicoes
            List<int> ConfigMedList = ViewBag._ConfigMed;

            // converte data e hora
            DateTime DataAtual = Funcoes_Converte.ConverteDataHora2DateTime(datahora);

            // numero de dias
            int NumDias = (int)DateTime.DaysInMonth((int)datahora.data.ano, (int)datahora.data.mes);
            ViewBag.NumDias = NumDias;

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:MM/yyyy}", DataAtual);

            // le supervisao das medicoes
            SupervMedicoesMetodos medicoesMetodos = new SupervMedicoesMetodos();
            List<SupervMedicoesDominio> medicoes = medicoesMetodos.ListarPorIDCliente(IDCliente, 0, IDUsuario, IDTipoAcesso, ConfigMedList);

            // medicoes
            List<OperacaoAssistida> medicoes_operacaoassistida = new List<OperacaoAssistida>();

            // percorre medicoes
            if (medicoes != null)
            {
                foreach (SupervMedicoesDominio medicao in medicoes)
                {
                    // verifica se nao eh energia
                    if (medicao.IDTipoMedicao_MD != 0)
                    {
                        continue;
                    }

                    // coloca na lista
                    OperacaoAssistida operacaoAssistida = new OperacaoAssistida();

                    // copia estrutura
                    for (int i = 0; i < 31; i++)
                    {
                        // status
                        OperacaoAssistidaStatus status = new OperacaoAssistidaStatus();

                        if( i < NumDias)
                        {
                            // dia
                            DateTime dia = new DateTime(datahora.data.ano, datahora.data.mes, i + 1, 0, 15, 0);

                            EN_Metodos enMetodos = new EN_Metodos();
                            status.Status = enMetodos.VerificarDia(IDCliente, medicao.IDMD_MD, dia);

                            status.Data = dia;
                            status.Comentarios = "";
                        }
                        else
                        {
                            status.Data = DataAtual;
                            status.Status = 1;
                            status.Comentarios = "";
                        }

                        operacaoAssistida.Dia[i] = status;
                    }

                    operacaoAssistida.IDMedicao = medicao.IDMD_MD;
                    operacaoAssistida.NomeMedicao = medicao.NMED;
                    operacaoAssistida.NomeUnidade = medicao.NUN;
                    operacaoAssistida.NomeGrupoUnidades = medicao.NGU;
                    operacaoAssistida.DataHoraAtualizacao = medicao.DataHoraAtualizacao;

                    operacaoAssistida.NumDias = (int)DateTime.DaysInMonth((int)datahora.data.ano, (int)datahora.data.mes); 

                    medicoes_operacaoassistida.Add(operacaoAssistida);
                }
            }

            // medicoes
            ViewBag.medicoes = medicoes_operacaoassistida;

            return;
        }
    }
}