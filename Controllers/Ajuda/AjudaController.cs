﻿using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class AjudaController : Controller
    {
        // GET: Ajuda
        public PartialViewResult _Ajuda()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // verifica se tela de contato
            if (ViewBag.PaginaAjuda == "Contato")
            {
                // consultor padrao - GESTAL
                ConsultoresDominio consultor = new ConsultoresDominio();

                consultor.IDConsultor = 0;
                consultor.Nome = "GESTAL Gestão de Energia e Utilidades Ltda.";
                consultor.Email = "<EMAIL>";
                consultor.Telefone1 = "(11) 5080-8200 [Ramal 232]";
                consultor.Telefone2 = "";
                consultor.Telefone3 = "";
                consultor.Celular = "(11) 9.7598-5359";
                consultor.Endereco = "Rua Borges Lagoa, 190";
                consultor.CEP = "04038-000";
                consultor.IDCidade = 5270;
                consultor.Cidade = "";
                consultor.Estado = "";
                consultor.IDEstado = 26;
                consultor.Latitude = 0;
                consultor.Longitude = 0;

                // verifica IDConsultor
                int IDConsultor = ViewBag._IDConsultor;

                if (IDConsultor > 0)
                {
                    ConsultoresMetodos consultorMetodos = new ConsultoresMetodos();
                    ConsultoresDominio consultor_bd = consultorMetodos.ListarPorId(IDConsultor);

                    if (consultor_bd != null)
                    {
                        // copia
                        consultor = consultor_bd;
                    }
                }

                // cidade
                CidadeEstadoPaisMetodos cidadeEstadoMetodos = new CidadeEstadoPaisMetodos();
                CidadesDominio cidade = cidadeEstadoMetodos.CidadePorId(consultor.IDCidade);
                if (cidade != null)
                {
                    consultor.Cidade = cidade.Nome;
                }

                // estado
                EstadosDominio estado = cidadeEstadoMetodos.EstadoPorId(consultor.IDEstado);
                if (estado != null)
                {
                    consultor.Estado = estado.Nome;
                }

                ViewBag.Consultor = consultor;
            }

            return PartialView();
        }
    }
}