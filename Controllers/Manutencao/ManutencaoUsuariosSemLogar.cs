﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public class UsuarioSemLogar_Resultado
    {
        public int IDUsuario { get; set; }
        public string NomeUsuario { get; set; }

        public int IDTipoAcesso { get; set; }
        public string TipoAcesso { get; set; }

        public DateTime Criacao { get; set; }
        public DateTime UltimoLogin { get; set; }

        public int IDCliente { get; set; }
        public string NomeCliente { get; set; }
    }

    [Authorize]
    public partial class ManutencaoController
    {
        public ActionResult UsuariosSemLogar()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // resultado da busca
            List<UsuarioSemLogar_Resultado> listaResultado = new List<UsuarioSemLogar_Resultado>();

            // clientes
            ClientesMetodos clienteMetodos = new ClientesMetodos();

            // le tipos 
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();

            // usuarios sem logar
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            List<UsuarioDominio> usuarios = usuarioMetodos.ListarPorQuemNaoLogou();

            if (usuarios != null)
            {
                foreach (UsuarioDominio usuario in usuarios)
                {
                    // tipos acesso
                    List<ListaTiposDominio> listatipos = listatiposMetodos.ListarTodos("TipoAcesso");
                    ListaTiposDominio tipoacesso = listatipos.Find(item => item.ID == usuario.IDTipoAcesso);

                    // le cliente
                    ClientesDominio cliente = new ClientesDominio();

                    // verifica se tem cliente
                    if (usuario.IDCliente > 0)
                    {
                        // le cliente
                        cliente = clienteMetodos.ListarPorId(usuario.IDCliente);

                        if (cliente == null)
                        {
                            cliente = new ClientesDominio();
                            cliente.Fantasia = "---";
                        }
                    }
                    else
                    {
                        cliente.Fantasia = "---";
                    }

                    UsuarioSemLogar_Resultado resultado = new UsuarioSemLogar_Resultado();

                    resultado.IDUsuario = usuario.IDUsuario;
                    resultado.NomeUsuario = usuario.NomeUsuario;

                    resultado.TipoAcesso = tipoacesso.Descricao;
                    resultado.IDTipoAcesso = usuario.IDTipoAcesso;

                    resultado.Criacao = usuario.Criacao;
                    resultado.UltimoLogin = usuario.UltimoLogin;

                    resultado.IDCliente = usuario.IDCliente;
                    resultado.NomeCliente = cliente.Fantasia;

                    listaResultado.Add(resultado);
                }
            }

            // resultado busca
            ViewBag.listaResultado = listaResultado;

            return View();
        }
    }
}