﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ManutencaoController
    {
        public ActionResult Gateways_Falha()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // caminho Recebidos
            string CaminhoRecebidos = ConfigurationManager.AppSettings["Recebidos"];

            // numero de arquivos em Recebidos
            var list = Directory.GetFiles(CaminhoRecebidos, "*.*");
            ViewBag.ArquivosRecebidos = list.Length;

            // gateway em atraso
            int GatewaysAtraso_Total = 0;
            int GatewaysAtraso_3dias = 0;
            int GatewaysAtraso_3_7dias = 0;
            int GatewaysAtraso_7_23dias = 0;
            int GatewaysAtraso_23dias = 0;
            int GatewaysRelogioErrado = 0;
            int GatewaysFaltaArquivo = 0;

            // leio lista de supervisao gateways
            SupervGatewaysMetodos gatewaysMetodos = new SupervGatewaysMetodos();
            List<SupervGatewaysDominio> GatewaysAtrasoList = new List<SupervGatewaysDominio>();
            List<SupervGatewaysDominio> GatewaysSupervListTodos = new List<SupervGatewaysDominio>();
            GatewaysSupervListTodos = gatewaysMetodos.ListarTodos();

            // verifica se existe
            if (GatewaysSupervListTodos != null)
            {
                // analiso os atrasos
                foreach (SupervGatewaysDominio gateway in GatewaysSupervListTodos)
                {
                    // verifico gateway se esta em atraso pelo tempo de envio configurado
                    bool gateway_em_atraso = false;

                    // data e hora
                    DateTime data_hora_hoje = DateTime.Now;
                    DateTime data_hora_atualizacao = gateway.DataHora;

                    // verifica status
                    switch (gateway.IDTipoTempo)
                    {
                        case TIPO_TEMPO_GATEWAY.GatewayBloqueada:                       // 0 - gateway bloqueada
                        case TIPO_TEMPO_GATEWAY.Ignorar_StartUpNaoRealizado:            // 1 - ignorar - start up nao realizado
                        case TIPO_TEMPO_GATEWAY.Ignorar_PendenciaCliente:               // 2 - ignorar - pendencia do cliente
                        case TIPO_TEMPO_GATEWAY.SCDE:                                   // 3 - ignorar - SCDE

                            gateway_em_atraso = false;
                            break;

                        case TIPO_TEMPO_GATEWAY.Envio_15minutos:                        // 12 - a cada 15 minutos

                            // verifica se atraso maior que 6 horas
                            if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(6, 0, 0))
                            {
                                // gateway em atraso
                                gateway_em_atraso = true;
                            }

                            break;

                        case TIPO_TEMPO_GATEWAY.Envio_hora:                             // 13 - a cada hora

                            // verifica se atraso maior que 6 horas
                            if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(6, 0, 0))
                            {
                                // gateway em atraso
                                gateway_em_atraso = true;
                            }

                            break;

                        case TIPO_TEMPO_GATEWAY.Envio_diariamente:                      // 14 - diariamente

                            // verifica se atraso maior que 2 dias
                            if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(48, 0, 0))
                            {
                                // gateway em atraso
                                gateway_em_atraso = true;
                            }

                            break;

                        case TIPO_TEMPO_GATEWAY.Envio_semanalmente:                      // 15 - semanalmente

                            // verifica se atraso maior que 8 dias
                            if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(192, 0, 0))
                            {
                                // gateway em atraso
                                gateway_em_atraso = true;
                            }

                            break;

                        case TIPO_TEMPO_GATEWAY.Envio_mensalmente:                      // 16 - diariamente

                            // verifica se atraso maior que 32 dias
                            if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(768, 0, 0))
                            {
                                // gateway em atraso
                                gateway_em_atraso = true;
                            }

                            break;
                    }


                    // diferenca
                    TimeSpan diferenca = DateTime.Now - gateway.DataHora;

                    // verifica se gateway em atraso
                    if (gateway_em_atraso)
                    {
                        // copia 
                        SupervGatewaysDominio gateway_novo = new SupervGatewaysDominio(gateway);

                        // total
                        GatewaysAtraso_Total++;

                        // verifica se menor que 3 dias
                        if (diferenca.Days < 3)
                        {
                            gateway_novo.TipoAtraso = 1;
                            GatewaysAtraso_3dias++;
                        }

                        // verifica se 3 a 7 dias
                        if (diferenca.Days >= 3 && diferenca.Days < 7)
                        {
                            gateway_novo.TipoAtraso = 2;
                            GatewaysAtraso_3_7dias++;
                        }

                        // verifica se 7 a 23 dias
                        if (diferenca.Days >= 7 && diferenca.Days < 23)
                        {
                            gateway_novo.TipoAtraso = 3;
                            GatewaysAtraso_7_23dias++;
                        }

                        // verifica se maior que 23 dias
                        if (diferenca.Days >= 23)
                        {
                            gateway_novo.TipoAtraso = 4;
                            GatewaysAtraso_23dias++;
                        }

                        // inclui gateway
                        GatewaysAtrasoList.Add(gateway_novo);
                    }
                    else
                    {
                        // le ultimo evento
                        EV_Metodos eventosMetodos = new EV_Metodos();
                        EV_Dominio ultimo_evento = eventosMetodos.ListarMaisRecente(gateway.IDCliente, gateway.IDGateway);

                        // verifica se existe
                        if (ultimo_evento != null)
                        {
                            // copia 
                            SupervGatewaysDominio gateway_novo = new SupervGatewaysDominio(gateway);

                            // diferenca
                            TimeSpan diferenca_evento = DateTime.Now - ultimo_evento.DataHora;

                            // verifica se atraso maior que 2 dias
                            if (diferenca_evento.Days > 2)
                            {
                                gateway_novo.TipoAtraso = 6;
                                GatewaysFaltaArquivo++;

                                // inclui gateway
                                GatewaysAtrasoList.Add(gateway_novo);
                            }
                        }
                    }
                }

                // analiso relogio errado
                foreach (SupervGatewaysDominio gateway in GatewaysSupervListTodos)
                {
                    // diferenca
                    TimeSpan diferenca = gateway.DataHora - gateway.DataEq;

                    // verifica se diferenca maior que 12 horas
                    if (Math.Abs(diferenca.TotalHours) >= 12)
                    {
                        // copia 
                        SupervGatewaysDominio gateway_novo = new SupervGatewaysDominio(gateway);

                        gateway_novo.TipoAtraso = 5;
                        GatewaysRelogioErrado++;

                        // inclui gateway
                        GatewaysAtrasoList.Add(gateway_novo);
                    }
                }
            }

            // lista degateways em atraso
            ViewBag.GatewaysAtrasoList = GatewaysAtrasoList;

            // gateways em atraso
            ViewBag.GatewaysAtraso = GatewaysAtraso_Total;
            ViewBag.GatewaysAtraso_3dias = GatewaysAtraso_3dias;
            ViewBag.GatewaysAtraso_3_7dias = GatewaysAtraso_3_7dias;
            ViewBag.GatewaysAtraso_7_23dias = GatewaysAtraso_7_23dias;
            ViewBag.GatewaysAtraso_23dias = GatewaysAtraso_23dias;

            ViewBag.GatewaysRelogioErrado = GatewaysRelogioErrado;
            ViewBag.GatewaysFaltaArquivo = GatewaysFaltaArquivo;

            return View(GatewaysAtrasoList);
        }

        public ActionResult Gateways_StartUp()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // leio lista de supervisao gateways - sem startup
            SupervGatewaysMetodos gatewaysMetodos = new SupervGatewaysMetodos();
            List<SupervGatewaysDominio> GatewaysAtualizaList = new List<SupervGatewaysDominio>();
            List<SupervGatewaysDominio> GatewaysSupervListTodos = gatewaysMetodos.ListarTodos(1);

            // verifica se existe
            if (GatewaysSupervListTodos != null)
            {
                // analiso os atrasos
                foreach (SupervGatewaysDominio gateway in GatewaysSupervListTodos)
                {
                    // copia 
                    SupervGatewaysDominio gateway_novo = new SupervGatewaysDominio(gateway);
                    gateway_novo.TipoAtraso = 0;

                    // diferenca
                    TimeSpan diferenca = DateTime.Now - gateway.DataHora;

                    // verifica se atualizou a 2 dias
                    if (diferenca.Days <= 2)
                    {
                        // atualizou
                        gateway_novo.TipoAtraso = 1;
                    }

                    // inclui gateway
                    GatewaysAtualizaList.Add(gateway_novo);
                }
            }

            // lista de gateways atualizadas
            ViewBag.GatewaysAtualizaList = GatewaysAtualizaList;

            return View(GatewaysAtualizaList);
        }

        public ActionResult Gateways_Pendencia()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // leio lista de supervisao gateways - pendencia do cliente
            SupervGatewaysMetodos gatewaysMetodos = new SupervGatewaysMetodos();
            List<SupervGatewaysDominio> GatewaysSupervListTodos = gatewaysMetodos.ListarTodos(2);

            return View(GatewaysSupervListTodos);
        }

        public ActionResult Gateways_Bloqueada()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // leio lista de supervisao gateways - gateways bloqueadas
            SupervGatewaysMetodos gatewaysMetodos = new SupervGatewaysMetodos();
            List<SupervGatewaysDominio> GatewaysSupervListTodos = gatewaysMetodos.ListarTodos(0);

            return View(GatewaysSupervListTodos);
        }


        // limpas gateways inexistentes na supervisão
        public ActionResult LimparInexistentes_SupervisaoGateway()
        {
            // le cookies
            LeCookies_SmartEnergy();

            return View();
        }

        // GET: Apaga as gateways que não existem
        public ActionResult Excluir_SupervisaoGateway()
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // le cookies
            LeCookies_SmartEnergy();

            // leio lista de gateways
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            List<GatewaysDominio> gateways = gatewaysMetodos.ListarTodas_Gateways();

            // leio lista de supervisao gateways
            SupervGatewaysMetodos gatewaysSupMetodos = new SupervGatewaysMetodos();
            List<SupervisaoGatewayDominio> gatewaysSuperv = gatewaysSupMetodos.ListarTodos_SupervisaoGateway();

            int num_gateways_excluidas = 0;

            // percorre gateways supervisão
            foreach(SupervisaoGatewayDominio gatewaySuperv in gatewaysSuperv)
            {
                bool existe_gateway = false;

                // percorre gateways configuradas
                foreach(GatewaysDominio gateway in gateways)
                {
                    // verifica se existe
                    if (gateway.IDGateway == gatewaySuperv.IDGateway)
                    {
                        // achou
                        existe_gateway = true;
                        break;
                    }
                }

                // verifica se não achou
                if (!existe_gateway)
                {
                    // apaga a gateway da supervisão
                    num_gateways_excluidas++;
                    gatewaysSupMetodos.ExcluirTodosIDGateway(gatewaySuperv.IDGateway);
                }
            }

            // retorna status
            returnedData = new
            {
                status = "OK",
                erro = string.Format("Excluídas {0} gateways", num_gateways_excluidas)
            };

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }
    }
}