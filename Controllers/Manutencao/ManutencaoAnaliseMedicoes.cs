﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ManutencaoController
    {
        public ActionResult AnaliseMedicoes(int IDCliente)
        {
            // salva cookie cliente
            CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);

            // le cookies
            LeCookies_SmartEnergy();

            // leio configuracoes
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cliente = clienteMetodos.ListarPorId(IDCliente);
            ViewBag.NomeCliente = cliente.Fantasia;

            // le cookie datahora
            DateTime DataIni = CookieStore.LeCookie_Datahora("Manut_DataIni");
            DateTime DataFim = CookieStore.LeCookie_Datahora("Manut_DataFim");

            if( DataIni.Year == 2000)
            {
                DataIni = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 0, 0, 0);
                DataFim = DataIni;
            }

            string data_ini = String.Format("{0:d}", DataIni);
            string hora_ini = String.Format("{0:t}", DataIni);
            string data_fim = String.Format("{0:d}", DataFim);
            string hora_fim = String.Format("{0:t}", DataFim);

            ViewBag.DataIni = data_ini;
            ViewBag.HoraIni = hora_ini;
            ViewBag.DataFim = data_fim;
            ViewBag.HoraFim = hora_fim;

            // medicoes com falta de registros
            List<MedicoesDominio> medicoesFalha = new List<MedicoesDominio>();
            ViewBag.MedicoesFalha = medicoesFalha;

            return View();
        }

        // GET: Analise Medicao - Resultado operacao
        public PartialViewResult _AnaliseMedicoes_Resultado(string DataIni, string DataFim)
        {
            // pega data
            DateTime dateValueIni = DateTime.Parse(DataIni);
            DateTime dateValueFim = DateTime.Parse(DataFim);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Manut_DataIni", dateValueIni);
            CookieStore.SalvaCookie_Datahora("Manut_DataFim", dateValueFim);

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;

            // medicoes com falta de registros
            List<MedicoesDominio> medicoesFalha = new List<MedicoesDominio>();

            // calcula numero de registros alvo
            TimeSpan diferenca = dateValueFim - dateValueIni;

            int NumRegistrosAlvo = (int)(diferenca.TotalMinutes / 15) + 1;

            // le medicoes do cliente
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicaoMetodos.ListarPorIDCliente(IDCliente);

            if( medicoes != null )
            {
                foreach(MedicoesDominio medicao in medicoes)
                {
                    // verifica se EN
                    if( medicao.IDTipoMedicao == 0 )
                    {
                        // verifica numero de registros no periodo
                        EN_Metodos enMetodos = new EN_Metodos();

                        int NumRegistros = enMetodos.NumRegistrosPeriodo(IDCliente, medicao.IDMedicao, dateValueIni, dateValueFim);

                        // verifica se diferente do alvo
                        if(NumRegistros != NumRegistrosAlvo)
                        {
                            // insere na lista
                            medicoesFalha.Add(medicao);
                        }
                    }
                }
            }

            // medicoes com falta de registros
            ViewBag.MedicoesFalha = medicoesFalha;

            return PartialView();
        }
    }
}