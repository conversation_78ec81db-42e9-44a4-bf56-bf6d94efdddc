﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ManutencaoController
    {
        public ActionResult CopiarRegistrosMedicao(int IDCliente, int IDMedicao)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // leio cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cliente = clienteMetodos.ListarPorId(IDCliente);
            ViewBag.NomeCliente = cliente.Fantasia;

            // Informacoes da medição selecionada
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // leio medições do tipo da medição selecionada
            List<MedicoesDominio> medicoes = medicoesMetodos.ListarPorIDCliente_Tipo(IDCliente, medicao.IDTipoMedicao);
            ViewBag.Medicoes = medicoes;

            // medição selecionada
            ViewBag.IDMedicao_selecionada = IDMedicao;

            return View();
        }

        // GET: Manutencao Copiar Registros
        public ActionResult Medicao_CopiarRegistros(int IDMedicao_Origem, int IDMedicao_Destino)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // copiar medicao
            Operacao_CopiarRegistrosMedicao(IDMedicao_Origem, IDMedicao_Destino);
           
            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        public bool Operacao_CopiarRegistrosMedicao(int IDMedicao_Origem, int IDMedicao_Destino)
        {

            // verifica se é a mesma medição
            if (IDMedicao_Origem == IDMedicao_Destino)
            {
                // retorna status
                return false;
            }

            //
            // Informacoes da MEDICAO
            //
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            MedicoesDominio medicao_origem = medicoesMetodos.ListarPorId(IDMedicao_Origem);
            MedicoesDominio medicao_destino = medicoesMetodos.ListarPorId(IDMedicao_Destino);

            if (medicao_origem == null || medicao_destino == null)
            {
                // retorna status
                return false;
            }

            // verifica se medicao EN
            if (medicao_origem.IDTipoMedicao == TIPO_MEDICAO.ENERGIA && medicao_destino.IDTipoMedicao == TIPO_MEDICAO.ENERGIA)
            {
                // copia registros
                EN_Metodos enMetodos = new EN_Metodos();
                enMetodos.CopiarRegistros(medicao_origem.IDCliente, IDMedicao_Origem, IDMedicao_Destino);

                // verifica se medição principal e gateways diferentes
                if (medicao_origem.IDCategoriaMedicao == CATEGORIA_MEDICAO.PRINCIPAL && medicao_origem.IDGateway != medicao_destino.IDGateway)
                {
                    // gateways
                    GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
                    GatewaysDominio gateway_origem = gatewaysMetodos.ListarPorId(medicao_origem.IDGateway);
                    GatewaysDominio gateway_destino = gatewaysMetodos.ListarPorId(medicao_destino.IDGateway);

                    if (gateway_origem == null || gateway_destino == null)
                    {
                        // retorna status
                        return false;
                    }

                    // verifica se é o mesmo modelo
                    if (gateway_origem.IDTipoGateway == gateway_destino.IDTipoGateway)
                    {
                        EV_Metodos evMetodos = new EV_Metodos();
                        evMetodos.CopiarRegistros(medicao_origem.IDCliente, medicao_origem.IDGateway, medicao_destino.IDGateway);
                    }
                }
            }

            // verifica se medicao GG
            if ((medicao_origem.IDTipoMedicao == TIPO_MEDICAO.UTILIDADES && medicao_destino.IDTipoMedicao == TIPO_MEDICAO.UTILIDADES) ||
                 (medicao_origem.IDTipoMedicao == TIPO_MEDICAO.ENTRADA_ANALOGICA && medicao_destino.IDTipoMedicao == TIPO_MEDICAO.ENTRADA_ANALOGICA) ||
                 (medicao_origem.IDTipoMedicao == TIPO_MEDICAO.CICLOMETRO && medicao_destino.IDTipoMedicao == TIPO_MEDICAO.CICLOMETRO))
            {
                // copia registros
                GG_Metodos ggMetodos = new GG_Metodos();
                ggMetodos.CopiarRegistros(medicao_origem.IDCliente, IDMedicao_Origem, IDMedicao_Destino);
            }

            // retorna status
            return false;
        }
    }
}