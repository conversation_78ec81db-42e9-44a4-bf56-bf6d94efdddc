﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ManutencaoController
    {
        // Listas utilizadas
        private void PreparaListas_Medicao(int IDCliente)
        {
            // le tipos medicao
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposMedicao = listatiposMetodos.ListarTodos("TipoMedicao");
            ViewBag.listaTipoMedicao = listatiposMedicao;

            // le unidades
            UnidadesMetodos unidadesMetodos = new UnidadesMetodos();
            List<UnidadesDominio> unidades = unidadesMetodos.ListarPorIDCliente(IDCliente);
            ViewBag.listaUnidades = unidades;

            // gateways
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            List<GatewaysDominio> gateways = gatewaysMetodos.ListarPorIDCliente(IDCliente);
            ViewBag.listaGateways = gateways;

            return;
        }

        // GET: Manutencao Medicoes
        public ActionResult Medicoes(int IDCliente)
        {

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Manut_Medicoes");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_Medicao(IDCliente);

            // le medicoes
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            List<MedicoesDominio> listaMedicoes = medicoesMetodos.ListarTodos(IDCliente, ViewBag._ConfigMed);
            return View(listaMedicoes);
        }
    }
}