﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ManutencaoController
    {
        public ActionResult Constante(int IDCliente, int IDMedicao)
        {
            // le configuracao da medicao
            MedicoesMetodos medicoesMetodos_conf = new MedicoesMetodos();
            MedicoesDominio medicao_conf = medicoesMetodos_conf.ListarPorId(IDMedicao);

            // salva cookie 
            CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", medicao_conf.IDTipoMedicao);
            CookieStore.SalvaCookie_Int("_IDGateway", medicao_conf.IDGateway);
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Manut_Constante");

            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            IDCliente = ViewBag._IDCliente;
            IDMedicao = ViewBag._IDMedicao;
            int IDTipoMedicao = ViewBag._IDTipoMedicao;

            // leio configuracoes
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cliente = clienteMetodos.ListarPorId(IDCliente);
            ViewBag.NomeCliente = cliente.Fantasia;

            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);
            ViewBag.NomeMedicao = medicao.Nome;

            // verifica se EN
            if (IDTipoMedicao == 0)
            {
                // leio historico das constantes
                EN_K_Metodos constantesMetodos = new EN_K_Metodos();
                List<EN_K_Dominio> constantesListTodos = constantesMetodos.ListarTodos(IDCliente, IDMedicao);
                ViewBag.ListaConstantes = constantesListTodos;
            }
            else
            {
                ViewBag.ListaConstantes = null;
            }

            // le cookie datahora
            DateTime DataIni = CookieStore.LeCookie_Datahora("Manut_DataIni");
            DateTime DataFim = CookieStore.LeCookie_Datahora("Manut_DataFim");

            if( DataIni.Year == 2000)
            {
                DataIni = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 0, 0, 0);
                DataFim = DataIni;
            }

            string data_ini = String.Format("{0:d}", DataIni);
            string hora_ini = String.Format("{0:t}", DataIni);
            string data_fim = String.Format("{0:d}", DataFim);
            string hora_fim = String.Format("{0:t}", DataFim);

            ViewBag.DataIni = data_ini;
            ViewBag.HoraIni = hora_ini;
            ViewBag.DataFim = data_fim;
            ViewBag.HoraFim = hora_fim;

            double valor = 1.0;
            ViewBag.Valor = valor;

            double operacao = 0;
            ViewBag.Operacao = operacao;

            return View();
        }

        // GET: Constante - Executar operacao
        public ActionResult Constante_Executar(string DataIni, string DataFim, int operacao, string valor)
        {
            // pega data
            DateTime dateValueIni = DateTime.Parse(DataIni);
            DateTime dateValueFim = DateTime.Parse(DataFim);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Manut_DataIni", dateValueIni);
            CookieStore.SalvaCookie_Datahora("Manut_DataFim", dateValueFim);

            // pega valor
            double Valor = double.Parse(valor);

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;
            int IDTipoMedicao = ViewBag._IDTipoMedicao;
            int IDGateway = ViewBag._IDGateway;

            // verifica se EN
            if( IDTipoMedicao == 0 )
            {
                // executa operacao
                EN_Metodos enMetodos = new EN_Metodos();
                enMetodos.Operacao(IDCliente, IDMedicao, dateValueIni, dateValueFim, operacao, Valor);
            }

            // verifica se GG
            if (IDTipoMedicao == 2 || IDTipoMedicao == 3)
            {
                // executa operacao
                GG_Metodos ggMetodos = new GG_Metodos();
                ggMetodos.Operacao(IDCliente, IDMedicao, dateValueIni, dateValueFim, operacao, Valor);
            }

            // evento constante
            UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.MANUT_CONSTANTE, IDMedicao, operacao);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}