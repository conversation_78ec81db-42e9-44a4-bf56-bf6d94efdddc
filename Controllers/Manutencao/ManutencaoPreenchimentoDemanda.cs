﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ManutencaoController
    {
        public ActionResult PreenchimentoDemanda(int IDCliente, int IDMedicao)
        {
            // le configuracao da medicao
            MedicoesMetodos medicoesMetodos_conf = new MedicoesMetodos();
            MedicoesDominio medicao_conf = medicoesMetodos_conf.ListarPorId(IDMedicao);

            // salva cookie 
            CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", medicao_conf.IDTipoMedicao);
            CookieStore.SalvaCookie_Int("_IDGateway", medicao_conf.IDGateway);
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Manut_PreenchimentoDemanda");

            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            IDCliente = ViewBag._IDCliente;
            IDMedicao = ViewBag._IDMedicao;

            // leio configuracoes
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cliente = clienteMetodos.ListarPorId(IDCliente);
            ViewBag.NomeCliente = cliente.Fantasia;

            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);
            ViewBag.NomeMedicao = medicao.Nome;

            // le cookie datahora
            DateTime DataIni = CookieStore.LeCookie_Datahora("Manut_DataIni");
            DateTime DataFim = CookieStore.LeCookie_Datahora("Manut_DataFim");

            if( DataIni.Year == 2000)
            {
                DataIni = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 0, 0, 0);
                DataFim = DataIni;
            }

            string data_ini = String.Format("{0:d}", DataIni);
            string hora_ini = String.Format("{0:t}", DataIni);
            string data_fim = String.Format("{0:d}", DataFim);
            string hora_fim = String.Format("{0:t}", DataFim);

            ViewBag.DataIni = data_ini;
            ViewBag.HoraIni = hora_ini;
            ViewBag.DataFim = data_fim;
            ViewBag.HoraFim = hora_fim;

            ViewBag.DataBase = data_ini;

            ViewBag.RegistrosBase = null;
            ViewBag.RegistrosPreencher = null;

            return View();
        }

        // GET: Preenchimento Demanda - Mostrar registros
        public PartialViewResult _PreenchimentoDemanda_Mostrar(string DataIni, string DataFim, string DataBase)
        {
            // pega data
            DateTime dateValueIni = DateTime.Parse(DataIni);
            DateTime dateValueFim = DateTime.Parse(DataFim);
            DateTime dateValueBase = DateTime.Parse(DataBase);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Manut_DataIni", dateValueIni);
            CookieStore.SalvaCookie_Datahora("Manut_DataFim", dateValueFim);

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;
            int IDGateway = ViewBag._IDGateway;

            // acerta data base inicio
            DateTime DataBaseIni = new DateTime(dateValueBase.Year, dateValueBase.Month, dateValueBase.Day, dateValueIni.Hour, dateValueIni.Minute, 0);

            // calcula numero de dias entre data base e inicio
            TimeSpan NumeroDias = DataBaseIni - dateValueIni;

            // acerta data base fim
            DateTime DataBaseFim = new DateTime(dateValueFim.Year, dateValueFim.Month, dateValueFim.Day, dateValueFim.Hour, dateValueFim.Minute, 0);
            DataBaseFim = DataBaseFim.AddDays(NumeroDias.Days);

            // le registros base
            EN_Metodos enMetodos = new EN_Metodos();
            List<EN_Dominio> registrosBase = enMetodos.ListarTodosPeriodo(IDCliente, IDMedicao, DataBaseIni, DataBaseFim);
            ViewBag.RegistrosBase = registrosBase;

            // le registros preencher
            List<EN_Dominio> registrosPreencher = enMetodos.ListarTodosPeriodo(IDCliente, IDMedicao, dateValueIni, dateValueFim);
            ViewBag.RegistrosPreencher = registrosPreencher;

            return PartialView();
        }

        // GET: Preenchimento Demanda - Executar operacao
        public ActionResult PreenchimentoDemanda_Executar(string DataIni, string DataFim, string DataBase)
        {
            // pega data
            DateTime dateValueIni = DateTime.Parse(DataIni);
            DateTime dateValueFim = DateTime.Parse(DataFim);
            DateTime dateValueBase = DateTime.Parse(DataBase);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Manut_DataIni", dateValueIni);
            CookieStore.SalvaCookie_Datahora("Manut_DataFim", dateValueFim);

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;
            int IDGateway = ViewBag._IDGateway;

            // executa operacao
            EN_Metodos enMetodos = new EN_Metodos();
            enMetodos.Preenchimento(IDCliente, IDMedicao, dateValueIni, dateValueFim, dateValueBase);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}