﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public class ClientesSemLogar_Resultado
    {
        public int IDCliente { get; set; }
        public string NomeCliente { get; set; }

        public int IDConsultor { get; set; }
        public string NomeConsultor { get; set; }

        public int NumUsuarios { get; set; }
        public DateTime UltimoLogin { get; set; }

        public bool EmailGerenciamento { get; set; }
        public bool EmailRelatorio { get; set; }
    }

    [Authorize]
    public partial class ManutencaoController
    {
        public ActionResult ClientesSemLogar()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // resultado da busca
            List<ClientesSemLogar_Resultado> listaResultado = new List<ClientesSemLogar_Resultado>();

            // usuarios 
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();

            // clientes
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> clientes = clienteMetodos.ListarTodos();

            if (clientes != null)
            {
                foreach (ClientesDominio cliente in clientes)
                {
                    // numero de usuarios do cliente
                    int num_usuarios = usuarioMetodos.NumUsuarios(cliente.IDCliente);

                    // usuarios sem logar
                    List<UsuarioDominio> usuarios = usuarioMetodos.ListarPorQuemNaoLogou(cliente.IDCliente);

                    if( usuarios != null )
                    {
                        // verifica se todos os usuarios nao se logaram
                        if (usuarios.Count > 0 && num_usuarios == usuarios.Count)
                        {
                            ClientesSemLogar_Resultado resultado = new ClientesSemLogar_Resultado();

                            resultado.IDCliente = cliente.IDCliente;
                            resultado.NomeCliente = cliente.Fantasia;

                            resultado.IDConsultor = cliente.IDConsultor;
                            resultado.NomeConsultor = "---";

                            if (cliente.IDConsultor > 0)
                            {
                                UsuarioDominio consultor = usuarioMetodos.ListarPorId(cliente.IDConsultor);

                                if (consultor != null)
                                {
                                    resultado.NomeConsultor = consultor.NomeUsuario;
                                }
                            }

                            resultado.NumUsuarios = usuarios.Count;
                            resultado.UltimoLogin = usuarios[0].UltimoLogin;

                            // verifica se recebe email de gerenciamento ou relatorio
                            bool EmailGerenciamento = false;
                            bool EmailRelatorio = false;

                            foreach(UsuarioDominio usuario in usuarios)
                            {
                                // verifica se recebe email de gerenciamento
                                if( usuario.MsgCliEve == 1 || usuario.MsgCliMed_Dem == 1 || usuario.MsgCliMed_Cons == 1 || usuario.MsgCliMed_FatPot == 1 || usuario.MsgCliEve_FEner == 1 || usuario.MsgCliEve_RepDem == 1 || usuario.MsgCliEve_Alarm == 1 )
                                {
                                    EmailGerenciamento = true;
                                }

                                // verifica se recebe email de relatorio
                                if( usuario.Diario == 1 || usuario.Semanal == 1 || usuario.Mensal == 1 )
                                {
                                    EmailRelatorio = true;
                                }
                            }

                            resultado.EmailGerenciamento = EmailGerenciamento;
                            resultado.EmailRelatorio = EmailRelatorio;

                            listaResultado.Add(resultado);
                        }
                    }
                }
            }

            // resultado busca
            ViewBag.listaResultado = listaResultado;

            return View();
        }
    }
}