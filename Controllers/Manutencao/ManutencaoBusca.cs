﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public class Busca_Resultado
    {
        public int ID { get; set; }
        public string Nome { get; set; }

        public int IDCliente { get; set; }
        public string NomeCliente { get; set; }

        public int MedicaoInterna { get; set; }

        public string NomeUsuario { get; set; }
        public string Apelido { get; set; }
        public string Login { get; set; }
        public string Senha { get; set; }
        public string Email { get; set; }
        public int IDTipoAcesso { get; set; }

        public string Texto1 { get; set; }
        public string Texto2 { get; set; }
        public string Texto3 { get; set; }
        public string Texto4 { get; set; }
        public string Texto5 { get; set; }
    }

    [Authorize]
    public partial class ManutencaoController
    {
        public ActionResult Busca()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // sem busca
            ViewBag.IDBusca = -1;

            return View();
        }

        public PartialViewResult _Busca_Resultado(int IDBusca, string PalavraBusca, int MedicaoInterna = -10)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // sem busca
            ViewBag.IDBusca = IDBusca;

            // resultado da busca
            List<Busca_Resultado> listaResultado = new List<Busca_Resultado>();

            // clientes
            ClientesMetodos clienteMetodos = new ClientesMetodos();

            // le tipos medicao
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();

            // tipo da busca
            switch(IDBusca)
            {
                case 0:     // cliente

                    // le clientes
                    List<ClientesDominio> clientes = clienteMetodos.Buscar(PalavraBusca);

                    if( clientes != null)
                    {
                        foreach(ClientesDominio cliente in clientes)
                        {
                            Busca_Resultado resultado = new Busca_Resultado();

                            resultado.ID = cliente.IDCliente;
                            resultado.Nome = cliente.Fantasia;
                            resultado.IDCliente = cliente.IDCliente;
                            resultado.NomeCliente = cliente.Fantasia;
                            resultado.MedicaoInterna = 0;
                            resultado.Texto1 = cliente.Nome;
                            resultado.Texto2 = "";
                            resultado.Texto3 = "";
                            resultado.Texto4 = "";
                            resultado.Texto5 = "";

                            listaResultado.Add(resultado);
                        }
                    }

                    break;

                case 1:     // gateway

                    // le gateways
                    SupervGatewaysMetodos gatewayMetodos = new SupervGatewaysMetodos();
                    List<SupervGatewaysDominio> gateways = gatewayMetodos.Buscar(PalavraBusca);

                    if (gateways != null)
                    {
                        foreach (SupervGatewaysDominio gateway in gateways)
                        {
                            Busca_Resultado resultado = new Busca_Resultado();

                            resultado.ID = gateway.IDGateway;
                            resultado.Nome = gateway.Nome;
                            resultado.IDCliente = gateway.IDCliente;
                            resultado.NomeCliente = gateway.Fantasia;
                            resultado.MedicaoInterna = 0;
                            resultado.Texto1 = gateway.StatusEq;
                            resultado.Texto2 = gateway.ICC;
                            resultado.Texto3 = gateway.IMEI;
                            resultado.Texto4 = gateway.MOD;
                            resultado.Texto5 = gateway.GSM_TEC;

                            listaResultado.Add(resultado);
                        }
                    }

                    break;

                case 2:     // medicao
                case 3:     // medicao - gateway

                    // le tipos medicao
                    List<ListaTiposDominio> listatiposMedicao = listatiposMetodos.ListarTodos("TipoMedicao");
                    ViewBag.listaTipoMedicao = listatiposMedicao;

                    // le medicoes
                    MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                    List<MedicoesDominio> medicoes = new List<MedicoesDominio>();

                    if( IDBusca == 2)
                    {
                        medicoes = medicaoMetodos.Buscar(PalavraBusca);
                    }
                    else
                    {
                        // IDGateway
                        int IDGateway = 0;
                        bool eh_numero = int.TryParse(PalavraBusca, out IDGateway);

                        if( !eh_numero)
                        {
                            IDGateway = -10;
                        }

                        medicoes = medicaoMetodos.BuscarGateway(IDGateway, MedicaoInterna);
                    }

                    if (medicoes != null)
                    {
                        foreach (MedicoesDominio medicao in medicoes)
                        {
                            // le cliente
                            ClientesDominio cliente = clienteMetodos.ListarPorId(medicao.IDCliente);

                            Busca_Resultado resultado = new Busca_Resultado();

                            resultado.ID = medicao.IDMedicao;
                            resultado.Nome = medicao.Nome;
                            resultado.MedicaoInterna = medicao.NumMedGateway;
                            resultado.IDCliente = medicao.IDCliente;
                            resultado.NomeCliente = cliente.Fantasia;
                            resultado.Texto1 = String.Format("{0}", medicao.IDGateway);
                            resultado.Texto2 = String.Format("{0}", medicao.IDTipoMedicao);
                            resultado.Texto3 = "";
                            resultado.Texto4 = "";
                            resultado.Texto5 = "";

                            listaResultado.Add(resultado);
                        }
                    }

                    break;

                case 4:     // usuario

                    // le usuarios
                    UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
                    List<UsuarioDominio> usuarios = usuarioMetodos.Buscar(PalavraBusca);

                    if (usuarios != null)
                    {
                        foreach (UsuarioDominio usuario in usuarios)
                        {
                            // tipos acesso
                            List<ListaTiposDominio> listatipos = listatiposMetodos.ListarTodos("TipoAcesso");
                            ListaTiposDominio tipoacesso = listatipos.Find(item => item.ID == usuario.IDTipoAcesso);

                            // prepara resultado
                            Busca_Resultado resultado = new Busca_Resultado();

                            resultado.ID = usuario.IDUsuario;
                            resultado.Nome = usuario.NomeUsuario;
                            resultado.IDCliente = usuario.IDCliente;
                            resultado.NomeCliente = "---";
                            resultado.MedicaoInterna = 0;
                            resultado.Texto1 = tipoacesso.Descricao;
                            resultado.Texto2 = String.Format("{0}", usuario.IDTipoAcesso);
                            resultado.Texto3 = "";
                            resultado.Texto4 = "";
                            resultado.Texto5 = "";

                            resultado.NomeUsuario = usuario.NomeUsuario;
                            resultado.Apelido = usuario.Apelido;
                            resultado.Login = usuario.Login;
                            resultado.Senha = usuario.Senha;
                            resultado.Email = usuario.Email;
                            resultado.IDTipoAcesso = usuario.IDTipoAcesso;

                            // verifica se admin
                            if( tipoacesso.ID == TIPO_ACESSO.GESTAL_ADMIN || 
                                tipoacesso.ID == TIPO_ACESSO.GESTAL_PRODUCAO || 
                                tipoacesso.ID == TIPO_ACESSO.GESTAL_SUPORTE || 
                                tipoacesso.ID == TIPO_ACESSO.GESTAL_VENDAS )
                            {
                                resultado.NomeCliente = "GESTAL Administrador";
                            }

                            if (tipoacesso.ID == TIPO_ACESSO.DEMONSTRACAO)
                            {
                                resultado.NomeCliente = "Demonstração";
                            }

                            // verifica se cliente
                            if (tipoacesso.ID == TIPO_ACESSO.CLIENTE_ADMIN || 
                                tipoacesso.ID == TIPO_ACESSO.CLIENTE_OPER )
                            {
                                // le cliente
                                ClientesDominio cliente = new ClientesDominio();

                                // verifica se tem cliente
                                if (usuario.IDCliente > 0)
                                {
                                    // le cliente
                                    cliente = clienteMetodos.ListarPorId(usuario.IDCliente);

                                    if (cliente == null)
                                    {
                                        cliente = new ClientesDominio();
                                        cliente.Fantasia = "---";
                                    }
                                }
                                else
                                {
                                    cliente.Fantasia = "---";
                                }

                                resultado.NomeCliente = cliente.Fantasia;
                            }

                            // verifica se consultor
                            if (tipoacesso.ID == TIPO_ACESSO.CONSULTOR || 
                                tipoacesso.ID == TIPO_ACESSO.CONSULTOR_ADMIN || 
                                tipoacesso.ID == TIPO_ACESSO.CONSULTOR_OPER )
                            {
                                resultado.NomeCliente = "Gestor";

                                // verifica se tem cliente
                                if (usuario.IDCliente > 0)
                                {
                                    UsuarioDominio consultor = usuarioMetodos.ListarPorId(usuario.IDCliente);

                                    resultado.NomeCliente = consultor.NomeUsuario;
                                }
                            }

                            // coloca na lista
                            listaResultado.Add(resultado);
                        }
                    }

                    break;
            }

            // resultado busca
            ViewBag.listaResultado = listaResultado;

            return PartialView();
        }
    }
}