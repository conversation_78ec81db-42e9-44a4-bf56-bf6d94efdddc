﻿using System;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ManutencaoController
    {
        public ActionResult AlterarPeriodosMedicao(int IDCliente, int IDMedicao)
        {
            // le configuracao da medicao
            MedicoesMetodos medicoesMetodos_conf = new MedicoesMetodos();
            MedicoesDominio medicao_conf = medicoesMetodos_conf.ListarPorId(IDMedicao);

            // salva cookie 
            CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", medicao_conf.IDTipoMedicao);
            CookieStore.SalvaCookie_Int("_IDGateway", medicao_conf.IDGateway);
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Manut_AlterarPeriodos");

            // le cookies
            LeCookies_SmartEnergy();

            // leio configuracoes
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cliente = clienteMetodos.ListarPorId(IDCliente);
            ViewBag.NomeCliente = cliente.Fantasia;

            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);
            ViewBag.Medicao = medicao;

            // le cookie datahora
            DateTime DataIni = CookieStore.LeCookie_Datahora("Manut_DataIni");
            DateTime DataFim = CookieStore.LeCookie_Datahora("Manut_DataFim");

            if (DataIni.Year == 2000)
            {
                DataIni = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 0, 0, 0);
                DataFim = DataIni;
            }

            string data_ini = String.Format("{0:d}", DataIni);
            string hora_ini = String.Format("{0:t}", DataIni);
            string data_fim = String.Format("{0:d}", DataFim);
            string hora_fim = String.Format("{0:t}", DataFim);

            ViewBag.DataIni = data_ini;
            ViewBag.HoraIni = hora_ini;
            ViewBag.DataFim = data_fim;
            ViewBag.HoraFim = hora_fim;

            return View();
        }

        // GET: Manutencao Alterar Periodos
        public ActionResult Medicao_AlterarPeriodos(string DataIni, string DataFim)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDMedicao
            int IDMedicao = ViewBag._IDMedicao;

            // alterar periodos
            AlterarPeriodos(IDMedicao, DataIni, DataFim);
           
            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        public bool AlterarPeriodos(int IDMedicao, string DataIni, string DataFim)
        {

            //
            // Informacoes da MEDICAO
            //
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicoesMetodos.ListarPorId(IDMedicao);

            if (medicao == null)
            {
                // retorna status
                return false;
            }

            // data e hora
            DateTime ini = DateTime.Parse(DataIni);
            DateTime fim = DateTime.Parse(DataFim);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Manut_DataIni", ini);
            CookieStore.SalvaCookie_Datahora("Manut_DataFim", fim);

            // verifica se medicao EN
            if (medicao.IDTipoMedicao == 0)
            {
                // altera periodos Medicao
                EN_Metodos enMetodos = new EN_Metodos();
                enMetodos.AlterarPeriodos(medicao, ini, fim);
            }

            // retorna status
            return false;
        }
    }
}