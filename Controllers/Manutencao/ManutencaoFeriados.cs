﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ManutencaoController
    {
        // GET: Feriados - Historico
        public ActionResult Feriados_Historico()
        {
            // tela de ajuda - feriados
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le feriados
            FeriadosMetodos feriadosMetodos = new FeriadosMetodos();
            List<FeriadosDominio> listaFeriados = feriadosMetodos.ListarTodos();
            ViewBag.listaFeriados = listaFeriados;

            return View();
        }

        // GET: Feriados - Editar
        public ActionResult Feriados_Editar(int IDFeriado)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // verifica se adicionando
            FeriadosDominio feriado = new FeriadosDominio();
            if (IDFeriado == 0)
            {
                // zera feriado com default
                feriado.IDFeriado = 0;
                feriado.DataHora = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 0, 0);
                feriado.Descricao = "Feriado";
                feriado.Repete = false;
            }
            else
            {
                // le feriado
                FeriadosMetodos feriadosMetodos = new FeriadosMetodos();
                feriado = feriadosMetodos.ListarPorId(IDFeriado);
            }

            return View(feriado);
        }

        // POST: Feriados - Salvar
        [HttpPost]
        public ActionResult Feriados_Salvar(FeriadosDominio feriado)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro feriado com a mesma data
            FeriadosMetodos feriadosMetodos = new FeriadosMetodos();
            if (feriadosMetodos.VerificarDuplicidade(feriado))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Data existente."
                };
            }
            else
            {
                // verifica se repete
                if( feriado.Repete )
                {
                    // forca ano 2000
                    DateTime datahora = new DateTime(2000, feriado.DataHora.Month, feriado.DataHora.Day, 0, 0, 0);
                    feriado.DataHora = datahora;
                }

                // salva feriado
                feriadosMetodos.Salvar(feriado);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Feriados - Excluir
        public ActionResult Feriados_Excluir(int IDFeriado)
        {
            // apaga o feriado
            FeriadosMetodos feriadosMetodos = new FeriadosMetodos();
            feriadosMetodos.Excluir(IDFeriado);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}