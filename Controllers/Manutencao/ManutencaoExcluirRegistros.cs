﻿using System;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ManutencaoController
    {
        public ActionResult ExcluirRegistrosMedicao(int IDCliente, int IDMedicao)
        {
            // le configuracao da medicao
            MedicoesMetodos medicoesMetodos_conf = new MedicoesMetodos();
            MedicoesDominio medicao_conf = medicoesMetodos_conf.ListarPorId(IDMedicao);

            // salva cookie 
            CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", medicao_conf.IDTipoMedicao);
            CookieStore.SalvaCookie_Int("_IDGateway", medicao_conf.IDGateway);
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Manut_ExcluirRegistros");

            // le cookies
            LeCookies_SmartEnergy();

            // leio configuracoes
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cliente = clienteMetodos.ListarPorId(IDCliente);
            ViewBag.NomeCliente = cliente.Fantasia;

            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);
            ViewBag.NomeMedicao = medicao.Nome;

            // le cookie datahora
            DateTime DataIni = CookieStore.LeCookie_Datahora("Manut_DataIni");
            DateTime DataFim = CookieStore.LeCookie_Datahora("Manut_DataFim");

            if (DataIni.Year == 2000)
            {
                DataIni = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 0, 0, 0);
                DataFim = DataIni;
            }

            string data_ini = String.Format("{0:d}", DataIni);
            string hora_ini = String.Format("{0:t}", DataIni);
            string data_fim = String.Format("{0:d}", DataFim);
            string hora_fim = String.Format("{0:t}", DataFim);

            ViewBag.DataIni = data_ini;
            ViewBag.HoraIni = hora_ini;
            ViewBag.DataFim = data_fim;
            ViewBag.HoraFim = hora_fim;

            return View();
        }

        // GET: Manutencao Excluir Registros
        public ActionResult Medicao_ExcluirRegistros(string DataIni, string DataFim)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDMedicao
            int IDMedicao = ViewBag._IDMedicao;

            // apagar medicao
            ApagarRegistrosMedicao(IDMedicao, DataIni, DataFim);
           
            // evento 
            UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.EXCLUI_CONFIG, TABELA_CONFIG.MEDICAO, IDMedicao);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        public bool ApagarRegistrosMedicao(int IDMedicao, string DataIni, string DataFim)
        {

            //
            // Informacoes da MEDICAO
            //
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicoesMetodos.ListarPorId(IDMedicao);

            if (medicao == null)
            {
                // retorna status
                return false;
            }

            // apagar todos
            bool por_periodo = false;

            // data e hora
            DateTime ini = new DateTime(2000, 1, 1, 0, 0, 0);
            DateTime fim = new DateTime(2000, 1, 1, 0, 0, 0);

            if( DataIni != "todos" )
            {
                // por periodo
                por_periodo = true;

                // data e hora
                ini = DateTime.Parse(DataIni);
                fim = DateTime.Parse(DataFim);
            }

            // verifica se medicao EN
            if (medicao.IDTipoMedicao == 0)
            {
                // apaga FECHAMENTOS
                FechamentosMetodos fechamentoMetodos = new FechamentosMetodos();
                if (por_periodo)
                    fechamentoMetodos.ExcluirData(medicao.IDGateway, ini, fim);
                else
                    fechamentoMetodos.ExcluirTodosIDGateway(medicao.IDGateway);

                // apaga historicos Medicao
                EN_Metodos enMetodos = new EN_Metodos();
                if (por_periodo)
                    enMetodos.ExcluirData(medicao.IDCliente, medicao.IDMedicao, ini, fim);
                else
                    enMetodos.ExcluirTabela(medicao.IDCliente, medicao.IDMedicao);
            }

            // verifica se medicao GG
            if (medicao.IDTipoMedicao == 2 || medicao.IDTipoMedicao == 3 || medicao.IDTipoMedicao == 4)
            {
                // apaga historicos Medicao
                GG_Metodos ggMetodos = new GG_Metodos();
                if (por_periodo)
                    ggMetodos.ExcluirData(medicao.IDCliente, medicao.IDMedicao, ini, fim);
                else
                    ggMetodos.ExcluirTabela(medicao.IDCliente, medicao.IDMedicao);
            }

            // apaga SUPERVISAO GATEWAY
            SupervGatewaysMetodos supervGatewayMetodos = new SupervGatewaysMetodos();
            if (por_periodo)
                supervGatewayMetodos.ExcluirData(medicao.IDGateway, ini, fim);
            else
                supervGatewayMetodos.ExcluirTodosIDGateway(medicao.IDGateway);

            // apaga historicos Gateway
            EV_Metodos evMetodos = new EV_Metodos();
            if (por_periodo)
                evMetodos.ExcluirData(medicao.IDCliente, medicao.IDGateway, ini, fim);
            else
                evMetodos.ExcluirTabela(medicao.IDCliente, medicao.IDGateway);

            // retorna status
            return false;
        }
    }
}