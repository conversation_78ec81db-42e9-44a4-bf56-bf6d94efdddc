﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Web.Hosting;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ManutencaoController
    {
        private static IDictionary<Guid, int> tasks = new Dictionary<Guid, int>();

        public ActionResult Start()
        {
            // log
            LogMessage("Início de Envio de Email");

            var taskId = Guid.NewGuid();
            tasks.Add(taskId, 0);

            // log
            LogMessage(string.Format("TaskId: {0}", taskId));

            Task.Factory.StartNew(() =>
            {
                // usuarios
                UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
                List<UsuarioDominio> usuarios = usuarioMetodos.ListarTodos();

                int total = usuarios.Count();
                int atual = 0;

                // log
                LogMessage(string.Format("Total Usuários: {0}", total));

                foreach (UsuarioDominio usuario in usuarios)
                {
                    // update task progress
                    tasks[taskId] = (atual/total) * 100; 
                    atual++;

                    // verifica se Cliente ou Consultor
                    //if( usuario.IDTipoAcesso == 1 || usuario.IDTipoAcesso == 2 || usuario.IDTipoAcesso == 3 )
                    //if (usuario.IDUsuario == 1)
                    //if (usuario.IDTipoAcesso != 1 && usuario.IDTipoAcesso != 2 && usuario.IDTipoAcesso != 3)
                    //{
                        // verifica se email tem pelo menos @
                        if (usuario.Email.Contains("@"))
                        {
                            // envia email
                            Enviar(usuario.NomeUsuario, usuario.Email);

                            // log
                            LogMessage(string.Format("Email N.[{0:000000}] | Usuário ID[{1:000000}] | IDCliente[{4:000000}] | Nome[{2}] | Email[{3}]", atual, usuario.IDUsuario, usuario.NomeUsuario, usuario.Email, usuario.IDCliente));

                            // da um tempo
                            Thread.Sleep(50);
                        }
                    //}
                }
                
                // terminou
                tasks.Remove(taskId);
            });

            // retorna status
            return Json(taskId, JsonRequestBehavior.AllowGet);
        }

        public ActionResult Progress(Guid id)
        {
            return Json(tasks.Keys.Contains(id) ? tasks[id] : 100, JsonRequestBehavior.AllowGet);
        }

        // GET: Enviar Email
        public async Task<ActionResult> Enviar(string Nome, string Email)
        {

            // emails
            string[] emails = { Email };

            // assunto
            string assunto = "Importante: Mudança na sua plataforma de Gerenciamento de Energia";

            // envia EMAIL
            var emailTemplate = "NovoSmartEnergy";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.Nome", Nome);
            message = message.Replace("ViewBag.Email", Email);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, null);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        public static async Task<string> EMailTemplate(string template)
        {
            var templateFilePath = HostingEnvironment.MapPath("~/Content/emailTemplates/") + template + ".cshtml";
            StreamReader objstreamreaderfile = new StreamReader(templateFilePath);
            var body = await objstreamreaderfile.ReadToEndAsync();
            objstreamreaderfile.Close();
            return body;
        }

        private static void LogMessage(string msg, string arquivo = null)
        {
            // nome de arquivo padrao
            string nome_arquivo = "C:\\Temp\\EnviaEMail_" + String.Format("{0:yyyyMMdd}", DateTime.Today) + ".txt";

            // verifica se tem nome de arquivo
            if (arquivo != null)
            {
                nome_arquivo = arquivo;
            }

            System.IO.StreamWriter sw = System.IO.File.AppendText(nome_arquivo);

            try
            {
                string logLine = System.String.Format(
                    "{0:G}: {1}", System.DateTime.Now, msg);
                sw.WriteLine(logLine);
                sw.Flush();
            }
            finally
            {
                sw.Close();
            }
        }
    }
}