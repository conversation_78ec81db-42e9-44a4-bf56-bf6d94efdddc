﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ManutencaoController
    {
        public ActionResult UsuarioEvento()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // le cookie datahora
            DateTime DataIni = CookieStore.LeCookie_Datahora("Manut_DataIni");
            DateTime DataFim = CookieStore.LeCookie_Datahora("Manut_DataFim");

            if( DataIni.Year == 2000)
            {
                DataIni = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 0, 0, 0);
                DataFim = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 23, 59, 59);
            }

            string data_ini = String.Format("{0:d}", DataIni);
            string hora_ini = String.Format("{0:t}", DataIni);
            string data_fim = String.Format("{0:d}", DataFim);
            string hora_fim = String.Format("{0:t}", DataFim);

            ViewBag.DataIni = data_ini;
            ViewBag.HoraIni = hora_ini;
            ViewBag.DataFim = data_fim;
            ViewBag.HoraFim = hora_fim;

            // lista de usuarios
            UsuarioMetodos usuariosMetodos = new UsuarioMetodos();
            List<UsuarioDominio> usuarios = new List<UsuarioDominio>();
            List<UsuarioLista> usuariosLista = new List<UsuarioLista>();

            // le todos os usuarios
            usuarios = usuariosMetodos.ListarTodos();

            // verifica se tem lista
            if( usuarios != null )
            {
                foreach(UsuarioDominio usuario in usuarios)
                {
                    UsuarioLista usuariolista = new UsuarioLista();

                    // usuario
                    usuariolista.IDUsuario = usuario.IDUsuario;
                    usuariolista.NomeUsuario = usuario.NomeUsuario;

                    // tipo acesso
                    ListaTiposMetodos tiposMetodos = new ListaTiposMetodos();
                    List<ListaTiposDominio> tiposAcesso = tiposMetodos.ListarTodos("TipoAcesso");
                    ListaTiposDominio tipoAcesso = tiposAcesso.Find(item => item.ID == usuario.IDTipoAcesso);
                    usuariolista.TipoAcesso = tipoAcesso.Descricao;

                    // cliente
                    usuariolista.IDCliente = usuario.IDCliente;

                    if( usuario.IDCliente > 0)
                    {
                        ClientesMetodos clientesMetodos = new ClientesMetodos();
                        ClientesDominio cliente = clientesMetodos.ListarPorId(usuario.IDCliente);

                        if( cliente != null )
                        {
                            usuariolista.NomeCliente = cliente.Fantasia;
                        }
                        else
                        {
                            usuariolista.NomeCliente = "";
                        }
                    }
                    else
                    {
                        usuariolista.NomeCliente = "";
                    }

                    // insere na lista
                    usuariosLista.Add(usuariolista);
                }
            }

            ViewBag.usuariosLista = usuariosLista;

            ViewBag.eventos = null;

            return View();
        }

        // GET: UsuarioEvento - Executar operacao
        public PartialViewResult _UsuarioEvento_Resultado(int IDUsuario, string DataIni, string DataFim, int GrupoEvento)
        {
            // pega data
            DateTime dateValueIni = DateTime.Parse(DataIni);
            DateTime dateValueFim = DateTime.Parse(DataFim);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Manut_DataIni", dateValueIni);
            CookieStore.SalvaCookie_Datahora("Manut_DataFim", dateValueFim);

            // le cookies
            LeCookies_SmartEnergy();

            // executa operacao
            UsuarioEventoMetodos usuarioEventoMetodos = new UsuarioEventoMetodos();
            List<UsuarioEventoDominio> eventos = usuarioEventoMetodos.ListarPorId(IDUsuario, dateValueIni, dateValueFim, GrupoEvento);

            ViewBag.eventos = eventos;

            // formata o texto do evento
            foreach(UsuarioEventoDominio evento in eventos)
            {
                // evento
                string evento_texto = "";

                switch(evento.Evento)
                {
                    case 0:
                        evento_texto = "Login";
                        break;

                    case 1:
                        evento_texto = "Logout";
                        break;

                    case 10:
                        evento_texto = "Alterou Configuração";
                        break;

                    case 11:
                        evento_texto = "Excluiu Configuração";
                        break;

                    case 12:
                        evento_texto = "Adicionou Configuração";
                        break;

                    case 20:
                        evento_texto = "Manutenção Constante";
                        break;
                }
                    
                if( evento.Evento >= 10 && evento.Evento <= 12)
                {
                    switch(evento.Aux1)
                    {
                        case 0:
                            evento_texto += string.Format(" [Cliente: ID {0}]", evento.Aux2);
                            break;

                        case 1:
                            evento_texto += string.Format(" [Gateway: ID {0}]", evento.Aux2);
                            break;

                        case 2:
                            evento_texto += string.Format(" [Grupo de Unidades: ID {0}]", evento.Aux2);
                            break;

                        case 3:
                            evento_texto += string.Format(" [Unidade: ID {0}]", evento.Aux2);
                            break;

                        case 4:
                            evento_texto += string.Format(" [Medição: ID {0}]", evento.Aux2);
                            break;

                        case 5:
                            evento_texto += string.Format(" [Usuário: ID {0}]", evento.Aux2);
                            break;

                        case 6:
                            evento_texto += string.Format(" [Ranking: ID {0}]", evento.Aux2);
                            break;

                        case 7:
                            evento_texto += string.Format(" [Grupo Usuário: ID {0}]", evento.Aux2);
                            break;

                        case 8:
                            evento_texto += string.Format(" [Rateio: ID {0}]", evento.Aux2);
                            break;

                        case 9:
                            evento_texto += string.Format(" [Grupo Medição: ID {0}]", evento.Aux2);
                            break;

                        default:
                            evento_texto += string.Format(" [Desconhecido: ID {0}]", evento.Aux2);
                            break;
                  
                    }
                }

                evento.TextoEvento = evento_texto;
            }

            // retorna status
            return PartialView();
        }

        // GET: UsuarioEvento - Executar operacao Quem Logou
        public PartialViewResult _UsuarioEvento_QuemLogou_Resultado(string DataIni, string DataFim)
        {
            // pega data
            DateTime dateValueIni = DateTime.Parse(DataIni);
            DateTime dateValueFim = DateTime.Parse(DataFim);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Manut_DataIni", dateValueIni);
            CookieStore.SalvaCookie_Datahora("Manut_DataFim", dateValueFim);

            // le cookies
            LeCookies_SmartEnergy();

            // executa operacao
            UsuarioEventoMetodos usuarioEventoMetodos = new UsuarioEventoMetodos();
            List<UsuarioEventoDominio> eventos = usuarioEventoMetodos.ListarPorQuemLogou(dateValueIni, dateValueFim);

            // lista de usuarios
            UsuarioMetodos usuariosMetodos = new UsuarioMetodos();
            List<UsuarioLista> usuariosLista = new List<UsuarioLista>();

            // cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();

            // encontro usuarios e ocorrencias de login
            foreach (UsuarioEventoDominio evento in eventos)
            {
                // verifica se login
                if (evento.Evento == 0)
                {
                    bool existe = false;

                    // procura usuario na lista
                    foreach( UsuarioLista user in usuariosLista)
                    {
                        if( user.IDUsuario == evento.IDUsuario )
                        {
                            // ja existe, soma no contador
                            user.contador++;
                            existe = true;
                            break;
                        }
                    }

                    if( !existe)
                    {
                        // nao existe, coloco na lista
                        UsuarioLista usuarioLista = new UsuarioLista();

                        usuarioLista.contador = 1;
                        usuarioLista.IDUsuario = evento.IDUsuario;
                        usuariosLista.Add(usuarioLista);
                    }
                }
            }

            // percorre lista de usuarios encontrados
            foreach (UsuarioLista user in usuariosLista)
            {
                // info do usuario
                UsuarioDominio usuario = usuariosMetodos.ListarPorId(user.IDUsuario);

                if (usuario != null)
                {
                    // preencho infos
                    user.NomeUsuario = usuario.NomeUsuario;
                    user.TipoAcesso = string.Format("{0}", usuario.IDTipoAcesso);
                    user.IDCliente = usuario.IDCliente;

                    ClientesDominio cliente = clienteMetodos.ListarPorId(usuario.IDCliente);

                    if(cliente != null)
                    {
                        user.NomeCliente = cliente.Fantasia;
                    }
                }
            }

            ViewBag.ListaUsuario = usuariosLista;

            // retorna status
            return PartialView();
        }
    }
}