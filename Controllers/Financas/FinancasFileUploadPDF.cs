﻿using System;
using System.IO;
using System.Web;
using System.Web.Mvc;

namespace SmartEnergy.Controllers
{
    public partial class FinancasController
    {
        #region Actions

        [HttpPost]
        public virtual ActionResult UploadFilePDFFatura()
        {
            HttpPostedFileBase myFile = Request.Files["nomePDF"];
            bool isUploaded = false;
            string message = "Envio falhou";

            if (myFile != null && myFile.ContentLength != 0)
            {
                // verifica se PDF
                string extensao = Path.GetExtension(myFile.FileName).ToUpper();

                if (extensao != ".PDF")
                {
                    message = "Arquivo deve ser PDF";
                }
                else
                {
                    string pathForSaving = Server.MapPath("~/PDF");

                    if (this.CreateFolderIfNeeded(pathForSaving))
                    {
                        try
                        {
                            //
                            // salva PDF
                            //
                            myFile.SaveAs(Path.Combine(pathForSaving, myFile.FileName));
                            isUploaded = true;
                            message = "Arquivo enviado com sucesso!";
                        }
                        catch (Exception ex)
                        {
                            message = string.Format("Envio falhou: {0}", ex.Message);
                        }
                    }
                }
            }

            return Json(new { isUploaded = isUploaded, message = message }, "text/html");
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Creates the folder if needed.
        /// </summary>
        /// <param name="path">The path.</param>
        /// <returns></returns>
        private bool CreateFolderIfNeeded(string path)
        {
            bool result = true;
            if (!Directory.Exists(path))
            {
                try
                {
                    Directory.CreateDirectory(path);
                }
                catch (Exception)
                {
                    /*TODO: You must process this exception.*/
                    result = false;
                }
            }
            return result;
        }

        #endregion
    }
}