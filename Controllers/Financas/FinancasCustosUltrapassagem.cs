﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class FinancasController
    {

        // GET: Custos de Ultrapassagem
        public ActionResult Custos_Ultrapassagem(int IDCliente, int tipo_arquivo = 0)
        {
            // Custos de Ultrapassagem
            return (Custos_Ultrapassagem_Show(IDCliente, tipo_arquivo));
        }

        // GET: Custos de Ultrapassagem
        private ActionResult Custos_Ultrapassagem_Show(int IDCliente, int tipo_arquivo = 0)
        {
            // tela de ajuda - custos de ultrapassagem
            CookieStore.SalvaCookie_String("PaginaAjuda", "Financas_CustosUltrapassagem");

            // salva cookie 
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "CustosUltrapassagem");

            // le cookies
            LeCookies_SmartEnergy();

            // data inicio
            DateTime data_hora_ini = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 0, 0);

            // data fim
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            // le cookie datahora
            DateTime data_hora_ini_cookie = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime data_hora_fim_cookie = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // verifica se existe datahora no cookie
            if (data_hora_ini_cookie.Year != 2000 && data_hora_fim_cookie.Year != 2000)
            {
                // copia data do cookie 
                data_hora_ini = data_hora_ini_cookie;
                data_hora_fim = data_hora_fim_cookie;
            }

            // converte
            DATAHORA dh_ini = new DATAHORA();
            DATAHORA dh_fim = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, data_hora_ini);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, data_hora_fim);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", data_hora_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", data_hora_fim);

            // data e hora atual
            ViewBag.DataIni = string.Format("{0:d}", data_hora_ini);
            ViewBag.HoraIni = string.Format("{0:HH:mm}", data_hora_ini);
            ViewBag.DataFim = string.Format("{0:d}", data_hora_fim);
            ViewBag.HoraFim = string.Format("{0:HH:mm}", data_hora_fim);

            ViewBag.DataTextoAtualIni = string.Format("{0:d} {1:HH:mm}", data_hora_ini, data_hora_ini);
            ViewBag.DataTextoAtualFim = string.Format("{0:d} {1:HH:mm}", data_hora_fim, data_hora_fim);

            ViewBag.DataAtualN = data_hora_ini;
            ViewBag.DataFinalN = data_hora_fim;

            return View();
        }

        // GET: Custos de Ultrapassagem Atualizar
        public ActionResult Custos_Ultrapassagem_Atualizar(int Navegacao, string DataIni, string DataFim)
        {
            // caso forcar data
            if (Navegacao == 100)
            {
                // pega data
                DateTime dateValueIni = DateTime.Parse(DataIni);
                DateTime dateValueFim = DateTime.Parse(DataFim);

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Fatura_DataIni", dateValueIni);
                CookieStore.SalvaCookie_Datahora("Fatura_DataFim", dateValueFim);
            }

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;

            // le cookie datahora
            DateTime datahora_cookie_ini = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -3:    // mes anterior

                    datahora_cookie_ini = datahora_cookie_ini.AddMonths(-1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(-1);
                    break;

                case 3:    // mes seguinte

                    datahora_cookie_ini = datahora_cookie_ini.AddMonths(1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(1);
                    break;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", datahora_cookie_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", datahora_cookie_fim);

            // data e hora atual
            ViewBag.DataIni = string.Format("{0:d}", datahora_cookie_ini);
            ViewBag.HoraIni = string.Format("{0:HH:mm}", datahora_cookie_ini);
            ViewBag.DataFim = string.Format("{0:d}", datahora_cookie_fim);
            ViewBag.HoraFim = string.Format("{0:HH:mm}", datahora_cookie_fim);

            ViewBag.DataTextoAtualIni = string.Format("{0:d} {1:HH:mm:ss}", datahora_cookie_ini, datahora_cookie_ini);
            ViewBag.DataTextoAtualFim = string.Format("{0:d} {1:HH:mm:ss}", datahora_cookie_fim, datahora_cookie_fim);

            ViewBag.DataAtualN = datahora_cookie_ini;
            ViewBag.DataFinalN = datahora_cookie_fim;

            // converte
            DATAHORA dh_ini = new DATAHORA();
            DATAHORA dh_fim = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, datahora_cookie_ini);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, datahora_cookie_fim);

            // retorna status
            return Json(0, JsonRequestBehavior.AllowGet);
        }

        // GET: Custos de Ultrapassagem Print
        public ActionResult Custos_Ultrapassagem_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Custos_Ultrapassagem_Show(IDCliente);

            // resultado
            ViewBag.custo_ultr_resultado = lista_custo_ultr.Keys.Contains(id) ? lista_custo_ultr[id] : null;

            // imprime
            return View();
        }

        // GET: Custos de Ultrapassagem EMAIL
        public async Task<ActionResult> Custos_Ultrapassagem_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Custos_Ultrapassagem_Show(IDCliente);

            // resultado
            ViewBag.custo_ultr_resultado = lista_custo_ultr.Keys.Contains(id) ? lista_custo_ultr[id] : null;

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("CustosUltrapassagem_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDCliente, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_Custos_Ultrapassagem_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "CustosUltrapassagemEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtualIni", ViewBag.DataTextoAtualIni);
            message = message.Replace("ViewBag.DataTextoAtualFim", ViewBag.DataTextoAtualFim);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Custos de Ultrapassagem PDF
        public ActionResult Custos_Ultrapassagem_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Custos_Ultrapassagem_Show(IDCliente);

            // resultado
            ViewBag.custo_ultr_resultado = lista_custo_ultr.Keys.Contains(id) ? lista_custo_ultr[id] : null;

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("CustosUltrapassagem_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDCliente, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_Custos_Ultrapassagem_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Custos de Ultrapassagem XLS
        public ActionResult Custos_Ultrapassagem_XLS()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Custos_Ultrapassagem_Show(IDCliente);

            // resultado
            ViewBag.custo_ultr_resultado = lista_custo_ultr.Keys.Contains(id) ? lista_custo_ultr[id] : null;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_Custos_Ultrapassagem(IDCliente);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("CustosUltrapassagem_{0:000000}_{1:yyyyMMddHHmm}.xls", IDCliente, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Custos de Ultrapassagem XLS Download
        [HttpGet]
        public virtual ActionResult Custos_Ultrapassagem_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }


        // estruturas
        private static IDictionary<Guid, int> tasks_custo_ultr = new Dictionary<Guid, int>();
        private List<FATURA_RESUMIDA> lista_custo_ultr_tmp = new List<FATURA_RESUMIDA>();
        private static IDictionary<Guid, List<FATURA_RESUMIDA>> lista_custo_ultr = new Dictionary<Guid, List<FATURA_RESUMIDA>>();

        public ActionResult Custos_Ultrapassagem_IniciaCalculo()
        {
            // taskID
            Guid taskId = Guid.NewGuid();
            tasks_custo_ultr.Add(taskId, 0);

            // salva cookie
            CookieStore.SalvaCookie_GUID("taskID", taskId);

            // le cookies
            LeCookies_SmartEnergy();

            // simulação
            int IDSimulacaoCenario = 0;

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // le cookie datahora
            DateTime datahora_cookie_ini = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // limpa lista
            lista_custo_ultr_tmp.Clear();

            // task
            Task.Factory.StartNew(() =>
            {
                // le medicoes
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                List<MedicoesDominio> medicoes = medicoesMetodos.ListarTodos(IDCliente, ViewBag._ConfigMed);

                // estruturas

                // lista de erros
                var listaErros = new List<string>();

                // preenche solicitacao
                CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
                config_interface.sweb.id_cliente = IDCliente;

                // converte
                DATAHORA dh_ini = new DATAHORA();
                DATAHORA dh_fim = new DATAHORA();
                Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, datahora_cookie_ini);
                Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, datahora_cookie_fim);

                // estrutura
                RESULT_ENERGIA_FATURA fatura = new RESULT_ENERGIA_FATURA();
                RESULT_ENERGIA_FATURA fatura_sim = new RESULT_ENERGIA_FATURA();

                // barra de progresso
                int total = 0;
                int atual = 0;

                // percorre medicoes
                if (medicoes != null)
                {
                    // barra de progresso
                    total = medicoes.Count();

                    // percorre medicoes
                    foreach (MedicoesDominio medicao in medicoes)
                    {
                        // update task progress
                        double progresso = ((double)atual / (double)total) * 100;
                        tasks_custo_ultr[taskId] = (int)progresso;
                        atual++;

                        // verifica se nao eh energia ou medição principal (unidade consumidora)
                        if ((medicao.IDTipoMedicao != TIPO_MEDICAO.ENERGIA && medicao.IDTipoMedicao != TIPO_MEDICAO.ENERGIA_FORMULA) || medicao.IDCategoriaMedicao != CATEGORIA_MEDICAO.PRINCIPAL)
                        {
                            continue;
                        }

                        // preenche solicitacao
                        config_interface.sweb.id_medicao = medicao.IDMedicao;
                        config_interface.sweb.id_gateway = medicao.IDGateway;

                        // demanda historica
                        fatura.demanda_hist = 0.0;

                        // calcula valores fatura (sem simulação)
                        int retorno = SmCalcDB_Energia_Fatura((char)0, ref config_interface, IDSimulacaoCenario, (char)medicao.IDContratoMedicao, (char)medicao.IDEstruturaTarifaria, (char)0, ref dh_ini, ref dh_fim, ref fatura, ref fatura_sim);

                        // coloca resultado na lista temporaria
                        FATURA_RESUMIDA fatura_resumida = new FATURA_RESUMIDA();
                        fatura_resumida.IDMedicao = medicao.IDMedicao;
                        fatura_resumida.Nome_Medicao = medicao.Nome;
                        fatura_resumida.IDEstruturaTarifaria = medicao.IDEstruturaTarifaria;
                        fatura_resumida.retorno = retorno;
                        fatura_resumida.fatura = fatura;

                        // verifica se houve custo de ultrapassagem
                        double Total_Multa = fatura.ultr_demanda_p[CF.TOT] + fatura.ultr_demanda_fp[CF.TOT] +
                                             fatura.ultr_demanda_pe_p[CF.TOT] + fatura.ultr_demanda_pe_fp[CF.TOT] +
                                             fatura.fdr_p[CF.TOT] + fatura.fdr_fp[CF.TOT] +
                                             fatura.fdr_pe_p[CF.TOT] + fatura.fdr_pe_fp[CF.TOT] +
                                             fatura.fer_p[CF.TOT] + fatura.fer_fpi[CF.TOT] + fatura.fer_fpc[CF.TOT];

                        if( Total_Multa != 0.0 )
                        {
                            lista_custo_ultr_tmp.Add(fatura_resumida);
                        }
                    }
                }

                // coloca resultado na lista
                lista_custo_ultr.Add(taskId, new List<FATURA_RESUMIDA>(lista_custo_ultr_tmp));

                // terminou
                tasks_custo_ultr.Remove(taskId);
            });

            // retorna status
            return Json(taskId, JsonRequestBehavior.AllowGet);
        }

        public ActionResult Custos_Ultrapassagem_Progress(Guid id)
        {
            return Json(tasks_custo_ultr.Keys.Contains(id) ? tasks_custo_ultr[id] : 100, JsonRequestBehavior.AllowGet);
        }

        public PartialViewResult _Custos_Ultrapassagem(Guid id)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // resultado
            ViewBag.custo_ultr_resultado = lista_custo_ultr.Keys.Contains(id) ? lista_custo_ultr[id] : null;

            return PartialView();
        }

        // Custos de Ultrapassagem XLS
        private HSSFWorkbook XLS_Custos_Ultrapassagem(int IDCliente)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            ICellStyle _textoBorderCellStyle = criaEstiloXLS(workbook, 100, true);
            ICellStyle _1CellBorderStyle = criaEstiloXLS(workbook, 1, true);
            ICellStyle _2CellBorderStyle = criaEstiloXLS(workbook, 2, true);

            //
            // MEDICOES
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Custos de Ultrapassagem");

            // cabecalho
            string[] cabecalho = { "Medições", "Estrutura Tarifária", "Demanda Contrato Ponta (kW)", "Demanda Contrato FPonta (kW)", "Demanda Registrada Ponta (kW)", "Demanda Registrada FPonta (kW)", "Ultrapassagem Ponta (kW)", "Ultrapassagem FPonta (kW)", "Multa Demanda (R$)", "Multa FDR (R$)", "Multa FER (R$)", "Multa Total (R$)", "Total (R$)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            List<FATURA_RESUMIDA> custo_ultr_resultado = ViewBag.custo_ultr_resultado;
            int i;
            IRow row;

            // percorre valores
            if (custo_ultr_resultado != null)
            {
                foreach (FATURA_RESUMIDA fat in custo_ultr_resultado)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex++);

                    // medicao
                    textoCelulaXLS(row, 0, fat.Nome_Medicao);

                    if (fat.retorno != 0)
                    {
                        textoCelulaXLS(row, 1, "---");
                        textoCelulaXLS(row, 2, "---");
                        textoCelulaXLS(row, 3, "---");
                        textoCelulaXLS(row, 4, "---");
                        textoCelulaXLS(row, 5, "---");
                        textoCelulaXLS(row, 6, "---");
                        textoCelulaXLS(row, 7, "---");
                        textoCelulaXLS(row, 8, "---");
                        textoCelulaXLS(row, 9, "---");
                        textoCelulaXLS(row, 10, "---");
                        textoCelulaXLS(row, 11, "---");
                        numeroCelulaXLS(row, 12, 0.0, _2CellStyle);
                    }
                    else
                    {
                        if (fat.IDEstruturaTarifaria == 0)
                        {
                            double Total_Multa_Dem = fat.fatura.ultr_demanda_p[CF.TOT] + fat.fatura.ultr_demanda_fp[CF.TOT] +
                                                   fat.fatura.ultr_demanda_pe_p[CF.TOT] + fat.fatura.ultr_demanda_pe_fp[CF.TOT];

                            double Total_Multa_FDR = fat.fatura.fdr_p[CF.TOT] + fat.fatura.fdr_fp[CF.TOT] +
                                                   fat.fatura.fdr_pe_p[CF.TOT] + fat.fatura.fdr_pe_fp[CF.TOT];

                            double Total_Multa_FER = fat.fatura.fer_p[CF.TOT] + fat.fatura.fer_fpi[CF.TOT] + fat.fatura.fer_fpc[CF.TOT];

                            double valor = 0.0;

                            if (fat.fatura.demanda_fpi[CF.REG] > fat.fatura.demanda_fpc[CF.REG])
                            {
                                valor = fat.fatura.demanda_fpi[CF.REG];
                            }
                            else
                            {
                                valor = fat.fatura.demanda_fpc[CF.REG];
                            }

                            textoCelulaXLS(row, 1, "THS Azul");

                            numeroCelulaXLS(row, 2, fat.fatura.contrato_dem_p, _1CellStyle);
                            numeroCelulaXLS(row, 3, fat.fatura.contrato_dem_fp, _1CellStyle);

                            numeroCelulaXLS(row, 4, fat.fatura.demanda_p[CF.REG], _1CellStyle);
                            numeroCelulaXLS(row, 5, valor, _1CellStyle);

                            numeroCelulaXLS(row, 6, fat.fatura.ultr_demanda_p[CF.FAT], _1CellStyle);
                            numeroCelulaXLS(row, 7, fat.fatura.ultr_demanda_fp[CF.FAT], _1CellStyle);

                            numeroCelulaXLS(row, 8, Total_Multa_Dem, _2CellStyle);
                            numeroCelulaXLS(row, 9, Total_Multa_FDR, _2CellStyle);
                            numeroCelulaXLS(row, 10, Total_Multa_FER, _2CellStyle);
                            numeroCelulaXLS(row, 11, Total_Multa_Dem + Total_Multa_FDR + Total_Multa_FER, _2CellStyle);

                            numeroCelulaXLS(row, 12, fat.fatura.total, _2CellStyle);
                        }
                        else
                        {
                            double Total_Multa_Dem = fat.fatura.ultr_demanda_p[CF.TOT] + fat.fatura.ultr_demanda_pe_p[CF.TOT];

                            double Total_Multa_FDR = fat.fatura.fdr_p[CF.TOT] + fat.fatura.fdr_pe_p[CF.TOT];

                            double Total_Multa_FER = fat.fatura.fer_p[CF.TOT] + fat.fatura.fer_fpi[CF.TOT] + fat.fatura.fer_fpc[CF.TOT];

                            textoCelulaXLS(row, 1, "THS Verde");

                            numeroCelulaXLS(row, 3, fat.fatura.contrato_dem_p, _1CellStyle);
                            numeroCelulaXLS(row, 5, fat.fatura.demanda_p[CF.REG], _1CellStyle);
                            numeroCelulaXLS(row, 7, fat.fatura.ultr_demanda_p[CF.FAT], _1CellStyle);

                            numeroCelulaXLS(row, 8, Total_Multa_Dem, _2CellStyle);
                            numeroCelulaXLS(row, 9, Total_Multa_FDR, _2CellStyle);
                            numeroCelulaXLS(row, 10, Total_Multa_FER, _2CellStyle);
                            numeroCelulaXLS(row, 11, Total_Multa_Dem + Total_Multa_FDR + Total_Multa_FER, _2CellStyle);

                            numeroCelulaXLS(row, 12, fat.fatura.total, _2CellStyle);
                        }
                    }
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS_CustosUltr(workbook, sheet, _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(4).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }
    }
}