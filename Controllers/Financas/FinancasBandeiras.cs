﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class FinancasController
    {
        // Listas utilizadas
        private void PreparaListas_Bandeira()
        {
            // le tipos bandeira
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposBandeira = listatiposMetodos.ListarTodos("TipoBandeiras", false);
            ViewBag.listatiposBandeira = listatiposBandeira;

            return;
        }

        // GET: Bandeiras - Historico
        public ActionResult Bandeiras_Historico()
        {
            // tela de ajuda - bandeiras
            CookieStore.SalvaCookie_String("PaginaAjuda", "Financas_Configuracao_Bandeiras");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_Bandeira();

            // le bandeiras
            BandeirasMetodos bandeiraMetodos = new BandeirasMetodos();
            List<BandeirasDominio> listaBandeiras = bandeiraMetodos.ListarTodos();
            ViewBag.listaBandeiras = listaBandeiras;

            return View();
        }

        // GET: Bandeira - Editar
        public ActionResult Bandeira_Editar(int IDBandeira)
        {
            // tela de ajuda - bandeiras
            CookieStore.SalvaCookie_String("PaginaAjuda", "Financas_Configuracao_BandeirasEditar");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_Bandeira();

            // verifica se adicionando
            BandeirasDominio bandeira = new BandeirasDominio();
            if (IDBandeira == 0)
            {
                // zera bandeira com default
                bandeira.IDBandeira = 0;
                bandeira.Data = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);

                bandeira.Bandeira_SE_CO = 0;
                bandeira.Bandeira_S = 0;
                bandeira.Bandeira_NE = 0;
                bandeira.Bandeira_N = 0;

                bandeira.TarifaVerde_SE_CO = 0.0;
                bandeira.TarifaVerde_S = 0.0;
                bandeira.TarifaVerde_NE = 0.0;
                bandeira.TarifaVerde_N = 0.0;

                bandeira.TarifaAmarela_SE_CO = 0.0;
                bandeira.TarifaAmarela_S = 0.0;
                bandeira.TarifaAmarela_NE = 0.0;
                bandeira.TarifaAmarela_N = 0.0;

                bandeira.TarifaVermelha_SE_CO = 0.0;
                bandeira.TarifaVermelha_S = 0.0;
                bandeira.TarifaVermelha_NE = 0.0;
                bandeira.TarifaVermelha_N = 0.0;

                bandeira.TarifaRosa_SE_CO = 0.0;
                bandeira.TarifaRosa_S = 0.0;
                bandeira.TarifaRosa_NE = 0.0;
                bandeira.TarifaRosa_N = 0.0;

                bandeira.TarifaEscassez_SE_CO = 0.0;
                bandeira.TarifaEscassez_S = 0.0;
                bandeira.TarifaEscassez_NE = 0.0;
                bandeira.TarifaEscassez_N = 0.0;
            }
            else
            {
                // le bandeira
                BandeirasMetodos bandeiraMetodos = new BandeirasMetodos();
                bandeira = bandeiraMetodos.ListarPorId(IDBandeira);
            }

            return View(bandeira);
        }

        // POST: Bandeira - Salvar
        [HttpPost]
        public ActionResult Bandeira_Salvar(BandeirasDominio bandeira)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outra bandeira com a mesma data
            BandeirasMetodos bandeiraMetodos = new BandeirasMetodos();
            if (bandeiraMetodos.VerificarDuplicidade(bandeira))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Data existente."
                };
            }
            else
            {
                // salva fechamento
                bandeiraMetodos.Salvar(bandeira);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Bandeira - Excluir
        public ActionResult Bandeira_Excluir(int IDBandeira)
        {
            // apaga bandeira
            BandeirasMetodos bandeiraMetodos = new BandeirasMetodos();
            bandeiraMetodos.Excluir(IDBandeira);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}