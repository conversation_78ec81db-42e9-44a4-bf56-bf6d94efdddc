﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class FinancasController
    {
        // GET: ICMS - Historico
        public ActionResult ICMS_Historico(int IDEstado = 26)
        {
            // tela de ajuda - ICMS
            CookieStore.SalvaCookie_String("PaginaAjuda", "Configuracao_Medicoes_ICMS");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le tipos Estado
            CidadeEstadoPaisMetodos cidadeMetodos = new CidadeEstadoPaisMetodos();
            List<EstadosDominio> listatiposEstado = cidadeMetodos.ListarTodosEstados();
            ViewBag.listaTipoEstado = listatiposEstado;

            // le ICMS do estado (selecionado SP - 26)
            ICMSMetodos icmsMetodos = new ICMSMetodos();
            List<ICMSDominio> listaICMS = icmsMetodos.ListarTodos(IDEstado);
            ViewBag.listaICMS = listaICMS;
            ViewBag.IDEstado = IDEstado;

            return View();
        }

        // GET: ICMS - Seleciona estado
        public PartialViewResult _ICMS_Historico_Atualizar(int IDEstado)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le ICMS do estado
            ICMSMetodos icmsMetodos = new ICMSMetodos();
            List<ICMSDominio> listaICMS = icmsMetodos.ListarTodos(IDEstado);
            ViewBag.listaICMS = listaICMS;

            return PartialView();
        }

        // GET: ICMS - Editar
        public ActionResult ICMS_Editar(int IDICMS)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // verifica se adicionando
            ICMSDominio icms = new ICMSDominio();
            if (IDICMS == 0)
            {
                // zera icms com default
                icms.IDICMS = 0;
                icms.IDEstado = 26;
                icms.DataIni = DateTime.Now;
                icms.valorICMS = 18.0;
            }
            else
            {
                // le ICMS
                ICMSMetodos icmsMetodos = new ICMSMetodos();
                icms = icmsMetodos.ListarPorId(IDICMS);
            }

            // le tipos Estado
            CidadeEstadoPaisMetodos cidadeMetodos = new CidadeEstadoPaisMetodos();
            List<EstadosDominio> listatiposEstado = cidadeMetodos.ListarTodosEstados();
            ViewBag.listaTipoEstado = listatiposEstado;

            return View(icms);
        }

        // POST: ICMS - Salvar
        [HttpPost]
        public ActionResult ICMS_Salvar(ICMSDominio icms)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro ICMS com a mesma data
            ICMSMetodos icmsMetodos = new ICMSMetodos();
            if (icmsMetodos.VerificarDuplicidade(icms))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Data existente."
                };
            }
            else
            {
                // salva ICMS
                icmsMetodos.Salvar(icms);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: ICMS - Excluir
        public ActionResult ICMS_Excluir(int IDICMS)
        {
            // apaga o ICMS
            ICMSMetodos icmsMetodos = new ICMSMetodos();
            icmsMetodos.Excluir(IDICMS);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}