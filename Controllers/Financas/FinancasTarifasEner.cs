﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class FinancasController
    {
        // GET: Tarifas de Energia Eletrica - Distribuidoras
        public ActionResult TarifasEner_Distribuidoras()
        {
            // tela de ajuda - tarifas
            CookieStore.SalvaCookie_String("PaginaAjuda", "Financas_Configuracao_Tarifas_Energia_Distribuidoras");

            // le cookies
            LeCookies_SmartEnergy();

            // le distribuidoras
            AgentesDistribuidoraMetodos distribuidorasMetodos = new AgentesDistribuidoraMetodos();
            List<AgentesDistribuidoraDominio> listaDistribuidoras = distribuidorasMetodos.ListarTodos();

            // percorre distribuidoras e descobre numero de medicoes associadas
            foreach(AgentesDistribuidoraDominio distribuidora in listaDistribuidoras)
            {
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();

                // numero de medicoes com esta distribuidora
                int NumMedicoes = medicoesMetodos.NumMedicoesDistribuidora(distribuidora.IDAgenteDistribuidora);
                distribuidora.NumMedicoes = NumMedicoes;

                // numero de clientes com esta distribuidora
                int NumClientes = medicoesMetodos.NumClientesDistribuidora(distribuidora.IDAgenteDistribuidora);
                distribuidora.NumClientes = NumClientes;

                // numero de gateways com esta distribuidora
                int NumGateways = medicoesMetodos.NumGatewaysDistribuidora(distribuidora.IDAgenteDistribuidora);
                distribuidora.NumGateways = NumGateways;
            }

            return View(listaDistribuidoras);
        }

        // GET: Tarifas de Energia Eletrica - Historico
        public ActionResult TarifasEner_Historico(int IDAgenteDistribuidora)
        {
            // tela de ajuda - tarifas
            CookieStore.SalvaCookie_String("PaginaAjuda", "Financas_Configuracao_Tarifas_Energia_Historicos");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le distribuidora
            AgentesDistribuidoraMetodos distribuidorasMetodos = new AgentesDistribuidoraMetodos();
            AgentesDistribuidoraDominio distribuidora = distribuidorasMetodos.ListarPorId(IDAgenteDistribuidora);

            ViewBag.IDAgenteDistribuidora = distribuidora.IDAgenteDistribuidora;
            ViewBag.Sigla = distribuidora.Nome;
            ViewBag.Nome = distribuidora.RazaoSocial;

            // le historico das tarifas da distribuidora
            // utiliza a tarifa azul, subgrupo A1
            Tarifas_AZMetodos tarifasAZMetodos = new Tarifas_AZMetodos();
            List<Tarifas_AZDominio> listaTarifas = tarifasAZMetodos.ListarPorIDDistribuidoraSubgrupo(IDAgenteDistribuidora, 1);
            return View(listaTarifas);
        }

        // GET: Tarifas de Energia Eletrica - Excluir
        public ActionResult TarifasEner_Historico_Excluir(int IDAgenteDistribuidora, string DataHora)
        {
            // data a excluir
            DateTime data = DateTime.Parse(DataHora);

            // AZ
            Tarifas_AZMetodos tarifasAZMetodos = new Tarifas_AZMetodos();
            tarifasAZMetodos.ExcluirData(IDAgenteDistribuidora, data);

            // VD
            Tarifas_VDMetodos tarifasVDMetodos = new Tarifas_VDMetodos();
            tarifasVDMetodos.ExcluirData(IDAgenteDistribuidora, data);

            // CV
            Tarifas_CVMetodos tarifasCVMetodos = new Tarifas_CVMetodos();
            tarifasCVMetodos.ExcluirData(IDAgenteDistribuidora, data);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // zera item lista
        private TarifasEnerDominio ZeraTarifasEnerDominio(int IDEstruturaTarifaria, int IDTipoSubgrupo)
        {
            TarifasEnerDominio tarifa = new TarifasEnerDominio();

            // zera
            tarifa.IDTarifa = 0;
            tarifa.IDEstruturaTarifaria = IDEstruturaTarifaria;
            tarifa.IDTipoSubgrupo = IDTipoSubgrupo;
            
            switch(IDEstruturaTarifaria)
            {
                case 0:
                    tarifa.TarifaDemandaP = "R$ -,--";
                    tarifa.TarifaDemandaFP = "R$ -,--";
                    tarifa.TarifaConsumoP = "R$ -,--";
                    tarifa.TarifaConsumoFP = "R$ -,--";
                    break;

                case 1:
                    tarifa.TarifaDemandaP = "R$ -,--";
                    tarifa.TarifaDemandaFP = "";
                    tarifa.TarifaConsumoP = "R$ -,--";
                    tarifa.TarifaConsumoFP = "R$ -,--";
                    break;

                case 2:
                    tarifa.TarifaDemandaP = "R$ -,--";
                    tarifa.TarifaDemandaFP = "";
                    tarifa.TarifaConsumoP = "R$ -,--";
                    tarifa.TarifaConsumoFP = "";
                    break;
            }

            return tarifa;
        }

        // le item lista - AZ
        private TarifasEnerDominio LeAZTarifasEnerDominio(int IDAgenteDistribuidora, int IDTipoSubgrupo, DateTime data)
        {
            TarifasEnerDominio tarifa = new TarifasEnerDominio();
            Tarifas_AZMetodos tarifasAZMetodos = new Tarifas_AZMetodos();

            // zera
            tarifa.IDTarifa = 0;
            tarifa.IDEstruturaTarifaria = 0;
            tarifa.IDTipoSubgrupo = IDTipoSubgrupo;
            tarifa.TarifaDemandaP = "R$ -,--";
            tarifa.TarifaDemandaFP = "R$ -,--";
            tarifa.TarifaConsumoP = "R$ -,--";
            tarifa.TarifaConsumoFP = "R$ -,--";

            // le tarifa
            Tarifas_AZDominio tarifaAZ = tarifasAZMetodos.ListarPorData(IDAgenteDistribuidora, IDTipoSubgrupo, data);

            if( tarifaAZ != null)
            {
                tarifa.IDTarifa = tarifaAZ.IDTarifa;
                tarifa.IDEstruturaTarifaria = 0;
                tarifa.IDTipoSubgrupo = IDTipoSubgrupo;
                tarifa.TarifaDemandaP = String.Format("R$ {0:0.00000000}", tarifaAZ.Tardp);
                tarifa.TarifaDemandaFP = String.Format("R$ {0:0.00000000}", tarifaAZ.Tardf);
                tarifa.TarifaConsumoP = String.Format("R$ {0:0.00000000}", tarifaAZ.Tarcp);
                tarifa.TarifaConsumoFP = String.Format("R$ {0:0.00000000}", tarifaAZ.Tarcf);
            }

            return tarifa;
        }

        // le item lista - VD
        private TarifasEnerDominio LeVDTarifasEnerDominio(int IDAgenteDistribuidora, int IDTipoSubgrupo, DateTime data)
        {
            TarifasEnerDominio tarifa = new TarifasEnerDominio();
            Tarifas_VDMetodos tarifasVDMetodos = new Tarifas_VDMetodos();

            // zera
            tarifa.IDTarifa = 0;
            tarifa.IDEstruturaTarifaria = 1;
            tarifa.IDTipoSubgrupo = IDTipoSubgrupo;
            tarifa.TarifaDemandaP = "R$ -,--";
            tarifa.TarifaDemandaFP = "";
            tarifa.TarifaConsumoP = "R$ -,--";
            tarifa.TarifaConsumoFP = "R$ -,--";

            // le tarifa
            Tarifas_VDDominio tarifaVD = tarifasVDMetodos.ListarPorData(IDAgenteDistribuidora, IDTipoSubgrupo, data);

            if (tarifaVD != null)
            {
                tarifa.IDTarifa = tarifaVD.IDTarifa;
                tarifa.IDEstruturaTarifaria = 1;
                tarifa.IDTipoSubgrupo = IDTipoSubgrupo;
                tarifa.TarifaDemandaP = String.Format("R$ {0:0.00000000}", tarifaVD.Tard);
                tarifa.TarifaDemandaFP = "";
                tarifa.TarifaConsumoP = String.Format("R$ {0:0.00000000}", tarifaVD.Tarcp);
                tarifa.TarifaConsumoFP = String.Format("R$ {0:0.00000000}", tarifaVD.Tarcf);
            }

            return tarifa;
        }

        // le item lista - CV
        private TarifasEnerDominio LeCVTarifasEnerDominio(int IDAgenteDistribuidora, int IDTipoSubgrupo, DateTime data)
        {
            TarifasEnerDominio tarifa = new TarifasEnerDominio();
            Tarifas_CVMetodos tarifasCVMetodos = new Tarifas_CVMetodos();

            // zera
            tarifa.IDTarifa = 0;
            tarifa.IDEstruturaTarifaria = 2;
            tarifa.IDTipoSubgrupo = IDTipoSubgrupo;
            tarifa.TarifaDemandaP = "R$ -,--";
            tarifa.TarifaDemandaFP = "";
            tarifa.TarifaConsumoP = "R$ -,--";
            tarifa.TarifaConsumoFP = "";

            // le tarifa
            Tarifas_CVDominio tarifaCV = tarifasCVMetodos.ListarPorData(IDAgenteDistribuidora, IDTipoSubgrupo, data);

            if (tarifaCV != null)
            {
                tarifa.IDTarifa = tarifaCV.IDTarifa;
                tarifa.IDEstruturaTarifaria = 2;
                tarifa.IDTipoSubgrupo = IDTipoSubgrupo;
                tarifa.TarifaDemandaP = String.Format("R$ {0:0.00000000}", tarifaCV.Tard);
                tarifa.TarifaDemandaFP = "";
                tarifa.TarifaConsumoP = String.Format("R$ {0:0.00000000}", tarifaCV.Tarc);
                tarifa.TarifaConsumoFP = "";
            }

            return tarifa;
        }

        // GET: Tarifas de Energia Eletrica - Editar
        public ActionResult TarifasEner_Editar_DataResolucao(int IDAgenteDistribuidora, string DataHora)
        {
            // tela de ajuda - tarifas
            CookieStore.SalvaCookie_String("PaginaAjuda", "Financas_Configuracao_Tarifas_Energia_Tarifas");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le distribuidora
            AgentesDistribuidoraMetodos distribuidorasMetodos = new AgentesDistribuidoraMetodos();
            AgentesDistribuidoraDominio distribuidora = distribuidorasMetodos.ListarPorId(IDAgenteDistribuidora);

            ViewBag.IDAgenteDistribuidora = distribuidora.IDAgenteDistribuidora;
            ViewBag.Sigla = distribuidora.Nome;
            ViewBag.Nome = distribuidora.RazaoSocial;

            // data e resolucao
            TarifasEnerDataResolucaoDominio tarifasenerDataResolucao = new TarifasEnerDataResolucaoDominio();

            // lista de tarifas
            List<TarifasEnerDominio> tarifasEner = new List<TarifasEnerDominio>();

            // data
            DateTime data = DateTime.Parse(DataHora);

            // verifica se adicionando
            if( data.Year == 2000 )
            {
                // distribuidora
                tarifasenerDataResolucao.IDAgenteDistribuidora = IDAgenteDistribuidora;

                // data
                tarifasenerDataResolucao.Data = DateTime.Now;

                //  resolucao
                tarifasenerDataResolucao.Resolucao = "";

                // tarifas 
                TarifasEnerDominio tarifa = new TarifasEnerDominio();

                // zera
                tarifasEner.Add(ZeraTarifasEnerDominio(0, 1));   // AZ A1
                tarifasEner.Add(ZeraTarifasEnerDominio(0, 2));   // AZ A2
                tarifasEner.Add(ZeraTarifasEnerDominio(0, 3));   // AZ A3
                tarifasEner.Add(ZeraTarifasEnerDominio(0, 4));   // AZ A3a
                tarifasEner.Add(ZeraTarifasEnerDominio(0, 5));   // AZ A4
                tarifasEner.Add(ZeraTarifasEnerDominio(0, 6));   // AZ AS

                tarifasEner.Add(ZeraTarifasEnerDominio(1, 4));   // VD A3a
                tarifasEner.Add(ZeraTarifasEnerDominio(1, 5));   // VD A4
                tarifasEner.Add(ZeraTarifasEnerDominio(1, 6));   // VD AS

                tarifasEner.Add(ZeraTarifasEnerDominio(2, 4));   // CV A3a
                tarifasEner.Add(ZeraTarifasEnerDominio(2, 5));   // CV A4
                tarifasEner.Add(ZeraTarifasEnerDominio(2, 6));   // CV AS
                tarifasEner.Add(ZeraTarifasEnerDominio(2, 7));   // CV B3

                // IDs
                tarifasenerDataResolucao.IDTarifa_AZA1 = tarifasEner[0].IDTarifa;
                tarifasenerDataResolucao.IDTarifa_AZA2 = tarifasEner[1].IDTarifa;
                tarifasenerDataResolucao.IDTarifa_AZA3 = tarifasEner[2].IDTarifa;
                tarifasenerDataResolucao.IDTarifa_AZA3a = tarifasEner[3].IDTarifa;
                tarifasenerDataResolucao.IDTarifa_AZA4 = tarifasEner[4].IDTarifa;
                tarifasenerDataResolucao.IDTarifa_AZAS = tarifasEner[5].IDTarifa;

                tarifasenerDataResolucao.IDTarifa_VDA3a = tarifasEner[6].IDTarifa;
                tarifasenerDataResolucao.IDTarifa_VDA4 = tarifasEner[7].IDTarifa;
                tarifasenerDataResolucao.IDTarifa_VDAS = tarifasEner[8].IDTarifa;

                tarifasenerDataResolucao.IDTarifa_CVA3a = tarifasEner[9].IDTarifa;
                tarifasenerDataResolucao.IDTarifa_CVA4 = tarifasEner[10].IDTarifa;
                tarifasenerDataResolucao.IDTarifa_CVAS = tarifasEner[11].IDTarifa;
                tarifasenerDataResolucao.IDTarifa_CVB3 = tarifasEner[12].IDTarifa;
            }
            else
            {
                // le tarifa referencia - AZ A1
                Tarifas_AZMetodos tarifasAZMetodos = new Tarifas_AZMetodos();
                Tarifas_AZDominio tarifaAZA1 = tarifasAZMetodos.ListarPorData(IDAgenteDistribuidora, 1, data);

                // distribuidora
                tarifasenerDataResolucao.IDAgenteDistribuidora = IDAgenteDistribuidora;

                // data
                tarifasenerDataResolucao.Data = tarifaAZA1.Data;

                //  resolucao
                tarifasenerDataResolucao.Resolucao = tarifaAZA1.Resolucao;

                // le tarifas
                tarifasEner.Add(LeAZTarifasEnerDominio(IDAgenteDistribuidora, 1, tarifaAZA1.Data));   // AZ A1
                tarifasEner.Add(LeAZTarifasEnerDominio(IDAgenteDistribuidora, 2, tarifaAZA1.Data));   // AZ A2
                tarifasEner.Add(LeAZTarifasEnerDominio(IDAgenteDistribuidora, 3, tarifaAZA1.Data));   // AZ A3
                tarifasEner.Add(LeAZTarifasEnerDominio(IDAgenteDistribuidora, 4, tarifaAZA1.Data));   // AZ A3a
                tarifasEner.Add(LeAZTarifasEnerDominio(IDAgenteDistribuidora, 5, tarifaAZA1.Data));   // AZ A4
                tarifasEner.Add(LeAZTarifasEnerDominio(IDAgenteDistribuidora, 6, tarifaAZA1.Data));   // AZ AS

                tarifasEner.Add(LeVDTarifasEnerDominio(IDAgenteDistribuidora, 4, tarifaAZA1.Data));   // VD A3a
                tarifasEner.Add(LeVDTarifasEnerDominio(IDAgenteDistribuidora, 5, tarifaAZA1.Data));   // VD A4
                tarifasEner.Add(LeVDTarifasEnerDominio(IDAgenteDistribuidora, 6, tarifaAZA1.Data));   // VD AS

                tarifasEner.Add(LeCVTarifasEnerDominio(IDAgenteDistribuidora, 4, tarifaAZA1.Data));   // CV A3a
                tarifasEner.Add(LeCVTarifasEnerDominio(IDAgenteDistribuidora, 5, tarifaAZA1.Data));   // CV A4
                tarifasEner.Add(LeCVTarifasEnerDominio(IDAgenteDistribuidora, 6, tarifaAZA1.Data));   // CV AS
                tarifasEner.Add(LeCVTarifasEnerDominio(IDAgenteDistribuidora, 7, tarifaAZA1.Data));   // CV B3

                // IDs
                tarifasenerDataResolucao.IDTarifa_AZA1 = tarifasEner[0].IDTarifa;
                tarifasenerDataResolucao.IDTarifa_AZA2 = tarifasEner[1].IDTarifa;
                tarifasenerDataResolucao.IDTarifa_AZA3 = tarifasEner[2].IDTarifa;
                tarifasenerDataResolucao.IDTarifa_AZA3a = tarifasEner[3].IDTarifa;
                tarifasenerDataResolucao.IDTarifa_AZA4 = tarifasEner[4].IDTarifa;
                tarifasenerDataResolucao.IDTarifa_AZAS = tarifasEner[5].IDTarifa;

                tarifasenerDataResolucao.IDTarifa_VDA3a = tarifasEner[6].IDTarifa;
                tarifasenerDataResolucao.IDTarifa_VDA4 = tarifasEner[7].IDTarifa;
                tarifasenerDataResolucao.IDTarifa_VDAS = tarifasEner[8].IDTarifa;

                tarifasenerDataResolucao.IDTarifa_CVA3a = tarifasEner[9].IDTarifa;
                tarifasenerDataResolucao.IDTarifa_CVA4 = tarifasEner[10].IDTarifa;
                tarifasenerDataResolucao.IDTarifa_CVAS = tarifasEner[11].IDTarifa;
                tarifasenerDataResolucao.IDTarifa_CVB3 = tarifasEner[12].IDTarifa;
            }

            ViewBag.TarifasEner = tarifasEner;

            return View(tarifasenerDataResolucao);
        }

        // GET: Tarifas de Energia Eletrica - Salvar
        [HttpPost]
        public ActionResult TarifasEner_Salvar_DataResolucao(TarifasEnerDataResolucaoDominio tarifa)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // salva tarifa AZ
            Tarifas_AZMetodos tarifasAZMetodos = new Tarifas_AZMetodos();
            Tarifas_AZDominio tarifaAZ = new Tarifas_AZDominio();

            tarifaAZ.IDAgenteDistribuidora = tarifa.IDAgenteDistribuidora;
            tarifaAZ.Data = tarifa.Data;
            tarifaAZ.Resolucao = tarifa.Resolucao;

            tarifaAZ.IDTarifa = tarifa.IDTarifa_AZA1;
            tarifaAZ.IDTipoSubgrupo = 1;
            tarifasAZMetodos.SalvarDataResolucao(tarifaAZ);

            tarifaAZ.IDTarifa = tarifa.IDTarifa_AZA2;
            tarifaAZ.IDTipoSubgrupo = 2;
            tarifasAZMetodos.SalvarDataResolucao(tarifaAZ);

            tarifaAZ.IDTarifa = tarifa.IDTarifa_AZA3;
            tarifaAZ.IDTipoSubgrupo = 3;
            tarifasAZMetodos.SalvarDataResolucao(tarifaAZ);

            tarifaAZ.IDTarifa = tarifa.IDTarifa_AZA3a;
            tarifaAZ.IDTipoSubgrupo = 4;
            tarifasAZMetodos.SalvarDataResolucao(tarifaAZ);

            tarifaAZ.IDTarifa = tarifa.IDTarifa_AZA4;
            tarifaAZ.IDTipoSubgrupo = 5;
            tarifasAZMetodos.SalvarDataResolucao(tarifaAZ);

            tarifaAZ.IDTarifa = tarifa.IDTarifa_AZAS;
            tarifaAZ.IDTipoSubgrupo = 6;
            tarifasAZMetodos.SalvarDataResolucao(tarifaAZ);

            // salva tarifa VD
            Tarifas_VDMetodos tarifasVDMetodos = new Tarifas_VDMetodos();
            Tarifas_VDDominio tarifaVD = new Tarifas_VDDominio();

            tarifaVD.IDAgenteDistribuidora = tarifa.IDAgenteDistribuidora;
            tarifaVD.Data = tarifa.Data;
            tarifaVD.Resolucao = tarifa.Resolucao;

            tarifaVD.IDTarifa = tarifa.IDTarifa_VDA3a;
            tarifaVD.IDTipoSubgrupo = 4;
            tarifasVDMetodos.SalvarDataResolucao(tarifaVD);

            tarifaVD.IDTarifa = tarifa.IDTarifa_VDA4;
            tarifaVD.IDTipoSubgrupo = 5;
            tarifasVDMetodos.SalvarDataResolucao(tarifaVD);

            tarifaVD.IDTarifa = tarifa.IDTarifa_VDAS;
            tarifaVD.IDTipoSubgrupo = 6;
            tarifasVDMetodos.SalvarDataResolucao(tarifaVD);

            // salva tarifa CV
            Tarifas_CVMetodos tarifasCVMetodos = new Tarifas_CVMetodos();
            Tarifas_CVDominio tarifaCV = new Tarifas_CVDominio();

            tarifaCV.IDAgenteDistribuidora = tarifa.IDAgenteDistribuidora;
            tarifaCV.Data = tarifa.Data;
            tarifaCV.Resolucao = tarifa.Resolucao;

            tarifaCV.IDTarifa = tarifa.IDTarifa_CVA3a;
            tarifaCV.IDTipoSubgrupo = 4;
            tarifasCVMetodos.SalvarDataResolucao(tarifaCV);

            tarifaCV.IDTarifa = tarifa.IDTarifa_CVA4;
            tarifaCV.IDTipoSubgrupo = 5;
            tarifasCVMetodos.SalvarDataResolucao(tarifaCV);

            tarifaCV.IDTarifa = tarifa.IDTarifa_CVAS;
            tarifaCV.IDTipoSubgrupo = 6;
            tarifasCVMetodos.SalvarDataResolucao(tarifaCV);

            tarifaCV.IDTarifa = tarifa.IDTarifa_CVB3;
            tarifaCV.IDTipoSubgrupo = 7;
            tarifasCVMetodos.SalvarDataResolucao(tarifaCV);

            // retorna status
            return Json(returnedData);
        }

        // GET: Tarifas de Energia Eletrica - Editar
        public ActionResult TarifasEner_Editar_AZ(int IDAgenteDistribuidora, int IDTarifa, int IDTipoSubgrupo, string DataHora, string Resolucao)
        {
            // tela de ajuda - tarifas
            CookieStore.SalvaCookie_String("PaginaAjuda", "Financas_Configuracao_Tarifas_Energia_Tarifas_Editar");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le distribuidora
            AgentesDistribuidoraMetodos distribuidorasMetodos = new AgentesDistribuidoraMetodos();
            AgentesDistribuidoraDominio distribuidora = distribuidorasMetodos.ListarPorId(IDAgenteDistribuidora);

            ViewBag.IDAgenteDistribuidora = distribuidora.IDAgenteDistribuidora;
            ViewBag.Sigla = distribuidora.Nome;
            ViewBag.Nome = distribuidora.RazaoSocial;

            Tarifas_AZDominio tarifa_AZ = new Tarifas_AZDominio();

            // verifica se adicionando
            if (IDTarifa == 0)
            {
                tarifa_AZ.IDTarifa = 0;
                tarifa_AZ.IDAgenteDistribuidora = IDAgenteDistribuidora;
                tarifa_AZ.IDTipoSubgrupo = IDTipoSubgrupo;
                tarifa_AZ.Data = DateTime.Parse(DataHora);
                tarifa_AZ.Resolucao = Resolucao;

                tarifa_AZ.Tardp = 0.0;
                tarifa_AZ.Tardf = 0.0;
                tarifa_AZ.Tardp_u = 0.0;
                tarifa_AZ.Tardf_u = 0.0;

                tarifa_AZ.Tarcp = 0.0;
                tarifa_AZ.Tarcf = 0.0;

                tarifa_AZ.Tarcp_rtv = 0.0;
                tarifa_AZ.Tarcf_rtv = 0.0;

                tarifa_AZ.Tusdp = 0.0;
                tarifa_AZ.Tusdf = 0.0;
                tarifa_AZ.Tep = 0.0;
                tarifa_AZ.Tef = 0.0;

            }
            else
            {
                // le tarifas AZ
                Tarifas_AZMetodos tarifasAZMetodos = new Tarifas_AZMetodos();
                tarifa_AZ = tarifasAZMetodos.ListarPorId(IDTarifa);
            }

            return View("../Financas/TarifasEner_Editar_AZ", tarifa_AZ);
        }

        // GET: Tarifas de Energia Eletrica - Editar
        public ActionResult TarifasEner_Editar_VD(int IDAgenteDistribuidora, int IDTarifa, int IDTipoSubgrupo, string DataHora, string Resolucao)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le distribuidora
            AgentesDistribuidoraMetodos distribuidorasMetodos = new AgentesDistribuidoraMetodos();
            AgentesDistribuidoraDominio distribuidora = distribuidorasMetodos.ListarPorId(IDAgenteDistribuidora);

            ViewBag.IDAgenteDistribuidora = distribuidora.IDAgenteDistribuidora;
            ViewBag.Sigla = distribuidora.Nome;
            ViewBag.Nome = distribuidora.RazaoSocial;

            Tarifas_VDDominio tarifa_VD = new Tarifas_VDDominio();

            // verifica se adicionando
            if (IDTarifa == 0)
            {
                tarifa_VD.IDTarifa = 0;
                tarifa_VD.IDAgenteDistribuidora = IDAgenteDistribuidora;
                tarifa_VD.IDTipoSubgrupo = IDTipoSubgrupo;
                tarifa_VD.Data = DateTime.Parse(DataHora);
                tarifa_VD.Resolucao = Resolucao;

                tarifa_VD.Tard = 0.0;
                tarifa_VD.Tard_u = 0.0;

                tarifa_VD.Tarcp = 0.0;
                tarifa_VD.Tarcf = 0.0;

                tarifa_VD.Tarcp_rtv = 0.0;
                tarifa_VD.Tarcf_rtv = 0.0;

                tarifa_VD.Tusdp = 0.0;
                tarifa_VD.Tusdf = 0.0;
                tarifa_VD.Tep = 0.0;
                tarifa_VD.Tef = 0.0;

            }
            else
            {
                // le tarifas VD
                Tarifas_VDMetodos tarifasVDMetodos = new Tarifas_VDMetodos();
                tarifa_VD = tarifasVDMetodos.ListarPorId(IDTarifa);
            }

            return View("../Financas/TarifasEner_Editar_VD", tarifa_VD);
        }

        // GET: Tarifas de Energia Eletrica - Editar
        public ActionResult TarifasEner_Editar_CV(int IDAgenteDistribuidora, int IDTarifa, int IDTipoSubgrupo, string DataHora, string Resolucao)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le distribuidora
            AgentesDistribuidoraMetodos distribuidorasMetodos = new AgentesDistribuidoraMetodos();
            AgentesDistribuidoraDominio distribuidora = distribuidorasMetodos.ListarPorId(IDAgenteDistribuidora);

            ViewBag.IDAgenteDistribuidora = distribuidora.IDAgenteDistribuidora;
            ViewBag.Sigla = distribuidora.Nome;
            ViewBag.Nome = distribuidora.RazaoSocial;

            Tarifas_CVDominio tarifa_CV = new Tarifas_CVDominio();

            // verifica se adicionando
            if (IDTarifa == 0)
            {
                tarifa_CV.IDTarifa = 0;
                tarifa_CV.IDAgenteDistribuidora = IDAgenteDistribuidora;
                tarifa_CV.IDTipoSubgrupo = IDTipoSubgrupo;
                tarifa_CV.Data = DateTime.Parse(DataHora);
                tarifa_CV.Resolucao = Resolucao;

                tarifa_CV.Tard = 0.0;
                tarifa_CV.Tard_u = 0.0;

                tarifa_CV.Tarc = 0.0;

                tarifa_CV.Tarc_rtv = 0.0;

                tarifa_CV.Tusd = 0.0;
                tarifa_CV.Te = 0.0;
            }
            else
            {
                // le tarifas CV
                Tarifas_CVMetodos tarifasCVMetodos = new Tarifas_CVMetodos();
                tarifa_CV = tarifasCVMetodos.ListarPorId(IDTarifa);
            }

            return View("../Financas/TarifasEner_Editar_CV", tarifa_CV);
        }

        // GET: Tarifas de Energia Eletrica - Salvar
        [HttpPost]
        public ActionResult TarifasEner_Salvar_AZ(Tarifas_AZDominio tarifa)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // salva tarifa
            Tarifas_AZMetodos tarifasAZMetodos = new Tarifas_AZMetodos();
            tarifasAZMetodos.Salvar(tarifa);

            // retorna status
            return Json(returnedData);
        }

        // GET: Tarifas de Energia Eletrica - Salvar
        [HttpPost]
        public ActionResult TarifasEner_Salvar_VD(Tarifas_VDDominio tarifa)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // salva tarifa
            Tarifas_VDMetodos tarifasVDMetodos = new Tarifas_VDMetodos();
            tarifasVDMetodos.Salvar(tarifa);

            // retorna status
            return Json(returnedData);
        }

        // GET: Tarifas de Energia Eletrica - Salvar
        [HttpPost]
        public ActionResult TarifasEner_Salvar_CV(Tarifas_CVDominio tarifa)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // salva tarifa
            Tarifas_CVMetodos tarifasCVMetodos = new Tarifas_CVMetodos();
            tarifasCVMetodos.Salvar(tarifa);

            // retorna status
            return Json(returnedData);
        }
    }
}