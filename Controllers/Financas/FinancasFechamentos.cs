﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class FinancasController
    {
        // GET: Fechamentos - Historico
        public ActionResult Fechamentos_Historico()
        {
            // tela de ajuda - fechamentos
            CookieStore.SalvaCookie_String("PaginaAjuda", "Financas_Configuracao_FechamentosFatura");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le medicao
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicoesMetodos.ListarPorId(ViewBag._IDMedicao);

            // salva cookie 
            CookieStore.SalvaCookie_Int("_IDGateway", medicao.IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            <PERSON>mis<PERSON>();

            // gateway do fechamento
            int IDGateway = medicao.IDGateway;

            // verifica se deve utilizar outra gateway
            if (medicao.IDGateway_Fechamentos > 0)
            {
                IDGateway = medicao.IDGateway_Fechamentos;
            }

            // le fechamentos
            FechamentosMetodos fechamentoMetodos = new FechamentosMetodos();
            List<FechamentosDominio> listaFechamentos = fechamentoMetodos.ListarTodos(IDGateway);
            ViewBag.listaFechamentos = listaFechamentos;

            return View();
        }

        // GET: Fechamento - Editar
        public ActionResult Fechamento_Editar(int IDFechamento)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le medicao
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicoesMetodos.ListarPorId(ViewBag._IDMedicao);

            // gateway do fechamento
            int IDGateway = medicao.IDGateway;

            // verifica se deve utilizar outra gateway
            if (medicao.IDGateway_Fechamentos > 0)
            {
                IDGateway = medicao.IDGateway_Fechamentos;
            }

            // verifica se adicionando
            FechamentosDominio fechamento = new FechamentosDominio();
            if (IDFechamento == 0)
            {
                // zera fechamento com default
                fechamento.IDFechamento = 0;
                fechamento.IDCliente = ViewBag._IDCliente;
                fechamento.IDGateway = IDGateway;
                fechamento.DataHora = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 15, 0);
                fechamento.Flag = 2;
            }
            else
            {
                // le fechamento
                FechamentosMetodos fechamentoMetodos = new FechamentosMetodos();
                fechamento = fechamentoMetodos.ListarPorId(IDFechamento);
            }

            return View(fechamento);
        }

        // POST: Fechamento - Salvar
        [HttpPost]
        public ActionResult Fechamento_Salvar(FechamentosDominio fechamento)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro fechamento com a mesma data
            FechamentosMetodos fechamentoMetodos = new FechamentosMetodos();
            if (fechamentoMetodos.VerificarDuplicidade(fechamento))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Data existente."
                };
            }
            else
            {
                // salva fechamento
                fechamentoMetodos.Salvar(fechamento);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Fechamento - Excluir
        public ActionResult Fechamento_Excluir(int IDFechamento)
        {
            // le fechamento
            FechamentosMetodos fechamentoMetodos = new FechamentosMetodos();
            FechamentosDominio fechamento = fechamentoMetodos.ListarPorId(IDFechamento);

            if( fechamento != null )
            {
                // verifica origem
                switch( fechamento.Flag )
                {
                    case 0:

                        // fechamento inserido pelo historico de eventos
                        // nao posso apagar, mas somente omitir
                        fechamento.Flag = 1;
                        fechamentoMetodos.Salvar(fechamento);
                        break;

                    case 1:

                        // fechamento inserido pelo historico de eventos
                        // nao posso apagar, mas somente desomitir
                        fechamento.Flag = 0;
                        fechamentoMetodos.Salvar(fechamento);
                        break;

                    case 2:

                        // apaga o fechamento inserido manualmente
                        fechamentoMetodos.Excluir(IDFechamento);
                        break;
                }
            }

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}