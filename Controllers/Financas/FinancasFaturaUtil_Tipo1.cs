﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class FinancasController
    {
        // GET: Fatura Utilidades Tipo 1
        private void Fatura_Utilidades_Tipo1(int IDCliente, int IDMedicao, int IDGateway, DATAHORA dh_ini, DATAHORA dh_fim)
        {
            //
            // FATURA ATUAL
            //

            // nome do relatorio
            ViewBag.NomeRelat = string.Format("Fatura de Utilidades");

            int retorno;
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RESULT_UTIL_FATURA fatura = new RESULT_UTIL_FATURA();
            RESULT_UTIL_FATURA faturaAnt1 = new RESULT_UTIL_FATURA();
            RESULT_UTIL_FATURA faturaAnt2 = new RESULT_UTIL_FATURA();

            // converte para DateTime
            DateTime data_hora_ini = Funcoes_Converte.ConverteDataHora2DateTime(dh_ini);
            DateTime data_hora_fim = Funcoes_Converte.ConverteDataHora2DateTime(dh_fim);

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = IDGateway;

            // calcula valores fatura
            retorno = SmCalcDB_Util_Fatura((char)0, ref config_interface, ref dh_ini, ref dh_fim, ref fatura);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Fatura {0}: Erro {1}", string.Format("{0:Y}", data_hora_ini), retorno));
                if (fatura.flag_registros > 0)
                    listaErros.Add(string.Format("Fatura {0}: {1}", string.Format("{0:Y}", data_hora_ini), Funcoes_SmartEnergy.ErroRelatorio((int)fatura.flag_registros)));
            }

            // calcula custo medio
            double CustoMedio = (fatura.consumo_total == 0.0) ? 0.0 : (fatura.total / fatura.consumo_total);

            // tabela totais
            ViewBag.Fatura_Atual_Data = Funcoes_SmartEnergy.StringMesFechamento(data_hora_fim);
            ViewBag.Fatura_Atual_DataMes = Funcoes_SmartEnergy.StringMesFechamento(data_hora_fim, "MMMM");

            ViewBag.Fatura_Atual_ValorTotal = string.Format("{0:C}", fatura.total);
            ViewBag.Fatura_Atual_ValorTotalN = fatura.total;

            ViewBag.Fatura_Atual_CustoMedio = string.Format("{0:C} / {1}", CustoMedio, ViewBag.UnidadeGrandeza);
            ViewBag.Fatura_Atual_CustoMedioN = CustoMedio;

            //
            // OUTROS
            //

            // encontra outros
            FaturaOutrosMetodos outrosMetodos = new FaturaOutrosMetodos();
            List<FaturaOutrosDominio> listaOutros = outrosMetodos.ListarPorData(IDMedicao, data_hora_ini, data_hora_fim);

            ViewBag.listaOutros = listaOutros;

            //
            // TOTAL
            //

            // total
            string total;
            total = string.Format("{0:C}", fatura.total);
            ViewBag.total = total;

            // fatura
            ViewBag.Fatura = fatura; 

            //
            // FATURA MES -1
            //

            // atual
            DateTime data_atual_ini = data_hora_ini;
            DateTime data_atual_fim = data_hora_fim;

            // mes anterior
            data_hora_ini = data_atual_ini.AddMonths(-1);
            data_hora_fim = data_atual_fim.AddMonths(-1);

            // converte para DateHora
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, data_hora_ini);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, data_hora_fim);

            // calcula valores fatura
            retorno = SmCalcDB_Util_Fatura((char)0, ref config_interface, ref dh_ini, ref dh_fim, ref faturaAnt1);

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Fatura {0}: Erro {1}", string.Format("{0:Y}", data_hora_ini), retorno));
                if (fatura.flag_registros > 0)
                    listaErros.Add(string.Format("Fatura {0}: {1}", string.Format("{0:Y}", data_hora_ini), Funcoes_SmartEnergy.ErroRelatorio((int)fatura.flag_registros)));
            }

            // calcula custo medio
            CustoMedio = (faturaAnt1.consumo_total == 0.0) ? 0.0 : (faturaAnt1.total / faturaAnt1.consumo_total);

            // tabela totais
            ViewBag.Fatura_Ant1_Data = Funcoes_SmartEnergy.StringMesFechamento(data_hora_fim);
            ViewBag.Fatura_Ant1_DataMes = Funcoes_SmartEnergy.StringMesFechamento(data_hora_fim, "MMMM");

            ViewBag.Fatura_Ant1_ValorTotal = string.Format("{0:C}", faturaAnt1.total);
            ViewBag.Fatura_Ant1_ValorTotalN = faturaAnt1.total;

            ViewBag.Fatura_Ant1_CustoMedio = string.Format("{0:C} / {1}", CustoMedio, ViewBag.UnidadeGrandeza);
            ViewBag.Fatura_Ant1_CustoMedioN = CustoMedio;

            //
            // FATURA ANO -1
            //

            // ano anterior
            data_hora_ini = data_atual_ini.AddYears(-1);
            data_hora_fim = data_atual_fim.AddYears(-1);

            // converte para DateHora
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, data_hora_ini);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, data_hora_fim);

            // calcula valores fatura
            retorno = SmCalcDB_Util_Fatura((char)0, ref config_interface, ref dh_ini, ref dh_fim, ref faturaAnt2);

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Fatura {0}: Erro {1}", string.Format("{0:Y}", data_hora_ini), retorno));
                if (fatura.flag_registros > 0)
                    listaErros.Add(string.Format("Fatura {0}: {1}", string.Format("{0:Y}", data_hora_ini), Funcoes_SmartEnergy.ErroRelatorio((int)fatura.flag_registros)));
            }

            // calcula custo medio
            CustoMedio = (faturaAnt2.consumo_total == 0.0) ? 0.0 : (faturaAnt2.total / faturaAnt2.consumo_total);

            // tabela totais
            ViewBag.Fatura_Ant2_Data = Funcoes_SmartEnergy.StringMesFechamento(data_hora_fim);
            ViewBag.Fatura_Ant2_DataMes = Funcoes_SmartEnergy.StringMesFechamento(data_hora_fim, "MMMM");

            ViewBag.Fatura_Ant2_ValorTotal = string.Format("{0:C}", faturaAnt2.total);
            ViewBag.Fatura_Ant2_ValorTotalN = faturaAnt2.total;

            ViewBag.Fatura_Ant2_CustoMedio = string.Format("{0:C} / {1}", CustoMedio, ViewBag.UnidadeGrandeza);
            ViewBag.Fatura_Ant2_CustoMedioN = CustoMedio;

            // lista de erros
            @ViewBag.listaErros = listaErros;

            return;
        }

        // Fatura Utilidades XLS
        private HSSFWorkbook Fatura_Utilidades_Tipo1_XLS(int IDCliente, int IDMedicao)
        {
            // fatura
            RESULT_UTIL_FATURA fatura = ViewBag.Fatura;

            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _5CellStyle = criaEstiloXLS(workbook, 5);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // FATURA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Fatura");
            IRow row;

            // cabecalho
            string[] cabecalho1 = { "Descrição", string.Format("Faturado ({0})", ViewBag.UnidadeGrandeza), "Tarifa (R$)", "Valor (R$)" };
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho1);

            // faixas de consumo
            int faixa = 0;
            double[] consumo_faixa = new double[5];

            for (faixa = 0; faixa < 6; faixa++)
            {
                switch(faixa)
                {
                    case 0:     // faixa 1
                        consumo_faixa = fatura.consumo_faixa1;
                        break;

                    case 1:     // faixa 2
                        consumo_faixa = fatura.consumo_faixa2;
                        break;

                    case 2:     // faixa 3
                        consumo_faixa = fatura.consumo_faixa3;
                        break;

                    case 3:     // faixa 4
                        consumo_faixa = fatura.consumo_faixa4;
                        break;
                                        
                    case 4:     // faixa 5
                        consumo_faixa = fatura.consumo_faixa5;
                        break;
                                        
                    case 5:     // faixa 6
                        consumo_faixa = fatura.consumo_faixa6;
                        break;
                }
                                
                // texto faixa
                string texto_faixa = "";
                                
                // verifica se faixa valida
                if( consumo_faixa[1] > 0.0 )
                {
                    // verifica se faixa final
                    if( consumo_faixa[0] == consumo_faixa[1] )
                    {
                        // acima de ...
                        texto_faixa = string.Format("{0} {1:0}", SmartEnergy.Resources.FinancasTexts.acima, consumo_faixa[0]);
                    }
                    else
                    {
                        // verifica se faixa inicial
                        if (consumo_faixa[0] == 0.0)
                        {
                            // ate ...
                            texto_faixa = string.Format("{0} {1:0}", SmartEnergy.Resources.FinancasTexts.ate, consumo_faixa[1]);
                        }
                        else
                        {
                            // ... ate ...
                            texto_faixa = string.Format("{0:0} {1} {2:0}", consumo_faixa[0], SmartEnergy.Resources.FinancasTexts.ate, consumo_faixa[1]);
                        }
                    }

                    // faixa de consumo
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, string.Format("Faixa de Consumo [{0} {1}]",texto_faixa, ViewBag.UnidadeGrandeza));
                    numeroCelulaXLS(row, 1, consumo_faixa[2], _2CellStyle);
                    numeroCelulaXLS(row, 2, consumo_faixa[3], _2CellStyle);
                    numeroCelulaXLS(row, 3, consumo_faixa[4], _2CellStyle);
                }
            }

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // consumo medio
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Consumo Horário Médio");
            numeroCelulaXLS(row, 1, fatura.consumo_med, _2CellStyle);

            // consumo minimo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Consumo Horário Mínimo");
            numeroCelulaXLS(row, 1, fatura.consumo_min, _2CellStyle);

            // consumo maximo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Consumo Horário Máximo");
            numeroCelulaXLS(row, 1, fatura.consumo_max, _2CellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // consumo total
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Consumo Total");
            numeroCelulaXLS(row, 1, fatura.consumo_total, _2CellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // OUTROS
            List<FaturaOutrosDominio> listaOutros = @ViewBag.listaOutros;

            if (listaOutros != null)
            {
                if (listaOutros.Count > 0)
                {
                    // cabecalho
                    string[] cabecalho11 = { "Outros", " ", " ", "Valor (R$)" };
                    cabecalhoTabelaXLS(workbook, sheet, cabecalho11, rowIndex++);

                    foreach (FaturaOutrosDominio outros in listaOutros)
                    {
                        row = sheet.CreateRow(rowIndex++);
                        textoCelulaXLS(row, 0, outros.Descricao);
                        numeroCelulaXLS(row, 3, outros.valorOutros, _2CellStyle);
                    }

                    // pula linha
                    row = sheet.CreateRow(rowIndex++);
                }
            }

            // TOTAL

            // cabecalho
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "TOTAL", _negritoCellStyle);
            numeroCelulaXLS(row, 3, fatura.total, _2CellStyle);

            // largura de cada coluna
            for (int i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 10000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fatura de Utilidades", "", _negritoCellStyle);

            // grupo de tarifas
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Grupo de Tarifas", _negritoCellStyle);
            textoCelulaXLS(row, 1, @ViewBag.TipoTarifasUtilGrupos);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // cabecalho
            string[] cabecalho = { "Mês", "Total (R$)", string.Format("Custo Médio (R$ / {0})", ViewBag.UnidadeGrandeza) };
            cabecalhoTabelaXLS(workbook, sheet, cabecalho, rowIndex++);

            // mes atual
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, @ViewBag.Fatura_Atual_Data, _negritoCellStyle);
            numeroCelulaXLS(row, 1, @ViewBag.Fatura_Atual_ValorTotalN, _2CellStyle);
            numeroCelulaXLS(row, 2, @ViewBag.Fatura_Atual_CustoMedioN, _2CellStyle);

            // mes anterior1
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, @ViewBag.Fatura_Ant1_Data, _negritoCellStyle);
            numeroCelulaXLS(row, 1, @ViewBag.Fatura_Ant1_ValorTotalN, _2CellStyle);
            numeroCelulaXLS(row, 2, @ViewBag.Fatura_Ant1_CustoMedioN, _2CellStyle);

            // mes anterior2
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, @ViewBag.Fatura_Ant2_Data, _negritoCellStyle);
            numeroCelulaXLS(row, 1, @ViewBag.Fatura_Ant2_ValorTotalN, _2CellStyle);
            numeroCelulaXLS(row, 2, @ViewBag.Fatura_Ant2_CustoMedioN, _2CellStyle);

            // largura de cada coluna
            for (int i = 0; i < sheet.GetRow(16).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }
    }
}