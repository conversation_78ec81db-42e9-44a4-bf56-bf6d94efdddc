﻿using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class FinancasController
    {

        // GET: Fatura de Energia Eletrica - Historico
        public ActionResult Fatura_Energia_Historico(int IDCliente, int IDMedicao)
        {
            // tela de ajuda - fatura
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permisso<PERSON>();

            // le supervisao da medicao
            SupervMedicoesMetodos supmedicoesMetodos = new SupervMedicoesMetodos();
            var listaMedicoes = supmedicoesMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

            // salva cookie 
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", listaMedicoes.IDTipoMedicao_MD);
            CookieStore.SalvaCookie_String("MedicaoNome", listaMedicoes.NMED);
            CookieStore.SalvaCookie_String("UnidadeNome", listaMedicoes.NUN);
            CookieStore.SalvaCookie_String("GrupoNome", listaMedicoes.NGU);
            CookieStore.SalvaCookie_Int("_IDGateway", listaMedicoes.IDGW_GW);
            CookieStore.SalvaCookie_Int("IDTipoGateway", listaMedicoes.IDTipoGateway_GW);
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Fatura");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le faturas
            FaturasMetodos faturaMetodos = new FaturasMetodos();
            List<FaturasDominio> listaFaturas = faturaMetodos.ListarTodos(IDMedicao);
            ViewBag.listaFaturas = listaFaturas;

            return View();
        }

        // GET: Fatura de Energia Eletrica - Editar
        public ActionResult Fatura_Energia_Editar(int IDFatura)
        {
            // le fatura
            FaturasMetodos faturaMetodos = new FaturasMetodos();
            FaturasDominio fatura = faturaMetodos.ListarPorId(IDFatura);

            // IDCliente e IDMedicao
            int IDCliente = fatura.IDCliente;
            int IDMedicao = fatura.IDMedicao;

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", fatura.DataHoraIni);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", fatura.DataHoraFim);


            // fatura de energia
            return (Fatura_Energia_Show(IDCliente, IDMedicao));
        }

        // GET: Fatura de Energia Eletrica - Excluir
        public ActionResult Fatura_Energia_Excluir(int IDFatura)
        {
            // apaga a fatura
            FaturasMetodos faturaMetodos = new FaturasMetodos();
            faturaMetodos.Excluir(IDFatura);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}