﻿using NPOI.HSSF.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Web.Hosting;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;
using System.Text.RegularExpressions;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class FinancasController
    {
        // fatura de energia eletrica
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_Fatura", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_Fatura(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, int id_simulacao, char tipo_contrato, char IDEstTarifaria, char <PERSON>h<PERSON><PERSON><PERSON><PERSON>, ref DATAHORA pdatahora_ini, ref DATAHOR<PERSON> pdatahora_fim, ref RESULT_ENERGIA_FATURA pfatura, ref RESULT_ENERGIA_FATURA pfatura_sim);

        // preenche lista com os periodos de reposicao de demanda
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Fechamentos_RepDemanda", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Fechamentos_RepDemanda(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref PERIODO_REP_DEMANDA prep_demanda);

        // atualiza fechamentos
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_AtualizaFechamentos", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_AtualizaFechamentos(char tipo_interface, ref CONFIG_INTERFACE cfg_interface);


        // GET: Fatura de Energia Eletrica
        public ActionResult Fatura_Energia(int IDCliente, int IDMedicao, int tipo_arquivo = 0)
        {
            // fatura de energia
            return (Fatura_Energia_Show(IDCliente, IDMedicao, tipo_arquivo));
        }

        // GET: Fatura Energia
        private ActionResult Fatura_Energia_Show(int IDCliente, int IDMedicao, int tipo_arquivo = 0)
        {
            // tela de ajuda - fatura
            CookieStore.SalvaCookie_String("PaginaAjuda", "Financas_Fatura_Energia");

            // le supervisao da medicao
            SupervMedicoesMetodos supmedicoesMetodos = new SupervMedicoesMetodos();
            SupervMedicoesDominio listaMedicoes = supmedicoesMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

            // salva cookie 
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", listaMedicoes.IDTipoMedicao_MD);
            CookieStore.SalvaCookie_String("MedicaoNome", listaMedicoes.NMED);
            CookieStore.SalvaCookie_String("UnidadeNome", listaMedicoes.NUN);
            CookieStore.SalvaCookie_String("GrupoNome", listaMedicoes.NGU);
            CookieStore.SalvaCookie_Int("_IDGateway", listaMedicoes.IDGW_GW);
            CookieStore.SalvaCookie_Int("IDTipoGateway", listaMedicoes.IDTipoGateway_GW);
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Fatura");

            int IDGateway = listaMedicoes.IDGW_GW;
            int IDTipoGateway = listaMedicoes.IDTipoGateway_GW;
            int IDGateway_Fechamentos = listaMedicoes.IDGateway_Fechamentos;

            // le cookies
            LeCookies_SmartEnergy();


            // simulacao 
            int IDSimulacaoCenario = 0;
            string NomeSimulacaoCenario = "";

            if (ViewBag._IDSimulacaoCenario != null)
            {
                // copia
                IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;
            }

            // verifica se deve aplicar simulacao
            bool aplicaSimulacao = false;

            if (ViewBag.AplicaSimulacao != null)
            {
                // copia
                aplicaSimulacao = ViewBag.AplicaSimulacao;
            }

            // verifica se possui cenário selecionado
            if (IDSimulacaoCenario > 0)
            {
                SimulacaoCenariosMetodos simMetodos = new SimulacaoCenariosMetodos();
                SimulacaoCenariosDominio simulacao = simMetodos.ListarPorID(IDSimulacaoCenario);

                // nome do cenário
                NomeSimulacaoCenario = simulacao.Nome;

                // verifica se este cenário corresponde a medição atual
                if (simulacao.IDMedicao != IDMedicao)
                {
                    IDSimulacaoCenario = 0;
                    NomeSimulacaoCenario = "";
                    aplicaSimulacao = false;
                }
            }

            ViewBag._NomeSimulacaoCenario = NomeSimulacaoCenario;

            CookieStore.SalvaCookie_Bool("AplicaSimulacao", aplicaSimulacao);
            CookieStore.SalvaCookie_Int("_IDSimulacaoCenario", IDSimulacaoCenario);
            CookieStore.SalvaCookie_String("_NomeSimulacaoCenario", NomeSimulacaoCenario);

            // verifica se NÃO deve aplicar simulação
            if (!aplicaSimulacao)
            {
                // zero cenário para gerar a fatura sem simulação
                IDSimulacaoCenario = 0;
            }


            // atualizo fechamentos
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = IDGateway;

            // verifica se deve utilizar outra gateway
            if (IDGateway_Fechamentos > 0)
            {
                config_interface.sweb.id_gateway = IDGateway_Fechamentos;
            }

            int retorno = SmCalcDB_AtualizaFechamentos((char)0, ref config_interface);

            // configuracao da medicao
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le cookie TipoContrato
            int TipoContrato = CookieStore.LeCookie_Int("Fatura_TipoContrato");

            if (TipoContrato < 0)
            {
                TipoContrato = medicao.IDContratoMedicao;
            }

            // le cookie TipoFatura
            int TipoFatura = CookieStore.LeCookie_Int("Fatura_TipoFatura");

            if( TipoFatura < 0 )
            {
                TipoFatura = medicao.IDEstruturaTarifaria;
            }

            // le tipos contrato medicao
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposContratoMedicao = listatiposMetodos.ListarTodos("TipoContratoMedicao");
            ListaTiposDominio tipoContratoMedicao = listatiposContratoMedicao.Find(item => item.ID == TipoContrato);
            if (tipoContratoMedicao != null)
            {
                ViewBag.TipoContratoMedicao = tipoContratoMedicao.Descricao;
            }
            else
            {
                ViewBag.TipoContratoMedicao = "";
            }

            // le distribuidoras
            List<ListaTiposDominio> listatiposDistribuidora = listatiposMetodos.ListarTodos("AgentesDistribuidora");
            ListaTiposDominio tipodistribuidora = listatiposDistribuidora.Find(item => item.ID == medicao.IDAgenteDistribuidora);
            if (tipodistribuidora != null)
            {
                ViewBag.TipoDistribuidora = tipodistribuidora.Descricao;
            }
            else
            {
                ViewBag.TipoDistribuidora = "";
            }

            // le tipos estrutura tarifaria - subgrupo
            List<EstruturaTarifariaSubgrupoDominio> listatiposEstruturaTarifaria = listatiposMetodos.ListarTodos_EstruturaTarifaria();
            string Descricao_Estrutura_Subgrupo = "";

            foreach (EstruturaTarifariaSubgrupoDominio tipo in listatiposEstruturaTarifaria)
            {
                if (tipo.IDEstruturaTarifaria == TipoFatura && tipo.IDTipoSubgrupo == medicao.IDTipoSubgrupo)
                {
                    Descricao_Estrutura_Subgrupo = tipo.Descricao_Estrutura_Subgrupo;
                    break;
                }
            }

            ViewBag.TipoEstruturaTarifariaSubgrupo = Descricao_Estrutura_Subgrupo;

            // le tipos SubSistema
            List<ListaTiposDominio> listatiposSubSistema = listatiposMetodos.ListarTodos("TipoSubSistema");
            ListaTiposDominio tiposubsistema = listatiposSubSistema.Find(item => item.ID == medicao.IDSubSistema);
            if (tiposubsistema != null)
            {
                ViewBag.TipoSubSistema = tiposubsistema.Descricao;
            }
            else
            {
                ViewBag.tiposubsistema = "";
            }

            // data inicio
            DateTime data_hora_ini = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 0, 0);

            // data fim
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            // le cookie datahora
            DateTime data_hora_ini_cookie = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime data_hora_fim_cookie = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // verifica se existe datahora no cookie
            if (data_hora_ini_cookie.Year != 2000 && data_hora_fim_cookie.Year != 2000)
            {
                // copia data do cookie 
                data_hora_ini = data_hora_ini_cookie;
                data_hora_fim = data_hora_fim_cookie;
            }

            // converte
            DATAHORA dh_ini = new DATAHORA();
            DATAHORA dh_fim = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, data_hora_ini);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, data_hora_fim);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", data_hora_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", data_hora_fim);

            // data e hora atual
            ViewBag.DataIni = string.Format("{0:d}", data_hora_ini);
            ViewBag.HoraIni = string.Format("{0:HH:mm}", data_hora_ini);
            ViewBag.DataFim = string.Format("{0:d}", data_hora_fim);
            ViewBag.HoraFim = string.Format("{0:HH:mm}", data_hora_fim);

            ViewBag.DataTextoAtualIni = string.Format("{0:d} {1:HH:mm}", data_hora_ini, data_hora_ini);
            ViewBag.DataTextoAtualFim = string.Format("{0:d} {1:HH:mm}", data_hora_fim, data_hora_fim);

            ViewBag.DataAtualN = data_hora_ini;
            ViewBag.DataFinalN = data_hora_fim;

            // tipo do contrato
            ViewBag.TipoContrato = TipoContrato;

            // tipo da fatura
            ViewBag.TipoFatura = TipoFatura;
            string viewRelatorio = "";

            // prepara fechamentos
            Prepara_Fechamentos(IDCliente, IDMedicao, IDGateway, IDGateway_Fechamentos);

            // Planilha Excel
            HSSFWorkbook workbook = new HSSFWorkbook();

            // calcula fatura
            switch (TipoFatura)
            {
                case 0: // Fatura Azul
                    Fatura_Energia_Azul(IDCliente, IDMedicao, IDGateway, dh_ini, dh_fim, TipoContrato, IDSimulacaoCenario);
                    viewRelatorio = "_Fatura_Energia_Azul";

                    if (tipo_arquivo == 2)
                        workbook = Fatura_Energia_Azul_XLS(IDCliente, IDMedicao);

                    break;

                case 1: // Fatura Verde
                    Fatura_Energia_Verde(IDCliente, IDMedicao, IDGateway, dh_ini, dh_fim, TipoContrato, IDSimulacaoCenario);
                    viewRelatorio = "_Fatura_Energia_Verde";

                    if (tipo_arquivo == 2)
                        workbook = Fatura_Energia_Verde_XLS(IDCliente, IDMedicao);

                    break;

                case 2: // Fatura Convencional

                    // demanda historica
                    string DemandaHistorica = CookieStore.LeCookie_String("Fatura_DemandaHistorica");

                    // demanda historica
                    double DemandaHistorica_valor;

                    if (!double.TryParse(DemandaHistorica, out DemandaHistorica_valor))
                    {
                        DemandaHistorica_valor = 0.0;
                    }

                    Fatura_Energia_Convencional(IDCliente, IDMedicao, IDGateway, dh_ini, dh_fim, DemandaHistorica_valor, TipoContrato, IDSimulacaoCenario);
                    viewRelatorio = "_Fatura_Energia_Convencional";

                    if (tipo_arquivo == 2)
                        workbook = Fatura_Energia_Convencional_XLS(IDCliente, IDMedicao);

                    break;
            }

            // verifica se PDF
            if (tipo_arquivo == 1)
            {
                // tiro caracteres especiais do nome
                Regex regex = new Regex("[^a-zA-Z0-9]");
                string novo_nome = regex.Replace(medicao.Nome, "");

                // nome do arquivo
                string nomeArquivo = string.Format("Fatura_{0}_ID{1:000000}_{2:MMMMyyyy}.pdf", novo_nome, IDMedicao, data_hora_ini);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Portrait,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                string caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna nome do arquivo
                var returnedData = new
                {
                    nomeArquivo = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);

                // abaixo original para fazer o download imediato do PDF sem usar o AJAX
                // utilizar a chamda na View:
                // <a href='@Url.Action("Relat_Grafico_PDF", "Relatorios")'><i class="fa fa-file-pdf-o" data-toggle="tooltip" data-placement="left" title="PDF"></i></a>
                // retorna PDF
                //return new Rotativa.PartialViewAsPdf(viewRelatorio) { 
                //    FileName = nomeArquivo,
                //    PageOrientation = Orientation.Portrait,
                //    PageSize = Size.A4,
                //}; 
            }

            // verifica se XLS
            if (tipo_arquivo == 2)
            {
                // gera identificador unico aonde o arquivo sera guardado
                string handle = Guid.NewGuid().ToString();

                using (MemoryStream memoryStream = new MemoryStream())
                {
                    workbook.Write(memoryStream);
                    memoryStream.Position = 0;
                    Session[handle] = memoryStream.ToArray();
                }

                // tiro caracteres especiais do nome
                Regex regex = new Regex("[^a-zA-Z0-9]");
                string novo_nome = regex.Replace(medicao.Nome, "");

                // nome do arquivo
                string nomeArquivo = string.Format("Fatura_{0}_ID{1:000000}_{2:MMMMyyyy}.xls", novo_nome, IDMedicao, data_hora_ini);

                // retorna nome do arquivo
                var returnedData = new
                {
                    FileGuid = handle,
                    FileName = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);

            }

            // verifica se EMAIL
            if (tipo_arquivo == 3)
            {
                // tiro caracteres especiais do nome
                Regex regex = new Regex("[^a-zA-Z0-9]");
                string novo_nome = regex.Replace(medicao.Nome, "");

                // nome do arquivo
                string nomeArquivo = string.Format("Fatura_{0}_ID{1:000000}_{2:MMMMyyyy}.pdf", novo_nome, IDMedicao, data_hora_ini);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Portrait,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna 
                ViewBag.caminhoCompleto = caminhoCompleto;
                var returnedData = new
                {
                    caminhoCompleto = caminhoCompleto
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            return View();
        }
        
        // GET: Fatura de Energia Print
        public ActionResult Fatura_Energia_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // imprime
            return Fatura_Energia_Show(IDCliente, IDMedicao, 4);
        }

        public static async Task<string> EMailTemplate(string template)
        {
            var templateFilePath = HostingEnvironment.MapPath("~/Content/emailTemplates/") + template + ".cshtml";
            StreamReader objstreamreaderfile = new StreamReader(templateFilePath);
            var body = await objstreamreaderfile.ReadToEndAsync();
            objstreamreaderfile.Close();
            return body;
        }

        // GET: Fatura de Energia EMAIL
        public async Task<ActionResult> Fatura_Energia_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // gera relatorio
            var retorno = Fatura_Energia_Show(IDCliente, IDMedicao, 3);

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(ViewBag.caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "FaturaEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.NomeRelat", ViewBag.NomeRelat);
            message = message.Replace("ViewBag.DataTextoAtualIni", ViewBag.DataTextoAtualIni);
            message = message.Replace("ViewBag.DataTextoAtualFim", ViewBag.DataTextoAtualFim);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.GrupoNome", ViewBag.GrupoNome);
            message = message.Replace("ViewBag.UnidadeNome", ViewBag.UnidadeNome);
            message = message.Replace("ViewBag.MedicaoNome", ViewBag.MedicaoNome);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Fatura de Energia PDF
        public ActionResult Fatura_Energia_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;
            int TipoRelat = ViewBag.Relat_Tipo;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // relatorio para PDF
            return Fatura_Energia_Show(IDCliente, IDMedicao, 1);
        }

        // GET: Fatura de Energia XLS
        public ActionResult Fatura_Energia_XLS()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // fatura para PDF
            return Fatura_Energia_Show(IDCliente, IDMedicao, 2);
        }

        // GET: Fatura de Energia XLS Download
        [HttpGet]
        public virtual ActionResult Fatura_Energia_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Fatura Atualizar
        public PartialViewResult _Fatura_Energia_Atualizar(int TipoContrato, int TipoFatura, int Navegacao, string DataIni, string DataFim, string DemandaHistorica)
        {
            // caso forcar data
            if (Navegacao == 100)
            {
                // pega data
                DateTime dateValueIni = DateTime.Parse(DataIni);
                DateTime dateValueFim = DateTime.Parse(DataFim);

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Fatura_DataIni", dateValueIni);
                CookieStore.SalvaCookie_Datahora("Fatura_DataFim", dateValueFim);
            }

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;
            int IDGateway = ViewBag._IDGateway;


            // simulacao 
            int IDSimulacaoCenario = 0;
            string NomeSimulacaoCenario = "";

            if (ViewBag._IDSimulacaoCenario != null)
            {
                // copia
                IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;
            }

            // verifica se deve aplicar simulacao
            bool aplicaSimulacao = false;

            if (ViewBag.AplicaSimulacao != null)
            {
                // copia
                aplicaSimulacao = ViewBag.AplicaSimulacao;
            }

            // verifica se possui cenário selecionado
            if (IDSimulacaoCenario > 0)
            {
                SimulacaoCenariosMetodos simMetodos = new SimulacaoCenariosMetodos();
                SimulacaoCenariosDominio simulacao = simMetodos.ListarPorID(IDSimulacaoCenario);

                // nome do cenário
                NomeSimulacaoCenario = simulacao.Nome;

                // verifica se este cenário corresponde a medição atual
                if (simulacao.IDMedicao != IDMedicao)
                {
                    IDSimulacaoCenario = 0;
                    NomeSimulacaoCenario = "";
                    aplicaSimulacao = false;
                }
            }

            CookieStore.SalvaCookie_Bool("AplicaSimulacao", aplicaSimulacao);
            CookieStore.SalvaCookie_Int("_IDSimulacaoCenario", IDSimulacaoCenario);
            CookieStore.SalvaCookie_String("_NomeSimulacaoCenario", NomeSimulacaoCenario);

            // verifica se NÃO deve aplicar simulação
            if (!aplicaSimulacao)
            {
                // zero cenário para gerar a fatura sem simulação
                IDSimulacaoCenario = 0;
            }


            // configuracao da medicao
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le tipos distribuidora
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposDistribuidora = listatiposMetodos.ListarTodos("AgentesDistribuidora");
            ListaTiposDominio tipodistribuidora = listatiposDistribuidora.Find(item => item.ID == medicao.IDAgenteDistribuidora);
            if (tipodistribuidora != null)
            {
                ViewBag.TipoDistribuidora = tipodistribuidora.Descricao;
            }
            else
            {
                ViewBag.TipoDistribuidora = "";
            }

            // le tipos estrutura tarifaria - subgrupo
            List<EstruturaTarifariaSubgrupoDominio> listatiposEstruturaTarifaria = listatiposMetodos.ListarTodos_EstruturaTarifaria();
            string Descricao_Estrutura_Subgrupo = "";

            foreach(EstruturaTarifariaSubgrupoDominio tipo in listatiposEstruturaTarifaria)
            {
                if( tipo.IDEstruturaTarifaria == TipoFatura && tipo.IDTipoSubgrupo == medicao.IDTipoSubgrupo )
                {
                    Descricao_Estrutura_Subgrupo = tipo.Descricao_Estrutura_Subgrupo;
                    break;
                }
            }

            ViewBag.TipoEstruturaTarifariaSubgrupo = Descricao_Estrutura_Subgrupo;

            // le tipos SubSistema
            List<ListaTiposDominio> listatiposSubSistema = listatiposMetodos.ListarTodos("TipoSubSistema");
            ListaTiposDominio tiposubsistema = listatiposSubSistema.Find(item => item.ID == medicao.IDSubSistema);
            if (tiposubsistema != null)
            {
                ViewBag.TipoSubSistema = tiposubsistema.Descricao;
            }
            else
            {
                ViewBag.tiposubsistema = "";
            }

            // le cookie datahora
            DateTime datahora_cookie_ini = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Fatura_DataFim");
            DATAHORA dh_ini = new DATAHORA();
            DATAHORA dh_fim = new DATAHORA();

            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -3:    // mes anterior

                    datahora_cookie_ini = datahora_cookie_ini.AddMonths(-1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(-1);
                    break;

                case 3:    // mes seguinte

                    datahora_cookie_ini = datahora_cookie_ini.AddMonths(1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(1);
                    break;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", datahora_cookie_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", datahora_cookie_fim);

            // data e hora atual
            ViewBag.DataIni = string.Format("{0:d}", datahora_cookie_ini);
            ViewBag.HoraIni = string.Format("{0:HH:mm}", datahora_cookie_ini);
            ViewBag.DataFim = string.Format("{0:d}", datahora_cookie_fim);
            ViewBag.HoraFim = string.Format("{0:HH:mm}", datahora_cookie_fim);

            ViewBag.DataTextoAtualIni = string.Format("{0:d} {1:HH:mm:ss}", datahora_cookie_ini, datahora_cookie_ini);
            ViewBag.DataTextoAtualFim = string.Format("{0:d} {1:HH:mm:ss}", datahora_cookie_fim, datahora_cookie_fim);

            ViewBag.DataAtualN = datahora_cookie_ini;
            ViewBag.DataFinalN = datahora_cookie_fim;

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, datahora_cookie_ini);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, datahora_cookie_fim);

            // tipo do contrato
            ViewBag.TipoContrato = TipoContrato;

            // salva cookie TipoContrato
            CookieStore.SalvaCookie_Int("Fatura_TipoContrato", TipoContrato);

            // tipo da fatura
            ViewBag.TipoFatura = TipoFatura;

            // salva cookie TipoFatura
            CookieStore.SalvaCookie_Int("Fatura_TipoFatura", TipoFatura);

            // prepara fechamentos
            Prepara_Fechamentos(IDCliente, IDMedicao, IDGateway, medicao.IDGateway_Fechamentos);

            // calcula fatura
            switch (TipoFatura)
            {
                case 0: // Fatura Azul
                    Fatura_Energia_Azul(IDCliente, IDMedicao, IDGateway, dh_ini, dh_fim, TipoContrato, IDSimulacaoCenario);
                    break;

                case 1: // Fatura Verde
                    Fatura_Energia_Verde(IDCliente, IDMedicao, IDGateway, dh_ini, dh_fim, TipoContrato, IDSimulacaoCenario);
                    break;

                case 2: // Fatura Convencional

                    // salva cookie datahora
                    CookieStore.SalvaCookie_String("Fatura_DemandaHistorica", DemandaHistorica);

                    // demanda historica
                    double DemandaHistorica_valor;

                    if (!double.TryParse(DemandaHistorica, out DemandaHistorica_valor))
                    {
                        DemandaHistorica_valor = 0.0;
                    }

                    Fatura_Energia_Convencional(IDCliente, IDMedicao, IDGateway, dh_ini, dh_fim, DemandaHistorica_valor, TipoContrato, IDSimulacaoCenario);
                    break;
            }

            return PartialView();
        }

        // GET: Prepara fechamentos
        private void Prepara_Fechamentos(int IDCliente, int IDMedicao, int IDGateway, int IDGateway_Fechamentos)
        {
            // funcao de fechamentos
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            PERIODO_REP_DEMANDA fechamentos = new PERIODO_REP_DEMANDA();

            List<string> FechamentosCativo = new List<string>();
            List<string> FechamentosCativo_Texto = new List<string>();

            List<string> FechamentosLivre = new List<string>();
            List<string> FechamentosLivre_Texto = new List<string>();


            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = IDGateway;

            // verifica se deve utilizar outra gateway
            if (IDGateway_Fechamentos > 0)
            {
                config_interface.sweb.id_gateway = IDGateway_Fechamentos;
            }

            // le fechamentos
            retorno = SmCalcDB_Fechamentos_RepDemanda((char)0, ref config_interface, ref fechamentos);

            // verifica se nao ocorreu erro
            if (retorno == 0)
            {
                // verifica se tem fechamentos
                if (fechamentos.num_rep_dem > 0)
                {
                    //
                    // CATIVO
                    //

                    FechamentosCativo.Add("Selecionar o Ciclo de Faturamento");
                    FechamentosCativo_Texto.Add("Selecionar o Ciclo de Faturamento");

                    // insere data atual
                    DateTime ini = Funcoes_Converte.ConverteDataHora2DateTime(fechamentos.datahora_fim[0]);
                    DateTime fim = ini.AddMonths(1);

                    string fechamentoCativo = string.Format("{0:dd/MM/yyyy HH:mm} - {1:dd/MM/yyyy HH:mm}", ini, fim);

                    // coloca na lista
                    FechamentosCativo.Add(fechamentoCativo);

                    // string do mes
                    fechamentoCativo = Funcoes_SmartEnergy.StringMesFechamento(fim);

                    // insere
                    FechamentosCativo_Texto.Add(fechamentoCativo);

                    // percorre fechamentos
                    for (int i = 0; i < fechamentos.num_rep_dem; i++)
                    {
                        // converta data e hora
                        ini = Funcoes_Converte.ConverteDataHora2DateTime(fechamentos.datahora_ini[i]);
                        fim = Funcoes_Converte.ConverteDataHora2DateTime(fechamentos.datahora_fim[i]);

                        string fechaCativo = string.Format("{0:dd/MM/yyyy HH:mm} - {1:dd/MM/yyyy HH:mm}", ini, fim);

                        // coloca na lista
                        FechamentosCativo.Add(fechaCativo);

                        // string do mes
                        fechaCativo = Funcoes_SmartEnergy.StringMesFechamento(fim);

                        // insere
                        FechamentosCativo_Texto.Add(fechaCativo);
                    }


                    //
                    // MERCADO LIVRE
                    //

                    FechamentosLivre.Add("Selecionar o Ciclo de Faturamento");
                    FechamentosLivre_Texto.Add("Selecionar o Ciclo de Faturamento");

                    // ultimo indice
                    int indice_ultimo = (fechamentos.num_rep_dem > 1) ? fechamentos.num_rep_dem - 1 : 0;

                    // primeira data
                    DateTime temp = Funcoes_Converte.ConverteDataHora2DateTime(fechamentos.datahora_fim[0]);
                    ini = new DateTime(temp.Year, temp.Month, 1, 0, 0, 0);

                    // ultima data
                    temp = Funcoes_Converte.ConverteDataHora2DateTime(fechamentos.datahora_ini[indice_ultimo]);
                    fim = new DateTime(temp.Year, temp.Month, 1, 0, 0, 0);

                    // percorre meses para montar fechamentos
                    while (ini >= fim)
                    {
                        // fechamento
                        string fechaLivre = string.Format("{0:01/MM/yyyy 00:00} - {1:01/MM/yyyy 00:00}", ini, ini.AddMonths(1));

                        // coloca na lista
                        FechamentosLivre.Add(fechaLivre);

                        // string do mes
                        fechaLivre = Funcoes_SmartEnergy.StringMesFechamento(ini.AddMonths(1));

                        // insere
                        FechamentosLivre_Texto.Add(fechaLivre);

                        // proximo
                        ini = ini.AddMonths(-1);
                    }
                }
            }


            // verifica se listas vazias
            if (FechamentosCativo.Count() == 0 || FechamentosLivre.Count() == 0)
            {
                // leio primeiro e último registro EN
                EN_Metodos enMetodos = new EN_Metodos();
                EN_Dominio maisAntigo = enMetodos.ListarMaisAntigo(IDCliente, IDMedicao);
                EN_Dominio maisRecente = enMetodos.ListarMaisRecente(IDCliente, IDMedicao);

                // cria lista com mes civil
                if (maisAntigo != null && maisRecente != null)
                {
                    // Cativo
                    if (FechamentosCativo.Count() == 0)
                    {
                        FechamentosCativo.Add("Selecionar o Ciclo de Faturamento");
                        FechamentosCativo_Texto.Add("Selecionar o Ciclo de Faturamento");

                        // primeira data
                        DateTime temp = maisRecente.DataHora;
                        DateTime ini = new DateTime(temp.Year, temp.Month, 1, 0, 0, 0);

                        // ultima data
                        temp = maisAntigo.DataHora;
                        DateTime fim = new DateTime(temp.Year, temp.Month, 1, 0, 0, 0);

                        // percorre meses para montar fechamentos
                        while (ini >= fim)
                        {
                            // fechamento
                            string fechaCativo = string.Format("{0:01/MM/yyyy 00:00} - {1:01/MM/yyyy 00:00}", ini, ini.AddMonths(1));

                            // coloca na lista
                            FechamentosCativo.Add(fechaCativo);

                            // string do mes
                            fechaCativo = Funcoes_SmartEnergy.StringMesFechamento(ini.AddMonths(1));

                            // insere
                            FechamentosCativo_Texto.Add(fechaCativo);

                            // proximo
                            ini = ini.AddMonths(-1);
                        }
                    }

                    // Livre
                    if (FechamentosLivre.Count() == 0)
                    {
                        FechamentosLivre.Add("Selecionar o Ciclo de Faturamento");
                        FechamentosLivre_Texto.Add("Selecionar o Ciclo de Faturamento");

                        // primeira data
                        DateTime temp = maisRecente.DataHora;
                        DateTime ini = new DateTime(temp.Year, temp.Month, 1, 0, 0, 0);

                        // ultima data
                        temp = maisAntigo.DataHora;
                        DateTime fim = new DateTime(temp.Year, temp.Month, 1, 0, 0, 0);

                        // percorre meses para montar fechamentos
                        while (ini >= fim)
                        {
                            // fechamento
                            string fechaLivre = string.Format("{0:01/MM/yyyy 00:00} - {1:01/MM/yyyy 00:00}", ini, ini.AddMonths(1));

                            // coloca na lista
                            FechamentosLivre.Add(fechaLivre);

                            // string do mes
                            fechaLivre = Funcoes_SmartEnergy.StringMesFechamento(ini.AddMonths(1));

                            // insere
                            FechamentosLivre_Texto.Add(fechaLivre);

                            // proximo
                            ini = ini.AddMonths(-1);
                        }
                    }
                }
            }

            // verifica se lista ainda vazia
            if (FechamentosCativo.Count() == 0)
            {
                // coloca na lista
                FechamentosCativo.Add("Não existem Ciclos de Faturamento");
                FechamentosCativo_Texto.Add("Não existem Ciclos de Faturamento");
            }

            ViewBag.FechamentosCativo = FechamentosCativo;
            ViewBag.FechamentosCativo_Texto = FechamentosCativo_Texto;


            // verifica se lista ainda vazia
            if (FechamentosLivre.Count() == 0)
            {
                // coloca na lista
                FechamentosLivre.Add("Não existem Ciclos de Faturamento");
                FechamentosLivre_Texto.Add("Não existem Ciclos de Faturamento");
            }

            ViewBag.FechamentosLivre = FechamentosLivre;
            ViewBag.FechamentosLivre_Texto = FechamentosLivre_Texto;

            return;
        }

        // Copia estruturas
        private FaturasDominio CopiaFaturaDLL(RESULT_ENERGIA_FATURA faturaDLL)
        {
            FaturasDominio fatura = new FaturasDominio();

            fatura.IDFatura = 0;
            fatura.IDCliente = 0;
            fatura.IDMedicao = 0;

            fatura.DataHoraIni = DateTime.Now;
            fatura.DataHoraFim = DateTime.Now;

            fatura.IDAgenteDistribuidora = 0;
            fatura.IDSubgrupo = 0;
            fatura.IDSubSistema = 0;

            fatura.num_dias = faturaDLL.num_dias;
            fatura.num_dias_pe = faturaDLL.num_dias_pe;

            fatura.bandeira_ini = faturaDLL.bandeira_ini;
            fatura.bandeira_fim = faturaDLL.bandeira_fim;

            fatura.data_bandeira_ini_i = Funcoes_Converte.ConverteDataHora2DateTime(faturaDLL.data_bandeira_ini_i);
            fatura.data_bandeira_ini_f = Funcoes_Converte.ConverteDataHora2DateTime(faturaDLL.data_bandeira_ini_f);
            fatura.data_bandeira_fim_i = Funcoes_Converte.ConverteDataHora2DateTime(faturaDLL.data_bandeira_fim_i);
            fatura.data_bandeira_fim_f = Funcoes_Converte.ConverteDataHora2DateTime(faturaDLL.data_bandeira_fim_f);

            fatura.consumo_bandeira_p_ini = new double[4];
            fatura.consumo_bandeira_p_fim = new double[4];
            fatura.consumo_bandeira_fp_ini = new double[4];
            fatura.consumo_bandeira_fp_fim = new double[4];
            fatura.consumo_bandeira_r_ini = new double[4];
            fatura.consumo_bandeira_r_fim = new double[4];

            fatura.demanda_p = new double[4];
            fatura.demanda_fpi = new double[4];
            fatura.demanda_fpc = new double[4];

            fatura.ultr_demanda_p = new double[4];
            fatura.ultr_demanda_fp = new double[4];

            fatura.demanda_pe_p = new double[4];
            fatura.demanda_pe_fpi = new double[4];
            fatura.demanda_pe_fpc = new double[4];

            fatura.ultr_demanda_pe_p = new double[4];
            fatura.ultr_demanda_pe_fp = new double[4];

            fatura.consumo_p_tusd = new double[4];
            fatura.consumo_fpi_tusd = new double[4];
            fatura.consumo_fpc_tusd = new double[4];
            fatura.consumo_r_tusd = new double[4];
            fatura.consumo_total_tusd = new double[4];

            fatura.consumo_p_te = new double[4];
            fatura.consumo_fpi_te = new double[4];
            fatura.consumo_fpc_te = new double[4];
            fatura.consumo_r_te = new double[4];
            fatura.consumo_total_te = new double[4];

            fatura.reativo_p = new double[4];
            fatura.reativo_fpi = new double[4];
            fatura.reativo_fpc = new double[4];
            fatura.reativo_r = new double[4];

            fatura.fatpot_p = new double[4];
            fatura.fatpot_fpi = new double[4];
            fatura.fatpot_fpc = new double[4];
            fatura.fatpot_r = new double[4];
            fatura.fatpot = new double[4];

            fatura.fdr_p = new double[4];
            fatura.fdr_fp = new double[4];

            fatura.fdr_pe_p = new double[4];
            fatura.fdr_pe_fp = new double[4];

            fatura.fer_p = new double[4];
            fatura.fer_fpi = new double[4];
            fatura.fer_fpc = new double[4];
            fatura.fer_r = new double[4];

            fatura.fatcar_p = new double[4];
            fatura.fatcar_fp = new double[4];

            fatura.consumo_livre_p = new double[4];
            fatura.consumo_livre_fp = new double[4];

            fatura.energia_acl_p = new double[4];
            fatura.energia_acl_fp = new double[4];

            fatura.piscofins = new double[4];
            fatura.icms = new double[4];

            fatura.encargo_covid_p = new double[4];
            fatura.encargo_covid_fp = new double[4];

            for (int i = 0; i < 4; i++)
            {
                fatura.consumo_bandeira_p_ini[i] = faturaDLL.consumo_bandeira_p_ini[i];
                fatura.consumo_bandeira_p_fim[i] = faturaDLL.consumo_bandeira_p_fim[i];
                fatura.consumo_bandeira_fp_ini[i] = faturaDLL.consumo_bandeira_fp_ini[i];
                fatura.consumo_bandeira_fp_fim[i] = faturaDLL.consumo_bandeira_fp_fim[i];
                fatura.consumo_bandeira_r_ini[i] = faturaDLL.consumo_bandeira_r_ini[i];
                fatura.consumo_bandeira_r_fim[i] = faturaDLL.consumo_bandeira_r_fim[i];

                fatura.demanda_p[i] = faturaDLL.demanda_p[i];
                fatura.demanda_fpi[i] = faturaDLL.demanda_fpi[i];
                fatura.demanda_fpc[i] = faturaDLL.demanda_fpc[i];

                fatura.ultr_demanda_p[i] = faturaDLL.ultr_demanda_p[i];
                fatura.ultr_demanda_fp[i] = faturaDLL.ultr_demanda_fp[i];

                fatura.demanda_pe_p[i] = faturaDLL.demanda_pe_p[i];
                fatura.demanda_pe_fpi[i] = faturaDLL.demanda_pe_fpi[i];
                fatura.demanda_pe_fpc[i] = faturaDLL.demanda_pe_fpc[i];

                fatura.ultr_demanda_pe_p[i] = faturaDLL.ultr_demanda_pe_p[i];
                fatura.ultr_demanda_pe_fp[i] = faturaDLL.ultr_demanda_pe_fp[i];

                fatura.consumo_p_tusd[i] = faturaDLL.consumo_p_tusd[i];
                fatura.consumo_fpi_tusd[i] = faturaDLL.consumo_fpi_tusd[i];
                fatura.consumo_fpc_tusd[i] = faturaDLL.consumo_fpc_tusd[i];
                fatura.consumo_r_tusd[i] = faturaDLL.consumo_r_tusd[i];
                fatura.consumo_total_tusd[i] = faturaDLL.consumo_total_tusd[i];

                fatura.consumo_p_te[i] = faturaDLL.consumo_p_te[i];
                fatura.consumo_fpi_te[i] = faturaDLL.consumo_fpi_te[i];
                fatura.consumo_fpc_te[i] = faturaDLL.consumo_fpc_te[i];
                fatura.consumo_r_te[i] = faturaDLL.consumo_r_te[i];
                fatura.consumo_total_te[i] = faturaDLL.consumo_total_te[i];

                fatura.reativo_p[i] = faturaDLL.reativo_p[i];
                fatura.reativo_fpi[i] = faturaDLL.reativo_fpi[i];
                fatura.reativo_fpc[i] = faturaDLL.reativo_fpc[i];
                fatura.reativo_r[i] = faturaDLL.reativo_r[i];

                fatura.fatpot_p[i] = faturaDLL.fatpot_p[i];
                fatura.fatpot_fpi[i] = faturaDLL.fatpot_fpi[i];
                fatura.fatpot_fpc[i] = faturaDLL.fatpot_fpc[i];
                fatura.fatpot_r[i] = faturaDLL.fatpot_r[i];
                fatura.fatpot[i] = faturaDLL.fatpot[i];

                fatura.fdr_p[i] = faturaDLL.fdr_p[i];
                fatura.fdr_fp[i] = faturaDLL.fdr_fp[i];

                fatura.fdr_pe_p[i] = faturaDLL.fdr_pe_p[i];
                fatura.fdr_pe_fp[i] = faturaDLL.fdr_pe_fp[i];

                fatura.fer_p[i] = faturaDLL.fer_p[i];
                fatura.fer_fpi[i] = faturaDLL.fer_fpi[i];
                fatura.fer_fpc[i] = faturaDLL.fer_fpc[i];
                fatura.fer_r[i] = faturaDLL.fer_r[i];

                fatura.fatcar_p[i] = faturaDLL.fatcar_p[i];
                fatura.fatcar_fp[i] = faturaDLL.fatcar_fp[i];

                fatura.consumo_livre_p[i] = faturaDLL.consumo_livre_p[i];
                fatura.consumo_livre_fp[i] = faturaDLL.consumo_livre_fp[i];

                fatura.energia_acl_p[i] = faturaDLL.energia_acl_p[i];
                fatura.energia_acl_fp[i] = faturaDLL.energia_acl_fp[i];

                fatura.piscofins[i] = faturaDLL.piscofins[i];
                fatura.icms[i] = faturaDLL.icms[i];

                fatura.encargo_covid_p[i] = faturaDLL.encargo_covid_p[i];
                fatura.encargo_covid_fp[i] = faturaDLL.encargo_covid_fp[i];
            }

            fatura.demanda_hist = faturaDLL.demanda_hist;

            fatura.contrato_dem_p = faturaDLL.contrato_dem_p;
            fatura.contrato_dem_fp = faturaDLL.contrato_dem_fp;

            fatura.contrato_dem_pe_p = faturaDLL.contrato_dem_pe_p;
            fatura.contrato_dem_pe_fp = faturaDLL.contrato_dem_pe_fp;

            fatura.demanda_k = faturaDLL.demanda_k;

            fatura.encargos_conexao = faturaDLL.encargos_conexao;
            fatura.desconto_demanda = faturaDLL.desconto_demanda;
            fatura.desconto_consumo_p = faturaDLL.desconto_consumo_p;
            fatura.desconto_consumo_fp = faturaDLL.desconto_consumo_fp;

            fatura.subvencao_tarifaria = faturaDLL.subvencao_tarifaria;

            fatura.per_desc_rural = faturaDLL.per_desc_rural;
            fatura.per_desc_irrigantes = faturaDLL.per_desc_irrigantes;
            fatura.desconto_rural = faturaDLL.desconto_rural;
            fatura.desconto_irrigantes = faturaDLL.desconto_irrigantes;

            fatura.subtotal = faturaDLL.subtotal;
            fatura.subtotal_A = faturaDLL.subtotal_A;
            fatura.subtotal_B = faturaDLL.subtotal_B;

            fatura.total = faturaDLL.total;

            fatura.Observacao = "";

            fatura.flag_registros = faturaDLL.flag_registros;
            fatura.flag_tusd_te = faturaDLL.flag_tusd_te;
            fatura.flag_regra_icms = faturaDLL.flag_regra_icms;
            fatura.flag_subvencao = faturaDLL.flag_subvencao;
            fatura.flag_rural = faturaDLL.flag_rural;
            fatura.flag_irrigantes = faturaDLL.flag_irrigantes;
            fatura.flag_bandeira_prop = faturaDLL.flag_bandeira_prop;
            fatura.flag_encargo_covid = faturaDLL.flag_encargo_covid;

            return (fatura);
        }
    }
}