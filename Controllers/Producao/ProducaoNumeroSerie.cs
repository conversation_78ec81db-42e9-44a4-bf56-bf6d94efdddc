﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ProducaoController
    {
        // GET: Configuracao Número de Série
        public ActionResult NumerosSerie()
        {
            // tela de ajuda - gateways
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Producao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_NumeroSerie();

            // le números de série
            NumeroSerieMetodos nsMetodos = new NumeroSerieMetodos();
            List<NumeroSerieDominio> listaNS = nsMetodos.ListarTodos();

            return View(listaNS);
        }

        // GET: Configuracao Número de Série - Editar
        public ActionResult NumeroSerie_Editar(int IDNumeroSerie)
        {
            // tela de ajuda - gateways
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // pagina anterior
            ViewBag.returnUrl = Request.UrlReferrer;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Producao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // verifica se adicionando
            NumeroSerieDominio ns = new NumeroSerieDominio();

            if (IDNumeroSerie == 0)
            {
                // zera gateway com default
                ns.IDNumeroSerie = 0;
                ns.IDTipoNS_Status = TIPO_NS_STATUS.SEM_CQ;
                ns.IDTipoGateway = TIPO_GATEWAY.GATE_E;
                ns.NS_Gateway = "";
                ns.IDCliente = 0;
                ns.IDGateway = 0;
                ns.IDTipoNS_CPU = 0;
                ns.NS_CPU = "";
                ns.IDTipoNS_MB = 0;
                ns.NS_MB = "";
                ns.IDTipoNS_Bluetooth = 0;
                ns.NS_Bluetooth = "";
                ns.IDTipoNS_RedeIoT = 0;
                ns.NS_RedeIoT = "";
                ns.IDTipoNS_RedeIO = 0;
                ns.NS_RedeIO = "";
                ns.Observacao = "";
            }
            else
            {
                // le número de série
                NumeroSerieMetodos nsMetodos = new NumeroSerieMetodos();
                ns = nsMetodos.ListarPorId(IDNumeroSerie);
            }

            // prepara listas
            PreparaListas_NumeroSerie();

            return View(ns);
        }

        // Listas utilizadas
        private void PreparaListas_NumeroSerie()
        {
            // le tipos status
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listaTipoNS_Status = listatiposMetodos.ListarTodos("TipoNS_Status", false);
            ViewBag.listaTipoNS_Status = listaTipoNS_Status;

            // le tipos gateway
            List<ListaTiposDominio> listatiposGateway = listatiposMetodos.ListarTodos("TipoGateway");
            ViewBag.listaTipoGateway = listatiposGateway;

            // le tipos CPU
            List<ListaTiposDominio> listaTipoNS_CPU = listatiposMetodos.ListarTodos("TipoNS_CPU");
            ViewBag.listaTipoNS_CPU = listaTipoNS_CPU;

            // le tipos MotherBoard
            List<ListaTiposDominio> listaTipoNS_MB = listatiposMetodos.ListarTodos("TipoNS_MB");
            ViewBag.listaTipoNS_MB = listaTipoNS_MB;

            // le tipos Bluetooth
            List<ListaTiposDominio> listaTipoNS_Bluetooth = listatiposMetodos.ListarTodos("TipoNS_Bluetooth");
            ViewBag.listaTipoNS_Bluetooth = listaTipoNS_Bluetooth;

            // le tipos Rede IoT
            List<ListaTiposDominio> listaTipoNS_RedeIoT = listatiposMetodos.ListarTodos("TipoNS_RedeIoT");
            ViewBag.listaTipoNS_RedeIoT = listaTipoNS_RedeIoT;

            // le tipos Rede IO
            List<ListaTiposDominio> listaTipoNS_RedeIO = listatiposMetodos.ListarTodos("TipoNS_RedeIO");
            ViewBag.listaTipoNS_RedeIO = listaTipoNS_RedeIO;

            return;
        }


        // POST: Configuracao Número de Série - Salvar
        [HttpPost]
        public ActionResult NumeroSerie_Salvar(NumeroSerieDominio ns)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro com o mesmo número de série
            NumeroSerieMetodos nsMetodos = new NumeroSerieMetodos();
            if (nsMetodos.VerificarDuplicidade(ns))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = string.Format("Número de Série {0} existente.", ns.NS_Gateway)
                };
            }
            else
            {
                // salva número de série 
                nsMetodos.Salvar(ns);

                // verifica integridade
                int retorno = nsMetodos.VerificarIntegridade(ns);

                switch (retorno)
                {
                    case 1:
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "Número de Série não adicionado."
                        };
                        break;

                    case 2:
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "IDNumeroSerie adicionado com valor alto.<br><br>FAVOR AVISAR O DESENVOLVIMENTO ANTES DE PROSSEGUIR !!!"
                        };
                        break;
                }


                // pego IDNumeroSerie novamente (pois pode ter sido insercao)
                NumeroSerieDominio ns_novo = nsMetodos.ListarPorNumeroSerie_IDTipoGateway(ns.NS_Gateway, ns.IDTipoGateway);

                // verifica se salvou
                if (ns_novo != null)
                {
                    // evento 
                    UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();

                    if (ns.IDNumeroSerie > 0)
                    {
                        usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.NUMERO_SERIE, ns.IDNumeroSerie);
                    }
                    else
                    {
                        usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.NUMERO_SERIE, ns.IDNumeroSerie);
                    }
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuracao Número de Série - Excluir
        public ActionResult NumeroSerie_Excluir(int IDNumeroSerie)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existem gateways utilizando este número de série
            GatewaysMetodos gatewayMetodos = new GatewaysMetodos();

            if (gatewayMetodos.NumeroSerieGateway(IDNumeroSerie))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Este número de série está sendo utilizado pelas Gateways.<br><br>Verificar as Gateways antes de excluir."
                };
            }
            else
            {
                // apaga o número de série
                NumeroSerieMetodos nsMetodos = new NumeroSerieMetodos();
                nsMetodos.Excluir(IDNumeroSerie);

                // evento 
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.EXCLUI_CONFIG, TABELA_CONFIG.NUMERO_SERIE, IDNumeroSerie);
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }
    }
}