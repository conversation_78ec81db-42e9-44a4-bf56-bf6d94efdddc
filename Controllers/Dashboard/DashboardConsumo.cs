﻿using System;
using System.Runtime.InteropServices;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class DashboardController : Controller
    {
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Dashboard_Consumo", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern sbyte SmCalcDB_Dashboard_Consumo(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref DATAHOR<PERSON> pdatahora, sbyte navega, ref DASHBOARD_CONSUMO psuperv);

        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Dashboard_Meta", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern sbyte SmCalcDB_Dashboard_Meta(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref DATAHOR<PERSON> pdatahora, sbyte navega, ref DASHBOARD_META psuperv);


        private int DB_Atualizar_Consumo(int IDDashboard, int IDMedicao, int Navegacao, string DataAtualTxt)
        {
            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("DB_Editar");
            ViewBag.Editar = editar_cookie;

            // le cookie datahora
            DateTime datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);

            if (DataAtualTxt != null)
            {
                datahora_cookie = DateTime.Parse(DataAtualTxt);
            }

            // verifica se atualizar apenas
            if (Navegacao == 0)
            {
                // forco setar data como ultimo registro
                datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);
            }


            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            DASHBOARD_CONSUMO superv = new DASHBOARD_CONSUMO();

            DATAHORA datahora = new DATAHORA();
            datahora.data.dia = (char)datahora_cookie.Day;
            datahora.data.mes = (char)datahora_cookie.Month;
            datahora.data.ano = (Int16)datahora_cookie.Year;
            datahora.hora.hora = (char)1;
            datahora.hora.min = (char)0;
            datahora.hora.seg = (char)0;

            // preenche interface
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = medicao.IDGateway;

            // funcao de supervisao
            int retorno;

            // calcula valores
            retorno = SmCalcDB_Dashboard_Consumo((char)0, ref config_interface, ref datahora, (sbyte)Navegacao, ref superv);
            FuncoesDashboard.DivisaoUnidade_DASHBOARD_CONSUMO(ref superv, medicao.IDTipoUnidadePotencia);

            // erro
            if (retorno != 0)
            {
                // erro
                return (retorno);
            }

            // converte data e hora
            DateTime DataAtual = new DateTime((int)superv.Data.data.ano,
                                              (int)superv.Data.data.mes,
                                              (int)superv.Data.data.dia,
                                              1, 0, 0);

            // nome da medicao
            ViewBag.NomeMedicao = medicao.Nome;

            ViewBag.Data = string.Format("{0:Y}", DataAtual);

            // consumo
            if( superv.flag_reg_mensal == 1 || superv.flag_reg_mensal == 2 )
            {
                ViewBag.ConsumoTotalP = "---";
                ViewBag.ConsumoTotalFP = "---";
                ViewBag.ConsumoTotalFPI = "---";
                ViewBag.ConsumoTotalFPC = "---";
                ViewBag.ConsumoTotalTotal = "---";
            }
            else
            {
                ViewBag.ConsumoTotalP = Funcoes_SmartEnergy.FormatValor(superv.ConsumoTotalP);
                ViewBag.ConsumoTotalFP = Funcoes_SmartEnergy.FormatValor(superv.ConsumoTotalFPI + superv.ConsumoTotalFPC);
                ViewBag.ConsumoTotalFPI = Funcoes_SmartEnergy.FormatValor(superv.ConsumoTotalFPI);
                ViewBag.ConsumoTotalFPC = Funcoes_SmartEnergy.FormatValor(superv.ConsumoTotalFPC);
                ViewBag.ConsumoTotal = Funcoes_SmartEnergy.FormatValor(superv.ConsumoTotal);
            }

            // grafico
            var Consumo = new double[40];
            var ConsumoP = new double[40];
            var ConsumoFPI = new double[40];
            var ConsumoFPC = new double[40];
            var Status = new int[40];
            var Dias = new string[40];

            double Cons_max = 0.0;

            // valores
            DateTime strData = new DateTime((int)superv.ConsumoDataHora[0].data.ano,
                                            (int)superv.ConsumoDataHora[0].data.mes,
                                            (int)superv.ConsumoDataHora[0].data.dia,
                                            1,0,0);

            int i = 0;

            for (i = 0; i < superv.NumDias; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proximo dia
                strData = strData.AddDays(1);

                // copia
                ConsumoP[i] = superv.ConsumoP[i];
                ConsumoFPI[i] = superv.ConsumoFPI[i];
                ConsumoFPC[i] = superv.ConsumoFPC[i];
                Consumo[i] = superv.ConsumoP[i] + superv.ConsumoFPI[i] + superv.ConsumoFPC[i];

                // status
                if (superv.flag_reg_mensal == 1 || superv.flag_reg_mensal == 2)
                {
                    Status[i] = 1;
                }
                else
                {
                    Status[i] = 0;
                }

                // verifica consumo maximo
                if (Consumo[i] > Cons_max)
                    Cons_max = Consumo[i];
            }

            Cons_max = Cons_max * 1.1;

            if (Cons_max == 0.0)
            {
                Cons_max = 10.0;
            }

            ViewBag.Cons_max = Cons_max;

            ViewBag.Consumo = Consumo;
            ViewBag.ConsumoP = ConsumoP;
            ViewBag.ConsumoFPI = ConsumoFPI;
            ViewBag.ConsumoFPC = ConsumoFPC;
            ViewBag.Status = Status;
            ViewBag.Dias = Dias;
            ViewBag.NumDias = superv.NumDias;

            // data atual
            string IDDashboardTxt = string.Format("dt{0}", IDDashboard);
            ViewBag.IDDashboardTxt = IDDashboardTxt;
            ViewBag.DataAtual = string.Format("{0:d} {1:HH:mm}", DataAtual, DataAtual);

            // ok
            return (0);
        }

        private int DB_Atualizar_Meta(int IDDashboard, int IDMedicao, int Navegacao, string DataAtualTxt)
        {
            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("DB_Editar");
            ViewBag.Editar = editar_cookie;

            // le cookie datahora
            DateTime datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);

            if (DataAtualTxt != null)
            {
                datahora_cookie = DateTime.Parse(DataAtualTxt);
            }

            // verifica se atualizar apenas
            if (Navegacao == 0)
            {
                // forco setar data como ultimo registro
                datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);
            }


            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            DASHBOARD_META superv = new DASHBOARD_META();

            DATAHORA datahora = new DATAHORA();
            datahora.data.dia = (char)datahora_cookie.Day;
            datahora.data.mes = (char)datahora_cookie.Month;
            datahora.data.ano = (Int16)datahora_cookie.Year;
            datahora.hora.hora = (char)1;
            datahora.hora.min = (char)0;
            datahora.hora.seg = (char)0;

            // preenche interface
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = medicao.IDGateway;

            // funcao de supervisao
            int retorno;

            // calcula valores
            retorno = SmCalcDB_Dashboard_Meta((char)0, ref config_interface, ref datahora, (sbyte)Navegacao, ref superv);

            // erro
            if (retorno != 0)
            {
                // erro
                return (retorno);
            }

            // converte data e hora
            DateTime DataAtual = new DateTime((int)superv.Data.data.ano,
                                              (int)superv.Data.data.mes,
                                              (int)superv.Data.data.dia,
                                              1, 0, 0);

            // nome da medicao
            ViewBag.NomeMedicao = medicao.Nome;

            // maior consumo
            double maior_valor = (superv.ProjetadoMes > superv.MetaMes) ? superv.ProjetadoMes : superv.MetaMes;
            double divisao_consumo = 1.0;

            ViewBag.Data = string.Format("{0:Y}", DataAtual);

            // consumo e meta diario
            if (superv.flag_reg_mensal == 1 || superv.flag_reg_mensal == 2)
            {
                ViewBag.ConsumoMedio = "---";
                ViewBag.ProjetadoMes = "---";
            }
            else
            {
                ViewBag.ConsumoMedio = Funcoes_SmartEnergy.FormatValor(superv.ConsumoMedio / divisao_consumo);
                ViewBag.ProjetadoMes = Funcoes_SmartEnergy.FormatValor(superv.ProjetadoMes / divisao_consumo);
            }

            ViewBag.MetaDiaria = Funcoes_SmartEnergy.FormatValor(superv.MetaDiaria / divisao_consumo);

            ViewBag.ConsumoMedioN = superv.ConsumoMedio / divisao_consumo;
            ViewBag.MetaDiariaN = superv.MetaDiaria / divisao_consumo;

            // consumo e meta no mes
            ViewBag.MetaMes = Funcoes_SmartEnergy.FormatValor(superv.MetaMes / divisao_consumo);

            ViewBag.ProjetadoMesN = superv.ProjetadoMes / divisao_consumo;
            ViewBag.MetaMesN = superv.MetaMes / divisao_consumo;

            // grafico
            var Consumo = new double[31];
            var Meta = new double[31];
            var Status = new int[31];
            var Dias = new string[31];

            double Cons_max = 0.0;

            // valores
            DateTime strData = new DateTime((int)superv.Consumo_DataHora[0].data.ano,
                                            (int)superv.Consumo_DataHora[0].data.mes,
                                            (int)superv.Consumo_DataHora[0].data.dia,
                                            1, 0, 0);

            int i = 0;

            for (i = 0; i < superv.NumDias; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proximo dia
                strData = strData.AddDays(1);

                // copia
                Consumo[i] = superv.Consumo[i] / divisao_consumo;
                Meta[i] = superv.MetaDiaria / divisao_consumo;

                // status
                if (superv.flag_reg_mensal == 1 || superv.flag_reg_mensal == 2)
                {
                    Status[i] = 1;
                }
                else
                {
                    Status[i] = 0;
                }

                // verifica consumo maximo
                if (Consumo[i] > Cons_max)
                    Cons_max = Consumo[i];

                if (Meta[i] > Cons_max)
                    Cons_max = Meta[i];
            }

            Cons_max = Cons_max * 1.1;

            if (Cons_max == 0.0)
            {
                Cons_max = 10.0;
            }

            ViewBag.Cons_max = Cons_max;

            ViewBag.Consumo = Consumo;
            ViewBag.Meta = Meta;
            ViewBag.Status = Status;
            ViewBag.Dias = Dias;
            ViewBag.NumDias = superv.NumDias;

            // data atual
            string IDDashboardTxt = string.Format("dt{0}", IDDashboard);
            ViewBag.IDDashboardTxt = IDDashboardTxt;
            ViewBag.DataAtual = string.Format("{0:d} {1:HH:mm}", DataAtual, DataAtual);

            // ok
            return (0);
        }
    }
}