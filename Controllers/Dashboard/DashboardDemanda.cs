﻿using System;
using System.Runtime.InteropServices;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class DashboardController : Controller
    {
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Dashboard_Demanda", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern sbyte SmCalcDB_Dashboard_Demanda(sbyte tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref DATAHORA pdatahora, sbyte navega, ref DASHBOARD_DEMANDA psuperv);

        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Dashboard_DemandaMensal", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern sbyte SmCalcDB_Dashboard_DemandaMensal(sbyte tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref DATAHOR<PERSON> pdatahora, sbyte navega, ref DASHBOARD_DEMANDAMENSAL psuperv);

        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Dashboard_DemandaAnual", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern sbyte SmCalcDB_Dashboard_DemandaAnual(sbyte tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref DATAHORA pdatahora, sbyte navega, ref DASHBOARD_DEMANDAANUAL psuperv);


        private int DB_Atualizar_Demanda(int IDDashboard, int IDMedicao, int Navegacao, string DataAtualTxt)
        {
            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("DB_Editar");
            ViewBag.Editar = editar_cookie;

            // le cookie datahora
            DateTime datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);

            if( DataAtualTxt != null )
            {
                datahora_cookie = DateTime.Parse(DataAtualTxt);
            }

            // verifica se atualizar apenas
            if (Navegacao == 0)
            {
                // forco setar data como ultimo registro
                datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);
            }


            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            DASHBOARD_DEMANDA superv = new DASHBOARD_DEMANDA();

            DATAHORA datahora = new DATAHORA();
            datahora.data.dia = (char)datahora_cookie.Day;
            datahora.data.mes = (char)datahora_cookie.Month;
            datahora.data.ano = (Int16)datahora_cookie.Year;
            datahora.hora.hora = (char)1;
            datahora.hora.min = (char)0;
            datahora.hora.seg = (char)0;

            // preenche interface
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = medicao.IDGateway;

            // funcao de supervisao
            sbyte retorno;

            // calcula valores
            retorno = SmCalcDB_Dashboard_Demanda(0, ref config_interface, ref datahora, (sbyte)Navegacao, ref superv);
            FuncoesDashboard.DivisaoUnidade_DASHBOARD_DEMANDA(ref superv, medicao.IDTipoUnidadePotencia);

            // erro
            if (retorno != 0)
            {
                // erro
                return (retorno);
            }

            // converte data e hora
            DateTime DataAtual = new DateTime((int)superv.Data.data.ano,
                                              (int)superv.Data.data.mes,
                                              (int)superv.Data.data.dia,
                                              1, 0, 0);

            DateTime Dem_MaxDatahora = new DateTime((int)superv.Dem_MaxDatahora.data.ano,
                                                   (int)superv.Dem_MaxDatahora.data.mes,
                                                   (int)superv.Dem_MaxDatahora.data.dia,
                                                   (int)superv.Dem_MaxDatahora.hora.hora,
                                                   (int)superv.Dem_MaxDatahora.hora.min, 0);

            DateTime Dem_MaxP_DataHora = new DateTime((int)superv.Dem_MaxP_DataHora.data.ano,
                                                   (int)superv.Dem_MaxP_DataHora.data.mes,
                                                   (int)superv.Dem_MaxP_DataHora.data.dia,
                                                   (int)superv.Dem_MaxP_DataHora.hora.hora,
                                                   (int)superv.Dem_MaxP_DataHora.hora.min, 0);

            DateTime Dem_MaxFP_DataHora = new DateTime((int)superv.Dem_MaxFP_DataHora.data.ano,
                                                   (int)superv.Dem_MaxFP_DataHora.data.mes,
                                                   (int)superv.Dem_MaxFP_DataHora.data.dia,
                                                   (int)superv.Dem_MaxFP_DataHora.hora.hora,
                                                   (int)superv.Dem_MaxFP_DataHora.hora.min, 0);

            // nome da medicao
            ViewBag.NomeMedicao = medicao.Nome;

            // demanda
            if (superv.flag_reg_diario == 1 || superv.flag_reg_diario == 2)
            {
                ViewBag.Dem_Max = "---";
                ViewBag.Dem_MaxPeriodo = 3;
            }
            else
            {
                ViewBag.Dem_Max = string.Format("{0:#,##0.0}", superv.Dem_Max);
                ViewBag.Dem_MaxPeriodo = superv.Dem_MaxPeriodo;
            }
            ViewBag.Dem_MaxDatahora = string.Format("{0:d} {1:HH:mm}", Dem_MaxDatahora, Dem_MaxDatahora);

            if (Dem_MaxP_DataHora.Year == 2000)
            {
                ViewBag.Dem_MaxP = "---";
                ViewBag.Dem_MaxP_DataHora = "--/--/----";
            }
            else
            {
                ViewBag.Dem_MaxP = string.Format("{0:#,##0.0}", superv.Dem_MaxP);
                ViewBag.Dem_MaxP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MaxP_DataHora, Dem_MaxP_DataHora);
            }

            if (Dem_MaxFP_DataHora.Year == 2000)
            {
                ViewBag.Dem_MaxFP = "---";
                ViewBag.Dem_MaxFP_DataHora = "--/--/----";
            }
            else
            {
                ViewBag.Dem_MaxFP = string.Format("{0:#,##0.0}", superv.Dem_MaxFP);
                ViewBag.Dem_MaxFP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MaxFP_DataHora, Dem_MaxFP_DataHora);
            }

            // datahoraAtual do dashboard
            if (Dem_MaxDatahora.Year == 2000)
            {
                ViewBag.DataHoraAtual = string.Format("{0:d}", DataAtual);
            }
            else
            {
                ViewBag.DataHoraAtual = string.Format("{0:d} {1:HH:mm}", Dem_MaxDatahora, Dem_MaxDatahora);
            }

            // grafico
            var Demanda = new double[98];
            var Periodo = new double[98];
            var Contrato = new double[98];
            var Tolerancia = new double[98];
            var Dias = new string[98];

            double Dem_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)superv.DataHora[0].data.ano,
                                            (int)superv.DataHora[0].data.mes,
                                            (int)superv.DataHora[0].data.dia,
                                            (int)superv.DataHora[0].hora.hora,
                                            (int)superv.DataHora[0].hora.min, 0);

            // volta 15min para a primeira barra
            strData = strData.AddMinutes(-15);

            int i = 0;
            int j = 0;

            for (i = 0; i < 98; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proximo quinze minutos
                strData = strData.AddMinutes(15);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    Demanda[i] = superv.Demanda[0];
                    Periodo[i] = 1;
                    Contrato[i] = superv.ContratoFP[0];
                    Tolerancia[i] = Contrato[i] * (1.0 + (superv.Tol_ContratoFP / 100.0));
                }

                // verifica se ultima barra
                if (i == 97)
                {
                    // zera
                    Demanda[i] = superv.Demanda[95];
                    Periodo[i] = 1;
                    Contrato[i] = superv.ContratoFP[95];
                    Tolerancia[i] = Contrato[i] * (1.0 + (superv.Tol_ContratoFP / 100.0));
                }

                if (i >= 1 && i <= 96)
                {
                    // copia
                    j = i - 1;

                    Demanda[i] = superv.Demanda[j];
                    Periodo[i] = superv.Periodo[j];

                    if (superv.Periodo[j] == 0)
                    {
                        Contrato[i] = superv.ContratoP[j];
                        Tolerancia[i] = Contrato[i] * (1.0 + (superv.Tol_ContratoP / 100.0));
                    }
                    else
                    {
                        Contrato[i] = superv.ContratoFP[j];
                        Tolerancia[i] = Contrato[i] * (1.0 + (superv.Tol_ContratoFP / 100.0));
                    }

                    // verifica demanda maxima
                    if (Demanda[i] > Dem_max_grafico)
                        Dem_max_grafico = Demanda[i];

                    if (Contrato[i] > Dem_max_grafico)
                        Dem_max_grafico = Contrato[i];

                    if (Tolerancia[i] > Dem_max_grafico)
                        Dem_max_grafico = Tolerancia[i];
                }
            }

            Dem_max_grafico = Dem_max_grafico * 1.1;

            if (Dem_max_grafico < 1.0)
            {
                Dem_max_grafico = 1.0;
            }

            ViewBag.DemMaxGrafico = Dem_max_grafico;

            ViewBag.Demanda = Demanda;
            ViewBag.Periodo = Periodo;
            ViewBag.Contrato = Contrato;
            ViewBag.Tolerancia = Tolerancia;
            ViewBag.Dias = Dias;

            // data atual
            string IDDashboardTxt = string.Format("dt{0}", IDDashboard);
            ViewBag.IDDashboardTxt = IDDashboardTxt;
            ViewBag.DataAtual = string.Format("{0:d} {1:HH:mm}", DataAtual, DataAtual);

            // ok
            return (0);
        }

        private int DB_Atualizar_DemandaMensal(int IDDashboard, int IDMedicao, int Navegacao, string DataAtualTxt)
        {
            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("DB_Editar");
            ViewBag.Editar = editar_cookie;

            // le cookie datahora
            DateTime datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);

            if (DataAtualTxt != null)
            {
                datahora_cookie = DateTime.Parse(DataAtualTxt);
            }

            // verifica se atualizar apenas
            if (Navegacao == 0)
            {
                // forco setar data como ultimo registro
                datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);
            }


            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            DASHBOARD_DEMANDAMENSAL superv = new DASHBOARD_DEMANDAMENSAL();

            DATAHORA datahora = new DATAHORA();
            datahora.data.dia = (char)datahora_cookie.Day;
            datahora.data.mes = (char)datahora_cookie.Month;
            datahora.data.ano = (Int16)datahora_cookie.Year;
            datahora.hora.hora = (char)1;
            datahora.hora.min = (char)0;
            datahora.hora.seg = (char)0;

            // preenche interface
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = medicao.IDGateway;

            // funcao de supervisao
            sbyte retorno;

            // calcula valores
            retorno = SmCalcDB_Dashboard_DemandaMensal(0, ref config_interface, ref datahora, (sbyte)Navegacao, ref superv);
            FuncoesDashboard.DivisaoUnidade_DASHBOARD_DEMANDAMENSAL(ref superv, medicao.IDTipoUnidadePotencia);

            // erro
            if (retorno != 0)
            {
                // erro
                return (retorno);
            }

            // converte data e hora
            DateTime DataAtual = new DateTime((int)superv.Data.data.ano,
                                              (int)superv.Data.data.mes,
                                              1, 1, 0, 0);

            DateTime Dem_MaxP_DataHora = new DateTime((int)superv.Dem_MaxP_DataHora.data.ano,
                                                   (int)superv.Dem_MaxP_DataHora.data.mes,
                                                   (int)superv.Dem_MaxP_DataHora.data.dia,
                                                   (int)superv.Dem_MaxP_DataHora.hora.hora,
                                                   (int)superv.Dem_MaxP_DataHora.hora.min, 0);

            DateTime Dem_MaxFP_DataHora = new DateTime((int)superv.Dem_MaxFP_DataHora.data.ano,
                                                   (int)superv.Dem_MaxFP_DataHora.data.mes,
                                                   (int)superv.Dem_MaxFP_DataHora.data.dia,
                                                   (int)superv.Dem_MaxFP_DataHora.hora.hora,
                                                   (int)superv.Dem_MaxFP_DataHora.hora.min, 0);

            // nome da medicao
            ViewBag.NomeMedicao = medicao.Nome;

            // demanda
            if (Dem_MaxP_DataHora.Year == 2000)
            {
                ViewBag.Dem_MaxP = "---";
                ViewBag.Dem_MaxP_DataHora = "--/--/----";
            }
            else
            {
                ViewBag.Dem_MaxP = string.Format("{0:#,##0.0}", superv.Dem_MaxP);
                ViewBag.Dem_MaxP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MaxP_DataHora, Dem_MaxP_DataHora);
            }

            if (Dem_MaxFP_DataHora.Year == 2000)
            {
                ViewBag.Dem_MaxFP = "---";
                ViewBag.Dem_MaxFP_DataHora = "--/--/----";
            }
            else
            {
                ViewBag.Dem_MaxFP = string.Format("{0:#,##0.0}", superv.Dem_MaxFP);
                ViewBag.Dem_MaxFP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MaxFP_DataHora, Dem_MaxFP_DataHora);
            }

            // datahoraAtual do dashboard
            ViewBag.DataHoraAtual = string.Format("{0:Y}", DataAtual);

            // grafico
            var DemandaP = new double[40];
            var DemandaFP = new double[40];
            var ContratoP = new double[40];
            var ContratoFP = new double[40];
            var ToleranciaP = new double[40];
            var ToleranciaFP = new double[40];
            var Status = new int[40];
            var Dias = new string[40];

            double Dem_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)superv.DataHora[0].data.ano,
                                            (int)superv.DataHora[0].data.mes,
                                            1, 1, 0, 0);

            int i = 0;

            for (i = 0; i < superv.NumDias; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proximo dia
                strData = strData.AddDays(1);

                // copia
                DemandaP[i] = superv.DemandaP[i];
                DemandaFP[i] = superv.DemandaFP[i];

                ContratoP[i] = superv.ContratoP[i];
                ToleranciaP[i] = ContratoP[i] * (1.0 + (superv.Tol_ContratoP / 100.0));

                ContratoFP[i] = superv.ContratoFP[i];
                ToleranciaFP[i] = ContratoFP[i] * (1.0 + (superv.Tol_ContratoFP / 100.0));

                // status
                if (superv.flag_reg_mensal == 1 || superv.flag_reg_mensal == 2)
                {
                    Status[i] = 1;
                }
                else
                {
                    Status[i] = 0;
                }

                // verifica demanda maxima
                if (DemandaP[i] > Dem_max_grafico)
                    Dem_max_grafico = DemandaP[i];

                if (DemandaFP[i] > Dem_max_grafico)
                    Dem_max_grafico = DemandaFP[i];

                if (ContratoP[i] > Dem_max_grafico)
                    Dem_max_grafico = ContratoP[i];

                if (ToleranciaP[i] > Dem_max_grafico)
                    Dem_max_grafico = ToleranciaP[i];

                if (ContratoFP[i] > Dem_max_grafico)
                    Dem_max_grafico = ContratoFP[i];

                if (ToleranciaFP[i] > Dem_max_grafico)
                    Dem_max_grafico = ToleranciaFP[i];
            }

            Dem_max_grafico = Dem_max_grafico * 1.1;

            if (Dem_max_grafico < 1.0)
            {
                Dem_max_grafico = 1.0;
            }

            ViewBag.DemMaxGrafico = Dem_max_grafico;

            ViewBag.DemandaP = DemandaP;
            ViewBag.DemandaFP = DemandaFP;
            ViewBag.ContratoP = ContratoP;
            ViewBag.ToleranciaP = ToleranciaP;
            ViewBag.ContratoFP = ContratoFP;
            ViewBag.ToleranciaFP = ToleranciaFP;
            ViewBag.Status = Status;
            ViewBag.Dias = Dias;
            ViewBag.NumDias = superv.NumDias;

            // data atual
            string IDDashboardTxt = string.Format("dt{0}", IDDashboard);
            ViewBag.IDDashboardTxt = IDDashboardTxt;
            ViewBag.DataAtual = string.Format("{0:d} {1:HH:mm}", DataAtual, DataAtual);

            // ok
            return (0);
        }

        private int DB_Atualizar_DemandaAnual(int IDDashboard, int IDMedicao, int Navegacao, string DataAtualTxt)
        {
            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("DB_Editar");
            ViewBag.Editar = editar_cookie;

            // le cookie datahora
            DateTime datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);

            if (DataAtualTxt != null)
            {
                datahora_cookie = DateTime.Parse(DataAtualTxt);
            }

            // verifica se atualizar apenas
            if (Navegacao == 0)
            {
                // forco setar data como ultimo registro
                datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);
            }


            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            DASHBOARD_DEMANDAANUAL superv = new DASHBOARD_DEMANDAANUAL();

            DATAHORA datahora = new DATAHORA();
            datahora.data.dia = (char)1;
            datahora.data.mes = (char)1;
            datahora.data.ano = (Int16)datahora_cookie.Year;
            datahora.hora.hora = (char)1;
            datahora.hora.min = (char)0;
            datahora.hora.seg = (char)0;

            // preenche interface
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = medicao.IDGateway;

            // funcao de supervisao
            sbyte retorno;

            // calcula valores
            retorno = SmCalcDB_Dashboard_DemandaAnual(0, ref config_interface, ref datahora, (sbyte)Navegacao, ref superv);
            FuncoesDashboard.DivisaoUnidade_DASHBOARD_DEMANDAANUAL(ref superv, medicao.IDTipoUnidadePotencia);

            // erro
            if (retorno != 0)
            {
                // erro
                return (retorno);
            }

            // converte data e hora
            DateTime DataAtual = new DateTime((int)superv.Data.data.ano,
                                              1, 1, 1, 0, 0);

            DateTime Dem_MaxP_DataHora = new DateTime((int)superv.Dem_MaxP_DataHora.data.ano,
                                                   (int)superv.Dem_MaxP_DataHora.data.mes,
                                                   (int)superv.Dem_MaxP_DataHora.data.dia,
                                                   (int)superv.Dem_MaxP_DataHora.hora.hora,
                                                   (int)superv.Dem_MaxP_DataHora.hora.min, 0);

            DateTime Dem_MaxFP_DataHora = new DateTime((int)superv.Dem_MaxFP_DataHora.data.ano,
                                                   (int)superv.Dem_MaxFP_DataHora.data.mes,
                                                   (int)superv.Dem_MaxFP_DataHora.data.dia,
                                                   (int)superv.Dem_MaxFP_DataHora.hora.hora,
                                                   (int)superv.Dem_MaxFP_DataHora.hora.min, 0);

            // nome da medicao
            ViewBag.NomeMedicao = medicao.Nome;

            // demanda
            if (Dem_MaxP_DataHora.Year == 2000)
            {
                ViewBag.Dem_MaxP = "---";
                ViewBag.Dem_MaxP_DataHora = "--/--/----";
            }
            else
            {
                ViewBag.Dem_MaxP = string.Format("{0:#,##0.0}", superv.Dem_MaxP);
                ViewBag.Dem_MaxP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MaxP_DataHora, Dem_MaxP_DataHora);
            }

            if (Dem_MaxFP_DataHora.Year == 2000)
            {
                ViewBag.Dem_MaxFP = "---";
                ViewBag.Dem_MaxFP_DataHora = "--/--/----";
            }
            else
            {
                ViewBag.Dem_MaxFP = string.Format("{0:#,##0.0}", superv.Dem_MaxFP);
                ViewBag.Dem_MaxFP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MaxFP_DataHora, Dem_MaxFP_DataHora);
            }

            // datahoraAtual do dashboard
            ViewBag.DataHoraAtual = string.Format("{0}", DataAtual.Year);

            // grafico
            var DemandaP = new double[12];
            var DemandaFP = new double[12];
            var ContratoP = new double[12];
            var ContratoFP = new double[12];
            var ToleranciaP = new double[12];
            var ToleranciaFP = new double[12];
            var Status = new int[12];
            var Dias = new string[12];

            double Dem_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)superv.DataHora[0].data.ano, 1, 1, 1, 0, 0);

            int i = 0;

            for (i = 0; i < 12; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proximo mes
                strData = strData.AddMonths(1);

                // copia
                DemandaP[i] = superv.DemandaP[i];
                DemandaFP[i] = superv.DemandaFP[i];

                ContratoP[i] = superv.ContratoP[i];
                ToleranciaP[i] = ContratoP[i] * (1.0 + (superv.Tol_ContratoP / 100.0));

                ContratoFP[i] = superv.ContratoFP[i];
                ToleranciaFP[i] = ContratoFP[i] * (1.0 + (superv.Tol_ContratoFP / 100.0));

                // status
                if (superv.flag_reg_anual == 1 || superv.flag_reg_anual == 2)
                {
                    Status[i] = 1;
                }
                else
                {
                    Status[i] = 0;
                }

                // verifica demanda maxima
                if (DemandaP[i] > Dem_max_grafico)
                    Dem_max_grafico = DemandaP[i];

                if (DemandaFP[i] > Dem_max_grafico)
                    Dem_max_grafico = DemandaFP[i];

                if (ContratoP[i] > Dem_max_grafico)
                    Dem_max_grafico = ContratoP[i];

                if (ToleranciaP[i] > Dem_max_grafico)
                    Dem_max_grafico = ToleranciaP[i];

                if (ContratoFP[i] > Dem_max_grafico)
                    Dem_max_grafico = ContratoFP[i];

                if (ToleranciaFP[i] > Dem_max_grafico)
                    Dem_max_grafico = ToleranciaFP[i];
            }

            Dem_max_grafico = Dem_max_grafico * 1.1;

            if (Dem_max_grafico < 1.0)
            {
                Dem_max_grafico = 1.0;
            }

            ViewBag.DemMaxGrafico = Dem_max_grafico;

            ViewBag.DemandaP = DemandaP;
            ViewBag.DemandaFP = DemandaFP;
            ViewBag.ContratoP = ContratoP;
            ViewBag.ToleranciaP = ToleranciaP;
            ViewBag.ContratoFP = ContratoFP;
            ViewBag.ToleranciaFP = ToleranciaFP;
            ViewBag.Status = Status;
            ViewBag.Dias = Dias;

            // data atual
            string IDDashboardTxt = string.Format("dt{0}", IDDashboard);
            ViewBag.IDDashboardTxt = IDDashboardTxt;
            ViewBag.DataAtual = string.Format("{0:d} {1:HH:mm}", DataAtual, DataAtual);

            // ok
            return (0);
        }
    }
}