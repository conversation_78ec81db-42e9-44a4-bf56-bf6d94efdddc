﻿using System;
using System.Runtime.InteropServices;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class DashboardController : Controller
    {
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Dashboard_Fatura", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern sbyte SmCalcDB_Dashboard_Fatura(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref DATAHORA pdatahora, sbyte navega, ref DASHBOARD_FATURA psuperv);

        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Dashboard_Comparativo_Cativo_Livre", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern sbyte SmCalcDB_Dashboard_Comparativo_Cativo_Livre(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref DATAHOR<PERSON> pdatahora, sbyte navega, ref DASHBOARD_COMPARATIVO_FATURA psuperv);



        private int DB_Atualizar_Fatura(int IDDashboard, int IDMedicao, int Navegacao, string DataAtualTxt)
        {
            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("DB_Editar");
            ViewBag.Editar = editar_cookie;

            // le cookie datahora
            DateTime datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);

            if (DataAtualTxt != null)
            {
                datahora_cookie = DateTime.Parse(DataAtualTxt);
            }

            // verifica se atualizar apenas
            if (Navegacao == 0)
            {
                // forco setar data como ultimo registro
                datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);
            }


            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            DASHBOARD_FATURA superv = new DASHBOARD_FATURA();

            DATAHORA datahora = new DATAHORA();
            datahora.data.dia = (char)datahora_cookie.Day;
            datahora.data.mes = (char)datahora_cookie.Month;
            datahora.data.ano = (Int16)datahora_cookie.Year;
            datahora.hora.hora = (char)1;
            datahora.hora.min = (char)0;
            datahora.hora.seg = (char)0;

            // preenche solicitacao
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = medicao.IDGateway;

            // funcao de supervisao
            int retorno;

            // calcula valores
            retorno = SmCalcDB_Dashboard_Fatura((char)0, ref config_interface, ref datahora, (sbyte)Navegacao, ref superv);

            // erro
            if (retorno != 0)
            {
                // erro
                return (retorno);
            }

            // converte data e hora
            DateTime DataAtual = new DateTime((int)superv.Data.data.ano,
                                              (int)superv.Data.data.mes,
                                              (int)superv.Data.data.dia,
                                              1, 0, 0);

            DateTime Fatura_Atual_Data = new DateTime((int)superv.Fatura_Atual.Data.ano,
                                                   (int)superv.Fatura_Atual.Data.mes,
                                                   (int)superv.Fatura_Atual.Data.dia, 0, 0, 0);

            DateTime Fatura_AtualProj_Data = new DateTime((int)superv.Fatura_AtualProj.Data.ano,
                                                   (int)superv.Fatura_AtualProj.Data.mes,
                                                   (int)superv.Fatura_AtualProj.Data.dia, 0, 0, 0);

            DateTime Fatura_Anterior_Data = new DateTime((int)superv.Fatura_Anterior.Data.ano,
                                                   (int)superv.Fatura_Anterior.Data.mes,
                                                   (int)superv.Fatura_Anterior.Data.dia, 0, 0, 0);

            // nome da medicao
            ViewBag.NomeMedicao = medicao.Nome;

            // fatura
            ViewBag.Fatura_Atual_Data = Funcoes_SmartEnergy.StringMesFechamento(Fatura_Atual_Data);
            ViewBag.Fatura_Atual_Bandeira = Funcoes_SmartEnergy.BandeiraCor(superv.Fatura_Atual.Bandeira);
            ViewBag.Fatura_Atual_BandeiraTexto = Funcoes_SmartEnergy.BandeiraTexto(superv.Fatura_Atual.Bandeira);
            ViewBag.Fatura_Atual_ValorTotal = string.Format("{0:C}", superv.Fatura_Atual.ValorTotal);
            ViewBag.Fatura_Atual_CustoMedio = string.Format("{0:C} / MWh", superv.Fatura_Atual.CustoMedio);

            ViewBag.Fatura_AtualProj_Data = Funcoes_SmartEnergy.StringMesFechamento(Fatura_AtualProj_Data, "MMMM");
            ViewBag.Fatura_AtualProj_Bandeira = Funcoes_SmartEnergy.BandeiraCor(superv.Fatura_AtualProj.Bandeira);
            ViewBag.Fatura_AtualProj_BandeiraTexto = Funcoes_SmartEnergy.BandeiraTexto(superv.Fatura_AtualProj.Bandeira);
            ViewBag.Fatura_AtualProj_ValorTotal = string.Format("{0:C}", superv.Fatura_AtualProj.ValorTotal);
            ViewBag.Fatura_AtualProj_CustoMedio = string.Format("{0:C} / MWh", superv.Fatura_AtualProj.CustoMedio);

            if (superv.Fatura_PorcAntProj <= -100.0)
                superv.Fatura_PorcAntProj = 0.0;

            ViewBag.Fatura_PorcAntProjN = superv.Fatura_PorcAntProj;
            ViewBag.Fatura_PorcAntProj = string.Format("{0:#,##0.0} %", superv.Fatura_PorcAntProj);
            ViewBag.Fatura_Anterior_Data = Funcoes_SmartEnergy.StringMesFechamento(Fatura_Anterior_Data, "MMMM");
            ViewBag.Fatura_Anterior_Bandeira = Funcoes_SmartEnergy.BandeiraCor(superv.Fatura_Anterior.Bandeira);
            ViewBag.Fatura_Anterior_BandeiraTexto = Funcoes_SmartEnergy.BandeiraTexto(superv.Fatura_Anterior.Bandeira);
            ViewBag.Fatura_Anterior_ValorTotal = string.Format("{0:C}", superv.Fatura_Anterior.ValorTotal);
            ViewBag.Fatura_Anterior_CustoMedio = string.Format("{0:C} / MWh", superv.Fatura_Anterior.CustoMedio);

            ViewBag.Porc_Consumo = superv.Porc_Consumo;
            ViewBag.Porc_Demanda = superv.Porc_Demanda;
            ViewBag.Porc_Imposto = superv.Porc_Imposto;
            ViewBag.Porc_Multa = superv.Porc_Multa;
            ViewBag.PlotarGrafico = true;

            if( superv.Porc_Consumo == 0 && superv.Porc_Demanda == 0 && superv.Porc_Imposto == 0 && superv.Porc_Multa == 0 )
            {
                ViewBag.PlotarGrafico = false;
            }

            // data atual
            string IDDashboardTxt = string.Format("dt{0}", IDDashboard);
            ViewBag.IDDashboardTxt = IDDashboardTxt;
            ViewBag.DataAtual = string.Format("{0:d} {1:HH:mm}", DataAtual, DataAtual);

            // ok
            return (0);
        }


        private int DB_Atualizar_Comparativo_Cativo_Livre(int IDDashboard, int IDMedicao, int Navegacao, string DataAtualTxt)
        {
            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("DB_Editar");
            ViewBag.Editar = editar_cookie;

            // le cookie datahora
            DateTime datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);

            if (DataAtualTxt != null)
            {
                datahora_cookie = DateTime.Parse(DataAtualTxt);
            }

            // verifica se atualizar apenas
            if (Navegacao == 0)
            {
                // forco setar data como ultimo registro
                datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);
            }


            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            DASHBOARD_COMPARATIVO_FATURA superv = new DASHBOARD_COMPARATIVO_FATURA();

            DATAHORA datahora = new DATAHORA();
            datahora.data.dia = (char)datahora_cookie.Day;
            datahora.data.mes = (char)datahora_cookie.Month;
            datahora.data.ano = (Int16)datahora_cookie.Year;
            datahora.hora.hora = (char)1;
            datahora.hora.min = (char)0;
            datahora.hora.seg = (char)0;

            // preenche solicitacao
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = medicao.IDGateway;

            // funcao de supervisao
            int retorno;

            // calcula valores
            retorno = SmCalcDB_Dashboard_Comparativo_Cativo_Livre((char)0, ref config_interface, ref datahora, (sbyte)Navegacao, ref superv);

            // erro
            if (retorno != 0)
            {
                // erro
                return (retorno);
            }

            // converte data e hora
            DateTime DataAtual = new DateTime((int)superv.Data.data.ano,
                                              (int)superv.Data.data.mes,
                                              (int)superv.Data.data.dia,
                                              1, 0, 0);

            DateTime Fatura_Atual_Data = new DateTime((int)superv.Fatura_Livre_Atual.Data.ano,
                                                   (int)superv.Fatura_Livre_Atual.Data.mes,
                                                   (int)superv.Fatura_Livre_Atual.Data.dia, 0, 0, 0);

            ViewBag.Fatura_Atual_Data = Funcoes_SmartEnergy.StringMesFechamento(Fatura_Atual_Data);

            // nome da medicao
            ViewBag.NomeMedicao = medicao.Nome;

            // Cativo
            ViewBag.Fatura_Cativo_Atual = string.Format("{0:C}", superv.Fatura_Cativo_Atual.ValorTotal);
            ViewBag.Fatura_Cativo_AtualProj = string.Format("{0:C}", superv.Fatura_Cativo_AtualProj.ValorTotal);

            // Livre
            ViewBag.Fatura_Livre_Atual = string.Format("{0:C}", superv.Fatura_Livre_Atual.ValorTotal);
            ViewBag.Fatura_Livre_AtualProj = string.Format("{0:C}", superv.Fatura_Livre_AtualProj.ValorTotal);

            // Economia
            // a referencia é a fatura Cativo ser mais cara que Livre
            // valor positivo = economia
            // valor negativo = prejuizo
            ViewBag.Economia_Atual = string.Format("{0:C}", superv.Economia_Atual);
            ViewBag.Economia_AtualProj = string.Format("{0:C}", superv.Economia_AtualProj);

            ViewBag.Economia_AtualN = superv.Economia_Atual;
            ViewBag.Economia_AtualProjN = superv.Economia_AtualProj;

            double Economia_Porc_Atual = 0.0;

            if (superv.Fatura_Cativo_Atual.ValorTotal != 0.0)
            {
                Economia_Porc_Atual = ((superv.Fatura_Cativo_Atual.ValorTotal - superv.Fatura_Livre_Atual.ValorTotal) / superv.Fatura_Cativo_Atual.ValorTotal) * 100.0;
            }

            ViewBag.Economia_Porc_Atual = string.Format("{0:0.00}%", Economia_Porc_Atual);


            double Economia_Porc_AtualProj = 0.0;

            if (superv.Fatura_Cativo_AtualProj.ValorTotal != 0.0)
            {
                Economia_Porc_AtualProj = ((superv.Fatura_Cativo_AtualProj.ValorTotal - superv.Fatura_Livre_AtualProj.ValorTotal) / superv.Fatura_Cativo_AtualProj.ValorTotal) * 100.0;
            }

            ViewBag.Economia_Porc_AtualProj = string.Format("{0:0.00}%", Economia_Porc_AtualProj);

            // data atual
            string IDDashboardTxt = string.Format("dt{0}", IDDashboard);
            ViewBag.IDDashboardTxt = IDDashboardTxt;
            ViewBag.DataAtual = string.Format("{0:d} {1:HH:mm}", DataAtual, DataAtual);

            // ok
            return (0);
        }
    }
}