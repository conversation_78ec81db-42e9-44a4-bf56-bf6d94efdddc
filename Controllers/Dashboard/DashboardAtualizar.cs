﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class DashboardController : Controller
    {

        // GET: Dashboard Atualizar - página
        public PartialViewResult _DB_Atualizar_Page(int IDDashboardPage)
        {
            // prepara página e retorna view
            return PartialView(_DB_Atualizar_Page_Prepara(IDDashboardPage));
        }

        // GET: Dashboard Atualizar - página
        public PartialViewResult _DB_Atualizar_Page_Editar(int IDDashboardPage)
        {
            // prepara página e retorna view
            return PartialView(_DB_Atualizar_Page_Prepara(IDDashboardPage));
        }

        // GET: Dashboard Atualizar - prepara página
        public List<DashboardDominio> _DB_Atualizar_Page_Prepara(int IDDashboardPage)
        {
            // leio a pagina
            DashboardPageMetodos dashboardPageMetodos = new DashboardPageMetodos();
            DashboardPageDominio dashboardPage = dashboardPageMetodos.ListarPorId(IDDashboardPage);

            // leio todos dashboards da página
            DashboardMetodos dashboardMetodos = new DashboardMetodos();
            List<DashboardDominio> listaDashboards = new List<DashboardDominio>();
            listaDashboards = dashboardMetodos.ListarPorPagina(IDDashboardPage);

            // le cookie excluir cookies do dashboard
            bool excluir_cookie = CookieStore.LeCookie_Bool("DB_Excluir_DataHora");

            // verifica se existe página
            if (dashboardPage != null)
            {
                // completo dashboard com nome da pagina
                int contador;

                // zero contador de dashboard das paginas
                dashboardPage.numDashboard = 0;

                // percorre lista dashboard
                for (contador = 0; contador < listaDashboards.Count(); contador++)
                {
                    // caso achou a pagina, copio titulo
                    if (listaDashboards[contador].IDDashboardPage == dashboardPage.IDDashboardPage)
                    {
                        listaDashboards[contador].PageTitle = dashboardPage.Title;

                        // verifica se utilidades
                        if (listaDashboards[contador].IDTipoDashboard == TIPO_DASHBOARD.UTILIDADES_DIARIO || listaDashboards[contador].IDTipoDashboard == TIPO_DASHBOARD.UTILIDADES_MENSAL || listaDashboards[contador].IDTipoDashboard == TIPO_DASHBOARD.UTILIDADES_ANUAL)
                        {
                            // le configuracao medicao
                            var medicoesMetodos = new MedicoesMetodos();
                            var medicao = medicoesMetodos.ListarPorId(listaDashboards[contador].IDMedicao);

                            if (medicao != null)
                            {
                                // verifica se medicao de utilidades
                                if (medicao.IDTipoMedicao == TIPO_MEDICAO.UTILIDADES || medicao.IDTipoMedicao == TIPO_MEDICAO.UTILIDADES_FORMULA)
                                {
                                    // atualiza sub tipo
                                    listaDashboards[contador].IDSubTipoDashboard = medicao.IDIcone;
                                }
                            }
                        }

                        dashboardPage.numDashboard++;
                    }

                    // verifica se deve excluir cookie
                    if (excluir_cookie)
                    {
                        // apaga cookie datahora
                        string nome_cookie = string.Format("DB{0}", listaDashboards[contador].IDDashboard);
                        CookieStore.DeleteCookie(nome_cookie);
                    }
                }
            }

            // salva cookie excluir cookies do dashboard
            CookieStore.SalvaCookie_Bool("DB_Excluir_DataHora", false);

            ViewBag.dashboardPage = dashboardPage;

            // le cookies
            LeCookies_SmartEnergy();

            return (listaDashboards);
        }



        // GET: Dashboard Atualizar - dashboard
        public PartialViewResult _DB_Atualizar(int IDDashboard, int Navegacao, string DataAtualTxt)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // le dashboard
            DashboardMetodos dashboardMetodos = new DashboardMetodos();
            DashboardDominio itemDashboard = dashboardMetodos.ListarPorId(IDDashboard);

            // medicao
            if( itemDashboard.IDMedicao <= 0)
            {
                // verifica se nao tem medicao atual
                if (ViewBag._IDMedicao <= 0 )
                {
                    // erro
                    DB_Atualizar_Erro(itemDashboard.IDMedicao, "Selecionar a Medição");

                    // retorna sem atualizar
                    return PartialView(itemDashboard);
                }

                // utiliza medicao atual
                itemDashboard.IDMedicao = ViewBag._IDMedicao;
            }

            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(itemDashboard.IDMedicao);

            if( medicao == null)
            {
                // erro
                DB_Atualizar_Erro(itemDashboard.IDMedicao, "Medição Inexistente");

                // retorna sem atualizar
                return PartialView(itemDashboard);
            }

            // retorno da atualização da dashboard
            int dashboard_retorno = 0;

            // verifica se medicao de energia eletrica
            if ( medicao.IDTipoMedicao == TIPO_MEDICAO.ENERGIA || medicao.IDTipoMedicao == TIPO_MEDICAO.ENERGIA_FORMULA || medicao.IDTipoMedicao == TIPO_MEDICAO.CICLOMETRO)
            {
                // unidade de potencia
                UnidadePotencia(itemDashboard.IDMedicao);

                // verifica tipo dashboard
                switch (itemDashboard.IDTipoDashboard)
                {
                    case TIPO_DASHBOARD.DEMANDA_DIARIO:             // demanda

                        dashboard_retorno = DB_Atualizar_Demanda(IDDashboard, itemDashboard.IDMedicao, Navegacao, DataAtualTxt);
                        break;

                    case TIPO_DASHBOARD.CONSUMO:                    // consumo

                        dashboard_retorno = DB_Atualizar_Consumo(IDDashboard, itemDashboard.IDMedicao, Navegacao, DataAtualTxt);
                        break;

                    case TIPO_DASHBOARD.FATPOT:                     // fator de potencia

                        dashboard_retorno = DB_Atualizar_FatPot(IDDashboard, itemDashboard.IDMedicao, Navegacao, DataAtualTxt);
                        break;

                    case TIPO_DASHBOARD.FATURA:                     // fatura

                        dashboard_retorno = DB_Atualizar_Fatura(IDDashboard, itemDashboard.IDMedicao, Navegacao, DataAtualTxt);
                        break;

                    case TIPO_DASHBOARD.META:                       // meta

                        dashboard_retorno = DB_Atualizar_Meta(IDDashboard, itemDashboard.IDMedicao, Navegacao, DataAtualTxt);
                        break;

                    case TIPO_DASHBOARD.DEMANDA_MENSAL:             // demanda mensal

                        dashboard_retorno = DB_Atualizar_DemandaMensal(IDDashboard, itemDashboard.IDMedicao, Navegacao, DataAtualTxt);
                        break;

                    case TIPO_DASHBOARD.DEMANDA_ANUAL:              // demanda anual

                        dashboard_retorno = DB_Atualizar_DemandaAnual(IDDashboard, itemDashboard.IDMedicao, Navegacao, DataAtualTxt);
                        break;

                    case TIPO_DASHBOARD.FATURA_COMPARATIVO_ATUAL:   // comparativo fatura cativo x livre (atual)
                    case TIPO_DASHBOARD.FATURA_COMPARATIVO_PROJ:    // comparativo fatura cativo x livre (projetada)

                        dashboard_retorno = DB_Atualizar_Comparativo_Cativo_Livre(IDDashboard, itemDashboard.IDMedicao, Navegacao, DataAtualTxt);
                        break;

                    case TIPO_DASHBOARD.UFER:                       // energia reativa excedente (UFER)

                        dashboard_retorno = DB_Atualizar_UFER(IDDashboard, itemDashboard.IDMedicao, Navegacao, DataAtualTxt);
                        break;

                    case TIPO_DASHBOARD.KPI_ECONOMIA_DIARIA:        // KPI Economia Diaria

                        dashboard_retorno = DB_Atualizar_KPI_EconomiaDiaria(IDDashboard, itemDashboard.IDMedicao, Navegacao, DataAtualTxt);
                        break;

                    case TIPO_DASHBOARD.KPI_ECONOMIA_MENSAL:        // KPI Economia Mensal

                        dashboard_retorno = DB_Atualizar_KPI_EconomiaMensal(IDDashboard, itemDashboard.IDMedicao, Navegacao, DataAtualTxt);
                        break;

                    case TIPO_DASHBOARD.KPI_ECONOMIA_ANUAL:         // KPI Economia Anual

                        //dashboard_retorno = DB_Atualizar_KPI_EconomiaAnual(IDDashboard, itemDashboard.IDMedicao, Navegacao);
                        break;

                    case TIPO_DASHBOARD.KPI_POTENCIA_INSTALADA:     // KPI Potencia Instalada

                        dashboard_retorno = DB_Atualizar_KPI_PotenciaInstalada(IDDashboard, itemDashboard.IDMedicao, Navegacao, DataAtualTxt);
                        break;

                    case TIPO_DASHBOARD.GERALENERGIA_MENSAL:        // geral energia mensal

                        dashboard_retorno = DB_Atualizar_GeralEnergiaMensal(IDDashboard, itemDashboard.IDMedicao, Navegacao, DataAtualTxt);
                        break;
                }
            }

            // verifica se medicao de utilidades
            if( medicao.IDTipoMedicao == TIPO_MEDICAO.UTILIDADES || medicao.IDTipoMedicao == TIPO_MEDICAO.UTILIDADES_FORMULA)
            {
                // verifica tipo dashboard
                switch (itemDashboard.IDTipoDashboard)
                {
                    case TIPO_DASHBOARD.UTILIDADES_DIARIO:          // utilidades diário

                        // atualiza sub tipo
                        itemDashboard.IDSubTipoDashboard = medicao.IDIcone;

                        dashboard_retorno = DB_Atualizar_Utilidades(IDDashboard, itemDashboard.IDMedicao, Navegacao, DataAtualTxt);
                        break;

                    case TIPO_DASHBOARD.UTILIDADES_MENSAL:          // utilidades mensal

                        // atualiza sub tipo
                        itemDashboard.IDSubTipoDashboard = medicao.IDIcone;

                        dashboard_retorno = DB_Atualizar_UtilidadesMensal(IDDashboard, itemDashboard.IDMedicao, Navegacao, DataAtualTxt);
                        break;

                    case TIPO_DASHBOARD.UTILIDADES_ANUAL:           // utilidades anual

                        // atualiza sub tipo
                        itemDashboard.IDSubTipoDashboard = medicao.IDIcone;

                        dashboard_retorno = DB_Atualizar_UtilidadesAnual(IDDashboard, itemDashboard.IDMedicao, Navegacao, DataAtualTxt);
                        break;
                }
            }

            // verifica se medicao de entrada analogica
            if (medicao.IDTipoMedicao == TIPO_MEDICAO.ENTRADA_ANALOGICA || medicao.IDTipoMedicao == TIPO_MEDICAO.EA_FORMULA)
            {
                // verifica tipo dashboard
                switch (itemDashboard.IDTipoDashboard)
                {
                    case TIPO_DASHBOARD.EA:                         // entrada analogica

                        dashboard_retorno = DB_Atualizar_EA(IDDashboard, itemDashboard.IDMedicao, Navegacao, DataAtualTxt);
                        break;
                }
            }

            // verifica tipo dashboard
            switch (itemDashboard.IDTipoDashboard)
            {
                case TIPO_DASHBOARD.CLIMATEMPO:                     // tempo

                    dashboard_retorno = DB_Atualizar_Tempo(itemDashboard.IDMedicao);
                    break;
            }

            // verifica se erro ao atualizar dashboard
            if (dashboard_retorno > 0)
            {
                // erro
                DB_Atualizar_Erro(itemDashboard.IDMedicao, string.Format("Erro Código {0}", dashboard_retorno));
            }

            // verifica se nao atualizou por erro
            if ( ViewBag.NomeMedicao == null )
            {
                // erro
                DB_Atualizar_Erro(itemDashboard.IDMedicao, "Erro na Medição");
            }

            return PartialView(itemDashboard);
        }

        private void DB_Atualizar_Erro(int IDMedicao, string erroDashboardTexto)
        {
            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("DB_Editar");
            ViewBag.Editar = editar_cookie;

            // nome da medicao
            if( IDMedicao <= 0 )
            {
                ViewBag.NomeMedicao = "Selecionar a Medição";
            }
            else
            {
                ViewBag.NomeMedicao = medicao.Nome;
            }

            // converte data e hora
            DateTime Datahora = DateTime.Now;
            ViewBag.Datahora = string.Format("{0:g}", Datahora);

            // indica erro
            bool erro = true;
            ViewBag.ErroDashboard = erro;
            ViewBag.ErroDashboardTexto = erroDashboardTexto;

            return;
        }


        // GET: Próxima Medição
        public ActionResult ProximaMedicao(int IDDashboardPage)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };


            // leio a pagina
            DashboardPageMetodos dashboardPageMetodos = new DashboardPageMetodos();
            DashboardPageDominio dashboardPage = dashboardPageMetodos.ListarPorId(IDDashboardPage);

            // verifica se página cicla medição
            if (dashboardPage.CiclarMedicao == 0)
            {
                returnedData = new
                {
                    status = "NOK",
                    erro = ""
                };

                // retorna status
                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // le cookies
            LeCookies_SmartEnergy();

            // IDs
            int IDTipoAcesso = ViewBag._IDTipoAcesso;
            int IDCliente = ViewBag._IDCliente;
            List<int> ConfigMedList = ViewBag._ConfigMed;

            // le medicoes
            ListaMedicoesMetodos medicoesMetodos = new ListaMedicoesMetodos();
            List<SupervMedicoesDominio> medicoes = medicoesMetodos.ListarPorIDCliente(IDCliente, IDTipoAcesso, ConfigMedList);

            // medição atual
            int IDMedicao = ViewBag._IDMedicao;

            // a principio não achou medição atual na lista
            bool achou_medicao_atual = false;
            bool mudou_medicao_atual = false;

            // define próxima medição
            foreach (SupervMedicoesDominio med in medicoes)
            {
                // verifica se já achou medição atual, portanto a próxima medição da lista é a desejada (caso esta seja de energia)
                if (achou_medicao_atual && med.IDTipoMedicao_MD == TIPOS_MEDICAO.energia)
                {
                    // salva medição
                    CookieStore.SalvaCookie_Int("_IDMedicao", med.IDMD_MD);

                    // mudou medição atual
                    mudou_medicao_atual = true;

                    // termina
                    break;
                }

                // verifica se medição atual
                if (med.IDMD_MD == IDMedicao)
                {
                    // achou medição atual
                    achou_medicao_atual = true;
                }
            }

            // verifica se não achou medição atual ou se não mudou, uso a primeira medição de energia
            if (!achou_medicao_atual || !mudou_medicao_atual)
            {
                // pego a primeira medição de energia
                foreach (SupervMedicoesDominio med in medicoes)
                {
                    // verifica se medição de energia e uso como atual
                    if (med.IDTipoMedicao_MD == TIPOS_MEDICAO.energia)
                    {
                        // salva medição
                        CookieStore.SalvaCookie_Int("_IDMedicao", med.IDMD_MD);

                        // termina
                        break;
                    }
                }
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }
    }
}
