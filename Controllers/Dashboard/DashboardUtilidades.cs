﻿using System;
using System.Runtime.InteropServices;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class DashboardController : Controller
    {
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Dashboard_Util", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern sbyte SmCalcDB_Dashboard_Util(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref DATAHORA pdatahora, sbyte navega, ref DASHBOARD_UTIL psuperv);

        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Dashboard_UtilMensal", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern sbyte SmCalcDB_Dashboard_UtilMensal(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref DATAHORA pdatahora, sbyte navega, ref DASHBOARD_UTILMENSAL psuperv);

        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Dashboard_UtilAnual", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern sbyte SmCalcDB_Dashboard_UtilAnual(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref DATAHORA pdatahora, sbyte navega, ref DASHBOARD_UTILANUAL psuperv);


        private int DB_Atualizar_Utilidades(int IDDashboard, int IDMedicao, int Navegacao, string DataAtualTxt)
        {
            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("DB_Editar");
            ViewBag.Editar = editar_cookie;

            // le cookie datahora
            DateTime datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);

            if (DataAtualTxt != null)
            {
                datahora_cookie = DateTime.Parse(DataAtualTxt);
            }

            // verifica se atualizar apenas
            if (Navegacao == 0)
            {
                // forco setar data como ultimo registro
                datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);
            }


            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            DASHBOARD_UTIL superv = new DASHBOARD_UTIL();

            DATAHORA datahora = new DATAHORA();
            datahora.data.dia = (char)datahora_cookie.Day;
            datahora.data.mes = (char)datahora_cookie.Month;
            datahora.data.ano = (Int16)datahora_cookie.Year;
            datahora.hora.hora = (char)1;
            datahora.hora.min = (char)0;
            datahora.hora.seg = (char)0;

            // preenche interface
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = medicao.IDGateway;

            // funcao de supervisao
            int retorno;

            // calcula valores
            retorno = SmCalcDB_Dashboard_Util((char)0, ref config_interface, ref datahora, (sbyte)Navegacao, ref superv);

            // erro
            if (retorno != 0)
            {
                // erro
                return (retorno);
            }

            // converte data e hora
            DateTime DataAtual = new DateTime((int)superv.Data.data.ano,
                                              (int)superv.Data.data.mes,
                                              (int)superv.Data.data.dia,
                                              1, 0, 0);

            DateTime Valor_MaxDiaDataHora = new DateTime((int)superv.Valor_MaxDiaDataHora.data.ano,
                                                   (int)superv.Valor_MaxDiaDataHora.data.mes,
                                                   (int)superv.Valor_MaxDiaDataHora.data.dia,
                                                   (int)superv.Valor_MaxDiaDataHora.hora.hora,
                                                   (int)superv.Valor_MaxDiaDataHora.hora.min, 0);

            // nome da medicao
            ViewBag.NomeMedicao = medicao.Nome;

            // utilidades
            if (superv.flag_reg_diario == 1 || superv.flag_reg_diario == 2)
            {
                ViewBag.Valor_MaxDia = "---";
                ViewBag.Valor_MediaDia = "---";
                ViewBag.Valor_TotalDia = "---";
            }
            else
            {
                ViewBag.Valor_MaxDia = string.Format("{0:0.00}", superv.Valor_MaxDia);
                ViewBag.Valor_MediaDia = string.Format("{0:0.00}", superv.Valor_MediaDia);
                ViewBag.Valor_TotalDia = string.Format("{0:0.00}", superv.Valor_TotalDia);
            }
            ViewBag.Valor_MaxDiaDataHora = string.Format("{0:d} {1:HH:mm}", Valor_MaxDiaDataHora, Valor_MaxDiaDataHora);

            ViewBag.NomeGrandeza = medicao.NomeGrandeza;
            ViewBag.UnidadeGrandeza = medicao.UnidadeGrandeza;

            // datahoraAtual do dashboard
            if (Valor_MaxDiaDataHora.Year == 2000)
            {
                ViewBag.DataHoraAtual = string.Format("{0:d}", DataAtual);
            }
            else
            {
                ViewBag.DataHoraAtual = string.Format("{0:d} {1:HH:mm}", Valor_MaxDiaDataHora, Valor_MaxDiaDataHora);
            }

            // grafico
            var Valor = new double[26];
            var Status = new int[26];
            var Dias = new string[26];

            double Valor_min = 0.0;
            double Valor_max = 0.0;

            // valores
            DateTime strData = new DateTime((int)superv.DataHora[0].data.ano,
                                            (int)superv.DataHora[0].data.mes,
                                            (int)superv.DataHora[0].data.dia,
                                            (int)superv.DataHora[0].hora.hora,
                                            (int)superv.DataHora[0].hora.min, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proxima hora
                strData = strData.AddHours(1);

                // status
                if (superv.flag_reg_diario == 1 || superv.flag_reg_diario == 2)
                {
                    Status[i] = 1;
                }
                else
                {
                    Status[i] = 0;
                }

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    Valor[i] = superv.Valor[0];
                }

                // verifica se ultima barra
                if (i == 25)
                {
                    // zera
                    Valor[i] = superv.Valor[23];
                }

                // restante
                if (i > 0 && i < 25)
                {
                    // copia
                    j = i - 1;

                    // copia
                    Valor[i] = superv.Valor[j];

                    // verifica valor minimo
                    if (Valor[i] < Valor_min)
                        Valor_min = Valor[i];

                    // verifica valor maximo
                    if (Valor[i] > Valor_max)
                        Valor_max = Valor[i];
                }
            }

            // grafico comeca em zero caso minimo maior que zero
            if (Valor_min > 0.0)
            {
                Valor_min = 0.0;
            }

            Valor_min = Valor_min * 1.1;
            Valor_max = Valor_max * 1.1;

            if (Valor_max == 0.0)
            {
                Valor_max = 1.0;
            }

            ViewBag.Valor_min = Valor_min;
            ViewBag.Valor_max = Valor_max;

            ViewBag.Valor = Valor;
            ViewBag.Status = Status;
            ViewBag.Dias = Dias;

            // data atual
            string IDDashboardTxt = string.Format("dt{0}", IDDashboard);
            ViewBag.IDDashboardTxt = IDDashboardTxt;
            ViewBag.DataAtual = string.Format("{0:d} {1:HH:mm}", DataAtual, DataAtual);

            // ok
            return (0);
        }


        private int DB_Atualizar_UtilidadesMensal(int IDDashboard, int IDMedicao, int Navegacao, string DataAtualTxt)
        {
            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("DB_Editar");
            ViewBag.Editar = editar_cookie;

            // le cookie datahora
            DateTime datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);

            if (DataAtualTxt != null)
            {
                datahora_cookie = DateTime.Parse(DataAtualTxt);
            }

            // verifica se atualizar apenas
            if (Navegacao == 0)
            {
                // forco setar data como ultimo registro
                datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);
            }


            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            DASHBOARD_UTILMENSAL superv = new DASHBOARD_UTILMENSAL();

            DATAHORA datahora = new DATAHORA();
            datahora.data.dia = (char)datahora_cookie.Day;
            datahora.data.mes = (char)datahora_cookie.Month;
            datahora.data.ano = (Int16)datahora_cookie.Year;
            datahora.hora.hora = (char)1;
            datahora.hora.min = (char)0;
            datahora.hora.seg = (char)0;

            // preenche interface
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = medicao.IDGateway;

            // funcao de supervisao
            sbyte retorno;

            // calcula valores
            retorno = SmCalcDB_Dashboard_UtilMensal((char)0, ref config_interface, ref datahora, (sbyte)Navegacao, ref superv);

            // erro
            if (retorno != 0)
            {
                // erro
                return (retorno);
            }

            // converte data e hora
            DateTime DataAtual = new DateTime((int)superv.Data.data.ano,
                                              (int)superv.Data.data.mes,
                                              1, 1, 0, 0);

            DateTime Valor_MaxMesDataHora = new DateTime((int)superv.Valor_MaxMesDataHora.data.ano,
                                                   (int)superv.Valor_MaxMesDataHora.data.mes,
                                                   (int)superv.Valor_MaxMesDataHora.data.dia,
                                                   (int)superv.Valor_MaxMesDataHora.hora.hora,
                                                   (int)superv.Valor_MaxMesDataHora.hora.min, 0);

            // nome da medicao
            ViewBag.NomeMedicao = medicao.Nome;

            // utilidades
            if (superv.flag_reg_mensal == 1 || superv.flag_reg_mensal == 2)
            {
                ViewBag.Valor_MaxMes = "---";
                ViewBag.Valor_MediaMes = "---";
                ViewBag.Valor_TotalMes = "---";
            }
            else
            {
                ViewBag.Valor_MaxMes = string.Format("{0:0.00}", superv.Valor_MaxMes);
                ViewBag.Valor_MediaMes = string.Format("{0:0.00}", superv.Valor_MediaMes);
                ViewBag.Valor_TotalMes = string.Format("{0:0.00}", superv.Valor_TotalMes);
            }
            ViewBag.Valor_MaxMesDataHora = string.Format("{0:d} {1:HH:mm}", Valor_MaxMesDataHora, Valor_MaxMesDataHora);

            ViewBag.NomeGrandeza = medicao.NomeGrandeza;
            ViewBag.UnidadeGrandeza = medicao.UnidadeGrandeza;

            // datahoraAtual do dashboard
            ViewBag.DataHoraAtual = string.Format("{0:Y}", DataAtual);

            // grafico
            var Valor = new double[40];
            var Status = new int[40];
            var Dias = new string[40];

            double Valor_min = 0.0;
            double Valor_max = 0.0;

            // valores
            DateTime strData = new DateTime((int)superv.DataHora[0].data.ano,
                                            (int)superv.DataHora[0].data.mes,
                                            1, 1, 0, 0);

            int i = 0;

            for (i = 0; i < superv.NumDias; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proximo dia
                strData = strData.AddDays(1);

                // status
                if (superv.flag_reg_mensal == 1 || superv.flag_reg_mensal == 2)
                {
                    Status[i] = 1;
                }
                else
                {
                    Status[i] = 0;
                }

                // copia
                Valor[i] = superv.Valor[i];

                // verifica valor minimo
                if (Valor[i] < Valor_min)
                    Valor_min = Valor[i];

                // verifica valor maximo
                if (Valor[i] > Valor_max)
                    Valor_max = Valor[i];
            }

            // grafico comeca em zero caso minimo maior que zero
            if (Valor_min > 0.0)
            {
                Valor_min = 0.0;
            }

            Valor_min = Valor_min * 1.1;
            Valor_max = Valor_max * 1.1;

            if (Valor_max == 0.0)
            {
                Valor_max = 1.0;
            }

            ViewBag.Valor_min = Valor_min;
            ViewBag.Valor_max = Valor_max;

            ViewBag.Valor = Valor;
            ViewBag.Status = Status;
            ViewBag.Dias = Dias;
            ViewBag.NumDias = superv.NumDias;

            // data atual
            string IDDashboardTxt = string.Format("dt{0}", IDDashboard);
            ViewBag.IDDashboardTxt = IDDashboardTxt;
            ViewBag.DataAtual = string.Format("{0:d} {1:HH:mm}", DataAtual, DataAtual);

            // ok
            return (0);
        }

        private int DB_Atualizar_UtilidadesAnual(int IDDashboard, int IDMedicao, int Navegacao, string DataAtualTxt)
        {
            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("DB_Editar");
            ViewBag.Editar = editar_cookie;

            // le cookie datahora
            DateTime datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);

            if (DataAtualTxt != null)
            {
                datahora_cookie = DateTime.Parse(DataAtualTxt);
            }

            // verifica se atualizar apenas
            if (Navegacao == 0)
            {
                // forco setar data como ultimo registro
                datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);
            }


            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            DASHBOARD_UTILANUAL superv = new DASHBOARD_UTILANUAL();

            DATAHORA datahora = new DATAHORA();
            datahora.data.dia = (char)1;
            datahora.data.mes = (char)1;
            datahora.data.ano = (Int16)datahora_cookie.Year;
            datahora.hora.hora = (char)1;
            datahora.hora.min = (char)0;
            datahora.hora.seg = (char)0;

            // preenche interface
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = medicao.IDGateway;

            // funcao de supervisao
            sbyte retorno;

            // calcula valores
            retorno = SmCalcDB_Dashboard_UtilAnual((char)0, ref config_interface, ref datahora, (sbyte)Navegacao, ref superv);

            // erro
            if (retorno != 0)
            {
                // erro
                return (retorno);
            }

            // converte data e hora
            DateTime DataAtual = new DateTime((int)superv.Data.data.ano,
                                              1, 1, 1, 0, 0);

            DateTime Valor_MaxAnoDataHora = new DateTime((int)superv.Valor_MaxAnoDataHora.data.ano,
                                                   (int)superv.Valor_MaxAnoDataHora.data.mes,
                                                   (int)superv.Valor_MaxAnoDataHora.data.dia,
                                                   (int)superv.Valor_MaxAnoDataHora.hora.hora,
                                                   (int)superv.Valor_MaxAnoDataHora.hora.min, 0);

            // nome da medicao
            ViewBag.NomeMedicao = medicao.Nome;

            // utilidades
            if (superv.flag_reg_anual == 1 || superv.flag_reg_anual == 2)
            {
                ViewBag.Valor_MaxAno = "---";
                ViewBag.Valor_MediaAno = "---";
                ViewBag.Valor_TotalAno = "---";
            }
            else
            {
                ViewBag.Valor_MaxAno = string.Format("{0:0.00}", superv.Valor_MaxAno);
                ViewBag.Valor_MediaAno = string.Format("{0:0.00}", superv.Valor_MediaAno);
                ViewBag.Valor_TotalAno = string.Format("{0:0.00}", superv.Valor_TotalAno);
            }
            ViewBag.Valor_MaxAnoDataHora = string.Format("{0:d} {1:HH:mm}", Valor_MaxAnoDataHora, Valor_MaxAnoDataHora);

            ViewBag.NomeGrandeza = medicao.NomeGrandeza;
            ViewBag.UnidadeGrandeza = medicao.UnidadeGrandeza;

            // datahoraAtual do dashboard
            ViewBag.DataHoraAtual = string.Format("{0}", DataAtual.Year);

            // grafico
            var Valor = new double[12];
            var Status = new int[12];
            var Dias = new string[12];

            double Valor_min = 0.0;
            double Valor_max = 0.0;

            // valores
            DateTime strData = new DateTime((int)superv.DataHora[0].data.ano, 1, 1, 1, 0, 0);

            int i = 0;

            for (i = 0; i < 12; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proximo mes
                strData = strData.AddMonths(1);

                // status
                if (superv.flag_reg_anual == 1 || superv.flag_reg_anual == 2)
                {
                    Status[i] = 1;
                }
                else
                {
                    Status[i] = 0;
                }

                // copia
                Valor[i] = superv.Valor[i];

                // verifica valor minimo
                if (Valor[i] < Valor_min)
                    Valor_min = Valor[i];

                // verifica valor maximo
                if (Valor[i] > Valor_max)
                    Valor_max = Valor[i];
            }

            // grafico comeca em zero caso minimo maior que zero
            if (Valor_min > 0.0)
            {
                Valor_min = 0.0;
            }

            Valor_min = Valor_min * 1.1;
            Valor_max = Valor_max * 1.1;

            if (Valor_max == 0.0)
            {
                Valor_max = 1.0;
            }

            ViewBag.Valor_min = Valor_min;
            ViewBag.Valor_max = Valor_max;

            ViewBag.Valor = Valor;
            ViewBag.Status = Status;
            ViewBag.Dias = Dias;

            // data atual
            string IDDashboardTxt = string.Format("dt{0}", IDDashboard);
            ViewBag.IDDashboardTxt = IDDashboardTxt;
            ViewBag.DataAtual = string.Format("{0:d} {1:HH:mm}", DataAtual, DataAtual);

            // ok
            return (0);
        }
    }
}