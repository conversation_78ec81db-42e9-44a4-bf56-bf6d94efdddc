﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class DashboardController : Controller
    {
        // GET: Dashboard modo Editar - Usuario
        public ActionResult DB_Superv_Editar(int IDUsuario, int IDDashboardGrupo = 0)
        {
            // tela de ajuda - dashboard configuracao
            CookieStore.SalvaCookie_String("PaginaAjuda", "Dashboard_Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // modo editar
            CookieStore.SalvaCookie_Bool("DB_Editar", true);

            // numero da pagina
            int PageNumber = CookieStore.LeCookie_Int("PageNumberDashboard");
            if (PageNumber < 0) PageNumber = 0;
            CookieStore.SalvaCookie_Int("PageNumberDashboard", PageNumber);
            ViewBag.PageNumber = PageNumber;

            // IDReferencia
            int IDReferencia = IDUsuario;
            int TipoReferencia = DB_REFERENCIA.IDUsuario;

            if (IDDashboardGrupo > 0)
            {
                IDReferencia = IDDashboardGrupo;
                TipoReferencia = DB_REFERENCIA.IDDashboardGrupo;
            }
            else
            {
                // le usuário
                UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
                UsuarioDominio usuario = usuarioMetodos.ListarPorId(IDUsuario);

                // verifica se usuário quer dashboard dele ou do modelo
                if (usuario != null)
                {
                    if (usuario.IDDashboardGrupo > 0)
                    {
                        IDReferencia = usuario.IDDashboardGrupo;
                        TipoReferencia = DB_REFERENCIA.IDDashboardGrupo;
                    }

                    // atualiza IDDashboardGrupo
                    IDDashboardGrupo = usuario.IDDashboardGrupo;
                }
            }

            // atualiza cookie
            CookieStore.SalvaCookie_Int("IDDashboardGrupo", IDDashboardGrupo);
            ViewBag._IDDashboardGrupo = IDDashboardGrupo;

            // nome do grupo de paineis
            string NomeDashboardGrupo = "";

            if (IDDashboardGrupo > 0)
            {
                // le grupo
                DashboardGrupoMetodos grupoMetodos = new DashboardGrupoMetodos();
                DashboardGrupoDominio grupo = grupoMetodos.ListarPorId(IDDashboardGrupo);

                if (grupo != null)
                {
                    NomeDashboardGrupo = grupo.Nome;
                }
            }

            ViewBag.NomeDashboardGrupo = NomeDashboardGrupo;

            return View(DB_Superv_Prepara(IDReferencia, TipoReferencia));
        }

        // GET: Dashboard Excluir 
        public JsonResult _DB_Excluir(int IDDashboard)
        {
            // apaga dashboard
            var dashboardMetodos = new DashboardMetodos();
            dashboardMetodos.Excluir(IDDashboard);

            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // GET: Dashboard Editar Modal
        public PartialViewResult _DB_Editar(int IDDashboard, int IDDashboardPage = 0)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // grupo dashboard
            int IDDashboardGrupo = ViewBag._IDDashboardGrupo;

            // metodos
            DashboardMetodos dashboardMetodos = new DashboardMetodos();
            DashboardPageMetodos dashboardPageMetodos = new DashboardPageMetodos();
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            ClientesMetodos clientesMetodos = new ClientesMetodos();

            // dashboard
            DashboardDominio itemDashboard = new DashboardDominio();

            // verifica se eh edicao
            if( IDDashboard > 0)
            {
                // le dashboard
                itemDashboard = dashboardMetodos.ListarPorId(IDDashboard);

                // leio pagina do dashboard
                DashboardPageDominio itemDashboardsPage = dashboardPageMetodos.ListarPorIdReferenciaPagina(itemDashboard.IDReferencia, itemDashboard.TipoReferencia, itemDashboard.IDDashboardPage);

                // nome da pagina
                itemDashboard.PageTitle = itemDashboardsPage.Title;

                if (itemDashboard.IDMedicao > 0)
                {
                    // le configuracao medicao
                    var medicao = medicoesMetodos.ListarPorId(itemDashboard.IDMedicao);

                    // nome da medicao
                    ViewBag.NomeMedicao = medicao.Nome;
                    ViewBag._IDTipoMedicao = medicao.IDTipoMedicao;

                    // le configuracao cliente
                    ClientesDominio cliente = clientesMetodos.ListarPorId(medicao.IDCliente);

                    // nome do cliente
                    ViewBag.NomeCliente = cliente.Nome;
                }
                else
                {
                    // nome da medicao
                    ViewBag.NomeMedicao = "Utilizar a Medição Atual";
                    ViewBag._IDTipoMedicao = -10;

                    // nome do cliente
                    ViewBag.NomeCliente = "Utilizar o Cliente Atual";
                }
            }
            else
            {
                // zera dashboard
                itemDashboard.IDDashboard = 0;

                // referência
                int IDReferencia = ViewBag._IDUsuario;
                int TipoReferencia = DB_REFERENCIA.IDUsuario;

                // verifica se esta usando grupo
                if (IDDashboardGrupo > 0)
                {
                    IDReferencia = IDDashboardGrupo;
                    TipoReferencia = DB_REFERENCIA.IDDashboardGrupo;
                }

                itemDashboard.IDReferencia = IDReferencia;
                itemDashboard.TipoReferencia = TipoReferencia;

                itemDashboard.IDTipoDashboard = -10;
                itemDashboard.IDMedicao = 0;

                if (itemDashboard.IDMedicao > 0)
                {
                    // le configuracao medicao
                    MedicoesDominio medicao = medicoesMetodos.ListarPorId(itemDashboard.IDMedicao);

                    // nome da medicao
                    ViewBag.NomeMedicao = medicao.Nome;
                    ViewBag._IDTipoMedicao = medicao.IDTipoMedicao;

                    // le configuracao cliente
                    ClientesDominio cliente = clientesMetodos.ListarPorId(medicao.IDCliente);

                    // nome do cliente
                    ViewBag.NomeCliente = cliente.Nome;
                }
                else
                {
                    // nome da medicao
                    ViewBag.NomeMedicao = "Utilizar a Medição Atual";
                    ViewBag._IDTipoMedicao = -10;

                    // nome do cliente
                    ViewBag.NomeCliente = "Utilizar o Cliente Atual";
                }

                itemDashboard.IDDashboardPage = IDDashboardPage;

                List<DashboardDominio> listaDashboard = dashboardMetodos.ListarPorPagina(IDDashboardPage);

                int linha_atual = -1;
                int coluna_atual = -1;

                foreach(DashboardDominio dashboard in listaDashboard)
                {
                    // primeira passada
                    if(linha_atual < 0)
                    {
                        linha_atual = dashboard.RowNumber;
                        coluna_atual = dashboard.ColumnNumber;
                    }

                    // verifica se mudou de linha
                    if( linha_atual != dashboard.RowNumber)
                    {
                        // mudou de linha, verifico se tem espaco na linha anterior
                        if(coluna_atual < 3)
                        {
                            // tem espaco, coloco dashboard aqui
                            itemDashboard.RowNumber = linha_atual;
                            itemDashboard.ColumnNumber = coluna_atual+1;

                            break;
                        }
                    }

                    // copia
                    linha_atual = dashboard.RowNumber;
                    coluna_atual = dashboard.ColumnNumber;
                }

                itemDashboard.Title = "";
                itemDashboard.PageTitle = "";
            }

            //
            // Lista de clientes e medicoes
            //
            var GrupoUnidMedicoesMetodos = new CliGateGrupoUnidMedicoesMetodos();

            // tipo acesso
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // verifica se cliente gerente ou operador
            if (isUser.isCliente(IDTipoAcesso))
            {
                // lista de todas medicoes habilitadas para o usuario
                List<int> ConfigMedicaoList = ViewBag._ConfigMed;

                // le todas medicoes para lista
                var GrupoUnidMedicoes = GrupoUnidMedicoesMetodos.ListarPorIDCliente(ViewBag._IDCliente, ConfigMedicaoList);
                ViewBag.GrupoUnidMedicoes = GrupoUnidMedicoes;
            }
            // verifica se consultor
            else if (isUser.isConsultor(IDTipoAcesso))
            {
                // lista de medicoes
                List<CliGateGrupoUnidMedicoesDominio> GrupoUnidMedicoes = new List<CliGateGrupoUnidMedicoesDominio>();

                // verifica se esta editando grupos de paineis para algum cliente
                if (IDDashboardGrupo > 0 && IDCliente > 0)
                {
                    // todas as medições do cliente
                    List<CliGateGrupoUnidMedicoesDominio> medicoes3 = GrupoUnidMedicoesMetodos.ListarPorIDCliente(IDCliente);

                    foreach (CliGateGrupoUnidMedicoesDominio medicao in medicoes3)
                    {
                        // coloca na lista
                        GrupoUnidMedicoes.Add(medicao);
                    }
                }
                else
                {
                    // lista de todas cliente e medicoes habilitadas para o usuario
                    List<int> ConfigClienteList = ViewBag._ConfigCli;
                    List<int> ConfigMedicaoList = ViewBag._ConfigMed;

                    // verifica se tem lista de cliente
                    if (ConfigClienteList != null)
                    {
                        // percorre lista de clientes
                        foreach (int id_cliente in ConfigClienteList)
                        {
                            // le medicoes para lista
                            List<CliGateGrupoUnidMedicoesDominio> medicoes3 = new List<CliGateGrupoUnidMedicoesDominio>();

                            // verifica se operador
                            if (IDTipoAcesso == TIPO_ACESSO.CONSULTOR_OPER)
                            {
                                // somente as medições habilitadas
                                medicoes3 = GrupoUnidMedicoesMetodos.ListarPorIDCliente(id_cliente, ConfigMedicaoList);
                            }
                            else
                            {
                                // todas as medições do cliente
                                medicoes3 = GrupoUnidMedicoesMetodos.ListarPorIDCliente(id_cliente);
                            }

                            foreach (CliGateGrupoUnidMedicoesDominio medicao in medicoes3)
                            {
                                // coloca na lista
                                GrupoUnidMedicoes.Add(medicao);
                            }
                        }
                    }
                }

                ViewBag.GrupoUnidMedicoes = GrupoUnidMedicoes;
            }
            else
            {
                // le todas medicoes para lista
                List<CliGateGrupoUnidMedicoesDominio> GrupoUnidMedicoes = GrupoUnidMedicoesMetodos.ListarPorIDCliente();
                ViewBag.GrupoUnidMedicoes = GrupoUnidMedicoes;
            }

            return PartialView(itemDashboard);
        }

        // POST: Dashboard Editar Salvar
        [HttpPost]
        public ActionResult _DB_Editar(DashboardDominio dashboard)
        {

            // salva dashboard
            var dashboardMetodos = new DashboardMetodos();
            dashboardMetodos.Salvar(dashboard);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData);
        }

        [HttpPost]
        public JsonResult SalvarPosicoes(List<DashboardPosicaoDominio> dashboards)
        {
            var dashboardMetodos = new DashboardMetodos();

            // percorre lista de paineis
            foreach (var dbpos in dashboards)
            {
                // salva
                dashboardMetodos.SalvarPosicao(dbpos);
            }

            string result = "OK";
            return Json(result, JsonRequestBehavior.AllowGet);
        }


        //
        // Painel
        //

        // movimenta painel para esquerda ou direita
        public JsonResult MoverPainel(int IDDashboardPage, int direcao)
        {
            // metodos
            DashboardPageMetodos dashboardPageMetodos = new DashboardPageMetodos();

            // ler DashboardPage do IDDashboardPage
            DashboardPageDominio dashboardPage = dashboardPageMetodos.ListarPorId(IDDashboardPage);

            // pegar PageNumber
            int PageNumber = dashboardPage.PageNumber;
            int PageNumberNovo = 0;

            // descobre quantos DashboardPage existem
            List<DashboardPageDominio> listaDashboardsPage = dashboardPageMetodos.ListarPorIdReferencia(dashboardPage.IDReferencia, dashboardPage.TipoReferencia);

            int NumDashboardPage = listaDashboardsPage.Count;

            // subtrair ou somar dependendo da direcao
            // 0 - esquerda - subtrair
            // 1 - direita - somar
            if (direcao == 0)
            {
                // verifica se primeiro
                if (PageNumber == 0)
                {
                    // nao faco nada
                    return Json("OK", JsonRequestBehavior.AllowGet);
                }

                // subtrai
                PageNumberNovo = PageNumber - 1;
            }
            else
            {
                // verifica se ultimo
                if (PageNumber == (NumDashboardPage-1))
                {
                    // nao faco nada
                    return Json("OK", JsonRequestBehavior.AllowGet);
                }

                // soma
                PageNumberNovo = PageNumber + 1;
            }

            // procurar DashboardPage do PageNumber
            DashboardPageDominio dashboardPageOriginal = dashboardPageMetodos.ListarPorPagenumber(dashboardPage.IDReferencia, dashboardPage.TipoReferencia, PageNumberNovo);

            // troco os PageNumber
            dashboardPageOriginal.PageNumber = PageNumber;
            dashboardPage.PageNumber = PageNumberNovo;

            // salvo mudanca
            dashboardPageMetodos.Salvar(dashboardPageOriginal);
            dashboardPageMetodos.Salvar(dashboardPage);

            // numero da pagina
            CookieStore.SalvaCookie_Int("PageNumberDashboard", PageNumberNovo);
            ViewBag.PageNumber = PageNumber;

            // retorno
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // adiciona painel
        public JsonResult AdicionarPainel()
        {
            // metodos
            DashboardPageMetodos dashboardPageMetodos = new DashboardPageMetodos();

            // le cookie IDCliente e IDUsuario
            int IDCliente = CookieStore.LeCookie_Int("_IDCliente");
            int IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");
            int IDDashboardGrupo = CookieStore.LeCookie_Int("IDDashboardGrupo");

            // referência
            int IDReferencia = IDUsuario;
            int TipoReferencia = DB_REFERENCIA.IDUsuario;

            // verifica se esta usando grupo
            if (IDDashboardGrupo > 0)
            {
                IDReferencia = IDDashboardGrupo;
                TipoReferencia = DB_REFERENCIA.IDDashboardGrupo;
            }

            // descobre quantos DashboardPage existem
            List<DashboardPageDominio> listaDashboardsPage = dashboardPageMetodos.ListarPorIdReferencia(IDReferencia, TipoReferencia);

            int NumDashboardPage = listaDashboardsPage.Count;

            // adiciona painel
            DashboardPageDominio dashboardPageNovo = new DashboardPageDominio();

            dashboardPageNovo.IDDashboardPage = 0;
            dashboardPageNovo.IDCliente = IDCliente;
            dashboardPageNovo.IDReferencia = IDReferencia;
            dashboardPageNovo.TipoReferencia = TipoReferencia;
            dashboardPageNovo.PageNumber = NumDashboardPage;
            dashboardPageNovo.Title = string.Format("Página {0}", NumDashboardPage+1);
            dashboardPageNovo.CiclarMedicao = 0;

            dashboardPageMetodos.Salvar(dashboardPageNovo);

            // numero da pagina
            CookieStore.SalvaCookie_Int("PageNumberDashboard", NumDashboardPage);
            ViewBag.PageNumber = NumDashboardPage;

            // retorno
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // apaga painel
        public JsonResult ExcluirPainel(int IDDashboardPage)
        {
            // metodos
            DashboardMetodos dashboardMetodos = new DashboardMetodos();
            DashboardPageMetodos dashboardPageMetodos = new DashboardPageMetodos();

            // ler DashboardPage do IDDashboardPage
            DashboardPageDominio dashboardPage = dashboardPageMetodos.ListarPorId(IDDashboardPage);

            // apaga pagina
            dashboardPageMetodos.Excluir(IDDashboardPage);

            // apaga dashboards
            dashboardMetodos.ExcluirTodosPagina(IDDashboardPage);

            // reordena paginas
            dashboardPageMetodos.ReordenaPaginas(dashboardPage.IDReferencia, dashboardPage.TipoReferencia);

            // numero da pagina
            CookieStore.SalvaCookie_Int("PageNumberDashboard", 0);
            ViewBag.PageNumber = 0;

            // retorno
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // GET: Painel Editar Modal
        public PartialViewResult _Painel_Editar(int IDDashboardPage)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // metodos
            DashboardPageMetodos dashboardPageMetodos = new DashboardPageMetodos();

            // dashboard
            DashboardPageDominio itemDashboardPage = new DashboardPageDominio();

            // verifica se eh edicao
            if (IDDashboardPage > 0)
            {
                // le dashboard
                itemDashboardPage = dashboardPageMetodos.ListarPorId(IDDashboardPage);
            }
            else
            {
                // le cookie IDCliente e IDUsuario
                int IDCliente = CookieStore.LeCookie_Int("_IDCliente");
                int IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");
                int IDDashboardGrupo = CookieStore.LeCookie_Int("IDDashboardGrupo");

                // referência
                int IDReferencia = IDUsuario;
                int TipoReferencia = DB_REFERENCIA.IDUsuario;

                // verifica se esta usando grupo
                if (IDDashboardGrupo > 0)
                {
                    IDReferencia = IDDashboardGrupo;
                    TipoReferencia = DB_REFERENCIA.IDDashboardGrupo;
                }

                // descobre quantos DashboardPage existem
                List<DashboardPageDominio> listaDashboardsPage = dashboardPageMetodos.ListarPorIdReferencia(IDReferencia, TipoReferencia);

                int NumDashboardPage = listaDashboardsPage.Count;

                // nova página
                itemDashboardPage.IDDashboardPage = 0;
                itemDashboardPage.IDCliente = IDCliente;
                itemDashboardPage.IDReferencia = IDReferencia;
                itemDashboardPage.TipoReferencia = TipoReferencia;
                itemDashboardPage.PageNumber = NumDashboardPage;
                itemDashboardPage.Title = string.Format("Página {0}", NumDashboardPage + 1);
            }

            return PartialView(itemDashboardPage);
        }

        // POST: Painel Editar Salvar
        [HttpPost]
        public ActionResult _Painel_Editar(DashboardPageDominio dashboardPage)
        {
            // salva dashboard page
            DashboardPageMetodos dashboardPageMetodos = new DashboardPageMetodos();
            dashboardPageMetodos.Salvar(dashboardPage);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData);
        }
    }
}