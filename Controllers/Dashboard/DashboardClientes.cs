﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class DashboardController
    {
        // GET: Dashboard Clientes
        public ActionResult DB_Clientes()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // consultor
            int IDConsultor = ViewBag._IDConsultor;

            // verifica se CPFL
            if (IDConsultor == CLIENTES_ESPECIAIS.CPFL)
            {
                // tela de ajuda - dashboard clientes CPFL
                CookieStore.SalvaCookie_String("PaginaAjuda", "Dashboard_Painel_CPFL");
            }
            else
            {
                // tela de ajuda - dashboard clientes
                CookieStore.SalvaCookie_String("PaginaAjuda", "Dashboard_Painel");
            }


            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Dashboard");

            // le cookies
            LeCookies_SmartEnergy();

            // verifica se nao eh admin
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            if (IDTipoAcesso == TIPO_ACESSO.CLIENTE_ADMIN || IDTipoAcesso == TIPO_ACESSO.CLIENTE_OPER)
            {
                return (Redirect("~/Login/Login"));
            }

            // lista de clientes
            List<int> ConfigCliList = ViewBag._ConfigCli;

            // le clientes
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clientesMetodos.ListarTodos(ConfigCliList);

            // percorre clientes e descobre numero de grupos de painéis
            foreach (ClientesDominio cliente in listaClientes)
            {
                // le grupos de painéis do cliente
                DashboardGrupoMetodos dashboardGrupoMetodos = new DashboardGrupoMetodos();
                int NumGruposPaineis = dashboardGrupoMetodos.NumGruposPaineisCliente(cliente.IDCliente);
                cliente.NumGruposPaineis = NumGruposPaineis;
            }

            return View(listaClientes);
        }

        // GET: Dashboard Cliente - Grupos de Painéis
        public ActionResult DB_Cliente_GruposPaineis(int IDCliente)
        {
            // salva cookie
            CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);

            // le contrato cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cli = clienteMetodos.ListarPorId(IDCliente);
            CookieStore.SalvaCookie_Int("IDTipoContrato", cli.IDTipoContrato);
            CookieStore.SalvaCookie_Int("IDTipoSupervisao", cli.IDTipoSupervisao);
            CookieStore.SalvaCookie_String("Nome_GrupoUnidades", cli.Nome_GrupoUnidades);
            CookieStore.SalvaCookie_String("Nome_Unidades", cli.Nome_Unidades);
            CookieStore.SalvaCookie_Int("IDTipoGrafico_Demanda", cli.IDTipoGrafico_Demanda);

            // tela de ajuda - dashboard clientes
            CookieStore.SalvaCookie_String("PaginaAjuda", "Dashboard_Painel");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Dashboard");

            // le cookies
            LeCookies_SmartEnergy();

            // le grupos de painéis
            DashboardGrupoMetodos dashboardGrupoMetodos = new DashboardGrupoMetodos();
            List<DashboardGrupoDominio> listaDashBoardGrupo = dashboardGrupoMetodos.ListarPorIdCliente(IDCliente);

            // percorre grupos de painéis e descobre numero de painéis e blocos
            foreach (DashboardGrupoDominio grupo in listaDashBoardGrupo)
            {
                // le painéis do grupo
                DashboardPageMetodos dashboardPageMetodos = new DashboardPageMetodos();
                int NumPaineis = dashboardPageMetodos.NumPaineis(grupo.IDDashboardGrupo, DB_REFERENCIA.IDDashboardGrupo);
                grupo.NumPaineis = NumPaineis;

                // le blocos do grupo
                DashboardMetodos dashboardMetodos = new DashboardMetodos();
                int NumBlocos = dashboardMetodos.NumBlocos(grupo.IDDashboardGrupo, DB_REFERENCIA.IDDashboardGrupo);
                grupo.NumBlocos = NumBlocos;
            }

            return View(listaDashBoardGrupo);
        }

        // GET: Dashboard Cliente - Grupos de Painéis - Editar
        public ActionResult DB_Cliente_GrupoPaineis_Editar(int IDDashboardGrupo)
        {
            // tela de ajuda - grupo de unidades
            CookieStore.SalvaCookie_String("PaginaAjuda", "Dashboard_Painel");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Dashboard");

            // le cookies
            LeCookies_SmartEnergy();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // verifica se adicionando
            DashboardGrupoDominio grupo = new DashboardGrupoDominio();
            if (IDDashboardGrupo == 0)
            {
                // zera grupounidades com default
                grupo.IDCliente = ViewBag._IDCliente;
                grupo.IDConsultor = IDConsultor;
            }
            else
            {
                // le grupo
                DashboardGrupoMetodos dashboardGrupoMetodos = new DashboardGrupoMetodos();
                grupo = dashboardGrupoMetodos.ListarPorId(IDDashboardGrupo);
            }

            return View(grupo);
        }

        // POST: Dashboard Cliente - Grupos de Painéis - Salvar
        [HttpPost]
        public ActionResult DB_Cliente_GrupoPaineis_Salvar(DashboardGrupoDominio grupo)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro grupo com o mesmo nome
            DashboardGrupoMetodos dashboardGrupoMetodos = new DashboardGrupoMetodos();
            if (dashboardGrupoMetodos.VerificarDuplicidade(grupo))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Grupo de Painéis existente."
                };
            }
            else
            {
                // salva grupo de painéis
                dashboardGrupoMetodos.Salvar(grupo);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Dashboard Cliente - Grupos de Painéis - Excluir
        public ActionResult DB_Cliente_GrupoPaineis_Excluir(int IDDashboardGrupo)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // apaga o grupo paineis
            DashboardGrupoMetodos dashboardGrupoMetodos = new DashboardGrupoMetodos();
            dashboardGrupoMetodos.Excluir(IDDashboardGrupo);

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }
    }
}