﻿////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// [Classe] Dashboard
//
// Métodos do 'Dashboard'
//
// Supervisão dos Dashboards
//
// DB_Superv_Prepara()         : Prepara as estruturas dos painéis e dashboards
// DB_Superv()                 : Dashboard modo Painel - Usuario
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class DashboardController : Controller
    {
        // Dashboard prepara
        private Dashboard_DashboardPageDominio DB_Superv_Prepara(int IDReferencia, int TipoReferencia)
        {
            // leio todas as paginas do usuario
            DashboardPageMetodos dashboardPageMetodos = new DashboardPageMetodos();
            List<DashboardPageDominio> listaDashboardsPage = new List<DashboardPageDominio>();
            listaDashboardsPage = dashboardPageMetodos.ListarPorIdReferencia(IDReferencia, TipoReferencia);

            // leio todos dashboards do usuario
            DashboardMetodos dashboardMetodos = new DashboardMetodos();
            List<DashboardDominio> listaDashboards = new List<DashboardDominio>();
            listaDashboards = dashboardMetodos.ListarPorIdReferencia(IDReferencia, TipoReferencia);

            // le cookie excluir cookies do dashboard
            bool excluir_cookie = CookieStore.LeCookie_Bool("DB_Excluir_DataHora");

            // completo dashboard com nome da pagina
            int contador;
            int contador2;

            // zero contador de dashboard das paginas
            for (contador2 = 0; contador2 < listaDashboardsPage.Count(); contador2++)
            {
                listaDashboardsPage[contador2].numDashboard = 0;
            }

            // percorre lista dashboard
            for (contador = 0; contador < listaDashboards.Count(); contador++)
            {
                // percorre lista das paginas
                for (contador2 = 0; contador2 < listaDashboardsPage.Count(); contador2++)
                {
                    // caso achou a pagina, copio titulo
                    if (listaDashboards[contador].IDDashboardPage == listaDashboardsPage[contador2].IDDashboardPage)
                    {
                        listaDashboards[contador].PageTitle = listaDashboardsPage[contador2].Title;
                        listaDashboardsPage[contador2].numDashboard++;
                        break;
                    }
                }

                // verifica se deve excluir cookie
                if (excluir_cookie)
                {
                    // apaga cookie datahora
                    string nome_cookie = string.Format("DB{0}", listaDashboards[contador].IDDashboard);
                    CookieStore.DeleteCookie(nome_cookie);
                }
            }

            // salva cookie excluir cookies do dashboard
            CookieStore.SalvaCookie_Bool("DB_Excluir_DataHora", false);

            // copia para modelo agrupado
            Dashboard_DashboardPageDominio db = new Dashboard_DashboardPageDominio();
            db.DashboardPage = listaDashboardsPage;
            db.Dashboard = listaDashboards;

            return db;
        }

        // GET: Dashboard modo Painel - Usuario
        public ActionResult DB_Superv(int Editavel, int IDUsuario, int IDDashboardGrupo = 0, int IDCliente = 0, int IDMedicao = 0)
        {
            // verifica se enviou IDCliente e IDMedicao
            if (IDCliente > 0 && IDMedicao > 0)
            {
                // le configuracao da medicao
                MedicoesMetodos medicoesMetodos_conf = new MedicoesMetodos();
                MedicoesDominio medicao_conf = medicoesMetodos_conf.ListarPorId(IDMedicao);
            
                // salva cookie 
                CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
                CookieStore.SalvaCookie_Int("IDTipoMedicao", medicao_conf.IDTipoMedicao);
                CookieStore.SalvaCookie_Int("_IDGateway", medicao_conf.IDGateway);
            }

            // le cookies
            LeCookies_SmartEnergy();

            // consultor
            int IDConsultor = ViewBag._IDConsultor;

            // verifica se CPFL
            if (IDConsultor == CLIENTES_ESPECIAIS.CPFL)
            {
                // tela de ajuda - dashboard clientes CPFL
                CookieStore.SalvaCookie_String("PaginaAjuda", "Dashboard_Painel_CPFL");
            }
            else
            {
                // tela de ajuda - dashboard clientes
                CookieStore.SalvaCookie_String("PaginaAjuda", "Dashboard_Painel");
            }

            // tela dashboard
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Dashboard");

            // le cookies
            LeCookies_SmartEnergy();

            // modo painel
            CookieStore.SalvaCookie_Bool("DB_Editar", false);

            // numero da pagina
            int PageNumber = CookieStore.LeCookie_Int("PageNumberDashboard");
            if (PageNumber < 0) PageNumber = 0;
            CookieStore.SalvaCookie_Int("PageNumberDashboard", PageNumber);
            ViewBag.PageNumber = PageNumber;

            // IDReferencia
            int IDReferencia = IDUsuario;
            int TipoReferencia = DB_REFERENCIA.IDUsuario;

            if (IDDashboardGrupo > 0)
            {
                IDReferencia = IDDashboardGrupo;
                TipoReferencia = DB_REFERENCIA.IDDashboardGrupo;
            }
            else
            {
                // le usuário
                UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
                UsuarioDominio usuario = usuarioMetodos.ListarPorId(IDUsuario);

                // verifica se usuário quer dashboard dele ou do modelo
                if (usuario != null)
                {
                    if (usuario.IDDashboardGrupo > 0)
                    {
                        IDReferencia = usuario.IDDashboardGrupo;
                        TipoReferencia = DB_REFERENCIA.IDDashboardGrupo;
                    }

                    // atualiza IDDashboardGrupo
                    IDDashboardGrupo = usuario.IDDashboardGrupo;
                }
            }

            // atualiza cookie
            CookieStore.SalvaCookie_Int("IDDashboardGrupo", IDDashboardGrupo);
            ViewBag._IDDashboardGrupo = IDDashboardGrupo;

            // nome do grupo de paineis
            string NomeDashboardGrupo = "";

            if (IDDashboardGrupo > 0)
            {
                // le grupo
                DashboardGrupoMetodos grupoMetodos = new DashboardGrupoMetodos();
                DashboardGrupoDominio grupo = grupoMetodos.ListarPorId(IDDashboardGrupo);

                if (grupo != null)
                {
                    NomeDashboardGrupo = grupo.Nome;
                }
            }

            ViewBag.NomeDashboardGrupo = NomeDashboardGrupo;

            // indica se dashboard é editável
            ViewBag.DashboardEditavel = Editavel;

            return View(DB_Superv_Prepara(IDReferencia, TipoReferencia));
        }
    }
}