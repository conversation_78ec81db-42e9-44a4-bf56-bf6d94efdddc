﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Web.Hosting;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RankingController
    {
        // Ranking Mensal
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_ConsProjetadoEnergia", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_ConsProjetadoEnergia(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char qual_periodo, ref DATAHORA pdatahora, ref CONS_PROJETADO pcons_projetado);

        //
        // RANKING MENSAL
        //

        // GET: Ranking Mensal
        public ActionResult Ranking_Mensal(int IDCliente)
        {
            // tela de ajuda - ranking
            CookieStore.SalvaCookie_String("PaginaAjuda", "RankingMensal");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Ranking_Mensal");
            CookieStore.SalvaCookie_Int("_IDRanking", 0);

            // le cookies
            LeCookies_SmartEnergy();

            // le rankings
            RankingMetodos rankingMetodos = new RankingMetodos();
            List<RankingDominio> listaRanking = rankingMetodos.ListarPorIDCliente(IDCliente);
            ViewBag.listaRanking = listaRanking;

            // data atual
            DateTime datahora_ultima = DateTime.Now;
            DATAHORA data_hora = new DATAHORA();

            // le cookie datahora
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            if( datahora_cookie.Year != 2000 )
            {
                datahora_ultima = datahora_cookie;
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_ultima);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", datahora_ultima);

            // IDRanking
            int IDRanking = ViewBag._IDRanking;

            if( listaRanking != null )
            {
                if (IDRanking == 0 && listaRanking.Count > 0 )
                {
                    ViewBag._IDRanking = listaRanking[0].IDRanking;
                }
            }

            // valores
            RANKING_MENSAL_TOTAL ranking_total = new RANKING_MENSAL_TOTAL();
            List<RANKING_MENSAL> ranking_resultado = new List<RANKING_MENSAL>();

            ViewBag.ranking_total = ranking_total;
            ViewBag.ranking_resultado = ranking_resultado;

            // lista de erros
            var listaErros = new List<string>();

            // erros
            ViewBag.listaErros = listaErros;

            // ranking
            ViewBag.NomeRanking = "";
            ViewBag.Unidade = "";

            return View();
        }

        // GET: Ranking Atualizar
        public PartialViewResult _Ranking_Atualizar(int IDRanking, int Navegacao, string Data)
        {
            // calcula
            Ranking_Mensal_Show(IDRanking, Navegacao, Data);

            return PartialView();
        }

        // GET: Ranking Mensal Print
        public ActionResult Ranking_Mensal_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDRanking
            int IDCliente = ViewBag._IDCliente;
            int IDRanking = ViewBag._IDRanking;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}",DateTime.Now);

            // calcula
            Ranking_Mensal_Show(IDRanking, 0, data_str);

            // imprime
            return View();
        }

        public static async Task<string> EMailTemplate(string template)
        {
            var templateFilePath = HostingEnvironment.MapPath("~/Content/emailTemplates/") + template + ".cshtml";
            StreamReader objstreamreaderfile = new StreamReader(templateFilePath);
            var body = await objstreamreaderfile.ReadToEndAsync();
            objstreamreaderfile.Close();
            return body;
        }

        // GET: Ranking Mensal EMAIL
        public async Task<ActionResult> Ranking_Mensal_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDRanking
            int IDCliente = ViewBag._IDCliente;
            int IDRanking = ViewBag._IDRanking;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Ranking_Mensal_Show(IDRanking, 0, data_str);

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Ranking_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDRanking, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_Ranking_Mensal_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "RankingEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.NomeRanking", ViewBag.NomeRanking);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Ranking Mensal PDF
        public ActionResult Ranking_Mensal_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDRanking
            int IDCliente = ViewBag._IDCliente;
            int IDRanking = ViewBag._IDRanking;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Ranking_Mensal_Show(IDRanking, 0, data_str);

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Ranking_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDRanking, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_Ranking_Mensal_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Ranking Mensal XLS
        public ActionResult Ranking_Mensal_XLS()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDRanking
            int IDCliente = ViewBag._IDCliente;
            int IDRanking = ViewBag._IDRanking;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Ranking_Mensal_Show(IDRanking, 0, data_str);

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_Ranking_Mensal(IDCliente, IDRanking);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Ranking_{0:000000}_{1:yyyyMMddHHmm}.xls", IDRanking, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Ranking Mensal XLS Download
        [HttpGet]
        public virtual ActionResult Ranking_Mensal_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Ranking Show
        public void Ranking_Mensal_Show(int IDRanking, int Navegacao, string Data)
        {
            // caso forcar data
            if (Navegacao == 100)
            {
                // pega data
                DateTime dateValue = DateTime.Parse(Data);

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Relat_Data", dateValue);
            }

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;

            // le rankings
            RankingMetodos rankingMetodos = new RankingMetodos();
            List<RankingDominio> listaRanking = rankingMetodos.ListarPorIDCliente(IDCliente);
            ViewBag.listaRanking = listaRanking;

            // verifica se IDRanking valido
            ViewBag._IDRanking = IDRanking;

            if (listaRanking != null)
            {
                if (IDRanking == 0 && listaRanking.Count > 0)
                {
                    ViewBag._IDRanking = listaRanking[0].IDRanking;
                }
            }

            // le cookie datahora
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");
            DATAHORA data_hora = new DATAHORA();

            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -3:    // mes anterior

                    datahora_cookie = datahora_cookie.AddMonths(-1);
                    break;

                case 3:    // mes seguinte

                    datahora_cookie = datahora_cookie.AddMonths(1);
                    break;

                case -4:    // ano anterior

                    datahora_cookie = datahora_cookie.AddYears(-1);
                    break;

                case 4:    // ano seguinte

                    datahora_cookie = datahora_cookie.AddYears(1);
                    break;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_cookie);

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_cookie);

            // calcula
            Calc_Ranking_Mensal(IDRanking, data_hora);

            return;
        }

        // Calcula Ranking Mensal
        private void Calc_Ranking_Mensal(int IDRanking, DATAHORA datahora)
        {
            // retorno
            int retorno;

            // le cookies
            LeCookies_SmartEnergy();

            // valores
            RANKING_MENSAL_TOTAL ranking_total = new RANKING_MENSAL_TOTAL();
            List<RANKING_MENSAL> ranking_resultado = new List<RANKING_MENSAL>();

            // lista de erros
            var listaErros = new List<string>();

            // converte data e hora
            DateTime DataAtual = Funcoes_Converte.ConverteDataHora2DateTime(datahora);

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:MM/yyyy}", DataAtual);

            // verifica se IDRanking valido
            ViewBag._IDRanking = IDRanking;

            if( IDRanking <= 0 )
            {
                if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
                {
                    listaErros.Add("Não existem Rankings configurados");
                }

                // ranking
                ViewBag.NomeRanking = "";
                ViewBag.Unidade = "";

                // valores
                ViewBag.ranking_resultado = ranking_resultado;
                ViewBag.ranking_total = ranking_total;

                // erros
                ViewBag.listaErros = listaErros;

                return;
            }

            // le ranking
            RankingMetodos rankingMetodos = new RankingMetodos();
            RankingDominio ranking = new RankingDominio();
            ranking = rankingMetodos.ListarPorId(IDRanking);

            // ranking
            ViewBag.NomeRanking = ranking.Nome;
            ViewBag.Unidade = ranking.Unidade;

            // le medicoes do ranking
            RankingGruposMedMetodos rankingGrupoMetodos = new RankingGruposMedMetodos();
            List<RankingGruposMedDominio> rankingGrupos = rankingGrupoMetodos.ListarPorIDRanking(IDRanking);

            // estruturas
            CONS_PROJETADO cons_projetado = new CONS_PROJETADO();

            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            config_interface.sweb.id_cliente = ranking.IDCliente;
            config_interface.sweb.id_gateway = 0;

            // percorre medicoes
            if (rankingGrupos != null)
            {
                foreach (RankingGruposMedDominio rankingGrupo in rankingGrupos)
                {
                    RANKING_MENSAL rank = new RANKING_MENSAL();

                    // medicao
                    rank.IDMedicao = rankingGrupo.IDMedicao;
                    rank.Nome_Medicao = "";

                    config_interface.sweb.id_medicao = rankingGrupo.IDMedicao;

                    // valor da unidade
                    rank.Valor = rankingGrupo.Valor;

                    // calcula ranking da medicao
                    MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                    MedicoesDominio medicao = medicaoMetodos.ListarPorId(rankingGrupo.IDMedicao);

                    if( medicao != null )
                    {
                        // nome medicao
                        rank.Nome_Medicao = medicao.Nome;

                        // numero de medicoes
                        ranking_total.NumMedicoes += 1;

                        // calcula ranking da medicao
                        retorno = SmCalcDB_ConsProjetadoEnergia((char)0, ref config_interface, (char)1, ref datahora, ref cons_projetado);

                        if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
                        {
                            if (retorno != 0)
                                listaErros.Add(string.Format("Medição {0} com Retorno {1}", medicao.Nome, retorno));
                        }

                        // MetaKWhPj / unidade do ranking
                        cons_projetado.K_Rank = (rank.Valor == 0) ? 0.0 : ((cons_projetado.MetaKWhPj / rank.Valor));

                        // consumo
                        ranking_total.MetaKWhPj += (double)cons_projetado.MetaKWhPj;
                        ranking_total.MetaKWh += (double)cons_projetado.MetaKWh;

                        // custo
                        ranking_total.MetaFatPj += cons_projetado.MetaFatPj;
                        ranking_total.MetaFat += cons_projetado.MetaFat;

                        // soma valor da unidade
                        ranking_total.Valor += rank.Valor;

                        // copia
                        rank.cons_projetado = cons_projetado;

                        // coloca na lista
                        ranking_resultado.Add(rank);
                    }
                }
            }

            // calcula variacao dos totais
            ranking_total.MetaKWhPjPC = (ranking_total.MetaKWh == 0) ? 0.0 : ((ranking_total.MetaKWhPj / ranking_total.MetaKWh) * 100.0);
            ranking_total.MetaFatPjPC = (ranking_total.MetaFat == 0) ? 0.0 : ((ranking_total.MetaFatPj / ranking_total.MetaFat) * 100.0);

            // MetaKWhPj / unidade
            ranking_total.K_Rank = (ranking_total.Valor == 0) ? 0.0 : ((ranking_total.MetaKWhPj / ranking_total.Valor));

            // valores
            ViewBag.ranking_resultado = ranking_resultado;
            ViewBag.ranking_total = ranking_total;

            // erros
            ViewBag.listaErros = listaErros;

            return;
        }

        // Ranking Mensal XLS
        private HSSFWorkbook XLS_Ranking_Mensal(int IDCliente, int IDRanking)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TOTAL e MEDICOES
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Total e Medições");

            // cabecalho
            string[] cabecalho = { "Medições", "Projeção Consumo (kWh)", "Meta (kWh)", "Variação Consumo (%)", "Projeção Custo (R$)", "Meta (R$)", "Variação Custo (%)", string.Format("Consumo Médio (kWh/{0})", ViewBag.Unidade), ViewBag.Unidade };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            List<RANKING_MENSAL> ranking_resultado = ViewBag.ranking_resultado;
            RANKING_MENSAL_TOTAL ranking_total = ViewBag.ranking_total;
            int i;
            IRow row;

            // adiciona linha
            row = sheet.CreateRow(rowIndex++);

            // medicao
            numeroCelulaXLS(row, 0, ranking_total.NumMedicoes, _intCellStyle);

            // consumo
            numeroCelulaXLS(row, 1, ranking_total.MetaKWhPj, _intCellStyle);
            numeroCelulaXLS(row, 2, ranking_total.MetaKWh, _intCellStyle);
            numeroCelulaXLS(row, 3, ranking_total.MetaKWhPjPC, _1CellStyle);

            // custo
            numeroCelulaXLS(row, 4, ranking_total.MetaFatPj, _2CellStyle);
            numeroCelulaXLS(row, 5, ranking_total.MetaFat, _2CellStyle);
            numeroCelulaXLS(row, 6, ranking_total.MetaFatPjPC, _1CellStyle);

            // consumo medico
            numeroCelulaXLS(row, 7, ranking_total.K_Rank, _1CellStyle);

            // unidade
            numeroCelulaXLS(row, 8, ranking_total.Valor, _1CellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // adiciona cabecalho
            cabecalhoTabelaXLS(workbook, sheet, cabecalho, rowIndex++);

            // percorre valores
            if (ranking_resultado != null)
            {
                foreach (RANKING_MENSAL rank in ranking_resultado)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // medicao
                    textoCelulaXLS(row, 0, rank.Nome_Medicao);

                    // consumo
                    numeroCelulaXLS(row, 1, rank.cons_projetado.MetaKWhPj, _intCellStyle);
                    numeroCelulaXLS(row, 2, rank.cons_projetado.MetaKWh, _intCellStyle);
                    numeroCelulaXLS(row, 3, rank.cons_projetado.MetaKWhPjPC, _1CellStyle);

                    // custo
                    numeroCelulaXLS(row, 4, rank.cons_projetado.MetaFatPj, _2CellStyle);
                    numeroCelulaXLS(row, 5, rank.cons_projetado.MetaFat, _2CellStyle);
                    numeroCelulaXLS(row, 6, rank.cons_projetado.MetaFatPjPC, _1CellStyle);

                    // consumo medico
                    numeroCelulaXLS(row, 7, rank.cons_projetado.K_Rank, _1CellStyle);

                    // unidade
                    numeroCelulaXLS(row, 8, rank.Valor, _1CellStyle);

                    // proxima
                    rowIndex++;
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);
            
            // retorna planilha
            return workbook;
        }
    }
}