﻿using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class RankingController
    {
        // GET: Ranking - Configuracao
        public ActionResult Ranking_Configuracao(int IDCliente)
        {
            // tela de ajuda - ranking
            CookieStore.SalvaCookie_String("PaginaAjuda", "Ranking_Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le rankings
            RankingMetodos rankingMetodos = new RankingMetodos();
            List<RankingDominio> listaRanking = rankingMetodos.ListarPorIDCliente(IDCliente);

            return View(listaRanking);
        }

        // GET: Ranking - Editar
        public ActionResult Ranking_Editar(int IDRanking)
        {
            int IDCliente = 0;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Ranking");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // verifica se adicionando
            RankingDominio ranking = new RankingDominio();
            if (IDRanking == 0)
            {
                // zera usuario com default
                ranking.IDCliente = ViewBag._IDCliente;
                ranking.Nome = "";
                ranking.Unidade = "";

                // IDCliente
                IDCliente = ViewBag._IDCliente;
            }
            else
            {
                // le ranking
                RankingMetodos rankingMetodos = new RankingMetodos();
                ranking = rankingMetodos.ListarPorId(IDRanking);

                // IDCliente
                IDCliente = ranking.IDCliente;
            }

            // lista de medicoes habilitadas do usuario
            List<int> ConfigMedicaoList_Usuario = new List<int>();
            ConfigMedicaoList_Usuario = ViewBag._ConfigMed;

            // verifica se existe lista de medicoes
            if (ConfigMedicaoList_Usuario != null)
            {
                if (ConfigMedicaoList_Usuario.Count() == 0)
                {
                    ConfigMedicaoList_Usuario = null;
                }
            }

            // le medicoes do ranking
            RankingGruposMedMetodos rankingGrupoMetodos = new RankingGruposMedMetodos();
            List<RankingGruposMedDominio> rankingGrupos = rankingGrupoMetodos.ListarPorIDRanking(ranking.IDRanking);

            // cria lista de medicoes deste ranking
            List<int> ConfigMedicaoList_Ranking = new List<int>();

            foreach (RankingGruposMedDominio rankingGrupo in rankingGrupos)
            {
                ConfigMedicaoList_Ranking.Add(rankingGrupo.IDMedicao);
            }

            // le medicoes configuradas do ranking para lista
            CliGateGrupoUnidMedicoesMetodos medicoesMetodos = new CliGateGrupoUnidMedicoesMetodos();
            List<CliGateGrupoUnidMedicoesDominio> medicoes = new List<CliGateGrupoUnidMedicoesDominio>();

            List<CliGateGrupoUnidMedicoesDominio> medicoes_utilizadas = new List<CliGateGrupoUnidMedicoesDominio>();
            List<CliGateGrupoUnidMedicoesDominio> medicoes_nao_utilizadas = new List<CliGateGrupoUnidMedicoesDominio>();

            if (ConfigMedicaoList_Ranking != null)
            {
                // le medicoes de energia habilitadas para o usuario no cliente
                medicoes = medicoesMetodos.ListarPorIDCliente(IDCliente, ConfigMedicaoList_Usuario, 0);

                // seleciona medicoes 
                int contador;

                // percorre lista
                if( medicoes != null )
                {
                    for (contador = 0; contador < medicoes.Count(); contador++)
                    {
                        // verifica se existe lista de medicoes do ranking
                        if (ConfigMedicaoList_Ranking != null)
                        {
                            // verifica se medicao esta habilitada para o ranking
                            if (ConfigMedicaoList_Ranking.Contains(medicoes[contador].IDMedicao))
                            {
                                // copia medicao na lista de utilizadas
                                medicoes_utilizadas.Add(medicoes[contador]);
                            }
                            else
                            {
                                // copia medicao na lista de nao utilizadas
                                medicoes_nao_utilizadas.Add(medicoes[contador]);
                            }
                        }
                        else
                        {
                            // copia medicao na lista de nao utilizadas
                            medicoes_nao_utilizadas.Add(medicoes[contador]);
                        }
                    }
                }
            }
            ViewBag.Medicoes = medicoes_utilizadas;
            ViewBag.Medicoes2 = medicoes_nao_utilizadas;
            ViewBag.rankingGrupos = rankingGrupos;


            return View(ranking);
        }

        // POST: Ranking - Salvar
        [HttpPost]
        public ActionResult Ranking_Salvar(RankingDominio ranking, List<RankingGruposMedDominio> rankingGrupos)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro ranking com o mesmo nome
            RankingMetodos rankingMetodos = new RankingMetodos();
            if (rankingMetodos.VerificarDuplicidade(ranking))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Ranking existente."
                };
            }
            else
            {
                // salva ranking
                rankingMetodos.Salvar(ranking);

                // evento 
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();

                if (ranking.IDRanking > 0)
                {
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.RANKING, ranking.IDRanking);
                }
                else
                {
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.RANKING, ranking.IDRanking);
                }

                // pego IDRanking novamente (pois pode ter sido insercao)
                RankingDominio rank = rankingMetodos.ListarPorNomeUnidade(ranking.IDCliente, ranking.Nome, ranking.Unidade);

                // salva rankingGrupo
                if (rank != null)
                {
                    RankingGruposMedMetodos rankingGruposMetodos = new RankingGruposMedMetodos();

                    // exclui todos as medicoes deste ranking
                    rankingGruposMetodos.Excluir(rank.IDRanking);

                    // salva rankingGrupo
                    if (rankingGrupos != null)
                    {
                        // percorre lista e salva
                        foreach (RankingGruposMedDominio rankingGrupo in rankingGrupos)
                        {
                            // preenche campos que faltam
                            rankingGrupo.IDRanking = rank.IDRanking;

                            // salva
                            rankingGruposMetodos.Salvar(rankingGrupo);
                        }
                    }
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Ranking - Excluir
        public ActionResult Ranking_Excluir(int IDRanking)
        {
            // apaga o ranking
            RankingMetodos rankingMetodos = new RankingMetodos();
            rankingMetodos.Excluir(IDRanking);

            RankingGruposMedMetodos rankingGruposMetodos = new RankingGruposMedMetodos();
            rankingGruposMetodos.Excluir(IDRanking);

            // evento 
            UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.EXCLUI_CONFIG, TABELA_CONFIG.RANKING, IDRanking);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}