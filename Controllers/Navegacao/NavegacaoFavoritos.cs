﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class NavegacaoController : Controller
    {
        // GET: Favoritos
        public PartialViewResult _Favoritos()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDs
            int IDUsuario = ViewBag._IDUsuario;

            // le favoritos
            FavoritosMetodos favoritosMetodos = new FavoritosMetodos();
            List<FavoritosDominio> favoritos = favoritosMetodos.ListarPorIDUsuario(IDUsuario);

            ViewBag.Favoritos = favoritos;

            return PartialView();
        }

        // GET: Validar favorito
        public JsonResult Favorito_Validar(string url)
        {
            // retorno
            var returnedData = new
            {
                status = "ERRO",
                descricao = "Erro ao validar o favorito"
            };

            if (url != null)
            {
                // le cookies
                LeCookies_SmartEnergy();

                // IDs
                int IDUsuario = ViewBag._IDUsuario;

                FavoritosDominio favorito = new FavoritosDominio();
                favorito.IDUsuario = IDUsuario;
                favorito.URL = url;

                // Verificar duplicidade 
                FavoritosMetodos favoritoMetodos = new FavoritosMetodos();
                if (favoritoMetodos.VerificarDuplicidade(favorito))
                {
                    returnedData = new
                    {
                        status = "ERRO",
                        descricao = "Favorito existente"
                    };

                    // retorna
                    return Json(returnedData, JsonRequestBehavior.AllowGet);
                }

                // separa
                string[] partes = url.Split('/');

                if (partes != null)
                {
                    if (partes.Length > 2)
                    {
                        // nome do favorito
                        string nome_favorito = Favorito_BuscarPagina(Pagina1, partes[3]);

                        if (partes.Length > 3)
                        {
                            // separa
                            string[] subpartes = partes[4].Split('?');

                            if (subpartes != null)
                            {
                                if (subpartes.Length > 0)
                                {
                                    // adiciona ao nome do favorito
                                    nome_favorito += " " + Favorito_BuscarPagina(Pagina2, subpartes[0]);
                                }
                            }
                        }

                        // limita palavras
                        nome_favorito = Favorito_LimitarPalavras(nome_favorito);

                        // retorno
                        returnedData = new
                        {
                            status = "OK",
                            descricao = nome_favorito
                        };
                    }
                }
            }

            // retorna
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        private string[,] Pagina1 = new string[,]
        {
            {"Supervisao", "Supervisão"},
            {"Relatorios", "Relatório"},
        };

        private string[,] Pagina2 = new string[,]
        {
            {"Medicoes", "Medições"},
            {"Medicoes_Mapa", "Mapa"},
            {"Medicoes_OperacaoAssistida", "Operação Assistida"},
            {"Medicao_Energia", "Energia"},
            {"Medicao_Utilidades", "Utilidades"},
            {"Medicao_EA", "Analógica"},
            {"Medicao_Ciclometro", "Ciclômetro"},
            {"Medicao_Meteorologia", "Meteorologia"},

            {"EntradasDigitais", "Entradas Digitais"},
            {"SaidasDigitais", "Saídas Digitais"},

            {"Relat_Ciclometro", "Ciclômetro"},
            {"Relat_Consolidado_DemCons", "Consolidado DemCons"},
            {"Relat_Consolidado_EA", "Consolidado Analógica"},
            {"Relat_Consolidado_Utilidades", "Consolidado Utilidades"},
            {"Relat_Consumo", "Consumo"},
            {"Relat_ControleSazonalidade", "Controle Sazonalidade"},
            {"Relat_Dem_Ativa", "Demanda Ativa"},
            {"Relat_Dem_Reativa", "Demanda Reativa"},
            {"Relat_EA", "Analógica"},
            {"Relat_Eventos", "Eventos"},
            {"Relat_FatCarga", "Fator Carga"},
            {"Relat_FatPot", "Fator Potência"},
            {"Relat_FatUtilizacao", "Fator Utilização"},
            {"Relat_Meteorologia", "Meteorologia"},
            {"Relat_UFER", "UFER"},
            {"Relat_Utilidades", "Utilidades"}
        };

        // buscar item no array bidimensional
        private string Favorito_BuscarPagina(string[,] arrayBidimensional, string chave)
        {
            // Percorrer o array bidimensional
            for (int i = 0; i < arrayBidimensional.GetLength(0); i++)
            {
                // chave
                string correspondente = chave;

                // verifica se a chave desejada é maior que a chave do campo 0
                if (chave.Length > arrayBidimensional[i, 0].Length)
                {
                    // copia só a parte
                    correspondente = chave.Substring(0, arrayBidimensional[i, 0].Length);
                }

                // Verificar se a chave corresponde à string do campo 0
                if (correspondente == arrayBidimensional[i, 0])
                {
                    // retorna correspondente
                    return (arrayBidimensional[i, 1]);
                }
            }

            // Caso a chave não seja encontrada
            return (chave);
        }

        // POST: Favoritos - Salvar
        [HttpPost]
        public ActionResult Favorito_Salvar(List<FavoritosDominio> favoritos)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // le cookies
            LeCookies_SmartEnergy();

            // IDs
            int IDUsuario = ViewBag._IDUsuario;

            // apaga todos os favoritos do usuário
            FavoritosMetodos favoritoMetodos = new FavoritosMetodos();
            favoritoMetodos.ExcluirUsuario(IDUsuario);

            if (favoritos != null)
            {
                // percorre favoritos
                foreach (FavoritosDominio favorito in favoritos)
                {
                    // coloca usuario
                    favorito.IDUsuario = IDUsuario;

                    // limita palavras da descrição
                    favorito.Descricao = Favorito_LimitarPalavras(favorito.Descricao);

                    // retira caracteres indesejados da URL
                    favorito.URL = favorito.URL.Replace("amp;", "");
                    favorito.URL = favorito.URL.Replace("#", "");

                    // salva
                    favoritoMetodos.Salvar(favorito);
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // função que limita o tamanho das palavras de um texto
        private string Favorito_LimitarPalavras(string texto)
        {
            // protege
            if (texto == null)
            {
                return ("");
            }

            // separa as palavras
            string[] palavras = texto.Split(' ');

            // percorre as palavras
            for (int i = 0; i < palavras.Length; i++)
            {
                // verifica se tamanho maior que o limite
                if (palavras[i].Length > 23)
                {
                    // limita
                    palavras[i] = palavras[i].Substring(0, 23);
                }
            }

            // nova string
            string nova_string = string.Join(" ", palavras);

            // limita em 50 caractes
            if (nova_string.Length > 50)
            {
                // limita
                nova_string = nova_string.Substring(0, 50);
            }

            return (nova_string);
        }
    }
}