﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class NavegacaoController : Controller
    {
        // GET: Tabela Medicoes
        public PartialViewResult _TabelaMedicoes()
        {
            // em caso de alterar tabela medicoes, apago tipo fatura
            CookieStore.DeleteCookie("Fatura_TipoContrato");
            CookieStore.DeleteCookie("Fatura_TipoFatura");

            // le cookies
            LeCookies_SmartEnergy();

            // IDs
            int IDTipoAcesso = ViewBag._IDTipoAcesso;
            int IDCliente = ViewBag._IDCliente;
            List<int> ConfigMedList = ViewBag._ConfigMed;

            // le medicoes
            ListaMedicoesMetodos medicoesMetodos = new ListaMedicoesMetodos();
            List<SupervMedicoesDominio> medicoes = medicoesMetodos.ListarPorIDCliente(IDCliente, IDTipoAcesso, ConfigMedList);

            ViewBag.TabelaMedicoes = medicoes;

            return PartialView();
        }
    }
}