﻿using System.Collections.Generic;
using System.Web.Mvc;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class NavegacaoController : Controller
    {
        // GET: Mudar senha
        public ActionResult MudarSenha(int IDUsuario)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // usuario
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = usuarioMetodos.ListarPorId(IDUsuario);

            // motivo para mudar senha
            int mudar_senha = 0;

            // verifica se expiração de senha
            if (Funcoes_Senha.Expiracao(usuario.Senha))
            {
                mudar_senha = 1;
            }

            // verifica se usuário novo
            if (Funcoes_Senha.NovoUsuario(usuario.Senha))
            {
                mudar_senha = 2;
            }

            ViewBag.mudar_senha = mudar_senha;

            return View(usuario);
        }

        // POST: <PERSON>var senha
        [HttpPost]
        public ActionResult SalvarSenha(UsuarioDominio usuario)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // ler a senha atual
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario_atual = usuarioMetodos.ListarPorId(usuario.IDUsuario);

            // verificar se a senha alterou
            if (usuario.Senha == usuario_atual.Senha)
            {
                // erro, senha não foi alterada
                returnedData = new
                {
                    status = "ERRO",
                    erro = "É necessário alterar a senha."
                };
            }
            else
            {
                // senhas usadas
                SenhasUsadasMetodos senhasUsadasMetodos = new SenhasUsadasMetodos();

                // verifica se senha já foi usada
                if (senhasUsadasMetodos.VerificaSenhaUsada(usuario.IDUsuario, usuario.Senha))
                {
                    returnedData = new
                    {
                        status = "ERRO",
                        erro = "Você está repetindo alguma das últimas 10 senhas utilizadas.<br><br>Por favor cadastrar outra senha."
                    };

                    // retorna status
                    return Json(returnedData);
                }

                // salva senha usada
                senhasUsadasMetodos.Inserir(usuario.IDUsuario, usuario.Senha);

                // salva senha
                usuarioMetodos.AlterarSenha(usuario);

                // evento 
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.USUARIO, usuario.IDUsuario);
            }

            // retorna status
            return Json(returnedData);
        }
    }
}