﻿using System;
using System.IO;
using System.Threading.Tasks;
using System.Web.Hosting;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class NavegacaoController : Controller
    {
        // GET: Solicita código segundo fator
        public async Task<ActionResult> ShowSegundoFator()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDUsuario
            int IDUsuario = ViewBag._IDUsuario;

            // usuário
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = usuarioMetodos.ListarPorId(IDUsuario);

            if (usuario == null)
            {
                // pagina inicial 
                return Redirect("/");
            }

            // gera código
            string codigo = GenerateRandomString_Numbers(6);

            // altera segundo fator no usuário
            usuarioMetodos.Atualiza_CodigoSegundoFator(IDUsuario, codigo);

            // envia email
            await EnviaEmailSegundoFator(usuario.NomeUsuario, usuario.Email, codigo);

            return View();
        }

        // GET: Verifica código do segundo fator
        public ActionResult VerificaSegundoFator(string codigo)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = "",
                caminho = ""
            };

            // le cookies
            LeCookies_SmartEnergy();

            // IDUsuario
            int IDUsuario = ViewBag._IDUsuario;

            // usuário
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = usuarioMetodos.ListarPorId(IDUsuario);

            if (usuario == null)
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Usuário inexistente.",
                    caminho = ""
                };

                // retorna erro
                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // verifica se código expirou
            if (usuario.ExpirarSegundoFator < DateTime.Now)
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Código expirou.",
                    caminho = ""
                };

                // retorna erro
                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // verifica código
            if (codigo != usuario.CodigoSegundoFator)
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Código inválido.",
                    caminho = ""
                };

                // retorna erro
                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // caminho
            string caminho = ViewBag._Caminho;

            if (caminho == null)
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Caminho inválido.",
                    caminho = ""
                };

                // retorna erro
                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // ok
            returnedData = new
            {
                status = "OK",
                erro = "",
                caminho = caminho
            };

            // após aceitar o código, indico que esta logado
            System.Web.Security.FormsAuthentication.SetAuthCookie(usuario.Apelido, false);

            // retorna erro
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // Envia email Segundo Fator
        public async Task<int> EnviaEmailSegundoFator(string Nome, string Email, string Codigo)
        {
            // emails
            string[] emails = { Email };

            // assunto
            string assunto = "[Smart Energy] Código de Acesso";

            // envia EMAIL
            var emailTemplate = "SegundoFatorEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.Nome", Nome);
            message = message.Replace("ViewBag.Codigo", Codigo);

            await EmailServices.SendBulkEmailAsync(emails, assunto, message, null);

            // retorna status
            return (0);
        }

        // template do email
        public static async Task<string> EMailTemplate(string template)
        {
            // caminho do template
            var templateFilePath = HostingEnvironment.MapPath("~/Content/emailTemplates/") + template + ".cshtml";

            // formata corpo do email
            StreamReader objstreamreaderfile = new StreamReader(templateFilePath);
            var body = await objstreamreaderfile.ReadToEndAsync();
            objstreamreaderfile.Close();

            // retorno corpo do email
            return body;
        }

        // Gera string com números randomico
        public static string GenerateRandomString_Numbers(int length)
        {
            // gera string com números aleatórios
            Random random = new Random();
            string numeros = "";

            for (int i = 0; i < length; i++)
            {
                numeros += random.Next(0, 10).ToString();
            }

            return numeros;
        }
    }
}