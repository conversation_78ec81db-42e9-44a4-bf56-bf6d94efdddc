﻿using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class NavegacaoController : Controller
    {
        // GET: Apresenta LGPD
        public ActionResult ShowLGPD()
        {
            // le cookies
            LeCookies_SmartEnergy();

            return View();
        }

        // GET: Aceite da política de privacidade
        public ActionResult AceitaLGPD(int IDUsuario)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // usuario
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            usuarioMetodos.AceiteLGPD(IDUsuario);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}