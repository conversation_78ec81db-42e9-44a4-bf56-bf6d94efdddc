﻿using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class NavegacaoController : Controller
    {
        // GET: Apresenta introducao
        public ActionResult ShowIntro()
        {
            // le cookies
            LeCookies_SmartEnergy();

            return View();
        }

        // GET: Não apresenta mais a introducao
        public ActionResult StopIntro(int IDUsuario)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // usuario
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            usuarioMetodos.StopIntro(IDUsuario);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}