﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Security.Cryptography;
using System.Threading;
using System.Web;
using System.Web.Hosting;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class UsuarioPerfilController : Controller
    {
        // Listas utilizadas
        private void PreparaListas_Usuario()
        {
            // le tipos acesso
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposAcesso = listatiposMetodos.ListarTodos("TipoAcesso");
            ViewBag.listaTipoAcesso = listatiposAcesso;

            // le tipos medicao
            List<ListaTiposDominio> listatiposMsgEmail = listatiposMetodos.ListarTodos("TipoMsgEmail");
            ViewBag.listatiposMsgEmail = listatiposMsgEmail;

            // tipos expirar
            List<ListaTiposDominio> listaExpirar = new List<ListaTiposDominio>();

            for (int i = 1; i <= 12; i++)
            {
                ListaTiposDominio tipo = new ListaTiposDominio();

                // ID
                tipo.ID = i;

                // descrição
                if (i == 0)
                {
                    tipo.Descricao = "Senha não expira";
                }
                else if (i == 1)
                {
                    tipo.Descricao = "1 mês";
                }
                else if (i == 2)
                {
                    tipo.Descricao = "45 dias";
                }
                else
                {
                    tipo.Descricao = string.Format("{0} meses", i);
                }

                // coloca na lista
                listaExpirar.Add(tipo);
            }

            ViewBag.listaExpirar = listaExpirar;

            // tipos idioma
            List<IdiomaDominio> listaTiposIdioma = listatiposMetodos.ListarTodos_Idioma();
            ViewBag.listaTiposIdioma = listaTiposIdioma;

            // le tipos segundo fator
            List<ListaTiposDominio> listaTiposSegundoFator = listatiposMetodos.ListarTodos("TipoSegundoFator", false);
            ViewBag.listaTiposSegundoFator = listaTiposSegundoFator;

            return;
        }

        // GET: UsuarioPerfil
        [Authorize]
        public ActionResult Edit()
        {
            // tela de ajuda - meu perfil
            CookieStore.SalvaCookie_String("PaginaAjuda", "MeuPerfil");
            ViewBag.PaginaAjuda = "MeuPerfil";

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Supervisao");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "UsuarioPerfil");

            // pagina anterior
            ViewBag.returnUrl = Request.UrlReferrer;

            // prepara listas
            PreparaListas_Usuario();

            // le cookies
            LeCookies_SmartEnergy();

            // IDUsuario
            int IDUsuario = ViewBag._IDUsuario;

            // le usuario 
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = new UsuarioDominio();
            usuario = usuarioMetodos.ListarPorId(IDUsuario);

            // le usuario medicoes
            UsuarioMedicaoMetodos usuarioMedicaoMetodos = new UsuarioMedicaoMetodos();
            List<UsuarioMedicaoDominio> usuarioMedicoes = usuarioMedicaoMetodos.ListarTodasMedicoesIDUsuario(IDUsuario);
            ViewBag.usuarioMedicoes = usuarioMedicoes;

            // lista de medicoes marcadas para receber email
            List<int> ConfigMedicaoList = new List<int>();

            foreach(UsuarioMedicaoDominio usuarioMedicao in usuarioMedicoes)
            {
                ConfigMedicaoList.Add(usuarioMedicao.IDMedicao);
            }

            // le medicoes do usuario para lista
            CliGateGrupoUnidMedicoesMetodos medicoesMetodos = new CliGateGrupoUnidMedicoesMetodos();
            List<CliGateGrupoUnidMedicoesDominio> medicoes = new List<CliGateGrupoUnidMedicoesDominio>();

            if (ConfigMedicaoList != null)
                medicoes = medicoesMetodos.ListarPorIDCliente(-1, ConfigMedicaoList);
            ViewBag.Medicoes = medicoes;

            // le todas medicoes do cliente para lista
            List<CliGateGrupoUnidMedicoesDominio> medicoes2 = new List<CliGateGrupoUnidMedicoesDominio>();

            // verifica se senha eh ainda a alterada pela expiracao
            int mudar_senha = 0;

            if (Funcoes_Senha.Expiracao(usuario.Senha))
            {
                // apresentar tela para mudar senha por expiração
                mudar_senha = 1;
            }

            // verifica se ainda é a primeira senha
            if (Funcoes_Senha.NovoUsuario(usuario.Senha))
            {
                // apresentar tela para mudar senha por ser nova
                mudar_senha = 2;
            }

            ViewBag.mudar_senha = mudar_senha;


            // esconde a senha
            usuario.Senha = "********";
            usuario.SenhaAtual = "********";
            ViewBag.usuario = usuario;


            // verifica se GESTAL
            if (isUser.isGESTAL(usuario.IDTipoAcesso))
            {
                // le todas medicoes para lista
                medicoes2 = medicoesMetodos.ListarPorIDCliente(-1);
                ViewBag.Medicoes2 = medicoes2;

                // retorna usuario
                return View("EditAdmin", usuario);
            }

            // verifica se consultor
            if (isUser.isConsultor(usuario.IDTipoAcesso))
            {
                // le clientes associados ao consultor - ativos
                ClientesMetodos clientesMetodos = new ClientesMetodos();
                List<ClientesDominio> clientes = clientesMetodos.ListarPorIDConsultor(usuario.IDCliente, 1);

                foreach (ClientesDominio cliente in clientes)
                {
                    // le medicoes para lista
                    List<CliGateGrupoUnidMedicoesDominio> medicoes3 = new List<CliGateGrupoUnidMedicoesDominio>();
                    medicoes3 = medicoesMetodos.ListarPorIDCliente(cliente.IDCliente);

                    foreach (CliGateGrupoUnidMedicoesDominio medicao in medicoes3)
                    {
                        // coloca na lista
                        medicoes2.Add(medicao);
                    }
                }

                ViewBag.Medicoes2 = medicoes2;

                // retorna usuario
                return View("EditAdmin", usuario);
            }

            // le medicoes do cliente
            medicoes2 = medicoesMetodos.ListarPorIDCliente(usuario.IDCliente);
            ViewBag.Medicoes2 = medicoes2;

            // verifica se cliente gerente ou operador
            if (isUser.isCliente(usuario.IDTipoAcesso))
            {
                // retorna usuario
                return View("EditGerOper", usuario);
            }

            // retorna usuario
            return View("EditGerOper", usuario);
        }

        // POST: UsuarioPerfil
        [HttpPost]
        public ActionResult Edit(UsuarioDominio usuario, List<UsuarioMedicaoDominio> usuarioMedicoes)
        {
            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "UsuarioPerfil");

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // protege contra IDCliente inválido
            if (usuario.IDCliente < 0)
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Erro ao salvar. Entrar em contato com o suporte e informar código -10"
                };

                // retorna status
                return Json(returnedData);
            }

            // protege contra salvar como Admin
            // só pode ser admin Junior e Sérgio, se quiser ter outro tem que fazer via banco de dados
            if (usuario.IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN && (usuario.IDUsuario != 1 && usuario.IDUsuario != 4165))
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Erro ao salvar. Entrar em contato com o suporte e informar código 0"
                };

                // retorna status
                return Json(returnedData);
            }


            // le cookies
            LeCookies_SmartEnergy();

            // IDUsuario
            int IDUsuario = ViewBag._IDUsuario;

            // le usuario atual
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario_atual = new UsuarioDominio();
            usuario_atual = usuarioMetodos.ListarPorId(IDUsuario);

            // protege usuario
            if (usuario_atual == null)
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Erro ao salvar. Entrar em contato com o suporte e informar código -8"
                };

                // retorna status
                return Json(returnedData);
            }

            // protege IDUsuario diferente
            if (usuario.IDUsuario != IDUsuario)
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Erro ao salvar. Entrar em contato com o suporte e informar código -9"
                };

                // retorna status
                return Json(returnedData);
            }

            // verifica se a senha mudou
            if (usuario.Senha != "********" && usuario.Senha != usuario_atual.SenhaAtual)
            {
                // senhas usadas
                SenhasUsadasMetodos senhasUsadasMetodos = new SenhasUsadasMetodos();

                // verifica se senha já foi usada
                if (senhasUsadasMetodos.VerificaSenhaUsada(usuario.IDUsuario, usuario.Senha))
                {
                    returnedData = new
                    {
                        status = "ERRO",
                        erro = "Você está repetindo alguma das últimas 10 senhas utilizadas.<br><br>Por favor cadastrar outra senha."
                    };

                    // retorna status
                    return Json(returnedData);
                }

                // salva senha usada
                senhasUsadasMetodos.Inserir(usuario.IDUsuario, usuario.Senha);
            }
            else
            {
                // não alterou a senha uso a atual
                usuario.Senha = usuario_atual.Senha;
                usuario.SenhaAtual = usuario_atual.SenhaAtual;
            }


            // verifica se mudou email
            bool mudou_email = false;

            // verifica se o email mudou
            if (usuario.Email != usuario.EmailAtual)
            {
                // mudou email
                mudou_email = true;
            }

            // zera inicialmente medicoes
            usuario.ConfigGrupo = "";
            usuario.ConfigGrupoEmail = "";
            usuario.ConfigUnid = "";
            usuario.ConfigUnidEmail = "";
            usuario.ConfigMed = "";
            usuario.ConfigMedEmail = "";

            // UsuarioMedicao
            UsuarioMedicaoMetodos usuarioMedicaoMetodos = new UsuarioMedicaoMetodos();

            // exclui todos as medicoes deste usuario
            usuarioMedicaoMetodos.Excluir(usuario.IDUsuario);

            // salva usuariomedicao
            if (usuarioMedicoes != null)
            {
                // percorre lista e salva
                foreach (UsuarioMedicaoDominio usuarioMedicao in usuarioMedicoes)
                {
                    // le cliente e gateway da medicao
                    MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                    MedicoesDominio medicao = medicaoMetodos.ListarPorId(usuarioMedicao.IDMedicao);

                    if (medicao != null)
                    {
                        // preenche campos que faltam
                        usuarioMedicao.IDUsuario = usuario.IDUsuario;
                        usuarioMedicao.IDCliente = medicao.IDCliente;
                        usuarioMedicao.IDGateway = medicao.IDGateway;


                        // utiliza os flags do usuario
                        usuarioMedicao.RelatDiario = false;
                        usuarioMedicao.RelatSemanal = false;
                        usuarioMedicao.RelatMensal = false;
                        usuarioMedicao.RelatRanking = false;
                        usuarioMedicao.MsgGeren = false;
                        usuarioMedicao.MsgProcessa = false;

                        // verifico os flags de relatorio periodico
                        if (usuario.DemAtv == 1 || usuario.FatPot == 1 || usuario.Consumo == 1 || usuario.Supervisao == 1 || usuario.Util_Anal_Ciclo == 1)
                        {
                            // diario
                            if (usuario.Diario == 1)
                                usuarioMedicao.RelatDiario = true;

                            // semanal
                            if (usuario.Semanal == 1)
                                usuarioMedicao.RelatSemanal = true;

                            // mensal
                            if (usuario.Mensal == 1)
                                usuarioMedicao.RelatMensal = true;
                        }

                        // verifico o flag de relatorio ranking
                        if (usuario.Ranking == 1)
                        {
                            // ranking
                            usuarioMedicao.RelatRanking = true;
                        }

                        // verifico os flags de mensagens de gerenciamento
                        if (usuario.MsgCliMed_Dem == 1 || usuario.MsgCliMed_Cons == 1 || usuario.MsgCliMed_FatPot == 1)
                        {
                            // mensagens de gerenciamento proveniente da medicao (dem alta, baixa, fat pot, cons alto, baixo)
                            usuarioMedicao.MsgGeren = true;
                        }

                        // verifico os flags de processamento
                        if (usuario.MsgCliEve == 1)
                        {
                            // mensagens de gerenciamento proveniente do processamento dos arquivos recebidos
                            usuarioMedicao.MsgProcessa = true;
                        }


                        // salva
                        usuarioMedicaoMetodos.Salvar(usuarioMedicao);
                    }
                }
            }

            // salva usuario 
            usuarioMetodos.Salvar(usuario);

            // atualiza nome no cabecalho
            System.Web.Security.FormsAuthentication.SetAuthCookie(usuario.Apelido, false);

            // atualiza idioma e cultura
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<IdiomaDominio> listaTiposIdioma = listatiposMetodos.ListarTodos_Idioma();

            if (listaTiposIdioma != null)
            {
                // encontra culture
                IdiomaDominio idioma = listaTiposIdioma.Find(x => x.IDIdioma == usuario.IDIdioma);

                if (idioma != null)
                {
                    Thread.CurrentThread.CurrentCulture = CultureInfo.CreateSpecificCulture(idioma.Culture);
                    Thread.CurrentThread.CurrentUICulture = new CultureInfo(idioma.Culture);

                    HttpCookie cookie = new HttpCookie("Language");
                    cookie.Value = idioma.Culture;
                    Response.Cookies.Add(cookie);
                }
            }

            //
            // ATUALIZA CLIENTES E MEDICOES HABILITADOS
            //
            // uso o UsuariosMedicoes para atualizar o campo CONFIGMED / CONFIGMEDEMAIL e flags de recebimento de email em Usuarios
            if ( isUser.isCliente(usuario.IDTipoAcesso) )
            {
                usuarioMedicaoMetodos.AtualizarUsuariosMedicoes(usuario.IDUsuario);
            }

            if ( isUser.isGESTAL(usuario.IDTipoAcesso) || isUser.isConsultor(usuario.IDTipoAcesso) )
            {
                usuarioMedicaoMetodos.AtualizarUsuariosMedicoes(usuario.IDUsuario);
                usuarioMedicaoMetodos.AtualizarUsuariosMedicoesAdmin(usuario.IDUsuario);
            }

            // atualiza cookie
            CookieStore.SalvaCookie_String("NomeUsuario", usuario.NomeUsuario);
            CookieStore.SalvaCookie_String("EmailUsuario", usuario.Email);



            //
            // CLIENTES HABILITADOS
            //

            // lista de clientes habilitados para o usuario
            string ConfigCli = "";
            List<int> ConfigCliList = new List<int>();

            // verifica se consultor - administrador
            if (usuario.IDTipoAcesso == ISUSER.Consultor || usuario.IDTipoAcesso == ISUSER.Consultor_Admin)
            {
                // leio TODOS clientes deste consultor - ativos
                ClientesMetodos clientesMetodos = new ClientesMetodos();
                List<ClientesDominio> clientes = clientesMetodos.ListarPorIDConsultor(usuario.IDCliente, 1);

                // verifica se tem cliente
                if (clientes.Count > 0)
                {
                    // percorre todos os clientes e monta string
                    foreach (ClientesDominio cliente in clientes)
                    {
                        ConfigCli = ConfigCli + "/" + cliente.IDCliente.ToString();
                    }

                    // fecha barra
                    ConfigCli = ConfigCli + "/";
                }
                else
                {
                    ConfigCli = "/-10/";
                }
            }

            // verifica se consultor - operador
            if (usuario.IDTipoAcesso == ISUSER.Consultor_Oper)
            {
                // leio medicoes habilitadas deste usuario
                List<UsuarioMedicaoDominio> usuariosmedicoes = usuarioMedicaoMetodos.ListarTodasMedicoesIDUsuario(usuario.IDUsuario);

                // verifica se tem medicao
                if (usuariosmedicoes.Count > 0)
                {
                    // percorre todas as medicoes e monta string
                    foreach (UsuarioMedicaoDominio usuariomedicao in usuariosmedicoes)
                    {
                        // verifica se IDCliente NAO existe na lista
                        if (!ConfigCliList.Contains(usuariomedicao.IDCliente))
                        {
                            // copia medicao na lista
                            ConfigCliList.Add(usuariomedicao.IDCliente);

                            ConfigCli = ConfigCli + "/" + usuariomedicao.IDCliente.ToString();
                        }
                    }

                    // fecha barra
                    ConfigCli = ConfigCli + "/";
                }
                else
                {
                    ConfigCli = "/-10/";
                }
            }

            //
            // MEDICOES HABILITADAS
            //

            // lista de medicoes habilitadas para o usuario
            string ConfigMed = "";

            // verifica se tem medicao
            if( usuarioMedicoes != null )
            {
                if (usuarioMedicoes.Count > 0)
                {
                    // percorre todas as medicoes e monta string
                    foreach (UsuarioMedicaoDominio usuariomedicao in usuarioMedicoes)
                    {
                        // medicao habilitada
                        ConfigMed = ConfigMed + "/" + usuariomedicao.IDMedicao.ToString();
                    }

                    // fecha barra
                    ConfigMed = ConfigMed + "/";
                }
            }

            CookieStore.SalvaCookie_String("ConfigMed", ConfigMed);
            CookieStore.SalvaCookie_String("ConfigCli", ConfigCli);


            // pego IDUsuario novamente (pois pode ter sido insercao)
            UsuarioDominio user = usuarioMetodos.ListarPorLogin(usuario.Login);

            // salva 
            if (user != null)
            {
                // verifica se o email mudou
                if (mudou_email)
                {
                    // enviar email de confirmação
                    EnviarConfirmacaoEmail(user.IDUsuario, user.NomeUsuario, user.Email);
                }
            }

            // retorna status
            return Json(returnedData);
        }


        // GET: Enviar Confirmação Email
        public void EnviarConfirmacaoEmail(int IDUsuario, string Nome, string Email)
        {

            // le cookies
            LeCookies_SmartEnergy();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // cria código de ativação
            string CodigoAtivacao = GenerateRandomString(15);

            // salva no banco de dados
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            usuarioMetodos.Atualiza_CodigoAtivacao(IDUsuario, CodigoAtivacao);

            // assunto
            string assunto = "[Smart Energy] Confirmação de Email";

            // envia EMAIL
            var emailTemplate = "ConfirmacaoEmail";

            if (IDConsultor == CLIENTES_ESPECIAIS.CPFL)
            {
                emailTemplate = "ConfirmacaoEmail_CPFL";
            }

            var message = EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.Nome", Nome);
            message = message.Replace("ViewBag.IDUsuario", IDUsuario.ToString());
            message = message.Replace("ViewBag.CodigoAtivacao", CodigoAtivacao);
            EmailServices.SendEmail(Email, assunto, message, "", "", null);

            // retorna
            return;
        }

        public static string GenerateRandomString(int length)
        {
            var numArray = new byte[length];
            new RNGCryptoServiceProvider().GetBytes(numArray);
            return CleanUpBase64String(Convert.ToBase64String(numArray), length);
        }

        private static string CleanUpBase64String(string input, int maxLength)
        {
            input = input.Replace("-", "");
            input = input.Replace("=", "");
            input = input.Replace("/", "");
            input = input.Replace("+", "");
            input = input.Replace(" ", "");
            while (input.Length < maxLength)
                input = input + GenerateRandomString(maxLength);
            return input.Length <= maxLength ?
                input.ToUpper() : //In my case I want capital letters
                input.ToUpper().Substring(0, maxLength);
        }

        public static string EMailTemplate(string template)
        {
            var templateFilePath = HostingEnvironment.MapPath("~/Content/emailTemplates/") + template + ".cshtml";
            StreamReader objstreamreaderfile = new StreamReader(templateFilePath);
            var body = objstreamreaderfile.ReadToEnd();
            objstreamreaderfile.Close();
            return body;
        }
    }
}