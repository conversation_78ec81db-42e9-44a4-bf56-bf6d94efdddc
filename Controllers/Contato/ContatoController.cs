﻿using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class ContatoController : Controller
    {
        // GET: Contato
        public ActionResult Contato()
        {
            // consultor padrao - GESTAL
            ConsultoresDominio consultor = new ConsultoresDominio();

            consultor.IDConsultor = 0;
            consultor.Nome = "GESTAL Automação de Sistemas";
            consultor.Email = "<EMAIL>";
            consultor.Telefone1 = "(11) 5080-8200";
            consultor.Telefone2 = "";
            consultor.Telefone3 = "";
            consultor.Celular = "(11) 9.7598-5359";
            consultor.Endereco = "Rua Borges Lagoa, 190";
            consultor.Site = "https://www.gestal.com";
            consultor.CEP = "04038-000";
            consultor.IDCidade = 5270;
            consultor.Cidade = "";
            consultor.Estado = "";
            consultor.IDEstado = 26;
            consultor.Latitude = 0;
            consultor.Longitude = 0;

            // le cookies
            LeCookies_SmartEnergy();

            // verifica IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            ConsultoresMetodos consultorMetodos = new ConsultoresMetodos();
            ConsultoresDominio consultor_bd = consultorMetodos.ListarPorId(IDConsultor);

            if (consultor_bd != null)
            {
                // copia
                consultor = consultor_bd;
            }

            // cidade
            CidadeEstadoPaisMetodos cidadeEstadoMetodos = new CidadeEstadoPaisMetodos();
            CidadesDominio cidade = cidadeEstadoMetodos.CidadePorId(consultor.IDCidade);
            if( cidade != null)
            {
                consultor.Cidade = cidade.Nome;
            }

            // estado
            EstadosDominio estado = cidadeEstadoMetodos.EstadoPorId(consultor.IDEstado);
            if (estado != null)
            {
                consultor.Estado = estado.Nome;
            }

            ViewBag.Consultor = consultor;

            return View();
        }
    }
}