﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.OleDb;
using System.IO;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{

    public partial class UploadController
    {

        // GET: Importar Histórico de Medição de Utilidades Mensal
        public ActionResult Upload_UtilidadesMensal()
        {
            // tela de ajuda - upload
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // le cookies
            LeCookies_SmartEnergy();

            return View();
        }


        // progresso
        public class PROGRESSO_TASK_UTIL_MENSAL
        {
            public int num_medicoes { get; set; }
            public int progresso_medicao { get; set; }
            public string status_medicao { get; set; }

            public int num_registros { get; set; }
            public int progresso_registro { get; set; }
            public string status_registro { get; set; }
        }

        // estruturas
        private static IDictionary<Guid, PROGRESSO_TASK_UTIL_MENSAL> tasks_util_mensal = new Dictionary<Guid, PROGRESSO_TASK_UTIL_MENSAL>();
        private static List<PROCESSA_UTIL_MENSAL_RESULTADO> lista_processa_util_mensal_tmp = new List<PROCESSA_UTIL_MENSAL_RESULTADO>();
        private static IDictionary<Guid, List<PROCESSA_UTIL_MENSAL_RESULTADO>> lista_processa_util_mensal = new Dictionary<Guid, List<PROCESSA_UTIL_MENSAL_RESULTADO>>();


        // GET: Processa Histórico de Medição de Utilidades Mensal
        public ActionResult ProcessaUtilidadesMensal_IniciaProcesso(string nomeHistorico)
        {
            // taskID
            Guid taskId = Guid.NewGuid();

            // progresso
            PROGRESSO_TASK_UTIL_MENSAL progresso = new PROGRESSO_TASK_UTIL_MENSAL();
            progresso.num_medicoes = 0;
            progresso.num_registros = 0;
            progresso.progresso_medicao = 0;
            progresso.progresso_registro = 0;
            progresso.status_medicao = "[-/-] ---";
            progresso.status_registro = "[-/-] Registros";

            tasks_util_mensal.Add(taskId, progresso);

            // salva cookie
            CookieStore.SalvaCookie_GUID("taskID", taskId);

            // limpa listas
            lista_processa_util_mensal_tmp.Clear();

            // task
            Task.Factory.StartNew(() =>
            {
                // verifica se arquivo existe
                if (nomeHistorico != null)
                {
                    // diretorio
                    string pathForSaving = Server.MapPath("~/Temp");

                    // nome do arquivo completo
                    string nomearquivo = Path.Combine(pathForSaving, nomeHistorico);

                    // extensao
                    string extensao = Path.GetExtension(nomeHistorico).ToUpper();

                    // processa histórico
                    ProcessaUtilidadesMensal_Processar(nomearquivo, extensao, taskId, ref progresso);
                }

                // coloca resultado na lista
                lista_processa_util_mensal.Add(taskId, new List<PROCESSA_UTIL_MENSAL_RESULTADO>(lista_processa_util_mensal_tmp));

                // terminou
                tasks_util_mensal.Remove(taskId);
            });

            // retorna status
            return Json(taskId, JsonRequestBehavior.AllowGet);
        }

        public ActionResult ProcessaUtilidadesMensal_Progress(Guid id)
        {
            PROGRESSO_TASK_UTIL_MENSAL progresso = new PROGRESSO_TASK_UTIL_MENSAL();
            progresso.num_medicoes = 0;
            progresso.num_registros = 0;
            progresso.progresso_medicao = 100;
            progresso.progresso_registro = 100;
            progresso.status_medicao = "";
            progresso.status_registro = "";

            return Json(tasks_util_mensal.Keys.Contains(id) ? tasks_util_mensal[id] : progresso, JsonRequestBehavior.AllowGet);
        }

        public PartialViewResult _ProcessaUtilidadesMensal_Resultado(Guid id)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // resultado
            ViewBag.processaUtilidadesMensal_resultado = lista_processa_util_mensal.Keys.Contains(id) ? lista_processa_util_mensal[id] : null;

            return PartialView();
        }

        public int ProcessaUtilidadesMensal_Processar(string caminho_arq, string extensao_arq, Guid taskId, ref PROGRESSO_TASK_UTIL_MENSAL progresso)
        {
            //
            // Número de linhas da planilha
            //
            int num_medicoes = GetNumberOfRows(caminho_arq, "Tabela") - 1;

            if (num_medicoes < 0)
            {
                // erro
                return (-1);
            }

            // conexao
            // baixar o provedor de acesso Excel 2010 em "https://www.microsoft.com/en-us/download/details.aspx?id=13255"
            // AccessDatabaseEngine_X64.exe
            // configurando no SQL SERVER https://www.dirceuresende.com/blog/sql-server-como-instalar-os-drivers-microsoft-ace-oledb-12-0-e-microsoft-jet-oledb-4-0/
            OleDbConnection _olecon;
            OleDbCommand _oleCmd;

            // XLS
            String _StringConexao = String.Format(@"Provider=Microsoft.JET.OLEDB.4.0;Data Source={0};Extended Properties='Excel 8.0;HDR=YES;';", caminho_arq);

            // verifica se XLSX
            if (extensao_arq == ".XLSX")
            {
                // XLSX
                _StringConexao = String.Format(@"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={0};Extended Properties='Excel 12.0;HDR=YES;';", caminho_arq);
            }

            _olecon = new OleDbConnection(_StringConexao);
            _oleCmd = new OleDbCommand();

            // linha convertida
            int linha = -1;

            // le Excel
            try
            {
                // conecta
                _olecon.Open();
                _oleCmd.Connection = _olecon;
                _oleCmd.CommandType = CommandType.Text;

                // le excel
                _oleCmd.CommandText = "SELECT * FROM [Tabela$]";
                OleDbDataReader reader = _oleCmd.ExecuteReader();

                // inicia linha
                linha = 0;


                // barra de progresso medicoes
                int total = num_medicoes;
                int atual = 0;

                // progresso
                progresso.num_medicoes = 0;
                progresso.progresso_medicao = 0;
                progresso.num_registros = 0;
                progresso.progresso_registro = 0;
                progresso.status_medicao = "[-/-] ---";
                progresso.status_registro = "[-/-] Registros";
                tasks_util_mensal[taskId] = progresso;

                // lê planilha
                while (reader.Read())
                {
                    // inicia resultado
                    PROCESSA_UTIL_MENSAL_RESULTADO resultado = new PROCESSA_UTIL_MENSAL_RESULTADO();
                    resultado.IDCliente = 0;
                    resultado.NomeCliente = "";
                    resultado.IDMedicao = 0;
                    resultado.NomeMedicao = "";
                    resultado.status = PROCESSA_UTIL_MENSAL_STATUS.SEM_STATUS;
                    resultado.linha = linha + 2;
                    resultado.inicio = new DateTime(2000, 1, 1, 0, 0, 0);
                    resultado.fim = new DateTime(2000, 1, 1, 0, 0, 0);
                    resultado.NumRegistros = 0;

                    // atualiza progresso medicoes
                    progresso.progresso_medicao = (int)(((double)atual / (double)total) * 100);
                    progresso.status_medicao = string.Format("[{0}/{1}] {2}", atual + 1, total, "---");
                    progresso.progresso_registro = 0;
                    progresso.status_registro = "[-/-] Registros";
                    tasks_util_mensal[taskId] = progresso;
                    atual++;

                    // IDMedicao
                    int IDMedicao = Convert.ToInt32(reader.GetDouble(1));

                    // lê configuracao da medição
                    MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                    MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);

                    if (medicao == null)
                    {
                        resultado.status = PROCESSA_UTIL_MENSAL_STATUS.MED_INEX;
                        goto FINAL;
                    }

                    // verifica medição
                    if (medicao.IDMedicao == 0)
                    {
                        resultado.status = PROCESSA_UTIL_MENSAL_STATUS.MED_INEX;
                        goto FINAL;
                    }

                    // verifica se não é medição de utilidades
                    if (medicao.IDTipoMedicao != TIPOS_MEDICAO.utilidades)
                    {
                        resultado.status = PROCESSA_UTIL_MENSAL_STATUS.MED_NO_UTIL;
                        goto FINAL;
                    }

                    // verifica se é gateway virtual (SCDE)
                    GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
                    GatewaysDominio gateway = gatewayMetodos.ListarPorId(medicao.IDGateway);

                    if (gateway == null)
                    {
                        resultado.status = PROCESSA_UTIL_MENSAL_STATUS.GATE_INEX;
                        goto FINAL;
                    }

                    if (gateway.IDTipoGateway != TIPO_GATEWAY.SCDE)
                    {
                        resultado.status = PROCESSA_UTIL_MENSAL_STATUS.GATE_NO_VIRT;
                        goto FINAL;
                    }

                    // encontrou medição
                    resultado.IDCliente = medicao.IDCliente;

                    ClientesMetodos clienteMetodos = new ClientesMetodos();
                    ClientesDominio cliente = clienteMetodos.ListarPorId(medicao.IDCliente);

                    if (cliente != null)
                    {
                        resultado.NomeCliente = cliente.Fantasia;
                    }

                    resultado.IDMedicao = medicao.IDMedicao;
                    resultado.NomeMedicao = medicao.Nome;

                    // progresso nome da medição
                    progresso.status_medicao = string.Format("[{0}/{1}] {2}", atual, total, medicao.Nome);
                    tasks_util_mensal[taskId] = progresso;

                    // DataHora Início
                    DateTime data_ini_original = Convert.ToDateTime(reader.GetDateTime(3));

                    // DataHora Fim
                    DateTime data_fim_original = Convert.ToDateTime(reader.GetDateTime(4));

                    // verifica se invertido
                    if (data_ini_original > data_fim_original)
                    {
                        // desinverto
                        DateTime aux = data_fim_original;
                        data_fim_original = data_ini_original;
                        data_ini_original = aux;
                    }

                    // verifica se ano válido
                    if (data_ini_original.Year < 2015 || data_ini_original.Year > 2030)
                    {
                        resultado.status = PROCESSA_UTIL_MENSAL_STATUS.DATA_INVALIDA;
                        goto FINAL;
                    }

                    // arredondo a data inicial para cima
                    DateTime data_ini = new DateTime(data_ini_original.Year, data_ini_original.Month, data_ini_original.Day, data_ini_original.Hour, 0, 0);
                    data_ini = data_ini.AddHours(1);

                    // arredondo a data final para baixo
                    DateTime data_fim = new DateTime(data_fim_original.Year, data_fim_original.Month, data_fim_original.Day, data_fim_original.Hour, 0, 0);

                    // verifica se passou dos 30 minutos
                    if (data_fim_original.Minute >= 30)
                    {
                        // arredondo para cima
                        data_fim = data_fim.AddHours(1);
                    }

                    // datas
                    resultado.inicio = data_ini;
                    resultado.fim = data_fim;

                    // verifica se data final é maior que hoje (não permitimos valores futuros)
                    if (resultado.fim > DateTime.Now)
                    {
                        resultado.status = PROCESSA_UTIL_MENSAL_STATUS.MAIOR_HOJE;
                        goto FINAL;
                    }

                    // verifica se diferença maior que 60 dias
                    TimeSpan diferenta_data = data_fim - data_ini;
                    double Dias = diferenta_data.TotalDays;
                    double Horas = diferenta_data.TotalHours + 1;

                    if (Dias > 60.0)
                    {
                        resultado.status = PROCESSA_UTIL_MENSAL_STATUS.MAIOR_60DIAS;
                        goto FINAL;
                    }

                    // consumo mês
                    double consumo_mes = reader.GetDouble(5);

                    // calcula consumo horário
                    double consumo_horario = 0.0;

                    if (Horas > 0)
                    {
                        consumo_horario = consumo_mes / Horas;
                    }

                    // data registro
                    DateTime data_registro = data_ini;

                    // barra de progresso registros
                    int total_registro = (int)Horas;
                    int atual_registro = 0;

                    // número de registros
                    resultado.NumRegistros = (int)Horas;

                    // percorre horas
                    for (int i = 0; i < Horas; i++)
                    {
                        // atualiza progresso registro
                        progresso.progresso_registro = (int)(((double)atual / (double)total_registro) * 100);
                        progresso.status_registro = string.Format("[{0}/{1}] Registros", atual_registro + 1, total_registro);
                        tasks_util_mensal[taskId] = progresso;
                        atual_registro++;


                        // histórico da medição
                        GG_Dominio registro_gg = new GG_Dominio();
                        registro_gg.DataHora = data_registro;

                        // VMed / VMin / VMax
                        registro_gg.VMed = consumo_horario;
                        registro_gg.VMin = consumo_horario;
                        registro_gg.VMax = consumo_horario;

                        // adiciona registro
                        GG_Metodos ggMetodos = new GG_Metodos();
                        if (!ggMetodos.InserirRegistro_ModificarInserir(medicao.IDCliente, medicao.IDMedicao, registro_gg))
                        {
                            resultado.status = PROCESSA_UTIL_MENSAL_STATUS.ERRO_SALVAR_REG;
                            goto FINAL;
                        }

                        // próxima hora
                        data_registro = data_registro.AddHours(1);
                    }

                    //
                    // Atualiza tabela SUP_GG
                    //
                    SUP_GG_Dominio registro_sup = new SUP_GG_Dominio();

                    registro_sup.Versao = "0.00";
                    registro_sup.Data = data_fim;
                    registro_sup.VUlt = consumo_horario;
                    registro_sup.DataUlt = data_fim;
                    registro_sup.VMin = consumo_horario;
                    registro_sup.DataMin = data_ini;
                    registro_sup.VMax = consumo_horario;
                    registro_sup.DataMax = data_ini;
                    registro_sup.Cons = consumo_mes;
                    registro_sup.DataIni = data_ini;
                    registro_sup.DataAtual = DateTime.Now;

                    SUP_GG_Metodos supGGMetodos = new SUP_GG_Metodos();
                    supGGMetodos.Atualiza(medicao.IDCliente, medicao.IDMedicao, registro_sup);

                    //
                    // Atualiza tabela SUP_000000
                    //
                    SUP_Metodos supMetodos = new SUP_Metodos();
                    supMetodos.Atualiza(medicao.IDCliente, medicao.IDGateway, data_fim);

                    // processado ok
                    resultado.status = PROCESSA_UTIL_MENSAL_STATUS.PROCESSO_OK;

                FINAL:

                    // insere na lista temporária
                    ProcessaUtilidadesMensal_InsereListaResultado(resultado);

                    // atualiza número de medições atualizadas
                    num_medicoes++;

                    // proxima linha
                    linha++;
                }

                reader.Close();

                // termina
                if (_oleCmd != null)
                {
                    _oleCmd.Parameters.Clear();
                    _oleCmd.Dispose();
                }
                _oleCmd = null;

                if (_olecon != null)
                {
                    if (_olecon.State == ConnectionState.Open)
                        _olecon.Close();
                    _olecon.Dispose();
                }
                _olecon = null;

            }
            catch (Exception)
            {
                // termina
                if (_oleCmd != null)
                {
                    _oleCmd.Parameters.Clear();
                    _oleCmd.Dispose();
                }
                _oleCmd = null;

                if (_olecon != null)
                {
                    if (_olecon.State == ConnectionState.Open)
                        _olecon.Close();
                    _olecon.Dispose();
                }
                _olecon = null;
                
                // erro
                return (-1);
            }
            
            // ok
            return (num_medicoes);
        }


        // Insere resultado temporário
        private void ProcessaUtilidadesMensal_InsereListaResultado(PROCESSA_UTIL_MENSAL_RESULTADO resultado)
        {
            // resultado
            PROCESSA_UTIL_MENSAL_RESULTADO tmp = new PROCESSA_UTIL_MENSAL_RESULTADO();
            tmp.IDCliente = resultado.IDCliente;
            tmp.NomeCliente = resultado.NomeCliente;
            tmp.IDMedicao = resultado.IDMedicao;
            tmp.NomeMedicao = resultado.NomeMedicao;
            tmp.status = resultado.status;
            tmp.linha = resultado.linha;
            tmp.inicio = resultado.inicio;
            tmp.fim = resultado.fim;
            tmp.NumRegistros = resultado.NumRegistros;

            // coloca na lista temporaria
            lista_processa_util_mensal_tmp.Add(tmp);
        }

        private int GetNumberOfRows(string filename, string sheetName)
        {
            int count = 0;

            string connectionString = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" + filename + ";Mode=ReadWrite;Extended Properties=\"Excel 8.0;HDR=NO;\""; ;

            if (filename.EndsWith(".xlsx"))
            {
                connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + filename + ";Mode=ReadWrite;Extended Properties=\"Excel 12.0 Xml;HDR=NO\"";
            }

            string SQL = "SELECT COUNT (*) FROM [" + sheetName + "$]";

            // le Excel
            try
            {
                using (OleDbConnection conn = new OleDbConnection(connectionString))
                {
                    conn.Open();

                    using (OleDbCommand cmd = new OleDbCommand(SQL, conn))
                    {
                        using (OleDbDataReader reader = cmd.ExecuteReader())
                        {
                            reader.Read();
                            count = reader.GetInt32(0);
                        }
                    }

                    conn.Close();
                }
            }
            catch
            {
                // erro
                return (-1);
            }

            return count;
        }
    }
}
