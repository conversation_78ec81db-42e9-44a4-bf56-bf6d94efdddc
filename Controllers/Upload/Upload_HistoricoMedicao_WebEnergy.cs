﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.OleDb;
using System.Globalization;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{

    public partial class UploadController
    {

        // GET: Importar Histórico de Medição
        public ActionResult Upload_HistoricoMedicao_WebEnergy(int IDCliente, int IDMedicao)
        {
            // le configuracao da medicao
            MedicoesMetodos medicoesMetodos_conf = new MedicoesMetodos();
            MedicoesDominio medicao_conf = medicoesMetodos_conf.ListarPorId(IDMedicao);

            // salva cookie 
            CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", medicao_conf.IDTipoMedicao);
            CookieStore.SalvaCookie_Int("_IDGateway", medicao_conf.IDGateway);
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Upload_WebEnergy");

            // tela de ajuda - upload
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // tipo pagina

            // le cookies
            LeCookies_SmartEnergy();

            // leio configuracoes
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cliente = clienteMetodos.ListarPorId(IDCliente);
            ViewBag.NomeCliente = cliente.Fantasia;

            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);
            ViewBag.NomeMedicao = medicao.Nome;

            return View();
        }


        // progresso
        public class PROGRESSO_TASK_HIST_MED_WEBENERGY
        {
            public int num_arquivos { get; set; }
            public int progresso_arquivo { get; set; }
            public string status_arquivo { get; set; }

            public int num_registros { get; set; }
            public int progresso_registro { get; set; }
            public string status_registro { get; set; }
        }

        // estruturas
        private static IDictionary<Guid, PROGRESSO_TASK_HIST_MED_WEBENERGY> tasks_hist_med_webEnergy = new Dictionary<Guid, PROGRESSO_TASK_HIST_MED_WEBENERGY>();
        private static List<string> lista_arquivos_webEnergy = new List<string>();
        private static List<PROCESSA_HIST_MED_RESULTADO> lista_processa_hist_med_webEnergy_tmp = new List<PROCESSA_HIST_MED_RESULTADO>();
        private static IDictionary<Guid, List<PROCESSA_HIST_MED_RESULTADO>> lista_processa_hist_med_webEnergy = new Dictionary<Guid, List<PROCESSA_HIST_MED_RESULTADO>>();


        // GET: Processa Histórico de Medição
        public ActionResult ProcessaHistoricoMedicao_WebEnergy_IniciaProcesso(string nomeHistorico)
        {
            // taskID
            Guid taskId = Guid.NewGuid();

            // progresso
            PROGRESSO_TASK_HIST_MED_WEBENERGY progresso = new PROGRESSO_TASK_HIST_MED_WEBENERGY();
            progresso.num_arquivos = 0;
            progresso.num_registros = 0;
            progresso.progresso_arquivo = 0;
            progresso.progresso_registro = 0;
            progresso.status_arquivo = "[-/-] ---";
            progresso.status_registro = "[-/-] Registros";

            tasks_hist_med_webEnergy.Add(taskId, progresso);

            // salva cookie
            CookieStore.SalvaCookie_GUID("taskID", taskId);

            // le cookies
            LeCookies_SmartEnergy();

            // IDMedicao
            int IDMedicao = ViewBag._IDMedicao;

            // lê configuracao da medição
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);

            // cliente da medição
            int IDCliente = medicao.IDCliente;

            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cliente = clienteMetodos.ListarPorId(IDCliente);

            // limpa listas
            lista_arquivos_webEnergy.Clear();
            lista_processa_hist_med_webEnergy_tmp.Clear();

            // task
            Task.Factory.StartNew(() =>
            {
                // verifica se arquivo existe
                if (nomeHistorico != null)
                {
                    // diretorio
                    string pathForSaving = Server.MapPath("~/Temp");

                    // subdiretorio destino
                    string subdiretorio_destino = "";

                    // nome do arquivo completo
                    string nomeArquivoFull = Path.Combine(pathForSaving, nomeHistorico);

                    // verifica se arquivo CSV eh ZIP
                    string extensao = Path.GetExtension(nomeHistorico).ToUpper();

                    if (extensao == ".ZIP")
                    {
                        // subdiretorio destino
                        subdiretorio_destino = string.Format("{0}\\WEBENERGY_{1:yyyyMMdd_hhmmss}", pathForSaving, DateTime.Now);

                        // descompacta arquivo
                        ZipFile.ExtractToDirectory(nomeArquivoFull, subdiretorio_destino, System.Text.Encoding.GetEncoding(850));

                        // lista de arquivos
                        DirectoryInfo info = new DirectoryInfo(subdiretorio_destino);
                        FileInfo[] files = info.GetFiles().OrderBy(f => f.LastWriteTime.Year <= 1601 ? f.CreationTime : f.LastWriteTime).ToArray();

                        if (files != null)
                        {
                            foreach (FileInfo file in files)
                            {
                                // nome do arquivo completo
                                string nomearq = Path.Combine(subdiretorio_destino, file.Name);

                                // coloca na lista
                                lista_arquivos_webEnergy.Add(nomearq);
                            }
                        }
                    }
                    else
                    {
                        // coloca na lista
                        lista_arquivos_webEnergy.Add(nomeArquivoFull);
                    }

                    // barra de progresso arquivos
                    int total = 0;
                    int atual = 0;

                    // progresso
                    progresso.num_arquivos = 0;
                    progresso.progresso_arquivo = 0;
                    progresso.num_registros = 0;
                    progresso.progresso_registro = 0;
                    progresso.status_arquivo = "[-/-] ---";
                    progresso.status_registro = "[-/-] Registros";

                    tasks_hist_med_webEnergy[taskId] = progresso;

                    // percorre arquivos
                    if (lista_arquivos_webEnergy != null)
                    {
                        // barra de progresso arquivos
                        total = lista_arquivos_webEnergy.Count();

                        // percorre arquivos
                        foreach (string arquivo in lista_arquivos_webEnergy)
                        {
                            // nome do arquivo
                            string nomeArquivo = Path.GetFileName(arquivo).ToUpper();

                            // atualiza progresso arquivos
                            progresso.progresso_arquivo = (int)(((double)atual / (double)total) * 100);
                            progresso.status_arquivo = string.Format("[{0}/{1}] {2}", atual + 1, total, nomeArquivo);
                            progresso.status_registro = "[-/-] Registros";

                            tasks_hist_med_webEnergy[taskId] = progresso;
                            atual++;

                            // verifica se tem arquivo
                            if (string.IsNullOrEmpty(arquivo))
                            {
                                continue;
                            }

                            // verifica se arquivo eh CSV
                            extensao = Path.GetExtension(arquivo).ToUpper();

                            if (extensao != ".CSV")
                            {
                                continue;
                            }

                            // recupera número da medição WebEnergy
                            string medicao_webEnergy = "0";

                            Regex regex = new Regex(@"DEMAND_DATA_(\d{1,7})_(\d{14})\.CSV");
                            Match match = regex.Match(nomeArquivo);

                            if (match.Success)
                            {
                                // medição WebEnergy
                                medicao_webEnergy = match.Groups[1].Value;

                                // processa histórico
                                if (ProcessaHistoricoMedicao_WebEnergy_Processar(arquivo, cliente, medicao, taskId, ref progresso) < 0)
                                {
                                    // erro
                                    PROCESSA_HIST_MED_RESULTADO resultado = new PROCESSA_HIST_MED_RESULTADO();
                                    resultado.NomeArquivo = arquivo;
                                    resultado.IDCliente = cliente.IDCliente;
                                    resultado.NomeCliente = cliente.Fantasia;
                                    resultado.IDMedicao = medicao.IDMedicao;
                                    resultado.NomeMedicao = medicao.Nome;
                                    resultado.status = PROCESSA_HIST_MED_STATUS.ERRO_ARQUIVO;
                                    resultado.linha = 0;
                                    resultado.inicio = new DateTime(2000, 1, 1, 0, 0, 0);
                                    resultado.fim = new DateTime(2000, 1, 1, 0, 0, 0);
                                    resultado.NumRegistros = 0;

                                    // insere na lista temporária
                                    ProcessaHistoricoMedicao_WebEnergy_InsereListaResultado(resultado, PROCESSA_HIST_MED_STATUS.ERRO_ARQUIVO);
                                }
                            }
                            else
                            {
                                // erro
                                PROCESSA_HIST_MED_RESULTADO resultado = new PROCESSA_HIST_MED_RESULTADO();
                                resultado.NomeArquivo = arquivo;
                                resultado.IDCliente = cliente.IDCliente;
                                resultado.NomeCliente = cliente.Fantasia;
                                resultado.IDMedicao = medicao.IDMedicao;
                                resultado.NomeMedicao = medicao.Nome;
                                resultado.status = PROCESSA_HIST_MED_STATUS.ERRO_NOME_ARQUIVO;
                                resultado.linha = 0;
                                resultado.inicio = new DateTime(2000, 1, 1, 0, 0, 0);
                                resultado.fim = new DateTime(2000, 1, 1, 0, 0, 0);
                                resultado.NumRegistros = 0;

                                // insere na lista temporária
                                ProcessaHistoricoMedicao_WebEnergy_InsereListaResultado(resultado, PROCESSA_HIST_MED_STATUS.ERRO_NOME_ARQUIVO);
                            }
                        }

                        // verifica se tem subdiretorio
                        if (!string.IsNullOrEmpty(subdiretorio_destino))
                        {
                            // apaga diretorio
                            Directory.Delete(subdiretorio_destino, true);

                            // apaga arquivo ZIP
                            System.IO.File.Delete(nomeArquivoFull);
                        }
                    }
                }

                // coloca resultado na lista
                lista_processa_hist_med_webEnergy.Add(taskId, new List<PROCESSA_HIST_MED_RESULTADO>(lista_processa_hist_med_webEnergy_tmp));

                // terminou
                tasks_hist_med_webEnergy.Remove(taskId);
            });

            // retorna status
            return Json(taskId, JsonRequestBehavior.AllowGet);
        }

        public ActionResult ProcessaHistoricoMedicao_WebEnergy_Progress(Guid id)
        {
            PROGRESSO_TASK_HIST_MED_WEBENERGY progresso = new PROGRESSO_TASK_HIST_MED_WEBENERGY();
            progresso.num_arquivos = 0;
            progresso.num_registros = 0;
            progresso.progresso_arquivo = 100;
            progresso.progresso_registro = 100;
            progresso.status_arquivo = "";
            progresso.status_registro = "";

            return Json(tasks_hist_med_webEnergy.Keys.Contains(id) ? tasks_hist_med_webEnergy[id] : progresso, JsonRequestBehavior.AllowGet);
        }

        public PartialViewResult _ProcessaHistoricoMedicao_WebEnergy_Resultado(Guid id)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // resultado
            ViewBag.processaHistoricoMedicao_resultado = lista_processa_hist_med_webEnergy.Keys.Contains(id) ? lista_processa_hist_med_webEnergy[id] : null;

            return PartialView();
        }

        public int ProcessaHistoricoMedicao_WebEnergy_Processar(string nomeArquivoFull, ClientesDominio cliente, MedicoesDominio medicao, Guid taskId, ref PROGRESSO_TASK_HIST_MED_WEBENERGY progresso)
        {
            // progresso
            progresso.num_arquivos = 0;
            progresso.progresso_arquivo = 0;
            progresso.num_registros = 0;
            progresso.progresso_registro = 0;
            progresso.status_registro = "[-/-] Registros";

            tasks_hist_med_webEnergy[taskId] = progresso;

            // número de registros
            int num_registros = 0;

            // inicia resultado
            PROCESSA_HIST_MED_RESULTADO resultado = new PROCESSA_HIST_MED_RESULTADO();
            resultado.NomeArquivo = Path.GetFileName(nomeArquivoFull);
            resultado.IDCliente = cliente.IDCliente;
            resultado.NomeCliente = cliente.Fantasia;
            resultado.IDMedicao = medicao.IDMedicao;
            resultado.NomeMedicao = medicao.Nome;
            resultado.status = PROCESSA_HIST_MED_STATUS.SEM_STATUS;
            resultado.linha = 0;
            resultado.coluna = 0;
            resultado.inicio = new DateTime(2000, 1, 1, 0, 0, 0);
            resultado.fim = new DateTime(2000, 1, 1, 0, 0, 0);
            resultado.NumRegistros = 0;

            // verifica se existe arquivo
            if (System.IO.File.Exists(nomeArquivoFull))
            {
                // le arquivo
                string[] fileContent = System.IO.File.ReadAllLines(nomeArquivoFull);

                // numero de linhas
                int numero_linhas = fileContent.Length;

                // verifica se tem minimo de linhas
                if (numero_linhas >= 1)
                {
                    // verifica cabeçalho (linha 0)

                    // separa colunas
                    string[] cabecalho = fileContent[0].Split(';');

                    // barra de progresso registros
                    progresso.num_registros = numero_linhas - 1;
                    progresso.progresso_registro = 0;
                    progresso.status_registro = string.Format("[-/{0}] Registros", progresso.num_registros + 1);
                    tasks_hist_med_webEnergy[taskId] = progresso;

                    int total = progresso.num_registros;
                    int atual = 0;

                    // le registros
                    for (int conta = 1; conta < progresso.num_registros; conta++)
                    {
                        // atualiza progresso registro
                        progresso.progresso_registro = (int)(((double)atual / (double)total) * 100);
                        progresso.status_registro = string.Format("[{0}/{1}] Registros", atual + 1, total + 1);
                        tasks_hist_med_webEnergy[taskId] = progresso;
                        atual++;

                        // linha
                        resultado.linha = conta;

                        // separa colunas
                        string[] colunas = fileContent[conta].Split(';');

                        // verifica se tem numero de colunas corretas
                        if (colunas == null)
                        {
                            // ERRO - numero errado de colunas
                            ProcessaHistoricoMedicao_WebEnergy_InsereListaResultado(resultado, PROCESSA_HIST_MED_STATUS.ERRO_ARQUIVO);

                            // descarto linha
                            continue;
                        }

                        // verifica se tem numero de colunas corretas
                        if (colunas.Length < 7)
                        {
                            // ERRO - numero errado de colunas
                            ProcessaHistoricoMedicao_WebEnergy_InsereListaResultado(resultado, PROCESSA_HIST_MED_STATUS.ERRO_ARQUIVO);

                            // descarto linha
                            continue;
                        }
                        else
                        {
                            //
                            // DATA HORA
                            //

                            // data hora
                            string data = colunas[0];
                            string hora = colunas[1];

                            // monta datahora
                            string dh = string.Format("{0} {1}", data, hora);
                            DateTime datahora = new DateTime();

                            if (!DateTime.TryParseExact(dh, "dd/MM/yyyy HH:mm", CultureInfo.InvariantCulture, DateTimeStyles.None, out datahora))
                            {
                                // ERRO - coluna data hora incorreta
                                ProcessaHistoricoMedicao_WebEnergy_InsereListaResultado(resultado, PROCESSA_HIST_MED_STATUS.ERRO_DATA_HORA, 1);

                                // descarto linha
                                continue;
                            }

                            // verifica se início
                            if (resultado.inicio.Year == 2000)
                            {
                                resultado.inicio = datahora;
                            }

                            // fim
                            resultado.fim = datahora;


                            //
                            // DEMANDA ATIVA
                            //

                            // verifica se registro inexistente
                            if (colunas[2] == "-" || colunas[3] == "-")
                            {
                                // descarto linha
                                continue;
                            }

                            // demanda ativa
                            double demanda_ativa = 0.0;

                            if (!double.TryParse(colunas[2], NumberStyles.Number, CultureInfo.CreateSpecificCulture("pt-BR"), out demanda_ativa))
                            {
                                // ERRO - coluna demanda incorreta
                                ProcessaHistoricoMedicao_WebEnergy_InsereListaResultado(resultado, PROCESSA_HIST_MED_STATUS.ERRO_LEITURA, 2);

                                // descarto linha
                                continue;
                            }


                            //
                            // DEMANDA REATIVA
                            //

                            // demanda reativa
                            double demanda_reativa = 0.0;

                            if (!double.TryParse(colunas[3], NumberStyles.Number, CultureInfo.CreateSpecificCulture("pt-BR"), out demanda_reativa))
                            {
                                // ERRO - coluna demanda incorreta
                                ProcessaHistoricoMedicao_WebEnergy_InsereListaResultado(resultado, PROCESSA_HIST_MED_STATUS.ERRO_LEITURA, 3);

                                // descarto linha
                                continue;
                            }


                            //
                            // PERIODO
                            //

                            // período
                            int periodo = 0;

                            if (!int.TryParse(colunas[6], NumberStyles.Number, CultureInfo.CreateSpecificCulture("pt-BR"), out periodo))
                            {
                                // ERRO - coluna demanda incorreta
                                ProcessaHistoricoMedicao_WebEnergy_InsereListaResultado(resultado, PROCESSA_HIST_MED_STATUS.ERRO_LEITURA, 6);

                                // descarto linha
                                continue;
                            }


                            //
                            // Atualiza tabela EN_000000
                            //

                            // registro
                            EN_Dominio registro_en = new EN_Dominio();
                            registro_en.DataHora = datahora;
                            registro_en.Ativo = demanda_ativa;
                            registro_en.Reativo = demanda_reativa;

                            switch (periodo)
                            {
                                case 1:     // fora de ponta indutivo
                                    registro_en.Periodo = PERIODO.FPI;
                                    break;

                                case 2:     // ponta 
                                    registro_en.Periodo = PERIODO.P;
                                    break;

                                case 3:     // fora de ponta capacitivo
                                    registro_en.Periodo = PERIODO.FPC;
                                    break;

                            }

                            // insere registro em EN_000000
                            EN_Metodos enMetodos = new EN_Metodos();
                            if (!enMetodos.InserirRegistro_ModificarInserir(medicao.IDCliente, medicao.IDMedicao, registro_en))
                            {
                                // ERRO - erro ao salvar registro
                                ProcessaHistoricoMedicao_WebEnergy_InsereListaResultado(resultado, PROCESSA_HIST_MED_STATUS.ERRO_SALVAR_REG);
                            }

                            // atualiza número de registros atualizados
                            resultado.NumRegistros++;
                        }
                    }

                    // insere na lista temporária
                    ProcessaHistoricoMedicao_WebEnergy_InsereListaResultado(resultado, PROCESSA_HIST_MED_STATUS.PROCESSO_OK);
                }
                else
                {
                    // ERRO - sem registros no arquivo
                    ProcessaHistoricoMedicao_WebEnergy_InsereListaResultado(resultado, PROCESSA_HIST_MED_STATUS.SEM_REG);
                }

                //
                // apaga arquivo
                //
                System.IO.File.Delete(nomeArquivoFull);
            }
            else
            {
                // ERRO - arquivo inexistente
                ProcessaHistoricoMedicao_WebEnergy_InsereListaResultado(resultado, PROCESSA_HIST_MED_STATUS.ERRO_ARQUIVO);
            }

            // retorna
            return (num_registros);
        }

        // Insere resultado temporário
        private void ProcessaHistoricoMedicao_WebEnergy_InsereListaResultado(PROCESSA_HIST_MED_RESULTADO resultado, int status, int coluna = 0)
        {
            // resultado
            PROCESSA_HIST_MED_RESULTADO tmp = new PROCESSA_HIST_MED_RESULTADO();
            tmp.NomeArquivo = resultado.NomeArquivo;
            tmp.IDCliente = resultado.IDCliente;
            tmp.NomeCliente = resultado.NomeCliente;
            tmp.IDMedicao = resultado.IDMedicao;
            tmp.NomeMedicao = resultado.NomeMedicao;
            tmp.status = status;
            tmp.linha = resultado.linha;
            tmp.coluna = coluna;
            tmp.inicio = resultado.inicio;
            tmp.fim = resultado.fim;
            tmp.NumRegistros = resultado.NumRegistros;

            // coloca na lista temporaria
            lista_processa_hist_med_webEnergy_tmp.Add(tmp);
        }
    }
}
