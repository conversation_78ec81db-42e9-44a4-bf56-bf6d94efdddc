﻿using System;
using System.IO;
using System.Web;
using System.Web.Mvc;

namespace SmartEnergy.Controllers
{
    public partial class UploadController
    {
        #region Actions

        [HttpPost]
        public virtual ActionResult UploadFileSCDE()
        {
            HttpPostedFileBase myFile = Request.Files["nomeSCDE"];
            bool isUploaded = false;
            string message = "Envio falhou";

            if (myFile != null && myFile.ContentLength != 0)
            {
                // verifica se CSV ou ZIP (arquivos SCDE)
                string extensao = Path.GetExtension(myFile.FileName).ToUpper();

                if (extensao != ".CSV" && extensao != ".ZIP")
                {
                    message = "Arquivo deve ser CSV ou ZIP";
                }
                else
                {
                    // diretorio
                    string pathForSaving = Server.MapPath("~/SCDE");

                    if (this.CreateFolderIfNeeded(pathForSaving))
                    {
                        try
                        {
                            //
                            // salva arquivo
                            //
                            myFile.SaveAs(Path.Combine(pathForSaving, myFile.FileName));
                            isUploaded = true;
                            message = "Arquivo enviado com sucesso!";
                        }
                        catch (Exception ex)
                        {
                            message = string.Format("Envio falhou: {0}", ex.Message);
                        }
                    }
                }
            }

            return Json(new { isUploaded = isUploaded, message = message }, "text/html");
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Creates the folder if needed.
        /// </summary>
        /// <param name="path">The path.</param>
        /// <returns></returns>
        private bool CreateFolderIfNeeded(string path)
        {
            bool result = true;
            if (!Directory.Exists(path))
            {
                try
                {
                    Directory.CreateDirectory(path);
                }
                catch (Exception)
                {
                    /*TODO: You must process this exception.*/
                    result = false;
                }
            }
            return result;
        }

        #endregion
    }
}