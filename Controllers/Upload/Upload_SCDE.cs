﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{

    public partial class UploadController
    {

        // GET: ProcessaSCDE
        public ActionResult ProcessaSCDE()
        {
            // tela de ajuda - processa SCDE
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // le cookies
            LeCookies_SmartEnergy();


            //
            // Status das medições
            //

            // lista de clientes
            List<int> ConfigCliList = ViewBag._ConfigCli;

            // le clientes - somente ativos
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clientesMetodos.ListarTodos(ConfigCliList, 1);

            // lista de supervisão SCDE
            List<SupervSCDEDominio> listaSupervSCDE = new List<SupervSCDEDominio>();

            // percorre clientes
            foreach (ClientesDominio cliente in listaClientes)
            {
                // menos o cliente zero
                if (cliente.IDCliente == 0)
                {
                    continue;
                }

                // le medicoes reais
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                List<MedicoesDominio> listaMedicoes = medicoesMetodos.ListarPorIDCliente(cliente.IDCliente, 1);

                // verifica se tem medição
                if (listaMedicoes != null)
                {
                    // percorre medições
                    foreach (MedicoesDominio medicao in listaMedicoes)
                    {
                        // verifica se medição de energia
                        if (medicao.IDTipoMedicao != TIPO_MEDICAO.ENERGIA)
                        {
                            continue;
                        }

                        // lê gateway
                        GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
                        GatewaysDominio gateway = gatewayMetodos.ListarPorId(medicao.IDGateway);

                        if (gateway != null)
                        {
                            // verifica se é SCDE e se tem ponto de medição
                            if (gateway.IDTipoGateway == TIPO_GATEWAY.SCDE && medicao.PontoMedicao.Length > 0)
                            {
                                // le supervisao da medicão
                                SupervMedicoesMetodos supervMedicoesMetodos = new SupervMedicoesMetodos();
                                DateTime ultimaData = supervMedicoesMetodos.UltimaDataSupervisao(medicao.IDMedicao);

                                // supervisão SCDE
                                SupervSCDEDominio supervSCDE = new SupervSCDEDominio();

                                supervSCDE.IDCliente = cliente.IDCliente;
                                supervSCDE.NomeCliente = cliente.Nome;
                                supervSCDE.FantasiaCliente = cliente.Fantasia;
                                supervSCDE.LogoCliente = cliente.Logo;
                                supervSCDE.IDGateway = gateway.IDGateway;
                                supervSCDE.IDMedicao = medicao.IDMedicao;
                                supervSCDE.NomeMedicao = medicao.Nome;
                                supervSCDE.PontoMedicao = medicao.PontoMedicao;
                                supervSCDE.UltimaSuperv = ultimaData;

                                // coloca na lista
                                listaSupervSCDE.Add(supervSCDE);
                            }
                        }
                    }
                }
            }

            // supervisão SCDE
            ViewBag.listaSupervSCDE = listaSupervSCDE;


            //
            // Resultado
            //

            ProcessaSCDEDominio processaSCDE = new ProcessaSCDEDominio();

            processaSCDE.nomeSCDE = "";

            return View(processaSCDE);
        }

        // progresso
        public class PROGRESSO_TASK
        {
            public int num_arquivos { get; set; }
            public int progresso_arquivo { get; set; }
            public string status_arquivo { get; set; }

            public int num_registros { get; set; }
            public int progresso_registro { get; set; }
            public string status_registro { get; set; }
        }

        // estruturas
        private static IDictionary<Guid, PROGRESSO_TASK> tasks = new Dictionary<Guid, PROGRESSO_TASK>();
        private static List<string> lista_arquivos = new List<string>();
        private static List<PROCESSA_SCDE_RESULTADO> lista_processa_SCDE_tmp = new List<PROCESSA_SCDE_RESULTADO>();
        private static IDictionary<Guid, List<PROCESSA_SCDE_RESULTADO>> lista_processa_SCDE = new Dictionary<Guid, List<PROCESSA_SCDE_RESULTADO>>();


        // GET: ProcessaSCDE - Processar arquivo SCDE
        public ActionResult ProcessaSCDE_IniciaProcesso(string nomeSCDE)
        {
            // taskID
            Guid taskId = Guid.NewGuid();

            // progresso
            PROGRESSO_TASK progresso = new PROGRESSO_TASK();
            progresso.num_arquivos = 0;
            progresso.num_registros = 0;
            progresso.progresso_arquivo = 0;
            progresso.progresso_registro = 0;
            progresso.status_arquivo = "[-/-] ---";
            progresso.status_registro = "[-/-] Registros";

            tasks.Add(taskId, progresso);

            // salva cookie
            CookieStore.SalvaCookie_GUID("taskID", taskId);

            // limpa listas
            lista_arquivos.Clear();
            lista_processa_SCDE_tmp.Clear();

            // task
            Task.Factory.StartNew(() =>
            {
                // verifica se arquivo existe
                if (nomeSCDE != null)
                {
                    // diretorio
                    string pathForSaving = Server.MapPath("~/SCDE");

                    // subdiretorio destino
                    string subdiretorio_destino = "";

                    // nome do arquivo completo
                    string nomearquivo = Path.Combine(pathForSaving, nomeSCDE);

                    // verifica se arquivo SCDE eh ZIP
                    string extensao = Path.GetExtension(nomeSCDE).ToUpper();

                    if (extensao == ".ZIP")
                    {
                        // subdiretorio destino
                        subdiretorio_destino = string.Format("{0}\\SCDE_{1:yyyyMMdd_hhmmss}", pathForSaving, DateTime.Now);

                        // descompacta arquivo
                        ZipFile.ExtractToDirectory(nomearquivo, subdiretorio_destino, System.Text.Encoding.GetEncoding(850));

                        // lista de arquivos
                        DirectoryInfo info = new DirectoryInfo(subdiretorio_destino);
                        FileInfo[] files = info.GetFiles().OrderBy(f => f.LastWriteTime.Year <= 1601 ? f.CreationTime : f.LastWriteTime).ToArray();

                        if( files != null )
                        {
                            foreach(FileInfo file in files)
                            {
                                // nome do arquivo completo
                                string nomearq = Path.Combine(subdiretorio_destino, file.Name);

                                // coloca na lista
                                lista_arquivos.Add(nomearq);
                            }
                        }
                    }
                    else
                    {
                        // coloca na lista
                        lista_arquivos.Add(nomearquivo);
                    }

                    // barra de progresso arquivos
                    int total = 0;
                    int atual = 0;

                    // progresso
                    progresso.num_arquivos = 0;
                    progresso.progresso_arquivo = 0;
                    progresso.num_registros = 0;
                    progresso.progresso_registro = 0;
                    progresso.status_arquivo = "[-/-] ---";
                    progresso.status_registro = "[-/-] Registros";
                    tasks[taskId] = progresso;

                    // percorre arquivos
                    if (lista_arquivos != null)
                    {
                        // barra de progresso arquivos
                        total = lista_arquivos.Count();

                        // percorre arquivos
                        foreach (string arquivo in lista_arquivos)
                        {
                            // nome do arquivo
                            string nomeArquivo = Path.GetFileName(arquivo);

                            // atualiza progresso arquivos
                            progresso.progresso_arquivo = (int)(((double)atual / (double)total) * 100);
                            progresso.status_arquivo = string.Format("[{0}/{1}] {2}", atual + 1, total, nomeArquivo);
                            progresso.status_registro = "[-/-] Registros";
                            tasks[taskId] = progresso;
                            atual++;

                            // verifica se tem arquivo
                            if (string.IsNullOrEmpty(arquivo))
                            {
                                continue;
                            }

                            // verifica se arquivo eh CSV
                            extensao = Path.GetExtension(arquivo).ToUpper();

                            if (extensao != ".CSV")
                            {
                                continue;
                            }

                            // processa CSV
                            ProcessaSCDE_Processar(arquivo, taskId, ref progresso);
                        }

                        // verifica se tem subdiretorio
                        if (!string.IsNullOrEmpty(subdiretorio_destino))
                        {
                            // apaga diretorio
                            Directory.Delete(subdiretorio_destino, true);

                            // apaga arquivo ZIP
                            System.IO.File.Delete(nomearquivo);
                        }
                    }
                }

                // coloca resultado na lista
                lista_processa_SCDE.Add(taskId, new List<PROCESSA_SCDE_RESULTADO>(lista_processa_SCDE_tmp));

                // terminou
                tasks.Remove(taskId);
            });

            // retorna status
            return Json(taskId, JsonRequestBehavior.AllowGet);
        }

        public ActionResult ProcessaSCDE_Progress(Guid id)
        {
            PROGRESSO_TASK progresso = new PROGRESSO_TASK();
            progresso.num_arquivos = 0;
            progresso.num_registros = 0;
            progresso.progresso_arquivo = 100;
            progresso.progresso_registro = 100;
            progresso.status_arquivo = "";
            progresso.status_registro = "";

            return Json(tasks.Keys.Contains(id) ? tasks[id] : progresso, JsonRequestBehavior.AllowGet);
        }

        public PartialViewResult _ProcessaSCDE_Resultado(Guid id)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // resultado
            ViewBag.processaSCDE_resultado = lista_processa_SCDE.Keys.Contains(id) ? lista_processa_SCDE[id] : null;

            return PartialView();
        }

        // Processar arquivo SCDE
        public void ProcessaSCDE_Processar(string nomeCompleto_SCDE, Guid taskId, ref PROGRESSO_TASK progresso)
        {
            // progresso
            progresso.num_registros = 0;
            progresso.progresso_registro = 0;
            progresso.status_registro = "[-/-] Registros";
            tasks[taskId] = progresso;

            // verifica se existe arquivo
            if (System.IO.File.Exists(nomeCompleto_SCDE))
            {
                // le arquivo
                string[] fileContent = System.IO.File.ReadAllLines(nomeCompleto_SCDE);

                // numero de linhas
                int numero_linhas = fileContent.Length;

                // verifica se tem minimo de linhas
                if (numero_linhas >= 5)
                {
                    // ponto / grupo
                    string ponto_grupo_atual = "";

                    // medicao
                    MedicoesDominio medicao = new MedicoesDominio();
                    medicao.IDMedicao = 0;

                    // horario ponta e posto capacitivo
                    medicao.InicioP = "17:30";
                    medicao.FimP = "20:30";
                    medicao.DomP = false; medicao.SegP = true; medicao.TerP = true; medicao.QuaP = true; medicao.QuiP = true; medicao.SexP = true; medicao.SabP = false;
                    medicao.InicioPC = "00:30";
                    medicao.FimPC = "06:30";
                    medicao.DomPC = true; medicao.SegPC = true; medicao.TerPC = true; medicao.QuaPC = true; medicao.QuiPC = true; medicao.SexPC = true; medicao.SabPC = true;

                    // lista de medições processadas
                    List<PROCESSA_SCDE_MEDICOES> listaMedicoesProcessadas = new List<PROCESSA_SCDE_MEDICOES>();

                    // barra de progresso registros
                    progresso.num_registros = numero_linhas - 5;
                    progresso.progresso_registro = 0;
                    progresso.status_registro = string.Format("[-/{0}] Registros", progresso.num_registros + 1);
                    tasks[taskId] = progresso;

                    int total = progresso.num_registros;
                    int atual = 0;

                    // le registros
                    for (int conta = 4; conta < fileContent.Length; conta++)
                    {
                        // atualiza progresso registro
                        progresso.progresso_registro = (int)(((double)atual / (double)total) * 100);
                        progresso.status_registro = string.Format("[{0}/{1}] Registros", atual + 1, total + 1);
                        tasks[taskId] = progresso;
                        atual++;

                        // linha
                        int linha = conta - 3;

                        // separa colunas
                        string[] colunas = fileContent[conta].Split(';');

                        // verifica se tem numero de colunas corretas
                        if( colunas.Length < 7 )
                        {
                            // ERRO - numero errado de colunas
                            ProcessaSCDE_InsereListaResultado(nomeCompleto_SCDE, PROCESSA_SCDE_STATUS.NUM_COL_ERRADO, linha);

                            break;
                        }
                        else
                        {
                            //
                            // Le colunas
                            //

                            // inicia resultado
                            PROCESSA_SCDE_RESULTADO resultado = new PROCESSA_SCDE_RESULTADO();
                            resultado.NomeArquivo = Path.GetFileName(nomeCompleto_SCDE);
                            resultado.status = PROCESSA_SCDE_STATUS.SEM_STATUS;
                            resultado.agente = "";
                            resultado.ponto_grupo = ponto_grupo_atual;
                            resultado.inicio = new DateTime(2000, 1, 1, 0, 0, 0);
                            resultado.fim = new DateTime(2000, 1, 1, 0, 0, 0);
                            resultado.IDMedicao = 0;
                            resultado.NumRegistros = 0;
                            resultado.linha_inicio = 0;
                            resultado.linha_fim = 0;

                            // 
                            // AGENTE
                            //

                            // agente
                            string agente = colunas[0].Replace("\"", string.Empty);

                            // copia para o resultado
                            resultado.agente = agente;

                            // 
                            // PONTO / GRUPO
                            //

                            // ponto / grupo
                            string ponto_grupo = colunas[1].Replace("\"", string.Empty);

                            // caso ponto grupo estiver vazio, descarto linha
                            if( string.IsNullOrEmpty(ponto_grupo) )
                            {
                                // ERRO - coluna ponto grupo vazio
                                ProcessaSCDE_InsereListaResultado(nomeCompleto_SCDE, PROCESSA_SCDE_STATUS.PONTO_GRUPO_VAZIO, linha);

                                // descarto linha
                                continue;
                            }

                            // copia para o resultado
                            resultado.ponto_grupo = ponto_grupo;

                            //
                            // DATA HORA
                            //

                            // data hora
                            string data = colunas[2];
                            int hora = Convert.ToInt32(colunas[3]);
                            string dh = "";
                            DateTime datahora = new DateTime();

                            // verifica se 24h
                            if (hora == 24)
                            {
                                // monta datahora
                                dh = string.Format("{0} 23:00:00", data);
                            }
                            else
                            {
                                // monta datahora
                                dh = string.Format("{0} {1:00}:00:00", data, hora);
                            }

                            if (DateTime.TryParseExact(dh, "dd/MM/yyyy HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out datahora))
                            {
                                // verifica se 24h
                                if (hora == 24)
                                {
                                    // adiciona 1 hora
                                    datahora = datahora.AddHours(1);
                                }
                            }
                            else
                            {
                                // ERRO - coluna data hora incorreta
                                resultado.status = PROCESSA_SCDE_STATUS.DATA_HORA_INCORRETA;
                                resultado.linha_inicio = linha;

                                ProcessaSCDE_InsereListaResultado(resultado);

                                // descarto linha
                                continue;
                            }

                            //
                            // CONSUMO ATIVO
                            //

                            // consumo ativo
                            double consumo = 0.0;

                            if (!double.TryParse(colunas[4], NumberStyles.Number, CultureInfo.CreateSpecificCulture("pt-BR"), out consumo))
                            {
                                // ERRO - coluna consumo incorreto
                                resultado.status = PROCESSA_SCDE_STATUS.CONSUMO_INCORRETO;
                                resultado.linha_inicio = linha;

                                ProcessaSCDE_InsereListaResultado(resultado);

                                // caso consumo incorreto, descarto linha
                                continue;
                            }

                            //
                            // Atualizo lista
                            //

                            // verifica se mudou de ponto_grupo
                            if (ponto_grupo_atual != ponto_grupo)
                            {
                                // atualiza ponto atual
                                ponto_grupo_atual = ponto_grupo;

                                // procura medicao
                                MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                                medicao = medicaoMetodos.BuscarPontoGrupo(ponto_grupo);

                                // verifica se achou medicao
                                if (medicao.IDMedicao > 0)
                                {
                                    // coloca na lista OK
                                    resultado.status = PROCESSA_SCDE_STATUS.PROCESSO_OK;
                                    resultado.IDMedicao = medicao.IDMedicao;
                                    resultado.NumRegistros = 1;
                                }
                                else
                                {
                                    // ERRO - nao encontrou medicao
                                    resultado.status = PROCESSA_SCDE_STATUS.MED_INEX;
                                }

                                // inicio data e hora
                                resultado.inicio = datahora;
                                resultado.fim = datahora;

                                resultado.linha_inicio = linha;
                                resultado.linha_fim = linha;

                                // insere resultado na lista
                                ProcessaSCDE_InsereListaResultado(resultado);
                            }
                            else 
                            {
                                //
                                // Ainda no mesmo ponto grupo, atualizo as informacoes
                                //

                                // inicialmente nao encontrou medicao
                                resultado.status = PROCESSA_SCDE_STATUS.MED_INEX;

                                // procuro na lista o ponto grupo
                                if (lista_processa_SCDE_tmp != null)
                                {
                                    for (int i = (lista_processa_SCDE_tmp.Count - 1); i >= 0; i--)
                                    {
                                        // verifica se eh o ponto grupo atual e com status OK
                                        if (lista_processa_SCDE_tmp[i].ponto_grupo == ponto_grupo_atual && lista_processa_SCDE_tmp[i].status == PROCESSA_SCDE_STATUS.PROCESSO_OK)
                                        {
                                            // atualizo
                                            lista_processa_SCDE_tmp[i].fim = datahora;
                                            lista_processa_SCDE_tmp[i].NumRegistros++;
                                            lista_processa_SCDE_tmp[i].linha_fim = linha;

                                            // IDMedicao
                                            medicao.IDMedicao = lista_processa_SCDE_tmp[i].IDMedicao;

                                            // status OK
                                            resultado.status = PROCESSA_SCDE_STATUS.PROCESSO_OK;

                                            // termino loop de busca do ponto grupo
                                            break;
                                        }
                                    }
                                }
                            }

                            //
                            // Insiro registro na medicao
                            //
                            if (resultado.status == PROCESSA_SCDE_STATUS.PROCESSO_OK)
                            {
                                //
                                // Atualiza tabela EN_000000
                                //
                                if( !ProcessaSCDE_InsereRegistros_Demanda(medicao, datahora, consumo) )
                                {
                                    // ERRO - erro ao salvar registro
                                    resultado.status = PROCESSA_SCDE_STATUS.ERRO_SALVAR_REG;
                                    resultado.linha_inicio = linha;

                                    ProcessaSCDE_InsereListaResultado(resultado);
                                }
                                else
                                {
                                    // processou OK, atualizo lista de medições
                                    bool achou_medicao = false;

                                    // verifica se já existe medição na lista
                                    foreach (PROCESSA_SCDE_MEDICOES medicaoProcessada in listaMedicoesProcessadas)
                                    {
                                        // verifica medição
                                        if (medicaoProcessada.IDMedicao == medicao.IDMedicao)
                                        {
                                            // achou, atualiza e sai
                                            medicaoProcessada.DataHora = datahora;

                                            // menor registro de DataHora
                                            if (datahora < medicaoProcessada.MinRecordDataTime)
                                            {
                                                // data hora inicial
                                                medicaoProcessada.MinRecordDataTime = datahora;
                                            }

                                            // maior registro de DataHora
                                            if (datahora > medicaoProcessada.MaxRecordDataTime)
                                            {
                                                // data hora inicial
                                                medicaoProcessada.MaxRecordDataTime = datahora;
                                            }

                                            achou_medicao = true;
                                            break;
                                        }
                                    }

                                    // verifica se não achou
                                    if (!achou_medicao)
                                    {
                                        // insere na lista
                                        PROCESSA_SCDE_MEDICOES medicaoProcessada = new PROCESSA_SCDE_MEDICOES();

                                        medicaoProcessada.IDCliente = medicao.IDCliente;
                                        medicaoProcessada.IDMedicao = medicao.IDMedicao;
                                        medicaoProcessada.IDGateway = medicao.IDGateway;
                                        medicaoProcessada.DataHora = datahora;
                                        medicaoProcessada.MinRecordDataTime = datahora;
                                        medicaoProcessada.MaxRecordDataTime = datahora;

                                        listaMedicoesProcessadas.Add(medicaoProcessada);
                                    }
                                }
                            }
                        }
                    }

                    //
                    // Atualiza tabelas EN_K | SUP_EN | SUP | Mensagem de Gerenciamento 
                    //

                    // percorre medições processadas
                    foreach (PROCESSA_SCDE_MEDICOES medicaoProcessada in listaMedicoesProcessadas)
                    {
                        //
                        // Atualiza tabela EN_K_000000
                        //
                        // atualiza contante sempre em 1
                        EN_K_Metodos enKMetodos = new EN_K_Metodos();
                        enKMetodos.Atualiza(medicaoProcessada.IDCliente, medicaoProcessada.IDMedicao, medicaoProcessada.DataHora);


                        //
                        // Atualiza tabela SUP_EN_000000
                        //
                        SUP_EN_Metodos supENMetodos = new SUP_EN_Metodos();
                        supENMetodos.Atualiza(medicaoProcessada.IDCliente, medicaoProcessada.IDMedicao, medicaoProcessada.DataHora);


                        //
                        // Atualiza tabela SUP_000000
                        //
                        SUP_Metodos supMetodos = new SUP_Metodos();
                        supMetodos.Atualiza(medicaoProcessada.IDCliente, medicaoProcessada.IDGateway, medicaoProcessada.DataHora);

                        //
                        // Insere Mensagem de Gerenciamento
                        //

                        //
                        // MENSAGENS DE GERENCIAMENTO
                        //

                        MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                        MedicoesDominio med = medicaoMetodos.ListarPorId(medicaoProcessada.IDMedicao);

                        if (med != null)
                        {
                            // verifica se tem gerenciamento configurado
                            if (med.Dem_P_pcH != 0.0 || med.Dem_F_pcH != 0.0 || med.Dem_P_pcL != 0.0 || med.Dem_F_pcL != 0.0 || med.FP_LimInd != 0.0 || med.FP_LimCap != 0.0 ||
                                med.DiasNCnPj != 0 || med.CnPjP_pcH != 0.0 || med.CnPjF_pcH != 0.0 || med.CnPjP_pcL != 0.0 || med.CnPjF_pcL != 0.0 || med.Funcionamento_DemMin > 0.0)
                            {
                                // insere na fila
                                MsgGerenQueueMetodos msgGerenMetodos = new MsgGerenQueueMetodos();
                                MsgGerenQueueDominio msgGeren = new MsgGerenQueueDominio();

                                msgGeren.IDMsgGeren = 0;
                                msgGeren.IDCliente = med.IDCliente;
                                msgGeren.IDGateway = med.IDGateway;
                                msgGeren.IDMedicao = med.IDMedicao;
                                msgGeren.TipoMsgGeren = TIPO_MSGGEREN.MedEner;

                                // o registro com Hora 01:00 contempla os 4 registros
                                // registro 1 = 00:15
                                // registro 2 = 00:30
                                // registro 3 = 00:45
                                // registro 4 = 01:00
                                msgGeren.DataIni = medicaoProcessada.MinRecordDataTime.AddMinutes(-45);
                                msgGeren.DataFim = medicaoProcessada.MaxRecordDataTime;

                                msgGerenMetodos.Inserir(msgGeren);
                            }
                        }
                    }
                }
                else
                {
                    // ERRO - sem registros no arquivo
                    ProcessaSCDE_InsereListaResultado(nomeCompleto_SCDE, PROCESSA_SCDE_STATUS.SEM_REG, 0);
                }

                //
                // apaga arquivo
                //
                System.IO.File.Delete(nomeCompleto_SCDE);
            }
            else
            {
                // arquivo inexistente
                ProcessaSCDE_InsereListaResultado(nomeCompleto_SCDE, PROCESSA_SCDE_STATUS.ARQ_INEX, 0);
            }

            // retorna
            return;
        }

        // Processar arquivo SCDE
        private void ProcessaSCDE_InsereListaResultado(string NomeArquivo, int status, int linha)
        {
            // resultado
            PROCESSA_SCDE_RESULTADO tmp = new PROCESSA_SCDE_RESULTADO();
            tmp.NomeArquivo = Path.GetFileName(NomeArquivo);
            tmp.status = status;
            
            tmp.agente = "";
            tmp.ponto_grupo= "";
            tmp.inicio = new DateTime(2000,1,1,0,0,0);
            tmp.fim = new DateTime(2000,1,1,0,0,0);
            tmp.IDMedicao = 0;
            tmp.NumRegistros = 0;
            tmp.linha_inicio = linha;
            tmp.linha_fim = linha;

            // coloca na lista temporaria
            lista_processa_SCDE_tmp.Add(tmp);
        }

        // Processar arquivo SCDE
        private void ProcessaSCDE_InsereListaResultado(PROCESSA_SCDE_RESULTADO resultado)
        {
            // resultado
            PROCESSA_SCDE_RESULTADO tmp = new PROCESSA_SCDE_RESULTADO();
            tmp.NomeArquivo = resultado.NomeArquivo;
            tmp.status = resultado.status;

            tmp.agente = resultado.agente;
            tmp.ponto_grupo = resultado.ponto_grupo;
            tmp.inicio = resultado.inicio;
            tmp.fim = resultado.fim;
            tmp.IDMedicao = resultado.IDMedicao;
            tmp.NumRegistros = resultado.NumRegistros;
            tmp.linha_inicio = resultado.linha_inicio;
            tmp.linha_fim = resultado.linha_fim;

            // coloca na lista temporaria
            lista_processa_SCDE_tmp.Add(tmp);
        }

        // Insere registros de demanda
        public bool ProcessaSCDE_InsereRegistros_Demanda(MedicoesDominio medicao, DateTime datahora, double consumo)
        {
            // a datahora reflete o fechamento do consumo (hora cheia), portanto as demandas devem começar antes, exemplo:
            // datahora = 01:00 , teremos os registros de demanda:
            // registro 1 = 00:15
            // registro 2 = 00:30
            // registro 3 = 00:45
            // registro 4 = 01:00
            DateTime dh = datahora.AddMinutes(-45);

            // insere 4 registros de demanda (1 hora)
            for (int i = 0; i < 4; i++)
            {
                // registro
                EN_Dominio registro_en = new EN_Dominio();

                // data hora
                registro_en.DataHora = dh.AddMinutes(i * 15);

                // demanda 
                registro_en.Ativo = consumo;
                registro_en.Reativo = 0.0;

                // inicialmente fora de ponta indutivo
                registro_en.Periodo = PERIODO.FPI;

                // le horarios ponta e posto capacitivo
                DateTime dh_p_ini = new DateTime(2000,1,1,0,0,0);
                DateTime dh_p_fim = new DateTime(2000,1,1,0,0,0);
                DateTime dh_fpc_ini = new DateTime(2000,1,1,0,0,0);
                DateTime dh_fpc_fim = new DateTime(2000,1,1,0,0,0);
                DateTime dh_tmp = new DateTime(2000,1,1,0,0,0);

                if (DateTime.TryParseExact(medicao.InicioP, "HH:mm", CultureInfo.InvariantCulture, DateTimeStyles.None, out dh_tmp))
                {
                    // inicio horario de ponta
                    dh_p_ini = new DateTime(datahora.Year, datahora.Month, datahora.Day, dh_tmp.Hour, dh_tmp.Minute, 0);
                }

                if (DateTime.TryParseExact(medicao.FimP, "HH:mm", CultureInfo.InvariantCulture, DateTimeStyles.None, out dh_tmp))
                {
                    // fim horario de ponta
                    dh_p_fim = new DateTime(datahora.Year, datahora.Month, datahora.Day, dh_tmp.Hour, dh_tmp.Minute, 0);
                }

                if (DateTime.TryParseExact(medicao.InicioPC, "HH:mm", CultureInfo.InvariantCulture, DateTimeStyles.None, out dh_tmp))
                {
                    // inicio horario posto capacitivo
                    dh_fpc_ini = new DateTime(datahora.Year, datahora.Month, datahora.Day, dh_tmp.Hour, dh_tmp.Minute, 0);
                }

                if (DateTime.TryParseExact(medicao.FimPC, "HH:mm", CultureInfo.InvariantCulture, DateTimeStyles.None, out dh_tmp))
                {
                    // fim horario posto capacitivo
                    dh_fpc_fim = new DateTime(datahora.Year, datahora.Month, datahora.Day, dh_tmp.Hour, dh_tmp.Minute, 0);
                }

                // dia da semana
                DayOfWeek dia_semana = registro_en.DataHora.DayOfWeek;

                //
                // PONTA
                //
                bool dia_habilitado = false;

                // verifica se esta no horario de ponta
                if( registro_en.DataHora > dh_p_ini && registro_en.DataHora <= dh_p_fim )
                {
                    // verifica se dia da semana habilitada para ponta
                    switch( dia_semana )
                    {
                        case DayOfWeek.Sunday:
                            dia_habilitado = medicao.DomP;
                            break;

                        case DayOfWeek.Monday:
                            dia_habilitado = medicao.SegP;
                            break;

                        case DayOfWeek.Tuesday:
                            dia_habilitado = medicao.TerP;
                            break;

                        case DayOfWeek.Wednesday:
                            dia_habilitado = medicao.QuaP;
                            break;

                        case DayOfWeek.Thursday:
                            dia_habilitado = medicao.QuiP;
                            break;

                        case DayOfWeek.Friday:
                            dia_habilitado = medicao.SexP;
                            break;

                        case DayOfWeek.Saturday:
                            dia_habilitado = medicao.SabP;
                            break;
                    }
                }

                // verifica se dentro do horario ponta e dia da semana habilitada para ponta
                if (dia_habilitado)
                {
                    // ponta
                    registro_en.Periodo = PERIODO.P;
                }

                //
                // FORA DE PONTA CAPACITIVO
                //
                dia_habilitado = false;

                // verifica se horarios invertidos
                if( dh_fpc_fim < dh_fpc_ini )
                {
                    // verifica se esta no horario posto capacitivo
                    if (registro_en.DataHora <= dh_fpc_fim || registro_en.DataHora > dh_fpc_ini)
                    {
                        // esta dentro do horario posto capacitivo
                        dia_habilitado = true;
                    }
                }
                else
                {
                    // verifica se esta no horario posto capacitivo
                    if (registro_en.DataHora > dh_fpc_ini && registro_en.DataHora <= dh_fpc_fim)
                    {
                        // esta dentro do horario posto capacitivo
                        dia_habilitado = true;
                    }
                }

                // verifica se horario esta no posto capacitivo
                if (dia_habilitado)
                {
                    dia_habilitado = false;

                    // verifica se dia da semana habilitada para posto capacitivo
                    switch (dia_semana)
                    {
                        case DayOfWeek.Sunday:
                            dia_habilitado = medicao.DomPC;
                            break;

                        case DayOfWeek.Monday:
                            dia_habilitado = medicao.SegPC;
                            break;

                        case DayOfWeek.Tuesday:
                            dia_habilitado = medicao.TerPC;
                            break;

                        case DayOfWeek.Wednesday:
                            dia_habilitado = medicao.QuaPC;
                            break;

                        case DayOfWeek.Thursday:
                            dia_habilitado = medicao.QuiPC;
                            break;

                        case DayOfWeek.Friday:
                            dia_habilitado = medicao.SexPC;
                            break;

                        case DayOfWeek.Saturday:
                            dia_habilitado = medicao.SabPC;
                            break;
                    }
                }

                // verifica se dia da semana habilitada para posto capacitivo
                if (dia_habilitado)
                {
                    // fora de ponta capacitivo
                    registro_en.Periodo = PERIODO.FPC;
                }

                // insere registro em EN_000000
                EN_Metodos enMetodos = new EN_Metodos();
                if (!enMetodos.InserirRegistro_ModificarInserir(medicao.IDCliente, medicao.IDMedicao, registro_en))
                {
                    // erro
                    return (false);
                }
            }

            // ok
            return (true);
        }
    }
}
