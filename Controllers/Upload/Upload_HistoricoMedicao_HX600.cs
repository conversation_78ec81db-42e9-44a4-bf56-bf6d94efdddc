﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.OleDb;
using System.Globalization;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{

    public partial class UploadController
    {

        // GET: Importar Histórico de Medição
        public ActionResult Upload_HistoricoMedicao_HX600(int IDCliente, int IDMedicao)
        {
            // le configuracao da medicao
            MedicoesMetodos medicoesMetodos_conf = new MedicoesMetodos();
            MedicoesDominio medicao_conf = medicoesMetodos_conf.ListarPorId(IDMedicao);

            // salva cookie 
            CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", medicao_conf.IDTipoMedicao);
            CookieStore.SalvaCookie_Int("_IDGateway", medicao_conf.IDGateway);
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Upload_HX600");

            // tela de ajuda - upload
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // tipo pagina

            // le cookies
            LeCookies_SmartEnergy();

            // leio configuracoes
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cliente = clienteMetodos.ListarPorId(IDCliente);
            ViewBag.NomeCliente = cliente.Fantasia;

            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);
            ViewBag.NomeMedicao = medicao.Nome;

            return View();
        }


        // progresso
        public class PROGRESSO_TASK_HIST_MED_HX600
        {
            public int num_arquivos { get; set; }
            public int progresso_arquivo { get; set; }
            public string status_arquivo { get; set; }

            public int num_registros { get; set; }
            public int progresso_registro { get; set; }
            public string status_registro { get; set; }
        }

        // estruturas
        private static IDictionary<Guid, PROGRESSO_TASK_HIST_MED_HX600> tasks_hist_med_hx600 = new Dictionary<Guid, PROGRESSO_TASK_HIST_MED_HX600>();
        private static List<string> lista_arquivos_hx600 = new List<string>();
        private static List<PROCESSA_HIST_MED_RESULTADO> lista_processa_hist_med_hx600_tmp = new List<PROCESSA_HIST_MED_RESULTADO>();
        private static IDictionary<Guid, List<PROCESSA_HIST_MED_RESULTADO>> lista_processa_hist_med_hx600 = new Dictionary<Guid, List<PROCESSA_HIST_MED_RESULTADO>>();


        // GET: Processa Histórico de Medição
        public ActionResult ProcessaHistoricoMedicao_HX600_IniciaProcesso(string nomeHistorico)
        {
            // taskID
            Guid taskId = Guid.NewGuid();

            // progresso
            PROGRESSO_TASK_HIST_MED_HX600 progresso = new PROGRESSO_TASK_HIST_MED_HX600();
            progresso.num_arquivos = 0;
            progresso.num_registros = 0;
            progresso.progresso_arquivo = 0;
            progresso.progresso_registro = 0;
            progresso.status_arquivo = "[-/-] ---";
            progresso.status_registro = "[-/-] Registros";

            tasks_hist_med_hx600.Add(taskId, progresso);

            // salva cookie
            CookieStore.SalvaCookie_GUID("taskID", taskId);

            // le cookies
            LeCookies_SmartEnergy();

            // IDMedicao
            int IDMedicao = ViewBag._IDMedicao;

            // lê configuracao da medição
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);

            // cliente da medição
            int IDCliente = medicao.IDCliente;

            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cliente = clienteMetodos.ListarPorId(IDCliente);

            // limpa listas
            lista_arquivos_hx600.Clear();
            lista_processa_hist_med_hx600_tmp.Clear();

            // task
            Task.Factory.StartNew(() =>
            {
                // verifica se arquivo existe
                if (nomeHistorico != null)
                {
                    // diretorio
                    string pathForSaving = Server.MapPath("~/Temp");

                    // subdiretorio destino
                    string subdiretorio_destino = "";

                    // nome do arquivo completo
                    string nomeArquivoFull = Path.Combine(pathForSaving, nomeHistorico);

                    // verifica se arquivo MDB eh ZIP
                    string extensao = Path.GetExtension(nomeHistorico).ToUpper();

                    if (extensao == ".ZIP")
                    {
                        // subdiretorio destino
                        subdiretorio_destino = string.Format("{0}\\HX600_{1:yyyyMMdd_hhmmss}", pathForSaving, DateTime.Now);

                        // descompacta arquivo
                        ZipFile.ExtractToDirectory(nomeArquivoFull, subdiretorio_destino, System.Text.Encoding.GetEncoding(850));

                        // lista de arquivos
                        DirectoryInfo info = new DirectoryInfo(subdiretorio_destino);
                        FileInfo[] files = info.GetFiles().OrderBy(f => f.LastWriteTime.Year <= 1601 ? f.CreationTime : f.LastWriteTime).ToArray();

                        if (files != null)
                        {
                            foreach (FileInfo file in files)
                            {
                                // nome do arquivo completo
                                string nomearq = Path.Combine(subdiretorio_destino, file.Name);

                                // coloca na lista
                                lista_arquivos_hx600.Add(nomearq);
                            }
                        }
                    }
                    else
                    {
                        // coloca na lista
                        lista_arquivos_hx600.Add(nomeArquivoFull);
                    }

                    // barra de progresso arquivos
                    int total = 0;
                    int atual = 0;

                    // progresso
                    progresso.num_arquivos = 0;
                    progresso.progresso_arquivo = 0;
                    progresso.num_registros = 0;
                    progresso.progresso_registro = 0;
                    progresso.status_arquivo = "[-/-] ---";
                    progresso.status_registro = "[-/-] Registros";

                    tasks_hist_med_hx600[taskId] = progresso;

                    // percorre arquivos
                    if (lista_arquivos_hx600 != null)
                    {
                        // barra de progresso arquivos
                        total = lista_arquivos_hx600.Count();

                        // percorre arquivos
                        foreach (string arquivo in lista_arquivos_hx600)
                        {
                            // nome do arquivo
                            string nomeArquivo = Path.GetFileName(arquivo).ToUpper();

                            // atualiza progresso arquivos
                            progresso.progresso_arquivo = (int)(((double)atual / (double)total) * 100);
                            progresso.status_arquivo = string.Format("[{0}/{1}] {2}", atual + 1, total, nomeArquivo);
                            progresso.status_registro = "[-/-] Registros";

                            tasks_hist_med_hx600[taskId] = progresso;
                            atual++;

                            // verifica se tem arquivo
                            if (string.IsNullOrEmpty(arquivo))
                            {
                                continue;
                            }

                            // verifica se arquivo eh MDB
                            extensao = Path.GetExtension(arquivo).ToUpper();

                            if (extensao != ".MDB")
                            {
                                continue;
                            }

                            // recupera número da medição HX600, mês e ano
                            Regex regex = new Regex("^([dD])([0-9]{3})([0-9]{2})([0-9]{2})([.mMdDbB]{4})$");
                            Match match = regex.Match(nomeArquivo);
                            string medicao_HX600 = match.Groups[2].Value;
                            string mes_str = match.Groups[3].Value;
                            string ano_str = match.Groups[4].Value;

                            int mes_int = Convert.ToInt32(mes_str);
                            int ano_int = Convert.ToInt32(ano_str) + 2000;

                            if (mes_int < 1 || mes_int > 12 || ano_int < 2000 || ano_int > 2030)
                            {
                                // erro
                                PROCESSA_HIST_MED_RESULTADO resultado = new PROCESSA_HIST_MED_RESULTADO();
                                resultado.NomeArquivo = arquivo;
                                resultado.IDCliente = cliente.IDCliente;
                                resultado.NomeCliente = cliente.Fantasia;
                                resultado.IDMedicao = medicao.IDMedicao;
                                resultado.NomeMedicao = medicao.Nome;
                                resultado.status = PROCESSA_HIST_MED_STATUS.ERRO_NOME_ARQUIVO;
                                resultado.linha = 0;
                                resultado.inicio = new DateTime(2000, 1, 1, 0, 0, 0);
                                resultado.fim = new DateTime(2000, 1, 1, 0, 0, 0);
                                resultado.NumRegistros = 0;

                                // insere na lista temporária
                                ProcessaHistoricoMedicao_HX600_InsereListaResultado(resultado);
                            }
                            else
                            {
                                // processa histórico
                                if (ProcessaHistoricoMedicao_HX600_Processar(arquivo, cliente, medicao, mes_int, ano_int, taskId, ref progresso) < 0)
                                {
                                    // erro
                                    PROCESSA_HIST_MED_RESULTADO resultado = new PROCESSA_HIST_MED_RESULTADO();
                                    resultado.NomeArquivo = arquivo;
                                    resultado.IDCliente = cliente.IDCliente;
                                    resultado.NomeCliente = cliente.Fantasia;
                                    resultado.IDMedicao = medicao.IDMedicao;
                                    resultado.NomeMedicao = medicao.Nome;
                                    resultado.status = PROCESSA_HIST_MED_STATUS.ERRO_ARQUIVO;
                                    resultado.linha = 0;
                                    resultado.inicio = new DateTime(2000, 1, 1, 0, 0, 0);
                                    resultado.fim = new DateTime(2000, 1, 1, 0, 0, 0);
                                    resultado.NumRegistros = 0;

                                    // insere na lista temporária
                                    ProcessaHistoricoMedicao_HX600_InsereListaResultado(resultado);
                                }
                            }
                        }

                        // verifica se tem subdiretorio
                        if (!string.IsNullOrEmpty(subdiretorio_destino))
                        {
                            // apaga diretorio
                            Directory.Delete(subdiretorio_destino, true);

                            // apaga arquivo ZIP
                            System.IO.File.Delete(nomeArquivoFull);
                        }
                    }
                }

                // coloca resultado na lista
                lista_processa_hist_med_hx600.Add(taskId, new List<PROCESSA_HIST_MED_RESULTADO>(lista_processa_hist_med_hx600_tmp));

                // terminou
                tasks_hist_med_hx600.Remove(taskId);
            });

            // retorna status
            return Json(taskId, JsonRequestBehavior.AllowGet);
        }

        public ActionResult ProcessaHistoricoMedicao_HX600_Progress(Guid id)
        {
            PROGRESSO_TASK_HIST_MED_HX600 progresso = new PROGRESSO_TASK_HIST_MED_HX600();
            progresso.num_arquivos = 0;
            progresso.num_registros = 0;
            progresso.progresso_arquivo = 100;
            progresso.progresso_registro = 100;
            progresso.status_arquivo = "";
            progresso.status_registro = "";

            return Json(tasks_hist_med_hx600.Keys.Contains(id) ? tasks_hist_med_hx600[id] : progresso, JsonRequestBehavior.AllowGet);
        }

        public PartialViewResult _ProcessaHistoricoMedicao_HX600_Resultado(Guid id)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // resultado
            ViewBag.processaHistoricoMedicao_resultado = lista_processa_hist_med_hx600.Keys.Contains(id) ? lista_processa_hist_med_hx600[id] : null;

            return PartialView();
        }

        public int ProcessaHistoricoMedicao_HX600_Processar(string nomeArquivoFull, ClientesDominio cliente, MedicoesDominio medicao, int mes_int, int ano_int, Guid taskId, ref PROGRESSO_TASK_HIST_MED_HX600 progresso)
        {
            //
            // Número de linhas da Tabela1 do MDB
            //
            int num_registros = GetNumberOfRows_HX600(nomeArquivoFull, "Tabela1") - 1;

            if (num_registros < 0)
            {
                // erro
                return (-1);
            }

            // conexao
            OleDbConnection _olecon;
            OleDbCommand _oleCmd;

            // MDB
            String _StringConexao = String.Format(@"Provider=Microsoft.JET.OLEDB.4.0;Data Source={0}", nomeArquivoFull);

            _olecon = new OleDbConnection(_StringConexao);
            _oleCmd = new OleDbCommand();

            // linha convertida
            int linha = -1;

            // le Excel
            try
            {
                // conecta
                _olecon.Open();
                _oleCmd.Connection = _olecon;
                _oleCmd.CommandType = CommandType.Text;

                // le Tabela1 do MDB
                _oleCmd.CommandText = "SELECT * FROM [Tabela1]";
                OleDbDataReader reader = _oleCmd.ExecuteReader();

                // inicia linha
                linha = 0;

                // barra de progresso registros
                int total = num_registros;
                int atual = 0;

                // progresso
                progresso.num_registros = 0;
                progresso.progresso_registro = 0;
                progresso.status_registro = "[-/-] Registros";
                tasks_hist_med_hx600[taskId] = progresso;

                // inicia resultado
                PROCESSA_HIST_MED_RESULTADO resultado = new PROCESSA_HIST_MED_RESULTADO();
                resultado.NomeArquivo = Path.GetFileName(nomeArquivoFull);
                resultado.IDCliente = cliente.IDCliente;
                resultado.NomeCliente = cliente.Fantasia;
                resultado.IDMedicao = medicao.IDMedicao;
                resultado.NomeMedicao = medicao.Nome;
                resultado.status = PROCESSA_HIST_MED_STATUS.SEM_STATUS;
                resultado.linha = linha + 1;
                resultado.inicio = new DateTime(2000, 1, 1, 0, 0, 0);
                resultado.fim = new DateTime(2000, 1, 1, 0, 0, 0);
                resultado.NumRegistros = 0;

                // abre contexto do banco de dados
                EN_Metodos enMetodos = new EN_Metodos();

                using (Funcoes_SQL contexto = new Funcoes_SQL(cliente.IDCliente))
                {
                    // lê planilha
                    while (reader.Read())
                    {
                        // atualiza progresso medicoes
                        progresso.progresso_registro = (int)(((double)atual / (double)total) * 100);
                        progresso.status_registro = string.Format("[{0}/{1}] Registros", atual + 1, total);
                        tasks_hist_med_hx600[taskId] = progresso;
                        atual++;

                        // resultado linha
                        resultado.linha = linha + 1;

                        // data hora auxiliar
                        DateTime data_aux = new DateTime(2000, 1, 1, 0, 0, 0);


                        // verifica se energia elétrica
                        if (medicao.IDTipoMedicao == TIPOS_MEDICAO.energia)
                        {
                            // histórico da medição
                            EN_Dominio registro_en = new EN_Dominio();

                            // DataHora (campo DIA_INT e HORA_INT)

                            // dia
                            string dia_int_str = reader["DIA_INT"].ToString();

                            int dia_int = Convert.ToInt32(dia_int_str);

                            if (dia_int < 1 || dia_int > 31)
                            {
                                // ignora registro pois o bando de dados HX600 tem dia inválidos (dia 32)
                                continue;
                            }

                            // hora
                            string hora_int_str = reader["HORA_INT"].ToString();

                            // data e hora
                            string data_hora_str = string.Format("{0:00}/{1:00}/{2:0000} {3}", dia_int, mes_int, ano_int, hora_int_str);

                            // verifica se data/hora válida
                            if (DateTime.TryParse(data_hora_str, new CultureInfo("pt-BR"), DateTimeStyles.None, out data_aux))
                            {
                                // DataHora
                                registro_en.DataHora = data_aux;

                                // Periodo (campo HORARIO)
                                string periodo_str = reader["HORARIO"].ToString();
                                registro_en.Periodo = PERIODO.P;

                                if (periodo_str == "F")
                                {
                                    registro_en.Periodo = PERIODO.FPI;
                                }

                                // Ativo (campo DEMANDA)
                                string ativo_str = reader["DEMANDA"].ToString();
                                registro_en.Ativo = Convert.ToDouble(ativo_str);

                                // Reativo (campo DEMREATIVA)
                                string reativo_str = reader["DEMREATIVA"].ToString();
                                registro_en.Reativo = Convert.ToDouble(reativo_str);

                                // adiciona registro
                                if (!enMetodos.InserirRegistro_InserirModificar_Contexto(medicao.IDCliente, medicao.IDMedicao, registro_en, contexto))
                                {
                                    resultado.status = PROCESSA_HIST_MED_STATUS.ERRO_SALVAR_REG;
                                    goto FINAL;
                                }
                            }
                            else
                            {
                                // erro data
                                data_aux = new DateTime(2000, 1, 1, 0, 0, 0);
                            }
                        }

                        if (data_aux.Year != 2000)
                        {
                            // data inicial
                            if (resultado.inicio.Year == 2000)
                            {
                                resultado.inicio = data_aux;
                            }

                            // data final
                            resultado.fim = data_aux;
                        }

                        // processado ok
                        resultado.status = PROCESSA_HIST_MED_STATUS.PROCESSO_OK;

                        // atualiza número de registros atualizados
                        resultado.NumRegistros++;

                        // proxima linha
                        linha++;
                    }
                }

                FINAL:

                // insere na lista temporária
                ProcessaHistoricoMedicao_HX600_InsereListaResultado(resultado);

                // fecha
                reader.Close();

                // termina
                if (_oleCmd != null)
                {
                    _oleCmd.Parameters.Clear();
                    _oleCmd.Dispose();
                }
                _oleCmd = null;

                if (_olecon != null)
                {
                    if (_olecon.State == ConnectionState.Open)
                        _olecon.Close();
                    _olecon.Dispose();
                }
                _olecon = null;

            }
            catch (Exception)
            {
                // termina
                if (_oleCmd != null)
                {
                    _oleCmd.Parameters.Clear();
                    _oleCmd.Dispose();
                }
                _oleCmd = null;

                if (_olecon != null)
                {
                    if (_olecon.State == ConnectionState.Open)
                        _olecon.Close();
                    _olecon.Dispose();
                }
                _olecon = null;

                // erro
                return (-1);
            }

            // ok
            return (num_registros);
        }


        // Insere resultado temporário
        private void ProcessaHistoricoMedicao_HX600_InsereListaResultado(PROCESSA_HIST_MED_RESULTADO resultado)
        {
            // resultado
            PROCESSA_HIST_MED_RESULTADO tmp = new PROCESSA_HIST_MED_RESULTADO();
            tmp.NomeArquivo = resultado.NomeArquivo;
            tmp.IDCliente = resultado.IDCliente;
            tmp.NomeCliente = resultado.NomeCliente;
            tmp.IDMedicao = resultado.IDMedicao;
            tmp.NomeMedicao = resultado.NomeMedicao;
            tmp.status = resultado.status;
            tmp.linha = resultado.linha;
            tmp.inicio = resultado.inicio;
            tmp.fim = resultado.fim;
            tmp.NumRegistros = resultado.NumRegistros;

            // coloca na lista temporaria
            lista_processa_hist_med_hx600_tmp.Add(tmp);
        }

        // Número de linhas
        private int GetNumberOfRows_HX600(string filename, string tableName)
        {
            int count = 0;

            string connectionString = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" + filename; 

            string SQL = "SELECT COUNT (*) FROM [" + tableName + "]";

            // le MDB
            try
            {
                using (OleDbConnection conn = new OleDbConnection(connectionString))
                {
                    conn.Open();

                    using (OleDbCommand cmd = new OleDbCommand(SQL, conn))
                    {
                        using (OleDbDataReader reader = cmd.ExecuteReader())
                        {
                            reader.Read();
                            count = reader.GetInt32(0);
                        }
                    }

                    conn.Close();
                }
            }
            catch (Exception)
            {
                // erro
                return (-1);
            }

            return count;
        }
    }
}
