﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class UploadController
    {

        // GET: Utilidades Mensal Modelo Download
        [HttpGet]
        public virtual ActionResult Upload_UtilidadesMensal_Modelo_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Download PROINFA
        public ActionResult Upload_UtilidadesMensal_Modelo()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // lista de clientes
            List<int> ConfigCliList = ViewBag._ConfigCli;

            // le clientes (ordem por Fantasia)
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clientesMetodos.ListarTodos(ConfigCliList, 0, 2);

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            // nome do arquivo
            string nomeArquivo = string.Format("Modelo_ConsumoUtilidades_{0:yyyyMMddHHmm}.xls",  DateTime.Now);

            // gera planilha
            workbook = UtilidadesMensal_exportar_XLS(listaClientes);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            // relatorio para PDF
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // PROINFA
        private HSSFWorkbook UtilidadesMensal_exportar_XLS(List<ClientesDominio> listaClientes)
        {
            // cria excel
            HSSFWorkbook workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // Medições de Utilidades Virtuais
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Cliente", "ID [Medição]", "Medição", "Data Inicial", "Data Final", "Consumo do Mês" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // percorre clientes do gestor
            if (listaClientes != null)
            {
                // adiciona linhas
                IRow row;

                // percorre clientes
                foreach (ClientesDominio cliente in listaClientes)
                {
                    // lê medições (ordem por nome)
                    MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                    List<MedicoesDominio> medicoes = medicaoMetodos.ListarPorIDCliente_Tipo(cliente.IDCliente, TIPO_MEDICAO.UTILIDADES, 1);

                    // percorre medicoes
                    if (medicoes != null)
                    {
                        // percorre medicoes
                        foreach (MedicoesDominio medicao in medicoes)
                        {
                            // verifica se é gateway virtual (SCDE)
                            GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
                            GatewaysDominio gateway = gatewayMetodos.ListarPorId(medicao.IDGateway);

                            if (gateway == null)
                            {
                                continue;
                            }

                            if (gateway.IDTipoGateway != TIPO_GATEWAY.SCDE)
                            {
                                continue;
                            }

                            // adiciona linha
                            row = sheet.CreateRow(rowIndex++);

                            // Nome do Cliente (Fantasia)
                            textoCelulaXLS(row, 0, cliente.Fantasia);

                            // IDMedicao
                            numeroCelulaXLS(row, 1, medicao.IDMedicao, _intCellStyle);

                            // Nome da Medição
                            textoCelulaXLS(row, 2, medicao.Nome);

                            // Data Inicial e Final
                            DateTime data_agora = DateTime.Now;
                            DateTime data_ini = new DateTime(data_agora.Year, data_agora.Month, 1, 0, 0, 0);
                            DateTime data_fim = data_ini.AddMonths(1);

                            // Data Inicial
                            datahoraCelulaXLS(row, 3, data_ini, _datahoraStyle);

                            // Data Final
                            datahoraCelulaXLS(row, 4, data_fim, _datahoraStyle);

                            // Consumo Mês
                            double consumo_mes = 0.0;
                            numeroCelulaXLS(row, 5, consumo_mes, _1CellStyle);
                        }
                    }
                }
            }

            // largura de cada coluna
            for (int i = 0; i < sheet.GetRow(0).LastCellNum; i++)
            {
                sheet.SetColumnWidth(i, 7000);
            }

            // retorna planilha
            return workbook;
        }
    }
}