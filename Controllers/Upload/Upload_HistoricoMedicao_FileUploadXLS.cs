﻿using System;
using System.IO;
using System.Web;
using System.Web.Mvc;

namespace SmartEnergy.Controllers
{
    public partial class UploadController
    {
        #region Actions

        [HttpPost]
        public virtual ActionResult Upload_HistoricoMedicao_FileUploadXLS()
        {
            // arquivo enviado
            HttpPostedFileBase myFile = Request.Files["fileupload_Historico"];

            // inicia variaveis
            bool isUploaded = false;
            string message = "Envio falhou";

            if (myFile != null && myFile.ContentLength != 0)
            {
                // verifica se XLS ou ZIP
                string extensao = Path.GetExtension(myFile.FileName).ToUpper();

                if (extensao != ".XLS" && extensao != ".ZIP")
                {
                    message = "Arquivo deve ser XLS ou ZIP";
                }
                else
                {
                    // diretorio
                    string pathForSaving = Server.MapPath("~/Temp");

                    if (this.CreateFolderIfNeeded(pathForSaving))
                    {
                        try
                        {
                            //
                            // salva arquivo
                            //
                            myFile.SaveAs(Path.Combine(pathForSaving, myFile.FileName));
                            isUploaded = true;
                            message = "Arquivo enviado com sucesso!";
                        }
                        catch (Exception ex)
                        {
                            message = string.Format("Envio falhou: {0}", ex.Message);
                        }
                    }
                }
            }

            return Json(new { isUploaded = isUploaded, message = message }, "text/html");
        }

        #endregion

    }
}