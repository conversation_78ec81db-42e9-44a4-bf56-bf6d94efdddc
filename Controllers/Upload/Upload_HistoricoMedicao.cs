﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.OleDb;
using System.IO;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;
using System.IO.Compression;
using System.Linq;

namespace SmartEnergy.Controllers
{

    public partial class UploadController
    {

        // GET: Importar Histórico de Medição
        public ActionResult Upload_HistoricoMedicao()
        {
            // tela de ajuda - upload
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // le cookies
            LeCookies_SmartEnergy();

            return View();
        }


        // progresso
        public class PROGRESSO_TASK_HIST_MED
        {
            public int num_arquivos { get; set; }
            public int progresso_arquivo { get; set; }
            public string status_arquivo { get; set; }

            public int num_registros { get; set; }
            public int progresso_registro { get; set; }
            public string status_registro { get; set; }
        }

        // estruturas
        private static IDictionary<Guid, PROGRESSO_TASK_HIST_MED> tasks_hist_med = new Dictionary<Guid, PROGRESSO_TASK_HIST_MED>();
        private static List<string> lista_arquivos_hist_med = new List<string>();
        private List<PROCESSA_HIST_MED_RESULTADO> lista_processa_hist_med_tmp = new List<PROCESSA_HIST_MED_RESULTADO>();
        private static IDictionary<Guid, List<PROCESSA_HIST_MED_RESULTADO>> lista_processa_hist_med = new Dictionary<Guid, List<PROCESSA_HIST_MED_RESULTADO>>();


        // GET: Processa Histórico de Medição
        public ActionResult ProcessaHistoricoMedicao_IniciaProcesso(string nomeHistorico)
        {
            // taskID
            Guid taskId = Guid.NewGuid();

            // progresso
            PROGRESSO_TASK_HIST_MED progresso = new PROGRESSO_TASK_HIST_MED();
            progresso.num_arquivos = 0;
            progresso.num_registros = 0;
            progresso.progresso_arquivo = 0;
            progresso.progresso_registro = 0;
            progresso.status_arquivo = "[-/-] ---";
            progresso.status_registro = "[-/-] Registros";

            tasks_hist_med.Add(taskId, progresso);

            // salva cookie
            CookieStore.SalvaCookie_GUID("taskID", taskId);

            // limpa listas
            lista_arquivos_hist_med.Clear();
            lista_processa_hist_med_tmp.Clear();

            // teste
            Funcoes_Log.Mensagem("EnviaHistorico", "Início");

            // task
            Task.Factory.StartNew(() =>
            {
                // verifica se arquivo existe
                if (nomeHistorico != null)
                {
                    // diretorio
                    string pathForSaving = Server.MapPath("~/Temp");

                    // subdiretorio destino
                    string subdiretorio_destino = "";

                    // nome do arquivo completo
                    string nomeArquivoFull = Path.Combine(pathForSaving, nomeHistorico);

                    // teste
                    Funcoes_Log.Mensagem("EnviaHistorico", nomeArquivoFull);

                    // verifica se arquivo eh ZIP
                    string extensao = Path.GetExtension(nomeHistorico).ToUpper();

                    if (extensao == ".ZIP")
                    {
                        // subdiretorio destino
                        subdiretorio_destino = string.Format("{0}\\HistMed_{1:yyyyMMdd_hhmmss}", pathForSaving, DateTime.Now);

                        // descompacta arquivo
                        ZipFile.ExtractToDirectory(nomeArquivoFull, subdiretorio_destino, System.Text.Encoding.GetEncoding(850));

                        // lista de arquivos
                        DirectoryInfo info = new DirectoryInfo(subdiretorio_destino);
                        FileInfo[] files = info.GetFiles().OrderBy(f => f.LastWriteTime.Year <= 1601 ? f.CreationTime : f.LastWriteTime).ToArray();

                        if (files != null)
                        {
                            foreach (FileInfo file in files)
                            {
                                // nome do arquivo completo
                                string nomearq = Path.Combine(subdiretorio_destino, file.Name);

                                // coloca na lista
                                lista_arquivos_hist_med.Add(nomearq);
                            }
                        }
                    }
                    else
                    {
                        // coloca na lista
                        lista_arquivos_hist_med.Add(nomeArquivoFull);
                    }

                    // barra de progresso arquivos
                    int total = 0;
                    int atual = 0;

                    // progresso
                    progresso.num_arquivos = 0;
                    progresso.progresso_arquivo = 0;
                    progresso.num_registros = 0;
                    progresso.progresso_registro = 0;
                    progresso.status_arquivo = "[-/-] ---";
                    progresso.status_registro = "[-/-] Registros";

                    tasks_hist_med[taskId] = progresso;

                    // percorre arquivos
                    if (lista_arquivos_hist_med != null)
                    {
                        // barra de progresso arquivos
                        total = lista_arquivos_hist_med.Count();

                        // percorre arquivos
                        foreach (string arquivo in lista_arquivos_hist_med)
                        {
                            // nome do arquivo
                            string nomeArquivo = Path.GetFileName(arquivo).ToUpper();

                            // atualiza progresso arquivos
                            progresso.progresso_arquivo = (int)(((double)atual / (double)total) * 100);
                            progresso.status_arquivo = string.Format("[{0}/{1}] {2}", atual + 1, total, nomeArquivo);
                            progresso.status_registro = "[-/-] Registros";

                            tasks_hist_med[taskId] = progresso;
                            atual++;

                            // a principio erro
                            PROCESSA_HIST_MED_RESULTADO resultado = new PROCESSA_HIST_MED_RESULTADO();
                            resultado.NomeArquivo = Path.GetFileName(arquivo);
                            resultado.IDCliente = 0;
                            resultado.NomeCliente = "";
                            resultado.IDMedicao = 0;
                            resultado.NomeMedicao = "";
                            resultado.status = PROCESSA_HIST_MED_STATUS.SEM_REG;
                            resultado.linha = 0;
                            resultado.coluna = 0;
                            resultado.erro = "";
                            resultado.erro_aux = "";
                            resultado.inicio = new DateTime(2000, 1, 1, 0, 0, 0);
                            resultado.fim = new DateTime(2000, 1, 1, 0, 0, 0);
                            resultado.NumRegistros = 0;

                            // verifica se tem arquivo
                            if (string.IsNullOrEmpty(arquivo))
                            {
                                // teste
                                Funcoes_Log.Mensagem("EnviaHistorico", "Erro 1");

                                // erro - insere na lista temporária
                                ProcessaHistoricoMedicao_InsereListaResultado(resultado);

                                continue;
                            }

                            // verifica se arquivo eh XLS
                            string extensaoArquivo = Path.GetExtension(arquivo).ToUpper();

                            if (extensaoArquivo != ".XLS")
                            {
                                // teste
                                Funcoes_Log.Mensagem("EnviaHistorico", "Erro 2");

                                // erro - insere na lista temporária
                                ProcessaHistoricoMedicao_InsereListaResultado(resultado);

                                continue;
                            }

                            // teste
                            Funcoes_Log.Mensagem("EnviaHistorico", string.Format("Processar ({0})", arquivo));

                            // processa histórico
                            ProcessaHistoricoMedicao_Processar(arquivo, extensaoArquivo, taskId, ref progresso);
                        }

                        // verifica se tem subdiretorio
                        if (!string.IsNullOrEmpty(subdiretorio_destino))
                        {
                            // apaga diretorio
                            Directory.Delete(subdiretorio_destino, true);

                            // apaga arquivo ZIP
                            System.IO.File.Delete(nomeArquivoFull);
                        }
                    }
                }

                // teste
                Funcoes_Log.Mensagem("EnviaHistorico", "Fim");

                // coloca resultado na lista
                lista_processa_hist_med.Add(taskId, new List<PROCESSA_HIST_MED_RESULTADO>(lista_processa_hist_med_tmp));

                // terminou
                tasks_hist_med.Remove(taskId);
            });

            // retorna status
            return Json(taskId, JsonRequestBehavior.AllowGet);
        }

        public ActionResult ProcessaHistoricoMedicao_Progress(Guid id)
        {
            PROGRESSO_TASK_HIST_MED progresso = new PROGRESSO_TASK_HIST_MED();
            progresso.num_arquivos = 0;
            progresso.num_registros = 0;
            progresso.progresso_arquivo = 100;
            progresso.progresso_registro = 100;
            progresso.status_arquivo = "";
            progresso.status_registro = "";

            return Json(tasks_hist_med.Keys.Contains(id) ? tasks_hist_med[id] : progresso, JsonRequestBehavior.AllowGet);
        }

        public PartialViewResult _ProcessaHistoricoMedicao_Resultado(Guid id)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // resultado
            ViewBag.processaHistoricoMedicao_resultado = lista_processa_hist_med.Keys.Contains(id) ? lista_processa_hist_med[id] : null;

            return PartialView();
        }


        public int ProcessaHistoricoMedicao_Processar(string caminho_arq, string extensao_arq, Guid taskId, ref PROGRESSO_TASK_HIST_MED progresso)
        {
            // inicia resultado
            PROCESSA_HIST_MED_RESULTADO resultado = new PROCESSA_HIST_MED_RESULTADO();

            //
            // Número de linhas da planilha
            //
            int num_registros = GetNumberOfRows(caminho_arq, "Tabela") - 1;

            if (num_registros < 0)
            {
                // teste
                Funcoes_Log.Mensagem("EnviaHistorico", "Erro num_registros < 0");

                // erro
                resultado.NomeArquivo = Path.GetFileName(caminho_arq);
                resultado.IDCliente = 0;
                resultado.NomeCliente = "";
                resultado.IDMedicao = 0;
                resultado.NomeMedicao = "";
                resultado.status = PROCESSA_HIST_MED_STATUS.SEM_REG;
                resultado.linha = 0;
                resultado.coluna = 0;
                resultado.erro = "";
                resultado.erro_aux = "";
                resultado.inicio = new DateTime(2000, 1, 1, 0, 0, 0);
                resultado.fim = new DateTime(2000, 1, 1, 0, 0, 0);
                resultado.NumRegistros = 0;

                // insere na lista temporária
                ProcessaHistoricoMedicao_InsereListaResultado(resultado);

                // erro
                return (-1);
            }

            int IDMedicao_atual = 0;

            // conexao
            // baixar o provedor de acesso Excel 2010 em "https://www.microsoft.com/en-us/download/details.aspx?id=13255"
            // AccessDatabaseEngine_X64.exe
            // configurando no SQL SERVER https://www.dirceuresende.com/blog/sql-server-como-instalar-os-drivers-microsoft-ace-oledb-12-0-e-microsoft-jet-oledb-4-0/
            OleDbConnection _olecon;
            OleDbCommand _oleCmd;

            // XLS
            String _StringConexao = String.Format(@"Provider=Microsoft.JET.OLEDB.4.0;Data Source={0};Extended Properties='Excel 8.0;HDR=YES;';", caminho_arq);

            // verifica se XLSX
            if (extensao_arq == ".XLSX")
            {
                // XLSX
                _StringConexao = String.Format(@"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={0};Extended Properties='Excel 12.0;HDR=YES;';", caminho_arq);
            }

            _olecon = new OleDbConnection(_StringConexao);
            _oleCmd = new OleDbCommand();

            // linha convertida
            int linha = -1;

            // le Excel
            try
            {
                // conecta
                _olecon.Open();
                _oleCmd.Connection = _olecon;
                _oleCmd.CommandType = CommandType.Text;

                // le excel
                _oleCmd.CommandText = "SELECT * FROM [Tabela$]";
                OleDbDataReader reader = _oleCmd.ExecuteReader();

                // inicia linha
                linha = 0;

                // configuração da medição
                MedicoesDominio medicao = new MedicoesDominio();


                // barra de progresso registros
                int total = num_registros;
                int atual = 0;

                // progresso
                progresso.num_registros = 0;
                progresso.progresso_registro = 0;
                progresso.status_registro = "[-/-] Registros";
                tasks_hist_med[taskId] = progresso;

                // inicia resultado
                resultado.NomeArquivo = Path.GetFileName(caminho_arq);
                resultado.IDCliente = 0;
                resultado.NomeCliente = "";
                resultado.IDMedicao = 0;
                resultado.NomeMedicao = "";
                resultado.status = PROCESSA_HIST_MED_STATUS.SEM_STATUS;
                resultado.linha = linha + 2;
                resultado.coluna = 0;
                resultado.erro = "";
                resultado.erro_aux = "";
                resultado.inicio = new DateTime(2000, 1, 1, 0, 0, 0);
                resultado.fim = new DateTime(2000, 1, 1, 0, 0, 0);
                resultado.NumRegistros = 0;

                // lê planilha
                while (reader.Read())
                {
                    // atualiza progresso medicoes
                    progresso.progresso_registro = (int)(((double)atual / (double)total) * 100);
                    progresso.status_registro = string.Format("[{0}/{1}] Registros", atual + 1, total);
                    tasks_hist_med[taskId] = progresso;
                    atual++;

                    // resultado linha
                    resultado.linha = linha + 2;

                    // IDCliente
                    resultado.coluna = 0;
                    int IDCliente = Convert.ToInt32(reader.GetDouble(0));

                    // IDMedicao
                    resultado.coluna = 1;
                    int IDMedicao = Convert.ToInt32(reader.GetDouble(1));

                    // verifica se mudou medição
                    if (IDMedicao != IDMedicao_atual)
                    {
                        // lê configuracao da medição
                        MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                        medicao = medicaoMetodos.ListarPorId(IDMedicao);

                        if (medicao == null)
                        {
                            // teste
                            Funcoes_Log.Mensagem("EnviaHistorico", "Falha 1");

                            resultado.status = PROCESSA_HIST_MED_STATUS.MED_INEX;
                            goto FINAL;
                        }

                        // verifica medição
                        if (medicao.IDMedicao == 0)
                        {
                            // teste
                            Funcoes_Log.Mensagem("EnviaHistorico", "Falha 2");

                            resultado.status = PROCESSA_HIST_MED_STATUS.MED_INEX;
                            goto FINAL;
                        }

                        // verifica se mudou de medição
                        if (IDMedicao_atual > 0)
                        {
                            // teste
                            Funcoes_Log.Mensagem("EnviaHistorico", "Falha 3");

                            resultado.status = PROCESSA_HIST_MED_STATUS.MED_MUDOU;
                            goto FINAL;
                        }

                        // copia
                        IDMedicao_atual = IDMedicao;

                        // encontrou medição
                        resultado.IDCliente = medicao.IDCliente;

                        ClientesMetodos clienteMetodos = new ClientesMetodos();
                        ClientesDominio cliente = clienteMetodos.ListarPorId(medicao.IDCliente);

                        if (cliente != null)
                        {
                            resultado.NomeCliente = cliente.Fantasia;
                        }

                        resultado.IDMedicao = medicao.IDMedicao;
                        resultado.NomeMedicao = medicao.Nome;


                        // teste
                        Funcoes_Log.Mensagem("EnviaHistorico", string.Format("{0} {1}", resultado.IDCliente, resultado.NomeCliente));
                        Funcoes_Log.Mensagem("EnviaHistorico", string.Format("{0} {1}", resultado.IDMedicao, resultado.NomeMedicao));
                    }

                    // verifica cliente
                    if (medicao.IDCliente != IDCliente)
                    {
                        // teste
                        Funcoes_Log.Mensagem("EnviaHistorico", "Falha 4");

                        resultado.status = PROCESSA_HIST_MED_STATUS.CLI_DIF;
                        goto FINAL;
                    }

                    // data hora auxiliar
                    DateTime data_aux = new DateTime(2000, 1, 1, 0, 0, 0);

                    // verifica se energia elétrica
                    if (medicao.IDTipoMedicao == TIPOS_MEDICAO.energia)
                    {
                        // histórico da medição
                        EN_Dominio registro_en = new EN_Dominio();

                        // DataHora
                        resultado.coluna = 2;
                        registro_en.DataHora = Convert.ToDateTime(reader.GetDateTime(2));
                        data_aux = registro_en.DataHora;

                        // Periodo
                        resultado.coluna = 3;
                        registro_en.Periodo = Convert.ToInt32(reader.GetDouble(3));

                        // Ativo
                        resultado.coluna = 4;
                        registro_en.Ativo = reader.GetDouble(4);

                        // Reativo
                        resultado.coluna = 5;
                        registro_en.Reativo = reader.GetDouble(5);

                        // adiciona registro
                        EN_Metodos enMetodos = new EN_Metodos();
                        if (!enMetodos.InserirRegistro_InserirModificar(IDCliente, IDMedicao, registro_en))
                        {
                            resultado.status = PROCESSA_HIST_MED_STATUS.ERRO_SALVAR_REG;
                            goto FINAL;
                        }
                    }

                    // verifica se utilidades ou ciclometro
                    if (medicao.IDTipoMedicao == TIPOS_MEDICAO.utilidades || medicao.IDTipoMedicao == TIPOS_MEDICAO.ciclometro)
                    {
                        // teste
                        Funcoes_Log.Mensagem("EnviaHistorico", "é utilidades");

                        // histórico da medição
                        GG_Dominio registro_gg = new GG_Dominio();

                        // DataHora
                        resultado.coluna = 2;
                        registro_gg.DataHora = Convert.ToDateTime(reader.GetDateTime(2));
                        data_aux = registro_gg.DataHora;

                        // VMed
                        registro_gg.VMed = reader.GetDouble(3);
                        registro_gg.VMin = registro_gg.VMed;
                        registro_gg.VMax = (medicao.IDTipoMedicao == TIPOS_MEDICAO.ciclometro) ? 0.0 : registro_gg.VMed;

                        // teste
                        Funcoes_Log.Mensagem("EnviaHistorico", string.Format("{0:G}: {1}", data_aux, registro_gg.VMed));

                        // adiciona registro
                        GG_Metodos ggMetodos = new GG_Metodos();
                        if (!ggMetodos.InserirRegistro_ModificarInserir(IDCliente, IDMedicao, registro_gg))
                        {
                            // teste
                            Funcoes_Log.Mensagem("EnviaHistorico", "Falha 5");

                            resultado.status = PROCESSA_HIST_MED_STATUS.ERRO_SALVAR_REG;
                            goto FINAL;
                        }
                    }

                    // verifica se entrada analógica
                    if (medicao.IDTipoMedicao == TIPOS_MEDICAO.ea)
                    {
                        // histórico da medição
                        GG_Dominio registro_gg = new GG_Dominio();

                        // DataHora
                        resultado.coluna = 2;
                        registro_gg.DataHora = Convert.ToDateTime(reader.GetDateTime(2));
                        data_aux = registro_gg.DataHora;

                        // VMed
                        resultado.coluna = 3;
                        registro_gg.VMed = reader.GetDouble(3);

                        // VMin
                        resultado.coluna = 4;
                        registro_gg.VMin = reader.GetDouble(4);

                        // VMax
                        resultado.coluna = 5;
                        registro_gg.VMax = reader.GetDouble(5);

                        // adiciona registro
                        GG_Metodos ggMetodos = new GG_Metodos();
                        if (!ggMetodos.InserirRegistro_ModificarInserir(IDCliente, IDMedicao, registro_gg))
                        {
                            resultado.status = PROCESSA_HIST_MED_STATUS.ERRO_SALVAR_REG;
                            goto FINAL;
                        }
                    }

                    // data inicial
                    if (resultado.inicio.Year == 2000)
                    {
                        resultado.inicio = data_aux;
                    }

                    // data final
                    resultado.fim = data_aux;

                    // processado ok
                    resultado.status = PROCESSA_HIST_MED_STATUS.PROCESSO_OK;

                    // atualiza número de registros atualizados
                    resultado.NumRegistros++;

                    // teste
                    Funcoes_Log.Mensagem("EnviaHistorico", string.Format("NumRegistros {0}", resultado.NumRegistros));

                    // proxima linha
                    linha++;
                }

            FINAL:

                // teste
                Funcoes_Log.Mensagem("EnviaHistorico", "Final");

                // insere na lista temporária
                ProcessaHistoricoMedicao_InsereListaResultado(resultado);

                // fecha
                reader.Close();

                // termina
                if (_oleCmd != null)
                {
                    _oleCmd.Parameters.Clear();
                    _oleCmd.Dispose();
                }
                _oleCmd = null;

                if (_olecon != null)
                {
                    if (_olecon.State == ConnectionState.Open)
                        _olecon.Close();
                    _olecon.Dispose();
                }
                _olecon = null;

            }
            catch (Exception ex)
            {
                // teste
                Funcoes_Log.Mensagem("EnviaHistorico", "Erro exception");

                // termina
                if (_oleCmd != null)
                {
                    _oleCmd.Parameters.Clear();
                    _oleCmd.Dispose();
                }
                _oleCmd = null;

                if (_olecon != null)
                {
                    if (_olecon.State == ConnectionState.Open)
                        _olecon.Close();
                    _olecon.Dispose();
                }
                _olecon = null;


                // erro
                resultado.status = PROCESSA_HIST_MED_STATUS.ERRO_LEITURA;
                resultado.erro = ex.Message;

                // insere na lista temporária
                ProcessaHistoricoMedicao_InsereListaResultado(resultado);

                // erro
                return (-1);
            }

            // ok
            return (num_registros);
        }


        //
        // A função abaixo de processamento executa a tarefa por blocos de QUERY. Se mostrou mais lento, mas vou manter o código para futuro.
        /*
        public int ProcessaHistoricoMedicao_Processar(string caminho_arq, string extensao_arq, Guid taskId, ref PROGRESSO_TASK_HIST_MED progresso)
        {
            // inicia resultado
            PROCESSA_HIST_MED_RESULTADO resultado = new PROCESSA_HIST_MED_RESULTADO();

            //
            // Número de linhas da planilha
            //
            int num_registros = GetNumberOfRows(caminho_arq, "Tabela") - 1;

            if (num_registros < 0)
            {
                // erro
                resultado.NomeArquivo = Path.GetFileName(caminho_arq);
                resultado.IDCliente = 0;
                resultado.NomeCliente = "";
                resultado.IDMedicao = 0;
                resultado.NomeMedicao = "";
                resultado.status = PROCESSA_HIST_MED_STATUS.SEM_REG;
                resultado.linha = 0;
                resultado.coluna = 0;
                resultado.erro = "";
                resultado.erro_aux = "";
                resultado.inicio = new DateTime(2000, 1, 1, 0, 0, 0);
                resultado.fim = new DateTime(2000, 1, 1, 0, 0, 0);
                resultado.NumRegistros = 0;

                // insere na lista temporária
                ProcessaHistoricoMedicao_InsereListaResultado(resultado);

                // erro
                return (-1);
            }

            int IDMedicao_atual = 0;

            // conexao
            // baixar o provedor de acesso Excel 2010 em "https://www.microsoft.com/en-us/download/details.aspx?id=13255"
            // AccessDatabaseEngine_X64.exe
            // configurando no SQL SERVER https://www.dirceuresende.com/blog/sql-server-como-instalar-os-drivers-microsoft-ace-oledb-12-0-e-microsoft-jet-oledb-4-0/
            OleDbConnection _olecon;
            OleDbCommand _oleCmd;

            // XLS
            String _StringConexao = String.Format(@"Provider=Microsoft.JET.OLEDB.4.0;Data Source={0};Extended Properties='Excel 8.0;HDR=YES;';", caminho_arq);

            // verifica se XLSX
            if (extensao_arq == ".XLSX")
            {
                // XLSX
                _StringConexao = String.Format(@"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={0};Extended Properties='Excel 12.0;HDR=YES;';", caminho_arq);
            }

            _olecon = new OleDbConnection(_StringConexao);
            _oleCmd = new OleDbCommand();

            // linha convertida
            int linha = -1;

            // le Excel
            try
            {
                // conecta
                _olecon.Open();
                _oleCmd.Connection = _olecon;
                _oleCmd.CommandType = CommandType.Text;

                // le excel
                _oleCmd.CommandText = "SELECT * FROM [Tabela$]";
                OleDbDataReader reader = _oleCmd.ExecuteReader();

                // inicia linha
                linha = 0;

                // configuração da medição
                MedicoesDominio medicao = new MedicoesDominio();


                // barra de progresso registros
                int total = num_registros;
                int atual = 0;

                // progresso
                progresso.num_registros = 0;
                progresso.progresso_registro = 0;
                progresso.status_registro = "[-/-] Registros";
                tasks_hist_med[taskId] = progresso;

                // inicia resultado
                resultado.NomeArquivo = Path.GetFileName(caminho_arq);
                resultado.IDCliente = 0;
                resultado.NomeCliente = "";
                resultado.IDMedicao = 0;
                resultado.NomeMedicao = "";
                resultado.status = PROCESSA_HIST_MED_STATUS.SEM_STATUS;
                resultado.linha = linha + 2;
                resultado.coluna = 0;
                resultado.erro = "";
                resultado.erro_aux = "";
                resultado.inicio = new DateTime(2000, 1, 1, 0, 0, 0);
                resultado.fim = new DateTime(2000, 1, 1, 0, 0, 0);
                resultado.NumRegistros = 0;

                // contador de registros da query
                int NumRegistrosQuery = 0;

                // query
                string strQuery = "";

                // IDCliente e IDMedicao
                int IDCliente = 0;
                int IDMedicao = 0;

                // lê planilha
                while (reader.Read())
                {
                    // atualiza progresso medicoes
                    progresso.progresso_registro = (int)(((double)atual / (double)total) * 100);
                    progresso.status_registro = string.Format("[{0}/{1}] Registros", atual + 1, total);
                    tasks_hist_med[taskId] = progresso;
                    atual++;

                    // resultado linha
                    resultado.linha = linha + 2;

                    // IDCliente
                    resultado.coluna = 0;
                    IDCliente = Convert.ToInt32(reader.GetDouble(0));

                    // IDMedicao
                    resultado.coluna = 1;
                    IDMedicao = Convert.ToInt32(reader.GetDouble(1));

                    // verifica se possui registros e se mudou medição ou se chegou no limite de registros (~ 1 ano = 96 * 30 * 12 = 34560)
                    if (NumRegistrosQuery > 0 && (IDMedicao != IDMedicao_atual || NumRegistrosQuery > 34560))
                    {
                        // verifica se energia elétrica
                        if (medicao.IDTipoMedicao == TIPOS_MEDICAO.energia)
                        {
                            // salvando
                            progresso.status_registro = string.Format("[{0}/{1}] Salvando no Banco de Dados...", atual + 1, total);
                            tasks_hist_med[taskId] = progresso;

                            // adiciona registros
                            EN_Metodos enMetodos = new EN_Metodos();
                            if (!enMetodos.InserirRegistro_VerificarInserirModificar(IDCliente, IDMedicao, strQuery))
                            {
                                resultado.status = PROCESSA_HIST_MED_STATUS.ERRO_SALVAR_REG;
                                goto FINAL;
                            }
                        }

                        // zera contador e query
                        NumRegistrosQuery = 0;
                        strQuery = "";
                    }

                    // verifica se mudou medição
                    if (IDMedicao != IDMedicao_atual)
                    {
                        // lê configuracao da medição
                        MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                        medicao = medicaoMetodos.ListarPorId(IDMedicao);

                        if (medicao == null)
                        {
                            resultado.status = PROCESSA_HIST_MED_STATUS.MED_INEX;
                            goto FINAL;
                        }

                        // verifica medição
                        if (medicao.IDMedicao == 0)
                        {
                            resultado.status = PROCESSA_HIST_MED_STATUS.MED_INEX;
                            goto FINAL;
                        }

                        // verifica se mudou de medição
                        if (IDMedicao_atual > 0)
                        {
                            resultado.status = PROCESSA_HIST_MED_STATUS.MED_MUDOU;
                            goto FINAL;
                        }

                        // copia
                        IDMedicao_atual = IDMedicao;

                        // encontrou medição
                        resultado.IDCliente = medicao.IDCliente;

                        ClientesMetodos clienteMetodos = new ClientesMetodos();
                        ClientesDominio cliente = clienteMetodos.ListarPorId(medicao.IDCliente);

                        if (cliente != null)
                        {
                            resultado.NomeCliente = cliente.Fantasia;
                        }

                        resultado.IDMedicao = medicao.IDMedicao;
                        resultado.NomeMedicao = medicao.Nome;
                    }

                    // verifica cliente
                    if (medicao.IDCliente != IDCliente)
                    {
                        resultado.status = PROCESSA_HIST_MED_STATUS.CLI_DIF;
                        goto FINAL;
                    }

                    // data hora auxiliar
                    DateTime data_aux = new DateTime(2000, 1, 1, 0, 0, 0);

                    // verifica se energia elétrica
                    if (medicao.IDTipoMedicao == TIPOS_MEDICAO.energia)
                    {
                        // histórico da medição
                        EN_Dominio registro_en = new EN_Dominio();

                        // DataHora
                        resultado.coluna = 2;
                        registro_en.DataHora = Convert.ToDateTime(reader.GetDateTime(2));
                        data_aux = registro_en.DataHora;

                        // Periodo
                        resultado.coluna = 3;
                        registro_en.Periodo = Convert.ToInt32(reader.GetDouble(3));

                        // Ativo
                        resultado.coluna = 4;
                        registro_en.Ativo = reader.GetDouble(4);

                        // Reativo
                        resultado.coluna = 5;
                        registro_en.Reativo = reader.GetDouble(5);

                        // adiciona registro na query
                        EN_Metodos enMetodos = new EN_Metodos();
                        strQuery += enMetodos.VerificarInserirAlterarSQL(IDMedicao, registro_en);

                        // incrementa contador de registros da query
                        NumRegistrosQuery++;
                    }

                    // verifica se utilidades ou ciclometro
                    if (medicao.IDTipoMedicao == TIPOS_MEDICAO.utilidades || medicao.IDTipoMedicao == TIPOS_MEDICAO.ciclometro)
                    {
                        // histórico da medição
                        GG_Dominio registro_gg = new GG_Dominio();

                        // DataHora
                        resultado.coluna = 2;
                        registro_gg.DataHora = Convert.ToDateTime(reader.GetDateTime(2));
                        data_aux = registro_gg.DataHora;

                        // VMed
                        registro_gg.VMed = reader.GetDouble(3);
                        registro_gg.VMin = registro_gg.VMed;
                        registro_gg.VMax = (medicao.IDTipoMedicao == TIPOS_MEDICAO.ciclometro) ? 0.0 : registro_gg.VMed;

                        // adiciona registro
                        GG_Metodos ggMetodos = new GG_Metodos();
                        if (!ggMetodos.InserirRegistro_ModificarInserir(IDCliente, IDMedicao, registro_gg))
                        {
                            resultado.status = PROCESSA_HIST_MED_STATUS.ERRO_SALVAR_REG;
                            goto FINAL;
                        }
                    }

                    // verifica se entrada analógica
                    if (medicao.IDTipoMedicao == TIPOS_MEDICAO.ea)
                    {
                        // histórico da medição
                        GG_Dominio registro_gg = new GG_Dominio();

                        // DataHora
                        resultado.coluna = 2;
                        registro_gg.DataHora = Convert.ToDateTime(reader.GetDateTime(2));
                        data_aux = registro_gg.DataHora;

                        // VMed
                        resultado.coluna = 3;
                        registro_gg.VMed = reader.GetDouble(3);

                        // VMin
                        resultado.coluna = 4;
                        registro_gg.VMin = reader.GetDouble(4);

                        // VMax
                        resultado.coluna = 5;
                        registro_gg.VMax = reader.GetDouble(5);

                        // adiciona registro
                        GG_Metodos ggMetodos = new GG_Metodos();
                        if (!ggMetodos.InserirRegistro_ModificarInserir(IDCliente, IDMedicao, registro_gg))
                        {
                            resultado.status = PROCESSA_HIST_MED_STATUS.ERRO_SALVAR_REG;
                            goto FINAL;
                        }
                    }

                    // data inicial
                    if (resultado.inicio.Year == 2000)
                    {
                        resultado.inicio = data_aux;
                    }

                    // data final
                    resultado.fim = data_aux;

                    // processado ok
                    resultado.status = PROCESSA_HIST_MED_STATUS.PROCESSO_OK;

                    // atualiza número de registros atualizados
                    resultado.NumRegistros++;

                    // proxima linha
                    linha++;
                }

                // verifica se possui registros que não foram salvos
                if (NumRegistrosQuery > 0)
                {
                    // verifica se energia elétrica
                    if (medicao.IDTipoMedicao == TIPOS_MEDICAO.energia)
                    {
                        // adiciona registros
                        EN_Metodos enMetodos = new EN_Metodos();
                        if (!enMetodos.InserirRegistro_VerificarInserirModificar(IDCliente, IDMedicao, strQuery))
                        {
                            resultado.status = PROCESSA_HIST_MED_STATUS.ERRO_SALVAR_REG;
                            goto FINAL;
                        }
                    }

                    // zera contador e query
                    NumRegistrosQuery = 0;
                    strQuery = "";
                }

            FINAL:

                // insere na lista temporária
                ProcessaHistoricoMedicao_InsereListaResultado(resultado);

                // fecha
                reader.Close();

                // termina
                if (_oleCmd != null)
                {
                    _oleCmd.Parameters.Clear();
                    _oleCmd.Dispose();
                }
                _oleCmd = null;

                if (_olecon != null)
                {
                    if (_olecon.State == ConnectionState.Open)
                        _olecon.Close();
                    _olecon.Dispose();
                }
                _olecon = null;

            }
            catch (Exception ex)
            {
                // termina
                if (_oleCmd != null)
                {
                    _oleCmd.Parameters.Clear();
                    _oleCmd.Dispose();
                }
                _oleCmd = null;

                if (_olecon != null)
                {
                    if (_olecon.State == ConnectionState.Open)
                        _olecon.Close();
                    _olecon.Dispose();
                }
                _olecon = null;


                // erro
                resultado.status = PROCESSA_HIST_MED_STATUS.ERRO_LEITURA;
                resultado.erro = ex.Message;

                // insere na lista temporária
                ProcessaHistoricoMedicao_InsereListaResultado(resultado);

                // erro
                return (-1);
            }

            // ok
            return (num_registros);
        }
        */


        // Insere resultado temporário
        private void ProcessaHistoricoMedicao_InsereListaResultado(PROCESSA_HIST_MED_RESULTADO resultado)
        {
            // resultado
            PROCESSA_HIST_MED_RESULTADO tmp = new PROCESSA_HIST_MED_RESULTADO();
            tmp.NomeArquivo = resultado.NomeArquivo;
            tmp.IDCliente = resultado.IDCliente;
            tmp.NomeCliente = resultado.NomeCliente;
            tmp.IDMedicao = resultado.IDMedicao;
            tmp.NomeMedicao = resultado.NomeMedicao;
            tmp.status = resultado.status;
            tmp.linha = resultado.linha;
            tmp.coluna = resultado.coluna;
            tmp.erro = resultado.erro;
            tmp.erro_aux = resultado.erro_aux;
            tmp.inicio = resultado.inicio;
            tmp.fim = resultado.fim;
            tmp.NumRegistros = resultado.NumRegistros;

            // coloca na lista temporaria
            lista_processa_hist_med_tmp.Add(tmp);
        }
    }
}
