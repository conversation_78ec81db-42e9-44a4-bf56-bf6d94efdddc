﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Web.Hosting;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RateioController
    {
        // Rateio calculo
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_ConsProjetadoEnergia", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_ConsProjetadoEnergia(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char qual_periodo, ref DATAHORA pdatahora, ref CONS_PROJETADO pcons_projetado);

        //
        // RATEIO CALCULO
        //

        // GET: Rateio Calculo
        public ActionResult Rateio_Calculo(int IDCliente)
        {
            // tela de ajuda - rateio
            CookieStore.SalvaCookie_String("PaginaAjuda", "Rateio_Calculo");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Rateio_Calculo");
            CookieStore.SalvaCookie_Int("_IDRateio", 0);

            // le cookies
            LeCookies_SmartEnergy();

            // medições do usuário
            List<int> ConfigMedList = ViewBag._ConfigMed;

            // le rateios
            RateioMetodos rateioMetodos = new RateioMetodos();
            List<RateioDominio> listaRateio = rateioMetodos.ListarPorIDCliente(IDCliente, ConfigMedList);
            ViewBag.listaRateio = listaRateio;

            // IDRateio
            int IDRateio = ViewBag._IDRateio;

            if (listaRateio != null)
            {
                if (IDRateio == 0 && listaRateio.Count > 0)
                {
                    ViewBag._IDRateio = listaRateio[0].IDRateio;
                }
            }


            // data inicio
            DateTime data_hora_ini = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 0, 0);

            // data fim
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);
            //data_hora_fim = data_hora_fim.AddMinutes(-15);

            // le cookie datahora
            DateTime data_hora_ini_cookie = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime data_hora_fim_cookie = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // verifica se existe datahora no cookie
            if (data_hora_ini_cookie.Year != 2000 && data_hora_fim_cookie.Year != 2000)
            {
                // copia data do cookie 
                data_hora_ini = data_hora_ini_cookie;
                data_hora_fim = data_hora_fim_cookie;
            }

            // converte
            DATAHORA dh_ini = new DATAHORA();
            DATAHORA dh_fim = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, data_hora_ini);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, data_hora_fim);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", data_hora_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", data_hora_fim);

            // data e hora atual
            ViewBag.DataIni = string.Format("{0:d}", data_hora_ini);
            ViewBag.HoraIni = string.Format("{0:HH:mm}", data_hora_ini);
            ViewBag.DataFim = string.Format("{0:d}", data_hora_fim);
            ViewBag.HoraFim = string.Format("{0:HH:mm}", data_hora_fim);

            ViewBag.DataTextoAtualIni = string.Format("{0:d} {1:HH:mm}", data_hora_ini, data_hora_ini);
            ViewBag.DataTextoAtualFim = string.Format("{0:d} {1:HH:mm}", data_hora_fim, data_hora_fim);

            ViewBag.DataAtualN = data_hora_ini;
            ViewBag.DataFinalN = data_hora_fim;


            // valores
            RATEIO_TOTAL rateio_total = new RATEIO_TOTAL();
            List<RATEIO_MEDICOES> rateio_medicoes = new List<RATEIO_MEDICOES>();

            ViewBag.rateio_total = rateio_total;
            ViewBag.rateio_medicoes = rateio_medicoes;

            // lista de erros
            var listaErros = new List<string>();

            // erros
            ViewBag.listaErros = listaErros;

            // rateio
            ViewBag.NomeRateio = "";

            return View();
        }

        // GET: Rateio Atualizar
        public PartialViewResult _Rateio_Atualizar(int IDRateio, int Navegacao, string DataIni, string DataFim)
        {
            // salva cookie
            CookieStore.SalvaCookie_Int("_IDRateio", IDRateio);

            // calcula
            Rateio_Calculo_Show(IDRateio, Navegacao, DataIni, DataFim);

            return PartialView();
        }

        // GET: Rateio Calculo Print
        public ActionResult Rateio_Calculo_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDRateio e GUID
            int IDCliente = ViewBag._IDCliente;
            int IDRateio = ViewBag._IDRateio;
            Guid id = ViewBag.taskID;

            // le rateio
            RateioMetodos rateioMetodos = new RateioMetodos();
            RateioDominio rateio = rateioMetodos.ListarPorId(IDRateio);
            ViewBag.NomeRateio = rateio.Nome;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_ini = string.Format("{0:g}",DateTime.Now);
            string data_fim = string.Format("{0:g}", DateTime.Now);

            // calcula
            Rateio_Calculo_Show(IDRateio, 0, data_ini, data_fim);

            // resultado
            ViewBag.rateio_medicoes = rateio_medicoes.Keys.Contains(id) ? rateio_medicoes[id] : null;
            ViewBag.rateio_total = rateio_total.Keys.Contains(id) ? rateio_total[id] : new RATEIO_TOTAL();

            // imprime
            return View();
        }

        public static async Task<string> EMailTemplate(string template)
        {
            var templateFilePath = HostingEnvironment.MapPath("~/Content/emailTemplates/") + template + ".cshtml";
            StreamReader objstreamreaderfile = new StreamReader(templateFilePath);
            var body = await objstreamreaderfile.ReadToEndAsync();
            objstreamreaderfile.Close();
            return body;
        }

        // GET: Rateio Calculo EMAIL
        public async Task<ActionResult> Rateio_Calculo_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDRateio e GUID
            int IDCliente = ViewBag._IDCliente;
            int IDRateio = ViewBag._IDRateio;
            Guid id = ViewBag.taskID;

            // le rateio
            RateioMetodos rateioMetodos = new RateioMetodos();
            RateioDominio rateio = rateioMetodos.ListarPorId(IDRateio);
            ViewBag.NomeRateio = rateio.Nome;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_ini = string.Format("{0:g}", DateTime.Now);
            string data_fim = string.Format("{0:g}", DateTime.Now);

            // calcula
            Rateio_Calculo_Show(IDRateio, 0, data_ini, data_fim);

            // resultado
            ViewBag.rateio_medicoes = rateio_medicoes.Keys.Contains(id) ? rateio_medicoes[id] : null;
            ViewBag.rateio_total = rateio_total.Keys.Contains(id) ? rateio_total[id] : new RATEIO_TOTAL();

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Rateio_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDRateio, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_Rateio_Calculo_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "RateioEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.NomeRateio", ViewBag.NomeRateio);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Rateio Calculo PDF
        public ActionResult Rateio_Calculo_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDRateio e GUID
            int IDCliente = ViewBag._IDCliente;
            int IDRateio = ViewBag._IDRateio;
            Guid id = ViewBag.taskID;

            // le rateio
            RateioMetodos rateioMetodos = new RateioMetodos();
            RateioDominio rateio = rateioMetodos.ListarPorId(IDRateio);
            ViewBag.NomeRateio = rateio.Nome;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_ini = string.Format("{0:g}", DateTime.Now);
            string data_fim = string.Format("{0:g}", DateTime.Now);

            // calcula
            Rateio_Calculo_Show(IDRateio, 0, data_ini, data_fim);

            // resultado
            ViewBag.rateio_medicoes = rateio_medicoes.Keys.Contains(id) ? rateio_medicoes[id] : null;
            ViewBag.rateio_total = rateio_total.Keys.Contains(id) ? rateio_total[id] : new RATEIO_TOTAL();

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Rateio_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDRateio, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_Rateio_Calculo_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Rateio Calculo XLS
        public ActionResult Rateio_Calculo_XLS()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDRateio e GUID
            int IDCliente = ViewBag._IDCliente;
            int IDRateio = ViewBag._IDRateio;
            Guid id = ViewBag.taskID;

            // le rateio
            RateioMetodos rateioMetodos = new RateioMetodos();
            RateioDominio rateio = rateioMetodos.ListarPorId(IDRateio);
            ViewBag.NomeRateio = rateio.Nome;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_ini = string.Format("{0:g}", DateTime.Now);
            string data_fim = string.Format("{0:g}", DateTime.Now);

            // calcula
            Rateio_Calculo_Show(IDRateio, 0, data_ini, data_fim);

            // resultado
            ViewBag.rateio_medicoes = rateio_medicoes.Keys.Contains(id) ? rateio_medicoes[id] : null;
            ViewBag.rateio_total = rateio_total.Keys.Contains(id) ? rateio_total[id] : new RATEIO_TOTAL();

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_Rateio_Calculo(IDCliente, IDRateio);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Rateio_{0:000000}_{1:yyyyMMddHHmm}.xls", IDRateio, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Rateio Calculo XLS Download
        [HttpGet]
        public virtual ActionResult Rateio_Calculo_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }




        // estruturas
        private static IDictionary<Guid, int> tasks = new Dictionary<Guid, int>();

        private RATEIO_TOTAL rateio_total_tmp = new RATEIO_TOTAL();
        private List<RATEIO_MEDICOES> rateio_medicoes_tmp = new List<RATEIO_MEDICOES>();

        private static IDictionary<Guid, RATEIO_TOTAL> rateio_total = new Dictionary<Guid, RATEIO_TOTAL>();
        private static IDictionary<Guid, List<RATEIO_MEDICOES>> rateio_medicoes = new Dictionary<Guid, List<RATEIO_MEDICOES>>();

        public ActionResult Rateio_IniciaCalculo(int IDRateio)
        {
            // taskID
            Guid taskId = Guid.NewGuid();
            tasks.Add(taskId, 0);

            // salva cookie
            CookieStore.SalvaCookie_GUID("taskID", taskId);

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;

            // lista de medicoes habilitadas do usuario
            List<int> ConfigMedicaoList_Usuario = new List<int>();
            ConfigMedicaoList_Usuario = ViewBag._ConfigMed;

            // verifica se existe lista de medicoes
            if (ConfigMedicaoList_Usuario != null)
            {
                if (ConfigMedicaoList_Usuario.Count == 0)
                {
                    ConfigMedicaoList_Usuario = null;
                }
            }

            // lista de erros
            var listaErros = new List<string>();

            // verifica se IDRateio valido
            ViewBag._IDRateio = IDRateio;

            if (IDRateio <= 0)
            {
                if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
                {
                    listaErros.Add("Não existem Rateio configurados");
                }

                // rateio
                ViewBag.NomeRateio = "";

                // valores
                ViewBag.rateio_medicoes = rateio_medicoes_tmp;
                ViewBag.rateio_total = rateio_total_tmp;

                // erros
                ViewBag.listaErros = listaErros;

                // terminou
                tasks.Remove(taskId);

                // retorna status
                return Json(taskId, JsonRequestBehavior.AllowGet);
            }



            // le rateio
            RateioMetodos rateioMetodos = new RateioMetodos();
            RateioDominio rateio = new RateioDominio();
            rateio = rateioMetodos.ListarPorId(IDRateio);

            // rateio
            ViewBag.NomeRateio = rateio.Nome;

            // tipo rateio
            ViewBag.IDTipoRateio = rateio.IDTipoRateio;

            // le medicoes do rateio
            GrupoMedicoesMedMetodos medGrupoMetodos = new GrupoMedicoesMedMetodos();
            List<GrupoMedicoesMedDominio> medGrupos = medGrupoMetodos.ListarPorIDGrupoMedicoes(rateio.IDGrupoMedicoes);

            // converte
            DateTime DataIni = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime DataFim = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // tarifa do rateio
            TarifasRateioMetodos tarifaMetodos = new TarifasRateioMetodos();
            TarifasRateioDominio tarifa = tarifaMetodos.MaisRecente(rateio.IDCliente, rateio.IDGrupoTarifas, DataFim);

            // limpa 
            rateio_medicoes_tmp.Clear();

            rateio_total_tmp.Consumo = 0.0;
            rateio_total_tmp.NumMedicoes = 0;
            rateio_total_tmp.Tarifa = 0.0;
            rateio_total_tmp.Valor = 0.0;

            // task
            Task.Factory.StartNew(() =>
            {
                // barra de progresso
                int total = 0;
                int atual = 0;

                // percorre medicoes
                if (medGrupos != null)
                {
                    // barra de progresso
                    total = medGrupos.Count;

                    // percorre medicoes
                    foreach (GrupoMedicoesMedDominio medGrupo in medGrupos)
                    {
                        // update task progress
                        double progresso = ((double)atual / (double)total) * 100;
                        tasks[taskId] = (int)progresso;
                        atual++;

                        // verifica se existe lista de medicoes
                        if (ConfigMedicaoList_Usuario != null)
                        {
                            // verifica se medicao NAO esta habilitada para o usuario
                            if (!ConfigMedicaoList_Usuario.Contains(medGrupo.IDMedicao))
                            {
                                // pula medicao
                                continue;
                            }
                        }

                        // rateio
                        RATEIO_MEDICOES rat = new RATEIO_MEDICOES();

                        // medicao
                        rat.IDMedicao = medGrupo.IDMedicao;
                        rat.Nome_Medicao = "";
                        rat.PontoMedicao = "";
                        rat.UnidadeConsumo = "kWh";
                        rat.CicloIni_Valor = "--------";
                        rat.CicloIni_DataHora = DataIni;
                        rat.CicloFim_Valor = "--------";
                        rat.CicloFim_DataHora = DataFim;

                        // calcula rateio da medicao
                        MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                        MedicoesDominio medicao = medicaoMetodos.ListarPorId(medGrupo.IDMedicao);

                        if (medicao != null)
                        {
                            // verifica se medição é do tipo do rateio energia elétrica
                            if (rateio.IDTipoRateio == 0)
                            {
                                if (medicao.IDTipoMedicao != TIPO_MEDICAO.ENERGIA && medicao.IDTipoMedicao != TIPO_MEDICAO.ENERGIA_FORMULA && medicao.IDTipoMedicao != TIPO_MEDICAO.CICLOMETRO)
                                {
                                    // pula medicao
                                    continue;
                                }
                            }

                            // verifica se medição é do tipo do rateio utilidades
                            if (rateio.IDTipoRateio == 1)
                            {
                                if (medicao.IDTipoMedicao != TIPO_MEDICAO.UTILIDADES && medicao.IDTipoMedicao != TIPO_MEDICAO.UTILIDADES_FORMULA && medicao.IDTipoMedicao != TIPO_MEDICAO.CICLOMETRO)
                                {
                                    // pula medicao
                                    continue;
                                }
                            }

                            // nome medicao
                            rat.Nome_Medicao = medicao.Nome;

                            // ponto medicao
                            rat.PontoMedicao = medicao.PontoMedicao;

                            // unidade de consumo
                            rat.UnidadeConsumo = "kWh";

                            if (rateio.IDTipoRateio == 1)
                            {
                                rat.UnidadeConsumo = medicao.UnidadeGrandeza;
                            }

                            // numero de medicoes
                            rateio_total_tmp.NumMedicoes += 1;

                            // consumo da medicao
                            double consumo = 0.0;

                            // verifica se medição é do tipo do rateio energia elétrica
                            if (rateio.IDTipoRateio == 0)
                            {
                                // verifica se medição é ciclometro
                                if (medicao.IDTipoMedicao == TIPO_MEDICAO.CICLOMETRO)
                                {
                                    GG_Metodos ggMetodos = new GG_Metodos();
                                    consumo = ggMetodos.ConsumoTotalPeriodo_Ciclometro(rateio.IDCliente, rat.IDMedicao, DataIni, DataFim, ref rat);
                                }
                                else
                                {
                                    EN_Metodos enMetodos = new EN_Metodos();
                                    consumo = enMetodos.ConsumoTotalPeriodo(rateio.IDCliente, rat.IDMedicao, DataIni, DataFim);
                                }
                            }

                            // verifica se medição é do tipo do rateio utilidades
                            if (rateio.IDTipoRateio == 1)
                            {
                                // verifica se medição é ciclometro
                                if (medicao.IDTipoMedicao == TIPO_MEDICAO.CICLOMETRO)
                                {
                                    GG_Metodos ggMetodos = new GG_Metodos();
                                    consumo = ggMetodos.ConsumoTotalPeriodo_Ciclometro(rateio.IDCliente, rat.IDMedicao, DataIni, DataFim, ref rat);
                                }
                                else
                                {
                                    GG_Metodos ggMetodos = new GG_Metodos();
                                    consumo = ggMetodos.ConsumoTotalPeriodo(rateio.IDCliente, rat.IDMedicao, DataIni, DataFim);
                                }
                            }

                            // consumo
                            rateio_total_tmp.Consumo += consumo;
                            rat.Consumo = consumo;

                            // tarifa
                            rateio_total_tmp.Tarifa = tarifa.Tarc;
                            rat.Tarifa = tarifa.Tarc;

                            // valor
                            rat.Valor = consumo * tarifa.Tarc;

                            // total
                            rateio_total_tmp.Valor += rat.Valor;

                            // coloca na lista
                            rateio_medicoes_tmp.Add(rat);
                        }
                    }
                }

                // coloca resultado
                rateio_medicoes.Add(taskId, new List<RATEIO_MEDICOES>(rateio_medicoes_tmp));
                rateio_total.Add(taskId, rateio_total_tmp);

                // terminou
                tasks.Remove(taskId);
            });

            // valores
            //ViewBag.rateio_medicoes = lista_tmp;
            //ViewBag.rateio_total = rateio_total;

            // retorna status
            return Json(taskId, JsonRequestBehavior.AllowGet);
        }

        public ActionResult Rateio_Progress(Guid id)
        {
            return Json(tasks.Keys.Contains(id) ? tasks[id] : 100, JsonRequestBehavior.AllowGet);
        }

        public PartialViewResult _Rateio_Calculo(Guid id)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // resultado
            ViewBag.rateio_medicoes = rateio_medicoes.Keys.Contains(id) ? rateio_medicoes[id] : null;
            ViewBag.rateio_total = rateio_total.Keys.Contains(id) ? rateio_total[id] : new RATEIO_TOTAL();

            return PartialView();
        }


        // Calcula Rateio
        private void Calc_Rateio(int IDRateio, DATAHORA dh_ini, DATAHORA dh_fim)
        {

            // le cookies
            LeCookies_SmartEnergy();

            // lista de medicoes habilitadas do usuario
            List<int> ConfigMedicaoList_Usuario = new List<int>();
            ConfigMedicaoList_Usuario = ViewBag._ConfigMed;

            // verifica se existe lista de medicoes
            if (ConfigMedicaoList_Usuario != null)
            {
                if (ConfigMedicaoList_Usuario.Count == 0)
                {
                    ConfigMedicaoList_Usuario = null;
                }
            }


            // valores
            RATEIO_TOTAL rateio_total = new RATEIO_TOTAL();
            List<RATEIO_MEDICOES> rateio_medicoes = new List<RATEIO_MEDICOES>();

            // lista de erros
            var listaErros = new List<string>();

            // verifica se IDRateio valido
            ViewBag._IDRateio = IDRateio;

            if (IDRateio <= 0)
            {
                if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
                {
                    listaErros.Add("Não existem Rateio configurados");
                }

                // rateio
                ViewBag.NomeRateio = "";

                // valores
                ViewBag.rateio_medicoes = rateio_medicoes;
                ViewBag.rateio_total = rateio_total;

                // erros
                ViewBag.listaErros = listaErros;

                return;
            }

            // le rateio
            RateioMetodos rateioMetodos = new RateioMetodos();
            RateioDominio rateio = new RateioDominio();
            rateio = rateioMetodos.ListarPorId(IDRateio);

            // rateio
            ViewBag.NomeRateio = rateio.Nome;

            // le medicoes do rateio
            GrupoMedicoesMedMetodos medGrupoMetodos = new GrupoMedicoesMedMetodos();
            List<GrupoMedicoesMedDominio> medGrupos = medGrupoMetodos.ListarPorIDGrupoMedicoes(rateio.IDGrupoMedicoes);

            // converte
            DateTime DataIni = Funcoes_Converte.ConverteDataHora2DateTime(dh_ini);
            DateTime DataFim = Funcoes_Converte.ConverteDataHora2DateTime(dh_fim);

            // tarifa do rateio
            TarifasRateioMetodos tarifaMetodos = new TarifasRateioMetodos();
            TarifasRateioDominio tarifa = tarifaMetodos.MaisRecente(rateio.IDCliente, rateio.IDGrupoTarifas, DataFim);

            // percorre medicoes
            if (medGrupos != null)
            {
                foreach (GrupoMedicoesMedDominio medGrupo in medGrupos)
                {
                    // verifica se existe lista de medicoes
                    if (ConfigMedicaoList_Usuario != null)
                    {
                        // verifica se medicao NAO esta habilitada para o usuario
                        if (!ConfigMedicaoList_Usuario.Contains(medGrupo.IDMedicao))
                        {
                            // pula medicao
                            continue;
                        }
                    }

                    // rateio
                    RATEIO_MEDICOES rat = new RATEIO_MEDICOES();

                    // medicao
                    rat.IDMedicao = medGrupo.IDMedicao;
                    rat.Nome_Medicao = "";
                    rat.PontoMedicao = "";
                    rat.CicloIni_Valor = "--------";
                    rat.CicloIni_DataHora = DataIni;
                    rat.CicloFim_Valor = "--------";
                    rat.CicloFim_DataHora = DataFim;

                    // calcula rateio da medicao
                    MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                    MedicoesDominio medicao = medicaoMetodos.ListarPorId(medGrupo.IDMedicao);

                    if (medicao != null)
                    {
                        // nome medicao
                        rat.Nome_Medicao = medicao.Nome;

                        // ponto medicao
                        rat.PontoMedicao = medicao.PontoMedicao;

                        // numero de medicoes
                        rateio_total.NumMedicoes += 1;

                        // consumo da medicao
                        EN_Metodos enMetodos = new EN_Metodos();
                        double consumo = enMetodos.ConsumoTotalPeriodo(rateio.IDCliente, rat.IDMedicao, DataIni, DataFim);

                        // consumo
                        rateio_total.Consumo += consumo;
                        rat.Consumo = consumo;

                        // tarifa
                        rateio_total.Tarifa = tarifa.Tarc;
                        rat.Tarifa = tarifa.Tarc;

                        // valor
                        rat.Valor = consumo * tarifa.Tarc;

                        // total
                        rateio_total.Valor += rat.Valor;

                        // coloca na lista
                        rateio_medicoes.Add(rat);
                    }
                }
            }

            // valores
            ViewBag.rateio_medicoes = rateio_medicoes;
            ViewBag.rateio_total = rateio_total;

            // erros
            ViewBag.listaErros = listaErros;

            return;
        }


        // GET: Rateio Show
        public void Rateio_Calculo_Show(int IDRateio, int Navegacao, string DataIni, string DataFim)
        {
            // caso forcar data
            if (Navegacao == 100)
            {
                // pega data
                DateTime dateValueIni = DateTime.Parse(DataIni);
                DateTime dateValueFim = DateTime.Parse(DataFim);

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Fatura_DataIni", dateValueIni);
                CookieStore.SalvaCookie_Datahora("Fatura_DataFim", dateValueFim);
            }

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;

            // medições do usuário
            List<int> ConfigMedList = ViewBag._ConfigMed;

            // le rateios
            RateioMetodos rateioMetodos = new RateioMetodos();
            List<RateioDominio> listaRateio = rateioMetodos.ListarPorIDCliente(IDCliente, ConfigMedList);
            ViewBag.listaRateio = listaRateio;

            // verifica se IDRateio valido
            ViewBag._IDRateio = IDRateio;

            if (listaRateio != null)
            {
                ViewBag.NomeRateio = listaRateio[0].Nome;

                if (IDRateio == 0 && listaRateio.Count > 0)
                {
                    IDRateio = listaRateio[0].IDRateio;
                    ViewBag._IDRateio = listaRateio[0].IDRateio;
                }
            }
            else
            {
                ViewBag.NomeRateio = "";
            }

            // le rateio
            RateioDominio rateio = rateioMetodos.ListarPorId(IDRateio);

            if (rateio != null)
            {
                ViewBag.NomeRateio = rateio.Nome;
            }


            // le cookie datahora
            DateTime datahora_cookie_ini = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Fatura_DataFim");
            DATAHORA dh_ini = new DATAHORA();
            DATAHORA dh_fim = new DATAHORA();

            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -3:    // mes anterior

                    datahora_cookie_ini = datahora_cookie_ini.AddMonths(-1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(-1);
                    break;

                case 3:    // mes seguinte

                    datahora_cookie_ini = datahora_cookie_ini.AddMonths(1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(1);
                    break;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", datahora_cookie_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", datahora_cookie_fim);

            // data e hora atual
            ViewBag.DataIni = string.Format("{0:d}", datahora_cookie_ini);
            ViewBag.HoraIni = string.Format("{0:HH:mm}", datahora_cookie_ini);
            ViewBag.DataFim = string.Format("{0:d}", datahora_cookie_fim);
            ViewBag.HoraFim = string.Format("{0:HH:mm}", datahora_cookie_fim);

            ViewBag.DataTextoAtualIni = string.Format("{0:d} {1:HH:mm:ss}", datahora_cookie_ini, datahora_cookie_ini);
            ViewBag.DataTextoAtualFim = string.Format("{0:d} {1:HH:mm:ss}", datahora_cookie_fim, datahora_cookie_fim);

            ViewBag.DataAtualN = datahora_cookie_ini;
            ViewBag.DataFinalN = datahora_cookie_fim;

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, datahora_cookie_ini);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, datahora_cookie_fim);

            return;
        }


        // Rateio Calculo XLS
        private HSSFWorkbook XLS_Rateio_Calculo(int IDCliente, int IDRateio)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _8CellStyle = criaEstiloXLS(workbook, 8);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TOTAL e MEDICOES
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Total e Medições");

            // cabecalho
            string[] cabecalho = { "Medição", "Ponto de Medição", "Ciclômetro Início", "Ciclômetro Fim", "Consumo", "Tarifa", "Valor" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            List<RATEIO_MEDICOES> rateio_medicoes = ViewBag.rateio_medicoes;
            RATEIO_TOTAL rateio_total = ViewBag.rateio_total;
            int i;
            IRow row;

            // percorre valores
            if (rateio_medicoes != null)
            {
                foreach (RATEIO_MEDICOES rat in rateio_medicoes)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // nome medicao
                    textoCelulaXLS(row, 0, rat.Nome_Medicao);

                    // ponto medicao
                    textoCelulaXLS(row, 1, rat.PontoMedicao);

                    // ciclometro inicio
                    textoCelulaXLS(row, 2, rat.CicloIni_Valor);

                    // ciclometro fim
                    textoCelulaXLS(row, 3, rat.CicloFim_Valor);

                    // consumo
                    numeroCelulaXLS(row, 4, rat.Consumo, _3CellStyle);

                    // tarifa
                    numeroCelulaXLS(row, 5, rat.Tarifa, _8CellStyle);

                    // valor
                    numeroCelulaXLS(row, 6, rat.Valor, _2CellStyle);

                    // proxima
                    rowIndex++;
                }
            }

            // adiciona linha
            row = sheet.CreateRow(rowIndex++);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // medicao
            textoCelulaXLS(row, 0, "TOTAL", _negritoCellStyle);

            // colunas em branco
            textoCelulaXLS(row, 1, "", _negritoCellStyle);
            textoCelulaXLS(row, 2, "", _negritoCellStyle);
            textoCelulaXLS(row, 3, "", _negritoCellStyle);

            // consumo
            numeroCelulaXLS(row, 4, rateio_total.Consumo, _3CellStyle);

            // tarifa
            numeroCelulaXLS(row, 5, rateio_total.Tarifa, _8CellStyle);

            // valor
            numeroCelulaXLS(row, 6, rateio_total.Valor, _2CellStyle);


            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(4).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }
    }
}