﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class RateioController
    {
        // GET: Tarifas de Rateio - Historico
        public ActionResult TarifasRateio_Historico(int IDGrupoTarifas)
        {
            // tela de ajuda - tarifas
            CookieStore.SalvaCookie_String("PaginaAjuda", "Rateios_Tarifas");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permisso<PERSON>();

            // le grupo
            TarifasRateioGruposMetodos grupoMetodos = new TarifasRateioGruposMetodos();
            TarifasRateioGruposDominio grupo = grupoMetodos.ListarPorId(IDGrupoTarifas);

            ViewBag.IDGrupoTarifas = grupo.IDGrupoTarifas;
            ViewBag.Nome = grupo.Nome;

            // le historico das tarifas do grupo
            TarifasRateioMetodos tarifasMetodos = new TarifasRateioMetodos();
            List<TarifasRateioDominio> listaTarifas = tarifasMetodos.ListarPorIDGrupo(IDGrupoTarifas);
            return View(listaTarifas);
        }

        // GET: Tarifas de Rateio - Editar
        public ActionResult TarifasRateio_Editar(int IDGrupoTarifas, int IDTarifa)
        {
            // tela de ajuda - tarifas
            CookieStore.SalvaCookie_String("PaginaAjuda", "Rateios_TarifasEditar");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le grupo
            TarifasRateioGruposMetodos grupoMetodos = new TarifasRateioGruposMetodos();
            TarifasRateioGruposDominio grupo = grupoMetodos.ListarPorId(IDGrupoTarifas);

            ViewBag.Nome = grupo.Nome;

            // verifica se adicionando
            TarifasRateioDominio tarifa = new TarifasRateioDominio();
            if (IDTarifa == 0)
            {
                // zera grupo com default
                tarifa.IDTarifa = 0;
                tarifa.IDCliente = ViewBag._IDCliente;
                tarifa.IDGrupoTarifas = IDGrupoTarifas;
                tarifa.Data = new DateTime(DateTime.Now.Year,DateTime.Now.Month,1,0,0,0);
                tarifa.Tarc = 0.0;
            }
            else
            {
                // le tarifa
                TarifasRateioMetodos tarifaMetodos = new TarifasRateioMetodos();
                tarifa = tarifaMetodos.ListarPorId(IDTarifa);
            }

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", tarifa.Data);

            return View(tarifa);
        }

        // POST: Tarifas de Rateio - Salvar
        [HttpPost]
        public ActionResult TarifasRateio_Salvar(TarifasRateioDominio tarifa)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outra tarifa com a mesma data
            TarifasRateioMetodos tarifaMetodos = new TarifasRateioMetodos();
            if (tarifaMetodos.VerificarDuplicidade(tarifa))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Data existente."
                };
            }
            else
            {
                // ajusta data
                tarifa.Data = new DateTime(tarifa.Data.Year, tarifa.Data.Month, 1, 0, 0, 0);

                // salva tarifa
                tarifaMetodos.Salvar(tarifa);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Tarifas de Rateio - Excluir
        public ActionResult TarifasRateio_Excluir(int IDTarifa)
        {
            // tarifa
            TarifasRateioMetodos tarifasMetodos = new TarifasRateioMetodos();
            tarifasMetodos.Excluir(IDTarifa);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}