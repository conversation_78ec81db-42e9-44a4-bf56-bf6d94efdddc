﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class RateioController
    {
        // GET: Tarifas de Rateio - Grupos
        public ActionResult TarifasRateio_Grupos(int IDCliente)
        {
            // tela de ajuda - tarifas
            CookieStore.SalvaCookie_String("PaginaAjuda", "Rateios_GruposTarifas");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le grupos
            TarifasRateioGruposMetodos gruposMetodos = new TarifasRateioGruposMetodos();
            List<TarifasRateioGruposDominio> listaGrupos = gruposMetodos.ListarPorIDCliente(IDCliente);

            return View(listaGrupos);
        }

        // GET: Tarifas de Rateio - Grupos - Editar
        public ActionResult TarifasRateio_Grupos_Editar(int IDGrupoTarifas)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // verifica se adicionando
            TarifasRateioGruposDominio grupo = new TarifasRateioGruposDominio();
            if (IDGrupoTarifas == 0)
            {
                // zera grupo com default
                grupo.IDGrupoTarifas = 0;
                grupo.IDCliente = ViewBag._IDCliente;
            }
            else
            {
                // le grupo
                TarifasRateioGruposMetodos grupoMetodos = new TarifasRateioGruposMetodos();
                grupo = grupoMetodos.ListarPorId(IDGrupoTarifas);
            }

            return View(grupo);
        }

        // POST: Tarifas de Rateio - Grupos - Salvar
        [HttpPost]
        public ActionResult TarifasRateio_Grupos_Salvar(TarifasRateioGruposDominio grupo)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro grupo com o mesmo nome
            TarifasRateioGruposMetodos grupoMetodos = new TarifasRateioGruposMetodos();
            if (grupoMetodos.VerificarDuplicidade(grupo))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Grupo existente."
                };
            }
            else
            {
                // salva grupo
                grupoMetodos.Salvar(grupo);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Tarifas de Rateio - Grupos - Excluir
        public ActionResult TarifasRateio_Grupos_Excluir(int IDGrupoTarifas)
        {
            // exclui grupo
            TarifasRateioGruposMetodos grupoMetodos = new TarifasRateioGruposMetodos();
            grupoMetodos.Excluir(IDGrupoTarifas);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}