﻿using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class RateioController
    {
        // GET: Rateio - Configuracao
        public ActionResult Rateio_Configuracao(int IDCliente)
        {
            // tela de ajuda - rateio
            CookieStore.SalvaCookie_String("PaginaAjuda", "Rateio_Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le rateios
            RateioMetodos rateioMetodos = new RateioMetodos();
            List<RateioDominio> listaRateio = rateioMetodos.ListarPorIDCliente(IDCliente);

            return View(listaRateio);
        }

        // GET: Rateio - Editar
        public ActionResult Rateio_Editar(int IDRateio)
        {
            int IDCliente = 0;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Rateio_Editar");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // verifica se adicionando
            RateioDominio rateio = new RateioDominio();
            if (IDRateio == 0)
            {
                // zera usuario com default
                rateio.IDRateio = 0;
                rateio.IDCliente = ViewBag._IDCliente;
                rateio.Nome = "";
                rateio.IDTipoRateio = 0;    // energia elétrica
                rateio.IDGrupoTarifas = 0;
                rateio.DiaInicioFat = 1;
                rateio.DiaEnvioRateio = 1;
                rateio.NomeGrupoTarifas = "";

                // IDCliente
                IDCliente = ViewBag._IDCliente;
            }
            else
            {
                // le rateio
                RateioMetodos rateioMetodos = new RateioMetodos();
                rateio = rateioMetodos.ListarPorId(IDRateio);

                // IDCliente
                IDCliente = rateio.IDCliente;
            }

            // le tipos medicao
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposMedicao = new List<ListaTiposDominio>();

            ListaTiposDominio tipo0 = new ListaTiposDominio();
            tipo0.ID = 0;
            tipo0.Descricao = "Energia Elétrica";
            listatiposMedicao.Add(tipo0);

            ListaTiposDominio tipo1 = new ListaTiposDominio();
            tipo1.ID = 1;
            tipo1.Descricao = "Utilidades";
            listatiposMedicao.Add(tipo1);

            ViewBag.listaTipoRateio = listatiposMedicao;

            // lista de medicoes habilitadas do usuario
            List<int> ConfigMedicaoList_Usuario = new List<int>();
            ConfigMedicaoList_Usuario = ViewBag._ConfigMed;

            // verifica se existe lista de medicoes
            if (ConfigMedicaoList_Usuario != null)
            {
                if (ConfigMedicaoList_Usuario.Count() == 0)
                {
                    ConfigMedicaoList_Usuario = null;
                }
            }

            // le grupos de medicoes
            GrupoMedicoesMetodos medGrupoMetodos = new GrupoMedicoesMetodos();
            List<GrupoMedicoesDominio> medGrupos = medGrupoMetodos.ListarPorIDCliente(IDCliente);
            ViewBag.medGrupos = medGrupos;

            // le medicoes do grupo de medicoes
            GrupoMedicoesMedMetodos medGrupoMedMetodos = new GrupoMedicoesMedMetodos();
            List<GrupoMedicoesMedDominio> medGruposMed = medGrupoMedMetodos.ListarPorIDGrupoMedicoes(rateio.IDGrupoMedicoes);

            // cria lista de medicoes deste rateio
            List<int> ConfigMedicaoList_Rateio = new List<int>();

            // verifica se existe lista de medicoes
            if (medGruposMed != null)
            {
                foreach (GrupoMedicoesMedDominio medGrupoMed in medGruposMed)
                {
                    ConfigMedicaoList_Rateio.Add(medGrupoMed.IDMedicao);
                }
            }

            // le medicoes configuradas do rateio para lista
            CliGateGrupoUnidMedicoesMetodos medicoesMetodos = new CliGateGrupoUnidMedicoesMetodos();
            List<CliGateGrupoUnidMedicoesDominio> medicoes = new List<CliGateGrupoUnidMedicoesDominio>();

            List<CliGateGrupoUnidMedicoesDominio> medicoes_utilizadas = new List<CliGateGrupoUnidMedicoesDominio>();

            if (ConfigMedicaoList_Rateio != null)
            {
                // le medicoes de energia habilitadas para o usuario no cliente
                medicoes = medicoesMetodos.ListarPorIDCliente(IDCliente, ConfigMedicaoList_Usuario, 10);

                // seleciona medicoes 
                int contador;

                // percorre lista
                if( medicoes != null )
                {
                    for (contador = 0; contador < medicoes.Count(); contador++)
                    {
                        // verifica se medicao de energia
                        if (rateio.IDTipoRateio == 0 && (medicoes[contador].IDTipoMedicao == TIPO_MEDICAO.ENERGIA || medicoes[contador].IDTipoMedicao == TIPO_MEDICAO.ENERGIA_FORMULA || medicoes[contador].IDTipoMedicao == TIPO_MEDICAO.CICLOMETRO))
                        {
                            // verifica se existe lista de medicoes do rateio
                            if (ConfigMedicaoList_Rateio != null)
                            {
                                // verifica se medicao esta habilitada para o rateio
                                if (ConfigMedicaoList_Rateio.Contains(medicoes[contador].IDMedicao))
                                {
                                    // copia medicao na lista de utilizadas
                                    medicoes_utilizadas.Add(medicoes[contador]);
                                }
                            }
                        }

                        // verifica se medicao de utilidades
                        if (rateio.IDTipoRateio == 1 && (medicoes[contador].IDTipoMedicao == TIPO_MEDICAO.UTILIDADES || medicoes[contador].IDTipoMedicao == TIPO_MEDICAO.UTILIDADES_FORMULA || medicoes[contador].IDTipoMedicao == TIPO_MEDICAO.CICLOMETRO))
                        {
                            // verifica se existe lista de medicoes do rateio
                            if (ConfigMedicaoList_Rateio != null)
                            {
                                // verifica se medicao esta habilitada para o rateio
                                if (ConfigMedicaoList_Rateio.Contains(medicoes[contador].IDMedicao))
                                {
                                    // copia medicao na lista de utilizadas
                                    medicoes_utilizadas.Add(medicoes[contador]);
                                }
                            }
                        }
                    }
                }
            }
            ViewBag.Medicoes = medicoes_utilizadas;

            // le tarifas
            TarifasRateioGruposMetodos tarifaMetodos = new TarifasRateioGruposMetodos();
            List<TarifasRateioGruposDominio> tarifaGrupos = tarifaMetodos.ListarPorIDCliente(IDCliente);
            ViewBag.tarifaGrupos = tarifaGrupos;

            return View(rateio);
        }

        // GET: Tabela Grupo de Medicoes
        public PartialViewResult _Rateio_TabelaGrupoMedicoes(int IDTipoRateio, int IDGrupoMedicoes)
        {

            // le medicoes do grupo de medicoes
            GrupoMedicoesMedMetodos medGrupoMedMetodos = new GrupoMedicoesMedMetodos();
            List<GrupoMedicoesMedDominio> medGruposMed = medGrupoMedMetodos.ListarPorIDGrupoMedicoes(IDGrupoMedicoes);

            CliGateGrupoUnidMedicoesMetodos cliGateGrupoUniMedMetodos = new CliGateGrupoUnidMedicoesMetodos();
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();

            List<CliGateGrupoUnidMedicoesDominio> medicoes_utilizadas = new List<CliGateGrupoUnidMedicoesDominio>();

            int IDCliente = 0;

            // cria lista de medicoes deste rateio
            List<int> ConfigMedicaoList_Rateio = new List<int>();

            // verifica se existe lista de medicoes
            if (medGruposMed != null)
            {
                foreach (GrupoMedicoesMedDominio medGrupoMed in medGruposMed)
                {
                    ConfigMedicaoList_Rateio.Add(medGrupoMed.IDMedicao);
                }

                // verifica se tem 1 pelo menos
                if( medGruposMed.Count > 0 )
                {
                    // medicao
                    MedicoesDominio medicao = medicaoMetodos.ListarPorId(medGruposMed[0].IDMedicao);

                    if (medicao != null)
                    {
                        // IDCliente
                        IDCliente = medicao.IDCliente;

                        // verifica IDCliente
                        if( IDCliente > 0 )
                        {
                            // le cliGateGrupoUniMed
                            List<CliGateGrupoUnidMedicoesDominio> cliGateGrupoUniMed = cliGateGrupoUniMedMetodos.ListarPorIDCliente(IDCliente);

                            // seleciona medicoes 
                            int contador;

                            // percorre lista
                            if (cliGateGrupoUniMed != null)
                            {
                                for (contador = 0; contador < cliGateGrupoUniMed.Count(); contador++)
                                {
                                    // verifica se medicao de energia
                                    if (IDTipoRateio == 0 && (cliGateGrupoUniMed[contador].IDTipoMedicao == TIPO_MEDICAO.ENERGIA || cliGateGrupoUniMed[contador].IDTipoMedicao == TIPO_MEDICAO.ENERGIA_FORMULA || cliGateGrupoUniMed[contador].IDTipoMedicao == TIPO_MEDICAO.CICLOMETRO))
                                    {
                                        // verifica se medicao esta habilitada para o rateio
                                        if (ConfigMedicaoList_Rateio.Contains(cliGateGrupoUniMed[contador].IDMedicao))
                                        {
                                            // copia medicao na lista de utilizadas
                                            medicoes_utilizadas.Add(cliGateGrupoUniMed[contador]);
                                        }
                                    }

                                    // verifica se medicao de utilidades
                                    if (IDTipoRateio == 1 && (cliGateGrupoUniMed[contador].IDTipoMedicao == TIPO_MEDICAO.UTILIDADES || cliGateGrupoUniMed[contador].IDTipoMedicao == TIPO_MEDICAO.UTILIDADES_FORMULA || cliGateGrupoUniMed[contador].IDTipoMedicao == TIPO_MEDICAO.CICLOMETRO))
                                    {
                                        // verifica se medicao esta habilitada para o rateio
                                        if (ConfigMedicaoList_Rateio.Contains(cliGateGrupoUniMed[contador].IDMedicao))
                                        {
                                            // copia medicao na lista de utilizadas
                                            medicoes_utilizadas.Add(cliGateGrupoUniMed[contador]);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            ViewBag.Medicoes = medicoes_utilizadas;

            return PartialView();
        }


        // POST: Rateio - Salvar
        [HttpPost]
        public ActionResult Rateio_Salvar(RateioDominio rateio)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro rateio com o mesmo nome
            RateioMetodos rateioMetodos = new RateioMetodos();
            if (rateioMetodos.VerificarDuplicidade(rateio))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Rateio existente."
                };
            }
            else
            {
                // salva rateio
                rateioMetodos.Salvar(rateio);

                // evento 
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();

                if (rateio.IDRateio > 0)
                {
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.RATEIO, rateio.IDRateio);
                }
                else
                {
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.RATEIO, rateio.IDRateio);
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Rateio - Excluir
        public ActionResult Rateio_Excluir(int IDRateio)
        {
            // apaga o rateio
            RateioMetodos rateioMetodos = new RateioMetodos();
            rateioMetodos.Excluir(IDRateio);

            // evento 
            UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.EXCLUI_CONFIG, TABELA_CONFIG.RATEIO, IDRateio);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}