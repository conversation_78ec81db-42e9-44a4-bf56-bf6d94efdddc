﻿using Rotativa.Options;
using SmartEnergy.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Mime;
using System.Web.Hosting;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public class EnviaRateiosController : Controller
    {
        // Estatísticas
        int Num_Relatorios = 0;

        // data hora atual
        DateTime DataHoraAtual = DateTime.Now;


        // GET: EnviaRateios
        public ActionResult EnviaRateios()
        {
            // log
            LogMessage("EnviaRateios 1.00");

            // le clientes
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> clientes = clienteMetodos.ListarTodos();

            // log
            LogMessage("Leu clientes");

            //
            // Usuários
            //

            // le todos usuários
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            List<UsuarioDominio> usuarios = usuarioMetodos.ListarTodos();

            // log
            LogMessage("Leu usuários");

            // log
            LogMessage("------");

            if (clientes != null && usuarioMetodos != null)
            {
                // estatisticas
                Num_Relatorios = 0;
                DateTime DataHoraInicio = DateTime.Now;

                // percorre clientes
                foreach (ClientesDominio cliente in clientes)
                {
                    // envia rateios do clientes
                    EnviaRateio(cliente, usuarios);
                }

                // diferença de tempo
                DateTime datahora_agora = System.DateTime.Now;
                TimeSpan diff = datahora_agora.Subtract(DataHoraInicio);

                string mensagem = string.Format("Número de Rateios enviados = {0} em {1:00}'{2:00}\"{3:000}", Num_Relatorios, diff.Minutes, diff.Seconds, diff.Milliseconds);
                string mensagem_erro = "Todos Rateios gerados com sucesso.";
                string relatorios_erro = "";

                // envia email estatistica
                EnviaEmailEstatistica(mensagem, mensagem_erro, relatorios_erro);

                // log
                LogMessage(mensagem);

                // log
                LogMessage("------");
            }

            // retorna status
            var returnedData = new
            {
                status = true
            };

            // relatorio
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        public void EnviaRateio(ClientesDominio cliente, List<UsuarioDominio> usuarios)
        {

            // verifica se cliente possui gestor
            string LogoConsultor = "LogoSmartEnergy.png";

            if (cliente.IDConsultor > 0)
            {
                // logo do gestor
                UsuarioDominio consultor = usuarios.Find(u => u.IDUsuario == cliente.IDConsultor);

                if (consultor != null)
                {
                    // copia logo do gestor
                    if (consultor.LogoConsult != null)
                    {
                        if (consultor.LogoConsult.Length > 0)
                        {
                            LogoConsultor = consultor.LogoConsult;
                        }
                    }
                }
            }

            // percorre usuarios
            foreach (UsuarioDominio usuario in usuarios)
            {
                // permite apenas usuário cliente
                if (usuario.IDTipoAcesso != TIPO_ACESSO.CLIENTE_ADMIN && usuario.IDTipoAcesso != TIPO_ACESSO.CLIENTE_OPER)
                {
                    continue;
                }

                // verifica se usuário quer receber rateio
                if (usuario.Rateio == 0)
                {
                    continue;
                }

                // monta lista de medições do usuário
                string ConfigMed = usuario.ConfigMed;
                List<int> ConfigMedList = null;

                // copia medicoes para lista
                if (!String.IsNullOrEmpty(ConfigMed))
                {
                    ConfigMedList = ConfigMed.Split('/')
                        .Select(possibleIntegerAsString =>
                        {
                            int parsedInteger = 0;
                            bool isInteger = int.TryParse(possibleIntegerAsString, out parsedInteger);
                            return new { isInteger, parsedInteger };
                        })
                        .Where(tryParseResult => tryParseResult.isInteger)
                        .Select(tryParseResult => tryParseResult.parsedInteger)
                        .ToList();
                }

                if (ConfigMedList != null)
                {
                    // le rateios do cliente que o usuário tem direito
                    RateioMetodos rateioMetodos = new RateioMetodos();
                    List<RateioDominio> listaRateio = rateioMetodos.ListarPorIDCliente(cliente.IDCliente, ConfigMedList);

                    if (listaRateio != null)
                    {
                        // percorre rateios
                        foreach (RateioDominio rateio in listaRateio)
                        {
                            // rateio para o usuário
                            LogMessage(string.Format(">>>>>> Cliente [{0:000000}] {1} || Rateio [{2:000000}] {3} || Dia Fat {4} || Dia Envio {5} || Usuário [{6:000000}] {7}", cliente.IDCliente, cliente.Nome, rateio.IDRateio, rateio.Nome, rateio.DiaInicioFat, rateio.DiaEnvioRateio, usuario.IDUsuario, usuario.NomeUsuario));

                            // calculo último dia do rateio 
                            // configuração permite dia inicio até o dia 28, dia fim é um mês depois
                            // exemplo: Dia inicio = 16 || Inicio 16/01/2021 00:00 Fim 16/02/2021 00:00 e dispara o envio no dia 16/02 
                            int DiaFimFat = rateio.DiaInicioFat;

                            // verifica se é o dia fim
                            if (DataHoraAtual.Day != rateio.DiaEnvioRateio)
                            {
                                // não é hora de enviar rateio
                                LogMessage(string.Format(">>>>>> Não é dia de enviar o Rateio [{0:000000}] {1} || Dia Envio {2}", rateio.IDRateio, rateio.Nome, rateio.DiaEnvioRateio));

                                // não é o dia para disparar este rateio
                                continue;
                            }

                            // datas do rateio
                            DateTime DataFim = new DateTime(DataHoraAtual.Year, DataHoraAtual.Month, DiaFimFat, 0, 0, 0);
                            DateTime DataIni = DataFim.AddMonths(-1);

                            // le medicoes do rateio
                            GrupoMedicoesMedMetodos medGrupoMetodos = new GrupoMedicoesMedMetodos();
                            List<GrupoMedicoesMedDominio> medGrupos = medGrupoMetodos.ListarPorIDGrupoMedicoes(rateio.IDGrupoMedicoes);

                            // tarifa do rateio
                            TarifasRateioMetodos tarifaMetodos = new TarifasRateioMetodos();
                            TarifasRateioDominio tarifa = tarifaMetodos.MaisRecente(rateio.IDCliente, rateio.IDGrupoTarifas, DataFim);

                            // valores
                            RATEIO_TOTAL rateio_total = new RATEIO_TOTAL();
                            List<RATEIO_MEDICOES> rateio_medicoes = new List<RATEIO_MEDICOES>();

                            // percorre medicoes
                            if (medGrupos != null)
                            {
                                // percorre medicoes do rateio
                                foreach (GrupoMedicoesMedDominio medGrupo in medGrupos)
                                {
                                    // verifica se medicao NAO esta habilitada para o usuario
                                    if (!ConfigMedList.Contains(medGrupo.IDMedicao))
                                    {
                                        // pula medicao
                                        continue;
                                    }

                                    // rateio
                                    RATEIO_MEDICOES rat = new RATEIO_MEDICOES();

                                    // medicao
                                    rat.IDMedicao = medGrupo.IDMedicao;
                                    rat.Nome_Medicao = "";
                                    rat.PontoMedicao = "";
                                    rat.UnidadeConsumo = "kWh";
                                    rat.CicloIni_Valor = "--------";
                                    rat.CicloIni_DataHora = DataIni;
                                    rat.CicloFim_Valor = "--------";
                                    rat.CicloFim_DataHora = DataFim;

                                    // calcula rateio da medicao
                                    MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                                    MedicoesDominio medicao = medicaoMetodos.ListarPorId(medGrupo.IDMedicao);

                                    if (medicao != null)
                                    {
                                        // verifica se medição é do tipo do rateio energia elétrica
                                        if (rateio.IDTipoRateio == 0)
                                        {
                                            if (medicao.IDTipoMedicao != TIPO_MEDICAO.ENERGIA && medicao.IDTipoMedicao != TIPO_MEDICAO.ENERGIA_FORMULA && medicao.IDTipoMedicao != TIPO_MEDICAO.CICLOMETRO)
                                            {
                                                // pula medicao
                                                continue;
                                            }
                                        }

                                        // verifica se medição é do tipo do rateio utilidades
                                        if (rateio.IDTipoRateio == 1)
                                        {
                                            if (medicao.IDTipoMedicao != TIPO_MEDICAO.UTILIDADES && medicao.IDTipoMedicao != TIPO_MEDICAO.UTILIDADES_FORMULA && medicao.IDTipoMedicao != TIPO_MEDICAO.CICLOMETRO)
                                            {
                                                // pula medicao
                                                continue;
                                            }
                                        }

                                        // nome medicao
                                        rat.Nome_Medicao = medicao.Nome;

                                        // ponto medicao
                                        rat.PontoMedicao = medicao.PontoMedicao;

                                        // unidade de consumo
                                        rat.UnidadeConsumo = "kWh";

                                        if (rateio.IDTipoRateio == 1)
                                        {
                                            rat.UnidadeConsumo = medicao.UnidadeGrandeza;
                                        }

                                        // numero de medicoes
                                        rateio_total.NumMedicoes += 1;

                                        // consumo da medicao
                                        double consumo = 0.0;

                                        // verifica se medição é do tipo do rateio energia elétrica
                                        if (rateio.IDTipoRateio == 0)
                                        {
                                            // verifica se medição é ciclometro
                                            if (medicao.IDTipoMedicao == TIPO_MEDICAO.CICLOMETRO)
                                            {
                                                GG_Metodos ggMetodos = new GG_Metodos();
                                                consumo = ggMetodos.ConsumoTotalPeriodo_Ciclometro(rateio.IDCliente, rat.IDMedicao, DataIni, DataFim, ref rat);
                                            }
                                            else
                                            {
                                                EN_Metodos enMetodos = new EN_Metodos();
                                                consumo = enMetodos.ConsumoTotalPeriodo(rateio.IDCliente, rat.IDMedicao, DataIni, DataFim);
                                            }
                                        }

                                        // verifica se medição é do tipo do rateio utilidades
                                        if (rateio.IDTipoRateio == 1)
                                        {
                                            // verifica se medição é ciclometro
                                            if (medicao.IDTipoMedicao == TIPO_MEDICAO.CICLOMETRO)
                                            {
                                                GG_Metodos ggMetodos = new GG_Metodos();
                                                consumo = ggMetodos.ConsumoTotalPeriodo_Ciclometro(rateio.IDCliente, rat.IDMedicao, DataIni, DataFim, ref rat);
                                            }
                                            else
                                            {
                                                GG_Metodos ggMetodos = new GG_Metodos();
                                                consumo = ggMetodos.ConsumoTotalPeriodo(rateio.IDCliente, rat.IDMedicao, DataIni, DataFim);
                                            }
                                        }

                                        // consumo
                                        rateio_total.Consumo += consumo;
                                        rat.Consumo = consumo;

                                        // tarifa
                                        rateio_total.Tarifa = tarifa.Tarc;
                                        rat.Tarifa = tarifa.Tarc;

                                        // valor
                                        rat.Valor = consumo * tarifa.Tarc;

                                        // total
                                        rateio_total.Valor += rat.Valor;

                                        // coloca na lista
                                        rateio_medicoes.Add(rat);
                                    }
                                }

                                // ordenar por nome
                                List<RATEIO_MEDICOES> sorted_rateio_medicoes = rateio_medicoes.OrderBy(x => x.Nome_Medicao).ToList();

                                // fim da rateio, enviar
                                Rateio_PDF(cliente, rateio, usuario, LogoConsultor, DataIni, DataFim, rateio_total, sorted_rateio_medicoes);

                            }
                        }
                    }
                }
            }
        }



        // Rateio PDF
        private bool Rateio_PDF(ClientesDominio cliente, RateioDominio rateio, UsuarioDominio usuario, string LogoConsultor, DateTime DataIni, DateTime DataFim, RATEIO_TOTAL rateio_total, List<RATEIO_MEDICOES> rateio_medicoes)
        {
            try
            {

                //
                // PREPARA RELATÓRIO
                //

                // log
                LogMessage(string.Format(">>>>>> Enviando Rateio para Usuário [{0:000000}] {1} || {2}",  usuario.IDUsuario, usuario.NomeUsuario, usuario.Email));

                //
                // cabeçalho
                //

                ViewBag._LogoConsultor = LogoConsultor;

                ViewBag.ClienteNome = cliente.Nome;
                ViewBag.NomeRateio = rateio.Nome;

                ViewBag.DataTextoAtualIni = string.Format("{0:d}", DataIni);
                ViewBag.DataTextoAtualFim = string.Format("{0:d}", DataFim);

                ViewBag.DataAtual = string.Format("{0:Y}", DataFim);

                //
                // relatório
                //

                ViewBag.rateio_medicoes = rateio_medicoes;
                ViewBag.rateio_total = rateio_total;



                // nome do arquivo
                string nomeArquivo = string.Format("Rateio_{0:000000}_{1:000000}_{2:yyyyMMdd}", cliente.IDCliente, rateio.IDRateio, DataFim);

                // partial view
                string viewPartial = "_Rateio_PDF";

                // buffer
                byte[] dataBuffer;

                // tipo do anexo
                string attachmentType = MediaTypeNames.Application.Pdf;

                //
                // PDF
                //

                // extesão do arquivo
                nomeArquivo += ".pdf";

                // tipo do anexo
                attachmentType = MediaTypeNames.Application.Pdf;


                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // PartialView como PDF
                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Portrait,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(10, 10, 0, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                dataBuffer = pdfResult.BuildFile(this.ControllerContext);

                // envia email
                EnviaEmail(usuario, nomeArquivo, attachmentType, dataBuffer);

                // log
                LogMessage("------");

                // estatística
                Num_Relatorios++;

                // ok
                return (false);

            }
            catch (Exception ex)
            {
                LogMessage("[ERRO] " + ex.Message);
            }

            // erro
            return (true);
        }


        // envia email
        private void EnviaEmail(UsuarioDominio usuario, string attachmentName, string attachmentType, byte[] attachment)
        {
            // assunto
            string assunto = "[Smart Energy] Rateio: " + ViewBag.NomeRateio + " - " + ViewBag.ClienteNome;

            // envia EMAIL
            var emailTemplate = "EnviaRateiosEmail";

            var message = EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.UsuarioNome", usuario.NomeUsuario);
            message = message.Replace("ViewBag.LogoConsultor", ViewBag._LogoConsultor);
            message = message.Replace("ViewBag.NomeRateio", ViewBag.NomeRateio);
            message = message.Replace("ViewBag.DataAtual", ViewBag.DataAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);

            EmailServices.SendEmail(usuario.Email, assunto, message, attachmentName, attachmentType, attachment);

            // log
            LogMessage(string.Format(">>>>>> Enviou email para usuário [{0:000000}] {1}", usuario.IDUsuario, usuario.Email));

            return;
        }

        public static string EMailTemplate(string template)
        {
            var templateFilePath = HostingEnvironment.MapPath("~/Content/emailTemplates/") + template + ".cshtml";
            StreamReader objstreamreaderfile = new StreamReader(templateFilePath);
            var body = objstreamreaderfile.ReadToEnd();
            objstreamreaderfile.Close();
            return body;
        }


        // envia email estatistica
        private void EnviaEmailEstatistica(string mensagem, string mensagem_erro, string relatorios_erro)
        {
            // assunto
            string assunto = "[Smart Energy] Envio de Rateios";

            // envia EMAIL
            var emailTemplate = "EnviaRelatoriosEstatisticaEmail";

            var message = EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.UsuarioNome", "Junior");
            message = message.Replace("ViewBag.LogoConsultor", "LogoSmartEnergy.png");
            message = message.Replace("ViewBag.Mensagem", mensagem);
            message = message.Replace("ViewBag.ErroMensagem", mensagem_erro);
            message = message.Replace("ViewBag.RelatoriosErro", relatorios_erro);

            EmailServices.SendEmail("<EMAIL>", assunto, message, "", "", null);

            // log
            LogMessage(">>>>>> Enviou email estatísticas");

            return;
        }


        private void LogMessage(string msg, string arquivo = null)
        {
            // nome de arquivo padrao
            string nome_arquivo = "LogEnviaRateio_" + String.Format("{0:yyyyMMdd}", DateTime.Today) + ".txt";

            // verifica se tem nome de arquivo
            if (arquivo != null)
            {
                nome_arquivo = arquivo;
            }

            // subdiretorio LOG
            string exeRuntimeDirectory = Server.MapPath("~/EnviaRelatorios");
            string subDirectory = System.IO.Path.Combine(exeRuntimeDirectory, "Log");
            if (!System.IO.Directory.Exists(subDirectory))
            {
                // Output directory does not exist, so create it.
                System.IO.Directory.CreateDirectory(subDirectory);
            }

            // caminho completo
            string path = System.IO.Path.Combine(subDirectory, nome_arquivo);

            // abre arquivo
            System.IO.StreamWriter sw = System.IO.File.AppendText(path);

            try
            {
                // diferença de tempo
                DateTime datahora_agora = System.DateTime.Now;
                TimeSpan diff = datahora_agora.Subtract(DataHoraAtual);
                DataHoraAtual = datahora_agora;

                // grava log
                string logLine = System.String.Format(
                    "{0:dd/MM/yyyy HH:mm:ss.fff}: [{1:00}\"{2:000}] {3}", datahora_agora, diff.Seconds, diff.Milliseconds, msg);
                sw.WriteLine(logLine);
                sw.Flush();
            }
            finally
            {
                sw.Close();
            }
        }
    }
}