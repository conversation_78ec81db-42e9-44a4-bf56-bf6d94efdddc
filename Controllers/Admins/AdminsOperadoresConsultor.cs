﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class AdminsController
    {
        // Listas utilizadas
        private void PreparaListas_OperadoresConsultor()
        {
            // le tipos medicao
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposMsgEmail = listatiposMetodos.ListarTodos("TipoMsgEmail");
            ViewBag.listatiposMsgEmail = listatiposMsgEmail;

            // le tipos acesso
            List<ListaTiposDominio> listatiposAcesso = new List<ListaTiposDominio>()
	        {
                new ListaTiposDominio(){ ID = 7, Descricao = "Gestor - Administrador"},
	            new ListaTiposDominio(){ ID = 8, Descricao = "Gestor - Operador"}
	        };
            ViewBag.listaTipoAcesso = listatiposAcesso;

            // tipos expirar
            List<ListaTiposDominio> listaExpirar = new List<ListaTiposDominio>();

            for (int i = 1; i <= 12; i++)
            {
                ListaTiposDominio tipo = new ListaTiposDominio();

                // ID
                tipo.ID = i;

                // descrição
                if (i == 0)
                {
                    tipo.Descricao = "Senha não expira";
                }
                else if (i == 1)
                {
                    tipo.Descricao = "1 mês";
                }
                else if (i == 2)
                {
                    tipo.Descricao = "45 dias";
                }
                else
                {
                    tipo.Descricao = string.Format("{0} meses", i);
                }

                // coloca na lista
                listaExpirar.Add(tipo);
            }

            ViewBag.listaExpirar = listaExpirar;

            // tipos idioma
            List<IdiomaDominio> listaTiposIdioma = listatiposMetodos.ListarTodos_Idioma();
            ViewBag.listaTiposIdioma = listaTiposIdioma;

            // le tipos bloqueio
            List<ListaTiposDominio> listaTiposBloqueio = listatiposMetodos.ListarTodos("TipoBloqueio", false);
            ViewBag.listaTiposBloqueio = listaTiposBloqueio;

            // le tipos segundo fator
            List<ListaTiposDominio> listaTiposSegundoFator = listatiposMetodos.ListarTodos("TipoSegundoFator", false);
            ViewBag.listaTiposSegundoFator = listaTiposSegundoFator;

            return;
        }

        // GET: Configuracao Operadores do Consultor
        public ActionResult OperadoresConsultor(int IDCliente)
        {
            // tela de ajuda - consultores
            CookieStore.SalvaCookie_String("PaginaAjuda", "Consultores");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Admins");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_OperadoresConsultor();

            // le usuarios
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            var listaUsuarios = usuarioMetodos.ListarOperadoresConsultor(IDCliente);
            return View(listaUsuarios);
        }

        // GET: Configuracao Operadores do Consultor - Editar
        public ActionResult OperadoresConsultor_Editar(int IDUsuario)
        {
            // tela de ajuda - consultores
            CookieStore.SalvaCookie_String("PaginaAjuda", "Consultores_Configuracao");

            // pagina anterior
            ViewBag.returnUrl = Request.UrlReferrer;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Admins");

            // le cookies
            LeCookies_SmartEnergy();

            // IDConsultor do cliente
            int IDConsultor = ViewBag._IDConsultor;

            // le grupos de painéis do cliente
            DashboardGrupoMetodos dashboardGrupoMetodos = new DashboardGrupoMetodos();
            List<DashboardGrupoDominio> listaDashBoardGrupo = dashboardGrupoMetodos.ListarPorIdConsultor(IDConsultor);
            ViewBag.listaDashBoardGrupo = listaDashBoardGrupo;

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_OperadoresConsultor();

            // verifica se adicionando
            UsuarioDominio usuario = new UsuarioDominio();
            if (IDUsuario == 0)
            {
                // zera usuario com default
                usuario.IDCliente = IDConsultor;
                usuario.IDTipoAcesso = ISUSER.Consultor_Oper;

                usuario.NomeUsuario = "";
                usuario.Apelido = "";
                usuario.Login = "";

                usuario.Senha = string.Format("Mudar.{0}", DateTime.Now.Year);
                usuario.SenhaAtual = usuario.Senha;
                usuario.IDTipoSegundoFator = TIPO_SEGUNDO_FATOR.NaoUtilizar;
                usuario.CodigoSegundoFator = "";
                usuario.ExpirarSegundoFator = new DateTime(2000, 1, 1, 0, 0, 0);

                usuario.LogoConsult = "";

                usuario.Email = "";
                usuario.EmailAtual = "confirmar";
                usuario.EmailConfirmado = 0;
                usuario.Telefone = "";
                usuario.Ramal = "";
                usuario.Celular = "";
                usuario.Cargo = "";
                usuario.Departamento = "";
                usuario.IDIdioma = 0;
                usuario.IDTema = 0;
                usuario.API_Key = "";
                usuario.API_Limite = 100;

                usuario.Bloqueio = 0;
                usuario.BloqueioEm = DateTime.Now;
                usuario.ExpirarMeses = 6;
                usuario.ExpirarEm = DateTime.Now.AddYears(1);
                usuario.ShowIntro = 1;
                usuario.AceiteLGPD = 0;

                usuario.IDDashboardGrupo = 0;

                usuario.Diario = 0;
                usuario.Semanal = 0;
                usuario.Mensal = 0;
                usuario.DemAtv = 0;
                usuario.FatPot = 0;
                usuario.Consumo = 0;
                usuario.Supervisao = 0;
                usuario.Util_Anal_Ciclo = 0;
                usuario.Gestao_Contrato = 0;
                usuario.Gestao_Energia = 0;

                usuario.ConfigGrupo = "";
                usuario.ConfigGrupoEmail = "";
                usuario.ConfigUnid = "";
                usuario.ConfigUnidEmail = "";
                usuario.ConfigMed = "";
                usuario.ConfigMedEmail = "";

                usuario.View_Analises = 1;
                usuario.View_Financas = 1;
                usuario.View_KPI = 1;
                usuario.View_Ranking = 1;
                usuario.View_Rateio = 1;
                usuario.View_Metas = 1;
                usuario.View_Configuracao = 1;
                usuario.View_ConfiguracaoRemota = 0;
                usuario.View_PerfilUsuario = 1;
            }
            else
            {
                // le usuario
                UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
                usuario = usuarioMetodos.ListarPorId(IDUsuario);
            }

            // le usuario medicoes
            UsuarioMedicaoMetodos usuarioMedicaoMetodos = new UsuarioMedicaoMetodos();
            List<UsuarioMedicaoDominio> usuarioMedicoes = usuarioMedicaoMetodos.ListarTodasMedicoesIDUsuario(IDUsuario);
            ViewBag.usuarioMedicoes = usuarioMedicoes;

            // cria lista de medicoes habilitadas para o usuario
            List<int> ConfigMedicaoList = new List<int>();

            foreach (UsuarioMedicaoDominio usuarioMedicao in usuarioMedicoes)
            {
                ConfigMedicaoList.Add(usuarioMedicao.IDMedicao);
            }

            // le medicoes configuradas do usuario para lista
            var medicoesMetodos = new CliGateGrupoUnidMedicoesMetodos();
            var medicoes = new List<CliGateGrupoUnidMedicoesDominio>();

            if (ConfigMedicaoList != null)
                medicoes = medicoesMetodos.ListarPorIDCliente(-1, ConfigMedicaoList);
            ViewBag.Medicoes = medicoes;

            // le clientes associados ao consultor - ativos
            List<CliGateGrupoUnidMedicoesDominio> medicoes2 = new List<CliGateGrupoUnidMedicoesDominio>();
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            List<ClientesDominio> clientes = clientesMetodos.ListarPorIDConsultor(usuario.IDCliente, 1);

            foreach (ClientesDominio cliente in clientes)
            {
                // le medicoes para lista
                List<CliGateGrupoUnidMedicoesDominio> medicoes3 = new List<CliGateGrupoUnidMedicoesDominio>();
                medicoes3 = medicoesMetodos.ListarPorIDCliente(cliente.IDCliente);

                foreach (CliGateGrupoUnidMedicoesDominio medicao in medicoes3)
                {
                    // coloca na lista
                    medicoes2.Add(medicao);
                }
            }

            ViewBag.Medicoes2 = medicoes2;

            return View(usuario);
        }

        // POST: Configuracao Operadores do Consultor - Salvar
        [HttpPost]
        public ActionResult OperadoresConsultor_Salvar(UsuarioDominio usuario, List<UsuarioMedicaoDominio> usuarioMedicoes)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // protege contra IDCliente inválido
            if (usuario.IDCliente < 0)
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Erro ao salvar. Entrar em contato com o suporte e informar código -10"
                };

                // retorna status
                return Json(returnedData);
            }

            // protege contra salvar como Admin
            // só pode ser admin Junior e Sérgio, se quiser ter outro tem que fazer via banco de dados
            if (usuario.IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN && (usuario.IDUsuario != 1 && usuario.IDUsuario != 4165))
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Erro ao salvar. Entrar em contato com o suporte e informar código 0"
                };

                // retorna status
                return Json(returnedData);
            }

            // verifica se a senha mudou
            if (usuario.Senha != usuario.SenhaAtual)
            {
                // senhas usadas
                SenhasUsadasMetodos senhasUsadasMetodos = new SenhasUsadasMetodos();

                // verifica se senha já foi usada
                if (senhasUsadasMetodos.VerificaSenhaUsada(usuario.IDUsuario, usuario.Senha))
                {
                    returnedData = new
                    {
                        status = "ERRO",
                        erro = "Você está repetindo alguma das últimas 10 senhas utilizadas.<br><br>Por favor cadastrar outra senha."
                    };

                    // retorna status
                    return Json(returnedData);
                }

                // salva senha usada
                senhasUsadasMetodos.Inserir(usuario.IDUsuario, usuario.Senha);
            }

            // le cookies
            LeCookies_SmartEnergy();

            // verifica se existe outro usuario com o mesmo login
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            if (usuarioMetodos.VerificarDuplicidade(usuario))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Login existente."
                };

                // retorna status
                return Json(returnedData);
            }
            else
            {
                // verifica se mudou email
                bool mudou_email = false;

                // verifica se o email mudou
                if (usuario.Email != usuario.EmailAtual)
                {
                    // mudou email
                    mudou_email = true;
                }

                // verifica se gestor admin
                if (usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR_ADMIN)
                {
                    // admin sempre pode ver tudo
                    usuario.View_Analises = 1;
                    usuario.View_Financas = 1;
                    usuario.View_KPI = 1;
                    usuario.View_Ranking = 1;
                    usuario.View_Rateio = 1;
                    usuario.View_Metas = 1;
                    usuario.View_Configuracao = 1;
                    usuario.View_ConfiguracaoRemota = 1;
                    usuario.View_PerfilUsuario = 1;
                }

                // salva usuario 
                usuarioMetodos.Salvar(usuario);

                // pego IDUsuario novamente (pois pode ter sido insercao)
                UsuarioDominio user = usuarioMetodos.ListarPorLogin(usuario.Login);

                // salva 
                if (user != null)
                {
                    // exclui todos as medicoes deste usuario
                    UsuarioMedicaoMetodos usuarioMedicaoMetodos = new UsuarioMedicaoMetodos();
                    usuarioMedicaoMetodos.Excluir(user.IDUsuario);

                    // IDCliente eh o mesmo do IDUsuario consultou que esta logado
                    user.IDCliente = ViewBag._IDConsultor;

                    // salva usuario 
                    usuarioMetodos.Salvar(user);

                    // verifica se o email mudou
                    if (mudou_email)
                    {
                        // enviar email de confirmação
                        EnviarConfirmacaoEmail(user.IDUsuario, user.NomeUsuario, user.Email);
                    }

                    // salva usuariomedicao
                    if (usuarioMedicoes != null)
                    {
                        // percorre lista e salva
                        foreach (UsuarioMedicaoDominio usuarioMedicao in usuarioMedicoes)
                        {
                            // le cliente e gateway da medicao
                            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                            MedicoesDominio medicao = medicaoMetodos.ListarPorId(usuarioMedicao.IDMedicao);

                            if (medicao != null)
                            {
                                // preenche campos que faltam
                                usuarioMedicao.IDUsuario = user.IDUsuario;
                                usuarioMedicao.IDCliente = medicao.IDCliente;
                                usuarioMedicao.IDGateway = medicao.IDGateway;


                                // utiliza os flags do usuario
                                usuarioMedicao.RelatDiario = false;
                                usuarioMedicao.RelatSemanal = false;
                                usuarioMedicao.RelatMensal = false;
                                usuarioMedicao.RelatRanking = false;
                                usuarioMedicao.MsgGeren = false;
                                usuarioMedicao.MsgProcessa = false;

                                // verifico os flags de relatorio periodico
                                if (user.DemAtv == 1 || user.FatPot == 1 || user.Consumo == 1 || user.Supervisao == 1 || user.Util_Anal_Ciclo == 1)
                                {
                                    // diario
                                    if (user.Diario == 1)
                                        usuarioMedicao.RelatDiario = true;

                                    // semanal
                                    if (user.Semanal == 1)
                                        usuarioMedicao.RelatSemanal = true;

                                    // mensal
                                    if (user.Mensal == 1)
                                        usuarioMedicao.RelatMensal = true;
                                }

                                // verifico o flag de relatorio ranking
                                if (user.Ranking == 1)
                                {
                                    // ranking
                                    usuarioMedicao.RelatRanking = true;
                                }

                                // verifico os flags de mensagens de gerenciamento
                                if (user.MsgCliMed_Dem == 1 || user.MsgCliMed_Cons == 1 || user.MsgCliMed_FatPot == 1)
                                {
                                    // mensagens de gerenciamento proveniente da medicao (dem alta, baixa, fat pot, cons alto, baixo)
                                    usuarioMedicao.MsgGeren = true;
                                }

                                // verifico os flags de processamento
                                if (user.MsgCliEve == 1)
                                {
                                    // mensagens de gerenciamento proveniente do processamento dos arquivos recebidos
                                    usuarioMedicao.MsgProcessa = true;
                                }


                                // salva
                                usuarioMedicaoMetodos.Salvar(usuarioMedicao);
                            }
                        }
                    }

                    //
                    // ATUALIZA CLIENTES E MEDICOES HABILITADOS
                    //
                    // uso o UsuariosMedicoes para atualizar o campo CONFIGMED / CONFIGMEDEMAIL e flags de recebimento de email em Usuarios
                    if (isUser.isConsultor(user.IDTipoAcesso))
                    {
                        // atualizo usuario
                        usuarioMedicaoMetodos.AtualizarUsuariosMedicoes(user.IDUsuario);

                        // atualizo CtrEmailAdm
                        usuarioMedicaoMetodos.AtualizarUsuariosMedicoesAdmin(user.IDUsuario);
                    }
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuracao Operadores do Consultor - Excluir
        public ActionResult OperadoresConsultor_Excluir(int IDUsuario)
        {
            // apaga o administrador
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            usuarioMetodos.Excluir(IDUsuario);

            UsuarioMedicaoMetodos usuarioMedicaoMetodos = new UsuarioMedicaoMetodos();
            usuarioMedicaoMetodos.Excluir(IDUsuario);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}