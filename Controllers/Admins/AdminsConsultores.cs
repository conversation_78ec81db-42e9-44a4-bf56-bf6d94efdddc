﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Security.Cryptography;
using System.Web.Hosting;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class AdminsController
    {
        // GET: Configuracao Consultores
        public ActionResult Consultores()
        {
            // tela de ajuda - consultores
            CookieStore.SalvaCookie_String("PaginaAjuda", "Consultores");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Admins");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_Consultores();

            // le consultores
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            List<UsuarioDominio> listaConsultores = usuarioMetodos.ListarTodosConsultores();

            // numero de clientes por consultor
            if(listaConsultores != null)
            {
                foreach(UsuarioDominio consultor in listaConsultores)
                {
                    // numero de clientes do gestor
                    ClientesMetodos clienteMetodos = new ClientesMetodos();
                    consultor.NumClientes = clienteMetodos.NumClientesConsultor(consultor.IDUsuario);

                    // numero de gateways do gestor
                    SupervGatewaysMetodos supervGatewaysMetodos = new SupervGatewaysMetodos();
                    consultor.NumGateways = supervGatewaysMetodos.NumGatewaysGestor(consultor.IDUsuario);
                }
            }

            return View(listaConsultores);
        }

        // GET: Configuracao Consultores - Editar
        public ActionResult Consultores_Editar(int IDConsultor)
        {
            // tela de ajuda - consultores
            CookieStore.SalvaCookie_String("PaginaAjuda", "Consultores_Configuracao");

            // pagina anterior
            ViewBag.returnUrl = Request.UrlReferrer;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Admins");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // usuário
            UsuarioDominio usuario = new UsuarioDominio();

            // verifica se adicionando
            if (IDConsultor == 0)
            {
                // zera usuário com default
                usuario.IDCliente = 0;
                usuario.IDTipoAcesso = ISUSER.Consultor;

                usuario.NomeUsuario = "";
                usuario.Apelido = "";
                usuario.Login = "";

                usuario.Senha = string.Format("Mudar.{0}", DateTime.Now.Year);
                usuario.SenhaAtual = usuario.Senha;
                usuario.IDTipoSegundoFator = TIPO_SEGUNDO_FATOR.NaoUtilizar;
                usuario.CodigoSegundoFator = "";
                usuario.ExpirarSegundoFator = new DateTime(2000, 1, 1, 0, 0, 0);

                usuario.LogoConsult = "LogoSmartEnergy85.png";

                usuario.Email = "";
                usuario.EmailAtual = "confirmar";
                usuario.EmailConfirmado = 0;
                usuario.Telefone = "";
                usuario.Ramal = "";
                usuario.Celular = "";
                usuario.Cargo = "";
                usuario.Departamento = "";
                usuario.IDIdioma = 0;
                usuario.IDTema = 0;
                usuario.API_Key = "";
                usuario.API_Limite = 100;

                usuario.Bloqueio = 0;
                usuario.BloqueioEm = DateTime.Now;
                usuario.ExpirarMeses = 6;
                usuario.ExpirarEm = DateTime.Now.AddYears(1);
                usuario.ShowIntro = 1;
                usuario.AceiteLGPD = 0;

                usuario.IDDashboardGrupo = 0;

                usuario.Diario = 0;
                usuario.Semanal = 0;
                usuario.Mensal = 0;
                usuario.DemAtv = 0;
                usuario.FatPot = 0;
                usuario.Consumo = 0;
                usuario.Supervisao = 0;
                usuario.Util_Anal_Ciclo = 0;
                usuario.Gestao_Contrato = 0;
                usuario.Gestao_Energia = 0;

                usuario.Ranking = 0;
                usuario.Rateio = 0;

                usuario.ConfigGrupo = "";
                usuario.ConfigGrupoEmail = "";
                usuario.ConfigUnid = "";
                usuario.ConfigUnidEmail = "";
                usuario.ConfigMed = "";
                usuario.ConfigMedEmail = "";

                usuario.View_Analises = 1;
                usuario.View_Financas = 1;
                usuario.View_KPI = 1;
                usuario.View_Ranking = 1;
                usuario.View_Rateio = 1;
                usuario.View_Metas = 1;
                usuario.View_Configuracao = 1;
                usuario.View_ConfiguracaoRemota = 1;
                usuario.View_PerfilUsuario = 1;

                usuario.Contato_Email = "<EMAIL>";
                usuario.Contato_Telefone1 = "(11) 5080-8200";
                usuario.Contato_Telefone2 = "";
                usuario.Contato_Telefone3 = "";
                usuario.Contato_Celular = "(11) 9.7598-5359";
                usuario.Contato_Site = "https://www.gestal.com";
                usuario.Contato_Endereco = "Rua Borges Lagoa, 190";
                usuario.Contato_CEP = "04038-000";
                usuario.Contato_IDPais = 1;
                usuario.Contato_IDEstado = 26;
                usuario.Contato_IDCidade = 5270;
                usuario.Contato_Latitude = 0;
                usuario.Contato_Longitude = 0;

                usuario.Demo_Habilitado = false;
                usuario.Demo_IDUsuario = 0;
                usuario.Demo_Login = "";
                usuario.Demo_Senha = "";
            }
            else
            {
                // le usuario
                UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
                usuario = usuarioMetodos.ListarPorId(IDConsultor);

                // contato gestor
                ConsultoresMetodos gestorMetodos = new ConsultoresMetodos();
                ConsultoresDominio gestor = gestorMetodos.ListarPorId(IDConsultor);

                if (gestor != null)
                {
                    // copia Contato
                    usuario.Contato_Email = gestor.Email;
                    usuario.Contato_Telefone1 = gestor.Telefone1;
                    usuario.Contato_Telefone2 = gestor.Telefone2;
                    usuario.Contato_Telefone3 = gestor.Telefone3;
                    usuario.Contato_Celular = gestor.Celular;
                    usuario.Contato_Site = gestor.Site;
                    usuario.Contato_Endereco = gestor.Endereco;
                    usuario.Contato_CEP = gestor.CEP;
                    usuario.Contato_IDPais = gestor.IDPais;
                    usuario.Contato_IDEstado = gestor.IDEstado;
                    usuario.Contato_IDCidade = gestor.IDCidade;
                    usuario.Contato_Latitude = gestor.Latitude;
                    usuario.Contato_Longitude = gestor.Longitude;
                    usuario.Demo_IDUsuario = gestor.Demo_IDUsuario;
                }
                else
                {
                    // contato default
                    usuario.Contato_Email = "<EMAIL>";
                    usuario.Contato_Telefone1 = "(11) 5080-8200";
                    usuario.Contato_Telefone2 = "";
                    usuario.Contato_Telefone3 = "";
                    usuario.Contato_Celular = "(11) 9.7598-5359";
                    usuario.Contato_Site = "https://www.gestal.com";
                    usuario.Contato_Endereco = "Rua Borges Lagoa, 190";
                    usuario.Contato_CEP = "04038-000";
                    usuario.Contato_IDPais = 1;
                    usuario.Contato_IDEstado = 26;
                    usuario.Contato_IDCidade = 5270;
                    usuario.Contato_Latitude = 0;
                    usuario.Contato_Longitude = 0;
                    usuario.Demo_IDUsuario = 0;
                }

                // demo gestor inicialmente desabilitado
                usuario.Demo_Habilitado = false;
                usuario.Demo_Login = "";
                usuario.Demo_Senha = "";

                // demo gestor
                if (usuario.Demo_IDUsuario > 0)
                {
                    // demo
                    UsuarioDominio demo = usuarioMetodos.ListarPorId(usuario.Demo_IDUsuario);

                    if (demo != null)
                    {
                        // habilitado
                        usuario.Demo_Habilitado = true;

                        // copia demo
                        usuario.Demo_Login = demo.Login;
                        usuario.Demo_Senha = demo.Senha;
                    }
                    else
                    {
                        // como não encontrei usuário demo, não é utilizado
                        usuario.Demo_IDUsuario = 0;
                    }
                }
            }

            // prepara listas
            PreparaListas_Consultores(usuario.Contato_IDEstado);

            // le usuário medições
            UsuarioMedicaoMetodos usuarioMedicaoMetodos = new UsuarioMedicaoMetodos();
            List<UsuarioMedicaoDominio> usuarioMedicoes = usuarioMedicaoMetodos.ListarTodasMedicoesIDUsuario(IDConsultor);
            ViewBag.usuarioMedicoes = usuarioMedicoes;

            // cria lista de medicoes habilitadas para o usuario
            List<int> ConfigMedicaoList = new List<int>();

            if (usuarioMedicoes != null)
            {
                foreach (UsuarioMedicaoDominio usuarioMedicao in usuarioMedicoes)
                {
                    ConfigMedicaoList.Add(usuarioMedicao.IDMedicao);
                }
            }

            // le medicoes configuradas do usuario para lista
            var medicoesMetodos = new CliGateGrupoUnidMedicoesMetodos();
            var medicoes = new List<CliGateGrupoUnidMedicoesDominio>();

            if (ConfigMedicaoList != null)
                medicoes = medicoesMetodos.ListarPorIDCliente(-1, ConfigMedicaoList);
            ViewBag.Medicoes = medicoes;

            // le clientes associados ao consultor
            List<CliGateGrupoUnidMedicoesDominio> medicoes2 = new List<CliGateGrupoUnidMedicoesDominio>();

            // verifica se esta editando
            if( usuario.IDCliente > 0 )
            {
                ClientesMetodos clientesMetodos = new ClientesMetodos();
                List<ClientesDominio> clientes = clientesMetodos.ListarPorIDConsultor(usuario.IDCliente, 1);

                if( clientes != null )
                {
                    foreach (ClientesDominio cliente in clientes)
                    {
                        // le medicoes para lista
                        List<CliGateGrupoUnidMedicoesDominio> medicoes3 = new List<CliGateGrupoUnidMedicoesDominio>();
                        medicoes3 = medicoesMetodos.ListarPorIDCliente(cliente.IDCliente);

                        if (medicoes3 != null)
                        {
                            foreach (CliGateGrupoUnidMedicoesDominio medicao in medicoes3)
                            {
                                // coloca na lista
                                medicoes2.Add(medicao);
                            }
                        }
                    }
                }
            }

            ViewBag.Medicoes2 = medicoes2;

            return View(usuario);
        }

        // Listas utilizadas
        private void PreparaListas_Consultores(int IDEstado = 0)
        {
            // le tipos Paises
            CidadeEstadoPaisMetodos cidadeMetodos = new CidadeEstadoPaisMetodos();
            List<PaisesDominio> listatiposPais = cidadeMetodos.ListarTodosPaises();
            ViewBag.listaTipoPais = listatiposPais;

            // le tipos Estado
            List<EstadosDominio> listatiposEstado = cidadeMetodos.ListarTodosEstados();
            ViewBag.listaTipoEstado = listatiposEstado;

            // le tipos Cidade
            List<CidadesDominio> listatiposCidade = cidadeMetodos.ListarTodosCidades(IDEstado);
            ViewBag.listaTipoCidade = listatiposCidade;

            // le tipos medicao
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposMsgEmail = listatiposMetodos.ListarTodos("TipoMsgEmail");
            ViewBag.listatiposMsgEmail = listatiposMsgEmail;

            // tipos expirar
            List<ListaTiposDominio> listaExpirar = new List<ListaTiposDominio>();

            for (int i = 1; i <= 12; i++)
            {
                ListaTiposDominio tipo = new ListaTiposDominio();

                // ID
                tipo.ID = i;

                // descrição
                if (i == 0)
                {
                    tipo.Descricao = "Senha não expira";
                }
                else if (i == 1)
                {
                    tipo.Descricao = "1 mês";
                }
                else if (i == 2)
                {
                    tipo.Descricao = "45 dias";
                }
                else
                {
                    tipo.Descricao = string.Format("{0} meses", i);
                }

                // coloca na lista
                listaExpirar.Add(tipo);
            }

            ViewBag.listaExpirar = listaExpirar;

            // tipos idioma
            List<IdiomaDominio> listaTiposIdioma = listatiposMetodos.ListarTodos_Idioma();
            ViewBag.listaTiposIdioma = listaTiposIdioma;

            // tipos tema
            List<TemaDominio> listaTiposTema = listatiposMetodos.ListarTodos_Tema();
            ViewBag.listaTiposTema = listaTiposTema;

            // le tipos bloqueio
            List<ListaTiposDominio> listaTiposBloqueio = listatiposMetodos.ListarTodos("TipoBloqueio", false);
            ViewBag.listaTiposBloqueio = listaTiposBloqueio;

            // le tipos segundo fator
            List<ListaTiposDominio> listaTiposSegundoFator = listatiposMetodos.ListarTodos("TipoSegundoFator", false);
            ViewBag.listaTiposSegundoFator = listaTiposSegundoFator;

            return;
        }

        // POST: Configuração Consultores - Salvar
        [HttpPost]
        public ActionResult Consultores_Salvar(UsuarioDominio usuario, List<UsuarioMedicaoDominio> usuarioMedicoes)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // protege contra IDCliente inválido
            if (usuario.IDCliente < 0)
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Erro ao salvar. Entrar em contato com o suporte e informar código -10"
                };

                // retorna status
                return Json(returnedData);
            }

            // protege contra salvar como Admin
            // só pode ser admin Junior e Sérgio, se quiser ter outro tem que fazer via banco de dados
            if (usuario.IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN && (usuario.IDUsuario != 1 && usuario.IDUsuario != 4165))
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Erro ao salvar. Entrar em contato com o suporte e informar código 0"
                };

                // retorna status
                return Json(returnedData);
            }

            // verifica se a senha mudou
            if (usuario.Senha != usuario.SenhaAtual)
            {
                // senhas usadas
                SenhasUsadasMetodos senhasUsadasMetodos = new SenhasUsadasMetodos();

                // verifica se senha já foi usada
                if (senhasUsadasMetodos.VerificaSenhaUsada(usuario.IDUsuario, usuario.Senha))
                {
                    returnedData = new
                    {
                        status = "ERRO",
                        erro = "Você está repetindo alguma das últimas 10 senhas utilizadas.<br><br>Por favor cadastrar outra senha."
                    };

                    // retorna status
                    return Json(returnedData);
                }

                // salva senha usada
                senhasUsadasMetodos.Inserir(usuario.IDUsuario, usuario.Senha);
            }

            // verifica se existe outro usuario com o mesmo login
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            if (usuarioMetodos.VerificarDuplicidade(usuario))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Login existente."
                };

                // retorna status
                return Json(returnedData);
            }
            else
            {
                // verifica se mudou email
                bool mudou_email = false;

                // verifica se o email mudou
                if (usuario.Email != usuario.EmailAtual)
                {
                    // mudou email
                    mudou_email = true;
                }

                // zera inicialmente medicoes
                usuario.ConfigGrupo = "";
                usuario.ConfigGrupoEmail = "";
                usuario.ConfigUnid = "";
                usuario.ConfigUnidEmail = "";
                usuario.ConfigMed = "";
                usuario.ConfigMedEmail = "";

                // admin sempre pode ver tudo
                usuario.View_Analises = 1;
                usuario.View_Financas = 1;
                usuario.View_KPI = 1;
                usuario.View_Ranking = 1;
                usuario.View_Rateio = 1;
                usuario.View_Metas = 1;
                usuario.View_Configuracao = 1;
                usuario.View_ConfiguracaoRemota = 1;
                usuario.View_PerfilUsuario = 1;

                // salva usuario 
                usuarioMetodos.Salvar(usuario);

                // pego IDUsuario novamente (pois pode ter sido insercao)
                UsuarioDominio user = usuarioMetodos.ListarPorLogin(usuario.Login);

                // salva 
                if (user != null)
                {
                    // exclui todos as medicoes deste usuario
                    UsuarioMedicaoMetodos usuarioMedicaoMetodos = new UsuarioMedicaoMetodos();
                    usuarioMedicaoMetodos.Excluir(user.IDUsuario);

                    // IDCliente eh o mesmo do IDUsuario
                    user.IDCliente = user.IDUsuario;

                    // salva usuario 
                    usuarioMetodos.Salvar(user);

                    // verifica se o email mudou
                    if (mudou_email)
                    {
                        // enviar email de confirmação
                        EnviarConfirmacaoEmail(user.IDUsuario, user.NomeUsuario, user.Email);
                    }

                    // salva usuariomedicao
                    if (usuarioMedicoes != null)
                    {
                        // percorre lista e salva
                        foreach (UsuarioMedicaoDominio usuarioMedicao in usuarioMedicoes)
                        {
                            // le cliente e gateway da medicao
                            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                            MedicoesDominio medicao = medicaoMetodos.ListarPorId(usuarioMedicao.IDMedicao);

                            if (medicao != null)
                            {
                                // preenche campos que faltam
                                usuarioMedicao.IDUsuario = user.IDUsuario;
                                usuarioMedicao.IDCliente = medicao.IDCliente;
                                usuarioMedicao.IDGateway = medicao.IDGateway;


                                // utiliza os flags do usuario
                                usuarioMedicao.RelatDiario = false;
                                usuarioMedicao.RelatSemanal = false;
                                usuarioMedicao.RelatMensal = false;
                                usuarioMedicao.RelatRanking = false;
                                usuarioMedicao.MsgGeren = false;
                                usuarioMedicao.MsgProcessa = false;

                                // verifico os flags de relatorio periodico
                                if (user.DemAtv == 1 || user.FatPot == 1 || user.Consumo == 1 || user.Supervisao == 1 || user.Util_Anal_Ciclo == 1)
                                {
                                    // diario
                                    if (user.Diario == 1)
                                        usuarioMedicao.RelatDiario = true;

                                    // semanal
                                    if (user.Semanal == 1)
                                        usuarioMedicao.RelatSemanal = true;

                                    // mensal
                                    if (user.Mensal == 1)
                                        usuarioMedicao.RelatMensal = true;
                                }

                                // verifico o flag de relatorio ranking
                                if (user.Ranking == 1)
                                {
                                    // ranking
                                    usuarioMedicao.RelatRanking = true;
                                }

                                // verifico os flags de mensagens de gerenciamento
                                if (user.MsgCliMed_Dem == 1 || user.MsgCliMed_Cons == 1 || user.MsgCliMed_FatPot == 1)
                                {
                                    // mensagens de gerenciamento proveniente da medicao (dem alta, baixa, fat pot, cons alto, baixo)
                                    usuarioMedicao.MsgGeren = true;
                                }

                                // verifico os flags de processamento
                                if (user.MsgCliEve == 1)
                                {
                                    // mensagens de gerenciamento proveniente do processamento dos arquivos recebidos
                                    usuarioMedicao.MsgProcessa = true;
                                }


                                // salva
                                usuarioMedicaoMetodos.Salvar(usuarioMedicao);
                            }
                        }
                    }

                    //
                    // ATUALIZA CLIENTES E MEDICOES HABILITADOS
                    //
                    // uso o UsuariosMedicoes para atualizar o campo CONFIGMED / CONFIGMEDEMAIL e flags de recebimento de email em Usuarios
                    if (isUser.isConsultor(user.IDTipoAcesso))
                    {
                        // atualizo usuario
                        usuarioMedicaoMetodos.AtualizarUsuariosMedicoes(user.IDUsuario);

                        // atualizo CtrEmailAdm
                        usuarioMedicaoMetodos.AtualizarUsuariosMedicoesAdmin(user.IDUsuario);
                    }

                    //
                    // ATUALIZA CONSULTOR (CONTATO)
                    //
                    if (isUser.isConsultor(user.IDTipoAcesso))
                    {
                        // atualiza Gestor Contato e Demo
                        if (!Consultores_Contato_Demo(user.IDUsuario, usuario))
                        {
                            // retorna status
                            returnedData = new
                            {
                                status = "ERRO",
                                erro = "Login Demo existente."
                            };

                            // retorna status
                            return Json(returnedData);
                        }
                    }
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // Gestor Contato e Demo
        public bool Consultores_Contato_Demo(int IDUsuario, UsuarioDominio usuario)
        {
            // usuário metodos
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();

            // exclui consultor (contato)
            ConsultoresMetodos gestorMetodos = new ConsultoresMetodos();
            gestorMetodos.Excluir(IDUsuario);

            // dados do consultor
            ConsultoresDominio gestor = new ConsultoresDominio();
            gestor.IDConsultor = IDUsuario;
            gestor.Nome = usuario.NomeUsuario;
            gestor.Email = usuario.Contato_Email;
            gestor.Telefone1 = usuario.Contato_Telefone1;
            gestor.Telefone2 = usuario.Contato_Telefone2;
            gestor.Telefone3 = usuario.Contato_Telefone3;
            gestor.Celular = usuario.Contato_Celular;
            gestor.Site = usuario.Contato_Site;
            gestor.Endereco = usuario.Contato_Endereco;
            gestor.CEP = usuario.Contato_CEP;
            gestor.IDPais = usuario.Contato_IDPais;
            gestor.IDEstado = usuario.Contato_IDEstado;
            gestor.IDCidade = usuario.Contato_IDCidade;
            gestor.Latitude = usuario.Contato_Latitude;
            gestor.Longitude = usuario.Contato_Longitude;
            gestor.Demo_IDUsuario = usuario.Demo_IDUsuario;

            // verifico se devo criar usuário demo
            if (usuario.Demo_Habilitado && usuario.Demo_IDUsuario == 0 && usuario.Demo_Login.Length > 0 && usuario.Demo_Senha.Length > 0)
            {
                UsuarioDominio demo = new UsuarioDominio();
                demo.IDUsuario = 0;
                demo.IDCliente = 1;
                demo.IDTipoAcesso = TIPO_ACESSO.DEMONSTRACAO;
                demo.NomeUsuario = usuario.NomeUsuario;
                demo.Apelido = usuario.Apelido;
                demo.Login = usuario.Demo_Login;
                demo.Senha = usuario.Demo_Senha;
                demo.IDTipoSegundoFator = 0;
                demo.CodigoSegundoFator = "";
                demo.ExpirarSegundoFator = new DateTime(2000, 1, 1, 0, 0, 0);
                demo.Bloqueio = 0;
                demo.BloqueioEm = new DateTime(2000, 1, 1, 0, 0, 0);
                demo.ExpirarMeses = 6;
                demo.ExpirarEm = new DateTime(2025, 1, 1, 0, 0, 0);
                demo.IP = "";
                demo.UltimoLogin = new DateTime(2000, 1, 1, 0, 0, 0);
                demo.Criacao = DateTime.Now;
                demo.TentativasLogin = 0;
                demo.Email = usuario.Contato_Email;
                demo.Telefone = usuario.Contato_Telefone1;
                demo.Ramal = "";
                demo.Celular = usuario.Contato_Celular;
                demo.Cargo = "";
                demo.Departamento = "";
                demo.LogoConsult = usuario.LogoConsult;
                demo.IDIdioma = usuario.IDIdioma;
                demo.IDTema = usuario.IDTema;
                demo.API_Key = "";
                demo.API_Limite = 100;
                demo.ConfigGrupo = "/1";
                demo.ConfigGrupoEmail = "";
                demo.ConfigUnid = "/1";
                demo.ConfigUnidEmail = "";
                demo.ConfigMed = "/1/2/3/702/703/903/5726/5787/5788/9783/15553/";
                demo.ConfigMedEmail = "";
                demo.IDDashboardGrupo = 0;
                demo.View_Analises = 1;
                demo.View_Financas = 1;
                demo.View_KPI = 1;
                demo.View_Ranking = 1;
                demo.View_Rateio = 1;
                demo.View_Metas = 1;
                demo.View_Configuracao = 1;
                demo.View_ConfiguracaoRemota = 0;
                demo.View_PerfilUsuario = 1;
                demo.Alarme = 0;
                demo.Diario = 0;
                demo.Semanal = 0;
                demo.Mensal = 0;
                demo.DemAtv = 0;
                demo.FatPot = 0;
                demo.Consumo = 0;
                demo.Supervisao = 0;
                demo.Util_Anal_Ciclo = 0;
                demo.Gestao_Contrato = 0;
                demo.Gestao_Energia = 0;
                demo.MsgSys = 0;
                demo.MsgCliMed = 0;
                demo.MsgCliEve = 0;
                demo.Ranking = 0;
                demo.Rateio = 0;
                demo.MsgCliMed_Dem = 0;
                demo.MsgCliMed_Cons = 0;
                demo.MsgCliMed_FatPot = 0;
                demo.MsgCliEve_FEner = 0;
                demo.MsgCliEve_RepDem = 0;
                demo.MsgCliEve_Alarm = 0;
                demo.MsgEmail = 0;
                demo.ShowIntro = 1;
                demo.AceiteLGPD = 0;
                demo.EmailConfirmado = 1;
                demo.CodigoAtivacao = "";

                // verifica se existe outro usuario com o mesmo login
                if (usuarioMetodos.VerificarDuplicidade(demo))
                {
                    // erro
                    return (false);
                }

                // salva usuario 
                usuarioMetodos.Salvar(demo);

                // pego Demo IDUsuario novamente (pois pode ter sido insercao)
                UsuarioDominio demo_novo = usuarioMetodos.ListarPorLogin(demo.Login);

                // salva 
                if (demo_novo != null)
                {
                    // copio Demo IDUsuario
                    gestor.Demo_IDUsuario = demo_novo.IDUsuario;

                    // Criar dashboard demonstração
                    DashboardPageMetodos dashboardPageMetodos = new DashboardPageMetodos();
                    dashboardPageMetodos.CriarDemonstracao(demo_novo.IDUsuario);
                }
            }

            // verifico se devo alterar usuário demo
            if (usuario.Demo_Habilitado && usuario.Demo_IDUsuario > 0 && usuario.Demo_Login.Length > 0 && usuario.Demo_Senha.Length > 0)
            {
                UsuarioDominio demo = usuarioMetodos.ListarPorId(usuario.Demo_IDUsuario);

                if (demo != null)
                {
                    // altera login e senha
                    demo.Login = usuario.Demo_Login;
                    demo.Senha = usuario.Demo_Senha;

                    // verifica se existe outro usuario com o mesmo login
                    if (usuarioMetodos.VerificarDuplicidade(demo))
                    {
                        // erro
                        return (false);
                    }

                    // salva usuario 
                    usuarioMetodos.Salvar(demo);
                }
            }

            // verifico se não existe mais demo
            if (!usuario.Demo_Habilitado && usuario.Demo_IDUsuario > 0)
            {
                // apaga demo
                usuarioMetodos.Excluir(usuario.Demo_IDUsuario);

                // apaga dashboards
                DashboardPageMetodos dashboardPageMetodos = new DashboardPageMetodos();
                dashboardPageMetodos.ExcluirTodosIDReferencia(usuario.Demo_IDUsuario);

                DashboardMetodos dashboardMetodos = new DashboardMetodos();
                dashboardMetodos.ExcluirTodosIDReferencia(usuario.Demo_IDUsuario);

                // não tem mais demo
                gestor.Demo_IDUsuario = 0;
            }

            // insere consultor
            gestorMetodos.Inserir(gestor);

            // ok
            return (true);
        }

        // GET: Configuracao Consultores - Excluir
        public ActionResult Consultores_Excluir(int IDConsultor)
        {
            // apaga operadores do consultor
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioMedicaoMetodos usuarioMedicaoMetodos = new UsuarioMedicaoMetodos();
            List<UsuarioDominio> listaUsuarios = usuarioMetodos.ListarOperadoresConsultor(IDConsultor);

            if( listaUsuarios != null )
            {
                foreach(UsuarioDominio usuario in listaUsuarios)
                {
                    // apaga o operador do consultor
                    usuarioMetodos.Excluir(usuario.IDUsuario);
                    usuarioMedicaoMetodos.Excluir(usuario.IDUsuario);
                }
            }

            // tira associacao de todos os clientes com este consultor
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            clienteMetodos.RetiraConsultor(IDConsultor);

            // apaga o consultor
            usuarioMetodos.Excluir(IDConsultor);
            usuarioMedicaoMetodos.Excluir(IDConsultor);

            // exclui dashboards
            ConsultoresMetodos gestorMetodos = new ConsultoresMetodos();
            ConsultoresDominio gestor = gestorMetodos.ListarPorId(IDConsultor);

            if (gestor != null)
            {
                // verifico se existe demo
                if (gestor.Demo_IDUsuario > 0)
                {
                    // apaga demo
                    usuarioMetodos.Excluir(gestor.Demo_IDUsuario);

                    // apaga dashboards
                    DashboardPageMetodos dashboardPageMetodos = new DashboardPageMetodos();
                    dashboardPageMetodos.ExcluirTodosIDReferencia(gestor.Demo_IDUsuario);

                    DashboardMetodos dashboardMetodos = new DashboardMetodos();
                    dashboardMetodos.ExcluirTodosIDReferencia(gestor.Demo_IDUsuario);
                }

                // exclui consultor (contato)
                gestorMetodos.Excluir(IDConsultor);
            }

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }


        // GET: Enviar Confirmação Email
        public void EnviarConfirmacaoEmail(int IDUsuario, string Nome, string Email)
        {

            // le cookies
            LeCookies_SmartEnergy();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // cria código de ativação
            string CodigoAtivacao = GenerateRandomString(15);

            // salva no banco de dados
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            usuarioMetodos.Atualiza_CodigoAtivacao(IDUsuario, CodigoAtivacao);

            // assunto
            string assunto = "[Smart Energy] Confirmação de Email";

            // envia EMAIL
            var emailTemplate = "ConfirmacaoEmail";

            if (IDConsultor == CLIENTES_ESPECIAIS.CPFL)
            {
                emailTemplate = "ConfirmacaoEmail_CPFL";
            }

            var message = EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.Nome", Nome);
            message = message.Replace("ViewBag.IDUsuario", IDUsuario.ToString());
            message = message.Replace("ViewBag.CodigoAtivacao", CodigoAtivacao);
            EmailServices.SendEmail(Email, assunto, message, "", "", null);

            // retorna
            return;
        }

        public static string GenerateRandomString(int length)
        {
            var numArray = new byte[length];
            new RNGCryptoServiceProvider().GetBytes(numArray);
            return CleanUpBase64String(Convert.ToBase64String(numArray), length);
        }

        private static string CleanUpBase64String(string input, int maxLength)
        {
            input = input.Replace("-", "");
            input = input.Replace("=", "");
            input = input.Replace("/", "");
            input = input.Replace("+", "");
            input = input.Replace(" ", "");
            while (input.Length < maxLength)
                input = input + GenerateRandomString(maxLength);
            return input.Length <= maxLength ?
                input.ToUpper() : //In my case I want capital letters
                input.ToUpper().Substring(0, maxLength);
        }

        public static string EMailTemplate(string template)
        {
            var templateFilePath = HostingEnvironment.MapPath("~/Content/emailTemplates/") + template + ".cshtml";
            StreamReader objstreamreaderfile = new StreamReader(templateFilePath);
            var body = objstreamreaderfile.ReadToEnd();
            objstreamreaderfile.Close();
            return body;
        }
    }
}