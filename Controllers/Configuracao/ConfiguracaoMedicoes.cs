﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ConfiguracaoController
    {
        // Listas utilizadas
        private void PreparaListas_Medicao(int IDCliente, int IDMedicao = 0, int IDEstado = 0)
        {
            // le tipos medicao
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposMedicao = listatiposMetodos.ListarTodos("TipoMedicao");
            ViewBag.listaTipoMedicao = listatiposMedicao;

            // le categorias medicao
            List<ListaTiposDominio> listatiposCategoriaMedicao = listatiposMetodos.ListarTodos("TipoCategoriaMedicao", false);
            ViewBag.listaTipoCategoriaMedicao = listatiposCategoriaMedicao;

            // le sub tipos medicao
            List<ListaTiposDominio> listatiposSubMedicao = listatiposMetodos.ListarTodos("TipoSubMedicao", false);
            ViewBag.listaTipoSubMedicao = listatiposSubMedicao;

            // le tipos classe ativo
            List<ListaTiposDominio> listatiposClasseAtivo = listatiposMetodos.ListarTodos("TipoClasseAtivo", false);
            ViewBag.listaTipoClasseAtivo = listatiposClasseAtivo;

            // le tipos unidade potencia
            List<ListaTiposDominio> listaTipoUnidadePotencia = listatiposMetodos.ListarTodos("TipoUnidadePotencia");
            ViewBag.listaTipoUnidadePotencia = listaTipoUnidadePotencia;

            // unidade de potencia
            UnidadePotencia(IDMedicao);

            // le tipos contrato medicao
            List<ListaTiposDominio> listatiposContratoMedicao = listatiposMetodos.ListarTodos("TipoContratoMedicao");
            ViewBag.listaTipoContratoMedicao = listatiposContratoMedicao;

            // le tipos distribuidora
            List<ListaTiposDominio> listatiposDistribuidora = listatiposMetodos.ListarTodos("AgentesDistribuidora");
            ViewBag.listaTipoDistribuidora = listatiposDistribuidora;

            // le tarifasutilgrupos
            TarifasUtilGruposMetodos listaTarifasUtilGruposMetodos = new TarifasUtilGruposMetodos();
            List<TarifasUtilGruposDominio> listaTarifasUtilGrupos = listaTarifasUtilGruposMetodos.ListarPorIDCliente(IDCliente);
            ViewBag.listaTarifasUtilGrupos = listaTarifasUtilGrupos;

            // le tipos estrutura tarifaria - subrupo
            List<EstruturaTarifariaSubgrupoDominio> listatiposEstruturaTarifaria = listatiposMetodos.ListarTodos_EstruturaTarifaria();
            ViewBag.listaTipoEstruturaTarifaria = listatiposEstruturaTarifaria;

            // le tipos SubSistema
            List<ListaTiposDominio> listatiposSubSistema = listatiposMetodos.ListarTodos("TipoSubSistema");
            ViewBag.listaTipoSubSistema = listatiposSubSistema;

            // le tipos RegraDem
            List<ListaTiposDominio> listatiposRegraDem = listatiposMetodos.ListarTodos("TipoRegraDem");
            ViewBag.listaTipoRegraDem = listatiposRegraDem;

            // le tipos RegraReativo
            List<ListaTiposDominio> listatiposRegraReativo = listatiposMetodos.ListarTodos("TipoRegraReativo");
            ViewBag.listaTipoRegraReativo = listatiposRegraReativo;

            // le tipos RegraICMS
            List<ListaTiposDominio> listatiposRegraICMS = listatiposMetodos.ListarTodos("TipoRegraICMS");
            ViewBag.listaTipoRegraICMS = listatiposRegraICMS;

            // le tipos ICMS
            List<ListaTiposDominio> listatiposICMS = listatiposMetodos.ListarTodos("TipoICMS");
            ViewBag.listaTipoICMS = listatiposICMS;

            // le tipos PIS/COFINS Mês
            List<ListaTiposDominio> listatipos = listatiposMetodos.ListarTodos("TipoPISCOFINS_Mes");
            ViewBag.listaPISCOFINS_Mes = listatipos;

            // le grupos de tarifas do mercado livre
            TarifasMercadoLivreGruposMetodos gruposTarifaMercadoLivreMetodos = new TarifasMercadoLivreGruposMetodos();
            List<TarifasMercadoLivreGruposDominio> listaGruposTarifaMercadoLivre = gruposTarifaMercadoLivreMetodos.ListarPorIDCliente(IDCliente);

            // insiro o termo "Grupo" no nome
            if (listaGruposTarifaMercadoLivre != null )
            {
                foreach(TarifasMercadoLivreGruposDominio tarifa in listaGruposTarifaMercadoLivre)
                {
                    tarifa.Nome = "Grupo de Tarifas - " + tarifa.Nome;
                }
            }

            ViewBag.listaGruposTarifaMercadoLivre = listaGruposTarifaMercadoLivre;

            // le tipos CicloMes
            List<ListaTiposDominio> listatiposCicloMes = listatiposMetodos.ListarTodos("TipoCicloMes");
            ViewBag.listaTipoCicloMes = listatiposCicloMes;

            // le tipos icone
            List<IconeDominio> listatiposIcone = listatiposMetodos.ListarTodos_Icone();
            ViewBag.listaTipoIcone = listatiposIcone;

            // le unidades
            UnidadesMetodos unidadesMetodos = new UnidadesMetodos();
            List<UnidadesDominio> unidades = unidadesMetodos.ListarPorIDCliente(IDCliente);
            ViewBag.listaUnidades = unidades;

            // cria deslocamento
            List<ListaTiposDominio> listaDeslocamento = new List<ListaTiposDominio>();
            
            int i,j;

            for(i=0;i<24;i++)
            {
                for(j=0;j<=45;j+=15)
                {
                    // copia 
                    var tmpObjeto = new ListaTiposDominio()
                    {
                        ID = i*4+(j/15),
                        Descricao = string.Format("{0:00}:{1:00}",i,j)
                    };

                    listaDeslocamento.Add(tmpObjeto);
                }
            }
            ViewBag.listaDeslocamento = listaDeslocamento;

            // le tipos IntervaloGrandeza
            List<ListaTiposDominio> listatiposIntervaloGrandeza = listatiposMetodos.ListarTodos("TipoIntervaloGrandeza", false);
            ViewBag.listatiposIntervaloGrandeza = listatiposIntervaloGrandeza;

            // gateways
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            List<GatewaysDominio> gateways = gatewaysMetodos.ListarPorIDCliente(IDCliente);
            ViewBag.listaGateways = gateways;

            // le historicos
            if( IDMedicao > 0 )
            {
                HistoricoContratosMetodos historicoContratosMetodos = new HistoricoContratosMetodos();
                List<HistoricoContratosDominio> HistoricoContratos = historicoContratosMetodos.ListarPorIDMedicao(IDMedicao);
                ViewBag.HistoricoContratos = HistoricoContratos;

                HistoricoICMSMetodos historicoICMSMetodos = new HistoricoICMSMetodos();
                List<HistoricoICMSDominio> HistoricoICMS = historicoICMSMetodos.ListarPorIDMedicao(IDMedicao);
                ViewBag.HistoricoICMS = HistoricoICMS;

                HistoricoRuralMetodos historicoRuralMetodos = new HistoricoRuralMetodos();
                List<HistoricoRuralDominio> HistoricoRural = historicoRuralMetodos.ListarPorIDMedicao(IDMedicao);
                ViewBag.HistoricoRural = HistoricoRural;
            }
            else
            {
                ViewBag.HistoricoContratos = null;
                ViewBag.HistoricoICMS = null;
                ViewBag.HistoricoRural = null;
            }

            // le KPIs
            KPIConfiguracaoMetodos kpiMetodos = new KPIConfiguracaoMetodos();
            List<KPIConfiguracaoDominio> kpis = kpiMetodos.ListarPorIDCliente(IDCliente);
            ViewBag.listaKPIs = kpis;

            // le tipos Estado
            CidadeEstadoPaisMetodos cidadeMetodos = new CidadeEstadoPaisMetodos();
            List<EstadosDominio> listatiposEstado = cidadeMetodos.ListarTodosEstados();
            ViewBag.listaTipoEstado = listatiposEstado;

            // le tipos Cidade
            List<CidadesDominio> listatiposCidade = cidadeMetodos.ListarTodosCidades(IDEstado);
            ViewBag.listaTipoCidade = listatiposCidade;

            // le tipos Sazonalidade
            List<ListaTiposDominio> listaTipoSazonalidade = listatiposMetodos.ListarTodos("TipoSazonalidade", false);
            ViewBag.listaTipoSazonalidade = listaTipoSazonalidade;

            return;
        }

        // GET: Configuracao Medicoes
        public ActionResult Medicoes(int IDCliente)
        {
            // tela de ajuda - medicoes
            CookieStore.SalvaCookie_String("PaginaAjuda", "Configuracao_Medicoes");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_Medicao(IDCliente);

            // le medicoes
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            List<MedicoesDominio> listaMedicoes = medicoesMetodos.ListarTodos(IDCliente, ViewBag._ConfigMed);
            return View(listaMedicoes);
        }

        // GET: Configuracao Medicoes - Editar
        public ActionResult Medicoes_Editar(int IDMedicao)
        {
            // tela de ajuda - medicoes
            CookieStore.SalvaCookie_String("PaginaAjuda", "Configuracao_MedicoesEditar");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // verifica se adicionando
            MedicoesDominio medicao = new MedicoesDominio();

            medicao.NumMedicoes = 1;

            if (IDMedicao == 0)
            {
                // zera medicao com default
                medicao.IDCliente = ViewBag._IDCliente;

                medicao.IDUnidade = -1;
                medicao.IDTipoMedicao = TIPO_MEDICAO.ENERGIA;               // Energia Eletrica
                medicao.IDCategoriaMedicao = CATEGORIA_MEDICAO.PRINCIPAL;   // Principal
                medicao.IDTipoSubMedicao = 1;                               // CARGA
                medicao.IDDeslocamento = 0;                                 // 00:00
                medicao.IDCicloMes = 0;                                     // Mes Civil

                medicao.Referencia = "";
                medicao.PontoMedicao = "";
                medicao.CodigoInstalacao = "";

                medicao.IDGateway = -1;
                medicao.NumMedGateway = 0;

                medicao.IDGateway_Fechamentos = 0;      // utilizar a própria gateway para gerar os fechamentos

                medicao.IDTipoUnidadePotencia = 1;      // kW

                medicao.IDContratoMedicao = 0;          // Cativo
                medicao.IDTipoClasseAtivo = 1;          // Consumidor Varejista
                medicao.IDEstruturaTarifaria = 1;       // THS Verde
                medicao.IDTipoSubgrupo = 5;             // A4
                medicao.IDSubgrupo = 7;                 // THS Verde A4
                medicao.IDSubSistema = 0;               // Sudeste/Centro-Oeste

                medicao.IDAgenteDistribuidora = 38;     // CPFL Paulista
                medicao.IDGrupoTarifaMercadoLivre = 0;  // utilizar historico de tarifas de mercado livre da medicao
                medicao.IDTipoSazonalidade = 0;         // não contempla sazonalidade

                medicao.IDRegraDem = 5;                 // res. 414 com a regra de proporcionalidade
                medicao.IDRegraReativo = 0;             // com medicao apropriada
                medicao.IDRegraICMS = 1;                // sobre demanda registrada
                medicao.IDCalculoICMS = 0;              // por dentro
                medicao.PISCOFINS_Mes = 0;              // mês vigente

                medicao.InicioP = "18:00";
                medicao.FimP = "21:00";
                medicao.DomP = false; medicao.SegP = true; medicao.TerP = true; medicao.QuaP = true; medicao.QuiP = true; medicao.SexP = true; medicao.SabP = false; medicao.FerP = false;
                medicao.InicioPC = "00:00";
                medicao.FimPC = "06:00";
                medicao.DomPC = true; medicao.SegPC = true; medicao.TerPC = true; medicao.QuaPC = true; medicao.QuiPC = true; medicao.SexPC = true; medicao.SabPC = true; medicao.FerPC = true;
                medicao.InicioR = "21:30";
                medicao.FimR = "06:00";
                medicao.DomR = true; medicao.SegR = true; medicao.TerR = true; medicao.QuaR = true; medicao.QuiR = true; medicao.SexR = true; medicao.SabR = true; medicao.FerR = true;

                medicao.ToleranciaDemP = 5;
                medicao.ToleranciaDemFP = 5;
                medicao.ContratoDemPU = 0;
                medicao.ContratoDemPS = 0;
                medicao.ContratoDemFPU = 0;
                medicao.ContratoDemFPS = 0;

                medicao.IDIcone = -1;

                medicao.Dem_P_pcH = 0.0;
                medicao.Dem_F_pcH = 0.0;
                medicao.Dem_P_pcL = 0.0;
                medicao.Dem_F_pcL = 0.0;
                medicao.FP_LimInd = 0.0;
                medicao.FP_LimCap = 0.0;
                medicao.FP_LimCap = 0;
                medicao.CnPjP_pcH = 0.0;
                medicao.CnPjF_pcH = 0.0;
                medicao.CnPjP_pcL = 0.0;
                medicao.CnPjF_pcL = 0.0;

                medicao.Funcionamento_DemMin = 0.0;
                medicao.Funcionamento_Inicio = "08:00";
                medicao.Funcionamento_Fim = "18:00";

                medicao.IDEstado = 26;
                medicao.IDCidade = 5270;

                medicao.IntervaloGrandeza = 0;
                medicao.FEGrandeza = 65535;

                medicao.DataContrato = new DateTime(2000,1,1,0,0,0);
                medicao.DataContratoTexto = medicao.DataContrato.ToString("d");
                medicao.ContratoDemP = 0;
                medicao.ContratoDemFP = 0;

                medicao.DataICMS = new DateTime(2000, 1, 1, 0, 0, 0);
                medicao.DataICMSTexto = medicao.DataICMS.ToString("d");
                medicao.valorICMS = 18;

                medicao.KPI_RefConsumo = 0.0;
                medicao.KPI_RefConsumo_Inicio = "08:00";
                medicao.KPI_RefConsumo_Fim = "18:00";
                medicao.KPI_RefConsumo_Dom = true; medicao.KPI_RefConsumo_Seg = true; medicao.KPI_RefConsumo_Ter = true; medicao.KPI_RefConsumo_Qua = true; medicao.KPI_RefConsumo_Qui = true; medicao.KPI_RefConsumo_Sex = true; medicao.KPI_RefConsumo_Sab = true; medicao.KPI_RefConsumo_Fer = true;

                medicao.KPI_PotenciaInstalada = 0.0;
                medicao.KPI_Area = 0.0;
                medicao.KPI_IDHistTemperatura = 0;
                medicao.KPI_IDHistVolume = 0;

                medicao.DEXMA_Habilita = false;
                medicao.DEXMA_Key = "";
                medicao.DEXMA_Token = "";
                medicao.DEXMA_did = "";
            }
            else
            {
                // le medicao
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                medicao = medicoesMetodos.ListarPorId(IDMedicao);

                // le último contrato
                HistoricoContratosMetodos contratosMetodos = new HistoricoContratosMetodos();
                HistoricoContratosDominio histContrato = contratosMetodos.ListarPorIDMedicaoMaisRecente(IDMedicao);

                if (histContrato != null)
                {
                    medicao.ContratoDemP = histContrato.ContratoDemP;
                    medicao.ContratoDemPU = histContrato.ContratoDemP;
                    medicao.ContratoDemPS = histContrato.ContratoDemP;
                    medicao.ContratoDemFP = histContrato.ContratoDemFP;
                    medicao.ContratoDemFPU = histContrato.ContratoDemFP;
                    medicao.ContratoDemFPS = histContrato.ContratoDemFP;
                }
                else
                {
                    medicao.ContratoDemP = 0;
                    medicao.ContratoDemPU = 0;
                    medicao.ContratoDemPS = 0;
                    medicao.ContratoDemFP = 0;
                    medicao.ContratoDemFPU = 0;
                    medicao.ContratoDemFPS = 0;
                }
            }

            // prepara listas
            PreparaListas_Medicao(medicao.IDCliente, IDMedicao, medicao.IDEstado);

            // caso tiver cliente
            if (medicao.IDCliente > 0)
            {
                CookieStore.SalvaCookie_Int("_IDCliente", medicao.IDCliente);

                // le contrato cliente
                ClientesMetodos clienteMetodos = new ClientesMetodos();
                ClientesDominio cli = clienteMetodos.ListarPorId(medicao.IDCliente);
                CookieStore.SalvaCookie_Int("IDTipoContrato", cli.IDTipoContrato);

                // le cookies
                LeCookies_SmartEnergy();
            }

            // estado e ICMS padrao
            string nome_estado = "Utilizar ICMS: São Paulo [SP] = 18%";
            double ICMS_padrao = 18.0;

            // estado e ICMS padrao
            ViewBag.Nome_Estado = nome_estado;
            ViewBag.ICMS_Padrao = ICMS_padrao;

            return View(medicao);
        }



        /*---(FUNCAO PUBLIC)--------------------------------------------------------

	        Formula_ListaMed()

	        Monta a lista de medições da fórmula

        PARAMETRO:

	        formula : string com a formula a tratar

        RETORNO:

	        retorna a lista de medicoes que compoem a formula

        ---------------------------------------------------------------------------*/
        private List<int> Formula_ListaMed(string formula)
        {
            List<int> lista_med = new List<int>();
            int conta_letra;
            int conta_numero;
            string str_numero;
            int lendo;	        // indica status da leitura
            // 0 inicio / 1 medicao / 2 numero / 3 operador

            // inicia
            lendo = 0;
            conta_letra = 0;
            conta_numero = 0;
            str_numero = "";

            // verifica
            if (formula == null)
            {
                return null;
            }

            // converte string para lista
            List<char> caracteres_formula = formula.ToUpper().ToList();

            // percorre
            foreach (char c in caracteres_formula)
            {
                // verifica se inicio de medicao
                if (c == 'M')
                {
                    // verifica se ainda lendo medicao ou numero
                    if (lendo == 1 || lendo == 2)
                    {
                        // erro de sintaxe
                        return null;
                    }

                    // indica que vai pegar ID da medicao
                    lendo = 1;

                    // proxima letra
                    conta_letra++;

                    // zera conta numero
                    conta_numero = 0;
                    str_numero = "";

                    continue;
                }

                // verifica se numero
                if ((c >= '0' && c <= '9') || c == '.' || c == ',')
                {
                    // verifica se nao esta lendo medicao
                    if (lendo != 1)
                    {
                        // indica que vai pegar numero
                        lendo = 2;

                        // zera conta numero
                        conta_numero = 0;
                        str_numero = "";

                        continue;
                    }

                    // copia para string do numero
                    str_numero += c.ToString();

                    // proximo numero
                    conta_numero++;

                    continue;
                }

                // verifica se operador
                if (c == '+' || c == '-' || c == '*' || c == '/' || c == '(' || c == ')')
                {
                    // verifica se estava ID da medicao
                    if (lendo == 1)
                    {
                        // verifica se tem algum numero mesmo
                        if (conta_numero == 0)
                        {
                            // erro de sintaxe
                            return null;
                        }

                        // fecha numero e coloca na lista de ID medicao
                        lista_med.Add(int.Parse(str_numero));
                    }

                    // indica que pegou operador
                    lendo = 3;

                    // zera conta numero
                    conta_numero = 0;
                    str_numero = "";

                    continue;
                }

                // se passar aqui significa que nao eh caracter valido, entao eu o ignoro
            }

            // verifica se estava ID da medicao
            if (lendo == 1)
            {
                // verifica se tem algum numero mesmo
                if (conta_numero == 0)
                {
                    // erro de sintaxe
                    return null;
                }

                // fecha numero e coloca na lista de ID medicao
                lista_med.Add(int.Parse(str_numero));
            }

            // retorna lista de medicoes
            return (lista_med);
        }





        // GET: Obter ICMS
        public JsonResult ObterICMS(int IDGateway)
        {
            // ICMS
            double[] listaICMS = { 25, 25, 25, 18, 27, 29, 21, 25, 29, 20, 25, 17, 27, 25, 25, 25, 25, 29, 29, 27, 20, 17, 30, 25, 27, 18, 25 };

            // estado e ICMS padrao
            string nome_estado = "Utilizar ICMS: São Paulo [SP] = 18%";
            double ICMS_padrao = listaICMS[25];

            // caso tiver gateway
            if (IDGateway > 0)
            {
                // le gateway
                GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
                GatewaysDominio gate = gatewayMetodos.ListarPorId(IDGateway);

                if (gate != null)
                {
                    // le empresa desta gateway
                    EmpresasMetodos empresaMetodos = new EmpresasMetodos();
                    EmpresasDominio empresa = empresaMetodos.ListarPorId(gate.IDEmpresa);

                    // le tipos Estado
                    CidadeEstadoPaisMetodos cidadeMetodos = new CidadeEstadoPaisMetodos();
                    List<EstadosDominio> listatiposEstado = cidadeMetodos.ListarTodosEstados();

                    // procura nome do estado
                    EstadosDominio estado = listatiposEstado.Find(x => x.IDEstado == empresa.IDEstado);

                    // estado e ICMS padrao
                    ICMS_padrao = listaICMS[empresa.IDEstado - 1];
                    nome_estado = string.Format("Utilizar ICMS: {0} [{1}] = {2:0}%", estado.Nome, estado.UF, ICMS_padrao);
                }
            }

            // estado e ICMS padrao
            ViewBag.Nome_Estado = nome_estado;
            ViewBag.ICMS_Padrao = ICMS_padrao;

            // retorno
            var returnedData = new
            {
                status = "OK",
                Nome_Estado = nome_estado,
                ICMS_padrao = ICMS_padrao
            };

            // retorna o valor em JSON
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // POST: Configuracao Medicoes - Salvar
        [HttpPost]
        public ActionResult Medicoes_Salvar(MedicoesDominio medicao)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se pouca ou muita medicao
            if( medicao.NumMedicoes <= 0 || medicao.NumMedicoes > 36)
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Número de Medições deve estar entre 1 e 36."
                };

                // retorna status
                return Json(returnedData);
            }

            // verifica se existe outra medicao com o mesmo nome
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();

            // salva nome
            string nome = medicao.Nome;

            // adiciona medicoes
            int inicio = medicao.NumMedGateway;
            int fim = medicao.NumMedGateway + medicao.NumMedicoes;

            for (int i = inicio; i < fim; i++)
            {
                // verifica se mais de uma medicao
                if( medicao.NumMedicoes > 1)
                {
                    // modifica 
                    medicao.Nome = nome + string.Format("{0}", i);
                    medicao.NumMedGateway = i;
                }

                // gateway
                int IDGateway = medicao.IDGateway;

                // verifica se formula
                if (medicao.IDTipoMedicao == TIPOS_MEDICAO.formula || medicao.IDTipoMedicao == TIPOS_MEDICAO.utilidades_formula || medicao.IDTipoMedicao == TIPOS_MEDICAO.ea_formula)
                {
                    // pega primeira medicao da formula
                    MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                    int primeira_medicao = medicaoMetodos.PrimeiraMedicao(medicao.Formula);

                    if (primeira_medicao > 0)
                    {
                        MedicoesDominio medicao_formula = medicaoMetodos.ListarPorId(primeira_medicao);

                        if (medicao_formula != null)
                        {
                            // gateway
                            IDGateway = medicao_formula.IDGateway;
                        }
                    }
                }

                // verifica se encontrou gateway
                if (IDGateway > 0)
                {
                    // gateway
                    GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
                    GatewaysDominio gateway = gatewayMetodos.ListarPorId(IDGateway);

                    if (gateway != null)
                    {
                        // le empresa desta gateway
                        EmpresasMetodos empresaMetodos = new EmpresasMetodos();
                        EmpresasDominio empresa = empresaMetodos.ListarPorId(gateway.IDEmpresa);

                        medicao.IDGateway = gateway.IDGateway;
                        medicao.IDEstado = empresa.IDEstado;
                        medicao.IDCidade = empresa.IDCidade;
                    }
                }

                // salva medicao 
                medicoesMetodos.Salvar(medicao);

                // verifica integridade
                int retorno = medicoesMetodos.VerificarIntegridade(medicao);

                switch(retorno)
                {
                    case 1:
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "Medição não adicionada."
                        };
                        break;

                    case 2:
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "IDMedicao adicionada com valor alto.<br><br>FAVOR AVISAR O DESENVOLVIMENTO ANTES DE PROSSEGUIR !!!"
                        };
                        break;
                }

                if( retorno == 0 )
                {
                    // recupera medicao adicionada
                    MedicoesDominio med = medicoesMetodos.ListaMedicaoAdicionada(medicao);

                    // verifica se não é medição nova
                    if (med == null && medicao.IDMedicao != 0)
                    {
                        med = medicoesMetodos.ListarPorId(medicao.IDMedicao);
                    }

                    // verifica se salvou
                    if( med != null )
                    {
                        // analise de consumo - atualiza parametros
                        AnaliseHorarioConsGruposMedMetodos analiseGruposMetodos = new AnaliseHorarioConsGruposMedMetodos();

                        // copia configuração Horário de Consumo
                        DateTime HoraIniRef = DateTime.ParseExact(med.Funcionamento_Inicio, "HH:mm", CultureInfo.InvariantCulture);
                        DateTime HoraFimRef = DateTime.ParseExact(med.Funcionamento_Fim, "HH:mm", CultureInfo.InvariantCulture);
                        double DemandaResidual = med.Funcionamento_DemMin;

                        // atualiza parametros Demanda Residual e horario de consumo
                        analiseGruposMetodos.Atualiza_DemandaResidual(med.IDMedicao, HoraIniRef, HoraFimRef, DemandaResidual);


                        // supervisão das medições - atualiza medição
                        MedicoesSupervMetodos medSupervMetodos = new MedicoesSupervMetodos();
                        medSupervMetodos.Atualizar_SupervisaoMedicao(med.IDMedicao);

                        // evento 
                        UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();

                        if (medicao.IDMedicao > 0)
                        {
                            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.MEDICAO, med.IDMedicao);
                        }
                        else
                        {
                            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.MEDICAO, med.IDMedicao);

                            // adiciona contrato
                            HistoricoContratosDominio contrato = new HistoricoContratosDominio();
                            contrato.Data = DateTime.Parse(medicao.DataContratoTexto);
                            contrato.ContratoDemP = medicao.ContratoDemP;
                            contrato.ContratoDemFP = medicao.ContratoDemFP;
                            contrato.IDCliente = med.IDCliente;
                            contrato.IDMedicao = med.IDMedicao;

                            medicao.ContratoDemPU = medicao.ContratoDemP;
                            medicao.ContratoDemPS = medicao.ContratoDemP;
                            medicao.ContratoDemFPU = medicao.ContratoDemFP;
                            medicao.ContratoDemFPS = medicao.ContratoDemFP;

                            // salva historicocontratos 
                            HistoricoContratosMetodos contratosMetodos = new HistoricoContratosMetodos();
                            contratosMetodos.Salvar(contrato);

                            // adiciona ICMS
                            HistoricoICMSDominio icms = new HistoricoICMSDominio();
                            icms.Data = DateTime.Parse(medicao.DataICMSTexto);
                            icms.valorICMS = medicao.valorICMS;
                            icms.IDCliente = med.IDCliente;
                            icms.IDMedicao = med.IDMedicao;
                            icms.ICMS_Demanda_P = true;
                            icms.ICMS_Demanda_FP = true;
                            icms.ICMS_Tusd_P = true;
                            icms.ICMS_Tusd_FP = true;


                            // salva historicoICMS
                            HistoricoICMSMetodos icmsMetodos = new HistoricoICMSMetodos();
                            icmsMetodos.Salvar(icms);
                        }
                    }
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuracao Medicao - Salvar ContratoMedicao
        public ActionResult Medicoes_Salvar_ContratoMedicao(int IDMedicao, int IDContratoMedicao)
        {
            // salva ContratoMedicao
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            medicaoMetodos.Salvar_ContratoMedicao(IDMedicao, IDContratoMedicao);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // GET: Configuracao Medicao - Excluir
        public ActionResult Medicao_Excluir(int IDMedicao, int Parar)
        {

            // apagar medicao
            ApagarMedicao(IDMedicao, Parar);
           
            // evento 
            UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.EXCLUI_CONFIG, TABELA_CONFIG.MEDICAO, IDMedicao);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        public bool ApagarMedicao(int IDMedicao, int Parar)
        {

            //
            // Informacoes da MEDICAO
            //
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicoesMetodos.ListarPorId(IDMedicao);

            if (medicao == null)
            {
                // retorna status
                return false;
            }

            //
            // Verifica UNIDADE
            //
            List<MedicoesDominio> medicoes = medicoesMetodos.ListarPorIDUnidade(medicao.IDCliente, medicao.IDUnidade);

            if (medicoes != null)
            {
                // verifica se tem apenas 1
                if (medicoes.Count() == 1)
                {
                    UnidadesMetodos unidadesMetodos = new UnidadesMetodos();
                    UnidadesDominio unidade = unidadesMetodos.ListarPorId(medicao.IDCliente, medicao.IDUnidade);

                    if (unidade != null)
                    {
                        List<UnidadesDominio> unidades = unidadesMetodos.ListarPorIDGrupoUnidades(unidade.IDCliente, unidade.IDGrupoUnidades);

                        if (unidades != null)
                        {
                            // verifica se tem apenas 1
                            if (unidades.Count() == 1)
                            {
                                // apaga grupo unidades
                                GrupoUnidadesMetodos grupoUnidadesMetodos = new GrupoUnidadesMetodos();
                                grupoUnidadesMetodos.Excluir(unidade.IDCliente, unidade.IDGrupoUnidades);
                            }
                        }
                    }

                    // apaga unidade
                    unidadesMetodos.Excluir(medicao.IDCliente, medicao.IDUnidade);
                }
            }

            // teste
            if (Parar == 0)
            {
                // retorna status
                return false;
            }

            //
            // Verifica GATEWAY
            //
            medicoes = medicoesMetodos.ListarPorIDGateway(medicao.IDGateway, "");

            if (medicoes != null)
            {
                // verifica quantas medicoes utilizam esta gateway
                // caso possuir apenas uma medição, excluo a gateway
                if (medicoes.Count() == 1)
                {
                    // leio gateway
                    GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
                    GatewaysDominio gateway = gatewayMetodos.ListarPorId(medicao.IDGateway);

                    if (gateway != null)
                    {
                        // verifico quantas gateways utilizam a empresa desta gateway
                        // caso possuir apenas uma empresa, excluo a empresa
                        if (gatewayMetodos.NumGatewaysEmpresa(gateway.IDEmpresa) == 1)
                        {
                            // leio empresa
                            EmpresasMetodos empresaMetodos = new EmpresasMetodos();
                            EmpresasDominio empresa = empresaMetodos.ListarPorId(gateway.IDEmpresa);

                            if (empresa != null)
                            {
                                // apaga os contatos da empresa
                                EmpresasContatosMetodos empresaContatoMetodos = new EmpresasContatosMetodos();
                                empresaContatoMetodos.ExcluirEmpresa(gateway.IDEmpresa);

                                // excluo empresa
                                empresaMetodos.Excluir(gateway.IDEmpresa);
                            }
                        }

                        // coloca gateway como análise da assistência técnica
                        NumeroSerieMetodos nsMetodos = new NumeroSerieMetodos();
                        nsMetodos.AlterarStatus(gateway.IDNumeroSerie, TIPO_NS_STATUS.AT);
                    }

                    // apaga descrição alarmes
                    DescricaoAlarmesMetodos descricaoAlmMetodos = new DescricaoAlarmesMetodos();
                    descricaoAlmMetodos.ExcluirGateway(medicao.IDGateway);

                    // apaga gateway
                    gatewayMetodos.Excluir(medicao.IDGateway);

                    // apaga FECHAMENTOS
                    FechamentosMetodos fechamentoMetodos = new FechamentosMetodos();
                    fechamentoMetodos.ExcluirTodosIDGateway(medicao.IDGateway);

                    // apaga SUPERVISAO GATEWAY
                    SupervGatewaysMetodos supervGatewayMetodos = new SupervGatewaysMetodos();
                    supervGatewayMetodos.ExcluirTodosIDGateway(medicao.IDGateway);

                    // apaga historicos Gateway
                    EV_Metodos evMetodos = new EV_Metodos();
                    evMetodos.ExcluirTabela(medicao.IDCliente, medicao.IDGateway);

                    // apaga Saidas Digitais
                    SaidasDigitaisMetodos sdMetodos = new SaidasDigitaisMetodos();
                    sdMetodos.ExcluirTodosIDGateway(medicao.IDGateway);

                    // apaga Entradas Digitais
                    EntradasDigitaisMetodos edMetodos = new EntradasDigitaisMetodos();
                    edMetodos.ExcluirTodosIDGateway(medicao.IDGateway);
                }
            }

            // teste
            if (Parar == 1)
            {
                // retorna status
                return false;
            }

            // apaga DASHBOARD
            DashboardMetodos dashboardMetodos = new DashboardMetodos();
            dashboardMetodos.ExcluirTodosIDMedicao(medicao.IDMedicao);

            // apaga HISTORICO CONTRATOS
            HistoricoContratosMetodos contratosMetodos = new HistoricoContratosMetodos();
            contratosMetodos.ExcluirTodosIDMedicao(medicao.IDMedicao);

            // apaga HISTORICO ICMS
            HistoricoICMSMetodos icmsMetodos = new HistoricoICMSMetodos();
            icmsMetodos.ExcluirTodosIDMedicao(medicao.IDMedicao);

            // apaga HISTORICO RURAL
            HistoricoRuralMetodos ruralMetodos = new HistoricoRuralMetodos();
            ruralMetodos.ExcluirTodosIDMedicao(medicao.IDMedicao);

            // apaga HISTORICO MERCADO LIVRE
            HistoricoMercadoLivreMetodos mercadolivreMetodos = new HistoricoMercadoLivreMetodos();
            mercadolivreMetodos.ExcluirTodosIDReferencia(medicao.IDMedicao, 0);

            // apaga FATURA OUTROS
            FaturaOutrosMetodos outrosMetodos = new FaturaOutrosMetodos();
            outrosMetodos.ExcluirTodosIDMedicao(medicao.IDMedicao);

            // apaga METAS CONSUMO
            MetasConsumoMetodos metasMetodos = new MetasConsumoMetodos();
            metasMetodos.ExcluirTodosIDMedicao(medicao.IDMedicao);

            // apaga OBSERVACAO MEDICOES
            ObservacaoMedicaoMetodos observacaoMetodos = new ObservacaoMedicaoMetodos();
            observacaoMetodos.ExcluirTodosIDMedicao(medicao.IDMedicao);

            // apaga SUPERVISAO MEDICAO
            SupervMedicoesMetodos supervMedicaoMetodos = new SupervMedicoesMetodos();
            supervMedicaoMetodos.ExcluirTodosIDMedicao(medicao.IDMedicao);

            MedicoesSupervMetodos medicoesSupervMetodos = new MedicoesSupervMetodos();
            medicoesSupervMetodos.Excluir(medicao.IDMedicao);


            // teste
            if (Parar == 2)
            {
                // retorna status
                return false;
            }

            // apaga USUARIO MEDICOES
            UsuarioMedicaoMetodos usuarioMedicaoMetodos = new UsuarioMedicaoMetodos();
            usuarioMedicaoMetodos.ExcluirTodosIDMedicao(medicao.IDMedicao);

            // teste
            if (Parar == 3)
            {
                // retorna status
                return false;
            }

            // atualiza USUARIO
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            List<UsuarioDominio> usuarios = usuarioMetodos.ListarPorIDMedicao(medicao.IDCliente, medicao.IDMedicao);

            if (usuarios != null)
            {
                foreach (UsuarioDominio usuario in usuarios)
                {
                    // uso o UsuariosMedicoes para atualizar o campo CONFIGMED / CONFIGMEDEMAIL e flags de recebimento de email em Usuarios
                    if (isUser.isCliente(usuario.IDTipoAcesso))
                    {
                        usuarioMedicaoMetodos.AtualizarUsuariosMedicoes(usuario.IDUsuario, true);

                        // verifica se usuario se nao tem mais medicao
                        UsuarioDominio user = usuarioMetodos.ListarPorId(usuario.IDUsuario);

                        if (user != null)
                        {
                            if (user.ConfigMed.Count() == 0 && isUser.isCliente(user.IDTipoAcesso))
                            {
                                // exclui usuario
                                usuarioMetodos.Excluir(user.IDUsuario);
                            }
                        }
                    }
                }
            }

            // teste
            if (Parar == 4)
            {
                // retorna status
                return false;
            }

            // apaga historicos Medicao
            EN_Metodos enMetodos = new EN_Metodos();
            enMetodos.ExcluirTabela(medicao.IDCliente, medicao.IDMedicao);

            // teste
            if (Parar == 5)
            {
                // retorna status
                return false;
            }

            // apaga MEDICAO
            medicoesMetodos.Excluir(IDMedicao);

            // supervisão das medições - apaga medição
            MedicoesSupervMetodos medSupervMetodos = new MedicoesSupervMetodos();
            medSupervMetodos.Excluir(IDMedicao);

            // retorna status
            return false;
        }
    }
}