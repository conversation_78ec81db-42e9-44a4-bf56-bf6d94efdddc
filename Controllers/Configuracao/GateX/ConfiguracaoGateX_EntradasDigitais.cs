﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Configuração Entradas Digitais
        public ActionResult GateX_EntradaDigital(int IDGateway, int Origem = 0)
        {
            // tela de ajuda - entradas digitais
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // receber configuração
            List<GateX_ED_Dominio> entradas = GateX_EntradaDigital_Receber(IDGateway, Origem);

            // retorna configuração
            return View(entradas);
        }

        // Configuração Entrada Digital - Receber
        private List<GateX_ED_Dominio> GateX_EntradaDigital_Receber(int IDGateway, int Origem)
        {
            // entradas digitais
            GateX_ED_Metodos edMetodos = new GateX_ED_Metodos();
            List<GateX_ED_Dominio> entradas = new List<GateX_ED_Dominio>();

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // status
            bool solicitaProg = true;

            // verifica Origem
            // 0: ler da gateway
            // 1: ler do banco de dados atual
            // 2: ler do banco de dados editando
            if (Origem == 0)
            {
                // status
                solicitaProg = false;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // solicita configuração das entradas
                    if (solicitaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_ED, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // copia configuração da gateway para 'editando'
                        // caso ainda não exista configuração 'atual', será criada 
                        entradas = edMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.ED);

                        // atualizar entradas digitais
                        Atualizar_EntradasDigitais(gateway, entradas);
                    }
                }

                // verifica se não solicitou
                if (!solicitaProg)
                {
                    // copia configuração 'atual' para 'editando'
                    entradas = edMetodos.CopiarParaEditando(IDGateway);
                }
            }
            else if (Origem == 1)
            {
                // copia configuração 'atual' para 'editando'
                entradas = edMetodos.CopiarParaEditando(IDGateway);
            }
            else
            {
                // configuração em 'editando'
                entradas = edMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            }

            // copia status
            ViewBag.SolicitaProg = solicitaProg;

            // retorna configuração
            return (entradas);
        }

        // GET: Configuração Entrada Digital - Editar
        public ActionResult GateX_EntradaDigital_Editar(int IDGateway, int NumEntradaGateway)
        {
            // tela de ajuda - programacoes entradas digitais
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            GateX_EntradaDigital_Editar_PreparaListas(IDGateway);

            // le entrada a ser programada (editando)          
            GateX_ED_Metodos edMetodos = new GateX_ED_Metodos();
            GateX_ED_Dominio entrada = edMetodos.ListarPorNumEntrada(IDGateway, NumEntradaGateway, STATUS_CFG.EDITANDO);

            // trata valor endereco
            if (entrada.Endereco > 65000)
            {
                entrada.Endereco = -1;
            }

            return View(entrada);
        }

        // prepara listas
        private void GateX_EntradaDigital_Editar_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // le tipos rede IO
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DO);      // remove DO
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.SEG);     // remove seg
            ViewBag.listaTipoRedeIO = listatipoRedeIO;

            // le tipos numeros remota Rede K
            List<ListaTiposDominio> listatipoNumRemotaRedeK = listatiposMetodos.ListarTodos("TipoGateX_NumRemotaRedeK", false, 0);
            ViewBag.listaTipoNumRemotaRedeK = listatipoNumRemotaRedeK;


            // lista numero remotas
            int[] listaRemotas = new int[SGATEX.NUM_REMOTAS];

            for (int i = 0; i < listaRemotas.Length; i++)
            {
                listaRemotas[i] = i;
            }

            // copia lista remotas
            ViewBag.ListaRemotas = listaRemotas;

            return;
        }

        // POST: Configuração Entrada Digital - Programar
        [HttpPost]
        public ActionResult GateX_EntradaDigital_Programar(GateX_ED_Dominio entradaDigital)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // salva
            GateX_ED_Metodos edMetodos = new GateX_ED_Metodos();
            edMetodos.Salvar(entradaDigital, STATUS_CFG.EDITANDO);

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Entrada Digital - Excluir
        public ActionResult GateX_EntradaDigital_Excluir(int IDGateway, int NumEntradaGateway)
        {
            // lista gateway para inserir evento                        
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // apaga programacao
            GateX_ED_Metodos edMetodos = new GateX_ED_Metodos();
            edMetodos.ExcluirPorNumEntrada(IDGateway, NumEntradaGateway, STATUS_CFG.EDITANDO);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // POST: Configuração Entrada Digital - Enviar
        [HttpPost]
        public ActionResult GateX_EntradaDigital_Enviar(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // configuração em 'editando'
            GateX_ED_Metodos edMetodos = new GateX_ED_Metodos();
            List<GateX_ED_Dominio> entradasDigitais = edMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // auxiliar de verificacao DI
            int aux_DI = 0;

            List<string> entradasErro = new List<string>();

            // trata valores
            foreach (GateX_ED_Dominio entrada in entradasDigitais)
            {
                if (entrada.RedeIO == 4)
                {
                    // copia descricao da saída com erro
                    entradasErro.Add(entrada.Descricao);

                    // sinaliza que entrada está configurada como Rede DI
                    aux_DI++;                    
                }
            }

            // caso exista mais de uma entrada configurada com Rede DI retorna erro
            if (aux_DI > 1)
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Não é possível programar mais de uma entrada configurada com Remota 'DI'. Favor verificar as seguintes Entradas:\n " + String.Join("\n", entradasErro.ToArray())
                };

                // retorna status
                return Json(returnedData);
            }

            // copia configuração 'editando' para 'atual'
            entradasDigitais = edMetodos.CopiarParaAtual(IDGateway);

            // variavel de retorno
            bool enviaProg = false;

            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // copia para lista de envio
                smartCom.gateX.ED = entradasDigitais;

                // envia 
                enviaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_ED, SMCOM_TIPO_SOL.ENVIA);
            }

            // verifica retorno
            if (enviaProg)
            {
                // atualizar entradas digitais
                Atualizar_EntradasDigitais(gateway, entradasDigitais);

                // excluir editando
                edMetodos.ExcluirEditando(IDGateway);

                // histórico de configuração
                HistoricoConfiguracoesDominio hist = new HistoricoConfiguracoesDominio();
                hist.IDGateway = IDGateway;
                hist.NomeConfiguracao = "GateX_ED";
                hist.Envio = "MQTT";
                hist.Atualizacao = DateTime.Now;
                hist.Versao = GateX_Versao_String(IDGateway);
                hist.IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");

                // insere histórico de configuração
                HistoricoConfiguracoesMetodos histMetodos = new HistoricoConfiguracoesMetodos();
                histMetodos.Inserir(hist);

                // caso OK insiro evento
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, IDGateway);
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Falha no envio da configuração!"
                };
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Entrada Digital - Copiar
        public ActionResult GateX_EntradaDigital_Copiar(int IDGateway, int Origem, int Inicio, int Fim)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // lista programacoes de entradas da gateway salvas no banco de dados
            GateX_ED_Metodos edMetodos = new GateX_ED_Metodos();
            List<GateX_ED_Dominio> entradasDigitais = edMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            ViewBag.Entradas = entradasDigitais;

            // lista entrada origem
            GateX_ED_Dominio entradaOrigem = edMetodos.ListarPorNumEntrada(IDGateway, Origem, STATUS_CFG.EDITANDO);

            if (entradaOrigem.RedeIO == 4)
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Não é permitido copiar saídas configuradas com Remota 'DI'."
                };

                // retorna erro
                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            if (entradaOrigem.NumEntradaGateway >= Inicio && entradaOrigem.NumEntradaGateway <= Fim)
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "A entrada origem não pode estar entre as entradas destino."
                };

                // retorna erro
                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // auxiliar
            int copia = 1;

            if (Fim < Inicio)
            {
                // copia para entradas desejadas decrescente
                for (int i = Inicio; i >= Fim; i--)
                {
                    GateX_ED_Dominio entradaCopia = edMetodos.ListarPorNumEntrada(IDGateway, i, STATUS_CFG.EDITANDO);

                    // copia
                    GateX_EntradaDigital_CopiaConfiguracao(entradaOrigem, ref entradaCopia, copia);

                    // salva entrada copiada
                    edMetodos.Salvar(entradaCopia, STATUS_CFG.EDITANDO);

                    // incrementa cópia
                    copia++;
                }
            }

            else
            {
                // copia para entradas desejadas crescente
                for (int i = Inicio; i <= Fim; i++)
                {
                    GateX_ED_Dominio entradaCopia = edMetodos.ListarPorNumEntrada(IDGateway, i, STATUS_CFG.EDITANDO);

                    // copia
                    GateX_EntradaDigital_CopiaConfiguracao(entradaOrigem, ref entradaCopia, copia);

                    // salva entrada copiada
                    edMetodos.Salvar(entradaCopia, STATUS_CFG.EDITANDO);

                    // incrementa cópia
                    copia++;
                }
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);

        }

        // copia configuração
        public void GateX_EntradaDigital_CopiaConfiguracao(GateX_ED_Dominio entradaOrigem, ref GateX_ED_Dominio entradaCopia, int copia)
        {
            entradaCopia.IDGateway = entradaOrigem.IDGateway;
            entradaCopia.Descricao = entradaOrigem.Descricao;

            // verifica tamanho se cabe cópia (limite - 3 caracteres)
            if (entradaCopia.Descricao.Length <= (31 - 3))
            {
                // coloca indice
                entradaCopia.Descricao += string.Format(".{0}", copia);
            }

            entradaCopia.RedeIO = entradaOrigem.RedeIO;
            entradaCopia.Remota = entradaOrigem.Remota;
            entradaCopia.Endereco = entradaOrigem.Endereco;

            entradaCopia.EEAtivo = entradaOrigem.EEAtivo;
            entradaCopia.ReconhecerAlarme = entradaOrigem.ReconhecerAlarme;

            entradaCopia.Ativo = entradaOrigem.Ativo;
            entradaCopia.Inativo = entradaOrigem.Inativo;

            return;
        }

        // Atualizar entradas digitais
        private void Atualizar_EntradasDigitais(GatewaysDominio gateway, List<GateX_ED_Dominio> entradas)
        {

            // salva EntradasDigitais
            EntradasDigitaisMetodos entradasDigitaisMetodos = new EntradasDigitaisMetodos();

            // exclui todas as entradas digitais desta gateway
            entradasDigitaisMetodos.ExcluirTodosIDGateway(gateway.IDGateway);

            // atualiza
            foreach (GateX_ED_Dominio ed in entradas)
            {
                // verifica se programado
                if (ed.Programado)
                {
                    EntradasDigitaisDominio entrada = new EntradasDigitaisDominio();

                    entrada.IDEntradaDigital = 0;
                    entrada.IDCliente = gateway.IDCliente;
                    entrada.IDGateway = gateway.IDGateway;
                    entrada.Descricao = ed.Descricao;
                    entrada.Ativou = ed.Ativo;
                    entrada.Desativou = ed.Inativo;
                    entrada.NumEntradaGateway = ed.NumEntradaGateway;
                    entrada.status = STATUS_ED.DESCONHECIDO;

                    // salva
                    entradasDigitaisMetodos.Salvar(entrada);
                }
            }

            // retorna
            return;
        }

        // recebe configuração (comando receber configurações)
        public bool GateX_EntradasDigitais_ComandoRecebe(GatewaysDominio gateway, string versao)
        {
            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita programação
                if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_ED, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // salvar
                    GateX_ED_Metodos edMetodos = new GateX_ED_Metodos();
                    edMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.ED, STATUS_CFG.ATUAL);

                    // atualizar entradas digitais
                    Atualizar_EntradasDigitais(gateway, smartCom.gateX.ED);

                    // ok
                    return (true);
                }
            }

            // erro
            return (false);
        }
    }
}