﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Configuração Medições de Energia (vigência de demanda) - Editar
        public ActionResult GateX_MedEner_VigDem_Editar(int IDGateway, int numVigDem, int NumMedicaoGateway)
        {
            // tela de ajuda - medição de energia
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            <PERSON>misso<PERSON>();

            // prepara listas
            List<GateX_MedEner_Dominio> listaMedEner = GateX_MedEner_VigDem_PreparaListas(IDGateway);

            // lê vigência de demanda (editando)
            GateX_MedEner_VigDem_Metodos medVigDemMetodos = new GateX_MedEner_VigDem_Metodos();
            GateX_MedEner_VigDem_Dominio medVigDem = medVigDemMetodos.ListarPorNumVigDem(IDGateway, numVigDem, STATUS_CFG.EDITANDO);

            // data em texto
            medVigDem.DataTexto = string.Format("{0:d}", medVigDem.DataVigencia);

            // nome da medição
            if (medVigDem.Programado)
            {
                // se programado, mantenho a medição programada
                medVigDem.NomeMedicao = string.Format("[{0:00}] {1}", medVigDem.Medicao, listaMedEner.Find(m => m.NumMedicaoGateway == medVigDem.Medicao).Descricao);
            }
            else
            {
                // se desprogramado, uso a medição atual
                medVigDem.Medicao = NumMedicaoGateway;
                medVigDem.NomeMedicao = string.Format("[{0:00}] {1}", medVigDem.Medicao, listaMedEner.Find(m => m.NumMedicaoGateway == medVigDem.Medicao).Descricao);
            }

            return View(medVigDem);
        }

        // prepara listas
        private List<GateX_MedEner_Dominio> GateX_MedEner_VigDem_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // lista medições em 'editando'
            GateX_MedEner_Metodos medMetodos = new GateX_MedEner_Metodos();
            List<GateX_MedEner_Dominio> listaMedEner = medMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.listaMedEner = listaMedEner;

            return (listaMedEner);
        }

        // POST: Configuração Medições de Energia (vigência de demanda) - Programar
        [HttpPost]
        public ActionResult GateX_MedEner_VigDem_Programar(GateX_MedEner_VigDem_Dominio medVigDem)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // data vigência
            DateTime dataVigencia = new DateTime(2000, 1, 1, 0, 0, 0);

            if (DateTime.TryParse(medVigDem.DataTexto, out dataVigencia))
            {
                // copia data vigencia
                medVigDem.DataVigencia = dataVigencia;

                // salva (editando)
                GateX_MedEner_VigDem_Metodos medVigDemMetodos = new GateX_MedEner_VigDem_Metodos();
                medVigDemMetodos.Salvar(medVigDem, STATUS_CFG.EDITANDO);
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Data e Hora incorreta!"
                };
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Medições de Energia (vigência de demanda) - Excluir
        public ActionResult GateX_MedEner_VigDem_Excluir(int IDGateway, int numVigDem)
        {
            // apaga programacao (editando e atual)
            GateX_MedEner_VigDem_Metodos medVigDemMetodos = new GateX_MedEner_VigDem_Metodos();
            medVigDemMetodos.ExcluirPorNumVigDem(IDGateway, numVigDem, STATUS_CFG.EDITANDO);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // ordena Vigências de demanda por medição e data da vigência
        private void GateX_MedEner_VigDem_Ordena(int IDGateway, ref List<GateX_MedEner_VigDem_Dominio> medVigDem)
        {
            // o que estiver desprogramado, aplico campos para ficar por última na ordenação
            foreach (GateX_MedEner_VigDem_Dominio vigDem in medVigDem)
            {
                // verifico se programado
                if (!vigDem.Programado)
                {
                    vigDem.DataVigencia = new DateTime(2300, 1, 1, 0, 0, 0);
                    vigDem.Medicao = 255;
                }
            }

            // ordena
            List<GateX_MedEner_VigDem_Dominio> medVigDem_novo = medVigDem.OrderBy(v => v.DataVigencia).ThenBy(v => v.Medicao).ToList();

            // copia
            medVigDem = medVigDem_novo;

            // Excluir configuração editando
            GateX_MedEner_VigDem_Metodos medVigDemMetodos = new GateX_MedEner_VigDem_Metodos();
            medVigDemMetodos.ExcluirEditando(IDGateway);

            // o que estiver desprogramado, volto ao default e renumero
            int numVigDem = 0;

            foreach (GateX_MedEner_VigDem_Dominio vigDem in medVigDem)
            {
                // renumero
                vigDem.numVigDem = numVigDem++;

                // verifico se programado
                if (!vigDem.Programado)
                {
                    // default
                    vigDem.DataVigencia = new DateTime(2000, 1, 1, 0, 0, 0);
                    vigDem.Medicao = 0;
                }
                else
                {
                    // desprogramo para a função salvar inserir
                    vigDem.Programado = false;

                    // se programado, salvo em editando
                    medVigDemMetodos.Salvar(vigDem, STATUS_CFG.EDITANDO);
                }
            }

            return;
        }
    }
}