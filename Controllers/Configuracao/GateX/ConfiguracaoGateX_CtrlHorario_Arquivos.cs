﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Controle Horário XLS Download
        [HttpGet]
        public virtual ActionResult GateX_CtrlHorario_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Controle Horário XLS
        public ActionResult GateX_CtrlHorario_XLS(int IDGateway)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_GateX_CtrlHorario(IDGateway);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_ControleHorario_Gateway_{0:000000}_{1:yyyyMMddHHmm}.xls", IDGateway, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // Controle Horário Planilha
        private HSSFWorkbook XLS_GateX_CtrlHorario(int IDGateway)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.DescriConfigCtrlHorario;

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // le lista controles horários
            GateX_CtrlHorario_Metodos ctrlMetodos = new GateX_CtrlHorario_Metodos();
            List<GateX_CtrlHorario_Dominio> ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            //  le lista de saidas da gateway
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidas = sdMetodos.ListarPorIDGateway(IDGateway);

            // cria planilha
            var sheet = workbook.CreateSheet("Controle Horário");

            // cabecalho
            string[] cabecalho = { "Controle", "Saídas Digitais", "Horário - Liga", "Horário - Desliga", "Dias da Semana" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;


            if (ctrls != null)
            {
                // percorre programacoes
                foreach (GateX_CtrlHorario_Dominio ctrl in ctrls)
                {
                    string desprogramado = "---";
                    string saida = desprogramado;
                    string horaLiga = desprogramado;
                    string horaDesliga = desprogramado;
                    string dias = "";

                    if (ctrl.Programado)
                    {
                        // descricao saida
                        if (saidas != null) saida = string.Format("[{0}] {1}", ctrl.Saida, saidas.Find(x => x.NumSaidaGateway == ctrl.Saida).Descricao);

                        // hora liga
                        horaLiga = string.Format("{0:00} : {1:00}", ctrl.HoraLiga, ctrl.MinutoLiga);

                        // hora desliga
                        horaDesliga = string.Format("{0:00} : {1:00}", ctrl.HoraDesliga, ctrl.MinutoDesliga);

                        if (ctrl.DiaSemana_Domingo)
                        {
                            dias = dias + " Dom ";
                        }

                        if (ctrl.DiaSemana_Segunda)
                        {
                            dias = dias + " Seg ";
                        }

                        if (ctrl.DiaSemana_Terca)
                        {
                            dias = dias + " Ter ";
                        }

                        if (ctrl.DiaSemana_Quarta)
                        {
                            dias = dias + " Qua ";
                        }

                        if (ctrl.DiaSemana_Quinta)
                        {
                            dias = dias + " Qui ";
                        }

                        if (ctrl.DiaSemana_Sexta)
                        {
                            dias = dias + " Sex ";
                        }

                        if (ctrl.DiaSemana_Sabado)
                        {
                            dias = dias + " Sab ";
                        }

                        if (ctrl.DiaSemana_Feriado)
                        {
                            dias = dias + " Fer ";
                        }

                        if (ctrl.DiaSemana_Especial)
                        {
                            dias = dias + " Esp ";
                        }
                    }
                    else
                    {
                        dias = desprogramado;
                    }

                    // adiciona linha
                    row = sheet.CreateRow(rowIndex++);

                    // numero controle horário
                    numeroCelulaXLS(row, 0, ctrl.NumCtrlGateway, _intCellStyle);

                    // Nome saída
                    textoCelulaXLS(row, 1, saida);

                    // horario liga 
                    textoCelulaXLS(row, 2, horaLiga);

                    // horario desliga
                    textoCelulaXLS(row, 3, horaDesliga);

                    // dias da semana
                    textoCelulaXLS(row, 4, dias);
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
            {
                sheet.SetColumnWidth(i, 7000);
                if (i == 4)
                {
                    sheet.SetColumnWidth(i, 10000);
                }
            }

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS_GateX(workbook, sheet, _negritoCellStyle);

            // largura de cada coluna
            sheet.SetColumnWidth(0, 7000);
            sheet.SetColumnWidth(1, 12000);

            // retorna planilha
            return workbook;

        }

        // GET: Controle Horário PDF
        public ActionResult GateX_CtrlHoraio_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista programacoes
            GateX_CtrlHorario_Metodos ctrlMetodos = new GateX_CtrlHorario_Metodos();
            List<GateX_CtrlHorario_Dominio> ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlHorario = ctrls;

            // lista saidas 
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.SaidasDigitais = saidasDigitais;

            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_ControleHorario_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_CtrlHorario_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        // GET: Controle Horário EMAIL
        public async Task<ActionResult> GateX_CtrlHorario_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.DescriConfigCtrlHorario;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista programacoes
            GateX_CtrlHorario_Metodos ctrlMetodos = new GateX_CtrlHorario_Metodos();
            List<GateX_CtrlHorario_Dominio> ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlHorario = ctrls;

            // lista saidas 
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.SaidasDigitais = saidasDigitais;

            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_ControleHorario_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_CtrlHorario_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "GateX_CtrlHorarioEmail";
            var message = await EMailTemplateAsync(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.NomeGateway", ViewBag.NomeGateway);
            message = message.Replace("ViewBag.DescrConfig", ViewBag.DescrConfig);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Controle Horário Print
        public ActionResult GateX_CtrlHorarioHoraria_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista programacoes
            GateX_CtrlHorario_Metodos ctrlMetodos = new GateX_CtrlHorario_Metodos();
            List<GateX_CtrlHorario_Dominio> ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlHorario = ctrls;

            // lista saidas 
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.SaidasDigitais = saidasDigitais;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // imprime
            return View();
        }
    }
}