﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Comando Receber Configurações
        public ActionResult GateX_ReceberConfiguracoes(int IDGateway)
        {
            // tela de ajuda - comando receber configurações
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // comando
            GateX_ReceberConfiguracoes_Dominio cmd = new GateX_ReceberConfiguracoes_Dominio();

            // prepara listas
            GateX_ReceberConfiguracoes_PreparaListas(IDGateway, ref cmd);

            // retorna
            return View(cmd);
        }

        // prepara listas
        private void GateX_ReceberConfiguracoes_PreparaListas(int IDGateway, ref GateX_ReceberConfiguracoes_Dominio cmd)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // configurações
            cmd.MedEner = true;
            cmd.MedUtil = true;
            cmd.MedAna = true;
            cmd.MedCiclo = true;

            cmd.CtrlDemProj = true;
            cmd.CtrlDemMed = true;
            cmd.CtrlDemAcum = true;
            cmd.CtrlFatPot = true;
            cmd.CtrlAna = true;
            cmd.CtrlHorario = true;
            cmd.Logicas = true;
            cmd.CtrlAlarme = true;

            cmd.DatasEspeciais = true;
            cmd.ED = true;
            cmd.SD = true;

            cmd.Empresa = true;
            cmd.Usuarios = true;
            cmd.Drivers = true;

            return;
        }

        // progresso
        public class RECEBER_CONFIGURACOES_TASK
        {
            public int progresso { get; set; }
            public string status_atual { get; set; }
        }

        // resultado processo
        public class RECEBER_CONFIGURACOES_RESULTADO
        {
            // status
            public int status { get; set; }
        }

        // estruturas
        private static IDictionary<Guid, RECEBER_CONFIGURACOES_TASK> task_receber_config = new Dictionary<Guid, RECEBER_CONFIGURACOES_TASK>();
        private static RECEBER_CONFIGURACOES_RESULTADO receber_config_resultado_tmp = new RECEBER_CONFIGURACOES_RESULTADO();
        private static IDictionary<Guid, RECEBER_CONFIGURACOES_RESULTADO> receber_config_resultado = new Dictionary<Guid, RECEBER_CONFIGURACOES_RESULTADO>();

        private static Dictionary<int, string> gatex_configuracoes = new Dictionary<int, string>()
        {
            {0, "Medições de Energia"},
            {1, "Medições de Utilidades"},
            {2, "Medições Analógicas"},
            {3, "Medições Ciclômetro"},
            {4, "Entradas Digitais"},
            {5, "Saídas Digitais"},
            {6, "Controle Demanda Projetada"},
            {7, "Controle Demanda Média"},
            {8, "Controle Demanda Acumulada"},
            {9, "Controle Fator de Potência"},
            {10, "Controle Analógico"},
            {11, "Controle Horário"},
            {12, "Controle Alarme"},
            {13, "Lógicas"},
            {14, "Datas Especiais"},
            {15, "Empresa"},
            {16, "Usuários"},
            {17, "Drivers e SmartEnergy"}
        };

        // POST: Comando Receber Configurações - Enviar
        [HttpPost]
        public ActionResult GateX_ReceberConfiguracoes_Enviar(GateX_ReceberConfiguracoes_Dominio cmd)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(cmd.IDGateway);

            // IDUsuario
            int IDUsuario = ViewBag._IDUsuario;

            // taskID
            Guid taskId = Guid.NewGuid();

            // progresso
            RECEBER_CONFIGURACOES_TASK progresso = new RECEBER_CONFIGURACOES_TASK();
            progresso.progresso = 0;
            progresso.status_atual = "---";

            task_receber_config.Add(taskId, progresso);

            // salva cookie
            CookieStore.SalvaCookie_GUID("taskID", taskId);

            // task
            Task.Factory.StartNew(() =>
            {
                // progresso
                progresso.progresso = 0;
                progresso.status_atual = "Recebendo Versão [0%]";
                task_receber_config[taskId] = progresso;

                // atualizando
                receber_config_resultado_tmp.status = TIPO_UPDATE_STATUS.Updating;
                bool updating = true;

                // barra de progresso
                int total = GateX_ReceberConfiguracoes_Total(cmd);
                int atual = 0;

                // versão
                string versao = GateX_Versao_String(gateway.IDGateway);

                // loop para aguardar configurações
                while (updating)
                {
                    // progresso
                    progresso.progresso = (int)(((double)atual / (double)total) * 100.0);
                    progresso.status_atual = string.Format("Recebendo {0} [{1}%]", gatex_configuracoes[atual], progresso.progresso);
                    task_receber_config[taskId] = progresso;

                    // recebe configuração
                    if (!GateX_ReceberConfiguracoes_Recebe(gateway, cmd, atual, versao, IDUsuario))
                    {
                        // atualiza progresso 
                        progresso.progresso = 100;
                        progresso.status_atual = string.Format("Erro ao receber a configuração [{0}]", gatex_configuracoes[atual]);
                        task_receber_config[taskId] = progresso;

                        // ok
                        receber_config_resultado_tmp.status = TIPO_UPDATE_STATUS.Atualizacao_Erro;

                        // terminou
                        updating = false;
                    }

                    // proximo
                    atual++;

                    // verifica se terminou
                    if (atual >= total)
                    {
                        // atualiza progresso 
                        progresso.progresso = 100;
                        progresso.status_atual = "Concluído [100%]";
                        task_receber_config[taskId] = progresso;

                        // ok
                        receber_config_resultado_tmp.status = TIPO_UPDATE_STATUS.Atualizacao_OK;

                        // terminou
                        updating = false;
                    }
                }

                // coloca resultado
                receber_config_resultado.Add(taskId, receber_config_resultado_tmp);

                // terminou
                task_receber_config.Remove(taskId);
            });

            // retorno
            var returnedData = new
            {
                status = "OK",
                taskId = taskId
            };

            // retorna taskId
            return Json(returnedData);
        }


        // recebe configuração
        public bool GateX_ReceberConfiguracoes_Recebe(GatewaysDominio gateway, GateX_ReceberConfiguracoes_Dominio cmd, int atual, string versao, int IDUsuario)
        {

            // configuração
            switch (atual)
            {
                case 0:     // Medições de Energia

                    // verifica se deve receber
                    if (cmd.MedEner)
                    {
                        // recebe configuração
                        if (!GateX_MedEner_ComandoRecebe(gateway, versao))
                        {
                            // erro
                            return (false);
                        }

                        // histórico de configuração (medições de energia)
                        GateX_ReceberConfiguracoes_Historico("GateX_MedEner", gateway, versao, IDUsuario);

                        // histórico de configuração (medição principal)
                        GateX_ReceberConfiguracoes_Historico("GateX_MedEner_MedPrinc", gateway, versao, IDUsuario);

                        // histórico de configuração (demandas suplementares)
                        GateX_ReceberConfiguracoes_Historico("GateX_MedEner_DemSup", gateway, versao, IDUsuario);

                        // histórico de configuração (vigência demanda)
                        GateX_ReceberConfiguracoes_Historico("GateX_MedEner_VigDem", gateway, versao, IDUsuario);
                    }
                    break;

                case 1:     // Medições de Utilidades

                    // verifica se deve receber
                    if (cmd.MedUtil)
                    {
                        // recebe configuração
                        if (!GateX_MedUtil_ComandoRecebe(gateway, versao))
                        {
                            // erro
                            return (false);
                        }

                        // histórico de configuração
                        GateX_ReceberConfiguracoes_Historico("GateX_MedUtil", gateway, versao, IDUsuario);
                    }
                    break;

                case 2:     // Medições Analógicas

                    // verifica se deve receber
                    if (cmd.MedAna)
                    {
                        // recebe configuração
                        if (!GateX_MedAna_ComandoRecebe(gateway, versao))
                        {
                            // erro
                            return (false);
                        }

                        // histórico de configuração
                        GateX_ReceberConfiguracoes_Historico("GateX_MedAna", gateway, versao, IDUsuario);
                    }
                    break;

                case 3:     // Medições Ciclômetro

                    // verifica se deve receber
                    if (cmd.MedCiclo)
                    {
                        // recebe configuração
                        if (!GateX_MedCiclo_ComandoRecebe(gateway, versao))
                        {
                            // erro
                            return (false);
                        }

                        // histórico de configuração
                        GateX_ReceberConfiguracoes_Historico("GateX_MedCiclo", gateway, versao, IDUsuario);
                    }
                    break;

                case 4:     // Entradas Digitais

                    // verifica se deve receber
                    if (cmd.ED)
                    {
                        // recebe configuração
                        if (!GateX_EntradasDigitais_ComandoRecebe(gateway, versao))
                        {
                            // erro
                            return (false);
                        }

                        // histórico de configuração
                        GateX_ReceberConfiguracoes_Historico("GateX_ED", gateway, versao, IDUsuario);
                    }
                    break;

                case 5:     // Saídas Digitais

                    // verifica se deve receber
                    if (cmd.SD)
                    {
                        // recebe configuração
                        if (!GateX_SaidasDigitais_ComandoRecebe(gateway, versao))
                        {
                            // erro
                            return (false);
                        }

                        // histórico de configuração
                        GateX_ReceberConfiguracoes_Historico("GateX_SD", gateway, versao, IDUsuario);
                    }
                    break;

                case 6:     // Controle Demanda Projetada

                    // verifica se deve receber
                    if (cmd.CtrlDemProj)
                    {
                        // recebe configuração
                        if (!GateX_CtrlDemProj_ComandoRecebe(gateway, versao))
                        {
                            // erro
                            return (false);
                        }

                        // histórico de configuração
                        GateX_ReceberConfiguracoes_Historico("GateX_CtrlDemProj", gateway, versao, IDUsuario);

                        // histórico de configuração (saídas)
                        GateX_ReceberConfiguracoes_Historico("GateX_CtrlDemProj_SD", gateway, versao, IDUsuario);
                    }
                    break;

                case 7:     // Controle Demanda Média

                    // verifica se deve receber
                    if (cmd.CtrlDemMed)
                    {
                        // recebe configuração
                        if (!GateX_CtrlDemMed_ComandoRecebe(gateway, versao))
                        {
                            // erro
                            return (false);
                        }

                        // histórico de configuração
                        GateX_ReceberConfiguracoes_Historico("GateX_CtrlDemMed", gateway, versao, IDUsuario);

                        // histórico de configuração (saídas)
                        GateX_ReceberConfiguracoes_Historico("GateX_CtrlDemMed_SD", gateway, versao, IDUsuario);
                    }
                    break;

                case 8:     // Controle Demanda Acumulada

                    // verifica se deve receber
                    if (cmd.CtrlDemAcum)
                    {
                        // recebe configuração
                        if (!GateX_CtrlDemAcum_ComandoRecebe(gateway, versao))
                        {
                            // erro
                            return (false);
                        }

                        // histórico de configuração
                        GateX_ReceberConfiguracoes_Historico("GateX_CtrlDemAcum", gateway, versao, IDUsuario);

                        // histórico de configuração (saídas)
                        GateX_ReceberConfiguracoes_Historico("GateX_CtrlDemAcum_SD", gateway, versao, IDUsuario);
                    }
                    break;

                case 9:     // Controle Fator de Potência

                    // verifica se deve receber
                    if (cmd.CtrlFatPot)
                    {
                        // recebe configuração
                        if (!GateX_CtrlFatPot_ComandoRecebe(gateway, versao))
                        {
                            // erro
                            return (false);
                        }

                        // histórico de configuração
                        GateX_ReceberConfiguracoes_Historico("GateX_CtrlFatPot", gateway, versao, IDUsuario);

                        // histórico de configuração (saídas)
                        GateX_ReceberConfiguracoes_Historico("GateX_CtrlFatPot_SD", gateway, versao, IDUsuario);
                    }
                    break;

                case 10:     // Controle Analógico

                    // verifica se deve receber
                    if (cmd.CtrlAna)
                    {
                        // recebe configuração
                        if (!GateX_CtrlAna_ComandoRecebe(gateway, versao))
                        {
                            // erro
                            return (false);
                        }

                        // histórico de configuração
                        GateX_ReceberConfiguracoes_Historico("GateX_CtrlAna", gateway, versao, IDUsuario);

                        // histórico de configuração (saídas)
                        GateX_ReceberConfiguracoes_Historico("GateX_CtrlAna_SD", gateway, versao, IDUsuario);
                    }
                    break;

                case 11:     // Controle Horário

                    // verifica se deve receber
                    if (cmd.CtrlHorario)
                    {
                        // recebe configuração
                        if (!GateX_CtrlHorario_ComandoRecebe(gateway, versao))
                        {
                            // erro
                            return (false);
                        }

                        // histórico de configuração
                        GateX_ReceberConfiguracoes_Historico("GateX_CtrlHorario", gateway, versao, IDUsuario);
                    }
                    break;

                case 12:     // Controle Alarme

                    // verifica se deve receber
                    if (cmd.CtrlAlarme)
                    {
                        // recebe configuração
                        if (!GateX_CtrlAlarme_ComandoRecebe(gateway, versao))
                        {
                            // erro
                            return (false);
                        }

                        // histórico de configuração
                        GateX_ReceberConfiguracoes_Historico("GateX_CtrlAlarme", gateway, versao, IDUsuario);
                    }
                    break;

                case 13:     // Lógicas

                    // verifica se deve receber
                    if (cmd.Logicas)
                    {
                        // recebe configuração
                        if (!GateX_Logicas_ComandoRecebe(gateway, versao))
                        {
                            // erro
                            return (false);
                        }

                        // histórico de configuração
                        GateX_ReceberConfiguracoes_Historico("GateX_Logicas", gateway, versao, IDUsuario);
                    }
                    break;

                case 14:     // Datas Especiais

                    // verifica se deve receber
                    if (cmd.DatasEspeciais)
                    {
                        // recebe configuração
                        if (!GateX_DatasEspeciais_ComandoRecebe(gateway, versao))
                        {
                            // erro
                            return (false);
                        }

                        // histórico de configuração
                        GateX_ReceberConfiguracoes_Historico("GateX_DatasEspeciais", gateway, versao, IDUsuario);
                    }
                    break;

                case 15:     // Empresa

                    // verifica se deve receber
                    if (cmd.Empresa)
                    {
                        // recebe configuração
                        if (!GateX_Empresa_ComandoRecebe(gateway, versao))
                        {
                            // erro
                            return (false);
                        }

                        // histórico de configuração
                        GateX_ReceberConfiguracoes_Historico("GateX_Empresa", gateway, versao, IDUsuario);
                    }
                    break;

                case 16:     // Usuários

                    // verifica se deve receber
                    if (cmd.Usuarios)
                    {
                        // recebe configuração
                        if (!GateX_Usuarios_ComandoRecebe(gateway, versao))
                        {
                            // erro
                            return (false);
                        }

                        // histórico de configuração
                        GateX_ReceberConfiguracoes_Historico("GateX_Usuarios", gateway, versao, IDUsuario);
                    }
                    break;

                case 17:     // Drivers e SmartEnergy

                    // verifica se deve receber
                    if (cmd.Drivers)
                    {
                        // recebe configuração
                        if (!GateX_Drivers_ComandoRecebe(gateway, versao))
                        {
                            // erro
                            return (false);
                        }

                        // histórico de configuração
                        GateX_ReceberConfiguracoes_Historico("GateX_Driver", gateway, versao, IDUsuario);
                    }
                    break;
            }

            // ok
            return (true);
        }

        // conta número de configurações a receber
        public int GateX_ReceberConfiguracoes_Total(GateX_ReceberConfiguracoes_Dominio cmd)
        {
            int total = 0;

            total += cmd.MedEner ? 1 : 0;
            total += cmd.MedUtil ? 1 : 0;
            total += cmd.MedAna ? 1 : 0;
            total += cmd.MedCiclo ? 1 : 0;
            total += cmd.ED ? 1 : 0;
            total += cmd.SD ? 1 : 0;
            total += cmd.CtrlDemProj ? 1 : 0;
            total += cmd.CtrlDemMed ? 1 : 0;
            total += cmd.CtrlDemAcum ? 1 : 0;
            total += cmd.CtrlFatPot ? 1 : 0;
            total += cmd.CtrlAna ? 1 : 0;
            total += cmd.CtrlHorario ? 1 : 0;
            total += cmd.CtrlAlarme ? 1 : 0;
            total += cmd.Logicas ? 1 : 0;
            total += cmd.DatasEspeciais ? 1 : 0;
            total += cmd.Empresa ? 1 : 0;
            total += cmd.Usuarios ? 1 : 0;
            total += cmd.Drivers ? 1 : 0;

            // total
            return (total);
        }

        // salva histórico
        public void GateX_ReceberConfiguracoes_Historico(string NomeConfiguracao, GatewaysDominio gateway, string versao, int IDUsuario)
        {
            // histórico de configuração
            HistoricoConfiguracoesDominio hist = new HistoricoConfiguracoesDominio();
            hist.IDGateway = gateway.IDGateway;
            hist.NomeConfiguracao = NomeConfiguracao;
            hist.Envio = "MQTT";
            hist.Atualizacao = DateTime.Now;
            hist.Versao = versao;
            hist.IDUsuario = IDUsuario;

            // histórico de configuração
            HistoricoConfiguracoesMetodos histMetodos = new HistoricoConfiguracoesMetodos();
            histMetodos.Inserir(hist);

            return;
        }

        // progresso da atualização
        public ActionResult GateX_ReceberConfiguracoes_Progress(Guid id)
        {
            RECEBER_CONFIGURACOES_TASK progresso = new RECEBER_CONFIGURACOES_TASK();
            progresso.progresso = 100;
            progresso.status_atual = "";

            return Json(task_receber_config.Keys.Contains(id) ? task_receber_config[id] : progresso, JsonRequestBehavior.AllowGet);
        }

        // resultado da atualização
        public ActionResult GateX_ReceberConfiguracoes_Resultado(Guid id)
        {
            // resultado
            return Json(receber_config_resultado.Keys.Contains(id) ? receber_config_resultado[id] : null, JsonRequestBehavior.AllowGet);
        }
    }
}