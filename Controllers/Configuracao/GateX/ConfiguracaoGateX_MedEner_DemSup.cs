﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Configuração Medições de Energia (demanda suplementar) - Editar
        public ActionResult GateX_MedEner_DemSup_Editar(int IDGateway, int numDemSup, int NumMedicaoGateway)
        {
            // tela de ajuda - medição de energia
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            List<GateX_MedEner_Dominio> listaMedEner = GateX_MedEner_DemSup_PreparaListas(IDGateway);

            // lê demanda suplementar (editando)
            GateX_MedEner_DemSup_Metodos medDemSupMetodos = new GateX_MedEner_DemSup_Metodos();
            GateX_MedEner_DemSup_Dominio medDemSup = medDemSupMetodos.ListarPorNumDemSup(IDGateway, numDemSup, STATUS_CFG.EDITANDO);

            // data hora em texto
            medDemSup.DataInicioTexto = string.Format("{0:d}", medDemSup.DataInicio);
            medDemSup.DataFimTexto = string.Format("{0:d}", medDemSup.DataFim);
            medDemSup.HoraInicioTexto = string.Format("{0:t}", medDemSup.HoraInicio);
            medDemSup.HoraFimTexto = string.Format("{0:t}", medDemSup.HoraFim);

            medDemSup.Dom = Funcoes_GateX.IsBitSet_UInt16((UInt16)medDemSup.UsaDia, 0);
            medDemSup.Seg = Funcoes_GateX.IsBitSet_UInt16((UInt16)medDemSup.UsaDia, 1);
            medDemSup.Ter = Funcoes_GateX.IsBitSet_UInt16((UInt16)medDemSup.UsaDia, 2);
            medDemSup.Qua = Funcoes_GateX.IsBitSet_UInt16((UInt16)medDemSup.UsaDia, 3);
            medDemSup.Qui = Funcoes_GateX.IsBitSet_UInt16((UInt16)medDemSup.UsaDia, 4);
            medDemSup.Sex = Funcoes_GateX.IsBitSet_UInt16((UInt16)medDemSup.UsaDia, 5);
            medDemSup.Sab = Funcoes_GateX.IsBitSet_UInt16((UInt16)medDemSup.UsaDia, 6);
            medDemSup.Fer = Funcoes_GateX.IsBitSet_UInt16((UInt16)medDemSup.UsaDia, 7);

            // arredonda 
            medDemSup.TarifaDemanda = Math.Round(medDemSup.TarifaDemanda, 5);
            medDemSup.TarifaConsumo = Math.Round(medDemSup.TarifaConsumo, 5);

            // nome da medição
            if (medDemSup.Programado)
            {
                // se programado, mantenho a medição programada
                medDemSup.NomeMedicao = string.Format("[{0:00}] {1}", medDemSup.NumeroMedEnergia, listaMedEner.Find(m => m.NumMedicaoGateway == medDemSup.NumeroMedEnergia).Descricao);
            }
            else
            {
                // se desprogramado, uso a medição atual
                medDemSup.NumeroMedEnergia = NumMedicaoGateway;
                medDemSup.NomeMedicao = string.Format("[{0:00}] {1}", medDemSup.NumeroMedEnergia, listaMedEner.Find(m => m.NumMedicaoGateway == medDemSup.NumeroMedEnergia).Descricao);
            }

            return View(medDemSup);
        }

        // prepara listas
        private List<GateX_MedEner_Dominio> GateX_MedEner_DemSup_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // tipo suplementar
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listaTipoSuplementar = listatiposMetodos.ListarTodos("TipoGateX_DemSup", false, 0);
            ViewBag.listaTipoSuplementar = listaTipoSuplementar;

            // período
            List<ListaTiposDominio> listaPeriodo = listatiposMetodos.ListarTodos("TipoGateX_Periodo", false, 0);
            ViewBag.listaPeriodo = listaPeriodo;

            // lista medições em 'editando'
            GateX_MedEner_Metodos medMetodos = new GateX_MedEner_Metodos();
            List<GateX_MedEner_Dominio> listaMedEner = medMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.listaMedEner = listaMedEner;

            return (listaMedEner);
        }

        // POST: Configuração Medições de Energia (demanda suplementar) - Programar
        [HttpPost]
        public ActionResult GateX_MedEner_DemSup_Programar(GateX_MedEner_DemSup_Dominio medDemSup)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };


            try
            {
                // data hora inicio e fim
                medDemSup.DataInicio = DateTime.ParseExact(medDemSup.DataInicioTexto, "dd/MM/yyyy", CultureInfo.InvariantCulture);
                medDemSup.DataFim = DateTime.ParseExact(medDemSup.DataFimTexto, "dd/MM/yyyy", CultureInfo.InvariantCulture);
                medDemSup.HoraInicio = DateTime.ParseExact(medDemSup.HoraInicioTexto, "HH:mm", CultureInfo.InvariantCulture);
                medDemSup.HoraFim = DateTime.ParseExact(medDemSup.HoraFimTexto, "HH:mm", CultureInfo.InvariantCulture);

                // auxiliar
                UInt16 UsaDia = 0;

                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 0, medDemSup.Dom);
                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 1, medDemSup.Seg);
                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 2, medDemSup.Ter);
                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 3, medDemSup.Qua);
                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 4, medDemSup.Qui);
                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 5, medDemSup.Sex);
                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 6, medDemSup.Sab);
                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 7, medDemSup.Fer);
                medDemSup.UsaDia = UsaDia;

                // salva (editando)
                GateX_MedEner_DemSup_Metodos medDemSupMetodos = new GateX_MedEner_DemSup_Metodos();
                medDemSupMetodos.Salvar(medDemSup, STATUS_CFG.EDITANDO);

            }
            catch
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Verificar as configurações"
                };
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Medições de Energia (demanda suplementar) - Excluir
        public ActionResult GateX_MedEner_DemSup_Excluir(int IDGateway, int numDemSup)
        {
            // apaga programacao (editando e atual)
            GateX_MedEner_DemSup_Metodos medDemSupMetodos = new GateX_MedEner_DemSup_Metodos();
            medDemSupMetodos.ExcluirPorNumDemSup(IDGateway, numDemSup, STATUS_CFG.EDITANDO);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // ordena Demandas Suplementares por medição
        private void GateX_MedEner_DemSup_Ordena(int IDGateway, ref List<GateX_MedEner_DemSup_Dominio> medDemSup)
        {
            // o que estiver desprogramado, aplico campos para ficar por última na ordenação
            foreach (GateX_MedEner_DemSup_Dominio demSup in medDemSup)
            {
                // verifico se programado
                if (!demSup.Programado)
                {
                    demSup.NumeroMedEnergia = 255;
                }
            }

            // ordena
            List<GateX_MedEner_DemSup_Dominio> medDemSup_novo = medDemSup.OrderBy(v => v.NumeroMedEnergia).ToList();

            // copia
            medDemSup = medDemSup_novo;

            // Excluir configuração editando
            GateX_MedEner_DemSup_Metodos medDemSupMetodos = new GateX_MedEner_DemSup_Metodos();
            medDemSupMetodos.ExcluirEditando(IDGateway);

            // o que estiver desprogramado, volto ao default e renumero
            int numDemSup = 0;

            foreach (GateX_MedEner_DemSup_Dominio demSup in medDemSup)
            {
                // renumero
                demSup.numDemSup = numDemSup++;

                // verifico se programado
                if (!demSup.Programado)
                {
                    // default
                    demSup.NumeroMedEnergia = 0;
                }
                else
                {
                    // desprogramo para a função salvar inserir
                    demSup.Programado = false;

                    // se programado, salvo em editando
                    medDemSupMetodos.Salvar(demSup, STATUS_CFG.EDITANDO);
                }
            }

            return;
        }
    }
}