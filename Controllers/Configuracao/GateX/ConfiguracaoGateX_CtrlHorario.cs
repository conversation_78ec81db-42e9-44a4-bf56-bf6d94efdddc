﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Configuração Controle Horário
        public ActionResult GateX_CtrlHorario(int IDGateway, int Origem = 0)
        {
            // tela de ajuda - controle horário
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // receber configuração
            List<GateX_CtrlHorario_Dominio> prgs = GateX_CtrlHorario_Receber(IDGateway, Origem);

            // prepara listas
            GateX_CtrlHorario_PreparaListas(IDGateway);

            // retorna configuração
            return View(prgs);
        }

        // Configuração Controle Horário - Receber
        private List<GateX_CtrlHorario_Dominio> GateX_CtrlHorario_Receber(int IDGateway, int Origem)
        {
            // controle horário
            GateX_CtrlHorario_Metodos ctrlMetodos = new GateX_CtrlHorario_Metodos();
            List<GateX_CtrlHorario_Dominio> ctrls = new List<GateX_CtrlHorario_Dominio>();

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // status
            bool solicitaCtrl = true;

            // verifica Origem
            // 0: ler da gateway
            // 1: ler do banco de dados atual
            // 2: ler do banco de dados editando
            if (Origem == 0)
            {
                // status
                solicitaCtrl = false;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // solicita configuração dos controles horários
                    if (solicitaCtrl = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_HORARIO, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // atualiza lista de saidas digitais
                        GateX_AtualizaLista(gateway, SMCOM_FUNCOES_SOL.PROGR_SD_LISTA);

                        // copia configuração da gateway para 'editando'
                        // caso ainda não exista configuração 'atual', será criada 
                        ctrls = ctrlMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.CtrlHorario);
                    }

                    // verifica se não solicitou
                    if (!solicitaCtrl)
                    {
                        // copia configuração 'atual' para 'editando'
                        ctrls = ctrlMetodos.CopiarParaEditando(IDGateway);
                    }
                }

            }
            else if (Origem == 1)
            {
                // copia configuração 'atual' para 'editando'
                ctrls = ctrlMetodos.CopiarParaEditando(IDGateway);
            }
            else
            {
                // configuração em 'editando'
                ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            }

            // copia status
            ViewBag.solicitaCtrl = solicitaCtrl;

            // retorna configuração
            return (ctrls);
        }

        // GET: Configuração Controle Horário - Editar
        public ActionResult GateX_CtrlHorario_Editar(int IDGateway, int NumCtrlGateway)
        {
            // tela de ajuda - controles horários
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            GateX_CtrlHorario_PreparaListas(IDGateway);

            // lê controle horário (editando)
            GateX_CtrlHorario_Metodos ctrlMetodos = new GateX_CtrlHorario_Metodos();
            GateX_CtrlHorario_Dominio ctrl = ctrlMetodos.ListarPorNumCtrl(IDGateway, NumCtrlGateway, STATUS_CFG.EDITANDO);

            return View(ctrl);
        }

        // prepara listas
        private void GateX_CtrlHorario_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // le lista saidas digitais
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.SaidasDigitais = saidasDigitais;

            return;
        }

        // POST: Configuração Controle Horário - Programar
        [HttpPost]
        public ActionResult GateX_CtrlHorario_Programar(GateX_CtrlHorario_Dominio ctrlHorario)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // salva (editando)
            GateX_CtrlHorario_Metodos ctrlMetodos = new GateX_CtrlHorario_Metodos();
            ctrlMetodos.Salvar(ctrlHorario, STATUS_CFG.EDITANDO);

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Controle Horário - Excluir
        public ActionResult GateX_CtrlHorario_Excluir(int IDGateway, int NumCtrlGateway)
        {
            // apaga programacao (editando e atual)
            GateX_CtrlHorario_Metodos ctrlMetodos = new GateX_CtrlHorario_Metodos();
            ctrlMetodos.ExcluirPorNumCtrl(IDGateway, NumCtrlGateway, STATUS_CFG.EDITANDO);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // POST: Configuração Controle Horário - Enviar
        [HttpPost]
        public ActionResult GateX_CtrlHorario_Enviar(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // copia configuração 'editando' para 'atual'
            GateX_CtrlHorario_Metodos ctrlMetodos = new GateX_CtrlHorario_Metodos();
            List<GateX_CtrlHorario_Dominio> ctrls = ctrlMetodos.CopiarParaAtual(IDGateway);

            // variavel de retorno
            bool enviaCtrlHorario = false;

            // verifica
            if (ctrls != null && gateway != null)
            {
                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // copia para lista de envio
                    smartCom.gateX.CtrlHorario = ctrls;

                    // envia controle horário
                    enviaCtrlHorario = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_HORARIO, SMCOM_TIPO_SOL.ENVIA);
                }
            }

            // verifica retorno
            if (enviaCtrlHorario)
            {
                // excluir editando
                ctrlMetodos.ExcluirEditando(IDGateway);

                // histórico de configuração
                HistoricoConfiguracoesDominio hist = new HistoricoConfiguracoesDominio();
                hist.IDGateway = IDGateway;
                hist.NomeConfiguracao = "GateX_CtrlHorario";
                hist.Envio = "MQTT";
                hist.Atualizacao = DateTime.Now;
                hist.Versao = GateX_Versao_String(IDGateway);
                hist.IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");

                // insere histórico de configuração
                HistoricoConfiguracoesMetodos histMetodos = new HistoricoConfiguracoesMetodos();
                histMetodos.Inserir(hist);

                // caso OK insiro evento
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, IDGateway);
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Falha no envio da configuração!"
                };
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Controle Horário - Ordenar
        public ActionResult GateX_CtrlHorario_Ordenar(int IDGateway)
        {
            // lista programacoes da gateway salvas no banco de dados 'editando'
            GateX_CtrlHorario_Metodos ctrlMetodos = new GateX_CtrlHorario_Metodos();
            List<GateX_CtrlHorario_Dominio> ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO).OrderBy(x => x.NumCtrlGateway).ToList();

            // cria lista auxiliar
            List<GateX_CtrlHorario_Dominio> aux = new List<GateX_CtrlHorario_Dominio>();

            // preenche auxiliar com default
            for (int i = 0; i < SGATEX.NUM_CTRL_HORARIO; i++)
            {
                // default
                GateX_CtrlHorario_Dominio ctrl = ctrlMetodos.ConfiguracaoDefault(IDGateway, i, STATUS_CFG.EDITANDO);

                // insere na lista
                aux.Add(ctrl);
            }

            int j = 0;

            // Algoritmo de ordenacao com base no codigo do smart32
            aux[0] = ctrls[0];

            for (int i = 1; i < SGATEX.NUM_CTRL_HORARIO; i++)
            {
                j = i;

                if (!ctrls[j].Programado) goto Inserir;
                for (; j > 0; j--)
                {
                    if (!aux[j - 1].Programado) goto DoShift;

                    if (aux[j - 1].Saida > ctrls[i].Saida) goto DoShift;

                    if ((aux[j - 1].Saida == ctrls[i].Saida) && (aux[j - 1].HoraLiga * 60 + aux[j - 1].MinutoLiga > ctrls[i].HoraLiga * 60 + ctrls[i].MinutoLiga))
                    {
                        goto DoShift;
                    }

                    break;

                DoShift:
                    aux[j] = aux[j - 1];
                }

            Inserir:
                aux[j] = ctrls[i];
            }

            // coloca NumCtrlGateway
            for (int i = 0; i < SGATEX.NUM_CTRL_HORARIO; i++)
            {
                aux[i].NumCtrlGateway = i;
            }

            ctrls = aux;

            // apaga banco de dados 'editando'
            ctrlMetodos.ExcluirEditando(IDGateway);

            // insere no banco de dados 'editando'
            ctrlMetodos.InserirLista(ctrls);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // recebe configuração (comando receber configurações)
        public bool GateX_CtrlHorario_ComandoRecebe(GatewaysDominio gateway, string versao)
        {
            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita programação
                if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_HORARIO, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // salvar
                    GateX_CtrlHorario_Metodos ctrlMetodos = new GateX_CtrlHorario_Metodos();
                    ctrlMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.CtrlHorario, STATUS_CFG.ATUAL);

                    // ok
                    return (true);
                }
            }

            // erro
            return (false);
        }
    }
}