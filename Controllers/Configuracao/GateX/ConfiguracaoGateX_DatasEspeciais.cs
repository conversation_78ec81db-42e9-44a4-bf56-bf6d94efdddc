﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Configuração Datas Especiais
        public ActionResult GateX_DatasEspeciais(int IDGateway, int Origem = 0)
        {
            // tela de ajuda - Datas Especiais
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // receber configuração
            List<GateX_DatasEspeciais_Dominio> datasEspeciais = GateX_DatasEspeciais_Receber(IDGateway, Origem);

            // prepara listas
            GateX_DatasEspeciais_PreparaListas(IDGateway);

            // retorna configuração
            return View(datasEspeciais);
        }

        // Configuração Datas Especiais - Receber
        private List<GateX_DatasEspeciais_Dominio> GateX_DatasEspeciais_Receber(int IDGateway, int Origem)
        {
            // datas especiais
            GateX_DatasEspeciais_Metodos prgMetodos = new GateX_DatasEspeciais_Metodos();
            List<GateX_DatasEspeciais_Dominio> datasEspeciais = new List<GateX_DatasEspeciais_Dominio>();

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // status
            bool solicitaProg = true;

            // verifica Origem
            // 0: ler da gateway
            // 1: ler do banco de dados atual
            // 2: ler do banco de dados editando
            if (Origem == 0)
            {
                // status
                solicitaProg = false;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // solicita configuração das datas especiais
                    if (solicitaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_DATAS_ESPECIAIS, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // copia configuração da gateway para 'editando'
                        // caso ainda não exista configuração 'atual', será criada 
                        datasEspeciais = prgMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.DatasEspeciais);
                    }
                }

                // verifica se não solicitou
                if (!solicitaProg)
                {
                    // copia configuração 'atual' para 'editando'
                    datasEspeciais = prgMetodos.CopiarParaEditando(IDGateway);
                }
            }
            else if (Origem == 1)
            {
                // copia configuração 'atual' para 'editando'
                datasEspeciais = prgMetodos.CopiarParaEditando(IDGateway);
            }
            else
            {
                // configuração em 'editando'
                datasEspeciais = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            }

            // copia status
            ViewBag.SolicitaProg = solicitaProg;

            // retorna configuração
            return (datasEspeciais);
        }

        // GET: Configuração Datas Especiais - Editar
        public ActionResult GateX_DatasEspeciais_Editar(int IDGateway, int NumDataGateway)
        {
            // tela de ajuda - programacoes datas especiais
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            GateX_DatasEspeciais_PreparaListas(IDGateway);

            // le data especial a ser programada (editando)          
            GateX_DatasEspeciais_Metodos prgMetodos = new GateX_DatasEspeciais_Metodos();
            GateX_DatasEspeciais_Dominio dataEspecial = prgMetodos.ListarPorNumData(IDGateway, NumDataGateway, STATUS_CFG.EDITANDO);

            // data em texto
            dataEspecial.DataTexto = string.Format("{0:00}/{1:00}/{2}", dataEspecial.Dia, dataEspecial.Mes, dataEspecial.Ano);

            return View(dataEspecial);
        }

        // prepara listas
        private void GateX_DatasEspeciais_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // lista
            List<ListaTiposDominio> listaRedirecionar = new List<ListaTiposDominio>();

            // preenche lista 
            foreach (KeyValuePair<int, string> redireciona in SGATEX.RedirecionarPara)
            {
                // default
                ListaTiposDominio tipo = new ListaTiposDominio();
                tipo.ID = redireciona.Key;
                tipo.Descricao = redireciona.Value;

                // insere na lista
                listaRedirecionar.Add(tipo);
            }

            // lista de saídas
            ViewBag.listaRedirecionar = listaRedirecionar;


            return;
        }

        // POST: Configuração Datas Especiais - Programar
        [HttpPost]
        public ActionResult GateX_DatasEspeciais_Programar(GateX_DatasEspeciais_Dominio dataEspecial)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // data hora
            DateTime programacaoDataHora = new DateTime(2000, 1, 1, 0, 0, 0);

            if (DateTime.TryParse(dataEspecial.DataTexto, out programacaoDataHora))
            {
                GateX_DatasEspeciais_Metodos prgMetodos = new GateX_DatasEspeciais_Metodos();

                // verifica se maior que 2000
                if (programacaoDataHora.Year > 2000)
                {
                    // data
                    dataEspecial.Dia = programacaoDataHora.Day;
                    dataEspecial.Mes = programacaoDataHora.Month;
                    dataEspecial.Ano = programacaoDataHora.Year;

                    // salva
                    prgMetodos.Salvar(dataEspecial, STATUS_CFG.EDITANDO);
                }
                else
                {
                    // apaga programacao
                    prgMetodos.ExcluirPorNumData(dataEspecial.IDGateway, dataEspecial.NumDataGateway, STATUS_CFG.EDITANDO);
                }
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Data e Hora incorreta!"
                };
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Datas Especiais - Excluir
        public ActionResult GateX_DatasEspeciais_Excluir(int IDGateway, int NumDataGateway)
        {
            // apaga programacao
            GateX_DatasEspeciais_Metodos prgMetodos = new GateX_DatasEspeciais_Metodos();
            prgMetodos.ExcluirPorNumData(IDGateway, NumDataGateway, STATUS_CFG.EDITANDO);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // POST: Configuração Datas Especiais - Enviar
        [HttpPost]
        public ActionResult GateX_DatasEspeciais_Enviar(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // configuração em 'editando'
            GateX_DatasEspeciais_Metodos prgMetodos = new GateX_DatasEspeciais_Metodos();
            List<GateX_DatasEspeciais_Dominio> datasEspeciais = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // copia configuração 'editando' para 'atual'
            datasEspeciais = prgMetodos.CopiarParaAtual(IDGateway);

            // variavel de retorno
            bool enviaProg = false;

            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // copia para lista de envio
                smartCom.gateX.DatasEspeciais = datasEspeciais;

                // envia 
                enviaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_DATAS_ESPECIAIS, SMCOM_TIPO_SOL.ENVIA);
            }

            // verifica retorno
            if (enviaProg)
            {
                // excluir editando
                prgMetodos.ExcluirEditando(IDGateway);

                // histórico de configuração
                HistoricoConfiguracoesDominio hist = new HistoricoConfiguracoesDominio();
                hist.IDGateway = IDGateway;
                hist.NomeConfiguracao = "GateX_DatasEspeciais";
                hist.Envio = "MQTT";
                hist.Atualizacao = DateTime.Now;
                hist.Versao = GateX_Versao_String(IDGateway);
                hist.IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");

                // insere histórico de configuração
                HistoricoConfiguracoesMetodos histMetodos = new HistoricoConfiguracoesMetodos();
                histMetodos.Inserir(hist);

                // caso OK insiro evento
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, IDGateway);
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Falha no envio da configuração!"
                };
            }

            // retorna status
            return Json(returnedData);
        }

        // recebe configuração (comando receber configurações)
        public bool GateX_DatasEspeciais_ComandoRecebe(GatewaysDominio gateway, string versao)
        {
            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita programação
                if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_DATAS_ESPECIAIS, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // salvar
                    GateX_DatasEspeciais_Metodos prgMetodos = new GateX_DatasEspeciais_Metodos();
                    prgMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.DatasEspeciais, STATUS_CFG.ATUAL);

                    // ok
                    return (true);
                }
            }

            // erro
            return (false);
        }
    }
}

