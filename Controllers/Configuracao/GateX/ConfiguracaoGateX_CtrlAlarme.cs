﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Configuração Controle Alarme
        public ActionResult GateX_CtrlAlarme(int IDGateway, int Origem = 0)
        {
            // tela de ajuda - controle alarme
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // receber configuração
            List<GateX_CtrlAlarme_Dominio> ctrls = GateX_CtrlAlarme_Receber(IDGateway, Origem);

            // prepara listas
            GateX_CtrlAlarme_PreparaListas(IDGateway);

            // retorna configuração
            return View(ctrls);
        }

        // Configuração Controle Alarme - Receber
        private List<GateX_CtrlAlarme_Dominio> GateX_CtrlAlarme_Receber(int IDGateway, int Origem)
        {
            // controle alarme
            GateX_CtrlAlarme_Metodos ctrlMetodos = new GateX_CtrlAlarme_Metodos();
            List<GateX_CtrlAlarme_Dominio> ctrls = new List<GateX_CtrlAlarme_Dominio>();

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // status
            bool solicitaProg = true;

            // verifica Origem
            // 0: ler da gateway
            // 1: ler do banco de dados atual
            // 2: ler do banco de dados editando
            if (Origem == 0)
            {
                // status
                solicitaProg = false;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // solicita configuração dos controles alarme
                    if (solicitaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_ALM, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // copia configuração da gateway para 'editando'
                        // caso ainda não exista configuração 'atual', será criada 
                        ctrls = ctrlMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.CtrlAlarme);
                    }
                }

                // verifica se não solicitou
                if (!solicitaProg)
                {
                    // copia configuração 'atual' para 'editando'
                    ctrls = ctrlMetodos.CopiarParaEditando(IDGateway);
                }
            }
            else if (Origem == 1)
            {
                // copia configuração 'atual' para 'editando'
                ctrls = ctrlMetodos.CopiarParaEditando(IDGateway);
            }
            else
            {
                // configuração em 'editando'
                ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            }

            // copia status
            ViewBag.SolicitaProg = solicitaProg;

            // retorna configuração
            return (ctrls);
        }

        // GET: Configuração Controle Alarme - Editar
        public ActionResult GateX_CtrlAlarme_Editar(int IDGateway, int NumAlarmeGateway)
        {
            // tela de ajuda - controle alarme
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            GateX_CtrlAlarme_PreparaListas(IDGateway);

            // le controle a ser programado (editando)          
            GateX_CtrlAlarme_Metodos ctrlMetodos = new GateX_CtrlAlarme_Metodos();
            GateX_CtrlAlarme_Dominio ctrl = ctrlMetodos.ListarPorNumAlarme(IDGateway, NumAlarmeGateway, STATUS_CFG.EDITANDO);

            // arredonda
            ctrl.SetPointLo = Math.Round(ctrl.SetPointLo, 5);
            ctrl.SetPointHi = Math.Round(ctrl.SetPointHi, 5);

            return View(ctrl);
        }

        // prepara listas
        private void GateX_CtrlAlarme_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // le lista medições de energia
            GateX_MedEner_Lista_Metodos medMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medEner = medMetodos.ListarPorIDGateway(IDGateway);
            List<GateX_MedEner_Lista_Dominio> listaMedEner = new List<GateX_MedEner_Lista_Dominio>();

            GateX_MedEner_Lista_Dominio nenhumMed = new GateX_MedEner_Lista_Dominio();
            nenhumMed.NumMedicaoGateway = 255;
            nenhumMed.Programado = false;
            nenhumMed.Descricao = "Nenhum";
            listaMedEner.Add(nenhumMed);

            foreach (GateX_MedEner_Lista_Dominio med in medEner)
            {
                med.Descricao = string.Format("[{0:00}] {1}", med.NumMedicaoGateway, med.Descricao);
                listaMedEner.Add(med);
            }
            ViewBag.listaMedEner = listaMedEner;

            // le lista entradas digitais
            GateX_ED_Lista_Metodos edMetodos = new GateX_ED_Lista_Metodos();
            List<GateX_ED_Lista_Dominio> entradasDigitas = edMetodos.ListarPorIDGateway(IDGateway);
            List<GateX_ED_Lista_Dominio> listaEntradas = new List<GateX_ED_Lista_Dominio>();

            GateX_ED_Lista_Dominio nenhumED = new GateX_ED_Lista_Dominio();
            nenhumED.NumEntradaGateway = 255;
            nenhumED.Programado = false;
            nenhumED.Descricao = "Nenhum";
            listaEntradas.Add(nenhumED);

            foreach (GateX_ED_Lista_Dominio entrada in entradasDigitas)
            {
                entrada.Descricao = string.Format("[{0:00}] {1}", entrada.NumEntradaGateway, entrada.Descricao);
                listaEntradas.Add(entrada);
            }
            ViewBag.listaEntradas = listaEntradas;

            // le lista saidas digitais
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitas = sdMetodos.ListarPorIDGateway(IDGateway);
            List<GateX_SD_Lista_Dominio> listaSaidas = new List<GateX_SD_Lista_Dominio>();

            GateX_SD_Lista_Dominio nenhumSD = new GateX_SD_Lista_Dominio();
            nenhumSD.NumSaidaGateway = 255;
            nenhumSD.Programado = false;
            nenhumSD.Descricao = "Nenhum";
            listaSaidas.Add(nenhumSD);

            foreach (GateX_SD_Lista_Dominio saida in saidasDigitas)
            {
                saida.Descricao = string.Format("[{0:00}] {1}", saida.NumSaidaGateway, saida.Descricao);
                listaSaidas.Add(saida);
            }
            ViewBag.listaSaidas = listaSaidas;

            // le tipos alarmes
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoAlarme = listatiposMetodos.ListarTodos("TipoGateX_Alarme", false, 0);
            ViewBag.listatipoAlarme = listatipoAlarme;

            return;
        }

        // POST: Configuração Controle Alarme - Programar
        [HttpPost]
        public ActionResult GateX_CtrlAlarme_Programar(GateX_CtrlAlarme_Dominio ctrlAlarme)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // controle alarme
            GateX_CtrlAlarme_Metodos ctrlMetodos = new GateX_CtrlAlarme_Metodos();

            // verifica se tipo alarme desprogramado
            if (ctrlAlarme.TipoAlarme == 0)
            {
                // apaga programacao
                ctrlMetodos.ExcluirPorNumAlarme(ctrlAlarme.IDGateway, ctrlAlarme.NumAlarmeGateway, STATUS_CFG.EDITANDO);
            }
            else
            {
                // salva controle alarme
                ctrlMetodos.Salvar(ctrlAlarme, STATUS_CFG.EDITANDO);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Controle Alarme - Excluir
        public ActionResult GateX_CtrlAlarme_Excluir(int IDGateway, int NumAlarmeGateway)
        {
            // apaga programacao
            GateX_CtrlAlarme_Metodos ctrlMetodos = new GateX_CtrlAlarme_Metodos();
            ctrlMetodos.ExcluirPorNumAlarme(IDGateway, NumAlarmeGateway, STATUS_CFG.EDITANDO);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // POST: Configuração Controle Alarme - Enviar
        [HttpPost]
        public ActionResult GateX_CtrlAlarme_Enviar(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // configuração em 'editando'
            GateX_CtrlAlarme_Metodos ctrlMetodos = new GateX_CtrlAlarme_Metodos();
            List<GateX_CtrlAlarme_Dominio> ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // copia configuração 'editando' para 'atual'
            ctrls = ctrlMetodos.CopiarParaAtual(IDGateway);

            // variavel de retorno
            bool enviaProg = false;

            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // copia para lista de envio
                smartCom.gateX.CtrlAlarme = ctrls;

                // envia 
                enviaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_ALM, SMCOM_TIPO_SOL.ENVIA);
            }

            // verifica retorno
            if (enviaProg)
            {
                // excluir editando
                ctrlMetodos.ExcluirEditando(IDGateway);

                // histórico de configuração
                HistoricoConfiguracoesDominio hist = new HistoricoConfiguracoesDominio();
                hist.IDGateway = IDGateway;
                hist.NomeConfiguracao = "GateX_CtrlAlarme";
                hist.Envio = "MQTT";
                hist.Atualizacao = DateTime.Now;
                hist.Versao = GateX_Versao_String(IDGateway);
                hist.IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");

                // insere histórico de configuração
                HistoricoConfiguracoesMetodos histMetodos = new HistoricoConfiguracoesMetodos();
                histMetodos.Inserir(hist);

                // caso OK insiro evento
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, IDGateway);
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Falha no envio da configuração!"
                };
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Controle Alarme - Copiar
        public ActionResult GateX_CtrlAlarme_Copiar(int IDGateway, int Origem, int Inicio, int Fim)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // lista programacoes de controles da gateway salvas no banco de dados
            GateX_CtrlAlarme_Metodos ctrlMetodos = new GateX_CtrlAlarme_Metodos();
            List<GateX_CtrlAlarme_Dominio> ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            ViewBag.Ctrls = ctrls;

            // lista controle origem
            GateX_CtrlAlarme_Dominio ctrlOrigem = ctrlMetodos.ListarPorNumAlarme(IDGateway, Origem, STATUS_CFG.EDITANDO);

            if (ctrlOrigem.NumAlarmeGateway >= Inicio && ctrlOrigem.NumAlarmeGateway <= Fim)
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "O alarme origem não pode estar entre os alarmes destino."
                };

                // retorna erro
                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            if (Fim < Inicio)
            {
                // copia para controles desejados decrescente
                for (int i = Inicio; i >= Fim; i--)
                {
                    GateX_CtrlAlarme_Dominio ctrlCopia = ctrlMetodos.ListarPorNumAlarme(IDGateway, i, STATUS_CFG.EDITANDO);

                    // copia
                    GateX_CtrlAlarme_CopiaConfiguracao(ctrlOrigem, ref ctrlCopia);

                    // salva controle copiado
                    ctrlMetodos.Salvar(ctrlCopia, STATUS_CFG.EDITANDO);
                }
            }

            else
            {
                // copia para controles desejados crescente
                for (int i = Inicio; i <= Fim; i++)
                {
                    GateX_CtrlAlarme_Dominio ctrlCopia = ctrlMetodos.ListarPorNumAlarme(IDGateway, i, STATUS_CFG.EDITANDO);

                    // copia
                    GateX_CtrlAlarme_CopiaConfiguracao(ctrlOrigem, ref ctrlCopia);

                    // salva controle copiada
                    ctrlMetodos.Salvar(ctrlCopia, STATUS_CFG.EDITANDO);
                }
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);

        }

        // copia configuração
        public void GateX_CtrlAlarme_CopiaConfiguracao(GateX_CtrlAlarme_Dominio ctrlOrigem, ref GateX_CtrlAlarme_Dominio ctrlCopia)
        {
            ctrlCopia.IDGateway = ctrlOrigem.IDGateway;

            ctrlCopia.TipoAlarme = ctrlOrigem.TipoAlarme;
            ctrlCopia.Medicao = ctrlOrigem.Medicao;
            ctrlCopia.SetPointLo = ctrlOrigem.SetPointLo;
            ctrlCopia.SetPointHi = ctrlOrigem.SetPointHi;

            return;
        }

        // recebe configuração (comando receber configurações)
        public bool GateX_CtrlAlarme_ComandoRecebe(GatewaysDominio gateway, string versao)
        {
            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita programação
                if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_ALM, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // salvar
                    GateX_CtrlAlarme_Metodos ctrlMetodos = new GateX_CtrlAlarme_Metodos();
                    ctrlMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.CtrlAlarme, STATUS_CFG.ATUAL);

                    // ok
                    return (true);
                }
            }

            // erro
            return (false);
        }
    }
}