﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Configuracao Medições de Energia
        public ActionResult GateX_MedEner(int IDGateway, int Origem = 0)
        {
            // tela de ajuda - medição de energia
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // receber configuração
            GateX_MedEner_MedPrinc_Dominio medPrinc = GateX_MedEner_Receber(IDGateway, Origem);

            // prepara listas
            GateX_MedEner_PreparaListas(IDGateway);

            // retorna configuração
            return View(medPrinc);
        }

        // Configuração Medições de Energia - Receber
        private GateX_MedEner_MedPrinc_Dominio GateX_MedEner_Receber(int IDGateway, int Origem)
        {
            // medições de energia
            GateX_MedEner_Metodos medMetodos = new GateX_MedEner_Metodos();
            List<GateX_MedEner_Dominio> medEner = new List<GateX_MedEner_Dominio>();

            GateX_MedEner_MedPrinc_Metodos medPrincMetodos = new GateX_MedEner_MedPrinc_Metodos();
            GateX_MedEner_MedPrinc_Dominio medPrinc = new GateX_MedEner_MedPrinc_Dominio();

            GateX_MedEner_DemSup_Metodos medDemSupMetodos = new GateX_MedEner_DemSup_Metodos();
            List<GateX_MedEner_DemSup_Dominio> medDemSup = new List<GateX_MedEner_DemSup_Dominio>();

            GateX_MedEner_VigDem_Metodos medVigDemMetodos = new GateX_MedEner_VigDem_Metodos();
            List<GateX_MedEner_VigDem_Dominio> medVigDem = new List<GateX_MedEner_VigDem_Dominio>();


            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // status
            bool solicitaProg = true;
            bool solicitaMedPrinc = true;
            bool solicitaDemSup = true;
            bool solicitaVigDem = true;

            // verifica Origem
            // 0: ler da gateway
            // 1: ler do banco de dados atual
            // 2: ler do banco de dados editando
            if (Origem == 0)
            {
                // versão (salva versão)
                GateX_Versao_String(IDGateway);

                // status
                solicitaProg = false;
                solicitaMedPrinc = false;
                solicitaDemSup = false;
                solicitaVigDem = false;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // solicita programação de medicoes de energia
                    if (solicitaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_MED_EN, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // solicita configuração da medição principal
                        if (solicitaMedPrinc = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_MED_PRINC, SMCOM_TIPO_SOL.SOLICITA))
                        {
                            // solicita configuração das demandas suplementares
                            if (solicitaDemSup = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_DEM_SUP, SMCOM_TIPO_SOL.SOLICITA))
                            {
                                // solicita configuração das vigências demanda
                                if (solicitaVigDem = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_VIG_DEM, SMCOM_TIPO_SOL.SOLICITA))
                                {
                                    // copia configuração da gateway para 'editando'
                                    // caso ainda não exista configuração 'atual', será criada 
                                    medEner = medMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.MedEner);
                                    medPrinc = medPrincMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.MedEner_MedPrinc);
                                    medDemSup = medDemSupMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.MedEner_DemSup);
                                    medVigDem = medVigDemMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.MedEner_VigDem);
                                }
                            }
                        }
                    }
                }

                // verifica se solicitou
                if (solicitaProg && solicitaMedPrinc && solicitaDemSup && solicitaVigDem)
                {
                    // atualiza lista de saidas digitais
                    GateX_AtualizaLista(gateway, SMCOM_FUNCOES_SOL.PROGR_SD_LISTA);
                }
                else
                {
                    // copia configuração 'atual' para 'editando'
                    medEner = medMetodos.CopiarParaEditando(IDGateway);
                    medPrinc = medPrincMetodos.CopiarParaEditando(IDGateway);
                    medDemSup = medDemSupMetodos.CopiarParaEditando(IDGateway);
                    medVigDem = medVigDemMetodos.CopiarParaEditando(IDGateway);
                }
            }
            else if (Origem == 1)
            {
                // copia configuração 'atual' para 'editando'
                medEner = medMetodos.CopiarParaEditando(IDGateway);
                medPrinc = medPrincMetodos.CopiarParaEditando(IDGateway);
                medDemSup = medDemSupMetodos.CopiarParaEditando(IDGateway);
                medVigDem = medVigDemMetodos.CopiarParaEditando(IDGateway);
            }
            else
            {
                // configuração em 'editando'
                medEner = medMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
                medPrinc = medPrincMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
                medDemSup = medDemSupMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
                medVigDem = medVigDemMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            }

            // ordena Demandas Suplementares por medição
            GateX_MedEner_DemSup_Ordena(IDGateway, ref medDemSup);

            // ordena Vigências de Dmanda por medição e data da vigência
            GateX_MedEner_VigDem_Ordena(IDGateway, ref medVigDem);

            // copia status
            ViewBag.SolicitaProg = solicitaProg;
            ViewBag.SolicitaMedPrinc = solicitaMedPrinc;
            ViewBag.SolicitaDemSup = solicitaDemSup;
            ViewBag.SolicitaVigDem = solicitaVigDem;

            // copia configuração
            ViewBag.medEner = medEner;
            ViewBag.medDemSup = medDemSup;
            ViewBag.medVigDem = medVigDem;


            // medição principal (ponta)
            medPrinc.InicioP = medPrinc.Ponta_HoraInicio.ToString("HH:mm");
            medPrinc.FimP = medPrinc.Ponta_HoraFim.ToString("HH:mm");

            medPrinc.DomP = Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.Ponta_UsaDia, 0);
            medPrinc.SegP = Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.Ponta_UsaDia, 1);
            medPrinc.TerP = Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.Ponta_UsaDia, 2);
            medPrinc.QuaP = Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.Ponta_UsaDia, 3);
            medPrinc.QuiP = Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.Ponta_UsaDia, 4);
            medPrinc.SexP = Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.Ponta_UsaDia, 5);
            medPrinc.SabP = Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.Ponta_UsaDia, 6);
            medPrinc.FerP = Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.Ponta_UsaDia, 7);

            // medição principal (posto capacitivo)
            medPrinc.InicioPC = medPrinc.PostoCap_HoraInicio.ToString("HH:mm");
            medPrinc.FimPC = medPrinc.PostoCap_HoraFim.ToString("HH:mm");

            medPrinc.DomPC = Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.PostoCap_UsaDia, 0);
            medPrinc.SegPC = Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.PostoCap_UsaDia, 1);
            medPrinc.TerPC = Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.PostoCap_UsaDia, 2);
            medPrinc.QuaPC = Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.PostoCap_UsaDia, 3);
            medPrinc.QuiPC = Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.PostoCap_UsaDia, 4);
            medPrinc.SexPC = Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.PostoCap_UsaDia, 5);
            medPrinc.SabPC = Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.PostoCap_UsaDia, 6);
            medPrinc.FerPC = Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.PostoCap_UsaDia, 7);

            // retorna configuração
            return (medPrinc);
        }

        // GET: Configuração Medições de Energia - Editar
        public ActionResult GateX_MedEner_Editar(int IDGateway, int NumMedicaoGateway)
        {
            // tela de ajuda - medição de energia
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            GateX_MedEner_PreparaListas(IDGateway);

            // lê medições de energia (editando)
            GateX_MedEner_Metodos medMetodos = new GateX_MedEner_Metodos();
            GateX_MedEner_Dominio programacao = medMetodos.ListarPorNumMedicao(IDGateway, NumMedicaoGateway, STATUS_CFG.EDITANDO);

            // tamanho anel (1=10seg 90=900s=15min=default)
            programacao.TamanhoAnel = (programacao.TamanhoAnel * 10) / 60;

            // alarme falta pulso (segue TipoMedidor) [tyRmXX:[1-90] 1=10s 0=nPrg tyCod[P/Q]:0x80=tyCod[P/Q]_falhaCodi (segue TipoMedidor, se TipoMedidor=3 ou 4 então 0x80, senão é tempo)]
            programacao.TempoPulsos = programacao.TempoPulsos * 10;

            // opções
            programacao.Op0 = Funcoes_Bit.IsBitSet_byte((byte)programacao.opGrEl, 0);
            programacao.Op1 = Funcoes_Bit.IsBitSet_byte((byte)programacao.opGrEl, 1);
            programacao.Op2 = Funcoes_Bit.IsBitSet_byte((byte)programacao.opGrEl, 2);
            programacao.Op3 = Funcoes_Bit.IsBitSet_byte((byte)programacao.opGrEl, 3);
            programacao.Op4 = Funcoes_Bit.IsBitSet_byte((byte)programacao.opGrEl, 4);
            programacao.Op5 = Funcoes_Bit.IsBitSet_byte((byte)programacao.opGrEl, 5);
            programacao.Op6 = Funcoes_Bit.IsBitSet_byte((byte)programacao.opGrEl, 6);
            programacao.Op7 = Funcoes_Bit.IsBitSet_byte((byte)programacao.opGrEl, 7);

            // fórmula
            programacao.Formula = Funcoes_GateX.Formula2String(programacao.Formula);

            // arredonda
            programacao.Constante = Math.Round(programacao.Constante, 5);
            programacao.RTC = Math.Round(programacao.RTC, 5);
            programacao.RTP = Math.Round(programacao.RTP, 5);

            // demandas suplementares em 'editando'
            GateX_MedEner_DemSup_Metodos medDemSupMetodos = new GateX_MedEner_DemSup_Metodos();
            List<GateX_MedEner_DemSup_Dominio> medDemSup = medDemSupMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // ordena Demandas Suplementares por medição
            GateX_MedEner_DemSup_Ordena(IDGateway, ref medDemSup);
            ViewBag.medDemSup = medDemSup;

            // vigência de demanda em 'editando'
            GateX_MedEner_VigDem_Metodos medVigDemMetodos = new GateX_MedEner_VigDem_Metodos();
            List<GateX_MedEner_VigDem_Dominio> medVigDem = medVigDemMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // ordena Vigências de Demanda por medição e data da vigência
            GateX_MedEner_VigDem_Ordena(IDGateway, ref medVigDem);
            ViewBag.medVigDem = medVigDem;


            return View(programacao);
        }

        // prepara listas
        private void GateX_MedEner_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // lista medições em 'editando'
            GateX_MedEner_Metodos medMetodos = new GateX_MedEner_Metodos();
            List<GateX_MedEner_Dominio> listaMedEner = medMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.listaMedEner = listaMedEner;

            // Versão do firmware (número)
            int versao = GateX_Versao_Int(IDGateway);

            // tipos medidores de energia
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoMedidoresEner = new List<ListaTiposDominio>();
            if (versao <= 429)
            {
                listatipoMedidoresEner = listatiposMetodos.ListarTodos("TipoGateX_MedidoresEner_429", false, 0);
            }
            else
            {
                listatipoMedidoresEner = listatiposMetodos.ListarTodos("TipoGateX_MedidoresEner_430", false, 0);
            }
            ViewBag.listatipoMedidoresEner = listatipoMedidoresEner;

            // le tipos rede IO
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DI);      // remove DI
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DO);      // remove DO
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.SEG);     // remove seg
            ViewBag.listaTipoRedeIO = listatipoRedeIO;

            // le tipos numeros remota Rede K
            List<ListaTiposDominio> listatipoNumRemotaRedeK = listatiposMetodos.ListarTodos("TipoGateX_NumRemotaRedeK", false, 0);
            ViewBag.listaTipoNumRemotaRedeK = listatipoNumRemotaRedeK;

            // lista numero remotas
            int[] listaRemotas = new int[SGATEX.NUM_REMOTAS];

            for (int i = 0; i < listaRemotas.Length; i++)
            {
                listaRemotas[i] = i;
            }

            // copia lista remotas
            ViewBag.ListaRemotas = listaRemotas;

            // le tipos reativo
            List<ListaTiposDominio> listatipoReativo = listatiposMetodos.ListarTodos("TipoGateX_Reativo", false, 0);
            ViewBag.listaTipoReativo = listatipoReativo;

            // le lista saidas digitais
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitas = sdMetodos.ListarPorIDGateway(IDGateway);
            List<GateX_SD_Lista_Dominio> listaSaidas = new List<GateX_SD_Lista_Dominio>();

            GateX_SD_Lista_Dominio nenhum = new GateX_SD_Lista_Dominio();
            nenhum.NumSaidaGateway = 255;
            nenhum.Programado = false;
            nenhum.Descricao = "Nenhum";
            listaSaidas.Add(nenhum);

            // percorre saidas
            foreach (GateX_SD_Lista_Dominio saida in saidasDigitas)
            {
                saida.Descricao = string.Format("[{0:00}] {1}{2}", saida.NumSaidaGateway, saida.Descricao, (saida.Programado ? "" : " [Disponível]"));
                listaSaidas.Add(saida);
            }
            ViewBag.listaSaidas = listaSaidas;

            // SinalCodi
            List<ListaTiposDominio> listaSinalCodi = listatiposMetodos.ListarTodos("TipoGateX_SinalCodi", false, 0);
            ViewBag.listaSinalCodi = listaSinalCodi;

            // PeriodoDefault
            List<ListaTiposDominio> listaPeriodoDefault = listatiposMetodos.ListarTodos("TipoGateX_PeriodoDefault", false, 0);
            ViewBag.listaPeriodoDefault = listaPeriodoDefault;

            return;
        }

        // POST: Configuração Medições de Energia - Programar
        [HttpPost]
        public ActionResult GateX_MedEner_Programar(GateX_MedEner_Dominio medicaoEnergia)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // tipo do medidor
            if (medicaoEnergia.TipoMedidor == 0)
            {
                // desprogramado
                medicaoEnergia.Programado = false;

                medicaoEnergia.Descricao = string.Format("Medição de Energia {0}", medicaoEnergia.NumMedicaoGateway);

                medicaoEnergia.TipoMedidor = SGATEX_TIPO_MEDIDOR.DESPROGRAMADO;
                medicaoEnergia.HabReativo = SGATEX_HAB_REATIVO.DESPROGRAMADO;
                medicaoEnergia.Status = 0;
                medicaoEnergia.TamanhoAnel = 90;

                medicaoEnergia.Origem = 0;
                medicaoEnergia.Unidade = 0;
                medicaoEnergia.RecebeHist = false;
                medicaoEnergia.opGrEl2 = 0;
                medicaoEnergia.opGrEl = 0;

                medicaoEnergia.SdNaoAcuMed = 255;
                medicaoEnergia.ReiniMM = false;

                medicaoEnergia.RedeIO = 0;
                medicaoEnergia.Remota = 0;
                medicaoEnergia.Endereco = 0;
                medicaoEnergia.TempoPulsos = 1;
                medicaoEnergia.Constante = 0.0;
                medicaoEnergia.RTP = 0.0;
                medicaoEnergia.RTC = 0.0;
                medicaoEnergia.Formula = "00000000000000000000";

                medicaoEnergia.PrgEntAtiva = 0;
                medicaoEnergia.PrgEntInd = 0;
                medicaoEnergia.PrgEntCap = 0;

                medicaoEnergia.Dem_Ponta = 0.0;
                medicaoEnergia.DemFPonta = 0.0;
            }
            else
            {
                // programado
                medicaoEnergia.Status = 1;

                // tamanho anel (1=10seg 90=900s=15min=default)
                medicaoEnergia.TamanhoAnel = (medicaoEnergia.TamanhoAnel * 60) / 10;

                // alarme falta pulso (segue TipoMedidor) [tyRmXX:[1-90] 1=10s 0=nPrg tyCod[P/Q]:0x80=tyCod[P/Q]_falhaCodi (segue TipoMedidor, se TipoMedidor=3 ou 4 então 0x80, senão é tempo)]
                medicaoEnergia.TempoPulsos = medicaoEnergia.TempoPulsos / 10;

                // tipo do medidor
                switch (medicaoEnergia.TipoMedidor)
                {
                    case SGATEX_TIPO_MEDIDOR.FORMULA:   // fórmula

                        // fórmula
                        string formulaHexa = "";
                        if (!Funcoes_GateX.String2Formula(medicaoEnergia.Formula, ref formulaHexa))
                        {
                            // retorno
                            returnedData = new
                            {
                                status = "ERRO",
                                erro = "Fórmula com erro de sintaxe"
                            };

                            // retorna status
                            return Json(returnedData);
                        }

                        // copia fórmula
                        medicaoEnergia.Formula = formulaHexa;

                        medicaoEnergia.Origem = 4;
                        break;

                    case SGATEX_TIPO_MEDIDOR.CODI:          // CODI

                        medicaoEnergia.Origem = 0;
                        medicaoEnergia.TempoPulsos = 0x80;
                        break;

                    case SGATEX_TIPO_MEDIDOR.CODIQ:         // CODI Q

                        medicaoEnergia.Origem = 0;
                        medicaoEnergia.TempoPulsos = 0x80;
                        break;

                    case SGATEX_TIPO_MEDIDOR.DIN_VIRTUAL:   // DIN virtual

                        medicaoEnergia.Origem = 1;
                        break;

                    case SGATEX_TIPO_MEDIDOR.DI:            // DI

                        medicaoEnergia.Origem = 3;
                        break;

                    case SGATEX_TIPO_MEDIDOR.REM_GENERICA:  // remota genérica
                    default:                                // medidores eletrônicos

                        medicaoEnergia.Origem = 2;
                        break;
                }

                // entradas
                medicaoEnergia.PrgEntAtiva = 1;

                switch (medicaoEnergia.HabReativo)
                {
                    case SGATEX_HAB_REATIVO.DESPROGRAMADO:
                        medicaoEnergia.PrgEntInd = 0;
                        medicaoEnergia.PrgEntCap = 0;
                        break;

                    case SGATEX_HAB_REATIVO.INDUTIVO:
                        medicaoEnergia.PrgEntInd = 1;
                        medicaoEnergia.PrgEntCap = 0;
                        break;

                    case SGATEX_HAB_REATIVO.IND_CAP:
                        medicaoEnergia.PrgEntInd = 1;
                        medicaoEnergia.PrgEntCap = 1;
                        break;

                    case SGATEX_HAB_REATIVO.KQ:
                        medicaoEnergia.PrgEntInd = 1;
                        medicaoEnergia.PrgEntCap = 0;
                        break;
                }

                // opções
                byte opGrEl = 0;
                Funcoes_Bit.BitSet_byte(ref opGrEl, 0, medicaoEnergia.Op0);
                Funcoes_Bit.BitSet_byte(ref opGrEl, 1, medicaoEnergia.Op1);
                Funcoes_Bit.BitSet_byte(ref opGrEl, 2, medicaoEnergia.Op2);
                Funcoes_Bit.BitSet_byte(ref opGrEl, 3, medicaoEnergia.Op3);
                Funcoes_Bit.BitSet_byte(ref opGrEl, 4, medicaoEnergia.Op4);
                Funcoes_Bit.BitSet_byte(ref opGrEl, 5, medicaoEnergia.Op5);
                Funcoes_Bit.BitSet_byte(ref opGrEl, 6, medicaoEnergia.Op6);
                Funcoes_Bit.BitSet_byte(ref opGrEl, 7, medicaoEnergia.Op7);
                medicaoEnergia.opGrEl = (int)opGrEl;
            }

            // salva (editando)
            GateX_MedEner_Metodos medMetodos = new GateX_MedEner_Metodos();
            medMetodos.Salvar(medicaoEnergia, STATUS_CFG.EDITANDO);

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Medições de Energia - Excluir
        public ActionResult GateX_MedEner_Excluir(int IDGateway, int NumMedicaoGateway)
        {
            // apaga programacao (editando)
            GateX_MedEner_Metodos medMetodos = new GateX_MedEner_Metodos();
            medMetodos.ExcluirPorNumMedicao(IDGateway, NumMedicaoGateway, STATUS_CFG.EDITANDO);

            GateX_MedEner_DemSup_Metodos medDemSupMetodos = new GateX_MedEner_DemSup_Metodos();
            medDemSupMetodos.ExcluirPorNumMedicao(IDGateway, NumMedicaoGateway, STATUS_CFG.EDITANDO);

            GateX_MedEner_VigDem_Metodos medVigDemMetodos = new GateX_MedEner_VigDem_Metodos();
            medVigDemMetodos.ExcluirPorNumMedicao(IDGateway, NumMedicaoGateway, STATUS_CFG.EDITANDO);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // POST: Configuração Medições de Energia - Enviar
        [HttpPost]
        public ActionResult GateX_MedEner_Enviar(GateX_MedEner_MedPrinc_Dominio medPrinc)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };


            try
            {
                // auxiliar
                UInt16 UsaDia = 0;

                // medição principal (ponta)
                medPrinc.Ponta_HoraInicio = DateTime.ParseExact(medPrinc.InicioP, "HH:mm", CultureInfo.InvariantCulture);
                medPrinc.Ponta_HoraFim = DateTime.ParseExact(medPrinc.FimP, "HH:mm", CultureInfo.InvariantCulture);

                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 0, medPrinc.DomP);
                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 1, medPrinc.SegP);
                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 2, medPrinc.TerP);
                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 3, medPrinc.QuaP);
                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 4, medPrinc.QuiP);
                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 5, medPrinc.SexP);
                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 6, medPrinc.SabP);
                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 7, medPrinc.FerP);
                medPrinc.Ponta_UsaDia = UsaDia;


                // medição principal (posto capacitivo)
                medPrinc.PostoCap_HoraInicio = DateTime.ParseExact(medPrinc.InicioPC, "HH:mm", CultureInfo.InvariantCulture);
                medPrinc.PostoCap_HoraFim = DateTime.ParseExact(medPrinc.FimPC, "HH:mm", CultureInfo.InvariantCulture);

                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 0, medPrinc.DomPC);
                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 1, medPrinc.SegPC);
                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 2, medPrinc.TerPC);
                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 3, medPrinc.QuaPC);
                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 4, medPrinc.QuiPC);
                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 5, medPrinc.SexPC);
                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 6, medPrinc.SabPC);
                Funcoes_GateX.BitSet_UInt16(ref UsaDia, 7, medPrinc.FerPC);
                medPrinc.PostoCap_UsaDia = UsaDia;

                // salva (editando)
                GateX_MedEner_MedPrinc_Metodos medPrincMetodos = new GateX_MedEner_MedPrinc_Metodos();
                medPrincMetodos.Salvar(medPrinc, STATUS_CFG.EDITANDO);


                // IDGateway
                int IDGateway = medPrinc.IDGateway;

                // le gateway
                GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
                GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

                // medições de energia - copia configuração 'editando' para 'atual'
                GateX_MedEner_Metodos medMetodos = new GateX_MedEner_Metodos();
                List<GateX_MedEner_Dominio> medEner = medMetodos.CopiarParaAtual(IDGateway);


                // medição principal - copia configuração 'editando' para 'atual'
                medPrincMetodos.CopiarParaAtual(IDGateway);


                // demanda suplementar - lê editando
                GateX_MedEner_DemSup_Metodos medDemSupMetodos = new GateX_MedEner_DemSup_Metodos();
                List<GateX_MedEner_DemSup_Dominio> medDemSup = medDemSupMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

                // ordena Demandas Suplementares por medição
                GateX_MedEner_DemSup_Ordena(IDGateway, ref medDemSup);

                // copia configuração 'editando' para 'atual'
                medDemSup = medDemSupMetodos.CopiarParaAtual(IDGateway);



                // vigência de demanda - lê editando
                GateX_MedEner_VigDem_Metodos medVigDemMetodos = new GateX_MedEner_VigDem_Metodos();
                List<GateX_MedEner_VigDem_Dominio> medVigDem = medVigDemMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

                // ordena Vigências de Demanda por medição e data da vigência
                GateX_MedEner_VigDem_Ordena(IDGateway, ref medVigDem);

                // copia configuração 'editando' para 'atual'
                medVigDem = medVigDemMetodos.CopiarParaAtual(IDGateway);


                // variavel de retorno
                bool enviaProg = false;
                bool enviaMedPrinc = false;
                bool enviaDemSup = false;
                bool enviaVigDem = false;

                // verifica
                if (medEner != null && medDemSup != null && medVigDem != null && gateway != null)
                {
                    // comunicação MQTT
                    SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                    // verifica conexão com gateway
                    if (smartCom.Conectar())
                    {
                        // copia para lista de envio
                        smartCom.gateX.MedEner = medEner;
                        smartCom.gateX.MedEner_MedPrinc = medPrinc;
                        smartCom.gateX.MedEner_DemSup = medDemSup;
                        smartCom.gateX.MedEner_VigDem = medVigDem;

                        // envia medição energia
                        enviaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_MED_EN, SMCOM_TIPO_SOL.ENVIA);

                        // envia medição energia (medição principal)
                        enviaMedPrinc = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_MED_PRINC, SMCOM_TIPO_SOL.ENVIA);

                        // envia medição energia (demandas suplementares)
                        enviaDemSup = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_DEM_SUP, SMCOM_TIPO_SOL.ENVIA);

                        // envia medição energia (vigência demanda)
                        enviaVigDem = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_VIG_DEM, SMCOM_TIPO_SOL.ENVIA);
                    }
                }

                // verifica retorno
                if (enviaProg && enviaMedPrinc && enviaDemSup && enviaVigDem)
                {
                    // excluir editando
                    medMetodos.ExcluirEditando(IDGateway);
                    medPrincMetodos.ExcluirEditando(IDGateway);
                    medDemSupMetodos.ExcluirEditando(IDGateway);
                    medVigDemMetodos.ExcluirEditando(IDGateway);

                    // histórico de configuração (medições)
                    HistoricoConfiguracoesDominio hist = new HistoricoConfiguracoesDominio();
                    hist.IDGateway = IDGateway;
                    hist.NomeConfiguracao = "GateX_MedEner";
                    hist.Envio = "MQTT";
                    hist.Atualizacao = DateTime.Now;
                    hist.Versao = GateX_Versao_String(IDGateway);
                    hist.IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");

                    // insere histórico de configuração
                    HistoricoConfiguracoesMetodos histMetodos = new HistoricoConfiguracoesMetodos();
                    histMetodos.Inserir(hist);

                    // histórico de configuração (medição principal)
                    hist.NomeConfiguracao = "GateX_MedEner_MedPrinc";
                    histMetodos.Inserir(hist);

                    // histórico de configuração (demandas suplementares)
                    hist.NomeConfiguracao = "GateX_MedEner_DemSup";
                    histMetodos.Inserir(hist);

                    // histórico de configuração (vigência demanda)
                    hist.NomeConfiguracao = "GateX_MedEner_VigDem";
                    histMetodos.Inserir(hist);


                    // caso OK insiro evento
                    UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, IDGateway);
                }
                else
                {
                    // erro
                    returnedData = new
                    {
                        status = "ERRO",
                        erro = "Falha no envio da configuração!"
                    };
                }
            }
            catch
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Verificar as configurações"
                };
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Medições de Energia - Copiar
        public ActionResult GateX_MedEner_Copiar(int IDGateway, int Origem, int Inicio, int Fim)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // lista programacoes de medições da gateway salvas no banco de dados
            GateX_MedEner_Metodos medMetodos = new GateX_MedEner_Metodos();
            List<GateX_MedEner_Dominio> medEner = medMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.medEner = medEner;

            // lista medição origem
            GateX_MedEner_Dominio medicaoOrigem = medMetodos.ListarPorNumMedicao(IDGateway, Origem, STATUS_CFG.EDITANDO);

            if (medicaoOrigem.NumMedicaoGateway >= Inicio && medicaoOrigem.NumMedicaoGateway <= Fim)
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "A medição origem não pode estar entre as medições destino."
                };

                // retorna erro
                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // auxiliar
            int copia = 1;

            if (Fim < Inicio)
            {
                // copia para medições desejadas decrescente
                for (int i = Inicio; i >= Fim; i--)
                {
                    GateX_MedEner_Dominio medicaoCopia = medMetodos.ListarPorNumMedicao(IDGateway, i, STATUS_CFG.EDITANDO);

                    // copia
                    GateX_MedEner_CopiaConfiguracao(medicaoOrigem, ref medicaoCopia, copia);

                    // salva medição copiada
                    medMetodos.Salvar(medicaoCopia, STATUS_CFG.EDITANDO);

                    // incrementa cópia
                    copia++;
                }
            }

            else
            {
                // copia para medições desejadas crescente
                for (int i = Inicio; i <= Fim; i++)
                {
                    GateX_MedEner_Dominio medicaoCopia = medMetodos.ListarPorNumMedicao(IDGateway, i, STATUS_CFG.EDITANDO);

                    // copia
                    GateX_MedEner_CopiaConfiguracao(medicaoOrigem, ref medicaoCopia, copia);

                    // salva medição copiada
                    medMetodos.Salvar(medicaoCopia, STATUS_CFG.EDITANDO);

                    // incrementa cópia
                    copia++;
                }
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);

        }

        // copia configuração
        public void GateX_MedEner_CopiaConfiguracao(GateX_MedEner_Dominio medicaoOrigem, ref GateX_MedEner_Dominio medicaoCopia, int copia)
        {
            medicaoCopia.IDGateway = medicaoOrigem.IDGateway;
            medicaoCopia.Descricao = medicaoOrigem.Descricao;

            // verifica tamanho se cabe cópia (limite - 3 caracteres)
            if (medicaoCopia.Descricao.Length <= (21-3))
            {
                // coloca indice
                medicaoCopia.Descricao += string.Format(".{0}", copia);
            }

            medicaoCopia.TipoMedidor = medicaoOrigem.TipoMedidor;
            medicaoCopia.HabReativo = medicaoOrigem.HabReativo;
            medicaoCopia.Status = medicaoOrigem.Status;
            medicaoCopia.TamanhoAnel = medicaoOrigem.TamanhoAnel;

            medicaoCopia.Origem = medicaoOrigem.Origem;
            medicaoCopia.Unidade = medicaoOrigem.Unidade;
            medicaoCopia.RecebeHist = medicaoOrigem.RecebeHist;
            medicaoCopia.opGrEl2 = medicaoOrigem.opGrEl2;
            medicaoCopia.opGrEl = medicaoOrigem.opGrEl;

            medicaoCopia.SdNaoAcuMed = medicaoOrigem.SdNaoAcuMed;
            medicaoCopia.ReiniMM = medicaoOrigem.ReiniMM;

            medicaoCopia.RedeIO = medicaoOrigem.RedeIO;
            medicaoCopia.Remota = medicaoOrigem.Remota;
            medicaoCopia.Endereco = medicaoOrigem.Endereco;
            medicaoCopia.TempoPulsos = medicaoOrigem.TempoPulsos;
            medicaoCopia.Constante = medicaoOrigem.Constante;
            medicaoCopia.RTP = medicaoOrigem.RTP;
            medicaoCopia.RTC = medicaoOrigem.RTC;
            medicaoCopia.Formula = medicaoOrigem.Formula;

            medicaoCopia.PrgEntAtiva = medicaoOrigem.PrgEntAtiva;
            medicaoCopia.PrgEntInd = medicaoOrigem.PrgEntInd;
            medicaoCopia.PrgEntCap = medicaoOrigem.PrgEntCap;

            medicaoCopia.Dem_Ponta = medicaoOrigem.Dem_Ponta;
            medicaoCopia.DemFPonta = medicaoOrigem.DemFPonta;

            return;
        }

        // recebe configuração (comando receber configurações)
        public bool GateX_MedEner_ComandoRecebe(GatewaysDominio gateway, string versao)
        {
            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita programação
                if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_MED_EN, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // solicita configuração da medição principal
                    if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_MED_PRINC, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // solicita configuração das demandas suplementares
                        if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_DEM_SUP, SMCOM_TIPO_SOL.SOLICITA))
                        {
                            // solicita configuração das vigências demanda
                            if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_VIG_DEM, SMCOM_TIPO_SOL.SOLICITA))
                            {
                                // salvar (medições de energia)
                                GateX_MedEner_Metodos medMetodos = new GateX_MedEner_Metodos();
                                medMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.MedEner, STATUS_CFG.ATUAL);

                                // salvar (medição principal)
                                GateX_MedEner_MedPrinc_Metodos medPrincMetodos = new GateX_MedEner_MedPrinc_Metodos();
                                medPrincMetodos.Salvar(smartCom.gateX.MedEner_MedPrinc, STATUS_CFG.ATUAL);

                                // salvar (demandas suplementares)
                                GateX_MedEner_DemSup_Metodos medDemSupMetodos = new GateX_MedEner_DemSup_Metodos();
                                medDemSupMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.MedEner_DemSup, STATUS_CFG.ATUAL);

                                // salvar (vigência demanda)
                                GateX_MedEner_VigDem_Metodos medVigDemMetodos = new GateX_MedEner_VigDem_Metodos();
                                medVigDemMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.MedEner_VigDem, STATUS_CFG.ATUAL);

                                // ok
                                return (true);
                            }
                        }
                    }
                }
            }

            // erro
            return (false);
        }
    }
}