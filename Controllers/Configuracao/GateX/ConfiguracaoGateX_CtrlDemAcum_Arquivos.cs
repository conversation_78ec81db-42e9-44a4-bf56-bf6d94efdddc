﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Controle Demanda Acumulada XLS Download
        [HttpGet]
        public virtual ActionResult GateX_CtrlDemAcum_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Controle Demanda Acumulada XLS
        public ActionResult GateX_CtrlDemAcum_XLS(int IDGateway)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_GateX_CtrlDemAcum(IDGateway);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_ControleDemAcum_Gateway_{0:000000}_{1:yyyyMMddHHmm}.xls", IDGateway, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }





        // Controle Demanda Acumulada Planilha
        private HSSFWorkbook XLS_GateX_CtrlDemAcum(int IDGateway)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.DescriConfigCtrlDemAcum;

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // le lista controles demanda acumulada
            GateX_CtrlDemAcum_Metodos ctrlMetodos = new GateX_CtrlDemAcum_Metodos();
            List<GateX_CtrlDemAcum_Dominio> ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // controle demanda acumulada (saídas)
            GateX_CtrlDemAcum_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemAcum_SD_Metodos();
            List<GateX_CtrlDemAcum_SD_Dominio> ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // medições energia
            GateX_MedEner_Lista_Metodos medEnerMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medEner_Lista = medEnerMetodos.ListarPorIDGateway(IDGateway);

            //  le lista de saidas da gateway
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidas = sdMetodos.ListarPorIDGateway(IDGateway);


            // cria planilha
            var sheet = workbook.CreateSheet("Controle Demanda Acumulada");

            // cabecalho
            string[] cabecalho = { "Controle", "Medição", "Medição Secundária", "Saída", "Nível de Desligamento (%)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;


            if (ctrls != null && ctrlSDs != null)
            {
                // percorre controles
                foreach (GateX_CtrlDemAcum_Dominio ctrl in ctrls)
                {
                    if (ctrl.Programado)
                    {
                        // percorre saídas
                        foreach (GateX_CtrlDemAcum_SD_Dominio sd in ctrlSDs)
                        {
                            if (sd.Programado && sd.NumCtrlGateway == ctrl.NumCtrlGateway)
                            {
                                // adiciona linha
                                row = sheet.CreateRow(rowIndex++);

                                // Número controle demanda acumulada
                                numeroCelulaXLS(row, 0, ctrl.NumCtrlGateway, _intCellStyle);

                                // medição
                                string medicao = "---";

                                if (ctrl.Medicao != 255)
                                {
                                    if (medEner_Lista != null)
                                    {
                                        medicao = string.Format("[{0:00}] {1}", ctrl.Medicao, medEner_Lista[ctrl.Medicao].Descricao);
                                    }
                                    else
                                    {
                                        medicao = string.Format("[{0:00}] Medição de Energia {1}", ctrl.Medicao, ctrl.Medicao);
                                    }
                                }

                                textoCelulaXLS(row, 1, medicao);

                                // medição secundária
                                medicao = "---";

                                if (ctrl.Medicao2 != 255)
                                {
                                    if (medEner_Lista != null)
                                    {
                                        medicao = string.Format("[{0:00}] {1}", ctrl.Medicao2, medEner_Lista[ctrl.Medicao2].Descricao);
                                    }
                                    else
                                    {
                                        medicao = string.Format("[{0:00}] Medição de Energia {1}", ctrl.Medicao2, ctrl.Medicao2);
                                    }
                                }

                                textoCelulaXLS(row, 2, medicao);

                                // Nome saída
                                string saida = string.Format("[{0}] Saída {1}", sd.NumSaida, sd.NumSaida);
                                if (saidas != null) saida = string.Format("[{0}] {1}", sd.NumSaida, saidas.Find(x => x.NumSaidaGateway == sd.NumSaida).Descricao);
                                textoCelulaXLS(row, 3, saida);

                                // nível de desligamento
                                numeroCelulaXLS(row, 4, sd.ControlAcumDeslig / 10.0, _1CellStyle);
                            }
                        }
                    }
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
            {
                sheet.SetColumnWidth(i, 7000);
                if (i == 4)
                {
                    sheet.SetColumnWidth(i, 10000);
                }

            }

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS_GateX(workbook, sheet, _negritoCellStyle);

            // largura de cada coluna
            sheet.SetColumnWidth(0, 7000);
            sheet.SetColumnWidth(1, 12000);

            // retorna planilha
            return workbook;

        }

        // GET: Controle Demanda Acumulada PDF
        public ActionResult GateX_CtrlDemAcum_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // controle demanda acumulada
            GateX_CtrlDemAcum_Metodos ctrlMetodos = new GateX_CtrlDemAcum_Metodos();
            List<GateX_CtrlDemAcum_Dominio> ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlDemAcum = ctrls;

            // controle demanda acumulada (saídas)
            GateX_CtrlDemAcum_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemAcum_SD_Metodos();
            List<GateX_CtrlDemAcum_SD_Dominio> ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlDemAcum_SD = ctrlSDs;

            // medições energia
            GateX_MedEner_Lista_Metodos medEnerMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medEner_Lista = medEnerMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medEner_Lista = medEner_Lista;

            // lista saidas 
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.SaidasDigitais = saidasDigitais;


            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_ControleDemAcum_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_CtrlDemAcum_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        // GET: Controle Demanda Acumulada EMAIL
        public async Task<ActionResult> GateX_CtrlDemAcum_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.DescriConfigCtrlDemAcum;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // controle demanda acumulada
            GateX_CtrlDemAcum_Metodos ctrlMetodos = new GateX_CtrlDemAcum_Metodos();
            List<GateX_CtrlDemAcum_Dominio> ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlDemAcum = ctrls;

            // controle demanda acumulada (saídas)
            GateX_CtrlDemAcum_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemAcum_SD_Metodos();
            List<GateX_CtrlDemAcum_SD_Dominio> ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlDemAcum_SD = ctrlSDs;

            // medições energia
            GateX_MedEner_Lista_Metodos medEnerMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medEner_Lista = medEnerMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medEner_Lista = medEner_Lista;

            // lista saidas 
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.SaidasDigitais = saidasDigitais;


            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_ControleDemAcum_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_CtrlDemAcum_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "GateX_ControleDemAcumEmail";
            var message = await EMailTemplateAsync(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.NomeGateway", ViewBag.NomeGateway);
            message = message.Replace("ViewBag.DescrConfig", ViewBag.DescrConfig);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Controle Demanda Acumulada Print
        public ActionResult GateX_CtrlDemAcum_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // controle demanda acumulada
            GateX_CtrlDemAcum_Metodos ctrlMetodos = new GateX_CtrlDemAcum_Metodos();
            List<GateX_CtrlDemAcum_Dominio> ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlDemAcum = ctrls;

            // controle demanda acumulada (saídas)
            GateX_CtrlDemAcum_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemAcum_SD_Metodos();
            List<GateX_CtrlDemAcum_SD_Dominio> ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlDemAcum_SD = ctrlSDs;

            // medições energia
            GateX_MedEner_Lista_Metodos medEnerMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medEner_Lista = medEnerMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medEner_Lista = medEner_Lista;

            // lista saidas 
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.SaidasDigitais = saidasDigitais;


            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // imprime
            return View();
        }
    }
}
