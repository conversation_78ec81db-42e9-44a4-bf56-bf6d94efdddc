﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Configuracao Controle Fator de Potência
        public ActionResult GateX_CtrlFatPot(int IDGateway, int Origem = 0)
        {
            // tela de ajuda - controle fator de potência
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // receber configuração
            List<GateX_CtrlFatPot_Dominio> ctrls = GateX_CtrlFatPot_Receber(IDGateway, Origem);

            // prepara listas
            GateX_CtrlFatPot_PreparaListas(IDGateway);

            // retorna configuração
            return View(ctrls);
        }

        // Configuração Controle Fator de Potência - Receber
        private List<GateX_CtrlFatPot_Dominio> GateX_CtrlFatPot_Receber(int IDGateway, int Origem)
        {
            // controle fator de potência
            GateX_CtrlFatPot_Metodos ctrlMetodos = new GateX_CtrlFatPot_Metodos();
            List<GateX_CtrlFatPot_Dominio> ctrls = new List<GateX_CtrlFatPot_Dominio>();

            // controle fator de potência (saídas)
            GateX_CtrlFatPot_SD_Metodos ctrlSDMetodos = new GateX_CtrlFatPot_SD_Metodos();
            List<GateX_CtrlFatPot_SD_Dominio> ctrlSDs = new List<GateX_CtrlFatPot_SD_Dominio>();

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // status
            bool solicitaCtrl = true;
            bool solicitaCtrlSD = true;

            // verifica Origem
            // 0: ler da gateway
            // 1: ler do banco de dados atual
            // 2: ler do banco de dados editando
            if (Origem == 0)
            {
                // status
                solicitaCtrl = false;
                solicitaCtrlSD = false;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // solicita configuração dos controles fator de potência
                    if (solicitaCtrl = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_FATPOT, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // solicita configuração dos controles fator de potência (saídas)
                        if (solicitaCtrlSD = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_FATPOT_SD, SMCOM_TIPO_SOL.SOLICITA))
                        {
                            // copia configuração da gateway para 'editando'
                            // caso ainda não exista configuração 'atual', será criada 
                            ctrls = ctrlMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.CtrlFatPot);
                            ctrlSDs = ctrlSDMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.CtrlFatPot_SD);
                        }
                    }
                }

                // verifica se solicitou
                if (solicitaCtrl && solicitaCtrlSD)
                {
                    // atualiza lista de medições energia
                    GateX_AtualizaLista(gateway, SMCOM_FUNCOES_SOL.PROGR_MED_EN_LISTA);

                    // atualiza lista de saidas digitais
                    GateX_AtualizaLista(gateway, SMCOM_FUNCOES_SOL.PROGR_SD_LISTA);
                }
                else
                {
                    // copia configuração 'atual' para 'editando'
                    ctrls = ctrlMetodos.CopiarParaEditando(IDGateway);
                    ctrlSDs = ctrlSDMetodos.CopiarParaEditando(IDGateway);
                }
            }
            else if (Origem == 1)
            {
                // copia configuração 'atual' para 'editando'
                ctrls = ctrlMetodos.CopiarParaEditando(IDGateway);
                ctrlSDs = ctrlSDMetodos.CopiarParaEditando(IDGateway);
            }
            else
            {
                // configuração em 'editando'
                ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
                ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            }

            // copia status
            ViewBag.solicitaCtrl = solicitaCtrl;
            ViewBag.solicitaCtrlSD = solicitaCtrlSD;

            // copia configuração
            ViewBag.CtrlFatPot = ctrls;
            ViewBag.CtrlFatPot_SD = ctrlSDs;

            // retorna configuração
            return (ctrls);
        }

        // GET: Configuracao Controle Fator de Potência - Editar
        public ActionResult GateX_CtrlFatPot_Editar(int IDGateway, int NumCtrlGateway)
        {
            // tela de ajuda - controle fator de potência
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            GateX_CtrlFatPot_PreparaListas(IDGateway);

            // controle fator de potência (editando)
            GateX_CtrlFatPot_Metodos ctrlMetodos = new GateX_CtrlFatPot_Metodos();
            GateX_CtrlFatPot_Dominio ctrl = ctrlMetodos.ListarPorNumCtrl(IDGateway, NumCtrlGateway, STATUS_CFG.EDITANDO);

            // divide limites por 1000 (920 = 0.920)
            ctrl.LimiteIndutivo /= 1000.0;
            ctrl.LimiteCapacitivo /= 1000.0;

            // alarmes
            if (ctrl.AlarmeTipoAllOn == 0 && ctrl.AlarmeAuxAllOn == 0)
            {
                // nenhum
                ctrl.AlarmeTipoAllOn = 100;
            }

            if (ctrl.AlarmeTipoAllOff == 0 && ctrl.AlarmeAuxAllOff == 0)
            {
                // nenhum
                ctrl.AlarmeTipoAllOff = 100;
            }

            ViewBag.CtrlFatPot = ctrl;

            // controle fator de potência (saídas)  (editando)
            GateX_CtrlFatPot_SD_Metodos ctrlSDMetodos = new GateX_CtrlFatPot_SD_Metodos();
            List<GateX_CtrlFatPot_SD_Dominio> ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlFatPot_SD = ctrlSDs;

            // medições
            GateX_CtrlFatPot_ObterMedicoes(IDGateway);

            // saidas
            GateX_CtrlFatPot_ObterSaidas(IDGateway);

            return View(ctrl);
        }

        private void GateX_CtrlFatPot_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // medições energia
            GateX_MedEner_Lista_Metodos medEnerMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medEner_Lista = medEnerMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medEner_Lista = medEner_Lista;

            // medições
            List<ListaTiposDominio> listaMedicoes = new List<ListaTiposDominio>();
            ViewBag.listaMedicoes = listaMedicoes;

            // le saidas
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.SaidasDigitais = saidasDigitais;

            // saidas
            List<ListaTiposDominio> listaSaidas = new List<ListaTiposDominio>();
            ViewBag.listaSaidas = listaSaidas;

            // le tipo comando alarme
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoCmdAlarme = listatiposMetodos.ListarTodos("TipoCmdAlarme", false, 0);
            ViewBag.listaTipoCmdAlarme = listatipoCmdAlarme;

            // le tipo alarme sistema
            List<ListaTiposDominio> listatipoAlarmeSistema = listatiposMetodos.ListarTodos("TipoAlarmeSistema", false, 0);
            ViewBag.listaTipoAlarmeSistema = listatipoAlarmeSistema;

            // le tipo controle demanda projetada
            List<ListaTiposDominio> listatipoCtrPj = listatiposMetodos.ListarTodos("TipoCtrlDemandaPrj", false, 0);
            ViewBag.listaTipoCtrPj = listatipoCtrPj;

            // le tipo controle fator de potencia indutivo
            List<ListaTiposDominio> listatipoCtrFPInd = listatiposMetodos.ListarTodos("TipoCtrlFPInd", false, 0);
            ViewBag.listaTipoCtrFPInd = listatipoCtrFPInd;

            // le tipo controle fator de potencia capacitivo
            List<ListaTiposDominio> listatipoCtrFPCap = listatiposMetodos.ListarTodos("TipoCtrlFPCap", false, 0);
            ViewBag.listaTipoCtrFPCap = listatipoCtrFPCap;

            // cria lista de alarmes do usuario
            List<ListaTiposDominio> listaAlmUsuario = new List<ListaTiposDominio>();

            for (int i = 0; i < SGATEX.NUM_CTRL_ALM; i++)
            {
                var alm = new ListaTiposDominio()
                {
                    ID = i,
                    Descricao = string.Format("Alarme de Usuário [{0:00}]", i)
                };

                listaAlmUsuario.Add(alm);
            }

            // copia lista alarme usuario
            ViewBag.listaAlmUsuario = listaAlmUsuario;

            // cria lista de prioridades do capacitor
            List<ListaTiposDominio> listaPrioCap = new List<ListaTiposDominio>();

            for (int i = 0; i < SGATEX.NUM_PRIO_ADAP; i++)
            {
                var prio = new ListaTiposDominio()
                {
                    ID = i,
                    Descricao = string.Format("Prioridade {0}", i)
                };

                listaPrioCap.Add(prio);
            }

            // copia lista prioridades do capacitor
            ViewBag.listaPrioCap = listaPrioCap;

            return;
        }

        // POST: Configuracao Controle Fator de Potência - Programar
        [HttpPost]
        public ActionResult GateX_CtrlFatPot_Programar(GateX_CtrlFatPot_Dominio CtrlFatPot, List<GateX_CtrlFatPot_SD_Dominio> ctrlSDs)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };


            // multiplica limites por 1000 (920 = 0.920)
            CtrlFatPot.LimiteIndutivo *= 1000.0;
            CtrlFatPot.LimiteCapacitivo *= 1000.0;

            // alarmes
            if (CtrlFatPot.AlarmeTipoAllOn == 100)
            {
                // nenhum
                CtrlFatPot.AlarmeTipoAllOn = 0;
                CtrlFatPot.AlarmeAuxAllOn = 0;
            }

            if (CtrlFatPot.AlarmeTipoAllOff == 100)
            {
                // nenhum
                CtrlFatPot.AlarmeTipoAllOff = 0;
                CtrlFatPot.AlarmeAuxAllOff = 0;
            }

            // salva controle fator de potência
            GateX_CtrlFatPot_Metodos ctrlMetodos = new GateX_CtrlFatPot_Metodos();
            ctrlMetodos.Salvar(CtrlFatPot, STATUS_CFG.EDITANDO);

            // excluir todas saidas do controle
            GateX_CtrlFatPot_SD_Metodos ctrlSDMetodos = new GateX_CtrlFatPot_SD_Metodos();
            ctrlSDMetodos.ExcluirPorNumCtrl(CtrlFatPot.IDGateway, CtrlFatPot.NumCtrlGateway, STATUS_CFG.EDITANDO);

            // salva controle fator de potência (saídas)
            foreach (GateX_CtrlFatPot_SD_Dominio sd in ctrlSDs)
            {
                // verifica se saída é deste controle
                if (sd.NumCtrlGateway == CtrlFatPot.NumCtrlGateway)
                {
                    ctrlSDMetodos.Salvar(sd, STATUS_CFG.EDITANDO);
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuracao Controle Fator de Potência - Excluir
        public ActionResult GateX_CtrlFatPot_Excluir(int IDGateway, int NumCtrlGateway)
        {
            // lista gateway para inserir evento                        
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // apaga programacao
            GateX_CtrlFatPot_Metodos ctrlMetodos = new GateX_CtrlFatPot_Metodos();
            ctrlMetodos.ExcluirPorNumCtrl(IDGateway, NumCtrlGateway, STATUS_CFG.EDITANDO);

            GateX_CtrlFatPot_SD_Metodos ctrlSDMetodos = new GateX_CtrlFatPot_SD_Metodos();
            ctrlSDMetodos.ExcluirPorNumCtrl(IDGateway, NumCtrlGateway, STATUS_CFG.EDITANDO);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // POST: Configuracao Controle Fator de Potência - Enviar
        [HttpPost]
        public ActionResult GateX_CtrlFatPot_Enviar(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // copia configuração 'editando' para 'atual'
            GateX_CtrlFatPot_Metodos ctrlMetodos = new GateX_CtrlFatPot_Metodos();
            List<GateX_CtrlFatPot_Dominio> ctrls = ctrlMetodos.CopiarParaAtual(IDGateway);

            GateX_CtrlFatPot_SD_Metodos ctrlSDMetodos = new GateX_CtrlFatPot_SD_Metodos();
            List<GateX_CtrlFatPot_SD_Dominio> ctrlSDs = ctrlSDMetodos.CopiarParaAtual(IDGateway);

            // variavel de retorno
            bool enviaCtrlFatPot = false;
            bool enviaCtrlFatPot_SD = false;

            // verifica
            if (ctrls != null && ctrlSDs != null && gateway != null)
            {
                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // copia para lista de envio
                    smartCom.gateX.CtrlFatPot = ctrls;

                    // envia controle fator de potência
                    enviaCtrlFatPot = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_FATPOT, SMCOM_TIPO_SOL.ENVIA);

                    // copia para lista de envio
                    smartCom.gateX.CtrlFatPot_SD = ctrlSDs;

                    // envia controle fator de potência (saídas)
                    enviaCtrlFatPot_SD = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_FATPOT_SD, SMCOM_TIPO_SOL.ENVIA);
                }
            }

            // verifica retorno
            if (enviaCtrlFatPot && enviaCtrlFatPot_SD)
            {
                // excluir editando
                ctrlMetodos.ExcluirEditando(IDGateway);
                ctrlSDMetodos.ExcluirEditando(IDGateway);

                // histórico de configuração
                HistoricoConfiguracoesDominio hist = new HistoricoConfiguracoesDominio();
                hist.IDGateway = IDGateway;
                hist.NomeConfiguracao = "GateX_CtrlFatPot";
                hist.Envio = "MQTT";
                hist.Atualizacao = DateTime.Now;
                hist.Versao = GateX_Versao_String(IDGateway);
                hist.IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");

                // insere histórico de configuração
                HistoricoConfiguracoesMetodos histMetodos = new HistoricoConfiguracoesMetodos();
                histMetodos.Inserir(hist);

                // controle fator de potência (saídas)
                hist.NomeConfiguracao = "GateX_CtrlFatPot_SD";
                histMetodos.Inserir(hist);

                // caso OK insiro evento
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, IDGateway);
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Falha no envio da configuração!"
                };
            }

            // retorna status
            return Json(returnedData);
        }




        //
        // MEDICOES
        //

        // GET: Obter Medicoes
        public JsonResult GateX_CtrlFatPot_ObterMedicoes(int IDGateway)
        {
            // medições
            List<ListaTiposDominio> listaMedicoes = new List<ListaTiposDominio>();

            // medições energia
            GateX_MedEner_Lista_Metodos medEnerMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medEner_Lista = medEnerMetodos.ListarPorIDGateway(IDGateway);

            // nenhum
            ListaTiposDominio tipo_nenhum = new ListaTiposDominio();
            tipo_nenhum.ID = 255;
            tipo_nenhum.Descricao = "Nenhum";
            listaMedicoes.Add(tipo_nenhum);

            // preenche lista de medições
            for (int i = 0; i < SGATEX.NUM_MED_EN; i++)
            {
                // default
                ListaTiposDominio tipo = new ListaTiposDominio();
                tipo.ID = i;
                tipo.Descricao = string.Format("[{0:00}] Medição de Energia {1}", i, i);

                // copia
                if (medEner_Lista != null)
                {
                    // verifica se tem na lista
                    GateX_MedEner_Lista_Dominio med = medEner_Lista.First(item => item.NumMedicaoGateway == i);

                    if (med != null)
                    {
                        tipo.Descricao = string.Format("[{0:00}] {1}", i, med.Descricao);
                    }
                }

                // insere na lista
                listaMedicoes.Add(tipo);
            }

            // lista de medições
            ViewBag.listaMedicoes = listaMedicoes;

            // retorna o valor em JSON
            return Json(listaMedicoes, JsonRequestBehavior.AllowGet);
        }



        //
        // SAIDAS
        //

        // GET: Obter Saidas
        public JsonResult GateX_CtrlFatPot_ObterSaidas(int IDGateway)
        {
            // saidas
            List<ListaTiposDominio> listaSaidas = new List<ListaTiposDominio>();

            // le saidas
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);

            // preenche lista de saídas
            for (int i = 0; i < SGATEX.NUM_SD; i++)
            {
                // default
                ListaTiposDominio tipo = new ListaTiposDominio();
                tipo.ID = i;
                tipo.Descricao = string.Format("[{0:00}] Saída Digital {1}", i, i);

                // copia
                if (saidasDigitais != null)
                {
                    // verifica se tem na lista
                    GateX_SD_Lista_Dominio med = saidasDigitais.First(item => item.NumSaidaGateway == i);

                    if (med != null)
                    {
                        tipo.Descricao = string.Format("[{0:00}] {1}", i, med.Descricao);
                    }
                }

                // insere na lista
                listaSaidas.Add(tipo);
            }

            // lista de saídas
            ViewBag.listaSaidas = listaSaidas;

            // retorna o valor em JSON
            return Json(listaSaidas, JsonRequestBehavior.AllowGet);
        }

        // recebe configuração (comando receber configurações)
        public bool GateX_CtrlFatPot_ComandoRecebe(GatewaysDominio gateway, string versao)
        {
            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita programação
                if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_FATPOT, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // solicita configuração dos controles fator de potência (saídas)
                    if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_FATPOT_SD, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // salvar
                        GateX_CtrlFatPot_Metodos ctrlMetodos = new GateX_CtrlFatPot_Metodos();
                        ctrlMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.CtrlFatPot, STATUS_CFG.ATUAL);

                        // salvar (controle fator de potência (saídas))
                        GateX_CtrlFatPot_SD_Metodos ctrlSDMetodos = new GateX_CtrlFatPot_SD_Metodos();
                        ctrlSDMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.CtrlFatPot_SD, STATUS_CFG.ATUAL);

                        // ok
                        return (true);
                    }
                }
            }

            // erro
            return (false);
        }
    }
}