﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Configuracao Medições Analógicas
        public ActionResult GateX_MedAna(int IDGateway, int Origem = 0)
        {
            // tela de ajuda - medição analógica
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // receber configuração
            List<GateX_MedAna_Dominio> medAna = GateX_MedAna_Receber(IDGateway, Origem);

            // prepara listas
            GateX_MedAna_PreparaListas(IDGateway);

            // retorna configuração
            return View(medAna);
        }

        // Configuração Medições Analógicas - Receber
        private List<GateX_MedAna_Dominio> GateX_MedAna_Receber(int IDGateway, int Origem)
        {
            // medições analógicas
            GateX_MedAna_Metodos medMetodos = new GateX_MedAna_Metodos();
            List<GateX_MedAna_Dominio> medAna = new List<GateX_MedAna_Dominio>();

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // status
            bool solicitaProg = true;

            // verifica Origem
            // 0: ler da gateway
            // 1: ler do banco de dados atual
            // 2: ler do banco de dados editando
            if (Origem == 0)
            {
                // versão (salva versão)
                GateX_Versao_String(IDGateway);

                // status
                solicitaProg = false;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // solicita programação de medicoes analógicas
                    if (solicitaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_MED_AN, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // copia configuração da gateway para 'editando'
                        // caso ainda não exista configuração 'atual', será criada 
                        medAna = medMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.MedAna);
                    }
                }

                // verifica se solicitou
                if (solicitaProg)
                {
                    // atualiza lista de saidas digitais
                    GateX_AtualizaLista(gateway, SMCOM_FUNCOES_SOL.PROGR_SD_LISTA);
                }
                else
                {
                    // copia configuração 'atual' para 'editando'
                    medAna = medMetodos.CopiarParaEditando(IDGateway);
                }
            }
            else if (Origem == 1)
            {
                // copia configuração 'atual' para 'editando'
                medAna = medMetodos.CopiarParaEditando(IDGateway);
            }
            else
            {
                // configuração em 'editando'
                medAna = medMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            }

            // copia status
            ViewBag.SolicitaProg = solicitaProg;

            // retorna configuração
            return (medAna);
        }

        // GET: Configuração Medições Analógicas - Editar
        public ActionResult GateX_MedAna_Editar(int IDGateway, int NumMedicaoGateway)
        {
            // tela de ajuda - medição analógica
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // lê medições analógicas (editando)
            GateX_MedAna_Metodos medMetodos = new GateX_MedAna_Metodos();
            GateX_MedAna_Dominio programacao = medMetodos.ListarPorNumMedicao(IDGateway, NumMedicaoGateway, STATUS_CFG.EDITANDO);

            // temporizadores
            programacao.TempoAmostraFaltaPulso = (programacao.TempoAmostraFaltaPulso * 10);
            programacao.TempoAmostraMediaMovel = (programacao.TempoAmostraMediaMovel * 10);
            programacao.v1_TempoZerarRaw = (programacao.v1_TempoZerarRaw * 10);
            programacao.v2_TempoZerarRaw = (programacao.v2_TempoZerarRaw * 10);

            // arredonda
            programacao.v1_EntAnalogicaMin = Math.Round(programacao.v1_EntAnalogicaMin, 5);
            programacao.v1_EntAnalogicaMax = Math.Round(programacao.v1_EntAnalogicaMax, 5);
            programacao.v1_UnidadeEngMin = Math.Round(programacao.v1_UnidadeEngMin, 5);
            programacao.v1_UnidadeEngMax = Math.Round(programacao.v1_UnidadeEngMax, 5);
            programacao.v2_ConstanteMult = Math.Round(programacao.v2_ConstanteMult, 5);
            programacao.v2_ConstanteSoma = Math.Round(programacao.v2_ConstanteSoma, 5);
            programacao.v4_UnidadeEngMin = Math.Round(programacao.v4_UnidadeEngMin, 5);
            programacao.v4_UnidadeEngMax = Math.Round(programacao.v4_UnidadeEngMax, 5);

            // prepara listas
            GateX_MedAna_PreparaListas(IDGateway, programacao.TipoVariavel);

            return View(programacao);
        }

        // prepara listas
        private void GateX_MedAna_PreparaListas(int IDGateway, int TipoVariavel = 0)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // le tipos rede IO
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DO);      // remove DO

            // verifica se Entrada Analógica
            if (TipoVariavel == SGATEX_TIPO_VARIAVEL.REM_EA)
            {
                listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.SEG);     // remove seg
            }

            ViewBag.listaTipoRedeIO = listatipoRedeIO;

            // le tipos numeros remota Rede K
            List<ListaTiposDominio> listatipoNumRemotaRedeK = listatiposMetodos.ListarTodos("TipoGateX_NumRemotaRedeK", false, 0);
            ViewBag.listaTipoNumRemotaRedeK = listatipoNumRemotaRedeK;

            // lista numero remotas
            int[] listaRemotas = new int[SGATEX.NUM_REMOTAS];

            for (int i = 0; i < listaRemotas.Length; i++)
            {
                listaRemotas[i] = i;
            }

            // copia lista remotas
            ViewBag.ListaRemotas = listaRemotas;

            // le lista tipo variável
            List<ListaTiposDominio> listatipoVariavel = listatiposMetodos.ListarTodos("TipoGateX_Variavel_AN", false, 0);
            ViewBag.listatipoVariavel = listatipoVariavel;

            // le lista tipo variável formato
            List<ListaTiposDominio> listatipoVariavelFormato = listatiposMetodos.ListarTodos("TipoGateX_Variavel_Formato", false, 0);
            ViewBag.listatipoVariavelFormato = listatipoVariavelFormato;

            // le lista tipo escala
            List<ListaTiposDominio> listatipoEscala = listatiposMetodos.ListarTodos("TipoGateX_Escala", false, 0);
            ViewBag.listatipoEscala = listatipoEscala;


            // lista medições
            GateX_MedEner_Metodos medMetodos = new GateX_MedEner_Metodos();
            List<GateX_MedEner_Dominio> listaMedEner = medMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.ATUAL);
            ViewBag.listaMedEner = listaMedEner;

            // le lista tipo variável grandezas elétricas
            List<ListaTiposDominio> listatipoVariavelGrandEletrica = listatiposMetodos.ListarTodos("TipoGateX_Variavel_GrandEletrica", false, 0);
            ViewBag.listatipoVariavelGrandEletrica = listatipoVariavelGrandEletrica;

            return;
        }

        // POST: Configuração Medições Analógicas - Programar
        [HttpPost]
        public ActionResult GateX_MedAna_Programar(GateX_MedAna_Dominio medAna)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // tipo do medidor
            if (medAna.TipoVariavel == SGATEX_TIPO_VARIAVEL.DESPROGRAMADO)
            {
                // desprogramado
                medAna.Programado = false;
                medAna.Programacao = SGATEX_TIPO_GG.DESPROGRAMADO;

                medAna.Descricao = string.Format("Medição Analógica {0}", medAna.NumMedicaoGateway);
                medAna.Unidade = "";

                medAna.TipoVariavel = SGATEX_TIPO_VARIAVEL.DESPROGRAMADO;
                medAna.RecebeHist = 0;
                medAna.TempoAmostraFaltaPulso = 0;
                medAna.TempoAmostraMediaMovel = 0;
                medAna.ZeraAcumuladoSd = 255;
                medAna.NaoAcumularSd = 255;

                medAna.v1_RedeIO = 0;
                medAna.v1_Remota = 0;
                medAna.v1_RemotaEnd = 0;
                medAna.v1_RemotaVar = 0;
                medAna.v1_TempoZerarRaw = 0;
                medAna.v1_EntAnalogicaMin = 0.0;
                medAna.v1_EntAnalogicaMax = 0.0;
                medAna.v1_UnidadeEngMin = 0.0;
                medAna.v1_UnidadeEngMax = 0.0;
                medAna.v1_UpScale = 0;
                medAna.v1_HabFuncaoLN = 0;
                medAna.v1_FuncaoLN = new int[] { 0, 0, 0, 0, 0, 0 };

                medAna.v2_RedeIO = 0;
                medAna.v2_Remota = 0;
                medAna.v2_RemotaEnd = 0;
                medAna.v2_RemotaVar = 0;
                medAna.v2_TempoZerarRaw = 0;
                medAna.v2_ConstanteMult = 0.0;
                medAna.v2_ConstanteSoma = 0.0;

                medAna.v4_MedicaoEE = 0;
                medAna.v4_Offset = 0;
                medAna.v4_THD = 0;
                medAna.v4_UnidadeEngMin = 0.0;
                medAna.v4_UnidadeEngMax = 0.0;
            }
            else
            {
                // programado
                medAna.Programacao = SGATEX_TIPO_GG.GG_AN;

                // temporizadores
                medAna.TempoAmostraFaltaPulso = (medAna.TempoAmostraFaltaPulso / 10);
                medAna.TempoAmostraMediaMovel = (medAna.TempoAmostraMediaMovel / 10);
                medAna.v1_TempoZerarRaw = (medAna.v1_TempoZerarRaw / 10);
                medAna.v2_TempoZerarRaw = (medAna.v2_TempoZerarRaw / 10);

                // na gateway está implementado, mas não no Smart32
                medAna.v1_HabFuncaoLN = 0;
                medAna.v1_FuncaoLN = new int[] { 0, 0, 0, 0, 0, 0 };

                // THD (offset entre 58 e 113)
                medAna.v4_THD = (medAna.v4_Offset >= 58) ? 1 : 0;
            }

            // salva (editando)
            GateX_MedAna_Metodos medMetodos = new GateX_MedAna_Metodos();
            medMetodos.Salvar(medAna, STATUS_CFG.EDITANDO);

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Medições Analógicas - Excluir
        public ActionResult GateX_MedAna_Excluir(int IDGateway, int NumMedicaoGateway)
        {
            // apaga programacao (editando)
            GateX_MedAna_Metodos medMetodos = new GateX_MedAna_Metodos();
            medMetodos.ExcluirPorNumMedicao(IDGateway, NumMedicaoGateway, STATUS_CFG.EDITANDO);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // POST: Configuração Medições Analógicas - Enviar
        [HttpPost]
        public ActionResult GateX_MedAna_Enviar(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };


            try
            {
                // le gateway
                GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
                GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

                // medições analógicas - copia configuração 'editando' para 'atual'
                GateX_MedAna_Metodos medMetodos = new GateX_MedAna_Metodos();
                List<GateX_MedAna_Dominio> medAna = medMetodos.CopiarParaAtual(IDGateway);

                // variavel de retorno
                bool enviaProg = false;

                // verifica
                if (medAna != null && gateway != null)
                {
                    // comunicação MQTT
                    SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                    // verifica conexão com gateway
                    if (smartCom.Conectar())
                    {
                        // copia para lista de envio
                        smartCom.gateX.MedAna = medAna;

                        // envia medição de Anaidades
                        enviaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_MED_AN, SMCOM_TIPO_SOL.ENVIA);
                    }
                }

                // verifica retorno
                if (enviaProg)
                {
                    // excluir editando
                    medMetodos.ExcluirEditando(IDGateway);

                    // histórico de configuração (medições)
                    HistoricoConfiguracoesDominio hist = new HistoricoConfiguracoesDominio();
                    hist.IDGateway = IDGateway;
                    hist.NomeConfiguracao = "GateX_MedAna";
                    hist.Envio = "MQTT";
                    hist.Atualizacao = DateTime.Now;
                    hist.Versao = GateX_Versao_String(IDGateway);
                    hist.IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");

                    // insere histórico de configuração
                    HistoricoConfiguracoesMetodos histMetodos = new HistoricoConfiguracoesMetodos();
                    histMetodos.Inserir(hist);

                    // caso OK insiro evento
                    UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, IDGateway);
                }
                else
                {
                    // erro
                    returnedData = new
                    {
                        status = "ERRO",
                        erro = "Falha no envio da configuração!"
                    };
                }
            }
            catch
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Verificar as configurações"
                };
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Medições Analógicas - Copiar
        public ActionResult GateX_MedAna_Copiar(int IDGateway, int Origem, int Inicio, int Fim)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // lista programacoes de medições da gateway salvas no banco de dados
            GateX_MedAna_Metodos medMetodos = new GateX_MedAna_Metodos();
            List<GateX_MedAna_Dominio> medAna = medMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.medAna = medAna;

            // lista medição origem
            GateX_MedAna_Dominio medicaoOrigem = medMetodos.ListarPorNumMedicao(IDGateway, Origem, STATUS_CFG.EDITANDO);

            if (medicaoOrigem.NumMedicaoGateway >= Inicio && medicaoOrigem.NumMedicaoGateway <= Fim)
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "A medição origem não pode estar entre as medições destino."
                };

                // retorna erro
                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // auxiliar
            int copia = 1;

            if (Fim < Inicio)
            {
                // copia para medições desejadas decrescente
                for (int i = Inicio; i >= Fim; i--)
                {
                    GateX_MedAna_Dominio medicaoCopia = medMetodos.ListarPorNumMedicao(IDGateway, i, STATUS_CFG.EDITANDO);

                    // copia
                    GateX_MedAna_CopiaConfiguracao(medicaoOrigem, ref medicaoCopia, copia);

                    // salva medição copiada
                    medMetodos.Salvar(medicaoCopia, STATUS_CFG.EDITANDO);

                    // incrementa cópia
                    copia++;
                }
            }

            else
            {
                // copia para medições desejadas crescente
                for (int i = Inicio; i <= Fim; i++)
                {
                    GateX_MedAna_Dominio medicaoCopia = medMetodos.ListarPorNumMedicao(IDGateway, i, STATUS_CFG.EDITANDO);

                    // copia
                    GateX_MedAna_CopiaConfiguracao(medicaoOrigem, ref medicaoCopia, copia);

                    // salva medição copiada
                    medMetodos.Salvar(medicaoCopia, STATUS_CFG.EDITANDO);

                    // incrementa cópia
                    copia++;
                }
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // copia configuração
        public void GateX_MedAna_CopiaConfiguracao(GateX_MedAna_Dominio medicaoOrigem, ref GateX_MedAna_Dominio medicaoCopia, int copia)
        {
            medicaoCopia.IDGateway = medicaoOrigem.IDGateway;

            medicaoCopia.Descricao = medicaoOrigem.Descricao;
            medicaoCopia.Unidade = medicaoOrigem.Unidade;

            // verifica tamanho se cabe cópia (limite - 3 caracteres)
            if (medicaoCopia.Descricao.Length <= (29 - 3))
            {
                // coloca indice
                medicaoCopia.Descricao += string.Format(".{0}", copia);
            }

            medicaoCopia.Programacao = medicaoOrigem.Programacao;
            medicaoCopia.TipoVariavel = medicaoOrigem.TipoVariavel;
            medicaoCopia.RecebeHist = medicaoOrigem.RecebeHist;
            medicaoCopia.TempoAmostraFaltaPulso = medicaoOrigem.TempoAmostraFaltaPulso;
            medicaoCopia.TempoAmostraMediaMovel = medicaoOrigem.TempoAmostraMediaMovel;
            medicaoCopia.ZeraAcumuladoSd = medicaoOrigem.ZeraAcumuladoSd;
            medicaoCopia.NaoAcumularSd = medicaoOrigem.NaoAcumularSd;

            medicaoCopia.v1_RedeIO = medicaoOrigem.v1_RedeIO;
            medicaoCopia.v1_Remota = medicaoOrigem.v1_Remota;
            medicaoCopia.v1_RemotaEnd = medicaoOrigem.v1_RemotaEnd;
            medicaoCopia.v1_RemotaVar = medicaoOrigem.v1_RemotaVar;
            medicaoCopia.v1_TempoZerarRaw = medicaoOrigem.v1_TempoZerarRaw;
            medicaoCopia.v1_EntAnalogicaMin = medicaoOrigem.v1_EntAnalogicaMin;
            medicaoCopia.v1_EntAnalogicaMax = medicaoOrigem.v1_EntAnalogicaMax;
            medicaoCopia.v1_UnidadeEngMin = medicaoOrigem.v1_UnidadeEngMin;
            medicaoCopia.v1_UnidadeEngMax = medicaoOrigem.v1_UnidadeEngMax;
            medicaoCopia.v1_UpScale = medicaoOrigem.v1_UpScale;
            medicaoCopia.v1_HabFuncaoLN = medicaoOrigem.v1_HabFuncaoLN;
            medicaoCopia.v1_FuncaoLN = medicaoOrigem.v1_FuncaoLN;

            medicaoCopia.v2_RedeIO = medicaoOrigem.v2_RedeIO;
            medicaoCopia.v2_Remota = medicaoOrigem.v2_Remota;
            medicaoCopia.v2_RemotaEnd = medicaoOrigem.v2_RemotaEnd;
            medicaoCopia.v2_RemotaVar = medicaoOrigem.v2_RemotaVar;
            medicaoCopia.v2_TempoZerarRaw = medicaoOrigem.v2_TempoZerarRaw;
            medicaoCopia.v2_ConstanteMult = medicaoOrigem.v2_ConstanteMult;
            medicaoCopia.v2_ConstanteSoma = medicaoOrigem.v2_ConstanteSoma;

            medicaoCopia.v4_MedicaoEE = medicaoOrigem.v4_MedicaoEE;
            medicaoCopia.v4_Offset = medicaoOrigem.v4_Offset;
            medicaoCopia.v4_THD = medicaoOrigem.v4_THD;
            medicaoCopia.v4_UnidadeEngMin = medicaoOrigem.v4_UnidadeEngMin;
            medicaoCopia.v4_UnidadeEngMax = medicaoOrigem.v4_UnidadeEngMax;

            return;
        }

        // recebe configuração (comando receber configurações)
        public bool GateX_MedAna_ComandoRecebe(GatewaysDominio gateway, string versao)
        {
            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita programação
                if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_MED_AN, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // salvar
                    GateX_MedAna_Metodos medMetodos = new GateX_MedAna_Metodos();
                    medMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.MedAna, STATUS_CFG.ATUAL);

                    // ok
                    return (true);
                }
            }

            // erro
            return (false);
        }
    }
}