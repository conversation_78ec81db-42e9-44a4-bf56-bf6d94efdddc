﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Configuracao Controle Demanda Acumulada
        public ActionResult GateX_CtrlDemAcum(int IDGateway, int Origem = 0)
        {
            // tela de ajuda - controle demanda acumulada
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // receber configuração
            List<GateX_CtrlDemAcum_Dominio> ctrls = GateX_CtrlDemAcum_Receber(IDGateway, Origem);

            // prepara listas
            GateX_CtrlDemAcum_PreparaListas(IDGateway);

            // retorna configuração
            return View(ctrls);
        }

        // Configuração Controle Demanda Acumulada - Receber
        private List<GateX_CtrlDemAcum_Dominio> GateX_CtrlDemAcum_Receber(int IDGateway, int Origem)
        {
            // controle demanda acumulada
            GateX_CtrlDemAcum_Metodos ctrlMetodos = new GateX_CtrlDemAcum_Metodos();
            List<GateX_CtrlDemAcum_Dominio> ctrls = new List<GateX_CtrlDemAcum_Dominio>();

            // controle demanda acumulada (saídas)
            GateX_CtrlDemAcum_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemAcum_SD_Metodos();
            List<GateX_CtrlDemAcum_SD_Dominio> ctrlSDs = new List<GateX_CtrlDemAcum_SD_Dominio>();

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // status
            bool solicitaCtrl = true;
            bool solicitaCtrlSD = true;

            // verifica Origem
            // 0: ler da gateway
            // 1: ler do banco de dados atual
            // 2: ler do banco de dados editando
            if (Origem == 0)
            {
                // status
                solicitaCtrl = false;
                solicitaCtrlSD = false;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // solicita configuração dos controles demanda acumulada
                    if (solicitaCtrl = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_DEM_ACUM, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // solicita configuração dos controles demanda acumulada (saídas)
                        if (solicitaCtrlSD = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_DEM_ACUM_SD, SMCOM_TIPO_SOL.SOLICITA))
                        {
                            // copia configuração da gateway para 'editando'
                            // caso ainda não exista configuração 'atual', será criada 
                            ctrls = ctrlMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.CtrlDemAcum);
                            ctrlSDs = ctrlSDMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.CtrlDemAcum_SD);
                        }
                    }
                }

                // verifica se solicitou
                if (solicitaCtrl && solicitaCtrlSD)
                {
                    // atualiza lista de medições energia
                    GateX_AtualizaLista(gateway, SMCOM_FUNCOES_SOL.PROGR_MED_EN_LISTA);

                    // atualiza lista de saidas digitais
                    GateX_AtualizaLista(gateway, SMCOM_FUNCOES_SOL.PROGR_SD_LISTA);
                }
                else
                {
                    // copia configuração 'atual' para 'editando'
                    ctrls = ctrlMetodos.CopiarParaEditando(IDGateway);
                    ctrlSDs = ctrlSDMetodos.CopiarParaEditando(IDGateway);
                }
            }
            else if (Origem == 1)
            {
                // copia configuração 'atual' para 'editando'
                ctrls = ctrlMetodos.CopiarParaEditando(IDGateway);
                ctrlSDs = ctrlSDMetodos.CopiarParaEditando(IDGateway);
            }
            else
            {
                // configuração em 'editando'
                ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
                ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            }

            // copia status
            ViewBag.solicitaCtrl = solicitaCtrl;
            ViewBag.solicitaCtrlSD = solicitaCtrlSD;

            // copia configuração
            ViewBag.CtrlDemAcum = ctrls;
            ViewBag.CtrlDemAcum_SD = ctrlSDs;

            // retorna configuração
            return (ctrls);
        }

        // GET: Configuracao Controle Demanda Acumulada - Editar
        public ActionResult GateX_CtrlDemAcum_Editar(int IDGateway, int NumCtrlGateway)
        {
            // tela de ajuda - controle demanda acumulada
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            GateX_CtrlDemAcum_PreparaListas(IDGateway);

            // controle demanda acumulada (editando)
            GateX_CtrlDemAcum_Metodos ctrlMetodos = new GateX_CtrlDemAcum_Metodos();
            GateX_CtrlDemAcum_Dominio ctrl = ctrlMetodos.ListarPorNumCtrl(IDGateway, NumCtrlGateway, STATUS_CFG.EDITANDO);

            // controle demanda acumulada (saídas) (editando)
            GateX_CtrlDemAcum_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemAcum_SD_Metodos();
            List<GateX_CtrlDemAcum_SD_Dominio> ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // percorre saidas
            foreach (GateX_CtrlDemAcum_SD_Dominio sd in ctrlSDs)
            {
                // divide nível de desligamento por 10 (920 = 92.0)
                sd.ControlAcumDeslig /= 10.0;
            }

            ViewBag.CtrlDemAcum_SD = ctrlSDs;

            // medições
            GateX_CtrlDemAcum_ObterMedicoes(IDGateway);

            // saidas
            GateX_CtrlDemAcum_ObterSaidas(IDGateway);

            return View(ctrl);
        }

        private void GateX_CtrlDemAcum_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // medições energia
            GateX_MedEner_Lista_Metodos medEnerMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medEner_Lista = medEnerMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medEner_Lista = medEner_Lista;

            // medições
            List<ListaTiposDominio> listaMedicoes = new List<ListaTiposDominio>();
            ViewBag.listaMedicoes = listaMedicoes;

            // saidas digitais
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.SaidasDigitais = saidasDigitais;

            // saidas
            List<ListaTiposDominio> listaSaidas = new List<ListaTiposDominio>();
            ViewBag.listaSaidas = listaSaidas;

            return;
        }

        // POST: Configuracao Controle Demanda Acumulada - Programar
        [HttpPost]
        public ActionResult GateX_CtrlDemAcum_Programar(GateX_CtrlDemAcum_Dominio CtrlDemAcum, List<GateX_CtrlDemAcum_SD_Dominio> ctrlSDs)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // salva controle demanda acumulada
            GateX_CtrlDemAcum_Metodos ctrlMetodos = new GateX_CtrlDemAcum_Metodos();
            ctrlMetodos.Salvar(CtrlDemAcum, STATUS_CFG.EDITANDO);

            // excluir todas saidas do controle
            GateX_CtrlDemAcum_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemAcum_SD_Metodos();
            ctrlSDMetodos.ExcluirPorNumCtrl(CtrlDemAcum.IDGateway, CtrlDemAcum.NumCtrlGateway, STATUS_CFG.EDITANDO);

            // salva controle demanda acumulada (saídas)
            foreach (GateX_CtrlDemAcum_SD_Dominio sd in ctrlSDs)
            {
                // verifica se saída é deste controle
                if (sd.NumCtrlGateway == CtrlDemAcum.NumCtrlGateway)
                {
                    // multiplica nível de desligamento por 10 (920 = 92.0)
                    sd.ControlAcumDeslig *= 10.0;

                    ctrlSDMetodos.Salvar(sd, STATUS_CFG.EDITANDO);
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuracao Controle Demanda Acumulada - Excluir
        public ActionResult GateX_CtrlDemAcum_Excluir(int IDGateway, int NumCtrlGateway)
        {
            // lista gateway para inserir evento                        
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // apaga programacao
            GateX_CtrlDemAcum_Metodos ctrlMetodos = new GateX_CtrlDemAcum_Metodos();
            ctrlMetodos.ExcluirPorNumCtrl(IDGateway, NumCtrlGateway, STATUS_CFG.EDITANDO);

            GateX_CtrlDemAcum_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemAcum_SD_Metodos();
            ctrlSDMetodos.ExcluirPorNumCtrl(IDGateway, NumCtrlGateway, STATUS_CFG.EDITANDO);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // POST: Configuracao Controle Demanda Acumulada - Enviar
        [HttpPost]
        public ActionResult GateX_CtrlDemAcum_Enviar(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // copia configuração 'editando' para 'atual'
            GateX_CtrlDemAcum_Metodos ctrlMetodos = new GateX_CtrlDemAcum_Metodos();
            List<GateX_CtrlDemAcum_Dominio> ctrls = ctrlMetodos.CopiarParaAtual(IDGateway);

            GateX_CtrlDemAcum_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemAcum_SD_Metodos();
            List<GateX_CtrlDemAcum_SD_Dominio> ctrlSDs = ctrlSDMetodos.CopiarParaAtual(IDGateway);

            // variavel de retorno
            bool enviaCtrlDemAcum = false;
            bool enviaCtrlDemAcum_SD = false;

            // verifica
            if (ctrls != null && ctrlSDs != null && gateway != null)
            {
                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // copia para lista de envio
                    smartCom.gateX.CtrlDemAcum = ctrls;

                    // envia controle demanda acumulada
                    enviaCtrlDemAcum = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_DEM_ACUM, SMCOM_TIPO_SOL.ENVIA);

                    // copia para lista de envio
                    smartCom.gateX.CtrlDemAcum_SD = ctrlSDs;

                    // envia controle demanda acumulada (saídas)
                    enviaCtrlDemAcum_SD = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_DEM_ACUM_SD, SMCOM_TIPO_SOL.ENVIA);
                }
            }

            // verifica retorno
            if (enviaCtrlDemAcum && enviaCtrlDemAcum_SD)
            {
                // excluir editando
                ctrlMetodos.ExcluirEditando(IDGateway);
                ctrlSDMetodos.ExcluirEditando(IDGateway);

                // histórico de configuração
                HistoricoConfiguracoesDominio hist = new HistoricoConfiguracoesDominio();
                hist.IDGateway = IDGateway;
                hist.NomeConfiguracao = "GateX_CtrlDemAcum";
                hist.Envio = "MQTT";
                hist.Atualizacao = DateTime.Now;
                hist.Versao = GateX_Versao_String(IDGateway);
                hist.IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");

                // insere histórico de configuração
                HistoricoConfiguracoesMetodos histMetodos = new HistoricoConfiguracoesMetodos();
                histMetodos.Inserir(hist);

                // controle demanda acumulada (saídas)
                hist.NomeConfiguracao = "GateX_CtrlDemAcum_SD";
                histMetodos.Inserir(hist);

                // caso OK insiro evento
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, IDGateway);
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Falha no envio da configuração!"
                };
            }

            // retorna status
            return Json(returnedData);
        }




        //
        // MEDICOES
        //

        // GET: Obter Medicoes
        public JsonResult GateX_CtrlDemAcum_ObterMedicoes(int IDGateway)
        {
            // medições
            List<ListaTiposDominio> listaMedicoes = new List<ListaTiposDominio>();

            // medições energia
            GateX_MedEner_Lista_Metodos medEnerMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medEner_Lista = medEnerMetodos.ListarPorIDGateway(IDGateway);

            // nenhum
            ListaTiposDominio tipo_nenhum = new ListaTiposDominio();
            tipo_nenhum.ID = 255;
            tipo_nenhum.Descricao = "Nenhum";
            listaMedicoes.Add(tipo_nenhum);

            // preenche lista de medições
            for (int i = 0; i < SGATEX.NUM_MED_EN; i++)
            {
                // default
                ListaTiposDominio tipo = new ListaTiposDominio();
                tipo.ID = i;
                tipo.Descricao = string.Format("[{0:00}] Medição de Energia {1}", i, i);

                // copia
                if (medEner_Lista != null)
                {
                    // verifica se tem na lista
                    GateX_MedEner_Lista_Dominio med = medEner_Lista.First(item => item.NumMedicaoGateway == i);

                    if (med != null)
                    {
                        tipo.Descricao = string.Format("[{0:00}] {1}", i, med.Descricao);
                    }
                }

                // insere na lista
                listaMedicoes.Add(tipo);
            }

            // lista de medições
            ViewBag.listaMedicoes = listaMedicoes;

            // retorna o valor em JSON
            return Json(listaMedicoes, JsonRequestBehavior.AllowGet);
        }



        //
        // SAIDAS
        //

        // GET: Obter Saidas
        public JsonResult GateX_CtrlDemAcum_ObterSaidas(int IDGateway)
        {
            // saidas
            List<ListaTiposDominio> listaSaidas = new List<ListaTiposDominio>();

            // le saidas
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);

            // preenche lista de saídas
            for (int i = 0; i < SGATEX.NUM_SD; i++)
            {
                // default
                ListaTiposDominio tipo = new ListaTiposDominio();
                tipo.ID = i;
                tipo.Descricao = string.Format("[{0:00}] Saída Digital {1}", i, i);

                // copia
                if (saidasDigitais != null)
                {
                    // verifica se tem na lista
                    GateX_SD_Lista_Dominio med = saidasDigitais.First(item => item.NumSaidaGateway == i);

                    if (med != null)
                    {
                        tipo.Descricao = string.Format("[{0:00}] {1}", i, med.Descricao);
                    }
                }

                // insere na lista
                listaSaidas.Add(tipo);
            }

            // lista de saídas
            ViewBag.listaSaidas = listaSaidas;

            // retorna o valor em JSON
            return Json(listaSaidas, JsonRequestBehavior.AllowGet);
        }

        // recebe configuração (comando receber configurações)
        public bool GateX_CtrlDemAcum_ComandoRecebe(GatewaysDominio gateway, string versao)
        {
            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita programação
                if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_DEM_ACUM, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // solicita configuração dos controles demanda acumulada (saídas)
                    if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_DEM_ACUM_SD, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // salvar
                        GateX_CtrlDemAcum_Metodos ctrlMetodos = new GateX_CtrlDemAcum_Metodos();
                        ctrlMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.CtrlDemAcum, STATUS_CFG.ATUAL);

                        // salvar (controle demanda acumulada (saídas))
                        GateX_CtrlDemAcum_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemAcum_SD_Metodos();
                        ctrlSDMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.CtrlDemAcum_SD, STATUS_CFG.ATUAL);

                        // ok
                        return (true);
                    }
                }
            }

            // erro
            return (false);
        }
    }
}