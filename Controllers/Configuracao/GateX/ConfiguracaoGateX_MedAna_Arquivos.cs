﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Medições Analógicas XLS Download
        [HttpGet]
        public virtual ActionResult GateX_MedAna_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Medições Analógicas XLS
        public ActionResult GateX_MedAna_XLS(int IDGateway)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_GateX_MedAna(IDGateway);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_MedAna_Gateway_{0:000000}_{1:yyyyMMddHHmm}.xls", IDGateway, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // Medições Analógicas Planilha
        private HSSFWorkbook XLS_GateX_MedAna(int IDGateway)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // MEDIÇÕES ANALÓGICAS
            //

            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoMedAna;

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            //  le lista de medições da gateway
            GateX_MedAna_Metodos prgMetodos = new GateX_MedAna_Metodos();
            List<GateX_MedAna_Dominio> medAna = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // le lista tipo variável
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoVariavel = listatiposMetodos.ListarTodos("TipoGateX_Variavel_AN", false, 0);

            // le tipos rede IO
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);

            // le lista tipo variável formato
            List<ListaTiposDominio> listatipoVariavelFormato = listatiposMetodos.ListarTodos("TipoGateX_Variavel_Formato", false, 0);

            // le lista tipo escala
            List<ListaTiposDominio> listatipoEscala = listatiposMetodos.ListarTodos("TipoGateX_Escala", false, 0);

            // lista medições
            GateX_MedEner_Metodos medMetodos = new GateX_MedEner_Metodos();
            List<GateX_MedEner_Dominio> listaMedEner = medMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.ATUAL);

            // le lista tipo variável grandezas elétricas
            List<ListaTiposDominio> listatipoVariavelGrandEletrica = listatiposMetodos.ListarTodos("TipoGateX_Variavel_GrandEletrica", false, 0);


            // cria planilha
            var sheet = workbook.CreateSheet("Medições Analógicas");

            // cabecalho
            string[] cabecalho = { "Medição Interna", "Medições Analógicas", "Unidade", "Variável", "Média Móvel (seg.)", "Rede", "Remota", "Endereço", "Formato", "Zerar em (seg.)", "Remota não OK", "Entrada Analógica Mínima", "Entrada Analógica Máxima", "Unidade de Engenharia Mínima", "Unidade de Engenharia Máxima", "Constante Multiplicador", "Constante de Soma", "Medição de Energia", "Variável", "Valor Mínimo", "Valor Máximo" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            IRow row;

            if (medAna != null)
            {
                // percorre lógicas
                foreach (GateX_MedAna_Dominio med in medAna)
                {
                    string desprogramado = "---";
                    string Descricao = desprogramado;
                    string Unidade = desprogramado;
                    string TipoVariavel = desprogramado;
                    string TempoAmostraMediaMovel = desprogramado;

                    string RedeIO = desprogramado;
                    string Remota = desprogramado;
                    string RemotaEnd = desprogramado;
                    string RemotaVar = desprogramado;
                    string TempoZerarRaw = desprogramado;

                    string v1_EntAnalogicaMin = desprogramado;
                    string v1_EntAnalogicaMax = desprogramado;
                    string v1_UnidadeEngMin = desprogramado;
                    string v1_UnidadeEngMax = desprogramado;
                    string v1_UpScale = desprogramado;

                    string v2_ConstanteMult = desprogramado;
                    string v2_ConstanteSoma = desprogramado;

                    string v4_MedicaoEE = desprogramado;
                    string v4_Offset = desprogramado;
                    string v4_UnidadeEngMin = desprogramado;
                    string v4_UnidadeEngMax = desprogramado;

                    if (med.Programado)
                    {
                        // Descrição e Unidade
                        Descricao = med.Descricao;
                        Unidade = med.Unidade;

                        // Média Móvel
                        TempoAmostraMediaMovel = string.Format("{0}", med.TempoAmostraMediaMovel * 10);

                        // Tipo Variável
                        ListaTiposDominio tipo = listatipoVariavel.Find(item => item.ID == med.TipoVariavel);
                        TipoVariavel = (tipo != null) ? tipo.Descricao : desprogramado;

                        switch (med.TipoVariavel)
                        {
                            case SGATEX_TIPO_VARIAVEL.REM_EA:

                                // RedeIO / Remota / Endereço / Formato
                                tipo = listatipoRedeIO.Find(item => item.ID == med.v1_RedeIO);
                                RedeIO = (tipo != null) ? tipo.Descricao : desprogramado;
                                Remota = med.v1_Remota.ToString();
                                RemotaEnd = med.v1_RemotaEnd.ToString();
                                tipo = listatipoVariavelFormato.Find(item => item.ID == med.v1_RemotaVar);
                                RemotaVar = (tipo != null) ? tipo.Descricao : desprogramado;

                                // Zerar em
                                TempoZerarRaw = string.Format("{0}", med.v1_TempoZerarRaw * 10);

                                // Remota não OK
                                tipo = listatipoEscala.Find(item => item.ID == med.v1_UpScale);
                                v1_UpScale = (tipo != null) ? tipo.Descricao : desprogramado;

                                // Entrada Analógica Min/Max
                                v1_EntAnalogicaMin = string.Format("{0}", Math.Round(med.v1_EntAnalogicaMin, 5));
                                v1_EntAnalogicaMax = string.Format("{0}", Math.Round(med.v1_EntAnalogicaMax, 5));

                                // Unidade de Engenharia Min/Max
                                v1_UnidadeEngMin = string.Format("{0}", Math.Round(med.v1_UnidadeEngMin, 5));
                                v1_UnidadeEngMax = string.Format("{0}", Math.Round(med.v1_UnidadeEngMax, 5));

                                break;

                            case SGATEX_TIPO_VARIAVEL.REM_PULSO:

                                // RedeIO / Remota / Endereço / Formato
                                tipo = listatipoRedeIO.Find(item => item.ID == med.v2_RedeIO);
                                RedeIO = (tipo != null) ? tipo.Descricao : desprogramado;
                                Remota = med.v2_Remota.ToString();
                                RemotaEnd = med.v2_RemotaEnd.ToString();
                                tipo = listatipoVariavelFormato.Find(item => item.ID == med.v2_RemotaVar);
                                RemotaVar = (tipo != null) ? tipo.Descricao : desprogramado;

                                // Zerar em
                                TempoZerarRaw = string.Format("{0}", med.v2_TempoZerarRaw * 10);

                                // Constante Multiplicador/Soma
                                v2_ConstanteMult = string.Format("{0}", Math.Round(med.v2_ConstanteMult, 5));
                                v2_ConstanteSoma = string.Format("{0}", Math.Round(med.v2_ConstanteSoma, 5));

                                break;

                            case SGATEX_TIPO_VARIAVEL.GRAND_ELETRICA:

                                // Medição de Energia
                                GateX_MedEner_Dominio medEner = listaMedEner.Find(item => item.NumMedicaoGateway == med.v4_MedicaoEE);
                                v4_MedicaoEE = (medEner != null) ? medEner.DescricaoNum : desprogramado;

                                // Variável
                                tipo = listatipoVariavelGrandEletrica.Find(item => item.ID == med.v4_Offset);
                                v4_Offset = (tipo != null) ? tipo.Descricao : desprogramado;

                                // Valor Min/Max
                                v4_UnidadeEngMin = string.Format("{0}", Math.Round(med.v4_UnidadeEngMin, 5));
                                v4_UnidadeEngMax = string.Format("{0}", Math.Round(med.v4_UnidadeEngMax, 5));

                                break;
                        }
                    }

                    // adiciona linha
                    row = sheet.CreateRow(rowIndex++);

                    // Medição
                    numeroCelulaXLS(row, 0, med.NumMedicaoGateway, _intCellStyle);
                    sheet.SetColumnWidth(0, 6000);

                    // Descrição
                    textoCelulaXLS(row, 1, Descricao);
                    sheet.SetColumnWidth(1, 7000);

                    // Unidade
                    textoCelulaXLS(row, 2, Unidade);
                    sheet.SetColumnWidth(2, 5000);

                    // Variável
                    textoCelulaXLS(row, 3, TipoVariavel);
                    sheet.SetColumnWidth(3, 7000);

                    // Média Móvel
                    textoCelulaXLS(row, 4, TempoAmostraMediaMovel);
                    sheet.SetColumnWidth(4, 6000);

                    // rede IO
                    textoCelulaXLS(row, 5, RedeIO);
                    sheet.SetColumnWidth(5, 4000);

                    // remota
                    textoCelulaXLS(row, 6, Remota);
                    sheet.SetColumnWidth(6, 4000);

                    // endereco
                    textoCelulaXLS(row, 7, RemotaEnd);
                    sheet.SetColumnWidth(7, 4000);

                    // formato
                    textoCelulaXLS(row, 8, RemotaVar);
                    sheet.SetColumnWidth(8, 4000);

                    // Zerar em
                    textoCelulaXLS(row, 9, TempoZerarRaw);
                    sheet.SetColumnWidth(9, 4000);

                    // Remota não OK
                    textoCelulaXLS(row, 10, v1_UpScale);
                    sheet.SetColumnWidth(10, 4500);

                    // Entrada Analógica Min/Max
                    textoCelulaXLS(row, 11, v1_EntAnalogicaMin);
                    sheet.SetColumnWidth(11, 8000);

                    textoCelulaXLS(row, 12, v1_EntAnalogicaMax);
                    sheet.SetColumnWidth(12, 8000);

                    // Unidade de Engenharia Min/Max
                    textoCelulaXLS(row, 13, v1_UnidadeEngMin);
                    sheet.SetColumnWidth(13, 8000);

                    textoCelulaXLS(row, 14, v1_UnidadeEngMax);
                    sheet.SetColumnWidth(14, 8000);

                    // Constante Multiplicador/Soma
                    textoCelulaXLS(row, 15, v2_ConstanteMult);
                    sheet.SetColumnWidth(15, 6500);

                    textoCelulaXLS(row, 16, v2_ConstanteSoma);
                    sheet.SetColumnWidth(16, 6500);

                    // Medição de Energia
                    textoCelulaXLS(row, 17, v4_MedicaoEE);
                    sheet.SetColumnWidth(17, 8000);

                    // Variável
                    textoCelulaXLS(row, 18, v4_Offset);
                    sheet.SetColumnWidth(18, 4000);

                    // Valor Min/Max
                    textoCelulaXLS(row, 19, v4_UnidadeEngMin);
                    sheet.SetColumnWidth(19, 4000);

                    textoCelulaXLS(row, 20, v4_UnidadeEngMax);
                    sheet.SetColumnWidth(20, 4000);
                }
            }

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS_GateX(workbook, sheet, _negritoCellStyle);

            // largura de cada coluna
            sheet.SetColumnWidth(0, 7000);
            sheet.SetColumnWidth(1, 8000);

            // retorna planilha
            return workbook;
        }


        // GET: Medições Analógicas PDF
        public ActionResult GateX_MedAna_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista Medições Analógicas
            GateX_MedAna_Metodos prgMetodos = new GateX_MedAna_Metodos();
            List<GateX_MedAna_Dominio> medAna = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.medAna = medAna;

            // le lista tipo variável
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoVariavel = listatiposMetodos.ListarTodos("TipoGateX_Variavel_AN", false, 0);
            ViewBag.listatipoVariavel = listatipoVariavel;

            // le tipos rede IO
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DO);      // remove DO
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.SEG);     // remove seg
            ViewBag.listatipoRedeIO = listatipoRedeIO;


            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_MedAna_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_MedAna_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        // GET: Medições Analógicas EMAIL
        public async Task<ActionResult> GateX_MedAna_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);


            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoMedAna;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista Medições Analógicas
            GateX_MedAna_Metodos prgMetodos = new GateX_MedAna_Metodos();
            List<GateX_MedAna_Dominio> medAna = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.medAna = medAna;

            // le lista tipo variável
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoVariavel = listatiposMetodos.ListarTodos("TipoGateX_Variavel_AN", false, 0);
            ViewBag.listatipoVariavel = listatipoVariavel;

            // le tipos rede IO
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DO);      // remove DO
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.SEG);     // remove seg
            ViewBag.listatipoRedeIO = listatipoRedeIO;


            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_MedAna_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_MedAna_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "GateX_MedAnaEmail";
            var message = await EMailTemplateAsync(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.NomeGateway", ViewBag.NomeGateway);
            message = message.Replace("ViewBag.DescrConfig", ViewBag.DescrConfig);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Medições Analógicas Print
        public ActionResult GateX_MedAna_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista Medições Analógicas
            GateX_MedAna_Metodos prgMetodos = new GateX_MedAna_Metodos();
            List<GateX_MedAna_Dominio> medAna = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.medAna = medAna;

            // le lista tipo variável
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoVariavel = listatiposMetodos.ListarTodos("TipoGateX_Variavel_AN", false, 0);
            ViewBag.listatipoVariavel = listatipoVariavel;

            // le tipos rede IO
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DO);      // remove DO
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.SEG);     // remove seg
            ViewBag.listatipoRedeIO = listatipoRedeIO;


            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // imprime
            return View();
        }
    }
}