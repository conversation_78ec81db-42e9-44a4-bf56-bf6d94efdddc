﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Configuracao Medições Ciclometro
        public ActionResult GateX_MedCiclo(int IDGateway, int Origem = 0)
        {
            // tela de ajuda - medição Ciclometro
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // receber configuração
            List<GateX_MedCiclo_Dominio> medCiclo = GateX_MedCiclo_Receber(IDGateway, Origem);

            // prepara listas
            GateX_MedCiclo_PreparaListas(IDGateway);

            // retorna configuração
            return View(medCiclo);
        }

        // Configuração Medições Ciclometro - Receber
        private List<GateX_MedCiclo_Dominio> GateX_MedCiclo_Receber(int IDGateway, int Origem)
        {
            // medições iclometro
            GateX_MedCiclo_Metodos medMetodos = new GateX_MedCiclo_Metodos();
            List<GateX_MedCiclo_Dominio> medCiclo = new List<GateX_MedCiclo_Dominio>();

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // status
            bool solicitaProg = true;

            // verifica Origem
            // 0: ler da gateway
            // 1: ler do banco de dados atual
            // 2: ler do banco de dados editando
            if (Origem == 0)
            {
                // versão (salva versão)
                GateX_Versao_String(IDGateway);

                // status
                solicitaProg = false;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // solicita programação de medicoes Ciclometro
                    if (solicitaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_MED_CY, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // copia configuração da gateway para 'editando'
                        // caso ainda não exista configuração 'atual', será criada 
                        medCiclo = medMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.MedCiclo);
                    }
                }

                // verifica se solicitou
                if (solicitaProg)
                {
                    // atualiza lista de saidas digitais
                    GateX_AtualizaLista(gateway, SMCOM_FUNCOES_SOL.PROGR_SD_LISTA);
                }
                else
                {
                    // copia configuração 'atual' para 'editando'
                    medCiclo = medMetodos.CopiarParaEditando(IDGateway);
                }
            }
            else if (Origem == 1)
            {
                // copia configuração 'atual' para 'editando'
                medCiclo = medMetodos.CopiarParaEditando(IDGateway);
            }
            else
            {
                // configuração em 'editando'
                medCiclo = medMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            }

            // copia status
            ViewBag.SolicitaProg = solicitaProg;

            // retorna configuração
            return (medCiclo);
        }

        // GET: Configuração Medições Ciclometro - Editar
        public ActionResult GateX_MedCiclo_Editar(int IDGateway, int NumMedicaoGateway)
        {
            // tela de ajuda - medição ciclometro
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            GateX_MedCiclo_PreparaListas(IDGateway);

            // lê medições ciclometro (editando)
            GateX_MedCiclo_Metodos medMetodos = new GateX_MedCiclo_Metodos();
            GateX_MedCiclo_Dominio programacao = medMetodos.ListarPorNumMedicao(IDGateway, NumMedicaoGateway, STATUS_CFG.EDITANDO);

            // temporizadores
            programacao.TempoAmostraFaltaPulso = (programacao.TempoAmostraFaltaPulso * 10);
            programacao.v2_TempoZerarRaw = (programacao.v2_TempoZerarRaw * 10);

            // arredonda
            programacao.v2_ConstanteMult = Math.Round(programacao.v2_ConstanteMult, 5);
            programacao.v2_ConstanteSoma = Math.Round(programacao.v2_ConstanteSoma, 5);

            return View(programacao);
        }

        // prepara listas
        private void GateX_MedCiclo_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // le tipos rede IO
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DO);      // remove DO
            ViewBag.listaTipoRedeIO = listatipoRedeIO;

            // le tipos numeros remota Rede K
            List<ListaTiposDominio> listatipoNumRemotaRedeK = listatiposMetodos.ListarTodos("TipoGateX_NumRemotaRedeK", false, 0);
            ViewBag.listaTipoNumRemotaRedeK = listatipoNumRemotaRedeK;

            // lista numero remotas
            int[] listaRemotas = new int[SGATEX.NUM_REMOTAS];

            for (int i = 0; i < listaRemotas.Length; i++)
            {
                listaRemotas[i] = i;
            }

            // copia lista remotas
            ViewBag.ListaRemotas = listaRemotas;

            // le lista tipo variável
            List<ListaTiposDominio> listatipoVariavel = listatiposMetodos.ListarTodos("TipoGateX_Variavel_CY", false, 0);
            ViewBag.listatipoVariavel = listatipoVariavel;

            // le lista tipo variável formato
            List<ListaTiposDominio> listatipoVariavelFormato = listatiposMetodos.ListarTodos("TipoGateX_Variavel_Formato", false, 0);
            ViewBag.listatipoVariavelFormato = listatipoVariavelFormato;

            return;
        }

        // POST: Configuração Medições Ciclometro - Programar
        [HttpPost]
        public ActionResult GateX_MedCiclo_Programar(GateX_MedCiclo_Dominio medCiclo)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // tipo do medidor
            if (medCiclo.TipoVariavel == SGATEX_TIPO_VARIAVEL.DESPROGRAMADO)
            {
                // desprogramado
                medCiclo.Programado = false;
                medCiclo.Programacao = SGATEX_TIPO_GG.DESPROGRAMADO;

                medCiclo.Descricao = string.Format("Medição Ciclômetro {0}", medCiclo.NumMedicaoGateway);
                medCiclo.Unidade = "";

                medCiclo.TipoVariavel = SGATEX_TIPO_VARIAVEL.DESPROGRAMADO;
                medCiclo.RecebeHist = 0;
                medCiclo.TempoAmostraFaltaPulso = 0;
                medCiclo.TempoAmostraMediaMovel = 0;
                medCiclo.ZeraAcumuladoSd = 255;
                medCiclo.NaoAcumularSd = 255;

                medCiclo.v2_RedeIO = 0;
                medCiclo.v2_Remota = 0;
                medCiclo.v2_RemotaEnd = 0;
                medCiclo.v2_RemotaVar = 0;
                medCiclo.v2_TempoZerarRaw = 0;
                medCiclo.v2_ConstanteMult = 0.0;
                medCiclo.v2_ConstanteSoma = 0.0;
            }
            else
            {
                // programado
                medCiclo.Programacao = SGATEX_TIPO_GG.GG_CY;

                // temporizadores
                medCiclo.TempoAmostraFaltaPulso = (medCiclo.TempoAmostraFaltaPulso / 10);
                medCiclo.v2_TempoZerarRaw = (medCiclo.v2_TempoZerarRaw / 10);
            }

            // salva (editando)
            GateX_MedCiclo_Metodos medMetodos = new GateX_MedCiclo_Metodos();
            medMetodos.Salvar(medCiclo, STATUS_CFG.EDITANDO);

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Medições Ciclometro - Excluir
        public ActionResult GateX_MedCiclo_Excluir(int IDGateway, int NumMedicaoGateway)
        {
            // apaga programacao (editando)
            GateX_MedCiclo_Metodos medMetodos = new GateX_MedCiclo_Metodos();
            medMetodos.ExcluirPorNumMedicao(IDGateway, NumMedicaoGateway, STATUS_CFG.EDITANDO);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // POST: Configuração Medições Ciclometro - Enviar
        [HttpPost]
        public ActionResult GateX_MedCiclo_Enviar(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };


            try
            {
                // le gateway
                GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
                GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

                // medições ciclometro - copia configuração 'editando' para 'atual'
                GateX_MedCiclo_Metodos medMetodos = new GateX_MedCiclo_Metodos();
                List<GateX_MedCiclo_Dominio> medCiclo = medMetodos.CopiarParaAtual(IDGateway);

                // variavel de retorno
                bool enviaProg = false;

                // verifica
                if (medCiclo != null && gateway != null)
                {
                    // comunicação MQTT
                    SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                    // verifica conexão com gateway
                    if (smartCom.Conectar())
                    {
                        // copia para lista de envio
                        smartCom.gateX.MedCiclo = medCiclo;

                        // envia medição ciclometro
                        enviaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_MED_CY, SMCOM_TIPO_SOL.ENVIA);
                    }
                }

                // verifica retorno
                if (enviaProg)
                {
                    // excluir editando
                    medMetodos.ExcluirEditando(IDGateway);

                    // histórico de configuração (medições)
                    HistoricoConfiguracoesDominio hist = new HistoricoConfiguracoesDominio();
                    hist.IDGateway = IDGateway;
                    hist.NomeConfiguracao = "GateX_MedCiclo";
                    hist.Envio = "MQTT";
                    hist.Atualizacao = DateTime.Now;
                    hist.Versao = GateX_Versao_String(IDGateway);
                    hist.IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");

                    // insere histórico de configuração
                    HistoricoConfiguracoesMetodos histMetodos = new HistoricoConfiguracoesMetodos();
                    histMetodos.Inserir(hist);

                    // caso OK insiro evento
                    UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, IDGateway);
                }
                else
                {
                    // erro
                    returnedData = new
                    {
                        status = "ERRO",
                        erro = "Falha no envio da configuração!"
                    };
                }
            }
            catch
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Verificar as configurações"
                };
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Medições Ciclometro - Copiar
        public ActionResult GateX_MedCiclo_Copiar(int IDGateway, int Origem, int Inicio, int Fim)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // lista programacoes de medições da gateway salvas no banco de dados
            GateX_MedCiclo_Metodos medMetodos = new GateX_MedCiclo_Metodos();
            List<GateX_MedCiclo_Dominio> medCiclo = medMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.medCiclo = medCiclo;

            // lista medição origem
            GateX_MedCiclo_Dominio medicaoOrigem = medMetodos.ListarPorNumMedicao(IDGateway, Origem, STATUS_CFG.EDITANDO);

            if (medicaoOrigem.NumMedicaoGateway >= Inicio && medicaoOrigem.NumMedicaoGateway <= Fim)
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "A medição origem não pode estar entre as medições destino."
                };

                // retorna erro
                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // auxiliar
            int copia = 1;

            if (Fim < Inicio)
            {
                // copia para medições desejadas decrescente
                for (int i = Inicio; i >= Fim; i--)
                {
                    GateX_MedCiclo_Dominio medicaoCopia = medMetodos.ListarPorNumMedicao(IDGateway, i, STATUS_CFG.EDITANDO);

                    // copia
                    GateX_MedCiclo_CopiaConfiguracao(medicaoOrigem, ref medicaoCopia, copia);

                    // salva medição copiada
                    medMetodos.Salvar(medicaoCopia, STATUS_CFG.EDITANDO);

                    // incrementa cópia
                    copia++;
                }
            }

            else
            {
                // copia para medições desejadas crescente
                for (int i = Inicio; i <= Fim; i++)
                {
                    GateX_MedCiclo_Dominio medicaoCopia = medMetodos.ListarPorNumMedicao(IDGateway, i, STATUS_CFG.EDITANDO);

                    // copia
                    GateX_MedCiclo_CopiaConfiguracao(medicaoOrigem, ref medicaoCopia, copia);

                    // salva medição copiada
                    medMetodos.Salvar(medicaoCopia, STATUS_CFG.EDITANDO);

                    // incrementa cópia
                    copia++;
                }
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // copia configuração
        public void GateX_MedCiclo_CopiaConfiguracao(GateX_MedCiclo_Dominio medicaoOrigem, ref GateX_MedCiclo_Dominio medicaoCopia, int copia)
        {
            medicaoCopia.IDGateway = medicaoOrigem.IDGateway;

            medicaoCopia.Descricao = medicaoOrigem.Descricao;
            medicaoCopia.Unidade = medicaoOrigem.Unidade;

            // verifica tamanho se cabe cópia (limite - 3 caracteres)
            if (medicaoCopia.Descricao.Length <= (29 - 3))
            {
                // coloca indice
                medicaoCopia.Descricao += string.Format(".{0}", copia);
            }

            medicaoCopia.Programacao = medicaoOrigem.Programacao;
            medicaoCopia.TipoVariavel = medicaoOrigem.TipoVariavel;
            medicaoCopia.RecebeHist = medicaoOrigem.RecebeHist;
            medicaoCopia.TempoAmostraFaltaPulso = medicaoOrigem.TempoAmostraFaltaPulso;
            medicaoCopia.TempoAmostraMediaMovel = medicaoOrigem.TempoAmostraMediaMovel;
            medicaoCopia.ZeraAcumuladoSd = medicaoOrigem.ZeraAcumuladoSd;
            medicaoCopia.NaoAcumularSd = medicaoOrigem.NaoAcumularSd;

            medicaoCopia.v2_RedeIO = medicaoOrigem.v2_RedeIO;
            medicaoCopia.v2_Remota = medicaoOrigem.v2_Remota;
            medicaoCopia.v2_RemotaEnd = medicaoOrigem.v2_RemotaEnd;
            medicaoCopia.v2_RemotaVar = medicaoOrigem.v2_RemotaVar;
            medicaoCopia.v2_TempoZerarRaw = medicaoOrigem.v2_TempoZerarRaw;
            medicaoCopia.v2_ConstanteMult = medicaoOrigem.v2_ConstanteMult;
            medicaoCopia.v2_ConstanteSoma = medicaoOrigem.v2_ConstanteSoma;

            return;
        }

        // recebe configuração (comando receber configurações)
        public bool GateX_MedCiclo_ComandoRecebe(GatewaysDominio gateway, string versao)
        {
            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita programação
                if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_MED_CY, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // salvar
                    GateX_MedCiclo_Metodos medMetodos = new GateX_MedCiclo_Metodos();
                    medMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.MedCiclo, STATUS_CFG.ATUAL);

                    // ok
                    return (true);
                }
            }

            // erro
            return (false);
        }
    }
}