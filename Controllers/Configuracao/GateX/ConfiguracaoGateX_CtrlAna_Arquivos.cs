﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Controle Analógico XLS Download
        [HttpGet]
        public virtual ActionResult GateX_CtrlAna_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Controle Analógico XLS
        public ActionResult GateX_CtrlAna_XLS(int IDGateway)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_GateX_CtrlAna(IDGateway);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_ControleAnalogico_Gateway_{0:000000}_{1:yyyyMMddHHmm}.xls", IDGateway, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // Controle Analógico Planilha
        private HSSFWorkbook XLS_GateX_CtrlAna(int IDGateway)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.DescriConfigCtrlAna;

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // le lista controles analógicos
            GateX_CtrlAna_Metodos ctrlMetodos = new GateX_CtrlAna_Metodos();
            List<GateX_CtrlAna_Dominio> ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // controle analógico (saídas)
            GateX_CtrlAna_SD_Metodos ctrlSDMetodos = new GateX_CtrlAna_SD_Metodos();
            List<GateX_CtrlAna_SD_Dominio> ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // medições analógicas
            GateX_MedAna_Lista_Metodos medAnaMetodos = new GateX_MedAna_Lista_Metodos();
            List<GateX_MedAna_Lista_Dominio> medAna_Lista = medAnaMetodos.ListarPorIDGateway(IDGateway);

            // medições utilidades
            GateX_MedUtil_Lista_Metodos medUtilMetodos = new GateX_MedUtil_Lista_Metodos();
            List<GateX_MedUtil_Lista_Dominio> medUtil_Lista = medUtilMetodos.ListarPorIDGateway(IDGateway);

            // medições ciclômetro
            GateX_MedCiclo_Lista_Metodos medCicloMetodos = new GateX_MedCiclo_Lista_Metodos();
            List<GateX_MedCiclo_Lista_Dominio> medCiclo_Lista = medCicloMetodos.ListarPorIDGateway(IDGateway);

            //  le lista de saidas da gateway
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidas = sdMetodos.ListarPorIDGateway(IDGateway);

            // cria planilha
            var sheet = workbook.CreateSheet("Controle Analógico");

            // cabecalho
            string[] cabecalho = { "Controle", "Variável", "Medição", "Saída", "Nível de Ligamento", "Nível de Desligamento", "Na Falha da Medição", "Tempo para Falha de Medição (seg)", "Tempo entre Controle (seg)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;


            if (ctrls != null && ctrlSDs != null)
            {
                // percorre controles
                foreach (GateX_CtrlAna_Dominio ctrl in ctrls)
                {
                    if (ctrl.Programado)
                    {
                        // percorre saídas
                        foreach (GateX_CtrlAna_SD_Dominio sd in ctrlSDs)
                        {
                            if (sd.Programado && sd.NumCtrlGateway == ctrl.NumCtrlGateway)
                            {
                                // adiciona linha
                                row = sheet.CreateRow(rowIndex++);

                                // Número controle analógico
                                numeroCelulaXLS(row, 0, ctrl.NumCtrlGateway, _intCellStyle);

                                // Variável
                                string medicao = "---";

                                switch (ctrl.MedicaoTy)
                                {
                                    case 1:
                                        medicao = "Utilidades [Média]";
                                        break;

                                    case 2:
                                        medicao = "Utilidades [Totalizado]";
                                        break;

                                    case 3:
                                        medicao = "Analógica [Média]";
                                        break;

                                    case 4:
                                        medicao = "Ciclômetro [Totalizado]";
                                        break;
                                }

                                textoCelulaXLS(row, 1, medicao);

                                // Medição
                                switch (ctrl.MedicaoTy)
                                {
                                    case 1:
                                    case 2:
                                        if (medUtil_Lista != null)
                                        {
                                            medicao = string.Format("[{0:00}] {1}", ctrl.Medicao, medUtil_Lista[ctrl.Medicao].Descricao);
                                        }
                                        else
                                        {
                                            medicao = string.Format("[{0:00}] Medição de Utilidades {1}", ctrl.Medicao, ctrl.Medicao);
                                        }
                                        break;

                                    case 3:
                                        if (medAna_Lista != null)
                                        {
                                            medicao = string.Format("[{0:00}] {1}", ctrl.Medicao, medAna_Lista[ctrl.Medicao].Descricao);
                                        }
                                        else
                                        {
                                            medicao = string.Format("[{0:00}] Medição Analógica {1}", ctrl.Medicao, ctrl.Medicao);
                                        }
                                        break;

                                    case 4:
                                        if (medCiclo_Lista != null)
                                        {
                                            medicao = string.Format("[{0:00}] {1}", ctrl.Medicao, medCiclo_Lista[ctrl.Medicao].Descricao);
                                        }
                                        else
                                        {
                                            medicao = string.Format("[{0:00}] Medição Ciclômetro {1}", ctrl.Medicao, ctrl.Medicao);
                                        }

                                        break;
                                }

                                textoCelulaXLS(row, 2, medicao);

                                // Nome saída
                                string saida = string.Format("[{0}] Saída {1}", sd.NumSaida, sd.NumSaida);
                                if (saidas != null) saida = string.Format("[{0}] {1}", sd.NumSaida, saidas.Find(x => x.NumSaidaGateway == sd.NumSaida).Descricao);
                                textoCelulaXLS(row, 3, saida);

                                // Nível de Ligamento
                                numeroCelulaXLS(row, 4, sd.ControlAnaLigado, _2CellStyle);

                                // Nível de Desligamento
                                numeroCelulaXLS(row, 5, sd.ControlAnaDesligado, _2CellStyle);

                                // Na Falha da Medição
                                string acaoFalhMd = "";

                                switch (ctrl.acaoFalhMd)
                                {
                                    case 0:
                                        acaoFalhMd = "Desligar todas Saídas";
                                        break;

                                    case 1:
                                        acaoFalhMd = "Ligar todas Saídas";
                                        break;

                                    case 2:
                                        acaoFalhMd = "Manter estado das Saídas";
                                        break;
                                }

                                textoCelulaXLS(row, 6, acaoFalhMd);

                                // Tempo para Falha de Medição (seg)
                                numeroCelulaXLS(row, 7, ctrl.TempoFalhMd * 10, _intCellStyle);
                                
                                // Tempo entre Controle (seg)
                                numeroCelulaXLS(row, 8, ctrl.TempoEntrCt * 10, _intCellStyle);
                            }
                        }
                    }
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
            {
                sheet.SetColumnWidth(i, 7000);
                if (i == 4)
                {
                    sheet.SetColumnWidth(i, 10000);
                }

            }

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS_GateX(workbook, sheet, _negritoCellStyle);

            // largura de cada coluna
            sheet.SetColumnWidth(0, 7000);
            sheet.SetColumnWidth(1, 12000);

            // retorna planilha
            return workbook;

        }

        // GET: Controle Analógico PDF
        public ActionResult GateX_CtrlAna_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // controle analógico
            GateX_CtrlAna_Metodos ctrlMetodos = new GateX_CtrlAna_Metodos();
            List<GateX_CtrlAna_Dominio> ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlAna = ctrls;

            // controle analógico (saídas)
            GateX_CtrlAna_SD_Metodos ctrlSDMetodos = new GateX_CtrlAna_SD_Metodos();
            List<GateX_CtrlAna_SD_Dominio> ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlAna_SD = ctrlSDs;

            // medições analógicas
            GateX_MedAna_Lista_Metodos medAnaMetodos = new GateX_MedAna_Lista_Metodos();
            List<GateX_MedAna_Lista_Dominio> medAna_Lista = medAnaMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medAna_Lista = medAna_Lista;

            // medições utilidades
            GateX_MedUtil_Lista_Metodos medUtilMetodos = new GateX_MedUtil_Lista_Metodos();
            List<GateX_MedUtil_Lista_Dominio> medUtil_Lista = medUtilMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medUtil_Lista = medUtil_Lista;

            // medições ciclômetro
            GateX_MedCiclo_Lista_Metodos medCicloMetodos = new GateX_MedCiclo_Lista_Metodos();
            List<GateX_MedCiclo_Lista_Dominio> medCiclo_Lista = medCicloMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medCiclo_Lista = medCiclo_Lista;

            // lista saidas 
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.SaidasDigitais = saidasDigitais;


            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_ControleAnalogico_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_CtrlAna_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        // GET: Controle Analógico EMAIL
        public async Task<ActionResult> GateX_CtrlAna_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.DescriConfigCtrlAna;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // controle analógico
            GateX_CtrlAna_Metodos ctrlMetodos = new GateX_CtrlAna_Metodos();
            List<GateX_CtrlAna_Dominio> ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlAna = ctrls;

            // controle analógico (saídas)
            GateX_CtrlAna_SD_Metodos ctrlSDMetodos = new GateX_CtrlAna_SD_Metodos();
            List<GateX_CtrlAna_SD_Dominio> ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlAna_SD = ctrlSDs;

            // medições analógicas
            GateX_MedAna_Lista_Metodos medAnaMetodos = new GateX_MedAna_Lista_Metodos();
            List<GateX_MedAna_Lista_Dominio> medAna_Lista = medAnaMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medAna_Lista = medAna_Lista;

            // medições utilidades
            GateX_MedUtil_Lista_Metodos medUtilMetodos = new GateX_MedUtil_Lista_Metodos();
            List<GateX_MedUtil_Lista_Dominio> medUtil_Lista = medUtilMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medUtil_Lista = medUtil_Lista;

            // medições ciclômetro
            GateX_MedCiclo_Lista_Metodos medCicloMetodos = new GateX_MedCiclo_Lista_Metodos();
            List<GateX_MedCiclo_Lista_Dominio> medCiclo_Lista = medCicloMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medCiclo_Lista = medCiclo_Lista;

            // lista saidas 
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.SaidasDigitais = saidasDigitais;


            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_ControleAnalogico_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_CtrlAna_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "GateX_ControleAnalogicoEmail";
            var message = await EMailTemplateAsync(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.NomeGateway", ViewBag.NomeGateway);
            message = message.Replace("ViewBag.DescrConfig", ViewBag.DescrConfig);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Controle Analógico Print
        public ActionResult GateX_CtrlAna_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // controle analógico
            GateX_CtrlAna_Metodos ctrlMetodos = new GateX_CtrlAna_Metodos();
            List<GateX_CtrlAna_Dominio> ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlAna = ctrls;

            // controle analógico (saídas)
            GateX_CtrlAna_SD_Metodos ctrlSDMetodos = new GateX_CtrlAna_SD_Metodos();
            List<GateX_CtrlAna_SD_Dominio> ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlAna_SD = ctrlSDs;

            // medições analógicas
            GateX_MedAna_Lista_Metodos medAnaMetodos = new GateX_MedAna_Lista_Metodos();
            List<GateX_MedAna_Lista_Dominio> medAna_Lista = medAnaMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medAna_Lista = medAna_Lista;

            // medições utilidades
            GateX_MedUtil_Lista_Metodos medUtilMetodos = new GateX_MedUtil_Lista_Metodos();
            List<GateX_MedUtil_Lista_Dominio> medUtil_Lista = medUtilMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medUtil_Lista = medUtil_Lista;

            // medições ciclômetro
            GateX_MedCiclo_Lista_Metodos medCicloMetodos = new GateX_MedCiclo_Lista_Metodos();
            List<GateX_MedCiclo_Lista_Dominio> medCiclo_Lista = medCicloMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medCiclo_Lista = medCiclo_Lista;

            // lista saidas 
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.SaidasDigitais = saidasDigitais;


            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // imprime
            return View();
        }
    }
}
