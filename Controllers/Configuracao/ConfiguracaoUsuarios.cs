﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Security.Cryptography;
using System.Threading.Tasks;
using System.Web.Hosting;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;
using System.Web;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ConfiguracaoController
    {
        // Listas utilizadas
        private void PreparaListas_Usuario(int IDTipoContrato = 0)
        {
            // le tipos medicao
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposMsgEmail = listatiposMetodos.ListarTodos("TipoMsgEmail");
            ViewBag.listatiposMsgEmail = listatiposMsgEmail;

            // tipos acesso
            List<ListaTiposDominio> listatiposAcesso = new List<ListaTiposDominio>();

            // verifica se consultor
            if( IDTipoContrato == 2 )
            {
                // tipos acesso
                listatiposAcesso = new List<ListaTiposDominio>()
	            {
	                new ListaTiposDominio(){ ID = 7, Descricao = "Gestor - Administrador"},
	                new ListaTiposDominio(){ ID = 8, Descricao = "Gestor - Operador"}
	            };
            }
            else
            {
                // tipos acesso
                listatiposAcesso = new List<ListaTiposDominio>()
	            {
	                new ListaTiposDominio(){ ID = 1, Descricao = "Cliente - Administrador"},
	                new ListaTiposDominio(){ ID = 2, Descricao = "Cliente - Operador"}
	            };
            }
            ViewBag.listaTipoAcesso = listatiposAcesso;

            // tipos expirar
            List<ListaTiposDominio> listaExpirar = new List<ListaTiposDominio>();

            for (int i = 1; i <= 12; i++)
            {
                ListaTiposDominio tipo = new ListaTiposDominio();

                // ID
                tipo.ID = i;

                // descrição
                if (i == 0)
                {
                    tipo.Descricao = "Senha não expira";
                }
                else if (i == 1)
                {
                    tipo.Descricao = "1 mês";
                }
                else if (i == 2)
                {
                    tipo.Descricao = "45 dias";
                }
                else
                {
                    tipo.Descricao = string.Format("{0} meses", i);
                }

                // coloca na lista
                listaExpirar.Add(tipo);
            }

            ViewBag.listaExpirar = listaExpirar;

            // tipos idioma
            List<IdiomaDominio> listaTiposIdioma = listatiposMetodos.ListarTodos_Idioma();
            ViewBag.listaTiposIdioma = listaTiposIdioma;

            // le tipos bloqueio
            List<ListaTiposDominio> listaTiposBloqueio = listatiposMetodos.ListarTodos("TipoBloqueio", false);
            ViewBag.listaTiposBloqueio = listaTiposBloqueio;

            // le tipos segundo fator
            List<ListaTiposDominio> listaTiposSegundoFator = listatiposMetodos.ListarTodos("TipoSegundoFator", false);
            ViewBag.listaTiposSegundoFator = listaTiposSegundoFator;

            return;
        }

        // GET: Configuracao Usuarios
        public ActionResult Usuarios(int IDCliente)
        {
            // tela de ajuda - usuarios
            CookieStore.SalvaCookie_String("PaginaAjuda", "Configuracao_Usuarios");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_Usuario(ViewBag._IDTipoContrato);

            // le usuarios
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            List<UsuarioDominio> listaUsuarios = usuarioMetodos.ListarPorIDCliente(IDCliente);
            return View(listaUsuarios);
        }

        // GET: Configuracao Usuarios - Editar
        public ActionResult Usuarios_Editar(int IDUsuario)
        {
            // tela de ajuda - usuarios
            CookieStore.SalvaCookie_String("PaginaAjuda", "Configuracao_UsuariosEditar");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // IDUsuario atual
            int IDUsuario_atual = ViewBag._IDUsuario;

            // verifica se usuário tem permissão a visualizar este usuário
            if (!VerificaPermissaoVisualizarUsuario(IDUsuario_atual, IDUsuario))
            {
                // caso usuário tentou burlar IDUsuario, faço o logout

                // redireciona para o site de retorno
                string siteinicio = CookieStore.LeCookie_String("SiteInicio");

                // evento logout
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento_IDUsuario(IDUsuario_atual, USUARIO_EVENTO.LOGOUT);

                // logout
                System.Web.Security.FormsAuthentication.SignOut();
                Session.Abandon();

                // limpar cookies
                LimparCookies();

                return new RedirectResult(siteinicio);
            }

            // IDCliente e IDConsultor
            int IDCliente = ViewBag._IDCliente;
            int IDConsultor = 0;

            // le consultor do cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cliente_atual = clienteMetodos.ListarPorId(IDCliente);

            if (cliente_atual != null)
            {
                // IDConsultor do cliente
                IDConsultor = cliente_atual.IDConsultor;
            }

            // le grupos de painéis do cliente
            DashboardGrupoMetodos dashboardGrupoMetodos = new DashboardGrupoMetodos();
            List<DashboardGrupoDominio> listaDashBoardGrupo = dashboardGrupoMetodos.ListarPorIdClienteConsultor(IDCliente, IDConsultor);
            ViewBag.listaDashBoardGrupo = listaDashBoardGrupo;

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_Usuario(ViewBag._IDTipoContrato);

            // verifica se adicionando
            UsuarioDominio usuario = new UsuarioDominio();
            if (IDUsuario == 0)
            {
                // zera usuario com default
                usuario.IDCliente = IDCliente;

                usuario.NomeUsuario = "";
                usuario.Apelido = "";
                usuario.Login = "";

                usuario.Senha = string.Format("Mudar.{0}", DateTime.Now.Year);
                usuario.SenhaAtual = usuario.Senha;
                usuario.IDTipoSegundoFator = TIPO_SEGUNDO_FATOR.NaoUtilizar;
                usuario.CodigoSegundoFator = "";
                usuario.ExpirarSegundoFator = new DateTime(2000, 1, 1, 0, 0, 0);

                usuario.LogoConsult = "";

                usuario.Email = "";
                usuario.EmailAtual = "confirmar";
                usuario.EmailConfirmado = 0;
                usuario.Telefone = "";
                usuario.Ramal = "";
                usuario.Celular = "";
                usuario.Cargo = "";
                usuario.Departamento = "";
                usuario.IDIdioma = 0;
                usuario.IDTema = 0;
                usuario.API_Key = "";
                usuario.API_Limite = 100;

                usuario.Bloqueio = 0;
                usuario.BloqueioEm = DateTime.Now;
                usuario.ExpirarMeses = 6;
                usuario.ExpirarEm = DateTime.Now.AddYears(1);
                usuario.ShowIntro = 1;
                usuario.AceiteLGPD = 0;

                usuario.IDDashboardGrupo = 0;

                usuario.Diario = 0;
                usuario.Semanal = 0;
                usuario.Mensal = 0;
                usuario.DemAtv = 0;
                usuario.FatPot = 0;
                usuario.Consumo = 0;
                usuario.Supervisao = 0;
                usuario.Util_Anal_Ciclo = 0;
                usuario.Gestao_Contrato = 0;
                usuario.Gestao_Energia = 0;

                usuario.Ranking = 0;
                usuario.Rateio = 0;

                usuario.ConfigGrupo = "";
                usuario.ConfigGrupoEmail = "";
                usuario.ConfigUnid = "";
                usuario.ConfigUnidEmail = "";
                usuario.ConfigMed = "";
                usuario.ConfigMedEmail = "";

                usuario.View_Analises = 1;
                usuario.View_Financas = 1;
                usuario.View_KPI = 0;
                usuario.View_Ranking = 0;
                usuario.View_Rateio = 0;
                usuario.View_Metas = 1;
                usuario.View_Configuracao = 0;
                usuario.View_ConfiguracaoRemota = 0;
                usuario.View_PerfilUsuario = 1;

                // COMERC nao quer que aparece
                if (IDConsultor == CLIENTES_ESPECIAIS.COMERC)
                {
                    usuario.View_Analises = 0;
                    usuario.View_Financas = 0;
                    usuario.View_KPI = 0;
                    usuario.View_Ranking = 1;
                    usuario.View_Rateio = 0;
                    usuario.View_Metas = 0;
                }

                // BRMALLS nao quer que aparece
                if (IDConsultor == CLIENTES_ESPECIAIS.BRMALLS)
                {
                    usuario.View_Analises = 0;
                    usuario.View_Financas = 0;
                    usuario.View_KPI = 0;
                    usuario.View_Ranking = 1;
                    usuario.View_Rateio = 1;
                    usuario.View_Metas = 0;
                }

                // RETHA nao quer que aparece
                if (IDConsultor == CLIENTES_ESPECIAIS.RETHA)
                {
                    usuario.View_Analises = 0;
                    usuario.View_Financas = 0;
                    usuario.View_KPI = 0;
                    usuario.View_Ranking = 0;
                    usuario.View_Rateio = 0;
                    usuario.View_Metas = 0;
                }

                // CPFL nao quer que aparece
                if (IDConsultor == CLIENTES_ESPECIAIS.CPFL)
                {
                    usuario.View_Analises = 0;
                    usuario.View_Financas = 0;
                    usuario.View_KPI = 0;
                    usuario.View_Ranking = 0;
                    usuario.View_Rateio = 0;
                    usuario.View_Metas = 0;
                }

                // LUMINAE nao quer que aparece
                if (IDConsultor == CLIENTES_ESPECIAIS.LUMINAE)
                {
                    usuario.View_Analises = 1;
                    usuario.View_Financas = 1;
                    usuario.View_KPI = 0;
                    usuario.View_Ranking = 0;
                    usuario.View_Rateio = 0;
                    usuario.View_Metas = 0;
                }

                // cliente ARAGUAIA nao quer que aparece
                if (IDCliente == CLIENTES_ESPECIAIS.ARAGUAIA)
                {
                    usuario.View_Analises = 0;
                    usuario.View_Financas = 0;
                    usuario.View_KPI = 1;
                    usuario.View_Ranking = 1;
                    usuario.View_Rateio = 0;
                    usuario.View_Metas = 0;
                }

                // cliente SERBOM nao quer que aparece
                if (IDCliente == CLIENTES_ESPECIAIS.SERBOM)
                {
                    usuario.View_Analises = 0;
                    usuario.View_Financas = 0;
                    usuario.View_KPI = 0;
                    usuario.View_Ranking = 0;
                    usuario.View_Rateio = 0;
                    usuario.View_Metas = 0;
                }

                // verifica se cliente eh consultor
                if(ViewBag._IDTipoContrato == 2)
                {
                    // consultor operador
                    usuario.IDTipoAcesso = ISUSER.Consultor_Oper;
                }
                else
                {
                    // cliente operador
                    usuario.IDTipoAcesso = ISUSER.Cliente_Oper;
                }
            }
            else
            {
                // le usuario
                UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
                usuario = usuarioMetodos.ListarPorId(IDUsuario);
            }

            // le usuario medicoes
            UsuarioMedicaoMetodos usuarioMedicaoMetodos = new UsuarioMedicaoMetodos();
            List<UsuarioMedicaoDominio> usuarioMedicoes = usuarioMedicaoMetodos.ListarTodasMedicoesIDUsuario(IDUsuario);
            ViewBag.usuarioMedicoes = usuarioMedicoes;

            // cria lista de medicoes habilitadas para o usuario
            List<int> ConfigMedicaoList = new List<int>();

            foreach (UsuarioMedicaoDominio usuarioMedicao in usuarioMedicoes)
            {
                ConfigMedicaoList.Add(usuarioMedicao.IDMedicao);
            }

            // le medicoes configuradas do usuario para lista
            CliGateGrupoUnidMedicoesMetodos medicoesMetodos = new CliGateGrupoUnidMedicoesMetodos();
            List<CliGateGrupoUnidMedicoesDominio> medicoes = new List<CliGateGrupoUnidMedicoesDominio>();

            if (ConfigMedicaoList != null)
                medicoes = medicoesMetodos.ListarPorIDCliente(-1, ConfigMedicaoList);
            ViewBag.Medicoes = medicoes;

            // le todas medicoes do cliente para lista
            List<CliGateGrupoUnidMedicoesDominio> medicoes2 = new List<CliGateGrupoUnidMedicoesDominio>();

            // verifica se consultor
            if( isUser.isConsultor(usuario.IDTipoAcesso) )
            {
                // le clientes associados ao consultor - ativos
                ClientesMetodos clientesMetodos = new ClientesMetodos();
                List<ClientesDominio> clientes = clientesMetodos.ListarPorIDConsultor(usuario.IDCliente, 1);

                foreach (ClientesDominio cliente in clientes)
                {
                    List<CliGateGrupoUnidMedicoesDominio> medicoes3 = new List<CliGateGrupoUnidMedicoesDominio>();
                    medicoes3 = medicoesMetodos.ListarPorIDCliente(cliente.IDCliente);

                    foreach (CliGateGrupoUnidMedicoesDominio medicao in medicoes3)
                    {
                        // coloca na lista
                        medicoes2.Add(medicao);
                    }
                }
            }
            else
            {
                medicoes2 = medicoesMetodos.ListarPorIDCliente(usuario.IDCliente);
            }
            ViewBag.Medicoes2 = medicoes2;


            // esconde a senha
            usuario.Senha = "********";
            usuario.SenhaAtual = "********";


            return View(usuario);
        }

        // verifica se usuário tem permissão a visualizar este usuário
        private bool VerificaPermissaoVisualizarUsuario(int IDUsuario_solicitante, int IDUsuario)
        {
            // libero caso for adicionar usuário
            if (IDUsuario == 0)
            {
                // libero
                return (true);
            }

            // leio usuário atual
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuarioSolicitante = usuarioMetodos.ListarPorId(IDUsuario_solicitante);

            if (usuarioSolicitante == null)
            {
                // erro
                return (false);
            }

            // libero caso admin da GESTAL
            if (isUser.isGESTAL(usuarioSolicitante.IDTipoAcesso))
            {
                // libero
                return (true);
            }

            // leio usuário a ser verificado
            UsuarioDominio usuario = usuarioMetodos.ListarPorId(IDUsuario);

            if (usuario == null)
            {
                // erro
                return (false);
            }

            // verifico se operador 
            if (isUser.isOperador(usuarioSolicitante.IDTipoAcesso))
            {
                // usuário comum não pode editar usuários
                return (false);
            }

            // verifico se gerente
            if (isUser.isGerente(usuarioSolicitante.IDTipoAcesso))
            {
                // verifica se usuário pode ser visto pelo solicitante
                if (usuarioSolicitante.IDCliente == usuario.IDCliente)
                {
                    // libero
                    return (true);
                }

                // erro
                return (false);
            }

            // verifico se gestor
            if (isUser.isConsultor(usuarioSolicitante.IDTipoAcesso))
            {
                // cliente
                ClientesMetodos clienteMetodos = new ClientesMetodos();
                ClientesDominio cliente = clienteMetodos.ListarPorId(usuario.IDCliente);

                if (cliente == null)
                {
                    // erro
                    return (false);
                }

                // verifica se cliente é do gestor
                // o campo IDCliente do usuário solicitante contém o IDConsultor
                if (cliente.IDConsultor == usuarioSolicitante.IDCliente)
                {
                    // libero
                    return (true);
                }

                // erro
                return (false);
            }

            // erro
            return (true);
        }

        // limpar cookies
        private void LimparCookies()
        {
            // limpa cookies
            string sessionId = Session.SessionID;

            // lista de todos os cookies
            foreach (string cookieName in Request.Cookies.AllKeys)
            {
                HttpCookie cookie = Request.Cookies[cookieName];
                if (cookie != null)
                {
                    // coloco data de expiração anterior a agora para fechar
                    cookie.Expires = DateTime.Now.AddDays(-1);
                    Response.Cookies.Add(cookie);
                }
            }

            return;
        }


        // POST: Configuracao Usuarios - Salvar
        [HttpPost]
        public ActionResult Usuarios_Salvar(UsuarioDominio usuario, List<UsuarioMedicaoDominio> usuarioMedicoes)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // protege contra IDCliente inválido
            if (usuario.IDCliente < 0)
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Erro ao salvar. Entrar em contato com o suporte e informar código -10"
                };

                // retorna status
                return Json(returnedData);
            }

            // protege contra salvar como Admin
            // só pode ser admin Junior e Sérgio, se quiser ter outro tem que fazer via banco de dados
            if (usuario.IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN && (usuario.IDUsuario != 1 && usuario.IDUsuario != 4165))
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Erro ao salvar. Entrar em contato com o suporte e informar código 0"
                };

                // retorna status
                return Json(returnedData);
            }

            // usuário metodos
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();

            // verifica se usuário existe
            if (usuario.IDUsuario > 0)
            {
                // le usuario atual
                UsuarioDominio usuario_atual = new UsuarioDominio();
                usuario_atual = usuarioMetodos.ListarPorId(usuario.IDUsuario);

                // protege usuario
                if (usuario_atual == null)
                {
                    returnedData = new
                    {
                        status = "ERRO",
                        erro = "Erro ao salvar. Entrar em contato com o suporte e informar código -8"
                    };

                    // retorna status
                    return Json(returnedData);
                }

                // verifica se a senha mudou
                if (usuario.Senha != "********" && usuario.Senha != usuario_atual.SenhaAtual)
                {
                    // senhas usadas
                    SenhasUsadasMetodos senhasUsadasMetodos = new SenhasUsadasMetodos();

                    // verifica se senha já foi usada
                    if (senhasUsadasMetodos.VerificaSenhaUsada(usuario.IDUsuario, usuario.Senha))
                    {
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "Você está repetindo alguma das últimas 10 senhas utilizadas.<br><br>Por favor cadastrar outra senha."
                        };

                        // retorna status
                        return Json(returnedData);
                    }

                    // salva senha usada
                    senhasUsadasMetodos.Inserir(usuario.IDUsuario, usuario.Senha);
                }
                else
                {
                    // não alterou a senha uso a atual
                    usuario.Senha = usuario_atual.Senha;
                    usuario.SenhaAtual = usuario_atual.SenhaAtual;
                }
            }
            else
            {
                // verifica se a senha não mudou
                if (usuario.Senha == "********")
                {
                    // senha default para novo usuário
                    usuario.Senha = string.Format("Mudar.{0}", DateTime.Now.Year);
                }

                usuario.SenhaAtual = usuario.Senha;
            }


            // verifica se existe outro usuario com o mesmo login
            if (usuarioMetodos.VerificarDuplicidade(usuario))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Login existente."
                };

                // retorna status
                return Json(returnedData);
            }
            else
            {
                // verifica se mudou email
                bool mudou_email = false;

                // verifica se o email mudou
                if (usuario.Email != usuario.EmailAtual)
                {
                    // mudou email
                     mudou_email = true;
                }

                // zera inicialmente medicoes
                usuario.ConfigGrupo = "";
                usuario.ConfigGrupoEmail = "";
                usuario.ConfigUnid = "";
                usuario.ConfigUnidEmail = "";
                usuario.ConfigMed = "";
                usuario.ConfigMedEmail = "";

                // salva usuario 
                usuarioMetodos.Salvar(usuario);

                // verifica integridade
                int retorno = usuarioMetodos.VerificarIntegridade(usuario);

                switch (retorno)
                {
                    case 1:
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "Usuário não adicionado."
                        };
                        break;

                    case 2:
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "IDUsuario adicionado com valor alto.<br><br>Favor verificar o usuário criado antes de prosseguir !!!"
                        };
                        break;
                }

                // pego IDUsuario novamente (pois pode ter sido insercao)
                UsuarioDominio user = usuarioMetodos.ListarPorLogin(usuario.Login);

                // verifica se salvou
                if (user != null)
                {
                    // verifica se o email mudou
                    if (mudou_email)
                    {
                        // enviar email de confirmação
                        EnviarConfirmacaoEmail(user.IDUsuario, user.NomeUsuario, user.Email);
                    }

                    // evento 
                    UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();

                    if (usuario.IDUsuario > 0)
                    {
                        usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.USUARIO, user.IDUsuario);
                    }
                    else
                    {
                        usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.USUARIO, user.IDUsuario);
                    }
                }

                // envia mensagem de boas-vindas
                if (usuario.IDUsuario == 0 && user != null)
                {
                    // data e hora atual
                    DateTime datahora = DateTime.Now;

                    // enviar mensagem para usuario
                    UsuarioMensagemMetodos usuarioMensagemMetodos = new UsuarioMensagemMetodos();
                    UsuarioMensagemDominio mensagem = new UsuarioMensagemDominio();

                    // preenche
                    mensagem.IDUsuarioMensagem = 0;
                    mensagem.IDMensagem = 1;
                    mensagem.DataHoraStatus = datahora;
                    mensagem.IDStatus = 0;
                    mensagem.IDUsuario = user.IDUsuario;

                    // envia mensagem
                    usuarioMensagemMetodos.Salvar(mensagem);
                }

                // salva usuariomedicao
                UsuarioMedicaoMetodos usuarioMedicaoMetodos = new UsuarioMedicaoMetodos();

                if (usuarioMedicoes == null && user != null)
                {
                    // exclui todos as medicoes deste usuario
                    usuarioMedicaoMetodos.Excluir(user.IDUsuario);
                }

                if( usuarioMedicoes != null && user != null)
                {
                    // exclui todos as medicoes deste usuario
                    usuarioMedicaoMetodos.Excluir(user.IDUsuario);

                    // percorre lista e salva
                    foreach(UsuarioMedicaoDominio usuarioMedicao in usuarioMedicoes)
                    {
                        // le cliente e gateway da medicao
                        MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                        MedicoesDominio medicao = medicaoMetodos.ListarPorId(usuarioMedicao.IDMedicao);

                        if( medicao != null)
                        {
                            // preenche campos que faltam
                            usuarioMedicao.IDUsuario = user.IDUsuario;
                            usuarioMedicao.IDCliente = medicao.IDCliente;
                            usuarioMedicao.IDGateway = medicao.IDGateway;


                            // utiliza os flags do usuario
                            usuarioMedicao.RelatDiario = false;
                            usuarioMedicao.RelatSemanal = false;
                            usuarioMedicao.RelatMensal = false;
                            usuarioMedicao.RelatRanking = false;
                            usuarioMedicao.MsgGeren = false;
                            usuarioMedicao.MsgProcessa = false;

                            // verifico os flags de relatorio periodico
                            if (user.DemAtv == 1 || user.FatPot == 1 || user.Consumo == 1 || user.Supervisao == 1 || user.Util_Anal_Ciclo == 1)
                            {
                                // diario
                                if (user.Diario == 1)
                                    usuarioMedicao.RelatDiario = true;

                                // semanal
                                if (user.Semanal == 1)
                                    usuarioMedicao.RelatSemanal = true;

                                // mensal
                                if (user.Mensal == 1)
                                    usuarioMedicao.RelatMensal = true;
                            }

                            // verifico o flag de relatorio ranking
                            if (user.Ranking == 1)
                            {
                                // ranking
                                usuarioMedicao.RelatRanking = true;
                            }

                            // verifico os flags de mensagens de gerenciamento
                            if( user.MsgCliMed_Dem == 1 || user.MsgCliMed_Cons == 1 || user.MsgCliMed_FatPot == 1)
                            {
                                // mensagens de gerenciamento proveniente da medicao (dem alta, baixa, fat pot, cons alto, baixo)
                                usuarioMedicao.MsgGeren = true;
                            }

                            // verifico os flags de processamento
                            if (user.MsgCliEve == 1)
                            {
                                // mensagens de gerenciamento proveniente do processamento dos arquivos recebidos
                                usuarioMedicao.MsgProcessa = true;
                            }


                            // salva
                            usuarioMedicaoMetodos.Salvar(usuarioMedicao);
                        }
                    }

                    //
                    // ATUALIZA CLIENTES E MEDICOES HABILITADOS
                    //
                    // uso o UsuariosMedicoes para atualizar o campo CONFIGMED / CONFIGMEDEMAIL e flags de recebimento de email em Usuarios
                    if ( isUser.isCliente(user.IDTipoAcesso) )
                    {
                        usuarioMedicaoMetodos.AtualizarUsuariosMedicoes(user.IDUsuario);
                    }

                    // le cookies
                    LeCookies_SmartEnergy();

                    // verifica se usuario atual
                    if (usuario.IDUsuario == ViewBag._IDUsuario)
                    {
                        // atualiza nome no cabecalho
                        System.Web.Security.FormsAuthentication.SetAuthCookie(usuario.Apelido, false);

                        // lista de medicoes habilitadas para o usuario
                        string ConfigMed = "";

                        // verifica se tem medicao
                        if (usuarioMedicoes.Count > 0)
                        {
                            // percorre todas as medicoes e monta string
                            foreach (UsuarioMedicaoDominio usuariomedicao in usuarioMedicoes)
                            {
                                // medicao habilitada
                                ConfigMed = ConfigMed + "/" + usuariomedicao.IDMedicao.ToString();
                            }

                            // fecha barra
                            ConfigMed = ConfigMed + "/";
                        }

                        CookieStore.SalvaCookie_String("ConfigMed", ConfigMed);
                    }
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // POST: Configuracao Usuarios - AlterarSenha
        [HttpPost]
        public ActionResult Usuarios_AlterarSenha(UsuarioDominio usuario)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // salva senha
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            usuarioMetodos.AlterarSenha(usuario);

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuracao Usuarios - Excluir
        public ActionResult Usuarios_Excluir(int IDUsuario)
        {
            // apaga o usuario
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            usuarioMetodos.Excluir(IDUsuario);

            UsuarioMedicaoMetodos usuarioMedicaoMetodos = new UsuarioMedicaoMetodos();
            usuarioMedicaoMetodos.Excluir(IDUsuario);

            // apaga UsuariosMensagens
            UsuarioMensagemMetodos usuarioMensagensMetodos = new UsuarioMensagemMetodos();
            usuarioMensagensMetodos.ExcluirTodosUsuario(IDUsuario);


            // evento 
            UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.EXCLUI_CONFIG, TABELA_CONFIG.USUARIO, IDUsuario);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // GET: Enviar Boas-Vindas
        public async Task<ActionResult> EnviarBoasVindas(string Login)
        {

            // pego IDUsuario novamente (pois pode ter sido insercao)
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio user = usuarioMetodos.ListarPorLogin(Login);

            // verifica se salvou
            if (user == null)
            {
                // retorna status
                return Json("ERRO", JsonRequestBehavior.AllowGet);
            }

            // le cookies
            LeCookies_SmartEnergy();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // emails
            string[] emails = { user.Email };

            // assunto
            string assunto = "Bem-vindo ao Smart Energy";

            // envia EMAIL
            var emailTemplate = "WelcomeEmail";

            if (IDConsultor == CLIENTES_ESPECIAIS.CPFL)
            {
                emailTemplate = "WelcomeEmail_CPFL";
            }

            var message = await EMailTemplateAsync(emailTemplate);
            message = message.Replace("ViewBag.Nome", user.NomeUsuario);
            message = message.Replace("ViewBag.Login", user.Login);
            message = message.Replace("ViewBag.Email", user.Email);
            message = message.Replace("ViewBag.Senha", user.Senha);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, null);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // GET: Enviar Confirmação Email
        public void EnviarConfirmacaoEmail(int IDUsuario, string Nome, string Email)
        {

            // le cookies
            LeCookies_SmartEnergy();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // cria código de ativação
            string CodigoAtivacao = GenerateRandomString(15);

            // salva no banco de dados
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            usuarioMetodos.Atualiza_CodigoAtivacao(IDUsuario, CodigoAtivacao);

            // assunto
            string assunto = "[Smart Energy] Confirmação de Email";

            // envia EMAIL
            var emailTemplate = "ConfirmacaoEmail";

            if (IDConsultor == CLIENTES_ESPECIAIS.CPFL)
            {
                emailTemplate = "ConfirmacaoEmail_CPFL";
            }

            var message = EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.Nome", Nome);
            message = message.Replace("ViewBag.IDUsuario", IDUsuario.ToString());
            message = message.Replace("ViewBag.CodigoAtivacao", CodigoAtivacao);
            EmailServices.SendEmail(Email, assunto, message, "", "", null);

            // retorna
            return;
        }

        // gerar chave randomica
        public static string GenerateRandomString(int length)
        {
            var numArray = new byte[length];
            new RNGCryptoServiceProvider().GetBytes(numArray);
            return CleanUpBase64String(Convert.ToBase64String(numArray), length);
        }

        private static string CleanUpBase64String(string input, int maxLength)
        {
            input = input.Replace("-", "");
            input = input.Replace("=", "");
            input = input.Replace("/", "");
            input = input.Replace("+", "");
            input = input.Replace(" ", "");
            while (input.Length < maxLength)
                input = input + GenerateRandomString(maxLength);
            return input.Length <= maxLength ?
                input.ToUpper() : //In my case I want capital letters
                input.ToUpper().Substring(0, maxLength);
        }


        public static async Task<string> EMailTemplateAsync(string template)
        {
            var templateFilePath = HostingEnvironment.MapPath("~/Content/emailTemplates/") + template + ".cshtml";
            StreamReader objstreamreaderfile = new StreamReader(templateFilePath);
            var body = await objstreamreaderfile.ReadToEndAsync();
            objstreamreaderfile.Close();
            return body;
        }

        public static string EMailTemplate(string template)
        {
            var templateFilePath = HostingEnvironment.MapPath("~/Content/emailTemplates/") + template + ".cshtml";
            StreamReader objstreamreaderfile = new StreamReader(templateFilePath);
            var body = objstreamreaderfile.ReadToEnd();
            objstreamreaderfile.Close();
            return body;
        }


        // GET: Gerar API Key
        public ActionResult GerarAPI_Key(int IDUsuario)
        {
            // gerar API Key
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            usuarioMetodos.GerarAPI_Key(IDUsuario);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // GET: Excluir API Key
        public ActionResult ExcluirAPI_Key(int IDUsuario)
        {
            // excluir API Key
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            usuarioMetodos.ExcluirAPI_Key(IDUsuario);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}