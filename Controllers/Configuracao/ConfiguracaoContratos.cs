﻿using System;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ConfiguracaoController
    {
        // GET: Configuracao HistoricoContratos
        public ActionResult HistoricoContratos(int IDMedicao)
        {
            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // unidade de potencia
            UnidadePotencia(IDMedicao);

            // permissoes
            Permissoes();

            // le historicocontratos
            HistoricoContratosMetodos contratosMetodos = new HistoricoContratosMetodos();
            var listaContratos = contratosMetodos.ListarPorIDMedicao(IDMedicao);
            return View(listaContratos);
        }

        // GET: Configuracao HistoricoContratos - Editar
        public ActionResult HistoricoContratos_Editar(int IDContrato, int IDMedicao)
        {
            // pagina anterior
            ViewBag.returnUrl = Request.UrlReferrer;

            // ID Contrato
            ViewBag.IDContrato = IDContrato;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // unidade de potencia
            UnidadePotencia(IDMedicao);

            // permissoes
            Permissoes();

            // verifica se adicionando
            HistoricoContratosDominio contrato = new HistoricoContratosDominio();
            if (IDContrato == 0)
            {
                // zera grupounidades com default
                contrato.IDContrato = 0;
                contrato.IDCliente = ViewBag._IDCliente;
                contrato.IDMedicao = IDMedicao;
                contrato.Data = DateTime.Now;
                contrato.DataTexto = contrato.Data.ToString("d");
                contrato.ContratoDemP = 0;
                contrato.ContratoDemFP = 0;
            }
            else
            {
                // le historicocontratos
                HistoricoContratosMetodos contratosMetodos = new HistoricoContratosMetodos();
                contrato = contratosMetodos.ListarPorID(IDContrato);
            }

            return View(contrato);
        }

        // POST: Configuracao HistoricoContratos - Salvar
        [HttpPost]
        public ActionResult HistoricoContratos_Salvar(HistoricoContratosDominio contrato)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // parse data
            contrato.Data = DateTime.Parse(contrato.DataTexto); 

            // verifica se existe outro contrato com a mesma data
            HistoricoContratosMetodos contratosMetodos = new HistoricoContratosMetodos();
            if (contratosMetodos.VerificarDuplicidade(contrato))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Contrato existente."
                };
            }
            else
            {
                // salva historicocontratos 
                contratosMetodos.Salvar(contrato);
            }

            // leio contrato mais recente
            HistoricoContratosDominio histContrato = contratosMetodos.ListarPorIDMedicaoMaisRecente(contrato.IDMedicao);

            // atualiza contrato na medição
            if (histContrato != null)
            {
                MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                medicaoMetodos.Atualizar_ContratosDemanda(contrato.IDMedicao, histContrato.ContratoDemP, histContrato.ContratoDemFP);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuracao HistoricoContratos - Excluir
        public ActionResult HistoricoContratos_Excluir(int IDContrato)
        {
            // leio contrato
            HistoricoContratosMetodos contratosMetodos = new HistoricoContratosMetodos();
            HistoricoContratosDominio contrato = contratosMetodos.ListarPorID(IDContrato);

            // apaga o historicocontratos
            contratosMetodos.Excluir(IDContrato);

            // leio contrato mais recente
            HistoricoContratosDominio histContrato = contratosMetodos.ListarPorIDMedicaoMaisRecente(contrato.IDMedicao);

            // atualiza contrato na medição
            if (histContrato != null)
            {
                MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                medicaoMetodos.Atualizar_ContratosDemanda(contrato.IDMedicao, histContrato.ContratoDemP, histContrato.ContratoDemFP);
            }

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}