﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ConfiguracaoController
    {
        // Listas utilizadas
        private void PreparaListas_Contato()
        {
            return;
        }

        // GET: Configuracao Contatos
        public ActionResult Contatos(int IDCliente)
        {
            // tela de ajuda - contatos
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_Contato();

            // le contatos
            ContatosMetodos contatosMetodos = new ContatosMetodos();
            List<ContatosDominio> listaContatos = contatosMetodos.ListarPorIDCliente(IDCliente);

            return View(listaContatos);
        }

        // GET: Configuracao Contato - Editar
        public ActionResult Contato_Editar(int IDContato)
        {
            // tela de ajuda - contatos
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // pagina anterior
            ViewBag.returnUrl = Request.UrlReferrer;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // verifica se adicionando
            ContatosDominio contato = new ContatosDominio();
            if (IDContato == 0)
            {
                // zera contato com default
                contato.IDContato = 0;
                contato.IDCliente = ViewBag._IDCliente;

                contato.IDTipoContato = 0;

                contato.Nome = "";
                contato.Email = "";
                contato.Telefone = "";
                contato.Ramal = "";
                contato.Celular = "";
                contato.Cargo = "";
                contato.Departamento = "";
            }
            else
            {
                // le contato
                ContatosMetodos contatosMetodos = new ContatosMetodos();
                contato = contatosMetodos.ListarPorId(IDContato);
            }

            // prepara listas
            PreparaListas_Contato();

            return View(contato);
        }

        // POST: Configuracao Contato - Salvar
        [HttpPost]
        public ActionResult Contato_Salvar(ContatosDominio contato)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outra contato com o mesmo nome
            ContatosMetodos contatosMetodos = new ContatosMetodos();
            if (contatosMetodos.VerificarDuplicidade(contato))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Contato existente."
                };
            }
            else
            {
                // salva contato
                contatosMetodos.Salvar(contato);

                // evento 
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();

                if (contato.IDContato > 0)
                {
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.CONTATO, contato.IDContato);
                }
                else
                {
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.CONTATO, contato.IDContato);
                }

                // verifica integridade
                int retorno = contatosMetodos.VerificarIntegridade(contato);

                switch (retorno)
                {
                    case 1:
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "Contato não adicionado."
                        };
                        break;

                    case 2:
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "IDContato adicionado com valor alto.<br><br>FAVOR AVISAR O DESENVOLVIMENTO ANTES DE PROSSEGUIR !!!"
                        };
                        break;
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuracao Contato - Excluir
        public ActionResult Contato_Excluir(int IDContato)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // apaga o contato
            ContatosMetodos contatosMetodos = new ContatosMetodos();
            contatosMetodos.Excluir(IDContato);

            // evento 
            UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.EXCLUI_CONFIG, TABELA_CONFIG.CONTATO, IDContato);

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }
    }
}