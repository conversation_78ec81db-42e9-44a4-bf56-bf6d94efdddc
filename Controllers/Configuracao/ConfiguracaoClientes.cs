﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ConfiguracaoController
    {
        // Listas utilizadas
        private void PreparaListas_Cliente(int IDConsultor)
        {
            // le tipos contrato
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipos = listatiposMetodos.ListarTodos("TipoContrato");
            ViewBag.listaTipoContrato = listatipos;

            // le tipos supervisao
            List<ListaTiposDominio> listasupervisao = listatiposMetodos.ListarTodos("TipoSupervisao");
            ViewBag.listaTipoSupervisao = listasupervisao;

            // le tipos grafico
            List<ListaTiposDominio> listatiposGrafico = listatiposMetodos.ListarTodos("TipoGrafico");
            ViewBag.listaTipoGrafico = listatiposGrafico;

            // le tipos status ativo
            List<ListaTiposDominio> listatiposStatus = listatiposMetodos.ListarTodos("TipoStatusAtivo");
            ViewBag.listaTipoStatus = listatiposStatus;

            // le tipos empresa
            List<ListaTiposDominio> listatiposEmpresa = listatiposMetodos.ListarTodos("TipoEmpresa", false);
            ViewBag.listaTipoEmpresa = listatiposEmpresa;

            // le tipos sim não
            List<ListaTiposDominio> listatiposSimNao = listatiposMetodos.ListarTodos("TipoSimNao", false);
            ViewBag.listaTipoSimNao = listatiposSimNao;

            // le tipos RamoAtividade
            List<ListaTiposDominio> listatiposRamoAtividade = listatiposMetodos.ListarTodos("TipoRamoAtividade");
            ViewBag.listaTipoRamoAtividade = listatiposRamoAtividade;

            // le tipos Estado
            CidadeEstadoPaisMetodos cidadeMetodos = new CidadeEstadoPaisMetodos();
            List<EstadosDominio> listatiposEstado = cidadeMetodos.ListarTodosEstados();
            ViewBag.listaTipoEstado = listatiposEstado;

            // le tipos Cidade
            List<CidadesDominio> listatiposCidade = cidadeMetodos.ListarTodosCidades(0);
            ViewBag.listaTipoCidade = listatiposCidade;

            // le comercializadoras
            AgentesMetodos agentesMetodos = new AgentesMetodos();
            List<AgentesDominio> listatiposComercializadoras = agentesMetodos.ListarPorComercializadora();
            ViewBag.listaTipoComercializadoras = listatiposComercializadoras;

            // preenche consultores
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            List<ConsultorDominio> consultores = new List<ConsultorDominio>();

            // verifica se consultor
            if (IDConsultor > 0)
            {
                // le consultor atual
                UsuarioDominio usuario = usuarioMetodos.ListarPorId(IDConsultor);

                if (usuario != null)
                {
                    ConsultorDominio consultor = new ConsultorDominio();

                    // copia
                    consultor.IDConsultor = IDConsultor;
                    consultor.Nome = usuario.NomeUsuario;

                    // coloca na lista
                    consultores.Add(consultor);
                }
            }
            else
            {
                // le todos os consultores
                List<UsuarioDominio> usuarios = usuarioMetodos.ListarTodosConsultores();

                foreach (UsuarioDominio usuario in usuarios)
                {
                    ConsultorDominio consultor = new ConsultorDominio();

                    // copia
                    consultor.IDConsultor = usuario.IDUsuario;
                    consultor.Nome = usuario.NomeUsuario;

                    // coloca na lista
                    consultores.Add(consultor);
                }
            }

            ViewBag.listaConsultores = consultores;

            return;
        }

        // GET: Configuracao Clientes
        public ActionResult Clientes()
        {
            // tela de ajuda - clientes
            CookieStore.SalvaCookie_String("PaginaAjuda", "Configuracao_Clientes");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // prepara listas
            PreparaListas_Cliente(IDConsultor);

            // lista de clientes
            List<int> ConfigCliList = ViewBag._ConfigCli;

            // le clientes
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clientesMetodos.ListarTodos(ConfigCliList);

            // percorre clientes e descobre numero de medicoes associadas
            foreach( ClientesDominio cliente in listaClientes )
            {
                // menos o cliente zero
                if (cliente.IDCliente == 0)
                {
                    continue;
                }

                // numero de empresas com este cliente (CPFL - Unidades Consumidoras)
                EmpresasMetodos empresasMetodos = new EmpresasMetodos();
                int NumEmpresas = empresasMetodos.NumEmpresasCliente(cliente.IDCliente);
                cliente.NumEmpresas = NumEmpresas;

                // numero de medicoes com este cliente
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                int NumMedicoes = medicoesMetodos.NumMedicoesClienteTipo(cliente.IDCliente, 1);
                cliente.NumMedicoes = NumMedicoes;

                int NumMedicoes_Virtuais = medicoesMetodos.NumMedicoesClienteTipo(cliente.IDCliente, 2);
                cliente.NumMedicoes_Virtuais = NumMedicoes_Virtuais;

                // numero de gateways com este cliente
                int NumGateways = medicoesMetodos.NumGatewaysCliente(cliente.IDCliente, 0);
                cliente.NumGateways = NumGateways;
            }

            // le agentes
            EmpresasMetodos empresaMetodos = new EmpresasMetodos();
            List<EmpresasDominio> agentesCCEE = empresaMetodos.ListarPorAgenteCCEE(0);
            ViewBag.listaAgentesCCEE = agentesCCEE;

            return View(listaClientes);
        }


        // GET: Configuracao Cliente Árvore
        public ActionResult Cliente_Arvore(int IDCliente)
        {
            // tela de ajuda - clientes
            CookieStore.SalvaCookie_String("PaginaAjuda", "Configuracao_Clientes");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            if (IDCliente > 0)
            {
                CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);

                // le contrato cliente
                ClientesMetodos clienteMetodos = new ClientesMetodos();
                ClientesDominio cli = clienteMetodos.ListarPorId(IDCliente);
                CookieStore.SalvaCookie_Int("IDTipoContrato", cli.IDTipoContrato);
                CookieStore.SalvaCookie_Int("IDTipoSupervisao", cli.IDTipoSupervisao);
                CookieStore.SalvaCookie_String("Nome_GrupoUnidades", cli.Nome_GrupoUnidades);
                CookieStore.SalvaCookie_String("Nome_Unidades", cli.Nome_Unidades);
                CookieStore.SalvaCookie_Int("IDTipoGrafico_Demanda", cli.IDTipoGrafico_Demanda);

                // limpa cookies
                CookieStore.SalvaCookie_Int("_IDMedicao", -10);
                CookieStore.SalvaCookie_Int("IDTipoMedicao", -10);
                CookieStore.SalvaCookie_Int("_IDGateway", -10);
                CookieStore.SalvaCookie_Int("IDTipoGateway", -10);
            }

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // prepara listas
            PreparaListas_Cliente(IDConsultor);

            // lista de clientes
            List<int> ConfigCliList = ViewBag._ConfigCli;


            return View();
        }


        public class json_nivel3
        {
            public string title { get; set; }
            public string type { get; set; }
            public string ID { get; set; }
            public string config { get; set; }
            public string tools1 { get; set; }
            public string tools2 { get; set; }
        }

        public class json_nivel2
        {
            public string title { get; set; }
            public string type { get; set; }
            public string ID { get; set; }
            public string config { get; set; }
            public string tools1 { get; set; }
            public string tools2 { get; set; }

            public List<json_nivel3> children { get; set; }
        }

        public class json_nivel1
        {
            public string title { get; set; }
            public string type { get; set; }
            public string ID { get; set; }
            public string config { get; set; }
            public string tools1 { get; set; }
            public string tools2 { get; set; }

            public List<json_nivel2> children { get; set; }
        }

        public class json_raiz
        {
            public string title { get; set; }
            public string type { get; set; }
            public string ID { get; set; }
            public string config { get; set; }
            public string tools1 { get; set; }
            public string tools2 { get; set; }

            public List<json_nivel1> children { get; set; }
        }


        // GET: Configuracao Árvore
        public JsonResult Config_Arvore()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // IDTipoAcesso
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // permissão
            int Permissao = ViewBag.Permissao;

            //
            // Listas
            //

            // le tipos empresa
            List<ListaTiposDominio> listatiposEmpresa = new List<ListaTiposDominio>();

            listatiposEmpresa = new List<ListaTiposDominio>()
	        {
	            new ListaTiposDominio(){ ID = 1, Descricao = "Agente CCEE"},
	            new ListaTiposDominio(){ ID = 2, Descricao = "Filial"}
	        };

            // le tipos gateway
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposGateway = listatiposMetodos.ListarTodos("TipoGateway");

            // le tipos medicao
            List<ListaTiposDominio> listatiposMedicao = listatiposMetodos.ListarTodos("TipoMedicao");

            // le tipos acesso
            List<ListaTiposDominio> listatiposAcesso = listatiposMetodos.ListarTodos("TipoAcesso");

            //
            // ÁRVORE DO CLIENTE
            //

            // medicoes fórmula
            List<json_nivel1> json_medicoes_formula = new List<json_nivel1>();

            // cliente
            List<json_raiz> json_raiz = new List<json_raiz>();

            // le cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cliente = clienteMetodos.ListarPorId(IDCliente);

            if (cliente != null)
            {
                // empresas
                List<json_nivel1> json_empresas = new List<json_nivel1>();

                // empresas do cliente
                EmpresasMetodos empresaMetodos = new EmpresasMetodos();
                List<EmpresasDominio> empresas = empresaMetodos.ListarPorIDCliente(IDCliente);

                if (empresas != null)
                {
                    // percorre empresas
                    foreach(EmpresasDominio empresa in empresas)
                    {
                        // gateways
                        List<json_nivel2> json_gateways = new List<json_nivel2>();

                        // gateways da empresa
                        GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
                        List<GatewaysDominio> gateways = gatewayMetodos.ListarPorIDEmpresa(empresa.IDEmpresa);

                        if (gateways != null)
                        {
                            // percorre gateways
                            foreach (GatewaysDominio gateway in gateways)
                            {
                                // medicoes
                                List<json_nivel3> json_medicoes = new List<json_nivel3>();

                                // medicoes da gateway
                                MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                                List<MedicoesDominio> medicoes = medicaoMetodos.ListarPorIDGateway(gateway.IDGateway, "ORDER BY Nome");

                                if (medicoes != null)
                                {
                                    // percorre medicoes
                                    foreach (MedicoesDominio medicao in medicoes)
                                    {
                                        // se for medicao fórmula
                                        if (medicao.IDTipoMedicao == TIPO_MEDICAO.ENERGIA_FORMULA || medicao.IDTipoMedicao == TIPO_MEDICAO.UTILIDADES_FORMULA || medicao.IDTipoMedicao == TIPO_MEDICAO.EA_FORMULA)
                                        {
                                            // preenche dados da medicao
                                            json_nivel1 json_med = new json_nivel1();

                                            json_med.title = medicao.Nome;
                                            json_med.ID = medicao.IDMedicao.ToString();
                                            json_med.type = "medicao";
                                            json_med.config = listatiposMedicao.Find(x => x.ID == medicao.IDTipoMedicao).Descricao;

                                            // insere no json medicao fórmula
                                            json_medicoes_formula.Add(json_med);
                                        }
                                        else
                                        {
                                            // preenche dados da medicao
                                            json_nivel3 json_med = new json_nivel3();

                                            json_med.title = medicao.Nome;
                                            json_med.ID = medicao.IDMedicao.ToString();
                                            json_med.type = "medicao";
                                            json_med.config = listatiposMedicao.Find(x => x.ID == medicao.IDTipoMedicao).Descricao;

                                            json_med.tools1 = "";

                                            // verifica se admin
                                            if (Permissao == PERMISSOES.ADMIN)
                                            {
                                                json_med.tools2 = string.Format("<a href='/Configuracao/Medicoes_Editar?IDMedicao={0}' title='Editar Medição'><i class='fa fa-edit icones'></i></a>", medicao.IDMedicao);
                                                json_med.tools2 += string.Format("<a href='#' onclick='javascript:Excluir_Medicao({0});' title='Excluir Medição'><i class='fa fa-times icones'></i> Medição</a>", medicao.IDMedicao);
                                            }
                                            else
                                            {
                                                json_med.tools2 = string.Format("<a href='/Configuracao/Medicoes_Editar?IDMedicao={0}' title='Editar Medição'><i class='fa fa-edit icones'></i> Medição</a>", medicao.IDMedicao);
                                            }

                                            // insere no json medicao 
                                            json_medicoes.Add(json_med);
                                        }
                                    }
                                }

                                // preenche dados da gateway
                                json_nivel2 json_gate = new json_nivel2();

                                json_gate.title = gateway.Nome;
                                json_gate.ID = gateway.IDGateway.ToString();
                                json_gate.type = "gateway";
                                json_gate.config = listatiposGateway.Find(x => x.ID == gateway.IDTipoGateway).Descricao;
                                json_gate.children = json_medicoes;

                                // verifica se admin
                                if (Permissao == PERMISSOES.ADMIN)
                                {
                                    json_gate.tools1 = "<a href='/Configuracao/Medicoes_Editar?IDMedicao=0' title='Adicionar Medição'><i class='fa fa-plus'></i> Medição</a>";
                                    json_gate.tools2 = string.Format("<a href='/Configuracao/Gateway_Editar?IDGateway={0}' title='Editar Gateway'><i class='fa fa-edit icones'></i></a>", gateway.IDGateway);
                                    json_gate.tools2 += string.Format("<a href='#' onclick='javascript:Excluir_Gateway({0});' title='Excluir Gateway'><i class='fa fa-times icones'></i> Gateway</a>", gateway.IDGateway);
                                }
                                else
                                {
                                    json_gate.tools2 = string.Format("<a href='/Configuracao/Gateway_Editar?IDGateway={0}' title='Editar Gateway'><i class='fa fa-edit icones'></i> Gateway</a>", gateway.IDGateway);
                                }

                                // insere no json gateway
                                json_gateways.Add(json_gate);
                            }
                        }

                        // preenche dados da empresa
                        json_nivel1 json_empr = new json_nivel1();

                        json_empr.title = empresa.RazaoSocial;
                        json_empr.ID = empresa.IDEmpresa.ToString();
                        json_empr.type = "empresa";
                        json_empr.config = listatiposEmpresa.Find(x => x.ID == empresa.IDTipoEmpresa).Descricao;
                        json_empr.children = json_gateways;

                        // insere no json empresa
                        json_empresas.Add(json_empr);
                    }
                }

                // preenche dados do cliente
                json_raiz json_cli = new json_raiz();

                json_cli.title = cliente.Fantasia;
                json_cli.ID = cliente.IDCliente.ToString();
                json_cli.type = "cliente";
                json_cli.config = "Cliente";
                json_cli.children = json_empresas;

                // insere no json raiz
                json_raiz.Add(json_cli);
            }

            //
            // MEDICOES FORMULA
            //

            // preenche dados das medicoes fórmula
            json_raiz json_medf = new json_raiz();

            json_medf.title = "Medições Fórmula";
            json_medf.ID = "";
            json_medf.type = "medicao";
            json_medf.config = "";
            json_medf.children = json_medicoes_formula;

            // insere no json raiz
            json_raiz.Add(json_medf);



            //
            // USUÁRIOS
            //

            // usuarios
            List<json_nivel1> json_usuarios = new List<json_nivel1>();

            // usuários do cliente
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            List<UsuarioDominio> usuarios = usuarioMetodos.ListarPorIDCliente(IDCliente);

            if (usuarios != null)
            {
                // percorre usuários
                foreach (UsuarioDominio usuario in usuarios)
                {
                    // preenche dados do usuario
                    json_nivel1 json_us = new json_nivel1();

                    json_us.title = usuario.NomeUsuario;
                    json_us.ID = usuario.IDUsuario.ToString();
                    json_us.type = "usuario";
                    json_us.config = listatiposAcesso.Find(x => x.ID == usuario.IDTipoAcesso).Descricao;

                    // insere no json usuario
                    json_usuarios.Add(json_us);
                }
            }

            // preenche dados dos usuário
            json_raiz json_user = new json_raiz();

            json_user.title = "Usuários";
            json_user.ID = "";
            json_user.type = "usuarios";
            json_user.config = "";
            json_user.children = json_usuarios;

            // insere no json raiz
            json_raiz.Add(json_user);


            // retorna árvore
            return Json(json_raiz, JsonRequestBehavior.AllowGet);
        }



        // GET: Configuracao Cliente - Editar
        public ActionResult Cliente_Editar(int IDCliente)
        {
            // tela de ajuda - clientes
            CookieStore.SalvaCookie_String("PaginaAjuda", "Configuracao_ClientesEditar");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            if (IDCliente > 0)
            {
                CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);

                // le contrato cliente
                ClientesMetodos clienteMetodos = new ClientesMetodos();
                ClientesDominio cli = clienteMetodos.ListarPorId(IDCliente);
                CookieStore.SalvaCookie_Int("IDTipoContrato", cli.IDTipoContrato);
                CookieStore.SalvaCookie_Int("IDTipoSupervisao", cli.IDTipoSupervisao);
                CookieStore.SalvaCookie_String("Nome_GrupoUnidades", cli.Nome_GrupoUnidades);
                CookieStore.SalvaCookie_String("Nome_Unidades", cli.Nome_Unidades);
                CookieStore.SalvaCookie_Int("IDTipoGrafico_Demanda", cli.IDTipoGrafico_Demanda);

                // limpa cookies
                CookieStore.SalvaCookie_Int("_IDMedicao", -10);
                CookieStore.SalvaCookie_Int("IDTipoMedicao", -10);
                CookieStore.SalvaCookie_Int("_IDGateway", -10);
                CookieStore.SalvaCookie_Int("IDTipoGateway", -10);
            }

            // le cookies
            LeCookies_SmartEnergy();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_Cliente(IDConsultor);

            // verifica se adicionando
            ClientesDominio cliente = new ClientesDominio();

            // verifica se adicionando cliente
            if (IDCliente == 0)
            {
                // zera cliente com default
                cliente.IDCliente = 0;
                cliente.IDTipoContrato = 0;
                cliente.IDConsultor = CLIENTES_ESPECIAIS.GESTAL;       // GESTAL Automação

                // verifica se criado por consultor e já dedica cliente a ele
                if (IDConsultor > 0)
                {
                    cliente.IDConsultor = IDConsultor;
                }

                cliente.Nome = "";
                cliente.Fantasia = "";
                cliente.Logo = "LogoSmartEnergy.png";
                cliente.CliVisual = "S";
                cliente.IDTipoStatusAtivo = 1;
                cliente.Nome_GrupoUnidades = "Grupo de Unidades";
                cliente.Nome_Unidades = "Unidade";
                cliente.IDRamoAtividade = 0;
                cliente.IDTipoGrafico_Demanda = 0;
                cliente.IDTipoSupervisao = 0;
            }
            else
            {
                // le cliente
                ClientesMetodos clientesMetodos = new ClientesMetodos();
                cliente = clientesMetodos.ListarPorId(IDCliente);
            }

            // le agentes
            EmpresasMetodos empresaMetodos = new EmpresasMetodos();
            List<EmpresasDominio> agentesCCEE = empresaMetodos.ListarPorAgenteCCEE(IDCliente);
            ViewBag.listaAgentesCCEE = agentesCCEE;

            // le empresas
            EmpresasMetodos empresasMetodos = new EmpresasMetodos();
            List<EmpresasDominio> listaEmpresas = empresasMetodos.ListarPorIDCliente(IDCliente);
            ViewBag.listaEmpresas = listaEmpresas;

            return View(cliente);
        }

        // POST: Configuracao Cliente - Salvar
        [HttpPost]
        public ActionResult Cliente_Salvar(ClientesDominio cliente)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                IDCliente = cliente.IDCliente,
                erro = ""
            };

            // verifica se existe outro cliente com o mesmo nome
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            if (clientesMetodos.VerificarDuplicidade(cliente))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    IDCliente = cliente.IDCliente,
                    erro = "Cliente existente."
                };
            }
            else
            {
                //
                // Salva cliente 
                //
                clientesMetodos.Salvar(cliente);

                // verifica integridade
                int retorno = clientesMetodos.VerificarIntegridade(cliente);

                switch (retorno)
                {
                    case 0:
                        // criar database do cliente (CL_xxxxxx)
                        clientesMetodos.CriarDataBase(cliente);
                        break;

                    case 1:
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            IDCliente = cliente.IDCliente,
                            erro = "Cliente não adicionado."
                        };
                        break;

                    case 2:
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            IDCliente = cliente.IDCliente,
                            erro = "IDCliente adicionado com valor alto.<br><br>FAVOR AVISAR O DESENVOLVIMENTO ANTES DE PROSSEGUIR !!!"
                        };
                        break;
                }

                if (retorno == 0)
                {
                    // pego IDCliente novamente (pois pode ter sido insercao)
                    ClientesDominio cli = clientesMetodos.ListarPorNomeFantasia(cliente.Nome, cliente.Fantasia);

                    // verifica se salvou
                    if (cli != null)
                    {
                        // supervisão das medições - atualiza cliente
                        MedicoesSupervMetodos medSupervMetodos = new MedicoesSupervMetodos();
                        medSupervMetodos.Atualizar_Cliente(cli.IDCliente);

                        // evento 
                        UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();

                        if (cliente.IDCliente > 0)
                        {
                            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.CLIENTE, cli.IDCliente);
                        }
                        else
                        {
                            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.CLIENTE, cli.IDCliente);
                        }

                        // ok
                        returnedData = new
                        {
                            status = "OK",
                            IDCliente = cli.IDCliente,
                            erro = ""
                        };

                        // le cookies
                        LeCookies_SmartEnergy();

                        // Tipo Acesso
                        int IDTipoAcesso = ViewBag._IDTipoAcesso;

                        // IDConsultor
                        int IDConsultor = ViewBag._IDConsultor;

                        // verifica se esta criando e se é gestor ou produção da GESTAL
                        if (cliente.IDCliente == 0 && (IDConsultor > 0 || IDTipoAcesso == TIPO_ACESSO.GESTAL_PRODUCAO))
                        {
                            // crio empresa, gateway, grupo de unidades, unidade e medição inicial
                            if (!Crio_Empresa_Gateway_Unidades_Medicao_Inicial(cli))
                            {
                                // retorna status
                                returnedData = new
                                {
                                    status = "ERRO",
                                    IDCliente = cliente.IDCliente,
                                    erro = "Agente CCEE, Gateway, Grupo de Unidades, Unidade e Medição do Cliente não adicionados."
                                };
                            }

                            // le cookies
                            LeCookies_SmartEnergy();

                            // lista de clientes habilitados para o consultor
                            string ConfigCli = "";

                            // leio TODOS clientes deste consultor
                            List<ClientesDominio> clientes = clientesMetodos.ListarPorIDConsultor(IDConsultor, 0);

                            // verifica se tem cliente
                            if (clientes.Count > 0)
                            {
                                // percorre todos os clientes e monta string
                                foreach (ClientesDominio client in clientes)
                                {
                                    ConfigCli = ConfigCli + "/" + client.IDCliente.ToString();
                                }

                                // fecha barra
                                ConfigCli = ConfigCli + "/";
                            }
                            else
                            {
                                ConfigCli = "/-10/";
                            }

                            // salva cookie
                            CookieStore.SalvaCookie_String("ConfigCli", ConfigCli);
                        }
                    }
                    else
                    {
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            IDCliente = cliente.IDCliente,
                            erro = "Cliente não encontrado."
                        };
                    }
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // crio empresa, gateway, grupo de unidades, unidade e medição inicial
        private bool Crio_Empresa_Gateway_Unidades_Medicao_Inicial(ClientesDominio cliente)
        {
            //
            // EMPRESA AGENTE CCEE
            //

            // empresa agente CCEE
            EmpresasMetodos empresaMetodos = new EmpresasMetodos();
            EmpresasDominio empresa = new EmpresasDominio();

            // zera empresa com default
            empresa.IDEmpresa = 0;
            empresa.IDTipoEmpresa = TIPO_EMPRESA.AgenteCCEE;

            empresa.IDCliente = cliente.IDCliente;
            empresa.RazaoSocial = cliente.Nome;
            empresa.SiglaCCEE = cliente.Fantasia;
            empresa.Logo = cliente.Logo;

            empresa.Senha_Atendimento = "";
            empresa.Senha_Representante = "";

            empresa.CNPJ = "00.000.000/0000-00";

            empresa.Endereco = "";
            empresa.CEP = "";
            empresa.IDEstado = 26;          // sao paulo
            empresa.IDCidade = 5270;        // sao paulo
            empresa.Latitude = 0.0;
            empresa.Longitude = 0.0;

            empresa.IDCCEE = 0;

            empresa.IDPerfil = "";
            empresa.Agente = 0;

            empresa.DataMigracao_Previsao = new DateTime(2000, 1, 1, 0, 0, 0);
            empresa.DataMigracao_Previsao_Texto = empresa.DataMigracao_Previsao.ToString("d");
            empresa.DataMigracao = new DateTime(2000, 1, 1, 0, 0, 0);
            empresa.DataMigracao_Texto = empresa.DataMigracao.ToString("d");

            empresa.Contrato_Status = 0;
            empresa.Contrato_Gestora = 1;   // CPFL Soluções
            empresa.Carteira = 0;
            empresa.Escopo = 0;
            empresa.Score = 0;
            empresa.Contrato_Inicio = new DateTime(2000, 1, 1, 0, 0, 0);
            empresa.Contrato_Inicio_Texto = empresa.Contrato_Inicio.ToString("d");
            empresa.Contrato_Fim = new DateTime(2000, 1, 1, 0, 0, 0);
            empresa.Contrato_Fim_Texto = empresa.Contrato_Fim.ToString("d");
            empresa.Base_Valor = 0;
            empresa.Base_Data = new DateTime(2000, 1, 1, 0, 0, 0);
            empresa.Base_Data_Texto = empresa.Base_Data.ToString("MM/yyyy");

            empresa.Reajuste_Mes = 0;
            empresa.Reajuste_Indice = 0;
            empresa.Reajuste_Valor = 0;

            empresa.Vencimento_Tipo = 0;
            empresa.Vencimento_Dias = 0;
            empresa.RenovacaoAutomatica = false;

            empresa.FechamentoTR = new DateTime(2000, 1, 1, 0, 0, 0);
            empresa.FechamentoTR_Texto = empresa.FechamentoTR.ToString("d");

            empresa.MesRescisao = new DateTime(2000, 1, 1, 0, 0, 0);
            empresa.MesRescisao_Texto = empresa.MesRescisao.ToString("MM/yyyy");
            empresa.UltimoMesFaturamento = new DateTime(2000, 1, 1, 0, 0, 0);
            empresa.UltimoMesFaturamento_Texto = empresa.UltimoMesFaturamento.ToString("MM/yyyy");

            empresa.Observacao = "";

            // cria empresa
            empresaMetodos.Salvar(empresa);

            // leio empresa novamente para recuperar IDEmpresa
            EmpresasDominio empresa_lida = empresaMetodos.ListarPorNome(empresa.IDCliente, empresa.RazaoSocial, empresa.SiglaCCEE);

            if (empresa_lida == null)
            {
                // erro
                return (false);
            }

            // evento
            UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.EMPRESA, empresa_lida.IDEmpresa);


            //
            // GATEWAY
            //

            // gateway
            GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = new GatewaysDominio();

            // zera gateway com default
            gateway.IDGateway = 0;
            gateway.IDCliente = cliente.IDCliente;
            gateway.Nome = string.Format("Gateway {0}", empresa_lida.SiglaCCEE);
            gateway.ProjetoNro = "";
            gateway.IDEmpresa = empresa_lida.IDEmpresa;
            gateway.IDTipoGateway = TIPO_GATEWAY.SCDE;      // SCDE
            gateway.IDTipoDCE = TIPO_DCE_GATEWAY.Ethernet;  // Ethernet
            gateway.IDTipoTempo = TIPO_TEMPO_GATEWAY.SCDE;  // SCDE (Ignorar falha upload)
            gateway.AcessoRemoto = false;

            // cria gateway
            gatewayMetodos.Salvar(gateway);

            // leio gateway novamente para recuperar IDGateway
            GatewaysDominio gateway_lida = gatewayMetodos.ListarPorNomeIDCliente(gateway.IDCliente, gateway.Nome);

            if (gateway_lida == null)
            {
                // erro
                return (false);
            }

            // evento 
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.GATEWAY, gateway_lida.IDGateway);


            //
            // GRUPO DE UNIDADES
            //

            // grupo de unidades
            GrupoUnidadesMetodos grupoUnidadesMetodos = new GrupoUnidadesMetodos();
            GrupoUnidadesDominio grupoUnidades = new GrupoUnidadesDominio();

            // zera grupo de unidades com default
            grupoUnidades.IDGrupoUnidades = 0;
            grupoUnidades.IDCliente = cliente.IDCliente;
            grupoUnidades.Nome = "Grupo de Unidades";

            // cria grupo de unidades
            grupoUnidadesMetodos.Salvar(grupoUnidades);

            // leio grupo de unidades novamente para recuperar IDGrupoUnidades
            GrupoUnidadesDominio grupoUnidades_lida = grupoUnidadesMetodos.ListarPorIDClienteNome(grupoUnidades.IDCliente, grupoUnidades.Nome);

            if (grupoUnidades_lida == null)
            {
                // erro
                return (false);
            }

            // evento
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.GRUPO, grupoUnidades_lida.IDGrupoUnidades);


            //
            // UNIDADE
            //

            // unidade
            UnidadesMetodos unidadesMetodos = new UnidadesMetodos();
            UnidadesDominio unidade = new UnidadesDominio();

            // zera unidade com default
            unidade.IDUnidade = 0;
            unidade.IDGrupoUnidades = grupoUnidades_lida.IDGrupoUnidades;
            unidade.IDCliente = cliente.IDCliente;
            unidade.Nome = "Unidade";

            // cria unidade
            unidadesMetodos.Salvar(unidade);

            // leio unidade novamente para recuperar IDUnidade
            UnidadesDominio unidade_lida = unidadesMetodos.ListarPorIDClienteNome(unidade.IDCliente, unidade.Nome);

            if (unidade_lida == null)
            {
                // erro
                return (false);
            }

            // evento
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.UNIDADE, unidade_lida.IDUnidade);


            //
            // MEDIÇÂO
            //

            // medição
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = new MedicoesDominio();

            // zera medicao com default
            medicao.IDMedicao = 0;
            medicao.IDCliente = cliente.IDCliente;

            medicao.IDUnidade = unidade_lida.IDUnidade;
            medicao.Nome = string.Format("Medição {0}", empresa_lida.SiglaCCEE);

            medicao.IDTipoMedicao = TIPO_MEDICAO.ENERGIA;       // Energia Eletrica
            medicao.IDTipoSubMedicao = 1;                       // CARGA
            
            medicao.Referencia = "";
            medicao.PontoMedicao = "";
            medicao.CodigoInstalacao = "";

            medicao.IDGateway = gateway_lida.IDGateway;
            medicao.NumMedGateway = 0;

            medicao.IDGateway_Fechamentos = 0;      // utilizar a própria gateway para gerar os fechamentos

            medicao.IDDeslocamento = 0;             // 00:00
            medicao.IDCicloMes = 0;                 // Mes Civil
            medicao.IDTipoUnidadePotencia = 1;      // kW

            medicao.IDContratoMedicao = 0;          // Cativo
            medicao.IDTipoClasseAtivo = 1;          // Consumidor Varejista
            medicao.IDEstruturaTarifaria = 1;       // THS Verde
            medicao.IDTipoSubgrupo = 5;             // A4
            medicao.IDSubgrupo = 7;                 // THS Verde A4
            medicao.IDSubSistema = 0;               // Sudeste/Centro-Oeste

            medicao.IDAgenteDistribuidora = 38;     // CPFL Paulista
            medicao.IDGrupoTarifaMercadoLivre = 0;  // utilizar historico de tarifas de mercado livre da medicao

            medicao.IDRegraDem = 5;                 // res. 414 com a regra de proporcionalidade
            medicao.IDRegraReativo = 0;             // com medicao apropriada
            medicao.IDRegraICMS = 1;                // sobre demanda registrada
            medicao.IDCalculoICMS = 0;              // por dentro

            medicao.InicioP = "17:30";
            medicao.FimP = "20:30";
            medicao.DomP = false; medicao.SegP = true; medicao.TerP = true; medicao.QuaP = true; medicao.QuiP = true; medicao.SexP = true; medicao.SabP = false; medicao.FerP = false;
            medicao.InicioPC = "00:30";
            medicao.FimPC = "06:30";
            medicao.DomPC = true; medicao.SegPC = true; medicao.TerPC = true; medicao.QuaPC = true; medicao.QuiPC = true; medicao.SexPC = true; medicao.SabPC = true; medicao.FerPC = true;
            medicao.InicioR = "21:30";
            medicao.FimR = "06:00";
            medicao.DomR = true; medicao.SegR = true; medicao.TerR = true; medicao.QuaR = true; medicao.QuiR = true; medicao.SexR = true; medicao.SabR = true; medicao.FerR = true;

            medicao.ToleranciaDemP = 5;
            medicao.ToleranciaDemFP = 5;

            medicao.Formula = "";
            medicao.NomeGrandeza = "";
            medicao.UnidadeGrandeza = "";
            medicao.IntervaloGrandeza = 0;
            medicao.FEGrandeza = 65535;

            medicao.IDIcone = 0;

            medicao.Dem_P_pcH = 0.0;
            medicao.Dem_F_pcH = 0.0;
            medicao.Dem_P_pcL = 0.0;
            medicao.Dem_F_pcL = 0.0;
            medicao.FP_LimInd = 0.0;
            medicao.FP_LimCap = 0.0;
            medicao.FP_LimCap = 0;
            medicao.DiasNCnPj = 0;
            medicao.CnPjP_pcH = 0.0;
            medicao.CnPjF_pcH = 0.0;
            medicao.CnPjP_pcL = 0.0;
            medicao.CnPjF_pcL = 0.0;

            medicao.Funcionamento_DemMin = 0.0;
            medicao.Funcionamento_Inicio = "08:00";
            medicao.Funcionamento_Fim = "18:00";

            medicao.IDEstado = 26;

            medicao.DataContrato = new DateTime(2000, 1, 1, 0, 0, 0);
            medicao.DataContratoTexto = medicao.DataContrato.ToString("d");
            medicao.ContratoDemP = 0;
            medicao.ContratoDemFP = 0;

            medicao.DataICMS = new DateTime(2000, 1, 1, 0, 0, 0);
            medicao.DataICMSTexto = medicao.DataICMS.ToString("d");
            medicao.valorICMS = 18;

            medicao.KPI_RefConsumo = 0.0;
            medicao.KPI_RefConsumo_Inicio = "08:00";
            medicao.KPI_RefConsumo_Fim = "18:00";
            medicao.KPI_RefConsumo_Dom = true; medicao.KPI_RefConsumo_Seg = true; medicao.KPI_RefConsumo_Ter = true; medicao.KPI_RefConsumo_Qua = true; medicao.KPI_RefConsumo_Qui = true; medicao.KPI_RefConsumo_Sex = true; medicao.KPI_RefConsumo_Sab = true; medicao.KPI_RefConsumo_Fer = true;

            medicao.KPI_PotenciaInstalada = 0.0;
            medicao.KPI_Area = 0.0;
            medicao.KPI_IDHistTemperatura = 0;
            medicao.KPI_IDHistVolume = 0;

            // cria medição
            medicoesMetodos.Salvar(medicao);

            // leio medição novamente para recuperar IDMedicao
            MedicoesDominio medicao_lida = medicoesMetodos.ListaMedicaoAdicionada(medicao);

            if (medicao_lida == null)
            {
                // erro
                return (false);
            }

            // supervisão das medições - cria medição
            MedicoesSupervMetodos medSupervMetodos = new MedicoesSupervMetodos();
            medSupervMetodos.Atualizar_SupervisaoMedicao(medicao_lida.IDMedicao);

            // evento
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.MEDICAO, medicao_lida.IDMedicao);


            // ok
            return (true);
        }

        // GET: Configuracao Cliente - Excluir
        public ActionResult Cliente_Excluir(int IDCliente)
        {
            // apaga todas as medicoes do cliente, que por sua vez apaga gateways, usuarios, etc.
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicoesMetodos.ListarPorIDCliente(IDCliente);

            // verifica se tem medicoes
            if( medicoes != null )
            {
                foreach(MedicoesDominio medicao in medicoes)
                {
                    // apaga medicao
                    ApagarMedicao(medicao.IDMedicao, 10);
                }
            }

            // apaga a empresa principal
            EmpresasMetodos empresaMetodos = new EmpresasMetodos();
            empresaMetodos.ExcluirCliente(IDCliente);

            // apaga os contatos da empresa
            EmpresasContatosMetodos empresaContatoMetodos = new EmpresasContatosMetodos();
            empresaContatoMetodos.ExcluirCliente(IDCliente);

            // apaga os contatos do cliente
            ContatosMetodos contatoMetodos = new ContatosMetodos();
            contatoMetodos.ExcluirCliente(IDCliente);

            // apaga USUARIO MEDICOES
            UsuarioMedicaoMetodos usuarioMedicaoMetodos = new UsuarioMedicaoMetodos();
            usuarioMedicaoMetodos.ExcluirTodosIDCliente(IDCliente);

            // apaga USUARIO
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            usuarioMetodos.ExcluirTodosIDCliente(IDCliente);

            // apaga o cliente
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            clientesMetodos.Excluir(IDCliente);

            // supervisão das medições - apaga cliente
            MedicoesSupervMetodos medSupervMetodos = new MedicoesSupervMetodos();
            medSupervMetodos.Atualizar_Cliente(IDCliente);

            // le cookies
            LeCookies_SmartEnergy();

            // verifica se cliente atual foi o excluido
            if( ViewBag._IDCliente == IDCliente)
            {
                // cliente nao mais selecionado
                CookieStore.SalvaCookie_Int("_IDCliente", -10);
                CookieStore.SalvaCookie_Int("IDTipoContrato", -10);
                CookieStore.SalvaCookie_Int("IDTipoSupervisao", -10);
                CookieStore.SalvaCookie_String("Nome_GrupoUnidades", "Grupo de Unidades");
                CookieStore.SalvaCookie_String("Nome_Unidades", "Unidades");
                CookieStore.SalvaCookie_Int("IDTipoGrafico_Demanda", 0);

                CookieStore.SalvaCookie_Int("_IDMedicao", -10);
                CookieStore.SalvaCookie_Int("IDTipoMedicao", -10);
                CookieStore.SalvaCookie_Int("_IDGateway", -10);
                CookieStore.SalvaCookie_Int("IDTipoGateway", -10);
            }

            // evento 
            UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.EXCLUI_CONFIG, TABELA_CONFIG.CLIENTE, IDCliente);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}