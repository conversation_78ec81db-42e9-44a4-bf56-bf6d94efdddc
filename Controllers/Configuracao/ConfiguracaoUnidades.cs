﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ConfiguracaoController
    {
        // Listas utilizadas
        private void PreparaListas_Unidade(int IDCliente)
        {
            // le grupounidades
            GrupoUnidadesMetodos grupounidadesMetodos = new GrupoUnidadesMetodos();
            List<GrupoUnidadesDominio> grupos = grupounidadesMetodos.ListarPorIDCliente(IDCliente);
            ViewBag.listaGrupos = grupos;

            return;
        }

        // GET: Configuracao Unidades
        public ActionResult Unidades(int IDCliente)
        {
            // tela de ajuda - unidades
            CookieStore.SalvaCookie_String("PaginaAjuda", "Configuracao_Unidades");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_Unidade(IDCliente);

            // lista de medicoes habilitadas
            List<int> ConfigMedList = ViewBag._ConfigMed;

            // le unidades
            UnidadesMetodos unidadesMetodos = new UnidadesMetodos();
            List<UnidadesDominio> listaUnidades = unidadesMetodos.ListarTodos(IDCliente, ConfigMedList);

            return View(listaUnidades);
        }

        // GET: Configuracao Unidades - Editar
        public ActionResult Unidades_Editar(int IDUnidade)
        {
            // tela de ajuda - unidades
            CookieStore.SalvaCookie_String("PaginaAjuda", "Configuracao_UnidadesEditar");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_Unidade(ViewBag._IDCliente);

            // verifica se adicionando
            UnidadesDominio unidade = new UnidadesDominio();
            if (IDUnidade == 0)
            {
                // zera unidade com default
                unidade.IDGrupoUnidades = -1;
                unidade.IDCliente = ViewBag._IDCliente;
            }
            else
            {
                // le unidade
                UnidadesMetodos unidadesMetodos = new UnidadesMetodos();
                unidade = unidadesMetodos.ListarPorId(ViewBag._IDCliente, IDUnidade);
            }

            return View(unidade);
        }

        // POST: Configuracao Unidades - Salvar
        [HttpPost]
        public ActionResult Unidades_Salvar(UnidadesDominio unidade)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outra unidade com o mesmo nome
            UnidadesMetodos unidadesMetodos = new UnidadesMetodos();
            if (unidadesMetodos.VerificarDuplicidade(unidade))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Unidade existente."
                };
            }
            else
            {
                // salva unidade
                unidadesMetodos.Salvar(unidade);

                // pego IDUnidade novamente (pois pode ter sido insercao)
                UnidadesDominio unid = unidadesMetodos.ListarPorIDClienteNome(unidade.IDCliente, unidade.Nome);

                // verifica se salvou
                if (unid != null)
                {
                    // supervisão das medições - atualiza unidade
                    MedicoesSupervMetodos medSupervMetodos = new MedicoesSupervMetodos();
                    medSupervMetodos.Atualizar_Unidade(unid.IDCliente, unid.IDUnidade);

                    // evento 
                    UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();

                    if (unidade.IDUnidade > 0)
                    {
                        usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.UNIDADE, unid.IDUnidade);
                    }
                    else
                    {
                        usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.UNIDADE, unid.IDUnidade);
                    }
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuracao Unidades - Excluir
        public ActionResult Unidades_Excluir(int IDCliente, int IDUnidade)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existem medições utilizando esta unidade
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();

            if (medicaoMetodos.NumMedicoesUnidade(IDCliente, IDUnidade) > 0)
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Esta Unidade está sendo utilizada pelas Medições.<br><br>Verificar as Medições antes de excluir."
                };
            }
            else
            {
                // apaga a unidade
                UnidadesMetodos unidadesMetodos = new UnidadesMetodos();
                unidadesMetodos.Excluir(IDCliente, IDUnidade);

                // supervisão das medições - atualiza unidade
                MedicoesSupervMetodos medSupervMetodos = new MedicoesSupervMetodos();
                medSupervMetodos.Atualizar_Unidade(IDCliente, IDUnidade);

                // evento 
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.EXCLUI_CONFIG, TABELA_CONFIG.UNIDADE, IDUnidade);
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }
    }
}