﻿using System;
using System.Web.Mvc;

using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Configuracao Data Hora
        public ActionResult GateE_DataHora(int IDGateway)
        {
            // tela de ajuda - medição de energia
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // receber configuração
            DateTime datahora = GateE_DataHora_Receber(IDGateway);

            // data e hora atual
            ViewBag.Data = string.Format("{0:d}", datahora);
            ViewBag.Hora = string.Format("{0:HH:mm:ss}", datahora);

            // prepara listas
            GateE_DataHora_PreparaListas(IDGateway);

            // retorna
            return View();
        }

        // Configuração Data Hora - Receber
        private DateTime GateE_DataHora_Receber(int IDGateway)
        {
            // data hora
            DateTime datahora = new DateTime(2000, 1, 1, 0, 0, 0);

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // status
            bool solicitaProg = false;

            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita versão
                if (solicitaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_DATAHORA, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // copia data hora recebida
                    datahora = smartCom.gateE.DataHora;
                }
            }

            // copia status
            ViewBag.SolicitaProg = solicitaProg;

            // retorna configuração
            return (datahora);
        }

        // prepara listas
        private void GateE_DataHora_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            return;
        }

        // POST: Configuracao Data Hora - Enviar
        [HttpPost]
        public ActionResult GateE_DataHora_Enviar(int IDGateway, string datahora)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // variavel de retorno
            bool enviaProg = false;

            // data hora
            DateTime programacaoDataHora = new DateTime(2000, 1, 1, 0, 0, 0);

            if ( DateTime.TryParse(datahora, out programacaoDataHora) )
            {
                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // copia data hora no envio
                    smartCom.gateE.DataHora = programacaoDataHora;

                    // envia data hora
                    enviaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_DATAHORA, SMCOM_TIPO_SOL.ENVIA);
                }

                // verifica retorno
                if (enviaProg)
                {
                    // caso OK insiro evento
                    UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, IDGateway);
                }
                else
                {
                    // erro
                    returnedData = new
                    {
                        status = "ERRO",
                        erro = "Falha no envio da configuração!"
                    };
                }
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Data e Hora incorreta!"
                };
            }

            // retorna status
            return Json(returnedData);
        }
    }
}