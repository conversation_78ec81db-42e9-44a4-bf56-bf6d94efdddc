﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Comando Limpar Histórico
        public ActionResult GateE_LimparHistorico(int IDGateway)
        {
            // tela de ajuda - comando limpar histórico
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            GateE_LimparHistorico_PreparaListas(IDGateway);

            // retorna
            return View();
        }

        // prepara listas
        private void GateE_LimparHistorico_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            return;
        }

        // POST: Comando Limpar Histórico - Enviar
        [HttpPost]
        public ActionResult GateE_LimparHistorico_Enviar(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // variavel de retorno
            bool enviaProg = false;

            // comunicação MQTT
            SmartCom smartCom = new SmartCom(IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // envia comando Envio Dados
                enviaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.COMANDO_LIMPAR_HIST_REINICIAR, SMCOM_TIPO_SOL.ENVIA);
            }

            // verifica retorno
            if (enviaProg)
            {
                // caso OK insiro evento
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.OPERACAO, TABELA_OPERACAO.LIMPAR_HISTORICO, IDGateway);
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Falha no envio do comando!"
                };

            }

            // retorna status
            return Json(returnedData);
        }
    }
}