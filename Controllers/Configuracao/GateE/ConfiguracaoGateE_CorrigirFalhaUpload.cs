﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Comando Corrigir Falha Upload
        public ActionResult GateE_CorrigirFalhaUpload(int IDGateway)
        {
            // tela de ajuda - comando reboot
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            GateE_CorrigirFalhaUpload_PreparaListas(IDGateway);

            // retorna
            return View();
        }

        // prepara listas
        private void GateE_CorrigirFalhaUpload_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // supervisão da gateway
            SupervGatewaysMetodos supervMetodos = new SupervGatewaysMetodos();
            SupervGatewaysDominio supervGateway = supervMetodos.ListarPorIDGateway(IDGateway);

            if (supervGateway != null)
            {
                ViewBag.VersaoEq = supervGateway.VersaoEq;
            }
            else
            {
                ViewBag.VersaoEq = "-";
            }

            return;
        }

        // POST: Comando Corrigir Falha Upload - Enviar
        [HttpPost]
        public ActionResult GateE_CorrigirFalhaUpload_Enviar(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // variavel de retorno
            bool enviaProg = false;

            // comunicação MQTT
            SmartCom smartCom = new SmartCom(IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // envia comando Corrigir Falha Upload
                enviaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.COMANDO_CORRIGIR_FALHA_UPLOAD, SMCOM_TIPO_SOL.ENVIA);
            }

            // verifica retorno
            if (enviaProg)
            {
                // caso OK insiro evento
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, IDGateway);
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Falha no envio do comando!"
                };

            }

            // retorna status
            return Json(returnedData);
        }
    }
}