﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Comando Envio Dados
        public ActionResult GateE_EnvioDados(int IDGateway)
        {
            // tela de ajuda - comando envio dados
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // receber comando
            GateE_EnvioDados_Dominio cmd = GateE_EnvioDados_Receber(IDGateway);

            // prepara listas
            GateE_EnvioDados_PreparaListas(IDGateway, cmd.tyUp);

            // retorna
            return View(cmd);
        }

        // Comando Envio Dados - Receber
        private GateE_EnvioDados_Dominio GateE_EnvioDados_Receber(int IDGateway)
        {
            // comando
            GateE_EnvioDados_Dominio cmd = new GateE_EnvioDados_Dominio();

            // default
            cmd.ultimoUpload = new DateTime(2000, 1, 1, 0, 0, 0);

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // status
            bool solicitaProg = false;

            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita comando
                if (solicitaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.COMANDO_UPLOAD, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // copia comando recebido
                    cmd = smartCom.gateE.EnvioDados;
                }
            }

            // default
            cmd.iniUpload = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 0, 0, 0);
            
            cmd.tyUp = 0x30;                // energia elétrica
            cmd.iMd = 0;                    // medição 0
            cmd.xUpldProt = 1;              // protocolo MQTT
            cmd.tyUpReg = 0;                // 96 registros

            // iniUpload
            ViewBag.Data = string.Format("{0:d}", cmd.ultimoUpload);
            ViewBag.Hora = string.Format("{0:HH:mm}", cmd.ultimoUpload);

            // copia status
            ViewBag.SolicitaProg = solicitaProg;

            // retorna configuração
            return (cmd);
        }

        // prepara listas
        private void GateE_EnvioDados_PreparaListas(int IDGateway, int tyUp)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // le tipo envio dados
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoEnvioDados = listatiposMetodos.ListarTodos("TipoGateX_EnvioDados", false, 0);
            ViewBag.listatipoEnvioDados = listatipoEnvioDados;

            // le tipo envio dados - registros
            List<ListaTiposDominio> listatipoEnvioDadosReg = listatiposMetodos.ListarTodos("TipoGateX_EnvioDadosReg", false, 0);
            ViewBag.listatipoEnvioDadosReg = listatipoEnvioDadosReg;

            // Obter Medicoes
            GateE_EnvioDados_ObterMedicoes(IDGateway, tyUp);

            return;
        }

        // POST: Comando Envio Dados - Enviar
        [HttpPost]
        public ActionResult GateE_EnvioDados_Enviar(GateE_EnvioDados_Dominio cmd)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(cmd.IDGateway);

            // variavel de retorno
            bool enviaProg = false;

            // verifica se FTP
            if (cmd.xUpldProt == 0)
            {
                cmd.tyUp = 1;
            }

            // verifica se MQTT e se deve enviar todos os registros
            if (cmd.xUpldProt == 1 && cmd.tyUpReg == 1)
            {
                cmd.iniUpload_DataTexto = "01/01/2020 00:00:00";
            }

            // data hora de envio
            DateTime datahora = new DateTime(2000, 1, 1, 0, 0, 0);

            if (DateTime.TryParse(cmd.iniUpload_DataTexto, out datahora))
            {
                // data hora inicio upload
                cmd.iniUpload = datahora;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // copia envio
                    smartCom.gateE.EnvioDados = cmd;

                    // envia comando Envio Dados
                    enviaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.COMANDO_UPLOAD, SMCOM_TIPO_SOL.ENVIA);
                }

                // verifica retorno
                if (enviaProg)
                {
                    // caso OK insiro evento
                    UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.OPERACAO, TABELA_OPERACAO.ENVIO_DADOS, cmd.IDGateway);
                }
                else
                {
                    // erro
                    returnedData = new
                    {
                        status = "ERRO",
                        erro = "Falha no envio da configuração!"
                    };

                }
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Data e Hora incorreta!"
                };
            }

            // retorna status
            return Json(returnedData);
        }

        //
        // MEDICOES
        //

        // GET: Obter Medicoes
        public JsonResult GateE_EnvioDados_ObterMedicoes(int IDGateway, int tyUp)
        {
            // medições
            List<ListaTiposDominio> listaMedicoes = new List<ListaTiposDominio>();

            // tipo da medição desejada
            switch (tyUp)
            {
                case 0x10:     // supervisão equipamento
                case 0x20:     // eventos
                    break;

                case 0x30:     // energia

                    // medições energia
                    GateE_MedEner_Metodos medEnerMetodos = new GateE_MedEner_Metodos();
                    List<GateE_MedEner_Dominio> medEner_Lista = medEnerMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.ATUAL);

                    // preenche lista de medições
                    for (int i = 0; i < SGATEE.NUM_MED_EN; i++)
                    {
                        // default
                        ListaTiposDominio tipo = new ListaTiposDominio();
                        tipo.ID = i;
                        tipo.Descricao = string.Format("[{0:00}] Medição de Energia Elétrica {1}", i, i);

                        // copia
                        if (medEner_Lista != null)
                        {
                            // verifica se tem na lista
                            GateE_MedEner_Dominio med = medEner_Lista.First(item => item.NumMedicaoGateway == i);

                            if (med != null)
                            {
                                tipo.Descricao = string.Format("[{0:00}] {1}", i, med.Descricao);
                            }
                        }

                        // insere na lista
                        listaMedicoes.Add(tipo);
                    }

                    break;

                case 0x31:     // utilidades

                    break;

                case 0x32:     // analogica

                    break;

                case 0x33:     // ciclometro 

                    break;
            }

            // lista de medições
            ViewBag.listaMedicoes = listaMedicoes;

            // retorna o valor em JSON
            return Json(listaMedicoes, JsonRequestBehavior.AllowGet);
        }
    }
}