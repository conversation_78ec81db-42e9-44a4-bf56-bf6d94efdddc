﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Configuração Medição de Energia
        public ActionResult GateE_MedEner(int IDGateway, int Origem = 0)
        {
            // tela de ajuda
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            <PERSON>mis<PERSON>();

            // receber configuração
            List<GateE_MedEner_Dominio> medicoesEnergia = GateE_MedEner_Receber(IDGateway, Origem);

            // prepara listas
            GateE_MedEner_PreparaListas(IDGateway);

            // retorna configuração
            return View(medicoesEnergia);
        }

        // Configuração Medição de Energia - Receber
        private List<GateE_MedEner_Dominio> GateE_MedEner_Receber(int IDGateway, int Origem)
        {
            // configuração medicoes de energia
            GateE_MedEner_Metodos medMetodos = new GateE_MedEner_Metodos();
            List<GateE_MedEner_Dominio> medicoesEnergia = new List<GateE_MedEner_Dominio>();

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // status
            bool solicitaProg = true;

            // verifica Origem
            // 0: ler da gateway
            // 1: ler do banco de dados atual
            // 2: ler do banco de dados editando
            if (Origem == 0)
            {
                // status
                solicitaProg = false;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // solicita programação de medicoes de energia
                    if (solicitaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_MED_EN, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // copia configuração da gateway para 'editando'
                        // caso ainda não exista configuração 'atual', será criada 
                        medicoesEnergia = medMetodos.CopiarParaEditando(IDGateway, smartCom.gateE.MedEner);
                    }
                }

                // verifica se não solicitou
                if (!solicitaProg)
                {
                    // copia configuração 'atual' para 'editando'
                    medicoesEnergia = medMetodos.CopiarParaEditando(IDGateway);
                }
            }
            else if (Origem == 1)
            {
                // copia configuração 'atual' para 'editando'
                medicoesEnergia = medMetodos.CopiarParaEditando(IDGateway);
            }
            else
            {
                // configuração em 'editando'
                medicoesEnergia = medMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            }


            // percorre medições
            foreach (GateE_MedEner_Dominio prg in medicoesEnergia)
            {
                // arredonda
                prg.mdK = Math.Round(prg.mdK, 5);
                prg.rtc = Math.Round(prg.rtc, 5);
                prg.rtp = Math.Round(prg.rtp, 5);

                // falta de pulsos
                prg.tFalP *= 10;
            }

            // copia status
            ViewBag.SolicitaProg = solicitaProg;

            // retorna configuração
            return (medicoesEnergia);
        }

        // GET: Configuração Medição de Energia - Editar
        public ActionResult GateE_MedEner_Editar(int IDGateway, int NumMedicaoGateway)
        {
            // tela de ajuda - medição de energia
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            GateE_MedEner_PreparaListas(IDGateway);

            // lê programação da medição (editando)
            GateE_MedEner_Metodos medMetodos = new GateE_MedEner_Metodos();
            GateE_MedEner_Dominio programacao = medMetodos.ListarPorNumMedicao(IDGateway, NumMedicaoGateway, STATUS_CFG.EDITANDO);

            // arredonda
            programacao.mdK = Math.Round(programacao.mdK, 5);
            programacao.rtc = Math.Round(programacao.rtc, 5);
            programacao.rtp = Math.Round(programacao.rtp, 5);

            // falta de pulsos
            programacao.tFalP *= 10;

            return View(programacao);
        }

        // prepara listas
        private void GateE_MedEner_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // tipos medidores de energia
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoMedidoresEner = listatiposMetodos.ListarTodos("TipoGateE_MedidoresEner", false, 0);

            // retirado da lista até ser implementado
 //           listatipoMedidoresEner.RemoveAll(x => x.ID == 2);      // CODI Fornecido
            listatipoMedidoresEner.RemoveAll(x => x.ID == 4);      // Contador [0]
            listatipoMedidoresEner.RemoveAll(x => x.ID == 8);      // Contador [1]
            listatipoMedidoresEner.RemoveAll(x => x.ID == 16);     // Rede de I/O

            ViewBag.listatipoMedidoresEner = listatipoMedidoresEner;

            // le tipos entrada reativa
            List<ListaTiposDominio> listaReativo = listatiposMetodos.ListarTodos("TipoGateE_Reativo", false, 0);
            ViewBag.listaReativo = listaReativo;

            // lista numero remotas
            int[] listaRemotas = new int[SGATEE.NUM_REMOTAS];

            for (int i = 0; i < listaRemotas.Length; i++)
            {
                listaRemotas[i] = i;
            }

            // copia lista remotas
            ViewBag.ListaRemotas = listaRemotas;

            return;
        }

        // POST: Configuração Medição de Energia - Programar
        [HttpPost]
        public ActionResult GateE_MedEner_Programar(GateE_MedEner_Dominio programacao)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };


            // arredonda
            programacao.mdK = Math.Round(programacao.mdK, 5);
            programacao.rtc = Math.Round(programacao.rtc, 5);
            programacao.rtp = Math.Round(programacao.rtp, 5);

            // falta de pulsos
            programacao.tFalP /= 10;

            // salva (editando)
            GateE_MedEner_Metodos medMetodos = new GateE_MedEner_Metodos();
            medMetodos.Salvar(programacao, STATUS_CFG.EDITANDO);

            // lista gateway para inserir evento
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(programacao.IDGateway);

            // evento 
            UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, gateway.IDGateway);

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Medição de Energia - Excluir
        public ActionResult GateE_MedEner_Excluir(int IDGateway, int NumMedicaoGateway)
        {
            // lista gateway para inserir evento                        
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // apaga programacao (editando e atual)
            GateE_MedEner_Metodos medMetodos = new GateE_MedEner_Metodos();
            medMetodos.ExcluirPorNumMedicao(IDGateway, NumMedicaoGateway, STATUS_CFG.EDITANDO);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // POST: Configuração Medição de Energia - Enviar
        [HttpPost]
        public ActionResult GateE_MedEner_Enviar(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // copia configuração 'editando' para 'atual'
            GateE_MedEner_Metodos medMetodos = new GateE_MedEner_Metodos();
            List<GateE_MedEner_Dominio> programacoesGateway = medMetodos.CopiarParaAtual(IDGateway);

            // variavel de retorno
            bool enviaProg = false;

            // verifica
            if (programacoesGateway != null && gateway != null)
            {
                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // copia para lista de envio
                    smartCom.gateE.MedEner = programacoesGateway;

                    // envia configuração
                    enviaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_MED_EN, SMCOM_TIPO_SOL.ENVIA);
                }
            }

            // verifica retorno
            if (enviaProg)
            {
                // excluir editando
                medMetodos.ExcluirEditando(IDGateway);

                // histórico de configuração
                HistoricoConfiguracoesDominio hist = new HistoricoConfiguracoesDominio();
                hist.IDGateway = IDGateway;
                hist.NomeConfiguracao = "GateE_MedEner";
                hist.Envio = "MQTT";
                hist.Atualizacao = DateTime.Now;
                hist.Versao = GateE_Versao_String(IDGateway);
                hist.IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");

                // insere histórico de configuração
                HistoricoConfiguracoesMetodos histMetodos = new HistoricoConfiguracoesMetodos();
                histMetodos.Inserir(hist);

                // caso OK insiro evento
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, IDGateway);
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Falha no envio da configuração!"
                };
            }

            // retorna status
            return Json(returnedData);
        }
    }
}