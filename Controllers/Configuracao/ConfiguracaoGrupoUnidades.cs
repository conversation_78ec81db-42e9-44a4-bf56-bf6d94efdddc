﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ConfiguracaoController
    {
        // GET: Configuracao GrupoUnidades
        public ActionResult GrupoUnidades(int IDCliente)
        {
            // tela de ajuda - grupo de unidades
            CookieStore.SalvaCookie_String("PaginaAjuda", "Configuracao_GruposUnidades");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // lista de medicoes habilitadas
            List<int> ConfigMedList = ViewBag._ConfigMed;

            // le grupounidades
            GrupoUnidadesMetodos grupounidadesMetodos = new GrupoUnidadesMetodos();
            List<GrupoUnidadesDominio> listaGrupos = grupounidadesMetodos.ListarTodos(IDCliente, ConfigMedList);

            return View(listaGrupos);
        }

        // GET: Configuracao GrupoUnidades - Editar
        public ActionResult GrupoUnidades_Editar(int IDGrupoUnidades)
        {
            // tela de ajuda - grupo de unidades
            CookieStore.SalvaCookie_String("PaginaAjuda", "Configuracao_GruposUnidadesEditar");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // verifica se adicionando
            GrupoUnidadesDominio grupo = new GrupoUnidadesDominio();
            if (IDGrupoUnidades == 0)
            {
                // zera grupounidades com default
                grupo.IDCliente = ViewBag._IDCliente;
            }
            else
            {
                // le grupounidades
                GrupoUnidadesMetodos grupounidadesMetodos = new GrupoUnidadesMetodos();
                grupo = grupounidadesMetodos.ListarPorId(ViewBag._IDCliente, IDGrupoUnidades);
            }

            return View(grupo);
        }

        // POST: Configuracao GrupoUnidades - Salvar
        [HttpPost]
        public ActionResult GrupoUnidades_Salvar(GrupoUnidadesDominio grupo)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro grupounidades com o mesmo nome
            GrupoUnidadesMetodos grupounidadesMetodos = new GrupoUnidadesMetodos();
            if (grupounidadesMetodos.VerificarDuplicidade(grupo))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Grupo de Unidades existente."
                };
            }
            else
            {
                // salva grupounidades 
                grupounidadesMetodos.Salvar(grupo);

                // pego IDGrupoUnidades novamente (pois pode ter sido insercao)
                GrupoUnidadesDominio gru = grupounidadesMetodos.ListarPorIDClienteNome(grupo.IDCliente, grupo.Nome);

                // verifica se salvou
                if (gru != null)
                {
                    // supervisão das medições - atualiza grupo de unidades
                    MedicoesSupervMetodos medSupervMetodos = new MedicoesSupervMetodos();
                    medSupervMetodos.Atualizar_GrupoUnidades(gru.IDCliente, gru.IDGrupoUnidades);

                    // evento 
                    UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();

                    if (grupo.IDGrupoUnidades > 0)
                    {
                        usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GRUPO, gru.IDGrupoUnidades);
                    }
                    else
                    {
                        usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.GRUPO, gru.IDGrupoUnidades);
                    }
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuracao GrupoUnidades - Excluir
        public ActionResult GrupoUnidades_Excluir(int IDCliente, int IDGrupoUnidades)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existem unidades utilizando este grupo de unidades
            UnidadesMetodos unidadeMetodos = new UnidadesMetodos();

            if (unidadeMetodos.NumUnidadesGrupoUnidades(IDCliente, IDGrupoUnidades) > 0)
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Este Grupo de Unidade está sendo utilizado pelas Unidades.<br><br>Verificar as Unidades antes de excluir."
                };
            }
            else
            {
                // apaga o grupounidades
                GrupoUnidadesMetodos grupounidadesMetodos = new GrupoUnidadesMetodos();
                grupounidadesMetodos.Excluir(IDCliente, IDGrupoUnidades);

                // supervisão das medições - atualiza grupo de unidades
                MedicoesSupervMetodos medSupervMetodos = new MedicoesSupervMetodos();
                medSupervMetodos.Atualizar_GrupoUnidades(IDCliente, IDGrupoUnidades);

                // evento 
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.EXCLUI_CONFIG, TABELA_CONFIG.GRUPO, IDGrupoUnidades);
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }
    }
}