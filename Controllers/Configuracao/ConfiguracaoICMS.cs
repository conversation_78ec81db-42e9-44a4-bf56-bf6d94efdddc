﻿using System;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ConfiguracaoController
    {
        // GET: Configuracao HistoricoICMS
        public ActionResult HistoricoICMS(int IDMedicao)
        {
            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le historicoICMS
            HistoricoICMSMetodos historicoMetodos = new HistoricoICMSMetodos();
            var listaHistorico = historicoMetodos.ListarPorIDMedicao(IDMedicao);
            return View(listaHistorico);
        }

        // GET: Configuracao HistoricoICMS - Editar
        public ActionResult HistoricoICMS_Editar(int IDICMS, int IDMedicao)
        {
            // pagina anterior
            ViewBag.returnUrl = Request.UrlReferrer;

            // ID ICMS
            ViewBag.IDICMS = IDICMS;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // verifica se adicionando
            HistoricoICMSDominio historico = new HistoricoICMSDominio();
            if (IDICMS == 0)
            {
                // zera com default
                historico.IDICMS = 0;
                historico.IDCliente = ViewBag._IDCliente;
                historico.IDMedicao = IDMedicao;
                historico.Data = DateTime.Now;
                historico.DataTexto = historico.Data.ToString("d");
                historico.valorICMS = 18.0;
                historico.ICMS_Demanda_P = true;
                historico.ICMS_Demanda_FP = true;
                historico.ICMS_Tusd_P = true;
                historico.ICMS_Tusd_FP = true;
            }
            else
            {
                // le historicoICMS
                HistoricoICMSMetodos historicoMetodos = new HistoricoICMSMetodos();
                historico = historicoMetodos.ListarPorID(IDICMS);
            }

            return View(historico);
        }

        // POST: Configuracao HistoricoICMS - Salvar
        [HttpPost]
        public ActionResult HistoricoICMS_Salvar(HistoricoICMSDominio historico)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // parse data
            historico.Data = DateTime.Parse(historico.DataTexto);

            // verifica se existe outro historico com a mesma data
            HistoricoICMSMetodos historicoMetodos = new HistoricoICMSMetodos();
            if (historicoMetodos.VerificarDuplicidade(historico))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "ICMS existente."
                };
            }
            else
            {
                // salva historicoICMS
                historicoMetodos.Salvar(historico);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuracao HistoricoICMS - Excluir
        public ActionResult HistoricoICMS_Excluir(int IDICMS)
        {
            // apaga o historicoICMS
            HistoricoICMSMetodos historicoMetodos = new HistoricoICMSMetodos();
            historicoMetodos.Excluir(IDICMS);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}