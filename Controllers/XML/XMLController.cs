﻿using System.Text;
using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public class XMLController : Controller
    {
        // Supervisao Gateway
        public void SupervisaoGateway(int IDCliente, int IDGateway, int IDUsuario)
        {
            StringBuilder xmlBuilder = new StringBuilder();

            // erro
            int status = 0;

            //
            // CLIENTE
            //

            // IDConsultor
            int IDConsultor = -1;

            // le cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cliente = clienteMetodos.ListarPorId(IDCliente);

            if (cliente != null)
            {
                // incrementa contador de status
                status += 1;

                // copia IDConsultor
                IDConsultor = cliente.IDConsultor;
            }

            //
            // GATEWAY
            //

            // leio supervisao gateway
            SupervGatewaysMetodos gatewaysMetodos = new SupervGatewaysMetodos();
            SupervGatewaysDominio gateway = new SupervGatewaysDominio();
            gateway = gatewaysMetodos.ListarPorIDGateway(IDGateway);

            if (gateway != null)
            {
                if (gateway.IDGateway > 0)
                {
                    // verifica se gateway pertence ao cliente solicitado
                    if (gateway.IDCliente == IDCliente)
                    {
                        // incrementa contador de status
                        status += 1;
                    }
                }
            }

            //
            // USUARIO
            //

            // le usuario
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = usuarioMetodos.ListarPorId(IDUsuario);

            if (usuario != null)
            {
                // verifica se usuario eh admin GESTAL
                if (isUser.isGESTAL(usuario.IDTipoAcesso))
                {
                    // incrementa contador de status
                    status += 1;
                }

                // verifica se usuario eh consultor
                if (usuario.IDTipoAcesso == 3)
                {
                    // verifica se o consultor do cliente solicitado eh este consultor
                    if (IDConsultor == IDUsuario)
                    {
                        // incrementa contador de status
                        status += 1;
                    }
                }
            }

            // verifica se contador de status deu OK pra todos os testes
            if (status == 3)
            {
                // IP
                int pos = gateway.StatusEq.IndexOf("IP=");

                if (pos >= 0)
                {
                    int pos2 = gateway.StatusEq.IndexOf(' ');

                    if (pos2 > 3)
                    {
                        string str = gateway.StatusEq.Substring(pos + 3, gateway.StatusEq.IndexOf(' ') - 3);
                        gateway.IP = str;
                    }
                    else
                    {
                        gateway.IP = "-";
                    }
                }
                else
                {
                    gateway.IP = "-";
                }

                // sinal
                pos = gateway.StatusEq.IndexOf("SQ=");

                if (pos >= 0)
                {
                    string str = gateway.StatusEq.Substring(pos + 3);
                    double sinal = double.Parse(str);

                    if (sinal >= 99.0)
                    {
                        sinal = 0.0;
                    }
                    else
                    {
                        sinal = (sinal / 31.0) * 100.0;
                    }

                    if (sinal <= 0.0)
                    {
                        gateway.Sinal = 0;
                    }
                    else
                    {
                        gateway.Sinal = (int)sinal;
                    }
                }
                else
                {
                    gateway.Sinal = 0;
                }

                // monta XML com supervisao da gateway
                xmlBuilder.Append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
                xmlBuilder.Append("<Gateway>");
                xmlBuilder.Append("<Status>");
                xmlBuilder.Append("OK");
                xmlBuilder.Append("</Status>");

                xmlBuilder.Append("<IDGateway>");
                xmlBuilder.Append(string.Format("{0}", IDGateway));
                xmlBuilder.Append("</IDGateway>");

                xmlBuilder.Append("<Nome>");
                xmlBuilder.Append(gateway.Nome);
                xmlBuilder.Append("</Nome>");

                xmlBuilder.Append("<Modelo>");
                xmlBuilder.Append(string.Format("{0} ({1})", gateway.ModeloEq, gateway.VersaoEq));
                xmlBuilder.Append("</Modelo>");

                xmlBuilder.Append("<Atualizacao>");
                xmlBuilder.Append(string.Format("{0:G}", gateway.DataHora));
                xmlBuilder.Append("</Atualizacao>");

                xmlBuilder.Append("<DataEq>");
                xmlBuilder.Append(string.Format("{0:G}", gateway.DataEq));
                xmlBuilder.Append("</DataEq>");

                xmlBuilder.Append("<IP>");
                xmlBuilder.Append(gateway.IP);
                xmlBuilder.Append("</IP>");

                xmlBuilder.Append("<Sinal>");
                xmlBuilder.Append(gateway.Sinal);
                xmlBuilder.Append("</Sinal>");
                xmlBuilder.Append("</Gateway>");

                Response.ContentType = "text/xml";

                Response.BinaryWrite(Encoding.UTF8.GetBytes(xmlBuilder.ToString()));
                Response.End();
            }
            else
            {
                // monta XML ERRO
                xmlBuilder.Append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
                xmlBuilder.Append("<Gateway>");
                xmlBuilder.Append("<Status>");
                xmlBuilder.Append("Erro");
                xmlBuilder.Append("</Status>");
                xmlBuilder.Append("<IDGateway>");
                xmlBuilder.Append(string.Format("{0}", IDGateway));
                xmlBuilder.Append("</IDGateway>");
                xmlBuilder.Append("</Gateway>");

                Response.ContentType = "text/xml";

                Response.BinaryWrite(Encoding.UTF8.GetBytes(xmlBuilder.ToString()));
                Response.End();
            }

            return;
        }
    }
}