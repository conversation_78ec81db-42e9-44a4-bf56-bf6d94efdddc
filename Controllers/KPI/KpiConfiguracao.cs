﻿using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class KpiController
    {

        // GET: KPI - Configuracao
        public ActionResult KPI_Configuracao(int IDCliente)
        {
            // tela de ajuda - KPI
            CookieStore.SalvaCookie_String("PaginaAjuda", "KPI_Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_KPI();

            // le KPIs
            KPIConfiguracaoMetodos kpiMetodos = new KPIConfiguracaoMetodos();
            List<KPIConfiguracaoDominio> listaKPIs = kpiMetodos.ListarPorIDCliente(IDCliente);

            return View(listaKPIs);
        }

        // GET: KPI - Configuracao - Editar
        public ActionResult KPI_Configuracao_Editar(int idKPI)
        {
            int IDCliente = 0;

            // tela de ajuda - KPI
            CookieStore.SalvaCookie_String("PaginaAjuda", "KPI_Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_KPI();

            // verifica se adicionando
            KPIConfiguracaoDominio kpi = new KPIConfiguracaoDominio();
            if (idKPI == 0)
            {
                // zera KPI com default
                kpi.IDKPI = 0;
                kpi.IDCliente = ViewBag._IDCliente;
                kpi.Nome = "";
                kpi.UnidadeGrandeza = "";
                kpi.IDTipoRazao = 0;
                kpi.IDTipoHistorico = 0;
                kpi.ValorFixo = 0.0;

                // IDCliente
                IDCliente = ViewBag._IDCliente;
            }
            else
            {
                // le KPI
                KPIConfiguracaoMetodos kpiMetodos = new KPIConfiguracaoMetodos();
                kpi = kpiMetodos.ListarPorId(idKPI);

                // IDCliente
                IDCliente = kpi.IDCliente;
            }

            // lista de medicoes habilitadas do usuario
            List<int> ConfigMedicaoList_Usuario = new List<int>();
            ConfigMedicaoList_Usuario = ViewBag._ConfigMed;

            // verifica se existe lista de medicoes
            if (ConfigMedicaoList_Usuario != null)
            {
                if (ConfigMedicaoList_Usuario.Count() == 0)
                {
                    ConfigMedicaoList_Usuario = null;
                }
            }

            // le medicoes do KPI
            KPIGruposMedMetodos kpiGrupoMetodos = new KPIGruposMedMetodos();
            List<KPIGruposMedDominio> kpiGrupos = kpiGrupoMetodos.ListarPorIDKPI(kpi.IDKPI);

            // cria lista de medicoes deste KPI
            List<int> ConfigMedicaoList_KPI = new List<int>();

            foreach (KPIGruposMedDominio kpiGrupo in kpiGrupos)
            {
                ConfigMedicaoList_KPI.Add(kpiGrupo.IDMedicao);
            }

            // le medicoes configuradas do KPI para lista
            CliGateGrupoUnidMedicoesMetodos medicoesMetodos = new CliGateGrupoUnidMedicoesMetodos();
            List<CliGateGrupoUnidMedicoesDominio> medicoes = new List<CliGateGrupoUnidMedicoesDominio>();

            List<CliGateGrupoUnidMedicoesDominio> medicoes_utilizadas = new List<CliGateGrupoUnidMedicoesDominio>();
            List<CliGateGrupoUnidMedicoesDominio> medicoes_nao_utilizadas = new List<CliGateGrupoUnidMedicoesDominio>();

            if (ConfigMedicaoList_KPI != null)
            {
                // le medicoes de energia habilitadas para o usuario no cliente
                medicoes = medicoesMetodos.ListarPorIDCliente(IDCliente, ConfigMedicaoList_Usuario, 0);

                // seleciona medicoes 
                int contador;

                // percorre lista
                if (medicoes != null)
                {
                    for (contador = 0; contador < medicoes.Count(); contador++)
                    {
                        // verifica se existe lista de medicoes do KPI
                        if (ConfigMedicaoList_KPI != null)
                        {
                            // verifica se medicao esta habilitada para o KPI
                            if (ConfigMedicaoList_KPI.Contains(medicoes[contador].IDMedicao))
                            {
                                // copia medicao na lista de utilizadas
                                medicoes_utilizadas.Add(medicoes[contador]);
                            }
                            else
                            {
                                // copia medicao na lista de nao utilizadas
                                medicoes_nao_utilizadas.Add(medicoes[contador]);
                            }
                        }
                        else
                        {
                            // copia medicao na lista de nao utilizadas
                            medicoes_nao_utilizadas.Add(medicoes[contador]);
                        }
                    }
                }
            }
            ViewBag.Medicoes = medicoes_utilizadas;
            ViewBag.Medicoes2 = medicoes_nao_utilizadas;
            ViewBag.kpiGrupos = kpiGrupos;

            return View(kpi);
        }


        // POST: KPI - Configuracao - Salvar
        [HttpPost]
        public ActionResult KPI_Configuracao_Salvar(KPIConfiguracaoDominio kpi, List<KPIGruposMedDominio> kpiGrupos)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro KPI com o mesmo nome
            KPIConfiguracaoMetodos kpiMetodos = new KPIConfiguracaoMetodos();
            if (kpiMetodos.VerificarDuplicidade(kpi))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "KPI existente."
                };
            }
            else
            {
                // salva KPI
                kpiMetodos.Salvar(kpi);

                // evento 
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();

                if (kpi.IDKPI > 0)
                {
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.KPI, kpi.IDKPI);
                }
                else
                {
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.KPI, kpi.IDKPI);
                }

                // pego IDKPIg novamente (pois pode ter sido insercao)
                KPIConfiguracaoDominio kpiConfig = kpiMetodos.ListarPorNomeUnidade(kpi.IDCliente, kpi.Nome, kpi.UnidadeGrandeza);

                // salva kpiGrupo
                if (kpiConfig != null)
                {
                    KPIGruposMedMetodos kpiGruposMetodos = new KPIGruposMedMetodos();

                    // exclui todos as medicoes deste KPI
                    kpiGruposMetodos.Excluir(kpiConfig.IDKPI);

                    // salva rankingGrupo
                    if (kpiGrupos != null)
                    {
                        // percorre lista e salva
                        foreach (KPIGruposMedDominio kpiGrupo in kpiGrupos)
                        {
                            // preenche campos que faltam
                            kpiGrupo.IDKPI = kpiConfig.IDKPI;

                            // salva
                            kpiGruposMetodos.Salvar(kpiGrupo);
                        }
                    }
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: KPI - Configuracao - Excluir
        public ActionResult KPI_Configuracao_Excluir(int idKPI)
        {
            // exclui KPI
            KPIConfiguracaoMetodos kpiMetodos = new KPIConfiguracaoMetodos();
            kpiMetodos.Excluir(idKPI);

            KPIGruposMedMetodos kpiGruposMetodos = new KPIGruposMedMetodos();
            kpiGruposMetodos.Excluir(idKPI);

            // evento 
            UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.EXCLUI_CONFIG, TABELA_CONFIG.KPI, idKPI);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // permissoes
        private void Permissoes()
        {
            // permissoes
            // 0 - permissao de Admin: ve e escreve em tudo
            // 1 - permissao de Gerente: ve tudo e escreve parte
            // 2 - permissao de Operador: ve tudo e nao pode escrever
            // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)
            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
            // 5 - permissao de Consultor: ve tudo e escreve parte 
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            switch (IDTipoAcesso)
            {
                case TIPO_ACESSO.MASTER:            // master
                case TIPO_ACESSO.GESTAL_ADMIN:      // admin

                    ViewBag.Permissao = PERMISSOES.ADMIN;
                    break;

                case TIPO_ACESSO.CONSULTOR:         // consultor
                case TIPO_ACESSO.CONSULTOR_ADMIN:   // consultor - administrador

                    ViewBag.Permissao = PERMISSOES.CONSULTOR;
                    break;

                case TIPO_ACESSO.CLIENTE_ADMIN:     // cliente - administrador

                    ViewBag.Permissao = PERMISSOES.GERENTE;
                    break;

                default:
                case TIPO_ACESSO.CLIENTE_OPER:      // operador
                case TIPO_ACESSO.GESTAL_VENDAS:     // vendas
                case TIPO_ACESSO.CONSULTOR_OPER:    // consultor - operador
                case TIPO_ACESSO.DEMONSTRACAO:      // demo

                    ViewBag.Permissao = PERMISSOES.OPERADOR;
                    break;

                case TIPO_ACESSO.GESTAL_PRODUCAO:   // producao

                    ViewBag.Permissao = PERMISSOES.PRODUCAO;
                    break;

                case TIPO_ACESSO.GESTAL_SUPORTE:    // suporte

                    ViewBag.Permissao = PERMISSOES.SUPORTE;
                    break;
            }
        }

        // Listas utilizadas
        private void PreparaListas_KPI()
        {
            // le tipos Razao
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposRazao = listatiposMetodos.ListarTodos("TipoRazao");
            ViewBag.listaTipoRazao = listatiposRazao;

            // le tipos Historico
            List<ListaTiposDominio> listatiposHistorico = listatiposMetodos.ListarTodos("TipoHistorico");
            ViewBag.listaTipoHistorico = listatiposHistorico;

            return;
        }
    }
}