﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.OleDb;
using System.IO;
using System.Web;
using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class KpiController
    {
        #region Actions

        /// <summary>
        /// Uploads the file.
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public virtual ActionResult UploadFile(int IDCliente, int IDKPI)
        {
            // arquivo enviado
            HttpPostedFileBase myFile = Request.Files["fileupload"];

            // inicia variaveis
            bool isUploaded = false;
            string message = "<PERSON>vio falhou";

            if (myFile != null && myFile.ContentLength != 0)
            {
                string pathForSaving = Server.MapPath("~/HistKPI");
                if (this.CreateFolderIfNeeded(pathForSaving))
                {
                    try
                    {
                        // nome do arquivo
                        string nome_arq = myFile.FileName;

                        // nome do caminho e arquivo
                        string caminho_arq = Path.Combine(pathForSaving, myFile.FileName);

                        // extensão através dos ultimos 4 caracteres
                        string extensao_arq = nome_arq.Substring(nome_arq.Length - 4).ToLower();

                        // verifica se arquivo existe e apaga
                        FileInfo file = new FileInfo(caminho_arq);

                        if( file.Exists )
                        {
                            // deleta
                            file.Delete();
                        }

                        // salva arquivo
                        myFile.SaveAs(caminho_arq);

                        // flag erro
                        bool OcorreuErro = false;
                        string MensagemErro = "Formato não permitido";

                        // historico
                        List<KPIHistoricoDominio> HistKPI = new List<KPIHistoricoDominio>();

                        // trata arquivo EXCEL
                        if( extensao_arq == ".xls" || extensao_arq == "xlsx")
                        {
                            // ler registros
                            HistKPI = LerExcel(IDCliente, IDKPI, caminho_arq, ref MensagemErro);

                            if (HistKPI == null || MensagemErro != "OK")
                            {
                                // falha
                                OcorreuErro = true;
                                message = string.Format("Envio falhou: {0}", MensagemErro);
                            }
                        }
                        else
                        {
                            // flag erro
                            OcorreuErro = true;
                            MensagemErro = "Formato não permitido";
                        }

                        // verifica se nao ocorreu erro de leitura
                        if ( !OcorreuErro )
                        {
                            // enviado, lido e atualizado BD com sucesso
                            isUploaded = true;
                            message = "Arquivo enviado com sucesso!";
                        }
                    }
                    catch (Exception ex)
                    {
                        // falha
                        message = string.Format("Envio falhou: {0}", ex.Message);
                    }
                }
            }
            return Json(new { isUploaded = isUploaded, message = message }, "text/html");
        }

        public List<KPIHistoricoDominio> LerExcel(int IDCliente, int IDKPI, string caminho_arq, ref string MensagemErro)
        {
            // conexao
            // baixar o provedor de acesso Excel 2010 em "https://www.microsoft.com/en-us/download/details.aspx?id=13255"
            // AccessDatabaseEngine_X64.exe
            // configurando no SQL SERVER https://www.dirceuresende.com/blog/sql-server-como-instalar-os-drivers-microsoft-ace-oledb-12-0-e-microsoft-jet-oledb-4-0/
            OleDbConnection _olecon;
            OleDbCommand _oleCmd;
            //String _StringConexao = String.Format(@"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={0};Extended Properties='Excel 12.0;HDR=YES;ReadOnly=False';", caminho_arq);
            String _StringConexao = String.Format(@"Provider=Microsoft.JET.OLEDB.4.0;Data Source={0};Extended Properties='Excel 8.0;HDR=YES;';", caminho_arq);

            // historico
            KPIHistoricoMetodos kpiMetodos = new KPIHistoricoMetodos();
            List<KPIHistoricoDominio> HistKPI = new List<KPIHistoricoDominio>();

            // le Excel
            try
            {
                // conecta
                _olecon = new OleDbConnection(_StringConexao);
                _olecon.Open();

                _oleCmd = new OleDbCommand();
                _oleCmd.Connection = _olecon;
                _oleCmd.CommandType = CommandType.Text;

                // le excel
                _oleCmd.CommandText = "SELECT * FROM [KPI$]";
                OleDbDataReader reader = _oleCmd.ExecuteReader();

                while (reader.Read())
                {
                    // le registro
                    KPIHistoricoDominio kpi = new KPIHistoricoDominio();
                    kpi.IDHistKPI = 0;
                    kpi.IDCliente = IDCliente;
                    kpi.IDKPI = IDKPI;
                    kpi.Data = reader.GetDateTime(0);
                    kpi.valorKPI = reader.GetDouble(1);

                    // insere na lista
                    HistKPI.Add(kpi);

                    // exclui se existir
                    kpiMetodos.ExcluirData(IDCliente, IDKPI, kpi.Data);

                    // salva
                    kpiMetodos.Salvar(kpi);
                }

                reader.Close();

                // termina
                if (_oleCmd != null)
                {
                    _oleCmd.Parameters.Clear();
                    _oleCmd.Dispose();
                }
                _oleCmd = null;

                if (_olecon != null)
                {
                    if (_olecon.State == ConnectionState.Open)
                        _olecon.Close();
                    _olecon.Dispose();
                }
                _olecon = null;

            }
            catch (Exception ex)
            {
                // erro
                MensagemErro = ex.Message;
                return (null);
            }

            // ok
            MensagemErro = "OK";
            return (HistKPI);
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Creates the folder if needed.
        /// </summary>
        /// <param name="path">The path.</param>
        /// <returns></returns>
        private bool CreateFolderIfNeeded(string path)
        {
            bool result = true;
            if (!Directory.Exists(path))
            {
                try
                {
                    Directory.CreateDirectory(path);
                }
                catch (Exception)
                {
                    /*TODO: You must process this exception.*/
                    result = false;
                }
            }
            return result;
        }

        #endregion
    }
}