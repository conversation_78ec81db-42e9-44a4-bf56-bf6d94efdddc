﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class KpiController
    {
        //
        // KPI CONSUMO
        //

        // GET: KPI Consumo
        public ActionResult KPI_Consumo(int IDCliente, int IDMedicao, int idKPI, int TipoRelat)
        {
            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "KPI_Consumo");

            // relatorio consumo
            return (KPI_Grafico_Show(IDCliente, IDMedicao, idKPI, TipoRelat));
        }

        // KPI Consumo Diario
        private void KPI_Consumo_Diario(int IDCliente, int IDMedicao, int idKPI, DATAHORA data_hora, int TipoRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_DIARIO relatorio = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise = new RELAT_DIARIO_ANALISE();

            RELAT_DIARIO relatorio_sim = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise_sim = new RELAT_DIARIO_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int TipoSimula = 0;

            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le configuracao do KPI
            KPIConfiguracaoMetodos kpiMetodos = new KPIConfiguracaoMetodos();
            KPIConfiguracaoDominio kpi = kpiMetodos.ListarPorId(idKPI);


            // calcula valores
            retorno = SmCalcDB_Energia_RelatDiario((char)0, ref config_interface, (char)2, (char)TipoSimula, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_DIARIO(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var Consumo = new double[26];
            var ConsumoKPI = new double[26];
            var ValoresKPI = new double[26];
            var Periodo = new int[26];
            var Dias = new string[26];
            var DatasN = new DateTime[26];
            var Horas = new string[26];

            double ConsumoKPI_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            DateTime strDataFim = new DateTime((int)relatorio.registro[23].datahora.data.ano,
                                   (int)relatorio.registro[23].datahora.data.mes,
                                   (int)relatorio.registro[23].datahora.data.dia,
                                   (int)relatorio.registro[23].datahora.hora.hora,
                                   (int)relatorio.registro[23].datahora.hora.min, 0);

            // valores do KPI
            List<KPIHistDominio> histKPI = Get_valoresKPI(kpi, medicao, strData, strDataFim);

            // valor KPI
            double valorKPI = histKPI[0].valorKPI;

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Horas[i] = strData.ToString("HH:mm");

                // KPI
                valorKPI = Get_valorKPI(histKPI, strData);

                // proxima hora
                strData = strData.AddHours(1);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    Consumo[i] = relatorio.registro[0].valor1;
                    ConsumoKPI[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[0].valor1, valorKPI);
                    ValoresKPI[i] = valorKPI;
                    Periodo[i] = relatorio.registro[0].periodo;
                }

                // verifica se ultima barra
                if (i == 25)
                {
                    // zera
                    Consumo[i] = relatorio.registro[23].valor1;
                    ConsumoKPI[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[23].valor1, valorKPI);
                    ValoresKPI[i] = valorKPI;
                    Periodo[i] = relatorio.registro[23].periodo;
                }

                if (i >= 1 && i <= 24)
                {
                    // copia
                    j = i - 1;

                    Consumo[i] = relatorio.registro[j].valor1;
                    ConsumoKPI[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[j].valor1, valorKPI);
                    ValoresKPI[i] = valorKPI;
                    Periodo[i] = relatorio.registro[j].periodo;

                    // verifica se sem registro
                    if (relatorio.registro[j].periodo == 3)
                    {
                        Consumo[i] = 0.0;
                        ConsumoKPI[i] = 0.0;
                    }

                    // verifica consumo maximo
                    if (ConsumoKPI[i] > ConsumoKPI_max_grafico)
                        ConsumoKPI_max_grafico = ConsumoKPI[i];
                }
            }

            ConsumoKPI_max_grafico = ConsumoKPI_max_grafico * 1.1;

            if (ConsumoKPI_max_grafico == 0.0)
            {
                ConsumoKPI_max_grafico = 1.0;
            }

            ViewBag.ConsKPIMaxGrafico = ConsumoKPI_max_grafico;

            ViewBag.Consumo = Consumo;
            ViewBag.ConsumoKPI = ConsumoKPI;
            ViewBag.ValoresKPI = ValoresKPI;
            ViewBag.Periodo = Periodo;
            ViewBag.Dias = Dias;
            ViewBag.DatasN = DatasN;
            ViewBag.Horas = Horas;

            ViewBag.valorKPI = valorKPI;

            // informacoes do relatorio
            InformacoesRelat(kpi, TipoRelat);

            ViewBag.PeriodoRelat = string.Format("Diário");
            
            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", DataAtual);


            // estatisticas
            double ConsP = 0.0;
            double ConsFPI = 0.0;
            double ConsFPC = 0.0;
            double ConsTotal = 0.0;

            // percorre valores
            for (i = 1; i < 25; i++)
            {
                // periodo
                switch (Periodo[i])
                {
                    case 0: // ponta
                        ConsP += ConsumoKPI[i];
                        break;

                    case 1: // fora ponta indutivo
                        ConsFPI += ConsumoKPI[i];
                        break;

                    case 2: // fora ponta capacitivo
                        ConsFPC += ConsumoKPI[i];
                        break;
                }
            }

            // total
            ConsTotal = ConsP + ConsFPI + ConsFPC;

            ViewBag.ConsP = string.Format("{0:#,##0.00}", ConsP);
            ViewBag.ConsPN = ConsP;

            ViewBag.ConsFPI = string.Format("{0:#,##0.00}", ConsFPI);
            ViewBag.ConsFPIN = ConsFPI;

            ViewBag.ConsFPC = string.Format("{0:#,##0.00}", ConsFPC);
            ViewBag.ConsFPCN = ConsFPC;

            ViewBag.ConsTotal = string.Format("{0:#,##0.00}", ConsTotal);
            ViewBag.ConsTotalN = ConsTotal;

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // KPI Consumo Diario XLS
        private HSSFWorkbook KPI_Consumo_Diario_XLS(int IDCliente, int IDMedicao, int idKPI, int TipoRelat)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Período", ViewBag.UnidadeRelat, "Consumo", ViewBag.NomeKPI };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 25; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _datahoraStyle);

                // periodo
                numeroCelulaXLS(row, 1, ViewBag.Periodo[i], _intCellStyle);

                // consumo / KPI
                numeroCelulaXLS(row, 2, ViewBag.ConsumoKPI[i], _2CellStyle);

                // consumo
                numeroCelulaXLS(row, 3, ViewBag.Consumo[i], _1CellStyle);

                // KPI
                numeroCelulaXLS(row, 4, ViewBag.ValoresKPI[i], _2CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "KPI - " + ViewBag.NomeRelat + " (" + ViewBag.UnidadeRelat + ")", "Diário", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string[] cab_cons = { "", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // consumo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, ViewBag.UnidadeRelat, _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.ConsFPCN, _2CellStyle);
            numeroCelulaXLS(row, 2, ViewBag.ConsFPIN, _2CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.ConsPN, _2CellStyle);
            numeroCelulaXLS(row, 4, ViewBag.ConsTotalN, _2CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // KPI Consumo Semanal
        private void KPI_Consumo_Semanal(int IDCliente, int IDMedicao, int idKPI, DATAHORA data_hora, int TipoRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_SEMANAL relatorio = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise = new RELAT_SEMANAL_ANALISE();

            RELAT_SEMANAL relatorio_sim = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise_sim = new RELAT_SEMANAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int TipoSimula = 0;

            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le configuracao do KPI
            KPIConfiguracaoMetodos kpiMetodos = new KPIConfiguracaoMetodos();
            KPIConfiguracaoDominio kpi = kpiMetodos.ListarPorId(idKPI);


            // calcula valores
            retorno = SmCalcDB_Energia_RelatSemanal((char)0, ref config_interface, (char)2, (char)TipoSimula, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_SEMANAL(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // maior consumo
            double maior_consumo = analise.analise_valor.consumo[0] + analise.analise_valor.consumo[1];

            // grafico
            var ConsumoKPIP = new double[9];
            var ConsumoKPIFPI = new double[9];
            var ConsumoKPIFPC = new double[9];
            var ConsumoKPITotal = new double[9];
            var ValoresKPI = new double[9];
            var ConsumoP = new double[9];
            var ConsumoFPI = new double[9];
            var ConsumoFPC = new double[9];
            var Datas = new string[9];
            var DatasN = new DateTime[9];
            var Dias = new string[9];

            DateTime inicio = new DateTime();
            DateTime fim = new DateTime();

            double ConsumoKPI_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            DateTime strDataFim = new DateTime((int)relatorio.registro[6].datahora.data.ano,
                                   (int)relatorio.registro[6].datahora.data.mes,
                                   (int)relatorio.registro[6].datahora.data.dia,
                                   (int)relatorio.registro[6].datahora.hora.hora,
                                   (int)relatorio.registro[6].datahora.hora.min, 0);

            // valores do KPI
            List<KPIHistDominio> histKPI = Get_valoresKPI(kpi, medicao, strData, strDataFim);

            // valor KPI
            double valorKPI = histKPI[0].valorKPI;

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 9; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = string.Format("{0:d}", strData);

                // guarda inicio
                if (i == 1)
                {
                    inicio = strData;
                }

                // guarda fim
                if (i == 7)
                {
                    fim = strData;
                }

                // KPI
                valorKPI = Get_valorKPI(histKPI, strData);

                // proximo dias
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    ConsumoKPIP[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[0].valor[0], valorKPI);
                    ConsumoKPIFPI[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[0].valor[1], valorKPI);
                    ConsumoKPIFPC[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[0].valor[2], valorKPI);

                    ConsumoP[i] = relatorio.registro[0].valor[0];
                    ConsumoFPI[i] = relatorio.registro[0].valor[1];
                    ConsumoFPC[i] = relatorio.registro[0].valor[2];

                    ConsumoKPITotal[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, (ConsumoP[i] + ConsumoFPI[i] + ConsumoFPC[i]), valorKPI);
                }

                if (i == 8)
                {
                    // zera
                    ConsumoKPIP[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[6].valor[0], valorKPI);
                    ConsumoKPIFPI[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[6].valor[1], valorKPI);
                    ConsumoKPIFPC[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[6].valor[2], valorKPI);

                    ConsumoP[i] = relatorio.registro[6].valor[0];
                    ConsumoFPI[i] = relatorio.registro[6].valor[1];
                    ConsumoFPC[i] = relatorio.registro[6].valor[2];

                    ConsumoKPITotal[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, (ConsumoP[i] + ConsumoFPI[i] + ConsumoFPC[i]), valorKPI);
                }

                if (i >= 1 && i <= 7)
                {
                    // copia
                    j = i - 1;

                    ConsumoKPIP[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[j].valor[0], valorKPI);
                    ConsumoKPIFPI[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[j].valor[1], valorKPI);
                    ConsumoKPIFPC[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[j].valor[2], valorKPI);

                    ConsumoP[i] = relatorio.registro[j].valor[0];
                    ConsumoFPI[i] = relatorio.registro[j].valor[1];
                    ConsumoFPC[i] = relatorio.registro[j].valor[2];

                    ConsumoKPITotal[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, (ConsumoP[i] + ConsumoFPI[i] + ConsumoFPC[i]), valorKPI);

                    // verifica consumo maximo
                    if (ConsumoKPITotal[i] > ConsumoKPI_max_grafico)
                        ConsumoKPI_max_grafico = ConsumoKPITotal[i];
                }
            }

            ConsumoKPI_max_grafico = ConsumoKPI_max_grafico * 1.1;

            if (ConsumoKPI_max_grafico == 0.0)
            {
                ConsumoKPI_max_grafico = 1.0;
            }

            ViewBag.ConsKPIMaxGrafico = ConsumoKPI_max_grafico;

            ViewBag.ConsumoKPIP = ConsumoKPIP;
            ViewBag.ConsumoKPIFPI = ConsumoKPIFPI;
            ViewBag.ConsumoKPIFPC = ConsumoKPIFPC;
            ViewBag.ConsumoKPITotal = ConsumoKPITotal;
            ViewBag.ValoresKPI = ValoresKPI;
            ViewBag.ConsumoP = ConsumoP;
            ViewBag.ConsumoFPI = ConsumoFPI;
            ViewBag.ConsumoFPC = ConsumoFPC;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            ViewBag.valorKPI = valorKPI;

            // informacoes do relatorio
            InformacoesRelat(kpi, TipoRelat);

            ViewBag.PeriodoRelat = string.Format("Semanal");

            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = inicio;
            ViewBag.DataFinalN = fim;
            ViewBag.DataTextoAtual = string.Format("{0:d} - {1:d} ", inicio, fim);


            // estatisticas
            double ConsP = 0.0;
            double ConsFPI = 0.0;
            double ConsFPC = 0.0;
            double ConsTotal = 0.0;

            // percorre valores
            for (i = 1; i < 8; i++)
            {
                ConsP += ConsumoKPIP[i];
                ConsFPI += ConsumoKPIFPI[i];
                ConsFPC += ConsumoKPIFPC[i];
            }

            // total
            ConsTotal = ConsP + ConsFPI + ConsFPC;

            ViewBag.ConsP = string.Format("{0:#,##0.00}", ConsP);
            ViewBag.ConsPN = ConsP;

            ViewBag.ConsFPI = string.Format("{0:#,##0.00}", ConsFPI);
            ViewBag.ConsFPIN = ConsFPI;

            ViewBag.ConsFPC = string.Format("{0:#,##0.00}", ConsFPC);
            ViewBag.ConsFPCN = ConsFPC;

            ViewBag.ConsTotal = string.Format("{0:#,##0.00}", ConsTotal);
            ViewBag.ConsTotalN = ConsTotal;

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(inicio.Year, inicio.Month, inicio.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(7);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // KPI Consumo Semanal XLS
        private HSSFWorkbook KPI_Consumo_Semanal_XLS(int IDCliente, int IDMedicao, int idKPI, int TipoRelat)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string str_fpc = string.Format("Fora de Ponta Capacitivo ({0})", ViewBag.UnidadeRelat);
            string str_fpi = string.Format("Fora de Ponta Indutivo ({0})", ViewBag.UnidadeRelat);
            string str_ponta = string.Format("Ponta ({0})", ViewBag.UnidadeRelat);
            string str_total = string.Format("Total ({0})", ViewBag.UnidadeRelat);
            string[] cabecalho = { "Semana", "Data e Hora", str_fpc, str_fpi, str_ponta, str_total };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 8; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // semana
                textoCelulaXLS(row, 0, string.Format("{0:dddd}", ViewBag.DatasN[i]));

                // data e hora
                datahoraCelulaXLS(row, 1, ViewBag.DatasN[i], _dataStyle);

                // consumo fora de ponta capacitivo
                numeroCelulaXLS(row, 2, ViewBag.ConsumoKPIFPC[i], _2CellStyle);

                // consumo fora de ponta indutivo
                numeroCelulaXLS(row, 3, ViewBag.ConsumoKPIFPI[i], _2CellStyle);

                // consumo ponta
                numeroCelulaXLS(row, 4, ViewBag.ConsumoKPIP[i], _2CellStyle);

                // consumo total
                numeroCelulaXLS(row, 5, ViewBag.ConsumoKPITotal[i], _2CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "KPI - " + ViewBag.NomeRelat + " (" + ViewBag.UnidadeRelat + ")", "Semanal", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string str_consumo = string.Format("{0}", ViewBag.UnidadeRelat);
            string[] cab_cons = { str_consumo, "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // consumo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0,  ViewBag.UnidadeRelat, _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.ConsFPCN, _2CellStyle);
            numeroCelulaXLS(row, 2, ViewBag.ConsFPIN, _2CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.ConsPN, _2CellStyle);
            numeroCelulaXLS(row, 4, ViewBag.ConsTotalN, _2CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // KPI Consumo Mensal
        private void KPI_Consumo_Mensal(int IDCliente, int IDMedicao, int idKPI, DATAHORA data_hora, int TipoRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_MENSAL relatorio = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise = new RELAT_MENSAL_ANALISE();

            RELAT_MENSAL relatorio_sim = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise_sim = new RELAT_MENSAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int TipoSimula = 0;

            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le configuracao do KPI
            KPIConfiguracaoMetodos kpiMetodos = new KPIConfiguracaoMetodos();
            KPIConfiguracaoDominio kpi = kpiMetodos.ListarPorId(idKPI);


            // calcula valores
            retorno = SmCalcDB_Energia_RelatMensal((char)0, ref config_interface, (char)2, (char)TipoSimula, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_MENSAL(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // numero de dias do mes
            int NumDiasMes = relatorio.num_dias;

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[NumDiasMes - 1].datahora.data.ano,
                                   (int)relatorio.registro[NumDiasMes - 1].datahora.data.mes,
                                   1, 1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // maior consumo
            double maior_consumo = analise.analise_valor.consumo[0] + analise.analise_valor.consumo[1];

            // grafico
            var ConsumoKPIP = new double[42];
            var ConsumoKPIFPI = new double[42];
            var ConsumoKPIFPC = new double[42];
            var ConsumoKPITotal = new double[42];
            var ValoresKPI = new double[42];
            var ConsumoP = new double[42];
            var ConsumoFPI = new double[42];
            var ConsumoFPC = new double[42];
            var Datas = new string[42];
            var DatasN = new DateTime[42];
            var Dias = new string[42];

            double ConsumoKPI_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            DateTime strDataFim = new DateTime((int)relatorio.registro[NumDiasMes-1].datahora.data.ano,
                                   (int)relatorio.registro[NumDiasMes-1].datahora.data.mes,
                                   (int)relatorio.registro[NumDiasMes-1].datahora.data.dia,
                                   (int)relatorio.registro[NumDiasMes-1].datahora.hora.hora,
                                   (int)relatorio.registro[NumDiasMes-1].datahora.hora.min, 0);

            // valores do KPI
            List<KPIHistDominio> histKPI = Get_valoresKPI(kpi, medicao, strData, strDataFim);

            // valor KPI
            double valorKPI = histKPI[0].valorKPI;

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = strData.ToString("dd/MM");

                // KPI
                valorKPI = Get_valorKPI(histKPI, strData);

                // proximo dias
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    ConsumoKPIP[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[0].valor[0], valorKPI);
                    ConsumoKPIFPI[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[0].valor[1], valorKPI);
                    ConsumoKPIFPC[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[0].valor[2], valorKPI);

                    ConsumoP[i] = relatorio.registro[0].valor[0];
                    ConsumoFPI[i] = relatorio.registro[0].valor[1];
                    ConsumoFPC[i] = relatorio.registro[0].valor[2];

                    ConsumoKPITotal[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, (ConsumoP[i] + ConsumoFPI[i] + ConsumoFPC[i]), valorKPI);
                }

                if (i == (NumDiasMes + 1))
                {
                    // zera
                    ConsumoKPIP[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[NumDiasMes-1].valor[0], valorKPI);
                    ConsumoKPIFPI[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[NumDiasMes-1].valor[1], valorKPI);
                    ConsumoKPIFPC[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[NumDiasMes-1].valor[2], valorKPI);

                    ConsumoP[i] = relatorio.registro[NumDiasMes-1].valor[0];
                    ConsumoFPI[i] = relatorio.registro[NumDiasMes-1].valor[1];
                    ConsumoFPC[i] = relatorio.registro[NumDiasMes-1].valor[2];

                    ConsumoKPITotal[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, (ConsumoP[i] + ConsumoFPI[i] + ConsumoFPC[i]), valorKPI);
                }

                if (i >= 1 && i <= NumDiasMes)
                {
                    // copia
                    j = i - 1;

                    ConsumoKPIP[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[j].valor[0], valorKPI);
                    ConsumoKPIFPI[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[j].valor[1], valorKPI);
                    ConsumoKPIFPC[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[j].valor[2], valorKPI);

                    ConsumoP[i] = relatorio.registro[j].valor[0];
                    ConsumoFPI[i] = relatorio.registro[j].valor[1];
                    ConsumoFPC[i] = relatorio.registro[j].valor[2];

                    ConsumoKPITotal[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, (ConsumoP[i] + ConsumoFPI[i] + ConsumoFPC[i]), valorKPI);

                    // verifica consumo maximo
                    if (ConsumoKPITotal[i] > ConsumoKPI_max_grafico)
                        ConsumoKPI_max_grafico = ConsumoKPITotal[i];
                }
            }

            ConsumoKPI_max_grafico = ConsumoKPI_max_grafico * 1.1;

            if (ConsumoKPI_max_grafico == 0.0)
            {
                ConsumoKPI_max_grafico = 1.0;
            }

            ViewBag.ConsKPIMaxGrafico = ConsumoKPI_max_grafico;

            ViewBag.ConsumoKPIP = ConsumoKPIP;
            ViewBag.ConsumoKPIFPI = ConsumoKPIFPI;
            ViewBag.ConsumoKPIFPC = ConsumoKPIFPC;
            ViewBag.ConsumoKPITotal = ConsumoKPITotal;
            ViewBag.ValoresKPI = ValoresKPI;
            ViewBag.ConsumoP = ConsumoP;
            ViewBag.ConsumoFPI = ConsumoFPI;
            ViewBag.ConsumoFPC = ConsumoFPC;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            ViewBag.valorKPI = valorKPI;

            // informacoes do relatorio
            InformacoesRelat(kpi, TipoRelat);

            ViewBag.PeriodoRelat = string.Format("Mensal");

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:y}", DataAtual);

            ViewBag.NumDiasMes = NumDiasMes;


            // estatisticas
            double ConsP = 0.0;
            double ConsFPI = 0.0;
            double ConsFPC = 0.0;
            double ConsTotal = 0.0;

            // percorre valores
            for (i = 1; i < (NumDiasMes+1); i++)
            {
                ConsP += ConsumoKPIP[i];
                ConsFPI += ConsumoKPIFPI[i];
                ConsFPC += ConsumoKPIFPC[i];
            }

            // total
            ConsTotal = ConsP + ConsFPI + ConsFPC;

            ViewBag.ConsP = string.Format("{0:#,##0.00}", ConsP);
            ViewBag.ConsPN = ConsP;

            ViewBag.ConsFPI = string.Format("{0:#,##0.00}", ConsFPI);
            ViewBag.ConsFPIN = ConsFPI;

            ViewBag.ConsFPC = string.Format("{0:#,##0.00}", ConsFPC);
            ViewBag.ConsFPCN = ConsFPC;

            ViewBag.ConsTotal = string.Format("{0:#,##0.00}", ConsTotal);
            ViewBag.ConsTotalN = ConsTotal;

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // KPI Consumo Mensal XLS
        private HSSFWorkbook KPI_Consumo_Mensal_XLS(int IDCliente, int IDMedicao, int idKPI, int TipoRelat)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string str_fpc = string.Format("Fora de Ponta Capacitivo ({0})", ViewBag.UnidadeRelat);
            string str_fpi = string.Format("Fora de Ponta Indutivo ({0})", ViewBag.UnidadeRelat);
            string str_ponta = string.Format("Ponta ({0})", ViewBag.UnidadeRelat);
            string str_total = string.Format("Total ({0})", ViewBag.UnidadeRelat);
            string[] cabecalho = { "Data", str_fpc, str_fpi, str_ponta, str_total };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < (ViewBag.NumDiasMes + 1); i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // consumo fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.ConsumoKPIFPC[i], _2CellStyle);

                // consumo fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.ConsumoKPIFPI[i], _2CellStyle);

                // consumo ponta
                numeroCelulaXLS(row, 3, ViewBag.ConsumoKPIP[i], _2CellStyle);

                // consumo total
                numeroCelulaXLS(row, 4, ViewBag.ConsumoKPITotal[i], _2CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "KPI - " + ViewBag.NomeRelat + " (" + ViewBag.UnidadeRelat + ")", "Mensal", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string str_consumo = string.Format("{0}", ViewBag.UnidadeRelat);
            string[] cab_cons = { str_consumo, "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // consumo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, ViewBag.UnidadeRelat, _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.ConsFPCN, _2CellStyle);
            numeroCelulaXLS(row, 2, ViewBag.ConsFPIN, _2CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.ConsPN, _2CellStyle);
            numeroCelulaXLS(row, 4, ViewBag.ConsTotalN, _2CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // KPI Consumo Anual
        private void KPI_Consumo_Anual(int IDCliente, int IDMedicao, int idKPI, DATAHORA data_hora, int TipoRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_ANUAL relatorio = new RELAT_ANUAL();
            RELAT_ANUAL_ANALISE analise = new RELAT_ANUAL_ANALISE();

            RELAT_ANUAL relatorio_sim = new RELAT_ANUAL();
            RELAT_ANUAL_ANALISE analise_sim = new RELAT_ANUAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int TipoSimula = 0;

            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le configuracao do KPI
            KPIConfiguracaoMetodos kpiMetodos = new KPIConfiguracaoMetodos();
            KPIConfiguracaoDominio kpi = kpiMetodos.ListarPorId(idKPI);


            // calcula valores
            retorno = SmCalcDB_Energia_RelatAnual((char)0, ref config_interface, (char)2, (char)TipoSimula, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_ANUAL(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   1, 1, 1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // maior consumo
            double maior_consumo = analise.analise_valor.consumo[0] + analise.analise_valor.consumo[1];

            // grafico
            var ConsumoKPIP = new double[14];
            var ConsumoKPIFPI = new double[14];
            var ConsumoKPIFPC = new double[14];
            var ValoresKPI = new double[14];
            var ConsumoKPITotal = new double[14];
            var ConsumoP = new double[14];
            var ConsumoFPI = new double[14];
            var ConsumoFPC = new double[14];
            var Datas = new string[14];
            var DatasN = new DateTime[14];
            var Meses = new string[14];

            double ConsumoKPI_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            DateTime strDataFim = new DateTime((int)relatorio.registro[11].datahora.data.ano,
                                   (int)relatorio.registro[11].datahora.data.mes,
                                   (int)relatorio.registro[11].datahora.data.dia,
                                   (int)relatorio.registro[11].datahora.hora.hora,
                                   (int)relatorio.registro[11].datahora.hora.min, 0);

            // valores do KPI
            List<KPIHistDominio> histKPI = Get_valoresKPI(kpi, medicao, strData, strDataFim);

            // valor KPI
            double valorKPI = histKPI[0].valorKPI;

            // volta 1 mes para a primeira barra
            strData = strData.AddMonths(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 14; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Meses[i] = string.Format("{0:MMMM}", strData);

                // KPI
                valorKPI = Get_valorKPI(histKPI, strData);

                // proximo mes
                strData = strData.AddMonths(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    ConsumoKPIP[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[0].valor[0], valorKPI);
                    ConsumoKPIFPI[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[0].valor[1], valorKPI);
                    ConsumoKPIFPC[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[0].valor[2], valorKPI);

                    ConsumoP[i] = relatorio.registro[0].valor[0];
                    ConsumoFPI[i] = relatorio.registro[0].valor[1];
                    ConsumoFPC[i] = relatorio.registro[0].valor[2];

                    ConsumoKPITotal[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, (ConsumoP[i] + ConsumoFPI[i] + ConsumoFPC[i]), valorKPI);
                }

                if (i == 13)
                {
                    // zera
                    ConsumoKPIP[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[11].valor[0], valorKPI);
                    ConsumoKPIFPI[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[11].valor[1], valorKPI);
                    ConsumoKPIFPC[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[11].valor[2], valorKPI);

                    ConsumoP[i] = relatorio.registro[11].valor[0];
                    ConsumoFPI[i] = relatorio.registro[11].valor[1];
                    ConsumoFPC[i] = relatorio.registro[11].valor[2];

                    ConsumoKPITotal[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, (ConsumoP[i] + ConsumoFPI[i] + ConsumoFPC[i]), valorKPI);
                }

                if (i >= 1 && i <= 12)
                {
                    // copia
                    j = i - 1;

                    ConsumoKPIP[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[j].valor[0], valorKPI);
                    ConsumoKPIFPI[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[j].valor[1], valorKPI);
                    ConsumoKPIFPC[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[j].valor[2], valorKPI);

                    ConsumoP[i] = relatorio.registro[j].valor[0];
                    ConsumoFPI[i] = relatorio.registro[j].valor[1];
                    ConsumoFPC[i] = relatorio.registro[j].valor[2];

                    ConsumoKPITotal[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, (ConsumoP[i] + ConsumoFPI[i] + ConsumoFPC[i]), valorKPI);

                    // verifica consumo maximo
                    if (ConsumoKPITotal[i] > ConsumoKPI_max_grafico)
                        ConsumoKPI_max_grafico = ConsumoKPITotal[i];
                }
            }

            ConsumoKPI_max_grafico = ConsumoKPI_max_grafico * 1.1;

            if (ConsumoKPI_max_grafico == 0.0)
            {
                ConsumoKPI_max_grafico = 1.0;
            }

            ViewBag.ConsKPIMaxGrafico = ConsumoKPI_max_grafico;

            ViewBag.ConsumoKPIP = ConsumoKPIP;
            ViewBag.ConsumoKPIFPI = ConsumoKPIFPI;
            ViewBag.ConsumoKPIFPC = ConsumoKPIFPC;
            ViewBag.ConsumoKPITotal = ConsumoKPITotal;
            ViewBag.ValoresKPI = ValoresKPI;
            ViewBag.ConsumoP = ConsumoP;
            ViewBag.ConsumoFPI = ConsumoFPI;
            ViewBag.ConsumoFPC = ConsumoFPC;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Meses = Meses;

            ViewBag.valorKPI = valorKPI;

            // informacoes do relatorio
            InformacoesRelat(kpi, TipoRelat);

            ViewBag.PeriodoRelat = string.Format("Anual");

            // data atual
            ViewBag.DataAtual = string.Format("{0:yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:yyyy}", DataAtual);


            // estatisticas
            double ConsP = 0.0;
            double ConsFPI = 0.0;
            double ConsFPC = 0.0;
            double ConsTotal = 0.0;

            // percorre valores
            for (i = 1; i < 13; i++)
            {
                ConsP += ConsumoKPIP[i];
                ConsFPI += ConsumoKPIFPI[i];
                ConsFPC += ConsumoKPIFPC[i];
            }

            // total
            ConsTotal = ConsP + ConsFPI + ConsFPC;

            ViewBag.ConsP = string.Format("{0:#,##0.00}", ConsP);
            ViewBag.ConsPN = ConsP;

            ViewBag.ConsFPI = string.Format("{0:#,##0.00}", ConsFPI);
            ViewBag.ConsFPIN = ConsFPI;

            ViewBag.ConsFPC = string.Format("{0:#,##0.00}", ConsFPC);
            ViewBag.ConsFPCN = ConsFPC;

            ViewBag.ConsTotal = string.Format("{0:#,##0.00}", ConsTotal);
            ViewBag.ConsTotalN = ConsTotal;

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddYears(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // KPI Consumo Anual XLS
        private HSSFWorkbook KPI_Consumo_Anual_XLS(int IDCliente, int IDMedicao, int idKPI, int TipoRelat)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string str_fpc = string.Format("Fora de Ponta Capacitivo ({0})", ViewBag.UnidadeRelat);
            string str_fpi = string.Format("Fora de Ponta Indutivo ({0})", ViewBag.UnidadeRelat);
            string str_ponta = string.Format("Ponta ({0})", ViewBag.UnidadeRelat);
            string str_total = string.Format("Total ({0})", ViewBag.UnidadeRelat);
            string[] cabecalho = { "Data", str_fpc, str_fpi, str_ponta, str_total };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 13; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // consumo fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.ConsumoKPIFPC[i], _2CellStyle);

                // consumo fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.ConsumoKPIFPI[i], _2CellStyle);

                // consumo ponta
                numeroCelulaXLS(row, 3, ViewBag.ConsumoKPIP[i], _2CellStyle);

                // consumo total
                numeroCelulaXLS(row, 4, ViewBag.ConsumoKPITotal[i], _2CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "KPI - " + ViewBag.NomeRelat + " (" + ViewBag.UnidadeRelat + ")", "Anual", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string str_consumo = string.Format("{0}", ViewBag.UnidadeRelat);
            string[] cab_cons = { str_consumo, "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // consumo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, ViewBag.UnidadeRelat, _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.ConsFPCN, _2CellStyle);
            numeroCelulaXLS(row, 2, ViewBag.ConsFPIN, _2CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.ConsPN, _2CellStyle);
            numeroCelulaXLS(row, 4, ViewBag.ConsTotalN, _2CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }
    }
}