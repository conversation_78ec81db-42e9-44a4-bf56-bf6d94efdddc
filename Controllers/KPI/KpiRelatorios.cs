﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class KpiController
    {

        // GET: KPI - Relatorios
        public ActionResult KPI_Relatorios(int IDCliente, int IDMedicao)
        {
            // tela de ajuda - KPI
            CookieStore.SalvaCookie_String("PaginaAjuda", "KPI_Configuracao");

            // le supervisao da medicao
            SupervMedicoesMetodos medSupervMetodos = new SupervMedicoesMetodos();
            SupervMedicoesDominio listaMedicoes = medSupervMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

            // salva cookie 
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", listaMedicoes.IDTipoMedicao_MD);
            CookieStore.SalvaCookie_String("MedicaoNome", listaMedicoes.NMED);
            CookieStore.SalvaCookie_String("UnidadeNome", listaMedicoes.NUN);
            CookieStore.SalvaCookie_String("GrupoNome", listaMedicoes.NGU);
            CookieStore.SalvaCookie_Int("_IDGateway", listaMedicoes.IDGW_GW);
            CookieStore.SalvaCookie_Int("IDTipoGateway", listaMedicoes.IDTipoGateway_GW);
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "KPI");

            // le cookies
            LeCookies_SmartEnergy();

            // prepara listas
            PreparaListas_KPI();

            // le KPIs
            KPIConfiguracaoMetodos kpiMetodos = new KPIConfiguracaoMetodos();
            List<KPIConfiguracaoDominio> listaKPIs = kpiMetodos.ListarPorIDCliente(IDCliente);
            List<KPIConfiguracaoDominio> nova_listaKPIs = new List<KPIConfiguracaoDominio>();

            if (listaKPIs != null)
            {
                // lista de medicoes habilitadas do usuario
                List<int> ConfigMedicaoList_Usuario = new List<int>();
                ConfigMedicaoList_Usuario = ViewBag._ConfigMed;

                // verifica se existe lista de medicoes
                if (ConfigMedicaoList_Usuario != null)
                {
                    if (ConfigMedicaoList_Usuario.Count() == 0)
                    {
                        ConfigMedicaoList_Usuario = null;
                    }
                }

                // le medicoes configuradas do KPI para lista
                CliGateGrupoUnidMedicoesMetodos medicoesMetodos = new CliGateGrupoUnidMedicoesMetodos();
                List<CliGateGrupoUnidMedicoesDominio> medicoes = new List<CliGateGrupoUnidMedicoesDominio>();

                // le medicoes de energia habilitadas para o usuario no cliente
                medicoes = medicoesMetodos.ListarPorIDCliente(IDCliente, ConfigMedicaoList_Usuario, 10);

                if (medicoes != null)
                {
                    // KPI grupos medições
                    KPIGruposMedMetodos kpiGruposMetodos = new KPIGruposMedMetodos();

                    // percorre KPIs
                    foreach (KPIConfiguracaoDominio kpi in listaKPIs)
                    {
                        // percorre medicoes 
                        int contador;

                        // verifica se esta KPI possui alguma medição que o usuário possa ver
                        for (contador = 0; contador < medicoes.Count(); contador++)
                        {
                            // verifica se medicao de energia
                            if (medicoes[contador].IDTipoMedicao == TIPO_MEDICAO.ENERGIA || medicoes[contador].IDTipoMedicao == TIPO_MEDICAO.ENERGIA_FORMULA)
                            {
                                // verifica se medicao esta habilitada para o KPI
                                if (kpiGruposMetodos.ExisteMedicao(kpi.IDKPI, IDMedicao))
                                {
                                    // copia KPI na lista de utilizadas
                                    nova_listaKPIs.Add(kpi);

                                    // achou pelo menos uma medição nesta KPI que o usuário pode ver, acrescento e vou para a próxima KPI
                                    break;
                                }
                            }
                        }
                    }
                }
            }

            return View(nova_listaKPIs);
        }



        // calcula Energia/Grandeza ou Grandeza/Energia
        private double Calcula_Energia_Grandeza(int IDTipoRazao, double energia, double valor)
        {
            if (IDTipoRazao == 0)
            {
                return ((valor != 0.0) ? (energia / valor) : 0.0);
            }

            return ((energia != 0.0) ? (valor / energia) : 0.0);
        }

        // valor do KPI
        private double Get_valorKPI(List<KPIHistDominio> histKPI, DateTime DataValor)
        {
            // valor
            double valorKPI = 0.0;
            double antKPI = 0.0;

            if (histKPI != null)
            {
                if (histKPI.Count > 0)
                {
                    valorKPI = histKPI[0].valorKPI;
                    antKPI = histKPI[0].valorKPI;
                }

                // busca
                foreach (KPIHistDominio kpi in histKPI)
                {
                    // verifica se data igual
                    if (DataValor == kpi.Data)
                    {
                        // achou
                        valorKPI = kpi.valorKPI;
                        break;
                    }

                    // verifica se data menor
                    if (DataValor < kpi.Data)
                    {
                        // achou, usa anterior
                        valorKPI = antKPI;
                        break;
                    }

                    // copia para anterior
                    antKPI = kpi.valorKPI;
                }
            }

            return (valorKPI);
        }

        // valores do KPI
        private List<KPIHistDominio> Get_valoresKPI(KPIConfiguracaoDominio kpi, MedicoesDominio medicao, DateTime DataIni, DateTime DataFim)
        {
            // historico
            List<KPIHistDominio> histKPI = new List<KPIHistDominio>();
            KPIHistDominio tmpHist = new KPIHistDominio();
            tmpHist.Data = DataIni;
            tmpHist.valorKPI = 0.0;

            // tipo
            switch (kpi.IDTipoHistorico)
            {
                case 0:       // área da medição (m2)

                    // adiciona 1 valor de area
                    tmpHist.Data = DataIni;
                    tmpHist.valorKPI = medicao.KPI_Area;
                    histKPI.Add(tmpHist);

                    break;

                case 1:       // valor fixo para todas as medições

                    // adiciona 1 valor de area
                    tmpHist.Data = DataIni;
                    tmpHist.valorKPI = kpi.ValorFixo;
                    histKPI.Add(tmpHist);

                    break;

                case 2:       // valor fixo por medição

                    // adiciona 1 valor de area
                    tmpHist.Data = DataIni;
                    tmpHist.valorKPI = kpi.ValorFixo;
                    histKPI.Add(tmpHist);

                    break;

                case 3:       // valor histório

                    // le valores KPI
                    KPIHistoricoMetodos kpiHistMetodos = new KPIHistoricoMetodos();

                    // le historico
                    List<KPIHistoricoDominio> kpiHist = kpiHistMetodos.ListarPorPeriodo(kpi.IDKPI, DataIni, DataFim);

                    if (kpiHist != null)
                    {
                        // indica que eh o primeiro registro
                        bool primeiro_registro = true;

                        // copia valores
                        foreach (KPIHistoricoDominio hist in kpiHist)
                        {
                            // verifica se eh o primeiro registro
                            if (primeiro_registro)
                            {
                                // nao eh mais o primeiro registro
                                primeiro_registro = false;

                                // verifica se data eh maior que da data inicial
                                if (hist.Data > DataIni)
                                {
                                    // leio primeiro registro anterior a data inicial
                                    KPIHistoricoDominio kpiHistIni = kpiHistMetodos.ListarPorDataAnterior(kpi.IDKPI, DataIni);

                                    KPIHistDominio tmpHistIni = new KPIHistDominio();
                                    tmpHistIni.Data = DataIni;
                                    tmpHistIni.valorKPI = 0.0;

                                    // verifica se existe
                                    if (kpiHistIni != null)
                                    {
                                        tmpHistIni.Data = kpiHistIni.Data;
                                        tmpHistIni.valorKPI = kpiHistIni.valorKPI;
                                    }

                                    // crio um registro inicial
                                    histKPI.Add(tmpHistIni);
                                }
                            }

                            // insiro registro
                            tmpHist.Data = hist.Data;
                            tmpHist.valorKPI = hist.valorKPI;
                            histKPI.Add(tmpHist);
                        }

                        // verifica se vazio
                        if (kpiHist.Count == 0)
                        {
                            // leio primeiro registro anterior a data inicial
                            KPIHistoricoDominio kpiHistIni = kpiHistMetodos.ListarPorDataAnterior(kpi.IDKPI, DataIni);

                            // verifica se existe
                            if (kpiHistIni != null)
                            {
                                tmpHist.Data = kpiHistIni.Data;
                                tmpHist.valorKPI = kpiHistIni.valorKPI;
                            }

                            // crio um registro inicial
                            histKPI.Add(tmpHist);
                        }
                    }

                    break;
            }

            // verifica se vazio e adiciona pelo menos 1 valor
            if (histKPI.Count == 0)
            {
                tmpHist.Data = DataIni;
                tmpHist.valorKPI = 0.0;
                histKPI.Add(tmpHist);
            }

            return (histKPI);
        }

        // informacoes do relatorio
        private void InformacoesRelat(KPIConfiguracaoDominio kpi, int TipoRelat)
        {
            // informacoes do relatorio
            string NomeKPI = kpi.Nome;
            string NomeRelat = "";
            string UnidadeKPI = kpi.UnidadeGrandeza;
            string UnidadeRelat = "";

            // verifica tipo razão
            if (kpi.IDTipoRazao == 0)
            {
                // verifica tipo relatório
                if (TipoRelat == 1)
                {
                    NomeRelat = string.Format("Demanda / {0}", UnidadeKPI);
                    UnidadeRelat = string.Format("{0}/{1}", ViewBag.UnidadeDemanda, UnidadeKPI);
                }
                else
                {
                    NomeRelat = string.Format("Consumo / {0}", UnidadeKPI);
                    UnidadeRelat = string.Format("{0}/{1}", ViewBag.UnidadeConsumo, UnidadeKPI);
                }
            }
            else
            {
                // verifica tipo relatório
                if (TipoRelat == 1)
                {
                    NomeRelat = string.Format("{0} / Demanda", UnidadeKPI);
                    UnidadeRelat = string.Format("{0}/{1}", UnidadeKPI, ViewBag.UnidadeDemanda);
                }
                else
                {
                    NomeRelat = string.Format("{0} / Consumo", UnidadeKPI);
                    UnidadeRelat = string.Format("{0}/{1}", UnidadeKPI, ViewBag.UnidadeConsumo);
                }
            }

            ViewBag.NomeKPI = NomeKPI;
            ViewBag.NomeRelat = NomeRelat;
            ViewBag.UnidadeKPI = UnidadeKPI;
            ViewBag.UnidadeRelat = UnidadeRelat;

            return;
        }
    }
}