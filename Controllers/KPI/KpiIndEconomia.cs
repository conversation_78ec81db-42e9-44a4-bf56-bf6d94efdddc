﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class KpiController
    {
        // Relatorio Diario
        // 0 -> Demanda Ativa e Fator de Potencia
        // 1 -> Demanda Reativa
        // 2 -> Consumo Ativo e Consumo Reativo
        // 3 -> <PERSON>or de Potencia (horario)
        // 4 -> <PERSON><PERSON> <PERSON> (Demanda Media / Demanda Maxima)
        // 5 -> Demanda nao utilizada
        // 6 -> Consumo Ativo segundo mercado livre
        // 7 -> <PERSON><PERSON> de Utilizacao (Demanda Media / Contrato)
        // 8 -> Consumo Ativo x Meta para Supervisao Mensal do Pao de Acucar
        // 9 -> KPI para Economia no Consumo de Energia
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_RelatDiario", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_RelatDiario(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, char simula, ref DATAHORA pdata_equipo, ref RELAT_DIARIO prelatorio, ref RELAT_DIARIO_ANALISE panalise, ref RELAT_DIARIO prelatorio_sim, ref RELAT_DIARIO_ANALISE panalise_sim);

        // Relatorio Semanal
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_RelatSemanal", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_RelatSemanal(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, char simula, ref DATAHORA pdata_equipo, ref RELAT_SEMANAL prelatorio, ref RELAT_SEMANAL_ANALISE panalise, ref RELAT_SEMANAL prelatorio_sim, ref RELAT_SEMANAL_ANALISE panalise_sim);

        // Relatorio Mensal
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_RelatMensal", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_RelatMensal(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, char simula, ref DATAHORA pdata_equipo, ref RELAT_MENSAL prelatorio, ref RELAT_MENSAL_ANALISE panalise, ref RELAT_MENSAL prelatorio_sim, ref RELAT_MENSAL_ANALISE panalise_sim);

        // Relatorio Anual
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_RelatAnual", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_RelatAnual(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, char simula, ref DATAHORA pdata_equipo, ref RELAT_ANUAL prelatorio, ref RELAT_ANUAL_ANALISE panalise, ref RELAT_ANUAL prelatorio_sim, ref RELAT_ANUAL_ANALISE panalise_sim);


        //
        // INDICADOR DE ECONOMIA
        //

        // GET: KPI Indicador de Economia
        public ActionResult KPI_IndEconomia(int IDCliente, int IDMedicao, int tipo_arquivo = 0, string Data = null)
        {
            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "KPI_IndEconomia");

            // data
            if( Data != null )
            {
                // pega data
                DateTime dateValue = DateTime.Parse(Data);

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Relat_Data", dateValue);

                // salva cookie tipoperiodo para diario
                CookieStore.SalvaCookie_Int("Relat_TipoPeriodo", 0);
            }

            // KPI indicador de economia
            return (KPI_Grafico_Show_PelaMedicao(IDCliente, IDMedicao, 0, tipo_arquivo));
        }

        // KPI Indicador de Economia
        private void KPI_IndEconomia_Diario(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_DIARIO relatorio = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise = new RELAT_DIARIO_ANALISE();

            RELAT_DIARIO relatorio_sim = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise_sim = new RELAT_DIARIO_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int TipoSimula = 0;

            // calcula valores
            retorno = SmCalcDB_Energia_RelatDiario((char)0, ref config_interface, (char)2, (char)TipoSimula, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var Consumo = new double[26];
            var EconomiaCons = new double[26];
            var EconomiaPorc = new double[26];
            var Economia = new double[26];
            var Periodo = new int[26];
            var Dias = new string[26];
            var DatasN = new DateTime[26];
            var Horas = new string[26];

            double Consumo_max_grafico = 0.0;

            // teste
            double ConsReferencia = 1500.0;


            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Horas[i] = strData.ToString("HH:mm");

                // proxima hora
                strData = strData.AddHours(1);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    Consumo[i] = relatorio.registro[0].valor1;
                    Periodo[i] = relatorio.registro[0].periodo;
                    EconomiaCons[i] = 0.0;
                    EconomiaPorc[i] = 0.0;
                    Economia[i] = 0.0;
                }

                // verifica se ultima barra
                if (i == 25)
                {
                    // zera
                    Consumo[i] = relatorio.registro[23].valor1;
                    Periodo[i] = relatorio.registro[23].periodo;
                    EconomiaCons[i] = 0.0;
                    EconomiaPorc[i] = 0.0;
                    Economia[i] = 0.0;
                }

                if (i >= 1 && i <= 24)
                {
                    // copia
                    j = i - 1;

                    Consumo[i] = ConsReferencia - relatorio.registro[j].valor1;
                    Periodo[i] = relatorio.registro[j].periodo;

                    // verifica se sem registro
                    if (relatorio.registro[j].periodo == 3)
                    {
                        Consumo[i] = 0.0;
                    }

                    // economia
                    EconomiaCons[i] = ConsReferencia - Consumo[i];
                    if (EconomiaCons[i] < 0.0 || EconomiaCons[i] >= ConsReferencia )
                    {
                        EconomiaCons[i] = 0.0;
                    }

                    // economia porcentagem
                    EconomiaPorc[i] = (EconomiaCons[i] / ConsReferencia) * 100.0;

                    // economia 
                    Economia[i] = EconomiaCons[i] * 1.234;


                    // verifica consumo maximo
                    if (Consumo[i] > Consumo_max_grafico)
                        Consumo_max_grafico = Consumo[i];
                }
            }

            if (ConsReferencia > Consumo_max_grafico)
                Consumo_max_grafico = ConsReferencia;

            Consumo_max_grafico = Consumo_max_grafico * 1.1;

            if (Consumo_max_grafico == 0.0)
            {
                Consumo_max_grafico = 10.0;
            }

            ViewBag.ConsMaxGrafico = Consumo_max_grafico;

            ViewBag.Consumo = Consumo;
            ViewBag.EconomiaCons = EconomiaCons;
            ViewBag.EconomiaPorc = EconomiaPorc;
            ViewBag.Economia = Economia;
            ViewBag.ConsReferencia = ConsReferencia;
            ViewBag.Periodo = Periodo;
            ViewBag.Dias = Dias;
            ViewBag.DatasN = DatasN;
            ViewBag.Horas = Horas;

            // nome do relatorio
            ViewBag.NomeRelat = string.Format("Consumo");
            ViewBag.PeriodoRelat = string.Format("Diário");

            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", DataAtual);

            // consumo
            double ConsP = analise.analise_valor1.consumo[0];
            double ConsFPI = analise.analise_valor1.consumo[1];
            double ConsFPC = analise.analise_valor1.consumo[2];
            double ConsTotal = analise.analise_valor1.consumo[0] + analise.analise_valor1.consumo[1] + analise.analise_valor1.consumo[2];

            if (ConsP < 100.0)
            {
                ViewBag.ConsP = string.Format("{0:#,##0.0}", ConsP);
            }
            else
            {
                ViewBag.ConsP = string.Format("{0:#,##0}", ConsP);
            }
            ViewBag.ConsPN = ConsP;

            if (ConsFPI < 100.0)
            {
                ViewBag.EconomiaConsFPI = string.Format("{0:#,##0.0}", ConsFPI);
            }
            else
            {
                ViewBag.ConsFPI = string.Format("{0:#,##0}", ConsFPI);
            }
            ViewBag.ConsFPIN = ConsFPI;

            if (ConsFPC < 100.0)
            {
                ViewBag.ConsFPC = string.Format("{0:#,##0.0}", ConsFPC);
            }
            else
            {
                ViewBag.ConsFPC = string.Format("{0:#,##0}", ConsFPC);
            }
            ViewBag.ConsFPCN = ConsFPC;

            if (ConsTotal < 100.0)
            {
                ViewBag.ConsTotal = string.Format("{0:#,##0.0}", ConsTotal);
            }
            else
            {
                ViewBag.ConsTotal = string.Format("{0:#,##0}", ConsTotal);
            }
            ViewBag.ConsTotalN = ConsTotal;

            

            // economia consumo
            double EconomiaConsP = analise.analise_valor1.consumo[0];
            double EconomiaConsFPI = analise.analise_valor1.consumo[1];
            double EconomiaConsFPC = analise.analise_valor1.consumo[2];
            double EconomiaConsTotal = analise.analise_valor1.consumo[0] + analise.analise_valor1.consumo[1] + analise.analise_valor1.consumo[2];


            //teste
            EconomiaConsP = (3 * ConsReferencia) - EconomiaConsP;
            if (EconomiaConsP < 0.0 || EconomiaConsP >= (3 * ConsReferencia))
            {
                EconomiaConsP = 0.0;
            }

            EconomiaConsFPI = (15 * ConsReferencia) - EconomiaConsFPI;
            if (EconomiaConsFPI < 0.0 || EconomiaConsFPI >= (15 * ConsReferencia))
            {
                EconomiaConsFPI = 0.0;
            }

            EconomiaConsFPC = (6 * ConsReferencia) - EconomiaConsFPC;
            if (EconomiaConsFPC < 0.0 || EconomiaConsFPC >= (6 * ConsReferencia))
            {
                EconomiaConsFPC = 0.0;
            }

            EconomiaConsTotal = EconomiaConsP + EconomiaConsFPI + EconomiaConsFPC;



            if (EconomiaConsP < 100.0)
            {
                ViewBag.EconomiaConsP = string.Format("{0:#,##0.0}", EconomiaConsP);
            }
            else
            {
                ViewBag.EconomiaConsP = string.Format("{0:#,##0}", EconomiaConsP);
            }
            ViewBag.EconomiaConsPN = EconomiaConsP;

            if (EconomiaConsFPI < 100.0)
            {
                ViewBag.EconomiaConsFPI = string.Format("{0:#,##0.0}", EconomiaConsFPI);
            }
            else
            {
                ViewBag.EconomiaConsFPI = string.Format("{0:#,##0}", EconomiaConsFPI);
            }
            ViewBag.EconomiaConsFPIN = EconomiaConsFPI;

            if (EconomiaConsFPC < 100.0)
            {
                ViewBag.EconomiaConsFPC = string.Format("{0:#,##0.0}", EconomiaConsFPC);
            }
            else
            {
                ViewBag.EconomiaConsFPC = string.Format("{0:#,##0}", EconomiaConsFPC);
            }
            ViewBag.EconomiaConsFPCN = EconomiaConsFPC;

            if (EconomiaConsTotal < 100.0)
            {
                ViewBag.EconomiaConsTotal = string.Format("{0:#,##0.0}", EconomiaConsTotal);
            }
            else
            {
                ViewBag.EconomiaConsTotal = string.Format("{0:#,##0}", EconomiaConsTotal);
            }
            ViewBag.EconomiaConsTotalN = EconomiaConsTotal;



            // economia porcentagem
            double EconomiaPorcP = analise.analise_valor1.consumo[0];
            double EconomiaPorcFPI = analise.analise_valor1.consumo[1];
            double EconomiaPorcFPC = analise.analise_valor1.consumo[2];
            double EconomiaPorcTotal = analise.analise_valor1.consumo[0] + analise.analise_valor1.consumo[1] + analise.analise_valor1.consumo[2];


            //teste
            EconomiaPorcP = EconomiaConsP / (3*ConsReferencia);
            EconomiaPorcFPI = EconomiaConsFPI / (15*ConsReferencia);
            EconomiaPorcFPC = EconomiaConsFPC / (6*ConsReferencia);
            EconomiaPorcTotal = EconomiaConsTotal / (24*ConsReferencia);



            ViewBag.EconomiaPorcP = string.Format("{0:#,##0.0}", EconomiaPorcP * 100);
            ViewBag.EconomiaPorcPN = EconomiaPorcP;

            ViewBag.EconomiaPorcFPI = string.Format("{0:#,##0.0}", EconomiaPorcFPI * 100);
            ViewBag.EconomiaPorcFPIN = EconomiaPorcFPI;

            ViewBag.EconomiaPorcFPC = string.Format("{0:#,##0.0}", EconomiaPorcFPC * 100);
            ViewBag.EconomiaPorcFPCN = EconomiaPorcFPC;

            ViewBag.EconomiaPorcTotal = string.Format("{0:#,##0.0}", EconomiaPorcTotal * 100);
            ViewBag.EconomiaPorcTotalN = EconomiaPorcTotal;



            // economia 
            double EconomiaP = analise.analise_valor1.consumo[0];
            double EconomiaFPI = analise.analise_valor1.consumo[1];
            double EconomiaFPC = analise.analise_valor1.consumo[2];
            double EconomiaTotal = analise.analise_valor1.consumo[0] + analise.analise_valor1.consumo[1] + analise.analise_valor1.consumo[2];

            //teste
            EconomiaP = EconomiaConsP * 1.234;
            EconomiaFPI = EconomiaFPI * 0.981;
            EconomiaFPI = EconomiaFPC * 0.981;
            EconomiaTotal = EconomiaP + EconomiaFPI + EconomiaFPC;


            ViewBag.EconomiaP = string.Format("{0:C}", EconomiaP);
            ViewBag.EconomiaPN = EconomiaP;

            ViewBag.EconomiaFPI = string.Format("{0:C}", EconomiaFPI);
            ViewBag.EconomiaFPIN = EconomiaFPI;

            ViewBag.EconomiaFPC = string.Format("{0:C}", EconomiaFPC);
            ViewBag.EconomiaFPCN = EconomiaFPC;

            ViewBag.EconomiaTotal = string.Format("{0:C}", EconomiaTotal);
            ViewBag.EconomiaTotalN = EconomiaTotal;





            //
            // Observacoes
            //

            // le tags
            ListaTiposMetodos listaMetodos = new ListaTiposMetodos();
            List<ObservacaoTagsDominio> tags = listaMetodos.ListarTodos_ObservacaoTags();
            ViewBag.tags = tags;

            // observacao
            ObservacaoMedicaoDominio observacao = new ObservacaoMedicaoDominio();
            ViewBag.observacao = observacao;

            // observacoes
            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(1);

            ObservacaoMedicaoMetodos observacaoMetodos = new ObservacaoMedicaoMetodos();
            List<ObservacaoMedicaoDominio> observacoes = observacaoMetodos.ListarPorPeriodo(IDMedicao, data_hora_ini, data_hora_fim);
            ViewBag.Observacoes = observacoes;

            return;
        }

        // KPI Indicador de Economia Diario XLS
        private HSSFWorkbook KPI_IndEconomia_Diario_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Período", "Consumo (kWh)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 25; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _datahoraStyle);

                // periodo
                numeroCelulaXLS(row, 1, ViewBag.Periodo[i], _intCellStyle);

                // consumo
                numeroCelulaXLS(row, 2, ViewBag.Consumo[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Consumo", "Relatório Diário", _negritoCellStyle);

            // cabecalho ponta, fora de ponta e total
            string[] cab_cons = { "Consumo (kWh)", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta", "Total" };
            cabecalhoTabelaXLS(workbook, sheet, cab_cons, rowIndex++);

            // consumo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Consumo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, ViewBag.ConsFPCN, _1CellStyle);
            numeroCelulaXLS(row, 2, ViewBag.ConsFPIN, _1CellStyle);
            numeroCelulaXLS(row, 3, ViewBag.ConsPN, _1CellStyle);
            numeroCelulaXLS(row, 4, ViewBag.ConsTotalN, _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

    }
}