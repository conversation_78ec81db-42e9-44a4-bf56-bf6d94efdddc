﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class KpiController
    {

        // GET: KPI - Historico
        public ActionResult KPI_Historico(int idKPI)
        {
            // tela de ajuda - Historico KPI
            CookieStore.SalvaCookie_String("PaginaAjuda", "KPI_Historicos");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_KPI();

            // le KPI
            KPIConfiguracaoMetodos kpiMetodos = new KPIConfiguracaoMetodos();
            KPIConfiguracaoDominio kpi = kpiMetodos.ListarPorId(idKPI);

            ViewBag.IDKPI = kpi.IDKPI;
            ViewBag.Nome = kpi.Nome;
            ViewBag.UnidadeGrandeza = kpi.UnidadeGrandeza;
            ViewBag.IDTipoRazao = kpi.IDTipoRazao;

            // le historico de KPIs
            KPIHistoricoMetodos kpiHistMetodos = new KPIHistoricoMetodos();
            List<KPIHistoricoDominio> listaHistKPI = kpiHistMetodos.ListarPorIDKPI(idKPI);
            return View(listaHistKPI);
        }

        // GET: KPI - Historico - Editar
        public ActionResult KPI_Historico_Editar(int idKPI, int idHistKPI)
        {
            // tela de ajuda - Historico KPI
            CookieStore.SalvaCookie_String("PaginaAjuda", "KPI_HistoricosEditar");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_KPI();

            // le KPI
            KPIConfiguracaoMetodos kpiMetodos = new KPIConfiguracaoMetodos();
            KPIConfiguracaoDominio kpi = kpiMetodos.ListarPorId(idKPI);

            ViewBag.Nome = kpi.Nome;
            ViewBag.UnidadeGrandeza = kpi.UnidadeGrandeza;
            ViewBag.IDTipoRazao = kpi.IDTipoRazao;

            // verifica se adicionando
            KPIHistoricoDominio temp = new KPIHistoricoDominio();
            if (idHistKPI == 0)
            {
                // zera com default
                temp.IDHistKPI = 0;
                temp.IDCliente = ViewBag._IDCliente;
                temp.IDKPI = idKPI;
                temp.Data = new DateTime(DateTime.Now.Year,DateTime.Now.Month,1,0,0,0);
                temp.valorKPI = 0.0;
            }
            else
            {
                // le historico
                KPIHistoricoMetodos kpiHistMetodos = new KPIHistoricoMetodos();
                temp = kpiHistMetodos.ListarPorIdHistKPI(idHistKPI);
            }

            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy HH:mm}", temp.Data);

            return View(temp);
        }

        // POST: KPI - Historico - Salvar
        [HttpPost]
        public ActionResult KPI_Historico_Salvar(KPIHistoricoDominio temp)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro registro historico com a mesma data
            KPIHistoricoMetodos kpiHistMetodos = new KPIHistoricoMetodos();
            if (kpiHistMetodos.VerificarDuplicidade(temp))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Data existente."
                };
            }
            else
            {
                // ajusta data
                temp.Data = new DateTime(temp.Data.Year, temp.Data.Month, temp.Data.Day, temp.Data.Hour, temp.Data.Minute, 0);

                // salva historico
                kpiHistMetodos.Salvar(temp);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: KPI - Historico - Excluir
        public ActionResult KPI_Historico_Excluir(int idHistKPI)
        {
            // historico KPI
            KPIHistoricoMetodos kpiHistMetodos = new KPIHistoricoMetodos();
            kpiHistMetodos.Excluir(idHistKPI);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}