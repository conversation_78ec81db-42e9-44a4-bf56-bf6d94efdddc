﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {
        //
        // TEMPO DE ATUAÇÃO SAIDAS DIGITAIS
        //

        // GET: Tempo de Atuação - Saidas Digitais Mensal
        public ActionResult Relat_TempoAtuacao_SaidasDigitais_Mensal(int IDCliente)
        {
            // tipo do relatorio
            int TipoRelat = TIPO_RELAT.TempoAtuacao_SaidasDigitais_Mensal;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "TempoAtuacao_Mensal");
            CookieStore.SalvaCookie_Int("Relat_Tipo", TipoRelat);

            // le cookies
            LeCookies_SmartEnergy();

            // ano atual
            DateTime datahora_ultima = DateTime.Now;
            DATAHORA data_hora = new DATAHORA();

            // le cookie datahora (atual ou inicial)
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            // verifica se existe datahora no cookie
            if (datahora_cookie.Year != 2000)
            {
                // copia data do cookie para ultima
                datahora_ultima = datahora_cookie;
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_ultima);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", datahora_ultima);

            // tipo do relatorio
            ViewBag.TipoRelat = TipoRelat;

            // seta para mensal
            ViewBag.Relat_TipoPeriodo = TIPO_RELAT_PERIODO.Mensal;

            // relatorios
            List<TempoAtuacaoMensal> temposAtuacao = new List<TempoAtuacaoMensal>();
            ViewBag.temposAtuacao = temposAtuacao;

            // lista de erros
            var listaErros = new List<string>();

            // erros
            ViewBag.listaErros = listaErros;

            return View();
        }

        // GET: Relatorio Grafico
        private ActionResult TempoAtuacao_SaidasDigitais_Mensal_Show(int IDCliente, int tipo_arquivo = 0)
        {
            // copia tipo de arquivo
            ViewBag.TipoArquivo = tipo_arquivo;

            // tipo do relatorio
            int TipoRelat = TIPO_RELAT.TempoAtuacao_SaidasDigitais_Mensal;

            // salva cookie 
            CookieStore.SalvaCookie_Int("Relat_Tipo", TipoRelat);

            // le cookies
            LeCookies_SmartEnergy();

            // ano atual
            DateTime datahora_ultima = DateTime.Now;
            DATAHORA data_hora = new DATAHORA();

            // le cookie datahora (atual ou inicial)
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            // verifica se existe datahora no cookie
            if (datahora_cookie.Year != 2000)
            {
                // copia data do cookie para ultima
                datahora_ultima = datahora_cookie;
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_ultima);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // tipo do relatorio
            ViewBag.TipoRelat = TipoRelat;

            // View do relatorio
            string viewRelatorio = "_TempoAtuacao_SaidasDigitais_Mensal";

            // calcula
            Calc_TempoAtuacao_SaidasDigitais_Mensal(IDCliente, data_hora);


            // verifica se PDF
            if (tipo_arquivo == 1)
            {
                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.pdf", viewRelatorio, IDCliente, data_atual);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Landscape,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna nome do arquivo
                var returnedData = new
                {
                    nomeArquivo = nomeArquivo
                };

                

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // verifica se XLS
            if (tipo_arquivo == 2)
            {
                // Planilha Excel
                var workbook = new HSSFWorkbook();

                // monta XLS
                workbook = TempoAtuacao_SaidasDigitais_Mensal_XLS(IDCliente);

                // gera identificador unico aonde o arquivo sera guardado
                string handle = Guid.NewGuid().ToString();

                using (MemoryStream memoryStream = new MemoryStream())
                {
                    workbook.Write(memoryStream);
                    memoryStream.Position = 0;
                    Session[handle] = memoryStream.ToArray();
                }

                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.xls", viewRelatorio, IDCliente, data_atual);

                // retorna nome do arquivo
                var returnedData = new
                {
                    FileGuid = handle,
                    FileName = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // verifica se EMAIL
            if (tipo_arquivo == 3)
            {
                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.pdf", viewRelatorio, IDCliente, data_atual);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Portrait,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna 
                ViewBag.caminhoCompleto = caminhoCompleto;
                var returnedData = new
                {
                    caminhoCompleto = caminhoCompleto
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            return View();
        }

        // Tempo de Atuação - Saidas Digitais
        private void Calc_TempoAtuacao_SaidasDigitais_Mensal(int IDCliente, DATAHORA datahora)
        {

            // le cookies
            LeCookies_SmartEnergy();

            // le gateways
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            List<GatewaysDominio> gateways = gatewaysMetodos.ListarPorIDCliente(IDCliente);

            List<TempoAtuacaoMensal> temposAtuacao = new List<TempoAtuacaoMensal>();

            // lista de erros
            var listaErros = new List<string>();

            // percorre gateways
            if (gateways != null)
            {
                foreach (GatewaysDominio gateway in gateways)
                {
                    // verifica se nao eh gate X e nem gate C
                    if (gateway.IDTipoGateway != TIPO_GATEWAY.GATE_X_421 && gateway.IDTipoGateway != TIPO_GATEWAY.GATE_X_422 && gateway.IDTipoGateway != TIPO_GATEWAY.GATE_C)
                    {
                        continue;
                    }

                    // saidas digitais
                    SaidasDigitaisMetodos sdMetodos = new SaidasDigitaisMetodos();
                    List<SaidasDigitaisDominio> saidas = sdMetodos.ListarPorIDGateway(gateway.IDGateway);

                    // percorre saidas
                    if (saidas != null)
                    {
                        foreach (SaidasDigitaisDominio sd in saidas)
                        {
                            // calcula valores
                            EV_Metodos EVMetodos = new EV_Metodos();
                            DateTime data = Funcoes_Converte.ConverteDataHora2DateTime(datahora);

                            // calcula valores | Evento 11 = Lista eventos Saídas Digitais
                            TempoAtuacaoMensal tempoAtuacao = EVMetodos.ListarTempoAtuacaoMensal(IDCliente, gateway.IDGateway, sd.NumSaidaGateway, data, 11);

                            // IDGateway e nome Gateway
                            tempoAtuacao.IDGateway = gateway.IDGateway;
                            tempoAtuacao.Nome = gateway.Nome;

                            // descricao saída
                            tempoAtuacao.DescrEntrada = "";
                            tempoAtuacao.DescrSaida = sd.Descricao;

                            temposAtuacao.Add(tempoAtuacao);
                        }
                    }                    
                }
            }

            // numero de dias do mes
            int NumDias = DateTime.DaysInMonth(datahora.data.ano, datahora.data.mes);
            ViewBag.NumDias = NumDias;

            // relatorios
            ViewBag.temposAtuacao = temposAtuacao;

            // erros
            ViewBag.listaErros = listaErros;

            // converte data e hora
            DateTime DataAtual = Funcoes_Converte.ConverteDataHora2DateTime(datahora);

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:MM/yyyy}", DataAtual);

            // tipo do relatorio
            ViewBag.TipoRelat = TIPO_RELAT.TempoAtuacao_SaidasDigitais_Mensal;

            // seta para mensal
            ViewBag.Relat_TipoPeriodo = TIPO_RELAT_PERIODO.Mensal;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.TempoAtuacao + " - " + @SmartEnergy.Resources.ConfiguracaoTexts.SaidasDigitais;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Mensal;

            return;
        }

        // Tempo de Atuação - Saidas Digitais Mensal XLS
        private HSSFWorkbook TempoAtuacao_SaidasDigitais_Mensal_XLS(int IDCliente)
        {
            // cria excel
            HSSFWorkbook workbook = new HSSFWorkbook();

            // planilhas
            TempoAtuacao_SaidasDigitais_Mensal_Planilha(workbook);

            // retorna planilha
            return workbook;
        }

        // Tempo de Atuação - Saidas Digitais Mensal Planilha
        private void TempoAtuacao_SaidasDigitais_Mensal_Planilha(HSSFWorkbook workbook)
        {
            List<TempoAtuacaoMensal> temposAtuacao = ViewBag.temposAtuacao;

            int NumDias = ViewBag.NumDias;

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _datahorafullStyle = criaEstiloXLS(workbook, 13);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            // cria planilha
            string str_planilha = "Tempo de Atuação - Saídas Digitais";

            ISheet sheet = workbook.CreateSheet(str_planilha);
            int rowIndex = 0;

            // cabecalho
            string[] cabecalho = new string[34];

            cabecalho[0] = "Gateways";
            cabecalho[1] = "Saídas Digitais";
            cabecalho[2] = "Descrição";

            // dias
            for (int i = 0; i < 31; i++)
            {
                string str_valor = "---";

                if (i < NumDias)
                {
                    str_valor = string.Format("{0:00}/{1}", i + 1, ViewBag.DataAtual);
                }

                cabecalho[i + 3] = str_valor;
            }

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            IRow row;

            if (temposAtuacao != null)
            {
                foreach (TempoAtuacaoMensal tempoAtuacao in temposAtuacao)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // nome gateway
                    textoCelulaXLS(row, 0, tempoAtuacao.Nome);

                    // descricao saída
                    textoCelulaXLS(row, 1, tempoAtuacao.DescrSaida);

                    // tempo ligado
                    textoCelulaXLS(row, 2, "Tempo Ligado (HH:mm:ss)");

                    // dias
                    for (int i = 0; i < 31; i++)
                    {
                        string ligado = "---";

                        if (i < NumDias)
                        {
                            int ligado_hora = 0;

                            int ligado_min = 0;

                            int ligado_seg = 0;

                            ligado_seg = (int)(tempoAtuacao.TempoAtuacaoMensalValor[i].tempoLigado % 60);

                            ligado_min = (int)(((tempoAtuacao.TempoAtuacaoMensalValor[i].tempoLigado - ligado_seg) / 60) % 60);

                            ligado_hora = (int)(((tempoAtuacao.TempoAtuacaoMensalValor[i].tempoLigado - ligado_min) / 60) / 60);

                            ligado = string.Format("{0:00}:{1:00}:{2:00}", ligado_hora, ligado_min, ligado_seg);
                        }

                        // valor
                        textoCelulaXLS(row, i + 3, ligado, _intCellStyle);
                    }

                    // adiciona linha
                    row = sheet.CreateRow(rowIndex + 1);

                    // tempo desligado
                    textoCelulaXLS(row, 2, "Tempo Desligado (HH:mm:ss)");

                    // dias
                    for (int i = 0; i < 31; i++)
                    {
                        string desligado = "---";

                        if (i < NumDias)
                        {
                            int desligado_hora = 0;

                            int desligado_min = 0;

                            int desligado_seg = 0;

                            desligado_seg = (int)(tempoAtuacao.TempoAtuacaoMensalValor[i].tempoDesligado % 60);

                            desligado_min = (int)(((tempoAtuacao.TempoAtuacaoMensalValor[i].tempoDesligado - desligado_seg) / 60) % 60);

                            desligado_hora = (int)(((tempoAtuacao.TempoAtuacaoMensalValor[i].tempoDesligado - desligado_min) / 60) / 60);

                            desligado = string.Format("{0:00}:{1:00}:{2:00}", desligado_hora, desligado_min, desligado_seg);
                        }

                        // valor
                        textoCelulaXLS(row, i + 3, desligado, _intCellStyle);
                    }

                    // proxima
                    rowIndex += 2;
                }

                // mescla duas celulas para o nome da gateway e duas celulas para a descricao da saida
                for (int i = 0; i < temposAtuacao.Count; i++)
                {
                    sheet.AddMergedRegion(new CellRangeAddress(i * 2 + 1, i * 2 + 2, 0, 0));
                    sheet.AddMergedRegion(new CellRangeAddress(i * 2 + 1, i * 2 + 2, 1, 1));
                }     

            }

            // largura de cada coluna
            sheet.SetColumnWidth(0, 6000);
            for (int i = 1; i < sheet.GetRow(0).LastCellNum; i++)
            {
                if (i < 3)
                {
                    sheet.SetColumnWidth(i, 7000);
                }

                else
                {
                    sheet.SetColumnWidth(i, 4000);
                }                
            }

            return;
        }
    }
}
