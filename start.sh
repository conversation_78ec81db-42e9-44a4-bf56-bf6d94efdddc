#!/bin/bash

# Script para inicializar o ambiente Docker do SmartEnergy

echo "=== Iniciando SmartEnergy Docker Environment ==="

# Verificar se Docker e Docker Compose estão instalados
if ! command -v docker &> /dev/null; then
    echo "❌ Docker não está instalado. Por favor, instale o Docker primeiro."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose não está instalado. Por favor, instale o Docker Compose primeiro."
    exit 1
fi

# Criar diretórios necessários
echo "📁 Criando diretórios necessários..."
mkdir -p data/sqlserver
mkdir -p data/redis
mkdir -p logs/app
mkdir -p logs/nginx
mkdir -p docker/nginx/ssl

# Verificar se é Linux ou Windows
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    COMPOSE_FILE="docker/docker-compose.linux.yml"
    echo "🐧 Detectado sistema Linux - usando Mono"
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
    COMPOSE_FILE="docker/docker-compose.yml"
    echo "🪟 Detectado sistema Windows - usando Windows Containers"
else
    echo "⚠️  Sistema não detectado, usando configuração Linux por padrão"
    COMPOSE_FILE="docker/docker-compose.linux.yml"
fi

# Parar containers existentes
echo "🛑 Parando containers existentes..."
docker-compose -f $COMPOSE_FILE down

# Construir e iniciar containers
echo "🔨 Construindo e iniciando containers..."
docker-compose -f $COMPOSE_FILE up --build -d

# Aguardar inicialização do SQL Server
echo "⏳ Aguardando inicialização do SQL Server..."
sleep 30

# Verificar status dos containers
echo "📊 Status dos containers:"
docker-compose -f $COMPOSE_FILE ps

# Mostrar logs se houver erro
if [ $? -ne 0 ]; then
    echo "❌ Erro ao iniciar containers. Mostrando logs:"
    docker-compose -f $COMPOSE_FILE logs
    exit 1
fi

echo ""
echo "✅ SmartEnergy iniciado com sucesso!"
echo ""
echo "🌐 Acesse a aplicação em:"
echo "   - Aplicação: http://localhost:8080"
echo "   - Nginx (proxy): http://localhost"
echo "   - SQL Server: localhost:1433"
echo "   - Redis: localhost:6379"
echo ""
echo "📊 Para ver logs em tempo real:"
echo "   docker-compose -f $COMPOSE_FILE logs -f"
echo ""
echo "🛑 Para parar o ambiente:"
echo "   docker-compose -f $COMPOSE_FILE down"
echo ""
echo "🔄 Para reiniciar:"
echo "   docker-compose -f $COMPOSE_FILE restart"
