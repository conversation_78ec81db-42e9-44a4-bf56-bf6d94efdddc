﻿<!--
    You can find existing browser definitions at
    <windir>\Microsoft.NET\Framework\<ver>\CONFIG\Browsers
    
    File per permettere a wkhtmltopdf di inviare richiete con cookie
-->
<browsers>
  <browser id="Qt" parentID="Safari">
    <identification>
      <userAgent match="Qt/(?'version'(?'major'\d+)(\.(?'minor'\d+)?)\w*)" />
    </identification>

          <!--<capture>
            <userAgent match="NewBrowser (?'version'\d+\.\d+)" />
        </capture>-->

        <capabilities>
          <capability name="browser"                         value="Qt" />
          <capability name="version"                         value="${version}" />
          <capability name="majorversion"                    value="${major}" />
          <capability name="minorversion"                    value="${minor}" />
          <capability name="type"                            value="Qt${major}" />
          <capability name="ecmascriptversion"               value="3.0" />
          <capability name="javascript"                      value="true" />
          <capability name="javascriptversion"               value="1.7" />
          <capability name="w3cdomversion"                   value="1.0" />
          <capability name="tagwriter"                       value="System.Web.UI.HtmlTextWriter" />
          <capability name="cookies"                         value="true" />
          <capability name="frames"                          value="true" />
          <capability name="javaapplets"                     value="true" />
          <capability name="supportsAccesskeyAttribute"      value="true" />
          <capability name="supportsCallback"                value="true" />
          <capability name="supportsDivNoWrap"               value="false" />
          <capability name="supportsFileUpload"              value="true" />
          <capability name="supportsMaintainScrollPositionOnPostback" value="true" />
          <capability name="supportsMultilineTextBoxDisplay" value="true" />
          <capability name="supportsXmlHttp"                 value="true" />
          <capability name="tables"                          value="true" />
        </capabilities>
    </browser>

    <!--<browser refID="Mozilla">
        <capabilities>
            <capability name="xml" value="true" />
        </capabilities>
    </browser>-->
</browsers>
