﻿@using System.Globalization
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = @SmartEnergy.Resources.RelatoriosTexts.PainelJBSDemanda;
}

<style>
    .overlay_aguarde {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.45);
        z-index: 9999;
        color: white;
        display: inline-block;
        border-radius: 6px;
        margin-bottom: 20px;
        margin-left: 14px;
        margin-right: 14px;
    }

    .spinner-aguarde .sk-spinner-wave div {
        background-color: #ffffff !important;
    }

    .spinner-aguarde {
        margin: 0;
        position: absolute;
        top: 50%;
        left: 50%;
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
    }
</style>

<div class="wrapper wrapper-content">

    <div class="row">
        <div class="col-lg-12">

            @{
                <div class="panel panel-title relatorio" id="atualizar">
                    @Html.Partial("_PainelJBS_Demanda_Atualizar")
                </div>
                
                <div class="overlay_aguarde" style="display: none">
                    <div class="spinner-aguarde">
                        <div class="sk-spinner sk-spinner-wandering-cubes">
                            <div class="sk-spinner sk-spinner-wave">
                            <div class="sk-rect1 text-ligh"></div>
                            <div class="sk-rect2"></div>
                            <div class="sk-rect3"></div>
                            <div class="sk-rect4"></div>
                            <div class="sk-rect5"></div>
                            </div>
                        </div>
                    </div>
                </div>
            }

        </div>
    </div>

    @if (@ViewBag.listaErros != null)
    { 
        if (@ViewBag.listaErros.Count > 0)
        {
            <div class="row">
                <div class="col-lg-12">
                    <div class="panel panel-danger">
                        <div class="panel-heading">
                            <h4>@SmartEnergy.Resources.SupervisaoTexts.Erros</h4><br />
                            <ul style="list-style-type:disc; margin-left: 20px;">
                                @foreach (var erro in @ViewBag.listaErros)
                                {
                                    <li>@erro</li>
                                }
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        }
    }
        
</div>

@section Styles {
    @Styles.Render("~/plugins/c3Styles")
    @Styles.Render("~/datetimePickerStyles")
    @Styles.Render("~/Content/Relatorios.css")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/select2Styles")
    @Styles.Render("~/Scripts/plugins/jquery-ui/jqueryuiStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/d3")
    @Scripts.Render("~/plugins/c3")
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/plugins/iCheck")
    @Scripts.Render("~/plugins/waitingFor")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/select2")
    @Scripts.Render("~/bundles/jqueryui")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
        <script type="text/javascript" language="javascript" src="~/Scripts/locales/bootstrap-datepicker.es.min.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
        <script type="text/javascript" language="javascript" src="~/Scripts/locales/bootstrap-datepicker.pt-BR.min.js"></script>
    }

    <script type="text/javascript">

        $(document).ready(function () {

            // atualiza
            Atualiza(0);

        });

        function Atualiza(navega) {
            
            var IDMedicao = parseInt(@ViewBag._IDMedicao);
            var IDCliente = parseInt(@ViewBag._IDCliente);

            // data nao utilizada
            var data = "01/01/2000";

            // aguarde
            $('.overlay_aguarde').toggle();

            $.ajax(
            {
                type: 'GET',
                url: '/PainelJBS/_PainelJBS_Demanda_Atualizar',
                dataType: 'html',
                data: { 'IDCliente': IDCliente, 'IDMedicao':IDMedicao, 'Navegacao': navega, 'Data':data },
                cache: false,
                async: true,
                success: function (data) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    // apresenta
                    $('#atualizar').html(data);
                },
                error: function (xhr, status, error) {

                    // fim
                    $('.overlay_aguarde').toggle();

                }
            });
        }

    </script>

}






