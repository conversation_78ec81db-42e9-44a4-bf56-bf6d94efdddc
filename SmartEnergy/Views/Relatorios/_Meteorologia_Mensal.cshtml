﻿<div class="row">
    <div class="col-lg-4 col-md-12">
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ea">
                        <h4>@ViewBag.NomeGrandeza</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.MediaMes</h5>
                        <h1>@ViewBag.VMedMes <span>@ViewBag.UnidadeGrandeza</span></h1>
                        <h6>@ViewBag.DataTextoAtual</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-preto">
                        <h4>@ViewBag.NomeGrandeza</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.Maxima</h5>
                        <h1>@ViewBag.VMaxMes <span>@ViewBag.UnidadeGrandeza</span></h1>
                        <h6>@ViewBag.VMaxMes_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
        <br /><br />
        <div class="row">
            <div class="col-lg-offset-6 col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-preto">
                        <h4>@ViewBag.NomeGrandeza</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.Minima</h5>
                        <h1>@ViewBag.VMinMes <span>@ViewBag.UnidadeGrandeza</span></h1>
                        <h6>@ViewBag.VMinMes_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-8 col-md-12">
        <div class="row">
            <div class="relat-grafico">
                <div id="grafico-ea" style="margin-top: -10px"></div>
                <div class='chart-legend' style="display:none;">
                    <div class='legend-scale-dem'>
                        <ul class='legend-labels' style="margin-top: -25px;">
                            <li><span style='background:#1C84C6;'></span>@ViewBag.NomeGrandeza</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@Html.Partial("_Relat_Botoes")

<br />
<div class="panel panel-title relatorio">
    <div class="panel-body">

        <div class="row">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-ea" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.MediaMes</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Maxima</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Minima</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@ViewBag.NomeGrandeza</td>
                                <td class="relat-ea">@ViewBag.VMedMes @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.DataTextoAtual)</small></td>
                                <td class="relat-preto">@ViewBag.VMaxMes @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMaxMes_DataHora)</small></td>
                                <td class="relat-preto">@ViewBag.VMinMes @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMinMes_DataHora)</small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal inmodal animated fadeIn" id="modalTabela" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-editar-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">@SmartEnergy.Resources.ComumTexts.BotaoFechar</span></button>
                <h4 class="modal-title"><i class="fa fa-list"></i>&nbsp;&nbsp;@SmartEnergy.Resources.RelatoriosTexts.TabelaRegistros</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-default">
                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Dia</th>
                                <th style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Media<br />(@ViewBag.UnidadeGrandeza)</th>
                                <th style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Maxima<br />(@ViewBag.UnidadeGrandeza)</th>
                                <th style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Minima<br />(@ViewBag.UnidadeGrandeza)</th>
                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var i = 0;
                                var j = 0;
                                var tempMed = "---";
                                var tempMax = "---";
                                var tempMin = "---";

                                var NumDiasMes = ViewBag.NumDiasMes;

                                for (i = 0; i < NumDiasMes; i++)
                                {
                                    j = i + 1;

                                    if (ViewBag.PossuiRegistros[j])
                                    {
                                        tempMed = string.Format("{0:0}", ViewBag.TempMed[j]);
                                        tempMax = string.Format("{0:0}", ViewBag.TempMax[j]);
                                        tempMin = string.Format("{0:0}", ViewBag.TempMin[j]);
                                    }
                                    else
                                    {
                                        tempMed = "---";
                                        tempMax = "---";
                                        tempMin = "---";
                                    }

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="relat-ea">@ViewBag.Dias[j]</td>
                                        <td style="text-align:center;" class="relat-ea">@tempMed</td>
                                        <td style="text-align:center;" class="relat-ea">@tempMax</td>
                                        <td style="text-align:center;" class="relat-ea">@tempMin</td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
            </div>
        </div>
    </div>
</div>


@Html.Partial("_EventosModal")

@Html.Partial("_ObservacoesModal")


<script type="text/javascript">

    $(document).ready(function () {

        // meteorologia
        var dataX = [];
        var labelX = [];
        var dataMaxima = [];
        var dataMedia = [];
        var dataMinima = [];

        // copia valores
        var TempMax = @Html.Raw(Json.Encode(@ViewBag.TempMax));
        var TempMed = @Html.Raw(Json.Encode(@ViewBag.TempMed));
        var TempMin = @Html.Raw(Json.Encode(@ViewBag.TempMin));
        var PossuiRegistros = @Html.Raw(Json.Encode(@ViewBag.PossuiRegistros));
        var ClimaMadrugada = @Html.Raw(Json.Encode(@ViewBag.ClimaMadrugada));
        var ClimaManha = @Html.Raw(Json.Encode(@ViewBag.ClimaManha));
        var ClimaTarde = @Html.Raw(Json.Encode(@ViewBag.ClimaTarde));
        var ClimaNoite = @Html.Raw(Json.Encode(@ViewBag.ClimaNoite));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));
        var NumDiasMes = @Html.Raw(Json.Encode(@ViewBag.NumDiasMes));
        var NomeGrandeza = @Html.Raw(Json.Encode(@ViewBag.NomeGrandeza));
        var UnidadeGrandeza = @Html.Raw(Json.Encode(@ViewBag.UnidadeGrandeza));

        // valor minimo e máximo
        var minimoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMin));
        var maximoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMax));

        dataX.push('x');
        dataMaxima.push(['data1']);
        dataMedia.push(['data2']);
        dataMinima.push(['data3']);

        for (i = 0; i < NumDiasMes+2; i++)
        {
            // X
            dataX.push(Datas[i]);

            dataMaxima.push([TempMax[i]]);
            dataMedia.push([TempMed[i]]);
            dataMinima.push([TempMin[i]]);
        }

        // valores label X
        labelX.push(Datas[1]);
        for (i = 5; i <= 25; i+=5)
        {
            labelX.push(Datas[i]);
        }
        labelX.push(Datas[NumDiasMes]);

        var Data = [dataX,dataMinima,dataMedia,dataMaxima];

        c3.generate({

            bindto: '#grafico-ea',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'bar',
                    data3: 'bar',
                },
                colors: {
                    data1: '#00007f',
                    data2: '#4284B2',
                    data3: '#D7E8F8',
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // retorna dia
                            return x.getDate();
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    // Grafico 1 dia     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(11 horas)
                    padding: -1000*60*60*11
                },
                y:  {
                    min: 0,
                    max: maximoValor,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {
                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.15 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < 4; i++) {

                        // if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        if (! text) {

                            // split string and create array.
                            var arr = Datas[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            title = DIA + " " + data.getDate();

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1C84C6'>" + title + "</th></tr>" : "");
                        }

                        var str_valor = "---";                        

                        switch(i)
                        {
                            case 0:     // minimo
                                name = MINIMO;
                                break;

                            case 1:     // medio
                                name = MEDIO;
                                break;

                            case 2:     // maximo
                                name = MAXIMO;
                                break;

                            case 3:     // clima
                                name = CLIMA;

                                break;

                        }

                        if (i < 3)
                        {
                            value = d[i].value;                            

                            if (PossuiRegistros[d[i].index] == true)
                            {
                                str_valor = value.toFixed(0) + " " + UnidadeGrandeza;
                            }

                            bgcolor = $$.levelColor ? $$.levelColor(d[i].value) : color(d[i].id);

                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                            text += "<td class='value'>" + str_valor + "</td>";
                            text += "</tr>";
                        }
                        else if (i==3)
                        {
                            // array para preencher madrugada / manha / tarde / noite
                            for (var j = 0; j < 4; j++)
                            {
                                var horario = -1;
                                var str = "---";

                                if (j == 0)
                                {
                                    horario = ClimaMadrugada[d[0].index];
                                    name = MADRUGADA;
                                }
                                else if (j == 1)
                                {
                                    horario = ClimaManha[d[0].index];
                                    name = MANHA;
                                }
                                else if (j == 2)
                                {
                                    horario = ClimaTarde[d[0].index];
                                    name = TARDE;
                                }
                                else if (j == 3)
                                {
                                    horario = ClimaNoite[d[0].index];
                                    name = NOITE;
                                }

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'>" + name + "</td>";

                                switch(horario)
                                {
                                    case 0:     // 0 = clear-day 
                                        str = CEU_CLARO;
                                        icone_tempo = 'icone_dia_claro16.png';
                                        break;

                                    case 1:     // 1 = clear-night
                                        str = NOITE_CLARA;
                                        icone_tempo = 'icone_noite_clara16.png';
                                        break;

                                    case 2:     // 2 = partly-cloudy-day
                                        str = PARC_NUBLADO;
                                        icone_tempo = 'icone_dia_nublado16.png';
                                        break;

                                    case 3:     // 3 = partly-cloudy-night
                                        str = PARC_NUBLADO;
                                        icone_tempo = 'icone_noite_nublada16.png';
                                        break;

                                    case 4:     // 4 = cloudy
                                        str = NUBLADO;
                                        icone_tempo = 'icone_nublado16.png';
                                        break;

                                    case 5:     // 5 = rain
                                        str = CHUVOSO;
                                        icone_tempo = 'icone_chuva16.png';
                                        break;

                                    case 6:     // 6 = sleet (granizo)
                                        str = GRANIZO;
                                        icone_tempo = 'icone_granizo16.png';
                                        break;

                                    case 7:     // 7 = snow
                                        str = NEVE;
                                        icone_tempo = 'icone_neve16.png';
                                        break;

                                    case 8:     // 8 = wind
                                        str = VENTO;
                                        icone_tempo = 'icone_vento16.png';
                                        break;

                                    case 9:     // 9 = fog
                                        str = NEVOEIRO;
                                        icone_tempo = 'icone_neblina16.png';
                                        break;

                                    default:
                                        str = '---';
                                        icone_tempo = '';
                                        break;
                                }

                                if (str == '---')
                                {
                                    text += "<td class='value'>" + str + "</td>";
                                }
                                else
                                {
                                    text += "<td class='value'>" + str + "<br><img src='/Imagens/Tempo/" + icone_tempo + "' /></td>";
                                }    
                                
                                text += "</tr>";
                            }

                            
                        }
                    }
                    return text + "</table>";
                }
            }
        });

        // labels
        $('.chart-legend').css("display", "block");

        $(".modal-dialog").draggable({
            handle: ".modal-editar-header"
        });
    });

</script>
