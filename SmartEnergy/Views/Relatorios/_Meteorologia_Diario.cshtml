﻿@using SmartEnergyLib.Declaracoes;

<div class="row">
    <div class="col-lg-4 col-md-12">
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ea">
                        <h4>@ViewBag.NomeGrandeza</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.MediaDia</h5>
                        <h1>@ViewBag.VMedDia <span>@ViewBag.UnidadeGrandeza</span></h1>
                        <h6>@ViewBag.DataAtual</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-preto">
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.Maxima</h5>
                        <h1>@ViewBag.VMaxDia <span>@ViewBag.UnidadeGrandeza</span></h1>
                        <h6>@ViewBag.VMaxDia_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
        <br /><br />
        <div class="row">
            <div class="col-lg-offset-6 col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-preto">
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.Minima</h5>
                        <h1>@ViewBag.VMinDia <span>@ViewBag.UnidadeGrandeza</span></h1>
                        <h6>@ViewBag.VMinDia_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-8 col-md-12">
        <div class="row">
            <div class="relat-grafico">
                <div class="postlet-grafico-ea" id="grafico-ea" style="margin-top: -10px"></div>
                <div class='chart-legend' style="display:none;">
                    <div class='legend-scale-dem'>
                        <ul class='legend-labels' style="margin-top: -25px;">
                            <li><span style='background:#1C84C6;'></span>@ViewBag.NomeGrandeza</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@Html.Partial("_Relat_Botoes")

<br />
<div class="panel panel-title relatorio">
    <div class="panel-body">

        <div class="row">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-ea" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.MediaDia</</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Maxima</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Minima</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@ViewBag.NomeGrandeza</td>
                                <td class="relat-ea">@ViewBag.VMedDia @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.DataAtual)</small></td>
                                <td class="relat-preto">@ViewBag.VMaxDia @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMaxDia_DataHora)</small></td>
                                <td class="relat-preto">@ViewBag.VMinDia @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMinDia_DataHora)</small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="panel panel-title">
    <div class="panel-body">
        <div class="row">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Horario</th>
                                <th class="relat-preto" style="width:25%;">@ViewBag.NomeGrandeza</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Umidade</th>
                                <th class="relat-preto" style="width:3%;">@SmartEnergy.Resources.RelatoriosTexts.Clima</th>
                                <th class="relat-preto" style="width:22%;">&nbsp;</th>
                            </tr>
                        </thead>
                        <tbody>
                                @{
                                    RELAT_METEOROLOGIA_DIARIO_REGISTRO[] horariosRelevantes = ViewBag.horariosRelevantes;
                                    if (horariosRelevantes != null && horariosRelevantes.Count() > 0)
                                    {
                                        for (int indice = 0; indice < 4; indice++)
                                        {
                                            string semRegistros = "---";
                                            string horario = "";
                                            string horario2 = "";
                                            string icone = string.Format("icon_hoje{0}", indice);
                                            string classe = string.Format("tempo{0}", indice);                                  
                                            
                                            <tr>
                                                @{
                                                    
                                                    switch(indice)
                                                    {
                                                        case 0:
                                                            horario = "Madrugada";
                                                            horario2 = "(04:00)";
                                                            break;
                                                            
                                                        case 1:
                                                            horario = "Manhã";
                                                            horario2 = "(10:00)";
                                                            break;
                                                            
                                                        case 2:
                                                            horario = "Tarde";
                                                            horario2 = "(16:00)";
                                                            break;
                                                            
                                                        case 3:
                                                            horario = "Noite";
                                                            horario2 = "(22:00)";
                                                            break;     
                                                            
                                                        default:
                                                            horario = "";
                                                            break;                                                       
                                                    }
                                                    
                                                    if (horariosRelevantes[indice].Tempo_Cod >= 0)
                                                    {
                                                        <td class="relat-preto">@horario<br />@horario2</td>
                                                        <td class="relat-preto">@horariosRelevantes[indice].Temp_Atual @ViewBag.UnidadeGrandeza</td>
                                                        <td class="relat-preto">@string.Format("{0} %", horariosRelevantes[indice].Umidade)</td>
                                                        <td class="relat-preto"><canvas id=@icone width="30" height="30"></canvas></td>
                                                        <td class="relat-preto"><h5 class=@classe></h5></td>
                                                    }
                                                    else
                                                    {
                                                        <td class="relat-preto">@horario</td>
                                                        <td class="relat-preto">@semRegistros</td>
                                                        <td class="relat-preto">@semRegistros</td>
                                                        <td class="relat-preto">@semRegistros</td>
                                                        <td class="relat-preto">@semRegistros</td>
                                                    }
                                                }
                                            </tr>                                           
                                        }
                                    }
                                }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal inmodal animated fadeIn" id="modalTabela" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-editar-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">@SmartEnergy.Resources.ComumTexts.BotaoFechar</span></button>
                <h4 class="modal-title"><i class="fa fa-list"></i>&nbsp;&nbsp;@SmartEnergy.Resources.RelatoriosTexts.TabelaRegistros</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-default">
                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:15%;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>
                                <th style="width:15%;">@ViewBag.NomeGrandeza<br /></th>
                                <th style="width:15%;">@SmartEnergy.Resources.RelatoriosTexts.Umidade<br /></th>
                                <th style="width:15%;">@SmartEnergy.Resources.RelatoriosTexts.Pressao<br /></th>
                                <th style="width:20%;">@SmartEnergy.Resources.RelatoriosTexts.Vento<br /></th>
                                <th style="width:20%;">@SmartEnergy.Resources.RelatoriosTexts.Clima<br /></th>
                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var i = 0;
                                var j = 0;
                                var temp = "---";
                                var umidade = "---";
                                var pressao = "---";
                                var vel_vento = "---";
                                var dir_vento = "---";                                
                                var clima = "---";
                                string[] nome_Direcao = { "Norte", "Norte - Nordeste", "Nordeste", "Este - Nordeste", "Leste", "Este - Sudeste", "Sudeste", "Sul - Sudeste", "Sul", "Sul - Sudoeste", "Sudoeste", "Oeste - Sudoeste", "Oeste", "Oeste - Noroeste", "Noroeste", "Norte - Noroeste" };
                                string[] nome_Clima = { "Céu Claro", "Noite Clara", "Parcialmente Nublado", "Parcialmente Nublado", "Nublado", "Chuvoso", "Granizo", "Neve", "Vento", "Nevoeiro" };

                                for (i = 0; i < 24; i++)
                                {
                                    j = i + 1;

                                    if (ViewBag.Tempo_Cod[j] >= 0)
                                    {
                                        temp = string.Format("{0:0} {1}", ViewBag.Temp[j], ViewBag.UnidadeGrandeza);
                                        umidade = string.Format("{0:0} %", ViewBag.Umidade[j]);
                                        pressao = string.Format("{0:0} hPa", ViewBag.Pressao[j]);
                                        vel_vento = string.Format("{0:0} km/h", ViewBag.Vento_Velocidade[j]);
                                        dir_vento = string.Format("[{0}]", nome_Direcao[ViewBag.Vento_Direcao[j]]);
                                        clima = nome_Clima[ViewBag.Tempo_Cod[j]];
                                    }
                                    else
                                    {
                                        temp = "---";
                                        umidade = "---";
                                        pressao = "---";
                                        vel_vento = "---";
                                        dir_vento = "";
                                        clima = "---";
                                    }
                                    
                                                                        
                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="relat-preto">@ViewBag.Horas[j]</td>
                                        <td style="text-align:center;" class="relat-ea">@temp</td>
                                        <td style="text-align:center;" class="relat-ea">@umidade</td>
                                        <td style="text-align:center;" class="relat-ea">@pressao</td>
                                        <td style="text-align:center;" class="relat-ea">@vel_vento<br />@dir_vento</td>
                                        <td style="text-align:center;" class="relat-ea">@clima</td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
            </div>
        </div>
    </div>
</div>


@Html.Partial("_EventosModal")

@Html.Partial("_ObservacoesModal")


<script type="text/javascript">

    $(document).ready(function () {

        // meteorologia
        var dataX = [];
        var labelX = [];
        var dataTemp = [];
        var dataUmidade = [];
        var dataPressao = [];
        var dataTempo_Cod = [];
        var dataVento_Direcao = [];
        var dataVento_Velocidade = [];

        // copia valores
        var Temp = @Html.Raw(Json.Encode(@ViewBag.Temp));
        var Dias = @Html.Raw(Json.Encode(@ViewBag.Dias));
        var NomeGrandeza = @Html.Raw(Json.Encode(@ViewBag.NomeGrandeza));
        var UnidadeGrandeza = @Html.Raw(Json.Encode(@ViewBag.UnidadeGrandeza));
        var Umidade = @Html.Raw(Json.Encode(@ViewBag.Umidade));
        var Pressao = @Html.Raw(Json.Encode(@ViewBag.Pressao));
        var Tempo_Cod = @Html.Raw(Json.Encode(@ViewBag.Tempo_Cod));
        var Vento_Direcao = @Html.Raw(Json.Encode(@ViewBag.Vento_Direcao));
        var Vento_Velocidade = @Html.Raw(Json.Encode(@ViewBag.Vento_Velocidade));
        var Possui_Registros = @Html.Raw(Json.Encode(@ViewBag.Possui_Registros));

        var minimoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMin));
        var maximoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMax));

        var diferenca = maximoValor - minimoValor;

        dataX.push('x');
        dataTemp.push(['Score']);

        for (i = 0; i < 26; i++)
        {
            // X
            dataX.push(Dias[i]);

            dataTemp.push([Temp[i]]);
        }

        // valores label X
        labelX.push(Dias[1]);
        for (i = 3; i <= 24; i+=3)
        {
            labelX.push(Dias[i]);
        }

        var Data = [dataX,dataTemp];

        c3.generate({
            bindto: '#grafico-ea',
            size: {
                height: 300
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    Score: 'bar',
                },
                colors: {
                    Score: 'rgb(120, 178, 235)',
                }
            },
            point: {
                r:2,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*28
                },
                y:  {
                    min: minimoValor,
                    max: maximoValor,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( diferenca < 10)
                            {
                                return d.toFixed(2);
                            }

                            if( diferenca < 100)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.25 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor, unidadeTooltip, valorTooltip, icone_tempo;

                    for (i = 0; i < 5; i++) {

                        if (! text) {

                            // split string and create array.
                            var arr = Dias[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // horas e minutos
                            var hours = data.getHours();
                            var minutes = data.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            title = hours + ':' + minutes;

                            text = "<table class='" + $$.CLASS.tooltip + "' style='color:#000000'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1C84C6'>" + title + "</th></tr>" : "");
                        }

                        // indice
                        var j = d[0].index;

                        // dados do tooltip
                        switch(i)
                        {
                            case 0:      // Temp_Atual
                                name = TEMPERATURA;
                                valorTooltip =  d[i].value.toFixed(0);
                                unidadeTooltip = "°C";
                                break;

                            case 1:     // Umidade
                                name = UMIDADE;
                                valorTooltip =  Umidade[j].toFixed(0);
                                unidadeTooltip = "%";
                                break;

                            case 2:     // Pressao
                                name = PRESSAO;
                                valorTooltip =  Pressao[j].toFixed(0);
                                unidadeTooltip = "hPa";
                                break;

                            case 3:     // Vento_Direcao
                                name = VENTO
                                unidadeTooltip = "km/h";
                                valorTooltip =  Vento_Velocidade[j].toFixed(0);
                                break;

                            case 4:     // Tempo_Cod
                                name = CLIMA;
                                unidadeTooltip = "";

                                switch (Tempo_Cod[d[0].index])
                                {
                                    case 0:     // 0 = clear-day
                                        valorTooltip = CEU_CLARO;
                                        icone_tempo = 'icone_dia_claro16.png';
                                        break;

                                    case 1:     // 1 = clear-night
                                        valorTooltip = NOITE_CLARA;
                                        icone_tempo = 'icone_noite_clara16.png';
                                        break;

                                    case 2:     // 2 = partly-cloudy-day
                                        valorTooltip = PARC_NUBLADO;
                                        icone_tempo = 'icone_dia_nublado16.png';
                                        break;

                                    case 3:     // 3 = partly-cloudy-night
                                        valorTooltip = PARC_NUBLADO;
                                        icone_tempo = 'icone_noite_nublada16.png';
                                        break;

                                    case 4:     // 4 = cloudy
                                        valorTooltip = NUBLADO;
                                        icone_tempo = 'icone_nublado16.png';
                                        break;

                                    case 5:     // 5 = rain
                                        valorTooltip = CHUVOSO;
                                        icone_tempo = 'icone_chuva16.png';
                                        break;

                                    case 6:     // 6 = sleet (granizo)
                                        valorTooltip = GRANIZO;
                                        icone_tempo = 'icone_granizo16.png';
                                        break;

                                    case 7:     // 7 = snow
                                        valorTooltip = NEVE;
                                        icone_tempo = 'icone_neve16.png';
                                        break;

                                    case 8:     // 8 = wind
                                        valorTooltip = VENTO;
                                        icone_tempo = 'icone_vento16.png';
                                        break;

                                    case 9:     // 9 = fog
                                        valorTooltip = NEVOEIRO;
                                        icone_tempo = 'icone_neblina16.png';
                                        break;

                                    default:
                                        valorTooltip = '---';
                                        icone_tempo = '';
                                        break;
                                }
                                break;
                        }

                        if (Tempo_Cod[d[0].index] < 0)
                        {
                            valorTooltip = "---";
                            unidadeTooltip = "";
                        }

                        // cor
                        bgcolor = $$.levelColor ? $$.levelColor(d[0].value) : color(d[0].id);

                        if (i==0)
                        {
                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                            text += "<td class='value'>" + valorTooltip + " " + unidadeTooltip + "</td>";
                            text += "</tr>";
                        }
                        else if (i==3)
                        {
                            var nome_Direcao = [NORTE, NORTE_NORDESTE, NORDESTE, ESTE_NORDESTE, LESTE, ESTE_SUDESTE, SUDESTE, SUL_SUDESTE, SUL, SUL_SUDOESTE, SUDOESTE, OESTE_SUDOESTE, OESTE, OESTE_NOROESTE, NOROESTE, NORTE_NOROESTE];

                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                            text += "<td class='name'>" + name + "</td>";

                            if (valorTooltip == '---')
                            {
                                text += "<td class='value'>" + valorTooltip + "</td>";
                            }
                            else
                            {
                                text += "<td class='value'>" + valorTooltip + " " + unidadeTooltip + "<br>[" + nome_Direcao[Vento_Direcao[d[0].index]] + "]</td>";
                            }
                            text += "</tr>";
                        }
                        else if (i==4)
                        {
                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                            text += "<td class='name'>" + name + "</td>";

                            if (valorTooltip == '---')
                            {
                                text += "<td class='value'>" + valorTooltip + "</td>";
                            }
                            else
                            {
                                text += "<td class='value'>" + valorTooltip + "<br><img src='/Imagens/Tempo/" + icone_tempo + "' /></td>";


                            }
                            text += "</tr>";
                        }
                        else
                        {
                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                            text += "<td class='name'>" + name + "</td>";
                            text += "<td class='value'>" + valorTooltip + " " + unidadeTooltip + "</td>";
                            text += "</tr>";
                        }
                    }
                    return text + "</table>";
                }
            }
        });

        d3.selection.prototype.moveToFront = function () {
            return this.each(function () {
                this.parentNode.appendChild(this);
            });
        };

        d3.select("svg g.c3-grid").moveToFront();

        // labels
        $('.chart-legend').css("display", "block");

        $(".modal-dialog").draggable({
            handle: ".modal-editar-header"
        });


        // copia valores
        var Tempo_Cod_Icone = @Html.Raw(Json.Encode(@ViewBag.Icones));
        var skycons = [];

        // icones tempo
        for (var i = 0; i < 4; i++)
        {
            skycons[i] = new Skycons({"color": "#23C6C8"});
            var tempo = Skycons.CLEAR_DAY;
            var tempo_texto = "Indefinido";

            switch (Tempo_Cod_Icone[i])
            {
                case 0:     // 0 = clear-day
                    tempo_texto = CEU_CLARO;
                    tempo = Skycons.CLEAR_DAY;
                    break;

                case 1:     // 1 = clear-night
                    tempo_texto = NOITE_CLARA;
                    tempo = Skycons.CLEAR_NIGHT;
                    break;

                case 2:     // 2 = partly-cloudy-day
                    tempo_texto = PARC_NUBLADO;
                    tempo = Skycons.PARTLY_CLOUDY_DAY;
                    break;

                case 3:     // 3 = partly-cloudy-night
                    tempo_texto = PARC_NUBLADO;
                    tempo = Skycons.PARTLY_CLOUDY_NIGHT;
                    break;

                case 4:     // 4 = cloudy
                    tempo_texto = NUBLADO;
                    tempo = Skycons.CLOUDY;
                    break;

                case 5:     // 5 = rain
                    tempo_texto = CHUVOSO;
                    tempo = Skycons.RAIN;
                    break;

                case 6:     // 6 = sleet (granizo)
                    tempo_texto = GRANIZO;
                    tempo = Skycons.SLEET;
                    break;

                case 7:     // 7 = snow
                    tempo_texto = NEVE;
                    tempo = Skycons.SNOW;
                    break;

                case 8:     // 8 = wind
                    tempo_texto = VENTO;
                    tempo = Skycons.WIND;
                    break;

                case 9:     // 9 = fog
                    tempo_texto = NEVOEIRO;
                    tempo = Skycons.FOG;
                    break;
            }

            $("h5.tempo" + i).text(tempo_texto);
            skycons[i].add("icon_hoje" + i, tempo);

            // inicia animacao
            skycons[i].play();
        }

        piscando();

    });

    function piscando() {

        var tempo = 500; //1000 = 1s

        blinks = document.getElementsByTagName("blink");
        for(var i=0;i<blinks.length;i++){
            if(blinks[i].getAttribute("style")=="VISIBILITY: hidden"){
                blinks[i].setAttribute("style", "VISIBILITY: visible");
            }else{
                blinks[i].setAttribute("style", "VISIBILITY: hidden");
            }
        }
        setTimeout('piscando()', tempo);
    }

</script>