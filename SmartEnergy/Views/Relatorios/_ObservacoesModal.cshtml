﻿@using SmartEnergyLib.SQL

<style>
    .icones {
        padding: 0px 5px;
        font-size: 18px;
        float: left;
    }

    .titulo {
        margin: 0;
        margin-left: 0px;
        margin-top: 20px;
        margin-bottom: 15px;
        padding-left: 15px;
        padding-top: 5px;
        padding-bottom: 5px;
        font-size: 12px;
        font-weight: bold;
        border-radius: 10px;
        float: left;
    }

    .link_branco a:link {
        color: #ffffff !important;
    }

    .link_branco a:visited {
        color: #ffffff !important;
    }

    .link_branco a:hover {
        color: #f0f0f0 !important;
    }

    .link_branco a:active {
        color: #ffffff !important;
    }

    .link_preto a:link {
        color: #7E6A6C !important;
    }

    .link_preto a:visited {
        color: #7E6A6C !important;
    }

    .link_preto a:hover {
        color: #a0a0a0 !important;
    }

    .link_preto a:active {
        color: #7E6A6C !important;
    }
</style>


@{
    int IDTipoAcesso = ViewBag._IDTipoAcesso;
}


<div class="modal inmodal animated fadeIn" id="modalObservacoes" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-editar-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">@SmartEnergy.Resources.ComumTexts.BotaoFechar</span></button>
                <i class="fa fa-check-square-o modal-editar-title"></i><span class="modal-editar-title">&nbsp;&nbsp;@SmartEnergy.Resources.SupervisaoTexts.Observacoes</span>
            </div>
            <div class="modal-body">
                <div class="panel panel-default">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-sm-12">
                                <table class="table table-striped table-bordered table-hover dataTables-observacoes">
                                    <thead>
                                        <tr>
                                            <th>@SmartEnergy.Resources.RelatoriosTexts.Data</th>
                                            <th class="some_minidesktop">Tag</th>
                                            <th>@SmartEnergy.Resources.SupervisaoTexts.Observacoes</th>
                                        </tr>
                                    </thead>
                                    <tbody>

                                        @{
                                            List<ObservacaoMedicaoDominio> observacoes = ViewBag.Observacoes;
                                            List<ObservacaoTagsDominio> tags = ViewBag.tags;

                                            foreach(ObservacaoMedicaoDominio observacao in observacoes)
                                            {
                                                string DataHora = String.Format("{0:d}", observacao.DataHora);
                                                string DataHora_Sort = String.Format("{0:yyyyMMddHHmmss}", observacao.DataHora);

                                                ObservacaoTagsDominio tag = tags.Find(item => item.IDTag == observacao.IDTag);
                        
                                                string Observacao_Tag_Badge = "primary";
                                                string Observacao_Tag_Sort = "Z";

                                                if (observacao.IDTag > 0 && observacao.IDTag < 100)
                                                {
                                                    Observacao_Tag_Badge = "danger";
                                                    Observacao_Tag_Sort = "A_" + tag.Descricao;
                                                }

                                                if (observacao.IDTag > 100 && observacao.IDTag < 200)
                                                {
                                                    Observacao_Tag_Badge = "warning";
                                                    Observacao_Tag_Sort = "B_" + tag.Descricao;
                                                }

                                                if (observacao.IDTag > 200 && observacao.IDTag < 300)
                                                {
                                                    Observacao_Tag_Badge = "primary";
                                                    Observacao_Tag_Sort = "C_" + tag.Descricao;
                                                }
                        

                                                <tr>
                                                    <td><span style="display:none;">@DataHora_Sort</span>@DataHora</td>

                                                    @if (observacao.IDTag == 0)
                                                    {
                                                        <td><span style="display:none;">@Observacao_Tag_Sort</span>&nbsp;</td>
                                                    }
                                                    else
                                                    {
                                                        string classe = string.Format("badge-{0}", Observacao_Tag_Badge);

                                                        <td><span style="display:none;">@Observacao_Tag_Sort</span><span class="badge @classe some_desktop" title="@tag.Descricao">&nbsp;&nbsp;@tag.Descricao&nbsp;&nbsp;</span></td>
                                                    }

                                                    <td>@observacao.Observacao</td>
                                                </tr>
                        
                                            }
                                        }

                                    </tbody>
                                </table>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">

    $(document).ready(function () {

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {

                var x = a;

                if (a == "-") {
                    x = "-1";
                }
                else {
                    x = a.replace(".", "");
                    x = x.replace(",", ".");
                }

                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-observacoes').DataTable({
            "iDisplayLength": 13,
            dom: 'ftp',

            "bAutoWidth": false,
            "bDestroy": true,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "portugues" },
                        { "aTargets": [1], "sType": "portugues" },
                        { "aTargets": [2], "sType": "portugues" },
            ],
            "aoColumns": [
            { sWidth: "15%" },
            { sWidth: "15%" },
            { sWidth: "70%" },
            ],
            "order": [0, "desc"],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

        $(".modal-dialog").draggable({
            handle: ".modal-editar-header"
        });
    });

</script>

    