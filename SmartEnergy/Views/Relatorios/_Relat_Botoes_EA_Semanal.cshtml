﻿@using SmartEnergyLib.SQL

<style>

.btn-cinza {
  color: #fff;
  background-color: #808080;
  border-color: #808080;
}
.btn-cinza:focus,
.btn-cinza.focus {
  color: #fff;
  background-color: #606060;
  border-color: #606060;
}
.btn-cinza:hover {
  color: #fff;
  background-color: #606060;
  border-color: #606060;
}


</style>

@{
    List<ObservacaoMedicaoDominio> listaobservacoes = ViewBag.Observacoes;
    List<EventosDescricao> listaeventos = ViewBag.listaEventosDescricao;
}


<div class="col-lg-3">

    @if ( listaobservacoes.Count > 0 )
    {
        <a id="BotaoObservacoes" data-toggle="modal" href="#modalObservacoes" class="btn btn-info pull-left" style="color:#ffffff; width:100%;">@SmartEnergy.Resources.SupervisaoTexts.Observacoes</a>
    }
    else
    {
        <a id="BotaoObservacoes" data-toggle="modal" href="#modalObservacoes" class="btn btn-cinza pull-left" style="color:#ffffff; width:100%;" disabled>@SmartEnergy.Resources.SupervisaoTexts.Observacoes</a>
    }

</div>


@if (ViewBag.TemEventos)
{
    <div class="col-lg-2">
        <a id="BotaoTabela" data-toggle="modal" href="#modalTabela" class="btn btn-info pull-left" style="color:#ffffff; width:100%;">@SmartEnergy.Resources.RelatoriosTexts.TabelaRegistros</a>
    </div>

    <div class="col-lg-3">
                
        @if (listaeventos.Count > 0)
        {
            <a id="BotaoEventos" data-toggle="modal" href="#modalEventos" class="btn btn-info pull-left" style="color:#ffffff; width:100%;">@SmartEnergy.Resources.RelatoriosTexts.Eventos</a>
        }
        else
        {
            <a id="BotaoEventos" data-toggle="modal" href="#modalEventos" class="btn btn-cinza pull-left" style="color:#ffffff; width:100%;" disabled>Não existem eventos no período</a>
        }
        
    </div>
}
else
{
    <div class="col-lg-5">
        <a id="BotaoTabela" data-toggle="modal" href="#modalTabela" class="btn btn-info pull-left" style="color:#ffffff; width:100%;">@SmartEnergy.Resources.RelatoriosTexts.TabelaRegistros</a>
    </div>
}


    