﻿@using SmartEnergyLib.SQL

<style>
    #grafico-ConsumoContrato .c3-axis-y text {
        font-size: 12px;
    }

    #grafico-ConsumoContrato .c3-axis-x text {
        font-size: 15px;
    }

    .c3 svg {
        font-size: 15px;
    }

    .dataTables-analiseContratos td {
        font-size: 11px;
    }

    .dataTables-analiseContratos th {
        font-size: 11px;
    }

</style>


@{
    Provisionamento_Tipo3 provisionamento = ViewBag.ProvisionamentoSemanal;
    
    if (provisionamento != null)
    {
        string Observacao_txt = "Considerando o histórico de consumo das últimas semanas, o consumo nesse mês ficará ";
        string Resultado_txt = SmartEnergy.Resources.ContratosCCEETexts.Previsao;
        string Consumo_txt = SmartEnergy.Resources.ContratosCCEETexts.ConsumoDentro;
        
        string Resultado_cor = "relat-verde";
        string Classe_cor = "panel-primary";
        bool piscar = false;

        if (provisionamento.Resultado == 0.0)
        {
            Observacao_txt += "DENTRO do Volume Contratado.";
        }        
        if (provisionamento.Resultado > 0.0)
        {
            Observacao_txt += "ABAIXO do Volume Contratado.";
            Resultado_txt = SmartEnergy.Resources.ContratosCCEETexts.PrevisaoSobra;
            Consumo_txt = SmartEnergy.Resources.ContratosCCEETexts.ConsumoMenor;
            Resultado_cor = "relat-amarelo";
            Classe_cor = "panel-warning";
        }
        if (provisionamento.Resultado < 0.0)
        {
            Observacao_txt += "ACIMA do Volume Contratado.";            
            Resultado_txt = SmartEnergy.Resources.ContratosCCEETexts.PrevisaoDeficit;
            Consumo_txt = SmartEnergy.Resources.ContratosCCEETexts.ConsumoMaior;
            Resultado_cor = "relat-vermelho";
            Classe_cor = "panel-danger";
            piscar = true;
        }

        
        <div class="row">
            <div class="col-lg-12">
                <div class="panel @Classe_cor">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-12">

                                @{
                                    if (piscar)
                                    {
                                        <blink><i class="fa fa-exclamation-triangle @Resultado_cor" style="font-size:20px;"></i></blink><span class="@Resultado_cor" style="font-size:large; padding-left:8px;"><b>@Observacao_txt</b></span>
                                    }
                                    else
                                    {
                                        <i class="fa fa-exclamation-triangle @Resultado_cor" style="font-size:20px;"></i><span class="@Resultado_cor" style="font-size:large; padding-left:8px;"><b>@Observacao_txt</b></span>
                                    }
                                }

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    
        <div class="row">
            <div class="col-lg-6">
                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>@SmartEnergy.Resources.ContratosCCEETexts.AnaliseContratoConsumo</h4>
                    </div>
                    <div class="panel-body" style="min-height:300px;">
                        <div class="row">
                            <div class="col-lg-7">
                                <span><b>@SmartEnergy.Resources.ContratosCCEETexts.TotalContrato</b></span>
                            </div>
                            <div class="col-lg-5">
                                <span style="font-size:large;"><b>@string.Format("{0:#,##0.000} MWh", provisionamento.Total_Contrato)</b></span>
                            </div>
                        </div>

                        <hr />

                        <div class="row">
                            <div class="col-lg-7">
                                <span>@SmartEnergy.Resources.ContratosCCEETexts.ConsumoConsolidado</span>
                            </div>
                            <div class="col-lg-5">
                                <span>@string.Format("{0:#,##0.000} MWh", provisionamento.Consumo_Consolidado)</span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-7">
                                <span>@SmartEnergy.Resources.ContratosCCEETexts.ConsumoEstimado</span>
                            </div>
                            <div class="col-lg-5">
                                <span>@string.Format("{0:#,##0.000} MWh", provisionamento.Consumo_Estimado)</span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-7">
                                <span>@SmartEnergy.Resources.ContratosCCEETexts.ConsumoTotalBruto</span>
                            </div>
                            <div class="col-lg-5">
                                <span>@string.Format("{0:#,##0.000} MWh", provisionamento.Consumo_Total_Bruto)</span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-7">
                                <span>@SmartEnergy.Resources.ContratosCCEETexts.Perdas</span>
                            </div>
                            <div class="col-lg-5">
                                <span>@string.Format("{0:0.0} %", provisionamento.Perdas)</span>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-7">
                                <span><b>@SmartEnergy.Resources.ContratosCCEETexts.PrevisaoConsumo</b></span>
                            </div>
                            <div class="col-lg-5">
                                <span style="font-size:large;"><b>@string.Format("{0:#,##0.000} MWh", provisionamento.Previsao_Consumo)</b></span>
                            </div>
                        </div>

                        <hr />

                        <div class="row">
                            <div class="col-lg-7">
                                <span class="@Resultado_cor"><b>@Resultado_txt</b></span>
                            </div>
                            <div class="col-lg-5">
                                <span class="@Resultado_cor" style="font-size:large;"><b>@string.Format("{0:#,##0.000} MWh", provisionamento.Resultado)</b></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>Previsão de Consumo x Contrato</h4>
                    </div>
                    <div class="panel-body" style="min-height:300px;">
                        <div class="row">
                            <div class="relat-grafico">
                                <div id="grafico-ConsumoContrato" style="margin-top: 10px"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-title">
                    <div class="panel-body" style="min-height:300px;">
                        <div class="row">
                            <div class="form-group col-lg-12">
                                <table id="dataTables-analiseContratos" class="table table-striped table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <th>Sequência</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.ContratoCodigo</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.Considera</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.ConsumoTotalBruto</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.Perdas</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.PrevisaoConsumo</th>
                                            <th>PROINFA</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.NecessidadeContratacao</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.Contrato</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.TakeMinimo</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.TakeMaximo</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.VolumeFaturado</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.TotalContrato</th>
                                        </tr>
                                    </thead>
                                    <tbody>

                                        @{
                                            List<ContratosCCEEDominio> contratosEnergia = ViewBag.contratosEnergia;

                                            if (contratosEnergia != null)
                                            {
                                                foreach (ContratosCCEEDominio contrato in contratosEnergia)
                                                {
                                                    // encontra provisionamento
                                                    Provisionamento_Tipo1_Tipo2 prov = provisionamento.por_contrato.Find(item => item.IDContratoCCEE == contrato.IDContratoCCEE);

                                                    if (prov == null)
                                                    {
                                                        continue;
                                                    }
                                                       
                                                    // considera
                                                    var considera = "NÃO";
                                                            
                                                    if (contrato.Considera == 1)
                                                    {
                                                        considera = "SIM";
                                                    }
                                                    
                                                    <tr>
                                                        <td>@contrato.Ordem</td>
                                                        <td>@contrato.Contrato_Codigo</td>
                                                        <td>@considera</td>
                                                        <td>@string.Format("{0:#,##0.000} MWh", prov.Consumo_Total_Bruto)</td>
                                                        <td>@string.Format("{0:0.0} %", prov.Perdas)</td>
                                                        <td>@string.Format("{0:#,##0.000} MWh", prov.Previsao_Consumo)</td>
                                                        <td>@string.Format("{0:#,##0.000} MWh", prov.PROINFA)</td>
                                                        <td>@string.Format("{0:#,##0.000} MWh", prov.Necessidade_Contratacao)</td>
                                                        <td>@string.Format("{0:#,##0.000} MWh", prov.Contrato)</td>
                                                        <td>@string.Format("{0:#,##0.000} MWh", prov.Take_Minimo)</td>
                                                        <td>@string.Format("{0:#,##0.000} MWh", prov.Take_Maximo)</td>
                                                        <td>@string.Format("{0:#,##0.000} MWh", prov.Volume_Contratado)</td>
                                                        <td>@string.Format("{0:#,##0.000} MWh", prov.Total_Contrato)</td>
                                                    </tr>
                                                }
                                            }
                                        }

                                    </tbody>
                                </table>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
    }
}



<script type="text/javascript">

    $(document).ready(function () {

        // barras
        var Barras = [];

        Barras.push('x');
        Barras.push('Previsão de Consumo');
        Barras.push('Contrato');

        // valores
        var dataValores = [];

        // copia valores
        var PrevisaoConsumo = @Html.Raw(Json.Encode(@ViewBag.PrevisaoConsumo));
        var Contrato = @Html.Raw(Json.Encode(@ViewBag.Contrato));

        var MaxGrafico = Math.ceil(@ViewBag.MaxGrafico);

        // verifico se nao tem dados e forco um limite
        if( MaxGrafico == 0.0)
        {
            MaxGrafico = 100.0;
        }

        dataValores.push(['data1']);
        dataValores.push([PrevisaoConsumo]);
        dataValores.push([Contrato]);

        c3.generate({

            bindto: '#grafico-ConsumoContrato',
            size: {
                height: 250
            },
            padding: {
                top: 8,
                right: 16,
                bottom: 0,
                left: 63
            },
            data: {
                x: 'x',
                columns: [Barras,dataValores],
                types: {
                    data1: 'bar',
                },
                labels: {
                    format: {
                        data1: function (v, id, i, j) {

                            var valor = v;
                            var str_valor = valor.toFixed(3).replace('.', ',');

                            //return (str_valor.replace(/\B(?=(\d{3})+(?!\d))/g, ".") + " MWh");
                            return (str_valor + " MWh");
                        },
                    }
                },
                color: function (color, d) {
                    if (typeof d.index === 'undefined') { return color; }

                    if( d.index == 0 )
                        return '#007F00';

                    if( d.index == 1 )
                        return '#1C84C6';

                    return '#007F00';
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type : 'categories',
                    tick: {
                        outer: false,
                        fit: true
                    }
                },
                y:  {
                    max: MaxGrafico,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {
                            return d.toFixed(0);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.5 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < 2; i++) {

                        if (! text) {

                            title = 'Consumo x Contrato';

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // verifica se barra
                        name = Barras[i+1];
                        value = parseFloat(dataValores[i+1]);

                        if( i == 0 )
                            bgcolor = '#007F00';
                        else
                            bgcolor = '#1C84C6';

                        //var valor = value.toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ".")
                        var valor = value.toFixed(3).replace('.', ',');

                        text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                        text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                        text += "<td class='value'>" + valor + " MWh</td>";
                        text += "</tr>";
                    }
                    return text + "</table>";
                }
            }
        });


        //
        // TABELA CONTRATOS
        //

        var tableContratos = $('#dataTables-analiseContratos').DataTable({
            rowReorder: {
                selector: '.reordena',
                update: true
            },
            "iDisplayLength": 10,
            dom: 'tp',
            "bAutoWidth": false,
            "aoColumnDefs": [
                     { "aTargets": [0], "bVisible": false, "bSortable": false, "bSearchable": false },
                     { "aTargets": [1], "sType": "portugues", "bSortable": false },
                     { "aTargets": [2], "sType": "portugues", "bSortable": false },
                     { "aTargets": [3], "sType": "numero", "bSortable": false },
                     { "aTargets": [4], "sType": "numero", "bSortable": false },
                     { "aTargets": [5], "sType": "numero", "bSortable": false },
                     { "aTargets": [6], "sType": "numero", "bSortable": false },
                     { "aTargets": [7], "sType": "numero", "bSortable": false },
                     { "aTargets": [8], "sType": "numero", "bSortable": false },
                     { "aTargets": [9], "sType": "numero", "bSortable": false },
                     { "aTargets": [10], "sType": "numero", "bSortable": false },
                     { "aTargets": [11], "sType": "numero", "bSortable": false },
                     { "aTargets": [12], "sType": "numero", "bSortable": false },
            ],
            "aoColumns": [
            { sWidth: "1%" },
            { sWidth: "10%" },
            { sWidth: "7%" },
            { sWidth: "9%" },
            { sWidth: "7%" },
            { sWidth: "9%" },
            { sWidth: "8%" },
            { sWidth: "8%" },
            { sWidth: "8%" },
            { sWidth: "8%" },
            { sWidth: "8%" },
            { sWidth: "8%" },
            { sWidth: "10%" },
            ],
            'order': [0, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });


    });

</script>
