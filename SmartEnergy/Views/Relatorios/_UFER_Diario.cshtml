﻿<div class="row">
    <div class="col-lg-4 col-md-12 relatorio_real">
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.DashboardTexts.UFERTotalDia</h5>
                        <h1>@ViewBag.UFER_Total_FPC <span>@ViewBag.UnidadeUFER</span></h1>
                        <h6>@ViewBag.DataAtual</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.DashboardTexts.UFERTotalDia</h5>
                        <h1>@ViewBag.UFER_Total_FPI <span>@ViewBag.UnidadeUFER</span></h1>
                        <h6>@ViewBag.DataAtual</h6>
                    </div>
                </div>
            </div>
        </div>
        <br /><br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.DashboardTexts.UFERTotalDia</h5>
                        <h1>@ViewBag.UFER_Total_P <span>@ViewBag.UnidadeUFER</span></h1>
                        <h6>@ViewBag.DataAtual</h6>
                    </div>
                </div>
            </div>
        </div>
        <br /><br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-preto">
                        <h5>@SmartEnergy.Resources.DashboardTexts.UFERTotalDia</h5>
                        <h1>@ViewBag.UFER_Total <span>@ViewBag.UnidadeUFER</span></h1>
                        <h6>@ViewBag.DataAtual</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-12 simulacao" style="display:none;">
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.DashboardTexts.UFERTotalDia</h5>
                        <h1>@ViewBag.UFER_Total_FPC_Sim <span>@ViewBag.UnidadeUFER</span></h1>
                        <h6>@ViewBag.DataAtual</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.DashboardTexts.UFERTotalDia</h5>
                        <h1>@ViewBag.UFER_Total_FPI_Sim <span>@ViewBag.UnidadeUFER</span></h1>
                        <h6>@ViewBag.DataAtual</h6>
                    </div>
                </div>
            </div>
        </div>
        <br /><br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.DashboardTexts.UFERTotalDia</h5>
                        <h1>@ViewBag.UFER_Total_P_Sim <span>@ViewBag.UnidadeUFER</span></h1>
                        <h6>@ViewBag.DataAtual</h6>
                    </div>
                </div>
            </div>
        </div>
        <br /><br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-preto">
                        <h5>@SmartEnergy.Resources.DashboardTexts.UFERTotalDia</h5>
                        <h1>@ViewBag.UFER_Total_Sim <span>@ViewBag.UnidadeUFER</span></h1>
                        <h6>@ViewBag.DataAtual</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-8 col-md-12">
        <div class="row">
            <div class="relat-grafico">
                <div id="grafico-UFER" style="margin-top: -10px"></div>
                <div class='chart-legend' style="display:none;">
                    <div class='legend-scale-dem'>
                        <ul class='legend-labels' style="margin-top: -25px;">
                            <li><span style='background:#1C84C6;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                            <li><span style='background:#007f00;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                            <li><span style='background:#ff0000;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                            <li class="simulacao" style="display: none;"><span style='background:#00b1ff;'></span>@SmartEnergy.Resources.RelatoriosTexts.Simulacao</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@Html.Partial("_Relat_Botoes")

<br />
<div class="panel panel-title relatorio">
    <div class="panel-body">

        <div class="row">
            <div class="col-lg-12 relatorio_real">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:20%;"></th>
                                <th class="relat-capacitivo" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                                <th class="relat-preto" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.DashboardTexts.UFERTotalDia<br />&nbsp;</td>
                                <td class="relat-capacitivo">@ViewBag.UFER_Total_FPC @ViewBag.UnidadeUFER</td>
                                <td class="relat-fponta">@ViewBag.UFER_Total_FPI @ViewBag.UnidadeUFER</td>
                                <td class="relat-ponta">@ViewBag.UFER_Total_P @ViewBag.UnidadeUFER</td>
                                <td class="relat-preto">@ViewBag.UFER_Total @ViewBag.UnidadeUFER</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="col-lg-12 simulacao" style="display:none;">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:20%;"></th>
                                <th class="relat-capacitivo" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                                <th class="relat-preto" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.DashboardTexts.UFERTotalDia<br />&nbsp;</td>
                                <td class="relat-capacitivo">@ViewBag.UFER_Total_FPC_Sim @ViewBag.UnidadeUFER</td>
                                <td class="relat-fponta">@ViewBag.UFER_Total_FPI_Sim @ViewBag.UnidadeUFER</td>
                                <td class="relat-ponta">@ViewBag.UFER_Total_P_Sim @ViewBag.UnidadeUFER</td>
                                <td class="relat-preto">@ViewBag.UFER_Total_Sim @ViewBag.UnidadeUFER</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>

<div class="modal inmodal animated fadeIn" id="modalTabela" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-editar-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">@SmartEnergy.Resources.ComumTexts.BotaoFechar</span></button>
                <h4 class="modal-title"><i class="fa fa-list"></i>&nbsp;&nbsp;@SmartEnergy.Resources.RelatoriosTexts.TabelaRegistros</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-default relatorio_real">
                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:20%;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>
                                <th style="width:26%;">@SmartEnergy.Resources.RelatoriosTexts.Periodo</th>
                                <th style="width:26%;">UFER<br /> (@ViewBag.UnidadeUFER)</th>
                                <th style="width:26%;">@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia</th>
                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var i = 0;
                                var j = 0;
                                var classe = "relat-semreg";
                                var periodo = "---";
                                var UFER = "---";
                                var fatpot = "---";

                                for (i = 0; i < 24; i++)
                                {
                                    j = i + 1;
                                    
                                    classe = "relat-semreg";
                                    periodo = "---";
                                    UFER = "---";
                                    fatpot = "---";

                                    if (ViewBag.Periodo[j] == 0)
                                    {
                                        classe = "relat-ponta";
                                        periodo = SmartEnergy.Resources.SupervisaoTexts.Ponta;
                                    }

                                    if (ViewBag.Periodo[j] == 1)
                                    {
                                        classe = "relat-fponta";
                                        periodo = SmartEnergy.Resources.SupervisaoTexts.FPontaInd;
                                    }

                                    if (ViewBag.Periodo[j] == 2)
                                    {
                                        classe = "relat-capacitivo";
                                        periodo = SmartEnergy.Resources.SupervisaoTexts.FPontaCap;
                                    }

                                    if (ViewBag.Periodo[j] != 3)
                                    {
                                        UFER = string.Format("{0:#,##0.0}", ViewBag.UFER[j]);
                                        fatpot = string.Format("{0:0.000}", ViewBag.FatPot[j]);
                                    }
                            
                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="@classe">@ViewBag.Horas[j]</td>
                                        <td style="text-align:center;" class="@classe">@periodo</td>
                                        <td style="text-align:center;" class="@classe">@UFER</td>
                                        <td style="text-align:center;" class="@classe">@fatpot</td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>

                <div class="panel panel-default simulacao" style="display:none;">
                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:20%;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>
                                <th style="width:26%;">@SmartEnergy.Resources.RelatoriosTexts.Periodo</th>
                                <th style="width:26%;">UFER<br /> (@ViewBag.UnidadeUFER)</th>
                                <th style="width:26%;">@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia</th>
                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var classe_sim = "relat-semreg";
                                var periodo_sim = "---";
                                var UFER_sim = "---";
                                var fatpot_sim = "---";

                                for (i = 0; i < 24; i++)
                                {
                                    j = i + 1;

                                    classe_sim = "relat-semreg";
                                    periodo_sim = "---";
                                    UFER_sim = "---";
                                    fatpot_sim = "---";

                                    if (ViewBag.Periodo[j] == 0)
                                    {
                                        classe_sim = "relat-ponta";
                                        periodo_sim = SmartEnergy.Resources.SupervisaoTexts.Ponta;
                                    }

                                    if (ViewBag.Periodo[j] == 1)
                                    {
                                        classe_sim = "relat-fponta";
                                        periodo_sim = SmartEnergy.Resources.SupervisaoTexts.FPontaInd;
                                    }

                                    if (ViewBag.Periodo[j] == 2)
                                    {
                                        classe_sim = "relat-capacitivo";
                                        periodo_sim = SmartEnergy.Resources.SupervisaoTexts.FPontaCap;
                                    }

                                    if (ViewBag.Periodo[j] != 3)
                                    {
                                        UFER_sim = string.Format("{0:#,##0.0}", ViewBag.UFER_Sim[j]);
                                        fatpot_sim = string.Format("{0:0.000}", ViewBag.FatPot_Sim[j]);
                                    }

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="@classe_sim">@ViewBag.Horas[j]</td>
                                        <td style="text-align:center;" class="@classe_sim">@periodo_sim</td>
                                        <td style="text-align:center;" class="@classe_sim">@UFER_sim</td>
                                        <td style="text-align:center;" class="@classe_sim">@fatpot_sim</td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
            </div>
        </div>
    </div>
</div>


@Html.Partial("_EventosModal")

@Html.Partial("_ObservacoesModal")


<script type="text/javascript">

    $(document).ready(function () {

        // UFER
        var dataX = [];
        var labelX = [];
        var dataUFER = [];
        var dataP = [];
        var dataFPI = [];
        var dataFPC = [];

        // UFER Simulacao
        var dataUFER_Sim = [];

        // copia valores
        var UFER = @Html.Raw(Json.Encode(@ViewBag.UFER));
        var FatPot = @Html.Raw(Json.Encode(@ViewBag.FatPot));
        var Periodo = @Html.Raw(Json.Encode(@ViewBag.Periodo));
        var Dias = @Html.Raw(Json.Encode(@ViewBag.Dias));

        // copia valores simulacao
        var UFER_Sim = @Html.Raw(Json.Encode(@ViewBag.UFER_Sim));
        var FatPot_Sim = @Html.Raw(Json.Encode(@ViewBag.FatPot_Sim));

        var maximoUFER = Math.ceil(@ViewBag.UFERMaxGrafico);

        var UnidadeUFER = @Html.Raw(Json.Encode(@ViewBag.UnidadeUFER));

        // verifico se nao tem dados e forco um limite
        if( maximoUFER == 1.0)
        {
            maximoUFER = 2.0;
        }

        if( maximoUFER == 0.0)
        {
            maximoUFER = 1.0;
        }

        dataX.push('x');
        dataUFER.push(['data1']);
        dataP.push(['data2']);
        dataFPI.push(['data3']);
        dataFPC.push(['data4']);

        // simulacao 
        dataUFER_Sim.push(['data5']);

        for (i = 0; i < 26; i++)
        {
            // X
            dataX.push(Dias[i]);

            dataUFER.push([UFER[i]]);

            // simulacao 
            dataUFER_Sim.push([UFER_Sim[i]]);

            if( Periodo[i] == 0 )
            {
                dataP.push([maximoUFER]);
                dataFPI.push([0]);
                dataFPC.push([0]);
            }
            else if( Periodo[i] == 1 )
            {
                dataP.push([0]);
                dataFPC.push([0]);
                dataFPI.push([maximoUFER]);
            }
            else if( Periodo[i] == 2 )
            {
                dataP.push([0]);
                dataFPI.push([0]);
                dataFPC.push([maximoUFER]);
            }
            else
            {
                dataP.push([0]);
                dataFPI.push([0]);
                dataFPC.push([0]);
            }
        }

        // valores label X
        labelX.push(Dias[1]);
        for (i = 3; i <= 24; i+=3)
        {
            labelX.push(Dias[i]);
        }

        var Data = [dataX,dataUFER,dataP,dataFPI,dataFPC,dataUFER_Sim];

        var chart = c3.generate({
            bindto: '#grafico-UFER',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                type:'bar',
                types: {
                    data2: 'area-step',
                    data3: 'area-step',
                    data4: 'area-step',
                    data5: 'step'
                },
                color: function (color, d) {
                    if (typeof d.index === 'undefined') { return color; }

                    if( Periodo[d.index] == 0 )
                        return '#FF0000';

                    if( Periodo[d.index] == 1 )
                        return '#007F00';

                    if( Periodo[d.index] == 2 )
                        return '#1C84C6';

                    return '#007F00';
                },
                colors: {
                    data2: '#FF0000',
                    data3: '#007F00',
                    data4: '#1C84C6',
                    data5: '#00b1ff'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*28
                },
                y:  {
                    max: maximoUFER,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoUFER < 100.0 )
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.25 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    // checkbox aplica simulacao
                    var simula = $('#aplica_simula').prop('checked');
                    var tooltip_index = 3;

                    if (simula)
                    {
                        // caso tenha ira percorrer o loop para preencher valores de simulacao
                        tooltip_index = 5;
                    }

                    for (i = 0; i < tooltip_index; i++) {

                        if (! text) {

                            // split string and create array.
                            var arr = Dias[d[0].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // horas e minutos
                            var hours = data.getHours();
                            var minutes = data.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            title = hours + ':' + minutes;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // periodo
                        switch(Periodo[d[0].index])
                        {
                            case 0:
                                periodo = PONTA;
                                bgcolor = '#FF0000';
                                break;

                            case 1:
                                periodo = FPONTA_IND;
                                bgcolor = '#007F00';
                                break;

                            case 2:
                                periodo = FPONTA_CAP;
                                bgcolor = '#1C84C6';
                                break;

                            default:
                                periodo = "---";
                                bgcolor = '#000000';
                                break;
                        }

                        // nome
                        switch(i)
                        {
                            case 0:
                                name = 'UFER';
                                unit = UnidadeUFER;
                                value = UFER[d[0].index].toFixed(1);
                                break;

                            case 1:
                                name = FATOR_POTENCIA;
                                unit = '';
                                value = FatPot[d[0].index].toFixed(3);
                                break;

                            case 2:
                                name = PERIODO;
                                unit = '';
                                value = periodo;
                                break;

                            case 3:
                                name = "UFER - " + SIMULACAO;
                                unit = UnidadeUFER;
                                value = UFER_Sim[d[0].index].toFixed(1);
                                break;

                            case 4:
                                name = FATOR_POTENCIA + " - " + SIMULACAO;
                                unit = '';
                                value = FatPot_Sim[d[0].index].toFixed(3);
                                break;

                        }

                        if (i > 2)
                        {
                            bgcolor = '#00b1ff';
                        }

                        value = value.replace('.', ',');

                        // verifica se sem registro
                        if( Periodo[d[0].index] == 3 )
                        {
                            value = '---';
                        }

                        if(i==2)
                        {
                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'>" + name + "</td>";
                            text += "<td class='value'>" + value + "</td>";
                            text += "</tr>";
                        }
                        else
                        {
                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                            text += "<td class='value'>" + value + " " + unit + "</td>";
                            text += "</tr>";
                        }
                    }
                    return text + "</table>";
                }
            }
        });

        // salva chart 
        $('#grafico-UFER').data('c3-chart', chart);

        // labels
        $('.chart-legend').css("display", "block");

        ShowSimulacao();

        $(".modal-dialog").draggable({
            handle: ".modal-editar-header"
        });
    });

    function ShowSimulacao () {

        var IDSimulacaoCenario = parseInt(@ViewBag._IDSimulacaoCenario);

        // verifica se ano existe nenhuma simulacao selecionada
        if (IDSimulacaoCenario == 0)
        {
            $("#aplica_simula").attr('disabled', true);
            $('#aplica_simula').prop('checked', false);
        }
        else
        {
            $("#aplica_simula").attr('disabled', false);
        }

        // grafico demanda
        var chart = $('#grafico-UFER').data('c3-chart');

        // checkbox aplica simulacao
        var simula = $('#aplica_simula').prop('checked');

        if (simula)
        {
            // apresenta grafico simulacao
            chart.show(['data5']);

            $('.simulacao').css("display", "block");
            $('.relatorio_real').css("display", "none");
        }
        else
        {
            // apresenta grafico real
            chart.hide(['data5']);

            $('.simulacao').css("display", "none");
            $('.relatorio_real').css("display", "block");
        }

        // salva em cookie
        setCookie("AplicaSimulacao", simula, null);
    }

</script>
