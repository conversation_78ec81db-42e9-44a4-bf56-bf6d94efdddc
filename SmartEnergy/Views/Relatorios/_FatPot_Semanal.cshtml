﻿<style>
    .icone_semana {
    padding: 0px 7px;
    font-size: 14px;
    float: left; 
    color: white;
}

.c3-area {
  opacity: 0.1 !important;
}

.c3-line {
    stroke-width: 1px;
}

</style>

<div class="row">
    <div class="col-lg-4 col-md-12 relatorio_real">
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia @SmartEnergy.Resources.SupervisaoTexts.MaisCapacitivo</h5>
                        <h1>@ViewBag.FatPot_MaisCapFPC</h1>
                        <h6>@ViewBag.FatPot_MaisCapFPC_DataHora</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia @SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</h5>
                        <h1>@ViewBag.FatPot_MaisIndFPI</h1>
                        <h6>@ViewBag.FatPot_MaisIndFPI_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia @SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</h5>
                        <h1>@ViewBag.FatPot_MaisIndP</h1>
                        <h6>@ViewBag.FatPot_MaisIndP_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4 col-md-12 simulacao" style="display:none;">
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia @SmartEnergy.Resources.SupervisaoTexts.MaisCapacitivo</h5>
                        <h1>@ViewBag.FatPot_MaisCapFPC_Sim</h1>
                        <h6>@ViewBag.FatPot_MaisCapFPC_DataHora_Sim</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia @SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</h5>
                        <h1>@ViewBag.FatPot_MaisIndFPI_Sim</h1>
                        <h6>@ViewBag.FatPot_MaisIndFPI_DataHora_Sim</h6>
                    </div>
                </div>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia @SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</h5>
                        <h1>@ViewBag.FatPot_MaisIndP_Sim</h1>
                        <h6>@ViewBag.FatPot_MaisIndP_DataHora_Sim</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-8 col-md-12">
        <div class="row">
            <div class="relat-grafico">
                <div class="relatorio_real" id="fatpot-chart"></div>
                <div class="simulacao" id="fatpot-chart-simulacao" style="display:none;"></div>
                <div class='chart-legend' style="display:none;">
                    <div class='legend-scale-dem'>
                        <ul class='legend-labels' style="margin-top: 0px;">
                            <li><a href="#" onclick="alteraDiaSemana(0,true);"><span style='background:#0000FF;' id="ckDomingo"><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.domingo</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(1,true);"><span style='background:#00FF00;' id="ckSegunda"><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.segunda</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(2,true);"><span style='background:#FF0000;' id="ckTerca"  ><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.terca</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(3,true);"><span style='background:#FF7F00;' id="ckQuarta" ><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.quarta</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(4,true);"><span style='background:#FF00FF;' id="ckQuinta" ><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.quinta</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(5,true);"><span style='background:#000000;' id="ckSexta"  ><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.sexta</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(6,true);"><span style='background:#00FFFF;' id="ckSabado" ><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.sabado</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@Html.Partial("_Relat_Botoes")

<br />
<div class="panel panel-title relatorio">
    <div class="panel-body">
        <div class="row">
            <div class="col-lg-12">
                <div class="relat-tabela relatorio_real">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisCapacitivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisCapFPC<br /><small>(@ViewBag.FatPot_MaisCapFPC_DataHora)</small></td>
                                <td class="relat-fponta">@ViewBag.FatPot_MaisCapFPI<br /><small>(@ViewBag.FatPot_MaisCapFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.FatPot_MaisCapP<br /><small>(@ViewBag.FatPot_MaisCapP_DataHora)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisIndFPC<br /><small>(@ViewBag.FatPot_MaisIndFPC_DataHora)</small></td>
                                <td class="relat-fponta">@ViewBag.FatPot_MaisIndFPI<br /><small>(@ViewBag.FatPot_MaisIndFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.FatPot_MaisIndP<br /><small>(@ViewBag.FatPot_MaisIndP_DataHora)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.NoPeriodo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_NoPeriodoFPC</td>
                                <td class="relat-fponta">@ViewBag.FatPot_NoPeriodoFPI</td>
                                <td class="relat-ponta">@ViewBag.FatPot_NoPeriodoP</td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.Referencia</td>
                                <td class="relat-capacitivo">@ViewBag.RefenciaFPC</td>
                                <td class="relat-fponta">@ViewBag.RefenciaFPI</td>
                                <td class="relat-ponta">@ViewBag.RefenciaFPI</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="relat-tabela simulacao" style="display:none;">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisCapacitivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisCapFPC_Sim<br /><small>(@ViewBag.FatPot_MaisCapFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.FatPot_MaisCapFPI_Sim<br /><small>(@ViewBag.FatPot_MaisCapFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.FatPot_MaisCapP_Sim<br /><small>(@ViewBag.FatPot_MaisCapP_DataHora_Sim)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisIndFPC_Sim<br /><small>(@ViewBag.FatPot_MaisIndFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.FatPot_MaisIndFPI_Sim<br /><small>(@ViewBag.FatPot_MaisIndFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.FatPot_MaisIndP_Sim<br /><small>(@ViewBag.FatPot_MaisIndP_DataHora_Sim)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.NoPeriodo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_NoPeriodoFPC_Sim</td>
                                <td class="relat-fponta">@ViewBag.FatPot_NoPeriodoFPI_Sim</td>
                                <td class="relat-ponta">@ViewBag.FatPot_NoPeriodoP_Sim</td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.Referencia</td>
                                <td class="relat-capacitivo">@ViewBag.RefenciaFPC</td>
                                <td class="relat-fponta">@ViewBag.RefenciaFPI</td>
                                <td class="relat-ponta">@ViewBag.RefenciaFPI</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal inmodal animated fadeIn" id="modalTabela" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-editar-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title"><i class="fa fa-list"></i>&nbsp;&nbsp;@SmartEnergy.Resources.RelatoriosTexts.TabelaRegistros</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-default relatorio_real">

                    @{
                        var k = 0;

                        var dia_semana = new string[] { SmartEnergy.Resources.ComumTexts.domingo,
                                                                    SmartEnergy.Resources.ComumTexts.segunda,
                                                                    SmartEnergy.Resources.ComumTexts.terca,
                                                                    SmartEnergy.Resources.ComumTexts.quarta,
                                                                    SmartEnergy.Resources.ComumTexts.quinta,
                                                                    SmartEnergy.Resources.ComumTexts.sexta,
                                                                    SmartEnergy.Resources.ComumTexts.sabado
                                                                };
                    }

                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:16%;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>

                                @for (k = 0; k < 7; k++)
                                {
                                    <th style="width:12%;">@dia_semana[k]<br />@ViewBag.Dias[k]</th>
                                }

                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var i = 0;
                                var j = 0;
                                var classe = "relat-semreg";
                                var fatpot = "---";

                                for (i = 0; i < 24; i++)
                                {
                                    j = i + 1;

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="relat-preto">@ViewBag.Horas[j]</td>

                                        @for (k = 0; k < 7; k++)
                                        {
                                            switch ((int)ViewBag.Periodo[k, j])
                                            {
                                                case 0:
                                                    classe = "relat-ponta";
                                                    break;

                                                case 1:
                                                    classe = "relat-indutivo";
                                                    break;

                                                case 2:
                                                    classe = "relat-capacitivo";
                                                    break;

                                                default:
                                                case 3:
                                                    classe = "relat-semreg";
                                                    break;
                                            }

                                            if (ViewBag.Periodo[k, j] == 3)
                                            {
                                                fatpot = "---";
                                            }
                                            else
                                            {
                                                fatpot = string.Format("{0:0.000}", ViewBag.FatPot[k, j]);
                                            }

                                            <td style="text-align:center;" class="@classe">@fatpot</td>
                                        }

                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>

                <div class="panel panel-default simulacao" style="display:none;">
                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:16%;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>

                                @for (k = 0; k < 7; k++)
                                {
                                    <th style="width:12%;">@dia_semana[k]<br />@ViewBag.Dias[k]</th>
                                }

                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var classe_sim = "relat-semreg";
                                var fatpot_sim = "---";

                                for (i = 0; i < 24; i++)
                                {
                                    j = i + 1;

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="relat-preto">@ViewBag.Horas_Sim[j]</td>

                                        @for (k = 0; k < 7; k++)
                                        {
                                            switch ((int)ViewBag.Periodo_Sim[k, j])
                                            {
                                                case 0:
                                                    classe_sim = "relat-ponta";
                                                    break;

                                                case 1:
                                                    classe_sim = "relat-indutivo";
                                                    break;

                                                case 2:
                                                    classe_sim = "relat-capacitivo";
                                                    break;

                                                default:
                                                case 3:
                                                    classe_sim = "relat-semreg";
                                                    break;
                                            }

                                            if (ViewBag.Periodo_Sim[k, j] == 3)
                                            {
                                                fatpot_sim = "---";
                                            }
                                            else
                                            {
                                                fatpot_sim = string.Format("{0:0.000}", ViewBag.FatPot_Sim[k, j]);
                                            }

                                            <td style="text-align:center;" class="@classe_sim">@fatpot_sim</td>
                                        }

                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
            </div>
        </div>
    </div>
</div>


@Html.Partial("_EventosModal")

@Html.Partial("_ObservacoesModal")


<script type="text/javascript">

    $(document).ready(function () {

        // dias da semana
        var diasSemana = [];

        diasSemana.push('Domingo');
        diasSemana.push('Segunda');
        diasSemana.push('Terça');
        diasSemana.push('Quarta');
        diasSemana.push('Quinta');
        diasSemana.push('Sexta');
        diasSemana.push('Sábado');
        diasSemana.push('Período');

        // cores
        var coresdiasSemana = [];

        coresdiasSemana.push('#0000FF');
        coresdiasSemana.push('#00FF00');
        coresdiasSemana.push('#FF0000');
        coresdiasSemana.push('#FF7F00');
        coresdiasSemana.push('#FF00FF');
        coresdiasSemana.push('#000000');
        coresdiasSemana.push('#00FFFF');

        // fator de potencia
        var dataX = [];
        var labelX = [];
        var dataReferenciaFPC = [];
        var dataReferenciaFPI = [];
        var dataDom = [];
        var dataSeg = [];
        var dataTer = [];
        var dataQua = [];
        var dataQui = [];
        var dataSex = [];
        var dataSab = [];
        var dataFatPotUm = [];

        // copia valores
        var FatPot = @Html.Raw(Json.Encode(@ViewBag.FatPot));
        var Periodo = @Html.Raw(Json.Encode(@ViewBag.Periodo));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));
        var FatPotMin = @Html.Raw(Json.Encode(@ViewBag.FatPotMin));

        dataX.push('x');
        dataDom.push(['data1']);
        dataSeg.push(['data2']);
        dataTer.push(['data3']);
        dataQua.push(['data4']);
        dataQui.push(['data5']);
        dataSex.push(['data6']);
        dataSab.push(['data7']);
        dataReferenciaFPC.push(['data8']);
        dataReferenciaFPI.push(['data9']);
        dataFatPotUm.push(['data10']);

        for (i = 0; i < 26; i++)
        {
            // X
            dataX.push(Datas[i]);

            // linha de fatpot um
            dataFatPotUm.push([0.0]);

            dataDom.push([ novaEscala(FatPot[i])        ]);
            dataSeg.push([ novaEscala(FatPot[i+26])     ]);
            dataTer.push([ novaEscala(FatPot[i+(2*26)]) ]);
            dataQua.push([ novaEscala(FatPot[i+(3*26)]) ]);
            dataQui.push([ novaEscala(FatPot[i+(4*26)]) ]);
            dataSex.push([ novaEscala(FatPot[i+(5*26)]) ]);
            dataSab.push([ novaEscala(FatPot[i+(6*26)]) ]);

            dataReferenciaFPI.push([0.08]);
            dataReferenciaFPC.push([-0.08]);
        }

        // prepara escala eixo Y
        var maximo = (1.0 - FatPotMin);
        var minimo = -1.0 * maximo;

        // valores label X
        labelX.push(Datas[1]);
        for (i = 3; i <= 24; i+=3)
        {
            labelX.push(Datas[i]);
        }

        var Data = [dataX,dataDom,dataSeg,dataTer,dataQua,dataQui,dataSex,dataSab,dataReferenciaFPC,dataReferenciaFPI,dataFatPotUm];

        var chart = c3.generate({

            bindto: '#fatpot-chart',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 10,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'step',
                    data2: 'step',
                    data3: 'step',
                    data4: 'step',
                    data5: 'step',
                    data6: 'step',
                    data7: 'step',
                    data8: 'step',
                    data9: 'step',
                    data10: 'step',
                    data11: 'step',
                },
                colors: {
                    data1: '#0000FF',
                    data2: '#00FF00',
                    data3: '#FF0000',
                    data4: '#FF7F00',
                    data5: '#FF00FF',
                    data6: '#000000',
                    data7: '#00FFFF',
                    data8: '#1C84C6',
                    data9: '#007F00',
                    data10: '#1C84C6',
                    data11: '#007F00'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*28
                },
                y:  {
                    max: maximo,
                    min: minimo,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            var valor;

                            // converte para nova escala
                            if( d < 0 )
                            {
                                valor = (1.0 - (-1.0*d)) * -1.0;
                            }
                            else
                            {
                                valor = (1.0 - d);
                            }

                            return valor.toFixed(3);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                },
                y: {
                    lines: [{value:0}]
                }
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < d.length; i++) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        // titulo
                        if (! text) {

                            // split string and create array.
                            var arr = Datas[d[0].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // horas e minutos
                            var hours = data.getHours();
                            var minutes = data.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            title = hours + ':' + minutes;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // pega o indice
                        var indice = d[i].name.toString().substr(4, 2);

                        // verifica se eh dia da semana
                        if( indice < 8)
                        {
                            name = diasSemana[indice-1];
                            bgcolor = coresdiasSemana[indice-1];
                            value = FatPot[d[0].index+((indice-1)*26)].toFixed(3);
                            value = value.replace('.', ',');

                            // verifica se sem registro
                            if( Periodo[d[0].index+((indice-1)*26)] == 3 )
                            {
                                value = '---';
                            }

                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                            text += "<td class='value'>" + value + "</td>";
                            text += "</tr>";
                        }
                    }

                    return text + "</table>";
                }
            }
        });

        //
        // GRAFICO SIMULACAO
        //

        // fator de potencia simulacao
        var dataX_Sim = [];
        var labelX_Sim = [];
        var dataDom_Sim = [];
        var dataSeg_Sim = [];
        var dataTer_Sim = [];
        var dataQua_Sim = [];
        var dataQui_Sim = [];
        var dataSex_Sim = [];
        var dataSab_Sim = [];
        var dataFatPotUm_Sim = [];

        // copia valores
        var FatPot_Sim = @Html.Raw(Json.Encode(@ViewBag.FatPot_Sim));
        var Periodo_Sim = @Html.Raw(Json.Encode(@ViewBag.Periodo_Sim));
        var Datas_Sim = @Html.Raw(Json.Encode(@ViewBag.Datas));

        dataX_Sim.push('x');
        dataDom_Sim.push(['data1']);
        dataSeg_Sim.push(['data2']);
        dataTer_Sim.push(['data3']);
        dataQua_Sim.push(['data4']);
        dataQui_Sim.push(['data5']);
        dataSex_Sim.push(['data6']);
        dataSab_Sim.push(['data7']);
        dataReferenciaFPC.push(['data8']);
        dataReferenciaFPI.push(['data9']);
        dataFatPotUm_Sim.push(['data10']);

        for (i = 0; i < 26; i++)
        {
            // X
            dataX_Sim.push(Datas_Sim[i]);

            // linha de fatpot um
            dataFatPotUm_Sim.push([0.0]);

            dataDom_Sim.push([ novaEscala(FatPot_Sim[i])        ]);
            dataSeg_Sim.push([ novaEscala(FatPot_Sim[i+26])     ]);
            dataTer_Sim.push([ novaEscala(FatPot_Sim[i+(2*26)]) ]);
            dataQua_Sim.push([ novaEscala(FatPot_Sim[i+(3*26)]) ]);
            dataQui_Sim.push([ novaEscala(FatPot_Sim[i+(4*26)]) ]);
            dataSex_Sim.push([ novaEscala(FatPot_Sim[i+(5*26)]) ]);
            dataSab_Sim.push([ novaEscala(FatPot_Sim[i+(6*26)]) ]);
        }

        // valores label X
        labelX_Sim.push(Datas_Sim[1]);
        for (i = 3; i <= 24; i+=3)
        {
            labelX_Sim.push(Datas_Sim[i]);
        }

        var Data = [dataX_Sim,dataDom_Sim,dataSeg_Sim,dataTer_Sim,dataQua_Sim,dataQui_Sim,dataSex_Sim,dataSab_Sim,dataReferenciaFPC,dataReferenciaFPI,dataFatPotUm_Sim];

        var chart_sim = c3.generate({

            bindto: '#fatpot-chart-simulacao',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 10,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'step',
                    data2: 'step',
                    data3: 'step',
                    data4: 'step',
                    data5: 'step',
                    data6: 'step',
                    data7: 'step',
                    data8: 'step',
                    data9: 'step',
                    data10: 'step',
                    data11: 'step',
                },
                colors: {
                    data1: '#0000FF',
                    data2: '#00FF00',
                    data3: '#FF0000',
                    data4: '#FF7F00',
                    data5: '#FF00FF',
                    data6: '#000000',
                    data7: '#00FFFF',
                    data8: '#1C84C6',
                    data9: '#007F00',
                    data10: '#1C84C6',
                    data11: '#007F00'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX_Sim,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*28
                },
                y:  {
                    max: maximo,
                    min: minimo,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            var valor;

                            // converte para nova escala
                            if( d < 0 )
                            {
                                valor = (1.0 - (-1.0*d)) * -1.0;
                            }
                            else
                            {
                                valor = (1.0 - d);
                            }

                            return valor.toFixed(3);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                },
                y: {
                    lines: [{value:0}]
                }
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < d.length; i++) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        // titulo
                        if (! text) {

                            // split string and create array.
                            var arr = Datas_Sim[d[0].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // horas e minutos
                            var hours = data.getHours();
                            var minutes = data.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            title = hours + ':' + minutes;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // pega o indice
                        var indice = d[i].name.toString().substr(4, 2);

                        // verifica se eh dia da semana
                        if( indice < 8)
                        {
                            name = diasSemana[indice-1];
                            bgcolor = coresdiasSemana[indice-1];
                            value = FatPot_Sim[d[0].index+((indice-1)*26)].toFixed(3);
                            value = value.replace('.', ',');

                            // verifica se sem registro
                            if( Periodo[d[0].index+((indice-1)*26)] == 3 )
                            {
                                value = '---';
                            }

                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + " - Simulação" + "</td>";
                            text += "<td class='value'>" + value + "</td>";
                            text += "</tr>";
                        }
                    }

                    return text + "</table>";
                }
            }
        });

        // salva chart
        $('#fatpot-chart').data('c3-chart', chart);

        // salva chart simulacao
        $('#fatpot-chart-simulacao').data('c3-chart', chart_sim);

        // label dias da semana
        todosDiaSemana();

        // verifica se apresenta simulacao
        ShowSimulacao();

        // labels
        $('.chart-legend').css("display", "block");

        $(".modal-dialog").draggable({
            handle: ".modal-editar-header"
        });
    });

    function novaEscala(fatpot) {

        var novo = 1.0;

        // converte para nova escala
        if( fatpot < 0.0 )
        {
            novo = (1.0 - (-1.0*fatpot)) * -1.0;
        }
        else
        {
            novo = (1.0 - fatpot);
        }

        return(novo);
    }

    function getCookie(c_name) {

        // Get name followed by anything except a semicolon
        var cookiestring = RegExp("" + c_name + "[^;]+").exec(document.cookie);

        // Return everything after the equal sign, or an empty string if the cookie name not found
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./, "") : "");
    }

    function setCookie(c_name, value, expiresecs) {
        var exdate = new Date();
        exdate.setTime(exdate.getTime() + ((expiresecs) ? expiresecs * 1000 : 0));

        document.cookie = c_name + "=" + escape(value) + ((expiresecs == null) ? "" : ";expires=" + exdate.toGMTString()) + ";path=/";
    }

    function todosDiaSemana() {

        alteraDiaSemana(0,false);
        alteraDiaSemana(1,false);
        alteraDiaSemana(2,false);
        alteraDiaSemana(3,false);
        alteraDiaSemana(4,false);
        alteraDiaSemana(5,false);
        alteraDiaSemana(6,false);
    }

    function alteraDiaSemana(dia_semana, alterar) {

        var c_name,id_name,color_name;

        // dia da semana
        switch(dia_semana)
        {
            case 0:
                c_name = "Relat_SemanalDomingo";
                id_name = "#ckDomingo";
                color_name = "#0000FF";
                break;

            case 1:
                c_name = "Relat_SemanalSegunda";
                id_name = "#ckSegunda";
                color_name = "#00FF00";
                break;

            case 2:
                c_name = "Relat_SemanalTerca";
                id_name = "#ckTerca";
                color_name = "#FF0000";
                break;

            case 3:
                c_name = "Relat_SemanalQuarta";
                id_name = "#ckQuarta";
                color_name = "#FF7F00";
                break;

            case 4:
                c_name = "Relat_SemanalQuinta";
                id_name = "#ckQuinta";
                color_name = "#FF00FF";
                break;

            case 5:
                c_name = "Relat_SemanalSexta";
                id_name = "#ckSexta";
                color_name = "#000000";
                break;

            case 6:
                c_name = "Relat_SemanalSabado";
                id_name = "#ckSabado";
                color_name = "#00FFFF";
                break;
        }

        // le cookie
        var estado = getCookie(c_name);

        if(estado == "")
        {
            estado = 1;
        }

        // verifica se quer alterar
        if( alterar )
        {
            // se estiver ligado, desliga
            estado = (estado == 0) ? 1 : 0;
        }

        // data series
        var data = "data" + (dia_semana+1);
        var chart = $('#fatpot-chart').data('c3-chart');

        // simulacao
        var chart_sim = $('#fatpot-chart-simulacao').data('c3-chart');

        // checkbox aplica simulacao
        var simula = $('#aplica_simula').prop('checked');

        // verifica estado
        if( estado == 1 )
        {
            if (simula) {
                chart_sim.show([data]);
            }
            else{
                chart.show([data]);
            }            

            $(id_name).css("background", color_name);
        }
        else
        {
            if (simula) {
                chart_sim.hide([data]);
            }
            else {
                chart.hide([data]);
            }            

            $(id_name).css("background", "white");
        }

        // salva em cookie
        setCookie(c_name, estado, null);
    }


    function ShowSimulacao () {

        // IDSimulacaoCenario
        var IDSimulacaoCenario = parseInt(@ViewBag._IDSimulacaoCenario);

        // verifica se ano existe nenhuma simulacao selecionada
        if (IDSimulacaoCenario == 0)
        {
            $("#aplica_simula").attr('disabled', true);
            $('#aplica_simula').prop('checked', false);
        }
        else
        {
            $("#aplica_simula").attr('disabled', false);
        }

        // checkbox aplica simulacao
        var simula = $('#aplica_simula').prop('checked');

        // verifica se deve apresentar simulacao
        if (simula)
        {
            $('.simulacao').css("display", "block");
            $('.relatorio_real').css("display", "none");

            todosDiaSemana();
        }
        else
        {
            $('.simulacao').css("display", "none");
            $('.relatorio_real').css("display", "block");

            todosDiaSemana();
        }

        // salva em cookie
        setCookie("AplicaSimulacao", simula, null);
    }

</script>
