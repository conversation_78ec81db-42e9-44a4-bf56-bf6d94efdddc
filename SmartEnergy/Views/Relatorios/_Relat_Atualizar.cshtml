﻿@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

<style>

    .overlay_aguarde {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.45);
        z-index: 9999;
        color: white;
        display: inline-block;
        border-radius: 6px;
        margin-bottom: 20px;
        margin-left: 14px;
        margin-right: 14px;
    }


    .spinner-aguarde .sk-spinner-wave div {
        background-color: #ffffff !important;
    }

    .spinner-aguarde {
        margin: 0;
        position: absolute;
        top: 50%;
        left: 50%;
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
    }

</style>


@{
    int tipo_relat = TIPO_RELAT.Demanda_Ativa;
    int tipo_periodo = TIPO_RELAT_PERIODO.Diario;
    int tipo_medicao = 0;
    bool aplica_simulacao = false;

    if (ViewBag.TipoRelat != null)
    {
        tipo_relat = ViewBag.Relat_Tipo;
        tipo_periodo = ViewBag.Relat_TipoPeriodo;
    }

    if( ViewBag._IDTipoMedicao != null)
    {
        tipo_medicao = ViewBag._IDTipoMedicao;
    }

    if (tipo_relat == TIPO_RELAT.Eventos || tipo_relat == TIPO_RELAT.EventosUsuario)
    {
        tipo_periodo = TIPO_RELAT_PERIODO.Diario;
    }

    if (ViewBag.AplicaSimulacao != null)
    {
        aplica_simulacao = ViewBag.AplicaSimulacao;
    }

    int IDCliente = ViewBag._IDCliente;

    int IDTipoGrafico_Demanda = ViewBag._IDTipoGrafico_Demanda;
}


<div class="overlay_aguarde" style="display: none">
    <div class="spinner-aguarde">
        <div class="sk-spinner sk-spinner-wandering-cubes">
            <div class="sk-spinner sk-spinner-wave">
                <div class="sk-rect1 text-ligh"></div>
                <div class="sk-rect2"></div>
                <div class="sk-rect3"></div>
                <div class="sk-rect4"></div>
                <div class="sk-rect5"></div>
            </div>
        </div>
    </div>
</div>

@{
    if (tipo_relat == TIPO_RELAT.Demanda_Ativa || tipo_relat == TIPO_RELAT.Consumo || tipo_relat == TIPO_RELAT.Demanda_Reativa || tipo_relat == TIPO_RELAT.FatPot || tipo_relat == TIPO_RELAT.FatCarga || tipo_relat == TIPO_RELAT.FatUtilizacao || tipo_relat == TIPO_RELAT.UFER)
    {
        @Html.Partial("~/Views/Simulacao/_Simulacoes.cshtml")
        @Html.Partial("~/Views/Simulacao/_Simulacoes_Editar.cshtml")
        @Html.Partial("~/Views/Simulacao/_Simulacoes_Cargas_Editar.cshtml")
    }
}


<div class="panel-heading">

    <div style="text-align:center;">

        @{
            switch (tipo_relat)
            {
                case TIPO_RELAT.Demanda_Ativa:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.DemandaAtiva</h4>
                    break;

                case TIPO_RELAT.Demanda_Reativa:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.DemandaReativa</h4>
                    break;

                case TIPO_RELAT.Consumo:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.Consumo</h4>
                    break;

                case TIPO_RELAT.FatPot:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia</h4>
                    break;

                case TIPO_RELAT.FatCarga:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.FatorCarga</h4>
                    break;

                case TIPO_RELAT.FatUtilizacao:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao</h4>
                    break;

                case TIPO_RELAT.UFER:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.UFER</h4>
                    break;

                case TIPO_RELAT.Eventos:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.Eventos</h4>
                    break;

                case TIPO_RELAT.EventosUsuario:

                    if (IDCliente > 0)
                    {
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.EventosUsuario Cliente</h4>
                    }
                    else
                    {
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.EventosUsuario Gestor</h4>
                    }

                    break;

                case TIPO_RELAT.Utilidades:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.Utilidades</h4>
                    break;

                case TIPO_RELAT.Analogica:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.EA</h4>
                    break;

                case TIPO_RELAT.Ciclometro:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.Ciclometro</h4>
                    break;

                case TIPO_RELAT.Meteorologia:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.Meteorologia</h4>
                    break;

                case TIPO_RELAT.ConsolidadoEnergia_Diario:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.ConsolidadoDiario</h4>
                    break;

                case TIPO_RELAT.ConsolidadoEnergia_Mensal:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.ConsolidadoMensal</h4>
                    break;

                case TIPO_RELAT.ConsolidadoEnergia_Anual:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.ConsolidadoAnual</h4>
                    break;

                case TIPO_RELAT.ConsolidadoEA_Diario:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.ConsolidadoDiario</h4>
                    break;

                case TIPO_RELAT.ConsolidadoEA_Mensal:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.ConsolidadoMensal</h4>
                    break;

                case TIPO_RELAT.ConsolidadoEA_Anual:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.ConsolidadoAnual</h4>
                    break;

                case TIPO_RELAT.ConsolidadoUtilidades_Diario:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.ConsolidadoDiario</h4>
                    break;

                case TIPO_RELAT.ConsolidadoUtilidades_Mensal:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.ConsolidadoMensal</h4>
                    break;

                case TIPO_RELAT.ConsolidadoUtilidades_Anual:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.ConsolidadoAnual</h4>
                    break;

                case TIPO_RELAT.TempoAtuacao_EntradasDigitais_Mensal:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.TempoAtuacao - @SmartEnergy.Resources.ConfiguracaoTexts.EntradasDigitais</h4>
                    break;

                case TIPO_RELAT.TempoAtuacao_SaidasDigitais_Mensal:
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.TempoAtuacao - @SmartEnergy.Resources.ConfiguracaoTexts.SaidasDigitais</h4>
                    break;

                case TIPO_RELAT.Provisionamento_Semanal_Tipo1_Tipo2:
                case TIPO_RELAT.Provisionamento_Semanal_Tipo3:
                    <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralRelatoriosProvisionamentoSemanal</h4>
                    break;
            }
        }

    </div>

    <div class="pull-left relat-navega">
        <h4>

            @{

                switch (tipo_periodo)
                {
                    case TIPO_RELAT_PERIODO.Diario:

                        <a class="link_mes_menos">
                            <i class="fa fa-angle-double-left" data-toggle="tooltip" data-placement="left" title="@SmartEnergy.Resources.RelatoriosTexts.MesAnterior"></i>
                        </a>
                        <a class="link_dia_menos">
                            <i class="fa fa-angle-left" data-toggle="tooltip" data-placement="left" title="@SmartEnergy.Resources.RelatoriosTexts.DiaAnterior"></i>
                        </a>
                        <a class="link_dia_mais">
                            <i class="fa fa-angle-right" data-toggle="tooltip" data-placement="left" title="@SmartEnergy.Resources.RelatoriosTexts.DiaSeguinte"></i>
                        </a>
                        <a class="link_mes_mais">
                            <i class="fa fa-angle-double-right" data-toggle="tooltip" data-placement="left" title="@SmartEnergy.Resources.RelatoriosTexts.MesSeguinte"></i>
                        </a>

                        break;

                    case TIPO_RELAT_PERIODO.Semanal:

                        <a class="link_mes_menos">
                            <i class="fa fa-angle-double-left" data-toggle="tooltip" data-placement="left" title="@SmartEnergy.Resources.RelatoriosTexts.MesAnterior"></i>
                        </a>
                        <a class="link_semana_menos">
                            <i class="fa fa-angle-left" data-toggle="tooltip" data-placement="left" title="@SmartEnergy.Resources.RelatoriosTexts.SemanaAnterior"></i>
                        </a>
                        <a class="link_semana_mais">
                            <i class="fa fa-angle-right" data-toggle="tooltip" data-placement="left" title="@SmartEnergy.Resources.RelatoriosTexts.SemanaSeguinte"></i>
                        </a>
                        <a class="link_mes_mais">
                            <i class="fa fa-angle-double-right" data-toggle="tooltip" data-placement="left" title="@SmartEnergy.Resources.RelatoriosTexts.MesSeguinte"></i>
                        </a>

                        break;

                    case TIPO_RELAT_PERIODO.Mensal:

                        <a class="link_ano_menos">
                            <i class="fa fa-angle-double-left" data-toggle="tooltip" data-placement="left" title="@SmartEnergy.Resources.RelatoriosTexts.AnoAnterior"></i>
                        </a>
                        <a class="link_mes_menos">
                            <i class="fa fa-angle-left" data-toggle="tooltip" data-placement="left" title="@SmartEnergy.Resources.RelatoriosTexts.MesAnterior"></i>
                        </a>
                        <a class="link_mes_mais">
                            <i class="fa fa-angle-right" data-toggle="tooltip" data-placement="left" title="@SmartEnergy.Resources.RelatoriosTexts.MesSeguinte"></i>
                        </a>
                        <a class="link_ano_mais">
                            <i class="fa fa-angle-double-right" data-toggle="tooltip" data-placement="left" title="@SmartEnergy.Resources.RelatoriosTexts.AnoSeguinte"></i>
                        </a>

                        break;

                    case TIPO_RELAT_PERIODO.Anual:

                        <a class="link_ano_menos">
                            <i class="fa fa-angle-left" data-toggle="tooltip" data-placement="left" title="@SmartEnergy.Resources.RelatoriosTexts.AnoAnterior"></i>
                        </a>
                        <a class="link_ano_mais">
                            <i class="fa fa-angle-right" data-toggle="tooltip" data-placement="left" title="@SmartEnergy.Resources.RelatoriosTexts.AnoSeguinte"></i>
                        </a>

                        break;

                    case TIPO_RELAT_PERIODO.NaoUtiliza:

                        break;
                }

                if (tipo_periodo != TIPO_RELAT_PERIODO.NaoUtiliza)
                {
                    <a class="link_ultimo">
                        <i class="fa fa-terminal" data-toggle="tooltip" data-placement="left" title="@SmartEnergy.Resources.RelatoriosTexts.UltimaData"></i>
                    </a>
                }
            }

        </h4>
    </div>

    <div class="pull-right relat-tools">
        <h4>

            @{
                if (tipo_relat == TIPO_RELAT.Demanda_Ativa || tipo_relat == TIPO_RELAT.Consumo || tipo_relat == TIPO_RELAT.Demanda_Reativa || tipo_relat == TIPO_RELAT.FatPot || tipo_relat == TIPO_RELAT.FatCarga || tipo_relat == TIPO_RELAT.FatUtilizacao || tipo_relat == TIPO_RELAT.UFER)
                {
                    <a onclick="SelecionarSimulacao();"><i class="fa fa-cogs fa-spin" data-toggle="tooltip" data-placement="left" title="@SmartEnergy.Resources.RelatoriosTexts.Simulacoes" style="font-size:22px; padding-top: 6px;"></i></a>
                }
            }
            <a href="@Url.Action("Relat_Grafico_Print", "Relatorios")" target="_blank"><i class="fa fa-print" data-toggle="tooltip" data-placement="left" title="@SmartEnergy.Resources.ComumTexts.BotaoImprimir"></i></a>
            <a data-toggle="modal" href="#modalEMAIL"><i class="fa fa-envelope-o" data-toggle="tooltip" data-placement="left" title="Email"></i></a>


            @{
                if (tipo_relat != TIPO_RELAT.ConsolidadoEnergia_Diario && tipo_relat != TIPO_RELAT.ConsolidadoEnergia_Mensal && tipo_relat != TIPO_RELAT.ConsolidadoEnergia_Anual)
                {
                    <a data-toggle="modal" href="#modalPDF"><i class="fa fa-file-pdf-o" data-toggle="tooltip" data-placement="left" title="PDF"></i></a>
                }
            }
    
            @{
                if (tipo_relat != TIPO_RELAT.Provisionamento_Semanal_Tipo1_Tipo2 && tipo_relat != TIPO_RELAT.Provisionamento_Semanal_Tipo3)
                {
                    <a data-toggle="modal" href="#modalXLS"><i class="fa fa-file-excel-o" data-toggle="tooltip" data-placement="left" title="XLS"></i></a>
                }
            }

            @{
                if (tipo_relat != TIPO_RELAT.FatCarga && 
                    tipo_relat != TIPO_RELAT.FatUtilizacao && 
                    tipo_relat != TIPO_RELAT.Eventos && 
                    tipo_relat != TIPO_RELAT.EventosUsuario && 
                    tipo_relat != TIPO_RELAT.ConsolidadoEnergia_Diario && 
                    tipo_relat != TIPO_RELAT.ConsolidadoEnergia_Mensal && 
                    tipo_relat != TIPO_RELAT.ConsolidadoEnergia_Anual && 
                    tipo_relat != TIPO_RELAT.ConsolidadoEA_Diario &&
                    tipo_relat != TIPO_RELAT.ConsolidadoEA_Mensal &&
                    tipo_relat != TIPO_RELAT.ConsolidadoEA_Anual &&
                    tipo_relat != TIPO_RELAT.ConsolidadoUtilidades_Diario &&
                    tipo_relat != TIPO_RELAT.ConsolidadoUtilidades_Mensal &&
                    tipo_relat != TIPO_RELAT.ConsolidadoUtilidades_Anual &&
                    tipo_relat != TIPO_RELAT.TempoAtuacao_EntradasDigitais_Mensal &&
                    tipo_relat != TIPO_RELAT.TempoAtuacao_SaidasDigitais_Mensal &&
                    tipo_relat != TIPO_RELAT.Provisionamento_Semanal_Tipo1_Tipo2 &&
                    tipo_relat != TIPO_RELAT.Provisionamento_Semanal_Tipo3 &&
                    tipo_relat != 100 && tipo_relat != 101 && tipo_relat != 102 && tipo_relat != 103)
                {
                    <a data-toggle="modal" href="#modalEXPORTAR"><i class="fa fa-download" data-toggle="tooltip" data-placement="left" title="@SmartEnergy.Resources.ComumTexts.BotaoExportar"></i></a>
                }
            }
        </h4>
    </div>

</div>

<div class="panel-body">

    <div class="modal inmodal animated fadeIn" id="modalEMAIL" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">@SmartEnergy.Resources.ComumTexts.BotaoFechar</span></button>
                    <h4 class="modal-title"><i class="fa fa-envelope-o"></i>&nbsp;&nbsp;@SmartEnergy.Resources.RelatoriosTexts.RelatorioEmail</h4>
                </div>

                <form id="formEMAIL" action="/Relatorios/Relat_Grafico_EMAIL" method="get">

                    <div class="modal-body">

                        <div class="row">
                            <div class="form-group col-xs-6">
                                <label class="control-label">&nbsp;Email</label>
                                <input class="form-control" id="Email" name="Email" type="email" value="@ViewBag._EmailUsuario" />
                            </div>
                        </div>

                        <div class="row">
                            <div class="form-group col-xs-10">
                                <label class="control-label">&nbsp;@SmartEnergy.Resources.RelatoriosTexts.Assunto</label>
                                <input class="form-control" id="Assunto" name="Assunto" type="text" value="@ViewBag.NomeRelat" />
                            </div>
                        </div>

                        @if (tipo_relat != TIPO_RELAT.Eventos && 
                             tipo_relat != TIPO_RELAT.EventosUsuario && 
                             tipo_relat != TIPO_RELAT.ConsolidadoEnergia_Diario && 
                             tipo_relat != TIPO_RELAT.ConsolidadoEnergia_Mensal && 
                             tipo_relat != TIPO_RELAT.ConsolidadoEnergia_Anual && 
                             tipo_relat != TIPO_RELAT.ConsolidadoEA_Diario &&
                             tipo_relat != TIPO_RELAT.ConsolidadoEA_Mensal &&
                             tipo_relat != TIPO_RELAT.ConsolidadoEA_Anual &&
                             tipo_relat != TIPO_RELAT.ConsolidadoUtilidades_Diario &&
                             tipo_relat != TIPO_RELAT.ConsolidadoUtilidades_Mensal &&
                             tipo_relat != TIPO_RELAT.ConsolidadoUtilidades_Anual &&
                             tipo_relat != TIPO_RELAT.TempoAtuacao_EntradasDigitais_Mensal &&
                             tipo_relat != TIPO_RELAT.TempoAtuacao_SaidasDigitais_Mensal &&
                             tipo_relat != TIPO_RELAT.Provisionamento_Semanal_Tipo1_Tipo2 &&
                             tipo_relat != TIPO_RELAT.Provisionamento_Semanal_Tipo3 )
                        {
                            <br /><br />
                            <div class="row">
                                <div class="form-group col-xs-6">
                                    <label> <input type="checkbox" class="i-checks" id="showGraficoEmail" checked> @SmartEnergy.Resources.RelatoriosTexts.ApresentarGrafico</label><br /><br />
                                    <label> <input type="checkbox" class="i-checks" id="showTabelaEmail"> @SmartEnergy.Resources.RelatoriosTexts.ApresentarTabela</label>
                                </div>
                            </div>
                        }
                    </div>
                    <div class="modal-footer">
                        <span class="pull-left" style="font-weight:bold; font-size:14px; margin-top:7px;">@SmartEnergy.Resources.RelatoriosTexts.ProcessoPodeDemorar</span>
                        <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                        <button type="submit" class="btn btn-primary">@SmartEnergy.Resources.RelatoriosTexts.EnviarEmail</button>
                    </div>

                </form>
            </div>
        </div>
    </div>

    <div class="modal inmodal animated fadeIn" id="modalPDF" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">@SmartEnergy.Resources.ComumTexts.BotaoFechar</span></button>
                    <h4 class="modal-title"><i class="fa fa-file-pdf-o"></i>&nbsp;&nbsp;@SmartEnergy.Resources.RelatoriosTexts.RelatorioPDF</h4>
                </div>
                <div class="modal-body">

                    <br /><p><label>@SmartEnergy.Resources.RelatoriosTexts.InfoPDF</label></p><br />

                    @if (tipo_relat != TIPO_RELAT.Eventos && 
                        tipo_relat != TIPO_RELAT.EventosUsuario && 
                        tipo_relat != TIPO_RELAT.ConsolidadoEnergia_Diario && 
                        tipo_relat != TIPO_RELAT.ConsolidadoEnergia_Mensal && 
                        tipo_relat != TIPO_RELAT.ConsolidadoEnergia_Anual && 
                        tipo_relat != TIPO_RELAT.ConsolidadoEA_Diario &&
                        tipo_relat != TIPO_RELAT.ConsolidadoEA_Mensal &&
                        tipo_relat != TIPO_RELAT.ConsolidadoEA_Anual &&                        
                        tipo_relat != TIPO_RELAT.ConsolidadoUtilidades_Diario &&
                        tipo_relat != TIPO_RELAT.ConsolidadoUtilidades_Mensal &&
                        tipo_relat != TIPO_RELAT.ConsolidadoUtilidades_Anual &&
                        tipo_relat != TIPO_RELAT.TempoAtuacao_EntradasDigitais_Mensal &&
                        tipo_relat != TIPO_RELAT.TempoAtuacao_SaidasDigitais_Mensal &&        
                        tipo_relat != TIPO_RELAT.Provisionamento_Semanal_Tipo1_Tipo2 &&
                        tipo_relat != TIPO_RELAT.Provisionamento_Semanal_Tipo3 )
                    {
                        <label> <input type="checkbox" class="i-checks" id="showGrafico" checked> @SmartEnergy.Resources.RelatoriosTexts.ApresentarGrafico</label><br /><br />
                        <label> <input type="checkbox" class="i-checks" id="showTabela"> @SmartEnergy.Resources.RelatoriosTexts.ApresentarTabela</label><br /><br />
                    }

                </div>
                <div class="modal-footer">
                    <span class="pull-left" style="font-weight:bold; font-size:14px; margin-top:7px;">@SmartEnergy.Resources.RelatoriosTexts.ProcessoPodeDemorar</span>
                    <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                    <button type="button" class="btn btn-primary" onclick="gerarPDF();" data-dismiss="modal">@SmartEnergy.Resources.RelatoriosTexts.GerarPDF</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal inmodal animated fadeIn" id="modalXLS" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">@SmartEnergy.Resources.ComumTexts.BotaoFechar</span></button>
                    <h4 class="modal-title"><i class="fa fa-file-excel-o"></i>&nbsp;&nbsp;@SmartEnergy.Resources.RelatoriosTexts.PlanilhaXLS</h4>
                </div>
                <div class="modal-body">
                    <br /><p><label>@SmartEnergy.Resources.RelatoriosTexts.InfoExcel</label></p>
                </div>
                <div class="modal-footer">
                    @if (tipo_relat == TIPO_RELAT.Eventos || tipo_relat == TIPO_RELAT.EventosUsuario)
                    {
                        <span class="pull-left" style="font-weight:bold; font-size:14px; margin-top:7px;">@SmartEnergy.Resources.RelatoriosTexts.FiltroNaoAplica</span>
                    }                        
                    <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                    <button type="button" class="btn btn-primary" onclick="gerarXLS();" data-dismiss="modal">@SmartEnergy.Resources.RelatoriosTexts.GerarXLS</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal inmodal animated fadeIn" id="modalEXPORTAR" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">@SmartEnergy.Resources.ComumTexts.BotaoFechar</span></button>
                    <h4 class="modal-title"><i class="fa fa-download"></i>&nbsp;&nbsp;@SmartEnergy.Resources.ComumTexts.BotaoExportar</h4>
                </div>
                <div class="modal-body" style="min-height:100px;">
                    
                    <row>
                        <div class="col-lg-6">
                            <div class="form-group">
                                <label class="control-label">&nbsp;Início</label>
                                <div class="input-group" id="startDateExportar">
                                    <input type='text' class="form-control dateiniExportar" name="data_ini_exportar" value=@ViewBag.DataIniExportar>
                                    <span class="input-group-addon">
                                        <i class="fa fa-calendar"></i>&nbsp;&nbsp;<i class="fa fa-clock-o"></i>
                                    </span>
                                    <div class="input-group" id="startTimeExportar">
                                        <input type="text" class="form-control timeIniExportar" name="hora_ini_exportar" value=@ViewBag.HoraIniExportar disabled>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="form-group">
                                <label class="control-label">&nbsp;Fim</label>
                                <div class="input-group" id="endDateExportar">
                                    <input type='text' class="form-control datefimExportar" name="data_fim_exportar" value=@ViewBag.DataFimExportar>
                                    <span class="input-group-addon">
                                        <i class="fa fa-calendar"></i>&nbsp;&nbsp;<i class="fa fa-clock-o"></i>
                                    </span>
                                    <div class="input-group" id="endTimeExportar">
                                        <input type='text' class="form-control timefimExportar" name="hora_fim_exportar" value=@ViewBag.HoraFimExportar disabled>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </row>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                    <button type="button" class="btn btn-primary" onclick="exportarXLS();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoExportar</button>
                </div>
            </div>
        </div>
    </div>


    <div class="row">

        @{
            switch (tipo_relat)
            {
                case TIPO_RELAT.Demanda_Ativa:
                case TIPO_RELAT.Consumo:
                case TIPO_RELAT.Demanda_Reativa:
                case TIPO_RELAT.FatPot:
                case TIPO_RELAT.FatCarga:
                case TIPO_RELAT.FatUtilizacao:
                case TIPO_RELAT.UFER:

                    <div class="col-lg-4">
                        <div class="form-group" id="data_relat">
                            <div class="input-group">
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span><input type="text" class="form-control" name="data_relat" value=@ViewBag.DataAtual>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="form-group">
                            <select class="form-control m-b" id="Relat_TipoPeriodo" onchange="SelecionouTipoPeriodo()">

                                @{
                                    var classe = "";

                                    if (tipo_periodo == TIPO_RELAT_PERIODO.Diario) { classe = "selected"; } else { classe = ""; }
                                    <option @classe>@SmartEnergy.Resources.RelatoriosTexts.RelatDiario</option>

                                    if (tipo_periodo == TIPO_RELAT_PERIODO.Semanal) { classe = "selected"; } else { classe = ""; }
                                    <option @classe>@SmartEnergy.Resources.RelatoriosTexts.RelatSemanal</option>

                                    if (tipo_periodo == TIPO_RELAT_PERIODO.Mensal) { classe = "selected"; } else { classe = ""; }
                                    <option @classe>@SmartEnergy.Resources.RelatoriosTexts.RelatMensal</option>

                                    if (tipo_periodo == TIPO_RELAT_PERIODO.Anual) { classe = "selected"; } else { classe = ""; }
                                    <option @classe>@SmartEnergy.Resources.RelatoriosTexts.RelatAnual</option>
                                }

                            </select>
                        </div>
                    </div>

                    <div class="col-lg-2">
                        <div class="i-checks" style="margin-top: 6px;">
                            @Html.CheckBox("AplicaSimulacao", aplica_simulacao, new { id = "aplica_simula", @onchange = "ShowSimulacao()", disabled = "" })&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.SimulacaoTexts.Aplicar</span>
                        </div>
                    </div>

                    break;

                case TIPO_RELAT.Utilidades:
                case TIPO_RELAT.Analogica:
                case TIPO_RELAT.Ciclometro:
                case TIPO_RELAT.Meteorologia:

                    <div class="col-lg-4">
                        <div class="form-group" id="data_relat">
                            <div class="input-group">
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span><input type="text" class="form-control" name="data_relat" value=@ViewBag.DataAtual>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-8">
                        <div class="form-group">
                            <select class="form-control m-b" id="Relat_TipoPeriodo" onchange="SelecionouTipoPeriodo()">

                                @{
                                    classe = "";

                                    if (tipo_periodo == TIPO_RELAT_PERIODO.Diario) { classe = "selected"; } else { classe = ""; }
                                    <option @classe>@SmartEnergy.Resources.RelatoriosTexts.RelatDiario</option>

                                    if (tipo_periodo == TIPO_RELAT_PERIODO.Semanal) { classe = "selected"; } else { classe = ""; }
                                    <option @classe>@SmartEnergy.Resources.RelatoriosTexts.RelatSemanal</option>

                                    if (tipo_periodo == TIPO_RELAT_PERIODO.Mensal) { classe = "selected"; } else { classe = ""; }
                                    <option @classe>@SmartEnergy.Resources.RelatoriosTexts.RelatMensal</option>

                                    if (tipo_periodo == TIPO_RELAT_PERIODO.Anual) { classe = "selected"; } else { classe = ""; }
                                    <option @classe>@SmartEnergy.Resources.RelatoriosTexts.RelatAnual</option>
                                }

                            </select>
                        </div>
                    </div>

                    break;

                case TIPO_RELAT.Eventos:
                case TIPO_RELAT.EventosUsuario:

                    <div class="col-lg-4">
                        <div class="form-group">
                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ComumTexts.Início</label>
                            <div class="input-group" id="startDate">
                                <input type='text' class="form-control dateini" name="data_ini" value=@ViewBag.DataIni>
                                <span class="input-group-addon">
                                    <i class="fa fa-calendar"></i>&nbsp;&nbsp;<i class="fa fa-clock-o"></i>
                                </span>
                                <div class="input-group" id="startTime">
                                    <input type="text" class="form-control timeIni" name="hora_ini" value=@ViewBag.HoraIni>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ComumTexts.Fim</label>
                        <div class="form-group">
                            <div class="input-group" id="endDate">
                                <input type='text' class="form-control datefim" name="data_fim" value=@ViewBag.DataFim>
                                <span class="input-group-addon">
                                    <i class="fa fa-calendar"></i>&nbsp;&nbsp;<i class="fa fa-clock-o"></i>
                                </span>
                                <div class="input-group" id="endTime">
                                    <input type='text' class="form-control timefim" name="hora_fim" value=@ViewBag.HoraFim>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4" style="margin-top:2px;">
                        <label class="control-label">&nbsp;</label>

                        @if (tipo_relat == TIPO_RELAT.Eventos)
                        {
                            <a class="btn btn-primary btn-sm btn-block" href="javascript:ExecutarEventos()" style="color:#ffffff; font-weight:bold;">@SmartEnergy.Resources.ComumTexts.BotaoAtualizar</a>
                        }
                        else
                        {
                            <a class="btn btn-primary btn-sm btn-block" href="javascript:ExecutarEventosUsuario()" style="color:#ffffff; font-weight:bold;">@SmartEnergy.Resources.ComumTexts.BotaoAtualizar</a>
                        }

                    </div>

                    break;

                case TIPO_RELAT.ConsolidadoEnergia_Diario:

                    <div class="col-lg-4">
                        <div class="form-group" id="data_relat">
                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ComumTexts.Data</label>
                            <div class="input-group">
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span><input type="text" class="form-control" name="data_relat" value=@ViewBag.DataAtual>
                            </div>
                        </div>
                    </div>

                    break;

                case TIPO_RELAT.ConsolidadoEnergia_Mensal:

                    <div class="col-lg-4">
                        <div class="form-group" id="data_relat_consolidado">
                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ComumTexts.Data</label>
                            <div class="input-group">
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span><input type="text" class="form-control" name="data_relat_consolidado" value=@ViewBag.DataAtual>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3">
                        <div class="form-group">
                            <label class="control-label">&nbsp;Turno</label>
                            <div class="input-group" id="startTime">
                                <input type='text' class="form-control timeIni" name="hora_ini" value=@ViewBag.TurnoIni>
                                <span class="input-group-addon">
                                    <i class="fa fa-clock-o"></i>
                                </span>
                                <div class="input-group" id="endTime">
                                    <input type="text" class="form-control timeFim" name="hora_fim" value=@ViewBag.TurnoFim>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-offset-2 col-lg-3" style="margin-top:2px;">
                        <label class="control-label">&nbsp;</label>
                        <a class="btn btn-primary btn-sm btn-block" href="javascript:ExecutarConsolidadoMensal()" style="color:#ffffff; font-weight:bold;">@SmartEnergy.Resources.ComumTexts.BotaoAtualizar</a>
                    </div>

                    break;

                case TIPO_RELAT.ConsolidadoEnergia_Anual:

                    <div class="col-lg-4">
                        <div class="form-group" id="data_relat">
                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ComumTexts.Data</label>
                            <div class="input-group">
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span><input type="text" class="form-control" name="data_relat" value=@ViewBag.DataAtual>
                            </div>
                        </div>
                    </div>

                    break;

                case TIPO_RELAT.ConsolidadoEA_Diario:

                    <div class="col-lg-4">
                        <div class="form-group" id="data_relat">
                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ComumTexts.Data</label>
                            <div class="input-group">
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span><input type="text" class="form-control" name="data_relat" value=@ViewBag.DataAtual>
                            </div>
                        </div>
                    </div>

                    break;

                case TIPO_RELAT.ConsolidadoEA_Mensal:

                    <div class="col-lg-4">
                        <div class="form-group" id="data_relat">
                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ComumTexts.Data</label>
                            <div class="input-group">
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span><input type="text" class="form-control" name="data_relat" value=@ViewBag.DataAtual>
                            </div>
                        </div>
                    </div>

                    break;

                case TIPO_RELAT.ConsolidadoEA_Anual:

                    <div class="col-lg-4">
                        <div class="form-group" id="data_relat">
                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ComumTexts.Data</label>
                            <div class="input-group">
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span><input type="text" class="form-control" name="data_relat" value=@ViewBag.DataAtual>
                            </div>
                        </div>
                    </div>

                    break;

                case TIPO_RELAT.ConsolidadoUtilidades_Diario:

                    <div class="col-lg-4">
                        <div class="form-group" id="data_relat">
                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ComumTexts.Data</label>
                            <div class="input-group">
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span><input type="text" class="form-control" name="data_relat" value=@ViewBag.DataAtual>
                            </div>
                        </div>
                    </div>

                    break;

                case TIPO_RELAT.ConsolidadoUtilidades_Mensal:

                    <div class="col-lg-4">
                        <div class="form-group" id="data_relat">
                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ComumTexts.Data</label>
                            <div class="input-group">
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span><input type="text" class="form-control" name="data_relat" value=@ViewBag.DataAtual>
                            </div>
                        </div>
                    </div>

                    break;

                case TIPO_RELAT.ConsolidadoUtilidades_Anual:

                    <div class="col-lg-4">
                        <div class="form-group" id="data_relat">
                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ComumTexts.Data</label>
                            <div class="input-group">
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span><input type="text" class="form-control" name="data_relat" value=@ViewBag.DataAtual>
                            </div>
                        </div>
                    </div>

                    break;

                case TIPO_RELAT.TempoAtuacao_EntradasDigitais_Mensal:

                    <div class="col-lg-4">
                        <div class="form-group" id="data_relat">
                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ComumTexts.Data</label>
                            <div class="input-group">
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span><input type="text" class="form-control" name="data_relat" value=@ViewBag.DataAtual>
                            </div>
                        </div>
                    </div>

                    break;

                case TIPO_RELAT.TempoAtuacao_SaidasDigitais_Mensal:

                    <div class="col-lg-4">
                        <div class="form-group" id="data_relat">
                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ComumTexts.Data</label>
                            <div class="input-group">
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span><input type="text" class="form-control" name="data_relat" value=@ViewBag.DataAtual>
                            </div>
                        </div>
                    </div>

                    break;

                case TIPO_RELAT.Provisionamento_Semanal_Tipo1_Tipo2:
                case TIPO_RELAT.Provisionamento_Semanal_Tipo3:

                    <div class="col-lg-4">
                        <div class="form-group" id="data_relat">
                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ComumTexts.Data</label>
                            <div class="input-group">
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span><input type="text" class="form-control" name="data_relat" value=@ViewBag.DataAtual>
                            </div>
                        </div>
                    </div>

                    break;
            }
        }

    </div>

                    <div id="relat_resultado" style="display:block;">
                        @{
                            if (ViewBag.Retorno == 2)
                            {
                                <br /><br /><br /><br /><br /><br />
                                <div class="row">
                                    <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
                                        <i class="fa fa-warning fa-5x"></i>
                                    </div>
                                </div>
                                <br />
                                <div class="row">
                                    <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
                                        <h2>@SmartEnergy.Resources.RelatoriosTexts.NaoExistemRegistros</h2>
                                    </div>
                                </div>
                                <br /><br /><br /><br />
                            }
                            else
                            {
                                switch (tipo_relat)
                                {
                                    case TIPO_RELAT.Demanda_Ativa:

                                        switch (tipo_periodo)
                                        {
                                            case TIPO_RELAT_PERIODO.Diario:

                                                // gráfico em barra
                                                if (IDTipoGrafico_Demanda == 0)
                                                {
                                                    @Html.Partial("_Dem_Ativa_Diario_Barra")
                                                }
                                                else
                                                {
                                                    @Html.Partial("_Dem_Ativa_Diario_Area")
                                                }

                                                break;

                                            case TIPO_RELAT_PERIODO.Semanal:
                                                @Html.Partial("_Dem_Ativa_Semanal")
                                                break;

                                            case TIPO_RELAT_PERIODO.Mensal:
                                                @Html.Partial("_Dem_Ativa_Mensal")
                                                break;

                                            case TIPO_RELAT_PERIODO.Anual:
                                                @Html.Partial("_Dem_Ativa_Anual")
                                                break;
                                        }

                                        break;

                                    case TIPO_RELAT.Demanda_Reativa:

                                        switch (tipo_periodo)
                                        {
                                            case TIPO_RELAT_PERIODO.Diario:

                                                // gráfico em barra
                                                if (IDTipoGrafico_Demanda == 0)
                                                {
                                                    @Html.Partial("_Dem_Reativa_Diario_Barra")
                                                }
                                                else
                                                {
                                                    @Html.Partial("_Dem_Reativa_Diario_Area")
                                                }

                                                break;

                                            case TIPO_RELAT_PERIODO.Semanal:
                                                @Html.Partial("_Dem_Reativa_Semanal")
                                                break;

                                            case TIPO_RELAT_PERIODO.Mensal:
                                                @Html.Partial("_Dem_Reativa_Mensal")
                                                break;

                                            case TIPO_RELAT_PERIODO.Anual:
                                                @Html.Partial("_Dem_Reativa_Anual")
                                                break;
                                        }

                                        break;

                                    case TIPO_RELAT.Consumo:

                                        switch (tipo_periodo)
                                        {
                                            case TIPO_RELAT_PERIODO.Diario:
                                                @Html.Partial("_Consumo_Diario")
                                                break;

                                            case TIPO_RELAT_PERIODO.Semanal:
                                                @Html.Partial("_Consumo_Semanal")
                                                break;

                                            case TIPO_RELAT_PERIODO.Mensal:
                                                @Html.Partial("_Consumo_Mensal")
                                                break;

                                            case TIPO_RELAT_PERIODO.Anual:
                                                @Html.Partial("_Consumo_Anual")
                                                break;
                                        }

                                        break;

                                    case TIPO_RELAT.FatPot:

                                        switch (tipo_periodo)
                                        {
                                            case TIPO_RELAT_PERIODO.Diario:
                                                @Html.Partial("_FatPot_Diario")
                                                break;

                                            case TIPO_RELAT_PERIODO.Semanal:
                                                @Html.Partial("_FatPot_Semanal")
                                                break;

                                            case TIPO_RELAT_PERIODO.Mensal:
                                                @Html.Partial("_FatPot_Mensal")
                                                break;

                                            case TIPO_RELAT_PERIODO.Anual:
                                                @Html.Partial("_FatPot_Anual")
                                                break;
                                        }

                                        break;

                                    case TIPO_RELAT.FatCarga:

                                        switch (tipo_periodo)
                                        {
                                            case TIPO_RELAT_PERIODO.Diario:
                                                @Html.Partial("_FatCarga_Diario")
                                                break;

                                            case TIPO_RELAT_PERIODO.Semanal:
                                                @Html.Partial("_FatCarga_Semanal")
                                                break;

                                            case TIPO_RELAT_PERIODO.Mensal:
                                                @Html.Partial("_FatCarga_Mensal")
                                                break;

                                            case TIPO_RELAT_PERIODO.Anual:
                                                @Html.Partial("_FatCarga_Anual")
                                                break;
                                        }

                                        break;

                                    case TIPO_RELAT.FatUtilizacao:

                                        switch (tipo_periodo)
                                        {
                                            case TIPO_RELAT_PERIODO.Diario:

                                                if (IDTipoGrafico_Demanda == 0)
                                                {
                                                    @Html.Partial("_FatUtilizacao_Diario_Barra")
                                                }
                                                else
                                                {
                                                    @Html.Partial("_FatUtilizacao_Diario_Area")
                                                }
                                                break;

                                            case TIPO_RELAT_PERIODO.Semanal:
                                                @Html.Partial("_FatUtilizacao_Semanal")
                                                break;

                                            case TIPO_RELAT_PERIODO.Mensal:
                                                @Html.Partial("_FatUtilizacao_Mensal")
                                                break;

                                            case TIPO_RELAT_PERIODO.Anual:
                                                @Html.Partial("_FatUtilizacao_Anual")
                                                break;
                                        }

                                        break;

                                    case TIPO_RELAT.UFER:

                                        switch (tipo_periodo)
                                        {
                                            case TIPO_RELAT_PERIODO.Diario:
                                                @Html.Partial("_UFER_Diario")
                                                break;

                                            case TIPO_RELAT_PERIODO.Semanal:
                                                @Html.Partial("_UFER_Semanal")
                                                break;

                                            case TIPO_RELAT_PERIODO.Mensal:
                                                @Html.Partial("_UFER_Mensal")
                                                break;

                                            case TIPO_RELAT_PERIODO.Anual:
                                                @Html.Partial("_UFER_Anual")
                                                break;
                                        }

                                        break;

                                    case TIPO_RELAT.Eventos:
                                        @Html.Partial("_Eventos")
                                        break;

                                    case TIPO_RELAT.EventosUsuario:
                                        @Html.Partial("_EventosUsuario")
                                        break;

                                    case TIPO_RELAT.Utilidades:

                                        switch (tipo_periodo)
                                        {
                                            case TIPO_RELAT_PERIODO.Diario:
                                                @Html.Partial("_Utilidades_Diario")
                                                break;

                                            case TIPO_RELAT_PERIODO.Semanal:
                                                @Html.Partial("_Utilidades_Semanal")
                                                break;

                                            case TIPO_RELAT_PERIODO.Mensal:
                                                @Html.Partial("_Utilidades_Mensal")
                                                break;

                                            case TIPO_RELAT_PERIODO.Anual:
                                                @Html.Partial("_Utilidades_Anual")
                                                break;
                                        }

                                        break;

                                    case TIPO_RELAT.Analogica:

                                        switch (tipo_periodo)
                                        {
                                            case TIPO_RELAT_PERIODO.Diario:
                                                @Html.Partial("_EA_Diario")
                                                break;

                                            case TIPO_RELAT_PERIODO.Semanal:
                                                @Html.Partial("_EA_Semanal")
                                                break;

                                            case TIPO_RELAT_PERIODO.Mensal:
                                                @Html.Partial("_EA_Mensal")
                                                break;

                                            case TIPO_RELAT_PERIODO.Anual:
                                                @Html.Partial("_EA_Anual")
                                                break;
                                        }

                                        break;

                                    case TIPO_RELAT.Ciclometro:

                                        switch (tipo_periodo)
                                        {
                                            case TIPO_RELAT_PERIODO.Diario:
                                                @Html.Partial("_Ciclometro_Diario")
                                                break;

                                            case TIPO_RELAT_PERIODO.Semanal:
                                                @Html.Partial("_Ciclometro_Semanal")
                                                break;

                                            case TIPO_RELAT_PERIODO.Mensal:
                                                @Html.Partial("_Ciclometro_Mensal")
                                                break;

                                            case TIPO_RELAT_PERIODO.Anual:
                                                @Html.Partial("_Ciclometro_Anual")
                                                break;
                                        }

                                        break;

                                    case TIPO_RELAT.Meteorologia:

                                        switch (tipo_periodo)
                                        {
                                            case TIPO_RELAT_PERIODO.Diario:
                                                @Html.Partial("_Meteorologia_Diario")
                                                break;

                                            case TIPO_RELAT_PERIODO.Semanal:
                                                @Html.Partial("_Meteorologia_Semanal")
                                                break;

                                            case TIPO_RELAT_PERIODO.Mensal:
                                                @Html.Partial("_Meteorologia_Mensal")
                                                break;


                                            case TIPO_RELAT_PERIODO.Anual:
                                                @Html.Partial("_Meteorologia_Anual")
                                                break;
                                        }

                                        break;

                                    case TIPO_RELAT.ConsolidadoEnergia_Diario:

                                        @Html.Partial("_Consolidado_DemCons_Diario")
                                        break;

                                    case TIPO_RELAT.ConsolidadoEnergia_Mensal:

                                        @Html.Partial("_Consolidado_DemCons_Mensal")
                                        break;

                                    case TIPO_RELAT.ConsolidadoEnergia_Anual:

                                        @Html.Partial("_Consolidado_DemCons_Anual")
                                        break;

                                    case TIPO_RELAT.ConsolidadoEA_Diario:

                                        @Html.Partial("_Consolidado_EA_Diario")
                                        break;

                                    case TIPO_RELAT.ConsolidadoEA_Mensal:

                                        @Html.Partial("_Consolidado_EA_Mensal")
                                        break;

                                    case TIPO_RELAT.ConsolidadoEA_Anual:

                                        @Html.Partial("_Consolidado_EA_Anual")
                                        break;

                                    case TIPO_RELAT.ConsolidadoUtilidades_Diario:

                                        @Html.Partial("_Consolidado_Utilidades_Diario")
                                        break;

                                    case TIPO_RELAT.ConsolidadoUtilidades_Mensal:

                                        @Html.Partial("_Consolidado_Utilidades_Mensal")
                                        break;

                                    case TIPO_RELAT.ConsolidadoUtilidades_Anual:

                                        @Html.Partial("_Consolidado_Utilidades_Anual")
                                        break;

                                    case TIPO_RELAT.TempoAtuacao_EntradasDigitais_Mensal:

                                        @Html.Partial("_TempoAtuacao_EntradasDigitais_Mensal")
                                        break;

                                    case TIPO_RELAT.TempoAtuacao_SaidasDigitais_Mensal:

                                        @Html.Partial("_TempoAtuacao_SaidasDigitais_Mensal")
                                        break;

                                    case TIPO_RELAT.Provisionamento_Semanal_Tipo1_Tipo2:

                                        @Html.Partial("_Provisionamento_Semanal_Tipo1_Tipo2")
                                        break;

                                    case TIPO_RELAT.Provisionamento_Semanal_Tipo3:

                                        @Html.Partial("_Provisionamento_Semanal_Tipo3")
                                        break;
                                }
                            }
                        }

                    </div>
                </div>

<script type="text/javascript">

    $(document).ready(function () {

        // tipo relatorio
        var tipo_relat = @Html.Raw(Json.Encode(@ViewBag.Relat_Tipo));

        // tipo periodo
        var tipo_periodo = @Html.Raw(Json.Encode(@ViewBag.Relat_TipoPeriodo));

        // verifica se eventos
        if(tipo_relat == 10 || tipo_relat == 11)
        {
            $('#startDate .dateini').datetimepicker({
                locale: 'pt-BR',
                format: 'DD/MM/YYYY',
                showTodayButton: true,
                allowInputToggle: true
            });

            $('#startTime').datetimepicker({
                locale: 'pt-BR',
                format: 'HH:mm:ss',
                allowInputToggle: true
            });

            $('#endDate .datefim').datetimepicker({
                locale: 'pt-BR',
                format: 'DD/MM/YYYY',
                showTodayButton: true,
                allowInputToggle: true
            });

            $('#endTime').datetimepicker({
                locale: 'pt-BR',
                format: 'HH:mm:ss',
                allowInputToggle: true
            });
        }
        else
        {
            // verifica se consolidado anual
            if(tipo_relat == 50 ||  tipo_relat == 55 || tipo_relat == 58)
            {
                $('#data_relat').datetimepicker({
                    locale: 'pt-BR',
                    format: 'YYYY',
                    viewMode: "years",
                    showTodayButton: true,
                    allowInputToggle: true
                });
            }

            // verifica se consolidado mensal de energia
            else if(tipo_relat == 51)
            {
                $('#data_relat_consolidado').datetimepicker({
                    locale: 'pt-BR',
                    format: 'MM/YYYY',
                    viewMode: "months",
                    showTodayButton: true,
                    allowInputToggle: true
                });

                $('#startTime .timeIni').datetimepicker({
                    locale: 'pt-BR',
                    format: 'HH:mm',
                    allowInputToggle: true
                });

                $('#endTime .timeFim').datetimepicker({
                    locale: 'pt-BR',
                    format: 'HH:mm',
                    allowInputToggle: true
                });
            }

            // verifica se consolidado mensal analogicas / utilidades
            else if (tipo_relat == 54 || tipo_relat == 57 || tipo_relat == 62 || tipo_relat == 63)
            {
                $('#data_relat').datetimepicker({
                    locale: 'pt-BR',
                    format: 'MM/YYYY',
                    viewMode: "months",
                    showTodayButton: true,
                    allowInputToggle: true
                });
            }

            // verifica se consolidado diario
            else if(tipo_relat == 52 || tipo_relat == 53 || tipo_relat == 56)
            {
                $('#data_relat').datetimepicker({
                    locale: 'pt-BR',
                    format: 'DD/MM/YYYY',
                    showTodayButton: true,
                    allowInputToggle: true
                });
            }

            // verifica se provisionamento semanal
            else if(tipo_relat == 60 || tipo_relat == 61)
            {
                $('#data_relat').datetimepicker({
                    locale: 'pt-BR',
                    format: 'DD/MM/YYYY',
                    showTodayButton: true,
                    allowInputToggle: true
                });
            }
            else
            {
                $('#startDateExportar .dateiniExportar').datetimepicker({
                    locale: 'pt-BR',
                    format: 'DD/MM/YYYY',
                    showTodayButton: true,
                    allowInputToggle: true
                });

                $('#startTimeExportar').datetimepicker({
                    locale: 'pt-BR',
                    format: 'HH:mm:ss',
                    allowInputToggle: true
                });

                $('#endDateExportar .datefimExportar').datetimepicker({
                    locale: 'pt-BR',
                    format: 'DD/MM/YYYY',
                    showTodayButton: true,
                    allowInputToggle: true
                });

                $('#endTimeExportar').datetimepicker({
                    locale: 'pt-BR',
                    format: 'HH:mm:ss',
                    allowInputToggle: true
                });

                // seta tipo periodo selecionado
                document.getElementById("Relat_TipoPeriodo").selectedIndex = tipo_periodo;

                // acerta calendario conforme o periodo
                switch(tipo_periodo)
                {
                    case 0:
                    case 1:

                        $('#data_relat').datetimepicker({
                            locale: 'pt-BR',
                            format: 'DD/MM/YYYY',
                            showTodayButton: true,
                            allowInputToggle: true
                        });

                        break;

                    case 2:

                        $('#data_relat').datetimepicker({
                            locale: 'pt-BR',
                            format: 'MM/YYYY',
                            viewMode: "months",
                            showTodayButton: true,
                            allowInputToggle: true
                        });

                        break;

                    case 3:

                        $('#data_relat').datetimepicker({
                            locale: 'pt-BR',
                            format: 'YYYY',
                            viewMode: "years",
                            showTodayButton: true,
                            allowInputToggle: true
                        });

                        break;
                }
            }
        }

        //
        // VALIDACAO DOS CAMPOS
        //

        jQuery.validator.addMethod("alphanumeric", function (value, element) {
            return this.optional(element) || /^[\'\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\+\-\_\(\)," "]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

        $("#formEMAIL").validate({
            rules: {
                Assunto: {
                    required: true,
                    alphanumeric: true,
                    minlength: 5,
                    maxlength: 100
                },
                Email: {
                    required: true
                }
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            }
        });

        $("#formEMAIL").submit(function(e) {

            e.preventDefault();
            var $form = $(this);

            // verifica se entradas sao validas
            if(! $form.valid()) return false;

            // opcoes EMAIL
            var EmailUsuario = @Html.Raw(Json.Encode(@ViewBag._EmailUsuario));
            var NomeRelat = @Html.Raw(Json.Encode(@ViewBag.NomeRelat));
            var PeriodoRelat = @Html.Raw(Json.Encode(@ViewBag.PeriodoRelat));

            // opcoes EMAIL
            var destino = $('input[name=Email]').val();
            var assunto = $('input[name=Assunto]').val();

            var showGrafico = $("#showGraficoEmail").is(":checked");
            var showTabela = $("#showTabelaEmail").is(":checked");

            // fecha janela email
            $('#modalEMAIL').modal('hide');

            // mostra tela wait
            waitingDialog.show(EMAIL_ENVIANDO);

            $.ajax(
            {
                type: 'GET',
                url: '/Relatorios/Relat_Grafico_EMAIL',
                data: { 'destino': destino, 'assunto': assunto, 'showGrafico': showGrafico, 'showTabela': showTabela },
                contentType: "application/json;charset=utf-8",
                dataType: "json",
                cache: false,
                async: true,
                success: function (data) {

                    // esconde tela wait
                    waitingDialog.hide();

                    swal({
                        title: EMAIL_ENVIADO,
                        text: EMAIL_SUCESSO,
                        type: "success",
                        confirmButtonColor: "#18a689",
                        confirmButtonText: FECHAR,
                    }, function () {
                    });

                },
                error: function(response) {

                    // esconde tela wait
                    waitingDialog.hide();

                    swal({
                        title: ERRO,
                        text: EMAIL_ERRO,
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: FECHAR,
                    }, function () {
                    });
                }
            });
        });

        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });

    });

    function Atualiza(navega) {

        // pego ID
        var id = $('.relatorio').prop('id');

        // pega tipo
        var tipo_relat = id.toString().substr(2, 3);

        // data nao utilizada
        var data = "01/01/2000";

        // aguarde
        $('.overlay_aguarde').toggle();

        $.ajax(
        {
            type: 'GET',
            url: '/Relatorios/_Relat_Atualizar',
            dataType: 'html',
            data: { 'TipoRelat': tipo_relat, 'Navegacao': navega, 'Data': data },
            cache: false,
            async: true,
            success: function (data) {

                // fim
                $('.overlay_aguarde').toggle();

                // apresenta
                $('#' + id).html(data);
            },
            error: function (xhr, status, error) {

                // fim
                $('.overlay_aguarde').toggle();

            }
        });
    }

    $('#data_relat').datetimepicker().on('dp.change', function (ev) {

        // pego ID
        var id = $('.relatorio').prop('id');

        // pega tipo
        var tipo_relat = id.toString().substr(2, 3);

        // tipo periodo
        var tipo_periodo = @Html.Raw(Json.Encode(@ViewBag.Relat_TipoPeriodo));

        // data
        var data_relat = document.querySelector('[name="data_relat"]').value;

        switch(tipo_periodo)
        {
            case 0:
            case 1:
                // formata data
                data = data_relat;
                break;

            case 2:
                // formata data
                data = "01/" + data_relat;
                break;

            case 3:
                // formata data
                data = "01/01/" + data_relat;
                break;
        }

        // aguarde
        $('.overlay_aguarde').toggle();

        $.ajax(
        {
            type: 'GET',
            url: '/Relatorios/_Relat_Atualizar',
            dataType: 'html',
            data: { 'TipoRelat': tipo_relat, 'Navegacao': 100, 'Data': data },
            cache: false,
            async: true,
            success: function (data) {

                // fim
                $('.overlay_aguarde').toggle();

                // apresenta
                $('#' + id).html(data);
            },
            error: function (xhr, status, error) {

                // fim
                $('.overlay_aguarde').toggle();

            }
        });

    });

    $('#data_relat_consolidado').datetimepicker().on('dp.change', function (ev) {

        ExecutarConsolidadoMensal();

    });

    function ExecutarEventos() {

        // pego ID
        var id = $('.relatorio').prop('id');

        // pega tipo
        var tipo_relat = id.toString().substr(2, 3);

        // data
        var data_ini = document.querySelector('[name="data_ini"]').value + " " + document.querySelector('[name="hora_ini"]').value;
        var data_fim = document.querySelector('[name="data_fim"]').value + " " + document.querySelector('[name="hora_fim"]').value;

        // aguarde
        $('.overlay_aguarde').toggle();

        $.ajax(
        {
            type: 'GET',
            url: '/Relatorios/_Relat_Atualizar',
            dataType: 'html',
            data: { 'TipoRelat': tipo_relat, 'Navegacao': 100, 'Data': data_ini, 'DataFim': data_fim },
            cache: false,
            async: true,
            success: function (data) {

                // fim
                $('.overlay_aguarde').toggle();

                // apresenta
                $('#' + id).html(data);
            },
            error: function (xhr, status, error) {

                // fim
                $('.overlay_aguarde').toggle();

            }
        });
    }

    function ExecutarEventosUsuario() {

        // pego ID
        var id = $('.relatorio').prop('id');

        // pega tipo
        var tipo_relat = id.toString().substr(2, 3);

        // data
        var data_ini = document.querySelector('[name="data_ini"]').value + " " + document.querySelector('[name="hora_ini"]').value;
        var data_fim = document.querySelector('[name="data_fim"]').value + " " + document.querySelector('[name="hora_fim"]').value;

        // aguarde
        $('.overlay_aguarde').toggle();

        $.ajax(
        {
            type: 'GET',
            url: '/Relatorios/_Relat_Atualizar',
            dataType: 'html',
            data: { 'TipoRelat': tipo_relat, 'Navegacao': 100, 'Data': data_ini, 'DataFim': data_fim },
            cache: false,
            async: true,
            success: function (data) {

                // fim
                $('.overlay_aguarde').toggle();

                // apresenta
                $('#' + id).html(data);
            },
            error: function (xhr, status, error) {

                // fim
                $('.overlay_aguarde').toggle();

            }
        });
    }

    function ExecutarConsolidadoMensal() {

        // pego ID
        var id = $('.relatorio').prop('id');

        // pega tipo
        var tipo_relat = id.toString().substr(2, 3);

        // data
        var data_relat_consolidado = document.querySelector('[name="data_relat_consolidado"]').value;
        var data_fim = data_relat_consolidado;

        // turno
        var turno_ini = data_relat_consolidado + "/01 " + document.querySelector('[name="hora_ini"]').value + ":00";
        var turno_fim = data_relat_consolidado + "/01 " + document.querySelector('[name="hora_fim"]').value + ":00";

        // aguarde
        $('.overlay_aguarde').toggle();

        $.ajax(
        {
            type: 'GET',
            url: '/Relatorios/_Relat_Atualizar',
            dataType: 'html',
            data: { 'TipoRelat': tipo_relat, 'Navegacao': 100, 'Data': data_relat_consolidado, 'DataFim': data_fim, 'TurnoIni': turno_ini, 'TurnoFim': turno_fim },
            cache: false,
            async: true,
            success: function (data) {

                // fim
                $('.overlay_aguarde').toggle();

                // apresenta
                $('#' + id).html(data);
            },
            error: function (xhr, status, error) {

                // fim
                $('.overlay_aguarde').toggle();

            }
        });
    }

    $(".link_dia_menos").off().on("click", function (event) {
        event.stopPropagation();

        // dia anterior
        Atualiza(-1);
    });

    $(".link_dia_mais").off().on("click", function (event) {
        event.stopPropagation();

        // dia seguinte
        Atualiza(1);
    });

    $(".link_semana_menos").off().on("click", function (event) {
        event.stopPropagation();

        // semana anterior
        Atualiza(-2);
    });

    $(".link_semana_mais").off().on("click", function (event) {
        event.stopPropagation();

        // semana seguinte
        Atualiza(2);
    });

    $(".link_mes_menos").off().on("click", function (event) {
        event.stopPropagation();

        // mes anterior
        Atualiza(-3);
    });

    $(".link_mes_mais").off().on("click", function (event) {
        event.stopPropagation();

        // mes seguinte
        Atualiza(3);
    });

    $(".link_ano_menos").off().on("click", function (event) {
        event.stopPropagation();

        // ano anterior
        Atualiza(-4);
    });

    $(".link_ano_mais").off().on("click", function (event) {
        event.stopPropagation();

        // ano seguinte
        Atualiza(4);
    });

    $(".link_ultimo").off().on("click", function (event) {
        event.stopPropagation();

        // ultimo
        Atualiza(10);
    });


    function getCookie(c_name) {

        // Get name followed by anything except a semicolon
        var cookiestring = RegExp("" + c_name + "[^;]+").exec(document.cookie);

        // Return everything after the equal sign, or an empty string if the cookie name not found
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./, "") : "");
    }

    function setCookie(c_name, value, expiresecs) {
        var exdate = new Date();
        exdate.setTime(exdate.getTime() + ((expiresecs) ? expiresecs * 1000 : 0));

        document.cookie = c_name + "=" + escape(value) + ((expiresecs == null) ? "" : ";expires=" + exdate.toGMTString()) + ";path=/";
    }

    function SelecionouTipoPeriodo() {

        // pega tipo selecionado
        var tipo_periodo = document.getElementById("Relat_TipoPeriodo").selectedIndex;

        // salva em cookie
        setCookie("Relat_TipoPeriodo", tipo_periodo, null);

        // atualiza na mesma data
        Atualiza(0);
    }

    function gerarPDF() {

        // opcoes PDF
        var showGrafico = $("#showGrafico").is(":checked");
        var showTabela = $("#showTabela").is(":checked");

        // mostra tela wait
        waitingDialog.show(PDF_GERANDO);

        $.ajax(
        {
            type: 'GET',
            url: '/Relatorios/Relat_Grafico_PDF',
            data: { 'showGrafico': showGrafico, 'showTabela': showTabela },
            contentType: "application/json;charset=utf-8",
            dataType: "json",
            cache: false,
            async: true,
            success: function (data) {

                // esconde tela wait
                waitingDialog.hide();

                // monta caminho
                var diretorio = window.location.origin + '/Temp/' + data.nomeArquivo;

                // apresenta PDF
                window.open(diretorio, '_blank');

            },
            error: function (response) {

                // esconde tela wait
                waitingDialog.hide();

                swal({
                    title: ERRO,
                    text: PDF_ERRO,
                    type: "warning",
                    confirmButtonColor: "#f8ac59",
                    confirmButtonText: FECHAR,
                }, function () {
                });
            }
        });
    }

    function gerarXLS() {

        $.ajax(
        {
            type: 'GET',
            url: '/Relatorios/Relat_Grafico_XLS',
            data: {},
            contentType: "application/json;charset=utf-8",
            dataType: "json",
            cache: false,
            async: true,
            success: function (data) {

                // inicia download
                window.location = '/Relatorios/Relat_Grafico_XLS_Download?fileGuid=' + data.FileGuid
                                  + '&filename=' + data.FileName;

            },
            error: function(xhr, status, error) {

                swal({
                    title: ERRO,
                    text: XLS_ERRO,
                    type: "warning",
                    confirmButtonColor: "#f8ac59",
                    confirmButtonText: FECHAR,
                }, function () {
                });

                alert(xhr.responseText);
            }
        });
    }

    function exportarXLS() {

        // data
        var data_ini = document.querySelector('[name="data_ini_exportar"]').value + " " + document.querySelector('[name="hora_ini_exportar"]').value;
        var data_fim = document.querySelector('[name="data_fim_exportar"]').value + " " + document.querySelector('[name="hora_fim_exportar"]').value;

        $.ajax(
        {
            type: 'GET',
            url: '/Relatorios/Relat_Grafico_exportarXLS',
            data: {'DataIni': data_ini, 'DataFim': data_fim},
            contentType: "application/json;charset=utf-8",
            dataType: "json",
            cache: false,
            async: true,
            success: function (data) {

                if( data == "MaxReg")
                {
                    swal({
                        title: ERRO,
                        text: XLS_DIAS_EXCEDIDO,
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: FECHAR,
                    }, function () {
                    });
                }
                else
                {
                    // inicia download
                    window.location = '/Relatorios/Relat_Grafico_XLS_Download?fileGuid=' + data.FileGuid
                                      + '&filename=' + data.FileName;
                }
            },
            error: function(xhr, status, error) {

                swal({
                    title: ERRO,
                    text: XLS_ERRO,
                    type: "warning",
                    confirmButtonColor: "#f8ac59",
                    confirmButtonText: FECHAR,
                }, function () {
                });

                alert(xhr.responseText);

            }
        });
    }


</script>


