﻿<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.FatorCarga</title>

    <link href="@Server.MapPath("~/Content/bootstrap.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/style.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/plugins/c3/c3.min.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/Relatorios.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/RelatoriosPDF.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/fonts/font-awesome/css/font-awesome.min.css")" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="@Server.MapPath("~/Scripts/jquery-2.1.1.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/d3/d3.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/c3/c3.min.js")"></script>

</head>

@{
    string arquivo = "~/Logos/" + ViewBag._LogoConsultor;
    string logo = Server.MapPath(arquivo);
}

@if (ViewBag.Retorno == 2)
{
    <div class="row">
        <div class="col-lg-12">
            <div class="linha cabecalho">
                <div class="coluna1">
                    <p><img src="@logo" style="height:42px; width:auto;" /></p>
                </div>

                <div class="coluna2">
                    <h3>@ViewBag.ClienteNome</h3>
                    @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                </div>

                <div class="coluna3">
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.FatorCarga - @SmartEnergy.Resources.RelatoriosTexts.RelatDiario</h4>
                    <p>@ViewBag.DataAtual</p>
                </div>
            </div>
        </div>
    </div>

    <br /><br /><br /><br /><br /><br />
    <div class="row">
        <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
            <i class="fa fa-warning fa-5x"></i>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>@SmartEnergy.Resources.RelatoriosTexts.NaoExistemRegistros</h2>
        </div>
    </div>
    <br /><br /><br /><br />

}
else
{

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="@logo" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.FatorCarga - @SmartEnergy.Resources.RelatoriosTexts.RelatDiario</h4>
                        <p>@ViewBag.DataAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <br />

        @{
            if (ViewBag.showGrafico)
            {
                <br /><br />
                <div class="row">
                    <div class="relat-grafico" style="height:490px; width:800px; margin-left:50px;">
                        <div id="grafico-fatcarga" style="margin-top: -10px"></div>
                        <div class='chart-legend' style="display:none;">
                            <div class='legend-scale-dem'>
                                <ul class='legend-labels' style="margin-top: -25px;">
                                    <li><span style='background:#1C84C6 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                                    <li><span style='background:#007f00 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                                    <li><span style='background:#ff0000 !important;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }

        <hr />

        <div class="row" style="margin-top: -15px;">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatCargaDia<br />&nbsp;</td>
                                <td class="relat-capacitivo">@ViewBag.FatCargaFPC %</td>
                                <td class="relat-fponta">@ViewBag.FatCargaFPI %</td>
                                <td class="relat-ponta">@ViewBag.FatCargaP %</td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMedia<br />&nbsp;</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MedFPC kW</td>
                                <td class="relat-fponta">@ViewBag.Dem_MedFPI kW</td>
                                <td class="relat-ponta">@ViewBag.Dem_MedP kW</td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMaxima</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MaxFPC kW<br /><small>(@ViewBag.Dem_MaxFPC_DataHora)</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MaxFPI kW<br /><small>(@ViewBag.Dem_MaxFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MaxP kW<br /><small>(@ViewBag.Dem_MaxP_DataHora)</small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>

<script type="text/javascript">

    $(document).ready(function () {

        // periodos
        var Periodos = [];

        Periodos.push('x');
        Periodos.push('Fora de Ponta (Capacitivo)');
        Periodos.push('Fora de Ponta (Indutivo)');
        Periodos.push('Ponta');

        // fator de carga
        var dataFatCarga = [];

        // copia valores
        var FatCarga = @Html.Raw(Json.Encode(@ViewBag.FatCarga));

        var FatCargaMaxGrafico = Math.ceil(@ViewBag.FatCargaMaxGrafico);

        // verifico se nao tem dados e forco um limite
        if( FatCargaMaxGrafico == 0.0)
        {
            FatCargaMaxGrafico = 100.0;
        }

        dataFatCarga.push(['data1']);
        dataFatCarga.push([FatCarga[2]]);
        dataFatCarga.push([FatCarga[1]]);
        dataFatCarga.push([FatCarga[0]]);

        c3.generate({

            bindto: '#grafico-fatcarga',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                columns: [Periodos,dataFatCarga],
                types: {
                    data1: 'bar',
                },
                color: function (color, d) {
                    if (typeof d.index === 'undefined') { return color; }

                    if( d.index == 2 )
                        return '#FF0000';

                    if( d.index == 1 )
                        return '#007F00';

                    if( d.index == 0 )
                        return '#1C84C6';

                    return '#007F00';
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type : 'categories',
                    tick: {
                        outer: false,
                        fit: true
                    }
                },
                y:  {
                    max: FatCargaMaxGrafico,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {
                            return d.toFixed(1);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.5 },
            }
        });

        // labels
        $('.chart-legend').css("display", "block");

    });

</script>

}
    