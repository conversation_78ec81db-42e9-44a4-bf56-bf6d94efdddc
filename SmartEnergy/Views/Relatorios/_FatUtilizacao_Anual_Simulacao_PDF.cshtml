﻿<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao</title>

    <link href="@Server.MapPath("~/Content/bootstrap.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/style.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/plugins/c3/c3.min.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/Relatorios.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/RelatoriosPDF.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/fonts/font-awesome/css/font-awesome.min.css")" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="@Server.MapPath("~/Scripts/jquery-2.1.1.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/d3/d3.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/c3/c3.min.js")"></script>

</head>

@{
    string arquivo = "~/Logos/" + ViewBag._LogoConsultor;
    string logo = Server.MapPath(arquivo);
}

@if (ViewBag.Retorno == 2)
{
    <div class="row">
        <div class="col-lg-12">
            <div class="linha cabecalho">
                <div class="coluna1">
                    <p><img src="@logo" style="height:42px; width:auto;" /></p>
                </div>

                <div class="coluna2">
                    <h3>@ViewBag.ClienteNome</h3>
                    @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                </div>

                <div class="coluna3">
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao - @SmartEnergy.Resources.RelatoriosTexts.RelatAnual (@SmartEnergy.Resources.RelatoriosTexts.Simulacao)</h4>
                    <p>@ViewBag.DataTextoAtual</p>
                </div>
            </div>
        </div>
    </div>

    <br /><br /><br /><br /><br /><br />
    <div class="row">
        <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
            <i class="fa fa-warning fa-5x"></i>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>@SmartEnergy.Resources.RelatoriosTexts.NaoExistemRegistros</h2>
        </div>
    </div>
    <br /><br /><br /><br />

}
else
{

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="@logo" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao - @SmartEnergy.Resources.RelatoriosTexts.RelatAnual (@SmartEnergy.Resources.RelatoriosTexts.Simulacao)</h4>
                        <p>@ViewBag.DataTextoAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <br />

        @{
            if (ViewBag.showGrafico)
            {
                <br /><br />
                <div class="row">
                    <div class="relat-grafico" style="height:490px; width:800px; margin-left:50px;">
                        <div id="fatutilizacao-chart-simulacao"></div>
                        <div class='chart-legend' style="display:none;">
                            <div class='legend-scale-dem'>
                                <ul class='legend-labels' style="margin-top: -25px;">
                                    <li><span style='background:#1C84C6 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                                    <li><span style='background:#007f00 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                                    <li><span style='background:#ff0000 !important;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }

        <hr />

        <div class="row" style="margin-top: -15px;">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao @SmartEnergy.Resources.RelatoriosTexts.Maximo</td>
                                <td class="relat-capacitivo">@ViewBag.FatUtilizacao_MaxFPC_Sim %<br /><small>(@ViewBag.FatUtilizacao_MaxFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.FatUtilizacao_MaxFPI_Sim %<br /><small>(@ViewBag.FatUtilizacao_MaxFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.FatUtilizacao_MaxP_Sim %<br /><small>(@ViewBag.FatUtilizacao_MaxP_DataHora_Sim)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao @SmartEnergy.Resources.RelatoriosTexts.Minimo</td>
                                <td class="relat-capacitivo">@ViewBag.FatUtilizacao_MinFPC_Sim %<br /><small>(@ViewBag.FatUtilizacao_MinFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.FatUtilizacao_MinFPI_Sim %<br /><small>(@ViewBag.FatUtilizacao_MinFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.FatUtilizacao_MinP_Sim %<br /><small>(@ViewBag.FatUtilizacao_MinP_DataHora_Sim)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao @SmartEnergy.Resources.RelatoriosTexts.Periodo</td>
                                <td class="relat-capacitivo">@ViewBag.FatUtilizacao_MedFPC_Sim %<br /><small>&nbsp;</small></td>
                                <td class="relat-fponta">@ViewBag.FatUtilizacao_MedFPI_Sim %<br /><small>&nbsp;</small></td>
                                <td class="relat-ponta">@ViewBag.FatUtilizacao_MedP_Sim %</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    @{
        if (ViewBag.showTabela)
        {
            <div class="page-break"></div>

            <div class="row">
                <br /><br />
                <div class="col-lg-12">
                    <div class="panel panel-default">
                        <table class="table no-margins tabela-grafico-pdf">
                            <thead>
                                <tr>
                                    <th style="width:25%; text-align:center; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Dia</th>
                                    <th style="width:25%; text-align:center; font-size:12px;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo<br />(%)</th>
                                    <th style="width:25%; text-align:center; font-size:12px;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo<br />(%)</th>
                                    <th style="width:25%; text-align:center; font-size:12px;">@SmartEnergy.Resources.SupervisaoTexts.Ponta<br />(%)</th>
                                </tr>
                            </thead>

                            <tbody>

                                @{
                                    var i = 0;
                                    var j = 0;
                                    var FatUtilizacaoFPC_sim = "---";
                                    var FatUtilizacaoFPI_sim = "---";
                                    var FatUtilizacaoP_sim = "---";

                                    for (i = 0; i < 12; i++)
                                    {
                                        j = i + 1;

                                        FatUtilizacaoFPC_sim = string.Format("{0:#,##0.0}", ViewBag.FatUtilizacaoFPC_Sim[j]);
                                        FatUtilizacaoFPI_sim = string.Format("{0:#,##0.0}", ViewBag.FatUtilizacaoFPI_Sim[j]);
                                        FatUtilizacaoP_sim = string.Format("{0:#,##0.0}", ViewBag.FatUtilizacaoP_Sim[j]);

                                        <tr class="tabela_valores" style="font-size:12px;">
                                            <td style="text-align:center; font-size:12px;" class="relat-preto">@ViewBag.Meses[j]</td>
                                            <td style="text-align:center; font-size:12px;" class="relat-capacitivo">@FatUtilizacaoFPC_sim</td>
                                            <td style="text-align:center; font-size:12px;" class="relat-fponta">@FatUtilizacaoFPI_sim</td>
                                            <td style="text-align:center; font-size:12px;" class="relat-ponta">@FatUtilizacaoP_sim</td>
                                        </tr>
                                    }
                                }

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }
    }

    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>

    <script type="text/javascript">

        $(document).ready(function () {

            //
            // GRÁFICO SIMULAÇÃO
            //

            // fator de utilizacao simulacao
            var dataX_Sim = [];
            var labelX_Sim = [];
            var dataFatUtilizacaoP_Sim = [];
            var dataFatUtilizacaoFPI_Sim = [];
            var dataFatUtilizacaoFPC_Sim = [];

            // copia valores
            var FatUtilizacaoP_Sim = @Html.Raw(Json.Encode(@ViewBag.FatUtilizacaoP_Sim));
            var FatUtilizacaoFPI_Sim = @Html.Raw(Json.Encode(@ViewBag.FatUtilizacaoFPI_Sim));
            var FatUtilizacaoFPC_Sim = @Html.Raw(Json.Encode(@ViewBag.FatUtilizacaoFPC_Sim));
            var Datas_Sim = @Html.Raw(Json.Encode(@ViewBag.Datas_Sim));
            var Meses = @Html.Raw(Json.Encode(@ViewBag.Meses));

            var maximoFatUtilizacao = @Html.Raw(Json.Encode(@ViewBag.FatUtilizacaoMaxGrafico));

            // verifico se nao tem dados e forco um limite
            if( maximoFatUtilizacao == 0.0)
            {
                maximoFatUtilizacao = 100.0;
            }

            dataX_Sim.push('x');
            dataFatUtilizacaoFPC_Sim.push(['data1']);
            dataFatUtilizacaoFPI_Sim.push(['data2']);
            dataFatUtilizacaoP_Sim.push(['data3']);

            for (i = 0; i < 14; i++)
            {
                // X
                dataX_Sim.push(Datas_Sim[i]);

                dataFatUtilizacaoFPC_Sim.push([FatUtilizacaoFPC_Sim[i]]);
                dataFatUtilizacaoFPI_Sim.push([FatUtilizacaoFPI_Sim[i]]);
                dataFatUtilizacaoP_Sim.push([FatUtilizacaoP_Sim[i]]);
            }

            // valores label X
            for (i = 1; i <= 12; i++)
            {
                labelX_Sim.push(Datas_Sim[i]);
            }

            var Data = [dataX_Sim,dataFatUtilizacaoFPC_Sim,dataFatUtilizacaoFPI_Sim,dataFatUtilizacaoP_Sim];

            c3.generate({

                bindto: '#fatutilizacao-chart-simulacao',
                size: {
                    height: 420
                },
                padding: {
                    top: 8,
                    right: 10,
                    bottom: 30,
                    left: 43
                },
                data: {
                    x: 'x',
                    xFormat : '%Y-%m-%d %H:%M:%S',
                    columns: Data,
                    types: {
                        data1: 'bar',
                        data2: 'bar',
                        data3: 'bar',
                    },
                    colors: {
                        data1: '#1C84C6',
                        data2: '#007F00',
                        data3: '#FF0000',
                    },
                },
                point: {
                    show: false,
                    sensitivity: 1000
                },
                legend: {
                    show: false
                },
                axis: {
                    x:  {
                        type: 'timeseries',
                        tick: {
                            outer: false,
                            values: labelX_Sim,
                            format: function (x) {

                                // mes
                                var mes = x.getMonth()+1;

                                // verifica se deve mostrar label
                                if(mes==1 || mes==4 || mes==7 || mes==10 || mes==12 )
                                    return Meses[mes];

                                return "";
                            }
                        },
                        // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                        // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                        // Grafico 1 dia     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(11 horas)
                        // Grafico 1 mes     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(24 horas)*(13 dias)
                        padding: -1000*60*60*24*13
                    },
                    y:  {
                        max: maximoFatUtilizacao,
                        padding: {
                            top: 0,
                            bottom: 0
                        },
                        tick: {
                            outer: false,
                            format: function (d) {

                                if( maximoFatUtilizacao < 10)
                                {
                                    return d.toFixed(1);
                                }

                                return parseInt(d);
                            }
                        }
                    }
                },
                grid: {
                    x: {
                        show: false
                    }
                },
                bar: {
                    width: { ratio: 0.7 },
                },
            });

            // labels
            $('.chart-legend').css("display", "block");

        });

    </script>

}
    