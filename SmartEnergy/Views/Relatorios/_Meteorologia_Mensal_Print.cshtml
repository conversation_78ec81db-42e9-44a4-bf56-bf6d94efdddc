﻿<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.Meteorologia</title>

    <link href="~/Content/bootstrap.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/style.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/plugins/c3/c3.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/Relatorios.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/RelatoriosPDF.css" rel="stylesheet" type="text/css" />
    <link href="~/fonts/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="~/Scripts/jquery-2.1.1.min.js" charset="utf-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/d3/d3.min.js" charset="utf-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/c3/c3.min.js"></script>

</head>

<style>
    th {
        font-size: 12px;
    }

    td {
        font-size: 12px;
    }

    tr {
        padding: 1px;
        page-break-inside: avoid;
    }
</style>

@if (ViewBag.Retorno == 2)
{
    <div class="row">
        <div class="col-lg-12">
            <div class="linha cabecalho">
                <div class="coluna1">
                    <p><img src="~/Logos/@ViewBag._LogoConsultor" style="height:42px; width:auto;" /></p>
                </div>

                <div class="coluna2">
                    <h3>@ViewBag.ClienteNome</h3>
                    @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                </div>

                <div class="coluna3">
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.Meteorologia - @SmartEnergy.Resources.RelatoriosTexts.RelatMensal</h4>
                    <p>@ViewBag.DataTextoAtual</p>
                </div>
            </div>
        </div>
    </div>

    <br /><br /><br /><br /><br /><br />
    <div class="row">
        <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
            <i class="fa fa-warning fa-5x"></i>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>@SmartEnergy.Resources.RelatoriosTexts.NaoExistemRegistros</h2>
        </div>
    </div>
    <br /><br /><br /><br />

}
else
{

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="~/Logos/@ViewBag._LogoConsultor" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.Meteorologia - @SmartEnergy.Resources.RelatoriosTexts.RelatMensal</h4>
                        <p>@ViewBag.DataTextoAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <br />

        @{
            if (ViewBag.showGrafico)
            {
                <div class="row">
                    <div class="relat-grafico" style="height:450px; width:800px; margin-left:50px;">
                        <div id="grafico-ea" style="margin-top: -10px"></div>
                        <div class='chart-legend' style="display:none;">
                            <div class='legend-scale-dem'>
                                <ul class='legend-labels' style="margin-top: -25px;">
                                    <li><span style='background:#1C84C6 !important;'></span>@ViewBag.NomeGrandeza</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }

        <hr />

        <div class="row">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-ea" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.MediaMes</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Maxima</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Minima</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@ViewBag.NomeGrandeza</td>
                                <td class="relat-ea">@ViewBag.VMedMes @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.DataTextoAtual)</small></td>
                                <td class="relat-preto">@ViewBag.VMaxMes @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMaxMes_DataHora)</small></td>
                                <td class="relat-preto">@ViewBag.VMinMes @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMinMes_DataHora)</small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>


    @{
        if (ViewBag.showTabela)
        {
            <div class="page-break"></div>
            
            <div class="row">

                <div class="col-lg-12">
                    <div class="panel panel-default">
                        <table class="table no-margins tabela-grafico-pdf">
                        <thead>
                            <tr>
                                <th style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Dia</th>
                                <th style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Media<br />(@ViewBag.UnidadeGrandeza)</th>
                                <th style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Maxima<br />(@ViewBag.UnidadeGrandeza)</th>
                                <th style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Minima<br />(@ViewBag.UnidadeGrandeza)</th>
                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var i = 0;
                                var j = 0;
                                var tempMed = "---";
                                var tempMax = "---";
                                var tempMin = "---";

                                var NumDiasMes = ViewBag.NumDiasMes;

                                for (i = 0; i < NumDiasMes; i++)
                                {
                                    j = i + 1;

                                    if (ViewBag.PossuiRegistros[j])
                                    {
                                        tempMed = string.Format("{0:0}", ViewBag.TempMed[j]);
                                        tempMax = string.Format("{0:0}", ViewBag.TempMax[j]);
                                        tempMin = string.Format("{0:0}", ViewBag.TempMin[j]);
                                    }
                                    else
                                    {
                                        tempMed = "---";
                                        tempMax = "---";
                                        tempMin = "---";
                                    }

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="relat-ea">@ViewBag.Dias[j]</td>
                                        <td style="text-align:center;" class="relat-ea">@tempMed</td>
                                        <td style="text-align:center;" class="relat-ea">@tempMax</td>
                                        <td style="text-align:center;" class="relat-ea">@tempMin</td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                    </div>
                </div>
            </div>
        }
    }

    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>

<script type="text/javascript">

    $(document).ready(function () {

        // meteorologia
        var dataX = [];
        var labelX = [];
        var dataMaxima = [];
        var dataMedia = [];
        var dataMinima = [];

        // copia valores
        var TempMax = @Html.Raw(Json.Encode(@ViewBag.TempMax));
        var TempMed = @Html.Raw(Json.Encode(@ViewBag.TempMed));
        var TempMin = @Html.Raw(Json.Encode(@ViewBag.TempMin));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));
        var NumDiasMes = @Html.Raw(Json.Encode(@ViewBag.NumDiasMes));
        var NomeGrandeza = @Html.Raw(Json.Encode(@ViewBag.NomeGrandeza));
        var UnidadeGrandeza = @Html.Raw(Json.Encode(@ViewBag.UnidadeGrandeza));

        // valor minimo e máximo
        var minimoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMin));
        var maximoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMax));

        dataX.push('x');
        dataMaxima.push(['data1']);
        dataMedia.push(['data2']);
        dataMinima.push(['data3']);

        for (i = 0; i < NumDiasMes+2; i++)
        {
            // X
            dataX.push(Datas[i]);

            dataMaxima.push([TempMax[i]]);
            dataMedia.push([TempMed[i]]);
            dataMinima.push([TempMin[i]]);
        }

        // valores label X
        labelX.push(Datas[1]);
        for (i = 5; i <= 25; i+=5)
        {
            labelX.push(Datas[i]);
        }
        labelX.push(Datas[NumDiasMes]);

        var Data = [dataX,dataMinima,dataMedia,dataMaxima];

        c3.generate({

            bindto: '#grafico-ea',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'bar',
                    data3: 'bar',
                },
                colors: {
                    data1: '#00007f',
                    data2: '#4284B2',
                    data3: '#D7E8F8',
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // retorna dia
                            return x.getDate();
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    // Grafico 1 dia     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(11 horas)
                    padding: -1000*60*60*11
                },
                y:  {
                    min: minimoValor,
                    max: maximoValor,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {
                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.15 },
            },
        });

        // labels
        $('.chart-legend').css("display", "block");

    });

</script>

}
    