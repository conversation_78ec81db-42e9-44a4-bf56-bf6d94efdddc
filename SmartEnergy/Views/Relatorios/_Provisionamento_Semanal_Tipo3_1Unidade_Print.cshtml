﻿@using SmartEnergyLib.SQL

<head>

    <title>@SmartEnergy.Resources.MenuTexts.MenuLateralRelatoriosProvisionamentoSemanal</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

    <link href="~/fonts/font-awesome/css/font-awesome.min.css" rel="stylesheet" />
    <link href="~/fonts/elusive-icons-2.0.0/css/elusive-icons.min.css" rel="stylesheet" />
    <link href="~/Content/bootstrap.min.css" rel="stylesheet" />
    <link href="~/Content/style.css" rel="stylesheet" />
    <link href="~/Content/plugins/c3/c3.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/Relatorios.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/RelatoriosPDF.css" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="~/Scripts/jquery-2.1.1.min.js" charset="utf-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/d3/d3.min.js" charset="utf-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/c3/c3.min.js"></script>

    <style>

        #grafico-ConsumoContrato .c3-axis-y text {
            font-size: 12px;
        }

        #grafico-ConsumoContrato .c3-axis-x text {
            font-size: 15px;
        }

        .c3 svg {
            font-size: 15px;
        }

        #consumo-mes-chart .c3-axis-y text {
            font-size: 10px;
        }

        #consumo-mes-chart .c3-axis-x text {
            font-size: 10px;
        }

        #consumo-12meses-chart .c3-axis-y text {
            font-size: 10px;
        }

        #consumo-12meses-chart .c3-axis-x text {
            font-size: 10px;
        }

        #consumo-12meses-chart .c3-line {
            stroke-width: 2px;
        }


        td {
            font-size: 8px;
        }

        th {
            font-size: 8px;
        }

        tr {
            page-break-inside: avoid; 
        }

    </style>
</head>



<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho" style="height:80px !important;">
                    <div class="coluna1">
                        <p><img src="~/Logos/@ViewBag._LogoConsultor" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralRelatoriosProvisionamentoSemanal</h4>
                        <p>@ViewBag.DataTextoAtual</p>
                    </div>
                </div>
            </div>
        </div>

        @{
            Provisionamento_Tipo3 provisionamento = ViewBag.ProvisionamentoSemanal;
            List<Provisionamento_UnidadesConsumidoras> unidadesConsumidoras = ViewBag.unidadesConsumidoras;
            List<ContratosCCEEDominio> contratosEnergia = ViewBag.contratosEnergia;

            if (provisionamento != null && unidadesConsumidoras != null && contratosEnergia != null)
            {
                List<Provisionamento_Tipo1_Tipo2> provs = provisionamento.por_contrato;
                               
                string Observacao_txt = "Considerando o histórico de consumo das últimas semanas, o consumo nesse mês ficará ";
                string Resultado_txt = SmartEnergy.Resources.ContratosCCEETexts.Previsao;
                string Consumo_txt = SmartEnergy.Resources.ContratosCCEETexts.ConsumoDentro;

                string Resultado_cor = "relat-verde";
                string Classe_cor = "panel-primary";

                if (provisionamento.Resultado == 0.0)
                {
                    Observacao_txt += "DENTRO do Volume Contratado.";
                }
                if (provisionamento.Resultado > 0.0)
                {
                    Observacao_txt += "ABAIXO do Volume Contratado.";
                    Resultado_txt = SmartEnergy.Resources.ContratosCCEETexts.PrevisaoSobra;
                    Consumo_txt = SmartEnergy.Resources.ContratosCCEETexts.ConsumoMenor;
                    Resultado_cor = "relat-amarelo";
                    Classe_cor = "panel-warning";
                }
                if (provisionamento.Resultado < 0.0)
                {
                    Observacao_txt += "ACIMA do Volume Contratado.";
                    Resultado_txt = SmartEnergy.Resources.ContratosCCEETexts.PrevisaoDeficit;
                    Consumo_txt = SmartEnergy.Resources.ContratosCCEETexts.ConsumoMaior;
                    Resultado_cor = "relat-vermelho";
                    Classe_cor = "panel-danger";
                }
                
                <br />
            
                <div class="row">
                    <div class="col-xs-12">
                        <div class="panel panel-title">
                            <div class="panel-heading">
                                <h4>@SmartEnergy.Resources.ContratosCCEETexts.AnaliseContratoConsumo</h4>
                            </div>
                            <div class="panel-body">

                                <div class="row">
                                    <div class="col-xs-7">
                                        <span><b>@SmartEnergy.Resources.ContratosCCEETexts.TotalContrato</b></span>
                                    </div>
                                    <div class="col-xs-5">
                                        <span style="font-size:large;"><b>@string.Format("{0:#,##0.000} MWh", provisionamento.Total_Contrato)</b></span>
                                    </div>
                                </div>

                                <hr />

                                <div class="row">
                                    <div class="col-xs-7">
                                        <span>@SmartEnergy.Resources.ContratosCCEETexts.ConsumoConsolidado</span>
                                    </div>
                                    <div class="col-xs-5">
                                        <span>@string.Format("{0:#,##0.000} MWh", provisionamento.Consumo_Consolidado)</span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-xs-7">
                                        <span>@SmartEnergy.Resources.ContratosCCEETexts.ConsumoEstimado</span>
                                    </div>
                                    <div class="col-xs-5">
                                        <span>@string.Format("{0:#,##0.000} MWh", provisionamento.Consumo_Estimado)</span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-xs-7">
                                        <span>@SmartEnergy.Resources.ContratosCCEETexts.ConsumoTotalBruto</span>
                                    </div>
                                    <div class="col-xs-5">
                                        <span>@string.Format("{0:#,##0.000} MWh", provisionamento.Consumo_Total_Bruto)</span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-xs-7">
                                        <span>@SmartEnergy.Resources.ContratosCCEETexts.Perdas</span>
                                    </div>
                                    <div class="col-xs-5">
                                        <span>@string.Format("{0:0.0} %", provisionamento.Perdas)</span>
                                    </div>
                                </div>
                                <br />
                                <div class="row">
                                    <div class="col-xs-7">
                                        <span><b>@SmartEnergy.Resources.ContratosCCEETexts.PrevisaoConsumo</b></span>
                                    </div>
                                    <div class="col-xs-5">
                                        <span style="font-size:large;"><b>@string.Format("{0:#,##0.000} MWh", provisionamento.Previsao_Consumo)</b></span>
                                    </div>
                                </div>

                                <hr />

                                <div class="row">
                                    <div class="col-xs-7">
                                        <span class="@Resultado_cor"><b>@Resultado_txt</b></span>
                                    </div>
                                    <div class="col-xs-5">
                                        <span class="@Resultado_cor" style="font-size:large;"><b>@string.Format("{0:#,##0.000} MWh", provisionamento.Resultado)</b></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            
                <div class="row">
                    <div class="col-lg-12">
                        <div class="panel @Classe_cor">
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-lg-12">
                                        <i class="fa fa-exclamation-triangle @Resultado_cor" style="font-size:16px;"></i><span class="@Resultado_cor" style="font-size:small; padding-left:8px;"><b>@Observacao_txt</b></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            
                <div class="row">
                    <div class="col-xs-12">
                        <div class="panel panel-title">
                            <div class="panel-body">
                                <div class="row">
                                    <div class="relat-grafico" style="height:300px; width:800px; margin-left:50px;">
                                        <div id="grafico-ConsumoContrato" style="margin-top: 20px"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            
                <br />
            
                <div class="row">
                    <div class="col-xs-12">
                        <table id="dataTables-analiseContratos" class="table table-striped table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>Sequência</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.ContratoCodigo</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.Considera</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.ConsumoTotalBruto</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.PercentualCarga</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.Perdas</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.PrevisaoConsumo</th>
                                    <th>PROINFA</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.NecessidadeContratacao</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.Contrato</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.TakeMinimo</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.TakeMaximo</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.VolumeFaturado</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.TotalContrato</th>
                                </tr>
                            </thead>
                            <tbody>

                                @{
                                    foreach (ContratosCCEEDominio contrato in contratosEnergia)
                                    {
                                        // encontra provisionamento
                                        Provisionamento_Tipo1_Tipo2 prov = provisionamento.por_contrato.Find(item => item.IDContratoCCEE == contrato.IDContratoCCEE);

                                        if (prov == null)
                                        {
                                            continue;
                                        }

                                        // considera
                                        var considera = "NÃO";

                                        if (contrato.Considera == 1)
                                        {
                                            considera = "SIM";
                                        }

                                        <tr>
                                            <td>@contrato.Ordem</td>
                                            <td>@contrato.Contrato_Codigo</td>
                                            <td>@considera</td>
                                            <td>@string.Format("{0:#,##0.000} MWh", prov.Consumo_Total_Bruto)</td>
                                            <td>@string.Format("{0:0.0} %", prov.PercentualCarga_Valor)</td>
                                            <td>@string.Format("{0:0.0} %", prov.Perdas)</td>
                                            <td>@string.Format("{0:#,##0.000} MWh", prov.Previsao_Consumo)</td>
                                            <td>@string.Format("{0:#,##0.000} MWh", prov.PROINFA)</td>
                                            <td>@string.Format("{0:#,##0.000} MWh", prov.Necessidade_Contratacao)</td>
                                            <td>@string.Format("{0:#,##0.000} MWh", prov.Contrato)</td>
                                            <td>@string.Format("{0:#,##0.000} MWh", prov.Take_Minimo)</td>
                                            <td>@string.Format("{0:#,##0.000} MWh", prov.Take_Maximo)</td>
                                            <td>@string.Format("{0:#,##0.000} MWh", prov.Volume_Contratado)</td>
                                            <td>@string.Format("{0:#,##0.000} MWh", prov.Total_Contrato)</td>
                                        </tr>
                                    }
                                }

                            </tbody>
                        </table>

                    </div>
                </div>
            
                <div class="page-break"></div>

                <div class="row">
                    <div class="col-xs-12">
                        <div class="panel panel-title">
                            <div class="panel-heading">
                                <h4>@SmartEnergy.Resources.ContratosCCEETexts.ContratosCCEE</h4>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-xs-12">
                        <table class="table table-striped table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>Sequência</th>
                                    <th></th>
                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.SiglaCCEE</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.ContratoCodigo</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.Fornecedor</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.VigenciaInicio</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.VigenciaFim</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.Considera</th>
                                </tr>
                            </thead>
                            <tbody>

                                @{
                                    List<AgentesDominio> listatiposComercializadoras = ViewBag.listaTipoComercializadoras;

                                    foreach (ContratosCCEEDominio contrato in contratosEnergia)
                                    {
                                        string logo = "http://www.smartenergy.com.br";

                                        if (contrato.Logo.IsEmpty())
                                        {
                                            logo += "/Logos/LogoSmartEnergy.png";
                                        }
                                        else
                                        {
                                            logo += "/Logos/" + contrato.Logo;
                                        }

                                        // comercializadora
                                        AgentesDominio tipo_comercializadora = listatiposComercializadoras.Find(item => item.IDAgente == contrato.IDComercializadora);
                                        string comercializadora = "---";
                                        if (tipo_comercializadora != null)
                                        {
                                            comercializadora = tipo_comercializadora.Nome;
                                        }
                                            
                                        // considera
                                        var considera = "NÃO";
                                                            
                                        if (contrato.Considera == 1)
                                        {
                                            considera = "SIM";
                                        }
                                                            
                                        <tr>
                                            <td>@contrato.Ordem</td>
                                            <td bgcolor="#293846" align="center"><img src=@logo title="@contrato.NomeCliente" style="max-height:45px; height: auto;" /></td>
                                            <td><b>@contrato.SiglaCCEE</b><br />@contrato.RazaoSocial</td>
                                            <td>@contrato.Contrato_Codigo</td>
                                            <td>@comercializadora</td>
                                            <td><span style="display:none;">@contrato.Vigencia_Inicio_Sort_aux</span>@contrato.Vigencia_Inicio_aux</td>
                                            <td><span style="display:none;">@contrato.Vigencia_Fim_Sort_aux</span>@contrato.Vigencia_Fim_aux</td>
                                            <td>@considera</td>
                                        </tr>
                                    }
                                }

                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="page-break"></div>

                <div class="row">
                    <div class="col-xs-12">
                        <div class="panel panel-title">
                            <div class="panel-heading">
                                <h4>Consumo Diário de @ViewBag.MesAtual</h4>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="relat-grafico" style="height:400px; width:800px; margin-left:50px;">
                                        <div id="consumo-mes-chart" style="margin-top: 10px;"></div>
                                        <div class='chart-legend' style="display:none;">
                                            <div class='legend-scale-dem'>
                                                <ul class='legend-labels' style="margin-top: -25px;">
                                                    <li><span style='background:#1C84C6;'></span>@SmartEnergy.Resources.ContratosCCEETexts.ConsumoConsolidado</li>
                                                    <li><span style='background:#FF7F00;'></span>@SmartEnergy.Resources.ContratosCCEETexts.ConsumoEstimado</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <br />
                <div class="row">
                    <div class="col-xs-12">
                        <div class="panel panel-title">
                            <div class="panel-heading">
                                <h4>Consumo 12 Meses</h4>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="relat-grafico" style="height:400px; width:800px; margin-left:50px;">
                                        <div id="consumo-12meses-chart" style="margin-top: 10px;"></div>
                                        <div class='chart-legend' style="display:none;">
                                            <div class='legend-scale-dem'>
                                                <ul class='legend-labels' style="margin-top: -25px;">
                                                    <li><span style='background:#1C84C6;'></span>Consumo Consolidado</li>
                                                    <li><span style='background:#FF7F00;'></span>Consumo Estimado</li>
                                                    <li><span style='background:#FF0000;'></span>Take Máximo</li>
                                                    <li><span style='background:#00FF00;'></span>Contrato</li>
                                                    <li><span style='background:#0000FF;'></span>Take Mínimo</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <br />
                <div class="row">
                    <div class="col-xs-12">
        
                        <i class="fa fa-exclamation-triangle" style="font-size:20px;"></i><span style="font-size:large; padding-left:8px;"><b>Os valores de consumo consideram as perdas estipuladas em contrato.</b></span>

                        <br /><br />
                        <i class="fa fa-calendar" style="font-size:20px;"></i><span style="font-size:large; padding-left:8px;"><b>@ViewBag.DataMesExtenso</b></span>

                    </div>
                </div>
            
                <div class="page-break"></div>

                if (provs != null)
                {
                    string unidade_consumidora = SmartEnergy.Resources.MenuTexts.MenuLateralRateio;
                    
                    if (provs.Count > 0)
                    {
                        if (unidadesConsumidoras != null)
                        {
                            if (unidadesConsumidoras.Count > 0)
                            {
                                unidade_consumidora += string.Format(" - {0} - CNPJ {1}", unidadesConsumidoras[0].RazaoSocial, unidadesConsumidoras[0].CNPJ);
                            }
                        }                            
                    }                        
                    
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="panel panel-title">
                                <div class="panel-heading">
                                    <h4>@unidade_consumidora</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <table class="table table-striped table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>@SmartEnergy.Resources.ContratosCCEETexts.ContratoCodigo</th>
                                        <th>@SmartEnergy.Resources.ContratosCCEETexts.ConsumoConsolidado<br />(MWh)</th>
                                        <th>@SmartEnergy.Resources.ContratosCCEETexts.ConsumoEstimado<br />(MWh)</th>
                                        <th>@SmartEnergy.Resources.ContratosCCEETexts.ConsumoTotalBruto<br />(MWh)</th>
                                        <th>@SmartEnergy.Resources.RelatoriosTexts.Consumo<br />(%)</th>
                                        <th>@SmartEnergy.Resources.ContratosCCEETexts.VolumeFaturado<br />(MWh)</th>
                                        <th>@SmartEnergy.Resources.ContratosCCEETexts.SobraDeficit<br />(MWh)</th>
                                        <th>@SmartEnergy.Resources.ContratosCCEETexts.PrecoEnergia<br />(R$/MWh)</th>
                                        <th>PLD<br />(R$/MWh)</th>
                                        <th>@SmartEnergy.Resources.ContratosCCEETexts.FaturaLP<br />(R$)</th>
                                        <th>@SmartEnergy.Resources.ContratosCCEETexts.FaturaCP<br />(R$)</th>
                                        <th>@SmartEnergy.Resources.ContratosCCEETexts.TotalCustoEnergia<br />(R$)</th>
                                    </tr>
                                </thead>
                                <tbody>

                                    @{
                                        if (provs.Count > 0)
                                        {
                                            int ordem = 0;
                                            double PLD = 0.0;
                                                
                                            foreach (Provisionamento_Tipo1_Tipo2 prov in provs)
                                            {
                                                ordem = ordem + 1;
                                                    
                                                List<Provisionamento_UnidadesConsumidoras> unidadesConsumidorasRateio = prov.unidadesConsumidoras;

                                                if (unidadesConsumidorasRateio != null)
                                                {
                                                    foreach (Provisionamento_UnidadesConsumidoras unid in unidadesConsumidorasRateio)
                                                    {
                                                        PLD = unid.PLD;
                                                            
                                                        <tr>
                                                            <td>@prov.Contrato_Codigo</td>
                                                            <td>-</td>
                                                            <td>-</td>
                                                            <td>-</td>
                                                            <td>-</td>
                                                            <td>@string.Format("{0:#,##0.000}", unid.Volume_Faturado)</td>
                                                            <td>-</td>
                                                            <td>@string.Format("{0:#,##0.00}", unid.Preco_Energia)</td>
                                                            <td>-</td>
                                                            <td>@string.Format("{0:#,##0.00}", unid.TotalFaturamento_LP)</td>
                                                            <td>-</td>
                                                            <td>-</td>
                                                        </tr>
                                                    }
                                                }
                                            }

                                            // total                                                
                                            Provisionamento_Tipo1_Tipo2 prov_total = provs[0];

                                            <tr>
                                                <td><b>TOTAL</b><br />&nbsp;</td>
                                                <td>@string.Format("{0:#,##0.000}", prov_total.Consumo_Consolidado)</td>
                                                <td>@string.Format("{0:#,##0.000}", prov_total.Consumo_Estimado)</td>
                                                <td>@string.Format("{0:#,##0.000}", prov_total.Consumo_Total_Bruto)</td>
                                                <td>100.0%</td>
                                                <td>@string.Format("{0:#,##0.000}", provisionamento.Volume_Contratado)</td>
                                                <td>@string.Format("{0:#,##0.000}", provisionamento.Resultado)</td>
                                                <td>-</td>
                                                <td>@string.Format("{0:#,##0.00}", PLD)</td>
                                                <td>@string.Format("{0:#,##0.00}", provisionamento.TotalFaturamento_LP)</td>
                                                <td>@string.Format("{0:#,##0.00}", provisionamento.TotalFaturamento_CP)</td>
                                                <td>@string.Format("{0:#,##0.00}", provisionamento.TotalCusto_Energia)</td>
                                            </tr>                                    
                                                
                                        }
                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                                                
                }
            
                <div class="page-break"></div>
                
                <div class="row">
                    <div class="col-xs-12">
                        <div class="panel panel-title">
                            <div class="panel-heading">
                                <h4>@SmartEnergy.Resources.FinancasTexts.FaturaEnergia</h4>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12">
                        <table class="table table-striped table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.UnidadeConsumidora</th>
                                    <th>CNPJ</th>
                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.Contrato</th>
                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.EstruturaTarifaria</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.FaturaUsoRede<br />(R$)</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.FaturaLongoPrazo<br />(R$)</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.FaturaCurtoPrazo<br />(R$)</th>
                                </tr>
                            </thead>
                            <tbody>

                                @{
                                
                                    if (provisionamento != null)
                                    {
                                        // tipos contrato
                                        List<ListaTiposDominio> listaTipoContratoMedicao = ViewBag.listaTipoContratoMedicao;

                                        // estrutura tarifária
                                        List<ListaTiposDominio> listaTipoEstruturaTarifaria = ViewBag.listaTipoEstruturaTarifaria;
                                
                                        if (unidadesConsumidoras != null)
                                        {
                                            foreach (Provisionamento_UnidadesConsumidoras unid in unidadesConsumidoras)
                                            {
                                                ListaTiposDominio tipo_contrato = listaTipoContratoMedicao.Find(item => item.ID == unid.IDContratoMedicao);
                                                string tipo_contrato_descr = "---";
                                                if (tipo_contrato != null)
                                                {
                                                    tipo_contrato_descr = tipo_contrato.Descricao;
                                                }
                                        
                                                ListaTiposDominio estrutura_tarifaria = listaTipoEstruturaTarifaria.Find(item => item.ID == unid.IDEstruturaTarifaria);
                                                string estrutura_tarifaria_descr = "---";
                                                if (estrutura_tarifaria != null)
                                                {
                                                    estrutura_tarifaria_descr = estrutura_tarifaria.Descricao;
                                                }

                                       
                                                <tr>
                                                    <td><b>@unid.SiglaCCEE</b><br />@unid.RazaoSocial</td>
                                                    <td>@unid.CNPJ</td>
                                                    <td>@tipo_contrato_descr</td>
                                                    <td>@estrutura_tarifaria_descr</td>
                                                    <td>@string.Format("{0:#,##0.00}", unid.Fatura_Distribuidora_Valor)</td>
                                                    <td>@string.Format("{0:#,##0.00}", provisionamento.TotalFaturamento_LP)</td>
                                                    <td>@string.Format("{0:#,##0.00}", provisionamento.TotalFaturamento_CP)</td>
                                                </tr>
                                            }
                                        }
                                    }                                    
                                }

                            </tbody>
                        </table>
                    </div>
                </div>
                <br />
                <div class="row">
                    <div class="col-lg-12">
                        <i class="fa fa-exclamation-triangle" style="font-size:20px;"></i><span style="font-size:large; padding-left:8px;"><b>Essa é uma simulação.<br /><br />Alguns fatores podem alterar o valor final, como:<br />Ajustes contratuais junto à distribuidora; Demanda registrada acima da contratada; Energia reativa; Contribuição Municipal de Iluminação Pública; Custo de conexão.</b></span>
                    </div>
                </div>
                            
            }
        }

    </div>

    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>


<script type="text/javascript">

    $(document).ready(function () {

        //
        // ANALISE CONTRATO x CONSUMO
        //

        // barras
        var Barras = [];

        Barras.push('x');
        Barras.push('Previsao de Consumo');
        Barras.push('Contrato');

        // valores
        var dataValores = [];

        // copia valores
        var PrevisaoConsumo = @Html.Raw(Json.Encode(@ViewBag.PrevisaoConsumo));
        var Contrato = @Html.Raw(Json.Encode(@ViewBag.Contrato));

        var MaxGrafico = Math.ceil(@ViewBag.MaxGrafico);

        // verifico se nao tem dados e forco um limite
        if( MaxGrafico == 0.0)
        {
            MaxGrafico = 100.0;
        }

        dataValores.push(['data1']);
        dataValores.push([PrevisaoConsumo]);
        dataValores.push([Contrato]);

        c3.generate({

            bindto: '#grafico-ConsumoContrato',
            size: {
                height: 290
            },
            padding: {
                top: 8,
                right: 16,
                bottom: 0,
                left: 63
            },
            data: {
                x: 'x',
                columns: [Barras,dataValores],
                types: {
                    data1: 'bar',
                },
                labels: {
                    format: {
                        data1: function (v, id, i, j) { 
                            
                            var valor = v;
                            var str_valor = valor.toFixed(3).replace('.', ',');

                            //return (str_valor.replace(/\B(?=(\d{3})+(?!\d))/g, ".") + " MWh"); 
                            return (str_valor + " MWh"); 
                        },
                    }
                },
                color: function (color, d) {
                    if (typeof d.index === 'undefined') { return color; }

                    if( d.index == 0 )
                        return '#007F00';

                    if( d.index == 1 )
                        return '#1C84C6';

                    return '#007F00';
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type : 'categories',
                    tick: {
                        outer: false,
                        fit: true
                    }
                },
                y:  {
                    max: MaxGrafico,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {
                            return d.toFixed(0);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.5 },
            }
        });


        //
        // CONSUMO DO MÊS
        //
        var dataX = [];
        var labelX = [];
        var dataConsumo = [];

        // copia valores
        var Consumo = @Html.Raw(Json.Encode(@ViewBag.ConsumoMes));
        var Periodo = @Html.Raw(Json.Encode(@ViewBag.PeriodoMes));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.DatasMes));
        var NumDiasMes = @Html.Raw(Json.Encode(@ViewBag.NumDiasMes));
        var Meses = @Html.Raw(Json.Encode(@ViewBag.Meses));

        var maximoCons = @Html.Raw(Json.Encode(@ViewBag.ConsMaxGraficoMes));

        var Unidade = @Html.Raw(Json.Encode(@ViewBag.UnidadeConsumo));

        dataX.push('x');
        dataConsumo.push(['data1']);

        for (i = 0; i < NumDiasMes+2; i++)
        {
            // X
            dataX.push(Datas[i]);

            dataConsumo.push([Consumo[i]]);
        }

        // valores label X
        labelX.push(Datas[1]);

        var ndias = (NumDiasMes < 25) ? NumDiasMes : 25;

        for (i = 5; i <= ndias; i+=5)
        {
            labelX.push(Datas[i]);
        }
        labelX.push(Datas[NumDiasMes]);

        var Data = [dataX,dataConsumo];

        c3.generate({

            bindto: '#consumo-mes-chart',
            size: {
                height: 390
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 63
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'bar'
                },
                color: function (color, d) {
                    if (typeof d.index === 'undefined') { return color; }

                    if( Periodo[d.index] == 0 )
                        return '#1C84C6';

                    if( Periodo[d.index] == 1 )
                        return '#FF7F00';

                    return '#FF7F00';
                },
                colors: {
                    data1: '#1C84C6'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // retorna dia
                            return x.getDate();
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    // Grafico 1 dia     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(11 horas)
                    padding: -1000*60*60*11
                },
                y:  {
                    max: maximoCons,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoCons < 100)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.15 },
            }
        });

        //
        // CONSUMO DOS 12 MESES
        //
        var dataX_12meses = [];
        var labelX_12meses = [];
        var dataConsumo_12meses = [];
        var dataContrato_12meses = [];
        var dataTake_Min_12meses = [];
        var dataTake_Max_12meses = [];

        // copia valores
        var Consumo_12meses = @Html.Raw(Json.Encode(@ViewBag.Consumo12Meses));
        var Periodo_12meses = @Html.Raw(Json.Encode(@ViewBag.Periodo12Meses));
        var Contrato_12meses = @Html.Raw(Json.Encode(@ViewBag.Contrato12Meses));
        var Take_Min_12meses = @Html.Raw(Json.Encode(@ViewBag.Take_Min12Meses));
        var Take_Max_12meses = @Html.Raw(Json.Encode(@ViewBag.Take_Max12Meses));
        var Datas_12meses = @Html.Raw(Json.Encode(@ViewBag.Datas12Meses));

        var maximoCons_12meses = @Html.Raw(Json.Encode(@ViewBag.ConsMaxGrafico12Meses));

        dataX_12meses.push('x');
        dataConsumo_12meses.push(['data1']);
        dataContrato_12meses.push(['data2']);
        dataTake_Min_12meses.push(['data3']);
        dataTake_Max_12meses.push(['data4']);

        for (i = 0; i < 14; i++)
        {
            // X
            dataX_12meses.push(Datas_12meses[i]);

            dataConsumo_12meses.push([Consumo_12meses[i]]);
            dataContrato_12meses.push([Contrato_12meses[i]]);
            dataTake_Min_12meses.push([Take_Min_12meses[i]]);
            dataTake_Max_12meses.push([Take_Max_12meses[i]]);
        }

        for (i = 1; i <= 12; i++)
        {
            // valores label X
            labelX_12meses.push(Datas_12meses[i]);
        }

        var Data_12meses = [dataX_12meses,dataConsumo_12meses,dataContrato_12meses,dataTake_Min_12meses,dataTake_Max_12meses];

        c3.generate({

            bindto: '#consumo-12meses-chart',
            size: {
                height: 390
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 63
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data_12meses,
                types: {
                    data1: 'bar',
                    data2: 'line',
                    data3: 'line',
                    data4: 'line'
                },
                color: function (color, d) {
                    if (typeof d.index === 'undefined') { return color; }

                    if( Periodo_12meses[d.index] == 0 )
                        return '#1C84C6';

                    if( Periodo_12meses[d.index] == 1 )
                        return '#FF7F00';

                    return '#FF7F00';
                },
                colors: {
                    data2: '#00FF00',
                    data3: '#0000FF',
                    data4: '#FF0000'
                },
            },
            point: {
                show: false,
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        curling: false,
                        values: labelX_12meses,
                        format: function (x) {

                            // mes
                            var mes = x.getMonth();

                            // ano
                            var year = x.getFullYear();
                            var ano = year.toString().substr(-2);

                            // mes e ano
                            var mesAno = Meses[mes] + "/" + ano;

                            return mesAno;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    // Grafico 1 dia     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(11 horas)
                    // Grafico 1 mes     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(24 horas)*(13 dias)
                    padding: -1000*60*60*24*13
                },
                y:  {
                    max: maximoCons_12meses,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoCons_12meses < 100)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.6 },
            }
        });

        // labels
        $('.chart-legend').css("display", "block");

    });

</script>
