﻿<style>
    #fatpot-chart .c3-area {
        opacity: 0.3;
    }
</style>

<div class="row">
    <div class="col-lg-4 col-md-12 relatorio_real">
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia @SmartEnergy.Resources.SupervisaoTexts.MaisCapacitivo</h5>
                        <h1>@ViewBag.FatPot_MaisCapFPC</h1>
                        <h6>@ViewBag.FatPot_MaisCapFPC_DataHora</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia @SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</h5>
                        <h1>@ViewBag.FatPot_MaisIndFPI</h1>
                        <h6>@ViewBag.FatPot_MaisIndFPI_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia @SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</h5>
                        <h1>@ViewBag.FatPot_MaisIndP</h1>
                        <h6>@ViewBag.FatPot_MaisIndP_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4 col-md-12 simulacao" style="display:none;">
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia @SmartEnergy.Resources.SupervisaoTexts.MaisCapacitivo</h5>
                        <h1>@ViewBag.FatPot_MaisCapFPC_Sim</h1>
                        <h6>@ViewBag.FatPot_MaisCapFPC_DataHora_Sim</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia @SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</h5>
                        <h1>@ViewBag.FatPot_MaisIndFPI_Sim</h1>
                        <h6>@ViewBag.FatPot_MaisIndFPI_DataHora_Sim</h6>
                    </div>
                </div>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia @SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</h5>
                        <h1>@ViewBag.FatPot_MaisIndP_Sim</h1>
                        <h6>@ViewBag.FatPot_MaisIndP_DataHora_Sim</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-8 col-md-12">
        <div class="row">
            <div class="relat-grafico">
                <div id="fatpot-chart" style="margin-top: -10px"></div>
                <div class='chart-legend' style="display:none;">
                    <div class='legend-scale-dem'>
                        <ul class='legend-labels' style="margin-top: -25px;">
                            <li><span style='background:#1C84C6;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                            <li><span style='background:#007f00;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                            <li><span style='background:#ff0000;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                            <li class="simulacao" style="display: none;"><span style='background:#00b1ff;'></span>@SmartEnergy.Resources.RelatoriosTexts.Simulacao</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@Html.Partial("_Relat_Botoes")

<br />
<div class="panel panel-title relatorio">
    <div class="panel-body">

        <div class="row">
            <div class="col-lg-12">
                <div class="relat-tabela relatorio_real">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisCapacitivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisCapFPC<br /><small>(@ViewBag.FatPot_MaisCapFPC_DataHora)</small></td>
                                <td class="relat-fponta">@ViewBag.FatPot_MaisCapFPI<br /><small>(@ViewBag.FatPot_MaisCapFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.FatPot_MaisCapP<br /><small>(@ViewBag.FatPot_MaisCapP_DataHora)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisIndFPC<br /><small>(@ViewBag.FatPot_MaisIndFPC_DataHora)</small></td>
                                <td class="relat-fponta">@ViewBag.FatPot_MaisIndFPI<br /><small>(@ViewBag.FatPot_MaisIndFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.FatPot_MaisIndP<br /><small>(@ViewBag.FatPot_MaisIndP_DataHora)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.NoPeriodo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_NoPeriodoFPC</td>
                                <td class="relat-fponta">@ViewBag.FatPot_NoPeriodoFPI</td>
                                <td class="relat-ponta">@ViewBag.FatPot_NoPeriodoP</td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.Referencia</td>
                                <td class="relat-capacitivo">@ViewBag.RefenciaFPC</td>
                                <td class="relat-fponta">@ViewBag.RefenciaFPI</td>
                                <td class="relat-ponta">@ViewBag.RefenciaFPI</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="relat-tabela simulacao" style="display:none;">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisCapacitivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisCapFPC_Sim<br /><small>(@ViewBag.FatPot_MaisCapFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.FatPot_MaisCapFPI_Sim<br /><small>(@ViewBag.FatPot_MaisCapFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.FatPot_MaisCapP_Sim<br /><small>(@ViewBag.FatPot_MaisCapP_DataHora_Sim)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisIndFPC_Sim<br /><small>(@ViewBag.FatPot_MaisIndFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.FatPot_MaisIndFPI_Sim<br /><small>(@ViewBag.FatPot_MaisIndFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.FatPot_MaisIndP_Sim<br /><small>(@ViewBag.FatPot_MaisIndP_DataHora_Sim)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.NoPeriodo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_NoPeriodoFPC_Sim</td>
                                <td class="relat-fponta">@ViewBag.FatPot_NoPeriodoFPI_Sim</td>
                                <td class="relat-ponta">@ViewBag.FatPot_NoPeriodoP_Sim</td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.Referencia</td>
                                <td class="relat-capacitivo">@ViewBag.RefenciaFPC</td>
                                <td class="relat-fponta">@ViewBag.RefenciaFPI</td>
                                <td class="relat-ponta">@ViewBag.RefenciaFPI</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal inmodal animated fadeIn" id="modalTabela" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-editar-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title"><i class="fa fa-list"></i>&nbsp;&nbsp;@SmartEnergy.Resources.RelatoriosTexts.TabelaRegistros</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-default relatorio_real">
                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:34%;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>
                                <th style="width:33%;">@SmartEnergy.Resources.RelatoriosTexts.Periodo</th>
                                <th style="width:33%;">@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia</th>
                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var i = 0;
                                var j = 0;
                                var classe = "relat-semreg";
                                var periodo = "---";
                                var fatpot = "---";

                                for (i = 0; i < 24; i++)
                                {
                                    j = i + 1;

                                    switch ((int)ViewBag.Periodo[j])
                                    {
                                        case 0:
                                            classe = "relat-ponta";
                                            periodo = SmartEnergy.Resources.SupervisaoTexts.Ponta;
                                            break;

                                        case 1:
                                            classe = "relat-indutivo";
                                            periodo = SmartEnergy.Resources.SupervisaoTexts.FPontaInd;
                                            break;

                                        case 2:
                                            classe = "relat-capacitivo";
                                            periodo = SmartEnergy.Resources.SupervisaoTexts.FPontaCap;
                                            break;

                                        default:
                                        case 3:
                                            classe = "relat-semreg";
                                            periodo = "---";
                                            break;
                                    }

                                    if (ViewBag.Periodo[j] == 3)
                                    {
                                        fatpot = "---";
                                    }
                                    else
                                    {
                                        fatpot = string.Format("{0:0.000}", ViewBag.FatPot[j]);
                                    }

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="@classe">@ViewBag.Horas[j]</td>
                                        <td style="text-align:center;" class="@classe">@periodo</td>
                                        <td style="text-align:center;" class="@classe">@fatpot</td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>
                <div class="panel panel-default simulacao" style="display:none;">
                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:34%;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>
                                <th style="width:33%;">@SmartEnergy.Resources.RelatoriosTexts.Periodo</th>
                                <th style="width:33%;">@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia</th>
                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var classe_sim = "relat-semreg";
                                var periodo_sim = "---";
                                var fatpot_sim = "---";

                                for (i = 0; i < 24; i++)
                                {
                                    j = i + 1;

                                    switch ((int)ViewBag.Periodo_Sim[j])
                                    {
                                        case 0:
                                            classe_sim = "relat-ponta";
                                            periodo_sim = SmartEnergy.Resources.SupervisaoTexts.Ponta;
                                            break;

                                        case 1:
                                            classe_sim = "relat-indutivo";
                                            periodo_sim = SmartEnergy.Resources.SupervisaoTexts.FPontaInd;
                                            break;

                                        case 2:
                                            classe_sim = "relat-capacitivo";
                                            periodo_sim = SmartEnergy.Resources.SupervisaoTexts.FPontaCap;
                                            break;

                                        default:
                                        case 3:
                                            classe_sim = "relat-semreg";
                                            periodo_sim = "---";
                                            break;
                                    }

                                    if (ViewBag.Periodo_Sim[j] == 3)
                                    {
                                        fatpot_sim = "---";
                                    }
                                    else
                                    {
                                        fatpot_sim = string.Format("{0:0.000}", ViewBag.FatPot_Sim[j]);
                                    }

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="@classe_sim">@ViewBag.Horas_Sim[j]</td>
                                        <td style="text-align:center;" class="@classe_sim">@periodo_sim</td>
                                        <td style="text-align:center;" class="@classe_sim">@fatpot_sim</td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
            </div>
        </div>
    </div>
</div>


@Html.Partial("_EventosModal")

@Html.Partial("_ObservacoesModal")


<script type="text/javascript">

    $(document).ready(function () {

        // fator de potencia
        var dataX = [];
        var labelX = [];
        var dataReferenciaFPC = [];
        var dataReferenciaFPI = [];
        var dataReferenciaP = [];
        var dataFatPot = [];
        var dataFatPotUm = [];

        // fator de potencia simulacao
        var dataFatPot_Sim = [];
        var dataFatPotUm_Sim = [];

        // copia valores
        var FatPot = @Html.Raw(Json.Encode(@ViewBag.FatPot));
        var Periodo = @Html.Raw(Json.Encode(@ViewBag.Periodo));
        var Dias = @Html.Raw(Json.Encode(@ViewBag.Dias));
        var FatPotMin = @Html.Raw(Json.Encode(@ViewBag.FatPotMin));

        // copia valores simulacao
        var FatPot_Sim = @Html.Raw(Json.Encode(@ViewBag.FatPot_Sim));

        dataX.push('x');
        dataFatPot.push(['data1']);
        dataReferenciaFPC.push(['data2']);
        dataReferenciaFPI.push(['data3']);
        dataReferenciaP.push(['data4']);
        dataFatPotUm.push(['data5']);

        // simulacao
        dataFatPot_Sim.push(['data6']);

        for (i = 0; i < 26; i++)
        {
            // X
            dataX.push(Dias[i]);

            // linha de fatpot um
            dataFatPotUm.push([0.0]);

            // converte para nova escala
            if( FatPot[i] < 0 )
            {
                dataFatPot.push([(1.0 - (-1.0*FatPot[i])) * -1.0]);
            }
            else
            {
                dataFatPot.push([(1.0 - FatPot[i])]);
            }

            // converte para nova escala simulacao
            if( FatPot_Sim[i] < 0 )
            {
                dataFatPot_Sim.push([(1.0 - (-1.0*FatPot_Sim[i])) * -1.0]);
            }
            else
            {
                dataFatPot_Sim.push([(1.0 - FatPot_Sim[i])]);
            }

            // periodo
            switch(Periodo[i])
            {
                case 0:
                    dataReferenciaP.push([0.08]);
                    dataReferenciaFPI.push([0]);
                    dataReferenciaFPC.push([0]);
                    break;

                case 1:
                    dataReferenciaP.push([0]);
                    dataReferenciaFPI.push([0.08]);
                    dataReferenciaFPC.push([0]);
                    break;

                case 2:
                    dataReferenciaP.push([0]);
                    dataReferenciaFPI.push([0]);
                    dataReferenciaFPC.push([-0.08]);
                    break;

                default:
                    dataReferenciaP.push([0]);
                    dataReferenciaFPI.push([0]);
                    dataReferenciaFPC.push([0]);
                    break;
            }

        }

        // prepara escala eixo Y
        var maximo = (1.0 - FatPotMin);
        var minimo = -1.0 * maximo;

        // valores label X
        labelX.push(Dias[1]);
        for (i = 3; i <= 24; i+=3)
        {
            labelX.push(Dias[i]);
        }

        var Data = [dataX,dataFatPot,dataReferenciaFPC,dataReferenciaFPI,dataReferenciaP,dataFatPotUm,dataFatPot_Sim];

        var chart = c3.generate({
            bindto: '#fatpot-chart',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 41
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'area-step',
                    data3: 'area-step',
                    data4: 'area-step',
                    data5: 'step',
                    data6: 'step'
                },
                color: function (color, d) {
                    if (typeof d.index === 'undefined') { return color; }

                    var cor = '#007F00'

                    switch(Periodo[d.index])
                    {
                        case 0:
                            cor = '#FF0000'
                            break;
                        case 1:
                            cor = '#007F00'
                            break;
                        case 2:
                            cor = '#1C84C6'
                            break;
                    }

                    return cor;
                },
                colors: {
                    data2: '#1C84C6',
                    data3: '#007F00',
                    data4: '#FF0000',
                    data5: '#000000',
                    data6: '#00b1ff'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*28
                },
                y:  {
                    max: maximo,
                    min: minimo,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            var valor;

                            // converte para nova escala
                            if( d < 0 )
                            {
                                valor = (1.0 - (-1.0*d)) * -1.0;
                            }
                            else
                            {
                                valor = (1.0 - d);
                            }

                            return valor.toFixed(3);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.25 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < d.length; i++) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        if (! text) {

                            // split string and create array.
                            var arr = Dias[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // horas e minutos
                            var hours = data.getHours();
                            var minutes = data.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            title = hours + ':' + minutes;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // verifica se barra
                        if( i == 0 )
                        {
                            switch(Periodo[d[i].index])
                            {
                                case 0:
                                    name = PONTA;
                                    break;
                                case 1:
                                default:
                                    name = FPONTA_IND;
                                    break;
                                case 2:
                                    name = FPONTA_CAP;
                                    break;
                            }

                            value = valueFormat(d[i].value, d[i].ratio, d[i].id, d[i].index);
                        }

                        // verifica se referencia
                        if( i == 1 )
                        {
                            name = REFERENCIA;

                            // verifica se FPC
                            if(Periodo[d[i].index] == 2)
                            {
                                // pego valor referencia FPC
                                value = valueFormat(d[1].value, d[1].ratio, d[1].id, d[1].index);
                            }

                            // verifica se FPI
                            if(Periodo[d[i].index] == 1)
                            {
                                // pego valor referencia FPI
                                value = valueFormat(d[2].value, d[2].ratio, d[2].id, d[2].index);
                            }

                            // verifica se P
                            if(Periodo[d[i].index] == 0)
                            {
                                // pego valor referencia FPI
                                value = valueFormat(d[3].value, d[3].ratio, d[3].id, d[3].index);
                            }
                        }

                        // nao desenha segundo contrato
                        if( i > 1 && i != 5)
                        {
                            continue;
                        }

                        switch(Periodo[d[i].index])
                        {
                            case 0:
                                bgcolor = '#FF0000';
                                break;
                            case 1:
                            default:
                                bgcolor = '#007F00';
                                break;
                            case 2:
                                bgcolor = '#1C84C6';
                                break;
                        }

                        // verifica se simulação
                        if (i == 5)
                        {                       
                            switch(Periodo[d[i].index])
                            {
                                case 0:
                                    name = PONTA + " - " + SIMULACAO;
                                    break;
                                case 1:
                                default:
                                    name = FPONTA_IND + " - " + SIMULACAO;
                                    break;
                                case 2:
                                    name = FPONTA_CAP + " - " + SIMULACAO;
                                    break;
                            }

                            value = valueFormat(d[i].value, d[i].ratio, d[i].id, d[i].index);
                            bgcolor = '#00b1ff';
                        }

                        text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                        text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                        text += "<td class='value'>" + value + "</td>";
                        text += "</tr>";
                    }
                    return text + "</table>";
                }
            }
        });

        // salva chart 
        $('#fatpot-chart').data('c3-chart', chart);

        // verifica se apresenta simulacao 
        ShowSimulacao();

        // labels
        $('.chart-legend').css("display", "block");

        $(".modal-dialog").draggable({
            handle: ".modal-editar-header"
        });
    });

    function ShowSimulacao () {

        var IDSimulacaoCenario = parseInt(@ViewBag._IDSimulacaoCenario);

        // verifica se ano existe nenhuma simulacao selecionada
        if (IDSimulacaoCenario == 0)
        {
            $("#aplica_simula").attr('disabled', true);
            $('#aplica_simula').prop('checked', false);
        }
        else
        {
            $("#aplica_simula").attr('disabled', false);
        }

        // grafico fator de potencia 
        var chart = $('#fatpot-chart').data('c3-chart');

        // checkbox aplica simulacao
        var simula = $('#aplica_simula').prop('checked');

        if (simula)
        {
            // apresenta grafico simulacao
            chart.show(['data6', 'data7']);

            $('.simulacao').css("display", "block");
            $('.relatorio_real').css("display", "none");
        }
        else
        {
            // apresenta grafico real
            chart.hide(['data6', 'data7']);

            $('.simulacao').css("display", "none");
            $('.relatorio_real').css("display", "block");
        }

        // salva em cookie
        setCookie("AplicaSimulacao", simula, null);

    }

</script>
