﻿@using SmartEnergyLib.SQL

<style>

    .dataTables-rateio td {
        font-size: 11px;
    }

    .dataTables-rateio th {
        font-size: 11px;
    }

</style>


<div class="row">
    <div class="col-lg-12">

        @{
            Provisionamento_Tipo3 provisionamento = ViewBag.ProvisionamentoSemanal;
            
            if (provisionamento != null)
            {
                List<Provisionamento_Tipo1_Tipo2> provs = provisionamento.por_contrato;
                
                if (provs != null)
                {
                    string unidade_consumidora = SmartEnergy.Resources.MenuTexts.MenuLateralRateio;
                    
                    if (provs.Count > 0)
                    {
                        List<Provisionamento_UnidadesConsumidoras> unidadesConsumidoras = provs[0].unidadesConsumidoras;
                                                
                        if (unidadesConsumidoras != null)
                        {
                            if (unidadesConsumidoras.Count > 0)
                            {
                                unidade_consumidora = string.Format("{0} - CNPJ {1}", unidadesConsumidoras[0].RazaoSocial, unidadesConsumidoras[0].CNPJ);
                            }
                        }                            
                    }                        
                    
                    <div class="panel panel-title">
                        <div class="panel-heading">
                            <h4>@unidade_consumidora</h4>
                        </div>
                        <div class="panel-body">
                            <div class="row">

                                <table class="table table-striped table-bordered table-hover dataTables-rateio">
                                    <thead>
                                        <tr>
                                            <th>Sequência</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.ContratoCodigo</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.ConsumoConsolidado<br />(MWh)</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.ConsumoEstimado<br />(MWh)</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.ConsumoTotalBruto<br />(MWh)</th>
                                            <th>@SmartEnergy.Resources.RelatoriosTexts.Consumo<br />(%)</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.VolumeFaturado<br />(MWh)</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.SobraDeficit<br />(MWh)</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.PrecoEnergia<br />(R$/MWh)</th>
                                            <th>PLD<br />(R$/MWh)</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.FaturaLP<br />(R$)</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.FaturaCP<br />(R$)</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.TotalCustoEnergia<br />(R$)</th>
                                        </tr>
                                    </thead>
                                    <tbody>

                                        @{
                                            if (provs.Count > 0)
                                            {
                                                int ordem = 0;
                                                double PLD = 0.0;
                                                
                                                foreach (Provisionamento_Tipo1_Tipo2 prov in provs)
                                                {
                                                    ordem = ordem + 1;
                                                    
                                                    List<Provisionamento_UnidadesConsumidoras> unidadesConsumidoras = prov.unidadesConsumidoras;
                                                
                                                    if (unidadesConsumidoras != null)
                                                    {
                                                        foreach (Provisionamento_UnidadesConsumidoras unid in unidadesConsumidoras)
                                                        {
                                                            PLD = unid.PLD;
                                                            
                                                            <tr>
                                                                <td>@ordem</td>
                                                                <td>@prov.Contrato_Codigo</td>
                                                                <td>-</td>
                                                                <td>-</td>
                                                                <td>-</td>
                                                                <td>-</td>
                                                                <td>@string.Format("{0:#,##0.000}", unid.Volume_Faturado)</td>
                                                                <td>-</td>
                                                                <td>@string.Format("{0:#,##0.00}", unid.Preco_Energia)</td>
                                                                <td>-</td>
                                                                <td>@string.Format("{0:#,##0.00}", unid.TotalFaturamento_LP)</td>
                                                                <td>-</td>
                                                                <td>-</td>
                                                            </tr>
                                                        }
                                                    }
                                                }

                                                // total                                                
                                                Provisionamento_Tipo1_Tipo2 prov_total = provs[0];

                                                <tr>
                                                    <td>@ordem</td>
                                                    <td><b>TOTAL</b><br />&nbsp;</td>
                                                    <td>@string.Format("{0:#,##0.000}", prov_total.Consumo_Consolidado)</td>
                                                    <td>@string.Format("{0:#,##0.000}", prov_total.Consumo_Estimado)</td>
                                                    <td>@string.Format("{0:#,##0.000}", prov_total.Consumo_Total_Bruto)</td>
                                                    <td>100.0%</td>
                                                    <td>@string.Format("{0:#,##0.000}", provisionamento.Volume_Contratado)</td>
                                                    <td>@string.Format("{0:#,##0.000}", provisionamento.Resultado)</td>
                                                    <td>-</td>
                                                    <td>@string.Format("{0:#,##0.00}", PLD)</td>
                                                    <td>@string.Format("{0:#,##0.00}", provisionamento.TotalFaturamento_LP)</td>
                                                    <td>@string.Format("{0:#,##0.00}", provisionamento.TotalFaturamento_CP)</td>
                                                    <td>@string.Format("{0:#,##0.00}", provisionamento.TotalCusto_Energia)</td>
                                                </tr>                                    
                                                
                                            }
                                        }

                                    </tbody>
                                </table>

                            </div>
                        </div>
                    </div>
                                                
                }
            }
        }

    </div>
</div>


<script type="text/javascript">

    $(document).ready(function () {


        $('.dataTables-rateio').DataTable({
            "bAutoWidth": false,
            "iDisplayLength": 18,
            dom: 'tp',

            "aoColumnDefs": [
                    { "aTargets": [0], "bVisible": false, "bSortable": false, "bSearchable": false },
                    { "aTargets": [1], "sType": "portugues", "bSortable": false, "bSearchable": false },
                    { "aTargets": [2], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [3], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [4], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [5], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [6], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [7], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [8], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [9], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [10], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [11], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [12], "sType": "numero", "bSortable": false, "bSearchable": false }
            ],

            "aoColumns": [
                { sWidth: "1%" },
                { sWidth: "14%" },
                { sWidth: "11%" },
                { sWidth: "11%" },
                { sWidth: "11%" },
                { sWidth: "11%" },
                { sWidth: "11%" },
                { sWidth: "11%" },
                { sWidth: "11%" },
                { sWidth: "11%" },
                { sWidth: "11%" },
                { sWidth: "11%" },
                { sWidth: "11%" }
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

    });

</script>
