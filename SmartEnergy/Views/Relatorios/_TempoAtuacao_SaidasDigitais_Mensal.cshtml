﻿@using SmartEnergyLib.SQL

@{   
    List<TempoAtuacaoMensal> temposAtuacao = ViewBag.temposAtuacao;

    int NumDias = 0;

    if (ViewBag.NumDias != null)
    {
        NumDias = ViewBag.NumDias;
    }
}

<div class="row" id="tempo_atuacao_resultado" style="display:none; min-height:450px;">
    <div class="col-lg-12">
        <div class="panel panel-default">
            <div class="panel-body">
                <div class="panel-body">
                    <table id="dataTables-tempoatuacao-1" class="table table-striped table-bordered table-hover dataTables-tempoatuacao-1">
                        <thead>
                            <tr>
                                <th>Gateways</th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.SaidasDigitais</th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.Descricao</th>

                                @{
                                    for (int i = 0; i < 31; i++)
                                    {
                                        string texto_dia = "";

                                        if (i < NumDias)
                                        {
                                            texto_dia = string.Format("{0}", i + 1);
                                        }

                                        <th>@texto_dia</th>
                                    }
                                }
                            </tr>
                        </thead>
                        <tbody>

                            @{
                                if (temposAtuacao != null)
                                {
                                    foreach (TempoAtuacaoMensal tempoAtuacao in temposAtuacao)
                                    {
                                        <tr>
                                            <td>@tempoAtuacao.Nome</td>
                                            <td>@tempoAtuacao.DescrSaida</td>
                                            <td>
                                                <p class="relat-medio">Ligado (HH:mm:ss)</p>
                                                <p>Desligado (HH:mm:ss)</p>
                                            </td>

                                            @for (int i = 0; i < 31; i++)
                                            {
                                                string ligado = "";
                                                string desligado = "";

                                                if (i < tempoAtuacao.NumDias)
                                                {
                                                    int ligado_hora = 0;
                                                    int desligado_hora = 0;

                                                    int ligado_min = 0;
                                                    int desligado_min = 0;

                                                    int ligado_seg = 0;
                                                    int desligado_seg = 0;


                                                    ligado_seg = (int)(@tempoAtuacao.TempoAtuacaoMensalValor[i].tempoLigado % 60);
                                                    desligado_seg = (int)(@tempoAtuacao.TempoAtuacaoMensalValor[i].tempoDesligado % 60);

                                                    ligado_min = (int)(((@tempoAtuacao.TempoAtuacaoMensalValor[i].tempoLigado - ligado_seg) / 60) % 60);
                                                    desligado_min = (int)(((@tempoAtuacao.TempoAtuacaoMensalValor[i].tempoDesligado - desligado_seg) / 60) % 60);

                                                    ligado_hora = (int)(((@tempoAtuacao.TempoAtuacaoMensalValor[i].tempoLigado - ligado_min) / 60) / 60);
                                                    desligado_hora = (int)(((@tempoAtuacao.TempoAtuacaoMensalValor[i].tempoDesligado - desligado_min) / 60) / 60);

                                                    ligado = string.Format("{0:00}:{1:00}:{2:00}", ligado_hora, ligado_min, ligado_seg);
                                                    desligado = string.Format("{0:00}:{1:00}:{2:00}", desligado_hora, desligado_min, desligado_seg);
                                                }

                                                <td>
                                                    <p class="relat-medio">@ligado</p>
                                                    <br />
                                                    <p>@desligado</p>
                                                </td>
                                            }
                                        </tr>
                                    }
                                }
                            }

                        </tbody>
                    </table>

                </div>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">

    $(document).ready(function () {

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {
                var x = (a == "-") ? 0 : a.replace(/,/, ".");
                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });


        $('.dataTables-tempoatuacao-1').DataTable({
            "scrollX": true,
            "responsive": true,
            "bAutoWidth": false,
            "iDisplayLength": 12,
            dom: 'ftp',
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "portugues", "bSortable": true, "bSearchable": true },
                        { "aTargets": [1], "sType": "portugues", "bSortable": true, "bSearchable": true },
                        { "aTargets": [2], "sType": "portugues", "bSortable": false, "bSearchable": true },
                        { "aTargets": [3], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [4], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [5], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [6], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [7], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [8], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [9], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [10], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [11], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [12], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [13], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [14], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [15], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [16], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [17], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [18], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [19], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [20], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [21], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [22], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [23], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [24], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [25], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [26], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [27], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [28], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [29], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [30], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [31], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [32], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [33], "sType": "numero", "bSortable": false, "bSearchable": false },
            ],
            "oSearch": {
                "bSmart": false,
                "bRegex": true,
                "sSearch": ""
            },
            "aoColumns": [                
            { sWidth: "14%" },
            { sWidth: "11%" },
            { sWidth: "14%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" }
            ],
            "order": [[0, "asc"], [1, "asc"]],
            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

        // cookie de search
        var search = decodeURIComponent(getCookie("Relat_Search"));
        $('.dataTables-tempoatuacao-1').DataTable().search(search).draw();

        $('.dataTables_filter input').unbind().keyup(function () {
            var value = $(this).val();
            if (value.length > 3 || value.length == 0) {

                // salva em cookie
                setCookie("Relat_Search", encodeURIComponent(value), null);

                $('.dataTables-tempoatuacao-1').DataTable().search(value).draw();
            }
        });

        // ajusta largura depois de um tempo
        setTimeout(function () {

            $('#dataTables-tempoatuacao-1').dataTable().fnSort([[0, "asc"]]);

        }, 10);

        // labels
        $('#tempo_atuacao_resultado').css("display", "block");


        // le cookie
        var url = getCookie('Relat_Tipo');

        // trigger datatable
        $('.dataTables-tempoatuacao-1')
                .on('order.dt', function () { TableOrder(0); })
                .DataTable();

    });

</script>
