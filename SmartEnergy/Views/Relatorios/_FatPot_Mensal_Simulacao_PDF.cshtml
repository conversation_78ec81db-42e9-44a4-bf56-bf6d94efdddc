﻿<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia</title>

    <link href="@Server.MapPath("~/Content/bootstrap.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/style.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/plugins/c3/c3.min.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/Relatorios.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/RelatoriosPDF.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/fonts/font-awesome/css/font-awesome.min.css")" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="@Server.MapPath("~/Scripts/jquery-2.1.1.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/d3/d3.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/c3/c3.min.js")"></script>

    <style>
        #fatpot-chart-simulacao .c3-area {
            opacity: 0.3;
        }
    </style>

</head>

@{
    string arquivo = "~/Logos/" + ViewBag._LogoConsultor;
    string logo = Server.MapPath(arquivo);
}

@if (ViewBag.Retorno == 2)
{
    <div class="row">
        <div class="col-lg-12">
            <div class="linha cabecalho">
                <div class="coluna1">
                    <p><img src="@logo" style="height:42px; width:auto;" /></p>
                </div>

                <div class="coluna2">
                    <h3>@ViewBag.ClienteNome</h3>
                    @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                </div>

                <div class="coluna3">
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia - @SmartEnergy.Resources.RelatoriosTexts.RelatMensal (@SmartEnergy.Resources.RelatoriosTexts.Simulacao)</h4>
                    <p>@ViewBag.DataTextoAtual</p>
                </div>
            </div>
        </div>
    </div>

    <br /><br /><br /><br /><br /><br />
    <div class="row">
        <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
            <i class="fa fa-warning fa-5x"></i>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>@SmartEnergy.Resources.RelatoriosTexts.NaoExistemRegistros</h2>
        </div>
    </div>
    <br /><br /><br /><br />

}
else
{

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="@logo" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia - @SmartEnergy.Resources.RelatoriosTexts.RelatMensal (@SmartEnergy.Resources.RelatoriosTexts.Simulacao)</h4>
                        <p>@ViewBag.DataTextoAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <br />

        @{
            if (ViewBag.showGrafico)
            {
                <div class="row">
                    <div class="relat-grafico" style="height:490px; width:800px; margin-left:50px;">
                        <div id="fatpot-chart-simulacao"></div>
                        <div class='chart-legend' style="display:none;">
                            <div class='legend-scale-dem'>
                                <ul class='legend-labels' style="margin-top: -25px;">
                                    <li><span style='background:#1C84C6 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                                    <li><span style='background:#007f00 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                                    <li><span style='background:#ff0000 !important;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }

        <hr />

        <div class="row" style="margin-top: -15px;">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisCapacitivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisCapFPC_Sim<br /><small>(@ViewBag.FatPot_MaisCapFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.FatPot_MaisCapFPI_Sim<br /><small>(@ViewBag.FatPot_MaisCapFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.FatPot_MaisCapP_Sim<br /><small>(@ViewBag.FatPot_MaisCapP_DataHora_Sim)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisIndFPC_Sim<br /><small>(@ViewBag.FatPot_MaisIndFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.FatPot_MaisIndFPI_Sim<br /><small>(@ViewBag.FatPot_MaisIndFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.FatPot_MaisIndP_Sim<br /><small>(@ViewBag.FatPot_MaisIndP_DataHora_Sim)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.NoPeriodo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_NoPeriodoFPC_Sim</td>
                                <td class="relat-fponta">@ViewBag.FatPot_NoPeriodoFPI_Sim</td>
                                <td class="relat-ponta">@ViewBag.FatPot_NoPeriodoP_Sim</td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.Referencia</td>
                                <td class="relat-capacitivo">@ViewBag.RefenciaFPC</td>
                                <td class="relat-fponta">@ViewBag.RefenciaFPI</td>
                                <td class="relat-ponta">@ViewBag.RefenciaFPI</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    @{
        if (ViewBag.showTabela)
        {
            <div class="page-break"></div>
            
            <div class="row">

                <br /><br />

                <div class="col-lg-12">
                    <div class="panel panel-default">
                        <table class="table no-margins tabela-grafico-pdf">
                            <thead>
                                <tr>
                                    <th class="relat-preto" style="width:20%; text-align:center; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Dia</th>
                                    <th class="relat-capacitivo" style="width:23%; text-align:center; font-size:12px;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                    <th class="relat-fponta" style="width:23%; text-align:center; font-size:12px;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                    <th class="relat-ponta" style="width:23%; text-align:center; font-size:12px;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                                </tr>
                            </thead>

                            <tbody>

                                @{
                                    var i = 0;
                                    var j = 0;
                                    var fatpotP_sim = "---";
                                    var fatpotFPI_sim = "---";
                                    var fatpotFPC_sim = "---";

                                    var NumDiasMes = ViewBag.NumDiasMes;

                                    for (i = 0; i < NumDiasMes; i++)
                                    {
                                        j = i + 1;

                                        fatpotFPC_sim = string.Format("{0:0.000}", ViewBag.FatPotCap_Sim[j]);
                                        fatpotFPI_sim = string.Format("{0:0.000}", ViewBag.FatPotInd_Sim[j]);
                                        fatpotP_sim = string.Format("{0:0.000}", ViewBag.FatPotP_Sim[j]);

                                        <tr class="tabela_valores" style="font-size:12px;">
                                            <td style="text-align:center; font-size:12px;" class="relat-preto">@ViewBag.Dias_Sim[j]</td>
                                            <td style="text-align:center; font-size:12px;" class="relat-capacitivo">@fatpotFPC_sim</td>
                                            <td style="text-align:center; font-size:12px;" class="relat-indutivo">@fatpotFPI_sim</td>
                                            <td style="text-align:center; font-size:12px;" class="relat-ponta">@fatpotP_sim</td>
                                        </tr>
                                    }
                                }

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }
    }

    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>

<script type="text/javascript">

    $(document).ready(function () {

        //
        // GRAFICO SIMULACAO
        //

        // fator de potencia
        var dataX_Sim = [];
        var labelX_Sim = [];
        var dataReferenciaFPC_Sim = [];
        var dataReferenciaFPI_Sim = [];
        var dataFatPotFPC_Sim = [];
        var dataFatPotFPI_Sim = [];
        var dataFatPotP_Sim = [];
        var dataFatPotUm_Sim = [];

        // copia valores
        var FatPotCap_Sim = @Html.Raw(Json.Encode(@ViewBag.FatPotCap_Sim));
        var FatPotInd_Sim = @Html.Raw(Json.Encode(@ViewBag.FatPotInd_Sim));
        var FatPotP_Sim = @Html.Raw(Json.Encode(@ViewBag.FatPotP_Sim));
        var Datas_Sim = @Html.Raw(Json.Encode(@ViewBag.Datas_Sim));;
        var NumDiasMes = @Html.Raw(Json.Encode(@ViewBag.NumDiasMes));
        var FatPotMin = @Html.Raw(Json.Encode(@ViewBag.FatPotMin));

        dataX_Sim.push('x');
        dataFatPotFPC_Sim.push(['data1']);
        dataFatPotFPI_Sim.push(['data2']);
        dataFatPotP_Sim.push(['data3']);
        dataReferenciaFPC_Sim.push(['data4']);
        dataReferenciaFPI_Sim.push(['data5']);
        dataFatPotUm_Sim.push(['data6']);

        var valorFatPotFPC_Sim, valorFatPotFPI_Sim, valorFatPotP_Sim;

        for (i = 0; i < NumDiasMes+2; i++)
        {
            // X
            dataX_Sim.push(Datas_Sim[i]);

            // linha de fatpot um
            dataFatPotUm_Sim.push([0.0]);

            // converte para nova escala
            if( FatPotCap_Sim[i] < 0 )
            {
                valorFatPotFPC_Sim = (1.0 - (-1.0*FatPotCap_Sim[i])) * -1.0;
            }
            else
            {
                valorFatPotFPC_Sim = (1.0 - FatPotCap_Sim[i]);
            }

            if( FatPotInd_Sim[i] < 0 )
            {
                valorFatPotFPI_Sim = (1.0 - (-1.0*FatPotInd_Sim[i])) * -1.0;
            }
            else
            {
                valorFatPotFPI_Sim = (1.0 - FatPotInd_Sim[i]);
            }

            if( FatPotP_Sim[i] < 0 )
            {
                valorFatPotP_Sim = (1.0 - (-1.0*FatPotP_Sim[i])) * -1.0;
            }
            else
            {
                valorFatPotP_Sim = (1.0 - FatPotP_Sim[i]);
            }

            dataFatPotFPC_Sim.push([valorFatPotFPC_Sim]);
            dataFatPotFPI_Sim.push([valorFatPotFPI_Sim]);
            dataFatPotP_Sim.push([valorFatPotP_Sim]);

            dataReferenciaFPC_Sim.push([-0.08]);
            dataReferenciaFPI_Sim.push([0.08]);
        }

        // prepara escala eixo Y
        var maximo = (1.0 - FatPotMin);
        var minimo = -1.0 * maximo;

        // valores label X
        labelX_Sim.push(Datas_Sim[1]);

        var ndias = (NumDiasMes < 25) ? NumDiasMes : 25;

        for (i = 5; i <= ndias; i+=5)
        {
            labelX_Sim.push(Datas_Sim[i]);
        }
        labelX_Sim.push(Datas_Sim[NumDiasMes]);

        var Data = [dataX_Sim,dataFatPotFPC_Sim,dataFatPotFPI_Sim,dataFatPotP_Sim,dataReferenciaFPC_Sim,dataReferenciaFPI_Sim,dataFatPotUm_Sim];

        c3.generate({
            bindto: '#fatpot-chart-simulacao',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 41
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'bar',
                    data3: 'bar',
                    data4: 'area',
                    data5: 'area',
                    data6: 'step'
                },
                order: null,
                colors: {
                    data1: '#1C84D6',
                    data2: '#007F00',
                    data3: '#FF0000',
                    data4: '#1C84C6',
                    data5: '#007F00',
                    data6: '#000000'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX_Sim,
                        format: function (x) {

                            // retorna dia
                            return x.getDate();
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    // Grafico 1 dia     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(11 horas)
                    padding: -1000*60*60*11
                },
                y:  {
                    max: maximo,
                    min: minimo,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            var valor;

                            // converte para nova escala
                            if( d < 0 )
                            {
                                valor = (1.0 - (-1.0*d)) * -1.0;
                            }
                            else
                            {
                                valor = (1.0 - d);
                            }

                            return valor.toFixed(3);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.15 },
            },
        });

        // labels
        $('.chart-legend').css("display", "block");

    });

</script>

}
    