﻿<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.UFER</title>

    <link href="@Server.MapPath("~/Content/bootstrap.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/style.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/plugins/c3/c3.min.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/Relatorios.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/RelatoriosPDF.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/fonts/font-awesome/css/font-awesome.min.css")" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="@Server.MapPath("~/Scripts/jquery-2.1.1.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/d3/d3.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/c3/c3.min.js")"></script>

    <style>

        td {
            font-size: 12px;
        }

        th {
            font-size: 12px;
        }

    </style>

</head>

@{
    string arquivo = "~/Logos/" + ViewBag._LogoConsultor;
    string logo = Server.MapPath(arquivo);
}

@if (ViewBag.Retorno == 2)
{   
    <div class="row">
        <div class="col-lg-12">
            <div class="linha cabecalho">
                <div class="coluna1">
                    <p><img src="@logo" style="height:42px; width:auto;" /></p>
                </div>

                <div class="coluna2">
                    <h3>@ViewBag.ClienteNome</h3>
                    @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                </div>

                <div class="coluna3">
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.UFER - @SmartEnergy.Resources.RelatoriosTexts.RelatAnual (@SmartEnergy.Resources.RelatoriosTexts.Simulacao)</h4>
                    <p>@ViewBag.DataTextoAtual</p>
                </div>
            </div>
        </div>
    </div>

    <br /><br /><br /><br /><br /><br />
    <div class="row">
        <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
            <i class="fa fa-warning fa-5x"></i>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>@SmartEnergy.Resources.RelatoriosTexts.NaoExistemRegistros</h2>
        </div>
    </div>
    <br /><br /><br /><br />

}
else
{

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="@logo" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.UFER - @SmartEnergy.Resources.RelatoriosTexts.RelatAnual (@SmartEnergy.Resources.RelatoriosTexts.Simulacao)</h4>
                        <p>@ViewBag.DataTextoAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <br />

        @{
            if (ViewBag.showGrafico)
            {
                <br /><br />
                <div class="row">
                    <div class="relat-grafico" style="height:430px; width:800px; margin-left:50px;">
                        <div id="grafico-UFER-simulacao" style="margin-top: -10px"></div>
                        <div class='chart-legend' style="display:none;">
                            <div class='legend-scale-dem'>
                                <ul class='legend-labels' style="margin-top: -25px;">
                                    <li><span style='background:#1C84C6 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                                    <li><span style='background:#007f00 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                                    <li><span style='background:#ff0000 !important;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }

        <hr />

        <div class="row" style="margin-top: -15px;">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:20%;"></th>
                                <th class="relat-capacitivo" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                                <th class="relat-preto" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.DashboardTexts.UFERTotalAno<br />&nbsp;</td>
                                <td class="relat-capacitivo">@ViewBag.UFER_Total_FPC_Sim @ViewBag.UnidadeUFER</td>
                                <td class="relat-fponta">@ViewBag.UFER_Total_FPI_Sim @ViewBag.UnidadeUFER</td>
                                <td class="relat-ponta">@ViewBag.UFER_Total_P_Sim @ViewBag.UnidadeUFER</td>
                                <td class="relat-preto">@ViewBag.UFER_Total_Sim @ViewBag.UnidadeUFER</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    @{
        if (ViewBag.showTabela)
        {
            <div class="row" style="margin-top: -15px;">
                <div class="col-lg-12">
                    <div class="panel panel-default">
                        <table class="table no-margins tabela-grafico-pdf">
                            <thead>
                                <tr>
                                    <th style="width:20%;">@SmartEnergy.Resources.RelatoriosTexts.Mes</th>
                                    <th style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo<br />(@ViewBag.UnidadeUFER)</th>
                                    <th style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo<br />(@ViewBag.UnidadeUFER)</th>
                                    <th style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta<br />(@ViewBag.UnidadeUFER)</th>
                                    <th style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Total<br />(@ViewBag.UnidadeUFER)</th>
                                </tr>
                            </thead>

                            <tbody>

                                @{
                                    var i = 0;
                                    var j = 0;
                                    var UFER_FPC_sim = "---";
                                    var UFER_FPI_sim = "---";
                                    var UFER_P_sim = "---";
                                    var UFER_T_sim = "---";

                                    for (i = 0; i < 12; i++)
                                    {
                                        j = i + 1;

                                        UFER_FPC_sim = string.Format("{0:#,##0.0}", ViewBag.UFER_FPC_Sim[j]);
                                        UFER_FPI_sim = string.Format("{0:#,##0.0}", ViewBag.UFER_FPI_Sim[j]);
                                        UFER_P_sim = string.Format("{0:#,##0.0}", ViewBag.UFER_P_Sim[j]);
                                        UFER_T_sim = string.Format("{0:#,##0.0}", ViewBag.UFER_P_Sim[j] + ViewBag.UFER_FPI_Sim[j] + ViewBag.UFER_FPC_Sim[j]);

                                        <tr class="tabela_valores">
                                            <td style="text-align:center;" class="relat-preto">@ViewBag.Meses[j]</td>
                                            <td style="text-align:center;" class="relat-capacitivo">@UFER_FPC_sim</td>
                                            <td style="text-align:center;" class="relat-fponta">@UFER_FPI_sim</td>
                                            <td style="text-align:center;" class="relat-ponta">@UFER_P_sim</td>
                                            <td style="text-align:center;" class="relat-preto">@UFER_T_sim</td>
                                        </tr>
                                    }
                                }

                            </tbody>
                        </table>
                    </div>

                </div>
            </div>

        }
    }

    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>

<script type="text/javascript">

    $(document).ready(function () {

        //
        // GRÁFICO SIMULAÇÃO
        //

        // UFER Simulacao
        var dataX_Sim = [];
        var labelX_Sim = [];
        var dataUFER_FPC_Sim = [];
        var dataUFER_FPI_Sim = [];
        var dataUFER_P_Sim = [];

        // copia valores
        var UFER_FPC_Sim = @Html.Raw(Json.Encode(@ViewBag.UFER_FPC_Sim));
        var UFER_FPI_Sim = @Html.Raw(Json.Encode(@ViewBag.UFER_FPI_Sim));
        var UFER_P_Sim = @Html.Raw(Json.Encode(@ViewBag.UFER_P_Sim));
        var Datas_Sim = @Html.Raw(Json.Encode(@ViewBag.Datas_Sim));
        var Meses = @Html.Raw(Json.Encode(@ViewBag.Meses));
        var Unidade = @Html.Raw(Json.Encode(@ViewBag.UnidadeUFER));

        var maximoUFER = Math.ceil(@ViewBag.UFERMaxGrafico);

        // verifico se nao tem dados e forco um limite
        if( maximoUFER == 0.0)
        {
            maximoUFER = 1.0;
        }

        dataX_Sim.push('x');
        dataUFER_FPC_Sim.push(['data1']);
        dataUFER_FPI_Sim.push(['data2']);
        dataUFER_P_Sim.push(['data3']);

        for (i = 0; i < 14; i++)
        {
            // X
            dataX_Sim.push(Datas_Sim[i]);

            dataUFER_FPC_Sim.push([UFER_FPC_Sim[i]]);
            dataUFER_FPI_Sim.push([UFER_FPI_Sim[i]]);
            dataUFER_P_Sim.push([UFER_P_Sim[i]]);
        }

        // valores label X
        for (i = 1; i <= 12; i++)
        {
            labelX_Sim.push(Datas_Sim[i]);
        }

        var Data = [dataX_Sim,dataUFER_P_Sim,dataUFER_FPI_Sim,dataUFER_FPC_Sim];

        c3.generate({

            bindto: '#grafico-UFER-simulacao',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'bar',
                    data3: 'bar',
                },
                groups: [
                            ['data1', 'data2', 'data3']
                ],
                order: null,
                colors: {
                    data1: '#1C84C6',
                    data2: '#007F00',
                    data3: '#FF0000'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX_Sim,
                        format: function (x) {

                            // mes
                            var mes = x.getMonth()+1;

                            // verifica se deve mostrar label
                            if(mes==1 || mes==4 || mes==7 || mes==10 || mes==12 )
                                return Meses[mes];

                            return "";
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    // Grafico 1 dia     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(11 horas)
                    // Grafico 1 mes     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(24 horas)*(13 dias)
                    padding: -1000*60*60*24*13
                },
                y:  {
                    max: maximoUFER,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoUFER < 100.0 )
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.7 },
            },
        });

        // labels
        $('.chart-legend').css("display", "block");

    });

</script>

}
    