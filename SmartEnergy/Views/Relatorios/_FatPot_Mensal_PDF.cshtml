﻿<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia</title>

    <link href="@Server.MapPath("~/Content/bootstrap.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/style.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/plugins/c3/c3.min.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/Relatorios.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/RelatoriosPDF.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/fonts/font-awesome/css/font-awesome.min.css")" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="@Server.MapPath("~/Scripts/jquery-2.1.1.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/d3/d3.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/c3/c3.min.js")"></script>

    <style>
        #fatpot-chart .c3-area {
            opacity: 0.3;
        }
    </style>

</head>

@{
    string arquivo = "~/Logos/" + ViewBag._LogoConsultor;
    string logo = Server.MapPath(arquivo);
}

@if (ViewBag.Retorno == 2)
{
    <div class="row">
        <div class="col-lg-12">
            <div class="linha cabecalho">
                <div class="coluna1">
                    <p><img src="@logo" style="height:42px; width:auto;" /></p>
                </div>

                <div class="coluna2">
                    <h3>@ViewBag.ClienteNome</h3>
                    @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                </div>

                <div class="coluna3">
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia - @SmartEnergy.Resources.RelatoriosTexts.RelatMensal</h4>
                    <p>@ViewBag.DataTextoAtual</p>
                </div>
            </div>
        </div>
    </div>

    <br /><br /><br /><br /><br /><br />
    <div class="row">
        <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
            <i class="fa fa-warning fa-5x"></i>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>@SmartEnergy.Resources.RelatoriosTexts.NaoExistemRegistros</h2>
        </div>
    </div>
    <br /><br /><br /><br />

}
else
{

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="@logo" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia - @SmartEnergy.Resources.RelatoriosTexts.RelatMensal</h4>
                        <p>@ViewBag.DataTextoAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <br />

        @{
            if (ViewBag.showGrafico)
            {
                <div class="row">
                    <div class="relat-grafico" style="height:490px; width:800px; margin-left:50px;">
                        <div id="fatpot-chart"></div>
                        <div class='chart-legend' style="display:none;">
                            <div class='legend-scale-dem'>
                                <ul class='legend-labels' style="margin-top: -25px;">
                                    <li><span style='background:#1C84C6 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                                    <li><span style='background:#007f00 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                                    <li><span style='background:#ff0000 !important;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }

        <hr />

        <div class="row" style="margin-top: -15px;">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisCapacitivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisCapFPC<br /><small>(@ViewBag.FatPot_MaisCapFPC_DataHora)</small></td>
                                <td class="relat-fponta">@ViewBag.FatPot_MaisCapFPI<br /><small>(@ViewBag.FatPot_MaisCapFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.FatPot_MaisCapP<br /><small>(@ViewBag.FatPot_MaisCapP_DataHora)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisIndFPC<br /><small>(@ViewBag.FatPot_MaisIndFPC_DataHora)</small></td>
                                <td class="relat-fponta">@ViewBag.FatPot_MaisIndFPI<br /><small>(@ViewBag.FatPot_MaisIndFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.FatPot_MaisIndP<br /><small>(@ViewBag.FatPot_MaisIndP_DataHora)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.NoPeriodo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_NoPeriodoFPC</td>
                                <td class="relat-fponta">@ViewBag.FatPot_NoPeriodoFPI</td>
                                <td class="relat-ponta">@ViewBag.FatPot_NoPeriodoP</td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.Referencia</td>
                                <td class="relat-capacitivo">@ViewBag.RefenciaFPC</td>
                                <td class="relat-fponta">@ViewBag.RefenciaFPI</td>
                                <td class="relat-ponta">@ViewBag.RefenciaFPI</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    @{
        if (ViewBag.showTabela)
        {
            <div class="page-break"></div>
            
            <div class="row">

                <br /><br />

                <div class="col-lg-12">
                    <div class="panel panel-default">
                        <table class="table no-margins tabela-grafico-pdf">
                            <thead>
                                <tr>
                                    <th class="relat-preto" style="width:20%; text-align:center; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Dia</th>
                                    <th class="relat-capacitivo" style="width:23%; text-align:center; font-size:12px;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                    <th class="relat-fponta" style="width:23%; text-align:center; font-size:12px;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                    <th class="relat-ponta" style="width:23%; text-align:center; font-size:12px;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                                </tr>
                            </thead>

                            <tbody>

                                @{
                                    var i = 0;
                                    var j = 0;
                                    var fatpotP = "---";
                                    var fatpotFPI = "---";
                                    var fatpotFPC = "---";

                                    var NumDiasMes = ViewBag.NumDiasMes;

                                    for (i = 0; i < NumDiasMes; i++)
                                    {
                                        j = i + 1;

                                        fatpotFPC = string.Format("{0:0.000}", ViewBag.FatPotCap[j]);
                                        fatpotFPI = string.Format("{0:0.000}", ViewBag.FatPotInd[j]);
                                        fatpotP = string.Format("{0:0.000}", ViewBag.FatPotP[j]);

                                        <tr class="tabela_valores" style="font-size:12px;">
                                            <td style="text-align:center; font-size:12px;" class="relat-preto">@ViewBag.Dias[j]</td>
                                            <td style="text-align:center; font-size:12px;" class="relat-capacitivo">@fatpotFPC</td>
                                            <td style="text-align:center; font-size:12px;" class="relat-indutivo">@fatpotFPI</td>
                                            <td style="text-align:center; font-size:12px;" class="relat-ponta">@fatpotP</td>
                                        </tr>
                                    }
                                }

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }
    }

    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>

<script type="text/javascript">

    $(document).ready(function () {

        // fator de potencia
        var dataX = [];
        var labelX = [];
        var dataReferenciaFPC = [];
        var dataReferenciaFPI = [];
        var dataFatPotFPC = [];
        var dataFatPotFPI = [];
        var dataFatPotP = [];
        var dataFatPotUm = [];

        // copia valores
        var FatPotCap = @Html.Raw(Json.Encode(@ViewBag.FatPotCap));
        var FatPotInd = @Html.Raw(Json.Encode(@ViewBag.FatPotInd));
        var FatPotP = @Html.Raw(Json.Encode(@ViewBag.FatPotP));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));
        var NumDiasMes = @Html.Raw(Json.Encode(@ViewBag.NumDiasMes));
        var FatPotMin = @Html.Raw(Json.Encode(@ViewBag.FatPotMin));

        dataX.push('x');
        dataFatPotFPC.push(['data1']);
        dataFatPotFPI.push(['data2']);
        dataFatPotP.push(['data3']);
        dataReferenciaFPC.push(['data4']);
        dataReferenciaFPI.push(['data5']);
        dataFatPotUm.push(['data6']);

        var valorFatPotFPC, valorFatPotFPI, valorFatPotP;

        for (i = 0; i < NumDiasMes+2; i++)
        {
            // X
            dataX.push(Datas[i]);

            // linha de fatpot um
            dataFatPotUm.push([0.0]);

            // converte para nova escala
            if( FatPotCap[i] < 0 )
            {
                valorFatPotFPC = (1.0 - (-1.0*FatPotCap[i])) * -1.0;
            }
            else
            {
                valorFatPotFPC = (1.0 - FatPotCap[i]);
            }

            if( FatPotInd[i] < 0 )
            {
                valorFatPotFPI = (1.0 - (-1.0*FatPotInd[i])) * -1.0;
            }
            else
            {
                valorFatPotFPI = (1.0 - FatPotInd[i]);
            }

            if( FatPotP[i] < 0 )
            {
                valorFatPotP = (1.0 - (-1.0*FatPotP[i])) * -1.0;
            }
            else
            {
                valorFatPotP = (1.0 - FatPotP[i]);
            }

            dataFatPotFPC.push([valorFatPotFPC]);
            dataFatPotFPI.push([valorFatPotFPI]);
            dataFatPotP.push([valorFatPotP]);

            dataReferenciaFPC.push([-0.08]);
            dataReferenciaFPI.push([0.08]);
        }

        // prepara escala eixo Y
        var maximo = (1.0 - FatPotMin);
        var minimo = -1.0 * maximo;

        // valores label X
        labelX.push(Datas[1]);

        var ndias = (NumDiasMes < 25) ? NumDiasMes : 25;

        for (i = 5; i <= ndias; i+=5)
        {
            labelX.push(Datas[i]);
        }
        labelX.push(Datas[NumDiasMes]);

        var Data = [dataX,dataFatPotFPC,dataFatPotFPI,dataFatPotP,dataReferenciaFPC,dataReferenciaFPI,dataFatPotUm];

        c3.generate({
            bindto: '#fatpot-chart',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 41
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'bar',
                    data3: 'bar',
                    data4: 'area',
                    data5: 'area',
                    data6: 'step'
                },
                order: null,
                colors: {
                    data1: '#1C84D6',
                    data2: '#007F00',
                    data3: '#FF0000',
                    data4: '#1C84C6',
                    data5: '#007F00',
                    data6: '#000000'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // retorna dia
                            return x.getDate();
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    // Grafico 1 dia     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(11 horas)
                    padding: -1000*60*60*11
                },
                y:  {
                    max: maximo,
                    min: minimo,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            var valor;

                            // converte para nova escala
                            if( d < 0 )
                            {
                                valor = (1.0 - (-1.0*d)) * -1.0;
                            }
                            else
                            {
                                valor = (1.0 - d);
                            }

                            return valor.toFixed(3);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.15 },
            }
        });

        // labels
        $('.chart-legend').css("display", "block");

    });

</script>

}
    