﻿
<div class="row">
    <div class="col-lg-4 col-md-12 relatorio_real">
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.DashboardTexts.UFERTotalAno</h5>
                        <h1>@ViewBag.UFER_Total_FPC <span>@ViewBag.UnidadeUFER</span></h1>
                        <h6>@ViewBag.DataTextoAtual</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.DashboardTexts.UFERTotalAno</h5>
                        <h1>@ViewBag.UFER_Total_FPI <span>@ViewBag.UnidadeUFER</span></h1>
                        <h6>@ViewBag.DataTextoAtual</h6>
                    </div>
                </div>
            </div>
        </div>
        <br /><br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.DashboardTexts.UFERTotalAno</h5>
                        <h1>@ViewBag.UFER_Total_P <span>@ViewBag.UnidadeUFER</span></h1>
                        <h6>@ViewBag.DataTextoAtual</h6>
                    </div>
                </div>
            </div>
        </div>
        <br /><br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-preto">
                        <h5>@SmartEnergy.Resources.DashboardTexts.UFERTotalAno</h5>
                        <h1>@ViewBag.UFER_Total <span>@ViewBag.UnidadeUFER</span></h1>
                        <h6>@ViewBag.DataTextoAtual</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-12 simulacao" style="display:none;">
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.DashboardTexts.UFERTotalAno</h5>
                        <h1>@ViewBag.UFER_Total_FPC_Sim <span>@ViewBag.UnidadeUFER</span></h1>
                        <h6>@ViewBag.DataTextoAtual</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.DashboardTexts.UFERTotalAno</h5>
                        <h1>@ViewBag.UFER_Total_FPI_Sim <span>@ViewBag.UnidadeUFER</span></h1>
                        <h6>@ViewBag.DataTextoAtual</h6>
                    </div>
                </div>
            </div>
        </div>
        <br /><br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.DashboardTexts.UFERTotalAno</h5>
                        <h1>@ViewBag.UFER_Total_P_Sim <span>@ViewBag.UnidadeUFER</span></h1>
                        <h6>@ViewBag.DataTextoAtual</h6>
                    </div>
                </div>
            </div>
        </div>
        <br /><br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-preto">
                        <h5>@SmartEnergy.Resources.DashboardTexts.UFERTotalAno</h5>
                        <h1>@ViewBag.UFER_Total_Sim <span>@ViewBag.UnidadeUFER</span></h1>
                        <h6>@ViewBag.DataTextoAtual</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-8 col-md-12">
        <div class="row">
            <div class="relat-grafico">
                <div class="relatorio_real" id="grafico-UFER" style="margin-top: -10px"></div>
                <div class="simulacao" id="grafico-UFER-simulacao" style="margin-top: -10px; display:none;"></div>
                <div class='chart-legend' style="display:none;">
                    <div class='legend-scale-dem'>
                        <ul class='legend-labels' style="margin-top: -25px;">
                            <li><span style='background:#1C84C6;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                            <li><span style='background:#007f00;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                            <li><span style='background:#ff0000;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@Html.Partial("_Relat_Botoes")

<br />
<div class="panel panel-title relatorio">
    <div class="panel-body">

        <div class="row">
            <div class="col-lg-12 relatorio_real">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:20%;"></th>
                                <th class="relat-capacitivo" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                                <th class="relat-preto" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.DashboardTexts.UFERTotalAno<br />&nbsp;</td>
                                <td class="relat-capacitivo">@ViewBag.UFER_Total_FPC @ViewBag.UnidadeUFER</td>
                                <td class="relat-fponta">@ViewBag.UFER_Total_FPI @ViewBag.UnidadeUFER</td>
                                <td class="relat-ponta">@ViewBag.UFER_Total_P @ViewBag.UnidadeUFER</td>
                                <td class="relat-preto">@ViewBag.UFER_Total @ViewBag.UnidadeUFER</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div class="col-lg-12 simulacao" style="display:none;">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:20%;"></th>
                                <th class="relat-capacitivo" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                                <th class="relat-preto" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.DashboardTexts.UFERTotalAno<br />&nbsp;</td>
                                <td class="relat-capacitivo">@ViewBag.UFER_Total_FPC_Sim @ViewBag.UnidadeUFER</td>
                                <td class="relat-fponta">@ViewBag.UFER_Total_FPI_Sim @ViewBag.UnidadeUFER</td>
                                <td class="relat-ponta">@ViewBag.UFER_Total_P_Sim @ViewBag.UnidadeUFER</td>
                                <td class="relat-preto">@ViewBag.UFER_Total_Sim @ViewBag.UnidadeUFER</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>

<div class="modal inmodal animated fadeIn" id="modalTabela" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-editar-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">@SmartEnergy.Resources.ComumTexts.BotaoFechar</span></button>
                <h4 class="modal-title"><i class="fa fa-list"></i>&nbsp;&nbsp;@SmartEnergy.Resources.RelatoriosTexts.TabelaRegistros</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-default relatorio_real" style="display:none;">
                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:20%;">@SmartEnergy.Resources.RelatoriosTexts.Mes</th>
                                <th style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo<br />(@ViewBag.UnidadeUFER)</th>
                                <th style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo<br />(@ViewBag.UnidadeUFER)</th>
                                <th style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta<br />(@ViewBag.UnidadeUFER)</th>
                                <th style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Total<br />(@ViewBag.UnidadeUFER)</th>
                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var i = 0;
                                var j = 0;
                                var UFER_FPC = "---";
                                var UFER_FPI = "---";
                                var UFER_P = "---";
                                var UFER_T = "---";

                                for (i = 0; i < 12; i++)
                                {
                                    j = i + 1;

                                    UFER_FPC = string.Format("{0:#,##0.0}", ViewBag.UFER_FPC[j]);
                                    UFER_FPI = string.Format("{0:#,##0.0}", ViewBag.UFER_FPI[j]);
                                    UFER_P = string.Format("{0:#,##0.0}", ViewBag.UFER_P[j]);
                                    UFER_T = string.Format("{0:#,##0.0}", ViewBag.UFER_P[j] + ViewBag.UFER_FPI[j] + ViewBag.UFER_FPC[j]);

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="relat-preto">@ViewBag.Meses[j]</td>
                                        <td style="text-align:center;" class="relat-capacitivo">@UFER_FPC</td>
                                        <td style="text-align:center;" class="relat-fponta">@UFER_FPI</td>
                                        <td style="text-align:center;" class="relat-ponta">@UFER_P</td>
                                        <td style="text-align:center;" class="relat-preto">@UFER_T</td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>

                <div class="panel panel-default simulacao" style="display:none;">
                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:20%;">@SmartEnergy.Resources.RelatoriosTexts.Mes</th>
                                <th style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo<br />(@ViewBag.UnidadeUFER)</th>
                                <th style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo<br />(@ViewBag.UnidadeUFER)</th>
                                <th style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta<br />(@ViewBag.UnidadeUFER)</th>
                                <th style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Total<br />(@ViewBag.UnidadeUFER)</th>
                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var UFER_FPC_sim = "---";
                                var UFER_FPI_sim = "---";
                                var UFER_P_sim = "---";
                                var UFER_T_sim = "---";

                                for (i = 0; i < 12; i++)
                                {
                                    j = i + 1;

                                    UFER_FPC_sim = string.Format("{0:#,##0.0}", ViewBag.UFER_FPC_Sim[j]);
                                    UFER_FPI_sim = string.Format("{0:#,##0.0}", ViewBag.UFER_FPI_Sim[j]);
                                    UFER_P_sim = string.Format("{0:#,##0.0}", ViewBag.UFER_P_Sim[j]);
                                    UFER_T_sim = string.Format("{0:#,##0.0}", ViewBag.UFER_P_Sim[j] + ViewBag.UFER_FPI_Sim[j] + ViewBag.UFER_FPC_Sim[j]);

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="relat-preto">@ViewBag.Meses[j]</td>
                                        <td style="text-align:center;" class="relat-capacitivo">@UFER_FPC_sim</td>
                                        <td style="text-align:center;" class="relat-fponta">@UFER_FPI_sim</td>
                                        <td style="text-align:center;" class="relat-ponta">@UFER_P_sim</td>
                                        <td style="text-align:center;" class="relat-preto">@UFER_T_sim</td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
            </div>
        </div>
    </div>
</div>


@Html.Partial("_EventosModal")

@Html.Partial("_ObservacoesModal")


<script type="text/javascript">

    $(document).ready(function () {

        // UFER
        var dataX = [];
        var labelX = [];
        var dataUFER_FPC = [];
        var dataUFER_FPI = [];
        var dataUFER_P = [];

        // copia valores
        var UFER_FPC = @Html.Raw(Json.Encode(@ViewBag.UFER_FPC));
        var UFER_FPI = @Html.Raw(Json.Encode(@ViewBag.UFER_FPI));
        var UFER_P = @Html.Raw(Json.Encode(@ViewBag.UFER_P));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));
        var Meses = @Html.Raw(Json.Encode(@ViewBag.Meses));
        var Unidade = @Html.Raw(Json.Encode(@ViewBag.UnidadeUFER));

        var maximoUFER = Math.ceil(@ViewBag.UFERMaxGrafico);

        // verifico se nao tem dados e forco um limite
        if( maximoUFER == 0.0)
        {
            maximoUFER = 1.0;
        }

        dataX.push('x');
        dataUFER_FPC.push(['data1']);
        dataUFER_FPI.push(['data2']);
        dataUFER_P.push(['data3']);

        for (i = 0; i < 14; i++)
        {
            // X
            dataX.push(Datas[i]);

            dataUFER_FPC.push([UFER_FPC[i]]);
            dataUFER_FPI.push([UFER_FPI[i]]);
            dataUFER_P.push([UFER_P[i]]);
        }

        // valores label X
        for (i = 1; i <= 12; i++)
        {
            labelX.push(Datas[i]);
        }

        var Data = [dataX,dataUFER_P,dataUFER_FPI,dataUFER_FPC];

        var chart = c3.generate({

            bindto: '#grafico-UFER',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'bar',
                    data3: 'bar',
                },
                groups: [
                            ['data1', 'data2', 'data3']
                ],
                order: null,
                colors: {
                    data1: '#1C84C6',
                    data2: '#007F00',
                    data3: '#FF0000'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // mes
                            var mes = x.getMonth()+1;

                            // verifica se deve mostrar label
                            if(mes==1 || mes==4 || mes==7 || mes==10 || mes==12 )
                                return Meses[mes];

                            return "";
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    // Grafico 1 dia     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(11 horas)
                    // Grafico 1 mes     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(24 horas)*(13 dias)
                    padding: -1000*60*60*24*13
                },
                y:  {
                    max: maximoUFER,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoUFER < 100.0 )
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.7 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < 4; i++) {

                        if (! text) {

                            // split string and create array.
                            var arr = Datas[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            title = Meses[data.getMonth()+1];

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // verifica se barra
                        if( i == 0 )
                        {
                            name = FPONTA_CAP;
                            value = d[2].value;
                            bgcolor = '#1C84C6';
                        }

                        if( i == 1 )
                        {
                            name = FPONTA_IND;
                            value = d[1].value;
                            bgcolor = '#007F00';
                        }

                        if( i == 2 )
                        {
                            name = PONTA;
                            value = d[0].value;
                            bgcolor = '#FF0000';
                        }

                        if( i == 3 )
                        {
                            name = 'Total';
                            value = d[0].value + d[1].value + d[2].value;
                            bgcolor = '#FFFFFF';
                        }

                        value = value.toFixed(1).replace('.', ',');

                        text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                        text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                        text += "<td class='value'>" + value + " " + Unidade +"</td>";
                        text += "</tr>";
                    }
                    return text + "</table>";
                }
            }
        });

        //
        // GRÁFICO SIMULAÇÃO
        //

        // UFER Simulação
        var dataX_Sim = [];
        var labelX_Sim = [];
        var dataUFER_FPC_Sim = [];
        var dataUFER_FPI_Sim = [];
        var dataUFER_P_Sim = [];

        // copia valores
        var UFER_FPC_Sim = @Html.Raw(Json.Encode(@ViewBag.UFER_FPC_Sim));
        var UFER_FPI_Sim = @Html.Raw(Json.Encode(@ViewBag.UFER_FPI_Sim));
        var UFER_P_Sim = @Html.Raw(Json.Encode(@ViewBag.UFER_P_Sim));
        var Datas_Sim = @Html.Raw(Json.Encode(@ViewBag.Datas_Sim));

        dataX_Sim.push('x');
        dataUFER_FPC_Sim.push(['data1']);
        dataUFER_FPI_Sim.push(['data2']);
        dataUFER_P_Sim.push(['data3']);

        for (i = 0; i < 14; i++)
        {
            // X
            dataX_Sim.push(Datas[i]);

            dataUFER_FPC_Sim.push([UFER_FPC_Sim[i]]);
            dataUFER_FPI_Sim.push([UFER_FPI_Sim[i]]);
            dataUFER_P_Sim.push([UFER_P_Sim[i]]);
        }

        // valores label X
        for (i = 1; i <= 12; i++)
        {
            labelX_Sim.push(Datas_Sim[i]);
        }

        var Data = [dataX_Sim,dataUFER_P_Sim,dataUFER_FPI_Sim,dataUFER_FPC_Sim];

        var chart_sim = c3.generate({

            bindto: '#grafico-UFER-simulacao',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'bar',
                    data3: 'bar',
                },
                groups: [
                            ['data1', 'data2', 'data3']
                ],
                order: null,
                colors: {
                    data1: '#1C84C6',
                    data2: '#007F00',
                    data3: '#FF0000'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX_Sim,
                        format: function (x) {

                            // mes
                            var mes = x.getMonth()+1;

                            // verifica se deve mostrar label
                            if(mes==1 || mes==4 || mes==7 || mes==10 || mes==12 )
                                return Meses[mes];

                            return "";
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    // Grafico 1 dia     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(11 horas)
                    // Grafico 1 mes     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(24 horas)*(13 dias)
                    padding: -1000*60*60*24*13
                },
                y:  {
                    max: maximoUFER,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoUFER < 100.0 )
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.7 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < 4; i++) {

                        if (! text) {

                            // split string and create array.
                            var arr = Datas[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            title = Meses[data.getMonth()+1];

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // verifica se barra
                        if( i == 0 )
                        {
                            name = FPONTA_CAP;
                            value = d[2].value;
                            bgcolor = '#1C84C6';
                        }

                        if( i == 1 )
                        {
                            name = FPONTA_IND;
                            value = d[1].value;
                            bgcolor = '#007F00';
                        }

                        if( i == 2 )
                        {
                            name = PONTA;
                            value = d[0].value;
                            bgcolor = '#FF0000';
                        }

                        if( i == 3 )
                        {
                            name = 'Total';
                            value = d[0].value + d[1].value + d[2].value;
                            bgcolor = '#FFFFFF';
                        }

                        value = value.toFixed(1).replace('.', ',');

                        text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                        text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + " - Simulação" + "</td>";
                        text += "<td class='value'>" + value + " " + Unidade +"</td>";
                        text += "</tr>";
                    }
                    return text + "</table>";
                }
            }
        });

        // salva chart 
        $('#grafico-UFER').data('c3-chart', chart);

        // salva chart simulacao
        $('#grafico-UFER-simulacao').data('c3-chart', chart_sim);

        // labels
        $('.chart-legend').css("display", "block");

        // verifica se apresenta simulacao
        ShowSimulacao();

        $(".modal-dialog").draggable({
            handle: ".modal-editar-header"
        });
    });

    function ShowSimulacao () {

        // IDSimulacaoCenario
        var IDSimulacaoCenario = parseInt(@ViewBag._IDSimulacaoCenario);

        // verifica se ano existe nenhuma simulacao selecionada
        if (IDSimulacaoCenario == 0)
        {
            $("#aplica_simula").attr('disabled', true);
            $('#aplica_simula').prop('checked', false);
        }
        else
        {
            $("#aplica_simula").attr('disabled', false);
        }

        // grafico UFER
        var chart = $('#grafico-UFER').data('c3-chart');

        // grafico UFER simulacao
        var chart_sim = $('#grafico-UFER-simulacao').data('c3-chart');

        // checkbox aplica simulacao
        var simula = $('#aplica_simula').prop('checked');

        // verifica se ano existe nenhuma simulacao selecionada
        if (simula)
        {
            $('.simulacao').css("display", "block");
            $('.relatorio_real').css("display", "none");
            chart_sim.show();
        }
        else
        {
            $('.simulacao').css("display", "none");
            $('.relatorio_real').css("display", "block");
            chart.show();
        }

        // salva em cookie
        setCookie("AplicaSimulacao", simula, null);
    }

</script>

    