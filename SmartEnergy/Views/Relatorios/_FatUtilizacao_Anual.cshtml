﻿
<div class="row">
    <div class="col-lg-4 col-md-12 relatorio_real">
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.MaximoAno</h5>
                        <h1>@ViewBag.FatUtilizacao_MaxFPC <span>%</span></h1>
                        <h6>@ViewBag.FatUtilizacao_MaxFPC_DataHora</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.MaximoAno</h5>
                        <h1>@ViewBag.FatUtilizacao_MaxFPI <span>%</span></h1>
                        <h6>@ViewBag.FatUtilizacao_MaxFPI_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.MaximoAno</h5>
                        <h1>@ViewBag.FatUtilizacao_MaxP <span>%</span></h1>
                        <h6>@ViewBag.FatUtilizacao_MaxP_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-12 simulacao" style="display:none;">
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.MaximoAno</h5>
                        <h1>@ViewBag.FatUtilizacao_MaxFPC_Sim <span>%</span></h1>
                        <h6>@ViewBag.FatUtilizacao_MaxFPC_DataHora_Sim</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.MaximoAno</h5>
                        <h1>@ViewBag.FatUtilizacao_MaxFPI_Sim <span>%</span></h1>
                        <h6>@ViewBag.FatUtilizacao_MaxFPI_DataHora_Sim</h6>
                    </div>
                </div>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.MaximoAno</h5>
                        <h1>@ViewBag.FatUtilizacao_MaxP_Sim <span>%</span></h1>
                        <h6>@ViewBag.FatUtilizacao_MaxP_DataHora_Sim</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-8 col-md-12">
        <div class="row">
            <div class="relat-grafico">
                <div class="relatorio_real" id="fatutilizacao-chart" style="margin-top: -10px"></div>
                <div class="simulacao" id="fatutilizacao-chart-simulacao" style="margin-top: -10px; display:none;"></div>
                <div class='chart-legend' style="display:none;">
                    <div class='legend-scale-dem'>
                        <ul class='legend-labels' style="margin-top: -25px;">
                            <li><span style='background:#1C84C6;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                            <li><span style='background:#007f00;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                            <li><span style='background:#ff0000;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@Html.Partial("_Relat_Botoes")

<br />
<div class="panel panel-title relatorio">
    <div class="panel-body">

        <div class="row">
            <div class="col-lg-12">
                <div class="relat-tabela relatorio_real">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao @SmartEnergy.Resources.RelatoriosTexts.Maximo</td>
                                <td class="relat-capacitivo">@ViewBag.FatUtilizacao_MaxFPC %<br /><small>(@ViewBag.FatUtilizacao_MaxFPC_DataHora)</small></td>
                                <td class="relat-fponta">@ViewBag.FatUtilizacao_MaxFPI %<br /><small>(@ViewBag.FatUtilizacao_MaxFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.FatUtilizacao_MaxP %<br /><small>(@ViewBag.FatUtilizacao_MaxP_DataHora)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao @SmartEnergy.Resources.RelatoriosTexts.Minimo</td>
                                <td class="relat-capacitivo">@ViewBag.FatUtilizacao_MinFPC %<br /><small>(@ViewBag.FatUtilizacao_MinFPC_DataHora)</small></td>
                                <td class="relat-fponta">@ViewBag.FatUtilizacao_MinFPI %<br /><small>(@ViewBag.FatUtilizacao_MinFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.FatUtilizacao_MinP %<br /><small>(@ViewBag.FatUtilizacao_MinP_DataHora)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao @SmartEnergy.Resources.RelatoriosTexts.Periodo</td>
                                <td class="relat-capacitivo">@ViewBag.FatUtilizacao_MedFPC %<br /><small>&nbsp;</small></td>
                                <td class="relat-fponta">@ViewBag.FatUtilizacao_MedFPI %<br /><small>&nbsp;</small></td>
                                <td class="relat-ponta">@ViewBag.FatUtilizacao_MedP %</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="relat-tabela simulacao" style="display:none;">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao @SmartEnergy.Resources.RelatoriosTexts.Maximo</td>
                                <td class="relat-capacitivo">@ViewBag.FatUtilizacao_MaxFPC_Sim %<br /><small>(@ViewBag.FatUtilizacao_MaxFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.FatUtilizacao_MaxFPI_Sim %<br /><small>(@ViewBag.FatUtilizacao_MaxFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.FatUtilizacao_MaxP_Sim %<br /><small>(@ViewBag.FatUtilizacao_MaxP_DataHora_Sim)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao @SmartEnergy.Resources.RelatoriosTexts.Minimo</td>
                                <td class="relat-capacitivo">@ViewBag.FatUtilizacao_MinFPC_Sim %<br /><small>(@ViewBag.FatUtilizacao_MinFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.FatUtilizacao_MinFPI_Sim %<br /><small>(@ViewBag.FatUtilizacao_MinFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.FatUtilizacao_MinP_Sim %<br /><small>(@ViewBag.FatUtilizacao_MinP_DataHora_Sim)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao @SmartEnergy.Resources.RelatoriosTexts.Periodo</td>
                                <td class="relat-capacitivo">@ViewBag.FatUtilizacao_MedFPC_Sim %<br /><small>&nbsp;</small></td>
                                <td class="relat-fponta">@ViewBag.FatUtilizacao_MedFPI_Sim %<br /><small>&nbsp;</small></td>
                                <td class="relat-ponta">@ViewBag.FatUtilizacao_MedP_Sim %</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>
</div>

<div class="modal inmodal animated fadeIn" id="modalTabela" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-editar-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title"><i class="fa fa-list"></i>&nbsp;&nbsp;@SmartEnergy.Resources.RelatoriosTexts.TabelaRegistros</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-default relatorio_real">
                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Mes</th>
                                <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo<br />(%)</th>
                                <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo<br />(%)</th>
                                <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta<br />(%)</th>
                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var i = 0;
                                var j = 0;
                                var FatUtilizacaoFPC = "---";
                                var FatUtilizacaoFPI = "---";
                                var FatUtilizacaoP = "---";

                                var NumDiasMes = ViewBag.NumDiasMes;

                                for (i = 0; i < 12; i++)
                                {
                                    j = i + 1;

                                    FatUtilizacaoFPC = string.Format("{0:#,##0.0}", ViewBag.FatUtilizacaoFPC[j]);
                                    FatUtilizacaoFPI = string.Format("{0:#,##0.0}", ViewBag.FatUtilizacaoFPI[j]);
                                    FatUtilizacaoP = string.Format("{0:#,##0.0}", ViewBag.FatUtilizacaoP[j]);

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="relat-preto">@ViewBag.Meses[j]</td>
                                        <td style="text-align:center;" class="relat-capacitivo">@FatUtilizacaoFPC</td>
                                        <td style="text-align:center;" class="relat-fponta">@FatUtilizacaoFPI</td>
                                        <td style="text-align:center;" class="relat-ponta">@FatUtilizacaoP</td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>

                <div class="panel panel-default simulacao" style="display:none;">
                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Mes</th>
                                <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo<br />(%)</th>
                                <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo<br />(%)</th>
                                <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta<br />(%)</th>
                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var FatUtilizacaoFPC_sim = "---";
                                var FatUtilizacaoFPI_sim = "---";
                                var FatUtilizacaoP_sim = "---";

                                for (i = 0; i < 12; i++)
                                {
                                    j = i + 1;

                                    FatUtilizacaoFPC_sim = string.Format("{0:#,##0.0}", ViewBag.FatUtilizacaoFPC_Sim[j]);
                                    FatUtilizacaoFPI_sim = string.Format("{0:#,##0.0}", ViewBag.FatUtilizacaoFPI_Sim[j]);
                                    FatUtilizacaoP_sim = string.Format("{0:#,##0.0}", ViewBag.FatUtilizacaoP_Sim[j]);

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="relat-preto">@ViewBag.Meses[j]</td>
                                        <td style="text-align:center;" class="relat-capacitivo">@FatUtilizacaoFPC_sim</td>
                                        <td style="text-align:center;" class="relat-fponta">@FatUtilizacaoFPI_sim</td>
                                        <td style="text-align:center;" class="relat-ponta">@FatUtilizacaoP_sim</td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
            </div>
        </div>
    </div>
</div>


@Html.Partial("_EventosModal")

@Html.Partial("_ObservacoesModal")


<script type="text/javascript">

    $(document).ready(function () {

        // fator de utilizacao
        var dataX = [];
        var labelX = [];
        var dataFatUtilizacaoP = [];
        var dataFatUtilizacaoFPI = [];
        var dataFatUtilizacaoFPC = [];

        // copia valores
        var FatUtilizacaoP = @Html.Raw(Json.Encode(@ViewBag.FatUtilizacaoP));
        var FatUtilizacaoFPI = @Html.Raw(Json.Encode(@ViewBag.FatUtilizacaoFPI));
        var FatUtilizacaoFPC = @Html.Raw(Json.Encode(@ViewBag.FatUtilizacaoFPC));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));
        var Meses = @Html.Raw(Json.Encode(@ViewBag.Meses));

        var maximoFatUtilizacao = @Html.Raw(Json.Encode(@ViewBag.FatUtilizacaoMaxGrafico));

        // verifico se nao tem dados e forco um limite
        if( maximoFatUtilizacao == 0.0)
        {
            maximoFatUtilizacao = 100.0;
        }
        
        dataX.push('x');
        dataFatUtilizacaoFPC.push(['data1']);
        dataFatUtilizacaoFPI.push(['data2']);
        dataFatUtilizacaoP.push(['data3']);

        for (i = 0; i < 14; i++)
        {
            // X
            dataX.push(Datas[i]);

            dataFatUtilizacaoFPC.push([FatUtilizacaoFPC[i]]);
            dataFatUtilizacaoFPI.push([FatUtilizacaoFPI[i]]);
            dataFatUtilizacaoP.push([FatUtilizacaoP[i]]);
        }

        // valores label X
        for (i = 1; i <= 12; i++)
        {
            labelX.push(Datas[i]);
        }

        var Data = [dataX,dataFatUtilizacaoFPC,dataFatUtilizacaoFPI,dataFatUtilizacaoP];

        var chart = c3.generate({

            bindto: '#fatutilizacao-chart',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'bar',
                    data3: 'bar',
                },
                colors: {
                    data1: '#1C84C6',
                    data2: '#007F00',
                    data3: '#FF0000',
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // mes
                            var mes = x.getMonth()+1;

                            // verifica se deve mostrar label
                            if(mes==1 || mes==4 || mes==7 || mes==10 || mes==12 )
                                return Meses[mes];

                            return "";
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    // Grafico 1 dia     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(11 horas)
                    // Grafico 1 mes     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(24 horas)*(13 dias)
                    padding: -1000*60*60*24*13
                },
                y:  {
                    max: maximoFatUtilizacao,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoFatUtilizacao < 10)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.7 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < 3; i++) {

                        if (! text) {

                            // split string and create array.
                            var arr = Datas[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            title = Meses[data.getMonth()+1];

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // nome
                        switch(i)
                        {
                            case 0:
                                name = FPONTA_CAP;
                                value = FatUtilizacaoFPC[d[0].index].toFixed(1);
                                bgcolor = '#1C84C6';
                                break;

                            case 1:
                                name = FPONTA_IND;
                                value = FatUtilizacaoFPI[d[0].index].toFixed(1);
                                bgcolor = '#007F00';
                                break;

                            case 2:
                                name = PONTA;
                                value = FatUtilizacaoP[d[0].index].toFixed(1);
                                bgcolor = '#FF0000'
                                break;
                        }

                        value = value.replace('.', ',');

                        text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                        text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                        text += "<td class='value'>" + value + " %</td>";
                        text += "</tr>";
                    }
                    return text + "</table>";
                }
            }
        });

        // 
        // GRAFICO SIMULACAO
        //

        // fator de utilizacao
        var dataX_Sim = [];
        var labelX_Sim = [];
        var dataFatUtilizacaoP_Sim = [];
        var dataFatUtilizacaoFPI_Sim = [];
        var dataFatUtilizacaoFPC_Sim = [];

        // copia valores
        var FatUtilizacaoP_Sim = @Html.Raw(Json.Encode(@ViewBag.FatUtilizacaoP_Sim));
        var FatUtilizacaoFPI_Sim = @Html.Raw(Json.Encode(@ViewBag.FatUtilizacaoFPI_Sim));
        var FatUtilizacaoFPC_Sim = @Html.Raw(Json.Encode(@ViewBag.FatUtilizacaoFPC_Sim));
        var Datas_Sim = @Html.Raw(Json.Encode(@ViewBag.Datas_Sim));

        dataX_Sim.push('x');
        dataFatUtilizacaoFPC_Sim.push(['data1']);
        dataFatUtilizacaoFPI_Sim.push(['data2']);
        dataFatUtilizacaoP_Sim.push(['data3']);

        for (i = 0; i < 14; i++)
        {            
            // X
            dataX_Sim.push(Datas_Sim[i]);

            dataFatUtilizacaoFPC_Sim.push([FatUtilizacaoFPC_Sim[i]]);
            dataFatUtilizacaoFPI_Sim.push([FatUtilizacaoFPI_Sim[i]]);
            dataFatUtilizacaoP_Sim.push([FatUtilizacaoP_Sim[i]]);
        }

        // valores label X
        for (i = 1; i <= 12; i++)
        {
            labelX_Sim.push(Datas_Sim[i]);
        }

        var Data = [dataX_Sim,dataFatUtilizacaoFPC_Sim,dataFatUtilizacaoFPI_Sim,dataFatUtilizacaoP_Sim];

        var chart_sim = c3.generate({

            bindto: '#fatutilizacao-chart-simulacao',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'bar',
                    data3: 'bar',
                },
                colors: {
                    data1: '#1C84C6',
                    data2: '#007F00',
                    data3: '#FF0000',
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX_Sim,
                        format: function (x) {

                            // mes
                            var mes = x.getMonth()+1;

                            // verifica se deve mostrar label
                            if(mes==1 || mes==4 || mes==7 || mes==10 || mes==12 )
                                return Meses[mes];

                            return "";
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    // Grafico 1 dia     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(11 horas)
                    // Grafico 1 mes     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(24 horas)*(13 dias)
                    padding: -1000*60*60*24*13
                },
                y:  {
                    max: maximoFatUtilizacao,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoFatUtilizacao < 10)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.7 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < 3; i++) {

                        if (! text) {

                            // split string and create array.
                            var arr = Datas_Sim[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            title = Meses[data.getMonth()+1];

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // nome
                        switch(i)
                        {
                            case 0:
                                name = FPONTA_CAP;
                                value = FatUtilizacaoFPC_Sim[d[0].index].toFixed(1);
                                bgcolor = '#1C84C6';
                                break;

                            case 1:
                                name = FPONTA_IND;
                                value = FatUtilizacaoFPI_Sim[d[0].index].toFixed(1);
                                bgcolor = '#007F00';
                                break;

                            case 2:
                                name = PONTA;
                                value = FatUtilizacaoP_Sim[d[0].index].toFixed(1);
                                bgcolor = '#FF0000'
                                break;
                        }

                        value = value.replace('.', ',');

                        text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                        text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + " - Simulação" + "</td>";
                        text += "<td class='value'>" + value + " %</td>";
                        text += "</tr>";
                    }
                    return text + "</table>";
                }
            }
        });

        // salva chart 
        $('#fatutilizacao-chart').data('c3-chart', chart)

        // salva chart simulacao
        $('#fatutilizacao-chart-simulacao').data('c3-chart', chart_sim)

        // labels
        $('.chart-legend').css("display", "block");

        ShowSimulacao();

        $(".modal-dialog").draggable({
            handle: ".modal-editar-header"
        });
    });


    function ShowSimulacao () {

        // IDSimulacaoCenario
        var IDSimulacaoCenario = parseInt(@ViewBag._IDSimulacaoCenario);

        // verifica se ano existe nenhuma simulacao selecionada
        if (IDSimulacaoCenario == 0)
        {
            $("#aplica_simula").attr('disabled', true);
            $('#aplica_simula').prop('checked', false);
        }
        else
        {
            $("#aplica_simula").attr('disabled', false);
        }

        // grafico fator de uitlizacao
        var chart = $('#fatutilizacao-chart').data('c3-chart');

        // grafico fator de uitlizacao simulacao
        var chart_sim = $('#fatutilizacao-chart-simulacao').data('c3-chart');

        // checkbox aplica simulacao
        var simula = $('#aplica_simula').prop('checked');

        // verifica se ano existe nenhuma simulacao selecionada
        if (simula)
        {
            $('.simulacao').css("display", "block");
            $('.relatorio_real').css("display", "none");
            chart_sim.show();
        }
        else
        {
            $('.simulacao').css("display", "none");
            $('.relatorio_real').css("display", "block");
            chart.show();
        }

        // salva em cookie
        setCookie("AplicaSimulacao", simula, null);
    }


</script>


  