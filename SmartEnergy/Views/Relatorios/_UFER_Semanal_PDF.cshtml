﻿<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.UFER</title>

    <link href="@Server.MapPath("~/Content/bootstrap.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/style.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/plugins/c3/c3.min.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/Relatorios.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/RelatoriosPDF.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/fonts/font-awesome/css/font-awesome.min.css")" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="@Server.MapPath("~/Scripts/jquery-2.1.1.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/d3/d3.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/c3/c3.min.js")"></script>

    <style>

        td {
            font-size: 12px;
        }

        th {
            font-size: 12px;
        }

    </style>

</head>

@{
    string arquivo = "~/Logos/" + ViewBag._LogoConsultor;
    string logo = Server.MapPath(arquivo);
}

@if (ViewBag.Retorno == 2)
{
    <div class="row">
        <div class="col-lg-12">
            <div class="linha cabecalho">
                <div class="coluna1">
                    <p><img src="@logo" style="height:42px; width:auto;" /></p>
                </div>

                <div class="coluna2">
                    <h3>@ViewBag.ClienteNome</h3>
                    @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                </div>

                <div class="coluna3">
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.UFER - @SmartEnergy.Resources.RelatoriosTexts.RelatSemanal</h4>
                    <p>@ViewBag.DataTextoAtual</p>
                </div>
            </div>
        </div>
    </div>

    <br /><br /><br /><br /><br /><br />
    <div class="row">
        <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
            <i class="fa fa-warning fa-5x"></i>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>@SmartEnergy.Resources.RelatoriosTexts.NaoExistemRegistros</h2>
        </div>
    </div>
    <br /><br /><br /><br />

}
else
{

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="@logo" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.UFER - @SmartEnergy.Resources.RelatoriosTexts.RelatSemanal</h4>
                        <p>@ViewBag.DataTextoAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <br />

        @{
            if (ViewBag.showGrafico)
            {
                <br /><br />
                <div class="row">
                    <div class="relat-grafico" style="height:450px; width:800px; margin-left:50px;">
                        <div id="grafico-UFER" style="margin-top: -10px"></div>
                        <div class='chart-legend' style="display:none;">
                            <div class='legend-scale-dem'>
                                <ul class='legend-labels' style="margin-top: -25px;">
                                    <li><span style='background:#1C84C6 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                                    <li><span style='background:#007f00 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                                    <li><span style='background:#ff0000 !important;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }

        <hr />

        <div class="row" style="margin-top: -15px;">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:20%;"></th>
                                <th class="relat-capacitivo" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                                <th class="relat-preto" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.DashboardTexts.UFERTotalSemana<br />&nbsp;</td>
                                <td class="relat-capacitivo">@ViewBag.UFER_Total_FPC @ViewBag.UnidadeUFER</td>
                                <td class="relat-fponta">@ViewBag.UFER_Total_FPI @ViewBag.UnidadeUFER</td>
                                <td class="relat-ponta">@ViewBag.UFER_Total_P @ViewBag.UnidadeUFER</td>
                                <td class="relat-preto">@ViewBag.UFER_Total @ViewBag.UnidadeUFER</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    @{
        if (ViewBag.showTabela)
        {
            <div class="row">
                <div class="col-lg-12">
                    <div class="panel panel-default">
                        <table class="table no-margins tabela-grafico-pdf">
                            <thead>
                                <tr>
                                    <th style="width:20%;">@SmartEnergy.Resources.RelatoriosTexts.Semana</th>
                                    <th style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo<br />(@ViewBag.UnidadeUFER)</th>
                                    <th style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo<br />(@ViewBag.UnidadeUFER)</th>
                                    <th style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta<br />(@ViewBag.UnidadeUFER)</th>
                                    <th style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Total<br />(@ViewBag.UnidadeUFER)</th>
                                </tr>
                            </thead>

                            <tbody>

                                @{
                                    var i = 0;
                                    var UFER_FPC = "---";
                                    var UFER_FPI = "---";
                                    var UFER_P = "---";
                                    var UFER_T = "---";

                                    var dia_semana = new string[] { SmartEnergy.Resources.ComumTexts.domingo, 
                                                             SmartEnergy.Resources.ComumTexts.segunda,
                                                             SmartEnergy.Resources.ComumTexts.terca,
                                                             SmartEnergy.Resources.ComumTexts.quarta,
                                                             SmartEnergy.Resources.ComumTexts.quinta,
                                                             SmartEnergy.Resources.ComumTexts.sexta,
                                                             SmartEnergy.Resources.ComumTexts.sabado
                                                           };

                                    for (i = 0; i < 7; i++)
                                    {
                                        UFER_FPC = string.Format("{0:#,##0.0}", ViewBag.UFER_FPC[i]);
                                        UFER_FPI = string.Format("{0:#,##0.0}", ViewBag.UFER_FPI[i]);
                                        UFER_P = string.Format("{0:#,##0.0}", ViewBag.UFER_P[i]);
                                        UFER_T = string.Format("{0:#,##0.0}", ViewBag.UFER_P[i] + ViewBag.UFER_FPI[i] + ViewBag.UFER_FPC[i]);

                                        <tr class="tabela_valores">
                                            <td style="text-align:center;" class="relat-preto">@dia_semana[i] (@ViewBag.Dias[i])</td>
                                            <td style="text-align:center;" class="relat-capacitivo">@UFER_FPC</td>
                                            <td style="text-align:center;" class="relat-fponta">@UFER_FPI</td>
                                            <td style="text-align:center;" class="relat-ponta">@UFER_P</td>
                                            <td style="text-align:center;" class="relat-preto">@UFER_T</td>
                                        </tr>
                                    }
                                }

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }
    }

    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>

<script type="text/javascript">

    $(document).ready(function () {

        // dias da semana
        var diasSemana = [];

        diasSemana.push('x');
        diasSemana.push('Domingo');
        diasSemana.push('Segunda');
        diasSemana.push('Terca');
        diasSemana.push('Quarta');
        diasSemana.push('Quinta');
        diasSemana.push('Sexta');
        diasSemana.push('Sabado');

        // UFER
        var dataUFER_FPC = [];
        var dataUFER_FPI = [];
        var dataUFER_P = [];

        // copia valores
        var UFER_FPC = @Html.Raw(Json.Encode(@ViewBag.UFER_FPC));
        var UFER_FPI = @Html.Raw(Json.Encode(@ViewBag.UFER_FPI));
        var UFER_P = @Html.Raw(Json.Encode(@ViewBag.UFER_P));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));
        var Unidade = @Html.Raw(Json.Encode(@ViewBag.UnidadeUFER));

        var maximoUFER = Math.ceil(@ViewBag.UFERMaxGrafico);

        // verifico se nao tem dados e forco um limite
        if( maximoUFER == 0.0)
        {
            maximoUFER = 1.0;
        }

        dataUFER_FPC.push(['data1']);
        dataUFER_FPI.push(['data2']);
        dataUFER_P.push(['data3']);

        for (i = 0; i < 7; i++)
        {
            dataUFER_FPC.push([UFER_FPC[i]]);
            dataUFER_FPI.push([UFER_FPI[i]]);
            dataUFER_P.push([UFER_P[i]]);
        }

        var Data = [dataUFER_FPC,dataUFER_FPI,dataUFER_P];

        c3.generate({

            bindto: '#grafico-UFER',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                columns: [diasSemana,dataUFER_P,dataUFER_FPI,dataUFER_FPC],
                types: {
                    data1: 'bar',
                    data2: 'bar',
                    data3: 'bar',
                },
                groups: [
                            ['data1', 'data2', 'data3']
                ],
                order: null,
                colors: {
                    data1: '#1C84C6',
                    data2: '#007F00',
                    data3: '#FF0000'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type : 'categories',
                    tick: {
                        outer: false,
                        fit: true
                    }
                },
                y:  {
                    max: maximoUFER,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoUFER < 100.0 )
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.7 },
            }
        });

        // labels
        $('.chart-legend').css("display", "block");

    });

</script>

}
    