﻿<div class="row">
    <div class="col-lg-4 col-md-12 relatorio_real">
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.FatCargaMes</h5>
                        <h1>@ViewBag.FatCargaFPC_Mes %</h1>
                        <h6>@ViewBag.DataTextoAtual</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.FatCargaMes</h5>
                        <h1>@ViewBag.FatCargaFPI_Mes %</h1>
                        <h6>@ViewBag.DataTextoAtual</h6>
                    </div>
                </div>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.FatCargaMes</h5>
                        <h1>@ViewBag.FatCargaP_Mes %</h1>
                        <h6>@ViewBag.DataTextoAtual</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-12 simulacao" style="display:none;">
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.FatCargaMes</h5>
                        <h1>@ViewBag.FatCargaFPC_Mes_Sim %</h1>
                        <h6>@ViewBag.DataTextoAtual</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.FatCargaMes</h5>
                        <h1>@ViewBag.FatCargaFPI_Mes_Sim %</h1>
                        <h6>@ViewBag.DataTextoAtual</h6>
                    </div>
                </div>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.FatCargaMes</h5>
                        <h1>@ViewBag.FatCargaP_Mes_Sim %</h1>
                        <h6>@ViewBag.DataTextoAtual</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-8 col-md-12">
        <div class="row">
            <div class="relat-grafico">
                <div class="relatorio_real" id="grafico-fatcarga" style="margin-top: -10px"></div>
                <div class="simulacao" id="grafico-fatcarga-simulacao" style="margin-top: -10px; display:none;"></div>
                <div class='chart-legend' style="display:none;">
                    <div class='legend-scale-dem'>
                        <ul class='legend-labels' style="margin-top: -25px;">
                            <li><span style='background:#1C84C6;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                            <li><span style='background:#007f00;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                            <li><span style='background:#ff0000;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@Html.Partial("_Relat_Botoes")

<br />
<div class="panel panel-title relatorio">
    <div class="panel-body">

        <div class="row">
            <div class="col-lg-12 relatorio_real">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatCargaMes<br />&nbsp;</td>
                                <td class="relat-capacitivo">@ViewBag.FatCargaFPC_Mes %</td>
                                <td class="relat-fponta">@ViewBag.FatCargaFPI_Mes %</td>
                                <td class="relat-ponta">@ViewBag.FatCargaP_Mes %</td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMedia<br />&nbsp;</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MedFPC kW</td>
                                <td class="relat-fponta">@ViewBag.Dem_MedFPI kW</td>
                                <td class="relat-ponta">@ViewBag.Dem_MedP kW</td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMaxima</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MaxFPC kW<br /><small>(@ViewBag.Dem_MaxFPC_DataHora)</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MaxFPI kW<br /><small>(@ViewBag.Dem_MaxFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MaxP kW<br /><small>(@ViewBag.Dem_MaxP_DataHora)</small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="col-lg-12 simulacao" style="display:none;">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatCargaMes<br />&nbsp;</td>
                                <td class="relat-capacitivo">@ViewBag.FatCargaFPC_Mes_Sim %</td>
                                <td class="relat-fponta">@ViewBag.FatCargaFPI_Mes_Sim %</td>
                                <td class="relat-ponta">@ViewBag.FatCargaP_Mes_Sim %</td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMedia<br />&nbsp;</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MedFPC_Sim kW</td>
                                <td class="relat-fponta">@ViewBag.Dem_MedFPI_Sim kW</td>
                                <td class="relat-ponta">@ViewBag.Dem_MedP_Sim kW</td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMaxima</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MaxFPC_Sim kW<br /><small>(@ViewBag.Dem_MaxFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MaxFPI_Sim kW<br /><small>(@ViewBag.Dem_MaxFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MaxP_Sim kW<br /><small>(@ViewBag.Dem_MaxP_DataHora_Sim)</small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal inmodal animated fadeIn" id="modalTabela" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-editar-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title"><i class="fa fa-list"></i>&nbsp;&nbsp;@SmartEnergy.Resources.RelatoriosTexts.TabelaRegistros</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-default relatorio_real">
                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Dia</th>
                                <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var i = 0;
                                var j = 0;
                                var fatcargaFPC = "---";
                                var fatcargaFPI = "---";
                                var fatcargaP = "---";

                                var NumDiasMes = ViewBag.NumDiasMes;

                                for (i = 0; i < NumDiasMes; i++)
                                {
                                    j = i + 1;

                                    fatcargaFPC = string.Format("{0:0.0} %", ViewBag.FatCargaFPC[j]);
                                    fatcargaFPI = string.Format("{0:0.0} %", ViewBag.FatCargaFPI[j]);
                                    fatcargaP = string.Format("{0:0.0} %", ViewBag.FatCargaP[j]);

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="relat-preto">@ViewBag.Dias[j]</td>
                                        <td style="text-align:center;" class="relat-capacitivo">@fatcargaFPC</td>
                                        <td style="text-align:center;" class="relat-fponta">@fatcargaFPI</td>
                                        <td style="text-align:center;" class="relat-ponta">@fatcargaP</td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>

                <div class="panel panel-default simulacao" style="display:none;">
                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Dia</th>
                                <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>

                        <tbody>

                            @{                               
                                var fatcargaFPC_sim = "---";
                                var fatcargaFPI_sim = "---";
                                var fatcargaP_sim = "---";

                                for (i = 0; i < NumDiasMes; i++)
                                {
                                    j = i + 1;

                                    fatcargaFPC_sim = string.Format("{0:0.0} %", ViewBag.FatCargaFPC_Sim[j]);
                                    fatcargaFPI_sim = string.Format("{0:0.0} %", ViewBag.FatCargaFPI_Sim[j]);
                                    fatcargaP_sim = string.Format("{0:0.0} %", ViewBag.FatCargaP_Sim[j]);

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="relat-preto">@ViewBag.Dias[j]</td>
                                        <td style="text-align:center;" class="relat-capacitivo">@fatcargaFPC_sim</td>
                                        <td style="text-align:center;" class="relat-fponta">@fatcargaFPI_sim</td>
                                        <td style="text-align:center;" class="relat-ponta">@fatcargaP_sim</td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
            </div>
        </div>
    </div>
</div>


@Html.Partial("_EventosModal")

@Html.Partial("_ObservacoesModal")


<script type="text/javascript">

    $(document).ready(function () {

        // fator de carga
        var dataX = [];
        var labelX = [];
        var dataFatCargaFPC = [];
        var dataFatCargaFPI = [];
        var dataFatCargaP = [];

        // copia valores
        var FatCargaFPC = @Html.Raw(Json.Encode(@ViewBag.FatCargaFPC));
        var FatCargaFPI = @Html.Raw(Json.Encode(@ViewBag.FatCargaFPI));
        var FatCargaP = @Html.Raw(Json.Encode(@ViewBag.FatCargaP));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));
        var NumDiasMes = @Html.Raw(Json.Encode(@ViewBag.NumDiasMes));

        var FatCargaMaxGrafico = Math.ceil(@ViewBag.FatCargaMaxGrafico);

        // verifico se nao tem dados e forco um limite
        if( FatCargaMaxGrafico == 0.0)
        {
            FatCargaMaxGrafico = 100.0;
        }

        dataX.push('x');
        dataFatCargaFPC.push(['data1']);
        dataFatCargaFPI.push(['data2']);
        dataFatCargaP.push(['data3']);

        for (i = 0; i < NumDiasMes+2; i++)
        {
            // X
            dataX.push(Datas[i]);

            dataFatCargaFPC.push([FatCargaFPC[i]]);
            dataFatCargaFPI.push([FatCargaFPI[i]]);
            dataFatCargaP.push([FatCargaP[i]]);
        }

        // valores label X
        labelX.push(Datas[1]);

        var ndias = (NumDiasMes < 25) ? NumDiasMes : 25;

        for (i = 5; i <= ndias; i+=5)
        {
            labelX.push(Datas[i]);
        }
        labelX.push(Datas[NumDiasMes]);

        var Data = [dataX,dataFatCargaFPC,dataFatCargaFPI,dataFatCargaP];

        var chart = c3.generate({

            bindto: '#grafico-fatcarga',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'bar',
                    data3: 'bar'
                },
                order: null,
                colors: {
                    data1: '#1C84C6',
                    data2: '#007F00',
                    data3: '#FF0000'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // retorna dia
                            return x.getDate();
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    // Grafico 1 dia     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(11 horas)
                    padding: -1000*60*60*11
                },
                y:  {
                    max: FatCargaMaxGrafico,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {
                            return d.toFixed(1);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.15 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < d.length; i++) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        if (! text) {

                            // split string and create array.
                            var arr = Datas[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            var datestring = ("0" + data.getDate()).slice(-2) + "/" + ("0" + (data.getMonth()+1)).slice(-2);
                            title = DATA + " " + datestring;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // verifica se barra
                        if( i == 0 )
                        {
                            name = FPONTA_CAP;
                        }

                        if( i == 1 )
                        {
                            name = FPONTA_IND;
                        }

                        if( i == 2 )
                        {
                            name = PONTA;
                        }

                        // verifica se resto
                        if( i > 3)
                        {
                            break;
                        }

                        value = d[i].value;
                        bgcolor = $$.levelColor ? $$.levelColor(d[i].value) : color(d[i].id);

                        text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                        text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                        text += "<td class='value'>" + value.toFixed(1) + " %</td>";
                        text += "</tr>";
                    }
                    return text + "</table>";
                }
            }
        });

        //
        // GRAFICO SIMULACAO
        //

        // fator de carga simulacao
        var dataX_Sim = [];
        var labelX_Sim = [];
        var dataFatCargaFPC_Sim = [];
        var dataFatCargaFPI_Sim = [];
        var dataFatCargaP_Sim = [];

        // copia valores
        var FatCargaFPC_Sim = @Html.Raw(Json.Encode(@ViewBag.FatCargaFPC_Sim));
        var FatCargaFPI_Sim = @Html.Raw(Json.Encode(@ViewBag.FatCargaFPI_Sim));
        var FatCargaP_Sim = @Html.Raw(Json.Encode(@ViewBag.FatCargaP_Sim));
        var Datas_Sim = @Html.Raw(Json.Encode(@ViewBag.Datas_Sim));

        // verifico se nao tem dados e forco um limite
        if( FatCargaMaxGrafico == 0.0)
        {
            FatCargaMaxGrafico = 100.0;
        }

        dataX_Sim.push('x');
        dataFatCargaFPC_Sim.push(['data1']);
        dataFatCargaFPI_Sim.push(['data2']);
        dataFatCargaP_Sim.push(['data3']);

        for (i = 0; i < NumDiasMes+2; i++)
        {
            // X
            dataX_Sim.push(Datas_Sim[i]);

            dataFatCargaFPC_Sim.push([FatCargaFPC_Sim[i]]);
            dataFatCargaFPI_Sim.push([FatCargaFPI_Sim[i]]);
            dataFatCargaP_Sim.push([FatCargaP_Sim[i]]);
        }

        // valores label X
        labelX_Sim.push(Datas_Sim[1]);

        for (i = 5; i <= ndias; i+=5)
        {
            labelX_Sim.push(Datas_Sim[i]);
        }
        labelX_Sim.push(Datas_Sim[NumDiasMes]);

        var Data = [dataX_Sim,dataFatCargaFPC_Sim,dataFatCargaFPI_Sim,dataFatCargaP_Sim];

        var chart_sim = c3.generate({

            bindto: '#grafico-fatcarga-simulacao',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'bar',
                    data3: 'bar'
                },
                order: null,
                colors: {
                    data1: '#1C84C6',
                    data2: '#007F00',
                    data3: '#FF0000'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX_Sim,
                        format: function (x) {

                            // retorna dia
                            return x.getDate();
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    // Grafico 1 dia     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(11 horas)
                    padding: -1000*60*60*11
                },
                y:  {
                    max: FatCargaMaxGrafico,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {
                            return d.toFixed(1);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.15 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < d.length; i++) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        if (! text) {

                            // split string and create array.
                            var arr = Datas_Sim[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            var datestring = ("0" + data.getDate()).slice(-2) + "/" + ("0" + (data.getMonth()+1)).slice(-2);
                            title = DATA + " " + datestring;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // verifica se barra
                        if( i == 0 )
                        {
                            name = FPONTA_CAP;
                        }

                        if( i == 1 )
                        {
                            name = FPONTA_IND;
                        }

                        if( i == 2 )
                        {
                            name = PONTA;
                        }

                        // verifica se resto
                        if( i > 3)
                        {
                            break;
                        }

                        value = d[i].value;
                        bgcolor = $$.levelColor ? $$.levelColor(d[i].value) : color(d[i].id);

                        text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                        text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + " - Simulação" + "</td>";
                        text += "<td class='value'>" + value.toFixed(1) + " %</td>";
                        text += "</tr>";
                    }
                    return text + "</table>";
                }
            }
        });

        // salva chart
        $('#grafico-fatcarga').data('c3-chart', chart);

        // salva chart simulacao
        $('#grafico-fatcarga-simulacao').data('c3-chart', chart_sim);

        // labels
        $('.chart-legend').css("display", "block");

        // verifica se apresenta simulacao
        ShowSimulacao();

        $(".modal-dialog").draggable({
            handle: ".modal-editar-header"
        });
    });

    function ShowSimulacao () {

        // IDSimulacaoCenario
        var IDSimulacaoCenario = parseInt(@ViewBag._IDSimulacaoCenario);

        // verifica se ano existe nenhuma simulacao selecionada
        if (IDSimulacaoCenario == 0)
        {
            $("#aplica_simula").attr('disabled', true);
            $('#aplica_simula').prop('checked', false);
        }
        else
        {
            $("#aplica_simula").attr('disabled', false);
        }

        // grafico fator de carga
        var chart = $('#grafico-fatcarga').data('c3-chart');

        // grafico fator de carga simulacao 
        var chart_sim = $('#grafico-fatcarga-simulacao').data('c3-chart');

        // checkbox aplica simulacao
        var simula = $('#aplica_simula').prop('checked');

        // verifica se ano existe nenhuma simulacao selecionada
        if (simula)
        {
            $('.simulacao').css("display", "block");
            $('.relatorio_real').css("display", "none");
            chart_sim.show();
        }
        else
        {
            $('.simulacao').css("display", "none");
            $('.relatorio_real').css("display", "block");
            chart.show();
        }

        // salva em cookie
        setCookie("AplicaSimulacao", simula, null);
    }


</script>
