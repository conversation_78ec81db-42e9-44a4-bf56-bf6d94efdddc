﻿<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.Meteorologia</title>

    <link href="~/Content/bootstrap.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/style.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/plugins/c3/c3.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/Relatorios.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/RelatoriosPDF.css" rel="stylesheet" type="text/css" />
    <link href="~/fonts/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="~/Scripts/jquery-2.1.1.min.js" charset="utf-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/d3/d3.min.js" charset="utf-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/c3/c3.min.js"></script>

</head>

@if (ViewBag.Retorno == 2)
{
    <div class="row">
        <div class="col-lg-12">
            <div class="linha cabecalho">
                <div class="coluna1">
                    <p><img src="~/Logos/@ViewBag._LogoConsultor" style="height:42px; width:auto;" /></p>
                </div>

                <div class="coluna2">
                    <h3>@ViewBag.ClienteNome</h3>
                    @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                </div>

                <div class="coluna3">
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.Meteorologia - @SmartEnergy.Resources.RelatoriosTexts.RelatSemanal</h4>
                    <p>@ViewBag.DataTextoAtual</p>
                </div>
            </div>
        </div>
    </div>

    <br /><br /><br /><br /><br /><br />
    <div class="row">
        <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
            <i class="fa fa-warning fa-5x"></i>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>@SmartEnergy.Resources.RelatoriosTexts.NaoExistemRegistros</h2>
        </div>
    </div>
    <br /><br /><br /><br />

}
else
{

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="~/Logos/@ViewBag._LogoConsultor" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.Meteorologia - @SmartEnergy.Resources.RelatoriosTexts.RelatSemanal</h4>
                        <p>@ViewBag.DataTextoAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <br />

        @{
            if (ViewBag.showGrafico)
            {
                <div class="row">
                    <div class="relat-grafico" style="height:490px; width:800px; margin-left:50px;">
                        <div id="grafico-ea"></div>
                        <div class='chart-legend' style="display:none;">
                            <div class='legend-scale-dem'>
                                <ul class='legend-labels' style="margin-top: 0px;">
                                    <li><span style='background:#0000FF !important;' id="ckDomingo"></span> @SmartEnergy.Resources.ComumTexts.domingo</li>
                                    <li><span style='background:#00FF00 !important;' id="ckSegunda"></span> @SmartEnergy.Resources.ComumTexts.segunda</li>
                                    <li><span style='background:#FF0000 !important;' id="ckTerca"></span> @SmartEnergy.Resources.ComumTexts.terca</li>
                                    <li><span style='background:#FF7F00 !important;' id="ckQuarta"></span> @SmartEnergy.Resources.ComumTexts.quarta</li>
                                    <li><span style='background:#FF00FF !important;' id="ckQuinta"></span> @SmartEnergy.Resources.ComumTexts.quinta</li>
                                    <li><span style='background:#000000 !important;' id="ckSexta"></span> @SmartEnergy.Resources.ComumTexts.sexta</li>
                                    <li><span style='background:#00FFFF !important;' id="ckSabado"></span> @SmartEnergy.Resources.ComumTexts.sabado</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }

        <hr />

        <div class="row" style="margin-top: -15px;">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-ea" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.MediaSemana</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Maxima</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Minima</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@ViewBag.NomeGrandeza</td>
                                <td class="relat-ea">@ViewBag.VMedSemana @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.DataTextoAtual)</small></td>
                                <td class="relat-preto">@ViewBag.VMaxSemana @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMaxSemana_DataHora)</small></td>
                                <td class="relat-preto">@ViewBag.VMinSemana @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMinSemana_DataHora)</small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    @{
        if (ViewBag.showTabela)
        {
            <div class="page-break"></div>
        
            <div class="row">

                <br /><br />

                <div class="col-lg-12">
                    <div class="panel panel-default">

                        @{
                            var k = 0;

                            var dia_semana = new string[] { SmartEnergy.Resources.ComumTexts.domingo, 
                                                                        SmartEnergy.Resources.ComumTexts.segunda,
                                                                        SmartEnergy.Resources.ComumTexts.terca,
                                                                        SmartEnergy.Resources.ComumTexts.quarta,
                                                                        SmartEnergy.Resources.ComumTexts.quinta,
                                                                        SmartEnergy.Resources.ComumTexts.sexta,
                                                                        SmartEnergy.Resources.ComumTexts.sabado
                                                                    };
                        }

                        <table class="table no-margins tabela-grafico-pdf">
                            <thead>
                                <tr>
                                    <th style="width:16%;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>

                                    @for (k = 0; k < 7; k++)
                                    {
                                        <th style="width:12%;">@dia_semana[k]<br />@ViewBag.Dias[k]<br />(@ViewBag.UnidadeGrandeza)</th>
                                    }

                                </tr>
                            </thead>

                            <tbody>

                                @{
                                    var i = 0;
                                    var j = 0;
                                    var temp = "---";

                                    for (i = 0; i < 24; i++)
                                    {
                                        j = i + 1;

                                        <tr class="tabela_valores">
                                            <td style="text-align:center;" class="relat-preto">@ViewBag.Horas[j]</td>

                                            @for (k = 0; k < 7; k++)
                                            {
                                                if (ViewBag.Tempo_Cod[k, j] >= 0)
                                                {
                                                    temp = string.Format("{0:0}", ViewBag.Temp[k, j]);
                                                }
                                                else
                                                {
                                                    temp = "---";
                                                }

                                                <td style="text-align:center;" class="relat-ea">@temp</td>

                                            }

                                        </tr>
                                    }
                                }

                            </tbody>
                        </table>
                    </div>
                </div>        
            </div>   
        }
    }        

    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>



<script type="text/javascript">

    // dias da semana
    var diasSemana = [];

    diasSemana.push('Domingo');
    diasSemana.push('Segunda');
    diasSemana.push('Terca');
    diasSemana.push('Quarta');
    diasSemana.push('Quinta');
    diasSemana.push('Sexta');
    diasSemana.push('Sabado');
    diasSemana.push('Periodo');

    // cores
    var coresdiasSemana = [];

    coresdiasSemana.push('#0000FF');
    coresdiasSemana.push('#00FF00');
    coresdiasSemana.push('#FF0000');
    coresdiasSemana.push('#FF7F00');
    coresdiasSemana.push('#FF00FF');
    coresdiasSemana.push('#000000');
    coresdiasSemana.push('#00FFFF');

    // c_names
    var c_names = [];

    c_names.push("Relat_SemanalDomingo");
    c_names.push("Relat_SemanalSegunda");
    c_names.push("Relat_SemanalTerca");
    c_names.push("Relat_SemanalQuarta");
    c_names.push("Relat_SemanalQuinta");
    c_names.push("Relat_SemanalSexta");
    c_names.push("Relat_SemanalSabado");

    // id_names
    var id_names = [];

    id_names.push("#ckDomingo");
    id_names.push("#ckSegunda");
    id_names.push("#ckTerca");
    id_names.push("#ckQuarta");
    id_names.push("#ckQuinta");
    id_names.push("#ckSexta");
    id_names.push("#ckSabado");


    $(document).ready(function () {
        // copia valores
        var Temp = @Html.Raw(Json.Encode(@ViewBag.Temp));
        var Tempo_Cod = @Html.Raw(Json.Encode(@ViewBag.Tempo_Cod));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));
        var NomeGrandeza = @Html.Raw(Json.Encode(@ViewBag.NomeGrandeza));
        var UnidadeGrandeza = @Html.Raw(Json.Encode(@ViewBag.UnidadeGrandeza));

        // valor minimo e máximo
        var minimoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMin));
        var maximoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMax));

        var diferenca = maximoValor - minimoValor;

        // grafico
        var dataX = [];
        var labelX = [];
        var dataDom = [];
        var dataSeg = [];
        var dataTer = [];
        var dataQua = [];
        var dataQui = [];
        var dataSex = [];
        var dataSab = [];

        dataX.push('x');
        dataDom.push(['data1']);
        dataSeg.push(['data2']);
        dataTer.push(['data3']);
        dataQua.push(['data4']);
        dataQui.push(['data5']);
        dataSex.push(['data6']);
        dataSab.push(['data7']);


        for (i = 0; i < 26; i++)
        {
            // X
            dataX.push(Datas[i]);

            dataDom.push([ Temp[i]        ]);
            dataSeg.push([ Temp[i+26]     ]);
            dataTer.push([ Temp[i+(2*26)] ]);
            dataQua.push([ Temp[i+(3*26)] ]);
            dataQui.push([ Temp[i+(4*26)] ]);
            dataSex.push([ Temp[i+(5*26)] ]);
            dataSab.push([ Temp[i+(6*26)] ]);
        }

        // valores label X
        labelX.push(Datas[1]);
        for (i = 3; i <= 24; i+=3)
        {
            labelX.push(Datas[i]);
        }

        var Data = [dataX,dataDom,dataSeg,dataTer,dataQua,dataQui,dataSex,dataSab];

        var chart = c3.generate({

            bindto: '#grafico-ea',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'line',
                    data2: 'line',
                    data3: 'line',
                    data4: 'line',
                    data5: 'line',
                    data6: 'line',
                    data7: 'line',
                },
                colors: {
                    data1: '#0000FF',
                    data2: '#00FF00',
                    data3: '#FF0000',
                    data4: '#FF7F00',
                    data5: '#FF00FF',
                    data6: '#000000',
                    data7: '#00FFFF',
                },
            },
            point: {
                r:3
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*28
                },
                y:  {
                    min: minimoValor,
                    max: maximoValor,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( diferenca < 10)
                            {
                                return d.toFixed(2);
                            }

                            if( diferenca < 100)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
        });

        // salva chart
        $('#grafico-ea').data('c3-chart', chart);

        // label dias da semana
        todosDiaSemana();

        // labels
        $('.chart-legend').css("display", "block");

    });

    function todosDiaSemana() {

        // dias da semana
        var i;

        for(i=0;i<7;i++)
        {
            alteraDiaSemana(i,false);
        }
    }

    function alteraDiaSemana(dia_semana, alterar) {

        // le cookie dia da semana
        var estado = 1;

        switch(dia_semana)
        {
            case 0:
                estado = Math.ceil(@ViewBag.Relat_SemanalDomingo);
                break;

            case 1:
                estado = Math.ceil(@ViewBag.Relat_SemanalSegunda);
                break;

            case 2:
                estado = Math.ceil(@ViewBag.Relat_SemanalTerca);
                break;

            case 3:
                estado = Math.ceil(@ViewBag.Relat_SemanalQuarta);
                break;

            case 4:
                estado = Math.ceil(@ViewBag.Relat_SemanalQuinta);
                break;

            case 5:
                estado = Math.ceil(@ViewBag.Relat_SemanalSexta);
                break;

            case 6:
                estado = Math.ceil(@ViewBag.Relat_SemanalSabado);
                break;
        }

        // data series
        var chart_ea = $('#grafico-ea').data('c3-chart');

        // verifica estado
        if( estado == 1 )
        {
            chart_ea.show(["data" + (dia_semana+1)]);

        }
        else
        {
            chart_ea.hide(["data" + (dia_semana+1)]);

        }
    }

</script>

}
    