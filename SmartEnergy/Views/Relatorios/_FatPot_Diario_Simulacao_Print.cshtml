﻿<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia</title>

    <link href="~/Content/bootstrap.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/style.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/plugins/c3/c3.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/Relatorios.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/RelatoriosPDF.css" rel="stylesheet" type="text/css" />
    <link href="~/fonts/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="~/Scripts/jquery-2.1.1.min.js" charset="utf-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/d3/d3.min.js" charset="utf-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/c3/c3.min.js"></script>

</head>

@if (ViewBag.Retorno == 2)
{
    <div class="row">
        <div class="col-lg-12">
            <div class="linha cabecalho">
                <div class="coluna1">
                    <p><img src="~/Logos/@ViewBag._LogoConsultor" style="height:42px; width:auto;" /></p>
                </div>

                <div class="coluna2">
                    <h3>@ViewBag.ClienteNome</h3>
                    @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                </div>

                <div class="coluna3">
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia - @SmartEnergy.Resources.RelatoriosTexts.RelatDiario (@SmartEnergy.Resources.RelatoriosTexts.Simulacao)</h4>
                    <p>@ViewBag.DataAtual</p>
                </div>
            </div>
        </div>
    </div>

    <br /><br /><br /><br /><br /><br />
    <div class="row">
        <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
            <i class="fa fa-warning fa-5x"></i>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>@SmartEnergy.Resources.RelatoriosTexts.NaoExistemRegistros</h2>
        </div>
    </div>
    <br /><br /><br /><br />

}
else
{

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="~/Logos/@ViewBag._LogoConsultor" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia - @SmartEnergy.Resources.RelatoriosTexts.RelatDiario (@SmartEnergy.Resources.RelatoriosTexts.Simulacao)</h4>
                        <p>@ViewBag.DataAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <br />

        @{
            if (ViewBag.showGrafico)
            {
                <div class="row">
                    <div class="relat-grafico" style="height:490px; width:800px; margin-left:50px;">
                        <div id="fatpot-chart"></div>
                        <div class='chart-legend' style="display:none;">
                            <div class='legend-scale-dem'>
                                <ul class='legend-labels' style="margin-top: -25px;">
                                    <li><span style='background:#1C84C6 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                                    <li><span style='background:#007f00 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                                    <li><span style='background:#ff0000 !important;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                                    <li class="simulacao"><span style='background:#00b1ff;'></span>@SmartEnergy.Resources.RelatoriosTexts.Simulacao</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }

        <hr />

        <div class="row" style="margin-top: -15px;">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisCapacitivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisCapFPC_Sim<br /><small>(@ViewBag.FatPot_MaisCapFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.FatPot_MaisCapFPI_Sim<br /><small>(@ViewBag.FatPot_MaisCapFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.FatPot_MaisCapP_Sim<br /><small>(@ViewBag.FatPot_MaisCapP_DataHora_Sim)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisIndFPC_Sim<br /><small>(@ViewBag.FatPot_MaisIndFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.FatPot_MaisIndFPI_Sim<br /><small>(@ViewBag.FatPot_MaisIndFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.FatPot_MaisIndP_Sim<br /><small>(@ViewBag.FatPot_MaisIndP_DataHora_Sim)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.NoPeriodo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_NoPeriodoFPC_Sim</td>
                                <td class="relat-fponta">@ViewBag.FatPot_NoPeriodoFPI_Sim</td>
                                <td class="relat-ponta">@ViewBag.FatPot_NoPeriodoP_Sim</td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.Referencia</td>
                                <td class="relat-capacitivo">@ViewBag.RefenciaFPC</td>
                                <td class="relat-fponta">@ViewBag.RefenciaFPI</td>
                                <td class="relat-ponta">@ViewBag.RefenciaFPI</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    @{
        if (ViewBag.showTabela)
        {
            <div class="page-break"></div>
            
            <div class="row">

                <br /><br />

                <div class="col-lg-12">
                    <div class="panel panel-default">
                        <table class="table no-margins tabela-grafico-pdf">
                            <thead>
                                <tr>
                                    <th style="width:20%; text-align:center; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>
                                    <th style="width:40%; text-align:center; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Periodo</th>
                                    <th style="width:40%; text-align:center; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia</th>
                                </tr>
                            </thead>

                            <tbody>

                                @{
                                    var i = 0;
                                    var j = 0;
                                    var classe_sim = "relat-semreg";
                                    var periodo_sim = "---";
                                    var fatpot_sim = "---";

                                    for (i = 0; i < 24; i++)
                                    {
                                        j = i + 1;

                                        switch ((int)ViewBag.Periodo_Sim[j])
                                        {
                                            case 0:
                                                classe_sim = "relat-ponta";
                                                periodo_sim = "Ponta";
                                                break;

                                            case 1:
                                                classe_sim = "relat-indutivo";
                                                periodo_sim = "FPonta Ind";
                                                break;

                                            case 2:
                                                classe_sim = "relat-capacitivo";
                                                periodo_sim = "FPonta Cap";
                                                break;

                                            default:
                                            case 3:
                                                classe_sim = "relat-semreg";
                                                periodo_sim = "---";
                                                break;
                                        }

                                        if (ViewBag.Periodo_Sim[j] == 3)
                                        {
                                            fatpot_sim = "---";
                                        }
                                        else
                                        {
                                            fatpot_sim = string.Format("{0:0.000}", ViewBag.FatPot_Sim[j]);
                                        }

                                        <tr class="tabela_valores" style="font-size:12px;">
                                            <td style="text-align:center; font-size:12px;" class="@classe_sim">@ViewBag.Horas_Sim[j]</td>
                                            <td style="text-align:center; font-size:12px;" class="@classe_sim">@periodo_sim</td>
                                            <td style="text-align:center; font-size:12px;" class="@classe_sim">@fatpot_sim</td>
                                        </tr>
                                    }
                                }

                            </tbody>
                        </table>
                    </div>


                </div>
            </div>
        }
    }

    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>

<script type="text/javascript">

    $(document).ready(function () {

        // fator de potencia
        var dataX = [];
        var labelX = [];
        var dataReferenciaFPC = [];
        var dataReferenciaFPI = [];
        var dataReferenciaP = [];
        var dataFatPot = [];
        var dataFatPotUm = [];

        // fator de potencia simulacao
        var dataFatPot_Sim = [];
        var dataFatPotUm_Sim = [];

        // copia valores
        var FatPot = @Html.Raw(Json.Encode(@ViewBag.FatPot));
        var Periodo = @Html.Raw(Json.Encode(@ViewBag.Periodo));
        var Dias = @Html.Raw(Json.Encode(@ViewBag.Dias));
        var FatPotMin = @Html.Raw(Json.Encode(@ViewBag.FatPotMin));

        // copia valores simulacao
        var FatPot_Sim = @Html.Raw(Json.Encode(@ViewBag.FatPot_Sim));

        dataX.push('x');
        dataFatPot.push(['data1']);
        dataReferenciaFPC.push(['data2']);
        dataReferenciaFPI.push(['data3']);
        dataReferenciaP.push(['data4']);
        dataFatPotUm.push(['data5']);

        // simulacao
        dataFatPot_Sim.push(['data6']);

        for (i = 0; i < 26; i++)
        {
            // X
            dataX.push(Dias[i]);

            // linha de fatpot um
            dataFatPotUm.push([0.0]);

            // converte para nova escala
            if( FatPot[i] < 0 )
            {
                dataFatPot.push([(1.0 - (-1.0*FatPot[i])) * -1.0]);
            }
            else
            {
                dataFatPot.push([(1.0 - FatPot[i])]);
            }

            // converte para nova escala simulacao
            if( FatPot_Sim[i] < 0 )
            {
                dataFatPot_Sim.push([(1.0 - (-1.0*FatPot_Sim[i])) * -1.0]);
            }
            else
            {
                dataFatPot_Sim.push([(1.0 - FatPot_Sim[i])]);
            }

            // periodo
            switch(Periodo[i])
            {
                case 0:
                    dataReferenciaP.push([0.08]);
                    dataReferenciaFPI.push([0]);
                    dataReferenciaFPC.push([0]);
                    break;

                case 1:
                    dataReferenciaP.push([0]);
                    dataReferenciaFPI.push([0.08]);
                    dataReferenciaFPC.push([0]);
                    break;

                case 2:
                    dataReferenciaP.push([0]);
                    dataReferenciaFPI.push([0]);
                    dataReferenciaFPC.push([-0.08]);
                    break;

                default:
                    dataReferenciaP.push([0]);
                    dataReferenciaFPI.push([0]);
                    dataReferenciaFPC.push([0]);
                    break;
            }

        }

        // prepara escala eixo Y
        var maximo = (1.0 - FatPotMin);
        var minimo = -1.0 * maximo;

        // valores label X
        labelX.push(Dias[1]);
        for (i = 3; i <= 24; i+=3)
        {
            labelX.push(Dias[i]);
        }

        var Data = [dataX,dataFatPot,dataReferenciaFPC,dataReferenciaFPI,dataReferenciaP,dataFatPotUm,dataFatPot_Sim];

        var chart = c3.generate({
            bindto: '#fatpot-chart',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 41
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'area-step',
                    data3: 'area-step',
                    data4: 'area-step',
                    data5: 'step',
                    data6: 'step'
                },
                color: function (color, d) {
                    if (typeof d.index === 'undefined') { return color; }

                    var cor = '#007F00'

                    switch(Periodo[d.index])
                    {
                        case 0:
                            cor = '#FF0000'
                            break;
                        case 1:
                            cor = '#007F00'
                            break;
                        case 2:
                            cor = '#1C84C6'
                            break;
                    }

                    return cor;
                },
                colors: {
                    data2: '#1C84C6',
                    data3: '#007F00',
                    data4: '#FF0000',
                    data5: '#000000',
                    data6: '#00b1ff'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*28
                },
                y:  {
                    max: maximo,
                    min: minimo,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            var valor;

                            // converte para nova escala
                            if( d < 0 )
                            {
                                valor = (1.0 - (-1.0*d)) * -1.0;
                            }
                            else
                            {
                                valor = (1.0 - d);
                            }

                            return valor.toFixed(3);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.25 },
            },
        });

        // salva chart
        $('#fatpot-chart').data('c3-chart', chart);

        // labels
        $('.chart-legend').css("display", "block");
    });

</script>

}
    