﻿@model SmartEnergyLib.SQL.AnaliseHorarioConsGruposMedDominio

@using SmartEnergyLib.SQL
@using SmartEnergyLib.SQL
@using System.Globalization

@{
    ViewBag.Title = "Análise Horário de Consumo - Grupos de Medição";
}

<style>
    .icones {
        padding: 0px 5px;
        font-size: 18px;
        float: left;
    }

    .titulo {
        margin: 0;
        margin-left: 0px;
        margin-top: 20px;
        margin-bottom: 15px;
        padding-left: 15px;
        padding-top: 5px;
        padding-bottom: 5px;
        font-size: 12px;
        font-weight: bold;
        border-radius: 10px;
        float: left;
    }

    .link_branco a:link {
        color: #ffffff !important;
    }

    .link_branco a:visited {
        color: #ffffff !important;
    }

    .link_branco a:hover {
        color: #f0f0f0 !important;
    }

    .link_branco a:active {
        color: #ffffff !important;
    }

    .link_preto a:link {
        color: #7E6A6C !important;
    }

    .link_preto a:visited {
        color: #7E6A6C !important;
    }

    .link_preto a:hover {
        color: #a0a0a0 !important;
    }

    .link_preto a:active {
        color: #7E6A6C !important;
    }

    table.dataTable.select tbody tr,
    table.dataTable thead th:first-child {
        cursor: pointer;
    }

    #dataTables-medicoes tbody tr.selected {
        color: white;
        background-color: #1ab394;
    }

    .overlay_aguarde {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.45);
        z-index: 9999;
        color: white;
        display: inline-block;
        border-radius: 6px;
        margin-bottom: 20px;
        margin-left: 14px;
        margin-right: 14px;
    }

    .sk-spinner-wave div {
        background-color: #ffffff !important;
    }

    .spinner-aguarde {
        margin: 0;
        position: absolute;
        top: 50%;
        left: 50%;
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
    }
</style>

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("AnaliseConsHorarioGruposMed_Editar", "Analises", FormMethod.Post, new { id = "form", role = "form" }))
            {                
                AnaliseHorarioConsDominio analise = ViewBag.Analise;
                CliGateGrupoUnidMedicoesDominio medAnalise = ViewBag.MedAnalise;
                
                @Html.Hidden("IDAnaliseHorarioCons", analise.IDAnaliseHorarioCons)
                @Html.Hidden("IDAnaliseHorarioConsGruposMed", Model.IDAnaliseHorarioConsGruposMed)
                @Html.Hidden("IDUnidade", medAnalise.IDUnidade)                                
                @Html.Hidden("IDMedicao", Model.IDMedicao)
                
                
                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracao - @SmartEnergy.Resources.ConfiguracaoTexts.AnaliseHorarioCons @SmartEnergy.Resources.MetasTexts.Medicao</h4>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="form-group col-lg-4">
                                <label class="control-label">&nbsp;Análise Horário de Consumo</label>
                                @Html.TextBox("NomeAnalise", analise.Nome, new { @class = "form-control", @disabled = "disabled" })
                            </div>
                            <div class="col-lg-offset-6 col-lg-2">
                                <div class="ibox-tools">
                                    <a style="color:#000000;" class="btn btn-default" href=@("/Analises/AnaliseHorarioCons_Editar?IDAnalisehorarioCons=" + @Model.IDAnaliseHorarioCons.ToString())>@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                    <button type="button" class="btn btn-primary" id="BotaoSalvar" onclick="Salvar();">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                </div>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.MetasTexts.Medicao</label>
                                            @Html.TextBox("NomeMedicao", medAnalise.NomeMedicao, new { @class = "form-control", @disabled = "disabled" })
                                        </div>
                                        <div class="form-group col-lg-6">
                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Unidade</label>
                                            @Html.TextBox("NomeMedicao", medAnalise.NomeUnidade, new { @class = "form-control", @disabled = "disabled" })
                                        </div>
                                    </div>

                                    <br />

                                    <div class="row">
                                        <div class="form-group col-lg-2">
                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.HoraIni @SmartEnergy.Resources.SupervisaoTexts.Referencia</label>
                                            @Html.TextBoxFor(model => model.HoraIniRef, new { @class = "form-control", data_mask = "99:99", @disabled = "disabled", @Value = String.Format("{0:t}", Model.HoraIniRef) })
                                        </div>
                                        <div class="form-group col-lg-2">
                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.HoraFim @SmartEnergy.Resources.SupervisaoTexts.Referencia</label>
                                            @Html.TextBoxFor(model => model.HoraFimRef, new { @class = "form-control", data_mask = "99:99", @disabled = "disabled", @Value = String.Format("{0:t}", Model.HoraFimRef) })
                                        </div>
                                        <div class="form-group col-lg-offset-2 col-lg-3">
                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.DemandaResidual (kW)</label>
                                            @Html.TextBoxFor(model => model.DemandaResidual, new { @class = "form-control", @disabled = "disabled", @Value = Model.DemandaResidual, @maxlength = "6" })
                                        </div>
                                        <div class="form-group col-lg-3">
                                            <br />
                                            <div class="i-checks" style="margin-top:12px;">
                                                @Html.CheckBoxFor(model => model.CalcResidualAutoCheck, new { @class = "form-control", @onchange = "ShowDemandaResidual(this)" })&nbsp;&nbsp;<span style="margin-right:14px; font-weight:bold;">Calcular Demanda Residual Automaticamente</span>
                                                @Html.Hidden("CalcResidualAuto", Model.CalcResidualAuto)
                                            </div>
                                        </div>
                                    </div>

                                    <br />

                                    <div class="row">
                                        <div class="col-lg-3">
                                            <div class="link_branco">
                                                <button type="button" id="BotaoAdicionarMedicao" data-toggle="modal" href="#ModalMedicoes" class="btn btn-info btn-lg pull-left" style="color:#ffffff; width:100%;">
                                                    <span style="font-size:large">@SmartEnergy.Resources.UsuarioPerfilTexts.AdicionarMedicao - @SmartEnergy.Resources.ConfiguracaoTexts.Temperatura</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <br />

                                    <div class="row">
                                        <div class="form-group col-lg-12">
                                            <table id="dataTables-medicoes" class="table table-striped table-bordered table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>ID</th>
                                                        <th>IDUnidade</th>
                                                        <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Grupos</th>
                                                        <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Unidades</th>
                                                        <th>@SmartEnergy.Resources.UsuarioPerfilTexts.Medicoes</th>
                                                        <th></th>
                                                        <th>IDAnaliseHorarioConsGruposMed</th>
                                                    </tr>
                                                </thead>
                                                <tbody>

                                                    @{
                                                        List<CliGateGrupoUnidMedicoesDominio> medicoes = ViewBag.Medicoes;
                                                        List<AnaliseHorarioConsGruposMedEADominio> analiseGruposEA = ViewBag.analiseGruposEA;

                                                        foreach (var medicao in medicoes)
                                                        {
                                                            int IDAnaliseHorarioConsGruposMed = Model.IDAnaliseHorarioConsGruposMed;

                                                            foreach (var analiseGrupoEA in analiseGruposEA)
                                                            {
                                                                if (medicao.IDMedicao == analiseGrupoEA.IDMedicao)
                                                                {
                                                                    IDAnaliseHorarioConsGruposMed = analiseGrupoEA.IDAnaliseHorarioConsGruposMed;
                                                                }
                                                            }
                                                            
                                                            <tr>
                                                                <td>@medicao.IDMedicao</td>
                                                                <td>@medicao.IDUnidade</td>
                                                                <td class="some_minidesktop">@medicao.NomeGrupoUnidades</td>
                                                                <td class="some_minidesktop">@medicao.NomeUnidade</td>
                                                                <td>@medicao.NomeMedicao</td>
                                                                <td class="link_preto">                                                                   
                                                                    <a href="#" class="confirm-delete-medicao"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>
                                                                </td>
                                                                <td>@IDAnaliseHorarioConsGruposMed</td>
                                                            </tr>
                                                        }
                                                    }

                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                    <br /><br />

                                    <div class="div_gerenciamento_selecione" style="display:block;">
                                        <div class="row">
                                            <div class="form-group col-lg-9">
                                                <label><i class="fa fa-exclamation-circle icones"></i>&nbsp;Adicione as medições de Temperatura para esta Análise do Horário de Consumo.</label><br />
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>

                        <div class="modal inmodal animated fadeIn" id="ModalMedicoes" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                                        <h4 class="modal-title"><i class="fa fa-check-square-o"></i>&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.SelecioneMedicao</h4>
                                    </div>
                                    <div class="modal-body">

                                        <table id="dataTables-medicoes2" class="table table-striped table-bordered table-hover">
                                            <thead>
                                                <tr>
                                                    <th><input name="select_all" value="1" type="checkbox"></th>
                                                    <th>ID</th>
                                                    <th>IDUnidade</th>
                                                    <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Grupos</th>
                                                    <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Unidades</th>
                                                    <th>@SmartEnergy.Resources.UsuarioPerfilTexts.Medicoes</th>                                                    
                                                </tr>
                                            </thead>
                                            <tbody>

                                                @{
                                                    List<CliGateGrupoUnidMedicoesDominio> medicoes2 = ViewBag.Medicoes2;

                                                    foreach (var medicao in medicoes2)
                                                    {
                                                        <tr>
                                                            <td>@medicao.IDMedicao</td>
                                                            <td>@medicao.IDMedicao</td>
                                                            <td>@medicao.IDUnidade</td>
                                                            <td class="some_minidesktop">@medicao.NomeGrupoUnidades</td>
                                                            <td class="some_minidesktop">@medicao.NomeUnidade</td>
                                                            <td>@medicao.NomeMedicao</td>
                                                        </tr>
                                                    }
                                                }

                                            </tbody>
                                        </table>

                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                        <button type="button" class="btn btn-primary" onclick="fnClickAddRowMedicoes();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoInserir</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="overlay_aguarde" style="display: none">
                        <div class="spinner-aguarde">
                            <div class="sk-spinner sk-spinner-wandering-cubes">
                                <div class="sk-spinner sk-spinner-wave">
                                    <div class="sk-rect1 text-ligh"></div>
                                    <div class="sk-rect2"></div>
                                    <div class="sk-rect3"></div>
                                    <div class="sk-rect4"></div>
                                    <div class="sk-rect5"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            }

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/plugins/iCheck")

    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        // Array holding selected row IDs
        var rows_selected_medicoes = [];
        var names_selected_medicoes_id_unid = [];
        var names_selected_medicoes_grupo = [];
        var names_selected_medicoes_unid = [];
        var names_selected_medicoes_med = [];

        var names_selected_medicoes_IDAnaliseGruposMed = [];

        //
        // Updates "Select all" control in a data table
        //
        function updateDataTableSelectAllCtrl(table) {
            var $table = table.table().node();
            var $chkbox_all = $('tbody input[type="checkbox"]', $table);
            var $chkbox_checked = $('tbody input[type="checkbox"]:checked', $table);
            var chkbox_select_all = $('thead input[name="select_all"]', $table).get(0);

            // If none of the checkboxes are checked
            if ($chkbox_checked.length === 0) {
                chkbox_select_all.checked = false;
                if ('indeterminate' in chkbox_select_all) {
                    chkbox_select_all.indeterminate = false;
                }

                // If all of the checkboxes are checked
            } else if ($chkbox_checked.length === $chkbox_all.length) {
                chkbox_select_all.checked = true;
                if ('indeterminate' in chkbox_select_all) {
                    chkbox_select_all.indeterminate = false;
                }

                // If some of the checkboxes are checked
            } else {
                chkbox_select_all.checked = true;
                if ('indeterminate' in chkbox_select_all) {
                    chkbox_select_all.indeterminate = true;
                }
            }
        }

        $(document).ready(function () {

            // classe iChecks
            $('.i-checks').iCheck({
                checkboxClass: 'icheckbox_square-green',
                radioClass: 'iradio_square-green',
            });

            $(".i-checks").on('ifToggled', function (e) { $(e.target).trigger('change'); });           

            //
            // VALIDACAO DOS CAMPOS
            //

            jQuery.validator.addMethod("alphanumeric", function (value, element) {
                return this.optional(element) || /^[\'\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\+\-\_\(\)," "]+$/i.test(value);
            }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

            jQuery.validator.addMethod("numeric", function (value, element) {
                return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
            }, "@SmartEnergy.Resources.ValidateTexts.numeric");


            jQuery.validator.addMethod("time", function (value, element) {
                return this.optional(element) || /^(([0-1]?[0-9])|([2][0-3])):([0-5]?[0-9])(:([0-5]?[0-9]))?$/i.test(value);
            }, "@SmartEnergy.Resources.ValidateTexts.horario");

            $("#form").validate({
                rules: {
                    HoraIniRef: {
                        required: true,
                        time: true,
                    },
                    HoraFimRef: {
                        required: true,
                        time: true,
                    },
                    DemandaRedidual: {
                        numeric: true,
                    }
                },
                highlight: function (element) {
                    $(element).closest('.control-group').removeClass('success').addClass('error');
                },
                unhighlight: function (element) {
                    $(element).closest('.control-group').addClass('valid success').removeClass('error');
                }
            });
            
            // desabilita campos
            disableAll();

            // verifica status do checkbox
            var calcAuto = document.getElementById("CalcResidualAuto").value.toLowerCase();

            if (calcAuto === "true") {
                $('#CalcResidualAutoCheck').iCheck('check');
                $("#DemandaResidual").attr('disabled', true);
                $("#DemandaResidual").val("");
            }
            else {
                $('#CalcResidualAutoCheck').iCheck('uncheck');
            }
            
            //
            // TABELA MEDICOES
            //

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "portugues-pre": function (data) {
                    var a = 'a';
                    var e = 'e';
                    var i = 'i';
                    var o = 'o';
                    var u = 'u';
                    var c = 'c';
                    var special_letters = {
                        "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                        "É": e, "é": e, "Ê": e, "ê": e,
                        "Í": i, "í": i, "Î": i, "î": i,
                        "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                        "Ú": u, "ú": u, "Ü": u, "ü": u,
                        "ç": c, "Ç": c
                    };
                    for (var val in special_letters)
                        data = data.split(val).join(special_letters[val]).toLowerCase();
                    return data;
                },
                "portugues-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },
                "portugues-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });
            
            var tableMedicoes = $('#dataTables-medicoes').DataTable({
                "iDisplayLength": 10,
                dom: 'ftp',

                'order': [1, 'asc'],
                "bAutoWidth": false,
                "aoColumns": [
                { sWidth: "4%" },
                { sWidth: "1%" },
                { sWidth: "18%" },
                { sWidth: "18%" },
                { sWidth: "34%" },
                { sWidth: "10%" },
                { sWidth: "1%" },
                ],

                "columnDefs": [
                     { "targets": [0], "visible": false, "searchable": false, "orderable": false },
                     { "targets": [1], "visible": false, "searchable": false, "orderable": false },
                     { "targets": [2], "sType": "portugues" },
                     { "targets": [3], "sType": "portugues" },
                     { "targets": [4], "sType": "portugues" },
                     { "targets": [5], "searchable": false, "orderable": false },
                     { "targets": [6], "visible": false, "searchable": false, "orderable": false },
                ],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }
            });

            //
            // TABELA MEDICOES (MODAL)
            //

            var tableMedicoes2 = $('#dataTables-medicoes2').DataTable({
                "iDisplayLength": 6,
                dom: 'ftp',

                'order': [2, 'asc'],
                "bAutoWidth": false,
                "aoColumns": [
                { sWidth: "5%" },
                { sWidth: "10%" },
                { sWidth: "1%" },
                { sWidth: "29%" },
                { sWidth: "30%" },
                { sWidth: "20%" },
                ],
                
                'columnDefs': [{
                    'targets': 0,
                    'searchable': false,
                    'orderable': false,
                    'className': 'dt-center',
                    'render': function (data, type, full, meta) {
                        return '<input type="checkbox" name="checkbox_modal_medicoes">';
                    },
                },
                {
                    'targets': 1,
                    'visible': false,
                    'searchable': false,
                    'orderable': false,
                },
                {
                    'targets': 2,
                    'visible': false,
                    'searchable': false,
                    'orderable': false,
                }],

                'rowCallback': function (row, data, dataIndex) {
                    // Get row ID
                    var rowId = data[0];

                    // If row ID is in the list of selected row IDs
                    if ($.inArray(rowId, rows_selected_medicoes) !== -1) {
                        $(row).find('input[type="checkbox"]').prop('checked', true);
                        $(row).addClass('selected');
                    }
                },

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }
            });           
                       
            // manipula checkbox
            $('#dataTables-medicoes2 tbody').on('click', 'input[type="checkbox"]', function (e) {

                var $row = $(this).closest('tr');

                // Get row data
                var data = tableMedicoes2.row($row).data();

                // Get row ID
                var rowId = data[0];
                var id_unid = data[2]
                var name_grupo = data[3];
                var name_unid = data[4];
                var name_med = data[5];

                // id unidade 
                var IDUnidade = parseInt(document.getElementById("IDUnidade").value);
                
                // Determine whether row ID is in the list of selected row IDs
                var index = $.inArray(rowId, rows_selected_medicoes);

                // If checkbox is checked and row ID is not in list of selected row IDs
                if (this.checked && index === -1) {
                    rows_selected_medicoes.push(rowId);
                    names_selected_medicoes_id_unid.push(id_unid);
                    names_selected_medicoes_grupo.push(name_grupo);
                    names_selected_medicoes_unid.push(name_unid);
                    names_selected_medicoes_med.push(name_med);

                    names_selected_medicoes_IDAnaliseGruposMed.push(0);
                   
                    // Otherwise, if checkbox is not checked and row ID is in list of selected row IDs
                } else if (!this.checked && index !== -1) {
                    rows_selected_medicoes.splice(index, 1);
                    names_selected_medicoes_id_unid.splice(index, 1);
                    names_selected_medicoes_grupo.splice(index, 1);
                    names_selected_medicoes_unid.splice(index, 1);
                    names_selected_medicoes_med.splice(index, 1);

                    names_selected_medicoes_IDAnaliseGruposMed.push(index, 1);
                }

                if (this.checked) {
                    $row.addClass('selected');
                } else {
                    $row.removeClass('selected');
                }

                // Update state of "Select all" control
                updateDataTableSelectAllCtrl(tableMedicoes2);

                // Prevent click event from propagating to parent
                e.stopPropagation();
            });

            // manipula click na tabela
            $('#dataTables-medicoes2').on('click', 'tbody td, thead th:first-child', function (e) {

                $(this).parent().find('input[type="checkbox"]').trigger('click');
            });

            // manipula click no "Select all"
            $('thead input[name="select_all"]', tableMedicoes2.table().container()).on('click', function (e) {
                if (this.checked) {
                    $('tbody input[type="checkbox"]:not(:checked)', tableMedicoes2.table().container()).trigger('click');
                } else {
                    $('tbody input[type="checkbox"]:checked', tableMedicoes2.table().container()).trigger('click');
                }

                // Prevent click event from propagating to parent
                e.stopPropagation();
            });
            
            // Handle table draw event
            tableMedicoes2.on('draw', function () {
                // Update state of "Select all" control
                updateDataTableSelectAllCtrl(tableMedicoes2);
            });

        });

            //
            // DESABILITA CAMPOS
            //

            function disableAll() {

                var permissao = Math.ceil(@ViewBag.Permissao);

                // verifica permissao
                switch (permissao) {
                    case 0:     // 0 - permissao de Admin: ve e escreve em tudo
                    case 1:     // 1 - permissao de Gerente: ve tudo e escreve parte
                    case 4:     // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
                    case 5:     // 5 - permissao de Consultor

                        $("#HoraIniRef").attr('disabled', false);
                        $("#HoraFimRef").attr('disabled', false);
                        $("#DemandaResidual").attr('disabled', false);
                        $("#CalcResidualAuto").attr('disabled', false);
                        
                        break;

                    default:
                    case 2:     // 2 - permissao de Operador: ve tudo e nao pode escrever
                    case 3:     // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                        $("#BotaoSalvar").attr('disabled', true);
                        $("#BotaoAdicionarMedicao").attr('disabled', true);

                        break;
                }
            }
                        
            //
            // BOTAO ADICIONA MEDICOES
            //

            function fnClickAddRowMedicoes() {

                // percorre lista de selecionados
                $.each(rows_selected_medicoes, function (index, rowId) {

                    // procura na tabela se ja existe iD
                    var table = $('#dataTables-medicoes').dataTable();
                    row_count = table.fnGetData().length;
                    var linhas = table.fnGetData();

                    var achou = false;

                    for (var j = 0; j < row_count; j++) {

                        if (linhas[j][0] == rowId) {
                            achou = true;
                        }
                    }

                    // insere na tabela se nao achou
                    if (achou == false) {
                        $('#dataTables-medicoes').dataTable().fnAddData([
                            rowId,
                            names_selected_medicoes_id_unid[index],
                            names_selected_medicoes_grupo[index],
                            names_selected_medicoes_unid[index],
                            names_selected_medicoes_med[index],
                            '<a href="#" class="confirm-delete-medicao link_preto"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>',
                            names_selected_medicoes_IDAnaliseGruposMed[index]]);

                    }
                });

                // apaga arrays
                rows_selected_medicoes = [];
                names_selected_medicoes_id_unid = [];
                names_selected_medicoes_grupo = [];
                names_selected_medicoes_unid = [];
                names_selected_medicoes_med = [];

                names_selected_medicoes_IDAnaliseGruposMed = [];

                // desmarca checkbox
                var boxes = document.getElementsByName("checkbox_modal_medicoes");
                for (var i = 0; i < boxes.length; i++)
                    boxes[i].checked = false;                

                var chkbox_select_all = $('thead input[name="select_all"]', $('#dataTables-medicoes2')).get(0);
                chkbox_select_all.indeterminate = false;
                chkbox_select_all.checked = false;

                // remove da lista
                $('#dataTables-medicoes2').dataTable().$('tr.selected').empty();
                $('#dataTables-medicoes2').dataTable().draw();

                // desenha tabela
                $('#dataTables-medicoes').dataTable().draw();
            }

            //
            // BOTAO APAGAR MEDICAO
            //

            $('#dataTables-medicoes').on('click', '.confirm-delete-medicao', function (e) {
                e.preventDefault();

                // linha
                var row = $(this).closest('tr');

                // le dados da linha
                var data = $('#dataTables-medicoes').DataTable().row(row).data();

                var rowId = data[0];
                var id_unid = data[1];
                var grupo = data[2];
                var unidade = data[3];
                var medicao = data[4];


                // titulo
                titulo = "Deseja excluir a medição da lista?";

                swal({
                    html: true,
                    title: titulo,
                    text: "Esta operação não poderá ser desfeita.",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#18a689",
                    confirmButtonText: "Excluir",
                    cancelButtonText: "Cancelar",
                    closeOnConfirm: true
                }, function () {

                    setTimeout(function () {

                        // insere no modal
                        $('#dataTables-medicoes2').dataTable().fnAddData([
                            rowId,
                            rowId,
                            id_unid,
                            grupo,
                            unidade,
                            medicao]);

                        // apaga linha solicitada
                        $('#dataTables-medicoes').dataTable().fnDeleteRow(row);

                        // desenha tabela
                        $('#dataTables-medicoes').dataTable().draw();

                        // desenha tabela
                        $('#dataTables-medicoes2').dataTable().draw();

                    }, 100);

                });

            });
           
            //
            // BOTAO SALVAR (SUBMIT)
            //
        
            $.fn.serializeObject = function () {
                var o = {};
                var a = this.serializeArray();
                $.each(a, function () {
                    if (o[this.name] !== undefined) {
                        if (!o[this.name].push) {
                            o[this.name] = [o[this.name]];
                        }
                        o[this.name].push(this.value || '');
                    } else {
                        o[this.name] = this.value || '';
                    }
                });
                return o;
            };

            // verifica se habilita demanda residual
            function ShowDemandaResidual(chkbox) {
                                
                if (chkbox.checked) {
                    $("#DemandaResidual").attr('disabled', true);
                    $("#DemandaResidual").val("");
                }
                else {
                    $("#DemandaResidual").attr('disabled', false);                    
                }                
            };
          
            function Salvar() {           

                // criei um campo auxiliar para tratar do checkBox, pois quando faz o JSON.stringify envia um array de estados [true,false] do checkbox em vez de somente um TRUE
                var chkBox = document.getElementById('CalcResidualAutoCheck');
                if (chkBox.checked) {
                    document.getElementById("CalcResidualAuto").value = true;
                }
                else {
                    document.getElementById("CalcResidualAuto").value = false;
                }

                var $form = $('#form');

                // verifica se entradas sao validas
                if (!$form.valid()) return false;

                // lista de medicoes
                var oTable = $('#dataTables-medicoes').dataTable();
                var rows = oTable.fnSettings().aoData;
                var id = 0;
                var IDAnaliseGruposMed = 0;
                var dataArray = [];

                // percorre tabela
                $.each(rows, function (i, val) {
                    
                    // pega valores
                    IDAnaliseGruposMed = parseInt(val._aData[6]);
                    IDMedicao = parseInt(val._aData[0]);

                    dataArray.push({
                        "IDAnaliseGruposMed": IDAnaliseGruposMed,
                        "IDMedicao": IDMedicao,
                    });
                    
                });

                // deseja salvar
                swal({
                    title: "Deseja salvar?",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#1ab394",
                    confirmButtonText: "Salvar",
                    cancelButtonText: "Cancelar",
                    closeOnConfirm: true
                }, function () {
                    event.stopPropagation();

                    // aguarde
                    $('.overlay_aguarde').toggle();

                    // retiro campos desabilitados, pois nao envia via POST
                    $(":disabled", $('#form')).removeAttr("disabled");
                    
                    // constroi estrutura com FORM e lista de medicoes
                    data = { 'analiseGruposMed': $form.serializeObject(), 'analiseGruposMedEA': dataArray };
                    data2 = JSON.stringify(data);

                    $.ajax({
                        contentType: 'application/json; charset=utf-8',
                        dataType: 'json',
                        url: '/Analises/AnaliseHorarioConsGruposMed_Salvar',
                        data: data2,
                        type: 'POST',
                        success: function (data) {

                            // fim
                            $('.overlay_aguarde').toggle();

                            setTimeout(function () {

                                // verifica se erro
                                if (data.status == "ERRO") {
                                    swal({
                                        html: true,
                                        title: "Erro",
                                        text: data.erro,
                                        type: "warning",
                                        confirmButtonColor: "#f8ac59",
                                        confirmButtonText: "Fechar",
                                    }, function () {

                                        // redireciona para pagina lista de configuracao das analises
                                        var url = '/Analises/AnaliseHorarioCons_Editar?IDAnaliseHorarioCons=' + document.getElementById('IDAnaliseHorarioCons').value;
                                        window.location.href = url;
                                    });
                                }
                                else {
                                        // redireciona para pagina lista de configuracao das analises
                                        var url = '/Analises/AnaliseHorarioCons_Editar?IDAnaliseHorarioCons=' + document.getElementById('IDAnaliseHorarioCons').value;
                                        window.location.href = url;                                    
                                }

                            }, 100);

                        },
                        error: function (xhr, status, error) {

                            // fim
                            $('.overlay_aguarde').toggle();

                            setTimeout(function () {

                                swal({
                                    title: "Erro",
                                    text: "Erro ao salvar!",
                                    type: "warning",
                                    confirmButtonColor: "#f8ac59",
                                    confirmButtonText: "Fechar",
                                });

                            }, 100);

                        }
                    });
                });

            };

    </script>
}
