﻿<h2>Análise do Horário de Consumo</h2>
<img src="~/Imagens/Ajuda/Analise_HorarioConsumo.png" class="img-responsive center-block" /><br />
<b>(1)</b> O usuário poderá gerar um relatório de Análise do Horário de Consumo escolhendo um determinado período.<br />
<b>(2)</b> Permite que o usuário navegue entre as Análises configuradas para este cliente.<br />

<b>(3)</b>A tabela apresenta os dados da Análise do Horário de Consumo selecionada.
<p>É possível ter uma visão geral da Análise do Horário de Consumo através dos seguintes parâmetros:</p>
<tab1><b>- Medições:</b> Nome da Medição analisada.</tab1><br />
<tab1><b>- Unidades:</b> Nome da Unidade.</tab1><br />
<tab1><b>- Referência Início:</b> Horário Referência Início configurado para esta Medição.<br /></tab1>
<tab1><b>- Referência Fim:</b> Horário Referência Fim configurado para esta Medição.</tab1><br />
<tab1><b>- Início:</b> Horário do primeiro registro do dia, em que o valor de demanda registrado ultrapassou o valor de Demanda Residual determinado para está medição. Considerando assim, o início do período de consumo normal.</tab1><br />
<tab1><b>- Fim:</b> Horário do último registro do dia, em que o valor de demanda registrado ultrapassou o valor de Demanda Residual determinado para está medição. Considerando assim, o fim do período de consumo normal.</tab1><br />
<tab1><b>- Percentual de Demanda (%):</b> Percentual de Demanda do dia analisado.</tab1><br />
<tab1><b>- Residual (kW):</b> Valor da Demanda Residual determinado para esta Medição, calculado pelo sistema, ou inserido pelo usuário que efetuou a configuração.</tab1><br />
<tab1><b>- Liga:</b> Verifica se a medição iniciou seu perfil de consumo padrão após o horário de Referência Início. E retorna uma das seguintes mensagens:<br /> 
    <tab1><b>“OK”</b>, caso o horário do campo início seja maior ou igual ao horário do campo referência início.</tab1><br />
    <tab1><b>“Não OK”</b>, Caso o horário do campo Início seja menor que o horário do campo Referência Início.</tab1><br />
    <tab1><b>“Sem Registros”</b>, Em caso de falta de registros no dia analisado.</tab1>
    </tab1><br />
<tab1><b>- Desliga:</b> Verifica se a medição finalizou seu perfil de consumo padrão após o horário de Referência Fim. E retorna uma das seguintes mensagens:<br />
    <tab1><b>“OK”</b>, Caso o horário do campo Fim seja menor ou igual ao horário do campo Referência Fim.</tab1><br />
    <tab1><b>“Não OK”</b>, caso o horário do campo fim seja maior que o horário do campo referência início.</tab1><br />
    <tab1><b>“Sem registros”</b>, em caso de falta de registros no dia analisado</tab1>
    </tab1><br />
<tab1><b>- Status:</b> Verifica a situação do campo Liga e Desliga, e retorna um status geral desta medição:<br />
    <tab1><b>“OK”</b>, Caso os campos Liga E Desliga estiverem “OK”.</tab1><br />
    <tab1><b>“Não OK”</b>, Caso o campo Liga OU Desliga estiverem “Não OK”.</tab1><br />
    <tab1><b>“Sem Registros”</b>, Em caso de falta de registros no dia analisado.</tab1>  
    </tab1><br />
<tab1><b>Temperatura (ºC):</b> Apresenta a média dos registros das medições de temperatura adicionadas, no dia analisado.</tab1><br />
<br />

@Html.Partial("~/Views/Ajuda/_Ajuda_Ferramentas.cshtml")
<div class="row">
    <div class="col-lg-offset-1 col-lg-2">
        <i class="fa fa-cog"></i>
    </div>
    <div class="col-lg-8">
        Direciona para a página de configuração da Análise do Horário de Consumo.
    </div>
</div>