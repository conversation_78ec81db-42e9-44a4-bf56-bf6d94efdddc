﻿<h2>An<PERSON><PERSON>e Demanda de Contrato Ideal</h2>
<img src="~/Imagens/Ajuda/Analise_Demanda.png" class="img-responsive center-block" /><br />
<b>(1)</b> O usuário poderá gerar um relatório de Análise da Demanda de Contrato Ideal escolhendo um determinado período fim. O período início será definido automaticamente como um ano antes do período fim escolhido, pois a análise é feita levando em consideração os registros da medição no intervalo de um ano.<br /><br />
<p>É possível ter uma visão geral de um relatório de Análise Demanda de Contrato Ideal através das seguintes informações:</p>
<b>(2)</b> Apresenta o valor de Contrato de Demanda Sugerido, a Economia Média Mensal no período definido e a Economia Total no período definido.<br />
<b>(3)</b> Apresenta um gráfico com a relação entre o valor da Fatura Média Mensal e o valor do Contrato de Demanda. Este gráfico tem como base valores de simulação para o Contrato de Demanda, demonstrando a possível curva de economia com a alteração sugerida.<br />
<b>(4)</b> Apresenta uma tabela com a comparação de valores de faturamento utilizando o Contrato de Demanda vigente (configurado atualmente na medição), e o Contrato de Demanda Ideal sugerido, separados por:<br />
<tab1><b>- Contrato (kW):</b> Valor do Contrato de Demanda vigente.</tab1><br />
<tab1><b>- Fatura (R$):</b> Valor total da fatura de cada mês, calculada com o Contrato de Demanda vigente.</tab1><br />
<tab1><b>- Contrato Sugerido (kW):</b> Valor do Contrato de Demanda Ideal sugerido pelo sistema.</tab1><br />
<tab1><b>- Fatura (R$):</b> Valor total da fatura de cada mês, calculada com o Contrato de Demanda Ideal sugerido pelo sistema.</tab1><br />
<tab1><b>- Economia (R$):</b> Valor da diferença das faturas geradas utilizando o Contrato de Demanda vigente, e as faturas geradas utilizando Contrato de Demanda Ideal sugerido pelo sistema. O cálculo desse valor é feito utilizando os meses do ano seleciona como base, Simulando a economia de cada mês caso o Contrato de Demanda Ideal fosse utilizado.</tab1><br />
@Html.Partial("~/Views/Ajuda/_Ajuda_Ferramentas.cshtml")
