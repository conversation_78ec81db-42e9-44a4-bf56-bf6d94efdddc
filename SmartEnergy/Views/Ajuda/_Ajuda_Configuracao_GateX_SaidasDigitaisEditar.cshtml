﻿<h2>Configuração da Saída Digital</h2>
<p>É possível alterar as configurações da Saída Digital do equipamento através dos seguintes itens:</p>
<br />
<i class="fa fa-cogs" style="font-size: 18px; font-weight: bold; margin: 2px"></i> <b>Geral</b><br /><br />
<b>Descrição:</b> Nome que será utilizado para descrever a saída.<br />
<b>Rede:</b> A rede do equipamento SmartGate X que será responsável pela comunicação entre a Saída Digital e o equipamento. Tipos de rede:<br />
<tab1><b>- Rede [R]</b></tab1><br />
<tab1><b>- Rede [Q]</b></tab1><br />
<tab1><b>- Rede [K]</b></tab1><br />
<tab1><b>- DO</b></tab1><br />
<b>Remota:</b> Número da remota que será configurado para esta saída, este número varia de acordo com a Rede configurada:<br />
<b>Rede [R] / Rede [Q]:</b><br />
<tab1>- Remota: 0 - 31</tab1><br />
<b>Rede [K]:</b><br />
<tab1>- Remota: [A,0] ou [B,9]</tab1><br />
<b>Endereço:</b> Caso a Saída Digital seja utilizada para comunicar com um equipamento, o endereço de driver deste equipamento é configurado neste campo. Em caso de maiores dúvidas a respeito do endereço de driver, consulte o manual do equipamento ou entre em contato com nosso suporte.<br />
<b>Registrar Eventos de Temporização:</b> Ao selecionar, a Saída Digital será programa para Registrar os Eventos de Temporização.<br />
<b>Lógica Invertida:</b> Ao selecionar, a Saída Digital será configurada com a lógica invertida.<br />
<b>Acesso Remoto:</b> Ao selecionar, permite que a Saída Digital seja acessada remotamente.<br />
<b>Comando pela Entrada de Estado:</b> Permite que a Saída Digital seja comandada através do estado de uma determinada Entrada Digital configurada no equipamento.<br />
<b>Comando pelo Horário de Tarifação:</b> Permite que a Saída Digital seja comandada através de um determinado Horário de Tarifação configurado no equipamento.<br />
<b>Desligar em Falha da Medição:</b> Permite que a Saída Digital seja desligada caso ocorra uma falha de uma determinada Medição de Energia configurada no equipamento.<br />
<b>Alarmes:</b> Permite que a Saída Digital seja comandada através de um determinado Alarme.<br />
<b>Comando:</b> A lógica que será utilizada para efetuar o comando da Saída Digital, de acordo com o Alarme selecionado.<br />
<b>Alarme:</b> Seleciona um dos seguintes grupos de Alarme: <br />
<tab1><b>- Alarme de Usuário</b></tab1><br />
<tab1><b>- Sistema</b></tab1><br />
<tab1><b>- Falta de Pulsos</b></tab1><br />
<tab1><b>- Tendência de Ultrapassagem de Demanda</b></tab1><br />
<tab1><b>- Tendência de Ultrapassagem de Fator de Potência Indutivo</b></tab1><br />
<tab1><b>- Tendência de Ultrapassagem de Fator de Potência Capacitivo</b></tab1><br />
<tab1><b>- Comando Supervisionado</b></tab1><br />
<b>Comando Supervisionado:</b> Configura uma Entrada Digital para fazer a retrosinalização do estado da Saída Digital, disparando um Alarme caso a Saída Digital não tenha efetuado o comando desejado após um determinado tempo.<br />
<b>Tempo para Alarme:</b> Define o tempo desejado para disparar o Alarme configurado no Comando Supervisionado.<br /><br />

<i class="fa fa-clock-o" style="font-size: 18px; font-weight: bold; margin: 2px"></i> <b>Temporizadores</b><br /><br />

<b>Atenção!</b> Os parâmetros configurados na aba de Temporizadores sobrepõem as demais lógicas de comando da Saída Digital.<br />
