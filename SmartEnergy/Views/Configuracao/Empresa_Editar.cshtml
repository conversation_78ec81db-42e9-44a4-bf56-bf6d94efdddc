﻿@model SmartEnergyLib.SQL.EmpresasDominio

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = @SmartEnergy.Resources.ConfiguracaoTexts.AgentesFiliais;
}


@{
    int IDTipoAcesso = ViewBag._IDTipoAcesso;
    int IDConsultor = ViewBag._IDConsultor;

    string display_cpfl = "none";

    // somente CPFL e GESTAL
    if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDConsultor == CLIENTES_ESPECIAIS.CPFL)
    {
        display_cpfl = "block";
    }
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("Empresa_Editar", "Configuracao", FormMethod.Post, new { id = "form", role = "form" }))
            {
                @Html.Hidden("IDCliente", Model.IDCliente)
                @Html.Hidden("DataMigracao_Previsao", Model.DataMigracao_Previsao)
                @Html.Hidden("DataMigracao", Model.DataMigracao)
                @Html.Hidden("Contrato_Inicio", Model.Contrato_Inicio)
                @Html.Hidden("Contrato_Fim", Model.Contrato_Fim)
                @Html.Hidden("Base_Data", Model.Base_Data)
                @Html.Hidden("FechamentoTR", Model.FechamentoTR)
                @Html.Hidden("MesRescisao", Model.MesRescisao)
                @Html.Hidden("UltimoMesFaturamento", Model.UltimoMesFaturamento)
                @Html.Hidden("RenovacaoAutomatica", Model.RenovacaoAutomatica)
                

                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>@SmartEnergy.Resources.ConfiguracaoTexts.AgentesFiliais</h4>
                        <div class="pull-right dashboard-tools link_branco" style="margin-top:-8px;">
                            <h4>

                                @{
                                    int IDCliente = ViewBag._IDCliente;

                                    if (IDCliente > 0)
                                    {
                                        <a href='@("/Configuracao/Menu?IDCliente=" + @IDCliente.ToString())' title="Menu @SmartEnergy.Resources.ConfiguracaoTexts.Configuracoes @SmartEnergy.Resources.ConfiguracaoTexts.ClienteAtual"><i class="fa fa-th-large"></i></a>
                                    }
                                }

                            </h4>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="form-group col-lg-2">
                                <label class="control-label">&nbsp;ID</label>

                                @{
                                    if (Model.IDEmpresa == 0)
                                    {
                                        @Html.Hidden("IDEmpresa", Model.IDEmpresa)
                                        @Html.TextBox("Novo", "Nova Empresa", new { @class = "form-control", @disabled = "disabled" })
                                    }
                                    else
                                    {
                                        @Html.TextBoxFor(model => model.IDEmpresa, new { @class = "form-control", @disabled = "disabled" })
                                    }
                                }

                            </div>
                            <div class="col-lg-offset-4 col-lg-6">
                                <div class="ibox-tools">
                                    <a href='@(ViewBag.returnUrl)' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                    <button type="button" class="btn btn-primary" id="BotaoSalvar" onclick="Salvar();" disabled="">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                </div>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="tabs-container">
                                    <ul class="nav nav-tabs">
                                        <li class="active"><a data-toggle="tab" href="#tab-1"><i class="fa fa-cogs"></i> @SmartEnergy.Resources.ConfiguracaoTexts.Geral</a></li>

                                        @{
                                            // somente CPFL e GESTAL
                                            if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDConsultor == CLIENTES_ESPECIAIS.CPFL)
                                            {
                                                <li class="tab-2"><a data-toggle="tab" href="#tab-2"><i class="fa fa-file-text-o"></i> @SmartEnergy.Resources.ConfiguracaoTexts.ContratoGestao</a></li>
                                            
                                                // somente CPFL e GESTAL
                                                if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.CONSULTOR || IDTipoAcesso == TIPO_ACESSO.CONSULTOR_ADMIN || IDTipoAcesso == TIPO_ACESSO.CONSULTOR_OPER)
                                                {
                                                    <li class="tab-3"><a data-toggle="tab" href="#tab-3"><i class="fa fa-calendar"></i> PROINFA</a></li>
                                                }                                            
                                            }
                                        }

                                        <li class="tab-4"><a data-toggle="tab" href="#tab-4"><i class="fa fa-map-marker"></i> @SmartEnergy.Resources.ConfiguracaoTexts.Endereco&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<i class="fa fa-user"></i> @SmartEnergy.Resources.ConfiguracaoTexts.Contato</a></li>

                                    </ul>
                                    <div class="tab-content">

                                        <div id="tab-1" class="tab-pane active">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.SiglaCCEE</label>

                                                        @{
                                                            if (Model.IDTipoEmpresa == TIPO_EMPRESA.AgenteCCEE)
                                                            {
                                                                @Html.TextBoxFor(model => model.SiglaCCEE, new { @class = "form-control", @maxlength = "50" })
                                                            }
                                                            else
                                                            {
                                                                @Html.TextBoxFor(model => model.SiglaCCEE, new { @class = "form-control", @maxlength = "50", @disabled = "disabled" })
                                                            }
                                                        }

                                                    </div>
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.RazaoSocial</label>
                                                        @Html.TextBoxFor(model => model.RazaoSocial, new { @class = "form-control", @maxlength = "100", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-2">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Agente</label><br />
                                                        @Html.DropDownListFor(model => model.Agente, new SelectList(ViewBag.listaTipoSimNao, "ID", "Descricao"),
                                                            string.Format("{0}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.TipoEmpresa</label>
                                                        @Html.DropDownListFor(model => model.IDTipoEmpresa, new SelectList(ViewBag.listaTipoEmpresa, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.TipoEmpresa),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;CNPJ</label>
                                                        @Html.TextBoxFor(model => model.CNPJ, new { @class = "form-control", data_mask = "99.999.999/9999-99", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                                <hr />

                                                <div class="row">
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Agente [Matriz] - @SmartEnergy.Resources.ConfiguracaoTexts.SiglaCCEE</label>
                                                        @Html.DropDownListFor(model => model.IDEmpresa_AgenteCCEE, new SelectList(ViewBag.listaAgentesCCEE, "IDEmpresa", "SiglaCCEE"), "---", new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div id="AgenteCCEEDIV" style="visibility:hidden">
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.RazaoSocial</label>
                                                            <select class="form-control" id="AgenteCCEE_RazaoSocial" name="AgenteCCEE_RazaoSocial" disabled="">
                                                                <option value="">---</option>

                                                                @{
                                                                    List<EmpresasDominio> agentesCCEE = ViewBag.listaAgentesCCEE;
                                                                    int IDEmpresa_AgenteCCEE = Model.IDEmpresa_AgenteCCEE;

                                                                    foreach (EmpresasDominio agente in agentesCCEE)
                                                                    {
                                                                        if (IDEmpresa_AgenteCCEE == agente.IDEmpresa)
                                                                        {
                                                                            <option value="@agente.IDEmpresa" selected="selected">@agente.RazaoSocial</option>
                                                                        }
                                                                        else
                                                                        {
                                                                            <option value="@agente.IDEmpresa">@agente.RazaoSocial</option>
                                                                        }
                                                                    }
                                                                }

                                                            </select>

                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;CNPJ</label>
                                                            <select class="form-control" id="AgenteCCEE_CNPJ" name="AgenteCCEE_CNPJ" disabled="">
                                                                <option value="">---</option>

                                                                @{
                                                                    foreach (EmpresasDominio agente in agentesCCEE)
                                                                    {
                                                                        if (IDEmpresa_AgenteCCEE == agente.IDEmpresa)
                                                                        {
                                                                            <option value="@agente.IDEmpresa" selected="selected">@agente.CNPJ</option>
                                                                        }
                                                                        else
                                                                        {
                                                                            <option value="@agente.IDEmpresa">@agente.CNPJ</option>
                                                                        }
                                                                    }
                                                                }

                                                            </select>
                                                        </div>
                                                        <div class="form-group col-lg-1">
                                                            <label class="control-label">&nbsp;ID</label>
                                                            <select class="form-control" id="AgenteCCEE_ID" name="AgenteCCEE_ID" disabled="">
                                                                <option value="">---</option>

                                                                @{
                                                                    foreach (EmpresasDominio agente in agentesCCEE)
                                                                    {
                                                                        if (IDEmpresa_AgenteCCEE == agente.IDEmpresa)
                                                                        {
                                                                            <option value="@agente.IDEmpresa" selected="selected">@agente.IDEmpresa</option>
                                                                        }
                                                                        else
                                                                        {
                                                                            <option value="@agente.IDEmpresa">@agente.IDEmpresa</option>
                                                                        }
                                                                    }
                                                                }

                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <hr />

                                                <div style="display:@display_cpfl ;">
                                                    <div class="row">

                                                        @{
                                                            if (Model.IDTipoEmpresa == TIPO_EMPRESA.AgenteCCEE)
                                                            {
                                                                <div class="form-group col-lg-4">
                                                                    <label class="control-label">&nbsp;ID CCEE</label>
                                                                    @Html.TextBoxFor(model => model.IDCCEE, new { @class = "form-control", @maxlength = "10" })
                                                                </div>
                                                                <div class="form-group col-lg-4">
                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.SenhaAtendimento</label>
                                                                    @Html.TextBoxFor(model => model.Senha_Atendimento, new { @class = "form-control", @maxlength = "10" })
                                                                </div>
                                                                <div class="form-group col-lg-4">
                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.SenhaRepresentante</label>
                                                                    @Html.TextBoxFor(model => model.Senha_Representante, new { @class = "form-control", @maxlength = "10" })
                                                                </div>
                                                            }
                                                            else
                                                            {
                                                                <div class="form-group col-lg-4">
                                                                    <label class="control-label">&nbsp;ID CCEE</label>
                                                                    @Html.TextBoxFor(model => model.IDCCEE, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                                </div>
                                                                <div class="form-group col-lg-4">
                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.SenhaAtendimento</label>
                                                                    @Html.TextBoxFor(model => model.Senha_Atendimento, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                                </div>
                                                                <div class="form-group col-lg-4">
                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.SenhaRepresentante</label>
                                                                    @Html.TextBoxFor(model => model.Senha_Representante, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                                </div>
                                                            }
                                                        }

                                                    </div>
                                                    <div class="row">

                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;Score</label>

                                                            @{
                                                                if (Model.IDTipoEmpresa == TIPO_EMPRESA.AgenteCCEE)
                                                                {
                                                                    @Html.DropDownListFor(model => model.Score, new SelectList(ViewBag.listaTipoScore, "ID", "Descricao"),
                                                                        "---", new { @class = "form-control" })
                                                                }    
                                                                else
                                                                {
                                                                    @Html.DropDownListFor(model => model.Score, new SelectList(ViewBag.listaTipoScore, "ID", "Descricao"),
                                                                        "---", new { @class = "form-control", @disabled = "disabled" })
                                                                }                                                            
                                                            }

                                                        </div>
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.IDPerfil</label>
                                                            @Html.TextBoxFor(model => model.IDPerfil, new { @class = "form-control", @maxlength = "15", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.DataMigracaoPrevisao</label>
                                                            <div class="input-group date">
                                                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>@Html.TextBoxFor(model => model.DataMigracao_Previsao_Texto, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                            </div>
                                                        </div>
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.DataMigracao</label>
                                                            <div class="input-group date">
                                                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>@Html.TextBoxFor(model => model.DataMigracao_Texto, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <hr />
                                                </div>

                                                <div class="row">
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Logo</label><br />
                                                        @Html.TextBoxFor(model => model.Logo, new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-3" id="BotaoUploadDIV" style="visibility:hidden">
                                                        <div id="progress" class="progress">
                                                            <div class="progress-bar progress-bar-success bar" style="width: 0%; height:20px;"></div>
                                                        </div>
                                                        <span id="BotaoUpload" class="btn btn-primary fileinput-button" style="height:30px; width:100%; margin-top:-18px; padding-top:4px;">
                                                            <span>Selecione a Logomarca</span>
                                                            @Html.TextBoxFor(model => model.Logo, new { id = "fileupload", name = "fileupload", type = "file" })
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="form-group col-lg-2">

                                                        @{
                                                            string logo;

                                                            if (Model.Logo.IsEmpty())
                                                            {
                                                                logo = "/Logos/LogoSmartEnergy.png";
                                                            }
                                                            else
                                                            {
                                                                logo = "/Logos/" + Model.Logo;
                                                            }
                                                        }

                                                        <img src=@logo id="LogoImagem" class="img-thumbnail" style="margin-top:0px; background-color:#233545; max-height:65px; height: auto;" />
                                                    </div>
                                                    <div class="form-group col-lg-offset-1 col-lg-3" id="DimensoesDIV" style="visibility:hidden">
                                                        <label>(Dimensões 120x45 pixels)</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div id="tab-2" class="tab-pane">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ContratoStatus</label>
                                                        @Html.DropDownListFor(model => model.Contrato_Status, new SelectList(ViewBag.listaTipoContratoStatus, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.ContratoStatus),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Gestao</label>
                                                        @Html.DropDownListFor(model => model.Contrato_Gestora, new SelectList(ViewBag.listaTipoComercializadoras, "IDAgente", "Nome"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.ComercializadoraGestao),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Carteira</label>
                                                        @Html.DropDownListFor(model => model.Carteira, new SelectList(ViewBag.listaTipoCarteira, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.Carteira),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Escopo</label>
                                                        @Html.DropDownListFor(model => model.Escopo, new SelectList(ViewBag.listaTipoEscopo, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.Escopo),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                                <hr />
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ContratoInicio</label>
                                                        <div class="input-group date">
                                                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>@Html.TextBoxFor(model => model.Contrato_Inicio_Texto, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ContratoFim</label>
                                                        <div class="input-group date">
                                                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>@Html.TextBoxFor(model => model.Contrato_Fim_Texto, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.MesRescisao</label>
                                                        <div class="input-group date">
                                                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>@Html.TextBoxFor(model => model.MesRescisao_Texto, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.UltimoMesFaturamento</label>
                                                        <div class="input-group date">
                                                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>@Html.TextBoxFor(model => model.UltimoMesFaturamento_Texto, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.BaseValor (R$)</label>
                                                        @Html.TextBoxFor(model => model.Base_Valor, "{0:0.00}", new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.BaseData</label>
                                                        <div class="input-group date">
                                                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>@Html.TextBoxFor(model => model.Base_Data_Texto, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ReajusteMes</label>
                                                        @Html.DropDownListFor(model => model.Reajuste_Mes, new SelectList(ViewBag.listaTipoMeses, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.ReajusteMes),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ReajusteIndice</label>
                                                        @Html.DropDownListFor(model => model.Reajuste_Indice, new SelectList(ViewBag.listaTipoIndiceReajuste, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.ReajusteIndice),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ReajusteValor (R$)</label>
                                                        @Html.TextBoxFor(model => model.Reajuste_Valor, "{0:0.00}", new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="link_branco" style="margin-top:10px;">
                                                            <button type="button" id="BotaoPrecosReajustados" class="btn btn-info btn-lg pull-left" onclick="ModalPrecosReajustados();" style="color:#ffffff; width:100%;" disabled="">
                                                                <span style="font-size:large">@SmartEnergy.Resources.ConfiguracaoTexts.ReajusteValores</span>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.DiaVencimento</label>
                                                        @Html.DropDownListFor(model => model.Vencimento_Dias, new SelectList(ViewBag.listaTipoDias, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.DiaVencimento),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.TipoVencimento</label>
                                                        @Html.DropDownListFor(model => model.Vencimento_Tipo, new SelectList(ViewBag.listaTipoVencimento, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.TipoVencimento),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.RenovacaoAutomatica</label><br />
                                                        <div class="i-checks" style="margin-top:6px;">
                                                            <input id="RenovacaoAutomaticaAux" name="RenovacaoAutomaticaAux" type="checkbox" @(Model.RenovacaoAutomatica == true ? "checked" : "")>&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ConfiguracaoTexts.RenovacaoAutomatica</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.FechamentoTR</label>
                                                        <div class="input-group date">
                                                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>@Html.TextBoxFor(model => model.FechamentoTR_Texto, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                </div>
                                                <hr />
                                                <div class="row">
                                                    <div class="form-group col-lg-12">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Observacoes</label>
                                                        @Html.TextAreaFor(model => model.Observacao, new { @class = "form-control", @maxlength = "500", style = "height: 200px;", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div id="tab-3" class="tab-pane">
                                            <div class="panel-body">

                                                @if (Model.IDEmpresa == 0) {
                                                    
                                                    <br />
                                                    <div class="row">
                                                        <div class="col-lg-1" style="text-align:center;">
                                                            <i class="fa fa-warning fa-5x"></i>
                                                        </div>
                                                        <div class="col-lg-11">
                                                            <h2>É necessário salvar a Matriz/Filial para editar o PROINFA.</h2>
                                                        </div>
                                                    </div>
                                                    <br />
                                                    
                                                }
                                                else
                                                {

                                                    <div class="row">
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Medida</label>
                                                            <select class="form-control" id="Unidade_PROINFA" name="Unidade_PROINFA">
                                                                <option value="0" selected="selected">MWh</option>
                                                                <option value="1">MWm</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-lg-offset-5 col-lg-3" style="margin-top:10px;">
                                                            <div class="link_branco">
                                                                <button type="button" id="BotaoAdicionarPROINFA" class="btn btn-info btn-lg pull-left" onclick="Adicionar_PROINFA();" style="color:#ffffff; width:100%;" disabled="">
                                                                    <span style="font-size:large">@SmartEnergy.Resources.ConfiguracaoTexts.AdicionarPROINFA</span>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <br />

                                                    <div class="div_PROINFA style="display:block;">
                                                        <div class="row">
                                                            <div class="form-group col-lg-12">
                                                                <table id="dataTables-PROINFA" class="table table-striped table-bordered table-hover">
                                                                    <thead>
                                                                        <tr>
                                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Periodo</th>
                                                                            <th>Jan</th>
                                                                            <th>Fev</th>
                                                                            <th>Mar</th>
                                                                            <th>Abr</th>
                                                                            <th>Mai</th>
                                                                            <th>Jun</th>
                                                                            <th>Jul</th>
                                                                            <th>Ago</th>
                                                                            <th>Set</th>
                                                                            <th>Out</th>
                                                                            <th>Nov</th>
                                                                            <th>Dez</th>
                                                                            <th>Total</th>
                                                                            <th></th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>

                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="div_PROINFA_MWm" style="display:none;">
                                                        <div class="row">
                                                            <div class="form-group col-lg-12">
                                                                <table id="dataTables-PROINFA-MWm" class="table table-striped table-bordered table-hover">
                                                                    <thead>
                                                                        <tr>
                                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Periodo</th>
                                                                            <th>MWm</th>
                                                                            <th></th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>

                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <br />

                                                    <div class="row">
                                                        <div class="form-group col-lg-3">
                                                            <span id="BotaoUpload_PROINFA" class="btn btn-primary btn-lg fileinput-button" style="color:#ffffff; width:100%;">
                                                                <span class="modal-title"><i class="fa fa-upload"></i>&nbsp;&nbsp;Enviar PROINFA</span>
                                                                <input type="file" id="fileupload_PROINFA" name="fileupload_PROINFA">
                                                            </span>
                                                            <div id="progress_PROINFA" class="progress_PROINFA">
                                                                <div class="progress-bar progress-bar-success bar" style="width: 0%; height:20px;"></div>
                                                            </div>
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <button type="button" class="btn btn-primary btn-lg modal-title" onclick="DownloadPROINFA();" style="color:#ffffff; width:100%;"><i class="fa fa-download"></i>&nbsp;&nbsp;Receber PROINFA</button>
                                                        </div>
                                                    </div>

                                                }
                                                                                                    
                                            </div>
                                        </div>

                                        <div id="tab-4" class="tab-pane">
                                            <div class="panel-body">

                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <div class="row">
                                                            <div class="form-group col-lg-8">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Endereco</label>
                                                                @Html.TextBoxFor(model => model.Endereco, new { @class = "form-control", @maxlength = "100", @disabled = "disabled" })
                                                            </div>
                                                            <div class="form-group col-lg-4">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.CEP</label>
                                                                @Html.TextBoxFor(model => model.CEP, new { @class = "form-control", @maxlength = "9", @disabled = "disabled" })
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-4">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Pais</label>
                                                                @Html.DropDownListFor(model => model.IDPais, new SelectList(ViewBag.listaTipoPais, "IDPais", "Nome"),
                                                                    string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.Pais),
                                                                     new { @class = "form-control", @disabled = "disabled" })
                                                            </div>

                                                            <div class="div_BRASIL" style="display:block;">
                                                                <div class="form-group col-lg-4">
                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Estado</label>
                                                                    @Html.DropDownListFor(model => model.IDEstado, new SelectList(ViewBag.listaTipoEstado, "IDEstado", "Nome"),
                                                                        string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.Estado),
                                                                         new { @class = "form-control", @disabled = "disabled" })
                                                                </div>
                                                                <div class="form-group col-lg-4">
                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Cidade</label>
                                                                    @Html.DropDownListFor(model => model.IDCidade, new SelectList(ViewBag.listaTipoCidade, "IDCidade", "Nome"),
                                                                        string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.Cidade),
                                                                         new { @class = "form-control", @disabled = "disabled" })
                                                                </div>
                                                            </div>

                                                            <div class="div_ESTRANGEIRO" style="display:none;">
                                                                <div class="form-group col-lg-4">
                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Estado</label>
                                                                    @Html.TextBoxFor(model => model.NomeEstado, new { @class = "form-control", @maxlength = "75" })
                                                                </div>
                                                                <div class="form-group col-lg-4">
                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Cidade</label>
                                                                    @Html.TextBoxFor(model => model.NomeCidade, new { @class = "form-control", @maxlength = "120" })
                                                                </div>
                                                            </div>

                                                        </div>

                                                        <div class="div_SmartWeather" style="display:block;">
                                                            <div class="row">
                                                                <div class="form-group col-lg-6">

                                                                    @{
                                                                        List<CidadesDominio> listatiposCidade = ViewBag.listaTipoCidade;
                                                                        int IDCidade = Model.IDCidade;

                                                                        CidadesDominio cidade = listatiposCidade.Find(x => x.IDCidade == IDCidade);

                                                                        if (cidade != null)
                                                                        {
                                                                            if (cidade.SmartWeather)
                                                                            {
                                                                                <div class="i-checks">
                                                                                    <label>
                                                                                        <input type="checkbox" value="" disabled="" checked="">&nbsp;&nbsp;<span style="margin-right:14px;">Utilizar informações de Metereologia</span>
                                                                                    </label>
                                                                                </div>
                                                                            }
                                                                            else
                                                                            {
                                                                                <div class="i-checks">
                                                                                    <label>
                                                                                        <input type="checkbox" value="" disabled="">&nbsp;&nbsp;<span style="margin-right:14px;">Utilizar informações de Metereologia</span>
                                                                                    </label>
                                                                                </div>
                                                                            }
                                                                        }
                                                                    }

                                                                </div>
                                                            </div>
                                                        </div>
                                                        <br />
                                                        <div class="row">
                                                            <div class="form-group col-lg-8">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.FusoHorario</label>
                                                                @Html.DropDownListFor(model => model.IDTipoFusoHorario, new SelectList(ViewBag.listaTipoFusoHorario, "ID", "Descricao"),
                                                                                    string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.FusoHorario),
                                                                                    new { @class = "form-control", @disabled = "disabled" })
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="form-group col-lg-offset-1 col-lg-5" style="margin-bottom:0px;">
                                                        <div class="row">
                                                            <div class="form-group col-lg-6" style="margin-bottom:0px;">
                                                                <div class="row">
                                                                    <div class="form-group col-lg-12">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Latitude</label>
                                                                        @Html.TextBoxFor(model => model.Latitude, new { @class = "form-control", @maxlength = "12", @disabled = "disabled" })
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="form-group col-lg-12">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Longitude</label>
                                                                        @Html.TextBoxFor(model => model.Longitude, new { @class = "form-control", @maxlength = "12", @disabled = "disabled" })
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="form-group col-lg-12">
                                                                        <button type="button" class="btn btn-w-m btn-primary" id="BotaoEncontrarLatLong" onclick="EncontrarLatLong();" style="width:100%;" disabled="">@SmartEnergy.Resources.ConfiguracaoTexts.EncontrarLatLong</button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                <div class="quadro_mapa">
                                                                    <div class="google-map" id="map" style="height: 176px;"></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <hr />

                                                <div class="row">
                                                    <div class="col-lg-2">
                                                        <div class="link_branco">
                                                            <a id="BotaoAdicionarContato" data-toggle="modal" href="#ModalContatos" class="btn btn-info btn-lg pull-left" style="color:#ffffff; width:100%;">
                                                                <span style="font-size:large">@SmartEnergy.Resources.ConfiguracaoTexts.AdicionarContato</span>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="form-group col-lg-12">
                                                        <table id="dataTables-contatos" class="table table-striped table-bordered table-hover">
                                                            <thead>
                                                                <tr>
                                                                    <th>ID</th>
                                                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.Contatos</th>
                                                                    <th class="some_minidesktop">Email</th>
                                                                    <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Telefone</th>
                                                                    <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.Cargo</th>
                                                                    <th></th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>

                                                                @{
                                                                    List<ContatosDominio> contatos = ViewBag.Contatos;

                                                                    foreach (ContatosDominio contato in contatos)
                                                                    {
                                                                        <tr>
                                                                            <td>@contato.IDContato</td>
                                                                            <td>@contato.Nome</td>
                                                                            <td class="some_minidesktop">@contato.Email</td>
                                                                            <td class="some_minidesktop">@contato.Telefone</td>
                                                                            <td class="some_minidesktop">@contato.Cargo</td>
                                                                            <td class="link_preto">
                                                                                <a href="#" class="confirm-delete-contato"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>
                                                                            </td>
                                                                        </tr>
                                                                    }
                                                                }

                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal inmodal animated fadeIn" id="modalPrecosReajustados" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                                        <h4 class="modal-title"><i class="fa fa-dollar"></i>&nbsp;&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ReajusteValores</h4>
                                    </div>
                                    <div class="modal-body" style="min-height:100px;">
                                        <div class="row">
                                            <div class="form-group col-lg-12">
                                                <h2>Atualizar os <b>@SmartEnergy.Resources.ConfiguracaoTexts.ReajusteValores</b> através da planilha selecionada.</h2>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-12" id="BotaoUploadValoresDIV">
                                                <span id="BotaoUploadValores" class="btn btn-primary btn-lg fileinput-button" style="color:#ffffff; width:100%;">
                                                    <span class="modal-title"><i class="fa fa-upload"></i>&nbsp;&nbsp;Enviar</span>
                                                    <input type="file" id="fileuploadValores" name="fileuploadValores">
                                                </span>
                                                <div id="progress" class="progress">
                                                    <div class="progress-bar progress-bar-success bar" style="width: 0%; height:20px;"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <hr />

                                        <div class="row">
                                            <div class="form-group col-lg-12">
                                                <h2>Receber a planilha com os <b>@SmartEnergy.Resources.ConfiguracaoTexts.ReajusteValores</b> atuais.</h2>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-12">
                                                <button type="button" class="btn btn-primary btn-lg modal-title" onclick="DownloadPrecosReajustados();" style="color:#ffffff; width:100%;"><i class="fa fa-download"></i>&nbsp;&nbsp;Receber</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal inmodal animated fadeIn" id="ModalContatos" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Fechar</span></button>
                                        <h4 class="modal-title"><i class="fa fa-check-square-o"></i>&nbsp;&nbsp;@string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.Contato)</h4>
                                    </div>
                                    <div class="modal-body">

                                        <table id="dataTables-contatos2" class="table table-striped table-bordered table-hover">
                                            <thead>
                                                <tr>
                                                    <th><input name="select_all" value="1" type="checkbox"></th>
                                                    <th>ID</th>
                                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.Contatos</th>
                                                    <th class="some_minidesktop">Email</th>
                                                    <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Telefone</th>
                                                    <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.Cargo</th>
                                                </tr>
                                            </thead>
                                            <tbody>

                                                @{
                                                    List<ContatosDominio> contatos2 = ViewBag.Contatos2;

                                                    foreach (ContatosDominio contato in contatos2)
                                                    {
                                                        <tr>
                                                            <td>@contato.IDContato</td>
                                                            <td>@contato.IDContato</td>
                                                            <td>@contato.Nome</td>
                                                            <td class="some_minidesktop">@contato.Email</td>
                                                            <td class="some_minidesktop">@contato.Telefone</td>
                                                            <td class="some_minidesktop">@contato.Cargo</td>
                                                        </tr>
                                                    }
                                                }

                                            </tbody>
                                        </table>

                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                        <button type="button" class="btn btn-primary" onclick="fnClickAddRowContatos();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoInserir</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal inmodal animated fadeIn" id="ModalPROINFA_Editar" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Fechar</span></button>
                                        <h4 class="modal-title"><i class="fa fa-edit"></i>&nbsp;&nbsp;PROINFA (MWh)</h4>
                                    </div>
                                    <div class="modal-body">

                                        <div class="row">
                                            <div class="form-group col-lg-12">
                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Ano</label>
                                                <input class="form-control" id="Ano_PROINFA" name="Ano_PROINFA" type="text" value="" />
                                            </div>
                                        </div>
                                        <br />
                                        <div class="row">
                                            <div class="form-group col-lg-3">
                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Janeiro</label>
                                                <input class="form-control" id="Jan_PROINFA" name="Jan_PROINFA" type="text" value="" />
                                            </div>
                                            <div class="form-group col-lg-3">
                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Fevereiro</label>
                                                <input class="form-control" id="Fev_PROINFA" name="Fev_PROINFA" type="text" value="" />
                                            </div>
                                            <div class="form-group col-lg-3">
                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Março</label>
                                                <input class="form-control" id="Mar_PROINFA" name="Mar_PROINFA" type="text" value="" />
                                            </div>
                                            <div class="form-group col-lg-3">
                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Abril</label>
                                                <input class="form-control" id="Abr_PROINFA" name="Abr_PROINFA" type="text" value="" />
                                            </div>
                                        </div>
                                        <br />
                                        <div class="row">
                                            <div class="form-group col-lg-3">
                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Maio</label>
                                                <input class="form-control" id="Mai_PROINFA" name="Mai_PROINFA" type="text" value="" />
                                            </div>
                                            <div class="form-group col-lg-3">
                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Junho</label>
                                                <input class="form-control" id="Jun_PROINFA" name="Jun_PROINFA" type="text" value="" />
                                            </div>
                                            <div class="form-group col-lg-3">
                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Julho</label>
                                                <input class="form-control" id="Jul_PROINFA" name="Jul_PROINFA" type="text" value="" />
                                            </div>
                                            <div class="form-group col-lg-3">
                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Agosto</label>
                                                <input class="form-control" id="Ago_PROINFA" name="Ago_PROINFA" type="text" value="" />
                                            </div>
                                        </div>
                                        <br />
                                        <div class="row">
                                            <div class="form-group col-lg-3">
                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Setembro</label>
                                                <input class="form-control" id="Set_PROINFA" name="Set_PROINFA" type="text" value="" />
                                            </div>
                                            <div class="form-group col-lg-3">
                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Outubro</label>
                                                <input class="form-control" id="Out_PROINFA" name="Out_PROINFA" type="text" value="" />
                                            </div>
                                            <div class="form-group col-lg-3">
                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Novembro</label>
                                                <input class="form-control" id="Nov_PROINFA" name="Nov_PROINFA" type="text" value="" />
                                            </div>
                                            <div class="form-group col-lg-3">
                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Dezembro</label>
                                                <input class="form-control" id="Dez_PROINFA" name="Dez_PROINFA" type="text" value="" />
                                            </div>
                                        </div>
                                        <br />
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                        <button type="button" class="btn btn-primary" onclick="AlteraPROINFA();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoAlterar</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="overlay_aguarde" style="display: none">
                        <div class="spinner-aguarde">
                            <div class="sk-spinner sk-spinner-wandering-cubes">
                                <div class="sk-spinner sk-spinner-wave">
                                    <div class="sk-rect1 text-ligh"></div>
                                    <div class="sk-rect2"></div>
                                    <div class="sk-rect3"></div>
                                    <div class="sk-rect4"></div>
                                    <div class="sk-rect5"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            }

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
    @Styles.Render("~/datetimePickerStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
    @Styles.Render("~/jQuery.FileUpload/FileUploadStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/plugins/iCheck")
    @Scripts.Render("~/jQuery.FileUpload/FileUpload")
    @Scripts.Render("~/plugins/maskMoney")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <!--
     You need to include this script on any page that has a Google Map.
     When using Google Maps on your own site you MUST signup for your own API key at:
     https://developers.google.com/maps/documentation/javascript/tutorial#api_key
     After your sign up replace the key in the URL below..
     key = AIzaSyDQTpXj82d8UpCi97wzo_nKXL7nYrd4G70
    -->
    @Scripts.Render("https://maps.googleapis.com/maps/api/js?key=AIzaSyA5X1xuge28QKLNI9glB7Ki2cYnqx5bqaM")


    <script type="text/javascript">

    // When the window has finished loading google map
    google.maps.event.addDomListener(window, 'load', init);

    var map;
    var marker;

    function init() {
        // Options for Google map
        // More info see: https://developers.google.com/maps/documentation/javascript/reference#MapOptions
        var mapOptions1 = {
            zoom: 15,
            //center: new google.maps.LatLng(-23.5971252, -46.6385829),
            // Style for Google Maps
            styles: [{ "featureType": "water", "stylers": [{ "saturation": 43 }, { "lightness": -11 }, { "hue": "#0088ff" }] }, { "featureType": "road", "elementType": "geometry.fill", "stylers": [{ "hue": "#ff0000" }, { "saturation": -100 }, { "lightness": 99 }] }, { "featureType": "road", "elementType": "geometry.stroke", "stylers": [{ "color": "#808080" }, { "lightness": 54 }] }, { "featureType": "landscape.man_made", "elementType": "geometry.fill", "stylers": [{ "color": "#ece2d9" }] }, { "featureType": "poi.park", "elementType": "geometry.fill", "stylers": [{ "color": "#ccdca1" }] }, { "featureType": "road", "elementType": "labels.text.fill", "stylers": [{ "color": "#767676" }] }, { "featureType": "road", "elementType": "labels.text.stroke", "stylers": [{ "color": "#ffffff" }] }, { "featureType": "poi", "stylers": [{ "visibility": "off" }] }, { "featureType": "landscape.natural", "elementType": "geometry.fill", "stylers": [{ "visibility": "on" }, { "color": "#b8cb93" }] }, { "featureType": "poi.park", "stylers": [{ "visibility": "on" }] }, { "featureType": "poi.sports_complex", "stylers": [{ "visibility": "on" }] }, { "featureType": "poi.medical", "stylers": [{ "visibility": "on" }] }, { "featureType": "poi.business", "stylers": [{ "visibility": "simplified" }] }]
        };

        // Get all html elements for map
        var mapElement1 = document.getElementById('map');

        // geocoder
        geocoder = new google.maps.Geocoder();

        // Create the Google Map using elements
        map = new google.maps.Map(mapElement1, mapOptions1);

        // verifica se possui latitude e longitude
        var model = @Html.Raw(Json.Encode(Model));
        var latitude = model.Latitude;
        var longitude = model.Longitude;

        if (latitude != 0.0 && longitude != 0.0)
        {
            // cria marcador
            marker = new google.maps.Marker({
                position: { lat: latitude, lng: longitude },
                map: map
            });

            // posiciona no marcador
            map.setCenter(new google.maps.LatLng(latitude, longitude));
        }
        else
        {
            // tenta posicionar pelo endereco
            EncontrarLatLong();
        }
    }

    function EncontrarLatLong() {

        // monta endereco
        var endereco_completo = "";
        var endereco = document.getElementById("Endereco").value;
        if (endereco.length > 0)
        {
            endereco_completo += endereco;
        }

        // CEP
        var CEP = document.getElementById("CEP").value;
        if (CEP.length > 0) {
            if (endereco_completo.length > 0) {
                endereco_completo += " - " + CEP;
            }
            else {
                endereco_completo += CEP;
            }
        }

        // cidade
        //var cidade = $("#IDCidade option:selected").text();
        var cidade = document.getElementById("NomeCidade").value;
        if (cidade.length > 0)
        {
            if (endereco_completo.length > 0) {
                endereco_completo += " - " + cidade;
            }
            else {
                endereco_completo += cidade;
            }
        }

        // estado
        //var estado = $("#IDEstado option:selected").text();
        var estado = document.getElementById("NomeEstado").value;
        if (estado.length > 0) {
            if (endereco_completo.length > 0) {
                endereco_completo += " - " + estado;
            }
            else {
                endereco_completo += estado;
            }
        }

        // pais
        var pais = $("#IDPais option:selected").text();
        if (pais.length > 0) {
            if (endereco_completo.length > 0) {
                endereco_completo += " - " + pais;
            }
            else {
                endereco_completo += pais;
            }
        }
        // encontra endereco
        geocoder.geocode({ 'address': endereco_completo }, function (resultado, status) {
            if (status == google.maps.GeocoderStatus.OK) {

                // pega latitude e longitude
                var latitude = resultado[0].geometry.location.lat();
                var longitude = resultado[0].geometry.location.lng();

                // apaga marcador antigo
                if (marker != null)
                {
                    marker.setMap(null);
                }

                // atualiza marcador
                marker = new google.maps.Marker({
                    // The below line is equivalent to writing:
                    // position: new google.maps.LatLng(-34.397, 150.644)
                    position: { lat: latitude, lng: longitude },
                    map: map
                });

                // posiciona no marcador
                map.setCenter(new google.maps.LatLng(latitude, longitude));

                // atualiza
                document.getElementById("Latitude").value = latitude.toFixed(7).toString().replace(".", ",");
                document.getElementById("Longitude").value = longitude.toFixed(7).toString().replace(".", ",");
            }
            else {
                //alert('Erro ao converter endereço: ' + status);
            }
        });
    }


    // Array holding selected row IDs
    var rows_selected_contatos = [];
    var names_selected_contatos_contato = [];
    var names_selected_contatos_email = [];
    var names_selected_contatos_telefone = [];
    var names_selected_contatos_cargo = [];

    //
    // Updates "Select all" control in a data table
    //
    function updateDataTableSelectAllCtrl(table) {
        var $table = table.table().node();
        var $chkbox_all = $('tbody input[type="checkbox"]', $table);
        var $chkbox_checked = $('tbody input[type="checkbox"]:checked', $table);
        var chkbox_select_all = $('thead input[name="select_all"]', $table).get(0);

        // If none of the checkboxes are checked
        if ($chkbox_checked.length === 0) {
            chkbox_select_all.checked = false;
            if ('indeterminate' in chkbox_select_all) {
                chkbox_select_all.indeterminate = false;
            }

            // If all of the checkboxes are checked
        } else if ($chkbox_checked.length === $chkbox_all.length) {
            chkbox_select_all.checked = true;
            if ('indeterminate' in chkbox_select_all) {
                chkbox_select_all.indeterminate = false;
            }

            // If some of the checkboxes are checked
        } else {
            chkbox_select_all.checked = true;
            if ('indeterminate' in chkbox_select_all) {
                chkbox_select_all.indeterminate = true;
            }
        }
    }


    $(document).ready(function () {

        // mask money
        $('#Base_Valor').maskMoney({thousands:'', decimal:','});
        $('#Reajuste_Valor').maskMoney({thousands:'', decimal:','});

        // classe iChecks
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });


        $('#DataMigracao_Previsao_Texto').datetimepicker({
            locale: 'pt-BR',
            format: 'DD/MM/YYYY',
            allowInputToggle: true,
            showTodayButton: true
        });

        $('#DataMigracao_Texto').datetimepicker({
            locale: 'pt-BR',
            format: 'DD/MM/YYYY',
            allowInputToggle: true,
            showTodayButton: true
        });

        $('#Contrato_Inicio_Texto').datetimepicker({
            locale: 'pt-BR',
            format: 'DD/MM/YYYY',
            allowInputToggle: true,
            showTodayButton: true
        });

        $('#Contrato_Fim_Texto').datetimepicker({
            locale: 'pt-BR',
            format: 'DD/MM/YYYY',
            allowInputToggle: true,
            showTodayButton: true
        });

        $('#Base_Data_Texto').datetimepicker({
            locale: 'pt-BR',
            format: 'MM/YYYY',
            allowInputToggle: true,
            showTodayButton: true
        });

        $('#FechamentoTR_Texto').datetimepicker({
            locale: 'pt-BR',
            format: 'DD/MM/YYYY',
            allowInputToggle: true,
            showTodayButton: true
        });

        $('#MesRescisao_Texto').datetimepicker({
            locale: 'pt-BR',
            format: 'MM/YYYY',
            allowInputToggle: true,
            showTodayButton: true
        });

        $('#UltimoMesFaturamento_Texto').datetimepicker({
            locale: 'pt-BR',
            format: 'MM/YYYY',
            allowInputToggle: true,
            showTodayButton: true
        });


        //
        // VALIDACAO DOS CAMPOS
        //

        jQuery.validator.addMethod("alphanumeric", function (value, element) {
            return this.optional(element) || /^[\.\^\~\´\`áàãâéèêíìóòõôúùçA-Za-z0-9\s&+\-_,()/\\]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

        jQuery.validator.addMethod("numeric", function (value, element) {
            return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.numeric");

        jQuery.validator.addMethod("cnpj", function (value, element) {

            var numeros, digitos, soma, i, resultado, pos, tamanho, digitos_iguais;
            if (value.length == 0) {
                return false;
            }

            // verifica se default zero
            if (value == "00.000.000/0000-00")
            {
                return true;
            }

            value = "0" + value.replace(/\D+/g, '');
            digitos_iguais = 1;

            for (i = 0; i < value.length - 1; i++)
                if (value.charAt(i) != value.charAt(i + 1)) {
                    digitos_iguais = 0;
                    break;
                }
            if (digitos_iguais)
                return false;

            tamanho = value.length - 2;
            numeros = value.substring(0, tamanho);
            digitos = value.substring(tamanho);
            soma = 0;
            pos = tamanho - 7;
            for (i = tamanho; i >= 1; i--) {
                soma += numeros.charAt(tamanho - i) * pos--;
                if (pos < 2)
                    pos = 9;
            }
            resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
            if (resultado != digitos.charAt(0)) {
                return false;
            }
            tamanho = tamanho + 1;
            numeros = value.substring(0, tamanho);
            soma = 0;
            pos = tamanho - 7;
            for (i = tamanho; i >= 1; i--) {
                soma += numeros.charAt(tamanho - i) * pos--;
                if (pos < 2)
                    pos = 9;
            }

            resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;

            return (resultado == digitos.charAt(1));
        }, "@SmartEnergy.Resources.ValidateTexts.cnpj");


        $("#form").validate({
            rules: {
                RazaoSocial: {
                    required: true,
                    alphanumeric: true,
                    minlength: 3,
                    maxlength: 100
                },
                IDTipoEmpresa: { required: true  },
                CNPJ: {
                    required: true,
                    cnpj: true,
                    minlength: 18,
                    maxlength: 18
                },
                IDPerfil: {
                    required: false,
                    numeric: true,
                    maxlength: 15
                },
                Endereco: {
                    required: true,
                    alphanumeric: true,
                    minlength: 3,
                    maxlength: 100
                },
                CEP: {
                    numeric: true
                },
                Latitude: {
                    required: true,
                    numeric: true
                },
                Longitude: {
                    required: true,
                    numeric: true
                },
                Base_Valor: { numeric: true },
                Reajuste_Valor: { numeric: true },
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            }
        });

        'use strict';

        $('#fileupload').fileupload({
            url: '/FileUpload/UploadFile',
            dataType: 'json',
            add: function (e, data) {
                jqXHRData = data;
                jqXHRData.submit();
            },
            done: function (e, data) {
                if (data.result.isUploaded) {
                    $("#Logo").val(data.files[0].name);
                    document.getElementById('LogoImagem').src = "/Logos/" + data.files[0].name;

                    setTimeout(function () {

                        $('#progress .progress-bar').css(
                                                'width',
                                                0 + '%'
                                            );

                    }, 2000);
                }
            },
            fail: function (event, data) {
                if (data.files[0].error) {
                    alert(data.files[0].error);
                }
            },
            progressall: function (e, data) {
                var progress = parseInt(data.loaded / data.total * 100, 10);
                $('#progress .progress-bar').css(
                    'width',
                    progress + '%'
                );
            }
        }).prop('disabled', !$.support.fileInput)
                .parent().addClass($.support.fileInput ? undefined : 'disabled');


        // desabilita campos
        disableAll();

        //
        // TABELA CONTATOS
        //

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('#dataTables-contatos').DataTable({
            "iDisplayLength": 10,
            dom: 'ftp',

            'order': [1, 'asc'],
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "1%" },
            { sWidth: "29%" },
            { sWidth: "20%" },
            { sWidth: "20%" },
            { sWidth: "20%" },
            { sWidth: "10%" },
            ],

            "columnDefs": [
                 { "targets": [0], "visible": false, "searchable": false, "orderable": false },
                 { "targets": [1], "sType": "portugues" },
                 { "targets": [2], "sType": "portugues" },
                 { "targets": [3], "sType": "portugues" },
                 { "targets": [4], "sType": "portugues" },
                 { "targets": [5], "searchable": false, "orderable": false }
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        //
        // TABELA CONTATOS (MODAL)
        //

        var tableContatos2 = $('#dataTables-contatos2').DataTable({
            "iDisplayLength": 10,
            dom: 'ftp',

            'order': [2, 'asc'],
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "1%" },
            { sWidth: "10%" },
            { sWidth: "29%" },
            { sWidth: "20%" },
            { sWidth: "20%" },
            { sWidth: "20%" }
            ],

            'columnDefs': [{
                'targets': 0,
                'searchable': false,
                'orderable': false,
                'className': 'dt-center',
                'render': function (data, type, full, meta) {
                    return '<input type="checkbox" name="checkbox_modal_contatos">';
                },
            },
            {
                'targets': 1,
                'visible': false,
                'searchable': false,
                'orderable': false,
            }],

            'rowCallback': function (row, data, dataIndex) {
                // Get row ID
                var rowId = data[0];

                // If row ID is in the list of selected row IDs
                if ($.inArray(rowId, rows_selected_contatos) !== -1) {
                    $(row).find('input[type="checkbox"]').prop('checked', true);
                    $(row).addClass('selected');
                }
            },

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        // manipula checkbox
        $('#dataTables-contatos2 tbody').on('click', 'input[type="checkbox"]', function (e) {
            var $row = $(this).closest('tr');

            // Get row data
            var data = tableContatos2.row($row).data();

            // Get row ID
            var rowId = data[0];
            var name_contato = data[2];
            var name_email = data[3];
            var name_telefone = data[4];
            var name_cargo = data[5];

            // Determine whether row ID is in the list of selected row IDs
            var index = $.inArray(rowId, rows_selected_contatos);

            // If checkbox is checked and row ID is not in list of selected row IDs
            if (this.checked && index === -1) {
                rows_selected_contatos.push(rowId);
                names_selected_contatos_contato.push(name_contato);
                names_selected_contatos_email.push(name_email);
                names_selected_contatos_telefone.push(name_telefone);
                names_selected_contatos_cargo.push(name_cargo);

                // Otherwise, if checkbox is not checked and row ID is in list of selected row IDs
            } else if (!this.checked && index !== -1) {
                rows_selected_contatos.splice(index, 1);
                names_selected_contatos_contato.splice(index, 1);
                names_selected_contatos_email.splice(index, 1);
                names_selected_contatos_telefone.splice(index, 1);
                names_selected_contatos_cargo.splice(index, 1);
            }

            if (this.checked) {
                $row.addClass('selected');
            } else {
                $row.removeClass('selected');
            }

            // Update state of "Select all" control
            updateDataTableSelectAllCtrl(tableContatos2);

            // Prevent click event from propagating to parent
            e.stopPropagation();
        });

        // manipula click na tabela
        $('#dataTables-contatos2').on('click', 'tbody td, thead th:first-child', function (e) {
            $(this).parent().find('input[type="checkbox"]').trigger('click');
        });

        // manipula click no "Select all"
        $('thead input[name="select_all"]', tableContatos2.table().container()).on('click', function (e) {
            if (this.checked) {
                $('tbody input[type="checkbox"]:not(:checked)', tableContatos2.table().container()).trigger('click');
            } else {
                $('tbody input[type="checkbox"]:checked', tableContatos2.table().container()).trigger('click');
            }

            // Prevent click event from propagating to parent
            e.stopPropagation();
        });

        // Handle table draw event
        tableContatos2.on('draw', function () {
            // Update state of "Select all" control
            updateDataTableSelectAllCtrl(tableContatos2);
        });


        //
        // TIPO DA EMPRESA
        //

        // caso tipo da empresa mudar, atualizo campos
        $('#IDTipoEmpresa').change(function () {

            // obtem o tipo empresa
            var id = $(this).find(":selected").val();

            // permissao
            var permissao = Math.ceil(@ViewBag.Permissao);

            // verifica se agente CCEE
            if (id == TIPO_EMPRESA_AGENTECCEE)
            {
                // agente CCEE

                // esconde combo do agente CCEE
                document.getElementById("AgenteCCEEDIV").style.visibility = "hidden";

                $("#IDEmpresa_AgenteCCEE").attr('disabled', true);

                // verifica permissao
                switch (permissao) {
                    case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
                    case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
                    case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte

                        $("#SiglaCCEE").attr('disabled', false);
                        $("#IDCCEE").attr('disabled', false);
                        $("#Senha_Atendimento").attr('disabled', false);
                        $("#Senha_Representante").attr('disabled', false);
                        $("#Score").attr('disabled', false);
                        break;

                    default:

                        $("#SiglaCCEE").attr('disabled', true);
                        $("#IDCCEE").attr('disabled', true);
                        $("#Senha_Atendimento").attr('disabled', true);
                        $("#Senha_Representante").attr('disabled', true);
                        $("#Score").attr('disabled', true);
                        break;
                }
            }
            else
            {
                // filial

                // apresenta combo do agente CCEE
                document.getElementById("AgenteCCEEDIV").style.visibility = "visible";

                // zera seleção do agente CCEE
                document.getElementById('IDEmpresa_AgenteCCEE').value = "";
                document.getElementById('AgenteCCEE_RazaoSocial').value = "";
                document.getElementById('AgenteCCEE_CNPJ').value = "";
                document.getElementById('AgenteCCEE_ID').value = "";

                $("#SiglaCCEE").attr('disabled', true);
                $("#IDCCEE").attr('disabled', true);
                $("#Senha_Atendimento").attr('disabled', true);
                $("#Senha_Representante").attr('disabled', true);
                $("#Score").attr('disabled', true);

                // verifica permissao
                switch (permissao) {
                    case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
                    case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
                    case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte

                        $("#IDEmpresa_AgenteCCEE").attr('disabled', false);
                        break;

                    default:

                        $("#IDEmpresa_AgenteCCEE").attr('disabled', true);
                        break;
                }
            }
        });


        //
        // AGENTE CCEE
        //

        // caso agente CCE mudar, atualizo campos
        $('#IDEmpresa_AgenteCCEE').change(function () {

            // obtem o agente
            var id = $(this).find(":selected").val();

            // Razao Social do agente
            document.getElementById('AgenteCCEE_RazaoSocial').value = id;

            // CNPJ do agente
            document.getElementById('AgenteCCEE_CNPJ').value = id;

            // ID do agente
            document.getElementById('AgenteCCEE_ID').value = id;

            // IDEmpresa
            var IDEmpresa = document.getElementById('IDEmpresa').value;

            // le informações do agente
            $.getJSON('/Configuracao/ObterEmpresa', { IDEmpresa: id }, function (data) {

                // informações da filial
                document.getElementById('SiglaCCEE').value = data.SiglaCCEE;
                document.getElementById('IDCCEE').value = data.IDCCEE;
                document.getElementById('Senha_Atendimento').value = data.Senha_Atendimento;
                document.getElementById('Senha_Representante').value = data.Senha_Representante;
                document.getElementById('Score').value = data.Score;

                // verifica se está adicionando uma filial
                if (IDEmpresa == 0)
                {
                    // copio valores de contrato do agente CCEE que a filial esta associada
                    document.getElementById('Agente').value = data.Agente;
                    document.getElementById('IDPerfil').value = data.IDPerfil;
                    document.getElementById('DataMigracao_Previsao').value = data.DataMigracao_Previsao;
                    document.getElementById('DataMigracao_Previsao_Texto').value = data.DataMigracao_Previsao_Texto;
                    document.getElementById('DataMigracao').value = data.DataMigracao_Texto;
                    document.getElementById('DataMigracao_Texto').value = data.DataMigracao_Texto;

                    document.getElementById('Contrato_Status').value = data.Contrato_Status;
                    document.getElementById('Contrato_Gestora').value = data.Contrato_Gestora;
                    document.getElementById('Carteira').value = data.Carteira;
                    document.getElementById('Escopo').value = data.Escopo;
                    document.getElementById('Contrato_Inicio').value = data.Contrato_Inicio;
                    document.getElementById('Contrato_Inicio_Texto').value = data.Contrato_Inicio_Texto;
                    document.getElementById('Contrato_Fim').value = data.Contrato_Fim;
                    document.getElementById('Contrato_Fim_Texto').value = data.Contrato_Fim_Texto;
                    document.getElementById('MesRescisao').value = data.MesRescisao;
                    document.getElementById('MesRescisao_Texto').value = data.MesRescisao_Texto;
                    document.getElementById('UltimoMesFaturamento').value = data.UltimoMesFaturamento;
                    document.getElementById('UltimoMesFaturamento_Texto').value = data.UltimoMesFaturamento_Texto;
                    document.getElementById('Base_Valor').value = data.Base_Valor.toString().replace('.', ',');
                    document.getElementById('Base_Data').value = data.Base_Data;
                    document.getElementById('Base_Data_Texto').value = data.Base_Data_Texto;
                    document.getElementById('Reajuste_Mes').value = data.Reajuste_Mes;
                    document.getElementById('Reajuste_Indice').value = data.Reajuste_Indice;
                    document.getElementById('Reajuste_Valor').value = data.Reajuste_Valor.toString().replace('.', ',');
                    document.getElementById('Vencimento_Dias').value = data.Vencimento_Dias;
                    document.getElementById('Vencimento_Tipo').value = data.Vencimento_Tipo;

                    document.getElementById('RenovacaoAutomatica').value = data.RenovacaoAutomatica;

                    if (data.RenovacaoAutomatica)
                    {
                        $('#RenovacaoAutomaticaAux').iCheck('check');
                    }
                    else
                    {
                        $('#RenovacaoAutomaticaAux').iCheck('uncheck');
                    }

                    document.getElementById('FechamentoTR').value = data.FechamentoTR;
                    document.getElementById('FechamentoTR_Texto').value = data.FechamentoTR_Texto;
                    document.getElementById('Observacao').value = data.Observacao;

                    document.getElementById('Endereco').value = data.Endereco;
                    document.getElementById('CEP').value = data.CEP;
                    document.getElementById('IDPais').value = data.IDPais;
                    document.getElementById('IDEstado').value = data.IDEstado;
                    document.getElementById('NomeEstado').value = data.NomeEstado;
                    document.getElementById('IDCidade').value = data.IDCidade;
                    document.getElementById('NomeCidade').value = data.NomeCidade;
                    document.getElementById('Latitude').value = data.Latitude.toString().replace('.', ',');
                    document.getElementById('Longitude').value = data.Latitude.toString().replace('.', ',');
                    document.getElementById('IDTipoFusoHorario').value = data.IDTipoFusoHorario;
                }
            });

            // informações da filial
            document.getElementById('SiglaCCEE').value = "---";
            document.getElementById('IDCCEE').value = "---";
            document.getElementById('Senha_Atendimento').value = "---";
            document.getElementById('Senha_Representante').value = "---";
            document.getElementById('Score').value = "";
        });



        //
        // PAIS / ESTADO / CIDADE
        //

        // caso pais mudar, troco DIV
        $('#IDPais').change(function () {

            // obtem o país
            var id = $(this).find(":selected").val();

            // apresenta cidade e estado
            ApresentaPaisCidadeEstado(id);

            // uso default como alterou pais
            $('#IDEstado').append('<option value="">Selecione Estado</option>');
            document.getElementById('NomeEstado').value = "";

            $('#IDCidade').append('<option value="">Selecione Cidade</option>');
            document.getElementById('NomeCidade').value = "";

            // remove os dados que ja possui das cidades
            $('#IDCidade option').remove();

        });

        // caso estado mudar, atualizo cidades
        $('#IDEstado').change(function () {

            // obtem o estado
            var id = $(this).find(":selected").val();

            // nome estado
            document.getElementById('NomeEstado').value = $("#IDEstado option:selected").text();

            // chama a Action para popular as cidades
            $.getJSON('/Configuracao/ObterCidades', { IDEstado: id }, function (data) {

                // remove os dados que ja possui das cidades
                $('#IDCidade option').remove();

                // default
                $('#IDCidade').append('<option value="">Selecione Cidade</option>');
                document.getElementById('NomeCidade').value = "";

                // popula os options com os valores retornados em JSON
                for (var i = 0; i < data.length; i++) {
                    $('#IDCidade').append('<option value="' +
                        data[i].IDCidade + '"> ' +
                        data[i].Nome + '</option>');
                }
            });
        });

        // caso cidade mudar
        $('#IDCidade').change(function () {

            // obtem o estado
            var id = $(this).find(":selected").val();

            // nome cidade
            document.getElementById('NomeCidade').value = $("#IDCidade option:selected").text();

        });


        //
        // STATUS CONTRATO
        //

        // caso status mudar
        $('#Contrato_Status').change(function () {

            // obtem o estado
            var id = $(this).find(":selected").val();

            // status rescindido
            if (id == 2)
            {
                $("#Contrato_Gestora").attr('disabled', false);
                $("#MesRescisao_Texto").attr('disabled', false);
                $("#UltimoMesFaturamento_Texto").attr('disabled', false);
            }
            else
            {
                // muda para CPFL Soluções
                $("#Contrato_Gestora").attr('disabled', true);
                document.getElementById('Contrato_Gestora').value = 1;

                // bloqueia mes rescisão                
                $("#MesRescisao_Texto").attr('disabled', true);
                document.getElementById('MesRescisao_Texto').value = "01/2000";

                $("#UltimoMesFaturamento_Texto").attr('disabled', true);
                document.getElementById('UltimoMesFaturamento_Texto').value = "01/2000";
            }
        });


        //
        // PRECOS REAJUSTADOS
        //

        $('#fileuploadValores').fileupload({
            url: '/Configuracao/UploadFile_PrecosReajustados',
            dataType: 'json',
            add: function (e, data) {
                jqXHRData = data;
                jqXHRData.submit();
            },
            done: function (e, data) {
                if (data.result.isUploaded) {

                    setTimeout(function () {

                        // fecha janela
                        $('#modalPrecosReajustados').modal('hide');

                        $('#progress .progress-bar').css(
                                                'width',
                                                0 + '%'
                                            );

                        swal({
                            title: "Enviado com sucesso",
                            text: data.result.message,
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // fecha janela
                            $('#modalPrecosReajustados').modal('hide');
                            $('#progress .progress-bar').css(
                            'width',
                            0 + '%'
                            );

                            // atualiza pagina
                            location.reload();
                        });

                    }, 1000);
                }
                else {
                    setTimeout(function () {

                        swal({
                            title: "Erro no envio",
                            text: data.result.message,
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // fecha janela
                            $('#modalPrecosReajustados').modal('hide');
                            $('#progress .progress-bar').css(
                            'width',
                            0 + '%'
                            );
                        });

                    }, 1000);
                }

            },
            fail: function (event, data) {
                if (data.files[0].error) {

                    swal({
                        title: "Erro no envio",
                        text: data.files[0].error,
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    }, function () {

                        // fecha janela
                        $('#modalPrecosReajustados').modal('hide');

                    });
                }
            },
            progressall: function (e, data) {
                var progress = parseInt(data.loaded / data.total * 100, 10);
                $('#progress .progress-bar').css(
                    'width',
                    progress + '%'
                );
            }
        }).prop('disabled', !$.support.fileInput)
                .parent().addClass($.support.fileInput ? undefined : 'disabled');



        //
        // PROINFA
        //

        $('#dataTables-PROINFA').DataTable({
            "iDisplayLength": 10,
            dom: 'tp',

            'order': [0, 'asc'],
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "7%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "11%" },
            { sWidth: "10%" },
            ],

            "columnDefs": [
                 { "targets": [0], "sType": "numero", "orderable": false },
                 { "targets": [1], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [2], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [3], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [4], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [5], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [6], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [7], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [8], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [9], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [10], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [11], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [12], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [13], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [14], "searchable": false, "orderable": false }
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        $('#dataTables-PROINFA-MWm').DataTable({
            "iDisplayLength": 10,
            dom: 'tp',

            'order': [0, 'asc'],
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "7%" },
            { sWidth: "83%" },
            { sWidth: "10%" },
            ],

            "columnDefs": [
                 { "targets": [0], "sType": "numero", "orderable": false },
                 { "targets": [1], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [2], "searchable": false, "orderable": false }
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        // apresenta PROINFA
        ApresentaPROINFA();

        // altera unidade
        $('#Unidade_PROINFA').change(function () {

            // obtem a unidade
            var id = $(this).find(":selected").val();

            if (id == 0) {
                $('.div_PROINFA').css("display", "block");
                $('.div_PROINFA_MWm').css("display", "none");
            }
            else {
                $('.div_PROINFA').css("display", "none");
                $('.div_PROINFA_MWm').css("display", "block");
            }
        });

        $('#fileupload_PROINFA').fileupload({
            url: '/Configuracao/UploadFile_PROINFA',
            dataType: 'json',
            add: function (e, data) {
                jqXHRData = data;
                jqXHRData.submit();
            },
            done: function (e, data) {
                if (data.result.isUploaded) {

                    setTimeout(function () {

                        $('#progress_PROINFA .progress-bar').css(
                                                'width',
                                                0 + '%'
                                            );

                        swal({
                            title: "Enviado com sucesso",
                            text: data.result.message,
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                            $('#progress_PROINFA .progress-bar').css(
                            'width',
                            0 + '%'
                            );

                            // apresenta PROINFA
                            ApresentaPROINFA();
                        });

                    }, 1000);
                }
                else {
                    setTimeout(function () {

                        swal({
                            title: "Erro no envio",
                            text: data.result.message,
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                            $('#progress_PROINFA .progress-bar').css(
                            'width',
                            0 + '%'
                            );
                        });

                    }, 1000);
                }

            },
            fail: function (event, data) {
                if (data.files[0].error) {

                    swal({
                        title: "Erro no envio",
                        text: data.files[0].error,
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    }, function () {

                    });
                }
            },
            progressall: function (e, data) {
                var progress = parseInt(data.loaded / data.total * 100, 10);
                $('#progress_PROINFA .progress-bar').css(
                    'width',
                    progress + '%'
                );
            }
        }).prop('disabled', !$.support.fileInput)
                .parent().addClass($.support.fileInput ? undefined : 'disabled');



        $('form input').on('keypress', function (e) {
            return e.which !== 13;
        });
    });


    function disableAll() {

        // tipo empresa
        var IDTipoEmpresa = document.getElementById('IDTipoEmpresa').value;

        // verifica se filial
        if (IDTipoEmpresa == TIPO_EMPRESA_FILIAL)
        {
            // apresenta combo do agente CCEE
            document.getElementById("AgenteCCEEDIV").style.visibility = "visible";
            $("#IDEmpresa_AgenteCCEE").attr('disabled', false);
        }

        // IDPais
        var IDPais = document.getElementById("IDPais").value;
        ApresentaPaisCidadeEstado(IDPais);

        // permissao
        var permissao = Math.ceil(@ViewBag.Permissao);

        // verifica permissao
        switch (permissao) {
            case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
            case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
            case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte

                $("#BotaoSalvar").attr('disabled', false);

                $("#RazaoSocial").attr('disabled', false);
                $("#IDTipoEmpresa").attr('disabled', false);
                $("#CNPJ").attr('disabled', false);

                $("#Agente").attr('disabled', false);
                $("#IDPerfil").attr('disabled', false);
                $("#DataMigracao_Previsao_Texto").attr('disabled', false);
                $("#DataMigracao_Texto").attr('disabled', false);

                document.getElementById("BotaoUploadDIV").style.visibility = "visible";
                document.getElementById("DimensoesDIV").style.visibility = "visible";
                $("#fileupload").attr('disabled', false);

                $("#Contrato_Status").attr('disabled', false);

                // status rescindido
                var status = document.getElementById("Contrato_Status").value;
                if (status == 2)
                {
                    $("#Contrato_Gestora").attr('disabled', false);
                    $("#MesRescisao_Texto").attr('disabled', false);
                    $("#UltimoMesFaturamento_Texto").attr('disabled', false);
                }

                $("#Carteira").attr('disabled', false);
                $("#Escopo").attr('disabled', false);
                $("#Contrato_Inicio_Texto").attr('disabled', false);
                $("#Contrato_Fim_Texto").attr('disabled', false);
                $("#Base_Valor").attr('disabled', false);
                $("#Base_Data_Texto").attr('disabled', false);
                $("#Reajuste_Mes").attr('disabled', false);
                $("#Reajuste_Indice").attr('disabled', false);
                $("#Reajuste_Valor").attr('disabled', false);
                $("#BotaoPrecosReajustados").attr('disabled', false);
                document.getElementById("BotaoUploadValoresDIV").style.visibility = "visible";

                $("#Vencimento_Dias").attr('disabled', false);
                $("#Vencimento_Tipo").attr('disabled', false);
                $("#RenovacaoAutomaticaAux").attr('disabled', false);
                $("#FechamentoTR_Texto").attr('disabled', false);
                $("#Observacao").attr('disabled', false);

                $("#Endereco").attr('disabled', false);
                $("#CEP").attr('disabled', false);
                $("#IDPais").attr('disabled', false);
                $("#IDEstado").attr('disabled', false);
                $("#NomeEstado").attr('disabled', false);
                $("#IDCidade").attr('disabled', false);
                $("#NomeCidade").attr('disabled', false);
                $("#Latitude").attr('disabled', false);
                $("#Longitude").attr('disabled', false);
                $("#IDTipoFusoHorario").attr('disabled', false);
                $("#BotaoEncontrarLatLong").attr('disabled', false);

                $("#BotaoAdicionarPROINFA").attr('disabled', false);

                break;

            case PERMISSOES_PRODUCAO:   // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                $("#BotaoSalvar").attr('disabled', false);
                $("#RazaoSocial").attr('disabled', false);
                $("#IDEmpresa_AgenteCCEE").attr('disabled', true);

                break;

            default:
            case PERMISSOES_GERENTE:    // 1 - permissao de Gerente: ve tudo e escreve parte
            case PERMISSOES_OPERADOR:   // 2 - permissao de Operador: ve tudo e nao pode escrever
            case PERMISSOES_CONSULTOR:  // 5 - permissao de Consultor

                $("#BotaoSalvar").attr('disabled', true);
                break;
        }
    }

    // apresenta cidade/estado do pais
    function ApresentaPaisCidadeEstado(IDPais) {

        // IDPais
        if (IDPais == 1) {
            $('.div_BRASIL').css("display", "block");
            $('.div_ESTRANGEIRO').css("display", "none");

            $('.div_SmartWeather').css("display", "block");

            $("#NomeEstado").attr('disabled', true);
            $("#NomeCidade").attr('disabled', true);
        }
        else {
            $('.div_BRASIL').css("display", "none");
            $('.div_ESTRANGEIRO').css("display", "block");

            $('.div_SmartWeather').css("display", "none");

            $("#NomeEstado").attr('disabled', false);
            $("#NomeCidade").attr('disabled', false);
        }
    }



    //
    // PRECOS REAJUSTADOS
    //

    function ModalPrecosReajustados() {
        event.stopPropagation();

        // apresenta janela
        $('#modalPrecosReajustados').modal('show');
    }

    function DownloadPrecosReajustados() {
        event.stopPropagation();

        $.ajax(
        {
            type: 'GET',
            url: '/Configuracao/DownloadFile_PrecosReajustados',
            data: {},
            contentType: "application/json;charset=utf-8",
            dataType: "json",
            cache: false,
            async: true,
            success: function (data) {

                // inicia download
                window.location = '/Configuracao/PrecosReajustados_XLS_Download?fileGuid=' + data.FileGuid
                                  + '&filename=' + data.FileName;

                // fecha janela
                $('#modalPrecosReajustados').modal('hide');

            },
            error: function (xhr, status, error) {

                // fecha janela
                $('#modalPrecosReajustados').modal('hide');

                swal({
                    title: "Erro",
                    text: "Erro ao gerar XLS!",
                    type: "warning",
                    confirmButtonColor: "#f8ac59",
                    confirmButtonText: "Fechar",
                }, function () {
                });

                alert(xhr.responseText);
            }
        });
    }


    //
    // BOTAO ADICIONA CONTATOS
    //

    function fnClickAddRowContatos() {

        // percorre lista de selecionados
        $.each(rows_selected_contatos, function (index, rowId) {

            // procura na tabela se ja existe iD
            var table = $('#dataTables-contatos').dataTable();
            row_count = table.fnGetData().length;
            var linhas = table.fnGetData();

            var achou = false;

            for (var j = 0; j < row_count; j++) {

                if (linhas[j][0] == rowId) {
                    achou = true;
                }
            }

            // insere na tabela se nao achou
            if (achou == false) {
                $('#dataTables-contatos').dataTable().fnAddData([
                    rowId,
                    names_selected_contatos_contato[index],
                    names_selected_contatos_email[index],
                    names_selected_contatos_telefone[index],
                    names_selected_contatos_cargo[index],
                    '<a href="#" class="confirm-delete-contato link_preto"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>',
                ]);
            }
        });

        // apaga arrays
        rows_selected_contatos = [];
        names_selected_contatos_contato = [];
        names_selected_contatos_email = [];
        names_selected_contatos_telefone = [];
        names_selected_contatos_cargo = [];

        // desmarca checkbox
        var boxes = document.getElementsByName("checkbox_modal_contatos");
        for (var i = 0; i < boxes.length; i++)
            boxes[i].checked = false;

        var chkbox_select_all = $('thead input[name="select_all"]', $('#dataTables-contatos2')).get(0);
        chkbox_select_all.indeterminate = false;
        chkbox_select_all.checked = false;

        // remove da lista
        $('#dataTables-contatos2').dataTable().$('tr.selected').empty();
        $('#dataTables-contatos2').dataTable().draw();

        // desenha tabela
        $('#dataTables-contatos').dataTable().draw();
    }

    //
    // BOTAO APAGAR CONTATO
    //

    $('#dataTables-contatos').on('click', '.confirm-delete-contato', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // le dados da linha
        var data = $('#dataTables-contatos').DataTable().row(row).data();

        var rowId = data[0];
        var contato = data[1];
        var email = data[2];
        var telefone = data[3];
        var cargo = data[4];

        // titulo
        titulo = "Deseja excluir o contato?";

        swal({
            html: true,
            title: titulo,
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            setTimeout(function () {

                // insere no modal
                $('#dataTables-contatos2').dataTable().fnAddData([
                    rowId,
                    rowId,
                    contato,
                    email,
                    telefone,
                    cargo
                ]);

                // apaga linha solicitada
                $('#dataTables-contatos').dataTable().fnDeleteRow(row);

                // desenha tabela
                $('#dataTables-contatos').dataTable().draw();

                // desenha tabela
                $('#dataTables-contatos2').dataTable().draw();

            }, 100);

        });

    });


    //
    // PROINFA
    //

    function ApresentaPROINFA() {
        event.stopPropagation();

        // IDEmpresa
        var IDEmpresa = document.getElementById("IDEmpresa").value;

        // limpa
        $('#dataTables-PROINFA').DataTable().clear().draw();
        $('#dataTables-PROINFA-MWm').DataTable().clear().draw();

        // chama a Action para obter PROINFA
        $.getJSON('/Configuracao/ObterPROINFA', { 'IDEmpresa': IDEmpresa }, function (resultado) {

            // percorre PROINFA
            for (var i = 0; i < resultado.length; i++) {

                $('#dataTables-PROINFA').dataTable().fnAddData([
                    resultado[i].Ano_Texto,
                    resultado[i].Jan_Texto,
                    resultado[i].Fev_Texto,
                    resultado[i].Mar_Texto,
                    resultado[i].Abr_Texto,
                    resultado[i].Mai_Texto,
                    resultado[i].Jun_Texto,
                    resultado[i].Jul_Texto,
                    resultado[i].Ago_Texto,
                    resultado[i].Set_Texto,
                    resultado[i].Out_Texto,
                    resultado[i].Nov_Texto,
                    resultado[i].Dez_Texto,
                    resultado[i].Total_Texto,
                    '<a href="#" class="edit-PROINFA"><i class="fa fa-edit icones" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a> <a href="#" class="delete-PROINFA"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>',
                ]);

                $('#dataTables-PROINFA-MWm').dataTable().fnAddData([
                    resultado[i].Ano_Texto,
                    resultado[i].MWm_AnoTotal_Texto,
                    '<a href="#" class="edit-PROINFA"><i class="fa fa-edit icones" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a> <a href="#" class="delete-PROINFA"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>',
                ]);
            }

            // desenha tabela
            $('#dataTables-PROINFA').dataTable().draw();
            $('#dataTables-PROINFA-MWm').dataTable().draw();

        });
    }

    function Adicionar_PROINFA() {
        event.stopPropagation();

        // IDEmpresa
        var IDEmpresa = document.getElementById("IDEmpresa").value;

        // ano
        var Ano = new Date().getFullYear();

        // valores da janela
        document.getElementById("Ano_PROINFA").value = Ano.toString();
        document.getElementById("Jan_PROINFA").value = "0.000";
        document.getElementById("Fev_PROINFA").value = "0.000";
        document.getElementById("Mar_PROINFA").value = "0.000";
        document.getElementById("Abr_PROINFA").value = "0.000";
        document.getElementById("Mai_PROINFA").value = "0.000";
        document.getElementById("Jun_PROINFA").value = "0.000";
        document.getElementById("Jul_PROINFA").value = "0.000";
        document.getElementById("Ago_PROINFA").value = "0.000";
        document.getElementById("Set_PROINFA").value = "0.000";
        document.getElementById("Out_PROINFA").value = "0.000";
        document.getElementById("Nov_PROINFA").value = "0.000";
        document.getElementById("Dez_PROINFA").value = "0.000";

        // apresenta janela
        $('#ModalPROINFA_Editar').modal('show');
    }

    $('#dataTables-PROINFA').on('click', '.edit-PROINFA', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // editar
        Editar_PROINFA(row);
    });

    $('#dataTables-PROINFA-MWm').on('click', '.edit-PROINFA', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // editar
        Editar_PROINFA(row);
    });

    function Editar_PROINFA(row) {

        // le dados da linha
        var data = $('#dataTables-PROINFA').DataTable().row(row).data();

        // valores da janela
        document.getElementById("Ano_PROINFA").value = data[0];
        document.getElementById("Jan_PROINFA").value = data[1];
        document.getElementById("Fev_PROINFA").value = data[2];
        document.getElementById("Mar_PROINFA").value = data[3];
        document.getElementById("Abr_PROINFA").value = data[4];
        document.getElementById("Mai_PROINFA").value = data[5];
        document.getElementById("Jun_PROINFA").value = data[6];
        document.getElementById("Jul_PROINFA").value = data[7];
        document.getElementById("Ago_PROINFA").value = data[8];
        document.getElementById("Set_PROINFA").value = data[9];
        document.getElementById("Out_PROINFA").value = data[10];
        document.getElementById("Nov_PROINFA").value = data[11];
        document.getElementById("Dez_PROINFA").value = data[12];

        // apresenta janela
        $('#ModalPROINFA_Editar').modal('show');
    }


    $('#dataTables-PROINFA').on('click', '.delete-PROINFA', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // excluir
        Excluir_PROINFA(row);
    });

    $('#dataTables-PROINFA-MWm').on('click', '.delete-PROINFA', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // excluir
        Excluir_PROINFA(row);
    });

    function Excluir_PROINFA(row) {

        // le dados da linha
        var data = $('#dataTables-PROINFA').DataTable().row(row).data();

        // ano
        var Ano = data[0];

        // IDEmpresa
        var IDEmpresa = document.getElementById("IDEmpresa").value;

        // titulo
        titulo = "Deseja excluir o Ano de " + Ano + " ?";

        swal({
            html: true,
            title: titulo,
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            $.ajax(
            {
                type: 'GET',
                url: '/Configuracao/ExcluirPROINFA',
                data: { 'IDEmpresa': IDEmpresa, 'Ano': Ano },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    // apresenta tabela PROINFA
                    ApresentaPROINFA();
                },
                error: function (response) {

                }
            });

        });
    }

    function AlteraPROINFA() {
        event.stopPropagation();

        // IDEmpresa
        var IDEmpresa = document.getElementById("IDEmpresa").value;

        // Ano
        var Ano_TXT = document.getElementById("Ano_PROINFA").value;
        var Ano = parseInt(Ano_TXT);

        if (Ano < 2000 || Ano > 2100)
        {
            swal({
                html: true,
                title: "Erro",
                text: "Digite um valor de Ano correto",
                type: "warning",
                confirmButtonColor: "#f8ac59",
                confirmButtonText: "Fechar",
            }, function () {

            });
        }
        else
        {
            // janeiro
            var valor_str = document.getElementById("Jan_PROINFA").value;
            var Jan_Valor = parseFloat(valor_str.replace(',', '.'));

            // fevereiro
            valor_str = document.getElementById("Fev_PROINFA").value;
            var Fev_Valor = parseFloat(valor_str.replace(',', '.'));

            // março
            valor_str = document.getElementById("Mar_PROINFA").value;
            var Mar_Valor = parseFloat(valor_str.replace(',', '.'));

            // abril
            valor_str = document.getElementById("Abr_PROINFA").value;
            var Abr_Valor = parseFloat(valor_str.replace(',', '.'));

            // maio
            valor_str = document.getElementById("Mai_PROINFA").value;
            var Mai_Valor = parseFloat(valor_str.replace(',', '.'));

            // junho
            valor_str = document.getElementById("Jun_PROINFA").value;
            var Jun_Valor = parseFloat(valor_str.replace(',', '.'));

            // julho
            valor_str = document.getElementById("Jul_PROINFA").value;
            var Jul_Valor = parseFloat(valor_str.replace(',', '.'));

            // agosto
            valor_str = document.getElementById("Ago_PROINFA").value;
            var Ago_Valor = parseFloat(valor_str.replace(',', '.'));

            // setembro
            valor_str = document.getElementById("Set_PROINFA").value;
            var Set_Valor = parseFloat(valor_str.replace(',', '.'));

            // outubro
            valor_str = document.getElementById("Out_PROINFA").value;
            var Out_Valor = parseFloat(valor_str.replace(',', '.'));

            // novembro
            valor_str = document.getElementById("Nov_PROINFA").value;
            var Nov_Valor = parseFloat(valor_str.replace(',', '.'));

            // dezembro
            valor_str = document.getElementById("Dez_PROINFA").value;
            var Dez_Valor = parseFloat(valor_str.replace(',', '.'));

            // titulo
            titulo = "Deseja salvar o PROINFA do Ano de " + Ano + " ?";

            swal({
                html: true,
                title: titulo,
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#18a689",
                confirmButtonText: "Salvar",
                cancelButtonText: "Cancelar",
                closeOnConfirm: true
            }, function () {

                // fecha janela
                $('#ModalPROINFA_Editar').modal('hide');

                // salvar
                $.ajax(
                {
                    type: 'GET',
                    url: '/Configuracao/SalvarPROINFA',
                    data: { 'IDEmpresa': IDEmpresa, 'Ano': Ano, 'Jan': Jan_Valor, 'Fev': Fev_Valor, 'Mar': Mar_Valor, 'Abr': Abr_Valor, 'Mai': Mai_Valor, 'Jun': Jun_Valor, 'Jul': Jul_Valor, 'Ago': Ago_Valor, 'Set': Set_Valor, 'Out': Out_Valor, 'Nov': Nov_Valor, 'Dez': Dez_Valor },
                    contentType: 'application/html',
                    dataType: 'html',
                    cache: false,
                    async: true,
                    success: function (result) {

                        // apresenta tabela PROINFA
                        ApresentaPROINFA();

                    },
                    error: function (response) {

                        setTimeout(function () {

                            swal({
                                title: "Erro",
                                text: "Erro ao salvar!",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            });

                        }, 100);

                    }
                });

            });

        }
    }

    function DownloadPROINFA() {
        event.stopPropagation();

        // IDEmpresa
        var IDEmpresa = document.getElementById("IDEmpresa").value;

        $.ajax(
        {
            type: 'GET',
            url: '/Configuracao/DownloadFile_PROINFA',
            data: { 'IDEmpresa': IDEmpresa },
            contentType: "application/json;charset=utf-8",
            dataType: "json",
            cache: false,
            async: true,
            success: function (data) {

                // inicia download
                window.location = '/Configuracao/PROINFA_XLS_Download?fileGuid=' + data.FileGuid
                                  + '&filename=' + data.FileName;

            },
            error: function (xhr, status, error) {

                swal({
                    title: "Erro",
                    text: "Erro ao gerar XLS!",
                    type: "warning",
                    confirmButtonColor: "#f8ac59",
                    confirmButtonText: "Fechar",
                }, function () {
                });

                alert(xhr.responseText);
            }
        });
    }


    //
    // BOTAO SALVAR (SUBMIT)
    //

    $.fn.serializeObject = function () {
        var o = {};
        var a = this.serializeArray();
        $.each(a, function () {
            if (o[this.name] !== undefined) {
                if (!o[this.name].push) {
                    o[this.name] = [o[this.name]];
                }
                o[this.name].push(this.value || '');
            } else {
                o[this.name] = this.value || '';
            }
        });
        return o;
    };

    function Salvar() {

        var $form = $('#form');

        // apresenta primeiro tab que tiver erro
        var IsTabValid = true;
        var TabInvalid = 1;

        $(".tab-content").find("div.tab-pane").each(function (index, tab) {
            var id = $(tab).attr("id");
            $('a[href="#' + id + '"]').tab('show');

            IsTabValid = $("#form").valid();

            if (!IsTabValid) {

                TabInvalid = id;
                return false;
            }
        });

        // apresenta primeira tab ou com erro
        $('a[href="#tab-' + TabInvalid + '"]').tab('show');

        // verifica se entradas sao validas
        if (!IsTabValid) return false;


        // lista de contatos
        var oTable = $('#dataTables-contatos').dataTable();
        var rows = oTable.fnSettings().aoData;
        var dataArray = [];

        // percorre tabela
        $.each(rows, function (i, val) {

            // pega valores
            IDCliente = 0;
            IDEmpresa = 0;
            IDContato = parseInt(val._aData[0]);

            dataArray.push({
                "IDCliente": IDCliente,
                "IDEmpresa": IDEmpresa,
                "IDContato": IDContato
            });
        });

        // checkbox
        document.getElementById('RenovacaoAutomatica').value = (document.getElementById('RenovacaoAutomaticaAux').checked == true) ? true : false;

        // deseja salvar
        swal({
            title: "Deseja salvar?",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#1ab394",
            confirmButtonText: "Salvar",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {
            event.stopPropagation();

            // aguarde
            $('.overlay_aguarde').toggle();

            // retiro campos desabilitados, pois nao envia via POST
            $(":disabled", $('#form')).removeAttr("disabled");

            // constroi estrutura com FORM e lista de contatos
            data = { 'empresa': $form.serializeObject(), 'contatos': dataArray };
            data2 = JSON.stringify(data);

            $.ajax({
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                url: '/Configuracao/Empresa_Salvar',
                data: data2,
                type: 'POST',
                success: function (data) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO")
                        {
                            swal({
                                html: true,
                                title: "Erro",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                            });
                        }
                        else
                        {
                            swal({
                                title: "Salvo com sucesso",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // redireciona para pagina lista de empresas
                                var url = @Html.Raw(Json.Encode(@ViewBag.returnUrl));
                                window.location.href = url;
                            });
                        }

                    }, 100);

                },
                error: function (xhr, status, error) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao salvar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });
    };

</script>
}
