﻿@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoClientes;
}

<style type="text/css">

    #treetable {
        table-layout: fixed;
    }

    #treetable tr td:nth-of-type(1) {
        text-align: right;
    }

    #treetable tr td:nth-of-type(2) {
        text-align: center;
    }

    #treetable tr td:nth-of-type(3) {
        min-width: 100px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    #treetable > tbody > tr.fancytree-selected > td >  a {
        color: #fff !important;
    }

    #treetable > tbody > tr.fancytree-active > td > a {
        color: #fff !important;
    }

</style>


<div class="panel panel-default">
    <div class="panel-heading">
        <h1>Configuração</h1>
    </div>
    <div class="panel-body">

        <table id="treetable" class="table table-condensed table-hover table-striped fancytree-fade-expander">
            <colgroup>
                <col width="0%"></col>
                <col width="0%"></col>
                <col width="55%"></col>
                <col width="15%"></col>
                <col width="10%"></col>
                <col width="10%"></col>
                <col width="10%"></col>
            </colgroup>
            <thead>
                <tr> <th></th> <th></th> <th></th> <th>@SmartEnergy.Resources.ConfiguracaoTexts.Descricao</th> <th>ID</th> <th></th> <th></th> </tr>
            </thead>
            <tbody>
                <tr> <td></td> <td></td> <td></td> <td></td> <td></td> <td class="link_preto"></td> <td class="link_preto"></td> </tr>
            </tbody>
        </table>

    </div>
</div>



@section Styles {
    @Styles.Render("~/Content/plugins/FancyTree")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/FancyTree")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        var glyph_opts = {
            preset: "bootstrap3",
            map: {
            }
        };

        $(document).ready(function () {

            // Initialize Fancytree
            $("#treetable").fancytree({
                extensions: ["glyph", "table"],
                checkbox: false,
                glyph: glyph_opts,
                types: {
                    "configuracao": { icon: "gears", iconTooltip: "Configuração" },
                    "cliente": { icon: "th-large", iconTooltip: "Cliente" },
                    "empresa": { icon: "th", iconTooltip: "Matriz e Filiais" },
                    "gateway": { icon: "download", iconTooltip: "Gateways" },
                    "medicao": { icon: "dashboard", iconTooltip: "Medições" },
                    "usuarios": { icon: "users", iconTooltip: "Usuários" },
                    "usuario": { icon: "user", iconTooltip: "Usuários" },
                    "contato": { icon: "vcard", iconTooltip: "Contatos" },
                    "grupo": { icon: "files-o", iconTooltip: "Grupo de Unidades" },
                    "unidade": { icon: "file-o", iconTooltip: "Unidades" },
                    "traco": { icon: "ellipsis-h", iconTooltip: "---" },
                },

                icon: function (event, data) {

                    var icone = '<i class="fa fa-' + data.typeInfo.icon + '"></i>';

                    if (data.typeInfo.icon == "") {
                        icone = '';

                    }
                    return { html: icone };
                },

                source: { url: "/Configuracao/Config_Arvore", cache: false },

                table: {
                    checkboxColumnIdx: 1,
                    nodeColumnIdx: 2
                },
                activate: function (event, data) {
                },
                renderColumns: function (event, data) {
                    var node = data.node,
                        $tdList = $(node.tr).find(">td");

                    // configuracao
                    $tdList.eq(3).text(node.data.config);

                    // ID
                    $tdList.eq(4).text(node.data.ID);

                    // tools1
                    $tdList.eq(5).html(node.data.tools1);

                    // tools2
                    $tdList.eq(6).html(node.data.tools2);
                }
            });

        });

    </script>
}
