﻿@model IEnumerable<SmartEnergyLib.SQL.ClientesDominio>

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoClientes;
}


@{
    int IDConsultor = ViewBag._IDConsultor;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoClientes</h4>
                    <div class="pull-right dashboard-tools link_branco" style="margin-top:-8px;">
                        <h4>

                            @{
                                int IDCliente = ViewBag._IDCliente;

                                if (IDCliente > 0)
                                {
                                    <a href='@("/Configuracao/Menu?IDCliente=" + @IDCliente.ToString())' title="Menu @SmartEnergy.Resources.ConfiguracaoTexts.Configuracoes @SmartEnergy.Resources.ConfiguracaoTexts.ClienteAtual"><i class="fa fa-th-large"></i></a>
                                }
                            }

                            <a href='@("/Configuracao/Cliente_Editar?IDCliente=0")' id="BotaoAdicionar" title="@SmartEnergy.Resources.ComumTexts.BotaoInserir"><i class="fa fa-plus"></i></a>

                        </h4>
                    </div>
                </div>
                <div class="panel-body">
                    <table class="table table-striped table-bordered table-hover dataTables-clientes">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.Logo</th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.Clientes</th>
                                <th class="some_minidesktop">Status</th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.RamoAtividade</th>

                                @{
                                    if (IDConsultor == CLIENTES_ESPECIAIS.CPFL)
                                    {
                                        <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.UnidadesConsumidoras</th>
                                    }
                                    else
                                    {
                                        <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.AgentesFiliais</th>
                                    }
                                }

                                <th class="some_minidesktop">Gateways</th>
                                <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.NumMedicoes <br />Reais</th>
                                <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.NumMedicoes <br />Virtuais</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>

                            @{ 
                                List<ListaTiposDominio> listaTipoRamoAtividade = ViewBag.listaTipoRamoAtividade;
                            }

                            @foreach (var cliente in Model)
                            {
                                string logo = "http://www.smartenergy.com.br/";

                                if (cliente.Logo.IsEmpty())
                                {
                                    logo += "/Logos/LogoSmartEnergy.png";
                                }
                                else
                                {
                                    logo += "/Logos/" + cliente.Logo;
                                }
                                
                            <tr>
                                <td>@cliente.IDCliente</td>
                                <td bgcolor="#293846" align="center"><img src=@logo style="max-height:45px; height: auto;" /></td>
                                <td><b>@cliente.Fantasia</b></td>

                                @{
                                    string statusAtivo = "Inativo";

                                    if (cliente.IDTipoStatusAtivo == 1)
                                    {
                                        statusAtivo = "Ativo";

                                    }

                                    string ramoAtividade = "---";
                                    if (listaTipoRamoAtividade != null)
                                    {
                                        ListaTiposDominio ramoAtiv = listaTipoRamoAtividade.Find(item => item.ID == cliente.IDRamoAtividade);

                                        if (ramoAtiv != null)
                                        {
                                            ramoAtividade = ramoAtiv.Descricao;
                                        }
                                    }
                                }
                                <td class="some_minidesktop">@statusAtivo</td>
                                <td class="some_minidesktop">@ramoAtividade</td>
                                <td class="some_minidesktop">@cliente.NumEmpresas</td>
                                <td class="some_minidesktop">@cliente.NumGateways</td>
                                <td class="some_minidesktop">@cliente.NumMedicoes</td>
                                <td class="some_minidesktop">@cliente.NumMedicoes_Virtuais</td>
                                <td class="link_preto">

                                    <a href='@("/Configuracao/Menu?IDCliente=" + @cliente.IDCliente.ToString())' title="Menu @SmartEnergy.Resources.ConfiguracaoTexts.Configuracoes"><i class="fa fa-th-large icones"></i></a>
                                    <a href='@("/Configuracao/Cliente_Editar?IDCliente=" + @cliente.IDCliente.ToString())' title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"><i class="fa fa-edit icones"></i></a>

                                    @if (ViewBag.Permissao == PERMISSOES.ADMIN)
                                    {
                                        <a href="#" onclick="javascript:Excluir(@cliente.IDCliente);" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-times icones"></i></a>
                                    }

                                    @if (ViewBag._IDUsuario == 1)
                                    {
                                        <a href='@("/Configuracao/Cliente_Arvore?IDCliente=" + @cliente.IDCliente.ToString())' title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"><i class="fa fa-tree icones"></i></a>
                                    }

                                </td>
                            </tr>
                            }

                        </tbody>
                    </table>

                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


<script type="text/javascript">

    $(document).ready(function () {

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {
                var x = (a == "-") ? 0 : a.replace(/,/, ".");
                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-clientes').DataTable({
            "iDisplayLength": 14,
            dom: 'Bftp',
            buttons: [
                {
                    extend: 'excelHtml5',
                    filename: 'Clientes',
                    title: 'Clientes',
                    text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-excel-o" data-toggle="tooltip" data-placement="left" title="Excel"></span>',
                    className: 'btn btn-default btn-xs'
                },
                {
                    extend: 'pdfHtml5',
                    filename: 'Clientes',
                    title: 'Clientes',
                    text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-pdf-o" data-toggle="tooltip" data-placement="left" title="PDF"></span>',
                    className: 'btn btn-default btn-xs'
                },
            ],

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "numero" },
                        { "aTargets": [1], "sType": "portugues", "bSortable": false, "bSearchable": false },
                        { "aTargets": [2], "sType": "portugues" },
                        { "aTargets": [3], "sType": "portugues" },
                        { "aTargets": [4], "sType": "portugues" },
                        { "aTargets": [5], "sType": "numero" },
                        { "aTargets": [6], "sType": "numero" },
                        { "aTargets": [7], "sType": "numero" },
                        { "aTargets": [8], "sType": "numero" },
                        { "aTargets": [9], "bVisible": true, "bSortable": false, "bSearchable": false },
            ],

            "aoColumns": [
                    { sWidth: "4%" },
                    { sWidth: "10%" },
                    { sWidth: "22%" },
                    { sWidth: "8%" },
                    { sWidth: "8%" },
                    { sWidth: "14%" },
                    { sWidth: "8%" },
                    { sWidth: "8%" },
                    { sWidth: "8%" },
                    { sWidth: "10%" }
            ],
            'order': [2, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
            "next": "@SmartEnergy.Resources.ComumTexts.next"
        },
        "search": "@SmartEnergy.Resources.ComumTexts.search",
        "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
        "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
        "info": "@SmartEnergy.Resources.ComumTexts.info",
        "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
        "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
    }

    });

    // desabilita campos
    disableAll();
    });

    function disableAll() {

        var permissao = Math.ceil(@ViewBag.Permissao);

        // verifica permissao
        switch(permissao)
        {
            case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
            case PERMISSOES_PRODUCAO:           // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)
            case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
            case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte

                break;

            default:
            case PERMISSOES_GERENTE:            // 1 - permissao de Gerente: ve tudo e escreve parte
            case PERMISSOES_OPERADOR:           // 2 - permissao de Operador: ve tudo e nao pode escrever
            case PERMISSOES_CONSULTOR:          // 5 - permissao de Consultor

                document.getElementById("BotaoAdicionar").style.visibility = "hidden";
                break;
        }
    }

    function Excluir(IDCliente) {
        event.stopPropagation();

        // titulo
        titulo = "Deseja excluir o Cliente?<br/>ID " + IDCliente;

        swal({
            html: true,
            title: titulo,
            text: "Esta operação exclui todos os relacionamentos deste cliente em outras configurações e os históricos:<br>Agentes CCEE e Filiais<br>Medições<br>Gateways<br>Grupo Unidades e Unidade<br>Usuários<br>Contatos<br>Históricos de Medição<br>Históricos da Gateway",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: false
        }, function () {

            // excluir
            $.ajax(
            {
                type: 'GET',
                url: '/Configuracao/Cliente_Excluir',
                data: { 'IDCliente': IDCliente },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        swal({
                            title: "Excluído com sucesso",
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // atualiza pagina
                            location.reload();
                        });

                    }, 100);
                },
                error: function (response) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao excluir!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });
    };

</script>
}


