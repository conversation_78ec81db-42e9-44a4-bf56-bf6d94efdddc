﻿@model SmartEnergyLib.SQL.ContatosDominio

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = @SmartEnergy.Resources.ConfiguracaoTexts.Contato;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("Contato_Editar", "Configuracao", new { ReturnUrl = ViewBag.ReturnUrl }, FormMethod.Post, new { id = "form", role = "form" }))
            {
                @Html.Hidden("IDCliente", Model.IDCliente)
                

                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>@SmartEnergy.Resources.ConfiguracaoTexts.Contato</h4>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="form-group col-lg-2">
                                <label class="control-label">&nbsp;ID</label>

                                @{
                                    if (Model.IDContato == 0)
                                    {
                                        @Html.Hidden("IDContato", Model.IDContato)
                                        @Html.TextBox("Novo", "Novo Contato", new { @class = "form-control", @disabled = "disabled" })
                                    }
                                    else
                                    {
                                        @Html.TextBoxFor(model => model.IDContato, new { @class = "form-control", @disabled = "disabled" })
                                    }
                                }

                            </div>
                            <div class="col-lg-offset-4 col-lg-6">
                                <div class="ibox-tools">
                                    <a href='@("/Configuracao/Contatos?IDCliente=" + @Model.IDCliente)' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                    <button type="button" class="btn btn-primary" id="BotaoSalvar" onclick="Salvar();" disabled="">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                </div>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="tabs-container">
                                    <ul class="nav nav-tabs">
                                        <li class="active"><a data-toggle="tab" href="#tab-1"><i class="fa fa-cogs"></i> @SmartEnergy.Resources.ConfiguracaoTexts.Geral</a></li>
                                    </ul>
                                    <div class="tab-content">

                                        <div id="tab-1" class="tab-pane active">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Contato</label>
                                                        @Html.TextBoxFor(model => model.Nome, new { @class = "form-control", @maxlength = "50", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;Email</label>
                                                        @Html.TextBoxFor(model => model.Email, new { @class = "form-control", @type = "email", @maxlength = "80", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                                <br />

                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Telefone</label>
                                                        @{
                                                            if (Model.Telefone == null)
                                                            {
                                                               @Html.TextBoxFor(model => model.Telefone, new { @class = "form-control", data_mask = "(99) 9999-9999?9", @disabled = "disabled" })
                                                            }
                                                            else
                                                            {
                                                                if (Model.Telefone.Length > 14)
                                                                {
                                                                    @Html.TextBoxFor(model => model.Telefone, new { @class = "form-control", data_mask = "(99) 99999-999?9", @disabled = "disabled" })
                                                                }
                                                                else
                                                                {
                                                                    @Html.TextBoxFor(model => model.Telefone, new { @class = "form-control", data_mask = "(99) 9999-9999?9", @disabled = "disabled" })
                                                                }
                                                            }
                                                        }

                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Ramal</label>
                                                        @Html.TextBoxFor(model => model.Ramal, new { @class = "form-control", @maxlength = "6", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                                <br />

                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Celular</label>
                                                        @{
                                                            if (Model.Celular == null)
                                                            {
                                                                @Html.TextBoxFor(model => model.Celular, new { @class = "form-control", data_mask = "(99) 9999-9999?9", @disabled = "disabled" })
                                                            }
                                                            else
                                                            {
                                                                if (Model.Celular.Length > 14)
                                                                {
                                                                    @Html.TextBoxFor(model => model.Celular, new { @class = "form-control", data_mask = "(99) 99999-999?9", @disabled = "disabled" })
                                                                }
                                                                else
                                                                {
                                                                    @Html.TextBoxFor(model => model.Celular, new { @class = "form-control", data_mask = "(99) 9999-9999?9", @disabled = "disabled" })
                                                                }
                                                            }
                                                        }
                                                    </div>
                                                </div>
                                                <br />

                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Cargo</label>
                                                        @Html.TextBoxFor(model => model.Cargo, new { @class = "form-control", @maxlength = "50", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Departamento</label>
                                                        @Html.TextBoxFor(model => model.Departamento, new { @class = "form-control", @maxlength = "50", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="overlay_aguarde" style="display: none">
                        <div class="spinner-aguarde">
                            <div class="sk-spinner sk-spinner-wandering-cubes">
                                <div class="sk-spinner sk-spinner-wave">
                                    <div class="sk-rect1 text-ligh"></div>
                                    <div class="sk-rect2"></div>
                                    <div class="sk-rect3"></div>
                                    <div class="sk-rect4"></div>
                                    <div class="sk-rect5"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            }

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


<script type="text/javascript">

    $(document).ready(function () {

        //
        // VALIDACAO DOS CAMPOS
        //

        jQuery.validator.addMethod("alphanumeric", function (value, element) {
            return this.optional(element) || /^[\.\^\~\´\`áàãâéèêíìóòõôúùçA-Za-z0-9\s&+\-_,()/\\]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

        jQuery.validator.addMethod("numeric", function (value, element) {
            return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.numeric");

        jQuery.validator.addMethod("cnpj", function (value, element) {

            var numeros, digitos, soma, i, resultado, pos, tamanho, digitos_iguais;
            if (value.length == 0) {
                return false;
            }

            value = value.replace(/\D+/g, '');
            digitos_iguais = 1;

            for (i = 0; i < value.length - 1; i++)
                if (value.charAt(i) != value.charAt(i + 1)) {
                    digitos_iguais = 0;
                    break;
                }
            if (digitos_iguais)
                return false;

            tamanho = value.length - 2;
            numeros = value.substring(0, tamanho);
            digitos = value.substring(tamanho);
            soma = 0;
            pos = tamanho - 7;
            for (i = tamanho; i >= 1; i--) {
                soma += numeros.charAt(tamanho - i) * pos--;
                if (pos < 2)
                    pos = 9;
            }
            resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
            if (resultado != digitos.charAt(0)) {
                return false;
            }
            tamanho = tamanho + 1;
            numeros = value.substring(0, tamanho);
            soma = 0;
            pos = tamanho - 7;
            for (i = tamanho; i >= 1; i--) {
                soma += numeros.charAt(tamanho - i) * pos--;
                if (pos < 2)
                    pos = 9;
            }

            resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;

            return (resultado == digitos.charAt(1));
        }, "@SmartEnergy.Resources.ValidateTexts.cnpj");


        $("#form").validate({
            rules: {
                Nome: {
                    required: true,
                    alphanumeric: true,
                    minlength: 3,
                    maxlength: 50
                },
                Email: {
                    required: true
                },
                Telefone: {  
                    required: true
                }
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            }
        });

        'use strict';

        // desabilita campos
        disableAll();

        $('form input').on('keypress', function (e) {
            return e.which !== 13;
        });
    });


    function disableAll() {

        var permissao = Math.ceil(@ViewBag.Permissao);

        // verifica permissao
        switch (permissao) {
            case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
            case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
            case PERMISSOES_CONSULTOR:          // 5 - permissao de Consultor
            case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte 

                $("#BotaoSalvar").attr('disabled', false);

                $("#Nome").attr('disabled', false);
                $("#Email").attr('disabled', false);
                $("#Telefone").attr('disabled', false);
                $("#Ramal").attr('disabled', false);
                $("#Celular").attr('disabled', false);
                $("#Cargo").attr('disabled', false);
                $("#Departamento").attr('disabled', false);
                break;

            default:
            case PERMISSOES_GERENTE:    // 1 - permissao de Gerente: ve tudo e escreve parte
            case PERMISSOES_OPERADOR:   // 2 - permissao de Operador: ve tudo e nao pode escrever
            case PERMISSOES_PRODUCAO:   // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                $("#BotaoSalvar").attr('disabled', true);
                break;
        }
    }


    //
    // BOTAO SALVAR (SUBMIT)
    //

    function Salvar() {

        var $form = $('#form');

        // apresenta primeiro tab que tiver erro
        var IsTabValid = true;
        var TabInvalid = 1;

        $(".tab-content").find("div.tab-pane").each(function (index, tab) {
            var id = $(tab).attr("id");
            $('a[href="#' + id + '"]').tab('show');

            IsTabValid = $("#form").valid();

            if (!IsTabValid) {

                TabInvalid = id;
                return false;
            }
        });

        // apresenta primeira tab ou com erro
        $('a[href="#tab-' + TabInvalid + '"]').tab('show');

        // verifica se entradas sao validas
        if (!IsTabValid) return false;

        swal({
            title: "Deseja salvar ?",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#1ab394",
            confirmButtonText: "Salvar",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {
            event.stopPropagation();

            // aguarde
            $('.overlay_aguarde').toggle();

            // retiro campos desabilitados, pois nao envia via POST
            $(":disabled", $('#form')).removeAttr("disabled");

            $.ajax({
                url: '/Configuracao/Contato_Salvar',
                data: $form.serialize(),
                type: 'POST',
                success: function (data) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO")
                        {
                            swal({
                                html: true,
                                title: "Erro",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                            });
                        }
                        else
                        {
                            swal({
                                title: "Salvo com sucesso",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // redireciona para pagina lista de contatos
                                var url = '/Configuracao/Contatos?IDCliente=' + document.getElementById('IDCliente').value;
                                window.location.href = url;
                            });
                        }

                    }, 100);

                },
                error: function (xhr, status, error) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao salvar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });
    };

    </script>
}
