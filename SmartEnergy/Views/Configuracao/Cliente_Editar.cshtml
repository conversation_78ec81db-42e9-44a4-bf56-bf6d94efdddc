﻿@model SmartEnergyLib.SQL.ClientesDominio

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoCliente;
}

@{
    int IDTipoAcesso = ViewBag._IDTipoAcesso;
    int IDConsultor = ViewBag._IDConsultor;

    string display_gestal = "none";
    
    // somente GESTAL
    if (isUser.isGESTAL(IDTipoAcesso))
    {
        display_gestal = "block";
    }
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("Cliente_Editar", "Configuracao", FormMethod.Post, new { id = "form", role = "form" }))
            {
                @Html.Hidden("CliVisual", Model.CliVisual)

                                
                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoCliente</h4>
                        <div class="pull-right dashboard-tools link_branco" style="margin-top:-8px;">
                            <h4>

                                @{
                                    int IDCliente = ViewBag._IDCliente;

                                    if (IDCliente > 0)
                                    {
                                        <a href='@("/Configuracao/Menu?IDCliente=" + @IDCliente.ToString())' title="Menu @SmartEnergy.Resources.ConfiguracaoTexts.Configuracoes @SmartEnergy.Resources.ConfiguracaoTexts.ClienteAtual"><i class="fa fa-th-large"></i></a>
                                    }
                                }

                            </h4>
                        </div>
                    </div>

                    <div class="panel-body">
                        <div class="row">
                            <div class="form-group col-lg-2">
                                <label class="control-label">&nbsp;ID</label>

                                @{
                                    if (Model.IDCliente == 0)
                                    {
                                         @Html.Hidden("IDCliente", Model.IDCliente)
                                         @Html.TextBox("Novo", "Novo Cliente", new { @class = "form-control", @disabled = "disabled" })
                                    }
                                    else
                                    {
                                        @Html.TextBoxFor(model => model.IDCliente, new { @class = "form-control", @disabled = "disabled" })
                                    }
                                }

                            </div>
                            <div class="col-lg-offset-4 col-lg-6">
                                <div class="ibox-tools">
                                    <a href='@("/Configuracao/Clientes")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                    <button type="button" class="btn btn-primary" id="BotaoSalvar" onclick="Salvar(1,0);" disabled="">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                </div>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="tabs-container">
                                    <ul class="nav nav-tabs">
                                        <li class="active"><a data-toggle="tab" href="#tab-1"><i class="fa fa-cogs"></i> @SmartEnergy.Resources.ConfiguracaoTexts.Geral</a></li>
                                        <li class="tab-2"><a data-toggle="tab" href="#tab-2"><i class="fa fa-th"></i> @SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoAgentesFiliais</a></li>
                                        <li class="tab-3"><a data-toggle="tab" href="#tab-3"><i class="fa fa-tasks"></i> @SmartEnergy.Resources.ConfiguracaoTexts.Personalizacao</a></li>
                                    </ul>
                                    <div class="tab-content">

                                        <div id="tab-1" class="tab-pane active">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;Status</label>
                                                        @Html.DropDownListFor(model => model.IDTipoStatusAtivo, new SelectList(ViewBag.listaTipoStatus, "ID", "Descricao"),
                                                        string.Format("{0} Status", SmartEnergy.Resources.ConfiguracaoTexts.Selecione),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Gestao</label>
                                                        @Html.DropDownListFor(model => model.IDConsultor, new SelectList(ViewBag.listaConsultores, "IDConsultor", "Nome"),
                                                            @SmartEnergy.Resources.ConfiguracaoTexts.SelecioneConsultor,
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                                <hr />
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Sigla</label>
                                                        @Html.TextBoxFor(model => model.Fantasia, new { @class = "form-control", @maxlength = "50", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.RazaoSocial</label>
                                                        @Html.TextBoxFor(model => model.Nome, new { @class = "form-control", @maxlength = "50", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.RamoAtividade</label>
                                                        @Html.DropDownListFor(model => model.IDRamoAtividade, new SelectList(ViewBag.listaTipoRamoAtividade, "ID", "Descricao"),
                                                        "Sem Ramo de Atividade", new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                                <hr />

                                                <div class="row">
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Logo</label><br />
                                                        @Html.TextBoxFor(model => model.Logo, new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-3" id="BotaoUploadDIV" style="visibility:hidden">
                                                        <div id="progress" class="progress">
                                                            <div class="progress-bar progress-bar-success bar" style="width: 0%; height:20px;"></div>
                                                        </div>
                                                        <span id="BotaoUpload" class="btn btn-primary fileinput-button" style="height:30px; width:100%; margin-top:-18px; padding-top:4px;">
                                                            <span>Selecione a Logomarca</span>
                                                            @Html.TextBoxFor(model => model.Logo, new { id = "fileupload", name = "fileupload", type = "file", accept = "image/png, image/jpeg, image/bmp, image/gif" })
                                                        </span>
                                                    </div>
                                                    <div style="display:@display_gestal ;">
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.TipoContrato</label>
                                                            @Html.DropDownListFor(model => model.IDTipoContrato, new SelectList(ViewBag.listaTipoContrato, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.TipoContrato),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="form-group col-lg-2">

                                                        @{
                                                            string logo;

                                                            if (Model.Logo.IsEmpty())
                                                            {
                                                                logo = "/Logos/LogoSmartEnergy.png";
                                                            }
                                                            else
                                                            {
                                                                logo = "/Logos/" + Model.Logo;
                                                            }
                                                        }

                                                        <img src=@logo id="LogoImagem" class="img-thumbnail" style="margin-top:0px; background-color:#233545; max-height:65px; height: auto;" /><br /><br />
                                                    </div>
                                                    <div class="form-group col-lg-offset-1 col-lg-3" id="DimensoesDIV" style="visibility:hidden">
                                                        <label>(Dimensões 120x45 pixels)</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div id="tab-2" class="tab-pane">
                                            <div class="panel-body">

                                                @{
                                                    if (Model.IDCliente == 0)
                                                    {
                                                        <br />
                                                        <br />
                                                        <div class="row">
                                                            <div class="col-lg-1" style="text-align:center;">
                                                                <i class="fa fa-warning fa-5x"></i>
                                                            </div>
                                                            <div class="col-lg-11">
                                                                <h2>É necessário salvar o cliente para editar os Agentes e Filiais.</h2>
                                                            </div>
                                                        </div>
                                                        <br />
                                                        <br />
                                                    }
                                                    else
                                                    {
                                                        <div class="row">
                                                            <div class="col-lg-3">
                                                                <div class="link_branco">
                                                                    <a href="#" onclick="javascript:EditarEmpresa(0);" id="BotaoAdicionarEmpresa" class="btn btn-info pull-left" style="color:#ffffff; width:100%;" disabled="">
                                                                        <span style="font-size:large">@SmartEnergy.Resources.ConfiguracaoTexts.AdicionarEmpresa</span>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <table class="table table-striped table-bordered table-hover dataTables-empresas">
                                                            <thead>
                                                                <tr>
                                                                    <th>ID</th>
                                                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.Logo</th>
                                                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.SiglaCCEE - @SmartEnergy.Resources.ConfiguracaoTexts.RazaoSocial</th>
                                                                    <th class="some_minidesktop">CNPJ</th>
                                                                    <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.TipoEmpresa</th>
                                                                    <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.Agente</th>
                                                                    <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.Agente [Matriz]</th>
                                                                    <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.Cidade</th>
                                                                    <th></th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>

                                                                @{
                                                                    List<EmpresasDominio> empresas = ViewBag.listaEmpresas;

                                                                    foreach (EmpresasDominio empresa in empresas)
                                                                    {
                                                                        logo = "http://www.smartenergy.com.br/";

                                                                        if (empresa.Logo.IsEmpty())
                                                                        {
                                                                            logo += "/Logos/LogoSmartEnergy.png";
                                                                        }
                                                                        else
                                                                        {
                                                                            logo += "/Logos/" + empresa.Logo;
                                                                        }
                                
                                                                        // tipo empresa
                                                                        List<ListaTiposDominio> listatiposEmpresa = ViewBag.listaTipoEmpresa;
                                                                        ListaTiposDominio tipo = listatiposEmpresa.Find(item => item.ID == empresa.IDTipoEmpresa);
                                                                        
                                                                        string tipo_empresa = "---";
                                                                        if (tipo != null)
                                                                        {
                                                                            tipo_empresa = tipo.Descricao;
                                                                        }
                                                                        
                                                                        // agente
                                                                        List<ListaTiposDominio> listaTipoSimNao = ViewBag.listaTipoSimNao;
                                                                        tipo = listaTipoSimNao.Find(item => item.ID == empresa.Agente);

                                                                        string agente = "---";
                                                                        if (tipo != null)
                                                                        {
                                                                            agente = tipo.Descricao;
                                                                        }
                                                                        
                                                                        // agente [matriz]
                                                                        List<EmpresasDominio> agentesCCEE = ViewBag.listaAgentesCCEE;
                                                                        EmpresasDominio agenteCCEE = agentesCCEE.Find(item => item.IDEmpresa == empresa.IDEmpresa_AgenteCCEE);
                                                                        string agente_ccee_sigla = "---";
                                                                        string agente_ccee_razaosocial = "";
                                                                        if (agenteCCEE != null)
                                                                        {
                                                                            agente_ccee_sigla = agenteCCEE.SiglaCCEE;
                                                                            agente_ccee_razaosocial = agenteCCEE.RazaoSocial;
                                                                        }
                                                                        
                                                                        if (empresa.IDTipoEmpresa == TIPO_EMPRESA.AgenteCCEE)
                                                                        {
                                                                            agente_ccee_sigla = "---";
                                                                            agente_ccee_razaosocial = "";
                                                                        }
                                                                        
                                                                        // tipo cidade
                                                                        List<CidadesDominio> listacidades = ViewBag.listaTipoCidade;
                                                                        CidadesDominio cidade = listacidades.Find(item => item.IDCidade == empresa.IDCidade);
                                                                        string tipo_cidade = "";
                                                                        if (cidade != null)
                                                                        {
                                                                            tipo_cidade = cidade.Nome;
                                                                        }
                                                                                                
                                                                        <tr>
                                                                            <td>@empresa.IDEmpresa</td>
                                                                            <td bgcolor="#293846" align="center"><img src=@logo style="max-height:45px; height: auto;" /></td>
                                                                            <td><b>@empresa.SiglaCCEE</b><span style="display:none;">@empresa.IDTipoEmpresa</span><br />@empresa.RazaoSocial</td>
                                                                            <td class="some_minidesktop">@empresa.CNPJ</td>
                                                                            <td class="some_minidesktop">@tipo_empresa</td>
                                                                            <td class="some_minidesktop">@agente</td>
                                                                            <td class="some_minidesktop"><b>@agente_ccee_sigla</b><br />@agente_ccee_razaosocial</td>
                                                                            <td class="some_minidesktop">@tipo_cidade</td>
                                                                            <td class="link_preto">
                                                                                <a href="#" onclick="javascript:EditarEmpresa(@empresa.IDEmpresa);" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"><i class="fa fa-edit icones"></i></a>

                                                                                @if (ViewBag.Permissao == PERMISSOES.ADMIN)
                                                                                {
                                                                                    <a href="#" onclick="javascript:ExcluirEmpresa(@empresa.IDEmpresa, '@empresa.SiglaCCEE');" id="BotaoExcluir" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-times icones"></i></a>
                                                                                }

                                                                            </td>
                                                                        </tr>
                                                                    }
                                                                }                                                            

                                                            </tbody>
                                                        </table>
                                                    }
                                                }

                                            </div>
                                        </div>

                                        <div id="tab-3" class="tab-pane">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.NomeGrupoUnidades</label>
                                                        @Html.TextBoxFor(model => model.Nome_GrupoUnidades, new { @class = "form-control", @maxlength = "50", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.NomeUnidades</label>
                                                        @Html.TextBoxFor(model => model.Nome_Unidades, new { @class = "form-control", @maxlength = "50", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                                <br />

                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.TipoSupervisao</label>
                                                        @Html.DropDownListFor(model => model.IDTipoSupervisao, new SelectList(ViewBag.listaTipoSupervisao, "ID", "Descricao"),
                                                        string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.TipoSupervisao),
                                                        new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.GraficoDemanda</label>
                                                        @Html.DropDownListFor(model => model.IDTipoGrafico_Demanda, new SelectList(ViewBag.listaTipoGrafico, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.GraficoDemanda),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="overlay_aguarde" style="display: none">
                        <div class="spinner-aguarde">
                            <div class="sk-spinner sk-spinner-wandering-cubes">
                                <div class="sk-spinner sk-spinner-wave">
                                    <div class="sk-rect1 text-ligh"></div>
                                    <div class="sk-rect2"></div>
                                    <div class="sk-rect3"></div>
                                    <div class="sk-rect4"></div>
                                    <div class="sk-rect5"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            }

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
    @Styles.Render("~/datetimePickerStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
    @Styles.Render("~/jQuery.FileUpload/FileUploadStyles")
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/plugins/iCheck")
    @Scripts.Render("~/jQuery.FileUpload/FileUpload")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
        <script type="text/javascript" language="javascript" src="~/Scripts/declaracoes/Traducao-pt-br.js"></script>
    }


    <script type="text/javascript">


    var jqXHRData;

    $(document).ready(function () {

        // classe iChecks
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });


        //
        // VALIDACAO DOS CAMPOS
        //

        jQuery.validator.addMethod("alphanumeric", function (value, element) {
            return this.optional(element) || /^[\'\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\&\+\-\_\(\)," "]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

        jQuery.validator.addMethod("numeric", function (value, element) {
            return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.numeric");

        jQuery.validator.addMethod("cnpj", function (value, element) {

            var numeros, digitos, soma, i, resultado, pos, tamanho, digitos_iguais;
            if (value.length == 0) {
                return false;
            }

            value = value.replace(/\D+/g, '');
            digitos_iguais = 1;

            for (i = 0; i < value.length - 1; i++)
                if (value.charAt(i) != value.charAt(i + 1)) {
                    digitos_iguais = 0;
                    break;
                }
            if (digitos_iguais)
                return false;

            tamanho = value.length - 2;
            numeros = value.substring(0, tamanho);
            digitos = value.substring(tamanho);
            soma = 0;
            pos = tamanho - 7;
            for (i = tamanho; i >= 1; i--) {
                soma += numeros.charAt(tamanho - i) * pos--;
                if (pos < 2)
                    pos = 9;
            }
            resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
            if (resultado != digitos.charAt(0)) {
                return false;
            }
            tamanho = tamanho + 1;
            numeros = value.substring(0, tamanho);
            soma = 0;
            pos = tamanho - 7;
            for (i = tamanho; i >= 1; i--) {
                soma += numeros.charAt(tamanho - i) * pos--;
                if (pos < 2)
                    pos = 9;
            }

            resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;

            return (resultado == digitos.charAt(1));
        }, "@SmartEnergy.Resources.ValidateTexts.cnpj");


        $("#form").validate({
            rules: {
                Nome: {
                    required: true,
                    alphanumeric: true,
                    minlength: 3,
                    maxlength: 50
                },
                Fantasia: {
                    required: true,
                    alphanumeric: true,
                    minlength: 3,
                    maxlength: 50
                },
                IDTipoContrato: {
                    required: true
                },

                Nome_GrupoUnidades: {
                    required: true,
                    alphanumeric: true,
                    minlength: 3,
                    maxlength: 50
                },
                Nome_Unidades: {
                    required: true,
                    alphanumeric: true,
                    minlength: 3,
                    maxlength: 50
                },
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            }
        });

        'use strict';

        //
        // LOGO
        //

        $('#fileupload').fileupload({
            url: '/FileUpload/UploadFile',
            dataType: 'json',
            add: function (e, data) {
                jqXHRData = data;
                jqXHRData.submit();
            },
            acceptFileTypes: /(\.|\/)(png|jpe?g|bmp|gif)$/i,
            done: function (e, data) {

                if (data.result.isUploaded) {
                    $("#Logo").val(data.files[0].name);
                    document.getElementById('LogoImagem').src = "/Logos/" + data.files[0].name;
                }
                else {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: data.result.message,
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);
                }

                setTimeout(function () {

                    $('#progress .progress-bar').css(
                        'width',
                        0 + '%'
                    );

                }, 2000);

            },
            fail: function (event, data) {
                if (data.files[0].error) {
                    alert(data.files[0].error);
                }
            },
            progressall: function (e, data) {
                var progress = parseInt(data.loaded / data.total * 100, 10);
                $('#progress .progress-bar').css(
                    'width',
                    progress + '%'
                );
            }
        }).prop('disabled', !$.support.fileInput)
                .parent().addClass($.support.fileInput ? undefined : 'disabled');

        // desabilita campos
        disableAll();

        //
        // TABELA EMPRESAS
        //

        var permissao = Math.ceil(@ViewBag.Permissao);
        var visivel = true;

        if (permissao == PERMISSOES_ADMIN)
        {
            visivel = true;
        }

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {
                var x = (a == "-") ? 0 : a.replace(/,/, ".");
                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-empresas').DataTable({
            "iDisplayLength": 18,
            dom: 'ftp',

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "visible": visivel, "sType": "numero" },
                        { "aTargets": [1], "sType": "portugues", "bSortable": false, "bSearchable": false },
                        { "aTargets": [2], "sType": "portugues" },
                        { "aTargets": [3], "sType": "portugues" },
                        { "aTargets": [4], "sType": "portugues" },
                        { "aTargets": [5], "sType": "portugues" },
                        { "aTargets": [6], "sType": "portugues" },
                        { "aTargets": [7], "sType": "portugues" },
                        { "aTargets": [8], "bVisible": true, "bSortable": false, "bSearchable": false },
            ],

            "aoColumns": [
                { sWidth: "4%" },
                { sWidth: "10%" },
                { sWidth: "20%" },
                { sWidth: "12%" },
                { sWidth: "12%" },
                { sWidth: "9%" },
                { sWidth: "13%" },
                { sWidth: "10%" },
                { sWidth: "10%" }
            ],
            'order': [2, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });


        $('form input').on('keypress', function (e) {
            return e.which !== 13;
        });

        // caso for selecionado alguma TAB, aponto para ele
        // utilizado caso retornou pela configuração da empresa para ir na TAB empresas
        var url = document.location.toString();

        if (url.match('#')) {
            $('.nav-tabs a[href="#' + url.split('#')[1] + '"]').tab('show');

            setTimeout(function () {
                window.scrollTo(0, 0);
            }, 100);
        }

    });

    function disableAll() {

        var permissao = Math.ceil(@ViewBag.Permissao);

        // verifica permissao
        switch (permissao) {
            case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
            case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir

                $("#IDConsultor").attr('disabled', false);

            case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte 

                $("#BotaoAdicionarEmpresa").attr('disabled', false);

                $("#BotaoSalvar").attr('disabled', false);
                $("#IDTipoStatusAtivo").attr('disabled', false);
                $("#Nome").attr('disabled', false);
                $("#Fantasia").attr('disabled', false);

                $("#IDRamoAtividade").attr('disabled', false);
                
                $("#IDTipoContrato").attr('disabled', false);

                document.getElementById("BotaoUploadDIV").style.visibility = "visible";
                document.getElementById("DimensoesDIV").style.visibility = "visible";
                $("#fileupload").attr('disabled', false);

                $("#Nome_GrupoUnidades").attr('disabled', false);
                $("#Nome_Unidades").attr('disabled', false);
                $("#IDTipoSupervisao").attr('disabled', false);
                $("#IDTipoGrafico_Demanda").attr('disabled', false);
                break;

            case PERMISSOES_PRODUCAO:   // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                $("#BotaoSalvar").attr('disabled', false);
                $("#IDConsultor").attr('disabled', false);
                $("#Nome").attr('disabled', false);
                $("#Fantasia").attr('disabled', false);
                break;

            case PERMISSOES_CONSULTOR:  // 5 - permissao de Consultor

                $("#BotaoSalvar").attr('disabled', false);
                $("#Nome").attr('disabled', false);
                $("#Fantasia").attr('disabled', false);

                $("#Nome_GrupoUnidades").attr('disabled', false);
                $("#Nome_Unidades").attr('disabled', false);
                $("#IDTipoGrafico_Demanda").attr('disabled', false);

                break;

            default:
            case PERMISSOES_GERENTE:    // 1 - permissao de Gerente: ve tudo e escreve parte
            case PERMISSOES_OPERADOR:   // 2 - permissao de Operador: ve tudo e nao pode escrever

                $("#BotaoSalvar").attr('disabled', true);
                break;
        }
    }


    //
    // BOTAO SALVAR (SUBMIT)
    //

    $.fn.serializeObject = function () {
        var o = {};
        var a = this.serializeArray();
        $.each(a, function () {
            if (o[this.name] !== undefined) {
                if (!o[this.name].push) {
                    o[this.name] = [o[this.name]];
                }
                o[this.name].push(this.value || '');
            } else {
                o[this.name] = this.value || '';
            }
        });
        return o;
    };

    function Salvar(mostrar_pergunta, IDEmpresaDesejada) {

        var $form = $('#form');

        // tab atual
        var tab_atual = $('.tab-content .active').attr('id');

        // apresenta primeiro tab que tiver erro
        var IsTabValid = true;
        var TabInvalid = 1;

        $(".tab-content").find("div.tab-pane").each(function (index, tab) {
            var id = $(tab).attr("id");
            $('a[href="#' + id + '"]').tab('show');

            IsTabValid = $("#form").valid();

            if (!IsTabValid) {

                TabInvalid = id;
                return false;
            }
        });

        // apresenta primeira tab ou com erro
        $('a[href="#tab-' + TabInvalid + '"]').tab('show');

        // verifica se entradas sao validas
        if (!IsTabValid) return false;


        // Caso 1: usuário esta apenas salvando o cliente.
        // Caso 0: usuário deseja editar a empresa e precisa salvar o cliente antes.
        if (mostrar_pergunta == 1)
        {
            var IDClienteDesejado = document.getElementById("IDCliente").value;
            var pergunta = "Deseja salvar ?";

            if (IDClienteDesejado == 0)
            {
                pergunta = "Deseja criar o Cliente ?"
            }

            swal({
                title: pergunta,
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#1ab394",
                confirmButtonText: "Salvar",
                cancelButtonText: "Cancelar",
                closeOnConfirm: true
            }, function () {
                event.stopPropagation();

                // aguarde
                $('.overlay_aguarde').toggle();

                // retiro campos desabilitados, pois nao envia via POST
                $(":disabled", $('#form')).removeAttr("disabled");

                // constroi estrutura com FORM
                data = { 'cliente': $form.serializeObject() };
                data2 = JSON.stringify(data);

                $.ajax({
                    contentType: 'application/json; charset=utf-8',
                    dataType: 'json',
                    url: '/Configuracao/Cliente_Salvar',
                    data: data2,
                    type: 'POST',
                    success: function (result) {

                        // fim
                        $('.overlay_aguarde').toggle();

                        setTimeout(function () {

                            // verifica se erro
                            if (result.status == "ERRO") {

                                swal({
                                    html: true,
                                    title: "Erro",
                                    text: result.erro,
                                    type: "warning",
                                    confirmButtonColor: "#f8ac59",
                                    confirmButtonText: "Fechar",
                                });

                            }
                            else {

                                swal({
                                    title: "Salvo com sucesso",
                                    type: "success",
                                    confirmButtonColor: "#18a689",
                                    confirmButtonText: "Fechar",
                                }, function () {

                                    // verifica se está adicionando cliente novo e esta na aba de empresas
                                    // suponho que está salvando para poder criar alguma empresa adicional
                                    if (result.IDCliente > 0 && IDClienteDesejado == 0 && tab_atual == "tab-2")
                                    {
                                        // releio a pagina e posiciono na aba empresa
                                        var url = '/Configuracao/Cliente_Editar?IDCliente=' + result.IDCliente.toString() + '#tab-2';
                                        window.location.href = url;
                                    }
                                    else
                                    {
                                        // redireciona para pagina lista de clientes
                                        var url = '/Configuracao/Clientes';
                                        window.location.href = url;
                                    }

                                });
                            }

                        }, 100);

                    },
                    error: function (xhr, status, error) {

                        // fim
                        $('.overlay_aguarde').toggle();

                        setTimeout(function () {

                            swal({
                                title: "Erro",
                                text: "Erro ao salvar!",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            });

                        }, 100);

                    }
                });
            });

        }
        else
        {
            event.stopPropagation();

            // aguarde
            $('.overlay_aguarde').toggle();

            // retiro campos desabilitados, pois nao envia via POST
            $(":disabled", $('#form')).removeAttr("disabled");

            // constroi estrutura com FORM
            data = { 'cliente': $form.serializeObject() };
            data2 = JSON.stringify(data);

            $.ajax({
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                url: '/Configuracao/Cliente_Salvar',
                data: data2,
                type: 'POST',
                success: function (result) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        // verifica se erro
                        if (result.status == "ERRO") {
                            swal({
                                html: true,
                                title: "Erro",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            });

                        }
                        else {

                            // redireciona para pagina editar empresa
                            // retorno 0 - voltar para janela Empresas
                            // retorno 1 - voltar para janela Cliente_Editar (aba Agentes e Filiais)
                            var url = "/Configuracao/Empresa_Editar?IDEmpresa=" + IDEmpresaDesejada.toString() + "&Retorno=1";
                            window.location.href = url;

                        }

                    }, 100);

                },
                error: function (xhr, status, error) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao salvar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });

        }
    };

    //
    // EMPRESA
    //

    function EditarEmpresa(IDEmpresa) {
        event.stopPropagation();

        // titulo
        titulo = "É necessário salvar as informações para continuar.";

        swal({
            html: true,
            title: titulo,
            text: "<h2>Deseja salvar ?</h2>",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Salvar",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            // salvar cliente e editar empresa
            Salvar(0, IDEmpresa);

        });
    }

    function ExcluirEmpresa(IDEmpresa, SiglaCCEE) {
        event.stopPropagation();

        // titulo
        titulo = "Deseja excluir a Empresa ?<br/>" + SiglaCCEE;

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            // excluir
            $.ajax(
            {
                type: 'GET',
                url: '/Configuracao/Empresa_Excluir',
                data: { 'IDEmpresa': IDEmpresa },
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO")
                        {
                            swal({
                                html: true,
                                title: "Erro",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                            });
                        }
                        else
                        {
                            swal({
                                title: "Excluído com sucesso",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // atualiza pagina
                                location.reload();
                            });
                        }

                    }, 100);
                },
                error: function (response) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao excluir!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });
    }

    </script>
}
