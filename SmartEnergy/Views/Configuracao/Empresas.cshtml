﻿@model IEnumerable<SmartEnergyLib.SQL.EmpresasDominio>

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoAgentesFiliais;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoAgentesFiliais</h4>
                    <div class="pull-right dashboard-tools link_branco" style="margin-top:-8px;">
                        <h4>

                            @{
                                int IDCliente = ViewBag._IDCliente;

                                if (IDCliente > 0)
                                {
                                    <a href='@("/Configuracao/Menu?IDCliente=" + @IDCliente.ToString())' title="Menu @SmartEnergy.Resources.ConfiguracaoTexts.Configuracoes @SmartEnergy.Resources.ConfiguracaoTexts.ClienteAtual"><i class="fa fa-th-large"></i></a>
                                }
                            }

                            <a href='@("/Configuracao/Empresa_Editar?IDEmpresa=0")' id="BotaoAdicionar" title="@SmartEnergy.Resources.ComumTexts.BotaoInserir"><i class="fa fa-plus"></i></a>

                        </h4>
                    </div>
                </div>
                <div class="panel-body">
                    <table class="table table-striped table-bordered table-hover dataTables-empresas">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.Logo</th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.SiglaCCEE - @SmartEnergy.Resources.ConfiguracaoTexts.RazaoSocial</th>
                                <th class="some_minidesktop">CNPJ</th>
                                <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.TipoEmpresa</th>
                                <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.Agente</th>
                                <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.Cidade</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>

                            @foreach (EmpresasDominio empresa in Model)
                            {
                                string logo = "http://www.smartenergy.com.br/";

                                if (empresa.Logo.IsEmpty())
                                {
                                    logo += "/Logos/LogoSmartEnergy.png";
                                }
                                else
                                {
                                    logo += "/Logos/" + empresa.Logo;
                                }

                                // tipo empresa
                                List<ListaTiposDominio> listatiposEmpresa = ViewBag.listaTipoEmpresa;
                                ListaTiposDominio tipo = listatiposEmpresa.Find(item => item.ID == empresa.IDTipoEmpresa);

                                string tipo_empresa = "---";
                                if (tipo != null)
                                {
                                    tipo_empresa = tipo.Descricao;
                                }
                               
                                // agente
                                List<ListaTiposDominio> listaTipoSimNao = ViewBag.listaTipoSimNao;
                                tipo = listaTipoSimNao.Find(item => item.ID == empresa.Agente);

                                string agente = "---";
                                if (tipo != null)
                                {
                                    agente = tipo.Descricao;
                                }
                                
                                // tipo cidade
                                string tipo_cidade = "";
                                if (empresa.IDPais == 1)
                                {
                                    List<CidadesDominio> listacidades = ViewBag.listaTipoCidade;
                                    CidadesDominio cidade = listacidades.Find(item => item.IDCidade == empresa.IDCidade);
                                    if (cidade != null)
                                    {
                                        tipo_cidade = cidade.Nome;
                                    }                                    
                                }
                                else
                                {
                                    tipo_cidade = empresa.NomeCidade;
                                }


                                <tr>
                                    <td>@empresa.IDEmpresa</td>
                                    <td bgcolor="#293846" align="center"><img src=@logo style="max-height:45px; height: auto;" /></td>
                                    <td><b>@empresa.SiglaCCEE</b><span style="display:none;">@empresa.IDTipoEmpresa</span><br />@empresa.RazaoSocial</td>
                                    <td class="some_minidesktop">@empresa.CNPJ</td>
                                    <td class="some_minidesktop">@tipo_empresa</td>
                                    <td class="some_minidesktop">@agente</td>
                                    <td class="some_minidesktop">@tipo_cidade</td>
                                    <td class="link_preto">

                                        <a href='@("/Configuracao/Empresa_Editar?IDEmpresa=" + @empresa.IDEmpresa.ToString())' title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"><i class="fa fa-edit icones"></i></a>

                                        @if (ViewBag.Permissao == PERMISSOES.ADMIN)
                                        {
                                            <a href="#" onclick="javascript:Excluir(@empresa.IDEmpresa);" id="BotaoExcluir" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-times icones"></i></a>
                                        }

                                    </td>
                                </tr>
                            }

                        </tbody>
                    </table>

                </div>
            </div>
        </div>
    </div>


</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


<script type="text/javascript">

    $(document).ready(function () {

        var permissao = Math.ceil(@ViewBag.Permissao);
        var visivel = true;

        if (permissao == PERMISSOES_ADMIN)
        {
            visivel = true;
        }

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {
                var x = (a == "-") ? 0 : a.replace(/,/, ".");
                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-empresas').DataTable({
            "iDisplayLength": 18,
            dom: 'Bftp',
            buttons: [
                {
                    extend: 'excelHtml5',
                    filename: 'Empresas',
                    title: 'Empresas',
                    text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-excel-o" data-toggle="tooltip" data-placement="left" title="Excel"></span>',
                    className: 'btn btn-default btn-xs'
                },
                {
                    extend: 'pdfHtml5',
                    filename: 'Empresas',
                    title: 'Empresas',
                    text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-pdf-o" data-toggle="tooltip" data-placement="left" title="PDF"></span>',
                    className: 'btn btn-default btn-xs'
                },
            ],

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "visible": visivel, "sType": "numero" },
                        { "aTargets": [1], "sType": "portugues", "bSortable": false, "bSearchable": false },
                        { "aTargets": [2], "sType": "portugues" },
                        { "aTargets": [3], "sType": "portugues" },
                        { "aTargets": [4], "sType": "portugues" },
                        { "aTargets": [5], "sType": "portugues" },
                        { "aTargets": [6], "sType": "portugues" },
                        { "aTargets": [7], "bVisible": true, "bSortable": false, "bSearchable": false },
            ],

            "aoColumns": [
                { sWidth: "4%" },
                { sWidth: "10%" },
                { sWidth: "25%" },
                { sWidth: "12%" },
                { sWidth: "12%" },
                { sWidth: "9%" },
                { sWidth: "18%" },
                { sWidth: "10%" }
            ],
            'order': [2, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

        // desabilita campos
        disableAll();
    });

function disableAll() {

    var permissao = Math.ceil(@ViewBag.Permissao);

    // verifica permissao
    switch(permissao)
    {
        case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
        case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
        case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte 

            break;

        default:
        case PERMISSOES_GERENTE:    // 1 - permissao de Gerente: ve tudo e escreve parte
        case PERMISSOES_OPERADOR:   // 2 - permissao de Operador: ve tudo e nao pode escrever
        case PERMISSOES_PRODUCAO:   // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)
        case PERMISSOES_CONSULTOR:  // 5 - permissao de Consultor

            document.getElementById("BotaoAdicionar").style.visibility = "hidden";
            break;
    }
}

function Excluir(IDEmpresa) {
    event.stopPropagation();

    // titulo
    titulo = "Deseja excluir a Empresa?<br/>ID " + IDEmpresa;

    swal({
        html: true,
        title: titulo,
        text: "Esta operação não poderá ser desfeita.",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#18a689",
        confirmButtonText: "Excluir",
        cancelButtonText: "Cancelar",
        closeOnConfirm: false
    }, function () {

        // excluir
        $.ajax(
        {
            type: 'GET',
            url: '/Configuracao/Empresa_Excluir',
            data: { 'IDEmpresa': IDEmpresa },
            contentType: 'application/json; charset=utf-8',
            dataType: 'json',
            cache: false,
            async: true,
            success: function (data) {

                setTimeout(function () {

                    // verifica se erro
                    if (data.status == "ERRO")
                    {
                        swal({
                            html: true,
                            title: "Erro",
                            text: data.erro,
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                        });
                    }
                    else
                    {
                        swal({
                            title: "Excluído com sucesso",
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // atualiza pagina
                            location.reload();
                        });
                    }

                }, 100);
            },
            error: function (response) {

                setTimeout(function () {

                    swal({
                        title: "Erro",
                        text: "Erro ao excluir!",
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    });

                }, 100);

            }
        });
    });
};

</script>
}


