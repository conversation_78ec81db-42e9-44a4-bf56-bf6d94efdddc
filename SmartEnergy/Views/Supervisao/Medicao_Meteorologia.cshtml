﻿@using System.Globalization

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoMedicao;

    int IDConsultor = ViewBag._IDConsultor;
}

<div class="row wrapper border-bottom white-bg page-heading">
    <div class="row">
        <div class="col-lg-12">
            <div class="superv-content">

                <div class="col-lg-4 col-md-12">
                    <div class="row">
                        <div class="col-lg-4 col-md-4">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual superv-ea">
                                    <h4>@ViewBag.NomeGrandeza</h4>
                                    <h5>Atual</h5>
                                    <h1>@ViewBag.Dia_Atual_Atual <span>@ViewBag.UnidadeGrandeza</span></h1>
                                    <h6>@ViewBag.Dia_Atual_DataHora</h6>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-4">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual superv-ea">
                                    <h4>Umidade</h4>
                                    <h5>Atual</h5>
                                    <h1>@ViewBag.Dia_Atual_Umidade <span>%</span></h1>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-4">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual superv-ea">
                                    <h4 class="tempo"></h4>
                                    <canvas id="icon_hoje" width="55" height="55" style="margin-top:5px;"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="superv-tabela superv-ea">
                                <table class="table no-margins" style="width:100%;">
                                    <thead>
                                        <tr>
                                            <th style="width:50%;"></th>
                                            <th style="width:50%;">@ViewBag.NomeGrandeza</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="text-align:center">@SmartEnergy.Resources.DashboardTexts.Maxima<br /><small>&nbsp;</small></td>
                                            <td>@ViewBag.Dia_Atual_Max @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.Dia_Atual_MaxDataHora)</small></td>
                                        </tr>
                                        <tr>
                                            <td style="text-align:center">@SmartEnergy.Resources.DashboardTexts.Media<br /><small>&nbsp;</small></td>
                                            <td>@ViewBag.Dia_Atual_Media @ViewBag.UnidadeGrandeza<br />&nbsp;</td>
                                        </tr>
                                        <tr>
                                            <td style="text-align:center">@SmartEnergy.Resources.DashboardTexts.Minima<br /><small>&nbsp;</small></td>
                                            <td>@ViewBag.Dia_Atual_Min @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.Dia_Atual_MinDataHora)</small></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-5 col-md-12">
                    <div class="row">
                        <div class="superv-grafico">
                            <div class="flot-chart">
                                <div class="postlet-grafico-ea" id="grafico-ea"></div>
                            </div>
                            <div class='chart-legend'>
                                <div class='legend-scale-dem'>
                                    <ul class='legend-labels'>
                                        <li><span style='background:#1C84C6;'></span>@ViewBag.NomeGrandeza</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-12">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual superv-ea">
                                    <h4>Previsão do Tempo</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="superv-tabela superv-ea">
                                <table class="table no-margins" style="width:100%;">
                                    <thead>
                                        <tr>
                                            <th style="width:25%;"></th>
                                            <th style="width:25%;">@SmartEnergy.Resources.ConfiguracaoTexts.Temperatura<br />@SmartEnergy.Resources.DashboardTexts.Maxima</th>
                                            <th style="width:25%;">@SmartEnergy.Resources.ConfiguracaoTexts.Temperatura<br />@SmartEnergy.Resources.DashboardTexts.Minima</th>
                                            <th style="width:25%;">Clima</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="width: 25%; text-align: left;">@ViewBag.DataHora_0<br />@ViewBag.DiaSemana_0</td>
                                            <td style="width: 25%; color:#FF0000; font-weight: bold;">@ViewBag.Temp_Max_0 @ViewBag.UnidadeGrandeza</td>
                                            <td style="width: 25%; color:#0000FF; font-weight: bold;">@ViewBag.Temp_Min_0 @ViewBag.UnidadeGrandeza</td>
                                            <td style="width: 25%;"><canvas id="icon_previsao_0" width="16" height="16"></canvas></td>
                                        </tr>
                                        <tr>
                                            <td style="width: 25%; text-align: left;">@ViewBag.DataHora_1<br />@ViewBag.DiaSemana_1</td>
                                            <td style="width: 25%; color:#FF0000; font-weight: bold;">@ViewBag.Temp_Max_1 @ViewBag.UnidadeGrandeza</td>
                                            <td style="width: 25%; color:#0000FF; font-weight: bold;">@ViewBag.Temp_Min_1 @ViewBag.UnidadeGrandeza</td>
                                            <td style="width: 25%;"><canvas id="icon_previsao_1" width="16" height="16"></canvas></td>
                                        </tr>
                                        <tr>
                                            <td style="width: 25%; text-align: left;">@ViewBag.DataHora_2<br />@ViewBag.DiaSemana_2</td>
                                            <td style="width: 25%; color:#FF0000; font-weight: bold;">@ViewBag.Temp_Max_2 @ViewBag.UnidadeGrandeza</td>
                                            <td style="width: 25%; color:#0000FF; font-weight: bold;">@ViewBag.Temp_Min_2 @ViewBag.UnidadeGrandeza</td>
                                            <td style="width: 25%;"><canvas id="icon_previsao_2" width="16" height="16"></canvas></td>
                                        </tr>
                                        <tr>
                                            <td style="width: 25%; text-align: left;">@ViewBag.DataHora_3<br />@ViewBag.DiaSemana_3</td>
                                            <td style="width: 25%; color:#FF0000; font-weight: bold;">@ViewBag.Temp_Max_3 @ViewBag.UnidadeGrandeza</td>
                                            <td style="width: 25%; color:#0000FF; font-weight: bold;">@ViewBag.Temp_Min_3 @ViewBag.UnidadeGrandeza</td>
                                            <td style="width: 25%;"><canvas id="icon_previsao_3" width="16" height="16"></canvas></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<div class="wrapper wrapper-content">

    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">

                    <div class="col-lg-6 col-md-12">
                        <div class="row">
                            <div class="superv-grafico-fatura">
                                <div class="flot-chart">
                                    <div id="ea-chart"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="superv-tabela superv-ea">
                                    <table class="table no-margins">
                                        <thead>
                                            <tr>
                                                <th style="width: 25%;"></th>
                                                <th style="width: 25%;">@SmartEnergy.Resources.ConfiguracaoTexts.Temperatura<br />@SmartEnergy.Resources.DashboardTexts.Maxima</th>
                                                <th style="width: 25%;">@SmartEnergy.Resources.ConfiguracaoTexts.Temperatura<br />@SmartEnergy.Resources.DashboardTexts.Media</th>
                                                <th style="width: 25%;">@SmartEnergy.Resources.ConfiguracaoTexts.Temperatura<br />@SmartEnergy.Resources.DashboardTexts.Minima</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>@ViewBag.Mes_Atual_Data</td>
                                                <td>@ViewBag.Mes_Atual_Max @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.Mes_Atual_MaxDataHora)</small></td>
                                                <td>@ViewBag.Mes_Atual_Media @ViewBag.UnidadeGrandeza<br /><small>&nbsp;</small></td>
                                                <td>@ViewBag.Mes_Atual_Min @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.Mes_Atual_MinDataHora)</small></td>
                                            </tr>
                                            <tr>
                                                <td>@ViewBag.Mes_Anterior1_Data</td>
                                                <td>@ViewBag.Mes_Anterior1_Max @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.Mes_Anterior1_MaxDataHora)</small></td>
                                                <td>@ViewBag.Mes_Anterior1_Media @ViewBag.UnidadeGrandeza<br /><small>&nbsp;</small></td>
                                                <td>@ViewBag.Mes_Anterior1_Min @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.Mes_Anterior1_MinDataHora)</small></td>
                                            </tr>
                                            <tr>
                                                <td>@ViewBag.Mes_Anterior2_Data</td>
                                                <td>@ViewBag.Mes_Anterior2_Max @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.Mes_Anterior2_MaxDataHora)</small></td>
                                                <td>@ViewBag.Mes_Anterior2_Media @ViewBag.UnidadeGrandeza<br /><small>&nbsp;</small></td>
                                                <td>@ViewBag.Mes_Anterior2_Min @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.Mes_Anterior2_MinDataHora)</small></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <br />
    <div class="row">
        <div class="col-lg-6">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h4>Cidade</h4><br />
                    @ViewBag.CidadeNome
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h4>Estado</h4><br />
                    @ViewBag.EstadoNome
                </div>
            </div>
        </div>
    </div>


    @if( @ViewBag.listaErros.Count > 0 )
    {
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-danger">
                    <div class="panel-heading">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Erros</h4><br />
                        <ul style="list-style-type:disc; margin-left: 20px;">
                            @foreach (var erro in @ViewBag.listaErros)
                            {
                                <li>@erro</li>
                            }
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    }
    
</div>

@section Styles {
    @Styles.Render("~/plugins/c3Styles")
    @Styles.Render("~/plugins/select2Styles")
    @Styles.Render("~/Supervisao.css")
}

@section Scripts {
    @Scripts.Render("~/plugins/d3")
    @Scripts.Render("~/plugins/c3")
    @Scripts.Render("~/plugins/select2")
    @Scripts.Render("~/plugins/skycons")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">
    $(document).ready(function () {

        // meteorologia
        var dataX = [];
        var labelX = [];
        var dataTemperatura = [];

        // copia valores
        var Temperatura = @Html.Raw(Json.Encode(@ViewBag.Media));
        var Umidade = @Html.Raw(Json.Encode(@ViewBag.Umidade));
        var Pressao = @Html.Raw(Json.Encode(@ViewBag.Pressao));
        var Vento_Direcao = @Html.Raw(Json.Encode(@ViewBag.Vento_Direcao));
        var Vento_Velocidade = @Html.Raw(Json.Encode(@ViewBag.Vento_Velocidade));
        var Tempo_Cod = @Html.Raw(Json.Encode(@ViewBag.Tempo_Cod));
        var Dias = @Html.Raw(Json.Encode(@ViewBag.Dias));
        var NomeGrandeza = @Html.Raw(Json.Encode(@ViewBag.NomeGrandeza));
        var UnidadeGrandeza = @Html.Raw(Json.Encode(@ViewBag.UnidadeGrandeza));

        var minimoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMin));
        var maximoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMax));

        var diferenca = maximoValor - minimoValor;

        dataX.push('x');
        dataTemperatura.push(['Temperatura']);

        for (i = 0; i < 26; i++)
        {
            // X
            dataX.push(Dias[i]);

            dataTemperatura.push([Temperatura[i]]);
        }

        // valores label X
        labelX.push(Dias[1]);
        for (i = 3; i <= 24; i+=3)
        {
            labelX.push(Dias[i]);
        }

        var Data = [dataX,dataTemperatura];

        c3.generate({
            bindto: '#grafico-ea',
            size: {
                height: 300
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    Temperatura: 'bar',
                },
                colors: {
                    Temperatura: 'rgb(120, 178, 235)',
                }
            },
            point: {
                r:2,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*28
                },
                y:  {
                    max: maximoValor,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( diferenca < 10)
                            {
                                return d.toFixed(2);
                            }

                            if( diferenca < 100)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.25 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, unit, icone_tempo, bgcolor;
                    for (i = 0; i < 5; i++) {

                        if (! text) {

                            // split string and create array.
                            var arr = Dias[d[0].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // horas e minutos
                            var hours = data.getHours();
                            var minutes = data.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            title = hours + ':' + minutes;

                            text = "<table class='" + $$.CLASS.tooltip + "' style='color:#000000'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1C84C6'>" + title + "</th></tr>" : "");
                        }

                        // nome
                        switch(i)
                        {
                            case 0:
                                name = 'Temperatura';
                                unit = '°C';
                                value = Temperatura[d[0].index].toFixed(0);
                                break;

                            case 1:
                                name = 'Umidade';
                                unit = '%';
                                value = Umidade[d[0].index].toFixed(0);
                                break;

                            case 2:
                                name = 'Pressão';
                                unit = 'hPa';
                                value = Pressao[d[0].index].toFixed(0);
                                break;

                            case 3:
                                name = 'Vento';
                                unit = 'km/h';
                                value = Vento_Velocidade[d[0].index].toFixed(0);

                                break;

                            case 4:
                                name = 'Clima';
                                unit = '';

                                switch (Tempo_Cod[d[0].index])
                                {
                                    case 0:     // 0 = clear-day
                                        value = 'Céu Claro';
                                        icone_tempo = 'icone_dia_claro16.png';
                                        break;

                                    case 1:     // 1 = clear-night
                                        value = 'Noite Clara';
                                        icone_tempo = 'icone_noite_clara16.png';
                                        break;

                                    case 2:     // 2 = partly-cloudy-day
                                        value = 'Parcialmente Nublado';
                                        icone_tempo = 'icone_dia_nublado16.png';
                                        break;

                                    case 3:     // 3 = partly-cloudy-night
                                        value = 'Parcialmente Nublado';
                                        icone_tempo = 'icone_noite_nublada16.png';
                                        break;

                                    case 4:     // 4 = cloudy
                                        value = 'Nublado';
                                        icone_tempo = 'icone_nublado16.png';
                                        break;

                                    case 5:     // 5 = rain
                                        value = 'Chuvoso';
                                        icone_tempo = 'icone_chuva16.png';
                                        break;

                                    case 6:     // 6 = sleet (granizo)
                                        value = 'Granizo';
                                        icone_tempo = 'icone_granizo16.png';
                                        break;

                                    case 7:     // 7 = snow
                                        value = 'Neve';
                                        icone_tempo = 'icone_neve16.png';
                                        break;

                                    case 8:     // 8 = wind
                                        value = 'Vento';
                                        icone_tempo = 'icone_vento16.png';
                                        break;

                                    case 9:     // 9 = fog
                                        value = 'Nevoeiro';
                                        icone_tempo = 'icone_neblina16.png';
                                        break;

                                    default:
                                        value = '---';
                                        icone_tempo = '';
                                        break;
                                }

                                break;
                        }

                        // verifica se valor inexistente
                        if (Temperatura[d[0].index] == 0)
                        {
                            value = '---';
                            unit = '';
                            icone_tempo = '';
                        }

                        // cor
                        bgcolor = $$.levelColor ? $$.levelColor(d[0].value) : color(d[0].id);

                        if (i==0)
                        {
                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                            text += "<td class='value'>" + value + " " + unit + "</td>";
                            text += "</tr>";
                        }
                        else if (i==3)
                        {
                            var nome_Direcao = ["Norte", "Norte - Nordeste", "Nordeste", "Este - Nordeste", "Leste", "Este - Sudeste", "Sudeste", "Sul - Sudeste", "Sul", "Sul - Sudoeste", "Sudoeste", "Oeste - Sudoeste", "Oeste", "Oeste - Noroeste", "Noroeste", "Norte - Noroeste"];

                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                            text += "<td class='name'>" + name + "</td>";

                            if (value == '---')
                            {
                                text += "<td class='value'>" + value + "</td>";
                            }
                            else
                            {
                                text += "<td class='value'>" + value + " " + unit + "<br>[" + nome_Direcao[Vento_Direcao[d[0].index]] + "]</td>";
                            }
                            text += "</tr>";
                        }
                        else if (i==4)
                        {
                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                            text += "<td class='name'>" + name + "</td>";

                            if (value == '---')
                            {
                                text += "<td class='value'>" + value + "</td>";
                            }
                            else
                            {
                                text += "<td class='value'>" + value + "<br><img src='/Imagens/Tempo/" + icone_tempo + "' /></td>";


                            }
                            text += "</tr>";
                        }
                        else
                        {
                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                            text += "<td class='name'>" + name + "</td>";
                            text += "<td class='value'>" + value + " " + unit + "</td>";
                            text += "</tr>";
                        }
                    }
                    return text + "</table>";
                }
            }
        });

        d3.selection.prototype.moveToFront = function () {
            return this.each(function () {
                this.parentNode.appendChild(this);
            });
        };

        d3.select("svg g.c3-grid").moveToFront();


        // meses

        // copia valores
        var Mes_Atual_Data = @Html.Raw(Json.Encode(ViewBag.Mes_Atual_DataMes));
        var Mes_Atual_Max = @Html.Raw(Json.Encode(ViewBag.Mes_Atual_MaxN));
        var Mes_Atual_Media = @Html.Raw(Json.Encode(ViewBag.Mes_Atual_MediaN));
        var Mes_Atual_Min = @Html.Raw(Json.Encode(ViewBag.Mes_Atual_MinN));

        var Mes_Anterior1_Data = @Html.Raw(Json.Encode(ViewBag.Mes_Anterior1_DataMes));
        var Mes_Anterior1_Max = @Html.Raw(Json.Encode(ViewBag.Mes_Anterior1_MaxN));
        var Mes_Anterior1_Media = @Html.Raw(Json.Encode(ViewBag.Mes_Anterior1_MediaN));
        var Mes_Anterior1_Min = @Html.Raw(Json.Encode(ViewBag.Mes_Anterior1_MinN));

        var Mes_Anterior2_Data = @Html.Raw(Json.Encode(ViewBag.Mes_Anterior2_DataMes));
        var Mes_Anterior2_Max = @Html.Raw(Json.Encode(ViewBag.Mes_Anterior2_MaxN));
        var Mes_Anterior2_Media = @Html.Raw(Json.Encode(ViewBag.Mes_Anterior2_MediaN));
        var Mes_Anterior2_Min = @Html.Raw(Json.Encode(ViewBag.Mes_Anterior2_MinN));


        var x = ['x', Mes_Anterior2_Data, Mes_Anterior1_Data, Mes_Atual_Data];
        var Min = ['Min', Mes_Anterior2_Min, Mes_Anterior1_Min, Mes_Atual_Min];
        var Med = ['Med', Mes_Anterior2_Media, Mes_Anterior1_Media, Mes_Atual_Media];
        var Max = ['Max', Mes_Anterior2_Max, Mes_Anterior1_Max, Mes_Atual_Max];

        // verifica maximo
        var maxValor = Mes_Atual_Max;

        if( Mes_Anterior1_Max > maxValor)
        {
            maxValor = Mes_Anterior1_Max;
        }

        if( Mes_Anterior2_Max > maxValor)
        {
            maxValor = Mes_Anterior2_Max;
        }

        maxValor = maxValor * 1.1;

        if (maxValor < 0.0)
        {
            maxValor = 0.0;
        }

        c3.generate({
            bindto: '#ea-chart',
            size: {
                height: 230
            },
            padding: {
                top: 8,
                right: 0,
                bottom: 20,
                left: 60
            },
            data: {
                x: 'x',
                columns: [
                    x,
                    Min, Med, Max
                ],
                type: 'bar'
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis:{
                rotated: false,
                x : {
                    type : 'categories',
                    tick: {
                        fit: true
                    }
                },
                y : {
                    show: true,
                    max: maxValor
                }
            },
            color: {
                pattern: ['#D7E8F8', '#4284B2', '#00007f']
            },
            grid: {
                y: {
                    lines: [{value:0}]
                }
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;
                    for (i = d.length-1; i >= 0; i--) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        if (! text) {
                            title = titleFormat ? titleFormat(d[i].x) : d[i].x;
                            text = "<table class='" + $$.CLASS.tooltip + "' style='color:#000000'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1C84C6'>" + title + "</th></tr>" : "");
                        }

                        switch(i)
                        {
                            case 0:     // minimo
                                name = "Mínima";
                                value = d[i].value.toFixed(0);
                                break;

                            case 1:     // medio
                                name = "Média";
                                value = d[i].value.toFixed(0);
                                break;

                            case 2:     // maximo
                                name = "Máxima";
                                var soma = d[i].value + d[0].value;
                                value = soma.toFixed(0);
                                break;
                        }

                        value = d[i].value.toFixed(0);
                        bgcolor = $$.levelColor ? $$.levelColor(d[i].value) : color(d[i].id);

                        text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                        text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                        text += "<td class='value'>" + value + " " + UnidadeGrandeza + "</td>";
                        text += "</tr>";
                    }
                    return text + "</table>";
                }
            }

        });


        // copia valores
        var Tempo_Cod_Icone = @Html.Raw(Json.Encode(@ViewBag.Dia_Atual_Tempo_Cod));

        // icones tempo
        var skycons = new Skycons({"color": "#23C6C8"});
        var tempo = Skycons.CLEAR_DAY;
        var tempo_texto = "Indefinido";

        switch (Tempo_Cod_Icone)
        {
            case 0:     // 0 = clear-day
                tempo_texto = 'Céu Claro';
                tempo = Skycons.CLEAR_DAY;
                break;

            case 1:     // 1 = clear-night
                tempo_texto = 'Noite Clara';
                tempo = Skycons.CLEAR_NIGHT;
                break;

            case 2:     // 2 = partly-cloudy-day
                tempo_texto = 'Parcialmente Nublado';
                tempo = Skycons.PARTLY_CLOUDY_DAY;
                break;

            case 3:     // 3 = partly-cloudy-night
                tempo_texto = 'Parcialmente Nublado';
                tempo = Skycons.PARTLY_CLOUDY_NIGHT;
                break;

            case 4:     // 4 = cloudy
                tempo_texto = 'Nublado';
                tempo = Skycons.CLOUDY;
                break;

            case 5:     // 5 = rain
                tempo_texto = 'Chuvoso';
                tempo = Skycons.RAIN;
                break;

            case 6:     // 6 = sleet (granizo)
                tempo_texto = 'Granizo';
                tempo = Skycons.SLEET;
                break;

            case 7:     // 7 = snow
                tempo_texto = 'Neve';
                tempo = Skycons.SNOW;
                break;

            case 8:     // 8 = wind
                tempo_texto = 'Vento';
                tempo = Skycons.WIND;
                break;

            case 9:     // 9 = fog
                tempo_texto = 'Nevoeiro';
                tempo = Skycons.FOG;
                break;
        }

        $("h4.tempo").text(tempo_texto);
        skycons.add("icon_hoje", tempo);

        // previsao
        Tempo_Cod_Icone = @Html.Raw(Json.Encode(@ViewBag.Tempo_Cod_0));
        skycons.add("icon_previsao_0", Tempo_Cod_DarkSky(Tempo_Cod_Icone));

        Tempo_Cod_Icone = @Html.Raw(Json.Encode(@ViewBag.Tempo_Cod_1));
        skycons.add("icon_previsao_1", Tempo_Cod_DarkSky(Tempo_Cod_Icone));

        Tempo_Cod_Icone = @Html.Raw(Json.Encode(@ViewBag.Tempo_Cod_2));
        skycons.add("icon_previsao_2", Tempo_Cod_DarkSky(Tempo_Cod_Icone));

        Tempo_Cod_Icone = @Html.Raw(Json.Encode(@ViewBag.Tempo_Cod_3));
        skycons.add("icon_previsao_3", Tempo_Cod_DarkSky(Tempo_Cod_Icone));

        // inicia animacao
        skycons.play();

        piscando();
    });


    function Tempo_Cod_DarkSky(Tempo_Cod_Icone) {

        var tempo = Skycons.CLEAR_DAY;

        switch (Tempo_Cod_Icone)
        {
            case 0:     // 0 = clear-day
                tempo = Skycons.CLEAR_DAY;
                break;

            case 1:     // 1 = clear-night
                tempo = Skycons.CLEAR_NIGHT;
                break;

            case 2:     // 2 = partly-cloudy-day
                tempo = Skycons.PARTLY_CLOUDY_DAY;
                break;

            case 3:     // 3 = partly-cloudy-night
                tempo = Skycons.PARTLY_CLOUDY_NIGHT;
                break;

            case 4:     // 4 = cloudy
                tempo = Skycons.CLOUDY;
                break;

            case 5:     // 5 = rain
                tempo = Skycons.RAIN;
                break;

            case 6:     // 6 = sleet (granizo)
                tempo = Skycons.SLEET;
                break;

            case 7:     // 7 = snow
                tempo = Skycons.SNOW;
                break;

            case 8:     // 8 = wind
                tempo = Skycons.WIND;
                break;

            case 9:     // 9 = fog
                tempo = Skycons.FOG;
                break;
        }

        return (tempo);
    }


    function piscando() {

        var tempo = 500; //1000 = 1s

        blinks = document.getElementsByTagName("blink");
        for(var i=0;i<blinks.length;i++){
            if(blinks[i].getAttribute("style")=="VISIBILITY: hidden"){
                blinks[i].setAttribute("style", "VISIBILITY: visible");
            }else{
                blinks[i].setAttribute("style", "VISIBILITY: hidden");
            }
        }
        setTimeout('piscando()', tempo);
    }


</script>
}





