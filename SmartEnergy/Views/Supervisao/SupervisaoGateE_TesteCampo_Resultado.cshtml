﻿@model SmartEnergy.Controllers.GateE_TesteCampo_Resultado

@using System.Globalization
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = "Teste de Campo";
}

<style>

    .relat-tools i:hover {
        cursor: pointer;
    }

</style>

<div class="wrapper wrapper-content">

    <input id="IDGateway" value="@Model.IDGateway_E" hidden />

    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">
                    <div class="panel panel-title relatorio">
                        <div class="panel-heading">
                            <div style="text-align:center;">
                                <h4>Análise do Período</h4>
                            </div>

                            <div class="pull-left relat-navega">
                                <h4>
                                    <a class="link_menos">
                                        <i class="fa fa-angle-left" data-toggle="tooltip" data-placement="left" title="Período Anterior"></i>
                                    </a>
                                    <a class="link_mais">
                                        <i class="fa fa-angle-right" data-toggle="tooltip" data-placement="left" title="Período Seguinte"></i>
                                    </a>
                                </h4>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <label class="control-label">&nbsp;Gateway</label>
                                        @Html.DropDownListFor(model => model.IDGateway_E, new SelectList(ViewBag.listaGateE, "ID", "Descricao"),
                                            string.Format("{0}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione),
                                            new { @class = "form-control", @onchange = "AlterouGateway()" })
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.SupervisaoTexts.Inicio</label>
                                        <div class="input-group" id="startDate">
                                            <input type='text' class="form-control dateini" name="data_relat_ini" value=@ViewBag.DataIni>
                                            <span class="input-group-addon">
                                                <i class="fa fa-calendar"></i>&nbsp;&nbsp;<i class="fa fa-clock-o"></i>
                                            </span>
                                            <div class="input-group" id="startTime">
                                                <input type="text" class="form-control timeini" name="hora_relat_ini" value=@ViewBag.HoraIni>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.SupervisaoTexts.Fim</label>
                                    <div class="form-group">
                                        <div class="input-group" id="endDate">
                                            <input type='text' class="form-control datefim" name="data_relat_fim" value=@ViewBag.DataFim>
                                            <span class="input-group-addon">
                                                <i class="fa fa-calendar"></i>&nbsp;&nbsp;<i class="fa fa-clock-o"></i>
                                            </span>
                                            <div class="input-group" id="endTime">
                                                <input type='text' class="form-control timefim" name="hora_relat_fim" value=@ViewBag.HoraFim>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="ibox-tools">
                                        <button type="button" class="btn btn-primary" onclick="ExecutarAnalise(100);" id="BotaoExecutar">@SmartEnergy.Resources.ComumTexts.BotaoExecutar</button>
                                        <a href='@("/Supervisao/SupervisaoGateE_TesteCampo")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                    </div>
                                </div>
                            </div>

                            <br />
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="panel panel-success">
                                        <div class="panel-heading">
                                            <div class="row">
                                                <div class="col-lg-8">
                                                    <h4>Gateway SmartGate E</h4><br />
                                                    @Model.NomeGateway_E
                                                    <span> [@Model.IDGateway_E]</span>
                                                </div>
                                                <div class="col-lg-4">
                                                    <div class="pull-right relat-tools grafico_E" style="margin-top:0px;">
                                                        <i class="fa fa-3x fa-bar-chart-o" data-toggle="tooltip" data-placement="left" title="Gráfico"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="panel-body">
                                            <div class="row">
                                                <div class="col-lg-12">
                                                    <h4>Registros</h4>
                                                    <span>@Model.numRegistros_E registros</span>
                                                </div>
                                            </div>

                                            <br />
                                            <br />
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <h4>Consumo Ativo [Ponta]</h4>
                                                    <span>@string.Format("{0:#,##0.000} kWh", Model.consumoAtv_P_E)</span>
                                                </div>
                                                <div class="col-lg-6">
                                                    <h4>Consumo Reativo [Ponta]</h4>
                                                    <span>@string.Format("{0:#,##0.000} kWh", Model.consumoRtv_P_E)</span>
                                                </div>
                                            </div>

                                            <br />
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <h4>Consumo Ativo [FPonta Ind]</h4>
                                                    <span>@string.Format("{0:#,##0.000} kWh", Model.consumoAtv_FPI_E)</span>
                                                </div>
                                                <div class="col-lg-6">
                                                    <h4>Consumo Reativo [FPonta Ind]</h4>
                                                    <span>@string.Format("{0:#,##0.000} kWh", Model.consumoRtv_FPI_E)</span>
                                                </div>
                                            </div>

                                            <br />
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <h4>Consumo Ativo [FPonta Cap]</h4>
                                                    <span>@string.Format("{0:#,##0.000} kWh", Model.consumoAtv_FPC_E)</span>
                                                </div>
                                                <div class="col-lg-6">
                                                    <h4>Consumo Reativo [FPonta Cap]</h4>
                                                    <span>@string.Format("{0:#,##0.000} kWh", Model.consumoRtv_FPC_E)</span>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="panel panel-success">
                                        <div class="panel-heading">
                                            <div class="row">
                                                <div class="col-lg-8">
                                                    <h4>Gateway SmartGate X</h4><br />
                                                    @Model.NomeGateway_X
                                                    <span> [@Model.IDGateway_X]</span>
                                                </div>
                                                <div class="col-lg-4">
                                                    <div class="pull-right relat-tools grafico_X" style="margin-top:0px;">
                                                        <i class="fa fa-3x fa-bar-chart-o" data-toggle="tooltip" data-placement="left" title="Gráfico"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="panel-body">
                                            <div class="row">
                                                <div class="col-lg-12">
                                                    <h4>Registros</h4>
                                                    <span>@Model.numRegistros_X registros</span>
                                                </div>
                                            </div>

                                            <br />
                                            <br />
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <h4>Consumo Ativo [Ponta]</h4>
                                                    <span>@string.Format("{0:#,##0.000} kWh", Model.consumoAtv_P_X)</span>
                                                </div>
                                                <div class="col-lg-6">
                                                    <h4>Consumo Reativo [Ponta]</h4>
                                                    <span>@string.Format("{0:#,##0.000} kWh", Model.consumoRtv_P_X)</span>
                                                </div>
                                            </div>

                                            <br />
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <h4>Consumo Ativo [FPonta Ind]</h4>
                                                    <span>@string.Format("{0:#,##0.000} kWh", Model.consumoAtv_FPI_X)</span>
                                                </div>
                                                <div class="col-lg-6">
                                                    <h4>Consumo Reativo [FPonta Ind]</h4>
                                                    <span>@string.Format("{0:#,##0.000} kWh", Model.consumoRtv_FPI_X)</span>
                                                </div>
                                            </div>

                                            <br />
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <h4>Consumo Ativo [FPonta Cap]</h4>
                                                    <span>@string.Format("{0:#,##0.000} kWh", Model.consumoAtv_FPC_X)</span>
                                                </div>
                                                <div class="col-lg-6">
                                                    <h4>Consumo Reativo [FPonta Cap]</h4>
                                                    <span>@string.Format("{0:#,##0.000} kWh", Model.consumoRtv_FPC_X)</span>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>

                            <br />
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="panel panel-title">
                                        <div class="panel-body">
                                            <div class="superv-grafico">
                                                <div class="flot-chart">
                                                    <div id="demanda_E-chart"></div>
                                                </div>
                                                <div class='chart-legend'>
                                                    <div class='legend-scale-dem'>
                                                        <ul class='legend-labels'>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="panel panel-title">
                                        <div class="panel-body">
                                            <div class="superv-grafico">
                                                <div class="flot-chart">
                                                    <div id="demanda_X-chart"></div>
                                                </div>
                                                <div class='chart-legend'>
                                                    <div class='legend-scale-dem'>
                                                        <ul class='legend-labels'>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <br />
                            <br />
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="panel panel-title">
                                        <div class="panel-body">
                                            <table class="table table-striped table-bordered table-hover dataTables-anormalidades">
                                                <thead>
                                                    <tr>
                                                        <th>Data Hora</th>
                                                        <th>Anormalidades</th>
                                                        <th>SmartGate E</th>
                                                        <th>SmartGate X</th>
                                                    </tr>
                                                </thead>
                                                <tbody>

                                                    @foreach (var anormalidade in Model.anormalidades)
                                                    {
                                                        <tr>
                                                            <td>@string.Format("{0:g}", anormalidade.DataHora)</td>
                                                            <td>@anormalidade.descricao</td>
                                                            <td>@anormalidade.gateE</td>
                                                            <td>@anormalidade.gateX</td>
                                                        </tr>
                                                    }

                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <br />
                            <br />
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="panel panel-title" style="min-height: 564px;">
                                        <div class="panel-body">

                                            <select class="select2_tipo form-control" id="TiposEvento_E" onchange="SelecionouTipo_E()">

                                                @{
                                                    var icone_tipo = "fa-th-list";
                                                    int tipo = 0;

                                                    for (var i = 0; i < Model.NumTiposEventos_E; i++)
                                                    {
                                                        tipo = Model.listaTiposEventos_E[i].Tipo;

                                                        switch (tipo)
                                                        {
                                                            case 0:
                                                                icone_tipo = "fa-th-list";
                                                                break;

                                                            case 1:
                                                                icone_tipo = "fa-bell";
                                                                break;

                                                            case 2:
                                                                icone_tipo = "fa-gear";
                                                                break;

                                                            case 3:
                                                                icone_tipo = "fa-power-off";
                                                                break;

                                                            case 4:
                                                                icone_tipo = "fa-retweet";
                                                                break;

                                                            case 5:
                                                                icone_tipo = "fa-sitemap";
                                                                break;

                                                            case 6:
                                                                icone_tipo = "fa-sign-in";
                                                                break;

                                                            case 7:
                                                                icone_tipo = "fa-sign-out";
                                                                break;

                                                            case 8:
                                                                icone_tipo = "fa-upload";
                                                                break;

                                                            case 9:
                                                                icone_tipo = "fa-clock-o";
                                                                break;

                                                            case 10:
                                                                icone_tipo = "fa-user";
                                                                break;
                                                        }

                                                        <option value=@tipo data-icon=@icone_tipo>&nbsp;&nbsp;@Model.listaTiposEventos_E[i].Descricao</option>
                                                    }
                                                }

                                            </select>

                                            <br /><br />

                                            <table class="table table-striped table-bordered table-hover dataTables-eventosE">
                                                <thead>
                                                    <tr>
                                                        <th>@SmartEnergy.Resources.RelatoriosTexts.Data</th>
                                                        <th>Tipo</th>
                                                        <th></th>
                                                        <th>@SmartEnergy.Resources.RelatoriosTexts.Evento</th>
                                                    </tr>
                                                </thead>
                                                <tbody>

                                                    @foreach (var evento in Model.listaEventosDescricao_E)
                                                    {
                                                        icone_tipo = "fa-th-list";
                                                        tipo = evento.Tipo;

                                                        switch (tipo)
                                                        {
                                                            case 0:
                                                                icone_tipo = "fa-th-list";
                                                                break;

                                                            case 1:
                                                                icone_tipo = "fa-bell";
                                                                break;

                                                            case 2:
                                                                icone_tipo = "fa-gear";
                                                                break;

                                                            case 3:
                                                                icone_tipo = "fa-power-off";
                                                                break;

                                                            case 4:
                                                                icone_tipo = "fa-retweet";
                                                                break;

                                                            case 5:
                                                                icone_tipo = "fa-sitemap";
                                                                break;

                                                            case 6:
                                                                icone_tipo = "fa-sign-in";
                                                                break;

                                                            case 7:
                                                                icone_tipo = "fa-sign-out";
                                                                break;

                                                            case 8:
                                                                icone_tipo = "fa-upload";
                                                                break;

                                                            case 9:
                                                                icone_tipo = "fa-clock-o";
                                                                break;

                                                            case 10:
                                                                icone_tipo = "fa-user";
                                                                break;
                                                        }

                                                        <tr>
                                                            <td style="font-size:11px;"><span style="display:none;">@evento.DataHora_Sort</span>@evento.DataHora</td>
                                                            <td>@evento.Tipo</td>
                                                            <td style="text-align:center;"><i class="fa @icone_tipo" style="font-size:20px;"></i></td>
                                                            <td style="font-size:11px;">@evento.Descricao</td>
                                                        </tr>
                                                    }

                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-lg-6">
                                    <div class="panel panel-title" style="min-height: 564px;">
                                        <div class="panel-body">

                                            <select class="select2_tipo form-control" id="TiposEvento_X" onchange="SelecionouTipo_X()">

                                                @{
                                                    icone_tipo = "fa-th-list";
                                                    tipo = 0;

                                                    for (var i = 0; i < Model.NumTiposEventos_X; i++)
                                                    {
                                                        tipo = Model.listaTiposEventos_X[i].Tipo;

                                                        switch (tipo)
                                                        {
                                                            case 0:
                                                                icone_tipo = "fa-th-list";
                                                                break;

                                                            case 1:
                                                                icone_tipo = "fa-bell";
                                                                break;

                                                            case 2:
                                                                icone_tipo = "fa-gear";
                                                                break;

                                                            case 3:
                                                                icone_tipo = "fa-power-off";
                                                                break;

                                                            case 4:
                                                                icone_tipo = "fa-retweet";
                                                                break;

                                                            case 5:
                                                                icone_tipo = "fa-sitemap";
                                                                break;

                                                            case 6:
                                                                icone_tipo = "fa-sign-in";
                                                                break;

                                                            case 7:
                                                                icone_tipo = "fa-sign-out";
                                                                break;

                                                            case 8:
                                                                icone_tipo = "fa-upload";
                                                                break;

                                                            case 9:
                                                                icone_tipo = "fa-clock-o";
                                                                break;

                                                            case 10:
                                                                icone_tipo = "fa-user";
                                                                break;
                                                        }

                                                        <option value=@tipo data-icon=@icone_tipo>&nbsp;&nbsp;@Model.listaTiposEventos_X[i].Descricao</option>
                                                    }
                                                }

                                            </select>

                                            <br /><br />

                                            <table class="table table-striped table-bordered table-hover dataTables-eventosX">
                                                <thead>
                                                    <tr>
                                                        <th>@SmartEnergy.Resources.RelatoriosTexts.Data</th>
                                                        <th>Tipo</th>
                                                        <th></th>
                                                        <th>@SmartEnergy.Resources.RelatoriosTexts.Evento</th>
                                                    </tr>
                                                </thead>
                                                <tbody>

                                                    @foreach (var evento in Model.listaEventosDescricao_X)
                                                    {
                                                        icone_tipo = "fa-th-list";
                                                        tipo = evento.Tipo;

                                                        switch (tipo)
                                                        {
                                                            case 0:
                                                                icone_tipo = "fa-th-list";
                                                                break;

                                                            case 1:
                                                                icone_tipo = "fa-bell";
                                                                break;

                                                            case 2:
                                                                icone_tipo = "fa-gear";
                                                                break;

                                                            case 3:
                                                                icone_tipo = "fa-power-off";
                                                                break;

                                                            case 4:
                                                                icone_tipo = "fa-retweet";
                                                                break;

                                                            case 5:
                                                                icone_tipo = "fa-sitemap";
                                                                break;

                                                            case 6:
                                                                icone_tipo = "fa-sign-in";
                                                                break;

                                                            case 7:
                                                                icone_tipo = "fa-sign-out";
                                                                break;

                                                            case 8:
                                                                icone_tipo = "fa-upload";
                                                                break;

                                                            case 9:
                                                                icone_tipo = "fa-clock-o";
                                                                break;

                                                            case 10:
                                                                icone_tipo = "fa-user";
                                                                break;
                                                        }

                                                        <tr>
                                                            <td style="font-size:11px;"><span style="display:none;">@evento.DataHora_Sort</span>@evento.DataHora</td>
                                                            <td>@evento.Tipo</td>
                                                            <td style="text-align:center;"><i class="fa @icone_tipo" style="font-size:20px;"></i></td>
                                                            <td style="font-size:11px;">@evento.Descricao</td>
                                                        </tr>
                                                    }

                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
    @Styles.Render("~/datetimePickerStyles")
    @Styles.Render("~/plugins/c3Styles")
    @Styles.Render("~/plugins/select2Styles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/plugins/d3")
    @Scripts.Render("~/plugins/c3")
    @Scripts.Render("~/plugins/select2")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


<script type="text/javascript">

        $('#startDate .dateini').datetimepicker({
            locale: 'pt-BR',
            format: 'DD/MM/YYYY',
            showTodayButton: true,
            allowInputToggle: true
        });

        $('#startTime').datetimepicker({
            locale: 'pt-BR',
            format: 'HH:mm',
            allowInputToggle: true
        });

        $('#endDate .datefim').datetimepicker({
            locale: 'pt-BR',
            format: 'DD/MM/YYYY',
            showTodayButton: true,
            allowInputToggle: true
        });

        $('#endTime').datetimepicker({
            locale: 'pt-BR',
            format: 'HH:mm',
            allowInputToggle: true
        });

        $(document).ready(function () {

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "portugues-pre": function (data) {
                    var a = 'a';
                    var e = 'e';
                    var i = 'i';
                    var o = 'o';
                    var u = 'u';
                    var c = 'c';
                    var special_letters = {
                        "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                        "É": e, "é": e, "Ê": e, "ê": e,
                        "Í": i, "í": i, "Î": i, "î": i,
                        "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                        "Ú": u, "ú": u, "Ü": u, "ü": u,
                        "ç": c, "Ç": c
                    };
                    for (var val in special_letters)
                        data = data.split(val).join(special_letters[val]).toLowerCase();
                    return data;
                },
                "portugues-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },
                "portugues-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "numero-pre": function (a) {

                    var x = a;

                    if (a == "-") {
                        x = "-1";
                    }
                    else {
                        x = a.replace(".", "");
                        x = x.replace(",", ".");
                    }

                    return parseFloat(x);
                },

                "numero-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },

                "numero-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "datetime-br-pre": function (a) {
                    var dateParts = a.split(' ');
                    var date = dateParts[0].split('/');
                    var time = dateParts[1].split(':');
                    return new Date(date[2], date[1] - 1, date[0], time[0], time[1]).getTime();
                },

                "datetime-br-asc": function (a, b) {
                    return a - b;
                },

                "datetime-br-desc": function (a, b) {
                    return b - a;
                }
            });

            // eventos
            $('.dataTables-eventosE').DataTable({
                "iDisplayLength": 9,
                dom: 'ftp',

                "aoColumnDefs": [
                            {
                                "aTargets": [1],
                                "bVisible": false,
                                "bSortable": true
                            },
                            {
                                "aTargets": [2],
                                "bVisible": true,
                                "bSortable": false
                            },
                ],
                "oSearch": {
                    "bSmart": false,
                    "bRegex": true,
                    "sSearch": ""
                },
                "bAutoWidth": false,
                "aoColumns": [
                    { sWidth: "25%" },
                    { sWidth: "5%" },
                    { sWidth: "5%" },
                    { sWidth: "80%" }
                ],
                'order': [0, 'desc'],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }

            });

            // eventos
            $('.dataTables-eventosX').DataTable({
                "iDisplayLength": 9,
                dom: 'ftp',

                "aoColumnDefs": [
                            {
                                "aTargets": [1],
                                "bVisible": false,
                                "bSortable": true
                            },
                            {
                                "aTargets": [2],
                                "bVisible": true,
                                "bSortable": false
                            },
                ],
                "oSearch": {
                    "bSmart": false,
                    "bRegex": true,
                    "sSearch": ""
                },
                "bAutoWidth": false,
                "aoColumns": [
                    { sWidth: "25%" },
                    { sWidth: "5%" },
                    { sWidth: "5%" },
                    { sWidth: "80%" }
                ],
                'order': [0, 'desc'],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }

            });


            function formatIcon(icon) {

                if (!icon.id) {
                    return icon.text;
                }
                var originalOption = icon.element;
                var $icon = $('<i style="font-size:16px;" class="fa ' + $(originalOption).data('icon') + '"></i><span>' + icon.text + '</span>');

                return $icon;

            };

            $(".select2_tipo").select2({
                minimumResultsForSearch: Infinity,
                width: "100%",
                templateResult: formatIcon,
                templateSelection: formatIcon
            });


            // anormalidades
            $('.dataTables-anormalidades').DataTable({
                "iDisplayLength": 20,
                dom: 'Bftp',
                buttons: [
                    {
                        extend: 'excelHtml5',
                        filename: 'Anormalidades',
                        title: 'Anormalidades',
                        text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-excel-o" data-toggle="tooltip" data-placement="left" title="Excel"></span>',
                        className: 'btn btn-default btn-xs'
                    },
                    {
                        extend: 'pdfHtml5',
                        filename: 'Anormalidades',
                        title: 'Anormalidades',
                        text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-pdf-o" data-toggle="tooltip" data-placement="left" title="PDF"></span>',
                        className: 'btn btn-default btn-xs'
                    },
                ],

                "aoColumnDefs": [
                    { "aTargets": [0], "sType": "datetime-br" },
                    { "aTargets": [1], "sType": "portugues" },
                    { "aTargets": [2], "sType": "numero" },
                    { "aTargets": [3], "sType": "numero" },
                ],
                "oSearch": {
                    "bSmart": false,
                    "bRegex": true,
                    "sSearch": ""
                },
                "bAutoWidth": false,
                "aoColumns": [
                    { sWidth: "20%" },
                    { sWidth: "40%" },
                    { sWidth: "20%" },
                    { sWidth: "20%" }
                ],
                'order': [0, 'desc'],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }

            });


            //
            // DEMANDA gateE
            //

            var maximoDem_E = @Html.Raw(Json.Encode(@ViewBag.DemMax_E));
            var maximoDem_X = @Html.Raw(Json.Encode(@ViewBag.DemMax_X));

            var maximoDem = maximoDem_E;

            if (maximoDem_X > maximoDem_E) {
                maximoDem = maximoDem_X;
            }

            var data_E = [];
            var label_E = [];
            var dataDemanda_E = [];

            // copia valores
            var DemandaAtv_E = @Html.Raw(Json.Encode(@ViewBag.DemandaAtv_E));
            var DemandaRtv_E = @Html.Raw(Json.Encode(@ViewBag.DemandaRtv_E));
            var Periodo_E = @Html.Raw(Json.Encode(@ViewBag.Periodo_E));
            var Dias_Demanda_E = @Html.Raw(Json.Encode(@ViewBag.Dias_Demanda_E));

            var numRegistros_E = @Html.Raw(Json.Encode(@ViewBag.numRegistros_E));

            data_E.push('x');
            dataDemanda_E.push(['data1']);

            for (i = 0; i < numRegistros_E; i++)
            {
                // X
                data_E.push(Dias_Demanda_E[i]);

                // demanda
                dataDemanda_E.push([DemandaAtv_E[i]]);
            }

            // valores label X
            var multiplo = numRegistros_E / 8;
            var i = 1;

            label_E.push(Dias_Demanda_E[1]);
            for (i = 1; i < numRegistros_E; i += multiplo) {

                var indice = Math.round(i);
                label_E.push(Dias_Demanda_E[indice]);
            }

            var Data_E = [data_E, dataDemanda_E];

            c3.generate({

                bindto: '#demanda_E-chart',
                size: {
                    height: 300
                },
                padding: {
                    top: 8,
                    right: 10,
                    bottom: 30,
                    left: 43
                },
                data: {
                    x: 'x',
                    xFormat: '%Y-%m-%d %H:%M:%S',
                    columns: Data_E,
                    types: {
                        data1: 'area-step',
                    },
                    color: function (color, d) {
                        if (typeof d.index === 'undefined') { return color; }

                        if (Periodo_E[d.index] == 0)
                            return '#FF0000';

                        if (Periodo_E[d.index] == 1)
                            return '#007F00';

                        if (Periodo_E[d.index] == 2)
                            return '#1C84C6';

                        return '#007F00';
                    },
                    colors: {
                        data1: '#001F00',
                    },
                },
                point: {
                    show: false,
                    sensitivity: 1000
                },
                legend: {
                    show: false
                },
                axis: {
                    x: {
                        type: 'timeseries',
                        tick: {
                            outer: false,
                            values: label_E,
                            format: function (x) {

                                // horas e minutos
                                var hours = x.getHours();
                                var minutes = x.getMinutes();

                                hours = hours < 10 ? '0' + hours : hours;
                                minutes = minutes < 10 ? '0' + minutes : minutes;
                                var strTime = hours + ':' + minutes;

                                // retorna hora e minuto
                                return strTime;
                            }
                        },
                        // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                        // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                        padding: -1000 * 60 * 6
                    },
                    y: {
                        min: 0,
                        max: maximoDem,
                        padding: {
                            top: 0,
                            bottom: 0
                        },
                        tick: {
                            outer: false,
                            format: function (d) {

                                if (maximoDem < 100) {
                                    return d.toFixed(1);
                                }

                                return parseInt(d);
                            }
                        }
                    }
                },
                grid: {
                    x: {
                        show: false
                    }
                },
                bar: {
                    width: { ratio: 0.15 },
                },
                tooltip: {
                    format: {
                        /*...*/
                    },
                    contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                        var $$ = this, 
                            text, i, title, value, name, periodo, bgcolor;

                        for (i = 0; i < 3; i++) {

                            if (!text) {

                                // split string and create array.
                                var arr = Dias_Demanda_E[d[0].index].split(/-|\s|:/);

                                // decrease month value by 1
                                var data = new Date(arr[0], arr[1] - 1, arr[2], arr[3], arr[4], arr[5]);

                                // dia, mês e ano
                                var day = data.getDate();
                                var month = data.getMonth() + 1;
                                var year = data.getYear() + 1900;

                                day = day < 10 ? '0' + day : day;
                                month = month < 10 ? '0' + month : month;

                                // horas e minutos
                                var hours = data.getHours();
                                var minutes = data.getMinutes();

                                hours = hours < 10 ? '0' + hours : hours;
                                minutes = minutes < 10 ? '0' + minutes : minutes;

                                // título
                                title = day + '/' + month + '/' + year + " " + hours + ':' + minutes;

                                text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                            }

                            // periodo
                            switch (Periodo_E[d[0].index]) {
                                case 0:
                                    periodo = "Ponta";
                                    bgcolor = "#FF0000";
                                    break;

                                case 1:
                                    periodo = "FPontaInd";
                                    bgcolor = "#007F00";
                                    break;

                                case 2:
                                    periodo = "FPontaCap";
                                    bgcolor = "#1C84C6";
                                    break;

                                default:
                                    periodo = "---";
                                    bgcolor = "#000000";
                                    break;
                            }

                            // nome
                            switch (i) {
                                case 0:
                                    name = "Demanda Atv";
                                    unit = "kW";
                                    value = DemandaAtv_E[d[0].index].toFixed(3);
                                    break;

                                case 1:
                                    name = "Demanda Rtv";
                                    unit = "kVAr";
                                    value = DemandaRtv_E[d[0].index].toFixed(3);
                                    break;

                                case 2:
                                    name = "Período";
                                    unit = "";
                                    value = periodo;
                                    break;
                            }

                            value = value.replace('.', ',');

                            // verifica se sem registro
                            if (Periodo_E[d[0].index] == 3) {
                                value = '---';
                            }

                            if (i == 2) {
                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'>" + name + "</td>";
                                text += "<td class='value'>" + value + "</td>";
                                text += "</tr>";
                            }
                            else {
                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + unit + "</td>";
                                text += "</tr>";
                            }
                        }

                        return text + "</table>";
                    }
                }
            });



            //
            // DEMANDA gateX
            //
            var data_X = [];
            var label_X = [];
            var dataDemanda_X = [];

            // copia valores
            var DemandaAtv_X = @Html.Raw(Json.Encode(@ViewBag.DemandaAtv_X));
            var DemandaRtv_X = @Html.Raw(Json.Encode(@ViewBag.DemandaRtv_X));
            var Periodo_X = @Html.Raw(Json.Encode(@ViewBag.Periodo_X));
            var Dias_Demanda_X = @Html.Raw(Json.Encode(@ViewBag.Dias_Demanda_X));

            var numRegistros_X = @Html.Raw(Json.Encode(@ViewBag.numRegistros_X));

            data_X.push('x');
            dataDemanda_X.push(['data1']);

            for (i = 0; i < numRegistros_X; i++)
            {
                // X
                data_X.push(Dias_Demanda_X[i]);

                // demanda
                dataDemanda_X.push([DemandaAtv_X[i]]);
            }

            // valores label X
            var multiplo = numRegistros_X / 8;
            var i = 1;

            label_X.push(Dias_Demanda_X[1]);
            for (i = 1; i < numRegistros_X; i += multiplo) {
                var indice = Math.round(i);
                label_X.push(Dias_Demanda_X[indice]);
            }

            var Data_X = [data_X, dataDemanda_X];

            c3.generate({

                bindto: '#demanda_X-chart',
                size: {
                    height: 300
                },
                padding: {
                    top: 8,
                    right: 10,
                    bottom: 30,
                    left: 43
                },
                data: {
                    x: 'x',
                    xFormat: '%Y-%m-%d %H:%M:%S',
                    columns: Data_X,
                    types: {
                        data1: 'area-step',
                    },
                    color: function (color, d) {
                        if (typeof d.index === 'undefined') { return color; }

                        if (Periodo_X[d.index] == 0)
                            return '#FF0000';

                        if (Periodo_X[d.index] == 1)
                            return '#007F00';

                        if (Periodo_X[d.index] == 2)
                            return '#1C84C6';

                        return '#007F00';
                    },
                    colors: {
                        data1: '#001F00',
                    },
                },
                point: {
                    show: false,
                    sensitivity: 1000
                },
                legend: {
                    show: false
                },
                axis: {
                    x: {
                        type: 'timeseries',
                        tick: {
                            outer: false,
                            values: label_X,
                            format: function (x) {

                                // horas e minutos
                                var hours = x.getHours();
                                var minutes = x.getMinutes();

                                hours = hours < 10 ? '0' + hours : hours;
                                minutes = minutes < 10 ? '0' + minutes : minutes;
                                var strTime = hours + ':' + minutes;

                                // retorna hora e minuto
                                return strTime;
                            }
                        },
                        // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                        // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                        padding: -1000 * 60 * 6
                    },
                    y: {
                        min: 0,
                        max: maximoDem,
                        padding: {
                            top: 0,
                            bottom: 0
                        },
                        tick: {
                            outer: false,
                            format: function (d) {

                                if (maximoDem < 100) {
                                    return d.toFixed(1);
                                }

                                return parseInt(d);
                            }
                        }
                    }
                },
                grid: {
                    x: {
                        show: false
                    }
                },
                bar: {
                    width: { ratio: 0.15 },
                },
                tooltip: {
                    format: {
                        /*...*/
                    },
                    contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                        var $$ = this, 
                            text, i, title, value, name, bgcolor;

                        for (i = 0; i < 3; i++) {

                            if (!text) {

                                // split string and create array.
                                var arr = Dias_Demanda_X[d[0].index].split(/-|\s|:/);

                                // decrease month value by 1
                                var data = new Date(arr[0], arr[1] - 1, arr[2], arr[3], arr[4], arr[5]);

                                // dia, mês e ano
                                var day = data.getDate();
                                var month = data.getMonth() + 1;
                                var year = data.getYear() + 1900;

                                day = day < 10 ? '0' + day : day;
                                month = month < 10 ? '0' + month : month;

                                // horas e minutos
                                var hours = data.getHours();
                                var minutes = data.getMinutes();

                                hours = hours < 10 ? '0' + hours : hours;
                                minutes = minutes < 10 ? '0' + minutes : minutes;

                                // título
                                title = day + '/' + month + '/' + year + " " + hours + ':' + minutes;

                                text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                            }

                            // periodo
                            switch (Periodo_X[d[0].index]) {
                                case 0:
                                    periodo = "Ponta";
                                    bgcolor = "#FF0000";
                                    break;

                                case 1:
                                    periodo = "FPontaInd";
                                    bgcolor = "#007F00";
                                    break;

                                case 2:
                                    periodo = "FPontaCap";
                                    bgcolor = "#1C84C6";
                                    break;

                                default:
                                    periodo = "---";
                                    bgcolor = "#000000";
                                    break;
                            }

                            // nome
                            switch (i) {
                                case 0:
                                    name = "Demanda Atv";
                                    unit = "kW";
                                    value = DemandaAtv_X[d[0].index].toFixed(3);
                                    break;

                                case 1:
                                    name = "Demanda Rtv";
                                    unit = "kVAr";
                                    value = DemandaRtv_X[d[0].index].toFixed(3);
                                    break;

                                case 2:
                                    name = "Período";
                                    unit = "";
                                    value = periodo;
                                    break;
                            }

                            value = value.replace('.', ',');

                            // verifica se sem registro
                            if (Periodo_X[d[0].index] == 3) {
                                value = '---';
                            }

                            if (i == 2) {
                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'>" + name + "</td>";
                                text += "<td class='value'>" + value + "</td>";
                                text += "</tr>";
                            }
                            else {
                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + unit + "</td>";
                                text += "</tr>";
                            }
                        }

                        return text + "</table>";
                    }
                }
            });

        });

        function SelecionouTipo_E() {

            // pega tipo selecionado
            var tipo = $("#TiposEvento_E").val();

            // verifica se todos
            if (tipo == 0) {
                // procura na tabela
                $('.dataTables-eventosE').DataTable().column(1).search("").draw();
            }
            else {
                // procura na tabela
                $('.dataTables-eventosE').DataTable().column(1).search(tipo).draw();
            }
        }

        function SelecionouTipo_X() {

            // pega tipo selecionado
            var tipo = $("#TiposEvento_X").val();

            // verifica se todos
            if (tipo == 0) {
                // procura na tabela
                $('.dataTables-eventosX').DataTable().column(1).search("").draw();
            }
            else {
                // procura na tabela
                $('.dataTables-eventosX').DataTable().column(1).search(tipo).draw();
            }
        }

        function ExecutarAnalise(navega) {

            // IDGateway
            var IDGateway_E = document.getElementById("IDGateway").value;

            // data
            var data_ini = document.querySelector('[name="data_relat_ini"]').value + " " + document.querySelector('[name="hora_relat_ini"]').value + ":00";
            var data_fim = document.querySelector('[name="data_relat_fim"]').value + " " + document.querySelector('[name="hora_relat_fim"]').value + ":00";

            var url = '/Supervisao/SupervisaoGateE_TesteCampo_Resultado?IDGateway=' + IDGateway_E + '&Navegacao=' + navega + '&inicio=' + data_ini + '&fim=' + data_fim;
            window.location.href = url;
        }

        $(".link_menos").off().on("click", function (event) {
            event.stopPropagation();

            // período anterior
            ExecutarAnalise(-3);
        });

        $(".link_mais").off().on("click", function (event) {
            event.stopPropagation();

            // período seguinte
            ExecutarAnalise(3);
        });

        $(".grafico_E").off().on("click", function (event) {
            event.stopPropagation();

            // IDCliente e IDMedicao
            var IDCliente_E = @Html.Raw(Json.Encode(Model.IDCliente_E));
            var IDMedicao_E = @Html.Raw(Json.Encode(Model.IDMedicao_E));

            // data
            var data_ini = document.querySelector('[name="data_relat_ini"]').value + " " + document.querySelector('[name="hora_relat_ini"]').value + ":00";

            var url = '/Relatorios/Relat_Dem_Ativa?IDCliente=' + IDCliente_E + '&IDMedicao=' + IDMedicao_E + '&tipo_arquivo=0&Data=' + data_ini;
            window.location.href = url;
        });

        $(".grafico_X").off().on("click", function (event) {
            event.stopPropagation();

            // IDCliente e IDMedicao
            var IDCliente_X = @Html.Raw(Json.Encode(Model.IDCliente_X));
            var IDMedicao_X = @Html.Raw(Json.Encode(Model.IDMedicao_X));

            // data
            var data_ini = document.querySelector('[name="data_relat_ini"]').value + " " + document.querySelector('[name="hora_relat_ini"]').value + ":00";

            var url = '/Relatorios/Relat_Dem_Ativa?IDCliente=' + IDCliente_X + '&IDMedicao=' + IDMedicao_X + '&tipo_arquivo=0&Data=' + data_ini;
            window.location.href = url;
        });

        function AlterouGateway() {

            // pega IDGateway
            var IDGateway_E = document.getElementById("IDGateway_E").value;

            // altera gateway
            document.getElementById("IDGateway").value = IDGateway_E;

            ExecutarAnalise(100);
        }

</script>
}
