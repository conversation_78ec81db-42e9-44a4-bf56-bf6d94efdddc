﻿@model IEnumerable<SmartEnergyLib.SQL.ClientesDominio>

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoClientesInativos;
}

<head>
    <title>@SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoClientesInativos</title>
    <style>

        a:link {
            color: #676A6C;
        }

        a:hover {
            color: #AFB0B1;
        }

    </style>
</head>

@{
    int IDConsultor = ViewBag._IDConsultor;
}

<div class="wrapper wrapper-content">

    <div class="row">
        <div class="col-lg-4">
            <a href="#">
                <div class="widget style1 navy-bg">
                    <div class="row">
                        <div class="col-xs-3">
                            <i class="fa fa-th-large fa-5x"></i>
                        </div>
                        <div class="col-xs-9 text-right">
                            <span> Número de @SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoClientesInativos </span>
                            <h2 class="font-bold">@ViewBag.NumClientes</h2>
                        </div>
                    </div>
                </div>
            </a>
        </div>
        <div class="col-lg-4">
            <div class="widget style1 navy-bg">
                <div class="row">
                    <div class="col-xs-3">
                        <i class="fa fa-download fa-5x"></i>
                    </div>
                    <div class="col-xs-9 text-right">
                        <span> Número de Gateways </span>
                        <h2 class="font-bold">@ViewBag.NumGateways</h2>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="widget style1 navy-bg">
                <div class="row">
                    <div class="col-xs-3">
                        <i class="fa fa-dashboard fa-5x"></i>
                    </div>
                    <div class="col-xs-9 text-right">
                        <span> Número de Medições </span>
                        <h2 class="font-bold">@ViewBag.NumMedicoes</h2>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <br />

    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">
                    <table class="table table-striped table-bordered table-hover dataTables-clientes">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.Logo</th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.Clientes</th>

                                @{
                                    if (IDConsultor == CLIENTES_ESPECIAIS.CPFL)
                                    {
                                        <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.UnidadesConsumidoras</th>
                                    }
                                    else
                                    {
                                        <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.AgentesFiliais</th>
                                    }
                                }

                                <th class="some_minidesktop">Gateways</th>
                                <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.NumMedicoes</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>

                            @foreach (var cliente in Model)
                            {
                                if( cliente.IDTipoContrato == 2)
                                {
                                    // nao apresenta cliente consultor
                                    continue;
                                }

                                string logo = "http://www.smartenergy.com.br/";

                                if (cliente.Logo.IsEmpty())
                                {
                                    logo += "/Logos/LogoSmartEnergy.png";
                                }
                                else
                                {
                                    logo += "/Logos/" + cliente.Logo;
                                }
                                
                                <tr>
                                    <td>@cliente.IDCliente</td>
                                    <td bgcolor="#293846" align="center"><img src=@logo style="max-height:45px; height: auto;" /></td>
                                    <td><b>@cliente.Fantasia</b><br />@cliente.Nome</td>
                                    <td class="some_minidesktop">@cliente.NumEmpresas</td>
                                    <td class="some_minidesktop">@cliente.NumGateways</td>
                                    <td class="some_minidesktop">@cliente.NumMedicoes</td>
                                    <td class="link_preto">
                                        <a href='@("/Supervisao/Medicoes?IDCliente=" + @cliente.IDCliente.ToString())' title="@SmartEnergy.Resources.ComumTexts.Supervisao"><i class="fa fa-desktop icones"></i></a>
                                        <a href='@("/Configuracao/Cliente_Editar?IDCliente=" + @cliente.IDCliente.ToString())' title="@SmartEnergy.Resources.ComumTexts.Configuracao"><i class="fa fa-gear icones"></i></a>
                                    </td>
                                </tr>
                            }

                        </tbody>
                    </table>

                </div>
            </div>
        </div>
    </div>


</div>

@section Styles {
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">
    $(document).ready(function () {

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {
                var x = (a == "-") ? 0 : a.replace(/,/, ".");
                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-clientes').DataTable({
            "iDisplayLength": 9,
            dom: 'ftp',

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "numero" },
                        { "aTargets": [1], "sType": "portugues", "bSortable": false, "bSearchable": false },
                        { "aTargets": [2], "sType": "portugues" },
                        { "aTargets": [3], "sType": "numero" },
                        { "aTargets": [4], "sType": "numero" },
                        { "aTargets": [5], "sType": "numero" },
                        { "aTargets": [6], "bVisible": true, "bSortable": false, "bSearchable": false },
            ],
            "aoColumns": [
                { sWidth: "4%" },
                { sWidth: "10%" },
                { sWidth: "41%" },
                { sWidth: "15%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "10%" }
            ],
            'order': [2, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

            });
        });

</script>
}


