﻿@using SmartEnergyLib.SQL

@{
    List<OperacaoAssistida> medicoes = ViewBag.medicoes;

    int NumDias = 0;
    
    if( ViewBag.NumDias != null )
    {
        NumDias = ViewBag.NumDias;        
    }
}

<div id="operacao_assistida_aguarde" style="display:block; height: 400px; margin-left:-20px; padding-top: 200px;">
    <div class="col-lg-12">
        <div class="spiner-aguarde">
            <div class="sk-spinner sk-spinner-wandering-cubes">
                <div class="sk-spinner sk-spinner-wave">
                    <div class="sk-rect1"></div>
                    <div class="sk-rect2"></div>
                    <div class="sk-rect3"></div>
                    <div class="sk-rect4"></div>
                    <div class="sk-rect5"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row" id="operacao_assistida_resultado" style="display:none;">
    <div class="col-lg-12">
        <div class="panel panel-default">
            <div class="panel-body">
                <table id="dataTables-operacao-assistida" class="table table-striped table-bordered table-hover dataTables-operacao-assistida">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>@SmartEnergy.Resources.RelatoriosTexts.Medicoes</th>
                            <th>@SmartEnergy.Resources.SupervisaoTexts.UltimaAtualizacao</th>

                            @{
                                for (int i = 0; i < 31; i++)
                                {
                                    string texto_dia = "";

                                    if (i < NumDias)
                                    {
                                        texto_dia = string.Format("{0}", i + 1);
                                    }

                                    <th>@texto_dia</th>
                                }
                            }
                        </tr>
                    </thead>
                    <tbody>

                        @{
                            int IDCliente = ViewBag._IDCliente;
                            
                            DateTime Agora = DateTime.Now;
                            DateTime Hoje = new DateTime(Agora.Year, Agora.Month, Agora.Day, 0, 0, 0);
                            DateTime DataAtualN = ViewBag.DataAtualN;
                            
                            if (medicoes != null)
                            {
                                foreach (OperacaoAssistida medicao in medicoes)
                                {
                                    DateTime DataHoraAtualizacao = new DateTime();
                                    string DataHora_Sort = "---";
                                    
                                    if (DateTime.TryParse(medicao.DataHoraAtualizacao, out DataHoraAtualizacao))
                                    {
                                        DataHora_Sort = string.Format("{0:yyyyMMddHHmmss}", DataHoraAtualizacao);
                                    }
                                    
                                    <tr>
                                        <td>@medicao.IDMedicao</td>
                                        <td>@medicao.NomeMedicao</td>

                                        @if(medicao.DataHoraAtualizacao == null)
                                        {
                                            <td>---</td>
                                        }
                                        else
                                        {
                                            <td><span style="display:none;">@DataHora_Sort</span>@medicao.DataHoraAtualizacao</td>
                                        }

                                        @for (int i = 0; i < 31; i++)
                                        {
                                            string classe = "badge-white";
                                            string titulo = "---";
                                            string estado = "-";
                                            
                                            if (i < NumDias)
                                            {
                                                DateTime dia = new DateTime(DataAtualN.Year, DataAtualN.Month, i + 1, 0, 0, 0);
                                                
                                                // status
                                                switch (medicao.Dia[i].Status)
                                                {
                                                    case 0:
                                                        classe = "badge-primary";
                                                        estado = " ";
                                                        titulo = "Normal";
                                                        break;
                                                        
                                                    default:    
                                                    case 1:
                                                        
                                                        classe = "badge-white";
                                                        estado = "-";
                                                        titulo = "Sem registros";
                                                        break;

                                                    case 2:

                                                        // verifica se eh hoje
                                                        if (dia == Hoje)
                                                        {
                                                            classe = "badge-info";
                                                            estado = " ";
                                                            titulo = "Não consolidado";
                                                        }
                                                        else
                                                        {
                                                            classe = "badge-danger";
                                                            estado = " ";
                                                            titulo = "Faltam registros";
                                                        }
                                                            
                                                        break;

                                                    case 3:
                                                        classe = "badge-danger";
                                                        estado = " ";
                                                        titulo = "Registros com valor em ZERO";
                                                        break;
                                                }

                                                <td>
                                                    <a href='@("/Relatorios/Relat_Dem_Ativa?IDCliente=" + @IDCliente.ToString() + "&IDMedicao=" + @medicao.IDMedicao.ToString() + "&tipo_arquivo=0&Data=" + string.Format("{0:dd/MM/yyyy}", dia))' title="@titulo">
                                                        <span class="badge @classe">&nbsp;&nbsp;@estado&nbsp;&nbsp;</span>                                                
                                                    </a>
                                                </td>                                            
                                            }
                                            else
                                            {
                                                <td><span class="badge @classe">&nbsp;&nbsp;@estado&nbsp;&nbsp;</span></td>                                            
                                            }
                                        }

                                    </tr>
                                }
                            }
                        }

                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>



<script type="text/javascript">

    $(document).ready(function () {

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {
                var x = (a == "-") ? 0 : a.replace(/,/, ".");
                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });


        $('.dataTables-operacao-assistida').DataTable({
            "scrollX": true,
            "responsive": true,
            "bAutoWidth": false,
            "iDisplayLength": 12,
            dom: 'ftp',
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "numero" },
                        { "aTargets": [1], "sType": "portugues", "bSortable": true, "bSearchable": true },
                        { "aTargets": [2], "sType": "portugues", "bSortable": true, "bSearchable": true },
                        { "aTargets": [3], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [4], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [5], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [6], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [7], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [8], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [9], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [10], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [11], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [12], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [13], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [14], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [15], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [16], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [17], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [18], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [19], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [20], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [21], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [22], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [23], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [24], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [25], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [26], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [27], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [28], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [29], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [30], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [31], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [32], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [33], "sType": "numero", "bSortable": false, "bSearchable": false },
            ],
            "oSearch": {
                "bSmart": false,
                "bRegex": true,
                "sSearch": ""
            },
            "aoColumns": [
            { sWidth: "10" },
            { sWidth: "40" },
            { sWidth: "29%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "1%" }
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

    
        // cookie de search
        var search = decodeURIComponent(getCookie("Relat_Search"));
        $('.dataTables-operacao-assistida').DataTable().search(search).draw();

        $('.dataTables_filter input').unbind().keyup(function () {
            var value = $(this).val();
            if (value.length > 3 || value.length == 0) {

                // salva em cookie
                setCookie("Relat_Search", encodeURIComponent(value), null);

                $('.dataTables-operacao-assistida').DataTable().search(value).draw();
            }
        });

        // ajusta largura depois de um tempo
        setTimeout(function () {

            $('#dataTables-operacao-assistida').dataTable().fnSort([[1, "asc"]]);

        }, 10);

        // labels
        $('#operacao_assistida_aguarde').css("display", "none");
        $('#operacao_assistida_resultado').css("display", "block");


        // trigger datatable
        $('.dataTables-operacao-assistida')
                .on('order.dt', function () { TableOrder(1); })
                .DataTable();

    });


    function TableOrder(tab_selecionado) {

        // nome tabela
        var nome_tabela = '#dataTables-operacao-assistida';

        // pega coluna de sort
        var sortedCol = $(nome_tabela).dataTable().fnSettings().aaSorting[0][0];

        // salva em cookie
        setCookie("Relat_SortedCol", sortedCol, null);

        // pega direcao de sort
        var sortedDir = $(nome_tabela).dataTable().fnSettings().aaSorting[0][1];

        // salva em cookie
        setCookie("Relat_SortedDir", sortedDir, null);
    } 

</script>
