﻿@using System.Globalization

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoClientesOperacaoAssistida;
}

<div class="wrapper wrapper-content">

    <div class="row">
        <div class="col-lg-12">

            @{
                <div class="panel panel-title relatorio" id="operacao_assistida">
                    @Html.Partial("_Medicoes_OperacaoAssistida_Atualizar")
                </div>
            }

        </div>
    </div>

</div>

@section Styles {
    @Styles.Render("~/datetimePickerStyles")
    @Styles.Render("~/Content/Relatorios.css")
    @Styles.Render("~/plugins/select2Styles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
    @Styles.Render("~/plugins/sweetAlertStyles")
}   

@section Scripts {
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/plugins/select2")
    @Scripts.Render("~/plugins/iCheck")
    @Scripts.Render("~/plugins/waitingFor")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
  

    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
        <script type="text/javascript" language="javascript" src="~/Scripts/locales/bootstrap-datepicker.es.min.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
        <script type="text/javascript" language="javascript" src="~/Scripts/locales/bootstrap-datepicker.pt-BR.min.js"></script>
    }

    <script type="text/javascript">

        $(document).ready(function () {

            // atualiza
            Atualiza(0);

        });

        function Atualiza(navega) {

            $.ajax(
            {
                type: 'GET',
                url: '/Supervisao/_Medicoes_OperacaoAssistida_Atualizar',
                dataType: 'html',
                data: { 'Navegacao': navega },
                cache: false,
                async: true,
                success: function (data) {
                    $('#operacao_assistida').html(data);
                }
            });
        }

    </script>

}






