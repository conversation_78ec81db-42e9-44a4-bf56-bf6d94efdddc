﻿@using System.Globalization

@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoSD;
}

<head>
    <title>@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoSD</title>
    <style>

        a:link {
            color: #676A6C;
        }

        a:hover {
            color: #AFB0B1;
        }


        .tooltip-inner {
            max-width: 350px;
            /* If max-width does not work, try using width instead */
            width: 350px;
            white-space: pre-wrap;
            text-align: left;
        }

        .tooltip-inner {
            max-width: 350px;
            /* If max-width does not work, try using width instead */
            width: 350px;
            white-space: pre-wrap;
            text-align: left;
        }

        .Atualiza-heading {
          position: relative;
        }

        .btn.Atualiza-btn {
            position:absolute;
            right: 0;
            top: 0;
            height: 100%;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
        }

    .disabled {
            pointer-events: none;
            cursor: default;
    }

    .overlay_aguarde
     {
         position: absolute;
         top: 0;
         left: 0;
         right: 0;
         bottom: 0;
         background-color: rgba(0, 0, 0, 0.45);
         z-index: 9999;
         color: white;
         display: inline-block;
     }

    .sk-spinner-wave div {
      background-color: #ffffff !important;
    }

    .spinner-aguarde {
        margin: 0;
        position: absolute;
        top: 50%;
        left: 50%;
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
    }

    .panel-body { 
        height:100%; 
        position:relative; 
    }


    .led-box {
      height: 30px;
      width: 25%;
      margin: 10px 0;
      float: left;
    }

    .led-box p {
      font-size: 12px;
      text-align: center;
      margin: 1em;
    }

    .led-red {
      margin: 0 auto;
      width: 20px;
      height: 20px;
      background-color: #F00;
      border-radius: 50%;
      box-shadow: rgba(0, 0, 0, 0.2) 0 -1px 5px 1px, inset #441313 0 -1px 7px, rgba(255, 0, 0, 0.5) 0 2px 10px;
    }

    .led-green {
      margin: 0 auto;
      width: 20px;
      height: 20px;
      background-color: #89FF00;
      border-radius: 50%;
      box-shadow: rgba(0, 0, 0, 0.2) 0 -1px 5px 1px, inset #304701 0 -1px 7px, #89FF00 0 2px 10px;
    }

    .led-yellow {
      margin: 0 auto;
      width: 20px;
      height: 20px;
      background-color: #FF0;
      border-radius: 50%;
      box-shadow: rgba(0, 0, 0, 0.2) 0 -1px 5px 1px, inset #808002 0 -1px 7px, #F00 0 2px 10px;
    }

    .led-gray {
      margin: 0 auto;
      width: 20px;
      height: 20px;
      background-color: #a0a0a0;
      border-radius: 50%;
      box-shadow: rgba(0, 0, 0, 0.2) 0 -1px 5px 1px, inset #304701 0 -1px 7px, #a0a0a0 0 2px 10px;
    }

</style>
</head>

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            <input type="hidden" id="GatewayID" name="GatewayID" value="@ViewBag.GatewayID">

            <div class="panel panel-title" >
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoSD</h4>
                </div>
                <div class="panel-body">

                    <div class="row">
                        <div class="col-lg-12">

                            <div class="panel panel-success">
                                <div class="panel-heading Atualiza-heading">
                                    <h4>Gateway</h4><br />

                                    @{
                                        int IDTipoAcesso = ViewBag._IDTipoAcesso;

                                        if (IDTipoAcesso == 0 || IDTipoAcesso == 5)
                                        {
                                            <span>[@ViewBag.GatewayID] </span>
                                        }
                                    }

                                    @ViewBag.GatewayNome

                                    <button class="Atualiza-btn btn btn-primary btn-lg" onclick="AtualizaSaidas_Pergunta()" style="color:#ffffff; font-weight:bold;">Atualiza Estado das Saídas</button>
                                </div>
                            </div>

                        </div>
                    </div>
                    <br />

                    <div id="superv_resultado">

                        @{
                            List<SaidasDigitaisDominio> saidas = ViewBag.listaSaidas;

                            if (saidas != null)
                            {
                                @Html.Partial("_SaidasDigitais_Resultado")
                            }
                        }

                    </div>

                </div>
            </div>

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/iCheck")



    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

    $(document).ready(function () {

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "portugues-pre": function (data) {
                    var a = 'a';
                    var e = 'e';
                    var i = 'i';
                    var o = 'o';
                    var u = 'u';
                    var c = 'c';
                    var special_letters = {
                        "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                        "É": e, "é": e, "Ê": e, "ê": e,
                        "Í": i, "í": i, "Î": i, "î": i,
                        "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                        "Ú": u, "ú": u, "Ü": u, "ü": u,
                        "ç": c, "Ç": c
                    };
                    for (var val in special_letters)
                        data = data.split(val).join(special_letters[val]).toLowerCase();
                    return data;
                },
                "portugues-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },
                "portugues-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "numero-pre": function (a) {

                    var x = a;

                    if (a == "-") {
                        x = "-1";
                    }
                    else {
                        x = a.replace(".", "");
                        x = x.replace(",", ".");
                    }

                    return parseFloat(x);
                },

                "numero-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },

                "numero-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            $('.dataTables-saidas').DataTable({
                "iDisplayLength": 12,
                dom: 'ftp',
                "bAutoWidth": false,
                "aoColumnDefs": [
                            { "aTargets": [0], "sType": "portugues" },
                            { "aTargets": [1], "sType": "numero" },
                            { "aTargets": [2], "bSortable": false, "bSearchable": false },
                            { "aTargets": [3], "sType": "portugues" },
                            { "aTargets": [4], "bSortable": false, "bSearchable": false }
                ],
                "aoColumns": [
                { sWidth: "63%" },
                { sWidth: "15%" },
                { sWidth: "2%" },
                { sWidth: "10%" },
                { sWidth: "10%" }
                ],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }
            });

            // ativa blink
            piscando();

            //
            // Atualiza saidas
            //

            AtualizaSaidas();

        });

        function piscando() {

            var tempo = 500; //1000 = 1s

            blinks = document.getElementsByTagName("blink");
            for (var i = 0; i < blinks.length; i++) {
                if (blinks[i].getAttribute("style") == "VISIBILITY: hidden") {
                    blinks[i].setAttribute("style", "VISIBILITY: visible");
                } else {
                    blinks[i].setAttribute("style", "VISIBILITY: hidden");
                }
            }
            setTimeout('piscando()', tempo);
        }


        //
        // Atualiza saidas com pergunta
        //
        function AtualizaSaidas_Pergunta() {

            // pergunta se deseja atualizar as saídas
            swal({
                html: true,
                title: "Deseja atualizar o Estado das Saídas Digitais ?",
                text: "Esta operação poderá demorar alguns segundos.",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#18a689",
                confirmButtonText: "Atualizar",
                cancelButtonText: "Cancelar",
                closeOnConfirm: true
            }, function () {

                setTimeout(function () {

                    // atualiza saidas
                    AtualizaSaidas();

                }, 100);

            });

        }

        //
        // Atualiza saidas
        //
        function AtualizaSaidas() {

            // pega IDGateway selecionado
            var IDGateway = $("#GatewayID").val();

            // aguarde
            $('.overlay_aguarde').toggle();

            // apresenta resultado saidas
            $.ajax(
            {
                type: 'GET',
                url: '/Supervisao/_SaidasDigitais_Resultado',
                dataType: 'html',
                data: { 'IDGateway': IDGateway },
                success: function (resultado) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    // retornou, apresenta resultado
                    $('#superv_resultado').html(resultado);

                },
                error: function (xhr, status, error) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    // retornou ERRO, apresenta janela de erro
                    swal({
                        title: "Erro",
                        text: xhr.responseText,
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    });

                }
            });
        }


        // estados
        const TIMEOUT_GATEWAY = -4;  // Gateway demorou para responder
        const TIMEOUT_MQTT = -3;     // SmartMQTT demorou para mudar de estado
        const SOLICITANDO = -2;      // solicitando status para a gateway
        const DESCONHECIDO = -1;     // desconhecido (ou erro)
        const AUTO_DESL = 0;         // automático desligado [Desligado 0x00 | Automático 0x00      ]
        const AUTO_LIG = 256;        // automático ligado    [Ligado 0x01    | Automático 0x00      ]
        const MANUAL_DESL = 1;       // manual desligado     [Desligado 0x00 | Manual Desligado 0x01]
        const MANUAL_LIG = 258;      // manual ligado        [Ligado 0x01    | Manual Ligado 0x02   ]

        //
        // Comanda saída
        //
        function ComandaSaida(Comando, NumSaidaGateway, NomeSaida) {
            event.stopPropagation();

            // nome comando
            nome_comando = "";
            cor_botao = "";

            switch (Comando) {
                case 1: // comando automático

                    nome_comando = "Modo Automático";
                    cor_botao = "#0000ff";
                    break;

                case 2: // comando manual desliga

                    nome_comando = "Manual Desliga";
                    cor_botao = "#ff0000";
                    break;

                case 3: // comando manual liga

                    nome_comando = "Manual Liga";
                    cor_botao = "#18a689";
                    break;
            }

            // titulo
            titulo = "Deseja executar o comando " + nome_comando + "?";
            subtitulo = "Saída Digital:  [" + NumSaidaGateway + "] " + NomeSaida;

            // pergunta se deseja comanda a saída
            swal({
                html: true,
                title: titulo,
                text: subtitulo,
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: cor_botao,
                confirmButtonText: nome_comando,
                cancelButtonText: "Cancelar",
                closeOnConfirm: true
            }, function () {

                setTimeout(function () {

                    // aguarde
                    AlteraEstadoNaTabela(NumSaidaGateway, SOLICITANDO);

                    // aguarde
                    $('.overlay_aguarde').toggle();

                    // pega IDGateway selecionado
                    var IDGateway = $("#GatewayID").val();

                    // apresenta resultado comando
                    $.ajax(
                    {
                        type: 'GET',
                        url: '/Supervisao/SaidasDigitais_SolicitaComando',
                        dataType: 'html',
                        data: { 'IDGateway': IDGateway, 'NumSaidaGateway': NumSaidaGateway, 'Comando': Comando },
                        success: function (resultado) {

                            // fim
                            $('.overlay_aguarde').toggle();

                            // atualiza estado das saidas
                            AtualizaSaidas();

                        },
                        error: function (xhr, status, error) {

                            // fim
                            $('.overlay_aguarde').toggle();

                            // retornou ERRO, apresenta janela de erro
                            swal({
                                title: "Erro",
                                text: xhr.responseText,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            });

                        }
                    });

                }, 100);

            });

        }

        // altera o estado na tabela
        function AlteraEstadoNaTabela(NumSaidaGateway, novo_estado)
        {
            // leio tabela
            var data = $('.dataTables-saidas').dataTable().fnGetData();

            // procura número da saída na coluna 1
            for (var e in data) {

                // verifica se achou número da saída
                if (data[e][1] == NumSaidaGateway) {

                    // piscar
                    var piscar = false;
                    var classe = "";
                    var descricao = "";
                    var cor_led = "";

                    // verifica estado
                    switch (novo_estado) {
                        case DESCONHECIDO:

                            classe = "badge-secondary";
                            descricao = "Desconhecido";
                            cor_led = "led-gray";
                            break;

                        case TIMEOUT_GATEWAY:

                            classe = "badge-warning";
                            descricao = "Erro de Timeout [Gateway]";
                            piscar = true;
                            cor_led = "led-yellow";
                            break;

                        case TIMEOUT_MQTT:

                            classe = "badge-warning";
                            descricao = "Erro de Timeout [MQTT]";
                            piscar = true;
                            cor_led = "led-yellow";
                            break;

                        case SOLICITANDO:

                            classe = "badge-warning";
                            descricao = "Solicitando...";
                            piscar = true;
                            cor_led = "led-yellow";
                            break;

                        case AUTO_DESL:

                            classe = "badge-danger";
                            descricao = "Desligado";
                            cor_led = "led-red";
                            break;

                        case MANUAL_DESL:

                            classe = "badge-danger";
                            descricao = "Desligado [MANUAL]";
                            piscar = true;
                            cor_led = "led-red";
                            break;

                        case AUTO_LIG:

                            classe = "badge-primary";
                            descricao = "Ligado";
                            cor_led = "led-green";
                            break;

                        case MANUAL_LIG:

                            classe = "badge-primary";
                            descricao = "Ligado [MANUAL]";
                            piscar = true;
                            cor_led = "led-green";
                            break;
                    }

                    var led = "<td><div class='" + cor_led + "'></div></></td>";

                    if (piscar) {
                        led = "<td><blink><div class='" + cor_led + "'></div><blink></td>";
                    }

                    var texto = "<td><span class='badge " + classe + "' style='width:300px;'>&nbsp;&nbsp;" + descricao + "&nbsp;&nbsp;</span></td>";

                    // altero status na lista
                    $('.dataTables-saidas').dataTable().fnUpdate(led, parseInt(e), 2, false, false);
                    $('.dataTables-saidas').dataTable().fnUpdate(texto, parseInt(e), 3, false, false);

                    break;
                }
            }
        }

    </script>
}




