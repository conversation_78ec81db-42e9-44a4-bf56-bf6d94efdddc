﻿@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

<div class="row">
    <div class="col-lg-12">

        <div class="panel panel-info">

            <div class="panel-body">

                @{
                    bool GatewayConectada_IoT = ViewBag.GatewayConectada_IoT;
                    int IDTipoAcesso = ViewBag._IDTipoAcesso;
                    int IDGateway = ViewBag._IDGateway;

                    // caso Demo
                    if (IDTipoAcesso == TIPO_ACESSO.DEMONSTRACAO)
                    {
                        GatewayConectada_IoT = true;
                    }

                    if (GatewayConectada_IoT)
                    {
                        <table class="table table-striped table-bordered table-hover dataTables-saidas">
                            <thead>
                                <tr>
                                    <th>@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoSD</th>
                                    <th>@SmartEnergy.Resources.SupervisaoTexts.NumSaida</th>
                                    <th>@SmartEnergy.Resources.SupervisaoTexts.Saida</th>
                                    <th>@SmartEnergy.Resources.SupervisaoTexts.Estado</th>
                                    <th>@SmartEnergy.Resources.SupervisaoTexts.Comandos</th>
                                </tr>
                            </thead>
                            <tbody>

                                @{
                                    List<SaidasDigitaisDominio> saidas = ViewBag.listaSaidas;

                                    var classe = "badge-secondary";
                                    string descricao = "Desconhecido";
                                    string cor_led = "led-gray";

                                    if (saidas != null)
                                    {
                                        foreach (SaidasDigitaisDominio saida in saidas)
                                        {
                                            <tr>
                                                <td>@saida.Descricao</td>
                                                <td>@saida.NumSaidaGateway</td>

                                                @{
                                                    bool piscar = false;

                                                    switch (saida.status)
                                                    {
                                                        case STATUS_SD.DESCONHECIDO:

                                                            classe = "badge-secondary";
                                                            descricao = "Desconhecido";
                                                            cor_led = "led-gray";
                                                            break;

                                                        case STATUS_SD.TIMEOUT_GATEWAY:

                                                            classe = "badge-warning";
                                                            descricao = "Erro de Timeout [Gateway não responde]";
                                                            piscar = true;
                                                            cor_led = "led-yellow";
                                                            break;

                                                        case STATUS_SD.TIMEOUT_MQTT:

                                                            classe = "badge-warning";
                                                            descricao = "Erro de Timeout [Servidor não responde]";
                                                            piscar = true;
                                                            cor_led = "led-yellow";
                                                            break;

                                                        case STATUS_SD.SOLICITANDO:

                                                            classe = "badge-warning";
                                                            descricao = "Solicitando";
                                                            cor_led = "led-yellow";
                                                            break;

                                                        case STATUS_SD.AUTO_DESL:

                                                            classe = "badge-danger";
                                                            descricao = "Desligado";
                                                            cor_led = "led-red";
                                                            break;

                                                        case STATUS_SD.MANUAL_DESL:

                                                            classe = "badge-danger";
                                                            descricao = "Desligado [MANUAL]";
                                                            piscar = true;
                                                            cor_led = "led-red";
                                                            break;

                                                        case STATUS_SD.AUTO_LIG:

                                                            classe = "badge-primary";
                                                            descricao = "Ligado";
                                                            cor_led = "led-green";
                                                            break;

                                                        case STATUS_SD.MANUAL_LIG:

                                                            classe = "badge-primary";
                                                            descricao = "Ligado [MANUAL]";
                                                            piscar = true;
                                                            cor_led = "led-green";
                                                            break;
                                                    }

                                                    if (piscar)
                                                    {
                                                        <td><blink><div class="@cor_led"></div></blink></td>
                                                    }
                                                    else
                                                    {
                                                        <td><div class="@cor_led"></div></td>
                                                    }

                                                    // controle associado
                                                    string controleAssociado = "";

                                                    int cAss = saida.cAss;
                                                    int rAss = saida.rAss;

                                                    if (cAss == 0)
                                                    {
                                                        controleAssociado = "Sem controle";

                                                        if (saida.status == STATUS_SD.DESCONHECIDO)
                                                        {
                                                            controleAssociado = "Desconhecido";
                                                        }

                                                        if (saida.status == STATUS_SD.TIMEOUT_GATEWAY)
                                                        {
                                                            controleAssociado = "Verificar Gateway";
                                                        }

                                                        if (saida.status == STATUS_SD.TIMEOUT_MQTT)
                                                        {
                                                            controleAssociado = "Verificar Servidor IoT";
                                                        }
                                                    }
                                                    else
                                                    {
                                                        var ctrlAss = new Dictionary<int, string>
                                                                            {
                                                    {0x8000, "Manual" },
                                                    {0x0001, "Controle Demanda Projetada" },
                                                    {0x0002, "Controle Demanda Média" },
                                                    {0x0004, "Controle Demanda Acumulada" },
                                                    {0x0008, "Controle Fator de Potência" },
                                                    {0x0800, "Controle Analógico" },
                                                    {0x0020, "Controle Horário" },
                                                    {0x0010, "Comando Horário de Tarifação" },
                                                    {0x0100, "Entrada Digital" },
                                                    {0x0040, "Falta de Pulsos na Medição" },
                                                    {0x0080, "Alarme" },
                                                    {0x0400, "Lógica" },
                                                    {0x4000, "Temporização" },
                                                    {0x0200, "Acesso Remoto" }
                                                };


                                                        controleAssociado = "<table>" +
                                                                               "<tbody>";

                                                        foreach (var ctrl in ctrlAss)
                                                        {
                                                            controleAssociado += "<tr><td>" + ctrl.Value + "    </td>";

                                                            string estado = "---";

                                                            if ((cAss & ctrl.Key) != 0)
                                                            {
                                                                estado = "Desligado";

                                                                if ((rAss & ctrl.Key) != 0)
                                                                {
                                                                    estado = "Ligado";
                                                                }
                                                            }

                                                            controleAssociado += "<td>" + estado + "</td></tr>";
                                                        }

                                                        controleAssociado += "</tbody>" +
                                                                             "</table>";
                                                    }

                                                    <td>
                                                        <span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="@controleAssociado">
                                                            <span class="badge @classe" style="width:300px;">&nbsp;&nbsp;@descricao&nbsp;&nbsp;</span>
                                                        </span>
                                                    </td>
                                                }

                                                @{
                                                    if (IDTipoAcesso != TIPO_ACESSO.CLIENTE_OPER && saida.status >= 0)
                                                    {
                                                        <td>
                                                            <div class="link_azul"><a href="#" onclick="ComandaSaida(1, @saida.NumSaidaGateway, '@saida.Descricao');" id="Automatico" title="@SmartEnergy.Resources.SupervisaoTexts.Automatico"><i class="fa fa-font icones"></i></a></div>
                                                            <div class="link_verde"><a href="#" onclick="ComandaSaida(3, @saida.NumSaidaGateway, '@saida.Descricao');" id="ManualLiga" title="@SmartEnergy.Resources.SupervisaoTexts.ManualLiga"><i class="fa fa-lock icones"></i></a></div>
                                                            <div class="link_vermelho"><a href="#" onclick="ComandaSaida(2, @saida.NumSaidaGateway, '@saida.Descricao');" id="ManualDesliga" title="@SmartEnergy.Resources.SupervisaoTexts.ManualDesliga"><i class="fa fa-unlock icones"></i></a></div>
                                                        </td>
                                                    }
                                                    else
                                                    {
                                                        <td>
                                                            <div class="link_disabled disabled"><a href="#" id="Automatico" title="@SmartEnergy.Resources.SupervisaoTexts.Automatico"><i class="fa fa-font icones"></i></a></div>
                                                            <div class="link_disabled disabled"><a href="#" id="ManualLiga" title="@SmartEnergy.Resources.SupervisaoTexts.ManualLiga"><i class="fa fa-lock icones"></i></a></div>
                                                            <div class="link_disabled disabled"><a href="#" id="ManualDesliga" title="@SmartEnergy.Resources.SupervisaoTexts.ManualDesliga"><i class="fa fa-unlock icones"></i></a></div>
                                                        </td>
                                                    }
                                                }

                                            </tr>
                                        }
                                    }
                                }

                            </tbody>
                        </table>
                    }
                    else
                    {
                        <br /><br /><br /><br /><br /><br />
                        <div class="row">
                            <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
                                <i class="fa fa-chain-broken fa-5x" style="color:red"></i>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
                                <h2 style="color:red">Gateway não conectada no Servidor IoT</h2>
                            </div>
                        </div>
                        <br /><br /><br /><br /><br /><br />
                    }
                }

            </div>

            <div class="overlay_aguarde" style="display: none">
                <div class="spinner-aguarde">
                    <div class="sk-spinner sk-spinner-wandering-cubes">
                        <div class="sk-spinner sk-spinner-wave">
                            <div class="sk-rect1 text-ligh"></div>
                            <div class="sk-rect2"></div>
                            <div class="sk-rect3"></div>
                            <div class="sk-rect4"></div>
                            <div class="sk-rect5"></div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>


<script type="text/javascript">

    $(document).ready(function () {

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {

                var x = a;

                if (a == "-") {
                    x = "-1";
                }
                else {
                    x = a.replace(".", "");
                    x = x.replace(",", ".");
                }

                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-saidas').DataTable({
            "iDisplayLength": 12,
            dom: 'ftp',
            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "portugues" },
                        { "aTargets": [1], "sType": "numero" },
                        { "aTargets": [2], "bSortable": false, "bSearchable": false },
                        { "aTargets": [3], "sType": "portugues" },
                        { "aTargets": [4], "bSortable": false, "bSearchable": false }
            ],
            "aoColumns": [
                { sWidth: "63%" },
                { sWidth: "15%" },
                { sWidth: "2%" },
                { sWidth: "10%" },
                { sWidth: "10%" }
            ],
            'order': [1, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            },

            fnDrawCallback: function (oSettings, json) {

                $(".bottom").tooltip({
                    placement: "bottom"
                });

            }
        });

    });


</script>
