﻿@using SmartEnergyLib.SQL

@{
    List<ObservacaoTagsDominio> tags = ViewBag.tags;
    ObservacaoMedicaoDominio observacao = ViewBag.observacao;

    int IDTipoAcesso = ViewBag._IDTipoAcesso;

    string desabilitado = "";

    if (isUser.isOperador(IDTipoAcesso) || IDTipoAcesso == 10)
    {
        desabilitado = "disabled";
    }

    string desabilitado_clientevisualiza = "";

    // nao permito ver flag CLIENTE VISUALIZA, caso:
    // - foi cliente pelo cliente
    // - usuario for DEMO ou cliente
    // - observacao for da GESTAL e se nao for usuario GESTAL 
    if (isUser.isCliente(observacao.IDTipoAcesso) || IDTipoAcesso == 10 || isUser.isCliente(IDTipoAcesso) || (isUser.isGESTAL(observacao.IDTipoAcesso) && !isUser.isGESTAL(IDTipoAcesso)))
    {
        desabilitado_clientevisualiza = "disabled";
    }
}


<div class="row">
    <div class="col-sm-12">
        <div class="form-group">
            <label class="control-label">&nbsp;Tag</label>

            <select class="form-control m-b" name="tags" id="tags" @desabilitado>
                <option value="0">@SmartEnergy.Resources.SupervisaoTexts.SemObservacao</option>

                @{
                    if (tags != null)
                    {
                        foreach (ObservacaoTagsDominio tag in tags)
                        {
                            if (tag.IDTag == observacao.IDTag)
                            {
                                <option value="@tag.IDTag" style="background-color: @tag.Cor; color: #FFFFFF;" selected>@tag.Descricao</option>
                            }
                            else
                            {
                                <option value="@tag.IDTag" style="background-color: @tag.Cor; color: #FFFFFF;">@tag.Descricao</option>
                            }
                        }
                    }
                }

            </select>
        </div>
    </div>
</div>

<br />
<div class="row">
    <div class="col-lg-4">
        <div class="form-group" id="data_relat">
            <label class="control-label">&nbsp;@SmartEnergy.Resources.RelatoriosTexts.Data</label>
            <div class="input-group">
                <span class="input-group-addon"><i class="fa fa-calendar"></i></span><input type="text" class="form-control" name="data_relat" value=@string.Format("{0:d}", observacao.DataHora) @desabilitado>
            </div>
        </div>
    </div>
</div>

<br />
<div class="row">
    <div class="col-lg-4">
        <div class="form-group">
            <div class="i-checks">

                @{
                    string ClienteVisualiza = "";
                    
                    if( observacao.ClienteVisualiza )
                    {
                        ClienteVisualiza = "checked";
                    }
                }

                <input type="checkbox" id="ClienteVisualiza" name="ClienteVisualiza" @desabilitado_clientevisualiza @ClienteVisualiza>&nbsp;&nbsp;@SmartEnergy.Resources.SupervisaoTexts.ClienteVisualiza

            </div>
        </div>
    </div>
</div>

<br />
<div class="row">
    <div class="col-sm-12">
        <div class="form-group">
            <label class="control-label">&nbsp;@SmartEnergy.Resources.SupervisaoTexts.Observacao</label>
            <textarea class="form-control" rows="5" id="ObservacaoTexto" onkeyup="limitaCaractere('ObservacaoTexto', 400);" @desabilitado>@observacao.Observacao</textarea>
        </div>
    </div>
</div>


<script type="text/javascript">

    $(document).ready(function () {

        // classe iChecks
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });

        $('#data_relat').datetimepicker({
            locale: 'pt-BR',
            format: 'DD/MM/YYYY',
            showTodayButton: true,
            allowInputToggle: true
        });


    });

    function limitaCaractere(textareaId, limite) {

        var caracterDigitado = document.getElementById(textareaId).value;
        var caracterRestante = limite - caracterDigitado.length;

        if (caracterDigitado.length >= limite) {
            document.getElementById(textareaId).value = document.getElementById(textareaId).value.substr(0, limite);
        }
    }

</script>
