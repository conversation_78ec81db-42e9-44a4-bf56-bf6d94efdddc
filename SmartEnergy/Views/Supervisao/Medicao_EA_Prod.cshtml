﻿@using System.Globalization

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoMedicao;
}

<div class="row wrapper border-bottom white-bg page-heading">
    <div class="row">
        <div class="col-lg-12">
            <div class="superv-content">

                <div class="col-lg-4 col-md-12">
                    <div class="row">
                        <div class="col-lg-12 col-md-6">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual superv-ea">
                                    <h4>@ViewBag.NomeGrandeza</h4>
                                    <h5>@SmartEnergy.Resources.DashboardTexts.MedioDia</h5>
                                    <h1>@ViewBag.Dia_Atual_Media <span>@ViewBag.UnidadeGrandeza</span></h1>
                                    <h6>@ViewBag.DiaAtual</h6>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="superv-tabela superv-ea">
                                <table class="table no-margins" style="width:100%;">
                                    <thead>
                                        <tr>
                                            <th style="width:50%;"></th>
                                            <th style="width:50%;">@ViewBag.NomeGrandeza</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="text-align:center">@SmartEnergy.Resources.SupervisaoTexts.Maximo<br /><small>&nbsp;</small></td>
                                            <td>@ViewBag.Dia_Atual_Max @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.Dia_Atual_MaxDataHora)</small></td>
                                        </tr>
                                        <tr>
                                            <td style="text-align:center">@SmartEnergy.Resources.SupervisaoTexts.Minimo<br /><small>&nbsp;</small></td>
                                            <td>@ViewBag.Dia_Atual_Min @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.Dia_Atual_MinDataHora)</small></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-8 col-md-12">
                    <div class="row">
                        <div class="superv-grafico">
                            <div class="flot-chart">
                                <div class="postlet-grafico-ea" id="grafico-ea"></div>
                            </div>
                            <div class='chart-legend'>
                                <div class='legend-scale-dem'>
                                    <ul class='legend-labels'>
                                        <li><span style='background:#1C84C6;'></span>@ViewBag.NomeGrandeza</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<div class="wrapper wrapper-content">

    <div class="row">
        <div class="col-lg-3">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h4>Gateway</h4><br />
                    @ViewBag.GatewayNome
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.SupervisaoTexts.ModeloVersao</h4><br />
                    @ViewBag.GatewayModelo
                </div>
            </div>
        </div>
        <div class="col-lg-2">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h4>Status</h4><br />
                    @ViewBag.GatewayStatus
                </div>
            </div>
        </div>
        <div class="col-lg-2">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.SupervisaoTexts.UltimaAtualizacao</h4><br />
                    @ViewBag.GatewayAtualizacao
                </div>
            </div>
        </div>
        <div class="col-lg-2">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.SupervisaoTexts.DataHoraEq</h4><br />
                    @ViewBag.GatewayDataEq
                </div>
            </div>
        </div>
    </div>

</div>

@section Styles {
    @Styles.Render("~/plugins/c3Styles")
    @Styles.Render("~/plugins/select2Styles")
    @Styles.Render("~/Supervisao.css")
}

@section Scripts {
    @Scripts.Render("~/plugins/d3")
    @Scripts.Render("~/plugins/c3")
    @Scripts.Render("~/plugins/select2")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">
    $(document).ready(function () {

        // entrada analogica
        var dataX = [];
        var labelX = [];
        var dataMedia = [];
        var dataMinima = [];
        var dataMaxima = [];

        // copia valores
        var Media = @Html.Raw(Json.Encode(@ViewBag.Media));
        var Minima = @Html.Raw(Json.Encode(@ViewBag.Minima));
        var Maxima = @Html.Raw(Json.Encode(@ViewBag.Maxima));
        var Dias = @Html.Raw(Json.Encode(@ViewBag.Dias));
        var NomeGrandeza = @Html.Raw(Json.Encode(@ViewBag.NomeGrandeza));
        var UnidadeGrandeza = @Html.Raw(Json.Encode(@ViewBag.UnidadeGrandeza));

        var minimoValor = Math.ceil(@ViewBag.Valor_min);
        var maximoValor = Math.ceil(@ViewBag.Valor_max);

        dataX.push('x');
        dataMaxima.push(['Max']);
        dataMedia.push(['Score']);
        dataMinima.push(['Min']);

        for (i = 0; i < 26; i++)
        {
            // X
            dataX.push(Dias[i]);

            dataMedia.push([Media[i]]);
            dataMinima.push([Minima[i]]);
            dataMaxima.push([Maxima[i]]);
        }

        // valores label X
        labelX.push(Dias[1]);
        for (i = 3; i <= 24; i+=3)
        {
            labelX.push(Dias[i]);
        }

        var Data = [dataX,dataMaxima,dataMedia,dataMinima];

        c3.generate({
            bindto: '#grafico-ea',
            size: {
                height: 300
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    Max: 'area',
                    Score: 'line',
                    Min: 'area',
                },
                colors: {
                    Max: 'rgb(215, 232, 248)',
                    Score: 'rgb(120, 178, 235)',
                    Min: '#ffffff'
                }
            },
            point: {
                r:2,
                sensitivity: 1000
            },
            legend: {
                hide: true
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*28
                },
                y:  {
                    min: minimoValor,
                    max: maximoValor,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {
                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;
                    for (i = 0; i < d.length; i++) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        if (! text) {

                            // split string and create array.
                            var arr = Dias[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // horas e minutos
                            var hours = data.getHours();
                            var minutes = data.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            title = hours + ':' + minutes;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1C84C6'>" + title + "</th></tr>" : "");
                        }

                        switch(i)
                        {
                            case 2:     // minimo
                                name = "Mínimo";
                                break;

                            case 1:     // medio
                                name = "Médio";
                                break;

                            case 0:     // maximo
                                name = "Máximo";
                                break;
                        }

                        value = valueFormat(d[i].value, d[i].ratio, d[i].id, d[i].index);
                        bgcolor = $$.levelColor ? $$.levelColor(d[i].value) : color(d[i].id);

                        text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                        text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                        text += "<td class='value'>" + d[i].value.toFixed(2) + " " + UnidadeGrandeza + "</td>";
                        text += "</tr>";
                    }
                    return text + "</table>";
                }
            }
        });

        d3.selection.prototype.moveToFront = function () {
            return this.each(function () {
                this.parentNode.appendChild(this);
            });
        };

        d3.select("svg g.c3-grid").moveToFront();

    });

</script>
}





