﻿@using System.Globalization

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoMedicao;
}

<div class="row wrapper border-bottom white-bg page-heading">
    <div class="row">
        <div class="col-lg-12">
            <div class="superv-content">

                <div class="col-lg-3 col-md-12">
                    <div class="row">
                        <div class="col-lg-6 col-md-6">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual superv-ponta">
                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                                    <h5>@SmartEnergy.Resources.DashboardTexts.DemandaMaximaDia</h5>
                                    <h1>@ViewBag.Dem_Dia_DemMaxP <span>kW</span></h1>
                                    <h6>@ViewBag.Dem_Dia_DemMaxP_DataHora</h6>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual superv-fponta">
                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta</h4>
                                    <h5>@SmartEnergy.Resources.DashboardTexts.DemandaMaximaDia</h5>
                                    <h1>@ViewBag.Dem_Dia_DemMaxFP <span>kW</span></h1>
                                    <h6>@ViewBag.Dem_Dia_DemMaxFP_DataHora</h6>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="superv-tabela">
                                <table class="table no-margins" style="width:100%;">
                                    <thead>
                                        <tr>
                                            <th style="width:34%;"></th>
                                            <th class="superv-ponta" style="width:33%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                                            <th class="superv-fponta" style="width:33%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>@SmartEnergy.Resources.SupervisaoTexts.MesAtual<br /><small>(@ViewBag.MesAtual)</small></td>
                                            <td class="superv-ponta">@ViewBag.Dem_Mes_DemMaxP kW<br /><small>(@ViewBag.Dem_Mes_DemMaxP_DataHora)</small></td>
                                            <td class="superv-fponta">@ViewBag.Dem_Mes_DemMaxFP kW<br /><small>(@ViewBag.Dem_Mes_DemMaxFP_DataHora)</small></td>
                                        </tr>
                                        <tr>
                                            <td>@SmartEnergy.Resources.SupervisaoTexts.MesAnterior<br /><small>(@ViewBag.MesAnterior1)</small></td>
                                            <td class="superv-ponta">@ViewBag.Dem_MesAnt_DemMaxP kW<br /><small>(@ViewBag.Dem_MesAnt_DemMaxP_DataHora)</small></td>
                                            <td class="superv-fponta">@ViewBag.Dem_MesAnt_DemMaxFP kW<br /><small>(@ViewBag.Dem_MesAnt_DemMaxFP_DataHora)</small></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="col-lg-5 col-md-12">
                    <div class="row">
                        <div class="superv-grafico">
                            <div class="flot-chart">
                                <div id="demanda-chart"></div>
                            </div>
                            <div class='chart-legend'>
                                <div class='legend-scale-dem'>
                                    <ul class='legend-labels'>
                                        <li><span style='background:#1C84C6;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                                        <li><span style='background:#007f00;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                                        <li><span style='background:#ff0000;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-8">
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual superv-ponta">
                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                                    <h5>@SmartEnergy.Resources.DashboardTexts.ConsumoTotalDia</h5>
                                    <h1>@ViewBag.Cons_Dia_ConsumoP <span>@ViewBag.UnidadeConsumo</span></h1>
                                    <h6>@ViewBag.DiaAtual</h6>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual superv-fponta">
                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta</h4>
                                    <h5>@SmartEnergy.Resources.DashboardTexts.ConsumoTotalDia</h5>
                                    <h1>@ViewBag.Cons_Dia_ConsumoFP <span>@ViewBag.UnidadeConsumo</span></h1>
                                    <h6>@ViewBag.DiaAtual</h6>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="superv-tabela superv-energia">
                                <table class="table no-margins">
                                    <thead>
                                        <tr>
                                            <th style="width: 25%;"></th>
                                            <th class="superv-ponta" style="width: 25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                                            <th class="superv-fponta" style="width: 25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta</th>
                                            <th style="width: 25%;">@SmartEnergy.Resources.SupervisaoTexts.Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>@SmartEnergy.Resources.SupervisaoTexts.MesAtual Projetado<br /><small>(@ViewBag.MesAtual)</small></td>
                                            <td class="superv-ponta">@ViewBag.Cons_MesProj_ConsumoP @ViewBag.UnidadeConsumo<br /><small>&nbsp;</small></td>
                                            <td class="superv-fponta">@ViewBag.Cons_MesProj_ConsumoFP @ViewBag.UnidadeConsumo<br /><small>&nbsp;</small></td>
                                            <td>@ViewBag.Cons_MesProj_ConsumoTotal @ViewBag.UnidadeConsumo<br /><small>&nbsp;</small></td>
                                        </tr>
                                        <tr>
                                            <td>@SmartEnergy.Resources.SupervisaoTexts.MesAnterior<br /><small>(@ViewBag.MesAnterior1)</small></td>
                                            <td class="superv-ponta">@ViewBag.Cons_MesAnt_ConsumoP @ViewBag.UnidadeConsumo<br /><small>&nbsp;</small></td>
                                            <td class="superv-fponta">@ViewBag.Cons_MesAnt_ConsumoFP @ViewBag.UnidadeConsumo<br /><small>&nbsp;</small></td>
                                            <td>@ViewBag.Cons_MesAnt_ConsumoTotal @ViewBag.UnidadeConsumo<br /><small>&nbsp;</small></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<div class="wrapper wrapper-content">

    <div class="row">
        <div class="col-lg-2">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h4>Gateway</h4><br />

                    @{
                        int IDTipoAcesso = ViewBag._IDTipoAcesso;

                        if (IDTipoAcesso == 0 || IDTipoAcesso == 5)
                        {
                            <span>[@ViewBag._IDGateway] </span>
                        }
                    }
                    @ViewBag.GatewayNome
                </div>
            </div>
        </div>
        <div class="col-lg-2">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.SupervisaoTexts.ModeloVersao</h4><br />
                    @ViewBag.GatewayModelo
                </div>
            </div>
        </div>
        <div class="col-lg-2">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h4>Status</h4><br />
                    @ViewBag.GatewayStatus
                </div>
            </div>
        </div>
        <div class="col-lg-2">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.SupervisaoTexts.UltimaAtualizacao</h4><br />
                    @ViewBag.GatewayAtualizacao
                </div>
            </div>
        </div>
        <div class="col-lg-2">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.SupervisaoTexts.DataHoraEq</h4><br />
                    @ViewBag.GatewayDataEq
                </div>
            </div>
        </div>
        <div class="col-lg-2">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.SupervisaoTexts.Constante</h4><br />
                    @ViewBag.Constante
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title" style="min-height:564px;">
                <div class="panel-body">

                    <select class="select2_tipo form-control" id="TiposEvento" onchange="SelecionouTipo()">

                        @{
                            var icone_tipo = "fa-th-list";
                            int tipo = 0;

                            for (var i = 0; i < ViewBag.NumTiposEventos; i++)
                            {
                                tipo = ViewBag.listaTiposEventosDescricao[i].Tipo;

                                switch (tipo)
                                {
                                    case 0:
                                        icone_tipo = "fa-th-list";
                                        break;

                                    case 1:
                                        icone_tipo = "fa-bell";
                                        break;

                                    case 2:
                                        icone_tipo = "fa-gear";
                                        break;

                                    case 3:
                                        icone_tipo = "fa-power-off";
                                        break;

                                    case 4:
                                        icone_tipo = "fa-bolt";
                                        break;

                                    case 5:
                                        icone_tipo = "fa-sitemap";
                                        break;

                                    case 6:
                                        icone_tipo = "fa-sign-in";
                                        break;

                                    case 7:
                                        icone_tipo = "fa-sign-out";
                                        break;

                                    case 8:
                                        icone_tipo = "fa-upload";
                                        break;

                                    case 9:
                                        icone_tipo = "fa-clock-o";
                                        break;

                                    case 10:
                                        icone_tipo = "fa-user";
                                        break;
                                }

                                <option value=@tipo data-icon=@icone_tipo>&nbsp;&nbsp;@ViewBag.listaTiposEventosDescricao[i].Descricao</option>
                            }
                        }

                    </select>

                    <br /><br />

                    <table class="table table-striped table-bordered table-hover dataTables-eventos">
                        <thead>
                            <tr>
                                <th>@SmartEnergy.Resources.RelatoriosTexts.Data</th>
                                <th>Tipo</th>
                                <th></th>
                                <th>@SmartEnergy.Resources.RelatoriosTexts.Evento</th>
                            </tr>
                        </thead>
                        <tbody>

                            @{
                                var listaEventosDescricao = ViewBag.listaEventosDescricao;

                                if( listaEventosDescricao != null )
                                {
                                    foreach (var evento in ViewBag.listaEventosDescricao)
                                    {
                                        icone_tipo = "fa-th-list";
                                        tipo = evento.Tipo;

                                        switch (tipo)
                                        {
                                            case 0:
                                                icone_tipo = "fa-th-list";
                                                break;

                                            case 1:
                                                icone_tipo = "fa-bell";
                                                break;

                                            case 2:
                                                icone_tipo = "fa-gear";
                                                break;

                                            case 3:
                                                icone_tipo = "fa-power-off";
                                                break;

                                            case 4:
                                                icone_tipo = "fa-bolt";
                                                break;

                                            case 5:
                                                icone_tipo = "fa-sitemap";
                                                break;

                                            case 6:
                                                icone_tipo = "fa-sign-in";
                                                break;

                                            case 7:
                                                icone_tipo = "fa-sign-out";
                                                break;

                                            case 8:
                                                icone_tipo = "fa-upload";
                                                break;

                                            case 9:
                                                icone_tipo = "fa-clock-o";
                                                break;

                                            case 10:
                                                icone_tipo = "fa-user";
                                                break;
                                        }

                                        <tr>
                                            <td style="font-size:11px;">@evento.DataHora</td>
                                            <td>@evento.Tipo</td>
                                            <td style="text-align:center;"><i class="fa @icone_tipo" style="font-size:20px;"></i></td>
                                            <td style="font-size:11px;">@evento.Descricao</td>
                                        </tr>
                                    }
                                }                                    
                            }

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>


</div>

@section Styles {
    @Styles.Render("~/plugins/c3Styles")
    @Styles.Render("~/plugins/select2Styles")
    @Styles.Render("~/Supervisao.css")
}

@section Scripts {
    @Scripts.Render("~/plugins/d3")
    @Scripts.Render("~/plugins/c3")
    @Scripts.Render("~/plugins/select2")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">
    $(document).ready(function () {

        // demanda
        var dataX = [];
        var labelX = [];
        var dataContratoP = [];
        var dataContratoFPI = [];
        var dataContratoFPC = [];
        var dataToleranciaP = [];
        var dataToleranciaFPI = [];
        var dataToleranciaFPC = [];
        var dataDemanda = [];

        // copia valores
        var Demanda = @Html.Raw(Json.Encode(@ViewBag.Demanda));
        var Contrato = @Html.Raw(Json.Encode(@ViewBag.Contrato));
        var Tolerancia = @Html.Raw(Json.Encode(@ViewBag.Tolerancia));
        var Periodo = @Html.Raw(Json.Encode(@ViewBag.Periodo));
        var Dias_Demanda = @Html.Raw(Json.Encode(@ViewBag.Dias_Demanda));

        var maximoDem = @Html.Raw(Json.Encode(@ViewBag.DemMax));

        dataX.push('x');
        dataDemanda.push(['data1']);
        dataContratoFPC.push(['data2']);
        dataContratoFPI.push(['data3']);
        dataContratoP.push(['data4']);
        dataToleranciaFPC.push(['data5']);
        dataToleranciaFPI.push(['data6']);
        dataToleranciaP.push(['data7']);

        for (i = 0; i < 98; i++)
        {
            // caso nao tenha contrato, maximo vira contrato para termos indicacao de periodo
            if( Contrato[i] == 0 && Periodo[i] != 3 )
            {
                Contrato[i] = maximoDem;
            }

            // X
            dataX.push(Dias_Demanda[i]);

            // demanda
            dataDemanda.push([Demanda[i]]);

            if( Periodo[i] == 0 )
            {
                dataContratoFPC.push([0]);
                dataContratoFPI.push([0]);
                dataContratoP.push([Contrato[i]]);

                dataToleranciaFPC.push([0]);
                dataToleranciaFPI.push([0]);
                dataToleranciaP.push([Tolerancia[i]]);
            }
            else if( Periodo[i] == 1 )
            {
                dataContratoFPC.push([0]);
                dataContratoFPI.push([Contrato[i]]);
                dataContratoP.push([0]);

                dataToleranciaFPC.push([0]);
                dataToleranciaFPI.push([Tolerancia[i]]);
                dataToleranciaP.push([0]);
            }
            else if( Periodo[i] == 2 )
            {
                dataContratoFPC.push([Contrato[i]]);
                dataContratoFPI.push([0]);
                dataContratoP.push([0]);

                dataToleranciaFPC.push([Tolerancia[i]]);
                dataToleranciaFPI.push([0]);
                dataToleranciaP.push([0]);
            }
            else
            {
                dataContratoFPC.push([0]);
                dataContratoFPI.push([0]);
                dataContratoP.push([0]);

                dataToleranciaFPC.push([0]);
                dataToleranciaFPI.push([0]);
                dataToleranciaP.push([0]);
            }
        }

        // valores label X
        labelX.push(Dias_Demanda[1]);
        for (i = 12; i <= 96; i+=12)
        {
            labelX.push(Dias_Demanda[i]);
        }

        var Data = [dataX,dataDemanda,dataContratoFPC,dataContratoFPI,dataContratoP,dataToleranciaFPC,dataToleranciaFPI,dataToleranciaP];

        c3.generate({

            bindto: '#demanda-chart',
            size: {
                height: 300
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'area-step',
                    data2: 'area-step',
                    data3: 'area-step',
                    data4: 'area-step',
                    data5: 'area-step',
                    data6: 'area-step',
                    data7: 'area-step'
                },
                color: function (color, d) {
                    if (typeof d.index === 'undefined') { return color; }

                    if( Periodo[d.index] == 0 )
                        return '#FF0000';

                    if( Periodo[d.index] == 1 )
                        return '#007F00';

                    if( Periodo[d.index] == 2 )
                        return '#1C84C6';

                    return '#007F00';
                },
                colors: {
                    data1: '#001F00',
                    data2: '#1C84C6',
                    data3: '#007F00',
                    data4: '#FF0000',
                    data5: '#1C84C6',
                    data6: '#007F00',
                    data7: '#FF0000'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*6
                },
                y:  {
                    max: maximoDem,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoDem < 100)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.15 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < 4; i++) {

                        if (! text) {

                            // split string and create array.
                            var arr = Dias_Demanda[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // horas e minutos
                            var hours = data.getHours();
                            var minutes = data.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            title = hours + ':' + minutes;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // periodo
                        switch(Periodo[d[0].index])
                        {
                            case 0:
                                periodo = "Ponta";
                                bgcolor = '#FF0000'
                                break;

                            case 1:
                                periodo = "FPonta Ind";
                                bgcolor = '#007F00';
                                break;

                            case 2:
                                periodo = "FPonta Cap";
                                bgcolor = '#1C84C6';
                                break;

                            default:
                                periodo = "---";
                                bgcolor = '#000000';
                                break;
                        }

                        // nome
                        switch(i)
                        {
                            case 0:
                                name = 'Demanda Ativa';
                                unit = 'kW';
                                value = Demanda[d[0].index].toFixed(1);
                                break;

                            case 1:
                                name = 'Contrato';
                                unit = 'kW';
                                value = Contrato[d[1].index].toFixed(1);
                                break;

                            case 2:
                                name = 'Tolerância';
                                unit = 'kW';
                                value = Tolerancia[d[4].index].toFixed(1);
                                break;

                            case 3:
                                name = 'Periodo';
                                unit = '';
                                value = periodo;
                                break;
                        }

                        value = value.replace('.', ',');

                        // verifica se sem registro
                        if( Periodo[d[0].index] == 3 )
                        {
                            value = '---';
                        }

                        if(i==3)
                        {
                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'>" + name + "</td>";
                            text += "<td class='value'>" + value + "</td>";
                            text += "</tr>";
                        }
                        else
                        {
                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                            text += "<td class='value'>" + value + " " + unit + "</td>";
                            text += "</tr>";
                        }
                    }

                    return text + "</table>";
                }
            }
        });



        // eventos
        $('.dataTables-eventos').DataTable({
            "iDisplayLength": 9,
            dom: 'ftp',

            "aoColumnDefs": [
                        {
                            "aTargets": [1],
                            "bVisible": false,
                            "bSortable": true
                        },
                        {
                            "aTargets": [2],
                            "bVisible": true,
                            "bSortable": false
                        },
            ],
            "oSearch": {
                "bSmart": false,
                "bRegex": true,
                "sSearch": ""
            },
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "25%" },
            { sWidth: "5%" },
            { sWidth: "5%" },
            { sWidth: "80%" }
            ],
            'order': [0, 'desc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });


        function formatIcon(icon) {

            if (!icon.id) {
                return icon.text;
            }
            var originalOption = icon.element;
            var $icon = $('<i style="font-size:16px;" class="fa ' + $(originalOption).data('icon') + '"></i><span>' + icon.text + '</span>');

            return $icon;

        };

        $(".select2_tipo").select2({
            minimumResultsForSearch: Infinity,
            width: "100%",
            templateResult: formatIcon,
            templateSelection: formatIcon
        });

    });

    function SelecionouTipo() {

        // pega tipo selecionado
        //var tipo = document.getElementById("TiposEvento").selectedIndex;
        var tipo = $("#TiposEvento").val();

        // verifica se todos
        if (tipo == 0)
        {
            // procura na tabela
            $('.dataTables-eventos').DataTable().column(1).search("").draw();
        }
        else
        {
            // procura na tabela
            $('.dataTables-eventos').DataTable().column(1).search(tipo).draw();
        }
    }

</script>
}





