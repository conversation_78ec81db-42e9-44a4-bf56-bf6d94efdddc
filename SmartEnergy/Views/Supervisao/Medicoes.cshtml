﻿@model IEnumerable<SmartEnergyLib.SQL.MedicoesSupervDominio>

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.Funcoes

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoMedicoes;
}

<head>
    <title>Medições</title>
    <style>

        a:link {
            color: #676A6C;
        }

        a:hover {
            color: #AFB0B1;
        }

    </style>
</head>

@{
    int IDTipoAcesso = ViewBag._IDTipoAcesso;
    int IDConsultor = ViewBag._IDConsultor;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.SupervisaoTexts.Medicoes</h4>
                </div>
                <div class="panel-body">

                    <table class="table table-striped table-bordered table-hover dataTables-medicoes">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th class="some_minidesktop">@Funcoes_SmartEnergy.Nome_GrupoUnidades(ViewBag.Nome_GrupoUnidades, SmartEnergy.Resources.SupervisaoTexts.Grupos)</th>
                                <th class="some_minidesktop">@Funcoes_SmartEnergy.Nome_Unidades(ViewBag.Nome_Unidades, SmartEnergy.Resources.SupervisaoTexts.Unidades)</th>
                                <th class="some_desktop"></th>
                                <th>@SmartEnergy.Resources.SupervisaoTexts.Medicoes</th>
                                <th class="some_desktop">@SmartEnergy.Resources.SupervisaoTexts.Descricao</th>
                                <th>@SmartEnergy.Resources.SupervisaoTexts.Valor</th>
                                <th class="some_minidesktop">@SmartEnergy.Resources.SupervisaoTexts.DataHora</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>

                            @foreach (MedicoesSupervDominio medicao in Model)
                            {
                                string DataHora = "-";
                                string DataHora_Sort = "20000101000000";

                                if (medicao.DataHoraValor != null)
                                {
                                    DataHora = String.Format("{0:d} {1:HH:mm}", medicao.DataHoraValor, medicao.DataHoraValor);
                                    DataHora_Sort = String.Format("{0:yyyyMMddHHmmss}", medicao.DataHoraValor);

                                    if (medicao.DataHoraValor.Year == 2000)
                                    {
                                        DataHora = "---";
                                    }
                                }

                                // consumo
                                string UnidadeConsumo = "Wh";
                                if (medicao.IDTipoUnidadePotencia == 1)
                                {
                                    UnidadeConsumo = "kWh";
                                }


                                string logo;
                                string tipomedicao = "0";
                                string caminho_superv = "Medicao_Energia";
                                string caminho_relat = "Relat_Dem_Ativa";
                                string caminho_financas = "Fatura_Energia";
                                string descricao = "-";
                                string valor = "-";

                                switch (medicao.IDTipoMedicao)
                                {
                                    case TIPO_MEDICAO.ENERGIA:

                                        caminho_superv = "Medicao_Energia";
                                        caminho_relat = "Relat_Dem_Ativa";
                                        caminho_financas = "Fatura_Energia";
                                        descricao = SmartEnergy.Resources.SupervisaoTexts.Consumo;
                                        valor = String.Format("{0:#,##0} {1}", medicao.UltimoValor, UnidadeConsumo);
                                        tipomedicao = "0";

                                        // verifica se cliente com meta
                                        if (medicao.IDTipoSupervisao == 1)
                                        {
                                            caminho_superv = "Medicao_Energia_PDA";
                                        }

                                        // verifica se gateway Arquivos SCDE
                                        if (medicao.IDTipoGateway == TIPO_GATEWAY.SCDE)
                                        {
                                            caminho_superv = "Medicao_Energia_SCDE";
                                        }

                                        break;

                                    case TIPO_MEDICAO.ENERGIA_FORMULA:

                                        caminho_superv = "Medicao_Energia_Formula";
                                        caminho_relat = "Relat_Dem_Ativa";
                                        caminho_financas = "Fatura_Energia";
                                        descricao = SmartEnergy.Resources.SupervisaoTexts.Consumo;
                                        valor = String.Format("{0:#,##0} {1}", medicao.UltimoValor, UnidadeConsumo);
                                        tipomedicao = "1";

                                        // verifica se cliente com meta
                                        if (medicao.IDTipoSupervisao == 1)
                                        {
                                            caminho_superv = "Medicao_Energia_PDA_Formula";
                                        }

                                        // verifica se CPFL
                                        if (IDConsultor == CLIENTES_ESPECIAIS.CPFL)
                                        {
                                            caminho_superv = "Medicao_Energia_SCDE_Formula";
                                        }

                                        break;

                                    case TIPO_MEDICAO.UTILIDADES:

                                        caminho_superv = "Medicao_Utilidades";
                                        caminho_relat = "Relat_Utilidades";
                                        caminho_financas = "Fatura_Utilidades";
                                        descricao = SmartEnergy.Resources.SupervisaoTexts.Consumo;
                                        valor = String.Format("{0:#,##0.00} {1}", medicao.UltimoValor, medicao.UnidadeGrandeza);
                                        tipomedicao = "2";

                                        if (medicao.IDIcone == 2)
                                        {
                                            descricao = "Tempo";
                                            valor = String.Format("{0:#,##0} {1}", medicao.UltimoValor, medicao.UnidadeGrandeza);
                                        }

                                        break;

                                    case TIPO_MEDICAO.UTILIDADES_FORMULA:

                                        caminho_superv = "Medicao_Utilidades_Formula";
                                        caminho_relat = "Relat_Utilidades";
                                        caminho_financas = "Fatura_Utilidades";
                                        descricao = SmartEnergy.Resources.SupervisaoTexts.Consumo;
                                        valor = String.Format("{0:#,##0.00} {1}", medicao.UltimoValor, medicao.UnidadeGrandeza);
                                        tipomedicao = "3";

                                        if (medicao.IDIcone == 2)
                                        {
                                            descricao = "Tempo";
                                            valor = String.Format("{0:#,##0} {1}", medicao.UltimoValor, medicao.UnidadeGrandeza);
                                        }

                                        break;

                                    case TIPO_MEDICAO.ENTRADA_ANALOGICA:

                                        caminho_superv = "Medicao_EA";
                                        caminho_relat = "Relat_EA";
                                        caminho_financas = "";
                                        descricao = medicao.NomeGrandeza + " (" + SmartEnergy.Resources.SupervisaoTexts.Medio + ")";
                                        valor = String.Format("{0:#,##0.00} {1}", medicao.UltimoValor, medicao.UnidadeGrandeza);
                                        tipomedicao = "5";

                                        break;

                                    case TIPO_MEDICAO.EA_FORMULA:

                                        caminho_superv = "Medicao_EA_Formula";
                                        caminho_relat = "Relat_EA";
                                        caminho_financas = "";
                                        descricao = medicao.NomeGrandeza + " (" + SmartEnergy.Resources.SupervisaoTexts.Medio + ")";
                                        valor = String.Format("{0:#,##0.00} {1}", medicao.UltimoValor, medicao.UnidadeGrandeza);
                                        tipomedicao = "6";

                                        break;

                                    case TIPO_MEDICAO.CICLOMETRO:

                                        caminho_superv = "Medicao_Ciclometro";
                                        caminho_relat = "Relat_Ciclometro";
                                        caminho_financas = "";
                                        descricao = SmartEnergy.Resources.SupervisaoTexts.Ciclometro;
                                        valor = String.Format("{0:00000000} {1}", medicao.UltimoValor, medicao.UnidadeGrandeza);
                                        tipomedicao = "4";

                                        break;

                                    case TIPO_MEDICAO.METEOROLOGIA:

                                        caminho_superv = "Medicao_Meteorologia";
                                        caminho_relat = "Relat_Meteorologia";
                                        caminho_financas = "";
                                        descricao = SmartEnergy.Resources.SupervisaoTexts.Temperatura;
                                        valor = String.Format("{0:#,##0} °C", medicao.UltimoValor);
                                        tipomedicao = "7";

                                        break;

                                }

                                string link_superv = "/Supervisao/" + caminho_superv + "?IDCliente=" + @medicao.IDCliente.ToString() + "&IDMedicao=" + @medicao.IDMedicao.ToString();

                            <tr style="cursor: pointer;">
                                <td onclick="window.location.href='@(link_superv)'">@medicao.IDMedicao<br />&nbsp;</td>
                                <td class="some_minidesktop" onclick="window.location.href='@(link_superv)'">@medicao.NomeGrupoUnidades</td>
                                <td class="some_minidesktop" onclick="window.location.href='@(link_superv)'">@medicao.NomeUnidade</td>

                                @{
                                    // energia
                                    logo = "/Imagens/icone_lampada24.png";

                                    // energia formula
                                    if (medicao.IDTipoMedicao == TIPO_MEDICAO.ENERGIA_FORMULA)
                                    {
                                        logo = "/Imagens/icone_soma24.png";
                                    }

                                    // utilidades
                                    if (medicao.IDTipoMedicao == TIPO_MEDICAO.UTILIDADES || medicao.IDTipoMedicao == TIPO_MEDICAO.UTILIDADES_FORMULA)
                                    {
                                        if (medicao.IDIcone == 0)
                                        {
                                            logo = "/Imagens/icone_gota24.png";
                                        }

                                        if (medicao.IDIcone == 1)
                                        {
                                            logo = "/Imagens/icone_fogo24.png";
                                        }

                                        if (medicao.IDIcone == 2)
                                        {
                                            logo = "/Imagens/icone_relogio24.png";
                                        }
                                    }

                                    // grandezes genericas
                                    if (medicao.IDTipoMedicao == TIPO_MEDICAO.ENTRADA_ANALOGICA || medicao.IDTipoMedicao == TIPO_MEDICAO.EA_FORMULA)
                                    {
                                        logo = "/Imagens/icone_gauge24.png";
                                    }

                                    // ciclometro
                                    if (medicao.IDTipoMedicao == TIPO_MEDICAO.CICLOMETRO)
                                    {
                                        logo = "/Imagens/icone_ciclo24.png";
                                    }

                                    // estacao metereologica
                                    if (medicao.IDTipoMedicao == TIPO_MEDICAO.METEOROLOGIA)
                                    {
                                        logo = "/Imagens/icone_meteorologia24.png";
                                    }
                                }

                                <td class="some_desktop" onclick="window.location.href='@(link_superv)'" style="text-align:center;"><span style="display:none;">@tipomedicao</span><img src=@logo /></td>
                                <td onclick="window.location.href='@(link_superv)'">@medicao.NomeMedicao</td>

                                <td class="some_desktop" onclick="window.location.href='@(link_superv)'">@descricao</td>
                                <td onclick="window.location.href='@(link_superv)'">@valor</td>
                                <td class="some_minidesktop" onclick="window.location.href='@(link_superv)'"><span style="display:none;">@DataHora_Sort</span>@DataHora</td>

                                <td class="link_preto">
                                    <a href='@("/Supervisao/" + caminho_superv + "?IDCliente=" + @medicao.IDCliente.ToString() + "&IDMedicao=" + @medicao.IDMedicao.ToString())' title="@SmartEnergy.Resources.MenuTexts.MenuLateralSupervisao">
                                        <i class="fa fa-desktop icones"></i>
                                    </a>
                                    <a href='@("/Relatorios/" + caminho_relat + "?IDCliente=" + @medicao.IDCliente.ToString() + "&IDMedicao=" + @medicao.IDMedicao.ToString())' title="@SmartEnergy.Resources.MenuTexts.MenuLateralRelatorios">
                                        <i class="fa fa-bar-chart icones"></i>
                                    </a>
                                    <a onclick="ObservacoesMedicao(event, @medicao.IDMedicao);" href='#' title="Observações">
                                        <i class="fa fa-asterisk icones"></i>
                                    </a>

                                    @{
                                        bool View_Financas = (ViewBag.View_Financas == 1) ? true : false;

                                        if (medicao.IDTipoMedicao != TIPO_MEDICAO.ENTRADA_ANALOGICA && medicao.IDTipoMedicao != TIPO_MEDICAO.EA_FORMULA && medicao.IDTipoMedicao != TIPO_MEDICAO.CICLOMETRO && medicao.IDTipoMedicao != TIPO_MEDICAO.METEOROLOGIA)
                                        {
                                            // verifica se usuário pode ver finanças
                                            if (View_Financas)
                                            {
                                                <a href='@("/Financas/" + caminho_financas + "?IDCliente=" + @medicao.IDCliente.ToString() + "&IDMedicao=" + @medicao.IDMedicao.ToString() + "&TipoFatura=0")' title="@SmartEnergy.Resources.MenuTexts.MenuLateralFinancas">
                                                    <i class="fa fa-pie-chart icones"></i>
                                                </a>
                                            }
                                        }
                                    }

                                    @{
                                        bool View_Configuracao = (ViewBag.View_Configuracao == 1) ? true : false;

                                        // verifica se usuário pode ver configuração
                                        if (View_Configuracao)
                                        {
                                            <a href='@("/Configuracao/Medicoes_Editar?IDMedicao=" + @medicao.IDMedicao.ToString())' title="@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracao">
                                                <i class="fa fa-gear icones"></i>
                                            </a>
                                        }
                                    }
                                </td>
                            </tr>
                            }

                        </tbody>
                    </table>

                    @Html.Hidden("IDMedSelecionado", 0)
                    @Html.Hidden("IDObservacaoSelecionado", 0)

                    <div class="modal inmodal animated fadeIn" id="ModalObservacoes" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-editar-header">
                                    <i class="fa fa-check-square-o modal-editar-title"></i><span class="modal-editar-title">&nbsp;&nbsp;@SmartEnergy.Resources.SupervisaoTexts.Observacoes</span>

                                    @if (!isUser.isOperador(IDTipoAcesso) && IDTipoAcesso != 10)
                                    {
                                        <div class="pull-right dashboard-tools link_branco" style="margin-top:-2px;">
                                            <h4>
                                                <a href="#" onclick="javascript:ObservacaoMedicao(event, 0, 0);" id="BotaoAdicionar" title="@SmartEnergy.Resources.ComumTexts.BotaoInserir"><i class="fa fa-plus"></i></a>
                                            </h4>
                                        </div>
                                    }

                                </div>
                                <div class="modal-body">

                                    <div id="Observacoes_resultado" min-height:500px;">
                                        @Html.Partial("_ObservacoesMedicao")
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-default" onclick="FecharObservacoesMedicao();">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal inmodal animated fadeIn" id="ModalObservacao" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-editar-header">
                                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only modal-tools-header">Close</span></button>
                                    <i class="fa fa-check-square-o modal-editar-title"></i><span class="modal-editar-title">&nbsp;&nbsp;@SmartEnergy.Resources.SupervisaoTexts.Observacao</span>
                                </div>
                                <div class="modal-body">

                                    <div id="Observacao_resultado" min-height:500px;">
                                        @Html.Partial("_ObservacaoMedicao")
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>

                                    @if (!isUser.isOperador(IDTipoAcesso) && IDTipoAcesso != 10)
                                    {
                                        <button type="button" class="btn btn-primary" onclick="SalvarObservacaoMedicao();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/datetimePickerStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/plugins/iCheck")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/declaracoes/Traducao-en.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "es-AR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
        <script type="text/javascript" language="javascript" src="~/Scripts/declaracoes/Traducao-es-AR.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
        <script type="text/javascript" language="javascript" src="~/Scripts/declaracoes/Traducao-pt-br.js"></script>
    }

    <script type="text/javascript">
    $(document).ready(function () {

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {

                var x = a;

                if (a == "-")
                {
                    x = "-1";
                }
                else
                {
                    x = a.replace(".", "");
                    x = x.replace(",", ".");
                }

                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-medicoes').DataTable({
            "iDisplayLength": 12,
            dom: 'Bftp',
            buttons: [
                {
                    extend: 'excelHtml5',
                    filename: 'Supervisão',
                    title: 'Supervisão',
                    text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-excel-o" data-toggle="tooltip" data-placement="left" title="Excel"></span>',
                    className: 'btn btn-default btn-xs'
                },
                {
                    extend: 'pdfHtml5',
                    filename: 'Supervisão',
                    title: 'Supervisão',
                    text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-pdf-o" data-toggle="tooltip" data-placement="left" title="PDF"></span>',
                    className: 'btn btn-default btn-xs'
                },
            ],

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "numero" },
                        { "aTargets": [1], "sType": "portugues" },
                        { "aTargets": [2], "sType": "portugues" },
                        { "aTargets": [3], "sType": "portugues" },
                        { "aTargets": [4], "sType": "portugues" },
                        { "aTargets": [5], "sType": "portugues" },
                        { "aTargets": [6], "sType": "numero", "bSearchable": false, },
                        { "aTargets": [7], "sType": "portugues" },
                        { "aTargets": [8], "bVisible": true, "bSortable": false, "bSearchable": false, },
            ],
            "aoColumns": [
            { sWidth: "5%" },
            { sWidth: "12%" },
            { sWidth: "12%" },
            { sWidth: "3%" },
            { sWidth: "16%" },
            { sWidth: "10%" },
            { sWidth: "10%" },
            { sWidth: "10%" },
            { sWidth: "10%" }
            ],
            "order": [[4, "asc"], [1, "asc"], [2, "asc"]],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });
    });

    function ObservacoesMedicao(e, IDMedicao) {

        e.stopPropagation();

        // apresenta modal
        $("#ModalObservacoes").modal("show");

        // aguarde
        $('#Observacoes_resultado').css("display", "none");

        // IDMedicao
        document.getElementById("IDMedSelecionado").value = IDMedicao;

        $.ajax(
        {
            type: 'GET',
            url: '/Supervisao/_ObservacoesMedicao',
            dataType: 'html',
            data: {"IDMedicao" : IDMedicao},
            cache: false,
            async: true,
            success: function (data) {

                $('#Observacoes_resultado').css("display", "block");
                $('#Observacoes_resultado').html(data);
            }
        });
    }

    function FecharObservacoesMedicao() {

        // fecha modal
        $("#ModalObservacoes").modal("hide");

        // atualiza pagina
        location.reload();
    }

    function ObservacaoMedicao(e, IDMedicao, IDObservacao) {

        e.stopPropagation();

        // apresenta modal
        $("#ModalObservacao").modal("show");

        // aguarde
        $('#Observacao_resultado').css("display", "none");

        // IDMedicao e IDObservacao
        if (IDMedicao == 0)
        {
            // IDMedicao
            IDMedicao = document.getElementById("IDMedSelecionado").value;
        }
        else
        {
            document.getElementById("IDMedSelecionado").value = IDMedicao;
        }

        document.getElementById("IDObservacaoSelecionado").value = IDObservacao;

        $.ajax(
        {
            type: 'GET',
            url: '/Supervisao/_ObservacaoMedicao',
            dataType: 'html',
            data: { "IDMedicao": IDMedicao, "IDObservacao": IDObservacao },
            cache: false,
            async: true,
            success: function (data) {

                $('#Observacao_resultado').css("display", "block");
                $('#Observacao_resultado').html(data);
            }
        });
    }

    function SalvarObservacaoMedicao() {

        // IDObservacao
        var IDObservacao = document.getElementById("IDObservacaoSelecionado").value;

        // IDMedicao
        var IDMedicao = document.getElementById("IDMedSelecionado").value;

        // IDTag
        var IDTag = $("#tags").val();

        // data
        var data_relat = document.querySelector('[name="data_relat"]').value;

        // cliente visualiza
        var ClienteVisualizaObj = document.getElementsByName('ClienteVisualiza');
        var ClienteVisualiza = ClienteVisualizaObj[0].checked;

        // Observacao
        var ObservacaoTexto = document.getElementById("ObservacaoTexto").value;

        // salvar
        $.ajax(
        {
            type: 'GET',
            url: '/Supervisao/ObservacaoMedicao_Salvar',
            data: { 'IDObservacao': IDObservacao, 'IDMedicao': IDMedicao, 'IDTag': IDTag, 'ClienteVisualiza': ClienteVisualiza, 'ObservacaoData': data_relat, 'ObservacaoTexto': ObservacaoTexto },
            contentType: 'application/html',
            dataType: 'html',
            cache: false,
            async: true,
            success: function (data) {

                setTimeout(function () {

                    $.ajax(
                    {
                        type: 'GET',
                        url: '/Supervisao/_ObservacoesMedicao',
                        dataType: 'html',
                        data: { "IDMedicao": IDMedicao },
                        cache: false,
                        async: true,
                        success: function (data) {

                            $('#Observacoes_resultado').css("display", "block");
                            $('#Observacoes_resultado').html(data);
                        }
                    });

                }, 100);
            },
            error: function (response) {

            }
        });
    }

    function ResolvidoObservacaoMedicao(IDObservacao) {
        event.stopPropagation();

        // IDMedicao
        var IDMedicao = document.getElementById("IDMedSelecionado").value;

        // resolvido
        $.ajax(
        {
            type: 'GET',
            url: '/Supervisao/ObservacaoMedicao_Resolvido',
            data: { 'IDObservacao': IDObservacao },
            contentType: 'application/html',
            dataType: 'html',
            cache: false,
            async: true,
            success: function (data) {

                setTimeout(function () {

                    $.ajax(
                    {
                        type: 'GET',
                        url: '/Supervisao/_ObservacoesMedicao',
                        dataType: 'html',
                        data: { "IDMedicao": IDMedicao },
                        cache: false,
                        async: true,
                        success: function (data) {

                            $('#Observacoes_resultado').css("display", "block");
                            $('#Observacoes_resultado').html(data);
                        }
                    });

                }, 100);
            },
            error: function (response) {

            }
        });
    };

    function ExcluirObservacaoMedicao(IDObservacao, DataTexto) {
        event.stopPropagation();

        // IDMedicao
        var IDMedicao = document.getElementById("IDMedSelecionado").value;

        // titulo
        titulo = DESEJA_EXCLUIR + "<br/>" + DataTexto;

        swal({
            html: true,
            title: titulo,
            text: NAO_PODERA_DEFAZER,
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: EXCLUIR,
            cancelButtonText: CANCELAR,
            closeOnConfirm: true
        }, function () {

            // excluir
            $.ajax(
            {
                type: 'GET',
                url: '/Supervisao/ObservacaoMedicao_Excluir',
                data: { 'IDObservacao': IDObservacao },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        $.ajax(
                        {
                            type: 'GET',
                            url: '/Supervisao/_ObservacoesMedicao',
                            dataType: 'html',
                            data: { "IDMedicao": IDMedicao },
                            cache: false,
                            async: true,
                            success: function (data) {

                                $('#Observacoes_resultado').css("display", "block");
                                $('#Observacoes_resultado').html(data);
                            }
                        });

                    }, 100);
                },
                error: function (response) {

                }
            });
        });
    };

    </script>

}


