﻿@using System.Globalization

@{
    ViewBag.Title = "Gateway";
}

<head>
    <title>Gateways</title>
    <style>

        a:link {
            color: #676A6C;
        }

        a:hover {
            color: #AFB0B1;
        }

        .tooltip-inner {
            max-width: 350px;
            /* If max-width does not work, try using width instead */
            width: 350px;
            white-space: pre-wrap;
            text-align: left;
        }
    </style>
</head>

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="panel panel-title">
                                        <div class="panel-heading">
                                            <br />
                                            <h4>Histórico dos Envios de Arquivos da Gateway</h4><br />
                                            <br />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-3">
                                    <div class="panel panel-success">
                                        <div class="panel-heading">
                                            <h4>ID Gateway</h4><br />
                                            @ViewBag.GatewayID
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-9">
                                    <div class="panel panel-success">
                                        <div class="panel-heading">
                                            <h4>Gateway</h4><br />
                                            @ViewBag.GatewayNome
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div id="sinal-chart"></div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-6">

                            <table id="dataTables-gateways" class="table table-bordered table-hover dataTables-gateways">
                                <thead>
                                    <tr>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.UltimaAtualizacao</th>
                                        <th>IP</th>
                                        <th><i class="fa fa-signal icones"></i> @SmartEnergy.Resources.SupervisaoTexts.Sinal</th>
                                    </tr>
                                </thead>
                                <tbody>

                                    @{
                                        var listaSup = ViewBag.ListaSup;
                                    }

                                    @foreach (var SUP in listaSup)
                                    {
                                        // copia data e hora
                                        var DataHora = String.Format("{0:G}", SUP.DataHora);
                                        var DataHora_Sort = String.Format("{0:yyyyMMddHHmmss}", SUP.DataHora);

                                        <tr>
                                            <td><span style="display:none;">@DataHora_Sort</span>@DataHora</td>
                                            <td>@SUP.IP</td>

                                            @{
                                        int sinal = SUP.Sinal;
                                        string SinalTexto = "Sinal Fraco";
                                        string SinalCor = "red";
                                        string sinalStr = "-";

                                        if (sinal >= 38 && sinal < 50)
                                        {
                                            SinalTexto = "Sinal Médio Fraco";
                                            SinalCor = "orange";
                                        }

                                        if (sinal >= 50 && sinal < 68)
                                        {
                                            SinalTexto = "Sinal Médio";
                                            SinalCor = "orange";
                                        }

                                        if (sinal >= 68 && sinal <= 100)
                                        {
                                            SinalTexto = "Sinal Forte";
                                            SinalCor = "green";
                                        }

                                        if (sinal >= 100)
                                        {
                                            sinal = 999;
                                        }

                                        if (sinal > 0)
                                        {
                                            sinalStr = string.Format("{0}%", SUP.Sinal);
                                        }
                                            }

                                            <td>
                                                <span style="display:none;">@string.Format("{000}", sinal)</span>
                                                <span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="@SinalTexto"><font color="@SinalCor"><b>@sinalStr</b></font></span>
                                            </td>

                                        </tr>
                                    }

                                </tbody>
                            </table>
                        </div>

                        <div class="col-lg-6">

                            <table id="dataTables-eventos" class="table table-bordered table-hover dataTables-eventos">
                                <thead>
                                    <tr>
                                        <th>@SmartEnergy.Resources.RelatoriosTexts.Data</th>
                                        <th>@SmartEnergy.Resources.RelatoriosTexts.Eventos</th>
                                    </tr>
                                </thead>
                                <tbody>

                                    @{
                                        var listaEventosDescricao = ViewBag.listaEventosDescricao;
                                    }

                                    @foreach (var EventoDescricao in listaEventosDescricao)
                                    {
                                        <tr>
                                            <td><span style="display:none;">@EventoDescricao.DataHora_Sort</span>@EventoDescricao.DataHora</td>
                                            <td>@EventoDescricao.Descricao</td>
                                        </tr>
                                    }

                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/c3Styles")
}

@section Scripts {
    @Scripts.Render("~/plugins/d3")
    @Scripts.Render("~/plugins/c3")
    @Scripts.Render("~/plugins/dataTables")
    @Scripts.Render("~/plugins/jqueryui")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }

    <script type="text/javascript">

    $(document).ready(function () {

        // sinal
        var labelX = [];
        var dataX1 = [];
        var dataSinal = [];
        var dataX2 = [];
        var dataEvento = [];

        // copia valores
        var Sinal = @Html.Raw(Json.Encode(@ViewBag.Sinal));
        var Eventos = @Html.Raw(Json.Encode(@ViewBag.Eventos));
        var IP = @Html.Raw(Json.Encode(@ViewBag.IP));
        var DataHoraSinal = @Html.Raw(Json.Encode(@ViewBag.DataHoraSinal));
        var DataHoraEvento = @Html.Raw(Json.Encode(@ViewBag.DataHoraEvento));

        var numRegistrosSinal = Math.ceil(@ViewBag.numRegistrosSinal);
        var numRegistrosEvento = Math.ceil(@ViewBag.numRegistrosEvento);

        dataX1.push(['x1']);
        dataSinal.push(['data1']);

        for (i = 0; i < numRegistrosSinal; i++)
        {
            // X1
            dataX1.push(DataHoraSinal[i]);
            dataSinal.push([Sinal[i]]);
        }

        dataX2.push(['x2']);
        dataEvento.push(['data2']);

        for (i = 0; i < numRegistrosEvento; i++)
        {
            // X2
            dataX2.push(DataHoraEvento[i]);
            dataEvento.push([100]);
        }

        // valores label X
        var arr = DataHoraSinal[0].split(/-|\s|:/);
        var datahora_ini = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

        arr = DataHoraSinal[numRegistrosSinal-1].split(/-|\s|:/);
        var datahora_fim = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

        var timeDiff = Math.abs(datahora_fim.getTime() - datahora_ini.getTime());
        var diffHours = Math.ceil(timeDiff / (1000 * 3600)); 

        var pulo = Math.ceil(diffHours / 10);

        var datahora = datahora_ini;

        for (i = 0; i < 10; i++)
        {
            labelX.push(new Date(datahora));
            datahora.setHours(datahora.getHours() + pulo);
        }

        var Data = [dataX1,dataSinal,dataX2,dataEvento];

        var chart = c3.generate({

            bindto: '#sinal-chart',
            size: {
                height: 220
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 10,
                left: 43
            },
            data: {
                xs: {
                    data1: 'x1',
                    data2: 'x2',
                },
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'line',
                    data2: 'scatter',
                },
                colors: {
                    data1: '#0000FF',
                    data2: '#000000',
                },
            },
            point: {
                show: false
            },
            legend: {
                hide: true
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: '%d/%m/%Y'
/*                        format: function (x1) {

                            // data e hora
                            var days = x1.getDate();
                            var months = x1.getMonth()+1;
                            var years = x1.getFullYear();
                            //var hours = x.getHours();
                            //var minutes = x.getMinutes();
                            //var seconds = x.getSeconds();

                            days = days < 10 ? '0'+days : days;
                            months = months < 10 ? '0'+months : months;
                            //hours = hours < 10 ? '0'+hours : hours;
                            //minutes = minutes < 10 ? '0'+minutes : minutes;
                            //seconds = seconds < 10 ? '0'+seconds : seconds;
                            strTime = days + "/" + months + "/" + years;

                            // retorna data
                            return strTime;
                        }*/
                    },
                },
                y:  {
                    max: 100,
                    min: 0,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                },
                y: {
                    lines: [{value:0}]
                }
            },

            tooltip: {
                format: {
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < d.length; i++) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        var indiceData = (nameFormat(d[i].name) == 'data1') ? 0 : 1;
                        var indice = d[0].index;

                        // titulo
                        if (! text) {

                            // split string and create array.
                            if( indiceData == 0 )
                            {
                                var arr = DataHoraSinal[indice].split(/-|\s|:/);
                            }

                            if( indiceData == 1 )
                            {
                                var arr = DataHoraEvento[indice].split(/-|\s|:/);
                            }

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // data e hora
                            var days = data.getDate();
                            var months = data.getMonth()+1;
                            var years = data.getFullYear();
                            var hours = data.getHours();
                            var minutes = data.getMinutes();
                            var seconds = data.getSeconds();

                            days = days < 10 ? '0'+days : days;
                            months = months < 10 ? '0'+months : months;
                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            seconds = seconds < 10 ? '0'+seconds : seconds;
                            title = days + "/" + months + "/" + years + "<br>" + hours + ':' + minutes + ':' + seconds;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        if( indiceData == 0 )
                        {
                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>IP</td>";
                            text += "<td class='value'>" + IP[indice] + "</td>";
                            text += "</tr>";

                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>Sinal</td>";
                            text += "<td class='value'>" + Sinal[indice].toFixed(0) + "%</td>";
                            text += "</tr>";
                        }

                        if( indiceData == 1 )
                        {
                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>Evento</td>";
                            text += "<td class='value'>" + Eventos[indice] + "</td>";
                            text += "</tr>";
                        }
                    }

                    return text + "</table>";
                }

            }
        });


        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-gateways').DataTable({
            "iDisplayLength": 12,
            dom: 'tp',

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "portugues" },
                        { "aTargets": [1], "sType": "portugues" },
                        { "aTargets": [2], "sType": "portugues" },
            ],
            "aoColumns": [
            { sWidth: "30%" },
            { sWidth: "35%" },
            { sWidth: "35%" },
            ],
            'order': [0, 'desc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }

            });

            $('.dataTables-eventos').DataTable({
                "iDisplayLength": 12,
                dom: 'tp',

                "bAutoWidth": false,
                "aoColumnDefs": [
                            { "aTargets": [0], "sType": "portugues" },
                            { "aTargets": [1], "sType": "portugues" },
                ],
                "aoColumns": [
                { sWidth: "30%" },
                { sWidth: "70%" },
                ],
                'order': [0, 'desc'],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }

            });

        });

        $(".bottom").tooltip({
            placement: "bottom"
        });

    </script>

}

