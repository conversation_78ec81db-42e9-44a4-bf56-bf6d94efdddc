﻿<div class="panel-heading">

    <div >
        <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoClientesOperacaoAssistida - @SmartEnergy.Resources.SupervisaoTexts.Medicoes</h4>
    </div>

    <div class="pull-right relat-navega">
        <h4>
            <a class="link_ano_menos">
                <i class="fa fa-angle-double-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.RelatoriosTexts.AnoAnterior></i>
            </a>
            <a class="link_mes_menos">
                <i class="fa fa-angle-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.RelatoriosTexts.MesAnterior></i>
            </a>
            <a class="link_mes_mais">
                <i class="fa fa-angle-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.RelatoriosTexts.MesSeguinte></i>
            </a>
            <a class="link_ano_mais">
                <i class="fa fa-angle-double-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.RelatoriosTexts.AnoSeguinte></i>
            </a>
            <a class="link_ultimo">
                <i class="fa fa-terminal" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.RelatoriosTexts.UltimaData></i>
            </a>
        </h4>
    </div>

</div>

<div class="panel-body">

    <div class="row">

        <div class="col-lg-4">
            <div class="form-group" id="data_operacao_assistida">
                <label class="control-label">&nbsp;Data</label>
                <div class="input-group">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span><input type="text" class="form-control" name="data_operacao_assistida" value=@ViewBag.DataAtual>
                </div>
            </div>
        </div>

        <div class="col-lg-8" style="margin-top:2px;">
            <label class="control-label">&nbsp;</label>
            <a class="btn btn-primary btn-sm btn-block" href="javascript:ExecutarOperacaoAssistida()" style="color:#ffffff; font-weight:bold;">@SmartEnergy.Resources.ComumTexts.BotaoAtualizar</a>
        </div>

    </div>
            
    <div style="display:block;">
        @Html.Partial("_Medicoes_OperacaoAssistida")
    </div>
</div>

<script type="text/javascript">

    $(document).ready(function () {

        $('#data_operacao_assistida').datetimepicker({
            locale: 'pt-BR',
            format: 'MM/YYYY',
            viewMode: "months",
            showTodayButton: true,
            allowInputToggle: true
        });

    });

    function Atualiza(navega) {

        // data nao utilizada
        var data = "01/01/2000";

        // aguarde
        $('#operacao_assistida_aguarde').css("display", "block");
        $('#operacao_assistida_resultado').css("display", "none");

        $.ajax(
        {
            type: 'GET',
            url: '/Supervisao/_Medicoes_OperacaoAssistida_Atualizar',
            dataType: 'html',
            data: { 'Navegacao': navega, 'Data': data },
            cache: false,
            async: true,
            success: function (data) {

                $('#operacao_assistida_aguarde').css("display", "none");
                $('#operacao_assistida_resultado').css("display", "block");

                $('#operacao_assistida').html(data);
            }
        });
    }

    $('#data_operacao_assistida').datetimepicker().on('dp.change', function (ev) {

        ExecutarOperacaoAssistida();

    });

    function ExecutarOperacaoAssistida() {

        // data
        var data_operacao_assistida = document.querySelector('[name="data_operacao_assistida"]').value;

        // aguarde
        $('#operacao_assistida_aguarde').css("display", "block");
        $('#operacao_assistida_resultado').css("display", "none");

        $.ajax(
        {
            type: 'GET',
            url: '/Supervisao/_Medicoes_OperacaoAssistida_Atualizar',
            dataType: 'html',
            data: { 'Navegacao': 100, 'Data': data_operacao_assistida },
            cache: false,
            async: true,
            success: function (data) {

                $('#operacao_assistida_aguarde').css("display", "none");
                $('#operacao_assistida_resultado').css("display", "block");

                $('#operacao_assistida').html(data);
            }
        });
    }

    $(".link_dia_menos").off().on("click", function (event) {
        event.stopPropagation();

        // dia anterior
        Atualiza(-1);
    });

    $(".link_dia_mais").off().on("click", function (event) {
        event.stopPropagation();

        // dia seguinte
        Atualiza(1);
    });

    $(".link_semana_menos").off().on("click", function (event) {
        event.stopPropagation();

        // semana anterior
        Atualiza(-2);
    });

    $(".link_semana_mais").off().on("click", function (event) {
        event.stopPropagation();

        // semana seguinte
        Atualiza(2);
    });

    $(".link_mes_menos").off().on("click", function (event) {
        event.stopPropagation();

        // mes anterior
        Atualiza(-3);
    });

    $(".link_mes_mais").off().on("click", function (event) {
        event.stopPropagation();

        // mes seguinte
        Atualiza(3);
    });

    $(".link_ano_menos").off().on("click", function (event) {
        event.stopPropagation();

        // ano anterior
        Atualiza(-4);
    });

    $(".link_ano_mais").off().on("click", function (event) {
        event.stopPropagation();

        // ano seguinte
        Atualiza(4);
    });

    $(".link_ultimo").off().on("click", function (event) {
        event.stopPropagation();

        // ultimo
        Atualiza(10);
    });


    function getCookie(c_name) {

        // Get name followed by anything except a semicolon
        var cookiestring = RegExp("" + c_name + "[^;]+").exec(document.cookie);

        // Return everything after the equal sign, or an empty string if the cookie name not found
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./, "") : "");
    }

    function setCookie(c_name, value, expiresecs) {
        var exdate = new Date();
        exdate.setTime(exdate.getTime() + ((expiresecs) ? expiresecs * 1000 : 0));

        document.cookie = c_name + "=" + escape(value) + ((expiresecs == null) ? "" : ";expires=" + exdate.toGMTString()) + ";path=/";
    }

</script>


