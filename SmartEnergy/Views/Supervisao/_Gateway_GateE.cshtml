﻿@using SmartEnergyLib.SQL

<style>

    .lightgray-bg {
        background-color: #999999;
    }

    .darkred-bg {
        background-color: #F00000;
        color: #ffffff;
    }

    .btn-Atualizar {
        background-color: #2f4050;
        border-color: #2f4050;
        color: #FFFFFF;
    }

        .btn-Atualizar:hover, .btn-Atualizar:focus, .btn-Atualizar:active, .btn-Atualizar.active, .open .dropdown-toggle.btn-Atualizar, .btn-Atualizar:active:focus, .btn-Atualizar:active:hover, .btn-Atualizar.active:hover, .btn-Atualizar.active:focus {
            background-color: #1e3040;
            border-color: #1e3040;
            color: #FFFFFF;
        }


    .led-box {
        height: 30px;
        width: 25%;
        margin: 10px 0;
        float: left;
    }

        .led-box p {
            font-size: 12px;
            text-align: center;
            margin: 1em;
        }

    .led-red {
        margin: 6px;
        margin-top: 10px;
        margin-bottom: 15px;
        width: 20px;
        height: 20px;
        background-color: #F00;
        border-radius: 20%;
    }

    .led-green {
        margin: 6px;
        margin-top: 10px;
        margin-bottom: 15px;
        width: 20px;
        height: 20px;
        background-color: #89FF00;
        border-radius: 20%;
    }

    .led-yellow {
        margin: 6px;
        margin-top: 10px;
        margin-bottom: 15px;
        width: 20px;
        height: 20px;
        background-color: #FF0;
        border-radius: 20%;
    }

    .led-gray {
        margin: 6px;
        margin-top: 10px;
        margin-bottom: 15px;
        width: 20px;
        height: 20px;
        background-color: #a0a0a0;
        border-radius: 20%;
    }

    .erroContainer {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        height: 350px;
    }

    .gatewayContainer {
        height: 65px;
    }

    .atualizarContainer {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 65px;
        color: #ffffff !important;
        font-weight: bold;
        font-size: 24px;
    }

    .statusGatewayContainer {
        position: relative;
        display: flex;
        justify-content: center;
        height: 120px;
    }

        .statusGatewayContainer span {
            position: absolute;
            top: 35%;
            font-size: 24px;
        }

    .statusGateway ul {
        position: absolute;
        top: 5%;
        margin-left: 10px;
        max-height: 8em;
        overflow-y: auto;
        width: 100%;
    }

    .statusInterfaceContainer {
        position: relative;
        display: flex;
        text-align: left;
        height: 120px;
    }

        .statusInterfaceContainer span {
            position: absolute;
            margin-left: 30px;
        }

        .statusInterfaceContainer ul {
            position: absolute;
            list-style: none;
            left: 0%;
            top: 5%;
            margin-left: 10px;
            width: 100%;
        }

    .statusRemotasContainer {
        position: relative;
        display: flex;
        justify-content: center;
        height: 150px;
    }

    .statuRemotasContainer span {
        position: absolute;
        top: 5%;
    }

    .statusRemotasSpan {
        position: absolute;
        margin-left: 30px;
    }

    .statusRemotas ul {
        position: absolute;
        top: 5%;
        margin-left: 10px;
        max-height: 10em;
        overflow-y: auto;
        width: 100%;
    }

    .statusBateriaContainer {
        height: 80px;
        text-align: right;
    }

        .statusBateriaContainer span {
            font-size: 56px;
        }

        .statusBateriaContainer i {
            font-size: 46px;
        }

    .statusMedicoesContainer {
        position: relative;
        display: flex;
        justify-content: center;
        height: 90px;
    }

    .statuMedicoesContainer span {
        position: absolute;
        top: 5%;
    }

    .statusMedicoesSpan {
        position: absolute;
        margin-left: 30px;
    }

    .statusMedicoes ul {
        position: absolute;
        top: 5%;
        margin-left: 10px;
        max-height: 10em;
        overflow-y: auto;
        width: 100%;
    }

    .verdeclaro-bg {
        background-color: #00aF00;
        color: #ffffff
    }

    .vermelho-bg {
        background-color: #ff0000;
        color: #ffffff
    }

    .tooltip-inner {
        max-width: 350px;
        width: 350px;
        white-space: pre-wrap;
        text-align: left;
    }

    .overlay_aguarde {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.45);
        z-index: 9999;
        color: white;
        display: inline-block;
    }

    .sk-spinner-wave div {
        background-color: #ffffff !important;
    }

    .spinner-aguarde {
        margin: 0;
        position: absolute;
        top: 50%;
        left: 50%;
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
    }
</style>

<div class="row">
    <div class="col-lg-3">
        <div class="panel panel-success">
            <div class="panel-heading gatewayContainer">
                <h4>ID Gateway</h4><br />
                @ViewBag.GatewayID @string.Format("[SE{0}]", ViewBag.GatewayNS)
            </div>
        </div>
    </div>
    <div class="col-lg-3">
        <div class="panel panel-success">
            <div class="panel-heading gatewayContainer">
                <h4>Gateway</h4><br />
                @ViewBag.GatewayNome
            </div>
        </div>
    </div>
    <div class="col-lg-3">
        <div class="panel panel-success">
            <div class="panel-heading gatewayContainer">
                <h4>@SmartEnergy.Resources.SupervisaoTexts.ModeloVersao</h4><br />
                @ViewBag.GatewayModelo
            </div>
        </div>
    </div>
    <div class="col-lg-3">
        <button class="btn btn-Atualizar btn-sm btn-block atualizarContainer" id="Atualizar" onclick="Atualizar()">@SmartEnergy.Resources.ComumTexts.BotaoAtualizar</button>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">

        @{
            GateE_InfoGeral_Dominio infoGeral = ViewBag.infoGeral;
            bool solicitaSuperv = ViewBag.SolicitaSuperv;


            if (infoGeral != null && solicitaSuperv)
            {
                <div style="color:white;">
                    <div class="row">
                        <div class="col-lg-3">
                            <div class="panel darkgray-bg">
                                <div class="panel-heading">
                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.DataHora</h4><br />
                                    <span>@string.Format("{0:G}", infoGeral.relogio)</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="panel darkgray-bg">
                                <div class="panel-heading">
                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.TotalResets</h4><br />
                                    <span>@infoGeral.qtReset</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3">

                            @{
                                string infos_celular = "";

                                if (infoGeral.IF_IoT == GateE_TIPO_INTERFACE_IOT.SIMCARD)
                                {
                                    infos_celular = "GSM<br><br>";
                                    infos_celular += "IP        : " + ((infoGeral.gsm_IP_eq.Length == 0) ? "---" : infoGeral.gsm_IP_eq) + "<br>";
                                    infos_celular += "IMEI      : " + ((infoGeral.gsm_IMEI.Length == 0) ? "---" : infoGeral.gsm_IMEI) + "<br>";
                                    infos_celular += "ICC       : " + ((infoGeral.gsm_ICC.Length == 0) ? "---" : infoGeral.gsm_ICC) + "<br>";
                                    infos_celular += "FABRICANTE: " + ((infoGeral.gsm_FAB.Length == 0) ? "---" : infoGeral.gsm_FAB) + "<br>";
                                    infos_celular += "MOD       : " + ((infoGeral.gsm_MOD.Length == 0) ? "---" : infoGeral.gsm_MOD) + "<br>";
                                    infos_celular += "OPERADORA : " + ((infoGeral.gsm_OP.Length == 0) ? "---" : infoGeral.gsm_OP) + "<br>";
                                    infos_celular += "TECNOLOGIA: " + ((infoGeral.gsm_TEC.Length == 0) ? "---" : infoGeral.gsm_TEC) + "<br>";
                                }
                                else
                                {
                                    infos_celular = "Wi-Fi<br><br>";
                                    infos_celular += "IP: " + ((infoGeral.gsm_IP_eq.Length == 0) ? "---" : infoGeral.gsm_IP_eq) + "<br>";
                                }
                            }

                            <div class="panel darkgray-bg bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="@infos_celular">
                                <div class="panel-heading">
                                    <div class="row">
                                        <div class="col-lg-10">
                                            <h4>@(infoGeral.IF_IoT == GateE_TIPO_INTERFACE_IOT.SIMCARD ? infoGeral.gsm_TEC : "Wi-Fi")</h4><br />
                                            <span>@infoGeral.status_GSM_WIFI</span>
                                        </div>
                                        <div class="col-lg-2" style="text-align: left; font-size: 30px;">

                                            @{
                                                if (infoGeral.IF_IoT == GateE_TIPO_INTERFACE_IOT.SIMCARD)
                                                {
                                                    switch (infoGeral.sinal_GSM_WIFI)
                                                    {
                                                        default:
                                                        case 0:
                                                        case 1:
                                                            <img src="..\Imagens\Sinal\sinal_gsm_0.svg" width="32" high="32" />
                                                            break;
                                                        case 2:
                                                            <img src="..\Imagens\Sinal\sinal_gsm_1.svg" width="32" high="32" />
                                                            break;
                                                        case 3:
                                                            <img src="..\Imagens\Sinal\sinal_gsm_2.svg" width="32" high="32" />
                                                            break;
                                                        case 4:
                                                            <img src="..\Imagens\Sinal\sinal_gsm_3.svg" width="32" high="32" />
                                                            break;
                                                    }
                                                }
                                                else
                                                {
                                                    switch (infoGeral.sinal_GSM_WIFI)
                                                    {
                                                        default:
                                                        case 0:
                                                        case 1:
                                                            <img src="..\Imagens\Sinal\sinal_wifi_0.svg" width="32" high="32" />
                                                            break;
                                                        case 2:
                                                            <img src="..\Imagens\Sinal\sinal_wifi_1.svg" width="32" high="32" />
                                                            break;
                                                        case 3:
                                                            <img src="..\Imagens\Sinal\sinal_wifi_2.svg" width="32" high="32" />
                                                            break;
                                                        case 4:
                                                            <img src="..\Imagens\Sinal\sinal_wifi_3.svg" width="32" high="32" />
                                                            break;
                                                    }
                                                }
                                            }

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="panel darkgray-bg">
                                <div class="panel-heading">
                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.UltimaAtualizacao Firmware @(infoGeral.OTA ? string.Format("(Tentativa {0} de 4)", infoGeral.OTA_try) : "")</h4><br />
                                    <span>@infoGeral.status_OTA</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-6">
                            <div class="panel darkgray-bg statusGateway">
                                <div class="panel-heading">
                                    <h4>Status Gateway</h4>

                                    <div class="statusGatewayContainer">

                                        @if (infoGeral.status_Gateway.Count > 0)
                                        {
                                            <ul>
                                                @{
                                                    foreach (string item in infoGeral.status_Gateway)
                                                    {
                                                        <li>@item</li>
                                                    }
                                                }
                                            </ul>
                                        }
                                        else
                                        {
                                            <span>Gateway OK</span>
                                        }

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="panel darkgray-bg">
                                <div class="panel-heading">
                                    <h4>Interfaces</h4>

                                    <div class="statusInterfaceContainer">
                                        <ul>

                                            @{
                                                string[] classesLed = new string[4] { "led-gray", "led-green", "led-red", "led-yellow" };

                                                <li><div class="@classesLed[infoGeral.status_Interfaces[0]]"><span>Bluetooth</span></div></li>
                                                <li><div class="@classesLed[infoGeral.status_Interfaces[1]]"><span>GSM</span></div></li>
                                                <li><div class="@classesLed[infoGeral.status_Interfaces[2]]"><span>Wi-Fi</span></div></li>
                                            }

                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="panel darkgray-bg statusMedicoes">
                                <div class="panel-heading">
                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.MedicoesEnergia</h4>

                                    <div class="statusInterfaceContainer">
                                        @if (infoGeral.status_MedicoesEnergia.Count > 0)
                                        {
                                            <ul>
                                                @{
                                                    for (int i = 0; i < 1; i++)
                                                    {
                                                        <li><div class="@classesLed[infoGeral.status_MedicoesEnergia[i].status]"><span class="statusRemotasSpan">@infoGeral.status_MedicoesEnergia[i].nome</span></div></li>
                                                    }
                                                }
                                            </ul>
                                        }
                                        else
                                        {
                                            <span><i class="fg fg-gear_off" style="font-size:60px;"></i></span>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-4">
                            @{
                                string classeNormal = "verdeclaro-bg";
                                if (infoGeral.eq32[(int)GateE_EQ32.VIN].nok_sin)
                                {
                                    classeNormal = "vermelho-bg";
                                }
                            }
                            <div class="panel @classeNormal">
                                <div class="panel-heading">
                                    <div class="row">
                                        <div class="col-lg-2 statusBateriaContainer">
                                            <i class="fg fg-plugue_energia"></i>
                                        </div>
                                        <div class="col-lg-9 statusBateriaContainer">
                                            <span>@string.Format("{0:0.0} V", infoGeral.volt_in)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            @{
                                if (infoGeral.eq32[(int)GateE_EQ32.VBAT].nok_sin)
                                {
                                    classeNormal = "vermelho-bg";
                                }
                                else
                                {
                                    classeNormal = "verdeclaro-bg";
                                }
                            }
                            <div class="panel @classeNormal">
                                <div class="panel-heading">
                                    <div class="row">
                                        <div class="col-lg-2 statusBateriaContainer">
                                            <i class="fg fg-bateria_energia"></i>
                                        </div>
                                        <div class="col-lg-9 statusBateriaContainer">
                                            <span>@string.Format("{0:0.00} V", infoGeral.volt_bat)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            @{
                                if (infoGeral.eq32[(int)GateE_EQ32.TBAT].nok_sin)
                                {
                                    classeNormal = "vermelho-bg";
                                }
                                else
                                {
                                    classeNormal = "verdeclaro-bg";
                                }
                            }
                            <div class="panel @classeNormal">
                                <div class="panel-heading">
                                    <div class="row">
                                        <div class="col-lg-2 statusBateriaContainer">
                                            <i class="fg fg-bateria_temperatura"></i>
                                        </div>
                                        <div class="col-lg-9 statusBateriaContainer">
                                            <span>@string.Format("{0:0.0} °C", infoGeral.temp_bat)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @{
                        string classeCODI = "darkgray-bg";
                        string classeCODIsub = "lightgray-bg";

                        if (infoGeral.eq32[(int)GateE_EQ32.CODI].nok_sin)
                        {
                            classeCODI = "darkred-bg";
                            classeCODIsub = "red-bg";
                        }
                    }

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="panel @classeCODI">
                                <div class="panel-heading">
                                    <h4>Medidor de Faturamento</h4><br />
                                    <br />

                                    @{
                                        string codi_atv = "---";
                                        string codi_rtv = "---";
                                        string[] periodo = new string[4] { "Ponta", "Fora de Ponta Indutivo", "Fora de Ponta Capacitivo", "---" };
                                        string codi_seg = "---";
                                        string[] quadrantes = new string[4] { "Q1", "Q4", "Q2", "Q3" };
                                        string quadrante = "---";

                                        if (infoGeral.status_CODI != "ERRO")
                                        {
                                            codi_atv = infoGeral.codi_atv.ToString();
                                            codi_rtv = infoGeral.codi_rtv.ToString();
                                            codi_seg = infoGeral.codi_seg.ToString();
                                            quadrante = quadrantes[infoGeral.codi_qdr];
                                        }
                                    }

                                    <div class="row">
                                        <div class="col-lg-2">
                                            <div class="panel @classeCODIsub">
                                                <div class="panel-heading">
                                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.Ativo</h4><br />
                                                    <span>@codi_atv</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-2">
                                            <div class="panel @classeCODIsub">
                                                <div class="panel-heading">
                                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.Reativo</h4><br />
                                                    <span>@codi_rtv</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-2">
                                            <div class="panel @classeCODIsub">
                                                <div class="panel-heading">
                                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.Periodo</h4><br />
                                                    <span>@periodo[infoGeral.codi_hrTari]</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-2">
                                            <div class="panel @classeCODIsub">
                                                <div class="panel-heading">
                                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.Segundos</h4><br />
                                                    <span>@codi_seg</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-2">
                                            <div class="panel @classeCODIsub">
                                                <div class="panel-heading">
                                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.Quadrante</h4><br />
                                                    <span>@quadrante</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-2">
                                            <div class="panel @classeCODIsub">
                                                <div class="panel-heading">
                                                    <h4>@SmartEnergy.Resources.ConfiguracaoTexts.Protocolo</h4><br />
                                                    <span>@infoGeral.status_CODI</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="overlay_aguarde" style="display: none">
                        <div class="spinner-aguarde">
                            <div class="sk-spinner sk-spinner-wandering-cubes">
                                <div class="sk-spinner sk-spinner-wave">
                                    <div class="sk-rect1 text-ligh"></div>
                                    <div class="sk-rect2"></div>
                                    <div class="sk-rect3"></div>
                                    <div class="sk-rect4"></div>
                                    <div class="sk-rect5"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="row">
                    <div class="col-lg-12">
                        <div class="panel red-bg">
                            <div class="panel-heading erroContainer">
                                <h2><i class="fa fa-5x fa-exclamation-triangle"></i><br /><br />Erro ao receber as informações da Gateway</h2>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }

    </div>
</div>

<div class="row">
    <div class="col-lg-6">
        <div class="panel panel-info">
            <div class="panel-heading">
                <h4>@SmartEnergy.Resources.SupervisaoTexts.UltimaAtualizacao dos Históricos</h4><br />
                @ViewBag.GatewayAtualizacao
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="panel panel-info">
            <div class="panel-heading">
                <h4>Horário do @SmartEnergy.Resources.SupervisaoTexts.DataHoraEq na @SmartEnergy.Resources.SupervisaoTexts.UltimaAtualizacao</h4><br />
                @ViewBag.GatewayDataEq
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">

    $(document).ready(function () {

    });


    function Atualizar() {

        // Desabilitar o botão
        document.getElementById("Atualizar").disabled = true;

        // Atualizar a página
        location.reload();
    };

</script>
