﻿@model IEnumerable<SmartEnergyLib.SQL.ClientesDominio>

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = "Gateways";
}

<head>
    <title>Gateways</title>
    <style>

        a:link {
            color: #676A6C;
        }

        a:hover {
            color: #AFB0B1;
        }


        .tooltip-inner {
            max-width: 350px;
            width: 350px;
            white-space: pre-wrap;
            text-align: left;
        }

    </style>
</head>

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-heading">
                    <h4>Gateways</h4>
                </div>
                <div class="panel-body">
                    <table class="table table-striped table-bordered table-hover dataTables-gateways">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Gateway</th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.Modelo | @SmartEnergy.Resources.ConfiguracaoTexts.Versao</th>
                                <th>@SmartEnergy.Resources.SupervisaoTexts.UltimaAtualizacao</th>
                                <th>IP</th>
                                <th><i class="fa fa-signal icones"></i> Sinal</th>
                                <th>@SmartEnergy.Resources.SupervisaoTexts.Operadora (Tecnologia)</th>                                                                
                                <th class="some_desktop">@SmartEnergy.Resources.SupervisaoTexts.Observacoes</th>
                                <th>Status</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>

                            @{
                                List<SupervGatewaysDominio> gateways = ViewBag.SupervList;

                                if (gateways != null)
                                {
                                    foreach (SupervGatewaysDominio gateway in @ViewBag.SupervList)
                                    {
                                        // data e hora atualizacao
                                        string DataHora = "-";
                                        string DataHora_Sort = "20000101000000";

                                        if (gateway.DataHora != null)
                                        {
                                            DataHora = String.Format("{0:G}", gateway.DataHora);
                                            DataHora_Sort = String.Format("{0:yyyyMMddHHmmss}", gateway.DataHora);
                                        }

                                        // data e hora equipamento
                                        string DataHoraEq = "-";
                                        string DataHora_SortEq = "20000101000000";

                                        if (gateway.DataEq != null)
                                        {
                                            DataHoraEq = String.Format("{0:G}", gateway.DataEq);
                                            DataHora_SortEq = String.Format("{0:yyyyMMddHHmmss}", gateway.DataEq);
                                        }

                                        int IDTipoAcesso = ViewBag._IDTipoAcesso;



                                        <tr>

                                            <td>@gateway.IDGateway</td>
                                            <td>@gateway.Nome</td>
                                            <td>@gateway.ModeloEq&nbsp;<br />@gateway.VersaoEq&nbsp;</td>
                                            <td><span style="display:none;">@DataHora_Sort</span>@DataHora</td>
                                            <td>@gateway.IP</td>

                                            @{
                                                    int sinal = gateway.Sinal;
                                                    string SinalTexto = "Sinal Fraco";
                                                    string SinalCor = "red";
                                                    string sinalStr = "-";

                                                    if (sinal >= 38 && sinal < 50)
                                                    {
                                                        SinalTexto = "Sinal Médio Fraco";
                                                        SinalCor = "orange";
                                                    }

                                                    if (sinal >= 50 && sinal < 68)
                                                    {
                                                        SinalTexto = "Sinal Médio";
                                                        SinalCor = "orange";
                                                    }

                                                    if (sinal >= 68 && sinal <= 100)
                                                    {
                                                        SinalTexto = "Sinal Forte";
                                                        SinalCor = "green";
                                                    }

                                                    if (sinal >= 100)
                                                    {
                                                        sinal = 999;
                                                    }

                                                    if (sinal > 0)
                                                    {
                                                        sinalStr = string.Format("{0}%", gateway.Sinal);
                                                    }
                                            }

                                            <td>
                                                <span style="display:none;">@string.Format("{000}", sinal)</span>
                                                <span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="@SinalTexto"><font color="@SinalCor"><b>@sinalStr</b></font></span>
                                            </td>

                                            @{
                                                string operadora = "-";
                                                string infos_celular = "";

                                                if (gateway.OP != null)
                                                {
                                                    if (gateway.OP.Length > 0)
                                                    {
                                                        operadora = gateway.OP;
                                                    }

                                                    if (gateway.GSM_TEC.Length > 0)
                                                    {
                                                        operadora += " (" + gateway.GSM_TEC + ")";
                                                    }

                                                    if (IDTipoAcesso == 0 || IDTipoAcesso == 5)
                                                    {
                                                        infos_celular = "ICC [" + ((gateway.ICC.Length == 0) ? "---" : gateway.ICC) + "]<br>";
                                                        infos_celular += "IMEI [" + ((gateway.IMEI.Length == 0) ? "---" : gateway.IMEI) + "]<br>";
                                                        infos_celular += "FAB [" + ((gateway.FAB.Length == 0) ? "---" : gateway.FAB) + "]<br>";
                                                        infos_celular += "MOD [" + ((gateway.MOD.Length == 0) ? "---" : gateway.MOD) + "]<br>";
                                                        infos_celular += "OP [" + ((gateway.OP.Length == 0) ? "---" : gateway.OP) + "]<br>";
                                                        infos_celular += "GSM_TEC [" + ((gateway.GSM_TEC.Length == 0) ? "---" : gateway.GSM_TEC) + "]<br>";
                                                        infos_celular += "MCC [" + ((gateway.MCC.Length == 0) ? "---" : gateway.MCC) + "]<br>";
                                                        infos_celular += "MNC [" + ((gateway.MNC.Length == 0) ? "---" : gateway.MNC) + "]<br>";
                                                        infos_celular += "LAC [" + ((gateway.LAC.Length == 0) ? "---" : gateway.LAC) + "]<br>";
                                                        infos_celular += "CID [" + ((gateway.CID.Length == 0) ? "---" : gateway.CID) + "]<br>";
                                                    }
                                                    else
                                                    {
                                                        infos_celular = gateway.OP;
                                                    }
                                                }

                                                if (infos_celular.Length == 0)
                                                {
                                                    infos_celular = "Sem informações";
                                                }
                                            }

                                            <td>
                                                <span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="@infos_celular">@operadora</span>
                                            </td>


                                            @{
                                                    string Status_Sort = "";

                                                    //
                                                    // STATUS UPLOAD ARQUIVOS
                                                    //

                                                    string cor_upload = "green";

                                                    Status_Sort = "Z";

                                                    if (gateway.Status == "1")
                                                    {
                                                        Status_Sort = "A";

                                                        cor_upload = "red";
                                                    }

                                                    //
                                                    // STATUS REMOTAS
                                                    //

                                                    string cor_remota = "gray";
                                                    string status_remota_texto = "Sem remotas";
                                                    int status_remota = 0;

                                                    if (gateway.RemotasList != null)
                                                    {
                                                        // comunicando
                                                        if (gateway.RemotasList.Count > 0)
                                                        {
                                                            status_remota = 1;
                                                            status_remota_texto = "";
                                                        }

                                                        // percorre remotas
                                                        foreach (GatewayRemotasDominio remota in gateway.RemotasList)
                                                        {
                                                            // verifica status
                                                            if (remota.Status == 0)
                                                            {
                                                                status_remota = 2;
                                                                status_remota_texto += string.Format("Remota {1} não comunicando - [{0}]", remota.DataStatusTexto, remota.Remota);
                                                            }

                                                            if (remota.Status == 1)
                                                            {
                                                                status_remota_texto += string.Format("Remota {1} Normal - [{0}]", remota.DataStatusTexto, remota.Remota);
                                                            }

                                                            if (remota.Status == -1)
                                                            {
                                                                status_remota = 2;
                                                                status_remota_texto += string.Format("Remota {1} Status indefinido - [{0}]", remota.DataStatusTexto, remota.Remota);
                                                            }

                                                            // pula linha
                                                            status_remota_texto += "<br>";
                                                        }
                                                    }

                                                    switch (status_remota)
                                                    {
                                                        case 0:
                                                        default:
                                                            Status_Sort += "B";

                                                            status_remota_texto = "Sem remotas";
                                                            cor_remota = "orange";
                                                            break;

                                                        case 1:
                                                            Status_Sort += "Z";

                                                            cor_remota = "green";
                                                            break;

                                                        case 2:
                                                            Status_Sort += "A";

                                                            cor_remota = "red";
                                                        break;
                                                    }

                                                    //
                                                    // STATUS FALHA ENVIO ARQUIVOS
                                                    //

                                                    if (gateway.NumFalhas > 0 && (gateway.IDTipoTempo != TIPO_TEMPO_GATEWAY.Ignorar_StartUpNaoRealizado && gateway.IDTipoTempo != TIPO_TEMPO_GATEWAY.Ignorar_PendenciaCliente && gateway.IDTipoTempo != TIPO_TEMPO_GATEWAY.SCDE))
                                                    {
                                                        Status_Sort += "A";
                                                    }
                                                    else
                                                    {
                                                        Status_Sort += "Z";
                                                    }

                                                    //
                                                    // STATUS GATEWAY
                                                    //

                                                    if (gateway.IDTipoTempo < 10)
                                                    {
                                                        Status_Sort += "A";
                                                    }
                                                    else
                                                    {
                                                        Status_Sort += "Z";
                                                    }

                                            }

                                            @{
                                                    // leio ultima observacao da gateway
                                                    ObservacaoGatewayMetodos observacaoMetodos = new ObservacaoGatewayMetodos();
                                                    ObservacaoGatewayDominio ultimaObservacao = observacaoMetodos.ListarPrimeiroGateway(gateway.IDGateway);


                                                    // le tags
                                                    ListaTiposMetodos listaMetodos = new ListaTiposMetodos();
                                                    List<ObservacaoTagsDominio> tags = listaMetodos.ListarTodos_ObservacaoTags();

                                                    var Observacao_Tag_Sort = "Z";
                                                    var classe = "badge-white";

                                                    // a principio sem observacao
                                                    string descricao = "Sem observação";

                                                    if (ultimaObservacao != null)
                                                    {
                                                        foreach (ObservacaoTagsDominio tag in tags)
                                                        {
                                                            if (ultimaObservacao.IDTag == tag.IDTag)
                                                            {
                                                                descricao = @tag.Descricao;
                                                            }
                                                        }


                                                        if (ultimaObservacao.IDTag > 0 && ultimaObservacao.IDTag < 100)
                                                        {
                                                            Observacao_Tag_Sort = "A_" + @descricao;
                                                            classe = "badge-danger";

                                                        }

                                                        if (ultimaObservacao.IDTag > 100 && ultimaObservacao.IDTag < 200)
                                                        {
                                                            Observacao_Tag_Sort = "B_" + @descricao;
                                                            classe = "badge-warning";
                                                        }

                                                        if (ultimaObservacao.IDTag > 200 && ultimaObservacao.IDTag < 300)
                                                        {
                                                            Observacao_Tag_Sort = "C_" + @descricao;
                                                            classe = "badge-primary";
                                                        }

                                                        if (@ultimaObservacao.IDTag == 0)
                                                        {
                                                            <td onclick="ObservacoesGateway(event, @gateway.IDGateway);"><span style="display:none;">@Observacao_Tag_Sort</span>&nbsp;</td>
                                                        }
                                                        else
                                                        {
                                                            var titulo = string.Format("{0:d}\n{1}", @ultimaObservacao.DataHora, @ultimaObservacao.Observacao);

                                                            <td onclick="ObservacoesGateway(event, @ultimaObservacao.IDGateway);"><span style="display:none;">@Observacao_Tag_Sort</span><span class="badge @classe some_desktop" title="@titulo">&nbsp;&nbsp;@descricao&nbsp;&nbsp;</span></td>
                                                        }
                                                    }
                                                    else
                                                    {
                                                        <td onclick="ObservacoesGateway(event, @gateway.IDGateway);"><span style="display:none;">@Observacao_Tag_Sort</span>&nbsp;</td>
                                                    }
                                            }     
                                                                                   
                                            <td>
                                                <span style="display:none;">@Status_Sort</span>

                                                <span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="@gateway.StatusTexto"><i class="fa fa-upload" style="font-size:20px; color:@cor_upload"></i></span>&nbsp;&nbsp;
                                                <span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="@status_remota_texto"><i class="fa fa-sitemap" style="font-size:20px; color:@cor_remota"></i></span>&nbsp;&nbsp;

                                                @{
                                                    bool View_ConfiguracaoRemota = (ViewBag.View_ConfiguracaoRemota == 1) ? true : false;

                                                    if (gateway.AcessoRemoto && View_ConfiguracaoRemota)
                                                    {
                                                        if (gateway.GatewayConectada_IoT)
                                                        {
                                                            <span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="Gateway conectada no Servidor IoT"><i class="fa fa-chain" style="font-size:20px; color:green"></i>&nbsp;&nbsp;</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="Gateway não conectada no Servidor IoT"><i class="fa fa-chain-broken" style="font-size:20px; color:red"></i>&nbsp;&nbsp;</span>
                                                        }
                                                    }
                                                }

                                                @{
                                                    if (IDTipoAcesso == 0 || IDTipoAcesso == 5)
                                                    {
                                                        if (gateway.NumFalhas > 0 && (gateway.IDTipoTempo != TIPO_TEMPO_GATEWAY.Ignorar_StartUpNaoRealizado && gateway.IDTipoTempo != TIPO_TEMPO_GATEWAY.Ignorar_PendenciaCliente && gateway.IDTipoTempo != TIPO_TEMPO_GATEWAY.SCDE))
                                                        {
                                                            string datahora_str = String.Format("{0:G}", gateway.DataHora);
                                                            string status_FalhaEnvio_texto = string.Format("Ocorreram {0} Falha(s) de Envio nos 3 dias anteriores a última atualização", gateway.NumFalhas);

                                                            <a href='@("/Relatorios/Relat_Eventos_Gateway?IDCliente=" + @gateway.IDCliente.ToString() + "&IDGateway=" + @gateway.IDGateway.ToString() + "&DataHora=" + datahora_str)'><span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="@status_FalhaEnvio_texto"><i class="fa fa-warning" style="font-size:20px; color:red"></i>&nbsp;&nbsp;</span></a>
                                                        }
                                                    }
                                                }

                                                @{
                                                    if (gateway.IDTipoTempo < 10)
                                                    {
                                                        string status_Gateway_texto = "Gateway Bloqueada";

                                                        switch ((int)gateway.IDTipoTempo)
                                                        {
                                                            case TIPO_TEMPO_GATEWAY.GatewayBloqueada:
                                                                status_Gateway_texto = "Gateway Bloqueada";
                                                                break;

                                                            case TIPO_TEMPO_GATEWAY.Ignorar_StartUpNaoRealizado:
                                                                status_Gateway_texto = "Ignorar Falha de Upload (Start Up não realizado)";
                                                                break;

                                                            case TIPO_TEMPO_GATEWAY.Ignorar_PendenciaCliente:
                                                                status_Gateway_texto = "Ignorar Falha de Upload (Pendência do Cliente)";
                                                                break;

                                                            case TIPO_TEMPO_GATEWAY.SCDE:
                                                                status_Gateway_texto = "Ignorar Falha de Upload (SCDE)";
                                                                break;
                                                        }

                                                        <span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="@status_Gateway_texto"><i class="fa fa-ban" style="font-size:20px; color:red"></i>&nbsp;&nbsp;</span>
                                                    }
                                                }

                                            </td>     
                                              
                                            <td class="link_preto">

                                                @{
                                                    bool View_Configuracao = (ViewBag.View_Configuracao == 1) ? true : false;

                                                    <a href='@("/Supervisao/Gateway?IDGateway=" + @gateway.IDGateway.ToString())' title="@SmartEnergy.Resources.MenuTexts.MenuLateralSupervisao">
                                                        <i class="fa fa-desktop icones"></i>
                                                    </a>

                                                    // verifica se usuário pode ver histórico de sinal
                                                    if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                                    {
                                                        <a href='@("/Supervisao/Gateway_HistoricoSinal?IDGateway=" + @gateway.IDGateway.ToString())' title="@SmartEnergy.Resources.SupervisaoTexts.Sinal">
                                                            <i class="fa fa-bar-chart-o icones"></i>
                                                        </a>
                                                    }

                                                    // verifica se usuário pode ver configuração
                                                    if (View_Configuracao)
                                                    {
                                                        <a href='@("/Configuracao/Gateway_Editar?IDGateway=" + @gateway.IDGateway.ToString())' title="@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracao">
                                                            <i class="fa fa-gear icones"></i>
                                                        </a>
                                                    }

                                                    // caso Demo
                                                    if (IDTipoAcesso == TIPO_ACESSO.DEMONSTRACAO)
                                                    {
                                                        <a href='@("/Supervisao/EntradasDigitais?IDGateway=" + @gateway.IDGateway.ToString())' title="@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoED">
                                                            <i class="fa fa-sign-in icones"></i>
                                                        </a>
                                                        <a href='@("/Supervisao/SaidasDigitais?IDGateway=" + @gateway.IDGateway.ToString())' title="@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoSD">
                                                            <i class="fa fa-sign-out icones"></i>
                                                        </a>
                                                    }

                                                    // somente SmartGate X com AcessoRemoto habilitado
                                                    if ((gateway.IDTipoGateway == TIPO_GATEWAY.GATE_X_421 || gateway.IDTipoGateway == TIPO_GATEWAY.GATE_X_422) && gateway.AcessoRemoto && View_ConfiguracaoRemota && gateway.GatewayConectada_IoT)
                                                    {
                                                        <a href='@("/Supervisao/EntradasDigitais?IDGateway=" + @gateway.IDGateway.ToString())' title="@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoED">
                                                            <i class="fa fa-sign-in icones"></i>
                                                        </a>
                                                        <a href='@("/Supervisao/SaidasDigitais?IDGateway=" + @gateway.IDGateway.ToString())' title="@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoSD">
                                                            <i class="fa fa-sign-out icones"></i>
                                                        </a>
                                                    }
                                                }

                                            </td>
                                                                       
                                        </tr>
                                    }                                    
                                }
                            }

                        </tbody>
                    </table>

                    @Html.Hidden("IDGatewaySelecionado", 0)
                    @Html.Hidden("IDObservacaoSelecionado", 0)

                    <div class="modal inmodal animated fadeIn" id="ModalObservacoes" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-editar-header">
                                    <i class="fa fa-check-square-o modal-editar-title"></i><span class="modal-editar-title">&nbsp;&nbsp;@SmartEnergy.Resources.SupervisaoTexts.Observacoes</span>


                                    <div class="pull-right dashboard-tools link_branco" style="margin-top:-2px;">
                                        <h4>
                                            <a href="#" onclick="javascript:ObservacaoGateway(event, 0, 0);" id="BotaoAdicionar" title="@SmartEnergy.Resources.ComumTexts.BotaoInserir"><i class="fa fa-plus"></i></a>
                                        </h4>
                                    </div>

                                </div>
                                <div class="modal-body">

                                    <div id="Observacoes_resultado" min-height:500px;">
                                        @Html.Partial("~/Views/Suporte/_ObservacoesGateway.cshtml")
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-default" onclick="FecharObservacoesGateway();">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal inmodal animated fadeIn" id="ModalObservacao" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-editar-header">
                                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only modal-tools-header">Close</span></button>
                                    <i class="fa fa-check-square-o modal-editar-title"></i><span class="modal-editar-title">&nbsp;&nbsp;@SmartEnergy.Resources.SupervisaoTexts.Observacao</span>
                                </div>
                                <div class="modal-body">

                                    <div id="Observacao_resultado" min-height:500px;">
                                        @Html.Partial("~/Views/Suporte/_ObservacaoGateway.cshtml")
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                    <button type="button" class="btn btn-primary" onclick="SalvarObservacaoGateway();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/datetimePickerStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/plugins/iCheck")



    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        $(document).ready(function () {

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "portugues-pre": function (data) {
                    var a = 'a';
                    var e = 'e';
                    var i = 'i';
                    var o = 'o';
                    var u = 'u';
                    var c = 'c';
                    var special_letters = {
                        "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                        "É": e, "é": e, "Ê": e, "ê": e,
                        "Í": i, "í": i, "Î": i, "î": i,
                        "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                        "Ú": u, "ú": u, "Ü": u, "ü": u,
                        "ç": c, "Ç": c
                    };
                    for (var val in special_letters)
                        data = data.split(val).join(special_letters[val]).toLowerCase();
                    return data;
                },
                "portugues-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },
                "portugues-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "numero-pre": function (a) {

                    var x = a;

                    if (a == "-") {
                        x = "-1";
                    }
                    else {
                        x = a.replace(".", "");
                        x = x.replace(",", ".");
                    }

                    return parseFloat(x);
                },

                "numero-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },

                "numero-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            $('.dataTables-gateways').DataTable({
                "iDisplayLength": 18,
                dom: 'ftp',

                "bAutoWidth": false,
                "aoColumnDefs": [
                            { "aTargets": [0], "sType": "numero" },
                            { "aTargets": [1], "sType": "portugues" },
                            { "aTargets": [2], "sType": "portugues" },
                            { "aTargets": [3], "sType": "portugues" },
                            { "aTargets": [4], "sType": "portugues" },
                            { "aTargets": [5], "sType": "portugues" },
                            { "aTargets": [6], "sType": "portugues" },
                            { "aTargets": [7], "sType": "portugues" },
                            { "aTargets": [8], "sType": "portugues" },
                            { "aTargets": [9], "bVisible": true, "bSortable": false, "bSearchable": false, },
                ],
                "aoColumns": [
                { sWidth: "5%" },   
                { sWidth: "21%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "9%" },
                { sWidth: "9%" },
                { sWidth: "9%" },
                { sWidth: "7%" },
                { sWidth: "10%" },
                { sWidth: "10%" }
                ],
                "order": [1, "asc"],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }

            });
        });

        $(".bottom").tooltip({
            placement: "bottom"
        });

        function ObservacoesGateway(e, IDGateway) {

            e.stopPropagation();

            // apresenta modal
            $("#ModalObservacoes").modal("show");

            // aguarde
            $('#Observacoes_resultado').css("display", "none");

            // IDGateway
            document.getElementById("IDGatewaySelecionado").value = IDGateway;

            $.ajax(
            {
                type: 'GET',
                url: '/Suporte/_ObservacoesGateway',
                dataType: 'html',
                data: { "IDGateway": IDGateway },
                cache: false,
                async: true,
                success: function (data) {

                    $('#Observacoes_resultado').css("display", "block");
                    $('#Observacoes_resultado').html(data);
                }
            });
        }

        function FecharObservacoesGateway() {

            // fecha modal
            $("#ModalObservacoes").modal("hide");

            // atualiza pagina
            location.reload();
        }

        function ObservacaoGateway(e, IDGateway, IDObservacao) {

            e.stopPropagation();

            // apresenta modal
            $("#ModalObservacao").modal("show");

            // aguarde
            $('#Observacao_resultado').css("display", "none");

            // IDGateway e IDObservacao
            if (IDGateway == 0) {
                // IDGateway
                IDGateway = document.getElementById("IDGatewaySelecionado").value;
            }
            else {
                document.getElementById("IDGatewaySelecionado").value = IDGateway;
            }

            document.getElementById("IDObservacaoSelecionado").value = IDObservacao;

            $.ajax(
            {
                type: 'GET',
                url: '/Suporte/_ObservacaoGateway',
                dataType: 'html',
                data: { "IDGateway": IDGateway, "IDObservacao": IDObservacao },
                cache: false,
                async: true,
                success: function (data) {

                    $('#Observacao_resultado').css("display", "block");
                    $('#Observacao_resultado').html(data);
                }
            });
        }


        function SalvarObservacaoGateway() {

            // IDObservacao
            var IDObservacao = document.getElementById("IDObservacaoSelecionado").value;

            // IDGateway
            var IDGateway = document.getElementById("IDGatewaySelecionado").value;

            // IDTag
            var IDTag = $("#tags").val();

            // data
            var data_relat = document.querySelector('[name="data_relat"]').value;

            // cliente visualiza
            var ClienteVisualizaObj = document.getElementsByName('ClienteVisualiza');
            var ClienteVisualiza = ClienteVisualizaObj[0].checked;

            // Observacao
            var ObservacaoTexto = document.getElementById("ObservacaoTexto").value;

            // salvar
            $.ajax(
            {
                type: 'GET',
                url: '/Suporte/ObservacaoGateway_Salvar',
                data: { 'IDObservacao': IDObservacao, 'IDGateway': IDGateway, 'IDTag': IDTag, 'ClienteVisualiza': ClienteVisualiza, 'ObservacaoData': data_relat, 'ObservacaoTexto': ObservacaoTexto },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        $.ajax(
                        {
                            type: 'GET',
                            url: '/Suporte/_ObservacoesGateway',
                            dataType: 'html',
                            data: { "IDGateway": IDGateway },
                            cache: false,
                            async: true,
                            success: function (data) {

                                $('#Observacoes_resultado').css("display", "block");
                                $('#Observacoes_resultado').html(data);
                            }
                        });

                    }, 100);
                },
                error: function (response) {

                }
            });
        }

        function ResolvidoObservacaoGateway(IDObservacao) {
            event.stopPropagation();

            // IDGateway
            var IDGateway = document.getElementById("IDGatewaySelecionado").value;

            // resolvido
            $.ajax(
            {
                type: 'GET',
                url: '/Suporte/ObservacaoGateway_Resolvido',
                data: { 'IDObservacao': IDObservacao },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        $.ajax(
                        {
                            type: 'GET',
                            url: '/Suporte/_ObservacoesGateway',
                            dataType: 'html',
                            data: { "IDGateway": IDGateway },
                            cache: false,
                            async: true,
                            success: function (data) {

                                $('#Observacoes_resultado').css("display", "block");
                                $('#Observacoes_resultado').html(data);
                            }
                        });

                    }, 100);
                },
                error: function (response) {

                }
            });
        };

        function ExcluirObservacaoGateway(IDObservacao, DataTexto) {
            event.stopPropagation();

            // IDGateway
            var IDGateway = document.getElementById("IDGatewaySelecionado").value;

            // titulo
            titulo = "Deseja excluir a Observação?<br/>" + DataTexto;

            swal({
                html: true,
                title: titulo,
                text: "Esta operação não poderá ser desfeita.",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#18a689",
                confirmButtonText: "Excluir",
                cancelButtonText: "Cancelar",
                closeOnConfirm: true
            }, function () {

                // excluir
                $.ajax(
                {
                    type: 'GET',
                    url: '/Suporte/ObservacaoGateway_Excluir',
                    data: { 'IDObservacao': IDObservacao },
                    contentType: 'application/html',
                    dataType: 'html',
                    cache: false,
                    async: true,
                    success: function (data) {

                        setTimeout(function () {

                            $.ajax(
                            {
                                type: 'GET',
                                url: '/Suporte/_ObservacoesGateway',
                                dataType: 'html',
                                data: { "IDGateway": IDGateway },
                                cache: false,
                                async: true,
                                success: function (data) {

                                    $('#Observacoes_resultado').css("display", "block");
                                    $('#Observacoes_resultado').html(data);
                                }
                            });

                        }, 100);
                    },
                    error: function (response) {

                    }
                });
            });
        };

    </script>
}


