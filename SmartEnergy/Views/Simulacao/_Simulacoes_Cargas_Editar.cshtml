﻿@using SmartEnergyLib.SQL

<style>

    .theme-config-box-2 {
        min-width: 1100px;
        margin-right: -1140px;
        position: relative;
        z-index: 2000;
        transition-duration: 0.8s;
    }

    .theme-config-box-2.show {
          margin-right: 0; }

</style>

@{
    SimulacaoCargasDominio cargaEditar = new SimulacaoCargasDominio();
    SimulacaoCenariosDominio cenarioEditar = new SimulacaoCenariosDominio();
    
    if (ViewBag.SimulacaoCarga != null)
    {
        cargaEditar = ViewBag.SimulacaoCarga;
    }

    if (ViewBag.SimulacaoCenario != null)
    {
        cenarioEditar = ViewBag.SimulacaoCenario;
    }    
}

<div id="editar_cargas_resultado">

    @if (ViewBag.SimulacaoCenario != null)
    {
     // verifica se deve renderizar a pagina de configuracao 
     // apenas renderiza o html abaixo se for feita a solicitacao da view atraves do AJAX
     <div class="theme-config">
        <div class="theme-config-box-2">
            <div class="menu-lateral">
                <div class="spin-icon" onclick="EditarCargas(-1, -1);">
                    <i class="fa fa-cogs fa-spin"></i>
                </div>
                <div class="wrapper wrapper-content">
                    <div class="panel panel-title">
                        <div class="panel-heading">
                            <h4>Configuração de Cargas</h4>
                        </div>
                        <div class="panel-body">
                            @using (Html.BeginForm("_Simulacoes_Cargas_Editar", "Simulacao", FormMethod.Post, new { id = "form-carga", role = "form" }))
                            {
                                @Html.Hidden("IDMedicao", cenarioEditar.IDMedicao)
                                @Html.Hidden("IDSimulacaoCarga", cargaEditar.IDSimulacaoCarga)
                                @Html.Hidden("IDSimulacaoCenario", cenarioEditar.IDSimulacaoCenario)
                                @Html.Hidden("Adicionar", cargaEditar.Adicionar)

                                            <div class="row">
                                    <div class="form-group col-lg-4">
                                        <label class="control-label">&nbsp;ID</label>
                                        @{
                                if (cargaEditar.IDSimulacaoCarga == 0)
                                            {
                                                @Html.Hidden("IDSimulacaoCarga", cargaEditar.IDSimulacaoCarga)
                                                @Html.TextBox("Novo", "Nova Carga", new { @class = "form-control", @disabled = "disabled" })
                                            }
                                            else
                                            {
                                                @Html.TextBox("ID", cargaEditar.IDSimulacaoCarga, new { @class = "form-control", @disabled = "disabled" })
                                            }
                                        }
                                    </div>
                                    <div class="col-lg-offset-2 col-lg-6">
                                        <div class="ibox-tools">
                                            <a class="btn btn-default" onclick="EditarCargas(-1, -1);" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                            <button type="button" class="btn btn-primary" id="BotaoSalvarCarga" onclick="SalvarCarga(@cenarioEditar.IDSimulacaoCenario, @cenarioEditar.IDMedicao);">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                        </div>
                                    </div>
                                </div>

                                 <div class="row">
                                    <div class="col-lg-10">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Nome</label>
                                        @Html.TextBox("Nome", cargaEditar.Nome, new { @class = "form-control" })
                                    </div>
                                </div>

                                <br />
                                <div class="row">
                                    <div class="col-lg-4">
                                        <label class="control-label">&nbsp;Carga</label>
                                        @Html.DropDownList("IDTipoCarga", new SelectList(ViewBag.listaTipoCarga, "ID", "Descricao", cargaEditar.IDTipoCarga), new { @class = "form-control", @onchange = "SelecionouTipoCarga();" })
                                    </div>
                                    <div class="col-lg-offset-2 col-lg-4">
                                        <div class="i-checks" style="margin-top:22px;">
                                            @Html.CheckBox("AdicionarCarga", cargaEditar.Adicionar, new { id = "add", @onchange = "CheckAdicionar(this)" })&nbsp;&nbsp;<span style="margin-right:14px;">Adicionar</span>
                                            @Html.CheckBox("RetirarCarga", !cargaEditar.Adicionar, new { id = "retirar", @onchange = "CheckRetirar(this)" })&nbsp;&nbsp;<span style="margin-right:14px;">Retirar</span>
                                        </div>
                                    </div>
                                </div>
                                <br />
                                <br />
                                <div class="row">
                                    <div class="col-lg-4">

                                        <label class="control-label carga">&nbsp;Potência (kW)</label>               
                                        <label class="control-label capacitor" style="display:none;">&nbsp;Potência (kVAr)</label>               
                                        @Html.TextBox("Potencia", Math.Abs(cargaEditar.Potencia), new { @class = "form-control" })
                                    </div>
                                    <div class="col-lg-offset-2 col-lg-4 carga">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia</label>
                                        @Html.TextBox("FatPot", cargaEditar.FatPot, new { @class = "form-control" })
                                    </div>
                                </div>
                                <br />                                      
                                <br />
                                <div class="panel panel-title">
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-lg-6">
                                                <h4 style="font-weight:bold">Horário</h4>
                                            </div>
                                        </div>
                                        <br />
                                        <div class="row">
                                            <div class="form-group col-lg-2">
                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.HoraIni</label>
                                                @Html.TextBox("HoraIni", String.Format("{0:t}", cargaEditar.HoraIni), new { @class = "form-control", data_mask = "99:99" })
                                            </div>
                                            <div class="form-group col-lg-2">
                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.HoraFim</label>
                                                @Html.TextBox("HoraFim", String.Format("{0:t}", cargaEditar.HoraFim), new { @class = "form-control", data_mask = "99:99" })
                                            </div>
                                            <div class="col-lg-8">
                                                <div class="i-checks" style="margin-top: 22px;">
                                                    @Html.CheckBox("Dom", cargaEditar.Dom, new { id = "dom", name = "dom", @class = "required-one" })&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.dom</span>
                                                    @Html.CheckBox("Seg", cargaEditar.Seg, new { id = "seg", name = "seg", @class = "required-one" })&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.seg</span>
                                                    @Html.CheckBox("Ter", cargaEditar.Ter, new { id = "ter", name = "ter", @class = "required-one" })&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.ter</span>
                                                    @Html.CheckBox("Qua", cargaEditar.Qua, new { id = "qua", name = "qua", @class = "required-one" })&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.qua</span>
                                                    @Html.CheckBox("Qui", cargaEditar.Qui, new { id = "qui", name = "qui", @class = "required-one" })&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.qui</span>
                                                    @Html.CheckBox("Sex", cargaEditar.Sex, new { id = "sex", name = "sex", @class = "required-one" })&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.sex</span>
                                                    @Html.CheckBox("Sab", cargaEditar.Sab, new { id = "sab", name = "sab", @class = "required-one" })&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.sab</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    }
    

</div>



<script type="text/javascript">

    $(document).ready(function () {

        //
        // VALIDACAO DOS CAMPOS
        //

        jQuery.validator.addMethod("alphanumeric", function (value, element) {
            return this.optional(element) || /^[\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\&\+\-\_\(\),]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

        jQuery.validator.addMethod("numeric", function (value, element) {
            return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.numeric");

        jQuery.validator.addMethod("time", function (value, element) {
            return this.optional(element) || /^(([0-1]?[0-9])|([2][0-3])):([0-5]?[0-9])(:([0-5]?[0-9]))?$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.horario");

        $.validator.addMethod('potencia', function (value) {
            return parseFloat(value) > 0;
        }, 'Potência deve ser maior que 0.');

        $.validator.addMethod('fatpot', function (value) {
            value = value.replace(",", ".");
            return parseFloat(value) >= -1.0 && parseFloat(value) <= 1.0;
        }, 'Digite um número entre -1 e 1.');

        $("#form-carga").validate({
            rules: {
                Nome: {
                    alphanumeric: true,
                    required: true,
                },
                HoraIni: {
                    time: true,
                },
                HoraFim: {
                    time: true,
                },
                Potencia: {
                    required: true,
                    numeric: true,
                    potencia: true,
                },
                FatPot: {
                    required: true,
                    numeric: true,
                    fatpot: true,
                },
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            },
        });

        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });

        $(".i-checks").on('ifToggled', function (e) { $(e.target).trigger('change'); });        
    });

    function CheckAdicionar(add) {
        if (add.checked) {
            $('#retirar').iCheck('uncheck');
        }
        else {
            $('#retirar').iCheck('check');
        }
    }

    function CheckRetirar(retira) {
        if (retira.checked) {
            $('#add').iCheck('uncheck');
        }
        else {
            $('#add').iCheck('check');
        }
    }

    function SalvarCarga(IDSimulacaoCenario, IDMedicao) {

        var $form = $('#form-carga');

        // converte potencia para negativo caso seja para retirar a carga
        var potencia = parseFloat(document.getElementById("Potencia").value);

        // verifica se entradas sao validas
        if (!$form.valid()) return false;

        if (retirar.checked)
        {
            document.getElementById("Adicionar").value = false;
        }
        else
        {
            document.getElementById("Adicionar").value = true;
        }

            event.stopPropagation();

            // aguarde
            $('.overlay_aguarde').toggle();

            // fecha janela
            $('.theme-config-box-2').toggleClass("show");

            // retiro campos desabilitados, pois nao envia via POST
            $(":disabled", $('#form')).removeAttr("disabled");

            $.ajax({
                url: '/Simulacao/Simulacao_Carga_Salvar',
                data: $form.serialize(),
                type: 'POST',
                success: function (data) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {
                            swal({
                                title: "Erro",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // abre novamente janela
                                setTimeout(function () { $('.theme-config-box-2').toggleClass("show"), 100 });
                            });
                        }
                        else {
                                // atualiza pagina
                                $.ajax(
                                {
                                    type: 'GET',
                                    url: '/Simulacao/_Simulacoes_Editar',
                                    dataType: 'html',
                                    data: { 'IDSimulacaoCenario': IDSimulacaoCenario, 'IDMedicao' : IDMedicao },
                                    cache: false,
                                    async: true,
                                    success: function (data) {

                                        // apresenta
                                        $('#cenarios_editar_resultado').html(data);

                                        // abre janela anterior
                                        setTimeout(function () { $('.theme-config-box-1').toggleClass("show"), 100 });
                                    },
                                    error: function (xhr, status, error) {
                                        setTimeout(function () {

                                            swal({
                                                title: "Erro",
                                                text: "Erro ao executar a operação!",
                                                type: "warning",
                                                confirmButtonColor: "#f8ac59",
                                                confirmButtonText: "Fechar",
                                            });

                                            // fim
                                            $('.overlay_aguarde').toggle();

                                        }, 100);

                                    }
                                });
                        }

                    }, 100);

                },
                error: function (xhr, status, error) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    // fecha janela
                    $('.theme-config-box-2').toggleClass("show");

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao salvar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);
                }
            });
    }

    function SelecionouTipoCarga () {

        var tipo_carga = parseInt(document.getElementById("IDTipoCarga").value);        

        if (tipo_carga == 0)
        {
            $('.capacitor').css("display", "none");
            $('.carga').css("display", "block");
        }
        else
        {
            $('.capacitor').css("display", "block");
            $('.carga').css("display", "none");
        }
    }


</script>