﻿@model SmartEnergyLib.SQL.SimulacaoContratosDominio

@using SmartEnergyLib.SQL
@using System.Globalization

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralSimulacao;
}

<style>
    .bar {
        height: 18px;
        background: #1AB394;
    }

    .quadro_mapa {
        padding: 2px;
        border: 1px solid #efefef;
    }

        .overlay_aguarde {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.45);
        z-index: 9999;
        color: white;
        display: inline-block;
        border-radius: 6px;
        margin-bottom: 20px;
        margin-left: 14px;
        margin-right: 14px;
    }

    .sk-spinner-wave div {
        background-color: #ffffff !important;
    }
    
    .spinner-aguarde {
        margin: 0;
        position: absolute;
        top: 50%;
        left: 50%;
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
    }
</style>


<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("Unidades_Editar", "Configuracao", FormMethod.Post, new { id = "form", role = "form" }))
            {
                @Html.Hidden("IDMedicao", Model.IDMedicao)

                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralSimulacao</h4>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-offset-6 col-lg-6">
                                <div class="ibox-tools">
                                    <button type="button" class="btn btn-primary" id="BotaoSalvar" onclick="Salvar();" disabled="">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                </div>
                            </div>
                        </div>
                        <br />

                        <div class="row">
                            <div class="form-group col-lg-4">
                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ContratoP (kW)</label>
                                @Html.TextBoxFor(model => model.ContratoDemP, new { @class = "form-control", @maxlength = "6" })
                            </div>
                            <div class="form-group col-lg-4">
                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ContratoFP (kW)</label>
                                @Html.TextBoxFor(model => model.ContratoDemFP, new { @class = "form-control", @maxlength = "6" })
                            </div>
                        </div>
                        <br />

                        <div class="row">
                            <div class="form-group col-lg-12">
                                <table id="dataTables-cargas" class="table table-striped table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>@SmartEnergy.Resources.SimulacaoTexts.Carga</th>
                                            <th>@SmartEnergy.Resources.SimulacaoTexts.Potencia</th>
                                            <th>@SmartEnergy.Resources.SimulacaoTexts.Inicio</th>
                                            <th>@SmartEnergy.Resources.SimulacaoTexts.Fim</th>
                                            <th>@SmartEnergy.Resources.SimulacaoTexts.DiaSemana</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>

                                        @{
                                            List<SimulacaoCargasDominio> cargas = ViewBag.simCargas;

                                            foreach (SimulacaoCargasDominio carga in cargas)
                                            {
                                                <tr>
                                                    <td>@carga.IDSimulacaoCargas</td>








                                                    <td class="link_preto">
                                                        <a href="#" class="confirm-delete-usuario"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>
                                                    </td>
                                                </tr>
                                            }
                                        }

                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-10">
                                <a id="BotaoAdicionarCarga" data-toggle="modal" href="#ModalUsuarios" class="btn btn-info pull-left" style="color:#ffffff; width:100%;">@SmartEnergy.Resources.SimulacaoTexts.AdicionarCarga</a>
                            </div>
                            <div class="col-lg-2">
                                <a id="BotaoExcluirTodos" href="#" onclick="javascript:Excluir_Todos();" class="btn btn-success pull-left" style="color:#ffffff; width:100%;">Excluir Todos</a>
                            </div>
                        </div>

                    </div>

                    <div class="overlay_aguarde" style="display: none">
                        <div class="spinner-aguarde">
                            <div class="sk-spinner sk-spinner-wandering-cubes">
                                <div class="sk-spinner sk-spinner-wave">
                                    <div class="sk-rect1 text-ligh"></div>
                                    <div class="sk-rect2"></div>
                                    <div class="sk-rect3"></div>
                                    <div class="sk-rect4"></div>
                                    <div class="sk-rect5"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            }

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/dataTables")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/plugins/iCheck")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        $(document).ready(function () {

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "portugues-pre": function (data) {
                    var a = 'a';
                    var e = 'e';
                    var i = 'i';
                    var o = 'o';
                    var u = 'u';
                    var c = 'c';
                    var special_letters = {
                        "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                        "É": e, "é": e, "Ê": e, "ê": e,
                        "Í": i, "í": i, "Î": i, "î": i,
                        "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                        "Ú": u, "ú": u, "Ü": u, "ü": u,
                        "ç": c, "Ç": c
                    };
                    for (var val in special_letters)
                        data = data.split(val).join(special_letters[val]).toLowerCase();
                    return data;
                },
                "portugues-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },
                "portugues-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "numero-pre": function (a) {
                    var x = (a == "-") ? 0 : a.replace(/,/, ".");
                    return parseFloat(x);
                },

                "numero-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },

                "numero-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            $('#dataTables-cargas').DataTable({
                "iDisplayLength": 10,
                dom: 'tp',

                'order': [1, 'asc'],
                "bAutoWidth": false,
                "aoColumns": [
                { sWidth: "5%" },
                { sWidth: "20%" },
                { sWidth: "20%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "20%" },
                { sWidth: "15%" },
                ],

                "columnDefs": [
                     { "targets": [0], "visible": false, "searchable": false, "orderable": false },
                     { "targets": [1], "sType": "portugues", "searchable": false },
                     { "targets": [2], "sType": "numero", "searchable": false },
                     { "targets": [3], "sType": "portugues", "searchable": false, "orderable": false },
                     { "targets": [4], "sType": "portugues", "searchable": false, "orderable": false },
                     { "targets": [5], "searchable": false, "orderable": false },
                     { "targets": [6], "searchable": false, "orderable": false },
                ],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }
            });

            //
            // VALIDACAO DOS CAMPOS
            //

            jQuery.validator.addMethod("alphanumeric", function (value, element) {
                return this.optional(element) || /^[\'\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\+\-\_\(\)," "]+$/i.test(value);
            }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

            jQuery.validator.addMethod("numeric", function (value, element) {
                return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
            }, "@SmartEnergy.Resources.ValidateTexts.numeric");

            $("#form").validate({
                rules: {
                    Nome: {
                        required: true,
                        alphanumeric: true,
                        minlength: 3,
                        maxlength: 50
                    },
                    IDGrupoUnidades: {
                        required: true
                    },
                },
                highlight: function (element) {
                    $(element).closest('.control-group').removeClass('success').addClass('error');
                },
                unhighlight: function (element) {
                    $(element).closest('.control-group').addClass('valid success').removeClass('error');
                }
            });

            // desabilita campos
            disableAll();

            $('form input').on('keypress', function (e) {
                return e.which !== 13;
            });

        });

        function disableAll() {

            var permissao = Math.ceil(@ViewBag.Permissao);

            // verifica permissao
            switch (permissao) {
                case 0:     // 0 - permissao de Admin: ve e escreve em tudo
                case 4:     // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
                case 3:     // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                    $("#BotaoSalvar").attr('disabled', false);
                    $("#IDGrupoUnidades").attr('disabled', false);
                    $("#Nome").attr('disabled', false);
                    break;

                case 5:     // 5 - permissao de Consultor

                    $("#BotaoSalvar").attr('disabled', false);
                    $("#IDGrupoUnidades").attr('disabled', false);
                    $("#Nome").attr('disabled', false);
                    break;

                default:
                case 1:     // 1 - permissao de Gerente: ve tudo e escreve parte
                case 2:     // 2 - permissao de Operador: ve tudo e nao pode escrever

                    break;
            }
        }

        function Salvar() {

            var $form = $('#form');

            // verifica se entradas sao validas
            if (!$form.valid()) return false;

            swal({
                title: "Deseja salvar?",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#1ab394",
                confirmButtonText: "Salvar",
                cancelButtonText: "Cancelar",
                closeOnConfirm: true
            }, function () {
                event.stopPropagation();

                // aguarde
                $('.overlay_aguarde').toggle();

                // retiro campos desabilitados, pois nao envia via POST
                $(":disabled", $('#form')).removeAttr("disabled");

                $.ajax({
                    url: '/Simulacao/Simulacao_Salvar',
                    data: $form.serialize(),
                    type: 'POST',
                    success: function (data) {

                        // fim
                        $('.overlay_aguarde').toggle();

                        setTimeout(function () {

                            // verifica se erro
                            if (data.status == "ERRO") {
                                swal({
                                    title: "Erro",
                                    text: data.erro,
                                    type: "warning",
                                    confirmButtonColor: "#f8ac59",
                                    confirmButtonText: "Fechar",
                                });
                            }
                            else {
                                swal({
                                    title: "Salvo com sucesso",
                                    type: "success",
                                    confirmButtonColor: "#18a689",
                                    confirmButtonText: "Fechar",
                                }, function () {

                                    // redireciona para pagina
                                    var url = '/Simulacao/Simulacao_Editar?IDMedicao=' + document.getElementById('IDMedicao').value;
                                    window.location.href = url;
                                });
                            }

                        }, 100);

                    },
                    error: function (xhr, status, error) {

                        // fim
                        $('.overlay_aguarde').toggle();

                        setTimeout(function () {

                            swal({
                                title: "Erro",
                                text: "Erro ao salvar!",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            });

                        }, 100);

                    }
                });
            });
        };

    </script>
}


