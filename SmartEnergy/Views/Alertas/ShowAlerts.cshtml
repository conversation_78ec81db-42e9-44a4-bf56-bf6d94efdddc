﻿@model IEnumerable<SmartEnergyLib.SQL.UsuarioAlertaDominio>

@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = @SmartEnergy.Resources.MensagensTexts.Alertas;

    // acesso
    int IDTipoAcesso = ViewBag._IDTipoAcesso;
}

<head>
    <title>@SmartEnergy.Resources.MensagensTexts.Alertas</title>
    <style>

        a:link {
            color: #676A6C;
        }

        a:hover {
            color: #AFB0B1;
        }

        .alerta-box-header {
          background-color: #ffffff;
          padding: 30px 20px 20px 20px; }

        .alerta-box-header h2 {
          margin-top: 0; }

    </style>
</head>

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            <div class="panel panel-title">

                <div class="row">
                    <div class="col-lg-8">
                        <div class="alerta-box-header">
                            <h2>
                                <i class="fa fa-bell"></i> @SmartEnergy.Resources.MensagensTexts.Alertas (@ViewBag.NumAlertasNaoLidas)
                            </h2>
                        </div>
                    </div>
                    <div class="col-lg-2">
                        <br />
                        @if (IDTipoAcesso != TIPO_ACESSO.DEMONSTRACAO)
                        {
                            <a id="BotaoExcluirTodos" href="#" onclick="javascript:Reconhecer_Todos();" class="btn btn-success btn-lg pull-left" style="color:#ffffff; width:90%;">Reconhecer Todos</a>
                        }
                    </div>
                    <div class="col-lg-2">
                        <br />
                        @if (IDTipoAcesso != TIPO_ACESSO.DEMONSTRACAO)
                        {
                            <a id="BotaoExcluirTodos" href="#" onclick="javascript:Excluir_Todos();" class="btn btn-success btn-lg pull-left" style="color:#ffffff; width:90%;">Excluir Todos</a>
                        }                            
                    </div>
                </div>

                <div class="panel-body">
                    <table class="table table-striped table-bordered table-hover dataTables-alertas">
                        <thead>
                            <tr>
                                <th>@SmartEnergy.Resources.MensagensTexts.Data</th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.Cliente</th>
                                <th>Gateway | @SmartEnergy.Resources.ContratosCCEETexts.Medicao</th>
                                <th>@SmartEnergy.Resources.MensagensTexts.Alerta</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>

                            @{
                                foreach (UsuarioAlertaDominio alerta in Model)
                                {
                                    string classe_status = "unread";

                                    if (alerta.Status != 0)
                                    {
                                        classe_status = "read";
                                    }

                                    string DataHora = "-";
                                    string DataHora_Sort = "20000101000000";

                                    DataHora = String.Format("{0:G}", alerta.DataHora);
                                    DataHora_Sort = String.Format("{0:yyyyMMddHHmmss}", alerta.DataHora);

                                    <tr class=@classe_status>
                                        <td><span style="display:none;">@DataHora_Sort</span>@DataHora</td>
                                        <td>@alerta.NomeCliente</td>
                                        <td>@alerta.NomeGatewayMedicao</td>
                                        <td>@alerta.Alerta</td>
                                        <td class="link_preto">

                                            @if (IDTipoAcesso != TIPO_ACESSO.DEMONSTRACAO)
                                            {
                                                if (alerta.Status == 0)
                                                {
                                                    <a href="#" onclick="javascript:ReconhecerAlerta(@alerta.IDUsuarioAlerta);" id="BotaoReconhecer" title="@SmartEnergy.Resources.ComumTexts.BotaoReconhecer"><i class="fa fa-check-square-o icones"></i></a>
                                                }
                                                
                                                <a href="#" onclick="javascript:Excluir(@alerta.IDUsuarioAlerta);" id="BotaoExcluir" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-trash icones"></i></a>
                                            }
                                            else
                                            {
                                                <a href="#" id="BotaoReconhecer" title="@SmartEnergy.Resources.ComumTexts.BotaoReconhecer"><i class="fa fa-check-square-o icones"></i></a>
                                            }      
                                                                                      
                                        </td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                    <br /><br /><br />
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/validate")


    <script type="text/javascript">

        $(document).ready(function () {

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "portugues-pre": function (data) {
                    var a = 'a';
                    var e = 'e';
                    var i = 'i';
                    var o = 'o';
                    var u = 'u';
                    var c = 'c';
                    var special_letters = {
                        "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                        "É": e, "é": e, "Ê": e, "ê": e,
                        "Í": i, "í": i, "Î": i, "î": i,
                        "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                        "Ú": u, "ú": u, "Ü": u, "ü": u,
                        "ç": c, "Ç": c
                    };
                    for (var val in special_letters)
                        data = data.split(val).join(special_letters[val]).toLowerCase();
                    return data;
                },
                "portugues-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },
                "portugues-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "numero-pre": function (a) {

                    var x = a;

                    if (a == "-") {
                        x = "-1";
                    }
                    else {
                        x = a.replace(".", "");
                        x = x.replace(",", ".");
                    }

                    return parseFloat(x);
                },

                "numero-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },

                "numero-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            $('.dataTables-alertas').DataTable({
                "iDisplayLength": 13,
                dom: 'ftp',

                "bAutoWidth": false,
                "aoColumnDefs": [
                            { "aTargets": [0], "sType": "portugues" },
                            { "aTargets": [1], "sType": "portugues" },
                            { "aTargets": [2], "sType": "portugues" },
                            { "aTargets": [3], "sType": "portugues" },
                            { "aTargets": [4], "bVisible": true, "bSortable": false, "bSearchable": false },
                ],
                "aoColumns": [
                { sWidth: "10%" },
                { sWidth: "18%" },
                { sWidth: "18%" },
                { sWidth: "44%" },
                { sWidth: "10%" },
                ],
                "order": [[0, "desc"]],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }

            });


        });

        function ReconhecerAlerta(IDUsuarioAlerta) {
            event.stopPropagation();

            // titulo
            titulo = "Deseja reconhecer o Alerta?";

            swal({
                html: true,
                title: titulo,
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#18a689",
                confirmButtonText: "Reconhecer",
                cancelButtonText: "Cancelar",
                closeOnConfirm: false
            }, function () {

                // excluir
                $.ajax(
                {
                    type: 'GET',
                    url: '/Alertas/Alerta_Reconhecer',
                    data: { 'IDUsuarioAlerta': IDUsuarioAlerta },
                    contentType: 'application/html',
                    dataType: 'html',
                    cache: false,
                    async: true,
                    success: function (data) {

                        setTimeout(function () {

                            swal({
                                title: "Reconhecido com sucesso",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // atualiza pagina
                                location.reload();
                            });

                        }, 100);
                    },
                    error: function (response) {

                        setTimeout(function () {

                            swal({
                                title: "Erro",
                                text: "Erro ao reconhecer!",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            });

                        }, 100);

                    }
                });
            });
        };

        function Reconhecer_Todos() {
            event.stopPropagation();

            // titulo
            titulo = "Deseja reconhecer todos os Alertas?";

            swal({
                html: true,
                title: titulo,
                text: "Esta operação não poderá ser desfeita.",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#18a689",
                confirmButtonText: "Reconhecer",
                cancelButtonText: "Cancelar",
                closeOnConfirm: true
            }, function () {

                $.ajax(
                {
                    type: 'GET',
                    url: '/Alertas/Alerta_ReconhecerTodos',
                    dataType: 'html',
                    data: { },
                    cache: false,
                    async: true,
                    success: function (data) {

                        setTimeout(function () {

                            swal({
                                title: "Alertas reconhecidos com sucesso",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // atualiza pagina
                                location.reload();

                            });

                        }, 100);

                    },
                    error: function (xhr, status, error) {

                        setTimeout(function () {

                            swal({
                                title: "Erro",
                                text: "Erro ao reconhecer!",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            });

                        }, 100);

                    }
                });
            });
        };

        function Excluir(IDUsuarioAlerta) {
            event.stopPropagation();

            // titulo
            titulo = "Deseja excluir o Alerta?";

            swal({
                html: true,
                title: titulo,
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#18a689",
                confirmButtonText: "Excluir",
                cancelButtonText: "Cancelar",
                closeOnConfirm: false
            }, function () {

                // excluir
                $.ajax(
                {
                    type: 'GET',
                    url: '/Alertas/Alerta_Excluir',
                    data: { 'IDUsuarioAlerta': IDUsuarioAlerta },
                    contentType: 'application/html',
                    dataType: 'html',
                    cache: false,
                    async: true,
                    success: function (data) {

                        setTimeout(function () {

                            swal({
                                title: "Excluído com sucesso",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // atualiza pagina
                                location.reload();
                            });

                        }, 100);
                    },
                    error: function (response) {

                        setTimeout(function () {

                            swal({
                                title: "Erro",
                                text: "Erro ao excluir!",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            });

                        }, 100);

                    }
                });
            });
        };

        function Excluir_Todos() {
            event.stopPropagation();

            // titulo
            titulo = "Deseja excluir todos os Alertas?";

            swal({
                html: true,
                title: titulo,
                text: "Esta operação não poderá ser desfeita.",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#18a689",
                confirmButtonText: "Excluir",
                cancelButtonText: "Cancelar",
                closeOnConfirm: true
            }, function () {

                $.ajax(
                {
                    type: 'GET',
                    url: '/Alertas/Alerta_ExcluirTodos',
                    dataType: 'html',
                    data: { },
                    cache: false,
                    async: true,
                    success: function (data) {

                        setTimeout(function () {

                            swal({
                                title: "Alertas excluídos com sucesso",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // atualiza pagina
                                location.reload();

                            });

                        }, 100);

                    },
                    error: function (xhr, status, error) {

                        setTimeout(function () {

                            swal({
                                title: "Erro",
                                text: "Erro ao excluir!",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            });

                        }, 100);

                    }
                });
            });
        };

    </script>
}
