﻿@using SmartEnergyLib.SQL
@using System.Globalization
@using SmartEnergyLib.Funcoes

@{
    ViewBag.Title = "Gestor";
}

<!-- Navigation -->
@{

    // le cookies
    ViewBag.DashboardTelaCheia = CookieStore.LeCookie_Bool("DashboardTelaCheia");

    // copia cookies
    bool DashboardTelaCheia = ViewBag.DashboardTelaCheia;

    var classe_db = "";

    if (DashboardTelaCheia)
    {
        classe_db = "top-navigation";
    }
}

<body class=@classe_db>

    <div class="row border-bottom">
        <nav class="navbar navbar-static-top verdeescuro-bg" role="navigation" style="margin-bottom: 0">
            @Html.Partial("_Gestor_Cabecalho")
        </nav>
    </div>

    <div class="wrapper wrapper-content">
        @{
            <div id="atualizar">
                @Html.Partial("_Gestor_Atualizar")
            </div>
        }
    </div>

</body>



@section Styles {
    @Styles.Render("~/plugins/c3Styles")
    @Styles.Render("~/Content/Relatorios.css")
}

@section Scripts {
    @Scripts.Render("~/plugins/d3")
    @Scripts.Render("~/plugins/c3")
    @Scripts.Render("~/plugins/dataTables")
    @Scripts.Render("~/plugins/jqueryui")

    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        $(document).ready(function () {

            // inicio timer das dashboards (a cada 5 minutos = 300000)
            setInterval(Atualiza, 300000);

            // pisca
            setInterval(piscando, 500);

            // atualiza
            Atualiza();


        });

        function Atualiza() {

            $.ajax(
                {
                    type: 'GET',
                    url: '/Monitoracao/_Gestor_Atualizar',
                    dataType: 'html',
                    data: {},
                    cache: false,
                    async: true,
                    success: function (data) {
                        $('#atualizar').html(data);
                    }
                });
        }

        function piscando() {

            blinks = document.getElementsByTagName("blink");
            for (var i = 0; i < blinks.length; i++) {
                if (blinks[i].getAttribute("style") == "VISIBILITY: hidden") {
                    blinks[i].setAttribute("style", "VISIBILITY: visible");
                } else {
                    blinks[i].setAttribute("style", "VISIBILITY: hidden");
                }
            }
        }

    </script>

}


