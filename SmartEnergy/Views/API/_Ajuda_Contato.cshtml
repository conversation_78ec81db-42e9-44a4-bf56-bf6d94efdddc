﻿@using SmartEnergyLib.SQL

@{
    // consultor
    ConsultoresDominio consultor = new ConsultoresDominio();
    consultor = new ConsultoresDominio();

    consultor.IDConsultor = 0;
    consultor.Nome = "GESTAL Automação de Sistemas Ltda.";
    consultor.Email = "<EMAIL>";
    consultor.Telefone1 = "(11) 5080-8200 [Ramal 226]";
    consultor.Celular = "(11) 9.7598-5359";
    consultor.Endereco = "Rua Borges Lagoa, 190";
    consultor.CEP = "04038-000";
    consultor.IDCidade = 5270;
    consultor.Cidade = "";
    consultor.Estado = "";
    consultor.IDEstado = 26;
    consultor.Latitude = 0;
    consultor.Longitude = 0;
}

<h2>Plataforma de Integração Smart Energy</h2>
<br />
<p>A <b>Plataforma de Integração Smart Energy</b> é um conjunto de funções utilizadas na integração de aplicações com o sistema <b>Smart Energy</b>.</p>
<p>Para ter acesso às funções é necessário solicitar uma <b>key</b> para o Departamento Comercial <b>Smart Energy</b>.</p>
<p>Dependendo da contração da chave, as funções terão limitações de tempo entre leituras e número de leituras por dia.</p>
<p>O número limite diário padrão por chave e por medição são 100 requisições. Ultrapassado este limite, a API será bloqueada até o dia seguinte.</p>
<p>Caso precisar aumentar este limite, entre em contato com o Departamento Comercial <b>Smart Energy</b>.</p>

<br /><br />
<p>Para mais informações ou para requisitar a chave de acesso, utilize os contatos abaixo:</p>

<h2>@consultor.Nome</h2><br />
<address>
    <span class="green-section">
        <i class="fa fa-phone"></i><br />@consultor.Telefone1<br /><br />
        <a href="mailto:@consultor.Email" class="green-section"><i class="fa fa-envelope"></i><br />@consultor.Email</a>
    </span>
</address>
