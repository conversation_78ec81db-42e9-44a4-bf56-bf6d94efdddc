﻿@using SmartEnergyLib.Funcoes;
@using System.Globalization

@{
    Layout = null;
    ViewBag.Title = "Plataforma de Integração";
}

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <title>Smart Energy | Plataforma de Integração</title>

    <link href="@Url.Content("~/Scripts/plugins/jquery-ui/jquery-ui.css")" rel="stylesheet" type="text/css" />

    <!-- Primary Inspinia style -->
    @Styles.Render("~/Content/css")
    @Styles.Render("~/font-awesome/css")
    @Styles.Render("~/elusive-icons-2.0.0/css")
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")

    @{
        // tema
        var tema = CookieStore.LeCookie_Int("Tema");
        var tema_background = CookieStore.LeCookie_String("background");
        var tema_background_hover = CookieStore.LeCookie_String("background_hover");
        var tema_background_nav_header = CookieStore.LeCookie_String("background_nav_header");
        var tema_background_panel_title = CookieStore.LeCookie_String("background_panel_title");
    }


    <style>

    :root {
        --tema-background-hover: @tema_background_hover;
        --tema-background: @tema_background;
        --tema-background-nav-header: url('@tema_background_nav_header') no-repeat;
        --tema-background-panel-title: @tema_background_panel_title;
    }

        .cabecalho {
            height: 160px;
            min-width: 400px;
            margin: 10px 0px 0px 0;
            padding: 0px;
            color: #fffff3;
            background-color: transparent;
            border-color: #2F4050;
            font-family: Arial;
        }

        .cabecalho_align img {
            vertical-align: middle;
            text-align: center;
        }

        .linha {
            height: 120px;
            margin-top: 14px;
            padding: 0px;
            clear: both;
            border-color: #2F4050;
            background-color: #2F4050;
        }

        .coluna1 {
            height: 25px;
            border: 0px solid #000000;
            float: left;
            margin-left: 20px;
        }

        .coluna2 {
            height: 25px;
            border: 0px solid #000000;
            float: left;
            margin-top: 0px;
            margin-left: 20px;
        }

        .coluna3 {
            height: 25px;
            border: 0px solid #000000;
            float: right;
        }

        .posicao-titulo {
            padding-top: 0px;
            padding-left: 0px;
        }

        .posicao-texto {
            padding-top: 8px;
            padding-left: 10px;
        }

        .link_preto a:link {
            color: #7E6A6C !important;
        }

        .link_preto a:visited {
            color: #7E6A6C !important;
        }

        .link_preto a:hover {
            color: #a0a0a0 !important;
        }

        .link_preto a:active {
            color: #7E6A6C !important;
        }

        .link_branco a:link {
            color: #ffffff !important;
        }

        .link_branco a:visited {
            color: #ffffff !important;
        }

        .link_branco a:hover {
            color: #a0a0a0 !important;
        }

        .link_branco a:active {
            color: #ffffff !important;
        }
    </style>
</head>

<body class="top-navigation" style="margin-left:30px; margin-right:30px;">

    <div id="wrapper">
        <div class="row border-bottom">
            <nav class="navbar navbar-static-top verdeescuro-bg" role="navigation" style="margin-bottom: 0">
                <div class="container-fluid">
                    <div class="linha cabecalho">
                        <div class="coluna1 some_desktop">
                            <p><img src="http://www.smartenergy.com.br//Logos/LogoSmartEnergy85.png" /></p>
                        </div>

                        <div class="coluna2 link_branco">
                            <h1>Plataforma de Integração Smart Energy</h1>
                            <h3>Sistema de Gerenciamento de Energia Elétrica e Utilidades</h3>
                        </div>

                        <div class="coluna3 link_branco">
                            <a href='#' onclick="Ajuda('Contato')">
                                <i class="fa fa-info-circle fa-4x"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </nav>
        </div>

        <br />

        <div class="row">
            <div class="col-lg-12 link_preto">
                <a href='#' onclick="Ajuda('Contas')">
                    <div class="widget style1 white-bg">
                        <div class="row">
                            <div class="col-xs-1 text-center">
                                <i class="fa fa-list fa-4x"></i>
                            </div>
                            <div class="col-xs-10 text-left posicao-titulo">
                                <h3 class="font-bold">Contas</h3><br />
                                Retorna um JSON com os dados cadastrais das Contas (Cliente).
                            </div>
                            <div class="col-xs-1 text-right">
                                <i class="fa fa-info-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12 link_preto">
                <a href='#' onclick="Ajuda('Usuarios')">
                    <div class="widget style1 white-bg">
                        <div class="row">
                            <div class="col-xs-1 text-center">
                                <i class="fa fa-users fa-4x"></i>
                            </div>
                            <div class="col-xs-10 text-left posicao-titulo">
                                <h3 class="font-bold">Usuários</h3><br />
                                Retorna um JSON com os dados cadastrais dos Usuários.
                            </div>
                            <div class="col-xs-1 text-right">
                                <i class="fa fa-info-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12 link_preto">
                <a href='#' onclick="Ajuda('Empresas')">
                    <div class="widget style1 white-bg">
                        <div class="row">
                            <div class="col-xs-1 text-center">
                                <i class="fa fa-building fa-4x"></i>
                            </div>
                            <div class="col-xs-10 text-left posicao-titulo">
                                <h3 class="font-bold">Empresas</h3><br />
                                Retorna um JSON com os dados cadastrais das Empresas.
                            </div>
                            <div class="col-xs-1 text-right">
                                <i class="fa fa-info-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12 link_preto">
                <a href='#' onclick="Ajuda('Dispositivos')">
                    <div class="widget style1 white-bg">
                        <div class="row">
                            <div class="col-xs-1 text-center">
                                <i class="fa fa-hdd-o fa-4x"></i>
                            </div>
                            <div class="col-xs-10 text-left posicao-titulo">
                                <h3 class="font-bold">Dispositivos</h3><br />
                                Retorna um JSON com os dados cadastrais dos Dispositivos (Gateways).
                            </div>
                            <div class="col-xs-1 text-right">
                                <i class="fa fa-info-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12 link_preto">
                <a href='#' onclick="Ajuda('Equipamentos')">
                    <div class="widget style1 white-bg">
                        <div class="row">
                            <div class="col-xs-1 text-center">
                                <i class="fa fa-dashboard fa-4x"></i>
                            </div>
                            <div class="col-xs-10 text-left posicao-titulo">
                                <h3 class="font-bold">Equipamentos</h3><br />
                                Retorna um JSON com os dados cadastrais dos Equipamentos (Medições).
                            </div>
                            <div class="col-xs-1 text-right">
                                <i class="fa fa-info-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>


        <div class="modal inmodal animated fadeIn" id="ModalAjuda" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-editar-header">
                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only modal-tools-header">Fechar</span></button>
                        <i class="fa fa-info-circle modal-editar-title"></i><span class="modal-editar-title">&nbsp;&nbsp;Plataforma de Integração Smart Energy</span>
                    </div>
                    <div id="Ajuda_resultado" class="modal-body">
                        @Html.Partial("~/Views/API/_Ajuda.cshtml")
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
                    </div>
                </div>
            </div>
        </div>

    </div>

    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")
    @Scripts.Render("~/plugins/slimScroll")
    @Scripts.Render("~/bundles/inspinia")
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/jasnyBootstrap")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        $(document).ready(function () {



        })

        function Ajuda(pagina_ajuda) {

            $.ajax(
                {
                    type: 'GET',
                    url: '/API/_Ajuda',
                    dataType: 'html',
                    data: { 'PaginaAjuda': pagina_ajuda },
                    cache: false,
                    async: true,
                    success: function (data) {

                        $('#Ajuda_resultado').html(data);

                        $("#ModalAjuda").modal("show");
                    }
                });
        }

    </script>
</body>
</html>
