﻿<h2>Hist<PERSON><PERSON>o</h2>
<p>A função <b>Histórico</b> retorna um XML ou JSON com os dados históricos da medição e período solicitado.</p>
<p>Só é possível ter acesso aos dados os usuários que estejam configurados nesta medição e que possuam uma chave de acesso.</p>
<br />

<p><b>Formato da solicitação:</b></p>
<p style="font-size:smaller;">https://www.smartenergy.com.br/API/Historico?<b>Key</b>=AAAA&<b>Medicao</b>=BBBB&<b>Inicio</b>=DD/MM/AAAA HH:mm&<b>Fim</b>=DD/MM/AAAA HH:mm&<b>Formato</b>=FORMATO</p>

<p><b>Key</b>: Chave de Acesso para o usuário</p>
<p><b>Medicao</b>: ID da Medição (formato numérico)</p>
<p><b>Inicio</b>: Data e Hora do primeiro registro desejado</p>
<tab1>Formato: Dia/Mês/Ano Hora:Minuto (DD/MM/AAAA HH:mm)</tab1><br />
<tab1>O minuto deve ser 00, 15, 30 ou 45.</tab1><br />
<tab1>Caso não for múltiplo de 15 minutos, retornará erro.</tab1><br /><br />
<p><b>Fim</b>: Data e Hora do último registro desejado</p>
<tab1>Formato: Dia/Mês/Ano Hora:Minuto (DD/MM/AAAA HH:mm)</tab1><br />
<tab1>O minuto deve ser 00, 15, 30 ou 45.</tab1><br />
<tab1>Caso não for múltiplo de 15 minutos, retornará erro.</tab1><br /><br />
<p><b>Formato</b>: Formato do retorno (XML ou JSON)</p>
<br />

<p><b>Exemplo:</b></p>
<a href="https://www.smartenergy.com.br/API/Historico?Key=1234567890&Medicao=1&Inicio=01/01/2021 08:15&Fim=01/01/2021 08:30&Formato=XML" target="_blank"><span style="font-size:smaller">https://www.smartenergy.com.br/API/Historico?<b>Key</b>=1234567890&<b>Medicao</b>=1&<b>Inicio</b>=01/01/2021 08:15&<b>Fim</b>=01/01/2021 08:30&<b>Formato</b>=XML</span></a>

<br /><br />

<p><b>O retorno dependerá do tipo da medição solicitada:</b></p>
<b>Energia</b>: ID da Medição | Data e Hora do Registro | Demanda Ativa (kW) | Demanta Reativa (kVar) | Fator de Potência | Período (P, FPI ou FPC)<br />
<b>Utilidades</b>: ID da Medição | Data e Hora do Registro | Valor<br />
<b>Analógica</b>: ID da Medição | Data e Hora do Registro | Médio | Mínimo | Máximo<br />
<b>Ciclômetro</b>: ID da Medição | Data e Hora do Registro | Ciclômetro<br />

<br />
Em caso de algum erro o retorno apresentará um texto descrevendo a falha.
