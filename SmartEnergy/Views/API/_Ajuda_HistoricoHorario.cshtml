﻿<h2>His<PERSON><PERSON><PERSON><PERSON></h2>
<p>A função <b>Histórico Horá<PERSON></b> retorna um XML ou JSON com os dados históricos horários da medição de Energia e período solicitado.</p>
<p>Só é possível ter acesso aos dados os usuários que estejam configurados nesta medição e que possuam uma chave de acesso.</p>
<br />

<p><b>Formato da solicitação:</b></p>
<p style="font-size:smaller;">https://www.smartenergy.com.br/API/HistoricoHorario?<b>Key</b>=AAAA&<b>Medicao</b>=BBBB&<b>Inicio</b>=DD/MM/AAAA HH:00&<b>Fim</b>=DD/MM/AAAA HH:00&<b>Formato</b>=FORMATO</p>

<p><b>Key</b>: Chave de Acesso para o usuário</p>
<p><b>Medicao</b>: ID da Medição de Energia (formato numérico)</p>
<p><b>Inicio</b>: Data e Hora do primeiro registro desejado (formato Dia/Mês/Ano Hora:Minuto DD/MM/AAAA HH:00)</p>
<p><b>Fim</b>: Data e Hora do último registro desejado (formato Dia/Mês/Ano Hora:Minuto DD/MM/AAAA HH:00)</p>
<p><b>Formato</b>: Formato do retorno (XML ou JSON)</p>
<br />

<p><b>Exemplo:</b></p>
<a href="https://www.smartenergy.com.br/API/HistoricoHorario?Key=1234567890&Medicao=1&Inicio=01/01/2021 08:00&Fim=01/01/2021 10:00&Formato=XML" target="_blank"><span style="font-size:smaller">https://www.smartenergy.com.br/API/HistoricoHorario?<b>Key</b>=1234567890&<b>Medicao</b>=1&<b>Inicio</b>=01/01/2021 08:00&<b>Fim</b>=01/01/2021 10:00&<b>Formato</b>=XML</span></a>

<br /><br />

<p><b>O retorno contém:</b></p>
<tab1><b>ID da Medição</b></tab1><br />
<tab1><b>Data e Hora do Registro</b></tab1><br />
<tab1><b>Consumo Ativo (kWh)</b></tab1><br />
<tab1><b>Consumo Reativo (kVarh)</b></tab1><br />
<tab1><b>Fator de Potência</b></tab1><br />
<tab1><b>Período (P, FPI ou FPC)</b></tab1><br />

<br />
Em caso de algum erro o retorno apresentará um texto descrevendo a falha.
