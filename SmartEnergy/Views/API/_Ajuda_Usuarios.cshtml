﻿<h2>Usuários</h2>
<p>A API <b>Usuários</b> retorna um JSON com os dados cadastrais dos usuários.</p>
<p>Só é possível ter acesso aos dados os usuários que possuam uma chave de acesso.</p>
<br />

<p><b>Formato da solicitação:</b></p>
<p style="font-size:smaller;">https://www.smartenergy.com.br/API/Usuarios?<b>Key</b>=AAAA&<b>id</b>=BBBB&<b>_q</b>=CCCC&<b>_sort</b>=id&<b>_order</b>=asc&<b>_limit</b>=10&<b>_page</b>=1</p>

<p><b>Key</b>: Cha<PERSON></p>
<p><b>id</b>: ID do Usuário ou Conta (Cliente)</p>
<p><b>_q</b>: Busca os usuários pelo campo nome</p>
<p><b>_sort</b>: Define o campo de ordenação da lista (id, name ou account)</p>
<p><b>_order</b>: Define a ordem da lista com base no campo definido (asc ou desc)</p>
<p><b>_limit</b>: Quantidade de itens por página</p>
<p><b>_page</b>: Página a ser carregada</p>
<br />

<p><b>Exemplo de busca por id Usuário:</b></p>
<a href="https://www.smartenergy.com.br/API/Usuarios?Key=**********&id=1" target="_blank"><span style="font-size:smaller">https://www.smartenergy.com.br/API/Usuarios?<b>Key</b>=**********&<b>id</b>=1</span></a>
<br />
<br />

<p><b>Exemplo de busca por id Conta (Cliente):</b></p>
<a href="https://www.smartenergy.com.br/API/Usuarios?Key=**********&id=1&_sort=account" target="_blank"><span style="font-size:smaller">https://www.smartenergy.com.br/API/Usuarios?<b>Key</b>=**********&<b>id</b>=1&<b>_sort</b>=account</span></a>
<br />
<br />

<p><b>Exemplo de busca por nome:</b></p>
<a href="https://www.smartenergy.com.br/API/Usuarios?Key=**********&_q=Smart&_sort=name&_order=asc&_limit=10&_page=1" target="_blank"><span style="font-size:smaller">https://www.smartenergy.com.br/API/Usuarios?<b>Key</b>=**********&<b>_q</b>=Smart&<b>_sort</b>=name&<b>_order</b>=asc&<b>_limit</b>=10&<b>_page</b>=1</span></a>

<br />
<br />

<p><b>Retorno do usuário solicitado:</b></p>
<b>id</b>: ID do Usuário
<br />
<b>name</b>: Nome do Usuário
<br />
<b>email</b>: Email do Usuário
<br />
<b>active</b>: Status do Usuário (Ativo ou Bloqueado)
<br />
<b>admin</b>: Indica se é administrador da conta (admin = 1) ou não (admin = 0)
<br />
<b>type</b>: Tipo do Acesso (0/4/5/6: Administradores GESTAL ||| 1: Cliente Administrador ||| 2: Cliente Operador ||| 3/7/8: Gestor)
<br />
<b>account_id</b>: ID da Conta (Cliente) que o usuário é associado
<br />

<br />
Em caso de algum erro o retorno apresentará um texto descrevendo a falha.
