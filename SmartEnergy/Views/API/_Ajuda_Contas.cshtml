﻿<h2>Contas</h2>
<p>A API <b>Contas</b> retorna um JSON com os dados cadastrais das Contas (Clientes).</p>
<p>Só é possível ter acesso aos dados os usuários que possuam uma chave de acesso.</p>
<br />

<p><b>Formato da solicitação:</b></p>
<p style="font-size:smaller;">https://www.smartenergy.com.br/API/Contas?<b>Key</b>=AAAA&<b>id</b>=BBBB&<b>_q</b>=CCCC&<b>_sort</b>=id&<b>_order</b>=asc&<b>_limit</b>=10&<b>_page</b>=1</p>

<p><b>Key</b>: Chave de <PERSON>sso</p>
<p><b>id</b>: ID do Cliente (Conta)</p>
<p><b>_q</b>: Busca as contas pelo campo nome</p>
<p><b>_sort</b>: Define o campo de ordenação da lista (id ou name)</p>
<p><b>_order</b>: Define a ordem da lista com base no campo definido (asc ou desc)</p>
<p><b>_limit</b>: Quantidade de itens por página</p>
<p><b>_page</b>: Página a ser carregada</p>
<br />

<p><b>Exemplo de busca por id:</b></p>
<a href="https://www.smartenergy.com.br/API/Contas?Key=1234567890&id=1" target="_blank"><span style="font-size:smaller">https://www.smartenergy.com.br/API/Contas?<b>Key</b>=1234567890&<b>id</b>=1</span></a>
<br />
<br />

<p><b>Exemplo de busca por nome:</b></p>
<a href="https://www.smartenergy.com.br/API/Contas?Key=1234567890&_q=Smart&_sort=name&_order=asc&_limit=10&_page=1" target="_blank"><span style="font-size:smaller">https://www.smartenergy.com.br/API/Contas?<b>Key</b>=1234567890&<b>_q</b>=Smart&<b>_sort</b>=name&<b>_order</b>=asc&<b>_limit</b>=10&<b>_page</b>=1</span></a>

<br />
<br />

<p><b>Retorno da conta solicitada:</b></p>
<b>id</b>: ID da Conta (Cliente)
<br />
<b>status</b>: Status da Conta (Ativo ou Inativo)
<br />
<b>name</b>: Nome da Conta
<br />

<br />
Em caso de algum erro o retorno apresentará um texto descrevendo a falha.
