﻿<h2>Fatura das Medições</h2>
<p>A função <b>Fatura das Medições</b> retorna um XML ou JSON com a fatura das medições de energia no período solicitado.</p>
<p>Só é possível visualizar as faturas das medições que o usuário tenha permissão e que possua uma chave de acesso.</p>
<br />

<p><b>Formato da solicitação:</b></p>
<p style="font-size:smaller;">https://www.smartenergy.com.br/API/MedicoesFatura?<b>Key</b>=AAAA&<b>Inicio</b>=DD/MM/AAAA HH:mm&<b>Fim</b>=DD/MM/AAAA HH:mm&<b>TipoData</b>=0&<b>Formato</b>=FORMATO</p>

<p><b>Key</b>: Chave de Acesso para o usuário</p>
<p><b>Inicio</b>: Data e Hora do início da fatura (formato Dia/Mês/Ano Hora:Minuto DD/MM/AAAA HH:mm)</p>
<p><b>Fim</b>: Data e Hora do fim da fatura (formato Dia/Mês/Ano Hora:Minuto DD/MM/AAAA HH:mm)</p>
<p><b>TipoData</b>: Indica se deve utilizar as datas de Reposição de Demanda mais próximas da solicitação (0) ou o período exato (1)</p>
<p><b>Formato</b>: Formato do retorno (XML ou JSON)</p>
<br />

<p><b>Exemplo:</b></p>
<a href="https://www.smartenergy.com.br/API/MedicoesFatura?Key=1234567890&Inicio=01/01/2021 00:00&Fim=01/02/2021 00:00&TipoData=1&Formato=XML" target="_blank"><span style="font-size:smaller">https://www.smartenergy.com.br/API/MedicoesFatura?<b>Key</b>=1234567890&<b>Inicio</b>=01/01/2021 00:00&<b>Fim</b>=01/02/2021 00:00&<b>TipoData</b>=1&<b>Formato</b>=XML</span></a>

<br /><br />

<p><b>O retorno contém:</b></p>
<tab1><b>ID do Cliente</b></tab1><br />
<tab1><b>Nome do Cliente</b></tab1><br />
<tab1><b>ID da Medição</b></tab1><br />
<tab1><b>Nome da Medição</b></tab1><br />
<tab1><b>Nº do Ativo</b></tab1><br />
<tab1><b>Resultado</b></tab1><br />
<tab1><b>Data Início</b></tab1><br />
<tab1><b>Data Fim</b></tab1><br />
<tab1><b>Estrutura Tarifária</b></tab1><br />
<tab1><b>Distribuidora</b></tab1><br />
<tab1><b>Constante</b></tab1><br />
<tab1><b>Demanda Contrato Ponta (kW)</b></tab1><br />
<tab1><b>Demanda Contrato Fora de Ponta (kW)</b></tab1><br />
<tab1><b>Demanda Registrada Ponta (kW)</b></tab1><br />
<tab1><b>Demanda Registrada Fora de Ponta (kW)</b></tab1><br />
<tab1><b>Ultrapassagem de Demanda Ponta (kW)</b></tab1><br />
<tab1><b>Ultrapassagem de Demanda Fora de Ponta (kW)</b></tab1><br />
<tab1><b>Consumo Ponta (kWh)</b></tab1><br />
<tab1><b>Consumo Fora de Ponta (kWh)</b></tab1><br />
<tab1><b>Consumo Total (kWh)</b></tab1><br />
<tab1><b>Multa de Demanda (R$)</b></tab1><br />
<tab1><b>Multa de Fator de Potência (R$)</b></tab1><br />
<tab1><b>Total da Fatura (R$)</b></tab1><br />

<br />
Em caso de algum erro o retorno apresentará um texto descrevendo a falha.
