﻿<h2>Empresas</h2>
<p>A API <b>Empresas</b> retorna um JSON com os dados cadastrais das Empresas.</p>
<p>Só é possível ter acesso aos dados os usuários que possuam uma chave de acesso.</p>
<br />

<p><b>Formato da solicitação:</b></p>
<p style="font-size:smaller;">https://www.smartenergy.com.br/API/Empresas?<b>Key</b>=AAAA&<b>id</b>=BBBB&<b>_q</b>=CCCC&<b>_sort</b>=id&<b>_order</b>=asc&<b>_limit</b>=10&<b>_page</b>=1</p>

<p><b>Key</b>: Chave de <PERSON></p>
<p><b>id</b>: ID da Empresa ou Conta (Cliente)</p>
<p><b>_q</b>: Busca as empresas pelo campo nome</p>
<p><b>_sort</b>: Define o campo de ordenação da lista (id, name ou account)</p>
<p><b>_order</b>: Define a ordem da lista com base no campo definido (asc ou desc)</p>
<p><b>_limit</b>: Quantidade de itens por página</p>
<p><b>_page</b>: Página a ser carregada</p>
<br />

<p><b>Exemplo de busca por id Empresa:</b></p>
<a href="https://www.smartenergy.com.br/API/Empresas?Key=**********&id=1" target="_blank"><span style="font-size:smaller">https://www.smartenergy.com.br/API/Empresas?<b>Key</b>=**********&<b>id</b>=1</span></a>
<br />
<br />

<p><b>Exemplo de busca por id Conta (Cliente):</b></p>
<a href="https://www.smartenergy.com.br/API/Empresas?Key=**********&id=1&_sort=account" target="_blank"><span style="font-size:smaller">https://www.smartenergy.com.br/API/Empresas?<b>Key</b>=**********&<b>id</b>=1&<b>_sort</b>=account</span></a>
<br />
<br />

<p><b>Exemplo de busca por nome:</b></p>
<a href="https://www.smartenergy.com.br/API/Empresas?Key=**********&_q=Smart&_sort=name&_order=asc&_limit=10&_page=1" target="_blank"><span style="font-size:smaller">https://www.smartenergy.com.br/API/Empresas?<b>Key</b>=**********&<b>_q</b>=Smart&<b>_sort</b>=name&<b>_order</b>=asc&<b>_limit</b>=10&<b>_page</b>=1</span></a>

<br />
<br />

<p><b>Retorno da empresa solicitada:</b></p>
<b>id</b>: ID da Empresa
<br />
<b>name</b>: Nome da Empresa
<br />
<b>cep</b>: CEP da Empresa
<br />
<b>endereco</b>: Endereço da Empresa
<br />
<b>cidade</b>: Cidade da Empresa
<br />
<b>estado</b>: Estado da Empresa
<br />
<b>parent_id</b>: ID da Empresa que esta empresa é associada
<br />
<b>account_id</b>: ID da Conta (Cliente) que a empresa é associada
<br />

<br />
Em caso de algum erro o retorno apresentará um texto descrevendo a falha.
