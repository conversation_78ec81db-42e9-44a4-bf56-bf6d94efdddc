﻿<h2>Consumo das Medições</h2>
<p>A função <b>Consumo das Medições</b> retorna um XML ou JSON com os consumos das medições de energia no período solicitado.</p>
<p>Só é possível ler os consumos das medições que o usuário tenha permissão e que possua uma chave de acesso.</p>
<br />

<p><b>Formato da solicitação:</b></p>
<p style="font-size:smaller;">https://www.smartenergy.com.br/API/MedicoesConsumo?<b>Key</b>=AAAA&<b>Inicio</b>=DD/MM/AAAA HH:mm&<b>Fim</b>=DD/MM/AAAA HH:mm&<b>Formato</b>=FORMATO</p>

<p><b>Key</b>: Chave de Acesso para o usuário</p>
<p><b>Inicio</b>: Data e Hora do início da contabilização do consumo (formato Dia/Mês/Ano Hora:Minuto DD/MM/AAAA HH:mm)</p>
<p><b>Fim</b>: Data e Hora do fim da contabilização do consumo (formato Dia/Mês/Ano Hora:Minuto DD/MM/AAAA HH:mm)</p>
<p><b>Formato</b>: Formato do retorno (XML ou JSON)</p>
<br />

<p><b>Exemplo:</b></p>
<a href="https://www.smartenergy.com.br/API/MedicoesConsumo?Key=1234567890&Inicio=01/01/2021 00:00&Fim=02/01/2021 00:00&Formato=XML" target="_blank"><span style="font-size:smaller">https://www.smartenergy.com.br/API/MedicoesConsumo?<b>Key</b>=1234567890&<b>Inicio</b>=01/01/2021 00:00&<b>Fim</b>=02/01/2021 00:00&<b>Formato</b>=XML</span></a>

<br /><br />

<p><b>O retorno contém:</b></p>
<tab1><b>ID do Cliente</b></tab1><br />
<tab1><b>Nome do Cliente</b></tab1><br />
<tab1><b>ID da Medição</b></tab1><br />
<tab1><b>Nome da Medição</b></tab1><br />
<tab1><b>Nº do Ativo</b></tab1><br />
<tab1><b>Consumo (kWh)</b></tab1><br />

<br />
Em caso de algum erro o retorno apresentará um texto descrevendo a falha.
