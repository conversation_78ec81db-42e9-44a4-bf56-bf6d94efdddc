﻿@model IEnumerable<SmartEnergyLib.SQL.MetasConsumoDominio>

@using System.Globalization
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.MetasTexts.Metas + " " + @SmartEnergy.Resources.MetasTexts.Historico;
}

<style>
    .icones {
        padding: 0px 5px;
        font-size: 18px;
        float: left;
    }

    .titulo {
        margin: 0;
        margin-left: 0px;
        margin-top: 20px;
        margin-bottom: 15px;
        padding-left: 15px;
        padding-top: 5px;
        padding-bottom: 5px;
        font-size: 12px;
        font-weight: bold;
        border-radius: 10px;
        float: left;
    }

    .link_branco a:link {
        color: #ffffff !important;
    }

    .link_branco a:visited {
        color: #ffffff !important;
    }

    .link_branco a:hover {
        color: #f0f0f0 !important;
    }

    .link_branco a:active {
        color: #ffffff !important;
    }

    .link_preto a:link {
        color: #7E6A6C !important;
    }

    .link_preto a:visited {
        color: #7E6A6C !important;
    }

    .link_preto a:hover {
        color: #a0a0a0 !important;
    }

    .link_preto a:active {
        color: #7E6A6C !important;
    }

    .bar {
        height: 18px;
        background: #1AB394;
    }
</style>

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.MetasTexts.Metas @SmartEnergy.Resources.MetasTexts.Historico</h4>
                    <div class="pull-right dashboard-tools link_branco" style="margin-top:-8px;">
                        <h4>
                            <a href='@("/Metas/Metas_Historico_Editar?IDMeta=0" + "&IDMedicao=" + ViewBag.IDMedicao)' id="BotaoAdicionar" title="@SmartEnergy.Resources.ComumTexts.BotaoInserir"><i class="fa fa-plus"></i></a>
                        </h4>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-lg-12">
                            <label class="control-label">&nbsp;@SmartEnergy.Resources.MetasTexts.Medicao</label>
                            <input type="text" class="form-control" value="@ViewBag.NomeMedicao" disabled>
                        </div>
                    </div>

                    <br /><br />
                    <div class="row">
                        <div class="col-lg-12">
                            <table class="table table-striped table-bordered table-hover dataTables-historicoMetas">
                                <thead>
                                    <tr>
                                        <th>@SmartEnergy.Resources.MetasTexts.DataIni</th>
                                        <th>@SmartEnergy.Resources.MetasTexts.DataFim</th>
                                        <th>@SmartEnergy.Resources.MetasTexts.MetaConsumo</th>
                                        <th>@SmartEnergy.Resources.MetasTexts.MetaFatura</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>

                                    @foreach (MetasConsumoDominio meta in Model)
                                    {
                                        // copia data e hora
                                        var DataHoraIni = String.Format("{0:d}", meta.Ini);
                                        var DataHoraIni_Sort = String.Format("{0:yyyyMMddHHmmss}", meta.Ini);

                                        var DataHoraFim = String.Format("{0:d}", meta.Fim);
                                        var DataHoraFim_Sort = String.Format("{0:yyyyMMddHHmmss}", meta.Fim);

                                        <tr>
                                            <td><span style="display:none;">@DataHoraIni_Sort</span>@DataHoraIni</td>
                                            <td><span style="display:none;">@DataHoraFim_Sort</span>@DataHoraFim</td>
                                            <td>@meta.MetaKWhP</td>
                                            <td>@String.Format("{0:C}", meta.MetaFatura)</td>
                                            <td class="link_preto">
                                                <a href='@("/Metas/Metas_Historico_Editar?IDMeta=" + meta.ID + "&IDMedicao=" + ViewBag.IDMedicao)' title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"><i class="fa fa-edit icones"></i></a>

                                                @if (ViewBag.Permissao == 0 || ViewBag.Permissao == 1 || ViewBag.Permissao == 4 || ViewBag.Permissao == 5)
                                                {
                                                    <a href="#" onclick="javascript:Excluir(@meta.ID,'@DataHoraIni.ToString()','@DataHoraFim.ToString()');" id="BotaoExcluir" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-times icones"></i></a>
                                                }
                                            </td>
                                        </tr>
                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/validate")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        var jqXHRData;

        $(document).ready(function () {

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "portugues-pre": function (data) {
                    var a = 'a';
                    var e = 'e';
                    var i = 'i';
                    var o = 'o';
                    var u = 'u';
                    var c = 'c';
                    var special_letters = {
                        "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                        "É": e, "é": e, "Ê": e, "ê": e,
                        "Í": i, "í": i, "Î": i, "î": i,
                        "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                        "Ú": u, "ú": u, "Ü": u, "ü": u,
                        "ç": c, "Ç": c
                    };
                    for (var val in special_letters)
                        data = data.split(val).join(special_letters[val]).toLowerCase();
                    return data;
                },
                "portugues-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },
                "portugues-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "numero-pre": function (a) {
                    var x = (a == "-") ? 0 : a.replace(/,/, ".");
                    return parseFloat(x);
                },

                "numero-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },

                "numero-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend( jQuery.fn.dataTableExt.oSort, {
                "currency-pre": function ( a ) {
                    a = (a==="-") ? 0 : a.replace( /[^\d\-\,]/g, "" );
                    return parseFloat( a );
                },

                "currency-asc": function ( a, b ) {
                    return a - b;
                },

                "currency-desc": function ( a, b ) {
                    return b - a;
                }
            });

            $('.dataTables-historicoMetas').DataTable({
                "iDisplayLength": 20,
                dom: 'ftp',

                "bAutoWidth": false,
                "aoColumnDefs": [
                            { "aTargets": [0], "sType": "portugues" },
                            { "aTargets": [1], "sType": "portugues" },
                            { "aTargets": [2], "sType": "numero" },
                            { "aTargets": [3], "sType": "currency" },
                            {
                                "aTargets": [4],
                                "bVisible": true,
                                "bSortable": false,
                                "bSearchable": false,
                            },
                ],

                "aoColumns": [
                { sWidth: "23%" },
                { sWidth: "23%" },
                { sWidth: "23%" },
                { sWidth: "23%" },
                { sWidth: "8%" },
                ],
                'order': [0, 'desc'],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }

            });

            'use strict';

            // desabilita campos
            disableAll();
        });

        function disableAll() {

            var permissao = Math.ceil(@ViewBag.Permissao);

            // verifica permissao
            switch(permissao)
            {
                case 0:     // 0 - permissao de Admin: ve e escreve em tudo
                case 1:     // 1 - permissao de Gerente: ve tudo e escreve parte
                case 4:     // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
                case 5:     // 5 - permissao de Consultor

                    break;

                default:
                case 2:     // 2 - permissao de Operador: ve tudo e nao pode escrever
                case 3:     // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                    document.getElementById("BotaoAdicionar").style.visibility = "hidden";
                    break;
            }
        }

        function Excluir(IDMeta, dataIni, dataFim) {
            event.stopPropagation();

            // titulo
            titulo = "Deseja excluir o histórico?<br/> Data Inicial: " + dataIni + "<br/>Data Final: " + dataFim;

            swal({
                html: true,
                title: titulo,
                text: "Esta operação não poderá ser desfeita.",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#18a689",
                confirmButtonText: "Excluir",
                cancelButtonText: "Cancelar",
                closeOnConfirm: false
            }, function () {

                // excluir
                $.ajax(
                {
                    type: 'GET',
                    url: '/Metas/Metas_Historico_Excluir',
                    data: { 'IDMeta': IDMeta },
                    contentType: 'application/html',
                    dataType: 'html',
                    cache: false,
                    async: true,
                    success: function (data) {

                        setTimeout(function () {

                            swal({
                                title: "Excluído com sucesso",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // atualiza pagina
                                location.reload();
                            });

                        }, 100);
                    },
                    error: function (response) {

                        setTimeout(function () {

                            swal({
                                title: "Erro",
                                text: "Erro ao excluir!",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            });

                        }, 100);

                    }
                });
            });
        };

    </script>
}


