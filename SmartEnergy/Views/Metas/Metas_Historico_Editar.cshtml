﻿@model SmartEnergyLib.SQL.MetasConsumoDominio

@using System.Globalization
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.MetasTexts.Meta + " " + @SmartEnergy.Resources.MetasTexts.Historico;
}

<style>
    .icones {
        padding: 0px 5px;
        font-size: 18px;
        float: left;
    }

    .titulo {
        margin: 0;
        margin-left: 0px;
        margin-top: 20px;
        margin-bottom: 15px;
        padding-left: 15px;
        padding-top: 5px;
        padding-bottom: 5px;
        font-size: 12px;
        font-weight: bold;
        border-radius: 10px;
        float: left;
    }

        .overlay_aguarde {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.45);
        z-index: 9999;
        color: white;
        display: inline-block;
        border-radius: 6px;
        margin-bottom: 20px;
        margin-left: 14px;
        margin-right: 14px;
    }

    .sk-spinner-wave div {
        background-color: #ffffff !important;
    }
    
    .spinner-aguarde {
        margin: 0;
        position: absolute;
        top: 50%;
        left: 50%;
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
    }

</style>

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("Metas_Historico_Editar", "MetasHistorico", FormMethod.Post, new { id = "form", role = "form" }))
            {
                @Html.Hidden("ID", Model.ID)
                @Html.Hidden("IDMedicao", Model.IDMedicao)
                @Html.Hidden("IDCliente", Model.IDCliente)
                
                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>@SmartEnergy.Resources.MetasTexts.Meta @SmartEnergy.Resources.MetasTexts.Historico</h4>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-offset-10 col-lg-2">
                                <div class="ibox-tools">
                                    <a href='@("/Metas/Metas_Historico?IDMedicao=" + Model.IDMedicao)' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                    <button type="button" class="btn btn-primary" id="BotaoSalvar" onclick="Salvar();">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-12">
                                <label class="control-label">&nbsp;@SmartEnergy.Resources.MetasTexts.Medicao</label>
                                <input type="text" class="form-control" id="NomeMedicao" value="@ViewBag.NomeMedicao" disabled>
                            </div>
                        </div>
                        <br /><br />
                        <div class="row">
                            <div class="col-lg-2">
                                <label class="control-label">&nbsp;@SmartEnergy.Resources.MetasTexts.DataIni</label>
                                <div class="input-group date">
                                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>@Html.TextBoxFor(model => model.Ini, "{0:dd/MM/yyyy}", new { @class = "form-control", @maxlength = "10" })
                                </div>

                                @{
                                    var DataIniStr = String.Format("{0:d}", Model.Ini);
                                }
                                @Html.Hidden("DataIniStr", DataIniStr)
                            </div>
                            <div class="col-lg-2">
                                <label class="control-label">&nbsp;@SmartEnergy.Resources.MetasTexts.DataFim</label>
                                <div class="input-group date">
                                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>@Html.TextBoxFor(model => model.Fim, "{0:dd/MM/yyyy}", new { @class = "form-control", @maxlength = "10" })
                                </div>

                                @{
                                    var DataFimStr = String.Format("{0:d}", Model.Fim);
                                }
                                @Html.Hidden("DataFimStr", DataFimStr)
                            </div>
                        </div>
                        <br /><br />
                        <div class="row">
                            <div class="form-group col-lg-2">
                                <label class="control-label">&nbsp;@SmartEnergy.Resources.MetasTexts.MetaConsumo</label><br />
                                @Html.TextBoxFor(model => model.MetaKWhP, new { @class = "form-control", @maxlength = "10" })
                            </div>
                            <div class="form-group col-lg-2">
                                <label class="control-label">&nbsp;@SmartEnergy.Resources.MetasTexts.MetaFatura</label><br />
                                @Html.TextBoxFor(model => model.MetaFatura, new { @class = "form-control", @maxlength = "10" })
                            </div>
                        </div>
                        <br />
                    </div>

                    <div class="overlay_aguarde" style="display: none">
                        <div class="spinner-aguarde">
                            <div class="sk-spinner sk-spinner-wandering-cubes">
                                <div class="sk-spinner sk-spinner-wave">
                                    <div class="sk-rect1 text-ligh"></div>
                                    <div class="sk-rect2"></div>
                                    <div class="sk-rect3"></div>
                                    <div class="sk-rect4"></div>
                                    <div class="sk-rect5"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

            }
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/Content/plugins/dataTables/dataTablesStyles")
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/datetimePickerStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/dataTables")
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/datetimePicker")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

    $(document).ready(function () {

        $('#Ini').datetimepicker({
            locale: 'pt-BR',
            format: 'DD/MM/YYYY',
            showTodayButton: true,
            allowInputToggle: true
        });

        $('#Fim').datetimepicker({
            locale: 'pt-BR',
            format: 'DD/MM/YYYY',
            showTodayButton: true,
            allowInputToggle: true
        });

        //
        // VALIDACAO DOS CAMPOS
        //

        jQuery.validator.addMethod("alphanumeric", function (value, element) {
            return this.optional(element) || /^[\'\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\+\-\_\(\)," "]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

        jQuery.validator.addMethod("numeric", function (value, element) {
            return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.numeric");

        $("#form").validate({
            rules: {
                Data: { required: true },
                Resolucao: { required: true },
                Tarc: { required: true, numeric: true },
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            }
        });

        // desabilita campos
        disableAll();
    });

    function disableAll() {

        var permissao = Math.ceil(@ViewBag.Permissao);

        // verifica permissao
        switch (permissao) {
            case 0:     // 0 - permissao de Admin: ve e escreve em tudo
            case 1:     // 1 - permissao de Gerente: ve tudo e escreve parte
            case 4:     // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
            case 5:     // 5 - permissao de Consultor

                break;

            default:
            case 2:     // 2 - permissao de Operador: ve tudo e nao pode escrever
            case 3:     // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                $("#BotaoSalvar").attr('disabled', true);
                $("#Tarc").attr('disabled', true);

                break;
        }
    }

    function Salvar() {

        var $form = $('#form');

        // verifica se entradas sao validas
        if (!$form.valid()) return false;

        swal({
            title: "Deseja salvar?",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#1ab394",
            confirmButtonText: "Salvar",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {
            event.stopPropagation();

            // aguarde
            $('.overlay_aguarde').toggle();

            // retiro campos desabilitados, pois nao envia via POST
            $(":disabled", $('#form')).removeAttr("disabled");

            $.ajax({
                url: '/Metas/Metas_Historico_Salvar',
                data: $form.serialize(),
                type: 'POST',
                contentType: "application/x-www-form-urlencoded; charset=UTF-8",
                success: function (data) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {
                            swal({
                                title: "Erro",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // desabilito novamente os campos
                                $("#NomeMedicao").attr('disabled', true);

                            });
                        }
                        else {
                            swal({
                                title: "Salvo com sucesso",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                var url = "/Metas/Metas_Historico?IDMedicao=" + document.getElementById('IDMedicao').value;
                                window.location.href = url;
                            });
                        }

                    }, 100);

                },
                error: function (xhr, status, error) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao salvar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });
                        

                    }, 100);

                }
            });
        });
    };

    </script>
}

