﻿@using System.Globalization

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.Intro;
    
    int IDTipoAcesso = ViewBag._IDTipoAcesso;
}

<style>
    .modal {
        text-align: center;
        padding: 0 !important;
    }

    .modal:before {
        content: '';
        display: inline-block;
        height: 100%;
        vertical-align: middle;
        margin-right: -4px; 
    }

    .modal-dialog {
        display: inline-block;
        text-align: left;
        vertical-align: middle;
    }

    .vcenter {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);    
    }

</style>

<div class="row">
    <div class="col-lg-12 vcenter">
        <img src="/Images/Login/LogoSmartEnergy85_transp.png" class="img-responsive center-block" /><br />
        <h3 class="font-bold" style="color:#233545; text-align:center">Gerenciamento e Supervisão de Energia Elétrica e Utilidades</h3>
    </div>
</div>

<div class="modal inmodal animated fadeIn" id="ModalIntro" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">

                @{
                    if (IDTipoAcesso != 10)
                    {
                        <div class="pull-right dashboard-tools" style="margin-top:-4px;">
                            <button type="button" class="btn btn-rounded btn-primary" style="font-weight:500;" onclick="StopIntro();"><i class="fa fa-check-square-o"></i>&nbsp;&nbsp;Não apresentar a Introdução novamente</button>
                        </div>
                    }
                    else
                    {
                        <div class="pull-right dashboard-tools" style="margin-top:-4px;">
                            <button type="button" class="btn btn-rounded btn-primary" style="font-weight:500;" onclick="GoDemo();"><i class="fa fa-mail-reply-all"></i>&nbsp;&nbsp;Navegar pelo Smart Energy</button>
                        </div>
                    }
                }

                <h2 class="font-bold">Bem-Vindo ao Smart Energy</h2><br />

                <p>
                    O SMART ENERGY é um serviço de GESTÃO DE ENERGIA e UTILIDADES via Internet contemplando armazenamento de históricos, supervisão e geração de relatórios e análises.
                </p>
                <p>
                    É a solução mais adequada para aplicações que requerem acompanhamentos centralizados da insumos energéticos com baixo custo, alta disponibilidade e confiabilidade.
                    Focaliza a gestão sobre unidades consumidoras distribuídas, onde a Internet se apresenta com a melhor relação custo/benefício.
                </p>
                <p>Abaixo apresentamos a estrutura do sistema Smart Energy. Em qualquer momento e em caso de dúvida é possível visualizar a tela de <b>Ajuda</b> pressionando o botão <i class="fa fa-info-circle"></i> localizado no canto superior direito da tela.</p><br />
                <img src="/Imagens/Ajuda/Ajuda_Intro.png" class="img-responsive center-block" /><br />
                <b>(1)</b> Logomarca e Nome do Cliente.<br />
                <b>(1)</b> Grupo de Unidades da qual a medição pertence.<br />
                <b>(1)</b> Unidade da qual a medição pertence.<br />
                <b>(1)</b> Nome da Medição atual a ser gerenciada.<br />
                <p>
                    Ao clicar no nome da Medição será apresentada uma tela com uma tabela contendo a lista de Medições configuradas com os grupos de unidades e unidades associadas.
                    Nessa mesma tela o usuário poderá selecionar qualquer outra Medição que deseja gerenciar.
                </p><br />
                <b>(2)</b> <i class="fa fa-user"></i> Nome do usuário logado no sistema.<br />
                <b>(2)</b> <i class="fa fa-info-circle"></i> Ajuda: Ao clicar nesse ícone o usuário terá uma rápida explicação da tela atual que está visualizando.<br />
                <b>(2)</b> <i class="fa fa-envelope"></i> Mensagens: Ao clicar nesse ícone será apresentada as mensagens do sistema destinadas ao usuário.<br />
                <b>(2)</b> <i class="fa fa-bell"></i> Alertas: Ao clicar nesse ícone será apresentado os alertas do sistema.<br />
                <b>(2)</b> <i class="fa fa-sign-out"></i> Sair: Ao clicar nesse ícone o usuário será desconectado do sistema.<br /><br />
                <b>(3)</b> Menus e SubMenus de navegação, onde o usuário poderá selecionar o tipo de supervisão, relatório, análise, fatura e configuração que deseja visualizar:<br />
                <tab1><b>- Dashboard:</b> Visualizar ou configurar blocos de supervisão. Os blocos de supervisão possibilitam a rápida visualização em tempo real das grandezas de Energia, Utilidades, Analógicas, Faturas, etc.</tab1><br />
                <tab1><b>- Supervisão:</b> Visualizar as informações pontuais de grandezas que compõem a medição atual a ser supervisionada. Seja ela de Energia, Utilidades, Analógicas ou Metas de Consumo.</tab1><br />
                <tab1><b>- Relatórios:</b> Visualizar gráficos, registros e informações das grandezas de Energia, Utilidades, Analógicas e Eventos em alguns determinados períodos como: Diário, Semanal, Mensal e Anual.</tab1><br />
                <tab1><b>- Finanças:</b> Emitir a Fatura de Energia Elétrica em determinado período.</tab1><br />
                <tab1><b>- Ranking:</b> Visualizar o Ranking de Meta de Consumo entre todas as medições configuradas. O Ranking tem como finalidade mostrar de forma rápida e objetiva através de valores atuais, projetados e alertas, quais são as medições que estão com o consumo atual fora da Meta preestabelecida e configurada para cada medição.</tab1><br />
                <tab1><b>- Configuração:</b> Usuário poderá visualizar e executar as configurações:</tab1><br />
                <tab2><b>- Cliente:</b> Configuração do cliente.</tab2><br />
                <tab2><b>- Gateways:</b> Configuração da Gateway.</tab2><br />
                <tab2><b>- Grupos:</b> Configuração dos Grupos de Unidade.</tab2><br />
                <tab2><b>- Unidades:</b> Configuração das Unidades.</tab2><br />
                <tab2><b>- Medições:</b> Configuração das Medições.</tab2><br />
                <tab2><b>- Usuário:</b> Configuração dos Usuários.</tab2><br />
                <tab1><b>- Mensagens:</b> Visualizar todas as mensagens do sistema destinadas ao usuário.</tab1><br />
                <tab1><b>- Alertas:</b> Visualizar todos os alertas do sistema.</tab1><br />
                <tab1><b>- Perfil:</b> Visualizar e configurar seu perfil.</tab1><br /><br />
                <b>(4)</b> Área de apresentação: Área principal do sistema onde são apresentadas todas as informações e configurações selecionadas no Menu e Submenu. Dependendo do item selecionado a tela será atualizada automaticamente, apresentando sempre valores atuais vindo das Gateways. Toda a área de apresentação, assim como Menus e submenus é redimensionável e adaptativa a qualquer resolução do monitor, permitindo uma visualização harmoniosa de todas as informações.<br />
                <br />

            </div>
            <div class="modal-footer">
                <div class="row">
                    <div class="col-lg-12">
                        @{
                            if (IDTipoAcesso != 10)
                            {
                                <button type="button" class="btn btn-rounded" style="background-color:#233545; color:#ffffff; font-weight:500;" onclick="StopIntro();"><i class="fa fa-check-square-o"></i>&nbsp;&nbsp;Não apresentar a Introdução novamente</button>
                            }
                            else
                            {
                                <button type="button" class="btn btn-rounded" style="background-color:#233545; color:#ffffff; font-weight:500;" onclick="GoDemo();"><i class="fa fa-mail-reply-all"></i>&nbsp;&nbsp;Navegar pelo Smart Energy</button>
                            }
                        }
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

@section Styles {
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }

<script type="text/javascript">

    $(document).ready(function () {

        // mostra modal
        $("#ModalIntro").modal("show");

    });

    function StopIntro() {

        var IDCliente = Math.ceil(@ViewBag._IDCliente);
        var IDUsuario = Math.ceil(@ViewBag._IDUsuario);

        $.ajax(
        {
            type: 'GET',
            url: '/Navegacao/StopIntro',
            dataType: 'html',
            data: { 'IDUsuario': IDUsuario },
            cache: false,
            async: true,
            success: function (data) {

                // esconde modal
                $("#ModalIntro").modal("hide");

                // redireciona para pagina lista de clientes
                var url = '/Supervisao/Clientes';

                if (IDCliente > 0)
                {
                    // redireciona para pagina lista de medicoes
                    url = '/Supervisao/Medicoes?IDCliente=' + IDCliente.toString();
                }

                window.location.href = url;
            }
        });
    }

    function GoDemo() {

        var IDUsuario = Math.ceil(@ViewBag._IDUsuario);

        // redireciona para pagina dashboard
        url = '/Dashboard/DB_Superv?Editavel=0&IDUsuario=' + IDUsuario.toString();

        window.location.href = url;
    }

</script>

}
