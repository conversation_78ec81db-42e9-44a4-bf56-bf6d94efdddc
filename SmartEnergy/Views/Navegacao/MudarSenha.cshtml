﻿@model SmartEnergyLib.SQL.UsuarioDominio

@using System.Globalization

@{
    ViewBag.Title = "Mudar <PERSON>ha";
}

<style>
    .modal {
        text-align: center;
        padding: 0 !important;
    }

        .modal:before {
            content: '';
            display: inline-block;
            height: 100%;
            vertical-align: middle;
            margin-right: -4px; /* Adjusts for spacing */
        }

    .modal-dialog {
        display: inline-block;
        text-align: left;
        vertical-align: middle;
    }

    .vcenter {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
    }
</style>

<div class="row">
    <div class="col-lg-12 vcenter">
        <img src="/Images/Login/LogoSmartEnergy85_transp.png" class="img-responsive center-block" /><br />
        <h3 class="font-bold" style="color:#233545; text-align:center">Gerenciamento e Supervisão de Energia Elétrica e Utilidades</h3>
    </div>
</div>

<div class="modal inmodal animated fadeIn" id="MudarSenha" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">

                <br />
                <div class="row">
                    <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
                        <i class="fa fa-shield fa-5x"></i>
                        <h2 class="font-bold">Mudar Senha</h2><br />
                    </div>
                </div>

                <br />
                <div class="row">
                    <div class="col-lg-12">
                        @using (Html.BeginForm("SalvarSenha", "Navegacao", FormMethod.Post, new { id = "form", role = "form" }))
                        {
                            @Html.Hidden("IDCliente", Model.IDCliente)
                            @Html.Hidden("IDUsuario", Model.IDUsuario)
                            @Html.Hidden("ExpirarMeses", Model.ExpirarMeses)

                            <div class="row">
                                <div class="col-lg-12">
                                    @{
                                        if (ViewBag.mudar_senha == 1)
                                        {
                                            <h2>
                                                Foi detectado que sua senha expirou.
                                            </h2>
                                            <br />
                                            <p>
                                                Visando maior segurança, solicitamos que altere a sua senha de acesso.
                                            </p>
                                        }

                                        if (ViewBag.mudar_senha == 2)
                                        {
                                            <p>
                                                Visando maior segurança, neste seu primeiro acesso, solicitamos que altere a sua senha de acesso.
                                            </p>
                                        }
                                    }
                                </div>
                            </div>

                            <br />
                            <div class="row">
                                <div class="form-group col-lg-6">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Senha</label>
                                    @Html.TextBoxFor(model => model.Senha, new { @class = "form-control", @type = "password", @maxlength = "20", @placeholder = @SmartEnergy.Resources.UsuarioPerfilTexts.Senha })
                                </div>
                                <div class="form-group col-lg-6">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Senha @SmartEnergy.Resources.UsuarioPerfilTexts.Confirmacao</label>
                                    <input id="SenhaConfirma" type="password" class="form-control" name="SenhaConfirma" placeholder="@SmartEnergy.Resources.UsuarioPerfilTexts.Confirmacao" value=@Html.DisplayFor(model => model.Senha)>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="row">
                    <div class="col-lg-12" align="right">
                        <button type="button" class="btn btn-rounded" style="background-color:#233545; color:#ffffff; font-weight:500;" onclick="Salvar();">Salvar Senha</button>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/declaracoes/Validates")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }

<script type="text/javascript">

        $(document).ready(function () {

            //
            // VALIDACAO DOS CAMPOS
            //
            Validator_AddMethods();

            $("#form").validate({
                rules: {
                    Senha: {
                        required: true,
                        checkSenha: true,
                        checkSimboloPermitido: true,
                        checkCaracteresSequenciais: true,
                        minlength: 8,
                        maxlength: 20
                    },
                    SenhaConfirma: {
                        equalTo: "#Senha"
                    }
                },
                highlight: function (element) {
                    $(element).closest('.control-group').removeClass('success').addClass('error');
                },
                unhighlight: function (element) {
                    $(element).closest('.control-group').addClass('valid success').removeClass('error');
                }
            });

            // mostra modal
            $("#MudarSenha").modal({ backdrop: 'static', keyboard: false });

        });

        function Salvar() {
            event.stopPropagation();

            var IDCliente = Math.ceil(@Model.IDCliente);

            var $form = $('#form');

            // verifica se entradas sao validas
            if (!$form.valid()) return false;

            $.ajax({
                url: '/Navegacao/SalvarSenha',
                data: $form.serialize(),
                type: 'POST',
                success: function (data) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {
                            swal({
                                html: true,
                                title: "Atenção",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                            });
                        }
                        else {

                            // esconde modal
                            $("#MudarSenha").modal("hide");

                            // redireciona para LGPD
                            var url = '/Navegacao/ShowLGPD';

                            window.location.href = url;
                        }

                    }, 100);

                },
                error: function (xhr, status, error) {

                    alert(xhr.responseText);

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao salvar!!!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        };

</script>

}
