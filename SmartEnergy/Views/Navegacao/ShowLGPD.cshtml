﻿@using System.Globalization

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.PoliticaPrivacidade;
    
    int IDTipoAcesso = ViewBag._IDTipoAcesso;
}

<style>
    .modal {
        text-align: center;
        padding: 0 !important;
    }

    .modal:before {
        content: '';
        display: inline-block;
        height: 100%;
        vertical-align: middle;
        margin-right: -4px; /* Adjusts for spacing */
    }

    .modal-dialog {
        display: inline-block;
        text-align: left;
        vertical-align: middle;
    }

    .vcenter {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);    
    }

</style>

<div class="row">
    <div class="col-lg-12 vcenter">
        <img src="/Images/Login/LogoSmartEnergy85_transp.png" class="img-responsive center-block" /><br />
        <h3 class="font-bold" style="color:#233545; text-align:center">Gerenciamento e Supervisão de Energia Elétrica e Utilidades</h3>
    </div>
</div>

<div class="modal inmodal animated fadeIn" id="ModalLGPD" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">

                <br />
                <div class="row">
                    <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
                        <i class="fa fa-shield fa-5x"></i>
                        <h2 class="font-bold">Política de Privacidade</h2><br />
                    </div>
                </div>

                <p>
                    Obrigado por acessar a plataforma <b>Smart Energy</b>.
                </p>
                <p>
                    Enquanto você navega, você nos confia seus dados e suas informações, e nós nos comprometemos a manter esses dados protegidos.
                </p>
                <p>
                    Em seus acessos, na coleta de dados, a empresa se valerá tão somente das informações imprescindíveis para a prestação dos serviços oferecidos e destinados a finalidade específica da relação obrigacional.
                </p>
                <p>
                    Mesmo após o término desse acesso, o <b>Smart Energy</b> ainda poderá tratar alguns de seus dados pessoais para exercer seus direitos garantidos em lei, inclusive como prova em processos administrativos, sempre nos baseando nos fundamentos relacionados pela LGPD (Lei Geral de Proteção de Dados).
                </p>
                <p>
                    Nos comprometemos desde já em garantir a segurança desses dados, adotando medidas técnicas aptas a manter as informações seguras e protegidas de acessos não autorizados e de situações incidentais ou ilícitas de destruição, perda, alteração ou qualquer outra forma inadequada de segurança.
                </p>
                <br />
                <p>
                    <b>SEUS DIREITOS</b>
                </p>
                <p>
                    A empresa se coloca à disposição para atendê-lo, sobretudo para que o usuário possa de forma gratuita saber se a empresa dispõe de algum dado pessoal a seu respeito e da necessidade de manutenção no seu sistema, bem como a finalidade, possibilitando a correção dos dados ou mesmo a sua exclusão, em conformidade com o que determina o artigo 18 da LGPD.
                </p>
                <p>
                    Para exercer os seus direitos como titular de dados pessoais ou para maiores informações, basta nos enviar um e-mail para <b><EMAIL></b> que será prontamente encaminhado ao encarregado da proteção de dados pessoais.
                </p>
                <br />
                <p>
                    Atenciosamente,
                </p>
                <p>
                    Equipe SMART ENERGY
                </p>

            </div>
            <div class="modal-footer">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="i-checks" style="margin-top: 6px;" align="left">
                            @Html.CheckBox("ConcordoReceberComunicacao", false, new { id = "ConcordoReceberComunicacao", @onchange = "ConcordoReceberComunicacao()" })&nbsp;&nbsp;<span>Eu concordo em receber comunicações</span>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-7">
                        <div class="i-checks" style="margin-top: 6px;" align="left">
                            @Html.CheckBox("ConcordoPolitica", false, new { id = "ConcordoPolitica", @onchange = "ConcordoPolitica()" })&nbsp;&nbsp;<span>Ao informar meus dados, eu concordo com a <u>Política de Privacidade</u></span>
                        </div>
                    </div>
                    <div class="col-lg-5" align="right">
                        <button type="button" id="Concordo" class="btn btn-rounded btn-primary" style="font-weight:500;" onclick="AceiteLGPD();" disabled>Concordo com a Política de Privacidade</button>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/iCheck")
    @Scripts.Render("~/plugins/jqueryui")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }

<script type="text/javascript">

    $(document).ready(function () {

        // mostra modal
        $("#ModalLGPD").modal({ backdrop: 'static', keyboard: false });

        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });

        $(".i-checks").on('ifToggled', function (e) { $(e.target).trigger('change'); });

    });


    function ConcordoReceberComunicacao() {

        // checkbox
        var ConcordoReceberComunicacao = $('#ConcordoReceberComunicacao').prop('checked');
        var ConcordoPolitica = $('#ConcordoPolitica').prop('checked');

        if (ConcordoReceberComunicacao && ConcordoPolitica)
        {
            document.getElementById("Concordo").disabled = false;
        }
        else
        {
            document.getElementById("Concordo").disabled = true;
        }
    }

    function ConcordoPolitica() {

        // checkbox
        var ConcordoReceberComunicacao = $('#ConcordoReceberComunicacao').prop('checked');
        var ConcordoPolitica = $('#ConcordoPolitica').prop('checked');

        if (ConcordoReceberComunicacao && ConcordoPolitica) {
            document.getElementById("Concordo").disabled = false;
        }
        else {
            document.getElementById("Concordo").disabled = true;
        }
    }

    function AceiteLGPD() {

        var IDUsuario = Math.ceil(@ViewBag._IDUsuario);

        $.ajax(
        {
            type: 'GET',
            url: '/Navegacao/AceitaLGPD',
            dataType: 'html',
            data: { 'IDUsuario': IDUsuario },
            cache: false,
            async: true,
            success: function (data) {

                // esconde modal
                $("#ModalLGPD").modal("hide");

                // redireciona para intro
                var url = '/Navegacao/ShowIntro';

                window.location.href = url;
            }
        });
    }

</script>

}
