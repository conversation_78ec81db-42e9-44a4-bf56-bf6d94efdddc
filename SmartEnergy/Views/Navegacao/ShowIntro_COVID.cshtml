﻿@using System.Globalization

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.Intro;
    
    int IDTipoAcesso = ViewBag._IDTipoAcesso;
}

<style>
    .icones {
        padding: 0px 5px;
        font-size: 18px;
        float: left;
    }

    .titulo {
        margin: 0;
        margin-left: 0px;
        margin-top: 20px;
        margin-bottom: 15px;
        padding-left: 15px;
        padding-top: 5px;
        padding-bottom: 5px;
        font-size: 12px;
        font-weight: bold;
        border-radius: 10px;
        float: left;
    }

    .link_branco a:link {
        color: #ffffff !important;
    }

    .link_branco a:visited {
        color: #ffffff !important;
    }

    .link_branco a:hover {
        color: #f0f0f0 !important;
    }

    .link_branco a:active {
        color: #ffffff !important;
    }

    .modal {
        text-align: center;
        padding: 0 !important;
    }

        .modal:before {
            content: '';
            display: inline-block;
            height: 100%;
            vertical-align: middle;
            margin-right: -4px; /* Adjusts for spacing */
        }

    .modal-dialog {
        display: inline-block;
        text-align: left;
        vertical-align: middle;
    }

    .vcenter {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);    
    }

</style>

<div class="row">
    <div class="col-lg-12 vcenter">
        <img src="/Images/Login/LogoSmartEnergy85_transp.png" class="img-responsive center-block" /><br />
        <h3 class="font-bold" style="color:#233545; text-align:center">Gerenciamento e Supervisão de Energia Elétrica e Utilidades</h3>
    </div>
</div>

<div class="modal inmodal animated fadeIn" id="ModalIntro" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">

                <br />
                <div class="row">
                    <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
                        <i class="fa fa-warning fa-5x"></i>
                        <h2 class="font-bold">COVID-19</h2><br />
                    </div>
                </div>

                <h2 class="font-bold">Caro usuário,</h2><br />

                <p>
                    Neste momento de crise o <b>SMART ENERGY</b> continua sendo uma importante ferramenta para a gestão remota de energia.
                </p>
                <p>
                    Seguindo as orientações das autoridades de saúde e pensando no bem-estar de nossos colaboradores, adotamos medidas de prevenção e conscientização, entre elas transferindo parte de nossa equipe para Home Office.
                </p>
                <p>
                    Seja trabalhando de casa ou do escritório, nosso compromisso é ajudar você neste período de crise.
                </p>
                <p>
                    Nossas operações continuam funcionando de segunda à sexta, das 8:00h às 12:00h e das 13:00h às 17:30h.<br />
                </p>
                <p>
                    Entre em contato conosco utilizando o <b>Suporte Online</b> ou os canais no menu <b><a href="@Url.Action("Contato", "Contato")">@SmartEnergy.Resources.MenuTexts.MenuLateralContato</a></b>
                </p>
                <br />
                <p>
                    Atenciosamente,
                </p>
                <p>
                    Equipe SMART ENERGY
                </p>

            </div>
            <div class="modal-footer">
                <div class="row">
                    <div class="col-lg-12">
                        @{
                            if (IDTipoAcesso != 10)
                            {
                                <button type="button" class="btn btn-rounded" style="background-color:#233545; color:#ffffff; font-weight:500;" onclick="StopIntro();"><i class="fa fa-check-square-o"></i>&nbsp;&nbsp;Não apresentar a Introdução novamente</button>
                            }
                            else
                            {
                                <button type="button" class="btn btn-rounded" style="background-color:#233545; color:#ffffff; font-weight:500;" onclick="GoDemo();"><i class="fa fa-mail-reply-all"></i>&nbsp;&nbsp;Navegar pelo Smart Energy</button>
                            }
                        }
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

@section Styles {
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }

<script type="text/javascript">

    $(document).ready(function () {

        // mostra modal
        $("#ModalIntro").modal("show");

    });

    function StopIntro() {

        var IDCliente = Math.ceil(@ViewBag._IDCliente);
        var IDUsuario = Math.ceil(@ViewBag._IDUsuario);

        $.ajax(
        {
            type: 'GET',
            url: '/Navegacao/StopIntro',
            dataType: 'html',
            data: { 'IDUsuario': IDUsuario },
            cache: false,
            async: true,
            success: function (data) {

                // esconde modal
                $("#ModalIntro").modal("hide");

                // redireciona para pagina lista de clientes
                var url = '/Supervisao/Clientes';

                if (IDCliente > 0)
                {
                    // redireciona para pagina lista de medicoes
                    url = '/Supervisao/Medicoes?IDCliente=' + IDCliente.toString();
                }

                window.location.href = url;
            }
        });
    }

    function GoDemo() {

        var IDUsuario = Math.ceil(@ViewBag._IDUsuario);

        // redireciona para pagina dashboard
        url = '/Dashboard/DB_Superv?Editavel=0&IDUsuario=' + IDUsuario.toString();

        window.location.href = url;
    }

</script>

}
