﻿@using System.Globalization

@{
    ViewBag.Title = @SmartEnergy.Resources.ConfiguracaoTexts.SegundoFator;

    int IDTipoAcesso = ViewBag._IDTipoAcesso;
}

<style>
    .modal {
        text-align: center;
        padding: 0 !important;
    }

        .modal:before {
            content: '';
            display: inline-block;
            height: 100%;
            vertical-align: middle;
            margin-right: -4px; /* Adjusts for spacing */
        }

    .modal-dialog {
        display: inline-block;
        text-align: left;
        vertical-align: middle;
    }

    .vcenter {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
    }

    .hcenter {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    input[type=text] {
        width: 72px;
        height: 72px;
        font-size: 60px;
        text-align: center;
        border: 1px solid #3c763d;
        background-color: #f5f5f5;
        margin-right: 20px;
        float: left;
    }
</style>

<div class="row">
    <div class="col-lg-12 vcenter">
        <img src="/Images/Login/LogoSmartEnergy85_transp.png" class="img-responsive center-block" /><br />
        <h3 class="font-bold" style="color:#233545; text-align:center">Gerenciamento e Supervisão de Energia Elétrica e Utilidades</h3>
    </div>
</div>

<div class="modal inmodal animated fadeIn" id="ModalSegundoFator" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">

                <br />
                <div class="row">
                    <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
                        <i class="fa fa-shield fa-5x"></i>
                        <h2 class="font-bold">@SmartEnergy.Resources.ConfiguracaoTexts.SegundoFator</h2><br />
                    </div>
                </div>

                <br />
                <form>
                    <div class="row">
                        <div class="col-lg-12 hcenter">
                            <input type="text" class="form-control num-input" id="numero1" name="numero1" maxlength="1" pattern="[0-9]" required autofocus>
                            <input type="text" class="form-control num-input" id="numero2" name="numero2" maxlength="1" pattern="[0-9]" required>
                            <input type="text" class="form-control num-input" id="numero3" name="numero3" maxlength="1" pattern="[0-9]" required>
                            <input type="text" class="form-control num-input" id="numero4" name="numero4" maxlength="1" pattern="[0-9]" required>
                            <input type="text" class="form-control num-input" id="numero5" name="numero5" maxlength="1" pattern="[0-9]" required>
                            <input type="text" class="form-control num-input" id="numero6" name="numero6" maxlength="1" pattern="[0-9]" required>
                        </div>
                    </div>
                </form>
                <br />

            </div>
            <div class="modal-footer">
                <div class="row">
                    <div class="col-lg-12" align="right">
                        <button type="button" class="btn btn-primary" onclick="VerificaSegundoFator();">Enviar</button>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }

<script type="text/javascript">

        $(document).ready(function () {

            // mostra modal
            $("#ModalSegundoFator").modal({ backdrop: 'static', keyboard: false });

            $('.num-input').keyup(function () {
                if (this.value.length == this.maxLength) {
                    $(this).next('.num-input').focus();
                }
            });

            // focus
            const inputElement = document.getElementById('numero1');
            inputElement.focus();
        });

        function VerificaSegundoFator() {

            // Obtém os valores dos campos de número
            var num1 = $('#numero1').val();
            var num2 = $('#numero2').val();
            var num3 = $('#numero3').val();
            var num4 = $('#numero4').val();
            var num5 = $('#numero5').val();
            var num6 = $('#numero6').val();

            // Valida se os valores são números entre 0 e 9
            if ($.isNumeric(num1) && $.isNumeric(num2) && $.isNumeric(num3) && $.isNumeric(num4) && $.isNumeric(num5) && $.isNumeric(num6)
                && num1 >= 0 && num1 <= 9 && num2 >= 0 && num2 <= 9 && num3 >= 0 && num3 <= 9 && num4 >= 0 && num4 <= 9 && num5 >= 0 && num5 <= 9 && num6 >= 0 && num6 <= 9) {

                var codigo = num1 + num2 + num3 + num4 + num5 + num6;

                $.ajax(
                {
                    type: 'GET',
                    url: '/Navegacao/VerificaSegundoFator',
                    data: { 'codigo': codigo },
                    contentType: 'application/json; charset=utf-8',
                    dataType: 'json',
                    cache: false,
                    async: true,
                    success: function (data) {

                        setTimeout(function () {

                            // verifica se erro
                            if (data.status == "ERRO") {

                                swal({
                                    title: "Erro",
                                    text: data.erro,
                                    type: "warning",
                                    html: true,
                                    confirmButtonColor: "#f8ac59",
                                    confirmButtonText: "Fechar",
                                    closeOnConfirm: true
                                }, function () {

                                    // redireciona
                                    var url = '/';
                                    window.location.href = url;

                                });

                            }
                            else {

                                // esconde modal
                                $("#ModalSegundoFator").modal("hide");

                                // retiro caminho relativo
                                var novoCaminho = data.caminho.replace("~","");

                                // Redireciona para o novo caminho
                                window.location.href = novoCaminho;
                            }

                        }, 100);

                    }
                });

            } else {

                swal({
                    title: "Erro",
                    text: "Por favor, digite 6 números válidos entre 0 e 9.",
                    type: "warning",
                    confirmButtonColor: "#f8ac59",
                    confirmButtonText: "Fechar",
                });

            }
        }

</script>

}
