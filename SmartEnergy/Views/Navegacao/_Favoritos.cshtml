﻿@using SmartEnergyLib.SQL

<style>

    .modal {
        text-align: center;
        padding: 0 !important;
    }

        .modal:before {
            content: '';
            display: inline-block;
            height: 100%;
            vertical-align: middle;
            margin-right: -4px; 
        }

    .modal-dialog {
        display: inline-block;
        text-align: left;
        vertical-align: middle;
    }

    .nav.navbar-right-jr > li > a {
        color: #fffff3;
    }

    .top-navigation .navbar-right-jr {
        margin-right: 10px;
    }

    .link_preto a:link {
        color: #7E6A6C !important;
    }

    .link_preto a:visited {
        color: #7E6A6C !important;
    }

    .link_preto a:hover {
        color: #a0a0a0 !important;
    }

    .link_preto a:active {
        color: #7E6A6C !important;
    }

    #dataTables-favoritos:hover {
        cursor: move;
    }

    #dataTables-favoritos tbody tr.selected {
        color: white;
        background-color: #a0a0a0 !important;
    }

</style>

@{
    // copia cookies
    string TipoPaginaAtual = "";

    if (ViewBag.TipoPaginaAtual != null)
    {
        TipoPaginaAtual = ViewBag.TipoPaginaAtual;
    }
}

<table id="dataTables-favoritos" class="table table-striped table-bordered table-hover">
    <thead>
        <tr>
            <th>Ordem</th>
            <th>@SmartEnergy.Resources.ComumTexts.Favoritos</th>
            <th>URL</th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @{
            List<FavoritosDominio> favoritos = ViewBag.Favoritos;

            if (favoritos != null)
            {
                foreach (var favorito in favoritos)
                {
                    <tr>
                        <td class="reordena">@favorito.Ordem</td>
                        <td class="reordena">@favorito.Descricao</td>
                        <td class="reordena">@favorito.URL</td>
                        <td class="link_preto">
                            <a href="#" onclick="Ir_Favorito('@favorito.URL')" title="@SmartEnergy.Resources.ComumTexts.BotaoExecutar"><i class="fa fa-star icones"></i></a>
                            <a href="#" class="edit-favorito" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"><i class="fa fa-edit icones"></i></a>
                            <a href="#" class="delete-favorito" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-times icones"></i></a>
                        </td>
                    </tr>
                }
            }
        }
    </tbody>
</table>



<script type="text/javascript">

    $(document).ready(function () {

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {
                var x = (a == "-") ? 0 : a.replace(/,/, ".");
                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        var tabela = $('#dataTables-favoritos').DataTable({
            rowReorder: {
                selector: '.reordena',
                update: true
            },
            "iDisplayLength": 10,
            dom: 'tp',

            "order": [0, "asc"],
            "bAutoWidth": false,
            "aoColumns": [
                { sWidth: "7%" },
                { sWidth: "81%" },
                { sWidth: "1%" },
                { sWidth: "12%" }
            ],

            "aoColumnDefs": [
                { "aTargets": [0], "bVisible": false, "bSortable": true, "bSearchable": false },
                { "aTargets": [1], "sType": "portugues", "bSortable": false, "bSearchable": false },
                { "aTargets": [2], "bVisible": false, "bSortable": false, "bSearchable": false },
                { "aTargets": [3], "bSortable": false, "bSearchable": false },
            ],

            "language": {
                "paginate": {
                    "previous": "Anterior",
                    "next": "Próximo"
                },
                "search": "Busca",
                "lengthMenu": "Apresenta _MENU_ itens por página",
                "zeroRecords": "Sem itens",
                "info": "Página _PAGE_ de _PAGES_",
                "infoEmpty": "Sem itens",
                "infoFiltered": "(filtrado do total de  _MAX_ itens)"
            }
        });

        tabela.on('row-reorder', function (e, diff, edit) {

            // salvar favoritos
            Salvar_Favoritos();

        });

        $('#dataTables-favoritos tbody').on('click', 'tr', function () {

            if ($(this).hasClass('selected')) {

                // retira selecao atual
                $(this).removeClass('selected');

            }
            else {

                // dataTable
                var table = $('#dataTables-favoritos').DataTable();

                // retira selecao atual
                table.$('tr.selected').removeClass('selected');

                // seleciona
                $(this).addClass('selected');

            }
        });

        tabela.on('click', '.edit-favorito', function (e) {
            e.preventDefault();

            // linha
            var row = $(this).closest('tr');

            // le dados da linha
            var data = $('#dataTables-favoritos').DataTable().row(row).data();

            // descrição
            document.getElementById("descricaoFavorito").value = data[1];

            // apresenta janela
            $("#ModalFavorito_Editar").modal("show");

        });

        tabela.on('click', '.delete-favorito', function (e) {
            e.preventDefault();

            // linha
            var row = $(this).closest('tr');

            // le dados da linha
            var data = $('#dataTables-favoritos').DataTable().row(row).data();

            // Descrição
            var Descricao = data[1];

            // titulo
            var titulo = "Deseja excluir o Favorito?<br/>" + Descricao;

            swal({
                html: true,
                title: titulo,
                text: "Esta operação não poderá ser desfeita.",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#18a689",
                confirmButtonText: "Excluir",
                cancelButtonText: "Cancelar",
                closeOnConfirm: true
            }, function () {

                // apaga linha solicitada
                $('#dataTables-favoritos').dataTable().fnDeleteRow(row);

                // salvar favoritos
                Salvar_Favoritos();

            });
        });
    });

    function Ir_Favorito(url) {

        // fecha modal
        $("#ModalFavoritos").modal("hide");

        // vai para favorito
        window.location.href = url;
    }

    function Adicionar_Favorito() {

        // pagina atual
        var pagina_atual = getCookie_BloqueioTempo("URLAtual");

        // excluir
        $.ajax(
            {
                type: 'GET',
                url: '/Navegacao/Favorito_Validar',
                data: { 'url': pagina_atual },
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {
                            swal({
                                html: true,
                                title: "Atenção",
                                text: data.descricao,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                            });
                        }
                        else {

                            // tabela
                            var oTable = $('#dataTables-favoritos').DataTable();

                            // último indice
                            var novaLinhaIndex = oTable.rows().count();

                            // dados da nova linha
                            var novaLinha = [novaLinhaIndex,
                                data.descricao,
                                pagina_atual,
                                '<a href="#" onclick="IrFavorito(' + pagina_atual + ')" title="@SmartEnergy.Resources.ComumTexts.BotaoExecutar"><i class="fa fa-star icones"></i></a>  <a href="#" class="edit-favorito" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"><i class="fa fa-edit icones"></i></a> <a href="#" class="delete-favorito" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-times icones"></i></a>'
                            ];

                            // cria linha
                            oTable.row.add(novaLinha).draw(false);

                            // reposiciona a nova linha na última sequência
                            var novaLinhaNode = oTable.row(novaLinhaIndex).node();

                            // torna possível de reordenar
                            $(novaLinhaNode).find('td:not(:last-child)').addClass('reordena');

                            // padrão de cores dos icones
                            $(novaLinhaNode).find('td:last-child').addClass('link_preto');

                            // salvar favoritos
                            Salvar_Favoritos();
                        }

                    }, 100);
                },
                error: function (response) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao validar favorito!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
    }

    function AlterarDescricao_Favorito() {

        // descrição
        var descricao = document.getElementById("descricaoFavorito").value;

        // tabela
        var oTable = $('#dataTables-favoritos').DataTable();
        var data = oTable.row('.selected').data();

        // verifica se tem selecionado
        if (data != undefined) {
            data[1] = descricao;
            oTable.row('.selected').data(data).draw();
        }

        // salvar favoritos
        Salvar_Favoritos();

        // fecha modal
        $("#ModalFavorito_Editar").modal("hide");

    }

    function FecharDescricao_Favorito() {

        // fecha modal
        $("#ModalFavorito_Editar").modal("hide");

    }

    $.fn.serializeObject = function () {
        var o = {};
        var a = this.serializeArray();
        $.each(a, function () {
            if (o[this.name] !== undefined) {
                if (!o[this.name].push) {
                    o[this.name] = [o[this.name]];
                }
                o[this.name].push(this.value || '');
            } else {
                o[this.name] = this.value || '';
            }
        });
        return o;
    };

    function Salvar_Favoritos() {

        setTimeout(function () {

            // lista de favoritos
            var oTable = $('#dataTables-favoritos').dataTable();
            var rows = oTable.fnSettings().aoData;
            var ordem = 0;
            var Descricao = "";
            var url = "";
            var dataArray = [];

            // percorre tabela
            $.each(rows, function (i, val) {

                // pega valores
                ordem = val._aData[0];
                Descricao = val._aData[1];
                url = val._aData[2];

                dataArray.push({
                    "IDFavorito": 0,
                    "IDUsuario": 0,
                    "Descricao": Descricao,
                    "URL": url,
                    "Ordem": ordem
                });
            });

            // constroi estrutura
            data = { 'favoritos': dataArray };
            data2 = JSON.stringify(data);

            // aguarde
            $('.overlay_aguarde').toggle();

            $.ajax({
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                url: '/Navegacao/Favorito_Salvar',
                data: data2,
                type: 'POST',
                success: function (data) {

                    // fim
                    $('.overlay_aguarde').toggle();

                },
                error: function (xhr, status, error) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao salvar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });

        }, 200);
    };

</script>

