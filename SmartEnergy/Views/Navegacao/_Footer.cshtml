﻿@using SmartEnergyLib.SQL

<style>
    .cabecalho {
        height: 100px;
        min-width: 400px;
        margin: 0px 0px 0px 0;
        padding: 0px;
        color: #fffff3;
        background-color: transparent;
        border-color: var(--tema-background-hover);
    }

    .cabecalho_align img {
        vertical-align: middle;
        text-align: center;
    }


    * {
        margin: 0px; /*Defina a separacao das colunas aqui*/
        padding: 0px;
    }

    .linha {
        height: 100px;
        margin-top: 15px;
        padding: 0px;
        clear: both;
        border-color: var(--tema-background-hover);
        background-color: var(--tema-background-hover);
    }

    .coluna1 {
        height: 25px;
        border: 0px solid #000000;
        float: left;
    }

    .coluna2 {
        height: 25px;
        border: 0px solid #000000;
        float: left;
        margin-top: 0px;
        margin-left: 10px;
    }

</style>



<div class="footer verdebemescuro-bg">

    <div class="linha cabecalho">

        @{
            // copia cookies
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;
            int IDCliente = ViewBag._IDCliente;

            string logo = "http://www.smartenergy.com.br/";
            
            if (IDCliente < 0)
            {
                <div class="coluna2">
                </div>
            }
            else
            {
                var clientesMetodos = new ClientesMetodos();
                var cliente = clientesMetodos.ListarPorId(IDCliente);

                if (cliente.Logo.IsEmpty())
                {
                    logo += "/Logos/LogoSmartEnergy.png";
                }
                else
                {
                    logo += "/Logos/" + cliente.Logo;
                }

                <div class="coluna2">
                    <p><img src=@logo /></p>
                    <h2>@cliente.Nome</h2>
                </div>
            }

            logo = "http://www.smartenergy.com.br/Logos/gestal_branco_site_85.png";
            // logo="~/Logos/@ViewBag._LogoConsultor";
                
            <div class="coluna1 pull-right">
                <img src=@logo />
            </div>

}

    </div>


</div>



