﻿@using System.Globalization

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.Intro;
    
    int IDTipoAcesso = ViewBag._IDTipoAcesso;
}

<style>
    .icones {
        padding: 0px 5px;
        font-size: 18px;
        float: left;
    }

    .titulo {
        margin: 0;
        margin-left: 0px;
        margin-top: 20px;
        margin-bottom: 15px;
        padding-left: 15px;
        padding-top: 5px;
        padding-bottom: 5px;
        font-size: 12px;
        font-weight: bold;
        border-radius: 10px;
        float: left;
    }

    .link_branco a:link {
        color: #ffffff !important;
    }

    .link_branco a:visited {
        color: #ffffff !important;
    }

    .link_branco a:hover {
        color: #f0f0f0 !important;
    }

    .link_branco a:active {
        color: #ffffff !important;
    }

    .modal {
        text-align: center;
        padding: 0 !important;
    }

        .modal:before {
            content: '';
            display: inline-block;
            height: 100%;
            vertical-align: middle;
            margin-right: -4px; /* Adjusts for spacing */
        }

    .modal-dialog {
        display: inline-block;
        text-align: left;
        vertical-align: middle;
    }

    .vcenter {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);    
    }

</style>

<div class="row">
    <div class="col-lg-12 vcenter">
        <img src="/Images/Login/LogoSmartEnergy85_transp.png" class="img-responsive center-block" /><br />
        <h3 class="font-bold" style="color:#233545; text-align:center">Gerenciamento e Supervisão de Energia Elétrica e Utilidades</h3>
    </div>
</div>

<div class="modal inmodal animated fadeIn" id="ModalBloqueio" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">

                <br />
                <div class="row">
                    <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
                        <i class="fa fa-warning fa-5x"></i>
                    </div>
                </div>
                <br />
                <div class="row">
                    <div class="col-lg-offset-1 col-lg-10" style="text-align:left;">
                        <h2><b>Temos um aviso importante para você.</b></h2>
                        <br />
                        <h3>Favor entrar em contato com nosso departamento Financeiro o mais rápido possível.</h3>
                    </div>
                </div>
                <br />
                <br />
                <div class="row">
                    <div class="col-lg-offset-1 col-lg-10" style="text-align:left;">
                        <h3>O acesso para a plataforma <b>Smart Energy</b> será liberado em instantes.</h3>
                    </div>
                </div>
                <br />
                <div class="row">
                    <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
                        <h1 id="numberCountdown"><b>60</b></h1>
                    </div>
                </div>
                <br />

            </div>
        </div>
    </div>
</div>

@section Styles {
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }

<script type="text/javascript">

    $(document).ready(function () {

        // mostra modal
        $("#ModalBloqueio").modal({ backdrop: 'static', keyboard: false });

        startCountdown();
    });


    // 60 segundos
    var g_iCount = 60;

    function startCountdown() {

        if ((g_iCount - 1) >= 0)
        {
            g_iCount = g_iCount - 1;
            document.getElementById("numberCountdown").innerText = g_iCount;
            setTimeout('startCountdown()', 1000);
        }
        else
        {
            FechaModal();
        }
    }


    function FechaModal() {

        // seta tempo
        setCookie_BloqueioTempo("BloqueioTempo", String(new Date().getTime()), null);

        // redireciona
        var IDCliente = Math.ceil(@ViewBag._IDCliente);
        var IDUsuario = Math.ceil(@ViewBag._IDUsuario);

        // esconde modal
        $("#ModalBloqueio").modal("hide");

        // redireciona para pagina lista de clientes
        var url = '/Supervisao/Clientes';

        if (IDCliente > 0)
        {
            // redireciona para pagina lista de medicoes
            url = '/Supervisao/Medicoes?IDCliente=' + IDCliente.toString();
        }

        window.location.href = url;
    }

    function setCookie_BloqueioTempo(c_name, value, expiresecs) {
        var exdate = new Date();
        exdate.setTime(exdate.getTime() + ((expiresecs) ? expiresecs * 1000 : 0));

        document.cookie = c_name + "=" + escape(value) + ((expiresecs == null) ? "" : ";expires=" + exdate.toGMTString()) + ";path=/";
    }

</script>

}
