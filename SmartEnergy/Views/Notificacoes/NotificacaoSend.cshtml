﻿@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL
@using System.Globalization

@{
    ViewBag.Title = "Notificação";

    // copia cookies
    int IDTipoAcesso = ViewBag._IDTipoAcesso;
    int IDCliente = ViewBag._IDCliente;
    string NomeCliente = ViewBag.NomeCliente;

}

<div class="row wrapper border-bottom white-bg page-heading">
    <div class="col-lg-10">
        <h2>Enviar Notificação para Smart Phone</h2>

        @using (Html.BeginForm("EnviarNotificacao", "Notificacoes", new { ReturnUrl = ViewBag.ReturnUrl }, FormMethod.Post, new { id = "form", role = "form" }))
        {
            <br /><br />
            <div class="row">
                <div class="col-lg-4">
                    <div class="form-group">
                        <label class="control-label">&nbsp;@SmartEnergy.Resources.MensagensTexts.EnviarPara</label>
                        <select class="form-control m-b" name="EnviarPara" id="EnviarPara">
                            <option selected disabled>Selecionar Enviar Para</option>
                            <option value="1">Todos os Usuários</option>
                            <option value="2">Todos os Gestores</option>
                            <option value="3">Todos os Administradores e Operadores dos Clientes</option>
                            <option value="4">Todos os Administradores dos Clientes</option>
                            <option value="5">Todos os Operadores dos Clientes</option>
                            <option value="6">Todos os Administradores Smart Energy</option>

                            @if (IDCliente > 0)
                            {
                                <option value="10">Usuários do Cliente [@ViewBag.NomeCliente]</option>
                                <option value="11">Administradores do Cliente [@ViewBag.NomeCliente]</option>
                                <option value="12">Operadores do Cliente [@ViewBag.NomeCliente]</option>
                            }

                        </select>
                    </div>
                </div>
            </div>
            <br />
            <div class="row">
                <div class="col-lg-12">
                    <div class="form-group">
                        <label class="control-label">&nbsp;Título</label>
                        <input class="form-control m-b" type="text" name="Titulo" id="Titulo" value="Smart Energy">
                    </div>
                </div>
            </div>
            <br />
            <div class="row">
                <div class="col-lg-12">
                    <div class="form-group">
                        <label class="control-label">&nbsp;Notificação</label>
                        <input class="form-control m-b" type="text" name="Notificacao" id="Notificacao" value="">
                    </div>
                </div>
            </div>

        }

    </div>
    <div class="col-lg-2">
        <div class="ibox-tools">
            <br />
            <button type="button" class="btn btn-primary" id="BotaoSalvar" onclick="EnviarNotificacao();">@SmartEnergy.Resources.MenuTexts.MenuLateralNotificacaoEnviar</button>
        </div>
    </div>
</div>


@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")

    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

    $(document).ready(function () {

        //
        // VALIDACAO DOS CAMPOS
        //

        jQuery.validator.addMethod("alphanumeric", function (value, element) {
            return this.optional(element) || /^[\'\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\+\-\_\(\)," "]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

        jQuery.validator.addMethod("numeric", function (value, element) {
            return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.numeric");

        $("#form").validate({
            rules: {
                Titulo: {
                    required: true,
                    alphanumeric: true,
                    minlength: 3,
                    maxlength: 50
                },
                Notificacao: {
                    required: true,
                    alphanumeric: true,
                    minlength: 3,
                    maxlength: 150
                },
                EnviarPara: { required: true }
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            }
        });
    });


    function EnviarNotificacao() {

        var $form = $('#form');

        // verifica se entradas sao validas
        if (!$form.valid()) return false;

        // enviar para
        var enviar_para_id = document.getElementById("EnviarPara");
        var enviar_para_selecionado = enviar_para_id.options[enviar_para_id.selectedIndex].value;

        // titulo
        var titulo = document.getElementById("Titulo").value;

        // notificação
        var notificacao = document.getElementById("Notificacao").value;

        // envia notificação
        swal({
            title: "Deseja Enviar a Notificação?",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#1ab394",
            confirmButtonText: "Enviar",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            $.ajax(
            {
                type: 'GET',
                url: '/Notificacoes/EnviarNotificacao',
                dataType: 'html',
                data: { 'EnviarPara': enviar_para_selecionado, 'Titulo': titulo, 'Notificacao': notificacao },
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        swal({
                            title: "Enviado com sucesso",
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // atualiza pagina
                            location.reload();
                        });

                    }, 100);

                },
                error: function (xhr, status, error) {

                    alert(xhr.responseText);

                }

            });


        });
    };

    </script>
}
