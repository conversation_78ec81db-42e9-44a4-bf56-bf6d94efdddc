﻿@model IEnumerable<SmartEnergyLib.SQL.SupervGatewaysDominio>

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.ConfiguracaoTexts.AtualizacaoFirmware;
}

<style>

    #dataTables-gateways tr.even:hover td {
        background-color: #E0E0E0 !important;
        color: #000000 !important;
    }

    #dataTables-gateways tr.odd:hover td {
        background-color: #E0E0E0 !important;
        color: #000000 !important;
    }

</style>


<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.ConfiguracaoTexts.AtualizacaoFirmware</h4>
                </div>
                <div class="panel-body">

                    <div class="row">
                        <div class="col-lg-1" style="text-align:center;">
                            <i class="fa fa-warning fa-5x"></i>
                        </div>
                        <div class="col-lg-11">
                            <h2>Esta operação atualiza o firmware das gateways selecionadas com a versão abaixo:</h2>
                        </div>
                    </div>

                    <br />
                    <br />

                    <div class="row">
                        <div class="col-lg-4">
                            <label class="control-label">&nbsp;Versões de Firmware</label>
                            <select class="form-control" name="firmware" id="firmware">
                                <option value="000" selected>Selecione a versão</option>

                                @{
                                    List<FirmwareDominio> listaFirmware = ViewBag.listaFirmware;
                                    
                                    foreach (FirmwareDominio firmware in listaFirmware)
                                    {
                                        <option value="@firmware.VersaoCompleta">SmartGateE [@firmware.Versao_txt]</option>
                                    }
                                }

                            </select>
                        </div>
                        <div class="col-lg-4">
                            <div class="form-group">
                                <label class="control-label">&nbsp;Horário da atualização</label>
                                <div class="input-group" id="data_div">
                                    <input type='text' class="form-control data" name="data" value=@ViewBag.Data>
                                    <span class="input-group-addon">
                                        <i class="fa fa-calendar"></i>&nbsp;&nbsp;<i class="fa fa-clock-o"></i>
                                    </span>
                                    <div class="input-group" id="hora_div">
                                        <input type="text" class="form-control hora" name="hora" value=@ViewBag.Hora>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <label class="control-label">&nbsp;</label>
                            <button type="button" class="btn btn-primary" id="BotaoAtualizarFirmware" style="height:34px; width:100%;" disabled>
                                <span onclick="AtualizarFirmware();">Atualizar o Firmware das gateways</span>
                            </button>
                        </div>
                    </div>

                    <br />

                    <table id="dataTables-gateways" class="table table-striped table-bordered table-hover">
                        <thead>
                            <tr>
                                <th></th>
                                <th>ID</th>
                                <th>@SmartEnergy.Resources.SupervisaoTexts.ClientesNomeFantasia</th>
                                <th>ID</th>
                                <th>Gateway</th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.Versao atual</th>
                                <th>Tecnologia</th>
                                <th>Upload</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>

                            @foreach (var gateway in Model)
                            {
                                string tipo_tempo = "-";

                                switch (gateway.IDTipoTempo)
                                {
                                    case TIPO_TEMPO_GATEWAY.GatewayBloqueada:
                                        tipo_tempo = "Bloqueada";
                                        break;

                                    case TIPO_TEMPO_GATEWAY.Ignorar_StartUpNaoRealizado:
                                        tipo_tempo = "Startup Não Realizado";
                                        break;

                                    case TIPO_TEMPO_GATEWAY.Ignorar_PendenciaCliente:
                                        tipo_tempo = "Pend. Cliente";
                                        break;

                                    case TIPO_TEMPO_GATEWAY.Ignorar_GatewayTeste:
                                        tipo_tempo = "Teste";
                                        break;

                                    case TIPO_TEMPO_GATEWAY.SCDE:
                                        tipo_tempo = "SCDE";
                                        break;

                                    case TIPO_TEMPO_GATEWAY.Envio_mensalmente:
                                        tipo_tempo = "Mensal";
                                        break;

                                    case TIPO_TEMPO_GATEWAY.Envio_semanalmente:
                                        tipo_tempo = "Semanal";
                                        break;

                                    case TIPO_TEMPO_GATEWAY.Envio_diariamente:
                                        tipo_tempo = "Diário";
                                        break;

                                    case TIPO_TEMPO_GATEWAY.Envio_hora:
                                        tipo_tempo = "Hora";
                                        break;

                                    case TIPO_TEMPO_GATEWAY.Envio_15minutos:
                                        tipo_tempo = "15 min.";
                                        break;
                                }

                                <tr>
                                    <td>@gateway.IDGateway</td>
                                    <td>@gateway.IDCliente</td>
                                    <td>@gateway.Fantasia.ToUpper()</td>
                                    <td>@gateway.IDGateway</td>
                                    <td>@gateway.Nome</td>
                                    <td>@gateway.VersaoEq</td>
                                    <td>@gateway.GSM_TEC</td>
                                    <td>@tipo_tempo</td>
                        
                                    @{
                                            if (gateway.StatusTexto == "-")
                                            {
                                                if (gateway.AcessoRemoto && gateway.GatewayConectada_IoT)
                                                {
                                                <td><span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="Gateway conectada no Servidor IoT"><i class="fa fa-chain" style="font-size:20px; color:green"></i>&nbsp;&nbsp;(conectado)</span></td>
                                            }
                                            else
                                            {
                                                <td><span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="Gateway não conectada no Servidor IoT"><i class="fa fa-chain-broken" style="font-size:20px; color:red"></i>&nbsp;&nbsp;(falha)</span></td>
                                            }
                                        }
                                        else
                                        {
                                            <td>@gateway.StatusTexto</td>
                                        }
                                    }

                                </tr>
                            }

                        </tbody>
                    </table>

                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
                                @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/datetimePickerStyles")
}

                            @section Scripts {
                                @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/AlertaBox")
    @Scripts.Render("~/datetimePicker")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
                                }

                                @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

                                @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        // gateway selecionadas
        var rows_selected_gateways = [];

        $(document).ready(function () {

            $('#data_div .data').datetimepicker({
                                    locale: 'pt-BR',
                format: 'DD/MM/YYYY',
                showTodayButton: true,
                allowInputToggle: true
            });

            $('#hora_div').datetimepicker({
                                    locale: 'pt-BR',
                format: 'HH:mm',
                allowInputToggle: true
            });

                                    jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                                        "portugues-pre": function (data) {
                                            var a = 'a';
                                            var e = 'e';
                                            var i = 'i';
                                            var o = 'o';
                                            var u = 'u';
                                            var c = 'c';
                                            var special_letters = {
                        "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                        "É": e, "é": e, "Ê": e, "ê": e,
                        "Í": i, "í": i, "Î": i, "î": i,
                        "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                        "Ú": u, "ú": u, "Ü": u, "ü": u,
                        "ç": c, "Ç": c
                                            };
                    for (var val in special_letters)
                                            data = data.split(val).join(special_letters[val]).toLowerCase();
                                        return data;
                                    },
                "portugues-asc": function (a, b) {
                                        return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                                    },
                "portugues-desc": function (a, b) {
                                        return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                                    }
                                });

                                jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                                    "numero-pre": function (a) {

                                        var x = a;

                                        if (a == "-") {
                                            x = "-1";
                                        }
                                        else {
                                            x = a.replace(".", "");
                                            x = x.replace(",", ".");
                                        }

                                        return parseFloat(x);
                                    },

                "numero-asc": function (a, b) {
                                        return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                                    },

                "numero-desc": function (a, b) {
                                        return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                                    }
                                });

                                var tableGateways = $('#dataTables-gateways').DataTable({
                                    "iDisplayLength": 10,
                dom: 'Bftp',
                buttons: [
                    {
                                    extend: 'excelHtml5',
                        filename: 'Gateways_GateE',
                        title: 'Gateways_GateE',
                        text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-excel-o" data-toggle="tooltip" data-placement="left" title="Excel"></span>',
                        className: 'btn btn-default btn-xs'
                    },
                    {
                                    extend: 'pdfHtml5',
                        filename: 'Gateways_GateE',
                        title: 'Gateways_GateE',
                        text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-pdf-o" data-toggle="tooltip" data-placement="left" title="PDF"></span>',
                        className: 'btn btn-default btn-xs'
                    },
                ],

                "bAutoWidth": false,
                "aoColumnDefs": [

                        {
                            "aTargets": [0], "sType": "numero", "bSortable": false, "bSearchable": false, 'sClass': 'dt-center',
                            "mRender": function (data, type, full) { return '<input type="checkbox" name="checkbox_gateways">'; },
                        },
                        { "aTargets": [1], "sType": "numero" },
                        { "aTargets": [2], "sType": "portugues" },
                        { "aTargets": [3], "sType": "numero" },
                        { "aTargets": [4], "sType": "portugues" },
                        { "aTargets": [5], "sType": "portugues" },
                        { "aTargets": [6], "sType": "portugues" },
                        { "aTargets": [7], "sType": "portugues" },
                        { "aTargets": [8], "sType": "portugues" },
                ],
                "aoColumns": [
                    { sWidth: "5%" },
                    { sWidth: "5%" },
                    { sWidth: "19%" },
                    { sWidth: "5%" },
                    { sWidth: "18%" },
                    { sWidth: "10%" },
                    { sWidth: "10%" },
                    { sWidth: "8%" },
                    { sWidth: "20%" },
                ],
                'order': [[2, 'asc'], [4, 'asc']],

                'rowCallback': function (row, data, dataIndex) {

                                        // pega IDGateway
                                        var rowId = data[0];

                                        // verifica ID se esta na lista de selecionados
                                        if ($.inArray(rowId, rows_selected_gateways) !== -1) {
                                            $(row).find('input[type="checkbox"]').prop('checked', true);
                                            $(row).addClass('selected');
                                        }
                                    },

                "language": {
                              "paginate": {
                                            "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                                            "next": "@SmartEnergy.Resources.ComumTexts.next"
                                          },
                                "search": "@SmartEnergy.Resources.ComumTexts.search",
                                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                                "info": "@SmartEnergy.Resources.ComumTexts.info",
                                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                            }

            });

            // manipula checkbox
            $('#dataTables-gateways tbody').on('click', 'input[type="checkbox"]', function (e) {
                                    var $row = $(this).closest('tr');

                                    // pega dados da linha
                                    var data = tableGateways.row($row).data();

                                    // IDGateway
                                    var rowId = data[0];

                                    // verifica se o ID da linha está na lista de IDs de linha selecionados
                                    var index = $.inArray(rowId, rows_selected_gateways);

                                    // se o checkbox de seleção estiver marcado e o ID da linha não estiver na lista de IDs de linha selecionados
                                    if (this.checked && index === -1) {
                                            rows_selected_gateways.push(rowId);

                                            // caso contrário, se o checkbox não estiver marcado e o ID da linha estiver na lista de IDs de linha selecionados
                                        } else if (!this.checked && index !== -1) {
                                                rows_selected_gateways.splice(index, 1);
                                            }

                                            if (this.checked) {
                    $row.addClass('selected');
                                                } else {
                    $row.removeClass('selected');
                                                }

                                                // impedir que o evento de clique se propague para o pai
                                                e.stopPropagation();
                                                });

            // manipula click na tabela
            $('#dataTables-gateways').on('click', 'tbody td, thead th:first-child', function (e) {
                $(this).parent().find('input[type="checkbox"]').trigger('click');
                                            });

                                            // firmwares
                                            var firmware = document.getElementById("firmware");

                                            firmware.addEventListener('change', function () {

                                                if (firmware.value == "000") {
                                                    document.getElementById('BotaoAtualizarFirmware').disabled = true;
                                                }
                                                else {
                                                    document.getElementById('BotaoAtualizarFirmware').disabled = false;
                                                }
                                            });
                                            });

        function AtualizarFirmware() {
            event.stopPropagation();

            // firmware
            var firmware = document.getElementById("firmware");
            var firmware_selecionado = firmware.options[firmware.selectedIndex];

            // data e hora
            var datahora = document.querySelector('[name="data"]').value + " " + document.querySelector('[name="hora"]').value;

            // verifica se não tem selecionado
            if (rows_selected_gateways == null || rows_selected_gateways.length == 0) {

                AlertaBox("Atenção", "Selecionar as gateways que deseja atualizar o firmware.", "warning");
                return;
            }

            swal({
                html: true,
                title: "Atualização de Firmware",
                text: "Deseja atualizar as <b>" + rows_selected_gateways.length + " gateways</b> selecionadas com o Firmware<br/>" + firmware_selecionado.text + " ?",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#18a689",
                confirmButtonText: "Atualizar",
                cancelButtonText: "Cancelar",
                closeOnConfirm: true
            }, function () {

                // atualizar firmware
                $.ajax(
                {
                    type: 'GET',
                    url: '/Suporte/Firmware_Atualizar_IniciaProcesso',
                    contentType: 'application/json; charset=utf-8',
                        data: { 'gateways': rows_selected_gateways, 'versaoSolicitada': firmware_selecionado.value, 'datahora': datahora },
                    dataType: 'json',
                    traditional: true,
                    cache: false,
                    async: true,
                    success: function (data) {

                        setTimeout(function () {

                            // verifica se erro
                            if (data.status == "ERRO") {

                                AlertaBox("Erro", data.erro, "warning");

                            }
                            else {

                                swal({
                                    title: "Atualização programada",
                                    text: "A atualização do Firmware foi programada.<br><br>Assim que a atualização for concluída você receberá um email em " + data.email,
                                    type: "success",
                                    html: true,
                                    confirmButtonColor: "#18a689",
                                    confirmButtonText: "Fechar",
                                }, function () {

                                    // redireciona para pagina
                                    var url = '/Suporte/GatewaysAtualizacaoFirmware';
                                    window.location.href = url;
                                });

                            }

                        }, 100);
                    },
                    error: function (xhr, status, error) {

                        AlertaBox("Erro", xhr.responseText, "warning");

                    }
                });

            });

        };

    </script>
}

