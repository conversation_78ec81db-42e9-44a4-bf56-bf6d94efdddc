﻿@model IEnumerable<SmartEnergyLib.SQL.MedicoesSupervDominio>

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoMedicoesAtraso;
}

<style>
    .icones {
        padding: 0px 5px;
        font-size: 18px;
        float: left;
        color: #333333 !important;
    }

    .icones-danger {
        text-shadow: 0 0 5px #000000;
        padding: 0px 5px;
        font-size: 18px;
        float: left;
        color: #ED5565 !important;
    }

    .icones-warning {
        text-shadow: 0 0 5px #000000;
        padding: 0px 5px;
        font-size: 18px;
        float: left;
        color: #f8ac59 !important;
    }

    .icones-primary {
        text-shadow: 0 0 5px #000000;
        padding: 0px 5px;
        font-size: 18px;
        float: left;
        color: #1ab394 !important;
    }

    .tipo1-bg {
        background-color: #DCDCDC !important;
        color: #000000 !important;
    }

    .tipo2-bg {
        background-color: #C0C0C0 !important;
        color: #000000 !important;
    }

    .tipo3-bg {
        background-color: #A9A9A9 !important;
        color: #ffffff !important;
    }

    .tipo4-bg {
        background-color: #696969 !important;
        color: #ffffff !important;
    }

    .tipo5-bg {
        background-color: #DCDCDC !important;
        color: #000000 !important;
    }

    .tipo6-bg {
        background-color: #DCDCDC !important;
        color: #000000 !important;
    }

    tfoot input {
        width: 100%;
        padding: 3px;
        box-sizing: border-box;
    }

</style>



<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoMedicoesAtraso</h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-lg-6">

                            @{
                                var MedicoesAtraso = ViewBag.MedicoesAtraso_Total;
                                var MedicoesAtraso_Classe = "navy-bg";

                                if (MedicoesAtraso >= 200)
                                {
                                    MedicoesAtraso_Classe = "red-bg";
                                }
                            }

                            <div class="widget style1 @MedicoesAtraso_Classe">
                                <div class="row">
                                    <div class="col-xs-4">
                                        <i class="fa fa-clock-o fa-5x"></i>
                                    </div>
                                    <div class="col-xs-8 text-right">
                                        <span> Medições em Atraso </span>
                                        <h2 class="font-bold">@MedicoesAtraso</h2>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <table class="table">
                                <tbody>
                                    <tr>
                                        <td style="width: 50%;">
                                            <button type="button" class="btn tipo1-bg m-r-sm">@ViewBag.MedicoesAtraso_3dias</button>
                                            Atraso entre 6 horas e 3 dias
                                        </td>
                                        <td style="width: 50%;">
                                            <button type="button" class="btn tipo2-bg m-r-sm">@ViewBag.MedicoesAtraso_3_7dias</button>
                                            Atraso entre 3 e 7 dias
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;">
                                            <button type="button" class="btn tipo3-bg m-r-sm">@ViewBag.MedicoesAtraso_7_23dias</button>
                                            Atraso entre 7 e 23 dias
                                        </td>
                                        <td style="width: 50%;">
                                            <button type="button" class="btn tipo4-bg m-r-sm">@ViewBag.MedicoesAtraso_23dias</button>
                                            Atraso entre 23 dias e 1 ano
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;">
                                            <button type="button" class="btn tipo5-bg m-r-sm">@ViewBag.MedicoesAtraso_1ano</button>
                                            Atraso acima de 1 ano
                                        </td>
                                        <td style="width: 50%;">
                                            <button type="button" class="btn tipo6-bg m-r-sm">@ViewBag.MedicoesAtraso_NuncaMediram</button>
                                            Nunca mediram
                                        </td>
                                    </tr>
                                    <tr>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-6">
                            <label class="control-label">&nbsp;Tipos de Medição</label>
                            <select class="select2_tipo form-control" id="TiposMedicao" onchange="SelecionouTipoMedicao()">
                                <option value='0'>Todos os tipos de Medição</option>
                                <option value='1'>Energia Elétrica</option>
                                <option value='2'>Energia Elétrica (Fórmula)</option>
                                <option value='3'>Utilidades</option>
                                <option value='4'>Utilidades (Fórmula)</option>
                                <option value='5'>Ciclômetro</option>
                                <option value='6'>Entrada Analógica</option>
                                <option value='7'>Entrada Analógica (Fórmula)</option>
                                <option value='8'>Meteorologia</option>
                            </select>
                        </div>
                        <div class="col-lg-6">
                            <label class="control-label">&nbsp;Atraso</label>
                            <select class="select2_tipo form-control" id="Tipos" onchange="SelecionouTipo()">
                                <option value='0'>Todas as Medições em atraso</option>
                                <option value='1'>Atraso entre 6 horas e 3 dias</option>
                                <option value='2'>Atraso entre 3 e 7 dias</option>
                                <option value='3'>Atraso entre 7 e 23 dias</option>
                                <option value='4'>Atraso entre 23 dias e 1 ano</option>
                                <option value='5'>Atraso maior que 1 ano</option>
                                <option value='6'>Nunca mediram</option>
                            </select>
                        </div>
                    </div>
                    <br />

                    <div class="row">
                        <div class="col-lg-12">
                            <table id="dataTables-medicoes" class="table table-bordered table-hover dataTables-medicoes">
                                <thead>
                                    <tr>
                                        <th>Tipo Atraso</th>
                                        <th>ID</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.ClientesNomeFantasia</th>
                                        <th>ID</th>
                                        <th>Medições</th>
                                        <th></th>
                                        <th>Último Registro</th>
                                        <th>CODI</th>
                                        <th>IDGateway</th>
                                        <th>Modelo (Versão)</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.UltimaAtualizacao</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.Equipamento</th>
                                        <th>Upload</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tfoot>
                                    <tr>
                                        <th>Tipo Atraso</th>
                                        <th>ID</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.ClientesNomeFantasia</th>
                                        <th>ID</th>
                                        <th>Medições</th>
                                        <th></th>
                                        <th>Último Registro</th>
                                        <th>CODI</th>
                                        <th>IDGateway</th>
                                        <th>Modelo (Versão)</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.UltimaAtualizacao</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.Equipamento</th>
                                        <th>Upload</th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                                <tbody>

                                    @{
                                        foreach (MedicoesSupervDominio medicao in Model)
                                        {
                                            string logo;
                                            int tipomedicao = 0;

                                            string Classe = "tipo" + medicao.TipoAtraso + "-bg";

                                            // copia data e hora atualização medição
                                            string DataHoraMedicao = String.Format("{0:g}", medicao.DataHoraValor);
                                            string DataHoraMedicao_Sort = String.Format("{0:yyyyMMddHHmm}", medicao.DataHoraValor);

                                            // copia data e hora atualização gateway
                                            string DataHoraGateway = String.Format("{0:g}", medicao.DataHoraAtualizacao);
                                            string DataHoraGateway_Sort = String.Format("{0:yyyyMMddHHmm}", medicao.DataHoraAtualizacao);

                                            // copia data e hora equipamento
                                            string DataEq = String.Format("{0:g}", medicao.DataHoraEq);
                                            string DataEq_Sort = String.Format("{0:yyyyMMddHHmm}", medicao.DataHoraEq);

                                            // tempo de upload
                                            string tempo_upload = "-";
                                            switch (medicao.IDTipoTempo)
                                            {
                                                case TIPO_TEMPO_GATEWAY.Envio_15minutos:
                                                    tempo_upload = "15 min.";
                                                    break;

                                                case TIPO_TEMPO_GATEWAY.Envio_hora:
                                                    tempo_upload = "Hora";
                                                    break;

                                                case TIPO_TEMPO_GATEWAY.Envio_diariamente:
                                                    tempo_upload = "Diário";
                                                    break;

                                                case TIPO_TEMPO_GATEWAY.Envio_semanalmente:
                                                    tempo_upload = "Semanal";
                                                    break;

                                                case TIPO_TEMPO_GATEWAY.Envio_mensalmente:
                                                    tempo_upload = "Mensal";
                                                    break;
                                            };

                                            // caminhos
                                            string caminho_superv = "Medicao_Energia";
                                            string caminho_relat = "Relat_Dem_Ativa";

                                            switch (medicao.IDTipoMedicao)
                                            {
                                                case TIPO_MEDICAO.ENERGIA:

                                                    caminho_superv = "Medicao_Energia";
                                                    caminho_relat = "Relat_Dem_Ativa";

                                                    // verifica se cliente com meta
                                                    if (medicao.IDTipoSupervisao == 1)
                                                    {
                                                        caminho_superv = "Medicao_Energia_PDA";
                                                    }

                                                    // verifica se gateway Arquivos SCDE
                                                    if (medicao.IDTipoGateway == TIPO_GATEWAY.SCDE)
                                                    {
                                                        caminho_superv = "Medicao_Energia_SCDE";
                                                    }

                                                    break;

                                                case TIPO_MEDICAO.ENERGIA_FORMULA:

                                                    caminho_superv = "Medicao_Energia_Formula";
                                                    caminho_relat = "Relat_Dem_Ativa";

                                                    // verifica se cliente com meta
                                                    if (medicao.IDTipoSupervisao == 1)
                                                    {
                                                        caminho_superv = "Medicao_Energia_PDA_Formula";
                                                    }

                                                    break;

                                                case TIPO_MEDICAO.UTILIDADES:

                                                    caminho_superv = "Medicao_Utilidades";
                                                    caminho_relat = "Relat_Utilidades";
                                                    break;

                                                case TIPO_MEDICAO.UTILIDADES_FORMULA:

                                                    caminho_superv = "Medicao_Utilidades_Formula";
                                                    caminho_relat = "Relat_Utilidades";
                                                    break;

                                                case TIPO_MEDICAO.ENTRADA_ANALOGICA:

                                                    caminho_superv = "Medicao_EA";
                                                    caminho_relat = "Relat_EA";
                                                    break;

                                                case TIPO_MEDICAO.EA_FORMULA:

                                                    caminho_superv = "Medicao_EA_Formula";
                                                    caminho_relat = "Relat_EA";
                                                    break;

                                                case TIPO_MEDICAO.CICLOMETRO:

                                                    caminho_superv = "Medicao_Ciclometro";
                                                    caminho_relat = "Relat_Ciclometro";
                                                    break;

                                                case TIPO_MEDICAO.METEOROLOGIA:

                                                    caminho_superv = "Medicao_Meteorologia";
                                                    caminho_relat = "Relat_Meteorologia";
                                                    break;

                                            }

                                            <tr class="@Classe">
                                                <td>@medicao.TipoAtraso</td>
                                                <td>@medicao.IDCliente</td>
                                                <td>@medicao.NomeCliente</td>
                                                <td>@medicao.IDMedicao</td>
                                                <td>@medicao.NomeMedicao</td>

                                                @{
                                                    // energia
                                                    logo = "/Imagens/icone_lampada24.png";
                                                    tipomedicao = 1;

                                                    // energia formula
                                                    if (medicao.IDTipoMedicao == TIPO_MEDICAO.ENERGIA_FORMULA)
                                                    {
                                                        logo = "/Imagens/icone_soma24.png";
                                                        tipomedicao = 2;
                                                    }

                                                    // utilidades
                                                    if (medicao.IDTipoMedicao == TIPO_MEDICAO.UTILIDADES || medicao.IDTipoMedicao == TIPO_MEDICAO.UTILIDADES_FORMULA)
                                                    {
                                                        if (medicao.IDIcone == 0)
                                                        {
                                                            logo = "/Imagens/icone_gota24.png";
                                                        }

                                                        if (medicao.IDIcone == 1)
                                                        {
                                                            logo = "/Imagens/icone_fogo24.png";
                                                        }

                                                        if (medicao.IDIcone == 2)
                                                        {
                                                            logo = "/Imagens/icone_relogio24.png";
                                                        }

                                                        tipomedicao = 3;
                                                    }

                                                    if (medicao.IDTipoMedicao == TIPO_MEDICAO.UTILIDADES_FORMULA)
                                                    {
                                                        tipomedicao = 4;
                                                    }

                                                    // ciclometro
                                                    if (medicao.IDTipoMedicao == TIPO_MEDICAO.CICLOMETRO)
                                                    {
                                                        logo = "/Imagens/icone_ciclo24.png";
                                                        tipomedicao = 5;
                                                    }

                                                    // grandezes genericas
                                                    if (medicao.IDTipoMedicao == TIPO_MEDICAO.ENTRADA_ANALOGICA)
                                                    {
                                                        logo = "/Imagens/icone_gauge24.png";
                                                        tipomedicao = 6;
                                                    }

                                                    // grandezes genericas
                                                    if (medicao.IDTipoMedicao == TIPO_MEDICAO.EA_FORMULA)
                                                    {
                                                        logo = "/Imagens/icone_gauge24.png";
                                                        tipomedicao = 7;
                                                    }

                                                    // estacao metereologica
                                                    if (medicao.IDTipoMedicao == TIPO_MEDICAO.METEOROLOGIA)
                                                    {
                                                        logo = "/Imagens/icone_meteorologia24.png";
                                                        tipomedicao = 8;
                                                    }
                                                }

                                                <td style="text-align:center;"><span style="display:none;">@tipomedicao</span><img src=@logo /></td>
                                                <td>@DataHoraMedicao</td>
                                                <td>@medicao.infoGeral.status_CODI</td>
                                                <td>@medicao.IDGateway</td>
                                                <td>@medicao.Modelo&nbsp;<br />(@medicao.Versao)&nbsp;</td>
                                                <td>@DataHoraGateway</td>
                                                <td>@DataEq</td>
                                                <td>@tempo_upload</td>
                                                <td class="link_preto">
                                                    <a href='@("/Supervisao/" + caminho_superv + "?IDCliente=" + @medicao.IDCliente.ToString() + "&IDMedicao=" + @medicao.IDMedicao.ToString())' title="@SmartEnergy.Resources.MenuTexts.MenuLateralSupervisao">
                                                        <i class="fa fa-desktop icones"></i>
                                                    </a>
                                                    <a href='@("/Relatorios/" + caminho_relat + "?IDCliente=" + @medicao.IDCliente.ToString() + "&IDMedicao=" + @medicao.IDMedicao.ToString())' title="@SmartEnergy.Resources.MenuTexts.MenuLateralRelatorios">
                                                        <i class="fa fa-bar-chart icones"></i>
                                                    </a>
                                                    <a href='@("/Configuracao/Gateway_Editar?IDGateway=" + medicao.IDGateway.ToString())' title="@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracao Gateway">
                                                        <i class="fa fa-gear icones"></i>
                                                    </a>
                                                    <a href='@("/Suporte/GateE_CorrigirFalhaUpload?IDGateway=" + medicao.IDGateway.ToString())' title="@SmartEnergy.Resources.ConfiguracaoTexts.CorrigirFalhaUpload">
                                                        <i class="fa fg-ferramenta icones"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        }
                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/datetimePickerStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/plugins/iCheck")

    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


<script type="text/javascript">

        $(document).ready(function () {

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "portugues-pre": function (data) {
                    var a = 'a';
                    var e = 'e';
                    var i = 'i';
                    var o = 'o';
                    var u = 'u';
                    var c = 'c';
                    var special_letters = {
                        "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                        "É": e, "é": e, "Ê": e, "ê": e,
                        "Í": i, "í": i, "Î": i, "î": i,
                        "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                        "Ú": u, "ú": u, "Ü": u, "ü": u,
                        "ç": c, "Ç": c
                    };
                    for (var val in special_letters)
                        data = data.split(val).join(special_letters[val]).toLowerCase();
                    return data;
                },
                "portugues-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },
                "portugues-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "numero-pre": function (a) {
                    var x = (a == "-") ? 0 : a.replace(/,/, ".");
                    return parseFloat(x);
                },

                "numero-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },

                "numero-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "datetime-br-pre": function (a) {
                    if (!a) return 0; // Se a célula estiver vazia, retorna 0
                    let parts = a.split(' '); // Divide a string entre data e hora
                    let dateParts = parts[0].split('/'); // Divide a data dd/mm/aaaa
                    let timeParts = parts[1].split(':'); // Divide a hora hh:mm

                    return new Date(
                        parseInt(dateParts[2], 10),     // Ano
                        parseInt(dateParts[1], 10) - 1, // Mês (base 0)
                        parseInt(dateParts[0], 10),     // Dia
                        parseInt(timeParts[0], 10),     // Hora
                        parseInt(timeParts[1], 10)      // Minuto
                    ).getTime();
                },

                "datetime-br-asc": function (a, b) {
                    return a - b;
                },

                "datetime-br-desc": function (a, b) {
                    return b - a;
                }
            });

            $('.dataTables-medicoes').DataTable({
                "iDisplayLength": 10,
                dom: 'Bftp',
                buttons: [
                    {
                        extend: 'excelHtml5',
                        filename: 'MedicoesAtraso',
                        title: 'Medicoes com Atraso',
                        text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-excel-o" data-toggle="tooltip" data-placement="left" title="Excel"></span>',
                        className: 'btn btn-default btn-xs',
                    },
                    {
                        extend: 'pdfHtml5',
                        filename: 'MedicoesAtraso',
                        title: 'Medicoes com Atraso',
                        text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-pdf-o" data-toggle="tooltip" data-placement="left" title="PDF"></span>',
                        className: 'btn btn-default btn-xs'
                    },
                ],

                "bAutoWidth": false,
                "aoColumnDefs": [
                            { "aTargets": [0], "bVisible": false },
                            { "aTargets": [1], "sType": "numero" },
                            { "aTargets": [2], "sType": "portugues" },
                            { "aTargets": [3], "sType": "numero" },
                            { "aTargets": [4], "sType": "portugues" },
                            { "aTargets": [5], "sType": "numero" },
                            { "aTargets": [6], "sType": "datetime-br" },
                            { "aTargets": [7], "sType": "portugues" },
                            { "aTargets": [8], "sType": "numero" },
                            { "aTargets": [9], "sType": "portugues" },
                            { "aTargets": [10], "sType": "datetime-br", "bSearchable": false },
                            { "aTargets": [11], "sType": "datetime-br", "bSearchable": false },
                            { "aTargets": [12], "sType": "portugues" },
                            { "aTargets": [13], "bSortable": false }
                ],
                "aoColumns": [
                { sWidth: "0%" },
                { sWidth: "5%" },
                { sWidth: "11%" },
                { sWidth: "5%" },
                { sWidth: "11%" },
                { sWidth: "3%" },
                { sWidth: "10%" },
                { sWidth: "6%" },
                { sWidth: "5%" },
                { sWidth: "7%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "5%" },
                { sWidth: "10%" },
                ],
                'order': [6, 'desc'],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }

            });

            //
            // BUSCA
            //

            // campos de busca em cada coluna na tabela
            $('.dataTables-medicoes tfoot th').each(function () {
                var title = $(this).text();
                if (title.length > 0)
                    $(this).html('<input type="text" />');
            });

            // dataTable
            var table = $('.dataTables-medicoes').DataTable();

            // aplica a busca
            table.columns().every(function () {
                var that = this;

                $('input', this.footer()).on('keyup change', function () {
                    if (that.search() !== this.value) {
                        that
                            .search(this.value)
                            .draw();
                    }
                });
            });

            // lista todas medições em atraso
            regExSearch = "^(?=.*?(1|2|3|4|5|6)).*?";
            $('.dataTables-medicoes').DataTable().column(0).search(regExSearch, true, false).draw();

            // lista todas medições em atraso
            regExSearch = "^(?=.*?(1|2|3|4|5|6|7|8)).*?";
            $('.dataTables-medicoes').DataTable().column(5).search(regExSearch, true, false).draw();

        });

        function SelecionouTipo() {

            // pega tipo selecionado
            var tipo = document.getElementById("Tipos").selectedIndex;

            // verifica se todos
            if (tipo == 0) {
                // lista somente medições em atraso
                regExSearch = "^(?=.*?(1|2|3|4|5|6)).*?";
                $('.dataTables-medicoes').DataTable().column(0).search(regExSearch, true, false).draw();
            }
            else {
                // lista tipo selecionado
                $('.dataTables-medicoes').DataTable().column(0).search(tipo).draw();
            }
        }

        function SelecionouTipoMedicao() {

            // pega tipo selecionado
            var tipo = document.getElementById("TiposMedicao").selectedIndex;

            // verifica se todos
            if (tipo == 0) {
                // lista todos
                regExSearch = "^(?=.*?(1|2|3|4|5|6|7|8)).*?";
                $('.dataTables-medicoes').DataTable().column(5).search(regExSearch, true, false).draw();
            }
            else {
                // lista tipo selecionado
                $('.dataTables-medicoes').DataTable().column(5).search(tipo).draw();
            }
        }

</script>
}

