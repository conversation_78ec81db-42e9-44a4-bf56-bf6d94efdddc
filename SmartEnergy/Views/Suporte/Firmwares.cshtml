﻿@model IEnumerable<SmartEnergyLib.SQL.FirmwaresDominio>

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = "Firmwares";
}

<style>
    .icones {
        padding: 0px 5px;
        font-size: 18px;
        float: left;
    }

    .titulo {
        margin: 0;
        margin-left: 0px;
        margin-top: 20px;
        margin-bottom: 15px;
        padding-left: 15px;
        padding-top: 5px;
        padding-bottom: 5px;
        font-size: 12px;
        font-weight: bold;
        border-radius: 10px;
        float: left;
    }

    .link_branco a:link {
        color: #ffffff !important;
    }

    .link_branco a:visited {
        color: #ffffff !important;
    }

    .link_branco a:hover {
        color: #f0f0f0 !important;
    }

    .link_branco a:active {
        color: #ffffff !important;
    }

    .link_preto a:link {
        color: #7E6A6C !important;
    }

    .link_preto a:visited {
        color: #7E6A6C !important;
    }

    .link_preto a:hover {
        color: #a0a0a0 !important;
    }

    .link_preto a:active {
        color: #7E6A6C !important;
    }
</style>

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-heading">
                    <h4>Firmwares</h4>
                    <div class="pull-right dashboard-tools link_branco" style="margin-top:-8px;">
                        <h4>

                            @if (ViewBag.Permissao == PERMISSOES.ADMIN)
                            {
                                <a href='@("/Suporte/Firmwares_Editar?IDFirmware=0")' id="BotaoAdicionar" title="@SmartEnergy.Resources.ComumTexts.BotaoInserir"><i class="fa fa-plus"></i></a>
                            }

                        </h4>
                    </div>
                </div>
                <div class="panel-body">

                    <table class="table table-striped table-bordered table-hover dataTables-firmwares">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.Modelo</th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.Versao</th>
                                <th>Status</th>
                                <th>Biblioteca</th>
                                <th class="some_minidesktop">Criação</th>
                                <th class="some_minidesktop">ColdBoot?</th>
                                <th class="some_minidesktop">Número de Drivers</th>
                                <th class="some_minidesktop"></th>
                            </tr>
                        </thead>
                        <tbody>

                            @foreach (FirmwaresDominio fw in Model)
                            {
                                // tipo gateway
                                List<ListaTiposDominio> listaTipoGateway = ViewBag.listaTipoGateway;
                                ListaTiposDominio tipo = listaTipoGateway.Find(item => item.ID == fw.IDTipoGateway);
                                string tipo_gateway = "";
                                if (tipo != null)
                                {
                                    tipo_gateway = tipo.Descricao;
                                }
                                
                                // status
                                List<ListaTiposDominio> listaFirmwareStatus = ViewBag.listaFirmwareStatus;
                                tipo = listaFirmwareStatus.Find(item => item.ID == fw.IDTipoFirmwareStatus);
                                string status = "---";
                                if (tipo != null)
                                {
                                    status = tipo.Descricao;
                                }

                                // criação
                                string DataHora = "-";
                                string DataHora_Sort = "20000101000000";

                                if (fw.Criacao != null)
                                {
                                    DataHora = String.Format("{0:d}", fw.Criacao);
                                    DataHora_Sort = String.Format("{0:yyyyMMdd}", fw.Criacao);
                                }
                                
                                <tr>
                                    <td>@fw.IDFirmware</td>
                                    <td>@tipo_gateway</td>
                                    <td>@fw.Versao</td>
                                    <td><span style="display:none;">@fw.IDTipoFirmwareStatus</span>@status</td>
                                    <td>@fw.Lib</td>
                                    <td class="some_minidesktop"><span style="display:none;">@DataHora_Sort</span>@DataHora</td>
                                    <td class="some_minidesktop">@(fw.Coldboot ? "Sim" : "Não")</td>
                                    <td>@fw.NumDrivers</td>

                                    <td class="some_minidesktop link_preto">
                                        <a href='@("/Suporte/Firmwares_Editar?IDFirmware=" + @fw.IDFirmware.ToString())' title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"><i class="fa fa-edit icones"></i></a>

                                        @if (ViewBag.Permissao == PERMISSOES.ADMIN)
                                        {
                                            <a href="#" onclick="javascript:Excluir(@fw.IDFirmware);" id="BotaoExcluir" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-times icones"></i></a>
                                        }

                                    </td>
                                </tr>
                            }

                        </tbody>
                    </table>

                </div>
            </div>
        </div>
    </div>


</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        $(document).ready(function () {

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "portugues-pre": function (data) {
                    var a = 'a';
                    var e = 'e';
                    var i = 'i';
                    var o = 'o';
                    var u = 'u';
                    var c = 'c';
                    var special_letters = {
                        "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                        "É": e, "é": e, "Ê": e, "ê": e,
                        "Í": i, "í": i, "Î": i, "î": i,
                        "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                        "Ú": u, "ú": u, "Ü": u, "ü": u,
                        "ç": c, "Ç": c
                    };
                    for (var val in special_letters)
                        data = data.split(val).join(special_letters[val]).toLowerCase();
                    return data;
                },
                "portugues-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },
                "portugues-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "numero-pre": function (a) {
                    var x = (a == "-") ? 0 : a.replace(/,/, ".");
                    return parseFloat(x);
                },

                "numero-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },

                "numero-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            $('.dataTables-firmwares').DataTable({
                "iDisplayLength": 18,
                dom: 'ftp',
                "bAutoWidth": false,
                "aoColumnDefs": [
                            { "aTargets": [0], "sType": "numero" },
                            { "aTargets": [1], "sType": "portugues" },
                            { "aTargets": [2], "sType": "numero" },
                            { "aTargets": [3], "sType": "portugues" },
                            { "aTargets": [4], "sType": "numero" },
                            { "aTargets": [5], "sType": "portugues" },
                            { "aTargets": [6], "sType": "portugues" },
                            { "aTargets": [7], "sType": "numero" },
                            { "aTargets": [8], "bVisible": true, "bSortable": false, "bSearchable": false },
                ],

                "aoColumns": [
                { sWidth: "5%" },
                { sWidth: "20%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "15%" },
                { sWidth: "10%" }
                ],
                'order': [[0, 'desc']],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }

            });

            // desabilita campos
            disableAll();
        });

        function disableAll() {

            var permissao = Math.ceil(@ViewBag.Permissao);

            // verifica permissao
            switch (permissao) {
                case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
                case PERMISSOES_PRODUCAO:           // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)
                case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
                case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte

                    break;

                default:
                case PERMISSOES_GERENTE:    // 1 - permissao de Gerente: ve tudo e escreve parte
                case PERMISSOES_OPERADOR:   // 2 - permissao de Operador: ve tudo e nao pode escrever
                case PERMISSOES_CONSULTOR:  // 5 - permissao de Consultor

                    document.getElementById("BotaoAdicionar").style.visibility = "hidden";
                    break;
            }
        }

        function Excluir(IDFirmware) {
            event.stopPropagation();

            // titulo
            titulo = "Deseja excluir o Firmware?<br/>ID " + IDFirmware;

            swal({
                html: true,
                title: titulo,
                text: "Esta operação não poderá ser desfeita.",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#18a689",
                confirmButtonText: "Excluir",
                cancelButtonText: "Cancelar",
                closeOnConfirm: false
            }, function () {

                // excluir
                $.ajax(
                {
                    type: 'GET',
                    url: '/Suporte/Firmwares_Excluir',
                    data: { 'IDFirmware': IDFirmware },
                    contentType: 'application/json; charset=utf-8',
                    dataType: 'json',
                    cache: false,
                    async: true,
                    success: function (data) {

                        setTimeout(function () {

                            // verifica se erro
                            if (data.status == "ERRO") {
                                swal({
                                    html: true,
                                    title: "Erro",
                                    text: data.erro,
                                    type: "warning",
                                    confirmButtonColor: "#f8ac59",
                                    confirmButtonText: "Fechar",
                                }, function () {

                                });
                            }
                            else {
                                swal({
                                    title: "Excluído com sucesso",
                                    type: "success",
                                    confirmButtonColor: "#18a689",
                                    confirmButtonText: "Fechar",
                                }, function () {

                                    // atualiza pagina
                                    location.reload();
                                });
                            }

                        }, 100);
                    },
                    error: function (response) {

                        setTimeout(function () {

                            swal({
                                title: "Erro",
                                text: "Erro ao excluir!",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            });

                        }, 100);

                    }
                });
            });
        };

    </script>
}


