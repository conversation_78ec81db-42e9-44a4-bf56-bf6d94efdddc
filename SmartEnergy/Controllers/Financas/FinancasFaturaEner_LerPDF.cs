﻿using DualLab.Pdf2data;
using DualLab.Pdf2data.Result;
using DualLab.Pdf2data.Template;
using iText.License;
using System;
using System.Globalization;
using System.Web.Mvc;
using System.Xml;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class FinancasController
    {

        // GET: Fatura de Energia Eletrica - Ler fatura em PDF
        public ActionResult Fatura_Energia_LerPDF()
        {

            // le cookies
            LeCookies_SmartEnergy();

            FaturaPDFDominio faturaPDF = new FaturaPDFDominio();

            faturaPDF.IDFatura = 0;
            faturaPDF.IDCliente = ViewBag._IDCliente;
            faturaPDF.IDMedicao = ViewBag._IDMedicao;
            faturaPDF.nomePDF = "";

            return View(faturaPDF);
        }

        // POST: Fatura de Energia Eletrica - Salvar
        [HttpPost]
        public ActionResult Fatura_Energia_Salvar(FaturaPDFDominio fatura)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // salva
            FaturasMetodos faturaMetodos = new FaturasMetodos();
            FaturasDominio faturaSalvar = new FaturasDominio();

            // copia dados
            faturaSalvar.IDCliente = fatura.IDCliente;
            faturaSalvar.IDMedicao = fatura.IDMedicao;
            faturaSalvar.DataHoraIni = fatura.DataHoraIni;
            faturaSalvar.DataHoraFim = fatura.DataHoraFim;
            faturaSalvar.total = fatura.TotalFatura;

            faturaMetodos.Salvar(faturaSalvar);

            // retorna status
            return Json(returnedData);
        }


        class Retorno
        {
            public string status = "ERRO";
            public string resultado = "Conversão falhou.";

            public string UnidadeConsumidora = "";
            public string Enquadramento = "";

            public string DataHoraIni = "";
            public string DataHoraFim = "";

            public string ImporteICMS = "";
            public string AliquotaICMS = "";
            public string ValorICMS = "";
            public string TotalFatura = "";

            public string[] Descricao = new string[100];
            public string[] LeituraAtual = new string[100];
            public string[] Registrado = new string[100];
            public string[] Contratado = new string[100];
            public string[] Faturado = new string[100];
            public string[] Tarifa = new string[100];
            public string[] Valores = new string[100];
        }

        // GET: Fatura de Energia Eletrica - Converter fatura em PDF
        public ActionResult Fatura_Energia_ConverterPDF(string nomePDF)
        {
            // retorno
            Retorno returnedData = new Retorno();

            // verifica se tem arquivo
            if( nomePDF != null )
            {
                // verifica se arquivo existe
                string pathForParse = System.IO.Path.Combine(Server.MapPath("~/PDF"), nomePDF);

                if( System.IO.File.Exists(pathForParse) )
                {
                    // licença
                    string pathForLicense = System.IO.Path.Combine(Server.MapPath("~/PDF/Templates"), "itextkey1569852787480_0.xml");

                    // le licença
                    LicenseKey.LoadLicenseFile(pathForLicense);

                    // template
                    string pathForTemplate = System.IO.Path.Combine(Server.MapPath("~/PDF/Templates"), "Tmpl - Eletropaulo_1.pdf");

                    // faz parse do template
                    Template template = Pdf2DataExtractor.ParseTemplateFromPDF(pathForTemplate);

                    // cria uma instancia de Pdf2DataExtractor para o template
                    Pdf2DataExtractor extractor = new Pdf2DataExtractor(template);

                    // extrai valores
                    ParsingResult result = extractor.Recognize(pathForParse);

                    // salva valores no XML
                    string nomeXML = string.Format("{0}.xml", System.IO.Path.GetFileNameWithoutExtension(nomePDF));
                    string pathForXML = System.IO.Path.Combine(Server.MapPath("~/PDF"), nomeXML);
                    result.SaveToXML(pathForXML);

                    // le XML
                    string UnidadeConsumidora = "";
                    string Enquadramento = "";
                    string DataHoraIni = "";
                    string DataHoraFim = "";
                    string ImporteICMS = "";
                    string AliquotaICMS = "";
                    string ValorICMS = "";
                    string TotalFatura = "";
                    string[] Descricao = new string[100];
                    string[] LeituraAtual = new string[100];
                    string[] Registrado = new string[100];
                    string[] Contratado = new string[100];
                    string[] Faturado = new string[100];
                    string[] Tarifa = new string[100];
                    string[] Valores = new string[100];


                    // abre arquivo XML e le para string
                    XmlTextReader reader = new XmlTextReader(pathForXML);
                    string xmlString = System.IO.File.ReadAllText(pathForXML);

                    XmlDocument xml = new XmlDocument();
                    xml.LoadXml(xmlString);

                    // Unidade Consumidora
                    UnidadeConsumidora = LeNode(xml, xmlString, "Contrato");

                    // Enquadramento Tarifario
                    Enquadramento = LeNode(xml, xmlString, "Enquadramento");

                    // Data da Leitura Atual
                    DataHoraFim = LeNode(xml, xmlString, "DataLeituraAtual", 2, "dd.MM.yyyy");

                    // Data da Leitura Anterior
                    DataHoraIni = LeNode(xml, xmlString, "DataLeituraAnterior", 2, "dd.MM.yyyy");

                    // Importe do ICMS
                    ImporteICMS = LeNode(xml, xmlString, "ImporteICMS", 1);

                    // Aliquota
                    AliquotaICMS = LeNode(xml, xmlString, "AliquotaICMS", 1);

                    // Valor ICMS
                    ValorICMS = LeNode(xml, xmlString, "ValorICMS", 1);

                    // Total da Fatura
                    TotalFatura = LeNode(xml, xmlString, "TotalFatura",1 );

                    // Tabela
                    Descricao = LeNodeList(xml, xmlString, "Descricao");
                    LeituraAtual = LeNodeList(xml, xmlString, "LeituraAtual");
                    Registrado = LeNodeList(xml, xmlString, "Registrado");
                    Contratado = LeNodeList(xml, xmlString, "Contratado");
                    Faturado = LeNodeList(xml, xmlString, "Faturado");
                    Tarifa = LeNodeList(xml, xmlString, "Tarifa");
                    Valores = LeNodeList(xml, xmlString, "Valores");

                    // ok
                    returnedData.status = "OK";
                    returnedData.resultado = "";

                    returnedData.UnidadeConsumidora = UnidadeConsumidora;
                    returnedData.Enquadramento = Enquadramento;
                    returnedData.DataHoraIni = DataHoraIni;
                    returnedData.DataHoraFim = DataHoraFim;
                    returnedData.ImporteICMS = ImporteICMS;
                    returnedData.AliquotaICMS = AliquotaICMS;
                    returnedData.ValorICMS = ValorICMS;
                    returnedData.TotalFatura = TotalFatura;
                    returnedData.Descricao = Descricao;
                    returnedData.LeituraAtual = LeituraAtual;
                    returnedData.Registrado = Registrado;
                    returnedData.Contratado = Contratado;
                    returnedData.Faturado = Faturado;
                    returnedData.Tarifa = Tarifa;
                    returnedData.Valores = Valores;
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // Le node do XML
        private string LeNode(XmlDocument xml, string xmlString, string nomeNode, int tipo_formato = 0, string formato = "")
        {
            // selecao
            string selecao = string.Format("/elements/data[@name='{0}']", nomeNode);
            string retorno = "";

            XmlNodeList xnList = xml.SelectNodes(selecao);

            // tipo formato
            switch(tipo_formato)
            {
                case 0:

                    foreach (XmlNode xn in xnList)
                    {
                        if (xn.HasChildNodes)
                        {
                            for (int i = 0; i < xn.ChildNodes.Count; i++)
                            {
                                retorno =  xn.ChildNodes[i].InnerText;
                            }
                        }
                    }

                    break;

                case 1:

                    foreach (XmlNode xn in xnList)
                    {
                        double result_Double;

                        retorno = xn.InnerText;

                        try
                        {
                            result_Double = Double.Parse(retorno);
                            retorno = string.Format("{0}", result_Double);
                        }
                        catch (FormatException)
                        {
                            retorno = "0";
                        }
                    }

                    break;

                case 2:

                    foreach (XmlNode xn in xnList)
                    {
                        DateTime result_Data;
                        CultureInfo provider = CultureInfo.InvariantCulture;

                        retorno = xn.InnerText;

                        try
                        {
                            result_Data = DateTime.ParseExact(retorno, formato, provider);
                            retorno = string.Format("{0:d}", result_Data);
                        }
                        catch (FormatException)
                        {
                            retorno = "--/--/----";
                        }
                    }

                    break;
            }

            // retorna
            return (retorno);
        }

        // Le node list do XML
        private string[] LeNodeList(XmlDocument xml, string xmlString, string nomeNode, int tipo_formato = 0, string formato = "")
        {
            // selecao
            string selecao = string.Format("/elements/data[@name='{0}']", nomeNode);
            string[] retorno = new string[100];

            XmlNodeList xnList = xml.SelectNodes(selecao);

            // tipo formato
            switch (tipo_formato)
            {
                case 0:

                    foreach (XmlNode xn in xnList)
                    {
                        if (xn.HasChildNodes)
                        {
                            for (int i = 0; i < xn.ChildNodes.Count; i++)
                            {
                                // protege
                                if(i >= 100 )
                                {
                                    break;
                                }

                                retorno[i] = xn.ChildNodes[i].InnerText;
                            }
                        }
                    }

                    break;
            }

            // retorna
            return (retorno);
        }

    }
}