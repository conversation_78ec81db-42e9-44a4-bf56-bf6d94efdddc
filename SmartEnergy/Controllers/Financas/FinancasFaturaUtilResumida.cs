﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class FinancasController
    {

        // GET: Fatura de Utilidades Resumida
        public ActionResult Fatura_Utilidades_Resumida(int IDCliente, int tipo_arquivo = 0)
        {
            // fatura de utilidades
            return (Fatura_Utilidades_Resumida_Show(IDCliente, tipo_arquivo));
        }

        // GET: Fatura Utilidades Resumida
        private ActionResult Fatura_Utilidades_Resumida_Show(int IDCliente, int tipo_arquivo = 0)
        {
            // tela de ajuda - fatura
            CookieStore.SalvaCookie_String("PaginaAjuda", "Financas_Fatura_UtilidadesResumida");

            // salva cookie 
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Fatura");

            // le cookies
            LeCookies_SmartEnergy();

            // data inicio
            DateTime data_hora_ini = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 0, 0);

            // data fim
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            // le cookie datahora
            DateTime data_hora_ini_cookie = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime data_hora_fim_cookie = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // verifica se existe datahora no cookie
            if (data_hora_ini_cookie.Year != 2000 && data_hora_fim_cookie.Year != 2000)
            {
                // copia data do cookie 
                data_hora_ini = data_hora_ini_cookie;
                data_hora_fim = data_hora_fim_cookie;
            }

            // converte
            DATAHORA dh_ini = new DATAHORA();
            DATAHORA dh_fim = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, data_hora_ini);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, data_hora_fim);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", data_hora_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", data_hora_fim);

            // data e hora atual
            ViewBag.DataIni = string.Format("{0:d}", data_hora_ini);
            ViewBag.HoraIni = string.Format("{0:HH:mm}", data_hora_ini);
            ViewBag.DataFim = string.Format("{0:d}", data_hora_fim);
            ViewBag.HoraFim = string.Format("{0:HH:mm}", data_hora_fim);

            ViewBag.DataTextoAtualIni = string.Format("{0:d} {1:HH:mm}", data_hora_ini, data_hora_ini);
            ViewBag.DataTextoAtualFim = string.Format("{0:d} {1:HH:mm}", data_hora_fim, data_hora_fim);

            ViewBag.DataAtualN = data_hora_ini;
            ViewBag.DataFinalN = data_hora_fim;

            return View();
        }

        // GET: Fatura Atualizar
        public ActionResult Fatura_Utilidades_Resumida_Atualizar(int Navegacao, string DataIni, string DataFim)
        {
            // caso forcar data
            if (Navegacao == 100)
            {
                // pega data
                DateTime dateValueIni = DateTime.Parse(DataIni);
                DateTime dateValueFim = DateTime.Parse(DataFim);

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Fatura_DataIni", dateValueIni);
                CookieStore.SalvaCookie_Datahora("Fatura_DataFim", dateValueFim);
            }

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;

            // le cookie datahora
            DateTime datahora_cookie_ini = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -3:    // mes anterior

                    datahora_cookie_ini = datahora_cookie_ini.AddMonths(-1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(-1);
                    break;

                case 3:    // mes seguinte

                    datahora_cookie_ini = datahora_cookie_ini.AddMonths(1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(1);
                    break;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", datahora_cookie_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", datahora_cookie_fim);

            // data e hora atual
            ViewBag.DataIni = string.Format("{0:d}", datahora_cookie_ini);
            ViewBag.HoraIni = string.Format("{0:HH:mm}", datahora_cookie_ini);
            ViewBag.DataFim = string.Format("{0:d}", datahora_cookie_fim);
            ViewBag.HoraFim = string.Format("{0:HH:mm}", datahora_cookie_fim);

            ViewBag.DataTextoAtualIni = string.Format("{0:d} {1:HH:mm:ss}", datahora_cookie_ini, datahora_cookie_ini);
            ViewBag.DataTextoAtualFim = string.Format("{0:d} {1:HH:mm:ss}", datahora_cookie_fim, datahora_cookie_fim);

            ViewBag.DataAtualN = datahora_cookie_ini;
            ViewBag.DataFinalN = datahora_cookie_fim;

            // converte
            DATAHORA dh_ini = new DATAHORA();
            DATAHORA dh_fim = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, datahora_cookie_ini);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, datahora_cookie_fim);

            // retorna status
            return Json(0, JsonRequestBehavior.AllowGet);
        }

        // GET: Fatura Resumida Print
        public ActionResult Fatura_Utilidades_Resumida_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Fatura_Utilidades_Resumida_Show(IDCliente);

            // resultado
            ViewBag.fatura_resultado = lista_util.Keys.Contains(id) ? lista_util[id] : null;

            // imprime
            return View();
        }

        // GET: Fatura Resumida EMAIL
        public async Task<ActionResult> Fatura_Utilidades_Resumida_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Fatura_Utilidades_Resumida_Show(IDCliente);

            // resultado
            ViewBag.fatura_resultado = lista_util.Keys.Contains(id) ? lista_util[id] : null;

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("FaturaUtilResumida_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDCliente, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_Fatura_Utilidades_Resumida_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "FaturaResumidaEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtualIni", ViewBag.DataTextoAtualIni);
            message = message.Replace("ViewBag.DataTextoAtualFim", ViewBag.DataTextoAtualFim);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Fatura Resumida PDF
        public ActionResult Fatura_Utilidades_Resumida_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Fatura_Utilidades_Resumida_Show(IDCliente);

            // resultado
            ViewBag.fatura_resultado = lista_util.Keys.Contains(id) ? lista_util[id] : null;

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("FaturaUtilResumida_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDCliente, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_Fatura_Utilidades_Resumida_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Fatura Resumida XLS
        public ActionResult Fatura_Utilidades_Resumida_XLS()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Fatura_Utilidades_Resumida_Show(IDCliente);

            // resultado
            ViewBag.fatura_resultado = lista_util.Keys.Contains(id) ? lista_util[id] : null;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_Fatura_Utilidades_Resumida(IDCliente);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("FaturaUtilResumida_{0:000000}_{1:yyyyMMddHHmm}.xls", IDCliente, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Fatura Resumida XLS Download
        [HttpGet]
        public virtual ActionResult Fatura_Utilidades_Resumida_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }


        // estruturas
        private static IDictionary<Guid, int> tasks_util = new Dictionary<Guid, int>();
        private List<FATURA_UTIL_RESUMIDA> lista_tmp_util = new List<FATURA_UTIL_RESUMIDA>();
        private static IDictionary<Guid, List<FATURA_UTIL_RESUMIDA>> lista_util = new Dictionary<Guid, List<FATURA_UTIL_RESUMIDA>>();

        public ActionResult Fatura_Utilidades_Resumida_IniciaCalculo()
        {
            // taskID
            Guid taskId = Guid.NewGuid();
            tasks_util.Add(taskId, 0);

            // salva cookie
            CookieStore.SalvaCookie_GUID("taskID", taskId);

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;

            // le cookie datahora
            DateTime datahora_cookie_ini = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // limpa lista
            lista_tmp_util.Clear();

            // task
            Task.Factory.StartNew(() =>
            {
                // le medicoes
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                List<MedicoesDominio> medicoes = medicoesMetodos.ListarTodos(IDCliente, ViewBag._ConfigMed);

                // estruturas

                // lista de erros
                var listaErros = new List<string>();

                // preenche solicitacao
                CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
                config_interface.sweb.id_cliente = IDCliente;

                // converte
                DATAHORA dh_ini = new DATAHORA();
                DATAHORA dh_fim = new DATAHORA();
                Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, datahora_cookie_ini);
                Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, datahora_cookie_fim);

                // estrutura
                RESULT_UTIL_FATURA fatura = new RESULT_UTIL_FATURA();

                // barra de progresso
                int total = 0;
                int atual = 0;

                // le tarifasutilgrupos
                TarifasUtilGruposMetodos listaTarifasUtilGruposMetodos = new TarifasUtilGruposMetodos();
                List<TarifasUtilGruposDominio> listaTarifasUtilGrupos = listaTarifasUtilGruposMetodos.ListarPorIDCliente(IDCliente);

                // percorre medicoes
                if (medicoes != null)
                {
                    // barra de progresso
                    total = medicoes.Count();

                    // percorre medicoes
                    foreach (MedicoesDominio medicao in medicoes)
                    {
                        // update task progress
                        double progresso = ((double)atual / (double)total) * 100;
                        tasks_util[taskId] = (int)progresso;
                        atual++;

                        // verifica se nao eh utilidades
                        if (medicao.IDTipoMedicao != 2 && medicao.IDTipoMedicao != 5)
                        {
                            continue;
                        }

                        // preenche solicitacao
                        config_interface.sweb.id_medicao = medicao.IDMedicao;
                        config_interface.sweb.id_gateway = medicao.IDGateway;

                        // calcula valores fatura
                        int retorno = SmCalcDB_Util_Fatura((char)0, ref config_interface, ref dh_ini, ref dh_fim, ref fatura);

                        // coloca resultado na lista temporaria
                        FATURA_UTIL_RESUMIDA fatura_resumida = new FATURA_UTIL_RESUMIDA();
                        fatura_resumida.IDMedicao = medicao.IDMedicao;
                        fatura_resumida.Nome_Medicao = medicao.Nome;
                        fatura_resumida.UnidadeGrandeza = medicao.UnidadeGrandeza;

                        // tarifasutilgrupos
                        TarifasUtilGruposDominio tipoTarifasUtilGrupos = listaTarifasUtilGrupos.Find(item => item.IDGrupoTarifas == medicao.IDAgenteDistribuidora);
                        if (tipoTarifasUtilGrupos != null && medicao.IDAgenteDistribuidora != 0)
                        {
                            fatura_resumida.GrupoTarifas = tipoTarifasUtilGrupos.Nome;
                        }
                        else
                        {
                            fatura_resumida.GrupoTarifas = "---";
                        }

                        fatura_resumida.retorno = retorno;
                        fatura_resumida.fatura = fatura;

                        lista_tmp_util.Add(fatura_resumida);
                    }
                }

                // coloca resultado na lista
                lista_util.Add(taskId, new List<FATURA_UTIL_RESUMIDA>(lista_tmp_util));

                // terminou
                tasks_util.Remove(taskId);
            });

            // retorna status
            return Json(taskId, JsonRequestBehavior.AllowGet);
        }

        public ActionResult Fatura_Utilidades_Resumida_Progress(Guid id)
        {
            return Json(tasks_util.Keys.Contains(id) ? tasks_util[id] : 100, JsonRequestBehavior.AllowGet);
        }

        public PartialViewResult _Fatura_Utilidades_Resumida(Guid id)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // resultado
            ViewBag.fatura_resultado = lista_util.Keys.Contains(id) ? lista_util[id] : null;

            return PartialView();
        }

        // Fatura Resumida XLS
        private HSSFWorkbook XLS_Fatura_Utilidades_Resumida(int IDCliente)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            ICellStyle _textoBorderCellStyle = criaEstiloXLS(workbook, 100, true);
            ICellStyle _1CellBorderStyle = criaEstiloXLS(workbook, 1, true);
            ICellStyle _2CellBorderStyle = criaEstiloXLS(workbook, 2, true);

            //
            // MEDICOES
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Fatura Resumida");

            // cabecalho
            string[] cabecalho = { "Medições", "Tipo", "Unidade", "Consumo Horário Médio", "Consumo Total", "Total (R$)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            List<FATURA_UTIL_RESUMIDA> fatura_resultado = ViewBag.fatura_resultado;
            int i;
            IRow row;

            // percorre valores
            if (fatura_resultado != null)
            {
                foreach (FATURA_UTIL_RESUMIDA fat in fatura_resultado)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex++);

                    // medicao
                    textoCelulaXLS(row, 0, fat.Nome_Medicao);

                    if (fat.retorno != 0)
                    {
                        textoCelulaXLS(row, 1, "---");
                        textoCelulaXLS(row, 2, "---");
                        textoCelulaXLS(row, 3, "---");
                        textoCelulaXLS(row, 4, "---");
                        numeroCelulaXLS(row, 5, 0.0, _2CellStyle);
                    }
                    else
                    {
                        textoCelulaXLS(row, 1, fat.GrupoTarifas);
                        textoCelulaXLS(row, 2, fat.UnidadeGrandeza);
                        numeroCelulaXLS(row, 3, fat.fatura.consumo_med, _2CellStyle);
                        numeroCelulaXLS(row, 4, fat.fatura.consumo_total, _2CellStyle);
                        numeroCelulaXLS(row, 5, fat.fatura.total, _2CellStyle);
                    }
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS_FatResumida(workbook, sheet, _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(4).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }
    }
}
