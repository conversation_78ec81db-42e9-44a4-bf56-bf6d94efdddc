﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class FinancasController
    {
        // GET: PLD - Historico
        public ActionResult PLD_Historico()
        {
            // tela de ajuda - ICMS
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le PLD
            HistoricoPLDMetodos pldMetodos = new HistoricoPLDMetodos();
            List<HistoricoPLDDominio> listaPLD = pldMetodos.ListarTodos();
            ViewBag.listaPLD = listaPLD;

            return View();
        }

        // GET: PLD - Editar
        public ActionResult PLD_Editar(string DataTexto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // converte data
            DateTime Data = DateTime.ParseExact(DataTexto, "MM/yyyy", CultureInfo.InvariantCulture);

            // verifica se adicionando
            HistoricoPLDDominio pld = new HistoricoPLDDominio();
            if (Data.Year == 2000)
            {
                // zera PLD com default
                DateTime hoje = DateTime.Now;
                pld.Data = new DateTime(hoje.Year, hoje.Month, 1, 0, 0, 0);
                pld.DataTexto = pld.Data.ToString("MM/yyyy");
                pld.SE_CO = 0.0;
                pld.S = 0.0;
                pld.NE = 0.0;
                pld.N = 0.0;
                pld.Fora_SIN = 0.0;
            }
            else
            {
                // le PLD
                HistoricoPLDMetodos pldMetodos = new HistoricoPLDMetodos();
                pld = pldMetodos.ListarPorMes(Data);
            }

            return View(pld);
        }

        // POST: PLD - Salvar
        [HttpPost]
        public ActionResult PLD_Salvar(HistoricoPLDDominio pld)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // converte data
            pld.Data = DateTime.ParseExact(pld.DataTexto, "MM/yyyy", CultureInfo.InvariantCulture);

            // excluo a data
            HistoricoPLDMetodos pldMetodos = new HistoricoPLDMetodos();
            pldMetodos.Excluir(pld.Data);

            // verifica se existe outro PLD com a mesma data
            if (pldMetodos.VerificarDuplicidade(pld))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Data existente."
                };
            }
            else
            {
                // salva ICMS
                pldMetodos.Salvar(pld);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: ICMS - Excluir
        public ActionResult PLD_Excluir(string DataTexto)
        {
            // converte data
            DateTime Data = DateTime.ParseExact(DataTexto, "MM/yyyy", CultureInfo.InvariantCulture);

            // apaga o PLD
            HistoricoPLDMetodos pldMetodos = new HistoricoPLDMetodos();
            pldMetodos.Excluir(Data);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}