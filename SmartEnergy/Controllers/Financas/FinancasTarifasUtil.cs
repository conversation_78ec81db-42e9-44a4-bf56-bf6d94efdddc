﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class FinancasController
    {
        // GET: Tarifas de Utilidades - Historico
        public ActionResult TarifasUtil_Historico(int IDGrupoTarifas)
        {
            // tela de ajuda - tarifas
            CookieStore.SalvaCookie_String("PaginaAjuda", "Financas_Configuracao_Tarifas_Utilidades");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le grupo
            TarifasUtilGruposMetodos grupoMetodos = new TarifasUtilGruposMetodos();
            TarifasUtilGruposDominio grupo = grupoMetodos.ListarPorId(IDGrupoTarifas);

            ViewBag.IDGrupoTarifas = grupo.IDGrupoTarifas;
            ViewBag.Nome = grupo.Nome;

            // le historico das tarifas do grupo
            TarifasUtilMetodos tarifasMetodos = new TarifasUtilMetodos();
            List<TarifasUtilDominio> listaTarifas = tarifasMetodos.ListarPorIDGrupo(IDGrupoTarifas);
            return View(listaTarifas);
        }

        // GET: Tarifas de Utilidades - Editar
        public ActionResult TarifasUtil_Editar(int IDGrupoTarifas, int IDTarifa)
        {
            // tela de ajuda - tarifas
            CookieStore.SalvaCookie_String("PaginaAjuda", "Financas_Configuracao_Tarifas_Utilidades_Editar");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le grupo
            TarifasUtilGruposMetodos grupoMetodos = new TarifasUtilGruposMetodos();
            TarifasUtilGruposDominio grupo = grupoMetodos.ListarPorId(IDGrupoTarifas);

            ViewBag.Nome = grupo.Nome;

            // verifica se adicionando
            TarifasUtilDominio tarifa = new TarifasUtilDominio();
            if (IDTarifa == 0)
            {
                // zera grupo com default
                tarifa.IDTarifa = 0;
                tarifa.IDCliente = ViewBag._IDCliente;
                tarifa.IDGrupoTarifas = IDGrupoTarifas;
                tarifa.Data = DateTime.Now;
                tarifa.Faixa1 = 0.0;
                tarifa.Faixa2 = 0.0;
                tarifa.Faixa3 = 0.0;
                tarifa.Faixa4 = 0.0;
                tarifa.Faixa5 = 0.0;
                tarifa.Faixa6 = 0.0;
            }
            else
            {
                // le tarifa
                TarifasUtilMetodos tarifaMetodos = new TarifasUtilMetodos();
                tarifa = tarifaMetodos.ListarPorId(IDTarifa);
            }

            return View(tarifa);
        }

        // POST: Tarifas de Utilidades - Salvar
        [HttpPost]
        public ActionResult TarifasUtil_Salvar(TarifasUtilDominio tarifa)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outra tarifa com a mesma data
            TarifasUtilMetodos tarifaMetodos = new TarifasUtilMetodos();
            if (tarifaMetodos.VerificarDuplicidade(tarifa))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Data existente."
                };
            }
            else
            {
                // salva tarifa
                tarifaMetodos.Salvar(tarifa);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Tarifas de Utilidades - Excluir
        public ActionResult TarifasUtil_Excluir(int IDTarifa)
        {
            // tarifa
            TarifasUtilMetodos tarifasMetodos = new TarifasUtilMetodos();
            tarifasMetodos.Excluir(IDTarifa);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}