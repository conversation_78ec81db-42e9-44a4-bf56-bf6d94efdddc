﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class FinancasController
    {
        // GET: Configuracao FaturaOutros - Historico
        public ActionResult FaturaOutros_Historico(int IDMedicao)
        {
            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Financas");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le FaturaOutros
            FaturaOutrosMetodos outrosMetodos = new FaturaOutrosMetodos();
            List<FaturaOutrosDominio> listaOutros = outrosMetodos.ListarPorIDMedicao(IDMedicao);

            ViewBag.listaOutros = listaOutros;

            return View();
        }

        // GET: Configuracao FaturaOutros - Editar
        public ActionResult FaturaOutros_Editar(int IDOutros, int IDMedicao)
        {
            // pagina anterior
            ViewBag.returnUrl = Request.UrlReferrer;

            // ID Outros
            ViewBag.IDOutros = IDOutros;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Financas");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // verifica se adicionando
            FaturaOutrosDominio outros = new FaturaOutrosDominio();
            if (IDOutros == 0)
            {
                // zera com default
                outros.IDOutros = 0;
                outros.IDCliente = ViewBag._IDCliente;
                outros.IDMedicao = IDMedicao;
                outros.DataIni = DateTime.Now;
                outros.DataFim = DateTime.Now;
                outros.DataIniTexto = outros.DataIni.ToString("d");
                outros.DataFimTexto = outros.DataFim.ToString("d");
                outros.Descricao = "";
                outros.valorOutros = 0.0;
                outros.Calcular_PIS_COFINS = false;
                outros.Calcular_ICMS = false;
            }
            else
            {
                // le FaturaOutros
                FaturaOutrosMetodos outrosMetodos = new FaturaOutrosMetodos();
                outros = outrosMetodos.ListarPorID(IDOutros);
            }

            return View(outros);
        }

        // POST: Configuracao FaturaOutros - Salvar
        [HttpPost]
        public ActionResult FaturaOutros_Salvar(FaturaOutrosDominio outros)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // parse data
            outros.DataIni = DateTime.Parse(outros.DataIniTexto);
            outros.DataFim = DateTime.Parse(outros.DataFimTexto);

            // verifica se existe outro com a mesma descricao
            FaturaOutrosMetodos outrosMetodos = new FaturaOutrosMetodos();
            if (outrosMetodos.VerificarDuplicidade(outros))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Outros existente."
                };
            }
            else
            {
                // salva FaturaOutros
                outrosMetodos.Salvar(outros);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuracao FaturaOutros - Excluir
        public ActionResult FaturaOutros_Excluir(int IDOutros)
        {
            // apaga o FaturaOutros
            FaturaOutrosMetodos outrosMetodos = new FaturaOutrosMetodos();
            outrosMetodos.Excluir(IDOutros);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}