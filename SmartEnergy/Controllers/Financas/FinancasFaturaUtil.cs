﻿using NPOI.HSSF.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;
using System.Text.RegularExpressions;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class FinancasController
    {
        // fatura de utilidades
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Util_Fatura", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Util_Fatura(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref DATAHORA pdatahora_ini, ref DATAHORA pdatahora_fim, ref RESULT_UTIL_FATURA pfatura);


        // GET: Fatura de Utilidades
        public ActionResult Fatura_Utilidades(int IDCliente, int IDMedicao, int tipo_arquivo = 0)
        {
            // fatura de utilidades
            return (Fatura_Utilidades_Show(IDCliente, IDMedicao, tipo_arquivo));
        }

        // GET: Fatura Utilidades
        private ActionResult Fatura_Utilidades_Show(int IDCliente, int IDMedicao, int tipo_arquivo = 0)
        {
            // tela de ajuda - fatura
            CookieStore.SalvaCookie_String("PaginaAjuda", "Financas_Fatura_Utilidades");

            // le supervisao da medicao
            SupervMedicoesMetodos supmedicoesMetodos = new SupervMedicoesMetodos();
            var listaMedicoes = supmedicoesMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

            // salva cookie 
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", listaMedicoes.IDTipoMedicao_MD);
            CookieStore.SalvaCookie_String("MedicaoNome", listaMedicoes.NMED);
            CookieStore.SalvaCookie_String("UnidadeNome", listaMedicoes.NUN);
            CookieStore.SalvaCookie_String("GrupoNome", listaMedicoes.NGU);
            CookieStore.SalvaCookie_Int("_IDGateway", listaMedicoes.IDGW_GW);
            CookieStore.SalvaCookie_Int("IDTipoGateway", listaMedicoes.IDTipoGateway_GW);
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Fatura");

            int IDGateway = listaMedicoes.IDGW_GW;
            int IDTipoGateway = listaMedicoes.IDTipoGateway_GW;

            // le cookies
            LeCookies_SmartEnergy();

            // configuracao da medicao
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // grandeza
            ViewBag.UnidadeGrandeza = medicao.UnidadeGrandeza;
            ViewBag.NomeGrandeza = medicao.NomeGrandeza;

            CookieStore.SalvaCookie_String("UnidadeGrandeza", medicao.UnidadeGrandeza);
            CookieStore.SalvaCookie_String("NomeGrandeza", medicao.NomeGrandeza);


            // le tarifasutilgrupos
            TarifasUtilGruposMetodos listaTarifasUtilGruposMetodos = new TarifasUtilGruposMetodos();
            List<TarifasUtilGruposDominio> listaTarifasUtilGrupos = listaTarifasUtilGruposMetodos.ListarPorIDCliente(IDCliente);
            TarifasUtilGruposDominio tipoTarifasUtilGrupos = listaTarifasUtilGrupos.Find(item => item.IDGrupoTarifas == medicao.IDAgenteDistribuidora);
            if (tipoTarifasUtilGrupos != null)
            {
                ViewBag.TipoTarifasUtilGrupos = tipoTarifasUtilGrupos.Nome;
            }
            else
            {
                ViewBag.TipoTarifasUtilGrupos = "";
            }

            // data inicio
            DateTime data_hora_ini = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 0, 0);

            // data fim
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            // le cookie datahora
            DateTime data_hora_ini_cookie = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime data_hora_fim_cookie = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // verifica se existe datahora no cookie
            if (data_hora_ini_cookie.Year != 2000 && data_hora_fim_cookie.Year != 2000)
            {
                // copia data do cookie 
                data_hora_ini = data_hora_ini_cookie;
                data_hora_fim = data_hora_fim_cookie;
            }

            // converte
            DATAHORA dh_ini = new DATAHORA();
            DATAHORA dh_fim = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, data_hora_ini);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, data_hora_fim);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", data_hora_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", data_hora_fim);

            // data e hora atual
            ViewBag.DataIni = string.Format("{0:d}", data_hora_ini);
            ViewBag.HoraIni = string.Format("{0:HH:mm}", data_hora_ini);
            ViewBag.DataFim = string.Format("{0:d}", data_hora_fim);
            ViewBag.HoraFim = string.Format("{0:HH:mm}", data_hora_fim);

            ViewBag.DataTextoAtualIni = string.Format("{0:d} {1:HH:mm}", data_hora_ini, data_hora_ini);
            ViewBag.DataTextoAtualFim = string.Format("{0:d} {1:HH:mm}", data_hora_fim, data_hora_fim);

            ViewBag.DataAtualN = data_hora_ini;
            ViewBag.DataFinalN = data_hora_fim;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            // calcula fatura
            Fatura_Utilidades_Tipo1(IDCliente, IDMedicao, IDGateway, dh_ini, dh_fim);

            // verifica se PDF
            if (tipo_arquivo == 1)
            {
                // tiro caracteres especiais do nome
                Regex regex = new Regex("[^a-zA-Z0-9]");
                string novo_nome = regex.Replace(medicao.Nome, "");

                // nome do arquivo
                string nomeArquivo = string.Format("Fatura_{0}_ID{1:000000}_{2:MMMMyyyy}.pdf", novo_nome, IDMedicao, data_hora_ini);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = "_Fatura_Utilidades_Tipo1_PDF";

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Portrait,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                //string caminhoCompleto = string.Format("D:/Gestal/Projetos/SmartWeb/SmartEnergy/SmartEnergy/Temp/{0}", nomeArquivo);
                //string caminhoCompleto = string.Format("C:/inetpub/wwwroot/GESTAL/SmartEnergy/Temp/{0}", nomeArquivo);
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna nome do arquivo
                var returnedData = new
                {
                    nomeArquivo = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);

                // abaixo original para fazer o download imediato do PDF sem usar o AJAX
                // utilizar a chamda na View:
                // <a href='@Url.Action("Relat_Grafico_PDF", "Relatorios")'><i class="fa fa-file-pdf-o" data-toggle="tooltip" data-placement="left" title="PDF"></i></a>
                // retorna PDF
                //return new Rotativa.PartialViewAsPdf(viewRelatorio) { 
                //    FileName = nomeArquivo,
                //    PageOrientation = Orientation.Portrait,
                //    PageSize = Size.A4,
                //}; 
            }

            // verifica se XLS
            if (tipo_arquivo == 2)
            {
                // gera XLS
                workbook = Fatura_Utilidades_Tipo1_XLS(IDCliente, IDMedicao);

                // gera identificador unico aonde o arquivo sera guardado
                string handle = Guid.NewGuid().ToString();

                using (MemoryStream memoryStream = new MemoryStream())
                {
                    workbook.Write(memoryStream);
                    memoryStream.Position = 0;
                    Session[handle] = memoryStream.ToArray();
                }

                // tiro caracteres especiais do nome
                Regex regex = new Regex("[^a-zA-Z0-9]");
                string novo_nome = regex.Replace(medicao.Nome, "");

                // nome do arquivo
                string nomeArquivo = string.Format("Fatura_{0}_ID{1:000000}_{2:MMMMyyyy}.xls", novo_nome, IDMedicao, data_hora_ini);

                // retorna nome do arquivo
                var returnedData = new
                {
                    FileGuid = handle,
                    FileName = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);

            }

            // verifica se EMAIL
            if (tipo_arquivo == 3)
            {
                // tiro caracteres especiais do nome
                Regex regex = new Regex("[^a-zA-Z0-9]");
                string novo_nome = regex.Replace(medicao.Nome, "");

                // nome do arquivo
                string nomeArquivo = string.Format("Fatura_{0}_ID{1:000000}_{2:MMMMyyyy}.pdf", novo_nome, IDMedicao, data_hora_ini);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = "_Fatura_Utilidades_Tipo1_PDF";

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Portrait,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna 
                ViewBag.caminhoCompleto = caminhoCompleto;
                var returnedData = new
                {
                    caminhoCompleto = caminhoCompleto
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            return View();
        }
        
        // GET: Fatura de Utilidades Print
        public ActionResult Fatura_Utilidades_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // imprime
            return Fatura_Utilidades_Show(IDCliente, IDMedicao, 4);
        }

        // GET: Fatura de Utilidades EMAIL
        public async Task<ActionResult> Fatura_Utilidades_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // gera relatorio
            var retorno = Fatura_Utilidades_Show(IDCliente, IDMedicao, 3);

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(ViewBag.caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "FaturaEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.NomeRelat", ViewBag.NomeRelat);
            message = message.Replace("ViewBag.DataTextoAtualIni", ViewBag.DataTextoAtualIni);
            message = message.Replace("ViewBag.DataTextoAtualFim", ViewBag.DataTextoAtualFim);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.GrupoNome", ViewBag.GrupoNome);
            message = message.Replace("ViewBag.UnidadeNome", ViewBag.UnidadeNome);
            message = message.Replace("ViewBag.MedicaoNome", ViewBag.MedicaoNome);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Fatura de Utilidades PDF
        public ActionResult Fatura_Utilidades_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // relatorio para PDF
            return Fatura_Utilidades_Show(IDCliente, IDMedicao, 1);
        }

        // GET: Fatura de Utilidades XLS
        public ActionResult Fatura_Utilidades_XLS()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // fatura para PDF
            return Fatura_Utilidades_Show(IDCliente, IDMedicao, 2);
        }

        // GET: Fatura de Utilidades XLS Download
        [HttpGet]
        public virtual ActionResult Fatura_Utilidades_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Fatura Utilidades Atualizar
        public PartialViewResult _Fatura_Utilidades_Atualizar(int Navegacao, string DataIni, string DataFim)
        {
            // caso forcar data
            if (Navegacao == 100)
            {
                // pega data
                DateTime dateValueIni = DateTime.Parse(DataIni);
                DateTime dateValueFim = DateTime.Parse(DataFim);

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Fatura_DataIni", dateValueIni);
                CookieStore.SalvaCookie_Datahora("Fatura_DataFim", dateValueFim);
            }

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;
            int IDGateway = ViewBag._IDGateway;

            // configuracao da medicao
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le tarifasutilgrupos
            TarifasUtilGruposMetodos listaTarifasUtilGruposMetodos = new TarifasUtilGruposMetodos();
            List<TarifasUtilGruposDominio> listaTarifasUtilGrupos = listaTarifasUtilGruposMetodos.ListarPorIDCliente(IDCliente);
            TarifasUtilGruposDominio tipoTarifasUtilGrupos = listaTarifasUtilGrupos.Find(item => item.IDGrupoTarifas == medicao.IDAgenteDistribuidora);
            if (tipoTarifasUtilGrupos != null)
            {
                ViewBag.TipoTarifasUtilGrupos = tipoTarifasUtilGrupos.Nome;
            }
            else
            {
                ViewBag.TipoTarifasUtilGrupos = "";
            }

            // le cookie datahora
            DateTime datahora_cookie_ini = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Fatura_DataFim");
            DATAHORA dh_ini = new DATAHORA();
            DATAHORA dh_fim = new DATAHORA();

            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -3:    // mes anterior

                    datahora_cookie_ini = datahora_cookie_ini.AddMonths(-1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(-1);
                    break;

                case 3:    // mes seguinte

                    datahora_cookie_ini = datahora_cookie_ini.AddMonths(1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(1);
                    break;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", datahora_cookie_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", datahora_cookie_fim);

            // data e hora atual
            ViewBag.DataIni = string.Format("{0:d}", datahora_cookie_ini);
            ViewBag.HoraIni = string.Format("{0:HH:mm}", datahora_cookie_ini);
            ViewBag.DataFim = string.Format("{0:d}", datahora_cookie_fim);
            ViewBag.HoraFim = string.Format("{0:HH:mm}", datahora_cookie_fim);

            ViewBag.DataTextoAtualIni = string.Format("{0:d} {1:HH:mm:ss}", datahora_cookie_ini, datahora_cookie_ini);
            ViewBag.DataTextoAtualFim = string.Format("{0:d} {1:HH:mm:ss}", datahora_cookie_fim, datahora_cookie_fim);

            ViewBag.DataAtualN = datahora_cookie_ini;
            ViewBag.DataFinalN = datahora_cookie_fim;

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, datahora_cookie_ini);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, datahora_cookie_fim);

            // calcula fatura
            Fatura_Utilidades_Tipo1(IDCliente, IDMedicao, IDGateway, dh_ini, dh_fim);

            return PartialView();
        }
    }
}