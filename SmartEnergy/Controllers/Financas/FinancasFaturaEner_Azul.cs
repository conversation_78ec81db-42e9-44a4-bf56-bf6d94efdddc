﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class FinancasController
    {

        private void Fatura_Energia_Azul_Formata(FaturasDominio fatura, FaturasDominio faturaAnt1, FaturasDominio faturaAnt2)
        {
            // calcula custo medio
            double CustoMedio = (fatura.consumo_total_te[CF.REG] == 0.0) ? 0.0 : (fatura.total / fatura.consumo_total_te[CF.REG]) * 1000.0;

            // grafico pizza
            double Total_Demanda = fatura.demanda_p[CF.TOT] + fatura.demanda_fpi[CF.TOT] + fatura.demanda_fpc[CF.TOT] +
                                   fatura.demanda_pe_p[CF.TOT] + fatura.demanda_pe_fpi[CF.TOT] + fatura.demanda_pe_fpc[CF.TOT];

            double Total_Consumo = fatura.consumo_p_tusd[CF.TOT] + fatura.consumo_fpi_tusd[CF.TOT] + fatura.consumo_fpc_tusd[CF.TOT] +
                                   fatura.consumo_p_te[CF.TOT] + fatura.consumo_fpi_te[CF.TOT] + fatura.consumo_fpc_te[CF.TOT] +
                                   fatura.consumo_bandeira_p_ini[CF.TOT] + fatura.consumo_bandeira_fp_ini[CF.TOT] +
                                   fatura.consumo_bandeira_p_fim[CF.TOT] + fatura.consumo_bandeira_fp_fim[CF.TOT];

            double Total_Imposto = fatura.piscofins[CF.TOT] + fatura.icms[CF.TOT];

            double Total_Multa = fatura.ultr_demanda_p[CF.TOT] + fatura.ultr_demanda_fp[CF.TOT] +
                                   fatura.ultr_demanda_pe_p[CF.TOT] + fatura.ultr_demanda_pe_fp[CF.TOT] +
                                   fatura.fdr_p[CF.TOT] + fatura.fdr_fp[CF.TOT] +
                                   fatura.fdr_pe_p[CF.TOT] + fatura.fdr_pe_fp[CF.TOT] +
                                   fatura.fer_p[CF.TOT] + fatura.fer_fpi[CF.TOT] + fatura.fer_fpc[CF.TOT];

            double Total = Total_Demanda + Total_Consumo + Total_Imposto + Total_Multa;


            // verifica se Livre
            if (fatura.IDContratoMedicao == TIPOCONTRATO.LIVRE)
            {
                // grafico pizza
                Total_Demanda = fatura.demanda_p[CF.TOT] + fatura.demanda_fpi[CF.TOT] + fatura.demanda_fpc[CF.TOT] +
                                fatura.demanda_pe_p[CF.TOT] + fatura.demanda_pe_fpi[CF.TOT] + fatura.demanda_pe_fpc[CF.TOT];

                Total_Consumo = fatura.consumo_p_tusd[CF.TOT] + fatura.consumo_fpi_tusd[CF.TOT] + fatura.consumo_fpc_tusd[CF.TOT] +
                                fatura.consumo_p_te[CF.TOT] + fatura.consumo_fpi_te[CF.TOT] + fatura.consumo_fpc_te[CF.TOT] +
                                fatura.consumo_livre_p[CF.TOT] + fatura.consumo_livre_fp[CF.TOT];

                Total_Imposto = fatura.encargos_conexao + fatura.piscofins[CF.TOT] + fatura.icms[CF.TOT];

                Total_Multa = fatura.ultr_demanda_p[CF.TOT] + fatura.ultr_demanda_fp[CF.TOT] +
                              fatura.ultr_demanda_pe_p[CF.TOT] + fatura.ultr_demanda_pe_fp[CF.TOT] +
                              fatura.fdr_p[CF.TOT] + fatura.fdr_fp[CF.TOT] +
                              fatura.fdr_pe_p[CF.TOT] + fatura.fdr_pe_fp[CF.TOT] +
                              fatura.fer_p[CF.TOT] + fatura.fer_fpi[CF.TOT] + fatura.fer_fpc[CF.TOT];

                Total = Total_Demanda + Total_Consumo + Total_Imposto + Total_Multa;
            }


            ViewBag.Total_Demanda = Total_Demanda;
            ViewBag.Total_Consumo = Total_Consumo;
            ViewBag.Total_Imposto = Total_Imposto;
            ViewBag.Total_Multa = Total_Multa;

            ViewBag.Porc_Demanda = (Total <= 0.0) ? 0.0 : (Total_Demanda / Total) * 100.0;
            ViewBag.Porc_Consumo = (Total <= 0.0) ? 0.0 : (Total_Consumo / Total) * 100.0;
            ViewBag.Porc_Imposto = (Total <= 0.0) ? 0.0 : (Total_Imposto / Total) * 100.0;
            ViewBag.Porc_Multa = (Total <= 0.0) ? 0.0 : (Total_Multa / Total) * 100.0;

            // tabela totais
            ViewBag.Fatura_Atual_Data = Funcoes_SmartEnergy.StringMesFechamento(fatura.DataHoraFim);
            ViewBag.Fatura_Atual_Bandeira_ini = Funcoes_SmartEnergy.BandeiraCor(fatura.bandeira_ini);
            ViewBag.Fatura_Atual_BandeiraTexto_ini = Funcoes_SmartEnergy.BandeiraTexto(fatura.bandeira_ini);

            // verifica se tem bandeira
            if (fatura.data_bandeira_fim_i.Year != 2000)
            {
                ViewBag.Fatura_Atual_Bandeira_fim = Funcoes_SmartEnergy.BandeiraCor(fatura.bandeira_fim);
                ViewBag.Fatura_Atual_BandeiraTexto_fim = Funcoes_SmartEnergy.BandeiraTexto(fatura.bandeira_fim);
            }
            else
            {
                ViewBag.Fatura_Atual_BandeiraTexto_fim = "false";
            }

            ViewBag.Fatura_Atual_ValorTotal = string.Format("{0:C}", fatura.total);
            ViewBag.Fatura_Atual_ValorTotalN = fatura.total;

            ViewBag.Fatura_Atual_CustoMedio = string.Format("{0:C} / MWh", CustoMedio);
            ViewBag.Fatura_Atual_CustoMedioN = CustoMedio;

            // tabela valores

            //
            // DEMANDA
            //

            // numero de dias - periodo normal
            ViewBag.num_dias = fatura.num_dias;

            // demanda ponta
            var demanda_p = new string[4];
            demanda_p[CF.REG] = string.Format("{0:#,##0.0}", fatura.demanda_p[CF.REG]);
            demanda_p[CF.FAT] = string.Format("{0:#,##0.0}", fatura.demanda_p[CF.FAT]);
            demanda_p[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.demanda_p[CF.TAR]);
            demanda_p[CF.TOT] = string.Format("{0:C}", fatura.demanda_p[CF.TOT]);
            ViewBag.demanda_p = demanda_p;

            // demanda fora de ponta
            var demanda_fpi = new string[4];
            demanda_fpi[CF.REG] = string.Format("{0:#,##0.0}", fatura.demanda_fpi[CF.REG]);
            demanda_fpi[CF.FAT] = string.Format("{0:#,##0.0}", fatura.demanda_fpi[CF.FAT]);
            demanda_fpi[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.demanda_fpi[CF.TAR]);
            demanda_fpi[CF.TOT] = string.Format("{0:C}", fatura.demanda_fpi[CF.TOT]);

            var demanda_fpc = new string[4];
            demanda_fpc[CF.REG] = string.Format("{0:#,##0.0}", fatura.demanda_fpc[CF.REG]);
            demanda_fpc[CF.FAT] = string.Format("{0:#,##0.0}", fatura.demanda_fpc[CF.FAT]);
            demanda_fpc[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.demanda_fpc[CF.TAR]);
            demanda_fpc[CF.TOT] = string.Format("{0:C}", fatura.demanda_fpc[CF.TOT]);

            // verifica qual deve apresentar
            if (fatura.demanda_fpi[CF.REG] >= fatura.demanda_fpc[CF.REG])
            {
                ViewBag.demanda_fp = demanda_fpi;
            }
            else
            {
                ViewBag.demanda_fp = demanda_fpc;
            }

            // ultrapassagem demanda ponta
            var ultr_demanda_p = new string[4];
            if (fatura.ultr_demanda_p[CF.FAT] == 0.0)
            {
                ultr_demanda_p[CF.REG] = "0";
                ultr_demanda_p[CF.FAT] = "0";
                ultr_demanda_p[CF.TAR] = "0";
                ultr_demanda_p[CF.TOT] = "0";
            }
            else
            {
                ultr_demanda_p[CF.REG] = string.Format("{0:#,##0.0}", fatura.ultr_demanda_p[CF.REG]);
                ultr_demanda_p[CF.FAT] = string.Format("{0:#,##0.0}", fatura.ultr_demanda_p[CF.FAT]);
                ultr_demanda_p[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.ultr_demanda_p[CF.TAR]);
                ultr_demanda_p[CF.TOT] = string.Format("{0:C}", fatura.ultr_demanda_p[CF.TOT]);
            }
            ViewBag.ultr_demanda_p = ultr_demanda_p;

            // ultrapassagem demanda fora de ponta
            var ultr_demanda_fp = new string[4];
            if (fatura.ultr_demanda_fp[CF.FAT] == 0.0)
            {
                ultr_demanda_fp[CF.REG] = "0";
                ultr_demanda_fp[CF.FAT] = "0";
                ultr_demanda_fp[CF.TAR] = "0";
                ultr_demanda_fp[CF.TOT] = "0";
            }
            else
            {
                ultr_demanda_fp[CF.REG] = string.Format("{0:#,##0.0}", fatura.ultr_demanda_fp[CF.REG]);
                ultr_demanda_fp[CF.FAT] = string.Format("{0:#,##0.0}", fatura.ultr_demanda_fp[CF.FAT]);
                ultr_demanda_fp[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.ultr_demanda_fp[CF.TAR]);
                ultr_demanda_fp[CF.TOT] = string.Format("{0:C}", fatura.ultr_demanda_fp[CF.TOT]);
            }
            ViewBag.ultr_demanda_fp = ultr_demanda_fp;

            // constante demanda 
            string demanda_k;
            demanda_k = string.Format("{0:0.00000}", fatura.demanda_k);
            ViewBag.demanda_k = demanda_k;

            // contrato demanda ponta
            string contrato_dem_p;
            contrato_dem_p = string.Format("{0:#,##0.0}", fatura.contrato_dem_p);
            ViewBag.contrato_dem_p = contrato_dem_p;

            // contrato demanda fora de ponta
            string contrato_dem_fp;
            contrato_dem_fp = string.Format("{0:#,##0.0}", fatura.contrato_dem_fp);
            ViewBag.contrato_dem_fp = contrato_dem_fp;

            // fator de carga ponta
            string fatcar_p;
            fatcar_p = string.Format("{0:0.00}", fatura.fatcar_p[CF.REG]);
            ViewBag.fatcar_p = fatcar_p;

            // fator de carga fora de ponta
            string fatcar_fp;
            fatcar_fp = string.Format("{0:0.00}", fatura.fatcar_fp[CF.REG]);
            ViewBag.fatcar_fp = fatcar_fp;

            //
            // DEMANDA PERIODO EXCEDENTE
            //

            // numero de dias - periodo excedente
            ViewBag.num_dias_pe = fatura.num_dias_pe;

            // demanda ponta
            var demanda_pe_p = new string[4];
            demanda_pe_p[CF.REG] = string.Format("{0:#,##0.0}", fatura.demanda_pe_p[CF.REG]);
            demanda_pe_p[CF.FAT] = string.Format("{0:#,##0.0}", fatura.demanda_pe_p[CF.FAT]);
            demanda_pe_p[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.demanda_pe_p[CF.TAR]);
            demanda_pe_p[CF.TOT] = string.Format("{0:C}", fatura.demanda_pe_p[CF.TOT]);
            ViewBag.demanda_pe_p = demanda_pe_p;

            // demanda fora de ponta
            var demanda_pe_fpi = new string[4];
            demanda_pe_fpi[CF.REG] = string.Format("{0:#,##0.0}", fatura.demanda_pe_fpi[CF.REG]);
            demanda_pe_fpi[CF.FAT] = string.Format("{0:#,##0.0}", fatura.demanda_pe_fpi[CF.FAT]);
            demanda_pe_fpi[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.demanda_pe_fpi[CF.TAR]);
            demanda_pe_fpi[CF.TOT] = string.Format("{0:C}", fatura.demanda_pe_fpi[CF.TOT]);

            var demanda_pe_fpc = new string[4];
            demanda_pe_fpc[CF.REG] = string.Format("{0:#,##0.0}", fatura.demanda_pe_fpc[CF.REG]);
            demanda_pe_fpc[CF.FAT] = string.Format("{0:#,##0.0}", fatura.demanda_pe_fpc[CF.FAT]);
            demanda_pe_fpc[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.demanda_pe_fpc[CF.TAR]);
            demanda_pe_fpc[CF.TOT] = string.Format("{0:C}", fatura.demanda_pe_fpc[CF.TOT]);

            // verifica qual deve apresentar
            if (fatura.demanda_pe_fpi[CF.REG] >= fatura.demanda_pe_fpc[CF.REG])
            {
                ViewBag.demanda_pe_fp = demanda_pe_fpi;
            }
            else
            {
                ViewBag.demanda_pe_fp = demanda_pe_fpc;
            }

            // ultrapassagem demanda ponta
            var ultr_demanda_pe_p = new string[4];
            if (fatura.ultr_demanda_pe_p[CF.FAT] == 0.0)
            {
                ultr_demanda_pe_p[CF.REG] = "0";
                ultr_demanda_pe_p[CF.FAT] = "0";
                ultr_demanda_pe_p[CF.TAR] = "0";
                ultr_demanda_pe_p[CF.TOT] = "0";
            }
            else
            {
                ultr_demanda_pe_p[CF.REG] = string.Format("{0:#,##0.0}", fatura.ultr_demanda_pe_p[CF.REG]);
                ultr_demanda_pe_p[CF.FAT] = string.Format("{0:#,##0.0}", fatura.ultr_demanda_pe_p[CF.FAT]);
                ultr_demanda_pe_p[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.ultr_demanda_pe_p[CF.TAR]);
                ultr_demanda_pe_p[CF.TOT] = string.Format("{0:C}", fatura.ultr_demanda_pe_p[CF.TOT]);
            }
            ViewBag.ultr_demanda_pe_p = ultr_demanda_pe_p;

            // ultrapassagem demanda fora de ponta
            var ultr_demanda_pe_fp = new string[4];
            if (fatura.ultr_demanda_pe_fp[CF.FAT] == 0.0)
            {
                ultr_demanda_pe_fp[CF.REG] = "0";
                ultr_demanda_pe_fp[CF.FAT] = "0";
                ultr_demanda_pe_fp[CF.TAR] = "0";
                ultr_demanda_pe_fp[CF.TOT] = "0";
            }
            else
            {
                ultr_demanda_pe_fp[CF.REG] = string.Format("{0:#,##0.0}", fatura.ultr_demanda_pe_fp[CF.REG]);
                ultr_demanda_pe_fp[CF.FAT] = string.Format("{0:#,##0.0}", fatura.ultr_demanda_pe_fp[CF.FAT]);
                ultr_demanda_pe_fp[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.ultr_demanda_pe_fp[CF.TAR]);
                ultr_demanda_pe_fp[CF.TOT] = string.Format("{0:C}", fatura.ultr_demanda_pe_fp[CF.TOT]);
            }
            ViewBag.ultr_demanda_pe_fp = ultr_demanda_pe_fp;

            // contrato demanda ponta
            string contrato_dem_pe_p;
            contrato_dem_pe_p = string.Format("{0:#,##0.0}", fatura.contrato_dem_pe_p);
            ViewBag.contrato_dem_pe_p = contrato_dem_pe_p;

            // contrato demanda fora de ponta
            string contrato_dem_pe_fp;
            contrato_dem_pe_fp = string.Format("{0:#,##0.0}", fatura.contrato_dem_pe_fp);
            ViewBag.contrato_dem_pe_fp = contrato_dem_pe_fp;

            //
            // CONSUMO TUSD
            //

            // flag
            ViewBag.flag_tusd_te = fatura.flag_tusd_te;

            // consumo ponta
            var consumo_p_tusd = new string[4];
            consumo_p_tusd[CF.REG] = string.Format("{0:#,##0}", fatura.consumo_p_tusd[CF.REG]);
            consumo_p_tusd[CF.FAT] = string.Format("{0:#,##0}", fatura.consumo_p_tusd[CF.FAT]);
            consumo_p_tusd[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.consumo_p_tusd[CF.TAR] / 1000.0);
            consumo_p_tusd[CF.TOT] = string.Format("{0:C}", fatura.consumo_p_tusd[CF.TOT]);
            ViewBag.consumo_p_tusd = consumo_p_tusd;

            // consumo fora de ponta
            var consumo_fp_tusd = new string[4];
            consumo_fp_tusd[CF.REG] = string.Format("{0:#,##0}", fatura.consumo_fpi_tusd[CF.REG] + fatura.consumo_fpc_tusd[CF.REG]);
            consumo_fp_tusd[CF.FAT] = string.Format("{0:#,##0}", fatura.consumo_fpi_tusd[CF.FAT] + fatura.consumo_fpc_tusd[CF.FAT]);
            consumo_fp_tusd[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.consumo_fpi_tusd[CF.TAR] / 1000.0);
            consumo_fp_tusd[CF.TOT] = string.Format("{0:C}", fatura.consumo_fpi_tusd[CF.TOT] + fatura.consumo_fpc_tusd[CF.TOT]);
            ViewBag.consumo_fp_tusd = consumo_fp_tusd;

            // consumo reservado
            var consumo_r_tusd = new string[4];
            consumo_r_tusd[CF.REG] = string.Format("{0:#,##0}", fatura.consumo_r_tusd[CF.REG]);
            consumo_r_tusd[CF.FAT] = string.Format("{0:#,##0}", fatura.consumo_r_tusd[CF.FAT]);
            consumo_r_tusd[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.consumo_r_tusd[CF.TAR] / 1000.0);
            consumo_r_tusd[CF.TOT] = string.Format("{0:C}", fatura.consumo_r_tusd[CF.TOT]);
            ViewBag.consumo_r_tusd = consumo_r_tusd;

            // consumo total
            string consumo_total_tusd;
            consumo_total_tusd = string.Format("{0:#,##0}", fatura.consumo_total_tusd[CF.REG]);
            ViewBag.consumo_total_tusd = consumo_total_tusd;

            //
            // CONSUMO TE
            //

            // consumo ponta
            var consumo_p_te = new string[4];
            consumo_p_te[CF.REG] = string.Format("{0:#,##0}", fatura.consumo_p_te[CF.REG]);
            consumo_p_te[CF.FAT] = string.Format("{0:#,##0}", fatura.consumo_p_te[CF.FAT]);
            consumo_p_te[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.consumo_p_te[CF.TAR] / 1000.0);
            consumo_p_te[CF.TOT] = string.Format("{0:C}", fatura.consumo_p_te[CF.TOT]);
            ViewBag.consumo_p_te = consumo_p_te;

            // consumo fora de ponta
            var consumo_fp_te = new string[4];
            consumo_fp_te[CF.REG] = string.Format("{0:#,##0}", fatura.consumo_fpi_te[CF.REG] + fatura.consumo_fpc_te[CF.REG]);
            consumo_fp_te[CF.FAT] = string.Format("{0:#,##0}", fatura.consumo_fpi_te[CF.FAT] + fatura.consumo_fpc_te[CF.FAT]);
            consumo_fp_te[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.consumo_fpi_te[CF.TAR] / 1000.0);
            consumo_fp_te[CF.TOT] = string.Format("{0:C}", fatura.consumo_fpi_te[CF.TOT] + fatura.consumo_fpc_te[CF.TOT]);
            ViewBag.consumo_fp_te = consumo_fp_te;

            // consumo reservado
            var consumo_r_te = new string[4];
            consumo_r_te[CF.REG] = string.Format("{0:#,##0}", fatura.consumo_r_te[CF.REG]);
            consumo_r_te[CF.FAT] = string.Format("{0:#,##0}", fatura.consumo_r_te[CF.FAT]);
            consumo_r_te[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.consumo_r_te[CF.TAR] / 1000.0);
            consumo_r_te[CF.TOT] = string.Format("{0:C}", fatura.consumo_r_te[CF.TOT]);
            ViewBag.consumo_r_te = consumo_r_te;

            // consumo total
            string consumo_total_te;
            consumo_total_te = string.Format("{0:#,##0}", fatura.consumo_total_te[CF.REG]);
            ViewBag.consumo_total_te = consumo_total_te;

            //
            // BANDEIRA TARIFARIA
            //

            // bandeira ini
            string bandeira_ini;
            string bandeira_ini_i;
            string bandeira_ini_f;

            // bandeira fim
            string bandeira_fim;
            string bandeira_fim_i;
            string bandeira_fim_f;

            // se existem bandeiras
            if ((fatura.bandeira_ini >= 0) && (fatura.bandeira_fim >= 0))
            {
                // verifica se bandeira inicio igual a bandeira fim
                if (fatura.bandeira_ini == fatura.bandeira_fim)
                {
                    bandeira_ini_i = string.Format("{0:d}", fatura.data_bandeira_ini_i);
                    bandeira_fim_f = string.Format("{0:d}", fatura.data_bandeira_fim_f);

                    bandeira_ini = string.Format("Bandeira {0} ({1} - {2})", Funcoes_SmartEnergy.BandeiraTexto(fatura.bandeira_ini), bandeira_ini_i, bandeira_fim_f);
                    ViewBag.bandeira_ini = bandeira_ini;

                    // consumo bandeira ponta ini
                    var consumo_bandeira_p_ini = new string[4];
                    consumo_bandeira_p_ini[CF.REG] = string.Format("{0:#,##0}", fatura.consumo_bandeira_p_ini[CF.REG] + fatura.consumo_bandeira_p_fim[CF.REG]);
                    consumo_bandeira_p_ini[CF.FAT] = string.Format("{0:#,##0}", fatura.consumo_bandeira_p_ini[CF.FAT] + fatura.consumo_bandeira_p_fim[CF.FAT]);
                    consumo_bandeira_p_ini[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.consumo_bandeira_p_ini[CF.TAR]);
                    consumo_bandeira_p_ini[CF.TOT] = string.Format("{0:C}", fatura.consumo_bandeira_p_ini[CF.TOT] + fatura.consumo_bandeira_p_fim[CF.TOT]);
                    ViewBag.consumo_bandeira_p_ini = consumo_bandeira_p_ini;

                    // consumo bandeira fora de ponta ini
                    var consumo_bandeira_fp_ini = new string[4];
                    consumo_bandeira_fp_ini[CF.REG] = string.Format("{0:#,##0}", fatura.consumo_bandeira_fp_ini[CF.REG] + fatura.consumo_bandeira_fp_fim[CF.REG]);
                    consumo_bandeira_fp_ini[CF.FAT] = string.Format("{0:#,##0}", fatura.consumo_bandeira_fp_ini[CF.FAT] + fatura.consumo_bandeira_fp_fim[CF.FAT]);
                    consumo_bandeira_fp_ini[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.consumo_bandeira_fp_ini[CF.TAR]);
                    consumo_bandeira_fp_ini[CF.TOT] = string.Format("{0:C}", fatura.consumo_bandeira_fp_ini[CF.TOT] + fatura.consumo_bandeira_fp_fim[CF.TOT]);
                    ViewBag.consumo_bandeira_fp_ini = consumo_bandeira_fp_ini;

                    // consumo bandeira reservado ini
                    var consumo_bandeira_r_ini = new string[4];
                    consumo_bandeira_r_ini[CF.REG] = string.Format("{0:#,##0}", fatura.consumo_bandeira_r_ini[CF.REG] + fatura.consumo_bandeira_r_fim[CF.REG]);
                    consumo_bandeira_r_ini[CF.FAT] = string.Format("{0:#,##0}", fatura.consumo_bandeira_r_ini[CF.FAT] + fatura.consumo_bandeira_r_ini[CF.FAT]);
                    consumo_bandeira_r_ini[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.consumo_bandeira_r_ini[CF.TAR]);
                    consumo_bandeira_r_ini[CF.TOT] = string.Format("{0:C}", fatura.consumo_bandeira_r_ini[CF.TOT] + fatura.consumo_bandeira_r_fim[CF.TOT]);
                    ViewBag.consumo_bandeira_r_ini = consumo_bandeira_r_ini;
                }
                else
                {
                    // apresenta bandeira inicio

                    bandeira_ini_i = string.Format("{0:d}", fatura.data_bandeira_ini_i);
                    bandeira_ini_f = string.Format("{0:d}", fatura.data_bandeira_ini_f);

                    bandeira_ini = string.Format("Bandeira {0} ({1} - {2})", Funcoes_SmartEnergy.BandeiraTexto(fatura.bandeira_ini), bandeira_ini_i, bandeira_ini_f);
                    ViewBag.bandeira_ini = bandeira_ini;

                    // consumo bandeira ponta ini
                    var consumo_bandeira_p_ini = new string[4];
                    consumo_bandeira_p_ini[CF.REG] = string.Format("{0:#,##0}", fatura.consumo_bandeira_p_ini[CF.REG]);
                    consumo_bandeira_p_ini[CF.FAT] = string.Format("{0:#,##0}", fatura.consumo_bandeira_p_ini[CF.FAT]);
                    consumo_bandeira_p_ini[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.consumo_bandeira_p_ini[CF.TAR]);
                    consumo_bandeira_p_ini[CF.TOT] = string.Format("{0:C}", fatura.consumo_bandeira_p_ini[CF.TOT]);
                    ViewBag.consumo_bandeira_p_ini = consumo_bandeira_p_ini;

                    // consumo bandeira fora de ponta ini
                    var consumo_bandeira_fp_ini = new string[4];
                    consumo_bandeira_fp_ini[CF.REG] = string.Format("{0:#,##0}", fatura.consumo_bandeira_fp_ini[CF.REG]);
                    consumo_bandeira_fp_ini[CF.FAT] = string.Format("{0:#,##0}", fatura.consumo_bandeira_fp_ini[CF.FAT]);
                    consumo_bandeira_fp_ini[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.consumo_bandeira_fp_ini[CF.TAR]);
                    consumo_bandeira_fp_ini[CF.TOT] = string.Format("{0:C}", fatura.consumo_bandeira_fp_ini[CF.TOT]);
                    ViewBag.consumo_bandeira_fp_ini = consumo_bandeira_fp_ini;

                    // consumo bandeira reservado ini
                    var consumo_bandeira_r_ini = new string[4];
                    consumo_bandeira_r_ini[CF.REG] = string.Format("{0:#,##0}", fatura.consumo_bandeira_r_ini[CF.REG]);
                    consumo_bandeira_r_ini[CF.FAT] = string.Format("{0:#,##0}", fatura.consumo_bandeira_r_ini[CF.FAT]);
                    consumo_bandeira_r_ini[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.consumo_bandeira_r_ini[CF.TAR]);
                    consumo_bandeira_r_ini[CF.TOT] = string.Format("{0:C}", fatura.consumo_bandeira_r_ini[CF.TOT]);
                    ViewBag.consumo_bandeira_r_ini = consumo_bandeira_r_ini;
                }


                // verifica se tem bandeira 2
                if (fatura.bandeira_ini != fatura.bandeira_fim)
                {
                    bandeira_fim_i = string.Format("{0:d}", fatura.data_bandeira_fim_i);
                    bandeira_fim_f = string.Format("{0:d}", fatura.data_bandeira_fim_f);

                    bandeira_fim = string.Format("Bandeira {0} ({1} - {2})", Funcoes_SmartEnergy.BandeiraTexto(fatura.bandeira_fim), bandeira_fim_i, bandeira_fim_f);
                    ViewBag.bandeira_fim = bandeira_fim;

                    // consumo bandeira ponta fim
                    var consumo_bandeira_p_fim = new string[4];
                    consumo_bandeira_p_fim[CF.REG] = string.Format("{0:#,##0}", fatura.consumo_bandeira_p_fim[CF.REG]);
                    consumo_bandeira_p_fim[CF.FAT] = string.Format("{0:#,##0}", fatura.consumo_bandeira_p_fim[CF.FAT]);
                    consumo_bandeira_p_fim[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.consumo_bandeira_p_fim[CF.TAR]);
                    consumo_bandeira_p_fim[CF.TOT] = string.Format("{0:C}", fatura.consumo_bandeira_p_fim[CF.TOT]);
                    ViewBag.consumo_bandeira_p_fim = consumo_bandeira_p_fim;

                    // consumo bandeira fora de ponta fim
                    var consumo_bandeira_fp_fim = new string[4];
                    consumo_bandeira_fp_fim[CF.REG] = string.Format("{0:#,##0}", fatura.consumo_bandeira_fp_fim[CF.REG]);
                    consumo_bandeira_fp_fim[CF.FAT] = string.Format("{0:#,##0}", fatura.consumo_bandeira_fp_fim[CF.FAT]);
                    consumo_bandeira_fp_fim[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.consumo_bandeira_fp_fim[CF.TAR]);
                    consumo_bandeira_fp_fim[CF.TOT] = string.Format("{0:C}", fatura.consumo_bandeira_fp_fim[CF.TOT]);
                    ViewBag.consumo_bandeira_fp_fim = consumo_bandeira_fp_fim;

                    // consumo bandeira reservado fim
                    var consumo_bandeira_r_fim = new string[4];
                    consumo_bandeira_r_fim[CF.REG] = string.Format("{0:#,##0}", fatura.consumo_bandeira_r_fim[CF.REG]);
                    consumo_bandeira_r_fim[CF.FAT] = string.Format("{0:#,##0}", fatura.consumo_bandeira_r_fim[CF.FAT]);
                    consumo_bandeira_r_fim[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.consumo_bandeira_r_fim[CF.TAR]);
                    consumo_bandeira_r_fim[CF.TOT] = string.Format("{0:C}", fatura.consumo_bandeira_r_fim[CF.TOT]);
                    ViewBag.consumo_bandeira_r_fim = consumo_bandeira_r_fim;
                }
                else
                {
                    bandeira_fim = "false";
                    ViewBag.bandeira_fim = bandeira_fim;
                }
            }
            else
            {
                bandeira_ini = "false";
                ViewBag.bandeira_ini = bandeira_ini;

                bandeira_fim = "false";
                ViewBag.bandeira_fim = bandeira_fim;
            }

            //
            // FDR
            //

            // FDR ponta
            var fdr_p = new string[4];
            fdr_p[CF.REG] = string.Format("{0:#,##0.0}", fatura.fdr_p[CF.REG]);
            fdr_p[CF.FAT] = string.Format("{0:#,##0.0}", fatura.fdr_p[CF.FAT]);
            fdr_p[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.fdr_p[CF.TAR]);
            fdr_p[CF.TOT] = string.Format("{0:C}", fatura.fdr_p[CF.TOT]);
            ViewBag.fdr_p = fdr_p;

            // FDR fora de ponta
            var fdr_fp = new string[4];
            fdr_fp[CF.REG] = string.Format("{0:#,##0.0}", fatura.fdr_fp[CF.REG]);
            fdr_fp[CF.FAT] = string.Format("{0:#,##0.0}", fatura.fdr_fp[CF.FAT]);
            fdr_fp[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.fdr_fp[CF.TAR]);
            fdr_fp[CF.TOT] = string.Format("{0:C}", fatura.fdr_fp[CF.TOT]);
            ViewBag.fdr_fp = fdr_fp;

            //
            // FDR PERIODO EXCEDENTE
            //

            // FDR ponta
            var fdr_pe_p = new string[4];
            fdr_pe_p[CF.REG] = string.Format("{0:#,##0.0}", fatura.fdr_pe_p[CF.REG]);
            fdr_pe_p[CF.FAT] = string.Format("{0:#,##0.0}", fatura.fdr_pe_p[CF.FAT]);
            fdr_pe_p[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.fdr_pe_p[CF.TAR]);
            fdr_pe_p[CF.TOT] = string.Format("{0:C}", fatura.fdr_pe_p[CF.TOT]);
            ViewBag.fdr_pe_p = fdr_pe_p;

            // FDR fora de ponta
            var fdr_pe_fp = new string[4];
            fdr_pe_fp[CF.REG] = string.Format("{0:#,##0.0}", fatura.fdr_pe_fp[CF.REG]);
            fdr_pe_fp[CF.FAT] = string.Format("{0:#,##0.0}", fatura.fdr_pe_fp[CF.FAT]);
            fdr_pe_fp[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.fdr_pe_fp[CF.TAR]);
            fdr_pe_fp[CF.TOT] = string.Format("{0:C}", fatura.fdr_pe_fp[CF.TOT]);
            ViewBag.fdr_pe_fp = fdr_pe_fp;

            //
            // FER
            //

            // FER ponta
            var fer_p = new string[4];
            fer_p[CF.REG] = string.Format("{0:#,##0}", fatura.fer_p[CF.REG]);
            fer_p[CF.FAT] = string.Format("{0:#,##0}", fatura.fer_p[CF.FAT]);
            fer_p[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.fer_p[CF.TAR] / 1000.0);
            fer_p[CF.TOT] = string.Format("{0:C}", fatura.fer_p[CF.TOT]);
            ViewBag.fer_p = fer_p;

            // FER fora de ponta indutivo
            var fer_fpi = new string[4];
            fer_fpi[CF.REG] = string.Format("{0:#,##0}", fatura.fer_fpi[CF.REG]);
            fer_fpi[CF.FAT] = string.Format("{0:#,##0}", fatura.fer_fpi[CF.FAT]);
            fer_fpi[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.fer_fpi[CF.TAR] / 1000.0);
            fer_fpi[CF.TOT] = string.Format("{0:C}", fatura.fer_fpi[CF.TOT]);
            ViewBag.fer_fpi = fer_fpi;

            // FER fora de ponta capacitivo
            var fer_fpc = new string[4];
            fer_fpc[CF.REG] = string.Format("{0:#,##0}", fatura.fer_fpc[CF.REG]);
            fer_fpc[CF.FAT] = string.Format("{0:#,##0}", fatura.fer_fpc[CF.FAT]);
            fer_fpc[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.fer_fpc[CF.TAR] / 1000.0);
            fer_fpc[CF.TOT] = string.Format("{0:C}", fatura.fer_fpc[CF.TOT]);
            ViewBag.fer_fpc = fer_fpc;

            // FER reservado
            var fer_r = new string[4];
            fer_r[CF.REG] = string.Format("{0:#,##0}", fatura.fer_r[CF.REG]);
            fer_r[CF.FAT] = string.Format("{0:#,##0}", fatura.fer_r[CF.FAT]);
            fer_r[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.fer_r[CF.TAR] / 1000.0);
            fer_r[CF.TOT] = string.Format("{0:C}", fatura.fer_r[CF.TOT]);
            ViewBag.fer_r = fer_r;

            //
            // ENERGIA ACL
            //

            // energia acl na ponta
            var energia_acl_p = new string[4];
            energia_acl_p[CF.REG] = string.Format("{0:#,##0}", fatura.energia_acl_p[CF.REG]);
            energia_acl_p[CF.FAT] = string.Format("{0:#,##0}", fatura.energia_acl_p[CF.FAT]);
            energia_acl_p[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.energia_acl_p[CF.TAR] / 1000.0);
            energia_acl_p[CF.TOT] = string.Format("{0:C}", fatura.energia_acl_p[CF.TOT]);
            ViewBag.energia_acl_p = energia_acl_p;

            // energia acl fora de ponta
            var energia_acl_fp = new string[4];
            energia_acl_fp[CF.REG] = string.Format("{0:#,##0}", fatura.energia_acl_fp[CF.REG]);
            energia_acl_fp[CF.FAT] = string.Format("{0:#,##0}", fatura.energia_acl_fp[CF.FAT]);
            energia_acl_fp[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.energia_acl_fp[CF.TAR] / 1000.0);
            energia_acl_fp[CF.TOT] = string.Format("{0:C}", fatura.energia_acl_fp[CF.TOT]);
            ViewBag.energia_acl_fp = energia_acl_fp;

            // Subvencao tarifaria
            string subvencao_tarifaria;
            subvencao_tarifaria = string.Format("{0:C}", fatura.subvencao_tarifaria);
            ViewBag.subvencao_tarifaria = subvencao_tarifaria;

            // flag Subvencao tarifaria
            ViewBag.flag_subvencao = fatura.flag_subvencao;

            //
            // Encargo contra COVID
            //

            // encargo covid na ponta
            var encargo_covid_p = new string[4];
            encargo_covid_p[CF.REG] = string.Format("{0:#,##0}", fatura.encargo_covid_p[CF.REG]);
            encargo_covid_p[CF.FAT] = string.Format("{0:#,##0}", fatura.encargo_covid_p[CF.FAT]);
            encargo_covid_p[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.encargo_covid_p[CF.TAR] / 1000.0);
            encargo_covid_p[CF.TOT] = string.Format("{0:C}", fatura.encargo_covid_p[CF.TOT]);
            ViewBag.encargo_covid_p = encargo_covid_p;

            // encargo covid fora de ponta
            var encargo_covid_fp = new string[4];
            encargo_covid_fp[CF.REG] = string.Format("{0:#,##0}", fatura.encargo_covid_fp[CF.REG]);
            encargo_covid_fp[CF.FAT] = string.Format("{0:#,##0}", fatura.encargo_covid_fp[CF.FAT]);
            encargo_covid_fp[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.encargo_covid_fp[CF.TAR] / 1000.0);
            encargo_covid_fp[CF.TOT] = string.Format("{0:C}", fatura.encargo_covid_fp[CF.TOT]);
            ViewBag.encargo_covid_fp = encargo_covid_fp;

            // flag encargo covid
            ViewBag.flag_encargo_covid = fatura.flag_encargo_covid;

            //
            // DESCONTOS
            //

            // desconto energia acl na ponta
            string desconto_energia_acl_p;
            desconto_energia_acl_p = string.Format("{0:C}", -1.0 * fatura.energia_acl_p[CF.TOT]);
            ViewBag.desconto_energia_acl_p = desconto_energia_acl_p;

            // desconto energia acl fora de ponta
            string desconto_energia_acl_fp;
            desconto_energia_acl_fp = string.Format("{0:C}", -1.0 * fatura.energia_acl_fp[CF.TOT]);
            ViewBag.desconto_energia_acl_fp = desconto_energia_acl_fp;

            // desconto Subvencao tarifaria
            string desconto_subvencao_tarifaria;
            desconto_subvencao_tarifaria = string.Format("{0:C}", -1.0 * fatura.subvencao_tarifaria);
            ViewBag.desconto_subvencao_tarifaria = desconto_subvencao_tarifaria;

            // desconto rural
            string per_desc_rural;
            per_desc_rural = string.Format("{0:0}", fatura.per_desc_rural);
            ViewBag.per_desc_rural = per_desc_rural;

            string desconto_rural;
            desconto_rural = string.Format("{0:C}", -1.0 * fatura.desconto_rural);
            ViewBag.desconto_rural = desconto_rural;

            // desconto irrigantes
            string per_desc_irrigantes;
            per_desc_irrigantes = string.Format("{0:0}", fatura.per_desc_irrigantes);
            ViewBag.per_desc_irrigantes = per_desc_irrigantes;

            string desconto_irrigantes;
            desconto_irrigantes = string.Format("{0:C}", -1.0 * fatura.desconto_irrigantes);
            ViewBag.desconto_irrigantes = desconto_irrigantes;

            // flag desconto rural
            ViewBag.flag_rural = fatura.flag_rural;

            // flag desconto irrigantes
            ViewBag.flag_irrigantes = fatura.flag_irrigantes;

            // flag bandeiras tarifarias propoporcionalmente
            ViewBag.flag_bandeira_prop = fatura.flag_bandeira_prop;

            //
            // IMPOSTOS
            //

            // PIS/COFINS
            var piscofins = new string[4];
            piscofins[CF.REG] = string.Format("{0:0.00}", fatura.piscofins[CF.REG]);
            piscofins[CF.FAT] = string.Format("{0:0.00}", fatura.piscofins[CF.FAT]);
            piscofins[CF.TAR] = string.Format("{0:C}", fatura.piscofins[CF.TAR]);
            piscofins[CF.TOT] = string.Format("{0:C}", fatura.piscofins[CF.TOT]);
            ViewBag.piscofins = piscofins;

            // ICMS
            var icms = new string[4];
            icms[CF.REG] = string.Format("{0:0.00}", fatura.icms[CF.REG]);
            icms[CF.FAT] = string.Format("{0:0.00}", fatura.icms[CF.FAT]);
            icms[CF.TAR] = string.Format("{0:C}", fatura.icms[CF.TAR]);
            icms[CF.TOT] = string.Format("{0:C}", fatura.icms[CF.TOT]);
            ViewBag.icms = icms;

            // flag ICMS
            ViewBag.flag_regra_icms = fatura.flag_regra_icms;

            //
            // MERCADO LIVRE
            //

            // encargos
            string encargos_conexao;
            encargos_conexao = string.Format("{0:C}", fatura.encargos_conexao);
            ViewBag.encargos_conexao = encargos_conexao;

            // descontos
            string desconto_demanda;
            string desconto_consumo_p;
            string desconto_consumo_fp;

            desconto_demanda = string.Format("{0:0.00} %", fatura.desconto_demanda);
            ViewBag.desconto_demanda = desconto_demanda;

            desconto_consumo_p = string.Format("{0:0.00} %", fatura.desconto_consumo_p);
            ViewBag.desconto_consumo_p = desconto_consumo_p;

            desconto_consumo_fp = string.Format("{0:0.00} %", fatura.desconto_consumo_fp);
            ViewBag.desconto_consumo_fp = desconto_consumo_fp;

            // consumo livre ponta
            var consumo_livre_p = new string[4];
            consumo_livre_p[CF.REG] = string.Format("{0:#,##0}", fatura.consumo_livre_p[CF.REG]);
            consumo_livre_p[CF.FAT] = string.Format("{0:#,##0}", fatura.consumo_livre_p[CF.FAT]);
            consumo_livre_p[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.consumo_livre_p[CF.TAR] / 1000.0);
            consumo_livre_p[CF.TOT] = string.Format("{0:C}", fatura.consumo_livre_p[CF.TOT]);
            ViewBag.consumo_livre_p = consumo_livre_p;

            // consumo livre fora de ponta
            var consumo_livre_fp = new string[4];
            consumo_livre_fp[CF.REG] = string.Format("{0:#,##0}", fatura.consumo_livre_fp[CF.REG]);
            consumo_livre_fp[CF.FAT] = string.Format("{0:#,##0}", fatura.consumo_livre_fp[CF.FAT]);
            consumo_livre_fp[CF.TAR] = string.Format("R$ {0:0.00000000}", fatura.consumo_livre_fp[CF.TAR] / 1000.0);
            consumo_livre_fp[CF.TOT] = string.Format("{0:C}", fatura.consumo_livre_fp[CF.TOT]);
            ViewBag.consumo_livre_fp = consumo_livre_fp;

            //
            // OUTROS
            //

            // encontra outros
            FaturaOutrosMetodos outrosMetodos = new FaturaOutrosMetodos();
            List<FaturaOutrosDominio> listaOutros = outrosMetodos.ListarPorData(fatura.IDMedicao, fatura.DataHoraIni, fatura.DataHoraFim);

            ViewBag.listaOutros = listaOutros;

            //
            // TOTAL
            //

            // subtotal
            string subtotal;
            subtotal = string.Format("{0:C}", fatura.subtotal);
            ViewBag.subtotal = subtotal;

            // subtotal A
            string subtotal_A;
            subtotal_A = string.Format("{0:C}", fatura.subtotal_A);
            ViewBag.subtotal_A = subtotal_A;

            // subtotal B
            string subtotal_B;
            subtotal_B = string.Format("{0:C}", fatura.subtotal_B);
            ViewBag.subtotal_B = subtotal_B;

            // total
            string total;
            total = string.Format("{0:C}", fatura.total);
            ViewBag.total = total;

            // fatura
            ViewBag.Fatura = fatura;


            //
            // FATURA MES -1
            //

            // calcula custo medio
            CustoMedio = (faturaAnt1.consumo_total_te[CF.REG] == 0.0) ? 0.0 : (faturaAnt1.total / faturaAnt1.consumo_total_te[CF.REG]) * 1000.0;

            // tabela totais
            ViewBag.Fatura_Ant1_Data = Funcoes_SmartEnergy.StringMesFechamento(faturaAnt1.DataHoraFim);
            ViewBag.Fatura_Ant1_Bandeira_ini = Funcoes_SmartEnergy.BandeiraCor(faturaAnt1.bandeira_ini);
            ViewBag.Fatura_Ant1_BandeiraTexto_ini = Funcoes_SmartEnergy.BandeiraTexto(faturaAnt1.bandeira_ini);

            // verifica se tem bandeira
            if (faturaAnt1.data_bandeira_fim_i.Year != 2000)
            {
                ViewBag.Fatura_Ant1_Bandeira_fim = Funcoes_SmartEnergy.BandeiraCor(faturaAnt1.bandeira_fim);
                ViewBag.Fatura_Ant1_BandeiraTexto_fim = Funcoes_SmartEnergy.BandeiraTexto(faturaAnt1.bandeira_fim);
            }
            else
            {
                ViewBag.Fatura_Ant1_BandeiraTexto_fim = "false";
            }

            ViewBag.Fatura_Ant1_ValorTotal = string.Format("{0:C}", faturaAnt1.total);
            ViewBag.Fatura_Ant1_ValorTotalN = faturaAnt1.total;

            ViewBag.Fatura_Ant1_CustoMedio = string.Format("{0:C} / MWh", CustoMedio);
            ViewBag.Fatura_Ant1_CustoMedioN = CustoMedio;


            //
            // FATURA ANO -1
            //

            // calcula custo medio
            CustoMedio = (faturaAnt2.consumo_total_te[CF.REG] == 0.0) ? 0.0 : (faturaAnt2.total / faturaAnt2.consumo_total_te[CF.REG]) * 1000.0;

            // tabela totais
            ViewBag.Fatura_Ant2_Data = Funcoes_SmartEnergy.StringMesFechamento(faturaAnt2.DataHoraFim);
            ViewBag.Fatura_Ant2_Bandeira = Funcoes_SmartEnergy.BandeiraCor(faturaAnt2.bandeira_ini);
            ViewBag.Fatura_Ant2_BandeiraTexto = Funcoes_SmartEnergy.BandeiraTexto(faturaAnt2.bandeira_ini);

            // verifica se tem bandeira
            if (faturaAnt2.data_bandeira_fim_i.Year != 2000)
            {
                ViewBag.Fatura_Ant2_Bandeira_fim = Funcoes_SmartEnergy.BandeiraCor(faturaAnt2.bandeira_fim);
                ViewBag.Fatura_Ant2_BandeiraTexto_fim = Funcoes_SmartEnergy.BandeiraTexto(faturaAnt2.bandeira_fim);
            }
            else
            {
                ViewBag.Fatura_Ant2_BandeiraTexto_fim = "false";
            }

            ViewBag.Fatura_Ant2_ValorTotal = string.Format("{0:C}", faturaAnt2.total);
            ViewBag.Fatura_Ant2_ValorTotalN = faturaAnt2.total;

            ViewBag.Fatura_Ant2_CustoMedio = string.Format("{0:C} / MWh", CustoMedio);
            ViewBag.Fatura_Ant2_CustoMedioN = CustoMedio;

            // diferenca em % entre total Atual e Anterior 1
            double A = faturaAnt1.total;
            double B = fatura.total;
            double PorcAnt1Atual = (A == 0.0) ? 0.0 : ((B - A) / A) * 100.0;

            ViewBag.Fatura_PorcAnt1AtualN = PorcAnt1Atual;
            ViewBag.Fatura_PorcAnt1Atual = string.Format("{0:0.0} %", PorcAnt1Atual);

            // diferenca em % entre total Anterior 2 e Anterior 1
            A = faturaAnt2.total;
            B = faturaAnt1.total;
            double PorcAnt2Ant1 = (A == 0.0) ? 0.0 : ((B - A) / A) * 100.0;

            ViewBag.Fatura_PorcAnt2Ant1N = PorcAnt2Ant1;
            ViewBag.Fatura_PorcAnt2Ant1 = string.Format("{0:0.0} %", PorcAnt2Ant1);

            return;
        }

        // GET: Fatura Azul
        private void Fatura_Energia_Azul(int IDCliente, int IDMedicao, int IDGateway, DATAHORA dh_ini, DATAHORA dh_fim, int TipoContrato, int IDSimulacaoCenario)
        {
            //
            // FECHAMENTOS
            //

            List<string> Fechamentos = new List<string>();
            List<string> FechamentosTexto = new List<string>();

            // verifica se livre
            if (TipoContrato == 1)
            {
                Fechamentos = ViewBag.FechamentosLivre;
                FechamentosTexto = ViewBag.FechamentosLivre_Texto;
            }
            else
            {
                Fechamentos = ViewBag.FechamentosCativo;
                FechamentosTexto = ViewBag.FechamentosCativo_Texto;
            }

            //
            // FATURA ATUAL
            //

            // nome do relatorio
            ViewBag.NomeRelat = string.Format("Fatura de Energia Azul");

            int retorno;
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RESULT_ENERGIA_FATURA faturaDLL = new RESULT_ENERGIA_FATURA();
            RESULT_ENERGIA_FATURA faturaDLL_sim = new RESULT_ENERGIA_FATURA();
            RESULT_ENERGIA_FATURA faturaAnt1DLL = new RESULT_ENERGIA_FATURA();
            RESULT_ENERGIA_FATURA faturaAnt1DLL_sim = new RESULT_ENERGIA_FATURA();
            RESULT_ENERGIA_FATURA faturaAnt2DLL = new RESULT_ENERGIA_FATURA();
            RESULT_ENERGIA_FATURA faturaAnt2DLL_sim = new RESULT_ENERGIA_FATURA();

            FaturasDominio fatura = new FaturasDominio();
            FaturasDominio faturaAnt1 = new FaturasDominio();
            FaturasDominio faturaAnt2 = new FaturasDominio();

            // converte para DateTime
            DateTime data_hora_ini = Funcoes_Converte.ConverteDataHora2DateTime(dh_ini);
            DateTime data_hora_fim = Funcoes_Converte.ConverteDataHora2DateTime(dh_fim);

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = IDGateway;

            // calcula valores fatura
            retorno = SmCalcDB_Energia_Fatura((char)0, ref config_interface, IDSimulacaoCenario, (char)TipoContrato, (char)TIPOESTR_TAR.THS_AZUL, (char)0, ref dh_ini, ref dh_fim, ref faturaDLL, ref faturaDLL_sim);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Fatura {0}: Erro {1}", string.Format("{0:Y}", data_hora_ini), retorno));
                if (faturaDLL.flag_registros > 0)
                    listaErros.Add(string.Format("Fatura {0}: {1}", string.Format("{0:Y}", data_hora_ini), Funcoes_SmartEnergy.ErroRelatorio((int)faturaDLL.flag_registros)));
            }

            // verifica se simulação ativa
            if (IDSimulacaoCenario > 0)
            {
                // copia estrutura
                fatura = CopiaFaturaDLL(faturaDLL_sim);
            }
            else
            {
                // copia estrutura
                fatura = CopiaFaturaDLL(faturaDLL);
            }

            fatura.IDCliente = IDCliente;
            fatura.IDMedicao = IDMedicao;
            fatura.IDContratoMedicao = TipoContrato;

            fatura.DataHoraIni = data_hora_ini;
            fatura.DataHoraFim = data_hora_fim;

            //
            // FATURA MES -1
            //

            // atual
            DateTime data_atual_ini = data_hora_ini;
            DateTime data_atual_fim = data_hora_fim;

            // mes anterior
            data_hora_ini = data_atual_ini.AddMonths(-1);
            data_hora_fim = data_atual_fim.AddMonths(-1);

            // verifica se tem a data nos fechamentos
            string data_busca = Funcoes_SmartEnergy.StringMesFechamento(data_hora_fim);

            // pega o indice
            int index = FechamentosTexto.FindIndex(s => s.Contains(data_busca));

            if( index >= 0 )
            {
                // data inicial
                string data_str = Fechamentos[index].Substring(0, 16);
                data_hora_ini = DateTime.Parse(data_str);

                // data final
                data_str = Fechamentos[index].Substring(19, 16);
                data_hora_fim = DateTime.Parse(data_str);
            }

            // converte para DateHora
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, data_hora_ini);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, data_hora_fim);

            // calcula valores fatura
            retorno = SmCalcDB_Energia_Fatura((char)0, ref config_interface, IDSimulacaoCenario, (char)TipoContrato, (char)TIPOESTR_TAR.THS_AZUL, (char)0, ref dh_ini, ref dh_fim, ref faturaAnt1DLL, ref faturaAnt1DLL_sim);

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Fatura {0}: Erro {1}", string.Format("{0:Y}", data_hora_ini), retorno));
                if (faturaAnt1DLL.flag_registros > 0)
                    listaErros.Add(string.Format("Fatura {0}: {1}", string.Format("{0:Y}", data_hora_ini), Funcoes_SmartEnergy.ErroRelatorio((int)faturaAnt1DLL.flag_registros)));
            }

            // verifica se simulação ativa
            if (IDSimulacaoCenario > 0)
            {
                // copia estrutura
                faturaAnt1 = CopiaFaturaDLL(faturaAnt1DLL_sim);
            }
            else
            {
                // copia estrutura
                faturaAnt1 = CopiaFaturaDLL(faturaAnt1DLL);
            }

            faturaAnt1.IDCliente = IDCliente;
            faturaAnt1.IDMedicao = IDMedicao;
            faturaAnt1.IDContratoMedicao = TipoContrato;

            faturaAnt1.DataHoraIni = data_hora_ini;
            faturaAnt1.DataHoraFim = data_hora_fim;

            //
            // FATURA ANO -1
            //

            // ano anterior
            data_hora_ini = data_atual_ini.AddYears(-1);
            data_hora_fim = data_atual_fim.AddYears(-1);

            // verifica se tem a data nos fechamentos
            data_busca = Funcoes_SmartEnergy.StringMesFechamento(data_hora_fim);

            // pega o indice
            index = FechamentosTexto.FindIndex(s => s.Contains(data_busca));

            if (index >= 0)
            {
                // data inicial
                string data_str = Fechamentos[index].Substring(0, 16);
                data_hora_ini = DateTime.Parse(data_str);

                // data final
                data_str = Fechamentos[index].Substring(19, 16);
                data_hora_fim = DateTime.Parse(data_str);
            }

            // converte para DateHora
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, data_hora_ini);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, data_hora_fim);

            // calcula valores fatura
            retorno = SmCalcDB_Energia_Fatura((char)0, ref config_interface, IDSimulacaoCenario, (char)TipoContrato, (char)TIPOESTR_TAR.THS_AZUL, (char)0, ref dh_ini, ref dh_fim, ref faturaAnt2DLL, ref faturaAnt2DLL_sim);

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Fatura {0}: Erro {1}", string.Format("{0:Y}", data_hora_ini), retorno));
                if (faturaAnt2DLL.flag_registros > 0)
                    listaErros.Add(string.Format("Fatura {0}: {1}", string.Format("{0:Y}", data_hora_ini), Funcoes_SmartEnergy.ErroRelatorio((int)faturaAnt2DLL.flag_registros)));
            }

            // verifica se simulação ativa
            if (IDSimulacaoCenario > 0)
            {
                // copia estrutura
                faturaAnt2 = CopiaFaturaDLL(faturaAnt2DLL_sim);
            }
            else
            {
                // copia estrutura
                faturaAnt2 = CopiaFaturaDLL(faturaAnt2DLL);
            }

            faturaAnt2.IDCliente = IDCliente;
            faturaAnt2.IDMedicao = IDMedicao;
            faturaAnt2.IDContratoMedicao = TipoContrato;

            faturaAnt2.DataHoraIni = data_hora_ini;
            faturaAnt2.DataHoraFim = data_hora_fim;


            // formata fatura
            Fatura_Energia_Azul_Formata(fatura, faturaAnt1, faturaAnt2);

            // lista de erros
            @ViewBag.listaErros = listaErros;

            return;
        }

        // Fatura Energia Azul XLS
        private HSSFWorkbook Fatura_Energia_Azul_XLS(int IDCliente, int IDMedicao)
        {
            // fatura
            FaturasDominio fatura = ViewBag.Fatura;

            // simulação
            bool aplica_simulacao = ViewBag.AplicaSimulacao;
            string NomeSimulacaoCenario = ViewBag._NomeSimulacaoCenario;


            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _5CellStyle = criaEstiloXLS(workbook, 5);
            ICellStyle _8CellStyle = criaEstiloXLS(workbook, 8);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // FATURA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Fatura");
            IRow row;
            int rowIndex;

            // DEMANDA

            // cabecalho
            string[] cabecalho1 = { "Demanda", "Registrado (kW)", "Faturado (kW)", "Tarifa (R$)", "Valor (R$)" };
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho1);

            // numero de dias
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Número de Dias");
            numeroCelulaXLS(row, 1, fatura.num_dias, _intCellStyle);

            // demanda ponta
            row = sheet.CreateRow(rowIndex++);

            if (fatura.IDContratoMedicao == TIPOCONTRATO.LIVRE)
            {
                textoCelulaXLS(row, 0, string.Format("Demanda Ponta com Desconto ({0:0.00})", ViewBag.desconto_demanda));
            }
            else
            {
                textoCelulaXLS(row, 0, "Demanda Ponta");
            }

            numeroCelulaXLS(row, 1, fatura.demanda_p[0], _1CellStyle);
            numeroCelulaXLS(row, 2, fatura.demanda_p[CF.FAT], _1CellStyle);
            numeroCelulaXLS(row, 3, fatura.demanda_p[CF.TAR], _8CellStyle);
            numeroCelulaXLS(row, 4, fatura.demanda_p[CF.TOT], _2CellStyle);

            // demanda fora de ponta
            row = sheet.CreateRow(rowIndex++);

            if (fatura.IDContratoMedicao == TIPOCONTRATO.LIVRE)
            {
                textoCelulaXLS(row, 0, string.Format("Demanda Fora de Ponta com Desconto ({0:0.00})", ViewBag.desconto_demanda));
            }
            else
            {
                textoCelulaXLS(row, 0, "Demanda Fora de Ponta");
            }

            // verifica qual deve apresentar
            if (fatura.demanda_fpi[CF.REG] >= fatura.demanda_fpc[CF.REG])
            {
                numeroCelulaXLS(row, 1, fatura.demanda_fpi[CF.REG], _1CellStyle);
                numeroCelulaXLS(row, 2, fatura.demanda_fpi[CF.FAT], _1CellStyle);
                numeroCelulaXLS(row, 3, fatura.demanda_fpi[CF.TAR], _8CellStyle);
                numeroCelulaXLS(row, 4, fatura.demanda_fpi[CF.TOT], _2CellStyle);
            }
            else
            {
                numeroCelulaXLS(row, 1, fatura.demanda_fpc[CF.REG], _1CellStyle);
                numeroCelulaXLS(row, 2, fatura.demanda_fpc[CF.FAT], _1CellStyle);
                numeroCelulaXLS(row, 3, fatura.demanda_fpc[CF.TAR], _8CellStyle);
                numeroCelulaXLS(row, 4, fatura.demanda_fpc[CF.TOT], _2CellStyle);
            }

            // ultrapassagem ponta
            if (fatura.ultr_demanda_p[CF.FAT] != 0.0)
            {
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Ultrapassagem Ponta");
                numeroCelulaXLS(row, 2, fatura.ultr_demanda_p[CF.FAT], _1CellStyle);
                numeroCelulaXLS(row, 3, fatura.ultr_demanda_p[CF.TAR], _8CellStyle);
                numeroCelulaXLS(row, 4, fatura.ultr_demanda_p[CF.TOT], _2CellStyle);
            }

            // ultrapassagem fora de ponta
            if (fatura.ultr_demanda_fp[CF.FAT] != 0.0)
            {
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Ultrapassagem Fora de Ponta");
                numeroCelulaXLS(row, 2, fatura.ultr_demanda_fp[CF.FAT], _1CellStyle);
                numeroCelulaXLS(row, 3, fatura.ultr_demanda_fp[CF.TAR], _8CellStyle);
                numeroCelulaXLS(row, 4, fatura.ultr_demanda_fp[CF.TOT], _2CellStyle);
            }

            // contrato ponta
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Contrato Ponta");
            numeroCelulaXLS(row, 1, fatura.contrato_dem_p, _1CellStyle);

            // contrato fora de ponta
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Contrato Fora de Ponta");
            numeroCelulaXLS(row, 1, fatura.contrato_dem_fp, _1CellStyle);

            // constante de demanda
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Constante de Demanda");
            numeroCelulaXLS(row, 1, fatura.demanda_k, _5CellStyle);

            // fator de carga ponta
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Fator de Carga Ponta");
            numeroCelulaXLS(row, 1, fatura.fatcar_p[CF.REG], _2CellStyle);

            // fator de carga fora de ponta
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Fator de Carga Fora de Ponta");
            numeroCelulaXLS(row, 1, fatura.fatcar_fp[CF.REG], _2CellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // DEMANDA PERIODO EXCEDENTE

            if (ViewBag.num_dias_pe > 0)
            {
                // cabecalho
                string[] cabecalho2 = { "Demanda Período Excedente", "Registrado (kW)", "Faturado (kW)", "Tarifa (R$)", "Valor (R$)" };
                cabecalhoTabelaXLS(workbook, sheet, cabecalho2, rowIndex++);

                // numero de dias
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Número de Dias");
                numeroCelulaXLS(row, 1, fatura.num_dias_pe, _intCellStyle);

                // demanda ponta
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Demanda Ponta");
                numeroCelulaXLS(row, 1, fatura.demanda_pe_p[CF.REG], _1CellStyle);
                numeroCelulaXLS(row, 2, fatura.demanda_pe_p[CF.FAT], _1CellStyle);
                numeroCelulaXLS(row, 3, fatura.demanda_pe_p[CF.TAR], _8CellStyle);
                numeroCelulaXLS(row, 4, fatura.demanda_pe_p[CF.TOT], _2CellStyle);

                // demanda fora de ponta
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Demanda Fora de Ponta");

                // verifica qual deve apresentar
                if (fatura.demanda_pe_fpi[CF.REG] >= fatura.demanda_pe_fpc[CF.REG])
                {
                    numeroCelulaXLS(row, 1, fatura.demanda_pe_fpi[CF.REG], _1CellStyle);
                    numeroCelulaXLS(row, 2, fatura.demanda_pe_fpi[CF.FAT], _1CellStyle);
                    numeroCelulaXLS(row, 3, fatura.demanda_pe_fpi[CF.TAR], _8CellStyle);
                    numeroCelulaXLS(row, 4, fatura.demanda_pe_fpi[CF.TOT], _2CellStyle);
                }
                else
                {
                    numeroCelulaXLS(row, 1, fatura.demanda_pe_fpc[CF.REG], _1CellStyle);
                    numeroCelulaXLS(row, 2, fatura.demanda_pe_fpc[CF.FAT], _1CellStyle);
                    numeroCelulaXLS(row, 3, fatura.demanda_pe_fpc[CF.TAR], _8CellStyle);
                    numeroCelulaXLS(row, 4, fatura.demanda_pe_fpc[CF.TOT], _2CellStyle);
                }

                // ultrapassagem ponta
                if (fatura.ultr_demanda_pe_p[1] != 0.0)
                {
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, "Ultrapassagem Ponta");
                    numeroCelulaXLS(row, 2, fatura.ultr_demanda_pe_p[CF.FAT], _1CellStyle);
                    numeroCelulaXLS(row, 3, fatura.ultr_demanda_pe_p[CF.TAR], _8CellStyle);
                    numeroCelulaXLS(row, 4, fatura.ultr_demanda_pe_p[CF.TOT], _2CellStyle);
                }

                // ultrapassagem fora de ponta
                if (fatura.ultr_demanda_pe_fp[1] != 0.0)
                {
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, "Ultrapassagem Fora de Ponta");
                    numeroCelulaXLS(row, 2, fatura.ultr_demanda_pe_fp[CF.FAT], _1CellStyle);
                    numeroCelulaXLS(row, 3, fatura.ultr_demanda_pe_fp[CF.TAR], _8CellStyle);
                    numeroCelulaXLS(row, 4, fatura.ultr_demanda_pe_fp[CF.TOT], _2CellStyle);
                }

                // contrato ponta
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Contrato Ponta");
                numeroCelulaXLS(row, 1, fatura.contrato_dem_pe_p, _1CellStyle);

                // contrato fora de ponta
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Contrato Fora de Ponta");
                numeroCelulaXLS(row, 1, fatura.contrato_dem_pe_fp, _1CellStyle);

                // pula linha
                row = sheet.CreateRow(rowIndex++);
            }

            // CONSUMO TUSD

            if (ViewBag.flag_tusd_te == 1)
            {
                // cabecalho
                string[] cabecalho3 = { "Consumo TUSD", "Registrado (kWh)", "Faturado (kWh)", "Tarifa (R$)", "Valor (R$)" };
                cabecalhoTabelaXLS(workbook, sheet, cabecalho3, rowIndex++);

                // consumo ponta
                row = sheet.CreateRow(rowIndex++);

                if (fatura.IDContratoMedicao == TIPOCONTRATO.LIVRE)
                {
                    textoCelulaXLS(row, 0, string.Format("Consumo Ponta com Desconto ({0})", ViewBag.desconto_consumo_p));
                }
                else
                {
                    textoCelulaXLS(row, 0, "Consumo Ponta");
                }

                numeroCelulaXLS(row, 1, fatura.consumo_p_tusd[CF.REG], _intCellStyle);
                numeroCelulaXLS(row, 2, fatura.consumo_p_tusd[CF.FAT], _intCellStyle);
                numeroCelulaXLS(row, 3, fatura.consumo_p_tusd[CF.TAR] / 1000.0, _8CellStyle);
                numeroCelulaXLS(row, 4, fatura.consumo_p_tusd[CF.TOT], _2CellStyle);

                // consumo fora de ponta
                row = sheet.CreateRow(rowIndex++);

                if (fatura.IDContratoMedicao == TIPOCONTRATO.LIVRE)
                {
                    textoCelulaXLS(row, 0, string.Format("Consumo Fora de Ponta com Desconto ({0})", ViewBag.desconto_consumo_fp));
                }
                else
                {
                    textoCelulaXLS(row, 0, "Consumo Fora de Ponta");
                }

                numeroCelulaXLS(row, 1, fatura.consumo_fpi_tusd[CF.REG] + fatura.consumo_fpc_tusd[CF.REG], _intCellStyle);
                numeroCelulaXLS(row, 2, fatura.consumo_fpi_tusd[CF.FAT] + fatura.consumo_fpc_tusd[CF.FAT], _intCellStyle);
                numeroCelulaXLS(row, 3, fatura.consumo_fpi_tusd[CF.TAR] / 1000.0, _8CellStyle);
                numeroCelulaXLS(row, 4, fatura.consumo_fpi_tusd[CF.TOT] + fatura.consumo_fpc_tusd[CF.TOT], _2CellStyle);

                // consumo reservado
                if (fatura.IDContratoMedicao == TIPOCONTRATO.RURAL)
                {
                    // consumo reservado
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, "Consumo Reservado");
                    numeroCelulaXLS(row, 1, fatura.consumo_r_tusd[CF.REG], _intCellStyle);
                    numeroCelulaXLS(row, 2, fatura.consumo_r_tusd[CF.FAT], _intCellStyle);
                    numeroCelulaXLS(row, 3, fatura.consumo_r_tusd[CF.TAR] / 1000.0, _8CellStyle);
                    numeroCelulaXLS(row, 4, fatura.consumo_r_tusd[CF.TOT], _2CellStyle);
                }

                // consumo total
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Consumo Total");
                numeroCelulaXLS(row, 1, fatura.consumo_total_tusd[CF.REG], _intCellStyle);

                // pula linha
                row = sheet.CreateRow(rowIndex++);
            }

            // CONSUMO TE

            if (fatura.IDContratoMedicao == TIPOCONTRATO.CATIVO || fatura.IDContratoMedicao == TIPOCONTRATO.RURAL)
            {
                // cabecalho
                if (ViewBag.flag_tusd_te == 1)
                {
                    string[] cabecalho4 = { "Consumo TE", "Registrado (kWh)", "Faturado (kWh)", "Tarifa (R$)", "Valor (R$)" };
                    cabecalhoTabelaXLS(workbook, sheet, cabecalho4, rowIndex++);
                }
                else
                {
                    string[] cabecalho5 = { "Consumo", "Registrado (kWh)", "Faturado (kWh)", "Tarifa (R$)", "Valor (R$)" };
                    cabecalhoTabelaXLS(workbook, sheet, cabecalho5, rowIndex++);
                }

                // consumo ponta
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Consumo Ponta");
                numeroCelulaXLS(row, 1, fatura.consumo_p_te[CF.REG], _intCellStyle);
                numeroCelulaXLS(row, 2, fatura.consumo_p_te[CF.FAT], _intCellStyle);
                numeroCelulaXLS(row, 3, fatura.consumo_p_te[CF.TAR] / 1000.0, _8CellStyle);
                numeroCelulaXLS(row, 4, fatura.consumo_p_te[CF.TOT], _2CellStyle);

                // consumo ponta
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Consumo Fora de Ponta");
                numeroCelulaXLS(row, 1, fatura.consumo_fpi_te[CF.REG] + fatura.consumo_fpc_te[CF.REG], _intCellStyle);
                numeroCelulaXLS(row, 2, fatura.consumo_fpi_te[CF.FAT] + fatura.consumo_fpc_te[CF.FAT], _intCellStyle);
                numeroCelulaXLS(row, 3, fatura.consumo_fpi_te[CF.TAR] / 1000.0, _8CellStyle);
                numeroCelulaXLS(row, 4, fatura.consumo_fpi_te[CF.TOT] + fatura.consumo_fpc_te[CF.TOT], _2CellStyle);

                // consumo reservado
                if (fatura.IDContratoMedicao == TIPOCONTRATO.RURAL)
                {
                    // consumo reservado
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, "Consumo Reservado");
                    numeroCelulaXLS(row, 1, fatura.consumo_r_te[CF.REG], _intCellStyle);
                    numeroCelulaXLS(row, 2, fatura.consumo_r_te[CF.FAT], _intCellStyle);
                    numeroCelulaXLS(row, 3, fatura.consumo_r_te[CF.TAR] / 1000.0, _8CellStyle);
                    numeroCelulaXLS(row, 4, fatura.consumo_r_te[CF.TOT], _2CellStyle);
                }

                // consumo total
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Consumo Total");
                numeroCelulaXLS(row, 1, fatura.consumo_total_te[CF.REG], _intCellStyle);

                // pula linha
                row = sheet.CreateRow(rowIndex++);
            }

            // BANDEIRAS TARIFARIAS

            if ( ViewBag.Fatura_Atual_BandeiraTexto_ini != "")
            {
                // cabecalho
                string[] cabecalho6 = { "Bandeiras Tarifárias", "Registrado (kWh)", "Faturado (kWh)", "Tarifa (R$)", "Valor (R$)" };
                cabecalhoTabelaXLS(workbook, sheet, cabecalho6, rowIndex++);

                string bandeira = @ViewBag.bandeira_ini;

                if (bandeira != "false")
                {
                    // bandeira ini
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, @ViewBag.bandeira_ini);

                    // ponta
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, "Ponta");
                    numeroCelulaXLS(row, 1, fatura.consumo_bandeira_p_ini[CF.REG], _intCellStyle);
                    numeroCelulaXLS(row, 2, fatura.consumo_bandeira_p_ini[CF.FAT], _intCellStyle);
                    numeroCelulaXLS(row, 3, fatura.consumo_bandeira_p_ini[CF.TAR], _8CellStyle);
                    numeroCelulaXLS(row, 4, fatura.consumo_bandeira_p_ini[CF.TOT], _2CellStyle);

                    // fora de ponta
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, "Fora de Ponta");
                    numeroCelulaXLS(row, 1, fatura.consumo_bandeira_fp_ini[CF.REG], _intCellStyle);
                    numeroCelulaXLS(row, 2, fatura.consumo_bandeira_fp_ini[CF.FAT], _intCellStyle);
                    numeroCelulaXLS(row, 3, fatura.consumo_bandeira_fp_ini[CF.TAR], _8CellStyle);
                    numeroCelulaXLS(row, 4, fatura.consumo_bandeira_fp_ini[CF.TOT], _2CellStyle);

                    // reservado
                    if (fatura.IDContratoMedicao == TIPOCONTRATO.RURAL)
                    {
                        // reservado
                        row = sheet.CreateRow(rowIndex++);
                        textoCelulaXLS(row, 0, "Reservado");
                        numeroCelulaXLS(row, 1, fatura.consumo_bandeira_r_ini[CF.REG], _intCellStyle);
                        numeroCelulaXLS(row, 2, fatura.consumo_bandeira_r_ini[CF.FAT], _intCellStyle);
                        numeroCelulaXLS(row, 3, fatura.consumo_bandeira_r_ini[CF.TAR], _8CellStyle);
                        numeroCelulaXLS(row, 4, fatura.consumo_bandeira_r_ini[CF.TOT], _2CellStyle);
                    }
                }

                bandeira = @ViewBag.bandeira_fim;

                if (bandeira != "false")
                {
                    // bandeira fim
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, @ViewBag.bandeira_fim);

                    // ponta
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, "Ponta");
                    numeroCelulaXLS(row, 1, fatura.consumo_bandeira_p_fim[CF.REG], _intCellStyle);
                    numeroCelulaXLS(row, 2, fatura.consumo_bandeira_p_fim[CF.FAT], _intCellStyle);
                    numeroCelulaXLS(row, 3, fatura.consumo_bandeira_p_fim[CF.TAR], _8CellStyle);
                    numeroCelulaXLS(row, 4, fatura.consumo_bandeira_p_fim[CF.TOT], _2CellStyle);

                    // fora de ponta
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, "Fora de Ponta");
                    numeroCelulaXLS(row, 1, fatura.consumo_bandeira_fp_fim[CF.REG], _intCellStyle);
                    numeroCelulaXLS(row, 2, fatura.consumo_bandeira_fp_fim[CF.FAT], _intCellStyle);
                    numeroCelulaXLS(row, 3, fatura.consumo_bandeira_fp_fim[CF.TAR], _8CellStyle);
                    numeroCelulaXLS(row, 4, fatura.consumo_bandeira_fp_fim[CF.TOT], _2CellStyle);

                    // reservado
                    if (fatura.IDContratoMedicao == TIPOCONTRATO.RURAL)
                    {
                        // reservado
                        row = sheet.CreateRow(rowIndex++);
                        textoCelulaXLS(row, 0, "Reservado");
                        numeroCelulaXLS(row, 1, fatura.consumo_bandeira_r_fim[CF.REG], _intCellStyle);
                        numeroCelulaXLS(row, 2, fatura.consumo_bandeira_r_fim[CF.FAT], _intCellStyle);
                        numeroCelulaXLS(row, 3, fatura.consumo_bandeira_r_fim[CF.TAR], _8CellStyle);
                        numeroCelulaXLS(row, 4, fatura.consumo_bandeira_r_fim[CF.TOT], _2CellStyle);
                    }
                }

                // pula linha
                row = sheet.CreateRow(rowIndex++);
            }
                
            // FDR

            // cabecalho
            string[] cabecalho7 = { "FDR", "Registrado (kW)", "Faturado (kW)", "Tarifa (R$)", "Valor (R$)" };
            cabecalhoTabelaXLS(workbook, sheet, cabecalho7, rowIndex++);

            // FDR ponta
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "FDR Ponta");
            numeroCelulaXLS(row, 1, fatura.fdr_p[CF.REG], _1CellStyle);
            numeroCelulaXLS(row, 2, fatura.fdr_p[CF.FAT], _1CellStyle);
            numeroCelulaXLS(row, 3, fatura.fdr_p[CF.TAR], _8CellStyle);
            numeroCelulaXLS(row, 4, fatura.fdr_p[CF.TOT], _2CellStyle);

            // FDR fora de ponta
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "FDR Fora de Ponta");
            numeroCelulaXLS(row, 1, fatura.fdr_fp[CF.REG], _1CellStyle);
            numeroCelulaXLS(row, 2, fatura.fdr_fp[CF.FAT], _1CellStyle);
            numeroCelulaXLS(row, 3, fatura.fdr_fp[CF.TAR], _8CellStyle);
            numeroCelulaXLS(row, 4, fatura.fdr_fp[CF.TOT], _2CellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // FDR PERIODO EXCEDENTE

            if (fatura.num_dias_pe > 0)
            {
                // cabecalho
                string[] cabecalho8 = { "FDR Período Excedente", "Registrado (kW)", "Faturado (kW)", "Tarifa (R$)", "Valor (R$)" };
                cabecalhoTabelaXLS(workbook, sheet, cabecalho8, rowIndex++);

                // FDR ponta
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "FDR Ponta");
                numeroCelulaXLS(row, 1, fatura.fdr_pe_p[CF.REG], _1CellStyle);
                numeroCelulaXLS(row, 2, fatura.fdr_pe_p[CF.FAT], _1CellStyle);
                numeroCelulaXLS(row, 3, fatura.fdr_pe_p[CF.TAR], _8CellStyle);
                numeroCelulaXLS(row, 4, fatura.fdr_pe_p[CF.TOT], _2CellStyle);

                // FDR fora de ponta
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "FDR Fora de Ponta");
                numeroCelulaXLS(row, 1, fatura.fdr_pe_fp[CF.REG], _1CellStyle);
                numeroCelulaXLS(row, 2, fatura.fdr_pe_fp[CF.FAT], _1CellStyle);
                numeroCelulaXLS(row, 3, fatura.fdr_pe_fp[CF.TAR], _8CellStyle);
                numeroCelulaXLS(row, 4, fatura.fdr_pe_fp[CF.TOT], _2CellStyle);

                // pula linha
                row = sheet.CreateRow(rowIndex++);
            }

            // FER

            // cabecalho
            string[] cabecalho9 = { "FER", "Registrado (kWh)", "Faturado (kWh)", "Tarifa (R$)", "Valor (R$)" };
            cabecalhoTabelaXLS(workbook, sheet, cabecalho9, rowIndex++);

            // FER ponta
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "FER Ponta");
            numeroCelulaXLS(row, 1, fatura.fer_p[CF.REG], _intCellStyle);
            numeroCelulaXLS(row, 2, fatura.fer_p[CF.FAT], _intCellStyle);
            numeroCelulaXLS(row, 3, fatura.fer_p[CF.TAR] / 1000.0, _8CellStyle);
            numeroCelulaXLS(row, 4, fatura.fer_p[CF.TOT], _2CellStyle);

            // FER fora de ponta indutivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "FER Fora de Ponta Indutivo");
            numeroCelulaXLS(row, 1, fatura.fer_fpi[CF.REG], _intCellStyle);
            numeroCelulaXLS(row, 2, fatura.fer_fpi[CF.FAT], _intCellStyle);
            numeroCelulaXLS(row, 3, fatura.fer_fpi[CF.TAR] / 1000.0, _8CellStyle);
            numeroCelulaXLS(row, 4, fatura.fer_fpi[CF.TOT], _2CellStyle);

            // FER fora de ponta capacitivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "FER Fora de Ponta Capacitivo");
            numeroCelulaXLS(row, 1, fatura.fer_fpc[CF.REG], _intCellStyle);
            numeroCelulaXLS(row, 2, fatura.fer_fpc[CF.FAT], _intCellStyle);
            numeroCelulaXLS(row, 3, fatura.fer_fpc[CF.TAR] / 1000.0, _8CellStyle);
            numeroCelulaXLS(row, 4, fatura.fer_fpc[CF.TOT], _2CellStyle);

            // FER fora de ponta reservado
            if (fatura.IDContratoMedicao == TIPOCONTRATO.RURAL)
            {
                // FER fora de ponta reservado
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "FER Reservado");
                numeroCelulaXLS(row, 1, fatura.fer_r[CF.REG], _intCellStyle);
                numeroCelulaXLS(row, 2, fatura.fer_r[CF.FAT], _intCellStyle);
                numeroCelulaXLS(row, 3, fatura.fer_r[CF.TAR] / 1000.0, _8CellStyle);
                numeroCelulaXLS(row, 4, fatura.fer_r[CF.TOT], _2CellStyle);
            }

            // SUBTOTAL
            if (fatura.IDContratoMedicao == TIPOCONTRATO.LIVRE)
            {
                // pula linha
                row = sheet.CreateRow(rowIndex++);

                // SUBTOTAL A

                // cabecalho
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "SUBTOTAL A", _negritoCellStyle);
                numeroCelulaXLS(row, 4, fatura.subtotal_A, _2CellStyle);

                // pula linha
                row = sheet.CreateRow(rowIndex++);

                string[] cabecalho12 = { "Energia ACL", "Registrado (kWh)", "Faturado (kWh)", "Tarifa (R$)", "Valor (R$)" };
                cabecalhoTabelaXLS(workbook, sheet, cabecalho12, rowIndex++);

                // ponta
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Ponta");
                numeroCelulaXLS(row, 1, fatura.energia_acl_p[CF.REG], _intCellStyle);
                numeroCelulaXLS(row, 2, fatura.energia_acl_p[CF.FAT], _intCellStyle);
                numeroCelulaXLS(row, 3, fatura.energia_acl_p[CF.TAR] / 1000.0, _8CellStyle);
                numeroCelulaXLS(row, 4, fatura.energia_acl_p[CF.TOT], _2CellStyle);

                // fora de ponta 
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Fora de Ponta");
                numeroCelulaXLS(row, 1, fatura.energia_acl_fp[CF.REG], _intCellStyle);
                numeroCelulaXLS(row, 2, fatura.energia_acl_fp[CF.FAT], _intCellStyle);
                numeroCelulaXLS(row, 3, fatura.energia_acl_fp[CF.TAR] / 1000.0, _8CellStyle);
                numeroCelulaXLS(row, 4, fatura.energia_acl_fp[CF.TOT], _2CellStyle);

                // subvencao tarifaria
                if (fatura.flag_subvencao != 0)
                {
                    // pula linha
                    row = sheet.CreateRow(rowIndex++);

                    // subvencao tarifaria
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, "Subvenção Tarifária");
                    numeroCelulaXLS(row, 4, fatura.subvencao_tarifaria, _2CellStyle);
                }

                // encargo conta covid
                if (fatura.flag_encargo_covid != 0)
                {
                    // pula linha
                    row = sheet.CreateRow(rowIndex++);

                    string[] cabecalho14 = { "Encargo Conta COVID", "Registrado (kWh)", "Faturado (kWh)", "Tarifa (R$)", "Valor (R$)" };
                    cabecalhoTabelaXLS(workbook, sheet, cabecalho14, rowIndex++);

                    // ponta
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, "Ponta");
                    numeroCelulaXLS(row, 1, fatura.encargo_covid_p[CF.REG], _intCellStyle);
                    numeroCelulaXLS(row, 2, fatura.encargo_covid_p[CF.FAT], _intCellStyle);
                    numeroCelulaXLS(row, 3, fatura.encargo_covid_p[CF.TAR] / 1000.0, _8CellStyle);
                    numeroCelulaXLS(row, 4, fatura.encargo_covid_p[CF.TOT], _2CellStyle);

                    // fora de ponta 
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, "Fora de Ponta");
                    numeroCelulaXLS(row, 1, fatura.encargo_covid_fp[CF.REG], _intCellStyle);
                    numeroCelulaXLS(row, 2, fatura.encargo_covid_fp[CF.FAT], _intCellStyle);
                    numeroCelulaXLS(row, 3, fatura.encargo_covid_fp[CF.TAR] / 1000.0, _8CellStyle);
                    numeroCelulaXLS(row, 4, fatura.encargo_covid_fp[CF.TOT], _2CellStyle);
                }
            }

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // SUBTOTAL
            if (fatura.IDContratoMedicao == TIPOCONTRATO.LIVRE)
            {
                // SUBTOTAL B

                // cabecalho
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "SUBTOTAL B", _negritoCellStyle);
                numeroCelulaXLS(row, 4, fatura.subtotal_B, _2CellStyle);
            }
            else
            {
                // SUBTOTAL 

                // cabecalho
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "SUBTOTAL", _negritoCellStyle);
                numeroCelulaXLS(row, 4, fatura.subtotal_B, _2CellStyle);
            }

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // IMPOSTOS

            // cabecalho
            string[] cabecalho10 = { "Impostos", "Alíquota (%)", "Alíquota de Ajuste (%)", "Importe (R$)", "Valor (R$)" };
            cabecalhoTabelaXLS(workbook, sheet, cabecalho10, rowIndex++);

            // encargos de conexao
            if (fatura.IDContratoMedicao == TIPOCONTRATO.LIVRE)
            {
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Encargos de Conexão");
                numeroCelulaXLS(row, 4, fatura.encargos_conexao, _2CellStyle);
            }

            // PIS/COFINS
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "PIS/COFINS");
            numeroCelulaXLS(row, 1, fatura.piscofins[CF.REG], _2CellStyle);
            numeroCelulaXLS(row, 2, fatura.piscofins[CF.FAT], _2CellStyle);
            numeroCelulaXLS(row, 3, fatura.piscofins[CF.TAR], _2CellStyle);
            numeroCelulaXLS(row, 4, fatura.piscofins[CF.TOT], _2CellStyle);

            // ICMS
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "ICMS");
            numeroCelulaXLS(row, 1, fatura.icms[CF.REG], _2CellStyle);
            numeroCelulaXLS(row, 2, fatura.icms[CF.FAT], _2CellStyle);
            numeroCelulaXLS(row, 3, fatura.icms[CF.TAR], _2CellStyle);
            numeroCelulaXLS(row, 4, fatura.icms[CF.TOT], _2CellStyle);

            if (fatura.flag_regra_icms == 1)
            {
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Fatura não possui o recolhimento do ICMS sobre a parcela de Demanda não utilizada.");
            }

            // DESCONTO
            if (fatura.IDContratoMedicao == TIPOCONTRATO.LIVRE)
            {
                // pula linha
                row = sheet.CreateRow(rowIndex++);

                string[] cabecalho13 = { "Desconto", " ", " ", " ", "Valor (R$)" };
                cabecalhoTabelaXLS(workbook, sheet, cabecalho13, rowIndex++);

                // energia acl ponta
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Energia ACL Ponta");
                numeroCelulaXLS(row, 4, -1.0 * fatura.energia_acl_p[CF.TOT], _2CellStyle);

                // energia acl fora de ponta 
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Energia ACL Fora de Ponta");
                numeroCelulaXLS(row, 4, -1.0 * fatura.energia_acl_fp[CF.TOT], _2CellStyle);

                // subvencao tarifaria
                if (fatura.flag_subvencao != 0)
                {
                    // pula linha
                    row = sheet.CreateRow(rowIndex++);

                    // subvencao tarifaria
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, "Subvenção Tarifária");
                    numeroCelulaXLS(row, 4, -1.0 * fatura.subvencao_tarifaria, _2CellStyle);
                }
            }

            if (fatura.IDContratoMedicao == TIPOCONTRATO.RURAL)
            {
                // pula linha
                row = sheet.CreateRow(rowIndex++);

                string[] cabecalho13 = { "Desconto", " ", " ", "%", "Valor (R$)" };
                cabecalhoTabelaXLS(workbook, sheet, cabecalho13, rowIndex++);

                // rural
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Rural");
                numeroCelulaXLS(row, 3, fatura.per_desc_rural, _2CellStyle);
                numeroCelulaXLS(row, 4, -1.0 * fatura.desconto_rural, _2CellStyle);

                // irrigantes
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Irrigantes");
                numeroCelulaXLS(row, 3, fatura.per_desc_irrigantes, _2CellStyle);
                numeroCelulaXLS(row, 4, -1.0 * fatura.desconto_irrigantes, _2CellStyle);
            }

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // OUTROS
            List<FaturaOutrosDominio> listaOutros = @ViewBag.listaOutros;

            if (listaOutros != null)
            {
                if (listaOutros.Count > 0)
                {
                    // cabecalho
                    string[] cabecalho11 = { "Outros", " ", " ", " ", "Valor (R$)" };
                    cabecalhoTabelaXLS(workbook, sheet, cabecalho11, rowIndex++);

                    foreach (FaturaOutrosDominio outros in listaOutros)
                    {
                        row = sheet.CreateRow(rowIndex++);
                        textoCelulaXLS(row, 0, outros.Descricao);
                        numeroCelulaXLS(row, 4, outros.valorOutros, _2CellStyle);
                    }

                    // pula linha
                    row = sheet.CreateRow(rowIndex++);
                }
            }

            if (fatura.IDContratoMedicao == TIPOCONTRATO.LIVRE)
            {
                // SUBTOTAL

                // cabecalho
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "SUBTOTAL", _negritoCellStyle);
                numeroCelulaXLS(row, 4, fatura.subtotal, _2CellStyle);

                // pula linha
                row = sheet.CreateRow(rowIndex++);

                // CONSUMO MERCADO LIVRE

                // cabecalho
                string[] cabecalho3 = { "Consumo Mercado Livre", "Registrado (kWh)", "Faturado (kWh)", "Tarifa (R$)", "Valor (R$)" };
                cabecalhoTabelaXLS(workbook, sheet, cabecalho3, rowIndex++);

                // consumo ponta
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Consumo Ponta");
                numeroCelulaXLS(row, 1, fatura.consumo_livre_p[CF.REG], _intCellStyle);
                numeroCelulaXLS(row, 2, fatura.consumo_livre_p[CF.FAT], _intCellStyle);
                numeroCelulaXLS(row, 3, fatura.consumo_livre_p[CF.TAR] / 1000.0, _8CellStyle);
                numeroCelulaXLS(row, 4, fatura.consumo_livre_p[CF.TOT], _2CellStyle);

                // consumo fora de ponta
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Consumo Fora de Ponta");
                numeroCelulaXLS(row, 1, fatura.consumo_livre_fp[CF.REG], _intCellStyle);
                numeroCelulaXLS(row, 2, fatura.consumo_livre_fp[CF.FAT], _intCellStyle);
                numeroCelulaXLS(row, 3, fatura.consumo_livre_fp[CF.TAR] / 1000.0, _8CellStyle);
                numeroCelulaXLS(row, 4, fatura.consumo_livre_fp[CF.TOT], _2CellStyle);

                // pula linha
                row = sheet.CreateRow(rowIndex++);
            }

            // TOTAL

            // cabecalho
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "TOTAL", _negritoCellStyle);
            numeroCelulaXLS(row, 4, fatura.total, _2CellStyle);

            // largura de cada coluna
            for (int i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 10000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            if (fatura.IDContratoMedicao == TIPOCONTRATO.LIVRE)
            {
                rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fatura de Energia Livre", "Azul", _negritoCellStyle);
            }

            if (fatura.IDContratoMedicao == TIPOCONTRATO.CATIVO)
            {
                rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fatura de Energia Cativo", "Azul", _negritoCellStyle);
            }

            if (fatura.IDContratoMedicao == TIPOCONTRATO.RURAL)
            {
                rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fatura de Energia Rural", "Azul", _negritoCellStyle);
            }

            // simulação
            if (aplica_simulacao)
            {
                // simulação
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Simulação", _negritoCellStyle);
                textoCelulaXLS(row, 1, NomeSimulacaoCenario);

                // pula linha
                row = sheet.CreateRow(rowIndex++);
            }

            // distribuidora
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Distribuidora", _negritoCellStyle);
            textoCelulaXLS(row, 1, @ViewBag.TipoDistribuidora);

            // estrutura tarifaria
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Estrutura Tarifária", _negritoCellStyle);
            textoCelulaXLS(row, 1, @ViewBag.TipoEstruturaTarifariaSubgrupo);

            // subsistema
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "SubSistema", _negritoCellStyle);
            textoCelulaXLS(row, 1, @ViewBag.TipoSubSistema);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // cabecalho ponta e fora de ponta
            string[] cabecalho = { "Mês", "Bandeira", "Total (R$)", "", "Custo Médio (R$ / MWh)", "" };
            cabecalhoTabelaXLS(workbook, sheet, cabecalho, rowIndex++);

            // mes atual
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, @ViewBag.Fatura_Atual_Data, _negritoCellStyle);
            if (@ViewBag.Fatura_Atual_BandeiraTexto_fim != "false")
            {
                textoCelulaXLS(row, 1, @ViewBag.Fatura_Atual_BandeiraTexto_fim);
            }
            else
            {
                textoCelulaXLS(row, 1, "-");
            }
            numeroCelulaXLS(row, 2, @ViewBag.Fatura_Atual_ValorTotalN, _2CellStyle);
            numeroCelulaXLS(row, 4, @ViewBag.Fatura_Atual_CustoMedioN, _2CellStyle);

            // mes anterior1
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, @ViewBag.Fatura_Ant1_Data, _negritoCellStyle);
            if (@ViewBag.Fatura_Ant1_BandeiraTexto_fim != "false")
            {
                textoCelulaXLS(row, 1, @ViewBag.Fatura_Ant1_BandeiraTexto_fim);
            }
            else
            {
                textoCelulaXLS(row, 1, "-");
            }
            numeroCelulaXLS(row, 2, @ViewBag.Fatura_Ant1_ValorTotalN, _2CellStyle);
            numeroCelulaXLS(row, 4, @ViewBag.Fatura_Ant1_CustoMedioN, _2CellStyle);

            // mes anterior2
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, @ViewBag.Fatura_Ant2_Data, _negritoCellStyle);
            if (@ViewBag.Fatura_Ant2_BandeiraTexto_fim != "false")
            {
                textoCelulaXLS(row, 1, @ViewBag.Fatura_Ant2_BandeiraTexto_fim);
            }
            else
            {
                textoCelulaXLS(row, 1, "-");
            }
            numeroCelulaXLS(row, 2, @ViewBag.Fatura_Ant2_ValorTotalN, _2CellStyle);
            numeroCelulaXLS(row, 4, @ViewBag.Fatura_Ant2_CustoMedioN, _2CellStyle);

            // largura de cada coluna
            for (int i = 0; i < sheet.GetRow(18).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }
    }
}