﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class FinancasController
    {
        // GET: PisCofins - Distribuidoras
        public ActionResult PisCofins_Distribuidoras()
        {
            // tela de ajuda - piscofins
            CookieStore.SalvaCookie_String("PaginaAjuda", "Financas_Configuracao_PIS_COFINS_Distribuidoras");

            // le cookies
            LeCookies_SmartEnergy();

            // le distribuidoras
            AgentesDistribuidoraMetodos distribuidorasMetodos = new AgentesDistribuidoraMetodos();
            List<AgentesDistribuidoraDominio> listaDistribuidoras = distribuidorasMetodos.ListarTodos();

            // percorre distribuidoras e descobre numero de medicoes associadas
            foreach (AgentesDistribuidoraDominio distribuidora in listaDistribuidoras)
            {
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                int NumMedicoes = medicoesMetodos.NumMedicoesDistribuidora(distribuidora.IDAgenteDistribuidora);

                // numero de medicoes com esta distribuidora
                distribuidora.NumMedicoes = NumMedicoes;
            }

            return View(listaDistribuidoras);
        }

        // GET: PisCofins - Historico
        public ActionResult PisCofins_Historico(int IDAgenteDistribuidora)
        {
            // tela de ajuda - piscofins
            CookieStore.SalvaCookie_String("PaginaAjuda", "Financas_Configuracao_PIS_COFINS_Historico");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le distribuidora
            AgentesDistribuidoraMetodos distribuidorasMetodos = new AgentesDistribuidoraMetodos();
            AgentesDistribuidoraDominio distribuidora = distribuidorasMetodos.ListarPorId(IDAgenteDistribuidora);

            ViewBag.IDAgenteDistribuidora = distribuidora.IDAgenteDistribuidora;
            ViewBag.Sigla = distribuidora.Nome;
            ViewBag.Nome = distribuidora.RazaoSocial;

            // le historico do PisCofins da distribuidora
            PisCofinsMetodos piscofinsMetodos = new PisCofinsMetodos();
            List<PisCofinsDominio> lista = piscofinsMetodos.ListarTodos(IDAgenteDistribuidora);
            return View(lista);
        }

        // GET: PisCofins - Editar
        public ActionResult PisCofins_Editar(int IDAgenteDistribuidora, int IDPisCofins)
        {
            // tela de ajuda - piscofins
            CookieStore.SalvaCookie_String("PaginaAjuda", "Financas_Configuracao_PIS_COFINS_Editar");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le distribuidora
            AgentesDistribuidoraMetodos distribuidorasMetodos = new AgentesDistribuidoraMetodos();
            AgentesDistribuidoraDominio distribuidora = distribuidorasMetodos.ListarPorId(IDAgenteDistribuidora);

            ViewBag.IDAgenteDistribuidora = distribuidora.IDAgenteDistribuidora;
            ViewBag.Sigla = distribuidora.Nome;
            ViewBag.Nome = distribuidora.RazaoSocial;

            // verifica se adicionando
            PisCofinsDominio piscofins = new PisCofinsDominio();
            if (IDPisCofins == 0)
            {
                // zera piscofins com default
                piscofins.IDPisCofins = 0;
                piscofins.IDAgenteDistribuidora = IDAgenteDistribuidora;
                piscofins.Periodo = DateTime.Now;
                piscofins.ValorPisCofins = 0.0;
            }
            else
            {
                // le PisCofins
                PisCofinsMetodos piscofinsMetodos = new PisCofinsMetodos();
                piscofins = piscofinsMetodos.ListarPorId(IDPisCofins);
            }


            return View(piscofins);
        }

        // POST: PisCofins - Salvar
        [HttpPost]
        public ActionResult PisCofins_Salvar(PisCofinsDominio piscofins)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro PisCofins com a mesma data
            PisCofinsMetodos piscofinsMetodos = new PisCofinsMetodos();
            if (piscofinsMetodos.VerificarDuplicidade(piscofins))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Data existente."
                };
            }
            else
            {
                // salva PisCofins
                piscofinsMetodos.Salvar(piscofins);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: ICMS - Excluir
        public ActionResult PisCofins_Excluir(int IDPisCofins)
        {
            // apaga o PisCofins
            PisCofinsMetodos piscofinsMetodos = new PisCofinsMetodos();
            piscofinsMetodos.Excluir(IDPisCofins);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}