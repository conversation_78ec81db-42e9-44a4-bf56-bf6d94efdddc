﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;
using System.Text.RegularExpressions;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class FinancasController
    {

        // GET: Fatura de Energia Eletrica Resumida
        public ActionResult Fatura_Energia_Resumida(int IDCliente, int tipo_arquivo = 0)
        {
            // tipo data (utilizar fechamentos)
            int TipoData = 0;
            CookieStore.SalvaCookie_Int("Fatura_TipoData", TipoData);

            // fatura de energia
            return (Fatura_Energia_Resumida_Show(IDCliente, tipo_arquivo));
        }

        // GET: Fatura Energia Resumida
        private ActionResult Fatura_Energia_Resumida_Show(int IDCliente, int tipo_arquivo = 0)
        {
            // tela de ajuda - fatura
            CookieStore.SalvaCookie_String("PaginaAjuda", "Financas_Fatura_EnergiaResumida");

            // salva cookie 
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Fatura");

            // le cookies
            LeCookies_SmartEnergy();

            // data inicio
            DateTime data_hora_ini = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 0, 0);

            // data fim
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            // le cookie datahora
            DateTime data_hora_ini_cookie = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime data_hora_fim_cookie = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // verifica se existe datahora no cookie
            if (data_hora_ini_cookie.Year != 2000 && data_hora_fim_cookie.Year != 2000)
            {
                // copia data do cookie 
                data_hora_ini = data_hora_ini_cookie;
                data_hora_fim = data_hora_fim_cookie;
            }

            // converte
            DATAHORA dh_ini = new DATAHORA();
            DATAHORA dh_fim = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, data_hora_ini);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, data_hora_fim);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", data_hora_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", data_hora_fim);
            CookieStore.SalvaCookie_Datahora("Relat_Data", data_hora_ini);

            // data e hora atual
            ViewBag.DataIni = string.Format("{0:d}", data_hora_ini);
            ViewBag.HoraIni = string.Format("{0:HH:mm}", data_hora_ini);
            ViewBag.DataFim = string.Format("{0:d}", data_hora_fim);
            ViewBag.HoraFim = string.Format("{0:HH:mm}", data_hora_fim);

            ViewBag.DataTextoAtualIni = string.Format("{0:d} {1:HH:mm}", data_hora_ini, data_hora_ini);
            ViewBag.DataTextoAtualFim = string.Format("{0:d} {1:HH:mm}", data_hora_fim, data_hora_fim);

            ViewBag.DataAtualN = data_hora_ini;
            ViewBag.DataFinalN = data_hora_fim;

            ViewBag.DataMes = string.Format("{0:MM/yyyy}", data_hora_ini);
            ViewBag.DataMesTexto = string.Format("{0:MMMMyyyy}", data_hora_ini);

            return View();
        }

        // GET: Fatura Atualizar
        public ActionResult Fatura_Energia_Resumida_Atualizar(int Navegacao, string DataIni, string DataFim, int TipoData)
        {
            // caso forcar data
            if (Navegacao == 100)
            {
                // pega data
                DateTime dateValueIni = DateTime.Parse(DataIni);
                DateTime dateValueFim = DateTime.Parse(DataFim);

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Fatura_DataIni", dateValueIni);
                CookieStore.SalvaCookie_Datahora("Fatura_DataFim", dateValueFim);
                CookieStore.SalvaCookie_Datahora("Relat_Data", dateValueIni);
            }

            // tipo data
            CookieStore.SalvaCookie_Int("Fatura_TipoData", TipoData);

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;

            // le cookie datahora
            DateTime datahora_cookie_ini = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // verifica se fechamento
            if (TipoData == 0)
            {
                // data fim
                datahora_cookie_fim = datahora_cookie_ini.AddMonths(1);
            }

            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -3:    // mes anterior

                    datahora_cookie_ini = datahora_cookie_ini.AddMonths(-1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(-1);
                    break;

                case 3:    // mes seguinte

                    datahora_cookie_ini = datahora_cookie_ini.AddMonths(1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(1);
                    break;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", datahora_cookie_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", datahora_cookie_fim);
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_cookie_ini);

            // data e hora atual
            ViewBag.DataIni = string.Format("{0:d}", datahora_cookie_ini);
            ViewBag.HoraIni = string.Format("{0:HH:mm}", datahora_cookie_ini);
            ViewBag.DataFim = string.Format("{0:d}", datahora_cookie_fim);
            ViewBag.HoraFim = string.Format("{0:HH:mm}", datahora_cookie_fim);

            ViewBag.DataTextoAtualIni = string.Format("{0:d} {1:HH:mm:ss}", datahora_cookie_ini, datahora_cookie_ini);
            ViewBag.DataTextoAtualFim = string.Format("{0:d} {1:HH:mm:ss}", datahora_cookie_fim, datahora_cookie_fim);

            ViewBag.DataAtualN = datahora_cookie_ini;
            ViewBag.DataFinalN = datahora_cookie_fim;

            ViewBag.DataMes = string.Format("{0:MM/yyyy}", datahora_cookie_ini);
            ViewBag.DataMesTexto = string.Format("{0:MMMMyyyy}", datahora_cookie_ini);

            // converte
            DATAHORA dh_ini = new DATAHORA();
            DATAHORA dh_fim = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, datahora_cookie_ini);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, datahora_cookie_fim);

            // retorna status
            return Json(0, JsonRequestBehavior.AllowGet);
        }

        // GET: Fatura Resumida Print
        public ActionResult Fatura_Energia_Resumida_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Fatura_Energia_Resumida_Show(IDCliente);

            // resultado
            ViewBag.fatura_resultado = lista_fat_resumida.Keys.Contains(id) ? lista_fat_resumida[id] : null;

            // imprime
            return View();
        }

        // GET: Fatura Resumida EMAIL
        public async Task<ActionResult> Fatura_Energia_Resumida_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Fatura_Energia_Resumida_Show(IDCliente);

            // resultado
            ViewBag.fatura_resultado = lista_fat_resumida.Keys.Contains(id) ? lista_fat_resumida[id] : null;

            // data e hora
            DateTime data_atual = DateTime.Now;

            // tiro caracteres especiais do nome
            Regex regex = new Regex("[^a-zA-Z0-9]");
            string novo_nome = regex.Replace(cliente.Nome, "");

            // nome do arquivo
            string nomeArquivo = string.Format("FaturaResumida_{0}_ID{1:000000}_{2}.pdf", novo_nome, IDCliente, ViewBag.DataMesTexto);
            
            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_Fatura_Energia_Resumida_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Landscape,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "FaturaResumidaEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtualIni", ViewBag.DataTextoAtualIni);
            message = message.Replace("ViewBag.DataTextoAtualFim", ViewBag.DataTextoAtualFim);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Fatura Resumida PDF
        public ActionResult Fatura_Energia_Resumida_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Fatura_Energia_Resumida_Show(IDCliente);

            // resultado
            ViewBag.fatura_resultado = lista_fat_resumida.Keys.Contains(id) ? lista_fat_resumida[id] : null;

            // data e hora
            DateTime data_atual = DateTime.Now;

            // tiro caracteres especiais do nome
            Regex regex = new Regex("[^a-zA-Z0-9]");
            string novo_nome = regex.Replace(cliente.Nome, "");

            // nome do arquivo
            string nomeArquivo = string.Format("FaturaResumida_{0}_ID{1:000000}_{2}.pdf", novo_nome, IDCliente, ViewBag.DataMesTexto);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_Fatura_Energia_Resumida_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Landscape,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Fatura Resumida XLS
        public ActionResult Fatura_Energia_Resumida_XLS()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Fatura_Energia_Resumida_Show(IDCliente);

            // resultado
            ViewBag.fatura_resultado = lista_fat_resumida.Keys.Contains(id) ? lista_fat_resumida[id] : null;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_Fatura_Energia_Resumida(IDCliente);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // tiro caracteres especiais do nome
            Regex regex = new Regex("[^a-zA-Z0-9]");
            string novo_nome = regex.Replace(cliente.Nome, "");

            // nome do arquivo
            string nomeArquivo = string.Format("FaturaResumida_{0}_ID{1:000000}_{2}.xls", novo_nome, IDCliente, ViewBag.DataMesTexto);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Fatura Resumida XLS Download
        [HttpGet]
        public virtual ActionResult Fatura_Energia_Resumida_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }


        // estruturas
        private static IDictionary<Guid, int> tasks_fatura_resumida = new Dictionary<Guid, int>();
        private List<FATURA_RESUMIDA> lista_fat_resumida_tmp = new List<FATURA_RESUMIDA>();
        private static IDictionary<Guid, List<FATURA_RESUMIDA>> lista_fat_resumida = new Dictionary<Guid, List<FATURA_RESUMIDA>>();

        public ActionResult Fatura_Energia_Resumida_IniciaCalculo()
        {
            // taskID
            Guid taskId = Guid.NewGuid();
            tasks_fatura_resumida.Add(taskId, 0);

            // salva cookie
            CookieStore.SalvaCookie_GUID("taskID", taskId);

            // le cookies
            LeCookies_SmartEnergy();

            // simulação
            int IDSimulacaoCenario = 0;

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // le cookie datahora
            DateTime datahora_cookie_ini = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // tipo data
            int TipoData = CookieStore.LeCookie_Int("Fatura_TipoData");

            // limpa lista
            lista_fat_resumida_tmp.Clear();

            // task
            Task.Factory.StartNew(() =>
            {
                // le medicoes
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                List<MedicoesDominio> medicoes = medicoesMetodos.ListarTodos(IDCliente, ViewBag._ConfigMed);

                // estruturas

                // lista de erros
                var listaErros = new List<string>();

                // preenche solicitacao
                CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
                config_interface.sweb.id_cliente = IDCliente;

                // estrutura
                RESULT_ENERGIA_FATURA fatura = new RESULT_ENERGIA_FATURA();
                RESULT_ENERGIA_FATURA fatura_sim = new RESULT_ENERGIA_FATURA();

                // barra de progresso
                int total = 0;
                int atual = 0;

                // percorre medicoes
                if (medicoes != null)
                {
                    // le tipos distribuidora
                    ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
                    List<ListaTiposDominio> listatiposDistribuidora = listatiposMetodos.ListarTodos("AgentesDistribuidora");

                    // barra de progresso
                    total = medicoes.Count();

                    // percorre medicoes
                    foreach (MedicoesDominio medicao in medicoes)
                    {
                        // update task progress
                        double progresso = ((double)atual / (double)total) * 100;
                        tasks_fatura_resumida[taskId] = (int)progresso;
                        atual++;

                        // verifica se nao eh energia
                        if (medicao.IDTipoMedicao != TIPO_MEDICAO.ENERGIA && medicao.IDTipoMedicao != TIPO_MEDICAO.ENERGIA_FORMULA)
                        {
                            continue;
                        }

                        // datas
                        DateTime DataIni = datahora_cookie_ini;
                        DateTime DataFim = datahora_cookie_fim;

                        // inicialmente tem fechamento
                        bool tem_fechamento = true;

                        // verifica se deve usar fechamento
                        if (TipoData == 0)
                        {
                            // verifica se medicao real
                            if (medicao.IDTipoMedicao == TIPO_MEDICAO.ENERGIA)
                            {
                                // atualiza fechamentos
                                CONFIG_INTERFACE config_interface_fecha = new CONFIG_INTERFACE();

                                config_interface_fecha.sweb.id_cliente = medicao.IDCliente;
                                config_interface_fecha.sweb.id_medicao = medicao.IDMedicao;
                                config_interface_fecha.sweb.id_gateway = medicao.IDGateway;

                                SmCalcDB_AtualizaFechamentos((char)0, ref config_interface_fecha);
                            }

                            // encontra datas do fechamento do mês solicitado
                            FechamentosMetodos fechamentosMetodos = new FechamentosMetodos();
                            tem_fechamento = fechamentosMetodos.DatasFechamento(medicao.IDGateway, DataIni, ref DataIni, ref DataFim);
                        }

                        // converte datas
                        DATAHORA dh_ini = new DATAHORA();
                        DATAHORA dh_fim = new DATAHORA();
                        Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, DataIni);
                        Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, DataFim);

                        // preenche solicitacao
                        config_interface.sweb.id_medicao = medicao.IDMedicao;
                        config_interface.sweb.id_gateway = medicao.IDGateway;

                        // demanda historica
                        fatura.demanda_hist = 0.0;

                        // calcula valores fatura (sem simulação)
                        int retorno = SmCalcDB_Energia_Fatura((char)0, ref config_interface, IDSimulacaoCenario, (char)medicao.IDContratoMedicao, (char)medicao.IDEstruturaTarifaria, (char)0, ref dh_ini, ref dh_fim, ref fatura, ref fatura_sim);


                        // coloca resultado na lista temporaria
                        FATURA_RESUMIDA fatura_resumida = new FATURA_RESUMIDA();
                        fatura_resumida.DataIni = DataIni;
                        fatura_resumida.DataFim = DataFim;
                        fatura_resumida.IDMedicao = medicao.IDMedicao;
                        fatura_resumida.IDTipoMedicao = medicao.IDTipoMedicao;
                        fatura_resumida.Nome_Medicao = medicao.Nome;
                        fatura_resumida.IDContratoMedicao = medicao.IDContratoMedicao;
                        fatura_resumida.IDEstruturaTarifaria = medicao.IDEstruturaTarifaria;
                        fatura_resumida.retorno = retorno;
                        fatura_resumida.tem_fechamento = tem_fechamento;
                        fatura_resumida.fatura = fatura;

                        // distribuidora
                        fatura_resumida.IDAgenteDistribuidora = medicao.IDAgenteDistribuidora;

                        ListaTiposDominio tipodistribuidora = listatiposDistribuidora.Find(item => item.ID == medicao.IDAgenteDistribuidora);
                        if (tipodistribuidora != null)
                        {
                            fatura_resumida.NomeDistribuidora = tipodistribuidora.Descricao;
                        }
                        else
                        {
                            fatura_resumida.NomeDistribuidora = "---";
                        }

                        // verifica se nao eh energia
                        if (medicao.IDTipoMedicao == TIPO_MEDICAO.ENERGIA)
                        {
                            // analise da medição
                            EN_Metodos enMetodos = new EN_Metodos();
                            fatura_resumida.analise = enMetodos.VerificarPeriodo(medicao.IDCliente, medicao.IDMedicao, DataIni, DataFim);

                            // constante
                            EN_K_Metodos constanteMetodos = new EN_K_Metodos();
                            EN_K_Dominio constante = constanteMetodos.UltimaConstante(medicao.IDCliente, medicao.IDMedicao);
                            double valor_constante = 0.0;

                            if (constante != null)
                            {
                                valor_constante = constante.Constante;
                            }

                            fatura_resumida.constante = valor_constante;
                        }
                        else
                        {
                            // analise da medição
                            fatura_resumida.analise = 0;

                            // constante
                            fatura_resumida.constante = 1.0;
                        }

                        // coloca na lista
                        lista_fat_resumida_tmp.Add(fatura_resumida);
                    }
                }

                // coloca resultado na lista
                lista_fat_resumida.Add(taskId, new List<FATURA_RESUMIDA>(lista_fat_resumida_tmp));

                // terminou
                tasks_fatura_resumida.Remove(taskId);
            });

            // retorna status
            return Json(taskId, JsonRequestBehavior.AllowGet);
        }

        public ActionResult Fatura_Energia_Resumida_Progress(Guid id)
        {
            return Json(tasks_fatura_resumida.Keys.Contains(id) ? tasks_fatura_resumida[id] : 100, JsonRequestBehavior.AllowGet);
        }

        public PartialViewResult _Fatura_Energia_Resumida(Guid id)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // resultado
            ViewBag.fatura_resultado = lista_fat_resumida.Keys.Contains(id) ? lista_fat_resumida[id] : null;

            return PartialView();
        }

        // Fatura Resumida XLS
        private HSSFWorkbook XLS_Fatura_Energia_Resumida(int IDCliente)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            ICellStyle _textoBorderCellStyle = criaEstiloXLS(workbook, 100, true);
            ICellStyle _1CellBorderStyle = criaEstiloXLS(workbook, 1, true);
            ICellStyle _2CellBorderStyle = criaEstiloXLS(workbook, 2, true);

            //
            // MEDICOES
            //

            // IDTipoAcesso
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // cria planilha
            var sheet = workbook.CreateSheet("Fatura Resumida");

            // cabecalho
            string[] cabecalho = { "ID", "Medições", "Resultado", "Início", "Fim", "Estrutura Tarifária", "Distribuidora", "Constante", "Demanda Contrato Ponta (kW)", "Demanda Contrato FPonta (kW)", "Demanda Registrada Ponta (kW)", "Demanda Registrada FPonta (kW)", "Ultrapassagem Ponta (kW)", "Ultrapassagem FPonta (kW)", "Consumo Ponta (kWh)", "Consumo FPonta (kWh)", "Consumo Total (kWh)", "Multa Demanda (R$)", "Multa Fator de Potência (R$)", "Total (R$)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            List<FATURA_RESUMIDA> fatura_resultado = ViewBag.fatura_resultado;
            int i;
            IRow row;

            // percorre valores
            if (fatura_resultado != null)
            {
                foreach (FATURA_RESUMIDA fat in fatura_resultado)
                {
                    // contrato
                    string contrato_medicao = "Cativo";
                                            
                    switch (fat.IDContratoMedicao)
                    {
                        case 1:
                            contrato_medicao = "Livre";
                            break;
                                                    
                        case 2:
                            contrato_medicao = "Rural / Irrigantes";
                            break;
                    }


                    // adiciona linha
                    row = sheet.CreateRow(rowIndex++);

                    // medicao
                    numeroCelulaXLS(row, 0, fat.IDMedicao, _intCellStyle);
                    textoCelulaXLS(row, 1, fat.Nome_Medicao);

                    if (fat.retorno != 0)
                    {
                        // verifica se GESTAL                             
                        if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                        {
                            string DescricaoErro = "---";

                            switch (fat.retorno)
                            {
                                case -1:    // estrutura tarifaria inexistente
                                    DescricaoErro = "Estrutura Tarifária inexistente";
                                    break;

                                case 1:     // nao foi possivel abrir banco de dados
                                    DescricaoErro = string.Format("Banco de Dados EN_{0:000000} inexistente", fat.IDMedicao);
                                    break;

                                case 2:     // nao existem dados
                                    DescricaoErro = string.Format("Não existem dados em EN_{0:000000}", fat.IDMedicao);
                                    break;

                                case 3:     // intervalo menos de 1 dia
                                    DescricaoErro = "Intervalo menor que 1 dia";
                                    break;

                                case 4:     // leitura da configuracao da medicao
                                    DescricaoErro = "Leitura do Histórico do Contrato de Demanda";
                                    break;

                                case 5:     // leitura do historico de contrato de demanda
                                    DescricaoErro = "Leitura do Histórico do Contrato de Demanda";
                                    break;

                                case 6:     // tarifas THS
                                    DescricaoErro = "Leitura da Tarifa THS";
                                    break;

                                case 7:     // tarifa pis/cofins
                                    DescricaoErro = "Leitura da Tarifa PIS/COFINSD";
                                    break;

                                case 8:     // nao existem bandeiras tarifarias
                                    DescricaoErro = "Não existem Bandeiras Tarifárias";
                                    break;

                                case 9:     // leitura da configuracao do feriado
                                    DescricaoErro = "Leitura da Configuracao do Feriado";
                                    break;
                            }

                            textoCelulaXLS(row, 2, string.Format("{0} (Erro {1}) ", DescricaoErro, fat.retorno));
                        }
                        else
                        {
                            textoCelulaXLS(row, 2, "---");
                        }

                        textoCelulaXLS(row, 3, "---");
                        textoCelulaXLS(row, 4, "---");
                        textoCelulaXLS(row, 5, "---");
                        textoCelulaXLS(row, 6, "---");
                        textoCelulaXLS(row, 7, "---");
                        textoCelulaXLS(row, 8, "---");
                        textoCelulaXLS(row, 9, "---");
                        textoCelulaXLS(row, 10, "---");
                        textoCelulaXLS(row, 11, "---");
                        textoCelulaXLS(row, 12, "---");
                        textoCelulaXLS(row, 13, "---");
                        textoCelulaXLS(row, 14, "---");
                        textoCelulaXLS(row, 15, "---");
                        textoCelulaXLS(row, 16, "---");
                        textoCelulaXLS(row, 17, "---");
                        textoCelulaXLS(row, 18, "---");
                        numeroCelulaXLS(row, 19, 0.0, _2CellStyle);
                    }
                    else
                    {
                        string DescricaoErro = "---";

                        // verifica se GESTAL                             
                        if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                        {
                            DescricaoErro = "OK";

                            if (!fat.tem_fechamento)
                            {
                                DescricaoErro = "Não possui Fechamentos";
                            }

                            if (fat.IDTipoMedicao == TIPO_MEDICAO.ENERGIA_FORMULA)
                            {
                                DescricaoErro = "Fórmula";
                            }
                            else
                            {
                                switch (fat.analise)
                                {
                                    case 1:     // 1 - ERRO, nao contem nenhum registro
                                        DescricaoErro = "Não contém registros de demanda";
                                        break;

                                    case 2:     // 2 - ERRO, nao contem a quantidade de registros correta
                                        DescricaoErro = "Quantidade de registros incorreta";
                                        break;

                                    case 3:     // 2 - ERRO, possui registros com zero
                                        DescricaoErro = "Possui demanda com valor em zero";
                                        break;
                                }
                            }
                        }

                        if (fat.IDEstruturaTarifaria == 0)
                        {
                            double Total_Multa_Dem = fat.fatura.ultr_demanda_p[CF.TOT] + fat.fatura.ultr_demanda_fp[CF.TOT] +
                                                   fat.fatura.ultr_demanda_pe_p[CF.TOT] + fat.fatura.ultr_demanda_pe_fp[CF.TOT];

                            double Total_Multa_FatPot = fat.fatura.fdr_p[CF.TOT] + fat.fatura.fdr_fp[CF.TOT] +
                                                   fat.fatura.fdr_pe_p[CF.TOT] + fat.fatura.fdr_pe_fp[CF.TOT] +
                                                   fat.fatura.fer_p[CF.TOT] + fat.fatura.fer_fpi[CF.TOT] + fat.fatura.fer_fpc[CF.TOT];

                            double valor = 0.0;

                            if (fat.fatura.demanda_fpi[CF.REG] > fat.fatura.demanda_fpc[CF.REG])
                            {
                                valor = fat.fatura.demanda_fpi[CF.REG];
                            }
                            else
                            {
                                valor = fat.fatura.demanda_fpc[CF.REG];
                            }

                            textoCelulaXLS(row, 2, DescricaoErro);
                            datahoraCelulaXLS(row, 3, fat.DataIni, _datahoraStyle);
                            datahoraCelulaXLS(row, 4, fat.DataFim, _datahoraStyle);

                            textoCelulaXLS(row, 5, contrato_medicao + " THS Azul");
                            textoCelulaXLS(row, 6, fat.NomeDistribuidora);
                            textoCelulaXLS(row, 7, string.Format("{0:0.00000}", fat.constante));

                            numeroCelulaXLS(row, 8, fat.fatura.contrato_dem_p, _1CellStyle);
                            numeroCelulaXLS(row, 9, fat.fatura.contrato_dem_fp, _1CellStyle);

                            numeroCelulaXLS(row, 10, fat.fatura.demanda_p[CF.REG], _1CellStyle);
                            numeroCelulaXLS(row, 11, valor, _1CellStyle);

                            numeroCelulaXLS(row, 12, fat.fatura.ultr_demanda_p[CF.FAT], _1CellStyle);
                            numeroCelulaXLS(row, 13, fat.fatura.ultr_demanda_fp[CF.FAT], _1CellStyle);

                            numeroCelulaXLS(row, 14, fat.fatura.consumo_p_tusd[CF.REG], _1CellStyle);
                            numeroCelulaXLS(row, 15, fat.fatura.consumo_fpi_tusd[CF.REG] + fat.fatura.consumo_fpc_tusd[CF.REG], _1CellStyle);
                            numeroCelulaXLS(row, 16, fat.fatura.consumo_total_tusd[CF.REG], _1CellStyle);
                            numeroCelulaXLS(row, 17, Total_Multa_Dem, _2CellStyle);
                            numeroCelulaXLS(row, 18, Total_Multa_FatPot, _2CellStyle);
                            numeroCelulaXLS(row, 19, fat.fatura.total, _2CellStyle);
                        }
                        else
                        {
                            double Total_Multa_Dem = fat.fatura.ultr_demanda_p[CF.TOT] + fat.fatura.ultr_demanda_pe_p[CF.TOT];

                            double Total_Multa_FatPot = fat.fatura.fdr_p[CF.TOT] + fat.fatura.fdr_pe_p[CF.TOT] +
                                                   fat.fatura.fer_p[CF.TOT] + fat.fatura.fer_fpi[CF.TOT] + fat.fatura.fer_fpc[CF.TOT];

                            textoCelulaXLS(row, 2, DescricaoErro);
                            datahoraCelulaXLS(row, 3, fat.DataIni, _datahoraStyle);
                            datahoraCelulaXLS(row, 4, fat.DataFim, _datahoraStyle);

                            textoCelulaXLS(row, 5, contrato_medicao + " THS Verde");
                            textoCelulaXLS(row, 6, fat.NomeDistribuidora);
                            textoCelulaXLS(row, 7, string.Format("{0:0.00000}", fat.constante));

                            numeroCelulaXLS(row, 9, fat.fatura.contrato_dem_p, _1CellStyle);
                            numeroCelulaXLS(row, 11, fat.fatura.demanda_p[CF.REG], _1CellStyle);
                            numeroCelulaXLS(row, 13, fat.fatura.ultr_demanda_p[CF.FAT], _1CellStyle);

                            numeroCelulaXLS(row, 14, fat.fatura.consumo_p_tusd[CF.REG], _1CellStyle);
                            numeroCelulaXLS(row, 15, fat.fatura.consumo_fpi_tusd[CF.REG] + fat.fatura.consumo_fpc_tusd[CF.REG], _1CellStyle);
                            numeroCelulaXLS(row, 16, fat.fatura.consumo_total_tusd[CF.REG], _1CellStyle);

                            numeroCelulaXLS(row, 17, Total_Multa_Dem, _2CellStyle);
                            numeroCelulaXLS(row, 18, Total_Multa_FatPot, _2CellStyle);

                            numeroCelulaXLS(row, 19, fat.fatura.total, _2CellStyle);
                        }
                    }
                }
            }

            // largura de cada coluna
            sheet.SetColumnWidth(0, 2000);
            for (i = 1; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS_FatResumida(workbook, sheet, _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(4).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }
    }
}