﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class FinancasController
    {
        // GET: Tarifas de Utilidades - Grupos
        public ActionResult TarifasUtil_Grupos(int IDCliente)
        {
            // tela de ajuda - tarifas
            CookieStore.SalvaCookie_String("PaginaAjuda", "Financas_Configuracao_Tarifas_Utilidades_Grupos");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le grupos
            TarifasUtilGruposMetodos gruposMetodos = new TarifasUtilGruposMetodos();
            List<TarifasUtilGruposDominio> listaGrupos = gruposMetodos.ListarPorIDCliente(IDCliente);

            // percorre grupos e descobre numero de medicoes associadas
            foreach (TarifasUtilGruposDominio grupo in listaGrupos)
            {
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                int NumMedicoes = medicoesMetodos.NumMedicoesTarifaUtilGrupos(grupo.IDGrupoTarifas);

                // numero de medicoes com este grupo
                grupo.NumMedicoes = NumMedicoes;
            }

            return View(listaGrupos);
        }

        // GET: Tarifas de Utilidades - Grupos - Editar
        public ActionResult TarifasUtil_Grupos_Editar(int IDGrupoTarifas)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // verifica se adicionando
            TarifasUtilGruposDominio grupo = new TarifasUtilGruposDominio();
            if (IDGrupoTarifas == 0)
            {
                // zera grupo com default
                grupo.IDGrupoTarifas = 0;
                grupo.IDCliente = ViewBag._IDCliente;
                grupo.Faixa1 = 0;
                grupo.Faixa2 = 0;
                grupo.Faixa3 = 0;
                grupo.Faixa4 = 0;
                grupo.Faixa5 = 0;
            }
            else
            {
                // le grupo
                TarifasUtilGruposMetodos grupoMetodos = new TarifasUtilGruposMetodos();
                grupo = grupoMetodos.ListarPorId(IDGrupoTarifas);
            }

            return View(grupo);
        }

        // POST: Tarifas de Utilidades - Grupos - Salvar
        [HttpPost]
        public ActionResult TarifasUtil_Grupos_Salvar(TarifasUtilGruposDominio grupo)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro grupo com o mesmo nome
            TarifasUtilGruposMetodos grupoMetodos = new TarifasUtilGruposMetodos();
            if (grupoMetodos.VerificarDuplicidade(grupo))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Grupo existente."
                };
            }
            else
            {
                // salva grupo
                grupoMetodos.Salvar(grupo);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Tarifas de Utilidades - Grupos - Excluir
        public ActionResult TarifasUtil_Grupos_Excluir(int IDGrupoTarifas)
        {
            // exclui grupo
            TarifasUtilGruposMetodos grupoMetodos = new TarifasUtilGruposMetodos();
            grupoMetodos.Excluir(IDGrupoTarifas);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}