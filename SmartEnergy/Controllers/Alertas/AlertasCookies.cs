﻿using System;
using System.Collections.Generic;
using System.Linq;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class AlertasController
    {
        private void LeCookies_SmartEnergy()
        {
            // le cookies
            ViewBag._IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");
            ViewBag._IDTipoAcesso = CookieStore.LeCookie_Int("IDTipoAcesso");
            ViewBag._NomeUsuario = CookieStore.LeCookie_String("NomeUsuario");
            ViewBag._EmailUsuario = CookieStore.LeCookie_String("EmailUsuario");
            ViewBag.ShowIntro = CookieStore.LeCookie_String("ShowIntro");
            ViewBag.AceiteLGPD = CookieStore.LeCookie_String("AceiteLGPD");

            ViewBag._IDDashboardGrupo = CookieStore.LeCookie_Int("IDDashboardGrupo");
            ViewBag.View_Analises = CookieStore.LeCookie_Int("View_Analises");
            ViewBag.View_Financas = CookieStore.LeCookie_Int("View_Financas");
            ViewBag.View_KPI = CookieStore.LeCookie_Int("View_KPI");
            ViewBag.View_Ranking = CookieStore.LeCookie_Int("View_Ranking");
            ViewBag.View_Rateio = CookieStore.LeCookie_Int("View_Rateio");
            ViewBag.View_Metas = CookieStore.LeCookie_Int("View_Metas");
            ViewBag.View_Configuracao = CookieStore.LeCookie_Int("View_Configuracao");
            ViewBag.View_ConfiguracaoRemota = CookieStore.LeCookie_Int("View_ConfiguracaoRemota");
            ViewBag.View_PerfilUsuario = CookieStore.LeCookie_Int("View_PerfilUsuario");

            ViewBag._IDCliente = CookieStore.LeCookie_Int("_IDCliente");
            ViewBag._IDTipoContrato = CookieStore.LeCookie_Int("IDTipoContrato");
            ViewBag._IDTipoSupervisao = CookieStore.LeCookie_Int("IDTipoSupervisao");
            ViewBag._IDConsultor = CookieStore.LeCookie_Int("IDConsultor");
            ViewBag._LogoConsultor = CookieStore.LeCookie_String("LogoConsultor");
            ViewBag.Nome_GrupoUnidades = CookieStore.LeCookie_String("Nome_GrupoUnidades");
            ViewBag.Nome_Unidades = CookieStore.LeCookie_String("Nome_Unidades");
            ViewBag._IDTipoGrafico_Demanda = CookieStore.LeCookie_Int("IDTipoGrafico_Demanda");

            ViewBag._IDMedicao = CookieStore.LeCookie_Int("_IDMedicao");
            ViewBag._IDTipoMedicao = CookieStore.LeCookie_Int("IDTipoMedicao");
            ViewBag.GrupoNome = CookieStore.LeCookie_String("GrupoNome");
            ViewBag.UnidadeNome = CookieStore.LeCookie_String("UnidadeNome");
            ViewBag.MedicaoNome = CookieStore.LeCookie_String("MedicaoNome");

            ViewBag._IDEmpresa = CookieStore.LeCookie_Int("_IDEmpresa");

            ViewBag._IDGateway = CookieStore.LeCookie_Int("_IDGateway");
            ViewBag._IDTipoGateway = CookieStore.LeCookie_Int("IDTipoGateway");

            ViewBag.TipoPaginaAtual = CookieStore.LeCookie_String("TipoPaginaAtual");
            ViewBag.PaginaAjuda = CookieStore.LeCookie_String("PaginaAjuda");
            ViewBag.Relat_Tipo = CookieStore.LeCookie_Int("Relat_Tipo");
            ViewBag.Relat_TipoPeriodo = CookieStore.LeCookie_Int("Relat_TipoPeriodo");

            ViewBag.Relat_SemanalDomingo = CookieStore.LeCookie_Int("Relat_SemanalDomingo");
            ViewBag.Relat_SemanalSegunda = CookieStore.LeCookie_Int("Relat_SemanalSegunda");
            ViewBag.Relat_SemanalTerca = CookieStore.LeCookie_Int("Relat_SemanalTerca");
            ViewBag.Relat_SemanalQuarta = CookieStore.LeCookie_Int("Relat_SemanalQuarta");
            ViewBag.Relat_SemanalQuinta = CookieStore.LeCookie_Int("Relat_SemanalQuinta");
            ViewBag.Relat_SemanalSexta = CookieStore.LeCookie_Int("Relat_SemanalSexta");
            ViewBag.Relat_SemanalSabado = CookieStore.LeCookie_Int("Relat_SemanalSabado");

            ViewBag.Relat_SemanalMax = CookieStore.LeCookie_Int("Relat_SemanalMax");
            ViewBag.Relat_SemanalMed = CookieStore.LeCookie_Int("Relat_SemanalMed");
            ViewBag.Relat_SemanalMin = CookieStore.LeCookie_Int("Relat_SemanalMin");

            ViewBag.Relat_Search = Server.UrlDecode(CookieStore.LeCookie_String("Relat_Search"));
            ViewBag.Relat_SortedCol = CookieStore.LeCookie_Int("Relat_SortedCol");
            ViewBag.Relat_SortedDir = CookieStore.LeCookie_String("Relat_SortedDir");
            ViewBag.Relat_TipoEvento = CookieStore.LeCookie_String("Relat_TipoEvento");

            ViewBag._IDSimulacaoCenario = CookieStore.LeCookie_Int("_IDSimulacaoCenario");
            ViewBag.AplicaSimulacao = CookieStore.LeCookie_Bool("AplicaSimulacao");

            // configmed
            string ConfigMed = CookieStore.LeCookie_String("ConfigMed");
            List<int> ConfigMedList = null;

            // copia medicoes para lista
            if (!String.IsNullOrEmpty(ConfigMed))
            {
                ConfigMedList = ConfigMed.Split('/')
                    .Select(possibleIntegerAsString =>
                    {
                        int parsedInteger = 0;
                        bool isInteger = int.TryParse(possibleIntegerAsString, out parsedInteger);
                        return new { isInteger, parsedInteger };
                    })
                    .Where(tryParseResult => tryParseResult.isInteger)
                    .Select(tryParseResult => tryParseResult.parsedInteger)
                    .ToList();
            }

            ViewBag._ConfigMed = ConfigMedList;

            // configcli
            string ConfigCli = CookieStore.LeCookie_String("ConfigCli");
            List<int> ConfigCliList = null;

            // copia medicoes para lista
            if (!String.IsNullOrEmpty(ConfigCli))
            {
                ConfigCliList = ConfigCli.Split('/')
                    .Select(possibleIntegerAsString =>
                    {
                        int parsedInteger = 0;
                        bool isInteger = int.TryParse(possibleIntegerAsString, out parsedInteger);
                        return new { isInteger, parsedInteger };
                    })
                    .Where(tryParseResult => tryParseResult.isInteger)
                    .Select(tryParseResult => tryParseResult.parsedInteger)
                    .ToList();
            }

            // protege contra consultor sem cliente
            // caso for usuário consultor e não tiver cliente associado, forço um cliente inexistente para não apresentar todos os clientes para ele.
            int IDTipoAcesso = CookieStore.LeCookie_Int("IDTipoAcesso");

            if (isUser.isConsultor(IDTipoAcesso))
            {
                bool erro = false;

                if (ConfigCliList != null)
                {
                    if (ConfigCliList.Count == 0)
                    {
                        // erro, não pode ser consultor e não ter cliente
                        erro = true;
                    }
                }
                else
                {
                    // erro, não pode ser consultor e não ter cliente
                    erro = true;
                }

                if (erro)
                {
                    // insiro cliente inexistente
                    List<int> CfgCli = new List<int>();
                    int cliente_inexistente = -1;
                    CfgCli.Add(cliente_inexistente);
                    ConfigCliList = CfgCli;
                }
            }

            ViewBag._ConfigCli = ConfigCliList;

            return;
        }
    }
}