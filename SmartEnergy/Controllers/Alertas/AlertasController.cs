﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class AlertasController : Controller
    {
        // GET: Alertas
        public ActionResult ShowAlerts()
        {
            // tela de ajuda - alertas
            ViewBag.PaginaAjuda = "Contato";

            // le cookies
            LeCookies_SmartEnergy();

            // IDUsuario
            int IDUsuario = ViewBag._IDUsuario;

            // le alertas do usuario
            UsuarioAlertaMetodos usuarioAlertaMetodos = new UsuarioAlertaMetodos();
            List<UsuarioAlertaDominio> usuarioAlertas = usuarioAlertaMetodos.ListarPorId(IDUsuario);

            // alertas nao lidas
            int NumAlertasNaoLidas = usuarioAlertaMetodos.NumAlertas_NaoLidas(IDUsuario);
            ViewBag.NumAlertasNaoLidas = NumAlertasNaoLidas;

            return View(usuarioAlertas);
        }

        // GET: Alerta - Reconhecer
        public ActionResult Alerta_Reconhecer(int IDUsuarioAlerta)
        {
            // reconhece o alerta
            UsuarioAlertaMetodos usuarioAlertaMetodos = new UsuarioAlertaMetodos();
            usuarioAlertaMetodos.Reconhecer(IDUsuarioAlerta);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // GET: Alerta - Reconhecer Todos
        public ActionResult Alerta_ReconhecerTodos()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDUsuario
            int IDUsuario = ViewBag._IDUsuario;

            // reconhece o alerta
            UsuarioAlertaMetodos usuarioAlertaMetodos = new UsuarioAlertaMetodos();
            usuarioAlertaMetodos.ReconhecerTodos(IDUsuario);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // GET: Alerta - Excluir
        public ActionResult Alerta_Excluir(int IDUsuarioAlerta)
        {
            // excluir o alerta
            UsuarioAlertaMetodos usuarioAlertaMetodos = new UsuarioAlertaMetodos();
            usuarioAlertaMetodos.Excluir(IDUsuarioAlerta);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // GET: Alerta - Excluir Todos
        public ActionResult Alerta_ExcluirTodos()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDUsuario
            int IDUsuario = ViewBag._IDUsuario;

            // excluir o alerta
            UsuarioAlertaMetodos usuarioAlertaMetodos = new UsuarioAlertaMetodos();
            usuarioAlertaMetodos.ExcluirTodos(IDUsuario);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}