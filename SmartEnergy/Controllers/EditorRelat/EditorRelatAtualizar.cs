﻿using System;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class EditorRelatController : Controller
    {

        // GET: Editor Relatório Atualizar
        public PartialViewResult _ER_Atualizar(int IDEditorRelat, int Navegacao, string DataAtualTxt)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // data atual
            DateTime datahora_ultima = DateTime.Now;

            // le cookie datahora
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            if (datahora_cookie.Year != 2000)
            {
                datahora_ultima = datahora_cookie;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", datahora_ultima);

            // le editor de relatórios
            var editorRelatMetodos = new EditorRelatMetodos();
            var itemEditorRelat = editorRelatMetodos.ListarPorId(IDEditorRelat);

            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("ER_Editar");
            ViewBag.Editar = editar_cookie;

            // verifica se modelo vazio
            if (itemEditorRelat.IDTipoEditorRelat == 0)
            {
                return PartialView(itemEditorRelat);
            }

            // medicao
            if( itemEditorRelat.IDMedicao <= 0)
            {
                // verifica se nao tem medicao atual
                if (ViewBag._IDMedicao <= 0 )
                {
                    // erro
                    ER_Atualizar_Erro(itemEditorRelat.IDMedicao);

                    // retorna sem atualizar
                    return PartialView(itemEditorRelat);
                }

                // utiliza medicao atual
                itemEditorRelat.IDMedicao = ViewBag._IDMedicao;
            }

            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(itemEditorRelat.IDMedicao);

            if( medicao == null)
            {
                // erro
                ER_Atualizar_Erro(itemEditorRelat.IDMedicao);

                // retorna sem atualizar
                return PartialView(itemEditorRelat);
            }

            // verifica se medicao de energia eletrica
            if( medicao.IDTipoMedicao == TIPO_MEDICAO.ENERGIA || medicao.IDTipoMedicao == TIPO_MEDICAO.ENERGIA_FORMULA)
            {
                // verifica tipo editor de relatorio
                switch (itemEditorRelat.IDTipoEditorRelat)
                {
                    case 1:     // demanda

                        ER_Atualizar_Demanda_Media_Mes(IDEditorRelat, itemEditorRelat.IDMedicao, datahora_ultima);
                        break;
                }
            }

            // verifica se nao atualizou por erro
            if( ViewBag.NomeMedicao == null )
            {
                // erro
                ER_Atualizar_Erro(itemEditorRelat.IDMedicao);
            }

            return PartialView(itemEditorRelat);
        }

        private void ER_Atualizar_Erro(int IDMedicao)
        {
            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("ER_Editar");
            ViewBag.Editar = editar_cookie;

            // nome da medicao
            if( IDMedicao <= 0 )
            {
                ViewBag.NomeMedicao = "Selecionar a Medição";
            }
            else
            {
                ViewBag.NomeMedicao = medicao.Nome;
            }

            // converte data e hora
            DateTime Datahora = DateTime.Now;
            ViewBag.Datahora = string.Format("{0:g}", Datahora);

            // indica erro
            bool erro = true;
            ViewBag.ErroEditorRelat = erro;

            return;
        }

    }
}