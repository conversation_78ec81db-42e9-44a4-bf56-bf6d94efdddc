﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class EditorRelatController
    {
        // GET: Editor Relatorios Clientes
        public ActionResult ER_Clientes()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // tela de ajuda - editores relatorios clientes
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "EditorRelat");

            // le cookies
            LeCookies_SmartEnergy();

            // verifica se nao eh admin
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            if (IDTipoAcesso == TIPO_ACESSO.CLIENTE_ADMIN || IDTipoAcesso == TIPO_ACESSO.CLIENTE_OPER)
            {
                return (Redirect("~/Login/Login"));
            }

            // lista de clientes
            List<int> ConfigCliList = ViewBag._ConfigCli;

            // le clientes
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clientesMetodos.ListarTodos(ConfigCliList);

            // percorre clientes e descobre numero de grupos de painéis
            foreach (ClientesDominio cliente in listaClientes)
            {
                // le grupos de painéis do cliente
                EditorRelatGrupoMetodos editorRelatGrupoMetodos = new EditorRelatGrupoMetodos();
                int NumGruposPaginas = editorRelatGrupoMetodos.NumGruposPaginasCliente(cliente.IDCliente);
                cliente.NumGruposPaginas = NumGruposPaginas;
            }

            return View(listaClientes);
        }

        // GET: Editor Relatórios Cliente - Grupos de Páginas
        public ActionResult ER_Cliente_GruposPaginas(int IDCliente)
        {
            // salva cookie
            CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);

            // le contrato cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cli = clienteMetodos.ListarPorId(IDCliente);
            CookieStore.SalvaCookie_Int("IDTipoContrato", cli.IDTipoContrato);
            CookieStore.SalvaCookie_Int("IDTipoSupervisao", cli.IDTipoSupervisao);
            CookieStore.SalvaCookie_String("Nome_GrupoUnidades", cli.Nome_GrupoUnidades);
            CookieStore.SalvaCookie_String("Nome_Unidades", cli.Nome_Unidades);
            CookieStore.SalvaCookie_Int("IDTipoGrafico_Demanda", cli.IDTipoGrafico_Demanda);

            // tela de ajuda - editores relatorio clientes
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "EditorRelat");

            // le cookies
            LeCookies_SmartEnergy();

            // le grupos de painéis
            EditorRelatGrupoMetodos editorRelatGrupoMetodos = new EditorRelatGrupoMetodos();
            List<EditorRelatGrupoDominio> listaEditorRelatGrupo = editorRelatGrupoMetodos.ListarPorIdCliente(IDCliente);

            // percorre grupos de painéis e descobre numero de painéis e blocos
            foreach (EditorRelatGrupoDominio grupo in listaEditorRelatGrupo)
            {
                // le painéis do grupo
                EditorRelatPageMetodos editorRelatPageMetodos = new EditorRelatPageMetodos();
                int NumPaginas = editorRelatPageMetodos.NumPaginas(grupo.IDEditorRelatGrupo, DB_REFERENCIA.IDDashboardGrupo);
                grupo.NumPaginas = NumPaginas;

                // le blocos do grupo
                EditorRelatMetodos editorRelatMetodos = new EditorRelatMetodos();
                int NumBlocos = editorRelatMetodos.NumBlocos(grupo.IDEditorRelatGrupo, DB_REFERENCIA.IDDashboardGrupo);
                grupo.NumBlocos = NumBlocos;
            }

            return View(listaEditorRelatGrupo);
        }

        // GET: Editores Relatórios Cliente - Grupos de Paginas - Editar
        public ActionResult ER_Cliente_GrupoPaginas_Editar(int IDEditorRelatGrupo)
        {
            // tela de ajuda - editor de relatórios
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "EditorRelat");

            // le cookies
            LeCookies_SmartEnergy();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // verifica se adicionando
            EditorRelatGrupoDominio grupo = new EditorRelatGrupoDominio();
            if (IDEditorRelatGrupo == 0)
            {
                // zera grupounidades com default
                grupo.IDCliente = ViewBag._IDCliente;
                grupo.IDConsultor = IDConsultor;
            }
            else
            {
                // le grupo
                EditorRelatGrupoMetodos editorRelatGrupoMetodos = new EditorRelatGrupoMetodos();
                grupo = editorRelatGrupoMetodos.ListarPorId(IDEditorRelatGrupo);
            }

            return View(grupo);
        }

        // POST: Editor Relatórios Cliente - Grupos de Paginas - Salvar
        [HttpPost]
        public ActionResult ER_Cliente_GrupoPaginas_Salvar(EditorRelatGrupoDominio grupo)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro grupo com o mesmo nome
            EditorRelatGrupoMetodos editorRelatGrupoMetodos = new EditorRelatGrupoMetodos();
            if (editorRelatGrupoMetodos.VerificarDuplicidade(grupo))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Grupo de Painéis existente."
                };
            }
            else
            {
                // salva grupo de painéis
                editorRelatGrupoMetodos.Salvar(grupo);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Editor Relatórios Cliente - Grupos de Paginas - Excluir
        public ActionResult ER_Cliente_GrupoPaginas_Excluir(int IDEditorRelatGrupo)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // apaga o grupo paginas
            EditorRelatGrupoMetodos editorRelatGrupoMetodos = new EditorRelatGrupoMetodos();
            editorRelatGrupoMetodos.Excluir(IDEditorRelatGrupo);

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }
    }
}