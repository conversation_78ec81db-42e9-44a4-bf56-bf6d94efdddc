﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class EditorRelatController : Controller
    {
        // Editor de Relatórios prepara
        private Editor_EditorRelatPageDominio ER_Superv_Prepara(int IDReferencia, int TipoReferencia)
        {
            // leio todas as paginas do usuario
            EditorRelatPageMetodos editoresRelatPageMetodos = new EditorRelatPageMetodos();
            List<EditorRelatPageDominio> listaEditoresRelatPage = new List<EditorRelatPageDominio>();
            listaEditoresRelatPage = editoresRelatPageMetodos.ListarPorIdReferencia(IDReferencia, TipoReferencia);

            // numero da pagina
            int PageNumber = CookieStore.LeCookie_Int("PageNumberEditorRelat");
            if (PageNumber < 0) PageNumber = 0;

            if (listaEditoresRelatPage != null && listaEditoresRelatPage.Count() > 0)
            {
                if (!listaEditoresRelatPage.Exists(page => page.PageNumber == PageNumber))
                {
                    PageNumber = listaEditoresRelatPage.FirstOrDefault().PageNumber;
                }
            }

            CookieStore.SalvaCookie_Int("PageNumberEditorRelat", PageNumber);
            ViewBag.PageNumber = PageNumber;

            // leio todos os editores de reatórios do usuario
            EditorRelatMetodos editoresRelatMetodos = new EditorRelatMetodos();
            List<EditorRelatDominio> listaEditoresRelat = new List<EditorRelatDominio>();
            listaEditoresRelat = editoresRelatMetodos.ListarPorIdReferencia(IDReferencia, TipoReferencia);

            // le cookie excluir cookies do editor
            bool excluir_cookie = CookieStore.LeCookie_Bool("ER_Excluir_DataHora");

            // completo editor com nome da pagina
            int contador;
            int contador2;

            // zero contador de editor das paginas
            for (contador2 = 0; contador2 < listaEditoresRelatPage.Count(); contador2++)
            {
                listaEditoresRelatPage[contador2].numEditorRelat = 0;
            }

            // percorre lista editores
            for (contador = 0; contador < listaEditoresRelat.Count(); contador++)
            {
                // percorre lista das paginas
                for (contador2 = 0; contador2 < listaEditoresRelatPage.Count(); contador2++)
                {
                    // caso achou a pagina, copio titulo
                    if (listaEditoresRelat[contador].IDEditorRelatPage == listaEditoresRelatPage[contador2].IDEditorRelatPage && listaEditoresRelat[contador].IDTipoEditorRelat != 0)
                    {
                        listaEditoresRelat[contador].PageTitle = listaEditoresRelatPage[contador2].Title;
                        listaEditoresRelatPage[contador2].numEditorRelat++;
                        break;
                    }
                }

                // verifica se deve excluir cookie
                if (excluir_cookie)
                {
                    // apaga cookie datahora
                    string nome_cookie = string.Format("ER{0}", listaEditoresRelat[contador].IDEditorRelat);
                    CookieStore.DeleteCookie(nome_cookie);
                }
            }

            // salva cookie excluir cookies do editor
            CookieStore.SalvaCookie_Bool("ER_Excluir_DataHora", false);

            // copia para modelo agrupado
            Editor_EditorRelatPageDominio editor = new Editor_EditorRelatPageDominio();
            editor.EditorRelatPage = listaEditoresRelatPage;
            editor.EditorRelat = listaEditoresRelat;

            return editor;
        }

        // GET: Editor de Relatórios modo Painel - Usuario
        public ActionResult ER_Superv(int IDUsuario, int IDEditorRelatGrupo = 0, int IDCliente = 0, int IDMedicao = 0)
        {
            // verifica se enviou IDCliente e IDMedicao
            if (IDCliente > 0 && IDMedicao > 0)
            {
                // le configuracao da medicao
                MedicoesMetodos medicoesMetodos_conf = new MedicoesMetodos();
                MedicoesDominio medicao_conf = medicoesMetodos_conf.ListarPorId(IDMedicao);
            
                // salva cookie 
                CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
                CookieStore.SalvaCookie_Int("IDTipoMedicao", medicao_conf.IDTipoMedicao);
            }

            // le cookies
            LeCookies_SmartEnergy();

            // data atual
            DateTime datahora_ultima = DateTime.Now;

            // le cookie datahora
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            if (datahora_cookie.Year != 2000)
            {
                datahora_ultima = datahora_cookie;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", datahora_ultima);

            // data atual
            ViewBag.DataTextoAtual = string.Format("{0:D}", datahora_ultima);

            // le configuracao cliente
            int _IDCliente = 0;
            _IDCliente = ViewBag._IDCliente;

            // verifica se leu id
            if (_IDCliente > 0)
            {
                ClientesMetodos clientesMetodos = new ClientesMetodos();
                ClientesDominio cliente = clientesMetodos.ListarPorId(_IDCliente);
                ViewBag.ClienteNome = cliente.Nome;
            }

            // tela de ajuda - editor clientes
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // tela editor de relatórios
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Editor_Relat");

            // modo painel
            CookieStore.SalvaCookie_Bool("ER_Editar", false);


            // IDReferencia
            int IDReferencia = IDUsuario;
            int TipoReferencia = DB_REFERENCIA.IDUsuario;

            if (IDEditorRelatGrupo > 0)
            {
                IDReferencia = IDEditorRelatGrupo;
                TipoReferencia = DB_REFERENCIA.IDDashboardGrupo;
            }
            else
            {
                // le usuário
                UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
                UsuarioDominio usuario = usuarioMetodos.ListarPorId(IDUsuario);

                // verifica se usuário quer dashboard dele ou do modelo
                if (usuario != null)
                {
                    if (usuario.IDEditorRelatGrupo > 0)
                    {
                        IDReferencia = usuario.IDEditorRelatGrupo;
                        TipoReferencia = DB_REFERENCIA.IDDashboardGrupo;
                    }

                    // atualiza IDDashboardGrupo
                    IDEditorRelatGrupo = usuario.IDEditorRelatGrupo;
                }
            }

            // atualiza cookie
            CookieStore.SalvaCookie_Int("IDEditorRelatGrupo", IDEditorRelatGrupo);
            ViewBag._IDEditorRelatGrupo = IDEditorRelatGrupo;

            // nome do grupo de paineis
            string NomeEditorRelatGrupo = "";

            if (IDEditorRelatGrupo > 0)
            {
                // le grupo
                EditorRelatGrupoMetodos grupoMetodos = new EditorRelatGrupoMetodos();
                EditorRelatGrupoDominio grupo = grupoMetodos.ListarPorId(IDEditorRelatGrupo);

                if (grupo != null)
                {
                    NomeEditorRelatGrupo = grupo.Nome;
                }
            }

            ViewBag.NomeEditorRelatGrupo = NomeEditorRelatGrupo;         

            return View(ER_Superv_Prepara(IDReferencia, TipoReferencia));
        }

        // GET: Editor Relatórios Atualizar supervisao
        public PartialViewResult _ER_Superv_Atualizar(int IDUsuario, int IDEditorRelatGrupo, int Navegacao, string Data)
        {
            // calcula
            ER_Superv_Show(IDUsuario, IDEditorRelatGrupo, Navegacao, Data);

            // IDReferencia
            int IDReferencia = IDUsuario;
            int TipoReferencia = DB_REFERENCIA.IDUsuario;

            if (IDEditorRelatGrupo > 0)
            {
                IDReferencia = IDEditorRelatGrupo;
                TipoReferencia = DB_REFERENCIA.IDDashboardGrupo;
            }
            else
            {
                // le usuário
                UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
                UsuarioDominio usuario = usuarioMetodos.ListarPorId(IDUsuario);

                // verifica se usuário quer dashboard dele ou do modelo
                if (usuario != null)
                {
                    if (usuario.IDEditorRelatGrupo > 0)
                    {
                        IDReferencia = usuario.IDEditorRelatGrupo;
                        TipoReferencia = DB_REFERENCIA.IDDashboardGrupo;
                    }

                    // atualiza IDDashboardGrupo
                    IDEditorRelatGrupo = usuario.IDEditorRelatGrupo;
                }
            }

            return PartialView(ER_Superv_Prepara(IDReferencia, TipoReferencia));
        }

        // GET: Editor Relatórios Show
        public void ER_Superv_Show(int IDUsuario, int IDEditorRelatGrupo, int Navegacao, string Data)
        {
            // caso forcar data
            if (Navegacao == 100)
            {
                // pega data
                DateTime dateValue = DateTime.Parse(Data);

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Relat_Data", dateValue);
            }

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;

            // verifica se leu id
            if (IDCliente > 0)
            {
                ClientesMetodos clientesMetodos = new ClientesMetodos();
                ClientesDominio cliente = clientesMetodos.ListarPorId(IDCliente);
                ViewBag.ClienteNome = cliente.Nome;
            }


            // numero da pagina
            int PageNumber = CookieStore.LeCookie_Int("PageNumberEditorRelat");
            if (PageNumber < 0) PageNumber = 0;
            CookieStore.SalvaCookie_Int("PageNumberEditorRelat", PageNumber);
            ViewBag.PageNumber = PageNumber;

            // atualiza cookie
            CookieStore.SalvaCookie_Int("IDEditorRelatGrupo", IDEditorRelatGrupo);
            ViewBag._IDEditorRelatGrupo = IDEditorRelatGrupo;

            // nome do grupo de paineis
            string NomeEditorRelatGrupo = "";

            if (IDEditorRelatGrupo > 0)
            {
                // le grupo
                EditorRelatGrupoMetodos grupoMetodos = new EditorRelatGrupoMetodos();
                EditorRelatGrupoDominio grupo = grupoMetodos.ListarPorId(IDEditorRelatGrupo);

                if (grupo != null)
                {
                    NomeEditorRelatGrupo = grupo.Nome;
                }
            }

            ViewBag.NomeEditorRelatGrupo = NomeEditorRelatGrupo;   

            // deifini hora como ultimo registro do dia (23:59) 
            DateTime datahora_relat = DateTime.Now;

            // le cookie datahora (atual ou inicial)
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            // verifica se existe datahora no cookie
            if (datahora_cookie.Year != 2000)
            {
                // copia data do cookie para ultima
                datahora_relat = datahora_cookie;
            }

            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -1:    // dia anterior

                    datahora_relat = datahora_relat.AddDays(-1);
                    break;

                case 1:    // dia seguinte

                    datahora_relat = datahora_relat.AddDays(1);
                    break;

                case -3:    // mes anterior

                    datahora_relat = datahora_relat.AddMonths(-1);
                    break;

                case 3:    // mes seguinte

                    datahora_relat = datahora_relat.AddMonths(1);
                    break;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_relat);

            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", datahora_relat);

            // data atual
            ViewBag.DataTextoAtual = string.Format("{0:D}", datahora_relat);    

            return;
        }
    }
}