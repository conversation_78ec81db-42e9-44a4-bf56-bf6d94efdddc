﻿using System;
using System.Runtime.InteropServices;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{

    public partial class EnviaRelatoriosController
    {
        // Relatorio Diario
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Ciclometro_RelatDiario", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Ciclometro_RelatDiario(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, ref DATAHORA pdata_equipo, ref RELAT_CICLOMETRO_DIARIO prelatorio, ref RELAT_CICLOMETRO_DIARIO_ANALISE panalise, ref GRANDEZA pgrandeza);

        // Relatorio Semanal
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Ciclometro_RelatSemanal", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Ciclometro_RelatSemanal(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, ref DATAHORA pdata_equipo, ref RELAT_CICLOMETRO_SEMANAL prelatorio, ref RELAT_CICLOMETRO_SEMANAL_ANALISE panalise, ref GRANDEZA pgrandeza);

        // Relatorio Mensal
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Ciclometro_RelatMensal", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Ciclometro_RelatMensal(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, ref DATAHORA pdata_equipo, ref RELAT_CICLOMETRO_MENSAL prelatorio, ref RELAT_CICLOMETRO_MENSAL_ANALISE panalise, ref GRANDEZA pgrandeza);


        // Ciclometro Diario
        private int Ciclometro_Diario(CliGrupoUnidMedicoesDominio medicao, DateTime dataRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_CICLOMETRO_DIARIO relatorio = new RELAT_CICLOMETRO_DIARIO();
            RELAT_CICLOMETRO_DIARIO_ANALISE analise = new RELAT_CICLOMETRO_DIARIO_ANALISE();
            GRANDEZA grandeza = new GRANDEZA();

            // preenche solicitacao
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = medicao.IDMedicao;

            // converte data hora
            DATAHORA data_hora = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, dataRelat);

            // calcula valores
            retorno = SmCalcDB_Ciclometro_RelatDiario((char)0, ref config_interface, (char)0, ref data_hora, ref relatorio, ref analise, ref grandeza);

            ViewBag.Retorno = retorno;

            // verifica se erro
            if (retorno != 0)
            {
                // erro
                // -2 - erro : tipo de interface indefinida
                // -1 - erro : tipo de relatorio indefinido
                // 0  - ok e com dados
                // 1  - erro : nao foi possivel abrir o banco de dados
                // 2  - erro : nao existem dados
                // 3  - erro : leitura da configuracao da medicao
                // 4  - erro : leitura das tarifas de energia
                return (retorno);
            }

            // grafico
            var Consumo = new double[26];
            var Dias = new string[26];
            var DatasN = new DateTime[26];
            var Horas = new string[26];

            var Valor_CicloIni = new string[26];
            var Hora_CicloIni = new string[26];

            var Valor_CicloFim = new string[26];
            var Hora_CicloFim = new string[26];

            double Consumo_min_grafico = 0.0;
            double Consumo_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Horas[i] = strData.ToString("HH:mm");

                // proxima hora
                strData = strData.AddHours(1);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    Consumo[i] = relatorio.registro[0].consumo;

                    if (relatorio.registro[0].valor_ciclo_ini < 0.0)
                    {
                        Consumo[i] = 0.0;
                        Valor_CicloIni[i] = "--------";
                        Hora_CicloIni[i] = "(--:--)";
                    }
                    else
                    {
                        Valor_CicloIni[i] = string.Format("{0:00000000}", relatorio.registro[0].valor_ciclo_ini);
                        Hora_CicloIni[i] = string.Format("({0:00}:{1:00})", (int)relatorio.registro[0].datahora_ciclo_ini.hora.hora, (int)relatorio.registro[0].datahora_ciclo_ini.hora.min);
                    }

                    if (relatorio.registro[0].valor_ciclo_fim < 0.0)
                    {
                        Consumo[i] = 0.0;
                        Valor_CicloFim[i] = "--------";
                        Hora_CicloFim[i] = "(--:--)";
                    }
                    else
                    {
                        Valor_CicloFim[i] = string.Format("{0:00000000}", relatorio.registro[0].valor_ciclo_fim);
                        Hora_CicloFim[i] = string.Format("({0:00}:{1:00})", (int)relatorio.registro[0].datahora_ciclo_fim.hora.hora, (int)relatorio.registro[0].datahora_ciclo_fim.hora.min);
                    }
                }

                // verifica se ultima barra
                if (i == 25)
                {
                    // zera
                    Consumo[i] = relatorio.registro[23].consumo;

                    if (relatorio.registro[23].valor_ciclo_ini < 0.0)
                    {
                        Consumo[i] = 0.0;
                        Valor_CicloIni[i] = "--------";
                        Hora_CicloIni[i] = "(--:--)";
                    }
                    else
                    {
                        Valor_CicloIni[i] = string.Format("{0:00000000}", relatorio.registro[23].valor_ciclo_ini);
                        Hora_CicloIni[i] = string.Format("({0:00}:{1:00})", (int)relatorio.registro[23].datahora_ciclo_ini.hora.hora, (int)relatorio.registro[23].datahora_ciclo_ini.hora.min);
                    }

                    if (relatorio.registro[23].valor_ciclo_fim < 0.0)
                    {
                        Consumo[i] = 0.0;
                        Valor_CicloFim[i] = "--------";
                        Hora_CicloFim[i] = "(--:--)";
                    }
                    else
                    {
                        Valor_CicloFim[i] = string.Format("{0:00000000}", relatorio.registro[23].valor_ciclo_fim);
                        Hora_CicloFim[i] = string.Format("({0:00}:{1:00})", (int)relatorio.registro[23].datahora_ciclo_fim.hora.hora, (int)relatorio.registro[23].datahora_ciclo_fim.hora.min);
                    }
                }

                if (i >= 1 && i <= 24)
                {
                    // copia
                    j = i - 1;

                    Consumo[i] = relatorio.registro[j].consumo;

                    if (relatorio.registro[j].valor_ciclo_ini < 0.0)
                    {
                        Consumo[i] = 0.0;
                        Valor_CicloIni[i] = "--------";
                        Hora_CicloIni[i] = "(--:--)";
                    }
                    else
                    {
                        Valor_CicloIni[i] = string.Format("{0:00000000}", relatorio.registro[j].valor_ciclo_ini);
                        Hora_CicloIni[i] = string.Format("({0:00}:{1:00})", (int)relatorio.registro[j].datahora_ciclo_ini.hora.hora, (int)relatorio.registro[j].datahora_ciclo_ini.hora.min);
                    }

                    if (relatorio.registro[j].valor_ciclo_fim < 0.0)
                    {
                        Consumo[i] = 0.0;
                        Valor_CicloFim[i] = "--------";
                        Hora_CicloFim[i] = "(--:--)";
                    }
                    else
                    {
                        Valor_CicloFim[i] = string.Format("{0:00000000}", relatorio.registro[j].valor_ciclo_fim);
                        Hora_CicloFim[i] = string.Format("({0:00}:{1:00})", (int)relatorio.registro[j].datahora_ciclo_fim.hora.hora, (int)relatorio.registro[j].datahora_ciclo_fim.hora.min);
                    }

                    // verifica consumo minimo
                    if (Consumo[i] < Consumo_min_grafico && Consumo[i] > 0.0)
                        Consumo_min_grafico = Consumo[i];

                    // verifica consumo maximo
                    if (Consumo[i] > Consumo_max_grafico)
                        Consumo_max_grafico = Consumo[i];
                }
            }

            // grafico comeca em zero caso minimo maior que zero
            if (Consumo_min_grafico > 0.0)
            {
                Consumo_min_grafico = 0.0;
            }

            Consumo_min_grafico = Consumo_min_grafico * 1.1;
            Consumo_max_grafico = Consumo_max_grafico * 1.1;

            ViewBag.ConsMinGrafico = Consumo_min_grafico;
            ViewBag.ConsMaxGrafico = Consumo_max_grafico;

            ViewBag.Consumo = Consumo;
            ViewBag.Dias = Dias;
            ViewBag.DatasN = DatasN;
            ViewBag.Horas = Horas;

            ViewBag.Valor_CicloIni = Valor_CicloIni;
            ViewBag.Hora_CicloIni = Hora_CicloIni;
            ViewBag.Valor_CicloFim = Valor_CicloFim;
            ViewBag.Hora_CicloFim = Hora_CicloFim;

            // nome do relatorio
            ViewBag.NomeRelat = string.Format("Ciclômetro");
            ViewBag.PeriodoRelat = string.Format("Diário");

            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", dataRelat);
            ViewBag.DataAtualN = dataRelat;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", dataRelat);

            // consumo
            ViewBag.Cons_Max = string.Format("{0:#,##0.00}", analise.analise_valor1.maximo);
            DateTime Cons_Max_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max);
            if (Cons_Max_DataHora.Year != 2000)
                ViewBag.Cons_Max_DataHora = string.Format("{0:t}", Cons_Max_DataHora);
            else
                ViewBag.Cons_Max_DataHora = "--:--";
            ViewBag.Cons_Max_DataHoraN = Cons_Max_DataHora;

            ViewBag.Cons_Med = string.Format("{0:#,##0.00}", analise.analise_valor1.medio);
            ViewBag.Cons_Total = string.Format("{0:#,##0.00}", analise.analise_valor1.consumo);

            ViewBag.NomeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.nome);
            ViewBag.UnidadeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.unidade);

            // ciclometro
            if (analise.analise_valor1.valor_ciclo_ini < 0.0)
            {
                ViewBag.CicloIni_DiaAtual_Valor = "--------";
                ViewBag.CicloIni_DiaAtual_Data = "";

                ViewBag.CicloIni_DiaAtual_ValorN = 0;
                ViewBag.CicloIni_DiaAtual_DataN = new DateTime(2000, 1, 1, 0, 0, 0);
            }
            else
            {
                DateTime CicloIni_DiaAtual_Data = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_ciclo_ini);

                ViewBag.CicloIni_DiaAtual_Valor = string.Format("{0:00000000}", analise.analise_valor1.valor_ciclo_ini);
                ViewBag.CicloIni_DiaAtual_Data = string.Format("({0:g})", CicloIni_DiaAtual_Data);

                ViewBag.CicloIni_DiaAtual_ValorN = analise.analise_valor1.valor_ciclo_ini;
                ViewBag.CicloIni_DiaAtual_DataN = CicloIni_DiaAtual_Data;
            }

            if (analise.analise_valor1.valor_ciclo_fim < 0.0)
            {
                ViewBag.CicloFim_DiaAtual_Valor = "--------";
                ViewBag.CicloFim_DiaAtual_Data = "";

                ViewBag.CicloFim_DiaAtual_ValorN = 0;
                ViewBag.CicloFim_DiaAtual_DataN = new DateTime(2000, 1, 1, 0, 0, 0);
            }
            else
            {
                DateTime CicloFim_DiaAtual_Data = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_ciclo_fim);

                ViewBag.CicloFim_DiaAtual_Valor = string.Format("{0:00000000}", analise.analise_valor1.valor_ciclo_fim);
                ViewBag.CicloFim_DiaAtual_Data = string.Format("({0:g})", CicloFim_DiaAtual_Data);

                ViewBag.CicloFim_DiaAtual_ValorN = analise.analise_valor1.valor_ciclo_fim;
                ViewBag.CicloFim_DiaAtual_DataN = CicloFim_DiaAtual_Data;
            }

            return(0);
        }

        // Ciclometro Semanal
        private int Ciclometro_Semanal(CliGrupoUnidMedicoesDominio medicao, DateTime dataRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_CICLOMETRO_SEMANAL relatorio = new RELAT_CICLOMETRO_SEMANAL();
            RELAT_CICLOMETRO_SEMANAL_ANALISE analise = new RELAT_CICLOMETRO_SEMANAL_ANALISE();
            GRANDEZA grandeza = new GRANDEZA();

            // preenche solicitacao
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = medicao.IDMedicao;

            // converte data hora
            DATAHORA data_hora = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, dataRelat);

            // calcula valores
            retorno = SmCalcDB_Ciclometro_RelatSemanal((char)0, ref config_interface, (char)0, ref data_hora, ref relatorio, ref analise, ref grandeza);

            ViewBag.Retorno = retorno;

            // verifica se erro
            if (retorno != 0)
            {
                // erro
                // -2 - erro : tipo de interface indefinida
                // -1 - erro : tipo de relatorio indefinido
                // 0  - ok e com dados
                // 1  - erro : nao foi possivel abrir o banco de dados
                // 2  - erro : nao existem dados
                // 3  - erro : leitura da configuracao da medicao
                // 4  - erro : leitura das tarifas de energia
                return (retorno);
            }

            // grafico
            var Consumo = new double[7, 26];
            var Datas = new string[26];
            var DatasN = new DateTime[7, 26];
            var Dias = new string[7];
            var Horas = new string[26];

            DateTime inicio = new DateTime();
            DateTime fim = new DateTime();

            double Consumo_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;
            int k = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                Horas[i] = strData.ToString("HH:mm");

                // percorre dias
                for (k = 0; k < 7; k++)
                {
                    // data e hora para excel
                    DatasN[k, i] = strData.AddDays(k);

                    // guarda inicio e fim
                    if (i == 0 && k == 0)
                    {
                        inicio = strData;
                        fim = inicio.AddDays(6);
                    }

                    // dia da semana
                    if (i == 1)
                    {
                        Dias[k] = string.Format("{0:d}", strData.AddDays(k));
                    }

                    // verifica se primeira ou ultima barra
                    if (i == 0)
                    {
                        // zera
                        Consumo[k, i] = relatorio.registro[0].consumo[k];
                    }

                    if (i == 25)
                    {
                        // zera
                        Consumo[k, i] = relatorio.registro[23].consumo[k];
                    }

                    if (i >= 1 && i <= 24)
                    {
                        // copia
                        j = i - 1;

                        Consumo[k, i] = relatorio.registro[j].consumo[k];

                        // verifica consumo maximo
                        if (Consumo[k, i] > Consumo_max_grafico)
                            Consumo_max_grafico = Consumo[k, i];
                    }
                }

                // proxima hora
                strData = strData.AddHours(1);
            }

            Consumo_max_grafico = Consumo_max_grafico * 1.1;

            ViewBag.ConsMaxGrafico = Consumo_max_grafico;

            ViewBag.Consumo = Consumo;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;
            ViewBag.Horas = Horas;

            // nome do relatorio
            ViewBag.NomeRelat = string.Format("Ciclômetro");
            ViewBag.PeriodoRelat = string.Format("Semanal");

            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", dataRelat);
            ViewBag.DataAtualN = inicio;
            ViewBag.DataFinalN = fim;
            ViewBag.DataTextoAtual = string.Format("{0:d} - {1:d}", inicio, fim);

            // consumo
            ViewBag.Cons_Max = string.Format("{0:#,##0.00}", analise.analise_valor1.maximo);
            DateTime Cons_Max_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max);
            if (Cons_Max_DataHora.Year != 2000)
                ViewBag.Cons_Max_DataHora = string.Format("{0:g}", Cons_Max_DataHora);
            else
                ViewBag.Cons_Max_DataHora = "--/--/---- --:--";
            ViewBag.Cons_Max_DataHoraN = Cons_Max_DataHora;

            ViewBag.Cons_Med = string.Format("{0:#,##0.00}", analise.analise_valor1.medio);
            ViewBag.Cons_Total = string.Format("{0:#,##0.00}", analise.analise_valor1.consumo);

            ViewBag.NomeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.nome);
            ViewBag.UnidadeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.unidade);

            // ciclometro
            if (analise.analise_valor1.valor_ciclo_ini < 0.0)
            {
                ViewBag.CicloIni_SemanaAtual_Valor = "--------";
                ViewBag.CicloIni_SemanaAtual_Data = "";

                ViewBag.CicloIni_SemanaAtual_ValorN = 0;
                ViewBag.CicloIni_SemanaAtual_DataN = new DateTime(2000, 1, 1, 0, 0, 0);
            }
            else
            {
                DateTime CicloIni_SemanaAtual_Data = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_ciclo_ini);

                ViewBag.CicloIni_SemanaAtual_Valor = string.Format("{0:00000000}", analise.analise_valor1.valor_ciclo_ini);
                ViewBag.CicloIni_SemanaAtual_Data = string.Format("({0:g})", CicloIni_SemanaAtual_Data);

                ViewBag.CicloIni_SemanaAtual_ValorN = analise.analise_valor1.valor_ciclo_ini;
                ViewBag.CicloIni_SemanaAtual_DataN = CicloIni_SemanaAtual_Data;
            }

            if (analise.analise_valor1.valor_ciclo_fim < 0.0)
            {
                ViewBag.CicloFim_SemanaAtual_Valor = "--------";
                ViewBag.CicloFim_SemanaAtual_Data = "";

                ViewBag.CicloFim_SemanaAtual_ValorN = 0;
                ViewBag.CicloFim_SemanaAtual_DataN = new DateTime(2000, 1, 1, 0, 0, 0);
            }
            else
            {
                DateTime CicloFim_SemanaAtual_Data = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_ciclo_fim);

                ViewBag.CicloFim_SemanaAtual_Valor = string.Format("{0:00000000}", analise.analise_valor1.valor_ciclo_fim);
                ViewBag.CicloFim_SemanaAtual_Data = string.Format("({0:g})", CicloFim_SemanaAtual_Data);

                ViewBag.CicloFim_SemanaAtual_ValorN = analise.analise_valor1.valor_ciclo_fim;
                ViewBag.CicloFim_SemanaAtual_DataN = CicloFim_SemanaAtual_Data;
            }

            return(0);
        }

        // Ciclometro Mensal
        private int Ciclometro_Mensal(CliGrupoUnidMedicoesDominio medicao, DateTime dataRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_CICLOMETRO_MENSAL relatorio = new RELAT_CICLOMETRO_MENSAL();
            RELAT_CICLOMETRO_MENSAL_ANALISE analise = new RELAT_CICLOMETRO_MENSAL_ANALISE();
            GRANDEZA grandeza = new GRANDEZA();

            // preenche solicitacao
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = medicao.IDMedicao;

            // converte data hora
            DATAHORA data_hora = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, dataRelat);

            // calcula valores
            retorno = SmCalcDB_Ciclometro_RelatMensal((char)0, ref config_interface, (char)0, ref data_hora, ref relatorio, ref analise, ref grandeza);

            int NumDiasMes = relatorio.num_dias;

            ViewBag.Retorno = retorno;

            // verifica se erro
            if (retorno != 0)
            {
                // erro
                // -2 - erro : tipo de interface indefinida
                // -1 - erro : tipo de relatorio indefinido
                // 0  - ok e com dados
                // 1  - erro : nao foi possivel abrir o banco de dados
                // 2  - erro : nao existem dados
                // 3  - erro : leitura da configuracao da medicao
                // 4  - erro : leitura das tarifas de energia
                return (retorno);
            }

            // grafico
            var Consumo = new double[33];
            var Datas = new string[33];
            var DatasN = new DateTime[33];
            var Dias = new string[33];

            var Valor_CicloIni = new string[33];
            var Hora_CicloIni = new string[33];

            var Valor_CicloFim = new string[33];
            var Hora_CicloFim = new string[33];

            double Consumo_min_grafico = 0.0;
            double Consumo_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int i = 0;
            int j = 0;

            DateTime DataAux = new DateTime();

            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = string.Format("{0:0}", strData.Day);

                // proximo dias
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    Consumo[i] = relatorio.registro[0].consumo;

                    if (relatorio.registro[0].valor_ciclo_ini < 0.0)
                    {
                        Consumo[i] = 0.0;
                        Valor_CicloIni[i] = "--------";
                        Hora_CicloIni[i] = "(--/--/---- --:--)";
                    }
                    else
                    {
                        Valor_CicloIni[i] = string.Format("{0:00000000}", relatorio.registro[0].valor_ciclo_ini);

                        DataAux = Funcoes_Converte.ConverteDataHora2DateTime(relatorio.registro[0].datahora_ciclo_ini);
                        Hora_CicloIni[i] = string.Format("({0:g})", DataAux);
                    }

                    if (relatorio.registro[0].valor_ciclo_fim < 0.0)
                    {
                        Consumo[i] = 0.0;
                        Valor_CicloFim[i] = "--------";
                        Hora_CicloFim[i] = "(--/--/---- --:--)";
                    }
                    else
                    {
                        Valor_CicloFim[i] = string.Format("{0:00000000}", relatorio.registro[0].valor_ciclo_fim);

                        DataAux = Funcoes_Converte.ConverteDataHora2DateTime(relatorio.registro[0].datahora_ciclo_fim);
                        Hora_CicloFim[i] = string.Format("({0:g})", DataAux);
                    }
                }

                if (i == (NumDiasMes + 1))
                {
                    // zera
                    Consumo[i] = relatorio.registro[NumDiasMes - 1].consumo;

                    if (relatorio.registro[NumDiasMes - 1].valor_ciclo_ini < 0.0)
                    {
                        Consumo[i] = 0.0;
                        Valor_CicloIni[i] = "--------";
                        Hora_CicloIni[i] = "(--/--/---- --:--)";
                    }
                    else
                    {
                        Valor_CicloIni[i] = string.Format("{0:00000000}", relatorio.registro[NumDiasMes - 1].valor_ciclo_ini);

                        DataAux = Funcoes_Converte.ConverteDataHora2DateTime(relatorio.registro[NumDiasMes - 1].datahora_ciclo_ini);
                        Hora_CicloIni[i] = string.Format("({0:g})", DataAux);
                    }

                    if (relatorio.registro[NumDiasMes - 1].valor_ciclo_fim < 0.0)
                    {
                        Consumo[i] = 0.0;
                        Valor_CicloFim[i] = "--------";
                        Hora_CicloFim[i] = "(--/--/---- --:--)";
                    }
                    else
                    {
                        Valor_CicloFim[i] = string.Format("{0:00000000}", relatorio.registro[NumDiasMes - 1].valor_ciclo_fim);

                        DataAux = Funcoes_Converte.ConverteDataHora2DateTime(relatorio.registro[NumDiasMes - 1].datahora_ciclo_fim);
                        Hora_CicloFim[i] = string.Format("({0:g})", DataAux);
                    }
                }

                if (i >= 1 && i <= NumDiasMes)
                {
                    // copia
                    j = i - 1;

                    Consumo[i] = relatorio.registro[j].consumo;

                    if (relatorio.registro[j].valor_ciclo_ini < 0.0)
                    {
                        Consumo[i] = 0.0;
                        Valor_CicloIni[i] = "--------";
                        Hora_CicloIni[i] = "(--/--/---- --:--)";
                    }
                    else
                    {
                        Valor_CicloIni[i] = string.Format("{0:00000000}", relatorio.registro[j].valor_ciclo_ini);

                        DataAux = Funcoes_Converte.ConverteDataHora2DateTime(relatorio.registro[j].datahora_ciclo_ini);
                        Hora_CicloIni[i] = string.Format("({0:g})", DataAux);
                    }

                    if (relatorio.registro[j].valor_ciclo_fim < 0.0)
                    {
                        Consumo[i] = 0.0;
                        Valor_CicloFim[i] = "--------";
                        Hora_CicloFim[i] = "(--/--/---- --:--)";
                    }
                    else
                    {
                        Valor_CicloFim[i] = string.Format("{0:00000000}", relatorio.registro[j].valor_ciclo_fim);

                        DataAux = Funcoes_Converte.ConverteDataHora2DateTime(relatorio.registro[j].datahora_ciclo_fim);
                        Hora_CicloFim[i] = string.Format("({0:g})", DataAux);
                    }

                    // verifica consumo minimo
                    if (Consumo[i] < Consumo_min_grafico && Consumo[i] > 0.0)
                        Consumo_min_grafico = Consumo[i];

                    // verifica consumo maximo
                    if (Consumo[i] > Consumo_max_grafico)
                        Consumo_max_grafico = Consumo[i];
                }
            }

            // grafico comeca em zero caso minimo maior que zero
            if (Consumo_min_grafico > 0.0)
            {
                Consumo_min_grafico = 0.0;
            }

            Consumo_min_grafico = Consumo_min_grafico * 1.1;
            Consumo_max_grafico = Consumo_max_grafico * 1.1;

            ViewBag.ConsMinGrafico = Consumo_min_grafico;
            ViewBag.ConsMaxGrafico = Consumo_max_grafico;

            ViewBag.Consumo = Consumo;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            ViewBag.Valor_CicloIni = Valor_CicloIni;
            ViewBag.Hora_CicloIni = Hora_CicloIni;
            ViewBag.Valor_CicloFim = Valor_CicloFim;
            ViewBag.Hora_CicloFim = Hora_CicloFim;

            // nome do relatorio
            ViewBag.NomeRelat = string.Format("Ciclômetro");
            ViewBag.PeriodoRelat = string.Format("Mensal");

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", dataRelat);
            ViewBag.DataAtualN = dataRelat;
            ViewBag.DataTextoAtual = string.Format("{0:y}", dataRelat);

            ViewBag.NumDiasMes = NumDiasMes;

            // consumo
            ViewBag.Cons_Max = string.Format("{0:#,##0.00}", analise.analise_valor1.maximo);
            DateTime Cons_Max_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max);
            if (Cons_Max_DataHora.Year != 2000)
                ViewBag.Cons_Max_DataHora = string.Format("{0:g}", Cons_Max_DataHora);
            else
                ViewBag.Cons_Max_DataHora = "--/--/---- --:--";
            ViewBag.Cons_Max_DataHoraN = Cons_Max_DataHora;

            ViewBag.Cons_Med = string.Format("{0:#,##0.00}", analise.analise_valor1.medio);
            ViewBag.Cons_Total = string.Format("{0:#,##0}", analise.analise_valor1.consumo);

            ViewBag.NomeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.nome);
            ViewBag.UnidadeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.unidade);

            // ciclometro
            if (analise.analise_valor1.valor_ciclo_ini < 0.0)
            {
                ViewBag.CicloIni_MesAtual_Valor = "--------";
                ViewBag.CicloIni_MesAtual_Data = "";

                ViewBag.CicloIni_MesAtual_ValorN = 0;
                ViewBag.CicloIni_MesAtual_DataN = new DateTime(2000, 1, 1, 0, 0, 0);
            }
            else
            {
                DateTime CicloIni_MesAtual_Data = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_ciclo_ini);

                ViewBag.CicloIni_MesAtual_Valor = string.Format("{0:00000000}", analise.analise_valor1.valor_ciclo_ini);
                ViewBag.CicloIni_MesAtual_Data = string.Format("({0:g})", CicloIni_MesAtual_Data);

                ViewBag.CicloIni_MesAtual_ValorN = analise.analise_valor1.valor_ciclo_ini;
                ViewBag.CicloIni_MesAtual_DataN = CicloIni_MesAtual_Data;
            }

            if (analise.analise_valor1.valor_ciclo_fim < 0.0)
            {
                ViewBag.CicloFim_MesAtual_Valor = "--------";
                ViewBag.CicloFim_MesAtual_Data = "";

                ViewBag.CicloFim_MesAtual_ValorN = 0;
                ViewBag.CicloFim_MesAtual_DataN = new DateTime(2000, 1, 1, 0, 0, 0);
            }
            else
            {
                DateTime CicloFim_MesAtual_Data = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_ciclo_fim);

                ViewBag.CicloFim_MesAtual_Valor = string.Format("{0:00000000}", analise.analise_valor1.valor_ciclo_fim);
                ViewBag.CicloFim_MesAtual_Data = string.Format("({0:g})", CicloFim_MesAtual_Data);

                ViewBag.CicloFim_MesAtual_ValorN = analise.analise_valor1.valor_ciclo_fim;
                ViewBag.CicloFim_MesAtual_DataN = CicloFim_MesAtual_Data;
            }

            return(0);
        }
    }
}
