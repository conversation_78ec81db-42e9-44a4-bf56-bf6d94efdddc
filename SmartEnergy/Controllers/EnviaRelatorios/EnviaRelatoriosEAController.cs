﻿using System;
using System.Runtime.InteropServices;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{

    public partial class EnviaRelatoriosController
    {
        // Relatorio Diario
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_EA_RelatDiario", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_EA_RelatDiario(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, ref DATAHOR<PERSON> pdata_equipo, ref RELAT_EA_DIARIO prelatorio, ref RELAT_EA_DIARIO_ANALISE panalise, ref GRANDEZA pgrandeza);

        // Relatorio Semanal
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_EA_RelatSemanal", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_EA_RelatSemanal(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, ref DATAHORA pdata_equipo, ref RELAT_EA_SEMANAL prelatorio, ref RELAT_EA_SEMANAL_ANALISE panalise, ref GRANDEZA pgrandeza);

        // Relatorio Mensal
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_EA_RelatMensal", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_EA_RelatMensal(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, ref DATAHORA pdata_equipo, ref RELAT_EA_MENSAL prelatorio, ref RELAT_EA_MENSAL_ANALISE panalise, ref GRANDEZA pgrandeza);


        // Entradas Analogicas Diario
        private int EA_Diario(CliGrupoUnidMedicoesDominio medicao, DateTime dataRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_EA_DIARIO relatorio = new RELAT_EA_DIARIO();
            RELAT_EA_DIARIO_ANALISE analise = new RELAT_EA_DIARIO_ANALISE();
            GRANDEZA grandeza = new GRANDEZA();

            // preenche solicitacao
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = medicao.IDMedicao;

            // converte data hora
            DATAHORA data_hora = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, dataRelat);

            // calcula valores
            retorno = SmCalcDB_EA_RelatDiario((char)0, ref config_interface, (char)0, ref data_hora, ref relatorio, ref analise, ref grandeza);

            ViewBag.Retorno = retorno;

            // verifica se erro
            if (retorno != 0)
            {
                // erro
                // -2 - erro : tipo de interface indefinida
                // -1 - erro : tipo de relatorio indefinido
                // 0  - ok e com dados
                // 1  - erro : nao foi possivel abrir o banco de dados
                // 2  - erro : nao existem dados
                // 3  - erro : leitura da configuracao da medicao
                // 4  - erro : leitura das tarifas de energia
                return (retorno);
            }

            // grafico
            var VMax = new double[26];
            var VMed = new double[26];
            var VMin = new double[26];
            var Dias = new string[26];
            var DatasN = new DateTime[26];
            var Horas = new string[26];

            double Valor_min = 0.0;
            double Valor_max = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Horas[i] = strData.ToString("HH:mm");

                // proxima hora
                strData = strData.AddHours(1);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    VMax[i] = relatorio.registro[1].vmax;
                    VMed[i] = relatorio.registro[1].vmed;
                    VMin[i] = relatorio.registro[1].vmin;

                    // minimo e maximo
                    Valor_min = VMin[i];
                    Valor_max = VMax[i];
                }

                // verifica se ultima barra
                if (i == 25)
                {
                    // zera
                    VMax[i] = relatorio.registro[23].vmax;
                    VMed[i] = relatorio.registro[23].vmed;
                    VMin[i] = relatorio.registro[23].vmin;
                }

                if (i >= 1 && i <= 24)
                {
                    // copia
                    j = i - 1;

                    VMax[i] = relatorio.registro[j].vmax;
                    VMed[i] = relatorio.registro[j].vmed;
                    VMin[i] = relatorio.registro[j].vmin;

                    // verifica minimo
                    if (VMax[i] < Valor_min)
                        Valor_min = VMax[i];

                    if (VMed[i] < Valor_min)
                        Valor_min = VMed[i];

                    if (VMin[i] < Valor_min)
                        Valor_min = VMin[i];

                    // verifica maximo
                    if (VMin[i] > Valor_max)
                        Valor_max = VMin[i];

                    if (VMed[i] > Valor_max)
                        Valor_max = VMed[i];

                    if (VMax[i] > Valor_max)
                        Valor_max = VMax[i];
                }
            }

            // valor máximo (5% e arredonda para cima)
            Valor_max = Valor_max + (Math.Abs(Valor_max) * 0.05);
            Valor_max = Math.Ceiling(Valor_max);

            // valor mínimo (5% e arredonda para baixo)
            Valor_min = Valor_min - (Math.Abs(Valor_min) * 0.05);
            Valor_min = Math.Floor(Valor_min);

            // copia valores
            ViewBag.ValorMax = Valor_max;
            ViewBag.ValorMin = Valor_min;

            ViewBag.VMax = VMax;
            ViewBag.VMed = VMed;
            ViewBag.VMin = VMin;
            ViewBag.Dias = Dias;
            ViewBag.DatasN = DatasN;
            ViewBag.Horas = Horas;

            // nome do relatorio
            ViewBag.NomeRelat = string.Format("Analógicas");
            ViewBag.PeriodoRelat = string.Format("Diário");

            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", dataRelat);
            ViewBag.DataAtualN = dataRelat;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", dataRelat);

            // valores
            ViewBag.VMaxDia = string.Format("{0:#,##0.00}", analise.analise_valor1.maximo[0]);
            DateTime VMaxDia_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max[0]);
            if (VMaxDia_DataHora.Year != 2000)
                ViewBag.VMaxDia_DataHora = string.Format("{0:t}", VMaxDia_DataHora);
            else
                ViewBag.VMaxDia_DataHora = "--:--";
            ViewBag.VMaxDia_DataHoraN = VMaxDia_DataHora;

            ViewBag.VMinDia = string.Format("{0:#,##0.00}", analise.analise_valor1.minimo[0]);
            DateTime VMinDia_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_min[0]);
            if (VMinDia_DataHora.Year != 2000)
                ViewBag.VMinDia_DataHora = string.Format("{0:t}", VMinDia_DataHora);
            else
                ViewBag.VMinDia_DataHora = "--:--";
            ViewBag.VMinDia_DataHoraN = VMinDia_DataHora;

            ViewBag.VMedDia = string.Format("{0:#,##0.00}", analise.analise_valor1.medio[0]);

            ViewBag.NomeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.nome);
            ViewBag.UnidadeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.unidade);

            return(0);
        }

        // Entradas Analogicas Semanal
        private int EA_Semanal(CliGrupoUnidMedicoesDominio medicao, DateTime dataRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_EA_SEMANAL relatorio = new RELAT_EA_SEMANAL();
            RELAT_EA_SEMANAL_ANALISE analise = new RELAT_EA_SEMANAL_ANALISE();
            GRANDEZA grandeza = new GRANDEZA();

            // preenche solicitacao
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = medicao.IDMedicao;

            // converte data hora
            DATAHORA data_hora = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, dataRelat);

            // calcula valores
            retorno = SmCalcDB_EA_RelatSemanal((char)0, ref config_interface, (char)0, ref data_hora, ref relatorio, ref analise, ref grandeza);

            ViewBag.Retorno = retorno;

            // verifica se erro
            if (retorno != 0)
            {
                // erro
                // -2 - erro : tipo de interface indefinida
                // -1 - erro : tipo de relatorio indefinido
                // 0  - ok e com dados
                // 1  - erro : nao foi possivel abrir o banco de dados
                // 2  - erro : nao existem dados
                // 3  - erro : leitura da configuracao da medicao
                // 4  - erro : leitura das tarifas de energia
                return (retorno);
            }

            // grafico
            var VMax = new double[7, 26];
            var VMed = new double[7, 26];
            var VMin = new double[7, 26];
            var Datas = new string[26];
            var DatasN = new DateTime[7, 26];
            var Dias = new string[7];
            var Horas = new string[26];

            DateTime inicio = new DateTime();
            DateTime fim = new DateTime();

            double Valor_min = 0.0;
            double Valor_max = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;
            int k = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                Horas[i] = strData.ToString("HH:mm");

                // percorre dias
                for (k = 0; k < 7; k++)
                {
                    // data e hora para excel
                    DatasN[k, i] = strData.AddDays(k);

                    // guarda inicio e fim
                    if (i == 0 && k == 0)
                    {
                        inicio = strData;
                        fim = inicio.AddDays(6);

                        // minimo e maximo
                        Valor_min = relatorio.registro[0].vmin[k];
                        Valor_max = relatorio.registro[0].vmax[k];
                    }

                    // dia da semana
                    if (i == 1)
                    {
                        Dias[k] = string.Format("{0:d}", strData.AddDays(k));
                    }

                    // verifica se primeira ou ultima barra
                    if (i == 0)
                    {
                        // zera
                        VMax[k, i] = relatorio.registro[0].vmax[k];
                        VMed[k, i] = relatorio.registro[0].vmed[k];
                        VMin[k, i] = relatorio.registro[0].vmin[k];
                    }

                    if (i == 25)
                    {
                        // zera
                        VMax[k, i] = relatorio.registro[23].vmax[k];
                        VMed[k, i] = relatorio.registro[23].vmed[k];
                        VMin[k, i] = relatorio.registro[23].vmin[k];
                    }

                    if (i >= 1 && i <= 24)
                    {
                        // copia
                        j = i - 1;

                        VMax[k, i] = relatorio.registro[j].vmax[k];
                        VMed[k, i] = relatorio.registro[j].vmed[k];
                        VMin[k, i] = relatorio.registro[j].vmin[k];

                        // verifica minimo
                        if (VMax[k, i] < Valor_min)
                            Valor_min = VMax[k, i];

                        if (VMed[k, i] < Valor_min)
                            Valor_min = VMed[k, i];

                        if (VMin[k, i] < Valor_min)
                            Valor_min = VMin[k, i];

                        // verifica maximo
                        if (VMin[k, i] > Valor_max)
                            Valor_max = VMin[k, i];

                        if (VMed[k, i] > Valor_max)
                            Valor_max = VMed[k, i];

                        if (VMax[k, i] > Valor_max)
                            Valor_max = VMax[k, i];
                    }
                }

                // proxima hora
                strData = strData.AddHours(1);
            }

            // valor máximo (5% e arredonda para cima)
            Valor_max = Valor_max + (Math.Abs(Valor_max) * 0.05);
            Valor_max = Math.Ceiling(Valor_max);

            // valor mínimo (5% e arredonda para baixo)
            Valor_min = Valor_min - (Math.Abs(Valor_min) * 0.05);
            Valor_min = Math.Floor(Valor_min);

            // copia valores
            ViewBag.ValorMax = Valor_max;
            ViewBag.ValorMin = Valor_min;

            ViewBag.VMax = VMax;
            ViewBag.VMed = VMed;
            ViewBag.VMin = VMin;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;
            ViewBag.Horas = Horas;

            // nome do relatorio
            ViewBag.NomeRelat = string.Format("Analógicas");
            ViewBag.PeriodoRelat = string.Format("Semanal");

            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", dataRelat);
            ViewBag.DataAtualN = inicio;
            ViewBag.DataFinalN = fim;
            ViewBag.DataTextoAtual = string.Format("{0:d} - {1:d}", inicio, fim);

            // valores
            ViewBag.VMaxSemana = string.Format("{0:#,##0.00}", analise.analise_valor.maximo[0]);
            DateTime VMaxSemana_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (VMaxSemana_DataHora.Year != 2000)
                ViewBag.VMaxSemana_DataHora = string.Format("{0:g}", VMaxSemana_DataHora);
            else
                ViewBag.VMaxSemana_DataHora = "--/--/---- --:--";
            ViewBag.VMaxSemana_DataHoraN = VMaxSemana_DataHora;

            ViewBag.VMinSemana = string.Format("{0:#,##0.00}", analise.analise_valor.minimo[0]);
            DateTime VMinSemana_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[0]);
            if (VMinSemana_DataHora.Year != 2000)
                ViewBag.VMinSemana_DataHora = string.Format("{0:g}", VMinSemana_DataHora);
            else
                ViewBag.VMinSemana_DataHora = "--/--/---- --:--";
            ViewBag.VMinSemana_DataHoraN = VMinSemana_DataHora;

            ViewBag.VMedSemana = string.Format("{0:#,##0.00}", analise.analise_valor.medio[0]);

            ViewBag.NomeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.nome);
            ViewBag.UnidadeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.unidade);

            return(0);
        }

        // Entradas Analogicas Mensal
        private int EA_Mensal(CliGrupoUnidMedicoesDominio medicao, DateTime dataRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_EA_MENSAL relatorio = new RELAT_EA_MENSAL();
            RELAT_EA_MENSAL_ANALISE analise = new RELAT_EA_MENSAL_ANALISE();
            GRANDEZA grandeza = new GRANDEZA();

            // preenche solicitacao
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = medicao.IDMedicao;

            // converte data hora
            DATAHORA data_hora = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, dataRelat);

            // calcula valores
            retorno = SmCalcDB_EA_RelatMensal((char)0, ref config_interface, (char)0, ref data_hora, ref relatorio, ref analise, ref grandeza);

            int NumDiasMes = relatorio.num_dias;

            ViewBag.Retorno = retorno;

            // verifica se erro
            if (retorno != 0)
            {
                // erro
                // -2 - erro : tipo de interface indefinida
                // -1 - erro : tipo de relatorio indefinido
                // 0  - ok e com dados
                // 1  - erro : nao foi possivel abrir o banco de dados
                // 2  - erro : nao existem dados
                // 3  - erro : leitura da configuracao da medicao
                // 4  - erro : leitura das tarifas de energia
                return (retorno);
            }

            // grafico
            var VMax = new double[33];
            var VMed = new double[33];
            var VMin = new double[33];
            var Datas = new string[33];
            var DatasN = new DateTime[33];
            var Dias = new string[33];

            double Valor_min = 0.0;
            double Valor_max = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = string.Format("{0:0}", strData.Day);

                // proximo dias
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    VMax[i] = relatorio.registro[0].vmax;
                    VMed[i] = relatorio.registro[0].vmed;
                    VMin[i] = relatorio.registro[0].vmin;

                    // minimo e maximo
                    Valor_min = VMin[i];
                    Valor_max = VMax[i];
                }

                if (i == (NumDiasMes + 1))
                {
                    // zera
                    VMax[i] = relatorio.registro[NumDiasMes - 1].vmax;
                    VMed[i] = relatorio.registro[NumDiasMes - 1].vmed;
                    VMin[i] = relatorio.registro[NumDiasMes - 1].vmin;
                }

                if (i >= 1 && i <= NumDiasMes)
                {
                    // copia
                    j = i - 1;

                    VMax[i] = relatorio.registro[j].vmax;
                    VMed[i] = relatorio.registro[j].vmed;
                    VMin[i] = relatorio.registro[j].vmin;

                    // verifica minimo
                    if (VMax[i] < Valor_min)
                        Valor_min = VMax[i];

                    if (VMed[i] < Valor_min)
                        Valor_min = VMed[i];

                    if (VMin[i] < Valor_min)
                        Valor_min = VMin[i];

                    // verifica maximo
                    if (VMin[i] > Valor_max)
                        Valor_max = VMin[i];

                    if (VMed[i] > Valor_max)
                        Valor_max = VMed[i];

                    if (VMax[i] > Valor_max)
                        Valor_max = VMax[i];
                }
            }

            // valor máximo (10% e arredonda para cima)
            Valor_max = Valor_max + (Math.Abs(Valor_max) * 0.1);
            Valor_max = Math.Ceiling(Valor_max);

            // valor mínimo (10% e arredonda para baixo)
            Valor_min = Valor_min - (Math.Abs(Valor_min) * 0.1);
            Valor_min = Math.Floor(Valor_min);

            // copia valores
            ViewBag.ValorMax = Valor_max;
            ViewBag.ValorMin = Valor_min;

            ViewBag.VMax = VMax;
            ViewBag.VMed = VMed;
            ViewBag.VMin = VMin;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            // nome do relatorio
            ViewBag.NomeRelat = string.Format("Analógicas");
            ViewBag.PeriodoRelat = string.Format("Mensal");

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", dataRelat);
            ViewBag.DataAtualN = dataRelat;
            ViewBag.DataTextoAtual = string.Format("{0:y}", dataRelat);

            ViewBag.NumDiasMes = NumDiasMes;

            // valores
            ViewBag.VMaxMes = string.Format("{0:#,##0.00}", analise.analise_valor.maximo[0]);
            DateTime VMaxMes_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (VMaxMes_DataHora.Year != 2000)
                ViewBag.VMaxMes_DataHora = string.Format("{0:g}", VMaxMes_DataHora);
            else
                ViewBag.VMaxMes_DataHora = "--/--/---- --:--";
            ViewBag.VMaxMes_DataHoraN = VMaxMes_DataHora;

            ViewBag.VMinMes = string.Format("{0:#,##0.00}", analise.analise_valor.minimo[0]);
            DateTime VMinMes_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[0]);
            if (VMinMes_DataHora.Year != 2000)
                ViewBag.VMinMes_DataHora = string.Format("{0:g}", VMinMes_DataHora);
            else
                ViewBag.VMinMes_DataHora = "--/--/---- --:--";
            ViewBag.VMinMes_DataHoraN = VMinMes_DataHora;

            ViewBag.VMedMes = string.Format("{0:#,##0.00}", analise.analise_valor.medio[0]);

            ViewBag.NomeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.nome);
            ViewBag.UnidadeGrandeza = Funcoes_Converte.ConverteByte2String(grandeza.unidade);

            return(0);
        }
    }
}
