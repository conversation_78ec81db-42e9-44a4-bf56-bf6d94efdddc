﻿using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Mime;
using System.Web.Hosting;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;
using System.Windows.Forms;

namespace SmartEnergy.Controllers
{

    public partial class EnviaRelatoriosController : Controller
    {
        // salvar arquivos no servidor
        bool SalvarArquivo_Servidor = false;

        // salvar arquivos no servidor
        int TipoAnexo_Email = 0;

        // Estatísticas
        int Num_Relatorios = 0;
        int Num_Relatorios_Erro = 0;
        List<string> Relatorios_Erro = new List<string>();

        DateTime DataHoraAtual = DateTime.Now;

        int Periodo_Relatorio = 0;


        // GET: EnviaRelatorios
        // Periodo
        //   0 = diário
        //   1 = semanal
        //   2 = mensal
        //
        // TipoAnexo
        //   0 = PDF
        //   1 = PNG
        //
        // SalvarArquivo
        //   0 = não salva arquivo no servidor
        //   1 = salva arquivo no servidor em ~\Temp
        //
        public ActionResult EnviaRelatorios(int Periodo = 0, int TipoAnexo = 0, int SalvarArquivo = 0)
        {
            // log
            LogMessage("_____________________________________________________________________________________________________________________");
            LogMessage("");
            LogMessage(string.Format("EnviaRelatorios v.{0}", Application.ProductVersion));
            LogMessage("");

            // envia relatório para todos os clientes
            EnviaRelat(0, Periodo, TipoAnexo, SalvarArquivo);

            // retorna status
            var returnedData = new
            {
                status = true
            };

            // relatorio
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: EnviaRelatorios_Cliente
        // IDCliente
        //
        // Periodo
        //   0 = diário
        //   1 = semanal
        //   2 = mensal
        //
        // TipoAnexo
        //   0 = PDF
        //   1 = PNG
        //
        // SalvarArquivo
        //   0 = não salva arquivo no servidor
        //   1 = salva arquivo no servidor em ~\Temp
        //
        public ActionResult EnviaRelatorios_Cliente(int IDCliente, int Periodo = 0, int TipoAnexo = 0, int SalvarArquivo = 0)
        {
            // log
            LogMessage("_____________________________________________________________________________________________________________________");
            LogMessage("");
            LogMessage(string.Format("EnviaRelatorios_Cliente v.{0} - Cliente [{1:000000}]", Application.ProductVersion, IDCliente));
            LogMessage("");

            // envia relatório somente para um cliente
            EnviaRelat(IDCliente, Periodo, TipoAnexo, SalvarArquivo);

            // retorna status
            var returnedData = new
            {
                status = true
            };

            // relatorio
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        public void EnviaRelat(int IDCliente, int Periodo, int TipoAnexo, int SalvarArquivo)
        {
            // periodo do relatorio
            Periodo_Relatorio = Periodo;


            // estatisticas
            Num_Relatorios = 0;
            DateTime DataHoraInicio = DateTime.Now;

            //
            // Configuração
            //

            // tipo do anexo
            TipoAnexo_Email = TipoAnexo;

            // salvar arquivo no servidor
            SalvarArquivo_Servidor = (SalvarArquivo == 1) ? true : false;


            //
            // Apaga arquivos
            //

            // verifica se é pra salvar arquivos
            if (SalvarArquivo_Servidor)
            {
                // apaga arquivos do diretório
                DirectoryInfo dir = new DirectoryInfo(Server.MapPath("~/Temp"));

                foreach (FileInfo fi in dir.GetFiles())
                {
                    fi.Delete();
                }

                // log
                LogMessage("Apagou arquivos");
            }

            //
            // Data do Relatório
            //

            // data do relatório
            DateTime data_atual = DateTime.Now.AddDays(-1);
            DateTime dataRelat = new DateTime(data_atual.Year, data_atual.Month, data_atual.Day, 0, 0, 0);

            //
            // Usuários
            //

            // le todos usuários
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            List<UsuarioDominio> usuarios = usuarioMetodos.ListarTodos(true);

            // log
            LogMessage("Leu usuários");

            //
            // Medições
            //

            // le todos os grupos de unidades/unidades/medições dos clientes
            CliGrupoUnidMedicoesMetodos medicaoMetodos = new CliGrupoUnidMedicoesMetodos();
            List<CliGrupoUnidMedicoesDominio> medicoes = new List<CliGrupoUnidMedicoesDominio>();

            if (IDCliente > 0)
            {
                medicoes = medicaoMetodos.LeListaGrupoUnidMedicoes_Cliente(IDCliente);
            }
            else
            {
                medicoes = medicaoMetodos.LeListaCliGrupoUnidMedicoes();
            }
                
            // log
            LogMessage("Leu medições");

            //
            // Envio dos Relatórios
            //

            if (usuarios != null && medicoes != null)
            {
                // log
                LogMessage("------");

                // percorre medições
                foreach (CliGrupoUnidMedicoesDominio medicao in medicoes)
                {
                    // verifica horario se eh cliente preferencial
                    int hora;
                    hora = System.DateTime.Now.Hour;
                    if (hora < 6)
                    {
                        // 291 - JBS Foods
                        // 356 - LDC 

                        // se nao for preferencial nao eh o momento de enviar
                        if (medicao.IDCliente != CLIENTES_ESPECIAIS.JBS && medicao.IDCliente != CLIENTES_ESPECIAIS.LOUIS_DREYFUS)
                        {
                            // proxima medicao
                            continue;
                        }
                    }
                    else
                    {
                        // se for preferencial nao eh o momento de enviar
                        if (medicao.IDCliente == CLIENTES_ESPECIAIS.JBS || medicao.IDCliente == CLIENTES_ESPECIAIS.LOUIS_DREYFUS)
                        {
                            // proxima medicao
                            continue;
                        }
                    }

                    // verifica se medição possui gestor
                    string LogoConsultor = "LogoSmartEnergy.png";

                    if (medicao.IDConsultor > 0)
                    {
                        // logo do gestor
                        UsuarioDominio consultor = usuarios.Find(u => u.IDUsuario == medicao.IDConsultor);

                        if (consultor != null)
                        {
                            // copia logo do gestor
                            if (consultor.LogoConsult != null)
                            {
                                if (consultor.LogoConsult.Length > 0)
                                {
                                    LogoConsultor = consultor.LogoConsult;
                                }
                            }
                        }
                    }

                    // busca todos os usuários que desejam enviar relatório desta medição deste periodo
                    List<UsuarioDominio> usuarios_relatorio = VerificaUsuario(medicao.IDMedicao, Periodo, usuarios);

                    // verifica se possui usuário para enviar
                    if (usuarios_relatorio != null)
                    {
                        if (usuarios_relatorio.Count > 0)
                        {
                            // verifica se energia
                            if (medicao.IDTipoMedicao == TIPO_MEDICAO.ENERGIA || medicao.IDTipoMedicao == TIPO_MEDICAO.ENERGIA_FORMULA)
                            {
                                // envia relatório PDF (consumo ativo)
                                if (Relat_Grafico_PDF(medicao, 2, Periodo, dataRelat, LogoConsultor, usuarios_relatorio))
                                {
                                    // relatório com erro
                                    Num_Relatorios_Erro++;

                                    // erro no relatório, próximo
                                    continue;
                                }

                                // envia relatório PDF (demanda ativa)
                                if (Relat_Grafico_PDF(medicao, 0, Periodo, dataRelat, LogoConsultor, usuarios_relatorio))
                                {
                                    // relatório com erro
                                    Num_Relatorios_Erro++;

                                    // erro no relatório, próximo
                                    continue;
                                }

                                // envia relatório PDF (fator de potência)
                                if (Relat_Grafico_PDF(medicao, 3, Periodo, dataRelat, LogoConsultor, usuarios_relatorio))
                                {
                                    // relatório com erro
                                    Num_Relatorios_Erro++;

                                    // erro no relatório, próximo
                                    continue;
                                }

                                // envia relatório PDF (supervisão de energia)
                                if (Periodo == 0)
                                {
                                    if (Relat_Grafico_PDF(medicao, 100, Periodo, dataRelat, LogoConsultor, usuarios_relatorio))
                                    {
                                        // relatório com erro
                                        Num_Relatorios_Erro++;
                                    }
                                }
                            }

                            // verifica se utilidades
                            if (medicao.IDTipoMedicao == TIPO_MEDICAO.UTILIDADES || medicao.IDTipoMedicao == TIPO_MEDICAO.UTILIDADES_FORMULA)
                            {
                                // envia relatório PDF (utilidades)
                                if (Relat_Grafico_PDF(medicao, 20, Periodo, dataRelat, LogoConsultor, usuarios_relatorio))
                                {
                                    // relatório com erro
                                    Num_Relatorios_Erro++;
                                }
                            }

                            // verifica se entrada analógica
                            if (medicao.IDTipoMedicao == TIPO_MEDICAO.ENTRADA_ANALOGICA)
                            {
                                // envia relatório PDF (entrada analógica)
                                if (Relat_Grafico_PDF(medicao, 30, Periodo, dataRelat, LogoConsultor, usuarios_relatorio))
                                {
                                    // relatório com erro
                                    Num_Relatorios_Erro++;
                                }
                            }

                            // verifica se ciclometro
                            if (medicao.IDTipoMedicao == TIPO_MEDICAO.CICLOMETRO)
                            {
                                // envia relatório PDF (ciclometro)
                                if (Relat_Grafico_PDF(medicao, 40, Periodo, dataRelat, LogoConsultor, usuarios_relatorio))
                                {
                                    // relatório com erro
                                    Num_Relatorios_Erro++;
                                }
                            }
                        }
                    }
                }
            }

            // diferença de tempo
            DateTime datahora_agora = System.DateTime.Now;
            TimeSpan diff = datahora_agora.Subtract(DataHoraInicio);

            string mensagem = string.Format("Número de Relatórios enviados = {0} em {1:00}'{2:00}\"{3:000}", Num_Relatorios, diff.Minutes, diff.Seconds, diff.Milliseconds);
            string mensagem_erro = "Todos Relatórios gerados com sucesso.";
            string relatorios_erro = "";

            if (Num_Relatorios_Erro > 0)
            {
                // erro
                mensagem_erro = string.Format("Número de Relatórios com problemas = {0}", Num_Relatorios_Erro);

                // apresenta erros
                foreach (string relat_err in Relatorios_Erro)
                {
                    relatorios_erro += relat_err + " <br>";
                }
            }

            // envia email estatistica
            EnviaEmailEstatistica(mensagem, mensagem_erro, relatorios_erro);

            // log
            LogMessage(mensagem);


            // retorna
            return;
        }


        // Relatorio Grafico PDF
        private bool Relat_Grafico_PDF(CliGrupoUnidMedicoesDominio medicao, int TipoRelat, int TipoPeriodo, DateTime dataRelat, string LogoConsultor, List<UsuarioDominio> usuarios_relatorio)
        {
            try
            {
                //
                // USUÁRIO
                //

                // usuários a enviar
                List<UsuarioDominio> usuarios_enviar = new List<UsuarioDominio>();

                // tipo do relatório
                switch (TipoRelat)
                {
                    case 0:     // Demanda Ativa

                        // Demanda Ativa
                        usuarios_enviar = usuarios_relatorio.FindAll(u => u.DemAtv == 1);
                        break;

                    case 2:     // Consumo Ativo

                        // Consumo Ativo
                        usuarios_enviar = usuarios_relatorio.FindAll(u => u.Consumo == 1);
                        break;

                    case 3:     // Fator de Potencia

                        // Fator de Potência
                        usuarios_enviar = usuarios_relatorio.FindAll(u => u.FatPot == 1);
                        break;

                    case 20:    // Utilidades
                    case 30:    // Entrada Analógica
                    case 40:    // Ciclometro

                        // Utilidades / Analógicas / Ciclômetro
                        usuarios_enviar = usuarios_relatorio.FindAll(u => u.Util_Anal_Ciclo == 1);
                        break;

                    case 100:   // Supervisão de Energia

                        // Supervisão de Energia
                        usuarios_enviar = usuarios_relatorio.FindAll(u => u.Supervisao == 1);
                        break;
                }

                // verifica se possui usuário para enviar
                if (usuarios_enviar == null)
                {
                    // ok
                    return (false);
                }

                if (usuarios_enviar.Count == 0)
                {
                    // ok
                    return (false);
                }


                //
                // PREPARA RELATÓRIO
                //

                // log
                LogMessage(string.Format("Relat Medição {0:000000}", medicao.IDMedicao));

                //
                // unidade de potencia
                //
                string UnidadeDemanda = "W";
                string UnidadeReativo = "var";

                if (medicao.IDTipoUnidadePotencia == 1)
                {
                    UnidadeDemanda = "kW";
                    UnidadeReativo = "kvar";
                }

                string UnidadeConsumo = UnidadeDemanda + "h";

                ViewBag.UnidadeDemanda = UnidadeDemanda;
                ViewBag.UnidadeReativo = UnidadeReativo;
                ViewBag.UnidadeConsumo = UnidadeConsumo;

                //
                // cabeçalho
                //

                ViewBag._LogoConsultor = LogoConsultor;
                ViewBag.ClienteNome = medicao.NomeCliente;
                ViewBag.GrupoNome = medicao.NomeGrupoUnidades;
                ViewBag.UnidadeNome = medicao.NomeUnidade;
                ViewBag.MedicaoNome = medicao.NomeMedicao;

                //
                // calcula relatório
                //

                // View do relatorio
                string viewRelatorio = "";

                // retorno
                int retorno = 0;
                string descricao_erro = "";

                // tipo do relatorio
                switch (TipoRelat)
                {
                    case 0: // Demanda Ativa

                        switch (TipoPeriodo)
                        {
                            case 0: // Diario
                                retorno = Dem_Ativa_Diario(medicao, dataRelat);
                                viewRelatorio = "_Dem_Ativa_Diario_Area";

                                // verifica se gráfico em barra
                                if (medicao.IDTipoGrafico_Demanda == 0)
                                {
                                    viewRelatorio = "_Dem_Ativa_Diario_Barra";
                                }

                                break;

                            case 1: // Semanal
                                retorno = Dem_Ativa_Semanal(medicao, dataRelat);
                                viewRelatorio = "_Dem_Ativa_Semanal";
                                break;

                            case 2: // Mensal
                                retorno = Dem_Ativa_Mensal(medicao, dataRelat);
                                viewRelatorio = "_Dem_Ativa_Mensal";
                                break;
                        }

                        break;

                    case 2: // Consumo Ativo

                        switch (TipoPeriodo)
                        {
                            case 0: // Diario
                                retorno = Consumo_Diario(medicao, dataRelat);
                                viewRelatorio = "_Consumo_Diario";
                                break;

                            case 1: // Semanal
                                retorno = Consumo_Semanal(medicao, dataRelat);
                                viewRelatorio = "_Consumo_Semanal";
                                break;

                            case 2: // Mensal
                                retorno = Consumo_Mensal(medicao, dataRelat);
                                viewRelatorio = "_Consumo_Mensal";
                                break;
                        }

                        break;

                    case 3: // Fator de Potência

                        switch (TipoPeriodo)
                        {
                            case 0: // Diario
                                retorno = FatPot_Diario(medicao, dataRelat);
                                viewRelatorio = "_FatPot_Diario";
                                break;

                            case 1: // Semanal
                                retorno = FatPot_Semanal(medicao, dataRelat);
                                viewRelatorio = "_FatPot_Semanal";
                                break;

                            case 2: // Mensal
                                retorno = FatPot_Mensal(medicao, dataRelat);
                                viewRelatorio = "_FatPot_Mensal";
                                break;
                        }

                        break;

                    case 20: // Utilidades

                        switch (TipoPeriodo)
                        {
                            case 0: // Diario
                                retorno = Utilidades_Diario(medicao, dataRelat);
                                viewRelatorio = "_Utilidades_Diario";
                                break;

                            case 1: // Semanal
                                retorno = Utilidades_Semanal(medicao, dataRelat);
                                viewRelatorio = "_Utilidades_Semanal";
                                break;

                            case 2: // Mensal
                                retorno = Utilidades_Mensal(medicao, dataRelat);
                                viewRelatorio = "_Utilidades_Mensal";
                                break;
                        }

                        break;

                    case 30: // Entradas Analogicas

                        switch (TipoPeriodo)
                        {
                            case 0: // Diario
                                retorno = EA_Diario(medicao, dataRelat);
                                viewRelatorio = "_EA_Diario";
                                break;

                            case 1: // Semanal
                                retorno = EA_Semanal(medicao, dataRelat);
                                viewRelatorio = "_EA_Semanal";
                                break;

                            case 2: // Mensal
                                retorno = EA_Mensal(medicao, dataRelat);
                                viewRelatorio = "_EA_Mensal";
                                break;
                        }

                        break;

                    case 40: // ciclometro

                        switch (TipoPeriodo)
                        {
                            case 0: // Diario
                                retorno = Ciclometro_Diario(medicao, dataRelat);
                                viewRelatorio = "_Ciclometro_Diario";
                                break;

                            case 1: // Semanal
                                retorno = Ciclometro_Semanal(medicao, dataRelat);
                                viewRelatorio = "_Ciclometro_Semanal";
                                break;

                            case 2: // Mensal
                                retorno = Ciclometro_Mensal(medicao, dataRelat);
                                viewRelatorio = "_Ciclometro_Mensal";
                                break;
                        }

                        break;

                    case 100: // Supervisão de Energia

                        retorno = Supervisao_Energia(medicao, dataRelat);
                        viewRelatorio = "_Supervisao_Energia";

                        if (medicao.Funcionamento_DemMin > 0)
                        {
                            viewRelatorio = "_Supervisao_Energia_Desperdicio";
                        }

                        break;
                }

                // relatório OK
                if (retorno == 0)
                {
                    // log
                    LogMessage(">>>>>> Cálculou Relatório");

                    // nome do arquivo
                    string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMdd}", viewRelatorio, medicao.IDMedicao, dataRelat);

                    // partial view
                    string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                    // buffer
                    byte[] dataBuffer;

                    // tipo do anexo
                    string attachmentType = MediaTypeNames.Application.Pdf;

                    // verifica qual formato do anexo
                    if (TipoAnexo_Email == 0)
                    {
                        //
                        // PDF
                        //

                        // extesão do arquivo
                        nomeArquivo += ".pdf";

                        // tipo do anexo
                        attachmentType = MediaTypeNames.Application.Pdf;


                        // rodape
                        string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                        // PartialView como PDF
                        var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                        {
                            FileName = nomeArquivo,
                            PageOrientation = Rotativa.Options.Orientation.Portrait,
                            PageSize = Size.A4,
                            PageMargins = new Rotativa.Options.Margins(10, 10, 0, 10),
                            CustomSwitches = footer,
                            IsLowQuality = true
                        };

                        dataBuffer = pdfResult.BuildFile(this.ControllerContext);

                        // log
                        LogMessage(">>>>>> Construiu PDF");
                    }
                    else
                    {
                        //
                        // PNG
                        //

                        // extesão do arquivo
                        nomeArquivo += ".png";

                        // tipo do anexo
                        attachmentType = "image/png";

                        // PartialView como PNG
                        var imageResult = new Rotativa.PartialViewAsImage(viewPartial)
                        {
                            Format = ImageFormat.png,
                            Quality = 1,
                            PageHeight = 1270,
                            PageWidth = 900,
                            FileName = nomeArquivo
                        };

                        dataBuffer = imageResult.BuildFile(this.ControllerContext);

                        // log
                        LogMessage(">>>>>> Construiu PNG");
                    }

                    //
                    // SALVA ARQUIVO
                    //

                    if (SalvarArquivo_Servidor)
                    {
                        // salva em arquivo
                        var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                        var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                        fileStream.Write(dataBuffer, 0, dataBuffer.Length);
                        fileStream.Close();

                        // log
                        LogMessage(">>>>>> Salvou arquivo");
                    }

                    //
                    // Verifica se CPFL
                    //
                    string complemento_template = "";

                    if (medicao.IDConsultor == CLIENTES_ESPECIAIS.CPFL)
                    {
                        complemento_template = "_CPFL";
                    }

                    //
                    // ENVIA EMAIL PARA USUARIOS
                    //
                    // Existem 3 possibilidades para otimizar o tempo de gerar o anexo e enviar o email:
                    // - Envio do email imediatamente após gerar o anexo 
                    // - Salvar o email na tabela EmailQueue para o processo EnviaEmailPendente fazer o envio em posterior
                    //   (o problema aqui é o tamanho dos anexos, o EnviaEmailPendente deve apagar o registro assim que enviar o email)
                    // - Mudar o tipo do anexo de PDF para PNG
                    //   (foi feito um teste e a qualidade e tamanho gerado não compensa)

                    // verifica se apenas 1 usuário
                    if (usuarios_enviar.Count == 1)
                    {
                        // envia email para 1 destinatário
                        EnviaEmail(usuarios_enviar[0], nomeArquivo, attachmentType, dataBuffer, complemento_template);
                    }
                    else
                    {
                        // envia email para todos destinatários simultaneamente
                        EnviaBulkEmail(usuarios_enviar, nomeArquivo, attachmentType, dataBuffer, complemento_template);
                    }

                    // log
                    LogMessage("------");

                    // estatística
                    Num_Relatorios++;

                    // ok
                    return (false);
                }

                //
                // Erro no relatório
                //

                switch (retorno)
                {
                    case -2:    // -2 - erro : tipo de interface indefinida
                        descricao_erro = "Tipo de interface indefinida";
                        break;

                    case -1:    // -1 - erro : tipo de relatorio indefinido
                        descricao_erro = "Tipo de relatorio indefinido";
                        break;

                    case 0:     // 0  - ok e com dados
                        break;

                    case 1:     // 1  - erro : nao foi possivel abrir o banco de dados
                        descricao_erro = "Erro ao abrir o banco de dados";
                        break;

                    case 2:     // 2  - erro : nao existem dados
                        descricao_erro = "Não existem dados";
                        break;

                    case 3:     // 3  - erro : leitura da configuracao da medicao
                        descricao_erro = "Configuracao da medição";
                        break;

                    case 4:     // 4  - erro : leitura das tarifas de energia
                        descricao_erro = "Tarifas de energia";
                        break;
                }

                // verifica se supervisão
                if (TipoRelat == 100)
                {
                    switch (retorno)
                    {
                        case -2:    // -2 - erro : tipo de interface indefinida
                            descricao_erro = "Tipo de interface indefinida";
                            break;

                        case -1:    // -1 - erro : ultima data e hora nao encontrada
                            descricao_erro = "Última data e hora não encontrada";
                            break;

                        case 0:     // 0  - ok e com dados
                            break;

                        case 1:     // 1  - erro : nao foi possivel abrir o banco de dados
                            descricao_erro = "Erro ao abrir o banco de dados";
                            break;

                        case 2:     // 2  - erro : nao existem dados
                            descricao_erro = "Não existem dados";
                            break;

                        case 3:     // 3  - OK, mas falta registros
                            break;
                    }
                }


                // log
                string erro = string.Format("[ERRO] Erro ao calcular {0} da Medição [{1:000000}] Data [{2:dd/MM/yyyy}] - Erro [{3}] {4}", viewRelatorio, medicao.IDMedicao, dataRelat, retorno, descricao_erro);
                LogMessage(erro);

                // relatório erro
                string relat_erro = string.Format("{0} da Medição [{1:000000}] Data [{2:dd/MM/yyyy}] - Erro [{3}] {4}", viewRelatorio, medicao.IDMedicao, dataRelat, retorno, descricao_erro);
                Relatorios_Erro.Add(relat_erro);

                // erro
                return (true);
            }
            catch (Exception ex)
            {
                LogMessage("[ERRO] " + ex.Message);
            }

            // erro
            return (true);
        }

        // envia email
        private void EnviaEmail(UsuarioDominio usuario, string attachmentName, string attachmentType, byte[] attachment, string complemento_template)
        {
            // assunto
            string assunto = "[Smart Energy] " + ViewBag.NomeRelat + " - " + ViewBag.ClienteNome + " - " + ViewBag.MedicaoNome; 

            // envia EMAIL
            var emailTemplate = "EnviaRelatoriosEmail" + complemento_template;

            var message = EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.UsuarioNome", usuario.NomeUsuario);
            message = message.Replace("ViewBag.LogoConsultor", ViewBag._LogoConsultor);
            message = message.Replace("ViewBag.NomeRelat", ViewBag.NomeRelat);
            message = message.Replace("ViewBag.PeriodoRelat", ViewBag.PeriodoRelat);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.GrupoNome", ViewBag.GrupoNome);
            message = message.Replace("ViewBag.UnidadeNome", ViewBag.UnidadeNome);
            message = message.Replace("ViewBag.MedicaoNome", ViewBag.MedicaoNome);
            message = message.Replace("ViewBag.DiaAtual", ViewBag.DataAtual);

            EmailServices.SendEmail(usuario.Email, assunto, message, attachmentName, attachmentType, attachment);

            // log
            LogMessage(string.Format(">>>>>> Enviou email para usuário [{0:000000}] {1}", usuario.IDUsuario, usuario.Email));

            return;
        }

        // envia email para vários destinatários
        private void EnviaBulkEmail(List<UsuarioDominio> usuarios, string attachmentName, string attachmentType, byte[] attachment, string complemento_template)
        {
            // assunto
            string assunto = "[Smart Energy] " + ViewBag.NomeRelat + " - " + ViewBag.ClienteNome + " - " + ViewBag.MedicaoNome; 

            // envia EMAIL
            var emailTemplate = "EnviaRelatoriosEmail" + complemento_template;

            var message = EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.UsuarioNome", "Olá");
            message = message.Replace("ViewBag.LogoConsultor", ViewBag._LogoConsultor);
            message = message.Replace("ViewBag.NomeRelat", ViewBag.NomeRelat);
            message = message.Replace("ViewBag.PeriodoRelat", ViewBag.PeriodoRelat);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.GrupoNome", ViewBag.GrupoNome);
            message = message.Replace("ViewBag.UnidadeNome", ViewBag.UnidadeNome);
            message = message.Replace("ViewBag.MedicaoNome", ViewBag.MedicaoNome);
            message = message.Replace("ViewBag.DiaAtual", ViewBag.DataAtual);

            // lista de emails
            List<string> emails = new List<string>();

            // percorre usuários
            foreach (UsuarioDominio usuario in usuarios)
            {
                // coloca na lista
                emails.Add(usuario.Email);
            }

            // envia
            EmailServices.SendBulkEmail(emails, assunto, message, attachmentName, attachmentType, attachment);

            // log
            foreach (UsuarioDominio usuario in usuarios)
            {
                // log
                LogMessage(string.Format(">>>>>> Enviou email para usuário [{0:000000}] {1}", usuario.IDUsuario, usuario.Email));
            }

            return;
        }

        // envia email estatistica
        private void EnviaEmailEstatistica(string mensagem, string mensagem_erro, string relatorios_erro)
        {
            // assunto
            string assunto = "[Smart Energy] Envio de Relatórios";

            // envia EMAIL
            var emailTemplate = "EnviaRelatoriosEstatisticaEmail";

            var message = EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.UsuarioNome", "Junior");
            message = message.Replace("ViewBag.LogoConsultor", "LogoSmartEnergy.png");
            message = message.Replace("ViewBag.Mensagem", mensagem);
            message = message.Replace("ViewBag.ErroMensagem", mensagem_erro);
            message = message.Replace("ViewBag.RelatoriosErro", relatorios_erro);

            EmailServices.SendEmail("<EMAIL>", assunto, message, "", "", null);

            // log
            LogMessage(">>>>>> Enviou email estatísticas");

            return;
        }


        public static string EMailTemplate(string template)
        {
            var templateFilePath = HostingEnvironment.MapPath("~/Content/emailTemplates/") + template + ".cshtml";
            StreamReader objstreamreaderfile = new StreamReader(templateFilePath);
            var body = objstreamreaderfile.ReadToEnd();
            objstreamreaderfile.Close();
            return body;
        }

        // verifica se usuário deseja receber relatório
        private List<UsuarioDominio> VerificaUsuario(int IDMedicao, int TipoPeriodo, List<UsuarioDominio> usuarios)
        {
            // busca todos os usuários que desejam enviar relatório desta medição
            List<UsuarioDominio> usuarios_relatorio = new List<UsuarioDominio>();

            // percorre usuarios
            foreach (UsuarioDominio usuario in usuarios)
            {
                // Flags:
                //
                // Diario  = relatório diário
                // Semanal = relatório semanal
                // Mensal  = relatório mensal
                //
                // DemAtv          = relatório de Demanda Ativa
                // FatPot          = relatório de Fator de Potência
                // Consumo         = relatório de Consumo
                // Util_Anal_Ciclo = relatório de Utilidades/Analogicas/Ciclometro
                // Supervisao      = informações de supervisão
                //
                // ConfigMedEmail = contém a lista de medições separadas por '/' = "/1/2/" medições 1 e 2
                //

                // verifica se usuário deseja receber relatório
                if ((TipoPeriodo == 0 && usuario.Diario == 1) || (TipoPeriodo == 1 && usuario.Semanal == 1) || (TipoPeriodo == 2 && usuario.Mensal == 1))
                {
                    if (usuario.DemAtv == 1 || usuario.FatPot == 1 || usuario.Consumo == 1 || usuario.Supervisao == 1 || usuario.Util_Anal_Ciclo == 1)
                    {
                        // verifica se usuário deseja receber relatório desta medição
                        string str_medicao = string.Format("/{0}/", IDMedicao);

                        if (usuario.ConfigMedEmail != null)
                        {
                            if (usuario.ConfigMedEmail.Contains(str_medicao))
                            {
                                // usuário deseja receber relatório desta medição
                                usuarios_relatorio.Add(usuario);
                            }
                        }
                    }
                }
            }

            return (usuarios_relatorio);
        }

        private void LogMessage(string msg, string arquivo = null)
        {
            // log
            Funcoes_Log.Mensagem_EnviaRelatorio(msg, Periodo_Relatorio, DataHoraAtual);
        }
    }
}
