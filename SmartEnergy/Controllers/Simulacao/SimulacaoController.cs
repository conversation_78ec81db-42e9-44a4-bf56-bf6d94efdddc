﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class SimulacaoController : Controller
    {
        // GET: Simulacao Ativar
        public ActionResult Simulacao_Ativar(int IDMedicao)
        {
            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // Simulacoes Medicao
        public PartialViewResult _Simulacoes(int IDMedicao)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // lista simulacao de cenarios
            SimulacaoCenariosMetodos simulacaoCenariosMetodos = new SimulacaoCenariosMetodos();
            List<SimulacaoCenariosDominio> simulacoesCenarios = simulacaoCenariosMetodos.ListarPorIDMedicao(IDMedicao);

            // copia para view 
            ViewBag.SimulacoesCenario = simulacoesCenarios;

            return PartialView();
        }

        // GET: Simulacao  - Editar
        public PartialViewResult _Simulacoes_Editar(int IDSimulacaoCenario, int IDMedicao)
        {

            if (IDSimulacaoCenario > 0)
            {
                // salva cookie
                CookieStore.SalvaCookie_Int("_IDSimulacaoCenario", IDSimulacaoCenario);
            }

            // le cookies
            LeCookies_SmartEnergy();

            // le configuracao cenario
            SimulacaoCenariosDominio cenario = new SimulacaoCenariosDominio();
            SimulacaoCenariosMetodos cenariosMetodos = new SimulacaoCenariosMetodos();

            // le configuracao cargas
            SimulacaoCargasMetodos cargasMetodos = new SimulacaoCargasMetodos();
            List<SimulacaoCargasDominio> cargas = new List<SimulacaoCargasDominio>();

            // verifica se adicionando 
            if (IDSimulacaoCenario == 0)
            {
                // zera com default 
                cenario.IDSimulacaoCenario = 0;
                cenario.IDMedicao = IDMedicao;
                cenario.Nome = "";
                cenario.ContratoDemFP = 0;
                cenario.ContratoDemP = 0;
            }
            else
            {
                cenario = cenariosMetodos.ListarPorID(IDSimulacaoCenario);

                // le configuracao cargas
                cargas = cargasMetodos.ListarPorIDSimulacaoCenario(IDSimulacaoCenario);
            }

            ViewBag.SimulacaoCenario = cenario;
            ViewBag.SimulacoesCargas = cargas;

            return PartialView();
        }

        // GET: Carga  - Editar
        public PartialViewResult _Simulacoes_Cargas_Editar(int IDSimulacaoCarga, int IDSimulacaoCenario)
        {
            // salva cookie
            CookieStore.SalvaCookie_Int("_IDSimulacaoCenario", IDSimulacaoCenario);

            // le cookies
            LeCookies_SmartEnergy();

            // le carga
            SimulacaoCargasDominio carga = new SimulacaoCargasDominio();
            SimulacaoCargasMetodos cargasMetodos = new SimulacaoCargasMetodos();

            // le simulacao de cenario
            SimulacaoCenariosMetodos cenariosMetodos = new SimulacaoCenariosMetodos();
            SimulacaoCenariosDominio cenario = cenariosMetodos.ListarPorID(IDSimulacaoCenario);

            // lista tipo carga 
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoCarga = listatiposMetodos.ListarTodos("TipoCarga", false, 0);
            ViewBag.listaTipoCarga = listatipoCarga;

            // verifica se adicionando 
            if (IDSimulacaoCarga == 0)
            {
                // zera com default 
                carga.IDSimulacaoCenario = IDSimulacaoCenario;
                carga.IDMedicao = carga.IDMedicao;
                carga.IDSimulacaoCarga = 0;
                carga.IDTipoCarga = 0;
                carga.Nome = "";
                carga.Potencia = 0.0;
                carga.FatPot = 1.0;
                carga.Adicionar = true;
                carga.HoraFim = "00:00";
                carga.HoraIni = "00:00";
                carga.Dom = true;
                carga.Seg = true;
                carga.Ter = true;
                carga.Qua = true;
                carga.Qui = true;
                carga.Sex = true;
                carga.Sab = true;
            }
            else
            {
                carga = cargasMetodos.ListarPorID(IDSimulacaoCarga);
            }

            ViewBag.SimulacaoCenario = cenario;
            ViewBag.SimulacaoCarga = carga;

            return PartialView();
        }

        // POST: Simulacao - Salvar
        [HttpPost]
        public ActionResult Simulacao_Cenario_Salvar(SimulacaoCenariosDominio cenario, List<SimulacaoCargasDominio> cargas)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                IDSimulacaoCenario = cenario.IDSimulacaoCenario,
                erro = ""
            };

            //  verifica se existe outro cenario com o mesmo nome
            SimulacaoCenariosMetodos cenariosMetodos = new SimulacaoCenariosMetodos();

            if (cenariosMetodos.VerificarDuplicidade(cenario))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    IDSimulacaoCenario = cenario.IDSimulacaoCenario,
                    erro = "Simulação de Cenário existente."
                };
            }

            else
            {
                // salva cenario 
                cenariosMetodos.Salvar(cenario);

                // pego IDSimulacaoCenario novamente (pois pode ter sido insercao)
                SimulacaoCenariosDominio novoCenario = new SimulacaoCenariosDominio();

                // verifica se alterando 
                if (cenario.IDSimulacaoCenario > 0)
                {
                    // caso alterando apenas copio do DB 
                    novoCenario = cenariosMetodos.ListarPorID(cenario.IDSimulacaoCenario);
                }

                else
                {
                    // caso inserindo faz leitura por IDMedicao e Nome para pegar novo ID
                    novoCenario = cenariosMetodos.ListarPorNomeMedicao(cenario.IDMedicao, cenario.Nome);
                }

                // salva cookie
                CookieStore.SalvaCookie_Int("_IDSimulacaoCenario", novoCenario.IDSimulacaoCenario);

                returnedData = new
                {
                    status = "OK",
                    IDSimulacaoCenario = novoCenario.IDSimulacaoCenario,
                    erro = ""
                };

                // salva lista de cargas 
                if (novoCenario != null)
                {
                    SimulacaoCargasMetodos cargasMetodos = new SimulacaoCargasMetodos();

                    // lista cargas atuais 
                    List<SimulacaoCargasDominio> cargasAntiga = cargasMetodos.ListarPorIDSimulacaoCenario(cenario.IDSimulacaoCenario);

                    // salva carga 
                    if (cargasAntiga != null)
                    {
                        // percorre lista e salva
                        foreach(SimulacaoCargasDominio carga in cargasAntiga)
                        {
                            // verifica se adicionando
                            if (carga.IDSimulacaoCarga == 0)
                            {
                                // zera com default 
                                carga.IDSimulacaoCenario = novoCenario.IDSimulacaoCenario;
                                carga.IDMedicao = novoCenario.IDMedicao;
                                carga.IDSimulacaoCarga = 0;
                                carga.IDTipoCarga = 0;
                                carga.Nome = "";
                                carga.Potencia = 0.0;
                                carga.FatPot = 1.0;
                                carga.Adicionar = true;
                                carga.HoraFim = "00:00";
                                carga.HoraIni = "00:00";
                                carga.Dom = true;
                                carga.Seg = true;
                                carga.Ter = true;
                                carga.Qua = true;
                                carga.Qui = true;
                                carga.Sex = true;
                                carga.Sab = true;
                            }

                            // salva carga 
                            cargasMetodos.Salvar(carga);
                        }
                    }
                }

            }

            // retorna status
            return Json(returnedData);
        }

        // POST: Simulacao Carga - Salvar
        [HttpPost]
        public ActionResult Simulacao_Carga_Salvar(SimulacaoCargasDominio carga)
        {
            // salva
            SimulacaoCargasMetodos cargasMetodos = new SimulacaoCargasMetodos();

            // se deseja retirar carga passo valor de potencia para negativo
            if (!carga.Adicionar) carga.Potencia = carga.Potencia * -1;                    

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se hora inicial é menor que hora final
            int horaIni = 0;
            int horaFim = 0;
            horaIni = int.Parse(carga.HoraIni.Split(':')[0]) * 60 + int.Parse(carga.HoraIni.Split(':')[1]);
            horaFim = int.Parse(carga.HoraFim.Split(':')[0]) * 60 + int.Parse(carga.HoraFim.Split(':')[1]);

            if ((horaIni >= horaFim && horaFim != 0))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Horário Início deve ser menor que o Horário Fim."
                };
            }

            else if (cargasMetodos.VerificarDuplicidade(carga))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Simulação de Cenário existente."
                };
            }

            else
            {
                // salva cenario 
                cargasMetodos.Salvar(carga);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: SimulacaoCenario - Excluir
        public ActionResult SimulacaoCenario_Excluir(int IDSimulacaoCenario)
        {
            // apaga a simulacao
            SimulacaoCenariosMetodos cenariosMetodos = new SimulacaoCenariosMetodos();
            cenariosMetodos.Excluir(IDSimulacaoCenario);

            // apaga cargas 
            SimulacaoCargasMetodos cargasMetodos = new SimulacaoCargasMetodos();
            cargasMetodos.ExcluirTodosIDSimulacaoCenario(IDSimulacaoCenario);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // GET: SimulacaoCarga - Excluir
        public ActionResult SimulacaoCarga_Excluir(int IDSimulacaoCarga)
        {
            // apaga carga
            SimulacaoCargasMetodos cargasMetodos = new SimulacaoCargasMetodos();
            cargasMetodos.Excluir(IDSimulacaoCarga);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}