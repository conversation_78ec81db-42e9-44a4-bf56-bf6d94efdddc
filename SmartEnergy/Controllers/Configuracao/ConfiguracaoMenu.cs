﻿using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ConfiguracaoController
    {
        // GET: Configuracao Menu
        public ActionResult Menu(int IDCliente)
        {
            // tela de ajuda - menu
            CookieStore.SalvaCookie_String("PaginaAjuda", "Menu_Clientes");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cliente = clienteMetodos.ListarPorId(IDCliente);

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente_Atual = ViewBag._IDCliente;

            // verifica se novo cliente selecionado
            if (IDCliente > 0 && IDCliente != IDCliente_Atual)
            {
                CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);

                // le contrato cliente
                CookieStore.SalvaCookie_Int("IDTipoContrato", cliente.IDTipoContrato);
                CookieStore.SalvaCookie_Int("IDTipoSupervisao", cliente.IDTipoSupervisao);
                CookieStore.SalvaCookie_String("Nome_GrupoUnidades", cliente.Nome_GrupoUnidades);
                CookieStore.SalvaCookie_String("Nome_Unidades", cliente.Nome_Unidades);
                CookieStore.SalvaCookie_Int("IDTipoGrafico_Demanda", cliente.IDTipoGrafico_Demanda);

                // limpa cookies
                CookieStore.SalvaCookie_Int("_IDMedicao", -10);
                CookieStore.SalvaCookie_Int("IDTipoMedicao", -10);
                CookieStore.SalvaCookie_Int("_IDGateway", -10);
                CookieStore.SalvaCookie_Int("IDTipoGateway", -10);
            }

            // le cookies
            LeCookies_SmartEnergy();

            return View(cliente);
        }
    }
}