﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ConfiguracaoController
    {
        // Listas utilizadas
        private void PreparaListas_Empresa(int IDCliente, int IDEstado = 0)
        {
            // le tipos empresa
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposEmpresa = listatiposMetodos.ListarTodos("TipoEmpresa", false);
            ViewBag.listaTipoEmpresa = listatiposEmpresa;

            // le tipos carteira
            List<ListaTiposDominio> listatiposCarteira = listatiposMetodos.ListarTodos("TipoCarteira", false);
            ViewBag.listaTipoCarteira = listatiposCarteira;

            // le tipos contrato status
            List<ListaTiposDominio> listatiposContratoStatus = listatiposMetodos.ListarTodos("TipoContratoStatus", false);
            ViewBag.listaTipoContratoStatus = listatiposContratoStatus;

            // le tipos índice de reajuste
            List<ListaTiposDominio> listatiposIndiceReajuste = listatiposMetodos.ListarTodos("TipoIndiceReajuste", false);
            ViewBag.listaTipoIndiceReajuste = listatiposIndiceReajuste;

            // le tipos vencimento
            List<ListaTiposDominio> listatiposVencimento = listatiposMetodos.ListarTodos("TipoVencimento", false);
            ViewBag.listaTipoVencimento = listatiposVencimento;

            // le tipos sim não
            List<ListaTiposDominio> listatiposSimNao = listatiposMetodos.ListarTodos("TipoSimNao", false);
            ViewBag.listaTipoSimNao = listatiposSimNao;

            // le tipos dias
            List<ListaTiposDominio> listatiposDias = listatiposMetodos.ListarTodos("TipoDias", false);
            ViewBag.listaTipoDias = listatiposDias;

            // le tipos meses
            List<ListaTiposDominio> listatiposMeses = listatiposMetodos.ListarTodos("TipoMeses", false);
            ViewBag.listaTipoMeses = listatiposMeses;

            // le tipos score
            List<ListaTiposDominio> listatiposScore = listatiposMetodos.ListarTodos("TipoScore", false);
            ViewBag.listaTipoScore = listatiposScore;

            // le tipos escopo
            List<ListaTiposDominio> listatiposEscopo = listatiposMetodos.ListarTodos("TipoEscopo", false);
            ViewBag.listaTipoEscopo = listatiposEscopo;

            // le tipos Paises
            CidadeEstadoPaisMetodos cidadeMetodos = new CidadeEstadoPaisMetodos();
            List<PaisesDominio> listatiposPais = cidadeMetodos.ListarTodosPaises();
            ViewBag.listaTipoPais = listatiposPais;
            
            // le tipos Estado
            List<EstadosDominio> listatiposEstado = cidadeMetodos.ListarTodosEstados();
            ViewBag.listaTipoEstado = listatiposEstado;

            // le tipos Cidade
            List<CidadesDominio> listatiposCidade = cidadeMetodos.ListarTodosCidades(IDEstado);
            ViewBag.listaTipoCidade = listatiposCidade;

            // le tipos fuso horário
            List<ListaTiposDominio> listatiposFusoHorario = listatiposMetodos.ListarTodos("TipoFusoHorario");
            ViewBag.listaTipoFusoHorario = listatiposFusoHorario;

            // le Contatos
            ContatosMetodos contatoMetodos = new ContatosMetodos();
            List<ContatosDominio> listaContatos = contatoMetodos.ListarPorIDCliente(IDCliente);
            ViewBag.listaContatos = listaContatos;

            // le comercializadoras
            AgentesMetodos agentesMetodos = new AgentesMetodos();
            List<AgentesDominio> listatiposComercializadoras = agentesMetodos.ListarPorComercializadora();
            ViewBag.listaTipoComercializadoras = listatiposComercializadoras;

            // le Empresas
            EmpresasMetodos empresaMetodos = new EmpresasMetodos();
            List<EmpresasDominio> agentesCCEE = empresaMetodos.ListarPorAgenteCCEE(IDCliente);
            ViewBag.listaAgentesCCEE = agentesCCEE;

            return;
        }

        // GET: Configuracao Empresas
        public ActionResult Empresas(int IDCliente)
        {
            // tela de ajuda - empresas
            CookieStore.SalvaCookie_String("PaginaAjuda", "Configuracao_Empresas");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_Empresa(IDCliente);

            // le empresas
            EmpresasMetodos empresasMetodos = new EmpresasMetodos();
            List<EmpresasDominio> listaEmpresas = empresasMetodos.ListarPorIDCliente(IDCliente);

            return View(listaEmpresas);
        }

        // GET: Configuracao Empresa - Editar
        // Retorno 0 - voltar para janela Empresas
        // Retorno 1 - voltar para janela Cliente_Editar (aba Matriz e Filiais)
        public ActionResult Empresa_Editar(int IDEmpresa, int Retorno = 0)
        {
            // tela de ajuda - empresas
            CookieStore.SalvaCookie_String("PaginaAjuda", "Configuracao_EmpresasEditar");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // pagina anterior
            string pagina_anterior = string.Format("/Configuracao/Empresas?IDCliente={0}", IDCliente);

            if (Retorno == 1)
            {
                pagina_anterior = string.Format("/Configuracao/Cliente_Editar?IDCliente={0}#tab-4", IDCliente);
            }

            ViewBag.returnUrl = pagina_anterior;

            // permissoes
            Permissoes();

            // verifica se adicionando
            EmpresasDominio empresa = new EmpresasDominio();
            if (IDEmpresa == 0)
            {
                // le informações do cliente atual
                ClientesMetodos clienteMetodos = new ClientesMetodos();
                ClientesDominio cliente = clienteMetodos.ListarPorId(IDCliente);

                if (cliente != null)
                {
                    empresa.IDCliente = cliente.IDCliente;
                    empresa.RazaoSocial = cliente.Nome;
                    empresa.SiglaCCEE = cliente.Fantasia;
                    empresa.Logo = cliente.Logo;
                }
                else
                {
                    empresa.IDCliente = ViewBag._IDCliente;
                    empresa.RazaoSocial = "";
                    empresa.SiglaCCEE = "";
                    empresa.Logo = "LogoSmartEnergy.png";
                }

                // zera empresa com default
                empresa.IDEmpresa = 0;
                empresa.IDTipoEmpresa = TIPO_EMPRESA.AgenteCCEE;
                empresa.IDEmpresa_AgenteCCEE = 0;

                empresa.Senha_Atendimento = "";
                empresa.Senha_Representante = "";

                empresa.CNPJ = "00.000.000/0000-00";

                empresa.Endereco = "";
                empresa.CEP = "";
                empresa.IDPais = 1;             // Brasil
                empresa.IDEstado = 26;          // São Paulo
                empresa.NomeEstado = "São Paulo";
                empresa.IDCidade = 5270;        // São Paulo
                empresa.NomeCidade = "São Paulo";
                empresa.Latitude = 0.0;
                empresa.Longitude = 0.0;
                empresa.IDTipoFusoHorario = 97; // (UTC-03:00) São Paulo

                empresa.IDCCEE = 0;
                empresa.IDPerfil = "";
                empresa.Agente = 0;

                empresa.DataMigracao_Previsao = new DateTime(DateTime.Now.Year, 1, 1, 0, 0, 0);
                empresa.DataMigracao_Previsao_Texto = empresa.DataMigracao_Previsao.ToString("d");
                empresa.DataMigracao = new DateTime(DateTime.Now.Year, 1, 1, 0, 0, 0);
                empresa.DataMigracao_Texto = empresa.DataMigracao.ToString("d");

                empresa.Contrato_Status = 0;
                empresa.Contrato_Gestora = 1;   // CPFL Soluções
                empresa.Carteira = 0;
                empresa.Escopo = 0;
                empresa.Score = 0;
                empresa.Contrato_Inicio = new DateTime(DateTime.Now.Year, 1, 1, 0, 0, 0);
                empresa.Contrato_Inicio_Texto = empresa.Contrato_Inicio.ToString("d");
                empresa.Contrato_Fim = new DateTime(DateTime.Now.Year, 1, 1, 0, 0, 0);
                empresa.Contrato_Fim_Texto = empresa.Contrato_Fim.ToString("d");
                empresa.Base_Valor = 0;
                empresa.Base_Data = new DateTime(DateTime.Now.Year, 1, 1, 0, 0, 0);
                empresa.Base_Data_Texto = empresa.Base_Data.ToString("MM/yyyy");

                empresa.Reajuste_Mes = 0;
                empresa.Reajuste_Indice = 0;
                empresa.Reajuste_Valor = 0;

                empresa.Vencimento_Tipo = 0;
                empresa.Vencimento_Dias = 0;
                empresa.RenovacaoAutomatica = false;

                empresa.FechamentoTR = new DateTime(DateTime.Now.Year, 1, 1, 0, 0, 0);
                empresa.FechamentoTR_Texto = empresa.FechamentoTR.ToString("d");

                empresa.MesRescisao = new DateTime(DateTime.Now.Year, 1, 1, 0, 0, 0);
                empresa.MesRescisao_Texto = empresa.MesRescisao.ToString("MM/yyyy");
                empresa.UltimoMesFaturamento = new DateTime(DateTime.Now.Year, 1, 1, 0, 0, 0);
                empresa.UltimoMesFaturamento_Texto = empresa.UltimoMesFaturamento.ToString("MM/yyyy");

                empresa.Observacao = "";
            }
            else
            {
                // le empresa
                EmpresasMetodos empresasMetodos = new EmpresasMetodos();
                empresa = empresasMetodos.ListarPorId(IDEmpresa);

                // verifica se é filial
                if (empresa.IDTipoEmpresa == TIPO_EMPRESA.Filial)
                {
                    // le empresa matriz
                    EmpresasDominio empresaMatriz = empresasMetodos.ListarPorId(empresa.IDEmpresa_AgenteCCEE);

                    if (empresaMatriz != null)
                    {
                        // copia informações
                        empresa.SiglaCCEE = empresaMatriz.SiglaCCEE;
                        empresa.IDCCEE = empresaMatriz.IDCCEE;
                        empresa.Senha_Atendimento = empresaMatriz.Senha_Atendimento;
                        empresa.Senha_Representante = empresaMatriz.Senha_Representante;
                        empresa.Score = empresaMatriz.Score;
                    }
                }
                else
                {
                    empresa.IDEmpresa_AgenteCCEE = 0;
                }
            }

            // prepara listas
            PreparaListas_Empresa(empresa.IDCliente, empresa.IDEstado);

            // le contatos da empresa
            EmpresasContatosMetodos empresaContatoMetodos = new EmpresasContatosMetodos();
            List<EmpresasContatosDominio> contatosEmpresa = empresaContatoMetodos.ListarTodosContatosIDEmpresa(empresa.IDEmpresa);

            List<ContatosDominio> contatos_utilizados = new List<ContatosDominio>();
            List<ContatosDominio> contatos_nao_utilizados = new List<ContatosDominio>();

            // le contatos do cliente
            List<ContatosDominio> listaContatos = ViewBag.listaContatos;

            // cria lista de contatos desta empresa
            List<int> IDContatosEmpresa = new List<int>();

            foreach (EmpresasContatosDominio contato in contatosEmpresa)
            {
                IDContatosEmpresa.Add(contato.IDContato);
            }

            // percorre contatos da empresa e pega informações de contato
            if (listaContatos != null)
            {
                // percorre contatos do cliente
                foreach (ContatosDominio contato in listaContatos)
                {
                    // novo contato
                    ContatosDominio novo_contato = new ContatosDominio();
                    novo_contato = contato;

                    // verifica se existe lista de contatos da empresa
                    if (IDContatosEmpresa != null)
                    {
                        // verifica se contato esta habilitado
                        if (IDContatosEmpresa.Contains(contato.IDContato))
                        {
                            // copia contato na lista de utilizadas
                            contatos_utilizados.Add(novo_contato);
                        }
                        else
                        {
                            // copia contato na lista de nao utilizadas
                            contatos_nao_utilizados.Add(novo_contato);
                        }
                    }
                    else
                    {
                        // copia contato na lista de nao utilizadas
                        contatos_nao_utilizados.Add(novo_contato);
                    }
                }
            }

            // contatos da empresa
            ViewBag.Contatos = contatos_utilizados;
            ViewBag.Contatos2 = contatos_nao_utilizados;

            // caso tiver cliente
            if (empresa.IDCliente > 0)
            {
                CookieStore.SalvaCookie_Int("_IDCliente", empresa.IDCliente);

                // le contrato cliente
                ClientesMetodos clienteMetodos = new ClientesMetodos();
                ClientesDominio cli = clienteMetodos.ListarPorId(empresa.IDCliente);
                CookieStore.SalvaCookie_Int("IDTipoContrato", cli.IDTipoContrato);

                // le cookies
                LeCookies_SmartEnergy();
            }

            return View(empresa);
        }


        //
        // CIDADES
        //

        // GET: Obter Cidades
        public JsonResult ObterCidades(int IDEstado)
        {
            // le tipos Cidade
            CidadeEstadoPaisMetodos cidadeMetodos = new CidadeEstadoPaisMetodos();
            List<CidadesDominio> listatiposCidade = cidadeMetodos.ListarTodosCidades(IDEstado);

            // retorna o valor em JSON
            return Json(listatiposCidade, JsonRequestBehavior.AllowGet);
        }


        //
        // EMPRESAS
        //

        // GET: Obter Empresa
        public JsonResult ObterEmpresa(int IDEmpresa)
        {
            // le empresa
            EmpresasMetodos empresaMetodos = new EmpresasMetodos();
            EmpresasDominio empresa = empresaMetodos.ListarPorId(IDEmpresa);

            // verifica
            if (empresa == null)
            {
                empresa = new EmpresasDominio();
            }

            // retorna o valor em JSON
            return Json(empresa, JsonRequestBehavior.AllowGet);
        }


        //
        // PROINFA
        //

        // GET: Obter PROINFA
        public JsonResult ObterPROINFA(int IDEmpresa)
        {
            // PROINFA
            EmpresasPROINFAMetodos proinfaMetodos = new EmpresasPROINFAMetodos();
            List<EmpresasPROINFADominio> proinfas = proinfaMetodos.ListarPorId(IDEmpresa);

            // retorna o valor em JSON
            return Json(proinfas, JsonRequestBehavior.AllowGet);
        }

        // converte MWm em MWh
        private double Converte_MWm_MWh(int mes, int ano, double montante)
        {
            // calcula número de horas do mês
            int num_horas = System.DateTime.DaysInMonth(ano, mes) * 24;

            // calcula contratado MWh do mês
            double contratado = montante * num_horas;

            // retorna PROINFA do mês
            return (contratado);
        }

        // copia PROINFA para o mês
        private void CopiaPROINFA_Mes(int mes, ref EmpresasPROINFADominio proinfa, double contratado)
        {

            // mes
            switch (mes)
            {
                case 1:     // janeiro
                    proinfa.Jan = contratado;
                    break;

                case 2:     // fevereiro
                    proinfa.Fev = contratado;
                    break;

                case 3:     // março
                    proinfa.Mar = contratado;
                    break;

                case 4:     // abril
                    proinfa.Abr = contratado;
                    break;

                case 5:     // maio
                    proinfa.Mai = contratado;
                    break;

                case 6:     // junho
                    proinfa.Jun = contratado;
                    break;

                case 7:     // julho
                    proinfa.Jul = contratado;
                    break;

                case 8:     // agosto
                    proinfa.Ago = contratado;
                    break;

                case 9:     // setembro
                    proinfa.Set = contratado;
                    break;

                case 10:     // outubro
                    proinfa.Out = contratado;
                    break;

                case 11:     // novembro
                    proinfa.Nov = contratado;
                    break;

                case 12:     // dezembro
                    proinfa.Dez = contratado;
                    break;
            }

            return;
        }

        // GET: PROINFA - Salvar
        public JsonResult SalvarPROINFA(int IDEmpresa, int Ano, double Jan, double Fev, double Mar, double Abr, double Mai, double Jun, double Jul, double Ago, double Set, double Out, double Nov, double Dez)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // PROINFA
            EmpresasPROINFADominio proinfa = new EmpresasPROINFADominio();

            proinfa.IDEmpresa = IDEmpresa;
            proinfa.Ano = Ano;
            proinfa.Jan = Jan;
            proinfa.Fev = Fev;
            proinfa.Mar = Mar;
            proinfa.Abr = Abr;
            proinfa.Mai = Mai;
            proinfa.Jun = Jun;
            proinfa.Jul = Jul;
            proinfa.Ago = Ago;
            proinfa.Set = Set;
            proinfa.Out = Out;
            proinfa.Nov = Nov;
            proinfa.Dez = Dez;

            // limpa PROINFA
            EmpresasPROINFAMetodos proinfaMetodos = new EmpresasPROINFAMetodos();
            proinfaMetodos.ExcluirAno(IDEmpresa, Ano);

            // adiciona PROINFA
            proinfaMetodos.Adicionar(proinfa);

            // retorna
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: PROINFA - Excluir
        public ActionResult ExcluirPROINFA(int IDEmpresa, int Ano)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // apaga o ano 
            EmpresasPROINFAMetodos proinfaMetodos = new EmpresasPROINFAMetodos();
            proinfaMetodos.ExcluirAno(IDEmpresa, Ano);

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        //
        // SALVAR e EXCLUIR
        //

        // POST: Configuracao Empresa - Salvar
        [HttpPost]
        public ActionResult Empresa_Salvar(EmpresasDominio empresa, List<EmpresasContatosDominio> contatos)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // parse datas
            empresa.DataMigracao_Previsao = DateTime.Parse(empresa.DataMigracao_Previsao_Texto);
            empresa.DataMigracao = DateTime.Parse(empresa.DataMigracao_Texto);
            empresa.Contrato_Inicio = DateTime.Parse(empresa.Contrato_Inicio_Texto);
            empresa.Contrato_Fim = DateTime.Parse(empresa.Contrato_Fim_Texto);
            empresa.Base_Data = DateTime.Parse(empresa.Base_Data_Texto);
            empresa.FechamentoTR = DateTime.Parse(empresa.FechamentoTR_Texto);
            empresa.MesRescisao = DateTime.Parse(empresa.MesRescisao_Texto);
            empresa.UltimoMesFaturamento = DateTime.Parse(empresa.UltimoMesFaturamento_Texto);


            // verifica se existe outra empresa com a mesma razão social
            EmpresasMetodos empresasMetodos = new EmpresasMetodos();
            if (empresasMetodos.VerificarDuplicidade(empresa))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Empresa existente."
                };
            }
            else
            {
                // verifica se é filial
                if (empresa.IDTipoEmpresa == TIPO_EMPRESA.Filial)
                {
                    // le empresa matriz
                    EmpresasDominio empresaMatriz = empresasMetodos.ListarPorId(empresa.IDEmpresa_AgenteCCEE);

                    if (empresaMatriz != null)
                    {
                        // copia informações da matriz
                        empresa.SiglaCCEE = empresaMatriz.SiglaCCEE;
                        empresa.IDCCEE = empresaMatriz.IDCCEE;
                        empresa.Senha_Atendimento = empresaMatriz.Senha_Atendimento;
                        empresa.Senha_Representante = empresaMatriz.Senha_Representante;
                        empresa.Score = empresaMatriz.Score;
                    }
                }
                else
                {
                    empresa.IDEmpresa_AgenteCCEE = 0;

                    // atualiza todas as filias deste agente
                    empresasMetodos.AlterarFiliais(empresa);
                }

                // verifica se é Brasil
                if (empresa.IDPais == 1)
                {
                    // le Estado
                    CidadeEstadoPaisMetodos cidadeMetodos = new CidadeEstadoPaisMetodos();
                    EstadosDominio estado = cidadeMetodos.EstadoPorId(empresa.IDEstado);
                    empresa.NomeEstado = estado.Nome;

                    // le  Cidade
                    CidadesDominio cidade = cidadeMetodos.CidadePorId(empresa.IDCidade);
                    empresa.NomeCidade = cidade.Nome;
                }
                else
                {
                    // estado e cidade invalido pelo ID
                    empresa.IDEstado = 0;
                    empresa.IDCidade = 0;
                }

                // salva empresa 
                empresasMetodos.Salvar(empresa);

                // verifica integridade
                int retorno = empresasMetodos.VerificarIntegridade(empresa);

                switch (retorno)
                {
                    case 1:
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "Empresa não adicionada."
                        };
                        break;

                    case 2:
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "IDEmpresa adicionada com valor alto.<br><br>FAVOR AVISAR O DESENVOLVIMENTO ANTES DE PROSSEGUIR !!!"
                        };
                        break;
                }

                if (retorno == 0)
                {
                    // pego IDEmpresa novamente (pois pode ter sido insercao)
                    EmpresasDominio empr = empresasMetodos.ListarPorNome(empresa.IDCliente, empresa.RazaoSocial, empresa.SiglaCCEE);

                    // verifica se salvou
                    if (empr != null)
                    {
                        // evento 
                        UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();

                        if (empresa.IDEmpresa > 0)
                        {
                            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.EMPRESA, empr.IDEmpresa);
                        }
                        else
                        {
                            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.EMPRESA, empr.IDEmpresa);
                        }

                        // salva contatos
                        EmpresasContatosMetodos empresaContatoMetodos = new EmpresasContatosMetodos();

                        // exclui todos os contatos desta empresa
                        empresaContatoMetodos.ExcluirEmpresa(empr.IDEmpresa);

                        // salva contatos
                        if (contatos != null)
                        {
                            // percorre lista e salva
                            foreach (EmpresasContatosDominio contato in contatos)
                            {
                                // preenche campos que faltam
                                contato.IDCliente =  empr.IDCliente;
                                contato.IDEmpresa = empr.IDEmpresa;

                                // salva
                                empresaContatoMetodos.Salvar(contato);
                            }
                        }
                    }
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuracao Empresa - Excluir
        public ActionResult Empresa_Excluir(int IDEmpresa)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existem gateways utilizando esta empresa
            GatewaysMetodos gatewayMetodos = new GatewaysMetodos();

            if (gatewayMetodos.NumGatewaysEmpresa(IDEmpresa) > 0)
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Esta Empresa está sendo utilizada pelas Gateways.<br><br>Verificar as Gateways antes de excluir."
                };
            }
            else
            {
                // apaga a empresa
                EmpresasMetodos empresasMetodos = new EmpresasMetodos();
                empresasMetodos.Excluir(IDEmpresa);

                // exclui todos os contatos desta empresa
                EmpresasContatosMetodos empresaContatoMetodos = new EmpresasContatosMetodos();
                empresaContatoMetodos.ExcluirEmpresa(IDEmpresa);

                // evento 
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.EXCLUI_CONFIG, TABELA_CONFIG.EMPRESA, IDEmpresa);
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }
    }
}