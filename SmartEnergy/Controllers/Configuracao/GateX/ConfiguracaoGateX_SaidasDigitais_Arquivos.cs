﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Saídas Digitais XLS Download
        [HttpGet]
        public virtual ActionResult GateX_SaidasDigitais_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Saídas Digitais XLS
        public ActionResult GateX_SaidasDigitais_XLS(int IDGateway)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_GateX_SaidasDigitais(IDGateway);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_SaidasDigitais_Gateway_{0:000000}_{1:yyyyMMddHHmm}.xls", IDGateway, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // Saídas Digitais Planilha
        private HSSFWorkbook XLS_GateX_SaidasDigitais(int IDGateway)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // SAÍDAS
            //

            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoSaidasDigitais;

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // le medicoes do banco de dados
            GateX_MedEner_Lista_Metodos medMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medicoes = medMetodos.ListarPorIDGateway(IDGateway);

            // le entradas banco de dados
            GateX_ED_Lista_Metodos edMetodos = new GateX_ED_Lista_Metodos();
            List<GateX_ED_Lista_Dominio> entradas = edMetodos.ListarPorIDGateway(IDGateway);

            //  le lista de saidas da gateway
            GateX_SD_Metodos sdMetodos = new GateX_SD_Metodos();
            List<GateX_SD_Dominio> saidas = sdMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // le tipos rede IO
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DI);      // remove DI
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.SEG);     // remove seg

            // le tipo comando tarifacao
            List<ListaTiposDominio> listatipoCmdTarifacao = listatiposMetodos.ListarTodos("TipoComandoTarifacao", false, 0);

            // le tipo alarme
            List<ListaTiposDominio> listatipoAlarme = listatiposMetodos.ListarTodos("TipoAlarmeStatus", false, 0);

            // le tipo comando alarme
            List<ListaTiposDominio> listatipoCmdAlarme = listatiposMetodos.ListarTodos("TipoCmdAlarme", false, 0);

            // le tipo alarme sistema
            List<ListaTiposDominio> listatipoAlarmeSistema = listatiposMetodos.ListarTodos("TipoAlarmeSistema", false, 0);

            // le tipo controle demanda projetada
            List<ListaTiposDominio> listatipoCtrPj = listatiposMetodos.ListarTodos("TipoCtrlDemandaPrj", false, 0);

            // le tipo controle fator de potencia indutivo
            List<ListaTiposDominio> listatipoCtrFPInd = listatiposMetodos.ListarTodos("TipoCtrlFPInd", false, 0);

            // le tipo controle fator de potencia capacitivo
            List<ListaTiposDominio> listatipoCtrFPCap = listatiposMetodos.ListarTodos("TipoCtrlFPCap", false, 0);

            // lista numero remotas
            int[] listaRemotas = new int[32];

            for (int j = 0; j < listaRemotas.Length; j++)
            {
                listaRemotas[j] = j;
            }

            // cria lista de alarmes do usuario
            List<ListaTiposDominio> listaAlmUsuario = new List<ListaTiposDominio>();

            for (int j = 0; j < 100; j++)
            {
                var alm = new ListaTiposDominio()
                {
                    ID = j,
                    Descricao = string.Format("Alarme de Usuário [{0:00}]", j)
                };

                listaAlmUsuario.Add(alm);
            }

            // cria planilha
            var sheet = workbook.CreateSheet("Saídas Digitais");

            // cabecalho
            string[] cabecalho = { "Saída Digital Interna", "Saídas Digitais", "Rede", "Remota", "Endereço", "Registrar Eventos de Temporização", "Lógica Invertida", "Acesso Remoto Habilitado", "Comando Entrada de Estado", "Comando Horário de Tarifação", "Desligar na Falha de Medição", "Comando Alarme", "Tipo Alarme", "Alarme", "Comando Supervisionado", "Tempo Alarme" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            IRow row;

            if (saidas != null)
            {
                // percorre clientes do consultor
                foreach (GateX_SD_Dominio saida in saidas)
                {
                    string desprogramado = "---";
                    string nomeSaida = desprogramado;
                    string rede = desprogramado;
                    string regEventos = desprogramado;
                    string logIvertida = desprogramado;
                    string remoto = desprogramado;
                    string cmdEntrada = desprogramado;
                    string cmdTarif = desprogramado;
                    string falhaMed = desprogramado;
                    string cmdAlm = desprogramado;
                    string tipoAlm = desprogramado;
                    string alm = desprogramado;
                    string cmdSuperv = desprogramado;
                    string tempAlm = desprogramado;

                    if (saida.Programado)
                    {
                        // descricao
                        nomeSaida = saida.Descricao;

                        // rede IO
                        if (listatipoRedeIO != null) rede = listatipoRedeIO.Find(x => x.ID == saida.RedeIO).Descricao;

                        // registrar eventos temporizacao
                        if (saida.RegEvTemporizador)
                        {
                            regEventos = "Sim";
                        }
                        else
                        {
                            regEventos = "Não";
                        }

                        // logica invrtida
                        if (saida.LogicaInvertida)
                        {
                            logIvertida = "Sim";
                        }
                        else
                        {
                            logIvertida = "Não";
                        }

                        // acesso remoto habilitado
                        if (saida.HabAcessoRemoto)
                        {
                            remoto = "Sim";
                        }
                        else
                        {
                            remoto = "Não";
                        }

                        // comando pela entrada de estado
                        if (saida.EEAtivaSaida >= 0 && saida.EEAtivaSaida <= 63 && entradas != null)
                        {
                            cmdEntrada = entradas.Find(x => x.NumEntradaGateway == saida.EEAtivaSaida).Descricao;
                        }

                        // comando pelo horario de tarifacao
                        if (saida.ComandoHrTarifador > 0 && listatipoCmdTarifacao != null)
                        {
                            cmdTarif = listatipoCmdTarifacao.Find(x => x.ID == saida.ComandoHrTarifador).Descricao;
                        }

                        // desligar em falha da medicao
                        if (saida.MedicaoFalhaDeslig >= 0 && saida.MedicaoFalhaDeslig < SGATEX.NUM_MED_EN && medicoes != null)
                        {
                            falhaMed = medicoes.Find(x => x.NumMedicaoGateway == saida.MedicaoFalhaDeslig).Descricao;
                        }

                        // comando alarme
                        if (saida.ComandoAlarme > 0)
                        {
                            if (listatipoAlarme != null) cmdAlm = listatipoAlarme.Find(x => x.ID == saida.ComandoAlarme).Descricao;

                            // tipo alarme
                            if (listatipoCmdAlarme != null) tipoAlm = listatipoCmdAlarme.Find(x => x.ID == saida.ComandoAlarmeTipo).Descricao;

                            // alarme
                            switch (saida.ComandoAlarmeTipo)
                            {
                                case 255: // alm usuario
                                    if (listaAlmUsuario != null) alm = listaAlmUsuario.Find(x => x.ID == saida.ComandoAlarmeAux).Descricao;

                                    break;

                                case 0: // alm sistema
                                    if (listatipoAlarmeSistema != null) alm = listatipoAlarmeSistema.Find(x => x.ID == saida.ComandoAlarmeAux).Descricao;

                                    break;

                                case 1: // falta pulsos
                                    if (medicoes != null) alm = medicoes.Find(x => x.NumMedicaoGateway == saida.ComandoAlarmeAux).Descricao;

                                    break;

                                case 2: // ultrapassagem de demanda
                                    if (listatipoCtrPj != null) alm = listatipoCtrPj.Find(x => x.ID == saida.ComandoAlarmeAux).Descricao;

                                    break;

                                case 3: // ultrapassagem fator de potencia indutivo
                                    if (listatipoCtrFPInd != null) alm = listatipoCtrFPInd.Find(x => x.ID == saida.ComandoAlarmeAux).Descricao;

                                    break;

                                case 4: // ultrapassagem fator de potencia capacitivo
                                    if (listatipoCtrFPCap != null) alm = listatipoCtrFPCap.Find(x => x.ID == saida.ComandoAlarmeAux).Descricao;

                                    break;

                                case 5: // comando supervisionado
                                    if (saidas != null) alm = saidas.Find(x => x.NumSaidaGateway == saida.ComandoAlarmeAux).Descricao;

                                    break;

                                default: // desprogramado
                                    alm = desprogramado;

                                    break;
                            }
                        }

                        // comando supervisionado
                        if (saida.Retrosinalizador >= 0 && saida.Retrosinalizador < SGATEX.NUM_ED && entradas != null)
                        {
                            cmdSuperv = entradas.Find(x => x.NumEntradaGateway == saida.Retrosinalizador).Descricao;
                        }

                        // tempo para alarme
                        if (saida.ComandoSuperv > 0)
                        {
                            tempAlm = string.Format("{0} seg.", saida.ComandoSuperv);
                        }
                    }

                    // adiciona linha
                    row = sheet.CreateRow(rowIndex++);

                    // Saída Digital Interna
                    numeroCelulaXLS(row, 0, saida.NumSaidaGateway, _intCellStyle);
                    sheet.SetColumnWidth(0, 6000);

                    // Nome saída
                    textoCelulaXLS(row, 1, saida.Descricao);
                    sheet.SetColumnWidth(1, 7000);

                    // rede IO
                    textoCelulaXLS(row, 2, rede);
                    sheet.SetColumnWidth(2, 4000);

                    if (saida.Programado && saida.Remota != 255)
                    {
                        // remota
                        numeroCelulaXLS(row, 3, saida.Remota, _intCellStyle);

                    }
                    else
                    {
                        // remota
                        textoCelulaXLS(row, 3, desprogramado);
                    }

                    sheet.SetColumnWidth(3, 3500);

                    if (saida.Programado && saida.Endereco >= 0 && saida.Endereco < 65000)
                    {
                        // endereco
                        numeroCelulaXLS(row, 4, saida.Endereco, _intCellStyle);
                    }
                    else
                    {
                        // endereco
                        textoCelulaXLS(row, 4, desprogramado);
                    }

                    sheet.SetColumnWidth(4, 3500);

                    // registrar eventos temporizacao
                    textoCelulaXLS(row, 5, regEventos);
                    sheet.SetColumnWidth(5, 8500);

                    // lógica invertida
                    textoCelulaXLS(row, 6, logIvertida);
                    sheet.SetColumnWidth(6, 5000);

                    // acesso remoto habilitado
                    textoCelulaXLS(row, 7, remoto);
                    sheet.SetColumnWidth(7, 7000);

                    // comando pela entrada de estado
                    textoCelulaXLS(row, 8, cmdEntrada);
                    sheet.SetColumnWidth(8, 9000);

                    // comando pelo horário de tarifacao
                    textoCelulaXLS(row, 9, cmdTarif);
                    sheet.SetColumnWidth(9, 10000);

                    // desligar na falha da medição 
                    textoCelulaXLS(row, 10, falhaMed);
                    sheet.SetColumnWidth(10, 7000);

                    // comando alarme
                    textoCelulaXLS(row, 11, cmdAlm);
                    sheet.SetColumnWidth(11, 10000);

                    // tipo alarme
                    textoCelulaXLS(row, 12, tipoAlm);
                    sheet.SetColumnWidth(12, 6500);

                    // alarme
                    textoCelulaXLS(row, 13, alm);
                    sheet.SetColumnWidth(13, 6500);

                    // comando supervisionado
                    textoCelulaXLS(row, 14, cmdSuperv);
                    sheet.SetColumnWidth(14, 8000);

                    // tempo alarme
                    textoCelulaXLS(row, 15, tempAlm);
                    sheet.SetColumnWidth(15, 4000);
                }
            }

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS_GateX(workbook, sheet, _negritoCellStyle);

            // largura de cada coluna
            sheet.SetColumnWidth(0, 7000);
            sheet.SetColumnWidth(1, 8000);

            // retorna planilha
            return workbook;
        }

        // cabecalho resumo XLS
        private int cabecalhoResumoXLS_GateX(HSSFWorkbook workbook, ISheet sheet, ICellStyle _negritoCellStyle)
        {
            int rowIndex = 0;

            // cria estilos
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _dmaStyle = criaEstiloXLS(workbook, 10);

            // cliente
            var row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Cliente", _negritoCellStyle);
            textoCelulaXLS(row, 1, ViewBag.ClienteNome);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // relatorio
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Gateway", _negritoCellStyle);
            textoCelulaXLS(row, 1, ViewBag.NomeGateway);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // relatorio
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Configuração do Equipamento", _negritoCellStyle);
            textoCelulaXLS(row, 1, ViewBag.DescrConfig);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // data
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Data", _negritoCellStyle);
            datahoraCelulaXLS(row, 1, DateTime.Now, _dataStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            return (rowIndex);
        }

        // GET: Saidas Digitais PDF
        public ActionResult GateX_SaidasDigitais_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista saidas
            GateX_SD_Metodos sdMetodos = new GateX_SD_Metodos();
            List<GateX_SD_Dominio> saidas = sdMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.Saidas = saidas;

            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_SaidasDigitais_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_SaidasDigitais_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        // GET: Saidas Digitais EMAIL
        public async Task<ActionResult> GateX_SaidasDigitais_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);


            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoSaidasDigitais;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista saidas
            GateX_SD_Metodos sdMetodos = new GateX_SD_Metodos();
            List<GateX_SD_Dominio> saidas = sdMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.Saidas = saidas;

            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_SaidasDigitais_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_SaidasDigitais_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "GateX_SaidasDigitaisEmail";
            var message = await EMailTemplateAsync(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.NomeGateway", ViewBag.NomeGateway);
            message = message.Replace("ViewBag.DescrConfig", ViewBag.DescrConfig);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Saidas Digitais Print
        public ActionResult GateX_SaidasDigitais_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista saidas
            GateX_SD_Metodos sdMetodos = new GateX_SD_Metodos();
            List<GateX_SD_Dominio> saidas = sdMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.Saidas = saidas;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // imprime
            return View();
        }
    }
}