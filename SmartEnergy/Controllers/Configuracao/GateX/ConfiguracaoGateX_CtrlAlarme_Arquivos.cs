﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Controle Alarme XLS Download
        [HttpGet]
        public virtual ActionResult GateX_CtrlAlarme_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Controle Alarme XLS
        public ActionResult GateX_CtrlAlarme_XLS(int IDGateway)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_GateX_CtrlAlarme(IDGateway);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_CtrlAlarme_Gateway_{0:000000}_{1:yyyyMMddHHmm}.xls", IDGateway, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // Controle Alarme Planilha
        private HSSFWorkbook XLS_GateX_CtrlAlarme(int IDGateway)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // LÓGICAS
            //

            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.ControleAlarme;

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            //  le lista de controle alarme da gateway
            GateX_CtrlAlarme_Metodos prgMetodos = new GateX_CtrlAlarme_Metodos();
            List<GateX_CtrlAlarme_Dominio> ctrls = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // le lista medições de energia
            GateX_MedEner_Lista_Metodos medMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medEner = medMetodos.ListarPorIDGateway(IDGateway);

            // le lista entradas digitais
            GateX_ED_Lista_Metodos edMetodos = new GateX_ED_Lista_Metodos();
            List<GateX_ED_Lista_Dominio> entradasDigitas = edMetodos.ListarPorIDGateway(IDGateway);

            // le lista saidas digitais
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitas = sdMetodos.ListarPorIDGateway(IDGateway);

            // le tipos alarmes
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoAlarme = listatiposMetodos.ListarTodos("TipoGateX_Alarme", false, 0);


            // cria planilha
            var sheet = workbook.CreateSheet("Controle Alarme");

            // cabecalho
            string[] cabecalho = { "Número", "Controle Alarme", "Origem", "Normaliza", "Ativa" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            IRow row;

            if (ctrls != null)
            {
                // percorre lógicas
                foreach (GateX_CtrlAlarme_Dominio ctrl in ctrls)
                {
                    string desprogramado = "---";
                    string nomeAlarme = desprogramado;
                    string origem = desprogramado;
                    string normalizaEm = desprogramado;
                    string ativaEm = desprogramado;

                    if (ctrl.Programado)
                    {
                        ListaTiposDominio tipo = listatipoAlarme.Find(x => x.ID == ctrl.TipoAlarme);
                        nomeAlarme = (tipo != null) ? tipo.Descricao : desprogramado;

                        normalizaEm = string.Format("{0}", ctrl.SetPointLo);
                        ativaEm = string.Format("{0}", ctrl.SetPointHi);

                        switch (ctrl.TipoAlarme)
                        {
                            case 0:
                                break;

                            case 6:
                                GateX_ED_Lista_Dominio tipoED = entradasDigitas.Find(x => x.NumEntradaGateway == ctrl.Medicao);
                                origem = (tipoED != null) ? tipoED.DescricaoNum : desprogramado;
                                break;

                            case 7:
                                GateX_SD_Lista_Dominio tipoSD = saidasDigitas.Find(x => x.NumSaidaGateway == ctrl.Medicao);
                                origem = (tipoSD != null) ? tipoSD.DescricaoNum : desprogramado;
                                break;

                            default:
                                GateX_MedEner_Lista_Dominio tipoMed = medEner.Find(x => x.NumMedicaoGateway == ctrl.Medicao);
                                origem = (tipoMed != null) ? tipoMed.DescricaoNum : desprogramado;
                                break;
                        }
                    }

                    // adiciona linha
                    row = sheet.CreateRow(rowIndex++);

                    // Controle Alarme
                    numeroCelulaXLS(row, 0, ctrl.NumAlarmeGateway, _intCellStyle);
                    sheet.SetColumnWidth(0, 6000);

                    // Alarme
                    textoCelulaXLS(row, 1, nomeAlarme);
                    sheet.SetColumnWidth(1, 7000);

                    // Origem
                    textoCelulaXLS(row, 2, origem);
                    sheet.SetColumnWidth(2, 7000);

                    // Normaliza
                    textoCelulaXLS(row, 3, normalizaEm);
                    sheet.SetColumnWidth(3, 4000);

                    // Ativa
                    textoCelulaXLS(row, 4, ativaEm);
                    sheet.SetColumnWidth(4, 4000);
                }
            }

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS_GateX(workbook, sheet, _negritoCellStyle);

            // largura de cada coluna
            sheet.SetColumnWidth(0, 7000);
            sheet.SetColumnWidth(1, 8000);

            // retorna planilha
            return workbook;
        }


        // GET: Controle Alarme PDF
        public ActionResult GateX_CtrlAlarme_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista controle alarme
            GateX_CtrlAlarme_Metodos prgMetodos = new GateX_CtrlAlarme_Metodos();
            List<GateX_CtrlAlarme_Dominio> ctrls = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlAlarme = ctrls;

            // le lista medições de energia
            GateX_MedEner_Lista_Metodos medMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medEner = medMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.listaMedEner = medEner;

            // le lista entradas digitais
            GateX_ED_Lista_Metodos edMetodos = new GateX_ED_Lista_Metodos();
            List<GateX_ED_Lista_Dominio> entradasDigitas = edMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.listaEntradas = entradasDigitas;

            // le lista saidas digitais
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitas = sdMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.listaSaidas = saidasDigitas;

            // le tipos alarmes
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoAlarme = listatiposMetodos.ListarTodos("TipoGateX_Alarme", false, 0);
            ViewBag.listatipoAlarme = listatipoAlarme;

            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_CtrlAlarme_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_CtrlAlarme_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        // GET: Controle Alarme EMAIL
        public async Task<ActionResult> GateX_CtrlAlarme_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);


            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.ControleAlarme;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista controle alarme
            GateX_CtrlAlarme_Metodos prgMetodos = new GateX_CtrlAlarme_Metodos();
            List<GateX_CtrlAlarme_Dominio> ctrls = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlAlarme = ctrls;

            // le lista medições de energia
            GateX_MedEner_Lista_Metodos medMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medEner = medMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.listaMedEner = medEner;

            // le lista entradas digitais
            GateX_ED_Lista_Metodos edMetodos = new GateX_ED_Lista_Metodos();
            List<GateX_ED_Lista_Dominio> entradasDigitas = edMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.listaEntradas = entradasDigitas;

            // le lista saidas digitais
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitas = sdMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.listaSaidas = saidasDigitas;

            // le tipos alarmes
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoAlarme = listatiposMetodos.ListarTodos("TipoGateX_Alarme", false, 0);
            ViewBag.listatipoAlarme = listatipoAlarme;

            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_CtrlAlarme_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_CtrlAlarme_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "GateX_CtrlAlarmeEmail";
            var message = await EMailTemplateAsync(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.NomeGateway", ViewBag.NomeGateway);
            message = message.Replace("ViewBag.DescrConfig", ViewBag.DescrConfig);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Controle Alarme Print
        public ActionResult GateX_CtrlAlarme_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista controle alarme
            GateX_CtrlAlarme_Metodos prgMetodos = new GateX_CtrlAlarme_Metodos();
            List<GateX_CtrlAlarme_Dominio> ctrls = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlAlarme = ctrls;

            // le lista medições de energia
            GateX_MedEner_Lista_Metodos medMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medEner = medMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.listaMedEner = medEner;

            // le lista entradas digitais
            GateX_ED_Lista_Metodos edMetodos = new GateX_ED_Lista_Metodos();
            List<GateX_ED_Lista_Dominio> entradasDigitas = edMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.listaEntradas = entradasDigitas;

            // le lista saidas digitais
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitas = sdMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.listaSaidas = saidasDigitas;

            // le tipos alarmes
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoAlarme = listatiposMetodos.ListarTodos("TipoGateX_Alarme", false, 0);
            ViewBag.listatipoAlarme = listatipoAlarme;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // imprime
            return View();
        }
    }
}