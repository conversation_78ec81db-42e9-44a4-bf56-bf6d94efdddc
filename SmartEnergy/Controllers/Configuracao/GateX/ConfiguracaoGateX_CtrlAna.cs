﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Configuracao Controle Analógico
        public ActionResult GateX_CtrlAna(int IDGateway, int Origem = 0)
        {
            // tela de ajuda - controle analógico
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // receber configuração
            List<GateX_CtrlAna_Dominio> ctrls = GateX_CtrlAna_Receber(IDGateway, Origem);

            // prepara listas
            GateX_CtrlAna_PreparaListas(IDGateway);

            // retorna configuração
            return View(ctrls);
        }

        // Configuração Controle Analógico - Receber
        private List<GateX_CtrlAna_Dominio> GateX_CtrlAna_Receber(int IDGateway, int Origem)
        {
            // controle analógico
            GateX_CtrlAna_Metodos ctrlMetodos = new GateX_CtrlAna_Metodos();
            List<GateX_CtrlAna_Dominio> ctrls = new List<GateX_CtrlAna_Dominio>();

            // controle analógico (saídas)
            GateX_CtrlAna_SD_Metodos ctrlSDMetodos = new GateX_CtrlAna_SD_Metodos();
            List<GateX_CtrlAna_SD_Dominio> ctrlSDs = new List<GateX_CtrlAna_SD_Dominio>();

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // status
            bool solicitaCtrl = true;
            bool solicitaCtrlSD = true;

            // verifica Origem
            // 0: ler da gateway
            // 1: ler do banco de dados atual
            // 2: ler do banco de dados editando
            if (Origem == 0)
            {
                // status
                solicitaCtrl = false;
                solicitaCtrlSD = false;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // solicita configuração dos controles analógicos
                    if (solicitaCtrl = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_ANA, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // solicita configuração dos controles analógicos (saídas)
                        if (solicitaCtrlSD = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_ANA_SD, SMCOM_TIPO_SOL.SOLICITA))
                        {
                            // copia configuração da gateway para 'editando'
                            // caso ainda não exista configuração 'atual', será criada 
                            ctrls = ctrlMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.CtrlAna);
                            ctrlSDs = ctrlSDMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.CtrlAna_SD);
                        }
                    }
                }

                // verifica se solicitou
                if (solicitaCtrl && solicitaCtrlSD)
                {
                    // atualiza lista de medições analógicas
                    GateX_AtualizaLista(gateway, SMCOM_FUNCOES_SOL.PROGR_MED_AN_LISTA);

                    // atualiza lista de medições utilidades
                    GateX_AtualizaLista(gateway, SMCOM_FUNCOES_SOL.PROGR_MED_UT_LISTA);

                    // atualiza lista de medições ciclometro
                    GateX_AtualizaLista(gateway, SMCOM_FUNCOES_SOL.PROGR_MED_CY_LISTA);

                    // atualiza lista de saidas digitais
                    GateX_AtualizaLista(gateway, SMCOM_FUNCOES_SOL.PROGR_SD_LISTA);
                }
                else
                {
                    // copia configuração 'atual' para 'editando'
                    ctrls = ctrlMetodos.CopiarParaEditando(IDGateway);
                    ctrlSDs = ctrlSDMetodos.CopiarParaEditando(IDGateway);
                }
            }
            else if (Origem == 1)
            {
                // copia configuração 'atual' para 'editando'
                ctrls = ctrlMetodos.CopiarParaEditando(IDGateway);
                ctrlSDs = ctrlSDMetodos.CopiarParaEditando(IDGateway);
            }
            else
            {
                // configuração em 'editando'
                ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
                ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            }

            // copia status
            ViewBag.solicitaCtrl = solicitaCtrl;
            ViewBag.solicitaCtrlSD = solicitaCtrlSD;

            // copia configuração
            ViewBag.CtrlAna = ctrls;
            ViewBag.CtrlAna_SD = ctrlSDs;

            // retorna configuração
            return (ctrls);
        }

        // GET: Configuracao Controle Analógico - Editar
        public ActionResult GateX_CtrlAna_Editar(int IDGateway, int NumCtrlGateway)
        {
            // tela de ajuda - controle analógico
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            GateX_CtrlAna_PreparaListas(IDGateway);

            // controle analógico (editando)
            GateX_CtrlAna_Metodos ctrlMetodos = new GateX_CtrlAna_Metodos();
            GateX_CtrlAna_Dominio ctrl = ctrlMetodos.ListarPorNumCtrl(IDGateway, NumCtrlGateway, STATUS_CFG.EDITANDO);

            // temporizadores
            ctrl.TempoEntrCt *= 10;
            ctrl.TempoFalhMd *= 10;

            // controle analógico (saídas) (editando)
            GateX_CtrlAna_SD_Metodos ctrlSDMetodos = new GateX_CtrlAna_SD_Metodos();
            List<GateX_CtrlAna_SD_Dominio> ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlAna_SD = ctrlSDs;

            // medições
            GateX_CtrlAna_ObterMedicoes(IDGateway, ctrl.MedicaoTy);

            return View(ctrl);
        }

        private void GateX_CtrlAna_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // medições analógicas
            GateX_MedAna_Lista_Metodos medAnaMetodos = new GateX_MedAna_Lista_Metodos();
            List<GateX_MedAna_Lista_Dominio> medAna_Lista = medAnaMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medAna_Lista = medAna_Lista;

            // medições utilidades
            GateX_MedUtil_Lista_Metodos medUtilMetodos = new GateX_MedUtil_Lista_Metodos();
            List<GateX_MedUtil_Lista_Dominio> medUtil_Lista = medUtilMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medUtil_Lista = medUtil_Lista;

            // medições ciclômetro
            GateX_MedCiclo_Lista_Metodos medCicloMetodos = new GateX_MedCiclo_Lista_Metodos();
            List<GateX_MedCiclo_Lista_Dominio> medCiclo_Lista = medCicloMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medCiclo_Lista = medCiclo_Lista;

            // saidas digitais
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.SaidasDigitais = saidasDigitais;

            // le tipos variaveis Controle Analogico
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoVariavelCtrlAna = listatiposMetodos.ListarTodos("TipoVariavelCtrlAna", false, 0);
            ViewBag.listatipoVariavelCtrlAna = listatipoVariavelCtrlAna;

            // le tipos ações Falha Medição
            List<ListaTiposDominio> listatipoAcaoFalhaMed = listatiposMetodos.ListarTodos("TipoAcaoFalhaMed", false, 0);
            ViewBag.listatipoAcaoFalhaMed = listatipoAcaoFalhaMed;

            // medições
            List<ListaTiposDominio> listaMedicoes = new List<ListaTiposDominio>();
            ViewBag.listaMedicoes = listaMedicoes;

            return;
        }

        // POST: Configuracao Controle Analógico - Programar
        [HttpPost]
        public ActionResult GateX_CtrlAna_Programar(GateX_CtrlAna_Dominio CtrlAna, List<GateX_CtrlAna_SD_Dominio> ctrlSDs)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // temporizadores
            CtrlAna.TempoEntrCt /= 10;
            CtrlAna.TempoFalhMd /= 10;

            // salva controle analógico
            GateX_CtrlAna_Metodos ctrlMetodos = new GateX_CtrlAna_Metodos();
            ctrlMetodos.Salvar(CtrlAna, STATUS_CFG.EDITANDO);

            // excluir todas saidas do controle
            GateX_CtrlAna_SD_Metodos ctrlSDMetodos = new GateX_CtrlAna_SD_Metodos();
            ctrlSDMetodos.ExcluirPorNumCtrl(CtrlAna.IDGateway, CtrlAna.NumCtrlGateway, STATUS_CFG.EDITANDO);

            // salva controle analógico (saídas)
            foreach (GateX_CtrlAna_SD_Dominio sd in ctrlSDs)
            {
                // verifica se saída é deste controle
                if (sd.NumCtrlGateway == CtrlAna.NumCtrlGateway)
                {
                    ctrlSDMetodos.Salvar(sd, STATUS_CFG.EDITANDO);
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuracao Controle Analógico - Excluir
        public ActionResult GateX_CtrlAna_Excluir(int IDGateway, int NumCtrlGateway)
        {
            // lista gateway para inserir evento                        
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // apaga programacao
            GateX_CtrlAna_Metodos ctrlMetodos = new GateX_CtrlAna_Metodos();
            ctrlMetodos.ExcluirPorNumCtrl(IDGateway, NumCtrlGateway, STATUS_CFG.EDITANDO);

            GateX_CtrlAna_SD_Metodos ctrlSDMetodos = new GateX_CtrlAna_SD_Metodos();
            ctrlSDMetodos.ExcluirPorNumCtrl(IDGateway, NumCtrlGateway, STATUS_CFG.EDITANDO);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // POST: Configuracao Controle Analógico - Enviar
        [HttpPost]
        public ActionResult GateX_CtrlAna_Enviar(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // copia configuração 'editando' para 'atual'
            GateX_CtrlAna_Metodos ctrlMetodos = new GateX_CtrlAna_Metodos();
            List<GateX_CtrlAna_Dominio> ctrls = ctrlMetodos.CopiarParaAtual(IDGateway);

            GateX_CtrlAna_SD_Metodos ctrlSDMetodos = new GateX_CtrlAna_SD_Metodos();
            List<GateX_CtrlAna_SD_Dominio> ctrlSDs = ctrlSDMetodos.CopiarParaAtual(IDGateway);

            // variavel de retorno
            bool enviaCtrlAna = false;
            bool enviaCtrlANA_SD = false;

            // verifica
            if (ctrls != null && ctrlSDs != null && gateway != null)
            {
                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // copia para lista de envio
                    smartCom.gateX.CtrlAna = ctrls;

                    // envia controle analógico
                    enviaCtrlAna = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_ANA, SMCOM_TIPO_SOL.ENVIA);

                    // copia para lista de envio
                    smartCom.gateX.CtrlAna_SD = ctrlSDs;

                    // envia controle analógico (saídas)
                    enviaCtrlANA_SD = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_ANA_SD, SMCOM_TIPO_SOL.ENVIA);
                }

            }

            // verifica retorno
            if (enviaCtrlAna && enviaCtrlANA_SD)
            {
                // excluir editando
                ctrlMetodos.ExcluirEditando(IDGateway);
                ctrlSDMetodos.ExcluirEditando(IDGateway);

                // histórico de configuração
                HistoricoConfiguracoesDominio hist = new HistoricoConfiguracoesDominio();
                hist.IDGateway = IDGateway;
                hist.NomeConfiguracao = "GateX_CtrlAna";
                hist.Envio = "MQTT";
                hist.Atualizacao = DateTime.Now;
                hist.Versao = GateX_Versao_String(IDGateway);
                hist.IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");

                // insere histórico de configuração
                HistoricoConfiguracoesMetodos histMetodos = new HistoricoConfiguracoesMetodos();
                histMetodos.Inserir(hist);

                // controle analógico (saídas)
                hist.NomeConfiguracao = "GateX_CtrlAna_SD";
                histMetodos.Inserir(hist);

                // caso OK insiro evento
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, IDGateway);
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Falha no envio da configuração!"
                };
            }

            // retorna status
            return Json(returnedData);
        }




        //
        // MEDICOES
        //

        // GET: Obter Medicoes
        public JsonResult GateX_CtrlAna_ObterMedicoes(int IDGateway, int MedicaoTy)
        {
            // medições
            List<ListaTiposDominio> listaMedicoes = new List<ListaTiposDominio>();

            // tipo da medição desejada
            switch (MedicaoTy)
            {
                case 0:     // nenhum
                    break;

                case 1:     // utilidades media
                case 2:     // utilidades totalizado

                    // medições utilidades
                    GateX_MedUtil_Lista_Metodos medUtilMetodos = new GateX_MedUtil_Lista_Metodos();
                    List<GateX_MedUtil_Lista_Dominio> medUtil_Lista = medUtilMetodos.ListarPorIDGateway(IDGateway);

                    // preenche lista de medições
                    for (int i = 0; i < SGATEX.NUM_MED_UT; i++)
                    {
                        // default
                        ListaTiposDominio tipo = new ListaTiposDominio();
                        tipo.ID = i;
                        tipo.Descricao = string.Format("[{0:00}] Medição de Utilidades {1}", i, i);

                        // copia
                        if (medUtil_Lista != null)
                        {
                            // verifica se tem na lista
                            GateX_MedUtil_Lista_Dominio med = medUtil_Lista.First(item => item.NumMedicaoGateway == i);

                            if (med != null)
                            {
                                tipo.Descricao = string.Format("[{0:00}] {1}", i, med.Descricao);
                            }
                        }

                        // insere na lista
                        listaMedicoes.Add(tipo);
                    }

                    break;

                case 3:     // analogica media

                    // medições analógicas
                    GateX_MedAna_Lista_Metodos medAnaMetodos = new GateX_MedAna_Lista_Metodos();
                    List<GateX_MedAna_Lista_Dominio> medAna_Lista = medAnaMetodos.ListarPorIDGateway(IDGateway);

                    // preenche lista de medições
                    for (int i = 0; i < SGATEX.NUM_MED_AN; i++)
                    {
                        // default
                        ListaTiposDominio tipo = new ListaTiposDominio();
                        tipo.ID = i;
                        tipo.Descricao = string.Format("[{0:00}] Medição Analógica {1}", i, i);

                        // copia
                        if (medAna_Lista != null)
                        {
                            // verifica se tem na lista
                            GateX_MedAna_Lista_Dominio med = medAna_Lista.First(item => item.NumMedicaoGateway == i);

                            if (med != null)
                            {
                                tipo.Descricao = string.Format("[{0:00}] {1}", i, med.Descricao);
                            }
                        }

                        // insere na lista
                        listaMedicoes.Add(tipo);
                    }

                    break;

                case 4:     // ciclometro totalizado

                    // medições ciclômetro
                    GateX_MedCiclo_Lista_Metodos medCicloMetodos = new GateX_MedCiclo_Lista_Metodos();
                    List<GateX_MedCiclo_Lista_Dominio> medCiclo_Lista = medCicloMetodos.ListarPorIDGateway(IDGateway);

                    // preenche lista de medições
                    for (int i = 0; i < SGATEX.NUM_MED_CY; i++)
                    {
                        // default
                        ListaTiposDominio tipo = new ListaTiposDominio();
                        tipo.ID = i;
                        tipo.Descricao = string.Format("[{0:00}] Medição Ciclômetro {1}", i, i);

                        // copia
                        if (medCiclo_Lista != null)
                        {
                            // verifica se tem na lista
                            GateX_MedCiclo_Lista_Dominio med = medCiclo_Lista.First(item => item.NumMedicaoGateway == i);

                            if (med != null)
                            {
                                tipo.Descricao = string.Format("[{0:00}] {1}", i, med.Descricao);
                            }
                        }

                        // insere na lista
                        listaMedicoes.Add(tipo);
                    }

                    break;
            }

            // lista de medições
            ViewBag.listaMedicoes = listaMedicoes;

            // retorna o valor em JSON
            return Json(listaMedicoes, JsonRequestBehavior.AllowGet);
        }

        // recebe configuração (comando receber configurações)
        public bool GateX_CtrlAna_ComandoRecebe(GatewaysDominio gateway, string versao)
        {
            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita programação
                if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_ANA, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // solicita configuração dos controles analógicos (saídas)
                    if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_ANA_SD, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // salvar
                        GateX_CtrlAna_Metodos ctrlMetodos = new GateX_CtrlAna_Metodos();
                        ctrlMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.CtrlAna, STATUS_CFG.ATUAL);

                        // salvar (controle analógico (saídas))
                        GateX_CtrlAna_SD_Metodos ctrlSDMetodos = new GateX_CtrlAna_SD_Metodos();
                        ctrlSDMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.CtrlAna_SD, STATUS_CFG.ATUAL);

                        // ok
                        return (true);
                    }
                }
            }

            // erro
            return (false);
        }
    }
}