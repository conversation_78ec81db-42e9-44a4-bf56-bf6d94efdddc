﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Configuração Drivers e SmartEnergy
        public ActionResult GateX_Drivers(int IDGateway, int Origem = 0)
        {
            // tela de ajuda
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // receber configuração
            GateX_Driver_Dominio prg = GateX_Drivers_Receber(IDGateway, Origem);

            // prepara listas
            GateX_Drivers_PreparaListas(IDGateway);

            // retorna configuração
            return View(prg);
        }

        // Configuração Drivers e SmartEnergy - Receber
        private GateX_Driver_Dominio GateX_Drivers_Receber(int IDGateway, int Origem)
        {
            // configuração
            GateX_Driver_Metodos prgMetodos = new GateX_Driver_Metodos();
            GateX_Driver_Dominio prg = new GateX_Driver_Dominio();

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // status
            bool solicitaProg = true;

            // verifica Origem
            // 0: ler da gateway
            // 1: ler do banco de dados atual
            // 2: ler do banco de dados editando
            if (Origem == 0)
            {
                // status
                solicitaProg = false;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // solicita 
                    if (solicitaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_DRIVER, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // copia configuração da gateway para 'editando'
                        // caso ainda não exista configuração 'atual', será criada 
                        prg = prgMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.Driver);

                        // solicita versão
                        if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.VERSAO, SMCOM_TIPO_SOL.SOLICITA))
                        {
                            // copia versão
                            prg.Modelo = smartCom.gateX.Versao.Modelo;
                            prg.Versao = smartCom.gateX.Versao.Versao;
                            prg.DriverRemota = smartCom.gateX.Versao.DriverRemota;
                        }
                    }
                }

                // verifica se não solicitou
                if (!solicitaProg)
                {
                    // copia configuração 'atual' para 'editando'
                    prg = prgMetodos.CopiarParaEditando(IDGateway);
                }
            }
            else if (Origem == 1)
            {
                // copia configuração 'atual' para 'editando'
                prg = prgMetodos.CopiarParaEditando(IDGateway);
            }
            else
            {
                // configuração em 'editando'
                prg = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            }

            // copia status
            ViewBag.SolicitaProg = solicitaProg;

            // histórico
            prg.xH2_qEN /= 4;
            prg.xH2_qUT /= 4;
            prg.xH2_qAN /= 4;
            prg.xH2_qCY /= 4;

            // upload
            prg.xUpld1st_indice = (prg.xUpld1st_hora * 4) + (prg.xUpld1st_min / 15);
            prg.xUpldFrq_indice = (prg.xUpldFrq_hora * 4) + (prg.xUpldFrq_min / 15);


            // xUpldLst (converto array de byte-hexa em string)
            int posicao = 0;

            // medições de energia
            prg.xUpldLstEN = GateX_Drivers_ListaMedicao(prg.xUpldLst, prg.xUpldQtEN, ref posicao);

            // medições de utilidades
            prg.xUpldLstUT = GateX_Drivers_ListaMedicao(prg.xUpldLst, prg.xUpldQtUT, ref posicao);

            // medições analógicas
            prg.xUpldLstAN = GateX_Drivers_ListaMedicao(prg.xUpldLst, prg.xUpldQtAN, ref posicao);

            // medições ciclômetro
            prg.xUpldLstCY = GateX_Drivers_ListaMedicao(prg.xUpldLst, prg.xUpldQtCY, ref posicao);


            // retorna configuração
            return (prg);
        }

        // converte medições em string com ';' como separador
        private string GateX_Drivers_ListaMedicao(string UpdlLst_X2, int UpldQt, ref int posicao)
        {
            // primeira medição
            bool primeira_medicao = true;
            string UpldLst = "";
            string medicao_string = "";
            int medicao_numero = 0;

            // percorre medições
            for (int i = 0; i < UpldQt; i++)
            {
                // medição
                medicao_string = UpdlLst_X2.Substring(posicao, 2);

                // parse do número da medição
                if (int.TryParse(medicao_string, out medicao_numero))
                {
                    // verifica se medição válida
                    if (medicao_numero >= 0 && medicao_numero < SGATEX.NUM_MED_EN)
                    {
                        // verifica se é a primeira medição da string
                        UpldLst += primeira_medicao ? "" : ";";
                        primeira_medicao = false;

                        // copia número da medição
                        UpldLst += medicao_numero.ToString();
                    }
                }

                // próximo
                posicao += 2;
            }

            // retorno
            return (UpldLst);
        }


        // prepara listas
        private void GateX_Drivers_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // ser2_serFN
            List<ListaTiposDominio> lista_ser2_serFN = new List<ListaTiposDominio>()
            {
                new ListaTiposDominio {ID = 0, Descricao = "Rede_SUP"},
                new ListaTiposDominio {ID = 1, Descricao = "Modem [2g/3g/4g]"},
            };
            ViewBag.lista_ser2_serFN = lista_ser2_serFN;

            // serP_serFN
            List<ListaTiposDominio> lista_serP_serFN = new List<ListaTiposDominio>()
            {
                new ListaTiposDominio {ID = 0, Descricao = "CODI P"},
                new ListaTiposDominio {ID = 1, Descricao = "DIN Virtual"},
                new ListaTiposDominio {ID = 2, Descricao = "---"},
            };
            ViewBag.lista_serP_serFN = lista_serP_serFN;

            // serQ_serFN
            List<ListaTiposDominio> lista_serQ_serFN = new List<ListaTiposDominio>()
            {
                new ListaTiposDominio {ID = 0, Descricao = "Rede IO"},
                new ListaTiposDominio {ID = 1, Descricao = "CODI Q Rx"},
                new ListaTiposDominio {ID = 2, Descricao = "CODI P reTx"},
            };
            ViewBag.lista_serQ_serFN = lista_serQ_serFN;

            // serR_serFN
            List<ListaTiposDominio> lista_serR_serFN = new List<ListaTiposDominio>()
            {
                new ListaTiposDominio {ID = 0, Descricao = "Rede IO"},
            };
            ViewBag.lista_serR_serFN = lista_serR_serFN;

            // serS_serFN
            List<ListaTiposDominio> lista_serS_serFN = new List<ListaTiposDominio>()
            {
                new ListaTiposDominio {ID = 0, Descricao = "Rede SUP"},
            };
            ViewBag.lista_serS_serFN = lista_serS_serFN;

            // COD_snCod
            List<ListaTiposDominio> lista_COD_snCod = new List<ListaTiposDominio>()
            {
                new ListaTiposDominio {ID = 0, Descricao = "CODI P"},
                new ListaTiposDominio {ID = 1, Descricao = "Slot_A CODI Normal"},
                new ListaTiposDominio {ID = 2, Descricao = "DIN Virtual"},
            };
            ViewBag.lista_COD_snCod = lista_COD_snCod;

            // Protocolo CODI
            List<ListaTiposDominio> lista_ProtocoloCODI = new List<ListaTiposDominio>()
            {
                new ListaTiposDominio {ID = 0, Descricao = "CODI Normal"},
                new ListaTiposDominio {ID = 1, Descricao = "CODI Extendido"},
                new ListaTiposDominio {ID = 2, Descricao = "CODI Misto"},
                new ListaTiposDominio {ID = 3, Descricao = "CODI Normal 9k6"},
                new ListaTiposDominio {ID = 4, Descricao = "CODI Extendido 9k6"},
            };
            ViewBag.lista_ProtocoloCODI = lista_ProtocoloCODI;

            // COD_uHb_rxP
            List<ListaTiposDominio> lista_COD_uHb_rxP = new List<ListaTiposDominio>()
            {
                new ListaTiposDominio {ID = 0, Descricao = "---"},
                new ListaTiposDominio {ID = 1, Descricao = "Rx"},
                new ListaTiposDominio {ID = 2, Descricao = "re Tx"},
            };
            ViewBag.lista_COD_uHb_rxP = lista_COD_uHb_rxP;

            // Protocolo Supervisão
            List<ListaTiposDominio> lista_ProtocoloSuperv = new List<ListaTiposDominio>()
            {
                new ListaTiposDominio {ID = 0, Descricao = "---"},
                new ListaTiposDominio {ID = 1, Descricao = "Snet"},
                new ListaTiposDominio {ID = 2, Descricao = "Snet2"},
                new ListaTiposDominio {ID = 3, Descricao = "Modbus TCP"},
                new ListaTiposDominio {ID = 4, Descricao = "MBAP"},
                new ListaTiposDominio {ID = 5, Descricao = "MBAP u"},
                new ListaTiposDominio {ID = 6, Descricao = "UDP IP"},
                new ListaTiposDominio {ID = 7, Descricao = "UDP BC"},
                new ListaTiposDominio {ID = 8, Descricao = "Modbus RTU"},
            };
            ViewBag.lista_ProtocoloSuperv = lista_ProtocoloSuperv;

            // Velocidade
            List<ListaTiposDominio> lista_Velocidade = new List<ListaTiposDominio>()
            {
                new ListaTiposDominio {ID = 0, Descricao = "---"},
                new ListaTiposDominio {ID = 1, Descricao = "110"},
                new ListaTiposDominio {ID = 2, Descricao = "300"},
                new ListaTiposDominio {ID = 3, Descricao = "600"},
                new ListaTiposDominio {ID = 4, Descricao = "1k2"},
                new ListaTiposDominio {ID = 5, Descricao = "2k4"},
                new ListaTiposDominio {ID = 6, Descricao = "4k8"},
                new ListaTiposDominio {ID = 7, Descricao = "9k6"},
                new ListaTiposDominio {ID = 8, Descricao = "19k2"},
                new ListaTiposDominio {ID = 9, Descricao = "38k4"},
                new ListaTiposDominio {ID = 10, Descricao = "57k6"},
            };
            ViewBag.lista_Velocidade = lista_Velocidade;

            // Protocolo IO
            List<ListaTiposDominio> lista_ProtocoloIO = new List<ListaTiposDominio>()
            {
                new ListaTiposDominio {ID = 0, Descricao = "Modbus RTU"},
                new ListaTiposDominio {ID = 1, Descricao = "Modbus ASC"},
            };
            ViewBag.lista_ProtocoloIO = lista_ProtocoloIO;

            // Sim ou Não
            List<ListaTiposDominio> lista_SimNao = new List<ListaTiposDominio>()
            {
                new ListaTiposDominio {ID = 0, Descricao = "Não"},
                new ListaTiposDominio {ID = 1, Descricao = "Sim"},
            };
            ViewBag.lista_SimNao = lista_SimNao;

            // Quantidade de medições
            List<ListaTiposDominio> lista_QuantMed = new List<ListaTiposDominio>()
            {
                new ListaTiposDominio {ID = 0, Descricao = "Nenhum"},
                new ListaTiposDominio {ID = 1, Descricao = "0 ~ 3"},
                new ListaTiposDominio {ID = 2, Descricao = "0 ~ 7"},
                new ListaTiposDominio {ID = 3, Descricao = "0 ~ 11"},
                new ListaTiposDominio {ID = 4, Descricao = "0 ~ 15"},
                new ListaTiposDominio {ID = 5, Descricao = "0 ~ 19"},
                new ListaTiposDominio {ID = 6, Descricao = "0 ~ 23"},
                new ListaTiposDominio {ID = 7, Descricao = "0 ~ 27"},
                new ListaTiposDominio {ID = 8, Descricao = "0 ~ 31"},
                new ListaTiposDominio {ID = 9, Descricao = "0 ~ 35"},
            };
            ViewBag.lista_QuantMed = lista_QuantMed;

            // Registros
            List<ListaTiposDominio> lista_Registros = new List<ListaTiposDominio>()
            {
                new ListaTiposDominio {ID = 1, Descricao = "01/01 min"},
                new ListaTiposDominio {ID = 2, Descricao = "05/05 min"},
                new ListaTiposDominio {ID = 3, Descricao = "15/15 min"},
                new ListaTiposDominio {ID = 4, Descricao = "Hora/Hora"},
            };
            ViewBag.lista_Registros = lista_Registros;

            // WanIs
            List<ListaTiposDominio> lista_WanIs = new List<ListaTiposDominio>()
            {
                new ListaTiposDominio {ID = 0, Descricao = "Nenhum"},
                new ListaTiposDominio {ID = 2, Descricao = "Ethernet"},
                new ListaTiposDominio {ID = 6, Descricao = "RS232 - Modem GSM"},

            };
            ViewBag.lista_WanIs = lista_WanIs;

            // WanEq
            List<ListaTiposDominio> lista_WanEq = new List<ListaTiposDominio>()
            {
                new ListaTiposDominio {ID = 0, Descricao = "Telit 2G/3G"},
                new ListaTiposDominio {ID = 1, Descricao = "Siemens T35/T39"},
            };
            ViewBag.lista_WanEq = lista_WanEq;

            // UpFrq
            List<ListaTiposDominio> lista_UpFrq = new List<ListaTiposDominio>()
            {
                new ListaTiposDominio {ID = 1, Descricao = "15 minutos"},
                new ListaTiposDominio {ID = 2, Descricao = "30 minutos"},
                new ListaTiposDominio {ID = 4, Descricao = "1 hora"},
                new ListaTiposDominio {ID = 24, Descricao = "6 horas"},
                new ListaTiposDominio {ID = 48, Descricao = "12 horas"},
                new ListaTiposDominio {ID = 96, Descricao = "24 horas"},
            };
            ViewBag.lista_UpFrq = lista_UpFrq;

            // Up1st
            List<ListaTiposDominio> lista_Up1st = new List<ListaTiposDominio>();
            for (int i = 0; i < 96; i++)
            {
                ListaTiposDominio item = new ListaTiposDominio();
                item.ID = i;
                item.Descricao = string.Format("{0:00}:{1:00}", i / 4, (i % 4) * 15);
                lista_Up1st.Add(item);
            }
            ViewBag.lista_Up1st = lista_Up1st;

            // Protocolo Upload
            List<ListaTiposDominio> lista_ProtocoloUpload = new List<ListaTiposDominio>()
            {
                new ListaTiposDominio {ID = 0, Descricao = "FTP"},
                new ListaTiposDominio {ID = 1, Descricao = "IoT"},
            };
            ViewBag.lista_ProtocoloUpload = lista_ProtocoloUpload;

            // UpldOnAlm
            List<ListaTiposDominio> lista_UpldOnAlm = new List<ListaTiposDominio>()
            {
                new ListaTiposDominio {ID = 0, Descricao = "Desabilitada"},
                new ListaTiposDominio {ID = 1, Descricao = "na ativação"},
                new ListaTiposDominio {ID = 1, Descricao = "na ativ/norm"},
                new ListaTiposDominio {ID = 1, Descricao = "na ativ/norm/rec"},
            };
            ViewBag.lista_UpldOnAlm = lista_UpldOnAlm;

            // IspAuth
            List<ListaTiposDominio> lista_IspAuth = new List<ListaTiposDominio>()
            {
                new ListaTiposDominio {ID = 0, Descricao = "PAP antes do link PPP"},
                new ListaTiposDominio {ID = 1, Descricao = "PAP após link PPP"},
                new ListaTiposDominio {ID = 2, Descricao = "CHAP"},
            };
            ViewBag.lista_IspAuth = lista_IspAuth;

            return;
        }

        // POST: Configuração Drivers e SmartEnergy - Enviar
        [HttpPost]
        public ActionResult GateX_Drivers_Enviar(GateX_Driver_Dominio prg)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(prg.IDGateway);

            // configuração atual
            prg.Status_Cfg = STATUS_CFG.ATUAL;

            // histórico
            prg.xH2_qEN *= 4;
            prg.xH2_qUT *= 4;
            prg.xH2_qAN *= 4;
            prg.xH2_qCY *= 4;

            // upload
            prg.xUpld1st_hora = prg.xUpld1st_indice / 4;
            prg.xUpld1st_min = (prg.xUpld1st_indice % 4) * 15;

            prg.xUpldFrq_hora = prg.xUpldFrq_indice / 4;
            prg.xUpldFrq_min = (prg.xUpldFrq_indice % 4) * 15; 

            // trata string com lista de medições e coloca no array principal (capacidade para os 4 tipos EN/UT/AN/CY)
            string UpldLst_med = "";
            int UpldQt_med = 0;
            string UpldLst = "";
            int UpldQt = 0;

            // medições de energia
            UpldLst_med = prg.xUpldLstEN;
            GateX_Drivers_String2Array(ref UpldLst_med, ref UpldQt_med, ref UpldLst, ref UpldQt);
            prg.xUpldLstEN = UpldLst_med;
            prg.xUpldQtEN = UpldQt_med;

            // medições de utilidades
            UpldLst_med = prg.xUpldLstUT;
            GateX_Drivers_String2Array(ref UpldLst_med, ref UpldQt_med, ref UpldLst, ref UpldQt);
            prg.xUpldLstUT = UpldLst_med;
            prg.xUpldQtUT = UpldQt_med;

            // medições analógicas
            UpldLst_med = prg.xUpldLstAN;
            GateX_Drivers_String2Array(ref UpldLst_med, ref UpldQt_med, ref UpldLst, ref UpldQt);
            prg.xUpldLstAN = UpldLst_med;
            prg.xUpldQtAN = UpldQt_med;

            // medições ciclômetro
            UpldLst_med = prg.xUpldLstCY;
            GateX_Drivers_String2Array(ref UpldLst_med, ref UpldQt_med, ref UpldLst, ref UpldQt);
            prg.xUpldLstCY = UpldLst_med;
            prg.xUpldQtCY = UpldQt_med;

            // completa UpldLst
            GateX_Drivers_CompletaUpldLst(ref UpldLst, ref UpldQt);

            // lista final
            prg.xUpldLst = UpldLst;

            // copia configuração 'editando' para 'atual'
            GateX_Driver_Metodos prgMetodos = new GateX_Driver_Metodos();
            prgMetodos.Salvar(prg);

            // variavel de retorno
            bool enviaProg = false;

            // verifica
            if (gateway != null)
            {
                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // copia data hora no envio
                    smartCom.gateX.Driver = prg;

                    // envia data hora
                    enviaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_DRIVER, SMCOM_TIPO_SOL.ENVIA);
                }
            }

            // verifica retorno
            if (enviaProg)
            {
                // excluir editando
                prgMetodos.ExcluirEditando(prg.IDGateway);

                // histórico de configuração
                HistoricoConfiguracoesDominio hist = new HistoricoConfiguracoesDominio();
                hist.IDGateway = prg.IDGateway;
                hist.NomeConfiguracao = "GateX_Driver";
                hist.Envio = "MQTT";
                hist.Atualizacao = DateTime.Now;
                hist.Versao = prg.Versao;
                hist.IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");

                // insere histórico de configuração
                HistoricoConfiguracoesMetodos histMetodos = new HistoricoConfiguracoesMetodos();
                histMetodos.Inserir(hist);

                // caso OK insiro evento
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, prg.IDGateway);
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Falha no envio da configuração!"
                };
            }

            // retorna status
            return Json(returnedData);
        }

        // trata string com lista de medições e coloca na string principal no formato X2
        //  UpldLst_med - string com os números de medições separados por ';' (Ex: "0;1;2"). Deve ser enviado para a função.
        //  UpldQt_med - quantidade de medições do tipo da medição que está sendo analisado (Ex. 3). Será preenchida na função.
        //  UpldLst - string com todas as medições de todos os tipos no formato X2. Será utilizado para o envio para a gateway.
        //  UpldQt - quantidade de medições dentro do array UpldLst_array.
        private bool GateX_Drivers_String2Array(ref string UpldLst_med, ref int UpldQt_med, ref string UpldLst, ref int UpldQt)
        {
            // protege
            if (UpldLst_med == null)
            {
                UpldLst_med = "";
            }

            // quantidade de medições
            UpldQt_med = 0;

            // verifica se início  
            if (UpldQt == 0)
            {
                // limpa 
                UpldLst = "";
            }

            // retorno
            bool retorno = true;

            // medição analisando
            string medicao_string = "";

            // percorre string
            foreach (char caracter in UpldLst_med)
            {
                // verifica se não é digito (considero como separador)
                if (!char.IsDigit(caracter))
                {
                    // verifica se estava colhendo número da medição e adiciona na string
                    GateX_Drivers_VerificaMedicao(ref medicao_string, ref UpldQt_med, ref UpldLst, ref UpldQt);

                    // próximo
                    continue;
                }

                // coloca na string o caracter da medição
                medicao_string += caracter.ToString();
            }

            // verifica se estava coletando número da medição e adiciona na string
            GateX_Drivers_VerificaMedicao(ref medicao_string, ref UpldQt_med, ref UpldLst, ref UpldQt);

            // retorno
            return (retorno);
        }

        // verifica medição caso em coleta do valor
        private bool GateX_Drivers_VerificaMedicao(ref string medicao_string, ref int UpldQt_med, ref string UpldLst, ref int UpldQt)
        {
            // medição
            int medicao_numero = 0;

            // retorno
            bool retorno = true;

            // verifica se string contém caracteres (colhendo número da medição)
            if (medicao_string.Length > 0)
            {
                // parse do número da medição
                if (int.TryParse(medicao_string, out medicao_numero))
                {
                    // verifica se medição válida
                    if (medicao_numero >= 0 && medicao_numero < SGATEX.NUM_MED_EN)
                    {
                        // incrementa quantidade de medições geral e do tipo analisado
                        UpldQt++;
                        UpldQt_med++;

                        // coloca na string no formato X2
                        UpldLst += string.Format("{0:X2}", medicao_numero);
                    }
                    else
                    {
                        // erro
                        retorno = false;
                    }
                }
            }

            // prepara para próximo
            medicao_string = "";

            // retorno
            return (retorno);
        }

        // completa UpldLst 
        private void GateX_Drivers_CompletaUpldLst(ref string UpldLst, ref int UpldQt)
        {
            // UpldLst pode conter até 100 medições, completo a lista no formato X2
            for (int i = UpldQt; i < 100; i++ )
            {
                // coloca na string no formato X2
                UpldLst += "00";
            }

            // retorno
            return;
        }

        // recebe configuração (comando receber configurações)
        public bool GateX_Drivers_ComandoRecebe(GatewaysDominio gateway, string versao)
        {
            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita programação
                if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_DRIVER, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // salvar
                    GateX_Driver_Metodos prgMetodos = new GateX_Driver_Metodos();
                    prgMetodos.Salvar(smartCom.gateX.Driver);

                    // ok
                    return (true);
                }
            }

            // erro
            return (false);
        }
    }
}