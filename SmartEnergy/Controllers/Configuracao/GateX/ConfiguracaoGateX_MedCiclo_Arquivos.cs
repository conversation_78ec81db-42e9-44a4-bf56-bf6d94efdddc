﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Medições Ciclometro XLS Download
        [HttpGet]
        public virtual ActionResult GateX_MedCiclo_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Medições Ciclometro XLS
        public ActionResult GateX_MedCiclo_XLS(int IDGateway)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_GateX_MedCiclo(IDGateway);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_MedCiclo_Gateway_{0:000000}_{1:yyyyMMddHHmm}.xls", IDGateway, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // Medições Ciclometro Planilha
        private HSSFWorkbook XLS_GateX_MedCiclo(int IDGateway)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // MEDIÇÕES CICLOMETRO
            //

            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoMedCiclo;

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            //  le lista de medições da gateway
            GateX_MedCiclo_Metodos prgMetodos = new GateX_MedCiclo_Metodos();
            List<GateX_MedCiclo_Dominio> medCiclo = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // le lista tipo variável
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoVariavel = listatiposMetodos.ListarTodos("TipoGateX_Variavel_CY", false, 0);

            // le tipos rede IO
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);

            // le lista tipo variável formato
            List<ListaTiposDominio> listatipoVariavelFormato = listatiposMetodos.ListarTodos("TipoGateX_Variavel_Formato", false, 0);


            // cria planilha
            var sheet = workbook.CreateSheet("Medições Ciclômetro");

            // cabecalho
            string[] cabecalho = { "Medição Interna", "Medições Ciclômetro", "Unidade", "Variável", "Falta de Pulso (seg.)", "Rede", "Remota", "Endereço", "Formato", "Zerar em (seg.)", "Constante Multiplicador", "Fundo de Escala" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            IRow row;

            if (medCiclo != null)
            {
                // percorre lógicas
                foreach (GateX_MedCiclo_Dominio med in medCiclo)
                {
                    string desprogramado = "---";
                    string Descricao = desprogramado;
                    string Unidade = desprogramado;
                    string TipoVariavel = desprogramado;
                    string TempoAmostraFaltaPulso = desprogramado;

                    string RedeIO = desprogramado;
                    string Remota = desprogramado;
                    string RemotaEnd = desprogramado;
                    string RemotaVar = desprogramado;
                    string TempoZerarRaw = desprogramado;

                    string v2_ConstanteMult = desprogramado;
                    string v2_ConstanteSoma = desprogramado;

                    if (med.Programado)
                    {
                        // Descrição e Unidade
                        Descricao = med.Descricao;
                        Unidade = med.Unidade;

                        // Falta de Pulso
                        TempoAmostraFaltaPulso = string.Format("{0}", med.TempoAmostraFaltaPulso * 10);

                        // Tipo Variável
                        ListaTiposDominio tipo = listatipoVariavel.Find(item => item.ID == med.TipoVariavel);
                        TipoVariavel = (tipo != null) ? tipo.Descricao : desprogramado;

                        // RedeIO / Remota / Endereço / Formato
                        tipo = listatipoRedeIO.Find(item => item.ID == med.v2_RedeIO);
                        RedeIO = (tipo != null) ? tipo.Descricao : desprogramado;
                        Remota = med.v2_Remota.ToString();
                        RemotaEnd = med.v2_RemotaEnd.ToString();
                        tipo = listatipoVariavelFormato.Find(item => item.ID == med.v2_RemotaVar);
                        RemotaVar = (tipo != null) ? tipo.Descricao : desprogramado;

                        // Zerar em
                        TempoZerarRaw = string.Format("{0}", med.v2_TempoZerarRaw * 10);

                        // Constante Multiplicador
                        v2_ConstanteMult = string.Format("{0}", Math.Round(med.v2_ConstanteMult, 5));

                        // Fundo de Escala
                        v2_ConstanteSoma = string.Format("{0}", Math.Round(med.v2_ConstanteSoma, 5));
                    }

                    // adiciona linha
                    row = sheet.CreateRow(rowIndex++);

                    // Medição
                    numeroCelulaXLS(row, 0, med.NumMedicaoGateway, _intCellStyle);
                    sheet.SetColumnWidth(0, 6000);

                    // Descrição
                    textoCelulaXLS(row, 1, Descricao);
                    sheet.SetColumnWidth(1, 7000);

                    // Unidade
                    textoCelulaXLS(row, 2, Unidade);
                    sheet.SetColumnWidth(2, 5000);

                    // Variável
                    textoCelulaXLS(row, 3, TipoVariavel);
                    sheet.SetColumnWidth(3, 7000);

                    // Falta de Pulso
                    textoCelulaXLS(row, 4, TempoAmostraFaltaPulso);
                    sheet.SetColumnWidth(4, 6000);

                    // rede IO
                    textoCelulaXLS(row, 5, RedeIO);
                    sheet.SetColumnWidth(5, 4000);

                    // remota
                    textoCelulaXLS(row, 6, Remota);
                    sheet.SetColumnWidth(6, 4000);

                    // endereco
                    textoCelulaXLS(row, 7, RemotaEnd);
                    sheet.SetColumnWidth(7, 4000);

                    // formato
                    textoCelulaXLS(row, 8, RemotaVar);
                    sheet.SetColumnWidth(8, 4000);

                    // Zerar em
                    textoCelulaXLS(row, 9, TempoZerarRaw);
                    sheet.SetColumnWidth(9, 4000);

                    // Constante Multiplicador
                    textoCelulaXLS(row, 10, v2_ConstanteMult);
                    sheet.SetColumnWidth(10, 6500);

                    // Fundo de Escala
                    textoCelulaXLS(row, 11, v2_ConstanteSoma);
                    sheet.SetColumnWidth(11, 6500);
                }
            }

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS_GateX(workbook, sheet, _negritoCellStyle);

            // largura de cada coluna
            sheet.SetColumnWidth(0, 7000);
            sheet.SetColumnWidth(1, 8000);

            // retorna planilha
            return workbook;
        }


        // GET: Medições Ciclometro PDF
        public ActionResult GateX_MedCiclo_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista Medições Ciclometro
            GateX_MedCiclo_Metodos prgMetodos = new GateX_MedCiclo_Metodos();
            List<GateX_MedCiclo_Dominio> medCiclo = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.medCiclo = medCiclo;

            // le lista tipo variável
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoVariavel = listatiposMetodos.ListarTodos("TipoGateX_Variavel_CY", false, 0);
            ViewBag.listatipoVariavel = listatipoVariavel;

            // le tipos rede IO
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DO);      // remove DO
            ViewBag.listatipoRedeIO = listatipoRedeIO;


            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_MedCiclo_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_MedCiclo_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        // GET: Medições Ciclometro EMAIL
        public async Task<ActionResult> GateX_MedCiclo_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);


            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoMedCiclo;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista Medições Ciclometro
            GateX_MedCiclo_Metodos prgMetodos = new GateX_MedCiclo_Metodos();
            List<GateX_MedCiclo_Dominio> medCiclo = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.medCiclo = medCiclo;

            // le lista tipo variável
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoVariavel = listatiposMetodos.ListarTodos("TipoGateX_Variavel_CY", false, 0);
            ViewBag.listatipoVariavel = listatipoVariavel;

            // le tipos rede IO
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DO);      // remove DO
            ViewBag.listatipoRedeIO = listatipoRedeIO;


            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_MedCiclo_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_MedCiclo_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "GateX_MedCicloEmail";
            var message = await EMailTemplateAsync(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.NomeGateway", ViewBag.NomeGateway);
            message = message.Replace("ViewBag.DescrConfig", ViewBag.DescrConfig);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Medições Ciclometro Print
        public ActionResult GateX_MedCiclo_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista Medições Ciclometro
            GateX_MedCiclo_Metodos prgMetodos = new GateX_MedCiclo_Metodos();
            List<GateX_MedCiclo_Dominio> medCiclo = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.medCiclo = medCiclo;

            // le lista tipo variável
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoVariavel = listatiposMetodos.ListarTodos("TipoGateX_Variavel_CY", false, 0);
            ViewBag.listatipoVariavel = listatipoVariavel;

            // le tipos rede IO
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DO);      // remove DO
            ViewBag.listatipoRedeIO = listatipoRedeIO;


            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // imprime
            return View();
        }
    }
}