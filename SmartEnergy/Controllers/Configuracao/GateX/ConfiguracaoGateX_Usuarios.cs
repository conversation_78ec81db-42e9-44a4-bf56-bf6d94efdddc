﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Configuração Usuários
        public ActionResult GateX_Usuarios(int IDGateway, int Origem = 0)
        {
            // tela de ajuda - Usuários
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permisso<PERSON>();

            // receber configuração
            List<GateX_Usuarios_Dominio> usuarios = GateX_Usuarios_Receber(IDGateway, Origem);

            // prepara listas
            GateX_Usuarios_PreparaListas(IDGateway);

            // retorna configuração
            return View(usuarios);
        }

        // Configuração Usuários - Receber
        private List<GateX_Usuarios_Dominio> GateX_Usuarios_Receber(int IDGateway, int Origem)
        {
            // usuários
            GateX_Usuarios_Metodos prgMetodos = new GateX_Usuarios_Metodos();
            List<GateX_Usuarios_Dominio> usuarios = new List<GateX_Usuarios_Dominio>();

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // status
            bool solicitaProg = true;

            // verifica Origem
            // 0: ler da gateway
            // 1: ler do banco de dados atual
            // 2: ler do banco de dados editando
            if (Origem == 0)
            {
                // status
                solicitaProg = false;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // solicita configuração das usuários
                    if (solicitaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_USUARIOS, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // copia configuração da gateway para 'editando'
                        // caso ainda não exista configuração 'atual', será criada 
                        usuarios = prgMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.Usuarios);
                    }
                }

                // verifica se não solicitou
                if (!solicitaProg)
                {
                    // copia configuração 'atual' para 'editando'
                    usuarios = prgMetodos.CopiarParaEditando(IDGateway);
                }
            }
            else if (Origem == 1)
            {
                // copia configuração 'atual' para 'editando'
                usuarios = prgMetodos.CopiarParaEditando(IDGateway);
            }
            else
            {
                // configuração em 'editando'
                usuarios = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            }

            // copia status
            ViewBag.SolicitaProg = solicitaProg;

            // retorna configuração
            return (usuarios);
        }

        // GET: Configuração Usuários - Editar
        public ActionResult GateX_Usuarios_Editar(int IDGateway, int NumUsuarioGateway)
        {
            // tela de ajuda - programacoes usuários
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            GateX_Usuarios_PreparaListas(IDGateway);

            // le usuário a ser programada (editando)          
            GateX_Usuarios_Metodos prgMetodos = new GateX_Usuarios_Metodos();
            GateX_Usuarios_Dominio usuario = prgMetodos.ListarPorNumUsuario(IDGateway, NumUsuarioGateway, STATUS_CFG.EDITANDO);

            return View(usuario);
        }

        // prepara listas
        private void GateX_Usuarios_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // lista
            List<ListaTiposDominio> listaRedirecionar = new List<ListaTiposDominio>();

            // preenche lista 
            foreach (KeyValuePair<int, string> redireciona in SGATEX.RedirecionarPara)
            {
                // default
                ListaTiposDominio tipo = new ListaTiposDominio();
                tipo.ID = redireciona.Key;
                tipo.Descricao = redireciona.Value;

                // insere na lista
                listaRedirecionar.Add(tipo);
            }

            // lista de saídas
            ViewBag.listaRedirecionar = listaRedirecionar;


            return;
        }

        // POST: Configuração Usuários - Programar
        [HttpPost]
        public ActionResult GateX_Usuarios_Programar(GateX_Usuarios_Dominio usuario)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica
            if (usuario.Nome != null && usuario.Senha != null)
            {
                // caixa alta
                usuario.Nome = usuario.Nome.ToUpper();
                usuario.Senha = usuario.Senha.ToUpper();

                // salva
                GateX_Usuarios_Metodos prgMetodos = new GateX_Usuarios_Metodos();
                prgMetodos.Salvar(usuario, STATUS_CFG.EDITANDO);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Usuários - Excluir
        public ActionResult GateX_Usuarios_Excluir(int IDGateway, int NumUsuarioGateway)
        {
            // apaga programacao
            GateX_Usuarios_Metodos prgMetodos = new GateX_Usuarios_Metodos();
            prgMetodos.ExcluirPorNumUsuario(IDGateway, NumUsuarioGateway, STATUS_CFG.EDITANDO);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // POST: Configuração Usuários - Enviar
        [HttpPost]
        public ActionResult GateX_Usuarios_Enviar(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // configuração em 'editando'
            GateX_Usuarios_Metodos prgMetodos = new GateX_Usuarios_Metodos();
            List<GateX_Usuarios_Dominio> usuarios = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // copia configuração 'editando' para 'atual'
            usuarios = prgMetodos.CopiarParaAtual(IDGateway);

            // variavel de retorno
            bool enviaProg = false;

            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // copia para lista de envio
                smartCom.gateX.Usuarios = usuarios;

                // envia 
                enviaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_USUARIOS, SMCOM_TIPO_SOL.ENVIA);
            }

            // verifica retorno
            if (enviaProg)
            {
                // excluir editando
                prgMetodos.ExcluirEditando(IDGateway);

                // histórico de configuração
                HistoricoConfiguracoesDominio hist = new HistoricoConfiguracoesDominio();
                hist.IDGateway = IDGateway;
                hist.NomeConfiguracao = "GateX_Usuarios";
                hist.Envio = "MQTT";
                hist.Atualizacao = DateTime.Now;
                hist.Versao = GateX_Versao_String(IDGateway);
                hist.IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");

                // insere histórico de configuração
                HistoricoConfiguracoesMetodos histMetodos = new HistoricoConfiguracoesMetodos();
                histMetodos.Inserir(hist);

                // caso OK insiro evento
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, IDGateway);
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Falha no envio da configuração!"
                };
            }

            // retorna status
            return Json(returnedData);
        }

        // recebe configuração (comando receber configurações)
        public bool GateX_Usuarios_ComandoRecebe(GatewaysDominio gateway, string versao)
        {
            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita programação
                if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_USUARIOS, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // salvar
                    GateX_Usuarios_Metodos prgMetodos = new GateX_Usuarios_Metodos();
                    prgMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.Usuarios, STATUS_CFG.ATUAL);

                    // ok
                    return (true);
                }
            }

            // erro
            return (false);
        }
    }
}

