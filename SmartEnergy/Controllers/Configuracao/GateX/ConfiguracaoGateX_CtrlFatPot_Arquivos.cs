﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Controle Fator de Potência XLS Download
        [HttpGet]
        public virtual ActionResult GateX_CtrlFatPot_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Controle Fator de Potência XLS
        public ActionResult GateX_CtrlFatPot_XLS(int IDGateway)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_GateX_CtrlFatPot(IDGateway);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_ControleFatPot_Gateway_{0:000000}_{1:yyyyMMddHHmm}.xls", IDGateway, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }





        // Controle Fator de Potência Planilha
        private HSSFWorkbook XLS_GateX_CtrlFatPot(int IDGateway)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.DescriConfigCtrlFatPot;

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // le lista controles fator de potência
            GateX_CtrlFatPot_Metodos ctrlMetodos = new GateX_CtrlFatPot_Metodos();
            List<GateX_CtrlFatPot_Dominio> ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // controle fator de potência (saídas)
            GateX_CtrlFatPot_SD_Metodos ctrlSDMetodos = new GateX_CtrlFatPot_SD_Metodos();
            List<GateX_CtrlFatPot_SD_Dominio> ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // medições energia
            GateX_MedEner_Lista_Metodos medEnerMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medEner_Lista = medEnerMetodos.ListarPorIDGateway(IDGateway);

            //  le lista de saidas da gateway
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidas = sdMetodos.ListarPorIDGateway(IDGateway);

            // le tipo comando alarme
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoCmdAlarme = listatiposMetodos.ListarTodos("TipoCmdAlarme", false, 0);

            // le tipo alarme sistema
            List<ListaTiposDominio> listatipoAlarmeSistema = listatiposMetodos.ListarTodos("TipoAlarmeSistema", false, 0);

            // le tipo controle demanda projetada
            List<ListaTiposDominio> listatipoCtrPj = listatiposMetodos.ListarTodos("TipoCtrlDemandaPrj", false, 0);

            // le tipo controle fator de potencia indutivo
            List<ListaTiposDominio> listatipoCtrFPInd = listatiposMetodos.ListarTodos("TipoCtrlFPInd", false, 0);

            // le tipo controle fator de potencia capacitivo
            List<ListaTiposDominio> listatipoCtrFPCap = listatiposMetodos.ListarTodos("TipoCtrlFPCap", false, 0);

            // cria lista de alarmes do usuario
            List<ListaTiposDominio> listaAlmUsuario = new List<ListaTiposDominio>();

            for (int j = 0; j < 100; j++)
            {
                var alm = new ListaTiposDominio()
                {
                    ID = j,
                    Descricao = string.Format("Alarme de Usuário [{0:00}]", j)
                };

                listaAlmUsuario.Add(alm);
            }


            // cria planilha
            var sheet = workbook.CreateSheet("Controle Fator de Potência");

            // cabecalho
            string[] cabecalho = { "Controle", "Medição", "Medição Secundária", "Limite Indutivo", "Limite Capacitivo", "Alarme manter saídas LIGADAS", "", "Alarme manter saídas DESLIGADAS", "", "Saída", "Prioridade do Capacitor", "Potência do Capacitor (kVAr)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;


            if (ctrls != null && ctrlSDs != null)
            {
                // percorre controles
                foreach (GateX_CtrlFatPot_Dominio ctrl in ctrls)
                {
                    if (ctrl.Programado)
                    {
                        // percorre saídas
                        foreach (GateX_CtrlFatPot_SD_Dominio sd in ctrlSDs)
                        {
                            if (sd.Programado && sd.NumCtrlGateway == ctrl.NumCtrlGateway)
                            {
                                // adiciona linha
                                row = sheet.CreateRow(rowIndex++);

                                // Número controle fator de potência
                                numeroCelulaXLS(row, 0, ctrl.NumCtrlGateway, _intCellStyle);

                                // medição
                                string medicao = "---";

                                if (ctrl.Medicao != 255)
                                {
                                    if (medEner_Lista != null)
                                    {
                                        medicao = string.Format("[{0:00}] {1}", ctrl.Medicao, medEner_Lista[ctrl.Medicao].Descricao);
                                    }
                                    else
                                    {
                                        medicao = string.Format("[{0:00}] Medição de Energia {1}", ctrl.Medicao, ctrl.Medicao);
                                    }
                                }

                                textoCelulaXLS(row, 1, medicao);

                                // medição secundária
                                medicao = "---";

                                if (ctrl.Medicao2 != 255)
                                {
                                    if (medEner_Lista != null)
                                    {
                                        medicao = string.Format("[{0:00}] {1}", ctrl.Medicao2, medEner_Lista[ctrl.Medicao2].Descricao);
                                    }
                                    else
                                    {
                                        medicao = string.Format("[{0:00}] Medição de Energia {1}", ctrl.Medicao2, ctrl.Medicao2);
                                    }
                                }

                                textoCelulaXLS(row, 2, medicao);

                                // limite capacitivo
                                numeroCelulaXLS(row, 3, ctrl.LimiteCapacitivo / 1000.0, _3CellStyle);

                                // limite indutivo
                                numeroCelulaXLS(row, 4, ctrl.LimiteIndutivo / 1000.0, _3CellStyle);

                                // alarme manter saidas ligadas
                                string alarme_tipo = "---";
                                string alarme_aux = "---";

                                // verifica se desprogramado
                                if (ctrl.AlarmeTipoAllOn == 0 && ctrl.AlarmeAuxAllOn == 0)
                                {
                                    // nenhum
                                    alarme_tipo = "---";
                                    alarme_aux = "---";
                                }
                                else
                                {
                                    // tipo alarme
                                    if (listatipoCmdAlarme != null) alarme_tipo = listatipoCmdAlarme.Find(x => x.ID == ctrl.AlarmeTipoAllOn).Descricao;

                                    // alarme
                                    switch (ctrl.AlarmeTipoAllOn)
                                    {
                                        case 255: // alm usuario
                                            if (listaAlmUsuario != null) alarme_aux = listaAlmUsuario.Find(x => x.ID == ctrl.AlarmeAuxAllOn).Descricao;

                                            break;

                                        case 0: // alm sistema
                                            if (listatipoAlarmeSistema != null) alarme_aux = listatipoAlarmeSistema.Find(x => x.ID == ctrl.AlarmeAuxAllOn).Descricao;

                                            break;

                                        case 1: // falta pulsos
                                            if (medEner_Lista != null)
                                            {
                                                GateX_MedEner_Lista_Dominio medLista = medEner_Lista.Find(x => x.NumMedicaoGateway == ctrl.AlarmeAuxAllOn);

                                                alarme_aux = string.Format("[{0}] Medição de Energia {1}", medLista.NumMedicaoGateway, medLista.NumMedicaoGateway);
                                                if (medLista != null)
                                                {
                                                    alarme_aux = string.Format("[{0}] {1}", medLista.NumMedicaoGateway, medLista.Descricao);
                                                }
                                            }    

                                            break;

                                        case 2: // ultrapassagem de demanda
                                            if (listatipoCtrPj != null) alarme_aux = listatipoCtrPj.Find(x => x.ID == ctrl.AlarmeAuxAllOn).Descricao;

                                            break;

                                        case 3: // ultrapassagem fator de potencia indutivo
                                            if (listatipoCtrFPInd != null) alarme_aux = listatipoCtrFPInd.Find(x => x.ID == ctrl.AlarmeAuxAllOn).Descricao;

                                            break;

                                        case 4: // ultrapassagem fator de potencia capacitivo
                                            if (listatipoCtrFPCap != null) alarme_aux = listatipoCtrFPCap.Find(x => x.ID == ctrl.AlarmeAuxAllOn).Descricao;

                                            break;

                                        case 5: // comando supervisionado
                                            if (saidas != null)
                                            {
                                                GateX_SD_Lista_Dominio sdLista = saidas.Find(x => x.NumSaidaGateway == ctrl.AlarmeAuxAllOn);

                                                alarme_aux = string.Format("[{0}] Saída {1}", sdLista.NumSaidaGateway, sdLista.NumSaidaGateway);
                                                if (sdLista != null)
                                                {
                                                    alarme_aux = string.Format("[{0}] {1}", sdLista.NumSaidaGateway, sdLista.Descricao);
                                                }
                                            }

                                            break;
                                    }
                                }
                                textoCelulaXLS(row, 5, alarme_tipo);
                                textoCelulaXLS(row, 6, alarme_aux);

                                // alarme manter saidas desligadas
                                alarme_tipo = "---";
                                alarme_aux = "---";

                                // verifica se desprogramado
                                if (ctrl.AlarmeTipoAllOff == 0 && ctrl.AlarmeAuxAllOff == 0)
                                {
                                    // nenhum
                                    alarme_tipo = "---";
                                    alarme_aux = "---";
                                }
                                else
                                {
                                    // tipo alarme
                                    if (listatipoCmdAlarme != null) alarme_tipo = listatipoCmdAlarme.Find(x => x.ID == ctrl.AlarmeTipoAllOff).Descricao;

                                    // alarme
                                    switch (ctrl.AlarmeTipoAllOff)
                                    {
                                        case 255: // alm usuario
                                            if (listaAlmUsuario != null) alarme_aux = listaAlmUsuario.Find(x => x.ID == ctrl.AlarmeAuxAllOff).Descricao;

                                            break;

                                        case 0: // alm sistema
                                            if (listatipoAlarmeSistema != null) alarme_aux = listatipoAlarmeSistema.Find(x => x.ID == ctrl.AlarmeAuxAllOff).Descricao;

                                            break;

                                        case 1: // falta pulsos
                                            if (medEner_Lista != null)
                                            {
                                                GateX_MedEner_Lista_Dominio medLista = medEner_Lista.Find(x => x.NumMedicaoGateway == ctrl.AlarmeAuxAllOff);

                                                alarme_aux = string.Format("[{0}] Medição de Energia {1}", medLista.NumMedicaoGateway, medLista.NumMedicaoGateway);
                                                if (medLista != null)
                                                {
                                                    alarme_aux = string.Format("[{0}] {1}", medLista.NumMedicaoGateway, medLista.Descricao);
                                                }
                                            }    

                                            break;

                                        case 2: // ultrapassagem de demanda
                                            if (listatipoCtrPj != null) alarme_aux = listatipoCtrPj.Find(x => x.ID == ctrl.AlarmeAuxAllOff).Descricao;

                                            break;

                                        case 3: // ultrapassagem fator de potencia indutivo
                                            if (listatipoCtrFPInd != null) alarme_aux = listatipoCtrFPInd.Find(x => x.ID == ctrl.AlarmeAuxAllOff).Descricao;

                                            break;

                                        case 4: // ultrapassagem fator de potencia capacitivo
                                            if (listatipoCtrFPCap != null) alarme_aux = listatipoCtrFPCap.Find(x => x.ID == ctrl.AlarmeAuxAllOff).Descricao;

                                            break;

                                        case 5: // comando supervisionado
                                            if (saidas != null)
                                            {
                                                GateX_SD_Lista_Dominio sdLista = saidas.Find(x => x.NumSaidaGateway == ctrl.AlarmeAuxAllOff);

                                                alarme_aux = string.Format("[{0}] Saída {1}", sdLista.NumSaidaGateway, sdLista.NumSaidaGateway);
                                                if (sdLista != null)
                                                {
                                                    alarme_aux = string.Format("[{0}] {1}", sdLista.NumSaidaGateway, sdLista.Descricao);
                                                }
                                            }

                                            break;
                                    }
                                }
                                textoCelulaXLS(row, 7, alarme_tipo);
                                textoCelulaXLS(row, 8, alarme_aux);

                                // Nome saída
                                string saida = string.Format("[{0}] Saída {1}", sd.NumSaida, sd.NumSaida);
                                if (saidas != null) saida = string.Format("[{0}] {1}", sd.NumSaida, saidas.Find(x => x.NumSaidaGateway == sd.NumSaida).Descricao);
                                textoCelulaXLS(row, 9, saida);

                                // prioridade do capacitor
                                numeroCelulaXLS(row, 10, sd.ControlPrioFP, _intCellStyle);

                                // potência do capacitor
                                numeroCelulaXLS(row, 11, sd.PotenciaCapacitor, _intCellStyle);
                            }
                        }
                    }
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
            {
                sheet.SetColumnWidth(i, 7000);
                if (i == 4)
                {
                    sheet.SetColumnWidth(i, 10000);
                }

            }

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS_GateX(workbook, sheet, _negritoCellStyle);

            // largura de cada coluna
            sheet.SetColumnWidth(0, 7000);
            sheet.SetColumnWidth(1, 12000);

            // retorna planilha
            return workbook;

        }

        // GET: Controle Fator de Potência PDF
        public ActionResult GateX_CtrlFatPot_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // controle fator de potência
            GateX_CtrlFatPot_Metodos ctrlMetodos = new GateX_CtrlFatPot_Metodos();
            List<GateX_CtrlFatPot_Dominio> ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlFatPot = ctrls;

            // controle fator de potência (saídas)
            GateX_CtrlFatPot_SD_Metodos ctrlSDMetodos = new GateX_CtrlFatPot_SD_Metodos();
            List<GateX_CtrlFatPot_SD_Dominio> ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlFatPot_SD = ctrlSDs;

            // medições energia
            GateX_MedEner_Lista_Metodos medEnerMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medEner_Lista = medEnerMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medEner_Lista = medEner_Lista;

            // lista saidas 
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.SaidasDigitais = saidasDigitais;


            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_ControleFatPot_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_CtrlFatPot_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        // GET: Controle Fator de Potência EMAIL
        public async Task<ActionResult> GateX_CtrlFatPot_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.DescriConfigCtrlFatPot;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // controle fator de potência
            GateX_CtrlFatPot_Metodos ctrlMetodos = new GateX_CtrlFatPot_Metodos();
            List<GateX_CtrlFatPot_Dominio> ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlFatPot = ctrls;

            // controle fator de potência (saídas)
            GateX_CtrlFatPot_SD_Metodos ctrlSDMetodos = new GateX_CtrlFatPot_SD_Metodos();
            List<GateX_CtrlFatPot_SD_Dominio> ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlFatPot_SD = ctrlSDs;

            // medições energia
            GateX_MedEner_Lista_Metodos medEnerMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medEner_Lista = medEnerMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medEner_Lista = medEner_Lista;

            // lista saidas 
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.SaidasDigitais = saidasDigitais;


            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_ControleFatPot_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_CtrlFatPot_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "GateX_ControleFatPotEmail";
            var message = await EMailTemplateAsync(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.NomeGateway", ViewBag.NomeGateway);
            message = message.Replace("ViewBag.DescrConfig", ViewBag.DescrConfig);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Controle Fator de Potência Print
        public ActionResult GateX_CtrlFatPot_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // controle fator de potência
            GateX_CtrlFatPot_Metodos ctrlMetodos = new GateX_CtrlFatPot_Metodos();
            List<GateX_CtrlFatPot_Dominio> ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlFatPot = ctrls;

            // controle fator de potência (saídas)
            GateX_CtrlFatPot_SD_Metodos ctrlSDMetodos = new GateX_CtrlFatPot_SD_Metodos();
            List<GateX_CtrlFatPot_SD_Dominio> ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlFatPot_SD = ctrlSDs;

            // medições energia
            GateX_MedEner_Lista_Metodos medEnerMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medEner_Lista = medEnerMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medEner_Lista = medEner_Lista;

            // lista saidas 
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.SaidasDigitais = saidasDigitais;


            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // imprime
            return View();
        }
    }
}
