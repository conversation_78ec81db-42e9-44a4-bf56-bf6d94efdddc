﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Configuracao Controle Demanda Projetada
        public ActionResult GateX_CtrlDemProj(int IDGateway, int Origem = 0)
        {
            // tela de ajuda - controle demanda projetada
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // receber configuração
            List<GateX_CtrlDemProj_Dominio> ctrls = GateX_CtrlDemProj_Receber(IDGateway, Origem);

            // prepara listas
            GateX_CtrlDemProj_PreparaListas(IDGateway);

            // retorna configuração
            return View(ctrls);
        }

        // Configuração Controle Demanda Projetada - Receber
        private List<GateX_CtrlDemProj_Dominio> GateX_CtrlDemProj_Receber(int IDGateway, int Origem)
        {
            // controle demanda projetada
            GateX_CtrlDemProj_Metodos ctrlMetodos = new GateX_CtrlDemProj_Metodos();
            List<GateX_CtrlDemProj_Dominio> ctrls = new List<GateX_CtrlDemProj_Dominio>();

            // controle demanda projetada (saídas)
            GateX_CtrlDemProj_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemProj_SD_Metodos();
            List<GateX_CtrlDemProj_SD_Dominio> ctrlSDs = new List<GateX_CtrlDemProj_SD_Dominio>();

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // status
            bool solicitaCtrl = true;
            bool solicitaCtrlSD = true;

            // verifica Origem
            // 0: ler da gateway
            // 1: ler do banco de dados atual
            // 2: ler do banco de dados editando
            if (Origem == 0)
            {
                // status
                solicitaCtrl = false;
                solicitaCtrlSD = false;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // solicita configuração dos controles demanda projetada
                    if (solicitaCtrl = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_DEM_PROJ, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // solicita configuração dos controles demanda projetada (saídas)
                        if (solicitaCtrlSD = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_DEM_PROJ_SD, SMCOM_TIPO_SOL.SOLICITA))
                        {
                            // copia configuração da gateway para 'editando'
                            // caso ainda não exista configuração 'atual', será criada 
                            ctrls = ctrlMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.CtrlDemProj);
                            ctrlSDs = ctrlSDMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.CtrlDemProj_SD);
                        }
                    }
                }

                // verifica se solicitou
                if (solicitaCtrl && solicitaCtrlSD)
                {
                    // atualiza lista de medições energia
                    GateX_AtualizaLista(gateway, SMCOM_FUNCOES_SOL.PROGR_MED_EN_LISTA);

                    // atualiza lista de saidas digitais
                    GateX_AtualizaLista(gateway, SMCOM_FUNCOES_SOL.PROGR_SD_LISTA);
                }
                else
                {
                    // copia configuração 'atual' para 'editando'
                    ctrls = ctrlMetodos.CopiarParaEditando(IDGateway);
                    ctrlSDs = ctrlSDMetodos.CopiarParaEditando(IDGateway);
                }
            }
            else if (Origem == 1)
            {
                // copia configuração 'atual' para 'editando'
                ctrls = ctrlMetodos.CopiarParaEditando(IDGateway);
                ctrlSDs = ctrlSDMetodos.CopiarParaEditando(IDGateway);
            }
            else
            {
                // configuração em 'editando'
                ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
                ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            }

            // copia status
            ViewBag.solicitaCtrl = solicitaCtrl;
            ViewBag.solicitaCtrlSD = solicitaCtrlSD;

            // copia configuração
            ViewBag.CtrlDemProj = ctrls;
            ViewBag.CtrlDemProj_SD = ctrlSDs;

            // retorna configuração
            return (ctrls);
        }

        // GET: Configuracao Controle Demanda Projetada - Editar
        public ActionResult GateX_CtrlDemProj_Editar(int IDGateway, int NumCtrlGateway)
        {
            // tela de ajuda - controle demanda projetada
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            GateX_CtrlDemProj_PreparaListas(IDGateway);

            // controle demanda projetada
            GateX_CtrlDemProj_Metodos ctrlMetodos = new GateX_CtrlDemProj_Metodos();
            GateX_CtrlDemProj_Dominio ctrl = ctrlMetodos.ListarPorNumCtrl(IDGateway, NumCtrlGateway, STATUS_CFG.EDITANDO);

            // divide fator de controle por 10 (920 = 92.0)
            ctrl.DemMaxP /= 10.0;
            ctrl.DemMaxP2 /= 10.0;
            ctrl.DemMaxF /= 10.0;
            ctrl.DemMaxF2 /= 10.0;

            // alarmes
            if (ctrl.AlarmeTipoAllOn == 0 && ctrl.AlarmeAuxAllOn == 0)
            {
                // nenhum
                ctrl.AlarmeTipoAllOn = 100;
            }

            if (ctrl.AlarmeTipoAllOff == 0 && ctrl.AlarmeAuxAllOff == 0)
            {
                // nenhum
                ctrl.AlarmeTipoAllOff = 100;
            }

            // multiplica tempo entre religamentos por 10 (10 = 1)
            ctrl.TempoReliga *= 10;

            ViewBag.CtrlDemProj = ctrl;

            // controle demanda projetada editar
            GateX_CtrlDemProj_Editar ctrlEditar = new GateX_CtrlDemProj_Editar();
            ctrlEditar.IDGateway = ctrl.IDGateway;
            ctrlEditar.Programado = ctrl.Programado;
            ctrlEditar.NumCtrlGateway = ctrl.NumCtrlGateway;
            ctrlEditar.Medicao = ctrl.Medicao;
            ctrlEditar.Medicao2 = ctrl.Medicao2;
            ctrlEditar.DemMaxP = ctrl.DemMaxP;
            ctrlEditar.DemMaxP2 = ctrl.DemMaxP2;
            ctrlEditar.DemMaxF = ctrl.DemMaxF;
            ctrlEditar.DemMaxF2 = ctrl.DemMaxF2;
            ctrlEditar.Otimizado = ctrl.Otimizado;
            ctrlEditar.QtSaidaIni = ctrl.QtSaidaIni;
            ctrlEditar.TempoReliga = ctrl.TempoReliga;
            ctrlEditar.AlarmeTipoAllOn = ctrl.AlarmeTipoAllOn;
            ctrlEditar.AlarmeAuxAllOn = ctrl.AlarmeAuxAllOn;
            ctrlEditar.AlarmeTipoAllOff = ctrl.AlarmeTipoAllOff;
            ctrlEditar.AlarmeAuxAllOff = ctrl.AlarmeAuxAllOff;

            // controle demanda projetada (prioridades adaptativas)
            List<GateX_CtrlDemProj_Adap_Dominio> ctrlAdaps = new List<GateX_CtrlDemProj_Adap_Dominio>();

            for (int j = 0; j < SGATEX.NUM_PRIO_ADAP; j++)
            {
                // copia
                GateX_CtrlDemProj_Adap_Dominio adap = new GateX_CtrlDemProj_Adap_Dominio();
                adap.IDGateway = ctrl.adap[j].IDGateway;
                adap.NumCtrlGateway = ctrl.adap[j].NumCtrlGateway;
                adap.NumPrio = j;
                adap.Comando = ctrl.adap[j].Comando;
                adap.UsuarioAlarme = ctrl.adap[j].UsuarioAlarme;
                adap.EstadoAlarme = ctrl.adap[j].EstadoAlarme;

                ctrlAdaps.Add(adap);
            }
            ViewBag.CtrlDemProj_Adap = ctrlAdaps;

            // controle demanda projetada (saídas)
            GateX_CtrlDemProj_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemProj_SD_Metodos();
            List<GateX_CtrlDemProj_SD_Dominio> ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.CtrlDemProj_SD = ctrlSDs;

            // medições
            GateX_CtrlDemProj_ObterMedicoes(IDGateway);

            // saidas
            GateX_CtrlDemProj_ObterSaidas(IDGateway);

            return View(ctrlEditar);
        }

        private void GateX_CtrlDemProj_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // medições energia
            GateX_MedEner_Lista_Metodos medEnerMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medEner_Lista = medEnerMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medEner_Lista = medEner_Lista;

            // medições
            List<ListaTiposDominio> listaMedicoes = new List<ListaTiposDominio>();
            ViewBag.listaMedicoes = listaMedicoes;

            // le saidas
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.SaidasDigitais = saidasDigitais;

            // saidas
            List<ListaTiposDominio> listaSaidas = new List<ListaTiposDominio>();
            ViewBag.listaSaidas = listaSaidas;


            // le tipo comando alarme
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listaCtrlOtimizacao = listatiposMetodos.ListarTodos("TipoCtrlOtimizacao", false, 0);
            ViewBag.listaCtrlOtimizacao = listaCtrlOtimizacao;

            // le tipo comando alarme
            List<ListaTiposDominio> listatipoCmdAlarme = listatiposMetodos.ListarTodos("TipoCmdAlarme", false, 0);
            ViewBag.listaTipoCmdAlarme = listatipoCmdAlarme;

            // le tipo alarme sistema
            List<ListaTiposDominio> listatipoAlarmeSistema = listatiposMetodos.ListarTodos("TipoAlarmeSistema", false, 0);
            ViewBag.listaTipoAlarmeSistema = listatipoAlarmeSistema;

            // le tipo controle demanda projetada
            List<ListaTiposDominio> listatipoCtrPj = listatiposMetodos.ListarTodos("TipoCtrlDemandaPrj", false, 0);
            ViewBag.listaTipoCtrPj = listatipoCtrPj;

            // le tipo controle fator de potencia indutivo
            List<ListaTiposDominio> listatipoCtrFPInd = listatiposMetodos.ListarTodos("TipoCtrlFPInd", false, 0);
            ViewBag.listaTipoCtrFPInd = listatipoCtrFPInd;

            // le tipo controle fator de potencia capacitivo
            List<ListaTiposDominio> listatipoCtrFPCap = listatiposMetodos.ListarTodos("TipoCtrlFPCap", false, 0);
            ViewBag.listaTipoCtrFPCap = listatipoCtrFPCap;

            // cria lista de alarmes do usuario
            List<ListaTiposDominio> listaAlmUsuario = new List<ListaTiposDominio>();

            for (int i = 0; i < SGATEX.NUM_CTRL_ALM; i++)
            {
                var alm = new ListaTiposDominio()
                {
                    ID = i,
                    Descricao = string.Format("Alarme de Usuário [{0:00}]", i)
                };

                listaAlmUsuario.Add(alm);
            }

            // copia lista alarme usuario
            ViewBag.listaAlmUsuario = listaAlmUsuario;

            // cria lista de prioridades da carga
            List<ListaTiposDominio> listaPrioCarga = new List<ListaTiposDominio>();

            for (int i = 0; i < SGATEX.NUM_PRIO_ADAP; i++)
            {
                var prio = new ListaTiposDominio()
                {
                    ID = i,
                    Descricao = string.Format("Prioridade {0}", i)
                };

                listaPrioCarga.Add(prio);
            }

            // copia lista prioridades da carga
            ViewBag.listaPrioCarga = listaPrioCarga;

            // le tipo prioridade adaptativa 'acao'
            List<ListaTiposDominio> listaPrioAdap_Acao = listatiposMetodos.ListarTodos("TipoPrioAdap_Acao", false, 0);
            ViewBag.listaPrioAdap_Acao = listaPrioAdap_Acao;

            // le tipo prioridade adaptativa 'for'
            List<ListaTiposDominio> listaPrioAdap_For = listatiposMetodos.ListarTodos("TipoPrioAdap_For", false, 0);
            ViewBag.listaPrioAdap_For = listaPrioAdap_For;

            return;
        }

        // POST: Configuracao Controle Demanda Projetada - Programar
        [HttpPost]
        public ActionResult GateX_CtrlDemProj_Programar(GateX_CtrlDemProj_Editar ctrlEditar, List<GateX_CtrlDemProj_Adap_Dominio> prioAdaps, List<GateX_CtrlDemProj_SD_Dominio> ctrlSDs)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };


            // controle demanda projetada
            GateX_CtrlDemProj_Dominio CtrlDemProj = new GateX_CtrlDemProj_Dominio();
            CtrlDemProj.IDGateway = ctrlEditar.IDGateway;
            CtrlDemProj.Programado = ctrlEditar.Programado;
            CtrlDemProj.NumCtrlGateway = ctrlEditar.NumCtrlGateway;
            CtrlDemProj.Medicao = ctrlEditar.Medicao;
            CtrlDemProj.Medicao2 = ctrlEditar.Medicao2;
            CtrlDemProj.DemMaxP = ctrlEditar.DemMaxP;
            CtrlDemProj.DemMaxP2 = ctrlEditar.DemMaxP2;
            CtrlDemProj.DemMaxF = ctrlEditar.DemMaxF;
            CtrlDemProj.DemMaxF2 = ctrlEditar.DemMaxF2;
            CtrlDemProj.Otimizado = ctrlEditar.Otimizado;
            CtrlDemProj.QtSaidaIni = ctrlEditar.QtSaidaIni;
            CtrlDemProj.TempoReliga = ctrlEditar.TempoReliga;
            CtrlDemProj.AlarmeTipoAllOn = ctrlEditar.AlarmeTipoAllOn;
            CtrlDemProj.AlarmeAuxAllOn = ctrlEditar.AlarmeAuxAllOn;
            CtrlDemProj.AlarmeTipoAllOff = ctrlEditar.AlarmeTipoAllOff;
            CtrlDemProj.AlarmeAuxAllOff = ctrlEditar.AlarmeAuxAllOff;

            // multiplica fator de controle por 10 (920 = 92.0)
            CtrlDemProj.DemMaxP *= 10.0;
            CtrlDemProj.DemMaxP2 *= 10.0;
            CtrlDemProj.DemMaxF *= 10.0;
            CtrlDemProj.DemMaxF2 *= 10.0;

            // alarmes
            if (CtrlDemProj.AlarmeTipoAllOn == 100)
            {
                // nenhum
                CtrlDemProj.AlarmeTipoAllOn = 0;
                CtrlDemProj.AlarmeAuxAllOn = 0;
            }

            if (CtrlDemProj.AlarmeTipoAllOff == 100)
            {
                // nenhum
                CtrlDemProj.AlarmeTipoAllOff = 0;
                CtrlDemProj.AlarmeAuxAllOff = 0;
            }

            // divide tempo entre religamentos por 10 (10 = 1)
            CtrlDemProj.TempoReliga /= 10;

            // prioridade adaptativas
            for (int j = 0; j < SGATEX.NUM_PRIO_ADAP; j++)
            {
                // default
                GateX_CtrlDemProj_Adap_Dominio adap = new GateX_CtrlDemProj_Adap_Dominio();
                adap.IDGateway = CtrlDemProj.IDGateway;
                adap.NumCtrlGateway = CtrlDemProj.NumCtrlGateway;
                adap.NumPrio = j;
                adap.Comando = 0;
                adap.UsuarioAlarme = 0;
                adap.EstadoAlarme = 0;

                CtrlDemProj.adap[j] = adap;
            }

            // percorre programados e coloca na lista geral
            foreach (GateX_CtrlDemProj_Adap_Dominio adap in prioAdaps)
            {
                // indice
                int indice = adap.NumPrio;

                // copia
                CtrlDemProj.adap[indice] = adap;
            }

            // salva controle demanda projetada
            GateX_CtrlDemProj_Metodos ctrlMetodos = new GateX_CtrlDemProj_Metodos();
            ctrlMetodos.Salvar(CtrlDemProj, STATUS_CFG.EDITANDO);

            // excluir todas saidas do controle
            GateX_CtrlDemProj_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemProj_SD_Metodos();
            ctrlSDMetodos.ExcluirPorNumCtrl(CtrlDemProj.IDGateway, CtrlDemProj.NumCtrlGateway, STATUS_CFG.EDITANDO);

            // salva controle demanda projetada (saídas)
            foreach (GateX_CtrlDemProj_SD_Dominio sd in ctrlSDs)
            {
                // verifica se saída é deste controle
                if (sd.NumCtrlGateway == CtrlDemProj.NumCtrlGateway)
                {
                    ctrlSDMetodos.Salvar(sd, STATUS_CFG.EDITANDO);
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuracao Controle Demanda Projetada - Excluir
        public ActionResult GateX_CtrlDemProj_Excluir(int IDGateway, int NumCtrlGateway)
        {
            // lista gateway para inserir evento                        
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // apaga programacao
            GateX_CtrlDemProj_Metodos ctrlMetodos = new GateX_CtrlDemProj_Metodos();
            ctrlMetodos.ExcluirPorNumCtrl(IDGateway, NumCtrlGateway, STATUS_CFG.EDITANDO);

            GateX_CtrlDemProj_Adap_Metodos adapMetodos = new GateX_CtrlDemProj_Adap_Metodos();
            adapMetodos.ExcluirPorNumCtrl(IDGateway, NumCtrlGateway, STATUS_CFG.EDITANDO);

            GateX_CtrlDemProj_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemProj_SD_Metodos();
            ctrlSDMetodos.ExcluirPorNumCtrl(IDGateway, NumCtrlGateway, STATUS_CFG.EDITANDO);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // POST: Configuracao Controle Demanda Projetada - Enviar
        [HttpPost]
        public ActionResult GateX_CtrlDemProj_Enviar(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // copia configuração 'editando' para 'atual'
            GateX_CtrlDemProj_Metodos ctrlMetodos = new GateX_CtrlDemProj_Metodos();
            List<GateX_CtrlDemProj_Dominio> ctrls = ctrlMetodos.CopiarParaAtual(IDGateway);

            GateX_CtrlDemProj_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemProj_SD_Metodos();
            List<GateX_CtrlDemProj_SD_Dominio> ctrlSDs = ctrlSDMetodos.CopiarParaAtual(IDGateway);

            // variavel de retorno
            bool enviaCtrlDemProj = false;
            bool enviaCtrlDemProj_SD = false;

            // verifica
            if (ctrls != null && ctrlSDs != null && gateway != null)
            {
                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // copia para lista de envio
                    smartCom.gateX.CtrlDemProj = ctrls;

                    // envia controle demanda projetada
                    enviaCtrlDemProj = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_DEM_PROJ, SMCOM_TIPO_SOL.ENVIA);

                    // copia para lista de envio
                    smartCom.gateX.CtrlDemProj_SD = ctrlSDs;

                    // envia controle demanda projetada (saídas)
                    enviaCtrlDemProj_SD = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_DEM_PROJ_SD, SMCOM_TIPO_SOL.ENVIA);
                }
            }

            // verifica retorno
            if (enviaCtrlDemProj && enviaCtrlDemProj_SD)
            {
                // excluir editando
                ctrlMetodos.ExcluirEditando(IDGateway);
                ctrlSDMetodos.ExcluirEditando(IDGateway);

                // histórico de configuração
                HistoricoConfiguracoesDominio hist = new HistoricoConfiguracoesDominio();
                hist.IDGateway = IDGateway;
                hist.NomeConfiguracao = "GateX_CtrlDemProj";
                hist.Envio = "MQTT";
                hist.Atualizacao = DateTime.Now;
                hist.Versao = GateX_Versao_String(IDGateway);
                hist.IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");

                // insere histórico de configuração
                HistoricoConfiguracoesMetodos histMetodos = new HistoricoConfiguracoesMetodos();
                histMetodos.Inserir(hist);

                // controle demanda projetada (saídas)
                hist.NomeConfiguracao = "GateX_CtrlDemProj_SD";
                histMetodos.Inserir(hist);

                // caso OK insiro evento
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, IDGateway);
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Falha no envio da configuração!"
                };
            }

            // retorna status
            return Json(returnedData);
        }


        //
        // MEDICOES
        //

        // GET: Obter Medicoes
        public JsonResult GateX_CtrlDemProj_ObterMedicoes(int IDGateway)
        {
            // medições
            List<ListaTiposDominio> listaMedicoes = new List<ListaTiposDominio>();

            // medições energia
            GateX_MedEner_Lista_Metodos medEnerMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medEner_Lista = medEnerMetodos.ListarPorIDGateway(IDGateway);

            // nenhum
            ListaTiposDominio tipo_nenhum = new ListaTiposDominio();
            tipo_nenhum.ID = 255;
            tipo_nenhum.Descricao = "Nenhum";
            listaMedicoes.Add(tipo_nenhum);

            // preenche lista de medições
            for (int i = 0; i < SGATEX.NUM_MED_EN; i++)
            {
                // default
                ListaTiposDominio tipo = new ListaTiposDominio();
                tipo.ID = i;
                tipo.Descricao = string.Format("[{0:00}] Medição de Energia {1}", i, i);

                // copia
                if (medEner_Lista != null)
                {
                    // verifica se tem na lista
                    GateX_MedEner_Lista_Dominio med = medEner_Lista.First(item => item.NumMedicaoGateway == i);

                    if (med != null)
                    {
                        tipo.Descricao = string.Format("[{0:00}] {1}", i, med.Descricao);
                    }
                }

                // insere na lista
                listaMedicoes.Add(tipo);
            }

            // lista de medições
            ViewBag.listaMedicoes = listaMedicoes;

            // retorna o valor em JSON
            return Json(listaMedicoes, JsonRequestBehavior.AllowGet);
        }



        //
        // SAIDAS
        //

        // GET: Obter Saidas
        public JsonResult GateX_CtrlDemProj_ObterSaidas(int IDGateway)
        {
            // saidas
            List<ListaTiposDominio> listaSaidas = new List<ListaTiposDominio>();

            // le saidas
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);

            // preenche lista de saídas
            for (int i = 0; i < SGATEX.NUM_SD; i++)
            {
                // default
                ListaTiposDominio tipo = new ListaTiposDominio();
                tipo.ID = i;
                tipo.Descricao = string.Format("[{0:00}] Saída Digital {1}", i, i);

                // copia
                if (saidasDigitais != null)
                {
                    // verifica se tem na lista
                    GateX_SD_Lista_Dominio med = saidasDigitais.First(item => item.NumSaidaGateway == i);

                    if (med != null)
                    {
                        tipo.Descricao = string.Format("[{0:00}] {1}", i, med.Descricao);
                    }
                }

                // insere na lista
                listaSaidas.Add(tipo);
            }

            // lista de saídas
            ViewBag.listaSaidas = listaSaidas;

            // retorna o valor em JSON
            return Json(listaSaidas, JsonRequestBehavior.AllowGet);
        }

        // recebe configuração (comando receber configurações)
        public bool GateX_CtrlDemProj_ComandoRecebe(GatewaysDominio gateway, string versao)
        {
            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita programação
                if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_DEM_PROJ, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // solicita configuração dos controles demanda projetada (saídas)
                    if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_DEM_PROJ_SD, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // salvar
                        GateX_CtrlDemProj_Metodos ctrlMetodos = new GateX_CtrlDemProj_Metodos();
                        ctrlMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.CtrlDemProj, STATUS_CFG.ATUAL);

                        // salvar (controle demanda projetada (saídas))
                        GateX_CtrlDemProj_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemProj_SD_Metodos();
                        ctrlSDMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.CtrlDemProj_SD, STATUS_CFG.ATUAL);

                        // ok
                        return (true);
                    }
                }
            }

            // erro
            return (false);
        }
    }
}