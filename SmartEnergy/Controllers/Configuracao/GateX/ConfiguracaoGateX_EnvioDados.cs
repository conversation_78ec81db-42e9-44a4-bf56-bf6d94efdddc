﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Comando Envio Dados
        public ActionResult GateX_EnvioDados(int IDGateway)
        {
            // tela de ajuda - comando envio dados
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // receber comando
            GateX_EnvioDados_Dominio cmd = GateX_EnvioDados_Receber(IDGateway);

            // prepara listas
            GateX_EnvioDados_PreparaListas(IDGateway, cmd.tyUp);

            // retorna
            return View(cmd);
        }

        // Comando Envio Dados - Receber
        private GateX_EnvioDados_Dominio GateX_EnvioDados_Receber(int IDGateway)
        {
            // comando
            GateX_EnvioDados_Dominio cmd = new GateX_EnvioDados_Dominio();
            GateX_Driver_Dominio drv = new GateX_Driver_Dominio();

            // default
            cmd.ultimoUpload = new DateTime(2000, 1, 1, 0, 0, 0);
            drv.xUpldProt = 0;

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // status
            bool solicitaProg = false;
            bool solicitaDrv = false;

            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita comando
                if (solicitaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.COMANDO_UPLOAD, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // solicita configuração driver
                    if (solicitaDrv = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_DRIVER, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // copia comando recebido
                        cmd = smartCom.gateX.EnvioDados;

                        // copia configuração recebida
                        drv = smartCom.gateX.Driver;
                    }
                }
            }

            // default
            cmd.iniUpload = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 0, 0, 0);
            
            cmd.tyUp = 0x30;                // energia elétrica
            cmd.iMd = 0;                    // medição 0
            cmd.xUpldProt = drv.xUpldProt;  // protocolo
            cmd.tyUpReg = 0;                // 96 registros

            // iniUpload
            ViewBag.Data = string.Format("{0:d}", cmd.ultimoUpload);
            ViewBag.Hora = string.Format("{0:HH:mm}", cmd.ultimoUpload);

            // copia status
            ViewBag.SolicitaProg = solicitaProg;
            ViewBag.SolicitaDrv = solicitaDrv;

            // retorna configuração
            return (cmd);
        }

        // prepara listas
        private void GateX_EnvioDados_PreparaListas(int IDGateway, int tyUp)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // le tipo envio dados
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoEnvioDados = listatiposMetodos.ListarTodos("TipoGateX_EnvioDados", false, 0);
            ViewBag.listatipoEnvioDados = listatipoEnvioDados;

            // le tipo envio dados - registros
            List<ListaTiposDominio> listatipoEnvioDadosReg = listatiposMetodos.ListarTodos("TipoGateX_EnvioDadosReg", false, 0);
            ViewBag.listatipoEnvioDadosReg = listatipoEnvioDadosReg;

            // Obter Medicoes
            GateX_EnvioDados_ObterMedicoes(IDGateway, tyUp);

            return;
        }

        // POST: Comando Envio Dados - Enviar
        [HttpPost]
        public ActionResult GateX_EnvioDados_Enviar(GateX_EnvioDados_Dominio cmd)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(cmd.IDGateway);

            // variavel de retorno
            bool enviaProg = false;

            // verifica se FTP
            if (cmd.xUpldProt == 0)
            {
                cmd.tyUp = 1;
            }

            // verifica se MQTT e se deve enviar todos os registros
            if (cmd.xUpldProt == 1 && cmd.tyUpReg == 1)
            {
                cmd.iniUpload_DataTexto = "01/01/2020 00:00:00";
            }

            // data hora de envio
            DateTime datahora = new DateTime(2000, 1, 1, 0, 0, 0);

            if (DateTime.TryParse(cmd.iniUpload_DataTexto, out datahora))
            {
                // data hora inicio upload
                cmd.iniUpload = datahora;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // copia envio
                    smartCom.gateX.EnvioDados = cmd;

                    // envia comando Envio Dados
                    enviaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.COMANDO_UPLOAD, SMCOM_TIPO_SOL.ENVIA);
                }

                // verifica retorno
                if (enviaProg)
                {
                    // caso OK insiro evento
                    UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, cmd.IDGateway);
                }
                else
                {
                    // erro
                    returnedData = new
                    {
                        status = "ERRO",
                        erro = "Falha no envio da configuração!"
                    };

                }
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Data e Hora incorreta!"
                };
            }

            // retorna status
            return Json(returnedData);
        }

        //
        // MEDICOES
        //

        // GET: Obter Medicoes
        public JsonResult GateX_EnvioDados_ObterMedicoes(int IDGateway, int tyUp)
        {
            // medições
            List<ListaTiposDominio> listaMedicoes = new List<ListaTiposDominio>();

            // tipo da medição desejada
            switch (tyUp)
            {
                case 0x10:     // supervisão equipamento
                case 0x20:     // eventos
                    break;

                case 0x30:     // energia

                    // medições energia
                    GateX_MedEner_Lista_Metodos medEnerMetodos = new GateX_MedEner_Lista_Metodos();
                    List<GateX_MedEner_Lista_Dominio> medEner_Lista = medEnerMetodos.ListarPorIDGateway(IDGateway);

                    // preenche lista de medições
                    for (int i = 0; i < SGATEX.NUM_MED_EN; i++)
                    {
                        // default
                        ListaTiposDominio tipo = new ListaTiposDominio();
                        tipo.ID = i;
                        tipo.Descricao = string.Format("[{0:00}] Medição de Energia Elétrica {1}", i, i);

                        // copia
                        if (medEner_Lista != null)
                        {
                            // verifica se tem na lista
                            GateX_MedEner_Lista_Dominio med = medEner_Lista.First(item => item.NumMedicaoGateway == i);

                            if (med != null)
                            {
                                tipo.Descricao = string.Format("[{0:00}] {1}", i, med.Descricao);
                            }
                        }

                        // insere na lista
                        listaMedicoes.Add(tipo);
                    }

                    break;

                case 0x31:     // utilidades

                    // medições utilidades
                    GateX_MedUtil_Lista_Metodos medUtilMetodos = new GateX_MedUtil_Lista_Metodos();
                    List<GateX_MedUtil_Lista_Dominio> medUtil_Lista = medUtilMetodos.ListarPorIDGateway(IDGateway);

                    // preenche lista de medições
                    for (int i = 0; i < SGATEX.NUM_MED_UT; i++)
                    {
                        // default
                        ListaTiposDominio tipo = new ListaTiposDominio();
                        tipo.ID = i;
                        tipo.Descricao = string.Format("[{0:00}] Medição de Utilidades {1}", i, i);

                        // copia
                        if (medUtil_Lista != null)
                        {
                            // verifica se tem na lista
                            GateX_MedUtil_Lista_Dominio med = medUtil_Lista.First(item => item.NumMedicaoGateway == i);

                            if (med != null)
                            {
                                tipo.Descricao = string.Format("[{0:00}] {1}", i, med.Descricao);
                            }
                        }

                        // insere na lista
                        listaMedicoes.Add(tipo);
                    }

                    break;

                case 0x32:     // analogica

                    // medições analógicas
                    GateX_MedAna_Lista_Metodos medAnaMetodos = new GateX_MedAna_Lista_Metodos();
                    List<GateX_MedAna_Lista_Dominio> medAna_Lista = medAnaMetodos.ListarPorIDGateway(IDGateway);

                    // preenche lista de medições
                    for (int i = 0; i < SGATEX.NUM_MED_AN; i++)
                    {
                        // default
                        ListaTiposDominio tipo = new ListaTiposDominio();
                        tipo.ID = i;
                        tipo.Descricao = string.Format("[{0:00}] Medição Analógica {1}", i, i);

                        // copia
                        if (medAna_Lista != null)
                        {
                            // verifica se tem na lista
                            GateX_MedAna_Lista_Dominio med = medAna_Lista.First(item => item.NumMedicaoGateway == i);

                            if (med != null)
                            {
                                tipo.Descricao = string.Format("[{0:00}] {1}", i, med.Descricao);
                            }
                        }

                        // insere na lista
                        listaMedicoes.Add(tipo);
                    }

                    break;

                case 0x33:     // ciclometro 

                    // medições ciclômetro
                    GateX_MedCiclo_Lista_Metodos medCicloMetodos = new GateX_MedCiclo_Lista_Metodos();
                    List<GateX_MedCiclo_Lista_Dominio> medCiclo_Lista = medCicloMetodos.ListarPorIDGateway(IDGateway);

                    // preenche lista de medições
                    for (int i = 0; i < SGATEX.NUM_MED_CY; i++)
                    {
                        // default
                        ListaTiposDominio tipo = new ListaTiposDominio();
                        tipo.ID = i;
                        tipo.Descricao = string.Format("[{0:00}] Medição Ciclômetro {1}", i, i);

                        // copia
                        if (medCiclo_Lista != null)
                        {
                            // verifica se tem na lista
                            GateX_MedCiclo_Lista_Dominio med = medCiclo_Lista.First(item => item.NumMedicaoGateway == i);

                            if (med != null)
                            {
                                tipo.Descricao = string.Format("[{0:00}] {1}", i, med.Descricao);
                            }
                        }

                        // insere na lista
                        listaMedicoes.Add(tipo);
                    }

                    break;
            }

            // lista de medições
            ViewBag.listaMedicoes = listaMedicoes;

            // retorna o valor em JSON
            return Json(listaMedicoes, JsonRequestBehavior.AllowGet);
        }
    }
}