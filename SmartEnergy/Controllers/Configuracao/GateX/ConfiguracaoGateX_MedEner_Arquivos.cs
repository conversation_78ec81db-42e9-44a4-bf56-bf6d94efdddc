﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Medições de Energia XLS Download
        [HttpGet]
        public virtual ActionResult GateX_MedEner_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Medições de Energia XLS
        public ActionResult GateX_MedEner_XLS(int IDGateway)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_GateX_MedEner(IDGateway);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_MedEner_Gateway_{0:000000}_{1:yyyyMMddHHmm}.xls", IDGateway, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // Medições de Energia Planilha
        private HSSFWorkbook XLS_GateX_MedEner(int IDGateway)
        {
            // cria excel
            HSSFWorkbook workbook = new HSSFWorkbook();

            // Medições
            XLS_GateX_MedEner_Medicoes(IDGateway, workbook);

            // Medição Principal
            XLS_GateX_MedEner_MedPrinc(IDGateway, workbook);

            // Vigências de Demanda
            XLS_GateX_MedEner_VigDem(IDGateway, workbook);

            // Demandas Suplementares
            XLS_GateX_MedEner_DemSup(IDGateway, workbook);

            // Resumo
            XLS_GateX_MedEner_Resumo(IDGateway, workbook);

            // retorna planilha
            return workbook;
        }


        // Medições de Energia Planilha - Medições
        private void XLS_GateX_MedEner_Medicoes(int IDGateway, HSSFWorkbook workbook)
        {
            // cria estilos
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);


            //
            // MEDIÇÕES DE ENERGIA
            //

            //  le lista de medições da gateway
            GateX_MedEner_Metodos prgMetodos = new GateX_MedEner_Metodos();
            List<GateX_MedEner_Dominio> medEner = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // Versão do firmware (número)
            int versao = GateX_Versao_Int(IDGateway);

            // tipos medidores de energia
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoMedidoresEner = new List<ListaTiposDominio>();
            if (versao <= 429)
            {
                listatipoMedidoresEner = listatiposMetodos.ListarTodos("TipoGateX_MedidoresEner_429", false, 0);
            }
            else
            {
                listatipoMedidoresEner = listatiposMetodos.ListarTodos("TipoGateX_MedidoresEner_430", false, 0);
            }

            // le tipos rede IO
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);

            // le tipos reativo
            List<ListaTiposDominio> listatipoReativo = listatiposMetodos.ListarTodos("TipoGateX_Reativo", false, 0);

            // le lista saidas digitais
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitas = sdMetodos.ListarPorIDGateway(IDGateway);
            List<GateX_SD_Lista_Dominio> listaSaidas = new List<GateX_SD_Lista_Dominio>();

            GateX_SD_Lista_Dominio nenhum = new GateX_SD_Lista_Dominio();
            nenhum.NumSaidaGateway = 255;
            nenhum.Programado = false;
            nenhum.Descricao = "Nenhum";
            listaSaidas.Add(nenhum);

            // percorre saidas
            foreach (GateX_SD_Lista_Dominio saida in saidasDigitas)
            {
                saida.Descricao = string.Format("[{0:00}] {1}{2}", saida.NumSaidaGateway, saida.Descricao, (saida.Programado ? "" : " [Disponível]"));
                listaSaidas.Add(saida);
            }


            // cria planilha
            var sheet = workbook.CreateSheet("Medições de Energia");

            // cabecalho
            string[] cabecalho = { "Medição Interna", "Medições de Energia", "Medidor", "Média Móvel (min.)", "Falta de Pulso (seg.)", "Reiniciar Média Móvel", "Registrar Histórico", "Constante", "Habilitar Reativo", "Rede", "Remota", "Endereço", "Op [0]", "Op [1]", "Op [2]", "Op [3]", "Op [4]", "Op [5]", "Op [6]", "Op [7]", "RTP", "RTC", "Fórmula", "Não medir se Saída Digital ativa", "Contrato Ponta (kW)", "Contrato Fora de Ponta (kW)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            IRow row;

            if (medEner != null)
            {
                // percorre medições
                foreach (GateX_MedEner_Dominio med in medEner)
                {
                    string desprogramado = "---";
                    string Descricao = desprogramado;
                    string TipoMedidor = desprogramado;

                    string TamanhoAnel = desprogramado;
                    string TempoPulsos = desprogramado;
                    string ReiniMM = desprogramado;
                    string RecebeHist = desprogramado;

                    string Constante = desprogramado;
                    string HabReativo = desprogramado;

                    string RedeIO = desprogramado;
                    string Remota = desprogramado;
                    string Endereco = desprogramado;

                    string RTP = desprogramado;
                    string RTC = desprogramado;

                    string Op0 = desprogramado;
                    string Op1 = desprogramado;
                    string Op2 = desprogramado;
                    string Op3 = desprogramado;
                    string Op4 = desprogramado;
                    string Op5 = desprogramado;
                    string Op6 = desprogramado;
                    string Op7 = desprogramado;

                    string Formula = desprogramado;

                    string SdNaoAcuMed = desprogramado;

                    string Dem_Ponta = desprogramado;
                    string DemFPonta = desprogramado;

                    if (med.Programado)
                    {
                        // Descrição
                        Descricao = med.Descricao;

                        // Média Móvel
                        TamanhoAnel = string.Format("{0}", (med.TamanhoAnel * 10) / 60);

                        // Falta de Pulso
                        TempoPulsos = string.Format("{0}", med.TempoPulsos * 10);

                        // Reiniciar Média Móvel
                        ReiniMM = (med.ReiniMM) ? "Sim" : "Não";

                        // Registrar Histórico
                        RecebeHist = (med.RecebeHist) ? "Sim" : "Não";

                        // Constante
                        Constante = string.Format("{0}", Math.Round(med.Constante, 5));

                        // Habilitar Reativo
                        ListaTiposDominio tipo = listatipoReativo.Find(item => item.ID == med.HabReativo);
                        HabReativo = (tipo != null) ? tipo.Descricao : desprogramado;

                        // Não medir se Saída Digital ativa
                        GateX_SD_Lista_Dominio saida = listaSaidas.Find(item => item.NumSaidaGateway == med.SdNaoAcuMed);
                        SdNaoAcuMed = (saida != null) ? saida.Descricao : desprogramado;

                        // Tipo Medidor
                        tipo = listatipoMedidoresEner.Find(item => item.ID == med.TipoMedidor);
                        TipoMedidor = (tipo != null) ? tipo.Descricao : desprogramado;

                        switch (med.TipoMedidor)
                        {
                            case SGATEX_TIPO_MEDIDOR.DESPROGRAMADO: // desprogramado

                                break;

                            case SGATEX_TIPO_MEDIDOR.FORMULA:       // fórmula

                                Formula = Funcoes_GateX.Formula2String(med.Formula);

                                break;

                            case SGATEX_TIPO_MEDIDOR.CODI:          // CODI
                            case SGATEX_TIPO_MEDIDOR.CODIQ:         // CODI Q

                                // Falta de Pulso
                                TempoPulsos = "Ativo";

                                // opções
                                Op0 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 0) ? "Sim" : "Não";
                                Op1 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 1) ? "Sim" : "Não";
                                Op2 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 2) ? "Sim" : "Não";
                                Op3 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 3) ? "Sim" : "Não";
                                Op4 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 4) ? "Sim" : "Não";
                                Op5 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 5) ? "Sim" : "Não";
                                Op6 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 6) ? "Sim" : "Não";
                                Op7 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 7) ? "Sim" : "Não";

                                break;

                            case SGATEX_TIPO_MEDIDOR.DIN_VIRTUAL:   // DIN virtual

                                // Falta de Pulso
                                TempoPulsos = "Ativo";

                                // opções
                                Op0 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 0) ? "Sim" : "Não";
                                Op1 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 1) ? "Sim" : "Não";
                                Op2 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 2) ? "Sim" : "Não";
                                Op3 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 3) ? "Sim" : "Não";
                                Op4 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 4) ? "Sim" : "Não";
                                Op5 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 5) ? "Sim" : "Não";
                                Op6 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 6) ? "Sim" : "Não";
                                Op7 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 7) ? "Sim" : "Não";

                                // Endereço
                                Endereco = med.Endereco.ToString();

                                break;

                            case SGATEX_TIPO_MEDIDOR.DI:            // DI

                                break;

                            case SGATEX_TIPO_MEDIDOR.REM_GENERICA:  // remota genérica
                            default:                                // medidores eletrônicos

                                // RedeIO / Remota / Endereço
                                tipo = listatipoRedeIO.Find(item => item.ID == med.RedeIO);
                                RedeIO = (tipo != null) ? tipo.Descricao : desprogramado;
                                Remota = med.Remota.ToString();
                                Endereco = med.Endereco.ToString();

                                // opções
                                Op0 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 0) ? "Sim" : "Não";
                                Op1 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 1) ? "Sim" : "Não";
                                Op2 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 2) ? "Sim" : "Não";
                                Op3 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 3) ? "Sim" : "Não";
                                Op4 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 4) ? "Sim" : "Não";
                                Op5 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 5) ? "Sim" : "Não";
                                Op6 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 6) ? "Sim" : "Não";
                                Op7 = Funcoes_Bit.IsBitSet_byte((byte)med.opGrEl, 7) ? "Sim" : "Não";

                                // RTP
                                RTP = string.Format("{0}", Math.Round(med.RTP, 5));

                                // RTC
                                RTC = string.Format("{0}", Math.Round(med.RTC, 5));

                                break;
                        }

                        // contrato ponta
                        Dem_Ponta = string.Format("{0}", Math.Round(med.Dem_Ponta, 5));

                        // contrato fora de ponta
                        DemFPonta = string.Format("{0}", Math.Round(med.DemFPonta, 5));
                    }

                    // adiciona linha
                    row = sheet.CreateRow(rowIndex++);

                    // Medição
                    numeroCelulaXLS(row, 0, med.NumMedicaoGateway, _intCellStyle);
                    sheet.SetColumnWidth(0, 6000);

                    // Descrição
                    textoCelulaXLS(row, 1, Descricao);
                    sheet.SetColumnWidth(1, 7000);

                    // Tipo Medidor
                    textoCelulaXLS(row, 2, TipoMedidor);
                    sheet.SetColumnWidth(2, 6000);

                    // Média Móvel
                    textoCelulaXLS(row, 3, TamanhoAnel);
                    sheet.SetColumnWidth(3, 6000);

                    // Falta de Pulso
                    textoCelulaXLS(row, 4, TempoPulsos);
                    sheet.SetColumnWidth(4, 6000);

                    // Reiniciar Média Móvel
                    textoCelulaXLS(row, 5, ReiniMM);
                    sheet.SetColumnWidth(5, 6000);

                    // Registrar Histórico
                    textoCelulaXLS(row, 6, RecebeHist);
                    sheet.SetColumnWidth(6, 6000);

                    // Constante
                    textoCelulaXLS(row, 7, Constante);
                    sheet.SetColumnWidth(7, 6000);

                    // Habilitar Reativo
                    textoCelulaXLS(row, 8, HabReativo);
                    sheet.SetColumnWidth(8, 6000);

                    // Rede IO
                    textoCelulaXLS(row, 9, RedeIO);
                    sheet.SetColumnWidth(9, 4000);

                    // Remota
                    textoCelulaXLS(row, 10, Remota);
                    sheet.SetColumnWidth(10, 4000);

                    // Endereco
                    textoCelulaXLS(row, 11, Endereco);
                    sheet.SetColumnWidth(11, 4000);

                    // opções
                    textoCelulaXLS(row, 12, Op0);
                    sheet.SetColumnWidth(12, 2000);

                    textoCelulaXLS(row, 13, Op1);
                    sheet.SetColumnWidth(13, 2000);

                    textoCelulaXLS(row, 14, Op2);
                    sheet.SetColumnWidth(14, 2000);

                    textoCelulaXLS(row, 15, Op3);
                    sheet.SetColumnWidth(15, 2000);

                    textoCelulaXLS(row, 16, Op4);
                    sheet.SetColumnWidth(16, 2000);

                    textoCelulaXLS(row, 17, Op5);
                    sheet.SetColumnWidth(17, 2000);

                    textoCelulaXLS(row, 18, Op6);
                    sheet.SetColumnWidth(18, 2000);

                    textoCelulaXLS(row, 19, Op7);
                    sheet.SetColumnWidth(19, 2000);

                    // RTP
                    textoCelulaXLS(row, 20, RTP);
                    sheet.SetColumnWidth(20, 4000);

                    // RTC
                    textoCelulaXLS(row, 21, RTP);
                    sheet.SetColumnWidth(21, 4000);

                    // Fórmula
                    textoCelulaXLS(row, 22, Formula);
                    sheet.SetColumnWidth(22, 4000);

                    // Não medir se Saída Digital ativa
                    textoCelulaXLS(row, 23, SdNaoAcuMed);
                    sheet.SetColumnWidth(23, 10000);

                    // contrato ponta
                    textoCelulaXLS(row, 24, Dem_Ponta);
                    sheet.SetColumnWidth(24, 8000);

                    // contrato fora de ponta
                    textoCelulaXLS(row, 25, DemFPonta);
                    sheet.SetColumnWidth(25, 8000);
                }
            }

            return;
        }

        // Medições de Energia Planilha - Medição Principal
        private void XLS_GateX_MedEner_MedPrinc(int IDGateway, HSSFWorkbook workbook)
        {
            // cria estilos
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);


            //
            // MEDIÇÕES PRINCIPAL
            //

            //  le medição principal
            GateX_MedEner_MedPrinc_Metodos medPrincMetodos = new GateX_MedEner_MedPrinc_Metodos();
            GateX_MedEner_MedPrinc_Dominio medPrinc = medPrincMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // SinalCodi
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listaSinalCodi = listatiposMetodos.ListarTodos("TipoGateX_SinalCodi", false, 0);

            // PeriodoDefault
            List<ListaTiposDominio> listaPeriodoDefault = listatiposMetodos.ListarTodos("TipoGateX_PeriodoDefault", false, 0);


            if (medPrinc != null)
            {
                // cria planilha
                var sheet = workbook.CreateSheet("Parâmetros de Faturamento");

                // inicia linha
                int rowIndex = 0;
                string desprogramado = "---";

                // Sinal Horário
                IRow row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Sinal Horário", _negritoCellStyle);
                ListaTiposDominio tipo = listaSinalCodi.Find(item => item.ID == medPrinc.SinalCodi);
                textoCelulaXLS(row, 1, (tipo != null) ? tipo.Descricao : desprogramado);

                // Na falta de Sinal Horário
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Na falta de Sinal Horário", _negritoCellStyle);
                tipo = listaPeriodoDefault.Find(item => item.ID == medPrinc.PeriodoDefault);
                textoCelulaXLS(row, 1, (tipo != null) ? tipo.Descricao : desprogramado);

                // pula linha
                row = sheet.CreateRow(rowIndex++);

                // Horário de Ponta
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Horário de Ponta", _negritoCellStyle);

                // inicio
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Início", _negritoCellStyle);
                textoCelulaXLS(row, 1, string.Format("{0:HH:mm}", medPrinc.Ponta_HoraInicio));

                // fim
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Fim", _negritoCellStyle);
                textoCelulaXLS(row, 1, string.Format("{0:HH:mm}", medPrinc.Ponta_HoraFim));

                // dias da semana
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Dias da Semana", _negritoCellStyle);

                string dias_semana = "";
                dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.Ponta_UsaDia, 0) ? "Dom " : "";
                dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.Ponta_UsaDia, 1) ? "Seg " : "";
                dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.Ponta_UsaDia, 2) ? "Ter " : "";
                dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.Ponta_UsaDia, 3) ? "Qua " : "";
                dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.Ponta_UsaDia, 4) ? "Qui " : "";
                dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.Ponta_UsaDia, 5) ? "Sex " : "";
                dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.Ponta_UsaDia, 6) ? "Sáb " : "";
                dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.Ponta_UsaDia, 7) ? "Fer " : "";
                textoCelulaXLS(row, 1, dias_semana);

                // pula linha
                row = sheet.CreateRow(rowIndex++);

                // Posto Capacitivo
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Posto Capacitivo", _negritoCellStyle);

                // inicio
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Início", _negritoCellStyle);
                textoCelulaXLS(row, 1, string.Format("{0:HH:mm}", medPrinc.PostoCap_HoraInicio));

                // fim
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Fim", _negritoCellStyle);
                textoCelulaXLS(row, 1, string.Format("{0:HH:mm}", medPrinc.PostoCap_HoraFim));

                // dias da semana
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Dias da Semana", _negritoCellStyle);

                dias_semana = "";
                dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.PostoCap_UsaDia, 0) ? "Dom " : "";
                dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.PostoCap_UsaDia, 1) ? "Seg " : "";
                dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.PostoCap_UsaDia, 2) ? "Ter " : "";
                dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.PostoCap_UsaDia, 3) ? "Qua " : "";
                dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.PostoCap_UsaDia, 4) ? "Qui " : "";
                dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.PostoCap_UsaDia, 5) ? "Sex " : "";
                dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.PostoCap_UsaDia, 6) ? "Sáb " : "";
                dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)medPrinc.PostoCap_UsaDia, 7) ? "Fer " : "";
                textoCelulaXLS(row, 1, dias_semana);

                // largura de cada coluna
                sheet.SetColumnWidth(0, 7000);
                sheet.SetColumnWidth(1, 10000);
            }

            return;
        }

        // Medições de Energia Planilha - Vigências de Demanda
        private void XLS_GateX_MedEner_VigDem(int IDGateway, HSSFWorkbook workbook)
        {
            // cria estilos
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);


            //
            // VIGÊNCIAS DE DEMANDA
            //

            //  le vigências de demanda
            GateX_MedEner_VigDem_Metodos medVigDemMetodos = new GateX_MedEner_VigDem_Metodos();
            List<GateX_MedEner_VigDem_Dominio> medVigDem = medVigDemMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // lista medições em 'editando'
            GateX_MedEner_Metodos medMetodos = new GateX_MedEner_Metodos();
            List<GateX_MedEner_Dominio> listaMedEner = medMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);


            // cria planilha
            var sheet = workbook.CreateSheet("Vigências de Demanda");

            // cabecalho
            string[] cabecalho = { "Número", "Medições de Energia", "Data Início", "Demanda Ponta", "Demanda Fora de Ponta" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            IRow row;

            if (medVigDem != null)
            {
                // percorre vigências
                foreach (GateX_MedEner_VigDem_Dominio vig in medVigDem)
                {
                    string desprogramado = "---";
                    string Medicao = desprogramado;
                    string DataVigencia = desprogramado;
                    string DemandaP = desprogramado;
                    string DemandaF = desprogramado;

                    if (vig.Programado)
                    {
                        // Medição
                        GateX_MedEner_Dominio med = listaMedEner.Find(item => item.NumMedicaoGateway == vig.Medicao);
                        Medicao = (med != null) ? med.DescricaoNum : desprogramado;

                        // Data Vigência
                        DataVigencia = string.Format("{0:d}", vig.DataVigencia);

                        // Demanda Ponta
                        DemandaP = string.Format("{0}", Math.Round(vig.DemandaP, 5));

                        // Demanda Fora de Ponta
                        DemandaF = string.Format("{0}", Math.Round(vig.DemandaF, 5));
                    }

                    // adiciona linha
                    row = sheet.CreateRow(rowIndex++);

                    // Número
                    numeroCelulaXLS(row, 0, vig.numVigDem, _intCellStyle);
                    sheet.SetColumnWidth(0, 6000);

                    // Medição
                    textoCelulaXLS(row, 1, Medicao);
                    sheet.SetColumnWidth(1, 10000);

                    // Data Vigência
                    textoCelulaXLS(row, 2, DataVigencia);
                    sheet.SetColumnWidth(2, 6000);

                    // Demanda Ponta
                    textoCelulaXLS(row, 3, DemandaP);
                    sheet.SetColumnWidth(3, 6000);

                    // Demanda Fora de Ponta
                    textoCelulaXLS(row, 4, DemandaF);
                    sheet.SetColumnWidth(4, 6000);
                }
            }

            return;
        }

        // Medições de Energia Planilha - Demandas Suplementares
        private void XLS_GateX_MedEner_DemSup(int IDGateway, HSSFWorkbook workbook)
        {
            // cria estilos
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);


            //
            // DEMANDAS SUPLEMENTARES
            //

            //  le demandas suplementares
            GateX_MedEner_DemSup_Metodos medDemSupMetodos = new GateX_MedEner_DemSup_Metodos();
            List<GateX_MedEner_DemSup_Dominio> medDemSup = medDemSupMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // lista medições em 'editando'
            GateX_MedEner_Metodos medMetodos = new GateX_MedEner_Metodos();
            List<GateX_MedEner_Dominio> listaMedEner = medMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // tipo suplementar
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listaTipoSuplementar = listatiposMetodos.ListarTodos("TipoGateX_DemSup", false, 0);

            // período
            List<ListaTiposDominio> listaPeriodo = listatiposMetodos.ListarTodos("TipoGateX_Periodo", false, 0);


            // cria planilha
            var sheet = workbook.CreateSheet("Demandas Suplementares");

            // cabecalho
            string[] cabecalho = { "Número", "Medições de Energia", "Tipo Demanda Suplementar", "Descrição", "Período", "Dia da Semana", "Data Início", "Data Fim", "Hora Início", "Hora Fim", "Demanda Suplementar (kW)", "Fator de Carga Histórico (%)", "Tarifa de Demanda (R$/kW)", "Tarifa de Consumo (R$/MWh)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            IRow row;

            if (medDemSup != null)
            {
                // percorre vigências
                foreach (GateX_MedEner_DemSup_Dominio dem in medDemSup)
                {
                    string desprogramado = "---";
                    string Medicao = desprogramado;
                    string TipoSuplementar = desprogramado;
                    string Descricao = desprogramado;
                    string PeriodoValido = desprogramado;
                    string dias_semana = desprogramado;
                    string FatorCargaHistorico = desprogramado;
                    string DataInicio = desprogramado;
                    string DataFim = desprogramado;
                    string HoraInicio = desprogramado;
                    string HoraFim = desprogramado;
                    string Demanda = desprogramado;
                    string TarifaDemanda = desprogramado;
                    string TarifaConsumo = desprogramado;

                    if (dem.Programado)
                    {
                        // Medição
                        GateX_MedEner_Dominio med = listaMedEner.Find(item => item.NumMedicaoGateway == dem.NumeroMedEnergia);
                        Medicao = (med != null) ? med.DescricaoNum : desprogramado;

                        // Tipo Suplementar
                        ListaTiposDominio tipo = listaTipoSuplementar.Find(item => item.ID == dem.TipoSuplementar);
                        TipoSuplementar = (tipo != null) ? tipo.Descricao : desprogramado;

                        // Descrição
                        Descricao = dem.Descricao;

                        // Período Válido
                        tipo = listaPeriodo.Find(item => item.ID == dem.PeriodoValido);
                        PeriodoValido = (tipo != null) ? tipo.Descricao : desprogramado;

                        // Dias da Semana
                        dias_semana = "";
                        dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)dem.UsaDia, 0) ? "Dom " : "";
                        dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)dem.UsaDia, 1) ? "Seg " : "";
                        dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)dem.UsaDia, 2) ? "Ter " : "";
                        dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)dem.UsaDia, 3) ? "Qua " : "";
                        dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)dem.UsaDia, 4) ? "Qui " : "";
                        dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)dem.UsaDia, 5) ? "Sex " : "";
                        dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)dem.UsaDia, 6) ? "Sáb " : "";
                        dias_semana += Funcoes_GateX.IsBitSet_UInt16((UInt16)dem.UsaDia, 7) ? "Fer " : "";

                        // Data Início e Fim
                        DataInicio = string.Format("{0:d}", dem.DataInicio);
                        DataFim = string.Format("{0:d}", dem.DataFim);

                        // Hora Início e Fim
                        HoraInicio = string.Format("{0:HH:mm}", dem.HoraInicio);
                        HoraFim = string.Format("{0:HH:mm}", dem.HoraFim);

                        // Demanda 
                        Demanda = string.Format("{0}", Math.Round(dem.Demanda, 5));

                        // Fator de Carga Histórico
                        FatorCargaHistorico = string.Format("{0}", dem.FatorCargaHistorico);

                        // Tarifas
                        TarifaDemanda = string.Format("{0}", Math.Round(dem.TarifaDemanda, 5));
                        TarifaConsumo = string.Format("{0}", Math.Round(dem.TarifaConsumo, 5));
                    }

                    // adiciona linha
                    row = sheet.CreateRow(rowIndex++);

                    // Número
                    numeroCelulaXLS(row, 0, dem.numDemSup, _intCellStyle);
                    sheet.SetColumnWidth(0, 6000);

                    // Medição
                    textoCelulaXLS(row, 1, Medicao);
                    sheet.SetColumnWidth(1, 10000);

                    // Tipo Suplementar
                    textoCelulaXLS(row, 2, TipoSuplementar);
                    sheet.SetColumnWidth(2, 10000);

                    // Descrição
                    textoCelulaXLS(row, 3, Descricao);
                    sheet.SetColumnWidth(3, 6000);

                    // Período Válido
                    textoCelulaXLS(row, 4, PeriodoValido);
                    sheet.SetColumnWidth(4, 6000);

                    // Dias da Semana
                    textoCelulaXLS(row, 5, dias_semana);
                    sheet.SetColumnWidth(5, 10000);

                    // Data Início
                    textoCelulaXLS(row, 6, DataInicio);
                    sheet.SetColumnWidth(6, 5000);

                    // Data Fim
                    textoCelulaXLS(row, 7, DataFim);
                    sheet.SetColumnWidth(7, 5000);

                    // Hora Início
                    textoCelulaXLS(row, 8, HoraInicio);
                    sheet.SetColumnWidth(8, 5000);

                    // Hora Fim
                    textoCelulaXLS(row, 9, HoraFim);
                    sheet.SetColumnWidth(9, 5000);

                    // Demanda 
                    textoCelulaXLS(row, 10, Demanda);
                    sheet.SetColumnWidth(10, 7000);

                    // Fator de Carga Histórico
                    textoCelulaXLS(row, 11, FatorCargaHistorico);
                    sheet.SetColumnWidth(11, 7000);

                    // Tarifa Demanda
                    textoCelulaXLS(row, 12, TarifaDemanda);
                    sheet.SetColumnWidth(12, 7000);

                    // Tarifa Consumo
                    textoCelulaXLS(row, 13, TarifaConsumo);
                    sheet.SetColumnWidth(13, 7000);
                }
            }

            return;
        }
        
        // Medições de Energia Planilha - Resumo
        private void XLS_GateX_MedEner_Resumo(int IDGateway, HSSFWorkbook workbook)
        {
            // cria estilos
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);


            //
            // RESUMO
            //

            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoMedEner;

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // cria planilha
            var sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            int rowIndex = cabecalhoResumoXLS_GateX(workbook, sheet, _negritoCellStyle);

            // largura de cada coluna
            sheet.SetColumnWidth(0, 7000);
            sheet.SetColumnWidth(1, 8000);

            return;
        }

        // GET: Medições de Energia PDF
        public ActionResult GateX_MedEner_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista Medições de Energia
            GateX_MedEner_Metodos prgMetodos = new GateX_MedEner_Metodos();
            List<GateX_MedEner_Dominio> medEner = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.medEner = medEner;

            // Versão do firmware (número)
            int versao = GateX_Versao_Int(IDGateway);

            // tipos medidores de energia
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoMedidoresEner = new List<ListaTiposDominio>();
            if (versao <= 429)
            {
                listatipoMedidoresEner = listatiposMetodos.ListarTodos("TipoGateX_MedidoresEner_429", false, 0);
            }
            else
            {
                listatipoMedidoresEner = listatiposMetodos.ListarTodos("TipoGateX_MedidoresEner_430", false, 0);
            }
            ViewBag.listatipoMedidoresEner = listatipoMedidoresEner;

            // le tipos rede IO
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DI);      // remove DI
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DO);      // remove DO
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.SEG);     // remove seg
            ViewBag.listatipoRedeIO = listatipoRedeIO;


            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_MedEner_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_MedEner_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        // GET: Medições de Energia EMAIL
        public async Task<ActionResult> GateX_MedEner_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);


            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoMedEner;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista Medições de Energia
            GateX_MedEner_Metodos prgMetodos = new GateX_MedEner_Metodos();
            List<GateX_MedEner_Dominio> medEner = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.medEner = medEner;

            // Versão do firmware (número)
            int versao = GateX_Versao_Int(IDGateway);

            // tipos medidores de energia
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoMedidoresEner = new List<ListaTiposDominio>();
            if (versao <= 429)
            {
                listatipoMedidoresEner = listatiposMetodos.ListarTodos("TipoGateX_MedidoresEner_429", false, 0);
            }
            else
            {
                listatipoMedidoresEner = listatiposMetodos.ListarTodos("TipoGateX_MedidoresEner_430", false, 0);
            }
            ViewBag.listatipoMedidoresEner = listatipoMedidoresEner;

            // le tipos rede IO
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DI);      // remove DI
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DO);      // remove DO
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.SEG);     // remove seg
            ViewBag.listatipoRedeIO = listatipoRedeIO;


            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_MedEner_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_MedEner_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "GateX_MedEnerEmail";
            var message = await EMailTemplateAsync(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.NomeGateway", ViewBag.NomeGateway);
            message = message.Replace("ViewBag.DescrConfig", ViewBag.DescrConfig);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Medições de Energia Print
        public ActionResult GateX_MedEner_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista Medições de Energia
            GateX_MedEner_Metodos prgMetodos = new GateX_MedEner_Metodos();
            List<GateX_MedEner_Dominio> medEner = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.medEner = medEner;

            // Versão do firmware (número)
            int versao = GateX_Versao_Int(IDGateway);

            // tipos medidores de energia
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoMedidoresEner = new List<ListaTiposDominio>();
            if (versao <= 429)
            {
                listatipoMedidoresEner = listatiposMetodos.ListarTodos("TipoGateX_MedidoresEner_429", false, 0);
            }
            else
            {
                listatipoMedidoresEner = listatiposMetodos.ListarTodos("TipoGateX_MedidoresEner_430", false, 0);
            }
            ViewBag.listatipoMedidoresEner = listatipoMedidoresEner;

            // le tipos rede IO
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DI);      // remove DI
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DO);      // remove DO
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.SEG);     // remove seg
            ViewBag.listatipoRedeIO = listatipoRedeIO;


            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // imprime
            return View();
        }
    }
}