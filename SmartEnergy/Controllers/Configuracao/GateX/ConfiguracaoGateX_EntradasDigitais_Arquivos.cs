﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Entradas Digitais XLS Download
        [HttpGet]
        public virtual ActionResult GateX_EntradasDigitais_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Entradas Digitais XLS
        public ActionResult GateX_EntradasDigitais_XLS(int IDGateway)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_GateX_EntradasDigitais(IDGateway);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_EntradasDigitais_Gateway_{0:000000}_{1:yyyyMMddHHmm}.xls", IDGateway, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // Entradas Digitais Planilha
        private HSSFWorkbook XLS_GateX_EntradasDigitais(int IDGateway)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // ENTRADAS
            //

            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoEntradasDigitais;

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            //  le lista de entradas da gateway
            GateX_ED_Metodos edMetodos = new GateX_ED_Metodos();
            List<GateX_ED_Dominio> entradas = edMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // le tipos rede IO
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DO);      // remove DO
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.SEG);     // remove seg

            // lista numero remotas
            int[] listaRemotas = new int[32];

            for (int j = 0; j < listaRemotas.Length; j++)
            {
                listaRemotas[j] = j;
            }

            // cria planilha
            var sheet = workbook.CreateSheet("Entradas Digitais");

            // cabecalho
            string[] cabecalho = { "Entrada Digital Interna", "Entradas Digitais", "Rede", "Remota", "Endereço", "Texto Ativo", "Texto Inativo", "Ativo em UM", "Reconhecer Alarmes" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            IRow row;

            if (entradas != null)
            {
                // percorre entradas
                foreach (GateX_ED_Dominio entrada in entradas)
                {
                    string desprogramado = "---";
                    string nomeEntrada = desprogramado;
                    string rede = desprogramado;
                    string EEAtivo = desprogramado;
                    string ReconhecerAlarme = desprogramado;

                    if (entrada.Programado)
                    {
                        // descricao
                        nomeEntrada = entrada.Descricao;

                        // rede IO
                        if (listatipoRedeIO != null) rede = listatipoRedeIO.Find(x => x.ID == entrada.RedeIO).Descricao;

                        // ativo em UM
                        if (entrada.EEAtivo)
                        {
                            EEAtivo = "Sim";
                        }
                        else
                        {
                            EEAtivo = "Não";
                        }

                        // reconhecer alarmes
                        if (entrada.ReconhecerAlarme)
                        {
                            ReconhecerAlarme = "Sim";
                        }
                        else
                        {
                            ReconhecerAlarme = "Não";
                        }
                    }

                    // adiciona linha
                    row = sheet.CreateRow(rowIndex++);

                    // Entrada Digital Interna
                    numeroCelulaXLS(row, 0, entrada.NumEntradaGateway, _intCellStyle);
                    sheet.SetColumnWidth(0, 6000);

                    // Nome entrada
                    textoCelulaXLS(row, 1, entrada.Descricao);
                    sheet.SetColumnWidth(1, 7000);

                    // rede IO
                    textoCelulaXLS(row, 2, rede);
                    sheet.SetColumnWidth(2, 4000);

                    if (entrada.Programado && entrada.Remota != 255)
                    {
                        // remota
                        numeroCelulaXLS(row, 3, entrada.Remota, _intCellStyle);
                    }
                    else
                    {
                        // remota
                        textoCelulaXLS(row, 3, desprogramado);
                    }

                    sheet.SetColumnWidth(3, 3500);

                    if (entrada.Programado && entrada.Endereco >= 0 && entrada.Endereco < 65000)
                    {
                        // endereco
                        numeroCelulaXLS(row, 4, entrada.Endereco, _intCellStyle);
                    }
                    else
                    {
                        // endereco
                        textoCelulaXLS(row, 4, desprogramado);
                    }

                    sheet.SetColumnWidth(4, 3500);

                    // texto Ativo
                    textoCelulaXLS(row, 5, entrada.Ativo);
                    sheet.SetColumnWidth(5, 7000);

                    // texto Inativo
                    textoCelulaXLS(row, 6, entrada.Inativo);
                    sheet.SetColumnWidth(6, 7000);

                    // ativo em UM
                    textoCelulaXLS(row, 7, EEAtivo);
                    sheet.SetColumnWidth(7, 5000);

                    // reconhecer alarme
                    textoCelulaXLS(row, 8, ReconhecerAlarme);
                    sheet.SetColumnWidth(8, 5000);
                }
            }

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS_GateX(workbook, sheet, _negritoCellStyle);

            // largura de cada coluna
            sheet.SetColumnWidth(0, 7000);
            sheet.SetColumnWidth(1, 8000);

            // retorna planilha
            return workbook;
        }


        // GET: Entradas Digitais PDF
        public ActionResult GateX_EntradasDigitais_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista entradas
            GateX_ED_Metodos edMetodos = new GateX_ED_Metodos();
            List<GateX_ED_Dominio> entradas = edMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.Entradas = entradas;

            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_EntradasDigitais_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_EntradasDigitais_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        // GET: Entradas Digitais EMAIL
        public async Task<ActionResult> GateX_EntradasDigitais_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);


            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoEntradasDigitais;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista entradas
            GateX_ED_Metodos edMetodos = new GateX_ED_Metodos();
            List<GateX_ED_Dominio> entradas = edMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.Entradas = entradas;

            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_EntradasDigitais_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_EntradasDigitais_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "GateX_EntradasDigitaisEmail";
            var message = await EMailTemplateAsync(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.NomeGateway", ViewBag.NomeGateway);
            message = message.Replace("ViewBag.DescrConfig", ViewBag.DescrConfig);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Entradas Digitais Print
        public ActionResult GateX_EntradasDigitais_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista entradas
            GateX_ED_Metodos edMetodos = new GateX_ED_Metodos();
            List<GateX_ED_Dominio> entradas = edMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.Entradas = entradas;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // imprime
            return View();
        }
    }
}