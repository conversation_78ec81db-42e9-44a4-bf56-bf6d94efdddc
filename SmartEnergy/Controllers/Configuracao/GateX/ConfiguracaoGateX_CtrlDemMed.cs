﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Configuracao Controle Demanda Média
        public ActionResult GateX_CtrlDemMed(int IDGateway, int Origem = 0)
        {
            // tela de ajuda - controle demanda média
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // receber configuração
            List<GateX_CtrlDemMed_Dominio> ctrls = GateX_CtrlDemMed_Receber(IDGateway, Origem);

            // prepara listas
            GateX_CtrlDemMed_PreparaListas(IDGateway);

            // retorna configuração
            return View(ctrls);
        }

        // Configuração Controle Demanda Média - Receber
        private List<GateX_CtrlDemMed_Dominio> GateX_CtrlDemMed_Receber(int IDGateway, int Origem)
        {
            // controle demanda média
            GateX_CtrlDemMed_Metodos ctrlMetodos = new GateX_CtrlDemMed_Metodos();
            List<GateX_CtrlDemMed_Dominio> ctrls = new List<GateX_CtrlDemMed_Dominio>();

            // controle demanda média (saídas)
            GateX_CtrlDemMed_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemMed_SD_Metodos();
            List<GateX_CtrlDemMed_SD_Dominio> ctrlSDs = new List<GateX_CtrlDemMed_SD_Dominio>();

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // status
            bool solicitaCtrl = true;
            bool solicitaCtrlSD = true;

            // verifica Origem
            // 0: ler da gateway
            // 1: ler do banco de dados atual
            // 2: ler do banco de dados editando
            if (Origem == 0)
            {
                // status
                solicitaCtrl = false;
                solicitaCtrlSD = false;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // solicita configuração dos controles demanda média
                    if (solicitaCtrl = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_DEM_MED, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // solicita configuração dos controles demanda média (saídas)
                        if (solicitaCtrlSD = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_DEM_MED_SD, SMCOM_TIPO_SOL.SOLICITA))
                        {
                            // copia configuração da gateway para 'editando'
                            // caso ainda não exista configuração 'atual', será criada 
                            ctrls = ctrlMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.CtrlDemMed);
                            ctrlSDs = ctrlSDMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.CtrlDemMed_SD);
                        }
                    }
                }

                // verifica se solicitou
                if (solicitaCtrl && solicitaCtrlSD)
                {
                    // atualiza lista de medições energia
                    GateX_AtualizaLista(gateway, SMCOM_FUNCOES_SOL.PROGR_MED_EN_LISTA);

                    // atualiza lista de saidas digitais
                    GateX_AtualizaLista(gateway, SMCOM_FUNCOES_SOL.PROGR_SD_LISTA);
                }
                else
                {
                    // copia configuração 'atual' para 'editando'
                    ctrls = ctrlMetodos.CopiarParaEditando(IDGateway);
                    ctrlSDs = ctrlSDMetodos.CopiarParaEditando(IDGateway);
                }
            }
            else if (Origem == 1)
            {
                // copia configuração 'atual' para 'editando'
                ctrls = ctrlMetodos.CopiarParaEditando(IDGateway);
                ctrlSDs = ctrlSDMetodos.CopiarParaEditando(IDGateway);
            }
            else
            {
                // configuração em 'editando'
                ctrls = ctrlMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
                ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            }

            // copia status
            ViewBag.solicitaCtrl = solicitaCtrl;
            ViewBag.solicitaCtrlSD = solicitaCtrlSD;

            // copia configuração
            ViewBag.CtrlDemMed = ctrls;
            ViewBag.CtrlDemMed_SD = ctrlSDs;

            // retorna configuração
            return (ctrls);
        }

        // GET: Configuracao Controle Demanda Média - Editar
        public ActionResult GateX_CtrlDemMed_Editar(int IDGateway, int NumCtrlGateway)
        {
            // tela de ajuda - controle demanda média
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            GateX_CtrlDemMed_PreparaListas(IDGateway);

            // controle demanda média (editando)
            GateX_CtrlDemMed_Metodos ctrlMetodos = new GateX_CtrlDemMed_Metodos();
            GateX_CtrlDemMed_Dominio ctrl = ctrlMetodos.ListarPorNumCtrl(IDGateway, NumCtrlGateway, STATUS_CFG.EDITANDO);

            // alarmes
            if (ctrl.AlarmeTipoAllOn == 0 && ctrl.AlarmeAuxAllOn == 0)
            {
                // nenhum
                ctrl.AlarmeTipoAllOn = 100;
            }

            if (ctrl.AlarmeTipoAllOff == 0 && ctrl.AlarmeAuxAllOff == 0)
            {
                // nenhum
                ctrl.AlarmeTipoAllOff = 100;
            }

            // multiplica tempo entre religamentos por 10 (10 = 1)
            ctrl.TempoReliga *= 10;

            // controle demanda média (saídas) (editando)
            GateX_CtrlDemMed_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemMed_SD_Metodos();
            List<GateX_CtrlDemMed_SD_Dominio> ctrlSDs = ctrlSDMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // percorre saidas
            foreach (GateX_CtrlDemMed_SD_Dominio sd in ctrlSDs)
            {
                // divide nível de desligamento por 10 (920 = 92.0)
                sd.ControlMedDesligado /= 10.0;

                // divide nível de sligamento por 10 (920 = 92.0)
                sd.ControlMedLigado /= 10.0;
            }

            ViewBag.CtrlDemMed_SD = ctrlSDs;

            // medições
            GateX_CtrlDemMed_ObterMedicoes(IDGateway);

            // saidas
            GateX_CtrlDemMed_ObterSaidas(IDGateway);

            return View(ctrl);
        }

        private void GateX_CtrlDemMed_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // medições energia
            GateX_MedEner_Lista_Metodos medEnerMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medEner_Lista = medEnerMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.medEner_Lista = medEner_Lista;

            // medições
            List<ListaTiposDominio> listaMedicoes = new List<ListaTiposDominio>();
            ViewBag.listaMedicoes = listaMedicoes;

            // le saidas
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.SaidasDigitais = saidasDigitais;

            // saidas
            List<ListaTiposDominio> listaSaidas = new List<ListaTiposDominio>();
            ViewBag.listaSaidas = listaSaidas;


            // le tipo comando alarme
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listaCtrlOtimizacao = listatiposMetodos.ListarTodos("TipoCtrlOtimizacao", false, 0);
            ViewBag.listaCtrlOtimizacao = listaCtrlOtimizacao;

            // le tipo comando alarme
            List<ListaTiposDominio> listatipoCmdAlarme = listatiposMetodos.ListarTodos("TipoCmdAlarme", false, 0);
            ViewBag.listaTipoCmdAlarme = listatipoCmdAlarme;

            // le tipo alarme sistema
            List<ListaTiposDominio> listatipoAlarmeSistema = listatiposMetodos.ListarTodos("TipoAlarmeSistema", false, 0);
            ViewBag.listaTipoAlarmeSistema = listatipoAlarmeSistema;

            // le tipo controle demanda projetada
            List<ListaTiposDominio> listatipoCtrPj = listatiposMetodos.ListarTodos("TipoCtrlDemandaPrj", false, 0);
            ViewBag.listaTipoCtrPj = listatipoCtrPj;

            // le tipo controle fator de potencia indutivo
            List<ListaTiposDominio> listatipoCtrFPInd = listatiposMetodos.ListarTodos("TipoCtrlFPInd", false, 0);
            ViewBag.listaTipoCtrFPInd = listatipoCtrFPInd;

            // le tipo controle fator de potencia capacitivo
            List<ListaTiposDominio> listatipoCtrFPCap = listatiposMetodos.ListarTodos("TipoCtrlFPCap", false, 0);
            ViewBag.listaTipoCtrFPCap = listatipoCtrFPCap;

            // cria lista de alarmes do usuario
            List<ListaTiposDominio> listaAlmUsuario = new List<ListaTiposDominio>();

            for (int i = 0; i < SGATEX.NUM_CTRL_ALM; i++)
            {
                var alm = new ListaTiposDominio()
                {
                    ID = i,
                    Descricao = string.Format("Alarme de Usuário [{0:00}]", i)
                };

                listaAlmUsuario.Add(alm);
            }

            // copia lista alarme usuario
            ViewBag.listaAlmUsuario = listaAlmUsuario;

            return;
        }

        // POST: Configuracao Controle Demanda Média - Programar
        [HttpPost]
        public ActionResult GateX_CtrlDemMed_Programar(GateX_CtrlDemMed_Dominio CtrlDemMed, List<GateX_CtrlDemMed_SD_Dominio> ctrlSDs)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };


            // alarmes
            if (CtrlDemMed.AlarmeTipoAllOn == 100)
            {
                // nenhum
                CtrlDemMed.AlarmeTipoAllOn = 0;
                CtrlDemMed.AlarmeAuxAllOn = 0;
            }

            if (CtrlDemMed.AlarmeTipoAllOff == 100)
            {
                // nenhum
                CtrlDemMed.AlarmeTipoAllOff = 0;
                CtrlDemMed.AlarmeAuxAllOff = 0;
            }

            // divide tempo entre religamentos por 10 (10 = 1)
            CtrlDemMed.TempoReliga /= 10;


            // salva controle demanda média
            GateX_CtrlDemMed_Metodos ctrlMetodos = new GateX_CtrlDemMed_Metodos();
            ctrlMetodos.Salvar(CtrlDemMed, STATUS_CFG.EDITANDO);

            // excluir todas saidas do controle
            GateX_CtrlDemMed_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemMed_SD_Metodos();
            ctrlSDMetodos.ExcluirPorNumCtrl(CtrlDemMed.IDGateway, CtrlDemMed.NumCtrlGateway, STATUS_CFG.EDITANDO);

            // salva controle demanda média (saídas)
            foreach (GateX_CtrlDemMed_SD_Dominio sd in ctrlSDs)
            {
                // verifica se saída é deste controle
                if (sd.NumCtrlGateway == CtrlDemMed.NumCtrlGateway)
                {
                    // multiplica nível de desligamento por 10 (920 = 92.0)
                    sd.ControlMedDesligado *= 10.0;

                    // multiplica nível de sligamento por 10 (920 = 92.0)
                    sd.ControlMedLigado *= 10.0;

                    ctrlSDMetodos.Salvar(sd, STATUS_CFG.EDITANDO);
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuracao Controle Demanda Média - Excluir
        public ActionResult GateX_CtrlDemMed_Excluir(int IDGateway, int NumCtrlGateway)
        {
            // lista gateway para inserir evento                        
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // apaga programacao
            GateX_CtrlDemMed_Metodos ctrlMetodos = new GateX_CtrlDemMed_Metodos();
            ctrlMetodos.ExcluirPorNumCtrl(IDGateway, NumCtrlGateway, STATUS_CFG.EDITANDO);

            GateX_CtrlDemMed_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemMed_SD_Metodos();
            ctrlSDMetodos.ExcluirPorNumCtrl(IDGateway, NumCtrlGateway, STATUS_CFG.EDITANDO);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // POST: Configuracao Controle Demanda Média - Enviar
        [HttpPost]
        public ActionResult GateX_CtrlDemMed_Enviar(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // copia configuração 'editando' para 'atual'
            GateX_CtrlDemMed_Metodos ctrlMetodos = new GateX_CtrlDemMed_Metodos();
            List<GateX_CtrlDemMed_Dominio> ctrls = ctrlMetodos.CopiarParaAtual(IDGateway);

            GateX_CtrlDemMed_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemMed_SD_Metodos();
            List<GateX_CtrlDemMed_SD_Dominio> ctrlSDs = ctrlSDMetodos.CopiarParaAtual(IDGateway);

            // variavel de retorno
            bool enviaCtrlDemMed = false;
            bool enviaCtrlDemMed_SD = false;

            // verifica
            if (ctrls != null && ctrlSDs != null && gateway != null)
            {
                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // copia para lista de envio
                    smartCom.gateX.CtrlDemMed = ctrls;

                    // envia controle demanda média
                    enviaCtrlDemMed = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_DEM_MED, SMCOM_TIPO_SOL.ENVIA);

                    // copia para lista de envio
                    smartCom.gateX.CtrlDemMed_SD = ctrlSDs;

                    // envia controle demanda média (saídas)
                    enviaCtrlDemMed_SD = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_DEM_MED_SD, SMCOM_TIPO_SOL.ENVIA);
                }
            }

            // verifica retorno
            if (enviaCtrlDemMed && enviaCtrlDemMed_SD)
            {
                // excluir editando
                ctrlMetodos.ExcluirEditando(IDGateway);
                ctrlSDMetodos.ExcluirEditando(IDGateway);

                // histórico de configuração
                HistoricoConfiguracoesDominio hist = new HistoricoConfiguracoesDominio();
                hist.IDGateway = IDGateway;
                hist.NomeConfiguracao = "GateX_CtrlDemMed";
                hist.Envio = "MQTT";
                hist.Atualizacao = DateTime.Now;
                hist.Versao = GateX_Versao_String(IDGateway);
                hist.IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");

                // insere histórico de configuração
                HistoricoConfiguracoesMetodos histMetodos = new HistoricoConfiguracoesMetodos();
                histMetodos.Inserir(hist);

                // controle demanda média (saídas)
                hist.NomeConfiguracao = "GateX_CtrlDemMed_SD";
                histMetodos.Inserir(hist);

                // caso OK insiro evento
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, IDGateway);
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Falha no envio da configuração!"
                };
            }

            // retorna status
            return Json(returnedData);
        }




        //
        // MEDICOES
        //

        // GET: Obter Medicoes
        public JsonResult GateX_CtrlDemMed_ObterMedicoes(int IDGateway)
        {
            // medições
            List<ListaTiposDominio> listaMedicoes = new List<ListaTiposDominio>();

            // medições energia
            GateX_MedEner_Lista_Metodos medEnerMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medEner_Lista = medEnerMetodos.ListarPorIDGateway(IDGateway);

            // nenhum
            ListaTiposDominio tipo_nenhum = new ListaTiposDominio();
            tipo_nenhum.ID = 255;
            tipo_nenhum.Descricao = "Nenhum";
            listaMedicoes.Add(tipo_nenhum);

            // preenche lista de medições
            for (int i = 0; i < SGATEX.NUM_MED_EN; i++)
            {
                // default
                ListaTiposDominio tipo = new ListaTiposDominio();
                tipo.ID = i;
                tipo.Descricao = string.Format("[{0:00}] Medição de Energia {1}", i, i);

                // copia
                if (medEner_Lista != null)
                {
                    // verifica se tem na lista
                    GateX_MedEner_Lista_Dominio med = medEner_Lista.First(item => item.NumMedicaoGateway == i);

                    if (med != null)
                    {
                        tipo.Descricao = string.Format("[{0:00}] {1}", i, med.Descricao);
                    }
                }

                // insere na lista
                listaMedicoes.Add(tipo);
            }

            // lista de medições
            ViewBag.listaMedicoes = listaMedicoes;

            // retorna o valor em JSON
            return Json(listaMedicoes, JsonRequestBehavior.AllowGet);
        }



        //
        // SAIDAS
        //

        // GET: Obter Saidas
        public JsonResult GateX_CtrlDemMed_ObterSaidas(int IDGateway)
        {
            // saidas
            List<ListaTiposDominio> listaSaidas = new List<ListaTiposDominio>();

            // le saidas
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);

            // preenche lista de saídas
            for (int i = 0; i < SGATEX.NUM_SD; i++)
            {
                // default
                ListaTiposDominio tipo = new ListaTiposDominio();
                tipo.ID = i;
                tipo.Descricao = string.Format("[{0:00}] Saída Digital {1}", i, i);

                // copia
                if (saidasDigitais != null)
                {
                    // verifica se tem na lista
                    GateX_SD_Lista_Dominio med = saidasDigitais.First(item => item.NumSaidaGateway == i);

                    if (med != null)
                    {
                        tipo.Descricao = string.Format("[{0:00}] {1}", i, med.Descricao);
                    }
                }

                // insere na lista
                listaSaidas.Add(tipo);
            }

            // lista de saídas
            ViewBag.listaSaidas = listaSaidas;

            // retorna o valor em JSON
            return Json(listaSaidas, JsonRequestBehavior.AllowGet);
        }

        // recebe configuração (comando receber configurações)
        public bool GateX_CtrlDemMed_ComandoRecebe(GatewaysDominio gateway, string versao)
        {
            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita programação
                if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_DEM_MED, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // solicita configuração dos controles demanda média (saídas)
                    if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_CTRL_DEM_MED_SD, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // salvar
                        GateX_CtrlDemMed_Metodos ctrlMetodos = new GateX_CtrlDemMed_Metodos();
                        ctrlMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.CtrlDemMed, STATUS_CFG.ATUAL);

                        // salvar (controle demanda média (saídas))
                        GateX_CtrlDemMed_SD_Metodos ctrlSDMetodos = new GateX_CtrlDemMed_SD_Metodos();
                        ctrlSDMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.CtrlDemMed_SD, STATUS_CFG.ATUAL);

                        // ok
                        return (true);
                    }
                }
            }

            // erro
            return (false);
        }
    }
}