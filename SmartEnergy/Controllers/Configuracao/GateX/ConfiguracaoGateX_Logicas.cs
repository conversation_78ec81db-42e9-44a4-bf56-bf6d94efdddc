﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Configuração Lógicas
        public ActionResult GateX_Logicas(int IDGateway, int Origem = 0)
        {
            // tela de ajuda - lógicas
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // receber configuração
            List<GateX_Logicas_Dominio> logicas = GateX_Logicas_Receber(IDGateway, Origem);

            // prepara listas
            GateX_Logicas_PreparaListas(IDGateway);

            // retorna configuração
            return View(logicas);
        }

        // Configuração Lógicas - Receber
        private List<GateX_Logicas_Dominio> GateX_Logicas_Receber(int IDGateway, int Origem)
        {
            // lógicas
            GateX_Logicas_Metodos prgMetodos = new GateX_Logicas_Metodos();
            List<GateX_Logicas_Dominio> logicas = new List<GateX_Logicas_Dominio>();

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // status
            bool solicitaProg = true;

            // verifica Origem
            // 0: ler da gateway
            // 1: ler do banco de dados atual
            // 2: ler do banco de dados editando
            if (Origem == 0)
            {
                // status
                solicitaProg = false;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // solicita configuração das lógicas
                    if (solicitaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_LOGICAS, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // copia configuração da gateway para 'editando'
                        // caso ainda não exista configuração 'atual', será criada 
                        logicas = prgMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.Logicas);
                    }
                }

                // verifica se não solicitou
                if (!solicitaProg)
                {
                    // copia configuração 'atual' para 'editando'
                    logicas = prgMetodos.CopiarParaEditando(IDGateway);
                }
            }
            else if (Origem == 1)
            {
                // copia configuração 'atual' para 'editando'
                logicas = prgMetodos.CopiarParaEditando(IDGateway);
            }
            else
            {
                // configuração em 'editando'
                logicas = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            }

            // converte operandos para fórmula de todas as lógicas
            foreach (GateX_Logicas_Dominio logica in logicas)
            {
                if (logica.Programado)
                {
                    logica.Formula = Funcoes_GateX.Logica2String(logica);
                }
            }

            // copia status
            ViewBag.SolicitaProg = solicitaProg;

            // retorna configuração
            return (logicas);
        }

        // GET: Configuração Lógicas - Editar
        public ActionResult GateX_Logicas_Editar(int IDGateway, int NumLogicaGateway)
        {
            // tela de ajuda - programacoes lógicas
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            GateX_Logicas_PreparaListas(IDGateway);

            // le lógica a ser programada (editando)          
            GateX_Logicas_Metodos prgMetodos = new GateX_Logicas_Metodos();
            GateX_Logicas_Dominio logica = prgMetodos.ListarPorNumLogica(IDGateway, NumLogicaGateway, STATUS_CFG.EDITANDO);

            // converte operandos para fórmula da lógica
            if (logica.Programado)
            {
                logica.Formula = Funcoes_GateX.Logica2String(logica);
            }

            return View(logica);
        }

        // prepara listas
        private void GateX_Logicas_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // saidas
            List<ListaTiposDominio> listaSaidas = new List<ListaTiposDominio>();

            // le saidas
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway);

            // preenche lista de saídas
            for (int i = 0; i < SGATEX.NUM_SD; i++)
            {
                // default
                ListaTiposDominio tipo = new ListaTiposDominio();
                tipo.ID = i;
                tipo.Descricao = string.Format("[{0:00}] Saída Digital {1}", i, i);

                // copia
                if (saidasDigitais != null)
                {
                    // verifica se tem na lista
                    GateX_SD_Lista_Dominio sd = saidasDigitais.First(item => item.NumSaidaGateway == i);

                    if (sd != null)
                    {
                        tipo.Descricao = string.Format("[{0:00}] {1}", i, sd.Descricao);
                    }
                }

                // insere na lista
                listaSaidas.Add(tipo);
            }

            // lista de saídas
            ViewBag.listaSaidas = listaSaidas;

            return;
        }

        // POST: Configuração Lógicas - Programar
        [HttpPost]
        public ActionResult GateX_Logicas_Programar(GateX_Logicas_Dominio logica)
        {
            // erro
            string status = "ERRO";
            string erro = "";

            // converte string para Lógica
            Funcoes_GateX.ERR_LG retorno = Funcoes_GateX.String2Logica(logica.Formula, ref logica);

            if (retorno == Funcoes_GateX.ERR_LG.LG_LG_OK)
            {
                // salva
                GateX_Logicas_Metodos prgMetodos = new GateX_Logicas_Metodos();
                prgMetodos.Salvar(logica, STATUS_CFG.EDITANDO);

                // ok
                status = "OK";
            }
            else
            {
                var erros = new Dictionary<Funcoes_GateX.ERR_LG, string>()
                {
                    {Funcoes_GateX.ERR_LG.LG_LG_OK, ""},
                    {Funcoes_GateX.ERR_LG.LG_NOT_INICIO_FORMULA, "Operador NOT não pode ser o primeiro da formula"},
                    {Funcoes_GateX.ERR_LG.LG_FALTA_OPERANDO, "Falta operando"},
                    {Funcoes_GateX.ERR_LG.LG_FALTA_OPERADOR, "Falta operador"},
                    {Funcoes_GateX.ERR_LG.LG_OPERADOR_INEXISTENTE, "Operador inexistente"},
                    {Funcoes_GateX.ERR_LG.LG_ERR_SINTAX, "Erro de sintaxe"},
                    {Funcoes_GateX.ERR_LG.LG_ERR_SINTAX_ENTR_PARENT, "Abrir parênteses incorreto"},
                    {Funcoes_GateX.ERR_LG.LG_FALTA_ABR_PARENT, "Falta abrir parênteses"},
                    {Funcoes_GateX.ERR_LG.LG_FALTA_FECH_PARENT, "Falta fechar parênteses"},
                    {Funcoes_GateX.ERR_LG.LG_OPERANDO_SDNRO_INVALID, "Operando SAÍDA com número inválido"},
                    {Funcoes_GateX.ERR_LG.LG_OPERANDO_EENRO_INVALID, "Operando ENTRADA com número inválido"},
                    {Funcoes_GateX.ERR_LG.LG_PARENT_FECH_ANTES_ABERT, "Fechar parênteses antes de abrir"},
                    {Funcoes_GateX.ERR_LG.LG_ABR_PARENT_APOS_OPERANDO, "Não pode abrir parênteses antes do operando"},
                    {Funcoes_GateX.ERR_LG.LG_DOIS_OPERANDOS_SEGUIDOS, "Não pode ter dois operandos seguidos"},
                    {Funcoes_GateX.ERR_LG.LG_NOT_APOS_OPERANDO, "Operador NOT não pode estar apó operando"},
                    {Funcoes_GateX.ERR_LG.LG_OVERFLOW_BUF_POLOREV, "Excedeu quantidade de operandos"},
                    {Funcoes_GateX.ERR_LG.LG_OVERFLOW_BUF_NPOLOREV, "Excedeu quantidade de operandos"}
                };

                // erro
                erro = erros[retorno];
            }
               
            // retorno
            var returnedData = new
            {
                status = status,
                erro = erro
            };

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Lógicas - Excluir
        public ActionResult GateX_Logicas_Excluir(int IDGateway, int NumLogicaGateway)
        {
            // apaga programacao
            GateX_Logicas_Metodos prgMetodos = new GateX_Logicas_Metodos();
            prgMetodos.ExcluirPorNumLogica(IDGateway, NumLogicaGateway, STATUS_CFG.EDITANDO);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // POST: Configuração Lógicas - Enviar
        [HttpPost]
        public ActionResult GateX_Logicas_Enviar(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // configuração em 'editando'
            GateX_Logicas_Metodos prgMetodos = new GateX_Logicas_Metodos();
            List<GateX_Logicas_Dominio> logicas = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // copia configuração 'editando' para 'atual'
            logicas = prgMetodos.CopiarParaAtual(IDGateway);

            // variavel de retorno
            bool enviaProg = false;

            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // copia para lista de envio
                smartCom.gateX.Logicas = logicas;

                // envia 
                enviaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_LOGICAS, SMCOM_TIPO_SOL.ENVIA);
            }

            // verifica retorno
            if (enviaProg)
            {
                // excluir editando
                prgMetodos.ExcluirEditando(IDGateway);

                // histórico de configuração
                HistoricoConfiguracoesDominio hist = new HistoricoConfiguracoesDominio();
                hist.IDGateway = IDGateway;
                hist.NomeConfiguracao = "GateX_Logicas";
                hist.Envio = "MQTT";
                hist.Atualizacao = DateTime.Now;
                hist.Versao = GateX_Versao_String(IDGateway);
                hist.IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");

                // insere histórico de configuração
                HistoricoConfiguracoesMetodos histMetodos = new HistoricoConfiguracoesMetodos();
                histMetodos.Inserir(hist);

                // caso OK insiro evento
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, IDGateway);
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Falha no envio da configuração!"
                };
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Lógicas - Copiar
        public ActionResult GateX_Logicas_Copiar(int IDGateway, int Origem, int Inicio, int Fim)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // lista programacoes de lógicas da gateway salvas no banco de dados
            GateX_Logicas_Metodos prgMetodos = new GateX_Logicas_Metodos();
            List<GateX_Logicas_Dominio> logicas = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            ViewBag.Logicas = logicas;

            // lista lógica origem
            GateX_Logicas_Dominio logicaOrigem = prgMetodos.ListarPorNumLogica(IDGateway, Origem, STATUS_CFG.EDITANDO);

            if (logicaOrigem.NumLogicaGateway >= Inicio && logicaOrigem.NumLogicaGateway <= Fim)
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "A lógica origem não pode estar entre as lógicas destino."
                };

                // retorna erro
                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            if (Fim < Inicio)
            {
                // copia para lógicas desejadas decrescente
                for (int i = Inicio; i >= Fim; i--)
                {
                    GateX_Logicas_Dominio logicaCopia = prgMetodos.ListarPorNumLogica(IDGateway, i, STATUS_CFG.EDITANDO);

                    // copia
                    GateX_Logicas_CopiaConfiguracao(logicaOrigem, ref logicaCopia);

                    // salva lógica copiada
                    prgMetodos.Salvar(logicaCopia, STATUS_CFG.EDITANDO);
                }
            }

            else
            {
                // copia para lógicas desejadas crescente
                for (int i = Inicio; i <= Fim; i++)
                {
                    GateX_Logicas_Dominio logicaCopia = prgMetodos.ListarPorNumLogica(IDGateway, i, STATUS_CFG.EDITANDO);

                    // copia
                    GateX_Logicas_CopiaConfiguracao(logicaOrigem, ref logicaCopia);

                    // salva lógica copiada
                    prgMetodos.Salvar(logicaCopia, STATUS_CFG.EDITANDO);
                }
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);

        }

        // copia configuração
        public void GateX_Logicas_CopiaConfiguracao(GateX_Logicas_Dominio logicaOrigem, ref GateX_Logicas_Dominio logicaCopia)
        {
            logicaCopia.IDGateway = logicaOrigem.IDGateway;

            logicaCopia.Saida = logicaOrigem.Saida;
            logicaCopia.QtOperandos = logicaOrigem.QtOperandos;
            logicaCopia.Operandos = logicaOrigem.Operandos;

            return;
        }

        // recebe configuração (comando receber configurações)
        public bool GateX_Logicas_ComandoRecebe(GatewaysDominio gateway, string versao)
        {
            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita programação
                if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_LOGICAS, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // salvar
                    GateX_Logicas_Metodos prgMetodos = new GateX_Logicas_Metodos();
                    prgMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.Logicas, STATUS_CFG.ATUAL);

                    // ok
                    return (true);
                }
            }

            // erro
            return (false);
        }
    }
}