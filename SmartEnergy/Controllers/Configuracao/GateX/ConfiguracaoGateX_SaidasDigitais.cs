﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Configuração Saidas Digitais
        public ActionResult GateX_SaidaDigital(int IDGateway, int Origem = 0)
        {
            // tela de ajuda - saidas digitais
            CookieStore.SalvaCookie_String("PaginaAjuda", "Configuracao_GateX_SaidasDigitais");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // receber configuração
            List<GateX_SD_Dominio> saidas = GateX_SaidaDigital_Receber(IDGateway, Origem);

            // retorna configuração
            return View(saidas);
        }

        // Configuração Saida Digital - Receber
        private List<GateX_SD_Dominio> GateX_SaidaDigital_Receber(int IDGateway, int Origem)
        {
            // saidas digitais
            GateX_SD_Metodos sdMetodos = new GateX_SD_Metodos();
            List<GateX_SD_Dominio> saidas = new List<GateX_SD_Dominio>();

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // status
            bool solicitaProg = true;

            // verifica Origem
            // 0: ler da gateway
            // 1: ler do banco de dados atual
            // 2: ler do banco de dados editando
            if (Origem == 0)
            {
                // status
                solicitaProg = false;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // solicita configuração das saidas
                    if (solicitaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_SD, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // copia configuração da gateway para 'editando'
                        // caso ainda não exista configuração 'atual', será criada 
                        saidas = sdMetodos.CopiarParaEditando(IDGateway, smartCom.gateX.SD);

                        // atualizar saídas digitais
                        Atualizar_SaidasDigitais(gateway, saidas);
                    }
                }

                // verifica se solicitou
                if (solicitaProg)
                {
                    // solicita supervisão das saidas (controles associados)
                    smartCom.Solicitar(SMCOM_FUNCOES_SOL.SUPERV_SD, SMCOM_TIPO_SOL.SOLICITA);

                    // atualiza lista de medições de energia
                    GateX_AtualizaLista(gateway, SMCOM_FUNCOES_SOL.PROGR_MED_EN_LISTA);

                    // atualiza lista de entradas digitais
                    GateX_AtualizaLista(gateway, SMCOM_FUNCOES_SOL.PROGR_ED_LISTA);
                }
                else
                {
                    // copia configuração 'atual' para 'editando'
                    saidas = sdMetodos.CopiarParaEditando(IDGateway);
                }
            }
            else if (Origem == 1)
            {
                // copia configuração 'atual' para 'editando'
                saidas = sdMetodos.CopiarParaEditando(IDGateway);
            }
            else
            {
                // configuração em 'editando'
                saidas = sdMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            }

            // lista das saídas
            GateX_SD_Lista_Metodos sdListaMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidas_lista = sdListaMetodos.ListarPorIDGateway(IDGateway);

            // percorre saidas da supervisão
            foreach (GateX_SD_Lista_Dominio saida_lista in saidas_lista)
            {
                // verifica se programado
                if (saida_lista.Programado)
                {
                    // copia
                    saidas[saida_lista.NumSaidaGateway].Estado = saida_lista.Estado;
                    saidas[saida_lista.NumSaidaGateway].cAss = saida_lista.cAss;
                    saidas[saida_lista.NumSaidaGateway].rAss = saida_lista.rAss;

                    // CTRL_ASS - controle associados    cAss          rAss
                    // ASS_PJ  0  0x0001 // demProj   PJ:0=N 1=Y    PJ:0=OFF 1=ON
                    // ASS_MD  1  0x0002 // demMedi   MD:0=N 1=Y    MD:0=OFF 1=ON
                    // ASS_AC  2  0x0004 // demAcum   AC:0=N 1=Y    AC:0=OFF 1=ON
                    // ASS_FP  3  0x0008 // fatPote   FP:0=N 1=Y    FP:0=OFF 1=ON

                    // ASS_HT  4  0x0010 // HrTarif   HT:0=N 1=Y    HT:0=OFF 1=ON
                    // ASS_PH  5  0x0020 // prgHora   PH:0=N 1=Y    PH:0=OFF 1=ON
                    // ASS_FM  6  0x0040 // falhaMd   FM:0=N 1=Y    FM:0=OFF 1=ON
                    // ASS_AL  7  0x0080 // alarme    AL:0=N 1=Y    AL:0=OFF 1=ON

                    // ASS_EE  8  0x0100 // EnEstad   EE:0=N 1=Y    EE:0=OFF 1=ON
                    // ASS_RM  9  0x0200 // remoto    RM:0=N 1=Y    RM:0=OFF 1=ON
                    // ASS_LG  10 0x0400 // prgLogi   LG:0=N 1=Y    LG:0=OFF 1=ON
                    // ASS_AN  11 0x0800 // analogi   AN:0=N 1=Y    AN:0=OFF 1=ON

                    // ASS_TZ  14 0x4000 // tempori   TZ:0=N 1=Y    TZ:0=OFF 1=ON ==> cAss:é tahTEMPORIZADO (não é cfg_ASS_TZ)
                    // ASS_MAN 15 0x8000 // autoMan   Sd:0=A 1=M    Sd:0=OFF 1=ON ==> cAss:é AUTO/MAN       rAss é estadoSd
                    string controles = "";

                    if (Funcoes_Bit.IsBitSet_UInt16((UInt16)saida_lista.cAss, 15))
                    {
                        controles += Funcoes_Bit.IsBitSet_UInt16((UInt16)saida_lista.cAss, 15) ? "Manual [ " : "";
                    }

                    controles += Funcoes_Bit.IsBitSet_UInt16((UInt16)saida_lista.cAss, 0) ? "CtrlPj " : "";
                    controles += Funcoes_Bit.IsBitSet_UInt16((UInt16)saida_lista.cAss, 1) ? "CtrlMd " : "";
                    controles += Funcoes_Bit.IsBitSet_UInt16((UInt16)saida_lista.cAss, 2) ? "CtrlAc " : "";
                    controles += Funcoes_Bit.IsBitSet_UInt16((UInt16)saida_lista.cAss, 3) ? "CtrlFP " : "";
                    controles += Funcoes_Bit.IsBitSet_UInt16((UInt16)saida_lista.cAss, 11) ? "CtrlAn " : "";
                    controles += Funcoes_Bit.IsBitSet_UInt16((UInt16)saida_lista.cAss, 5) ? "CtrlHora " : "";
                    controles += Funcoes_Bit.IsBitSet_UInt16((UInt16)saida_lista.cAss, 10) ? "Lógica " : "";
                    controles += Funcoes_Bit.IsBitSet_UInt16((UInt16)saida_lista.cAss, 4) ? "CmdH " : "";
                    controles += Funcoes_Bit.IsBitSet_UInt16((UInt16)saida_lista.cAss, 8) ? "ED " : "";
                    controles += Funcoes_Bit.IsBitSet_UInt16((UInt16)saida_lista.cAss, 6) ? "Falha " : "";
                    controles += Funcoes_Bit.IsBitSet_UInt16((UInt16)saida_lista.cAss, 7) ? "Alarme " : "";
                    controles += Funcoes_Bit.IsBitSet_UInt16((UInt16)saida_lista.cAss, 9) ? "Remoto " : "";
                    controles += Funcoes_Bit.IsBitSet_UInt16((UInt16)saida_lista.cAss, 14) ? "Tempo " : "";

                    if (Funcoes_Bit.IsBitSet_UInt16((UInt16)saida_lista.cAss, 15))
                    {
                        controles += Funcoes_Bit.IsBitSet_UInt16((UInt16)saida_lista.cAss, 15) ? " ]" : "";
                    }

                    saidas[saida_lista.NumSaidaGateway].Controles = controles;
                }
            }

            // copia status
            ViewBag.SolicitaProg = solicitaProg;

            // retorna configuração
            return (saidas);
        }

        // GET: Configuração Saida Digital - Editar
        public ActionResult GateX_SaidaDigital_Editar(int IDGateway, int NumSaidaGateway)
        {
            // tela de ajuda - programacoes saidas digitais
            CookieStore.SalvaCookie_String("PaginaAjuda", "Configuracao_GateX_SaidasDigitaisEditar");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            GateX_SaidaDigital_Editar_PreparaListas(IDGateway);

            // le saida a ser programada (editando)          
            GateX_SD_Metodos sdMetodos = new GateX_SD_Metodos();
            GateX_SD_Dominio saida = sdMetodos.ListarPorNumSaida(IDGateway, NumSaidaGateway, STATUS_CFG.EDITANDO);

            // trata valor endereco
            if (saida.Endereco > 65000)
            {
                saida.Endereco = -1;
            }
            
            // temporizadores
            saida.TempoMinLigadoP *= 10;
            saida.TempoMinLigadoFP *= 10;
            saida.TempoMaxDesligadoP *= 10;
            saida.TempoMaxDesligadoFP *= 10;

            saida.TempoMinDesligado *= 10;
            saida.TempoReligar *= 10;
            saida.TempoMinLigado *= 10;
            saida.DelayDesligar *= 10;
            saida.DelayLigar *= 10;

            // auxiliares para separar os valores minuto/segundo
            int TrMnD_seg = 0;
            int TrMnD_min = 0;
            int TrMxD_seg = 0;
            int TrMxD_min = 0;
            int TrMnL_seg = 0;
            int TrMnL_min = 0;
            int P_MxD_seg = 0;
            int P_MxD_min = 0;
            int P_Mnl_seg = 0;
            int P_Mnl_min = 0;
            int F_MxD_seg = 0;
            int F_MxD_min = 0;
            int F_Mnl_seg = 0;
            int F_Mnl_min = 0;

            // converte valores para formato (mm:ss)
            TrMnD_seg = saida.TempoMinDesligado % 60;
            TrMnD_min = (saida.TempoMinDesligado - TrMnD_seg) / 60;

            TrMxD_seg = saida.TempoReligar % 60;
            TrMxD_min = (saida.TempoReligar - TrMxD_seg) / 60;

            TrMnL_seg = saida.TempoMinLigado % 60;
            TrMnL_min = (saida.TempoMinLigado - TrMnL_seg) / 60;

            P_MxD_seg = saida.TempoMaxDesligadoP % 60;
            P_MxD_min = (saida.TempoMaxDesligadoP - P_MxD_seg) / 60;

            P_Mnl_seg = saida.TempoMinLigadoP % 60;
            P_Mnl_min = (saida.TempoMinLigadoP - P_Mnl_seg) / 60;

            F_MxD_seg = saida.TempoMaxDesligadoFP % 60;
            F_MxD_min = (saida.TempoMaxDesligadoFP - F_MxD_seg) / 60;

            F_Mnl_seg = saida.TempoMinLigadoFP % 60;
            F_Mnl_min = (saida.TempoMinLigadoFP - F_Mnl_seg) / 60;

            // copia para view
            ViewBag.TrMnD_seg = TrMnD_seg;
            ViewBag.TrMnD_min = TrMnD_min;
            ViewBag.TrMxD_seg = TrMxD_seg;
            ViewBag.TrMxD_min = TrMxD_min;
            ViewBag.TrMnL_seg = TrMnL_seg;
            ViewBag.TrMnL_min = TrMnL_min;
            ViewBag.P_Mnl_min = P_Mnl_min;
            ViewBag.P_MxD_seg = P_MxD_seg;
            ViewBag.P_MxD_min = P_MxD_min;
            ViewBag.P_Mnl_seg = P_Mnl_seg;
            ViewBag.P_Mnl_min = P_Mnl_min;
            ViewBag.F_MxD_seg = F_MxD_seg;
            ViewBag.F_MxD_min = F_MxD_min;
            ViewBag.F_Mnl_seg = F_Mnl_seg;
            ViewBag.F_Mnl_min = F_Mnl_min;


            // lista das saídas
            GateX_SD_Lista_Metodos sdListaMetodos = new GateX_SD_Lista_Metodos();
            GateX_SD_Lista_Dominio saida_lista = sdListaMetodos.ListarPorNumSaida(IDGateway, NumSaidaGateway);

            // copia
            saida.Estado = saida_lista.Estado;
            saida.cAss = saida_lista.cAss;
            saida.rAss = saida_lista.rAss;


            return View(saida);
        }

        // prepara listas
        private void GateX_SaidaDigital_Editar_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // le lista medições de energia
            GateX_MedEner_Lista_Metodos medMetodos = new GateX_MedEner_Lista_Metodos();
            List<GateX_MedEner_Lista_Dominio> medicoes = medMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.Medicoes = medicoes;

            // le lista entradas digitais
            GateX_ED_Lista_Metodos edMetodos = new GateX_ED_Lista_Metodos();
            List<GateX_ED_Lista_Dominio> entradas = edMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.Entradas = entradas;

            // le configuração saidas digitais
            GateX_SD_Metodos sdMetodos = new GateX_SD_Metodos();
            List<GateX_SD_Dominio> saidas = sdMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.Saidas = saidas;

            // le tipos rede IO
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DI);      // remove DI
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.SEG);     // remove seg
            ViewBag.listaTipoRedeIO = listatipoRedeIO;

            // le tipos numeros remota Rede K
            List<ListaTiposDominio> listatipoNumRemotaRedeK = listatiposMetodos.ListarTodos("TipoGateX_NumRemotaRedeK", false, 0);
            ViewBag.listaTipoNumRemotaRedeK = listatipoNumRemotaRedeK;

            // le tipo comando tarifacao
            List<ListaTiposDominio> listatipoCmdTarifacao = listatiposMetodos.ListarTodos("TipoComandoTarifacao", false, 0);
            ViewBag.listaTipoCmdTarifacao = listatipoCmdTarifacao;

            // le tipo alarme
            List<ListaTiposDominio> listatipoAlarme = listatiposMetodos.ListarTodos("TipoAlarmeStatus", false, 0);
            ViewBag.listaTipoAlarme = listatipoAlarme;

            // le tipo comando alarme
            List<ListaTiposDominio> listatipoCmdAlarme = listatiposMetodos.ListarTodos("TipoCmdAlarme", false, 0);
            ViewBag.listaTipoCmdAlarme = listatipoCmdAlarme;

            // le tipo alarme sistema
            List<ListaTiposDominio> listatipoAlarmeSistema = listatiposMetodos.ListarTodos("TipoAlarmeSistema", false, 0);
            ViewBag.listaTipoAlarmeSistema = listatipoAlarmeSistema;

            // le tipo controle demanda projetada
            List<ListaTiposDominio> listatipoCtrPj = listatiposMetodos.ListarTodos("TipoCtrlDemandaPrj", false, 0);
            ViewBag.listaTipoCtrPj = listatipoCtrPj;

            // le tipo controle fator de potencia indutivo
            List<ListaTiposDominio> listatipoCtrFPInd = listatiposMetodos.ListarTodos("TipoCtrlFPInd", false, 0);
            ViewBag.listaTipoCtrFPInd = listatipoCtrFPInd;

            // le tipo controle fator de potencia capacitivo
            List<ListaTiposDominio> listatipoCtrFPCap = listatiposMetodos.ListarTodos("TipoCtrlFPCap", false, 0);
            ViewBag.listaTipoCtrFPCap = listatipoCtrFPCap;

            // lista numero remotas
            int[] listaRemotas = new int[SGATEX.NUM_REMOTAS];

            for (int i = 0; i < listaRemotas.Length; i++)
            {
                listaRemotas[i] = i;
            }

            // copia lista remotas
            ViewBag.ListaRemotas = listaRemotas;

            // cria lista de alarmes do usuario
            List<ListaTiposDominio> listaAlmUsuario = new List<ListaTiposDominio>();

            for (int i = 0; i < SGATEX.NUM_CTRL_ALM; i++)
            {
                var alm = new ListaTiposDominio()
                {
                    ID = i,
                    Descricao = string.Format("Alarme de Usuário [{0:00}]", i)
                };

                listaAlmUsuario.Add(alm);
            }

            // copia lista alarme usuario
            ViewBag.listaAlmUsuario = listaAlmUsuario;

            return;
        }

        // POST: Configuração Saida Digital - Programar
        [HttpPost]
        public ActionResult GateX_SaidaDigital_Programar(GateX_SD_Dominio saidaDigital)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // temporizadores
            saidaDigital.TempoMinLigadoP /= 10;
            saidaDigital.TempoMinLigadoFP /= 10;
            saidaDigital.TempoMaxDesligadoP /= 10;
            saidaDigital.TempoMaxDesligadoFP /= 10;

            saidaDigital.TempoMinDesligado /= 10;
            saidaDigital.TempoReligar /= 10;
            saidaDigital.TempoMinLigado /= 10;
            saidaDigital.DelayDesligar /= 10;
            saidaDigital.DelayLigar /= 10;

            // verifica alarme
            if (saidaDigital.ComandoAlarme == 0)
            {
                saidaDigital.ComandoAlarmeTipo = 0;
                saidaDigital.ComandoAlarmeAux = 0;
            }            

            // trato endereco driver
            if (saidaDigital.Remota == 255)
            {
                saidaDigital.Endereco = -1;
            }

            // salva
            GateX_SD_Metodos sdMetodos = new GateX_SD_Metodos();
            sdMetodos.Salvar(saidaDigital, STATUS_CFG.EDITANDO);

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Saida Digital - Excluir
        public ActionResult GateX_SaidaDigital_Excluir(int IDGateway, int NumSaidaGateway)
        {
            // lista gateway para inserir evento                        
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // apaga programacao
            GateX_SD_Metodos sdMetodos = new GateX_SD_Metodos();
            sdMetodos.ExcluirPorNumSaida(IDGateway, NumSaidaGateway, STATUS_CFG.EDITANDO);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // POST: Configuração Saida Digital - Enviar
        [HttpPost]
        public ActionResult GateX_SaidaDigital_Enviar(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // configuração em 'editando'
            GateX_SD_Metodos sdMetodos = new GateX_SD_Metodos();
            List<GateX_SD_Dominio> saidasDigitais = sdMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // auxiliar de verificacao DO
            int aux_DO = 0;

            List<string> saidasErro = new List<string>();

            // trata valores temporizadores
            foreach (GateX_SD_Dominio saida in saidasDigitais)
            {
                if (saida.RedeIO == 4)
                {
                    // copia descricao da saída com erro
                    saidasErro.Add(saida.Descricao);

                    // sinaliza que saida está configurada como Rede DO
                    aux_DO++;                    
                }
            }

            // caso exista mais de uma saida configurada com Rede DO retorna erro
            if (aux_DO > 1)
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Não é possível programar mais de uma saída configurada com Remota 'DO'. Favor verificar as seguintes Saídas:\n " + String.Join("\n", saidasErro.ToArray())
                };

                // retorna status
                return Json(returnedData);
            }

            // copia configuração 'editando' para 'atual'
            saidasDigitais = sdMetodos.CopiarParaAtual(IDGateway);

            // variavel de retorno
            bool enviaProg = false;

            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // copia para lista de envio
                smartCom.gateX.SD = saidasDigitais;

                // envia programação horária
                enviaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_SD, SMCOM_TIPO_SOL.ENVIA);
            }

            // verifica retorno
            if (enviaProg)
            {
                // atualizar saídas digitais
                Atualizar_SaidasDigitais(gateway, saidasDigitais);

                // excluir editando
                sdMetodos.ExcluirEditando(IDGateway);

                // histórico de configuração
                HistoricoConfiguracoesDominio hist = new HistoricoConfiguracoesDominio();
                hist.IDGateway = IDGateway;
                hist.NomeConfiguracao = "GateX_SD";
                hist.Envio = "MQTT";
                hist.Atualizacao = DateTime.Now;
                hist.Versao = GateX_Versao_String(IDGateway);
                hist.IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");

                // insere histórico de configuração
                HistoricoConfiguracoesMetodos histMetodos = new HistoricoConfiguracoesMetodos();
                histMetodos.Inserir(hist);

                // caso OK insiro evento
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, IDGateway);
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Falha no envio da configuração!"
                };
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuração Saida Digital - Copiar
        public ActionResult GateX_SaidaDigital_Copiar(int IDGateway, int Origem, int Inicio, int Fim)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // lista programacoes de saidas da gateway salvas no banco de dados
            GateX_SD_Metodos saidasDigitaisMetodos = new GateX_SD_Metodos();
            List<GateX_SD_Dominio> saidasDigitais = saidasDigitaisMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            ViewBag.Saidas = saidasDigitais;

            // lista saida origem
            GateX_SD_Dominio saidaOrigem = saidasDigitaisMetodos.ListarPorNumSaida(IDGateway, Origem, STATUS_CFG.EDITANDO);

            if (saidaOrigem.RedeIO == 4)
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Não é permitido copiar saídas configuradas com Remota 'DO'."
                };

                // retorna erro
                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            if (saidaOrigem.NumSaidaGateway >= Inicio && saidaOrigem.NumSaidaGateway <= Fim)
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "A saída origem não pode estar entre as saídas destino."
                };

                // retorna erro
                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // auxiliar
            int copia = 1;

            if (Fim < Inicio)
            {
                // copia para saidas desejadas decrescente
                for (int i = Inicio; i >= Fim; i--)
                {
                    GateX_SD_Dominio saidaCopia = saidasDigitaisMetodos.ListarPorNumSaida(IDGateway, i, STATUS_CFG.EDITANDO);

                    // copia
                    GateX_SaidaDigital_CopiaConfiguracao(saidaOrigem, ref saidaCopia, copia);

                    // salva saida copiada
                    saidasDigitaisMetodos.Salvar(saidaCopia, STATUS_CFG.EDITANDO);

                    // incrementa cópia
                    copia++;
                }
            }

            else
            {
                // copia para saidas desejadas crescente
                for (int i = Inicio; i <= Fim; i++)
                {
                    GateX_SD_Dominio saidaCopia = saidasDigitaisMetodos.ListarPorNumSaida(IDGateway, i, STATUS_CFG.EDITANDO);

                    // copia
                    GateX_SaidaDigital_CopiaConfiguracao(saidaOrigem, ref saidaCopia, copia);

                    // salva saida copiada
                    saidasDigitaisMetodos.Salvar(saidaCopia, STATUS_CFG.EDITANDO);

                    // incrementa cópia
                    copia++;
                }
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);

        }

        // copia configuração
        public void GateX_SaidaDigital_CopiaConfiguracao(GateX_SD_Dominio saidaOrigem, ref GateX_SD_Dominio saidaCopia, int copia)
        {
            saidaCopia.IDGateway = saidaOrigem.IDGateway;
            saidaCopia.Descricao = saidaOrigem.Descricao;

            // verifica tamanho se cabe cópia (limite - 3 caracteres)
            if (saidaCopia.Descricao.Length <= (31 - 3))
            {
                // coloca indice
                saidaCopia.Descricao += string.Format(".{0}", copia);
            }

            saidaCopia.RegEvTemporizador = saidaOrigem.RegEvTemporizador;
            saidaCopia.RedeIO = saidaOrigem.RedeIO;
            saidaCopia.Remota = saidaOrigem.Remota;
            saidaCopia.Endereco = saidaOrigem.Endereco;
            saidaCopia.LogicaInvertida = saidaOrigem.LogicaInvertida;

            saidaCopia.TempoMinLigadoP = saidaOrigem.TempoMinLigadoP;
            saidaCopia.TempoMinLigadoFP = saidaOrigem.TempoMinLigadoFP;
            saidaCopia.TempoMaxDesligadoP = saidaOrigem.TempoMaxDesligadoP;
            saidaCopia.TempoMaxDesligadoFP = saidaOrigem.TempoMaxDesligadoFP;
            saidaCopia.TempoMaxDesligadoFP_Hora = saidaOrigem.TempoMaxDesligadoFP_Hora;

            saidaCopia.TempoMinDesligado = saidaOrigem.TempoMinDesligado;
            saidaCopia.TempoReligar = saidaOrigem.TempoReligar;
            saidaCopia.TempoMinLigado = saidaOrigem.TempoMinLigado;
            saidaCopia.DelayDesligar = saidaOrigem.DelayDesligar;
            saidaCopia.DelayLigar = saidaOrigem.DelayLigar;

            saidaCopia.ComandoHrTarifador = saidaOrigem.ComandoHrTarifador;
            saidaCopia.MedicaoFalhaDeslig = saidaOrigem.MedicaoFalhaDeslig;
            saidaCopia.Retrosinalizador = saidaOrigem.Retrosinalizador;
            saidaCopia.ComandoSuperv = saidaOrigem.ComandoSuperv;

            saidaCopia.ControlProjNro = saidaOrigem.ControlProjNro;
            saidaCopia.PotenciaCargaProj = saidaOrigem.PotenciaCargaProj;
            saidaCopia.MedicaoRetro = saidaOrigem.MedicaoRetro;
            saidaCopia.ControlProjPrio = saidaOrigem.ControlProjPrio;

            saidaCopia.ControlFPNro = saidaOrigem.ControlFPNro;
            saidaCopia.PotenciaCapacitor = saidaOrigem.PotenciaCapacitor;
            saidaCopia.ControlPrioFP = saidaOrigem.ControlPrioFP;

            saidaCopia.ControlMedNro = saidaOrigem.ControlMedNro;
            saidaCopia.ControlMedDesligado = saidaOrigem.ControlMedDesligado;
            saidaCopia.ControlMedLigado = saidaOrigem.ControlMedLigado;

            saidaCopia.ControlAcumNro = saidaOrigem.ControlAcumNro;
            saidaCopia.ControlAcumDeslig = saidaOrigem.ControlAcumDeslig;

            saidaCopia.ControlAnaNro = saidaOrigem.ControlAnaNro;
            saidaCopia.ControlAnaDesligado = saidaOrigem.ControlAnaDesligado;
            saidaCopia.ControlAnaLigado = saidaOrigem.ControlAnaLigado;

            saidaCopia.ComandoAlarme = saidaOrigem.ComandoAlarme;
            saidaCopia.ComandoAlarmeTipo = saidaOrigem.ComandoAlarmeTipo;
            saidaCopia.ComandoAlarmeAux = saidaOrigem.ComandoAlarmeAux;

            saidaCopia.EEAtivaSaida = saidaOrigem.EEAtivaSaida;
            saidaCopia.HabAcessoRemoto = saidaOrigem.HabAcessoRemoto;

            return;
        }

        // Atualizar saidas digitais
        private void Atualizar_SaidasDigitais(GatewaysDominio gateway, List<GateX_SD_Dominio> saidas)
        {

            // salva SaidasDigitais
            SaidasDigitaisMetodos saidasDigitaisMetodos = new SaidasDigitaisMetodos();

            // exclui todas as saidas digitais desta gateway
            saidasDigitaisMetodos.ExcluirTodosIDGateway(gateway.IDGateway);

            // atualiza
            foreach (GateX_SD_Dominio sd in saidas)
            {
                // verifica se programado
                if (sd.Programado)
                {
                    SaidasDigitaisDominio saida = new SaidasDigitaisDominio();

                    saida.IDSaidaDigital = 0;
                    saida.IDCliente = gateway.IDCliente;
                    saida.IDGateway = gateway.IDGateway;
                    saida.Descricao = sd.Descricao;
                    saida.NumSaidaGateway = sd.NumSaidaGateway;
                    saida.status = STATUS_SD.DESCONHECIDO;

                    // salva
                    saidasDigitaisMetodos.Salvar(saida);
                }
            }

            // retorna
            return;
        }

        // recebe configuração (comando receber configurações)
        public bool GateX_SaidasDigitais_ComandoRecebe(GatewaysDominio gateway, string versao)
        {
            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita programação
                if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_SD, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // salvar
                    GateX_SD_Metodos sdMetodos = new GateX_SD_Metodos();
                    sdMetodos.InserirLista(gateway.IDGateway, smartCom.gateX.SD, STATUS_CFG.ATUAL);

                    // atualizar saídas digitais
                    Atualizar_SaidasDigitais(gateway, smartCom.gateX.SD);

                    // ok
                    return (true);
                }
            }

            // erro
            return (false);
        }
    }
}