﻿using System.Web.Mvc;

using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Versão do firmware
        public ActionResult GateX_Versao(int IDGateway)
        {
            // tela de ajuda
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // receber versão
            GateX_Versao_Dominio versao = GateX_Versao_Receber(IDGateway);

            // prepara listas
            GateX_Versao_PreparaListas(IDGateway);

            // retorna
            return View(versao);
        }

        // Versão do Firmware - Receber
        private GateX_Versao_Dominio GateX_Versao_Receber(int IDGateway)
        {
            // versão
            GateX_Versao_Dominio versao = new GateX_Versao_Dominio();
            versao.Modelo = "SmartGate X";
            versao.Versao = "---";
            versao.DriverRemota = "---";

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // status
            bool solicitaProg = false;

            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita versão
                if (solicitaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.VERSAO, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // copia versão recebida
                    versao = smartCom.gateX.Versao;

                    // controle da configuração
                    versao.Status_Cfg = STATUS_CFG.ATUAL;

                    // salva configuração
                    GateX_Versao_Metodos prgMetodos = new GateX_Versao_Metodos();
                    prgMetodos.Salvar(IDGateway, versao);
                }
            }

            // copia novo status da solicitacao para a view
            ViewBag.SolicitaProg = solicitaProg;

            // retorna versão
            return (versao);
        }

        // prepara listas
        private void GateX_Versao_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            return;
        }

        // Versão do firmware (string)
        public string GateX_Versao_String(int IDGateway)
        {
            // versão
            GateX_Versao_Metodos prgMetodos = new GateX_Versao_Metodos();

            // comunicação MQTT
            SmartCom smartCom = new SmartCom(IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita versão
                if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.VERSAO, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // salva versão atual
                    smartCom.gateX.Versao.Status_Cfg = STATUS_CFG.ATUAL;
                    prgMetodos.Salvar(IDGateway, smartCom.gateX.Versao);

                    // retorna
                    return (smartCom.gateX.Versao.Versao);
                }
            }

            // erro
            return ("");
        }

        // Versão do firmware (número)
        public int GateX_Versao_Int(int IDGateway)
        {
            // versão
            GateX_Versao_Metodos prgMetodos = new GateX_Versao_Metodos();
            GateX_Versao_Dominio versao = prgMetodos.ListarPorIDGateway(IDGateway);

            // versão
            return (versao.VersaoInt);
        }
    }
}