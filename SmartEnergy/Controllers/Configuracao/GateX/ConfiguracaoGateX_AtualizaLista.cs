﻿using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // atualiza lista
        private bool GateX_AtualizaLista(GatewaysDominio gateway, int funcaoSolicitacao)
        {
            // status
            bool solicita = false;

            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita e atualiza lista de medições
                solicita = smartCom.Solicitar(funcaoSolicitacao, SMCOM_TIPO_SOL.SOLICITA);
            }

            // retorno status
            return (solicita);
        }
    }
}