﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Medições de Utilidades XLS Download
        [HttpGet]
        public virtual ActionResult GateX_MedUtil_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Medições de Utilidades XLS
        public ActionResult GateX_MedUtil_XLS(int IDGateway)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_GateX_MedUtil(IDGateway);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_MedUtil_Gateway_{0:000000}_{1:yyyyMMddHHmm}.xls", IDGateway, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // Medições de Utilidades Planilha
        private HSSFWorkbook XLS_GateX_MedUtil(int IDGateway)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // MEDIÇÕES DE UTILIDADES
            //

            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoMedUtil;

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            //  le lista de medições da gateway
            GateX_MedUtil_Metodos prgMetodos = new GateX_MedUtil_Metodos();
            List<GateX_MedUtil_Dominio> medUtil = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);

            // le lista tipo variável
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoVariavel = listatiposMetodos.ListarTodos("TipoGateX_Variavel_UT", false, 0);

            // le tipos rede IO
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);

            // le lista tipo variável formato
            List<ListaTiposDominio> listatipoVariavelFormato = listatiposMetodos.ListarTodos("TipoGateX_Variavel_Formato", false, 0);

            // le lista tipo escala
            List<ListaTiposDominio> listatipoEscala = listatiposMetodos.ListarTodos("TipoGateX_Escala", false, 0);

            // le lista saidas digitais
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidasDigitas = sdMetodos.ListarPorIDGateway(IDGateway);
            List<GateX_SD_Lista_Dominio> listaSaidas = new List<GateX_SD_Lista_Dominio>();

            GateX_SD_Lista_Dominio nenhum = new GateX_SD_Lista_Dominio();
            nenhum.NumSaidaGateway = 255;
            nenhum.Programado = false;
            nenhum.Descricao = "Nenhum";
            listaSaidas.Add(nenhum);

            // percorre saidas
            foreach (GateX_SD_Lista_Dominio saida in saidasDigitas)
            {
                saida.Descricao = string.Format("[{0:00}] {1}{2}", saida.NumSaidaGateway, saida.Descricao, (saida.Programado ? "" : " [Disponível]"));
                listaSaidas.Add(saida);
            }


            // cria planilha
            var sheet = workbook.CreateSheet("Medições de Utilidades");

            // cabecalho
            string[] cabecalho = { "Medição Interna", "Medições de Utilidades", "Unidade", "Variável", "Falta de Pulso (seg.)", "Média Móvel (seg.)", "Saída digital para não totalizar", "Saída digital para zerar totalizado", "Rede", "Remota", "Endereço", "Formato", "Zerar em (seg.)", "Remota não OK", "Entrada Analógica Mínima", "Entrada Analógica Máxima", "Unidade de Engenharia Mínima", "Unidade de Engenharia Máxima", "Constante Multiplicador", "Constante de Soma" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            IRow row;

            if (medUtil != null)
            {
                // percorre lógicas
                foreach (GateX_MedUtil_Dominio med in medUtil)
                {
                    string desprogramado = "---";
                    string Descricao = desprogramado;
                    string Unidade = desprogramado;
                    string TipoVariavel = desprogramado;
                    string TempoAmostraFaltaPulso = desprogramado;
                    string TempoAmostraMediaMovel = desprogramado;
                    string ZeraAcumuladoSd = desprogramado;
                    string NaoAcumularSd = desprogramado;

                    string RedeIO = desprogramado;
                    string Remota = desprogramado;
                    string RemotaEnd = desprogramado;
                    string RemotaVar = desprogramado;
                    string TempoZerarRaw = desprogramado;

                    string v1_EntAnalogicaMin = desprogramado;
                    string v1_EntAnalogicaMax = desprogramado;
                    string v1_UnidadeEngMin = desprogramado;
                    string v1_UnidadeEngMax = desprogramado;
                    string v1_UpScale = desprogramado;

                    string v2_ConstanteMult = desprogramado;
                    string v2_ConstanteSoma = desprogramado;

                    if (med.Programado)
                    {
                        // Descrição e Unidade
                        Descricao = med.Descricao;
                        Unidade = med.Unidade;

                        // Falta de Pulso
                        TempoAmostraFaltaPulso = string.Format("{0}", med.TempoAmostraFaltaPulso * 10);
                      
                        // Média Móvel
                        TempoAmostraMediaMovel = string.Format("{0}", med.TempoAmostraMediaMovel * 10);

                        // Saída digital para não totalizar
                        GateX_SD_Lista_Dominio saida = listaSaidas.Find(item => item.NumSaidaGateway == med.NaoAcumularSd);
                        NaoAcumularSd = (saida != null) ? saida.Descricao : desprogramado;

                        // Saída digital para zerar totalizado
                        saida = listaSaidas.Find(item => item.NumSaidaGateway == med.ZeraAcumuladoSd);
                        ZeraAcumuladoSd = (saida != null) ? saida.Descricao : desprogramado;

                        // Tipo Variável
                        ListaTiposDominio tipo = listatipoVariavel.Find(item => item.ID == med.TipoVariavel);
                        TipoVariavel = (tipo != null) ? tipo.Descricao : desprogramado;

                        switch (med.TipoVariavel)
                        {
                            case SGATEX_TIPO_VARIAVEL.REM_EA:

                                // RedeIO / Remota / Endereço / Formato
                                tipo = listatipoRedeIO.Find(item => item.ID == med.v1_RedeIO);
                                RedeIO = (tipo != null) ? tipo.Descricao : desprogramado;
                                Remota = med.v1_Remota.ToString();
                                RemotaEnd = med.v1_RemotaEnd.ToString();
                                tipo = listatipoVariavelFormato.Find(item => item.ID == med.v1_RemotaVar);
                                RemotaVar = (tipo != null) ? tipo.Descricao : desprogramado;

                                // Zerar em
                                TempoZerarRaw = string.Format("{0}", med.v1_TempoZerarRaw * 10);

                                // Remota não OK
                                tipo = listatipoEscala.Find(item => item.ID == med.v1_UpScale);
                                v1_UpScale = (tipo != null) ? tipo.Descricao : desprogramado;

                                // Entrada Analógica Min/Max
                                v1_EntAnalogicaMin = string.Format("{0}", Math.Round(med.v1_EntAnalogicaMin, 5));
                                v1_EntAnalogicaMax = string.Format("{0}", Math.Round(med.v1_EntAnalogicaMax, 5));

                                // Unidade de Engenharia Min/Max
                                v1_UnidadeEngMin = string.Format("{0}", Math.Round(med.v1_UnidadeEngMin, 5));
                                v1_UnidadeEngMax = string.Format("{0}", Math.Round(med.v1_UnidadeEngMax, 5));

                                break;

                            case SGATEX_TIPO_VARIAVEL.REM_PULSO:

                                // RedeIO / Remota / Endereço / Formato
                                tipo = listatipoRedeIO.Find(item => item.ID == med.v2_RedeIO);
                                RedeIO = (tipo != null) ? tipo.Descricao : desprogramado;
                                Remota = med.v2_Remota.ToString();
                                RemotaEnd = med.v2_RemotaEnd.ToString();
                                tipo = listatipoVariavelFormato.Find(item => item.ID == med.v2_RemotaVar);
                                RemotaVar = (tipo != null) ? tipo.Descricao : desprogramado;

                                // Zerar em
                                TempoZerarRaw = string.Format("{0}", med.v2_TempoZerarRaw * 10);

                                // Constante Multiplicador/Soma
                                v2_ConstanteMult = string.Format("{0}", Math.Round(med.v2_ConstanteMult, 5));
                                v2_ConstanteSoma = string.Format("{0}", Math.Round(med.v2_ConstanteSoma, 5));

                                break;
                        }
                    }

                    // adiciona linha
                    row = sheet.CreateRow(rowIndex++);

                    // Medição
                    numeroCelulaXLS(row, 0, med.NumMedicaoGateway, _intCellStyle);
                    sheet.SetColumnWidth(0, 6000);

                    // Descrição
                    textoCelulaXLS(row, 1, Descricao);
                    sheet.SetColumnWidth(1, 7000);

                    // Unidade
                    textoCelulaXLS(row, 2, Unidade);
                    sheet.SetColumnWidth(2, 5000);

                    // Variável
                    textoCelulaXLS(row, 3, TipoVariavel);
                    sheet.SetColumnWidth(3, 7000);

                    // Falta de Pulso
                    textoCelulaXLS(row, 4, TempoAmostraFaltaPulso);
                    sheet.SetColumnWidth(4, 6000);

                    // Média Móvel
                    textoCelulaXLS(row, 5, TempoAmostraMediaMovel);
                    sheet.SetColumnWidth(5, 6000);

                    // Saída digital para não totalizar
                    textoCelulaXLS(row, 6, NaoAcumularSd);
                    sheet.SetColumnWidth(6, 8000);

                    // Saída digital para zerar totalizado
                    textoCelulaXLS(row, 7, ZeraAcumuladoSd);
                    sheet.SetColumnWidth(7, 8000);

                    // rede IO
                    textoCelulaXLS(row, 8, RedeIO);
                    sheet.SetColumnWidth(8, 4000);

                    // remota
                    textoCelulaXLS(row, 9, Remota);
                    sheet.SetColumnWidth(9, 4000);

                    // endereco
                    textoCelulaXLS(row, 10, RemotaEnd);
                    sheet.SetColumnWidth(10, 4000);

                    // formato
                    textoCelulaXLS(row, 11, RemotaVar);
                    sheet.SetColumnWidth(11, 4000);

                    // Zerar em
                    textoCelulaXLS(row, 12, TempoZerarRaw);
                    sheet.SetColumnWidth(12, 4000);

                    // Remota não OK
                    textoCelulaXLS(row, 13, v1_UpScale);
                    sheet.SetColumnWidth(13, 4500);

                    // Entrada Analógica Min/Max
                    textoCelulaXLS(row, 14, v1_EntAnalogicaMin);
                    sheet.SetColumnWidth(14, 8000);

                    textoCelulaXLS(row, 15, v1_EntAnalogicaMax);
                    sheet.SetColumnWidth(15, 8000);

                    // Unidade de Engenharia Min/Max
                    textoCelulaXLS(row, 16, v1_UnidadeEngMin);
                    sheet.SetColumnWidth(16, 8000);

                    textoCelulaXLS(row, 17, v1_UnidadeEngMax);
                    sheet.SetColumnWidth(17, 8000);

                    // Constante Multiplicador/Soma
                    textoCelulaXLS(row, 18, v2_ConstanteMult);
                    sheet.SetColumnWidth(18, 6500);

                    textoCelulaXLS(row, 19, v2_ConstanteSoma);
                    sheet.SetColumnWidth(19, 6500);
                }
            }

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS_GateX(workbook, sheet, _negritoCellStyle);

            // largura de cada coluna
            sheet.SetColumnWidth(0, 7000);
            sheet.SetColumnWidth(1, 8000);

            // retorna planilha
            return workbook;
        }


        // GET: Medições de Utilidades PDF
        public ActionResult GateX_MedUtil_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista Medições de Utilidades
            GateX_MedUtil_Metodos prgMetodos = new GateX_MedUtil_Metodos();
            List<GateX_MedUtil_Dominio> medUtil = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.medUtil = medUtil;

            // le lista tipo variável
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoVariavel = listatiposMetodos.ListarTodos("TipoGateX_Variavel_UT", false, 0);
            ViewBag.listatipoVariavel = listatipoVariavel;

            // le tipos rede IO
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DO);      // remove DO
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.SEG);     // remove seg
            ViewBag.listatipoRedeIO = listatipoRedeIO;


            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_MedUtil_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_MedUtil_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        // GET: Medições de Utilidades EMAIL
        public async Task<ActionResult> GateX_MedUtil_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);


            // descricao da configuracao
            ViewBag.DescrConfig = SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoMedUtil;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista Medições de Utilidades
            GateX_MedUtil_Metodos prgMetodos = new GateX_MedUtil_Metodos();
            List<GateX_MedUtil_Dominio> medUtil = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.medUtil = medUtil;

            // le lista tipo variável
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoVariavel = listatiposMetodos.ListarTodos("TipoGateX_Variavel_UT", false, 0);
            ViewBag.listatipoVariavel = listatipoVariavel;

            // le tipos rede IO
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DO);      // remove DO
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.SEG);     // remove seg
            ViewBag.listatipoRedeIO = listatipoRedeIO;


            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // nome do arquivo
            string nomeArquivo = string.Format("GateX_MedUtil_Gateway_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDGateway, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_GateX_MedUtil_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "GateX_MedUtilEmail";
            var message = await EMailTemplateAsync(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.NomeGateway", ViewBag.NomeGateway);
            message = message.Replace("ViewBag.DescrConfig", ViewBag.DescrConfig);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Medições de Utilidades Print
        public ActionResult GateX_MedUtil_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // IDGateway
            int IDGateway = ViewBag._IDGateway;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // lista gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.NomeGateway = gateway.Nome;

            // lista Medições de Utilidades
            GateX_MedUtil_Metodos prgMetodos = new GateX_MedUtil_Metodos();
            List<GateX_MedUtil_Dominio> medUtil = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            ViewBag.medUtil = medUtil;

            // le lista tipo variável
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatipoVariavel = listatiposMetodos.ListarTodos("TipoGateX_Variavel_UT", false, 0);
            ViewBag.listatipoVariavel = listatipoVariavel;

            // le tipos rede IO
            List<ListaTiposDominio> listatipoRedeIO = listatiposMetodos.ListarTodos("TipoGateX_RedeIO", false, 0);
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.DO);      // remove DO
            listatipoRedeIO.RemoveAll(x => x.ID == SGATEX_TIPO_REDE_IO.SEG);     // remove seg
            ViewBag.listatipoRedeIO = listatipoRedeIO;


            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // data e hora
            DateTime data_atual = DateTime.Now;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", data_atual);

            // imprime
            return View();
        }
    }
}