﻿using System;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ConfiguracaoController
    {
        // GET: Configuracao HistoricoRural
        public ActionResult HistoricoRural(int IDMedicao)
        {
            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le historicoRural
            HistoricoRuralMetodos historicoMetodos = new HistoricoRuralMetodos();
            var listaHistorico = historicoMetodos.ListarPorIDMedicao(IDMedicao);
            return View(listaHistorico);
        }

        // GET: Configuracao HistoricoRural - Editar
        public ActionResult HistoricoRural_Editar(int IDRural, int IDMedicao)
        {
            // pagina anterior
            ViewBag.returnUrl = Request.UrlReferrer;

            // ID Rural
            ViewBag.IDRural = IDRural;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // verifica se adicionando
            HistoricoRuralDominio historico = new HistoricoRuralDominio();
            if (IDRural == 0)
            {
                // zera com default
                historico.IDRural = 0;
                historico.IDCliente = ViewBag._IDCliente;
                historico.IDMedicao = IDMedicao;
                historico.Data = DateTime.Now;
                historico.DataTexto = historico.Data.ToString("d");
                historico.Rural = false;
                historico.Irrigantes = false;
                historico.BandeiraProporcional = false;
                historico.BandeiraDesconto = false;
                historico.DescontoRural = 0.0;
                historico.DescontoIrrigantes = 0.0;
            }
            else
            {
                // le historicoRural
                HistoricoRuralMetodos historicoMetodos = new HistoricoRuralMetodos();
                historico = historicoMetodos.ListarPorID(IDRural);
            }

            return View(historico);
        }

        // POST: Configuracao HistoricoRural - Salvar
        [HttpPost]
        public ActionResult HistoricoRural_Salvar(HistoricoRuralDominio historico)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // parse data
            historico.Data = DateTime.Parse(historico.DataTexto);

            // verifica se existe outro historico com a mesma data
            HistoricoRuralMetodos historicoMetodos = new HistoricoRuralMetodos();
            if (historicoMetodos.VerificarDuplicidade(historico))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Rural existente."
                };
            }
            else
            {
                // salva historicoRural
                historicoMetodos.Salvar(historico);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuracao HistoricoRural - Excluir
        public ActionResult HistoricoRural_Excluir(int IDRural)
        {
            // apaga o historicoRural
            HistoricoRuralMetodos historicoMetodos = new HistoricoRuralMetodos();
            historicoMetodos.Excluir(IDRural);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}