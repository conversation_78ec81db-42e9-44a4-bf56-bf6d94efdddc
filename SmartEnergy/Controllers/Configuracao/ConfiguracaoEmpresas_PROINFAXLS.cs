﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ConfiguracaoController
    {
        // GET: PROINFA XLS Download
        [HttpGet]
        public virtual ActionResult PROINFA_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Download PROINFA
        public ActionResult DownloadFile_PROINFA(int IDEmpresa = 0)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            // nome do arquivo
            string nomeArquivo = string.Format("PROINFA_{0:yyyyMMddHHmm}.xls", DateTime.Now);

            // gera planilha
            workbook = PROINFA_XLS(IDConsultor, IDEmpresa);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            // relatorio para PDF
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // PROINFA Planilha
        private HSSFWorkbook PROINFA_XLS(int IDConsultor, int IDEmpresa)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // PROINFA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("PROINFA");

            // cabecalho
            string[] cabecalho = { "ID [Agente/Filial]", "Sigla CCEE", "Ano", "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho", "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;


            // le clientes do consultor - ativos
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clienteMetodos.ListarPorIDConsultor(IDConsultor, 1);

            if (listaClientes != null)
            {
                // percorre clientes do consultor
                foreach (ClientesDominio cliente in listaClientes)
                {
                    // le empresas dos clientes do consultor
                    EmpresasMetodos empresasMetodos = new EmpresasMetodos();
                    List<EmpresasDominio> empresas = empresasMetodos.ListarPorIDCliente(cliente.IDCliente);

                    if (empresas != null)
                    {
                        // percorre empresas
                        foreach (EmpresasDominio empresa in empresas)
                        {
                            // verica se envia apenas a empresa selecionada
                            if (IDEmpresa > 0 && empresa.IDEmpresa != IDEmpresa)
                            {
                                continue;
                            }

                            // le PROINFA
                            EmpresasPROINFAMetodos proinfaMetodos = new EmpresasPROINFAMetodos();
                            List<EmpresasPROINFADominio> proinfas = proinfaMetodos.ListarPorId(empresa.IDEmpresa);

                            if (proinfas != null)
                            {
                                // verifica se vazio
                                if (proinfas.Count == 0)
                                {
                                    EmpresasPROINFADominio pro = new EmpresasPROINFADominio();
                                    pro.IDEmpresa = empresa.IDEmpresa;
                                    pro.Ano = DateTime.Now.Year;
                                    pro.Jan = 0.0;
                                    pro.Fev = 0.0;
                                    pro.Mar = 0.0;
                                    pro.Abr = 0.0;
                                    pro.Mai = 0.0;
                                    pro.Jun = 0.0;
                                    pro.Jul = 0.0;
                                    pro.Ago = 0.0;
                                    pro.Set = 0.0;
                                    pro.Out = 0.0;
                                    pro.Nov = 0.0;
                                    pro.Dez = 0.0;

                                    proinfas.Add(pro);
                                }

                                // percorre PROINFA
                                foreach (EmpresasPROINFADominio proinfa in proinfas)
                                {
                                    // adiciona linha
                                    row = sheet.CreateRow(rowIndex++);

                                    // IDEmpresa
                                    numeroCelulaXLS(row, 0, empresa.IDEmpresa, _intCellStyle);

                                    // SiglaCCEE
                                    textoCelulaXLS(row, 1, empresa.SiglaCCEE);

                                    // Ano
                                    numeroCelulaXLS(row, 2, proinfa.Ano, _intCellStyle);

                                    // Janeiro
                                    numeroCelulaXLS(row, 3, proinfa.Jan, _3CellStyle);

                                    // Fevereiro
                                    numeroCelulaXLS(row, 4, proinfa.Fev, _3CellStyle);

                                    // Março
                                    numeroCelulaXLS(row, 5, proinfa.Mar, _3CellStyle);

                                    // Abril
                                    numeroCelulaXLS(row, 6, proinfa.Abr, _3CellStyle);

                                    // Maio
                                    numeroCelulaXLS(row, 7, proinfa.Mai, _3CellStyle);

                                    // Junho
                                    numeroCelulaXLS(row, 8, proinfa.Jun, _3CellStyle);

                                    // Julho
                                    numeroCelulaXLS(row, 9, proinfa.Jul, _3CellStyle);

                                    // Agosto
                                    numeroCelulaXLS(row, 10, proinfa.Ago, _3CellStyle);

                                    // Setembro
                                    numeroCelulaXLS(row, 11, proinfa.Set, _3CellStyle);

                                    // Outubro
                                    numeroCelulaXLS(row, 12, proinfa.Out, _3CellStyle);

                                    // Novembro
                                    numeroCelulaXLS(row, 13, proinfa.Nov, _3CellStyle);

                                    // Dezembro
                                    numeroCelulaXLS(row, 14, proinfa.Dez, _3CellStyle);
                                }
                            }
                        }
                    }
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
            {
                if (i < 2)
                {
                    sheet.SetColumnWidth(i, 7000);
                }
                else
                {
                    sheet.SetColumnWidth(i, 3500);
                }
            }

            // retorna planilha
            return workbook;
        }
    }
}