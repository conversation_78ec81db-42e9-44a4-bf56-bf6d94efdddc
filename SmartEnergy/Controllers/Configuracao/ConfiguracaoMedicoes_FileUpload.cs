﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.OleDb;
using System.IO;
using System.Web;
using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        #region Actions

        /// <summary>
        /// Uploads the file.
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public virtual ActionResult UploadFile_ConfiguracaoMedicoes(int IDCliente)
        {
            // arquivo enviado
            HttpPostedFileBase myFile = Request.Files["fileupload"];

            // inicia variaveis
            bool isUploaded = false;
            string message = "Envio falhou";

            if (myFile != null && myFile.ContentLength != 0)
            {
                string pathForSaving = Server.MapPath("~/Temp");
                if (this.CreateFolderIfNeeded(pathForSaving))
                {
                    try
                    {
                        // nome do arquivo
                        string nome_arq = myFile.FileName;

                        // nome do caminho e arquivo
                        string caminho_arq = Path.Combine(pathForSaving, myFile.FileName);

                        // extensão através dos ultimos 4 caracteres
                        string extensao_arq = nome_arq.Substring(nome_arq.Length - 4).ToLower();

                        // verifica se arquivo existe e apaga
                        FileInfo file = new FileInfo(caminho_arq);

                        if( file.Exists )
                        {
                            // deleta
                            file.Delete();
                        }

                        // salva arquivo
                        myFile.SaveAs(caminho_arq);

                        // flag erro
                        bool OcorreuErro = false;
                        string MensagemErro = "Formato não permitido";

                        // configuração das medições
                        List<MedicoesDominio> listaMedicoes = new List<MedicoesDominio>();

                        // trata arquivo EXCEL
                        if( extensao_arq == ".xls" || extensao_arq == "xlsx")
                        {
                            // ler registros
                            listaMedicoes = LerExcel_ConfiguracaoMedicoes(IDCliente, caminho_arq, ref MensagemErro);

                            if (listaMedicoes == null || MensagemErro != "OK")
                            {
                                // falha
                                OcorreuErro = true;
                                message = string.Format("Envio falhou: {0}", MensagemErro);
                            }
                        }
                        else
                        {
                            // flag erro
                            OcorreuErro = true;
                            MensagemErro = "Formato não permitido";
                        }

                        // verifica se nao ocorreu erro de leitura
                        if ( !OcorreuErro )
                        {
                            // enviado, lido e atualizado BD com sucesso
                            isUploaded = true;
                            message = "Arquivo enviado com sucesso!";
                        }
                    }
                    catch (Exception ex)
                    {
                        // falha
                        message = string.Format("Envio falhou: {0}", ex.Message);
                    }
                }
            }
            return Json(new { isUploaded = isUploaded, message = message }, "text/html");
        }

        public List<MedicoesDominio> LerExcel_ConfiguracaoMedicoes(int IDCliente, string caminho_arq, ref string MensagemErro)
        {
            // conexao
            // baixar o provedor de acesso Excel 2010 em "https://www.microsoft.com/en-us/download/details.aspx?id=13255"
            // AccessDatabaseEngine_X64.exe
            // configurando no SQL SERVER https://www.dirceuresende.com/blog/sql-server-como-instalar-os-drivers-microsoft-ace-oledb-12-0-e-microsoft-jet-oledb-4-0/
            OleDbConnection _olecon;
            OleDbCommand _oleCmd;
            //String _StringConexao = String.Format(@"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={0};Extended Properties='Excel 12.0;HDR=YES;ReadOnly=False';", caminho_arq);
            String _StringConexao = String.Format(@"Provider=Microsoft.JET.OLEDB.4.0;Data Source={0};Extended Properties='Excel 8.0;HDR=YES;';", caminho_arq);

            // medições
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = new List<MedicoesDominio>();

            // le Excel
            try
            {
                // conecta
                _olecon = new OleDbConnection(_StringConexao);
                _olecon.Open();

                _oleCmd = new OleDbCommand();
                _oleCmd.Connection = _olecon;
                _oleCmd.CommandType = CommandType.Text;

                // le excel
                _oleCmd.CommandText = "SELECT * FROM [Carga$]";
                OleDbDataReader reader = _oleCmd.ExecuteReader();

                // inicia linha
                int linha = 0;

                // auxiliares de erro
                int erro = 0;
                string erro_str = "";

                // percorre linhas
                while (reader.Read())
                {
                    // le registro
                    MedicoesDominio medicao = new MedicoesDominio();
                    medicao.IDCliente = IDCliente;

                    // lê IDCliente
                    int IDCliente_lido = LeCelulabyName_Int(reader, linha, "IDCliente", ref erro, ref erro_str);

                    // verifica se IDCliente solicitado é o mesmo lido da tabela
                    if (IDCliente != IDCliente_lido)
                    {
                        // erro
                        erro = -1;
                        erro_str = string.Format("Linha [{0}] Coluna [IDCliente]: IDCliente inválido", linha);

                        goto FINAL;
                    }

                    // lê IDMedicao
                    medicao.IDMedicao = LeCelulabyName_Int(reader, linha, "IDMedicao", ref erro, ref erro_str);

                    // verifica se é alteração de medição existente
                    if (medicao.IDMedicao > 0)
                    {
                        // lê configuração da medição
                        medicao = medicaoMetodos.ListarPorId(medicao.IDMedicao);

                        // verifica se medição existente
                        if (medicao == null)
                        {

                        }




                    }



                    // insere na lista
                    medicoes.Add(medicao);

                    // salva
                    medicaoMetodos.Salvar(medicao);

                FINAL:

                    // proxima linha
                    linha++;
                }

                reader.Close();

                // termina
                if (_oleCmd != null)
                {
                    _oleCmd.Parameters.Clear();
                    _oleCmd.Dispose();
                }
                _oleCmd = null;

                if (_olecon != null)
                {
                    if (_olecon.State == ConnectionState.Open)
                        _olecon.Close();
                    _olecon.Dispose();
                }
                _olecon = null;

            }
            catch (Exception ex)
            {
                // erro
                MensagemErro = ex.Message;
                return (null);
            }

            // ok
            MensagemErro = "OK";
            return (medicoes);
        }



        // Lê celula INT pelo nome da coluna
        public int LeCelulabyName_Int(OleDbDataReader reader, int linha, string NomeColuna, ref int erro, ref string erro_str)
        {
            try
            {
                // procura coluna pelo nome
                int coluna = reader.GetOrdinal(NomeColuna);

                if (coluna >= 0)
                {
                    // le celula
                    int valor = reader.GetInt32(coluna);

                    // ok
                    erro = 0;
                    erro_str = "";
                    return (valor);
                }
            }
            catch (System.IndexOutOfRangeException)
            {
                // erro 
                erro = -1;
                erro_str = string.Format("Linha [{0}] Coluna [{1}]: Coluna não encontrada", linha, NomeColuna);
                return (0);
            }
            catch (System.InvalidCastException)
            {
                // erro 
                erro = -1;
                erro_str = string.Format("Linha [{0}] Coluna [{1}]: Valor inválido", linha, NomeColuna);
                return (0);
            }
            catch (Exception ex)
            {
                // erro 
                erro = -1;
                erro_str = string.Format("Linha [{0}] Coluna [{1}]: {2}", linha, NomeColuna, ex.Message);
                return (0);
            }

            // erro
            erro = -1;
            erro_str = string.Format("Linha [{0}] Coluna [{1}]: Erro Indefinido", linha, NomeColuna);
            return (0);
        }

        #endregion
    }
}