﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ConfiguracaoController
    {
        // Listas utilizadas
        private void PreparaListas_GrupoUsuario()
        {

            return;
        }

        // GET: Configuracao GrupoUsuarios
        public ActionResult GrupoUsuarios(int IDCliente)
        {
            // tela de ajuda - grupousuarios
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_GrupoUsuario();

            // le grupousuarios
            UsuarioGrupoMetodos grupoMetodos = new UsuarioGrupoMetodos();
            var listaGrupos = grupoMetodos.ListarPorIDCliente(IDCliente);
            return View(listaGrupos);
        }

        // GET: Configuracao GrupoUsuarios - Editar
        public ActionResult GrupoUsuarios_Editar(int IDUsuarioGrupo)
        {
            // tela de ajuda - grupousuarios
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_GrupoUsuario();

            // verifica se adicionando
            UsuarioGrupoDominio grupo = new UsuarioGrupoDominio();
            if (IDUsuarioGrupo == 0)
            {
                // zera usuario com default
                grupo.IDCliente = ViewBag._IDCliente;
                grupo.Usuarios = "";
            }
            else
            {
                // le grupousuario
                UsuarioGrupoMetodos grupoMetodos = new UsuarioGrupoMetodos();
                grupo = grupoMetodos.ListarPorId(IDUsuarioGrupo);
            }

            // usuarios
            List<int> UsuariosList = null;

            // copia usuarios para lista
            if (!String.IsNullOrEmpty(grupo.Usuarios))
            {
                UsuariosList = grupo.Usuarios.Split('/')
                    .Select(possibleIntegerAsString =>
                    {
                        int parsedInteger = 0;
                        bool isInteger = int.TryParse(possibleIntegerAsString, out parsedInteger);
                        return new { isInteger, parsedInteger };
                    })
                    .Where(tryParseResult => tryParseResult.isInteger)
                    .Select(tryParseResult => tryParseResult.parsedInteger)
                    .ToList();
            }

            // le usuarios configurados do grupo para lista
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            List<UsuarioDominio> usuarios = new List<UsuarioDominio>();

            // percorre usuarios
            if(UsuariosList != null)
            {
                foreach(int usuarioID in UsuariosList)
                {
                    // coloca na lista
                    UsuarioDominio user = usuarioMetodos.ListarPorId(usuarioID);

                    if( user != null )
                    {
                        usuarios.Add(user);
                    }
                }
            }

            ViewBag.Usuarios = usuarios;

            // le todos usuarios do cliente para lista
            List<UsuarioDominio> usuarios2 = usuarioMetodos.ListarPorIDCliente(grupo.IDCliente);
            ViewBag.Usuarios2 = usuarios2;

            return View(grupo);
        }

        // POST: Configuracao GrupoUsuarios - Salvar
        [HttpPost]
        public ActionResult GrupoUsuarios_Salvar(UsuarioGrupoDominio grupo, List<UsuarioDominio> Usuarios)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro grupousuario com o mesmo nome
            UsuarioGrupoMetodos grupoMetodos = new UsuarioGrupoMetodos();
            if (grupoMetodos.VerificarDuplicidade(grupo))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Grupo existente."
                };
            }
            else
            {
                // zera config
                string ConfigUsuarios = "";

                // verifica usuarios
                if (Usuarios != null)
                {
                    // percorre lista de usuarios
                    foreach (UsuarioDominio user in Usuarios)
                    {
                        // copia usuario
                        ConfigUsuarios = ConfigUsuarios + "/" + user.IDUsuario.ToString();
                    }

                    // fecha barra
                    ConfigUsuarios = ConfigUsuarios + "/";
                }

                // copia usuarios
                grupo.Usuarios = ConfigUsuarios;

                // salva grupousuario 
                grupoMetodos.Salvar(grupo);

                // evento 
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();

                if (grupo.IDUsuarioGrupo > 0)
                {
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GRUPO_USUARIO, grupo.IDUsuarioGrupo);
                }
                else
                {
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.GRUPO_USUARIO, grupo.IDUsuarioGrupo);
                }

                // verifica integridade
                int retorno = grupoMetodos.VerificarIntegridade(grupo);

                switch (retorno)
                {
                    case 1:
                        break;

                    case 2:
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "IDUsuarioGrupo adicionado com valor alto.<br><br>Favor verificar o grupo criado antes de prosseguir !!!"
                        };
                        break;
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuracao GrupoUsuarios - Excluir
        public ActionResult GrupoUsuarios_Excluir(int IDUsuarioGrupo)
        {
            // apaga o grupousuario
            UsuarioGrupoMetodos grupoMetodos = new UsuarioGrupoMetodos();
            grupoMetodos.Excluir(IDUsuarioGrupo);

            // evento 
            UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.EXCLUI_CONFIG, TABELA_CONFIG.GRUPO_USUARIO, IDUsuarioGrupo);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}