﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ConfiguracaoController
    {
        // GET: Configuracao Gateways
        public ActionResult Gateways(int IDCliente)
        {
            // tela de ajuda - gateways
            CookieStore.SalvaCookie_String("PaginaAjuda", "Configuracao_Gateways");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_Gateway(IDCliente);

            // lista de medicoes habilitadas
            List<int> ConfigMedList = ViewBag._ConfigMed;

            // le gateways
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            List<GatewaysDominio> listaGateways = gatewaysMetodos.ListarTodos(IDCliente, ConfigMedList);
            return View(listaGateways);
        }

        // GET: Configuracao Gateway - Editar
        public ActionResult Gateway_Editar(int IDGateway)
        {
            // tela de ajuda - gateways
            CookieStore.SalvaCookie_String("PaginaAjuda", "Configuracao_GatewaysEditar");

            // pagina anterior
            ViewBag.returnUrl = Request.UrlReferrer;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = new GatewaysDominio();

            // caso for edição, verifico se devo selecionar cliente e medição
            if (IDGateway > 0)
            {
                // le gateway
                gateway = gatewaysMetodos.ListarPorId(IDGateway);

                if (gateway != null)
                {
                    // IDCliente
                    int IDCliente = ViewBag._IDCliente;

                    // verifica se cliente diferente
                    if (IDCliente != gateway.IDCliente)
                    {
                        CookieStore.SalvaCookie_Int("_IDCliente", gateway.IDCliente);

                        // le contrato cliente
                        ClientesMetodos clienteMetodos = new ClientesMetodos();
                        ClientesDominio cli = clienteMetodos.ListarPorId(gateway.IDCliente);
                        CookieStore.SalvaCookie_Int("IDTipoContrato", cli.IDTipoContrato);

                        // le cookies
                        LeCookies_SmartEnergy();
                    }
                }
            }


            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // permissoes
            Permissoes();

            // alarmes de usuario
            List<DescricaoAlarmesDominio> AlmUsuarioList = null;

            // remotas
            List<GatewayRemotasDominio> RemotasList = null;

            // entradas digitais
            List<EntradasDigitaisDominio> EntradasDigitaisList = null;

            // saidas digitais
            List<SaidasDigitaisDominio> SaidasDigitaisList = null;

            // verifica se adicionando
            if (IDGateway == 0)
            {
                // zera gateway com default
                gateway.IDGateway = 0;
                gateway.IDCliente = ViewBag._IDCliente;
                gateway.Nome = "";
                gateway.EnderecoLocal = 1;
                gateway.ProjetoNro = "";
                gateway.IDEmpresa = -1;
                gateway.IDTipoGateway = -1;
                gateway.IDTipoDCE = -1;
                gateway.IDTipoTempo = -1;

                if (IDConsultor > 0)
                {
                    gateway.IDTipoGateway = TIPO_GATEWAY.SCDE;      // SCDE
                    gateway.IDTipoDCE = TIPO_DCE_GATEWAY.Ethernet;  // Ethernet
                    gateway.IDTipoTempo = TIPO_TEMPO_GATEWAY.SCDE;  // SCDE (Ignorar Falha de Upload)
                }

                gateway.AcessoRemoto = false;
                gateway.GatewayConectada_IoT = false;

                gateway.IDNumeroSerie = 0;
            }
            else
            {
                // le gateway
                gateway = gatewaysMetodos.ListarPorId(IDGateway);

                // le lista remotas
                if (gateway != null)
                {
                    // alarmes de usuario
                    DescricaoAlarmesMetodos AlmUsuarioMetodos = new DescricaoAlarmesMetodos();
                    AlmUsuarioList = AlmUsuarioMetodos.ListarPorIDGateway(gateway.IDGateway);

                    // remotas
                    GatewayRemotasMetodos remotasMetodos = new GatewayRemotasMetodos();
                    RemotasList = remotasMetodos.ListarTodasMedicoesIDGateway(gateway.IDGateway);

                    // entradas digitais
                    EntradasDigitaisMetodos entradasDigitaisMetodos = new EntradasDigitaisMetodos();
                    EntradasDigitaisList = entradasDigitaisMetodos.ListarPorIDGateway(gateway.IDGateway);

                    // saidas digitais
                    SaidasDigitaisMetodos saidasDigitaisMetodos = new SaidasDigitaisMetodos();
                    SaidasDigitaisList = saidasDigitaisMetodos.ListarPorIDGateway(gateway.IDGateway);

                    //
                    // Número de Série
                    //

                    // verifica se possui número de série associado
                    if (gateway.IDNumeroSerie > 0)
                    {
                        // número de série
                        NumeroSerieMetodos nsMetodos = new NumeroSerieMetodos();
                        NumeroSerieDominio ns = nsMetodos.ListarPorId(gateway.IDNumeroSerie);

                        if (ns != null)
                        {
                            // número de série
                            gateway.NS_Gateway = ns.NS_Gateway;
                        }
                    }
                }
            }

            // alarmes de usuario
            ViewBag.AlmUsuarioList = AlmUsuarioList;

            // remotas
            ViewBag.RemotasList = RemotasList;

            // entradas digitais
            ViewBag.EntradasDigitaisList = EntradasDigitaisList;

            // saidas digitais
            ViewBag.SaidasDigitaisList = SaidasDigitaisList;

            // prepara listas
            PreparaListas_Gateway(gateway.IDCliente, gateway.IDTipoGateway, gateway.IDNumeroSerie);

            // caso tiver cliente
            if (gateway.IDCliente > 0)
            {
                CookieStore.SalvaCookie_Int("_IDCliente", gateway.IDCliente);

                // le contrato cliente
                ClientesMetodos clienteMetodos = new ClientesMetodos();
                ClientesDominio cli = clienteMetodos.ListarPorId(gateway.IDCliente);
                CookieStore.SalvaCookie_Int("IDTipoContrato", cli.IDTipoContrato);

                // le cookies
                LeCookies_SmartEnergy();
            }

            return View(gateway);
        }

        // Listas utilizadas
        private void PreparaListas_Gateway(int IDCliente, int IDTipoGateway = 0, int IDNumeroSerie = 0)
        {
            // le tipos gateway
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposGateway = listatiposMetodos.ListarTodos("TipoGateway");
            ViewBag.listaTipoGateway = listatiposGateway;

            // le tipos DCE
            List<ListaTiposDominio> listatiposDCE = listatiposMetodos.ListarTodos("TipoDCE");
            ViewBag.listaTipoDCE = listatiposDCE;

            // le tipos Tempo
            List<ListaTiposDominio> listatiposTempo = listatiposMetodos.ListarTodos("TipoTempo", true, 0);
            ViewBag.listaTipoTempo = listatiposTempo;

            // le Empresas
            EmpresasMetodos empresaMetodos = new EmpresasMetodos();
            List<EmpresasDominio> empresas = empresaMetodos.ListarPorIDCliente(IDCliente);
            ViewBag.listaEmpresas = empresas;

            // le tipos Estado
            CidadeEstadoPaisMetodos cidadeMetodos = new CidadeEstadoPaisMetodos();
            List<EstadosDominio> listatiposEstado = cidadeMetodos.ListarTodosEstados();
            ViewBag.listaTipoEstado = listatiposEstado;

            // le tipos Cidade
            List<CidadesDominio> listatiposCidade = cidadeMetodos.ListarTodosCidades(0);
            ViewBag.listaTipoCidade = listatiposCidade;

            // listar gateways em estoque (número de série)
            NumeroSerieMetodos nsMetodos = new NumeroSerieMetodos();
            List<NumeroSerieDominio> listaGatewaysEstoque = nsMetodos.ListarEstoqueReformado_IDTipoGateway(IDTipoGateway, IDNumeroSerie);
            ViewBag.listaGatewaysEstoque = listaGatewaysEstoque;

            return;
        }

        // GET: Procurar remotas
        public JsonResult ProcurarRemotas(int IDGateway)
        {
            // remotas
            List<GatewayRemotasDominio> RemotasList = new List<GatewayRemotasDominio>();

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // le lista remotas
            if( gateway != null )
            {
                GatewayRemotasMetodos remotasMetodos = new GatewayRemotasMetodos();

                // busca todas remotas de 0 a 255
                for(int NumRemota=0; NumRemota<256; NumRemota++)
                {
                    // verifico ultimo evento na remota
                    EV_Metodos eventoMetodos = new EV_Metodos();
                    EV_Dominio evento = eventoMetodos.ListarMaisRecenteRemota(gateway.IDCliente, gateway.IDGateway, NumRemota);

                    // verifica se tem evento
                    if( evento != null )
                    {
                        // insere na lista de remotas
                        GatewayRemotasDominio gatewayRemota = new GatewayRemotasDominio();

                        gatewayRemota.IDGateway = gateway.IDGateway;
                        gatewayRemota.IDCliente = gateway.IDCliente;
                        gatewayRemota.Remota = NumRemota;
                        gatewayRemota.Status = (evento.Valor >= 65536) ? 1 : 0;
                        gatewayRemota.DataStatus = evento.DataHora;
                        gatewayRemota.DataStatusTexto = gatewayRemota.DataStatus.ToString("g");
                        gatewayRemota.OrigemStatus = 0;     // origem pelo log de eventos

                        // coloca na lista
                        RemotasList.Add(gatewayRemota);
                    }
                }
            }

            // remotas
            ViewBag.RemotasList = RemotasList;

            // retorna o valor em JSON
            return Json(RemotasList, JsonRequestBehavior.AllowGet);
        }


        // GET: Atualizar entradas digitais
        public ActionResult Atualizar_EntradasDigitais(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "ERRO",
                erro = "Gateway inexistente"
            };

            // le gateway
            GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewayMetodos.ListarPorId(IDGateway);

            // verifica se leu
            if (gateway != null)
            {
                if (gateway.IDGateway == IDGateway)
                {
                    // verifica se não é GATE X
                    if (gateway.IDTipoGateway != TIPO_GATEWAY.GATE_X_421 && gateway.IDTipoGateway != TIPO_GATEWAY.GATE_X_422)
                    {
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "Gateway não é SmartGate X"
                        };

                        // retorna status
                        return Json(returnedData, JsonRequestBehavior.AllowGet);
                    }

                    // le entradas da gateway

                    // comunicação MQTT
                    SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                    // verifica conexão com gateway
                    if (smartCom.Conectar())
                    {
                        // solicita lista de entradas e atualiza banco de dados
                        smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_ED_LISTA, SMCOM_TIPO_SOL.SOLICITA);
                    }

                    // entradas digitais
                    List<EntradasDigitaisDominio> listaEntradas = new List<EntradasDigitaisDominio>();

                    // preenche entradas digitais
                    Converte_SupervED(IDGateway, ref listaEntradas);

                    // salva EntradasDigitais
                    EntradasDigitaisMetodos entradasDigitaisMetodos = new EntradasDigitaisMetodos();

                    // exclui todas as entradas digitais desta gateway
                    entradasDigitaisMetodos.ExcluirTodosIDGateway(IDGateway);

                    // percorre lista e salva
                    foreach (EntradasDigitaisDominio ed in listaEntradas)
                    {
                        // preenche
                        ed.IDEntradaDigital = 0;
                        ed.IDCliente = gateway.IDCliente;
                        ed.IDGateway = gateway.IDGateway;

                        // salva
                        entradasDigitaisMetodos.Salvar(ed);
                    }

                    // retorno
                    returnedData = new
                    {
                        status = "OK",
                        erro = ""
                    };
                }
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        private void Converte_SupervED(int IDGateway, ref List<EntradasDigitaisDominio> listaEntradas)
        {
            // le entradas banco de dados
            GateX_ED_Lista_Metodos edMetodos = new GateX_ED_Lista_Metodos();
            List<GateX_ED_Lista_Dominio> entradas = edMetodos.ListarPorIDGateway(IDGateway);

            // atualiza
            foreach (GateX_ED_Lista_Dominio ed in entradas)
            {
                // verifica se programado
                if (ed.Programado)
                {
                    EntradasDigitaisDominio entrada = new EntradasDigitaisDominio();

                    entrada.IDCliente = 0;
                    entrada.IDGateway = ed.IDGateway;
                    entrada.Descricao = ed.Descricao;
                    entrada.Ativou = "Ativou";
                    entrada.Desativou = "Desativou";
                    entrada.NumEntradaGateway = ed.NumEntradaGateway;
                    entrada.status = STATUS_ED.DESCONHECIDO;

                    listaEntradas.Add(entrada);
                }
            }

            return;
        }


        // GET: Atualizar saídas digitais
        public ActionResult Atualizar_SaidasDigitais(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "ERRO",
                erro = "Gateway inexistente"
            };

            // le gateway
            GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewayMetodos.ListarPorId(IDGateway);

            // verifica se leu
            if (gateway != null)
            {
                if (gateway.IDGateway == IDGateway)
                {
                    // verifica se não é GATE X
                    if (gateway.IDTipoGateway != TIPO_GATEWAY.GATE_X_421 && gateway.IDTipoGateway != TIPO_GATEWAY.GATE_X_422)
                    {
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "Gateway não é SmartGate X"
                        };

                        // retorna status
                        return Json(returnedData, JsonRequestBehavior.AllowGet);
                    }

                    // le saidas da gateway

                    // comunicação MQTT
                    SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                    // verifica conexão com gateway
                    if (smartCom.Conectar())
                    {
                        // solicita lista de saidas e atualiza banco de dados
                        smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_SD_LISTA, SMCOM_TIPO_SOL.SOLICITA);
                    }

                    // saida digitais
                    List<SaidasDigitaisDominio> listaSaidas = new List<SaidasDigitaisDominio>();

                    // preenche saidas digitais
                    Converte_SupervSD(IDGateway, ref listaSaidas);

                    // salva SaidasDigitais
                    SaidasDigitaisMetodos saidasDigitaisMetodos = new SaidasDigitaisMetodos();

                    // exclui todas as saidas digitais desta gateway
                    saidasDigitaisMetodos.ExcluirTodosIDGateway(IDGateway);

                    // percorre lista e salva
                    foreach (SaidasDigitaisDominio sd in listaSaidas)
                    {
                        // preenche
                        sd.IDSaidaDigital = 0;
                        sd.IDCliente = gateway.IDCliente;
                        sd.IDGateway = gateway.IDGateway;

                        // salva
                        saidasDigitaisMetodos.Salvar(sd);
                    }

                    // retorno
                    returnedData = new
                    {
                        status = "OK",
                        erro = ""
                    };
                }
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        private void Converte_SupervSD(int IDGateway, ref List<SaidasDigitaisDominio> listaSaidas)
        {
            // le saida banco de dados
            GateX_SD_Lista_Metodos sdMetodos = new GateX_SD_Lista_Metodos();
            List<GateX_SD_Lista_Dominio> saidas = sdMetodos.ListarPorIDGateway(IDGateway);

            // atualiza
            foreach (GateX_SD_Lista_Dominio sd in saidas)
            {
                // verifica se programado
                if (sd.Programado)
                {
                    SaidasDigitaisDominio saida = new SaidasDigitaisDominio();

                    saida.IDCliente = 0;
                    saida.IDGateway = sd.IDGateway;
                    saida.Descricao = sd.Descricao;
                    saida.NumSaidaGateway = sd.NumSaidaGateway;
                    saida.status = STATUS_SD.DESCONHECIDO;

                    listaSaidas.Add(saida);
                }
            }

            return;
        }


        //
        // Números de Série
        //

        // GET: Obter Números de série do estoque
        public JsonResult ObterNumerosSerieEstoque(int IDTipoGateway, int IDNumeroSerie)
        {
            // listar gateways em estoque
            NumeroSerieMetodos nsMetodos = new NumeroSerieMetodos();
            List<NumeroSerieDominio> listaGatewaysEstoque = nsMetodos.ListarEstoqueReformado_IDTipoGateway(IDTipoGateway, IDNumeroSerie);

            // retorna o valor em JSON
            return Json(listaGatewaysEstoque, JsonRequestBehavior.AllowGet);
        }


        // POST: Configuracao Gateway - Salvar
        [HttpPost]
        public ActionResult Gateway_Salvar(GatewaysDominio gateway, List<DescricaoAlarmesDominio> gatewayAlmUsuario, List<GatewayRemotasDominio> gatewayRemotas, List<EntradasDigitaisDominio> gatewayED, List<SaidasDigitaisDominio> gatewaySD)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outra gateway com o mesmo nome
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            if( gatewaysMetodos.VerificarDuplicidade(gateway) )
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Gateway existente."
                };
            }
            else
            {
                // configuração da gateway existente
                GatewaysDominio gateway_existente = new GatewaysDominio();
                gateway_existente.IDGateway = 0;
                gateway_existente.IDNumeroSerie = 0;

                // verifica se é uma gateway existente
                if( gateway.IDGateway > 0)
                {
                    // lê configuração da gateway
                    gateway_existente = gatewaysMetodos.ListarPorId(gateway.IDGateway);
                }

                // salva gateway 
                gatewaysMetodos.Salvar(gateway);

                // verifica integridade
                int retorno = gatewaysMetodos.VerificarIntegridade(gateway);

                switch (retorno)
                {
                    case 1:
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "Gateway não adicionada."
                        };
                        break;

                    case 2:
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "IDGateway adicionada com valor alto.<br><br>FAVOR AVISAR O DESENVOLVIMENTO ANTES DE PROSSEGUIR !!!"
                        };
                        break;
                }

                // le empresa desta gateway
                EmpresasMetodos empresaMetodos = new EmpresasMetodos();
                EmpresasDominio empresa = empresaMetodos.ListarPorId(gateway.IDEmpresa);

                // le medicoes desta gateway
                MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                List<MedicoesDominio> medicoes = medicaoMetodos.ListarPorIDGateway(gateway.IDGateway, "");

                if( empresa != null && medicoes != null )
                {
                    // percorre medicoes e atualiza IDEstado
                    foreach(MedicoesDominio medicao in medicoes)
                    {
                        // atualiza medicao
                        medicao.IDEstado = empresa.IDEstado;
                        medicaoMetodos.Salvar(medicao);
                    }
                }

                // pego IDGateway novamente (pois pode ter sido insercao)
                GatewaysDominio gate = gatewaysMetodos.ListarPorNomeIDCliente(gateway.IDCliente, gateway.Nome);

                // verifica se salvou
                if (gate != null)
                {
                    // verifica se mudou número de série
                    NumeroSerieMetodos nsMetodos = new NumeroSerieMetodos();

                    if (gate.IDNumeroSerie != gateway_existente.IDNumeroSerie)
                    {
                        // coloca gateway anterior como análise da assistência técnica
                        nsMetodos.AlterarStatus(gateway_existente.IDNumeroSerie, TIPO_NS_STATUS.AT);
                    }

                    // coloca gateway atual no cliente
                    nsMetodos.AlterarStatus(gate.IDNumeroSerie, TIPO_NS_STATUS.CLIENTE, gate.IDCliente, gate.IDGateway);

                    // supervisão das medições - atualiza gateway
                    MedicoesSupervMetodos medSupervMetodos = new MedicoesSupervMetodos();
                    medSupervMetodos.Atualizar_Gateway(gate.IDGateway);

                    // evento 
                    UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();

                    if (gateway.IDGateway > 0)
                    {
                        usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, gate.IDGateway);
                    }
                    else
                    {
                        usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.GATEWAY, gate.IDGateway);
                    }
                }


                // salva AlmUsuario
                DescricaoAlarmesMetodos AlmUsuarioMetodos = new DescricaoAlarmesMetodos();

                if (gatewayAlmUsuario == null && gate != null)
                {
                    // exclui todos os alarmes de usuários desta gateway
                    AlmUsuarioMetodos.ExcluirGateway(gate.IDGateway);
                }

                if (gatewayAlmUsuario != null && gate != null)
                {
                    // exclui todos os alarmes de usuários desta gateway
                    AlmUsuarioMetodos.ExcluirGateway(gate.IDGateway);

                    // percorre lista e salva
                    foreach (DescricaoAlarmesDominio almUsuario in gatewayAlmUsuario)
                    {
                        // preenche
                        almUsuario.IDDescricao = 0;
                        almUsuario.IDCliente = gate.IDCliente;
                        almUsuario.IDGateway = gate.IDGateway;

                        // salva
                        AlmUsuarioMetodos.Salvar(almUsuario);
                    }
                }

                
                // salva gatewayRemotas
                GatewayRemotasMetodos gatewayRemotasMetodos = new GatewayRemotasMetodos();

                if (gatewayRemotas == null && gate != null)
                {
                    // exclui todos as remotas desta gateway
                    gatewayRemotasMetodos.ExcluirTodosIDGateway(gate.IDGateway);
                }

                if (gatewayRemotas != null && gate != null)
                {
                    // exclui todos as remotas desta gateway
                    gatewayRemotasMetodos.ExcluirTodosIDGateway(gate.IDGateway);

                    // percorre lista e salva
                    foreach (GatewayRemotasDominio gatewayRemota in gatewayRemotas)
                    {
                        // preenche
                        gatewayRemota.IDCliente = gate.IDCliente;
                        gatewayRemota.IDGateway = gate.IDGateway;
                        gatewayRemota.Status = -1;
                        gatewayRemota.DataStatus = new DateTime(2000, 1, 1, 0, 0, 0);
                        gatewayRemota.DataStatusTexto = "01/01/2000 00:00:00";
                        gatewayRemota.OrigemStatus = 0;     // origem pelo log de eventos

                        // salva
                        gatewayRemotasMetodos.Inserir(gatewayRemota);
                    }
                }


                // salva EntradasDigitais
                EntradasDigitaisMetodos entradasDigitaisMetodos = new EntradasDigitaisMetodos();

                if (gatewayED == null && gate != null)
                {
                    // exclui todas as entradas digitais desta gateway
                    entradasDigitaisMetodos.ExcluirTodosIDGateway(gate.IDGateway);
                }

                if (gatewayED != null && gate != null)
                {
                    // exclui todas as entradas digitais desta gateway
                    entradasDigitaisMetodos.ExcluirTodosIDGateway(gate.IDGateway);

                    // percorre lista e salva
                    foreach (EntradasDigitaisDominio edUsuario in gatewayED)
                    {
                        // preenche
                        edUsuario.IDEntradaDigital = 0;
                        edUsuario.IDCliente = gate.IDCliente;
                        edUsuario.IDGateway = gate.IDGateway;

                        // salva
                        entradasDigitaisMetodos.Salvar(edUsuario);
                    }
                }


                // salva SaidasDigitais
                SaidasDigitaisMetodos saidasDigitaisMetodos = new SaidasDigitaisMetodos();

                if (gatewaySD == null && gate != null)
                {
                    // exclui todas as saidas digitais desta gateway
                    saidasDigitaisMetodos.ExcluirTodosIDGateway(gate.IDGateway);
                }

                if (gatewaySD != null && gate != null)
                {
                    // exclui todas as saidas digitais desta gateway
                    saidasDigitaisMetodos.ExcluirTodosIDGateway(gate.IDGateway);

                    // percorre lista e salva
                    foreach (SaidasDigitaisDominio sdUsuario in gatewaySD)
                    {
                        // preenche
                        sdUsuario.IDSaidaDigital = 0;
                        sdUsuario.IDCliente = gate.IDCliente;
                        sdUsuario.IDGateway = gate.IDGateway;

                        // salva
                        saidasDigitaisMetodos.Salvar(sdUsuario);
                    }
                }

            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuracao Gateway - Excluir
        public ActionResult Gateway_Excluir(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existem medições utilizando esta gateway
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();

            if (medicaoMetodos.NumMedicoesGateway(IDGateway) > 0)
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Esta Gateway está sendo utilizada pelas Medições.<br><br>Verificar as Medições antes de excluir."
                };
            }
            else
            {
                // coloca gateway como análise da assistência técnica
                GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
                GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

                if (gateway != null)
                {
                    NumeroSerieMetodos nsMetodos = new NumeroSerieMetodos();
                    nsMetodos.AlterarStatus(gateway.IDNumeroSerie, TIPO_NS_STATUS.AT);
                }

                // apaga alarmes de usuário
                DescricaoAlarmesMetodos AlmUsuarioMetodos = new DescricaoAlarmesMetodos();
                AlmUsuarioMetodos.ExcluirGateway(IDGateway);

                // apaga remotas
                GatewayRemotasMetodos gatewayRemotasMetodos = new GatewayRemotasMetodos();
                gatewayRemotasMetodos.ExcluirTodosIDGateway(IDGateway);

                // apaga entradas digitais
                EntradasDigitaisMetodos entradasDigitaisMetodos = new EntradasDigitaisMetodos();
                entradasDigitaisMetodos.ExcluirTodosIDGateway(IDGateway);

                // apaga saidas digitais
                SaidasDigitaisMetodos saidasDigitaisMetodos = new SaidasDigitaisMetodos();
                saidasDigitaisMetodos.ExcluirTodosIDGateway(IDGateway);

                // apaga a gateway
                gatewaysMetodos.Excluir(IDGateway);

                // supervisão das medições - atualiza gateway
                MedicoesSupervMetodos medSupervMetodos = new MedicoesSupervMetodos();
                medSupervMetodos.Atualizar_Gateway(IDGateway);

                // evento 
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.EXCLUI_CONFIG, TABELA_CONFIG.GATEWAY, IDGateway);
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }
    }
}