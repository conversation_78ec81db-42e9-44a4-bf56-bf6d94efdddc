﻿using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ConfiguracaoController
    {
        // GET: Configuracao Grupo Medicoes
        public ActionResult GrupoMedicoes(int IDCliente)
        {
            // tela de ajuda - grupo de medicoes
            CookieStore.SalvaCookie_String("PaginaAjuda", "Configuracao_GruposMedicoes");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le grupos medicoes
            GrupoMedicoesMetodos grupoMetodos = new GrupoMedicoesMetodos();
            List<GrupoMedicoesDominio> listaGrupos = grupoMetodos.ListarPorIDCliente(IDCliente);

            return View(listaGrupos);
        }

        // GET: Configuracao GrupoMedicoes - Editar
        public ActionResult GrupoMedicoes_Editar(int IDGrupoMedicoes)
        {
            int IDCliente = 0;

            // tela de ajuda - grupo de medicoes
            CookieStore.SalvaCookie_String("PaginaAjuda", "Configuracao_GruposMedicoesEditar");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // verifica se adicionando
            GrupoMedicoesDominio grupo = new GrupoMedicoesDominio();
            if (IDGrupoMedicoes == 0)
            {
                // zera usuario com default
                grupo.IDGrupoMedicoes = 0;
                grupo.IDCliente = ViewBag._IDCliente;
                grupo.Nome = "";
                grupo.NumMedicoes = 0;

                // IDCliente
                IDCliente = ViewBag._IDCliente;
            }
            else
            {
                // le grupo de medicoes
                GrupoMedicoesMetodos grupoMetodos = new GrupoMedicoesMetodos();
                grupo = grupoMetodos.ListarPorId(IDGrupoMedicoes);

                // IDCliente
                IDCliente = grupo.IDCliente;
            }

            // lista de medicoes habilitadas do usuario
            List<int> ConfigMedicaoList_Usuario = new List<int>();
            ConfigMedicaoList_Usuario = ViewBag._ConfigMed;

            // verifica se existe lista de medicoes
            if (ConfigMedicaoList_Usuario != null)
            {
                if (ConfigMedicaoList_Usuario.Count == 0)
                {
                    ConfigMedicaoList_Usuario = null;
                }
            }

            // le medicoes do grupo
            GrupoMedicoesMedMetodos medGrupoMetodos = new GrupoMedicoesMedMetodos();
            List<GrupoMedicoesMedDominio> medGrupos = medGrupoMetodos.ListarPorIDGrupoMedicoes(grupo.IDGrupoMedicoes);

            // cria lista de medicoes deste grupo
            List<int> ConfigMedicaoList_Grupo = new List<int>();

            foreach (GrupoMedicoesMedDominio medGrupo in medGrupos)
            {
                ConfigMedicaoList_Grupo.Add(medGrupo.IDMedicao);
            }

            // le medicoes configuradas do grupo para lista
            CliGateGrupoUnidMedicoesMetodos medicoesMetodos = new CliGateGrupoUnidMedicoesMetodos();
            List<CliGateGrupoUnidMedicoesDominio> medicoes = new List<CliGateGrupoUnidMedicoesDominio>();

            List<CliGateGrupoUnidMedicoesDominio> medicoes_utilizadas = new List<CliGateGrupoUnidMedicoesDominio>();
            List<CliGateGrupoUnidMedicoesDominio> medicoes_nao_utilizadas = new List<CliGateGrupoUnidMedicoesDominio>();

            if (ConfigMedicaoList_Grupo != null)
            {
                // le medicoes de energia habilitadas para o usuario no cliente
                medicoes = medicoesMetodos.ListarPorIDCliente(IDCliente, ConfigMedicaoList_Usuario, 11);

                // seleciona medicoes 
                int contador;

                // percorre lista
                if( medicoes != null )
                {
                    for (contador = 0; contador < medicoes.Count(); contador++)
                    {
                        // verifica se medicao de energia ou utilidades
                        if (medicoes[contador].IDTipoMedicao == TIPO_MEDICAO.ENERGIA || medicoes[contador].IDTipoMedicao == TIPO_MEDICAO.ENERGIA_FORMULA ||
                            medicoes[contador].IDTipoMedicao == TIPO_MEDICAO.UTILIDADES || medicoes[contador].IDTipoMedicao == TIPO_MEDICAO.UTILIDADES_FORMULA ||
                            medicoes[contador].IDTipoMedicao == TIPO_MEDICAO.CICLOMETRO )
                        {
                            // verifica se medicao esta habilitada para o grupo
                            if (ConfigMedicaoList_Grupo.Contains(medicoes[contador].IDMedicao))
                            {
                                // copia medicao na lista de utilizadas
                                medicoes_utilizadas.Add(medicoes[contador]);
                            }
                            else
                            {
                                // copia medicao na lista de nao utilizadas
                                medicoes_nao_utilizadas.Add(medicoes[contador]);
                            }
                        }
                    }
                }
            }
            ViewBag.Medicoes = medicoes_utilizadas;
            ViewBag.Medicoes2 = medicoes_nao_utilizadas;
            ViewBag.medGrupos = medGrupos;

            return View(grupo);
        }

        // POST: Configuracao GrupoMedicoes - Salvar
        [HttpPost]
        public ActionResult GrupoMedicoes_Salvar(GrupoMedicoesDominio grupo, List<GrupoMedicoesMedDominio> medGrupos)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro grupo com o mesmo nome
            GrupoMedicoesMetodos grupoMetodos = new GrupoMedicoesMetodos();
            if (grupoMetodos.VerificarDuplicidade(grupo))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Grupo existente."
                };
            }
            else
            {
                // salva rgrupo de medicoes
                grupoMetodos.Salvar(grupo);

                // evento 
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();

                if (grupo.IDGrupoMedicoes > 0)
                {
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GRUPO_MEDICOES, grupo.IDGrupoMedicoes);
                }
                else
                {
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.GRUPO_MEDICOES, grupo.IDGrupoMedicoes);
                }

                // pego IDGrupoMedicoes novamente (pois pode ter sido insercao)
                GrupoMedicoesDominio gru = grupoMetodos.ListarPorNome(grupo.IDCliente, grupo.Nome);

                // salva medGrupo
                if (gru != null)
                {
                    GrupoMedicoesMedMetodos medGruposMetodos = new GrupoMedicoesMedMetodos();

                    // exclui todos as medicoes deste grupo
                    medGruposMetodos.Excluir(gru.IDGrupoMedicoes);

                    // salva medGrupo
                    if (medGrupos != null)
                    {
                        // percorre lista e salva
                        foreach (GrupoMedicoesMedDominio medGrupo in medGrupos)
                        {
                            // preenche campos que faltam
                            medGrupo.IDGrupoMedicoes = gru.IDGrupoMedicoes;

                            // salva
                            medGruposMetodos.Salvar(medGrupo);
                        }
                    }
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuracao GrupoMedicoes - Excluir
        public ActionResult GrupoMedicoes_Excluir(int IDGrupoMedicoes)
        {
            // apaga o grupo
            GrupoMedicoesMetodos grupoMetodos = new GrupoMedicoesMetodos();
            grupoMetodos.Excluir(IDGrupoMedicoes);

            GrupoMedicoesMedMetodos medGruposMetodos = new GrupoMedicoesMedMetodos();
            medGruposMetodos.Excluir(IDGrupoMedicoes);

            // apaga a distribuição GruposMed
            DistribuicaoConsumoGruposMedMetodos distribuicaoGruposMetodos = new DistribuicaoConsumoGruposMedMetodos();
            distribuicaoGruposMetodos.ExcluirPorGrupoMedicao(IDGrupoMedicoes);

            // evento 
            UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.EXCLUI_CONFIG, TABELA_CONFIG.GRUPO_MEDICOES, IDGrupoMedicoes);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}