﻿using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ConfiguracaoController
    {
        // GET: Configuracao Gateways Producao
        public ActionResult GatewaysProducao(int IDCliente)
        {
            // tela de ajuda - gateways
            CookieStore.SalvaCookie_String("PaginaAjuda", "Configuracao_Gateways");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_Gateway(IDCliente);

            // le clientes
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clientesMetodos.ListarTodos();
            ViewBag.listaClientes = listaClientes;

            // le gateways StartUp Não Realizado (tinha dedicado IDCliente 2 para producao)
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            List<GatewaysDominio> listaGateways = gatewaysMetodos.ListarPorStartUpIDCliente(IDCliente);

            // percorre gateways e descobre numero de medicoes associadas
            foreach (GatewaysDominio gateway in listaGateways)
            {
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();

                // numero de medicoes de energia com este cliente
                int NumMedicoes = medicoesMetodos.NumMedicoesGateway(gateway.IDGateway);
                gateway.NumMedicoes = NumMedicoes;
            }

            return View(listaGateways);
        }

        // GET: Configuracao Gateway - Novo
        public ActionResult GatewayProducao_Novo()
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // nova gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = new GatewaysDominio();

            // zera gateway com default (ID 2)
            gateway.Nome = "NovaGateway_000002";
            gateway.IDGateway = 0;
            gateway.IDCliente = 2;
            gateway.IDTipoGateway = TIPO_GATEWAY.GATE_M;                                // gate M
            gateway.IDEmpresa = 0;
            gateway.IDTipoDCE = TIPO_DCE_GATEWAY.ModemGPRS;                             // modem GPRS
            gateway.IDTipoTempo = TIPO_TEMPO_GATEWAY.Ignorar_StartUpNaoRealizado;       // startup nao realizado

            // salva gateway 
            gatewaysMetodos.Salvar(gateway);

            // pego IDGateway da nova gateway
            GatewaysDominio gate = gatewaysMetodos.ListarPorNome(gateway.Nome);

            if( gate.IDCliente != 2 )
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Gateway não foi criada."
                };
            }
            else
            {
                // altera o nome da gateway
                gate.Nome = string.Format("Gateway {0:000000}", gate.IDGateway);

                // salva gateway 
                gatewaysMetodos.Salvar(gate);

                // evento nova gateway
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.GATEWAY, gate.IDGateway);

                // cria medicao
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                MedicoesDominio medicao = new MedicoesDominio();

                // zera medicao com default
                medicao.IDCliente = 2;

                medicao.Nome = gate.Nome;

                medicao.IDUnidade = 1;
                medicao.IDTipoMedicao = 0;              // Energia Eletrica
                medicao.IDDeslocamento = 0;             // 00:00
                medicao.IDCicloMes = 1;                 // Mes Civil

                medicao.IDGateway = gate.IDGateway;
                medicao.NumMedGateway = 0;

                medicao.IDGateway_Fechamentos = 0;      // utilizar a própria gateway para gerar os fechamentos

                medicao.IDAgenteDistribuidora = 3;        // ELETROPAULO
                medicao.IDEstruturaTarifaria = 0;       // THS Azul
                medicao.IDTipoSubgrupo = 0;             // A1
                medicao.IDSubgrupo = 0;                 // THS Azul A1
                medicao.IDSubSistema = 0;               // Sudeste/Centro-Oeste
                medicao.IDRegraDem = 4;                 // res. 456
                medicao.IDRegraReativo = 0;             // com medicao apropriada
                medicao.IDRegraICMS = 1;                // sobre demanda registrada
                medicao.IDCalculoICMS = 0;              // por dentro

                medicao.InicioP = "18:00";
                medicao.FimP = "21:00";
                medicao.DomP = false; medicao.SegP = true; medicao.TerP = true; medicao.QuaP = true; medicao.QuiP = true; medicao.SexP = true; medicao.SabP = false; medicao.FerP = false;
                medicao.InicioPC = "00:00";
                medicao.FimPC = "06:00";
                medicao.DomPC = true; medicao.SegPC = true; medicao.TerPC = true; medicao.QuaPC = true; medicao.QuiPC = true; medicao.SexPC = true; medicao.SabPC = true; medicao.FerPC = true;

                medicao.ToleranciaDemP = 0;
                medicao.ToleranciaDemFP = 0;

                medicao.IDIcone = 0;

                medicao.Dem_P_pcH = 0.0;
                medicao.Dem_F_pcH = 0.0;
                medicao.Dem_P_pcL = 0.0;
                medicao.Dem_F_pcL = 0.0;
                medicao.FP_LimInd = 0.0;
                medicao.FP_LimCap = 0.0;
                medicao.FP_LimCap = 0;
                medicao.CnPjP_pcH = 0.0;
                medicao.CnPjF_pcH = 0.0;
                medicao.CnPjP_pcL = 0.0;
                medicao.CnPjF_pcL = 0.0;

                medicao.IDEstado = 26;

                medicao.IntervaloGrandeza = 0;
                medicao.FEGrandeza = 65535;

                // salva medicao 
                medicoesMetodos.Salvar(medicao);

                // evento nova medicao
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.MEDICAO, medicao.IDMedicao);
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        // GET: Configuracao Gateway - Mover
        public ActionResult GatewayProducao_Mover(int IDGateway, int IDClienteDestino)
        {
            // retorno
            string status = "OK";
            string erro = "";

            // gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            if( gateway == null )
            {
                // erro
                status = "ERRO";
                erro = "Gateway inexistente";

                goto FIM;
            }

            // origem
            int IDClienteOrigem = gateway.IDCliente;

            //
            // EMPRESA
            //

            // ID Empresa
            int IDEmpresa = 0;

            // empresa da gateway origem
            EmpresasMetodos empresasMetodos = new EmpresasMetodos();
            EmpresasDominio empresaOrigem = empresasMetodos.ListarPorId(gateway.IDEmpresa);

            if (empresaOrigem != null)
            {
                // verifica se no cliente destino tem uma empresa com a mesma razão social
                EmpresasDominio empresaDestino = empresasMetodos.ListarPorIDClienteNome(IDClienteDestino, empresaOrigem.RazaoSocial);

                if (empresaDestino == null)
                {
                    // NAO TEM a mesma empresa no destino, crio empresa no cliente destino
                    EmpresasDominio empresaNovo = new EmpresasDominio();
                    empresaNovo = empresaOrigem;

                    empresaNovo.IDCliente = IDClienteDestino;
                    empresaNovo.IDEmpresa = 0;

                    empresasMetodos.Salvar(empresaNovo);

                    // leio a empresa criada para verificar o ID
                    empresaDestino = empresasMetodos.ListarPorIDClienteNome(IDClienteDestino, empresaOrigem.RazaoSocial);

                    if (empresaDestino != null)
                    {
                        // copio ID Empresa
                        IDEmpresa = empresaDestino.IDEmpresa;
                    }
                }
                else
                {
                    // TEM a mesma empresa no destino, copio ID
                    IDEmpresa = empresaDestino.IDEmpresa;
                }
            }

            //
            // GATEWAY
            //

            // altera gateway
            gateway.IDCliente = IDClienteDestino;
            gateway.IDEmpresa = IDEmpresa;
            gateway.IDTipoTempo = TIPO_TEMPO_GATEWAY.Envio_diariamente;   // diariamente 

            gatewaysMetodos.Salvar(gateway);

            // move remotas da gateway
            GatewayRemotasMetodos gatewayRemotasMetodos = new GatewayRemotasMetodos();
            gatewayRemotasMetodos.AlterarCliente(IDClienteOrigem, IDClienteDestino);

            // move EV_000000
            EV_Metodos evMetodos = new EV_Metodos();
            evMetodos.MoverTabela(IDClienteOrigem, IDGateway, IDClienteDestino);

            // move SUP_000000
            SUP_Metodos supMetodos = new SUP_Metodos();
            supMetodos.MoverTabela(IDClienteOrigem, IDGateway, IDClienteDestino);

            // apaga FECHAMENTOS
            FechamentosMetodos fechamentoMetodos = new FechamentosMetodos();
            fechamentoMetodos.ExcluirTodosIDGateway(IDGateway);

            //
            // MEDICOES
            //

            // medicoes da gateway
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicoesMetodos.ListarPorIDGateway(IDGateway, "");

            if( medicoes != null )
            {
                // percorre medicoes
                foreach (MedicoesDominio medicao in medicoes)
                {
                    //
                    // MEDICAO
                    //

                    // altera medicao - IDCliente
                    medicao.IDCliente = IDClienteDestino;
                    medicoesMetodos.Salvar(medicao);

                    // verifica tipo da medicao EN
                    if (medicao.IDTipoMedicao == 0)
                    {
                        // move EN_000000
                        EN_Metodos enMetodos = new EN_Metodos();
                        enMetodos.MoverTabela(IDClienteOrigem, medicao.IDMedicao, IDClienteDestino);

                        // move EN_K_000000
                        EN_K_Metodos enKMetodos = new EN_K_Metodos();
                        enKMetodos.MoverTabela(IDClienteOrigem, medicao.IDMedicao, IDClienteDestino);

                        // move SUP_EN_000000
                        SUP_EN_Metodos supEnMetodos = new SUP_EN_Metodos();
                        supEnMetodos.MoverTabela(IDClienteOrigem, medicao.IDMedicao, IDClienteDestino);
                    }

                    // verifica tipo da medicao GG
                    if (medicao.IDTipoMedicao == 2 || medicao.IDTipoMedicao == 3 || medicao.IDTipoMedicao == 4)
                    {
                        // move GG_000000
                        GG_Metodos ggMetodos = new GG_Metodos();
                        ggMetodos.MoverTabela(IDClienteOrigem, medicao.IDMedicao, IDClienteDestino);

                        // move SUP_GG_000000
                        SUP_GG_Metodos supGgMetodos = new SUP_GG_Metodos();
                        supGgMetodos.MoverTabela(IDClienteOrigem, medicao.IDMedicao, IDClienteDestino);
                    }

                    //
                    // GRUPO UNIDADE E UNIDADE
                    //

                    // ID Grupo Unidade
                    int IDGrupoUnidades = 0;

                    // ID Unidade
                    int IDUnidade = 0;

                    // unidade da medicao origem
                    UnidadesMetodos unidadesMetodos = new UnidadesMetodos();
                    UnidadesDominio unidadeOrigem = unidadesMetodos.ListarPorId(IDClienteOrigem, medicao.IDUnidade);

                    if( unidadeOrigem != null )
                    {
                        // grupo unidade da unidade origem
                        GrupoUnidadesMetodos grupoUnidadeMetodos = new GrupoUnidadesMetodos();
                        GrupoUnidadesDominio grupoUnidadeOrigem = grupoUnidadeMetodos.ListarPorId(IDClienteOrigem, unidadeOrigem.IDGrupoUnidades);

                        // ID Grupo Unidade
                        IDGrupoUnidades = unidadeOrigem.IDGrupoUnidades;

                        if (grupoUnidadeOrigem != null)
                        {
                            // verifica se no cliente destino tem um grupo unidade com o mesmo nome
                            GrupoUnidadesDominio grupoUnidadeDestino = grupoUnidadeMetodos.ListarPorIDClienteNome(IDClienteDestino, grupoUnidadeOrigem.Nome);

                            if (grupoUnidadeDestino == null)
                            {
                                // NAO TEM o mesmo grupo no destino, crio grupo unidade no cliente destino
                                GrupoUnidadesDominio grupoUnidadeNovo = new GrupoUnidadesDominio();
                                grupoUnidadeNovo.IDCliente = IDClienteDestino;
                                grupoUnidadeNovo.IDGrupoUnidades = 0;
                                grupoUnidadeNovo.Nome = grupoUnidadeOrigem.Nome;
                                grupoUnidadeMetodos.Salvar(grupoUnidadeNovo);

                                // leio o grupo unidade criado para verificar o ID
                                grupoUnidadeDestino = grupoUnidadeMetodos.ListarPorIDClienteNome(IDClienteDestino, grupoUnidadeOrigem.Nome);

                                if( grupoUnidadeDestino != null )
                                {
                                    // copio ID Grupo Unidade
                                    IDGrupoUnidades = grupoUnidadeDestino.IDGrupoUnidades;
                                }
                            }
                            else
                            {
                                // TEM o mesmo grupo no destino, copio ID Grupo Unidade
                                IDGrupoUnidades = grupoUnidadeDestino.IDGrupoUnidades;
                            }
                        }

                        // ID Unidade
                        IDUnidade = unidadeOrigem.IDUnidade;

                        // verifica se no cliente destino tem um unidade com o mesmo nome
                        UnidadesDominio unidadeDestino = unidadesMetodos.ListarPorIDClienteNome(IDClienteDestino, unidadeOrigem.Nome);

                        if (unidadeDestino == null)
                        {
                            // NAO TEM a mesma unidade no destino, crio unidade no cliente destino
                            UnidadesDominio unidadeNovo = new UnidadesDominio();
                            unidadeNovo.IDCliente = IDClienteDestino;
                            unidadeNovo.IDUnidade = 0;
                            unidadeNovo.IDGrupoUnidades = IDGrupoUnidades;
                            unidadeNovo.Nome = unidadeOrigem.Nome;
                            unidadesMetodos.Salvar(unidadeNovo);

                            // leio a unidade criado para verificar o ID
                            unidadeDestino = unidadesMetodos.ListarPorIDClienteNome(IDClienteDestino, unidadeOrigem.Nome);

                            if (unidadeDestino != null)
                            {
                                // copio ID Unidade
                                IDUnidade = unidadeDestino.IDUnidade;
                            }
                        }
                        else
                        {
                            // TEM o mesmo grupo no destino, copio ID
                            IDUnidade = unidadeDestino.IDUnidade;
                        }

                        // verifica se encontrou grupo de unidade e unidade
                        if (IDGrupoUnidades != 0 && IDUnidade != 0)
                        {
                            // altera medicao - IDUnidade
                            medicao.IDUnidade = IDUnidade;
                            medicoesMetodos.Salvar(medicao);
                        }


                        // analiso unidade na origem para verificar se ficou orfã (sem medicoes utilizando a unidade)
                        List<MedicoesDominio> meds = medicoesMetodos.ListarPorIDUnidade(IDClienteOrigem, unidadeOrigem.IDUnidade);

                        bool ficou_orfa = true;

                        if (meds != null)
                        {
                            // verifico se tem mais medicoes usando a unidade
                            if (meds.Count() > 0)
                            {
                                // tem mais medicoes usando a unidade
                                ficou_orfa = false;
                            }
                        }

                        if (ficou_orfa)
                        {
                            // apaga unidade orfã
                            unidadesMetodos.Excluir(IDClienteOrigem, unidadeOrigem.IDUnidade);
                        }

                        // analiso grupo unidade na origem para verificar se ficou orfão (sem unidades utilizando o grupo)
                        List<UnidadesDominio> unids = unidadesMetodos.ListarPorIDGrupoUnidades(IDClienteOrigem, unidadeOrigem.IDGrupoUnidades);

                        ficou_orfa = true;

                        if (unids != null)
                        {
                            // verifico se tem mais unidade usando o grupo
                            if (unids.Count() > 0)
                            {
                                // tem mais unidades usando o grupo
                                ficou_orfa = false;
                            }
                        }

                        if (ficou_orfa)
                        {
                            // apago grupo unidade orfão
                            grupoUnidadeMetodos.Excluir(IDClienteOrigem, unidadeOrigem.IDGrupoUnidades);
                        }
                    }

                    // historico de contrato - altera cliente
                    HistoricoContratosMetodos histContratoMetodos = new HistoricoContratosMetodos();
                    histContratoMetodos.AlterarCliente(medicao.IDMedicao, IDClienteDestino);

                    // historico de ICMS
                    HistoricoICMSMetodos histICMSMetodos = new HistoricoICMSMetodos();
                    histICMSMetodos.AlterarCliente(medicao.IDMedicao, IDClienteDestino);

                    //
                    // APAGAR RELACIONAMENTOS DESNECESSARIOS PARA PRODUCAO
                    //

                    // apaga DASHBOARD
                    DashboardMetodos dashboardMetodos = new DashboardMetodos();
                    dashboardMetodos.ExcluirTodosIDMedicao(medicao.IDMedicao);

                    // apaga HISTORICO RURAL
                    HistoricoRuralMetodos ruralMetodos = new HistoricoRuralMetodos();
                    ruralMetodos.ExcluirTodosIDMedicao(medicao.IDMedicao);

                    // apaga HISTORICO MERCADO LIVRE
                    HistoricoMercadoLivreMetodos mercadolivreMetodos = new HistoricoMercadoLivreMetodos();
                    mercadolivreMetodos.ExcluirTodosIDReferencia(medicao.IDMedicao, 0);

                    // apaga FATURA OUTROS
                    FaturaOutrosMetodos outrosMetodos = new FaturaOutrosMetodos();
                    outrosMetodos.ExcluirTodosIDMedicao(medicao.IDMedicao);

                    // apaga METAS CONSUMO
                    MetasConsumoMetodos metasMetodos = new MetasConsumoMetodos();
                    metasMetodos.ExcluirTodosIDMedicao(medicao.IDMedicao);

                    // apaga OBSERVACAO MEDICOES
                    ObservacaoMedicaoMetodos observacaoMetodos = new ObservacaoMedicaoMetodos();
                    observacaoMetodos.ExcluirTodosIDMedicao(medicao.IDMedicao);

                    // apaga USUARIO MEDICOES
                    UsuarioMedicaoMetodos usuarioMedicaoMetodos = new UsuarioMedicaoMetodos();
                    usuarioMedicaoMetodos.ExcluirTodosIDMedicao(medicao.IDMedicao);

                    // atualiza USUARIO
                    UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
                    List<UsuarioDominio> usuarios = usuarioMetodos.ListarPorIDMedicao(medicao.IDCliente, medicao.IDMedicao);

                    if (usuarios != null)
                    {
                        foreach (UsuarioDominio usuario in usuarios)
                        {
                            // uso o UsuariosMedicoes para atualizar o campo CONFIGMED / CONFIGMEDEMAIL e flags de recebimento de email em Usuarios
                            if (isUser.isCliente(usuario.IDTipoAcesso))
                            {
                                usuarioMedicaoMetodos.AtualizarUsuariosMedicoes(usuario.IDUsuario, true);

                                // verifica se usuario se nao tem mais medicao
                                UsuarioDominio user = usuarioMetodos.ListarPorId(usuario.IDUsuario);

                                if (user != null)
                                {
                                    if (user.ConfigMed.Count() == 0 && isUser.isCliente(user.IDTipoAcesso))
                                    {
                                        // exclui usuario
                                        usuarioMetodos.Excluir(user.IDUsuario);
                                    }
                                }
                            }
                        }
                    }

                }
            }
        
FIM:

            // retorno
            var returnedData = new
            {
                status = status,
                erro = erro
            };

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

    }
}