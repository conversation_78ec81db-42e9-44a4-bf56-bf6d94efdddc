﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Configuração Driver e Sincronismo
        public ActionResult GateE_DriverSincronismo(int IDGateway, int Origem = 0)
        {
            // tela de ajuda
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // receber configuração
            GateE_DriverSincronismo_Dominio prg = GateE_DriverSincronismo_Receber(IDGateway, Origem);

            // prepara listas
            GateE_DriverSincronismo_PreparaListas(IDGateway);

            // retorna configuração
            return View(prg);
        }

        // Configuração Driver Sincronismo - Receber
        private GateE_DriverSincronismo_Dominio GateE_DriverSincronismo_Receber(int IDGateway, int Origem)
        {
            // configuração
            GateE_DriverSincronismo_Metodos prgMetodos = new GateE_DriverSincronismo_Metodos();
            GateE_DriverSincronismo_Dominio prg = new GateE_DriverSincronismo_Dominio();

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // status
            bool solicitaProg = true;

            // verifica Origem
            // 0: ler da gateway
            // 1: ler do banco de dados atual
            // 2: ler do banco de dados editando
            if (Origem == 0)
            {
                // status
                solicitaProg = false;

                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // solicita Rede IoT
                    if (solicitaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_DRIVER_SINCRONISMO, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // copia configuração da gateway para 'editando'
                        // caso ainda não exista configuração 'atual', será criada 
                        prg = prgMetodos.CopiarParaEditando(IDGateway, smartCom.gateE.DriverSincronismo);
                    }
                }

                // verifica se não solicitou
                if (!solicitaProg)
                {
                    // copia configuração 'atual' para 'editando'
                    prg = prgMetodos.CopiarParaEditando(IDGateway);
                }
            }
            else if (Origem == 1)
            {
                // copia configuração 'atual' para 'editando'
                prg = prgMetodos.CopiarParaEditando(IDGateway);
            }
            else
            {
                // configuração em 'editando'
                prg = prgMetodos.ListarPorIDGateway(IDGateway, STATUS_CFG.EDITANDO);
            }

            // arredonda
            prg.icCodiKe = Math.Round(prg.icCodiKe, 5);

            // copia status
            ViewBag.SolicitaProg = solicitaProg;

            // retorna configuração
            return (prg);
        }

        // prepara listas
        private void GateE_DriverSincronismo_PreparaListas(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // le tipos interface driver
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposInterfaceDriver = listatiposMetodos.ListarTodos("TipoInterface_Driver", false);
            ViewBag.listatiposInterfaceDriver = listatiposInterfaceDriver;

            // le tipos fonte sincronismo
            List<ListaTiposDominio> listatiposFonteSincronismo = listatiposMetodos.ListarTodos("TipoFonte_Sincronismo", false);
            ViewBag.listatiposFonteSincronismo = listatiposFonteSincronismo;

            // le tipos driver LoRa
            List<ListaTiposDominio> listatiposDriverLoRa = listatiposMetodos.ListarTodos("TipoDriver_LoRa", false);
            ViewBag.listatiposDriverLoRa = listatiposDriverLoRa;

            // le tipos formato RS485
            List<ListaTiposDominio> listatiposFormatoRS485 = listatiposMetodos.ListarTodos("TipoFormato_RS485", false);
            ViewBag.listatiposFormatoRS485 = listatiposFormatoRS485;

            // le tipos velocidade RS485
            List<ListaTiposDominio> listatiposVelocidadeRS485 = listatiposMetodos.ListarTodos("TipoVelocidade_RS485", false);
            ViewBag.listatiposVelocidadeRS485 = listatiposVelocidadeRS485;

            // le tipos tentativas RS485
            List<ListaTiposDominio> listatiposTentativasRS485 = listatiposMetodos.ListarTodos("TipoTentativas_RS485", false);
            ViewBag.listatiposTentativasRS485 = listatiposTentativasRS485;

            return;
        }

        // POST: Configuração Rede IoT - Enviar
        [HttpPost]
        public ActionResult GateE_DriverSincronismo_Enviar(GateE_DriverSincronismo_Dominio prg)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(prg.IDGateway);

            // arredonda
            prg.icCodiKe = Math.Round(prg.icCodiKe, 5);

            // configuração atual
            prg.Status_Cfg = STATUS_CFG.ATUAL;

            // copia configuração 'editando' para 'atual'
            GateE_DriverSincronismo_Metodos prgMetodos = new GateE_DriverSincronismo_Metodos();
            prgMetodos.Salvar(prg);

            // variavel de retorno
            bool enviaProg = false;

            // verifica
            if (gateway != null)
            {
                // comunicação MQTT
                SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // copia data hora no envio
                    smartCom.gateE.DriverSincronismo = prg;

                    // envia configuração
                    enviaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.PROGR_DRIVER_SINCRONISMO, SMCOM_TIPO_SOL.ENVIA);
                }
            }

            // verifica retorno
            if (enviaProg)
            {
                // excluir editando
                prgMetodos.ExcluirEditando(prg.IDGateway);

                // histórico de configuração
                HistoricoConfiguracoesDominio hist = new HistoricoConfiguracoesDominio();
                hist.IDGateway = prg.IDGateway;
                hist.NomeConfiguracao = "GateE_DriverSinctronismo";
                hist.Envio = "MQTT";
                hist.Atualizacao = DateTime.Now;
                hist.Versao = GateE_Versao_String(prg.IDGateway);
                hist.IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");

                // insere histórico de configuração
                HistoricoConfiguracoesMetodos histMetodos = new HistoricoConfiguracoesMetodos();
                histMetodos.Inserir(hist);

                // caso OK insiro evento
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GATEWAY, prg.IDGateway);
            }
            else
            {
                // erro
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Falha no envio da configuração!"
                };
            }

            // retorna status
            return Json(returnedData);
        }
    }
}