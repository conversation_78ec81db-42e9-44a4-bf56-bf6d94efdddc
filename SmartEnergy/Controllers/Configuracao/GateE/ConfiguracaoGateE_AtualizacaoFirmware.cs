﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Atualização do firmware
        public ActionResult GateE_AtualizacaoFirmware(int IDGateway)
        {
            // tela de ajuda
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permisso<PERSON>();

            // Verifica update em andamento e recebe versão
            GateE_Versao_Dominio versao = GateE_AtualizacaoFirmware_VerificaUpdateAndamento(IDGateway);

            // prepara listas
            GateE_AtualizacaoFirmware_PreparaListas(IDGateway, versao);

            // retorna
            return View(versao);
        }


        // Verifica update em andamento e recebe versão
        private GateE_Versao_Dominio GateE_AtualizacaoFirmware_VerificaUpdateAndamento(int IDGateway)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // update em andamento
            bool updateProgramada = false;
            bool updateAndamento = false;
            bool updateAndamento_Gateway = false;
            bool updateAndamento_SmartMQTT = false;
            bool updateAndamento_Usuario = false;
            int updateAndamento_IDUpdate = 0;

            // status
            bool solicitaProg = false;

            // versão
            GateE_Versao_Dominio versao = new GateE_Versao_Dominio();
            versao.Modelo = "SmartGate E";
            versao.Versao = "---";
            versao.DriverRemota = "---";


            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita versão
                if (solicitaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.VERSAO, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // copia versão recebida
                    versao = smartCom.gateE.Versao;

                    // controle da configuração
                    versao.Status_Cfg = STATUS_CFG.ATUAL;

                    // salva configuração
                    GateE_Versao_Metodos prgMetodos = new GateE_Versao_Metodos();
                    prgMetodos.Salvar(IDGateway, versao);


                    //
                    // Verifica se existe atualização em andamento na gateway
                    //

                    // solicita info geral
                    if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.INFO_GERAL, SMCOM_TIPO_SOL.SOLICITA))
                    {
                        // status atualização
                        updateAndamento_Gateway = (smartCom.gateE.InfoGeral.OTA_try > 0) ? true : false;
                    }


                    //
                    // Verifica se existe atualização em andamento da gateway no SmartMQTT
                    //

                    // verifica se existe atualização em andamento desta gateway
                    ListaUpdatesMetodos updateMetodos = new ListaUpdatesMetodos();
                    ListaUpdatesDominio update = updateMetodos.AtualizacaoEmAndamento(IDGateway);

                    // verifica se existe atualização em andamento
                    if (update.IDUpdate > 0)
                    {
                        // se não existe atualização na gateway em andamento e se atualização não é programada, coloco esta atualização em erro
                        if (!updateAndamento_Gateway && update.Inicio < DateTime.Now)
                        {
                            updateMetodos.AlterarStatusIDUpdate(update.IDUpdate, TIPO_UPDATE_STATUS.Atualizacao_Erro);
                        }
                        else
                        {
                            // verifica atualização é programada
                            if (update.Inicio > DateTime.Now)
                            {
                                // update programada
                                updateAndamento_IDUpdate = update.IDUpdate;
                                updateProgramada = true;
                            }
                            else
                            {
                                // update em andamento
                                updateAndamento_IDUpdate = update.IDUpdate;
                                updateAndamento_SmartMQTT = true;

                                // IDUsuario
                                int IDUsuario = ViewBag._IDUsuario;

                                // verifica se a atualização é do usuário atual
                                if (IDUsuario == update.IDUsuario)
                                {
                                    // atualização atual é deste usuário, portanto ele poderá abortar se quiser
                                    updateAndamento_Usuario = true;
                                }
                            }
                        }
                    }

                    // atualização em andamento na gateway ou no SmartMQTT
                    updateAndamento = (updateAndamento_Gateway || updateAndamento_SmartMQTT);
                }
            }

            // copia novo status da solicitacao para a view
            ViewBag.SolicitaProg = solicitaProg;

            // update em andamento
            ViewBag.updateProgramada = updateProgramada;
            ViewBag.updateAndamento = updateAndamento;
            ViewBag.updateAndamento_Gateway = updateAndamento_Gateway;
            ViewBag.updateAndamento_SmartMQTT = updateAndamento_SmartMQTT;
            ViewBag.updateAndamento_Usuario = updateAndamento_Usuario;
            ViewBag.updateAndamento_IDUpdate = updateAndamento_IDUpdate;

            // retorna 
            return (versao);
        }

        // prepara listas
        private void GateE_AtualizacaoFirmware_PreparaListas(int IDGateway, GateE_Versao_Dominio versao)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // le firmwares excluindo da lista a versão atual da gateway
            FirmwaresMetodos firmwaresMetodos = new FirmwaresMetodos();
            List<FirmwaresDominio> listaFirmwares = firmwaresMetodos.ListarPorIDTipoGateway(gateway.IDTipoGateway, versao.VersaoInt);
            ViewBag.listaFirmwares = listaFirmwares;

            // le atualizações
            ListaUpdatesMetodos updateMetodos = new ListaUpdatesMetodos();
            List<ListaUpdatesDominio> listaUpdates = updateMetodos.ListarPorIDGateway(IDGateway);
            ViewBag.listaUpdates = listaUpdates;

            return;
        }


        //  ===========================================
        //  Etapas do processo de update no SmartEnergy
        //  ===========================================
        //
        //  - Usuário seleciona a gateway e a versão desejada provenientes do banco de dados 'Firmwares'.
        //  - O SmartEnergy cria uma solicitação de update no banco de dados 'ListaUpdatesMQTT'. 
        //  - O processo de update é feito entre a gateway e o aplicativo SmartMQTT. O progresso do update é registrado no banco de dados 'ListaUpdatesMQTT' para acompanhamento.
        //  - O SmartMQTT faz uma recepção de todas as configurações da gateway para atualizar os bancos de dados.
        //  - O SmartMQTT copia o arquivo da versão solicitada para o subdiretório '\Firmware\Update' renomeando para 'SmartGateE_GTxxxxxx.dat'.
        //  - O SmartMQTT envia para a gateway o comando de atualização de firmware via MQTT. 
        //  - O SmartEnergy acompanha o progresso do update e caso ele pare por algum motivo, é contado timeout no SmartEnergy. No final do timeout é apresentado mensagem de erro ao usuário.
        //  - Assim que o processo de update for concluído, o SmartEnergy avisa o usuário pela tela e o SmartMQTT avisa por email e registra a atualização no 'ListaUpdatesMQTT'.
        //  - Durante a atualização, o usuário poderá cancelar o update. O SmartEnergy envia um comando MQTT para a gateway solicitando o cancelamento.
        //

        // progresso
        public class ATUALIZAR_FIRMWARE_TASK
        {
            public int progresso { get; set; }
            public int timeout { get; set; }
            public string status_atual { get; set; }
        }

        // resultado processo
        public class ATUALIZAR_FIRMWARE_RESULTADO
        {
            // status
            public int status { get; set; }
        }

        // estruturas
        private static IDictionary<Guid, ATUALIZAR_FIRMWARE_TASK> tasks = new Dictionary<Guid, ATUALIZAR_FIRMWARE_TASK>();
        private static ATUALIZAR_FIRMWARE_RESULTADO update_resultado_tmp = new ATUALIZAR_FIRMWARE_RESULTADO();
        private static IDictionary<Guid, ATUALIZAR_FIRMWARE_RESULTADO> update_resultado = new Dictionary<Guid, ATUALIZAR_FIRMWARE_RESULTADO>();

        // GET: Processo de atualização de firmware
        public ActionResult Firmware_Atualizar_IniciaProcesso(int IDGateway, int VersaoGateway = 0, string versaoSolicitada = "0", string datahora = "01/01/2000")
        {
            //
            // Insere solicitação de atualização na lista de updates
            //

            // le cookies
            LeCookies_SmartEnergy();

            // IDUsuario
            int IDUsuario = ViewBag._IDUsuario;

            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = usuarioMetodos.ListarPorId(IDUsuario);

            // converte versão solicitada
            int VersaoSolicitada = int.Parse(versaoSolicitada);

            // data hora
            DateTime programacaoDataHora = DateTime.Now;

            if ( !DateTime.TryParse(datahora, out programacaoDataHora) )
            {
                // executa imediatamente
                programacaoDataHora = DateTime.Now;
            }

            // inicia update
            ListaUpdatesMetodos updateMetodos = new ListaUpdatesMetodos();

            // verifica se é comando para atualização (>0) ou somente acompanhar atualização em andamento (=0)
            if (VersaoSolicitada > 0)
            {
                // inicia update
                updateMetodos.Inicia(IDGateway, IDUsuario, VersaoGateway, VersaoSolicitada, programacaoDataHora);

                // caso início insiro evento
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
                usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.OPERACAO, TABELA_OPERACAO.ATUALIZACAO_FIRMWARE, IDGateway);
            }

            // lê update
            ListaUpdatesDominio updateNovo = updateMetodos.AtualizacaoEmAndamento(IDGateway);

            // IDUpdate
            int IDUpdate = updateNovo.IDUpdate;

            // verifica se a atualização é imediatamente ou programada
            if (programacaoDataHora > DateTime.Now)
            {
                // a atualização será feita mais tarde

                // email do usuário que irá receber resultado da atualização
                string email = "---";

                if (usuario != null)
                {
                    email = usuario.Email;
                }

                // retorno
                var returnedProgramada = new
                {
                    status = "PROGRAMADA",
                    email = email
                };

                // retorna taskId
                return Json(returnedProgramada, JsonRequestBehavior.AllowGet);
            }


            //
            // Supervisiona processo de update
            //

            // taskID
            Guid taskId = Guid.NewGuid();

            // progresso
            ATUALIZAR_FIRMWARE_TASK progresso = new ATUALIZAR_FIRMWARE_TASK();
            progresso.progresso = 0;
            progresso.timeout = 0;
            progresso.status_atual = "---";

            tasks.Add(taskId, progresso);

            // salva cookie
            CookieStore.SalvaCookie_GUID("taskID", taskId);

            // task
            Task.Factory.StartNew(() =>
            {
                // progresso
                progresso.progresso = 0;
                progresso.timeout = 0;
                progresso.status_atual = "Atualizando Firmware [0%]";
                tasks[taskId] = progresso;

                // atualizando
                update_resultado_tmp.status = TIPO_UPDATE_STATUS.Updating;
                bool updating = true;
                int timeout = 0;
                int progresso_anterior = 0;

                // loop para aguardar update
                while (updating)
                {
                    //
                    // Status do update
                    //
                    // o status do update é atualizado pelo aplicativo SmartMQTT.exe

                    // lê status do update
                    ListaUpdatesMetodos updateMetodosTask = new ListaUpdatesMetodos();
                    ListaUpdatesDominio update = updateMetodosTask.ListarPorIDUpdate(IDUpdate);

                    if (update != null)
                    {
                        // verifica se atualizando
                        if (update.Status == TIPO_UPDATE_STATUS.Updating)
                        {
                            // verifica se progresso está progredindo
                            if (progresso_anterior != progresso.progresso)
                            {
                                // progresso está progredindo, zero timeout
                                timeout = 0;
                            }

                            // copio progresso
                            progresso_anterior = progresso.progresso;
                        }

                        // verifica se terminou
                        if (update.Progresso >= 100 || update.Status == TIPO_UPDATE_STATUS.Atualizacao_OK)
                        {
                            // atualiza progresso arquivos
                            progresso.progresso = 100;
                            progresso.timeout = 0;
                            progresso.status_atual = "Atualizando Firmware [100%]";
                            tasks[taskId] = progresso;

                            // ok
                            update_resultado_tmp.status = TIPO_UPDATE_STATUS.Atualizacao_OK;

                            // terminou
                            updating = false;
                        }
                        else
                        {
                            // conta timeout
                            timeout++;

                            // verifica se passou timeout (100 segundos) ou erro
                            if (timeout > 100 || update.Status == TIPO_UPDATE_STATUS.Atualizacao_Erro)
                            {
                                // atualiza progresso arquivos
                                progresso.progresso = 100;
                                progresso.timeout = 100;
                                progresso.status_atual = "Timeout";
                                tasks[taskId] = progresso;

                                // erro
                                update_resultado_tmp.status = TIPO_UPDATE_STATUS.Atualizacao_Erro;

                                // terminou
                                updating = false;
                            }

                            // verifica se cancelado
                            if (update.Status == TIPO_UPDATE_STATUS.Cancelar_Inicio || update.Status == TIPO_UPDATE_STATUS.Atualizacao_Cancelada)
                            {
                                // atualiza progresso arquivos
                                progresso.progresso = 100;
                                progresso.timeout = 100;
                                progresso.status_atual = "Atualização cancelada";
                                tasks[taskId] = progresso;

                                // cancelado
                                update_resultado_tmp.status = TIPO_UPDATE_STATUS.Atualizacao_Cancelada;

                                // terminou
                                updating = false;
                            }

                            // atualiza progresso arquivos
                            progresso.progresso = (int)update.Progresso;
                            progresso.timeout = timeout;
                            progresso.status_atual = string.Format("Atualizando Firmware [{0}%]", progresso.progresso);
                            tasks[taskId] = progresso;

                            // delay de 1 segundo
                            System.Threading.Thread.Sleep(1000);
                        }
                    }
                    else
                    {
                        // erro
                        progresso.progresso = 100;
                        progresso.timeout = 100;
                        progresso.status_atual = "Erro ao ler Update";
                        tasks[taskId] = progresso;

                        // ok
                        update_resultado_tmp.status = TIPO_UPDATE_STATUS.Atualizacao_Erro;

                        // terminou
                        updating = false;
                    }
                }

                // coloca resultado
                update_resultado.Add(taskId, update_resultado_tmp);

                // terminou
                tasks.Remove(taskId);
            });

            // retorno
            var returnedData = new
            {
                status = "OK",
                taskId = taskId
            };

            // retorna taskId
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // verifica se existe atualização em andamento
        public ActionResult Firmware_Atualizar_Verifica(int IDGateway)
        {
            // verifica se existe atualização em andamento desta gateway
            ListaUpdatesMetodos updateMetodos = new ListaUpdatesMetodos();
            ListaUpdatesDominio updateNovo = updateMetodos.AtualizacaoEmAndamento(IDGateway);

            string status = updateNovo.IDUpdate == 0 ? "INEXISTENTE" : "EM_ANDAMENTO";

            // retorno
            var returnedData = new
            {
                status = status,
                versao = updateNovo.VersaoSolicitada_Texto
            };

            // retorna versão
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // progresso da atualização
        public ActionResult Firmware_Atualizar_Progress(Guid id)
        {
            ATUALIZAR_FIRMWARE_TASK progresso = new ATUALIZAR_FIRMWARE_TASK();
            progresso.progresso = 100;
            progresso.timeout = 0;
            progresso.status_atual = "";

            return Json(tasks.Keys.Contains(id) ? tasks[id] : progresso, JsonRequestBehavior.AllowGet);
        }

        // resultado da atualização
        public ActionResult Firmware_Atualizar_Resultado(Guid id)
        {
            // resultado
            return Json(update_resultado.Keys.Contains(id) ? update_resultado[id] : null, JsonRequestBehavior.AllowGet);
        }


        // cancelar a atualização
        public ActionResult Firmware_CancelarAtualizacao(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "ERRO",
                erro = "Erro no comando Cancelar Atualização"
            };

            // verifica se existe atualização em andamento desta gateway
            ListaUpdatesMetodos updateMetodos = new ListaUpdatesMetodos();
            ListaUpdatesDominio update = updateMetodos.AtualizacaoEmAndamento(IDGateway);

            if (update.IDUpdate > 0)
            {
                //
                // Comando cancelar atualização
                //
                // comunicação MQTT
                SmartCom smartCom = new SmartCom(IDGateway, SMCOM_PROTOCOLO.MQTT);

                // verifica conexão com gateway
                if (smartCom.Conectar())
                {
                    // comando atualização de firmware
                    if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.COMANDO_CANCELAR_ATUALIZACAO, SMCOM_TIPO_SOL.ENVIA))
                    {
                        // cancela atualização. O aplicativo SmartMQTT.exe fará o procedimento de cancelamento para apagar o firmware.
                        updateMetodos.CancelarAtualizacao(update.IDUpdate);

                        // retorno
                        returnedData = new
                        {
                            status = "OK",
                            erro = ""
                        };
                    }
                }
            }

            // retorna 
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        // cancelar a atualização
        public ActionResult Firmware_CancelarAtualizacao_Programada(int IDGateway)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe atualização em andamento desta gateway
            ListaUpdatesMetodos updateMetodos = new ListaUpdatesMetodos();
            updateMetodos.ExcluirInicio(IDGateway);

            // retorna 
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }
    }
}