﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class ConfiguracaoController
    {
        // GET: Versão do firmware
        public ActionResult GateE_Versao(int IDGateway)
        {
            // tela de ajuda
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");
            CookieStore.SalvaCookie_Int("_IDGateway", IDGateway);

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // receber versão
            GateE_Versao_Dominio versao = GateE_Versao_Receber(IDGateway);

            // prepara listas
            GateE_Versao_PreparaListas(IDGateway, versao);

            // retorna
            return View(versao);
        }

        // Versão do Firmware - Receber
        private GateE_Versao_Dominio GateE_Versao_Receber(int IDGateway)
        {
            // versão
            GateE_Versao_Dominio versao = new GateE_Versao_Dominio();
            versao.Modelo = "SmartGate E";
            versao.Versao = "---";
            versao.DriverRemota = "---";

            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);

            // status
            bool solicitaProg = false;

            // comunicação MQTT
            SmartCom smartCom = new SmartCom(gateway.IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita versão
                if (solicitaProg = smartCom.Solicitar(SMCOM_FUNCOES_SOL.VERSAO, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // copia versão recebida
                    versao = smartCom.gateE.Versao;

                    // controle da configuração
                    versao.Status_Cfg = STATUS_CFG.ATUAL;

                    // salva configuração
                    GateE_Versao_Metodos prgMetodos = new GateE_Versao_Metodos();
                    prgMetodos.Salvar(IDGateway, versao);
                }
            }

            // copia novo status da solicitacao para a view
            ViewBag.SolicitaProg = solicitaProg;

            // retorna versão
            return (versao);
        }

        // prepara listas
        private void GateE_Versao_PreparaListas(int IDGateway, GateE_Versao_Dominio versao)
        {
            // le gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(IDGateway);
            ViewBag.Gateway = gateway;

            // le firmwares excluindo da lista a versão atual da gateway
            FirmwaresMetodos firmwaresMetodos = new FirmwaresMetodos();
            List<FirmwaresDominio> listaFirmwares = firmwaresMetodos.ListarPorIDTipoGateway(gateway.IDTipoGateway, versao.VersaoInt);
            ViewBag.listaFirmwares = listaFirmwares;

            // le atualizações
            ListaUpdatesMetodos updateMetodos = new ListaUpdatesMetodos();
            List<ListaUpdatesDominio> listaUpdates = updateMetodos.ListarPorIDGateway(IDGateway);

            // percorre atualizações para substituir usuários
            foreach (ListaUpdatesDominio update in listaUpdates)
            {
                if (update.IDUsuario > 0)
                {
                    UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
                    UsuarioDominio usuario = usuarioMetodos.ListarPorId(update.IDUsuario);

                    if (usuario != null)
                    {
                        if (usuario.IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || usuario.IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                        {
                            update.NomeUsuario = "GESTAL";
                        }
                    }
                }
                else
                {
                    update.NomeUsuario = "GESTAL";
                }
            }
            ViewBag.listaUpdates = listaUpdates;

            return;
        }

        // Versão do firmware (string)
        public string GateE_Versao_String(int IDGateway)
        {
            // versão
            GateE_Versao_Metodos prgMetodos = new GateE_Versao_Metodos();

            // comunicação MQTT
            SmartCom smartCom = new SmartCom(IDGateway, SMCOM_PROTOCOLO.MQTT);

            // verifica conexão com gateway
            if (smartCom.Conectar())
            {
                // solicita versão
                if (smartCom.Solicitar(SMCOM_FUNCOES_SOL.VERSAO, SMCOM_TIPO_SOL.SOLICITA))
                {
                    // salva versão atual
                    smartCom.gateE.Versao.Status_Cfg = STATUS_CFG.ATUAL;
                    prgMetodos.Salvar(IDGateway, smartCom.gateE.Versao);

                    // retorna
                    return (smartCom.gateE.Versao.Versao);
                }
            }

            // erro
            return ("");
        }
    }
}