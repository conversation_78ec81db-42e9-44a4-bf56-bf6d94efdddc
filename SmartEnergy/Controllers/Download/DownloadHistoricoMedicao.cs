﻿using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class DownloadController
    {
        // GET: Exportar Histórico de Medição
        public ActionResult Download_HistoricoMedicao(int IDCliente, int TipoDownload = 0)
        {
            // tela de ajuda - download
            CookieStore.SalvaCookie_String("PaginaAjuda", "Download_Demanda");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Download");

            // le cookies
            LeCookies_SmartEnergy();

            // data inicio
            DateTime data_hora_ini = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 15, 0);

            // data fim
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);
            data_hora_fim = data_hora_fim.AddMinutes(-15);

            // le cookie datahora
            DateTime data_hora_ini_cookie = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime data_hora_fim_cookie = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // verifica se existe datahora no cookie
            if (data_hora_ini_cookie.Year != 2000 && data_hora_fim_cookie.Year != 2000)
            {
                // copia data do cookie 
                data_hora_ini = data_hora_ini_cookie;
                data_hora_fim = data_hora_fim_cookie;
            }

            // converte
            DATAHORA dh_ini = new DATAHORA();
            DATAHORA dh_fim = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, data_hora_ini);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, data_hora_fim);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", data_hora_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", data_hora_fim);

            // data e hora atual
            ViewBag.DataIni = string.Format("{0:d}", data_hora_ini);
            ViewBag.HoraIni = string.Format("{0:HH:mm}", data_hora_ini);
            ViewBag.DataFim = string.Format("{0:d}", data_hora_fim);
            ViewBag.HoraFim = string.Format("{0:HH:mm}", data_hora_fim);

            ViewBag.DataTextoAtualIni = string.Format("{0:d} {1:HH:mm}", data_hora_ini, data_hora_ini);
            ViewBag.DataTextoAtualFim = string.Format("{0:d} {1:HH:mm}", data_hora_fim, data_hora_fim);

            ViewBag.DataAtualN = data_hora_ini;
            ViewBag.DataFinalN = data_hora_fim;

            // lista de medicoes habilitadas do usuario
            List<int> ConfigMedicaoList_Usuario = new List<int>();
            ConfigMedicaoList_Usuario = ViewBag._ConfigMed;

            // verifica se existe lista de medicoes
            if (ConfigMedicaoList_Usuario != null)
            {
                if (ConfigMedicaoList_Usuario.Count == 0)
                {
                    ConfigMedicaoList_Usuario = null;
                }
            }

            // le medicoes configuradas do grupo para lista
            CliGateGrupoUnidMedicoesMetodos medicoesMetodos = new CliGateGrupoUnidMedicoesMetodos();
            List<CliGateGrupoUnidMedicoesDominio> listaMedicoes = new List<CliGateGrupoUnidMedicoesDominio>();

            // le medicoes habilitadas para o usuario no cliente
            listaMedicoes = medicoesMetodos.ListarPorIDCliente(IDCliente, ConfigMedicaoList_Usuario);

            ViewBag.listaMedicoes = listaMedicoes;

            // le tipos medicao
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposMedicao = listatiposMetodos.ListarTodos("TipoMedicao");
            ViewBag.listaTipoMedicao = listatiposMedicao;

            return View();
        }


        // estruturas
        private static IDictionary<Guid, int> tasks_historico = new Dictionary<Guid, int>();

        public class SourceFile
        {
            public string Name { get; set; }
            public string Extension { get; set; }
            public Byte[] FileBytes { get; set; }
        }

        public ActionResult Download_HistoricoMedicao_Inicia(string DataIni, string DataFim, List<GrupoMedicoesMedDominio> Medicoes)
        {
            // pega data
            DateTime dateValueIni = DateTime.Parse(DataIni);
            DateTime dateValueFim = DateTime.Parse(DataFim);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", dateValueIni);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", dateValueFim);


            // taskID
            Guid taskId = Guid.NewGuid();
            tasks_historico.Add(taskId, 0);

            // salva cookie
            CookieStore.SalvaCookie_GUID("taskID", taskId);

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;

            // lista de erros
            var listaErros = new List<string>();

            // lista de medicoes habilitadas do usuario
            List<int> ConfigMedicaoList_Usuario = new List<int>();
            ConfigMedicaoList_Usuario = ViewBag._ConfigMed;

            // verifica se existe lista de medicoes
            if (ConfigMedicaoList_Usuario != null)
            {
                if (ConfigMedicaoList_Usuario.Count == 0)
                {
                    ConfigMedicaoList_Usuario = null;
                }
            }

            // le medicoes configuradas do grupo para lista
            CliGateGrupoUnidMedicoesMetodos medicoesMetodos = new CliGateGrupoUnidMedicoesMetodos();
            List<CliGateGrupoUnidMedicoesDominio> listaMedicoes = new List<CliGateGrupoUnidMedicoesDominio>();

            // le medicoes habilitadas para o usuario no cliente
            listaMedicoes = medicoesMetodos.ListarPorIDCliente(IDCliente, ConfigMedicaoList_Usuario);

            // lista de arquivos
            List<SourceFile> sourceFiles = new List<SourceFile>();

            // nome do arquivo
            string ZipArquivo = string.Format("HistoricoMedicoes_{0:000000}_{1:yyyyMMddHHmm}_{2:yyyyMMddHHmm}.zip", IDCliente, dateValueIni, dateValueFim);


            // task
            Task.Factory.StartNew(() =>
            {
                // barra de progresso
                int total = 0;
                int atual = 0;

                // percorre medicoes
                if (listaMedicoes != null)
                {
                    // barra de progresso
                    total = Medicoes.Count;

                    // compacta arquivos
                    using (MemoryStream memoryStream = new MemoryStream())
                    {
                        // cria o ZIP
                        using (ZipArchive zip = new ZipArchive(memoryStream, System.IO.Compression.ZipArchiveMode.Create, true))
                        {
                            // percorre medicoes
                            foreach (GrupoMedicoesMedDominio medicao in Medicoes)
                            {
                                // encontra medicao na lista
                                CliGateGrupoUnidMedicoesDominio med = listaMedicoes.Find(x => x.IDMedicao == medicao.IDMedicao);

                                if (med == null)
                                {
                                    continue;
                                }

                                // update task progress
                                atual++;
                                double progresso = ((double)atual / (double)total) * 100;
                                tasks_historico[taskId] = (int)progresso;

                                // arquivo a ser compactado
                                SourceFile file = new SourceFile();

                                file.Name = string.Format("Medicao_{0:000000}_{1:yyyyMMddHHmm}_{2:yyyyMMddHHmm}", med.IDMedicao, dateValueIni, dateValueFim);
                                file.Extension = "xlsx";

                                // lê configuração da medição
                                MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                                MedicoesDominio med_config = medicaoMetodos.ListarPorId(med.IDMedicao);

                                // Planilha Excel
                                XSSFWorkbook workbook = new XSSFWorkbook();

                                switch (med.IDTipoMedicao)
                                {
                                    case TIPO_MEDICAO.ENERGIA:
                                    case TIPO_MEDICAO.ENERGIA_FORMULA:

                                        workbook = Dem_exportar_XLSX(med, med_config, dateValueIni, dateValueFim);
                                        break;

                                    case TIPO_MEDICAO.UTILIDADES:
                                    case TIPO_MEDICAO.UTILIDADES_FORMULA:

                                        workbook = Util_exportar_XLSX(med, med_config, dateValueIni, dateValueFim);
                                        break;

                                    case TIPO_MEDICAO.CICLOMETRO:

                                        workbook = Ciclometro_exportar_XLSX(med, med_config, dateValueIni, dateValueFim);
                                        break;

                                    case TIPO_MEDICAO.ENTRADA_ANALOGICA:
                                    case TIPO_MEDICAO.EA_FORMULA:

                                        workbook = EA_exportar_XLSX(med, med_config, dateValueIni, dateValueFim);
                                        break;

                                    case TIPO_MEDICAO.METEOROLOGIA:

                                        workbook = Meteorologia_exportar_XLSX(med, med_config, dateValueIni, dateValueFim);
                                        break;
                                }

                                // copia bytes
                                MemoryStream ms = new MemoryStream();
                                workbook.Write(ms);
                                file.FileBytes = ms.ToArray();

                                // coloca arquivo no ZIP
                                ZipArchiveEntry zipItem = zip.CreateEntry(file.Name + "." + file.Extension);

                                // add the item bytes to the zip entry by opening the original file and copying the bytes 
                                using (MemoryStream originalFileMemoryStream = new MemoryStream(file.FileBytes))
                                {
                                    using (Stream entryStream = zipItem.Open())
                                    {
                                        originalFileMemoryStream.CopyTo(entryStream);
                                    }
                                }
                            }
                        }

                        //
                        // Salva ZIP
                        //

                        // diretorio
                        string pathForSaving = Server.MapPath("~/Temp");

                        // nome do arquivo completo
                        string nomeArquivoPath = Path.Combine(pathForSaving, ZipArquivo);

                        // coloca planilha no arquivo
                        using (FileStream fs = new FileStream(nomeArquivoPath, FileMode.Create, FileAccess.Write))
                        {
                            memoryStream.Position = 0;
                            //byte[] bytes = new byte[memoryStream.Length];
                            //memoryStream.Read(bytes, 0, (int)memoryStream.Length);

                            byte[] bytes = new byte[memoryStream.Length];
                            bytes = memoryStream.ToArray();

                            fs.Write(bytes, 0, bytes.Length);
                        } 
                    }
                }

                // terminou
                tasks_historico.Remove(taskId);
            });

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = taskId,
                FileName = ZipArquivo
            };

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        public ActionResult Download_HistoricoMedicao_Progress(Guid id)
        {
            return Json(tasks_historico.Keys.Contains(id) ? tasks_historico[id] : 101, JsonRequestBehavior.AllowGet);
        }

        // GET: Histórico Medicao XLSX Download
        [HttpGet]
        public virtual ActionResult HistoricoMedicao_XLSX_Download(string fileName)
        {
            // le arquivo 
            string fullPath = Path.Combine(Server.MapPath("~/Temp"), fileName);
            return File(fullPath, "application/vnd.ms-excel", fileName);
        }



        public ActionResult Download_HistoricoMedicao_Teste(string DataIni, string DataFim, List<GrupoMedicoesMedDominio> Medicoes)
        {
            // pega data
            DateTime dateValueIni = DateTime.Parse(DataIni);
            DateTime dateValueFim = DateTime.Parse(DataFim);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", dateValueIni);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", dateValueFim);

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;

            // lista de erros
            var listaErros = new List<string>();

            // lista de medicoes habilitadas do usuario
            List<int> ConfigMedicaoList_Usuario = new List<int>();
            ConfigMedicaoList_Usuario = ViewBag._ConfigMed;

            // verifica se existe lista de medicoes
            if (ConfigMedicaoList_Usuario != null)
            {
                if (ConfigMedicaoList_Usuario.Count == 0)
                {
                    ConfigMedicaoList_Usuario = null;
                }
            }

            // le medicoes configuradas do grupo para lista
            CliGateGrupoUnidMedicoesMetodos medicoesMetodos = new CliGateGrupoUnidMedicoesMetodos();
            List<CliGateGrupoUnidMedicoesDominio> listaMedicoes = new List<CliGateGrupoUnidMedicoesDominio>();

            // le medicoes habilitadas para o usuario no cliente
            listaMedicoes = medicoesMetodos.ListarPorIDCliente(IDCliente, ConfigMedicaoList_Usuario);

            // lista de arquivos
            List<SourceFile> sourceFiles = new List<SourceFile>();

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            // compacta arquivos
            using (MemoryStream memoryStream = new MemoryStream())
            {
                // cria o ZIP
                using (ZipArchive zip = new ZipArchive(memoryStream, System.IO.Compression.ZipArchiveMode.Create, true))
                {
                    // percorre medicoes
                    foreach (GrupoMedicoesMedDominio medicao in Medicoes)
                    {
                        // encontra medicao na lista
                        CliGateGrupoUnidMedicoesDominio med = listaMedicoes.Find(x => x.IDMedicao == medicao.IDMedicao);

                        if( med == null )
                        {
                            continue;
                        }

                        // arquivo a ser compactado
                        SourceFile file = new SourceFile();

                        file.Name = string.Format("Historico_{0:000000}_{1:yyyyMMddHHmm}_{2:yyyyMMddHHmm}", med.IDMedicao, dateValueIni, dateValueFim);
                        file.Extension = "xlsx";

                        // dados da medicao
                        ViewBag.ClienteNome = med.NomeCliente;
                        ViewBag.GrupoNome = med.NomeGrupoUnidades;
                        ViewBag.UnidadeNome = med.NomeUnidade;
                        ViewBag.MedicaoNome = med.NomeMedicao;

                        // lê configuração da medição
                        MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                        MedicoesDominio med_config = medicaoMetodos.ListarPorId(med.IDMedicao);

                        // Planilha Excel
                        XSSFWorkbook workbook = new XSSFWorkbook();

                        switch (med.IDTipoMedicao)
                        {
                            case TIPO_MEDICAO.ENERGIA:
                            case TIPO_MEDICAO.ENERGIA_FORMULA:

                                workbook = Dem_exportar_XLSX(med, med_config, dateValueIni, dateValueFim);
                                break;

                            case TIPO_MEDICAO.UTILIDADES:
                            case TIPO_MEDICAO.UTILIDADES_FORMULA:

                                workbook = Util_exportar_XLSX(med, med_config, dateValueIni, dateValueFim);
                                break;

                            case TIPO_MEDICAO.CICLOMETRO:

                                workbook = Ciclometro_exportar_XLSX(med, med_config, dateValueIni, dateValueFim);
                                break;

                            case TIPO_MEDICAO.ENTRADA_ANALOGICA:
                            case TIPO_MEDICAO.EA_FORMULA:

                                workbook = EA_exportar_XLSX(med, med_config, dateValueIni, dateValueFim);
                                break;

                            case TIPO_MEDICAO.METEOROLOGIA:

                                workbook = Meteorologia_exportar_XLSX(med, med_config, dateValueIni, dateValueFim);
                                break;
                        }

                        // copia bytes
                        MemoryStream ms = new MemoryStream();
                        workbook.Write(ms);
                        file.FileBytes = ms.ToArray();

                        // coloca arquivo no ZIP
                        ZipArchiveEntry zipItem = zip.CreateEntry(file.Name + "." + file.Extension);

                        // add the item bytes to the zip entry by opening the original file and copying the bytes 
                        using (MemoryStream originalFileMemoryStream = new MemoryStream(file.FileBytes))
                        {
                            using (Stream entryStream = zipItem.Open())
                            {
                                originalFileMemoryStream.CopyTo(entryStream);
                            }
                        }
                    }
                }

                // memoria
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();

            }

            // nome do arquivo
            string ZipArquivo = string.Format("HistoricoMedicoes_{0:000000}_{1:yyyyMMddHHmm}_{2:yyyyMMddHHmm}.zip", IDCliente, dateValueIni, dateValueFim);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = ZipArquivo
            };

            // zip
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // Demanda Ativa/Reativa/Fator de Potencia exportar XLSX
        private XSSFWorkbook Dem_exportar_XLSX(CliGateGrupoUnidMedicoesDominio medicao, MedicoesDominio med_config, DateTime DataIni, DateTime DataFim)
        {
            // datas
            ViewBag.DataAtualN = DataIni;
            ViewBag.DataFinalN = DataFim;

            // calcula
            EN_Metodos enMetodos = new EN_Metodos();
            List<EN_Dominio> enRegistros = enMetodos.ListarTodosPeriodo(med_config.IDCliente, med_config.IDMedicao, DataIni, DataFim);

            // cria excel
            XSSFWorkbook workbook = new XSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLSX(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLSX(workbook, 0);
            ICellStyle _int4CellStyle = criaEstiloXLSX(workbook, 4);
            ICellStyle _1CellStyle = criaEstiloXLSX(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLSX(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLSX(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            ISheet sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "ID Cliente", "ID Medição", "Data e Hora", "Período", "Demanda Ativa", "Demanda Reativa", "Fator de Potência" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLSX(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            if (enRegistros != null)
            {
                // percorre valores
                foreach (EN_Dominio registro in enRegistros)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // IDCliente
                    numeroCelulaXLSX(row, 0, med_config.IDCliente, _int4CellStyle);

                    // IDMedicao
                    numeroCelulaXLSX(row, 1, med_config.IDMedicao, _int4CellStyle);

                    // data e hora
                    datahoraCelulaXLSX(row, 2, registro.DataHora, _datahoraStyle);

                    // periodo
                    numeroCelulaXLSX(row, 3, registro.Periodo, _intCellStyle);

                    // demanda ativa
                    numeroCelulaXLSX(row, 4, registro.Ativo, _1CellStyle);

                    // demanda reativa
                    numeroCelulaXLSX(row, 5, registro.Reativo, _1CellStyle);

                    // fator de potencia
                    double fatpot = 1.0;

                    if (registro.Ativo != 0.0)
                    {
                        double aux1 = registro.Reativo / registro.Ativo;
                        fatpot = 1.0 / Math.Sqrt(1.0 + (aux1 * aux1));

                        fatpot *= (registro.Reativo < 0.0) ? -1.0 : 1.0;
                    }

                    numeroCelulaXLSX(row, 6, fatpot, _3CellStyle);

                    // proxima
                    rowIndex++;
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // nomes
            ViewBag.ClienteNome = medicao.NomeCliente;
            ViewBag.GrupoNome = medicao.NomeGrupoUnidades;
            ViewBag.UnidadeNome = medicao.NomeUnidade;
            ViewBag.MedicaoNome = medicao.NomeMedicao;

            // cabecalho
            rowIndex = cabecalhoResumoXLSX(workbook, sheet, "Histórico de Demanda", "", _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(9).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }

        // Utilidades exportar XLSX
        private XSSFWorkbook Util_exportar_XLSX(CliGateGrupoUnidMedicoesDominio medicao, MedicoesDominio med_config, DateTime DataIni, DateTime DataFim)
        {
            // datas
            ViewBag.DataAtualN = DataIni;
            ViewBag.DataFinalN = DataFim;

            // calcula
            GG_Metodos ggMetodos = new GG_Metodos();
            List<GG_Dominio> ggRegistros = ggMetodos.ListarTodosPeriodo(med_config.IDCliente, med_config.IDMedicao, DataIni, DataFim);

            // cria excel
            XSSFWorkbook workbook = new XSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLSX(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLSX(workbook, 0);
            ICellStyle _int4CellStyle = criaEstiloXLSX(workbook, 4);
            ICellStyle _6CellStyle = criaEstiloXLSX(workbook, 6);
            ICellStyle _negritoCellStyle = criaEstiloXLSX(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            ISheet sheet = workbook.CreateSheet("Tabela");

            // grandeza
            string grandeza = string.Format("{0} ({1})", med_config.NomeGrandeza, med_config.UnidadeGrandeza);

            // cabecalho
            string[] cabecalho = { "ID Cliente", "ID Medição", "Data e Hora", grandeza };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLSX(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            if (ggRegistros != null)
            {
                // percorre valores
                foreach (GG_Dominio registro in ggRegistros)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // IDCliente
                    numeroCelulaXLSX(row, 0, med_config.IDCliente, _int4CellStyle);

                    // IDMedicao
                    numeroCelulaXLSX(row, 1, med_config.IDMedicao, _int4CellStyle);

                    // data e hora
                    datahoraCelulaXLSX(row, 2, registro.DataHora, _datahoraStyle);

                    // valor
                    numeroCelulaXLSX(row, 3, registro.VMed, _6CellStyle);

                    // proxima
                    rowIndex++;
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // nomes
            ViewBag.ClienteNome = medicao.NomeCliente;
            ViewBag.GrupoNome = medicao.NomeGrupoUnidades;
            ViewBag.UnidadeNome = medicao.NomeUnidade;
            ViewBag.MedicaoNome = medicao.NomeMedicao;

            // cabecalho
            rowIndex = cabecalhoResumoXLSX(workbook, sheet, "Histórico de Utilidades", "", _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(9).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }

        // Ciclometro exportar XLSX
        private XSSFWorkbook Ciclometro_exportar_XLSX(CliGateGrupoUnidMedicoesDominio medicao, MedicoesDominio med_config, DateTime DataIni, DateTime DataFim)
        {
            // datas
            ViewBag.DataAtualN = DataIni;
            ViewBag.DataFinalN = DataFim;

            // calcula
            GG_Metodos ggMetodos = new GG_Metodos();
            List<GG_Dominio> ggRegistros = ggMetodos.ListarTodosPeriodo(med_config.IDCliente, med_config.IDMedicao, DataIni, DataFim);

            // cria excel
            XSSFWorkbook workbook = new XSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLSX(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLSX(workbook, 0);
            ICellStyle _int4CellStyle = criaEstiloXLSX(workbook, 4);
            ICellStyle _int8CellStyle = criaEstiloXLSX(workbook, 28);
            ICellStyle _negritoCellStyle = criaEstiloXLSX(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            ISheet sheet = workbook.CreateSheet("Tabela");

            // grandeza
            string grandeza = string.Format("{0} ({1})", med_config.NomeGrandeza, med_config.UnidadeGrandeza);

            // cabecalho
            string[] cabecalho = { "ID Cliente", "ID Medição", "Data e Hora", grandeza };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLSX(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            if (ggRegistros != null)
            {
                // percorre valores
                foreach (GG_Dominio registro in ggRegistros)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // IDCliente
                    numeroCelulaXLSX(row, 0, med_config.IDCliente, _int4CellStyle);

                    // IDMedicao
                    numeroCelulaXLSX(row, 1, med_config.IDMedicao, _int4CellStyle);

                    // data e hora
                    datahoraCelulaXLSX(row, 2, registro.DataHora, _datahoraStyle);

                    // valor
                    numeroCelulaXLSX(row, 3, registro.VMed, _int8CellStyle);

                    // proxima
                    rowIndex++;
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // nomes
            ViewBag.ClienteNome = medicao.NomeCliente;
            ViewBag.GrupoNome = medicao.NomeGrupoUnidades;
            ViewBag.UnidadeNome = medicao.NomeUnidade;
            ViewBag.MedicaoNome = medicao.NomeMedicao;

            // cabecalho
            rowIndex = cabecalhoResumoXLSX(workbook, sheet, "Histórico de Ciclômetro", "", _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(9).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }

        // Analógicas exportar XLSX
        private XSSFWorkbook EA_exportar_XLSX(CliGateGrupoUnidMedicoesDominio medicao, MedicoesDominio med_config, DateTime DataIni, DateTime DataFim)
        {
            // datas
            ViewBag.DataAtualN = DataIni;
            ViewBag.DataFinalN = DataFim;

            // calcula
            GG_Metodos ggMetodos = new GG_Metodos();
            List<GG_Dominio> ggRegistros = ggMetodos.ListarTodosPeriodo(med_config.IDCliente, med_config.IDMedicao, DataIni, DataFim);

            // cria excel
            XSSFWorkbook workbook = new XSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLSX(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLSX(workbook, 0);
            ICellStyle _int4CellStyle = criaEstiloXLSX(workbook, 4);
            ICellStyle _6CellStyle = criaEstiloXLSX(workbook, 6);
            ICellStyle _negritoCellStyle = criaEstiloXLSX(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            ISheet sheet = workbook.CreateSheet("Tabela");

            // grandeza
            string grandeza = string.Format("{0} ({1})", med_config.NomeGrandeza, med_config.UnidadeGrandeza);

            // cabecalho
            string[] cabecalho = { "ID Cliente", "ID Medição", "Data e Hora", grandeza, "Mínimo", "Máximo" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLSX(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            if (ggRegistros != null)
            {
                // percorre valores
                foreach (GG_Dominio registro in ggRegistros)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // IDCliente
                    numeroCelulaXLSX(row, 0, med_config.IDCliente, _int4CellStyle);

                    // IDMedicao
                    numeroCelulaXLSX(row, 1, med_config.IDMedicao, _int4CellStyle);

                    // data e hora
                    datahoraCelulaXLSX(row, 2, registro.DataHora, _datahoraStyle);

                    // médio
                    numeroCelulaXLSX(row, 3, registro.VMed, _6CellStyle);

                    // mínimo
                    numeroCelulaXLSX(row, 4, registro.VMin, _6CellStyle);

                    // máximo
                    numeroCelulaXLSX(row, 5, registro.VMax, _6CellStyle);

                    // proxima
                    rowIndex++;
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // nomes
            ViewBag.ClienteNome = medicao.NomeCliente;
            ViewBag.GrupoNome = medicao.NomeGrupoUnidades;
            ViewBag.UnidadeNome = medicao.NomeUnidade;
            ViewBag.MedicaoNome = medicao.NomeMedicao;

            // cabecalho
            rowIndex = cabecalhoResumoXLSX(workbook, sheet, "Histórico de Analógicas", "", _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(9).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }

        // Meteorologia exportar XLSX
        private XSSFWorkbook Meteorologia_exportar_XLSX(CliGateGrupoUnidMedicoesDominio medicao, MedicoesDominio med_config, DateTime DataIni, DateTime DataFim)
        {
            // datas
            ViewBag.DataAtualN = DataIni;
            ViewBag.DataFinalN = DataFim;

            // calcula
            HistoricoWeatherMetodos metMetodos = new HistoricoWeatherMetodos();
            List<HistoricoWeatherDominio> metRegistros = metMetodos.ListarPeriodo(med_config.IDCidade, DataIni, DataFim);

            // cria excel
            XSSFWorkbook workbook = new XSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLSX(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLSX(workbook, 0);
            ICellStyle _negritoCellStyle = criaEstiloXLSX(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            ISheet sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Temperatura (°C)", "Umidade (%)", "Pressão (hPa)", "Vento Velocidade (km/h)", "Vento Direção", "Clima" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLSX(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            if (metRegistros != null)
            {
                // percorre valores
                foreach (HistoricoWeatherDominio registro in metRegistros)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // data e hora
                    datahoraCelulaXLSX(row, 0, registro.DataHora, _datahoraStyle);

                    // temperatura
                    numeroCelulaXLSX(row, 1, registro.Temp_Atual, _intCellStyle);

                    // umidade
                    numeroCelulaXLSX(row, 2, registro.Umidade, _intCellStyle);

                    // pressão
                    numeroCelulaXLSX(row, 3, registro.Pressao, _intCellStyle);

                    // vento velocidade
                    numeroCelulaXLSX(row, 4, registro.Vento_Velocidade, _intCellStyle);

                    // vento direção
                    string[] nome_Direcao = { "Norte", "Norte - Nordeste", "Nordeste", "Este - Nordeste", "Leste", "Este - Sudeste", "Sudeste", "Sul - Sudeste", "Sul", "Sul - Sudoeste", "Sudoeste", "Oeste - Sudoeste", "Oeste", "Oeste - Noroeste", "Noroeste", "Norte - Noroeste" };
                    textoCelulaXLSX(row, 5, nome_Direcao[registro.Vento_Direcao]);

                    // clima
                    string[] nome_Clima = { "Céu Claro", "Noite Clara", "Parcialmente Nublado", "Parcialmente Nublado", "Nublado", "Chuvoso", "Granizo", "Neve", "Vento", "Nevoeiro" };
                    textoCelulaXLSX(row, 6, nome_Clima[registro.Tempo_Cod]);

                    // proxima
                    rowIndex++;
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // nomes
            ViewBag.ClienteNome = medicao.NomeCliente;
            ViewBag.GrupoNome = medicao.NomeGrupoUnidades;
            ViewBag.UnidadeNome = medicao.NomeUnidade;
            ViewBag.MedicaoNome = medicao.NomeMedicao;

            // cabecalho
            rowIndex = cabecalhoResumoXLSX(workbook, sheet, "Estação Meteorológica", "", _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(9).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }
    }
}