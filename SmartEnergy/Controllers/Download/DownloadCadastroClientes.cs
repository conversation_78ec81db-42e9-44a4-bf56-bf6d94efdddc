﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class DownloadController
    {
        // GET: Receber Cadastro dos Clientes
        public ActionResult Download_CadastroClientes()
        {
            // tela de ajuda - download
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Download");

            // le cookies
            LeCookies_SmartEnergy();

            // lista de clientes
            List<int> ConfigCliList = ViewBag._ConfigCli;

            // le clientes
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clientesMetodos.ListarTodos(ConfigCliList);

            return View(listaClientes);
        }


        // GET: Cadastro Clientes XLS Download
        [HttpGet]
        public virtual ActionResult CadastroClientes_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Download Cadastro Clientes
        public ActionResult DownloadFile_CadastroClientes(List<ListaClientes> Clientes)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // lista de clientes
            List<int> ConfigCliList = ViewBag._ConfigCli;

            // le clientes - ativos
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clientesMetodos.ListarTodos(ConfigCliList);

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            // nome do arquivo
            string nomeArquivo = string.Format("CadastroClientes_{0:yyyyMMddHHmm}.xls", DateTime.Now);

            // gera planilha
            workbook = CadastroClientes_exportar_XLS(Clientes, listaClientes);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            // relatorio para PDF
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // Cadastro de clientes
        private HSSFWorkbook CadastroClientes_exportar_XLS(List<ListaClientes> Clientes, List<ClientesDominio> listaClientes)
        {
            // cria excel
            HSSFWorkbook workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // AUXILIARES
            //

            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposScore = listatiposMetodos.ListarTodos("TipoScore", false);
            List<ListaTiposDominio> listatiposSimNao = listatiposMetodos.ListarTodos("TipoSimNao", false);
            List<ListaTiposDominio> listatiposContratoStatus = listatiposMetodos.ListarTodos("TipoContratoStatus", false);
            List<ListaTiposDominio> listatiposCarteira = listatiposMetodos.ListarTodos("TipoCarteira", false);
            List<ListaTiposDominio> listatiposEscopo = listatiposMetodos.ListarTodos("TipoEscopo", false);
            List<ListaTiposDominio> listatiposMeses = listatiposMetodos.ListarTodos("TipoMeses", false);
            List<ListaTiposDominio> listatiposIndiceReajuste = listatiposMetodos.ListarTodos("TipoIndiceReajuste", false);
            List<ListaTiposDominio> listatiposDias = listatiposMetodos.ListarTodos("TipoDias", false);
            List<ListaTiposDominio> listatiposVencimento = listatiposMetodos.ListarTodos("TipoVencimento", false);

            CidadeEstadoPaisMetodos cidadeMetodos = new CidadeEstadoPaisMetodos();
            List<EstadosDominio> listatiposEstado = cidadeMetodos.ListarTodosEstados();
            List<CidadesDominio> listatiposCidade = cidadeMetodos.ListarTodosCidades(0);

            List<ListaTiposDominio> listatiposDistribuidora = listatiposMetodos.ListarTodos("AgentesDistribuidora");
            List<ListaTiposDominio> listatiposSubMedicao = listatiposMetodos.ListarTodos("TipoSubMedicao", false);
            List<ListaTiposDominio> listatiposClasseAtivo = listatiposMetodos.ListarTodos("TipoClasseAtivo", false);
            List<ListaTiposDominio> listatiposContratoMedicao = listatiposMetodos.ListarTodos("TipoContratoMedicao");
            List<EstruturaTarifariaSubgrupoDominio> listatiposEstruturaTarifaria = listatiposMetodos.ListarTodos_EstruturaTarifaria();
            List<ListaTiposDominio> listatiposSubSistema = listatiposMetodos.ListarTodos("TipoSubSistema");

            AgentesMetodos agentesMetodos = new AgentesMetodos();
            List<AgentesDominio> listatiposComercializadoras = agentesMetodos.ListarPorComercializadora();

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Cadastro de Clientes");

            // cabecalho
            string[] cabecalho = { "ID [Cliente]", "ID [Agente/Filial]", "SiglaCCEE", "Razão Social", "CNPJ", "Agente", "CEP", "Endereço", "Cidade", "Estado", "Distribuidora", "Estrutura Tarifária", "Demanda Fora Ponta", "Demanda Ponta", "ICMS (%)", "Código de Instalação", "SubMercado", "ID CCEE", "Ponto de Medição", "Nome do Ativo", "Nº do Ativo", "Tipo do Ativo", "Classe do Ativo", "Senha de Atendimento", "Senha de Representante", "Data de Previsão de Migração", "Data de Migração", "Carteira", "Início do Contrato", "Fim do Contrato", "Valor Base (R$)", "Data Base", "Mês do Reajuste", "Índice de Reajuste", "Valor Reajustado (R$)", "Dia do Vencimento", "Tipo do Vencimento", "Renovação Automática", "Fechamento TR", "Status do Contrato", "Score", "Gestão", "Mês de Rescisão", "Último Mês de Faturamento", "Observações" };
                                     
            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre clientes
            if (listaClientes != null)
            {
                // percorre clientes
                foreach (ClientesDominio cliente in listaClientes)
                {
                    // verifica se cliente desejado
                    if (!Clientes.Any(x => x.IDCliente == cliente.IDCliente))
                    {
                        continue;
                    }

                    // a principio não tem empresa
                    bool nao_tem_empresa = true;

                    // le empresas do cliente
                    EmpresasMetodos empresasMetodos = new EmpresasMetodos();
                    List<EmpresasDominio> empresas = empresasMetodos.ListarPorIDCliente(cliente.IDCliente);

                    if (empresas != null)
                    {
                        foreach (EmpresasDominio empresa in empresas)
                        {
                            // tem empresa
                            nao_tem_empresa = false;


                            //
                            // MEDICAO UNIDADE CONSUMIDORA
                            //

                            // medição da unidade consumidora
                            MedicoesDominio medicao = new MedicoesDominio();
                            empresa.IDMedicao_UnidadeConsumidora = Encontra_IDMedicao_UnidadeConsumidora_Principal(empresa.IDEmpresa, ref medicao);

                            string NomeDistribuidora = "---";
                            string NomeEstruturaTarifaria = "---";
                            double Demanda_ForaPonta = 0.0;
                            double Demanda_Ponta = 0.0;
                            double ICMS = 0.0;
                            string CodigoInstalacao = "---";
                            string NomeSubMercado = "---";
                            string PontoMedicao = "---";
                            string NomeAtivo = "---";
                            string NumeroAtivo = "---";
                            string NomeTipoAtivo = "---";
                            string ClasseAtivo = "---";

                            if (medicao.IDMedicao > 0)
                            {
                                // configuracao da medicao
                                NomeDistribuidora = GetDescricaoTipo(listatiposDistribuidora, medicao.IDAgenteDistribuidora);
                                CodigoInstalacao = medicao.CodigoInstalacao;
                                NomeSubMercado = GetDescricaoTipo(listatiposSubSistema, medicao.IDSubSistema);
                                PontoMedicao = medicao.PontoMedicao;
                                NomeAtivo = medicao.Nome;
                                NumeroAtivo = medicao.Referencia;
                                NomeTipoAtivo = GetDescricaoTipo(listatiposSubMedicao, medicao.IDTipoSubMedicao);
                                ClasseAtivo = GetDescricaoTipo(listatiposClasseAtivo, medicao.IDTipoClasseAtivo);

                                // estrutura tarifaria
                                EstruturaTarifariaSubgrupoDominio estrutura_tarifaria = listatiposEstruturaTarifaria.Find(item => item.IDSubgrupo == medicao.IDSubgrupo);
                                if (estrutura_tarifaria != null)
                                {
                                    NomeEstruturaTarifaria = estrutura_tarifaria.Descricao_Estrutura_Subgrupo;
                                }

                                // le historicocontratos
                                HistoricoContratosMetodos contratosMetodos = new HistoricoContratosMetodos();
                                HistoricoContratosDominio contrato_demanda = contratosMetodos.ListarPorIDMedicaoMaisRecente(medicao.IDMedicao);

                                if (contrato_demanda != null)
                                {
                                    Demanda_ForaPonta = contrato_demanda.ContratoDemFP;
                                    Demanda_Ponta = contrato_demanda.ContratoDemP;
                                }

                                // le historicoICMS
                                HistoricoICMSMetodos historicoMetodos = new HistoricoICMSMetodos();
                                HistoricoICMSDominio valor_ICMS = historicoMetodos.ListarPorIDMedicaoMaisRecente(medicao.IDMedicao);

                                if (valor_ICMS != null)
                                {
                                    ICMS = valor_ICMS.valorICMS;
                                }
                            }


                            // adiciona linha
                            row = sheet.CreateRow(rowIndex++);

                            //
                            // CLIENTE
                            //

                            // IDCliente
                            i = 0;
                            numeroCelulaXLS(row, i++, cliente.IDCliente, _intCellStyle);


                            //
                            // EMPRESA
                            //
                            numeroCelulaXLS(row, i++, empresa.IDEmpresa, _intCellStyle);
                            textoCelulaXLS(row, i++, empresa.SiglaCCEE);
                            textoCelulaXLS(row, i++, empresa.RazaoSocial);
                            textoCelulaXLS(row, i++, empresa.CNPJ);

                            textoCelulaXLS(row, i++, GetDescricaoTipo(listatiposSimNao, empresa.Agente));

                            textoCelulaXLS(row, i++, empresa.CEP);
                            textoCelulaXLS(row, i++, empresa.Endereco);
                            CidadesDominio cidade = listatiposCidade.Find(item => item.IDCidade == empresa.IDCidade);
                            if (cidade != null)
                                textoCelulaXLS(row, i++, cidade.Nome);
                            else
                                textoCelulaXLS(row, i++, "---");

                            EstadosDominio estado = listatiposEstado.Find(item => item.IDEstado == empresa.IDEstado);
                            if (estado != null)
                                textoCelulaXLS(row, i++, estado.Nome);
                            else
                                textoCelulaXLS(row, i++, "---");

                            textoCelulaXLS(row, i++, NomeDistribuidora);
                            textoCelulaXLS(row, i++, NomeEstruturaTarifaria);
                            numeroCelulaXLS(row, i++, Demanda_ForaPonta, _intCellStyle);
                            numeroCelulaXLS(row, i++, Demanda_Ponta, _intCellStyle);
                            numeroCelulaXLS(row, i++, ICMS, _2CellStyle);
                            textoCelulaXLS(row, i++, CodigoInstalacao);
                            textoCelulaXLS(row, i++, NomeSubMercado);

                            numeroCelulaXLS(row, i++, empresa.IDCCEE, _intCellStyle);

                            textoCelulaXLS(row, i++, PontoMedicao);
                            textoCelulaXLS(row, i++, NomeAtivo);
                            textoCelulaXLS(row, i++, NumeroAtivo);
                            textoCelulaXLS(row, i++, NomeTipoAtivo);
                            textoCelulaXLS(row, i++, ClasseAtivo);

                            textoCelulaXLS(row, i++, empresa.Senha_Atendimento);
                            textoCelulaXLS(row, i++, empresa.Senha_Representante);

                            textoCelulaXLS(row, i++, empresa.DataMigracao_Previsao.ToString("d"));
                            textoCelulaXLS(row, i++, empresa.DataMigracao.ToString("d"));

                            textoCelulaXLS(row, i++, GetDescricaoTipo(listatiposCarteira, empresa.Carteira));

                            textoCelulaXLS(row, i++, empresa.Contrato_Inicio.ToString("d"));
                            textoCelulaXLS(row, i++, empresa.Contrato_Fim.ToString("d"));

                            numeroCelulaXLS(row, i++, empresa.Base_Valor, _2CellStyle);
                            textoCelulaXLS(row, i++, empresa.Base_Data.ToString("MM/yyyy"));

                            textoCelulaXLS(row, i++, GetDescricaoTipo(listatiposMeses, empresa.Reajuste_Mes));
                            textoCelulaXLS(row, i++, GetDescricaoTipo(listatiposIndiceReajuste, empresa.Reajuste_Indice));
                            numeroCelulaXLS(row, i++, empresa.Reajuste_Valor, _2CellStyle);

                            textoCelulaXLS(row, i++, GetDescricaoTipo(listatiposDias, empresa.Vencimento_Dias));
                            textoCelulaXLS(row, i++, GetDescricaoTipo(listatiposVencimento, empresa.Vencimento_Tipo));
                            int renovacao_automatica = (empresa.RenovacaoAutomatica) ? 1 : 0;
                            textoCelulaXLS(row, i++, GetDescricaoTipo(listatiposSimNao, renovacao_automatica));

                            textoCelulaXLS(row, i++, empresa.FechamentoTR.ToString("d"));

                            textoCelulaXLS(row, i++, GetDescricaoTipo(listatiposContratoStatus, empresa.Contrato_Status));
                            textoCelulaXLS(row, i++, GetDescricaoTipo(listatiposScore, empresa.Score));

                            AgentesDominio gestao = listatiposComercializadoras.Find(item => item.IDAgente == empresa.Contrato_Gestora);
                            if (gestao != null)
                                textoCelulaXLS(row, i++, gestao.Nome);
                            else
                                textoCelulaXLS(row, i++, "---");

                            if (empresa.MesRescisao.Year == 2000)
                            {
                                textoCelulaXLS(row, i++, "--/----");
                            }
                            else
                            {
                                textoCelulaXLS(row, i++, empresa.MesRescisao.ToString("MM/yyyy"));
                            }

                            if (empresa.UltimoMesFaturamento.Year == 2000)
                            {
                                textoCelulaXLS(row, i++, "--/----");
                            }
                            else
                            {
                                textoCelulaXLS(row, i++, empresa.UltimoMesFaturamento.ToString("MM/yyyy"));
                            }

                            textoCelulaXLS(row, i++, empresa.Observacao);
                        }
                    }

                    // verifica se cliente não tem empresa
                    if (nao_tem_empresa)
                    {
                        // adiciona linha
                        row = sheet.CreateRow(rowIndex++);

                        // IDCliente
                        numeroCelulaXLS(row, 0, cliente.IDCliente, _intCellStyle);

                        // preenche com inexistente
                        for (int j = 1; j < sheet.GetRow(0).LastCellNum; j++)
                        {
                            textoCelulaXLS(row, j, "---");
                        }
                    }
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }


        private string GetDescricaoTipo(List<ListaTiposDominio> listaTipo, int IDTipo)
        {
            ListaTiposDominio tipo = listaTipo.Find(item => item.ID == IDTipo);

            if (tipo == null)
            {
                return ("---");
            }

            return (tipo.Descricao);
        }

        private string GetDescricaoAgente(List<AgentesDominio> listaAgente, int IDAgente)
        {
            AgentesDominio agente = listaAgente.Find(item => item.IDAgente == IDAgente);

            if (agente == null)
            {
                return ("---");
            }

            return (agente.Nome);
        }


        // encontra IDMedicao da unidade consumidoda da empresa
        private int Encontra_IDMedicao_UnidadeConsumidora_Principal(int IDEmpresa, ref MedicoesDominio medicao)
        {
            // IDMedicao unidade consumidora
            int IDMedicao_UnidadeConsumidora = 0;

            medicao.Nome = "---";
            medicao.IDMedicao = 0;
            medicao.IDSubSistema = 0;

            // leio gateways atreladas a esta empresa
            GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
            List<GatewaysDominio> gateways = gatewayMetodos.ListarPorIDEmpresa(IDEmpresa);

            if (gateways != null)
            {
                // percorre gateways e encontra primeira medição de energia marcada como unidade consumidora
                foreach (GatewaysDominio gateway in gateways)
                {
                    // filtro, somente medições principais e em ordem de número de medição interna
                    string order = "AND IDCategoriaMedicao = 0 ORDER BY NumMedGateway";

                    // leio medições da gateway
                    MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                    List<MedicoesDominio> medicoes = medicaoMetodos.ListarPorIDGateway(gateway.IDGateway, order);

                    if (medicoes != null)
                    {
                        // percorre medições e encontra primeira medição de energia marcada como unidade consumidora
                        foreach (MedicoesDominio med in medicoes)
                        {
                            // verifica se medição de energia elétrica e medição 0
                            //if (med.IDTipoMedicao == TIPO_MEDICAO.ENERGIA && med.NumMedGateway == 0)

                            // verifica se medição de energia elétrica
                            if (med.IDTipoMedicao == TIPO_MEDICAO.ENERGIA)
                            {
                                // copia
                                medicao = med;

                                // achou
                                IDMedicao_UnidadeConsumidora = med.IDMedicao;
                                break;
                            }
                        }
                    }

                    // verifica se achou
                    if (IDMedicao_UnidadeConsumidora > 0)
                    {
                        // interrompe busca
                        break;
                    }
                }
            }

            return (IDMedicao_UnidadeConsumidora);
        }
    }
}