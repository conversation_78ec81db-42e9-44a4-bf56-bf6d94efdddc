﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class DownloadController
    {
        // GET: Receber Contratos CCEE
        public ActionResult Download_ContratosCCEE()
        {
            // tela de ajuda - download
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Download");

            // le cookies
            LeCookies_SmartEnergy();

            // le tipos contrato status
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposContratoStatus = listatiposMetodos.ListarTodos("TipoContratoStatus", false);
            ViewBag.listaTipoContratoStatus = listatiposContratoStatus;

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // le clientes do consultor - ativos
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clienteMetodos.ListarPorIDConsultor(IDConsultor, 1);

            // le empresas dos clientes do consultor
            EmpresasMetodos empresasMetodos = new EmpresasMetodos();
            List<EmpresasDominio> listaEmpresas = new List<EmpresasDominio>();

            // percorre clientes
            if (listaClientes != null)
            {
                foreach (ClientesDominio cliente in listaClientes)
                {
                    // le empresas do cliente
                    List<EmpresasDominio> empresas = empresasMetodos.ListarPorIDCliente(cliente.IDCliente);

                    if (empresas != null)
                    {
                        // percorre empresas
                        foreach (EmpresasDominio empresa in empresas)
                        {
                            // adiciona na lista das empresas
                            listaEmpresas.Add(empresa);
                        }
                    }
                }
            }

            // le contratos CCEE do cliente
            ContratosCCEEMetodos contratosMetodos = new ContratosCCEEMetodos();
            List<ContratosCCEEDominio> listaContratos = contratosMetodos.ListarPorIDCliente(0);

            // percorre contratos para resgatar nome dos clientes e empresas
            if (listaContratos != null)
            {
                foreach (ContratosCCEEDominio contrato in listaContratos)
                {
                    // nome e logo do cliente
                    ClientesDominio cliente = listaClientes.Find(c => c.IDCliente == contrato.IDCliente);

                    if (cliente != null)
                    {
                        contrato.NomeCliente = cliente.Nome;
                        contrato.Logo = cliente.Logo;
                    }

                    // SiglaCCEE da empresa
                    EmpresasDominio empresa = listaEmpresas.Find(e => e.IDEmpresa == contrato.IDEmpresa);

                    if (empresa != null)
                    {
                        contrato.RazaoSocial = empresa.RazaoSocial;
                        contrato.SiglaCCEE = empresa.SiglaCCEE;
                        contrato.IDTipoEmpresa = empresa.IDTipoEmpresa;
                        contrato.CNPJ = empresa.CNPJ;
                        contrato.IDEstado = empresa.IDEstado;
                        contrato.IDCidade = empresa.IDCidade;
                        contrato.Carteira = empresa.Carteira;
                    }

                    // formata datas auxiliares
                    contrato.Vigencia_Inicio_aux = String.Format("{0:d}", contrato.Vigencia_Inicio);
                    contrato.Vigencia_Inicio_Sort_aux = String.Format("{0:yyyyMMddHHmmss}", contrato.Vigencia_Inicio);
                    contrato.Vigencia_Fim_aux = String.Format("{0:d}", contrato.Vigencia_Fim);
                    contrato.Vigencia_Fim_Sort_aux = String.Format("{0:yyyyMMddHHmmss}", contrato.Vigencia_Fim);
                }
            }

            return View(listaContratos);
        }

        // lista de contratos
        public class ListaContratos
        {
            public int IDContratoCCEE { get; set; }
        }

        // GET: Contratos CCEE XLS Download
        [HttpGet]
        public virtual ActionResult ContratosCCEE_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Download Contratos CCEE
        public ActionResult DownloadFile_ContratosCCEE(List<ListaContratos> Contratos)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            // nome do arquivo
            string nomeArquivo = string.Format("ContratosEnergia_{0:yyyyMMddHHmm}.xls", DateTime.Now);

            // gera planilha
            workbook = ContratosCCEE_exportar_XLS(Contratos);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            // relatorio para PDF
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // Contratos CCEE
        private HSSFWorkbook ContratosCCEE_exportar_XLS(List<ListaContratos> Contratos)
        {
            //
            // CONTRATOS CCEE
            //

            // le cookies
            LeCookies_SmartEnergy();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // le clientes do consultor - ativos
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clienteMetodos.ListarPorIDConsultor(IDConsultor, 1);

            // le empresas dos clientes do consultor
            EmpresasMetodos empresasMetodos = new EmpresasMetodos();
            List<EmpresasDominio> listaEmpresas = new List<EmpresasDominio>();

            // percorre clientes
            if (listaClientes != null)
            {
                foreach (ClientesDominio cliente in listaClientes)
                {
                    // le empresas do cliente
                    List<EmpresasDominio> empresas = empresasMetodos.ListarPorIDCliente(cliente.IDCliente);

                    if (empresas != null)
                    {
                        // percorre empresas
                        foreach (EmpresasDominio empresa in empresas)
                        {
                            // adiciona na lista das empresas
                            listaEmpresas.Add(empresa);
                        }
                    }
                }
            }

            // le contratos CCEE do cliente
            ContratosCCEEMetodos contratosMetodos = new ContratosCCEEMetodos();
            List<ContratosCCEEDominio> listaContratos = contratosMetodos.ListarPorIDCliente(0);

            // percorre contratos para resgatar nome dos clientes e empresas
            if (listaContratos != null)
            {
                foreach (ContratosCCEEDominio contrato in listaContratos)
                {
                    // nome e logo do cliente
                    ClientesDominio cliente = listaClientes.Find(c => c.IDCliente == contrato.IDCliente);

                    if (cliente != null)
                    {
                        contrato.NomeCliente = cliente.Nome;
                        contrato.Logo = cliente.Logo;
                    }

                    // SiglaCCEE da empresa
                    EmpresasDominio empresa = listaEmpresas.Find(e => e.IDEmpresa == contrato.IDEmpresa);

                    if (empresa != null)
                    {
                        contrato.RazaoSocial = empresa.RazaoSocial;
                        contrato.SiglaCCEE = empresa.SiglaCCEE;
                        contrato.IDTipoEmpresa = empresa.IDTipoEmpresa;
                        contrato.CNPJ = empresa.CNPJ;
                        contrato.IDEstado = empresa.IDEstado;
                        contrato.IDCidade = empresa.IDCidade;
                        contrato.Carteira = empresa.Carteira;
                    }

                    // formata datas auxiliares
                    contrato.Vigencia_Inicio_aux = String.Format("{0:d}", contrato.Vigencia_Inicio);
                    contrato.Vigencia_Inicio_Sort_aux = String.Format("{0:yyyyMMddHHmmss}", contrato.Vigencia_Inicio);
                    contrato.Vigencia_Fim_aux = String.Format("{0:d}", contrato.Vigencia_Fim);
                    contrato.Vigencia_Fim_Sort_aux = String.Format("{0:yyyyMMddHHmmss}", contrato.Vigencia_Fim);
                }
            }


            //
            // PLANILHA
            //

            // cria excel
            HSSFWorkbook workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _6CellStyle = criaEstiloXLS(workbook, 6);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // AUXILIARES
            //

            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposContratoStatus = listatiposMetodos.ListarTodos("TipoContratoStatus", false);
            List<ListaTiposDominio> listatiposFonte = listatiposMetodos.ListarTodos("TipoFonte", false);
            List<ListaTiposDominio> listatiposSubSistema = listatiposMetodos.ListarTodos("TipoSubSistema", false);
            List<ListaTiposDominio> listatiposMeses = listatiposMetodos.ListarTodos("TipoMeses", false);
            List<ListaTiposDominio> listatiposIndiceReajuste = listatiposMetodos.ListarTodos("TipoIndiceReajuste", false);
            List<ListaTiposDominio> listatiposSimNao = listatiposMetodos.ListarTodos("TipoSimNao", false);

            AgentesMetodos agentesMetodos = new AgentesMetodos();
            List<AgentesDominio> listatiposComercializadoras = agentesMetodos.ListarPorComercializadora();


            //
            // DADOS DE CONTRATO
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Dados de Contrato");

            // cabecalho 
            string[] cabecalho = { "ID Contrato", "Sigla CCEE", "Razão Social", "CNPJ", "Código Contrato Fornecedor", "Início da Vigência", "Fim da Vigência", "Comercializadora", "Ano", "Unidades Atendidas", "Preço Base (R$)", "Data Base", "Mês do Reajuste", "Índice de Reajuste", "Preço Reajustado (R$)", "Fonte", "SubMercado", "Volume (MWm)", "Data Limite da Sazonalização", "Sazonalização Mínima (%)", "Sazonalização Máxima (%)", "Flexibilidade Mínima (%)", "Flexibilidade Máxima (%)", "Modulação Mínima (%)", "Modulação Máxima (%)", "Perdas (%)", "Abate PROINFA", "Possui Percentual de Carga", "Percentual de Carga (%)", "Consultor do Negócio", "Status do Contrato", "Observações" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre contratos
            if (listaContratos != null)
            {
                // percorre contratos
                foreach (ContratosCCEEDominio contrato in listaContratos)
                {
                    // verifica se contrato desejado
                    if (!Contratos.Any(x => x.IDContratoCCEE == contrato.IDContratoCCEE))
                    {
                        continue;
                    }

                    //
                    // Descrições
                    //

                    // unidades atendidas
                    ContratosCCEE_UnidadesAtendidasMetodos unidadesAtendidasMetodos = new ContratosCCEE_UnidadesAtendidasMetodos();
                    List<ContratosCCEE_UnidadesAtendidasDominio> unidadesAtendidas = unidadesAtendidasMetodos.ListarPorId(contrato.IDContratoCCEE);

                    int NumUnidadesAtendidas = 0;

                    if (unidadesAtendidas != null)
                    {
                        NumUnidadesAtendidas = unidadesAtendidas.Count;
                    }

                    // preços base
                    ContratosCCEE_PrecoBaseMetodos precoBaseMetodos = new ContratosCCEE_PrecoBaseMetodos();
                    List<ContratosCCEE_PrecoBaseDominio> precos_base = precoBaseMetodos.ListarPorId(contrato.IDContratoCCEE);

                    // volume
                    ContratosCCEE_VolumeMetodos volumeMetodos = new ContratosCCEE_VolumeMetodos();
                    List<ContratosCCEE_VolumeDominio> volumes = volumeMetodos.ListarPorId(contrato.IDContratoCCEE);


                    // calcula número de anos que o contrato possui
                    int ano_Inicio = contrato.Vigencia_Inicio.Year;
                    int ano_Fim = contrato.Vigencia_Fim.Year;

                    // número de anos
                    int numero_anos = (ano_Fim - ano_Inicio) + 1;

                    // percorre anos
                    for (int ano = 0; ano < numero_anos; ano++)
                    {
                        // adiciona linha
                        row = sheet.CreateRow(rowIndex++);
                        i = 0;

                        textoCelulaXLS(row, i++, contrato.Contrato_ID);
                        textoCelulaXLS(row, i++, contrato.SiglaCCEE);
                        textoCelulaXLS(row, i++, contrato.RazaoSocial);
                        textoCelulaXLS(row, i++, contrato.CNPJ);
                        textoCelulaXLS(row, i++, contrato.Contrato_Codigo);

                        textoCelulaXLS(row, i++, contrato.Vigencia_Inicio.ToString("d"));
                        textoCelulaXLS(row, i++, contrato.Vigencia_Fim.ToString("d"));

                        textoCelulaXLS(row, i++, GetDescricaoAgente(listatiposComercializadoras, contrato.IDComercializadora));

                        // Ano
                        DateTime vigencia_atual = contrato.Vigencia_Inicio.AddYears(ano);
                        DateTime ano_atual = new DateTime(vigencia_atual.Year, 1, 1, 0, 0, 0);
                        numeroCelulaXLS(row, i++, ano_atual.Year, _intCellStyle);

                        numeroCelulaXLS(row, i++, NumUnidadesAtendidas, _intCellStyle);

                        // preço e data base
                        double preco_valor = contrato.Base_Preco;

                        if (!contrato.Base_Preco_Flat)
                        {
                            if (precos_base != null)
                            {
                                ContratosCCEE_PrecoBaseDominio preco = precos_base.Find(item => item.Data.Year == ano_atual.Year);

                                if (preco != null)
                                {
                                    preco_valor = preco.Base_Preco;
                                }
                            }
                        }

                        numeroCelulaXLS(row, i++, preco_valor, _2CellStyle);
                        textoCelulaXLS(row, i++, contrato.Base_Data.ToString("MM/yyyy"));

                        textoCelulaXLS(row, i++, GetDescricaoTipo(listatiposMeses, contrato.Reajuste_Mes));
                        textoCelulaXLS(row, i++, GetDescricaoTipo(listatiposIndiceReajuste, contrato.Reajuste_Indice));
                        numeroCelulaXLS(row, i++, contrato.Reajuste_Preco, _2CellStyle);

                        textoCelulaXLS(row, i++, GetDescricaoTipo(listatiposFonte, contrato.IDTipoFonte));
                        textoCelulaXLS(row, i++, GetDescricaoTipo(listatiposSubSistema, contrato.IDSubSistema));

                        // volume
                        if (contrato.Montante_Tipo == 0)
                        {
                            double volume_contratado = 0.0;

                            // montante (MWm)
                            if (volumes != null)
                            {
                                ContratosCCEE_VolumeDominio volume = volumes.Find(item => (item.Data.Year == ano_atual.Year) && (item.Montante_Tipo == contrato.Montante_Tipo));

                                if (volume != null)
                                {
                                    volume_contratado = volume.Contratado;
                                }
                            }

                            numeroCelulaXLS(row, i++, volume_contratado, _6CellStyle);
                        }
                        else
                        {
                            // sazonalizado (MWh)
                            textoCelulaXLS(row, i++, "Contrato Sazonalizado");
                        }

                        textoCelulaXLS(row, i++, contrato.DataLimiteSazo.ToString("dd/MM"));
                        numeroCelulaXLS(row, i++, contrato.Sazonalizacao_Minima, _2CellStyle);
                        numeroCelulaXLS(row, i++, contrato.Sazonalizacao_Maxima, _2CellStyle);

                        numeroCelulaXLS(row, i++, contrato.Flexibilidade_Minima, _2CellStyle);
                        numeroCelulaXLS(row, i++, contrato.Flexibilidade_Maxima, _2CellStyle);
                        numeroCelulaXLS(row, i++, contrato.Modulacao_Minima, _2CellStyle);
                        numeroCelulaXLS(row, i++, contrato.Modulacao_Maxima, _2CellStyle);

                        numeroCelulaXLS(row, i++, contrato.Perdas, _6CellStyle);

                        int proinfa = (contrato.Proinfa_Abate) ? 1 : 0;
                        textoCelulaXLS(row, i++, GetDescricaoTipo(listatiposSimNao, proinfa));
                        int possui_carga = (contrato.PercentualCarga_Possui) ? 1 : 0;
                        textoCelulaXLS(row, i++, GetDescricaoTipo(listatiposSimNao, possui_carga));
                        if (contrato.PercentualCarga_Possui)
                        {
                            numeroCelulaXLS(row, i++, contrato.PercentualCarga_Valor, _2CellStyle);
                        }
                        else
                        {
                            textoCelulaXLS(row, i++, "---");
                        }

                        textoCelulaXLS(row, i++, contrato.ConsultorNegocio);

                        textoCelulaXLS(row, i++, GetDescricaoTipo(listatiposContratoStatus, contrato.Contrato_Status));
                        textoCelulaXLS(row, i++, contrato.Observacao);
                    }
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // SAZO CONTRATO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Sazo Contrato");

            // cabecalho
            string[] cabecalhoSazoContrato = { "ID Contrato", "Sigla CCEE", "Razão Social", "CNPJ", "Código Contrato Fornecedor", "Início da Vigência", "Fim da Vigência", "Comercializadora", "Ano", "Sazonalização Mínima (%)", "Sazonalização Máxima (%)", "Flexibilidade Mínima (%)", "Flexibilidade Máxima (%)", "Modulação Mínima (%)", "Modulação Máxima (%)", "Volume Contratado", "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho", "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalhoSazoContrato);

            // percorre contratos
            if (listaContratos != null)
            {
                // percorre contratos
                foreach (ContratosCCEEDominio contrato in listaContratos)
                {
                    // verifica se contrato desejado
                    if (!Contratos.Any(x => x.IDContratoCCEE == contrato.IDContratoCCEE))
                    {
                        continue;
                    }

                    //
                    // Descrições
                    //

                    // volume
                    ContratosCCEE_VolumeMetodos volumeMetodos = new ContratosCCEE_VolumeMetodos();
                    List<ContratosCCEE_VolumeDominio> volumes = volumeMetodos.ListarPorId(contrato.IDContratoCCEE);


                    // calcula número de anos que o contrato possui
                    int ano_Inicio = contrato.Vigencia_Inicio.Year;
                    int ano_Fim = contrato.Vigencia_Fim.Year;

                    // número de anos
                    int numero_anos = (ano_Fim - ano_Inicio) + 1;

                    // percorre anos
                    for (int ano = 0; ano < numero_anos; ano++)
                    {
                        // adiciona linha
                        row = sheet.CreateRow(rowIndex++);
                        i = 0;

                        textoCelulaXLS(row, i++, contrato.Contrato_ID);
                        textoCelulaXLS(row, i++, contrato.SiglaCCEE);
                        textoCelulaXLS(row, i++, contrato.RazaoSocial);
                        textoCelulaXLS(row, i++, contrato.CNPJ);
                        textoCelulaXLS(row, i++, contrato.Contrato_Codigo);

                        textoCelulaXLS(row, i++, contrato.Vigencia_Inicio.ToString("d"));
                        textoCelulaXLS(row, i++, contrato.Vigencia_Fim.ToString("d"));

                        textoCelulaXLS(row, i++, GetDescricaoAgente(listatiposComercializadoras, contrato.IDComercializadora));

                        // Ano
                        DateTime vigencia_atual = contrato.Vigencia_Inicio.AddYears(ano);
                        DateTime ano_atual = new DateTime(vigencia_atual.Year, 1, 1, 0, 0, 0);
                        numeroCelulaXLS(row, i++, ano_atual.Year, _intCellStyle);

                        numeroCelulaXLS(row, i++, contrato.Sazonalizacao_Minima, _2CellStyle);
                        numeroCelulaXLS(row, i++, contrato.Sazonalizacao_Maxima, _2CellStyle);
                        numeroCelulaXLS(row, i++, contrato.Flexibilidade_Minima, _2CellStyle);
                        numeroCelulaXLS(row, i++, contrato.Flexibilidade_Maxima, _2CellStyle);
                        numeroCelulaXLS(row, i++, contrato.Modulacao_Minima, _2CellStyle);
                        numeroCelulaXLS(row, i++, contrato.Modulacao_Maxima, _2CellStyle);

                        // sazonalização do contrato
                        double[] sazo_meses = new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 };

                        // volume
                        if (contrato.Montante_Tipo == 0)
                        {
                            double volume_contratado = 0.0;

                            // montante (MWm)
                            if (volumes != null)
                            {
                                ContratosCCEE_VolumeDominio volume = volumes.Find(item => (item.Data.Year == ano_atual.Year) && (item.Montante_Tipo == contrato.Montante_Tipo));

                                if (volume != null)
                                {
                                    volume_contratado = volume.Contratado;
                                }
                            }

                            numeroCelulaXLS(row, i++, volume_contratado, _6CellStyle);

                            // meses do ano
                            for (int mes = 0; mes < 12; mes++)
                            {
                                // converte MWm em MWh
                                sazo_meses[mes] = Converte_MWm_MWh(mes+1, ano_atual.Year, volume_contratado);
                            }
                        }
                        else
                        {
                            // sazonalizado (MWh)
                            textoCelulaXLS(row, i++, "Contrato Sazonalizado");

                            // meses do ano
                            for (int mes = 0; mes < 12; mes++)
                            {
                                // encontra volume
                                DateTime data_sazo = new DateTime(ano_atual.Year, mes+1, 1, 0, 0, 0);
                                ContratosCCEE_VolumeDominio volume = volumes.Find(item => item.Data == data_sazo);

                                if (volume != null)
                                {
                                    sazo_meses[mes] = volume.Contratado;
                                }
                            }
                        }

                        numeroCelulaXLS(row, i++, sazo_meses[0], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[1], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[2], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[3], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[4], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[5], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[6], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[7], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[8], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[9], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[10], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[11], _3CellStyle);
                    }
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // SAZO REGISTRADA
            //

            // cria planilha
            sheet = workbook.CreateSheet("Sazo Registrada");

            // cabecalho
            string[] cabecalhoSazoRegistrada = { "ID Contrato", "Sigla CCEE", "Razão Social", "CNPJ", "Código Contrato Fornecedor", "Início da Vigência", "Fim da Vigência", "Comercializadora", "Ano", "Volume Contratado", "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho", "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalhoSazoRegistrada);

            // percorre contratos
            if (listaContratos != null)
            {
                // percorre contratos
                foreach (ContratosCCEEDominio contrato in listaContratos)
                {
                    // verifica se contrato desejado
                    if (!Contratos.Any(x => x.IDContratoCCEE == contrato.IDContratoCCEE))
                    {
                        continue;
                    }

                    //
                    // Descrições
                    //

                    // volume
                    ContratosCCEE_VolumeMetodos volumeMetodos = new ContratosCCEE_VolumeMetodos();
                    List<ContratosCCEE_VolumeDominio> volumes = volumeMetodos.ListarPorId(contrato.IDContratoCCEE);

                    // sazonalizacoes
                    ContratosCCEE_SazonalizacaoMetodos sazoMetodos = new ContratosCCEE_SazonalizacaoMetodos();
                    List<ContratosCCEE_SazonalizacaoDominio> sazonalizacoes = sazoMetodos.ListarPorId(contrato.IDContratoCCEE);


                    // calcula número de anos do inicio do contrato até o ano atual
                    int ano_Inicio = contrato.Vigencia_Inicio.Year;
                    int ano_Fim = DateTime.Now.Year;

                    // número de anos
                    int numero_anos = (ano_Fim - ano_Inicio) + 1;

                    // percorre anos
                    for (int ano = 0; ano < numero_anos; ano++)
                    {
                        // adiciona linha
                        row = sheet.CreateRow(rowIndex++);
                        i = 0;

                        textoCelulaXLS(row, i++, contrato.Contrato_ID);
                        textoCelulaXLS(row, i++, contrato.SiglaCCEE);
                        textoCelulaXLS(row, i++, contrato.RazaoSocial);
                        textoCelulaXLS(row, i++, contrato.CNPJ);
                        textoCelulaXLS(row, i++, contrato.Contrato_Codigo);

                        textoCelulaXLS(row, i++, contrato.Vigencia_Inicio.ToString("d"));
                        textoCelulaXLS(row, i++, contrato.Vigencia_Fim.ToString("d"));

                        textoCelulaXLS(row, i++, GetDescricaoAgente(listatiposComercializadoras, contrato.IDComercializadora));

                        // Ano
                        DateTime vigencia_atual = contrato.Vigencia_Inicio.AddYears(ano);
                        DateTime ano_atual = new DateTime(vigencia_atual.Year, 1, 1, 0, 0, 0);
                        numeroCelulaXLS(row, i++, ano_atual.Year, _intCellStyle);

                        // sazonalização do contrato
                        double[] sazo_meses = new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 };

                        // volume
                        if (contrato.Montante_Tipo == 0)
                        {
                            double volume_contratado = 0.0;

                            // montante (MWm)
                            if (volumes != null)
                            {
                                ContratosCCEE_VolumeDominio volume = volumes.Find(item => (item.Data.Year == ano_atual.Year) && (item.Montante_Tipo == contrato.Montante_Tipo));

                                if (volume != null)
                                {
                                    volume_contratado = volume.Contratado;
                                }
                            }

                            numeroCelulaXLS(row, i++, volume_contratado, _6CellStyle);

                            // meses do ano
                            for (int mes = 0; mes < 12; mes++)
                            {
                                // converte MWm em MWh
                                sazo_meses[mes] = Converte_MWm_MWh(mes + 1, ano_atual.Year, volume_contratado);
                            }
                        }
                        else
                        {
                            // sazonalizado (MWh)
                            textoCelulaXLS(row, i++, "Contrato Sazonalizado");

                            if (sazonalizacoes != null)
                            {
                                ContratosCCEE_SazonalizacaoDominio sazo = sazonalizacoes.Find(item => item.Ano == ano_atual.Year);

                                if (sazo != null)
                                {
                                    sazo_meses[0] = sazo.Jan;
                                    sazo_meses[1] = sazo.Fev;
                                    sazo_meses[2] = sazo.Mar;
                                    sazo_meses[3] = sazo.Abr;
                                    sazo_meses[4] = sazo.Mai;
                                    sazo_meses[5] = sazo.Jun;
                                    sazo_meses[6] = sazo.Jul;
                                    sazo_meses[7] = sazo.Ago;
                                    sazo_meses[8] = sazo.Set;
                                    sazo_meses[9] = sazo.Out;
                                    sazo_meses[10] = sazo.Nov;
                                    sazo_meses[11] = sazo.Dez;
                                }
                            }
                        }

                        numeroCelulaXLS(row, i++, sazo_meses[0], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[1], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[2], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[3], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[4], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[5], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[6], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[7], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[8], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[9], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[10], _3CellStyle);
                        numeroCelulaXLS(row, i++, sazo_meses[11], _3CellStyle);
                    }
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // converte MWm em MWh
        private double Converte_MWm_MWh(int mes, int ano, double montante)
        {
            // calcula número de horas do mês
            int num_horas = System.DateTime.DaysInMonth(ano, mes) * 24;

            // calcula contratado MWh do mês
            double contratado = montante * num_horas;

            // retorna sazonalizado do mês
            return (contratado);
        }
    }
}