﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.ServiceProcess;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SmartCom;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;
using System.Threading.Tasks;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class HomeController : Controller
    {
        public ActionResult Inicio_Admin(int IDCliente = 0, int IDMedicao = 0)
        {
            // log Junior
            LogMessage_Junior("Inicio_Admin 1");


            // verifica se enviou IDCliente e IDMedicao
            if (IDCliente > 0 && IDMedicao > 0)
            {
                // le configuracao da medicao
                MedicoesMetodos medicoesMetodos_conf = new MedicoesMetodos();
                MedicoesDominio medicao_conf = medicoesMetodos_conf.ListarPorId(IDMedicao);

                // salva cookie 
                CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
                CookieStore.SalvaCookie_Int("IDTipoMedicao", medicao_conf.IDTipoMedicao);
                CookieStore.SalvaCookie_Int("_IDGateway", medicao_conf.IDGateway);

                // log Junior
                LogMessage_Junior("Inicio_Admin 2");

            }
            else
            {
                // limpa cookies
                CookieStore.SalvaCookie_Int("_IDCliente", -10);
                CookieStore.SalvaCookie_Int("IDTipoContrato", -10);
                CookieStore.SalvaCookie_Int("IDTipoSupervisao", -10);
                CookieStore.SalvaCookie_String("Nome_GrupoUnidades", "Grupo de Unidades");
                CookieStore.SalvaCookie_String("Nome_Unidades", "Unidades");
                CookieStore.SalvaCookie_Int("IDTipoGrafico_Demanda", 0);

                CookieStore.SalvaCookie_Int("_IDMedicao", -10);
                CookieStore.SalvaCookie_Int("IDTipoMedicao", -10);
                CookieStore.SalvaCookie_Int("_IDGateway", -10);
                CookieStore.SalvaCookie_Int("IDTipoGateway", -10);

                // log Junior
                LogMessage_Junior("Inicio_Admin 3");

            }

            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Inicio_Admin");


            //
            // Preenche ViewBag vazio
            //

            // le cookies
            LeCookies_SmartEnergy();

            // arquivos recebidos
            ViewBag.ArquivosRecebidos = 0;

            // gateways em atraso
            ViewBag.GatewaysAtraso = 0;
            ViewBag.GatewaysAtraso_LUMINAE = 0;
            ViewBag.GatewaysAtraso_CPFL_Solucoes = 0;
            ViewBag.GatewaysAtraso_CPFL_Eficiencia = 0;
            ViewBag.GatewaysAtraso_SIMER = 0;

            // medições em atraso
            ViewBag.MedicoesAtraso = 0;

            // consultores
            List<UsuarioDominio> listaConsultores = new List<UsuarioDominio>();
            ViewBag.listaConsultores = listaConsultores;

            // número de gateways bateria fraca
            ViewBag.NumGatewaysBateriaFraca = 0;

            // gateways em atraso
            ViewBag.GatewaysStartup_Atualizado = 0;

            // usuarios sem logar
            ViewBag.ClientesSemLogar_Total = 0;

            // status Broker (Artemis)
            ViewBag.status_Broker = false;

            // status MQTT
            ViewBag.status_SmartMQTT = false;
            ViewBag.status_SmEnergy = false;
            ViewBag.status_SmUpload = false;
            ViewBag.status_SmUpdate = false;

            // grafico
            ViewBag.DataAtual = "01/01/2000";
            ViewBag.DiaSemanaAtual = "-";

            double NumArquivos_max = 0.0;
            var NumArquivos = new double[98];
            var NumArquivos_Processados = new double[98];
            var NumArquivos_Problemas = new double[98];
            var NumArquivos_Tempo = new double[98];
            var Datas = new string[98];
            ViewBag.NumArquivos_max = NumArquivos_max;
            ViewBag.NumArquivos = NumArquivos;
            ViewBag.NumArquivos_Processados = NumArquivos_Processados;
            ViewBag.NumArquivos_Problemas = NumArquivos_Problemas;
            ViewBag.NumArquivos_Tempo = NumArquivos_Tempo;
            ViewBag.Datas = Datas;

            double NumUploads_max = 0.0;
            var NumUploads = new double[98];
            var NumUploads_Processados = new double[98];
            var NumUploads_Problemas = new double[98];
            var DatasUploads = new string[98];
            ViewBag.NumUploads_max = NumUploads_max;
            ViewBag.NumUploads = NumUploads;
            ViewBag.NumUploads_Processados = NumUploads_Processados;
            ViewBag.NumUploads_Problemas = NumUploads_Problemas;
            ViewBag.DatasUploads = DatasUploads;

            ViewBag.MesAtual = "-";

            double NumArquivos_max_Mes = 0.0;
            var NumArquivos_Mes = new double[33];
            var NumArquivos_Problemas_Mes = new double[33];
            var NumUploads_Mes = new double[33];
            var NumUploads_Problemas_Mes = new double[33];
            var Datas_Mes = new string[33];
            int NumDiasMes = 0;
            ViewBag.NumArquivos_max_Mes = NumArquivos_max_Mes;
            ViewBag.NumArquivos_Mes = NumArquivos_Mes;
            ViewBag.NumArquivos_Problemas_Mes = NumArquivos_Problemas_Mes;
            ViewBag.NumUploads_Mes = NumUploads_Mes;
            ViewBag.NumUploads_Problemas_Mes = NumUploads_Problemas_Mes;
            ViewBag.Datas_Mes = Datas_Mes;
            ViewBag.NumDiasMes = NumDiasMes;

            double NumAcessos_max = 0.0;
            var NumAcessos = new double[24];
            var DatasAcessos = new string[24];
            ViewBag.NumAcessos_max = NumAcessos_max;
            ViewBag.NumAcessos = NumAcessos;
            ViewBag.DatasAcessos = DatasAcessos;

            double NumAcessos_max_Mes = 0.0;
            var NumAcessos_Mes = new double[33];
            ViewBag.NumAcessos_max_Mes = NumAcessos_max_Mes;
            ViewBag.NumAcessos_Mes = NumAcessos_Mes;

            // número de cliente, gateway e medições
            ViewBag.NumClientes = 0;
            ViewBag.NumGateways = 0;
            ViewBag.NumMedicoesFisicas = 0;
            ViewBag.NumMedicoesVirtuais = 0;
            ViewBag.NumMedicoes = 0;

            // % gateways em atraso
            ViewBag.GatewaysAtraso_Porc = 0.0;

            // % medições em atraso
            ViewBag.MedicoesAtraso_Porc = 0.0;

            // estatísticas SmartEnergy
            ViewBag.NumDias_90dias = 0;
            ViewBag.NumClientes_90dias = 0;
            ViewBag.NumGateways_90dias = 0;
            ViewBag.NumMedicoes_90dias = 0;

            // registros da estatística SmartEnergy
            List<StatsSmartEnergyDominio> statsSmartEnergy_registros = new List<StatsSmartEnergyDominio>();
            ViewBag.statsSmartEnergy_registros = statsSmartEnergy_registros;


            // log Junior
            LogMessage_Junior("Inicio_Admin 4");


            return View();
        }

        // GET: Atualizar
        public PartialViewResult _Inicio_Admin_Atualizar()
        {
            // log Junior
            LogMessage_Junior("Inicio_Admin_Atualizar 1");

            // calcula
            Calc_Inicio_Admin();

            // log Junior
            LogMessage_Junior("Inicio_Admin_Atualizar 2");

            return PartialView();
        }

        public void Calc_Inicio_Admin()
        {
            // log Junior
            LogMessage_Junior("Calc_Inicio_Admin A");


            // tela de ajuda - inicio admin
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            //
            // Arquivos Recebidos
            //

            // caminho Recebidos
            string CaminhoRecebidos = ConfigurationManager.AppSettings["Recebidos"];

            // numero de arquivos em Recebidos
            string[] list = Directory.GetFiles(CaminhoRecebidos, "*.*");
            ViewBag.ArquivosRecebidos = list.Length;

            // log Junior
            LogMessage_Junior("Calc_Inicio_Admin B");

            //
            // Gestores
            //

            // le consultores
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            List<UsuarioDominio> listaConsultores = usuarioMetodos.ListarTodosConsultores();

            // numero de clientes e gateways por consultor
            if (listaConsultores != null)
            {
                foreach (UsuarioDominio consultor in listaConsultores)
                {
                    // numero de clientes do gestor
                    ClientesMetodos cliMetodos = new ClientesMetodos();
                    consultor.NumClientes = cliMetodos.NumClientesConsultor(consultor.IDUsuario);

                    // numero de gateways do gestor
                    SupervGatewaysMetodos supMetodos = new SupervGatewaysMetodos();
                    consultor.NumGateways = supMetodos.NumGatewaysGestor(consultor.IDUsuario);
                }
            }

            // log Junior
            LogMessage_Junior("Calc_Inicio_Admin C");

            //
            // Gateways em Atraso
            //

            // gateway em atraso
            SupervGatewaysMetodos supervGatewaysMetodos = new SupervGatewaysMetodos();
            List<SupervGatewaysDominio> GatewaysSupervListTodos = new List<SupervGatewaysDominio>();
            GatewaysSupervListTodos = supervGatewaysMetodos.ListarEmAtraso();

            int GatewaysAtraso_Total = 0;
            int GatewaysAtraso_LUMINAE = 0;
            int GatewaysAtraso_CPFL_Solucoes = 0;
            int GatewaysAtraso_CPFL_Eficiencia = 0;
            int GatewaysAtraso_SIMER = 0;

            // verifica se existe
            if (GatewaysSupervListTodos != null)
            {
                // analiso os atrasos
                foreach (SupervGatewaysDominio gateway in GatewaysSupervListTodos)
                {
                    // separa o status
                    string[] separa_Status = gateway.Status.Split('/');

                    // a principio sem falha
                    int TipoAtrasoAtualizacao = 0;

                    // verifica se o status eh nulo ou vazio e se retornou indice de duas strings 
                    if (!String.IsNullOrEmpty(gateway.Status) && separa_Status.Length == 2)
                    {
                        // separa o IDHist da atualizacao do Tipo de atraso
                        string[] separa_TipoAtrasoAtualizacao = separa_Status[0].Split(' ');

                        // verifica se retornou indice de duas strings
                        if (separa_TipoAtrasoAtualizacao.Length == 2)
                        {
                            // verifica se o tipo de atraso nao eh nulo ou vazio
                            if (!String.IsNullOrEmpty(separa_TipoAtrasoAtualizacao[1]))
                            {
                                // copio o tipo de atraso
                                TipoAtrasoAtualizacao = int.Parse(separa_TipoAtrasoAtualizacao[1]);
                            }
                        }
                    }

                    //
                    // Atraso de Atualizacao
                    //

                    // verifica tipo de atraso
                    switch (TipoAtrasoAtualizacao)
                    {
                        case 1: // atraso menor que 3 dias
                        case 2: // atraso 3 a 7 dias
                        case 3: // atraso 7 a 23 dias
                        case 4: // atraso maior que 23 dias

                            // total
                            GatewaysAtraso_Total++;

                            // verifica se LUMINAE
                            if (gateway.IDConsultor == CLIENTES_ESPECIAIS.LUMINAE)
                            {
                                // total LUMINAE
                                GatewaysAtraso_LUMINAE++;
                            }

                            // verifica se CPFL Soluções
                            if (gateway.IDConsultor == CLIENTES_ESPECIAIS.CPFL)
                            {
                                // total CPFL Soluções
                                GatewaysAtraso_CPFL_Solucoes++;
                            }

                            // verifica se CPFL Eficiência
                            if (gateway.IDConsultor == CLIENTES_ESPECIAIS.CPFL_EFICIENCIA)
                            {
                                // total CPFL Eficiência
                                GatewaysAtraso_CPFL_Eficiencia++;
                            }

                            // verifica se SIMER
                            if (gateway.IDConsultor == CLIENTES_ESPECIAIS.GESTOR_SIMER)
                            {
                                // total SIMER
                                GatewaysAtraso_SIMER++;
                            }

                            // verifica se é consultor
                            if (gateway.IDConsultor > 0)
                            {
                                // procura na lista de consultores
                                UsuarioDominio consultor = listaConsultores.Find(x => x.IDUsuario == gateway.IDConsultor);

                                if (consultor != null)
                                {
                                    consultor.NumGateways_Atraso++;
                                }
                            }

                            break;
                    }
                }
            }

            // log Junior
            LogMessage_Junior("Calc_Inicio_Admin D");

            // gateways em atraso
            ViewBag.GatewaysAtraso = GatewaysAtraso_Total;
            ViewBag.GatewaysAtraso_LUMINAE = GatewaysAtraso_LUMINAE;
            ViewBag.GatewaysAtraso_CPFL_Solucoes = GatewaysAtraso_CPFL_Solucoes;
            ViewBag.GatewaysAtraso_CPFL_Eficiencia = GatewaysAtraso_CPFL_Eficiencia;
            ViewBag.GatewaysAtraso_SIMER = GatewaysAtraso_SIMER;

            // porcentagem atraso por consultor
            if (listaConsultores != null)
            {
                foreach (UsuarioDominio consultor in listaConsultores)
                {
                    // % gateways em atraso
                    consultor.GatewaysAtraso_Porc = 0.0;
                    if (consultor.NumGateways > 0)
                    {
                        consultor.GatewaysAtraso_Porc = (consultor.NumGateways_Atraso / (double)consultor.NumGateways) * 100.0;
                    }
                }
            }

            // consultores
            ViewBag.listaConsultores = listaConsultores;

            // log Junior
            LogMessage_Junior("Calc_Inicio_Admin E");


            //
            // Medições em Atraso
            //

            // medições em atraso
            MedicoesSupervMetodos medicoesSupervMetodos = new MedicoesSupervMetodos();
            int MedicoesAtraso_Total = medicoesSupervMetodos.NumMedicoesAtraso();

            // medições em atraso
            ViewBag.MedicoesAtraso = MedicoesAtraso_Total;

            // log Junior
            LogMessage_Junior("Calc_Inicio_Admin E1");


            //
            // Gateways Bateria Fraca
            //

            // número de gateways bateria fraca
            SupervGatewaysBateriaFracaMetodos gatewaysBateriaFracaMetodos = new SupervGatewaysBateriaFracaMetodos();
            int NumGatewaysBateriaFraca = gatewaysBateriaFracaMetodos.NumGatewaysBateriaFraca();
            ViewBag.NumGatewaysBateriaFraca = NumGatewaysBateriaFraca;

            // log Junior
            LogMessage_Junior("Calc_Inicio_Admin F");


            //
            // Gateways Startup nao realizado
            //

            // gateway startup nao realizado com atualizacao < 2 dias
            int GatewaysStartup_Atualizado = 0;

            // leio lista de supervisao gateways
            GatewaysSupervListTodos = supervGatewaysMetodos.ListarTodos(1);

            // verifica se existe
            if (GatewaysSupervListTodos != null)
            {
                // analiso os atrasos
                foreach (SupervGatewaysDominio gateway in GatewaysSupervListTodos)
                {
                    // diferenca
                    TimeSpan diferenca = DateTime.Now - gateway.DataHora;

                    // verifica se atraso menor que 2 dias
                    if (diferenca.Days <= 2)
                    {
                        // total
                        GatewaysStartup_Atualizado++;
                    }
                }
            }

            // gateways em atraso
            ViewBag.GatewaysStartup_Atualizado = GatewaysStartup_Atualizado;

            // log Junior
            LogMessage_Junior("Calc_Inicio_Admin G");


            //
            // Clientes que nao se logaram nos ultimos 3 meses
            //

            // clientes sem logar
            int ClientesSemLogar_Total = 0;

            // clientes
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> clientes = clienteMetodos.ListarTodos();

            if (clientes != null)
            {
                foreach (ClientesDominio cliente in clientes)
                {
                    // numero de usuarios do cliente
                    int num_usuarios = usuarioMetodos.NumUsuarios(cliente.IDCliente);

                    // usuarios sem logar
                    List<UsuarioDominio> usuarios = usuarioMetodos.ListarPorQuemNaoLogou(cliente.IDCliente);

                    if (usuarios != null)
                    {
                        // verifica se todos os usuarios nao se logaram
                        if (usuarios.Count > 0 && num_usuarios == usuarios.Count)
                        {
                            // incrementa
                            ClientesSemLogar_Total++;
                        }
                    }
                }
            }

            // usuarios sem logar
            ViewBag.ClientesSemLogar_Total = ClientesSemLogar_Total;


            // log Junior
            LogMessage_Junior("Calc_Inicio_Admin H");


            //
            // Status de processos
            //

            // verifica Broker (Mosquitto)
            //string aProcName = "mosquitto";

            // verifica Broker (Artemis)
            bool status_Broker = false;
            string aProcName = "artemis-broker-0.0.0.0";
            if (verificarServicoAtivo(aProcName))
            {
                // serviço running
                status_Broker = true;
            }

            ViewBag.status_Broker = status_Broker;

            // verifica SmartMQTT
            bool status_SmartMQTT = false;
            aProcName = "SmartMQTT";
            if (verificarProcessoAtivo(aProcName))
            {
                // programa em execução
                status_SmartMQTT = true;
            }
            ViewBag.status_SmartMQTT = status_SmartMQTT;

            // API Artemis Broker - listAllConsumers
            ArtemisBroker artemis = new ArtemisBroker();
            bool status_SmEnergy = artemis.Consumer_Conectado("SmEnergy");
            bool status_SmUpload = artemis.Consumer_Conectado("SmUpload");
            bool status_SmUpdate = artemis.Consumer_Conectado(ConfigurationManager.AppSettings["SmUpdate_Topico_Subscribe"]);

            ViewBag.status_SmEnergy = status_SmEnergy;
            ViewBag.status_SmUpload = status_SmUpload;
            ViewBag.status_SmUpdate = status_SmUpdate;


            // log Junior
            LogMessage_Junior("Calc_Inicio_Admin I");

            //
            // Estatisticas de Processamento - Graficos
            //
            GraficoProcessamento();

            // log Junior
            LogMessage_Junior("Calc_Inicio_Admin J");


            //
            // Estatisticas SmartEnergy
            //

            // lista de clientes
            List<int> ConfigCliList = ViewBag._ConfigCli;

            // le clientes - somente ativos
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clientesMetodos.ListarTodos(ConfigCliList, 1);

            // numero de clientes
            int NumClientes_total = 0;

            // numero de medicoes e gateways
            int NumGateways_total = 0;
            int NumMedicoes_total = 0;
            int NumMedicoesFisicas_total = 0;
            int NumMedicoesVirtuais_total = 0;

            // percorre clientes e descobre numero de medicoes associadas
            foreach (ClientesDominio cliente in listaClientes)
            {
                // menos o cliente zero
                if (cliente.IDCliente == 0)
                {
                    continue;
                }

                // número de clientes
                NumClientes_total++;

                // numero de empresas com este cliente (CPFL - Unidades Consumidoras)
                EmpresasMetodos empresasMetodos = new EmpresasMetodos();
                int NumEmpresas = empresasMetodos.NumEmpresasCliente(cliente.IDCliente);
                cliente.NumEmpresas = NumEmpresas;

                // numero de medicoes REAIS com este cliente
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                int NumMedicoesFisicas = medicoesMetodos.NumMedicoesClienteTipo(cliente.IDCliente, 1);
                NumMedicoesFisicas_total += NumMedicoesFisicas;
                cliente.NumMedicoes = NumMedicoesFisicas;

                // numero de medicoes VIRTUAIS com este cliente
                int NumMedicoesVirtuais = medicoesMetodos.NumMedicoesClienteTipo(cliente.IDCliente, 2);
                NumMedicoesVirtuais_total += NumMedicoesVirtuais;

                // numero de gateways com este cliente
                GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
                int NumGateways = gatewayMetodos.NumGatewaysCliente(cliente.IDCliente);
                NumGateways_total += NumGateways;
                cliente.NumGateways = NumGateways;
            }

            ViewBag.NumClientes = NumClientes_total;
            ViewBag.NumGateways = NumGateways_total;
            ViewBag.NumMedicoesFisicas = NumMedicoesFisicas_total;
            ViewBag.NumMedicoesVirtuais = NumMedicoesVirtuais_total;


            // log Junior
            LogMessage_Junior("Calc_Inicio_Admin K");


            // % gateways em atraso
            double GatewaysAtraso_Porc = 0.0;
            if (NumGateways_total > 0)
            {
                GatewaysAtraso_Porc = (GatewaysAtraso_Total / (double)NumGateways_total) * 100.0;
            }
            ViewBag.GatewaysAtraso_Porc = GatewaysAtraso_Porc;


            // % medições em atraso
            double MedicoesAtraso_Porc = 0.0;
            if (NumMedicoesFisicas_total > 0)
            {
                MedicoesAtraso_Porc = (MedicoesAtraso_Total / (double)NumMedicoesFisicas_total) * 100.0;
            }
            ViewBag.MedicoesAtraso_Porc = MedicoesAtraso_Porc;


            // numero de medicoes
            NumMedicoes_total = NumMedicoesFisicas_total + NumMedicoesVirtuais_total;
            ViewBag.NumMedicoes = NumMedicoes_total;


            // log Junior
            LogMessage_Junior("Calc_Inicio_Admin L");


            // estatisticas SmartEnergy
            StatsSmartEnergyMetodos statsSmartEnergyMetodos = new StatsSmartEnergyMetodos();
            List<StatsSmartEnergyDominio> statsSmartEnergy = statsSmartEnergyMetodos.Ultimos90Dias();

            int NumClientes_90dias = 0;
            int NumGateways_90dias = 0;
            int NumMedicoes_90dias = 0;
            int NumDias_90dias = 0;

            if( statsSmartEnergy != null )
            {
                if( statsSmartEnergy.Count > 0 )
                {
                    NumClientes_90dias = statsSmartEnergy[0].num_clientes;
                    NumGateways_90dias = statsSmartEnergy[0].num_gateways;
                    NumMedicoes_90dias = statsSmartEnergy[0].num_medicoes;

                    TimeSpan diferenca = DateTime.Now - statsSmartEnergy[0].DataHora;
                    NumDias_90dias = diferenca.Days;
                }
            }

            ViewBag.NumDias_90dias = NumDias_90dias;
            ViewBag.NumClientes_90dias = NumClientes_total - NumClientes_90dias;
            ViewBag.NumGateways_90dias = NumGateways_total - NumGateways_90dias;
            ViewBag.NumMedicoes_90dias = NumMedicoes_total - NumMedicoes_90dias;

            // registros da estatística SmartEnergy
            List<StatsSmartEnergyDominio> statsSmartEnergy_registros = new List<StatsSmartEnergyDominio>();

            // estatisticas SmartEnergy
            statsSmartEnergy_registros = statsSmartEnergyMetodos.ListarTodos2Anos();
            ViewBag.statsSmartEnergy_registros = statsSmartEnergy_registros;

            // log Junior
            LogMessage_Junior("Calc_Inicio_Admin M");


            return;
        }

        private bool verificarProcessoAtivo(string nomeProcesso)
        {
            try
            {
                // Cria um array de processos - Lembrar de importar o namespace System.Diagnostics
                Process[] listaProcessos = Process.GetProcessesByName(nomeProcesso);

                if (listaProcessos != null)
                {
                    // número de processos
                    int NumProcessos = listaProcessos.Count();

                    // verifica se tem algum processo
                    if (NumProcessos > 0)
                    {
                        // log
                        LogMessage(string.Format("[{0}] Processo OK", nomeProcesso));

                        // encontrou processo
                        return true;
                    }
                }

                // log
                LogMessage(string.Format("[{0}] ERRO: Não encontrou processo", nomeProcesso));

                // não encontrou o processo, retorna false
                return false;
            }
            catch (Exception ex)
            {
                // log
                LogMessage(string.Format("[{0}] ERRO: Excessão em 'verificarProcessoAtivo' [{1}]", nomeProcesso, ex.Message));

                // não encontrou o processo, retorna false
                return false;
            }
        }

        private bool verificarServicoAtivo(string nomeServico)
        {
            // status
            string status = "Desconhecido";

            try
            {
                // Verifica servico - Lembrar de importar o namespace System.ServiceProcess
                ServiceController sc = new ServiceController(nomeServico);

                if (sc != null)
                {
                    // verifica status
                    switch (sc.Status)
                    {
                        case ServiceControllerStatus.Running:

                            // log
                            LogMessage(string.Format("[{0}] Serviço Running", nomeServico.Substring(0, 9)));

                            // serviço running
                            return true;

                        case ServiceControllerStatus.Stopped:
                            status = "Stopped";
                            break;

                        case ServiceControllerStatus.Paused:
                            status = "Paused";
                            break;

                        case ServiceControllerStatus.StopPending:
                            status = "Stopping";
                            break;

                        case ServiceControllerStatus.StartPending:
                            status = "Starting";
                            break;

                        default:
                            status = "Status Changing";
                            break;
                    }
                }

                // log
                LogMessage(string.Format("[{0}] ERRO: Serviço {1}", nomeServico.Substring(0, 9), status));

                // serviço não running, retorna false
                return false;
            }
            catch (Exception ex)
            {
                // log
                LogMessage(string.Format("[{0}] ERRO: Excessão em 'verificarServicoAtivo' [{1}]", nomeServico.Substring(0, 9), ex.Message));

                // serviço não running, retorna false
                return false;
            }
        }

        private void LogMessage(string msg)
        {
            // log
            Funcoes_Log.Mensagem("SmartEnergy_LogProcessos", string.Format("[Home_Inicio_Admin ] {0}", msg));
        }

        // GET: Grafico processados 
        public PartialViewResult _Inicio_Admin_GraficoProcessados(int Navegacao = 0)
        {
            //
            // Verifica navegacao
            //

            // le cookie datahora (atual ou inicial)
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Home_Data");

            // verifica se existe datahora no cookie
            if (datahora_cookie.Year == 2000)
            {
                // datahora
                datahora_cookie = DateTime.Now;
            }

            // navega
            switch (Navegacao)
            {
                case -1:    // dia anterior

                    datahora_cookie = datahora_cookie.AddDays(-1);
                    break;

                case 1:    // dia seguinte

                    datahora_cookie = datahora_cookie.AddDays(1);
                    break;

                case -3:    // mes anterior

                    datahora_cookie = datahora_cookie.AddMonths(-1);
                    break;

                case 3:    // mes seguinte

                    datahora_cookie = datahora_cookie.AddMonths(1);
                    break;

                case 10:    // ultimo

                    // datahora
                    datahora_cookie = DateTime.Now;
                    break;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Home_Data", datahora_cookie);

            //
            // Estatisticas de Processamento - Graficos
            //
            GraficoProcessamento();

            return PartialView();
        }

        private void GraficoProcessamento()
        {

            //
            // Estatisticas de Processamento FTP - Dia
            //

            // log Junior
            LogMessage_Junior("GraficoProcessamento 1");


            // le cookie datahora (atual ou inicial)
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Home_Data");

            // verifica se existe datahora no cookie
            if (datahora_cookie.Year == 2000)
            {
                // datahora
                datahora_cookie = DateTime.Now;
            }

            // salva cookie datahora (final)
            CookieStore.SalvaCookie_Datahora("Home_Data", datahora_cookie);

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", datahora_cookie);

            // dia da semana
            string day = new CultureInfo("pt-BR").DateTimeFormat.GetDayName(datahora_cookie.DayOfWeek);
            string diaExtenso = char.ToUpper(day[0]) + day.Substring(1);
            ViewBag.DiaSemanaAtual = diaExtenso;

            // estatistica de processamento FTP - hoje
            StatsProcessamentoMetodos statsMetodos = new StatsProcessamentoMetodos();
            List<StatsProcessamentoDominio> stats_hoje_FTP = statsMetodos.Dia(datahora_cookie);

            // log Junior
            LogMessage_Junior("GraficoProcessamento 2");


            int i = 0;

            if (stats_hoje_FTP != null)
            {
                // grafico
                var NumArquivos = new double[98];
                var NumArquivos_Processados = new double[98];
                var NumArquivos_Problemas = new double[98];
                var NumArquivos_Tempo = new double[98];
                var Datas = new string[98];

                double NumArquivos_max = 0.0;

                // valores
                DateTime hoje = datahora_cookie;
                DateTime strDataIni = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 0, 0);
                DateTime inicioDoDia = strDataIni;

                for (i = 0; i < 98; i++)
                {
                    // formata label
                    Datas[i] = strDataIni.ToString("yyyy-MM-dd HH:mm:ss");
                    DateTime strDataFim = strDataIni.AddMinutes(15);

                    // zera valor
                    NumArquivos[i] = 0;
                    NumArquivos_Processados[i] = 0;
                    NumArquivos_Problemas[i] = 0;
                    NumArquivos_Tempo[i] = 0;

                    if (i==0 || i==97)
                    {
                        continue;
                    }

                    // busca horario
                    List<StatsProcessamentoDominio> inside = stats_hoje_FTP.Where(item => (item.DataHora > strDataIni && item.DataHora <= strDataFim)).ToList();

                    if (inside != null)
                    {
                        foreach( StatsProcessamentoDominio stat in inside)
                        {
                            NumArquivos[i] += stat.num_arquivos;
                            NumArquivos_Processados[i] += stat.num_processados;
                            NumArquivos_Problemas[i] += stat.num_problemas;
                            NumArquivos_Tempo[i] += stat.tempo_processamento;
                        }
                    }

                    // proximo 15 minutos
                    strDataIni = strDataIni.AddMinutes(15);

                    // verifica valor maximo
                    if (NumArquivos[i] > NumArquivos_max)
                    {
                        NumArquivos_max = NumArquivos[i];
                    }
                }
                

                NumArquivos_max = NumArquivos_max * 1.1;

                if (NumArquivos_max < 1.0)
                {
                    NumArquivos_max = 10.0;
                }

                ViewBag.NumArquivos_max = NumArquivos_max;

                ViewBag.NumArquivos = NumArquivos;
                ViewBag.NumArquivos_Processados = NumArquivos_Processados;
                ViewBag.NumArquivos_Problemas = NumArquivos_Problemas;
                ViewBag.NumArquivos_Tempo = NumArquivos_Tempo;
                ViewBag.Datas = Datas;
            }

            // log Junior
            LogMessage_Junior("GraficoProcessamento 3");



            //
            // Estatisticas de Processamento MQTT - Dia
            //

            // estatistica de processamento MQTT - hoje
            StatsProcessamentoMQTTMetodos statsMQTTMetodos = new StatsProcessamentoMQTTMetodos();
            List<StatsProcessamentoMQTTDominio> stats_hoje_MQTT = statsMQTTMetodos.Dia(datahora_cookie);

            // log Junior
            LogMessage_Junior("GraficoProcessamento 4");

            i = 0;

            if (stats_hoje_MQTT != null)
            {
                // grafico
                var NumUploads = new double[98];
                var NumUploads_Processados = new double[98];
                var NumUploads_Problemas = new double[98];
                var DatasUploads = new string[98];

                double NumUploads_max = 0.0;

                // valores
                DateTime hoje = datahora_cookie;
                DateTime strDataIni = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 0, 0);
                DateTime inicioDoDia = strDataIni;

                for (i = 0; i < 98; i++)
                {
                    // formata label
                    DatasUploads[i] = strDataIni.ToString("yyyy-MM-dd HH:mm:ss");
                    DateTime strDataFim = strDataIni.AddMinutes(15);

                    // zera valor
                    NumUploads[i] = 0;
                    NumUploads_Processados[i] = 0;
                    NumUploads_Problemas[i] = 0;

                    if (i == 0 || i == 97)
                    {
                        continue;
                    }

                    // busca horario
                    List<StatsProcessamentoMQTTDominio> inside = stats_hoje_MQTT.Where(item => (item.DataHora > strDataIni && item.DataHora <= strDataFim)).ToList();

                    if (inside != null)
                    {
                        foreach (StatsProcessamentoMQTTDominio stat in inside)
                        {
                            NumUploads[i] += stat.num_arquivos;
                            NumUploads_Processados[i] += stat.num_processados;
                            NumUploads_Problemas[i] += stat.num_problemas;
                        }
                    }

                    // proximo 15 minutos
                    strDataIni = strDataIni.AddMinutes(15);

                    // verifica valor maximo
                    if (NumUploads[i] > NumUploads_max)
                    {
                        NumUploads_max = NumUploads[i];
                    }
                }

                NumUploads_max = NumUploads_max * 1.1;

                if (NumUploads_max < 1.0)
                {
                    NumUploads_max = 10.0;
                }

                ViewBag.NumUploads_max = NumUploads_max;

                ViewBag.NumUploads = NumUploads;
                ViewBag.NumUploads_Processados = NumUploads_Processados;
                ViewBag.NumUploads_Problemas = NumUploads_Problemas;
                ViewBag.DatasUploads = DatasUploads;
            }

            // log Junior
            LogMessage_Junior("GraficoProcessamento 5");


            //
            // Estatisticas de Processamento - Mes
            //

            // mes atual
            ViewBag.MesAtual = string.Format("{0:MMMM} de {0:yyyy}", datahora_cookie);

            // grafico mes
            var NumArquivos_Mes = new double[33];
            var NumArquivos_Problemas_Mes = new double[33];
            var NumUploads_Mes = new double[33];
            var NumUploads_Problemas_Mes = new double[33];
            var Datas_Mes = new string[33];

            double NumArquivos_max_Mes = 0.0;

            // valores
            DateTime hoje_Mes = datahora_cookie;
            DateTime strDataIni_Mes = new DateTime(hoje_Mes.Year, hoje_Mes.Month, 1, 0, 5, 0);

            // volta 1 dia para a primeira barra
            strDataIni_Mes = strDataIni_Mes.AddDays(-1);

            // numero de dias do mes
            int NumDiasMes = DateTime.DaysInMonth(hoje_Mes.Year, hoje_Mes.Month);


            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // formata label
                Datas_Mes[i] = strDataIni_Mes.ToString("yyyy-MM-dd HH:mm:ss");

                // zera valor
                NumArquivos_Mes[i] = 0;
                NumArquivos_Problemas_Mes[i] = 0;
                NumUploads_Mes[i] = 0;
                NumUploads_Problemas_Mes[i] = 0;

                // le dia FTP
                StatsProcessamentoTotal stats_dia = statsMetodos.TotalDia(strDataIni_Mes);
                NumArquivos_Mes[i] = stats_dia.num_arquivos;
                NumArquivos_Problemas_Mes[i] = stats_dia.num_problemas;

                // le dia MQTT
                StatsProcessamentoTotal statsMQTT_dia = statsMQTTMetodos.TotalDia(strDataIni_Mes);
                NumUploads_Mes[i] = statsMQTT_dia.num_arquivos;
                NumUploads_Problemas_Mes[i] = statsMQTT_dia.num_problemas;

                // proximo dia
                strDataIni_Mes = strDataIni_Mes.AddDays(1);

                // verifica valor maximo
                if ((NumArquivos_Mes[i] + NumUploads_Mes[i]) > NumArquivos_max_Mes && (i > 0 && i < (NumDiasMes+2)-1))
                {
                    NumArquivos_max_Mes = NumArquivos_Mes[i] + NumUploads_Mes[i];
                }
            }

            NumArquivos_max_Mes = NumArquivos_max_Mes * 1.1;

            if (NumArquivos_max_Mes < 1.0)
            {
                NumArquivos_max_Mes = 10.0;
            }

            ViewBag.NumArquivos_max_Mes = NumArquivos_max_Mes;

            ViewBag.NumArquivos_Mes = NumArquivos_Mes;
            ViewBag.NumArquivos_Problemas_Mes = NumArquivos_Problemas_Mes;
            ViewBag.NumUploads_Mes = NumUploads_Mes;
            ViewBag.NumUploads_Problemas_Mes = NumUploads_Problemas_Mes;
            ViewBag.Datas_Mes = Datas_Mes;
            ViewBag.NumDiasMes = NumDiasMes;

            // log Junior
            LogMessage_Junior("GraficoProcessamento 6");


            //
            // Estatisticas de Páginas de Acesso - Dia
            //

            // estatistica de página de acesso - hoje
            HistoricoPaginasVisitadasMetodos paginasMetodos = new HistoricoPaginasVisitadasMetodos();
            List<int> acessos = paginasMetodos.Dia(datahora_cookie);

            i = 0;

            if (acessos != null)
            {
                // grafico
                var NumAcessos = new double[24];
                var DatasAcessos = new string[24];

                double NumAcessos_max = 0.0;

                // valores
                DateTime hoje = datahora_cookie;
                DateTime strDataIni = new DateTime(hoje.Year, hoje.Month, hoje.Day, 1, 0, 0);

                // volta uma hora para a primeira barra
                strDataIni = strDataIni.AddHours(-1);

                for (i = 0; i < 24; i++)
                {
                    // formata label
                    DatasAcessos[i] = strDataIni.ToString("yyyy-MM-dd HH:mm:ss");

                    // zera valor
                    NumAcessos[i] = acessos[i];

                    // proximo hora
                    strDataIni = strDataIni.AddHours(1);

                    // verifica valor maximo
                    if (NumAcessos[i] > NumAcessos_max && (i > 0 && i < 23))
                    {
                        NumAcessos_max = NumAcessos[i];
                    }
                }

                NumAcessos_max = NumAcessos_max * 1.1;

                if (NumAcessos_max < 1.0)
                {
                    NumAcessos_max = 10.0;
                }

                ViewBag.NumAcessos_max = NumAcessos_max;

                ViewBag.NumAcessos = NumAcessos;
                ViewBag.DatasAcessos = DatasAcessos;
            }


            // log Junior
            LogMessage_Junior("GraficoProcessamento 7");

            //
            // Estatisticas de Páginas de Acesso - Mes
            //

            // grafico mes
            var NumAcessos_Mes = new double[33];

            double NumAcessos_max_Mes = 0.0;

            // valores
            strDataIni_Mes = new DateTime(hoje_Mes.Year, hoje_Mes.Month, 1, 0, 5, 0);

            // volta 1 dia para a primeira barra
            strDataIni_Mes = strDataIni_Mes.AddDays(-1);

            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // zera valor
                NumAcessos_Mes[i] = 0;

                // le dia FTP
                NumAcessos_Mes[i] = paginasMetodos.TotalDia(strDataIni_Mes);

                // proximo dia
                strDataIni_Mes = strDataIni_Mes.AddDays(1);

                // verifica valor maximo
                if (NumAcessos_Mes[i] > NumAcessos_max_Mes && (i > 0 && i < (NumDiasMes + 2) - 1))
                {
                    NumAcessos_max_Mes = NumAcessos_Mes[i];
                }
            }

            NumAcessos_max_Mes = NumAcessos_max_Mes * 1.1;

            if (NumAcessos_max_Mes < 1.0)
            {
                NumAcessos_max_Mes = 10.0;
            }

            ViewBag.NumAcessos_max_Mes = NumAcessos_max_Mes;

            ViewBag.NumAcessos_Mes = NumAcessos_Mes;

            // log Junior
            LogMessage_Junior("GraficoProcessamento 8");

            return;
        }


        // log Junior
        public void LogMessage_Junior(string msg)
        {
            // log
            Funcoes_Log.Mensagem("Junior", msg);
        }
    }
}