﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class HomeController
    {
        public ActionResult GestorAnaliseGateways(int IDConsultor)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // gateways em atraso do gestor
            int NumGatewaysGestor = 0;
            int NumGatewaysAtrasoGestor = 0;

            // gestor
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio gestor = usuarioMetodos.ListarPorId(IDConsultor);
            ViewBag.gestor = gestor;

            // leio lista de supervisao gateways
            SupervGatewaysMetodos gatewaysMetodos = new SupervGatewaysMetodos();
            List<SupervGatewaysDominio> GatewaysAtrasoList = new List<SupervGatewaysDominio>();
            List<SupervGatewaysDominio> GatewaysSupervListGestor = new List<SupervGatewaysDominio>();
            GatewaysSupervListGestor = gatewaysMetodos.ListarGestor(IDConsultor);

            // verifica se existe
            if (GatewaysSupervListGestor != null)
            {
                // analiso os atrasos
                foreach (SupervGatewaysDominio gateway in GatewaysSupervListGestor)
                {
                    // separa o status
                    string[] separa_Status = gateway.Status.Split('/');

                    // a principio sem falha
                    int TipoAtrasoAtualizacao = 0;
                    int TipoAtrasoRelogio = 0;

                    // verifica se o status eh nulo ou vazio e se retornou indice de duas strings 
                    if (!String.IsNullOrEmpty(gateway.Status) && separa_Status.Length == 2)
                    {
                        // separa o IDHist da atualizacao do Tipo de atraso
                        string[] separa_TipoAtrasoAtualizacao = separa_Status[0].Split(' ');

                        // verifica se retornou indice de duas strings
                        if (separa_TipoAtrasoAtualizacao.Length == 2)
                        {
                            // verifica se o tipo de atraso nao eh nulo ou vazio
                            if (!String.IsNullOrEmpty(separa_TipoAtrasoAtualizacao[1]))
                            {
                                // copio o tipo de atraso
                                TipoAtrasoAtualizacao = int.Parse(separa_TipoAtrasoAtualizacao[1]);
                            }
                        }

                        // separa o IDHist do relogio do Tipo de atraso
                        string[] separa_TipoAtrasoRelogio = separa_Status[1].Split(' ');

                        // verifica se retornou indice de duas strings
                        if (separa_TipoAtrasoRelogio.Length == 2)
                        {
                            // verifica se o tipo de atraso nao eh nulo ou vazio
                            if (!String.IsNullOrEmpty(separa_TipoAtrasoRelogio[1]))
                            {
                                // copio o tipo de atraso
                                TipoAtrasoRelogio = int.Parse(separa_TipoAtrasoRelogio[1]);
                            }
                        }
                    }

                    //
                    // Atraso de Atualizacao
                    //

                    SupervGatewaysDominio gateway_AtrasoAtualizacao = new SupervGatewaysDominio(gateway);

                    // sinal
                    int pos = gateway_AtrasoAtualizacao.StatusEq.IndexOf("SQ=");
                    double sinal = -1.0;
                    gateway_AtrasoAtualizacao.Sinal = 0;

                    if (pos >= 0)
                    {
                        string str = gateway_AtrasoAtualizacao.StatusEq.Substring(pos + 3);
                        sinal = double.Parse(str);

                        if (sinal >= 99.0)
                        {
                            sinal = 0.0;
                        }
                        else
                        {
                            sinal = (sinal / 31.0) * 100.0;
                        }
                    }

                    // copia sinal
                    if (sinal > 0.0)
                    {
                        // copia sinal
                        gateway_AtrasoAtualizacao.Sinal = (int)sinal;
                    }


                    // copia
                    gateway_AtrasoAtualizacao.TipoAtraso = TipoAtrasoAtualizacao;


                    // verifica se em atraso
                    switch (gateway_AtrasoAtualizacao.TipoAtraso)
                    {
                        case 1: // atraso menor que 3 dias
                        case 2: // atraso 3 a 7 dias
                        case 3: // atraso 7 a 23 dias
                        case 4: // atraso maior que 23 dias

                            // gateways em atraso do gestor
                            NumGatewaysAtrasoGestor++;
                            break;

                        case 5: // relogio da gateway em atraso
                        case 6: // faltam arquivos (nao recebeu arquivo de eventos)
                            break;
                    }

                    // inclui atraso
                    GatewaysAtrasoList.Add(gateway_AtrasoAtualizacao);

                    // número de gateways
                    NumGatewaysGestor++;
                }
            }

            //
            // Gestor
            //

            // lista de gateways em atraso do gestor
            ViewBag.GatewaysAtrasoList = GatewaysAtrasoList;

            // gateways em atraso do gestor
            ViewBag.NumGatewaysAtrasoGestor = NumGatewaysAtrasoGestor;

            // % gateways em atraso do gestor
            double GatewaysAtrasoGestor_Porc = 0.0;
            if (NumGatewaysGestor > 0)
            {
                GatewaysAtrasoGestor_Porc = (NumGatewaysAtrasoGestor / (double)NumGatewaysGestor) * 100.0;
            }
            ViewBag.NumGatewaysGestor = NumGatewaysGestor;
            ViewBag.GatewaysAtrasoGestor_Porc = GatewaysAtrasoGestor_Porc;


            //
            // Total
            //

            //
            // Gateways em Atraso
            //

            // gateway em atraso
            SupervGatewaysMetodos supervGatewaysMetodos = new SupervGatewaysMetodos();
            List<SupervGatewaysDominio> GatewaysSupervListTodos = new List<SupervGatewaysDominio>();
            GatewaysSupervListTodos = supervGatewaysMetodos.ListarEmAtraso();

            int GatewaysAtraso_Total = 0;

            // verifica se existe
            if (GatewaysSupervListTodos != null)
            {
                // analiso os atrasos
                foreach (SupervGatewaysDominio gateway in GatewaysSupervListTodos)
                {
                    // separa o status
                    string[] separa_Status = gateway.Status.Split('/');

                    // a principio sem falha
                    int TipoAtrasoAtualizacao = 0;

                    // verifica se o status eh nulo ou vazio e se retornou indice de duas strings 
                    if (!String.IsNullOrEmpty(gateway.Status) && separa_Status.Length == 2)
                    {
                        // separa o IDHist da atualizacao do Tipo de atraso
                        string[] separa_TipoAtrasoAtualizacao = separa_Status[0].Split(' ');

                        // verifica se retornou indice de duas strings
                        if (separa_TipoAtrasoAtualizacao.Length == 2)
                        {
                            // verifica se o tipo de atraso nao eh nulo ou vazio
                            if (!String.IsNullOrEmpty(separa_TipoAtrasoAtualizacao[1]))
                            {
                                // copio o tipo de atraso
                                TipoAtrasoAtualizacao = int.Parse(separa_TipoAtrasoAtualizacao[1]);
                            }
                        }
                    }

                    //
                    // Atraso de Atualizacao
                    //

                    // verifica tipo de atraso
                    switch (TipoAtrasoAtualizacao)
                    {
                        case 1: // atraso menor que 3 dias
                        case 2: // atraso 3 a 7 dias
                        case 3: // atraso 7 a 23 dias
                        case 4: // atraso maior que 23 dias

                            // total
                            GatewaysAtraso_Total++;
                            break;

                        case 5: // relogio da gateway em atraso
                        case 6: // faltam arquivos (nao recebeu arquivo de eventos)
                            break;
                    }
                }
            }

            // numero de gateways
            GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
            int NumGateways = gatewayMetodos.NumGateways();
            ViewBag.NumGateways = NumGateways;

            // % gateways em atraso
            double GatewaysAtraso_Porc = 0.0;
            if (NumGateways > 0)
            {
                GatewaysAtraso_Porc = (GatewaysAtraso_Total / (double)NumGateways) * 100.0;
            }
            ViewBag.GatewaysAtraso_Porc = GatewaysAtraso_Porc;
            ViewBag.NumGatewaysAtraso = GatewaysAtraso_Total;


            // observacao tags
            List<ObservacaoTagsDominio> tags = new List<ObservacaoTagsDominio>();
            ViewBag.tags = tags;

            // observacao
            ObservacaoGatewayDominio observacao = new ObservacaoGatewayDominio();
            int IDGatewaySelecionado = 0;

            ViewBag.observacao = observacao;
            ViewBag.IDGatewaySelecionado = IDGatewaySelecionado;

            // observacoes
            List<ObservacaoGatewayDominio> observacoes = new List<ObservacaoGatewayDominio>();
            ViewBag.Observacoes = observacoes;

            return View(GatewaysAtrasoList);
        }
    }
}