﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class HomeController : Controller
    {
        public ActionResult Inicio_CPFL(int IDCliente = 0)
        {
            // tela de ajuda - contratos CCEE
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "ContratosCCEE");

            // le cookies
            LeCookies_SmartEnergy();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // le clientes do consultor - ativos
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clienteMetodos.ListarPorIDConsultor(IDConsultor, 1);

            // calcula 
            Calc_Gestor(IDConsultor, listaClientes);

            // le empresas
            EmpresasMetodos empresasMetodos = new EmpresasMetodos();
            List<EmpresasDominio> listaEmpresas = empresasMetodos.ListarTodos();

            // le contratos CCEE vigentes do cliente
            ContratosCCEEMetodos contratosMetodos = new ContratosCCEEMetodos();
            List<ContratosCCEEDominio> listaContratos = contratosMetodos.ListarPorVigentes(IDCliente);

            // número de contratos vigentes
            int NumContratosVigentes = 0;

            // percorre contratos para resgatar nome dos clientes e empresas
            if (listaContratos != null)
            {
                foreach (ContratosCCEEDominio contrato in listaContratos)
                {
                    // nome e logo do cliente
                    ClientesDominio cliente = listaClientes.Find(c => c.IDCliente == contrato.IDCliente);

                    if (cliente != null)
                    {
                        contrato.NomeCliente = cliente.Nome;
                        contrato.Logo = cliente.Logo;
                    }

                    // SiglaCCEE da empresa
                    EmpresasDominio empresa = listaEmpresas.Find(e => e.IDEmpresa == contrato.IDEmpresa);

                    if (empresa != null)
                    {
                        contrato.RazaoSocial = empresa.RazaoSocial;
                        contrato.SiglaCCEE = empresa.SiglaCCEE;
                        contrato.CNPJ = empresa.CNPJ;
                        contrato.IDEstado = empresa.IDEstado;
                        contrato.IDCidade = empresa.IDCidade;
                    }

                    // verifica se contrato está vigente
                    if (contrato.Contrato_Status == 1)
                    {
                        NumContratosVigentes++;
                    }
                }
            }

            // número de contratos vigentes
            ViewBag.NumContratosVigentes = NumContratosVigentes;


            return View(listaContratos);
        }

        public void Calc_Gestor(int IDConsultor, List<ClientesDominio> clientes)
        {

            // numero de clientes
            int NumClientes = clientes.Count;

            // numero de medicoes e gateways
            int NumGateways = 0;
            int NumMedicoes = 0;

            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            SupervGatewaysMetodos supGatewaysMetodos = new SupervGatewaysMetodos();

            if (clientes != null)
            {
                foreach (ClientesDominio cliente in clientes)
                {
                    // numero de gateways
                    List<GatewaysDominio> gateways = gatewaysMetodos.ListarPorIDCliente(cliente.IDCliente);

                    int GatewaysAtraso = 0;

                    if (gateways != null)
                    {
                        NumGateways += gateways.Count;

                        foreach (GatewaysDominio gateway in gateways)
                        {
                            // leio lista de supervisao gateways
                            SupervGatewaysDominio gatewaySuperv = supGatewaysMetodos.ListarPorIDGateway(gateway.IDGateway);

                            if (gatewaySuperv != null)
                            {
                                // data e hora
                                DateTime data_hora_hoje = DateTime.Now;
                                DateTime data_hora_atualizacao = gatewaySuperv.DataHora;

                                // verifica status
                                switch (gatewaySuperv.IDTipoTempo)
                                {
                                    case TIPO_TEMPO_GATEWAY.GatewayBloqueada:               // 0 - gateway bloqueada
                                    case TIPO_TEMPO_GATEWAY.Ignorar_StartUpNaoRealizado:    // 1 - ignorar - start up nao realizado
                                    case TIPO_TEMPO_GATEWAY.Ignorar_PendenciaCliente:       // 2 - ignorar - pendencia do cliente
                                    case TIPO_TEMPO_GATEWAY.SCDE:                           // 3 - ignorar - SCDE
                                        break;

                                    case TIPO_TEMPO_GATEWAY.Envio_15minutos:                // 12 - a cada 15 minutos

                                        // verifica se atraso maior que 6 horas
                                        if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(6, 0, 0))
                                        {
                                            // total
                                            GatewaysAtraso++;
                                        }

                                        break;

                                    case TIPO_TEMPO_GATEWAY.Envio_hora:                     // 13 - a cada hora

                                        // verifica se atraso maior que 6 horas
                                        if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(6, 0, 0))
                                        {
                                            // total
                                            GatewaysAtraso++;
                                        }

                                        break;

                                    case TIPO_TEMPO_GATEWAY.Envio_diariamente:              // 14 - diariamente

                                        // verifica se atraso maior que 2 dias
                                        if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(48, 0, 0))
                                        {
                                            // total
                                            GatewaysAtraso++;
                                        }

                                        break;

                                    case TIPO_TEMPO_GATEWAY.Envio_semanalmente:             // 15 - semanalmente

                                        // verifica se atraso maior que 8 dias
                                        if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(192, 0, 0))
                                        {
                                            // total
                                            GatewaysAtraso++;
                                        }

                                        break;

                                    case TIPO_TEMPO_GATEWAY.Envio_mensalmente:              // 16 - mensalmente

                                        // verifica se atraso maior que 32 dias
                                        if ((data_hora_hoje - data_hora_atualizacao) > new TimeSpan(768, 0, 0))
                                        {
                                            // total
                                            GatewaysAtraso++;
                                        }

                                        break;
                                }
                            }
                        }
                    }

                    // numero de gateways em atraso
                    cliente.NumGateways = GatewaysAtraso;

                    // numero de medicoes
                    List<MedicoesDominio> medicoes = medicoesMetodos.ListarPorIDCliente(cliente.IDCliente, 1);

                    if (medicoes != null)
                    {
                        NumMedicoes += medicoes.Count;
                    }
                }
            }

            ViewBag.ListaClientes = clientes;
            ViewBag.NumClientes = NumClientes;
            ViewBag.NumGateways = NumGateways;
            ViewBag.NumMedicoes = NumMedicoes;

            return;
        }
    }
}
