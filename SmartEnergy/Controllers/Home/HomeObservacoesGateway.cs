﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class HomeController
    {
        // GET: Observacoes Gateway
        public PartialViewResult _ObservacoesGateway(int IDGateway)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // tipos acesso
            ListaTiposMetodos listaMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listaTipoAcesso = listaMetodos.ListarTodos("TipoAcesso");

            ViewBag.listaTipoAcesso = listaTipoAcesso;

            // le tags
            List<ObservacaoTagsDominio> tags = listaMetodos.ListarTodos_ObservacaoTags();

            ViewBag.tags = tags;

            // observacao
            ObservacaoGatewayDominio observacao = new ObservacaoGatewayDominio();

            ViewBag.observacao = observacao;
            ViewBag.IDGatewaySelecionado = IDGateway;

            // observacoes
            ObservacaoGatewayMetodos observacaoMetodos = new ObservacaoGatewayMetodos();
            List<ObservacaoGatewayDominio> observacoes_lidas = observacaoMetodos.ListarTodosGateway(IDGateway);
            List<ObservacaoGatewayDominio> observacoes = new List<ObservacaoGatewayDominio>();

            // le observacoes
            if (observacoes_lidas != null)
            {
                // percorre observacoes
                foreach (ObservacaoGatewayDominio obs in observacoes_lidas)
                {
                    bool adicionar = false;

                    // caso for admin GESTAL pode ver todas observacoes (inseridas pela GESTAL, consultores ou clientes)
                    if (isUser.isGESTAL(IDTipoAcesso))
                    {
                        // permite ele ver
                        adicionar = true;
                    }

                    // caso usuario atual for consultor pode ver apenas as de consultor e dos clientes
                    if (isUser.isConsultor(IDTipoAcesso))
                    {
                        // verifica se mensagem eh de consultor ou se de cliente habilitado para visualizar
                        if (isUser.isConsultor(obs.IDTipoAcesso) || obs.ClienteVisualiza)
                        {
                            adicionar = true;
                        }
                    }

                    // caso usuario atual for cliente pode ver apenas se habilitado para visualizar
                    if (isUser.isCliente(IDTipoAcesso))
                    {
                        // verifica se cliente habilitado para visualizar
                        if (obs.ClienteVisualiza)
                        {
                            adicionar = true;
                        }
                    }

                    // adiciona na lista
                    if (adicionar)
                    {
                        // adiona na lista
                        observacoes.Add(obs);
                    }
                }
            }

            ViewBag.Observacoes = observacoes;

            return PartialView();
        }

        // GET: Observacao Gateway
        public PartialViewResult _ObservacaoGateway(int IDGateway, int IDObservacao)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // le tags
            ListaTiposMetodos listaMetodos = new ListaTiposMetodos();
            List<ObservacaoTagsDominio> tags = listaMetodos.ListarTodos_ObservacaoTags();

            ViewBag.tags = tags;

            // observacao
            ObservacaoGatewayDominio observacao = new ObservacaoGatewayDominio();

            if (IDObservacao > 0)
            {
                // le observacao
                ObservacaoGatewayMetodos observacaoMetodos = new ObservacaoGatewayMetodos();
                ObservacaoGatewayDominio observacao_lido = observacaoMetodos.ListarPorId(IDObservacao);

                if (observacao_lido == null)
                {
                    observacao.IDObservacao = 0;
                    observacao.IDGateway = IDGateway;
                    observacao.DataHora = DateTime.Now;
                    observacao.IDTag = 0;
                    observacao.IDTipoAcesso = IDTipoAcesso;
                    observacao.ClienteVisualiza = false;
                    observacao.Observacao = "";
                }
                else
                {
                    observacao = observacao_lido;
                }
            }
            else
            {
                // novo
                observacao.IDObservacao = 0;
                observacao.IDGateway = IDGateway;
                observacao.DataHora = DateTime.Now;
                observacao.IDTag = 0;
                observacao.IDTipoAcesso = IDTipoAcesso;
                observacao.ClienteVisualiza = false;
                observacao.Observacao = "";
            }

            // caso usuario atual for cliente
            if (isUser.isCliente(IDTipoAcesso))
            {
                // cliente sempre visualiza se o usuario for o cliente
                observacao.ClienteVisualiza = true;
            }

            ViewBag.observacao = observacao;
            ViewBag.IDGatewaySelecionado = IDGateway;

            return PartialView();
        }

        // GET: Observacao Gateway - Salvar
        public ActionResult ObservacaoGateway_Salvar(int IDObservacao, int IDGateway, int IDTag, bool ClienteVisualiza, string ObservacaoData, string ObservacaoTexto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // verifica se tem gateway
            if (IDGateway > 0)
            {
                // pega data
                DateTime dateValue = DateTime.Parse(ObservacaoData);

                ObservacaoGatewayMetodos observacaoMetodos = new ObservacaoGatewayMetodos();
                ObservacaoGatewayDominio observacao = new ObservacaoGatewayDominio();

                // preenche
                observacao.IDObservacao = IDObservacao;
                observacao.IDGateway = IDGateway;
                observacao.IDTag = IDTag;
                observacao.DataHora = dateValue;
                observacao.IDTipoAcesso = IDTipoAcesso;
                observacao.ClienteVisualiza = ClienteVisualiza;
                observacao.Observacao = ObservacaoTexto;

                // salva a observacao
                observacaoMetodos.Salvar(observacao);
            }

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // GET: Observacao Gateway - Excluir
        public ActionResult ObservacaoGateway_Excluir(int IDObservacao)
        {
            // excluir
            ObservacaoGatewayMetodos observacaoMetodos = new ObservacaoGatewayMetodos();
            observacaoMetodos.Excluir(IDObservacao);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // GET: Observacao Gateway - Resolvido
        public ActionResult ObservacaoGateway_Resolvido(int IDObservacao)
        {
            // excluir
            ObservacaoGatewayMetodos observacaoMetodos = new ObservacaoGatewayMetodos();
            observacaoMetodos.Resolvido(IDObservacao);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}