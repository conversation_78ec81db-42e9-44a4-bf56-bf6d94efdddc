﻿using SmartEnergyLib.Funcoes;
using System;
using System.Web.Mvc;

namespace SmartEnergy.Controllers.Filters
{
    public class LogPageVisitisAttibute : ActionFilterAttribute
    {
        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            int IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");




            var url = filterContext.HttpContext.Request.Url.ToString();
            var timestamp = DateTime.Now;

            // Aqui você implementa a lógica para salvar as informações no banco de dados
            // Exemplo: dbContext.PageVisits.Add(new PageVisit { Url = url, Timestamp = timestamp });
            //dbContext.SaveChanges();

            base.OnActionExecuting(filterContext);
        }
    }
}