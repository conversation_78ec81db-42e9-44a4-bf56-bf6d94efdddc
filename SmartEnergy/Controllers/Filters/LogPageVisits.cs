﻿using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace SmartEnergy.Controllers.Filters
{
    public class LogPageVisits : ActionFilterAttribute
    {
        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            // histórico
            HistoricoPaginasVisitadasDominio historico = new HistoricoPaginasVisitadasDominio();

            // data hora
            historico.DataHora = DateTime.Now;

            // IDUsuario
            historico.IDUsuario = CookieStore.LeCookie_Int("_IDUsuario");

            // IDCliente
            historico.IDCliente = CookieStore.LeCookie_Int("_IDCliente");

            // página visitada
            historico.URL = filterContext.HttpContext.Request.Url.ToString();

            // salva página
            HistoricoPaginasVisitadasMetodos historicoMetodos = new HistoricoPaginasVisitadasMetodos();
            historicoMetodos.Inserir(historico);

            // filtro
            base.OnActionExecuting(filterContext);
        }
    }
}