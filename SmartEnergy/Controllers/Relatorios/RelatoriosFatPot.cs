﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {
        //
        // FATOR DE POTENCIA
        //

        // GET: Relatorio Fator de Potencia
        public ActionResult Relat_FatPot(int IDCliente, int IDMedicao)
        {
            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Relat_FatPot");

            // relatorio fator de potencia
            return (Relat_Grafico_Show(IDCliente, IDMedicao, 3));
        }

        // Fator de Potencia Diario
        private void FatPot_Diario(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_DIARIO relatorio = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise = new RELAT_DIARIO_ANALISE();

            RELAT_DIARIO relatorio_sim = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise_sim = new RELAT_DIARIO_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // IDSimulacaoCenario
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // calcula valores
            retorno = SmCalcDB_Energia_RelatDiario((char)0, ref config_interface, (char)3, (char)IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var FatPot = new double[26];
            var Periodo = new int[26];
            var Dias = new string[26];
            var DatasN = new DateTime[26];
            var Horas = new string[26];

            // grafico simulacao
            var FatPot_Sim = new double[26];
            var Periodo_Sim = new int[26];
            var Dias_Sim = new string[26];
            var DatasN_Sim = new DateTime[26];
            var Horas_Sim = new string[26];

            double FatPot_min = 1.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Horas[i] = strData.ToString("HH:mm");

                // formata label simulacao
                Dias_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Sim[i] = strData;
                Horas_Sim[i] = strData.ToString("HH:mm");

                // proxima hora
                strData = strData.AddHours(1);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    FatPot[i] = relatorio.registro[0].valor1;
                    Periodo[i] = relatorio.registro[0].periodo;

                    // zera simulacao
                    FatPot_Sim[i] = relatorio_sim.registro[0].valor1;
                    Periodo_Sim[i] = relatorio_sim.registro[0].periodo;
                }

                // verifica se ultima barra
                if (i == 25)
                {
                    // zera
                    FatPot[i] = relatorio.registro[23].valor1;
                    Periodo[i] = relatorio.registro[23].periodo;

                    // zera simulacao
                    FatPot_Sim[i] = relatorio_sim.registro[23].valor1;
                    Periodo_Sim[i] = relatorio_sim.registro[23].periodo;
                }

                if (i >= 1 && i <= 24)
                {
                    // copia
                    j = i - 1;

                    FatPot[i] = relatorio.registro[j].valor1;
                    Periodo[i] = relatorio.registro[j].periodo;

                    // simulacao
                    FatPot_Sim[i] = relatorio_sim.registro[j].valor1;
                    Periodo_Sim[i] = relatorio_sim.registro[j].periodo;

                    // verifica se sem registro
                    if (relatorio.registro[j].periodo == 3)
                    {
                        FatPot[i] = 1.0;
                    }

                    // verifica se sem registro simulacao
                    if (relatorio_sim.registro[j].periodo == 3)
                    {
                        FatPot_Sim[i] = 1.0;
                    }

                    // verifica fatpot minimo
                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPot[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPot[i]);

                    // verifica fatpot minimo simulacao
                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPot_Sim[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPot_Sim[i]);
                }
            }

            if (FatPot_min > 0.92)
                FatPot_min = 0.92;

            FatPot_min = FatPot_min - (FatPot_min * 0.01);

            ViewBag.FatPotMin = FatPot_min;

            ViewBag.FatPot = FatPot;
            ViewBag.Periodo = Periodo;
            ViewBag.Dias = Dias;
            ViewBag.DatasN = DatasN;
            ViewBag.Horas = Horas;

            // simulacao
            ViewBag.FatPot_Sim = FatPot_Sim;
            ViewBag.Periodo_Sim = Periodo_Sim;
            ViewBag.Dias_Sim = Dias_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;
            ViewBag.Horas_Sim = Horas_Sim;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.FatorPotencia;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Diario;

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:d}", DataAtual);

            // fator de potencia Mais Indutivo fora ponta capacitivo
            ViewBag.FatPot_MaisIndFPC = string.Format("{0:0.000}", analise.analise_valor1.maximo[2]);
            DateTime FatPot_MaisIndFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max[2]);
            if (FatPot_MaisIndFPC_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndFPC_DataHora = string.Format("{0:HH:mm}", FatPot_MaisIndFPC_DataHora);
            else
                ViewBag.FatPot_MaisIndFPC_DataHora = "--:--";
            ViewBag.FatPot_MaisIndFPC_DataHoraN = FatPot_MaisIndFPC_DataHora;

            // fator de potencia Mais Indutivo fora ponta capacitivo Simulacao
            ViewBag.FatPot_MaisIndFPC_Sim = string.Format("{0:0.000}", analise_sim.analise_valor1.maximo[2]);
            DateTime FatPot_MaisIndFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor1.datahora_max[2]);
            if (FatPot_MaisIndFPC_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisIndFPC_DataHora_Sim = string.Format("{0:HH:mm}", FatPot_MaisIndFPC_DataHora_Sim);
            else
                ViewBag.FatPot_MaisIndFPC_DataHora_Sim = "--:--";
            ViewBag.FatPot_MaisIndFPC_DataHoraN_Sim = FatPot_MaisIndFPC_DataHora_Sim;

            // fator de potencia Mais Indutivo fora ponta indutivo
            ViewBag.FatPot_MaisIndFPI = string.Format("{0:0.000}", analise.analise_valor1.maximo[1]);
            DateTime FatPot_MaisIndFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max[1]);
            if (FatPot_MaisIndFPI_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndFPI_DataHora = string.Format("{0:HH:mm}", FatPot_MaisIndFPI_DataHora);
            else
                ViewBag.FatPot_MaisIndFPI_DataHora = "--:--";
            ViewBag.FatPot_MaisIndFPI_DataHoraN = FatPot_MaisIndFPI_DataHora;

            // fator de potencia Mais Indutivo fora ponta indutivo simulacao
            ViewBag.FatPot_MaisIndFPI_Sim = string.Format("{0:0.000}", analise_sim.analise_valor1.maximo[1]);
            DateTime FatPot_MaisIndFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor1.datahora_max[1]);
            if (FatPot_MaisIndFPI_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisIndFPI_DataHora_Sim = string.Format("{0:HH:mm}", FatPot_MaisIndFPI_DataHora_Sim);
            else
                ViewBag.FatPot_MaisIndFPI_DataHora_Sim = "--:--";
            ViewBag.FatPot_MaisIndFPI_DataHoraN_Sim = FatPot_MaisIndFPI_DataHora_Sim;

            // fator de potencia Mais indutivo ponta
            ViewBag.FatPot_MaisIndP = string.Format("{0:0.000}", analise.analise_valor1.maximo[0]);
            DateTime FatPot_MaisIndP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max[0]);
            if (FatPot_MaisIndP_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndP_DataHora = string.Format("{0:HH:mm}", FatPot_MaisIndP_DataHora);
            else
                ViewBag.FatPot_MaisIndP_DataHora = "--:--";
            ViewBag.FatPot_MaisIndP_DataHoraN = FatPot_MaisIndP_DataHora;

            // fator de potencia Mais indutivo ponta simulacao
            ViewBag.FatPot_MaisIndP_Sim = string.Format("{0:0.000}", analise_sim.analise_valor1.maximo[0]);
            DateTime FatPot_MaisIndP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor1.datahora_max[0]);
            if (FatPot_MaisIndP_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisIndP_DataHora_Sim = string.Format("{0:HH:mm}", FatPot_MaisIndP_DataHora_Sim);
            else
                ViewBag.FatPot_MaisIndP_DataHora_Sim = "--:--";
            ViewBag.FatPot_MaisIndP_DataHoraN_Sim = FatPot_MaisIndP_DataHora_Sim;

            // fator de potencia Mais Capacitivo foraponta capacitivo
            ViewBag.FatPot_MaisCapFPC = string.Format("{0:0.000}", analise.analise_valor1.minimo[2]);
            DateTime FatPot_MaisCapFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_min[2]);
            if (FatPot_MaisCapFPC_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapFPC_DataHora = string.Format("{0:HH:mm}", FatPot_MaisCapFPC_DataHora);
            else
                ViewBag.FatPot_MaisCapFPC_DataHora = "--:--";
            ViewBag.FatPot_MaisCapFPC_DataHoraN = FatPot_MaisCapFPC_DataHora;

            // fator de potencia Mais Capacitivo foraponta capacitivo simulacao
            ViewBag.FatPot_MaisCapFPC_Sim = string.Format("{0:0.000}", analise_sim.analise_valor1.minimo[2]);
            DateTime FatPot_MaisCapFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor1.datahora_min[2]);
            if (FatPot_MaisCapFPC_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisCapFPC_DataHora_Sim = string.Format("{0:HH:mm}", FatPot_MaisCapFPC_DataHora_Sim);
            else
                ViewBag.FatPot_MaisCapFPC_DataHora_Sim = "--:--";
            ViewBag.FatPot_MaisCapFPC_DataHoraN_Sim = FatPot_MaisCapFPC_DataHora_Sim;

            // fator de potencia Mais Capacitivo foraponta indutivo
            ViewBag.FatPot_MaisCapFPI = string.Format("{0:0.000}", analise.analise_valor1.minimo[1]);
            DateTime FatPot_MaisCapFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_min[1]);
            if (FatPot_MaisCapFPI_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapFPI_DataHora = string.Format("{0:HH:mm}", FatPot_MaisCapFPI_DataHora);
            else
                ViewBag.FatPot_MaisCapFPI_DataHora = "--:--";
            ViewBag.FatPot_MaisCapFPI_DataHoraN = FatPot_MaisCapFPI_DataHora;

            // fator de potencia Mais Capacitivo foraponta indutivo simulacao
            ViewBag.FatPot_MaisCapFPI_Sim = string.Format("{0:0.000}", analise_sim.analise_valor1.minimo[1]);
            DateTime FatPot_MaisCapFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor1.datahora_min[1]);
            if (FatPot_MaisCapFPI_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisCapFPI_DataHora_Sim = string.Format("{0:HH:mm}", FatPot_MaisCapFPI_DataHora_Sim);
            else
                ViewBag.FatPot_MaisCapFPI_DataHora_Sim = "--:--";
            ViewBag.FatPot_MaisCapFPI_DataHoraN_Sim = FatPot_MaisCapFPI_DataHora_Sim;

            // fator de potencia Mais Capacitivo ponta
            ViewBag.FatPot_MaisCapP = string.Format("{0:0.000}", analise.analise_valor1.minimo[0]);
            DateTime FatPot_MaisCapP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_min[0]);
            if (FatPot_MaisCapP_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapP_DataHora = string.Format("{0:HH:mm}", FatPot_MaisCapP_DataHora);
            else
                ViewBag.FatPot_MaisCapP_DataHora = "--:--";
            ViewBag.FatPot_MaisCapP_DataHoraN = FatPot_MaisCapP_DataHora;

            // fator de potencia Mais Capacitivo ponta simulacao
            ViewBag.FatPot_MaisCapP_Sim = string.Format("{0:0.000}", analise_sim.analise_valor1.minimo[0]);
            DateTime FatPot_MaisCapP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor1.datahora_min[0]);
            if (FatPot_MaisCapP_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisCapP_DataHora_Sim = string.Format("{0:HH:mm}", FatPot_MaisCapP_DataHora_Sim);
            else
                ViewBag.FatPot_MaisCapP_DataHora_Sim = "--:--";
            ViewBag.FatPot_MaisCapP_DataHoraN_Sim = FatPot_MaisCapP_DataHora_Sim;

            // fator de potencia No Periodo
            ViewBag.FatPot_NoPeriodoP = string.Format("{0:0.000}", analise.analise_valor1.medio[0]);
            ViewBag.FatPot_NoPeriodoFPI = string.Format("{0:0.000}", analise.analise_valor1.medio[1]);
            ViewBag.FatPot_NoPeriodoFPC = string.Format("{0:0.000}", analise.analise_valor1.medio[2]);

            // fator de potencia No Periodo simulacao
            ViewBag.FatPot_NoPeriodoP_Sim = string.Format("{0:0.000}", analise_sim.analise_valor1.medio[0]);
            ViewBag.FatPot_NoPeriodoFPI_Sim = string.Format("{0:0.000}", analise_sim.analise_valor1.medio[1]);
            ViewBag.FatPot_NoPeriodoFPC_Sim = string.Format("{0:0.000}", analise_sim.analise_valor1.medio[2]);

            // referencia
            ViewBag.RefenciaFPC = string.Format("{0:0.000}", -0.92);
            ViewBag.RefenciaFPI = string.Format("{0:0.000}", 0.92);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(1);

            ViewBag.data_hora_ini = data_hora_ini;

            EventosPeriodo(IDCliente, IDMedicao, data_hora_ini, data_hora_fim);

            return;
        }

        // Fator de Potencia Diario XLS
        private HSSFWorkbook FatPot_Diario_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Período", "Fator de Potência" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 25; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _datahoraStyle);

                // posto
                numeroCelulaXLS(row, 1, ViewBag.Periodo[i], _intCellStyle);

                // fator de potencia
                numeroCelulaXLS(row, 2, ViewBag.FatPot[i], _3CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Potência", "Relatório Diário", _negritoCellStyle);

            // cabecalho capacitivo e indutivo
            string[] cab_fatpot = { "Fator de Potência", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data" };
            cabecalhoTabelaXLS(workbook, sheet, cab_fatpot, rowIndex++);

            // fatpot mais capacitivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Capacitivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisCapFPC), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisCapFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisCapFPI), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisCapFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisCapP), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisCapP_DataHoraN, _datahoraStyle);

            // fatpot mais indutivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Indutivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisIndFPC), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisIndFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisIndFPI), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisIndFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisIndP), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisIndP_DataHoraN, _datahoraStyle);

            // fatpot no periodo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "No Período", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_NoPeriodoFPC), _3CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_NoPeriodoFPI), _3CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_NoPeriodoP), _3CellStyle);

            // referencia
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Referência", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.RefenciaFPC), _3CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.RefenciaFPI), _3CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.RefenciaFPI), _3CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Fator de Potencia Diario XLS simulacao
        private HSSFWorkbook FatPot_Diario_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho simulacao
            string[] simulacao = { "Relatório de Fator de Potência - Simulação" };

            // simulação 
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas do titulo simulacao
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 2));

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Período", "Fator de Potência" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 25; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[i], _datahoraStyle);

                // posto
                numeroCelulaXLS(row, 1, ViewBag.Periodo_Sim[i], _intCellStyle);

                // fator de potencia
                numeroCelulaXLS(row, 2, ViewBag.FatPot_Sim[i], _3CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Potência - Simulação", "Relatório Diário", _negritoCellStyle);

            // cabecalho capacitivo e indutivo
            string[] cab_fatpot = { "Fator de Potência", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data" };
            cabecalhoTabelaXLS(workbook, sheet, cab_fatpot, rowIndex++);

            // fatpot mais capacitivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Capacitivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisCapFPC_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisCapFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisCapFPI_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisCapFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisCapP_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisCapP_DataHoraN_Sim, _datahoraStyle);

            // fatpot mais indutivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Indutivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisIndFPC_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisIndFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisIndFPI_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisIndFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisIndP_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisIndP_DataHoraN_Sim, _datahoraStyle);

            // fatpot no periodo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "No Período", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_NoPeriodoFPC_Sim), _3CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_NoPeriodoFPI_Sim), _3CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_NoPeriodoP_Sim), _3CellStyle);

            // referencia
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Referência", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.RefenciaFPC), _3CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.RefenciaFPI), _3CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.RefenciaFPI), _3CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Fator de Potencia Semanal
        private void FatPot_Semanal(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_SEMANAL relatorio = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise = new RELAT_SEMANAL_ANALISE();

            RELAT_SEMANAL relatorio_sim = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise_sim = new RELAT_SEMANAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // IDSimulacaoCenario
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // calcula valores
            retorno = SmCalcDB_Energia_RelatSemanal((char)0, ref config_interface, (char)3, (char)IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var FatPot = new double[7, 26];
            var Periodo = new int[7, 26];
            var Datas = new string[26];
            var DatasN = new DateTime[7, 26];
            var Dias = new string[7];
            var Horas = new string[26];

            // grafico
            var FatPot_Sim = new double[7, 26];
            var Periodo_Sim = new int[7, 26];
            var Datas_Sim = new string[26];
            var DatasN_Sim = new DateTime[7, 26];
            var Dias_Sim = new string[7];
            var Horas_Sim = new string[26];

            DateTime inicio = new DateTime();
            DateTime fim = new DateTime();

            double FatPot_min = 1.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;
            int k = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                Horas[i] = strData.ToString("HH:mm");

                // formata label simulacao
                Datas_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                Horas_Sim[i] = strData.ToString("HH:mm");

                // percorre dias
                for (k = 0; k < 7; k++)
                {
                    // data e hora para excel
                    DatasN[k, i] = strData.AddDays(k);

                    // data e hora para excel simulacao
                    DatasN_Sim[k, i] = strData.AddDays(k);

                    // guarda inicio e fim
                    if (i == 0 && k == 0)
                    {
                        inicio = strData;
                        fim = inicio.AddDays(6);
                    }

                    // dia da semana
                    if (i == 1)
                    {
                        Dias[k] = string.Format("{0:d}", strData.AddDays(k));

                        // simulacao 
                        Dias_Sim[k] = string.Format("{0:d}", strData.AddDays(k));
                    }

                    // verifica se primeira ou ultima barra
                    if (i == 0)
                    {
                        // zera
                        FatPot[k, i] = relatorio.registro[0].valor[k];
                        Periodo[k, i] = relatorio.registro[0].periodo[k];

                        // zera simulacao
                        FatPot_Sim[k, i] = relatorio_sim.registro[0].valor[k];
                        Periodo_Sim[k, i] = relatorio_sim.registro[0].periodo[k];
                    }

                    if (i == 25)
                    {
                        // zera
                        FatPot[k, i] = relatorio.registro[23].valor[k];
                        Periodo[k, i] = relatorio.registro[23].periodo[k];

                        // zera simulacao
                        FatPot_Sim[k, i] = relatorio_sim.registro[23].valor[k];
                        Periodo_Sim[k, i] = relatorio_sim.registro[23].periodo[k];
                    }

                    if (i >= 1 && i <= 24)
                    {
                        // copia
                        j = i - 1;

                        FatPot[k, i] = relatorio.registro[j].valor[k];
                        Periodo[k, i] = relatorio.registro[j].periodo[k];

                        // simulacao
                        FatPot_Sim[k, i] = relatorio_sim.registro[j].valor[k];
                        Periodo_Sim[k, i] = relatorio_sim.registro[j].periodo[k];

                        // verifica se sem registro
                        if (relatorio.registro[j].periodo[k] == 3)
                        {
                            FatPot[k, i] = 1.0;
                        }

                        // verifica se sem registro simulacao
                        if (relatorio_sim.registro[j].periodo[k] == 3)
                        {
                            FatPot_Sim[k, i] = 1.0;
                        }

                        // verifica fatpot minimo
                        if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPot[k, i]), FatPot_min) > 0)
                            FatPot_min = Math.Abs(FatPot[k, i]);

                        // verifica fatpot minimo simulacao
                        if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPot_Sim[k, i]), FatPot_min) > 0)
                            FatPot_min = Math.Abs(FatPot_Sim[k, i]);
                    }
                }

                // proxima hora
                strData = strData.AddHours(1);
            }

            if (FatPot_min > 0.92)
                FatPot_min = 0.92;

            FatPot_min = FatPot_min - (FatPot_min * 0.01);

            ViewBag.FatPotMin = FatPot_min;

            ViewBag.FatPot = FatPot;
            ViewBag.Periodo = Periodo;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;
            ViewBag.Horas = Horas;

            // simulacao
            ViewBag.FatPot_Sim = FatPot_Sim;
            ViewBag.Periodo_Sim = Periodo_Sim;
            ViewBag.Datas_Sim = Datas_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;
            ViewBag.Dias_Sim = Dias_Sim;
            ViewBag.Horas_Sim = Horas_Sim;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.FatorPotencia;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Semanal;

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);
            ViewBag.DataAtualN = inicio;
            ViewBag.DataFinalN = fim;
            ViewBag.DataTextoAtual = string.Format("{0:d} - {1:d}", inicio, fim);

            // fator de potencia Mais Indutivo fora ponta capacitivo
            ViewBag.FatPot_MaisIndFPC = string.Format("{0:0.000}", analise.analise_valor.maximo[2]);
            DateTime FatPot_MaisIndFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[2]);
            if (FatPot_MaisIndFPC_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndFPC_DataHora = String.Format("{0:d} {1:HH:mm}", FatPot_MaisIndFPC_DataHora, FatPot_MaisIndFPC_DataHora);
            else
                ViewBag.FatPot_MaisIndFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndFPC_DataHoraN = FatPot_MaisIndFPC_DataHora;

            // fator de potencia Mais Indutivo fora ponta capacitivo simulacao
            ViewBag.FatPot_MaisIndFPC_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.maximo[2]);
            DateTime FatPot_MaisIndFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[2]);
            if (FatPot_MaisIndFPC_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisIndFPC_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", FatPot_MaisIndFPC_DataHora_Sim, FatPot_MaisIndFPC_DataHora_Sim);
            else
                ViewBag.FatPot_MaisIndFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndFPC_DataHoraN_Sim = FatPot_MaisIndFPC_DataHora_Sim;

            // fator de potencia Mais Indutivo fora ponta indutivo
            ViewBag.FatPot_MaisIndFPI = string.Format("{0:0.000}", analise.analise_valor.maximo[1]);
            DateTime FatPot_MaisIndFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[1]);
            if (FatPot_MaisIndFPI_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndFPI_DataHora = String.Format("{0:d} {1:HH:mm}", FatPot_MaisIndFPI_DataHora, FatPot_MaisIndFPI_DataHora);
            else
                ViewBag.FatPot_MaisIndFPI_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndFPI_DataHoraN = FatPot_MaisIndFPI_DataHora;

            // fator de potencia Mais Indutivo fora ponta indutivo simulacao
            ViewBag.FatPot_MaisIndFPI_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.maximo[1]);
            DateTime FatPot_MaisIndFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[1]);
            if (FatPot_MaisIndFPI_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisIndFPI_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", FatPot_MaisIndFPI_DataHora_Sim, FatPot_MaisIndFPI_DataHora_Sim);
            else
                ViewBag.FatPot_MaisIndFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndFPI_DataHoraN_Sim = FatPot_MaisIndFPI_DataHora_Sim;

            // fator de potencia Mais Indutivo ponta 
            ViewBag.FatPot_MaisIndP = string.Format("{0:0.000}", analise.analise_valor.maximo[0]);
            DateTime FatPot_MaisIndP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (FatPot_MaisIndP_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndP_DataHora = String.Format("{0:d} {1:HH:mm}", FatPot_MaisIndP_DataHora, FatPot_MaisIndP_DataHora);
            else
                ViewBag.FatPot_MaisIndP_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndP_DataHoraN = FatPot_MaisIndP_DataHora;

            // fator de potencia Mais Indutivo ponta simulacao
            ViewBag.FatPot_MaisIndP_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.maximo[0]);
            DateTime FatPot_MaisIndP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[0]);
            if (FatPot_MaisIndP_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisIndP_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", FatPot_MaisIndP_DataHora_Sim, FatPot_MaisIndP_DataHora_Sim);
            else
                ViewBag.FatPot_MaisIndP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndP_DataHoraN_Sim = FatPot_MaisIndP_DataHora_Sim;

            // fator de potencia Mais Capacitivo fora ponta capacitivo
            ViewBag.FatPot_MaisCapFPC = string.Format("{0:0.000}", analise.analise_valor.minimo[2]);
            DateTime FatPot_MaisCapFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[2]);
            if (FatPot_MaisCapFPC_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapFPC_DataHora = String.Format("{0:d} {1:HH:mm}", FatPot_MaisCapFPC_DataHora, FatPot_MaisCapFPC_DataHora);
            else
                ViewBag.FatPot_MaisCapFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapFPC_DataHoraN = FatPot_MaisCapFPC_DataHora;

            // fator de potencia Mais Capacitivo fora ponta capacitivo simulacao
            ViewBag.FatPot_MaisCapFPC_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.minimo[2]);
            DateTime FatPot_MaisCapFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[2]);
            if (FatPot_MaisCapFPC_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisCapFPC_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", FatPot_MaisCapFPC_DataHora_Sim, FatPot_MaisCapFPC_DataHora_Sim);
            else
                ViewBag.FatPot_MaisCapFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapFPC_DataHoraN_Sim = FatPot_MaisCapFPC_DataHora_Sim;

            // fator de potencia Mais Cpacitivo fora ponta indutivo 
            ViewBag.FatPot_MaisCapFPI = string.Format("{0:0.000}", analise.analise_valor.minimo[1]);
            DateTime FatPot_MaisCapFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[1]);
            if (FatPot_MaisCapFPI_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapFPI_DataHora = String.Format("{0:d} {1:HH:mm}", FatPot_MaisCapFPI_DataHora, FatPot_MaisCapFPI_DataHora);
            else
                ViewBag.FatPot_MaisCapFPI_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapFPI_DataHoraN = FatPot_MaisCapFPI_DataHora;

            // fator de potencia Mais Cpacitivo fora ponta indutivo simulacao
            ViewBag.FatPot_MaisCapFPI_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.minimo[1]);
            DateTime FatPot_MaisCapFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[1]);
            if (FatPot_MaisCapFPI_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisCapFPI_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", FatPot_MaisCapFPI_DataHora_Sim, FatPot_MaisCapFPI_DataHora_Sim);
            else
                ViewBag.FatPot_MaisCapFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapFPI_DataHoraN_Sim = FatPot_MaisCapFPI_DataHora_Sim;

            // fator de potencia Mais Capacitivo ponta 
            ViewBag.FatPot_MaisCapP = string.Format("{0:0.000}", analise.analise_valor.minimo[0]);
            DateTime FatPot_MaisCapP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[0]);
            if (FatPot_MaisCapP_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapP_DataHora = String.Format("{0:d} {1:HH:mm}", FatPot_MaisCapP_DataHora, FatPot_MaisCapP_DataHora);
            else
                ViewBag.FatPot_MaisCapP_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapP_DataHoraN = FatPot_MaisCapP_DataHora;

            // fator de potencia Mais Capacitivo ponta simulacao
            ViewBag.FatPot_MaisCapP_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.minimo[0]);
            DateTime FatPot_MaisCapP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[0]);
            if (FatPot_MaisCapP_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisCapP_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", FatPot_MaisCapP_DataHora_Sim, FatPot_MaisCapP_DataHora_Sim);
            else
                ViewBag.FatPot_MaisCapP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapP_DataHoraN_Sim = FatPot_MaisCapP_DataHora_Sim;

            // fator de potencia No Periodo
            ViewBag.FatPot_NoPeriodoP = string.Format("{0:0.000}", analise.analise_valor.medio[0]);
            ViewBag.FatPot_NoPeriodoFPI = string.Format("{0:0.000}", analise.analise_valor.medio[1]);
            ViewBag.FatPot_NoPeriodoFPC = string.Format("{0:0.000}", analise.analise_valor.medio[2]);

            // fator de potencia No Periodo simulacao
            ViewBag.FatPot_NoPeriodoP_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.medio[0]);
            ViewBag.FatPot_NoPeriodoFPI_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.medio[1]);
            ViewBag.FatPot_NoPeriodoFPC_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.medio[2]);

            // referencia
            ViewBag.RefenciaFPC = string.Format("{0:0.000}", -0.92);
            ViewBag.RefenciaFPI = string.Format("{0:0.000}", 0.92);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(inicio.Year, inicio.Month, inicio.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(7);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Fator de Potencia Semanal XLS
        private HSSFWorkbook FatPot_Semanal_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // dias da semana
            int k = 0;
            int i;
            IRow row;
            ISheet sheet;
            int rowIndex;

            for (k = 0; k < 7; k++)
            {
                // cria planilha
                sheet = workbook.CreateSheet(string.Format("{0:dddd}", ViewBag.DatasN[k, 0]));

                // cabecalho
                string[] cabecalho = { "Data e Hora", "Período", "Fator de Potência" };

                // adiciona cabecalho
                rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

                // percorre valores
                for (i = 1; i < 25; i++)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // data e hora
                    datahoraCelulaXLS(row, 0, ViewBag.DatasN[k, i], _datahoraStyle);

                    // periodo
                    numeroCelulaXLS(row, 1, ViewBag.Periodo[k, i], _intCellStyle);

                    // fator de potencia
                    numeroCelulaXLS(row, 2, ViewBag.FatPot[k, i], _3CellStyle);

                    // proxima
                    rowIndex++;
                }

                // largura de cada coluna
                for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                    sheet.SetColumnWidth(i, 7000);
            }

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Potência", "Relatório Semanal", _negritoCellStyle);

            // cabecalho capacitivo e indutivo
            string[] cab_fatpot = { "Fator de Potência", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data" };
            cabecalhoTabelaXLS(workbook, sheet, cab_fatpot, rowIndex++);

            // fatpot mais capacitivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Capacitivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisCapFPC), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisCapFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisCapFPI), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisCapFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisCapP), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisCapP_DataHoraN, _datahoraStyle);

            // fatpot mais indutivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Indutivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisIndFPC), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisIndFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisIndFPI), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisIndFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisIndP), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisIndP_DataHoraN, _datahoraStyle);

            // fatpot no periodo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "No Período", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_NoPeriodoFPC), _3CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_NoPeriodoFPI), _3CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_NoPeriodoP), _3CellStyle);

            // referencia
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Referência", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.RefenciaFPC), _3CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.RefenciaFPI), _3CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.RefenciaFPI), _3CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Fator de Potencia Semanal XLS Simulacao
        private HSSFWorkbook FatPot_Semanal_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // dias da semana
            int k = 0;
            int i;
            IRow row;
            ISheet sheet;
            int rowIndex;

            for (k = 0; k < 7; k++)
            {
                // cria planilha
                sheet = workbook.CreateSheet(string.Format("{0:dddd}", ViewBag.DatasN[k, 0]));

                // cabecalho simulacao
                string[] simulacao = { "Relatório de Fator Potência - Simulação" };

                // simulação 
                rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

                // mescla celulas do titulo simulacao
                sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 2));

                // cabecalho
                string[] cabecalho = { "Data e Hora", "Período", "Fator de Potência" };

                // adiciona cabecalho
                rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

                // percorre valores
                for (i = 1; i < 25; i++)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // data e hora
                    datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[k, i], _datahoraStyle);

                    // periodo
                    numeroCelulaXLS(row, 1, ViewBag.Periodo_Sim[k, i], _intCellStyle);

                    // fator de potencia
                    numeroCelulaXLS(row, 2, ViewBag.FatPot_Sim[k, i], _3CellStyle);

                    // proxima
                    rowIndex++;
                }

                // largura de cada coluna
                for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                    sheet.SetColumnWidth(i, 7000);
            }

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Potência - Simulação", "Relatório Semanal", _negritoCellStyle);

            // cabecalho capacitivo e indutivo
            string[] cab_fatpot = { "Fator de Potência", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data" };
            cabecalhoTabelaXLS(workbook, sheet, cab_fatpot, rowIndex++);

            // fatpot mais capacitivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Capacitivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisCapFPC_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisCapFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisCapFPI_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisCapFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisCapP_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisCapP_DataHoraN_Sim, _datahoraStyle);

            // fatpot mais indutivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Indutivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisIndFPC_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisIndFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisIndFPI_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisIndFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisIndP_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisIndP_DataHoraN_Sim, _datahoraStyle);

            // fatpot no periodo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "No Período", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_NoPeriodoFPC_Sim), _3CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_NoPeriodoFPI_Sim), _3CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_NoPeriodoP_Sim), _3CellStyle);

            // referencia
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Referência", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.RefenciaFPC), _3CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.RefenciaFPI), _3CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.RefenciaFPI), _3CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Fator de Potencia Mensal
        private void FatPot_Mensal(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_MENSAL relatorio = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise = new RELAT_MENSAL_ANALISE();

            RELAT_MENSAL relatorio_sim = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise_sim = new RELAT_MENSAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // IDSimulacaoCenario
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // calcula valores
            retorno = SmCalcDB_Energia_RelatMensal((char)0, ref config_interface, (char)3, (char)IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);

            // numero de dias do mes
            int NumDiasMes = relatorio.num_dias;

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[NumDiasMes - 1].datahora.data.ano,
                                   (int)relatorio.registro[NumDiasMes - 1].datahora.data.mes,
                                   1, 0, 15, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var FatPotP = new double[42];
            var FatPotInd = new double[42];
            var FatPotCap = new double[42];
            var Datas = new string[42];
            var DatasN = new DateTime[42];
            var Dias = new string[42];

            // grafico simulacao
            var FatPotP_Sim = new double[42];
            var FatPotInd_Sim = new double[42];
            var FatPotCap_Sim = new double[42];
            var Datas_Sim = new string[42];
            var DatasN_Sim = new DateTime[42];
            var Dias_Sim = new string[42];

            double FatPot_min = 1.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = strData.ToString("dd/MM");

                // formata label simulacao
                Datas_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Sim[i] = strData;
                Dias_Sim[i] = strData.ToString("dd/MM");

                // proximo dia
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    FatPotP[i] = relatorio.registro[0].valor[0];
                    FatPotInd[i] = relatorio.registro[0].valor[1];
                    FatPotCap[i] = relatorio.registro[0].valor[2];

                    // zera simulacao
                    FatPotP_Sim[i] = relatorio_sim.registro[0].valor[0];
                    FatPotInd_Sim[i] = relatorio_sim.registro[0].valor[1];
                    FatPotCap_Sim[i] = relatorio_sim.registro[0].valor[2];
                }

                if (i == (NumDiasMes + 1))
                {
                    // zera
                    FatPotP[i] = relatorio.registro[NumDiasMes - 1].valor[0];
                    FatPotInd[i] = relatorio.registro[NumDiasMes - 1].valor[1];
                    FatPotCap[i] = relatorio.registro[NumDiasMes - 1].valor[2];

                    // zera simulacao
                    FatPotP_Sim[i] = relatorio_sim.registro[NumDiasMes - 1].valor[0];
                    FatPotInd_Sim[i] = relatorio_sim.registro[NumDiasMes - 1].valor[1];
                    FatPotCap_Sim[i] = relatorio_sim.registro[NumDiasMes - 1].valor[2];
                }

                if (i >= 1 && i <= NumDiasMes)
                {
                    // copia
                    j = i - 1;

                    FatPotP[i] = relatorio.registro[j].valor[0];
                    FatPotInd[i] = relatorio.registro[j].valor[1];
                    FatPotCap[i] = relatorio.registro[j].valor[2];

                    // simulacao
                    FatPotP_Sim[i] = relatorio_sim.registro[j].valor[0];
                    FatPotInd_Sim[i] = relatorio_sim.registro[j].valor[1];
                    FatPotCap_Sim[i] = relatorio_sim.registro[j].valor[2];

                    // verifica fatpot minimo ponta
                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPotP[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPotP[i]);

                    // verifica fatpot minimo ponta simulacao
                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPotP_Sim[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPotP_Sim[i]);

                    // verifica fatpot minimo fora ponta indutivo
                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPotInd[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPotInd[i]);

                    // verifica fatpot minimo fora ponta indutivo simulacao
                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPotInd_Sim[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPotInd_Sim[i]);

                    // verifica fatpot minimo fora ponta capacitivo
                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPotCap[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPotCap[i]);

                    // verifica fatpot minimo fora ponta capacitivo simulacao
                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPotCap_Sim[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPotCap_Sim[i]);
                }
            }

            if (FatPot_min > 0.92)
                FatPot_min = 0.92;

            FatPot_min = FatPot_min - (FatPot_min * 0.01);

            ViewBag.FatPotMin = FatPot_min;

            ViewBag.FatPotP = FatPotP;
            ViewBag.FatPotInd = FatPotInd;
            ViewBag.FatPotCap = FatPotCap;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            // simulacao
            ViewBag.FatPotP_Sim = FatPotP_Sim;
            ViewBag.FatPotInd_Sim = FatPotInd_Sim;
            ViewBag.FatPotCap_Sim = FatPotCap_Sim;
            ViewBag.Datas_Sim = Datas_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;
            ViewBag.Dias_Sim = Dias_Sim;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.FatorPotencia;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Mensal;

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:Y}", DataAtual);

            ViewBag.NumDiasMes = NumDiasMes;

            // fator de potencia Mais Indutivo
            ViewBag.FatPot_MaisIndFPC = string.Format("{0:0.000}", analise.analise_valor.maximo[2]);
            DateTime FatPot_MaisIndFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[2]);
            if (FatPot_MaisIndFPC_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndFPC_DataHora = String.Format("{0:d} {1:HH:mm}", FatPot_MaisIndFPC_DataHora, FatPot_MaisIndFPC_DataHora);
            else
                ViewBag.FatPot_MaisIndFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndFPC_DataHoraN = FatPot_MaisIndFPC_DataHora;

            // fator de potencia Mais Indutivo fora ponta capacitivo simulacao
            ViewBag.FatPot_MaisIndFPC_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.maximo[2]);
            DateTime FatPot_MaisIndFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[2]);
            if (FatPot_MaisIndFPC_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisIndFPC_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", FatPot_MaisIndFPC_DataHora_Sim, FatPot_MaisIndFPC_DataHora_Sim);
            else
                ViewBag.FatPot_MaisIndFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndFPC_DataHoraN_Sim = FatPot_MaisIndFPC_DataHora_Sim;

            // fator de potencia Mais Indutivo fora ponta indutivo 
            ViewBag.FatPot_MaisIndFPI = string.Format("{0:0.000}", analise.analise_valor.maximo[1]);
            DateTime FatPot_MaisIndFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[1]);
            if (FatPot_MaisIndFPI_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndFPI_DataHora = String.Format("{0:d} {1:HH:mm}", FatPot_MaisIndFPI_DataHora, FatPot_MaisIndFPI_DataHora);
            else
                ViewBag.FatPot_MaisIndFPI_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndFPI_DataHoraN = FatPot_MaisIndFPI_DataHora;

            // fator de potencia Mais Indutivo fora ponta indutivo simulacao
            ViewBag.FatPot_MaisIndFPI_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.maximo[1]);
            DateTime FatPot_MaisIndFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[1]);
            if (FatPot_MaisIndFPI_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisIndFPI_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", FatPot_MaisIndFPI_DataHora_Sim, FatPot_MaisIndFPI_DataHora_Sim);
            else
                ViewBag.FatPot_MaisIndFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndFPI_DataHoraN_Sim = FatPot_MaisIndFPI_DataHora_Sim;

            // fator de potencia Mais Indutivo ponta 
            ViewBag.FatPot_MaisIndP = string.Format("{0:0.000}", analise.analise_valor.maximo[0]);
            DateTime FatPot_MaisIndP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (FatPot_MaisIndP_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndP_DataHora = String.Format("{0:d} {1:HH:mm}", FatPot_MaisIndP_DataHora, FatPot_MaisIndP_DataHora);
            else
                ViewBag.FatPot_MaisIndP_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndP_DataHoraN = FatPot_MaisIndP_DataHora;

            // fator de potencia Mais Indutivo ponta simulacao
            ViewBag.FatPot_MaisIndP_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.maximo[0]);
            DateTime FatPot_MaisIndP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (FatPot_MaisIndP_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisIndP_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", FatPot_MaisIndP_DataHora_Sim, FatPot_MaisIndP_DataHora_Sim);
            else
                ViewBag.FatPot_MaisIndP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndP_DataHoraN_Sim = FatPot_MaisIndP_DataHora_Sim;

            // fator de potencia Mais Capacitivo fora ponta capacitivo
            ViewBag.FatPot_MaisCapFPC = string.Format("{0:0.000}", analise.analise_valor.minimo[2]);
            DateTime FatPot_MaisCapFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[2]);
            if (FatPot_MaisCapFPC_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapFPC_DataHora = String.Format("{0:d} {1:HH:mm}", FatPot_MaisCapFPC_DataHora, FatPot_MaisCapFPC_DataHora);
            else
                ViewBag.FatPot_MaisCapFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapFPC_DataHoraN = FatPot_MaisCapFPC_DataHora;

            // fator de potencia Mais Capacitivo fora ponta capacitivo simulacao
            ViewBag.FatPot_MaisCapFPC_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.minimo[2]);
            DateTime FatPot_MaisCapFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[2]);
            if (FatPot_MaisCapFPC_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisCapFPC_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", FatPot_MaisCapFPC_DataHora_Sim, FatPot_MaisCapFPC_DataHora_Sim);
            else
                ViewBag.FatPot_MaisCapFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapFPC_DataHoraN_Sim = FatPot_MaisCapFPC_DataHora_Sim;

            // fator de potencia Mais Capacitivo fora ponta indutivo 
            ViewBag.FatPot_MaisCapFPI = string.Format("{0:0.000}", analise.analise_valor.minimo[1]);
            DateTime FatPot_MaisCapFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[1]);
            if (FatPot_MaisCapFPI_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapFPI_DataHora = String.Format("{0:d} {1:HH:mm}", FatPot_MaisCapFPI_DataHora, FatPot_MaisCapFPI_DataHora);
            else
                ViewBag.FatPot_MaisCapFPI_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapFPI_DataHoraN = FatPot_MaisCapFPI_DataHora;

            // fator de potencia Mais Capacitivo fora ponta indutivo simulacao
            ViewBag.FatPot_MaisCapFPI_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.minimo[1]);
            DateTime FatPot_MaisCapFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[1]);
            if (FatPot_MaisCapFPI_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisCapFPI_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", FatPot_MaisCapFPI_DataHora_Sim, FatPot_MaisCapFPI_DataHora_Sim);
            else
                ViewBag.FatPot_MaisCapFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapFPI_DataHoraN_Sim = FatPot_MaisCapFPI_DataHora_Sim;

            // fator de potencia Mais Capacitivo ponta 
            ViewBag.FatPot_MaisCapP = string.Format("{0:0.000}", analise.analise_valor.minimo[0]);
            DateTime FatPot_MaisCapP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[0]);
            if (FatPot_MaisCapP_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapP_DataHora = String.Format("{0:d} {1:HH:mm}", FatPot_MaisCapP_DataHora, FatPot_MaisCapP_DataHora);
            else
                ViewBag.FatPot_MaisCapP_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapP_DataHoraN = FatPot_MaisCapP_DataHora;

            // fator de potencia Mais Capacitivo ponta simulacao 
            ViewBag.FatPot_MaisCapP_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.minimo[0]);
            DateTime FatPot_MaisCapP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[0]);
            if (FatPot_MaisCapP_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisCapP_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", FatPot_MaisCapP_DataHora_Sim, FatPot_MaisCapP_DataHora_Sim);
            else
                ViewBag.FatPot_MaisCapP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapP_DataHoraN_Sim = FatPot_MaisCapP_DataHora_Sim;

            // fator de potencia No Periodo
            ViewBag.FatPot_NoPeriodoP = string.Format("{0:0.000}", analise.analise_valor.medio[0]);
            ViewBag.FatPot_NoPeriodoFPI = string.Format("{0:0.000}", analise.analise_valor.medio[1]);
            ViewBag.FatPot_NoPeriodoFPC = string.Format("{0:0.000}", analise.analise_valor.medio[2]);

            // fator de potencia No Periodo simulacao
            ViewBag.FatPot_NoPeriodoP_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.medio[0]);
            ViewBag.FatPot_NoPeriodoFPI_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.medio[1]);
            ViewBag.FatPot_NoPeriodoFPC_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.medio[2]);

            // referencia
            ViewBag.RefenciaFPC = string.Format("{0:0.000}", -0.92);
            ViewBag.RefenciaFPI = string.Format("{0:0.000}", 0.92);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Fator de Potencia Mensal XLS
        private HSSFWorkbook FatPot_Mensal_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < (ViewBag.NumDiasMes + 1); i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // fator de potencia capacitivo
                numeroCelulaXLS(row, 1, ViewBag.FatPotCap[i], _3CellStyle);

                // fator de potencia indutivo
                numeroCelulaXLS(row, 2, ViewBag.FatPotInd[i], _3CellStyle);

                // fator de potencia ponta
                numeroCelulaXLS(row, 3, ViewBag.FatPotP[i], _3CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Potência", "Relatório Mensal", _negritoCellStyle);

            // cabecalho capacitivo e indutivo
            string[] cab_fatpot = { "Fator de Potência", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data" };
            cabecalhoTabelaXLS(workbook, sheet, cab_fatpot, rowIndex++);

            // fatpot mais capacitivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Capacitivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisCapFPC), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisCapFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisCapFPI), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisCapFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisCapP), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisCapP_DataHoraN, _datahoraStyle);

            // fatpot mais indutivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Indutivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisIndFPC), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisIndFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisIndFPI), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisIndFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisIndP), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisIndP_DataHoraN, _datahoraStyle);

            // fatpot no periodo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "No Período", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_NoPeriodoFPC), _3CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_NoPeriodoFPI), _3CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_NoPeriodoP), _3CellStyle);

            // referencia
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Referência", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.RefenciaFPC), _3CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.RefenciaFPI), _3CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.RefenciaFPI), _3CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Fator de Potencia Mensal XLS simulacao
        private HSSFWorkbook FatPot_Mensal_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho simulacao
            string[] simulacao = { "Relatório de Fator de Potência - Simulação" };

            // simulação 
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas do titulo simulacao
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 3));

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < (ViewBag.NumDiasMes + 1); i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[i], _dataStyle);

                // fator de potencia capacitivo
                numeroCelulaXLS(row, 1, ViewBag.FatPotCap_Sim[i], _3CellStyle);

                // fator de potencia indutivo
                numeroCelulaXLS(row, 2, ViewBag.FatPotInd_Sim[i], _3CellStyle);

                // fator de potencia ponta
                numeroCelulaXLS(row, 3, ViewBag.FatPotP_Sim[i], _3CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Potência - Simulação", "Relatório Mensal", _negritoCellStyle);

            // cabecalho capacitivo e indutivo
            string[] cab_fatpot = { "Fator de Potência", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data" };
            cabecalhoTabelaXLS(workbook, sheet, cab_fatpot, rowIndex++);

            // fatpot mais capacitivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Capacitivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisCapFPC_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisCapFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisCapFPI_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisCapFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisCapP_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisCapP_DataHoraN_Sim, _datahoraStyle);

            // fatpot mais indutivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Indutivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisIndFPC_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisIndFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisIndFPI_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisIndFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisIndP_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisIndP_DataHoraN_Sim, _datahoraStyle);

            // fatpot no periodo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "No Período", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_NoPeriodoFPC_Sim), _3CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_NoPeriodoFPI_Sim), _3CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_NoPeriodoP_Sim), _3CellStyle);

            // referencia
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Referência", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.RefenciaFPC), _3CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.RefenciaFPI), _3CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.RefenciaFPI), _3CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Fator de Potencia Anual
        private void FatPot_Anual(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_ANUAL relatorio = new RELAT_ANUAL();
            RELAT_ANUAL_ANALISE analise = new RELAT_ANUAL_ANALISE();

            RELAT_ANUAL relatorio_sim = new RELAT_ANUAL();
            RELAT_ANUAL_ANALISE analise_sim = new RELAT_ANUAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // IDSimulacaoCenario
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // calcula valores
            retorno = SmCalcDB_Energia_RelatAnual((char)0, ref config_interface, (char)3, (char)IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   1, 1, 0, 15, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var FatPotP = new double[14];
            var FatPotInd = new double[14];
            var FatPotCap = new double[14];
            var Datas = new string[14];
            var DatasN = new DateTime[14];
            var Meses = new string[14];

            // grafico simulacao
            var FatPotP_Sim = new double[14];
            var FatPotInd_Sim = new double[14];
            var FatPotCap_Sim = new double[14];
            var Datas_Sim = new string[14];
            var DatasN_Sim = new DateTime[14];
            var Meses_Sim = new string[14];

            double FatPot_min = 1.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 mes para a primeira barra
            strData = strData.AddMonths(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 14; i++)
            {
                
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Meses[i] = string.Format("{0:MMMM}", strData);

                // formata label simulacao
                Datas_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Sim[i] = strData;
                Meses_Sim[i] = string.Format("{0:MMMM}", strData);

                // proximo mes
                strData = strData.AddMonths(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    FatPotP[i] = relatorio.registro[0].valor[0];
                    FatPotInd[i] = relatorio.registro[0].valor[1];
                    FatPotCap[i] = relatorio.registro[0].valor[2];

                    // zera simulacao
                    FatPotP_Sim[i] = relatorio_sim.registro[0].valor[0];
                    FatPotInd_Sim[i] = relatorio_sim.registro[0].valor[1];
                    FatPotCap_Sim[i] = relatorio_sim.registro[0].valor[2];
                }

                if (i == 13)
                {
                    // zera
                    FatPotP[i] = relatorio.registro[11].valor[0];
                    FatPotInd[i] = relatorio.registro[11].valor[1];
                    FatPotCap[i] = relatorio.registro[11].valor[2];

                    // zera simulacao
                    FatPotP_Sim[i] = relatorio_sim.registro[11].valor[0];
                    FatPotInd_Sim[i] = relatorio_sim.registro[11].valor[1];
                    FatPotCap_Sim[i] = relatorio_sim.registro[11].valor[2];
                }

                if (i >= 1 && i <= 12)
                {
                    // copia
                    j = i - 1;

                    FatPotP[i] = relatorio.registro[j].valor[0];
                    FatPotInd[i] = relatorio.registro[j].valor[1];
                    FatPotCap[i] = relatorio.registro[j].valor[2];

                    // copia simulacao
                    FatPotP_Sim[i] = relatorio_sim.registro[j].valor[0];
                    FatPotInd_Sim[i] = relatorio_sim.registro[j].valor[1];
                    FatPotCap_Sim[i] = relatorio_sim.registro[j].valor[2];

                    // verifica fatpot minimo
                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPotP[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPotP[i]);

                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPotInd[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPotInd[i]);

                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPotCap[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPotCap[i]);

                    // verifica fatpot minimo simulacao
                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPotP_Sim[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPotP_Sim[i]);

                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPotInd_Sim[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPotInd_Sim[i]);

                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPotCap_Sim[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPotCap_Sim[i]);
                }
            }

            if (FatPot_min > 0.92)
                FatPot_min = 0.92;

            FatPot_min = FatPot_min - (FatPot_min * 0.01);

            ViewBag.FatPotMin = FatPot_min;

            ViewBag.FatPotP = FatPotP;
            ViewBag.FatPotInd = FatPotInd;
            ViewBag.FatPotCap = FatPotCap;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Meses = Meses;

            // simulacao
            ViewBag.FatPotP_Sim = FatPotP_Sim;
            ViewBag.FatPotInd_Sim = FatPotInd_Sim;
            ViewBag.FatPotCap_Sim = FatPotCap_Sim;
            ViewBag.Datas_Sim = Datas_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;
            ViewBag.Meses_Sim = Meses_Sim;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.FatorPotencia;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Anual;

            // data atual
            ViewBag.DataAtual = string.Format("{0:yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:yyyy}", DataAtual);

            // fator de potencia Mais Indutivo fora ponta capacitivo 
            ViewBag.FatPot_MaisIndFPC = string.Format("{0:0.000}", analise.analise_valor.maximo[2]);
            DateTime FatPot_MaisIndFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[2]);
            if (FatPot_MaisIndFPC_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndFPC_DataHora = String.Format("{0:d} {1:HH:mm}", FatPot_MaisIndFPC_DataHora, FatPot_MaisIndFPC_DataHora);
            else
                ViewBag.FatPot_MaisIndFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndFPC_DataHoraN = FatPot_MaisIndFPC_DataHora;

            // fator de potencia Mais Indutivo fora ponta capacitivo simulacao
            ViewBag.FatPot_MaisIndFPC_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.maximo[2]);
            DateTime FatPot_MaisIndFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[2]);
            if (FatPot_MaisIndFPC_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisIndFPC_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", FatPot_MaisIndFPC_DataHora_Sim, FatPot_MaisIndFPC_DataHora_Sim);
            else
                ViewBag.FatPot_MaisIndFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndFPC_DataHoraN_Sim = FatPot_MaisIndFPC_DataHora_Sim;

            // fator de potencia Mais Indutivo fora ponta indutivo 
            ViewBag.FatPot_MaisIndFPI = string.Format("{0:0.000}", analise.analise_valor.maximo[1]);
            DateTime FatPot_MaisIndFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[1]);
            if (FatPot_MaisIndFPI_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndFPI_DataHora = String.Format("{0:d} {1:HH:mm}", FatPot_MaisIndFPI_DataHora, FatPot_MaisIndFPI_DataHora);
            else
                ViewBag.FatPot_MaisIndFPI_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndFPI_DataHoraN = FatPot_MaisIndFPI_DataHora;

            // fator de potencia Mais Indutivo fora ponta indutivo simulacao
            ViewBag.FatPot_MaisIndFPI_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.maximo[1]);
            DateTime FatPot_MaisIndFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[1]);
            if (FatPot_MaisIndFPI_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisIndFPI_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", FatPot_MaisIndFPI_DataHora_Sim, FatPot_MaisIndFPI_DataHora_Sim);
            else
                ViewBag.FatPot_MaisIndFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndFPI_DataHoraN_Sim = FatPot_MaisIndFPI_DataHora_Sim;

            // fator de potenci Mais Indutivo ponta 
            ViewBag.FatPot_MaisIndP = string.Format("{0:0.000}", analise.analise_valor.maximo[0]);
            DateTime FatPot_MaisIndP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (FatPot_MaisIndP_DataHora.Year != 2000)
                ViewBag.FatPot_MaisIndP_DataHora = String.Format("{0:d} {1:HH:mm}", FatPot_MaisIndP_DataHora, FatPot_MaisIndP_DataHora);
            else
                ViewBag.FatPot_MaisIndP_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndP_DataHoraN = FatPot_MaisIndP_DataHora;

            // fator de potenci Mais Indutivo ponta simulacao
            ViewBag.FatPot_MaisIndP_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.maximo[0]);
            DateTime FatPot_MaisIndP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[0]);
            if (FatPot_MaisIndP_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisIndP_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", FatPot_MaisIndP_DataHora_Sim, FatPot_MaisIndP_DataHora_Sim);
            else
                ViewBag.FatPot_MaisIndP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatPot_MaisIndP_DataHoraN_Sim = FatPot_MaisIndP_DataHora_Sim;

            // fator de potencia Mais Capacitivo fora ponta capacitivo 
            ViewBag.FatPot_MaisCapFPC = string.Format("{0:0.000}", analise.analise_valor.minimo[2]);
            DateTime FatPot_MaisCapFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[2]);
            if (FatPot_MaisCapFPC_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapFPC_DataHora = String.Format("{0:d} {1:HH:mm}", FatPot_MaisCapFPC_DataHora, FatPot_MaisCapFPC_DataHora);
            else
                ViewBag.FatPot_MaisCapFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapFPC_DataHoraN = FatPot_MaisCapFPC_DataHora;

            // fator de potencia Mais Capacitivo fora ponta capacitivo simulacao
            ViewBag.FatPot_MaisCapFPC_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.minimo[2]);
            DateTime FatPot_MaisCapFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[2]);
            if (FatPot_MaisCapFPC_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisCapFPC_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", FatPot_MaisCapFPC_DataHora_Sim, FatPot_MaisCapFPC_DataHora_Sim);
            else
                ViewBag.FatPot_MaisCapFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapFPC_DataHoraN_Sim = FatPot_MaisCapFPC_DataHora_Sim;

            // fator de potencia Mais Capacitivo fora ponta indutivo 
            ViewBag.FatPot_MaisCapFPI = string.Format("{0:0.000}", analise.analise_valor.minimo[1]);
            DateTime FatPot_MaisCapFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[1]);
            if (FatPot_MaisCapFPI_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapFPI_DataHora = String.Format("{0:d} {1:HH:mm}", FatPot_MaisCapFPI_DataHora, FatPot_MaisCapFPI_DataHora);
            else
                ViewBag.FatPot_MaisCapFPI_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapFPI_DataHoraN = FatPot_MaisCapFPI_DataHora;

            // fator de potencia Mais Capacitivo fora ponta indutivo simulacao
            ViewBag.FatPot_MaisCapFPI_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.minimo[1]);
            DateTime FatPot_MaisCapFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[1]);
            if (FatPot_MaisCapFPI_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisCapFPI_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", FatPot_MaisCapFPI_DataHora_Sim, FatPot_MaisCapFPI_DataHora_Sim);
            else
                ViewBag.FatPot_MaisCapFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapFPI_DataHoraN_Sim = FatPot_MaisCapFPI_DataHora_Sim;

            // fator de potencia Mais Capacitivo ponta 
            ViewBag.FatPot_MaisCapP = string.Format("{0:0.000}", analise.analise_valor.minimo[0]);
            DateTime FatPot_MaisCapP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[0]);
            if (FatPot_MaisCapP_DataHora.Year != 2000)
                ViewBag.FatPot_MaisCapP_DataHora = String.Format("{0:d} {1:HH:mm}", FatPot_MaisCapP_DataHora, FatPot_MaisCapP_DataHora);
            else
                ViewBag.FatPot_MaisCapP_DataHora = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapP_DataHoraN = FatPot_MaisCapP_DataHora;

            // fator de potencia Mais Capacitivo ponta simulacao
            ViewBag.FatPot_MaisCapP_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.minimo[0]);
            DateTime FatPot_MaisCapP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[0]);
            if (FatPot_MaisCapP_DataHora_Sim.Year != 2000)
                ViewBag.FatPot_MaisCapP_DataHora_Sim = String.Format("{0:d} {1:HH:mm}", FatPot_MaisCapP_DataHora_Sim, FatPot_MaisCapP_DataHora_Sim);
            else
                ViewBag.FatPot_MaisCapP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatPot_MaisCapP_DataHoraN_Sim = FatPot_MaisCapP_DataHora_Sim;

            // fator de potencia No Periodo
            ViewBag.FatPot_NoPeriodoP = string.Format("{0:0.000}", analise.analise_valor.medio[0]);
            ViewBag.FatPot_NoPeriodoFPI = string.Format("{0:0.000}", analise.analise_valor.medio[1]);
            ViewBag.FatPot_NoPeriodoFPC = string.Format("{0:0.000}", analise.analise_valor.medio[2]);

            // fator de potencia No Periodo simulacao
            ViewBag.FatPot_NoPeriodoP_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.medio[0]);
            ViewBag.FatPot_NoPeriodoFPI_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.medio[1]);
            ViewBag.FatPot_NoPeriodoFPC_Sim = string.Format("{0:0.000}", analise_sim.analise_valor.medio[2]);

            // referencia
            ViewBag.RefenciaFPC = string.Format("{0:0.000}", -0.92);
            ViewBag.RefenciaFPI = string.Format("{0:0.000}", 0.92);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddYears(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Fator de Potencia Anual XLS
        private HSSFWorkbook FatPot_Anual_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 13; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // fator de potencia capacitivo
                numeroCelulaXLS(row, 1, ViewBag.FatPotCap[i], _3CellStyle);

                // fator de potencia indutivo
                numeroCelulaXLS(row, 2, ViewBag.FatPotInd[i], _3CellStyle);

                // fator de potencia ponta
                numeroCelulaXLS(row, 3, ViewBag.FatPotP[i], _3CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Potência", "Relatório Anual", _negritoCellStyle);

            // cabecalho capacitivo e indutivo
            string[] cab_fatpot = { "Fator de Potência", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data" };
            cabecalhoTabelaXLS(workbook, sheet, cab_fatpot, rowIndex++);

            // fatpot mais capacitivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Capacitivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisCapFPC), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisCapFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisCapFPI), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisCapFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisCapP), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisCapP_DataHoraN, _datahoraStyle);

            // fatpot mais indutivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Indutivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisIndFPC), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisIndFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisIndFPI), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisIndFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisIndP), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisIndP_DataHoraN, _datahoraStyle);

            // fatpot no periodo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "No Período", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_NoPeriodoFPC), _3CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_NoPeriodoFPI), _3CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_NoPeriodoP), _3CellStyle);

            // referencia
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Referência", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.RefenciaFPC), _3CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.RefenciaFPI), _3CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.RefenciaFPI), _3CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Fator de Potencia Anual XLS simulacao
        private HSSFWorkbook FatPot_Anual_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho simulacao
            string[] simulacao = { "Relatório de Fator de Potência - Simulação" };

            // simulação 
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas do titulo simulacao
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 3));

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 13; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[i], _dataStyle);

                // fator de potencia capacitivo
                numeroCelulaXLS(row, 1, ViewBag.FatPotCap_Sim[i], _3CellStyle);

                // fator de potencia indutivo
                numeroCelulaXLS(row, 2, ViewBag.FatPotInd_Sim[i], _3CellStyle);

                // fator de potencia ponta
                numeroCelulaXLS(row, 3, ViewBag.FatPotP_Sim[i], _3CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Potência - Simulação", "Relatório Anual", _negritoCellStyle);

            // cabecalho capacitivo e indutivo
            string[] cab_fatpot = { "Fator de Potência", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data" };
            cabecalhoTabelaXLS(workbook, sheet, cab_fatpot, rowIndex++);

            // fatpot mais capacitivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Capacitivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisCapFPC_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisCapFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisCapFPI_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisCapFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisCapP_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisCapP_DataHoraN_Sim, _datahoraStyle);

            // fatpot mais indutivo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mais Indutivo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_MaisIndFPC_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatPot_MaisIndFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_MaisIndFPI_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatPot_MaisIndFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_MaisIndP_Sim), _3CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatPot_MaisIndP_DataHoraN_Sim, _datahoraStyle);

            // fatpot no periodo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "No Período", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatPot_NoPeriodoFPC_Sim), _3CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatPot_NoPeriodoFPI_Sim), _3CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatPot_NoPeriodoP_Sim), _3CellStyle);

            // referencia
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Referência", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.RefenciaFPC), _3CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.RefenciaFPI), _3CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.RefenciaFPI), _3CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }
    }
}