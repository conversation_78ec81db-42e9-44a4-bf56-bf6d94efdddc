﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {
        // Relatorio Eventos
        // 10 -> Eventos
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Eventos_Descricao", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Eventos_Descricao(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char IDtipo_gateway, ref EVENTO pevento);

        // Tipos de Eventos
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Eventos_Tipos", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Eventos_Tipos(char tipo_interface, char IDtipo_gateway, ref TIPOS_EVENTO ptipos);


        //
        // EVENTOS
        //

        // GET: Relatorio Eventos
        public ActionResult Relat_Eventos(int IDCliente, int IDMedicao)
        {
            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Relat_Eventos");

            // relatorio eventos
            return (Relat_Grafico_Show(IDCliente, IDMedicao, TIPO_RELAT.Eventos));
        }

        // GET: Relatorio Eventos pela Gateway
        public ActionResult Relat_Eventos_Gateway(int IDCliente, int IDGateway, string DataHora)
        {
            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Relat_Eventos");

            // encontra medicao associada a gateway
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicaoMetodos.ListarPorIDGateway(IDGateway, "");

            // data hora fim
            DateTime datahora_fim = DateTime.Parse(DataHora);
            DateTime datahora_ini = datahora_fim.AddDays(-7);

            // salva cookie datahora (inicio)
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ini);

            // salva cookie datahora (final)
            CookieStore.SalvaCookie_Datahora("Relat_DataFim", datahora_fim);

            // salva tipo evento falha envio
            CookieStore.SalvaCookie_String("Relat_TipoEvento", "8");

            if( medicoes != null )
            {
                // relatorio eventos
                return (Relat_Grafico_Show(IDCliente, medicoes[0].IDMedicao, 10));
            }

            return View();
        }

        // Eventos
        private void Eventos(int IDCliente, int IDGateway, int IDTipoGateway, DATAHORA data_hora_ini, DATAHORA data_hora_fim)
        {
            // funcao relatorio
            int retorno = 0;

            // tipos de eventos
            TIPOS_EVENTO tipos_evento = new TIPOS_EVENTO();
            retorno = SmCalcDB_Eventos_Tipos((char)0, (char)IDTipoGateway, ref tipos_evento);

            // lista tipos de eventos
            var listaTiposEventos = new List<TiposEventosDescricao>();

            // verifica se tem eventos
            if (tipos_evento.num_tipos > 0)
            {
                // percorre lista
                int contador = 0;

                for (contador = 0; contador < tipos_evento.num_tipos; contador++)
                {
                    string str_aux;
                    TiposEventosDescricao tipoeventoDescricao = new TiposEventosDescricao();

                    // tipo
                    tipoeventoDescricao.Tipo = tipos_evento.tipos[contador].tipo;

                    // codigos
                    List<int> CodigosList = null;

                    // Converter o byte[] para String
                    str_aux = Encoding.GetEncoding("ISO-8859-1").GetString(tipos_evento.tipos[contador].codigos);

                    // remove \0
                    str_aux = str_aux.Replace("\0", "");

                    // copia codigos para lista
                    if (!String.IsNullOrEmpty(str_aux))
                    {
                        CodigosList = str_aux.Split('/')
                            .Select(possibleIntegerAsString =>
                            {
                                int parsedInteger = 0;
                                bool isInteger = int.TryParse(possibleIntegerAsString, out parsedInteger);
                                return new { isInteger, parsedInteger };
                            })
                            .Where(tryParseResult => tryParseResult.isInteger)
                            .Select(tryParseResult => tryParseResult.parsedInteger)
                            .ToList();
                    }

                    // copia lista de codigos
                    tipoeventoDescricao.CodigosList = CodigosList;

                    // descricao

                    // Converter o byte[] para String
                    str_aux = Encoding.GetEncoding("ISO-8859-1").GetString(tipos_evento.tipos[contador].descricao);

                    // remove \0
                    str_aux = str_aux.Replace("\0", "");

                    // copia descricao evento
                    tipoeventoDescricao.Descricao = str_aux;

                    // adiciona tipo evento tratado
                    listaTiposEventos.Add(tipoeventoDescricao);
                }
            }

            // copia lista de tipos de eventos
            ViewBag.NumTiposEventos = tipos_evento.num_tipos;
            ViewBag.listaTiposEventosDescricao = listaTiposEventos;

            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_gateway = IDGateway;

            // converte data e hora
            DateTime DataAtual = Funcoes_Converte.ConverteDataHora2DateTime(data_hora_ini);
            DateTime DataIni = Funcoes_Converte.ConverteDataHora2DateTime(data_hora_ini);
            DateTime DataFim = Funcoes_Converte.ConverteDataHora2DateTime(data_hora_fim);

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.Eventos;
            ViewBag.PeriodoRelat = " ";

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);

            // data e hora atual
            ViewBag.DataIni = string.Format("{0:d}", DataIni);
            ViewBag.HoraIni = string.Format("{0:HH:mm:ss}", DataIni);
            ViewBag.DataFim = string.Format("{0:d}", DataFim);
            ViewBag.HoraFim = string.Format("{0:HH:mm:ss}", DataFim);

            ViewBag.DataAtualN = DataIni;
            ViewBag.DataFinalN = DataFim;

            ViewBag.DataTextoAtualIni = string.Format("{0:d} {1:HH:mm:ss}", DataIni, DataIni);
            ViewBag.DataTextoAtualFim = string.Format("{0:d} {1:HH:mm:ss}", DataFim, DataFim);

            // le eventos
            EV_Metodos eventosMetodos = new EV_Metodos();
            List<EV_Dominio> listaEventos = eventosMetodos.ListarPorPeriodo(IDCliente, IDGateway, DataIni, DataFim);

            // lista de eventos
            List<EventosDescricao> listaEventosDescricao = new List<EventosDescricao>();

            // verifica se tem eventos
            if (listaEventos.Count() > 0)
            {
                // percorre lista
                int contador = 0;

                for (contador = 0; contador < listaEventos.Count(); contador++)
                {
                    EVENTO evento = new EVENTO();
                    EV_Dominio item_evento = new EV_Dominio();
                    EventosDescricao eventoDescricao = new EventosDescricao();
                    string descricao;

                    // copia evento
                    item_evento = listaEventos[contador];

                    // converte para tipo evento
                    Funcoes_Converte.ConverteDateTime2DataHora(ref evento.datahora, item_evento.DataHora);
                    evento.codigo = (short)item_evento.Evento;
                    evento.valor = item_evento.Valor;

                    // pega descricao
                    SmCalcDB_Eventos_Descricao((char)0, ref config_interface, (char)IDTipoGateway, ref evento);

                    // Converter o byte[] para String
                    descricao = Encoding.GetEncoding("ISO-8859-1").GetString(evento.descricao);

                    // remove \0
                    descricao = descricao.Replace("\0", "");

                    // copia descricao evento
                    eventoDescricao.Descricao = descricao;

                    // data e hora evento
                    eventoDescricao.DataHoraN = item_evento.DataHora;

                    // evento e valor
                    eventoDescricao.Evento = item_evento.Evento;
                    eventoDescricao.Valor = item_evento.Valor;

                    // copia data e hora evento
                    eventoDescricao.DataHora = String.Format("{0:d} {1:HH:mm:ss}", item_evento.DataHora, item_evento.DataHora);

                    // data e hora para sort
                    eventoDescricao.DataHora_Sort = String.Format("{0:yyyyMMddHHmmssfff}", item_evento.DataHora);

                    // percorre lista de tipos de eventos
                    eventoDescricao.Tipo = 0;

                    for (int contador2 = 0; contador2 < listaTiposEventos.Count(); contador2++)
                    {
                        // verifica se codigo esta na lista
                        if (listaTiposEventos[contador2].CodigosList.Contains(evento.codigo))
                        {
                            // pego codigo
                            eventoDescricao.Tipo = listaTiposEventos[contador2].Tipo;
                        }
                    }

                    // adiciona evento tratado
                    listaEventosDescricao.Add(eventoDescricao);
                }
            }

            // copia lista de eventos
            ViewBag.listaEventosDescricao = listaEventosDescricao;

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;

            return;
        }

        // Eventos XLS
        private HSSFWorkbook Eventos_XLS(int IDCliente, int IDMedicao)
        {

            // cookie de filtro
            string lista_selecao = "/" + CookieStore.LeCookie_String("Relat_TipoEvento") + "/";
            List<int> FiltroList = null;

            // copia filtros para lista
            if (!String.IsNullOrEmpty(lista_selecao))
            {
                FiltroList = lista_selecao.Split('/')
                    .Select(possibleIntegerAsString =>
                    {
                        int parsedInteger = 0;
                        bool isInteger = int.TryParse(possibleIntegerAsString, out parsedInteger);
                        return new { isInteger, parsedInteger };
                    })
                    .Where(tryParseResult => tryParseResult.isInteger)
                    .Select(tryParseResult => tryParseResult.parsedInteger)
                    .ToList();
            }


            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _datahorafullStyle = criaEstiloXLS(workbook, 13);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Eventos");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Código", "Evento" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // lista de eventos
            List<EventosDescricao> listaEventosDescricao = ViewBag.listaEventosDescricao;

            // percorre eventos
            foreach (EventosDescricao evento in listaEventosDescricao)
            {
                // verifica se NAO pertence ao filtro
                if( FiltroList != null )
                {
                    if( FiltroList.Count > 0 )
                    {
                        if (!FiltroList.Contains(evento.Tipo))
                        {
                            continue;
                        }
                    }
                }

                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, evento.DataHoraN, _datahorafullStyle);

                // codigo
                numeroCelulaXLS(row, 1, evento.Evento, _intCellStyle);

                // evento
                textoCelulaXLS(row, 2, evento.Descricao);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            sheet.SetColumnWidth(0, 6000);
            sheet.SetColumnWidth(1, 4000);
            sheet.SetColumnWidth(2, 18000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Eventos", "", _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(9).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }

        public void EventosPeriodo(int IDCliente, int IDMedicao, DateTime data_hora_ini, DateTime data_hora_fim)
        {

            // le supervisao da medicao
            SupervMedicoesMetodos medicoesMetodos = new SupervMedicoesMetodos();
            SupervMedicoesDominio listaMedicoes = medicoesMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

            // tipo medicao
            int TipoMedicao = listaMedicoes.IDTipoMedicao_MD;
            bool TemEventos = false;

            if( TipoMedicao == 0 || TipoMedicao == 2 || TipoMedicao == 3 || TipoMedicao == 4)
            {
                TemEventos = true;
                ViewBag.TemEventos = TemEventos;
            }
            else
            {
                ViewBag.TemEventos = TemEventos;
                return;
            }

            // funcao relatorio eventos
            int retorno = 0;

            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_gateway = listaMedicoes.IDGW_GW;

            // tipos de eventos
            TIPOS_EVENTO tipos_evento = new TIPOS_EVENTO();
            retorno = SmCalcDB_Eventos_Tipos((char)0, (char)listaMedicoes.IDTipoGateway_GW, ref tipos_evento);

            // lista tipos de eventos
            var listaTiposEventos = new List<TiposEventosDescricao>();

            // verifica se tem eventos
            if (tipos_evento.num_tipos > 0)
            {
                // percorre lista
                int contador = 0;

                for (contador = 0; contador < tipos_evento.num_tipos; contador++)
                {
                    string str_aux;
                    TiposEventosDescricao tipoeventoDescricao = new TiposEventosDescricao();

                    // tipo
                    tipoeventoDescricao.Tipo = tipos_evento.tipos[contador].tipo;

                    // codigos
                    List<int> CodigosList = null;

                    // Converter o byte[] para String
                    str_aux = Encoding.GetEncoding("ISO-8859-1").GetString(tipos_evento.tipos[contador].codigos);

                    // remove \0
                    str_aux = str_aux.Replace("\0", "");

                    // copia codigos para lista
                    if (!String.IsNullOrEmpty(str_aux))
                    {
                        CodigosList = str_aux.Split('/')
                            .Select(possibleIntegerAsString =>
                            {
                                int parsedInteger = 0;
                                bool isInteger = int.TryParse(possibleIntegerAsString, out parsedInteger);
                                return new { isInteger, parsedInteger };
                            })
                            .Where(tryParseResult => tryParseResult.isInteger)
                            .Select(tryParseResult => tryParseResult.parsedInteger)
                            .ToList();
                    }

                    // copia lista de codigos
                    tipoeventoDescricao.CodigosList = CodigosList;

                    // descricao

                    // Converter o byte[] para String
                    str_aux = Encoding.GetEncoding("ISO-8859-1").GetString(tipos_evento.tipos[contador].descricao);

                    // remove \0
                    str_aux = str_aux.Replace("\0", "");

                    // copia descricao evento
                    tipoeventoDescricao.Descricao = str_aux;

                    // adiciona tipo evento tratado
                    listaTiposEventos.Add(tipoeventoDescricao);
                }
            }

            // copia lista de tipos de eventos
            ViewBag.NumTiposEventos = tipos_evento.num_tipos;
            ViewBag.listaTiposEventosDescricao = listaTiposEventos;

            // le eventos
            EV_Metodos eventosMetodos = new EV_Metodos();
//            List<EV_Dominio> listaEventos = eventosMetodos.ListarPorPeriodo(IDCliente, listaMedicoes.IDGW_GW, data_hora_ini, data_hora_fim, 500);
            List<EV_Dominio> listaEventos = eventosMetodos.ListarPorPeriodo(IDCliente, listaMedicoes.IDGW_GW, data_hora_ini, data_hora_fim);

            // lista de eventos
            List<EventosDescricao> listaEventosDescricao = new List<EventosDescricao>();

            // verifica se tem eventos
            if (listaEventos.Count() > 0)
            {
                // percorre lista
                int contador = 0;

                for (contador = 0; contador < listaEventos.Count(); contador++)
                {
                    EVENTO evento = new EVENTO();
                    EV_Dominio item_evento = new EV_Dominio();
                    EventosDescricao eventoDescricao = new EventosDescricao();
                    string descricao;

                    // copia evento
                    item_evento = listaEventos[contador];

                    // converte para tipo evento
                    Funcoes_Converte.ConverteDateTime2DataHora(ref evento.datahora, item_evento.DataHora);
                    evento.codigo = (short)item_evento.Evento;
                    evento.valor = item_evento.Valor;

                    // pega descricao
                    SmCalcDB_Eventos_Descricao((char)0, ref config_interface, (char)listaMedicoes.IDTipoGateway_GW, ref evento);

                    // Converter o byte[] para String
                    descricao = Encoding.GetEncoding("ISO-8859-1").GetString(evento.descricao);

                    // remove \0
                    descricao = descricao.Replace("\0", "");

                    // copia descricao evento
                    eventoDescricao.Descricao = descricao;

                    // copia data evento
                    eventoDescricao.DataHora = String.Format("{0:d} {1:HH:mm:ss}", item_evento.DataHora, item_evento.DataHora);

                    // data e hora para sort
                    eventoDescricao.DataHora_Sort = String.Format("{0:yyyyMMddHHmmssfff}", item_evento.DataHora);

                    // percorre lista de tipos de eventos
                    eventoDescricao.Tipo = 0;

                    for (int contador2 = 0; contador2 < listaTiposEventos.Count(); contador2++)
                    {
                        // verifica se codigo esta na lista
                        if (listaTiposEventos[contador2].CodigosList.Contains(evento.codigo))
                        {
                            // pego codigo
                            eventoDescricao.Tipo = listaTiposEventos[contador2].Tipo;
                        }
                    }

                    // verifica se tipo UPLOAD
                    if (eventoDescricao.Tipo == 8)
                    {
                        if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
                        {
                            // adiciona evento tratado
                            listaEventosDescricao.Add(eventoDescricao);
                        }
                    }
                    else
                    {
                        // adiciona evento tratado
                        listaEventosDescricao.Add(eventoDescricao);
                    }
                }
            }

            // copia lista de eventos
            @ViewBag.listaEventosDescricao = listaEventosDescricao;

            return;
        }
    }
}