﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {
        // GET: Distribuição de Consumo
        public ActionResult DistribuicaoConsumo(int IDCliente)
        {
            // tela de ajuda - Distribuição de Consumo
            CookieStore.SalvaCookie_String("PaginaAjuda", "DistribuicaoCons");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "DistribuicaoConsumo");
            CookieStore.SalvaCookie_Int("_IDDistribuicaoConsumo", 0);

            // le cookies
            LeCookies_SmartEnergy();

            // le distribuições
            DistribuicaoConsumoMetodos distribuicaoMetodos = new DistribuicaoConsumoMetodos();
            List<DistribuicaoConsumoDominio> listaDistribuicoes = distribuicaoMetodos.ListarPorIDCliente(IDCliente);
            ViewBag.listaDistribuicoes = listaDistribuicoes;

            // data atual
            DateTime hoje = DateTime.Now;
            hoje = hoje.AddDays(-1);

            DateTime datahora_inicio = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 0, 0);
            DateTime datahora_fim = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 0, 0);

            DATAHORA data_hora_inicio = new DATAHORA();
            DATAHORA data_hora_fim = new DATAHORA();


            // le cookie datahora
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            if (datahora_cookie.Year != 2000)
            {
                datahora_inicio = new DateTime(datahora_cookie.Year, datahora_cookie.Month, datahora_cookie.Day, 0, 0, 0);
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora_inicio, datahora_inicio);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_inicio);


            // le cookie datahora (final)
            datahora_cookie = CookieStore.LeCookie_Datahora("Relat_DataFim");

            if (datahora_cookie.Year != 2000)
            {
                datahora_fim = new DateTime(datahora_cookie.Year, datahora_cookie.Month, datahora_cookie.Day, 0, 0, 0);
            }


            // verifica se fim eh menor que inicio
            if (datahora_fim < datahora_inicio)
            {
                // periodo de pelo menos 1 dia
                datahora_fim = datahora_inicio;
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora_fim, datahora_fim);

            // salva cookie datahora (final)
            CookieStore.SalvaCookie_Datahora("Relat_DataFim", datahora_fim);


            // data atual
            ViewBag.DataInicio = string.Format("{0:dd/MM/yyyy}", datahora_inicio);
            ViewBag.DataFim = string.Format("{0:dd/MM/yyyy}", datahora_fim);


            // IDDistribuicaoConsumo
            int IDDistribuicaoConsumo = ViewBag._IDDistribuicaoConsumo;

            if (listaDistribuicoes != null)
            {
                if (IDDistribuicaoConsumo == 0 && listaDistribuicoes.Count > 0)
                {
                    ViewBag._IDDistribuicaoConsumo = listaDistribuicoes[0].IDDistribuicaoConsumo;
                }
            }

            // valores
            List<DistribuicaoConsumoValor> valores_resultado = new List<DistribuicaoConsumoValor>();
            ViewBag.valores_resultado = valores_resultado;

            // lista de erros
            var listaErros = new List<string>();

            // erros
            ViewBag.listaErros = listaErros;

            // Nome Distribuição
            ViewBag.NomeDistribuicao = "";

            return View();
        }

        // GET: Distribuição de Consumo Print
        public ActionResult DistribuicaoConsumo_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDDistribuicaoConsumo
            int IDCliente = ViewBag._IDCliente;
            int IDDistribuicaoConsumo = ViewBag._IDDistribuicaoConsumo;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            DistribuicaoConsumo_Show(IDDistribuicaoConsumo, 0, data_str, data_str);

            // imprime
            return View();
        }

        // GET: Distribuição de Consumo EMAIL
        public async Task<ActionResult> DistribuicaoConsumo_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDDistribuicaoConsumo
            int IDCliente = ViewBag._IDCliente;
            int IDDistribuicaoConsumo = ViewBag._IDDistribuicaoConsumo;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            DistribuicaoConsumo_Show(IDDistribuicaoConsumo, 0, data_str, data_str);

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("DistribuicaoConsumo_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDDistribuicaoConsumo, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_DistribuicaoConsumo_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "DistribuicaoConsumoEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.DataTextoInicio", ViewBag.DataTextoInicio);
            message = message.Replace("ViewBag.DataTextoFim", ViewBag.DataTextoFim);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.NomeDistribuicao", ViewBag.NomeDistribuicao);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Distribuição de Consumo PDF
        public ActionResult DistribuicaoConsumo_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDDistribuicaoConsumo
            int IDCliente = ViewBag._IDCliente;
            int IDDistribuicaoConsumo = ViewBag._IDDistribuicaoConsumo;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            DistribuicaoConsumo_Show(IDDistribuicaoConsumo, 0, data_str, data_str);

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("DistribuicaoConsumo_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDDistribuicaoConsumo, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_DistribuicaoConsumo_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Distribuição de Consumo XLS
        public ActionResult DistribuicaoConsumo_XLS()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDDistribuicaoConsumo
            int IDCliente = ViewBag._IDCliente;
            int IDDistribuicaoConsumo = ViewBag._IDDistribuicaoConsumo;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            DistribuicaoConsumo_Show(IDDistribuicaoConsumo, 0, data_str, data_str);

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_DistribuicaoConsumo();

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("DistribuicaoConsumo_{0:000000}_{1:yyyyMMddHHmm}.xls", IDDistribuicaoConsumo, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Distribuição de Consumo XLS Download
        [HttpGet]
        public virtual ActionResult DistribuicaoConsumo_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }


        // Calcula Distribuição XLS
        private HSSFWorkbook XLS_DistribuicaoConsumo()
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // DISTRIBUIÇÃO DE CONSUMO
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Distribuição de Consumo");
            IRow row;

            // cabecalho
            string[] cabecalho = { "Grupo de Medições", "Consumo (kWh)", "Percentual (%)" };
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // valores
            List<DistribuicaoConsumoValor> valores_resultado = new List<DistribuicaoConsumoValor>();
            valores_resultado = ViewBag.valores_resultado;

            if (valores_resultado != null)
            {
                foreach (DistribuicaoConsumoValor distribuicao in valores_resultado)
                {
                    // linha
                    row = sheet.CreateRow(rowIndex++);

                    textoCelulaXLS(row, 0, distribuicao.NomeGrupoMedicoes);
                    numeroCelulaXLS(row, 1, distribuicao.Consumo, _1CellStyle);
                    numeroCelulaXLS(row, 2, distribuicao.Porcentagem, _1CellStyle);
                }

                // pula linha
                row = sheet.CreateRow(rowIndex++);
                row = sheet.CreateRow(rowIndex++);

                textoCelulaXLS(row, 0, "TOTAL");
                numeroCelulaXLS(row, 1, ViewBag.ConsumoTotal, _1CellStyle);
                numeroCelulaXLS(row, 2, 100.0, _1CellStyle);
            }

            // largura de cada coluna
            for (int i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 10000);


            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoDistribuicaoConsumoXLS(workbook, sheet, "Distribuição de Consumo", _negritoCellStyle);

            // largura de cada coluna
            for (int i = 0; i < sheet.GetRow(4).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }




        // GET: Distribuição de Consumo Atualizar
        public PartialViewResult _DistribuicaoConsumo_Atualizar(int IDDistribuicaoConsumo, int Navegacao, string DataInicio, string DataFim)
        {
            // calcula
            DistribuicaoConsumo_Show(IDDistribuicaoConsumo, Navegacao, DataInicio, DataFim);

            return PartialView();
        }

        // GET: Distribuição de Consumo Show
        public void DistribuicaoConsumo_Show(int IDDistribuicaoConsumo, int Navegacao, string Data, string DataFim)
        {
            // caso forcar data
            if (Navegacao == 100)
            {
                // pega data
                DateTime dateValue = DateTime.Parse(Data);
                DateTime dateValueFim = DateTime.Parse(DataFim);

                DateTime dh_inicio = new DateTime(dateValue.Year, dateValue.Month, dateValue.Day, 0, 0, 0);
                DateTime dh_fim = new DateTime(dateValueFim.Year, dateValueFim.Month, dateValueFim.Day, 0, 0, 0);

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Relat_Data", dh_inicio);

                // salva cookie datahora (final)
                CookieStore.SalvaCookie_Datahora("Relat_DataFim", dh_fim);
            }

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;

            // le distribuições
            DistribuicaoConsumoMetodos distribuicaoMetodos = new DistribuicaoConsumoMetodos();
            List<DistribuicaoConsumoDominio> listaDistribuicoes = distribuicaoMetodos.ListarPorIDCliente(IDCliente);
            ViewBag.listaDistribuicoes = listaDistribuicoes;

            // IDDistribuicaoConsumo
            ViewBag._IDDistribuicaoConsumo = IDDistribuicaoConsumo;

            if (listaDistribuicoes != null)
            {
                if (IDDistribuicaoConsumo == 0 && listaDistribuicoes.Count > 0)
                {
                    ViewBag._IDDistribuicaoConsumo = listaDistribuicoes[0].IDDistribuicaoConsumo;
                }
            }


            // inicio
            DateTime hoje = DateTime.Now;
            hoje = hoje.AddDays(-1);

            DateTime datahora_inicio = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 0, 0);
            DateTime datahora_fim = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 0, 0);

            DATAHORA data_hora_inicio = new DATAHORA();
            DATAHORA data_hora_fim = new DATAHORA();


            // le cookie datahora
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            if (datahora_cookie.Year != 2000)
            {
                datahora_inicio = new DateTime(datahora_cookie.Year, datahora_cookie.Month, datahora_cookie.Day, 0, 0, 0);
            }


            // le cookie datahora (final)
            datahora_cookie = CookieStore.LeCookie_Datahora("Relat_DataFim");

            if (datahora_cookie.Year != 2000)
            {
                datahora_fim = new DateTime(datahora_cookie.Year, datahora_cookie.Month, datahora_cookie.Day, 0, 0, 0);
            }


            // verifica se fim eh menor que inicio
            if (datahora_fim < datahora_inicio)
            {
                // periodo de pelo menos 1 dia
                datahora_fim = datahora_inicio;
            }


            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -3:    // dia anterior

                    datahora_inicio = datahora_inicio.AddDays(-1);
                    datahora_fim = datahora_fim.AddDays(-1);
                    break;

                case 3:    // dia seguinte

                    datahora_inicio = datahora_inicio.AddDays(1);
                    datahora_fim = datahora_fim.AddDays(1);
                    break;

                case -4:    // mes anterior

                    datahora_inicio = datahora_inicio.AddMonths(-1);
                    datahora_fim = datahora_fim.AddMonths(-1);
                    break;

                case 4:    // mes seguinte

                    datahora_inicio = datahora_inicio.AddMonths(1);
                    datahora_fim = datahora_fim.AddMonths(1);
                    break;
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora_inicio, datahora_inicio);
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora_fim, datahora_fim);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_inicio);

            // salva cookie datahora (final)
            CookieStore.SalvaCookie_Datahora("Relat_DataFim", datahora_fim);

            // calcula
            Calc_DistribuicaoConsumo(IDDistribuicaoConsumo, data_hora_inicio, data_hora_fim);

            return;
        }

        // Calcula Distribuição Consumo
        private void Calc_DistribuicaoConsumo(int IDDistribuicaoConsumo, DATAHORA datahora_inicio, DATAHORA datahora_fim)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // converte data e hora
            DateTime DataInicio = Funcoes_Converte.ConverteDataHora2DateTime(datahora_inicio);
            DateTime DataFim = Funcoes_Converte.ConverteDataHora2DateTime(datahora_fim);

            // lista de erros
            var listaErros = new List<string>();

            // data atual
            ViewBag.DataInicio = string.Format("{0:dd/MM/yyyy}", DataInicio);
            ViewBag.DataFim = string.Format("{0:dd/MM/yyyy}", DataFim);

            ViewBag.DataAtualN = DataInicio;
            ViewBag.DataFinalN = DataFim;

            ViewBag.DataTextoInicio = string.Format("{0:dd/MM/yyyy}", DataInicio);
            ViewBag.DataTextoFim = string.Format("{0:dd/MM/yyyy}", DataFim);

            // consumo total
            double ConsumoTotal = 0.0;


            // converte data e hora para calcular
            DateTime DataCalculoInicio = new DateTime(DataInicio.Year, DataInicio.Month, DataInicio.Day, 0, 0, 0);
            DateTime DataCalculoFim = new DateTime(DataFim.Year, DataFim.Month, DataFim.Day, 0, 0, 0);
            DataCalculoFim = DataCalculoFim.AddDays(1);


            // valores
            List<DistribuicaoConsumoValor> valores_resultado = new List<DistribuicaoConsumoValor>();

            // verifica se IDDistribuicaoConsumo valido
            ViewBag._IDDistribuicaoConsumo = IDDistribuicaoConsumo;

            if (IDDistribuicaoConsumo <= 0)
            {
                if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
                {
                    listaErros.Add("Não existem Distribuições de Consumo configuradas");
                }

                // distribuição
                ViewBag.NomeDistribuicao = "";

                // valores
                ViewBag.valores_resultado = valores_resultado;

                // consumo total
                ViewBag.ConsumoTotal = ConsumoTotal;

                // erros
                ViewBag.listaErros = listaErros;

                return;
            }


            // número de grupos de medições
            int numGruposMedicoes = 0;

            // le distribuição
            DistribuicaoConsumoMetodos distribuicaoMetodos = new DistribuicaoConsumoMetodos();
            DistribuicaoConsumoDominio distribuicao = distribuicaoMetodos.ListarPorId(IDDistribuicaoConsumo);

            if (distribuicao != null)
            {
                ViewBag.Distribuicao = distribuicao;
                ViewBag.NomeDistribuicao = distribuicao.Nome;

                // le grupos
                DistribuicaoConsumoGruposMedMetodos distribuicaoGruposMetodos = new DistribuicaoConsumoGruposMedMetodos();
                List<DistribuicaoConsumoGruposMedDominio> distribuicaoGrupos = distribuicaoGruposMetodos.ListarPorIDDistribuicao(IDDistribuicaoConsumo);

                // grupo de medicoes
                GrupoMedicoesMetodos grupoMedMetodos = new GrupoMedicoesMetodos();

                // EN metodos
                EN_Metodos ENMetodos = new EN_Metodos();

                // percorre grupos 
                if (distribuicaoGrupos != null)
                {
                    foreach (DistribuicaoConsumoGruposMedDominio distribuicaoGrupo in distribuicaoGrupos)
                    {
                        // grupo de medições
                        GrupoMedicoesDominio grupoMed = new GrupoMedicoesDominio();

                        // valores
                        DistribuicaoConsumoValor valor = new DistribuicaoConsumoValor();

                        // le grupo de medições 
                        grupoMed = grupoMedMetodos.ListarPorId(distribuicaoGrupo.IDGrupoMedicoes);

                        if (grupoMed != null)
                        {
                            if (grupoMed.IDGrupoMedicoes == distribuicaoGrupo.IDGrupoMedicoes)
                            {
                                // nome
                                valor.NomeGrupoMedicoes = grupoMed.Nome;
                                valor.Consumo = 0.0;
                                valor.Porcentagem = 0.0;

                                // medições
                                GrupoMedicoesMedMetodos grupoMedicoesMetodos = new GrupoMedicoesMedMetodos();
                                List<GrupoMedicoesMedDominio> medicoes = grupoMedicoesMetodos.ListarPorIDGrupoMedicoes(grupoMed.IDGrupoMedicoes);

                                if (medicoes != null)
                                {
                                    // percorre medições
                                    foreach (GrupoMedicoesMedDominio medicao in medicoes)
                                    {
                                        // consumo do período
                                        double consumo = 0.0;
                                        consumo = ENMetodos.ConsumoTotalPeriodo(IDCliente, medicao.IDMedicao, DataCalculoInicio, DataCalculoFim);

                                        // soma no total do grupo
                                        valor.Consumo += consumo;

                                        // soma no total
                                        ConsumoTotal += consumo;
                                    }
                                }

                                // copia para lista
                                valores_resultado.Add(valor);
                            }
                        }
                    }

                    // percorre resultado para calcular a porcentagem
                    foreach (DistribuicaoConsumoValor valor in valores_resultado)
                    {
                        // porcentagem
                        if (ConsumoTotal > 0.0)
                        {
                            valor.Porcentagem = (valor.Consumo / ConsumoTotal) * 100.0;
                        }
                    }

                    // valores
                    ViewBag.valores_resultado = valores_resultado;


                    // número de grupos de medições
                    numGruposMedicoes = distribuicaoGrupos.Count();

                    // pizza
                    string[] Nomes = new string[numGruposMedicoes];
                    double[] Consumo = new double[numGruposMedicoes];
                    double[] Porcentagem = new double[numGruposMedicoes];

                    for (int i=0; i<numGruposMedicoes; i++)
                    {
                        Nomes[i] = valores_resultado[i].NomeGrupoMedicoes;
                        Consumo[i] = valores_resultado[i].Consumo;
                        Porcentagem[i] = valores_resultado[i].Porcentagem;
                    }

                    ViewBag.Nomes = Nomes;
                    ViewBag.Consumo = Consumo;
                    ViewBag.Porcentagem = Porcentagem;
                }
            }
            else
            {
                if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
                {
                    listaErros.Add("Não existem Distribuições de Consumo configuradas");
                }

                // distribuição
                ViewBag.NomeDistribuicao = "";

                // valores
                ViewBag.valores_resultado = valores_resultado;
            }

            // pizza
            ViewBag.numGruposMedicoes = numGruposMedicoes;

            // consumo total
            ViewBag.ConsumoTotal = ConsumoTotal;

            // erros
            ViewBag.listaErros = listaErros;

            return;
        }
    }
}