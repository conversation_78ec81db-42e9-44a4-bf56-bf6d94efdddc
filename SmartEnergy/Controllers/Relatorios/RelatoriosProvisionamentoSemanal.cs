﻿using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {
        // fatura de energia eletrica
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_Fatura", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_Fatura(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, int id_simulacao, char tipo_contrato, char IDEstTarifaria, char EhProjetada, ref DATAHORA pdatahora_ini, ref DATAHORA pdatahora_fim, ref RESULT_ENERGIA_FATURA pfatura, ref RESULT_ENERGIA_FATURA pfatura_sim);


        //
        // PROVISIONAMENTO SEMANAL - CPFL
        //

        // Listas utilizadas
        private void PreparaListas_ContratosCCEE(int IDCliente, int IDEstado = 0)
        {
            // le tipos empresa
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposEmpresa = listatiposMetodos.ListarTodos("TipoEmpresa", false);
            ViewBag.listaTipoEmpresa = listatiposEmpresa;

            // le tipos sim não
            List<ListaTiposDominio> listatiposSimNao = listatiposMetodos.ListarTodos("TipoSimNao", false);
            ViewBag.listaTipoSimNao = listatiposSimNao;

            // le tipos Cidade
            CidadeEstadoPaisMetodos cidadeMetodos = new CidadeEstadoPaisMetodos();
            List<CidadesDominio> listatiposCidade = cidadeMetodos.ListarTodosCidades(IDEstado);
            ViewBag.listaTipoCidade = listatiposCidade;

            // le comercializadoras
            AgentesMetodos agentesMetodos = new AgentesMetodos();
            List<AgentesDominio> listatiposComercializadoras = agentesMetodos.ListarPorComercializadora();
            ViewBag.listaTipoComercializadoras = listatiposComercializadoras;

            // le Empresas
            EmpresasMetodos empresaMetodos = new EmpresasMetodos();
            List<EmpresasDominio> agentesCCEE = empresaMetodos.ListarPorAgenteCCEE(IDCliente);
            ViewBag.listaAgentesCCEE = agentesCCEE;

            // le tipos contrato medicao
            List<ListaTiposDominio> listatiposContratoMedicao = listatiposMetodos.ListarTodos("TipoContratoMedicao");
            ViewBag.listaTipoContratoMedicao = listatiposContratoMedicao;

            // le tipos estrutura tarifaria
            List<ListaTiposDominio> listatiposEstruturaTarifaria = listatiposMetodos.ListarTodos("TipoEstruturaTarifaria");
            ViewBag.listaTipoEstruturaTarifaria = listatiposEstruturaTarifaria;

            return;
        }

        // GET: Relatorio Provisionamento Semanal - Clientes
        public ActionResult Relat_Provisionamento_Semanal_Clientes()
        {
            // tela de ajuda - relatorio provisionamento semanal
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // le cookies
            LeCookies_SmartEnergy();

            // lista de clientes
            List<int> ConfigCliList = ViewBag._ConfigCli;

            // le clientes - somente ativos
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clientesMetodos.ListarTodos(ConfigCliList, 1);

            // percorre clientes e descobre numero de medicoes associadas
            foreach (ClientesDominio cliente in listaClientes)
            {
                // menos o cliente zero
                if (cliente.IDCliente == 0)
                {
                    continue;
                }

                // numero de empresas com este cliente (CPFL - Unidades Consumidoras)
                EmpresasMetodos empresasMetodos = new EmpresasMetodos();
                int NumEmpresas = empresasMetodos.NumEmpresasCliente(cliente.IDCliente);
                cliente.NumEmpresas = NumEmpresas;

                // numero de medicoes REAIS com este cliente
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                int NumMedicoesFisicas = medicoesMetodos.NumMedicoesClienteTipo(cliente.IDCliente, 1);
                cliente.NumMedicoes = NumMedicoesFisicas;

                // numero de gateways com este cliente
                int NumGateways = medicoesMetodos.NumGatewaysCliente(cliente.IDCliente, 0);
                cliente.NumGateways = NumGateways;
            }

            return View(listaClientes);
        }

        // GET: Relatorio Provisionamento Semanal - Empresas
        public ActionResult Relat_Provisionamento_Semanal_Empresas(int IDCliente)
        {
            // salva cookie
            CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);

            // le contrato cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cli = clienteMetodos.ListarPorId(IDCliente);
            CookieStore.SalvaCookie_Int("IDTipoContrato", cli.IDTipoContrato);
            CookieStore.SalvaCookie_Int("IDTipoSupervisao", cli.IDTipoSupervisao);
            CookieStore.SalvaCookie_String("Nome_GrupoUnidades", cli.Nome_GrupoUnidades);
            CookieStore.SalvaCookie_String("Nome_Unidades", cli.Nome_Unidades);
            CookieStore.SalvaCookie_Int("IDTipoGrafico_Demanda", cli.IDTipoGrafico_Demanda);

            // tela de ajuda - relatorio provisionamento semanal
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // le cookies
            LeCookies_SmartEnergy();

            // prepara listas
            PreparaListas_ContratosCCEE(IDCliente);

            // le empresas
            EmpresasMetodos empresasMetodos = new EmpresasMetodos();
            List<EmpresasDominio> listaEmpresas = empresasMetodos.ListarPorIDCliente(IDCliente);

            // le contratos e verifica quais empresas tem contrato 
            List<EmpresasDominio> EmpresasComContrato = new List<EmpresasDominio>();

            // verifica se tem empresas
            if (listaEmpresas != null)
            {
                // percorre empresas
                foreach (EmpresasDominio empresa in listaEmpresas)
                {
                    // verifica se empresa tem contrato vigente
                    ContratosCCEEMetodos contratosMetodos = new ContratosCCEEMetodos();
                    List<ContratosCCEEDominio> contratosEnergia = contratosMetodos.ListarPorIDEmpresa(empresa.IDEmpresa, TIPO_CONTRATO_STATUS.Vigente);

                    // verifica se tem contratos
                    if (contratosEnergia != null)
                    {
                        // verifica se tem contratos
                        if (contratosEnergia.Count > 0)
                        {
                            // inicialmente não tem 
                            bool tem_empresa = false;

                            // verifica na lista se ja tem a empresa
                            foreach (EmpresasDominio empr in EmpresasComContrato)
                            {
                                // verifica se ja tem a empresa
                                if (empr.IDEmpresa == empresa.IDEmpresa)
                                {
                                    // ja tem a empresa
                                    tem_empresa = true;

                                    break;
                                }
                            }

                            // verifica se nao tem a empresa
                            if (!tem_empresa)
                            {
                                // copia empresa
                                EmpresasDominio nova_empresa = new EmpresasDominio();
                                nova_empresa = empresa;

                                // adiciona na lista
                                EmpresasComContrato.Add(nova_empresa);
                            }
                        }
                    }
                }
            }

            return View(EmpresasComContrato);
        }


        // GET: Relatorio Provisionamento Semanal
        public ActionResult Relat_Provisionamento_Semanal(int IDEmpresa)
        {

            //
            // Empresa
            //

            // IDEmpresa
            CookieStore.SalvaCookie_Int("_IDEmpresa", IDEmpresa);

            // le empresa
            EmpresasMetodos empresasMetodos = new EmpresasMetodos();
            EmpresasDominio empresa = empresasMetodos.ListarPorId(IDEmpresa);

            //
            // Contratos da Empresa
            //

            // le contratos CCEE vigentes da empresa
            ContratosCCEEMetodos contratosMetodos = new ContratosCCEEMetodos();
            List<ContratosCCEEDominio> contratosEnergia = contratosMetodos.ListarPorIDEmpresa(IDEmpresa, TIPO_CONTRATO_STATUS.Vigente);

            // percorre contratos para resgatar nome dos clientes e empresas
            if (contratosEnergia != null && empresa != null)
            {
                foreach (ContratosCCEEDominio contrato in contratosEnergia)
                {
                    // informações
                    contrato.NomeCliente = empresa.RazaoSocial;
                    contrato.RazaoSocial = empresa.RazaoSocial;
                    contrato.SiglaCCEE = empresa.SiglaCCEE;
                    contrato.CNPJ = empresa.CNPJ;
                    contrato.IDEstado = empresa.IDEstado;
                    contrato.IDCidade = empresa.IDCidade;
                    contrato.Logo = empresa.Logo;
                }
            }

            ViewBag.listaContratos = contratosEnergia;

            //
            // Medição da Unidade Consumidora
            //

            // medição e gateway da unidade consumidora
            MedicoesDominio medicao = new MedicoesDominio();
            GatewaysDominio gateway = new GatewaysDominio();

            // encontra IDMedicao da unidade consumidora da empresa
            int IDMedicao_UnidadeConsumidora = Encontra_IDMedicao_UnidadeConsumidora_Principal(IDEmpresa, ref medicao, ref gateway);

            // verifica se existe medição
            if (IDMedicao_UnidadeConsumidora > 0 && medicao != null && gateway != null)
            {
                // salva cookie medicao
                CookieStore.SalvaCookie_Int("_IDMedicao", medicao.IDMedicao);
                CookieStore.SalvaCookie_Int("IDTipoMedicao", medicao.IDTipoMedicao);
                CookieStore.SalvaCookie_Int("_IDGateway", medicao.IDGateway);
                CookieStore.SalvaCookie_Int("IDTipoGateway", gateway.IDTipoGateway);
            }
            else
            {
                // insere cookies invalidos
                CookieStore.SalvaCookie_Int("_IDMedicao", -10);
                CookieStore.SalvaCookie_Int("IDTipoMedicao", -10);
                CookieStore.SalvaCookie_Int("_IDGateway", -10);
                CookieStore.SalvaCookie_Int("IDTipoGateway", -10);
            }


            //
            // Informações do relatório
            //

            // tela de ajuda - relatorio provisionamento semanal
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");


            // verifica qual o tipo:
            // Tipo 1 = 1 contrato para 1 unidade consumidora
            // Tipo 2 = 1 contrato para N unidades consumidoras
            // Tipo 3 = N contratos para N unidades consumidoras

            // número de contratos
            int numContratos = 0;

            // tipo do relatório
            int TipoRelat = TIPO_RELAT.Provisionamento_Semanal_Tipo1_Tipo2;

            if (contratosEnergia != null)
            {
                // número de contratos
                numContratos = contratosEnergia.Count;

                // verifica se tem 1 contrato
                if (numContratos == 1)
                {
                    // tipo do relatorio
                    TipoRelat = TIPO_RELAT.Provisionamento_Semanal_Tipo1_Tipo2;
                }
                else
                {
                    // tipo do relatorio
                    TipoRelat = TIPO_RELAT.Provisionamento_Semanal_Tipo3;
                }
            }

            ViewBag.numContratos = numContratos;


            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Relat_Provisionamento_Semanal");
            CookieStore.SalvaCookie_Int("Relat_Tipo", TipoRelat);

            // le cookies
            LeCookies_SmartEnergy();

            // data atual
            DateTime DataAtual = DateTime.Now;
            DataAtual = DataAtual.AddDays(-1);

            // le cookie datahora (atual ou inicial)
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            // verifica se existe datahora no cookie
            if (datahora_cookie.Year != 2000)
            {
                // copia data do cookie para ultima
                DataAtual = datahora_cookie;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", DataAtual);


            //
            // Data Fatura
            //

            // data inicio
            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, 1, 0, 0, 0);

            // data fim
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", data_hora_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", data_hora_fim);



            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", DataAtual);

            // tipo do relatorio
            ViewBag.TipoRelat = TipoRelat;

            // seta para semanal
            ViewBag.Relat_TipoPeriodo = TIPO_RELAT_PERIODO.Diario;

            // lista de erros
            var listaErros = new List<string>();

            // erros
            ViewBag.listaErros = listaErros;

            return View();
        }

        // GET: Relatorio Provisionamento Semanal Show
        private ActionResult Relat_Provisionamento_Semanal_Show(int IDEmpresa, int tipo_arquivo = 0)
        {
            //
            // Tipo do Relatório
            //

            // le cookies
            LeCookies_SmartEnergy();

            // tipo do relatorio
            int TipoRelat = ViewBag.Relat_Tipo;


            //
            // Data do Relatório
            //

            // ano atual
            DateTime DataAtual = DateTime.Now;
            DataAtual = DataAtual.AddDays(-1);

            // le cookie datahora (atual ou inicial)
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            // verifica se existe datahora no cookie
            if (datahora_cookie.Year != 2000)
            {
                // copia data do cookie para ultima
                DataAtual = datahora_cookie;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", DataAtual);


            //
            // Data Fatura
            //

            // data inicio
            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, 1, 0, 0, 0);

            // data fim
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", data_hora_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", data_hora_fim);



            // tipo do relatorio
            ViewBag.TipoRelat = TipoRelat;

            // View do relatorio
            string viewRelatorio = "_Provisionamento_Semanal";


            //
            // CALCULA PROVISIONAMENTO
            //

            // calcula
            int numContratos = Calc_Provisionamento_Semanal(IDEmpresa, DataAtual);

            if (numContratos < 1)
            {
                var returnedData = new
                {
                    caminhoCompleto = ""
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            if (numContratos == 1)
            {
                viewRelatorio = "_Provisionamento_Semanal_Tipo1_Tipo2";
            }
            else
            {
                viewRelatorio = "_Provisionamento_Semanal_Tipo3_NUnidades";

                // verifica quantidade de unidades consumidoras
                List<Provisionamento_UnidadesConsumidoras> unidCons_Unificados = ViewBag.unidadesConsumidoras;

                if (unidCons_Unificados != null)
                {
                    if (unidCons_Unificados.Count == 1)
                    {
                        viewRelatorio = "_Provisionamento_Semanal_Tipo3_1Unidade";
                    }
                }
            }


            // verifica se PDF
            if (tipo_arquivo == 1)
            {
                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.pdf", viewRelatorio, IDEmpresa, data_atual);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Portrait,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna nome do arquivo
                var returnedData = new
                {
                    nomeArquivo = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            // verifica se EMAIL
            if (tipo_arquivo == 3)
            {
                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.pdf", viewRelatorio, IDEmpresa, data_atual);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Portrait,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna 
                ViewBag.caminhoCompleto = caminhoCompleto;
                var returnedData = new
                {
                    caminhoCompleto = caminhoCompleto
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            return View();
        }



        // Calcula Provisionamento Semanal
        private int Calc_Provisionamento_Semanal(int IDEmpresa, DateTime DataAtual)
        {
            //
            // Contratos de Energia da Empresa
            //
            List<ContratosCCEEDominio> contratosEnergia = new List<ContratosCCEEDominio>();

            // lê contratos de energia
            ContratosEnergia(IDEmpresa, ref contratosEnergia, DataAtual);


            //
            // Calcula Provisionamento Semanal
            //

            // verifica qual o tipo:
            // Tipo 1 = 1 contrato para 1 unidade consumidora
            // Tipo 2 = 1 contrato para N unidades consumidoras
            // Tipo 3 = N contratos para N unidades consumidoras

            // número de contratos
            int numContratos = 0;

            // tipo do relatório
            int TipoRelat = 0;

            if (contratosEnergia != null)
            {
                // número de contratos
                numContratos = contratosEnergia.Count;

                // verifica se tem 1 contrato
                if (numContratos == 1)
                {
                    // Calcula Provisionamento Semanal Tipo 1 ou Tipo 2
                    Calc_Provisionamento_Semanal_Tipo1_Tipo2(IDEmpresa, DataAtual, contratosEnergia[0]);

                    // tipo do relatorio
                    TipoRelat = TIPO_RELAT.Provisionamento_Semanal_Tipo1_Tipo2;
                }

                // verifica se tem N contratos
                if (numContratos > 1)
                {
                    // Calcula Provisionamento Semanal Tipo 3
                    Calc_Provisionamento_Semanal_Tipo3(IDEmpresa, DataAtual, contratosEnergia);

                    // tipo do relatorio
                    TipoRelat = TIPO_RELAT.Provisionamento_Semanal_Tipo3;
                }
            }


            //
            // Resultado
            //

            ViewBag.contratosEnergia = contratosEnergia;
            ViewBag.numContratos = numContratos;


            // lista de erros
            var listaErros = new List<string>();

            // erros
            ViewBag.listaErros = listaErros;

            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", DataAtual);


            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", DataAtual);


            //
            // Data Fatura
            //

            // data inicio
            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, 1, 0, 0, 0);

            // data fim
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", data_hora_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", data_hora_fim);



            // tipo do relatorio
            ViewBag.TipoRelat = TipoRelat;

            // seta para semanal
            ViewBag.Relat_TipoPeriodo = TIPO_RELAT_PERIODO.Diario;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.MenuTexts.MenuLateralRelatoriosProvisionamentoSemanal;
            ViewBag.PeriodoRelat = " ";


            // número de contratos
            return (numContratos);
        }

        // Contratos de Energia da Empresa
        private void ContratosEnergia(int IDEmpresa, ref List<ContratosCCEEDominio> contratosEnergia, DateTime DataAtual)
        {

            //
            // Empresa
            //

            // le empresa
            EmpresasMetodos empresasMetodos = new EmpresasMetodos();
            EmpresasDominio empresa = empresasMetodos.ListarPorId(IDEmpresa);

            //
            // Contratos da Empresa
            //

            // le contratos CCEE vigentes da empresa
            ContratosCCEEMetodos contratosMetodos = new ContratosCCEEMetodos();
            contratosEnergia = contratosMetodos.ListarPorIDEmpresa(IDEmpresa, TIPO_CONTRATO_STATUS.Vigente);

            // número de contratos
            int numContratos = 0;

            // percorre contratos para preencher informações e ordenar
            if (contratosEnergia != null && empresa != null)
            {
                // ordem
                int ordem = 1;

                // número de contratos
                numContratos = contratosEnergia.Count;

                // percorre contratos
                foreach (ContratosCCEEDominio contrato in contratosEnergia)
                {
                    // preenche informações
                    contrato.NomeCliente = empresa.RazaoSocial;
                    contrato.RazaoSocial = empresa.RazaoSocial;
                    contrato.SiglaCCEE = empresa.SiglaCCEE;
                    contrato.CNPJ = empresa.CNPJ;
                    contrato.IDEstado = empresa.IDEstado;
                    contrato.IDCidade = empresa.IDCidade;
                    contrato.Logo = empresa.Logo;

                    // formata datas auxiliares
                    contrato.Vigencia_Inicio_aux = String.Format("{0:d}", contrato.Vigencia_Inicio);
                    contrato.Vigencia_Inicio_Sort_aux = String.Format("{0:yyyyMMddHHmmss}", contrato.Vigencia_Inicio);
                    contrato.Vigencia_Fim_aux = String.Format("{0:d}", contrato.Vigencia_Fim);
                    contrato.Vigencia_Fim_Sort_aux = String.Format("{0:yyyyMMddHHmmss}", contrato.Vigencia_Fim);

                    // preço reajustado da data
                    ContratosCCEE_PrecoReajustadoMetodos precoMetodos = new ContratosCCEE_PrecoReajustadoMetodos();
                    contrato.Reajuste_Preco = precoMetodos.PrecoReajustadoData(contrato.IDContratoCCEE, DataAtual);

                    // ordena
                    contrato.Ordem = ordem++;

                    // verifica se tem apenas 1 contrato
                    if (numContratos == 1)
                    {
                        // possui apenas 1 contrato, torno ele considerado
                        contrato.Considera = 1;
                    }

                    // atualiza ordem
                    contratosMetodos.Alterar_Ordem_Considera(contrato.IDContratoCCEE, contrato.Ordem, contrato.Considera);
                }
            }

            return;
        }


        //
        // AUXILIARES
        //

        // preenche info da unidade consumidora
        private bool InfoUnidadeConsumidora(ref Provisionamento_UnidadesConsumidoras unidConsumidora, int IDEmpresa, int IDMedicao = 0)
        {
            // le info da empresa
            EmpresasMetodos empresaMetodos = new EmpresasMetodos();
            EmpresasDominio empresa = empresaMetodos.ListarPorId(IDEmpresa);

            if (empresa != null)
            {
                // IDCliente e IDEmpresa
                unidConsumidora.IDCliente = empresa.IDCliente;
                unidConsumidora.IDEmpresa = IDEmpresa;

                // medicao e gateway
                MedicoesDominio medicao = new MedicoesDominio();
                GatewaysDominio gateway = new GatewaysDominio();

                if (IDMedicao > 0)
                {
                    unidConsumidora.IDMedicao = IDMedicao;

                    MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                    medicao = medicoesMetodos.ListarPorId(IDMedicao);
                }
                else
                {
                    unidConsumidora.IDMedicao = Encontra_IDMedicao_UnidadeConsumidora_Principal(IDEmpresa, ref medicao, ref gateway);
                }

                unidConsumidora.RazaoSocial = empresa.RazaoSocial;
                unidConsumidora.SiglaCCEE = empresa.SiglaCCEE;
                unidConsumidora.CNPJ = empresa.CNPJ;
                unidConsumidora.CNPJ_RazaoSocial = string.Format("{0} [{1}]", unidConsumidora.CNPJ, unidConsumidora.RazaoSocial);

                if (medicao != null)
                {
                    unidConsumidora.IDGateway = medicao.IDGateway;
                    unidConsumidora.PontoMedicao = medicao.PontoMedicao;
                    unidConsumidora.IDSubSistema = medicao.IDSubSistema;
                    unidConsumidora.IDContratoMedicao = medicao.IDContratoMedicao;
                    unidConsumidora.IDEstruturaTarifaria = medicao.IDEstruturaTarifaria;
                }

                // verifica se tem medicao
                if (unidConsumidora.IDMedicao <= 0)
                {
                    // erro
                    unidConsumidora.IDCliente = 0;
                    unidConsumidora.IDEmpresa = 0;
                    unidConsumidora.IDMedicao = 0;
                    unidConsumidora.RazaoSocial = "---";
                    unidConsumidora.SiglaCCEE = "---";
                    unidConsumidora.CNPJ = "00.000.000/0000-00";
                    unidConsumidora.CNPJ_RazaoSocial = string.Format("{0} [{1}]", unidConsumidora.CNPJ, unidConsumidora.RazaoSocial);
                    unidConsumidora.PontoMedicao = "---";
                    unidConsumidora.IDSubSistema = 0;

                    return (false);
                }

                return (true);
            }

            // erro
            unidConsumidora.IDCliente = 0;
            unidConsumidora.IDEmpresa = 0;
            unidConsumidora.IDMedicao = 0;
            unidConsumidora.RazaoSocial = "---";
            unidConsumidora.SiglaCCEE = "---";
            unidConsumidora.CNPJ = "00.000.000/0000-00";
            unidConsumidora.CNPJ_RazaoSocial = string.Format("{0} [{1}]", unidConsumidora.CNPJ, unidConsumidora.RazaoSocial);
            unidConsumidora.PontoMedicao = "---";
            unidConsumidora.IDSubSistema = 0;

            return (false);
        }

        // encontra IDMedicao da unidade consumidora da empresa
        private int Encontra_IDMedicao_UnidadeConsumidora_Principal(int IDEmpresa, ref MedicoesDominio medicao, ref GatewaysDominio gateway)
        {
            // IDMedicao unidade consumidora
            int IDMedicao_UnidadeConsumidora = 0;

            medicao.Nome = "---";
            medicao.IDMedicao = 0;
            medicao.IDSubSistema = 0;
            medicao.IDGateway = 0;

            gateway.IDGateway = 0;

            // leio gateways atreladas a esta empresa
            GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
            List<GatewaysDominio> gateways = gatewayMetodos.ListarPorIDEmpresa(IDEmpresa);

            if (gateways != null)
            {
                // percorre gateways e encontra primeira medição de energia marcada como unidade consumidora
                foreach (GatewaysDominio gate in gateways)
                {
                    // filtro, somente medições principais e em ordem de número de medição interna
                    string order = "AND IDCategoriaMedicao = 0 ORDER BY NumMedGateway";

                    // leio medições da gateway
                    MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                    List<MedicoesDominio> medicoes = medicaoMetodos.ListarPorIDGateway(gate.IDGateway, order);

                    if (medicoes != null)
                    {
                        // percorre medições e encontra primeira medição de energia marcada como unidade consumidora
                        foreach (MedicoesDominio med in medicoes)
                        {
                            // verifica se medição de energia elétrica e medição 0
                            //if (med.IDTipoMedicao == TIPO_MEDICAO.ENERGIA && med.NumMedGateway == 0)

                            // verifica se medição de energia elétrica
                            if (med.IDTipoMedicao == TIPO_MEDICAO.ENERGIA)
                            {
                                // copia
                                medicao = med;
                                gateway = gate;

                                // achou
                                IDMedicao_UnidadeConsumidora = med.IDMedicao;
                                break;
                            }
                        }
                    }

                    // verifica se achou
                    if (IDMedicao_UnidadeConsumidora > 0)
                    {
                        // interrompe busca
                        break;
                    }
                }
            }

            return (IDMedicao_UnidadeConsumidora);
        }
    }
}