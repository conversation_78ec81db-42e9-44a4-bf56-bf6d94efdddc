﻿using NPOI.HSSF.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Web.Hosting;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{

    public partial class RelatoriosController : Controller
    {
        // atualiza fechamentos
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_AtualizaFechamentos", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_AtualizaFechamentos(char tipo_interface, ref CONFIG_INTERFACE cfg_interface);


        // permissoes
        private void Permissoes()
        {
            // permissoes
            // 0 - permissao de Admin: ve e escreve em tudo
            // 1 - permissao de Gerente: ve tudo e escreve parte
            // 2 - permissao de Operador: ve tudo e nao pode escrever
            // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)
            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            switch (IDTipoAcesso)
            {
                case TIPO_ACESSO.MASTER:            // master
                case TIPO_ACESSO.GESTAL_ADMIN:      // admin

                    ViewBag.Permissao = PERMISSOES.ADMIN;
                    break;

                case TIPO_ACESSO.CONSULTOR:         // consultor
                case TIPO_ACESSO.CONSULTOR_ADMIN:   // consultor - administrador

                    ViewBag.Permissao = PERMISSOES.CONSULTOR;
                    break;

                case TIPO_ACESSO.CLIENTE_ADMIN:     // cliente - administrador

                    ViewBag.Permissao = PERMISSOES.GERENTE;
                    break;

                default:
                case TIPO_ACESSO.CLIENTE_OPER:      // operador
                case TIPO_ACESSO.GESTAL_VENDAS:     // vendas
                case TIPO_ACESSO.CONSULTOR_OPER:    // consultor - operador
                case TIPO_ACESSO.DEMONSTRACAO:      // demo

                    ViewBag.Permissao = PERMISSOES.OPERADOR;
                    break;

                case TIPO_ACESSO.GESTAL_PRODUCAO:   // producao

                    ViewBag.Permissao = PERMISSOES.PRODUCAO;
                    break;

                case TIPO_ACESSO.GESTAL_SUPORTE:    // suporte

                    ViewBag.Permissao = PERMISSOES.SUPORTE;
                    break;
            }
        }


        // GET: Relatorio Grafico
        private ActionResult Relat_Grafico_Show(int IDCliente, int IDMedicao, int TipoRelat, int tipo_arquivo = 0)
        {
            // salva cookie
            CookieStore.SalvaCookie_Int("_IDCliente", IDCliente);

            // le contrato cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cliente = clienteMetodos.ListarPorId(IDCliente);
            CookieStore.SalvaCookie_Int("IDTipoContrato", cliente.IDTipoContrato);
            CookieStore.SalvaCookie_Int("IDTipoSupervisao", cliente.IDTipoSupervisao);
            CookieStore.SalvaCookie_String("Nome_GrupoUnidades", cliente.Nome_GrupoUnidades);
            CookieStore.SalvaCookie_String("Nome_Unidades", cliente.Nome_Unidades);
            CookieStore.SalvaCookie_Int("IDTipoGrafico_Demanda", cliente.IDTipoGrafico_Demanda);

            // le supervisao da medicao
            SupervMedicoesMetodos medicoesMetodos = new SupervMedicoesMetodos();
            SupervMedicoesDominio listaMedicoes = medicoesMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

            // salva cookie 
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", listaMedicoes.IDTipoMedicao_MD);
            CookieStore.SalvaCookie_String("MedicaoNome", listaMedicoes.NMED);
            CookieStore.SalvaCookie_String("UnidadeNome", listaMedicoes.NUN);
            CookieStore.SalvaCookie_String("GrupoNome", listaMedicoes.NGU);
            CookieStore.SalvaCookie_Int("_IDGateway", listaMedicoes.IDGW_GW);
            CookieStore.SalvaCookie_Int("IDTipoGateway", listaMedicoes.IDTipoGateway_GW);
            CookieStore.SalvaCookie_Int("Relat_Tipo", TipoRelat);

            int IDGateway = listaMedicoes.IDGW_GW;
            int IDTipoGateway = listaMedicoes.IDTipoGateway_GW;

            // gráfico em barra
            int IDTipoGrafico_Demanda = 0;

            if (cliente != null)
            {
                // copia
                IDTipoGrafico_Demanda = cliente.IDTipoGrafico_Demanda;
            }

            // le cookies
            LeCookies_SmartEnergy();


            // simulacao 
            int IDSimulacaoCenario = 0;

            if (ViewBag._IDSimulacaoCenario != null)
            {
                // copia
                IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;
            }

            // verifica se deve aplicar simulacao
            bool aplicaSimulacao = false;

            if (ViewBag.AplicaSimulacao != null)
            {
                // copia
                aplicaSimulacao = ViewBag.AplicaSimulacao;
            }

            // verifica se possui cenário selecionado
            if (IDSimulacaoCenario > 0)
            {
                SimulacaoCenariosMetodos simMetodos = new SimulacaoCenariosMetodos();
                SimulacaoCenariosDominio simulacao = simMetodos.ListarPorID(IDSimulacaoCenario);

                // verifica se este cenário corresponde a medição atual
                if (simulacao.IDMedicao != IDMedicao)
                {
                    IDSimulacaoCenario = 0;
                    aplicaSimulacao = false;
                }
            }

            CookieStore.SalvaCookie_Bool("AplicaSimulacao", aplicaSimulacao);
            CookieStore.SalvaCookie_Int("_IDSimulacaoCenario", IDSimulacaoCenario);

            // verifica se NÃO deve aplicar simulação
            if (!aplicaSimulacao)
            {
                // zero cenário para gerar a fatura sem simulação
                IDSimulacaoCenario = 0;
            }


            // tipo de acesso do usuario
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // unidade de potencia
            UnidadePotencia(IDMedicao);

            // atualizo fechamentos
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = listaMedicoes.IDGW_GW;

            // verifica se deve utilizar outra gateway
            if (listaMedicoes.IDGateway_Fechamentos > 0)
            {
                config_interface.sweb.id_gateway = listaMedicoes.IDGateway_Fechamentos;
            }

            int retorno = SmCalcDB_AtualizaFechamentos((char)0, ref config_interface);


            // verifica tipo periodo
            if (ViewBag.Relat_TipoPeriodo == -10)
            {
                // seta para diario
                ViewBag.Relat_TipoPeriodo = 0;

                // salva cookie 
                CookieStore.SalvaCookie_Int("Relat_TipoPeriodo", 0);
            }

            // tipo periodo
            int TipoPeriodo = ViewBag.Relat_TipoPeriodo;
            bool TemPeriodo = true;

            // tela de ajuda - relatorios graficos
            string PaginaAjuda = "Relatorio_";

            switch (TipoRelat)
            {
                case TIPO_RELAT.Demanda_Ativa:      // demanda ativa
                    PaginaAjuda += "DemAtiva_";
                    break;

                case TIPO_RELAT.Demanda_Reativa:    // demanda reativa
                    PaginaAjuda += "DemReativa_";
                    break;

                case TIPO_RELAT.Consumo:            // consumo
                    PaginaAjuda += "Consumo_";
                    break;

                case TIPO_RELAT.FatPot:             // fator de potencia
                    PaginaAjuda += "FatPot_";
                    break;

                case TIPO_RELAT.FatCarga:           // fator de carga
                    PaginaAjuda += "FatCarga_";
                    break;

                case TIPO_RELAT.FatUtilizacao:      // fator de utilizacao
                    PaginaAjuda += "FatUtilizacao_";
                    break;

                case TIPO_RELAT.UFER:               // UFER Energia Reativa Excedente
                    PaginaAjuda += "UFER_";
                    break;

                case TIPO_RELAT.Eventos:            // eventos
                    PaginaAjuda += "EventosGateway";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.Utilidades:         // utilidades
                    PaginaAjuda += "Utilidades_";
                    break;

                case TIPO_RELAT.Analogica:          // analogica
                    PaginaAjuda += "Analogicas_";
                    break;

                case TIPO_RELAT.Ciclometro:         // ciclometro
                    PaginaAjuda += "Ciclometro_";
                    break;

                case TIPO_RELAT.ConsolidadoEnergia_Diario:      // consolidado diario energia
                    PaginaAjuda += "Consolidado_Energia_Diario";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.ConsolidadoEnergia_Mensal:      // consolidado mensal energia
                    PaginaAjuda += "Consolidado_Energia_Mensal";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.ConsolidadoEnergia_Anual:       // consolidado anual energia
                    PaginaAjuda += "Consolidado_Energia_Anual";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.ConsolidadoEA_Diario:    // consolidado diario analogicas
                    PaginaAjuda += "Consolidado_EA_Diario";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.ConsolidadoEA_Mensal:    // consolidado mensal analogicas
                    PaginaAjuda += "Consolidado_EA_Mensal";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.ConsolidadoEA_Anual:    // consolidado anual analogicas
                    PaginaAjuda += "Consolidado_EA_Anual";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.ConsolidadoUtilidades_Diario:    // consolidado diario utilidades
                    PaginaAjuda += "Consolidado_Utilidades_Diario";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.ConsolidadoUtilidades_Mensal:    // consolidado mensal utilidades
                    PaginaAjuda += "Consolidado_Utilidades_Mensal";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.ConsolidadoUtilidades_Anual:    // consolidado anual utilidades
                    PaginaAjuda += "Consolidado_Utilidades_Anual";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.TempoAtuacao_EntradasDigitais_Mensal:    // tempo de atuação mensal entradas digitais
                    PaginaAjuda = "TempoAtuacao_EntradasDigitais";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.TempoAtuacao_SaidasDigitais_Mensal:    // tempo de atuação mensal entradas digitais
                    PaginaAjuda = "TempoAtuacao_SaidasDigitais";
                    TemPeriodo = false;
                    break;

                default:
                    PaginaAjuda = "Contato";
                    TemPeriodo = false;
                    break;
            }

            // verifica se tem periodo
            if (TemPeriodo)
            {
                switch (TipoPeriodo)
                {
                    case TIPO_RELAT_PERIODO.Diario:     // diario
                        PaginaAjuda += "Diario";
                        break;

                    case TIPO_RELAT_PERIODO.Semanal:    // semanal
                        PaginaAjuda += "Semanal";
                        break;

                    case TIPO_RELAT_PERIODO.Mensal:     // mensal
                        PaginaAjuda += "Mensal";
                        break;

                    case TIPO_RELAT_PERIODO.Anual:      // anual
                        PaginaAjuda += "Anual";
                        break;
                }
            }

            CookieStore.SalvaCookie_String("PaginaAjuda", PaginaAjuda);
            ViewBag.PaginaAjuda = PaginaAjuda;


            // dia atual
            DateTime datahora_ultima = DateTime.Parse(listaMedicoes.DataHoraAtualizacao);

            // protege
            if (datahora_ultima.Year <= 2000)
            {
                datahora_ultima = DateTime.Now;
            }

            DATAHORA data_hora = new DATAHORA();
            DATAHORA data_hora_fim = new DATAHORA();

            // le cookie datahora (atual ou inicial)
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            // verifica se existe datahora no cookie
            if (datahora_cookie.Year != 2000)
            {
                // copia data do cookie para ultima
                datahora_ultima = datahora_cookie;

                // verifico se nao eh eventos
                if (TipoRelat != TIPO_RELAT.Eventos)
                {
                    // forco hora para nao voltar 1 dia 
                    datahora_ultima = Funcoes_Converte.ChangeDateTimePart(datahora_ultima, Funcoes_Converte.DateTimePart.Hours, 12);
                }
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_ultima);

            // caso for muito cedo, pego do dia anterior
            if (datahora_ultima.Hour < 12 && TipoRelat != TIPO_RELAT.Eventos)
            {
                // caso NAO for eventos, zero o horario
                datahora_ultima = Funcoes_Converte.ChangeDateTimePart(datahora_ultima, Funcoes_Converte.DateTimePart.Hours, 0);
                datahora_ultima = Funcoes_Converte.ChangeDateTimePart(datahora_ultima, Funcoes_Converte.DateTimePart.Minutes, 0);
                datahora_ultima = Funcoes_Converte.ChangeDateTimePart(datahora_ultima, Funcoes_Converte.DateTimePart.Seconds, 0);

                datahora_ultima = datahora_ultima.AddDays(-1);

                data_hora.data.dia = (char)datahora_ultima.Day;
                data_hora.data.mes = (char)datahora_ultima.Month;
                data_hora.data.ano = (short)datahora_ultima.Year;
                data_hora.hora.hora = (char)0;
                data_hora.hora.min = (char)0;
                data_hora.hora.seg = (char)0;
            }

            // caso NAO for eventos
            if (TipoRelat != TIPO_RELAT.Eventos)
            {
                // zero o horario
                datahora_ultima = Funcoes_Converte.ChangeDateTimePart(datahora_ultima, Funcoes_Converte.DateTimePart.Hours, 0);
                datahora_ultima = Funcoes_Converte.ChangeDateTimePart(datahora_ultima, Funcoes_Converte.DateTimePart.Minutes, 0);
                datahora_ultima = Funcoes_Converte.ChangeDateTimePart(datahora_ultima, Funcoes_Converte.DateTimePart.Seconds, 0);

                data_hora.hora.hora = (char)0;
                data_hora.hora.min = (char)0;
                data_hora.hora.seg = (char)0;
            }

            // le cookie datahora (final)
            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Relat_DataFim");

            // verifica se NAO eh eventos
            if (TipoRelat != TIPO_RELAT.Eventos)
            {
                // quando estiver em outros relatorios, faco sempre o periodo de 1 dia
                datahora_cookie_fim = datahora_ultima.AddDays(1);
            }
            else
            {
                // verifica se ainda não definiu data final
                if (datahora_cookie_fim.Year <= 2000)
                {
                    // caso não definiu período ainda, último é hoje e apresento 1 dia de eventos
                    DateTime atual = DateTime.Now;
                    datahora_ultima = new DateTime(atual.Year, atual.Month, atual.Day, 0, 0, 0);
                    datahora_cookie_fim = datahora_ultima.AddDays(1);
                }
            }

            // verifica se fim eh menor que inicio
            if (datahora_cookie_fim < datahora_ultima)
            {
                // periodo de pelo menos 1 dia
                datahora_cookie_fim = datahora_ultima.AddDays(1);
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_ultima);

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora_fim, datahora_cookie_fim);

            // salva cookie datahora (inícial)
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // salva cookie datahora (final)
            CookieStore.SalvaCookie_Datahora("Relat_DataFim", datahora_cookie_fim);


            // tipo do relatorio
            ViewBag.TipoRelat = TipoRelat;

            // View do relatorio
            string viewRelatorio = "";

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            // ler observacoes
            bool ler_observacoes = false;

            // tipo do relatorio
            switch (TipoRelat)
            {
                case TIPO_RELAT.Demanda_Ativa:          // Demanda Ativa

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            Dem_Ativa_Diario(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_Dem_Ativa_Diario";

                            // verifica se gráfico em barra
                            if (IDTipoGrafico_Demanda == 0)
                            {
                                viewRelatorio += "_Barra";
                            }
                            else
                            {
                                viewRelatorio += "_Area";
                            }

                            // verifica se aplica simulacao
                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                // verifica se aplica simulacao
                                if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                                {
                                    workbook = Dem_Ativa_Diario_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = Dem_Ativa_Diario_XLS(IDCliente, IDMedicao);
                                }
                            }

                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            Dem_Ativa_Semanal(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_Dem_Ativa_Semanal";

                            // verifica se aplica simulacao
                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                                {
                                    workbook = Dem_Ativa_Semanal_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = Dem_Ativa_Semanal_XLS(IDCliente, IDMedicao);
                                }
                            }

                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            Dem_Ativa_Mensal(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_Dem_Ativa_Mensal";

                            // verifica se aplica simulacao
                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                                {
                                    workbook = Dem_Ativa_Mensal_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = Dem_Ativa_Mensal_XLS(IDCliente, IDMedicao);
                                }
                            }
                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            Dem_Ativa_Anual(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_Dem_Ativa_Anual";

                            // verifica se aplica simulacao
                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                                {
                                    workbook = Dem_Ativa_Anual_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = Dem_Ativa_Anual_XLS(IDCliente, IDMedicao);
                                }
                            }
                            break;
                    }

                    break;

                case TIPO_RELAT.Demanda_Reativa:        // Demanda Reativa

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            Dem_Ativa_Diario(IDCliente, IDMedicao, data_hora, 1);
                            viewRelatorio = "_Dem_Reativa_Diario";

                            // verifica se gráfico em barra
                            if (IDTipoGrafico_Demanda == 0)
                            {
                                viewRelatorio += "_Barra";
                            }
                            else
                            {
                                viewRelatorio += "_Area";
                            }

                            // verifica se aplica simulacao
                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (aplicaSimulacao)
                                {
                                    workbook = Dem_Reativa_Diario_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = Dem_Reativa_Diario_XLS(IDCliente, IDMedicao);
                                }
                            }

                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            Dem_Ativa_Semanal(IDCliente, IDMedicao, data_hora, 1);
                            viewRelatorio = "_Dem_Reativa_Semanal";

                            // verifica se aplica simulacao
                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (aplicaSimulacao)
                                {
                                    workbook = Dem_Reativa_Semanal_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = Dem_Reativa_Semanal_XLS(IDCliente, IDMedicao);
                                }
                            }

                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            Dem_Ativa_Mensal(IDCliente, IDMedicao, data_hora, 1);
                            viewRelatorio = "_Dem_Reativa_Mensal";

                            // verifica se aplica simulacao
                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (aplicaSimulacao)
                                {
                                    workbook = Dem_Reativa_Mensal_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = Dem_Reativa_Mensal_XLS(IDCliente, IDMedicao);
                                }
                            }

                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            Dem_Ativa_Anual(IDCliente, IDMedicao, data_hora, 1);
                            viewRelatorio = "_Dem_Reativa_Anual";

                            // verifica se aplica simulacao
                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (aplicaSimulacao)
                                {
                                    workbook = Dem_Reativa_Anual_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = Dem_Reativa_Anual_XLS(IDCliente, IDMedicao);
                                }
                            }

                            break;
                    }

                    break;

                case TIPO_RELAT.Consumo:            // Consumo Ativo

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            Consumo_Diario(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_Consumo_Diario";

                            // verifica se aplica simulacao
                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                                {
                                    workbook = Consumo_Diario_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = Consumo_Diario_XLS(IDCliente, IDMedicao);
                                }
                            }
                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            Consumo_Semanal(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_Consumo_Semanal";

                            // verifica se aplica simulacao
                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                                {
                                    workbook = Consumo_Semanal_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = Consumo_Semanal_XLS(IDCliente, IDMedicao);
                                }
                            }


                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            Consumo_Mensal(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_Consumo_Mensal";

                            // verifica se aplica simulacao
                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                                {
                                    workbook = Consumo_Mensal_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = Consumo_Mensal_XLS(IDCliente, IDMedicao);
                                }
                            }

                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            Consumo_Anual(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_Consumo_Anual";

                            // verifica se aplica simulacao
                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                                {
                                    workbook = Consumo_Anual_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = Consumo_Anual_XLS(IDCliente, IDMedicao);
                                }
                            }

                            break;
                    }

                    break;

                case TIPO_RELAT.FatPot:             // Fator de Potencia

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            FatPot_Diario(IDCliente, IDMedicao, data_hora);

                            viewRelatorio = "_FatPot_Diario";

                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                                {
                                    workbook = FatPot_Diario_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = FatPot_Diario_XLS(IDCliente, IDMedicao);
                                }
                            }

                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            FatPot_Semanal(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_FatPot_Semanal";

                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                                {
                                    workbook = FatPot_Semanal_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = FatPot_Semanal_XLS(IDCliente, IDMedicao);
                                }
                            }

                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            FatPot_Mensal(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_FatPot_Mensal";

                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                                {
                                    workbook = FatPot_Mensal_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = FatPot_Mensal_XLS(IDCliente, IDMedicao);
                                }
                            }

                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            FatPot_Anual(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_FatPot_Anual";

                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                                {
                                    workbook = FatPot_Anual_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = FatPot_Anual_XLS(IDCliente, IDMedicao);
                                }
                            }

                            break;
                    }

                    break;

                case TIPO_RELAT.FatCarga:           // Fator de Carga

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            FatCarga_Diario(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_FatCarga_Diario";

                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (aplicaSimulacao)
                                {
                                    workbook = FatCarga_Diario_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = FatCarga_Diario_XLS(IDCliente, IDMedicao);
                                }
                            }

                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            FatCarga_Semanal(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_FatCarga_Semanal";

                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (aplicaSimulacao)
                                {
                                    workbook = FatCarga_Semanal_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = FatCarga_Semanal_XLS(IDCliente, IDMedicao);
                                }
                            }

                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            FatCarga_Mensal(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_FatCarga_Mensal";

                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (aplicaSimulacao)
                                {
                                    workbook = FatCarga_Mensal_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = FatCarga_Mensal_XLS(IDCliente, IDMedicao);
                                }
                            }

                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            FatCarga_Anual(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_FatCarga_Anual";

                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (aplicaSimulacao)
                                {
                                    workbook = FatCarga_Anual_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = FatCarga_Anual_XLS(IDCliente, IDMedicao);
                                }
                            }

                            break;
                    }

                    break;

                case TIPO_RELAT.FatUtilizacao:          // Fator de Utilizacao

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            FatUtilizacao_Diario(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_FatUtilizacao_Diario";

                            // verifica se gráfico em barra
                            if (IDTipoGrafico_Demanda == 0)
                            {
                                viewRelatorio += "_Barra";
                            }
                            else
                            {
                                viewRelatorio += "_Area";
                            }

                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (aplicaSimulacao)
                                {
                                    workbook = FatUtilizacao_Diario_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = FatUtilizacao_Diario_XLS(IDCliente, IDMedicao);
                                }
                            }

                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            FatUtilizacao_Semanal(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_FatUtilizacao_Semanal";

                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (aplicaSimulacao)
                                {
                                    workbook = FatUtilizacao_Semanal_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = FatUtilizacao_Semanal_XLS(IDCliente, IDMedicao);
                                }
                            }

                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            FatUtilizacao_Mensal(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_FatUtilizacao_Mensal";

                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (aplicaSimulacao)
                                {
                                    workbook = FatUtilizacao_Mensal_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = FatUtilizacao_Mensal_XLS(IDCliente, IDMedicao);
                                }
                            }

                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            FatUtilizacao_Anual(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_FatUtilizacao_Anual";

                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (aplicaSimulacao)
                                {
                                    workbook = FatUtilizacao_Anual_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = FatUtilizacao_Anual_XLS(IDCliente, IDMedicao);
                                }
                            }


                            break;
                    }

                    break;

                case TIPO_RELAT.UFER:               // UFER Energia Reativa Excedente

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            UFER_Diario(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_UFER_Diario";

                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (aplicaSimulacao)
                                {
                                    workbook = UFER_Diario_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = UFER_Diario_XLS(IDCliente, IDMedicao);
                                }
                            }
                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            UFER_Semanal(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_UFER_Semanal";

                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (aplicaSimulacao)
                                {
                                    workbook = UFER_Semanal_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = UFER_Semanal_XLS(IDCliente, IDMedicao);
                                }
                            }                               
                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            UFER_Mensal(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_UFER_Mensal";

                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (aplicaSimulacao)
                                {
                                    workbook = UFER_Mensal_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = UFER_Mensal_XLS(IDCliente, IDMedicao);
                                }
                            }                                
                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            UFER_Anual(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_UFER_Anual";

                            if (IDSimulacaoCenario > 0 && aplicaSimulacao)
                            {
                                viewRelatorio += "_Simulacao";
                            }

                            if (tipo_arquivo == 2)
                            {
                                if (aplicaSimulacao)
                                {
                                    workbook = UFER_Anual_XLS_Simulacao(IDCliente, IDMedicao);
                                }
                                else
                                {
                                    workbook = UFER_Anual_XLS(IDCliente, IDMedicao);
                                }
                            }                                
                            break;
                    }

                    break;

                case TIPO_RELAT.Eventos:            // Eventos
                    Eventos(IDCliente, IDGateway, IDTipoGateway, data_hora, data_hora_fim);
                    viewRelatorio = "_Eventos";

                    if (tipo_arquivo == 2)
                        workbook = Eventos_XLS(IDCliente, IDMedicao);
                    break;

                case TIPO_RELAT.Utilidades:         // Utilidades

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            Utilidades_Diario(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_Utilidades_Diario";

                            if (tipo_arquivo == 2)
                                workbook = Utilidades_Diario_XLS(IDCliente, IDMedicao);
                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            Utilidades_Semanal(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_Utilidades_Semanal";

                            if (tipo_arquivo == 2)
                                workbook = Utilidades_Semanal_XLS(IDCliente, IDMedicao);
                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            Utilidades_Mensal(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_Utilidades_Mensal";

                            if (tipo_arquivo == 2)
                                workbook = Utilidades_Mensal_XLS(IDCliente, IDMedicao);
                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            Utilidades_Anual(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_Utilidades_Anual";

                            if (tipo_arquivo == 2)
                                workbook = Utilidades_Anual_XLS(IDCliente, IDMedicao);
                            break;
                    }

                    break;

                case TIPO_RELAT.Analogica:          // Entradas Analogicas

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            EA_Diario(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_EA_Diario";

                            if (tipo_arquivo == 2)
                                workbook = EA_Diario_XLS(IDCliente, IDMedicao);
                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            EA_Semanal(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_EA_Semanal";

                            if (tipo_arquivo == 2)
                                workbook = EA_Semanal_XLS(IDCliente, IDMedicao);
                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            EA_Mensal(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_EA_Mensal";

                            if (tipo_arquivo == 2)
                                workbook = EA_Mensal_XLS(IDCliente, IDMedicao);
                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            EA_Anual(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_EA_Anual";

                            if (tipo_arquivo == 2)
                                workbook = EA_Anual_XLS(IDCliente, IDMedicao);
                            break;
                    }

                    break;

                case TIPO_RELAT.Ciclometro:             // ciclometro

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            Ciclometro_Diario(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_Ciclometro_Diario";

                            if (tipo_arquivo == 2)
                                workbook = Ciclometro_Diario_XLS(IDCliente, IDMedicao);
                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            Ciclometro_Semanal(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_Ciclometro_Semanal";

                            if (tipo_arquivo == 2)
                                workbook = Ciclometro_Semanal_XLS(IDCliente, IDMedicao);
                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            Ciclometro_Mensal(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_Ciclometro_Mensal";

                            if (tipo_arquivo == 2)
                                workbook = Ciclometro_Mensal_XLS(IDCliente, IDMedicao);
                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            Ciclometro_Anual(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_Ciclometro_Anual";

                            if (tipo_arquivo == 2)
                                workbook = Ciclometro_Anual_XLS(IDCliente, IDMedicao);
                            break;
                    }

                    break;

                case TIPO_RELAT.Meteorologia:           // meteorologia

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:
                            Meteorologia_Diario(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_Meteorologia_Diario";

                            if (tipo_arquivo == 2)
                                workbook = Meteorologia_Diario_XLS(IDCliente, IDMedicao);
                            break;

                        case TIPO_RELAT_PERIODO.Semanal:
                            Meteorologia_Semanal(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_Meteorologia_Semanal";

                            if (tipo_arquivo == 2)
                                workbook = Meteorologia_Semanal_XLS(IDCliente, IDMedicao);
                            break;

                        case TIPO_RELAT_PERIODO.Mensal:
                            Meteorologia_Mensal(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_Meteorologia_Mensal";

                            if (tipo_arquivo == 2)
                                workbook = Meteorologia_Mensal_XLS(IDCliente, IDMedicao);
                            break;


                        case TIPO_RELAT_PERIODO.Anual:
                            Meteorologia_Anual(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_Meteorologia_Anual";

                            if (tipo_arquivo == 2)
                                workbook = Meteorologia_Anual_XLS(IDCliente, IDMedicao);
                            break;
                    }

                    break;
            }


            //
            // Observacoes
            //
            if (ler_observacoes)
            {
                // data atual
                DateTime data_hora_i = ViewBag.data_hora_ini;

                if (data_hora_i != null)
                {
                    DateTime data_hora_f = new DateTime();

                    // periodo
                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            data_hora_f = data_hora_i.AddDays(1);
                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            data_hora_f = data_hora_i.AddDays(7);
                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            data_hora_f = data_hora_i.AddMonths(1);
                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            data_hora_f = data_hora_i.AddYears(1);
                            break;

                    }

                    // tipos acesso
                    ListaTiposMetodos listaMetodos = new ListaTiposMetodos();
                    List<ListaTiposDominio> listatipos = listaMetodos.ListarTodos("TipoAcesso");

                    // le tags
                    List<ObservacaoTagsDominio> tags = listaMetodos.ListarTodos_ObservacaoTags();
                    ViewBag.tags = tags;

                    // observacao
                    ObservacaoMedicaoDominio observacao = new ObservacaoMedicaoDominio();
                    ViewBag.observacao = observacao;

                    // observacoes
                    ObservacaoMedicaoMetodos observacaoMetodos = new ObservacaoMedicaoMetodos();
                    List<ObservacaoMedicaoDominio> observacoes_lidas = observacaoMetodos.ListarPorPeriodo(IDMedicao, data_hora_i, data_hora_f);
                    List<ObservacaoMedicaoDominio> observacoes = new List<ObservacaoMedicaoDominio>();

                    // le observacoes
                    if (observacoes_lidas != null)
                    {
                        // percorre observacoes
                        foreach (ObservacaoMedicaoDominio obs in observacoes_lidas)
                        {
                            bool adicionar = false;

                            // caso for admin GESTAL pode ver todas observacoes (inseridas pela GESTAL, consultores ou clientes)
                            if (isUser.isGESTAL(IDTipoAcesso))
                            {
                                // permite ele ver
                                adicionar = true;
                            }

                            // caso usuario atual for consultor pode ver apenas as de consultor e dos clientes
                            if (isUser.isConsultor(IDTipoAcesso))
                            {
                                // verifica se mensagem eh de consultor ou se de cliente habilitado para visualizar
                                if (isUser.isConsultor(obs.IDTipoAcesso) || obs.ClienteVisualiza)
                                {
                                    adicionar = true;
                                }
                            }

                            // caso usuario atual for cliente pode ver apenas se habilitado para visualizar
                            if (isUser.isCliente(IDTipoAcesso))
                            {
                                // verifica se cliente habilitado para visualizar
                                if (obs.ClienteVisualiza)
                                {
                                    adicionar = true;
                                }
                            }

                            // adiciona na lista
                            if (adicionar)
                            {
                                // adiona na lista
                                observacoes.Add(obs);
                            }
                        }
                    }

                    ViewBag.Observacoes = observacoes;
                }
            }



            // verifica se PDF
            if (tipo_arquivo == 1)
            {
                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.pdf", viewRelatorio, IDMedicao, data_atual);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Portrait,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna nome do arquivo
                var returnedData = new
                {
                    nomeArquivo = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);

                // abaixo original para fazer o download imediato do PDF sem usar o AJAX
                // utilizar a chamda na View:
                // <a href='@Url.Action("Relat_Grafico_PDF", "Relatorios")'><i class="fa fa-file-pdf-o" data-toggle="tooltip" data-placement="left" title="PDF"></i></a>
                // retorna PDF
                //return new Rotativa.PartialViewAsPdf(viewRelatorio) { 
                //    FileName = nomeArquivo,
                //    PageOrientation = Orientation.Portrait,
                //    PageSize = Size.A4,
                //}; 
            }

            // verifica se XLS
            if (tipo_arquivo == 2)
            {
                // gera identificador unico aonde o arquivo sera guardado
                string handle = Guid.NewGuid().ToString();

                using (MemoryStream memoryStream = new MemoryStream())
                {
                    workbook.Write(memoryStream);
                    memoryStream.Position = 0;
                    Session[handle] = memoryStream.ToArray();
                }

                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.xls", viewRelatorio, IDMedicao, data_atual);

                // retorna nome do arquivo
                var returnedData = new
                {
                    FileGuid = handle,
                    FileName = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);

            }

            // verifica se EMAIL
            if (tipo_arquivo == 3)
            {
                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.pdf", viewRelatorio, IDMedicao, data_atual);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Portrait,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna 
                ViewBag.caminhoCompleto = caminhoCompleto;
                var returnedData = new
                {
                    caminhoCompleto = caminhoCompleto
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            return View();
        }

        // GET: Relatorio Grafico Print
        public ActionResult Relat_Grafico_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;
            int IDEmpresa = ViewBag._IDEmpresa;
            int TipoRelat = ViewBag.Relat_Tipo;

            // nome do cliente
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            ClientesDominio cliente = clientesMetodos.ListarPorId(IDCliente);

            if (cliente != null)
            {
                ViewBag.ClienteNome = cliente.Nome;
            }
            else
            {
                ViewBag.ClienteNome = "";
            }

            // show grafico e tabela
            ViewBag.showGrafico = true;
            ViewBag.showTabela = true;

            // verifica relatorio
            if (TipoRelat == TIPO_RELAT.EventosUsuario)
            {
                // imprime
                return Relat_EventosUsuario_Show(IDCliente, 4);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoEnergia_Diario)
            {
                // imprime
                return Relat_Consolidado_Diario_Show(IDCliente, 4);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoEnergia_Mensal)
            {
                // imprime
                return Relat_Consolidado_Mensal_Show(IDCliente, 4);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoEnergia_Anual)
            {
                // imprime
                return Relat_Consolidado_Anual_Show(IDCliente, 4);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoEA_Diario)
            {
                // imprime
                return Relat_Consolidado_EA_Diario_Show(IDCliente, 4);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoEA_Mensal)
            {
                // imprime
                return Relat_Consolidado_EA_Mensal_Show(IDCliente, 4);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoEA_Anual)
            {
                // imprime
                return Relat_Consolidado_EA_Anual_Show(IDCliente, 4);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoUtilidades_Diario)
            {
                // imprime
                return Relat_Consolidado_Utilidades_Diario_Show(IDCliente, 4);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoUtilidades_Mensal)
            {
                // imprime
                return Relat_Consolidado_Utilidades_Mensal_Show(IDCliente, 4);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoUtilidades_Anual)
            {
                // imprime
                return Relat_Consolidado_Utilidades_Anual_Show(IDCliente, 4);
            }

            if (TipoRelat == TIPO_RELAT.TempoAtuacao_EntradasDigitais_Mensal)
            {
                // imprime
                return TempoAtuacao_EntradasDigitais_Mensal_Show(IDCliente, 4);
            }

            if (TipoRelat == TIPO_RELAT.TempoAtuacao_SaidasDigitais_Mensal)
            {
                // imprime
                return TempoAtuacao_SaidasDigitais_Mensal_Show(IDCliente, 4);
            }

            if (TipoRelat == TIPO_RELAT.Provisionamento_Semanal_Tipo1_Tipo2 || TipoRelat == TIPO_RELAT.Provisionamento_Semanal_Tipo3)
            {
                // imprime
                return Relat_Provisionamento_Semanal_Show(IDEmpresa, 4);
            }

            // imprime
            return Relat_Grafico_Show(IDCliente, IDMedicao, TipoRelat, 4);
        }

        public static async Task<string> EMailTemplate(string template)
        {
            var templateFilePath = HostingEnvironment.MapPath("~/Content/emailTemplates/") + template + ".cshtml";
            StreamReader objstreamreaderfile = new StreamReader(templateFilePath);
            var body = await objstreamreaderfile.ReadToEndAsync();
            objstreamreaderfile.Close();
            return body;
        }

        // GET: Relatorio Grafico EMAIL
        public async Task<ActionResult> Relat_Grafico_EMAIL(string destino, string assunto, bool showGrafico, bool showTabela)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;
            int IDEmpresa = ViewBag._IDEmpresa;
            int TipoRelat = ViewBag.Relat_Tipo;

            // nome do cliente
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            ClientesDominio cliente = clientesMetodos.ListarPorId(IDCliente);

            if (cliente != null)
            {
                ViewBag.ClienteNome = cliente.Nome;
            }
            else
            {
                ViewBag.ClienteNome = "";
            }

            // show grafico e tabela
            ViewBag.showGrafico = showGrafico;
            ViewBag.showTabela = showTabela;

            // verifica relatorio
            if (TipoRelat == TIPO_RELAT.EventosUsuario)
            {
                // relatorio para PDF Email
                var retorno = Relat_EventosUsuario_Show(IDCliente, 3);
            }
            else if (TipoRelat == TIPO_RELAT.ConsolidadoEnergia_Diario)
            {
                // relatorio para PDF Email
                var retorno = Relat_Consolidado_Diario_Show(IDCliente, 3);
            }
            else if (TipoRelat == TIPO_RELAT.ConsolidadoEnergia_Mensal)
            {
                // relatorio para PDF Email
                var retorno = Relat_Consolidado_Mensal_Show(IDCliente, 3);
            }
            else if (TipoRelat == TIPO_RELAT.ConsolidadoEnergia_Anual)
            {
                // relatorio para PDF Email
                var retorno = Relat_Consolidado_Anual_Show(IDCliente, 3);
            }
            else if (TipoRelat == TIPO_RELAT.ConsolidadoEA_Diario)
            {
                // relatorio para PDF Email
                var retorno = Relat_Consolidado_EA_Diario_Show(IDCliente, 3);
            }
            else if (TipoRelat == TIPO_RELAT.ConsolidadoEA_Mensal)
            {
                // relatorio para PDF Email
                var retorno = Relat_Consolidado_EA_Mensal_Show(IDCliente, 3);
            }
            else if (TipoRelat == TIPO_RELAT.ConsolidadoEA_Anual)
            {
                // relatorio para PDF Email
                var retorno = Relat_Consolidado_EA_Anual_Show(IDCliente, 3);
            }
            else if (TipoRelat == TIPO_RELAT.ConsolidadoUtilidades_Diario)
            {
                // relatorio para PDF Email
                var retorno = Relat_Consolidado_Utilidades_Diario_Show(IDCliente, 3);
            }
            else if (TipoRelat == TIPO_RELAT.ConsolidadoUtilidades_Mensal)
            {
                // relatorio para PDF Email
                var retorno = Relat_Consolidado_Utilidades_Mensal_Show(IDCliente, 3);
            }
            else if (TipoRelat == TIPO_RELAT.ConsolidadoUtilidades_Anual)
            {
                // relatorio para PDF Email
                var retorno = Relat_Consolidado_Utilidades_Anual_Show(IDCliente, 3);
            }
            else if (TipoRelat == TIPO_RELAT.TempoAtuacao_EntradasDigitais_Mensal)
            {
                // relatorio para PDF Email
                var retorno = TempoAtuacao_EntradasDigitais_Mensal_Show(IDCliente, 3);
            }
            else if (TipoRelat == TIPO_RELAT.TempoAtuacao_SaidasDigitais_Mensal)
            {
                // relatorio para PDF Email
                var retorno = TempoAtuacao_SaidasDigitais_Mensal_Show(IDCliente, 3);
            }
            else if (TipoRelat == TIPO_RELAT.Provisionamento_Semanal_Tipo1_Tipo2 || TipoRelat == TIPO_RELAT.Provisionamento_Semanal_Tipo3)
            {
                // relatorio para PDF Email
                var retorno = Relat_Provisionamento_Semanal_Show(IDEmpresa, 3);
            }
            else
            {
                // relatorio para PDF Email
                var retorno = Relat_Grafico_Show(IDCliente, IDMedicao, TipoRelat, 3);
            }

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(ViewBag.caminhoCompleto);

            // verifica relatorio
            if (TipoRelat == TIPO_RELAT.EventosUsuario)
            {
                // envia EMAIL
                var emailTemplate = "EventosUsuarioEmail";
                var message = await EMailTemplate(emailTemplate);
                message = message.Replace("ViewBag.NomeRelat", ViewBag.NomeRelat);
                message = message.Replace("ViewBag.DataTextoAtualIni", ViewBag.DataTextoAtualIni);
                message = message.Replace("ViewBag.DataTextoAtualFim", ViewBag.DataTextoAtualFim);
                message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
                await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);
            }
            else if (TipoRelat == TIPO_RELAT.ConsolidadoEnergia_Diario ||
                     TipoRelat == TIPO_RELAT.ConsolidadoEnergia_Mensal ||
                     TipoRelat == TIPO_RELAT.ConsolidadoEnergia_Anual ||
                     TipoRelat == TIPO_RELAT.ConsolidadoEA_Diario ||
                     TipoRelat == TIPO_RELAT.ConsolidadoEA_Mensal ||
                     TipoRelat == TIPO_RELAT.ConsolidadoEA_Anual ||
                     TipoRelat == TIPO_RELAT.ConsolidadoUtilidades_Diario ||
                     TipoRelat == TIPO_RELAT.ConsolidadoUtilidades_Mensal ||
                     TipoRelat == TIPO_RELAT.ConsolidadoUtilidades_Anual ||
                     TipoRelat == TIPO_RELAT.TempoAtuacao_EntradasDigitais_Mensal ||
                     TipoRelat == TIPO_RELAT.TempoAtuacao_SaidasDigitais_Mensal)
            {
                // envia EMAIL
                var emailTemplate = "ConsolidadoEmail";
                var message = await EMailTemplate(emailTemplate);
                message = message.Replace("ViewBag.NomeRelat", ViewBag.NomeRelat);
                message = message.Replace("ViewBag.PeriodoRelat", ViewBag.PeriodoRelat);
                message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
                message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
                await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);
            }
            else
            {
                // envia EMAIL
                var emailTemplate = "RelatEmail";
                var message = await EMailTemplate(emailTemplate);
                message = message.Replace("ViewBag.NomeRelat", ViewBag.NomeRelat);
                message = message.Replace("ViewBag.PeriodoRelat", ViewBag.PeriodoRelat);
                message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
                message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
                message = message.Replace("ViewBag.GrupoNome", ViewBag.GrupoNome);
                message = message.Replace("ViewBag.UnidadeNome", ViewBag.UnidadeNome);
                message = message.Replace("ViewBag.MedicaoNome", ViewBag.MedicaoNome);
                await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);
            }

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Relatorio Grafico PDF
        public ActionResult Relat_Grafico_PDF(bool showGrafico, bool showTabela)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;
            int IDEmpresa = ViewBag._IDEmpresa;
            int TipoRelat = ViewBag.Relat_Tipo;

            // nome do cliente
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            ClientesDominio cliente = clientesMetodos.ListarPorId(IDCliente);

            if (cliente != null)
            {
                ViewBag.ClienteNome = cliente.Nome;
            }
            else
            {
                ViewBag.ClienteNome = "";
            }

            // show grafico e tabela
            ViewBag.showGrafico = showGrafico;
            ViewBag.showTabela = showTabela;

            // verifica relatorio
            if (TipoRelat == TIPO_RELAT.EventosUsuario)
            {
                // imprime
                return Relat_EventosUsuario_Show(IDCliente, 1);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoEnergia_Diario)
            {
                // relatorio para PDF
                return Relat_Consolidado_Diario_Show(IDCliente, 1);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoEnergia_Mensal)
            {
                // relatorio para PDF
                return Relat_Consolidado_Mensal_Show(IDCliente, 1);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoEnergia_Anual)
            {
                // relatorio para PDF
                return Relat_Consolidado_Anual_Show(IDCliente, 1);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoEA_Diario)
            {
                // relatorio para PDF
                return Relat_Consolidado_EA_Diario_Show(IDCliente, 1);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoEA_Mensal)
            {
                // relatorio para PDF
                return Relat_Consolidado_EA_Mensal_Show(IDCliente, 1);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoEA_Anual)
            {
                // relatorio para PDF
                return Relat_Consolidado_EA_Anual_Show(IDCliente, 1);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoUtilidades_Diario)
            {
                // relatorio para PDF
                return Relat_Consolidado_Utilidades_Diario_Show(IDCliente, 1);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoUtilidades_Mensal)
            {
                // relatorio para PDF
                return Relat_Consolidado_Utilidades_Mensal_Show(IDCliente, 1);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoUtilidades_Anual)
            {
                // relatorio para PDF
                return Relat_Consolidado_Utilidades_Anual_Show(IDCliente, 1);
            }

            if (TipoRelat == TIPO_RELAT.TempoAtuacao_EntradasDigitais_Mensal)
            {
                // relatorio para PDF
                return TempoAtuacao_EntradasDigitais_Mensal_Show(IDCliente, 1);
            }

            if (TipoRelat == TIPO_RELAT.TempoAtuacao_SaidasDigitais_Mensal)
            {
                // relatorio para PDF
                return TempoAtuacao_SaidasDigitais_Mensal_Show(IDCliente, 1);
            }

            if (TipoRelat == TIPO_RELAT.Provisionamento_Semanal_Tipo1_Tipo2 || TipoRelat == TIPO_RELAT.Provisionamento_Semanal_Tipo3)
            {
                // relatorio para PDF
                return Relat_Provisionamento_Semanal_Show(IDEmpresa, 1);
            }

            // relatorio para PDF
            return Relat_Grafico_Show(IDCliente, IDMedicao, TipoRelat, 1);
        }

        // GET: Relatorio Grafico XLS
        public ActionResult Relat_Grafico_XLS()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;
            int IDEmpresa = ViewBag._IDEmpresa;
            int TipoRelat = ViewBag.Relat_Tipo;

            // nome do cliente
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            ClientesDominio cliente = clientesMetodos.ListarPorId(IDCliente);

            if (cliente != null)
            {
                ViewBag.ClienteNome = cliente.Nome;
            }
            else
            {
                ViewBag.ClienteNome = "";
            }

            // verifica relatorio
            if (TipoRelat == TIPO_RELAT.EventosUsuario)
            {
                // relatorio para XLS
                return Relat_EventosUsuario_Show(IDCliente, 2);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoEnergia_Diario)
            {
                // relatorio para XLS
                return Relat_Consolidado_Diario_Show(IDCliente, 2);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoEnergia_Mensal)
            {
                // relatorio para XLS
                return Relat_Consolidado_Mensal_Show(IDCliente, 2);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoEnergia_Anual)
            {
                // relatorio para XLS
                return Relat_Consolidado_Anual_Show(IDCliente, 2);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoEA_Diario)
            {
                // relatorio para XLS
                return Relat_Consolidado_EA_Diario_Show(IDCliente, 2);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoEA_Mensal)
            {
                // relatorio para XLS
                return Relat_Consolidado_EA_Mensal_Show(IDCliente, 2);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoEA_Anual)
            {
                // relatorio para XLS
                return Relat_Consolidado_EA_Anual_Show(IDCliente, 2);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoUtilidades_Diario)
            {
                // relatorio para XLS
                return Relat_Consolidado_Utilidades_Diario_Show(IDCliente, 2);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoUtilidades_Mensal)
            {
                // relatorio para XLS
                return Relat_Consolidado_Utilidades_Mensal_Show(IDCliente, 2);
            }

            if (TipoRelat == TIPO_RELAT.ConsolidadoUtilidades_Anual)
            {
                // relatorio para XLS
                return Relat_Consolidado_Utilidades_Anual_Show(IDCliente, 2);
            }

            if (TipoRelat == TIPO_RELAT.TempoAtuacao_EntradasDigitais_Mensal)
            {
                // relatorio para XLS
                return TempoAtuacao_EntradasDigitais_Mensal_Show(IDCliente, 2);
            }

            if (TipoRelat == TIPO_RELAT.TempoAtuacao_SaidasDigitais_Mensal)
            {
                // relatorio para XLS
                return TempoAtuacao_SaidasDigitais_Mensal_Show(IDCliente, 2);
            }

            // relatorio para XLS
            return Relat_Grafico_Show(IDCliente, IDMedicao, TipoRelat, 2);
        }

        // GET: Relatorio Grafico exportar XLS
        public ActionResult Relat_Grafico_exportarXLS(string DataIni, string DataFim)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;
            int TipoRelat = ViewBag.Relat_Tipo;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // pega data
            DateTime dateValueIni = DateTime.Parse(DataIni);
            DateTime dateValueFim = DateTime.Parse(DataFim);

            // verifica numero de registros
            double num_registros = (dateValueFim - dateValueIni).TotalDays * 96.0;

            if (TipoRelat == TIPO_RELAT.Consumo || TipoRelat == TIPO_RELAT.Utilidades || TipoRelat == TIPO_RELAT.Ciclometro)
            {
                num_registros = (dateValueFim - dateValueIni).TotalDays * 24.0;
            }

            if (num_registros > 65000.0)
            {
                // relatorio para PDF
                return Json("MaxReg", JsonRequestBehavior.AllowGet);
            }

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            // nome do arquivo
            string nomeArquivo = string.Format("Relat_{0:000000}_{1:yyyyMMddHHmm}_{2:yyyyMMddHHmm}.xls", IDMedicao, dateValueIni, dateValueFim);

            // tipo do relatorio
            switch (TipoRelat)
            {
                default:
                case TIPO_RELAT.Demanda_Ativa:      // demanda ativa
                case TIPO_RELAT.Demanda_Reativa:    // demanda reativa
                    workbook = Dem_exportar_XLS(IDCliente, IDMedicao, dateValueIni, dateValueFim);

                    nomeArquivo = string.Format("Demanda_{0:000000}_{1:yyyyMMddHHmm}_{2:yyyyMMddHHmm}.xls", IDMedicao, dateValueIni, dateValueFim);

                    break;

                case TIPO_RELAT.Consumo:            // consumo
                case TIPO_RELAT.FatPot:             // fator de potencia
                    workbook = Cons_exportar_XLS(IDCliente, IDMedicao, dateValueIni, dateValueFim);

                    nomeArquivo = string.Format("Consumo_{0:000000}_{1:yyyyMMddHHmm}_{2:yyyyMMddHHmm}.xls", IDMedicao, dateValueIni, dateValueFim);

                    break;

                case TIPO_RELAT.Utilidades:         // utilidades
                    workbook = Util_exportar_XLS(IDCliente, IDMedicao, dateValueIni, dateValueFim);

                    nomeArquivo = string.Format("Util_{0:000000}_{1:yyyyMMddHHmm}_{2:yyyyMMddHHmm}.xls", IDMedicao, dateValueIni, dateValueFim);

                    break;

                case TIPO_RELAT.Analogica:          // analogica
                    workbook = EA_exportar_XLS(IDCliente, IDMedicao, dateValueIni, dateValueFim);

                    nomeArquivo = string.Format("EA_{0:000000}_{1:yyyyMMddHHmm}_{2:yyyyMMddHHmm}.xls", IDMedicao, dateValueIni, dateValueFim);

                    break;

                case TIPO_RELAT.Ciclometro:         // ciclometro
                    workbook = Ciclometro_exportar_XLS(IDCliente, IDMedicao, dateValueIni, dateValueFim);

                    nomeArquivo = string.Format("Ciclo_{0:000000}_{1:yyyyMMddHHmm}_{2:yyyyMMddHHmm}.xls", IDMedicao, dateValueIni, dateValueFim);

                    break;

                case TIPO_RELAT.Meteorologia:       // meteorologia
                    workbook = Meteorologia_exportar_XLS(IDCliente, IDMedicao, dateValueIni, dateValueFim);

                    nomeArquivo = string.Format("Meteorologia_{0:000000}_{1:yyyyMMddHHmm}_{2:yyyyMMddHHmm}.xls", IDMedicao, dateValueIni, dateValueFim);

                    break;
            }

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            // relatorio para PDF
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Relatorio Grafico XLS Download
        [HttpGet]
        public virtual ActionResult Relat_Grafico_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Relatorio Atualizar
        public PartialViewResult _Relat_Atualizar(int TipoRelat, int Navegacao, string Data, string DataFim = null, string TurnoIni = null, string TurnoFim = null)
        {
            // caso forcar data
            if (Navegacao == 100)
            {
                // pega data
                DateTime dateValue = DateTime.Parse(Data);
                DateTime dateValueFim = new DateTime();

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Relat_Data", dateValue);

                // verifica se eventos
                if ((TipoRelat == TIPO_RELAT.Eventos || TipoRelat == TIPO_RELAT.EventosUsuario) && DataFim != null)
                {
                    // pega data
                    dateValueFim = DateTime.Parse(DataFim);
                }
                // verifica se consolidado mensal
                else if (TipoRelat == TIPO_RELAT.ConsolidadoEnergia_Mensal && TurnoIni != null && TurnoFim != null)
                {
                    // quando estiver em outros relatorios, faco sempre o periodo de 1 dia
                    dateValueFim = dateValue.AddDays(1);

                    // pega turno
                    DateTime turnoIni = DateTime.Parse(TurnoIni);
                    DateTime turnoFim = DateTime.Parse(TurnoFim);

                    // salva cookie turno
                    CookieStore.SalvaCookie_Datahora("Consolidado_TurnoIni", turnoIni);
                    CookieStore.SalvaCookie_Datahora("Consolidado_TurnoFim", turnoFim);
                }
                else
                {
                    // quando estiver em outros relatorios, faco sempre o periodo de 1 dia
                    dateValueFim = dateValue.AddDays(1);
                }

                // salva cookie datahora (final)
                CookieStore.SalvaCookie_Datahora("Relat_DataFim", dateValueFim);
            }

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;
            int IDGateway = ViewBag._IDGateway;
            int IDEmpresa = ViewBag._IDEmpresa;
            int IDTipoGateway = ViewBag._IDTipoGateway;
            int TipoPeriodo = ViewBag.Relat_TipoPeriodo;

            // tipo de acesso do usuario
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // unidade de potencia
            UnidadePotencia(IDMedicao);

            // le cookie datahora
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");
            DATAHORA data_hora = new DATAHORA();

            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Relat_DataFim");
            DATAHORA data_hora_fim = new DATAHORA();

            // verifica se fim eh menor que inicio
            if (datahora_cookie_fim < datahora_cookie)
            {
                // periodo de pelo menos 1 dia
                datahora_cookie_fim = datahora_cookie.AddDays(1);
            }

            // data e hora atual
            ViewBag.DataIniExportar = string.Format("{0:d}", datahora_cookie);
            ViewBag.HoraIniExportar = "00:15:00";
            ViewBag.DataFimExportar = string.Format("{0:d}", datahora_cookie_fim);
            ViewBag.HoraFimExportar = "00:00:00";

            if (TipoRelat == TIPO_RELAT.Consumo || TipoRelat == TIPO_RELAT.Utilidades || TipoRelat == TIPO_RELAT.Ciclometro)
            {
                ViewBag.HoraIniExportar = "01:00:00";

            }
            else if (TipoRelat == TIPO_RELAT.Analogica)
            {
                ViewBag.HoraIniExportar = "00:00:00";
            }

            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -1:    // dia anterior

                    datahora_cookie = datahora_cookie.AddDays(-1);
                    datahora_cookie_fim = datahora_cookie_fim.AddDays(-1);
                    break;

                case 1:    // dia seguinte

                    datahora_cookie = datahora_cookie.AddDays(1);
                    datahora_cookie_fim = datahora_cookie_fim.AddDays(1);
                    break;

                case -2:    // semana anterior

                    datahora_cookie = datahora_cookie.AddDays(-7);
                    datahora_cookie_fim = datahora_cookie_fim.AddDays(-7);
                    break;

                case 2:    // semana seguinte

                    datahora_cookie = datahora_cookie.AddDays(7);
                    datahora_cookie_fim = datahora_cookie_fim.AddDays(7);
                    break;

                case -3:    // mes anterior

                    datahora_cookie = datahora_cookie.AddMonths(-1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(-1);
                    break;

                case 3:    // mes seguinte

                    datahora_cookie = datahora_cookie.AddMonths(1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(1);
                    break;

                case -4:    // ano anterior

                    datahora_cookie = datahora_cookie.AddYears(-1);
                    datahora_cookie_fim = datahora_cookie_fim.AddYears(-1);
                    break;

                case 4:    // ano seguinte

                    datahora_cookie = datahora_cookie.AddYears(1);
                    datahora_cookie_fim = datahora_cookie_fim.AddYears(1);
                    break;

                case 10:    // ultimo

                    // verifica se evento usuario
                    if (TipoRelat == TIPO_RELAT.EventosUsuario)
                    {
                        // data atual
                        DateTime ts = DateTime.Now;
                        datahora_cookie = new DateTime(ts.Year, ts.Month, ts.Day, 0, 0, 0);

                        // periodo de pelo menos 1 dia
                        datahora_cookie_fim = datahora_cookie.AddDays(1);
                    }
                    else
                    {
                        // le supervisao da medicao
                        var medicoesMetodos = new SupervMedicoesMetodos();
                        var listaMedicoes = medicoesMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

                        // verifica se evento
                        if (TipoRelat == TIPO_RELAT.Eventos)
                        {
                            // dia atual
                            DateTime atual = DateTime.Parse(listaMedicoes.DataHoraAtualizacao);
                            datahora_cookie = new DateTime(atual.Year, atual.Month, atual.Day, 0, 0, 0);

                            // periodo de 1 dia
                            datahora_cookie_fim = datahora_cookie.AddDays(1);
                        }
                        else
                        {
                            // dia atual
                            datahora_cookie = DateTime.Parse(listaMedicoes.DataHora);

                            // periodo de 1 dia
                            datahora_cookie_fim = datahora_cookie.AddDays(1);
                        }
                    }

                    break;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_cookie);

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_cookie);

            // salva cookie datahora (final)
            CookieStore.SalvaCookie_Datahora("Relat_DataFim", datahora_cookie_fim);

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora_fim, datahora_cookie_fim);

            // tipo do relatorio
            ViewBag.TipoRelat = TipoRelat;

            // ler observacoes
            bool ler_observacoes = false;

            switch (TipoRelat)
            {
                case TIPO_RELAT.Demanda_Ativa:      // Demanda Ativa

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            Dem_Ativa_Diario(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            Dem_Ativa_Semanal(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            Dem_Ativa_Mensal(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            Dem_Ativa_Anual(IDCliente, IDMedicao, data_hora);
                            break;
                    }

                    break;

                case TIPO_RELAT.Demanda_Reativa:    // Demanda Reativa

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            Dem_Ativa_Diario(IDCliente, IDMedicao, data_hora, 1);
                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            Dem_Ativa_Semanal(IDCliente, IDMedicao, data_hora, 1);
                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            Dem_Ativa_Mensal(IDCliente, IDMedicao, data_hora, 1);
                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            Dem_Ativa_Anual(IDCliente, IDMedicao, data_hora, 1);
                            break;
                    }

                    break;

                case TIPO_RELAT.Consumo:            // Consumo Ativo  

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            Consumo_Diario(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            Consumo_Semanal(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            Consumo_Mensal(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            Consumo_Anual(IDCliente, IDMedicao, data_hora);
                            break;
                    }

                    break;

                case TIPO_RELAT.FatPot:         // Fator de Potencia

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            FatPot_Diario(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            FatPot_Semanal(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            FatPot_Mensal(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            FatPot_Anual(IDCliente, IDMedicao, data_hora);
                            break;
                    }

                    break;

                case TIPO_RELAT.FatCarga:       // Fator de Carga

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            FatCarga_Diario(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            FatCarga_Semanal(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            FatCarga_Mensal(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            FatCarga_Anual(IDCliente, IDMedicao, data_hora);
                            break;
                    }

                    break;

                case TIPO_RELAT.FatUtilizacao:  // Fator de Utilizacao

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            FatUtilizacao_Diario(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            FatUtilizacao_Semanal(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            FatUtilizacao_Mensal(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            FatUtilizacao_Anual(IDCliente, IDMedicao, data_hora);
                            break;
                    }

                    break;

                case TIPO_RELAT.UFER:               // UFER Energia Reativa Excedente

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            UFER_Diario(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            UFER_Semanal(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            UFER_Mensal(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            UFER_Anual(IDCliente, IDMedicao, data_hora);
                            break;
                    }

                    break;

                case TIPO_RELAT.Eventos:            // Eventos
                    Eventos(IDCliente, IDGateway, IDTipoGateway, data_hora, data_hora_fim);
                    break;

                case TIPO_RELAT.EventosUsuario:     // Eventos Usuario

                    // IDConsultor
                    int IDConsultor = ViewBag._IDConsultor;

                    // le eventos
                    EventosUsuario(IDCliente, data_hora, data_hora_fim, IDTipoAcesso, IDConsultor);
                    break;

                case TIPO_RELAT.Utilidades:         // Utilidades

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            Utilidades_Diario(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            Utilidades_Semanal(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            Utilidades_Mensal(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            Utilidades_Anual(IDCliente, IDMedicao, data_hora);
                            break;
                    }

                    break;

                case TIPO_RELAT.Analogica:      // Entradas Analogicas

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            EA_Diario(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            EA_Semanal(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            EA_Mensal(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            EA_Anual(IDCliente, IDMedicao, data_hora);
                            break;
                    }

                    break;

                case TIPO_RELAT.Ciclometro:             // ciclometro

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            Ciclometro_Diario(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            Ciclometro_Semanal(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            Ciclometro_Mensal(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            Ciclometro_Anual(IDCliente, IDMedicao, data_hora);
                            break;
                    }

                    break;

                case TIPO_RELAT.Meteorologia:             // meteorologia

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            Meteorologia_Diario(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Semanal:     // Semanal
                            Meteorologia_Semanal(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            Meteorologia_Mensal(IDCliente, IDMedicao, data_hora);
                            break;

                        case TIPO_RELAT_PERIODO.Anual:     // Anual
                            Meteorologia_Anual(IDCliente, IDMedicao, data_hora);
                            break;
                    }

                    break;

                case TIPO_RELAT.ConsolidadoEnergia_Diario:      // Consolidado diario energia
                    Calc_Consolidado_Diario(IDCliente, data_hora);
                    break;

                case TIPO_RELAT.ConsolidadoEnergia_Mensal:      // Consolidado mensal energia

                    // le cookie turno
                    DateTime turnoIni_cookie = CookieStore.LeCookie_Datahora("Consolidado_TurnoIni");
                    DATAHORA turno_ini = new DATAHORA();

                    DateTime turnoFim_cookie = CookieStore.LeCookie_Datahora("Consolidado_TurnoFim");
                    DATAHORA turno_fim = new DATAHORA();

                    // verifica se tem turno
                    if (turnoIni_cookie.Hour == 0 && turnoIni_cookie.Minute == 0 && turnoFim_cookie.Hour == 0 && turnoFim_cookie.Minute == 0)
                    {
                        turnoIni_cookie = new DateTime(2000, 1, 1, 0, 0, 0);
                        turnoFim_cookie = new DateTime(2000, 1, 1, 6, 0, 0);

                        // salva cookie
                        CookieStore.SalvaCookie_Datahora("Consolidado_TurnoIni", turnoIni_cookie);
                        CookieStore.SalvaCookie_Datahora("Consolidado_TurnoFim", turnoFim_cookie);
                    }

                    // converte
                    Funcoes_Converte.ConverteDateTime2DataHora(ref turno_ini, turnoIni_cookie);
                    Funcoes_Converte.ConverteDateTime2DataHora(ref turno_fim, turnoFim_cookie);

                    // calcula
                    Calc_Consolidado_Mensal(IDCliente, data_hora, turno_ini, turno_fim);
                    break;

                case TIPO_RELAT.ConsolidadoEnergia_Anual:       // Consolidado anual energia
                    Calc_Consolidado_Anual(IDCliente, data_hora);
                    break;

                case TIPO_RELAT.ConsolidadoEA_Diario:    // Consolidado diario analogicas
                    Calc_Consolidado_EA_Diario(IDCliente, data_hora);
                    break;

                case TIPO_RELAT.ConsolidadoEA_Mensal:    // Consolidado mensal analogicas
                    Calc_Consolidado_EA_Mensal(IDCliente, data_hora);
                    break;

                case TIPO_RELAT.ConsolidadoEA_Anual:    // Consolidado anual analogicas
                    Calc_Consolidado_EA_Anual(IDCliente, data_hora);
                    break;

                case TIPO_RELAT.ConsolidadoUtilidades_Diario:    // Consolidado diario utilidades
                    Calc_Consolidado_Utilidades_Diario(IDCliente, data_hora);
                    break;

                case TIPO_RELAT.ConsolidadoUtilidades_Mensal:    // Consolidado mensal utilidades
                    Calc_Consolidado_Utilidades_Mensal(IDCliente, data_hora);
                    break;

                case TIPO_RELAT.ConsolidadoUtilidades_Anual:    // Consolidado anual utilidades
                    Calc_Consolidado_Utilidades_Anual(IDCliente, data_hora);
                    break;

                case TIPO_RELAT.TempoAtuacao_EntradasDigitais_Mensal:    // Tempo de Atuação mensal - entradas digitais 
                    Calc_TempoAtuacao_EntradasDigitais_Mensal(IDCliente, data_hora);
                    break;

                case TIPO_RELAT.TempoAtuacao_SaidasDigitais_Mensal:    // Tempo de Atuação mensal - saidas digitais 
                    Calc_TempoAtuacao_SaidasDigitais_Mensal(IDCliente, data_hora);
                    break;

                case TIPO_RELAT.Provisionamento_Semanal_Tipo1_Tipo2:    // Provisionamento Semanal Tipo 1 ou Tipo 2
                case TIPO_RELAT.Provisionamento_Semanal_Tipo3:          // Provisionamento Semanal Tipo 3
                    Calc_Provisionamento_Semanal(IDEmpresa, datahora_cookie);
                    break;
            }


            //
            // Observacoes
            //
            if (ler_observacoes)
            {
                // data atual
                DateTime data_hora_i = ViewBag.data_hora_ini;

                if (data_hora_i != null)
                {
                    DateTime data_hora_f = new DateTime();

                    // periodo
                    switch (TipoPeriodo)
                    {
                        case TIPO_RELAT_PERIODO.Diario:     // Diario
                            data_hora_f = data_hora_i.AddDays(1);
                            break;

                        case TIPO_RELAT_PERIODO.Semanal:    // Semanal
                            data_hora_f = data_hora_i.AddDays(7);
                            break;

                        case TIPO_RELAT_PERIODO.Mensal:     // Mensal
                            data_hora_f = data_hora_i.AddMonths(1);
                            break;

                        case TIPO_RELAT_PERIODO.Anual:      // Anual
                            data_hora_f = data_hora_i.AddYears(1);
                            break;
                    }

                    // tipos acesso
                    ListaTiposMetodos listaMetodos = new ListaTiposMetodos();
                    List<ListaTiposDominio> listatipos = listaMetodos.ListarTodos("TipoAcesso");

                    // le tags
                    List<ObservacaoTagsDominio> tags = listaMetodos.ListarTodos_ObservacaoTags();
                    ViewBag.tags = tags;

                    // observacao
                    ObservacaoMedicaoDominio observacao = new ObservacaoMedicaoDominio();
                    ViewBag.observacao = observacao;

                    // observacoes
                    ObservacaoMedicaoMetodos observacaoMetodos = new ObservacaoMedicaoMetodos();
                    List<ObservacaoMedicaoDominio> observacoes_lidas = observacaoMetodos.ListarPorPeriodo(IDMedicao, data_hora_i, data_hora_f);
                    List<ObservacaoMedicaoDominio> observacoes = new List<ObservacaoMedicaoDominio>();

                    // le observacoes
                    if (observacoes_lidas != null)
                    {
                        // percorre observacoes
                        foreach (ObservacaoMedicaoDominio obs in observacoes_lidas)
                        {
                            bool adicionar = false;

                            // caso for admin GESTAL pode ver todas observacoes (inseridas pela GESTAL, consultores ou clientes)
                            if (isUser.isGESTAL(IDTipoAcesso))
                            {
                                // permite ele ver
                                adicionar = true;
                            }

                            // caso usuario atual for consultor pode ver apenas as de consultor e dos clientes
                            if (isUser.isConsultor(IDTipoAcesso))
                            {
                                // verifica se mensagem eh de consultor ou se de cliente habilitado para visualizar
                                if (isUser.isConsultor(obs.IDTipoAcesso) || obs.ClienteVisualiza)
                                {
                                    adicionar = true;
                                }
                            }

                            // caso usuario atual for cliente pode ver apenas se habilitado para visualizar
                            if (isUser.isCliente(IDTipoAcesso))
                            {
                                // verifica se cliente habilitado para visualizar
                                if (obs.ClienteVisualiza)
                                {
                                    adicionar = true;
                                }
                            }

                            // adiciona na lista
                            if (adicionar)
                            {
                                // adiona na lista
                                observacoes.Add(obs);
                            }
                        }
                    }

                    ViewBag.Observacoes = observacoes;
                }
            }


            // tem periodo
            bool TemPeriodo = true;

            // tela de ajuda - relatorios graficos
            string PaginaAjuda = "Relatorio_";

            switch (TipoRelat)
            {
                case TIPO_RELAT.Demanda_Ativa:          // demanda ativa
                    PaginaAjuda += "DemAtiva_";
                    break;

                case TIPO_RELAT.Demanda_Reativa:        // demanda reativa
                    PaginaAjuda += "DemReativa_";
                    break;

                case TIPO_RELAT.Consumo:                // consumo
                    PaginaAjuda += "Consumo_";
                    break;

                case TIPO_RELAT.FatPot:                 // fator de potencia
                    PaginaAjuda += "FatPot_";
                    break;

                case TIPO_RELAT.FatCarga:               // fator de carga
                    PaginaAjuda += "FatCarga_";
                    break;

                case TIPO_RELAT.FatUtilizacao:          // fator de utilizacao
                    PaginaAjuda += "FatUtilizacao_";
                    break;

                case TIPO_RELAT.UFER:                   // UFER Energia Reativa Excedente
                    PaginaAjuda += "UFER_";
                    break;

                case TIPO_RELAT.Eventos:                // eventos
                    PaginaAjuda += "EventosGateway";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.EventosUsuario:         // eventos de usuarios
                    PaginaAjuda += "Contato";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.Utilidades:             // utilidades
                    PaginaAjuda += "Utilidades_";
                    break;

                case TIPO_RELAT.Analogica:              // analogica
                    PaginaAjuda += "Analogicas_";
                    break;

                case TIPO_RELAT.Ciclometro:             // ciclometro
                    PaginaAjuda += "Ciclometro_";
                    break;

                case TIPO_RELAT.ConsolidadoEnergia_Diario:      // consolidado diario energia
                    PaginaAjuda += "Consolidado_Energia_Diario";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.ConsolidadoEnergia_Mensal:      // consolidado mensal energia
                    PaginaAjuda += "Consolidado_Energia_Mensal";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.ConsolidadoEnergia_Anual:       // consolidado anual energia
                    PaginaAjuda += "Consolidado_Energia_Anual";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.ConsolidadoEA_Diario:    // consolidado diario analogicas
                    PaginaAjuda += "Consolidado_EA_Diario";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.ConsolidadoEA_Mensal:    // consolidado mensal analogicas
                    PaginaAjuda += "Consolidado_EA_Mensal";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.ConsolidadoEA_Anual:    // consolidado anual analogicas
                    PaginaAjuda += "Consolidado_EA_Anual";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.ConsolidadoUtilidades_Diario:    // consolidado diario utilidades
                    PaginaAjuda += "Consolidado_Utilidades_Diario";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.ConsolidadoUtilidades_Mensal:    // consolidado mensal utilidades
                    PaginaAjuda += "Consolidado_Utilidades_Mensal";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.ConsolidadoUtilidades_Anual:    // consolidado anual utilidades
                    PaginaAjuda += "Consolidado_Utilidades_Anual";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.TempoAtuacao_EntradasDigitais_Mensal:    // tempo de atuação mensal entradas digitais
                    PaginaAjuda = "TempoAtuacao_EntradasDigitais";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.TempoAtuacao_SaidasDigitais_Mensal:    // tempo de atuação mensal entradas digitais
                    PaginaAjuda = "TempoAtuacao_SaidasDigitais";
                    TemPeriodo = false;
                    break;

                case TIPO_RELAT.Provisionamento_Semanal_Tipo1_Tipo2:    // provisionamento semanal tipo 1 ou tipo 2
                case TIPO_RELAT.Provisionamento_Semanal_Tipo3:          // provisionamento semanal tipo 3
                    PaginaAjuda = "Contato";
                    TemPeriodo = false;
                    break;

                default:
                    PaginaAjuda = "Contato";
                    TemPeriodo = false;
                    break;
            }

            // verifica se tem periodo
            if (TemPeriodo)
            {
                switch (TipoPeriodo)
                {
                    case TIPO_RELAT_PERIODO.Diario:     // diario
                        PaginaAjuda += "Diario";
                        break;

                    case TIPO_RELAT_PERIODO.Semanal:    // semanal
                        PaginaAjuda += "Semanal";
                        break;

                    case TIPO_RELAT_PERIODO.Mensal:     // mensal
                        PaginaAjuda += "Mensal";
                        break;

                    case TIPO_RELAT_PERIODO.Anual:      // anual
                        PaginaAjuda += "Anual";
                        break;
                }
            }

            CookieStore.SalvaCookie_String("PaginaAjuda", PaginaAjuda);
            ViewBag.PaginaAjuda = PaginaAjuda;

            return PartialView();
        }

        private void LogMessage(string msg)
        {
            // log
            Funcoes_Log.Mensagem("SmartEnergy_LogRelatorio", msg);
        }
    }
}
