﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class RelatoriosController
    {
        //
        // FATOR DE UTILIZACAO
        //

        // GET: Relatorio Fator de Utilizacao
        public ActionResult Relat_FatUtilizacao(int IDCliente, int IDMedicao)
        {
            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Relat_FatUtilizacao");

            // relatorio fator de utilizacao
            return (Relat_Grafico_Show(IDCliente, IDMedicao, 7));
        }

        // Fator de Utilizacao Diario
        private void FatUtilizacao_Diario(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_DIARIO relatorio = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise = new RELAT_DIARIO_ANALISE();

            RELAT_DIARIO relatorio_sim = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise_sim = new RELAT_DIARIO_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // calcula valores
            retorno = SmCalcDB_Energia_RelatDiario((char)0, ref config_interface, (char)7, (char)IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   0, 15, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var FatUtilizacao = new double[98];
            var DemandaAtv = new double[98];
            var Periodo = new int[98];
            var Contrato = new double[98];
            var Datas = new string[98];
            var DatasN = new DateTime[98];
            var Horas = new string[98];

            // grafico simulacao
            var FatUtilizacao_Sim = new double[98];
            var DemandaAtv_Sim = new double[98];
            var Periodo_Sim = new int[98];
            var Contrato_Sim = new double[98];
            var Datas_Sim = new string[98];
            var DatasN_Sim = new DateTime[98];

            double FatUtilizacao_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 15min para a primeira barra
            strData = strData.AddMinutes(-15);

            int i = 0;
            int j = 0;

            for (i = 0; i < 98; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Horas[i] = strData.ToString("HH:mm");

                // formata label simulacao
                Datas_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Sim[i] = strData;

                // proximo quinze minutos
                strData = strData.AddMinutes(15);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    FatUtilizacao[i] = relatorio.registro[0].valor1;
                    DemandaAtv[i] = relatorio.registro[0].valor2;
                    Periodo[i] = relatorio.registro[0].periodo;
                    Contrato[i] = relatorio.contrato[0];

                    // zera simulacao
                    FatUtilizacao_Sim[i] = relatorio_sim.registro[0].valor1;
                    DemandaAtv_Sim[i] = relatorio_sim.registro[0].valor2;
                    Periodo_Sim[i] = relatorio_sim.registro[0].periodo;
                    Contrato_Sim[i] = relatorio_sim.contrato[0];
                }

                // verifica se ultima barra
                if (i == 97)
                {
                    // zera
                    FatUtilizacao[i] = relatorio.registro[95].valor1;
                    DemandaAtv[i] = relatorio.registro[95].valor2;
                    Periodo[i] = relatorio.registro[95].periodo;
                    Contrato[i] = relatorio.contrato[95];

                    // zera simulacao
                    FatUtilizacao_Sim[i] = relatorio_sim.registro[95].valor1;
                    DemandaAtv_Sim[i] = relatorio_sim.registro[95].valor2;
                    Periodo_Sim[i] = relatorio_sim.registro[95].periodo;
                    Contrato_Sim[i] = relatorio_sim.contrato[95];
                }

                if (i >= 1 && i <= 96)
                {
                    // copia
                    j = i - 1;

                    FatUtilizacao[i] = relatorio.registro[j].valor1;
                    DemandaAtv[i] = relatorio.registro[j].valor2;
                    Periodo[i] = relatorio.registro[j].periodo;
                    Contrato[i] = relatorio.contrato[j];

                    // copia simulacao
                    FatUtilizacao_Sim[i] = relatorio_sim.registro[j].valor1;
                    DemandaAtv_Sim[i] = relatorio_sim.registro[j].valor2;
                    Periodo_Sim[i] = relatorio_sim.registro[j].periodo;
                    Contrato_Sim[i] = relatorio_sim.contrato[j];

                    // verifica se sem registro
                    if (relatorio.registro[j].periodo == 3)
                    {
                        FatUtilizacao[i] = 0.0;
                        DemandaAtv[i] = 0.0;
                        Contrato[i] = 0.0;
                    }

                    // verifica se sem registro simulacao
                    if (relatorio_sim.registro[j].periodo == 3)
                    {
                        FatUtilizacao_Sim[i] = 0.0;
                        DemandaAtv_Sim[i] = 0.0;
                        Contrato_Sim[i] = 0.0;
                    }

                    // verifica fator de utilizacao maximo
                    if (FatUtilizacao[i] > FatUtilizacao_max_grafico)
                        FatUtilizacao_max_grafico = FatUtilizacao[i];

                    // verifica fator de utilizacao maximo simulacao
                    if (FatUtilizacao_Sim[i] > FatUtilizacao_max_grafico)
                        FatUtilizacao_max_grafico = FatUtilizacao_Sim[i];
                }
            }

            FatUtilizacao_max_grafico = FatUtilizacao_max_grafico * 1.1;

            ViewBag.FatUtilizacaoMaxGrafico = FatUtilizacao_max_grafico;

            ViewBag.FatUtilizacao = FatUtilizacao;
            ViewBag.DemandaAtv = DemandaAtv;
            ViewBag.Periodo = Periodo;
            ViewBag.Contrato = Contrato;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Horas = Horas;

            // simulacao
            ViewBag.FatUtilizacao_Sim = FatUtilizacao_Sim;
            ViewBag.DemandaAtv_Sim = DemandaAtv_Sim;
            ViewBag.Periodo_Sim = Periodo_Sim;
            ViewBag.Contrato_Sim = Contrato_Sim;
            ViewBag.Datas_Sim = Datas_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Diario;

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:d}", DataAtual);

            // fator de utilizacao maximo ponta
            ViewBag.FatUtilizacao_MaxP = string.Format("{0:#,##0.0}", analise.analise_valor1.maximo[0]);
            DateTime FatUtilizacao_MaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max[0]);
            if (FatUtilizacao_MaxP_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MaxP_DataHora = string.Format("{0:HH:mm}", FatUtilizacao_MaxP_DataHora);
            else
                ViewBag.FatUtilizacao_MaxP_DataHora = "--:--";
            ViewBag.FatUtilizacao_MaxP_DataHoraN = FatUtilizacao_MaxP_DataHora;

            // fator de utilizacao maximo ponta simulacao
            ViewBag.FatUtilizacao_MaxP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.maximo[0]);
            DateTime FatUtilizacao_MaxP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor1.datahora_max[0]);
            if (FatUtilizacao_MaxP_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MaxP_DataHora_Sim = string.Format("{0:HH:mm}", FatUtilizacao_MaxP_DataHora);
            else
                ViewBag.FatUtilizacao_MaxP_DataHora_Sim = "--:--";
            ViewBag.FatUtilizacao_MaxP_DataHoraN_Sim = FatUtilizacao_MaxP_DataHora_Sim;

            // fator de utilizacao maximo fora ponta indutivo
            ViewBag.FatUtilizacao_MaxFPI = string.Format("{0:#,##0.0}", analise.analise_valor1.maximo[1]);
            DateTime FatUtilizacao_MaxFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max[1]);
            if (FatUtilizacao_MaxFPI_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MaxFPI_DataHora = string.Format("{0:HH:mm}", FatUtilizacao_MaxFPI_DataHora);
            else
                ViewBag.FatUtilizacao_MaxFPI_DataHora = "--:--";
            ViewBag.FatUtilizacao_MaxFPI_DataHoraN = FatUtilizacao_MaxFPI_DataHora;

            // fator de utilizacao maximo fora ponta indutivo simulacao
            ViewBag.FatUtilizacao_MaxFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.maximo[1]);
            DateTime FatUtilizacao_MaxFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor1.datahora_max[1]);
            if (FatUtilizacao_MaxFPI_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MaxFPI_DataHora_Sim = string.Format("{0:HH:mm}", FatUtilizacao_MaxFPI_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MaxFPI_DataHora_Sim = "--:--";
            ViewBag.FatUtilizacao_MaxFPI_DataHoraN_Sim = FatUtilizacao_MaxFPI_DataHora_Sim;

            // fator de utilizacao maximo fora ponta capacitivo
            ViewBag.FatUtilizacao_MaxFPC = string.Format("{0:#,##0.0}", analise.analise_valor1.maximo[2]);
            DateTime FatUtilizacao_MaxFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_max[2]);
            if (FatUtilizacao_MaxFPC_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MaxFPC_DataHora = string.Format("{0:HH:mm}", FatUtilizacao_MaxFPC_DataHora);
            else
                ViewBag.FatUtilizacao_MaxFPC_DataHora = "--:--";
            ViewBag.FatUtilizacao_MaxFPC_DataHoraN = FatUtilizacao_MaxFPC_DataHora;

            // fator de utilizacao maximo fora ponta capacitivo simulacao
            ViewBag.FatUtilizacao_MaxFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.maximo[2]);
            DateTime FatUtilizacao_MaxFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor1.datahora_max[2]);
            if (FatUtilizacao_MaxFPC_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MaxFPC_DataHora_Sim = string.Format("{0:HH:mm}", FatUtilizacao_MaxFPC_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MaxFPC_DataHora_Sim = "--:--";
            ViewBag.FatUtilizacao_MaxFPC_DataHoraN_Sim = FatUtilizacao_MaxFPC_DataHora_Sim;

            // fator de utilizacao minimo ponta
            ViewBag.FatUtilizacao_MinP = string.Format("{0:#,##0.0}", analise.analise_valor1.minimo[0]);
            DateTime FatUtilizacao_MinP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_min[0]);
            if (FatUtilizacao_MinP_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MinP_DataHora = string.Format("{0:HH:mm}", FatUtilizacao_MinP_DataHora);
            else
                ViewBag.FatUtilizacao_MinP_DataHora = "--:--";
            ViewBag.FatUtilizacao_MinP_DataHoraN = FatUtilizacao_MinP_DataHora;

            // fator de utilizacao minimo ponta simulacao
            ViewBag.FatUtilizacao_MinP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.minimo[0]);
            DateTime FatUtilizacao_MinP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor1.datahora_min[0]);
            if (FatUtilizacao_MinP_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MinP_DataHora_Sim = string.Format("{0:HH:mm}", FatUtilizacao_MinP_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MinP_DataHora_Sim = "--:--";
            ViewBag.FatUtilizacao_MinP_DataHoraN_Sim = FatUtilizacao_MinP_DataHora_Sim;

            // fator de utilizacao minimo fora ponta indutivo
            ViewBag.FatUtilizacao_MinFPI = string.Format("{0:#,##0.0}", analise.analise_valor1.minimo[1]);
            DateTime FatUtilizacao_MinFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_min[1]);
            if (FatUtilizacao_MinFPI_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MinFPI_DataHora = string.Format("{0:HH:mm}", FatUtilizacao_MinFPI_DataHora);
            else
                ViewBag.FatUtilizacao_MinFPI_DataHora = "--:--";
            ViewBag.FatUtilizacao_MinFPI_DataHoraN = FatUtilizacao_MinFPI_DataHora;

            // fator de utilizacao minimo fora ponta indutivo simulacao
            ViewBag.FatUtilizacao_MinFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.minimo[1]);
            DateTime FatUtilizacao_MinFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor1.datahora_min[1]);
            if (FatUtilizacao_MinFPI_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MinFPI_DataHora_Sim = string.Format("{0:HH:mm}", FatUtilizacao_MinFPI_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MinFPI_DataHora_Sim = "--:--";
            ViewBag.FatUtilizacao_MinFPI_DataHoraN_Sim = FatUtilizacao_MinFPI_DataHora_Sim;

            // fator de utilizacao fora ponta capacitivo 
            ViewBag.FatUtilizacao_MinFPC = string.Format("{0:#,##0.0}", analise.analise_valor1.minimo[2]);
            DateTime FatUtilizacao_MinFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor1.datahora_min[2]);
            if (FatUtilizacao_MinFPC_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MinFPC_DataHora = string.Format("{0:HH:mm}", FatUtilizacao_MinFPC_DataHora);
            else
                ViewBag.FatUtilizacao_MinFPC_DataHora = "--:--";
            ViewBag.FatUtilizacao_MinFPC_DataHoraN = FatUtilizacao_MinFPC_DataHora;

            // fator de utilizacao fora ponta capacitivo simulacao
            ViewBag.FatUtilizacao_MinFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.minimo[2]);
            DateTime FatUtilizacao_MinFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor1.datahora_min[2]);
            if (FatUtilizacao_MinFPC_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MinFPC_DataHora_Sim = string.Format("{0:HH:mm}", FatUtilizacao_MinFPC_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MinFPC_DataHora_Sim = "--:--";
            ViewBag.FatUtilizacao_MinFPC_DataHoraN_Sim = FatUtilizacao_MinFPC_DataHora_Sim;

            ViewBag.FatUtilizacao_MedP = string.Format("{0:#,##0.0}", analise.analise_valor1.medio[0]);
            ViewBag.FatUtilizacao_MedFPI = string.Format("{0:#,##0.0}", analise.analise_valor1.medio[1]);
            ViewBag.FatUtilizacao_MedFPC = string.Format("{0:#,##0.0}", analise.analise_valor1.medio[2]);

            // simulacao
            ViewBag.FatUtilizacao_MedP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.medio[0]);
            ViewBag.FatUtilizacao_MedFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.medio[1]);
            ViewBag.FatUtilizacao_MedFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor1.medio[2]);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(1);

            ViewBag.data_hora_ini = data_hora_ini;

            EventosPeriodo(IDCliente, IDMedicao, data_hora_ini, data_hora_fim);

            return;
        }

        // Fator de Utilizacao Diario XLS
        private HSSFWorkbook FatUtilizacao_Diario_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Período", "Fator de Utilização (%)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 97; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _datahoraStyle);

                // periodo
                numeroCelulaXLS(row, 1, ViewBag.Periodo[i], _intCellStyle);

                // fator de utilizacao
                numeroCelulaXLS(row, 2, ViewBag.FatUtilizacao[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Utilização", "Relatório Diário", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Fator de Utilização (%)", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // fator de utilizacao maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máximo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MaxFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatUtilizacao_MaxFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MaxFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatUtilizacao_MaxFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MaxP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatUtilizacao_MaxP_DataHoraN, _datahoraStyle);

            // fator de utilizacao minimo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínimo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MinFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatUtilizacao_MinFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MinFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatUtilizacao_MinFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MinP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatUtilizacao_MinP_DataHoraN, _datahoraStyle);

            // fator de utilizacao periodo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Período", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MedFPC), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MedFPI), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MedP), _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Fator de Utilizacao Diario XLS simulacao
        private HSSFWorkbook FatUtilizacao_Diario_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // simulacao 
            string[] simulacao = { "Relatório Fator de Utilização - Simulação" };

            // adiciona cabecalho simulacao
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas do titulo simulacao
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 2));

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Período", "Fator de Utilização (%)" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 97; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[i], _datahoraStyle);

                // periodo
                numeroCelulaXLS(row, 1, ViewBag.Periodo_Sim[i], _intCellStyle);

                // fator de utilizacao
                numeroCelulaXLS(row, 2, ViewBag.FatUtilizacao_Sim[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Utilização - Simulação", "Relatório Diário", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Fator de Utilização (%)", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // fator de utilizacao maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máximo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MaxFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatUtilizacao_MaxFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MaxFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatUtilizacao_MaxFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MaxP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatUtilizacao_MaxP_DataHoraN_Sim, _datahoraStyle);

            // fator de utilizacao minimo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínimo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MinFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatUtilizacao_MinFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MinFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatUtilizacao_MinFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MinP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatUtilizacao_MinP_DataHoraN_Sim, _datahoraStyle);

            // fator de utilizacao periodo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Período", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MedFPC_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MedFPI_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MedP_Sim), _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Fator de Utilizacao Semanal
        private void FatUtilizacao_Semanal(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_SEMANAL relatorio = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise = new RELAT_SEMANAL_ANALISE();

            RELAT_SEMANAL relatorio_sim = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise_sim = new RELAT_SEMANAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // calcula valores
            retorno = SmCalcDB_Energia_RelatSemanal((char)0, ref config_interface, (char)7, (char)IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   1, 0, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var FatUtilizacaoP = new double[9];
            var FatUtilizacaoFPI = new double[9];
            var FatUtilizacaoFPC = new double[9];
            var Datas = new string[9];
            var DatasN = new DateTime[9];
            var Dias = new string[9];

            // grafico simulacao
            var FatUtilizacaoP_Sim = new double[9];
            var FatUtilizacaoFPI_Sim = new double[9];
            var FatUtilizacaoFPC_Sim = new double[9];
            var Datas_Sim = new string[9];
            var DatasN_Sim = new DateTime[9];

            DateTime inicio = new DateTime();
            DateTime fim = new DateTime();

            double FatUtilizacao_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 9; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = string.Format("{0:d}", strData);

                // formata label simulacao
                Datas_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Sim[i] = strData;

                // guarda inicio
                if (i == 1)
                {
                    inicio = strData;
                }

                // guarda fim
                if (i == 7)
                {
                    fim = strData;
                }

                // proximo dias
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    FatUtilizacaoP[i] = relatorio.registro[0].valor[0];
                    FatUtilizacaoFPI[i] = relatorio.registro[0].valor[1];
                    FatUtilizacaoFPC[i] = relatorio.registro[0].valor[2];

                    // zera simulacao
                    FatUtilizacaoP_Sim[i] = relatorio_sim.registro[0].valor[0];
                    FatUtilizacaoFPI_Sim[i] = relatorio_sim.registro[0].valor[1];
                    FatUtilizacaoFPC_Sim[i] = relatorio_sim.registro[0].valor[2];
                }

                if (i == 8)
                {
                    // zera
                    FatUtilizacaoP[i] = relatorio.registro[6].valor[0];
                    FatUtilizacaoFPI[i] = relatorio.registro[6].valor[1];
                    FatUtilizacaoFPC[i] = relatorio.registro[6].valor[2];

                    // zera simulacao
                    FatUtilizacaoP_Sim[i] = relatorio_sim.registro[6].valor[0];
                    FatUtilizacaoFPI_Sim[i] = relatorio_sim.registro[6].valor[1];
                    FatUtilizacaoFPC_Sim[i] = relatorio_sim.registro[6].valor[2];
                }

                if (i >= 1 && i <= 7)
                {
                    // copia
                    j = i - 1;

                    FatUtilizacaoP[i] = relatorio.registro[j].valor[0];
                    FatUtilizacaoFPI[i] = relatorio.registro[j].valor[1];
                    FatUtilizacaoFPC[i] = relatorio.registro[j].valor[2];

                    // simulacao 
                    FatUtilizacaoP_Sim[i] = relatorio_sim.registro[j].valor[0];
                    FatUtilizacaoFPI_Sim[i] = relatorio_sim.registro[j].valor[1];
                    FatUtilizacaoFPC_Sim[i] = relatorio_sim.registro[j].valor[2];

                    // verifica fator de utilizacao maximo
                    if (FatUtilizacaoP[i] > FatUtilizacao_max_grafico)
                        FatUtilizacao_max_grafico = FatUtilizacaoP[i];

                    if (FatUtilizacaoFPI[i] > FatUtilizacao_max_grafico)
                        FatUtilizacao_max_grafico = FatUtilizacaoFPI[i];

                    if (FatUtilizacaoFPC[i] > FatUtilizacao_max_grafico)
                        FatUtilizacao_max_grafico = FatUtilizacaoFPC[i];

                    // verifica fator de utilizacao maximo simulacao
                    if (FatUtilizacaoP_Sim[i] > FatUtilizacao_max_grafico)
                        FatUtilizacao_max_grafico = FatUtilizacaoP_Sim[i];

                    if (FatUtilizacaoFPI_Sim[i] > FatUtilizacao_max_grafico)
                        FatUtilizacao_max_grafico = FatUtilizacaoFPI_Sim[i];

                    if (FatUtilizacaoFPC_Sim[i] > FatUtilizacao_max_grafico)
                        FatUtilizacao_max_grafico = FatUtilizacaoFPC_Sim[i];
                }
            }

            FatUtilizacao_max_grafico = FatUtilizacao_max_grafico * 1.1;

            ViewBag.FatUtilizacaoMaxGrafico = FatUtilizacao_max_grafico;

            ViewBag.FatUtilizacaoP = FatUtilizacaoP;
            ViewBag.FatUtilizacaoFPI = FatUtilizacaoFPI;
            ViewBag.FatUtilizacaoFPC = FatUtilizacaoFPC;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            // simulacao
            ViewBag.FatUtilizacaoP_Sim = FatUtilizacaoP_Sim;
            ViewBag.FatUtilizacaoFPI_Sim = FatUtilizacaoFPI_Sim;
            ViewBag.FatUtilizacaoFPC_Sim = FatUtilizacaoFPC_Sim;
            ViewBag.Datas_Sim = Datas_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Semanal;

            // data atual
            ViewBag.DataAtual = string.Format("{0:d}", DataAtual);
            ViewBag.DataAtualN = inicio;
            ViewBag.DataFinalN = fim;
            ViewBag.DataTextoAtual = string.Format("{0:d} - {1:d} ", inicio, fim);

            // fator de utilizacao maximo ponta
            ViewBag.FatUtilizacao_MaxP = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[0]);
            DateTime FatUtilizacao_MaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (FatUtilizacao_MaxP_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MaxP_DataHora = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MaxP_DataHora, FatUtilizacao_MaxP_DataHora);
            else
                ViewBag.FatUtilizacao_MaxP_DataHora = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MaxP_DataHoraN = FatUtilizacao_MaxP_DataHora;

            // fator de utilizacao maximo ponta simulacao 
            ViewBag.FatUtilizacao_MaxP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[0]);
            DateTime FatUtilizacao_MaxP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[0]);
            if (FatUtilizacao_MaxP_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MaxP_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MaxP_DataHora_Sim, FatUtilizacao_MaxP_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MaxP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MaxP_DataHoraN_Sim = FatUtilizacao_MaxP_DataHora_Sim;

            // fator de utilizacao maximo fora ponta indutivo
            ViewBag.FatUtilizacao_MaxFPI = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[1]);
            DateTime FatUtilizacao_MaxFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[1]);
            if (FatUtilizacao_MaxFPI_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MaxFPI_DataHora = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MaxFPI_DataHora, FatUtilizacao_MaxFPI_DataHora);
            else
                ViewBag.FatUtilizacao_MaxFPI_DataHora = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MaxFPI_DataHoraN = FatUtilizacao_MaxFPI_DataHora;

            // fator de utilizacao maximo fora ponta indutivo simulacao
            ViewBag.FatUtilizacao_MaxFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[1]);
            DateTime FatUtilizacao_MaxFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[1]);
            if (FatUtilizacao_MaxFPI_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MaxFPI_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MaxFPI_DataHora_Sim, FatUtilizacao_MaxFPI_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MaxFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MaxFPI_DataHoraN_Sim = FatUtilizacao_MaxFPI_DataHora_Sim;

            // fator de utilizacao maximo fora ponta capacitivo 
            ViewBag.FatUtilizacao_MaxFPC = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[2]);
            DateTime FatUtilizacao_MaxFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[2]);
            if (FatUtilizacao_MaxFPC_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MaxFPC_DataHora = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MaxFPC_DataHora, FatUtilizacao_MaxFPC_DataHora);
            else
                ViewBag.FatUtilizacao_MaxFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MaxFPC_DataHoraN = FatUtilizacao_MaxFPC_DataHora;

            // fator de utilizacao maximo fora ponta capacitivo simulacao
            ViewBag.FatUtilizacao_MaxFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[2]);
            DateTime FatUtilizacao_MaxFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[2]);
            if (FatUtilizacao_MaxFPC_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MaxFPC_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MaxFPC_DataHora_Sim, FatUtilizacao_MaxFPC_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MaxFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MaxFPC_DataHoraN_Sim = FatUtilizacao_MaxFPC_DataHora_Sim;

            // fator de utilizacao minimo ponta
            ViewBag.FatUtilizacao_MinP = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[0]);
            DateTime FatUtilizacao_MinP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[0]);
            if (FatUtilizacao_MinP_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MinP_DataHora = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MinP_DataHora, FatUtilizacao_MinP_DataHora);
            else
                ViewBag.FatUtilizacao_MinP_DataHora = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MinP_DataHoraN = FatUtilizacao_MinP_DataHora;

            // fator de utilizacao minimo ponta simulacao
            ViewBag.FatUtilizacao_MinP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.minimo[0]);
            DateTime FatUtilizacao_MinP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[0]);
            if (FatUtilizacao_MinP_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MinP_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MinP_DataHora_Sim, FatUtilizacao_MinP_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MinP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MinP_DataHoraN_Sim = FatUtilizacao_MinP_DataHora_Sim;

            // fator de utilizacao minimo fora ponta indutivo 
            ViewBag.FatUtilizacao_MinFPI = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[1]);
            DateTime FatUtilizacao_MinFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[1]);
            if (FatUtilizacao_MinFPI_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MinFPI_DataHora = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MinFPI_DataHora, FatUtilizacao_MinFPI_DataHora);
            else
                ViewBag.FatUtilizacao_MinFPI_DataHora = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MinFPI_DataHoraN = FatUtilizacao_MinFPI_DataHora;

            // fator de utilizacao minimo fora ponta indutivo simulacao
            ViewBag.FatUtilizacao_MinFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.minimo[1]);
            DateTime FatUtilizacao_MinFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[1]);
            if (FatUtilizacao_MinFPI_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MinFPI_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MinFPI_DataHora_Sim, FatUtilizacao_MinFPI_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MinFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MinFPI_DataHoraN_Sim = FatUtilizacao_MinFPI_DataHora_Sim;

            // fator de utilizacao minimo fora ponta capacitivo
            ViewBag.FatUtilizacao_MinFPC = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[2]);
            DateTime FatUtilizacao_MinFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[2]);
            if (FatUtilizacao_MinFPC_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MinFPC_DataHora = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MinFPC_DataHora, FatUtilizacao_MinFPC_DataHora);
            else
                ViewBag.FatUtilizacao_MinFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MinFPC_DataHoraN = FatUtilizacao_MinFPC_DataHora;

            // fator de utilizacao minimo fora ponta capacitivo simulacao
            ViewBag.FatUtilizacao_MinFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.minimo[2]);
            DateTime FatUtilizacao_MinFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[2]);
            if (FatUtilizacao_MinFPC_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MinFPC_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MinFPC_DataHora_Sim, FatUtilizacao_MinFPC_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MinFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MinFPC_DataHoraN_Sim = FatUtilizacao_MinFPC_DataHora_Sim;

            ViewBag.FatUtilizacao_MedP = string.Format("{0:#,##0.0}", analise.analise_valor.medio[0]);
            ViewBag.FatUtilizacao_MedFPI = string.Format("{0:#,##0.0}", analise.analise_valor.medio[1]);
            ViewBag.FatUtilizacao_MedFPC = string.Format("{0:#,##0.0}", analise.analise_valor.medio[2]);

            // simulacao
            ViewBag.FatUtilizacao_MedP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[0]);
            ViewBag.FatUtilizacao_MedFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[1]);
            ViewBag.FatUtilizacao_MedFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[2]);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(inicio.Year, inicio.Month, inicio.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(7);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Fator de Utilizacao Semanal XLS
        private HSSFWorkbook FatUtilizacao_Semanal_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Semana", "Data e Hora", "Fora de Ponta Capacitivo (%)", "Fora de Ponta Indutivo (%)", "Ponta (%)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 8; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // semana
                textoCelulaXLS(row, 0, string.Format("{0:dddd}", ViewBag.DatasN[i]));

                // data e hora
                datahoraCelulaXLS(row, 1, ViewBag.DatasN[i], _dataStyle);

                // fator de utilizacao capacitivo
                numeroCelulaXLS(row, 2, ViewBag.FatUtilizacaoFPC[i], _1CellStyle);

                // fator de utilizacao fora de ponta indutivo
                numeroCelulaXLS(row, 3, ViewBag.FatUtilizacaoFPI[i], _1CellStyle);

                // fator de utilizacao ponta
                numeroCelulaXLS(row, 4, ViewBag.FatUtilizacaoP[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Utilização", "Relatório Semanal", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Fator de Utilização (%)", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // fator de utilizacao maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máximo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MaxFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatUtilizacao_MaxFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MaxFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatUtilizacao_MaxFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MaxP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatUtilizacao_MaxP_DataHoraN, _datahoraStyle);

            // fator de utilizacao minimo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínimo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MinFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatUtilizacao_MinFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MinFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatUtilizacao_MinFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MinP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatUtilizacao_MinP_DataHoraN, _datahoraStyle);

            // fator de utilizacao periodo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Período", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MedFPC), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MedFPI), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MedP), _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Fator de Utilizacao Semanal XLS simulacao
        private HSSFWorkbook FatUtilizacao_Semanal_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // simulacao 
            string[] simulacao = { "Relatório Fator de Utilização - Simulação" };

            // adiciona cabecalho simulacao
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas do titulo simulacao
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 4));

            // cabecalho
            string[] cabecalho = { "Semana", "Data e Hora", "Fora de Ponta Capacitivo (%)", "Fora de Ponta Indutivo (%)", "Ponta (%)" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho,1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 8; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // semana
                textoCelulaXLS(row, 0, string.Format("{0:dddd}", ViewBag.DatasN[i]));

                // data e hora
                datahoraCelulaXLS(row, 1, ViewBag.DatasN_Sim[i], _dataStyle);

                // fator de utilizacao capacitivo
                numeroCelulaXLS(row, 2, ViewBag.FatUtilizacaoFPC_Sim[i], _1CellStyle);

                // fator de utilizacao fora de ponta indutivo
                numeroCelulaXLS(row, 3, ViewBag.FatUtilizacaoFPI_Sim[i], _1CellStyle);

                // fator de utilizacao ponta
                numeroCelulaXLS(row, 4, ViewBag.FatUtilizacaoP_Sim[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Utilização - Simulação", "Relatório Semanal", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Fator de Utilização (%)", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // fator de utilizacao maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máximo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MaxFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatUtilizacao_MaxFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MaxFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatUtilizacao_MaxFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MaxP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatUtilizacao_MaxP_DataHoraN_Sim, _datahoraStyle);

            // fator de utilizacao minimo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínimo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MinFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatUtilizacao_MinFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MinFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatUtilizacao_MinFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MinP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatUtilizacao_MinP_DataHoraN_Sim, _datahoraStyle);

            // fator de utilizacao periodo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Período", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MedFPC_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MedFPI_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MedP_Sim), _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Fator de Utilizacao Mensal
        private void FatUtilizacao_Mensal(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_MENSAL relatorio = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise = new RELAT_MENSAL_ANALISE();

            RELAT_MENSAL relatorio_sim = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise_sim = new RELAT_MENSAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // calcula valores
            retorno = SmCalcDB_Energia_RelatMensal((char)0, ref config_interface, (char)7, (char)IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);

            // numero de dias do mes
            int NumDiasMes = relatorio.num_dias;

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[NumDiasMes - 1].datahora.data.ano,
                                   (int)relatorio.registro[NumDiasMes - 1].datahora.data.mes,
                                   1, 0, 15, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var FatUtilizacaoP = new double[42];
            var FatUtilizacaoFPI = new double[42];
            var FatUtilizacaoFPC = new double[42];
            var Datas = new string[42];
            var DatasN = new DateTime[42];
            var Dias = new string[42];

            // grafico simulacao 
            var FatUtilizacaoP_Sim = new double[42];
            var FatUtilizacaoFPI_Sim = new double[42];
            var FatUtilizacaoFPC_Sim = new double[42];
            var Datas_Sim = new string[42];
            var DatasN_Sim = new DateTime[42];

            double FatUtilizacao_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = strData.ToString("dd/MM");

                // formata label simulacao
                Datas_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Sim[i] = strData;

                // proximo dia
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    FatUtilizacaoP[i] = relatorio.registro[0].valor[0];
                    FatUtilizacaoFPI[i] = relatorio.registro[0].valor[1];
                    FatUtilizacaoFPC[i] = relatorio.registro[0].valor[2];

                    // zera simulacao 
                    FatUtilizacaoP_Sim[i] = relatorio_sim.registro[0].valor[0];
                    FatUtilizacaoFPI_Sim[i] = relatorio_sim.registro[0].valor[1];
                    FatUtilizacaoFPC_Sim[i] = relatorio_sim.registro[0].valor[2];
                }

                if (i == (NumDiasMes + 1))
                {
                    // zera
                    FatUtilizacaoP[i] = relatorio.registro[NumDiasMes - 1].valor[0];
                    FatUtilizacaoFPI[i] = relatorio.registro[NumDiasMes - 1].valor[1];
                    FatUtilizacaoFPC[i] = relatorio.registro[NumDiasMes - 1].valor[2];

                    // zera simulacao
                    FatUtilizacaoP_Sim[i] = relatorio_sim.registro[NumDiasMes - 1].valor[0];
                    FatUtilizacaoFPI_Sim[i] = relatorio_sim.registro[NumDiasMes - 1].valor[1];
                    FatUtilizacaoFPC_Sim[i] = relatorio_sim.registro[NumDiasMes - 1].valor[2];
                }

                if (i >= 1 && i <= NumDiasMes)
                {
                    // copia
                    j = i - 1;

                    FatUtilizacaoP[i] = relatorio.registro[j].valor[0];
                    FatUtilizacaoFPI[i] = relatorio.registro[j].valor[1];
                    FatUtilizacaoFPC[i] = relatorio.registro[j].valor[2];

                    // simulacao
                    FatUtilizacaoP_Sim[i] = relatorio_sim.registro[j].valor[0];
                    FatUtilizacaoFPI_Sim[i] = relatorio_sim.registro[j].valor[1];
                    FatUtilizacaoFPC_Sim[i] = relatorio_sim.registro[j].valor[2];

                    // verifica fator de utilizacao maximo
                    if (FatUtilizacaoP[i] > FatUtilizacao_max_grafico)
                        FatUtilizacao_max_grafico = FatUtilizacaoP[i];

                    if (FatUtilizacaoFPI[i] > FatUtilizacao_max_grafico)
                        FatUtilizacao_max_grafico = FatUtilizacaoFPI[i];

                    if (FatUtilizacaoFPC[i] > FatUtilizacao_max_grafico)
                        FatUtilizacao_max_grafico = FatUtilizacaoFPC[i];

                    // verifica fator de utilizacao maximo simulacao 
                    if (FatUtilizacaoP_Sim[i] > FatUtilizacao_max_grafico)
                        FatUtilizacao_max_grafico = FatUtilizacaoP_Sim[i];

                    if (FatUtilizacaoFPI_Sim[i] > FatUtilizacao_max_grafico)
                        FatUtilizacao_max_grafico = FatUtilizacaoFPI_Sim[i];

                    if (FatUtilizacaoFPC_Sim[i] > FatUtilizacao_max_grafico)
                        FatUtilizacao_max_grafico = FatUtilizacaoFPC_Sim[i];
                }
            }

            FatUtilizacao_max_grafico = FatUtilizacao_max_grafico * 1.1;

            ViewBag.FatUtilizacaoMaxGrafico = FatUtilizacao_max_grafico;

            ViewBag.FatUtilizacaoP = FatUtilizacaoP;
            ViewBag.FatUtilizacaoFPI = FatUtilizacaoFPI;
            ViewBag.FatUtilizacaoFPC = FatUtilizacaoFPC;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            // simulacao
            ViewBag.FatUtilizacaoP_Sim = FatUtilizacaoP_Sim;
            ViewBag.FatUtilizacaoFPI_Sim = FatUtilizacaoFPI_Sim;
            ViewBag.FatUtilizacaoFPC_Sim = FatUtilizacaoFPC_Sim;
            ViewBag.Datas_Sim = Datas_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Mensal;

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:Y}", DataAtual);

            ViewBag.NumDiasMes = NumDiasMes;

            // fator de utilizacao maximo ponta 
            ViewBag.FatUtilizacao_MaxP = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[0]);
            DateTime FatUtilizacao_MaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (FatUtilizacao_MaxP_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MaxP_DataHora = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MaxP_DataHora, FatUtilizacao_MaxP_DataHora);
            else
                ViewBag.FatUtilizacao_MaxP_DataHora = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MaxP_DataHoraN = FatUtilizacao_MaxP_DataHora;

            // fator de utilizacao maximo ponta simulacao
            ViewBag.FatUtilizacao_MaxP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[0]);
            DateTime FatUtilizacao_MaxP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[0]);
            if (FatUtilizacao_MaxP_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MaxP_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MaxP_DataHora_Sim, FatUtilizacao_MaxP_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MaxP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MaxP_DataHoraN_Sim = FatUtilizacao_MaxP_DataHora_Sim;

            // fator de utilizacao maximo fora ponta indutivo
            ViewBag.FatUtilizacao_MaxFPI = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[1]);
            DateTime FatUtilizacao_MaxFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[1]);
            if (FatUtilizacao_MaxFPI_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MaxFPI_DataHora = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MaxFPI_DataHora, FatUtilizacao_MaxFPI_DataHora);
            else
                ViewBag.FatUtilizacao_MaxFPI_DataHora = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MaxFPI_DataHoraN = FatUtilizacao_MaxFPI_DataHora;

            // fator de utilizacao maximo fora ponta indutivo simulacao
            ViewBag.FatUtilizacao_MaxFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[1]);
            DateTime FatUtilizacao_MaxFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[1]);
            if (FatUtilizacao_MaxFPI_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MaxFPI_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MaxFPI_DataHora_Sim, FatUtilizacao_MaxFPI_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MaxFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MaxFPI_DataHoraN_Sim = FatUtilizacao_MaxFPI_DataHora_Sim;

            // fator de utilizacao maximo fora ponta capacitivo 
            ViewBag.FatUtilizacao_MaxFPC = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[2]);
            DateTime FatUtilizacao_MaxFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[2]);
            if (FatUtilizacao_MaxFPC_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MaxFPC_DataHora = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MaxFPC_DataHora, FatUtilizacao_MaxFPC_DataHora);
            else
                ViewBag.FatUtilizacao_MaxFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MaxFPC_DataHoraN = FatUtilizacao_MaxFPC_DataHora;

            // fator de utilizacao maximo fora ponta capacitivo simulacao
            ViewBag.FatUtilizacao_MaxFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[2]);
            DateTime FatUtilizacao_MaxFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[2]);
            if (FatUtilizacao_MaxFPC_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MaxFPC_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MaxFPC_DataHora_Sim, FatUtilizacao_MaxFPC_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MaxFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MaxFPC_DataHoraN_Sim = FatUtilizacao_MaxFPC_DataHora_Sim;

            // fator de utilizacao minimo ponta 
            ViewBag.FatUtilizacao_MinP = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[0]);
            DateTime FatUtilizacao_MinP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[0]);
            if (FatUtilizacao_MinP_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MinP_DataHora = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MinP_DataHora, FatUtilizacao_MinP_DataHora);
            else
                ViewBag.FatUtilizacao_MinP_DataHora = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MinP_DataHoraN = FatUtilizacao_MinP_DataHora;

            // fator de utilizacao minimo ponta simulacao
            ViewBag.FatUtilizacao_MinP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.minimo[0]);
            DateTime FatUtilizacao_MinP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[0]);
            if (FatUtilizacao_MinP_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MinP_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MinP_DataHora_Sim, FatUtilizacao_MinP_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MinP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MinP_DataHoraN_Sim = FatUtilizacao_MinP_DataHora_Sim;

            // fator de utilizacao minimo fora ponta indutivo 
            ViewBag.FatUtilizacao_MinFPI = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[1]);
            DateTime FatUtilizacao_MinFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[1]);
            if (FatUtilizacao_MinFPI_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MinFPI_DataHora = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MinFPI_DataHora, FatUtilizacao_MinFPI_DataHora);
            else
                ViewBag.FatUtilizacao_MinFPI_DataHora = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MinFPI_DataHoraN = FatUtilizacao_MinFPI_DataHora;

            // fator de utilizacao minimo fora ponta indutivo simulacao
            ViewBag.FatUtilizacao_MinFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.minimo[1]);
            DateTime FatUtilizacao_MinFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[1]);
            if (FatUtilizacao_MinFPI_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MinFPI_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MinFPI_DataHora_Sim, FatUtilizacao_MinFPI_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MinFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MinFPI_DataHoraN_Sim = FatUtilizacao_MinFPI_DataHora_Sim;

            // fator de utilizacao minimo fora ponta capacitivo 
            ViewBag.FatUtilizacao_MinFPC = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[2]);
            DateTime FatUtilizacao_MinFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[2]);
            if (FatUtilizacao_MinFPC_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MinFPC_DataHora = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MinFPC_DataHora, FatUtilizacao_MinFPC_DataHora);
            else
                ViewBag.FatUtilizacao_MinFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MinFPC_DataHoraN = FatUtilizacao_MinFPC_DataHora;

            // fator de utilizacao minimo fora ponta capacitivo simulacao 
            ViewBag.FatUtilizacao_MinFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.minimo[2]);
            DateTime FatUtilizacao_MinFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[2]);
            if (FatUtilizacao_MinFPC_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MinFPC_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MinFPC_DataHora_Sim, FatUtilizacao_MinFPC_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MinFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MinFPC_DataHoraN_Sim = FatUtilizacao_MinFPC_DataHora_Sim;

            ViewBag.FatUtilizacao_MedP = string.Format("{0:#,##0.0}", analise.analise_valor.medio[0]);
            ViewBag.FatUtilizacao_MedFPI = string.Format("{0:#,##0.0}", analise.analise_valor.medio[1]);
            ViewBag.FatUtilizacao_MedFPC = string.Format("{0:#,##0.0}", analise.analise_valor.medio[2]);

            // simulacao
            ViewBag.FatUtilizacao_MedP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[0]);
            ViewBag.FatUtilizacao_MedFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[1]);
            ViewBag.FatUtilizacao_MedFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[2]);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Fator de Utilizacao Mensal XLS
        private HSSFWorkbook FatUtilizacao_Mensal_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo (%)", "Fora de Ponta Indutivo (%)", "Ponta (%)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < (ViewBag.NumDiasMes + 1); i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.FatUtilizacaoFPC[i], _1CellStyle);

                // fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.FatUtilizacaoFPI[i], _1CellStyle);

                // ponta
                numeroCelulaXLS(row, 3, ViewBag.FatUtilizacaoP[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Utilização", "Relatório Mensal", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Fator de Utilização (%)", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // fator de utilizacao maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máximo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MaxFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatUtilizacao_MaxFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MaxFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatUtilizacao_MaxFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MaxP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatUtilizacao_MaxP_DataHoraN, _datahoraStyle);

            // fator de utilizacao minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínimo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MinFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatUtilizacao_MinFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MinFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatUtilizacao_MinFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MinP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatUtilizacao_MinP_DataHoraN, _datahoraStyle);

            // fator de utilizacao media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Período", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MedFPC), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MedFPI), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MedP), _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Fator de Utilizacao Mensal XLS simulacao
        private HSSFWorkbook FatUtilizacao_Mensal_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // simulacao
            string[] simulacao = { "Relatório Fator de Utilização - Simulação" };

            // adiciona cabecalho simulacao
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas do titulo simulacao
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 3));

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo (%)", "Fora de Ponta Indutivo (%)", "Ponta (%)" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < (ViewBag.NumDiasMes + 1); i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[i], _dataStyle);

                // fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.FatUtilizacaoFPC_Sim[i], _1CellStyle);

                // fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.FatUtilizacaoFPI_Sim[i], _1CellStyle);

                // ponta
                numeroCelulaXLS(row, 3, ViewBag.FatUtilizacaoP_Sim[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Utilização - Simulação", "Relatório Mensal", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Fator de Utilização (%)", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // fator de utilizacao maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máximo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MaxFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatUtilizacao_MaxFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MaxFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatUtilizacao_MaxFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MaxP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatUtilizacao_MaxP_DataHoraN_Sim, _datahoraStyle);

            // fator de utilizacao minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínimo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MinFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatUtilizacao_MinFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MinFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatUtilizacao_MinFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MinP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatUtilizacao_MinP_DataHoraN_Sim, _datahoraStyle);

            // fator de utilizacao media
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Período", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MedFPC_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MedFPI_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MedP_Sim), _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Fator de Utilizacao Anual
        private void FatUtilizacao_Anual(int IDCliente, int IDMedicao, DATAHORA data_hora)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_ANUAL relatorio = new RELAT_ANUAL();
            RELAT_ANUAL_ANALISE analise = new RELAT_ANUAL_ANALISE();

            RELAT_ANUAL relatorio_sim = new RELAT_ANUAL();
            RELAT_ANUAL_ANALISE analise_sim = new RELAT_ANUAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;

            // calcula valores
            retorno = SmCalcDB_Energia_RelatAnual((char)0, ref config_interface, (char)7, (char)IDSimulacaoCenario, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   1, 1, 0, 15, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var FatUtilizacaoP = new double[14];
            var FatUtilizacaoFPI = new double[14];
            var FatUtilizacaoFPC = new double[14];
            var Datas = new string[14];
            var DatasN = new DateTime[14];
            var Meses = new string[14];

            // grafico simulacao
            var FatUtilizacaoP_Sim = new double[14];
            var FatUtilizacaoFPI_Sim = new double[14];
            var FatUtilizacaoFPC_Sim = new double[14];
            var Datas_Sim = new string[14];
            var DatasN_Sim = new DateTime[14];

            double FatUtilizacao_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            // volta 1 mes para a primeira barra
            strData = strData.AddMonths(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 14; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Meses[i] = string.Format("{0:MMMM}", strData);

                // formata label simulacao
                Datas_Sim[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN_Sim[i] = strData;

                // proximo mes
                strData = strData.AddMonths(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    FatUtilizacaoP[i] = relatorio.registro[0].valor[0];
                    FatUtilizacaoFPI[i] = relatorio.registro[0].valor[1];
                    FatUtilizacaoFPC[i] = relatorio.registro[0].valor[2];

                    // zera simulacao
                    FatUtilizacaoP_Sim[i] = relatorio_sim.registro[0].valor[0];
                    FatUtilizacaoFPI_Sim[i] = relatorio_sim.registro[0].valor[1];
                    FatUtilizacaoFPC_Sim[i] = relatorio_sim.registro[0].valor[2];
                }

                if (i == 13)
                {
                    // zera
                    FatUtilizacaoP[i] = relatorio.registro[11].valor[0];
                    FatUtilizacaoFPI[i] = relatorio.registro[11].valor[1];
                    FatUtilizacaoFPC[i] = relatorio.registro[11].valor[2];

                    // zera simulacao
                    FatUtilizacaoP_Sim[i] = relatorio_sim.registro[11].valor[0];
                    FatUtilizacaoFPI_Sim[i] = relatorio_sim.registro[11].valor[1];
                    FatUtilizacaoFPC_Sim[i] = relatorio_sim.registro[11].valor[2];
                }

                if (i >= 1 && i <= 12)
                {
                    // copia
                    j = i - 1;

                    FatUtilizacaoP[i] = relatorio.registro[j].valor[0];
                    FatUtilizacaoFPI[i] = relatorio.registro[j].valor[1];
                    FatUtilizacaoFPC[i] = relatorio.registro[j].valor[2];

                    // copia simulacao 
                    FatUtilizacaoP_Sim[i] = relatorio_sim.registro[j].valor[0];
                    FatUtilizacaoFPI_Sim[i] = relatorio_sim.registro[j].valor[1];
                    FatUtilizacaoFPC_Sim[i] = relatorio_sim.registro[j].valor[2];

                    // verifica fator de utilizacao maximo
                    if (FatUtilizacaoP[i] > FatUtilizacao_max_grafico && FatUtilizacaoP[i] < 1000)
                        FatUtilizacao_max_grafico = FatUtilizacaoP[i];

                    if (FatUtilizacaoFPI[i] > FatUtilizacao_max_grafico && FatUtilizacaoFPI[i] < 1000)
                        FatUtilizacao_max_grafico = FatUtilizacaoFPI[i];

                    if (FatUtilizacaoFPC[i] > FatUtilizacao_max_grafico && FatUtilizacaoFPC[i] < 1000)
                        FatUtilizacao_max_grafico = FatUtilizacaoFPC[i];

                    // verifica fator de utilizacao maximo simulacao
                    if (FatUtilizacaoP_Sim[i] > FatUtilizacao_max_grafico && FatUtilizacaoP_Sim[i] < 1000)
                        FatUtilizacao_max_grafico = FatUtilizacaoP_Sim[i];

                    if (FatUtilizacaoFPI_Sim[i] > FatUtilizacao_max_grafico && FatUtilizacaoFPI_Sim[i] < 1000)
                        FatUtilizacao_max_grafico = FatUtilizacaoFPI_Sim[i];

                    if (FatUtilizacaoFPC_Sim[i] > FatUtilizacao_max_grafico && FatUtilizacaoFPC_Sim[i] < 1000)
                        FatUtilizacao_max_grafico = FatUtilizacaoFPC_Sim[i];
                }
            }

            FatUtilizacao_max_grafico = FatUtilizacao_max_grafico * 1.1;

            ViewBag.FatUtilizacaoMaxGrafico = FatUtilizacao_max_grafico;

            ViewBag.FatUtilizacaoP = FatUtilizacaoP;
            ViewBag.FatUtilizacaoFPI = FatUtilizacaoFPI;
            ViewBag.FatUtilizacaoFPC = FatUtilizacaoFPC;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Meses = Meses;

            // simulacao 
            ViewBag.FatUtilizacaoP_Sim = FatUtilizacaoP_Sim;
            ViewBag.FatUtilizacaoFPI_Sim = FatUtilizacaoFPI_Sim;
            ViewBag.FatUtilizacaoFPC_Sim = FatUtilizacaoFPC_Sim;
            ViewBag.Datas_Sim = Datas_Sim;
            ViewBag.DatasN_Sim = DatasN_Sim;

            // nome do relatorio
            ViewBag.NomeRelat = SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao;
            ViewBag.PeriodoRelat = SmartEnergy.Resources.ComumTexts.Anual;

            // data atual
            ViewBag.DataAtual = string.Format("{0:yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:yyyy}", DataAtual);

            // fator de utilizacao maximo ponta 
            ViewBag.FatUtilizacao_MaxP = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[0]);
            DateTime FatUtilizacao_MaxP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[0]);
            if (FatUtilizacao_MaxP_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MaxP_DataHora = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MaxP_DataHora, FatUtilizacao_MaxP_DataHora);
            else
                ViewBag.FatUtilizacao_MaxP_DataHora = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MaxP_DataHoraN = FatUtilizacao_MaxP_DataHora;

            // fator de utilizacao maximo ponta simulacao
            ViewBag.FatUtilizacao_MaxP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[0]);
            DateTime FatUtilizacao_MaxP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[0]);
            if (FatUtilizacao_MaxP_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MaxP_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MaxP_DataHora_Sim, FatUtilizacao_MaxP_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MaxP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MaxP_DataHoraN_Sim = FatUtilizacao_MaxP_DataHora_Sim;

            // fator de utilizacao maximo fora ponta indutivo
            ViewBag.FatUtilizacao_MaxFPI = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[1]);
            DateTime FatUtilizacao_MaxFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[1]);
            if (FatUtilizacao_MaxFPI_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MaxFPI_DataHora = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MaxFPI_DataHora, FatUtilizacao_MaxFPI_DataHora);
            else
                ViewBag.FatUtilizacao_MaxFPI_DataHora = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MaxFPI_DataHoraN = FatUtilizacao_MaxFPI_DataHora;

            // fator de utilizacao maximo fora ponta indutivo simulacao
            ViewBag.FatUtilizacao_MaxFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[1]);
            DateTime FatUtilizacao_MaxFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[1]);
            if (FatUtilizacao_MaxFPI_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MaxFPI_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MaxFPI_DataHora_Sim, FatUtilizacao_MaxFPI_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MaxFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MaxFPI_DataHoraN_Sim = FatUtilizacao_MaxFPI_DataHora_Sim;

            // fator de utilizacao maximo
            ViewBag.FatUtilizacao_MaxFPC = string.Format("{0:#,##0.0}", analise.analise_valor.maximo[2]);
            DateTime FatUtilizacao_MaxFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_max[2]);
            if (FatUtilizacao_MaxFPC_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MaxFPC_DataHora = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MaxFPC_DataHora, FatUtilizacao_MaxFPC_DataHora);
            else
                ViewBag.FatUtilizacao_MaxFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MaxFPC_DataHoraN = FatUtilizacao_MaxFPC_DataHora;

            // fator de utilizacao maximo simulacao
            ViewBag.FatUtilizacao_MaxFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.maximo[2]);
            DateTime FatUtilizacao_MaxFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_max[2]);
            if (FatUtilizacao_MaxFPC_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MaxFPC_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MaxFPC_DataHora_Sim, FatUtilizacao_MaxFPC_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MaxFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MaxFPC_DataHoraN_Sim = FatUtilizacao_MaxFPC_DataHora_Sim;

            // fator de utilizacao minimo ponta 
            ViewBag.FatUtilizacao_MinP = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[0]);
            DateTime FatUtilizacao_MinP_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[0]);
            if (FatUtilizacao_MinP_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MinP_DataHora = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MinP_DataHora, FatUtilizacao_MinP_DataHora);
            else
                ViewBag.FatUtilizacao_MinP_DataHora = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MinP_DataHoraN = FatUtilizacao_MinP_DataHora;

            // fator de utilizacao minimo ponta simulacao
            ViewBag.FatUtilizacao_MinP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.minimo[0]);
            DateTime FatUtilizacao_MinP_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[0]);
            if (FatUtilizacao_MinP_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MinP_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MinP_DataHora_Sim, FatUtilizacao_MinP_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MinP_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MinP_DataHoraN_Sim = FatUtilizacao_MinP_DataHora_Sim;

            // fator de utilizacao minimo fora ponta indutivo 
            ViewBag.FatUtilizacao_MinFPI = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[1]);
            DateTime FatUtilizacao_MinFPI_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[1]);
            if (FatUtilizacao_MinFPI_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MinFPI_DataHora = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MinFPI_DataHora, FatUtilizacao_MinFPI_DataHora);
            else
                ViewBag.FatUtilizacao_MinFPI_DataHora = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MinFPI_DataHoraN = FatUtilizacao_MinFPI_DataHora;

            // fator de utilizacao minimo fora ponta indutivo simulacao 
            ViewBag.FatUtilizacao_MinFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.minimo[1]);
            DateTime FatUtilizacao_MinFPI_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise_sim.analise_valor.datahora_min[1]);
            if (FatUtilizacao_MinFPI_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MinFPI_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MinFPI_DataHora_Sim, FatUtilizacao_MinFPI_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MinFPI_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MinFPI_DataHoraN_Sim = FatUtilizacao_MinFPI_DataHora_Sim;

            // fator de utilizacao minimo fora ponta capacitivo 
            ViewBag.FatUtilizacao_MinFPC = string.Format("{0:#,##0.0}", analise.analise_valor.minimo[2]);
            DateTime FatUtilizacao_MinFPC_DataHora = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[2]);
            if (FatUtilizacao_MinFPC_DataHora.Year != 2000)
                ViewBag.FatUtilizacao_MinFPC_DataHora = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MinFPC_DataHora, FatUtilizacao_MinFPC_DataHora);
            else
                ViewBag.FatUtilizacao_MinFPC_DataHora = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MinFPC_DataHoraN = FatUtilizacao_MinFPC_DataHora;

            // fator de utilizacao minimo fora ponta capacitivo simulacao
            ViewBag.FatUtilizacao_MinFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.minimo[2]);
            DateTime FatUtilizacao_MinFPC_DataHora_Sim = Funcoes_Converte.ConverteDataHora2DateTime(analise.analise_valor.datahora_min[2]);
            if (FatUtilizacao_MinFPC_DataHora_Sim.Year != 2000)
                ViewBag.FatUtilizacao_MinFPC_DataHora_Sim = string.Format("{0:d} {1:HH:mm}", FatUtilizacao_MinFPC_DataHora_Sim, FatUtilizacao_MinFPC_DataHora_Sim);
            else
                ViewBag.FatUtilizacao_MinFPC_DataHora_Sim = "--/--/---- --:--";
            ViewBag.FatUtilizacao_MinFPC_DataHoraN_Sim = FatUtilizacao_MinFPC_DataHora_Sim;

            ViewBag.FatUtilizacao_MedP = string.Format("{0:#,##0.0}", analise.analise_valor.medio[0]);
            ViewBag.FatUtilizacao_MedFPI = string.Format("{0:#,##0.0}", analise.analise_valor.medio[1]);
            ViewBag.FatUtilizacao_MedFPC = string.Format("{0:#,##0.0}", analise.analise_valor.medio[2]);

            // simulacao
            ViewBag.FatUtilizacao_MedP_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[0]);
            ViewBag.FatUtilizacao_MedFPI_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[1]);
            ViewBag.FatUtilizacao_MedFPC_Sim = string.Format("{0:#,##0.0}", analise_sim.analise_valor.medio[2]);

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddYears(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // Fator de Utilizacao Anual XLS
        private HSSFWorkbook FatUtilizacao_Anual_XLS(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo (%)", "Fora de Ponta Indutivo (%)", "Ponta (%)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 13; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.FatUtilizacaoFPC[i], _1CellStyle);

                // fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.FatUtilizacaoFPI[i], _1CellStyle);

                // ponta
                numeroCelulaXLS(row, 3, ViewBag.FatUtilizacaoP[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Utilização", "Relatório Anual", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Fator de Utilização (%)", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // fator de utilizacao maximo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máximo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MaxFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatUtilizacao_MaxFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MaxFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatUtilizacao_MaxFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MaxP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatUtilizacao_MaxP_DataHoraN, _datahoraStyle);

            // fator de utilizacao minimo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínimo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MinFPC), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatUtilizacao_MinFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MinFPI), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatUtilizacao_MinFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MinP), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatUtilizacao_MinP_DataHoraN, _datahoraStyle);

            // fator de utilizacao periodo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Período", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MedFPC), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MedFPI), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MedP), _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // Fator de Utilizacao Anual XLS simulacao 
        private HSSFWorkbook FatUtilizacao_Anual_XLS_Simulacao(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // simulacao 
            string[] simulacao = { "Relatório Fator de Utilização - Simulação" };

            // adiciona cabecalho simulacao
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, simulacao);

            // mescla celulas do titulo simulacao
            sheet.AddMergedRegion(new CellRangeAddress(0, 0, 0, 3));

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo (%)", "Fora de Ponta Indutivo (%)", "Ponta (%)" };

            // adiciona cabecalho
            rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho, 1);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 13; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN_Sim[i], _dataStyle);

                // fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.FatUtilizacaoFPC_Sim[i], _1CellStyle);

                // fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.FatUtilizacaoFPI_Sim[i], _1CellStyle);

                // ponta
                numeroCelulaXLS(row, 3, ViewBag.FatUtilizacaoP_Sim[i], _1CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(1).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Fator de Utilização - Simulação", "Relatório Anual", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Fator de Utilização (%)", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // fator de utilizacao maximo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máximo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MaxFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatUtilizacao_MaxFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MaxFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatUtilizacao_MaxFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MaxP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatUtilizacao_MaxP_DataHoraN_Sim, _datahoraStyle);

            // fator de utilizacao minimo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínimo", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MinFPC_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.FatUtilizacao_MinFPC_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MinFPI_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.FatUtilizacao_MinFPI_DataHoraN_Sim, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MinP_Sim), _1CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.FatUtilizacao_MinP_DataHoraN_Sim, _datahoraStyle);

            // fator de utilizacao periodo
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Período", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.FatUtilizacao_MedFPC_Sim), _1CellStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.FatUtilizacao_MedFPI_Sim), _1CellStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.FatUtilizacao_MedP_Sim), _1CellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }
    }
}