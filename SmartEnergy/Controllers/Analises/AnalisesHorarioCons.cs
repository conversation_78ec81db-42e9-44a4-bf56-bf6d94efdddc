﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class AnalisesController
    {
        // GET: AnaliseHorarioCons
        public ActionResult AnaliseHorarioCons(int IDCliente)
        {
            // tela de ajuda - AnaliseHorarioCons
            CookieStore.SalvaCookie_String("PaginaAjuda", "Analise_HorarioCons");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "AnaliseHorarioCons");
            CookieStore.SalvaCookie_Int("_IDAnaliseHorarioCons", 0);

            // le cookies
            LeCookies_SmartEnergy();

            // le analises
            AnaliseHorarioConsMetodos analiseMetodos = new AnaliseHorarioConsMetodos();
            List<AnaliseHorarioConsDominio> listaAnalises = analiseMetodos.ListarPorIDCliente(IDCliente);
            ViewBag.listaAnalises = listaAnalises;

            // data atual
            DateTime datahora_ultima = DateTime.Now;
            DATAHORA data_hora = new DATAHORA();

            // le cookie datahora
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            if (datahora_cookie.Year != 2000)
            {
                datahora_ultima = datahora_cookie;
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_ultima);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", datahora_ultima);

            // IDAnaliseHorarioCons
            int IDAnaliseHorarioCons = ViewBag._IDAnaliseHorarioCons;

            if (listaAnalises != null)
            {
                if (IDAnaliseHorarioCons == 0 && listaAnalises.Count > 0)
                {
                    ViewBag._IDAnaliseHorarioCons = listaAnalises[0].IDAnaliseHorarioCons;
                }
            }

            // valores
            List<AnaliseHorarioConsValor> valores_resultado = new List<AnaliseHorarioConsValor>();
            ViewBag.analise_resultado = valores_resultado;

            // lista de erros
            var listaErros = new List<string>();

            // erros
            ViewBag.listaErros = listaErros;

            // Analise
            ViewBag.NomeAnalise = "";

            return View();
        }

        // GET: AnaliseHorarioCons Atualizar
        public PartialViewResult _AnaliseHorarioCons_Atualizar(int IDAnaliseHorarioCons, int Navegacao, string Data)
        {
            // calcula
            AnaliseHorarioCons_Show(IDAnaliseHorarioCons, Navegacao, Data);

            return PartialView();
        }

        // GET: Analise Horario de Consumo Print
        public ActionResult AnaliseHorarioCons_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDAnaliseHorarioCons
            int IDCliente = ViewBag._IDCliente;
            int IDAnaliseHorarioCons = ViewBag._IDAnaliseHorarioCons;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            AnaliseHorarioCons_Show(IDAnaliseHorarioCons, 0, data_str);

            // imprime
            return View();
        }

        // GET: Analise Horario de Consumo EMAIL
        public async Task<ActionResult> AnaliseHorarioCons_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDAnaliseHorarioCons
            int IDCliente = ViewBag._IDCliente;
            int IDAnaliseHorarioCons = ViewBag._IDAnaliseHorarioCons;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            AnaliseHorarioCons_Show(IDAnaliseHorarioCons, 0, data_str);

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("AnaliseHorarioCons_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDAnaliseHorarioCons, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_AnaliseHorarioCons_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "AnaliseHorarioConsEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.NomeAnalise", ViewBag.NomeAnalise);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Analise Horario de Consumo PDF
        public ActionResult AnaliseHorarioCons_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDAnaliseHorarioCons
            int IDCliente = ViewBag._IDCliente;
            int IDAnaliseHorarioCons = ViewBag._IDAnaliseHorarioCons;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            AnaliseHorarioCons_Show(IDAnaliseHorarioCons, 0, data_str);

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("AnaliseHorarioCons_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDAnaliseHorarioCons, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_AnaliseHorarioCons_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: AnaliseHorarioCons XLS
        public ActionResult AnaliseHorarioCons_XLS()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDAnaliseHorarioCons
            int IDCliente = ViewBag._IDCliente;
            int IDAnaliseHorarioCons = ViewBag._IDAnaliseHorarioCons;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            AnaliseHorarioCons_Show(IDAnaliseHorarioCons, 0, data_str);

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_AnaliseHorarioCons();

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("AnaliseHorarioCons_{0:000000}_{1:yyyyMMddHHmm}.xls", IDAnaliseHorarioCons, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: AnaliseHorarioCons XLS Download
        [HttpGet]
        public virtual ActionResult AnaliseHorarioCons_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: AnaliseHorarioCons Show
        public void AnaliseHorarioCons_Show(int IDAnaliseHorarioCons, int Navegacao, string Data)
        {
            // caso forcar data
            if (Navegacao == 100)
            {
                // pega data
                DateTime dateValue = DateTime.Parse(Data);

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Relat_Data", dateValue);
            }

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;

            // le analises
            AnaliseHorarioConsMetodos analiseMetodos = new AnaliseHorarioConsMetodos();
            List<AnaliseHorarioConsDominio> listaAnalises = analiseMetodos.ListarPorIDCliente(IDCliente);
            ViewBag.listaAnalises = listaAnalises;

            // IDAnaliseHorarioCons
            ViewBag._IDAnaliseHorarioCons = IDAnaliseHorarioCons;

            if (listaAnalises != null)
            {
                if (IDAnaliseHorarioCons == 0 && listaAnalises.Count > 0)
                {
                    ViewBag._IDAnaliseHorarioCons = listaAnalises[0].IDAnaliseHorarioCons;
                }
            }

            // define hora como ultimo registro do dia (23:59) 
            DateTime datahora_relat = DateTime.Now;
            DATAHORA data_hora = new DATAHORA();

            // le cookie datahora (atual ou inicial)
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            // verifica se existe datahora no cookie
            if (datahora_cookie.Year != 2000)
            {
                // copia data do cookie para ultima
                datahora_relat = datahora_cookie;
            }

            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -3:    // dia anterior

                    datahora_relat = datahora_relat.AddDays(-1);
                    break;

                case 3:    // dia seguinte

                    datahora_relat = datahora_relat.AddDays(1);
                    break;

                case -4:    // mes anterior

                    datahora_relat = datahora_relat.AddMonths(-1);
                    break;

                case 4:    // mes seguinte

                    datahora_relat = datahora_relat.AddMonths(1);
                    break;
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_relat);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_relat);

            // calcula
            Calc_AnaliseHorarioCons(IDAnaliseHorarioCons, data_hora);

            return;
        }

        // Calcula AnaliseHorarioCons
        private void Calc_AnaliseHorarioCons(int IDAnaliseHorarioCons, DATAHORA datahora)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // converte data e hora
            DateTime DataAtual = Funcoes_Converte.ConverteDataHora2DateTime(datahora);

            // lista de erros
            var listaErros = new List<string>();

            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", DataAtual);

            // valores da analise de horario de consumo
            List<AnaliseHorarioConsValor> valores_resultado = new List<AnaliseHorarioConsValor>();

            // verifica se AnaliseHorarioCons valido
            ViewBag._IDAnaliseHorarioCons = IDAnaliseHorarioCons;

            if (IDAnaliseHorarioCons <= 0)
            {
                if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
                {
                    listaErros.Add("Não existem Análises do Horário de Consumo configuradas");
                }

                // analise
                ViewBag.NomeAnalise = "";

                // valores
                ViewBag.valores_resultado = valores_resultado;

                // erros
                ViewBag.listaErros = listaErros;

                return;
            }

            // le analises
            AnaliseHorarioConsMetodos analiseMetodos = new AnaliseHorarioConsMetodos();
            AnaliseHorarioConsDominio analise = analiseMetodos.ListarPorId(IDAnaliseHorarioCons);
            ViewBag.Analise = analise;
            ViewBag.NomeAnalise = analise.Nome;

            // le grupo de medicoes
            AnaliseHorarioConsGruposMedMetodos analiseGruposMetodos = new AnaliseHorarioConsGruposMedMetodos();
            List<AnaliseHorarioConsGruposMedDominio> analiseGrupos = analiseGruposMetodos.ListarPorIDAnalise(IDAnaliseHorarioCons);

            if (analiseGrupos != null)
            {
                foreach (AnaliseHorarioConsGruposMedDominio analiseGrupo in analiseGrupos)
                {
                    // le medicao
                    MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                    MedicoesDominio medicaoConf = medicaoMetodos.ListarPorId(analiseGrupo.IDMedicao);

                    if (medicaoConf != null)
                    {
                        // copia configuração Horário de Consumo
                        analiseGrupo.HoraIniRef = DateTime.ParseExact(medicaoConf.Funcionamento_Inicio, "HH:mm", CultureInfo.InvariantCulture);
                        analiseGrupo.HoraFimRef = DateTime.ParseExact(medicaoConf.Funcionamento_Fim, "HH:mm", CultureInfo.InvariantCulture);
                        analiseGrupo.DemandaResidual = medicaoConf.Funcionamento_DemMin;
                    }
                }
            }

            // lista grupo de medicoes de temperatura (EA)
            AnaliseHorarioConsGruposMedEAMetodos analiseGruposEAMetodos = new AnaliseHorarioConsGruposMedEAMetodos();
            List<AnaliseHorarioConsGruposMedEADominio> analiseGruposEA = new List<AnaliseHorarioConsGruposMedEADominio>();

            // le medicoes
            MedicoesMetodos medMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = new MedicoesDominio();

            // le unidades
            UnidadesMetodos unidMetodos = new UnidadesMetodos();
            UnidadesDominio unidade = new UnidadesDominio();

            if (analiseGrupos != null)
            {
                foreach (AnaliseHorarioConsGruposMedDominio analiseGrupo in analiseGrupos)
                {
                    // valores
                    AnaliseHorarioConsValor valor = new AnaliseHorarioConsValor();

                    // nome medicao 
                    medicao = medMetodos.ListarPorId(analiseGrupo.IDMedicao);
                    valor.NomeMedicao = medicao.Nome;

                    // nome unidade
                    unidade = unidMetodos.ListarPorId(IDCliente, medicao.IDUnidade);
                    valor.NomeUnidade = unidade.Nome;

                    // preenche deafult                    
                    valor.HoraIni = new DateTime(2000, 1, 1, 0, 0, 0);
                    valor.HoraFim = new DateTime(2000, 1, 1, 0, 0, 0);
                    valor.HoraIniRef = analiseGrupo.HoraIniRef;
                    valor.HoraFimRef = analiseGrupo.HoraFimRef;
                    valor.PercentualDemanda = 0.0;
                    valor.TemperaturaMedia = 0.0;
                    valor.DesperdicioConsumoP = 0.0;
                    valor.DesperdicioConsumoFP = 0.0;
                    valor.DesperdicioMonetizacao = 0.0;
                    valor.RegistrosTemp = false;
                    valor.StatusLiga = 0;
                    valor.StatusDesliga = 0;
                    valor.DemandaResidual = analiseGrupo.DemandaResidual;

                    // define data para que faça a leitura dos ultimos tres meses de registro da medicao
                    DateTime dataFim = Funcoes_Converte.ConverteDataHora2DateTime(datahora);
                    dataFim = new DateTime(dataFim.Year, dataFim.Month, dataFim.Day, 0, 0, 0).AddDays(+1);
                    DateTime dataIni = dataFim.AddMonths(-3);

                    // EN metodos
                    EN_Metodos ENMetodos = new EN_Metodos();

                    // demanda maxima do periodo de tres meses analisado
                    double demandaMaxima = 0.0;
                    demandaMaxima = ENMetodos.DemandaMaximaPeriodo(IDCliente, medicao.IDMedicao, dataIni, dataFim);

                    // media de demanda do periodo de referencia nos ultimos tres meses
                    double mediaRef = 0.0;
                    mediaRef = ENMetodos.DemandaMediaPeriodo(IDCliente, medicao.IDMedicao, dataIni, dataFim, string.Format("{0:t}", analiseGrupo.HoraIniRef), string.Format("{0:t}", analiseGrupo.HoraFimRef), false);

                    // 
                    // STATUS ANALISE
                    //

                    // le valores do dia para analise
                    List<EN_Dominio> listaRegistrosDia = ENMetodos.ListarTodosPeriodo(IDCliente, medicao.IDMedicao, dataFim.AddDays(-1).AddMinutes(+15), dataFim);

                    // verifica se tem registros no dia e se demanda maxima do periodo analisado nao retornou zerada
                    if (listaRegistrosDia != null && listaRegistrosDia.Count > 0 && demandaMaxima != 0)
                    {
                        //
                        // Residual
                        //

                        // verifica se deve calcular o residual pela analise
                        if (analiseGrupo.CalcResidualAuto)
                        {
                            // residual (10% da media de demanda no periodo de referência) => mediaRef * 0.1
                            valor.DemandaResidual = mediaRef * 0.1;
                        }

                        //
                        // Percentual de Demanda
                        //

                        // percentual de demanda => media de demanda do dia analisado / demanda maxima da analise de 3 meses
                        valor.PercentualDemanda = (listaRegistrosDia.Average(registro => registro.Ativo) / demandaMaxima) * 100;

                        //
                        // Horario Inicio
                        //

                        // percorre valores do dia
                        for (int i = 0; i < listaRegistrosDia.Count(); i++)
                        {

                            if (listaRegistrosDia[i].Ativo > valor.DemandaResidual)
                            {
                                valor.HoraIni = listaRegistrosDia[i].DataHora;

                                break;
                            }
                        }

                        //
                        // Horario Fim
                        //

                        // percorre valores do ultimo ao primeiro para encontrar hora fim 
                        for (int i = listaRegistrosDia.Count(); i > 0; i--)
                        {
                            if (listaRegistrosDia[i - 1].Ativo > valor.DemandaResidual)
                            {
                                valor.HoraFim = listaRegistrosDia[i - 1].DataHora;

                                break;
                            }
                        }

                        //
                        // Status
                        //

                        // verifica horario inicio, caso nao tenha encontrado (ano 2000), seta como OK pois nao houve consumo maior que residual
                        if ((valor.HoraIni.Hour * 60 + valor.HoraIni.Minute >= analiseGrupo.HoraIniRef.Hour * 60 + analiseGrupo.HoraIniRef.Minute) || (valor.HoraIni.Year == 2000))
                        {
                            // ok - medicao ligada no horario correto 
                            valor.StatusLiga = 1;
                        }

                        // verifica horario fim, caso nao tenha encontrado (ano 2000), seta como OK pois nao houve consumo maior que residual
                        // verifica tambem se esta no mesmo dia da hora inicial, confirmando que nao eh o ultimo registro do dia (00:00 do dia seguinte)
                        if ((valor.HoraFim.Hour * 60 + valor.HoraFim.Minute <= analiseGrupo.HoraFimRef.Hour * 60 + analiseGrupo.HoraFimRef.Minute || valor.HoraFim.Year == 2000) && (valor.HoraFim.Day == valor.HoraIni.Day))
                        {
                            // ok - medicao desligada no horario correto
                            valor.StatusDesliga = 1;
                        }

                        //
                        // Desperdicio
                        //

                        // percorre valores do dia
                        for (int i = 0; i < listaRegistrosDia.Count(); i++)
                        {
                            // verifica se fora do horário de funcionamento
                            if ( (listaRegistrosDia[i].DataHora.Hour * 60 + listaRegistrosDia[i].DataHora.Minute < analiseGrupo.HoraIniRef.Hour * 60 + analiseGrupo.HoraIniRef.Minute) ||
                                 (listaRegistrosDia[i].DataHora.Hour * 60 + listaRegistrosDia[i].DataHora.Minute > analiseGrupo.HoraFimRef.Hour * 60 + analiseGrupo.HoraFimRef.Minute) )
                            {
                                // verifica se demanda maior que residual
                                if (listaRegistrosDia[i].Ativo > valor.DemandaResidual)
                                {
                                    // verifica periodo
                                    if (listaRegistrosDia[i].Periodo == PERIODO.P)
                                    {
                                        valor.DesperdicioConsumoP += listaRegistrosDia[i].Ativo - valor.DemandaResidual;
                                    }
                                    else
                                    {
                                        valor.DesperdicioConsumoFP += listaRegistrosDia[i].Ativo - valor.DemandaResidual;
                                    }
                                }
                            }
                        }

                        // transforma em consumo
                        valor.DesperdicioConsumoP /= 4.0;
                        valor.DesperdicioConsumoFP /= 4.0;

                        // verifica se existe despedicio
                        if (valor.DesperdicioConsumoP > 0.0 || valor.DesperdicioConsumoFP > 0.0)
                        {
                            // lê tarifa de consumo
                            double Tarcp = 0.0;
                            double Tarcf = 0.0;

                            // tarifa azul
                            if (medicao.IDEstruturaTarifaria == TIPOESTR_TAR.THS_AZUL)
                            {
                                Tarifas_AZMetodos tarifas_AZMetodos = new Tarifas_AZMetodos();
                                Tarifas_AZDominio tarifa_azul = new Tarifas_AZDominio();
                                tarifa_azul = tarifas_AZMetodos.ListarMaisRecenteData(medicao.IDAgenteDistribuidora, medicao.IDTipoSubgrupo, DataAtual);

                                // verifica se encontrou tarifa
                                if (tarifa_azul != null)
                                {
                                    // lê tarifa de consumo
                                    Tarcp = tarifa_azul.Tarcp;
                                    Tarcf = tarifa_azul.Tarcf;
                                }
                            }

                            // tarifa verde 
                            if (medicao.IDEstruturaTarifaria == TIPOESTR_TAR.THS_VERDE)
                            {
                                Tarifas_VDMetodos tarifas_VDMetodos = new Tarifas_VDMetodos();
                                Tarifas_VDDominio tarifa_verde = new Tarifas_VDDominio();
                                tarifa_verde = tarifas_VDMetodos.ListarMaisRecenteData(medicao.IDAgenteDistribuidora, medicao.IDTipoSubgrupo, DataAtual);

                                // verifica se encontrou tarifa
                                if (tarifa_verde != null)
                                {
                                    // lê tarifa de consumo
                                    Tarcp = tarifa_verde.Tarcp;
                                    Tarcf = tarifa_verde.Tarcf;
                                }
                            }

                            // monetiza desperdicio
                            valor.DesperdicioMonetizacao = valor.DesperdicioConsumoFP * (Tarcf / 1000.0) + valor.DesperdicioConsumoP * (Tarcp / 1000.0);
                        }
                    }

                    // faltam registros para analise
                    else
                    {
                        valor.StatusLiga = 2;
                        valor.StatusDesliga = 2;
                    }

                    //
                    // MEDIÇÕES DE TEMPERATURA
                    //

                    // verifica se executou a análise corretamente
                    if (valor.StatusLiga != 2 && valor.StatusDesliga != 2)
                    {
                        // GG Metodos
                        GG_Metodos GGMetodos = new GG_Metodos();

                        // lista grupo de medicoes de EA
                        analiseGruposEA = analiseGruposEAMetodos.ListarPorIDAnaliseGruposMed(analiseGrupo.IDAnaliseHorarioConsGruposMed);

                        if (analiseGruposEA != null && analiseGruposEA.Count() > 0)
                        {
                            // soma registros para calculo da média
                            double somaRegistros = 0.0;

                            foreach (AnaliseHorarioConsGruposMedEADominio analiseGrupoEA in analiseGruposEA)
                            {
                                // listo registros da media de cada medicao no dia analisado
                                somaRegistros += GGMetodos.EA_MediaPeriodo(IDCliente, analiseGrupoEA.IDMedicao, dataFim.AddDays(-1).AddMinutes(+15), dataFim);
                            }

                            // calcula média dos registros das medicoes
                            valor.TemperaturaMedia = somaRegistros / analiseGruposEA.Count();

                            if (somaRegistros != 0.0)
                            {
                                // tem registros
                                valor.RegistrosTemp = true;
                            }
                        }
                    }

                    // copia para lista
                    valores_resultado.Add(valor);
                }

                // valores
                ViewBag.valores_resultado = valores_resultado;
            }

            // erros
            ViewBag.listaErros = listaErros;

            return;
        }

        // AnaliseHorarioCons XLS
        private HSSFWorkbook XLS_AnaliseHorarioCons()
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TOTAL e MEDICOES
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Análise");

            // cabecalho
            string[] cabecalho = { "Medições", "Unidades" , "Referência Início", "Referência Fim", "Início", "Fim", "Percentual Demanda (%)", "Residual (kW)", "Desperdício (R$)", "Liga", "Desliga", "Status", "Temperatura (°C)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // analise
            AnaliseHorarioConsDominio analise = ViewBag.Analise;

            // adiciona linhas
            List<AnaliseHorarioConsValor> valores_resultado = ViewBag.valores_resultado;

            int i;
            IRow row;

            // percorre valores
            if (valores_resultado != null)
            {
                foreach (AnaliseHorarioConsValor valor in valores_resultado)
                {
                    // status
                    string statusLiga = "Não OK";
                    string statusDesliga = "Não OK";
                    string statusAnalise = "Não OK";
                    string horaIni = string.Format("{0:t}", valor.HoraIni);
                    string horaFim = string.Format("{0:t}", valor.HoraFim);
                    string horaIniRef = string.Format("{0:t}", valor.HoraIniRef);
                    string horaFimRef = string.Format("{0:t}", valor.HoraFimRef);
                    string percentual = string.Format("{0:P2}", valor.PercentualDemanda / 100);
                    string residual = string.Format("{0:0.0}", valor.DemandaResidual);
                    string desperdicio = string.Format("{0:0.00}", valor.DesperdicioMonetizacao);
                    string tempMedia = string.Format("{0:0.0}", valor.TemperaturaMedia);

                    switch (valor.StatusLiga)
                    {
                        case 1:

                            // ok                            
                            statusLiga = "OK";

                            break;

                        case 2:

                            // sem registros
                            statusLiga = "Sem Registros";
                            statusAnalise = "Sem Registros";

                            break;
                    }

                    switch (valor.StatusDesliga)
                    {
                        case 1:

                            // ok                            
                            statusDesliga = "OK";

                            break;

                        case 2:

                            // sem registros
                            statusDesliga = "Sem Registros";
                            statusAnalise = "Sem Registros";
                            horaIni = "---";
                            horaFim = "---";
                            horaIniRef = "---";
                            horaFimRef = "---";
                            percentual = "---";
                            residual = "---";
                            tempMedia = "---";

                            break;
                    }

                    if (valor.StatusLiga == 1 && valor.StatusDesliga == 1)
                    {
                        statusAnalise = "OK";
                    }

                    if (valor.HoraIni == valor.HoraFim)
                    {
                        horaIni = "---";
                        horaFim = "---";
                    }

                    // verifica se tem registros de temperatura
                    if (!valor.RegistrosTemp)
                    {
                        tempMedia = "---";
                    }

                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // medicao
                    textoCelulaXLS(row, 0, valor.NomeMedicao);
                    textoCelulaXLS(row, 1, valor.NomeUnidade);

                    // horarios
                    textoCelulaXLS(row, 2, horaIniRef);
                    textoCelulaXLS(row, 3, horaFimRef);
                    textoCelulaXLS(row, 4, horaIni);
                    textoCelulaXLS(row, 5, horaFim);

                    // valores
                    textoCelulaXLS(row, 6, percentual);
                    textoCelulaXLS(row, 7, residual);
                    textoCelulaXLS(row, 8, desperdicio);

                    // status 
                    textoCelulaXLS(row, 9, statusLiga);
                    textoCelulaXLS(row, 10, statusDesliga);
                    textoCelulaXLS(row, 11, statusAnalise);

                    // temperatura
                    textoCelulaXLS(row, 12, tempMedia);

                    // proxima
                    rowIndex++;
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS_AnaliseHorarioCons(workbook, sheet, analise.Nome, _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }
    }
}