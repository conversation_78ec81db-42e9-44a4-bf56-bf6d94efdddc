﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class AnalisesController
    {

        //
        // ANALISE RAMO DE ATIVIDADE
        //

        // GET: Analise Ramo de Atividade
        public ActionResult Analise_RamoAtividade()
        {
            // tela de ajuda - ramo de atividade
            CookieStore.SalvaCookie_String("PaginaAjuda", "RamoAtividade");

            // le cookies
            LeCookies_SmartEnergy();

            // data atual
            DateTime data_atual = DateTime.Now;
            DateTime data_referencia = data_atual.AddMonths(-1);

            // le cookie datahora atual
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            if( datahora_cookie.Year != 2000 )
            {
                data_atual = datahora_cookie;
            }

            // le cookie datahora referencia
            datahora_cookie = CookieStore.LeCookie_Datahora("Relat_DataFim");

            if (datahora_cookie.Year != 2000)
            {
                data_referencia = datahora_cookie;
            }

            // inverte
            if (data_atual < data_referencia)
            {
                DateTime data_temp = data_atual;
                data_atual = data_referencia;
                data_referencia = data_temp;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", data_atual);
            CookieStore.SalvaCookie_Datahora("Relat_DataFim", data_referencia);

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", data_atual);
            ViewBag.DataAtualN = data_atual;
            ViewBag.DataTextoAtual = string.Format("{0:MM/yyyy}", data_atual);

            // data referencia
            ViewBag.DataReferencia = string.Format("{0:MM/yyyy}", data_referencia);
            ViewBag.DataReferenciaN = data_referencia;
            ViewBag.DataTextoReferencia = string.Format("{0:MM/yyyy}", data_referencia);


            // le tipos RamoAtividade
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposRamoAtividade = listatiposMetodos.ListarTodos("TipoRamoAtividade");

            // analise
            List<AnaliseRamoAtividade_Total> ramosAtividades = new List<AnaliseRamoAtividade_Total>();

            if (listatiposRamoAtividade != null)
            {
                // percorre ramos de atividade
                foreach (ListaTiposDominio tipoRamoAtividade in listatiposRamoAtividade)
                {
                    AnaliseRamoAtividade_Total ramo = new AnaliseRamoAtividade_Total();

                    ramo.IDRamoAtividade = tipoRamoAtividade.ID;
                    ramo.Descricao = tipoRamoAtividade.Descricao;
                    ramo.ConsumoMesAnterior = 0.0;
                    ramo.ConsumoMesAtual = 0.0;
                    ramo.Diferenca = 0.0;
                    ramo.NumClientes = 0;
                    ramo.NumMedicoes = 0;

                    // insere na lista
                    ramosAtividades.Add(ramo);
                }
            }

            ViewBag.ramosAtividades = ramosAtividades;

            return View();
        }

        // GET: Analise Ramo de Atividade
        public PartialViewResult _Analise_RamoAtividade_Atualizar(int Navegacao, string DataAtual, string DataReferencia)
        {
            // calcula
            Analise_RamoAtividade_Show(Navegacao, DataAtual, DataReferencia);

            return PartialView();
        }

        // GET: Analise Ramo de Atividade Print
        public ActionResult Analise_RamoAtividade_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // GUID
            Guid id = ViewBag.taskID;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Analise_RamoAtividade_Show(0, data_str, data_str);

            // resultado
            ViewBag.ramosAtividades = analise_RamoAtividade.Keys.Contains(id) ? analise_RamoAtividade[id] : null;

            // imprime
            return View();
        }

        // GET: Analise Ramo de Atividade EMAIL
        public async Task<ActionResult> Analise_RamoAtividade_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // GUID
            Guid id = ViewBag.taskID;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Analise_RamoAtividade_Show(0, data_str, data_str);

            // resultado
            ViewBag.ramosAtividades = analise_RamoAtividade.Keys.Contains(id) ? analise_RamoAtividade[id] : null;

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Analise_RamoAtividade_{0:yyyyMMddHHmm}.pdf", data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_Analise_RamoAtividade_Calculo_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "AnaliseRamoAtividadeEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Analise Ramo de Atividade PDF
        public ActionResult Analise_RamoAtividade_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // GUID
            Guid id = ViewBag.taskID;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Analise_RamoAtividade_Show(0, data_str, data_str);

            // resultado
            ViewBag.ramosAtividades = analise_RamoAtividade.Keys.Contains(id) ? analise_RamoAtividade[id] : null;

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Analise_RamoAtividade_{0:yyyyMMddHHmm}.pdf", data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_Analise_RamoAtividade_Calculo_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Analise Ramo de Atividade XLS
        public ActionResult Analise_RamoAtividade_XLS()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // GUID
            Guid id = ViewBag.taskID;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Analise_RamoAtividade_Show(0, data_str, data_str);

            // resultado
            ViewBag.ramosAtividades = analise_RamoAtividade.Keys.Contains(id) ? analise_RamoAtividade[id] : null;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_Analise_RamoAtividade();

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Analise_RamoAtividade_{0:yyyyMMddHHmm}.xls", data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // Analise Ramo de Atividade XLS
        private HSSFWorkbook XLS_Analise_RamoAtividade()
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 14);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // ANALISE RAMO DE ATIVIDADE
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Análise Ramo de Atividade");

            // cabecalho
            string[] cabecalho = { "Ramo de Atividade", "Consumo Atual (MWh)", "Consumo Referência (MWh)", "Porcentagem (%)", "Clientes", "Medições" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            List<AnaliseRamoAtividade_Total> ramosAtividades = ViewBag.ramosAtividades;
            int i;
            IRow row;

            // percorre valores
            if (ramosAtividades != null)
            {
                foreach (AnaliseRamoAtividade_Total ramo in ramosAtividades)
                {
                    if (ramo.ConsumoMesAtual > 0.0 || ramo.ConsumoMesAnterior > 0.0)
                    {
                        // adiciona linha
                        row = sheet.CreateRow(rowIndex);

                        // ramo de atividade
                        textoCelulaXLS(row, 0, ramo.Descricao);

                        // consumo atual
                        numeroCelulaXLS(row, 1, ramo.ConsumoMesAtual / 1000.0, _1CellStyle);

                        // consumo referencia
                        numeroCelulaXLS(row, 2, ramo.ConsumoMesAnterior / 1000.0, _1CellStyle);

                        // porcentagem
                        numeroCelulaXLS(row, 3, ramo.Porcentagem, _1CellStyle);

                        // clientes
                        numeroCelulaXLS(row, 4, ramo.NumClientes, _intCellStyle);

                        // medições
                        numeroCelulaXLS(row, 5, ramo.NumMedicoes, _intCellStyle);

                        // proxima
                        rowIndex++;
                    }
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS_RamoAtividade(workbook, sheet, "Ramo de Atividade", _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }

        // GET: Analise Ramo de Atividade XLS Download
        [HttpGet]
        public virtual ActionResult Analise_RamoAtividade_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }



        // GET: Analise Ramo de Atividade Show
        public void Analise_RamoAtividade_Show(int Navegacao, string DataAtual, string DataReferencia)
        {
            // caso forcar data
            if (Navegacao == 100)
            {
                // data atual
                DateTime dateValue = DateTime.Parse(DataAtual);
                CookieStore.SalvaCookie_Datahora("Relat_Data", dateValue);

                // data referencia
                dateValue = DateTime.Parse(DataReferencia);
                CookieStore.SalvaCookie_Datahora("Relat_DataFim", dateValue);
            }

            // le cookies
            LeCookies_SmartEnergy();

            // le cookie datahora atual
            DateTime data_atual = CookieStore.LeCookie_Datahora("Relat_Data");
            DateTime data_referencia = CookieStore.LeCookie_Datahora("Relat_DataFim");

            // inverte
            if (data_atual < data_referencia)
            {
                DateTime data_temp = data_atual;
                data_atual = data_referencia;
                data_referencia = data_temp;
            }

            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -3:    // mes anterior

                    data_atual = data_atual.AddMonths(-1);
                    data_referencia = data_referencia.AddMonths(-1);
                    break;

                case 3:    // mes seguinte

                    data_atual = data_atual.AddMonths(1);
                    data_referencia = data_referencia.AddMonths(1);
                    break;

                case -4:    // ano anterior

                    data_atual = data_atual.AddYears(-1);
                    data_referencia = data_referencia.AddYears(-1);
                    break;

                case 4:    // ano seguinte

                    data_atual = data_atual.AddYears(1);
                    data_referencia = data_referencia.AddYears(1);
                    break;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", data_atual);
            CookieStore.SalvaCookie_Datahora("Relat_DataFim", data_referencia);

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", data_atual);
            ViewBag.DataAtualN = data_atual;
            ViewBag.DataTextoAtual = string.Format("{0:MM/yyyy}", data_atual);

            // data referencia
            ViewBag.DataReferencia = string.Format("{0:MM/yyyy}", data_referencia);
            ViewBag.DataReferenciaN = data_referencia;
            ViewBag.DataTextoReferencia = string.Format("{0:MM/yyyy}", data_referencia);

            return;
        }


        // estruturas
        private static IDictionary<Guid, int> tasks = new Dictionary<Guid, int>();
        private List<AnaliseRamoAtividade_Total> analise_RamoAtividade_tmp = new List<AnaliseRamoAtividade_Total>();
        private static IDictionary<Guid, List<AnaliseRamoAtividade_Total>> analise_RamoAtividade = new Dictionary<Guid, List<AnaliseRamoAtividade_Total>>();

        public ActionResult Analise_RamoAtividade_IniciaCalculo()
        {
            // taskID
            Guid taskId = Guid.NewGuid();
            tasks.Add(taskId, 0);

            // salva cookie
            CookieStore.SalvaCookie_GUID("taskID", taskId);

            // le cookies
            LeCookies_SmartEnergy();

            // le cookie datahora atual
            DateTime data_atual = CookieStore.LeCookie_Datahora("Relat_Data");
            DateTime data_referencia = CookieStore.LeCookie_Datahora("Relat_DataFim");

            // inverte
            if (data_atual < data_referencia)
            {
                DateTime data_temp = data_atual;
                data_atual = data_referencia;
                data_referencia = data_temp;

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Relat_Data", data_atual);
                CookieStore.SalvaCookie_Datahora("Relat_DataFim", data_referencia);
            }

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", data_atual);
            ViewBag.DataAtualN = data_atual;
            ViewBag.DataTextoAtual = string.Format("{0:MM/yyyy}", data_atual);

            // data referencia
            ViewBag.DataReferencia = string.Format("{0:MM/yyyy}", data_referencia);
            ViewBag.DataReferenciaN = data_referencia;
            ViewBag.DataTextoReferencia = string.Format("{0:MM/yyyy}", data_referencia);

            // le clientes
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            List<ClientesDominio> clientes = clientesMetodos.ListarTodos();

            // le tipos RamoAtividade
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposRamoAtividade = listatiposMetodos.ListarTodos("TipoRamoAtividade");

            // limpa estrutura
            analise_RamoAtividade_tmp.Clear();


            // task
            Task.Factory.StartNew(() =>
            {
                // barra de progresso
                int total = 0;
                int atual = 0;

                // percorre ramos de atividade
                if (listatiposRamoAtividade != null)
                {
                    // barra de progresso
                    total = listatiposRamoAtividade.Count;

                    // percorre ramos de atividade
                    foreach (ListaTiposDominio tipoRamoAtividade in listatiposRamoAtividade)
                    {
                        // update task progress
                        double progresso = ((double)atual / (double)total) * 100;
                        tasks[taskId] = (int)progresso;
                        atual++;

                        // ramo de atividade
                        AnaliseRamoAtividade_Total ramo = new AnaliseRamoAtividade_Total();

                        ramo.IDRamoAtividade = tipoRamoAtividade.ID;
                        ramo.Descricao = tipoRamoAtividade.Descricao;
                        ramo.ConsumoMesAnterior = 0.0;
                        ramo.ConsumoMesAtual = 0.0;
                        ramo.Diferenca = 0.0;
                        ramo.NumClientes = 0;
                        ramo.NumMedicoes = 0;

                        // Calcula Analise Ramo de Atividade
                        Calc_Analise_RamoAtividade(clientes, ref ramo, data_atual, data_referencia);

                        // coloca na lista
                        analise_RamoAtividade_tmp.Add(ramo);
                    }
                }

                // coloca resultado
                analise_RamoAtividade.Add(taskId, new List<AnaliseRamoAtividade_Total>(analise_RamoAtividade_tmp));

                // terminou
                tasks.Remove(taskId);
            });

            // retorna status
            return Json(taskId, JsonRequestBehavior.AllowGet);
        }

        public ActionResult Analise_RamoAtividade_Progress(Guid id)
        {
            return Json(tasks.Keys.Contains(id) ? tasks[id] : 100, JsonRequestBehavior.AllowGet);
        }

        public PartialViewResult _Analise_RamoAtividade_Calculo(Guid id)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // resultado
            ViewBag.ramosAtividades = analise_RamoAtividade.Keys.Contains(id) ? analise_RamoAtividade[id] : null;


            // le cookie datahora atual
            DateTime data_atual = CookieStore.LeCookie_Datahora("Relat_Data");
            DateTime data_referencia = CookieStore.LeCookie_Datahora("Relat_DataFim");

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", data_atual);
            ViewBag.DataAtualN = data_atual;
            ViewBag.DataTextoAtual = string.Format("{0:MM/yyyy}", data_atual);

            // data referencia
            ViewBag.DataReferencia = string.Format("{0:MM/yyyy}", data_referencia);
            ViewBag.DataReferenciaN = data_referencia;
            ViewBag.DataTextoReferencia = string.Format("{0:MM/yyyy}", data_referencia);

            return PartialView();
        }


        // Calcula Analise Ramo de Atividade
        private void Calc_Analise_RamoAtividade(List<ClientesDominio> clientes, ref AnaliseRamoAtividade_Total ramo, DateTime data_atual, DateTime data_referencia)
        {
            if (clientes == null)
            {
                return;
            }

            // hoje
            DateTime hoje = DateTime.Now;

            // Data Atual
            DateTime DataAtualIni = new DateTime(data_atual.Year, data_atual.Month, 1, 0, 15, 0);
            DateTime DataAtualFim = DataAtualIni.AddMonths(1);
            DataAtualFim = DataAtualFim.AddMinutes(-15);

            // verifica se é o mês/ano atual
            if (DataAtualIni.Month == hoje.Month && DataAtualIni.Year == hoje.Year)
            {
                DataAtualFim = new DateTime(data_atual.Year, data_atual.Month, hoje.Day, 0, 0, 0);
            }

            // numero de dias
            TimeSpan diferenca = DataAtualFim - DataAtualIni;
            int num_dias = diferenca.Days + 1;

            // Data Referencia
            DateTime DataReferenciaIni = new DateTime(data_referencia.Year, data_referencia.Month, 1, 0, 15, 0);
            DateTime DataReferencialFim = DataReferenciaIni.AddDays(num_dias);
            DataReferencialFim = DataReferencialFim.AddMinutes(-15);


            // zera 
            ramo.ConsumoMesAtual = 0.0;
            ramo.ConsumoMesAnterior = 0.0;
            ramo.Diferenca = 0.0;
            ramo.Porcentagem = 0.0;
            ramo.NumClientes = 0;
            ramo.NumMedicoes = 0;

            // percorre clientes
            foreach (ClientesDominio cliente in clientes)
            {
                // verifica se cliente é do ramo de atividade
                if (cliente.IDRamoAtividade != ramo.IDRamoAtividade)
                {
                    // não é do ramo, ignora
                    continue;
                }

                // consumos
                double ConsumoMesAtual_Cliente = 0.0;
                double ConsumoMesAnterior_Cliente = 0.0;

                // lê medições principais
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                List<MedicoesDominio> medicoes = medicoesMetodos.ListarPrincipais(cliente.IDCliente);

                if (medicoes == null)
                {
                    // não tem medições principais, ignora
                    continue;
                }

                // percorre medições
                foreach (MedicoesDominio medicao in medicoes)
                {
                    // le consumo do mês atual
                    EN_Metodos enMetodos = new EN_Metodos();
                    double ConsumoMesAtual_Medicao = enMetodos.ConsumoTotalPeriodo(cliente.IDCliente, medicao.IDMedicao, DataAtualIni, DataAtualFim);

                    // le consumo do mês referência
                    double ConsumoMesAnterior_Medicao = enMetodos.ConsumoTotalPeriodo(cliente.IDCliente, medicao.IDMedicao, DataReferenciaIni, DataReferencialFim);

                    // verifica se teve consumo no cliente
                    if (ConsumoMesAtual_Medicao > 0.0 && ConsumoMesAnterior_Medicao > 0.0)
                    {
                        // incrementa numero de medições
                        ramo.NumMedicoes++;

                        // consumos
                        ConsumoMesAtual_Cliente += ConsumoMesAtual_Medicao;
                        ConsumoMesAnterior_Cliente += ConsumoMesAnterior_Medicao;

                        ramo.ConsumoMesAtual += ConsumoMesAtual_Medicao;
                        ramo.ConsumoMesAnterior += ConsumoMesAnterior_Medicao;

                        // calcula diferença
                        ramo.Diferenca = ramo.ConsumoMesAtual - ramo.ConsumoMesAnterior;

                        // porcentagem
                        if (ramo.ConsumoMesAnterior > 0.0)
                        {
                            ramo.Porcentagem = (ramo.Diferenca / ramo.ConsumoMesAnterior) * 100.0;
                        }
                    }
                }

                // verifica se teve consumo no cliente
                if (ConsumoMesAtual_Cliente > 0.0 && ConsumoMesAnterior_Cliente > 0.0)
                {
                    // incrementa numero de clientes
                    ramo.NumClientes++;
                }
            }

            return;
        }
    }
}