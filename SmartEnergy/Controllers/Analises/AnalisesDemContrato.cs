﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Web.Hosting;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class AnalisesController
    {
        // Demanda de Contrato
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_DemandaIdeal", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_DemandaIdeal(char tipo_interface, ref CONFIG_INTERFACE pcfg_interface, ref DATAHORA pdatahora, ref DEMANDA_IDEAL pdem_ideal);

        //
        // DEMANDA DE CONTRATO IDEAL
        //

        // GET: Demanda de Contrato Ideal
        public ActionResult Dem_Contrato_Ideal(int IDCliente, int IDMedicao)
        {
            // tela de ajuda - demanda de contrato ideal
            CookieStore.SalvaCookie_String("PaginaAjuda", "Analise_DemandaIdeal");

            // le supervisao da medicao
            SupervMedicoesMetodos supmedicoesMetodos = new SupervMedicoesMetodos();
            var listaMedicoes = supmedicoesMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

            // salva cookie 
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", listaMedicoes.IDTipoMedicao_MD);
            CookieStore.SalvaCookie_String("MedicaoNome", listaMedicoes.NMED);
            CookieStore.SalvaCookie_String("UnidadeNome", listaMedicoes.NUN);
            CookieStore.SalvaCookie_String("GrupoNome", listaMedicoes.NGU);
            CookieStore.SalvaCookie_Int("_IDGateway", listaMedicoes.IDGW_GW);
            CookieStore.SalvaCookie_Int("IDTipoGateway", listaMedicoes.IDTipoGateway_GW);
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Dem_Contrato_Ideal");

            // le cookies
            LeCookies_SmartEnergy();

            // data atual
            DateTime datahora_ultima = DateTime.Now;
            DATAHORA data_hora = new DATAHORA();

            // le cookie datahora
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            if( datahora_cookie.Year != 2000 )
            {
                datahora_ultima = datahora_cookie;
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_ultima);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", datahora_ultima);
            ViewBag.DataAtualN = datahora_ultima;
            ViewBag.DataTextoAtualFim = string.Format("{0:MM/yyyy}", datahora_ultima);

            ViewBag.DataIni = string.Format("{0:MM/yyyy}", datahora_ultima.AddMonths(-11));
            ViewBag.DataIniN = datahora_ultima.AddMonths(-11);
            ViewBag.DataTextoAtualIni = string.Format("{0:MM/yyyy}", datahora_ultima.AddMonths(-11));

            // grafico
            var Demanda = new double[10];
            var Valores = new double[10];

            int NumValores = 0;

            double Valor_min_grafico = 0.0;
            double Valor_max_grafico = 0.0;

            ViewBag.ValorMinGrafico = Valor_min_grafico;
            ViewBag.ValorMaxGrafico = Valor_max_grafico;

            ViewBag.Demanda = Demanda;
            ViewBag.DemIdealP = Demanda[0];
            ViewBag.DemIdealFP = Demanda[0];
            ViewBag.Valores = Valores;
            ViewBag.NumValores = NumValores;

            // valores
            DEMANDA_IDEAL_RESULT dem_ideal_resultado = new DEMANDA_IDEAL_RESULT();
            ViewBag.dem_ideal_resultado = dem_ideal_resultado;

            // lista de erros
            var listaErros = new List<string>();

            // erros
            ViewBag.listaErros = listaErros;

            return View();
        }

        // GET: Demanda de Contrato Ideal
        public PartialViewResult _Dem_Contrato_Ideal_Atualizar(int Navegacao, string Data)
        {
            // calcula
            Dem_Contrato_Ideal_Show(Navegacao, Data);

            return PartialView();
        }

        // GET: Demanda de Contrato Ideal Print
        public ActionResult Dem_Contrato_Ideal_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDRanking
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Dem_Contrato_Ideal_Show(0, data_str);

            // imprime
            return View();
        }

        public static async Task<string> EMailTemplate(string template)
        {
            var templateFilePath = HostingEnvironment.MapPath("~/Content/emailTemplates/") + template + ".cshtml";
            StreamReader objstreamreaderfile = new StreamReader(templateFilePath);
            var body = await objstreamreaderfile.ReadToEndAsync();
            objstreamreaderfile.Close();
            return body;
        }

        // GET: Demanda de Contrato Ideal EMAIL
        public async Task<ActionResult> Dem_Contrato_Ideal_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Dem_Contrato_Ideal_Show(0, data_str);

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Dem_Ideal_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDMedicao, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            DEMANDA_IDEAL_RESULT dem_ideal_resultado = ViewBag.dem_ideal_resultado;

            string viewPartial = "_Dem_Contrato_Ideal_Azul_PDF";

            switch (dem_ideal_resultado.TipoFatura)
            {
                case 0:
                    viewPartial = "_Dem_Contrato_Ideal_Azul_PDF";
                    break;

                case 1:
                case 2:
                    viewPartial = "_Dem_Contrato_Ideal_Verde_PDF";
                    break;
            }

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "DemContratoIdealEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtualIni", ViewBag.DataTextoAtualIni);
            message = message.Replace("ViewBag.DataTextoAtualFim", ViewBag.DataTextoAtualFim);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.GrupoNome", ViewBag.GrupoNome);
            message = message.Replace("ViewBag.UnidadeNome", ViewBag.UnidadeNome);
            message = message.Replace("ViewBag.MedicaoNome", ViewBag.MedicaoNome);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Demanda de Contrato Ideal PDF
        public ActionResult Dem_Contrato_Ideal_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Dem_Contrato_Ideal_Show(0, data_str);

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Dem_Ideal_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDMedicao, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            DEMANDA_IDEAL_RESULT dem_ideal_resultado = ViewBag.dem_ideal_resultado;

            string viewPartial = "_Dem_Contrato_Ideal_Azul_PDF";

            switch (dem_ideal_resultado.TipoFatura)
            {
                case 0:
                    viewPartial = "_Dem_Contrato_Ideal_Azul_PDF";
                    break;

                case 1:
                case 2:
                    viewPartial = "_Dem_Contrato_Ideal_Verde_PDF";
                    break;
            }

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Demanda de Contrato Ideal XLS
        public ActionResult Dem_Contrato_Ideal_XLS()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Dem_Contrato_Ideal_Show(0, data_str);

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_Dem_Contrato_Ideal(IDCliente, IDMedicao);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Dem_Ideal_{0:000000}_{1:yyyyMMddHHmm}.xls", IDMedicao, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Demanda de Contrato Ideal XLS Download
        [HttpGet]
        public virtual ActionResult Dem_Contrato_Ideal_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Demanda de Contrato Ideal Show
        public void Dem_Contrato_Ideal_Show(int Navegacao, string Data)
        {
            // caso forcar data
            if (Navegacao == 100)
            {
                // pega data
                DateTime dateValue = DateTime.Parse(Data);

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Relat_Data", dateValue);
            }

            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // le supervisao da medicao
            var medicoesMetodos = new SupervMedicoesMetodos();
            var listaMedicoes = medicoesMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

            // salva cookie 
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", listaMedicoes.IDTipoMedicao_MD);
            CookieStore.SalvaCookie_String("MedicaoNome", listaMedicoes.NMED);
            CookieStore.SalvaCookie_String("UnidadeNome", listaMedicoes.NUN);
            CookieStore.SalvaCookie_String("GrupoNome", listaMedicoes.NGU);
            CookieStore.SalvaCookie_Int("_IDGateway", listaMedicoes.IDGW_GW);
            CookieStore.SalvaCookie_Int("IDTipoGateway", listaMedicoes.IDTipoGateway_GW);

            // le cookie datahora
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");
            DATAHORA data_hora = new DATAHORA();

            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -3:    // mes anterior

                    datahora_cookie = datahora_cookie.AddMonths(-1);
                    break;

                case 3:    // mes seguinte

                    datahora_cookie = datahora_cookie.AddMonths(1);
                    break;

                case -4:    // ano anterior

                    datahora_cookie = datahora_cookie.AddYears(-1);
                    break;

                case 4:    // ano seguinte

                    datahora_cookie = datahora_cookie.AddYears(1);
                    break;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_cookie);

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_cookie);

            // calcula
            Calc_Dem_Contrato_Ideal(data_hora);

            return;
        }

        // Calcula Demanda de Contrato Ideal
        private void Calc_Dem_Contrato_Ideal(DATAHORA datahora)
        {
            // retorno
            int retorno = 0;

            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // medicao
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);

            // estruturas
            DEMANDA_IDEAL_RESULT dem_ideal_resultado = new DEMANDA_IDEAL_RESULT();
            dem_ideal_resultado.IDMedicao = IDMedicao;

            // lista de erros
            var listaErros = new List<string>();

            // converte data e hora
            DateTime DataAtual = Funcoes_Converte.ConverteDataHora2DateTime(datahora);
            DateTime DataAtualNova = DataAtual.AddMonths(-11);

            DATAHORA datahora_nova = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref datahora_nova, DataAtualNova);

            if( medicao != null )
            {
                // preenche solicitacao
                CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
                config_interface.sweb.id_cliente = IDCliente;
                config_interface.sweb.id_medicao = IDMedicao;
                config_interface.sweb.id_gateway = 0;

                // nome medicao
                dem_ideal_resultado.Nome_Medicao = medicao.Nome;
                dem_ideal_resultado.TipoFatura = medicao.IDEstruturaTarifaria;

                // resultado
                DEMANDA_IDEAL dem_ideal = new DEMANDA_IDEAL();

                // calcula demanda ideal
                retorno = SmCalcDB_Energia_DemandaIdeal((char)0, ref config_interface, ref datahora_nova, ref dem_ideal);

                if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
                {
                    if (retorno > 0)
                        listaErros.Add(string.Format("Medição {0} com Retorno {1}", medicao.Nome, retorno));
                }

                // copia
                dem_ideal_resultado.dem_ideal = dem_ideal;
            }

            // economia total
            dem_ideal_resultado.Economia_Total = 0.0;

            for (int i = 0; i < 12; i++)
            {
                dem_ideal_resultado.Economia_Total += dem_ideal_resultado.dem_ideal.Economia[i];
            }

            // grafico
            var Demanda = new double[12];
            var Valores = new double[12];

            int NumValores = 0;

            double Valor_min_grafico = 0.0;
            double Valor_max_grafico = 0.0;

            for (int i = 0; i < 12; i++)
            {
                Demanda[i] = 0.0;
                Valores[i] = 0.0;

                // verifica se tem valor
                if( dem_ideal_resultado.dem_ideal.DemP_Atual != null )
                {
                    // verifica se tem valor
                    if( dem_ideal_resultado.dem_ideal.Demanda[i] < 0.0)
                    {
                        // encontrou valor default do final da lista
                        break;
                    }

                    // incrementa numero de valores
                    NumValores++;

                    // copia
                    Demanda[i] = dem_ideal_resultado.dem_ideal.Demanda[i];
                    Valores[i] = dem_ideal_resultado.dem_ideal.Valor[i];

                    if(i==0)
                    {
                        Valor_min_grafico = Valores[i];
                        Valor_max_grafico = Valores[i];
                    }

                    // verifica minimo
                    if (Valores[i] < Valor_min_grafico)
                    {
                        Valor_min_grafico = Valores[i];
                    }

                    // verifica maximo
                    if( Valores[i] > Valor_max_grafico )
                    {
                        Valor_max_grafico = Valores[i];
                    }
                }
            }

            // protege se nao tiver ideal
            if( Demanda[0] == 0.0 )
            {
                NumValores = 0;
                Valor_min_grafico = 0.0;
                Valor_max_grafico = 0.0;
            }

            Valor_max_grafico = Valor_max_grafico * 1.01;

            if (Valor_max_grafico == 0.0)
            {
                Valor_max_grafico = 10.0;
            }

            if (Valor_min_grafico > 0.0)
                Valor_min_grafico = Valor_min_grafico - (Valor_min_grafico * 0.01);
            else
                Valor_min_grafico = Valor_min_grafico * 1.01;

            ViewBag.ValorMinGrafico = Valor_min_grafico;
            ViewBag.ValorMaxGrafico = Valor_max_grafico;

            ViewBag.Demanda = Demanda;
            ViewBag.DemIdealP = dem_ideal_resultado.dem_ideal.DemP_Ideal;
            ViewBag.DemIdealFP = dem_ideal_resultado.dem_ideal.DemFP_Ideal;
            ViewBag.Valores = Valores;
            ViewBag.NumValores = NumValores;

            // valores
            ViewBag.dem_ideal_resultado = dem_ideal_resultado;

            // erros
            ViewBag.listaErros = listaErros;

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtualFim = string.Format("{0:MM/yyyy}", DataAtual);

            ViewBag.DataIni = string.Format("{0:MM/yyyy}", DataAtualNova);
            ViewBag.DataIniN = DataAtualNova;
            ViewBag.DataTextoAtualIni = string.Format("{0:MM/yyyy}", DataAtualNova);

            return;
        }

        // Demanda de Contrato Ideal XLS
        private HSSFWorkbook XLS_Dem_Contrato_Ideal(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 14);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // DEMANDA IDEAL e MESES
            //

            // resultado
            DEMANDA_IDEAL_RESULT dem_ideal_resultado = ViewBag.dem_ideal_resultado;

            // cria planilha
            var sheet = workbook.CreateSheet("Demanda Ideal");

            // variaveis
            IRow row;
            int rowIndex = 0;
            int i;

            // mes
            DateTime mes = ViewBag.DataIniN;

            // verifica tipo faturamento
            switch (dem_ideal_resultado.TipoFatura)
            {
                case 0:

                    // contrato ideal ponta
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, "Contrato Sugerido Ponta (kW)", _negritoCellStyle);
                    numeroCelulaXLS(row, 1, dem_ideal_resultado.dem_ideal.DemP_Ideal, _1CellStyle);

                    // contrato ideal fora de ponta
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, "Contrato Sugerido Fora Ponta (kW)", _negritoCellStyle);
                    numeroCelulaXLS(row, 1, dem_ideal_resultado.dem_ideal.DemFP_Ideal, _1CellStyle);

                    // economia media mensal
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, "Economia Média Mensal (R$)", _negritoCellStyle);
                    numeroCelulaXLS(row, 1, (dem_ideal_resultado.dem_ideal.ValorMedio_Atual - dem_ideal_resultado.dem_ideal.ValorMedio_Ideal), _2CellStyle);

                    // economia no periodo
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, "Economia no Período (R$)", _negritoCellStyle);
                    numeroCelulaXLS(row, 1, dem_ideal_resultado.Economia_Total, _2CellStyle);

                    // pula linha
                    row = sheet.CreateRow(rowIndex++);

                    // cabecalho
                    string[] cabecalhoAzul = { "Meses", "Contrato Ponta (kW)", "Contrato Fora Ponta (kW)", "Fatura (R$)", "Contrato Sugerido Ponta (kW)", "Contrato Sugerido Fora Ponta (kW)", "Fatura (R$)", "Economia (R$" };

                    // adiciona cabecalho
                    cabecalhoTabelaXLS(workbook, sheet, cabecalhoAzul, rowIndex++);

                    // meses
                    for (i = 0; i < 12; i++)
                    {
                        // adiciona linha
                        row = sheet.CreateRow(rowIndex++);

                        // mes
                        datahoraCelulaXLS(row, 0, mes, _dataStyle);

                        // proximo mes
                        mes = mes.AddMonths(1);

                        // contrato ponta
                        numeroCelulaXLS(row, 1, dem_ideal_resultado.dem_ideal.DemP_Atual[i], _1CellStyle);

                        // contrato fora de ponta
                        numeroCelulaXLS(row, 2, dem_ideal_resultado.dem_ideal.DemFP_Atual[i], _1CellStyle);

                        // fatura
                        numeroCelulaXLS(row, 3, dem_ideal_resultado.dem_ideal.Valor_Atual[i], _2CellStyle);

                        // contrato ideal ponta
                        numeroCelulaXLS(row, 4, dem_ideal_resultado.dem_ideal.DemP_Ideal, _1CellStyle);

                        // contrato ideal fora de ponta
                        numeroCelulaXLS(row, 5, dem_ideal_resultado.dem_ideal.DemFP_Ideal, _1CellStyle);

                        // fatura ideal
                        numeroCelulaXLS(row, 6, dem_ideal_resultado.dem_ideal.Valor_Ideal[i], _2CellStyle);

                        // economia
                        numeroCelulaXLS(row, 7, dem_ideal_resultado.dem_ideal.Economia[i], _2CellStyle);
                    }

                    break;

                case 1:
                case 2:

                    // contrato ideal ponta
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, "Contrato Sugerido (kW)", _negritoCellStyle);
                    numeroCelulaXLS(row, 1, dem_ideal_resultado.dem_ideal.DemP_Ideal, _1CellStyle);

                    // economia media mensal
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, "Economia Média Mensal (R$)", _negritoCellStyle);
                    numeroCelulaXLS(row, 1, (dem_ideal_resultado.dem_ideal.ValorMedio_Atual - dem_ideal_resultado.dem_ideal.ValorMedio_Ideal), _2CellStyle);

                    // economia no periodo
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, "Economia no Período (R$)", _negritoCellStyle);
                    numeroCelulaXLS(row, 1, dem_ideal_resultado.Economia_Total, _2CellStyle);

                    // pula linha
                    row = sheet.CreateRow(rowIndex++);

                    // cabecalho
                    string[] cabecalhoVerde = { "Meses", "Contrato (kW)", "Fatura (R$)", "Contrato Sugerido (kW)", "Fatura (R$)", "Economia (R$" };

                    // adiciona cabecalho
                    cabecalhoTabelaXLS(workbook, sheet, cabecalhoVerde, rowIndex++);

                    // meses
                    for (i = 0; i < 12; i++)
                    {
                        // adiciona linha
                        row = sheet.CreateRow(rowIndex++);

                        // mes
                        datahoraCelulaXLS(row, 0, mes, _dataStyle);

                        // proximo mes
                        mes = mes.AddMonths(1);

                        // contrato 
                        numeroCelulaXLS(row, 1, dem_ideal_resultado.dem_ideal.DemP_Atual[i], _1CellStyle);

                        // fatura
                        numeroCelulaXLS(row, 2, dem_ideal_resultado.dem_ideal.Valor_Atual[i], _2CellStyle);

                        // contrato ideal 
                        numeroCelulaXLS(row, 3, dem_ideal_resultado.dem_ideal.DemP_Ideal, _1CellStyle);

                        // fatura ideal
                        numeroCelulaXLS(row, 4, dem_ideal_resultado.dem_ideal.Valor_Ideal[i], _2CellStyle);

                        // economia
                        numeroCelulaXLS(row, 5, dem_ideal_resultado.dem_ideal.Economia[i], _2CellStyle);
                    }

                    break;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(4).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Análise Demanda de Contrato", _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }
    }
}