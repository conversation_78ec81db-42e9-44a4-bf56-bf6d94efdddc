﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class AnalisesController
    {
        // Correcao de Fator de Potencia
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Analise_FatorPotencia", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Analise_FatorPotencia(char tipo_interface, ref CONFIG_INTERFACE pcfg_interface, ref DATAHORA pdatahora_ini, ref DATAHORA pdatahora_fim, double fator_potencia, ref CORR_FATPOTENCIA pfator_potencia);


        //
        // CORRECAO DE FATOR DE POTENCIA
        //

        // GET: Correcao de Fator de Potencia
        public ActionResult Correcao_FatPot(int IDCliente, int IDMedicao)
        {
            // tela de ajuda - correcao de fator de potencia
            CookieStore.SalvaCookie_String("PaginaAjuda", "Analise_CorrecaoFatPot");

            // le supervisao da medicao
            SupervMedicoesMetodos supmedicoesMetodos = new SupervMedicoesMetodos();
            var listaMedicoes = supmedicoesMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

            // salva cookie 
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", listaMedicoes.IDTipoMedicao_MD);
            CookieStore.SalvaCookie_String("MedicaoNome", listaMedicoes.NMED);
            CookieStore.SalvaCookie_String("UnidadeNome", listaMedicoes.NUN);
            CookieStore.SalvaCookie_String("GrupoNome", listaMedicoes.NGU);
            CookieStore.SalvaCookie_Int("_IDGateway", listaMedicoes.IDGW_GW);
            CookieStore.SalvaCookie_Int("IDTipoGateway", listaMedicoes.IDTipoGateway_GW);
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Correcao_FatPot");

            // le cookies
            LeCookies_SmartEnergy();

            // data inicio
            DateTime data_hora_ini = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 0, 0);

            // data fim
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);
            data_hora_fim = data_hora_fim.AddMinutes(-15);

            // le cookie datahora
            DateTime data_hora_ini_cookie = CookieStore.LeCookie_Datahora("Relat_Data");
            DateTime data_hora_fim_cookie = CookieStore.LeCookie_Datahora("Relat_DataFim");

            // verifica se existe datahora no cookie
            if (data_hora_ini_cookie.Year != 2000 && data_hora_fim_cookie.Year != 2000)
            {
                // copia data do cookie 
                data_hora_ini = data_hora_ini_cookie;
                data_hora_fim = data_hora_fim_cookie;
            }

            // converte
            DATAHORA dh_ini = new DATAHORA();
            DATAHORA dh_fim = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, data_hora_ini);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, data_hora_fim);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", data_hora_ini);
            CookieStore.SalvaCookie_Datahora("Relat_DataFim", data_hora_fim);

            // data e hora atual
            ViewBag.DataIni = string.Format("{0:d}", data_hora_ini);
            ViewBag.HoraIni = string.Format("{0:HH}:{1:mm}:00", data_hora_ini, data_hora_ini);
            ViewBag.DataFim = string.Format("{0:d}", data_hora_fim);
            ViewBag.HoraFim = string.Format("{0:HH}:{1:mm}:00", data_hora_fim, data_hora_fim);

            ViewBag.DataTextoAtualIni = string.Format("{0:d} {1:HH:mm}", data_hora_ini, data_hora_ini);
            ViewBag.DataTextoAtualFim = string.Format("{0:d} {1:HH:mm}", data_hora_fim, data_hora_fim);

            // fator de potencia de correcao
            double fatpot_corrigir = CookieStore.LeCookie_Double("Correcao_FatPot");

            if (fatpot_corrigir == 0.0)
            {
                fatpot_corrigir = 0.92;
            }

            // salva cookie fator de potencia de correcao
            CookieStore.SalvaCookie_Double("Correcao_FatPot", fatpot_corrigir);

            ViewBag.fatpot_corrigir = string.Format("{0:0.00}", fatpot_corrigir);

            // valores
            CORR_FATPOTENCIA_RESULT correcao_fatpot_resultado = new CORR_FATPOTENCIA_RESULT();
            correcao_fatpot_resultado.calculo_feito = false;
            ViewBag.correcao_fatpot_resultado = correcao_fatpot_resultado;

            // lista de erros
            var listaErros = new List<string>();

            // erros
            ViewBag.listaErros = listaErros;

            return View();
        }

        // GET: Correcao de Fator de Potencia
        public PartialViewResult _Correcao_FatPot_Atualizar(int Navegacao, string DataIni = "01/01/2000 00:00:00", string DataFim = "01/01/2000 00:00:00", string fatpot_desejado = "0")
        {
            // calcula
            Correcao_FatPot_Show(Navegacao, DataIni, DataFim, fatpot_desejado);

            return PartialView();
        }

        // GET: Correcao de Fator de Potencia Print
        public ActionResult Correcao_FatPot_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDRanking
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // nao imprimir grafico
            bool PrintGrafico = true;
            ViewBag.PrintGrafico = PrintGrafico;

            // calcula
            Correcao_FatPot_Show(0);

            // imprime
            return View();
        }

        // GET: Correcao de Fator de Potencia EMAIL
        public async Task<ActionResult> Correcao_FatPot_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // nao imprimir grafico
            bool PrintGrafico = false;
            ViewBag.PrintGrafico = PrintGrafico;

            // calcula
            Correcao_FatPot_Show(0);

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Correcao_FatPot_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDMedicao, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            CORR_FATPOTENCIA_RESULT correcao_fatpot_resultado = ViewBag.correcao_fatpot_resultado;

            string viewPartial = "_Correcao_FatPot_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "CorrecaoFatPotEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtualIni", ViewBag.DataIni);
            message = message.Replace("ViewBag.DataTextoAtualFim", ViewBag.DataFim);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            message = message.Replace("ViewBag.GrupoNome", ViewBag.GrupoNome);
            message = message.Replace("ViewBag.UnidadeNome", ViewBag.UnidadeNome);
            message = message.Replace("ViewBag.MedicaoNome", ViewBag.MedicaoNome);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Correcao de Fator de Potencia PDF
        public ActionResult Correcao_FatPot_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // nao imprimir grafico
            bool PrintGrafico = false;
            ViewBag.PrintGrafico = PrintGrafico;

            // calcula
            Correcao_FatPot_Show(0);

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Correcao_FatPot_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDMedicao, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            CORR_FATPOTENCIA_RESULT correcao_fatpot_resultado = ViewBag.correcao_fatpot_resultado;

            string viewPartial = "_Correcao_FatPot_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Correcao de Fator de Potencia XLS
        public ActionResult Correcao_FatPot_XLS()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // calcula
            Correcao_FatPot_Show(0);

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_Correcao_FatPot(IDCliente, IDMedicao);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Correcao_FatPot_{0:000000}_{1:yyyyMMddHHmm}.xls", IDMedicao, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Correcao de Fator de Potencia XLS Download
        [HttpGet]
        public virtual ActionResult Correcao_FatPot_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Correcao de Fator de Potencia Show
        public void Correcao_FatPot_Show(int Navegacao, string DataIni = "01/01/2000 00:00:00", string DataFim = "01/01/2000 00:00:00", string fatpot_desejado = "0")
        {
            // caso forcar data
            if (Navegacao == 100)
            {
                // pega data
                DateTime dateiniValue = DateTime.Parse(DataIni);
                DateTime datefimValue = DateTime.Parse(DataFim);

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Relat_Data", dateiniValue);
                CookieStore.SalvaCookie_Datahora("Relat_DataFim", datefimValue);
            }

            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // le supervisao da medicao
            var medicoesMetodos = new SupervMedicoesMetodos();
            var listaMedicoes = medicoesMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

            // salva cookie 
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", listaMedicoes.IDTipoMedicao_MD);
            CookieStore.SalvaCookie_String("MedicaoNome", listaMedicoes.NMED);
            CookieStore.SalvaCookie_String("UnidadeNome", listaMedicoes.NUN);
            CookieStore.SalvaCookie_String("GrupoNome", listaMedicoes.NGU);
            CookieStore.SalvaCookie_Int("_IDGateway", listaMedicoes.IDGW_GW);
            CookieStore.SalvaCookie_Int("IDTipoGateway", listaMedicoes.IDTipoGateway_GW);

            // le cookie datahora
            DateTime data_hora_ini_cookie = CookieStore.LeCookie_Datahora("Relat_Data");
            DateTime data_hora_fim_cookie = CookieStore.LeCookie_Datahora("Relat_DataFim");

            DATAHORA data_hora_ini = new DATAHORA();
            DATAHORA data_hora_fim = new DATAHORA();

            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -3:    // mes anterior

                    data_hora_ini_cookie = data_hora_ini_cookie.AddMonths(-1);
                    data_hora_fim_cookie = data_hora_fim_cookie.AddMonths(-1);
                    break;

                case 3:    // mes seguinte

                    data_hora_ini_cookie = data_hora_ini_cookie.AddMonths(1);
                    data_hora_fim_cookie = data_hora_fim_cookie.AddMonths(1);
                    break;

                case -4:    // ano anterior

                    data_hora_ini_cookie = data_hora_ini_cookie.AddYears(-1);
                    data_hora_fim_cookie = data_hora_fim_cookie.AddMonths(-1);
                    break;

                case 4:    // ano seguinte

                    data_hora_ini_cookie = data_hora_ini_cookie.AddYears(1);
                    data_hora_fim_cookie = data_hora_fim_cookie.AddYears(1);
                    break;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", data_hora_ini_cookie);
            CookieStore.SalvaCookie_Datahora("Relat_DataFim", data_hora_fim_cookie);

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora_ini, data_hora_ini_cookie);
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora_fim, data_hora_fim_cookie);

            // fator de potencia de correcao
            double fatpot_corrigir = 0.92;

            if( fatpot_desejado == "0")
            {
                fatpot_corrigir = CookieStore.LeCookie_Double("Correcao_FatPot");
            }
            else
            {
                fatpot_corrigir = double.Parse(fatpot_desejado);
            }

            if (fatpot_corrigir == 0.0)
            {
                fatpot_corrigir = 0.92;
            }

            // salva cookie fator de potencia de correcao
            CookieStore.SalvaCookie_Double("Correcao_FatPot", fatpot_corrigir);

            // calcula
            Calc_Correcao_FatPot(data_hora_ini, data_hora_fim, fatpot_corrigir);

            return;
        }

        // Calcula Correcao de Fator de Potencia
        private void Calc_Correcao_FatPot(DATAHORA data_hora_ini, DATAHORA data_hora_fim, double fatpot_corrigir)
        {
            // retorno
            int retorno = 0;

            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e IDMedicao
            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;

            // medicao
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicaoMetodos.ListarPorId(IDMedicao);

            // estruturas
            CORR_FATPOTENCIA_RESULT correcao_fatpot_resultado = new CORR_FATPOTENCIA_RESULT();
            correcao_fatpot_resultado.IDMedicao = IDMedicao;

            // lista de erros
            var listaErros = new List<string>();

            // converte data e hora
            DateTime DataHoraIni = Funcoes_Converte.ConverteDataHora2DateTime(data_hora_ini);
            DateTime DataHoraFim = Funcoes_Converte.ConverteDataHora2DateTime(data_hora_fim);

            if( medicao != null )
            {
                // preenche solicitacao
                CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
                config_interface.sweb.id_cliente = IDCliente;
                config_interface.sweb.id_medicao = IDMedicao;
                config_interface.sweb.id_gateway = 0;

                // nome medicao
                correcao_fatpot_resultado.Nome_Medicao = medicao.Nome;

                // resultado
                CORR_FATPOTENCIA correcao_fatpot = new CORR_FATPOTENCIA();

                // calcula correcao fator de potencia
                retorno = SmCalcDB_Analise_FatorPotencia((char)0, ref config_interface, ref data_hora_ini, ref data_hora_fim, fatpot_corrigir, ref correcao_fatpot);

                if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
                {
                    if (retorno > 0)
                        listaErros.Add(string.Format("Medição {0} com Retorno {1}", medicao.Nome, retorno));
                }

                // copia
                correcao_fatpot_resultado.correcao_fatpot = correcao_fatpot;
            }

            // valores
            correcao_fatpot_resultado.calculo_feito = true;
            ViewBag.correcao_fatpot_resultado = correcao_fatpot_resultado;

            // erros
            ViewBag.listaErros = listaErros;

            // data e hora atual
            ViewBag.DataIni = string.Format("{0:d}", DataHoraIni);
            ViewBag.HoraIni = string.Format("{0:HH:mm}", DataHoraIni);
            ViewBag.DataFim = string.Format("{0:d}", DataHoraFim);
            ViewBag.HoraFim = string.Format("{0:HH:mm}", DataHoraFim);

            ViewBag.DataTextoAtualIni = string.Format("{0:d} {1:HH:mm}", DataHoraIni, DataHoraIni);
            ViewBag.DataTextoAtualFim = string.Format("{0:d} {1:HH:mm}", DataHoraFim, DataHoraFim);

            ViewBag.DataIniN = DataHoraIni;
            ViewBag.DataFimN = DataHoraFim;

            // fator de potencia a corrigir
            ViewBag.fatpot_corrigir = string.Format("{0:0.000}", fatpot_corrigir);

            // valores
            ViewBag.Necessario = correcao_fatpot_resultado.correcao_fatpot.necessario;
            ViewBag.Excedente = correcao_fatpot_resultado.correcao_fatpot.excedente;
            ViewBag.Fixo = correcao_fatpot_resultado.correcao_fatpot.fixo;
            ViewBag.Controlado = correcao_fatpot_resultado.correcao_fatpot.controlado;

            return;
        }

        // Correcao de Fator de Potencia XLS
        private HSSFWorkbook XLS_Correcao_FatPot(int IDCliente, int IDMedicao)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 14);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // DEMANDA IDEAL e MESES
            //

            // resultado
            CORR_FATPOTENCIA_RESULT result = ViewBag.correcao_fatpot_resultado;

            // cria planilha
            var sheet = workbook.CreateSheet("Correção de Fator de Potência");

            // variaveis
            IRow row;
            int rowIndex = 0;
            int i;

            // fator de potencia desejado
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Fator de Potência desejado", _negritoCellStyle);
            numeroCelulaXLS(row, 1, result.correcao_fatpot.fatpot_sol, _2CellStyle);

            // pula linha
            row = sheet.CreateRow(rowIndex++);
            row = sheet.CreateRow(rowIndex++);

            // KVAR
            if (result.correcao_fatpot.necessario != 0.0 || result.correcao_fatpot.excedente != 0.0 || result.correcao_fatpot.fixo != 0.0 || result.correcao_fatpot.controlado != 0.0)
            {
                // aviso
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "É necessária a correção para o Fator de Potência desejado", _negritoCellStyle);

                // pula linha
                row = sheet.CreateRow(rowIndex++);

                // kvar necessario
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Necessário (kvar)", _negritoCellStyle);
                numeroCelulaXLS(row, 1, result.correcao_fatpot.necessario, _intCellStyle);

                if (result.correcao_fatpot.excedente != 0.0)
                {
                    // kvar excedente
                    row = sheet.CreateRow(rowIndex++);
                    textoCelulaXLS(row, 0, "Excedente (kvar)", _negritoCellStyle);
                    numeroCelulaXLS(row, 1, result.correcao_fatpot.excedente, _intCellStyle);
                }

                // pula linha
                row = sheet.CreateRow(rowIndex++);
                row = sheet.CreateRow(rowIndex++);

                // aviso
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "É sugerida a seguinte instalação:", _negritoCellStyle);

                // pula linha
                row = sheet.CreateRow(rowIndex++);

                // kvar fixo
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Fixo (kvar)", _negritoCellStyle);
                numeroCelulaXLS(row, 1, result.correcao_fatpot.fixo, _intCellStyle);

                // kvar controlado
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Controlado (kvar)", _negritoCellStyle);
                numeroCelulaXLS(row, 1, result.correcao_fatpot.controlado, _intCellStyle);
            }
            else
            {
                // aviso
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Não é necessária a correção para o Fator de Potência desejado", _negritoCellStyle);
            }

            // pula linha
            row = sheet.CreateRow(rowIndex++);
            row = sheet.CreateRow(rowIndex++);

            // multa sem correcao
            row = sheet.CreateRow(rowIndex++);
            if (result.correcao_fatpot.multa_sem_correcao > 0.0)
            {
                textoCelulaXLS(row, 0, "Multa de Fator de Potência no período selecionado (R$)", _negritoCellStyle);
                numeroCelulaXLS(row, 1, result.correcao_fatpot.multa_sem_correcao, _2CellStyle);
            }
            else
            {
                textoCelulaXLS(row, 0, "Não houve multa de Fator de Potência no período selecionado", _negritoCellStyle);
            }

            // pula linha
            row = sheet.CreateRow(rowIndex++);

            // economia
            if (result.correcao_fatpot.economia > 0.0)
            {
                // aviso
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Com base na sugestão acima existe potencial de economia", _negritoCellStyle);

                // pula linha
                row = sheet.CreateRow(rowIndex++);

                // kvar necessario
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Economia (R$)", _negritoCellStyle);
                numeroCelulaXLS(row, 1, result.correcao_fatpot.economia, _intCellStyle);
            }
            else
            {
                // aviso
                row = sheet.CreateRow(rowIndex++);
                textoCelulaXLS(row, 0, "Com base na sugestão acima NÃO temos economia na multa de Fator de Potência", _negritoCellStyle);
            }

            // largura de cada coluna
            sheet.SetColumnWidth(0, 16000);
            sheet.SetColumnWidth(1, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "Análise de Correção de Fator de Potência", _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(8).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }
    }
}