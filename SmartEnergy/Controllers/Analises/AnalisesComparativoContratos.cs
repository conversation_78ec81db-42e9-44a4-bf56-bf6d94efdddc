﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class AnalisesController
    {
        // fatura de energia eletrica
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_Fatura", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_Fatura(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, int id_simulacao, char tipo_contrato, char IDEstTarifaria, char <PERSON>hP<PERSON><PERSON><PERSON>, ref DATAHOR<PERSON> pdatahora_ini, ref DATAHOR<PERSON> pdatahora_fim, ref RESULT_ENERGIA_FATURA pfatura, ref RESULT_ENERGIA_FATURA pfatura_sim);


        // GET: Comparativo Contratos
        public ActionResult Comparativo_Contratos(int IDCliente, int tipo_arquivo = 0)
        {
            // comparativo
            return (Comparativo_Contratos_Show(IDCliente, tipo_arquivo));
        }

        // GET: Comparativo Contratos
        private ActionResult Comparativo_Contratos_Show(int IDCliente, int tipo_arquivo = 0)
        {
            // tela de ajuda - fatura
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie 
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Analises");

            // le cookies
            LeCookies_SmartEnergy();

            // data inicio
            DateTime data_hora_ini = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 0, 0);

            // data fim
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            // le cookie datahora
            DateTime data_hora_ini_cookie = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime data_hora_fim_cookie = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // verifica se existe datahora no cookie
            if (data_hora_ini_cookie.Year != 2000 && data_hora_fim_cookie.Year != 2000)
            {
                // copia data do cookie 
                data_hora_ini = data_hora_ini_cookie;
                data_hora_fim = data_hora_fim_cookie;
            }

            // converte
            DATAHORA dh_ini = new DATAHORA();
            DATAHORA dh_fim = new DATAHORA();
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, data_hora_ini);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, data_hora_fim);

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", data_hora_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", data_hora_fim);

            // data e hora atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", data_hora_ini);
            ViewBag.DataTextoAtual = string.Format("{0:MMMM/yyyy}", data_hora_ini);

            ViewBag.DataAtualN = data_hora_ini;
            ViewBag.DataFinalN = data_hora_fim;

            return View();
        }

        // GET: Comparativo Contratos - Atualizar
        public ActionResult Comparativo_Contratos_Atualizar(int Navegacao, string Data)
        {
            // caso forcar data
            if (Navegacao == 100)
            {
                // pega data
                DateTime dateValueIni = DateTime.Parse(Data);

                // data inicio
                DateTime data_hora_ini = new DateTime(dateValueIni.Year, dateValueIni.Month, 1, 0, 0, 0);

                // data fim
                DateTime data_hora_fim = data_hora_ini.AddMonths(1);

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Fatura_DataIni", data_hora_ini);
                CookieStore.SalvaCookie_Datahora("Fatura_DataFim", data_hora_fim);
            }

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;

            // le cookie datahora
            DateTime datahora_cookie_ini = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Fatura_DataFim");

            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -3:    // mes anterior

                    datahora_cookie_ini = datahora_cookie_ini.AddMonths(-1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(-1);
                    break;

                case 3:    // mes seguinte

                    datahora_cookie_ini = datahora_cookie_ini.AddMonths(1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(1);
                    break;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Fatura_DataIni", datahora_cookie_ini);
            CookieStore.SalvaCookie_Datahora("Fatura_DataFim", datahora_cookie_fim);

            // data e hora atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", datahora_cookie_ini);
            ViewBag.DataTextoAtual = string.Format("{0:MMMM/yyyy}", datahora_cookie_ini);

            ViewBag.DataAtualN = datahora_cookie_ini;
            ViewBag.DataFinalN = datahora_cookie_fim;

            // retorna status
            return Json(0, JsonRequestBehavior.AllowGet);
        }

        // GET: Comparativo Contratos - Print
        public ActionResult Comparativo_Contratos_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Comparativo_Contratos_Show(IDCliente);

            // resultado
            ViewBag.fatura_resultado = lista_comparativo.Keys.Contains(id) ? lista_comparativo[id] : null;

            // imprime
            return View();
        }

        // GET: Comparativo Contratos - EMAIL
        public async Task<ActionResult> Comparativo_Contratos_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Comparativo_Contratos_Show(IDCliente);

            // resultado
            ViewBag.fatura_resultado = lista_comparativo.Keys.Contains(id) ? lista_comparativo[id] : null;

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Comparativo_Contratos_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDCliente, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_Comparativo_Contratos_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "ComparativoFaturaEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.ClienteNome", ViewBag.ClienteNome);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Comparativo Contratos - PDF
        public ActionResult Comparativo_Contratos_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Comparativo_Contratos_Show(IDCliente);

            // resultado
            ViewBag.fatura_resultado = lista_comparativo.Keys.Contains(id) ? lista_comparativo[id] : null;

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Comparativo_Contratos_{0:000000}_{1:yyyyMMddHHmm}.pdf", IDCliente, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_Comparativo_Contratos_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Comparativo Contratos - XLS
        public ActionResult Comparativo_Contratos_XLS()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDCliente e GUID
            int IDCliente = ViewBag._IDCliente;
            Guid id = ViewBag.taskID;

            // nome do cliente
            var clientesMetodos = new ClientesMetodos();
            var cliente = clientesMetodos.ListarPorId(IDCliente);
            ViewBag.ClienteNome = cliente.Nome;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Comparativo_Contratos_Show(IDCliente);

            // resultado
            ViewBag.fatura_resultado = lista_comparativo.Keys.Contains(id) ? lista_comparativo[id] : null;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_Comparativo_Contratos(IDCliente);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Comparativo_Contratos_{0:000000}_{1:yyyyMMddHHmm}.xls", IDCliente, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Comparativo Contratos - XLS Download
        [HttpGet]
        public virtual ActionResult Comparativo_Contratos_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }


        // estruturas
        private static IDictionary<Guid, int> tasks_comparativo = new Dictionary<Guid, int>();
        private List<COMPARATIVO_CONTRATOS> lista_comparativo_tmp = new List<COMPARATIVO_CONTRATOS>();
        private static IDictionary<Guid, List<COMPARATIVO_CONTRATOS>> lista_comparativo = new Dictionary<Guid, List<COMPARATIVO_CONTRATOS>>();

        public ActionResult Comparativo_Contratos_IniciaCalculo()
        {
            // taskID
            Guid taskId = Guid.NewGuid();
            tasks_comparativo.Add(taskId, 0);

            // salva cookie
            CookieStore.SalvaCookie_GUID("taskID", taskId);

            // le cookies
            LeCookies_SmartEnergy();

            // simulação
            int IDSimulacaoCenario = 0;

            // IDCliente
            int IDCliente = ViewBag._IDCliente;

            // le cookie datahora
            DateTime datahora_cookie_ini = CookieStore.LeCookie_Datahora("Fatura_DataIni");
            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Fatura_DataFim");


            // limpa lista
            lista_comparativo_tmp.Clear();

            // task
            Task.Factory.StartNew(() =>
            {
                // le medicoes
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                List<MedicoesDominio> medicoes = medicoesMetodos.ListarTodos(IDCliente, ViewBag._ConfigMed);

                // estruturas

                // lista de erros
                var listaErros = new List<string>();

                // preenche solicitacao
                CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
                config_interface.sweb.id_cliente = IDCliente;

                // converte
                DATAHORA dh_ini = new DATAHORA();
                DATAHORA dh_fim = new DATAHORA();
                Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, datahora_cookie_ini);
                Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, datahora_cookie_fim);

                // estruturas
                RESULT_ENERGIA_FATURA fatura_Cativo_Azul = new RESULT_ENERGIA_FATURA();
                RESULT_ENERGIA_FATURA fatura_Cativo_Verde = new RESULT_ENERGIA_FATURA();
                RESULT_ENERGIA_FATURA fatura_Livre_Azul = new RESULT_ENERGIA_FATURA();
                RESULT_ENERGIA_FATURA fatura_Livre_Verde = new RESULT_ENERGIA_FATURA();
                RESULT_ENERGIA_FATURA fatura_sim = new RESULT_ENERGIA_FATURA();

                // barra de progresso
                int total = 0;
                int atual = 0;

                // percorre medicoes
                if (medicoes != null)
                {
                    // barra de progresso
                    total = medicoes.Count();

                    // percorre medicoes
                    foreach (MedicoesDominio medicao in medicoes)
                    {
                        // update task progress
                        double progresso = ((double)atual / (double)total) * 100;
                        tasks_comparativo[taskId] = (int)progresso;
                        atual++;

                        // verifica se nao eh energia real ou medição principal (unidade consumidora)
                        if (medicao.IDTipoMedicao != TIPO_MEDICAO.ENERGIA || medicao.IDCategoriaMedicao != CATEGORIA_MEDICAO.PRINCIPAL)
                        {
                            continue;
                        }

                        // preenche solicitacao
                        config_interface.sweb.id_medicao = medicao.IDMedicao;
                        config_interface.sweb.id_gateway = medicao.IDGateway;


                        // Calcula Fatura Cativo Azul (sem simulação)
                        int retorno = SmCalcDB_Energia_Fatura((char)0, ref config_interface, IDSimulacaoCenario, (char)TIPOCONTRATO.CATIVO, (char)TIPOESTR_TAR.THS_AZUL, (char)0, ref dh_ini, ref dh_fim, ref fatura_Cativo_Azul, ref fatura_sim);

                        // Calcula Fatura Cativo Verde (sem simulação)
                        retorno = SmCalcDB_Energia_Fatura((char)0, ref config_interface, IDSimulacaoCenario, (char)TIPOCONTRATO.CATIVO, (char)TIPOESTR_TAR.THS_VERDE, (char)0, ref dh_ini, ref dh_fim, ref fatura_Cativo_Verde, ref fatura_sim);

                        // Calcula Fatura Livre Azul (sem simulação)
                        retorno = SmCalcDB_Energia_Fatura((char)0, ref config_interface, IDSimulacaoCenario, (char)TIPOCONTRATO.LIVRE, (char)TIPOESTR_TAR.THS_AZUL, (char)0, ref dh_ini, ref dh_fim, ref fatura_Livre_Azul, ref fatura_sim);

                        // Calcula Fatura Livre Verde (sem simulação)
                        retorno = SmCalcDB_Energia_Fatura((char)0, ref config_interface, IDSimulacaoCenario, (char)TIPOCONTRATO.LIVRE, (char)TIPOESTR_TAR.THS_VERDE, (char)0, ref dh_ini, ref dh_fim, ref fatura_Livre_Verde, ref fatura_sim);


                        // coloca resultado na lista temporaria
                        COMPARATIVO_CONTRATOS comparativo = new COMPARATIVO_CONTRATOS();
                        comparativo.IDMedicao = medicao.IDMedicao;
                        comparativo.Nome_Medicao = medicao.Nome;
                        comparativo.IDContratoMedicao = medicao.IDContratoMedicao;
                        comparativo.IDEstruturaTarifaria = medicao.IDEstruturaTarifaria;
                        comparativo.Contrato_Atual = "---";

                        comparativo.fatura_Cativo_Azul = fatura_Cativo_Azul;
                        comparativo.fatura_Cativo_Verde = fatura_Cativo_Verde;
                        comparativo.fatura_Livre_Azul = fatura_Livre_Azul;
                        comparativo.fatura_Livre_Verde = fatura_Livre_Verde;

                        comparativo.IDContratoMedicao_Economia = medicao.IDContratoMedicao;
                        comparativo.IDEstruturaTarifaria_Economia = medicao.IDEstruturaTarifaria;
                        comparativo.Contrato_Economia = "---";
                        comparativo.economia = 0.0;
                        comparativo.economia_porc = 0.0;

                        comparativo.info_icon = 0;
                        comparativo.info1 = "";
                        comparativo.info2 = "";
                        comparativo.info3 = "";
                        comparativo.retorno = retorno;

                        //
                        // Economia
                        //
                        // Compara o contrato configurado versus o dos outros cenários e encontra o mais vantajoso
                        // valor positivo = economia
                        // valor negativo = não tem, pois no pior caso mantém o mesmo contrato
                        // vaor zero = o contrato atual é o mais vantajoso

                        // valor de referencia
                        double valor_referencia = 0;

                        // verifica se configurado é Cativo Azul
                        if (medicao.IDContratoMedicao == TIPOCONTRATO.CATIVO && medicao.IDEstruturaTarifaria == TIPOESTR_TAR.THS_AZUL)
                        {
                            valor_referencia = fatura_Cativo_Azul.total;
                            comparativo.Contrato_Atual = "Cativo THS Azul";
                        }

                        // verifica se configurado é Cativo Verde
                        if (medicao.IDContratoMedicao == TIPOCONTRATO.CATIVO && medicao.IDEstruturaTarifaria == TIPOESTR_TAR.THS_VERDE)
                        {
                            valor_referencia = fatura_Cativo_Verde.total;
                            comparativo.Contrato_Atual = "Cativo THS Verde";
                        }

                        // verifica se configurado é Livre Azul
                        if (medicao.IDContratoMedicao == TIPOCONTRATO.LIVRE && medicao.IDEstruturaTarifaria == TIPOESTR_TAR.THS_AZUL)
                        {
                            valor_referencia = fatura_Livre_Azul.total;
                            comparativo.Contrato_Atual = "Livre THS Azul";
                        }

                        // verifica se configurado é Livre Verde
                        if (medicao.IDContratoMedicao == TIPOCONTRATO.LIVRE && medicao.IDEstruturaTarifaria == TIPOESTR_TAR.THS_VERDE)
                        {
                            valor_referencia = fatura_Livre_Verde.total;
                            comparativo.Contrato_Atual = "Livre THS Verde";
                        }

                        // compara com Cativo Azul
                        double economia = valor_referencia - fatura_Cativo_Azul.total;

                        if (economia > 0)
                        {
                            comparativo.IDContratoMedicao_Economia = TIPOCONTRATO.CATIVO;
                            comparativo.IDEstruturaTarifaria_Economia = TIPOESTR_TAR.THS_AZUL;

                            comparativo.economia = economia;
                            comparativo.Contrato_Economia = "Cativo THS Azul";
                        }

                        // compara com Cativo Verde
                        economia = valor_referencia - fatura_Cativo_Verde.total;

                        if (economia > 0 && economia > comparativo.economia)
                        {
                            comparativo.IDContratoMedicao_Economia = TIPOCONTRATO.CATIVO;
                            comparativo.IDEstruturaTarifaria_Economia = TIPOESTR_TAR.THS_VERDE;

                            comparativo.economia = economia;
                            comparativo.Contrato_Economia = "Cativo THS Verde";
                        }

                        // compara com Livre Azul
                        economia = valor_referencia - fatura_Livre_Azul.total;

                        if (economia > 0 && economia > comparativo.economia)
                        {
                            comparativo.IDContratoMedicao_Economia = TIPOCONTRATO.LIVRE;
                            comparativo.IDEstruturaTarifaria_Economia = TIPOESTR_TAR.THS_AZUL;

                            comparativo.economia = economia;
                            comparativo.Contrato_Economia = "Livre THS Azul";
                        }

                        // compara com Livre Verde
                        economia = valor_referencia - fatura_Livre_Verde.total;

                        if (economia > 0 && economia > comparativo.economia)
                        {
                            comparativo.IDContratoMedicao_Economia = TIPOCONTRATO.LIVRE;
                            comparativo.IDEstruturaTarifaria_Economia = TIPOESTR_TAR.THS_VERDE;

                            comparativo.economia = economia;
                            comparativo.Contrato_Economia = "Livre THS Verde";
                        }

                        // porcentagem de economia
                        if (valor_referencia != 0.0)
                        {
                            comparativo.economia_porc = (comparativo.economia / valor_referencia) * 100.0;

                            if (comparativo.economia_porc < 0.0)
                            {
                                comparativo.economia_porc = 0.0;
                            }
                        }

                        //
                        // Análise
                        //
                        // verifica se existe tarifa de mercado livre
                        // caso não existir, a comparação entre faturas é prejudicada, pois o cálculo da fatura ML desconta o valor de consumo da distribuidora e insere o consumo 
                        // da comercializadora, e sem essa tarifa, o cativo sempre será penalizado.

                        bool tarifaML_desconfigurada = false;

                        if (fatura_Livre_Azul.consumo_livre_p[CF.TAR] == 0.0 && fatura_Livre_Azul.consumo_livre_fp[CF.TAR] == 0.0)
                        {
                            tarifaML_desconfigurada = true;
                        }

                        if (fatura_Livre_Verde.consumo_livre_p[CF.TAR] == 0.0 && fatura_Livre_Verde.consumo_livre_fp[CF.TAR] == 0.0)
                        {
                            tarifaML_desconfigurada = true;
                        }

                        if (tarifaML_desconfigurada)
                        {
                            comparativo.info_icon = 1;
                            comparativo.info1 = "A tarifa de energia do Mercado Livre não está configurada.";
                        }
                        else
                        {
                            if (comparativo.economia == 0.0)
                            {
                                comparativo.info_icon = 3;
                                comparativo.info1 = "O contrato atual é o mais vantajoso.";
                            }
                            else
                            {
                                comparativo.info_icon = 2;
                                comparativo.info1 = string.Format("O contrato {0} é mais vantajoso que o atual.", comparativo.Contrato_Economia);
                            }
                        }

                        // coloca resultado na lista temporária
                        lista_comparativo_tmp.Add(comparativo);
                    }
                }

                // coloca resultado na lista
                lista_comparativo.Add(taskId, new List<COMPARATIVO_CONTRATOS>(lista_comparativo_tmp));

                // terminou
                tasks_comparativo.Remove(taskId);
            });

            // retorna status
            return Json(taskId, JsonRequestBehavior.AllowGet);
        }

        public ActionResult Comparativo_Contratos_Progress(Guid id)
        {
            return Json(tasks_comparativo.Keys.Contains(id) ? tasks_comparativo[id] : 100, JsonRequestBehavior.AllowGet);
        }

        public PartialViewResult _Comparativo_Contratos(Guid id)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // resultado
            ViewBag.fatura_resultado = lista_comparativo.Keys.Contains(id) ? lista_comparativo[id] : null;

            return PartialView();
        }

        // Comparativo Contratos - XLS
        private HSSFWorkbook XLS_Comparativo_Contratos(int IDCliente)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _2CellStyle_Bold = criaEstiloXLS(workbook, 2, false, true);
            ICellStyle _2CellStyle_Verde = criaEstiloXLS(workbook, 2, false, true, IndexedColors.White.Index, IndexedColors.Green.Index);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            ICellStyle _textoBorderCellStyle = criaEstiloXLS(workbook, 100, true);
            ICellStyle _1CellBorderStyle = criaEstiloXLS(workbook, 1, true);
            ICellStyle _2CellBorderStyle = criaEstiloXLS(workbook, 2, true);

            //
            // MEDICOES
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Comparativo de Contratos");

            // cabecalho
            string[] cabecalho = { "Medições", "Contrato Atual", "Cativo THS Azul (R$)", "Cativo THS Verde (R$)", "Livre THS Azul (R$)",  "Livre THS Verde (R$)", "Economia (%)", "Economia (R$)", "Análises" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            List<COMPARATIVO_CONTRATOS> fatura_resultado = ViewBag.fatura_resultado;
            int i;
            IRow row;

            // percorre valores
            if (fatura_resultado != null)
            {
                foreach (COMPARATIVO_CONTRATOS fat in fatura_resultado)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex++);

                    // medicao
                    textoCelulaXLS(row, 0, fat.Nome_Medicao);

                    if (fat.retorno != 0)
                    {
                        textoCelulaXLS(row, 1, string.Format("Erro [código {0}]", fat.retorno));
                        textoCelulaXLS(row, 2, "---");
                        textoCelulaXLS(row, 3, "---");
                        textoCelulaXLS(row, 4, "---");
                        textoCelulaXLS(row, 5, "---");
                        textoCelulaXLS(row, 6, "---");
                        textoCelulaXLS(row, 7, "---");

                        string texto_erro = "Erro desconhecido";
                                            
                        switch (fat.retorno)
                        {
                            case 1:     // nao foi possivel abrir banco de dados
                                texto_erro = "Não existem registros de demanda";
                                break;
                            case 2:     // nao existem dados
                                texto_erro = "Não existem registros de demanda";
                                break;
                            case 3:     // intervalo menos de 1 dia
                                texto_erro = "Intervalo menor que 1 dia";
                                break;
                            case 4:     // leitura da configuracao da medicao
                                texto_erro = "Erro na Configuração da Medição";
                                break;
                            case 5:     // leitura da configuracao da gateway
                                texto_erro = "Erro na Configuração da Gateway";
                                break;
                            case 6:     // leitura do historico de contrato de demanda
                                texto_erro = "Não existe contrato de demanda";
                                break;
                            case 7:     // tarifas THS
                                texto_erro = "Erro tarifas THS";
                                break;
                            case 8:     // tarifa pis/cofins
                                texto_erro = "Erro tarifas PIS/COFINS";
                                break;
                            case 9:     // nao existem bandeiras tarifarias
                                texto_erro = "Não existem Bandeiras Tarifárias";
                                break;
                        }

                        textoCelulaXLS(row, 8, texto_erro);
                    }
                    else
                    {
                        //
                        // Faturas
                        //

                        textoCelulaXLS(row, 1, fat.Contrato_Atual);

                        if (fat.IDContratoMedicao_Economia == TIPOCONTRATO.CATIVO && fat.IDEstruturaTarifaria_Economia == TIPOESTR_TAR.THS_AZUL)
                        {
                            numeroCelulaXLS(row, 2, fat.fatura_Cativo_Azul.total, _2CellStyle_Verde);
                        }
                        else
                        {
                            numeroCelulaXLS(row, 2, fat.fatura_Cativo_Azul.total, _2CellStyle);
                        }

                        if (fat.IDContratoMedicao_Economia == TIPOCONTRATO.CATIVO && fat.IDEstruturaTarifaria_Economia == TIPOESTR_TAR.THS_VERDE)
                        {
                            numeroCelulaXLS(row, 3, fat.fatura_Cativo_Verde.total, _2CellStyle_Verde);
                        }
                        else
                        {
                            numeroCelulaXLS(row, 3, fat.fatura_Cativo_Verde.total, _2CellStyle);
                        }

                        if (fat.IDContratoMedicao_Economia == TIPOCONTRATO.LIVRE && fat.IDEstruturaTarifaria_Economia == TIPOESTR_TAR.THS_AZUL)
                        {
                            numeroCelulaXLS(row, 4, fat.fatura_Livre_Azul.total, _2CellStyle_Verde);
                        }
                        else
                        {
                            numeroCelulaXLS(row, 4, fat.fatura_Livre_Azul.total, _2CellStyle);
                        }

                        if (fat.IDContratoMedicao_Economia == TIPOCONTRATO.LIVRE && fat.IDEstruturaTarifaria_Economia == TIPOESTR_TAR.THS_VERDE)
                        {
                            numeroCelulaXLS(row, 5, fat.fatura_Livre_Verde.total, _2CellStyle_Verde);
                        }
                        else
                        {
                            numeroCelulaXLS(row, 5, fat.fatura_Livre_Verde.total, _2CellStyle);
                        }

                        //
                        // Economia
                        //

                        if (fat.economia < 0.0)
                        {
                            textoCelulaXLS(row, 6, "---");
                        }
                        else
                        {
                            numeroCelulaXLS(row, 6, fat.economia_porc, _2CellStyle_Bold);
                        }

                        numeroCelulaXLS(row, 7, fat.economia, _2CellStyle_Bold);

                        textoCelulaXLS(row, 8, fat.info1);
                    }
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS_ComparativoContratos(workbook, sheet, _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(4).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }
    }
}
