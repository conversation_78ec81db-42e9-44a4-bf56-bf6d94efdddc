﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class AnalisesController
    {

        //
        // ANALISE RAMO DE ATIVIDADE MÊS
        //

        // GET: Analise Ramo de Atividade Mês
        public ActionResult Analise_RamoAtividade_Mes(int IDRamoAtividade)
        {
            // tela de ajuda - ramo de atividade
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");
            CookieStore.SalvaCookie_Int("_IDRamoAtividade", IDRamoAtividade);

            // le cookies
            LeCookies_SmartEnergy();

            // data atual
            DateTime data_atual = DateTime.Now;
            DateTime data_referencia = data_atual.AddMonths(-1);

            // le cookie datahora atual
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            if( datahora_cookie.Year != 2000 )
            {
                data_atual = datahora_cookie;
            }

            // le cookie datahora referencia
            datahora_cookie = CookieStore.LeCookie_Datahora("Relat_DataFim");

            if (datahora_cookie.Year != 2000)
            {
                data_referencia = datahora_cookie;
            }

            // inverte
            if (data_atual < data_referencia)
            {
                DateTime data_temp = data_atual;
                data_atual = data_referencia;
                data_referencia = data_temp;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", data_atual);
            CookieStore.SalvaCookie_Datahora("Relat_DataFim", data_referencia);

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", data_atual);
            ViewBag.DataAtualN = data_atual;
            ViewBag.DataTextoAtual = string.Format("{0:MM/yyyy}", data_atual);

            // data referencia
            ViewBag.DataReferencia = string.Format("{0:MM/yyyy}", data_referencia);
            ViewBag.DataReferenciaN = data_referencia;
            ViewBag.DataTextoReferencia = string.Format("{0:MM/yyyy}", data_referencia);


            // le tipos RamoAtividade
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposRamoAtividade = listatiposMetodos.ListarTodos("TipoRamoAtividade");

            // analise
            AnaliseRamoAtividade_Mes ramoAtividades = new AnaliseRamoAtividade_Mes();

            if (listatiposRamoAtividade != null)
            {
                // encontra ramo
                ListaTiposDominio tipoRamoAtividade = listatiposRamoAtividade.Find(t => t.ID == IDRamoAtividade);

                if (tipoRamoAtividade != null)
                {
                    ramoAtividades.IDRamoAtividade = tipoRamoAtividade.ID;
                    ramoAtividades.Descricao = tipoRamoAtividade.Descricao;
                    ramoAtividades.ConsumoMesAnterior = 0.0;
                    ramoAtividades.ConsumoMesAtual = 0.0;
                    ramoAtividades.Diferenca = 0.0;
                    ramoAtividades.NumClientes = 0;
                    ramoAtividades.NumMedicoes = 0;

                    for (int i=0; i<31; i++)
                    {
                        ramoAtividades.Data[i] = new DateTime(data_atual.Year, data_atual.Month, 1, 0, 0, 0);
                        ramoAtividades.ConsumoMesAtual_Dia[i] = 0.0;
                    }
                }
            }

            ViewBag.ramoAtividades = ramoAtividades;
            ViewBag.listatiposRamoAtividade = listatiposRamoAtividade;

            return View();
        }

        // GET: Analise Ramo de Atividade Mês
        public PartialViewResult _Analise_RamoAtividade_Mes_Atualizar(int Navegacao, int IDRamoAtividade, string DataAtual, string DataReferencia)
        {
            // calcula
            Analise_RamoAtividade_Mes_Show(Navegacao, IDRamoAtividade, DataAtual, DataReferencia);

            return PartialView();
        }

        // GET: Analise Ramo de Atividade Mês Print
        public ActionResult Analise_RamoAtividade_Mes_Print()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // GUID
            Guid id = ViewBag.taskID;

            // ramo de atividade
            int IDRamoAtividade = ViewBag._IDRamoAtividade;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Analise_RamoAtividade_Mes_Show(0, IDRamoAtividade, data_str, data_str);

            // resultado
            Analise_RamoAtividade_Mes_Resultado(id);

            // imprime
            return View();
        }

        // GET: Analise Ramo de Atividade Mês EMAIL
        public async Task<ActionResult> Analise_RamoAtividade_Mes_EMAIL(string destino, string assunto)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // GUID
            Guid id = ViewBag.taskID;

            // ramo de atividade
            int IDRamoAtividade = ViewBag._IDRamoAtividade;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Analise_RamoAtividade_Mes_Show(0, IDRamoAtividade, data_str, data_str);

            // resultado
            Analise_RamoAtividade_Mes_Resultado(id);

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Analise_RamoAtividade_{0}_{1:yyyyMMddHHmm}.pdf", ViewBag.NomeRamoAtividade, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_Analise_RamoAtividade_Mes_Calculo_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna 
            ViewBag.caminhoCompleto = caminhoCompleto;

            // emails
            string[] emails = { destino };

            // anexos
            List<string> Attachments = new List<string>();
            Attachments.Add(caminhoCompleto);

            // envia EMAIL
            var emailTemplate = "AnaliseRamoAtividadeMesEmail";
            var message = await EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);
            message = message.Replace("ViewBag.NomeRamoAtividade", ViewBag.NomeRamoAtividade);
            await EmailServices.SendBulkEmailAsync(emails, assunto, message, Attachments);

            // retorna status
            var returnedData = new
            {
                status = "OK"
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Analise Ramo de Atividade Mês PDF
        public ActionResult Analise_RamoAtividade_Mes_PDF()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // GUID
            Guid id = ViewBag.taskID;

            // ramo de atividade
            int IDRamoAtividade = ViewBag._IDRamoAtividade;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Analise_RamoAtividade_Mes_Show(0, IDRamoAtividade, data_str, data_str);

            // resultado
            Analise_RamoAtividade_Mes_Resultado(id);

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Analise_RamoAtividade_{0}_{1:yyyyMMddHHmm}.pdf", ViewBag.NomeRamoAtividade, data_atual);

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // gera PDF
            string viewPartial = "_Analise_RamoAtividade_Mes_Calculo_PDF";

            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            // salva PDF em arquivo
            var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

            byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
            var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
            fileStream.Write(dataPDF, 0, dataPDF.Length);
            fileStream.Close();

            // retorna nome do arquivo
            var returnedData = new
            {
                nomeArquivo = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Analise Ramo de Atividade Mês XLS
        public ActionResult Analise_RamoAtividade_Mes_XLS()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // GUID
            Guid id = ViewBag.taskID;

            // ramo de atividade
            int IDRamoAtividade = ViewBag._IDRamoAtividade;

            // data atual
            string data_str = string.Format("{0:g}", DateTime.Now);

            // calcula
            Analise_RamoAtividade_Mes_Show(0, IDRamoAtividade, data_str, data_str);

            // resultado
            Analise_RamoAtividade_Mes_Resultado(id);

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            workbook = XLS_Analise_RamoAtividade_Mes();

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // data e hora
            DateTime data_atual = DateTime.Now;

            // nome do arquivo
            string nomeArquivo = string.Format("Analise_RamoAtividade_{0}_{1:yyyyMMddHHmm}.xls", ViewBag.NomeRamoAtividade, data_atual);

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // Analise Ramo de Atividade Mês XLS
        private HSSFWorkbook XLS_Analise_RamoAtividade_Mes()
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // ANALISE RAMO DE ATIVIDADE
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Análise Ramo de Atividade");

            // cabecalho
            string[] cabecalho = { "Data", "Consumo (MWh)", "Referência (MWh)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            AnaliseRamoAtividade_Mes ramoAtividades = ViewBag.ramoAtividades;

            var Consumo = new double[33];
            var Referencia = new double[33];
            var DatasN = new DateTime[33];

            if (ViewBag.Consumo != null && ViewBag.Referencia != null && ViewBag.DatasN != null)
            {
                Consumo = ViewBag.Consumo;
                Referencia = ViewBag.Referencia;
                DatasN = ViewBag.DatasN;
            }

            int i;
            IRow row;

            // valores
            if (ramoAtividades != null)
            {
                // número de dias do mês
                int NumDiasMes = 0;

                // data atual
                if (ViewBag.DataAtualN != null)
                {
                    DateTime data_atual = ViewBag.DataAtualN;

                    // número de dias do mês
                    NumDiasMes = DateTime.DaysInMonth(data_atual.Year, data_atual.Month);
                }

                for (i = 1; i < NumDiasMes+1; i++)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // data
                    datahoraCelulaXLS(row, 0, DatasN[i], _dataStyle);

                    // consumo atual
                    numeroCelulaXLS(row, 1, Consumo[i], _1CellStyle);

                    // consumo referencia
                    numeroCelulaXLS(row, 2, Referencia[i], _1CellStyle);

                    // proxima
                    rowIndex++;
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 8000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS_RamoAtividade(workbook, sheet, "Ramo de Atividade", _negritoCellStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }

        // GET: Analise Ramo de Atividade XLS Mês Download
        [HttpGet]
        public virtual ActionResult Analise_RamoAtividade_Mes_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }


        // GET: Analise Ramo de Atividade Mês Show
        public void Analise_RamoAtividade_Mes_Show(int Navegacao, int IDRamoAtividade, string DataAtual, string DataReferencia)
        {
            // caso forcar data
            if (Navegacao == 100)
            {
                // data atual
                DateTime dateValue = DateTime.Parse(DataAtual);
                CookieStore.SalvaCookie_Datahora("Relat_Data", dateValue);

                // data referencia
                dateValue = DateTime.Parse(DataReferencia);
                CookieStore.SalvaCookie_Datahora("Relat_DataFim", dateValue);
            }

            // le cookies
            LeCookies_SmartEnergy();

            // le cookie datahora atual
            DateTime data_atual = CookieStore.LeCookie_Datahora("Relat_Data");
            DateTime data_referencia = CookieStore.LeCookie_Datahora("Relat_DataFim");

            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -3:    // mes anterior

                    data_atual = data_atual.AddMonths(-1);
                    data_referencia = data_referencia.AddMonths(-1);
                    break;

                case 3:    // mes seguinte

                    data_atual = data_atual.AddMonths(1);
                    data_referencia = data_referencia.AddMonths(1);
                    break;

                case -4:    // ano anterior

                    data_atual = data_atual.AddYears(-1);
                    data_referencia = data_referencia.AddYears(-1);
                    break;

                case 4:    // ano seguinte

                    data_atual = data_atual.AddYears(1);
                    data_referencia = data_referencia.AddYears(-1);
                    break;
            }

            // inverte
            if (data_atual < data_referencia)
            {
                DateTime data_temp = data_atual;
                data_atual = data_referencia;
                data_referencia = data_temp;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", data_atual);
            CookieStore.SalvaCookie_Datahora("Relat_DataFim", data_referencia);

            CookieStore.SalvaCookie_Int("_IDRamoAtividade", IDRamoAtividade);

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", data_atual);
            ViewBag.DataAtualN = data_atual;
            ViewBag.DataTextoAtual = string.Format("{0:MM/yyyy}", data_atual);

            // data referencia
            ViewBag.DataReferencia = string.Format("{0:MM/yyyy}", data_referencia);
            ViewBag.DataReferenciaN = data_referencia;
            ViewBag.DataTextoReferencia = string.Format("{0:MM/yyyy}", data_referencia);

            // le tipos RamoAtividade
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposRamoAtividade = listatiposMetodos.ListarTodos("TipoRamoAtividade");

            ViewBag.listatiposRamoAtividade = listatiposRamoAtividade;

            return;
        }


        // estruturas
        private static IDictionary<Guid, int> tasks_Mes = new Dictionary<Guid, int>();
        private AnaliseRamoAtividade_Mes analise_RamoAtividade_Mes_tmp = new AnaliseRamoAtividade_Mes();
        private static IDictionary<Guid, AnaliseRamoAtividade_Mes> analise_RamoAtividade_Mes = new Dictionary<Guid, AnaliseRamoAtividade_Mes>();

        public ActionResult Analise_RamoAtividade_Mes_IniciaCalculo()
        {
            // taskID
            Guid taskId = Guid.NewGuid();
            tasks_Mes.Add(taskId, 0);

            // salva cookie
            CookieStore.SalvaCookie_GUID("taskID", taskId);

            // le cookies
            LeCookies_SmartEnergy();

            // ramo de atividade
            int IDRamoAtividade = ViewBag._IDRamoAtividade;

            // le cookie datahora atual
            DateTime data_atual = CookieStore.LeCookie_Datahora("Relat_Data");
            DateTime data_referencia = CookieStore.LeCookie_Datahora("Relat_DataFim");

            // inverte
            if (data_atual < data_referencia)
            {
                DateTime data_temp = data_atual;
                data_atual = data_referencia;
                data_referencia = data_temp;

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Relat_Data", data_atual);
                CookieStore.SalvaCookie_Datahora("Relat_DataFim", data_referencia);
            }

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", data_atual);
            ViewBag.DataAtualN = data_atual;
            ViewBag.DataTextoAtual = string.Format("{0:MM/yyyy}", data_atual);

            // data referencia
            ViewBag.DataReferencia = string.Format("{0:MM/yyyy}", data_referencia);
            ViewBag.DataReferenciaN = data_referencia;
            ViewBag.DataTextoReferencia = string.Format("{0:MM/yyyy}", data_referencia);

            // le clientes
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            List<ClientesDominio> clientes = clientesMetodos.ListarTodos();

            // le tipos RamoAtividade
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposRamoAtividade = listatiposMetodos.ListarTodos("TipoRamoAtividade");

            ViewBag.listatiposRamoAtividade = listatiposRamoAtividade;

            // limpa estrutura
            analise_RamoAtividade_Mes_tmp.ConsumoMesAnterior = 0.0;
            analise_RamoAtividade_Mes_tmp.ConsumoMesAtual = 0.0;
            analise_RamoAtividade_Mes_tmp.Diferenca = 0.0;
            analise_RamoAtividade_Mes_tmp.NumClientes = 0;
            analise_RamoAtividade_Mes_tmp.NumMedicoes = 0;

            for (int i = 0; i < 31; i++)
            {
                analise_RamoAtividade_Mes_tmp.Data[i] = new DateTime(data_atual.Year, data_atual.Month, 1, 0, 0, 0);
                analise_RamoAtividade_Mes_tmp.ConsumoMesAtual_Dia[i] = 0.0;
            }


            // task
            Task.Factory.StartNew(() =>
            {
                // barra de progresso
                int total = 0;
                int atual = 0;

                // percorre ramos de atividade
                if (listatiposRamoAtividade != null && clientes != null)
                {
                    // ramo de atividade
                    AnaliseRamoAtividade_Mes ramo = new AnaliseRamoAtividade_Mes();

                    ramo.IDRamoAtividade = IDRamoAtividade;
                    ramo.Descricao = "---";
                    ramo.ConsumoMesAnterior = 0.0;
                    ramo.ConsumoMesAtual = 0.0;
                    ramo.Diferenca = 0.0;
                    ramo.NumClientes = 0;
                    ramo.NumMedicoes = 0;

                    for (int i = 0; i < 31; i++)
                    {
                        ramo.Data[i] = new DateTime(data_atual.Year, data_atual.Month, 1, 0, 0, 0);
                        ramo.ConsumoMesAtual_Dia[i] = 0.0;
                    }

                    // encontra ramo
                    ListaTiposDominio tipoRamoAtividade = listatiposRamoAtividade.Find(t => t.ID == IDRamoAtividade);

                    if (tipoRamoAtividade != null)
                    {
                        // ramo de atividade
                        ramo.IDRamoAtividade = tipoRamoAtividade.ID;
                        ramo.Descricao = tipoRamoAtividade.Descricao;

                        // barra de progresso
                        total = clientes.Count;

                        // percorre ramos de atividade
                        foreach (ClientesDominio cliente in clientes)
                        {
                            // update task progress
                            double progresso = ((double)atual / (double)total) * 100;
                            tasks_Mes[taskId] = (int)progresso;
                            atual++;

                            // Calcula Analise Ramo de Atividade do cliente
                            Calc_Analise_RamoAtividade_Mes(cliente, ref ramo, data_atual, data_referencia);
                        }
                    }

                    // coloca na lista
                    analise_RamoAtividade_Mes_tmp = ramo;
                }

                // coloca resultado
                analise_RamoAtividade_Mes.Add(taskId, analise_RamoAtividade_Mes_tmp);

                // terminou
                tasks_Mes.Remove(taskId);
            });

            // retorna status
            return Json(taskId, JsonRequestBehavior.AllowGet);
        }

        public ActionResult Analise_RamoAtividade_Mes_Progress(Guid id)
        {
            return Json(tasks_Mes.Keys.Contains(id) ? tasks_Mes[id] : 100, JsonRequestBehavior.AllowGet);
        }

        public PartialViewResult _Analise_RamoAtividade_Mes_Calculo(Guid id)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // resultado
            Analise_RamoAtividade_Mes_Resultado(id);

            return PartialView();
        }

        public void Analise_RamoAtividade_Mes_Resultado(Guid id)
        {

            //
            // Resultado
            //
            AnaliseRamoAtividade_Mes ramoAtividades = analise_RamoAtividade_Mes.Keys.Contains(id) ? analise_RamoAtividade_Mes[id] : null;
            ViewBag.ramoAtividades = ramoAtividades;


            //
            // Ramo de Atividade
            //

            // le tipos RamoAtividade
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposRamoAtividade = listatiposMetodos.ListarTodos("TipoRamoAtividade");

            // nome ramo atividade
            string NomeRamoAtividade = "";

            if (listatiposRamoAtividade != null)
            {
                // encontra ramo
                ListaTiposDominio tipoRamoAtividade = listatiposRamoAtividade.Find(t => t.ID == ramoAtividades.IDRamoAtividade);

                if (tipoRamoAtividade != null)
                {
                    NomeRamoAtividade = tipoRamoAtividade.Descricao;
                }
            }

            ViewBag.NomeRamoAtividade = NomeRamoAtividade;


            //
            // Grafico
            //

            // le cookie datahora atual
            DateTime data_atual = CookieStore.LeCookie_Datahora("Relat_Data");

            // número de dias do mês
            int NumDiasMes = DateTime.DaysInMonth(data_atual.Year, data_atual.Month);

            // número de dias para a média
            double NumDiasMedia = NumDiasMes;

            DateTime hoje = DateTime.Now;

            // verifica se é o mês/ano atual
            if (data_atual.Month == hoje.Month && data_atual.Year == hoje.Year)
            {
                NumDiasMedia = hoje.Day - 1;

                if (NumDiasMedia <= 0)
                {
                    NumDiasMedia = 1;
                }
            }


            // grafico
            var Consumo = new double[33];
            var Referencia = new double[33];
            var Datas = new string[33];
            var DatasN = new DateTime[33];
            var Dias = new string[33];

            double Consumo_min_grafico = 0.0;
            double Consumo_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime(data_atual.Year, data_atual.Month, 1, 0, 0, 0);

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int j = 0;

            for (int i = 0; i < (NumDiasMes + 2); i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = string.Format("{0:0}", strData.Day);

                // proximo dias
                strData = strData.AddDays(1);

                // referencia
                if (NumDiasMedia > 0)
                {
                    Referencia[i] = (ramoAtividades.ConsumoMesAnterior / (double)NumDiasMedia) / 1000.0;
                }

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    Consumo[i] = ramoAtividades.ConsumoMesAtual_Dia[0] / 1000.0;
                }

                if (i == (NumDiasMes + 1))
                {
                    // zera
                    Consumo[i] = ramoAtividades.ConsumoMesAtual_Dia[NumDiasMes - 1] / 1000.0;
                }

                if (i >= 1 && i <= NumDiasMes)
                {
                    // copia
                    j = i - 1;

                    Consumo[i] = ramoAtividades.ConsumoMesAtual_Dia[j] / 1000.0;

                    // verifica consumo minimo
                    if (Consumo[i] < Consumo_min_grafico && Consumo[i] > 0.0)
                        Consumo_min_grafico = Consumo[i];

                    // verifica consumo maximo
                    if (Consumo[i] > Consumo_max_grafico)
                        Consumo_max_grafico = Consumo[i];


                    // verifica consumo minimo
                    if (Referencia[i] < Consumo_min_grafico && Referencia[i] > 0.0)
                        Consumo_min_grafico = Referencia[i];

                    // verifica consumo maximo
                    if (Referencia[i] > Consumo_max_grafico)
                        Consumo_max_grafico = Referencia[i];
                }
            }

            Consumo_min_grafico = Consumo_min_grafico - (Consumo_min_grafico * 0.1);

            if (Consumo_min_grafico < 0.0)
            {
                Consumo_min_grafico = 0.0;
            }

            Consumo_max_grafico = Consumo_max_grafico * 1.1;

            ViewBag.ConsMinGrafico = Consumo_min_grafico;
            ViewBag.ConsMaxGrafico = Consumo_max_grafico;

            ViewBag.Consumo = Consumo;
            ViewBag.Referencia = Referencia;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            ViewBag.NumDiasMes = NumDiasMes;

            return;
        }


        // Calcula Analise Ramo de Atividade
        private void Calc_Analise_RamoAtividade_Mes(ClientesDominio cliente, ref AnaliseRamoAtividade_Mes ramo, DateTime data_atual, DateTime data_referencia)
        {
            if (cliente == null)
            {
                return;
            }

            // hoje
            DateTime hoje = DateTime.Now;

            // Data Atual
            DateTime DataAtualIni = new DateTime(data_atual.Year, data_atual.Month, 1, 0, 15, 0);
            DateTime DataAtualFim = DataAtualIni.AddMonths(1);
            DataAtualFim = DataAtualFim.AddMinutes(-15);

            // verifica se é o mês/ano atual
            if (DataAtualIni.Month == hoje.Month && DataAtualIni.Year == hoje.Year)
            {
                DataAtualFim = new DateTime(data_atual.Year, data_atual.Month, hoje.Day, 0, 0, 0);
            }

            // numero de dias
            TimeSpan diferenca = DataAtualFim - DataAtualIni;
            int num_dias = diferenca.Days + 1;

            // Data Referencia
            DateTime DataReferenciaIni = new DateTime(data_referencia.Year, data_referencia.Month, 1, 0, 15, 0);
            DateTime DataReferencialFim = DataReferenciaIni.AddDays(num_dias);
            DataReferencialFim = DataReferencialFim.AddMinutes(-15);

            // verifica se cliente é do ramo de atividade
            if (cliente.IDRamoAtividade != ramo.IDRamoAtividade)
            {
                // não é do ramo, ignora
                return;
            }

            // consumos
            double ConsumoMesAtual_Cliente = 0.0;
            double ConsumoMesAnterior_Cliente = 0.0;

            // lê medições principais
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicoesMetodos.ListarPrincipais(cliente.IDCliente);

            if (medicoes == null)
            {
                // não tem medições principais, ignora
                return;
            }

            // percorre medições
            foreach (MedicoesDominio medicao in medicoes)
            {
                // le consumo do mês atual
                EN_Metodos enMetodos = new EN_Metodos();
                double ConsumoMesAtual_Medicao = enMetodos.ConsumoTotalPeriodo(cliente.IDCliente, medicao.IDMedicao, DataAtualIni, DataAtualFim);

                // le consumo do mês referência
                double ConsumoMesAnterior_Medicao = enMetodos.ConsumoTotalPeriodo(cliente.IDCliente, medicao.IDMedicao, DataReferenciaIni, DataReferencialFim);

                // verifica se teve consumo no cliente
                if (ConsumoMesAtual_Medicao > 0.0 && ConsumoMesAnterior_Medicao > 0.0)
                {
                    // incrementa numero de medições
                    ramo.NumMedicoes++;

                    // consumos
                    ConsumoMesAtual_Cliente += ConsumoMesAtual_Medicao;
                    ConsumoMesAnterior_Cliente += ConsumoMesAnterior_Medicao;

                    ramo.ConsumoMesAtual += ConsumoMesAtual_Medicao;
                    ramo.ConsumoMesAnterior += ConsumoMesAnterior_Medicao;

                    // calcula diferença
                    ramo.Diferenca = ramo.ConsumoMesAtual - ramo.ConsumoMesAnterior;

                    // porcentagem
                    if (ramo.ConsumoMesAnterior > 0.0)
                    {
                        ramo.Porcentagem = (ramo.Diferenca / ramo.ConsumoMesAnterior) * 100.0;
                    }

                    // le consumos diários
                    EN_ConsolidadoMensal ConsumoMes = enMetodos.ConsumoMes(cliente.IDCliente, medicao.IDMedicao, DataAtualIni);

                    if (ConsumoMes != null)
                    {
                        // copia consumo
                        for (int i = 0; i < 31; i++)
                        {
                            ramo.Data[i] = ConsumoMes.Data[i];
                            ramo.ConsumoMesAtual_Dia[i] += ConsumoMes.ConsumoAtivo[i];
                        }
                    }
                }
            }

            // verifica se teve consumo no cliente
            if (ConsumoMesAtual_Cliente > 0.0 && ConsumoMesAnterior_Cliente > 0.0)
            {
                // incrementa numero de clientes
                ramo.NumClientes++;
            }

            return;
        }
    }
}
