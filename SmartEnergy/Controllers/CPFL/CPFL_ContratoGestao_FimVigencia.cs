﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.IO;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{

    public partial class CPFLController
    {

        // Contrato Gestão - Fim da Vigência
        private void ContratoGestao_FimVigencia()
        {
            //
            // Alerta Fim de Vigência Contrato Gestão
            //
            // Alerta o cliente e o gestor quando algum contrato VIGENTE for vencer em 2 meses, caso não for Renovação Automática.
            //
            // Para o cliente é enviado o contrato que esta para vencer e em anexo uma planilha com todos os contratos dele.
            // Para o gestor é enviado em anexo uma planilha com os contratos de todos os clientes que estão para vencer.
            //
            // Esta rotina é executada todas as segundas-feiras às 7am
            //

            // log
            LogMessage("----------------------------------------------------------------------------------------");
            LogMessage(">> Alerta Fim de Vigência Contrato de Gestão");

            // data atual
            DateTime hoje = System.DateTime.Now;
            DateTime dataAtual = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 0, 0);

            // verifica se é dia de verificar o encerramento do contrato
            if (dataAtual.DayOfWeek != DayOfWeek.Monday)
            {
                // não é dia para verificar
                LogMessage(">> Hoje não deve verificar Encerramento Contrato de Gestão");
                return;
            }


            // Listas utilizadas
            PreparaListas_ContratoGestao();


            // verifica se existe clientes e empresas
            if (clientes != null && empresas != null)
            {
                // contrato a vencer do Gestor
                List<EmpresasDominio> contratosVencer_Gestor = new List<EmpresasDominio>();

                // percorre clientes
                foreach (ClientesDominio cliente in clientes)
                {
                    // todos os contratos do Cliente
                    List<EmpresasDominio> contratosTodos_Cliente = new List<EmpresasDominio>();

                    // contrato a vencer do Cliente
                    List<EmpresasDominio> contratosVencer_Cliente = new List<EmpresasDominio>();

                    // percorre contratos de todos os clientes
                    foreach (EmpresasDominio contrato in empresas)
                    {
                        //
                        // Verifica se deve analisar o contrato
                        //

                        // verifica se este contrato é deste cliente
                        if (contrato.IDCliente != cliente.IDCliente)
                        {
                            // próximo contrato
                            continue;
                        }

                        // verifica se contrato é vigente e com renovação NÃO automática
                        if (contrato.Contrato_Status != TIPO_CONTRATO_STATUS.Vigente || contrato.RenovacaoAutomatica)
                        {
                            // próximo contrato
                            continue;
                        }


                        //
                        // Insiro na lista de todos os contratos do Cliente
                        //
                        contratosTodos_Cliente.Add(contrato);


                        //
                        // Verifica se contrato encerra em 2 meses
                        //

                        // verifica se tempo da vigência configurado
                        if (dataAtual > contrato.Contrato_Fim)
                        {
                            // desconfigurado ou já passou fim da vigência, ignoro
                            continue;
                        }

                        // verifica se Fim da vigência maior que 2 meses
                        if (contrato.Contrato_Fim > dataAtual.AddMonths(2))
                        {
                            // ainda longe para encerrar
                            continue;
                        }

                        //
                        // Insiro na lista de contratos com encerramento próximo do Gestor
                        //
                        contratosVencer_Gestor.Add(contrato);


                        //
                        // Insiro na lista de contratos com encerramento próximo do Cliente
                        //
                        contratosVencer_Cliente.Add(contrato);
                    }

                    // verifica se existe contrato com encerramento próximo deste cliente
                    if (contratosVencer_Cliente.Count > 0)
                    {
                        // Contrato Gestão - Fim da Vigência - Enviar Alerta ao Cliente
                        ContratoGestao_FimVigencia_Cliente(cliente, contratosTodos_Cliente, contratosVencer_Cliente);
                    }
                }

                // verifica se existe contrato com encerramento próximo dos clientes
                if (contratosVencer_Gestor.Count > 0)
                {
                    // Contrato Gestçao - Fim da Vigência - Enviar Alerta ao Gestor
                    ContratoGestao_FimVigencia_Gestor(contratosVencer_Gestor);
                }
            }
        }

        // Contrato Gestão - Fim da Vigência - Enviar Alerta ao Cliente
        private void ContratoGestao_FimVigencia_Cliente(ClientesDominio cliente, List<EmpresasDominio> contratosTodos_Cliente, List<EmpresasDominio> contratosVencer_Cliente)
        {
            // data atual
            DateTime hoje = System.DateTime.Now;
            DateTime dataAtual = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 0, 0);

            // percorre contratos com encerramento próximo deste cliente
            foreach (EmpresasDominio contrato in contratosVencer_Cliente)
            {
                // tempo para o fim do contrato
                TimeSpan diferenca = contrato.Contrato_Fim - dataAtual;
                int num_dias = diferenca.Days;


                //
                // Usuários a enviar
                //

                // usuários a enviar
                List<UsuarioDominio> usuarios_enviar = new List<UsuarioDominio>();

                // usuários do cliente
                UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
                List<UsuarioDominio> usuarios = usuarioMetodos.ListarPorIDCliente(cliente.IDCliente);

                if (usuarios != null)
                {
                    // percorre usuarios
                    foreach (UsuarioDominio usuario in usuarios)
                    {
                        // verifica se usuário deseja receber alerta
                        if (usuario.Gestao_Contrato == 1)
                        {
                            usuarios_enviar.Add(usuario);
                        }
                    }
                }


                //
                // ENVIA EMAIL PARA USUARIOS
                //

                // log
                LogMessage(string.Format(">>>> Empresa ID{0} encerrando em {1} dias", contrato.IDEmpresa, num_dias));

                // envia email para todos destinatários simultaneamente
                ContratoGestao_FimVigencia_Cliente_EnviaBulkEmail(usuarios_enviar, contrato, num_dias);
            }

            return;
        }

        // Contrato Gestão - Fim da Vigência - Enviar Alerta ao Gestor
        private void ContratoGestao_FimVigencia_Gestor(List<EmpresasDominio> contratosVencer_Gestor)
        {
            // data atual
            DateTime hoje = System.DateTime.Now;
            DateTime dataAtual = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 0, 0);


            //
            // Prepara planilha de contratos
            //

            // buffer
            byte[] dataBuffer;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            // gera planilha de contratos
            workbook = ContratoGestao_FimVigencia_XLS(contratosVencer_Gestor, dataAtual);

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                dataBuffer = memoryStream.ToArray();
            }


            //
            // Usuários a enviar
            //

            // usuários a enviar
            List<UsuarioDominio> usuarios_enviar = new List<UsuarioDominio>();

            // usuários do cliente
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            List<UsuarioDominio> usuarios = usuarioMetodos.ListarOperadoresConsultor(CLIENTES_ESPECIAIS.CPFL);

            if (usuarios != null)
            {
                // percorre usuarios
                foreach (UsuarioDominio usuario in usuarios)
                {
                    // verifica se usuário deseja receber alerta
                    if (usuario.Gestao_Contrato == 1)
                    {
                        usuarios_enviar.Add(usuario);
                    }
                }
            }


            //
            // ENVIA EMAIL PARA USUARIOS
            //

            // log
            LogMessage(">> Enviando Contratos para os Gestores");

            // envia email para todos destinatários simultaneamente
            ContratoGestao_FimVigencia_Gestor_EnviaBulkEmail(usuarios_enviar, "ContratosGestao.xls", "application/vnd.ms-excel", dataBuffer);

            return;
        }

        // envia email para vários destinatários - cliente
        private void ContratoGestao_FimVigencia_Cliente_EnviaBulkEmail(List<UsuarioDominio> usuarios, EmpresasDominio contrato, int num_dias)
        {
            // verifica se tem usuarios
            if (usuarios == null)
            {
                // log
                LogMessage(">> Sem usuários para enviar");

                return;
            }

            if (usuarios.Count <= 0)
            {
                // log
                LogMessage(">> Sem usuários para enviar");

                return;
            }


            // assunto
            string assunto = string.Format("[Smart Energy] Encerramento do Contrato de Gestão - {0}", contrato.SiglaCCEE);

            // envia EMAIL
            var emailTemplate = "ContratoGestaoEncerramentoEmail_CPFL";

            var message = EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.UsuarioNome", "Olá");
            message = message.Replace("ViewBag.SiglaCCEE", contrato.SiglaCCEE);
            message = message.Replace("ViewBag.Vigencia_Inicio", string.Format("{0:d}", contrato.Contrato_Inicio));
            message = message.Replace("ViewBag.Vigencia_Fim", string.Format("{0:d}", contrato.Contrato_Fim));

            string texto_dias = string.Format("em <b>{0} dias</b>", num_dias);
            switch (num_dias)
            {
                case 0:
                    texto_dias = "<b>hoje</b>";
                    break;

                case 1:
                    texto_dias = "<b>amanhã</b>";
                    break;
            }
            message = message.Replace("ViewBag.Dias", texto_dias);

            // lista de emails
            List<string> emails = new List<string>();

            // percorre usuários
            foreach (UsuarioDominio usuario in usuarios)
            {
                // coloca na lista
                emails.Add(usuario.Email);
            }

            // envia
            EmailServices.SendBulkEmail(emails, assunto, message, "", "", null);

            // log
            foreach (UsuarioDominio usuario in usuarios)
            {
                // log
                LogMessage(string.Format(">>>> Enviou email para usuário [{0:000000}] {1}", usuario.IDUsuario, usuario.Email));
            }

            return;
        }

        // envia email para vários destinatários - gestor
        private void ContratoGestao_FimVigencia_Gestor_EnviaBulkEmail(List<UsuarioDominio> usuarios, string attachmentName, string attachmentType, byte[] attachment)
        {
            // verifica se tem usuarios
            if (usuarios == null)
            {
                // log
                LogMessage(">> Sem usuários para enviar");

                return;
            }

            if (usuarios.Count <= 0)
            {
                // log
                LogMessage(">> Sem usuários para enviar");

                return;
            }


            // assunto
            string assunto = "[Smart Energy] Encerramento dos Contratos de Gestão";

            // envia EMAIL
            var emailTemplate = "ContratoGestaoEncerramentoGestorEmail_CPFL";

            var message = EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.UsuarioNome", "Prezado Gestor");

            // lista de emails
            List<string> emails = new List<string>();

            // percorre usuários
            foreach (UsuarioDominio usuario in usuarios)
            {
                // coloca na lista
                emails.Add(usuario.Email);
            }

            // envia
            EmailServices.SendBulkEmail(emails, assunto, message, attachmentName, attachmentType, attachment);

            // log
            foreach (UsuarioDominio usuario in usuarios)
            {
                // log
                LogMessage(string.Format(">>>> Enviou email para usuário [{0:000000}] {1}", usuario.IDUsuario, usuario.Email));
            }

            return;
        }


        // Contrato Gestão - Fim da Vigência - planilha XLS
        private HSSFWorkbook ContratoGestao_FimVigencia_XLS(List<EmpresasDominio> contratos, DateTime dataAtual)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);


            //
            // CONTRATOS
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Contratos de Gestão");

            // cabecalho
            string[] cabecalho = { "Sigla CCEE", "Carteira", "Início do Contrato", "Fim do Contrato", "Valor Reajustado (R$)", "Renovação Automática" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            IRow row;

            // percorre contratos
            foreach (EmpresasDominio contrato in contratos)
            {
                //
                // Descrições
                //

                // Carteira
                string NomeCarteira = "";

                // carteira
                ListaTiposDominio carteira = listatiposCarteira.Find(e => e.ID == contrato.Carteira);

                if (carteira != null)
                {
                    // Carteira
                    NomeCarteira = carteira.Descricao;
                }


                //
                // Planilha
                //

                // adiciona linha
                row = sheet.CreateRow(rowIndex++);

                // SiglaCCEE
                textoCelulaXLS(row, 0, contrato.SiglaCCEE);

                // Carteira
                textoCelulaXLS(row, 1, NomeCarteira);

                // Início Contrato
                datahoraCelulaXLS(row, 2, contrato.Contrato_Inicio, _dataStyle);

                // Fim Contrato
                datahoraCelulaXLS(row, 3, contrato.Contrato_Fim, _dataStyle);

                // Valor Reajustado
                numeroCelulaXLS(row, 4, contrato.Reajuste_Valor, _2CellStyle);

                // Renovação Automática
                textoCelulaXLS(row, 5, (contrato.RenovacaoAutomatica ? "SIM" : "NÃO") );
            }

            // largura de cada coluna
            for (int i = 0; i < sheet.GetRow(0).LastCellNum; i++)
            {
                sheet.SetColumnWidth(i, 6000);
            }

            // retorna planilha
            return workbook;
        }
    }
}
