﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.IO;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{

    public partial class CPFLController
    {

        // Contrato Energia - Finalizou
        private void ContratoEnergia_Finalizou()
        {
            //
            // Alerta Fim de Vigência Contrato Energia
            //
            // Alerta o gestor quando algum contrato VIGENTE finalizou
            //
            // Para o gestor é enviado em anexo uma planilha com os contratos de todos os clientes que finalizaram.
            //
            // Esta rotina é executada todo dia às 7am
            //

            // log
            LogMessage("----------------------------------------------------------------------------------------");
            LogMessage(">> Alerta Finalizou Contrato de Energia");

            // data atual
            DateTime hoje = System.DateTime.Now;
            DateTime dataAtual = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 0, 0);


            // Listas utilizadas
            PreparaListas_ContratoEnergia();


            // verifica se existe clientes e empresas
            if (clientes != null && empresas != null)
            {
                // contratos finalizadosr do Gestor
                List<ContratosCCEEDominio> contratosFinalizados_Gestor = new List<ContratosCCEEDominio>();

                // percorre clientes
                foreach (ClientesDominio cliente in clientes)
                {
                    // contratos energia Vigentes
                    ContratosCCEEMetodos contratosMetodos = new ContratosCCEEMetodos();
                    List<ContratosCCEEDominio> contratos = contratosMetodos.ListarPorIDCliente(cliente.IDCliente, TIPO_CONTRATO_STATUS.Vigente);

                    if (contratos != null)
                    {
                        // percorre contratos
                        foreach (ContratosCCEEDominio contrato in contratos)
                        {
                            //
                            // Verifica se contrato encerrou
                            //

                            // verifica se encerrou
                            if (dataAtual <= contrato.Vigencia_Fim)
                            {
                                // desconfigurado ou ainda não finalizou, ignoro
                                continue;
                            }


                            // verifica se é dia seguinte do encerramento
                            DateTime dia_seguinte = contrato.Vigencia_Fim.AddDays(1);

                            // verifica se hoje é o dia seguinte do encerramento deste contrato
                            if (dataAtual.Day != dia_seguinte.Day || dataAtual.Month != dia_seguinte.Month || dataAtual.Year != dia_seguinte.Year)
                            {
                                // não é o dia seguinte do encerramento
                                continue;
                            }


                            //
                            // Atualizo estrutura com informações
                            //

                            if (contrato.IDEmpresa > 0)
                            {
                                // empresa
                                EmpresasDominio empresa = empresas.Find(e => e.IDEmpresa == contrato.IDEmpresa);

                                if (empresa != null)
                                {
                                    // SiglaCCEE
                                    contrato.SiglaCCEE = empresa.SiglaCCEE;
                                }
                            }

                            // insiro na lista de contratos finalizados do Gestor
                            contratosFinalizados_Gestor.Add(contrato);
                        }
                    }
                }

                // verifica se existe contrato finalizado
                if (contratosFinalizados_Gestor.Count > 0)
                {
                    // Contrato Energia - Finalizou - Enviar Alerta ao Gestor
                    ContratoEnergia_Finalizou_Gestor(contratosFinalizados_Gestor);
                }
            }
        }

        // Contrato Energia - Finalizou - Enviar Alerta ao Gestor
        private void ContratoEnergia_Finalizou_Gestor(List<ContratosCCEEDominio> contratosFinalizados_Gestor)
        {
            // data atual
            DateTime hoje = System.DateTime.Now;
            DateTime dataAtual = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 0, 0);

            //
            // Prepara planilha de contratos
            //

            // buffer
            byte[] dataBuffer;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            // gera planilha de contratos
            workbook = ContratoEnergia_Finalizou_XLS(contratosFinalizados_Gestor, dataAtual);

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                dataBuffer = memoryStream.ToArray();
            }


            //
            // Usuários a enviar
            //

            // usuários a enviar
            List<UsuarioDominio> usuarios_enviar = new List<UsuarioDominio>();

            // usuários do cliente
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            List<UsuarioDominio> usuarios = usuarioMetodos.ListarOperadoresConsultor(CLIENTES_ESPECIAIS.CPFL);

            if (usuarios != null)
            {
                // percorre usuarios
                foreach (UsuarioDominio usuario in usuarios)
                {
                    // verifica se usuário deseja receber alerta
                    if (usuario.Gestao_Energia == 1)
                    {
                        usuarios_enviar.Add(usuario);
                    }
                }
            }


            //
            // ENVIA EMAIL PARA USUARIOS
            //

            // log
            LogMessage(">> Enviando Contratos para os Gestores");

            // envia email para todos destinatários simultaneamente
            ContratoEnergia_Finalizou_Gestor_EnviaBulkEmail(usuarios_enviar, "ContratosEnergia.xls", "application/vnd.ms-excel", dataBuffer);

            return;
        }

        // envia email para vários destinatários - gestor
        private void ContratoEnergia_Finalizou_Gestor_EnviaBulkEmail(List<UsuarioDominio> usuarios, string attachmentName, string attachmentType, byte[] attachment)
        {
            // verifica se tem usuarios
            if (usuarios == null)
            {
                // log
                LogMessage(">> Sem usuários para enviar");

                return;
            }

            if (usuarios.Count <= 0)
            {
                // log
                LogMessage(">> Sem usuários para enviar");

                return;
            }


            // assunto
            string assunto = "[Smart Energy] Contratos de Energia Finalizados";

            // envia EMAIL
            var emailTemplate = "ContratoEnergiaFinalizadoGestorEmail_CPFL";

            var message = EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.UsuarioNome", "Prezado Gestor");

            // lista de emails
            List<string> emails = new List<string>();

            // percorre usuários
            foreach (UsuarioDominio usuario in usuarios)
            {
                // coloca na lista
                emails.Add(usuario.Email);
            }

            // envia
            EmailServices.SendBulkEmail(emails, assunto, message, attachmentName, attachmentType, attachment);

            // log
            foreach (UsuarioDominio usuario in usuarios)
            {
                // log
                LogMessage(string.Format(">>>> Enviou email para usuário [{0:000000}] {1}", usuario.IDUsuario, usuario.Email));
            }

            return;
        }


        // Contrato Energia - Finalizou - planilha XLS
        private HSSFWorkbook ContratoEnergia_Finalizou_XLS(List<ContratosCCEEDominio> contratos, DateTime dataAtual)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);


            //
            // CONTRATOS
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Contratos de Energia");

            // cabecalho
            string[] cabecalho = { "Sigla CCEE", "Código Contrato", "Início Vigência", "Término Vigência", "Comercializadora", "Preço Base (R$)", "Fonte", "SubMercado", "Ano", "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho", "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro", "Flex. Mín. (%)", "Flex Máx. (%)", "Sazo. Mín. (%)", "Sazo. Máx. (%)", "Mod. Mín (%)", "Mod. Máx. (%)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            IRow row;

            // percorre contratos
            foreach (ContratosCCEEDominio contrato in contratos)
            {
                //
                // Descrições
                //

                // Comercializadora
                string NomeComercializadora = "";

                // comercializadora
                AgentesDominio comercializadora = comercializadoras.Find(e => e.IDAgente == contrato.IDComercializadora);

                if (comercializadora != null)
                {
                    // Comercializadora
                    NomeComercializadora = comercializadora.Nome;
                }

                // Fonte
                string NomeFonte = "";

                // fonte
                ListaTiposDominio fonte = listatiposFonte.Find(e => e.ID == contrato.IDTipoFonte);

                if (fonte != null)
                {
                    // Fonte
                    NomeFonte = fonte.Descricao;
                }

                // SubMercado
                string NomeSubMercado = "";

                // submercado
                ListaTiposDominio submercado = listatiposSubSistema.Find(e => e.ID == contrato.IDSubSistema);

                if (submercado != null)
                {
                    // SubMercado
                    NomeSubMercado = submercado.Descricao;
                }

                // Preço base
                double preco_base = 0.0;

                if (contrato.Base_Preco_Flat)
                {
                    preco_base = contrato.Base_Preco;
                }
                else
                {
                    // busca preço base do mês
                    ContratosCCEE_PrecoBaseMetodos precoBaseMetodos = new ContratosCCEE_PrecoBaseMetodos();
                    ContratosCCEE_PrecoBaseDominio precoBase = precoBaseMetodos.ListarPorMes(contrato.IDContratoCCEE, dataAtual);

                    if (precoBase != null)
                    {
                        preco_base = precoBase.Base_Preco;
                    }
                }


                //
                // Planilha
                //

                // le volume
                ContratosCCEE_VolumeMetodos volumeMetodos = new ContratosCCEE_VolumeMetodos();
                List<ContratosCCEE_VolumeDominio> volumes = volumeMetodos.ListarPorId(contrato.IDContratoCCEE, contrato.Montante_Tipo);


                // calcula número de anos que o contrato possui
                int ano_Inicio = contrato.Vigencia_Inicio.Year;
                int ano_Fim = contrato.Vigencia_Fim.Year;

                // número de anos
                int numero_anos = (ano_Fim - ano_Inicio) + 1;

                // percorre anos
                for (int i = 0; i < numero_anos; i++)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex++);

                    // SiglaCCEE
                    textoCelulaXLS(row, 0, contrato.SiglaCCEE);

                    // Código Contrato
                    textoCelulaXLS(row, 1, contrato.Contrato_Codigo);

                    // Início Vigência
                    datahoraCelulaXLS(row, 2, contrato.Vigencia_Inicio, _dataStyle);

                    // Fim Vigência
                    datahoraCelulaXLS(row, 3, contrato.Vigencia_Fim, _dataStyle);

                    // Comercializadora
                    textoCelulaXLS(row, 4, NomeComercializadora);

                    // Preço base
                    numeroCelulaXLS(row, 5, contrato.Base_Preco, _2CellStyle);

                    // Fonte
                    textoCelulaXLS(row, 6, NomeFonte);

                    // SubMercado
                    textoCelulaXLS(row, 7, NomeSubMercado);

                    // Ano
                    DateTime vigencia_atual = contrato.Vigencia_Inicio.AddYears(i);
                    DateTime ano_atual = new DateTime(vigencia_atual.Year, 1, 1, 0, 0, 0);
                    numeroCelulaXLS(row, 8, ano_atual.Year, _intCellStyle);

                    // meses
                    for (int j = 0; j < 12; j++)
                    {
                        // mes
                        DateTime mes_atual = new DateTime(ano_atual.Year, j + 1, 1, 0, 0, 0);

                        // volume do mês
                        double contratado = 0.0;

                        // verifica se dentro da vigência
                        if (mes_atual >= contrato.Vigencia_Inicio && mes_atual <= contrato.Vigencia_Fim )
                        {
                            // verifica se tem volumes
                            if (volumes != null)
                            {
                                // verifica se Montante (MWm) = 1 por ano
                                if (contrato.Montante_Tipo == 0)
                                {
                                    // procura ano
                                    ContratosCCEE_VolumeDominio volume = volumes.Find(e => e.Data.Year == mes_atual.Year);

                                    if (volume != null)
                                    {
                                        // calcula número de horas do mês
                                        int num_horas = System.DateTime.DaysInMonth(mes_atual.Year, mes_atual.Month) * 24;

                                        // calcula contratado MWh do mês
                                        contratado = volume.Contratado * num_horas;
                                    }
                                }
                                else
                                {
                                    // procura mês
                                    ContratosCCEE_VolumeDominio volume = volumes.Find(e => e.Data == mes_atual);

                                    if (volume != null)
                                    {
                                        // contratado MWh do mês
                                        contratado = volume.Contratado;
                                    }
                                }
                            }
                        }

                        // contratado do mês
                        numeroCelulaXLS(row, 9 + j, contratado, _3CellStyle);
                    }

                    // Flexibilidade mínima
                    numeroCelulaXLS(row, 21, contrato.Flexibilidade_Minima, _2CellStyle);

                    // Flexibilidade máxima
                    numeroCelulaXLS(row, 22, contrato.Flexibilidade_Maxima, _2CellStyle);

                    // Sazonalidade mínima
                    numeroCelulaXLS(row, 23, contrato.Sazonalizacao_Minima, _2CellStyle);

                    // Sazonalidade máxima
                    numeroCelulaXLS(row, 24, contrato.Sazonalizacao_Maxima, _2CellStyle);

                    // Modulação mínima
                    numeroCelulaXLS(row, 25, contrato.Modulacao_Minima, _2CellStyle);

                    // Modulação máxima
                    numeroCelulaXLS(row, 26, contrato.Modulacao_Maxima, _2CellStyle);
                }
            }

            // largura de cada coluna
            for (int i = 0; i < sheet.GetRow(0).LastCellNum; i++)
            {
                if (i >= 8 && i <= 20)
                {
                    sheet.SetColumnWidth(i, 4000);
                }
                else
                {
                    sheet.SetColumnWidth(i, 6000);
                }
            }

            // retorna planilha
            return workbook;
        }
    }
}
