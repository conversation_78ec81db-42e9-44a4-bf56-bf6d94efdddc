﻿using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.Net.Mime;
using System.Runtime.InteropServices;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{

    public partial class CPFLController
    {
        // fatura de energia eletrica
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_Fatura", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_Fatura(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, int id_simulacao, char tipo_contrato, char IDEstTarifaria, char EhProjetada, ref DATAHORA pdatahora_ini, ref DATAHORA pdatahora_fim, ref RESULT_ENERGIA_FATURA pfatura, ref RESULT_ENERGIA_FATURA pfatura_sim);


        // Provisionamento Semanal
        private void ProvisionamentoSemanal()
        {
            //
            // Relatório Provisionamento Semanal
            //
            // Envia ao cliente o relatório de Provisionamento Semanal dos contratos dos Agentes/Filiais.
            //
            // Para o cliente é enviado o relatório em PDF do Provisionamento Semanal, um para cada Agente/Filial que possua contrato de energia.
            //
            // Esta rotina é executada todo domingo, após o dia 10.
            //

            // log
            LogMessage("----------------------------------------------------------------------------------------");
            LogMessage(">> Relatório Provisionamento de Consumo");

            // data atual
            DateTime hoje = System.DateTime.Now;
            DateTime dataAtual = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 0, 0);

            // verifica se hoje é domingo ou antes do dia 10
            if (hoje.DayOfWeek != DayOfWeek.Sunday || hoje.Day <= 10)
            {
                // não é dia para enviar
                LogMessage(">> Hoje não deve enviar Provisionamento de Consumo");
                return;
            }


            // Listas utilizadas
            PreparaListas_ProvisionamentoSemanal();


            // verifica se existe clientes
            if (clientes != null)
            {
                // percorre clientes
                foreach (ClientesDominio cliente in clientes)
                {
                    // le empresas do cliente
                    EmpresasMetodos empresasMetodos = new EmpresasMetodos();
                    List<EmpresasDominio> empresas_lidas = empresasMetodos.ListarPorIDCliente(cliente.IDCliente);

                    if (empresas_lidas != null)
                    {
                        // percorre empresas
                        foreach (EmpresasDominio empresa in empresas_lidas)
                        {
                            //
                            // Prepara empresa
                            //

                            // monto CNPJ Razão Social
                            empresa.CNPJ_RazaoSocial = string.Format("{0} [{1}]", empresa.CNPJ, empresa.RazaoSocial);

                            // medição da unidade consumidora
                            MedicoesDominio medicao = new MedicoesDominio();
                            GatewaysDominio gateway = new GatewaysDominio();
                            empresa.IDMedicao_UnidadeConsumidora = Encontra_IDMedicao_UnidadeConsumidora_Principal(empresa.IDEmpresa, ref medicao, ref gateway);

                            if (medicao.IDMedicao > 0)
                            {
                                empresa.NomeMedicao = string.Format("[{0}] {1}", medicao.IDMedicao, medicao.Nome);
                                empresa.IDSubSistema = medicao.IDSubSistema;
                            }


                            //
                            // Contratos de Energia
                            //

                            // verifica qual o tipo:
                            // Tipo 1 = 1 contrato para 1 unidade consumidora
                            // Tipo 2 = 1 contrato para N unidades consumidoras
                            // Tipo 3 = N contratos para N unidades consumidoras

                            // le contratos CCEE vigentes da empresa
                            ContratosCCEEMetodos contratosMetodos = new ContratosCCEEMetodos();
                            List<ContratosCCEEDominio> contratosEnergia = contratosMetodos.ListarPorIDEmpresa(empresa.IDEmpresa, TIPO_CONTRATO_STATUS.Vigente);

                            // verifica se possui contratos
                            if (contratosEnergia == null)
                            {
                                // empresa não possui contratos, ignoro
                                continue;
                            }

                            // número de contratos
                            int numContratos = contratosEnergia.Count;

                            // verifica se possui contratos
                            if (numContratos == 0)
                            {
                                // empresa não possui contratos, ignoro
                                continue;
                            }

                            // ordem
                            int ordem = 1;

                            // percorre contratos para resgatar nome dos clientes e empresas
                            foreach (ContratosCCEEDominio contrato in contratosEnergia)
                            {
                                // informações
                                contrato.NomeCliente = cliente.Nome;
                                contrato.RazaoSocial = empresa.RazaoSocial;
                                contrato.SiglaCCEE = empresa.SiglaCCEE;
                                contrato.CNPJ = empresa.CNPJ;
                                contrato.IDEstado = empresa.IDEstado;
                                contrato.IDCidade = empresa.IDCidade;
                                contrato.Logo = empresa.Logo;

                                // formata datas auxiliares
                                contrato.Vigencia_Inicio_aux = String.Format("{0:d}", contrato.Vigencia_Inicio);
                                contrato.Vigencia_Inicio_Sort_aux = String.Format("{0:yyyyMMddHHmmss}", contrato.Vigencia_Inicio);
                                contrato.Vigencia_Fim_aux = String.Format("{0:d}", contrato.Vigencia_Fim);
                                contrato.Vigencia_Fim_Sort_aux = String.Format("{0:yyyyMMddHHmmss}", contrato.Vigencia_Fim);

                                // ordena
                                contrato.Ordem = ordem++;

                                // verifica se tem apenas 1 contrato
                                if (numContratos == 1)
                                {
                                    // possui apenas 1 contrato, torno ele considerado
                                    contrato.Considera = 1;
                                }

                                // atualiza ordem
                                contratosMetodos.Alterar_Ordem_Considera(contrato.IDContratoCCEE, contrato.Ordem, contrato.Considera);
                            }

                            ViewBag.contratosEnergia = contratosEnergia;
                            ViewBag.numContratos = numContratos;


                            //
                            // Usuários a enviar
                            //

                            // usuários a enviar
                            List<UsuarioDominio> usuarios_enviar = new List<UsuarioDominio>();

                            // usuários do cliente
                            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
                            List<UsuarioDominio> usuarios = usuarioMetodos.ListarPorIDCliente(cliente.IDCliente);

                            if (usuarios == null)
                            {
                                // cliente não possui usuários para receber, ignoro
                                continue;
                            }

                            // percorre usuarios
                            foreach (UsuarioDominio usuario in usuarios)
                            {
                                // verifica se usuário deseja receber alerta
                                if (usuario.Gestao_Energia == 1)
                                {
                                    usuarios_enviar.Add(usuario);
                                }
                            }

                            if (usuarios_enviar.Count == 0)
                            {
                                // cliente não possui usuários para receber, ignoro
                                continue;
                            }


                            //
                            // Calcula Provisionamento Semanal
                            //

                            // verifica se tem 1 contrato
                            if (numContratos == 1)
                            {
                                // Calcula Provisionamento Semanal Tipo 1 ou Tipo 2
                                Calc_Provisionamento_Semanal_Tipo1_Tipo2(empresa.IDEmpresa, dataAtual, contratosEnergia[0]);

                                // Envia Provisionamento Semanal Tipo 1 ou Tipo 2 (1 contrato para N unidades)
                                Envia_ProvisionamentoSemanal(empresa.SiglaCCEE, dataAtual, usuarios_enviar, 1);
                            }

                            // verifica se tem N contratos
                            if (numContratos > 1)
                            {
                                // Calcula Provisionamento Semanal Tipo 3
                                Calc_Provisionamento_Semanal_Tipo3(empresa.IDEmpresa, dataAtual, contratosEnergia);

                                // verifica quantidade de unidades consumidoras
                                List<Provisionamento_UnidadesConsumidoras> unidCons_Unificados = ViewBag.unidadesConsumidoras;

                                if (unidCons_Unificados != null)
                                {
                                    if (unidCons_Unificados.Count > 1)
                                    {
                                        // Envia Provisionamento Semanal Tipo 3 (N contratos para N unidades)
                                        Envia_ProvisionamentoSemanal(empresa.SiglaCCEE, dataAtual, usuarios_enviar, 3);
                                    }
                                    else
                                    {
                                        // Envia Provisionamento Semanal Tipo 3 (N contratos para 1 unidade)
                                        Envia_ProvisionamentoSemanal(empresa.SiglaCCEE, dataAtual, usuarios_enviar, 2);
                                    }
                                }

                            }
                        }
                    }
                }
            }

            return;
        }


        // envia provisionamento semanal
        private void Envia_ProvisionamentoSemanal(string SiglaCCEE, DateTime dataRelat, List<UsuarioDominio> usuarios_enviar, int tipo)
        {

            //
            // Prepara Anexo
            //

            // nome do arquivo
            string nomeArquivo = string.Format("ProvisionamentoConsumo_{0}_{1:yyyyMMdd}.pdf", SiglaCCEE, dataRelat);

            // partial view
            string viewPartial = "_Provisionamento_Semanal_Tipo1_Tipo2_PDF";

            if (tipo == 2)
            {
                viewPartial = "_Provisionamento_Semanal_Tipo3_1Unidade_PDF";
            }

            if (tipo == 3)
            {
                viewPartial = "_Provisionamento_Semanal_Tipo3_NUnidades_PDF";
            }

            // buffer
            byte[] dataBuffer;

            // tipo do anexo
            string attachmentType = MediaTypeNames.Application.Pdf;

            // rodape
            string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

            // PartialView como PDF
            var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
            {
                FileName = nomeArquivo,
                PageOrientation = Orientation.Portrait,
                PageSize = Size.A4,
                PageMargins = new Rotativa.Options.Margins(10, 10, 0, 10),
                CustomSwitches = footer,
                IsLowQuality = true
            };

            dataBuffer = pdfResult.BuildFile(this.ControllerContext);

            // verifica se apenas 1 usuário
            if (usuarios_enviar.Count == 1)
            {
                // envia email para 1 destinatário
                EnviaEmail_ProvisionamentoSemanal(usuarios_enviar[0], nomeArquivo, attachmentType, dataBuffer, tipo);
            }
            else
            {
                // envia email para todos destinatários simultaneamente
                EnviaBulkEmail_ProvisionamentoSemanal(usuarios_enviar, nomeArquivo, attachmentType, dataBuffer, tipo);
            }

            return;
        }

        // envia email
        private void EnviaEmail_ProvisionamentoSemanal(UsuarioDominio usuario, string attachmentName, string attachmentType, byte[] attachment, int tipo)
        {
            // assunto e template
            string assunto = "[Smart Energy] Provisionamento de Consumo - " + ViewBag.SiglaCCEE;
            var emailTemplate = "ProvisionamentoSemanal_Tipo3_Email_CPFL";

            if (tipo == 1)
            {
                assunto += " - " + ViewBag.Contrato_Codigo;
                emailTemplate = "ProvisionamentoSemanal_Tipo1_Tipo2_Email_CPFL";
            }

            // envia EMAIL
            var message = EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.UsuarioNome", usuario.NomeUsuario);
            message = message.Replace("ViewBag.SiglaCCEE", ViewBag.SiglaCCEE);
            message = message.Replace("ViewBag.RazaoSocial", ViewBag.RazaoSocial);
            message = message.Replace("ViewBag.Contrato_Codigo", ViewBag.Contrato_Codigo);
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);

            EmailServices.SendEmail(usuario.Email, assunto, message, attachmentName, attachmentType, attachment);

            // log
            LogMessage(string.Format(">>>> Enviou email para usuário [{0:000000}] {1}", usuario.IDUsuario, usuario.Email));

            return;
        }

        // envia email para vários destinatários
        private void EnviaBulkEmail_ProvisionamentoSemanal(List<UsuarioDominio> usuarios, string attachmentName, string attachmentType, byte[] attachment, int tipo)
        {
            // assunto e template
            string assunto = "[Smart Energy] Provisionamento de Consumo - " + ViewBag.SiglaCCEE;
            var emailTemplate = "ProvisionamentoSemanal_Tipo3_Email_CPFL";

            if (tipo == 1)
            {
                assunto += " - " + ViewBag.Contrato_Codigo;
                emailTemplate = "ProvisionamentoSemanal_Tipo1_Tipo2_Email_CPFL";
            }

            // envia EMAIL
            var message = EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.UsuarioNome", "Olá");
            message = message.Replace("ViewBag.SiglaCCEE", ViewBag.SiglaCCEE);
            message = message.Replace("ViewBag.RazaoSocial", ViewBag.RazaoSocial);
            message = message.Replace("ViewBag.Contrato_Codigo", "");
            message = message.Replace("ViewBag.DataTextoAtual", ViewBag.DataTextoAtual);

            // lista de emails
            List<string> emails = new List<string>();

            // percorre usuários
            foreach (UsuarioDominio usuario in usuarios)
            {
                // coloca na lista
                emails.Add(usuario.Email);
            }

            // envia
            EmailServices.SendBulkEmail(emails, assunto, message, attachmentName, attachmentType, attachment);

            // log
            foreach (UsuarioDominio usuario in usuarios)
            {
                // log
                LogMessage(string.Format(">>>> Enviou email para usuário [{0:000000}] {1}", usuario.IDUsuario, usuario.Email));
            }

            return;
        }



        //
        // AUXILIARES
        //

        // preenche info da unidade consumidora
        private bool InfoUnidadeConsumidora(ref Provisionamento_UnidadesConsumidoras unidConsumidora, int IDEmpresa, int IDMedicao = 0)
        {
            // le info da empresa
            EmpresasMetodos empresaMetodos = new EmpresasMetodos();
            EmpresasDominio empresa = empresaMetodos.ListarPorId(IDEmpresa);

            if (empresa != null)
            {
                // IDCliente e IDEmpresa
                unidConsumidora.IDCliente = empresa.IDCliente;
                unidConsumidora.IDEmpresa = IDEmpresa;

                // medicao e gateway
                MedicoesDominio medicao = new MedicoesDominio();
                GatewaysDominio gateway = new GatewaysDominio();

                if (IDMedicao > 0)
                {
                    unidConsumidora.IDMedicao = IDMedicao;

                    MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                    medicao = medicoesMetodos.ListarPorId(IDMedicao);
                }
                else
                {
                    unidConsumidora.IDMedicao = Encontra_IDMedicao_UnidadeConsumidora_Principal(IDEmpresa, ref medicao, ref gateway);
                }

                unidConsumidora.RazaoSocial = empresa.RazaoSocial;
                unidConsumidora.SiglaCCEE = empresa.SiglaCCEE;
                unidConsumidora.CNPJ = empresa.CNPJ;
                unidConsumidora.CNPJ_RazaoSocial = string.Format("{0} [{1}]", unidConsumidora.CNPJ, unidConsumidora.RazaoSocial);

                if (medicao != null)
                {
                    unidConsumidora.IDGateway = medicao.IDGateway;
                    unidConsumidora.PontoMedicao = medicao.PontoMedicao;
                    unidConsumidora.IDSubSistema = medicao.IDSubSistema;
                    unidConsumidora.IDContratoMedicao = medicao.IDContratoMedicao;
                    unidConsumidora.IDEstruturaTarifaria = medicao.IDEstruturaTarifaria;
                }

                return (true);
            }

            // erro
            unidConsumidora.IDCliente = 0;
            unidConsumidora.IDEmpresa = 0;
            unidConsumidora.IDMedicao = 0;
            unidConsumidora.RazaoSocial = "---";
            unidConsumidora.SiglaCCEE = "---";
            unidConsumidora.CNPJ = "00.000.000/0000-00";
            unidConsumidora.CNPJ_RazaoSocial = string.Format("{0} [{1}]", unidConsumidora.CNPJ, unidConsumidora.RazaoSocial);
            unidConsumidora.PontoMedicao = "---";
            unidConsumidora.IDSubSistema = 0;

            return (false);
        }

    }
}
