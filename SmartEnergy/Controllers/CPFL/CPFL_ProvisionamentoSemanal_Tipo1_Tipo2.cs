﻿using System;
using System.Collections.Generic;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class CPFLController
    {

        //
        // PROVISIONAMENTO SEMANAL TIPO 1 OU TIPO 2
        //
        //
        // Tipo 1 = 1 contrato para 1 unidade consumidora
        // Tipo 2 = 1 contrato para N unidades consumidoras
        //

        // Calcula Provisionamento Semanal Tipo 1 ou Tipo 2
        private void Calc_Provisionamento_Semanal_Tipo1_Tipo2(int IDEmpresa, DateTime DataAtual, ContratosCCEEDominio contrato_energia)
        {

            //
            // Prepara Provisionamento Semanal
            //
            Provisionamento_Tipo1_Tipo2 provisionamento = new Provisionamento_Tipo1_Tipo2();

            // prepara provisionamento semanal
            PreparaProvisionamentoSemanal_Tipo1_Tipo2(IDEmpresa, DataAtual, ref provisionamento);


            //
            // Calcula Provisionamento Semanal
            //

            // calcula provisionamento
            Calc_Tipo1_Tipo2(IDEmpresa, ref provisionamento, contrato_energia);
 

            //
            // Resultado
            //

            ViewBag.ClienteNome = contrato_energia.NomeCliente;
            ViewBag.RazaoSocial = contrato_energia.RazaoSocial;
            ViewBag.SiglaCCEE = contrato_energia.SiglaCCEE;
            ViewBag.Contrato_Codigo = string.Format("Contrato {0}", contrato_energia.Contrato_Codigo);
            ViewBag.DataTextoAtual = string.Format("{0:MMMM/yyyy}", DataAtual);


            return;
        }


        // Prepara Provisionamento Semanal Tipo 1 ou Tipo 2
        private void PreparaProvisionamentoSemanal_Tipo1_Tipo2(int IDEmpresa, DateTime DataAtual, ref Provisionamento_Tipo1_Tipo2 provisionamento)
        {

            // provisionamento semanal
            provisionamento.IDEmpresa = IDEmpresa;
            provisionamento.Data = DataAtual;
            provisionamento.Data_Texto = string.Format("{0:dd/MM/yyyy}", DataAtual);

            return;
        }


        // Calcula Provisionamento Semanal Tipo 1 ou Tipo 2
        private void Calc_Tipo1_Tipo2(int IDEmpresa, ref Provisionamento_Tipo1_Tipo2 provisionamento, ContratosCCEEDominio contrato_gestao)
        {
            //
            // Informações Gerais
            //
            provisionamento.IDContratoCCEE = contrato_gestao.IDContratoCCEE;
            provisionamento.Contrato_Codigo = contrato_gestao.Contrato_Codigo;

            //
            // Unidades Consumidoras do Contrato
            //
            List<Provisionamento_UnidadesConsumidoras> unidadesConsumidoras = new List<Provisionamento_UnidadesConsumidoras>();


            //
            // Unidades consumidoras do contrato (inclusive o garantidor)
            //

            // garantidor
            Provisionamento_UnidadesConsumidoras unidade = new Provisionamento_UnidadesConsumidoras();

            // IDComercializadora
            unidade.IDComercializadora = contrato_gestao.IDComercializadora;

            // preenche info da unidade consumidora
            if (InfoUnidadeConsumidora(ref unidade, contrato_gestao.IDEmpresa))
            {
                // percentual de carga
                unidade.PercentualCarga_Possui = contrato_gestao.PercentualCarga_Possui;
                unidade.PercentualCarga_Valor = contrato_gestao.PercentualCarga_Valor;

                // adiciona na lista de unidades consumidoras
                unidadesConsumidoras.Add(unidade);

                // adiciona na lista de unidades consumidoras do provisionamento (sem uso no Tipo1 e Tipo2)
                provisionamento.unidadesConsumidoras.Add(unidade);
            }

            // unidades atendidas pelo contrato
            ContratosCCEE_UnidadesAtendidasMetodos unidadesAtendidasMetodos = new ContratosCCEE_UnidadesAtendidasMetodos();
            List<ContratosCCEE_UnidadesAtendidasDominio> unidadesAtendidas = unidadesAtendidasMetodos.ListarPorId(contrato_gestao.IDContratoCCEE);

            if (unidadesAtendidas != null)
            {
                foreach (ContratosCCEE_UnidadesAtendidasDominio unidadeAtendida in unidadesAtendidas)
                {
                    // unidade consumidora
                    Provisionamento_UnidadesConsumidoras unidConsumidora = new Provisionamento_UnidadesConsumidoras();

                    // IDComercializadora
                    unidConsumidora.IDComercializadora = contrato_gestao.IDComercializadora;

                    // preenche info da unidade consumidora
                    if (InfoUnidadeConsumidora(ref unidConsumidora, unidadeAtendida.IDEmpresa))
                    {
                        // percentual de carga
                        unidConsumidora.PercentualCarga_Possui = contrato_gestao.PercentualCarga_Possui;
                        unidConsumidora.PercentualCarga_Valor = unidadeAtendida.PercentualCarga_Valor;

                        // adiona na lista de unidades consumidoras
                        unidadesConsumidoras.Add(unidConsumidora);

                        // adiciona na lista de unidades consumidoras do provisionamento (sem uso no Tipo1 e Tipo2)
                        provisionamento.unidadesConsumidoras.Add(unidConsumidora);
                    }
                }
            }


            //
            // Analise Contrato x Consumo
            //
            AnaliseContratoConsumo_Tipo1_Tipo2(ref provisionamento, contrato_gestao, unidadesConsumidoras);


            //
            // Resultado
            //

            ViewBag.ProvisionamentoSemanal = provisionamento;
            ViewBag.unidadesConsumidoras = unidadesConsumidoras;

            return;
        }


        // Analise Contrato x Consumo
        private void AnaliseContratoConsumo_Tipo1_Tipo2(ref Provisionamento_Tipo1_Tipo2 provisionamento, ContratosCCEEDominio contrato_gestao, List<Provisionamento_UnidadesConsumidoras> unidadesConsumidoras)
        {
            //
            // Consumos Consolidado e Estimado do Mês
            //
            EN_ConsolidadoMensal consumo_mensal_total = new EN_ConsolidadoMensal();
            ConsumoConsolidadoEstimado_Mes(provisionamento.Data, contrato_gestao, ref consumo_mensal_total, unidadesConsumidoras);

            //
            // Consumos Consolidado 12 meses
            //

            // coloca contrato numa lista
            List<ContratosCCEEDominio> contratosEnergia = new List<ContratosCCEEDominio>();
            contratosEnergia.Add(contrato_gestao);

            ConsumoConsolidado_12meses(provisionamento.Data, contratosEnergia, consumo_mensal_total, unidadesConsumidoras);

            //
            // Analise Contrato x Consumo
            //

            // Contrato da Sazo Mensal
            ContratosCCEE_SazonalizacaoMetodos sazonalizacaoMetodos = new ContratosCCEE_SazonalizacaoMetodos();
            provisionamento.Contrato = sazonalizacaoMetodos.SazonalizacaoMes(contrato_gestao.IDContratoCCEE, provisionamento.Data);

            // Take Mínimo
            provisionamento.Take_Minimo = provisionamento.Contrato * (contrato_gestao.Flexibilidade_Minima / 100.0);

            // Take Máximo
            provisionamento.Take_Maximo = provisionamento.Contrato * (contrato_gestao.Flexibilidade_Maxima / 100.0);

            // Consumo Consolidado e Estimado
            if (consumo_mensal_total != null)
            {
                // consumo consolidado
                provisionamento.Consumo_Consolidado = consumo_mensal_total.Consumo_Consolidado;

                // consumo estimado
                provisionamento.Consumo_Estimado = consumo_mensal_total.Consumo_Estimado;
            }

            // Consumo Total Bruto
            provisionamento.Consumo_Total_Bruto = provisionamento.Consumo_Consolidado + provisionamento.Consumo_Estimado;

            // Perdas
            provisionamento.Perdas = contrato_gestao.Perdas;

            // Previsão de Consumo
            provisionamento.Previsao_Consumo = provisionamento.Consumo_Total_Bruto * (1.0 + (provisionamento.Perdas / 100.0));

            // PROINFA
            Calcula_PROINFA(ref provisionamento, contrato_gestao, unidadesConsumidoras);

            // Necessidade de contratação
            provisionamento.Necessidade_Contratacao = provisionamento.Previsao_Consumo;

            if (provisionamento.Proinfa_Abate)
            {
                provisionamento.Necessidade_Contratacao -= provisionamento.PROINFA;
            }
            else
            {
                provisionamento.PROINFA = 0.0;
            }

            // Total Contrato e Resultado
            provisionamento.Total_Contrato = provisionamento.Necessidade_Contratacao;
            provisionamento.Resultado = 0.0;

            // verifica se menor que a flexibilidade mínima
            if (provisionamento.Necessidade_Contratacao < provisionamento.Take_Minimo)
            {
                provisionamento.Total_Contrato = provisionamento.Take_Minimo;
                provisionamento.Resultado = provisionamento.Take_Minimo - provisionamento.Necessidade_Contratacao;
            }

            // verifica se maior que a flexibilidade máxima
            if (provisionamento.Necessidade_Contratacao > provisionamento.Take_Maximo)
            {
                provisionamento.Total_Contrato = provisionamento.Take_Maximo;
                provisionamento.Resultado = provisionamento.Take_Maximo - provisionamento.Necessidade_Contratacao;
            }

            //
            // Rateio
            //
            Calcula_Rateio_Tipo1_Tipo2(ref provisionamento, contrato_gestao, ref unidadesConsumidoras);


            //
            // Fatura
            //
            Calcula_Fatura_Tipo1_Tipo2(provisionamento, ref unidadesConsumidoras);


            //
            // Resultado
            //

            ViewBag.PrevisaoConsumo = provisionamento.Previsao_Consumo;
            ViewBag.Contrato = provisionamento.Contrato;

            double MaxGrafico = (provisionamento.Previsao_Consumo > provisionamento.Contrato) ? provisionamento.Previsao_Consumo : provisionamento.Contrato;
            MaxGrafico *= 1.1;
            ViewBag.MaxGrafico = MaxGrafico;

            return;
        }

        // Consumo Consolidado e Estimado Total do Mês
        private void ConsumoConsolidadoEstimado_Mes(DateTime Data, ContratosCCEEDominio contrato_gestao, ref EN_ConsolidadoMensal consumo_mensal_total, List<Provisionamento_UnidadesConsumidoras> unidadesConsumidoras)
        {
            //
            // Consumos Consolidado e Estimado do mês
            //

            // zera valores do total
            consumo_mensal_total.Consumo_Consolidado = 0.0;
            consumo_mensal_total.Consumo_Estimado = 0.0;
            consumo_mensal_total.Consumo_Total_Bruto = 0.0;

            for (int i = 0; i < 31; i++)
            {
                consumo_mensal_total.Data[i] = Data;
                consumo_mensal_total.ConsumoAtivo[i] = 0.0;
                consumo_mensal_total.Periodo[i] = 0;
                consumo_mensal_total.NumReg_Dia[i] = 0;
            }

            // percorre unidades consumidoras
            bool primeira_unidade = true;

            foreach (Provisionamento_UnidadesConsumidoras unidConsumidora in unidadesConsumidoras)
            {
                // calculo consumos consolidado e estimado para cada unidade consumidora
                EN_Metodos enMetodos = new EN_Metodos();
                EN_ConsolidadoMensal consumo_mensal = enMetodos.ConsumoConsolidadoEstimado(unidConsumidora.IDCliente, unidConsumidora.IDMedicao, Data);

                if (consumo_mensal != null)
                {
                    // consumo total bruto da unidade consumidora
                    consumo_mensal.Consumo_Total_Bruto = consumo_mensal.Consumo_Consolidado + consumo_mensal.Consumo_Estimado;

                    // copia
                    unidConsumidora.consumoMensal = consumo_mensal;

                    // soma no total
                    consumo_mensal_total.Consumo_Consolidado += consumo_mensal.Consumo_Consolidado;
                    consumo_mensal_total.Consumo_Estimado += consumo_mensal.Consumo_Estimado;

                    // verifica se primeira unidade
                    if (primeira_unidade)
                    {
                        consumo_mensal_total.DataUltimoRegistro = consumo_mensal.DataUltimoRegistro;
                    }

                    for (int i = 0; i < 31; i++)
                    {
                        // verifica se primeira unidade
                        if (primeira_unidade)
                        {
                            // usa as datas do garantidor
                            consumo_mensal_total.Data[i] = consumo_mensal.Data[i];
                            consumo_mensal_total.Periodo[i] = consumo_mensal.Periodo[i];
                        }

                        // soma no total
                        consumo_mensal_total.ConsumoAtivo[i] += consumo_mensal.ConsumoAtivo[i];
                        consumo_mensal_total.NumReg_Dia[i] += consumo_mensal.NumReg_Dia[i];
                    }

                    // não é mais a primeira unidade
                    primeira_unidade = false;
                }
            }

            // consumo total bruto
            consumo_mensal_total.Consumo_Total_Bruto = consumo_mensal_total.Consumo_Consolidado + consumo_mensal_total.Consumo_Estimado;


            //
            // Prepara gráfico de consumo mensal
            //

            // número de dias do mês
            int NumDiasMes = DateTime.DaysInMonth(Data.Year, Data.Month);

            // valores
            double[] Consumo = new double[33];
            int[] Periodo = new int[33];
            string[] Datas = new string[33];

            double Consumo_max_grafico = 0.0;

            // data
            DateTime strData = new DateTime(Data.Year, Data.Month, 1, 0, 0, 0);

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int j = 0;

            for (int i = 0; i < (NumDiasMes + 2); i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proximo dia
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    Consumo[i] = consumo_mensal_total.ConsumoAtivo[0];
                    Periodo[i] = consumo_mensal_total.Periodo[0];
                }

                if (i == (NumDiasMes + 1))
                {
                    // zera
                    Consumo[i] = consumo_mensal_total.ConsumoAtivo[NumDiasMes - 1];
                    Periodo[i] = consumo_mensal_total.Periodo[NumDiasMes - 1];
                }

                if (i >= 1 && i <= NumDiasMes)
                {
                    // copia
                    j = i - 1;

                    Consumo[i] = consumo_mensal_total.ConsumoAtivo[j];
                    Periodo[i] = consumo_mensal_total.Periodo[j];

                    // aplica perdas para o gráfico
                    Consumo[i] = Consumo[i] * (1.0 + (contrato_gestao.Perdas / 100.0));

                    // verifica consumo maximo
                    if (Consumo[i] > Consumo_max_grafico)
                        Consumo_max_grafico = Consumo[i];
                }
            }

            Consumo_max_grafico = Consumo_max_grafico * 1.1;

            if (Consumo_max_grafico == 0.0)
            {
                Consumo_max_grafico = 10.0;
            }


            //
            // Resultado
            //

            ViewBag.ConsMaxGraficoMes = Consumo_max_grafico;

            ViewBag.ConsumoMes = Consumo;
            ViewBag.PeriodoMes = Periodo;
            ViewBag.DatasMes = Datas;

            ViewBag.NumDiasMes = NumDiasMes;

            string unidade_consumo = "MWh";
            ViewBag.UnidadeConsumo = unidade_consumo;

            string MesAtual = string.Format("{0:MMMM/yyyy}", Data);
            ViewBag.MesAtual = MesAtual;


            // data do último registro
            string DataMesExtenso = string.Format("Consumo consolidado até {0:dd} de {0:MMMM}", consumo_mensal_total.DataUltimoRegistro);
            ViewBag.DataMesExtenso = DataMesExtenso;

            return;
        }

        // Consumo Consolidado Total dos 12 meses
        private void ConsumoConsolidado_12meses(DateTime Data, List<ContratosCCEEDominio> contratosEnergia, EN_ConsolidadoMensal consumo_mensal_total, List<Provisionamento_UnidadesConsumidoras> unidadesConsumidoras)
        {

            //
            // Consumos Consolidado dos 12 meses
            //

            //
            // CONSUMO
            //

            double[] consumo_mensal = new double[12];

            // zera consumo
            for (int i = 0; i < 12; i++)
            {
                consumo_mensal[i] = 0.0;
            }

            // percorre unidades consumidoras
            foreach (Provisionamento_UnidadesConsumidoras unidConsumidora in unidadesConsumidoras)
            {
                // calculo consumo consolidado do mês
                EN_Metodos enMetodos = new EN_Metodos();

                // mês inicio
                DateTime mes = new DateTime(Data.Year, Data.Month, 1, 0, 0, 0);
                mes = mes.AddMonths(-11);

                for (int i = 0; i < 12; i++)
                {
                    // calcula consumo do mês
                    consumo_mensal[i] += enMetodos.ConsumoTotalMes(unidConsumidora.IDCliente, unidConsumidora.IDMedicao, mes);

                    // próximo mês
                    mes = mes.AddMonths(1);
                }
            }

            //
            // Consumo do Mês Atual
            //

            // utilizo o consumo total consolidado + estimado
            consumo_mensal[11] = consumo_mensal_total.Consumo_Consolidado + consumo_mensal_total.Consumo_Estimado;


            //
            // CONTRATO e TAKEs
            //

            double[] contrato_mensal = new double[12];
            double[] Take_Min_mensal = new double[12];
            double[] Take_Max_mensal = new double[12];

            for (int i = 0; i < 12; i++)
            {
                contrato_mensal[i] = 0.0;
                Take_Min_mensal[i] = 0.0;
                Take_Max_mensal[i] = 0.0;
            }

            // percorre contratos
            foreach (ContratosCCEEDominio contrato in contratosEnergia)
            {
                // Contrato da Sazo Mensal
                ContratosCCEE_SazonalizacaoMetodos sazonalizacaoMetodos = new ContratosCCEE_SazonalizacaoMetodos();
                List<ContratosCCEE_SazonalizacaoDominio> sazonalizacoes = sazonalizacaoMetodos.ListarPorId(contrato.IDContratoCCEE);

                // mês inicio
                DateTime mes = new DateTime(Data.Year, Data.Month, 1, 0, 0, 0);
                mes = mes.AddMonths(-11);

                for (int i = 0; i < 12; i++)
                {
                    // encontra contrato do mês
                    if (sazonalizacoes != null)
                    {
                        ContratosCCEE_SazonalizacaoDominio sazo = sazonalizacoes.Find(e => e.Ano == mes.Year);

                        if (sazo != null)
                        {
                            double valor = 0.0;

                            switch (mes.Month)
                            {
                                case 1:
                                    valor = sazo.Jan;
                                    break;

                                case 2:
                                    valor = sazo.Fev;
                                    break;

                                case 3:
                                    valor = sazo.Mar;
                                    break;

                                case 4:
                                    valor = sazo.Abr;
                                    break;

                                case 5:
                                    valor = sazo.Mai;
                                    break;

                                case 6:
                                    valor = sazo.Jun;
                                    break;

                                case 7:
                                    valor = sazo.Jul;
                                    break;

                                case 8:
                                    valor = sazo.Ago;
                                    break;

                                case 9:
                                    valor = sazo.Set;
                                    break;

                                case 10:
                                    valor = sazo.Out;
                                    break;

                                case 11:
                                    valor = sazo.Nov;
                                    break;

                                case 12:
                                    valor = sazo.Dez;
                                    break;
                            }

                            // contrato
                            contrato_mensal[i] += valor;

                            // Take Mínimo
                            Take_Min_mensal[i] += valor * (contrato.Flexibilidade_Minima / 100.0);

                            // Take Máximo
                            Take_Max_mensal[i] += valor * (contrato.Flexibilidade_Maxima / 100.0);
                        }
                    }

                    // próximo mês
                    mes = mes.AddMonths(1);
                }
            }



            //
            // Prepara gráfico de consumo 12 meses
            //

            //
            // Primeiro contrato será utilizado como base
            //
            ContratosCCEEDominio contrato_energia = contratosEnergia[0];

            // valores
            double[] Consumo = new double[14];
            int[] Periodo = new int[14];
            double[] Contrato = new double[14];
            double[] Take_Min = new double[14];
            double[] Take_Max = new double[14];
            string[] Datas = new string[14];

            string[] Meses = new string[12] { "Jan", "Fev", "Mar", "Abr", "Mai", "Jun", "Jul", "Ago", "Set", "Out", "Nov", "Dez" };

            double Consumo_max_grafico = 0.0;

            // data
            DateTime strData = new DateTime(Data.Year, Data.Month, 1, 0, 0, 0);

            // mês inicio
            strData = strData.AddMonths(-12);

            int j = 0;

            for (int i = 0; i < 14; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proximo mês
                strData = strData.AddMonths(1);

                // ultimo dia deste mês
                DateTime ultimo_dia_mes = strData.AddDays(-1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // consumo
                    Consumo[i] = consumo_mensal[0];
                    Periodo[i] = 0;

                    // contrato
                    Contrato[i] = contrato_mensal[0];
                    Take_Min[i] = Take_Min_mensal[0];
                    Take_Max[i] = Take_Max_mensal[0];
                }

                if (i == 13)
                {
                    // consumo
                    Consumo[i] = consumo_mensal[11];
                    Periodo[i] = 1;

                    // contrato
                    Contrato[i] = contrato_mensal[11];
                    Take_Min[i] = Take_Min_mensal[11];
                    Take_Max[i] = Take_Max_mensal[11];
                }

                if (i >= 1 && i <= 12)
                {
                    // copia
                    j = i - 1;

                    // consumo
                    Consumo[i] = consumo_mensal[j];

                    // assume mês consolidado
                    Periodo[i] = 0;

                    // verifica se ultimo dia deste mês esta na frente da data do ultimo registro
                    // caso estiver, este mes é estimado, senão ele é consolidado
                    if (ultimo_dia_mes > consumo_mensal_total.DataUltimoRegistro)
                    {
                        // mês estimado
                        Periodo[i] = 1;
                    }

                    // contrato
                    Contrato[i] = contrato_mensal[j];
                    Take_Min[i] = Take_Min_mensal[j];
                    Take_Max[i] = Take_Max_mensal[j];

                    // aplica perdas para o gráfico
                    Consumo[i] = Consumo[i] * (1.0 + (contrato_energia.Perdas / 100.0));

                    // verifica consumo maximo
                    if (Consumo[i] > Consumo_max_grafico)
                        Consumo_max_grafico = Consumo[i];
                    if (Contrato[i] > Consumo_max_grafico)
                        Consumo_max_grafico = Contrato[i];
                    if (Take_Min[i] > Consumo_max_grafico)
                        Consumo_max_grafico = Take_Min[i];
                    if (Take_Max[i] > Consumo_max_grafico)
                        Consumo_max_grafico = Take_Max[i];
                }
            }

            Consumo_max_grafico = Consumo_max_grafico * 1.1;

            if (Consumo_max_grafico == 0.0)
            {
                Consumo_max_grafico = 10.0;
            }

            //
            // Resultado
            //

            ViewBag.ConsMaxGrafico12Meses = Consumo_max_grafico;

            ViewBag.Consumo12Meses = Consumo;
            ViewBag.Periodo12Meses = Periodo;
            ViewBag.Contrato12Meses = Contrato;
            ViewBag.Take_Min12Meses = Take_Min;
            ViewBag.Take_Max12Meses = Take_Max;

            ViewBag.Datas12Meses = Datas;
            ViewBag.Meses = Meses;

            return;
        }

        // Calcula Rateio
        private void Calcula_Rateio_Tipo1_Tipo2(ref Provisionamento_Tipo1_Tipo2 provisionamento, ContratosCCEEDominio contrato_gestao, ref List<Provisionamento_UnidadesConsumidoras> unidadesConsumidoras)
        {
            //
            // PLD
            //
            HistoricoPLDMetodos pldMetodos = new HistoricoPLDMetodos();
            HistoricoPLDDominio pld = pldMetodos.ListarPorMes(provisionamento.Data);


            //
            // Unidades Consumidoras
            //

            // provisionamento
            provisionamento.TotalFaturamento_LP = 0.0;
            provisionamento.TotalFaturamento_CP = 0.0;
            provisionamento.TotalCusto_Energia = 0.0;

            // percorre unidades consumidoras
            foreach (Provisionamento_UnidadesConsumidoras unidConsumidora in unidadesConsumidoras)
            {
                // porcentagem do consumo total
                unidConsumidora.Consumo_Porc = 0.0;

                if (provisionamento.Consumo_Total_Bruto != 0.0)
                {
                    unidConsumidora.Consumo_Porc = (unidConsumidora.consumoMensal.Consumo_Total_Bruto / provisionamento.Consumo_Total_Bruto) * 100.0;
                }

                // Total Contrato
                unidConsumidora.Volume_Faturado = provisionamento.Total_Contrato * (unidConsumidora.Consumo_Porc / 100.0);

                // Preço Energia
                unidConsumidora.Preco_Energia = contrato_gestao.Reajuste_Preco;

                // Sobra / Deficit
                unidConsumidora.Sobra_Deficit = provisionamento.Resultado * (unidConsumidora.Consumo_Porc / 100.0);

                // PLD
                unidConsumidora.PLD = 0.0;

                switch (unidConsumidora.IDSubSistema)
                {
                    case 0:     // Sudeste/Centro-Oeste
                        unidConsumidora.PLD = pld.SE_CO;
                        break;

                    case 1:     // Sul
                        unidConsumidora.PLD = pld.S;
                        break;

                    case 2:     // Nordeste
                        unidConsumidora.PLD = pld.NE;
                        break;

                    case 3:     // Norte
                        unidConsumidora.PLD = pld.N;
                        break;

                    case 4:     // Fora do SIN
                        unidConsumidora.PLD = pld.Fora_SIN;
                        break;
                }

                // Total Faturamento LP
                unidConsumidora.TotalFaturamento_LP = unidConsumidora.Volume_Faturado * unidConsumidora.Preco_Energia;

                // Total Faturamento CP
                unidConsumidora.TotalFaturamento_CP = Math.Abs(unidConsumidora.Sobra_Deficit) * unidConsumidora.PLD;

                // Total Custo Energia
                unidConsumidora.TotalCusto_Energia = 0.0;

                // caso tiver sobras, subtrair CP do LP
                if (unidConsumidora.Sobra_Deficit > 0.0)
                {
                    unidConsumidora.TotalCusto_Energia = unidConsumidora.TotalFaturamento_LP - unidConsumidora.TotalFaturamento_CP;
                }

                // caso tiver deficit, somar CP com LP
                if (unidConsumidora.Sobra_Deficit < 0.0)
                {
                    unidConsumidora.TotalCusto_Energia = unidConsumidora.TotalFaturamento_LP + unidConsumidora.TotalFaturamento_CP;
                }

                // provisionamento
                provisionamento.TotalFaturamento_LP += unidConsumidora.TotalFaturamento_LP;
                provisionamento.TotalFaturamento_CP += unidConsumidora.TotalFaturamento_CP;
                provisionamento.TotalCusto_Energia += unidConsumidora.TotalCusto_Energia;
            }

            return;
        }

        // Calcula Fatura
        private void Calcula_Fatura_Tipo1_Tipo2(Provisionamento_Tipo1_Tipo2 provisionamento, ref List<Provisionamento_UnidadesConsumidoras> unidadesConsumidoras)
        {
            // PLD
            HistoricoPLDMetodos pldMetodos = new HistoricoPLDMetodos();
            HistoricoPLDDominio pld = pldMetodos.ListarPorMes(provisionamento.Data);

            // data inicio e fim do mês atual
            DATAHORA dh_ini = new DATAHORA();
            DateTime dataini = new DateTime(provisionamento.Data.Year, provisionamento.Data.Month, 1, 0, 0, 0);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_ini, dataini);

            DATAHORA dh_fim = new DATAHORA();
            DateTime datafim = dataini.AddMonths(1);
            Funcoes_Converte.ConverteDateTime2DataHora(ref dh_fim, datafim);


            // percorre unidades consumidoras
            foreach (Provisionamento_UnidadesConsumidoras unidConsumidora in unidadesConsumidoras)
            {
                //
                // Fatura uso de rede (distribuidora)
                //

                // simulação
                int IDSimulacaoCenario = 0;

                CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
                RESULT_ENERGIA_FATURA faturaDLL = new RESULT_ENERGIA_FATURA();
                RESULT_ENERGIA_FATURA faturaDLL_sim = new RESULT_ENERGIA_FATURA();

                // preenche solicitacao
                config_interface.sweb.id_cliente = unidConsumidora.IDCliente;
                config_interface.sweb.id_medicao = unidConsumidora.IDMedicao;
                config_interface.sweb.id_gateway = unidConsumidora.IDGateway;

                // calcula valores fatura projetada (mesmo sendo consolidado) (sem simulação)
                int retorno = SmCalcDB_Energia_Fatura((char)0, ref config_interface, IDSimulacaoCenario, (char)unidConsumidora.IDContratoMedicao, (char)unidConsumidora.IDEstruturaTarifaria, (char)1, ref dh_ini, ref dh_fim, ref faturaDLL, ref faturaDLL_sim);

                // total da fatura
                unidConsumidora.Fatura_Distribuidora_Valor = faturaDLL.total;


                //
                // Fatura Curto Prazo (comercializadora)
                //

                // fatura curto prazo
                unidConsumidora.Fatura_CurtoPrazo_PLD = unidConsumidora.PLD;
                unidConsumidora.Fatura_CurtoPrazo_Quantidade = unidConsumidora.Sobra_Deficit;
                unidConsumidora.Fatura_CurtoPrazo_Valor = Math.Abs(unidConsumidora.Fatura_CurtoPrazo_Quantidade) * unidConsumidora.Fatura_CurtoPrazo_PLD;

                //
                // Fatura Longo Prazo (comercializadora)
                //

                // fatura longo prazo
                unidConsumidora.Fatura_LongoPrazo_Quantidade = unidConsumidora.Volume_Faturado;
                unidConsumidora.Fatura_LongoPrazo_PrecoEnergia = unidConsumidora.Preco_Energia;
                unidConsumidora.Fatura_LongoPrazo_Valor = unidConsumidora.Fatura_LongoPrazo_Quantidade * unidConsumidora.Fatura_LongoPrazo_PrecoEnergia;
            }

            return;
        }

        // Calcula PROINFA
        private void Calcula_PROINFA(ref Provisionamento_Tipo1_Tipo2 provisionamento, ContratosCCEEDominio contrato_gestao, List<Provisionamento_UnidadesConsumidoras> unidadesConsumidoras)
        {
            //
            // PROINFA
            //
            EmpresasPROINFAMetodos proinfaMetodos = new EmpresasPROINFAMetodos();
            provisionamento.PROINFA = 0;

            // abate PROINFA
            provisionamento.Proinfa_Abate = contrato_gestao.Proinfa_Abate;

            // Percentual de carga
            provisionamento.PercentualCarga_Possui = contrato_gestao.PercentualCarga_Possui;
            provisionamento.PercentualCarga_Valor = contrato_gestao.PercentualCarga_Valor;

            // percorre unidades consumidoras
            foreach (Provisionamento_UnidadesConsumidoras unidConsumidora in unidadesConsumidoras)
            {
                // PROINFA da unidade consumidora
                double valor_PROINFA = proinfaMetodos.PROINFAMes(unidConsumidora.IDEmpresa, provisionamento.Data);

                // verifica se possui percentual de carga
                if (provisionamento.PercentualCarga_Possui)
                {
                    // verifica se unidade consumidora esta neste contrato
                    double PercentualCarga_Valor = PercentualCargaUnidadeConsumidora(contrato_gestao, unidConsumidora.IDMedicao);

                    // aplica percentual de carga
                    valor_PROINFA *= PercentualCarga_Valor / 100.0;
                }
                else
                {
                    // 100%
                    provisionamento.PercentualCarga_Valor = 100.0;
                }

                // soma PROINFA das unidades consumidoras
                provisionamento.PROINFA += valor_PROINFA;
            }

            return;
        }
    }
}