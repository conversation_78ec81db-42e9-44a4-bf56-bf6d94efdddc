﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.IO;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{

    public partial class CPFLController
    {

        // Contrato Energia - Fim da Vigência
        private void ContratoEnergia_FimVigencia()
        {
            //
            // Alerta Fim de Vigência Contrato Energia
            //
            // Alerta o cliente e o gestor quando algum contrato VIGENTE ou NAO INICIADO for vencer em 24,23,22,...,2 ou 1 meses exatos ou +15 dias
            //
            // Para o cliente é enviado o contrato que esta para vencer e em anexo uma planilha com todos os contratos dele.
            // Para o gestor é enviado em anexo uma planilha com os contratos de todos os clientes que estão para vencer.
            //
            // Esta rotina é executada todo dia às 7am
            //

            // log
            LogMessage("----------------------------------------------------------------------------------------");
            LogMessage(">> Alerta Fim de Vigência Contrato de Energia");

            // data atual
            DateTime hoje = System.DateTime.Now;
            DateTime dataAtual = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 0, 0);


            // Listas utilizadas
            PreparaListas_ContratoEnergia();


            // verifica se existe clientes e empresas
            if (clientes != null && empresas != null)
            {
                // contrato a vencer do Gestor
                List<ContratosCCEEDominio> contratosVencer_Gestor = new List<ContratosCCEEDominio>();

                // percorre clientes
                foreach (ClientesDominio cliente in clientes)
                {
                    // contratos energia Vigentes ou Não Iniciados
                    ContratosCCEEMetodos contratosMetodos = new ContratosCCEEMetodos();
                    List<ContratosCCEEDominio> contratos = contratosMetodos.ListarPorIDCliente(cliente.IDCliente, TIPO_CONTRATO_STATUS.Vigente, TIPO_CONTRATO_STATUS.NaoIniciado);

                    if (contratos != null)
                    {
                        // contrato a vencer do Cliente
                        List<ContratosCCEEDominio> contratosVencer_Cliente = new List<ContratosCCEEDominio>();

                        // percorre contratos
                        foreach (ContratosCCEEDominio contrato in contratos)
                        {
                            //
                            // Verifica se contrato encerra em 24 meses
                            //

                            // verifica se tempo da vigência configurado
                            if (dataAtual > contrato.Vigencia_Fim)
                            {
                                // desconfigurado ou já passou fim da vigência, ignoro
                                continue;
                            }

                            // verifica se Fim da vigência maior que 24 meses
                            if (contrato.Vigencia_Fim > dataAtual.AddMonths(24))
                            {
                                // ainda longe para encerrar
                                continue;
                            }


                            //
                            // Atualizo estrutura com informações
                            //

                            if (contrato.IDEmpresa > 0)
                            {
                                // empresa
                                EmpresasDominio empresa = empresas.Find(e => e.IDEmpresa == contrato.IDEmpresa);

                                if (empresa != null)
                                {
                                    // SiglaCCEE
                                    contrato.SiglaCCEE = empresa.SiglaCCEE;
                                }
                            }


                            // insiro na lista de contratos com encerramento próximo do Gestor
                            contratosVencer_Gestor.Add(contrato);

                            // verifica se este contrato pode ativar alerta
                            if (!contrato.Alerta_Encerramento)
                            {
                                // não pode alertar, continuo
                                continue;
                            }


                            //
                            // Insiro na lista de contratos com encerramento próximo do Cliente
                            //
                            contratosVencer_Cliente.Add(contrato);
                        }

                        // verifica se existe contrato com encerramento próximo deste cliente
                        if (contratosVencer_Cliente.Count > 0)
                        {
                            // Contrato Energia - Fim da Vigência - Enviar Alerta ao Cliente
                            ContratoEnergia_FimVigencia_Cliente(cliente, contratos, contratosVencer_Cliente);
                        }
                    }
                }

                // verifica se existe contrato com encerramento próximo dos clientes
                if (contratosVencer_Gestor.Count > 0)
                {
                    // Contrato Energia - Fim da Vigência - Enviar Alerta ao Gestor
                    ContratoEnergia_FimVigencia_Gestor(contratosVencer_Gestor);
                }
            }
        }

        // Contrato Energia - Fim da Vigência - Enviar Alerta ao Cliente
        private void ContratoEnergia_FimVigencia_Cliente(ClientesDominio cliente, List<ContratosCCEEDominio> contratos, List<ContratosCCEEDominio> contratosVencer_Cliente)
        {
            // data atual
            DateTime hoje = System.DateTime.Now;
            DateTime dataAtual = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 0, 0);

            // percorre contratos com encerramento próximo deste cliente
            foreach (ContratosCCEEDominio contrato in contratosVencer_Cliente)
            {
                // não enviar alerta ao cliente
                bool enviar_alerta = false;
                int num_meses = 0;

                // fim da vigência
                DateTime Vigencia_Fim = new DateTime(contrato.Vigencia_Fim.Year, contrato.Vigencia_Fim.Month, contrato.Vigencia_Fim.Day, 0, 0, 0);

                // verifica se contrato vence exatamente a 24/23/22...2 ou 1 meses
                for (int i = 0; i <= 24; i++)
                {
                    // mês
                    DateTime dataMes = dataAtual.AddMonths(i);
                    DateTime data15Dias = dataMes.AddDays(15);

                    // verifica se Fim da Vigência vence exatamente no mês ou mais 15 dias
                    if (Vigencia_Fim == dataMes || Vigencia_Fim == data15Dias)
                    {
                        // enviar alerta ao cliente
                        enviar_alerta = true;
                        num_meses = i;

                        break;
                    }
                }

                // verifica se deve enviar alerta ao cliente
                if (enviar_alerta)
                {
                    //
                    // Prepara planilha de contratos
                    //

                    // buffer
                    byte[] dataBuffer;

                    // Planilha Excel
                    var workbook = new HSSFWorkbook();

                    // gera planilha de contratos
                    workbook = ContratoEnergia_FimVigencia_XLS(contratos, dataAtual);

                    using (MemoryStream memoryStream = new MemoryStream())
                    {
                        workbook.Write(memoryStream);
                        memoryStream.Position = 0;
                        dataBuffer = memoryStream.ToArray();
                    }


                    //
                    // Usuários a enviar
                    //

                    // usuários a enviar
                    List<UsuarioDominio> usuarios_enviar = new List<UsuarioDominio>();

                    // usuários do cliente
                    UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
                    List<UsuarioDominio> usuarios = usuarioMetodos.ListarPorIDCliente(cliente.IDCliente);

                    if (usuarios != null)
                    {
                        // percorre usuarios
                        foreach (UsuarioDominio usuario in usuarios)
                        {
                            // verifica se usuário deseja receber alerta
                            if (usuario.Gestao_Energia == 1)
                            {
                                usuarios_enviar.Add(usuario);
                            }
                        }
                    }


                    //
                    // ENVIA EMAIL PARA USUARIOS
                    //

                    // log
                    LogMessage(string.Format(">>>> Contrato ID{0} [{1}] encerrando em {2} meses", contrato.IDContratoCCEE, contrato.Contrato_Codigo, num_meses));

                    // envia email para todos destinatários simultaneamente
                    ContratoEnergia_FimVigencia_Cliente_EnviaBulkEmail(usuarios_enviar, contrato, num_meses, "ContratosEnergia_FimVigencia.xls", "application/vnd.ms-excel", dataBuffer);
                }
            }

            return;
        }

        // Contrato Energia - Fim da Vigência - Enviar Alerta ao Gestor
        private void ContratoEnergia_FimVigencia_Gestor(List<ContratosCCEEDominio> contratosVencer_Gestor)
        {
            // data atual
            DateTime hoje = System.DateTime.Now;
            DateTime dataAtual = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 0, 0);

            // não enviar alerta ao gestor
            bool enviar_alerta = false;

            // percorre contratos com encerramento próximo dos clientes
            foreach (ContratosCCEEDominio contrato in contratosVencer_Gestor)
            {
                // fim da vigência
                DateTime Vigencia_Fim = new DateTime(contrato.Vigencia_Fim.Year, contrato.Vigencia_Fim.Month, contrato.Vigencia_Fim.Day, 0, 0, 0);

                // verifica se contrato vence exatamente a 24/23/22.../2/1 meses
                for (int i = 0; i <= 24; i++)
                {
                    // mês
                    DateTime dataMes = dataAtual.AddMonths(i);
                    DateTime data15Dias = dataMes.AddDays(15);

                    // verifica se Fim da Vigência vence exatamente no mês ou mais 15 dias
                    if (Vigencia_Fim == dataMes || Vigencia_Fim == data15Dias)
                    {
                        // enviar alerta ao gestor
                        enviar_alerta = true;

                        break;
                    }
                }
            }

            // verifica se deve enviar alerta ao gestor
            if (enviar_alerta)
            {
                //
                // Prepara planilha de contratos
                //

                // buffer
                byte[] dataBuffer;

                // Planilha Excel
                var workbook = new HSSFWorkbook();

                // gera planilha de contratos
                workbook = ContratoEnergia_FimVigencia_XLS(contratosVencer_Gestor, dataAtual);

                using (MemoryStream memoryStream = new MemoryStream())
                {
                    workbook.Write(memoryStream);
                    memoryStream.Position = 0;
                    dataBuffer = memoryStream.ToArray();
                }


                //
                // Usuários a enviar
                //

                // usuários a enviar
                List<UsuarioDominio> usuarios_enviar = new List<UsuarioDominio>();

                // usuários do cliente
                UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
                List<UsuarioDominio> usuarios = usuarioMetodos.ListarOperadoresConsultor(CLIENTES_ESPECIAIS.CPFL);

                if (usuarios != null)
                {
                    // percorre usuarios
                    foreach (UsuarioDominio usuario in usuarios)
                    {
                        // verifica se usuário deseja receber alerta
                        if (usuario.Gestao_Energia == 1)
                        {
                            usuarios_enviar.Add(usuario);
                        }
                    }
                }


                //
                // ENVIA EMAIL PARA USUARIOS
                //

                // log
                LogMessage(">> Enviando Contratos para os Gestores");

                // envia email para todos destinatários simultaneamente
                ContratoEnergia_FimVigencia_Gestor_EnviaBulkEmail(usuarios_enviar, "ContratosEnergia.xls", "application/vnd.ms-excel", dataBuffer);
            }

            return;
        }

        // envia email para vários destinatários - cliente
        private void ContratoEnergia_FimVigencia_Cliente_EnviaBulkEmail(List<UsuarioDominio> usuarios, ContratosCCEEDominio contrato, int num_meses, string attachmentName, string attachmentType, byte[] attachment)
        {
            // verifica se tem usuarios
            if (usuarios == null)
            {
                // log
                LogMessage(">> Sem usuários para enviar");

                return;
            }

            if (usuarios.Count <= 0)
            {
                // log
                LogMessage(">> Sem usuários para enviar");

                return;
            }


            // assunto
            string assunto = string.Format("[Smart Energy] Encerramento do Contrato de Energia - {0}", contrato.Contrato_Codigo);

            // envia EMAIL
            var emailTemplate = "ContratoEnergiaEncerramentoEmail_CPFL";

            var message = EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.UsuarioNome", "Olá");
            message = message.Replace("ViewBag.CodigoContrato", contrato.Contrato_Codigo);
            message = message.Replace("ViewBag.Vigencia_Inicio", string.Format("{0:d}", contrato.Vigencia_Inicio));
            message = message.Replace("ViewBag.Vigencia_Fim", string.Format("{0:d}", contrato.Vigencia_Fim));

            string texto_meses = string.Format("em <b>{0} meses</b>", num_meses);
            switch (num_meses)
            {
                case 0:
                    texto_meses = "<b>hoje</b>";
                    break;

                case 1:
                    texto_meses = "no <b>próximo mês</b>";
                    break;
            }
            message = message.Replace("ViewBag.Meses", texto_meses);

            // lista de emails
            List<string> emails = new List<string>();

            // percorre usuários
            foreach (UsuarioDominio usuario in usuarios)
            {
                // coloca na lista
                emails.Add(usuario.Email);
            }

            // envia
            EmailServices.SendBulkEmail(emails, assunto, message, attachmentName, attachmentType, attachment);

            // log
            foreach (UsuarioDominio usuario in usuarios)
            {
                // log
                LogMessage(string.Format(">>>> Enviou email para usuário [{0:000000}] {1}", usuario.IDUsuario, usuario.Email));
            }

            return;
        }

        // envia email para vários destinatários - gestor
        private void ContratoEnergia_FimVigencia_Gestor_EnviaBulkEmail(List<UsuarioDominio> usuarios, string attachmentName, string attachmentType, byte[] attachment)
        {
            // verifica se tem usuarios
            if (usuarios == null)
            {
                // log
                LogMessage(">> Sem usuários para enviar");

                return;
            }

            if (usuarios.Count <= 0)
            {
                // log
                LogMessage(">> Sem usuários para enviar");

                return;
            }


            // assunto
            string assunto = "[Smart Energy] Encerramento dos Contratos de Energia";

            // envia EMAIL
            var emailTemplate = "ContratoEnergiaEncerramentoGestorEmail_CPFL";

            var message = EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.UsuarioNome", "Prezado Gestor");

            // lista de emails
            List<string> emails = new List<string>();

            // percorre usuários
            foreach (UsuarioDominio usuario in usuarios)
            {
                // coloca na lista
                emails.Add(usuario.Email);
            }

            // envia
            EmailServices.SendBulkEmail(emails, assunto, message, attachmentName, attachmentType, attachment);

            // log
            foreach (UsuarioDominio usuario in usuarios)
            {
                // log
                LogMessage(string.Format(">>>> Enviou email para usuário [{0:000000}] {1}", usuario.IDUsuario, usuario.Email));
            }

            return;
        }


        // Contrato Energia - Fim da Vigência - planilha XLS
        private HSSFWorkbook ContratoEnergia_FimVigencia_XLS(List<ContratosCCEEDominio> contratos, DateTime dataAtual)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);


            //
            // CONTRATOS
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Contratos de Energia");

            // cabecalho
            string[] cabecalho = { "Sigla CCEE", "Código Contrato", "Início Vigência", "Término Vigência", "Comercializadora", "Preço Base (R$)", "Fonte", "SubMercado", "Ano", "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho", "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro", "Flex. Mín. (%)", "Flex Máx. (%)", "Sazo. Mín. (%)", "Sazo. Máx. (%)", "Mod. Mín (%)", "Mod. Máx. (%)" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            IRow row;

            // percorre contratos
            foreach (ContratosCCEEDominio contrato in contratos)
            {
                //
                // Descrições
                //

                // Comercializadora
                string NomeComercializadora = "";

                // comercializadora
                AgentesDominio comercializadora = comercializadoras.Find(e => e.IDAgente == contrato.IDComercializadora);

                if (comercializadora != null)
                {
                    // Comercializadora
                    NomeComercializadora = comercializadora.Nome;
                }

                // Fonte
                string NomeFonte = "";

                // fonte
                ListaTiposDominio fonte = listatiposFonte.Find(e => e.ID == contrato.IDTipoFonte);

                if (fonte != null)
                {
                    // Fonte
                    NomeFonte = fonte.Descricao;
                }

                // SubMercado
                string NomeSubMercado = "";

                // submercado
                ListaTiposDominio submercado = listatiposSubSistema.Find(e => e.ID == contrato.IDSubSistema);

                if (submercado != null)
                {
                    // SubMercado
                    NomeSubMercado = submercado.Descricao;
                }

                // Preço base
                double preco_base = 0.0;

                if (contrato.Base_Preco_Flat)
                {
                    preco_base = contrato.Base_Preco;
                }
                else
                {
                    // busca preço base do mês
                    ContratosCCEE_PrecoBaseMetodos precoBaseMetodos = new ContratosCCEE_PrecoBaseMetodos();
                    ContratosCCEE_PrecoBaseDominio precoBase = precoBaseMetodos.ListarPorMes(contrato.IDContratoCCEE, dataAtual);

                    if (precoBase != null)
                    {
                        preco_base = precoBase.Base_Preco;
                    }
                }


                //
                // Planilha
                //

                // le volume
                ContratosCCEE_VolumeMetodos volumeMetodos = new ContratosCCEE_VolumeMetodos();
                List<ContratosCCEE_VolumeDominio> volumes = volumeMetodos.ListarPorId(contrato.IDContratoCCEE, contrato.Montante_Tipo);


                // calcula número de anos que o contrato possui
                int ano_Inicio = contrato.Vigencia_Inicio.Year;
                int ano_Fim = contrato.Vigencia_Fim.Year;

                // número de anos
                int numero_anos = (ano_Fim - ano_Inicio) + 1;

                // percorre anos
                for (int i = 0; i < numero_anos; i++)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex++);

                    // SiglaCCEE
                    textoCelulaXLS(row, 0, contrato.SiglaCCEE);

                    // Código Contrato
                    textoCelulaXLS(row, 1, contrato.Contrato_Codigo);

                    // Início Vigência
                    datahoraCelulaXLS(row, 2, contrato.Vigencia_Inicio, _dataStyle);

                    // Fim Vigência
                    datahoraCelulaXLS(row, 3, contrato.Vigencia_Fim, _dataStyle);

                    // Comercializadora
                    textoCelulaXLS(row, 4, NomeComercializadora);

                    // Preço base
                    numeroCelulaXLS(row, 5, contrato.Base_Preco, _2CellStyle);

                    // Fonte
                    textoCelulaXLS(row, 6, NomeFonte);

                    // SubMercado
                    textoCelulaXLS(row, 7, NomeSubMercado);

                    // Ano
                    DateTime vigencia_atual = contrato.Vigencia_Inicio.AddYears(i);
                    DateTime ano_atual = new DateTime(vigencia_atual.Year, 1, 1, 0, 0, 0);
                    numeroCelulaXLS(row, 8, ano_atual.Year, _intCellStyle);

                    // meses
                    for (int j = 0; j < 12; j++)
                    {
                        // mes
                        DateTime mes_atual = new DateTime(ano_atual.Year, j + 1, 1, 0, 0, 0);

                        // volume do mês
                        double contratado = 0.0;

                        // verifica se dentro da vigência
                        if (mes_atual >= contrato.Vigencia_Inicio && mes_atual <= contrato.Vigencia_Fim )
                        {
                            // verifica se tem volumes
                            if (volumes != null)
                            {
                                // verifica se Montante (MWm) = 1 por ano
                                if (contrato.Montante_Tipo == 0)
                                {
                                    // procura ano
                                    ContratosCCEE_VolumeDominio volume = volumes.Find(e => e.Data.Year == mes_atual.Year);

                                    if (volume != null)
                                    {
                                        // calcula número de horas do mês
                                        int num_horas = System.DateTime.DaysInMonth(mes_atual.Year, mes_atual.Month) * 24;

                                        // calcula contratado MWh do mês
                                        contratado = volume.Contratado * num_horas;
                                    }
                                }
                                else
                                {
                                    // procura mês
                                    ContratosCCEE_VolumeDominio volume = volumes.Find(e => e.Data == mes_atual);

                                    if (volume != null)
                                    {
                                        // contratado MWh do mês
                                        contratado = volume.Contratado;
                                    }
                                }
                            }
                        }

                        // contratado do mês
                        numeroCelulaXLS(row, 9 + j, contratado, _3CellStyle);
                    }

                    // Flexibilidade mínima
                    numeroCelulaXLS(row, 21, contrato.Flexibilidade_Minima, _2CellStyle);

                    // Flexibilidade máxima
                    numeroCelulaXLS(row, 22, contrato.Flexibilidade_Maxima, _2CellStyle);

                    // Sazonalidade mínima
                    numeroCelulaXLS(row, 23, contrato.Sazonalizacao_Minima, _2CellStyle);

                    // Sazonalidade máxima
                    numeroCelulaXLS(row, 24, contrato.Sazonalizacao_Maxima, _2CellStyle);

                    // Modulação mínima
                    numeroCelulaXLS(row, 25, contrato.Modulacao_Minima, _2CellStyle);

                    // Modulação máxima
                    numeroCelulaXLS(row, 26, contrato.Modulacao_Maxima, _2CellStyle);
                }
            }

            // largura de cada coluna
            for (int i = 0; i < sheet.GetRow(0).LastCellNum; i++)
            {
                if (i >= 8 && i <= 20)
                {
                    sheet.SetColumnWidth(i, 4000);
                }
                else
                {
                    sheet.SetColumnWidth(i, 6000);
                }
            }

            // retorna planilha
            return workbook;
        }
    }
}
