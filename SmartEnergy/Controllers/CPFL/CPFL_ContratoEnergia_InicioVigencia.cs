﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;

using SmartEnergy.Models;
using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{

    public partial class CPFLController
    {

        // Contrato Energia - Início da Vigência
        private void ContratoEnergia_InicioVigencia()
        {
            //
            // Alerta Início de Vigência Contrato Energia
            //
            // Alerta o gestor quando algum contrato NÃO INICIADO for iniciar no mês de referência.
            //
            // Para o gestor é enviado em anexo uma planilha com os contratos de todos os clientes que estão para iniciar o contrato.
            //
            // Esta rotina é executada todo dia 1 (apenas 1x no mês) às 7am.
            //

            // log
            LogMessage("----------------------------------------------------------------------------------------");
            LogMessage(">> Alerta Início de Vigência Contrato de Energia");

            // data atual
            DateTime hoje = System.DateTime.Now;
            DateTime dataAtual = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 0, 0);

            // verifica se é dia de verificar o início dos contratos
            if (dataAtual.Day != 1)
            {
                // não é dia para verificar
                LogMessage(">> Hoje não deve verificar Início de Vigência Contrato de Energia");
                return;
            }


            // Listas utilizadas
            PreparaListas_ContratoEnergia();


            // verifica se existe clientes e empresas
            if (clientes != null && empresas != null)
            {
                // contrato a iniciar do Gestor
                List<ContratosCCEEDominio> contratosIniciar_Gestor = new List<ContratosCCEEDominio>();

                // percorre clientes
                foreach (ClientesDominio cliente in clientes)
                {
                    // contratos energia Não Iniciados
                    ContratosCCEEMetodos contratosMetodos = new ContratosCCEEMetodos();
                    List<ContratosCCEEDominio> contratos = contratosMetodos.ListarPorIDCliente(cliente.IDCliente, TIPO_CONTRATO_STATUS.NaoIniciado);
                    List<ContratosCCEEDominio> contratosVigentes = contratosMetodos.ListarPorIDCliente(cliente.IDCliente, TIPO_CONTRATO_STATUS.Vigente);

                    if (contratos != null)
                    {
                        // percorre contratos
                        foreach (ContratosCCEEDominio contrato in contratos)
                        {
                            //
                            // Verifica se contrato inicia este mês
                            //

                            // verifica se contrato inicia este mês e ano
                            if (contrato.Vigencia_Inicio.Month != dataAtual.Month || contrato.Vigencia_Inicio.Year != dataAtual.Year)
                            {
                                // próximo contrato
                                continue;
                            }

                            // inicialmente não possui contrato vigente
                            contrato.Existem_Contratos_Vigentes = false;

                            // verifica se existem outros contratos VIGENTES deste cliente
                            if (contratosVigentes != null)
                            {
                                if (contratosVigentes.Count > 0)
                                {
                                    // existe contratos vigentes
                                    contrato.Existem_Contratos_Vigentes = true;
                                }
                            }

                            //
                            // Atualizo estrutura com informações
                            //

                            if (contrato.IDEmpresa > 0)
                            {
                                // empresa
                                EmpresasDominio empresa = empresas.Find(e => e.IDEmpresa == contrato.IDEmpresa);

                                if (empresa != null)
                                {
                                    // SiglaCCEE
                                    contrato.SiglaCCEE = empresa.SiglaCCEE;
                                }
                            }

                            // insiro na lista de contratos a iniciar do Gestor
                            contratosIniciar_Gestor.Add(contrato);
                        }
                    }
                }

                // verifica se existe contrato a iniciar
                if (contratosIniciar_Gestor.Count > 0)
                {
                    // Contrato Energia - Início da Vigência - Enviar Alerta ao Gestor
                    ContratoEnergia_InicioVigencia_Gestor(contratosIniciar_Gestor);
                }
            }
        }

        // Contrato Energia - Início da Vigência - Enviar Alerta ao Gestor
        private void ContratoEnergia_InicioVigencia_Gestor(List<ContratosCCEEDominio> contratosIniciar_Gestor)
        {
            // data atual
            DateTime hoje = System.DateTime.Now;
            DateTime dataAtual = new DateTime(hoje.Year, hoje.Month, hoje.Day, 0, 0, 0);


            //
            // Prepara planilha de contratos
            //

            // buffer
            byte[] dataBuffer;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            // gera planilha de contratos
            workbook = ContratoEnergia_InicioVigencia_XLS(contratosIniciar_Gestor, dataAtual);

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                dataBuffer = memoryStream.ToArray();
            }


            //
            // Usuários a enviar
            //

            // usuários a enviar
            List<UsuarioDominio> usuarios_enviar = new List<UsuarioDominio>();

            // usuários do cliente
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            List<UsuarioDominio> usuarios = usuarioMetodos.ListarOperadoresConsultor(CLIENTES_ESPECIAIS.CPFL);

            if (usuarios != null)
            {
                // percorre usuarios
                foreach (UsuarioDominio usuario in usuarios)
                {
                    // verifica se usuário deseja receber alerta
                    if (usuario.Gestao_Energia == 1)
                    {
                        usuarios_enviar.Add(usuario);
                    }
                }
            }


            //
            // ENVIA EMAIL PARA USUARIOS
            //

            // log
            LogMessage(">> Enviando Contratos para os Gestores");

            // envia email para todos destinatários simultaneamente
            ContratoEnergia_InicioVigencia_Gestor_EnviaBulkEmail(usuarios_enviar, dataAtual, "ContratosEnergia_InicioVigencia.xls", "application/vnd.ms-excel", dataBuffer);

            return;
        }


        // envia email para vários destinatários - gestor
        private void ContratoEnergia_InicioVigencia_Gestor_EnviaBulkEmail(List<UsuarioDominio> usuarios, DateTime dataAtual, string attachmentName, string attachmentType, byte[] attachment)
        {
            // verifica se tem usuarios
            if (usuarios == null)
            {
                // log
                LogMessage(">> Sem usuários para enviar");

                return;
            }

            if (usuarios.Count <= 0)
            {
                // log
                LogMessage(">> Sem usuários para enviar");

                return;
            }


            // assunto
            string assunto = "[Smart Energy] Início dos Contratos de Energia";

            // envia EMAIL
            var emailTemplate = "ContratoEnergiaInicioGestorEmail_CPFL";

            var message = EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.UsuarioNome", "Prezado Gestor");

            // Mes início
            CultureInfo culture = new CultureInfo("pt-BR");
            string NomeMes = dataAtual.ToString("MMMM", culture);
            message = message.Replace("ViewBag.Mes", string.Format("{0} de {1:yyyy}", NomeMes, dataAtual));

            // lista de emails
            List<string> emails = new List<string>();

            // percorre usuários
            foreach (UsuarioDominio usuario in usuarios)
            {
                // coloca na lista
                emails.Add(usuario.Email);
            }

            // envia
            EmailServices.SendBulkEmail(emails, assunto, message, attachmentName, attachmentType, attachment);

            // log
            foreach (UsuarioDominio usuario in usuarios)
            {
                // log
                LogMessage(string.Format(">>>> Enviou email para usuário [{0:000000}] {1}", usuario.IDUsuario, usuario.Email));
            }

            return;
        }


        // Contrato Energia - Início da Vigência - planilha XLS
        private HSSFWorkbook ContratoEnergia_InicioVigencia_XLS(List<ContratosCCEEDominio> contratos, DateTime dataAtual)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);


            //
            // CONTRATOS
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Contratos de Energia");

            // cabecalho
            string[] cabecalho = { "Sigla CCEE", "Código Contrato", "Início Vigência", "Existem Contratos Vigentes" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            IRow row;

            // percorre contratos
            foreach (ContratosCCEEDominio contrato in contratos)
            {
                //
                // Planilha
                //

                // adiciona linha
                row = sheet.CreateRow(rowIndex++);

                // SiglaCCEE
                textoCelulaXLS(row, 0, contrato.SiglaCCEE);

                // Código Contrato
                textoCelulaXLS(row, 1, contrato.Contrato_Codigo);

                // Início Vigência
                datahoraCelulaXLS(row, 2, contrato.Vigencia_Inicio, _dataStyle);

                // verifica se existem contratos vigentes
                if (contrato.Existem_Contratos_Vigentes)
                {
                    // existe contrato vigente
                    textoCelulaXLS(row, 3, "SIM");
                }
                else
                {
                    // não existe contrato vigente
                    textoCelulaXLS(row, 3, "NÃO");
                }
            }

            // largura de cada coluna
            for (int i = 0; i < sheet.GetRow(0).LastCellNum; i++)
            {
                sheet.SetColumnWidth(i, 7000);
            }

            // retorna planilha
            return workbook;
        }
    }
}
