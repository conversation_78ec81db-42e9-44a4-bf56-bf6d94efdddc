﻿using System;
using System.Collections.Generic;
using System.Text;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class APIController
    {
        // Cadastro dos Equipamentos
        // https://www.smartenergy.com.br/API/Equipamentos?Key=1234567890123456&id=1

        // Key = API key do usuário
        // id = id do equipamento ou da conta
        // _q = Busca os Equipamentos pelos campos nome
        // _sort = Define o campo de ordenação da lista
        // _order = Define a ordem da lista com base no campo definido
        // _limit = Quantidade de itens por página
        // _page = Página a ser carregada

        // Status
        // 0  - OK
        // 1  - Erro: Senha ou Datas nulas
        // 2  - Erro: Chave incorreta
        // 3  - Erro: Usuário inexistente
        // 4  - Erro: Medição inexistente
        // 5  - Erro: Usuário não pertence ao cliente da medição
        // 6  - Erro: Cliente inexistente
        // 7  - Erro: Usuário não tem acesso ao cliente
        // 8  - Erro: ---
        // 9  - Erro: ---
        // 10 - Erro: Sem registro
        // 11 - Erro: ---
        // 12 - Erro: ---
        // 14 - Erro: ---
        // 20 - Erro: Ultrapassou limite diário de requisições
        // 21 - Erro: Usuário bloqueado
        // 22 - Erro: Gateway inexistente

        public void Equipamentos(string Key, int id = 0, string _q = "", string _sort = "id", string _order = "asc", int _limit = 10, int _page = 1)
        {
            // Inicializa variáveis
            DateTime Data = new DateTime(2000, 1, 1, 0, 0, 0);

            //
            // Parâmetros
            //
            if (Key == null)
            {
                // Log - [1] key nulo
                API_Log(Key, "Equipamentos", 0, id, Data, Data, 1);

                // erro
                API_Erro(1, "O campo Key selecionado é inválido");
                return;
            }

            if (_sort.ToLower() != "id" && _sort.ToLower() != "name" && _sort.ToLower() != "account")
            {
                // Log - [1] _sort
                API_Log(Key, "Equipamentos", 0, id, Data, Data, 1);

                // erro
                API_Erro(1, "O campo _sort deve ser 'id', 'name' ou 'account'");
                return;
            }

            if (_order.ToLower() != "asc" && _order.ToLower() != "desc")
            {
                // Log - [1] _order
                API_Log(Key, "Equipamentos", 0, id, Data, Data, 1);

                // erro
                API_Erro(1, "O campo _order deve ser 'asc' ou 'desc'");
                return;
            }

            if (_limit < 0)
            {
                // Log - [1] _limit
                API_Log(Key, "Equipamentos", 0, id, Data, Data, 1);

                // erro
                API_Erro(1, "O campo _limit deve ser maior que zero");
                return;
            }

            if (_page < 0)
            {
                // Log - [1] _page
                API_Log(Key, "Equipamentos", 0, id, Data, Data, 1);

                // erro
                API_Erro(1, "O campo _page deve ser maior que zero");
                return;
            }

            //
            // USUARIO
            //

            // le usuario
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = usuarioMetodos.ListarPorAPI_Key(Key);

            if (usuario != null)
            {
                // verifica se key eh correta
                if (usuario.API_Key != Key)
                {
                    // Log - [2] chave incorreta
                    API_Log(Key, "Equipamentos", 0, 0, Data, Data, 2);

                    // erro
                    API_Erro(2, string.Format("O campo Key está incorreto ({0})", Key));
                    return;
                }
            }
            else
            {
                // Log - [3] usuário inexistente
                API_Log(Key, "Equipamentos", 0, id, Data, Data, 3);

                // erro
                API_Erro(3, string.Format("Usuário Incorreto ({0})", Key));
                return;
            }

            // verifica se usuário bloqueado
            if (usuario.Bloqueio == TIPO_BLOQUEIO.TempoAPI || usuario.Bloqueio == TIPO_BLOQUEIO.Generico)
            {
                // Log - [21] usuário bloqueado
                API_Log(Key, "Equipamentos", 0, id, Data, Data, 21);

                // erro
                API_Erro(21, string.Format("Usuário Bloqueado ({0})", usuario.IDUsuario));
                return;
            }

            // monta lista
            string strWhere = "";

            // verifica se busca por id
            if (id > 0 && _sort.ToLower() == "id")
            {
                strWhere = string.Format("IDMedicao = {0}", id);
            }

            // verifica se busca por account
            if (id > 0 && _sort.ToLower() == "account")
            {
                strWhere = string.Format("IDCliente = {0}", id);
            }

            // verifica se busca por nome
            if (_sort.ToLower() == "name")
            {
                // busca
                strWhere = string.Format("(Nome collate Latin1_General_CI_AI LIKE '%{0}%') ", _q);
                strWhere += "ORDER BY Nome";

                // ordem decrescente
                if (_order.ToLower() == "desc")
                {
                    strWhere += " DESC";
                }
            }

            // busca
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicoesMetodos.BuscarWhere(strWhere);

            if (medicoes == null)
            {
                // Log - [6] medição inexistente
                API_Log(Key, "Equipamentos", 0, id, Data, Data, 6);

                // erro
                API_Erro(6, "Medição inexistente");
                return;
            }

            //
            // REGISTROS
            //

            bool retorno = Equipamentos_Item(medicoes, _limit, _page);

            // verifica se erro nos registros
            if (!retorno)
            {
                // Log do Equipamentos - [10] Sem registro
                API_Log(Key, "Equipamentos", usuario.IDUsuario, id, Data, Data, 10);

                // erro
                API_Erro(10, "Sem Registros");
                return;
            }

            // Log do Equipamentos - OK
            API_Log(Key, "Equipamentos", usuario.IDUsuario, id, Data, Data, 0);
            return;
        }

        // itens
        public bool Equipamentos_Item(List<MedicoesDominio> medicoes, int _limit, int _page)
        {

            // JSON
            StringBuilder xmlBuilder = new StringBuilder();
            IFormatProvider iFormatProvider = new System.Globalization.CultureInfo("en-US");

            // inicio e fim
            int conta_item = 0;
            int conta_item_publicado = 0;

            int inicio = (_page - 1) * _limit;
            int fim = _page * _limit;

            if (fim > medicoes.Count)
            {
                fim = medicoes.Count;
            }

            fim = fim - 1;

            // tipos da medição
            string[] tipo_medicao = new string[]
            {
                        "Energia",
                        "Energia Fórmula",
                        "Utilidades",
                        "Entrada Analógica",
                        "Ciclômetro",
                        "Utilidades Fórmula",
                        "Entrada Analógica Fórmula",
                        "Meteorologia"
            };

            // subtipos da medição
            string[] subtipo_medicao = new string[]
            {
                        "Fronteira",
                        "Setorizada"
            };

            // le distribuidora
            AgentesDistribuidoraMetodos agenteDistribuidoraMetodos = new AgentesDistribuidoraMetodos();
            List<AgentesDistribuidoraDominio> agentesDistribuidora = agenteDistribuidoraMetodos.ListarTodos();


            // percorre medições
            foreach (MedicoesDominio medicao in medicoes)
            {
                if (conta_item >= inicio && conta_item <= fim)
                {
                    if (conta_item == inicio)
                    {
                        // inicia
                        API_write(xmlBuilder, "{");
                        API_write(xmlBuilder, "  \"items\": [");
                    }

                    // unidade
                    string unidade = "";

                    if (medicao.IDTipoMedicao == TIPOS_MEDICAO.utilidades || medicao.IDTipoMedicao == TIPOS_MEDICAO.utilidades_formula ||
                        medicao.IDTipoMedicao == TIPOS_MEDICAO.ea || medicao.IDTipoMedicao == TIPOS_MEDICAO.ea_formula)
                    {
                        unidade = medicao.UnidadeGrandeza;
                    }

                    // constante
                    EN_K_Metodos constanteMetodos = new EN_K_Metodos();
                    EN_K_Dominio constante = constanteMetodos.UltimaConstante(medicao.IDCliente, medicao.IDMedicao);
                    double valor_constante = 0.0;

                    if (constante != null)
                    {
                        valor_constante = constante.Constante;
                    }

                    // le distribuidora
                    AgentesDistribuidoraDominio agenteDistribuidora = agentesDistribuidora.Find(e => e.IDAgenteDistribuidora == medicao.IDAgenteDistribuidora);

                    string distribuidora = "---";

                    if (agenteDistribuidora != null)
                    {
                        distribuidora = agenteDistribuidora.Nome;
                    }


                    // item
                    API_write(xmlBuilder, "    {");
                    API_write(xmlBuilder, string.Format("      \"id\": {0},", medicao.IDMedicao));
                    API_write(xmlBuilder, string.Format("      \"name\": \"{0}\",", medicao.Nome));
                    API_write(xmlBuilder, string.Format("      \"type\": \"{0}\",", tipo_medicao[medicao.IDTipoMedicao]));
                    API_write(xmlBuilder, string.Format("      \"subtype\": \"{0}\",", subtipo_medicao[medicao.IDCategoriaMedicao]));
                    API_write(xmlBuilder, string.Format("      \"unit\": \"{0}\",", unidade));
                    API_write(xmlBuilder, string.Format("      \"meter_constant\": {0},", valor_constante.ToString().Replace(',','.')));
                    API_write(xmlBuilder, string.Format("      \"distribuidora\": \"{0}\",", distribuidora));
                    API_write(xmlBuilder, string.Format("      \"device_id\": {0},", medicao.IDGateway));
                    API_write(xmlBuilder, string.Format("      \"account_id\": {0}", medicao.IDCliente));

                    // verifica se último item
                    if (conta_item == fim)
                    {
                        API_write(xmlBuilder, "    }");
                    }
                    else
                    {
                        API_write(xmlBuilder, "    },");
                    }

                    // incrementa contador
                    conta_item_publicado++;
                }

                // incrementa contador
                conta_item++;
            }

            // verifica se tem algum item
            if (conta_item_publicado > 0)
            {
                // finaliza
                API_write(xmlBuilder, "  ],");
                API_write(xmlBuilder, string.Format("  \"total\": {0},", medicoes.Count));
                API_write(xmlBuilder, string.Format("  \"per_page\": {0},", _limit));
                API_write(xmlBuilder, string.Format("  \"current_page\": {0}", _page));
                API_write(xmlBuilder, "}");
            }
            else
            {
                // sem itens
                return (false);
            }

            // ContentType
            Response.ContentType = "application/json";

            Response.BinaryWrite(Encoding.UTF8.GetBytes(xmlBuilder.ToString()));
            Response.End();

            return (true);
        }
    }
}