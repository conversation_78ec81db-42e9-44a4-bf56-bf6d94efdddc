﻿using System;
using System.Collections.Generic;
using System.Text;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class APIController
    {
        // Cadastro das empresas
        // https://www.smartenergy.com.br/API/Empresas?Key=1234567890123456&id=1

        // Key = API key do usuário
        // id = id da empresa ou da conta
        // _q = Bus<PERSON> as empresas pelos campos nome
        // _sort = Define o campo de ordenação da lista
        // _order = Define a ordem da lista com base no campo definido
        // _limit = Quantidade de itens por página
        // _page = Página a ser carregada

        // Status
        // 0  - OK
        // 1  - Erro: Senha ou Datas nulas
        // 2  - Erro: Chave incorreta
        // 3  - Erro: Usuário inexistente
        // 4  - Erro: Medição inexistente
        // 5  - Erro: Usuário não pertence ao cliente da medição
        // 6  - Erro: Cliente inexistente
        // 7  - Erro: Usuário não tem acesso ao cliente
        // 8  - Erro: ---
        // 9  - Erro: ---
        // 10 - Erro: Sem registro
        // 11 - Erro: ---
        // 12 - Erro: ---
        // 14 - Erro: ---
        // 20 - Erro: Ultrapassou limite diário de requisições
        // 21 - Erro: Usuário bloqueado
        // 22 - Erro: Gateway inexistente

        public void Empresas(string Key, int id = 0, string _q = "", string _sort = "id", string _order = "asc", int _limit = 10, int _page = 1)
        {
            // Inicializa variáveis
            DateTime Data = new DateTime(2000, 1, 1, 0, 0, 0);

            //
            // Parâmetros
            //
            if (Key == null)
            {
                // Log - [1] key nulo
                API_Log(Key, "Empresas", 0, id, Data, Data, 1);

                // erro
                API_Erro(1, "O campo Key selecionado é inválido");
                return;
            }

            if (_sort.ToLower() != "id" && _sort.ToLower() != "name" && _sort.ToLower() != "account")
            {
                // Log - [1] _sort
                API_Log(Key, "Empresas", 0, id, Data, Data, 1);

                // erro
                API_Erro(1, "O campo _sort deve ser 'id', 'name' ou 'account'");
                return;
            }

            if (_order.ToLower() != "asc" && _order.ToLower() != "desc")
            {
                // Log - [1] _order
                API_Log(Key, "Empresas", 0, id, Data, Data, 1);

                // erro
                API_Erro(1, "O campo _order deve ser 'asc' ou 'desc'");
                return;
            }

            if (_limit < 0)
            {
                // Log - [1] _limit
                API_Log(Key, "Empresas", 0, id, Data, Data, 1);

                // erro
                API_Erro(1, "O campo _limit deve ser maior que zero");
                return;
            }

            if (_page < 0)
            {
                // Log - [1] _page
                API_Log(Key, "Empresas", 0, id, Data, Data, 1);

                // erro
                API_Erro(1, "O campo _page deve ser maior que zero");
                return;
            }

            //
            // USUARIO
            //

            // le usuario
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = usuarioMetodos.ListarPorAPI_Key(Key);

            if (usuario != null)
            {
                // verifica se key eh correta
                if (usuario.API_Key != Key)
                {
                    // Log - [2] chave incorreta
                    API_Log(Key, "Empresas", 0, 0, Data, Data, 2);

                    // erro
                    API_Erro(2, string.Format("O campo Key está incorreto ({0})", Key));
                    return;
                }
            }
            else
            {
                // Log - [3] usuário inexistente
                API_Log(Key, "Empresas", 0, id, Data, Data, 3);

                // erro
                API_Erro(3, string.Format("Usuário Incorreto ({0})", Key));
                return;
            }

            // verifica se usuário bloqueado
            if (usuario.Bloqueio == TIPO_BLOQUEIO.TempoAPI || usuario.Bloqueio == TIPO_BLOQUEIO.Generico)
            {
                // Log - [21] usuário bloqueado
                API_Log(Key, "Empresas", 0, id, Data, Data, 21);

                // erro
                API_Erro(21, string.Format("Usuário Bloqueado ({0})", usuario.IDUsuario));
                return;
            }

            // monta lista
            string strWhere = "";

            // verifica se busca por id
            if (id > 0 && _sort.ToLower() == "id")
            {
                strWhere = string.Format("IDEmpresa = {0}", id);
            }

            // verifica se busca por account
            if (id > 0 && _sort.ToLower() == "account")
            {
                strWhere = string.Format("IDCliente = {0}", id);
            }

            // verifica se busca por nome
            if (_sort.ToLower() == "name")
            {
                // busca
                strWhere = string.Format("(RazaoSocial collate Latin1_General_CI_AI LIKE '%{0}%') ", _q);
                strWhere += "ORDER BY RazaoSocial";

                // ordem decrescente
                if (_order.ToLower() == "desc")
                {
                    strWhere += " DESC";
                }
            }

            // busca
            EmpresasMetodos empresasMetodos = new EmpresasMetodos();
            List<EmpresasDominio> empresas = empresasMetodos.BuscarWhere(strWhere);

            if (empresas == null)
            {
                // Log - [6] empresa inexistente
                API_Log(Key, "Empresas", 0, id, Data, Data, 6);

                // erro
                API_Erro(6, "Empresa inexistente");
                return;
            }

            //
            // REGISTROS
            //

            bool retorno = Empresas_Item(empresas, _limit, _page);

            // verifica se erro nos registros
            if (!retorno)
            {
                // Log do Devices - [10] Sem registro
                API_Log(Key, "Empresas", usuario.IDUsuario, id, Data, Data, 10);

                // erro
                API_Erro(10, "Sem Registros");
                return;
            }

            // Log do Devices - OK
            API_Log(Key, "Empresas", usuario.IDUsuario, id, Data, Data, 0);
            return;
        }

        // itens
        public bool Empresas_Item(List<EmpresasDominio> empresas, int _limit, int _page)
        {

            // JSON
            StringBuilder xmlBuilder = new StringBuilder();
            IFormatProvider iFormatProvider = new System.Globalization.CultureInfo("en-US");

            // inicio e fim
            int conta_item = 0;
            int conta_item_publicado = 0;

            int inicio = (_page - 1) * _limit;
            int fim = _page * _limit;

            if (fim > empresas.Count)
            {
                fim = empresas.Count;
            }

            fim = fim - 1;

            foreach (EmpresasDominio empresa in empresas)
            {
                if (conta_item >= inicio && conta_item <= fim)
                {
                    // cliente
                    int IDCliente = 0;
                    string NomeCliente = "";

                    // cliente
                    ClientesMetodos clientesMetodos = new ClientesMetodos();
                    ClientesDominio cliente = clientesMetodos.ListarPorId(empresa.IDCliente);

                    if (cliente != null)
                    {
                        IDCliente = cliente.IDCliente;
                        NomeCliente = cliente.Nome;
                    }

                    if (conta_item == inicio)
                    {
                        // inicia
                        API_write(xmlBuilder, "{");
                        API_write(xmlBuilder, "  \"items\": [");
                    }

                    // item
                    API_write(xmlBuilder, "    {");
                    API_write(xmlBuilder, string.Format("      \"id\": {0},", empresa.IDEmpresa));
                    API_write(xmlBuilder, string.Format("      \"name\": \"{0}\",", empresa.RazaoSocial));
                    API_write(xmlBuilder, string.Format("      \"cep\": \"{0}\",", empresa.CEP));
                    API_write(xmlBuilder, string.Format("      \"endereco\": \"{0}\",", empresa.Endereco));
                    API_write(xmlBuilder, string.Format("      \"cidade\": \"{0}\",", empresa.NomeCidade));
                    API_write(xmlBuilder, string.Format("      \"estado\": \"{0}\",", empresa.NomeEstado));
                    API_write(xmlBuilder, string.Format("      \"parent_id\": {0},", 0));
                    API_write(xmlBuilder, string.Format("      \"account_id\": {0}", empresa.IDCliente));

                    // verifica se último item
                    if (conta_item == fim)
                    {
                        API_write(xmlBuilder, "    }");
                    }
                    else
                    {
                        API_write(xmlBuilder, "    },");
                    }

                    // incrementa contador
                    conta_item_publicado++;
                }

                // incrementa contador
                conta_item++;
            }

            // verifica se tem algum item
            if (conta_item_publicado > 0)
            {
                // finaliza
                API_write(xmlBuilder, "  ],");
                API_write(xmlBuilder, string.Format("  \"total\": {0},", empresas.Count));
                API_write(xmlBuilder, string.Format("  \"per_page\": {0},", _limit));
                API_write(xmlBuilder, string.Format("  \"current_page\": {0}", _page));
                API_write(xmlBuilder, "}");
            }
            else
            {
                // sem itens
                return (false);
            }

            // ContentType
            Response.ContentType = "application/json";

            Response.BinaryWrite(Encoding.UTF8.GetBytes(xmlBuilder.ToString()));
            Response.End();

            return (true);
        }
    }
}