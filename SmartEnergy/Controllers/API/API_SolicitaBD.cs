﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class APIController
    {
        // Solicitacao Banco de dados
        // https://www.smartenergy.com.br/API/SolicitacaoBD?login=1789&senha=Gestal123&medicao=1&dtBegin=20/06/2018 00:00&dtEnd=20/06/2018 23:45&arquivo=XML
        // login = IDUsuario
        // senha = senha do usuario
        // medicao = IDMedicao
        // dtBegin = data e hora inicial
        // dtEnd = data e hora final
        // arquivo = tipo do arquivo (XML ou JSON)

        // Status
        // 0  - OK
        // 1  - Erro: Senha ou Datas nulas
        // 2  - Erro: Senha diferente da senha do usuário
        // 3  - Erro: Usuário inexistente
        // 4  - Erro: Medição inexistente
        // 5  - Erro: Usuário não pertence ao cliente da medição
        // 6  - Erro: Cliente inexistente
        // 7  - Erro: Usuário não tem acesso ao cliente
        // 8  - Erro: DataIni incorreta
        // 9  - Erro: DataFim incorreta
        // 10 - Erro: Sem registro
        // 11 - Erro: DataIni não é múltiplo de 15
        // 12 - Erro: DataFim não é múltiplo de 15
        // 20 - Erro: Ultrapassou limite diário de requisições
        // 100 - Erro: Usuário não esta na lista de usuários permitidos

        public void SolicitacaoBD(int login, string senha, int medicao, string dtBegin, string dtEnd, string arquivo = "XML")
        {

            //
            // Esta API esta descontinuada, portanto permito apenas o uso pelos usuários que já a utilizam.
            // Novos usuários não poderão usar.
            //
            // Os usuarios abaixo utilizam a API e serão bloqueados:
            // IDUsuario 5512 - Fernanda Viridis - Cliente AEGEA, utiliza a API de forma incorreta e massiva. Foi bloqueado pela lista abaixo.
            // IDUsuario 5337 - Usuário não existe.
            //
            // Os usuarios abaixo utilizam a API e serão liberados para continuar usando:
            // IDUsuario 5057 - Solver Energia
            // IDUsuario 8656 - Minerva
            // IDUsuario 8545 - Green Yellow
            // IDUsuario 4477 - Green Yellow
            // IDUsuario 8302 - Ennergia
            // IDUsuario 7388 - DASA
            List<int> IDUsuarios_Permitidos = new List<int> {
                5057, 8656, 8545, 4477, 8302, 7388
            };

            // verifica se pertence a lista permitida
            bool usuario_permitido = IDUsuarios_Permitidos.Any(IDUsuario => IDUsuario == login);

            if (!usuario_permitido)
            {
                // erro
                API_Erro(100, "Entrar em contato com o departamento Smart Energy para obter informações sobre a API.", arquivo);
                return;
            }

            // Inicializa variáveis
            int IDCliente = 0;
            int IDMedicao = medicao;
            DateTime DataIni = new DateTime(2000, 1, 1, 0, 0, 0);
            DateTime DataFim = new DateTime(2000, 1, 1, 0, 0, 0);

            //
            // Parâmetros
            //
            if (senha == null || dtBegin == null || dtEnd == null)
            {
                // Log do SolicitaBD - [1] senha ou Datas nulas
                API_Log("", "SolicitaBD", login, IDMedicao, DataIni, DataFim, 1);

                // erro
                API_Erro(1, "Faltam Parâmetros", arquivo);
                return;
            }

            //
            // DATAS
            //
            if (!DateTime.TryParse(dtBegin, out DataIni))
            {
                // erro
                DataIni = new DateTime(2000, 1, 1, 0, 0, 0);

                // Log do SolicitaBD - [8] DataIni incorreta
                API_Log("", "SolicitaBD", login, IDMedicao, DataIni, DataFim, 8);

                // erro
                API_Erro(8, string.Format("Data Inicial Incorreta ({0})", dtBegin), arquivo);
                return;

            }

            if (!DateTime.TryParse(dtEnd, out DataFim))
            {
                // erro
                DataFim = new DateTime(2000, 1, 1, 0, 0, 0);

                // Log do SolicitaBD - [9] DataFim incorreta
                API_Log("", "SolicitaBD", login, IDMedicao, DataIni, DataFim, 9);

                // erro
                API_Erro(9, string.Format("Data Final Incorreta ({0})", dtEnd), arquivo);
                return;
            }

            //
            // Verifica se horários são multiplos de 15 (+/-1) minutos
            //
            // Não aceito solicitação que não seja multipo de 15 (+/-1) minutos exatos.
            // Faço isso pois clientes criam robôs para coleta de dados de minuto a minuto.
            if (!((DataIni.Minute == 59 || DataIni.Minute == 0 || DataIni.Minute == 1 ||
                   DataIni.Minute == 14 || DataIni.Minute == 15 || DataIni.Minute == 16 ||
                   DataIni.Minute == 29 || DataIni.Minute == 30 || DataIni.Minute == 31 ||
                   DataIni.Minute == 44 || DataIni.Minute == 45 || DataIni.Minute == 46)))
            {
                // Log do SolicitaBD - [11] DataIni não é múltiplo de 15
                API_Log("", "SolicitaBD", login, IDMedicao, DataIni, DataFim, 11);

                // erro
                API_Erro(11, string.Format("Horário Inicial deve ser múltiplo exato de 15 minutos (00, 15, 30 ou 45): ({0:T})", DataIni), arquivo);
                return;
            }

            if (!((DataFim.Minute == 59 || DataFim.Minute == 0 || DataFim.Minute == 1 ||
                   DataFim.Minute == 14 || DataFim.Minute == 15 || DataFim.Minute == 16 ||
                   DataFim.Minute == 29 || DataFim.Minute == 30 || DataFim.Minute == 31 ||
                   DataFim.Minute == 44 || DataFim.Minute == 45 || DataFim.Minute == 46)))
            {
                // Log do SolicitaBD - [12] DataFimi não é múltiplo de 15
                API_Log("", "SolicitaBD", login, IDMedicao, DataIni, DataFim, 12);

                // erro
                API_Erro(12, string.Format("Horário Final deve ser múltiplo exato de 15 minutos (00, 15, 30 ou 45): ({0:T})", DataFim), arquivo);
                return;
            }


            //
            // USUARIO
            //

            // le usuario
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = usuarioMetodos.ListarPorId(login);

            if (usuario != null)
            {
                // verifica se senha eh correta
                if (usuario.Senha != senha)
                {
                    // Log do SolicitaBD - [2] senha diferente da senha do usuário
                    API_Log("", "SolicitaBD", login, IDMedicao, DataIni, DataFim, 2);

                    // erro
                    API_Erro(2, string.Format("Usuário Incorreto ({0})", login), arquivo);
                    return;
                }
            }
            else
            {
                // Log do SolicitaBD - [3] usuário inexistente
                API_Log("", "SolicitaBD", login, IDMedicao, DataIni, DataFim, 3);

                // erro
                API_Erro(3, string.Format("Usuário Incorreto ({0})", login), arquivo);
                return;
            }

            //
            // NÚMERO DE REQUISIÇÕES
            //
            API_LogMetodos logMetodos = new API_LogMetodos();
            int NumRequisicoes = logMetodos.NumRequisicoes("SolicitaBD", usuario.IDUsuario, IDMedicao);

            // verifica se passou do limite diário
            if (NumRequisicoes >= usuario.API_Limite)
            {
                // Log - [20] Ultrapassou limite diário de requisições
                API_Log("", "SolicitaBD", login, IDMedicao, DataIni, DataFim, 20);

                // erro
                API_Erro(20, string.Format("Ultrapassou limite diário de requisições ({0})", NumRequisicoes), arquivo);
                return;
            }

            //
            // MEDICAO
            //

            // le medicao
            MedicoesMetodos medMetodos = new MedicoesMetodos();
            MedicoesDominio med = medMetodos.ListarPorId(IDMedicao);

            if (med == null)
            {
                // Log do SolicitaBD - [4] medição inexistente
                API_Log("", "SolicitaBD", login, IDMedicao, DataIni, DataFim, 4);

                // erro
                API_Erro(4, string.Format("Medição Incorreta ({0})", IDMedicao), arquivo);
                return;
            }

            // cliente
            IDCliente = med.IDCliente;

            // verifica se usuario pertence a este cliente
            if (usuario.IDTipoAcesso == TIPO_ACESSO.CLIENTE_ADMIN || usuario.IDTipoAcesso == TIPO_ACESSO.CLIENTE_OPER || usuario.IDTipoAcesso == TIPO_ACESSO.DEMONSTRACAO)
            {
                if (usuario.IDCliente != IDCliente)
                {
                    // Log do SolicitaBD - [5] usuário não pertence ao cliente da medição
                    API_Log("", "SolicitaBD", login, IDMedicao, DataIni, DataFim, 5);

                    // erro
                    API_Erro(5, string.Format("Usuário Incorreto ({0})", login), arquivo);
                    return;
                }
            }
            else if (usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR || usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR_ADMIN || usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR_OPER)
            {
                ClientesMetodos clienteMetodos = new ClientesMetodos();
                List<ClientesDominio> clientes = clienteMetodos.ListarPorIDConsultor(usuario.IDCliente, 1);

                if( clientes == null )
                {
                    // Log do SolicitaBD - [6] cliente inexistente
                    API_Log("", "SolicitaBD", login, IDMedicao, DataIni, DataFim, 6);

                    // erro
                    API_Erro(6, string.Format("Usuário Incorreto ({0})", login), arquivo);
                    return;
                }

                int index = clientes.FindIndex(x => x.IDCliente == IDCliente);
                if (index < 0) 
                {
                    // Log do SolicitaBD - [7] usuário não tem acesso ao cliente
                    API_Log("", "SolicitaBD", login, IDMedicao, DataIni, DataFim, 7);

                    // erro
                    API_Erro(7, string.Format("Usuário Incorreto ({0})", login), arquivo);
                    return;
                }
            }
            else
            {
                // Log do SolicitaBD - [7] usuário não tem acesso ao cliente
                API_Log("", "SolicitaBD", login, IDMedicao, DataIni, DataFim, 7);

                // erro
                API_Erro(7, string.Format("Usuário Incorreto ({0})", login), arquivo);
                return;
            }


            //
            // REGISTROS
            //

            bool retorno = false;

            switch(med.IDTipoMedicao)
            {
                case 0:
                case 1:

                    // registros de energia eletrica
                    retorno = SolicitacaoBD_EN(IDCliente, IDMedicao, DataIni, DataFim, arquivo);
                    break;

                case 2:
                case 5:

                    // registros de utilidades
                    retorno = SolicitacaoBD_UTIL(IDCliente, IDMedicao, DataIni, DataFim, arquivo);
                    break;

                case 3:

                    // registros de entradas analogicas
                    retorno = SolicitacaoBD_EA(IDCliente, IDMedicao, DataIni, DataFim, arquivo);
                    break;

                case 4:

                    // registros de ciclometro
                    retorno = SolicitacaoBD_CICLO(IDCliente, IDMedicao, DataIni, DataFim, arquivo);
                    break;
            }

            // verifica se erro nos registros
            if (!retorno)
            {
                // Log do SolicitaBD - [10] Sem registro
                API_Log("", "SolicitaBD", login, IDMedicao, DataIni, DataFim, 10);

                // erro
                API_Erro(10, "Sem Registros", arquivo);
                return;
            }

            // Log do SolicitaBD - OK
            API_Log("", "SolicitaBD", login, IDMedicao, DataIni, DataFim, 0);
            return;
        }

        public bool SolicitacaoBD_EN(int IDCliente, int IDMedicao, DateTime DataIni, DateTime DataFim, string arquivo = "XML")
        {
            StringBuilder xmlBuilder = new StringBuilder();
            IFormatProvider iFormatProvider = new System.Globalization.CultureInfo("en-US");

            //
            // REGISTROS
            //
            EN_Metodos enMetodos = new EN_Metodos();
            List<EN_Dominio> enRegistros = enMetodos.ListarTodosPeriodo(IDCliente, IDMedicao, DataIni, DataFim);

            // verifica se tem registros
            if (enRegistros == null)
            {
                // erro
                return(false);
            }

            if (enRegistros.Count == 0)
            {
                // erro
                return (false);
            }

            // verifica se JSON
            if (arquivo.ToUpper() == "JSON")
            {
                // inicia
                xmlBuilder.Append("{");

                // medicao
                xmlBuilder.Append(string.Format("\"Medicao\": {0},", IDMedicao));

                // campos
                xmlBuilder.Append("\"Campos\": ");
                xmlBuilder.Append("[\"DataHora\",\"Ativo\",\"Reativo\",\"Periodo\"],");

                // registros
                xmlBuilder.Append("\"Registros\": ");
                xmlBuilder.Append("[");

                // registros
                int conta = 0;

                foreach (EN_Dominio reg in enRegistros)
                {
                    xmlBuilder.Append("[");
                    xmlBuilder.Append(string.Format("\"{0:g}\",", reg.DataHora));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.00},", reg.Ativo));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.00},", reg.Reativo));

                    switch (reg.Periodo)
                    {
                        default:
                            xmlBuilder.Append(string.Format("\"{0}\"", reg.Periodo));
                            break;

                        case 0:
                            xmlBuilder.Append("\"P\"");
                            break;

                        case 1:
                            xmlBuilder.Append("\"FPI\"");
                            break;

                        case 2:
                            xmlBuilder.Append("\"FPC\"");
                            break;
                    }

                    // verifica se ultimo
                    if (conta < (enRegistros.Count - 1))
                    {
                        // coloca virgula
                        xmlBuilder.Append("],");
                    }
                    else
                    {
                        xmlBuilder.Append("]");
                    }

                    // proximo
                    conta++;
                }

                // finaliza
                xmlBuilder.Append("]");
                xmlBuilder.Append("}");

                // ContentType
                Response.ContentType = "application/json";

                Response.BinaryWrite(Encoding.UTF8.GetBytes(xmlBuilder.ToString()));
                Response.End();
            }
            else
            {
                // monta XML
                xmlBuilder.Append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
                xmlBuilder.Append(string.Format("<medicao medicaoid=\"{0:000000}\">", IDMedicao));

                // registros
                foreach (EN_Dominio reg in enRegistros)
                {
                    xmlBuilder.Append(string.Format("<registro registroid=\"{0:g}\">", reg.DataHora));

                    xmlBuilder.Append("<ativo>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.00}", reg.Ativo));
                    xmlBuilder.Append("</ativo>");

                    xmlBuilder.Append("<reativo>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.00}", reg.Reativo));
                    xmlBuilder.Append("</reativo>");

                    xmlBuilder.Append("<periodo>");
                    switch (reg.Periodo)
                    {
                        default:
                            xmlBuilder.Append(string.Format("{0}", reg.Periodo));
                            break;

                        case 0:
                            xmlBuilder.Append("P");
                            break;

                        case 1:
                            xmlBuilder.Append("FPI");
                            break;

                        case 2:
                            xmlBuilder.Append("FPC");
                            break;
                    }
                    xmlBuilder.Append("</periodo>");

                    xmlBuilder.Append("</registro>");
                }

                xmlBuilder.Append("</medicao>");

                // ContentType
                Response.ContentType = "text/xml";

                Response.BinaryWrite(Encoding.UTF8.GetBytes(xmlBuilder.ToString()));
                Response.End();
            }

            // ok
            return (true);
        }

        public bool SolicitacaoBD_UTIL(int IDCliente, int IDMedicao, DateTime DataIni, DateTime DataFim, string arquivo = "XML")
        {
            StringBuilder xmlBuilder = new StringBuilder();
            IFormatProvider iFormatProvider = new System.Globalization.CultureInfo("en-US");

            //
            // REGISTROS
            //
            GG_Metodos ggMetodos = new GG_Metodos();
            List<GG_Dominio> ggRegistros = ggMetodos.ListarTodosPeriodo(IDCliente, IDMedicao, DataIni, DataFim);

            // verifica se tem registros
            if (ggRegistros == null)
            {
                // erro
                return (false);
            }

            if (ggRegistros.Count == 0)
            {
                // erro
                return (false);
            }

            // verifica se JSON
            if (arquivo.ToUpper() == "JSON")
            {
                // inicia
                xmlBuilder.Append("{");

                // medicao
                xmlBuilder.Append(string.Format("\"Medicao\": {0},", IDMedicao));

                // campos
                xmlBuilder.Append("\"Campos\": ");
                xmlBuilder.Append("[\"DataHora\",\"Valor\"],");

                // registros
                xmlBuilder.Append("\"Registros\": ");
                xmlBuilder.Append("[");

                // registros
                int conta = 0;

                foreach (GG_Dominio reg in ggRegistros)
                {
                    xmlBuilder.Append("[");
                    xmlBuilder.Append(string.Format("\"{0:g}\",", reg.DataHora));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.0000}", reg.VMed));

                    // verifica se ultimo
                    if (conta < (ggRegistros.Count - 1))
                    {
                        // coloca virgula
                        xmlBuilder.Append("],");
                    }
                    else
                    {
                        xmlBuilder.Append("]");
                    }

                    // proximo
                    conta++;
                }

                // finaliza
                xmlBuilder.Append("]");
                xmlBuilder.Append("}");

                // ContentType
                Response.ContentType = "application/json";

                Response.BinaryWrite(Encoding.UTF8.GetBytes(xmlBuilder.ToString()));
                Response.End();
            }
            else
            {
                // monta XML
                xmlBuilder.Append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
                xmlBuilder.Append(string.Format("<medicao medicaoid=\"{0:000000}\">", IDMedicao));

                // registros
                foreach (GG_Dominio reg in ggRegistros)
                {
                    xmlBuilder.Append(string.Format("<registro registroid=\"{0:g}\">", reg.DataHora));

                    xmlBuilder.Append("<valor>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.0000}", reg.VMed));
                    xmlBuilder.Append("</valor>");

                    xmlBuilder.Append("</registro>");
                }

                xmlBuilder.Append("</medicao>");

                // ContentType
                Response.ContentType = "text/xml";

                Response.BinaryWrite(Encoding.UTF8.GetBytes(xmlBuilder.ToString()));
                Response.End();
            }

            // ok
            return (true);
        }

        public bool SolicitacaoBD_EA(int IDCliente, int IDMedicao, DateTime DataIni, DateTime DataFim, string arquivo = "XML")
        {
            StringBuilder xmlBuilder = new StringBuilder();
            IFormatProvider iFormatProvider = new System.Globalization.CultureInfo("en-US");

            //
            // REGISTROS
            //
            GG_Metodos ggMetodos = new GG_Metodos();
            List<GG_Dominio> ggRegistros = ggMetodos.ListarTodosPeriodo(IDCliente, IDMedicao, DataIni, DataFim);

            // verifica se tem registros
            if (ggRegistros == null)
            {
                // erro
                return (false);
            }

            if (ggRegistros.Count == 0)
            {
                // erro
                return (false);
            }

            // verifica se JSON
            if (arquivo.ToUpper() == "JSON")
            {
                // inicia
                xmlBuilder.Append("{");

                // medicao
                xmlBuilder.Append(string.Format("\"Medicao\": {0},", IDMedicao));

                // campos
                xmlBuilder.Append("\"Campos\": ");
                xmlBuilder.Append("[\"DataHora\",\"Médio\",\"Mínimo\",\"Máximo\"],");

                // registros
                xmlBuilder.Append("\"Registros\": ");
                xmlBuilder.Append("[");

                // registros
                int conta = 0;

                foreach (GG_Dominio reg in ggRegistros)
                {
                    xmlBuilder.Append("[");
                    xmlBuilder.Append(string.Format("\"{0:g}\",", reg.DataHora));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.0000},", reg.VMed));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.0000},", reg.VMin));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.0000}", reg.VMax));

                    // verifica se ultimo
                    if (conta < (ggRegistros.Count - 1))
                    {
                        // coloca virgula
                        xmlBuilder.Append("],");
                    }
                    else
                    {
                        xmlBuilder.Append("]");
                    }

                    // proximo
                    conta++;
                }

                // finaliza
                xmlBuilder.Append("]");
                xmlBuilder.Append("}");

                // ContentType
                Response.ContentType = "application/json";

                Response.BinaryWrite(Encoding.UTF8.GetBytes(xmlBuilder.ToString()));
                Response.End();
            }
            else
            {
                // monta XML
                xmlBuilder.Append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
                xmlBuilder.Append(string.Format("<medicao medicaoid=\"{0:000000}\">", IDMedicao));

                // registros
                foreach (GG_Dominio reg in ggRegistros)
                {
                    xmlBuilder.Append(string.Format("<registro registroid=\"{0:g}\">", reg.DataHora));

                    xmlBuilder.Append("<medio>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.0000}", reg.VMed));
                    xmlBuilder.Append("</medio>");

                    xmlBuilder.Append("<minimo>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.0000}", reg.VMin));
                    xmlBuilder.Append("</minimo>");

                    xmlBuilder.Append("<maximo>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0.0000}", reg.VMax));
                    xmlBuilder.Append("</maximo>");

                    xmlBuilder.Append("</registro>");
                }

                xmlBuilder.Append("</medicao>");

                // ContentType
                Response.ContentType = "text/xml";

                Response.BinaryWrite(Encoding.UTF8.GetBytes(xmlBuilder.ToString()));
                Response.End();
            }

            // ok
            return (true);
        }

        public bool SolicitacaoBD_CICLO(int IDCliente, int IDMedicao, DateTime DataIni, DateTime DataFim, string arquivo = "XML")
        {
            StringBuilder xmlBuilder = new StringBuilder();
            IFormatProvider iFormatProvider = new System.Globalization.CultureInfo("en-US");

            //
            // REGISTROS
            //
            GG_Metodos ggMetodos = new GG_Metodos();
            List<GG_Dominio> ggRegistros = ggMetodos.ListarTodosPeriodo(IDCliente, IDMedicao, DataIni, DataFim);

            // verifica se tem registros
            if (ggRegistros == null)
            {
                // erro
                return (false);
            }

            if (ggRegistros.Count == 0)
            {
                // erro
                return (false);
            }

            // verifica se JSON
            if (arquivo.ToUpper() == "JSON")
            {
                // inicia
                xmlBuilder.Append("{");

                // medicao
                xmlBuilder.Append(string.Format("\"Medicao\": {0},", IDMedicao));

                // campos
                xmlBuilder.Append("\"Campos\": ");
                xmlBuilder.Append("[\"DataHora\",\"Ciclômetro\"],");

                // registros
                xmlBuilder.Append("\"Registros\": ");
                xmlBuilder.Append("[");

                // registros
                int conta = 0;

                foreach (GG_Dominio reg in ggRegistros)
                {
                    xmlBuilder.Append("[");
                    xmlBuilder.Append(string.Format("\"{0:g}\",", reg.DataHora));
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0}", reg.VMed));

                    // verifica se ultimo
                    if (conta < (ggRegistros.Count - 1))
                    {
                        // coloca virgula
                        xmlBuilder.Append("],");
                    }
                    else
                    {
                        xmlBuilder.Append("]");
                    }

                    // proximo
                    conta++;
                }

                // finaliza
                xmlBuilder.Append("]");
                xmlBuilder.Append("}");

                // ContentType
                Response.ContentType = "application/json";

                Response.BinaryWrite(Encoding.UTF8.GetBytes(xmlBuilder.ToString()));
                Response.End();
            }
            else
            {
                // monta XML
                xmlBuilder.Append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
                xmlBuilder.Append(string.Format("<medicao medicaoid=\"{0:000000}\">", IDMedicao));

                // registros
                foreach (GG_Dominio reg in ggRegistros)
                {
                    xmlBuilder.Append(string.Format("<registro registroid=\"{0:g}\">", reg.DataHora));

                    xmlBuilder.Append("<ciclometro>");
                    xmlBuilder.Append(string.Format(iFormatProvider, "{0:0}", reg.VMed));
                    xmlBuilder.Append("</ciclometro>");

                    xmlBuilder.Append("</registro>");
                }

                xmlBuilder.Append("</medicao>");

                // ContentType
                Response.ContentType = "text/xml";

                Response.BinaryWrite(Encoding.UTF8.GetBytes(xmlBuilder.ToString()));
                Response.End();
            }

            // ok
            return (true);
        }
    }
}