﻿using System;
using System.Collections.Generic;
using System.Text;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class APIController
    {
        // Histórico da medição
        // https://www.smartenergy.com.br/API/Devices?Key=1234567890123456&Medicao=1&Formato=XML

        // Key = API key do usuário
        // Medicao = IDMedicao
        // Formato = tipo do arquivo de retorno: 'XML' ou 'JSON' (default XML)

        // Status
        // 0  - OK
        // 1  - Erro: Senha ou Datas nulas
        // 2  - Erro: Chave incorreta
        // 3  - Erro: Usuário inexistente
        // 4  - Erro: Medição inexistente
        // 5  - Erro: Usuário não pertence ao cliente da medição
        // 6  - Erro: Cliente inexistente
        // 7  - Erro: Usuário não tem acesso ao cliente
        // 8  - Erro: ---
        // 9  - Erro: ---
        // 10 - Erro: Sem registro
        // 11 - Erro: ---
        // 12 - Erro: ---
        // 14 - Erro: ---
        // 20 - Erro: Ultrapassou limite diário de requisições
        // 21 - Erro: Usuário bloqueado
        // 22 - Erro: Gateway inexistente

        public void Devices(string Key, int Medicao, string Formato = "XML")
        {
            // Inicializa variáveis
            int IDCliente = 0;
            int IDMedicao = Medicao;

            // Inicializa variáveis
            DateTime Data = new DateTime(2000, 1, 1, 0, 0, 0);

            //
            // Parâmetros
            //
            if (Key == null)
            {
                // Log - [1] key ou Datas nulas
                API_Log(Key, "Devices", 0, IDMedicao, Data, Data, 1);

                // erro
                API_Erro(1, "Faltam Parâmetros", Formato);
                return;
            }

            //
            // USUARIO
            //

            // le usuario
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = usuarioMetodos.ListarPorAPI_Key(Key);

            if (usuario != null)
            {
                // verifica se key eh correta
                if (usuario.API_Key != Key)
                {
                    // Log - [2] chave incorreta
                    API_Log(Key, "Devices", 0, 0, Data, Data, 2);

                    // erro
                    API_Erro(2, string.Format("Chave Incorreta ({0})", Key), Formato);
                    return;
                }
            }
            else
            {
                // Log - [3] usuário inexistente
                API_Log(Key, "Devices", 0, IDMedicao, Data, Data, 3);

                // erro
                API_Erro(3, string.Format("Usuário Incorreto ({0})", Key), Formato);
                return;
            }

            // verifica se usuário bloqueado
            if (usuario.Bloqueio == TIPO_BLOQUEIO.TempoAPI || usuario.Bloqueio == TIPO_BLOQUEIO.Generico)
            {
                // Log - [21] usuário bloqueado
                API_Log(Key, "Devices", 0, IDMedicao, Data, Data, 21);

                // erro
                API_Erro(21, string.Format("Usuário Bloqueado ({0})", usuario.IDUsuario), Formato);
                return;
            }

            //
            // NÚMERO DE REQUISIÇÕES
            //
            API_LogMetodos logMetodos = new API_LogMetodos();
            int NumRequisicoes = logMetodos.NumRequisicoes("Historico", usuario.IDUsuario, IDMedicao);

            // verifica se passou do limite diário
            if (NumRequisicoes >= usuario.API_Limite)
            {
                // Log - [20] Ultrapassou limite diário de requisições
                API_Log(Key, "Devices", 0, IDMedicao, Data, Data, 20);

                // erro
                API_Erro(20, string.Format("Ultrapassou limite diário de requisições ({0})", NumRequisicoes), Formato);
                return;
            }

            //
            // MEDICAO
            //

            // le medicao
            MedicoesMetodos medMetodos = new MedicoesMetodos();
            MedicoesDominio med = medMetodos.ListarPorId(IDMedicao);

            if (med == null)
            {
                // Log - [4] medição inexistente
                API_Log(Key, "Devices", usuario.IDUsuario, IDMedicao, Data, Data, 4);

                // erro
                API_Erro(4, string.Format("Medição Incorreta ({0})", IDMedicao), Formato);
                return;
            }

            // cliente
            IDCliente = med.IDCliente;

            // verifica se usuario pertence a este cliente
            if (usuario.IDTipoAcesso == TIPO_ACESSO.CLIENTE_ADMIN || usuario.IDTipoAcesso == TIPO_ACESSO.CLIENTE_OPER || usuario.IDTipoAcesso == TIPO_ACESSO.DEMONSTRACAO)
            {
                if (usuario.IDCliente != IDCliente)
                {
                    // Log - [5] usuário não pertence ao cliente da medição
                    API_Log(Key, "Devices", usuario.IDUsuario, IDMedicao, Data, Data, 5);

                    // erro
                    API_Erro(5, string.Format("Usuário ({0}) não pertence ao cliente ({1})", usuario.IDUsuario, usuario.IDCliente), Formato);
                    return;
                }
            }
            else if (usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR || usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR_ADMIN || usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR_OPER)
            {
                ClientesMetodos clienteMetodos = new ClientesMetodos();
                List<ClientesDominio> clientes = clienteMetodos.ListarPorIDConsultor(usuario.IDCliente, 1);

                if( clientes == null )
                {
                    // Log - [6] cliente inexistente
                    API_Log(Key, "Devices", usuario.IDUsuario, IDMedicao, Data, Data, 6);

                    // erro
                    API_Erro(6, string.Format("Cliente inexistente ({0})", usuario.IDCliente), Formato);
                    return;
                }

                int index = clientes.FindIndex(x => x.IDCliente == IDCliente);
                if (index < 0) 
                {
                    // Log - [7] usuário não tem acesso ao cliente
                    API_Log(Key, "Devices", usuario.IDUsuario, IDMedicao, Data, Data, 7);

                    // erro
                    API_Erro(7, string.Format("Usuário ({0}) não tem acesso ao cliente ({1})", usuario.IDUsuario, usuario.IDCliente), Formato);
                    return;
                }
            }


            // le supervisão medicao
            SupervMedicoesMetodos supMedicaoMetodos = new SupervMedicoesMetodos();
            List<SupervMedicoesDominio> supMedicaoLista = supMedicaoMetodos.ListarPorIDMedicao(med.IDCliente, med.IDMedicao);

            if (supMedicaoLista == null)
            {
                // Log - [22] gateway inexistente
                API_Log(Key, "Devices", usuario.IDUsuario, IDMedicao, Data, Data, 4);

                // erro
                API_Erro(4, string.Format("Supervisão Medição Inexistente ({0})", med.IDGateway), Formato);
                return;
            }

            if (supMedicaoLista.Count < 1)
            {
                // Log - [22] gateway inexistente
                API_Log(Key, "Devices", usuario.IDUsuario, IDMedicao, Data, Data, 4);

                // erro
                API_Erro(4, string.Format("Supervisão Medição Inexistente ({0})", med.IDGateway), Formato);
                return;
            }

            SupervMedicoesDominio supMedicao = supMedicaoLista[0];

            //
            // GATEWAY
            //

            // le gateway
            GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewayMetodos.ListarPorId(med.IDGateway);

            if (gateway == null)
            {
                // Log - [22] gateway inexistente
                API_Log(Key, "Devices", usuario.IDUsuario, IDMedicao, Data, Data, 22);

                // erro
                API_Erro(22, string.Format("Gateway Inexistente ({0})", med.IDGateway), Formato);
                return;
            }

            // le supervisão gateway
            SupervGatewaysMetodos supGatewayMetodos = new SupervGatewaysMetodos();
            SupervGatewaysDominio supGateway = supGatewayMetodos.ListarPorIDGateway(med.IDGateway);

            if (supGateway == null)
            {
                // Log - [22] gateway inexistente
                API_Log(Key, "Devices", usuario.IDUsuario, IDMedicao, Data, Data, 22);

                // erro
                API_Erro(22, string.Format("Supervisão Gateway Inexistente ({0})", med.IDGateway), Formato);
                return;
            }

            //
            // CLIENTE
            //

            // le cliente
            ClientesMetodos clientesMetodos = new ClientesMetodos();
            ClientesDominio cliente = clientesMetodos.ListarPorId(med.IDCliente);

            if (gateway == null)
            {
                // Log - [22] gateway inexistente
                API_Log(Key, "Devices", usuario.IDUsuario, IDMedicao, Data, Data, 6);

                // erro
                API_Erro(6, string.Format("Cliente Inexistente ({0})", med.IDGateway), Formato);
                return;
            }

            //
            // REGISTROS
            //

            bool retorno = Device_Item(cliente, med, supMedicao, gateway, supGateway);

            // verifica se erro nos registros
            if (!retorno)
            {
                // Log do Devices - [10] Sem registro
                API_Log(Key, "Devices", usuario.IDUsuario, IDMedicao, Data, Data, 10);

                // erro
                API_Erro(10, "Sem Registros", Formato);
                return;
            }

            // Log do Devices - OK
            API_Log(Key, "Devices", usuario.IDUsuario, IDMedicao, Data, Data, 0);
            return;
        }


        public bool Device_Item(ClientesDominio cliente, MedicoesDominio med, SupervMedicoesDominio supMedicao, GatewaysDominio gateway, SupervGatewaysDominio supGateway)
        {
            // constante
            EN_K_Metodos constanteMetodos = new EN_K_Metodos();
            EN_K_Dominio constante = constanteMetodos.UltimaConstante(med.IDCliente, med.IDMedicao);
            double valor_constante = 0.0;

            if (constante != null)
            {
                valor_constante = constante.Constante;
            }

            // hourly_frequency
            int frequencia_horaria;
            switch (gateway.IDTipoTempo)
            {
                default:
                    frequencia_horaria = 0;
                    break;
                case 12:
                    frequencia_horaria = 4;
                    break;
                case 13:
                    frequencia_horaria = 1;
                    break;
            }

            // type
            int type = 0;
            string type_name = "";
            switch (med.IDTipoMedicao)
            {
                default:
                case TIPOS_MEDICAO.energia:
                    type = 0;
                    type_name = "Energia";
                    break;

            }

            // subtype
            int subtype = 0;
            string subtype_name = "";
            switch (med.IDCategoriaMedicao)
            {
                default:
                case CATEGORIA_MEDICAO.PRINCIPAL:
                    subtype = 0;
                    subtype_name = "Fronteira";
                    break;

                case CATEGORIA_MEDICAO.SETORIAL:
                    subtype = 1;
                    subtype_name = "Setorial";
                    break;
            }

            // status
            DateTime startTime = supGateway.DataHora;
            DateTime endTime = DateTime.Now;
            TimeSpan dif_hour = endTime.Subtract(startTime);
            int minutos = (dif_hour.Days * 24 * 60) + (dif_hour.Hours * 60) + dif_hour.Minutes;

            // verifica se última recepção maior que 2 horas
            int status = 1;
            string status_name = "Conectado";
            if (minutos > 120)
            {
                status = 2;
                status_name = "Desconectado";
            }

            // comment
            ObservacaoGatewayMetodos observacoesMetodos = new ObservacaoGatewayMetodos();
            ObservacaoGatewayDominio observacaoGateway = observacoesMetodos.ListarPrimeiroGateway(gateway.IDGateway);

            string comment = "";
            DateTime comment_date = new DateTime(2000, 1, 1, 0, 0, 0);
            if (observacaoGateway != null)
            {
                comment = observacaoGateway.Observacao;
                comment_date = observacaoGateway.DataHora;
            }


            // XML ou JSON
            StringBuilder xmlBuilder = new StringBuilder();
            IFormatProvider iFormatProvider = new System.Globalization.CultureInfo("en-US");

            // inicia
            xmlBuilder.Append("{\r\n");

            // medições
            xmlBuilder.Append("  \"items\": [\r\n");
            xmlBuilder.Append("    {\r\n");
            xmlBuilder.Append(string.Format("      \"id\": {0},\r\n", med.IDMedicao));
            xmlBuilder.Append(string.Format("      \"code\": \"{0}\" ,\r\n", med.CodigoInstalacao));
            xmlBuilder.Append(string.Format("      \"connection\": \"{0}\",\r\n", "gsm"));
            xmlBuilder.Append(string.Format("      \"url_firmware\": \"{0}\",\r\n", ""));
            xmlBuilder.Append(string.Format("      \"iccid\": \"{0}\",\r\n", supGateway.ICC));
            xmlBuilder.Append(string.Format("      \"version\": \"{0} {1}\",\r\n", supGateway.ModeloEq, supGateway.VersaoEq));
            xmlBuilder.Append(string.Format("      \"ip\": \"{0}\",\r\n", supGateway.IP));
            xmlBuilder.Append(string.Format("      \"alert_seconds\": {0},\r\n", 0));
            xmlBuilder.Append(string.Format("      \"current_relation\": {0},\r\n", 1));
            xmlBuilder.Append(string.Format("      \"potencial_relation\": {0},\r\n", 1));
            xmlBuilder.Append(string.Format("      \"meter_constant\": \"{0:0.0000}\",\r\n", valor_constante.ToString("0.0000").Replace(",",".")));
            xmlBuilder.Append(string.Format("      \"ssid_op\": \"{0}\",\r\n", supGateway.OP));
            xmlBuilder.Append(string.Format("      \"hourly_frequency\": {0},\r\n", frequencia_horaria));

            xmlBuilder.Append("      \"model\": {\r\n");
            xmlBuilder.Append(string.Format("        \"id\": {0},\r\n", med.IDMedicao));
            xmlBuilder.Append(string.Format("        \"name\": \"{0}\",\r\n", med.Nome));
            xmlBuilder.Append("      },\r\n");

            xmlBuilder.Append("      \"equipment\": {\r\n");
            xmlBuilder.Append(string.Format("        \"id\": {0},\r\n", gateway.IDGateway));
            xmlBuilder.Append(string.Format("        \"name\": \"{0}\",\r\n", gateway.Nome));
            xmlBuilder.Append(string.Format("        \"activation_date\": \"{0}\",\r\n", "2000-01-01"));
            xmlBuilder.Append("      },\r\n");

            xmlBuilder.Append("      \"company\": {\r\n");
            xmlBuilder.Append(string.Format("        \"id\": {0},\r\n", cliente.IDCliente));
            xmlBuilder.Append(string.Format("        \"name\": \"{0}\",\r\n", cliente.Nome));
            xmlBuilder.Append("      },\r\n");

            xmlBuilder.Append("      \"type\": {\r\n");
            xmlBuilder.Append(string.Format("        \"id\": {0},\r\n", type));
            xmlBuilder.Append(string.Format("        \"name\": \"{0}\",\r\n", type_name));
            xmlBuilder.Append("      },\r\n");

            xmlBuilder.Append("      \"subtype\": {\r\n");
            xmlBuilder.Append(string.Format("        \"id\": {0},\r\n", subtype));
            xmlBuilder.Append(string.Format("        \"name\": \"{0}\",\r\n", subtype_name));
            xmlBuilder.Append("      },\r\n");

            xmlBuilder.Append("      \"status\": {\r\n");
            xmlBuilder.Append(string.Format("        \"id\": {0},\r\n", status));
            xmlBuilder.Append(string.Format("        \"name\": \"{0}\",\r\n", status_name));
            xmlBuilder.Append("      },\r\n");

            xmlBuilder.Append("      \"master\": {\r\n");
            xmlBuilder.Append(string.Format("        \"id\": {0},\r\n", 0));
            xmlBuilder.Append(string.Format("        \"code\": \"{0}\",\r\n", ""));
            xmlBuilder.Append("      },\r\n");

            xmlBuilder.Append("      \"comment\": {\r\n");
            xmlBuilder.Append(string.Format("        \"value\": \"{0}\",\r\n", comment));
            xmlBuilder.Append(string.Format("        \"date\": \"{0:yyyy-MM-dd HH-mm-ss}\",\r\n", comment_date));
            xmlBuilder.Append("      }\r\n");

            // finaliza
            xmlBuilder.Append("    }\r\n");
            xmlBuilder.Append("  ],\r\n");
            xmlBuilder.Append("  \"total\": 1,\r\n");
            xmlBuilder.Append("  \"per_page\": 1,\r\n");
            xmlBuilder.Append("  \"current_page\": 1\r\n");
            xmlBuilder.Append("}");

            // ContentType
            Response.ContentType = "application/json";

            Response.BinaryWrite(Encoding.UTF8.GetBytes(xmlBuilder.ToString()));
            Response.End();

            return(true);
        }
    }
}