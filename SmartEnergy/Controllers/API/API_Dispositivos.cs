﻿using System;
using System.Collections.Generic;
using System.Text;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class APIController
    {
        // Cadastro dos Dispositivos
        // https://www.smartenergy.com.br/API/Dispositivos?Key=1234567890123456&id=1

        // Key = API key do usuário
        // id = id do dispositivo ou da conta
        // _q = Busca os Dispositivos pelos campos nome
        // _sort = Define o campo de ordenação da lista
        // _order = Define a ordem da lista com base no campo definido
        // _limit = Quantidade de itens por página
        // _page = Página a ser carregada

        // Status
        // 0  - OK
        // 1  - Erro: Senha ou Datas nulas
        // 2  - Erro: Chave incorreta
        // 3  - Erro: Usuário inexistente
        // 4  - Erro: Medição inexistente
        // 5  - Erro: Usuário não pertence ao cliente da medição
        // 6  - Erro: Cliente inexistente
        // 7  - Erro: Usuário não tem acesso ao cliente
        // 8  - Erro: ---
        // 9  - Erro: ---
        // 10 - Erro: Sem registro
        // 11 - Erro: ---
        // 12 - Erro: ---
        // 14 - Erro: ---
        // 20 - Erro: Ultrapassou limite diário de requisições
        // 21 - Erro: Usuário bloqueado
        // 22 - Erro: Gateway inexistente

        public void Dispositivos(string Key, int id = 0, string _q = "", string _sort = "id", string _order = "asc", int _limit = 10, int _page = 1)
        {
            // Inicializa variáveis
            DateTime Data = new DateTime(2000, 1, 1, 0, 0, 0);

            //
            // Parâmetros
            //
            if (Key == null)
            {
                // Log - [1] key nulo
                API_Log(Key, "Dispositivos", 0, id, Data, Data, 1);

                // erro
                API_Erro(1, "O campo Key selecionado é inválido");
                return;
            }

            if (_sort.ToLower() != "id" && _sort.ToLower() != "name" && _sort.ToLower() != "account")
            {
                // Log - [1] _sort
                API_Log(Key, "Dispositivos", 0, id, Data, Data, 1);

                // erro
                API_Erro(1, "O campo _sort deve ser 'id', 'name' ou 'account'");
                return;
            }

            if (_order.ToLower() != "asc" && _order.ToLower() != "desc")
            {
                // Log - [1] _order
                API_Log(Key, "Dispositivos", 0, id, Data, Data, 1);

                // erro
                API_Erro(1, "O campo _order deve ser 'asc' ou 'desc'");
                return;
            }

            if (_limit < 0)
            {
                // Log - [1] _limit
                API_Log(Key, "Dispositivos", 0, id, Data, Data, 1);

                // erro
                API_Erro(1, "O campo _limit deve ser maior que zero");
                return;
            }

            if (_page < 0)
            {
                // Log - [1] _page
                API_Log(Key, "Dispositivos", 0, id, Data, Data, 1);

                // erro
                API_Erro(1, "O campo _page deve ser maior que zero");
                return;
            }

            //
            // USUARIO
            //

            // le usuario
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = usuarioMetodos.ListarPorAPI_Key(Key);

            if (usuario != null)
            {
                // verifica se key eh correta
                if (usuario.API_Key != Key)
                {
                    // Log - [2] chave incorreta
                    API_Log(Key, "Dispositivos", 0, 0, Data, Data, 2);

                    // erro
                    API_Erro(2, string.Format("O campo Key está incorreto ({0})", Key));
                    return;
                }
            }
            else
            {
                // Log - [3] usuário inexistente
                API_Log(Key, "Dispositivos", 0, id, Data, Data, 3);

                // erro
                API_Erro(3, string.Format("Usuário Incorreto ({0})", Key));
                return;
            }

            // verifica se usuário bloqueado
            if (usuario.Bloqueio == TIPO_BLOQUEIO.TempoAPI || usuario.Bloqueio == TIPO_BLOQUEIO.Generico)
            {
                // Log - [21] usuário bloqueado
                API_Log(Key, "Dispositivos", 0, id, Data, Data, 21);

                // erro
                API_Erro(21, string.Format("Usuário Bloqueado ({0})", usuario.IDUsuario));
                return;
            }

            // monta lista
            string strWhere = "";

            // verifica se busca por id
            if (id > 0 && _sort.ToLower() == "id")
            {
                strWhere = string.Format("IDGateway = {0}", id);
            }

            // verifica se busca por account
            if (id > 0 && _sort.ToLower() == "account")
            {
                strWhere = string.Format("IDCliente = {0}", id);
            }

            // verifica se busca por nome
            if (_sort.ToLower() == "name")
            {
                // busca
                strWhere = string.Format("(Nome collate Latin1_General_CI_AI LIKE '%{0}%') ", _q);
                strWhere += "ORDER BY Nome";

                // ordem decrescente
                if (_order.ToLower() == "desc")
                {
                    strWhere += " DESC";
                }
            }

            // busca
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            List<GatewaysDominio> gateways = gatewaysMetodos.BuscarWhere(strWhere);

            if (gateways == null)
            {
                // Log - [6] gateway inexistente
                API_Log(Key, "Dispositivos", 0, id, Data, Data, 6);

                // erro
                API_Erro(6, "Gateway inexistente");
                return;
            }

            //
            // REGISTROS
            //

            bool retorno = Dispositivos_Item(gateways, _limit, _page);

            // verifica se erro nos registros
            if (!retorno)
            {
                // Log do Dispositivos - [10] Sem registro
                API_Log(Key, "Dispositivos", usuario.IDUsuario, id, Data, Data, 10);

                // erro
                API_Erro(10, "Sem Registros");
                return;
            }

            // Log do Dispositivos - OK
            API_Log(Key, "Dispositivos", usuario.IDUsuario, id, Data, Data, 0);
            return;
        }

        // itens
        public bool Dispositivos_Item(List<GatewaysDominio> gateways, int _limit, int _page)
        {

            // JSON
            StringBuilder xmlBuilder = new StringBuilder();
            IFormatProvider iFormatProvider = new System.Globalization.CultureInfo("en-US");

            // inicio e fim
            int conta_item = 0;
            int conta_item_publicado = 0;

            int inicio = (_page - 1) * _limit;
            int fim = _page * _limit;

            if (fim > gateways.Count)
            {
                fim = gateways.Count;
            }

            fim = fim - 1;

            // le supervisão gateway
            SupervGatewaysMetodos supGatewayMetodos = new SupervGatewaysMetodos();
            List<SupervGatewaysDominio> supsGateway = supGatewayMetodos.ListarTodos(10, false);

            // lê empresa
            EmpresasMetodos empresaMetodos = new EmpresasMetodos();
            List<EmpresasDominio> empresas = empresaMetodos.ListarTodos();

            // lê número de série
            NumeroSerieMetodos numSerieMetodos = new NumeroSerieMetodos();
            List<NumeroSerieDominio> numsSerie = numSerieMetodos.ListarTodos();


            foreach (GatewaysDominio gateway in gateways)
            {
                if (conta_item >= inicio && conta_item <= fim)
                {
                    if (conta_item == inicio)
                    {
                        // inicia
                        API_write(xmlBuilder, "{");
                        API_write(xmlBuilder, "  \"items\": [");
                    }

                    // hourly_frequency
                    int frequencia_horaria = 0;

                    switch (gateway.IDTipoTempo)
                    {
                        default:
                            frequencia_horaria = 0;
                            break;
                        case 12:
                            frequencia_horaria = 4;
                            break;
                        case 13:
                            frequencia_horaria = 1;
                            break;
                    }

                    // le supervisão gateway
                    SupervGatewaysDominio supGateway = supsGateway.Find(e => e.IDGateway == gateway.IDGateway);

                    string device_model = "---";
                    string versao = "---";
                    string ssid_op = "---";
                    string iccid = "---";

                    if (supGateway != null)
                    {
                        device_model = supGateway.ModeloEq;
                        versao = supGateway.VersaoEq;
                        ssid_op = supGateway.OP;
                        iccid = supGateway.ICC;
                    }

                    // lê empresa
                    EmpresasDominio empresa = empresas.Find(e => e.IDEmpresa == gateway.IDEmpresa);

                    double longitude = 0.0;
                    double latitude = 0.0;

                    if (empresa != null)
                    {
                        longitude = empresa.Longitude;
                        latitude = empresa.Latitude;
                    }

                    // lê número de série
                    NumeroSerieDominio numSerie = numsSerie.Find(e => e.IDNumeroSerie == gateway.IDNumeroSerie);

                    string NS_Gateway = "---";

                    if (numSerie != null)
                    {
                        NS_Gateway = numSerie.NS_Gateway;
                    }


                    // item
                    API_write(xmlBuilder, "    {");
                    API_write(xmlBuilder, string.Format("      \"id\": {0},", gateway.IDGateway));
                    API_write(xmlBuilder, string.Format("      \"name\": \"{0}\",", gateway.Nome));
                    API_write(xmlBuilder, string.Format("      \"hourly_frequency\": {0},", frequencia_horaria));
                    API_write(xmlBuilder, string.Format("      \"code\": \"{0}\",", NS_Gateway));
                    API_write(xmlBuilder, string.Format("      \"device_model\": \"{0}\",", device_model));
                    API_write(xmlBuilder, string.Format("      \"version\": \"{0}\",", versao));
                    API_write(xmlBuilder, string.Format("      \"ssid_op\": \"{0}\",", ssid_op));
                    API_write(xmlBuilder, string.Format("      \"iccid\": \"{0}\",", iccid));
                    API_write(xmlBuilder, string.Format("      \"longitude\": {0},", longitude.ToString().Replace(',','.')));
                    API_write(xmlBuilder, string.Format("      \"latitude\": {0},", latitude.ToString().Replace(',', '.')));
                    API_write(xmlBuilder, string.Format("      \"company_id\": {0},", gateway.IDEmpresa));
                    API_write(xmlBuilder, string.Format("      \"account_id\": {0}", gateway.IDCliente));

                    // verifica se último item
                    if (conta_item == fim)
                    {
                        API_write(xmlBuilder, "    }");
                    }
                    else
                    {
                        API_write(xmlBuilder, "    },");
                    }

                    // incrementa contador
                    conta_item_publicado++;
                }

                // incrementa contador
                conta_item++;
            }

            // verifica se tem algum item
            if (conta_item_publicado > 0)
            {
                // finaliza
                API_write(xmlBuilder, "  ],");
                API_write(xmlBuilder, string.Format("  \"total\": {0},", gateways.Count));
                API_write(xmlBuilder, string.Format("  \"per_page\": {0},", _limit));
                API_write(xmlBuilder, string.Format("  \"current_page\": {0}", _page));
                API_write(xmlBuilder, "}");
            }
            else
            {
                // sem itens
                return (false);
            }

            // ContentType
            Response.ContentType = "application/json";

            Response.BinaryWrite(Encoding.UTF8.GetBytes(xmlBuilder.ToString()));
            Response.End();

            return (true);
        }
    }
}