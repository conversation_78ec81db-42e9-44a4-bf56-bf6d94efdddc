﻿using System;
using System.Collections.Generic;
using System.Text;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class APIController
    {
        // Lista das Medições do usuário
        // http://www.smartenergy.com.br/API/MedicoesLista?Key=1234567890123456&Formato=XML

        // Key = API key do usuário
        // Formato = tipo do arquivo de retorno: 'XML' ou 'JSON' (default XML)

        // Status
        // 0  - OK
        // 1  - Erro: Key ou Datas nulas
        // 2  - Erro: Chave incorreta
        // 3  - Erro: Usuário inexistente
        // 4  - Erro: Usuário não é Cliente ou Gestor
        // 5  - Erro: Medicoes inexistentes
        // 6  - Erro: Cliente inexistente
        // 7  - Erro: ---
        // 8  - Erro: ---
        // 9  - Erro: ---
        // 10 - Erro: ---
        // 11 - Erro: ---
        // 12 - Erro: ---
        // 20 - Erro: Ultrapassou limite diário de requisições
        // 21 - Erro: Usuário bloqueado

        public class MedicoesListaAPI
        {
            public int IDCliente { get; set; }
            public string NomeCliente { get; set; }

            public int IDMedicao { get; set; }
            public string NomeMedicao { get; set; }
            public int IDTipoMedicao { get; set; }
            public string Referencia { get; set; }

        }


        // Lista das Medições do usuário
        public void MedicoesLista(string Key, string Formato = "XML")
        {
            // Inicializa variáveis
            DateTime Data = new DateTime(2000, 1, 1, 0, 0, 0);

            //
            // Parâmetros
            //
            if (Key == null)
            {
                // Log - [1] key nula
                API_Log(Key, "MedicoesLista", 0, 0, Data, Data, 1);

                // erro
                API_Erro(1, "Faltam Parâmetros", Formato);
                return;
            }

            //
            // USUARIO
            //

            // le usuario
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuario = usuarioMetodos.ListarPorAPI_Key(Key);

            if (usuario != null)
            {
                // verifica se key eh correta
                if (usuario.API_Key != Key)
                {
                    // Log - [2] chave incorreta
                    API_Log(Key, "MedicoesLista", 0, 0, Data, Data, 2);

                    // erro
                    API_Erro(2, string.Format("Chave Incorreta ({0})", Key), Formato);
                    return;
                }
            }
            else
            {
                // Log - [3] usuário inexistente
                API_Log(Key, "MedicoesLista", 0, 0, Data, Data, 3);

                // erro
                API_Erro(3, string.Format("Usuário Incorreto ({0})", Key), Formato);
                return;
            }

            // verifica se usuário bloqueado
            if (usuario.Bloqueio == TIPO_BLOQUEIO.TempoAPI || usuario.Bloqueio == TIPO_BLOQUEIO.Generico)
            {
                // Log - [21] usuário bloqueado
                API_Log(Key, "MedicoesLista", 0, 0, Data, Data, 21);

                // erro
                API_Erro(21, string.Format("Usuário Bloqueado ({0})", usuario.IDUsuario), Formato);
                return;
            }

            //
            // NÚMERO DE REQUISIÇÕES
            //
            API_LogMetodos logMetodos = new API_LogMetodos();
            int NumRequisicoes = logMetodos.NumRequisicoes("MedicoesLista", usuario.IDUsuario);

            // verifica se passou do limite diário
            if (NumRequisicoes >= usuario.API_Limite)
            {
                // Log - [20] Ultrapassou limite diário de requisições
                API_Log(Key, "MedicoesLista", 0, 0, Data, Data, 20);

                // erro
                API_Erro(20, string.Format("Ultrapassou limite diário de requisições ({0})", NumRequisicoes), Formato);
                return;
            }

            //
            // MEDICOES
            //

            // verifica se Cliente
            if (usuario.IDTipoAcesso == TIPO_ACESSO.CLIENTE_ADMIN || usuario.IDTipoAcesso == TIPO_ACESSO.CLIENTE_OPER || usuario.IDTipoAcesso == TIPO_ACESSO.DEMONSTRACAO)
            {
                // medições
                MedicoesLista_Cliente(usuario, Formato);

                // Log - OK
                API_Log(Key, "MedicoesLista", usuario.IDUsuario, 0, Data, Data, 0);
                return;
            }

            // verifica se Gestor
            if (usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR || usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR_ADMIN || usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR_OPER)
            {
                // medições
                MedicoesLista_Gestor(usuario, Formato);

                // Log - OK
                API_Log(Key, "MedicoesLista", usuario.IDUsuario, 0, Data, Data, 0);
                return;
            }

            // Log - [4] Usuário não é Cliente ou Gestor
            API_Log(Key, "MedicoesLista", usuario.IDUsuario, 0, Data, Data, 4);

            // erro
            API_Erro(4, string.Format("Usuário não é Cliente ou Gestor ({0})", Key), Formato);
            return;
        }


        public void MedicoesLista_Cliente(UsuarioDominio usuario, string Formato = "XML")
        {
            // Inicializa variáveis
            DateTime Data = new DateTime(2000, 1, 1, 0, 0, 0);

            // IDCliente
            int IDCliente = usuario.IDCliente;

            // cliente
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            ClientesDominio cliente = clienteMetodos.ListarPorId(IDCliente);

            if (cliente == null)
            {
                // Log - [6] Cliente inexistente
                API_Log(usuario.API_Key, "MedicoesLista", usuario.IDUsuario, 0, Data, Data, 6);

                // erro
                API_Erro(6, "Sem Cliente", Formato);
                return;
            }

            // lista de medições
            List<MedicoesListaAPI> medicoesAPI = new List<MedicoesListaAPI>();

            // medicoes habilitadas
            List<int> ConfigMedList = new List<int>();

            // leio medicoes habilitadas deste usuario
            UsuarioMedicaoMetodos usuariomedicaoMetodos = new UsuarioMedicaoMetodos();
            List<UsuarioMedicaoDominio> usuariosmedicoes = usuariomedicaoMetodos.ListarTodasMedicoesIDUsuario(usuario.IDUsuario);

            // verifica se tem medicao
            if (usuariosmedicoes == null)
            {
                // Log - [5] Medicoes inexistentes
                API_Log(usuario.API_Key, "MedicoesLista", usuario.IDUsuario, 0, Data, Data, 5);

                // erro
                API_Erro(5, "Sem Medições", Formato);
                return;
            }

            // copia medições habilitadas
            foreach (UsuarioMedicaoDominio usuarioMed in usuariosmedicoes)
            {
                ConfigMedList.Add(usuarioMed.IDMedicao);
            }

            // medições dos clientes do cliente
            MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
            List<MedicoesDominio> medicoes = medicaoMetodos.ListarTodos(cliente.IDCliente, (ConfigMedList.Count == 0 ? null : ConfigMedList), " ORDER BY nome");

            if (medicoes == null)
            {
                // Log - [5] Medicoes inexistentes
                API_Log(usuario.API_Key, "MedicoesLista", usuario.IDUsuario, 0, Data, Data, 5);

                // erro
                API_Erro(5, "Sem Medições", Formato);
                return;
            }

            // percorre medicoes
            foreach (MedicoesDominio medicao in medicoes)
            {
                // medicao API
                MedicoesListaAPI medicaoAPI = new MedicoesListaAPI();

                medicaoAPI.IDCliente = cliente.IDCliente;
                medicaoAPI.NomeCliente = cliente.Nome;
                medicaoAPI.IDMedicao = medicao.IDMedicao;
                medicaoAPI.NomeMedicao = medicao.Nome;
                medicaoAPI.IDTipoMedicao = medicao.IDTipoMedicao;
                medicaoAPI.Referencia = medicao.Referencia;

                // coloca na lista
                medicoesAPI.Add(medicaoAPI);
            }

            // lista de medições
            MedicoesLista_Lista(medicoesAPI, Formato);

            return;
        }

        public void MedicoesLista_Gestor(UsuarioDominio usuario, string Formato = "XML")
        {
            // Inicializa variáveis
            DateTime Data = new DateTime(2000, 1, 1, 0, 0, 0);

            // IDConsultor
            int IDConsultor = usuario.IDCliente;

            // clientes ativos do gestor
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> clientes = clienteMetodos.ListarPorIDConsultor(IDConsultor, 1);

            if (clientes == null)
            {
                // Log - [6] Clientes inexistentes
                API_Log(usuario.API_Key, "MedicoesLista", usuario.IDUsuario, 0, Data, Data, 6);

                // erro
                API_Erro(6, "Sem Clientes", Formato);
                return;
            }

            // lista de medições
            List<MedicoesListaAPI> medicoesAPI = new List<MedicoesListaAPI>();

            // medicoes habilitadas
            List<int> ConfigMedList = new List<int>();

            // verifica se consultor operador
            if (usuario.IDTipoAcesso == TIPO_ACESSO.CONSULTOR_OPER)
            {
                // leio medicoes habilitadas deste usuario
                UsuarioMedicaoMetodos usuariomedicaoMetodos = new UsuarioMedicaoMetodos();
                List<UsuarioMedicaoDominio> usuariosmedicoes = usuariomedicaoMetodos.ListarTodasMedicoesIDUsuario(usuario.IDUsuario);

                // verifica se tem medicao
                if (usuariosmedicoes == null)
                {
                    // Log - [5] Medicoes inexistentes
                    API_Log(usuario.API_Key, "MedicoesLista", usuario.IDUsuario, 0, Data, Data, 5);

                    // erro
                    API_Erro(5, "Sem Medições", Formato);
                    return;
                }

                // copia medições habilitadas
                foreach (UsuarioMedicaoDominio usuarioMed in usuariosmedicoes)
                {
                    ConfigMedList.Add(usuarioMed.IDMedicao);
                }
            }

            // percorre clientes
            foreach (ClientesDominio cliente in clientes)
            {
                // medições dos clientes do gestor
                MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                List<MedicoesDominio> medicoes = medicaoMetodos.ListarTodos(cliente.IDCliente, (ConfigMedList.Count == 0 ? null : ConfigMedList), " ORDER BY nome");

                if (medicoes == null)
                {
                    // ignora
                    continue;
                }

                // percorre medicoes
                foreach (MedicoesDominio medicao in medicoes)
                {
                    // medicao API
                    MedicoesListaAPI medicaoAPI = new MedicoesListaAPI();

                    medicaoAPI.IDCliente = cliente.IDCliente;
                    medicaoAPI.NomeCliente = cliente.Nome;
                    medicaoAPI.IDMedicao = medicao.IDMedicao;
                    medicaoAPI.NomeMedicao = medicao.Nome;
                    medicaoAPI.IDTipoMedicao = medicao.IDTipoMedicao;
                    medicaoAPI.Referencia = medicao.Referencia;

                    // coloca na lista
                    medicoesAPI.Add(medicaoAPI);
                }
            }

            // verifica se tem medições
            if (medicoesAPI.Count == 0)
            {
                // Log - [5] Medicoes inexistentes
                API_Log(usuario.API_Key, "MedicoesLista", usuario.IDUsuario, 0, Data, Data, 5);

                // erro
                API_Erro(5, "Sem Medições", Formato);
                return;
            }
            else
            {
                // lista de medições
                MedicoesLista_Lista(medicoesAPI, Formato);
            }

            return;
        }

        public void MedicoesLista_Lista(List<MedicoesListaAPI> medicoesAPI, string Formato = "XML")
        {

            // XML ou JSON
            StringBuilder xmlBuilder = new StringBuilder();
            IFormatProvider iFormatProvider = new System.Globalization.CultureInfo("en-US");

            // verifica se JSON
            if (Formato.ToUpper() == "JSON")
            {
                // inicia
                xmlBuilder.Append("{");

                // cabecalho
                xmlBuilder.Append("\"Cabecalho\": ");
                xmlBuilder.Append("[\"IDCliente\",\"NomeCliente\",\"IDMedicao\",\"NomeMedicao\",\"TipoMedicao\",\"N_Ativo\"],");

                // medições
                xmlBuilder.Append("\"Medicoes\": ");
                xmlBuilder.Append("[");

                // percorre medições
                int conta = 0;

                foreach (MedicoesListaAPI medicaoAPI in medicoesAPI)
                {
                    xmlBuilder.Append("[");
                    xmlBuilder.Append(string.Format("\"{0:000000}\",", medicaoAPI.IDCliente));
                    xmlBuilder.Append(string.Format("\"{0}\",", medicaoAPI.NomeCliente));
                    xmlBuilder.Append(string.Format("\"{0:000000}\",", medicaoAPI.IDMedicao));
                    xmlBuilder.Append(string.Format("\"{0}\",", medicaoAPI.NomeMedicao));

                    switch (medicaoAPI.IDTipoMedicao)
                    {
                        default:
                            xmlBuilder.Append(string.Format("\"{0}\",", medicaoAPI.IDTipoMedicao));
                            break;

                        case TIPO_MEDICAO.ENERGIA:
                            xmlBuilder.Append("\"Energia\",");
                            break;

                        case TIPO_MEDICAO.ENERGIA_FORMULA:
                            xmlBuilder.Append("\"Fórmula Energia\",");
                            break;

                        case TIPO_MEDICAO.UTILIDADES:
                            xmlBuilder.Append("\"Utilidades\",");
                            break;

                        case TIPO_MEDICAO.UTILIDADES_FORMULA:
                            xmlBuilder.Append("\"Fórmula Utilidades\",");
                            break;

                        case TIPO_MEDICAO.ENTRADA_ANALOGICA:
                            xmlBuilder.Append("\"Entrada Analógica\",");
                            break;

                        case TIPO_MEDICAO.EA_FORMULA:
                            xmlBuilder.Append("\"Fórmula Entrada Analógica\",");
                            break;

                        case TIPO_MEDICAO.CICLOMETRO:
                            xmlBuilder.Append("\"Ciclômetro\",");
                            break;

                        case TIPO_MEDICAO.METEOROLOGIA:
                            xmlBuilder.Append("\"Meteorologia\",");
                            break;
                    }

                    xmlBuilder.Append(string.Format("\"{0}\"", medicaoAPI.Referencia));

                    // verifica se ultimo
                    if (conta < (medicoesAPI.Count - 1))
                    {
                        // coloca virgula
                        xmlBuilder.Append("],");
                    }
                    else
                    {
                        xmlBuilder.Append("]");
                    }

                    // proximo
                    conta++;
                }

                // finaliza
                xmlBuilder.Append("]");
                xmlBuilder.Append("}");

                // ContentType
                Response.ContentType = "application/json";
            }
            else
            {
                // monta XML
                xmlBuilder.Append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
                xmlBuilder.Append("<Medicoes>");

                // percorre medições
                foreach (MedicoesListaAPI medicaoAPI in medicoesAPI)
                {
                    xmlBuilder.Append(string.Format("<Medicao IDMedicao=\"{0:000000}\">", medicaoAPI.IDMedicao));

                    xmlBuilder.Append("<IDCliente>");
                    xmlBuilder.Append(string.Format("{0:000000}", medicaoAPI.IDCliente));
                    xmlBuilder.Append("</IDCliente>");

                    xmlBuilder.Append("<NomeCliente>");
                    xmlBuilder.Append(medicaoAPI.NomeCliente);
                    xmlBuilder.Append("</NomeCliente>");

                    xmlBuilder.Append("<IDMedicao>");
                    xmlBuilder.Append(string.Format("{0:000000}", medicaoAPI.IDMedicao));
                    xmlBuilder.Append("</IDMedicao>");

                    xmlBuilder.Append("<NomeMedicao>");
                    xmlBuilder.Append(medicaoAPI.NomeMedicao);
                    xmlBuilder.Append("</NomeMedicao>");

                    xmlBuilder.Append("<TipoMedicao>");
                    switch (medicaoAPI.IDTipoMedicao)
                    {
                        default:
                            xmlBuilder.Append(string.Format("{0}", medicaoAPI.IDTipoMedicao));
                            break;

                        case TIPO_MEDICAO.ENERGIA:
                            xmlBuilder.Append("Energia");
                            break;

                        case TIPO_MEDICAO.ENERGIA_FORMULA:
                            xmlBuilder.Append("Fórmula Energia");
                            break;

                        case TIPO_MEDICAO.UTILIDADES:
                            xmlBuilder.Append("Utilidades");
                            break;

                        case TIPO_MEDICAO.UTILIDADES_FORMULA:
                            xmlBuilder.Append("Fórmula Utilidades");
                            break;

                        case TIPO_MEDICAO.ENTRADA_ANALOGICA:
                            xmlBuilder.Append("Entrada Analógica");
                            break;

                        case TIPO_MEDICAO.EA_FORMULA:
                            xmlBuilder.Append("Fórmula Entrada Analógica");
                            break;

                        case TIPO_MEDICAO.CICLOMETRO:
                            xmlBuilder.Append("Ciclômetro");
                            break;

                        case TIPO_MEDICAO.METEOROLOGIA:
                            xmlBuilder.Append("Meteorologia");
                            break;
                    }
                    xmlBuilder.Append("</TipoMedicao>");

                    xmlBuilder.Append("<N_Ativo>");
                    xmlBuilder.Append(medicaoAPI.Referencia);
                    xmlBuilder.Append("</N_Ativo>");

                    xmlBuilder.Append("</Medicao>");
                }

                xmlBuilder.Append("</Medicoes>");

                // ContentType
                Response.ContentType = "text/xml";
            }

            Response.BinaryWrite(Encoding.UTF8.GetBytes(xmlBuilder.ToString()));
            Response.End();

            return;
        }
    }
}
