﻿using System;
using System.Text;
using System.Web.Mvc;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class APIController : Controller
    {

        //
        // GET: /API
        [AllowAnonymous]
        [OutputCache(NoStore = true, Duration = 0, VaryByParam = "None")]
        public ActionResult API()
        {
            ViewBag.PaginaAjuda = "Contato";

            // tema
            ListaTiposMetodos temaMetodos = new ListaTiposMetodos();
            TemaDominio tema = temaMetodos.ListarPorId_Tema(0);

            CookieStore.SalvaCookie_Int("Tema", tema.IDTema);
            CookieStore.SalvaCookie_String("background", tema.background);
            CookieStore.SalvaCookie_String("background_hover", tema.background_hover);
            CookieStore.SalvaCookie_String("background_nav_header", tema.background_nav_header);
            CookieStore.SalvaCookie_String("background_panel_title", tema.background_panel_title);

            return View();
        }

        //
        // GET: /API_Zordon
        [AllowAnonymous]
        [OutputCache(NoStore = true, Duration = 0, VaryByParam = "None")]
        public ActionResult API_Zordon()
        {
            ViewBag.PaginaAjuda = "Contato";

            // tema
            ListaTiposMetodos temaMetodos = new ListaTiposMetodos();
            TemaDominio tema = temaMetodos.ListarPorId_Tema(0);

            CookieStore.SalvaCookie_Int("Tema", tema.IDTema);
            CookieStore.SalvaCookie_String("background", tema.background);
            CookieStore.SalvaCookie_String("background_hover", tema.background_hover);
            CookieStore.SalvaCookie_String("background_nav_header", tema.background_nav_header);
            CookieStore.SalvaCookie_String("background_panel_title", tema.background_panel_title);

            return View();
        }

        // GET: Ajuda
        public PartialViewResult _Ajuda(string PaginaAjuda)
        {
            ViewBag.PaginaAjuda = PaginaAjuda;

            return PartialView();
        }


        // API write
        void API_write(StringBuilder xmlBuilder, string escrita)
        {
            xmlBuilder.Append(escrita + "\r\n");
            return;
        }

        // API erro
        void API_Erro(int code_erro, string str_erro, string arquivo = "JSON")
        {
            StringBuilder xmlBuilder = new StringBuilder();

            // verifica se JSON
            if (arquivo.ToUpper() == "JSON")
            {
                // inicia
                xmlBuilder.Append("{\r\n");

                // erro
                xmlBuilder.Append("  \"Erro\": [\r\n");
                xmlBuilder.Append(string.Format("    \"Codigo\": {0},\r\n", code_erro));
                xmlBuilder.Append(string.Format("    \"Descricao\": \"{0}\"\r\n", str_erro));
                xmlBuilder.Append("  ]\r\n");

                // finaliza
                xmlBuilder.Append("}\r\n");

                // ContentType
                Response.ContentType = "application/json";
            }
            else
            {
                // monta XML ERRO
                xmlBuilder.Append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");

                xmlBuilder.Append(string.Format("<Erro erroID=\"{0}\">", code_erro));

                xmlBuilder.Append("<Codigo>");
                xmlBuilder.Append(code_erro);
                xmlBuilder.Append("</Codigo>");

                xmlBuilder.Append("<Descricao>");
                xmlBuilder.Append(str_erro);
                xmlBuilder.Append("</Descricao>");

                xmlBuilder.Append("</Erro>");

                // ContentType
                Response.ContentType = "text/xml";
            }

            // escreve
            Response.BinaryWrite(Encoding.UTF8.GetBytes(xmlBuilder.ToString()));
            Response.End();

            return;
        }

        private void API_Log(string API_Key, string Funcao_API, int IDUsuario, int IDMedicao, DateTime DataIni, DateTime DataFim, int Status)
        {
            //
            // Log do SolicitaBD
            //
            API_LogMetodos logMetodos = new API_LogMetodos();
            API_LogDominio log = new API_LogDominio();
            log.DataHora = DateTime.Now;
            log.API_Key = API_Key;
            log.Funcao_API = Funcao_API;
            log.IDUsuario = IDUsuario;
            log.IDMedicao = IDMedicao;
            log.DataIni = DataIni;
            log.DataFim = DataFim;
            log.Status = Status;
            logMetodos.Salvar(log);

            return;
        }
    }
}