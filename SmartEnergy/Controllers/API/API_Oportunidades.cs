﻿using System;
using System.Collections.Generic;
using System.Text;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class APIController
    {
        // Oportunidades
        // https://www.smartenergy.com.br/API/Oportunidades?Key=1234567890&id=1
        // http://localhost:59328/API/Oportunidades?Key=1234567890&id=1

        // Key = API key do usuário
        // id = id do usuário
        // _q = Busca o Usuário pelos campos nome ou email
        // _sort = Define o campo de ordenação da lista
        // _order = Define a ordem da lista com base no campo definido
        // _limit = Quantidade de itens por página
        // _page = Página a ser carregada

        // Status
        // 0  - OK
        // 1  - Erro: Senha ou Datas nulas
        // 2  - Erro: Chave incorreta
        // 3  - Erro: Usuário inexistente
        // 4  - Erro: Medição inexistente
        // 5  - Erro: Usuário não pertence ao cliente da medição
        // 6  - Erro: Cliente inexistente
        // 7  - Erro: Usuário não tem acesso ao cliente
        // 8  - Erro: ---
        // 9  - Erro: ---
        // 10 - Erro: Sem registro
        // 11 - Erro: ---
        // 12 - Erro: ---
        // 14 - Erro: ---
        // 20 - Erro: Ultrapassou limite diário de requisições
        // 21 - Erro: Usuário bloqueado
        // 22 - Erro: Gateway inexistente

        public void Oportunidades(string Key, int id = 0, string _q = "", string _sort = "id", string _order = "asc", int _limit = 10, int _page = 1)
        {
            // Inicializa variáveis
            DateTime Data = new DateTime(2000, 1, 1, 0, 0, 0);

            //
            // Parâmetros
            //
            if (Key == null)
            {
                // Log - [1] key nulo
                API_Log(Key, "Oportunidades", 0, id, Data, Data, 1);

                // erro
                API_Erro(1, "O campo Key selecionado é inválido");
                return;
            }

            if (_sort.ToLower() != "id" && _sort.ToLower() != "name" && _sort.ToLower() != "email")
            {
                // Log - [1] _sort
                API_Log(Key, "Oportunidades", 0, id, Data, Data, 1);

                // erro
                API_Erro(1, "O campo _sort deve ser 'id', 'name' ou 'email'");
                return;
            }

            if (_order.ToLower() != "asc" && _order.ToLower() != "desc")
            {
                // Log - [1] _order
                API_Log(Key, "Oportunidades", 0, id, Data, Data, 1);

                // erro
                API_Erro(1, "O campo _order deve ser 'asc' ou 'desc'");
                return;
            }

            if (_limit < 0)
            {
                // Log - [1] _limit
                API_Log(Key, "Oportunidades", 0, id, Data, Data, 1);

                // erro
                API_Erro(1, "O campo _limit deve ser maior que zero");
                return;
            }

            if (_page < 0)
            {
                // Log - [1] _page
                API_Log(Key, "Oportunidades", 0, id, Data, Data, 1);

                // erro
                API_Erro(1, "O campo _page deve ser maior que zero");
                return;
            }

            //
            // USUARIO
            //

            // le usuario
            UsuarioMetodos usuarioMetodos = new UsuarioMetodos();
            UsuarioDominio usuarioKey = usuarioMetodos.ListarPorAPI_Key(Key);

            if (usuarioKey != null)
            {
                // verifica se key eh correta
                if (usuarioKey.API_Key != Key)
                {
                    // Log - [2] chave incorreta
                    API_Log(Key, "Oportunidades", 0, 0, Data, Data, 2);

                    // erro
                    API_Erro(2, string.Format("O campo Key está incorreto ({0})", Key));
                    return;
                }
            }
            else
            {
                // Log - [3] usuário inexistente
                API_Log(Key, "Oportunidades", 0, id, Data, Data, 3);

                // erro
                API_Erro(3, string.Format("Usuário da Key inexistente ({0})", Key));
                return;
            }

            // verifica se usuário bloqueado
            if (usuarioKey.Bloqueio == TIPO_BLOQUEIO.TempoAPI || usuarioKey.Bloqueio == TIPO_BLOQUEIO.Generico)
            {
                // Log - [21] usuário bloqueado
                API_Log(Key, "Oportunidades", 0, id, Data, Data, 21);

                // erro
                API_Erro(21, string.Format("Usuário da Key está Bloqueado ({0})", usuarioKey.IDUsuario));
                return;
            }

            // monta lista
            string strWhere = "";

            // verifica se busca por id
            if (id > 0 && _sort.ToLower() == "id")
            {
                strWhere = string.Format("IDUsuario = {0}", id);
            }

            // verifica se busca por nome
            if (_sort.ToLower() == "name")
            {
                // busca
                strWhere = string.Format("(NomeUsuario collate Latin1_General_CI_AI LIKE '%{0}%') ", _q);
            }

            // verifica se busca por email
            if (_sort.ToLower() == "email")
            {
                // busca
                strWhere = string.Format("(Email collate Latin1_General_CI_AI LIKE '%{0}%') ", _q);
            }

            // busca
            List<UsuarioDominio> usuarios = usuarioMetodos.BuscarWhere(strWhere);

            if (usuarios == null)
            {
                // Log - [3] usuário inexistente
                API_Log(Key, "Oportunidades", 0, id, Data, Data, 3);

                // erro
                API_Erro(3, "Usuário inexistente");
                return;
            }

            if (usuarios.Count == 0)
            {
                // Log - [3] usuário inexistente
                API_Log(Key, "Oportunidades", 0, id, Data, Data, 3);

                // erro
                API_Erro(3, "Usuário inexistente");
                return;
            }

            // pega primeiro usuário
            UsuarioDominio usuario = usuarios[0];

            if (usuario == null)
            {
                // Log - [3] usuário inexistente
                API_Log(Key, "Oportunidades", 0, id, Data, Data, 3);

                // erro
                API_Erro(3, "Usuário inexistente");
                return;
            }

            //
            // REGISTROS
            //

            //
            // teste
            //
            List<OportunidadeDominio> oportunidades = new List<OportunidadeDominio>();

            // oportunidade 1
            OportunidadeDominio oportunidade = new OportunidadeDominio
            {
                IDOportunidade = 52,

                IDUsuario = usuario.IDUsuario,
                NomeUsuario = usuario.NomeUsuario,
                Email = usuario.Email,
                Celular = usuario.Celular,

                IDTipoOportunidade = 1,
                NomeOportunidade = "Redução de Contratos",

                IDCliente = 1,
                NomeCliente = "Smart Energy",

                IDMedicao = 3,
                NomeMedicao = "Medição Setor 2",

                DataInicio = new DateTime(2024, 12, 1),
                DataFim = new DateTime(2024, 12, 31),

                Dados = new Dados_Oportunidade
                {
                    EstruturaTarifaria = "Cativo THS Azul",
                    Distribuidora = "ENEL SP",
                    DemandaContratoP = 300.0,
                    DemandaRegistradaP = 267.0,
                    DiferencaP = -33.0,
                    FatorUtilizacaoP = 89.0,
                    DemandaContratoFP = 550.0,
                    DemandaRegistradaFP = 490.0,
                    DiferencaFP = -60.0,
                    FatorUtilizacaoFP = 89.1,
                    OportunidadeP = 951.72,
                    OportunidadeFP = 1023.0
                }
            };

            oportunidades.Add(oportunidade);

            // oportunidade 2
            oportunidade = new OportunidadeDominio
            {
                IDOportunidade = 87,

                IDUsuario = usuario.IDUsuario,
                NomeUsuario = usuario.NomeUsuario,
                Email = usuario.Email,
                Celular = usuario.Celular,

                IDTipoOportunidade = 2,
                NomeOportunidade = "Multas",

                IDCliente = 1,
                NomeCliente = "Smart Energy",

                IDMedicao = 1,
                NomeMedicao = "Medição São Paulo",

                DataInicio = new DateTime(2024, 12, 1),
                DataFim = new DateTime(2024, 12, 31),

                Dados = new Dados_Oportunidade
                {
                    EstruturaTarifaria = "Cativo THS Azul",
                    Distribuidora = "ENEL SP",
                    DemandaContratoP = 750.0,
                    DemandaRegistradaP = 750.0,
                    DiferencaP = 0.0,
                    FatorUtilizacaoP = 100.0,
                    DemandaContratoFP = 1300.0,
                    DemandaRegistradaFP = 1376.0,
                    DiferencaFP = 76.0,
                    FatorUtilizacaoFP = 105.8,
                    MultaP = 0.0,
                    MultaFP = 2591.6
                }
            };

            oportunidades.Add(oportunidade);


            // anomalia 1001
            oportunidade = new OportunidadeDominio
            {
                IDOportunidade = 123,

                IDUsuario = usuario.IDUsuario,
                NomeUsuario = usuario.NomeUsuario,
                Email = usuario.Email,
                Celular = usuario.Celular,

                IDTipoOportunidade = 1001,
                NomeOportunidade = "Ultrapassagem de Demanda",

                IDCliente = 1,
                NomeCliente = "Smart Energy",

                IDMedicao = 1,
                NomeMedicao = "Medição São Paulo",

                DataInicio = new DateTime(2024, 12, 1),
                DataFim = new DateTime(2024, 12, 31),

                Dados = new Dados_Oportunidade
                {
                    DemandaContratoP = 750.0,
                    DemandaMaximaP = 750.0,
                    DiferencaP = 0.0,
                    DataDemandaMaximaP = new DateTime(2024,12,13,17,15,0),

                    DemandaContratoFP = 1300.0,
                    DemandaMaximaFP = 1376.0,
                    DiferencaFP = 76.0,
                    DataDemandaMaximaFP = new DateTime(2024, 12, 15, 10, 45, 0),
                }
            };

            oportunidades.Add(oportunidade);


            // anomalia 1002
            oportunidade = new OportunidadeDominio
            {
                IDOportunidade = 131,

                IDUsuario = usuario.IDUsuario,
                NomeUsuario = usuario.NomeUsuario,
                Email = usuario.Email,
                Celular = usuario.Celular,

                IDTipoOportunidade = 1002,
                NomeOportunidade = "Falta de Energia",

                IDCliente = 1,
                NomeCliente = "Smart Energy",

                IDMedicao = 1,
                NomeMedicao = "Medição São Paulo",

                DataInicio = new DateTime(2024, 12, 1),
                DataFim = new DateTime(2024, 12, 31),

                Dados = new Dados_Oportunidade
                {
                    DataFaltaEnergia = new DateTime(2024, 12, 21, 16, 33, 24),
                }
            };

            oportunidades.Add(oportunidade);


            // JSON
            bool retorno = Oportunidades_Item(oportunidades, _limit, _page);

            // verifica se erro nos registros
            if (!retorno)
            {
                // Log do Dispositivos - [10] Sem registro
                API_Log(Key, "Oportunidades", usuario.IDUsuario, id, Data, Data, 10);

                // erro
                API_Erro(10, "Sem Registros");
                return;
            }

            // Oportunidades - OK
            API_Log(Key, "Oportunidades", usuario.IDUsuario, id, Data, Data, 0);
            return;
        }


        // Oportunidade Redução Contrato e Multas
        public class OportunidadeDominio
        {
            public int IDOportunidade { get; set; }

            public int IDUsuario { get; set; }
            public string NomeUsuario { get; set; }
            public string Email { get; set; }
            public string Celular { get; set; }

            public int IDTipoOportunidade { get; set; }
            public string NomeOportunidade { get; set; }

            public int IDCliente { get; set; }
            public string NomeCliente { get; set; }

            public int IDMedicao { get; set; }
            public string NomeMedicao { get; set; }

            public DateTime DataInicio { get; set; }
            public DateTime DataFim { get; set; }

            public Dados_Oportunidade Dados { get; set; }
        }

        public class Dados_Oportunidade
        {
            // Oportunidade 1: Redução de Contratos
            // Oportunidade 2: Multas 
            public string EstruturaTarifaria { get; set; }
            public string Distribuidora { get; set; }
            public double? DemandaContratoP { get; set; }
            public double? DemandaRegistradaP { get; set; }
            public double? DiferencaP { get; set; }
            public double? FatorUtilizacaoP { get; set; }
            public double? DemandaContratoFP { get; set; }
            public double? DemandaRegistradaFP { get; set; }
            public double? DiferencaFP { get; set; }
            public double? FatorUtilizacaoFP { get; set; }
            public double? OportunidadeP { get; set; }      // Campo para IDOportunidade = 1
            public double? OportunidadeFP { get; set; }     // Campo para IDOportunidade = 1
            public double? MultaP { get; set; }             // Campo para IDOportunidade = 2
            public double? MultaFP { get; set; }            // Campo para IDOportunidade = 2

            // Anomalia 1001: Ultrapassagem de Demanda
            public double? DemandaMaximaP { get; set; }
            public DateTime? DataDemandaMaximaP { get; set; }

            public double? DemandaMaximaFP { get; set; }
            public DateTime? DataDemandaMaximaFP { get; set; }

            // Anomalia 1002: Falta de Energia
            public DateTime? DataFaltaEnergia { get; set; }
        }

        // itens
        public bool Oportunidades_Item(List<OportunidadeDominio> oportunidades, int _limit, int _page)
        {

            // JSON
            StringBuilder jsonBuilder = new StringBuilder();
            IFormatProvider iFormatProvider = new System.Globalization.CultureInfo("en-US");

            // inicio e fim
            int conta_item = 0;
            int conta_item_publicado = 0;

            int inicio = (_page - 1) * _limit;
            int fim = _page * _limit;

            if (fim > oportunidades.Count)
            {
                fim = oportunidades.Count;
            }

            fim = fim - 1;


            // percorre oportunidades
            foreach (OportunidadeDominio oportunidade in oportunidades)
            {
                if (conta_item >= inicio && conta_item <= fim)
                {
                    if (conta_item == inicio)
                    {
                        // inicia
                        API_write(jsonBuilder, "{");
                        API_write(jsonBuilder, "  \"Oportunidades\": [");
                    }

                    // item
                    API_write(jsonBuilder, "    {");
                    API_write(jsonBuilder, string.Format("      \"IDOportunidade\": {0},", oportunidade.IDOportunidade));

                    API_write(jsonBuilder, string.Format("      \"IDUsuario\": {0},", oportunidade.IDUsuario));
                    API_write(jsonBuilder, string.Format("      \"NomeUsuario\": \"{0}\",", oportunidade.NomeUsuario));
                    API_write(jsonBuilder, string.Format("      \"Email\": \"{0}\",", oportunidade.Email));
                    API_write(jsonBuilder, string.Format("      \"Celular\": \"{0}\",", oportunidade.Celular));

                    API_write(jsonBuilder, string.Format("      \"IDTipoOportunidade\": {0},", oportunidade.IDTipoOportunidade));
                    API_write(jsonBuilder, string.Format("      \"NomeOportunidade\": \"{0}\",", oportunidade.NomeOportunidade));
                    API_write(jsonBuilder, string.Format("      \"IDCliente\": {0},", oportunidade.IDCliente));
                    API_write(jsonBuilder, string.Format("      \"NomeCliente\": \"{0}\",", oportunidade.NomeCliente));
                    API_write(jsonBuilder, string.Format("      \"IDMedicao\": {0},", oportunidade.IDMedicao));
                    API_write(jsonBuilder, string.Format("      \"NomeMedicao\": \"{0}\",", oportunidade.NomeMedicao));
                    API_write(jsonBuilder, string.Format("      \"DataInicio\": \"{0:yyyy-MM-dd}\",", oportunidade.DataInicio));
                    API_write(jsonBuilder, string.Format("      \"DataFim\": \"{0:yyyy-MM-dd}\",", oportunidade.DataFim));

                    API_write(jsonBuilder, "      \"Dados\": {");

                    //
                    // Oportunidade 1: Redução de Contratos
                    //
                    if (oportunidade.IDTipoOportunidade == 1)
                    {
                        API_write(jsonBuilder, string.Format("        \"EstruturaTarifaria\": \"{0}\",", oportunidade.Dados.EstruturaTarifaria));
                        API_write(jsonBuilder, string.Format("        \"Distribuidora\": \"{0}\",", oportunidade.Dados.Distribuidora));

                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"DemandaContratoP\": {0},", oportunidade.Dados.DemandaContratoP));
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"DemandaRegistradaP\": {0},", oportunidade.Dados.DemandaRegistradaP));
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"DiferencaP\": {0},", oportunidade.Dados.DiferencaP));
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"FatorUtilizacaoP\": {0:0.0},", oportunidade.Dados.FatorUtilizacaoP));

                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"DemandaContratoFP\": {0},", oportunidade.Dados.DemandaContratoFP));
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"DemandaRegistradaFP\": {0},", oportunidade.Dados.DemandaRegistradaFP));
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"DiferencaFP\": {0},", oportunidade.Dados.DiferencaFP));
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"FatorUtilizacaoFP\": {0:0.0},", oportunidade.Dados.FatorUtilizacaoFP));

                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"OportunidadeP\": {0:0.00}", oportunidade.Dados.OportunidadeP));
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"OportunidadeFP\": {0:0.00}", oportunidade.Dados.OportunidadeFP));
                    }


                    //
                    // Oportunidade 2: Multas
                    //
                    if (oportunidade.IDTipoOportunidade == 2)
                    {
                        API_write(jsonBuilder, string.Format("        \"EstruturaTarifaria\": \"{0}\",", oportunidade.Dados.EstruturaTarifaria));
                        API_write(jsonBuilder, string.Format("        \"Distribuidora\": \"{0}\",", oportunidade.Dados.Distribuidora));

                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"DemandaContratoP\": {0},", oportunidade.Dados.DemandaContratoP));
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"DemandaRegistradaP\": {0},", oportunidade.Dados.DemandaRegistradaP));
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"DiferencaP\": {0},", oportunidade.Dados.DiferencaP));
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"FatorUtilizacaoP\": {0:0.0},", oportunidade.Dados.FatorUtilizacaoP));

                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"DemandaContratoFP\": {0},", oportunidade.Dados.DemandaContratoFP));
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"DemandaRegistradaFP\": {0},", oportunidade.Dados.DemandaRegistradaFP));
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"DiferencaFP\": {0},", oportunidade.Dados.DiferencaFP));
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"FatorUtilizacaoFP\": {0:0.0},", oportunidade.Dados.FatorUtilizacaoFP));

                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"MultaP\": {0:0.00}", oportunidade.Dados.MultaP));
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"MultaFP\": {0:0.00}", oportunidade.Dados.MultaFP));
                    }


                    //
                    // Anomalia 1001: Ultrapassagem de Demanda
                    //
                    if (oportunidade.IDTipoOportunidade == 1001)
                    {
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"DemandaContratoP\": {0},", oportunidade.Dados.DemandaContratoP));
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"DemandaMaximaP\": {0},", oportunidade.Dados.DemandaMaximaP));
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"DiferencaP\": {0},", oportunidade.Dados.DiferencaP));
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"DataDemandaMaximaP\": \"{0:yyyy-MM-dd HH:mm}\",", oportunidade.Dados.DataDemandaMaximaP));

                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"DemandaContratoFP\": {0},", oportunidade.Dados.DemandaContratoFP));
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"DemandaMaximaFP\": {0},", oportunidade.Dados.DemandaMaximaFP));
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"DiferencaFP\": {0},", oportunidade.Dados.DiferencaFP));
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"DataDemandaMaximaFP\": \"{0:yyyy-MM-dd HH:mm}\"", oportunidade.Dados.DataDemandaMaximaFP));
                    }

                    //
                    // Anomalia 1002: Falta de Energia
                    //
                    if (oportunidade.IDTipoOportunidade == 1002)
                    {
                        API_write(jsonBuilder, string.Format(iFormatProvider, "        \"DataFaltaEnergia\": \"{0:yyyy-MM-dd HH:mm:ss}\"", oportunidade.Dados.DataFaltaEnergia));
                    }


                    // fim
                    API_write(jsonBuilder, "      }");

                    // verifica se último item
                    if (conta_item == fim)
                    {
                        API_write(jsonBuilder, "    }");
                    }
                    else
                    {
                        API_write(jsonBuilder, "    },");
                    }

                    // incrementa contador
                    conta_item_publicado++;
                }

                // incrementa contador
                conta_item++;
            }

            // verifica se tem algum item
            if (conta_item_publicado > 0)
            {
                // finaliza
                API_write(jsonBuilder, "  ],");
                API_write(jsonBuilder, string.Format("  \"total\": {0},", oportunidades.Count));
                API_write(jsonBuilder, string.Format("  \"per_page\": {0},", _limit));
                API_write(jsonBuilder, string.Format("  \"current_page\": {0}", _page));
                API_write(jsonBuilder, "}");
            }
            else
            {
                // sem itens
                return (false);
            }

            // ContentType
            Response.ContentType = "application/json";

            Response.BinaryWrite(Encoding.UTF8.GetBytes(jsonBuilder.ToString()));
            Response.End();

            return (true);
        }
    }
}