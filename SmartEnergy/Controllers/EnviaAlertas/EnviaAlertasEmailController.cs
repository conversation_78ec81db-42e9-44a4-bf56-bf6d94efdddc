﻿using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Mime;
using System.Web.Hosting;
using System.Web.Mvc;

using SmartEnergy.Models;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public class EnviaAlertasEmailController : Controller
    {
        // Estatísticas
        int Num_Emails_Enviados = 0;

        // data hora atual
        DateTime DataHoraAtual = DateTime.Now;


        // GET: EnviaAlertasEmail
        public ActionResult EnviaAlertasEmail()
        {
            // log
            LogMessage("EnviaAlertasEmail 1.00");

            // data hora atual
            DataHoraAtual = DateTime.Now;

            // leio usuários
            UsuarioMetodos usuariosMetodos = new UsuarioMetodos();
            List<UsuarioDominio> usuarios = usuariosMetodos.ListarAlertasAgrupados();

            if (usuarios != null)
            {
                // percorre usuarios
                foreach (UsuarioDominio usuario in usuarios)
                {
                    // usuário programado para receber alertas agrupado
                    LogMessage(string.Format("Usuário [{0}] {1}", usuario.IDUsuario, usuario.NomeUsuario));

                    // leio alertas do usuario
                    UsuarioAlertaEmailMetodos alertasMetodos = new UsuarioAlertaEmailMetodos();
                    List<UsuarioAlertaEmailDominio> alertas = alertasMetodos.ListarIDUsuario(usuario.IDUsuario);

                    if (alertas != null)
                    {
                        // número de pendentes
                        LogMessage(string.Format(">>> Número de Alertas Pendentes [{0}]", alertas.Count));

                        // verifica se tem usuário para enviar
                        if (alertas.Count > 0)
                        {
                            // cliente
                            ClientesMetodos clienteMetodos = new ClientesMetodos();
                            ClientesDominio cliente = new ClientesDominio();
                            cliente.IDCliente = 0;

                            // percorre alertas
                            foreach (UsuarioAlertaEmailDominio alerta in alertas)
                            {
                                // verifica se mudou cliente
                                if (cliente.IDCliente != alerta.IDCliente)
                                {
                                    // leio cliente
                                    ClientesDominio aux_cliente = clienteMetodos.ListarPorId(alerta.IDCliente);

                                    if (aux_cliente != null)
                                    {
                                        // copia
                                        cliente = aux_cliente;
                                    }
                                    else
                                    {
                                        // cliente não existe, ignora alerta
                                        LogMessage(string.Format(">>> ERRO: Cliente inexistente [{0}]", alerta.IDCliente));
                                        goto APAGA;
                                    }
                                }

                                // nome do cliente
                                string nome_cliente = cliente.Fantasia;

                                // nome da gateway
                                string nome_gateway = "---";

                                // nome do grupo
                                string nome_grupo = "---";

                                // nome da unidade
                                string nome_unidade = "---";

                                // nome da medição
                                string nome_medicao = "---";

                                // verifica se alerta de medição
                                if (alerta.IDMedicao > 0)
                                {
                                    MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                                    MedicoesDominio medicao = medicaoMetodos.ListarPorId(alerta.IDMedicao);
                                    MedicaoInfoDominio medicao_info = medicaoMetodos.ListarMedicaoInfoPorIDMedicao(alerta.IDMedicao);

                                    // verifica se existe medição
                                    if (medicao == null || medicao_info == null)
                                    {
                                        // ignora alerta
                                        LogMessage(string.Format(">>> ERRO: Medição inexistente [{0}]", alerta.IDMedicao));
                                        goto APAGA;
                                    }

                                    GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
                                    GatewaysDominio gateway = gatewayMetodos.ListarPorId(medicao.IDGateway);

                                    // verifica se existe gateway
                                    if (gateway == null)
                                    {
                                        // ignora notificação
                                        LogMessage(string.Format(">>> ERRO: Gateway inexistente pela Medição existente [{0}]", medicao.IDGateway));
                                        goto APAGA;
                                    }

                                    // nome da gateway
                                    nome_gateway = gateway.Nome;

                                    // nome do grupo
                                    nome_grupo = medicao_info.nomeGrupo;

                                    // nome da unidade
                                    nome_unidade = medicao_info.nomeUnidade;

                                    // nome da medição
                                    nome_medicao = medicao.Nome;
                                }

                                // verifica se notificação de gateway
                                if (alerta.IDMedicao == 0 && alerta.IDGateway > 0)
                                {
                                    GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
                                    GatewaysDominio gateway = gatewayMetodos.ListarPorId(alerta.IDGateway);

                                    // verifica se existe gateway
                                    if (gateway == null)
                                    {
                                        // ignora notificação
                                        LogMessage(string.Format(">>> ERRO: Gateway inexistente [{0}]", alerta.IDGateway));
                                        goto APAGA;
                                    }

                                    // nome da gateway
                                    nome_gateway = gateway.Nome;
                                }

                                // coloca na estrutura
                                alerta.NomeCliente = nome_cliente;
                                alerta.NomeGrupo = nome_grupo;
                                alerta.NomeUnidade = nome_unidade;
                                alerta.NomeMedicao = nome_medicao;
                                alerta.NomeGateway = nome_gateway;

                            APAGA:

                                // exclui notificação
                                alertasMetodos.Excluir(alerta.IDUsuarioAlertaEmail);

                            }

                            // enviar alertas pendentes
                            AlertasPendentes_PDF(alertas, usuario, cliente.IDConsultor);
                        }
                    }
                }

                // envia email estatistica
                DateTime datahora_agora = System.DateTime.Now;
                TimeSpan diff = datahora_agora.Subtract(DataHoraAtual);

                string mensagem = string.Format("Número de Emails de Alertas enviados = {0} em {1:00}'{2:00}\"{3:000}", Num_Emails_Enviados, diff.Minutes, diff.Seconds, diff.Milliseconds);
                string mensagem_erro = "Todos emails gerados com sucesso.";
                string relatorios_erro = "";

                EnviaEmailEstatistica(mensagem, mensagem_erro, relatorios_erro);

                // log
                LogMessage(mensagem);
            }

            // log
            LogMessage("------");

            // retorna status
            var returnedData = new
            {
                status = true
            };

            // relatorio
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        // Alertas Pendentes PDF
        private bool AlertasPendentes_PDF(List<UsuarioAlertaEmailDominio> alertas, UsuarioDominio usuario, int IDConsultor)
        {
            try
            {

                //
                // PREPARA RELATÓRIO
                //

                // log
                LogMessage(string.Format(">>> Enviando Alertas para Usuário [{0:000000}] {1} || {2}",  usuario.IDUsuario, usuario.NomeUsuario, usuario.Email));

                //
                // cabeçalho
                //

                // logo
                string logoConsultor = "LogoSmartEnergy85.png";

                if (IDConsultor > 0)
                {
                    UsuarioMetodos consultorMetodos = new UsuarioMetodos();
                    UsuarioDominio consultor = consultorMetodos.ListarPorId(IDConsultor);

                    if (consultor != null)
                    {
                        logoConsultor = consultor.LogoConsult;
                    }
                }

                ViewBag._LogoConsultor = logoConsultor;


                //
                // relatório
                //

                ViewBag.alertas = alertas;


                // nome do arquivo
                string nomeArquivo = string.Format("Alertas_{0:000000}_{1:yyyyMMdd}", usuario.IDUsuario, DateTime.Now);

                // partial view
                string viewPartial = "_Alertas_PDF";

                // buffer
                byte[] dataBuffer;

                // tipo do anexo
                string attachmentType = MediaTypeNames.Application.Pdf;

                //
                // PDF
                //

                // extesão do arquivo
                nomeArquivo += ".pdf";

                // tipo do anexo
                attachmentType = MediaTypeNames.Application.Pdf;


                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // PartialView como PDF
                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Landscape,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(10, 10, 0, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                dataBuffer = pdfResult.BuildFile(this.ControllerContext);

                // envia email
                EnviaEmail(usuario, nomeArquivo, attachmentType, dataBuffer);

                // log
                LogMessage("------");

                // estatística
                Num_Emails_Enviados++;

                // ok
                return (false);

            }
            catch (Exception ex)
            {
                LogMessage("[ERRO] " + ex.Message);
            }

            // erro
            return (true);
        }


        // envia email
        private void EnviaEmail(UsuarioDominio usuario, string attachmentName, string attachmentType, byte[] attachment)
        {
            // assunto
            string assunto = "[Smart Energy] Alertas";

            // envia EMAIL
            var emailTemplate = "EnviaAlertasEmail";

            var message = EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.UsuarioNome", usuario.NomeUsuario);
            message = message.Replace("ViewBag.LogoConsultor", ViewBag._LogoConsultor);

            EmailServices.SendEmail(usuario.Email, assunto, message, attachmentName, attachmentType, attachment);

            // log
            LogMessage(string.Format(">>> Enviou email para usuário [{0:000000}] {1}", usuario.IDUsuario, usuario.Email));

            return;
        }

        public static string EMailTemplate(string template)
        {
            var templateFilePath = HostingEnvironment.MapPath("~/Content/emailTemplates/") + template + ".cshtml";
            StreamReader objstreamreaderfile = new StreamReader(templateFilePath);
            var body = objstreamreaderfile.ReadToEnd();
            objstreamreaderfile.Close();
            return body;
        }


        // envia email estatistica
        private void EnviaEmailEstatistica(string mensagem, string mensagem_erro, string relatorios_erro)
        {
            // assunto
            string assunto = "[Smart Energy] Envio de Alertas";

            // envia EMAIL
            var emailTemplate = "EnviaRelatoriosEstatisticaEmail";

            var message = EMailTemplate(emailTemplate);
            message = message.Replace("ViewBag.UsuarioNome", "Junior");
            message = message.Replace("ViewBag.LogoConsultor", "LogoSmartEnergy.png");
            message = message.Replace("ViewBag.Mensagem", mensagem);
            message = message.Replace("ViewBag.ErroMensagem", mensagem_erro);
            message = message.Replace("ViewBag.RelatoriosErro", relatorios_erro);

            EmailServices.SendEmail("<EMAIL>", assunto, message, "", "", null);

            // log
            LogMessage(">>> Enviou email estatísticas");

            return;
        }


        private void LogMessage(string msg, string arquivo = null)
        {
            // nome de arquivo padrao
            string nome_arquivo = "LogEnviaAlertas_Pendentes_" + String.Format("{0:yyyyMMdd}", DateTime.Today) + ".txt";

            // verifica se tem nome de arquivo
            if (arquivo != null)
            {
                nome_arquivo = arquivo;
            }

            // subdiretorio LOG
            string exeRuntimeDirectory = Server.MapPath("~/EnviaRelatorios");
            string subDirectory = System.IO.Path.Combine(exeRuntimeDirectory, "Log");
            if (!System.IO.Directory.Exists(subDirectory))
            {
                // Output directory does not exist, so create it.
                System.IO.Directory.CreateDirectory(subDirectory);
            }

            // caminho completo
            string path = System.IO.Path.Combine(subDirectory, nome_arquivo);

            // abre arquivo
            System.IO.StreamWriter sw = System.IO.File.AppendText(path);

            try
            {
                // diferença de tempo
                DateTime datahora_agora = System.DateTime.Now;

                // grava log
                string logLine = System.String.Format(
                    "{0:dd/MM/yyyy HH:mm:ss}: {1}", datahora_agora, msg);
                sw.WriteLine(logLine);
                sw.Flush();
            }
            finally
            {
                sw.Close();
            }
        }
    }
}