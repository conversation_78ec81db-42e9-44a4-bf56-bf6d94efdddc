﻿using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ManutencaoController
    {
        public ActionResult ApagarEVFuturo()
        {
            // le cookie datahora
            DateTime DataIni = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, 0, 0, 0);

            ViewBag.DataIni = DataIni;

            return View();
        }

        // GET: Processo de apagar eventos no futuro
        public ActionResult _ApagarEVFuturo_Executar(string DataIni)
        {

            // data hora
            DateTime apagarDataHora = new DateTime(2000, 1, 1, 0, 0, 0);

            if (!DateTime.TryParse(DataIni, out apagarDataHora))
            {
                // executa imediatamente
                apagarDataHora = new DateTime(2000, 1, 1, 0, 0, 0);
            }

            // verifica data
            if (apagarDataHora < DateTime.Now)
            {
                // erro
                var returnedErro = new
                {
                    status = "ERRO",
                    erro = "Data Inicial no passado."
                };

                // retorna erro
                return Json(returnedErro, JsonRequestBehavior.AllowGet);
            }

            // leio todas as gateways
            GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
            List<GatewaysDominio> gateways = gatewayMetodos.ListarTodas_Gateways();

            // percorre gateways
            foreach (GatewaysDominio gateway in gateways)
            {
                // apaga eventos no futuro
                EV_Metodos evMetodos = new EV_Metodos();
                evMetodos.ExcluirDataFutura(gateway.IDCliente, gateway.IDGateway, apagarDataHora);
            }

            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = "OK"
            };

            // retorna ok
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }
    }
}