﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ContratosCCEEController
    {
        // GET: ContratosCCEE Grupos 
        public ActionResult ContratosCCEE_Grupos()
        {
            // tela de ajuda - grupos de contrato
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le grupos
            ContratosCCEE_GruposMetodos gruposMetodos = new ContratosCCEE_GruposMetodos();
            List<ContratosCCEE_Grupos> listaGrupos = gruposMetodos.ListarTodos();

            if (listaGrupos != null)
            {
                foreach (ContratosCCEE_Grupos grupo in listaGrupos)
                {
                    ContratosCCEE_GruposOrdemMetodos gruposOrdemMetodos = new ContratosCCEE_GruposOrdemMetodos();
                    List<ContratosCCEE_GruposOrdem> gruposOrdem = gruposOrdemMetodos.ListarPorId(grupo.IDContratoCCEE_Grupo);

                    if (gruposOrdem != null)
                    {
                        grupo.NumContratos = gruposOrdem.Count;
                    }
                }
            }

            return View(listaGrupos);
        }

        // GET: ContratosCCEE Grupos - Editar
        public ActionResult ContratosCCEE_Grupos_Editar(int IDContratoCCEE_Grupo)
        {
            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "ContratosCCEE");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // verifica se adicionando
            ContratosCCEE_Grupos grupo = new ContratosCCEE_Grupos();

            if (IDContratoCCEE_Grupo == 0)
            {
                // zera com default
                grupo.IDContratoCCEE_Grupo = 0;
                grupo.Nome = "";
            }
            else
            {
                // le grupo
                ContratosCCEE_GruposMetodos gruposMetodos = new ContratosCCEE_GruposMetodos();
                grupo = gruposMetodos.ListarPorId(IDContratoCCEE_Grupo);
            }

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // le clientes do consultor
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clienteMetodos.ListarPorIDConsultor(IDConsultor, 0);

            // le empresas dos clientes do consultor
            EmpresasMetodos empresasMetodos = new EmpresasMetodos();
            List<EmpresasDominio> listaEmpresas = new List<EmpresasDominio>();

            // percorre clientes
            if (listaClientes != null)
            {
                foreach (ClientesDominio cliente in listaClientes)
                {
                    // le empresas do cliente
                    List<EmpresasDominio> empresas = empresasMetodos.ListarPorIDCliente(cliente.IDCliente);

                    if (empresas != null)
                    {
                        // percorre empresas
                        foreach (EmpresasDominio empresa in empresas)
                        {
                            // adiciona na lista das empresas
                            listaEmpresas.Add(empresa);
                        }
                    }
                }
            }

            // le contratos CCEE vigentes de todos os clientes
            ContratosCCEEMetodos contratosMetodos = new ContratosCCEEMetodos();
            List<ContratosCCEEDominio> contratosVigentes = contratosMetodos.ListarPorIDCliente(0, TIPO_CONTRATO_STATUS.Vigente);

            // percorre contratos para resgatar nome dos clientes e empresas
            if (contratosVigentes != null)
            {
                foreach (ContratosCCEEDominio contrato in contratosVigentes)
                {
                    // nome e logo do cliente
                    ClientesDominio cliente = listaClientes.Find(c => c.IDCliente == contrato.IDCliente);

                    if (cliente != null)
                    {
                        contrato.NomeCliente = cliente.Nome;
                        contrato.Logo = cliente.Logo;
                    }

                    // SiglaCCEE da empresa
                    EmpresasDominio empresa = listaEmpresas.Find(e => e.IDEmpresa == contrato.IDEmpresa);

                    if (empresa != null)
                    {
                        contrato.RazaoSocial = empresa.RazaoSocial;
                        contrato.SiglaCCEE = empresa.SiglaCCEE;
                        contrato.IDTipoEmpresa = empresa.IDTipoEmpresa;
                        contrato.CNPJ = empresa.CNPJ;
                        contrato.IDEstado = empresa.IDEstado;
                        contrato.IDCidade = empresa.IDCidade;
                        contrato.Carteira = empresa.Carteira;
                    }

                    // formata datas auxiliares
                    contrato.Vigencia_Inicio_aux = String.Format("{0:d}", contrato.Vigencia_Inicio);
                    contrato.Vigencia_Inicio_Sort_aux = String.Format("{0:yyyyMMddHHmmss}", contrato.Vigencia_Inicio);
                    contrato.Vigencia_Fim_aux = String.Format("{0:d}", contrato.Vigencia_Fim);
                    contrato.Vigencia_Fim_Sort_aux = String.Format("{0:yyyyMMddHHmmss}", contrato.Vigencia_Fim);
                }
            }

            ViewBag.contratosVigentes = contratosVigentes;

            // le contratos do grupo
            ContratosCCEE_GruposOrdemMetodos gruposOrdemMetodos = new ContratosCCEE_GruposOrdemMetodos();
            List<ContratosCCEE_GruposOrdem> contratosGrupo = gruposOrdemMetodos.ListarPorId(grupo.IDContratoCCEE_Grupo);

            // percorre contratos para resgatar nome dos clientes e empresas
            if (contratosGrupo != null)
            {
                foreach (ContratosCCEE_GruposOrdem contratoGrupo in contratosGrupo)
                {
                    // contrato CCEE
                    ContratosCCEEDominio contratoCCEE = contratosVigentes.Find(c => c.IDContratoCCEE == contratoGrupo.IDContratoCCEE);

                    if (contratoCCEE != null)
                    {
                        contratoGrupo.NomeCliente = contratoCCEE.NomeCliente;
                        contratoGrupo.Logo = contratoCCEE.Logo;
                        contratoGrupo.RazaoSocial = contratoCCEE.RazaoSocial;
                        contratoGrupo.SiglaCCEE = contratoCCEE.SiglaCCEE;
                        contratoGrupo.CNPJ = contratoCCEE.CNPJ;
                        contratoGrupo.Contrato_ID = contratoCCEE.Contrato_ID;
                        contratoGrupo.Contrato_Codigo = contratoCCEE.Contrato_Codigo;
                    }
                    else
                    {
                        contratoGrupo.NomeCliente = "---";
                        contratoGrupo.Logo = "";
                        contratoGrupo.RazaoSocial = "---";
                        contratoGrupo.SiglaCCEE = "---";
                        contratoGrupo.CNPJ = "00.000.000/0000-00";
                        contratoGrupo.Contrato_ID = "---";
                        contratoGrupo.Contrato_Codigo = "---";
                    }
                }
            }

            ViewBag.contratosGrupo = contratosGrupo;

            return View(grupo);
        }


        // POST: ContratosCCEE Grupos - Salvar
        [HttpPost]
        public ActionResult ContratosCCEE_Grupos_Salvar(ContratosCCEE_Grupos grupo, List<ContratosCCEE_GruposOrdem> grupoOrdem)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro grupo com o mesmo nome
            ContratosCCEE_GruposMetodos gruposMetodos = new ContratosCCEE_GruposMetodos();
            if (gruposMetodos.VerificarDuplicidade(grupo))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Grupo existente."
                };
            }
            else
            {
                // salva grupo
                gruposMetodos.Salvar(grupo);

                // evento 
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();

                if (grupo.IDContratoCCEE_Grupo > 0)
                {
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.GRUPO_CONTRATO_CCEE, grupo.IDContratoCCEE_Grupo);
                }
                else
                {
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.GRUPO_CONTRATO_CCEE, grupo.IDContratoCCEE_Grupo);
                }


                // pego IDContratoCCEE_Grupo novamente (pois pode ter sido insercao)
                ContratosCCEE_Grupos gr = gruposMetodos.ListarPorNome(grupo.Nome);

                // salva grupo
                if (gr != null)
                {
                    ContratosCCEE_GruposOrdemMetodos gruposOrdemMetodos = new ContratosCCEE_GruposOrdemMetodos();

                    // exclui todos os contratos deste grupo
                    gruposOrdemMetodos.Excluir(gr.IDContratoCCEE_Grupo);

                    // salva ContratosCCEE_GruposOrdem
                    if (grupoOrdem != null)
                    {
                        // ordena lista
                        List<ContratosCCEE_GruposOrdem> grupoOrdem_Sorted = grupoOrdem.OrderBy(o => o.Ordem).ToList();

                        if (grupoOrdem_Sorted != null)
                        {
                            // ordem
                            //int ordem = 0;

                            // percorre lista e salva
                            foreach (ContratosCCEE_GruposOrdem grOrdem in grupoOrdem)
                            {
                                // preenche campos que faltam
                                grOrdem.IDContratoCCEE_Grupo = gr.IDContratoCCEE_Grupo;
                                //grOrdem.Ordem = ordem++;

                                // salva
                                gruposOrdemMetodos.Salvar(grOrdem);
                            }
                        }
                    }
                }
            }

            // retorna status
            return Json(returnedData);
        }


        // GET: ContratosCCEE Grupos - Excluir
        public ActionResult ContratosCCEE_Grupos_Excluir(int IDContratoCCEE_Grupo)
        {
            // apaga o grupo
            ContratosCCEE_GruposMetodos gruposMetodos = new ContratosCCEE_GruposMetodos();
            gruposMetodos.Excluir(IDContratoCCEE_Grupo);

            ContratosCCEE_GruposOrdemMetodos gruposOrdemMetodos = new ContratosCCEE_GruposOrdemMetodos();
            gruposOrdemMetodos.Excluir(IDContratoCCEE_Grupo);

            // evento 
            UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.EXCLUI_CONFIG, TABELA_CONFIG.GRUPO_CONTRATO_CCEE, IDContratoCCEE_Grupo);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}