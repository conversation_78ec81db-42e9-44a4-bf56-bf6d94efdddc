﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ContratosCCEEController
    {
        // GET: Preços Reajustados XLS Download
        [HttpGet]
        public virtual ActionResult PrecosReajustados_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Download Preços Reajustados
        public ActionResult DownloadFile_PrecosReajustados()
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            // nome do arquivo
            string nomeArquivo = string.Format("PrecosReajustados_Contratos_{0:yyyyMMddHHmm}.xls", DateTime.Now);

            // gera planilha
            workbook = PrecosReajustados_XLS(IDConsultor);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            // relatorio para PDF
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // Preços Reajustados Planilha
        private HSSFWorkbook PrecosReajustados_XLS(int IDConsultor)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // PREÇOS
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Contratos");

            // cabecalho
            string[] cabecalho = { "ID", "Data", "Preço Reajustado", "ID Contrato", "Razão Social", "Sigla CCEE", "CNPJ" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // le clientes do consultor - ativos
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clienteMetodos.ListarPorIDConsultor(IDConsultor, 1);

            // le empresas dos clientes do consultor
            EmpresasMetodos empresasMetodos = new EmpresasMetodos();
            List<EmpresasDominio> listaEmpresas = new List<EmpresasDominio>();

            // percorre clientes
            if (listaClientes != null)
            {
                foreach (ClientesDominio cliente in listaClientes)
                {
                    // le empresas do cliente
                    List<EmpresasDominio> empresas = empresasMetodos.ListarPorIDCliente(cliente.IDCliente);

                    if (empresas != null)
                    {
                        // percorre empresas
                        foreach (EmpresasDominio empresa in empresas)
                        {
                            // monto CNPJ Razão Social
                            empresa.CNPJ_RazaoSocial = string.Format("{0} [{1}]", empresa.CNPJ, empresa.RazaoSocial);

                            // adiciona na lista das empresas
                            listaEmpresas.Add(empresa);
                        }
                    }
                }
            }

            // le contratos
            ContratosCCEEMetodos contratosCCEE = new ContratosCCEEMetodos();
            List<ContratosCCEEDominio> contratos = contratosCCEE.ListarPorVigentes();

            if (contratos != null)
            {
                // percorre preços
                foreach (ContratosCCEEDominio contrato in contratos)
                {
                    // le ultimo preço reajustado
                    ContratosCCEE_PrecoReajustadoMetodos precoMetodos = new ContratosCCEE_PrecoReajustadoMetodos();
                    ContratosCCEE_PrecoReajustadoDominio precoReajustado = precoMetodos.UltimoPorIDContratoCCEE(contrato.IDContratoCCEE);

                    // zera data e preço reajustado
                    DateTime data_precoReajustado = new DateTime(2000, 1, 1, 0, 0, 0);
                    contrato.Reajuste_Preco = 0.0;

                    if (precoReajustado != null)
                    {
                        // data
                        data_precoReajustado = precoReajustado.Data;

                        // preco reajustado
                        contrato.Reajuste_Preco = precoReajustado.PrecoReajustado;
                    }

                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // IDContratoCCEE
                    numeroCelulaXLS(row, 0, contrato.IDContratoCCEE, _intCellStyle);

                    // Data
                    datahoraCelulaXLS(row, 1, data_precoReajustado, _dataStyle);

                    // Valor Reajustado
                    numeroCelulaXLS(row, 2, contrato.Reajuste_Preco, _2CellStyle);

                    // IDContrato
                    textoCelulaXLS(row, 3, contrato.Contrato_ID);

                    // empresa
                    contrato.RazaoSocial = "---";
                    contrato.SiglaCCEE = "---";
                    contrato.CNPJ = "---";

                    if (listaEmpresas != null)
                    {
                        // encontra empresa
                        EmpresasDominio empresa = listaEmpresas.Find(e => e.IDEmpresa == contrato.IDEmpresa);

                        if (empresa != null)
                        {
                            contrato.RazaoSocial = empresa.RazaoSocial;
                            contrato.SiglaCCEE = empresa.SiglaCCEE;
                            contrato.CNPJ = empresa.CNPJ;
                        }
                    }

                    // razão social
                    textoCelulaXLS(row, 4, contrato.RazaoSocial);

                    // SiglaCCEE
                    textoCelulaXLS(row, 5, contrato.SiglaCCEE);

                    // CNPJ
                    textoCelulaXLS(row, 6, contrato.CNPJ);

                    // proxima
                    rowIndex++;
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            // retorna planilha
            return workbook;
        }


        // cabecalho tabela XLS
        private int cabecalhoTabelaXLS(HSSFWorkbook workbook, ISheet sheet, string[] cabecalho, int linha = 0)
        {
            // estilo do cabecalho
            var cabecalhoCellStyle = workbook.CreateCellStyle();

            // borda superior e inferior
            cabecalhoCellStyle.BorderTop = BorderStyle.Thin;
            cabecalhoCellStyle.BorderBottom = BorderStyle.Thin;

            // alinhamento centro
            cabecalhoCellStyle.Alignment = HorizontalAlignment.Center;

            // negrito
            var cabecalhoFont = workbook.CreateFont();
            cabecalhoFont.Boldweight = (short)FontBoldWeight.Bold;
            cabecalhoCellStyle.SetFont(cabecalhoFont);

            // adiciona cabecalho
            var rowIndex = linha;
            var row = sheet.CreateRow(rowIndex++);

            // adiciona itens do cabecalho
            int conta_item = 0;

            foreach (string item in cabecalho)
            {
                // celula
                var cell = row.CreateCell(conta_item);
                cell.SetCellValue(item);
                cell.CellStyle = cabecalhoCellStyle;

                // proximo
                conta_item++;
            }

            return (rowIndex);
        }

        // cria estilo XLS
        private ICellStyle criaEstiloXLS(HSSFWorkbook workbook, int tipo_estilo)
        {
            // cria estilo
            ICellStyle estilo = null;
            estilo = workbook.CreateCellStyle();

            // cria estilo
            switch (tipo_estilo)
            {
                default:
                case 0:     // inteiro
                    estilo.DataFormat = workbook.CreateDataFormat().GetFormat("#,##0");
                    break;

                case 1:     // 1 casa decimal
                    estilo.DataFormat = workbook.CreateDataFormat().GetFormat("#,##0.0");
                    break;

                case 2:     // 2 casa decimal
                    estilo.DataFormat = workbook.CreateDataFormat().GetFormat("#,##0.00");
                    break;

                case 3:     // 3 casas decimais
                    estilo.DataFormat = workbook.CreateDataFormat().GetFormat("0.000");
                    break;

                case 4:     // inteiro
                    estilo.DataFormat = workbook.CreateDataFormat().GetFormat("0");
                    break;

                case 10:    // data e hora (DD/MM/AAAA HH:MM)
                    estilo.DataFormat = workbook.CreateDataFormat().GetFormat("dd/MM/yyyy HH:mm");
                    break;

                case 11:    // data (DD/MM/AAAA)
                    estilo.DataFormat = workbook.CreateDataFormat().GetFormat("dd/MM/yyyy");
                    break;

                case 12:    // hora (HH:MM)
                    estilo.DataFormat = workbook.CreateDataFormat().GetFormat("HH:mm");
                    break;

                case 13:    // data e hora (DD/MM/AAAA HH:MM:SS)
                    estilo.DataFormat = workbook.CreateDataFormat().GetFormat("dd/MM/yyyy HH:mm:ss");
                    break;

                case 20:    // texto negrito
                    var fonte = workbook.CreateFont();
                    fonte.Boldweight = (short)FontBoldWeight.Bold;
                    estilo.SetFont(fonte);
                    break;
            }

            // retorna estilo
            return (estilo);
        }

        // formata celula data e hora XLS
        private ICell datahoraCelulaXLS(IRow linha, int coluna, DateTime valor, ICellStyle estilo)
        {
            // cria celular
            var celula = linha.CreateCell(coluna);

            // tipo
            celula.SetCellType(CellType.Numeric);

            // valor
            celula.SetCellValue(valor);

            // aplica estilo
            celula.CellStyle = estilo;

            // retorna celula
            return (celula);
        }

        // formata celula numero XLS
        private ICell numeroCelulaXLS(IRow linha, int coluna, double valor, ICellStyle estilo)
        {
            // cria celular
            var celula = linha.CreateCell(coluna);

            // tipo
            celula.SetCellType(CellType.Numeric);

            // valor
            celula.SetCellValue(valor);

            // aplica estilo
            celula.CellStyle = estilo;

            // retorna celula
            return (celula);
        }

        // formata celula texto XLS
        private ICell textoCelulaXLS(IRow linha, int coluna, string valor, ICellStyle estilo = null)
        {
            // cria celular
            var celula = linha.CreateCell(coluna);

            // tipo
            celula.SetCellType(CellType.String);

            // valor
            celula.SetCellValue(valor);

            // aplica estilo
            if(estilo != null)
                celula.CellStyle = estilo;

            // retorna celula
            return (celula);
        }
    }
}