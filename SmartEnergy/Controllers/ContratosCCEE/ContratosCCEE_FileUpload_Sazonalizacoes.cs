﻿using System;
using System.Data;
using System.Data.OleDb;
using System.IO;
using System.Web;
using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class ContratosCCEEController
    {
        #region Actions

        /// <summary>
        /// Uploads the file.
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public virtual ActionResult UploadFile_Sazonalizacoes()
        {
            // arquivo enviado
            HttpPostedFileBase myFile = Request.Files["fileupload_Sazo"];

            // inicia variaveis
            bool isUploaded = false;
            string message = "Envio falhou";
            int num_sazos = 0;

            if (myFile != null && myFile.ContentLength != 0)
            {
                string pathForSaving = Server.MapPath("~/ContratosCCEE");
                if (this.CreateFolderIfNeeded(pathForSaving))
                {
                    try
                    {
                        // nome do arquivo
                        string nome_arq = myFile.FileName;

                        // nome do caminho e arquivo
                        string caminho_arq = Path.Combine(pathForSaving, myFile.FileName);

                        // extensão através dos ultimos 4 caracteres
                        string extensao_arq = nome_arq.Substring(nome_arq.Length - 4).ToLower();

                        // verifica se arquivo existe e apaga
                        FileInfo file = new FileInfo(caminho_arq);

                        if (file.Exists)
                        {
                            // deleta
                            file.Delete();
                        }

                        // salva arquivo
                        myFile.SaveAs(caminho_arq);

                        // flag erro
                        bool OcorreuErro = false;
                        string MensagemErro = "Formato não permitido";

                        // trata arquivo EXCEL
                        if (extensao_arq == ".xls" || extensao_arq == "xlsx")
                        {
                            // ler sazonalizações
                            num_sazos = LerExcel_Sazonalizacoes(caminho_arq, ref MensagemErro);

                            if (MensagemErro != "OK")
                            {
                                // falha
                                OcorreuErro = true;
                                message = string.Format("Envio falhou: {0}", MensagemErro);
                            }
                        }
                        else
                        {
                            // flag erro
                            OcorreuErro = true;
                            message = "Formato não permitido";
                        }

                        // verifica se nao ocorreu erro de leitura
                        if (!OcorreuErro)
                        {
                            // enviado, lido e atualizado BD com sucesso
                            isUploaded = true;
                            
                            switch (num_sazos)
                            {
                                case 0:
                                    OcorreuErro = true;
                                    message = "Não foram encontrados as sazonalizações da planilha.";
                                    break;

                                case 1:
                                    message = "A sazonalização foi atualizada.";
                                    break;

                                default:
                                    message = string.Format("Foram atualizadas {0} sazonalizações.", num_sazos);
                                    break;
                            };
                        }
                    }
                    catch (Exception ex)
                    {
                        // falha
                        message = string.Format("Envio falhou: {0}", ex.Message);
                    }
                }
            }
            return Json(new { isUploaded = isUploaded, message = message }, "text/html");
        }

        public int LerExcel_Sazonalizacoes(string caminho_arq, ref string MensagemErro)
        {
            // conexao
            // baixar o provedor de acesso Excel 2010 em "https://www.microsoft.com/en-us/download/details.aspx?id=13255"
            // AccessDatabaseEngine_X64.exe
            // configurando no SQL SERVER https://www.dirceuresende.com/blog/sql-server-como-instalar-os-drivers-microsoft-ace-oledb-12-0-e-microsoft-jet-oledb-4-0/
            OleDbConnection _olecon;
            OleDbCommand _oleCmd;
            String _StringConexao = String.Format(@"Provider=Microsoft.JET.OLEDB.4.0;Data Source={0};Extended Properties='Excel 8.0;HDR=YES;';", caminho_arq);
            _olecon = new OleDbConnection(_StringConexao);
            _oleCmd = new OleDbCommand();

            // Preços reajustados
            ContratosCCEEMetodos contratosMetodos = new ContratosCCEEMetodos();

            // linha convertida
            int linha = -1;
            int coluna = -1;
            int num_sazos = 0;

            // le Excel
            try
            {
                // conecta
                _olecon.Open();
                _oleCmd.Connection = _olecon;
                _oleCmd.CommandType = CommandType.Text;

                // le excel
                _oleCmd.CommandText = "SELECT * FROM [Sazonalizações$]";
                OleDbDataReader reader = _oleCmd.ExecuteReader();

                while (reader.Read())
                {
                    // sazonalização
                    ContratosCCEE_SazonalizacaoDominio sazo = new ContratosCCEE_SazonalizacaoDominio();

                    // IDContratoCCEE
                    linha = 0;
                    coluna = 0;
                    int IDContratoCCEE = Convert.ToInt32(reader.GetDouble(0));
                    sazo.IDContratoCCEE = IDContratoCCEE;

                    // Ano
                    coluna = 2;
                    sazo.Ano = Convert.ToInt32(reader.GetDouble(2));

                    // Janeiro
                    coluna = 3;
                    sazo.Jan = reader.GetDouble(3);

                    // Fevereiro
                    coluna = 4;
                    sazo.Fev = reader.GetDouble(4);

                    // Março
                    coluna = 5;
                    sazo.Mar = reader.GetDouble(5);

                    // Abril
                    coluna = 6;
                    sazo.Abr = reader.GetDouble(6);

                    // Maio
                    coluna = 7;
                    sazo.Mai = reader.GetDouble(7);

                    // Junho
                    coluna = 8;
                    sazo.Jun = reader.GetDouble(8);

                    // Julho
                    coluna = 9;
                    sazo.Jul = reader.GetDouble(9);

                    // Agosto
                    coluna = 10;
                    sazo.Ago = reader.GetDouble(10);

                    // Setembro
                    coluna = 11;
                    sazo.Set = reader.GetDouble(11);

                    // Outubro
                    coluna = 12;
                    sazo.Out = reader.GetDouble(12);

                    // Novembro
                    coluna = 13;
                    sazo.Nov = reader.GetDouble(13);

                    // Dezembro
                    coluna = 14;
                    sazo.Dez = reader.GetDouble(14);

                    // limpa sazonalização
                    ContratosCCEE_SazonalizacaoMetodos sazoMetodos = new ContratosCCEE_SazonalizacaoMetodos();
                    sazoMetodos.ExcluirAno(IDContratoCCEE, sazo.Ano);

                    // adiciona sazo
                    sazoMetodos.Adicionar(sazo);

                    // atualiza número de sazonalizações atualizadas
                    num_sazos++;

                    // proxima linha
                    linha++;
                }

                reader.Close();

                // termina
                if (_oleCmd != null)
                {
                    _oleCmd.Parameters.Clear();
                    _oleCmd.Dispose();
                }
                _oleCmd = null;

                if (_olecon != null)
                {
                    if (_olecon.State == ConnectionState.Open)
                        _olecon.Close();
                    _olecon.Dispose();
                }
                _olecon = null;

            }
            catch (Exception ex)
            {
                // termina
                if (_oleCmd != null)
                {
                    _oleCmd.Parameters.Clear();
                    _oleCmd.Dispose();
                }
                _oleCmd = null;

                if (_olecon != null)
                {
                    if (_olecon.State == ConnectionState.Open)
                        _olecon.Close();
                    _olecon.Dispose();
                }
                _olecon = null;
                
                // erro caso iniciou leitura do excel
                if (linha >= 0 && coluna >= 0)
                {  
                    MensagemErro = ex.Message + string.Format(" [Linha {0} | Coluna {1}]", linha + 2, coluna + 1);
                    return (-1);
                }

                // erro
                MensagemErro = ex.Message;
                return (-1);
            }
            
            // ok
            MensagemErro = "OK";

            return (num_sazos);
        }

        #endregion

    }
}