﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ContratosCCEEController
    {
        // Listas utilizadas
        private void PreparaListas_ContratosCCEE(int IDCliente)
        {
            // le tipos Cidade
            CidadeEstadoPaisMetodos cidadeMetodos = new CidadeEstadoPaisMetodos();
            List<CidadesDominio> listatiposCidade = cidadeMetodos.ListarTodosCidades(0);
            ViewBag.listaTipoCidade = listatiposCidade;

            // le tipos Estado
            List<EstadosDominio> listatiposEstado = cidadeMetodos.ListarTodosEstados();
            ViewBag.listaTipoEstado = listatiposEstado;

            // le tipos contrato status
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposContratoStatus = listatiposMetodos.ListarTodos("TipoContratoStatus", false);
            ViewBag.listaTipoContratoStatus = listatiposContratoStatus;

            // le comercializadoras
            AgentesMetodos agentesMetodos = new AgentesMetodos();
            List<AgentesDominio> listatiposComercializadoras = agentesMetodos.ListarPorComercializadora();
            ViewBag.listaTipoComercializadoras = listatiposComercializadoras;

            // le tipos carteira
            List<ListaTiposDominio> listatiposCarteira = listatiposMetodos.ListarTodos("TipoCarteira", false);
            ViewBag.listaTipoCarteira = listatiposCarteira;

            // le tipos fonte
            List<ListaTiposDominio> listatiposFonte = listatiposMetodos.ListarTodos("TipoFonte", false);
            ViewBag.listaTipoFonte = listatiposFonte;

            // le tipos SubSistema
            List<ListaTiposDominio> listatiposSubSistema = listatiposMetodos.ListarTodos("TipoSubSistema", false);
            ViewBag.listaTipoSubSistema = listatiposSubSistema;

            // le tipos sim não
            List<ListaTiposDominio> listatiposSimNao = listatiposMetodos.ListarTodos("TipoSimNao", false);
            ViewBag.listaTipoSimNao = listatiposSimNao;

            // le tipos meses
            List<ListaTiposDominio> listatiposMeses = listatiposMetodos.ListarTodos("TipoMeses", false);
            ViewBag.listaTipoMeses = listatiposMeses;

            // le tipos índice de reajuste
            List<ListaTiposDominio> listatiposIndiceReajuste = listatiposMetodos.ListarTodos("TipoIndiceReajuste", false);
            ViewBag.listaTipoIndiceReajuste = listatiposIndiceReajuste;

            // le tipos montante (inserir)
            List<ListaTiposDominio> listatiposMontante = listatiposMetodos.ListarTodos("TipoMontante", false);
            ViewBag.listaTipoMontante = listatiposMontante;

            // le tipos empresa
            List<ListaTiposDominio> listatiposEmpresa = listatiposMetodos.ListarTodos("TipoEmpresa", false);
            ViewBag.listaTipoEmpresa = listatiposEmpresa;

            return;
        }

        // GET: Contratos CCEE
        public ActionResult ContratosCCEE(int IDCliente = 0)
        {
            // tela de ajuda - contratos CCEE
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "ContratosCCEE");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // le tipos contrato status
            ListaTiposMetodos listatiposMetodos = new ListaTiposMetodos();
            List<ListaTiposDominio> listatiposContratoStatus = listatiposMetodos.ListarTodos("TipoContratoStatus", false);
            ViewBag.listaTipoContratoStatus = listatiposContratoStatus;

            // le tipos contrato status com NAO INICIADO do mês atual
            List<ListaTiposDominio> listatiposContratoStatus_MesAtual = listatiposContratoStatus;

            ListaTiposDominio tipo_aux = new ListaTiposDominio();
            tipo_aux.ID = 5;    // NÃO INICIADO no mês atual
            tipo_aux.Descricao = "NÃO INICIADO no mês atual";

            listatiposContratoStatus_MesAtual.Add(tipo_aux);
            ViewBag.listaTipoContratoStatus_MesAtual = listatiposContratoStatus_MesAtual;

            // le comercializadoras
            AgentesMetodos agentesMetodos = new AgentesMetodos();
            List<AgentesDominio> listatiposComercializadoras = agentesMetodos.ListarPorComercializadora();
            ViewBag.listaTipoComercializadoras = listatiposComercializadoras;

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // le clientes do consultor
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clienteMetodos.ListarPorIDConsultor(IDConsultor, 0);

            // le empresas dos clientes do consultor
            EmpresasMetodos empresasMetodos = new EmpresasMetodos();
            List<EmpresasDominio> listaEmpresas = new List<EmpresasDominio>();

            // percorre clientes
            if (listaClientes != null)
            {
                foreach (ClientesDominio cliente in listaClientes)
                {
                    // le empresas do cliente
                    List<EmpresasDominio> empresas = empresasMetodos.ListarPorIDCliente(cliente.IDCliente);

                    if (empresas != null)
                    {
                        // percorre empresas
                        foreach (EmpresasDominio empresa in empresas)
                        {
                            // adiciona na lista das empresas
                            listaEmpresas.Add(empresa);
                        }
                    }
                }
            }

            // le contratos CCEE do cliente
            ContratosCCEEMetodos contratosMetodos = new ContratosCCEEMetodos();
            List<ContratosCCEEDominio> listaContratos = contratosMetodos.ListarPorIDCliente(IDCliente, TIPO_CONTRATO_STATUS.Vigente);

            // percorre contratos para resgatar nome dos clientes e empresas
            if (listaContratos != null)
            {
                foreach (ContratosCCEEDominio contrato in listaContratos)
                {
                    // nome e logo do cliente
                    ClientesDominio cliente = listaClientes.Find(c => c.IDCliente == contrato.IDCliente);

                    if (cliente != null)
                    {
                        contrato.NomeCliente = cliente.Nome;
                        contrato.Logo = cliente.Logo;
                    }

                    // SiglaCCEE da empresa
                    EmpresasDominio empresa = listaEmpresas.Find(e => e.IDEmpresa == contrato.IDEmpresa);

                    if (empresa != null)
                    {
                        contrato.RazaoSocial = empresa.RazaoSocial;
                        contrato.SiglaCCEE = empresa.SiglaCCEE;
                        contrato.IDTipoEmpresa = empresa.IDTipoEmpresa;
                        contrato.CNPJ = empresa.CNPJ;
                        contrato.IDEstado = empresa.IDEstado;
                        contrato.IDCidade = empresa.IDCidade;
                        contrato.Carteira = empresa.Carteira;
                    }

                    // formata datas auxiliares
                    contrato.Vigencia_Inicio_aux = String.Format("{0:d}", contrato.Vigencia_Inicio);
                    contrato.Vigencia_Inicio_Sort_aux = String.Format("{0:yyyyMMddHHmmss}", contrato.Vigencia_Inicio);
                    contrato.Vigencia_Fim_aux = String.Format("{0:d}", contrato.Vigencia_Fim);
                    contrato.Vigencia_Fim_Sort_aux = String.Format("{0:yyyyMMddHHmmss}", contrato.Vigencia_Fim);
                }
            }

            return View(listaContratos);
        }

        // GET: Contrato CCEE - Editar
        public ActionResult ContratoCCEE_Editar(int IDContratoCCEE)
        {
            // tela de ajuda - contratos CCEE
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // pagina anterior
            ViewBag.returnUrl = Request.UrlReferrer;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "ContratosCCEE");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // le clientes do consultor
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clienteMetodos.ListarPorIDConsultor(IDConsultor, 0);
            ViewBag.listaClientes = listaClientes;

            // le empresas dos clientes do consultor
            EmpresasMetodos empresasMetodos = new EmpresasMetodos();
            List<EmpresasDominio> listaEmpresas = new List<EmpresasDominio>();

            // percorre clientes
            if (listaClientes != null)
            {
                foreach (ClientesDominio cliente in listaClientes)
                {
                    // le empresas do cliente
                    List<EmpresasDominio> empresas = empresasMetodos.ListarPorIDCliente(cliente.IDCliente);

                    if (empresas != null)
                    {
                        // percorre empresas
                        foreach (EmpresasDominio empresa in empresas)
                        {
                            // monto CNPJ Razão Social
                            empresa.CNPJ_RazaoSocial = string.Format("{0} [{1}]", empresa.CNPJ, empresa.RazaoSocial);

                            // medição da unidade consumidora
                            MedicoesDominio medicao = new MedicoesDominio();
                            empresa.IDMedicao_UnidadeConsumidora = Encontra_IDMedicao_UnidadeConsumidora_Principal(empresa.IDEmpresa, ref medicao);
                            empresa.NomeMedicao = "---";
                            empresa.PontoMedicao = "---";
                            empresa.IDSubSistema = 0;

                            if (medicao.IDMedicao > 0)
                            {
                                empresa.NomeMedicao = string.Format("[{0}] {1}", medicao.IDMedicao, medicao.Nome);
                                empresa.PontoMedicao = medicao.PontoMedicao;
                                empresa.IDSubSistema = medicao.IDSubSistema;
                            }

                            // adiciona na lista das empresas
                            listaEmpresas.Add(empresa);
                        }
                    }
                }
            }

            // ordena
            listaEmpresas = listaEmpresas.OrderBy(x => x.CNPJ_RazaoSocial).ToList();
            ViewBag.listaEmpresas = listaEmpresas;


            // verifica se adicionando
            ContratosCCEEDominio contrato = new ContratosCCEEDominio();

            if (IDContratoCCEE == 0)
            {
                // zera
                contrato.IDCliente = 0;
                contrato.IDEmpresa = 0;
                contrato.IDMedicao = 0;

                // encontra primeira empresa do consultor
                if (listaEmpresas != null)
                {
                    if (listaEmpresas.Count > 0)
                    {
                        // cliente, empresa e medição
                        contrato.IDCliente = listaEmpresas[0].IDCliente;
                        contrato.IDEmpresa = listaEmpresas[0].IDEmpresa;
                        contrato.IDMedicao = listaEmpresas[0].IDMedicao_UnidadeConsumidora;
                    }
                }

                // zera contrato com default
                contrato.Contrato_ID = "";
                contrato.Contrato_Codigo = "";
                contrato.Contrato_Status = 0;
                contrato.Alerta_Encerramento = true;
                contrato.IDComercializadora = 0;

                contrato.Carteira = 0;
                contrato.IDTipoFonte = 0;
                contrato.IDSubSistema = -1;

                contrato.Vigencia_Inicio = new DateTime(2000, 1, 1, 0, 0, 0);
                contrato.Vigencia_Inicio_Texto = contrato.Vigencia_Inicio.ToString("d");
                contrato.Vigencia_Fim = new DateTime(2000, 1, 1, 0, 0, 0);
                contrato.Vigencia_Fim_Texto = contrato.Vigencia_Fim.ToString("d");

                contrato.Base_Data = new DateTime(2000, 1, 1, 0, 0, 0);
                contrato.Base_Data_Texto = contrato.Base_Data.ToString("MM/yyyy");
                contrato.Base_Preco_Flat = true;
                contrato.Base_Preco = 0;

                contrato.Reajuste_Mes = 0;
                contrato.Reajuste_Indice = 0;
                contrato.Reajuste_Preco = 0;

                contrato.Montante_Tipo = 0;

                contrato.Flexibilidade_Minima = 0;
                contrato.Flexibilidade_Maxima = 0;
                contrato.Sazonalizacao_Minima = 0;
                contrato.Sazonalizacao_Maxima = 0;
                contrato.Modulacao_Minima = 0;
                contrato.Modulacao_Maxima = 0;

                contrato.Perdas = 0;

                contrato.Sazonaliza_1_Ano = 0;
                contrato.DataLimiteSazo = new DateTime(2000, 1, 1, 0, 0, 0);
                contrato.DataLimiteSazo_Texto = contrato.DataLimiteSazo.ToString("dd/MM");

                contrato.Proinfa_Abate = false;

                contrato.PercentualCarga_Possui = false;
                contrato.PercentualCarga_Valor = 100.0;

                contrato.ConsultorNegocio = "";

                contrato.Observacao = "";
                contrato.Ordem = 0;
                contrato.Considera = 1;

                contrato.NomeCliente = "";
                contrato.Logo = "";
                contrato.SiglaCCEE = "";
                contrato.IDTipoEmpresa = 0;
                contrato.CNPJ = "00.000.000/0000-00";
                contrato.IDEstado = 0;
                contrato.IDCidade = 0;
                contrato.NomeMedicao = "---";
                contrato.PontoMedicao = "---";
            }
            else
            {
                // le contrato
                ContratosCCEEMetodos contratosMetodos = new ContratosCCEEMetodos();
                contrato = contratosMetodos.ListarPorId(IDContratoCCEE);

                // verifica se flat
                if (contrato.Base_Preco_Flat)
                {
                    // cria preços base vazios
                    ZerarPrecosBase(contrato.IDContratoCCEE, contrato.Vigencia_Inicio_Texto, contrato.Vigencia_Fim_Texto);
                }
            }
            
            // prepara listas
            PreparaListas_ContratosCCEE(contrato.IDCliente);

            // caso tiver cliente
            if (contrato.IDCliente > 0)
            {
                // nome e logo do cliente
                ClientesDominio cliente = listaClientes.Find(c => c.IDCliente == contrato.IDCliente);

                if (cliente != null)
                {
                    contrato.NomeCliente = cliente.Nome;
                    contrato.Logo = cliente.Logo;
                }
            }

            // caso tiver empresa
            if (contrato.IDEmpresa > 0)
            {
                // SiglaCCEE da empresa
                if (listaEmpresas != null)
                {
                    EmpresasDominio empresa = listaEmpresas.Find(e => e.IDEmpresa == contrato.IDEmpresa);

                    if (empresa != null)
                    {
                        contrato.SiglaCCEE = empresa.SiglaCCEE;
                        contrato.IDTipoEmpresa = empresa.IDTipoEmpresa;
                        contrato.CNPJ = empresa.CNPJ;
                        contrato.IDEstado = empresa.IDEstado;
                        contrato.IDCidade = empresa.IDCidade;
                        contrato.Carteira = empresa.Carteira;

                        contrato.IDMedicao = empresa.IDMedicao_UnidadeConsumidora;
                    }
                }
            }

            // caso tiver medição
            contrato.NomeMedicao = "---";
            contrato.PontoMedicao = "---";
            contrato.IDSubSistema = 0;

            if (contrato.IDMedicao > 0)
            {
                // le medicoes do cliente
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                MedicoesDominio medicao = medicoesMetodos.ListarPorId(contrato.IDMedicao);

                if (medicao != null)
                {
                    if (contrato.IDMedicao > 0)
                    {
                        contrato.NomeMedicao = string.Format("[{0}] {1}", medicao.IDMedicao, medicao.Nome);
                        contrato.PontoMedicao = medicao.PontoMedicao;
                        contrato.IDSubSistema = medicao.IDSubSistema;
                    }
                }
            }

            // unidades atendidas
            ContratosCCEE_UnidadesAtendidasMetodos unidadesAtendidasMetodos = new ContratosCCEE_UnidadesAtendidasMetodos();
            List<ContratosCCEE_UnidadesAtendidasDominio> unidadesAtendidas = unidadesAtendidasMetodos.ListarPorId(contrato.IDContratoCCEE);

            // percorre unidades atendidas para preencher informações
            if (unidadesAtendidas != null)
            {
                foreach (ContratosCCEE_UnidadesAtendidasDominio unidade in unidadesAtendidas)
                {
                    EmpresasDominio empresa = listaEmpresas.Find(e => e.IDEmpresa == unidade.IDEmpresa);

                    if (empresa != null)
                    {
                        unidade.CNPJ_RazaoSocial = empresa.CNPJ_RazaoSocial;
                        unidade.RazaoSocial = empresa.RazaoSocial;
                        unidade.SiglaCCEE = empresa.SiglaCCEE;
                        unidade.IDTipoEmpresa = empresa.IDTipoEmpresa;
                        unidade.Agente = empresa.Agente;
                        unidade.CNPJ = empresa.CNPJ;
                        unidade.IDEstado = empresa.IDEstado;
                        unidade.IDCidade = empresa.IDCidade;
                        unidade.NomeMedicao = "---";
                        unidade.PontoMedicao = "---";

                        // caso tiver medição
                        if (empresa.IDMedicao_UnidadeConsumidora > 0)
                        {
                            // le medicoes do cliente
                            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                            MedicoesDominio medicao = medicoesMetodos.ListarPorId(empresa.IDMedicao_UnidadeConsumidora);

                            if (medicao != null)
                            {
                                if (medicao.IDMedicao > 0)
                                {
                                    unidade.NomeMedicao = string.Format("[{0}] {1}", medicao.IDMedicao, medicao.Nome);
                                    unidade.PontoMedicao = medicao.PontoMedicao;
                                }
                            }
                        }
                    }
                }
            }

            ViewBag.unidadesAtendidas = unidadesAtendidas;

            // unidades não atendidas
            List<ContratosCCEE_UnidadesAtendidasDominio> unidadesNaoAtendidas = new List<ContratosCCEE_UnidadesAtendidasDominio>();

            // percorre empresas do cliente
            if (listaEmpresas != null)
            {
                foreach (EmpresasDominio empresa in listaEmpresas)
                {
                    // não achou ainda
                    bool achou = false;

                    foreach (ContratosCCEE_UnidadesAtendidasDominio unidade in unidadesAtendidas)
                    {
                        // verifica se empresa esta na lista de unidades atendidas
                        if (unidade.IDEmpresa == empresa.IDEmpresa)
                        {
                            // achou
                            achou = true;
                            break;
                        }
                    }

                    // verifica se achou
                    if (!achou)
                    {
                        // coloca na lista de não atendidas
                        ContratosCCEE_UnidadesAtendidasDominio unid = new ContratosCCEE_UnidadesAtendidasDominio();
                        unid.IDEmpresa = empresa.IDEmpresa;

                        unidadesNaoAtendidas.Add(unid);
                    }
                }
            }

            // percorre unidades não atendidas para preencher informações
            if (unidadesNaoAtendidas != null)
            {
                foreach (ContratosCCEE_UnidadesAtendidasDominio unidade in unidadesNaoAtendidas)
                {
                    EmpresasDominio empresa = listaEmpresas.Find(e => e.IDEmpresa == unidade.IDEmpresa);

                    if (empresa != null)
                    {
                        unidade.CNPJ_RazaoSocial = empresa.CNPJ_RazaoSocial;
                        unidade.RazaoSocial = empresa.RazaoSocial;
                        unidade.SiglaCCEE = empresa.SiglaCCEE;
                        unidade.IDTipoEmpresa = empresa.IDTipoEmpresa;
                        unidade.Agente = empresa.Agente;
                        unidade.CNPJ = empresa.CNPJ;
                        unidade.IDEstado = empresa.IDEstado;
                        unidade.IDCidade = empresa.IDCidade;
                        unidade.NomeMedicao = "---";
                        unidade.PontoMedicao = "---";

                        // caso tiver medição
                        if (empresa.IDMedicao_UnidadeConsumidora > 0)
                        {
                            // le medicoes do cliente
                            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
                            MedicoesDominio medicao = medicoesMetodos.ListarPorId(empresa.IDMedicao_UnidadeConsumidora);

                            if (medicao != null)
                            {
                                if (medicao.IDMedicao > 0)
                                {
                                    unidade.NomeMedicao = string.Format("[{0}] {1}", medicao.IDMedicao, medicao.Nome);
                                    unidade.PontoMedicao = medicao.PontoMedicao;
                                }
                            }
                        }
                    }
                }
            }

            ViewBag.unidadesNaoAtendidas = unidadesNaoAtendidas;

            return View(contrato);
        }

        // encontra IDMedicao da unidade consumidoda da empresa
        private int Encontra_IDMedicao_UnidadeConsumidora_Principal(int IDEmpresa, ref MedicoesDominio medicao)
        {
            // IDMedicao unidade consumidora
            int IDMedicao_UnidadeConsumidora = 0;

            medicao.Nome = "---";
            medicao.PontoMedicao = "---";
            medicao.IDMedicao = 0;
            medicao.IDSubSistema = 0;

            // leio gateways atreladas a esta empresa
            GatewaysMetodos gatewayMetodos = new GatewaysMetodos();
            List<GatewaysDominio> gateways = gatewayMetodos.ListarPorIDEmpresa(IDEmpresa);

            if (gateways != null)
            {
                // percorre gateways e encontra primeira medição de energia marcada como unidade consumidora
                foreach (GatewaysDominio gateway in gateways)
                {
                    // filtro, somente medições principais e em ordem de número de medição interna
                    string order = "AND IDCategoriaMedicao = 0 ORDER BY NumMedGateway";

                    // leio medições da gateway
                    MedicoesMetodos medicaoMetodos = new MedicoesMetodos();
                    List<MedicoesDominio> medicoes = medicaoMetodos.ListarPorIDGateway(gateway.IDGateway, order);

                    if (medicoes != null)
                    {
                        // percorre medições e encontra primeira medição de energia marcada como unidade consumidora
                        foreach (MedicoesDominio med in medicoes)
                        {
                            // verifica se medição de energia elétrica e medição 0
                            //if (med.IDTipoMedicao == TIPO_MEDICAO.ENERGIA && med.NumMedGateway == 0)

                            // verifica se medição de energia elétrica
                            if (med.IDTipoMedicao == TIPO_MEDICAO.ENERGIA)
                            {
                                // copia
                                medicao = med;

                                // achou
                                IDMedicao_UnidadeConsumidora = med.IDMedicao;
                                break;
                            }
                        }
                    }

                    // verifica se achou
                    if (IDMedicao_UnidadeConsumidora > 0)
                    {
                        // interrompe busca
                        break;
                    }
                }
            }

            return (IDMedicao_UnidadeConsumidora);
        }

        // GET: Salvar unidade atendida Empresas
        public JsonResult SalvarUnidadeAtendida(int IDContratoCCEE, int IDEmpresa, string Contrato_ID, double PercentualCarga_Valor)
        {
            // salvar
            ContratosCCEE_UnidadesAtendidasMetodos unidadeAtendidaMetodos = new ContratosCCEE_UnidadesAtendidasMetodos();
            unidadeAtendidaMetodos.Adicionar(IDContratoCCEE, IDEmpresa, Contrato_ID, PercentualCarga_Valor);

            // retorna
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // GET: Excluir unidade atendida Empresas
        public JsonResult ExcluirUnidadeAtendida(int IDContratoCCEE, int IDEmpresa)
        {
            // excluir
            ContratosCCEE_UnidadesAtendidasMetodos unidadeAtendidaMetodos = new ContratosCCEE_UnidadesAtendidasMetodos();
            unidadeAtendidaMetodos.ExcluirEmpresa(IDContratoCCEE, IDEmpresa);

            // retorna
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // GET: Obter Unidades Atendidas
        public JsonResult ObterUnidadesAtendidas(int IDContratoCCEE)
        {
            // unidades atendidas
            ContratosCCEE_UnidadesAtendidasMetodos unidadesAtendidasMetodos = new ContratosCCEE_UnidadesAtendidasMetodos();
            List<ContratosCCEE_UnidadesAtendidasDominio> unidadesAtendidas = unidadesAtendidasMetodos.ListarPorId(IDContratoCCEE);

            // empresa
            EmpresasMetodos empresaMetodos = new EmpresasMetodos();

            // percorre unidades atendidas para preencher informações
            if (unidadesAtendidas != null)
            {
                foreach (ContratosCCEE_UnidadesAtendidasDominio unidade in unidadesAtendidas)
                {
                    EmpresasDominio empresa = empresaMetodos.ListarPorId(unidade.IDEmpresa);

                    if (empresa != null)
                    {
                        unidade.CNPJ_RazaoSocial = string.Format("{0} [{1}]", empresa.CNPJ, empresa.RazaoSocial);
                        unidade.RazaoSocial = empresa.RazaoSocial;
                        unidade.SiglaCCEE = empresa.SiglaCCEE;
                        unidade.IDTipoEmpresa = empresa.IDTipoEmpresa;
                        unidade.Agente = empresa.Agente;
                        unidade.CNPJ = empresa.CNPJ;
                        unidade.IDEstado = empresa.IDEstado;
                        unidade.IDCidade = empresa.IDCidade;
                        unidade.NomeMedicao = "---";
                        unidade.PontoMedicao = "---";

                        // IDMedicao Unidade Consumidora
                        MedicoesDominio medicao = new MedicoesDominio();
                        empresa.IDMedicao_UnidadeConsumidora = Encontra_IDMedicao_UnidadeConsumidora_Principal(empresa.IDEmpresa, ref medicao);

                        // caso tiver medição
                        if (empresa.IDMedicao_UnidadeConsumidora > 0)
                        {
                            if (medicao != null)
                            {
                                if (medicao.IDMedicao > 0)
                                {
                                    unidade.NomeMedicao = string.Format("[{0}] {1}", medicao.IDMedicao, medicao.Nome);
                                    unidade.PontoMedicao = medicao.PontoMedicao;
                                }
                            }
                        }
                    }
                }
            }

            // retorna o valor em JSON
            return Json(unidadesAtendidas, JsonRequestBehavior.AllowGet);
        }

        // GET: Obter Unidades Não Atendidas
        public JsonResult ObterUnidadesNaoAtendidas(int IDContratoCCEE)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // le clientes do consultor - ativos
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clienteMetodos.ListarPorIDConsultor(IDConsultor, 0);

            // le empresas dos clientes do consultor
            EmpresasMetodos empresasMetodos = new EmpresasMetodos();
            List<EmpresasDominio> listaEmpresas = new List<EmpresasDominio>();

            // percorre clientes
            if (listaClientes != null)
            {
                foreach (ClientesDominio cliente in listaClientes)
                {
                    // le empresas do cliente
                    List<EmpresasDominio> empresas = empresasMetodos.ListarPorIDCliente(cliente.IDCliente);

                    if (empresas != null)
                    {
                        // percorre empresas
                        foreach (EmpresasDominio empresa in empresas)
                        {
                            // monto CNPJ Razão Social
                            empresa.CNPJ_RazaoSocial = string.Format("{0} [{1}]", empresa.CNPJ, empresa.RazaoSocial);

                            // adiciona na lista das empresas
                            listaEmpresas.Add(empresa);
                        }
                    }
                }
            }

            // unidades atendidas
            ContratosCCEE_UnidadesAtendidasMetodos unidadesAtendidasMetodos = new ContratosCCEE_UnidadesAtendidasMetodos();
            List<ContratosCCEE_UnidadesAtendidasDominio> unidadesAtendidas = unidadesAtendidasMetodos.ListarPorId(IDContratoCCEE);

            // unidades não atendidas
            List<ContratosCCEE_UnidadesAtendidasDominio> unidadesNaoAtendidas = new List<ContratosCCEE_UnidadesAtendidasDominio>();

            // percorre empresas do cliente
            if (listaEmpresas != null)
            {
                foreach (EmpresasDominio empresa in listaEmpresas)
                {
                    // não achou ainda
                    bool achou = false;

                    foreach (ContratosCCEE_UnidadesAtendidasDominio unidade in unidadesAtendidas)
                    {
                        // verifica se empresa esta na lista de unidades atendidas
                        if (unidade.IDEmpresa == empresa.IDEmpresa)
                        {
                            // achou
                            achou = true;
                            break;
                        }
                    }

                    // verifica se achou
                    if (!achou)
                    {
                        // coloca na lista de não atendidas
                        ContratosCCEE_UnidadesAtendidasDominio unid = new ContratosCCEE_UnidadesAtendidasDominio();
                        unid.IDEmpresa = empresa.IDEmpresa;

                        unidadesNaoAtendidas.Add(unid);
                    }
                }
            }

            // empresa
            EmpresasMetodos empresaMetodos = new EmpresasMetodos();

            // percorre unidades não atendidas para preencher informações
            if (unidadesNaoAtendidas != null)
            {
                foreach (ContratosCCEE_UnidadesAtendidasDominio unidade in unidadesNaoAtendidas)
                {
                    EmpresasDominio empresa = empresaMetodos.ListarPorId(unidade.IDEmpresa);

                    if (empresa != null)
                    {
                        unidade.CNPJ_RazaoSocial = string.Format("{0} [{1}]", empresa.CNPJ, empresa.RazaoSocial);
                        unidade.RazaoSocial = empresa.RazaoSocial;
                        unidade.SiglaCCEE = empresa.SiglaCCEE;
                        unidade.IDTipoEmpresa = empresa.IDTipoEmpresa;
                        unidade.Agente = empresa.Agente;
                        unidade.CNPJ = empresa.CNPJ;
                        unidade.IDEstado = empresa.IDEstado;
                        unidade.IDCidade = empresa.IDCidade;
                        unidade.NomeMedicao = "---";
                        unidade.PontoMedicao = "---";

                        // IDMedicao Unidade Consumidora
                        MedicoesDominio medicao = new MedicoesDominio();
                        empresa.IDMedicao_UnidadeConsumidora = Encontra_IDMedicao_UnidadeConsumidora_Principal(empresa.IDEmpresa, ref medicao);

                        // caso tiver medição
                        if (empresa.IDMedicao_UnidadeConsumidora > 0)
                        {
                            if (medicao != null)
                            {
                                if (medicao.IDMedicao > 0)
                                {
                                    unidade.NomeMedicao = string.Format("[{0}] {1}", medicao.IDMedicao, medicao.Nome);
                                    unidade.PontoMedicao = medicao.PontoMedicao;
                                }
                            }
                        }
                    }
                }
            }

            // retorna o valor em JSON
            return Json(unidadesNaoAtendidas, JsonRequestBehavior.AllowGet);
        }


        //
        // PRECOS BASE
        //

        // GET: Salvar Preços Base
        public JsonResult SalvarPrecosBase(int IDContratoCCEE, List<ContratosCCEE_PrecoBaseDominio> precos_base)
        {
            // apaga os preços base
            ContratosCCEE_PrecoBaseMetodos precoBaseMetodos = new ContratosCCEE_PrecoBaseMetodos();
            precoBaseMetodos.ExcluirContrato(IDContratoCCEE);

            // percorre preços base e salva
            if (precos_base != null)
            {
                foreach (ContratosCCEE_PrecoBaseDominio preco_base in precos_base)
                {
                    // data
                    DateTime data = new DateTime();
                    data = DateTime.ParseExact(preco_base.Data_Texto, "yyyy", CultureInfo.CreateSpecificCulture("pt-BR"));
                    preco_base.Data = data;

                    // salva
                    precoBaseMetodos.Adicionar(preco_base);
                }
            }

            // retorna
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // GET: Obter Preços Base
        public JsonResult ObterPrecosBase(int IDContratoCCEE)
        {
            // preço base
            ContratosCCEE_PrecoBaseMetodos precoBaseMetodos = new ContratosCCEE_PrecoBaseMetodos();
            List<ContratosCCEE_PrecoBaseDominio> precos_base = precoBaseMetodos.ListarPorId(IDContratoCCEE);

            // retorna o valor em JSON
            return Json(precos_base, JsonRequestBehavior.AllowGet);
        }

        // GET: Zerar Preços Base
        public JsonResult ZerarPrecosBase(int IDContratoCCEE, string Vigencia_Inicio_Texto, string Vigencia_Fim_Texto)
        {
            // apaga os preços base
            ContratosCCEE_PrecoBaseMetodos precoBaseMetodos = new ContratosCCEE_PrecoBaseMetodos();
            precoBaseMetodos.ExcluirContrato(IDContratoCCEE);

            // pega data
            DateTime Vigencia_Inicio = DateTime.Parse(Vigencia_Inicio_Texto);
            DateTime Vigencia_Fim = DateTime.Parse(Vigencia_Fim_Texto);

            // verifica se invertido
            if (Vigencia_Inicio > Vigencia_Fim)
            {
                // inverto
                Vigencia_Fim = DateTime.Parse(Vigencia_Inicio_Texto);
                Vigencia_Inicio = DateTime.Parse(Vigencia_Fim_Texto);
            }

            // data inicial
            DateTime data_inicio = new DateTime(Vigencia_Inicio.Year, 1, 1, 0, 0, 0);

            // anos
            int ano_Inicio = Vigencia_Inicio.Year;
            int ano_Fim = Vigencia_Fim.Year;

            // número de anos
            int numero_anos = (ano_Fim - ano_Inicio) + 1;

            // crio preços base
            for (int i = 0; i < numero_anos; i++)
            {
                // preço base
                ContratosCCEE_PrecoBaseDominio preco_base = new ContratosCCEE_PrecoBaseDominio();

                preco_base.IDContratoCCEE = IDContratoCCEE;
                preco_base.Data = new DateTime(data_inicio.Year, 1, 1, 0, 0, 0);
                preco_base.Data_Texto = "";
                preco_base.Base_Preco = 0;

                // insere
                precoBaseMetodos.Adicionar(preco_base);

                // próximo ano
                data_inicio = data_inicio.AddYears(1);
            }

            // retorna
            return Json("OK", JsonRequestBehavior.AllowGet);
        }


        //
        // VOLUMES
        //

        // GET: Salvar volumes
        public JsonResult SalvarVolumes(int IDContratoCCEE, int Montante_Tipo, List<ContratosCCEE_VolumeDominio> volumes)
        {
            // apaga os volumes
            ContratosCCEE_VolumeMetodos volumeMetodos = new ContratosCCEE_VolumeMetodos();
            volumeMetodos.ExcluirContrato(IDContratoCCEE, Montante_Tipo);

            // percorre volumes e salva
            if (volumes != null)
            {
                foreach (ContratosCCEE_VolumeDominio volume in volumes)
                {
                    // data
                    DateTime data = new DateTime();

                    // verifica tipo
                    if (Montante_Tipo == 0)
                    {
                        // caso for Montante: volume é por ano
                        data = DateTime.ParseExact(volume.Data_Texto, "yyyy", CultureInfo.CreateSpecificCulture("pt-BR"));
                    }
                    else
                    {
                        // caso for Sazonalização: volume é por mês
                        data = DateTime.ParseExact(volume.Data_Texto, "MM/yyyy", CultureInfo.CreateSpecificCulture("pt-BR"));
                    }

                    volume.Montante_Tipo = Montante_Tipo;
                    volume.Data = data;

                    // salva
                    volumeMetodos.Adicionar(volume);
                }
            }

            // retorna
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        // GET: Obter Volumes
        public JsonResult ObterVolumes(int IDContratoCCEE, int Montante_Tipo, string Vigencia_Inicio_Texto, string Vigencia_Fim_Texto)
        {
            // volume
            ContratosCCEE_VolumeMetodos volumeMetodos = new ContratosCCEE_VolumeMetodos();
            List<ContratosCCEE_VolumeDominio> volumes = volumeMetodos.ListarPorId(IDContratoCCEE, Montante_Tipo);

            // criar volume
            bool criar_volume = true;

            if (volumes != null)
            {
                if (volumes.Count > 0)
                {
                    // leu, não preciso criar
                    criar_volume = false;

                }
            }

            // verifica se deve criar o volume
            if (criar_volume)
            {
                // cria volumes
                ZerarVolumes(IDContratoCCEE, Montante_Tipo, Vigencia_Inicio_Texto, Vigencia_Fim_Texto);

                // leio volume
                volumes = volumeMetodos.ListarPorId(IDContratoCCEE, Montante_Tipo);
            }

            // verifica se existe
            if (volumes != null)
            {
                foreach (ContratosCCEE_VolumeDominio volume in volumes)
                {
                    // verifica tipo
                    if (Montante_Tipo == 0)
                    {
                        // caso for Montante: volume é por ano
                        volume.Data_Texto = volume.Data.ToString("yyyy");
                    }
                    else
                    {
                        // caso for Sazonalização: volume é por mês
                        volume.Data_Texto = volume.Data.ToString("MM/yyyy");
                    }
                }
            }

            // retorna o valor em JSON
            return Json(volumes, JsonRequestBehavior.AllowGet);
        }

        // GET: Zerar Volumes
        public JsonResult ZerarVolumes(int IDContratoCCEE, int Montante_Tipo, string Vigencia_Inicio_Texto, string Vigencia_Fim_Texto)
        {
            // apaga os volumes
            ContratosCCEE_VolumeMetodos volumeMetodos = new ContratosCCEE_VolumeMetodos();
            volumeMetodos.ExcluirContrato(IDContratoCCEE);

            // pega data
            DateTime Vigencia_Inicio = DateTime.Parse(Vigencia_Inicio_Texto);
            DateTime Vigencia_Fim = DateTime.Parse(Vigencia_Fim_Texto);

            // verifica se invertido
            if (Vigencia_Inicio > Vigencia_Fim)
            {
                // inverto
                Vigencia_Fim = DateTime.Parse(Vigencia_Inicio_Texto);
                Vigencia_Inicio = DateTime.Parse(Vigencia_Fim_Texto);
            }

            //
            // MONTANTE (volume é por ano)
            //

            // data inicial
            DateTime data_inicio = new DateTime(Vigencia_Inicio.Year, 1, 1, 0, 0, 0);

            // anos
            int ano_Inicio = Vigencia_Inicio.Year;
            int ano_Fim = Vigencia_Fim.Year;

            // número de anos
            int numero_anos = (ano_Fim - ano_Inicio) + 1;

            // crio volumes
            for (int i=0; i<numero_anos; i++)
            {
                // volume
                ContratosCCEE_VolumeDominio volume = new ContratosCCEE_VolumeDominio();

                volume.IDContratoCCEE = IDContratoCCEE;
                volume.Data = new DateTime(data_inicio.Year, 1, 1, 0, 0, 0);
                volume.Data_Texto = "";
                volume.Montante_Tipo = 0;
                volume.Contratado = 0;

                // insere
                volumeMetodos.Adicionar(volume);

                // próximo ano
                data_inicio = data_inicio.AddYears(1);
            }


            //
            // SAZONALIZAÇÃO (volume é por mês)
            //

            // datas
            data_inicio = new DateTime(Vigencia_Inicio.Year, Vigencia_Inicio.Month, 1, 0, 0, 0);
            DateTime data_fim = new DateTime(Vigencia_Fim.Year, Vigencia_Fim.Month, 1, 0, 0, 0);

            // número de meses
            int numero_meses = (ajustaMesAno(data_fim) - ajustaMesAno(data_inicio)) + 1;

            // crio volumes
            for (int i = 0; i < numero_meses; i++)
            {
                // volume
                ContratosCCEE_VolumeDominio volume = new ContratosCCEE_VolumeDominio();

                volume.IDContratoCCEE = IDContratoCCEE;
                volume.Data = new DateTime(data_inicio.Year, data_inicio.Month, 1, 0, 0, 0);
                volume.Data_Texto = "";
                volume.Montante_Tipo = 1;
                volume.Contratado = 0;

                // insere
                volumeMetodos.Adicionar(volume);

                // próximo mês
                data_inicio = data_inicio.AddMonths(1);
            }

            // retorna
            return Json("OK", JsonRequestBehavior.AllowGet);
        }

        static int ajustaMesAno(DateTime d) {
            return d.Year * 12 + d.Month;
        }


        //
        // EMPRESA
        //

        // GET: Obter Empresa
        public JsonResult ObterEmpresa(int IDEmpresa)
        {
            // le empresa
            EmpresasMetodos empresasMetodos = new EmpresasMetodos();
            EmpresasDominio empresa = empresasMetodos.ListarPorId(IDEmpresa);

            if (empresa != null)
            {
                // medição da unidade consumidora
                MedicoesDominio medicao = new MedicoesDominio();
                empresa.IDMedicao_UnidadeConsumidora = Encontra_IDMedicao_UnidadeConsumidora_Principal(empresa.IDEmpresa, ref medicao);

                if (medicao.IDMedicao > 0)
                {
                    empresa.NomeMedicao = string.Format("[{0}] {1}", medicao.IDMedicao, medicao.Nome);
                    empresa.PontoMedicao = medicao.PontoMedicao;
                    empresa.IDSubSistema = medicao.IDSubSistema;
                }
            }

            // retorna o valor em JSON
            return Json(empresa, JsonRequestBehavior.AllowGet);
        }

        //
        // CONTRATOS
        //

        // GET: Obter Contratos
        public JsonResult ObterContratos(int Contrato_Status)
        {
            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // le clientes do consultor - ativos
            ClientesMetodos clienteMetodos = new ClientesMetodos();
            List<ClientesDominio> listaClientes = clienteMetodos.ListarPorIDConsultor(IDConsultor, 0);

            // le empresas dos clientes do consultor
            EmpresasMetodos empresasMetodos = new EmpresasMetodos();
            List<EmpresasDominio> listaEmpresas = new List<EmpresasDominio>();

            // percorre clientes
            if (listaClientes != null)
            {
                foreach (ClientesDominio cliente in listaClientes)
                {
                    // le empresas do cliente
                    List<EmpresasDominio> empresas = empresasMetodos.ListarPorIDCliente(cliente.IDCliente);

                    if (empresas != null)
                    {
                        // percorre empresas
                        foreach (EmpresasDominio empresa in empresas)
                        {
                            // adiciona na lista das empresas
                            listaEmpresas.Add(empresa);
                        }
                    }
                }
            }

            // le contratos CCEE do cliente
            ContratosCCEEMetodos contratosMetodos = new ContratosCCEEMetodos();
            List<ContratosCCEEDominio> listaContratos = contratosMetodos.ListarPorIDCliente(0, Contrato_Status);

            // percorre contratos para resgatar nome dos clientes e empresas
            if (listaContratos != null)
            {
                foreach (ContratosCCEEDominio contrato in listaContratos)
                {
                    // nome e logo do cliente
                    ClientesDominio cliente = listaClientes.Find(c => c.IDCliente == contrato.IDCliente);

                    if (cliente != null)
                    {
                        contrato.NomeCliente = cliente.Nome;
                        contrato.Logo = cliente.Logo;
                    }

                    // SiglaCCEE da empresa
                    EmpresasDominio empresa = listaEmpresas.Find(e => e.IDEmpresa == contrato.IDEmpresa);

                    if (empresa != null)
                    {
                        contrato.RazaoSocial = empresa.RazaoSocial;
                        contrato.SiglaCCEE = empresa.SiglaCCEE;
                        contrato.IDTipoEmpresa = empresa.IDTipoEmpresa;
                        contrato.CNPJ = empresa.CNPJ;
                        contrato.IDEstado = empresa.IDEstado;
                        contrato.IDCidade = empresa.IDCidade;
                        contrato.Carteira = empresa.Carteira;
                    }

                    // formata datas auxiliares
                    contrato.Vigencia_Inicio_aux = String.Format("{0:d}", contrato.Vigencia_Inicio);
                    contrato.Vigencia_Inicio_Sort_aux = String.Format("{0:yyyyMMddHHmmss}", contrato.Vigencia_Inicio);
                    contrato.Vigencia_Fim_aux = String.Format("{0:d}", contrato.Vigencia_Fim);
                    contrato.Vigencia_Fim_Sort_aux = String.Format("{0:yyyyMMddHHmmss}", contrato.Vigencia_Fim);
                }
            }

            // retorna o valor em JSON
            return Json(listaContratos, JsonRequestBehavior.AllowGet);
        }


        // GET: Alterar status dos contratos NAO INICIADO para VIGENTE
        public ActionResult ContratoCCEE_AlterarStatusVigente(List<int> contratos)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se tem contratos a alterar
            if (contratos != null)
            {
                // percorre contratos
                foreach (int contrato in contratos)
                {
                    // altera status
                    ContratosCCEEMetodos contratosMetodos = new ContratosCCEEMetodos();
                    contratosMetodos.Alterar_Status_Vigente(contrato);
                }
            }
            else
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Contrato não selecionado."
                };
            }

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        //
        // PRECOS REAJUSTADOS
        //

        // GET: Obter Preços Reajustados
        public JsonResult ObterPrecosReajustados(int IDContratoCCEE)
        {
            // Preços Reajustados
            ContratosCCEE_PrecoReajustadoMetodos precoMetodos = new ContratosCCEE_PrecoReajustadoMetodos();
            List<ContratosCCEE_PrecoReajustadoDominio> precos = precoMetodos.ListarPorIDContratoCCEE(IDContratoCCEE);

            // retorna o valor em JSON
            return Json(precos, JsonRequestBehavior.AllowGet);
        }

        // GET: Preços Reajustados - Salvar
        public JsonResult SalvarPrecoReajustado(int IDContratoCCEE, string Data_PrecoReajustado, double Valor_PrecoReajustado)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            try
            {
                // pega data
                DateTime data = new DateTime();
                data = DateTime.ParseExact(Data_PrecoReajustado, "MM/yyyy", CultureInfo.CreateSpecificCulture("pt-BR"));

                // preço reajustado
                ContratosCCEE_PrecoReajustadoDominio preco = new ContratosCCEE_PrecoReajustadoDominio();

                preco.IDPrecoReajustado = 0;
                preco.IDContratoCCEE = IDContratoCCEE;
                preco.Data = data;
                preco.PrecoReajustado = Valor_PrecoReajustado;

                // limpa preço reajustado
                ContratosCCEE_PrecoReajustadoMetodos precoMetodos = new ContratosCCEE_PrecoReajustadoMetodos();
                precoMetodos.ExcluirData(IDContratoCCEE, preco.Data);

                // adiciona preço reajustado
                precoMetodos.Salvar(preco);
            }
            catch (FormatException)
            {
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Erro na Data"
                };

            }

            // retorna
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Preços Reajustados - Excluir
        public ActionResult ExcluirPrecoReajustado(int IDPrecoReajustado)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // apaga o ano 
            ContratosCCEE_PrecoReajustadoMetodos precoMetodos = new ContratosCCEE_PrecoReajustadoMetodos();
            precoMetodos.Excluir(IDPrecoReajustado);

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


        //
        // SAZONALIZACAO
        //

        // GET: Obter Sazonalizações
        public JsonResult ObterSazonalizacoes(int IDContratoCCEE)
        {
            // sazonalização
            ContratosCCEE_SazonalizacaoMetodos sazoMetodos = new ContratosCCEE_SazonalizacaoMetodos();
            List<ContratosCCEE_SazonalizacaoDominio> sazonalizacoes = sazoMetodos.ListarPorId(IDContratoCCEE);

            // retorna o valor em JSON
            return Json(sazonalizacoes, JsonRequestBehavior.AllowGet);
        }

        // GET: Utilizar os Volumes na Sazonalização
        public JsonResult UtilizarVolume_Sazonalizacao(int IDContratoCCEE, int Montante_Tipo)
        {
            // sazonalizacao
            List<ContratosCCEE_SazonalizacaoDominio> sazonalizacoes = new List<ContratosCCEE_SazonalizacaoDominio>();

            // volume
            ContratosCCEE_VolumeMetodos volumeMetodos = new ContratosCCEE_VolumeMetodos();
            List<ContratosCCEE_VolumeDominio> volumes = volumeMetodos.ListarPorId(IDContratoCCEE, Montante_Tipo);

            // copiar volume
            if (volumes != null)
            {
                if (volumes.Count > 0)
                {
                    // verifica tipo
                    if (Montante_Tipo == 0)
                    {
                        // caso for Montante: volume é por ano

                        // percorre volumes
                        foreach (ContratosCCEE_VolumeDominio volume in volumes)
                        {
                            // sazo
                            ContratosCCEE_SazonalizacaoDominio sazo = new ContratosCCEE_SazonalizacaoDominio();

                            // IDContratoCCEE
                            sazo.IDContratoCCEE = IDContratoCCEE;

                            // ano
                            sazo.Ano = volume.Data.Year;

                            // converte MWm em MWh (meses)
                            sazo.Jan = Converte_MWm_MWh(1, sazo.Ano, volume.Contratado);
                            sazo.Fev = Converte_MWm_MWh(2, sazo.Ano, volume.Contratado);
                            sazo.Mar = Converte_MWm_MWh(3, sazo.Ano, volume.Contratado);
                            sazo.Abr = Converte_MWm_MWh(4, sazo.Ano, volume.Contratado);
                            sazo.Mai = Converte_MWm_MWh(5, sazo.Ano, volume.Contratado);
                            sazo.Jun = Converte_MWm_MWh(6, sazo.Ano, volume.Contratado);
                            sazo.Jul = Converte_MWm_MWh(7, sazo.Ano, volume.Contratado);
                            sazo.Ago = Converte_MWm_MWh(8, sazo.Ano, volume.Contratado);
                            sazo.Set = Converte_MWm_MWh(9, sazo.Ano, volume.Contratado);
                            sazo.Out = Converte_MWm_MWh(10, sazo.Ano, volume.Contratado);
                            sazo.Nov = Converte_MWm_MWh(11, sazo.Ano, volume.Contratado);
                            sazo.Dez = Converte_MWm_MWh(12, sazo.Ano, volume.Contratado);

                            // adiciona
                            sazonalizacoes.Add(sazo);
                        }
                    }
                    else
                    {
                        // caso for Sazonalização: volume é por mês

                        // ano atual
                        int ano_atual = 0;

                        // sazo
                        ContratosCCEE_SazonalizacaoDominio sazo = new ContratosCCEE_SazonalizacaoDominio();

                        // percorre volumes
                        foreach (ContratosCCEE_VolumeDominio volume in volumes)
                        {
                            // verifica se mudou ano
                            if (ano_atual != volume.Data.Year)
                            {
                                // verifica se não é o primeiro
                                if (ano_atual != 0)
                                {
                                    ContratosCCEE_SazonalizacaoDominio sazo_copia = new ContratosCCEE_SazonalizacaoDominio();
                                    sazo_copia.IDContratoCCEE = sazo.IDContratoCCEE;
                                    sazo_copia.Ano = sazo.Ano;
                                    sazo_copia.Jan = sazo.Jan;
                                    sazo_copia.Fev = sazo.Fev;
                                    sazo_copia.Mar = sazo.Mar;
                                    sazo_copia.Abr = sazo.Abr;
                                    sazo_copia.Mai = sazo.Mai;
                                    sazo_copia.Jun = sazo.Jun;
                                    sazo_copia.Jul = sazo.Jul;
                                    sazo_copia.Ago = sazo.Ago;
                                    sazo_copia.Set = sazo.Set;
                                    sazo_copia.Out = sazo.Out;
                                    sazo_copia.Nov = sazo.Nov;
                                    sazo_copia.Dez = sazo.Dez;

                                    // significa que já tem sazo para salvar de outro ano
                                    sazonalizacoes.Add(sazo_copia);
                                }

                                // ano atual
                                ano_atual = volume.Data.Year;

                                // IDContratoCCEE
                                sazo.IDContratoCCEE = IDContratoCCEE;

                                // ano
                                sazo.Ano = ano_atual;

                                sazo.Jan = 0.0; sazo.Fev = 0.0; sazo.Mar = 0.0; sazo.Abr = 0.0; sazo.Mai = 0.0; sazo.Jun = 0.0;
                                sazo.Jul = 0.0; sazo.Ago = 0.0; sazo.Set = 0.0; sazo.Out = 0.0; sazo.Nov = 0.0; sazo.Dez = 0.0;
                            }

                            // copia sazo para o mês
                            CopiaSazo_Mes(volume.Data.Month, ref sazo, volume.Contratado);
                        }

                        // verifica se não é o primeiro
                        if (ano_atual != 0)
                        {
                            ContratosCCEE_SazonalizacaoDominio sazo_copia = new ContratosCCEE_SazonalizacaoDominio();
                            sazo_copia.IDContratoCCEE = sazo.IDContratoCCEE;
                            sazo_copia.Ano = sazo.Ano;
                            sazo_copia.Jan = sazo.Jan;
                            sazo_copia.Fev = sazo.Fev;
                            sazo_copia.Mar = sazo.Mar;
                            sazo_copia.Abr = sazo.Abr;
                            sazo_copia.Mai = sazo.Mai;
                            sazo_copia.Jun = sazo.Jun;
                            sazo_copia.Jul = sazo.Jul;
                            sazo_copia.Ago = sazo.Ago;
                            sazo_copia.Set = sazo.Set;
                            sazo_copia.Out = sazo.Out;
                            sazo_copia.Nov = sazo.Nov;
                            sazo_copia.Dez = sazo.Dez;

                            // significa que já tem sazo para salvar de outro ano
                            sazonalizacoes.Add(sazo_copia);
                        }                                
                    }
                }
            }

            // limpa sazonalizacoes
            ContratosCCEE_SazonalizacaoMetodos sazoMetodos = new ContratosCCEE_SazonalizacaoMetodos();
            sazoMetodos.ExcluirContrato(IDContratoCCEE);

            // percorre sazonalizacoes e salva
            foreach (ContratosCCEE_SazonalizacaoDominio sazo in sazonalizacoes)
            {
                // adiciona sazo
                sazoMetodos.Adicionar(sazo);
            }

            // le novamente sazonalização do contrato para criar textos na estrutura
            sazonalizacoes.Clear();
            sazonalizacoes = sazoMetodos.ListarPorId(IDContratoCCEE);

            // retorna o valor em JSON
            return Json(sazonalizacoes, JsonRequestBehavior.AllowGet);
        }

        // converte MWm em MWh
        private double Converte_MWm_MWh(int mes, int ano, double montante)
        {
            // calcula número de horas do mês
            int num_horas = System.DateTime.DaysInMonth(ano, mes) * 24;

            // calcula contratado MWh do mês
            double contratado = montante * num_horas;

            // retorna sazonalizado do mês
            return (contratado);
        }

        // copia sazo para o mês
        private void CopiaSazo_Mes(int mes, ref ContratosCCEE_SazonalizacaoDominio sazo, double contratado)
        {

            // mes
            switch (mes)
            {
                case 1:     // janeiro
                    sazo.Jan = contratado;
                    break;

                case 2:     // fevereiro
                    sazo.Fev = contratado;
                    break;

                case 3:     // março
                    sazo.Mar = contratado;
                    break;

                case 4:     // abril
                    sazo.Abr = contratado;
                    break;

                case 5:     // maio
                    sazo.Mai = contratado;
                    break;

                case 6:     // junho
                    sazo.Jun = contratado;
                    break;

                case 7:     // julho
                    sazo.Jul = contratado;
                    break;

                case 8:     // agosto
                    sazo.Ago = contratado;
                    break;

                case 9:     // setembro
                    sazo.Set = contratado;
                    break;

                case 10:     // outubro
                    sazo.Out = contratado;
                    break;

                case 11:     // novembro
                    sazo.Nov = contratado;
                    break;

                case 12:     // dezembro
                    sazo.Dez = contratado;
                    break;
            }

            return;
        }

        // GET: Sazonalização - Salvar
        public JsonResult SalvarSazonalizacao(int IDContratoCCEE, int Ano, double Jan, double Fev, double Mar, double Abr, double Mai, double Jun, double Jul, double Ago, double Set, double Out, double Nov, double Dez)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // sazonalização
            ContratosCCEE_SazonalizacaoDominio sazo = new ContratosCCEE_SazonalizacaoDominio();

            sazo.IDContratoCCEE = IDContratoCCEE;
            sazo.Ano = Ano;
            sazo.Jan = Jan;
            sazo.Fev = Fev;
            sazo.Mar = Mar;
            sazo.Abr = Abr;
            sazo.Mai = Mai;
            sazo.Jun = Jun;
            sazo.Jul = Jul;
            sazo.Ago = Ago;
            sazo.Set = Set;
            sazo.Out = Out;
            sazo.Nov = Nov;
            sazo.Dez = Dez;

            // limpa sazonalização
            ContratosCCEE_SazonalizacaoMetodos sazoMetodos = new ContratosCCEE_SazonalizacaoMetodos();
            sazoMetodos.ExcluirAno(IDContratoCCEE, Ano);

            // adiciona sazo
            sazoMetodos.Adicionar(sazo);

            // retorna
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // GET: Sazonalização - Excluir
        public ActionResult ExcluirSazonalizacao(int IDContratoCCEE, int Ano)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // apaga o ano 
            ContratosCCEE_SazonalizacaoMetodos sazoMetodos = new ContratosCCEE_SazonalizacaoMetodos();
            sazoMetodos.ExcluirAno(IDContratoCCEE, Ano);

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        //
        // SALVAR ORDEM
        //

        // POST: ContratosCCEE Ordem - Salvar
        [HttpPost]
        public ActionResult ContratosCCEE_Ordem_Salvar(List<ContratosCCEE_Ordem> contratosOrdem)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // salva ContratosCCEE Ordem
            if (contratosOrdem != null)
            {
                // percorre lista e salva
                foreach (ContratosCCEE_Ordem contrato in contratosOrdem)
                {
                    // salva
                    ContratosCCEEMetodos contratosMetodos = new ContratosCCEEMetodos();
                    contratosMetodos.Alterar_Ordem_Considera(contrato.IDContratoCCEE, contrato.Ordem, contrato.Considera);
                }
            }

            // retorna status
            return Json(returnedData);
        }


        //
        // SALVAR e EXCLUIR
        //

        // POST: Contrato CCEE - Salvar
        [HttpPost]
        public ActionResult ContratoCCEE_Salvar(ContratosCCEEDominio contrato)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                IDContratoCCEE = contrato.IDContratoCCEE,
                erro = ""
            };

            // parse datas
            contrato.Vigencia_Inicio = DateTime.Parse(contrato.Vigencia_Inicio_Texto);
            contrato.Vigencia_Fim = DateTime.Parse(contrato.Vigencia_Fim_Texto);
            contrato.Base_Data = DateTime.Parse(contrato.Base_Data_Texto);
            contrato.DataLimiteSazo = DateTime.Parse(contrato.DataLimiteSazo_Texto);

            // salva contrato
            ContratosCCEEMetodos contratosMetodos = new ContratosCCEEMetodos();
            contratosMetodos.Salvar(contrato);

            // pego IDContratoCCEE novamente (pois pode ter sido insercao)
            ContratosCCEEDominio contr = contratosMetodos.ListarPorVigencia(contrato.IDEmpresa, contrato.Vigencia_Inicio, contrato.Vigencia_Fim);

            // verifica se existe
            if (contr != null)
            {
                // ok
                returnedData = new
                {
                    status = "OK",
                    IDContratoCCEE = contr.IDContratoCCEE,
                    erro = ""
                };


                // verifica se tem preços base salvo
                ContratosCCEE_PrecoBaseMetodos precoBaseMetodos = new ContratosCCEE_PrecoBaseMetodos();

                // não achou preço base
                bool achou = false;

                // verifica se flat
                if (contr.Base_Preco_Flat)
                {
                    // cria preços base vazios
                    ZerarPrecosBase(contr.IDContratoCCEE, contr.Vigencia_Inicio_Texto, contr.Vigencia_Fim_Texto);
                }
                else
                {
                    // cria preços base se não existirem
                    List<ContratosCCEE_PrecoBaseDominio> precos_base = precoBaseMetodos.ListarPorId(contr.IDContratoCCEE);

                    if (precos_base != null)
                    {
                        if (precos_base.Count > 0)
                        {
                            achou = true;
                        }
                    }

                    // verifica se não achou
                    if (!achou)
                    {
                        // cria preços base vazios
                        ZerarPrecosBase(contr.IDContratoCCEE, contr.Vigencia_Inicio_Texto, contr.Vigencia_Fim_Texto);
                    }
                }


                // verifica se tem volume salvo
                ContratosCCEE_VolumeMetodos volumeMetodos = new ContratosCCEE_VolumeMetodos();
                List<ContratosCCEE_VolumeDominio> volumes = volumeMetodos.ListarPorId(contr.IDContratoCCEE);

                // não achou volume
                achou = false;

                if (volumes != null)
                {
                    if (volumes.Count > 0)
                    {
                        achou = true;
                    }
                }

                // verifica se não achou
                if (!achou)
                {
                    // cria volumes vazios
                    ZerarVolumes(contr.IDContratoCCEE, contr.Montante_Tipo, contr.Vigencia_Inicio_Texto, contr.Vigencia_Fim_Texto);
                }
            }
            else
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    IDContratoCCEE = contrato.IDContratoCCEE,
                    erro = "Contrato não encontrado."
                };
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Contrato CCEE - Excluir
        public ActionResult ContratoCCEE_Excluir(int IDContratoCCEE)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // apaga o contrato
            ContratosCCEEMetodos contratosMetodos = new ContratosCCEEMetodos();
            contratosMetodos.Excluir(IDContratoCCEE);

            // apaga as unidades atendidas
            ContratosCCEE_UnidadesAtendidasMetodos unidadesMetodos = new ContratosCCEE_UnidadesAtendidasMetodos();
            unidadesMetodos.ExcluirContrato(IDContratoCCEE);

            // apaga os volumes
            ContratosCCEE_VolumeMetodos volumeMetodos = new ContratosCCEE_VolumeMetodos();
            volumeMetodos.ExcluirContrato(IDContratoCCEE);

            // apaga os preços base
            ContratosCCEE_PrecoBaseMetodos precoBaseMetodos = new ContratosCCEE_PrecoBaseMetodos();
            precoBaseMetodos.ExcluirContrato(IDContratoCCEE);

            // limpa sazonalizacoes
            ContratosCCEE_SazonalizacaoMetodos sazoMetodos = new ContratosCCEE_SazonalizacaoMetodos();
            sazoMetodos.ExcluirContrato(IDContratoCCEE);

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }


    }
}