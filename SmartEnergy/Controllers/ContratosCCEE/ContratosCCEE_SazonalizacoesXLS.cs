﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Web.Mvc;

using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class ContratosCCEEController
    {
        // GET: Sazonalizações XLS Download
        [HttpGet]
        public virtual ActionResult Sazonalizacoes_XLS_Download(string fileGuid, string fileName)
        {
            if (Session[fileGuid] != null)
            {
                byte[] data = Session[fileGuid] as byte[];
                return File(data, "application/vnd.ms-excel", fileName);
            }
            else
            {
                // erro
                return new EmptyResult();
            }
        }

        // GET: Download Sazonalizações
        public ActionResult DownloadFile_Sazonalizacoes(int IDContratoCCEE)
        {
            // Planilha Excel
            var workbook = new HSSFWorkbook();

            // nome do arquivo
            string nomeArquivo = string.Format("Sazonalizacoes_{0:000000}_{1:yyyyMMddHHmm}.xls", IDContratoCCEE, DateTime.Now);

            // gera planilha
            workbook = Sazonalizacoes_XLS(IDContratoCCEE);

            // gera identificador unico aonde o arquivo sera guardado
            string handle = Guid.NewGuid().ToString();

            using (MemoryStream memoryStream = new MemoryStream())
            {
                workbook.Write(memoryStream);
                memoryStream.Position = 0;
                Session[handle] = memoryStream.ToArray();
            }

            // retorna nome do arquivo
            var returnedData = new
            {
                FileGuid = handle,
                FileName = nomeArquivo
            };

            // relatorio para PDF
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }

        // Sazonalizações Planilha
        private HSSFWorkbook Sazonalizacoes_XLS(int IDContratoCCEE)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 4);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // PREÇOS
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Sazonalizações");

            // cabecalho
            string[] cabecalho = { "ID", "ID Contrato", "Ano", "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho", "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // le contrato
            ContratosCCEEMetodos contratosCCEE = new ContratosCCEEMetodos();
            ContratosCCEEDominio contrato = contratosCCEE.ListarPorId(IDContratoCCEE);

            // le sazonalizacoes
            ContratosCCEE_SazonalizacaoMetodos sazoMetodos = new ContratosCCEE_SazonalizacaoMetodos();
            List<ContratosCCEE_SazonalizacaoDominio>  sazonalizacoes = sazoMetodos.ListarPorId(IDContratoCCEE);

            // percorre sazonalizações
            if (sazonalizacoes != null && contrato != null)
            {
                foreach (ContratosCCEE_SazonalizacaoDominio sazo in sazonalizacoes)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // IDContratoCCEE
                    numeroCelulaXLS(row, 0, IDContratoCCEE, _intCellStyle);

                    // IDContrato
                    textoCelulaXLS(row, 1, contrato.Contrato_ID);

                    // Ano
                    numeroCelulaXLS(row, 2, sazo.Ano, _intCellStyle);

                    // Janeiro
                    numeroCelulaXLS(row, 3, sazo.Jan, _3CellStyle);

                    // Fevereiro
                    numeroCelulaXLS(row, 4, sazo.Fev, _3CellStyle);

                    // Março
                    numeroCelulaXLS(row, 5, sazo.Mar, _3CellStyle);

                    // Abril
                    numeroCelulaXLS(row, 6, sazo.Abr, _3CellStyle);

                    // Maio
                    numeroCelulaXLS(row, 7, sazo.Mai, _3CellStyle);

                    // Junho
                    numeroCelulaXLS(row, 8, sazo.Jun, _3CellStyle);

                    // Julho
                    numeroCelulaXLS(row, 9, sazo.Jul, _3CellStyle);

                    // Agosto
                    numeroCelulaXLS(row, 10, sazo.Ago, _3CellStyle);

                    // Setembro
                    numeroCelulaXLS(row, 11, sazo.Set, _3CellStyle);

                    // Outubro
                    numeroCelulaXLS(row, 12, sazo.Out, _3CellStyle);

                    // Novembro
                    numeroCelulaXLS(row, 13, sazo.Nov, _3CellStyle);

                    // Dezembro
                    numeroCelulaXLS(row, 14, sazo.Dez, _3CellStyle);

                    // proxima
                    rowIndex++;
                }
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
            {
                sheet.SetColumnWidth(i, 3500);
            }

            // retorna planilha
            return workbook;
        }
    }
}