﻿using System;
using System.IO;
using System.Web;
using System.Web.Mvc;

namespace SmartEnergy.Controllers
{
    public class FileUploadController : Controller
    {
        #region Actions

        /// <summary>
        /// Uploads the file.
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public virtual ActionResult UploadFile()
        {
            HttpPostedFileBase myFile = Request.Files["Logo"];
            bool isUploaded = false;
            string message = "Envio falhou";

            if (myFile != null && myFile.ContentLength != 0)
            {
                // diretório
                string pathForSaving = Server.MapPath("~/Logos");

                // verifica se arquivo imagem
                string extensao = Path.GetExtension(myFile.FileName).ToUpper();

                if (extensao == ".GIF" || extensao == ".JPG" || extensao == ".JPEG" || extensao == ".PNG" || extensao == ".BMP")
                {
                    if (this.CreateFolderIfNeeded(pathForSaving))
                    {
                        try
                        {
                            myFile.SaveAs(Path.Combine(pathForSaving, myFile.FileName));
                            isUploaded = true;
                            message = "Arquivo enviado com sucesso!";
                        }
                        catch (Exception ex)
                        {
                            message = string.Format("Envio falhou: {0}", ex.Message);
                        }
                    }
                }
                else
                {
                    message = "Formato não reconhecido";
                }
            }

            return Json(new { isUploaded = isUploaded, message = message }, "text/html");
        }

        [HttpPost]
        public virtual ActionResult UploadFileConsult()
        {
            HttpPostedFileBase myFile = Request.Files["LogoConsult"];
            bool isUploaded = false;
            string message = "Envio falhou";

            if (myFile != null && myFile.ContentLength != 0)
            {
                // diretório
                string pathForSaving = Server.MapPath("~/Logos");

                // verifica se arquivo imagem
                string extensao = Path.GetExtension(myFile.FileName).ToUpper();

                if (extensao == ".GIF" || extensao == ".JPG" || extensao == ".JPEG" || extensao == ".PNG" || extensao == ".BMP")
                {
                    if (this.CreateFolderIfNeeded(pathForSaving))
                    {
                        try
                        {
                            myFile.SaveAs(Path.Combine(pathForSaving, myFile.FileName));
                            isUploaded = true;
                            message = "Arquivo enviado com sucesso!";
                        }
                        catch (Exception ex)
                        {
                            message = string.Format("Envio falhou: {0}", ex.Message);
                        }
                    }
                }
                else
                {
                    message = "Formato não reconhecido";
                }
            }

            return Json(new { isUploaded = isUploaded, message = message }, "text/html");
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Creates the folder if needed.
        /// </summary>
        /// <param name="path">The path.</param>
        /// <returns></returns>
        private bool CreateFolderIfNeeded(string path)
        {
            bool result = true;
            if (!Directory.Exists(path))
            {
                try
                {
                    Directory.CreateDirectory(path);
                }
                catch (Exception)
                {
                    /*TODO: You must process this exception.*/
                    result = false;
                }
            }
            return result;
        }

        #endregion
    }
}