﻿using System;
using System.Runtime.InteropServices;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class DashboardController : Controller
    {
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Dashboard_ReativoExcedente", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern sbyte SmCalcDB_Dashboard_ReativoExcedente(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref DATAHORA pdatahora, sbyte navega, ref DASHBOARD_RTV_EXCEDENTE psuperv);


        private int DB_Atualizar_UFER(int IDDashboard, int IDMedicao, int Navegacao, string DataAtualTxt)
        {
            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("DB_Editar");
            ViewBag.Editar = editar_cookie;

            // le cookie datahora
            DateTime datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);

            if (DataAtualTxt != null)
            {
                datahora_cookie = DateTime.Parse(DataAtualTxt);
            }

            // verifica se atualizar apenas
            if (Navegacao == 0)
            {
                // forco setar data como ultimo registro
                datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);
            }


            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            DASHBOARD_RTV_EXCEDENTE superv = new DASHBOARD_RTV_EXCEDENTE();

            DATAHORA datahora = new DATAHORA();
            datahora.data.dia = (char)datahora_cookie.Day;
            datahora.data.mes = (char)datahora_cookie.Month;
            datahora.data.ano = (Int16)datahora_cookie.Year;
            datahora.hora.hora = (char)1;
            datahora.hora.min = (char)0;
            datahora.hora.seg = (char)0;

            // preenche interface
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = medicao.IDGateway;

            // funcao de supervisao
            int retorno;

            // calcula valores
            retorno = SmCalcDB_Dashboard_ReativoExcedente((char)0, ref config_interface, ref datahora, (sbyte)Navegacao, ref superv);
            FuncoesDashboard.DivisaoUnidade_DASHBOARD_UFER(ref superv, medicao.IDTipoUnidadePotencia);

            // erro
            if (retorno != 0)
            {
                // erro
                return (retorno);
            }

            // converte data e hora
            DateTime DataAtual = new DateTime((int)superv.Data.data.ano,
                                              (int)superv.Data.data.mes,
                                              (int)superv.Data.data.dia,
                                              1, 0, 0);

            // nome da medicao
            ViewBag.NomeMedicao = medicao.Nome;

            ViewBag.Data = string.Format("{0:Y}", DataAtual);

            // UFER
            if (superv.flag_reg_mensal == 1 || superv.flag_reg_mensal == 2)
            {
                ViewBag.UFER_TotalP = "---";
                ViewBag.UFER_TotalFP = "---";
                ViewBag.UFER_TotalFPI = "---";
                ViewBag.UFER_TotalFPC = "---";
                ViewBag.UFER_Total = "---";
            }
            else
            {
                ViewBag.UFER_TotalP = Funcoes_SmartEnergy.FormatValor(superv.RtvExcedenteTotalP);
                ViewBag.UFER_TotalFP = Funcoes_SmartEnergy.FormatValor(superv.RtvExcedenteTotalFPI + superv.RtvExcedenteTotalFPC);
                ViewBag.UFER_TotalFPI = Funcoes_SmartEnergy.FormatValor(superv.RtvExcedenteTotalFPI);
                ViewBag.UFER_TotalFPC = Funcoes_SmartEnergy.FormatValor(superv.RtvExcedenteTotalFPC);
                ViewBag.UFER_Total = Funcoes_SmartEnergy.FormatValor(superv.RtvExcedenteTotal);
            }

            // grafico
            var UFER = new double[40];
            var UFER_P = new double[40];
            var UFER_FPI = new double[40];
            var UFER_FPC = new double[40];
            var Status = new int[40];
            var Dias = new string[40];

            double UFER_max = 0.0;

            // valores
            DateTime strData = new DateTime((int)superv.RtvExcedenteDataHora[0].data.ano,
                                            (int)superv.RtvExcedenteDataHora[0].data.mes,
                                            (int)superv.RtvExcedenteDataHora[0].data.dia,
                                            1, 0, 0);

            int i = 0;

            for (i = 0; i < superv.NumDias; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proximo dia
                strData = strData.AddDays(1);

                // copia
                UFER_P[i] = superv.RtvExcedenteP[i];
                UFER_FPI[i] = superv.RtvExcedenteFPI[i];
                UFER_FPC[i] = superv.RtvExcedenteFPC[i];
                UFER[i] = superv.RtvExcedenteP[i] + superv.RtvExcedenteFPI[i] + superv.RtvExcedenteFPC[i];

                // status
                if (superv.flag_reg_mensal == 1 || superv.flag_reg_mensal == 2)
                {
                    Status[i] = 1;
                }
                else
                {
                    Status[i] = 0;
                }

                // verifica UFER maximo
                if (UFER[i] > UFER_max)
                    UFER_max = UFER[i];
            }

            UFER_max = UFER_max * 1.1;

            if (UFER_max == 0.0)
            {
                UFER_max = 10.0;
            }

            ViewBag.UFER_max = UFER_max;

            ViewBag.UFER = UFER;
            ViewBag.UFER_P = UFER_P;
            ViewBag.UFER_FPI = UFER_FPI;
            ViewBag.UFER_FPC = UFER_FPC;
            ViewBag.Status = Status;
            ViewBag.Dias = Dias;
            ViewBag.NumDias = superv.NumDias;

            // data atual
            string IDDashboardTxt = string.Format("dt{0}", IDDashboard);
            ViewBag.IDDashboardTxt = IDDashboardTxt;
            ViewBag.DataAtual = string.Format("{0:d} {1:HH:mm}", DataAtual, DataAtual);

            // ok
            return (0);
        }
    }
}