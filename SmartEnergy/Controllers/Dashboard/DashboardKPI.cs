﻿using System;
using System.Runtime.InteropServices;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class DashboardController : Controller
    {
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Dashboard_KpiEconomiaDiario", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern sbyte SmCalcDB_Dashboard_KpiEconomiaDiario(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref DATAHOR<PERSON> pdatahora, sbyte navega, ref DASHBOARD_KPI_ECONOMIA_DIARIO psuperv);

        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Dashboard_KpiEconomiaMensal", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern sbyte SmCalcDB_Dashboard_KpiEconomiaMensal(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref DATAHORA pdatahora, sbyte navega, ref DASHBOARD_KPI_ECONOMIA_MENSAL psuperv);

        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Energia_RelatDiario", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern char SmCalcDB_Energia_RelatDiario(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, char tipo_relatorio, char simula, ref DATAHORA pdata_equipo, ref RELAT_DIARIO prelatorio, ref RELAT_DIARIO_ANALISE panalise, ref RELAT_DIARIO prelatorio_sim, ref RELAT_DIARIO_ANALISE panalise_sim);


        private int DB_Atualizar_KPI_EconomiaDiaria(int IDDashboard, int IDMedicao, int Navegacao, string DataAtualTxt)
        {
            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("DB_Editar");
            ViewBag.Editar = editar_cookie;

            // le cookie datahora
            DateTime datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);

            if (DataAtualTxt != null)
            {
                datahora_cookie = DateTime.Parse(DataAtualTxt);
            }

            // verifica se atualizar apenas
            if (Navegacao == 0)
            {
                // forco setar data como ultimo registro
                datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);
            }


            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            DASHBOARD_KPI_ECONOMIA_DIARIO superv = new DASHBOARD_KPI_ECONOMIA_DIARIO();

            DATAHORA datahora = new DATAHORA();
            datahora.data.dia = (char)datahora_cookie.Day;
            datahora.data.mes = (char)datahora_cookie.Month;
            datahora.data.ano = (Int16)datahora_cookie.Year;
            datahora.hora.hora = (char)1;
            datahora.hora.min = (char)0;
            datahora.hora.seg = (char)0;

            // preenche interface
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = medicao.IDGateway;

            // funcao de supervisao
            int retorno;

            // calcula valores
            retorno = SmCalcDB_Dashboard_KpiEconomiaDiario((char)0, ref config_interface, ref datahora, (sbyte)Navegacao, ref superv);
            FuncoesDashboard.DivisaoUnidade_DASHBOARD_KPI_ECONOMIA_DIARIO(ref superv, medicao.IDTipoUnidadePotencia);

            // erro
            if (retorno != 0)
            {
                // erro
                return (retorno);
            }

            // converte data e hora
            DateTime DataAtual = new DateTime((int)superv.Data.data.ano,
                                              (int)superv.Data.data.mes,
                                              (int)superv.Data.data.dia,
                                              1, 0, 0);

            // nome da medicao
            ViewBag.NomeMedicao = medicao.Nome;

            ViewBag.Data = string.Format("{0:d}", DataAtual);

            // grafico
            var Consumo = new double[24];
            var Periodo = new int[24];
            var Referencia = new double[24];
            var EconomiaConsumo = new double[24];
            var EconomiaValor = new double[24];
            var EconomiaPercentual = new double[24];
            var Dias = new string[24];
            var DatasN = new DateTime[24];
            var Horas = new string[24];

            double Cons_min = 0.0;
            double Cons_max = 0.0;

            // valores
            DateTime strData = new DateTime((int)superv.DataHora[0].data.ano,
                                            (int)superv.DataHora[0].data.mes,
                                            (int)superv.DataHora[0].data.dia,
                                            1, 0, 0);

            int i = 0;

            for (i = 0; i < 24; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Horas[i] = strData.ToString("HH:mm");

                // proxima hora
                strData = strData.AddHours(1);

                // copia
                Consumo[i] = superv.Consumo[i];
                Periodo[i] = (int)superv.Periodo[i];
                Referencia[i] = superv.Referencia;
                EconomiaConsumo[i] = superv.EconomiaConsumo[i];
                EconomiaValor[i] = superv.EconomiaValor[i];
                EconomiaPercentual[i] = superv.EconomiaPercentual[i];

                // verifica consumo minimo
                if (Cons_min == 0.0)
                    Cons_min = Consumo[i];

                if (Consumo[i] < Cons_min && Periodo[i] < 3)
                    Cons_min = Consumo[i];

                // verifica consumo maximo
                if (Consumo[i] > Cons_max)
                    Cons_max = Consumo[i];

                if (Referencia[i] > Cons_max)
                    Cons_max = Referencia[i];
            }

            // minimo e maximo
            if (Cons_min > 0.0)
                Cons_min = Cons_min - (Cons_min * 0.05);
            else
                Cons_min = Cons_min * 1.05;

            Cons_max = Cons_max * 1.05;

            if (Cons_max == 0.0)
            {
                Cons_max = 10.0;
            }

            ViewBag.Cons_min = Cons_min;
            ViewBag.Cons_max = Cons_max;

            ViewBag.Consumo = Consumo;
            ViewBag.Periodo = Periodo;
            ViewBag.Referencia = Referencia;
            ViewBag.EconomiaConsumo = EconomiaConsumo;
            ViewBag.EconomiaValor = EconomiaValor;
            ViewBag.EconomiaPercentual = EconomiaPercentual;
            ViewBag.Dias = Dias;

            // consumo
            ViewBag.ConsTotal = string.Format("{0:#,##0}", superv.ConsumoTotal);
            ViewBag.ConsTotalN = superv.ConsumoTotal;

            // economia
            ViewBag.EconomiaConsTotal = string.Format("{0:#,##0}", superv.EconomiaTotal);
            ViewBag.EconomiaConsTotalN = superv.EconomiaTotal;

            // porc
            ViewBag.EconomiaPorcTotal = string.Format("{0:#,##0.0}", superv.EconomiaTotal_P);
            ViewBag.EconomiaPorcTotalN = superv.EconomiaTotal_P;

            // economia
            ViewBag.EconomiaTotal = string.Format("{0:C}", superv.EconomiaTotal_V);
            ViewBag.EconomiaTotalN = superv.EconomiaTotal_V;

            // data atual
            string IDDashboardTxt = string.Format("dt{0}", IDDashboard);
            ViewBag.IDDashboardTxt = IDDashboardTxt;
            ViewBag.DataAtual = string.Format("{0:d} {1:HH:mm}", DataAtual, DataAtual);

            // ok
            return (0);
        }


        private int DB_Atualizar_KPI_EconomiaMensal(int IDDashboard, int IDMedicao, int Navegacao, string DataAtualTxt)
        {
            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("DB_Editar");
            ViewBag.Editar = editar_cookie;

            // le cookie datahora
            DateTime datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);

            if (DataAtualTxt != null)
            {
                datahora_cookie = DateTime.Parse(DataAtualTxt);
            }

            // verifica se atualizar apenas
            if (Navegacao == 0)
            {
                // forco setar data como ultimo registro
                datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);
            }


            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            DASHBOARD_KPI_ECONOMIA_MENSAL superv = new DASHBOARD_KPI_ECONOMIA_MENSAL();

            DATAHORA datahora = new DATAHORA();
            datahora.data.dia = (char)datahora_cookie.Day;
            datahora.data.mes = (char)datahora_cookie.Month;
            datahora.data.ano = (Int16)datahora_cookie.Year;
            datahora.hora.hora = (char)1;
            datahora.hora.min = (char)0;
            datahora.hora.seg = (char)0;

            // preenche interface
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = medicao.IDGateway;

            // funcao de supervisao
            int retorno;

            // calcula valores
            retorno = SmCalcDB_Dashboard_KpiEconomiaMensal((char)0, ref config_interface, ref datahora, (sbyte)Navegacao, ref superv);
            FuncoesDashboard.DivisaoUnidade_DASHBOARD_KPI_ECONOMIA_MENSAL(ref superv, medicao.IDTipoUnidadePotencia);

            // erro
            if (retorno != 0)
            {
                // erro
                return (retorno);
            }

            // converte data e hora
            DateTime DataAtual = new DateTime((int)superv.Data.data.ano,
                                              (int)superv.Data.data.mes,
                                              (int)superv.Data.data.dia,
                                              1, 0, 0);

            // nome da medicao
            ViewBag.NomeMedicao = medicao.Nome;

            ViewBag.Data = string.Format("{0:Y}", DataAtual);

            // grafico
            var Consumo = new double[31];
            var Referencia = new double[31];
            var EconomiaConsumo = new double[31];
            var EconomiaValor = new double[31];
            var EconomiaPercentual = new double[31];
            var Status = new int[31];
            var Dias = new string[31];

            double Cons_max = 0.0;

            // valores
            DateTime strData = new DateTime((int)superv.DataHora[0].data.ano,
                                            (int)superv.DataHora[0].data.mes,
                                            (int)superv.DataHora[0].data.dia,
                                            1, 0, 0);

            int i = 0;

            for (i = 0; i < superv.NumDias; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proximo dia
                strData = strData.AddDays(1);

                // copia
                Consumo[i] = superv.Consumo[i];
                Referencia[i] = superv.Referencia;
                EconomiaConsumo[i] = superv.EconomiaConsumo[i];
                EconomiaValor[i] = superv.EconomiaValor[i];
                EconomiaPercentual[i] = superv.EconomiaPercentual[i];

                // status (nao utiliza o retorno de erro)
                if (superv.flag_registros > 10)
                {
                    Status[i] = 1;
                }
                else
                {
                    Status[i] = 0;
                }

                // verifica consumo maximo
                if (Consumo[i] > Cons_max)
                    Cons_max = Consumo[i];

                if (Referencia[i] > Cons_max)
                    Cons_max = Referencia[i];
            }

            Cons_max = Cons_max * 1.1;

            if (Cons_max == 0.0)
            {
                Cons_max = 10.0;
            }

            ViewBag.Cons_max = Cons_max;

            ViewBag.Consumo = Consumo;
            ViewBag.Referencia = Referencia;
            ViewBag.EconomiaConsumo = EconomiaConsumo;
            ViewBag.EconomiaValor = EconomiaValor;
            ViewBag.EconomiaPercentual = EconomiaPercentual;
            ViewBag.Status = Status;
            ViewBag.Dias = Dias;
            ViewBag.NumDias = superv.NumDias;

            // consumo
            ViewBag.ConsTotal = string.Format("{0:#,##0}", superv.ConsumoTotal);
            ViewBag.ConsTotalN = superv.ConsumoTotal;

            // economia
            ViewBag.EconomiaConsTotal = string.Format("{0:#,##0}", superv.EconomiaTotal);
            ViewBag.EconomiaConsTotalN = superv.EconomiaTotal;

            // porc
            ViewBag.EconomiaPorcTotal = string.Format("{0:#,##0.0}", superv.EconomiaTotal_P);
            ViewBag.EconomiaPorcTotalN = superv.EconomiaTotal_P;

            // economia
            ViewBag.EconomiaTotal = string.Format("{0:C}", superv.EconomiaTotal_V);
            ViewBag.EconomiaTotalN = superv.EconomiaTotal_V;

            // data atual
            string IDDashboardTxt = string.Format("dt{0}", IDDashboard);
            ViewBag.IDDashboardTxt = IDDashboardTxt;
            ViewBag.DataAtual = string.Format("{0:d} {1:HH:mm}", DataAtual, DataAtual);

            // ok
            return (0);
        }


        private int DB_Atualizar_KPI_PotenciaInstalada(int IDDashboard, int IDMedicao, int Navegacao, string DataAtualTxt)
        {
            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("DB_Editar");
            ViewBag.Editar = editar_cookie;

            // le cookie datahora
            DateTime datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);

            if (DataAtualTxt != null)
            {
                datahora_cookie = DateTime.Parse(DataAtualTxt);
            }

            // forco setar data como ultimo registro
            datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);

            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            DASHBOARD_DEMANDA superv = new DASHBOARD_DEMANDA();

            DATAHORA datahora = new DATAHORA();
            datahora.data.dia = (char)datahora_cookie.Day;
            datahora.data.mes = (char)datahora_cookie.Month;
            datahora.data.ano = (Int16)datahora_cookie.Year;
            datahora.hora.hora = (char)1;
            datahora.hora.min = (char)0;
            datahora.hora.seg = (char)0;

            // preenche interface
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = medicao.IDGateway;

            // funcao de supervisao
            sbyte retorno;

            // calcula valores
            retorno = SmCalcDB_Dashboard_Demanda(0, ref config_interface, ref datahora, (sbyte)Navegacao, ref superv);
            FuncoesDashboard.DivisaoUnidade_DASHBOARD_DEMANDA(ref superv, medicao.IDTipoUnidadePotencia);

            // erro
            if (retorno != 0)
            {
                // erro
                return (retorno);
            }

            // converte data e hora
            DateTime DataAtual = new DateTime((int)superv.Data.data.ano,
                                              (int)superv.Data.data.mes,
                                              (int)superv.Data.data.dia,
                                              1, 0, 0);

            // nome da medicao
            ViewBag.NomeMedicao = medicao.Nome;

            // grafico
            var Demanda = new double[98];
            var Periodo = new double[98];
            var PotenciaInstalada = new double[98];
            var Reducao = new double[98];
            var Dias = new string[98];
            var DiasN = new DateTime[98];

            double Dem_min_grafico = 0.0;
            double Dem_max_grafico = 0.0;

            double Dem_Min = 0.0;
            double Dem_MinPeriodo = 3;
            DateTime Dem_MinDatahora = new DateTime((int)superv.DataHora[0].data.ano,
                                            (int)superv.DataHora[0].data.mes,
                                            (int)superv.DataHora[0].data.dia,
                                            (int)superv.DataHora[0].hora.hora,
                                            (int)superv.DataHora[0].hora.min, 0);

            // valores
            DateTime strData = new DateTime((int)superv.DataHora[0].data.ano,
                                            (int)superv.DataHora[0].data.mes,
                                            (int)superv.DataHora[0].data.dia,
                                            (int)superv.DataHora[0].hora.hora,
                                            (int)superv.DataHora[0].hora.min, 0);

            // volta 15min para a primeira barra
            strData = strData.AddMinutes(-15);

            int i = 0;
            int j = 0;

            for (i = 0; i < 98; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DiasN[i] = strData;

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    Demanda[i] = superv.Demanda[0];
                    Periodo[i] = 1;
                    PotenciaInstalada[i] = medicao.KPI_PotenciaInstalada;
                    Reducao[i] = 0.0;
                }

                // verifica se ultima barra
                if (i == 97)
                {
                    // zera
                    Demanda[i] = superv.Demanda[95];
                    Periodo[i] = 1;
                    PotenciaInstalada[i] = medicao.KPI_PotenciaInstalada;
                    Reducao[i] = 0.0;
                }

                if (i >= 1 && i <= 96)
                {
                    // copia
                    j = i - 1;

                    Demanda[i] = superv.Demanda[j];
                    Periodo[i] = superv.Periodo[j];
                    PotenciaInstalada[i] = medicao.KPI_PotenciaInstalada;

                    // demanda minima
                    if( Dem_Min == 0.0 )
                    {
                        Dem_Min = Demanda[i];
                    }

                    if( Demanda[i] > 0.0 && Demanda[i] < Dem_Min)
                    {
                        Dem_Min = Demanda[i];
                        Dem_MinPeriodo = Periodo[i];
                        Dem_MinDatahora = strData;
                    }

                    // reducao
                    if (medicao.KPI_PotenciaInstalada > 0.0)
                    {
                        Reducao[i] = ((medicao.KPI_PotenciaInstalada - Demanda[i]) / medicao.KPI_PotenciaInstalada) * 100.0;
                    }
                    else
                    {
                        Reducao[i] = 0.0;
                    }

                    // verifica demanda minima
                    if (Dem_min_grafico == 0.0)
                        Dem_min_grafico = Demanda[i];

                    if (Demanda[i] < Dem_min_grafico && Periodo[i] < 3)
                        Dem_min_grafico = Demanda[i];

                    // verifica demanda maxima
                    if (Demanda[i] > Dem_max_grafico)
                        Dem_max_grafico = Demanda[i];

                    if (PotenciaInstalada[i] > Dem_max_grafico)
                        Dem_max_grafico = PotenciaInstalada[i];
                }

                // proximo quinze minutos
                strData = strData.AddMinutes(15);
            }

            // minimo e maximo
            if (Dem_min_grafico > 0.0)
                Dem_min_grafico = Dem_min_grafico - (Dem_min_grafico * 0.05);
            else
                Dem_min_grafico = Dem_min_grafico * 1.05;

            Dem_max_grafico = Dem_max_grafico * 1.05;

            if (Dem_max_grafico < 1.0)
            {
                Dem_max_grafico = 1.0;
            }

            ViewBag.DemMinGrafico = Dem_min_grafico;
            ViewBag.DemMaxGrafico = Dem_max_grafico;

            ViewBag.Demanda = Demanda;
            ViewBag.Periodo = Periodo;
            ViewBag.PotenciaInstalada = PotenciaInstalada;
            ViewBag.Reducao = Reducao;
            ViewBag.Dias = Dias;


            // procura ultima demanda
            double Dem_Ult = 0.0;
            double Dem_UltPeriodo = 3;
            DateTime Dem_UltDatahora = new DateTime((int)superv.DataHora[0].data.ano,
                                            (int)superv.DataHora[0].data.mes,
                                            (int)superv.DataHora[0].data.dia,
                                            (int)superv.DataHora[0].hora.hora,
                                            (int)superv.DataHora[0].hora.min, 0);

            double Reducao_Ult = 0.0;

            for (i = 96; i >= 1; i--)
            {
                // verifica se eh registro inexistente
                if (Periodo[i] != 3)
                {
                    // encontrou ultima
                    Dem_Ult = Demanda[i];
                    Dem_UltPeriodo = Periodo[i];
                    Dem_UltDatahora = DiasN[i];

                    break;
                }
            }

            // reducao
            if( medicao.KPI_PotenciaInstalada > 0.0 )
            {
                Reducao_Ult = ((medicao.KPI_PotenciaInstalada - Dem_Ult) / medicao.KPI_PotenciaInstalada) * 100.0;
            }

            // demanda
            if (superv.flag_reg_diario == 1 || superv.flag_reg_diario == 2)
            {
                ViewBag.Dem_Ult = "---";
                ViewBag.Dem_UltPeriodo = 3;

                ViewBag.Reducao_Ult = "---";
            }
            else
            {
                ViewBag.Dem_Ult = string.Format("{0:#,##0.0}", Dem_Ult);
                ViewBag.Dem_UltPeriodo = Dem_UltPeriodo;

                ViewBag.Reducao_Ult = string.Format("{0:#,##0.0}", Reducao_Ult);
            }
            ViewBag.Dem_UltDatahora = string.Format("{0:d} {1:HH:mm}", Dem_UltDatahora, Dem_UltDatahora);

            // datahoraAtual do dashboard
            if (Dem_UltDatahora.Year == 2000)
            {
                ViewBag.DataHoraAtual = string.Format("{0:d}", DataAtual);
            }
            else
            {
                ViewBag.DataHoraAtual = string.Format("{0:d} {1:HH:mm}", Dem_UltDatahora, Dem_UltDatahora);
            }

            // data atual
            string IDDashboardTxt = string.Format("dt{0}", IDDashboard);
            ViewBag.IDDashboardTxt = IDDashboardTxt;
            ViewBag.DataAtual = string.Format("{0:d} {1:HH:mm}", DataAtual, DataAtual);

            // ok
            return (0);
        }
    }
}