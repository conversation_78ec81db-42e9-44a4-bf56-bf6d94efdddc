﻿using System;
using System.Runtime.InteropServices;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class DashboardController : Controller
    {
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Dashboard_EA", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern sbyte SmCalcDB_Dashboard_EA(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref DATAHORA pdatahora, sbyte navega, ref DASHBOARD_EA psuperv);


        private int DB_Atualizar_EA(int IDDashboard, int IDMedicao, int Navegacao, string DataAtualTxt)
        {
            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("DB_Editar");
            ViewBag.Editar = editar_cookie;

            // le cookie datahora
            DateTime datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);

            if (DataAtualTxt != null)
            {
                datahora_cookie = DateTime.Parse(DataAtualTxt);
            }

            // verifica se atualizar apenas
            if (Navegacao == 0)
            {
                // forco setar data como ultimo registro
                datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);
            }


            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            DASHBOARD_EA superv = new DASHBOARD_EA();

            DATAHORA datahora = new DATAHORA();
            datahora.data.dia = (char)datahora_cookie.Day;
            datahora.data.mes = (char)datahora_cookie.Month;
            datahora.data.ano = (Int16)datahora_cookie.Year;
            datahora.hora.hora = (char)1;
            datahora.hora.min = (char)0;
            datahora.hora.seg = (char)0;

            // preenche interface
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = medicao.IDGateway;

            // funcao de supervisao
            int retorno;

            // calcula valores
            retorno = SmCalcDB_Dashboard_EA((char)0, ref config_interface, ref datahora, (sbyte)Navegacao, ref superv);

            // erro
            if (retorno != 0)
            {
                // erro
                return (retorno);
            }

            // converte data e hora
            DateTime DataAtual = new DateTime((int)superv.Data.data.ano,
                                              (int)superv.Data.data.mes,
                                              (int)superv.Data.data.dia,
                                              1, 0, 0);

            DateTime Min_DataHoraDia = new DateTime((int)superv.Min_DataHoraDia.data.ano,
                                                   (int)superv.Min_DataHoraDia.data.mes,
                                                   (int)superv.Min_DataHoraDia.data.dia,
                                                   (int)superv.Min_DataHoraDia.hora.hora,
                                                   (int)superv.Min_DataHoraDia.hora.min, 0);

            DateTime Max_DataHoraDia = new DateTime((int)superv.Max_DataHoraDia.data.ano,
                                                   (int)superv.Max_DataHoraDia.data.mes,
                                                   (int)superv.Max_DataHoraDia.data.dia,
                                                   (int)superv.Max_DataHoraDia.hora.hora,
                                                   (int)superv.Max_DataHoraDia.hora.min, 0);

            // nome da medicao
            ViewBag.NomeMedicao = medicao.Nome;

            // entrada analogica
            if (superv.flag_reg_diario == 1 || superv.flag_reg_diario == 2)
            {
                ViewBag.Media_Dia = "---";
            }
            else
            {
                ViewBag.Media_Dia = string.Format("{0:0.00}", superv.Media_Dia);
            }
            ViewBag.Data_Dia = string.Format("{0:d}", DataAtual);

            if (Min_DataHoraDia.Year == 2000)
            {
                ViewBag.Min_Dia = "---";
                ViewBag.Min_DataHoraDia = "--/--/----";
            }
            else
            {
                ViewBag.Min_Dia = string.Format("{0:0.00}", superv.Min_Dia);
                ViewBag.Min_DataHoraDia = string.Format("{0:d} {1:HH:mm}", Min_DataHoraDia, Min_DataHoraDia);
            }

            if (Min_DataHoraDia.Year == 2000)
            {
                ViewBag.Max_Dia = "---";
                ViewBag.Max_DataHoraDia = "--/--/----";
            }
            else
            {
                ViewBag.Max_Dia = string.Format("{0:0.00}", superv.Max_Dia);
                ViewBag.Max_DataHoraDia = string.Format("{0:d} {1:HH:mm}", Max_DataHoraDia, Max_DataHoraDia);
            }

            ViewBag.NomeGrandeza = medicao.NomeGrandeza;
            ViewBag.UnidadeGrandeza = medicao.UnidadeGrandeza;

            // grafico
            var Media = new double[26];
            var Minima = new double[26];
            var Maxima = new double[26];
            var Status = new int[26];
            var Dias = new string[26];

            double Valor_min = 0.0;
            double Valor_max = 0.0;

            // valores
            DateTime strData = new DateTime((int)superv.DataHora[0].data.ano,
                                            (int)superv.DataHora[0].data.mes,
                                            (int)superv.DataHora[0].data.dia,
                                            (int)superv.DataHora[0].hora.hora,
                                            (int)superv.DataHora[0].hora.min, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proxima hora
                strData = strData.AddHours(1);

                // status
                if (superv.flag_reg_diario == 1 || superv.flag_reg_diario == 2)
                {
                    Status[i] = 1;
                }
                else
                {
                    Status[i] = 0;
                }

                // verifica se primeira barra
                if (i == 0)
                {
                    // copia do primeiro real
                    Media[0] = superv.Media[0];
                    Minima[0] = superv.Minima[0];
                    Maxima[0] = superv.Maxima[0];

                    // minimo e maximo
                    Valor_min = Minima[0];
                    Valor_max = Maxima[0];
                }

                // verifica se ultima barra
                if (i == 25)
                {
                    // copia do ultimo real
                    Media[25] = superv.Media[23];
                    Minima[25] = superv.Minima[23];
                    Maxima[25] = superv.Maxima[23];
                }

                // restante
                if (i > 0 && i < 25)
                {
                    // copia
                    j = i - 1;

                    // copia
                    Media[i] = superv.Media[j];
                    Minima[i] = superv.Minima[j];
                    Maxima[i] = superv.Maxima[j];

                    // verifica valor minimo
                    if (Minima[i] < Valor_min)
                        Valor_min = Minima[i];

                    // verifica valor maximo
                    if (Maxima[i] > Valor_max)
                        Valor_max = Maxima[i];
                }
            }

            // minimo e maximo
            Valor_max = Valor_max * 1.1;

            if (Valor_min > 0.0)
                Valor_min = Valor_min - (Valor_min * 0.1);
            else
                Valor_min = Valor_min * 1.1;

            ViewBag.Valor_min = Valor_min;
            ViewBag.Valor_max = Valor_max;

            ViewBag.Media = Media;
            ViewBag.Minima = Minima;
            ViewBag.Maxima = Maxima;
            ViewBag.Status = Status;
            ViewBag.Dias = Dias;

            // data atual
            string IDDashboardTxt = string.Format("dt{0}", IDDashboard);
            ViewBag.IDDashboardTxt = IDDashboardTxt;
            ViewBag.DataAtual = string.Format("{0:d} {1:HH:mm}", DataAtual, DataAtual);

            // ok
            return (0);
        }
    }
}