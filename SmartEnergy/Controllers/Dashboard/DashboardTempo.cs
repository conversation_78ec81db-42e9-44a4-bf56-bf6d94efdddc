﻿using System;
using System.Web.Mvc;

using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class DashboardController : Controller
    {

        private int DB_Atualizar_Tempo(int IDMedicao)
        {
            // le configuracao medicao
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("DB_Editar");
            ViewBag.Editar = editar_cookie;

            // le configuracao gateway
            GatewaysMetodos gatewaysMetodos = new GatewaysMetodos();
            GatewaysDominio gateway = gatewaysMetodos.ListarPorId(medicao.IDGateway);

            // le configuracao empresa
            EmpresasMetodos empresaMetodos = new EmpresasMetodos();
            EmpresasDominio empresa = empresaMetodos.ListarPorId(gateway.IDEmpresa);

            // nome da medicao
            ViewBag.NomeMedicao = medicao.Nome;

            // verifica se Brasil
            string nome_cidade = empresa.NomeCidade;

            if (empresa.IDPais == 1)
            {
                // le tipos Cidade
                CidadeEstadoPaisMetodos cidadeMetodos = new CidadeEstadoPaisMetodos();
                CidadesDominio cidade = cidadeMetodos.CidadePorId(empresa.IDCidade);

                nome_cidade = cidade.Nome;
            }

            // cidade
            ViewBag.Cidade = nome_cidade;

            // converte data e hora
            DateTime Datahora = DateTime.Now;

            // meteorologia
            PrevisaoWeatherMetodos previsaoMetodos = new PrevisaoWeatherMetodos();
            PrevisaoWeatherDominio previsao = previsaoMetodos.ListarPorIDCidade(empresa.IDCidade);
            ViewBag.previsao = previsao;

            // icones
            int icon_hoje = 0;
            int icon_semana0 = 0;
            int icon_semana1 = 0;
            int icon_semana2 = 0;
            int icon_semana3 = 0;

            if (previsao != null)
            {
                // data e hora
                Datahora = previsao.DataHora_Atual;

                // icones
                icon_hoje = previsao.Tempo_Cod_Atual;
                icon_semana0 = previsao.Tempo_Cod_0;
                icon_semana1 = previsao.Tempo_Cod_1;
                icon_semana2 = previsao.Tempo_Cod_2;
                icon_semana3 = previsao.Tempo_Cod_3;
            }

            // icones
            ViewBag.icon_hoje = icon_hoje;
            ViewBag.icon_semana0 = icon_semana0;
            ViewBag.icon_semana1 = icon_semana1;
            ViewBag.icon_semana2 = icon_semana2;
            ViewBag.icon_semana3 = icon_semana3;

            // data e hora
            ViewBag.Datahora = string.Format("{0:d} {1:HH:mm}", Datahora, Datahora);

            // ok
            return (0);
        }
    }
}