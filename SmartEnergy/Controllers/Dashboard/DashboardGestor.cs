﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class DashboardController
    {
        // GET: Dashboard Gestor - Grupos de Painéis
        public ActionResult DB_Gestor_GruposPaineis()
        {
            // cliente nao selecionado
            CookieStore.SalvaCookie_Int("_IDCliente", -10);
            CookieStore.SalvaCookie_String("Nome_GrupoUnidades", "Grupo de Unidades");
            CookieStore.SalvaCookie_String("Nome_Unidades", "Unidades");

            // medicao e gateway nao selecionados
            CookieStore.SalvaCookie_Int("_IDMedicao", -10);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", -10);
            CookieStore.SalvaCookie_Int("_IDGateway", -10);
            CookieStore.SalvaCookie_Int("IDTipoGateway", -10);

            // tela de ajuda - dashboard 
            CookieStore.SalvaCookie_String("PaginaAjuda", "Dashboard_Painel");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Dashboard");

            // le cookies
            LeCookies_SmartEnergy();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // le grupos de painéis
            DashboardGrupoMetodos dashboardGrupoMetodos = new DashboardGrupoMetodos();
            List<DashboardGrupoDominio> listaDashBoardGrupo = dashboardGrupoMetodos.ListarPorIdConsultor(IDConsultor);

            // percorre grupos de painéis e descobre numero de painéis e blocos
            foreach (DashboardGrupoDominio grupo in listaDashBoardGrupo)
            {
                // le painéis do grupo
                DashboardPageMetodos dashboardPageMetodos = new DashboardPageMetodos();
                int NumPaineis = dashboardPageMetodos.NumPaineis(grupo.IDDashboardGrupo, DB_REFERENCIA.IDDashboardGrupo);
                grupo.NumPaineis = NumPaineis;

                // le blocos do grupo
                DashboardMetodos dashboardMetodos = new DashboardMetodos();
                int NumBlocos = dashboardMetodos.NumBlocos(grupo.IDDashboardGrupo, DB_REFERENCIA.IDDashboardGrupo);
                grupo.NumBlocos = NumBlocos;
            }

            return View(listaDashBoardGrupo);
        }

        // GET: Dashboard Gestor - Grupos de Painéis - Editar
        public ActionResult DB_Gestor_GrupoPaineis_Editar(int IDDashboardGrupo)
        {
            // tela de ajuda - grupo de unidades
            CookieStore.SalvaCookie_String("PaginaAjuda", "Dashboard_Painel");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Dashboard");

            // le cookies
            LeCookies_SmartEnergy();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // verifica se adicionando
            DashboardGrupoDominio grupo = new DashboardGrupoDominio();
            if (IDDashboardGrupo == 0)
            {
                // zera grupounidades com default
                grupo.IDCliente = 0;
                grupo.IDConsultor = IDConsultor;
            }
            else
            {
                // le grupo
                DashboardGrupoMetodos dashboardGrupoMetodos = new DashboardGrupoMetodos();
                grupo = dashboardGrupoMetodos.ListarPorId(IDDashboardGrupo);
            }

            return View(grupo);
        }

        // POST: Dashboard Gestor - Grupos de Painéis - Salvar
        [HttpPost]
        public ActionResult DB_Gestor_GrupoPaineis_Salvar(DashboardGrupoDominio grupo)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outro grupo com o mesmo nome
            DashboardGrupoMetodos dashboardGrupoMetodos = new DashboardGrupoMetodos();
            if (dashboardGrupoMetodos.VerificarDuplicidade(grupo))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Grupo de Painéis existente."
                };
            }
            else
            {
                // salva grupo de painéis
                dashboardGrupoMetodos.Salvar(grupo);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Dashboard Gestor - Grupos de Painéis - Excluir
        public ActionResult DB_Gestor_GrupoPaineis_Excluir(int IDDashboardGrupo)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // apaga o grupo paineis
            DashboardGrupoMetodos dashboardGrupoMetodos = new DashboardGrupoMetodos();
            dashboardGrupoMetodos.Excluir(IDDashboardGrupo);

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }
    }
}