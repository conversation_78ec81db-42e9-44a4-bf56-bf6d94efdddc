﻿using System;
using System.Runtime.InteropServices;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class DashboardController : Controller
    {
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Dashboard_FatPotencia", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern sbyte SmCalcDB_Dashboard_FatPotencia(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref DATAHOR<PERSON> pdatahora, sbyte navega, ref DASHBOARD_FATPOT psuperv);


        private int DB_Atualizar_FatPot(int IDDashboard, int IDMedicao, int Navegacao, string DataAtualTxt)
        {
            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("DB_Editar");
            ViewBag.Editar = editar_cookie;

            // le cookie datahora
            DateTime datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);

            if (DataAtualTxt != null)
            {
                datahora_cookie = DateTime.Parse(DataAtualTxt);
            }

            // verifica se atualizar apenas
            if (Navegacao == 0)
            {
                // forco setar data como ultimo registro
                datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);
            }


            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            DASHBOARD_FATPOT superv = new DASHBOARD_FATPOT();

            DATAHORA datahora = new DATAHORA();
            datahora.data.dia = (char)datahora_cookie.Day;
            datahora.data.mes = (char)datahora_cookie.Month;
            datahora.data.ano = (Int16)datahora_cookie.Year;
            datahora.hora.hora = (char)1;
            datahora.hora.min = (char)0;
            datahora.hora.seg = (char)0;

            // preenche interface
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = medicao.IDGateway;

            // funcao de supervisao
            int retorno;

            // calcula valores
            retorno = SmCalcDB_Dashboard_FatPotencia((char)0, ref config_interface, ref datahora, (sbyte)Navegacao, ref superv);

            // erro
            if (retorno != 0)
            {
                // erro
                return (retorno);
            }

            // converte data e hora
            DateTime DataAtual = new DateTime((int)superv.Data.data.ano,
                                              (int)superv.Data.data.mes,
                                              (int)superv.Data.data.dia,
                                              1, 0, 0);

            DateTime FatPot_UltimaDatahora = new DateTime((int)superv.FatPot_UltimaDatahora.data.ano,
                                                   (int)superv.FatPot_UltimaDatahora.data.mes,
                                                   (int)superv.FatPot_UltimaDatahora.data.dia,
                                                   (int)superv.FatPot_UltimaDatahora.hora.hora,
                                                   (int)superv.FatPot_UltimaDatahora.hora.min, 0); 

            DateTime FatPot_FPC_DataHora = new DateTime((int)superv.FatPot_FPC_DataHora.data.ano,
                                                   (int)superv.FatPot_FPC_DataHora.data.mes,
                                                   (int)superv.FatPot_FPC_DataHora.data.dia,
                                                   (int)superv.FatPot_FPC_DataHora.hora.hora,
                                                   (int)superv.FatPot_FPC_DataHora.hora.min, 0);

            DateTime FatPot_FPI_DataHora = new DateTime((int)superv.FatPot_FPI_DataHora.data.ano,
                                                   (int)superv.FatPot_FPI_DataHora.data.mes,
                                                   (int)superv.FatPot_FPI_DataHora.data.dia,
                                                   (int)superv.FatPot_FPI_DataHora.hora.hora,
                                                   (int)superv.FatPot_FPI_DataHora.hora.min, 0);

            // nome da medicao
            ViewBag.NomeMedicao = medicao.Nome;

            // fator de potencia
            if (superv.flag_reg_diario == 1 || superv.flag_reg_diario == 2)
            {
                ViewBag.FatPot_Ultimo = "---";
                ViewBag.FatPot_UltimoPeriodo = 3;
            }
            else
            {
                ViewBag.FatPot_Ultimo = string.Format("{0:0.000}", superv.FatPot_Ultimo);
                ViewBag.FatPot_UltimoPeriodo = superv.FatPot_UltimoPeriodo;
            }
            ViewBag.FatPot_UltimaDatahora = string.Format("{0:d} {1:HH:mm}", FatPot_UltimaDatahora, FatPot_UltimaDatahora);

            if (FatPot_FPC_DataHora.Year == 2000)
            {
                ViewBag.FatPot_FPC = "---";
                ViewBag.FatPot_FPC_DataHora = "--/--/----";
            }
            else
            {
                ViewBag.FatPot_FPC = string.Format("{0:0.000}", superv.FatPot_FPC);
                ViewBag.FatPot_FPC_DataHora = string.Format("{0:d} {1:HH:mm}", FatPot_FPC_DataHora, FatPot_FPC_DataHora);
            }

            if (FatPot_FPI_DataHora.Year == 2000)
            {
                ViewBag.FatPot_FPI = "---";
                ViewBag.FatPot_FPI_DataHora = "--/--/----";
            }
            else
            {
                ViewBag.FatPot_FPI = string.Format("{0:0.000}", superv.FatPot_FPI);
                ViewBag.FatPot_FPI_DataHora = string.Format("{0:d} {1:HH:mm}", FatPot_FPI_DataHora, FatPot_FPI_DataHora);
            }

            ViewBag.FatPot_ReferenciaFPC = string.Format("{0:0.000}", -0.92);
            ViewBag.FatPot_ReferenciaFPI = string.Format("{0:0.000}", 0.92);

            // datahoraAtual do dashboard
            if (FatPot_UltimaDatahora.Year == 2000)
            {
                ViewBag.DataHoraAtual = string.Format("{0:d}", DataAtual);
            }
            else
            {
                ViewBag.DataHoraAtual = string.Format("{0:d} {1:HH:mm}", FatPot_UltimaDatahora, FatPot_UltimaDatahora);
            }

            // grafico
            var FatPot = new double[26];
            var Periodo = new double[26];
            var Dias = new string[26];

            double FatPot_min = 1.0;

            // valores
            DateTime strData = new DateTime((int)superv.DataHora[0].data.ano,
                                            (int)superv.DataHora[0].data.mes,
                                            (int)superv.DataHora[0].data.dia,
                                            (int)superv.DataHora[0].hora.hora,
                                            (int)superv.DataHora[0].hora.min, 0);

            // volta 1 hora para a primeira barra
            strData = strData.AddHours(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 26; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proxima hora
                strData = strData.AddHours(1);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    FatPot[i] = superv.FatPot[0];
                    Periodo[i] = superv.Periodo[0];
                }

                // verifica se ultima barra
                if (i == 25)
                {
                    // zera
                    FatPot[i] = superv.FatPot[23];
                    Periodo[i] = superv.Periodo[23];
                }

                // restante
                if (i > 0 && i < 25)
                {
                    // copia
                    j = i - 1;

                    // copia
                    FatPot[i] = superv.FatPot[j];
                    Periodo[i] = superv.Periodo[j];

                    // verifica fatpot minimo
                    if (Funcoes_FatPot.ComparaFatPot(Math.Abs(FatPot[i]), FatPot_min) > 0)
                        FatPot_min = Math.Abs(FatPot[i]);
                }
            }

            if (FatPot_min > 0.92)
                FatPot_min = 0.92;

            FatPot_min = FatPot_min - (FatPot_min * 0.01);

            ViewBag.FatPotMin = FatPot_min;

            ViewBag.FatPot = FatPot;
            ViewBag.Periodo = Periodo;
            ViewBag.Dias = Dias;

            // data atual
            string IDDashboardTxt = string.Format("dt{0}", IDDashboard);
            ViewBag.IDDashboardTxt = IDDashboardTxt;
            ViewBag.DataAtual = string.Format("{0:d} {1:HH:mm}", DataAtual, DataAtual);

            // ok
            return (0);
        }
    }
}