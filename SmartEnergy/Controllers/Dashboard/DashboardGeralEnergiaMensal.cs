﻿using System;
using System.Runtime.InteropServices;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.Funcoes;
using SmartEnergyLib.SQL;

namespace SmartEnergy.Controllers
{
    public partial class DashboardController : Controller
    {
        [DllImport("SmartCalcDB.dll", EntryPoint = "SmCalcDB_Dashboard_GeralMensalEnergia", SetLastError = true, CallingConvention = CallingConvention.Cdecl)]
        public static extern sbyte SmCalcDB_Dashboard_GeralMensalEnergia(char tipo_interface, ref CONFIG_INTERFACE cfg_interface, ref DATAHOR<PERSON> pdatahora, sbyte navega, ref DASHBOARD_GERALENERGIA_MENSAL psuperv);


        private int DB_Atualizar_GeralEnergiaMensal(int IDDashboard, int IDMedicao, int Navegacao, string DataAtualTxt)
        {
            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le cookie configuracao
            bool editar_cookie = CookieStore.LeCookie_Bool("DB_Editar");
            ViewBag.Editar = editar_cookie;

            // le cookie datahora
            DateTime datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);

            if (DataAtualTxt != null)
            {
                datahora_cookie = DateTime.Parse(DataAtualTxt);
            }

            // verifica se atualizar apenas
            if (Navegacao == 0)
            {
                // forco setar data como ultimo registro
                datahora_cookie = new DateTime(2000, 1, 1, 0, 0, 0);
            }

            // preenche solicitacao
            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            DASHBOARD_GERALENERGIA_MENSAL superv = new DASHBOARD_GERALENERGIA_MENSAL();

            DATAHORA datahora = new DATAHORA();
            datahora.data.dia = (char)datahora_cookie.Day;
            datahora.data.mes = (char)datahora_cookie.Month;
            datahora.data.ano = (Int16)datahora_cookie.Year;
            datahora.hora.hora = (char)1;
            datahora.hora.min = (char)0;
            datahora.hora.seg = (char)0;

            // preenche interface
            config_interface.sweb.id_cliente = medicao.IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;
            config_interface.sweb.id_gateway = medicao.IDGateway;

            // calcula valores
            int retorno = SmCalcDB_Dashboard_GeralMensalEnergia((char)0, ref config_interface, ref datahora, (sbyte)Navegacao, ref superv);
            FuncoesDashboard.DivisaoUnidade_DASHBOARD_GERALENERGIA_MENSAL(ref superv, medicao.IDTipoUnidadePotencia);

            // erro
            if (retorno != 0)
            {
                // erro
                return (retorno);
            }

            // converte data e hora
            DateTime DataAtual = new DateTime((int)superv.Data.data.ano,
                                              (int)superv.Data.data.mes,
                                              (int)superv.Data.data.dia,
                                              1, 0, 0);

            DateTime Dem_MaxP_DataHora = new DateTime((int)superv.Dem_MaxP_DataHora.data.ano,
                                                   (int)superv.Dem_MaxP_DataHora.data.mes,
                                                   (int)superv.Dem_MaxP_DataHora.data.dia,
                                                   (int)superv.Dem_MaxP_DataHora.hora.hora,
                                                   (int)superv.Dem_MaxP_DataHora.hora.min, 0);

            DateTime Dem_MaxFPI_DataHora = new DateTime((int)superv.Dem_MaxFPI_DataHora.data.ano,
                                                   (int)superv.Dem_MaxFPI_DataHora.data.mes,
                                                   (int)superv.Dem_MaxFPI_DataHora.data.dia,
                                                   (int)superv.Dem_MaxFPI_DataHora.hora.hora,
                                                   (int)superv.Dem_MaxFPI_DataHora.hora.min, 0);

            DateTime Dem_MaxFPC_DataHora = new DateTime((int)superv.Dem_MaxFPC_DataHora.data.ano,
                                                   (int)superv.Dem_MaxFPC_DataHora.data.mes,
                                                   (int)superv.Dem_MaxFPC_DataHora.data.dia,
                                                   (int)superv.Dem_MaxFPC_DataHora.hora.hora,
                                                   (int)superv.Dem_MaxFPC_DataHora.hora.min, 0);

            // nome da medicao
            ViewBag.NomeMedicao = medicao.Nome;

            ViewBag.Data = string.Format("{0:Y}", DataAtual);

            // consumo
            if( superv.flag_reg_mensal == 1 || superv.flag_reg_mensal == 2 )
            {
                ViewBag.ConsumoTotalP = "---";
                ViewBag.ConsumoTotalFPI = "---";
                ViewBag.ConsumoTotalFPC = "---";

                ViewBag.FatPotP = "---";
                ViewBag.FatPotFPI = "---";
                ViewBag.FatPotFPC = "---";

                ViewBag.UferP = "---";
                ViewBag.UferFPI = "---";
                ViewBag.UferFPC = "---";
            }
            else
            {
                ViewBag.ConsumoTotalP = Funcoes_SmartEnergy.FormatValor(superv.ConsumoTotalP);
                ViewBag.ConsumoTotalFPI = Funcoes_SmartEnergy.FormatValor(superv.ConsumoTotalFPI);
                ViewBag.ConsumoTotalFPC = Funcoes_SmartEnergy.FormatValor(superv.ConsumoTotalFPC);

                ViewBag.FatPotP = string.Format("{0:0.000}", superv.FatPotP);
                ViewBag.FatPotFPI = string.Format("{0:0.000}", superv.FatPotFPI);
                ViewBag.FatPotFPC = string.Format("{0:0.000}", superv.FatPotFPC);

                ViewBag.UferP = Funcoes_SmartEnergy.FormatValor(superv.UferP);
                ViewBag.UferFPI = Funcoes_SmartEnergy.FormatValor(superv.UferFPI);
                ViewBag.UferFPC = Funcoes_SmartEnergy.FormatValor(superv.UferFPC);
            }

            if (Dem_MaxP_DataHora.Year == 2000)
            {
                ViewBag.Dem_MaxP = "---";
                ViewBag.Dem_MaxP_DataHora = "--/--/----";
            }
            else
            {
                ViewBag.Dem_MaxP = string.Format("{0:#,##0.0}", superv.Dem_MaxP);
                ViewBag.Dem_MaxP_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MaxP_DataHora, Dem_MaxP_DataHora);
            }

            if (Dem_MaxFPI_DataHora.Year == 2000)
            {
                ViewBag.Dem_MaxFPI = "---";
                ViewBag.Dem_MaxFPI_DataHora = "--/--/----";
            }
            else
            {
                ViewBag.Dem_MaxFPI = string.Format("{0:#,##0.0}", superv.Dem_MaxFPI);
                ViewBag.Dem_MaxFPI_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MaxFPI_DataHora, Dem_MaxFPI_DataHora);
            }

            if (Dem_MaxFPC_DataHora.Year == 2000)
            {
                ViewBag.Dem_MaxFPC = "---";
                ViewBag.Dem_MaxFPC_DataHora = "--/--/----";
            }
            else
            {
                ViewBag.Dem_MaxFPC = string.Format("{0:#,##0.0}", superv.Dem_MaxFPC);
                ViewBag.Dem_MaxFPC_DataHora = string.Format("{0:d} {1:HH:mm}", Dem_MaxFPC_DataHora, Dem_MaxFPC_DataHora);
            }

            // grafico
            var Consumo = new double[40];
            var ConsumoP = new double[40];
            var ConsumoFPI = new double[40];
            var ConsumoFPC = new double[40];
            var Status = new int[40];
            var Dias = new string[40];

            double Cons_max = 0.0;

            // valores
            DateTime strData = new DateTime((int)superv.DataHora[0].data.ano,
                                            (int)superv.DataHora[0].data.mes,
                                            (int)superv.DataHora[0].data.dia,
                                            1,0,0);

            int i = 0;

            for (i = 0; i < superv.NumDias; i++)
            {
                // formata label
                Dias[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");

                // proximo dia
                strData = strData.AddDays(1);

                // copia
                ConsumoP[i] = superv.ConsumoP[i];
                ConsumoFPI[i] = superv.ConsumoFPI[i];
                ConsumoFPC[i] = superv.ConsumoFPC[i];
                Consumo[i] = superv.ConsumoP[i] + superv.ConsumoFPI[i] + superv.ConsumoFPC[i];

                // status
                if (superv.flag_reg_mensal == 1 || superv.flag_reg_mensal == 2)
                {
                    Status[i] = 1;
                }
                else
                {
                    Status[i] = 0;
                }

                // verifica consumo maximo
                if (Consumo[i] > Cons_max)
                    Cons_max = Consumo[i];
            }

            Cons_max = Cons_max * 1.1;

            if (Cons_max == 0.0)
            {
                Cons_max = 10.0;
            }

            ViewBag.Cons_max = Cons_max;

            ViewBag.Consumo = Consumo;
            ViewBag.ConsumoP = ConsumoP;
            ViewBag.ConsumoFPI = ConsumoFPI;
            ViewBag.ConsumoFPC = ConsumoFPC;
            ViewBag.Status = Status;
            ViewBag.Dias = Dias;
            ViewBag.NumDias = superv.NumDias;

            // data atual
            string IDDashboardTxt = string.Format("dt{0}", IDDashboard);
            ViewBag.IDDashboardTxt = IDDashboardTxt;
            ViewBag.DataAtual = string.Format("{0:d} {1:HH:mm}", DataAtual, DataAtual);

            // ok
            return (0);
        }
    }
}