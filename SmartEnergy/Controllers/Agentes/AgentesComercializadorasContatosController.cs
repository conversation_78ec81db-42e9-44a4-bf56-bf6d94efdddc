﻿using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class AgentesController
    {

        // GET: ComercializadoraContato Editar
        public ActionResult ComercializadoraContato_Editar(int IDAgente, int IDContato)
        {
            // tela de ajuda - comercializadora contatos
            CookieStore.SalvaCookie_String("PaginaAjuda", "Contato");

            // pagina anterior
            ViewBag.returnUrl = Request.UrlReferrer;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // verifica se adicionando
            AgentesContatosDominio contato = new AgentesContatosDominio();
            if (IDContato == 0)
            {
                // zera contato com default
                contato.IDContato = 0;
                contato.IDAgente = IDAgente;
                contato.IDConsultor = ViewBag._IDConsultor;

                contato.IDTipoContato = 0;

                contato.Nome = "";
                contato.Email = "";
                contato.Telefone = "";
                contato.Ramal = "";
                contato.Celular = "";
                contato.Cargo = "";
                contato.Departamento = "";
            }
            else
            {
                // le contato
                AgentesContatosMetodos contatosMetodos = new AgentesContatosMetodos();
                contato = contatosMetodos.ListarPorId(IDContato);
            }

            return View(contato);
        }

        // POST: ComercializadoraContato - Salvar
        [HttpPost]
        public ActionResult ComercializadoraContato_Salvar(AgentesContatosDominio contato)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // caso não tive consultor, pois é a GESTAL que esta editando, uso o default a CPFL
            if (contato.IDConsultor < 0)
            {
                // CPFL
                contato.IDConsultor = CLIENTES_ESPECIAIS.CPFL;
            }

            // verifica se existe outra contato com o mesmo nome
            AgentesContatosMetodos contatosMetodos = new AgentesContatosMetodos();
            if (contatosMetodos.VerificarDuplicidade(contato))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Contato existente."
                };
            }
            else
            {
                // salva contato
                contatosMetodos.Salvar(contato);

                // evento 
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();

                if (contato.IDContato > 0)
                {
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.CONTATO, contato.IDContato);
                }
                else
                {
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.CONTATO, contato.IDContato);
                }

                // verifica integridade
                int retorno = contatosMetodos.VerificarIntegridade(contato);

                switch (retorno)
                {
                    case 1:
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "Contato não adicionado."
                        };
                        break;

                    case 2:
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            erro = "IDContato adicionado com valor alto.<br><br>FAVOR AVISAR O DESENVOLVIMENTO ANTES DE PROSSEGUIR !!!"
                        };
                        break;
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: ComercializadoraContato Excluir
        public ActionResult ComercializadoraContato_Excluir(int IDAgente, int IDContato)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                IDAgente = IDAgente,
                erro = ""
            };

            // apaga o contato
            AgentesContatosMetodos contatosMetodos = new AgentesContatosMetodos();
            contatosMetodos.Excluir(IDContato);

            // evento 
            UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.EXCLUI_CONFIG, TABELA_CONFIG.CONTATO, IDContato);

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }
    }
}
