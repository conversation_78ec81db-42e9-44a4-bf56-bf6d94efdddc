﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class AgentesController : Controller
    {
        // Listas utilizadas
        private void PreparaListas_Comercializadoras(int IDAgente, int IDEstado = 0)
        {
            // le tipos Estado
            CidadeEstadoPaisMetodos cidadeMetodos = new CidadeEstadoPaisMetodos();
            List<EstadosDominio> listatiposEstado = cidadeMetodos.ListarTodosEstados();
            ViewBag.listaTipoEstado = listatiposEstado;

            // le tipos Cidade
            List<CidadesDominio> listatiposCidade = cidadeMetodos.ListarTodosCidades(IDEstado);
            ViewBag.listaTipoCidade = listatiposCidade;

            // le Contatos
            AgentesContatosMetodos contatoMetodos = new AgentesContatosMetodos();
            List<AgentesContatosDominio> listaContatos = contatoMetodos.ListarPorIDAgente(IDAgente);
            ViewBag.listaContatos = listaContatos;

            return;
        }

        // GET: Configuracao Comercializadoras
        public ActionResult Comercializadoras()
        {
            // tela de ajuda - comercializadoras
            CookieStore.SalvaCookie_String("PaginaAjuda", "Comercializadoras");

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_Comercializadoras(IDConsultor);

            // le comercializadoras
            AgentesMetodos agentesMetodos = new AgentesMetodos();
            List<AgentesDominio> listaComercializadoras = agentesMetodos.ListarPorComercializadora();

            return View(listaComercializadoras);
        }

        // GET: Configuracao Comercializadora - Editar
        public ActionResult Comercializadora_Editar(int IDAgente)
        {
            // tela de ajuda - comercializadoras
            CookieStore.SalvaCookie_String("PaginaAjuda", "Comercializadoras_Editar");

            // pagina anterior
            ViewBag.returnUrl = Request.UrlReferrer;

            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "Configuracao");

            // le cookies
            LeCookies_SmartEnergy();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // permissoes
            Permissoes();

            // verifica se adicionando
            AgentesDominio comercializadora = new AgentesDominio();
            if (IDAgente == 0)
            {
                // zera comercializadora com default
                comercializadora.IDAgente = 0;
                comercializadora.IDConsultor = IDConsultor;
                comercializadora.IDTipoClasseAgente = TIPO_CLASSE_AGENTE.Comercializador;

                comercializadora.Nome = "";
                comercializadora.RazaoSocial = "";
                comercializadora.CNPJ = "00.000.000/0000-00";

                comercializadora.Endereco = "";
                comercializadora.CEP = "";
                comercializadora.IDEstado = 26;          // sao paulo
                comercializadora.IDCidade = 5270;        // sao paulo
                comercializadora.Latitude = 0.0;
                comercializadora.Longitude = 0.0;
            }
            else
            {
                // le comercializadora
                AgentesMetodos agentesMetodos = new AgentesMetodos();
                comercializadora = agentesMetodos.ListarPorId(IDAgente);
            }

            // prepara listas
            PreparaListas_Comercializadoras(comercializadora.IDAgente, comercializadora.IDEstado);

            return View(comercializadora);
        }

        // GET: Obter Cidades
        public JsonResult ObterCidades(int IDEstado)
        {
            // le tipos Cidade
            CidadeEstadoPaisMetodos cidadeMetodos = new CidadeEstadoPaisMetodos();
            List<CidadesDominio> listatiposCidade = cidadeMetodos.ListarTodosCidades(IDEstado);

            // retorna o valor em JSON
            return Json(listatiposCidade, JsonRequestBehavior.AllowGet);
        }

        // POST: Configuracao Comercializadora - Salvar
        [HttpPost]
        public ActionResult Comercializadora_Salvar(AgentesDominio comercializadora)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                IDAgente = comercializadora.IDAgente,
                erro = ""
            };

            // le cookies
            LeCookies_SmartEnergy();

            // IDConsultor
            int IDConsultor = ViewBag._IDConsultor;

            // verifica se existe outra comercializadora com o mesmo nome
            AgentesMetodos agentesMetodos = new AgentesMetodos();
            if (agentesMetodos.VerificarDuplicidade(comercializadora))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    IDAgente = comercializadora.IDAgente,
                    erro = "Comercializadora existente."
                };
            }
            else
            {
                // salva comercializadora
                agentesMetodos.Salvar(comercializadora);

                // evento 
                UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();

                if (comercializadora.IDAgente > 0)
                {
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.ALTERA_CONFIG, TABELA_CONFIG.COMERCIALIZADORA, comercializadora.IDAgente);
                }
                else
                {
                    usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.INSERE_CONFIG, TABELA_CONFIG.COMERCIALIZADORA, comercializadora.IDAgente);
                }

                // verifica integridade
                int retorno = agentesMetodos.VerificarIntegridade(comercializadora);

                switch (retorno)
                {
                    case 1:
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            IDAgente = comercializadora.IDAgente,
                            erro = "Comercializadora não adicionada."
                        };
                        break;

                    case 2:
                        // retorna status
                        returnedData = new
                        {
                            status = "ERRO",
                            IDAgente = comercializadora.IDAgente,
                            erro = "IDAgente adicionado com valor alto.<br><br>FAVOR AVISAR O DESENVOLVIMENTO ANTES DE PROSSEGUIR !!!"
                        };
                        break;
                }
            }

            // retorna status
            return Json(returnedData);
        }

        // GET: Configuracao Comercializadora - Excluir
        public ActionResult Comercializadora_Excluir(int IDAgente)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // apaga a comercializadora
            AgentesMetodos agentesMetodos = new AgentesMetodos();
            agentesMetodos.Excluir(IDAgente);

            // apaga contatos da comercializadora
            AgentesContatosMetodos agentesContatosMetodos = new AgentesContatosMetodos();
            agentesContatosMetodos.ExcluirAgente(IDAgente);

            // evento 
            UsuarioEventoMetodos usuarioEventoMetodo = new UsuarioEventoMetodos();
            usuarioEventoMetodo.InserirEvento(USUARIO_EVENTO.EXCLUI_CONFIG, TABELA_CONFIG.COMERCIALIZADORA, IDAgente);

            // retorna status
            return Json(returnedData, JsonRequestBehavior.AllowGet);
        }
    }
}
