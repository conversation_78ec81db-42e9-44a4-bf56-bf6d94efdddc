﻿using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class AgentesController
    {
        // Listas utilizadas
        private void PreparaListas_Distribuidoras(int IDEstado = 0)
        {
            // le tipos Estado
            CidadeEstadoPaisMetodos cidadeMetodos = new CidadeEstadoPaisMetodos();
            List<EstadosDominio> listatiposEstado = cidadeMetodos.ListarTodosEstados();
            ViewBag.listaTipoEstado = listatiposEstado;

            // le tipos Cidade
            List<CidadesDominio> listatiposCidade = cidadeMetodos.ListarTodosCidades(IDEstado);
            ViewBag.listaTipoCidade = listatiposCidade;

            return;
        }

        // GET: Distribuidoras
        public ActionResult Distribuidoras()
        {
            // tela de ajuda - distribuidoras
            CookieStore.SalvaCookie_String("PaginaAjuda", "Financas_Configuracao_Distribuidoras");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // prepara listas
            PreparaListas_Distribuidoras();

            // le distribuidoras
            AgentesDistribuidoraMetodos distribuidorasMetodos = new AgentesDistribuidoraMetodos();
            List<AgentesDistribuidoraDominio> listaDistribuidoras = distribuidorasMetodos.ListarTodos();

            // percorre distribuidoras e descobre numero de medicoes associadas
            foreach (AgentesDistribuidoraDominio distribuidora in listaDistribuidoras)
            {
                MedicoesMetodos medicoesMetodos = new MedicoesMetodos();

                // numero de medicoes com esta distribuidora
                int NumMedicoes = medicoesMetodos.NumMedicoesDistribuidora(distribuidora.IDAgenteDistribuidora);
                distribuidora.NumMedicoes = NumMedicoes;

                // numero de clientes com esta distribuidora
                int NumClientes = medicoesMetodos.NumClientesDistribuidora(distribuidora.IDAgenteDistribuidora);
                distribuidora.NumClientes = NumClientes;

                // numero de gateways com esta distribuidora
                int NumGateways = medicoesMetodos.NumGatewaysDistribuidora(distribuidora.IDAgenteDistribuidora);
                distribuidora.NumGateways = NumGateways;
            }

            return View(listaDistribuidoras);
        }

        // GET: Editar
        public ActionResult Distribuidoras_Editar(int IDAgenteDistribuidora)
        {
            // tela de ajuda - distribuidoras
            CookieStore.SalvaCookie_String("PaginaAjuda", "Financas_Configuracao_DistribuidorasEditar");

            // le cookies
            LeCookies_SmartEnergy();

            // permissoes
            Permissoes();

            // verifica se adicionando
            AgentesDistribuidoraDominio distribuidora = new AgentesDistribuidoraDominio();
            if (IDAgenteDistribuidora == 0)
            {
                // zera distribuidora com default
                distribuidora.IDAgenteDistribuidora = 0;
                distribuidora.IDConsultor = 0;
                distribuidora.IDTipoClasseAgente = TIPO_CLASSE_AGENTE.Distribuidor;

                distribuidora.Nome = "";
                distribuidora.RazaoSocial = "";
                distribuidora.CNPJ = "00.000.000/0000-00";

                distribuidora.Endereco = "";
                distribuidora.CEP = "";
                distribuidora.IDEstado = 26;          // sao paulo
                distribuidora.IDCidade = 5270;        // sao paulo
                distribuidora.Latitude = 0.0;
                distribuidora.Longitude = 0.0;

                distribuidora.NumClientes = 0;
                distribuidora.NumGateways = 0;
                distribuidora.NumMedicoes = 0;
            }
            else
            {
                // le distribuidora
                AgentesDistribuidoraMetodos distribuidorasMetodos = new AgentesDistribuidoraMetodos();
                distribuidora = distribuidorasMetodos.ListarPorId(IDAgenteDistribuidora);
            }

            // prepara listas
            PreparaListas_Distribuidoras(distribuidora.IDEstado);

            return View(distribuidora);
        }

        // POST: Salvar
        [HttpPost]
        public ActionResult Distribuidoras_Salvar(AgentesDistribuidoraDominio distribuidora)
        {
            // retorno
            var returnedData = new
            {
                status = "OK",
                erro = ""
            };

            // verifica se existe outra distribuidora com o mesmo nome
            AgentesDistribuidoraMetodos distribuidorasMetodos = new AgentesDistribuidoraMetodos();
            if (distribuidorasMetodos.VerificarDuplicidade(distribuidora))
            {
                // retorna status
                returnedData = new
                {
                    status = "ERRO",
                    erro = "Distribuidora existente."
                };
            }
            else
            {
                // salva distribuidora
                distribuidorasMetodos.Salvar(distribuidora);
            }

            // retorna status
            return Json(returnedData);
        }

        // GET:  Excluir
        public ActionResult Distribuidoras_Excluir(int IDAgenteDistribuidora)
        {
            // apaga os PIS COFINS da distribuidora
            PisCofinsMetodos PisCofinsMetodos = new PisCofinsMetodos();
            PisCofinsMetodos.Excluir_IDAgenteDistribuidora(IDAgenteDistribuidora);

            // apaga tarifas da distribuidora
            Tarifas_AZMetodos TarifaAZMetodos = new Tarifas_AZMetodos();
            TarifaAZMetodos.Excluir_IDAgenteDistribuidora(IDAgenteDistribuidora);

            Tarifas_VDMetodos TarifaVDMetodos = new Tarifas_VDMetodos();
            TarifaVDMetodos.Excluir_IDAgenteDistribuidora(IDAgenteDistribuidora);

            Tarifas_CVMetodos TarifaCVMetodos = new Tarifas_CVMetodos();
            TarifaCVMetodos.Excluir_IDAgenteDistribuidora(IDAgenteDistribuidora);

            // apaga a distribuidora
            AgentesDistribuidoraMetodos distribuidorasMetodos = new AgentesDistribuidoraMetodos();
            distribuidorasMetodos.Excluir(IDAgenteDistribuidora);

            // retorna status
            return Json("OK", JsonRequestBehavior.AllowGet);
        }
    }
}