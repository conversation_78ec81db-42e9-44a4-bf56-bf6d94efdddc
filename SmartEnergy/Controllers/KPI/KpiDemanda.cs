﻿using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    public partial class KpiController
    {

        //
        // KPI DEMANDA
        //

        // GET: KPI Demanda
        public ActionResult KPI_Demanda(int IDCliente, int IDMedicao, int idKPI, int TipoRelat, int tipo_arquivo = 0, string Data = null)
        {
            // salva cookie
            CookieStore.SalvaCookie_String("TipoPaginaAtual", "KPI_Demanda");

            // data
            if( Data != null )
            {
                // pega data
                DateTime dateValue = DateTime.Parse(Data);

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Relat_Data", dateValue);

                // salva cookie tipoperiodo para diario
                CookieStore.SalvaCookie_Int("Relat_TipoPeriodo", 0);
            }

            // relatorio 
            return (KPI_Grafico_Show(IDCliente, IDMedicao, idKPI, TipoRelat, tipo_arquivo));
        }

        // KPI Demanda Diario
        private void KPI_Demanda_Diario(int IDCliente, int IDMedicao, int idKPI, DATAHORA data_hora, int TipoRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_DIARIO relatorio = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise = new RELAT_DIARIO_ANALISE();

            RELAT_DIARIO relatorio_sim = new RELAT_DIARIO();
            RELAT_DIARIO_ANALISE analise_sim = new RELAT_DIARIO_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int TipoSimula = 0;

            // le configuracao medicao
            MedicoesMetodos medicoesMetodos = new MedicoesMetodos();
            MedicoesDominio medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le configuracao do KPI
            KPIConfiguracaoMetodos kpiMetodos = new KPIConfiguracaoMetodos();
            KPIConfiguracaoDominio kpi = kpiMetodos.ListarPorId(idKPI);


            // calcula valores
            retorno = SmCalcDB_Energia_RelatDiario((char)0, ref config_interface, (char)0, (char)TipoSimula, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_DIARIO(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   0, 15, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno Atv {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var DemandaAtv = new double[98];
            var DemandaKPI = new double[98];
            var ValoresKPI = new double[98];
            var Periodo = new int[98];
            var Datas = new string[98];
            var DatasN = new DateTime[98];
            var Horas = new string[98];

            double DemKPI_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            DateTime strDataFim = new DateTime((int)relatorio.registro[95].datahora.data.ano,
                                   (int)relatorio.registro[95].datahora.data.mes,
                                   (int)relatorio.registro[95].datahora.data.dia,
                                   (int)relatorio.registro[95].datahora.hora.hora,
                                   (int)relatorio.registro[95].datahora.hora.min, 0);

            // valores do KPI
            List<KPIHistDominio> histKPI = Get_valoresKPI(kpi, medicao, strData, strDataFim);

            // valor KPI
            double valorKPI = histKPI[0].valorKPI;

            // volta 15min para a primeira barra
            strData = strData.AddMinutes(-15);

            int i = 0;
            int j = 0;
           
            for (i = 0; i < 98; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Horas[i] = strData.ToString("HH:mm");

                // KPI
                valorKPI = Get_valorKPI(histKPI, strData);

                // proximo quinze minutos
                strData = strData.AddMinutes(15);

                // verifica se primeira barra
                if (i == 0)
                {
                    // zera
                    DemandaAtv[i] = relatorio.registro[0].valor1;
                    DemandaKPI[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[0].valor1, valorKPI);
                    ValoresKPI[i] = valorKPI;
                    Periodo[i] = relatorio.registro[0].periodo;
                }

                // verifica se ultima barra
                if (i == 97)
                {
                    // zera
                    DemandaAtv[i] = relatorio.registro[95].valor1;
                    DemandaKPI[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[95].valor1, valorKPI);
                    ValoresKPI[i] = valorKPI;
                    Periodo[i] = relatorio.registro[95].periodo;
                }

                if (i >= 1 && i <= 96)
                {
                    // copia
                    j = i - 1;

                    DemandaAtv[i] = relatorio.registro[j].valor1;
                    DemandaKPI[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[j].valor1, valorKPI);
                    ValoresKPI[i] = valorKPI;
                    Periodo[i] = relatorio.registro[j].periodo;

                    // verifica se sem registro
                    if (relatorio.registro[j].periodo == 3)
                    {
                        DemandaAtv[i] = 0.0;
                        DemandaKPI[i] = 0.0;
                    }

                    // verifica maxima
                    if (DemandaKPI[i] > DemKPI_max_grafico)
                        DemKPI_max_grafico = DemandaKPI[i];
                }
            }

            DemKPI_max_grafico = DemKPI_max_grafico * 1.1;

            if (DemKPI_max_grafico < 1.0)
            {
                //DemKPI_max_grafico = 1.0;
            }

            ViewBag.DemKPIMaxGrafico = DemKPI_max_grafico;

            ViewBag.DemandaAtv = DemandaAtv;
            ViewBag.DemandaKPI = DemandaKPI;
            ViewBag.ValoresKPI = ValoresKPI;
            ViewBag.Periodo = Periodo;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Horas = Horas;

            ViewBag.valorKPI = valorKPI;

            // informacoes do relatorio
            InformacoesRelat(kpi, TipoRelat);

            ViewBag.PeriodoRelat = string.Format("Diário");

            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:dd/MM/yyyy}", DataAtual);


            // estatisticas
            bool achouP = false;
            double DemKPI_MinP = 0.0;
            DateTime DemKPI_MinP_DataHora = new DateTime(2000, 1, 1, 0, 0, 0);
            double DemKPI_MaxP = 0.0;
            DateTime DemKPI_MaxP_DataHora = new DateTime(2000, 1, 1, 0, 0, 0);

            bool achouFPI = false;
            double DemKPI_MinFPI = 0.0;
            DateTime DemKPI_MinFPI_DataHora = new DateTime(2000, 1, 1, 0, 0, 0);
            double DemKPI_MaxFPI = 0.0;
            DateTime DemKPI_MaxFPI_DataHora = new DateTime(2000, 1, 1, 0, 0, 0);

            bool achouFPC = false;
            double DemKPI_MinFPC = 0.0;
            DateTime DemKPI_MinFPC_DataHora = new DateTime(2000, 1, 1, 0, 0, 0);
            double DemKPI_MaxFPC = 0.0;
            DateTime DemKPI_MaxFPC_DataHora = new DateTime(2000, 1, 1, 0, 0, 0);

            // percorre valores
            for (i = 1; i < 97; i++)
            {
                // periodo
                switch( Periodo[i] )
                {
                    case 0: // ponta

                        // verifica se ja tem ponta
                        if( achouP )
                        {
                            // verifica se minimo
                            if (DemandaKPI[i] < DemKPI_MinP)
                            {
                                DemKPI_MinP = DemandaKPI[i];
                                DemKPI_MinP_DataHora = DatasN[i];
                            }

                            // verifica se maximo
                            if( DemandaKPI[i] > DemKPI_MaxP )
                            {
                                DemKPI_MaxP = DemandaKPI[i];
                                DemKPI_MaxP_DataHora = DatasN[i];
                            }
                        }
                        else
                        {
                            achouP = true;

                            DemKPI_MinP = DemandaKPI[i];
                            DemKPI_MinP_DataHora = DatasN[i];
                            DemKPI_MaxP = DemandaKPI[i];
                            DemKPI_MaxP_DataHora = DatasN[i];
                        }
                        break;

                    case 1: // fora ponta indutivo

                        // verifica se ja tem fora ponta indutivo
                        if (achouFPI)
                        {
                            // verifica se minimo
                            if (DemandaKPI[i] < DemKPI_MinFPI)
                            {
                                DemKPI_MinFPI = DemandaKPI[i];
                                DemKPI_MinFPI_DataHora = DatasN[i];
                            }

                            // verifica se maximo
                            if (DemandaKPI[i] > DemKPI_MaxFPI)
                            {
                                DemKPI_MaxFPI = DemandaKPI[i];
                                DemKPI_MaxFPI_DataHora = DatasN[i];
                            }
                        }
                        else
                        {
                            achouFPI = true;

                            DemKPI_MinFPI = DemandaKPI[i];
                            DemKPI_MinFPI_DataHora = DatasN[i];
                            DemKPI_MaxFPI = DemandaKPI[i];
                            DemKPI_MaxFPI_DataHora = DatasN[i];
                        }
                        break;

                    case 2: // fora ponta capacitivo

                        // verifica se ja tem fora ponta capacitivo
                        if (achouFPC)
                        {
                            // verifica se minimo
                            if (DemandaKPI[i] < DemKPI_MinFPC)
                            {
                                DemKPI_MinFPC = DemandaKPI[i];
                                DemKPI_MinFPC_DataHora = DatasN[i];
                            }

                            // verifica se maximo
                            if (DemandaKPI[i] > DemKPI_MaxFPC)
                            {
                                DemKPI_MaxFPC = DemandaKPI[i];
                                DemKPI_MaxFPC_DataHora = DatasN[i];
                            }
                        }
                        else
                        {
                            achouFPC = true;

                            DemKPI_MinFPC = DemandaKPI[i];
                            DemKPI_MinFPC_DataHora = DatasN[i];
                            DemKPI_MaxFPC = DemandaKPI[i];
                            DemKPI_MaxFPC_DataHora = DatasN[i];
                        }
                        break;
                }
            }

            // demanda / KPI
            ViewBag.DemKPI_MaxP = string.Format("{0:#,##0.00}", DemKPI_MaxP);
            if (DemKPI_MaxP_DataHora.Year != 2000)
                ViewBag.DemKPI_MaxP_DataHora = string.Format("{0:t}", DemKPI_MaxP_DataHora);
            else
                ViewBag.DemKPI_MaxP_DataHora = "--:--";
            ViewBag.DemKPI_MaxP_DataHoraN = DemKPI_MaxP_DataHora;

            ViewBag.DemKPI_MaxFPI = string.Format("{0:#,##0.00}", DemKPI_MaxFPI);
            if (DemKPI_MaxFPI_DataHora.Year != 2000)
                ViewBag.DemKPI_MaxFPI_DataHora = string.Format("{0:t}", DemKPI_MaxFPI_DataHora);
            else
                ViewBag.DemKPI_MaxFPI_DataHora = "--:--";
            ViewBag.DemKPI_MaxFPI_DataHoraN = DemKPI_MaxFPI_DataHora;

            ViewBag.DemKPI_MaxFPC = string.Format("{0:#,##0.00}", DemKPI_MaxFPC);
            if (DemKPI_MaxFPC_DataHora.Year != 2000)
                ViewBag.DemKPI_MaxFPC_DataHora = string.Format("{0:t}", DemKPI_MaxFPC_DataHora);
            else
                ViewBag.DemKPI_MaxFPC_DataHora = "--:--";
            ViewBag.DemKPI_MaxFPC_DataHoraN = DemKPI_MaxFPC_DataHora;

            ViewBag.DemKPI_MinP = string.Format("{0:#,##0.00}", DemKPI_MinP);
            if (DemKPI_MinP_DataHora.Year != 2000)
                ViewBag.DemKPI_MinP_DataHora = string.Format("{0:t}", DemKPI_MinP_DataHora);
            else
                ViewBag.DemKPI_MinP_DataHora = "--:--";
            ViewBag.DemKPI_MinP_DataHoraN = DemKPI_MinP_DataHora;

            ViewBag.DemKPI_MinFPI = string.Format("{0:#,##0.00}", DemKPI_MinFPI);
            if (DemKPI_MinFPI_DataHora.Year != 2000)
                ViewBag.DemKPI_MinFPI_DataHora = string.Format("{0:t}", DemKPI_MinFPI_DataHora);
            else
                ViewBag.DemKPI_MinFPI_DataHora = "--:--";
            ViewBag.DemKPI_MinFPI_DataHoraN = DemKPI_MinFPI_DataHora;

            ViewBag.DemKPI_MinFPC = string.Format("{0:#,##0.00}", DemKPI_MinFPC);
            if (DemKPI_MinFPC_DataHora.Year != 2000)
                ViewBag.DemKPI_MinFPC_DataHora = string.Format("{0:t}", DemKPI_MinFPC_DataHora);
            else
                ViewBag.DemKPI_MinFPC_DataHora = "--:--";
            ViewBag.DemKPI_MinFPC_DataHoraN = DemKPI_MinFPC_DataHora;

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // KPI Demanda Diario XLS
        private HSSFWorkbook KPI_Demanda_Diario_XLS(int IDCliente, int IDMedicao, int idKPI, int TipoRelat)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Período", ViewBag.UnidadeRelat, "Demanda Ativa", ViewBag.NomeKPI };
    
            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);
    
            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 97; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _datahoraStyle);

                // periodo
                numeroCelulaXLS(row, 1, ViewBag.Periodo[i], _intCellStyle);

                // demanda KPI
                numeroCelulaXLS(row, 2, ViewBag.DemandaKPI[i], _2CellStyle);

                // demanda ativa
                numeroCelulaXLS(row, 3, ViewBag.DemandaAtv[i], _1CellStyle);

                // KPI
                numeroCelulaXLS(row, 4, ViewBag.ValoresKPI[i], _2CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 6000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "KPI - " + ViewBag.NomeRelat + " (" + ViewBag.UnidadeRelat + ")", "Diário", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { ViewBag.UnidadeRelat, "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row,    0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.DemKPI_MaxFPC), _2CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.DemKPI_MaxFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.DemKPI_MaxFPI), _2CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.DemKPI_MaxFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row,   5, double.Parse(ViewBag.DemKPI_MaxP), _2CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.DemKPI_MaxP_DataHoraN, _datahoraStyle);

            // demanda minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row,    0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.DemKPI_MinFPC), _2CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.DemKPI_MinFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.DemKPI_MinFPI), _2CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.DemKPI_MinFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.DemKPI_MinP), _2CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.DemKPI_MinP_DataHoraN, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // KPI Demanda Semanal
        private void KPI_Demanda_Semanal(int IDCliente, int IDMedicao, int idKPI, DATAHORA data_hora, int TipoRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_SEMANAL relatorio = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise = new RELAT_SEMANAL_ANALISE();

            RELAT_SEMANAL relatorio_sim = new RELAT_SEMANAL();
            RELAT_SEMANAL_ANALISE analise_sim = new RELAT_SEMANAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int TipoSimula = 0;

            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le configuracao do KPI
            KPIConfiguracaoMetodos kpiMetodos = new KPIConfiguracaoMetodos();
            KPIConfiguracaoDominio kpi = kpiMetodos.ListarPorId(idKPI);


            // calcula valores
            retorno = SmCalcDB_Energia_RelatSemanal((char)0, ref config_interface, (char)0, (char)TipoSimula, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_SEMANAL(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   0, 15, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno Atv {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var DemandaKPI = new double[7, 98];
            var ValoresKPI = new double[7, 98];
            var Demanda = new double[7, 98];
            var Periodo = new int[7, 98];
            var Datas = new string[98];
            var DatasN = new DateTime[7, 98];
            var Dias = new string[7];
            var Horas = new string[98];

            DateTime inicio = new DateTime();
            DateTime fim = new DateTime();

            double DemKPI_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            DateTime strDataFim = new DateTime((int)relatorio.registro[95].datahora.data.ano,
                                   (int)relatorio.registro[95].datahora.data.mes,
                                   (int)relatorio.registro[95].datahora.data.dia,
                                   (int)relatorio.registro[95].datahora.hora.hora,
                                   (int)relatorio.registro[95].datahora.hora.min, 0);

            // valores do KPI
            List<KPIHistDominio> histKPI = Get_valoresKPI(kpi, medicao, strData, strDataFim);

            // valor KPI
            double valorKPI = histKPI[0].valorKPI;

            // volta 15min para a primeira barra
            strData = strData.AddMinutes(-15);

            int i = 0;
            int j = 0;
            int k = 0;

            for (i = 0; i < 98; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                Horas[i] = strData.ToString("HH:mm");

                // percorre dias
                for (k = 0; k < 7; k++)
                {
                    // KPI
                    valorKPI = Get_valorKPI(histKPI, strData.AddDays(k));

                    // data e hora para excel
                    DatasN[k, i] = strData.AddDays(k);

                    // guarda inicio e fim
                    if (i == 0 && k == 0)
                    {
                        inicio = strData;
                        fim = inicio.AddDays(6);
                    }

                    // dia da semana
                    if (i == 1)
                    {
                        Dias[k] = string.Format("{0:d}", strData.AddDays(k));
                    }

                    // verifica se primeira ou ultima barra
                    if (i == 0)
                    {
                        // zera
                        DemandaKPI[k, i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[0].valor[k], valorKPI);
                        Demanda[k, i] = relatorio.registro[0].valor[k];
                        Periodo[k, i] = relatorio.registro[0].periodo[k];
                    }

                    if (i == 97)
                    {
                        // zera
                        DemandaKPI[k, i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[95].valor[k], valorKPI);
                        Demanda[k, i] = relatorio.registro[95].valor[k];
                        Periodo[k, i] = relatorio.registro[95].periodo[k];
                    }

                    if (i >= 1 && i <= 96)
                    {
                        // copia
                        j = i - 1;

                        DemandaKPI[k, i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[j].valor[k], valorKPI);
                        Demanda[k, i] = relatorio.registro[j].valor[k];
                        Periodo[k, i] = relatorio.registro[j].periodo[k];

                        // verifica se sem registro
                        if (relatorio.registro[j].periodo[k] == 3)
                        {
                            DemandaKPI[k, i] = 0.0;
                            Demanda[k, i] = 0.0;
                        }

                        // verifica demanda KPI maxima
                        if (DemandaKPI[k, i] > DemKPI_max_grafico)
                            DemKPI_max_grafico = DemandaKPI[k, i];
                    }
                }

                // proximo quinze minutos
                strData = strData.AddMinutes(15);
            }

            DemKPI_max_grafico = DemKPI_max_grafico * 1.1;

            if (DemKPI_max_grafico < 1.0)
            {
                //DemKPI_max_grafico = 1.0;
            }

            ViewBag.DemKPIMaxGrafico = DemKPI_max_grafico;

            ViewBag.DemandaKPI = DemandaKPI;
            ViewBag.ValoresKPI = ValoresKPI;
            ViewBag.Demanda = Demanda;
            ViewBag.Periodo = Periodo;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;
            ViewBag.Horas = Horas;

            ViewBag.valorKPI = valorKPI;

            // informacoes do relatorio
            InformacoesRelat(kpi, TipoRelat);

            ViewBag.PeriodoRelat = string.Format("Semanal");

            // data atual
            ViewBag.DataAtual = string.Format("{0:dd/MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = inicio;
            ViewBag.DataFinalN = fim;
            ViewBag.DataTextoAtual = string.Format("{0:d} - {1:d}", inicio, fim);


            // estatisticas
            bool achouP = false;
            double DemKPI_MinP = 0.0;
            DateTime DemKPI_MinP_DataHora = new DateTime(2000, 1, 1, 0, 0, 0);
            double DemKPI_MaxP = 0.0;
            DateTime DemKPI_MaxP_DataHora = new DateTime(2000, 1, 1, 0, 0, 0);

            bool achouFPI = false;
            double DemKPI_MinFPI = 0.0;
            DateTime DemKPI_MinFPI_DataHora = new DateTime(2000, 1, 1, 0, 0, 0);
            double DemKPI_MaxFPI = 0.0;
            DateTime DemKPI_MaxFPI_DataHora = new DateTime(2000, 1, 1, 0, 0, 0);

            bool achouFPC = false;
            double DemKPI_MinFPC = 0.0;
            DateTime DemKPI_MinFPC_DataHora = new DateTime(2000, 1, 1, 0, 0, 0);
            double DemKPI_MaxFPC = 0.0;
            DateTime DemKPI_MaxFPC_DataHora = new DateTime(2000, 1, 1, 0, 0, 0);

            // percorre valores
            for (i = 1; i < 97; i++)
            {
                // percorre dias
                for (k = 0; k < 7; k++)
                {
                    // periodo
                    switch (Periodo[k,i])
                    {
                        case 0: // ponta

                            // verifica se ja tem ponta
                            if (achouP)
                            {
                                // verifica se minimo
                                if (DemandaKPI[k,i] < DemKPI_MinP)
                                {
                                    DemKPI_MinP = DemandaKPI[k,i];
                                    DemKPI_MinP_DataHora = DatasN[k,i];
                                }

                                // verifica se maximo
                                if (DemandaKPI[k,i] > DemKPI_MaxP)
                                {
                                    DemKPI_MaxP = DemandaKPI[k,i];
                                    DemKPI_MaxP_DataHora = DatasN[k,i];
                                }
                            }
                            else
                            {
                                achouP = true;

                                DemKPI_MinP = DemandaKPI[k,i];
                                DemKPI_MinP_DataHora = DatasN[k,i];
                                DemKPI_MaxP = DemandaKPI[k,i];
                                DemKPI_MaxP_DataHora = DatasN[k,i];
                            }
                            break;

                        case 1: // fora ponta indutivo

                            // verifica se ja tem fora ponta indutivo
                            if (achouFPI)
                            {
                                // verifica se minimo
                                if (DemandaKPI[k,i] < DemKPI_MinFPI)
                                {
                                    DemKPI_MinFPI = DemandaKPI[k, i];
                                    DemKPI_MinFPI_DataHora = DatasN[k, i];
                                }

                                // verifica se maximo
                                if (DemandaKPI[k, i] > DemKPI_MaxFPI)
                                {
                                    DemKPI_MaxFPI = DemandaKPI[k, i];
                                    DemKPI_MaxFPI_DataHora = DatasN[k, i];
                                }
                            }
                            else
                            {
                                achouFPI = true;

                                DemKPI_MinFPI = DemandaKPI[k, i];
                                DemKPI_MinFPI_DataHora = DatasN[k, i];
                                DemKPI_MaxFPI = DemandaKPI[k, i];
                                DemKPI_MaxFPI_DataHora = DatasN[k, i];
                            }
                            break;

                        case 2: // fora ponta capacitivo

                            // verifica se ja tem fora ponta capacitivo
                            if (achouFPC)
                            {
                                // verifica se minimo
                                if (DemandaKPI[k, i] < DemKPI_MinFPC)
                                {
                                    DemKPI_MinFPC = DemandaKPI[k, i];
                                    DemKPI_MinFPC_DataHora = DatasN[k, i];
                                }

                                // verifica se maximo
                                if (DemandaKPI[k, i] > DemKPI_MaxFPC)
                                {
                                    DemKPI_MaxFPC = DemandaKPI[k, i];
                                    DemKPI_MaxFPC_DataHora = DatasN[k, i];
                                }
                            }
                            else
                            {
                                achouFPC = true;

                                DemKPI_MinFPC = DemandaKPI[k, i];
                                DemKPI_MinFPC_DataHora = DatasN[k, i];
                                DemKPI_MaxFPC = DemandaKPI[k, i];
                                DemKPI_MaxFPC_DataHora = DatasN[k, i];
                            }
                            break;
                    }
                }
            }

            // demanda / KPI
            ViewBag.DemKPI_MaxP = string.Format("{0:#,##0.00}", DemKPI_MaxP);
            if (DemKPI_MaxP_DataHora.Year != 2000)
                ViewBag.DemKPI_MaxP_DataHora = string.Format("{0:g}", DemKPI_MaxP_DataHora);
            else
                ViewBag.DemKPI_MaxP_DataHora = "--/--/---- --:--";
            ViewBag.DemKPI_MaxP_DataHoraN = DemKPI_MaxP_DataHora;

            ViewBag.DemKPI_MaxFPI = string.Format("{0:#,##0.00}", DemKPI_MaxFPI);
            if (DemKPI_MaxFPI_DataHora.Year != 2000)
                ViewBag.DemKPI_MaxFPI_DataHora = string.Format("{0:g}", DemKPI_MaxFPI_DataHora);
            else
                ViewBag.DemKPI_MaxFPI_DataHora = "--/--/---- --:--";
            ViewBag.DemKPI_MaxFPI_DataHoraN = DemKPI_MaxFPI_DataHora;

            ViewBag.DemKPI_MaxFPC = string.Format("{0:#,##0.00}", DemKPI_MaxFPC);
            if (DemKPI_MaxFPC_DataHora.Year != 2000)
                ViewBag.DemKPI_MaxFPC_DataHora = string.Format("{0:g}", DemKPI_MaxFPC_DataHora);
            else
                ViewBag.DemKPI_MaxFPC_DataHora = "--/--/---- --:--";
            ViewBag.DemKPI_MaxFPC_DataHoraN = DemKPI_MaxFPC_DataHora;

            ViewBag.DemKPI_MinP = string.Format("{0:#,##0.00}", DemKPI_MinP);
            if (DemKPI_MinP_DataHora.Year != 2000)
                ViewBag.DemKPI_MinP_DataHora = string.Format("{0:g}", DemKPI_MinP_DataHora);
            else
                ViewBag.DemKPI_MinP_DataHora = "--/--/---- --:--";
            ViewBag.DemKPI_MinP_DataHoraN = DemKPI_MinP_DataHora;

            ViewBag.DemKPI_MinFPI = string.Format("{0:#,##0.00}", DemKPI_MinFPI);
            if (DemKPI_MinFPI_DataHora.Year != 2000)
                ViewBag.DemKPI_MinFPI_DataHora = string.Format("{0:g}", DemKPI_MinFPI_DataHora);
            else
                ViewBag.DemKPI_MinFPI_DataHora = "--/--/---- --:--";
            ViewBag.DemKPI_MinFPI_DataHoraN = DemKPI_MinFPI_DataHora;

            ViewBag.DemKPI_MinFPC = string.Format("{0:#,##0.00}", DemKPI_MinFPC);
            if (DemKPI_MinFPC_DataHora.Year != 2000)
                ViewBag.DemKPI_MinFPC_DataHora = string.Format("{0:g}", DemKPI_MinFPC_DataHora);
            else
                ViewBag.DemKPI_MinFPC_DataHora = "--/--/---- --:--";
            ViewBag.DemKPI_MinFPC_DataHoraN = DemKPI_MinFPC_DataHora;

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(inicio.Year, inicio.Month, inicio.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddDays(7);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // KPI Demanda Semanal XLS
        private HSSFWorkbook KPI_Demanda_Semanal_XLS(int IDCliente, int IDMedicao, int idKPI, int TipoRelat)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // dias da semana
            int k = 0;
            int i;
            IRow row;
            ISheet sheet;
            int rowIndex;

            for (k = 0; k < 7; k++)
            {
                // cria planilha
                sheet = workbook.CreateSheet(string.Format("{0:dddd}", ViewBag.DatasN[k, 0]));

                // cabecalho
                string[] cabecalho = { "Data e Hora", "Período", ViewBag.UnidadeRelat, "Demanda Ativa", ViewBag.NomeKPI };

                // adiciona cabecalho
                rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

                // percorre valores
                for (i = 1; i < 97; i++)
                {
                    // adiciona linha
                    row = sheet.CreateRow(rowIndex);

                    // data e hora
                    datahoraCelulaXLS(row, 0, ViewBag.DatasN[k, i], _datahoraStyle);

                    // periodo
                    numeroCelulaXLS(row, 1, ViewBag.Periodo[k, i], _intCellStyle);

                    // demanda KPI
                    numeroCelulaXLS(row, 2, ViewBag.Demanda[k, i], _2CellStyle);

                    // demanda ativa
                    numeroCelulaXLS(row, 3, ViewBag.Demanda[k, i], _1CellStyle);

                    // KPI
                    numeroCelulaXLS(row, 4, ViewBag.valorKPI, _intCellStyle);

                    // proxima
                    rowIndex++;
                }

                // largura de cada coluna
                for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                    sheet.SetColumnWidth(i, 6000);
            }

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "KPI - " + ViewBag.NomeRelat + " (" + ViewBag.UnidadeRelat + ")", "Semanal", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Demanda", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.DemKPI_MaxFPC), _2CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.DemKPI_MaxFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.DemKPI_MaxFPI), _2CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.DemKPI_MaxFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.DemKPI_MaxP), _2CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.DemKPI_MaxP_DataHoraN, _datahoraStyle);

            // demanda minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.DemKPI_MinFPC), _2CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.DemKPI_MinFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.DemKPI_MinFPI), _2CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.DemKPI_MinFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.DemKPI_MinP), _2CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.DemKPI_MinP_DataHoraN, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // KPI Demanda Mensal
        private void KPI_Demanda_Mensal(int IDCliente, int IDMedicao, int idKPI, DATAHORA data_hora, int TipoRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_MENSAL relatorio = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise = new RELAT_MENSAL_ANALISE();

            RELAT_MENSAL relatorio_sim = new RELAT_MENSAL();
            RELAT_MENSAL_ANALISE analise_sim = new RELAT_MENSAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int TipoSimula = 0;

            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le configuracao do KPI
            KPIConfiguracaoMetodos kpiMetodos = new KPIConfiguracaoMetodos();
            KPIConfiguracaoDominio kpi = kpiMetodos.ListarPorId(idKPI);


            // calcula valores
            retorno = SmCalcDB_Energia_RelatMensal((char)0, ref config_interface, (char)0, (char)TipoSimula, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_MENSAL(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // numero de dias do mes
            int NumDiasMes = relatorio.num_dias;

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[NumDiasMes - 1].datahora.data.ano,
                                   (int)relatorio.registro[NumDiasMes - 1].datahora.data.mes,
                                   1, 0, 15, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var DemandaKPIP = new double[42];
            var DemandaKPIFPI = new double[42];
            var DemandaKPIFPC = new double[42];
            var ValoresKPI = new double[42];
            var DemandaP = new double[42];
            var DemandaFPI = new double[42];
            var DemandaFPC = new double[42];
            var Datas = new string[42];
            var DatasN = new DateTime[42];
            var Dias = new string[42];

            double DemKPI_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            DateTime strDataFim = new DateTime((int)relatorio.registro[NumDiasMes-1].datahora.data.ano,
                                   (int)relatorio.registro[NumDiasMes-1].datahora.data.mes,
                                   (int)relatorio.registro[NumDiasMes-1].datahora.data.dia,
                                   (int)relatorio.registro[NumDiasMes-1].datahora.hora.hora,
                                   (int)relatorio.registro[NumDiasMes-1].datahora.hora.min, 0);

            // valores do KPI
            List<KPIHistDominio> histKPI = Get_valoresKPI(kpi, medicao, strData, strDataFim);

            // valor KPI
            double valorKPI = histKPI[0].valorKPI;

            // volta 1 dia para a primeira barra
            strData = strData.AddDays(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < (NumDiasMes + 2); i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Dias[i] = strData.ToString("dd/MM");

                // KPI
                valorKPI = Get_valorKPI(histKPI, strData);

                // proximo dia
                strData = strData.AddDays(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    DemandaKPIP[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[0].valor[0], valorKPI);
                    DemandaKPIFPI[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[0].valor[1], valorKPI);
                    DemandaKPIFPC[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[0].valor[2], valorKPI);  

                    DemandaP[i] = relatorio.registro[0].valor[0];
                    DemandaFPI[i] = relatorio.registro[0].valor[1];
                    DemandaFPC[i] = relatorio.registro[0].valor[2];
                }

                if (i == (NumDiasMes + 1))
                {
                    // zera
                    DemandaKPIP[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[NumDiasMes - 1].valor[0], valorKPI);
                    DemandaKPIFPI[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[NumDiasMes - 1].valor[1], valorKPI);
                    DemandaKPIFPC[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[NumDiasMes - 1].valor[2], valorKPI);

                    DemandaP[i] = relatorio.registro[NumDiasMes - 1].valor[0];
                    DemandaFPI[i] = relatorio.registro[NumDiasMes - 1].valor[1];
                    DemandaFPC[i] = relatorio.registro[NumDiasMes - 1].valor[2];
                }

                if (i >= 1 && i <= NumDiasMes)
                {
                    // copia
                    j = i - 1;

                    DemandaKPIP[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[j].valor[0], valorKPI);
                    DemandaKPIFPI[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[j].valor[1], valorKPI);
                    DemandaKPIFPC[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[j].valor[2], valorKPI);

                    DemandaP[i] = relatorio.registro[j].valor[0];
                    DemandaFPI[i] = relatorio.registro[j].valor[1];
                    DemandaFPC[i] = relatorio.registro[j].valor[2];

                    // verifica demanda maxima
                    if (DemandaKPIP[i] > DemKPI_max_grafico)
                        DemKPI_max_grafico = DemandaKPIP[i];

                    if (DemandaKPIFPI[i] > DemKPI_max_grafico)
                        DemKPI_max_grafico = DemandaKPIFPI[i];

                    if (DemandaKPIFPC[i] > DemKPI_max_grafico)
                        DemKPI_max_grafico = DemandaKPIFPC[i];
                }
            }

            DemKPI_max_grafico = DemKPI_max_grafico * 1.1;

            if (DemKPI_max_grafico < 1.0)
            {
                //DemKPI_max_grafico = 1.0;
            }

            ViewBag.DemKPIMaxGrafico = DemKPI_max_grafico;

            ViewBag.DemandaKPIP = DemandaKPIP;
            ViewBag.DemandaKPIFPI = DemandaKPIFPI;
            ViewBag.DemandaKPIFPC = DemandaKPIFPC;
            ViewBag.ValoresKPI = ValoresKPI;
            ViewBag.DemandaP = DemandaP;
            ViewBag.DemandaFPI = DemandaFPI;
            ViewBag.DemandaFPC = DemandaFPC;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Dias = Dias;

            ViewBag.valorKPI = valorKPI;

            // informacoes do relatorio
            InformacoesRelat(kpi, TipoRelat);

            ViewBag.PeriodoRelat = string.Format("Mensal");

            // data atual
            ViewBag.DataAtual = string.Format("{0:MM/yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:Y}", DataAtual);

            ViewBag.NumDiasMes = NumDiasMes;


            // estatisticas
            double DemKPI_MinP = DemandaKPIP[1];
            DateTime DemKPI_MinP_DataHora = DatasN[1];
            double DemKPI_MaxP = DemandaKPIP[1];
            DateTime DemKPI_MaxP_DataHora = DatasN[1];

            double DemKPI_MinFPI = DemandaKPIFPI[1];
            DateTime DemKPI_MinFPI_DataHora = DatasN[1];
            double DemKPI_MaxFPI = DemandaKPIFPI[1];
            DateTime DemKPI_MaxFPI_DataHora = DatasN[1];

            double DemKPI_MinFPC = DemandaKPIFPC[1];
            DateTime DemKPI_MinFPC_DataHora = DatasN[1];
            double DemKPI_MaxFPC = DemandaKPIFPC[1];
            DateTime DemKPI_MaxFPC_DataHora = DatasN[1];

            // percorre valores
            for (i = 1; i < (NumDiasMes+1); i++)
            {
                // verifica se minimo
                if (DemandaKPIP[i] < DemKPI_MinP)
                {
                    DemKPI_MinP = DemandaKPIP[i];
                    DemKPI_MinP_DataHora = DatasN[i];
                }

                // verifica se maximo
                if (DemandaKPIP[i] > DemKPI_MaxP)
                {
                    DemKPI_MaxP = DemandaKPIP[i];
                    DemKPI_MaxP_DataHora = DatasN[i];
                }

                // verifica se minimo
                if (DemandaKPIFPI[i] < DemKPI_MinFPI)
                {
                    DemKPI_MinFPI = DemandaKPIFPI[i];
                    DemKPI_MinFPI_DataHora = DatasN[i];
                }

                // verifica se maximo
                if (DemandaKPIFPI[i] > DemKPI_MaxFPI)
                {
                    DemKPI_MaxFPI = DemandaKPIFPI[i];
                    DemKPI_MaxFPI_DataHora = DatasN[i];
                }

                // verifica se minimo
                if (DemandaKPIFPC[i] < DemKPI_MinFPC)
                {
                    DemKPI_MinFPC = DemandaKPIFPC[i];
                    DemKPI_MinFPC_DataHora = DatasN[i];
                }

                // verifica se maximo
                if (DemandaKPIFPC[i] > DemKPI_MaxFPC)
                {
                    DemKPI_MaxFPC = DemandaKPIFPC[i];
                    DemKPI_MaxFPC_DataHora = DatasN[i];
                }
            }

            // demanda / KPI
            ViewBag.DemKPI_MaxP = string.Format("{0:#,##0.00}", DemKPI_MaxP);
            if (DemKPI_MaxP_DataHora.Year != 2000)
                ViewBag.DemKPI_MaxP_DataHora = string.Format("{0:g}", DemKPI_MaxP_DataHora);
            else
                ViewBag.DemKPI_MaxP_DataHora = "--/--/---- --:--";
            ViewBag.DemKPI_MaxP_DataHoraN = DemKPI_MaxP_DataHora;

            ViewBag.DemKPI_MaxFPI = string.Format("{0:#,##0.00}", DemKPI_MaxFPI);
            if (DemKPI_MaxFPI_DataHora.Year != 2000)
                ViewBag.DemKPI_MaxFPI_DataHora = string.Format("{0:g}", DemKPI_MaxFPI_DataHora);
            else
                ViewBag.DemKPI_MaxFPI_DataHora = "--/--/---- --:--";
            ViewBag.DemKPI_MaxFPI_DataHoraN = DemKPI_MaxFPI_DataHora;

            ViewBag.DemKPI_MaxFPC = string.Format("{0:#,##0.00}", DemKPI_MaxFPC);
            if (DemKPI_MaxFPC_DataHora.Year != 2000)
                ViewBag.DemKPI_MaxFPC_DataHora = string.Format("{0:g}", DemKPI_MaxFPC_DataHora);
            else
                ViewBag.DemKPI_MaxFPC_DataHora = "--/--/---- --:--";
            ViewBag.DemKPI_MaxFPC_DataHoraN = DemKPI_MaxFPC_DataHora;

            ViewBag.DemKPI_MinP = string.Format("{0:#,##0.00}", DemKPI_MinP);
            if (DemKPI_MinP_DataHora.Year != 2000)
                ViewBag.DemKPI_MinP_DataHora = string.Format("{0:g}", DemKPI_MinP_DataHora);
            else
                ViewBag.DemKPI_MinP_DataHora = "--/--/---- --:--";
            ViewBag.DemKPI_MinP_DataHoraN = DemKPI_MinP_DataHora;

            ViewBag.DemKPI_MinFPI = string.Format("{0:#,##0.00}", DemKPI_MinFPI);
            if (DemKPI_MinFPI_DataHora.Year != 2000)
                ViewBag.DemKPI_MinFPI_DataHora = string.Format("{0:g}", DemKPI_MinFPI_DataHora);
            else
                ViewBag.DemKPI_MinFPI_DataHora = "--/--/---- --:--";
            ViewBag.DemKPI_MinFPI_DataHoraN = DemKPI_MinFPI_DataHora;

            ViewBag.DemKPI_MinFPC = string.Format("{0:#,##0.00}", DemKPI_MinFPC);
            if (DemKPI_MinFPC_DataHora.Year != 2000)
                ViewBag.DemKPI_MinFPC_DataHora = string.Format("{0:g}", DemKPI_MinFPC_DataHora);
            else
                ViewBag.DemKPI_MinFPC_DataHora = "--/--/---- --:--";
            ViewBag.DemKPI_MinFPC_DataHoraN = DemKPI_MinFPC_DataHora;

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddMonths(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // KPI Demanda Mensal XLS
        private HSSFWorkbook KPI_Demanda_Mensal_XLS(int IDCliente, int IDMedicao, int idKPI, int TipoRelat)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < (ViewBag.NumDiasMes + 1); i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // demanda fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.DemandaKPIFPC[i], _2CellStyle);

                // demanda fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.DemandaKPIFPI[i], _2CellStyle);

                // demanda ponta
                numeroCelulaXLS(row, 3, ViewBag.DemandaKPIP[i], _2CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "KPI - " + ViewBag.NomeRelat + " (" + ViewBag.UnidadeRelat + ")", "Mensal", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Demanda", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.DemKPI_MaxFPC), _2CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.DemKPI_MaxFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.DemKPI_MaxFPI), _2CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.DemKPI_MaxFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.DemKPI_MaxP), _2CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.DemKPI_MaxP_DataHoraN, _datahoraStyle);

            // demanda minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.DemKPI_MinFPC), _2CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.DemKPI_MinFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.DemKPI_MinFPI), _2CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.DemKPI_MinFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.DemKPI_MinP), _2CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.DemKPI_MinP_DataHoraN, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

        // KPI Demanda Anual
        private void KPI_Demanda_Anual(int IDCliente, int IDMedicao, int idKPI, DATAHORA data_hora, int TipoRelat)
        {
            // funcao relatorio
            int retorno;

            CONFIG_INTERFACE config_interface = new CONFIG_INTERFACE();
            RELAT_ANUAL relatorio = new RELAT_ANUAL();
            RELAT_ANUAL_ANALISE analise = new RELAT_ANUAL_ANALISE();

            RELAT_ANUAL relatorio_sim = new RELAT_ANUAL();
            RELAT_ANUAL_ANALISE analise_sim = new RELAT_ANUAL_ANALISE();

            // preenche solicitacao
            config_interface.sweb.id_cliente = IDCliente;
            config_interface.sweb.id_medicao = IDMedicao;

            // simulacao
            int TipoSimula = 0;

            // le configuracao medicao
            var medicoesMetodos = new MedicoesMetodos();
            var medicao = medicoesMetodos.ListarPorId(IDMedicao);

            // le configuracao do KPI
            KPIConfiguracaoMetodos kpiMetodos = new KPIConfiguracaoMetodos();
            KPIConfiguracaoDominio kpi = kpiMetodos.ListarPorId(idKPI);


            // calcula valores
            retorno = SmCalcDB_Energia_RelatAnual((char)0, ref config_interface, (char)0, (char)TipoSimula, ref data_hora, ref relatorio, ref analise, ref relatorio_sim, ref analise_sim);
            FuncoesRelat.DivisaoUnidade_RELAT_ANUAL(ref relatorio, ref analise, medicao.IDTipoUnidadePotencia);

            // converte data e hora
            DateTime DataAtual = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   1, 1, 0, 15, 0);

            // lista de erros
            var listaErros = new List<string>();

            if (isUser.isGESTAL(ViewBag._IDTipoAcesso))
            {
                if (retorno > 0)
                    listaErros.Add(string.Format("Retorno {0}", retorno));
            }

            ViewBag.listaErros = listaErros;
            ViewBag.Retorno = retorno;

            // grafico
            var DemandaKPIP = new double[14];
            var DemandaKPIFPI = new double[14];
            var DemandaKPIFPC = new double[14];
            var ValoresKPI = new double[14];
            var DemandaP = new double[14];
            var DemandaFPI = new double[14];
            var DemandaFPC = new double[14];
            var Datas = new string[14];
            var DatasN = new DateTime[14];
            var Meses = new string[14];

            double DemKPI_max_grafico = 0.0;

            // valores
            DateTime strData = new DateTime((int)relatorio.registro[0].datahora.data.ano,
                                   (int)relatorio.registro[0].datahora.data.mes,
                                   (int)relatorio.registro[0].datahora.data.dia,
                                   (int)relatorio.registro[0].datahora.hora.hora,
                                   (int)relatorio.registro[0].datahora.hora.min, 0);

            DateTime strDataFim = new DateTime((int)relatorio.registro[11].datahora.data.ano,
                                   (int)relatorio.registro[11].datahora.data.mes,
                                   (int)relatorio.registro[11].datahora.data.dia,
                                   (int)relatorio.registro[11].datahora.hora.hora,
                                   (int)relatorio.registro[11].datahora.hora.min, 0);

            // valores do KPI
            List<KPIHistDominio> histKPI = Get_valoresKPI(kpi, medicao, strData, strDataFim);

            // valor KPI
            double valorKPI = histKPI[0].valorKPI;

            // volta 1 mes para a primeira barra
            strData = strData.AddMonths(-1);

            int i = 0;
            int j = 0;

            for (i = 0; i < 14; i++)
            {
                // formata label
                Datas[i] = strData.ToString("yyyy-MM-dd HH:mm:ss");
                DatasN[i] = strData;
                Meses[i] = string.Format("{0:MMMM}", strData);

                // KPI
                valorKPI = Get_valorKPI(histKPI, strData);

                // proximo mes
                strData = strData.AddMonths(1);

                // verifica se primeira ou ultima barra
                if (i == 0)
                {
                    // zera
                    DemandaKPIP[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[0].valor[0], valorKPI);
                    DemandaKPIFPI[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[0].valor[1], valorKPI);
                    DemandaKPIFPC[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[0].valor[2], valorKPI);  

                    DemandaP[i] = relatorio.registro[0].valor[0];
                    DemandaFPI[i] = relatorio.registro[0].valor[1];
                    DemandaFPC[i] = relatorio.registro[0].valor[2];
                }

                if (i == 13)
                {
                    // zera
                    DemandaKPIP[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[11].valor[0], valorKPI);
                    DemandaKPIFPI[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[11].valor[1], valorKPI);
                    DemandaKPIFPC[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[11].valor[2], valorKPI);

                    DemandaP[i] = relatorio.registro[11].valor[0];
                    DemandaFPI[i] = relatorio.registro[11].valor[1];
                    DemandaFPC[i] = relatorio.registro[11].valor[2];
                }

                if (i >= 1 && i <= 12)
                {
                    // copia
                    j = i - 1;

                    DemandaKPIP[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[j].valor[0], valorKPI);
                    DemandaKPIFPI[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[j].valor[1], valorKPI);
                    DemandaKPIFPC[i] = Calcula_Energia_Grandeza(kpi.IDTipoRazao, relatorio.registro[j].valor[2], valorKPI);

                    DemandaP[i] = relatorio.registro[j].valor[0];
                    DemandaFPI[i] = relatorio.registro[j].valor[1];
                    DemandaFPC[i] = relatorio.registro[j].valor[2];

                    // verifica demanda maxima
                    if (DemandaKPIP[i] > DemKPI_max_grafico)
                        DemKPI_max_grafico = DemandaKPIP[i];

                    if (DemandaKPIFPI[i] > DemKPI_max_grafico)
                        DemKPI_max_grafico = DemandaKPIFPI[i];

                    if (DemandaKPIFPC[i] > DemKPI_max_grafico)
                        DemKPI_max_grafico = DemandaKPIFPC[i];
                }
            }

            DemKPI_max_grafico = DemKPI_max_grafico * 1.1;

            if (DemKPI_max_grafico < 1.0)
            {
                //DemKPI_max_grafico = 1.0;
            }

            ViewBag.DemKPIMaxGrafico = DemKPI_max_grafico;

            ViewBag.DemandaKPIP = DemandaKPIP;
            ViewBag.DemandaKPIFPI = DemandaKPIFPI;
            ViewBag.DemandaKPIFPC = DemandaKPIFPC;
            ViewBag.ValoresKPI = ValoresKPI;
            ViewBag.DemandaP = DemandaP;
            ViewBag.DemandaFPI = DemandaFPI;
            ViewBag.DemandaFPC = DemandaFPC;
            ViewBag.Datas = Datas;
            ViewBag.DatasN = DatasN;
            ViewBag.Meses = Meses;

            ViewBag.valorKPI = valorKPI;

            // informacoes do relatorio
            InformacoesRelat(kpi, TipoRelat);

            ViewBag.PeriodoRelat = string.Format("Anual");

            // data atual
            ViewBag.DataAtual = string.Format("{0:yyyy}", DataAtual);
            ViewBag.DataAtualN = DataAtual;
            ViewBag.DataTextoAtual = string.Format("{0:yyyy}", DataAtual);


            // estatisticas
            double DemKPI_MinP = DemandaKPIP[1];
            DateTime DemKPI_MinP_DataHora = DatasN[1];
            double DemKPI_MaxP = DemandaKPIP[1];
            DateTime DemKPI_MaxP_DataHora = DatasN[1];

            double DemKPI_MinFPI = DemandaKPIFPI[1];
            DateTime DemKPI_MinFPI_DataHora = DatasN[1];
            double DemKPI_MaxFPI = DemandaKPIFPI[1];
            DateTime DemKPI_MaxFPI_DataHora = DatasN[1];

            double DemKPI_MinFPC = DemandaKPIFPC[1];
            DateTime DemKPI_MinFPC_DataHora = DatasN[1];
            double DemKPI_MaxFPC = DemandaKPIFPC[1];
            DateTime DemKPI_MaxFPC_DataHora = DatasN[1];

            // percorre valores
            for (i = 1; i < 13; i++)
            {
                // verifica se minimo
                if (DemandaKPIP[i] < DemKPI_MinP)
                {
                    DemKPI_MinP = DemandaKPIP[i];
                    DemKPI_MinP_DataHora = DatasN[i];
                }

                // verifica se maximo
                if (DemandaKPIP[i] > DemKPI_MaxP)
                {
                    DemKPI_MaxP = DemandaKPIP[i];
                    DemKPI_MaxP_DataHora = DatasN[i];
                }

                // verifica se minimo
                if (DemandaKPIFPI[i] < DemKPI_MinFPI)
                {
                    DemKPI_MinFPI = DemandaKPIFPI[i];
                    DemKPI_MinFPI_DataHora = DatasN[i];
                }

                // verifica se maximo
                if (DemandaKPIFPI[i] > DemKPI_MaxFPI)
                {
                    DemKPI_MaxFPI = DemandaKPIFPI[i];
                    DemKPI_MaxFPI_DataHora = DatasN[i];
                }

                // verifica se minimo
                if (DemandaKPIFPC[i] < DemKPI_MinFPC)
                {
                    DemKPI_MinFPC = DemandaKPIFPC[i];
                    DemKPI_MinFPC_DataHora = DatasN[i];
                }

                // verifica se maximo
                if (DemandaKPIFPC[i] > DemKPI_MaxFPC)
                {
                    DemKPI_MaxFPC = DemandaKPIFPC[i];
                    DemKPI_MaxFPC_DataHora = DatasN[i];
                }
            }

            // demanda / KPI
            ViewBag.DemKPI_MaxP = string.Format("{0:#,##0.00}", DemKPI_MaxP);
            if (DemKPI_MaxP_DataHora.Year != 2000)
                ViewBag.DemKPI_MaxP_DataHora = string.Format("{0:g}", DemKPI_MaxP_DataHora);
            else
                ViewBag.DemKPI_MaxP_DataHora = "--/--/---- --:--";
            ViewBag.DemKPI_MaxP_DataHoraN = DemKPI_MaxP_DataHora;

            ViewBag.DemKPI_MaxFPI = string.Format("{0:#,##0.00}", DemKPI_MaxFPI);
            if (DemKPI_MaxFPI_DataHora.Year != 2000)
                ViewBag.DemKPI_MaxFPI_DataHora = string.Format("{0:g}", DemKPI_MaxFPI_DataHora);
            else
                ViewBag.DemKPI_MaxFPI_DataHora = "--/--/---- --:--";
            ViewBag.DemKPI_MaxFPI_DataHoraN = DemKPI_MaxFPI_DataHora;

            ViewBag.DemKPI_MaxFPC = string.Format("{0:#,##0.00}", DemKPI_MaxFPC);
            if (DemKPI_MaxFPC_DataHora.Year != 2000)
                ViewBag.DemKPI_MaxFPC_DataHora = string.Format("{0:g}", DemKPI_MaxFPC_DataHora);
            else
                ViewBag.DemKPI_MaxFPC_DataHora = "--/--/---- --:--";
            ViewBag.DemKPI_MaxFPC_DataHoraN = DemKPI_MaxFPC_DataHora;

            ViewBag.DemKPI_MinP = string.Format("{0:#,##0.00}", DemKPI_MinP);
            if (DemKPI_MinP_DataHora.Year != 2000)
                ViewBag.DemKPI_MinP_DataHora = string.Format("{0:g}", DemKPI_MinP_DataHora);
            else
                ViewBag.DemKPI_MinP_DataHora = "--/--/---- --:--";
            ViewBag.DemKPI_MinP_DataHoraN = DemKPI_MinP_DataHora;

            ViewBag.DemKPI_MinFPI = string.Format("{0:#,##0.00}", DemKPI_MinFPI);
            if (DemKPI_MinFPI_DataHora.Year != 2000)
                ViewBag.DemKPI_MinFPI_DataHora = string.Format("{0:g}", DemKPI_MinFPI_DataHora);
            else
                ViewBag.DemKPI_MinFPI_DataHora = "--/--/---- --:--";
            ViewBag.DemKPI_MinFPI_DataHoraN = DemKPI_MinFPI_DataHora;

            ViewBag.DemKPI_MinFPC = string.Format("{0:#,##0.00}", DemKPI_MinFPC);
            if (DemKPI_MinFPC_DataHora.Year != 2000)
                ViewBag.DemKPI_MinFPC_DataHora = string.Format("{0:g}", DemKPI_MinFPC_DataHora);
            else
                ViewBag.DemKPI_MinFPC_DataHora = "--/--/---- --:--";
            ViewBag.DemKPI_MinFPC_DataHoraN = DemKPI_MinFPC_DataHora;

            //
            // Eventos
            //

            DateTime data_hora_ini = new DateTime(DataAtual.Year, DataAtual.Month, DataAtual.Day, 0, 0, 0);
            DateTime data_hora_fim = data_hora_ini.AddYears(1);

            ViewBag.data_hora_ini = data_hora_ini;

            ViewBag.TemEventos = false;

            return;
        }

        // KPI Demanda Anual XLS
        private HSSFWorkbook KPI_Demanda_Anual_XLS(int IDCliente, int IDMedicao, int idKPI, int TipoRelat)
        {
            // cria excel
            var workbook = new HSSFWorkbook();

            // cria estilos
            ICellStyle _datahoraStyle = criaEstiloXLS(workbook, 10);
            ICellStyle _dataStyle = criaEstiloXLS(workbook, 11);
            ICellStyle _intCellStyle = criaEstiloXLS(workbook, 0);
            ICellStyle _1CellStyle = criaEstiloXLS(workbook, 1);
            ICellStyle _2CellStyle = criaEstiloXLS(workbook, 2);
            ICellStyle _3CellStyle = criaEstiloXLS(workbook, 3);
            ICellStyle _negritoCellStyle = criaEstiloXLS(workbook, 20);

            //
            // TABELA
            //

            // cria planilha
            var sheet = workbook.CreateSheet("Tabela");

            // cabecalho
            string[] cabecalho = { "Data e Hora", "Fora de Ponta Capacitivo", "Fora de Ponta Indutivo", "Ponta" };

            // adiciona cabecalho
            int rowIndex = cabecalhoTabelaXLS(workbook, sheet, cabecalho);

            // adiciona linhas
            int i;
            IRow row;

            // percorre valores
            for (i = 1; i < 13; i++)
            {
                // adiciona linha
                row = sheet.CreateRow(rowIndex);

                // data e hora
                datahoraCelulaXLS(row, 0, ViewBag.DatasN[i], _dataStyle);

                // demanda fora de ponta capacitivo
                numeroCelulaXLS(row, 1, ViewBag.DemandaKPIFPC[i], _2CellStyle);

                // demanda fora de ponta indutivo
                numeroCelulaXLS(row, 2, ViewBag.DemandaKPIFPI[i], _2CellStyle);

                // demanda ponta
                numeroCelulaXLS(row, 3, ViewBag.DemandaKPIP[i], _2CellStyle);

                // proxima
                rowIndex++;
            }

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(0).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            //
            // RESUMO
            //

            // cria planilha
            sheet = workbook.CreateSheet("Resumo");

            // cabecalho
            rowIndex = cabecalhoResumoXLS(workbook, sheet, "KPI - " + ViewBag.NomeRelat + " (" + ViewBag.UnidadeRelat + ")", "Anual", _negritoCellStyle);

            // cabecalho ponta e fora de ponta
            string[] cab_dem = { "Demanda", "Fora de Ponta Capacitivo", "Data", "Fora de Ponta Indutivo", "Data", "Ponta", "Data", "" };
            cabecalhoTabelaXLS(workbook, sheet, cab_dem, rowIndex++);

            // demanda maxima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Máxima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.DemKPI_MaxFPC), _2CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.DemKPI_MaxFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.DemKPI_MaxFPI), _2CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.DemKPI_MaxFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.DemKPI_MaxP), _2CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.DemKPI_MaxP_DataHoraN, _datahoraStyle);

            // demanda minima
            row = sheet.CreateRow(rowIndex++);
            textoCelulaXLS(row, 0, "Mínima", _negritoCellStyle);
            numeroCelulaXLS(row, 1, double.Parse(ViewBag.DemKPI_MinFPC), _2CellStyle);
            datahoraCelulaXLS(row, 2, ViewBag.DemKPI_MinFPC_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 3, double.Parse(ViewBag.DemKPI_MinFPI), _2CellStyle);
            datahoraCelulaXLS(row, 4, ViewBag.DemKPI_MinFPI_DataHoraN, _datahoraStyle);
            numeroCelulaXLS(row, 5, double.Parse(ViewBag.DemKPI_MinP), _2CellStyle);
            datahoraCelulaXLS(row, 6, ViewBag.DemKPI_MinP_DataHoraN, _datahoraStyle);

            // largura de cada coluna
            for (i = 0; i < sheet.GetRow(11).LastCellNum; i++)
                sheet.SetColumnWidth(i, 7000);

            // retorna planilha
            return workbook;
        }

   }
}