﻿using NPOI.HSSF.UserModel;
using Rotativa.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web.Mvc;

using SmartEnergyLib.Declaracoes;
using SmartEnergyLib.SQL;
using SmartEnergyLib.Funcoes;

namespace SmartEnergy.Controllers
{
    [Authorize]
    public partial class KpiController
    {

        // GET: KPI Grafico
        private ActionResult KPI_Grafico_Show_PelaMedicao(int IDCliente, int IDMedicao, int TipoRelat, int tipo_arquivo = 0)
        {
            // le supervisao da medicao
            var medicoesMetodos = new SupervMedicoesMetodos();
            var listaMedicoes = medicoesMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

            // salva cookie 
            CookieStore.SalvaCookie_Int("_IDMedicao", IDMedicao);
            CookieStore.SalvaCookie_Int("IDTipoMedicao", listaMedicoes.IDTipoMedicao_MD);
            CookieStore.SalvaCookie_String("MedicaoNome", listaMedicoes.NMED);
            CookieStore.SalvaCookie_String("UnidadeNome", listaMedicoes.NUN);
            CookieStore.SalvaCookie_String("GrupoNome", listaMedicoes.NGU);
            CookieStore.SalvaCookie_Int("_IDGateway", listaMedicoes.IDGW_GW);
            CookieStore.SalvaCookie_Int("IDTipoGateway", listaMedicoes.IDTipoGateway_GW);
            CookieStore.SalvaCookie_Int("KPI_Tipo", TipoRelat);
            CookieStore.SalvaCookie_Int("_IDKPI", 0);

            int IDGateway = listaMedicoes.IDGW_GW;
            int IDTipoGateway = listaMedicoes.IDTipoGateway_GW;

            // le cookies
            LeCookies_SmartEnergy();

            // tipo de acesso do usuario
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // unidade de potencia
            UnidadePotencia(IDMedicao);

            // verifica tipo periodo
            if (ViewBag.Relat_TipoPeriodo == -10)
            {
                // seta para diario
                ViewBag.Relat_TipoPeriodo = 0;

                // salva cookie 
                CookieStore.SalvaCookie_Int("Relat_TipoPeriodo", 0);
            }

            // tipo periodo
            int TipoPeriodo = ViewBag.Relat_TipoPeriodo;

            // tela de ajuda - KPI 
            string PaginaAjuda = "Contato";

            switch (TipoRelat)
            {
                case 0: // indicador de economia
                    PaginaAjuda = "KPI_IndicadorEconomia";
                    break;

                case 100: // KPI demanda / Area
                    PaginaAjuda = "KPI_Demanda_Area";
                    break;

                case 101: // KPI consumo / Area
                    PaginaAjuda = "KPI_Consumo_Area";
                    break;

                case 102: // KPI demanda / temperatura
                    break;

                case 103: // KPI consumo / temperatura
                    break;

                case 104: // KPI demanda / volume
                    break;

                case 105: // KPI consumo / volume
                    break;
            }

            CookieStore.SalvaCookie_String("PaginaAjuda", PaginaAjuda);
            ViewBag.PaginaAjuda = PaginaAjuda;

            // dia atual
            DateTime datahora_ultima = DateTime.Parse(listaMedicoes.DataHora);
            DATAHORA data_hora = new DATAHORA();
            DATAHORA data_hora_fim = new DATAHORA();

            // le cookie datahora (atual ou inicial)
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");

            // verifica se existe datahora no cookie
            if (datahora_cookie.Year != 2000)
            {
                // copia data do cookie para ultima
                datahora_ultima = datahora_cookie;

                // forco hora para nao voltar 1 dia 
                datahora_ultima = Funcoes_Converte.ChangeDateTimePart(datahora_ultima, Funcoes_Converte.DateTimePart.Hours, 12);
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_ultima);

            // caso for muito cedo, pego do dia anterior
            if (datahora_ultima.Hour < 12)
            {
                // caso NAO for eventos, zero o horario
                datahora_ultima = Funcoes_Converte.ChangeDateTimePart(datahora_ultima, Funcoes_Converte.DateTimePart.Hours, 0);
                datahora_ultima = Funcoes_Converte.ChangeDateTimePart(datahora_ultima, Funcoes_Converte.DateTimePart.Minutes, 0);
                datahora_ultima = Funcoes_Converte.ChangeDateTimePart(datahora_ultima, Funcoes_Converte.DateTimePart.Seconds, 0);

                datahora_ultima = datahora_ultima.AddDays(-1);

                data_hora.data.dia = (char)datahora_ultima.Day;
                data_hora.data.mes = (char)datahora_ultima.Month;
                data_hora.data.ano = (short)datahora_ultima.Year;
                data_hora.hora.hora = (char)0;
                data_hora.hora.min = (char)0;
                data_hora.hora.seg = (char)0;
            }

            // zero o horario
            datahora_ultima = Funcoes_Converte.ChangeDateTimePart(datahora_ultima, Funcoes_Converte.DateTimePart.Hours, 0);
            datahora_ultima = Funcoes_Converte.ChangeDateTimePart(datahora_ultima, Funcoes_Converte.DateTimePart.Minutes, 0);
            datahora_ultima = Funcoes_Converte.ChangeDateTimePart(datahora_ultima, Funcoes_Converte.DateTimePart.Seconds, 0);

            data_hora.hora.hora = (char)0;
            data_hora.hora.min = (char)0;
            data_hora.hora.seg = (char)0;

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_ultima);

            // le cookie datahora (final)
            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Relat_DataFim");

            // quando estiver em outros relatorios, faco sempre o periodo de 1 dia
            datahora_cookie_fim = datahora_ultima.AddDays(1);

            // verifica se fim eh menor que inicio
            if (datahora_cookie_fim < datahora_ultima)
            {
                // periodo de pelo menos 1 dia
                datahora_cookie_fim = datahora_ultima.AddDays(1);
            }

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora_fim, datahora_cookie_fim);

            // salva cookie datahora (final)
            CookieStore.SalvaCookie_Datahora("Relat_DataFim", datahora_cookie_fim);

            // tipo do relatorio
            ViewBag.TipoRelat = TipoRelat;

            // View do relatorio
            string viewRelatorio = "";

            // Planilha Excel
            var workbook = new HSSFWorkbook();

            // ler observacoes
            bool ler_observacoes = false;

            switch (TipoRelat)
            {
                case 0: // indicador de economia

                    switch (TipoPeriodo)
                    {
                        case 0: // Diario
                            KPI_IndEconomia_Diario(IDCliente, IDMedicao, data_hora);
                            viewRelatorio = "_KPI_IndEconomia_Diario";

                            if (tipo_arquivo == 2)
                                workbook = KPI_IndEconomia_Diario_XLS(IDCliente, IDMedicao);
                            break;

                        case 1: // Semanal
                            //Dem_Ativa_Semanal(IDCliente, IDMedicao, data_hora);
                            //viewRelatorio = "_Dem_Ativa_Semanal";

                            //if (tipo_arquivo == 2)
                            //    workbook = Dem_Ativa_Semanal_XLS(IDCliente, IDMedicao);
                            break;

                        case 2: // Mensal
                            //Dem_Ativa_Mensal(IDCliente, IDMedicao, data_hora);
                            //viewRelatorio = "_Dem_Ativa_Mensal";

                            //if (tipo_arquivo == 2)
                            //    workbook = Dem_Ativa_Mensal_XLS(IDCliente, IDMedicao);
                            break;

                        case 3: // Anual
                            //Dem_Ativa_Anual(IDCliente, IDMedicao, data_hora);
                            //viewRelatorio = "_Dem_Ativa_Anual";

                            //if (tipo_arquivo == 2)
                            //    workbook = Dem_Ativa_Anual_XLS(IDCliente, IDMedicao);
                            break;
                    }

                    break;

                case 100: // KPI Demanda / Area
                case 102: // KPI Demanda / Temperatura
                case 104: // KPI Demanda / Volume

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case 0: // Diario
                            KPI_Demanda_Diario_PelaMedicao(IDCliente, IDMedicao, data_hora, TipoRelat);
                            viewRelatorio = "_Dem_Area_Diario";

                            if (tipo_arquivo == 2)
                                workbook = KPI_Demanda_Diario_PelaMedicao_XLS(IDCliente, IDMedicao, TipoRelat);
                            break;

                        case 1: // Semanal
                            KPI_Demanda_Semanal_PelaMedicao(IDCliente, IDMedicao, data_hora, TipoRelat);
                            viewRelatorio = "_Dem_Area_Semanal";

                            if (tipo_arquivo == 2)
                                workbook = KPI_Demanda_Semanal_PelaMedicao_XLS(IDCliente, IDMedicao, TipoRelat);
                            break;

                        case 2: // Mensal
                            KPI_Demanda_Mensal_PelaMedicao(IDCliente, IDMedicao, data_hora, TipoRelat);
                            viewRelatorio = "_Dem_Area_Mensal";

                            if (tipo_arquivo == 2)
                                workbook = KPI_Demanda_Mensal_PelaMedicao_XLS(IDCliente, IDMedicao, TipoRelat);
                            break;

                        case 3: // Anual
                            KPI_Demanda_Anual_PelaMedicao(IDCliente, IDMedicao, data_hora, TipoRelat);
                            viewRelatorio = "_Dem_Area_Anual";

                            if (tipo_arquivo == 2)
                                workbook = KPI_Demanda_Anual_PelaMedicao_XLS(IDCliente, IDMedicao, TipoRelat);
                            break;

                    }

                    break;

                case 101: // KPI Consumo / Area
                case 103: // KPI Consumo / Temperatura
                case 105: // KPI Consumo / Volume

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case 0: // Diario
                            KPI_Consumo_Diario_PelaMedicao(IDCliente, IDMedicao, data_hora, TipoRelat);
                            viewRelatorio = "_Consumo_Area_Diario";

                            if (tipo_arquivo == 2)
                                workbook = KPI_Consumo_Diario_PelaMedicao_XLS(IDCliente, IDMedicao, TipoRelat);
                            break;

                        case 1: // Semanal
                            KPI_Consumo_Semanal_PelaMedicao(IDCliente, IDMedicao, data_hora, TipoRelat);
                            viewRelatorio = "_Consumo_Area_Semanal";

                            if (tipo_arquivo == 2)
                                workbook = KPI_Consumo_Semanal_PelaMedicao_XLS(IDCliente, IDMedicao, TipoRelat);
                            break;

                        case 2: // Mensal
                            KPI_Consumo_Mensal_PelaMedicao(IDCliente, IDMedicao, data_hora, TipoRelat);
                            viewRelatorio = "_Consumo_Area_Mensal";

                            if (tipo_arquivo == 2)
                                workbook = KPI_Consumo_Mensal_PelaMedicao_XLS(IDCliente, IDMedicao, TipoRelat);
                            break;

                        case 3: // Anual
                            KPI_Consumo_Anual_PelaMedicao(IDCliente, IDMedicao, data_hora, TipoRelat);
                            viewRelatorio = "_ConsumoArea_Anual";

                            if (tipo_arquivo == 2)
                                workbook = KPI_Consumo_Anual_PelaMedicao_XLS(IDCliente, IDMedicao, TipoRelat);
                            break;
                    }

                    break;
            }


            //
            // Observacoes
            //
            if (ler_observacoes)
            {
                // data atual
                DateTime data_hora_i = ViewBag.data_hora_ini;

                if (data_hora_i != null)
                {
                    DateTime data_hora_f = new DateTime();

                    // periodo
                    switch (TipoPeriodo)
                    {
                        case 0: // Diario
                            data_hora_f = data_hora_i.AddDays(1);
                            break;

                        case 1: // Semanal
                            data_hora_f = data_hora_i.AddDays(7);
                            break;

                        case 2: // Mensal
                            data_hora_f = data_hora_i.AddMonths(1);
                            break;

                        case 3: // Anual
                            data_hora_f = data_hora_i.AddYears(1);
                            break;

                    }

                    // tipos acesso
                    ListaTiposMetodos listaMetodos = new ListaTiposMetodos();
                    List<ListaTiposDominio> listatipos = listaMetodos.ListarTodos("TipoAcesso");

                    // le tags
                    List<ObservacaoTagsDominio> tags = listaMetodos.ListarTodos_ObservacaoTags();
                    ViewBag.tags = tags;

                    // observacao
                    ObservacaoMedicaoDominio observacao = new ObservacaoMedicaoDominio();
                    ViewBag.observacao = observacao;

                    // observacoes
                    ObservacaoMedicaoMetodos observacaoMetodos = new ObservacaoMedicaoMetodos();
                    List<ObservacaoMedicaoDominio> observacoes_lidas = observacaoMetodos.ListarPorPeriodo(IDMedicao, data_hora_i, data_hora_f);
                    List<ObservacaoMedicaoDominio> observacoes = new List<ObservacaoMedicaoDominio>();

                    // le observacoes
                    if (observacoes_lidas != null)
                    {
                        // percorre observacoes
                        foreach (ObservacaoMedicaoDominio obs in observacoes_lidas)
                        {
                            bool adicionar = false;

                            // caso for admin GESTAL pode ver todas observacoes (inseridas pela GESTAL, consultores ou clientes)
                            if (isUser.isGESTAL(IDTipoAcesso))
                            {
                                // permite ele ver
                                adicionar = true;
                            }

                            // caso usuario atual for consultor pode ver apenas as de consultor e dos clientes
                            if (isUser.isConsultor(IDTipoAcesso))
                            {
                                // verifica se mensagem eh de consultor ou se de cliente habilitado para visualizar
                                if (isUser.isConsultor(obs.IDTipoAcesso) || obs.ClienteVisualiza)
                                {
                                    adicionar = true;
                                }
                            }

                            // caso usuario atual for cliente pode ver apenas se habilitado para visualizar
                            if (isUser.isCliente(IDTipoAcesso))
                            {
                                // verifica se cliente habilitado para visualizar
                                if (obs.ClienteVisualiza)
                                {
                                    adicionar = true;
                                }
                            }

                            // adiciona na lista
                            if (adicionar)
                            {
                                // adiona na lista
                                observacoes.Add(obs);
                            }
                        }
                    }

                    ViewBag.Observacoes = observacoes;
                }
            }


            // verifica se PDF
            if (tipo_arquivo == 1)
            {
                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("KPI{0}_{1:000000}_{2:yyyyMMddHHmm}.pdf", viewRelatorio, IDMedicao, data_atual);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Portrait,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna nome do arquivo
                var returnedData = new
                {
                    nomeArquivo = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);

                // abaixo original para fazer o download imediato do PDF sem usar o AJAX
                // utilizar a chamda na View:
                // <a href='@Url.Action("KPI_PDF", "Relatorios")'><i class="fa fa-file-pdf-o" data-toggle="tooltip" data-placement="left" title="PDF"></i></a>
                // retorna PDF
                //return new Rotativa.PartialViewAsPdf(viewRelatorio) { 
                //    FileName = nomeArquivo,
                //    PageOrientation = Orientation.Portrait,
                //    PageSize = Size.A4,
                //}; 
            }

            // verifica se XLS
            if (tipo_arquivo == 2)
            {
                // gera identificador unico aonde o arquivo sera guardado
                string handle = Guid.NewGuid().ToString();

                using (MemoryStream memoryStream = new MemoryStream())
                {
                    workbook.Write(memoryStream);
                    memoryStream.Position = 0;
                    Session[handle] = memoryStream.ToArray();
                }

                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("KPI{0}_{1:000000}_{2:yyyyMMddHHmm}.xls", viewRelatorio, IDMedicao, data_atual);

                // retorna nome do arquivo
                var returnedData = new
                {
                    FileGuid = handle,
                    FileName = nomeArquivo
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);

            }

            // verifica se EMAIL
            if (tipo_arquivo == 3)
            {
                // data e hora
                DateTime data_atual = DateTime.Now;

                // nome do arquivo
                string nomeArquivo = string.Format("Relat{0}_{1:000000}_{2:yyyyMMddHHmm}.pdf", viewRelatorio, IDMedicao, data_atual);

                // rodape
                string footer = "--footer-right \"Página [page]/[toPage]\"" + " --footer-line --footer-font-size \"9\" --footer-spacing 6 --footer-font-name \"calibri light\"";

                // gera PDF
                string viewPartial = string.Format("{0}_PDF", viewRelatorio);

                var pdfResult = new Rotativa.PartialViewAsPdf(viewPartial)
                {
                    FileName = nomeArquivo,
                    PageOrientation = Orientation.Portrait,
                    PageSize = Size.A4,
                    PageMargins = new Rotativa.Options.Margins(15, 10, 15, 10),
                    CustomSwitches = footer,
                    IsLowQuality = true
                };

                // salva PDF em arquivo
                var caminhoCompleto = Path.Combine(Server.MapPath("~/Temp"), nomeArquivo);

                byte[] dataPDF = pdfResult.BuildFile(this.ControllerContext);
                var fileStream = new FileStream(caminhoCompleto, FileMode.Create, FileAccess.Write);
                fileStream.Write(dataPDF, 0, dataPDF.Length);
                fileStream.Close();

                // retorna 
                ViewBag.caminhoCompleto = caminhoCompleto;
                var returnedData = new
                {
                    caminhoCompleto = caminhoCompleto
                };

                return Json(returnedData, JsonRequestBehavior.AllowGet);
            }

            return View();
        }


        // GET: KPI Atualizar
        public PartialViewResult _KPI_Atualizar_PelaMedicao(int TipoRelat, int Navegacao, string Data, string DataFim = null, string TurnoIni = null, string TurnoFim = null)
        {
            // caso forcar data
            if (Navegacao == 100)
            {
                // pega data
                DateTime dateValue = DateTime.Parse(Data);
                DateTime dateValueFim = new DateTime();

                // salva cookie datahora
                CookieStore.SalvaCookie_Datahora("Relat_Data", dateValue);

                // faco sempre o periodo de 1 dia
                dateValueFim = dateValue.AddDays(1);

                // salva cookie datahora (final)
                CookieStore.SalvaCookie_Datahora("Relat_DataFim", dateValueFim);
            }

            // le cookies
            LeCookies_SmartEnergy();

            int IDCliente = ViewBag._IDCliente;
            int IDMedicao = ViewBag._IDMedicao;
            int IDGateway = ViewBag._IDGateway;
            int IDTipoGateway = ViewBag._IDTipoGateway;
            int TipoPeriodo = ViewBag.Relat_TipoPeriodo;

            // tipo de acesso do usuario
            int IDUsuario = ViewBag._IDUsuario;
            int IDTipoAcesso = ViewBag._IDTipoAcesso;

            // unidade de potencia
            UnidadePotencia(IDMedicao);

            // le cookie datahora
            DateTime datahora_cookie = CookieStore.LeCookie_Datahora("Relat_Data");
            DATAHORA data_hora = new DATAHORA();

            DateTime datahora_cookie_fim = CookieStore.LeCookie_Datahora("Relat_DataFim");
            DATAHORA data_hora_fim = new DATAHORA();

            // verifica se fim eh menor que inicio
            if (datahora_cookie_fim < datahora_cookie)
            {
                // periodo de pelo menos 1 dia
                datahora_cookie_fim = datahora_cookie.AddDays(1);
            }

            // data e hora atual
            ViewBag.DataIniExportar = string.Format("{0:d}", datahora_cookie);
            ViewBag.HoraIniExportar = "00:15:00";
            ViewBag.DataFimExportar = string.Format("{0:d}", datahora_cookie_fim);
            ViewBag.HoraFimExportar = "00:00:00";


            // navega
            switch (Navegacao)
            {
                case 0:     // nao navega
                    break;

                case -1:    // dia anterior

                    datahora_cookie = datahora_cookie.AddDays(-1);
                    datahora_cookie_fim = datahora_cookie_fim.AddDays(-1);
                    break;

                case 1:    // dia seguinte

                    datahora_cookie = datahora_cookie.AddDays(1);
                    datahora_cookie_fim = datahora_cookie_fim.AddDays(1);
                    break;

                case -2:    // semana anterior

                    datahora_cookie = datahora_cookie.AddDays(-7);
                    datahora_cookie_fim = datahora_cookie_fim.AddDays(-7);
                    break;

                case 2:    // semana seguinte

                    datahora_cookie = datahora_cookie.AddDays(7);
                    datahora_cookie_fim = datahora_cookie_fim.AddDays(7);
                    break;

                case -3:    // mes anterior

                    datahora_cookie = datahora_cookie.AddMonths(-1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(-1);
                    break;

                case 3:    // mes seguinte

                    datahora_cookie = datahora_cookie.AddMonths(1);
                    datahora_cookie_fim = datahora_cookie_fim.AddMonths(1);
                    break;

                case -4:    // ano anterior

                    datahora_cookie = datahora_cookie.AddYears(-1);
                    datahora_cookie_fim = datahora_cookie_fim.AddYears(-1);
                    break;

                case 4:    // ano seguinte

                    datahora_cookie = datahora_cookie.AddYears(1);
                    datahora_cookie_fim = datahora_cookie_fim.AddYears(1);
                    break;

                case 10:    // ultimo

                    // le supervisao da medicao
                    var medicoesMetodos = new SupervMedicoesMetodos();
                    var listaMedicoes = medicoesMetodos.ListarPorIDMedicao(IDCliente, IDMedicao).First();

                    // dia atual
                    datahora_cookie = DateTime.Parse(listaMedicoes.DataHora);

                    // periodo de 1 dia
                    datahora_cookie_fim = datahora_cookie.AddDays(1);

                    break;
            }

            // salva cookie datahora
            CookieStore.SalvaCookie_Datahora("Relat_Data", datahora_cookie);

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora, datahora_cookie);

            // salva cookie datahora (final)
            CookieStore.SalvaCookie_Datahora("Relat_DataFim", datahora_cookie_fim);

            // converte
            Funcoes_Converte.ConverteDateTime2DataHora(ref data_hora_fim, datahora_cookie_fim);

            // tipo do relatorio
            ViewBag.TipoRelat = TipoRelat;

            // ler observacoes
            bool ler_observacoes = false;

            switch (TipoRelat)
            {
                case 0: // indicador de economia

                    switch (TipoPeriodo)
                    {
                        case 0: // Diario
                            KPI_IndEconomia_Diario(IDCliente, IDMedicao, data_hora);
                            break;

                        case 1: // Semanal
                            //Dem_Ativa_Semanal(IDCliente, IDMedicao, data_hora);
                            break;

                        case 2: // Mensal
                            //Dem_Ativa_Mensal(IDCliente, IDMedicao, data_hora);
                            break;

                        case 3: // Anual
                            //Dem_Ativa_Anual(IDCliente, IDMedicao, data_hora);
                            break;
                    }

                    break;

                case 100: // KPI Demanda / Area
                case 102: // KPI Demanda / Temperatura
                case 104: // KPI Demanda / Volume

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case 0: // Diario
                            KPI_Demanda_Diario_PelaMedicao(IDCliente, IDMedicao, data_hora, TipoRelat);
                            break;

                        case 1: // Semanal
                            KPI_Demanda_Semanal_PelaMedicao(IDCliente, IDMedicao, data_hora, TipoRelat);
                            break;

                        case 2: // Mensal
                            KPI_Demanda_Mensal_PelaMedicao(IDCliente, IDMedicao, data_hora, TipoRelat);
                            break;

                        case 3: // Anual
                            KPI_Demanda_Anual_PelaMedicao(IDCliente, IDMedicao, data_hora, TipoRelat);
                            break;
                    }

                    break;

                case 101: // KPI Consumo / Area
                case 103: // KPI Consumo / Temperatura
                case 105: // KPI Consumo / Volume

                    // ler observacoes
                    ler_observacoes = true;

                    switch (TipoPeriodo)
                    {
                        case 0: // Diario
                            KPI_Consumo_Diario_PelaMedicao(IDCliente, IDMedicao, data_hora, TipoRelat);
                            break;

                        case 1: // Semanal
                            KPI_Consumo_Semanal_PelaMedicao(IDCliente, IDMedicao, data_hora, TipoRelat);
                            break;

                        case 2: // Mensal
                            KPI_Consumo_Mensal_PelaMedicao(IDCliente, IDMedicao, data_hora, TipoRelat);
                            break;

                        case 3: // Anual
                            KPI_Consumo_Anual_PelaMedicao(IDCliente, IDMedicao, data_hora, TipoRelat);
                            break;
                    }

                    break;
            }


            //
            // Observacoes
            //
            if (ler_observacoes)
            {
                // data atual
                DateTime data_hora_i = ViewBag.data_hora_ini;

                if (data_hora_i != null)
                {
                    DateTime data_hora_f = new DateTime();

                    // periodo
                    switch (TipoPeriodo)
                    {
                        case 0: // Diario
                            data_hora_f = data_hora_i.AddDays(1);
                            break;

                        case 1: // Semanal
                            data_hora_f = data_hora_i.AddDays(7);
                            break;

                        case 2: // Mensal
                            data_hora_f = data_hora_i.AddMonths(1);
                            break;

                        case 3: // Anual
                            data_hora_f = data_hora_i.AddYears(1);
                            break;

                    }

                    // tipos acesso
                    ListaTiposMetodos listaMetodos = new ListaTiposMetodos();
                    List<ListaTiposDominio> listatipos = listaMetodos.ListarTodos("TipoAcesso");

                    // le tags
                    List<ObservacaoTagsDominio> tags = listaMetodos.ListarTodos_ObservacaoTags();
                    ViewBag.tags = tags;

                    // observacao
                    ObservacaoMedicaoDominio observacao = new ObservacaoMedicaoDominio();
                    ViewBag.observacao = observacao;

                    // observacoes
                    ObservacaoMedicaoMetodos observacaoMetodos = new ObservacaoMedicaoMetodos();
                    List<ObservacaoMedicaoDominio> observacoes_lidas = observacaoMetodos.ListarPorPeriodo(IDMedicao, data_hora_i, data_hora_f);
                    List<ObservacaoMedicaoDominio> observacoes = new List<ObservacaoMedicaoDominio>();

                    // le observacoes
                    if (observacoes_lidas != null)
                    {
                        // percorre observacoes
                        foreach (ObservacaoMedicaoDominio obs in observacoes_lidas)
                        {
                            bool adicionar = false;

                            // caso for admin GESTAL pode ver todas observacoes (inseridas pela GESTAL, consultores ou clientes)
                            if (isUser.isGESTAL(IDTipoAcesso))
                            {
                                // permite ele ver
                                adicionar = true;
                            }

                            // caso usuario atual for consultor pode ver apenas as de consultor e dos clientes
                            if (isUser.isConsultor(IDTipoAcesso))
                            {
                                // verifica se mensagem eh de consultor ou se de cliente habilitado para visualizar
                                if (isUser.isConsultor(obs.IDTipoAcesso) || obs.ClienteVisualiza)
                                {
                                    adicionar = true;
                                }
                            }

                            // caso usuario atual for cliente pode ver apenas se habilitado para visualizar
                            if (isUser.isCliente(IDTipoAcesso))
                            {
                                // verifica se cliente habilitado para visualizar
                                if (obs.ClienteVisualiza)
                                {
                                    adicionar = true;
                                }
                            }

                            // adiciona na lista
                            if (adicionar)
                            {
                                // adiona na lista
                                observacoes.Add(obs);
                            }
                        }
                    }

                    ViewBag.Observacoes = observacoes;
                }
            }

            // tela de ajuda - KPI 
            string PaginaAjuda = "Contato";

            switch (TipoRelat)
            {
                case 0: // indicador de economia
                    PaginaAjuda = "KPI_IndicadorEconomia";
                    break;

                case 100: // KPI demanda / Area
                    PaginaAjuda = "KPI_Demanda_Area";
                    break;

                case 101: // KPI consumo / Area
                    PaginaAjuda = "KPI_Consumo_Area";
                    break;

                case 102: // KPI demanda / temperatura
                    break;

                case 103: // KPI consumo / temperatura
                    break;

                case 104: // KPI demanda / volume
                    break;

                case 105: // KPI consumo / volume
                    break;
            }

            CookieStore.SalvaCookie_String("PaginaAjuda", PaginaAjuda);
            ViewBag.PaginaAjuda = PaginaAjuda;


            return PartialView();
        }
    }
}
