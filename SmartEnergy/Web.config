<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>

  <!-- Connection Strings para Docker -->
  <connectionStrings>
    <add name="DefaultConnection" 
         connectionString="Server=sqlserver,1433;Database=SmartEnergyDB;User Id=sa;Password=SmartEnergy123!;TrustServerCertificate=true;MultipleActiveResultSets=true;" 
         providerName="System.Data.SqlClient" />
  </connectionStrings>

  <appSettings>
    <add key="aspnet:MaxJsonDeserializerMembers" value="150000" />
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />

    <!-- Configurações SMTP para Docker -->
    <add key="SMTP" value="email-smtp.sa-east-1.amazonaws.com" />
    <add key="EmailSMTP" value="********************" />
    <add key="EmailAddr" value="<EMAIL>" />
    <add key="EmailPassword" value="BDgMX4j7o0oo5iDCtTQovvH7nmYjapkyPtlImafb7Qov" />
    <add key="SMTP_EnableSsl" value="true" />
    <add key="SMTP_Port" value="587" />

    <!-- Diretórios para Docker -->
    <add key="Recebidos" value="/app/files/recebidos/" />
    <add key="Firmware" value="/app/firmware/" />
    <add key="Firmware_Update" value="/app/firmware/update/" />
    <add key="Firmware_Problemas" value="/app/firmware/problemas/" />

    <add key="SmUpdate_Topico_Subscribe" value="SmUpdate_Cmd.*" />
    <add key="SiteEmManutencao" value="false" />
    <add key="MensagemManutencao" value="Estamos atualizando a plataforma com novos recursos e aplicando melhorias." />

    <add key="reCaptchaPublicKey" value="6Lcjw9QaAAAAAAWAo3TTBZDddWsgRtNDZI0I0HUQ" />
    <add key="reCaptchaPrivateKey" value="6Lcjw9QaAAAAAMouCM7HCcWn4ITGNiuTx_qz3q2E" />

    <add key="GraphBand" value="4c633a42-a74a-4c22-842d-c1d0008e1994" />
  </appSettings>

  <system.web>
    <authentication mode="Forms">
      <forms loginUrl="~/Login/Login" timeout="50000" />
    </authentication>
    <compilation debug="false" targetFramework="4.6.2" />
    <httpRuntime targetFramework="4.5.1" requestValidationMode="2.0" maxRequestLength="104857600" />
    <customErrors mode="RemoteOnly" />
    <pages validateRequest="false" />
    <trust level="Full" />
  </system.web>

  <system.webServer>
    <modules>
      <remove name="FormsAuthenticationModule" />
    </modules>
    <defaultDocument>
      <files>
        <clear />
        <add value="Default.aspx" />
        <add value="index.html" />
      </files>
    </defaultDocument>
  </system.webServer>

  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.5.2.14234" newVersion="1.5.2.14234" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="1*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.AspNet.Identity.Core" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.0.0" newVersion="2.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>

  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.SqlConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="Server=sqlserver,1433;Database=SmartEnergyDB;User Id=sa;Password=SmartEnergy123!;TrustServerCertificate=true;" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
</configuration>
