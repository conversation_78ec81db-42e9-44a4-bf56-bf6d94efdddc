﻿using System.ComponentModel.DataAnnotations;

namespace SmartEnergy.Models
{
    public class LoginViewModel
    {
        [Required]
        [Display(Name = "Usuário")]
        public string UserName { get; set; }

        [Required]
        [DataType(DataType.Password)]
        [Display(Name = "Senha")]
        public string Password { get; set; }

        [Display(Name = "SiteInicio")]
        public string SiteInicio { get; set; }

        [Display(Name = "Externo")]
        public bool Externo { get; set; }
    }
}
