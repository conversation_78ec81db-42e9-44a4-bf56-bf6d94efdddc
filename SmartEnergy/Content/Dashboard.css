﻿body {
    min-width: 520px;
}

.column {
    min-height: 100px;
    width: 370px;
    float: left;
    padding-bottom: 0px;
    margin-top: 14px;
}

.portlet {
    height: 320px;
    margin: 0 12px 12px 0;
    padding: 0px;
    border-radius: 6px;
}

.portlet_inserir {
    height: 320px;
    margin: 0 12px 12px 0;
    padding: 0px;
    border-radius: 6px;
    opacity: 0.4;
}

.portlet-toggle {
    position: absolute;
    top: 50%;
    right: 0;
    margin-top: -8px;
}

.portlet-placeholder {
    height: 330px;
    border: 1px dotted green;
    margin: 0 12px 12px 0;
    border-radius: 6px;
}

.portlet-header {
    height: 34px;
    border: 1px solid #aaaaaa;
    color: #FFFFFF;
    font-weight: bold;
    padding: 6px 4px;
    margin-bottom: 2px;
    position: relative;
}

.portlet-header a {
    color: #222222;
}

.portlet-header h5 {
    width: 300px;
    white-space: nowrap;
    overflow: hidden;	
    display: inline-block;
    font-size: 12px;
    margin: 0 0 7px;
    padding: 4px 4px;
    text-overflow: ellipsis;
    float: left;
}

.portlet-tools {
    display: block;
    float: none;
    margin-top: 0;
    position: relative;
    padding: 0;
    text-align: right;
}

.portlet-tools a {
    cursor: pointer;
    margin-left: 5px;
    padding-top: 2px;
    padding-right: 2px;
    color: #FFFFFF;
}

.portlet-tools a:hover {
    color: #e1e1e1;
}

.portlet-content {
    background: #ffffff;
    color: #222222;
    padding-left: 2px;
    padding-right: 2px;
}

.portlet-valorAtual {
    background: #FFFFFF;
    font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    float: left;
    padding-left: 5px;
}

.portlet-valorAtual h1 {
    margin: 0px;
    padding: 5px 0px 0px 5px;
    font-size: 30px;
    font-weight: bold;
}

.portlet-valorAtual h1>span {
    font-size: 20px;
}

.portlet-valorAtual h4 {
    margin: 0px;
    padding: 0px 0px 0px 6px;
    font-weight: normal;
}

.portlet-valorAtual h5 {
    margin: 0px;
    padding: 0px 0px 0px 6px;
    font-weight: normal;
}

.portlet-valorAtualLinha {
    clear: both;
}

.portlet-valorAtualDireita {
    background: #FFFFFF;
    font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    float: right;
    padding-top: 10px;
    padding-right: 5px;
}

.portlet-valorAtualDireita h1 {
    margin: 0px;
    padding: 5px 0px 0px 5px;
    font-size: 20px;
    font-weight: bold;
}

.portlet-valorAtualDireita h1>span {
    font-size: 14px;
}

.portlet-valorAtualDireita h5 {
    margin: 0px;
    padding: 0px 0px 0px 6px;
    font-weight: normal;
    float: right;
}

.portlet-datahoraAtual {
    background: #ffffff;
    font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    float: right;
    padding-left: 5px;
}

.portlet-datahoraAtual h4 {
    margin: 0px;
    padding: 10px 2px 0px 0px;
    font-weight: normal;
    text-align:right;
}

.portlet-datahoraAtual h6 {
    margin: 0px;
    padding: 0px 2px 0px 0px;
    font-weight: normal;
    text-align:right;
}

.portlet-tabela {
    background: #ffffff;
    color: #1AB394;
    font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    float: right;
    padding: 0px 5px 0px 5px;
    margin-bottom: 0px;
}

.portlet-tabela table {
    font-size: 10px;
    font-weight: normal;
    width: 340px;
}

.portlet-grafico {
    background: #ffffff;
    color: #1AB394;
    font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 10px;
    float: left;
    margin: 0px;
    padding-left: 5px;
    padding-top: -10px;
    width: 340px;
    height: 100px;
}

.postlet-grafico-ea .c3-area {
    opacity:1;
}

.postlet-grafico-ea .c3-target-Min .c3-circle, .c3-target-Max .c3-circle {
    display:none;
}

.portlet-erro {
    background: #ffffff;
    color: #1AB394;
    font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    padding: 0px 5px 0px 5px;
    margin-bottom: 0px;
    text-align: center;
}

.portlet-semregistros {
    background: #ffffff;
    color: #808080;
    font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    margin-bottom: 0px;
    text-align: center;
    margin-top: 15px;
}

.portlet-energia {
    color: #1AB394;
}

.portlet-fponta {
    color: #1AB394;
}

.portlet-ponta {
    color: #ED5565;
}

.portlet-indutivo {
    color: #1AB394;
}

.portlet-capacitivo {
    color: #1C84C6;
}

.portlet-util {
    color: #1C84C6;
}

.portlet-ea {
    color: #1C84C6;
}

.portlet-tempo {
    color: #23C6C8;
}

.portlet-blink {
    color: #ED5565;
}

@font-face {
    font-family: 'weather';
    src: url('https://s3-us-west-2.amazonaws.com/s.cdpn.io/93/artill_clean_icons-webfont.eot');
    src: url('https://s3-us-west-2.amazonaws.com/s.cdpn.io/93/artill_clean_icons-webfont.eot?#iefix') format('embedded-opentype'),
            url('https://s3-us-west-2.amazonaws.com/s.cdpn.io/93/artill_clean_icons-webfont.woff') format('woff'),
            url('https://s3-us-west-2.amazonaws.com/s.cdpn.io/93/artill_clean_icons-webfont.ttf') format('truetype'),
            url('https://s3-us-west-2.amazonaws.com/s.cdpn.io/93/artill_clean_icons-webfont.svg#artill_clean_weather_iconsRg') format('svg');
    font-weight: normal;
    font-style: normal;
}

#weather {
    width: 320px;
    margin: 0px auto;
    text-align: center;
}
    
.portlet-weather {
    background: #ffffff;
    font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
    padding: 10px 5px 0px 5px;
    margin-bottom: 0px;
    text-align: center;
    vertical-align: middle;
}

.weather_hoje {
    font-size: 40px;
    font-weight: bold;
}

.weather_hoje i {
    font-family: weather;
    font-size: 70px;
    font-weight: bold;
    font-style: normal;
    line-height: 1.0;
    text-transform: none;
}

.weather_hoje table {
    border:0px solid #FFF;
    border-collapse:collapse;    
}

.weather_hoje tr {
    border:none !important;
}

.weather_hoje td {
    border:none  !important;
}

.weather_forecast {
    font-size: 12px;
    font-weight: normal;
}

.weather_forecast i {
    font-family: weather;
    font-size: 22px;
    font-weight: normal;
    font-style: normal;
    line-height: 1.0;
    text-transform: none;
}

.icon-0:before { content: ":"; }
.icon-1:before { content: "p"; }
.icon-2:before { content: "S"; }
.icon-3:before { content: "Q"; }
.icon-4:before { content: "S"; }
.icon-5:before { content: "W"; }
.icon-6:before { content: "W"; }
.icon-7:before { content: "W"; }
.icon-8:before { content: "W"; }
.icon-9:before { content: "I"; }
.icon-10:before { content: "W"; }
.icon-11:before { content: "I"; }
.icon-12:before { content: "I"; }
.icon-13:before { content: "I"; }
.icon-14:before { content: "I"; }
.icon-15:before { content: "W"; }
.icon-16:before { content: "I"; }
.icon-17:before { content: "W"; }
.icon-18:before { content: "U"; }
.icon-19:before { content: "Z"; }
.icon-20:before { content: "Z"; }
.icon-21:before { content: "Z"; }
.icon-22:before { content: "Z"; }
.icon-23:before { content: "Z"; }
.icon-24:before { content: "E"; }
.icon-25:before { content: "E"; }
.icon-26:before { content: "3"; }
.icon-27:before { content: "a"; }
.icon-28:before { content: "A"; }
.icon-29:before { content: "a"; }
.icon-30:before { content: "A"; }
.icon-31:before { content: "6"; }
.icon-32:before { content: "1"; }
.icon-33:before { content: "6"; }
.icon-34:before { content: "1"; }
.icon-35:before { content: "W"; }
.icon-36:before { content: "1"; }
.icon-37:before { content: "S"; }
.icon-38:before { content: "S"; }
.icon-39:before { content: "S"; }
.icon-40:before { content: "M"; }
.icon-41:before { content: "W"; }
.icon-42:before { content: "I"; }
.icon-43:before { content: "W"; }
.icon-44:before { content: "a"; }
.icon-45:before { content: "S"; }
.icon-46:before { content: "U"; }
.icon-47:before { content: "S"; }

    
.nav-tabs{
 
    border:1px solid #F3F3F4 !important;
    background-color:#F3F3F4;
 
}
 
.nav-tabs > li > a{
    border:1px solid #F3F3F4 !important;
    color: #D0D0D0 !important;
}

.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
    outline: 0;
    color: #1AB394 !important;
    cursor: default;
    background-color: #F3F3F4 !important;
    border:1px solid #F3F3F4 !important;

} 


.ui-sortable {
    margin-left: auto;
    margin-right: auto;

}

.bordered-tab-contents > .tab-content > .tab-pane {
    border: 0px;
    background-color: #F3F3F4 !important;
}

.bordered-tab-contents > .nav-tabs {
    margin-bottom: 0;
}

.cente-pills {
    display: flex;
    justify-content: center;
    margin:0;
    padding:0;

}

.spiner-dashboard {
  height: 200px;
  margin-left:-20px;
  padding-top: 118px; 

}

.dashboard-tools {
    display: block;
    float: none;
    margin-top: 3px;
    margin-bottom: 0px;
    margin-right: 10px;
    position: relative;
    padding: 0;
    text-align: right;
}

.dashboard-tools a {
    cursor: pointer;
    margin-left: 10px;
    padding-top: 0px;
    padding-right: 2px;
    color: #FFFFFF;
    font-size: 30px;
    font-weight: bold;
}

.dashboard-tools a:hover {
    color: #e1e1e1;
}
