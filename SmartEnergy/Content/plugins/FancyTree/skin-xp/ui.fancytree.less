/*!
 * Fancytree "XP" skin.
 *
 * DON'T EDIT THE CSS FILE DIRECTLY, since it is automatically generated from
 * the LESS templates.
 */

// Import common styles
@import "../skin-common.less";


/*******************************************************************************
 * Styles specific to this skin.
 *
 * This section is automatically generated from the `ui-fancytree.less` template.
 ******************************************************************************/

// Override the variable after the import.
// NOTE: Variables are always resolved as the last definition, even if it is
// after where it is used.
@fancy-use-sprites: true;      // false: suppress all background images (i.e. icons)

@fancy-icon-width: 16px;
@fancy-icon-height: 16px;
@fancy-icon-spacing: 3px;
@fancy-node-border-width: 0;

// We need to define this variable here (not in skin-common.less) to make it
// work with grunt and webpack:
@fancy-image-prefix: "./skin-xp/";

// Use 'data-uri(...)' to embed the image into CSS instead of linking to 'loading.gif':
// @fancy-loading-url: data-uri("@{fancy-image-prefix}loading.gif");
// Set to `true` to use `data-uri(...)` which will embed icons.gif into CSS
// instead of linking to that file:
// @fancy-inline-sprites: true;


/*******************************************************************************
 * Tree container
 */
ul.fancytree-container {
	li {
		// background-image: url("vline.gif");
		// Use 'data-uri(...)' to embed the image into CSS instead of linking to 'loading.gif':
		background-image: data-uri("@{fancy-image-prefix}vline.gif");
		background-position: 0 0;
	}
	&.fancytree-rtl {
		li {
			background-position: right 0;
			background-image: url("vline-rtl.gif");
		}
		.fancytree-exp-n span.fancytree-expander {
			background-image: url("icons-rtl.gif");
			.useSprite(0, 4);
		}
		.fancytree-exp-nl span.fancytree-expander {
			background-image: url("icons-rtl.gif");
			.useSprite(1, 4);
		}
	}
	// Suppress lines for last child node
	li.fancytree-lastsib {
		background-image: none;
	}
}
// Suppress lines if level is fixed expanded (option minExpandLevel)
ul.fancytree-no-connector > li {
	background-image: none;
}

// XP theme always displays connectors (not only when .fancytree-connectors is set)
.fancytree-exp-n span.fancytree-expander,
.fancytree-exp-nl span.fancytree-expander {
	.setBgImageUrl("icons.gif");
	// margin-top: 0;
}
.fancytree-exp-n span.fancytree-expander,       // End-node, not last sibling
.fancytree-exp-n span.fancytree-expander:hover {
	.useSprite(0, 4);
}
.fancytree-exp-nl span.fancytree-expander,      // End-node, last sibling
.fancytree-exp-nl span.fancytree-expander:hover {
	.useSprite(1, 4);
}

/*******************************************************************************
 * Node titles
 */

span.fancytree-title {
  border: @fancy-node-border-width solid transparent;  // avoid jumping, when a border is added on hover
}
span.fancytree-title:hover {
	background-color: #F2F7FD; // light blue
	border-color: #B8D6FB; // darker light blue
}
span.fancytree-focused span.fancytree-title {
	outline: 1px dotted black;
	background-color: #EFEBDE; // gray
}
.fancytree-folder span.fancytree-title {
	font-weight: bold;
}
.fancytree-selected span.fancytree-title {
	color: green;
	font-style: italic;
}
.fancytree-active span.fancytree-title {
	background-color: #3169C6 !important;
	color: white !important; // @ IE6
}

/*******************************************************************************
 * 'table' extension
 */
table.fancytree-ext-table {
	border-collapse: collapse;
	tbody tr.fancytree-focused {
		background-color: #99DEFD;
	}
	tbody tr.fancytree-active {
		background-color: royalblue;
	}
	tbody tr.fancytree-selected {
		background-color: #99FDDE;
	}
}
