﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Energy</title>

    <style>
		.container {
			max-width: 800px;
			margin: 0 auto;
			background-color: #ffffff;
			padding: 20px;
			font-family: Arial, sans-serif;
			font-size: 1em;
		}

        body {
			margin: 0;
			padding: 0;
			background-color: #e0e0e0;
			line-height: 1.5;
        }

	    body > .container {
			background-color: #ffffff;
			padding: 0px;
			margin: 0 auto;
			max-width: 800px;
	    }

		.conteudo {
			max-width: 800px;
			margin: 0 auto;
			background-color: #ffffff;
			padding: 20px;
		}

		.header {
			height: 80px;
			background-color: #293846;
			color: #fff;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 10px;
		}

		h1 {
			color: #333;
			font-size: 1.5em;
			font-weight: bold;
			margin-top: 0;
		}

		p {
			color: #666;
			margin-bottom: 20px;
		}

		footer {
			background-color: #293846;
			color: #fff;
			padding: 10px;
			text-align: center;
			border-radius: 0 0 0px 0px;
		}

		@@media (max-width: 800px) {
			.container {
				font-size: 0.8em;
			}

			h1 {
				font-size: 1em;
			}
		}

		@@font-face {
			font-family: 'gestal-fg-fonts-3-4';
			src: url('https://www.smartenergy.com.br/fonts/font-gestal-3.4/font/gestal-icon-fonts-3-4.eot?30848902');
			src: url('https://www.smartenergy.com.br/fonts/font-gestal-3.4/font/gestal-icon-fonts-3-4.eot?30848902#iefix') format('embedded-opentype'),
				 url('https://www.smartenergy.com.br/fonts/font-gestal-3.4/font/gestal-icon-fonts-3-4.woff2?30848902') format('woff2'),
				 url('https://www.smartenergy.com.br/fonts/font-gestal-3.4/font/gestal-icon-fonts-3-4.woff?30848902') format('woff'),
				 url('https://www.smartenergy.com.br/fonts/font-gestal-3.4/font/gestal-icon-fonts-3-4.ttf?30848902') format('truetype'),
				 url('https://www.smartenergy.com.br/fonts/font-gestal-3.4/font/gestal-icon-fonts-3-4.svg?30848902#gestal-icon-fonts-3-4') format('svg');
			font-weight: normal;
			font-style: normal;
		}

		[class^="fg-"]:before, [class*=" fg-"]:before {
			font-family: "gestal-fg-fonts-3-4";
			font-style: normal;
			font-weight: normal;

			display: inline-block;
			text-decoration: inherit;
			width: 1em;
			margin-right: .2em;
			text-align: center;

			font-variant: normal;
			text-transform: none;

			line-height: 1em;

			margin-left: .2em;

			-webkit-font-smoothing: antialiased;
			-moz-osx-font-smoothing: grayscale;
		}

		.fg-5x {
			font-size: 5em;
		}

		.fg-alerta:before { content: '\e800'; }
		.fg-alerta_off:before { content: '\e801'; }

    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <img class="logo" src="https://smartenergy.com.br/Logos/LogoSmartEnergy.png">
            <i class="fg fg-alerta fg-5x"></i>
        </div>

        <div class="conteudo">
            <h1>Tentativa de Login não autorizada !!!</h1>
            <p>Login: <b><font size="4" color="#ff0000"> ViewBag.Login </font></b></p>
            <p>Senha: <b><font size="4" color="#ff0000"> ViewBag.Senha </font></b></p>
            <p><b><font size="4" color="#ff0000"> ViewBag.TextoAdmin </font></b></p>
            <p>IP Cliente: <b><font size="4" color="#ff0000"> ViewBag.IP_Cliente </font></b></p>
            <p>IP Local: <b><font size="4" color="#ff0000"> ViewBag.IP_Local </font></b></p>
            <p><b><font size="4" color="#ff0000"> ViewBag.info </font></b></p>
        </div>

        <footer>Este é um email automático, favor não respondê-lo.</footer>
    </div>
</body>
</html>

