﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=301880
  -->
<configuration>

<configSections>
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    
<!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 --></configSections>

<appSettings>
    <add key="aspnet:MaxJsonDeserializerMembers" value="150000" />
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
<!--	  
    <add key="SMTP" value="smtp.gestal.com" />
    <add key="EmailSMTP" value="<EMAIL>" />
    <add key="EmailPassword" value="SmEn#123456" />
	<add key="SMTP_EnableSsl" value="true" />
	<add key="SMTP_Port" value="587" />
-->

	<add key="SMTP" value="email-smtp.sa-east-1.amazonaws.com" />
	<add key="EmailSMTP" value="********************" />
	<add key="EmailAddr" value="<EMAIL>" />
	<add key="EmailPassword" value="BDgMX4j7o0oo5iDCtTQovvH7nmYjapkyPtlImafb7Qov" />
	<add key="SMTP_EnableSsl" value="true" />
	<add key="SMTP_Port" value="587" />

	<add key="Recebidos" value="C:\GESTAL FTP\GESTAL\Files\Recebidos\" />
	  
    <add key="Firmware" value="C:\GESTAL FTP\GESTAL\Firmware\" />
    <add key="Firmware_Update" value="C:\GESTAL FTP\GESTAL\Firmware\Update\" />
    <add key="Firmware_Problemas" value="C:\GESTAL FTP\GESTAL\Firmware\Problemas\" />

    <add key="SmUpdate_Topico_Subscribe" value="SmUpdate_Cmd.*" />
	  
    <add key="SiteEmManutencao" value="false" />
    <add key="MensagemManutencao" value="Estamos atualizando a plataforma com novos recursos e aplicando melhorias." />
	  
    <add key="reCaptchaPublicKey" value="6Lcjw9QaAAAAAAWAo3TTBZDddWsgRtNDZI0I0HUQ" />
    <add key="reCaptchaPrivateKey" value="6Lcjw9QaAAAAAMouCM7HCcWn4ITGNiuTx_qz3q2E" />
	  
    <add key="GraphBand" value="4c633a42-a74a-4c22-842d-c1d0008e1994" />
</appSettings>
	
<!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.6.2" />
      </system.Web>
-->
<system.web>
    <authentication mode="Forms">
      <forms loginUrl="~/Login/Login" timeout="50000" />
    </authentication>
    <compilation debug="true" targetFramework="4.6.2" />
    <httpRuntime targetFramework="4.5.1" requestValidationMode="2.0" maxRequestLength="104857600" />
    <customErrors mode="Off" />
    <pages validateRequest="false" />

<!--	
	<httpCookies httpOnlyCookies="true" requireSSL="true" />
	<sessionState cookieless="false" />
-->

</system.web>
<system.webServer>
    <modules>
      <remove name="FormsAuthenticationModule" />
    </modules>

<!--	
	<httpProtocol>
		<customHeaders>
			<add name="Content-Security-Policy" value="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self';" />
		</customHeaders>
	</httpProtocol>
	<rewrite>
		<rules>
			<rule name="Redireciona HTTP" stopProcessing="true">
				<match url="(.*)" />
				<conditions>
					<add input="{HTTPS}" pattern="^OFF$" />
				</conditions>
				<action type="Redirect" url="https://{HTTP_HOST}/{R:1}" />
			</rule>
		</rules>
	</rewrite>
-->

</system.webServer>
<runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
	<dependentAssembly>
		<assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" />
		<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
	</dependentAssembly>
	<dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.5.2.14234" newVersion="1.5.2.14234" />
    </dependentAssembly>
    <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="1*******" />
    </dependentAssembly>
    <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
    </dependentAssembly>
    <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
    </dependentAssembly>
    <dependentAssembly>
        <assemblyIdentity name="Microsoft.AspNet.Identity.Core" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.0.0" newVersion="2.0.0.0" />
    </dependentAssembly>
    <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
    </dependentAssembly>
    <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
    </dependentAssembly>
    <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
    </dependentAssembly>
  </assemblyBinding>
</runtime>
	
<entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="v11.0" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
</configuration>