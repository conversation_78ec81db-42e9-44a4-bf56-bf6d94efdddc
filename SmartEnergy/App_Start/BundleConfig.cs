﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Web;
using System.Web.Mvc;
using System.Web.Optimization;

namespace SmartEnergy
{
    public class BundleConfig
    {
        public static void RegisterBundles(BundleCollection bundles)
        {

            // Declarações
            bundles.Add(new ScriptBundle("~/declaracoes/Constants").Include(
                      "~/Scripts/declaracoes/ConstantsDeclaracao.js"));

            // AlertaBox
            bundles.Add(new ScriptBundle("~/plugins/AlertaBox").Include(
                      "~/Scripts/plugins/AlertaBox/AlertaBox.js"));

            // jQuery Validation
            bundles.Add(new ScriptBundle("~/bundles/jqueryval").Include(
            "~/Scripts/jquery.validate.min.js"));

            bundles.Add(new ScriptBundle("~/declaracoes/Validates").Include(
                      "~/Scripts/declaracoes/Validates.js"));


            // CSS style (style.css)
            string cssStylePath = HttpContext.Current.Server.MapPath("~/Content/style.css");

            // Gera o hash baseado no conteúdo do arquivo CSS
            string fileStyleHash = GetFileHash(cssStylePath);

            // Novo nome com hash
            var hashedStylePath = HttpContext.Current.Server.MapPath($"~/Content/style-{fileStyleHash}.css");

            // renomeio arquivo
            if (!File.Exists(hashedStylePath))
            {
                File.Copy(cssStylePath, hashedStylePath);
            }


            // CSS style (Padrao.css)
            cssStylePath = HttpContext.Current.Server.MapPath("~/Content/Padrao.css");

            // Gera o hash baseado no conteúdo do arquivo CSS
            string filePadraoHash = GetFileHash(cssStylePath);

            // Novo nome com hash
            hashedStylePath = HttpContext.Current.Server.MapPath($"~/Content/Padrao-{filePadraoHash}.css");

            // renomeio arquivo
            if (!File.Exists(hashedStylePath))
            {
                File.Copy(cssStylePath, hashedStylePath);
            }



            // CSS style (bootstrap/inspinia)
            bundles.Add(new StyleBundle("~/Content/css").Include(
                      "~/Content/bootstrap.min.css",
                      "~/Content/animate.css",
                      $"~/Content/style-{fileStyleHash}.css",
                      $"~/Content/Padrao-{filePadraoHash}.css",
                      "~/Content/Dashboard.css",
                      "~/Content/Supervisao.css",
                      "~/Content/Relatorios.css",
                      "~/Content/Configuracoes.css"));

            // Font Awesome icons
            bundles.Add(new StyleBundle("~/font-awesome/css").Include(
                      "~/fonts/font-awesome/css/font-awesome.min.css", new CssRewriteUrlTransform()));

            // Font Elusive
            bundles.Add(new StyleBundle("~/elusive-icons-2.0.0/css").Include(
                      "~/fonts/elusive-icons-2.0.0/css/elusive-icons.min.css", new CssRewriteUrlTransform()));

            // Font Gestal icons
            bundles.Add(new StyleBundle("~/font-gestal/css").Include(
                      "~/fonts/font-gestal-3.4/css/gestal-icon-fonts-3-4.css", new CssRewriteUrlTransform()));

            // jQuery
            bundles.Add(new ScriptBundle("~/bundles/jquery").Include(
                        "~/Scripts/jquery-2.1.1.min.js"));

            // jQueryUI CSS
            bundles.Add(new StyleBundle("~/Scripts/plugins/jquery-ui/jqueryuiStyles").Include(
                        "~/Scripts/plugins/jquery-ui/jquery-ui.css"));

            // jQueryUI 
            bundles.Add(new ScriptBundle("~/bundles/jqueryui").Include(
                        "~/Scripts/plugins/jquery-ui/jquery-ui.min.js"));

            // Bootstrap
            bundles.Add(new ScriptBundle("~/bundles/bootstrap").Include(
                      "~/Scripts/bootstrap.min.js"));

            // Inspinia script
            bundles.Add(new ScriptBundle("~/bundles/inspinia").Include(
                      "~/Scripts/plugins/metisMenu/metisMenu.min.js",
                      "~/Scripts/plugins/pace/pace.min.js",
                      "~/Scripts/app/inspinia.js"));

            // Slicks css styles
            bundles.Add(new StyleBundle("~/plugins/slickStyles").Include(
                      "~/Content/plugins/slick/slick.css",
                      "~/Content/plugins/slick/slick-theme.css"));

            // Slick
            bundles.Add(new ScriptBundle("~/plugins/slick").Include(
                      "~/Scripts/plugins/slick/slick.min.js"));

            // SlimScroll
            bundles.Add(new ScriptBundle("~/plugins/slimScroll").Include(
                      "~/Scripts/plugins/slimscroll/jquery.slimscroll.min.js"));

            // iCheck css styles
            bundles.Add(new StyleBundle("~/Content/plugins/iCheck/iCheckStyles").Include(
                      "~/Content/plugins/iCheck/custom.css"));

            // iCheck
            bundles.Add(new ScriptBundle("~/plugins/iCheck").Include(
                      "~/Scripts/plugins/iCheck/icheck.min.js"));

            // dataTables css styles
            bundles.Add(new StyleBundle("~/Content/plugins/dataTables/dataTablesStyles").Include(
                      "~/Content/plugins/dataTables/datatables.min.css"));

            // dataTables 
            bundles.Add(new ScriptBundle("~/plugins/dataTables").Include(
                      "~/Scripts/plugins/dataTables/datatables.min.js"));

            // validate 
            bundles.Add(new ScriptBundle("~/plugins/validate").Include(
                      "~/Scripts/plugins/validate/jquery.validate.min.js"));

            // dataPicker styles
            bundles.Add(new StyleBundle("~/dataPickerStyles").Include(
                        "~/Content/bootstrap-datepicker3.css"));

            // dataPicker 
            bundles.Add(new ScriptBundle("~/dataPicker").Include(
                        "~/Scripts/bootstrap-datepicker.js",
                        "~/Scripts/bootstrap-datepicker-globalize.js"));

            // datetimePicker styles
            bundles.Add(new StyleBundle("~/datetimePickerStyles").Include(
                        "~/Content/bootstrap-datetimepicker.min.css"));

            // datetimePicker 
            bundles.Add(new ScriptBundle("~/datetimePicker").Include(
                        "~/Scripts/moment.min.js",
                        "~/Scripts/moment-with-locales.min.js",
                        "~/Scripts/bootstrap-datetimepicker.min.js"));

            // jasnyBootstrap styles
            bundles.Add(new StyleBundle("~/plugins/jasnyBootstrapStyles").Include(
                      "~/Content/plugins/jasny/jasny-bootstrap.min.css"));

            // jasnyBootstrap 
            bundles.Add(new ScriptBundle("~/plugins/jasnyBootstrap").Include(
                      "~/Scripts/plugins/jasny/jasny-bootstrap.min.js"));

            // chosen styles
            bundles.Add(new StyleBundle("~/Content/plugins/chosen/chosenStyles").Include(
                      "~/Content/plugins/chosen/chosen.css"));

            // chosen 
            bundles.Add(new ScriptBundle("~/plugins/chosen").Include(
                      "~/Scripts/plugins/chosen/chosen.jquery.js"));

            bundles.Add(new StyleBundle("~/plugins/summernoteStyles").Include(
                      "~/Scripts/plugins/summernote/summernote.css"));

            // summernote 
            bundles.Add(new ScriptBundle("~/plugins/summernote").Include(
                      "~/Scripts/plugins/summernote/summernote.js"));

            // toastr notification 
            bundles.Add(new ScriptBundle("~/plugins/toastr").Include(
                      "~/Scripts/plugins/toastr/toastr.min.js"));

            // toastr notification styles
            bundles.Add(new StyleBundle("~/plugins/toastrStyles").Include(
                      "~/Content/plugins/toastr/toastr.min.css"));

            // FancyTree
            bundles.Add(new ScriptBundle("~/plugins/FancyTree").Include(
                    "~/Scripts/plugins/FancyTree/jquery.fancytree.min.js",
                    "~/Scripts/plugins/FancyTree/jquery.fancytree-all-deps.min.js"));

            // FancyTree styles
            bundles.Add(new StyleBundle("~/Content/plugins/FancyTree").Include(
                      "~/Content/plugins/FancyTree/skin-bootstrap/ui.fancytree.min.css"));

            // Clockpicker styles
            bundles.Add(new StyleBundle("~/plugins/clockpickerStyles").Include(
                      "~/Content/plugins/clockpicker/clockpicker.css"));

            // Clockpicker
            bundles.Add(new ScriptBundle("~/plugins/clockpicker").Include(
                      "~/Scripts/plugins/clockpicker/clockpicker.js"));

            // Date range picker Styless
            bundles.Add(new StyleBundle("~/plugins/dateRangeStyles").Include(
                      "~/Content/plugins/daterangepicker/daterangepicker-bs3.css"));

            // Date range picker
            bundles.Add(new ScriptBundle("~/plugins/dateRange").Include(
                      // Date range use moment.js same as full calendar plugin 
                      "~/Scripts/plugins/fullcalendar/moment.min.js",
                      "~/Scripts/plugins/daterangepicker/daterangepicker.js"));

            // Sweet alert Styless
            bundles.Add(new StyleBundle("~/plugins/sweetAlertStyles").Include(
                      "~/Content/plugins/sweetalert/sweetalert.css"));

            // Sweet alert
            bundles.Add(new ScriptBundle("~/plugins/sweetAlert").Include(
                      "~/Scripts/plugins/sweetalert/sweetalert.min.js"));

            // Select2 Styless
            bundles.Add(new StyleBundle("~/plugins/select2Styles").Include(
                      "~/Content/plugins/select2/select2.min.css"));

            // Select2
            bundles.Add(new ScriptBundle("~/plugins/select2").Include(
                      "~/Scripts/plugins/select2/select2.full.min.js"));

            // selectpicker styles
            bundles.Add(new StyleBundle("~/selectpickerStyles").Include(
                        "~/Content/bootstrap-selectpicker.min.css"));

            // selectpicker 
            bundles.Add(new ScriptBundle("~/selectpicker").Include(
                        "~/Scripts/bootstrap-selectpicker.min.js"));

            // c3 Styless
            bundles.Add(new StyleBundle("~/plugins/c3Styles").Include(
                      "~/Content/plugins/c3/c3.min.css"));

            // c3 Spin
            bundles.Add(new ScriptBundle("~/plugins/c3").Include(
                      "~/Scripts/plugins/c3/c3.min.js"));

            // d3 Spin
            bundles.Add(new ScriptBundle("~/plugins/d3").Include(
                      "~/Scripts/plugins/d3/d3.min.js"));

            // simplerWeather
            bundles.Add(new ScriptBundle("~/plugins/simplerWeather").Include(
                      "~/Scripts/plugins/simplerWeather/jquery.simplerWeather.js"));

            // skycons
            bundles.Add(new ScriptBundle("~/plugins/skycons").Include(
                      "~/Scripts/plugins/skycons/skycons.js"));

            // waitingFor
            bundles.Add(new ScriptBundle("~/plugins/waitingFor").Include(
                      "~/Scripts/plugins/waitingFor/waitingFor.js"));

            // FileUpload
            bundles.Add(new StyleBundle("~/jQuery.FileUpload/FileUploadStyles").Include(
                     "~/Content/jQuery.FileUpload/css/jquery.fileupload.css",
                     "~/Content/jQuery.FileUpload/css/jquery.fileupload-ui.css"));

            bundles.Add(new ScriptBundle("~/jQuery.FileUpload/FileUpload").Include(
                      "~/Scripts/jquery-ui-1.9.0.js",
                      "~/Scripts/jQuery.FileUpload/jquery.iframe-transport.js",
                      "~/Scripts/jQuery.FileUpload/jquery.fileupload.js",
                      "~/Scripts/jQuery.FileUpload/jquery.fileupload-ui.js"));

            // maskMoney
            bundles.Add(new ScriptBundle("~/plugins/maskMoney").Include(
                      "~/Scripts/plugins/maskMoney/jquery.maskMoney.min.js"));
        }


        // Função para gerar o hash do arquivo
        private static string GetFileHash(string filePath)
        {
            using (var md5 = MD5.Create())
            {
                using (var stream = File.OpenRead(filePath))
                {
                    var hashBytes = md5.ComputeHash(stream);
                    return BitConverter.ToString(hashBytes).Replace("-", "").ToLowerInvariant();
                }
            }
        }
    }
}
