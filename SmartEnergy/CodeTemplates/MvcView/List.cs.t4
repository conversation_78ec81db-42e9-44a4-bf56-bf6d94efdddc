﻿<#@ template language="C#" HostSpecific="True" #>
<#@ output extension=".cshtml" #>
<#@ include file="Imports.include.t4" #>
@model IEnumerable<#= "<" + ViewDataTypeName + ">" #>
<#
// The following chained if-statement outputs the file header code and markup for a partial view, a view using a layout page, or a regular view.
if(IsPartialView) {
#>

<#
} else if(IsLayoutPageSelected) {
#>

@{
    ViewBag.Title = "<#= ViewName#>";
<#
if (!String.IsNullOrEmpty(LayoutPageFile)) {
#>
    Layout = "<#= LayoutPageFile#>";
<#
}
#>
}

<div class="row wrapper border-bottom white-bg page-heading">
    <div class="col-sm-4">
        <h2><#= ViewName#></h2>
        <ol class="breadcrumb">
            <li class="active">
                <strong><#= ViewDataTypeShortName #></strong>
            </li>
        </ol>
    </div>
</div>

<#
} else {
#>

@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title><#= ViewName #></title>
</head>
<body>
<#
    PushIndent("    ");
}
#>

<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5>List of <#= ViewDataTypeShortName #></h5>
					<div class="ibox-tools">
						@Html.ActionLink("Create New", "Create", null, new { @class = "btn btn-primary btn-xs"})
					</div>
                </div>
                <div class="ibox-content">

<table class="table table-striped">
    <tr>
<#
IEnumerable<PropertyMetadata> properties = ModelMetadata.Properties;
foreach (PropertyMetadata property in properties) {
    if (property.Scaffold && !property.IsPrimaryKey && !property.IsForeignKey) {
#>
<#
        // This is a temporary work around until core scaffolding provides support for independent associations.
        if (property.IsAssociation && GetRelatedModelMetadata(property) == null) {
            continue;
        }
#>
        <th>
            @Html.DisplayNameFor(model => model.<#= GetValueExpression(property) #>)
        </th>
<#
    }
}
#>
        <th></th>
    </tr>

@foreach (var item in Model) {
    <tr>
<#
foreach (PropertyMetadata property in properties) {
    if (property.Scaffold && !property.IsPrimaryKey && !property.IsForeignKey) {
#>
<#
        // This is a temporary work around until core scaffolding provides support for independent associations.
        if (property.IsAssociation && GetRelatedModelMetadata(property) == null) {
            continue;
        }
#>
        <td>
            @Html.DisplayFor(modelItem => <#= "item." + GetValueExpression(property) #>)
        </td>
<#
    }
}

string pkName = GetPrimaryKeyName();
if (pkName != null) {
#>
        <td>
			@Html.ActionLink("Details", "Details", new { id=item.<#= pkName #> }, new { @class = "btn btn-primary btn-sm"})
            @Html.ActionLink("Edit", "Edit", new { id=item.<#= pkName #> }, new { @class = "btn btn-white btn-sm"})
            @Html.ActionLink("Delete", "Delete", new { id=item.<#= pkName #> }, new { @class = "btn btn-white btn-sm"})
        </td>
<#
} else {
#>
        <td>
			@Html.ActionLink("Details", "Details", new { /* id=item.PrimaryKey */ }, new { @class = "btn btn-primary btn-sm"})
            @Html.ActionLink("Edit", "Edit", new { /* id=item.PrimaryKey */ }, new { @class = "btn btn-white btn-sm"})
            @Html.ActionLink("Delete", "Delete", new { /* id=item.PrimaryKey */ }, new { @class = "btn btn-white btn-sm"})
        </td>
<#
}
#>
    </tr>
}

</table>

                </div>
            </div>
        </div>
    </div>
 </div>



<#
// The following code closes the tag used in the case of a view using a layout page and the body and html tags in the case of a regular view page
#>
<#
if(!IsPartialView && !IsLayoutPageSelected) {
    ClearIndent();
#>
</body>
</html>
<#
}
#>
<#@ include file="ModelMetadataFunctions.cs.include.t4" #>