<!DOCTYPE html>
<html>
  <head>
    <title>Skycons</title>
    <meta charset="us-ascii">

    <style type="text/css">
      body {
        font: 300 112.5%/1.5em "Helvetica", "Arial", sans-serif;
        margin: 3em 3em 6em;
        padding: 0;
      }

      article {
        margin: auto;
        width: 30em;
      }

      h1 {
        font-size: 3em;
        line-height: 1em;
        text-align: center;
        margin: 0.5em 0;
      }

      p {
        margin: 1.5em 0;
      }

      figure {
        margin: 3em auto;
        width: 368px;
        height: 152px;
      }

      canvas {
        display: block;
        float: left;
        margin-left: 8px;
        margin-top: 8px;
      }

      pre {
        font: 77.8%/1.5em "Lucida Sans Typewriter", "Lucida Console", "Monaco", "Bitstream Vera Sans Mono", monospace;
        margin: 3.375em 1.6875em;
      }

      a, pre span.comment {
        color: royalblue;
      }

      a:visited, pre span.constant {
        color: indianred;
      }
    </style>
  </head>

  <body>
    <article>
      <h1>Skycons</h1>

      <p><strong>Skycons</strong> is a set of ten animated weather glyphs,
      procedurally generated by JavaScript using the HTML5 canvas tag.</p>

      <figure class="icons">
        <canvas id="clear-day" width="64" height="64">
        </canvas>

        <canvas id="clear-night" width="64" height="64">
        </canvas>

        <canvas id="partly-cloudy-day" width="64" height="64">
        </canvas>

        <canvas id="partly-cloudy-night" width="64" height="64">
        </canvas>

        <canvas id="cloudy" width="64" height="64">
        </canvas>

        <canvas id="rain" width="64" height="64">
        </canvas>

        <canvas id="sleet" width="64" height="64">
        </canvas>

        <canvas id="snow" width="64" height="64">
        </canvas>

        <canvas id="wind" width="64" height="64">
        </canvas>

        <canvas id="fog" width="64" height="64">
        </canvas>
      </figure>

      <p>They&rsquo;re easy to use, and pretty lightweight, so they
      shouldn&rsquo;t rain on your parade:</p>

      <pre>&lt;canvas id="icon1" width="128" height="128"&gt;&lt;/canvas&gt;
&lt;canvas id="icon2" width="128" height="128"&gt;&lt;/canvas&gt;

&lt;script&gt;
  var skycons = new Skycons({<span class="constant">"color"</span>: <span class="constant">"pink"</span>});
  <span class="comment">// on Android, a nasty hack is needed: {"resizeClear": true}</span>

  <span class="comment">// you can add a canvas by it's ID...</span>
  skycons.add(<span class="constant">"icon1"</span>, Skycons.PARTLY_CLOUDY_DAY);

  <span class="comment">// ...or by the canvas DOM element itself.</span>
  skycons.add(document.getElementById(<span class="constant">"icon2"</span>), Skycons.RAIN);

  <span class="comment">// if you're using the Forecast API, you can also supply
  // strings: "partly-cloudy-day" or "rain".</span>

  <span class="comment">// start animation!</span>
  skycons.play();

  <span class="comment">// you can also halt animation with skycons.pause()</span>

  <span class="comment">// want to change the icon? no problem:</span>
  skycons.set(<span class="constant">"icon1"</span>, Skycons.PARTLY_CLOUDY_NIGHT);

  <span class="comment">// want to remove one altogether? no problem:</span>
  skycons.remove(<span class="constant">"icon2"</span>);
&lt;/script&gt;</pre>

      <p>Skycons were designed for <a href="http://forecast.io/">Forecast</a>
      by those wacky folks at <strong>The Dark Sky Company</strong>, and were
      heavily inspired by Adam Whitcroft&rsquo;s excellent
      <a href="http://adamwhitcroft.com/climacons/">Climacons</a>. The source
      code is available on
      <a href="http://github.com/darkskyapp/skycons">Github</a>, and has been
      <a href="http://creativecommons.org/publicdomain/zero/1.0/">released
      into the public domain</a>, so please do with it as you see fit!
      &#9825;</p>
    </article>

    <script src="skycons.js"></script>

    <script>
      var icons = new Skycons(),
          list  = [
            "clear-day", "clear-night", "partly-cloudy-day",
            "partly-cloudy-night", "cloudy", "rain", "sleet", "snow", "wind",
            "fog"
          ],
          i;

      for(i = list.length; i--; )
        icons.set(list[i], list[i]);

      icons.play();
    </script>
  </body>
</html>
