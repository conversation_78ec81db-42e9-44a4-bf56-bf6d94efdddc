<html>
  <head>
    <link rel="stylesheet" type="text/css" href="c3.css">
  </head>
  <body>
    <div id="chart"></div>

    <script src="http://d3js.org/d3.v4.min.js" charset="utf-8"></script>
    <script src="c3.min.js"></script>
    <script src="bubble.js"></script>
    <script>
     var chart = c3.generate({
         data: {
             type: 'bubble',
             pairs: [
                 { x: 'Name_0', y: 'Name_0', value: 10000 },
                 { x: 'Name_0', y: 'Name_1', value: 20000 },
                 { x: 'Name_0', y: 'Name_2', value: 39990 },
//                 { x: 'Name_1', y: 'Name_0', value: 5000 },
                 { x: 'Name_1', y: 'Name_1', value: 6000 },
                 { x: 'Name_1', y: 'Name_2', value: 50000 },
                 { x: 'Name_2', y: 'Name_0', value: 1000 },
                 { x: 'Name_2', y: 'Name_1', value: 2000 },
                 { x: 'Name_2', y: 'Name_2', value: 3000 },
             ]
         },
         grid: {
             x: {
                 show: true
             },
             y: {
                 show: true
             }
         },
      });
    </script>
  </body>
</html>
