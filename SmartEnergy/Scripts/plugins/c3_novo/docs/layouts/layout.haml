!!!
%html.no-js
  %head
    %meta( charset="utf-8" )
    %meta( http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" )
    %meta( name="description" content="D3 based reusable chart library" )
    %meta( name="author" content="c3js.org" )
    %meta( name="viewport" content="width=device-width" )
    %link( rel="icon" href="/img/favicon.png" )
    %title C3.js | D3-based reusable chart library

    -# Place favicon.ico and apple-touch-icon.png in the root directory

    -#= stylesheet_link_tag 'bootstrap.min'
    = stylesheet_link_tag 'normalize'
    = stylesheet_link_tag 'foundation.min'
    = stylesheet_link_tag 'tomorrow'
    = stylesheet_link_tag 'c3'
    = stylesheet_link_tag 'style'
    = stylesheet_link_tag get_css_name(current_page.path)

    :javascript
      (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
      (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
      m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
      })(window,document,'script','//www.google-analytics.com/analytics.js','ga');
      ga('create', 'UA-42896059-3', 'c3js.org');
      ga('send', 'pageview');

    = javascript_include_tag 'vendor/modernizr-2.6.1.min.js'

  %body.antialiased
    .sticky
      %nav.top-bar( data-topbar data-options="sticky_on: large" )
        %ul.title-area
          %li.name
            %h1
              %a( href="/" ) C3.js | D3-based reusable chart library
          %li.toggle-topbar.menu-icon
            %a( href="#" )
              %span Menu
        %section.top-bar-section
          %ul.right
            %li.has-form
              %a.button( href="/gettingstarted.html" ) Getting Started
            %li.divider
            %li
              %a( href="/examples.html") Examples
            %li.divider
            %li
              %a( href="/reference.html") Reference
            %li.divider
            %li
              %a( href="https://groups.google.com/forum/#!forum/c3js" target="_blank") Forum
            %li.divider
            %li
              %a( href="https://github.com/c3js/c3" target="_blank") Source
    = yield
