@font-face {
  font-family: 'gestal-fg-fonts-3-4';
  src: url('../font/gestal-icon-fonts-3-4.eot?30848902');
  src: url('../font/gestal-icon-fonts-3-4.eot?30848902#iefix') format('embedded-opentype'),
       url('../font/gestal-icon-fonts-3-4.woff2?30848902') format('woff2'),
       url('../font/gestal-icon-fonts-3-4.woff?30848902') format('woff'),
       url('../font/gestal-icon-fonts-3-4.ttf?30848902') format('truetype'),
       url('../font/gestal-icon-fonts-3-4.svg?30848902#gestal-icon-fonts-3-4') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="fg-"]:before, [class*=" fg-"]:before {
  font-family: "gestal-fg-fonts-3-4";
  font-style: normal;
  font-weight: normal;
  speak: never;

  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  margin-right: .2em;
  text-align: center;
  /* opacity: .8; */

  /* For safety - reset parent styles, that can break glyph codes*/
  font-variant: normal;
  text-transform: none;

  /* fix buttons height, for twitter bootstrap */
  line-height: 1em;

  /* Animation center compensation - margins should be symmetric */
  /* remove if not needed */
  margin-left: .2em;

  /* you can be more comfortable with increased icons size */
  /* font-size: 120%; */

  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Uncomment for 3D effect */
  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}



/* makes the font 33% larger relative to the icon container */
.fg-lg {
    font-size: 1.33333333em;
    line-height: 0.75em;
    vertical-align: -15%;
}

.fg-2x {
    font-size: 2em;
}

.fg-3x {
    font-size: 3em;
}

.fg-4x {
    font-size: 4em;
}

.fg-5x {
    font-size: 5em;
}

.fg-fw {
    width: 1.28571429em;
    text-align: center;
}

.fg-ul {
    padding-left: 0;
    margin-left: 2.14285714em;
    list-style-type: none;
}

    .fg-ul > li {
        position: relative;
    }

.fg-li {
    position: absolute;
    left: -2.14285714em;
    width: 2.14285714em;
    top: 0.14285714em;
    text-align: center;
}

    .fg-li.fg-lg {
        left: -1.85714286em;
    }

.fg-border {
    padding: .2em .25em .15em;
    border: solid 0.08em #eeeeee;
    border-radius: .1em;
}

.fg-pull-left {
    float: left;
}

.fg-pull-right {
    float: right;
}

.fg.fg-pull-left {
    margin-right: .3em;
}

.fg.fg-pull-right {
    margin-left: .3em;
}
/* Deprecated as of 4.4.0 */
.pull-right {
    float: right;
}

.pull-left {
    float: left;
}

.fg.pull-left {
    margin-right: .3em;
}

.fg.pull-right {
    margin-left: .3em;
}

.fg-spin {
    -webkit-animation: fg-spin 2s infinite linear;
    animation: fg-spin 2s infinite linear;
}

.fg-pulse {
    -webkit-animation: fg-spin 1s infinite steps(8);
    animation: fg-spin 1s infinite steps(8);
}

@-webkit-keyframes fg-spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    100% {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg);
    }
}

@keyframes fg-spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    100% {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg);
    }
}

.fg-rotate-90 {
    -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
}

.fg-rotate-180 {
    -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
}

.fg-rotate-270 {
    -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
    -webkit-transform: rotate(270deg);
    -ms-transform: rotate(270deg);
    transform: rotate(270deg);
}

.fg-flip-horizontal {
    -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
    -webkit-transform: scale(-1, 1);
    -ms-transform: scale(-1, 1);
    transform: scale(-1, 1);
}

.fg-flip-vertical {
    -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
    -webkit-transform: scale(1, -1);
    -ms-transform: scale(1, -1);
    transform: scale(1, -1);
}

:root .fg-rotate-90,
:root .fg-rotate-180,
:root .fg-rotate-270,
:root .fg-flip-horizontal,
:root .fg-flip-vertical {
    filter: none;
}

.fg-stack {
    position: relative;
    display: inline-block;
    width: 2em;
    height: 2em;
    line-height: 2em;
    vertical-align: middle;
}

.fg-stack-1x,
.fg-stack-2x {
    position: absolute;
    left: 0;
    width: 100%;
    text-align: center;
}

.fg-stack-1x {
    line-height: inherit;
}

.fg-stack-2x {
    font-size: 2em;
}

.fg-inverse {
    color: #ffffff;
}

.fg-alerta:before { content: '\e800'; } /* '' */
.fg-alerta_off:before { content: '\e801'; } /* '' */
.fg-atualizar:before { content: '\e802'; } /* '' */
.fg-atualizar_datahora:before { content: '\e803'; } /* '' */
.fg-automatico:before { content: '\e804'; } /* '' */
.fg-automatico_circulo:before { content: '\e805'; } /* '' */
.fg-bluetooth:before { content: '\e806'; } /* '' */
.fg-desbloqueado:before { content: '\e807'; } /* '' */
.fg-desbloqueado_circulo:before { content: '\e808'; } /* '' */
.fg-bloqueado:before { content: '\e809'; } /* '' */
.fg-bloqueado_circulo:before { content: '\e80a'; } /* '' */
.fg-ciclometro:before { content: '\e80b'; } /* '' */
.fg-ciclometro_energia:before { content: '\e80c'; } /* '' */
.fg-ciclometro_formula:before { content: '\e80d'; } /* '' */
.fg-ciclometro_gas:before { content: '\e80e'; } /* '' */
.fg-ciclometro_utilidades:before { content: '\e80f'; } /* '' */
.fg-clientes:before { content: '\e810'; } /* '' */
.fg-desconectado:before { content: '\e811'; } /* '' */
.fg-conectado:before { content: '\e812'; } /* '' */
.fg-desligar:before { content: '\e813'; } /* '' */
.fg-email:before { content: '\e814'; } /* '' */
.fg-email_aberto:before { content: '\e815'; } /* '' */
.fg-energia:before { content: '\e816'; } /* '' */
.fg-enviar:before { content: '\e817'; } /* '' */
.fg-fatura:before { content: '\e818'; } /* '' */
.fg-filtro_lista:before { content: '\e819'; } /* '' */
.fg-gas:before { content: '\e81a'; } /* '' */
.fg-gas_formula:before { content: '\e81b'; } /* '' */
.fg-gateway:before { content: '\e81c'; } /* '' */
.fg-gauge:before { content: '\e81d'; } /* '' */
.fg-gauge_alto:before { content: '\e81e'; } /* '' */
.fg-gauge_formula:before { content: '\e81f'; } /* '' */
.fg-gear:before { content: '\e820'; } /* '' */
.fg-gears:before { content: '\e821'; } /* '' */
.fg-gota:before { content: '\e822'; } /* '' */
.fg-gota_formula:before { content: '\e823'; } /* '' */
.fg-grafico_consumo-1:before { content: '\e824'; } /* '' */
.fg-grafico_demanda:before { content: '\e825'; } /* '' */
.fg-grafico_fatpot:before { content: '\e826'; } /* '' */
.fg-home:before { content: '\e827'; } /* '' */
.fg-ihm:before { content: '\e828'; } /* '' */
.fg-iniciar:before { content: '\e829'; } /* '' */
.fg-lampada:before { content: '\e82a'; } /* '' */
.fg-lampada_formula:before { content: '\e82b'; } /* '' */
.fg-lampada_on:before { content: '\e82c'; } /* '' */
.fg-metereologia:before { content: '\e82d'; } /* '' */
.fg-monitor:before { content: '\e82e'; } /* '' */
.fg-olho:before { content: '\e82f'; } /* '' */
.fg-olho_off:before { content: '\e830'; } /* '' */
.fg-pausar:before { content: '\e831'; } /* '' */
.fg-pesquisar:before { content: '\e832'; } /* '' */
.fg-seta_baixo_19:before { content: '\e833'; } /* '' */
.fg-seta_baixo_91:before { content: '\e834'; } /* '' */
.fg-seta_baixo_az:before { content: '\e835'; } /* '' */
.fg-seta_baixo_maior_menor:before { content: '\e836'; } /* '' */
.fg-seta_baixo_menor_maior:before { content: '\e837'; } /* '' */
.fg-seta_baixo_za:before { content: '\e838'; } /* '' */
.fg-seta_entrada:before { content: '\e839'; } /* '' */
.fg-seta_saida:before { content: '\e83a'; } /* '' */
.fg-somatoria:before { content: '\e83b'; } /* '' */
.fg-temperatura:before { content: '\e83c'; } /* '' */
.fg-temperatura_formula:before { content: '\e83d'; } /* '' */
.fg-upload:before { content: '\e83e'; } /* '' */
.fg-download:before { content: '\e83f'; } /* '' */
.fg-sair:before { content: '\e840'; } /* '' */
.fg-upload_ota:before { content: '\e841'; } /* '' */
.fg-salvar:before { content: '\e842'; } /* '' */
.fg-qr_code-1:before { content: '\e843'; } /* '' */
.fg-configuracao_iot:before { content: '\e844'; } /* '' */
.fg-seta_esquerda:before { content: '\e845'; } /* '' */
.fg-arquivo:before { content: '\e846'; } /* '' */
.fg-diretorio:before { content: '\e847'; } /* '' */
.fg-fechar:before { content: '\e848'; } /* '' */
.fg-hamburger:before { content: '\e849'; } /* '' */
.fg-info:before { content: '\e84a'; } /* '' */
.fg-minimizar:before { content: '\e84b'; } /* '' */
.fg-seta_direita:before { content: '\e84c'; } /* '' */
.fg-restaurar:before { content: '\e84d'; } /* '' */
.fg-globo_gestal:before { content: '\e84e'; } /* '' */
.fg-logo_smartconfigurador:before { content: '\e84f'; } /* '' */
.fg-circulo_erro:before { content: '\e850'; } /* '' */
.fg-circulo_ok:before { content: '\e851'; } /* '' */
.fg-wifi_atencao:before { content: '\e852'; } /* '' */
.fg-wifi_off:before { content: '\e853'; } /* '' */
.fg-wifi_otimo:before { content: '\e854'; } /* '' */
.fg-wifi_bom:before { content: '\e855'; } /* '' */
.fg-wifi_fraco:before { content: '\e856'; } /* '' */
.fg-procura_wifi:before { content: '\e857'; } /* '' */
.fg-medidor_gas:before { content: '\e858'; } /* '' */
.fg-aponta_esquerda:before { content: '\e859'; } /* '' */
.fg-aponta_cima:before { content: '\e85a'; } /* '' */
.fg-aponta_direita:before { content: '\e85b'; } /* '' */
.fg-aponta_baixo:before { content: '\e85c'; } /* '' */
.fg-bateria_carga0:before { content: '\e85d'; } /* '' */
.fg-bateria_carga1:before { content: '\e85e'; } /* '' */
.fg-bateria_carga2:before { content: '\e85f'; } /* '' */
.fg-bateria_carga3:before { content: '\e860'; } /* '' */
.fg-bateria_carga4:before { content: '\e861'; } /* '' */
.fg-bateria_carga5:before { content: '\e862'; } /* '' */
.fg-bateria_energia:before { content: '\e863'; } /* '' */
.fg-bateria_atencao:before { content: '\e864'; } /* '' */
.fg-bateria_off:before { content: '\e865'; } /* '' */
.fg-microfone:before { content: '\e866'; } /* '' */
.fg-microfone_off:before { content: '\e867'; } /* '' */
.fg-sinal_1:before { content: '\e868'; } /* '' */
.fg-sinal_2:before { content: '\e869'; } /* '' */
.fg-sinal_3:before { content: '\e86a'; } /* '' */
.fg-sinal_4:before { content: '\e86b'; } /* '' */
.fg-sinal_off:before { content: '\e86c'; } /* '' */
.fg-bateria_temperatura:before { content: '\e86d'; } /* '' */
.fg-monitores:before { content: '\e86e'; } /* '' */
.fg-sim_card:before { content: '\e86f'; } /* '' */
.fg-sim_cards:before { content: '\e870'; } /* '' */
.fg-arquivo_atencao:before { content: '\e871'; } /* '' */
.fg-bandeira:before { content: '\e872'; } /* '' */
.fg-bandeira_completa:before { content: '\e873'; } /* '' */
.fg-chat:before { content: '\e874'; } /* '' */
.fg-chat_atencao:before { content: '\e875'; } /* '' */
.fg-chat_ajuda:before { content: '\e876'; } /* '' */
.fg-chats:before { content: '\e877'; } /* '' */
.fg-circulo_atencao:before { content: '\e878'; } /* '' */
.fg-cliente_atencao:before { content: '\e879'; } /* '' */
.fg-cliente_bloqueado:before { content: '\e87a'; } /* '' */
.fg-cliente_erro:before { content: '\e87b'; } /* '' */
.fg-borracha:before { content: '\e87c'; } /* '' */
.fg-cliente_ok:before { content: '\e87d'; } /* '' */
.fg-cliente_protegido:before { content: '\e87e'; } /* '' */
.fg-cloud_download:before { content: '\e87f'; } /* '' */
.fg-cloud_atencao:before { content: '\e880'; } /* '' */
.fg-computador:before { content: '\e881'; } /* '' */
.fg-emails:before { content: '\e882'; } /* '' */
.fg-lampada_atencao:before { content: '\e883'; } /* '' */
.fg-lixeira:before { content: '\e884'; } /* '' */
.fg-monitor-configurar:before { content: '\e885'; } /* '' */
.fg-monitor_atencao:before { content: '\e886'; } /* '' */
.fg-monitor_off:before { content: '\e887'; } /* '' */
.fg-pin:before { content: '\e888'; } /* '' */
.fg-pin_bloqueado:before { content: '\e889'; } /* '' */
.fg-pin_off:before { content: '\e88a'; } /* '' */
.fg-pin_atencao:before { content: '\e88b'; } /* '' */
.fg-pin_mais:before { content: '\e88c'; } /* '' */
.fg-pin_mapa:before { content: '\e88d'; } /* '' */
.fg-pin_menos:before { content: '\e88e'; } /* '' */
.fg-pin_ok:before { content: '\e88f'; } /* '' */
.fg-plugue:before { content: '\e890'; } /* '' */
.fg-plugue_energia:before { content: '\e891'; } /* '' */
.fg-plugue_atencao:before { content: '\e892'; } /* '' */
.fg-plugue_erro:before { content: '\e893'; } /* '' */
.fg-plugue_mais:before { content: '\e894'; } /* '' */
.fg-plugue_menos:before { content: '\e895'; } /* '' */
.fg-plugue_ok:before { content: '\e896'; } /* '' */
.fg-protegido:before { content: '\e897'; } /* '' */
.fg-protegido_atencao:before { content: '\e898'; } /* '' */
.fg-quadrado_atencao:before { content: '\e899'; } /* '' */
.fg-transformador:before { content: '\e89a'; } /* '' */
.fg-triangulo_atencao:before { content: '\e89b'; } /* '' */
.fg-usuario:before { content: '\e89c'; } /* '' */
.fg-usuario_bloqueado:before { content: '\e89d'; } /* '' */
.fg-usuario_chat:before { content: '\e89e'; } /* '' */
.fg-usuario_configurar:before { content: '\e89f'; } /* '' */
.fg-usuario_desbloqueado:before { content: '\e8a0'; } /* '' */
.fg-usuario_editar:before { content: '\e8a1'; } /* '' */
.fg-usuario_off:before { content: '\e8a2'; } /* '' */
.fg-usuario_erro:before { content: '\e8a3'; } /* '' */
.fg-usuario_mais:before { content: '\e8a4'; } /* '' */
.fg-usuario_menos:before { content: '\e8a5'; } /* '' */
.fg-usuario_ok:before { content: '\e8a6'; } /* '' */
.fg-usuario_protegido:before { content: '\e8a7'; } /* '' */
.fg-usuarios:before { content: '\e8a8'; } /* '' */
.fg-circulo_0:before { content: '\e8a9'; } /* '' */
.fg-circulo_1:before { content: '\e8aa'; } /* '' */
.fg-circulo_2:before { content: '\e8ab'; } /* '' */
.fg-circulo_3:before { content: '\e8ac'; } /* '' */
.fg-circulo_4:before { content: '\e8ad'; } /* '' */
.fg-circulo_5:before { content: '\e8ae'; } /* '' */
.fg-circulo_6:before { content: '\e8af'; } /* '' */
.fg-circulo_7:before { content: '\e8b0'; } /* '' */
.fg-circulo_8:before { content: '\e8b1'; } /* '' */
.fg-circulo_9:before { content: '\e8b2'; } /* '' */
.fg-circulo_radiacao:before { content: '\e8b3'; } /* '' */
.fg-luz_emergencia:before { content: '\e8b4'; } /* '' */
.fg-luz_emergencia_on:before { content: '\e8b5'; } /* '' */
.fg-interruptor_on:before { content: '\e8b6'; } /* '' */
.fg-lampada_dollar:before { content: '\e8b8'; } /* '' */
.fg-lampada_off:before { content: '\e8b9'; } /* '' */
.fg-gotas:before { content: '\e8ba'; } /* '' */
.fg-atencao:before { content: '\e8bb'; } /* '' */
.fg-erro:before { content: '\e8bc'; } /* '' */
.fg-ok:before { content: '\e8bd'; } /* '' */
.fg-estrela:before { content: '\e8be'; } /* '' */
.fg-calendario:before { content: '\e8bf'; } /* '' */
.fg-grid2:before { content: '\e8c0'; } /* '' */
.fg-like:before { content: '\e8c1'; } /* '' */
.fg-dislike:before { content: '\e8c2'; } /* '' */
.fg-chave:before { content: '\e8c3'; } /* '' */
.fg-aperto_mao:before { content: '\e8c4'; } /* '' */
.fg-dedo_digital:before { content: '\e8c5'; } /* '' */
.fg-terra_americas:before { content: '\e8c6'; } /* '' */
.fg-globo:before { content: '\e8c7'; } /* '' */
.fg-alta_tensao:before { content: '\e8c8'; } /* '' */
.fg-alta_tensao_dupla:before { content: '\e8c9'; } /* '' */
.fg-elipses_horizontal:before { content: '\e8ca'; } /* '' */
.fg-elipses_vertical:before { content: '\e8cb'; } /* '' */
.fg-ferramenta:before { content: '\e8cc'; } /* '' */
.fg-fone_circulo:before { content: '\e8cd'; } /* '' */
.fg-fone:before { content: '\e8ce'; } /* '' */
.fg-fone_erro:before { content: '\e8cf'; } /* '' */
.fg-fone_mais:before { content: '\e8d0'; } /* '' */
.fg-fone_office:before { content: '\e8d1'; } /* '' */
.fg-fone_volume_baixo:before { content: '\e8d2'; } /* '' */
.fg-fone_volume_medio:before { content: '\e8d3'; } /* '' */
.fg-fone_volume_alto:before { content: '\e8d4'; } /* '' */
.fg-gear_duplo:before { content: '\e8d5'; } /* '' */
.fg-laptop_mobile:before { content: '\e8d6'; } /* '' */
.fg-mobile_sinal_in:before { content: '\e8d7'; } /* '' */
.fg-mobile_sinal_out:before { content: '\e8d8'; } /* '' */
.fg-pen_drive:before { content: '\e8d9'; } /* '' */
.fg-prancheta_lista:before { content: '\e8da'; } /* '' */
.fg-prancheta_lista_ok:before { content: '\e8db'; } /* '' */
.fg-prancheta_ok:before { content: '\e8dc'; } /* '' */
.fg-prancheta_pesquisa_ok:before { content: '\e8dd'; } /* '' */
.fg-salvar_processador:before { content: '\e8de'; } /* '' */
.fg-processador:before { content: '\e8df'; } /* '' */
.fg-impressora:before { content: '\e8e0'; } /* '' */
.fg-impressora_off:before { content: '\e8e1'; } /* '' */
.fg-impressora_pesquisa:before { content: '\e8e2'; } /* '' */
.fg-arquivo_pdf:before { content: '\e8e3'; } /* '' */
.fg-arquivo_txt:before { content: '\e8e4'; } /* '' */
.fg-arquivo_csv:before { content: '\e8e5'; } /* '' */
.fg-arquivo_xls:before { content: '\e8e6'; } /* '' */
.fg-arquivo_doc:before { content: '\e8e7'; } /* '' */
.fg-arquivo_dat:before { content: '\e8e8'; } /* '' */
.fg-sinal_menor_circulo:before { content: '\e8e9'; } /* '' */
.fg-sinal_maior_circulo:before { content: '\e8ea'; } /* '' */
.fg-seta_esquerda_circulo:before { content: '\e8eb'; } /* '' */
.fg-seta_direita_circulo:before { content: '\e8ec'; } /* '' */
.fg-esquerda_circulo:before { content: '\e8ed'; } /* '' */
.fg-direita_circulo:before { content: '\e8ee'; } /* '' */
.fg-sinal_menor:before { content: '\e8ef'; } /* '' */
.fg-sinal_maior:before { content: '\e8f0'; } /* '' */
.fg-configuracao_info:before { content: '\e8f1'; } /* '' */
.fg-configuracao_energia:before { content: '\e8f2'; } /* '' */
.fg-configuracao_datahora:before { content: '\e8f3'; } /* '' */
.fg-quadrado:before { content: '\e8f4'; } /* '' */
.fg-quadrado_check:before { content: '\e8f5'; } /* '' */
.fg-producao:before { content: '\e8f6'; } /* '' */
.fg-led_aceso:before { content: '\e8f7'; } /* '' */
.fg-sd_card:before { content: '\e8f8'; } /* '' */
.fg-servidor_iot:before { content: '\e8f9'; } /* '' */
.fg-historico:before { content: '\e8fa'; } /* '' */
.fg-historico_check:before { content: '\e8fb'; } /* '' */
.fg-historico_info:before { content: '\e8fc'; } /* '' */
.fg-historico_energia:before { content: '\e8fd'; } /* '' */
.fg-historico_utilidades:before { content: '\e8fe'; } /* '' */
.fg-historico_gas:before { content: '\e8ff'; } /* '' */
.fg-historico_limpar:before { content: '\e900'; } /* '' */
.fg-reboot:before { content: '\e901'; } /* '' */
.fg-sair_configuracao:before { content: '\e902'; } /* '' */
.fg-entrar_configuracao:before { content: '\e903'; } /* '' */
.fg-suporte:before { content: '\e904'; } /* '' */
.fg-hora_extra:before { content: '\e905'; } /* '' */
.fg-processo_inspecao:before { content: '\e906'; } /* '' */
.fg-processo_liberacao:before { content: '\e907'; } /* '' */
.fg-processo_troca:before { content: '\e908'; } /* '' */
.fg-processo_reformado:before { content: '\e909'; } /* '' */
.fg-processo_perdatotal:before { content: '\e90a'; } /* '' */
.fg-mais_circulo:before { content: '\e90b'; } /* '' */
.fg-menos_circulo:before { content: '\e90c'; } /* '' */
.fg-cima_circulo:before { content: '\e90d'; } /* '' */
.fg-baixo_circulo:before { content: '\e90e'; } /* '' */
.fg-teclado:before { content: '\e90f'; } /* '' */
.fg-livro:before { content: '\e910'; } /* '' */
.fg-livro_magico:before { content: '\e911'; } /* '' */
.fg-varinha:before { content: '\e912'; } /* '' */
.fg-varinha_magica:before { content: '\e913'; } /* '' */
.fg-suite:before { content: '\e914'; } /* '' */
.fg-gateway_en:before { content: '\e915'; } /* '' */
.fg-gate:before { content: '\e916'; } /* '' */
.fg-construcao:before { content: '\e917'; } /* '' */
.fg-rolo_pintar:before { content: '\e918'; } /* '' */
.fg-pincel:before { content: '\e919'; } /* '' */
.fg-caixa_ferramentas:before { content: '\e91a'; } /* '' */
.fg-trabalhador:before { content: '\e91b'; } /* '' */
.fg-trabalhando:before { content: '\e91c'; } /* '' */
.fg-atencao_trabalhando:before { content: '\e91d'; } /* '' */
.fg-quadrado_interrogacao:before { content: '\e91e'; } /* '' */
.fg-interrogacao:before { content: '\e91f'; } /* '' */
.fg-configuracao_bloqueada:before { content: '\e920'; } /* '' */
.fg-configuracao_desbloqueada:before { content: '\e921'; } /* '' */
.fg-gear_off:before { content: '\e922'; } /* '' */
.fg-reset_contador:before { content: '\e923'; } /* '' */
.fg-reset_demanda:before { content: '\e924'; } /* '' */
