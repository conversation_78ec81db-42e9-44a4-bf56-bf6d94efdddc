﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.4.1.9004" targetFramework="net451" />
  <package id="bootstrap" version="3.3.0" targetFramework="net451" />
  <package id="Bootstrap.Datepicker" version="1.6.4" targetFramework="net451" />
  <package id="Bootstrap.Datepicker.Globalize" version="1.0.0" targetFramework="net451" />
  <package id="bootstrap.less" version="3.3.5" targetFramework="net451" />
  <package id="Bootstrap.v3.Datetimepicker" version="4.15.35.1" targetFramework="net451" />
  <package id="Bootstrap.v3.Datetimepicker.CSS" version="4.15.35" targetFramework="net451" />
  <package id="BouncyCastle.Cryptography" version="2.4.0" targetFramework="net462" />
  <package id="cco.selectpicker" version="1.0.0" targetFramework="net451" />
  <package id="Common.Logging" version="3.4.1" targetFramework="net451" />
  <package id="Common.Logging.Core" version="3.4.1" targetFramework="net451" />
  <package id="EntityFramework" version="6.1.3" targetFramework="net462" />
  <package id="itext7" version="7.1.8" targetFramework="net451" />
  <package id="itext7.font-asian" version="7.1.8" targetFramework="net451" />
  <package id="itext7.licensekey" version="3.0.5" targetFramework="net451" />
  <package id="itext7.pdf2data" version="2.1.4" targetFramework="net451" />
  <package id="iTextSharp" version="5.5.13.1" targetFramework="net451" />
  <package id="jQuery" version="1.9.1" targetFramework="net451" />
  <package id="jquery.fancytree.combined" version="2.30.2" targetFramework="net451" />
  <package id="jQuery.FileUpload" version="9.12.3" targetFramework="net451" />
  <package id="jQuery.UI.Combined" version="1.9.0" targetFramework="net451" />
  <package id="jquery-globalize" version="0.1.1" targetFramework="net451" />
  <package id="MailKit" version="4.7.1.1" targetFramework="net462" />
  <package id="Microsoft.AspNet.Identity.Core" version="2.1.0" targetFramework="net451" />
  <package id="Microsoft.AspNet.Identity.EntityFramework" version="2.1.0" targetFramework="net451" />
  <package id="Microsoft.AspNet.Identity.Owin" version="2.1.0" targetFramework="net451" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.3" targetFramework="net451" />
  <package id="Microsoft.AspNet.Razor" version="3.2.3" targetFramework="net451" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.1" targetFramework="net451" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.3" targetFramework="net451" />
  <package id="Microsoft.CSharp" version="4.0.1" targetFramework="net451" />
  <package id="Microsoft.jQuery.Unobtrusive.Validation" version="3.0.0" targetFramework="net451" />
  <package id="Microsoft.Owin" version="3.0.0" targetFramework="net451" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="2.0.0" targetFramework="net451" />
  <package id="Microsoft.Owin.Security" version="3.0.0" targetFramework="net451" />
  <package id="Microsoft.Owin.Security.Cookies" version="2.1.0" targetFramework="net451" />
  <package id="Microsoft.Owin.Security.Facebook" version="2.0.0" targetFramework="net451" />
  <package id="Microsoft.Owin.Security.Google" version="2.0.0" targetFramework="net451" />
  <package id="Microsoft.Owin.Security.MicrosoftAccount" version="2.0.0" targetFramework="net451" />
  <package id="Microsoft.Owin.Security.OAuth" version="2.1.0" targetFramework="net451" />
  <package id="Microsoft.Owin.Security.Twitter" version="2.0.0" targetFramework="net451" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net451" />
  <package id="MimeKit" version="4.7.1" targetFramework="net462" />
  <package id="Modernizr" version="2.6.2" targetFramework="net451" />
  <package id="Moment.js" version="2.9.0" targetFramework="net451" />
  <package id="Newtonsoft.Json" version="13.0.2" targetFramework="net462" />
  <package id="NPOI" version="2.2.1" targetFramework="net451" />
  <package id="Owin" version="1.0" targetFramework="net451" />
  <package id="PdfiumViewer" version="********" targetFramework="net451" />
  <package id="Portable.BouncyCastle" version="*******" targetFramework="net451" />
  <package id="Rotativa" version="1.7.4-rc" targetFramework="net451" />
  <package id="SharpZipLib" version="0.86.0" targetFramework="net451" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net462" />
  <package id="System.Formats.Asn1" version="8.0.1" targetFramework="net462" />
  <package id="System.Memory" version="4.5.5" targetFramework="net462" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net462" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net462" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net462" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net462" />
  <package id="WebGrease" version="1.5.2" targetFramework="net451" />
  <package id="ZXing.Net" version="0.16.9" targetFramework="net462" />
</packages>