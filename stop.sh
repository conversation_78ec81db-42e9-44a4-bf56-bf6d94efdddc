#!/bin/bash

# Script para parar o ambiente Docker do SmartEnergy

echo "=== Parando SmartEnergy Docker Environment ==="

# Detectar sistema operacional
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    COMPOSE_FILE="docker/docker-compose.linux.yml"
    echo "🐧 Sistema Linux detectado"
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
    COMPOSE_FILE="docker/docker-compose.yml"
    echo "🪟 Sistema Windows detectado"
else
    COMPOSE_FILE="docker/docker-compose.linux.yml"
    echo "⚠️  Sistema não detectado, usando configuração Linux"
fi

# Parar e remover containers
echo "🛑 Parando containers..."
docker-compose -f $COMPOSE_FILE down

# Opção para remover volumes (dados)
read -p "🗑️  Deseja remover os volumes (dados do banco)? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️  Removendo volumes..."
    docker-compose -f $COMPOSE_FILE down -v
    docker volume prune -f
fi

# Opção para remover imagens
read -p "🗑️  Deseja remover as imagens Docker? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️  Removendo imagens..."
    docker-compose -f $COMPOSE_FILE down --rmi all
fi

echo "✅ SmartEnergy parado com sucesso!"
