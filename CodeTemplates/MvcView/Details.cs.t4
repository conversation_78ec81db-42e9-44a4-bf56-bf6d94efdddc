﻿<#@ template language="C#" HostSpecific="True" #>
<#@ output extension=".cshtml" #>
<#@ include file="Imports.include.t4" #>
@model <#= ViewDataTypeName #>
<#
// The following chained if-statement outputs the file header code and markup for a partial view, a view using a layout page, or a regular view.
if(IsPartialView) {
#>

<#
} else if(IsLayoutPageSelected) {
#>

@{
    ViewBag.Title = "<#= ViewName#>";
<#
if (!String.IsNullOrEmpty(LayoutPageFile)) {
#>
    Layout = "<#= LayoutPageFile#>";
<#
}
#>
}

<div class="row wrapper border-bottom white-bg page-heading">
    <div class="col-sm-4">
        <h2><#= ViewName#></h2>
        <ol class="breadcrumb">
            <li>
                @Html.ActionLink("List", "Index")
            </li>
            <li class="active">
                <strong><#= ViewName#></strong>
            </li>
        </ol>
    </div>
</div>


<#
} else {
#>

@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title><#= ViewName #></title>
</head>
<body>
<#
    PushIndent("    ");
}
#>


<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox float-e-margins">
                <div class="ibox-title">
                    <h5><#= ViewName#> <#= ViewDataTypeShortName #></h5>
                </div>
                <div class="ibox-content">


				    <dl class="dl-horizontal">
<#
foreach (PropertyMetadata property in ModelMetadata.Properties) {
    if (property.Scaffold && !property.IsPrimaryKey && !property.IsForeignKey) {
#>
<#
        // This is a temporary work around until core scaffolding provides support for independent associations.
        if (property.IsAssociation && GetRelatedModelMetadata(property) == null) {
            continue;
        }
#>
        <dt>
            @Html.DisplayNameFor(model => model.<#= GetValueExpression(property) #>)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.<#= GetValueExpression(property) #>)
        </dd>

<#
    }
}
#>
    </dl>


	<#
string pkName = GetPrimaryKeyName();
if (pkName != null) {
#>
    @Html.ActionLink("Edit", "Edit", new { id = Model.<#= pkName #> }, new { @class = "btn btn-primary"})
    @Html.ActionLink("Back to List", "Index", null, new { @class = "btn btn-white"})
<#
} else {
#>
    @Html.ActionLink("Edit", "Edit", new { /* id = Model.PrimaryKey */ }, new { @class = "btn btn-primary"})
    @Html.ActionLink("Back to List", "Index",null , new { @class = "btn btn-white"})
<#
}
#>

                </div>
            </div>
        </div>
    </div>
 </div>













<#
// The following code closes the tag used in the case of a view using a layout page and the body and html tags in the case of a regular view page
#>
<#
if(!IsPartialView && !IsLayoutPageSelected) {
    ClearIndent();
#>
</body>
</html>
<#
}
#>
<#@ include file="ModelMetadataFunctions.cs.include.t4" #>