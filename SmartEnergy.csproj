﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <Import Project="..\packages\itext7.pdf2data.2.1.4\Build\itext7.pdf2data.props"
    Condition="Exists('..\packages\itext7.pdf2data.2.1.4\Build\itext7.pdf2data.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props"
    Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{86CADB26-5817-46B6-88C7-76D5753722C3}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SmartEnergy</RootNamespace>
    <AssemblyName>SmartEnergy</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>false</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>04514f31</NuGetPackageImportStamp>
    <Use64BitIISExpress />
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <ItemGroup>
    <Reference
      Include="BouncyCastle.Crypto, Version=1.8.1.0, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>..\packages\Portable.BouncyCastle.1.8.1.3\lib\net40\BouncyCastle.Crypto.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="BouncyCastle.Cryptography, Version=2.0.0.0, Culture=neutral, PublicKeyToken=072edcf4a5328938, processorArchitecture=MSIL">
      <HintPath>..\packages\BouncyCastle.Cryptography.2.4.0\lib\net461\BouncyCastle.Cryptography.dll</HintPath>
    </Reference>
    <Reference
      Include="Common.Logging, Version=3.4.1.0, Culture=neutral, PublicKeyToken=af08829b84f0328e, processorArchitecture=MSIL">
      <HintPath>..\packages\Common.Logging.3.4.1\lib\net40\Common.Logging.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="Common.Logging.Core, Version=3.4.1.0, Culture=neutral, PublicKeyToken=af08829b84f0328e, processorArchitecture=MSIL">
      <HintPath>..\packages\Common.Logging.Core.3.4.1\lib\net40\Common.Logging.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference
      Include="EntityFramework.SqlServer, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference
      Include="ICSharpCode.SharpZipLib, Version=0.86.0.518, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpZipLib.0.86.0\lib\20\ICSharpCode.SharpZipLib.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="itext.barcodes, Version=*******, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itext7.7.1.8\lib\net40\itext.barcodes.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="itext.font_asian, Version=*******, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itext7.font-asian.7.1.8\lib\net40\itext.font_asian.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="itext.forms, Version=*******, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itext7.7.1.8\lib\net40\itext.forms.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="itext.io, Version=*******, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itext7.7.1.8\lib\net40\itext.io.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="itext.kernel, Version=*******, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itext7.7.1.8\lib\net40\itext.kernel.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="itext.layout, Version=*******, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itext7.7.1.8\lib\net40\itext.layout.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="itext.licensekey, Version=3.0.5.0, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itext7.licensekey.3.0.5\lib\net40\itext.licensekey.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="itext.pdf2data, Version=2.1.4.0, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itext7.pdf2data.2.1.4\lib\net40\itext.pdf2data.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="itext.pdfa, Version=*******, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itext7.7.1.8\lib\net40\itext.pdfa.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="itext.sign, Version=*******, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itext7.7.1.8\lib\net40\itext.sign.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="itext.styledxmlparser, Version=*******, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itext7.7.1.8\lib\net40\itext.styledxmlparser.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="itext.svg, Version=*******, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itext7.7.1.8\lib\net40\itext.svg.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="itextsharp, Version=5.5.13.1, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\iTextSharp.5.5.13.1\lib\itextsharp.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="MailKit, Version=4.7.0.0, Culture=neutral, PublicKeyToken=4e064fe7c44a8f1b, processorArchitecture=MSIL">
      <HintPath>..\packages\MailKit.4.7.1.1\lib\net462\MailKit.dll</HintPath>
    </Reference>
    <Reference
      Include="Microsoft.AspNet.Identity.Core, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>
        ..\packages\Microsoft.AspNet.Identity.Core.2.1.0\lib\net45\Microsoft.AspNet.Identity.Core.dll</HintPath>
    </Reference>
    <Reference
      Include="Microsoft.AspNet.Identity.EntityFramework, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>
        ..\packages\Microsoft.AspNet.Identity.EntityFramework.2.1.0\lib\net45\Microsoft.AspNet.Identity.EntityFramework.dll</HintPath>
    </Reference>
    <Reference
      Include="Microsoft.AspNet.Identity.Owin, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>
        ..\packages\Microsoft.AspNet.Identity.Owin.2.1.0\lib\net45\Microsoft.AspNet.Identity.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference
      Include="Microsoft.Owin, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.Owin.3.0.0\lib\net45\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference
      Include="Microsoft.Owin.Security, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.Owin.Security.3.0.0\lib\net45\Microsoft.Owin.Security.dll</HintPath>
    </Reference>
    <Reference
      Include="Microsoft.Owin.Security.Cookies, Version=2.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>
        ..\packages\Microsoft.Owin.Security.Cookies.2.1.0\lib\net45\Microsoft.Owin.Security.Cookies.dll</HintPath>
    </Reference>
    <Reference
      Include="Microsoft.Owin.Security.OAuth, Version=2.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>
        ..\packages\Microsoft.Owin.Security.OAuth.2.1.0\lib\net45\Microsoft.Owin.Security.OAuth.dll</HintPath>
    </Reference>
    <Reference
      Include="MimeKit, Version=4.7.0.0, Culture=neutral, PublicKeyToken=bede1c8a46c66814, processorArchitecture=MSIL">
      <HintPath>..\packages\MimeKit.4.7.1\lib\net462\MimeKit.dll</HintPath>
    </Reference>
    <Reference
      Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference
      Include="NPOI, Version=2.2.1.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.2.1\lib\net40\NPOI.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="NPOI.OOXML, Version=2.2.1.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.2.1\lib\net40\NPOI.OOXML.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="NPOI.OpenXml4Net, Version=2.2.1.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.2.1\lib\net40\NPOI.OpenXml4Net.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="NPOI.OpenXmlFormats, Version=2.2.1.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.2.1\lib\net40\NPOI.OpenXmlFormats.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="PdfiumViewer, Version=2.13.0.0, Culture=neutral, PublicKeyToken=91e4789cfb0609e0, processorArchitecture=MSIL">
      <HintPath>..\packages\PdfiumViewer.2.13.0.0\lib\net20\PdfiumViewer.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Rotativa, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Rotativa.1.7.4-rc\lib\net45\Rotativa.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="SmartEnergyLib, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Modulos\SmartEnergyLib\SmartEnergyLib\bin\Release\SmartEnergyLib.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference
      Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference
      Include="System.Formats.Asn1, Version=8.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Formats.Asn1.8.0.1\lib\net462\System.Formats.Asn1.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference
      Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference
      Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference
      Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>
        ..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.ServiceProcess" />
    <Reference
      Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>
        ..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference
      Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web.Extensions" />
    <Reference
      Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.Helpers.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="System.Web.Mvc, Version=5.2.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.3\lib\net45\System.Web.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>
        ..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference
      Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference
      Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>
        ..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>
        ..\packages\Microsoft.AspNet.Web.Optimization.1.1.1\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>..\packages\WebGrease.1.5.2\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime">
      <Private>True</Private>
      <HintPath>..\packages\Antlr.3.4.1.9004\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Owin">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb">
      <HintPath>
        ..\packages\Microsoft.Owin.Host.SystemWeb.2.0.0\lib\net45\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Facebook">
      <HintPath>
        ..\packages\Microsoft.Owin.Security.Facebook.2.0.0\lib\net45\Microsoft.Owin.Security.Facebook.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Google">
      <HintPath>
        ..\packages\Microsoft.Owin.Security.Google.2.0.0\lib\net45\Microsoft.Owin.Security.Google.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Twitter">
      <HintPath>
        ..\packages\Microsoft.Owin.Security.Twitter.2.0.0\lib\net45\Microsoft.Owin.Security.Twitter.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.MicrosoftAccount">
      <HintPath>
        ..\packages\Microsoft.Owin.Security.MicrosoftAccount.2.0.0\lib\net45\Microsoft.Owin.Security.MicrosoftAccount.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\Startup.Auth.cs" />
    <Compile Include="Controllers\API\API_Oportunidades.cs" />
    <Compile Include="Controllers\API\API_Equipamentos.cs" />
    <Compile Include="Controllers\API\API_Dispositivos.cs" />
    <Compile Include="Controllers\API\API_Empresas.cs" />
    <Compile Include="Controllers\API\API_Usuarios.cs" />
    <Compile Include="Controllers\API\API_Contas.cs" />
    <Compile Include="Controllers\API\API_Devices.cs" />
    <Compile Include="Controllers\Configuracao\GateE\ConfiguracaoGateE_CorrigirFalhaUpload.cs" />
    <Compile Include="Controllers\Configuracao\GateE\ConfiguracaoGateE_Reboot.cs" />
    <Compile Include="Controllers\Configuracao\GateE\ConfiguracaoGateE_LimparHistorico.cs" />
    <Compile Include="Controllers\Configuracao\GateE\ConfiguracaoGateE_DriverSincronismo.cs" />
    <Compile Include="Controllers\Configuracao\GateE\ConfiguracaoGateE_RedeIoT.cs" />
    <Compile Include="Controllers\Configuracao\GateE\ConfiguracaoGateE_Informacoes.cs" />
    <Compile Include="Controllers\Configuracao\GateE\ConfiguracaoGateE_DataHora.cs" />
    <Compile Include="Controllers\Configuracao\GateE\ConfiguracaoGateE_EnvioDados.cs" />
    <Compile Include="Controllers\Configuracao\GateE\ConfiguracaoGateE_AtualizacaoFirmware.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_CtrlAlarme.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_CtrlAlarme_Arquivos.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_Drivers.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_ReceberConfiguracoes.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_EnvioDados.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_Logicas_Arquivos.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_MedCiclo.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_MedCiclo_Arquivos.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_MedAna.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_MedAna_Arquivos.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_MedUtil_Arquivos.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_MedUtil.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_MedEner_DemSup.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_MedEner_VigDem.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_Usuarios.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_Empresa.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_AtualizaLista.cs" />
    <Compile Include="Controllers\Configuracao\GateE\ConfiguracaoGateE_Versao.cs" />
    <Compile Include="Controllers\Configuracao\GateE\ConfiguracaoGateE_MedEner.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_DatasEspeciais.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_MedEner_Arquivos.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_Logicas.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_EntradasDigitais.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_EntradasDigitais_Arquivos.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_Versao.cs" />
    <Compile Include="Controllers\Dashboard\DashboardGeralEnergiaMensal.cs" />
    <Compile Include="Controllers\Filters\LogPageVisits.cs" />
    <Compile Include="Controllers\Login\reCaptcha.cs" />
    <Compile Include="Controllers\Navegacao\NavegacaoBloqueio.cs" />
    <Compile Include="Controllers\Navegacao\NavegacaoSegundoFator.cs" />
    <Compile Include="Controllers\Navegacao\NavegacaoFavoritos.cs" />
    <Compile Include="Controllers\Navegacao\NavegacaoTabelaMedicoes.cs" />
    <Compile Include="Controllers\Navegacao\NavegacaoIntro.cs" />
    <Compile Include="Controllers\Navegacao\NavegacaoLGPD.cs" />
    <Compile Include="Controllers\Navegacao\NavegacaoMudarSenha.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosComparativoConsumo.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoGateE_TesteCampoController.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoGateway.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoGateway_HistoricoSinal.cs" />
    <Compile Include="Controllers\Manutencao\ManutencaoApagarEVFuturo.cs" />
    <Compile Include="Controllers\Suporte\ConfiguracaoGateE_CorrigirFalhaUpload.cs" />
    <Compile Include="Controllers\Suporte\SuporteAnaliseMedicoes.cs" />
    <Compile Include="Controllers\Suporte\SuporteFirmwareMetodos.cs" />
    <Compile Include="Controllers\Suporte\SuporteFirmwares.cs" />
    <Compile Include="Controllers\Suporte\SuporteDrivers.cs" />
    <Compile Include="Controllers\Suporte\SuporteGatewaysAtualizacaoFirmware.cs" />
    <Compile Include="Controllers\Suporte\SuporteGestorGatewaysBateriaFraca.cs" />
    <Compile Include="Controllers\Suporte\SuporteGestorAnaliseGateways.cs" />
    <Compile Include="Controllers\Suporte\SuporteGatewaysBateriaFraca.cs" />
    <Compile Include="Controllers\Suporte\SuporteBuscaGatewayProblemas.cs" />
    <Compile Include="Controllers\Suporte\SuporteBuscaGatewayRecebidos.cs" />
    <Compile Include="Controllers\Suporte\SuporteAnaliseGateways.cs" />
    <Compile Include="Controllers\Suporte\SuporteGatewaysInfo.cs" />
    <Compile Include="Controllers\Suporte\SuporteCookies.cs" />
    <Compile Include="Controllers\Suporte\SuporteController.cs" />
    <Compile Include="Controllers\Suporte\SuporteObservacoesGateway.cs" />
    <Compile Include="Controllers\Account\AccountController.cs" />
    <Compile Include="Controllers\Admins\AdminsController.cs" />
    <Compile Include="Controllers\Alertas\AlertasController.cs" />
    <Compile Include="Controllers\Admins\AdminsGESTAL.cs" />
    <Compile Include="Controllers\Admins\AdminsConsultores.cs" />
    <Compile Include="Controllers\Alertas\AlertasCookies.cs" />
    <Compile Include="Controllers\Admins\AdminsCookies.cs" />
    <Compile Include="Controllers\Analises\AnalisesComparativoContratos.cs" />
    <Compile Include="Controllers\Analises\AnalisesController.cs" />
    <Compile Include="Controllers\Analises\AnalisesCorrecaoFatPot.cs" />
    <Compile Include="Controllers\Analises\AnalisesOportunidadesDemanda.cs" />
    <Compile Include="Controllers\API\API_HistoricoHorario.cs" />
    <Compile Include="Controllers\API\API_MedicoesFatura.cs" />
    <Compile Include="Controllers\API\API_MedicoesLista.cs" />
    <Compile Include="Controllers\API\API_MedicoesConsumo.cs" />
    <Compile Include="Controllers\API\API_Medicoes.cs" />
    <Compile Include="Controllers\API\API_Historico.cs" />
    <Compile Include="Controllers\API\API_SolicitaBD.cs" />
    <Compile Include="Controllers\Agentes\AgentesComercializadoras.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_DataHora.cs" />
    <Compile Include="Controllers\Producao\ProducaoNumeroSerie.cs" />
    <Compile Include="Controllers\Producao\ProducaoController.cs" />
    <Compile Include="Controllers\Producao\ProducaoCookies.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_CtrlDemAcum.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_CtrlDemMed.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_CtrlDemAcum_Arquivos.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_CtrlDemProj.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_CtrlDemMed_Arquivos.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_CtrlDemProj_Arquivos.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_CtrlFatPot_Arquivos.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_CtrlFatPot.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_CtrlAna.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_CtrlAna_Arquivos.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_MedEner.cs" />
    <Compile Include="Controllers\EditorRelat\EditorRelatAtualizar.cs" />
    <Compile Include="Controllers\EditorRelat\EditorRelatClientes.cs" />
    <Compile Include="Controllers\EditorRelat\EditorRelatController.cs" />
    <Compile Include="Controllers\EditorRelat\EditorRelatCookies.cs" />
    <Compile Include="Controllers\EditorRelat\EditorRelatDemanda.cs" />
    <Compile Include="Controllers\EditorRelat\EditorRelatEditar.cs" />
    <Compile Include="Controllers\EditorRelat\EditorRelatPDF.cs" />
    <Compile Include="Controllers\EnviaAlertas\EnviaAlertasEmailController.cs" />
    <Compile Include="Controllers\EnviaRateios\EnviaRateiosController.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoGateways_Servidor_IoT.cs" />
    <Compile Include="Controllers\Home\HomeGestorAnaliseGateways.cs" />
    <Compile Include="Controllers\KPI\KpiConsumo.cs" />
    <Compile Include="Controllers\KPI\KpiDemanda.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoMedicoes_FileUpload.cs" />
    <Compile Include="Controllers\KPI\Kpi_PelaMedicao.cs" />
    <Compile Include="Controllers\KPI\KpiRelatorios.cs" />
    <Compile Include="Controllers\PainelJBS\PainelJBSTabelaMedicoes.cs" />
    <Compile Include="Controllers\PainelJBS\PainelJBSXLS.cs" />
    <Compile Include="Controllers\PainelJBS\PainelJBSCookies.cs" />
    <Compile Include="Controllers\PainelJBS\PainelJBSDemanda.cs" />
    <Compile Include="Controllers\PainelJBS\PainelJBSMultas.cs" />
    <Compile Include="Controllers\Notificacoes\NotificacoesCookies.cs" />
    <Compile Include="Controllers\Notificacoes\NotificacoesController.cs" />
    <Compile Include="Controllers\PainelJBS\PainelJBSCalculos.cs" />
    <Compile Include="Controllers\PainelJBS\PainelJBSController.cs" />
    <Compile Include="Controllers\Home\HomeObservacoesGateway.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosControleSazonalidade.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosHeatMap.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoMedicoes_Mapa.cs" />
    <Compile Include="Controllers\Upload\UploadXLS.cs" />
    <Compile Include="Controllers\Upload\Upload_HistoricoMedicao_WebEnergy_FileUploadCSV.cs" />
    <Compile Include="Controllers\Upload\Upload_HistoricoMedicao_WebEnergy.cs" />
    <Compile Include="Controllers\Upload\Upload_HistoricoMedicao_HX600_FileUploadMDB.cs" />
    <Compile Include="Controllers\Upload\Upload_HistoricoMedicao_HX600.cs" />
    <Compile Include="Controllers\Upload\Upload_UtilidadesMensal_Modelo.cs" />
    <Compile Include="Controllers\Upload\Upload_UtilidadesMensal_FileUploadXLS.cs" />
    <Compile Include="Controllers\Upload\Upload_UtilidadesMensal.cs" />
    <Compile Include="Controllers\Upload\Upload_HistoricoMedicao_FileUploadXLS.cs" />
    <Compile Include="Controllers\Dashboard\DashboardUFER.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosDistribuicaoConsumoGruposMedConfiguracao.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosDistribuicaoConsumoConfiguracao.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosDistribuicaoConsumo.cs" />
    <Compile Include="Controllers\Analises\AnalisesRamoAtividade_Mes.cs" />
    <Compile Include="Controllers\Analises\AnalisesRamoAtividade.cs" />
    <Compile Include="Controllers\Analises\AnalisesHorarioCons.cs" />
    <Compile Include="Controllers\Analises\AnalisesHorarioConsConfiguracao.cs" />
    <Compile Include="Controllers\Analises\AnalisesHorarioConsGruposMedConfiguracao.cs" />
    <Compile Include="Controllers\API\APIController.cs" />
    <Compile Include="Controllers\Agentes\AgentesComercializadorasContatosController.cs" />
    <Compile Include="Controllers\Agentes\AgentesController.cs" />
    <Compile Include="Controllers\Agentes\AgentesCookies.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_CtrlHorario_Arquivos.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_SaidasDigitais.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_SaidasDigitais_Arquivos.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoMenu.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoContatos.cs" />
    <Compile Include="Controllers\Configuracao\GateX\ConfiguracaoGateX_CtrlHorario.cs" />
    <Compile Include="Controllers\ContratosCCEE\ContratosCCEECookies.cs" />
    <Compile Include="Controllers\ContratosCCEE\ContratosCCEEController.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoEmpresas.cs" />
    <Compile Include="Controllers\Ajuda\AjudaController.cs" />
    <Compile Include="Controllers\Ajuda\AjudaCookies.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoEmpresas_FileUpload_PrecosReajustados.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoEmpresas_PrecosReajustadosXLS.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoEmpresas_FileUpload_PROINFA.cs" />
    <Compile Include="Controllers\ContratosCCEE\ContratosCCEE_FileUpload_Sazonalizacoes.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoEmpresas_PROINFAXLS.cs" />
    <Compile Include="Controllers\ContratosCCEE\ContratosCCEE_SazonalizacoesXLS.cs" />
    <Compile Include="Controllers\CPFL\CPFL_ProvisionamentoSemanal.cs" />
    <Compile Include="Controllers\CPFL\CPFL_ContratoEnergia_Finalizou.cs" />
    <Compile Include="Controllers\CPFL\CPFL_ContratoEnergia_InicioVigencia.cs" />
    <Compile Include="Controllers\CPFL\CPFL_ContratoGestao_ReajustePreco.cs" />
    <Compile Include="Controllers\CPFL\CPFL_ContratoGestao_FimVigencia.cs" />
    <Compile Include="Controllers\CPFL\CPFL_ContratoEnergia_ReajustePreco.cs" />
    <Compile Include="Controllers\CPFL\CPFL_XLS.cs" />
    <Compile Include="Controllers\CPFL\CPFL_ContratoEnergia_FimVigencia.cs" />
    <Compile Include="Controllers\Dashboard\DashboardGestor.cs" />
    <Compile Include="Controllers\Download\DownloadUtilizacaoDemanda.cs" />
    <Compile Include="Controllers\Download\DownloadPROINFA.cs" />
    <Compile Include="Controllers\Download\DownloadContratosCCEE.cs" />
    <Compile Include="Controllers\Download\DownloadCadastroClientes.cs" />
    <Compile Include="Controllers\Download\DownloadHistoricoMedicao.cs" />
    <Compile Include="Controllers\Download\DownloadXLSX.cs" />
    <Compile Include="Controllers\EnviaRelatorios\EnviaRelatoriosCiclometroController.cs" />
    <Compile Include="Controllers\CPFL\CPFLController.cs" />
    <Compile Include="Controllers\EnviaRelatorios\EnviaRelatoriosSupervisaoController.cs" />
    <Compile Include="Controllers\EnviaRelatorios\EnviaRelatoriosEAController.cs" />
    <Compile Include="Controllers\EnviaRelatorios\EnviaRelatoriosUtilidadesController.cs" />
    <Compile Include="Controllers\EnviaRelatorios\EnviaRelatoriosConsumoController.cs" />
    <Compile Include="Controllers\EnviaRelatorios\EnviaRelatoriosFatPotController.cs" />
    <Compile Include="Controllers\EnviaRelatorios\EnviaRelatoriosDemandaController.cs" />
    <Compile Include="Controllers\Financas\FinancasPLD.cs" />
    <Compile Include="Controllers\Home\HomeCPFL.cs" />
    <Compile Include="Controllers\ContratosCCEE\ContratosCCEE_FileUpload_PrecosReajustados.cs" />
    <Compile Include="Controllers\Manutencao\ManutencaoAlterarPeriodos.cs" />
    <Compile Include="Controllers\ContratosCCEE\ContratosCCEE_Grupos.cs" />
    <Compile Include="Controllers\CPFL\CPFL_ProvisionamentoSemanal_Tipo1_Tipo2.cs" />
    <Compile Include="Controllers\CPFL\CPFL_ProvisionamentoSemanal_Tipo3.cs" />
    <Compile Include="Controllers\Manutencao\ManutencaoCopiarRegistros.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosConsolidadoEAAnual.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosConsolidadoEAMensal.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosConsolidadoUtilidadesAnual.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosConsolidadoUtilidadesDiario.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosConsolidadoUtilidadesMensal.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosMeteorologia.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosUFER.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosProvisionamentoSemanal_Tipo3.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosProvisionamentoSemanal_Tipo1_Tipo2.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosProvisionamentoSemanal.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoMedicaoMeteorologia.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoMedicaoEAFormula.cs" />
    <Compile Include="Controllers\TempoAtuacao\TempoAtuacaoEntradasDigitais.cs" />
    <Compile Include="Controllers\TempoAtuacao\TempoAtuacaoSaidasDigitais.cs" />
    <Compile Include="Controllers\Upload\Upload_HistoricoMedicao.cs" />
    <Compile Include="Controllers\Usuario\UsuarioPerfilCookies.cs" />
    <Compile Include="Controllers\Upload\Upload_PROINFA.cs" />
    <Compile Include="Controllers\Upload\Upload_SCDE.cs" />
    <Compile Include="Controllers\Upload\UploadCookies.cs" />
    <Compile Include="Controllers\Upload\Upload_SCDE_FileUploadCSV.cs" />
    <Compile Include="Controllers\Financas\FinancasFileUploadPDF.cs" />
    <Compile Include="Controllers\Financas\FinancasCustosUltrapassagem.cs" />
    <Compile Include="Controllers\Financas\FinancasFaturaEner_LerPDF.cs" />
    <Compile Include="Controllers\Financas\FinancasFaturaEner_Historico.cs" />
    <Compile Include="Controllers\Manutencao\ManutencaoExcluirRegistros.cs" />
    <Compile Include="Controllers\Manutencao\ManutencaoMedicoes.cs" />
    <Compile Include="Controllers\Dashboard\DashboardKPI.cs" />
    <Compile Include="Controllers\Download\DownloadController.cs" />
    <Compile Include="Controllers\KPI\KpiFileUpload.cs" />
    <Compile Include="Controllers\Metas\MetasController.cs" />
    <Compile Include="Controllers\Metas\MetasCookies.cs" />
    <Compile Include="Controllers\Metas\MetasFileUpload.cs" />
    <Compile Include="Controllers\Metas\MetasHistorico.cs" />
    <Compile Include="Controllers\Monitoracao\MonitoracaoConsultor.cs" />
    <Compile Include="Controllers\Download\DownloadCookies.cs" />
    <Compile Include="Controllers\KPI\KpiConfiguracao.cs" />
    <Compile Include="Controllers\KPI\KpiHistorico.cs" />
    <Compile Include="Controllers\MercadoLivre\MercadoLivreTarifas_Grupos.cs" />
    <Compile Include="Controllers\MercadoLivre\MercadoLivreController.cs" />
    <Compile Include="Controllers\MercadoLivre\MercadoLivreCookies.cs" />
    <Compile Include="Controllers\Upload\UploadController.cs" />
    <Compile Include="Controllers\Rateio\RateioXLS.cs" />
    <Compile Include="Controllers\Rateio\RateioCalculo.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoGrupoMedicoes.cs" />
    <Compile Include="Controllers\Rateio\RateioConfiguracao.cs" />
    <Compile Include="Controllers\Rateio\RateioCookies.cs" />
    <Compile Include="Controllers\Rateio\RateioController.cs" />
    <Compile Include="Controllers\Rateio\RateioTarifas.cs" />
    <Compile Include="Controllers\Rateio\RateioTarifas_Grupos.cs" />
    <Compile Include="Controllers\KPI\KpiController.cs" />
    <Compile Include="Controllers\KPI\KpiCookies.cs" />
    <Compile Include="Controllers\KPI\KpiIndEconomia.cs" />
    <Compile Include="Controllers\KPI\KpiXLS.cs" />
    <Compile Include="Controllers\Monitoracao\MonitoracaoController.cs" />
    <Compile Include="Controllers\Download\DownloadXLS.cs" />
    <Compile Include="Controllers\KPI\KpiConsumo_PelaMedicao.cs" />
    <Compile Include="Controllers\KPI\KpiDemanda_PelaMedicao.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosConsolidadoDemConsDiario.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosConsolidadoEADiario.cs" />
    <Compile Include="Controllers\ContratosCCEE\ContratosCCEE_PrecosReajustadosXLS.cs" />
    <Compile Include="Controllers\EnviaRelatorios\EnviaRelatoriosController.cs" />
    <Compile Include="Controllers\Simulacao\SimulacaoCookies.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoGatewaysProducao.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoGrupoUsuarios.cs" />
    <Compile Include="Controllers\Simulacao\SimulacaoController.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoRural.cs" />
    <Compile Include="Controllers\Dashboard\DashboardTempo.cs" />
    <Compile Include="Controllers\Dashboard\DashboardEA.cs" />
    <Compile Include="Controllers\Dashboard\DashboardUtilidades.cs" />
    <Compile Include="Controllers\Dashboard\DashboardFatura.cs" />
    <Compile Include="Controllers\Dashboard\DashboardFatPot.cs" />
    <Compile Include="Controllers\Dashboard\DashboardConsumo.cs" />
    <Compile Include="Controllers\Dashboard\DashboardDemanda.cs" />
    <Compile Include="Controllers\Dashboard\DashboardAtualizar.cs" />
    <Compile Include="Controllers\Dashboard\DashboardEditar.cs" />
    <Compile Include="Controllers\Manutencao\ManutencaoFeriados.cs" />
    <Compile Include="Controllers\MercadoLivre\MercadoLivreTarifas_Historico.cs" />
    <Compile Include="Controllers\Manutencao\ManutencaoAnaliseMedicoes.cs" />
    <Compile Include="Controllers\Manutencao\ManutencaoClientesSemLogar.cs" />
    <Compile Include="Controllers\Manutencao\ManutencaoUsuariosSemLogar.cs" />
    <Compile Include="Controllers\Mensagens\MensagensCookies.cs" />
    <Compile Include="Controllers\Financas\FinancasFaturaUtilResumida.cs" />
    <Compile Include="Controllers\Financas\FinancasFaturaUtil_Tipo1.cs" />
    <Compile Include="Controllers\Financas\FinancasFaturaUtil.cs" />
    <Compile Include="Controllers\Financas\FinancasOutros.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoICMS.cs" />
    <Compile Include="Controllers\Admins\AdminsOperadoresConsultor.cs" />
    <Compile Include="Controllers\Financas\FinancasFaturaEnerResumida.cs" />
    <Compile Include="Controllers\Financas\FinancasTarifasUtil.cs" />
    <Compile Include="Controllers\Financas\FinancasTarifasUtil_Grupos.cs" />
    <Compile Include="Controllers\Manutencao\ManutencaoEmailNovoSmartEnergy.cs" />
    <Compile Include="Controllers\Analises\AnalisesCookies.cs" />
    <Compile Include="Controllers\Analises\AnalisesDemContrato.cs" />
    <Compile Include="Controllers\Analises\AnalisesXLS.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosCiclometro.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosConsolidadoDemConsMensal.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosEventosUsuario.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosFatUtilizacao.cs" />
    <Compile Include="Controllers\ContratosCCEE\ContratosCCEE.cs" />
    <Compile Include="Controllers\Dashboard\DashboardClientes.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoContratosCCEE.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoEntradasDigitais.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoMedicaoEnergiaSCDEFormula.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoMedicaoEnergiaSCDE.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoSaidasDigitais.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoMedicoes_OperacaoAssistida.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoClientes_OperacaoAssistida.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoMedicaoCiclometro.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoMedicaoEnergiaFormula.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoMedicaoEnergiaPDAFormula.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoMedicaoUtilidadesFormula.cs" />
    <Compile Include="Controllers\Contato\ContatoCookies.cs" />
    <Compile Include="Controllers\Navegacao\NavegacaoCookies.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoContratos.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoUsuarios.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoMedicoes.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoUnidades.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoGrupoUnidades.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoGateways.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoController.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoClientes.cs" />
    <Compile Include="Controllers\Dashboard\DashboardController.cs" />
    <Compile Include="Controllers\FileUpload\FileUploadController.cs" />
    <Compile Include="Controllers\Ranking\RankingConfiguracao.cs" />
    <Compile Include="Controllers\Ranking\RankingCookies.cs" />
    <Compile Include="Controllers\Ranking\RankingController.cs" />
    <Compile Include="Controllers\Financas\FinancasFaturaEner_Convencional.cs" />
    <Compile Include="Controllers\Financas\FinancasFaturaEner_Verde.cs" />
    <Compile Include="Controllers\Financas\FinancasFaturaEner_Azul.cs" />
    <Compile Include="Controllers\Financas\FinancasFaturaEner.cs" />
    <Compile Include="Controllers\Financas\FinancasFechamentos.cs" />
    <Compile Include="Controllers\Financas\FinancasBandeiras.cs" />
    <Compile Include="Controllers\Financas\FinancasICMS.cs" />
    <Compile Include="Controllers\Agentes\AgentesDistribuidoras.cs" />
    <Compile Include="Controllers\Financas\FinancasPisCofins.cs" />
    <Compile Include="Controllers\Financas\FinancasTarifasEner.cs" />
    <Compile Include="Controllers\Financas\FinancasController.cs" />
    <Compile Include="Controllers\Financas\FinancasCookies.cs" />
    <Compile Include="Controllers\Home\HomeController.cs" />
    <Compile Include="Controllers\Login\LoginController.cs" />
    <Compile Include="Controllers\Manutencao\ManutencaoPreenchimentoDemanda.cs" />
    <Compile Include="Controllers\Manutencao\ManutencaoUsuarioEvento.cs" />
    <Compile Include="Controllers\Manutencao\ManutencaoConstante.cs" />
    <Compile Include="Controllers\Manutencao\ManutencaoBusca.cs" />
    <Compile Include="Controllers\Manutencao\ManutencaoGateways.cs" />
    <Compile Include="Controllers\Manutencao\ManutencaoController.cs" />
    <Compile Include="Controllers\Mensagens\MensagensController.cs" />
    <Compile Include="Controllers\Migracao\MigracaoController.cs" />
    <Compile Include="Controllers\Relatorios\RelatEmailController.cs" />
    <Compile Include="Controllers\Ranking\RankingMensal.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosConsumo.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosController.cs" />
    <Compile Include="Controllers\Dashboard\DashboardCookies.cs" />
    <Compile Include="Controllers\Configuracao\ConfiguracaoCookies.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosCookies.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosConsolidadoDemConsAnual.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosDemReativa.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosDemAtiva.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosEventos.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosFatCarga.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosFatPot.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosEA.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosUtilidades.cs" />
    <Compile Include="Controllers\Financas\FinancasXLS.cs" />
    <Compile Include="Controllers\Ranking\RankingXLS.cs" />
    <Compile Include="Controllers\Relatorios\RelatoriosXLS.cs" />
    <Compile Include="Controllers\Navegacao\NavegacaoController.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoClientes.cs" />
    <Compile Include="Controllers\Home\HomeCookies.cs" />
    <Compile Include="Controllers\Manutencao\ManutencaoCookies.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoGateways.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoMedicaoEA.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoMedicaoEnergiaPDA.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoMedicaoUtilidades.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoMedicoes.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoMedicaoEnergia.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoController.cs" />
    <Compile Include="Controllers\Supervisao\SupervisaoCookies.cs" />
    <Compile Include="Controllers\Contato\ContatoController.cs" />
    <Compile Include="Controllers\Usuario\UsuarioPerfilController.cs" />
    <Compile Include="Controllers\WebSiteToImage\WebSiteToImageController.cs" />
    <Compile Include="Controllers\XML\XMLController.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Helpers\StringExtensions.cs" />
    <Compile Include="Helpers\HMTLHelperExtensions.cs" />
    <Compile Include="Models\AccountViewModels.cs" />
    <Compile Include="Models\ClienteModel.cs" />
    <Compile Include="Models\EmailServices.cs" />
    <Compile Include="Models\EmailViewModels.cs" />
    <Compile Include="Models\IdentityModels.cs" />
    <Compile Include="Models\UsuarioModel.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Resources\ConfiguracoesGateX_Texts.es.Designer.cs">
      <DependentUpon>ConfiguracoesGateX_Texts.es.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\ConfiguracoesGateX_Texts.en.Designer.cs">
      <DependentUpon>ConfiguracoesGateX_Texts.en.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\ConfiguracoesGateX_Texts.Designer.cs">
      <DependentUpon>ConfiguracoesGateX_Texts.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\ContratosCCEETexts.Designer.cs">
      <DependentUpon>ContratosCCEETexts.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\ContratosCCEETexts.en.Designer.cs">
      <DependentUpon>ContratosCCEETexts.en.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\ContratosCCEETexts.es.Designer.cs">
      <DependentUpon>ContratosCCEETexts.es.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\MetasTexts.en.Designer.cs">
      <DependentUpon>MetasTexts.en.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\MetasTexts.es.Designer.cs">
      <DependentUpon>MetasTexts.es.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\MetasTexts.Designer.cs">
      <DependentUpon>MetasTexts.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\KpiTexts.es.Designer.cs">
      <DependentUpon>KpiTexts.es.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\KpiTexts.en.Designer.cs">
      <DependentUpon>KpiTexts.en.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\MensagensTexts.en.Designer.cs">
      <DependentUpon>MensagensTexts.en.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\MensagensTexts.es.Designer.cs">
      <DependentUpon>MensagensTexts.es.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\MensagensTexts.Designer.cs">
      <DependentUpon>MensagensTexts.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\ConfiguracaoTexts.en.Designer.cs">
      <DependentUpon>ConfiguracaoTexts.en.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\ConfiguracaoTexts.es.Designer.cs">
      <DependentUpon>ConfiguracaoTexts.es.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\DashboardTexts.es.Designer.cs">
      <DependentUpon>DashboardTexts.es.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\DashboardTexts.en.Designer.cs">
      <DependentUpon>DashboardTexts.en.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\DashboardTexts.Designer.cs">
      <DependentUpon>DashboardTexts.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\AnalisesTexts.en.Designer.cs">
      <DependentUpon>AnalisesTexts.en.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\AnalisesTexts.es.Designer.cs">
      <DependentUpon>AnalisesTexts.es.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\AnalisesTexts.Designer.cs">
      <DependentUpon>AnalisesTexts.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\KpiTexts.Designer.cs">
      <DependentUpon>KpiTexts.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\RateioTexts.en.Designer.cs">
      <DependentUpon>RateioTexts.en.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\RateioTexts.es.Designer.cs">
      <DependentUpon>RateioTexts.es.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\RateioTexts.Designer.cs">
      <DependentUpon>RateioTexts.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\SimulacaoTexts.en.Designer.cs">
      <DependentUpon>SimulacaoTexts.en.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\SimulacaoTexts.es.Designer.cs">
      <DependentUpon>SimulacaoTexts.es.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\SimulacaoTexts.Designer.cs">
      <DependentUpon>SimulacaoTexts.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\RankingTexts.en.Designer.cs">
      <DependentUpon>RankingTexts.en.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\RankingTexts.es.Designer.cs">
      <DependentUpon>RankingTexts.es.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\RankingTexts.Designer.cs">
      <DependentUpon>RankingTexts.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\FinancasTexts.Designer.cs">
      <DependentUpon>FinancasTexts.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\FinancasTexts.es.Designer.cs">
      <DependentUpon>FinancasTexts.es.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\FinancasTexts.en.Designer.cs">
      <DependentUpon>FinancasTexts.en.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\RelatoriosTexts.es.Designer.cs">
      <DependentUpon>RelatoriosTexts.es.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\RelatoriosTexts.en.Designer.cs">
      <DependentUpon>RelatoriosTexts.en.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\RelatoriosTexts.Designer.cs">
      <DependentUpon>RelatoriosTexts.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\ConfiguracaoTexts.Designer.cs">
      <DependentUpon>ConfiguracaoTexts.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\UsuarioPerfilTexts.es.Designer.cs">
      <DependentUpon>UsuarioPerfilTexts.es.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\UsuarioPerfilTexts.en.Designer.cs">
      <DependentUpon>UsuarioPerfilTexts.en.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\UsuarioPerfilTexts.Designer.cs">
      <DependentUpon>UsuarioPerfilTexts.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\ValidateTexts.es.Designer.cs">
      <DependentUpon>ValidateTexts.es.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\ValidateTexts.en.Designer.cs">
      <DependentUpon>ValidateTexts.en.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\ValidateTexts.Designer.cs">
      <DependentUpon>ValidateTexts.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\ComumTexts.es.Designer.cs">
      <DependentUpon>ComumTexts.es.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\ComumTexts.en.Designer.cs">
      <DependentUpon>ComumTexts.en.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\LoginTexts.Designer.cs">
      <DependentUpon>LoginTexts.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\ComumTexts.Designer.cs">
      <DependentUpon>ComumTexts.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\ErrosTexts.Designer.cs">
      <DependentUpon>ErrosTexts.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\SupervisaoTexts.es.Designer.cs">
      <DependentUpon>SupervisaoTexts.es.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\SupervisaoTexts.en.Designer.cs">
      <DependentUpon>SupervisaoTexts.en.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\SupervisaoTexts.Designer.cs">
      <DependentUpon>SupervisaoTexts.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\MenuTexts.en.Designer.cs">
      <DependentUpon>MenuTexts.en.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\MenuTexts.es.Designer.cs">
      <DependentUpon>MenuTexts.es.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\MenuTexts.Designer.cs">
      <DependentUpon>MenuTexts.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\LoginTexts.es.Designer.cs">
      <DependentUpon>LoginTexts.es.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Resources\LoginTexts.en.Designer.cs">
      <DependentUpon>LoginTexts.en.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Startup.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="bin\SmartCalcDB.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <Content Include="Content\animate.css" />
    <Content Include="Content\bootstrap-datepicker.css" />
    <Content Include="Content\bootstrap-datepicker.min.css" />
    <Content Include="Content\bootstrap-datepicker.standalone.css" />
    <Content Include="Content\bootstrap-datepicker.standalone.min.css" />
    <Content Include="Content\bootstrap-datepicker3.css" />
    <Content Include="Content\bootstrap-datepicker3.min.css" />
    <Content Include="Content\bootstrap-datepicker3.standalone.css" />
    <Content Include="Content\bootstrap-datepicker3.standalone.min.css" />
    <Content Include="Content\bootstrap-datetimepicker.css" />
    <Content Include="Content\bootstrap-datetimepicker.min.css" />
    <Content Include="Content\bootstrap-selectpicker.css" />
    <Content Include="Content\bootstrap-selectpicker.min.css" />
    <Content Include="Content\bootstrap-theme.css" />
    <Content Include="Content\bootstrap-theme.min.css" />
    <Content Include="Content\bootstrap.css" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="Content\Configuracoes.css" />
    <Content Include="Content\fonts\glyphicons-halflings-regular.svg" />
    <Content Include="Content\jQuery.FileUpload\css\jquery.fileupload-noscript.css" />
    <Content Include="Content\jQuery.FileUpload\css\jquery.fileupload-ui-noscript.css" />
    <Content Include="Content\jQuery.FileUpload\css\jquery.fileupload-ui.css" />
    <Content Include="Content\jQuery.FileUpload\css\jquery.fileupload.css" />
    <Content Include="Content\jQuery.FileUpload\img\loading.gif" />
    <Content Include="Content\jQuery.FileUpload\img\progressbar.gif" />
    <Content Include="Content\patterns\header-profile-skin-1.png" />
    <Content Include="Content\patterns\header-profile-skin-4.png" />
    <Content Include="Content\patterns\header-profile-skin-5.png" />
    <Content Include="Content\plugins\FancyTree\skin-awesome\ui.fancytree.css" />
    <Content Include="Content\plugins\FancyTree\skin-awesome\ui.fancytree.min.css" />
    <Content Include="Content\plugins\FancyTree\skin-awesome\vline-rtl.gif" />
    <Content Include="Content\plugins\FancyTree\skin-awesome\vline.gif" />
    <Content Include="Content\plugins\FancyTree\skin-bootstrap-n\ui.fancytree.css" />
    <Content Include="Content\plugins\FancyTree\skin-bootstrap-n\ui.fancytree.min.css" />
    <Content Include="Content\plugins\FancyTree\skin-bootstrap-n\vline-rtl.gif" />
    <Content Include="Content\plugins\FancyTree\skin-bootstrap-n\vline.gif" />
    <Content Include="Content\plugins\FancyTree\skin-bootstrap\ui.fancytree.css" />
    <Content Include="Content\plugins\FancyTree\skin-bootstrap\ui.fancytree.min.css" />
    <Content Include="Content\plugins\FancyTree\skin-bootstrap\vline-rtl.gif" />
    <Content Include="Content\plugins\FancyTree\skin-bootstrap\vline.gif" />
    <Content Include="Content\plugins\FancyTree\skin-lion\icons-rtl.gif" />
    <Content Include="Content\plugins\FancyTree\skin-lion\icons.gif" />
    <Content Include="Content\plugins\FancyTree\skin-lion\loading.gif" />
    <Content Include="Content\plugins\FancyTree\skin-lion\ui.fancytree.css" />
    <Content Include="Content\plugins\FancyTree\skin-lion\ui.fancytree.min.css" />
    <Content Include="Content\plugins\FancyTree\skin-lion\vline-rtl.gif" />
    <Content Include="Content\plugins\FancyTree\skin-lion\vline.gif" />
    <Content Include="Content\plugins\FancyTree\skin-material\ui.fancytree.css" />
    <Content Include="Content\plugins\FancyTree\skin-material\ui.fancytree.min.css" />
    <Content Include="Content\plugins\FancyTree\skin-material\vline-rtl.gif" />
    <Content Include="Content\plugins\FancyTree\skin-material\vline.gif" />
    <Content Include="Content\plugins\FancyTree\skin-themeroller\icons-rtl.gif" />
    <Content Include="Content\plugins\FancyTree\skin-themeroller\icons.gif" />
    <Content Include="Content\plugins\FancyTree\skin-themeroller\loading.gif" />
    <Content Include="Content\plugins\FancyTree\skin-themeroller\ui.fancytree.css" />
    <Content Include="Content\plugins\FancyTree\skin-themeroller\ui.fancytree.min.css" />
    <Content Include="Content\plugins\FancyTree\skin-themeroller\vline-rtl.gif" />
    <Content Include="Content\plugins\FancyTree\skin-themeroller\vline.gif" />
    <Content Include="Content\plugins\FancyTree\skin-vista\icons-rtl.gif" />
    <Content Include="Content\plugins\FancyTree\skin-vista\icons.gif" />
    <Content Include="Content\plugins\FancyTree\skin-vista\loading.gif" />
    <Content Include="Content\plugins\FancyTree\skin-vista\ui.fancytree.css" />
    <Content Include="Content\plugins\FancyTree\skin-vista\ui.fancytree.min.css" />
    <Content Include="Content\plugins\FancyTree\skin-vista\vline-rtl.gif" />
    <Content Include="Content\plugins\FancyTree\skin-vista\vline.gif" />
    <Content Include="Content\plugins\FancyTree\skin-win7\icons-rtl.gif" />
    <Content Include="Content\plugins\FancyTree\skin-win7\icons.gif" />
    <Content Include="Content\plugins\FancyTree\skin-win7\loading.gif" />
    <Content Include="Content\plugins\FancyTree\skin-win7\ui.fancytree.css" />
    <Content Include="Content\plugins\FancyTree\skin-win7\ui.fancytree.min.css" />
    <Content Include="Content\plugins\FancyTree\skin-win7\vline-rtl.gif" />
    <Content Include="Content\plugins\FancyTree\skin-win7\vline.gif" />
    <Content Include="Content\plugins\FancyTree\skin-win8-n\icons-rtl.gif" />
    <Content Include="Content\plugins\FancyTree\skin-win8-n\icons.gif" />
    <Content Include="Content\plugins\FancyTree\skin-win8-n\loading.gif" />
    <Content Include="Content\plugins\FancyTree\skin-win8-n\ui.fancytree.css" />
    <Content Include="Content\plugins\FancyTree\skin-win8-n\ui.fancytree.min.css" />
    <Content Include="Content\plugins\FancyTree\skin-win8-n\vline-rtl.gif" />
    <Content Include="Content\plugins\FancyTree\skin-win8-n\vline.gif" />
    <Content Include="Content\plugins\FancyTree\skin-win8-xxl\icons-rtl.gif" />
    <Content Include="Content\plugins\FancyTree\skin-win8-xxl\icons.gif" />
    <Content Include="Content\plugins\FancyTree\skin-win8-xxl\loading.gif" />
    <Content Include="Content\plugins\FancyTree\skin-win8-xxl\ui.fancytree.css" />
    <Content Include="Content\plugins\FancyTree\skin-win8-xxl\ui.fancytree.min.css" />
    <Content Include="Content\plugins\FancyTree\skin-win8-xxl\vline-rtl.gif" />
    <Content Include="Content\plugins\FancyTree\skin-win8-xxl\vline.gif" />
    <Content Include="Content\plugins\FancyTree\skin-win8\icons-rtl.gif" />
    <Content Include="Content\plugins\FancyTree\skin-win8\icons.gif" />
    <Content Include="Content\plugins\FancyTree\skin-win8\loading.gif" />
    <Content Include="Content\plugins\FancyTree\skin-win8\ui.fancytree.css" />
    <Content Include="Content\plugins\FancyTree\skin-win8\ui.fancytree.min.css" />
    <Content Include="Content\plugins\FancyTree\skin-win8\vline-rtl.gif" />
    <Content Include="Content\plugins\FancyTree\skin-win8\vline.gif" />
    <Content Include="Content\plugins\FancyTree\skin-xp\icons-rtl.gif" />
    <Content Include="Content\plugins\FancyTree\skin-xp\icons.gif" />
    <Content Include="Content\plugins\FancyTree\skin-xp\loading.gif" />
    <Content Include="Content\plugins\FancyTree\skin-xp\ui.fancytree.css" />
    <Content Include="Content\plugins\FancyTree\skin-xp\ui.fancytree.min.css" />
    <Content Include="Content\plugins\FancyTree\skin-xp\vline-rtl.gif" />
    <Content Include="Content\plugins\FancyTree\skin-xp\vline.gif" />
    <Content Include="Content\Padrao.css" />
    <Content Include="Content\RelatoriosPDF.css" />
    <Content Include="Content\Site.css" />
    <Content Include="Content\Supervisao.css" />
    <Content Include="Content\Relatorios.css" />
    <Content Include="Content\Dashboard.css" />
    <Content Include="Content\login\bg-carousel-01.jpg" />
    <Content Include="Content\login\bg-carousel-02.jpg" />
    <Content Include="Content\login\bg-topo.png" />
    <Content Include="Content\login\logo_gestal_g.png" />
    <Content Include="Content\login\logo_gestal_t.png" />
    <Content Include="Content\login\logo_gestal_t_gg.png" />
    <Content Include="Content\patterns\congruent_pentagon.png" />
    <Content Include="Content\patterns\header-profile-skin-2.png" />
    <Content Include="Content\patterns\header-profile-skin-0.png" />
    <Content Include="Content\patterns\header-profile-skin-3.png" />
    <Content Include="Content\patterns\header-profile.png" />
    <Content Include="Content\patterns\otis_redding.png" />
    <Content Include="Content\patterns\shattered.png" />
    <Content Include="Content\patterns\triangular.png" />
    <Content Include="Content\plugins\awesome-bootstrap-checkbox\awesome-bootstrap-checkbox.css" />
    <Content Include="Content\plugins\blueimp\css\blueimp-gallery-indicator.css" />
    <Content Include="Content\plugins\blueimp\css\blueimp-gallery-video.css" />
    <Content Include="Content\plugins\blueimp\css\blueimp-gallery.css" />
    <Content Include="Content\plugins\blueimp\css\blueimp-gallery.min.css" />
    <Content Include="Content\plugins\blueimp\css\demo.css" />
    <Content Include="Content\plugins\blueimp\img\error.png" />
    <Content Include="Content\plugins\blueimp\img\error.svg" />
    <Content Include="Content\plugins\blueimp\img\loading.gif" />
    <Content Include="Content\plugins\blueimp\img\play-pause.png" />
    <Content Include="Content\plugins\blueimp\img\play-pause.svg" />
    <Content Include="Content\plugins\blueimp\img\video-play.png" />
    <Content Include="Content\plugins\blueimp\img\video-play.svg" />
    <Content Include="Content\plugins\bootstrap-markdown\bootstrap-markdown.min.css" />
    <Content Include="Content\plugins\bootstrap-rtl\bootstrap-rtl.css" />
    <Content Include="Content\plugins\bootstrap-rtl\bootstrap-rtl.min.css" />
    <Content Include="Content\plugins\bootstrapTour\bootstrap-tour.min.css" />
    <Content Include="Content\plugins\c3\c3.min.css" />
    <Content Include="Content\plugins\chartist\chartist.min.css" />
    <Content Include="Content\plugins\chosen\chosen-sprite%402x.png" />
    <Content Include="Content\plugins\chosen\chosen-sprite.png" />
    <Content Include="Content\plugins\chosen\chosen.css" />
    <Content Include="Content\plugins\clockpicker\clockpicker.css" />
    <Content Include="Content\plugins\codemirror\ambiance.css" />
    <Content Include="Content\plugins\codemirror\codemirror.css" />
    <Content Include="Content\plugins\colorpicker\bootstrap-colorpicker.min.css" />
    <Content Include="Content\plugins\cropper\cropper.min.css" />
    <Content Include="Content\plugins\datapicker\datepicker3.css" />
    <Content Include="Content\plugins\dataTables\datatables.min.css" />
    <Content Include="Content\plugins\daterangepicker\daterangepicker-bs3.css" />
    <Content Include="Content\plugins\dropzone\basic.css" />
    <Content Include="Content\plugins\dropzone\dropzone.css" />
    <Content Include="Content\plugins\footable\fonts\footable.svg" />
    <Content Include="Content\plugins\footable\footable.core.css" />
    <Content Include="Content\plugins\fullcalendar\fullcalendar.css" />
    <Content Include="Content\plugins\fullcalendar\fullcalendar.print.css" />
    <Content Include="Content\plugins\iCheck\custom.css" />
    <Content Include="Content\plugins\iCheck\green%402x.png" />
    <Content Include="Content\plugins\iCheck\green.png" />
    <Content Include="Content\plugins\images\bootstrap-colorpicker\alpha-horizontal.png" />
    <Content Include="Content\plugins\images\bootstrap-colorpicker\alpha.png" />
    <Content Include="Content\plugins\images\bootstrap-colorpicker\hue-horizontal.png" />
    <Content Include="Content\plugins\images\bootstrap-colorpicker\hue.png" />
    <Content Include="Content\plugins\images\bootstrap-colorpicker\saturation.png" />
    <Content Include="Content\plugins\images\sort.png" />
    <Content Include="Content\plugins\images\sort_asc.png" />
    <Content Include="Content\plugins\images\sort_desc.png" />
    <Content Include="Content\plugins\images\sprite-skin-flat.png" />
    <Content Include="Content\plugins\images\sprite-skin-flat2.png" />
    <Content Include="Content\plugins\images\sprite-skin-nice.png" />
    <Content Include="Content\plugins\images\sprite-skin-simple.png" />
    <Content Include="Content\plugins\images\spritemap%402x.png" />
    <Content Include="Content\plugins\images\spritemap.png" />
    <Content Include="Content\plugins\ionRangeSlider\ion.rangeSlider.css" />
    <Content Include="Content\plugins\ionRangeSlider\ion.rangeSlider.skinFlat.css" />
    <Content Include="Content\plugins\ionRangeSlider\ion.rangeSlider.skinNice.css" />
    <Content Include="Content\plugins\ionRangeSlider\ion.rangeSlider.skinSimple.css" />
    <Content Include="Content\plugins\jasny\jasny-bootstrap.min.css" />
    <Content Include="Content\plugins\jqGrid\ui.jqgrid.css" />
    <Content Include="Content\plugins\jQueryUI\images\animated-overlay.gif" />
    <Content Include="Content\plugins\jQueryUI\images\ui-bg_flat_0_aaaaaa_40x100.png" />
    <Content Include="Content\plugins\jQueryUI\images\ui-bg_flat_75_ffffff_40x100.png" />
    <Content Include="Content\plugins\jQueryUI\images\ui-bg_glass_55_fbf9ee_1x400.png" />
    <Content Include="Content\plugins\jQueryUI\images\ui-bg_glass_65_ffffff_1x400.png" />
    <Content Include="Content\plugins\jQueryUI\images\ui-bg_glass_75_dadada_1x400.png" />
    <Content Include="Content\plugins\jQueryUI\images\ui-bg_glass_75_e6e6e6_1x400.png" />
    <Content Include="Content\plugins\jQueryUI\images\ui-bg_glass_95_fef1ec_1x400.png" />
    <Content Include="Content\plugins\jQueryUI\images\ui-bg_highlight-soft_75_cccccc_1x100.png" />
    <Content Include="Content\plugins\jQueryUI\images\ui-icons_222222_256x240.png" />
    <Content Include="Content\plugins\jQueryUI\images\ui-icons_2e83ff_256x240.png" />
    <Content Include="Content\plugins\jQueryUI\images\ui-icons_454545_256x240.png" />
    <Content Include="Content\plugins\jQueryUI\images\ui-icons_888888_256x240.png" />
    <Content Include="Content\plugins\jQueryUI\images\ui-icons_cd0a0a_256x240.png" />
    <Content Include="Content\plugins\jQueryUI\jquery-ui-1.10.4.custom.min.css" />
    <Content Include="Content\plugins\jsTree\32px.png" />
    <Content Include="Content\plugins\jsTree\39px.png" />
    <Content Include="Content\plugins\jsTree\40px.png" />
    <Content Include="Content\plugins\jsTree\style.css" />
    <Content Include="Content\plugins\jsTree\style.min.css" />
    <Content Include="Content\plugins\jsTree\throbber.gif" />
    <Content Include="Content\plugins\ladda\ladda-themeless.min.css" />
    <Content Include="Content\plugins\ladda\ladda.min.css" />
    <Content Include="Content\plugins\morris\morris-0.4.3.min.css" />
    <Content Include="Content\plugins\nouslider\jquery.nouislider.css" />
    <Content Include="Content\plugins\select2\select2.min.css" />
    <Content Include="Content\plugins\slick\ajax-loader.gif" />
    <Content Include="Content\plugins\slick\fonts\slick.svg" />
    <Content Include="Content\plugins\slick\slick-theme.css" />
    <Content Include="Content\plugins\slick\slick.css" />
    <Content Include="Content\plugins\social-buttons\social-buttons.css" />
    <Content Include="Content\plugins\steps\jquery.steps.css" />
    <Content Include="Content\plugins\summernote\summernote.css" />
    <Content Include="Content\plugins\sweetalert\sweetalert.css" />
    <Content Include="Content\plugins\switchery\switchery.css" />
    <Content Include="Content\plugins\toastr\toastr.min.css" />
    <Content Include="Content\plugins\touchspin\jquery.bootstrap-touchspin.min.css" />
    <Content Include="Content\style.css" />
    <Content Include="Content\themes\base\images\ui-bg_flat_0_aaaaaa_40x100.png" />
    <Content Include="Content\themes\base\images\ui-bg_flat_75_ffffff_40x100.png" />
    <Content Include="Content\themes\base\images\ui-bg_glass_55_fbf9ee_1x400.png" />
    <Content Include="Content\themes\base\images\ui-bg_glass_65_ffffff_1x400.png" />
    <Content Include="Content\themes\base\images\ui-bg_glass_75_dadada_1x400.png" />
    <Content Include="Content\themes\base\images\ui-bg_glass_75_e6e6e6_1x400.png" />
    <Content Include="Content\themes\base\images\ui-bg_glass_95_fef1ec_1x400.png" />
    <Content Include="Content\themes\base\images\ui-bg_highlight-soft_75_cccccc_1x100.png" />
    <Content Include="Content\themes\base\images\ui-icons_222222_256x240.png" />
    <Content Include="Content\themes\base\images\ui-icons_2e83ff_256x240.png" />
    <Content Include="Content\themes\base\images\ui-icons_454545_256x240.png" />
    <Content Include="Content\themes\base\images\ui-icons_888888_256x240.png" />
    <Content Include="Content\themes\base\images\ui-icons_cd0a0a_256x240.png" />
    <Content Include="Content\themes\base\jquery-ui.css" />
    <Content Include="Content\themes\base\jquery.ui.accordion.css" />
    <Content Include="Content\themes\base\jquery.ui.all.css" />
    <Content Include="Content\themes\base\jquery.ui.autocomplete.css" />
    <Content Include="Content\themes\base\jquery.ui.base.css" />
    <Content Include="Content\themes\base\jquery.ui.button.css" />
    <Content Include="Content\themes\base\jquery.ui.core.css" />
    <Content Include="Content\themes\base\jquery.ui.datepicker.css" />
    <Content Include="Content\themes\base\jquery.ui.dialog.css" />
    <Content Include="Content\themes\base\jquery.ui.menu.css" />
    <Content Include="Content\themes\base\jquery.ui.progressbar.css" />
    <Content Include="Content\themes\base\jquery.ui.resizable.css" />
    <Content Include="Content\themes\base\jquery.ui.selectable.css" />
    <Content Include="Content\themes\base\jquery.ui.slider.css" />
    <Content Include="Content\themes\base\jquery.ui.spinner.css" />
    <Content Include="Content\themes\base\jquery.ui.tabs.css" />
    <Content Include="Content\themes\base\jquery.ui.theme.css" />
    <Content Include="Content\themes\base\jquery.ui.tooltip.css" />
    <Content Include="Content\themes\base\minified\images\ui-bg_flat_0_aaaaaa_40x100.png" />
    <Content Include="Content\themes\base\minified\images\ui-bg_flat_75_ffffff_40x100.png" />
    <Content Include="Content\themes\base\minified\images\ui-bg_glass_55_fbf9ee_1x400.png" />
    <Content Include="Content\themes\base\minified\images\ui-bg_glass_65_ffffff_1x400.png" />
    <Content Include="Content\themes\base\minified\images\ui-bg_glass_75_dadada_1x400.png" />
    <Content Include="Content\themes\base\minified\images\ui-bg_glass_75_e6e6e6_1x400.png" />
    <Content Include="Content\themes\base\minified\images\ui-bg_glass_95_fef1ec_1x400.png" />
    <Content Include="Content\themes\base\minified\images\ui-bg_highlight-soft_75_cccccc_1x100.png" />
    <Content Include="Content\themes\base\minified\images\ui-icons_222222_256x240.png" />
    <Content Include="Content\themes\base\minified\images\ui-icons_2e83ff_256x240.png" />
    <Content Include="Content\themes\base\minified\images\ui-icons_454545_256x240.png" />
    <Content Include="Content\themes\base\minified\images\ui-icons_888888_256x240.png" />
    <Content Include="Content\themes\base\minified\images\ui-icons_cd0a0a_256x240.png" />
    <Content Include="Content\themes\base\minified\jquery-ui.min.css" />
    <Content Include="Content\themes\base\minified\jquery.ui.accordion.min.css" />
    <Content Include="Content\themes\base\minified\jquery.ui.autocomplete.min.css" />
    <Content Include="Content\themes\base\minified\jquery.ui.button.min.css" />
    <Content Include="Content\themes\base\minified\jquery.ui.core.min.css" />
    <Content Include="Content\themes\base\minified\jquery.ui.datepicker.min.css" />
    <Content Include="Content\themes\base\minified\jquery.ui.dialog.min.css" />
    <Content Include="Content\themes\base\minified\jquery.ui.menu.min.css" />
    <Content Include="Content\themes\base\minified\jquery.ui.progressbar.min.css" />
    <Content Include="Content\themes\base\minified\jquery.ui.resizable.min.css" />
    <Content Include="Content\themes\base\minified\jquery.ui.selectable.min.css" />
    <Content Include="Content\themes\base\minified\jquery.ui.slider.min.css" />
    <Content Include="Content\themes\base\minified\jquery.ui.spinner.min.css" />
    <Content Include="Content\themes\base\minified\jquery.ui.tabs.min.css" />
    <Content Include="Content\themes\base\minified\jquery.ui.theme.min.css" />
    <Content Include="Content\themes\base\minified\jquery.ui.tooltip.min.css" />
    <Content Include="favicon.ico" />
    <Content Include="fonts\elusive-icons-2.0.0\css\elusive-icons.css" />
    <Content Include="fonts\elusive-icons-2.0.0\css\elusive-icons.min.css" />
    <Content Include="fonts\elusive-icons-2.0.0\fonts\elusiveicons-webfont.svg" />
    <Content Include="fonts\font-awesome\css\fa-brands.css" />
    <Content Include="fonts\font-awesome\css\fa-brands.min.css" />
    <Content Include="fonts\font-awesome\css\fa-regular.css" />
    <Content Include="fonts\font-awesome\css\fa-regular.min.css" />
    <Content Include="fonts\font-awesome\css\fa-solid.css" />
    <Content Include="fonts\font-awesome\css\fa-solid.min.css" />
    <Content Include="fonts\font-awesome\css\font-awesome.css" />
    <Content Include="fonts\font-awesome\css\font-awesome.min.css" />
    <Content Include="fonts\font-awesome\css\fontawesome-all.css" />
    <Content Include="fonts\font-awesome\css\fontawesome-all.min.css" />
    <Content Include="fonts\font-awesome\css\fontawesome.css" />
    <Content Include="fonts\font-awesome\css\fontawesome.min.css" />
    <Content Include="fonts\font-awesome\fonts\fontawesome-webfont.svg" />
    <Content Include="fonts\font-awesome\webfonts\fa-brands-400.svg" />
    <Content Include="fonts\font-awesome\webfonts\fa-regular-400.svg" />
    <Content Include="fonts\font-awesome\webfonts\fa-solid-900.svg" />
    <Content Include="fonts\font-gestal-3.4\css\animation.css" />
    <Content Include="fonts\font-gestal-3.4\css\gestal-icon-fonts-3-4-codes.css" />
    <Content Include="fonts\font-gestal-3.4\css\gestal-icon-fonts-3-4-embedded.css" />
    <Content Include="fonts\font-gestal-3.4\css\gestal-icon-fonts-3-4-ie7-codes.css" />
    <Content Include="fonts\font-gestal-3.4\css\gestal-icon-fonts-3-4-ie7.css" />
    <Content Include="fonts\font-gestal-3.4\css\gestal-icon-fonts-3-4.css" />
    <Content Include="fonts\font-gestal-3.4\font\gestal-icon-fonts-3-4.svg" />
    <Content Include="fonts\glyphicons-halflings-regular.svg">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Global.asax" />
    <Content Include="fonts\glyphicons-halflings-regular.woff" />
    <Content Include="fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="fonts\glyphicons-halflings-regular.eot" />
    <Content Include="Imagens\Acesso_VersaoAntiga.png" />
    <Content Include="Imagens\Ajuda\Ajuda_012000.png" />
    <Content Include="Imagens\Ajuda\Ajuda_012010.png" />
    <Content Include="Imagens\Ajuda\Ajuda_012020.png" />
    <Content Include="Imagens\Ajuda\Ajuda_012030.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020000.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020010.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020020.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020030.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020040.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020050.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020060.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020100.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020110.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020120.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020130.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020140.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020150.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020160.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020200.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020210.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020220.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020230.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020240.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020250.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020260.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020300.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020310.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020320.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020330.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020340.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020350.png" />
    <Content Include="Imagens\Ajuda\Ajuda_020360.png" />
    <Content Include="Imagens\Ajuda\Ajuda_040000.png" />
    <Content Include="Imagens\Ajuda\Ajuda_050000.png" />
    <Content Include="Imagens\Ajuda\Ajuda_Intro.png" />
    <Content Include="Imagens\Ajuda\Dash_Analogica.png" />
    <Content Include="Imagens\Ajuda\Dash_Consumo.png" />
    <Content Include="Imagens\Ajuda\Dash_Demanda.png" />
    <Content Include="Imagens\Ajuda\Dash_FatorPotencia.png" />
    <Content Include="Imagens\Ajuda\Dash_Fatura.png" />
    <Content Include="Imagens\Ajuda\Dash_MetaConsumo.png" />
    <Content Include="Imagens\Ajuda\Dash_Temperatura.png" />
    <Content Include="Imagens\Ajuda\Dash_Utilidades.png" />
    <Content Include="Imagens\Ajuda\Supervisao_Energia_CPFL.png" />
    <Content Include="Imagens\filamento-da-lampada_318-28486.png" />
    <Content Include="Imagens\fogo.png" />
    <Content Include="Imagens\gas.jpg" />
    <Content Include="Imagens\gauge.png" />
    <Content Include="Imagens\gota.jpg" />
    <Content Include="Imagens\icone_ciclo24.png" />
    <Content Include="Imagens\icone_gas.png" />
    <Content Include="Imagens\icone_gas24.png" />
    <Content Include="Imagens\icone_gauge.png" />
    <Content Include="Imagens\icone_gauge24.png" />
    <Content Include="Imagens\icone_gota.png" />
    <Content Include="Imagens\icone_gota24.png" />
    <Content Include="Imagens\icone_grupo_unidades.jpg" />
    <Content Include="Imagens\icone_grupo_unidades.png" />
    <Content Include="Imagens\icone_grupo_unidades24.png" />
    <Content Include="Imagens\icone_lampada.png" />
    <Content Include="Imagens\icone_lampada24.png" />
    <Content Include="Imagens\icone_soma.png" />
    <Content Include="Imagens\icone_soma24.png" />
    <Content Include="Imagens\icone_unidade.png" />
    <Content Include="Imagens\icone_unidade24.png" />
    <Content Include="Imagens\img_energia.png" />
    <Content Include="Imagens\img_energia_formula.png" />
    <Content Include="Imagens\img_gg_mult.png" />
    <Content Include="Imagens\img_grupo_unidades.png" />
    <Content Include="Imagens\img_unidade.png" />
    <Content Include="Imagens\img_util_agua.png" />
    <Content Include="Imagens\img_util_gas.png" />
    <Content Include="Imagens\lampada.png" />
    <Content Include="Imagens\Logo_SmartWeb.png" />
    <Content Include="Imagens\Sinal\sinal_gsm_0.svg" />
    <Content Include="Imagens\Sinal\sinal_gsm_1.svg" />
    <Content Include="Imagens\Sinal\sinal_gsm_2.svg" />
    <Content Include="Imagens\Sinal\sinal_gsm_3.svg" />
    <Content Include="Imagens\Sinal\sinal_wifi_0.svg" />
    <Content Include="Imagens\Sinal\sinal_wifi_1.svg" />
    <Content Include="Imagens\Sinal\sinal_wifi_2.svg" />
    <Content Include="Imagens\Sinal\sinal_wifi_3.svg" />
    <Content Include="Imagens\soma.jpg" />
    <Content Include="Imagens\suporte.png" />
    <Content Include="Imagens\Tempo\icone_chuva.png" />
    <Content Include="Imagens\Tempo\icone_chuva16.png" />
    <Content Include="Imagens\Tempo\icone_dia_claro.png" />
    <Content Include="Imagens\Tempo\icone_dia_claro16.png" />
    <Content Include="Imagens\Tempo\icone_dia_nublado.png" />
    <Content Include="Imagens\Tempo\icone_dia_nublado16.png" />
    <Content Include="Imagens\Tempo\icone_granizo.png" />
    <Content Include="Imagens\Tempo\icone_granizo16.png" />
    <Content Include="Imagens\Tempo\icone_neblina.png" />
    <Content Include="Imagens\Tempo\icone_neblina16.png" />
    <Content Include="Imagens\Tempo\icone_neve.png" />
    <Content Include="Imagens\Tempo\icone_neve16.png" />
    <Content Include="Imagens\Tempo\icone_noite_clara.png" />
    <Content Include="Imagens\Tempo\icone_noite_clara16.png" />
    <Content Include="Imagens\Tempo\icone_noite_nublada.png" />
    <Content Include="Imagens\Tempo\icone_noite_nublada16.png" />
    <Content Include="Imagens\Tempo\icone_nublado.png" />
    <Content Include="Imagens\Tempo\icone_nublado16.png" />
    <Content Include="Imagens\Tempo\icone_vento.png" />
    <Content Include="Imagens\Tempo\icone_vento16.png" />
    <Content Include="Imagens\unidade.png" />
    <Content Include="Images\email\CabecalhoEmail.png" />
    <Content Include="Images\flags\Brazil.png" />
    <Content Include="Images\flags\Spain.png" />
    <Content Include="Images\flags\United-States.png" />
    <Content Include="Images\login\dashboard.png" />
    <Content Include="Images\login\LogoIcoSmartEnergy.png" />
    <Content Include="Images\login\LogoManutencao.png" />
    <Content Include="Images\login\LogoSmartEnergy.png" />
    <Content Include="Images\login\LogoSmartEnergy43.png" />
    <Content Include="Images\login\LogoSmartEnergy85_transp.png" />
    <Content Include="Images\login\pattern-wallpaper-74.jpg" />
    <Content Include="Images\login\relatorio.png" />
    <Content Include="Images\login\tela_perspective.png" />
    <Content Include="Images\login\Tela_perspectiveP.png" />
    <Content Include="PDF\Templates\itextkey1569852787480_0.xml" />
    <Content Include="PoliticaPrivacidade\Policita_Privacidade - Copy.htm" />
    <Content Include="Rotativa\help-wkhtmltoimage.txt" />
    <Content Include="Rotativa\help-wkhtmltopdf.txt" />
    <Content Include="Rotativa\wkhtmltoimage.exe" />
    <Content Include="Rotativa\wkhtmltopdf.exe" />
    <Content Include="Scripts\app\inspinia.js" />
    <Content Include="Scripts\bootstrap-datepicker-globalize.js" />
    <Content Include="Scripts\bootstrap-datepicker.js" />
    <Content Include="Scripts\bootstrap-datepicker.min.js" />
    <Content Include="Scripts\bootstrap-datetimepicker.js" />
    <Content Include="Scripts\bootstrap-datetimepicker.min.js" />
    <Content Include="Scripts\bootstrap-selectpicker.js" />
    <Content Include="Scripts\bootstrap-selectpicker.min.js" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <Content Include="Content\bootstrap.css.map" />
    <Content Include="fonts\font-awesome\fonts\fontawesome-webfont.eot" />
    <Content Include="fonts\font-awesome\fonts\fontawesome-webfont.ttf" />
    <Content Include="fonts\font-awesome\fonts\fontawesome-webfont.woff" />
    <Content Include="fonts\font-awesome\fonts\fontawesome-webfont.woff2" />
    <Content Include="fonts\font-awesome\fonts\FontAwesome.otf" />
    <Content Include="fonts\font-awesome\less\animated.less" />
    <Content Include="fonts\font-awesome\less\bordered-pulled.less" />
    <Content Include="fonts\font-awesome\less\core.less" />
    <Content Include="fonts\font-awesome\less\fixed-width.less" />
    <Content Include="fonts\font-awesome\less\font-awesome.less" />
    <Content Include="fonts\font-awesome\less\icons.less" />
    <Content Include="fonts\font-awesome\less\larger.less" />
    <Content Include="fonts\font-awesome\less\list.less" />
    <Content Include="fonts\font-awesome\less\mixins.less" />
    <Content Include="fonts\font-awesome\less\path.less" />
    <Content Include="fonts\font-awesome\less\rotated-flipped.less" />
    <Content Include="fonts\font-awesome\less\stacked.less" />
    <Content Include="fonts\font-awesome\less\variables.less" />
    <Content Include="fonts\font-awesome\scss\font-awesome.scss" />
    <Content Include="fonts\font-awesome\scss\_animated.scss" />
    <Content Include="fonts\font-awesome\scss\_bordered-pulled.scss" />
    <Content Include="fonts\font-awesome\scss\_core.scss" />
    <Content Include="fonts\font-awesome\scss\_fixed-width.scss" />
    <Content Include="fonts\font-awesome\scss\_icons.scss" />
    <Content Include="fonts\font-awesome\scss\_larger.scss" />
    <Content Include="fonts\font-awesome\scss\_list.scss" />
    <Content Include="fonts\font-awesome\scss\_mixins.scss" />
    <Content Include="fonts\font-awesome\scss\_path.scss" />
    <Content Include="fonts\font-awesome\scss\_rotated-flipped.scss" />
    <Content Include="fonts\font-awesome\scss\_stacked.scss" />
    <Content Include="fonts\font-awesome\scss\_variables.scss" />
    <Content Include="Content\bootstrap.min.css.map" />
    <Content Include="fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="Content\plugins\bootstrap-rtl\bootstrap-rtl.css.map" />
    <Content Include="Content\plugins\footable\fonts\footable.eot" />
    <Content Include="Content\plugins\footable\fonts\footable.ttf" />
    <Content Include="Content\plugins\footable\fonts\footable.woff" />
    <Content Include="Content\plugins\slick\fonts\slick.eot" />
    <Content Include="Content\plugins\slick\fonts\slick.ttf" />
    <Content Include="Content\plugins\slick\fonts\slick.woff" />
    <Content Include="fonts\elusive-icons-2.0.0\fonts\elusiveicons-webfont.eot" />
    <Content Include="fonts\elusive-icons-2.0.0\fonts\elusiveicons-webfont.ttf" />
    <Content Include="fonts\elusive-icons-2.0.0\fonts\elusiveicons-webfont.woff" />
    <Content Include="fonts\elusive-icons-2.0.0\less\animated.less" />
    <Content Include="fonts\elusive-icons-2.0.0\less\bordered-pulled.less" />
    <Content Include="fonts\elusive-icons-2.0.0\less\core.less" />
    <Content Include="fonts\elusive-icons-2.0.0\less\elusive-icons.less" />
    <Content Include="fonts\elusive-icons-2.0.0\less\fixed-width.less" />
    <Content Include="fonts\elusive-icons-2.0.0\less\icons.less" />
    <Content Include="fonts\elusive-icons-2.0.0\less\larger.less" />
    <Content Include="fonts\elusive-icons-2.0.0\less\list.less" />
    <Content Include="fonts\elusive-icons-2.0.0\less\mixins.less" />
    <Content Include="fonts\elusive-icons-2.0.0\less\path.less" />
    <Content Include="fonts\elusive-icons-2.0.0\less\rotated-flipped.less" />
    <Content Include="fonts\elusive-icons-2.0.0\less\stacked.less" />
    <Content Include="fonts\elusive-icons-2.0.0\less\variables.less" />
    <Content Include="fonts\elusive-icons-2.0.0\scss\elusive-icons.scss" />
    <Content Include="fonts\elusive-icons-2.0.0\scss\_animated.scss" />
    <Content Include="fonts\elusive-icons-2.0.0\scss\_bordered-pulled.scss" />
    <Content Include="fonts\elusive-icons-2.0.0\scss\_core.scss" />
    <Content Include="fonts\elusive-icons-2.0.0\scss\_fixed-width.scss" />
    <Content Include="fonts\elusive-icons-2.0.0\scss\_icons.scss" />
    <Content Include="fonts\elusive-icons-2.0.0\scss\_larger.scss" />
    <Content Include="fonts\elusive-icons-2.0.0\scss\_list.scss" />
    <Content Include="fonts\elusive-icons-2.0.0\scss\_mixins.scss" />
    <Content Include="fonts\elusive-icons-2.0.0\scss\_path.scss" />
    <Content Include="fonts\elusive-icons-2.0.0\scss\_rotated-flipped.scss" />
    <Content Include="fonts\elusive-icons-2.0.0\scss\_stacked.scss" />
    <Content Include="fonts\elusive-icons-2.0.0\scss\_variables.scss" />
    <Content Include="Imagens\lampada.psd" />
    <Content Include="Imagens\soma.psd" />
    <Content Include="Imagens\unidade.psd" />
    <Content Include="Content\bootstrap-datepicker3.standalone.min.css.map" />
    <Content Include="Content\bootstrap-datepicker3.standalone.css.map" />
    <Content Include="Content\bootstrap-datepicker3.min.css.map" />
    <Content Include="Content\bootstrap-datepicker3.css.map" />
    <Content Include="Content\bootstrap-datepicker.standalone.min.css.map" />
    <Content Include="Content\bootstrap-datepicker.standalone.css.map" />
    <Content Include="Content\bootstrap-datepicker.min.css.map" />
    <Content Include="Content\bootstrap-datepicker.css.map" />
    <Content Include="Content\fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="Content\fonts\glyphicons-halflings-regular.woff" />
    <Content Include="Content\fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="Content\fonts\glyphicons-halflings-regular.eot" />
    <Content Include="Content\bootstrap\wells.less" />
    <Content Include="Content\bootstrap\variables.less" />
    <Content Include="Content\bootstrap\utilities.less" />
    <Content Include="Content\bootstrap\type.less" />
    <Content Include="Content\bootstrap\tooltip.less" />
    <Content Include="Content\bootstrap\thumbnails.less" />
    <Content Include="Content\bootstrap\theme.less" />
    <Content Include="Content\bootstrap\tables.less" />
    <Content Include="Content\bootstrap\scaffolding.less" />
    <Content Include="Content\bootstrap\responsive-utilities.less" />
    <Content Include="Content\bootstrap\responsive-embed.less" />
    <Content Include="Content\bootstrap\progress-bars.less" />
    <Content Include="Content\bootstrap\print.less" />
    <Content Include="Content\bootstrap\popovers.less" />
    <Content Include="Content\bootstrap\panels.less" />
    <Content Include="Content\bootstrap\pagination.less" />
    <Content Include="Content\bootstrap\pager.less" />
    <Content Include="Content\bootstrap\normalize.less" />
    <Content Include="Content\bootstrap\navs.less" />
    <Content Include="Content\bootstrap\navbar.less" />
    <Content Include="Content\bootstrap\modals.less" />
    <Content Include="Content\bootstrap\mixins\vendor-prefixes.less" />
    <Content Include="Content\bootstrap\mixins\text-overflow.less" />
    <Content Include="Content\bootstrap\mixins\text-emphasis.less" />
    <Content Include="Content\bootstrap\mixins\table-row.less" />
    <Content Include="Content\bootstrap\mixins\tab-focus.less" />
    <Content Include="Content\bootstrap\mixins\size.less" />
    <Content Include="Content\bootstrap\mixins\responsive-visibility.less" />
    <Content Include="Content\bootstrap\mixins\resize.less" />
    <Content Include="Content\bootstrap\mixins\reset-text.less" />
    <Content Include="Content\bootstrap\mixins\reset-filter.less" />
    <Content Include="Content\bootstrap\mixins\progress-bar.less" />
    <Content Include="Content\bootstrap\mixins\panels.less" />
    <Content Include="Content\bootstrap\mixins\pagination.less" />
    <Content Include="Content\bootstrap\mixins\opacity.less" />
    <Content Include="Content\bootstrap\mixins\nav-vertical-align.less" />
    <Content Include="Content\bootstrap\mixins\nav-divider.less" />
    <Content Include="Content\bootstrap\mixins\list-group.less" />
    <Content Include="Content\bootstrap\mixins\labels.less" />
    <Content Include="Content\bootstrap\mixins\image.less" />
    <Content Include="Content\bootstrap\mixins\hide-text.less" />
    <Content Include="Content\bootstrap\mixins\grid-framework.less" />
    <Content Include="Content\bootstrap\mixins\grid.less" />
    <Content Include="Content\bootstrap\mixins\gradients.less" />
    <Content Include="Content\bootstrap\mixins\forms.less" />
    <Content Include="Content\bootstrap\mixins\clearfix.less" />
    <Content Include="Content\bootstrap\mixins\center-block.less" />
    <Content Include="Content\bootstrap\mixins\buttons.less" />
    <Content Include="Content\bootstrap\mixins\border-radius.less" />
    <Content Include="Content\bootstrap\mixins\background-variant.less" />
    <Content Include="Content\bootstrap\mixins\alerts.less" />
    <Content Include="Content\bootstrap\mixins.less" />
    <Content Include="Content\bootstrap\media.less" />
    <Content Include="Content\bootstrap\list-group.less" />
    <Content Include="Content\bootstrap\labels.less" />
    <Content Include="Content\bootstrap\jumbotron.less" />
    <Content Include="Content\bootstrap\input-groups.less" />
    <Content Include="Content\bootstrap\grid.less" />
    <Content Include="Content\bootstrap\glyphicons.less" />
    <Content Include="Content\bootstrap\forms.less" />
    <Content Include="Content\bootstrap\dropdowns.less" />
    <Content Include="Content\bootstrap\component-animations.less" />
    <Content Include="Content\bootstrap\code.less" />
    <Content Include="Content\bootstrap\close.less" />
    <Content Include="Content\bootstrap\carousel.less" />
    <Content Include="Content\bootstrap\buttons.less" />
    <Content Include="Content\bootstrap\button-groups.less" />
    <Content Include="Content\bootstrap\breadcrumbs.less" />
    <Content Include="Content\bootstrap\bootstrap.less" />
    <Content Include="Content\bootstrap\badges.less" />
    <Content Include="Content\bootstrap\alerts.less" />
    <Content Include="Content\bootstrap-datetimepicker-build.less" />
    <Content Include="Content\_bootstrap-datetimepicker.less" />
    <Content Include="Content\bootstrap-theme.css.map" />
    <Content Include="Content\emailTemplates\WelcomeEmail.cshtml" />
    <Content Include="Content\emailTemplates\RelatEmail.cshtml" />
    <Content Include="Content\emailTemplates\EsqueciSenhaEmail.cshtml" />
    <Content Include="Content\emailTemplates\SolicitarDemoEmail.cshtml" />
    <Content Include="Content\emailTemplates\FaturaEmail.cshtml" />
    <Content Include="Content\emailTemplates\RankingEmail.cshtml" />
    <Content Include="Content\emailTemplates\NovoSmartEnergy.cshtml" />
    <Content Include="Content\emailTemplates\ErroLoginEmail_Original.cshtml" />
    <Content Include="Content\emailTemplates\DemContratoIdealEmail.cshtml" />
    <Content Include="Content\emailTemplates\FaturaResumidaEmail.cshtml" />
    <Content Include="Content\emailTemplates\CorrecaoFatPotEmail.cshtml" />
    <Content Include="Content\emailTemplates\ConsolidadoEmail.cshtml" />
    <Content Include="Content\emailTemplates\EventosUsuarioEmail.cshtml" />
    <Content Include="Content\emailTemplates\RateioEmail.cshtml" />
    <Content Include="Content\emailTemplates\SenhaExpirouEmail.cshtml" />
    <Content Include="Scripts\declaracoes\Validates.js" />
    <Content Include="Scripts\plugins\AlertaBox\AlertaBox.js" />
    <Content Include="Views\Suporte\GatewayInfo.cshtml" />
    <Content Include="Views\Suporte\GatewaysInfo.cshtml" />
    <Content Include="Imagens\Metas_Exemplo.xls" />
    <Content Include="Content\emailTemplates\CustosUltrapassagemEmail.cshtml" />
    <Content Include="Views\Suporte\AnaliseGateways.cshtml" />
    <Content Include="Views\Suporte\AnaliseGatewaysHist.cshtml" />
    <Content Include="PDF\Templates\modeloPDF.pdf" />
    <Content Include="PDF\Templates\Tmpl - Eletropaulo_1.pdf" />
    <Content Include="Views\Suporte\_ObservacoesGateway.cshtml" />
    <Content Include="Views\Suporte\_ObservacaoGateway.cshtml" />
    <Content Include="Views\Suporte\_BuscaGatewayRecebidos_Resultado.cshtml" />
    <Content Include="Views\Suporte\BuscaGatewayRecebidos.cshtml" />
    <Content Include="Views\Suporte\BuscaGatewayProblemas.cshtml" />
    <Content Include="Views\Suporte\_BuscaGatewayProblemas_Resultado.cshtml" />
    <Content Include="fonts\font-awesome\webfonts\fa-brands-400.eot" />
    <Content Include="fonts\font-awesome\webfonts\fa-brands-400.ttf" />
    <Content Include="fonts\font-awesome\webfonts\fa-brands-400.woff" />
    <Content Include="fonts\font-awesome\webfonts\fa-brands-400.woff2" />
    <Content Include="fonts\font-awesome\webfonts\fa-regular-400.eot" />
    <Content Include="fonts\font-awesome\webfonts\fa-regular-400.ttf" />
    <Content Include="fonts\font-awesome\webfonts\fa-regular-400.woff" />
    <Content Include="fonts\font-awesome\webfonts\fa-regular-400.woff2" />
    <Content Include="fonts\font-awesome\webfonts\fa-solid-900.eot" />
    <Content Include="fonts\font-awesome\webfonts\fa-solid-900.ttf" />
    <Content Include="fonts\font-awesome\webfonts\fa-solid-900.woff" />
    <Content Include="fonts\font-awesome\webfonts\fa-solid-900.woff2" />
    <Content Include="Content\plugins\FancyTree\skin-awesome\ui.fancytree.less" />
    <Content Include="Content\plugins\FancyTree\skin-bootstrap-n\ui.fancytree.less" />
    <Content Include="Content\plugins\FancyTree\skin-bootstrap\ui.fancytree.less" />
    <Content Include="Content\plugins\FancyTree\skin-custom-1\README.md" />
    <Content Include="Content\plugins\FancyTree\skin-lion\ui.fancytree.less" />
    <Content Include="Content\plugins\FancyTree\skin-material\ui.fancytree.less" />
    <Content Include="Content\plugins\FancyTree\skin-themeroller\ui.fancytree.less" />
    <Content Include="Content\plugins\FancyTree\skin-vista\ui.fancytree.less" />
    <Content Include="Content\plugins\FancyTree\skin-win7\ui.fancytree.less" />
    <Content Include="Content\plugins\FancyTree\skin-win8-n\ui.fancytree.less" />
    <Content Include="Content\plugins\FancyTree\skin-win8-xxl\ui.fancytree.less" />
    <Content Include="Content\plugins\FancyTree\skin-win8\ui.fancytree.less" />
    <Content Include="Content\plugins\FancyTree\skin-xp\ui.fancytree.less" />
    <Content Include="Content\emailTemplates\WelcomeEmail_CPFL.cshtml" />
    <Content Include="Content\emailTemplates\EnviaRelatoriosEmail.cshtml" />
    <Content Include="App_Browsers\qt.browser" />
    <Content Include="Content\emailTemplates\EnviaRelatoriosEmail_CPFL.cshtml" />
    <Content Include="Content\emailTemplates\EnviaRelatoriosEstatisticaEmail.cshtml" />
    <Content Include="Content\emailTemplates\ContratoEnergiaEncerramentoEmail_CPFL.cshtml" />
    <Content Include="Content\emailTemplates\ContratoEnergiaEncerramentoGestorEmail_CPFL.cshtml" />
    <Content Include="Content\emailTemplates\ContratoEnergiaReajustePrecoGestorEmail_CPFL.cshtml" />
    <Content Include="Content\emailTemplates\ContratoGestaoEncerramentoEmail_CPFL.cshtml" />
    <Content Include="Content\emailTemplates\ContratoGestaoEncerramentoGestorEmail_CPFL.cshtml" />
    <Content Include="Content\emailTemplates\ContratoGestaoReajustePrecoGestorEmail_CPFL.cshtml" />
    <Content Include="Content\emailTemplates\ContratoEnergiaInicioGestorEmail_CPFL.cshtml" />
    <Content Include="Content\emailTemplates\ContratoEnergiaFinalizadoGestorEmail_CPFL.cshtml" />
    <Content Include="Content\emailTemplates\ProvisionamentoSemanal_Tipo1_Tipo2_Email_CPFL.cshtml" />
    <Content Include="Content\emailTemplates\ProvisionamentoSemanal_Tipo3_Email_CPFL.cshtml" />
    <Content Include="Content\emailTemplates\AnaliseHorarioConsEmail.cshtml" />
    <Content Include="Content\emailTemplates\ComparativoFaturaEmail.cshtml" />
    <Content Include="Content\emailTemplates\GateX_CtrlHorarioEmail.cshtml" />
    <Content Include="Content\emailTemplates\GateX_SaidasDigitaisEmail.cshtml" />
    <Content Include="Content\emailTemplates\AnaliseRamoAtividadeMesEmail.cshtml" />
    <Content Include="Content\emailTemplates\AnaliseRamoAtividadeEmail.cshtml" />
    <Content Include="Content\emailTemplates\ConfirmacaoEmail.cshtml" />
    <Content Include="Content\emailTemplates\ConfirmacaoEmail_CPFL.cshtml" />
    <Content Include="Content\emailTemplates\DistribuicaoConsumoEmail.cshtml" />
    <Content Include="Content\emailTemplates\OportunidadesDemandaEmail.cshtml" />
    <Content Include="Views\Suporte\GestorAnaliseGateways.cshtml" />
    <Content Include="Content\emailTemplates\PainelJBSDemandaEmail.cshtml" />
    <Content Include="Content\emailTemplates\PainelJBSMultasEmail.cshtml" />
    <Content Include="Content\emailTemplates\GateX_ControleAnalogicoEmail.cshtml" />
    <Content Include="Content\emailTemplates\ModeloImpressaoEmail.cshtml" />
    <Content Include="Content\emailTemplates\EnviaRateiosEmail.cshtml" />
    <Content Include="Content\emailTemplates\GateX_ControleFatPotEmail.cshtml" />
    <Content Include="Content\emailTemplates\GateX_ControleDemProjEmail.cshtml" />
    <Content Include="Content\emailTemplates\GateX_ControleDemMedEmail.cshtml" />
    <Content Include="Content\emailTemplates\GateX_ControleDemAcumEmail.cshtml" />
    <Content Include="Content\emailTemplates\EnviaAlertasEmail.cshtml" />
    <Content Include="Views\Suporte\GatewaysBateriaFraca.cshtml" />
    <Content Include="Views\Suporte\GestorGatewaysBateriaFraca.cshtml" />
    <Content Include="Content\emailTemplates\LoginBloqueadoEmail.cshtml" />
    <Content Include="Content\emailTemplates\GateX_EntradasDigitaisEmail.cshtml" />
    <Content Include="Content\emailTemplates\GateX_LogicasEmail.cshtml" />
    <Content Include="Content\emailTemplates\GateX_MedEnerEmail.cshtml" />
    <Content Include="Content\emailTemplates\GateX_MedUtilEmail.cshtml" />
    <Content Include="Content\emailTemplates\GateX_MedCicloEmail.cshtml" />
    <Content Include="Content\emailTemplates\GateX_MedAnaEmail.cshtml" />
    <Content Include="Content\emailTemplates\GateX_CtrlAlarmeEmail.cshtml" />
    <Content Include="Content\emailTemplates\LoginTentativasEmail.cshtml" />
    <Content Include="fonts\font-gestal-3.4\font\gestal-icon-fonts-3-4.eot" />
    <Content Include="fonts\font-gestal-3.4\font\gestal-icon-fonts-3-4.ttf" />
    <Content Include="fonts\font-gestal-3.4\font\gestal-icon-fonts-3-4.woff" />
    <Content Include="fonts\font-gestal-3.4\font\gestal-icon-fonts-3-4.woff2" />
    <Content Include="Content\emailTemplates\SegundoFatorEmail.cshtml" />
    <Content Include="Content\emailTemplates\WelcomeEmail_Original.cshtml" />
    <Content Include="Content\emailTemplates\ErroLoginEmail.cshtml" />
    <Content Include="Content\emailTemplates\ComparativoConsumoEmail.cshtml" />
    <None Include="Properties\PublishProfiles\HostDemo.pubxml" />
    <None Include="Properties\PublishProfiles\Servidor.pubxml" />
    <None Include="Scripts\jquery-1.9.1.intellisense.js" />
    <Content Include="Scripts\DataTableMedicoes.js" />
    <Content Include="Scripts\declaracoes\Traducao-es-ar.js" />
    <Content Include="Scripts\declaracoes\Traducao-en.js" />
    <Content Include="Scripts\declaracoes\Traducao-pt-br.js" />
    <Content Include="Scripts\declaracoes\ConstantsDeclaracao.js" />
    <Content Include="Scripts\jquery-1.9.1.js" />
    <Content Include="Scripts\jquery-1.9.1.min.js" />
    <Content Include="Scripts\jquery-1.9.1.min.map" />
    <None Include="Scripts\jquery-2.1.1.intellisense.js" />
    <Content Include="Scripts\jquery-2.1.1.js" />
    <Content Include="Scripts\jquery-2.1.1.min.js" />
    <Content Include="Scripts\jquery-ui-1.9.0.js" />
    <Content Include="Scripts\jquery-ui-1.9.0.min.js" />
    <Content Include="Scripts\jQuery.FileUpload\cors\jquery.postmessage-transport.js" />
    <Content Include="Scripts\jQuery.FileUpload\cors\jquery.xdr-transport.js" />
    <Content Include="Scripts\jQuery.FileUpload\jquery.fileupload-angular.js" />
    <Content Include="Scripts\jQuery.FileUpload\jquery.fileupload-audio.js" />
    <Content Include="Scripts\jQuery.FileUpload\jquery.fileupload-image.js" />
    <Content Include="Scripts\jQuery.FileUpload\jquery.fileupload-jquery-ui.js" />
    <Content Include="Scripts\jQuery.FileUpload\jquery.fileupload-process.js" />
    <Content Include="Scripts\jQuery.FileUpload\jquery.fileupload-ui.js" />
    <Content Include="Scripts\jQuery.FileUpload\jquery.fileupload-validate.js" />
    <Content Include="Scripts\jQuery.FileUpload\jquery.fileupload-video.js" />
    <Content Include="Scripts\jQuery.FileUpload\jquery.fileupload.js" />
    <Content Include="Scripts\jQuery.FileUpload\jquery.iframe-transport.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.af-ZA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.af.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.am-ET.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.am.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ar-AE.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ar-BH.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ar-DZ.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ar-EG.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ar-IQ.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ar-JO.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ar-KW.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ar-LB.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ar-LY.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ar-MA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ar-OM.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ar-QA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ar-SA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ar-SY.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ar-TN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ar-YE.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ar.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.arn-CL.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.arn.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.as-IN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.as.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.az-Cyrl-AZ.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.az-Cyrl.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.az-Latn-AZ.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.az-Latn.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.az.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ba-RU.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ba.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.be-BY.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.be.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.bg-BG.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.bg.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.bn-BD.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.bn-IN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.bn.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.bo-CN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.bo.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.br-FR.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.br.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.bs-Cyrl-BA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.bs-Cyrl.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.bs-Latn-BA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.bs-Latn.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.bs.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ca-ES.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ca.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.co-FR.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.co.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.cs-CZ.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.cs.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.cy-GB.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.cy.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.da-DK.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.da.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.de-AT.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.de-CH.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.de-DE.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.de-LI.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.de-LU.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.de.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.dsb-DE.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.dsb.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.dv-MV.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.dv.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.el-GR.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.el.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.en-029.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.en-AU.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.en-BZ.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.en-CA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.en-GB.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.en-IE.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.en-IN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.en-JM.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.en-MY.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.en-NZ.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.en-PH.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.en-SG.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.en-TT.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.en-US.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.en-ZA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.en-ZW.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.es-AR.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.es-BO.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.es-CL.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.es-CO.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.es-CR.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.es-DO.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.es-EC.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.es-ES.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.es-GT.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.es-HN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.es-MX.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.es-NI.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.es-PA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.es-PE.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.es-PR.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.es-PY.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.es-SV.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.es-US.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.es-UY.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.es-VE.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.es.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.et-EE.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.et.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.eu-ES.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.eu.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.fa-IR.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.fa.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.fi-FI.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.fi.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.fil-PH.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.fil.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.fo-FO.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.fo.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.fr-BE.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.fr-CA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.fr-CH.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.fr-FR.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.fr-LU.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.fr-MC.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.fr.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.fy-NL.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.fy.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ga-IE.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ga.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.gd-GB.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.gd.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.gl-ES.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.gl.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.gsw-FR.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.gsw.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.gu-IN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.gu.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ha-Latn-NG.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ha-Latn.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ha.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.he-IL.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.he.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.hi-IN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.hi.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.hr-BA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.hr-HR.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.hr.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.hsb-DE.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.hsb.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.hu-HU.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.hu.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.hy-AM.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.hy.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.id-ID.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.id.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ig-NG.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ig.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ii-CN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ii.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.is-IS.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.is.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.it-CH.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.it-IT.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.it.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.iu-Cans-CA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.iu-Cans.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.iu-Latn-CA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.iu-Latn.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.iu.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ja-JP.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ja.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ka-GE.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ka.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.kk-KZ.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.kk.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.kl-GL.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.kl.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.km-KH.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.km.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.kn-IN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.kn.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ko-KR.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ko.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.kok-IN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.kok.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ky-KG.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ky.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.lb-LU.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.lb.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.lo-LA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.lo.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.lt-LT.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.lt.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.lv-LV.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.lv.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.mi-NZ.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.mi.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.mk-MK.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.mk.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ml-IN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ml.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.mn-Cyrl.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.mn-MN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.mn-Mong-CN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.mn-Mong.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.mn.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.moh-CA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.moh.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.mr-IN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.mr.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ms-BN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ms-MY.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ms.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.mt-MT.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.mt.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.nb-NO.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.nb.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ne-NP.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ne.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.nl-BE.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.nl-NL.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.nl.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.nn-NO.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.nn.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.no.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.nso-ZA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.nso.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.oc-FR.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.oc.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.or-IN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.or.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.pa-IN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.pa.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.pl-PL.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.pl.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.prs-AF.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.prs.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ps-AF.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ps.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.pt-BR.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.pt-PT.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.pt.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.qut-GT.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.qut.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.quz-BO.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.quz-EC.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.quz-PE.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.quz.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.rm-CH.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.rm.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ro-RO.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ro.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ru-RU.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ru.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.rw-RW.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.rw.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sa-IN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sa.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sah-RU.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sah.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.se-FI.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.se-NO.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.se-SE.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.se.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.si-LK.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.si.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sk-SK.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sk.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sl-SI.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sl.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sma-NO.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sma-SE.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sma.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.smj-NO.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.smj-SE.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.smj.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.smn-FI.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.smn.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sms-FI.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sms.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sq-AL.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sq.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sr-Cyrl-BA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sr-Cyrl-CS.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sr-Cyrl-ME.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sr-Cyrl-RS.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sr-Cyrl.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sr-Latn-BA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sr-Latn-CS.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sr-Latn-ME.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sr-Latn-RS.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sr-Latn.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sr.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sv-FI.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sv-SE.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sv.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sw-KE.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.sw.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.syr-SY.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.syr.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ta-IN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ta.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.te-IN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.te.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.tg-Cyrl-TJ.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.tg-Cyrl.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.tg.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.th-TH.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.th.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.tk-TM.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.tk.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.tn-ZA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.tn.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.tr-TR.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.tr.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.tt-RU.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.tt.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.tzm-Latn-DZ.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.tzm-Latn.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.tzm.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ug-CN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ug.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.uk-UA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.uk.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ur-PK.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.ur.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.uz-Cyrl-UZ.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.uz-Cyrl.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.uz-Latn-UZ.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.uz-Latn.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.uz.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.vi-VN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.vi.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.wo-SN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.wo.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.xh-ZA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.xh.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.yo-NG.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.yo.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.zh-CHS.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.zh-CHT.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.zh-CN.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.zh-Hans.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.zh-Hant.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.zh-HK.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.zh-MO.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.zh-SG.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.zh-TW.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.zh.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.zu-ZA.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.culture.zu.js" />
    <Content Include="Scripts\jquery.globalize\cultures\globalize.cultures.js" />
    <Content Include="Scripts\jquery.globalize\globalize.js" />
    <Content Include="Scripts\jquery.validate.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.ar.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.az.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.bg.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.bs.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.ca.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.cs.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.cy.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.da.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.de.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.el.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.en-AU.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.en-GB.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.eo.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.es.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.et.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.eu.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.fa.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.fi.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.fo.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.fr-CH.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.fr.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.gl.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.he.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.hr.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.hu.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.hy.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.id.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.is.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.it-CH.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.it.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.ja.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.ka.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.kh.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.kk.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.ko.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.kr.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.lt.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.lv.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.me.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.mk.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.mn.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.ms.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.nb.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.nl-BE.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.nl.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.no.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.pl.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.pt-BR.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.pt.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.ro.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.rs-latin.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.rs.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.ru.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.sk.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.sl.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.sq.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.sr-latin.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.sr.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.sv.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.sw.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.th.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.tr.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.uk.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.vi.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.zh-CN.min.js" />
    <Content Include="Scripts\locales\bootstrap-datepicker.zh-TW.min.js" />
    <Content Include="Scripts\modernizr-2.6.2.js" />
    <Content Include="Scripts\moment-with-locales.js" />
    <Content Include="Scripts\moment-with-locales.min.js" />
    <Content Include="Scripts\moment.js" />
    <Content Include="Scripts\moment.min.js" />
    <Content Include="Scripts\npm.js" />
    <Content Include="Scripts\plugins\blueimp\jquery.blueimp-gallery.min.js" />
    <Content Include="Scripts\plugins\bootstrap-markdown\bootstrap-markdown.js" />
    <Content Include="Scripts\plugins\bootstrap-markdown\markdown.js" />
    <Content Include="Scripts\plugins\bootstrapTour\bootstrap-tour.min.js" />
    <Content Include="Scripts\plugins\c3\c3.min.js" />
    <Content Include="Scripts\plugins\chartist\chartist.min.js" />
    <Content Include="Scripts\plugins\chartJs\Chart.min.js" />
    <Content Include="Scripts\plugins\chosen\chosen.jquery.js" />
    <Content Include="Scripts\plugins\clipboard\clipboard.min.js" />
    <Content Include="Scripts\plugins\clockpicker\clockpicker.js" />
    <Content Include="Scripts\plugins\codemirror\codemirror.js" />
    <Content Include="Scripts\plugins\codemirror\mode\apl\apl.js" />
    <Content Include="Scripts\plugins\codemirror\mode\apl\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\asterisk\asterisk.js" />
    <Content Include="Scripts\plugins\codemirror\mode\asterisk\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\clike\clike.js" />
    <Content Include="Scripts\plugins\codemirror\mode\clike\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\clike\scala.html" />
    <Content Include="Scripts\plugins\codemirror\mode\clojure\clojure.js" />
    <Content Include="Scripts\plugins\codemirror\mode\clojure\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\cobol\cobol.js" />
    <Content Include="Scripts\plugins\codemirror\mode\cobol\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\coffeescript\coffeescript.js" />
    <Content Include="Scripts\plugins\codemirror\mode\coffeescript\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\commonlisp\commonlisp.js" />
    <Content Include="Scripts\plugins\codemirror\mode\commonlisp\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\css\css.js" />
    <Content Include="Scripts\plugins\codemirror\mode\css\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\css\less.html" />
    <Content Include="Scripts\plugins\codemirror\mode\css\less_test.js" />
    <Content Include="Scripts\plugins\codemirror\mode\css\scss.html" />
    <Content Include="Scripts\plugins\codemirror\mode\css\scss_test.js" />
    <Content Include="Scripts\plugins\codemirror\mode\css\test.js" />
    <Content Include="Scripts\plugins\codemirror\mode\cypher\cypher.js" />
    <Content Include="Scripts\plugins\codemirror\mode\cypher\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\diff\diff.js" />
    <Content Include="Scripts\plugins\codemirror\mode\diff\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\django\django.js" />
    <Content Include="Scripts\plugins\codemirror\mode\django\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\dtd\dtd.js" />
    <Content Include="Scripts\plugins\codemirror\mode\dtd\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\dylan\dylan.js" />
    <Content Include="Scripts\plugins\codemirror\mode\dylan\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\d\d.js" />
    <Content Include="Scripts\plugins\codemirror\mode\d\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\ecl\ecl.js" />
    <Content Include="Scripts\plugins\codemirror\mode\ecl\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\eiffel\eiffel.js" />
    <Content Include="Scripts\plugins\codemirror\mode\eiffel\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\erlang\erlang.js" />
    <Content Include="Scripts\plugins\codemirror\mode\erlang\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\fortran\fortran.js" />
    <Content Include="Scripts\plugins\codemirror\mode\fortran\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\gas\gas.js" />
    <Content Include="Scripts\plugins\codemirror\mode\gas\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\gfm\gfm.js" />
    <Content Include="Scripts\plugins\codemirror\mode\gfm\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\gfm\test.js" />
    <Content Include="Scripts\plugins\codemirror\mode\gherkin\gherkin.js" />
    <Content Include="Scripts\plugins\codemirror\mode\gherkin\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\go\go.js" />
    <Content Include="Scripts\plugins\codemirror\mode\go\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\groovy\groovy.js" />
    <Content Include="Scripts\plugins\codemirror\mode\groovy\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\haml\haml.js" />
    <Content Include="Scripts\plugins\codemirror\mode\haml\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\haml\test.js" />
    <Content Include="Scripts\plugins\codemirror\mode\haskell\haskell.js" />
    <Content Include="Scripts\plugins\codemirror\mode\haskell\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\haxe\haxe.js" />
    <Content Include="Scripts\plugins\codemirror\mode\haxe\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\htmlembedded\htmlembedded.js" />
    <Content Include="Scripts\plugins\codemirror\mode\htmlembedded\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\htmlmixed\htmlmixed.js" />
    <Content Include="Scripts\plugins\codemirror\mode\htmlmixed\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\http\http.js" />
    <Content Include="Scripts\plugins\codemirror\mode\http\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\jade\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\jade\jade.js" />
    <Content Include="Scripts\plugins\codemirror\mode\javascript\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\javascript\javascript.js" />
    <Content Include="Scripts\plugins\codemirror\mode\javascript\json-ld.html" />
    <Content Include="Scripts\plugins\codemirror\mode\javascript\test.js" />
    <Content Include="Scripts\plugins\codemirror\mode\javascript\typescript.html" />
    <Content Include="Scripts\plugins\codemirror\mode\jinja2\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\jinja2\jinja2.js" />
    <Content Include="Scripts\plugins\codemirror\mode\julia\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\julia\julia.js" />
    <Content Include="Scripts\plugins\codemirror\mode\kotlin\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\kotlin\kotlin.js" />
    <Content Include="Scripts\plugins\codemirror\mode\livescript\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\livescript\livescript.js" />
    <Content Include="Scripts\plugins\codemirror\mode\lua\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\lua\lua.js" />
    <Content Include="Scripts\plugins\codemirror\mode\markdown\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\markdown\markdown.js" />
    <Content Include="Scripts\plugins\codemirror\mode\markdown\test.js" />
    <Content Include="Scripts\plugins\codemirror\mode\meta.js" />
    <Content Include="Scripts\plugins\codemirror\mode\mirc\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\mirc\mirc.js" />
    <Content Include="Scripts\plugins\codemirror\mode\mllike\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\mllike\mllike.js" />
    <Content Include="Scripts\plugins\codemirror\mode\modelica\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\modelica\modelica.js" />
    <Content Include="Scripts\plugins\codemirror\mode\nginx\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\nginx\nginx.js" />
    <Content Include="Scripts\plugins\codemirror\mode\ntriples\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\ntriples\ntriples.js" />
    <Content Include="Scripts\plugins\codemirror\mode\octave\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\octave\octave.js" />
    <Content Include="Scripts\plugins\codemirror\mode\pascal\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\pascal\pascal.js" />
    <Content Include="Scripts\plugins\codemirror\mode\pegjs\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\pegjs\pegjs.js" />
    <Content Include="Scripts\plugins\codemirror\mode\perl\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\perl\perl.js" />
    <Content Include="Scripts\plugins\codemirror\mode\php\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\php\php.js" />
    <Content Include="Scripts\plugins\codemirror\mode\php\test.js" />
    <Content Include="Scripts\plugins\codemirror\mode\pig\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\pig\pig.js" />
    <Content Include="Scripts\plugins\codemirror\mode\properties\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\properties\properties.js" />
    <Content Include="Scripts\plugins\codemirror\mode\puppet\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\puppet\puppet.js" />
    <Content Include="Scripts\plugins\codemirror\mode\python\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\python\python.js" />
    <Content Include="Scripts\plugins\codemirror\mode\q\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\q\q.js" />
    <Content Include="Scripts\plugins\codemirror\mode\rpm\changes\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\rpm\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\rpm\rpm.js" />
    <Content Include="Scripts\plugins\codemirror\mode\rst\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\rst\rst.js" />
    <Content Include="Scripts\plugins\codemirror\mode\ruby\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\ruby\ruby.js" />
    <Content Include="Scripts\plugins\codemirror\mode\ruby\test.js" />
    <Content Include="Scripts\plugins\codemirror\mode\rust\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\rust\rust.js" />
    <Content Include="Scripts\plugins\codemirror\mode\r\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\r\r.js" />
    <Content Include="Scripts\plugins\codemirror\mode\sass\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\sass\sass.js" />
    <Content Include="Scripts\plugins\codemirror\mode\scheme\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\scheme\scheme.js" />
    <Content Include="Scripts\plugins\codemirror\mode\shell\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\shell\shell.js" />
    <Content Include="Scripts\plugins\codemirror\mode\shell\test.js" />
    <Content Include="Scripts\plugins\codemirror\mode\sieve\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\sieve\sieve.js" />
    <Content Include="Scripts\plugins\codemirror\mode\slim\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\slim\slim.js" />
    <Content Include="Scripts\plugins\codemirror\mode\slim\test.js" />
    <Content Include="Scripts\plugins\codemirror\mode\smalltalk\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\smalltalk\smalltalk.js" />
    <Content Include="Scripts\plugins\codemirror\mode\smartymixed\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\smartymixed\smartymixed.js" />
    <Content Include="Scripts\plugins\codemirror\mode\smarty\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\smarty\smarty.js" />
    <Content Include="Scripts\plugins\codemirror\mode\solr\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\solr\solr.js" />
    <Content Include="Scripts\plugins\codemirror\mode\sparql\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\sparql\sparql.js" />
    <Content Include="Scripts\plugins\codemirror\mode\sql\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\sql\sql.js" />
    <Content Include="Scripts\plugins\codemirror\mode\stex\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\stex\stex.js" />
    <Content Include="Scripts\plugins\codemirror\mode\stex\test.js" />
    <Content Include="Scripts\plugins\codemirror\mode\tcl\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\tcl\tcl.js" />
    <Content Include="Scripts\plugins\codemirror\mode\textile\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\textile\test.js" />
    <Content Include="Scripts\plugins\codemirror\mode\textile\textile.js" />
    <Content Include="Scripts\plugins\codemirror\mode\tiddlywiki\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\tiddlywiki\tiddlywiki.css" />
    <Content Include="Scripts\plugins\codemirror\mode\tiddlywiki\tiddlywiki.js" />
    <Content Include="Scripts\plugins\codemirror\mode\tiki\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\tiki\tiki.css" />
    <Content Include="Scripts\plugins\codemirror\mode\tiki\tiki.js" />
    <Content Include="Scripts\plugins\codemirror\mode\toml\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\toml\toml.js" />
    <Content Include="Scripts\plugins\codemirror\mode\tornado\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\tornado\tornado.js" />
    <Content Include="Scripts\plugins\codemirror\mode\turtle\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\turtle\turtle.js" />
    <Content Include="Scripts\plugins\codemirror\mode\vbscript\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\vbscript\vbscript.js" />
    <Content Include="Scripts\plugins\codemirror\mode\vb\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\vb\vb.js" />
    <Content Include="Scripts\plugins\codemirror\mode\velocity\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\velocity\velocity.js" />
    <Content Include="Scripts\plugins\codemirror\mode\verilog\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\verilog\test.js" />
    <Content Include="Scripts\plugins\codemirror\mode\verilog\verilog.js" />
    <Content Include="Scripts\plugins\codemirror\mode\xml\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\xml\test.js" />
    <Content Include="Scripts\plugins\codemirror\mode\xml\xml.js" />
    <Content Include="Scripts\plugins\codemirror\mode\xquery\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\xquery\test.js" />
    <Content Include="Scripts\plugins\codemirror\mode\xquery\xquery.js" />
    <Content Include="Scripts\plugins\codemirror\mode\yaml\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\yaml\yaml.js" />
    <Content Include="Scripts\plugins\codemirror\mode\z80\index.html" />
    <Content Include="Scripts\plugins\codemirror\mode\z80\z80.js" />
    <Content Include="Scripts\plugins\colorpicker\bootstrap-colorpicker.min.js" />
    <Content Include="Scripts\plugins\cropper\cropper.min.js" />
    <Content Include="Scripts\plugins\d3\d3.min.js" />
    <Content Include="Scripts\plugins\datapicker\bootstrap-datepicker.js" />
    <Content Include="Scripts\plugins\dataTables\datatables.min.js" />
    <Content Include="Scripts\plugins\daterangepicker\daterangepicker.js" />
    <Content Include="Scripts\plugins\diff_match_patch\javascript\diff_match_patch.js" />
    <Content Include="Scripts\plugins\diff_match_patch\README.txt" />
    <Content Include="Scripts\plugins\dotdotdot\jquery.dotdotdot.min.js" />
    <Content Include="Scripts\plugins\dropzone\dropzone.js" />
    <Content Include="Scripts\plugins\easypiechart\easypiechart.js" />
    <Content Include="Scripts\plugins\easypiechart\jquery.easypiechart.js" />
    <Content Include="Scripts\plugins\FancyTree\jquery.fancytree-all-deps.js" />
    <Content Include="Scripts\plugins\FancyTree\jquery.fancytree-all-deps.min.js" />
    <Content Include="Scripts\plugins\FancyTree\jquery.fancytree-all.js" />
    <Content Include="Scripts\plugins\FancyTree\jquery.fancytree-all.min.js" />
    <Content Include="Scripts\plugins\FancyTree\jquery.fancytree.min.js" />
    <Content Include="Scripts\plugins\FancyTree\LICENSE.txt" />
    <Content Include="Scripts\plugins\FancyTree\modules\jquery.fancytree.ariagrid.js" />
    <Content Include="Scripts\plugins\FancyTree\modules\jquery.fancytree.childcounter.js" />
    <Content Include="Scripts\plugins\FancyTree\modules\jquery.fancytree.clones.js" />
    <Content Include="Scripts\plugins\FancyTree\modules\jquery.fancytree.columnview.js" />
    <Content Include="Scripts\plugins\FancyTree\modules\jquery.fancytree.dnd.js" />
    <Content Include="Scripts\plugins\FancyTree\modules\jquery.fancytree.dnd5.js" />
    <Content Include="Scripts\plugins\FancyTree\modules\jquery.fancytree.edit.js" />
    <Content Include="Scripts\plugins\FancyTree\modules\jquery.fancytree.filter.js" />
    <Content Include="Scripts\plugins\FancyTree\modules\jquery.fancytree.fixed.js" />
    <Content Include="Scripts\plugins\FancyTree\modules\jquery.fancytree.glyph.js" />
    <Content Include="Scripts\plugins\FancyTree\modules\jquery.fancytree.grid.js" />
    <Content Include="Scripts\plugins\FancyTree\modules\jquery.fancytree.gridnav.js" />
    <Content Include="Scripts\plugins\FancyTree\modules\jquery.fancytree.js" />
    <Content Include="Scripts\plugins\FancyTree\modules\jquery.fancytree.logger.js" />
    <Content Include="Scripts\plugins\FancyTree\modules\jquery.fancytree.menu.js" />
    <Content Include="Scripts\plugins\FancyTree\modules\jquery.fancytree.multi.js" />
    <Content Include="Scripts\plugins\FancyTree\modules\jquery.fancytree.persist.js" />
    <Content Include="Scripts\plugins\FancyTree\modules\jquery.fancytree.table.js" />
    <Content Include="Scripts\plugins\FancyTree\modules\jquery.fancytree.themeroller.js" />
    <Content Include="Scripts\plugins\FancyTree\modules\jquery.fancytree.ui-deps.js" />
    <Content Include="Scripts\plugins\FancyTree\modules\jquery.fancytree.wide.js" />
    <Content Include="Scripts\plugins\flot\curvedLines.js" />
    <Content Include="Scripts\plugins\flot\excanvas.min.js" />
    <Content Include="Scripts\plugins\flot\jquery.flot.js" />
    <Content Include="Scripts\plugins\flot\jquery.flot.pie.js" />
    <Content Include="Scripts\plugins\flot\jquery.flot.resize.js" />
    <Content Include="Scripts\plugins\flot\jquery.flot.spline.js" />
    <Content Include="Scripts\plugins\flot\jquery.flot.stack.js" />
    <Content Include="Scripts\plugins\flot\jquery.flot.symbol.js" />
    <Content Include="Scripts\plugins\flot\jquery.flot.time.js" />
    <Content Include="Scripts\plugins\flot\jquery.flot.tooltip.min.js" />
    <Content Include="Scripts\plugins\footable\footable.all.min.js" />
    <Content Include="Scripts\plugins\fullcalendar\fullcalendar.min.js" />
    <Content Include="Scripts\plugins\fullcalendar\moment.min.js" />
    <Content Include="Scripts\plugins\gritter\images\gritter-light.png" />
    <Content Include="Scripts\plugins\gritter\images\gritter-long.png" />
    <Content Include="Scripts\plugins\gritter\images\gritter.png" />
    <Content Include="Scripts\plugins\gritter\images\ie-spacer.gif" />
    <Content Include="Scripts\plugins\gritter\jquery.gritter.css" />
    <Content Include="Scripts\plugins\gritter\jquery.gritter.min.js" />
    <Content Include="Scripts\plugins\i18next\i18next.min.js" />
    <Content Include="Scripts\plugins\iCheck\icheck.min.js" />
    <Content Include="Scripts\plugins\idle-timer\idle-timer.min.js" />
    <Content Include="Scripts\plugins\ionRangeSlider\ion.rangeSlider.min.js" />
    <Content Include="Scripts\plugins\jasny\jasny-bootstrap.min.js" />
    <Content Include="Scripts\plugins\jeditable\jquery.jeditable.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-ar.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-bg.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-bg1251.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-cat.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-cn.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-cs.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-da.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-de.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-dk.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-el.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-en.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-es.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-fa.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-fi.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-fr.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-gl.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-he.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-hr.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-hr1250.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-hu.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-id.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-is.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-it.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-ja.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-kr.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-lt.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-mne.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-nl.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-no.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-pl.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-pt-br.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-pt.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-ro.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-ru.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-sk.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-sr-latin.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-sr.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-sv.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-th.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-tr.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-tw.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-ua.js" />
    <Content Include="Scripts\plugins\jqGrid\i18n\grid.locale-vi.js" />
    <Content Include="Scripts\plugins\jqGrid\jquery.jqGrid.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery-ui-i18n.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-af.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-ar-DZ.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-ar.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-az.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-be.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-bg.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-bs.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-ca.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-cs.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-cy-GB.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-da.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-de.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-el.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-en-AU.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-en-GB.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-en-NZ.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-eo.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-es.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-et.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-eu.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-fa.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-fi.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-fo.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-fr-CA.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-fr-CH.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-fr.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-gl.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-he.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-hi.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-hr.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-hu.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-hy.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-id.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-is.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-it.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-ja.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-ka.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-kk.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-km.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-ko.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-ky.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-lb.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-lt.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-lv.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-mk.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-ml.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-ms.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-nb.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-nl-BE.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-nl.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-nn.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-no.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-pl.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-pt-BR.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-pt.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-rm.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-ro.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-ru.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-sk.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-sl.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-sq.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-sr-SR.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-sr.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-sv.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-ta.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-th.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-tj.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-tr.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-uk.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-vi.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-zh-CN.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-zh-HK.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\i18n\jquery.ui.datepicker-zh-TW.min.js" />
    <Content Include="Scripts\plugins\jquery-ui\images\animated-overlay.gif" />
    <Content Include="Scripts\plugins\jquery-ui\images\ui-bg_flat_0_aaaaaa_40x100.png" />
    <Content Include="Scripts\plugins\jquery-ui\images\ui-bg_flat_75_ffffff_40x100.png" />
    <Content Include="Scripts\plugins\jquery-ui\images\ui-bg_glass_55_fbf9ee_1x400.png" />
    <Content Include="Scripts\plugins\jquery-ui\images\ui-bg_glass_65_ffffff_1x400.png" />
    <Content Include="Scripts\plugins\jquery-ui\images\ui-bg_glass_75_dadada_1x400.png" />
    <Content Include="Scripts\plugins\jquery-ui\images\ui-bg_glass_75_e6e6e6_1x400.png" />
    <Content Include="Scripts\plugins\jquery-ui\images\ui-bg_glass_95_fef1ec_1x400.png" />
    <Content Include="Scripts\plugins\jquery-ui\images\ui-bg_highlight-soft_75_cccccc_1x100.png" />
    <Content Include="Scripts\plugins\jquery-ui\images\ui-icons_222222_256x240.png" />
    <Content Include="Scripts\plugins\jquery-ui\images\ui-icons_2e83ff_256x240.png" />
    <Content Include="Scripts\plugins\jquery-ui\images\ui-icons_454545_256x240.png" />
    <Content Include="Scripts\plugins\jquery-ui\images\ui-icons_888888_256x240.png" />
    <Content Include="Scripts\plugins\jquery-ui\images\ui-icons_cd0a0a_256x240.png" />
    <Content Include="Scripts\plugins\jquery-ui\jquery-ui.css" />
    <Content Include="Scripts\plugins\jquery-ui\jquery-ui.js" />
    <Content Include="Scripts\plugins\jquery-ui\jquery-ui.min.css" />
    <Content Include="Scripts\plugins\jquery-ui\jquery-ui.min.js" />
    <Content Include="Scripts\plugins\jsizes\jquery.sizes.js" />
    <Content Include="Scripts\plugins\jsKnob\jquery.knob.js" />
    <Content Include="Scripts\plugins\jsTree\jstree.min.js" />
    <Content Include="Scripts\plugins\justified-gallery\jquery.justifiedgallery.css" />
    <Content Include="Scripts\plugins\justified-gallery\jquery.justifiedgallery.js" />
    <Content Include="Scripts\plugins\justified-gallery\jquery.justifiedgallery.min.css" />
    <Content Include="Scripts\plugins\justified-gallery\jquery.justifiedgallery.min.js" />
    <Content Include="Scripts\plugins\justified-gallery\loading.gif" />
    <Content Include="Scripts\plugins\jvectormap\jquery-jvectormap-1.2.2.min.js" />
    <Content Include="Scripts\plugins\jvectormap\jquery-jvectormap-2.0.2.css" />
    <Content Include="Scripts\plugins\jvectormap\jquery-jvectormap-2.0.2.min.js" />
    <Content Include="Scripts\plugins\jvectormap\jquery-jvectormap-world-mill-en.js" />
    <Content Include="Scripts\plugins\ladda\ladda.jquery.min.js" />
    <Content Include="Scripts\plugins\ladda\ladda.min.js" />
    <Content Include="Scripts\plugins\ladda\spin.min.js" />
    <Content Include="Scripts\plugins\maskMoney\jquery.maskMoney.js" />
    <Content Include="Scripts\plugins\maskMoney\jquery.maskMoney.min.js" />
    <Content Include="Scripts\plugins\masonary\masonry.pkgd.min.js" />
    <Content Include="Scripts\plugins\metisMenu\metisMenu.min.js" />
    <Content Include="Scripts\plugins\morris\morris.js" />
    <Content Include="Scripts\plugins\morris\raphael-2.1.0.min.js" />
    <Content Include="Scripts\plugins\nestable\jquery.nestable.js" />
    <Content Include="Scripts\plugins\nouslider\jquery.nouislider.min.js" />
    <Content Include="Scripts\plugins\pace\pace.min.js" />
    <Content Include="Scripts\plugins\peity\jquery.peity.min.js" />
    <Content Include="Scripts\plugins\preetyTextDiff\jquery.pretty-text-diff.min.js" />
    <Content Include="Scripts\plugins\rickshaw\rickshaw.min.js" />
    <Content Include="Scripts\plugins\rickshaw\vendor\d3.v3.js" />
    <Content Include="Scripts\plugins\select2\select2.full.min.js" />
    <Content Include="Scripts\plugins\simplerWeather\example.js" />
    <Content Include="Scripts\plugins\simplerWeather\geolocation-example.js" />
    <Content Include="Scripts\plugins\simplerWeather\jquery.simplerWeather.min.js" />
    <Content Include="Scripts\plugins\simplerWeather\jquery.simplerWeather.js" />
    <Content Include="Scripts\plugins\simplerWeather\jquery.simplerWeather-original.min.js" />
    <Content Include="Scripts\plugins\simpleWeather\gulpfile.js" />
    <Content Include="Scripts\plugins\simpleWeather\index.html" />
    <Content Include="Scripts\plugins\simpleWeather\jquery.simpleWeather.js" />
    <Content Include="Scripts\plugins\simpleWeather\jquery.simpleWeather.min.js" />
    <Content Include="Scripts\plugins\simpleWeather\MIT-LICENSE.txt" />
    <Content Include="Scripts\plugins\skycons\index.html" />
    <Content Include="Scripts\plugins\skycons\skycons.js" />
    <Content Include="Scripts\plugins\slick\slick.min.js" />
    <Content Include="Scripts\plugins\slimscroll\jquery.slimscroll.min.js" />
    <Content Include="Scripts\plugins\sparkline\jquery.sparkline.min.js" />
    <Content Include="Scripts\plugins\staps\jquery.steps.min.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-ar-AR.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-bg-BG.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-ca-ES.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-cs-CZ.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-da-DK.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-de-DE.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-el-GR.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-es-ES.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-es-EU.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-fa-IR.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-fi-FI.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-fr-FR.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-gl-ES.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-he-IL.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-hr-HR.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-hu-HU.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-id-ID.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-it-IT.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-ja-JP.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-ko-KR.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-lt-LT.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-lt-LV.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-mn-MN.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-nb-NO.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-nl-NL.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-pl-PL.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-pt-BR.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-pt-PT.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-ro-RO.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-ru-RU.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-sk-SK.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-sl-SI.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-sr-RS-Latin.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-sr-RS.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-sv-SE.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-ta-IN.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-th-TH.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-tr-TR.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-uk-UA.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-vi-VN.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-zh-CN.js" />
    <Content Include="Scripts\plugins\summernote\lang\summernote-zh-TW.js" />
    <Content Include="Scripts\plugins\summernote\plugin\databasic\summernote-ext-databasic.css" />
    <Content Include="Scripts\plugins\summernote\plugin\databasic\summernote-ext-databasic.js" />
    <Content Include="Scripts\plugins\summernote\plugin\hello\summernote-ext-hello.js" />
    <Content Include="Scripts\plugins\summernote\plugin\specialchars\summernote-ext-specialchars.js" />
    <Content Include="Scripts\plugins\summernote\summernote.css" />
    <Content Include="Scripts\plugins\summernote\summernote.js" />
    <Content Include="Scripts\plugins\summernote\summernote.min.js" />
    <Content Include="Scripts\plugins\sweetalert\sweetalert.min.js" />
    <Content Include="Scripts\plugins\switchery\switchery.js" />
    <Content Include="Scripts\plugins\tinycon\tinycon.min.js" />
    <Content Include="Scripts\plugins\toastr\toastr.min.js" />
    <Content Include="Scripts\plugins\touchspin\jquery.bootstrap-touchspin.min.js" />
    <Content Include="Scripts\plugins\validate\jquery.validate.min.js" />
    <Content Include="Scripts\plugins\validate\messages_pt-br.js" />
    <Content Include="Scripts\plugins\validate\messages_es.js" />
    <Content Include="Scripts\plugins\velocity\velocity.min.js" />
    <Content Include="Scripts\plugins\velocity\velocity.ui.min.js" />
    <Content Include="Scripts\plugins\video\responsible-video.js" />
    <Content Include="Scripts\plugins\waitingFor\waitingFor.js" />
    <Content Include="login.asp" />
    <Content Include="PoliticaPrivacidade\PoliticaPrivacidade.htm" />
    <Content Include="Views\Web.config" />
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Navegacao\_Footer.cshtml" />
    <Content Include="Views\Navegacao\_Navigation.cshtml" />
    <Content Include="Views\Navegacao\_TopNavbar.cshtml" />
    <Content Include="Views\Login\Login.cshtml" />
    <Content Include="Scripts\plugins\chartist\chartist.min.js.map" />
    <Content Include="Scripts\plugins\diff_match_patch\COPYING" />
    <Content Include="Scripts\plugins\justified-gallery\README.md" />
    <Content Include="Views\Navegacao\_Navigation_Admin_Clientes.cshtml" />
    <Content Include="Views\Supervisao\Clientes.cshtml" />
    <Content Include="Views\Navegacao\_Navigation_Admin_Medicoes.cshtml" />
    <Content Include="Views\Supervisao\Medicoes.cshtml" />
    <Content Include="Views\Navegacao\_Navigation_Admin_Medicao.cshtml" />
    <Content Include="Views\Supervisao\Medicao_Energia_Slider.cshtml" />
    <Content Include="Views\Navegacao\_Navigation_GerOper_Medicoes.cshtml" />
    <Content Include="Views\Navegacao\_Navigation_GerOper_Medicao.cshtml" />
    <Content Include="Views\Dashboard\_DB_Atualizar.cshtml" />
    <Content Include="Views\Dashboard\_DB_Demanda.cshtml" />
    <Content Include="Views\Dashboard\DB_Superv.cshtml" />
    <Content Include="Views\Dashboard\_DB_Consumo.cshtml" />
    <Content Include="Views\Dashboard\_DB_FatPot.cshtml" />
    <Content Include="Views\Dashboard\_DB_Tempo.cshtml" />
    <Content Include="Scripts\plugins\simpleWeather\.jshintrc" />
    <Content Include="Scripts\plugins\simpleWeather\bower.json" />
    <Content Include="Scripts\plugins\simpleWeather\CHANGELOG.md" />
    <Content Include="Scripts\plugins\simpleWeather\component.json" />
    <Content Include="Scripts\plugins\simpleWeather\package.json" />
    <Content Include="Scripts\plugins\simpleWeather\README.md" />
    <Content Include="Scripts\plugins\simpleWeather\simpleweather.jquery.json" />
    <Content Include="Views\Dashboard\_DB_Utilidades.cshtml" />
    <Content Include="Views\Dashboard\_DB_EA.cshtml" />
    <Content Include="Views\Dashboard\DB_Superv_Editar.cshtml" />
    <Content Include="Views\Dashboard\_DB_Editar.cshtml" />
    <Content Include="Views\Financas\Fatura_Energia.cshtml" />
    <Content Include="Views\Financas\_Fatura_Energia_Atualizar.cshtml" />
    <Content Include="Views\Financas\_Fatura_Energia_Azul_ComPorc.cshtml" />
    <Content Include="Views\Relatorios\_Eventos.cshtml" />
    <Content Include="Views\Relatorios\_Relat_Atualizar.cshtml" />
    <Content Include="Views\Relatorios\Relat_Dem_Ativa.cshtml" />
    <Content Include="Views\Relatorios\Relat_Eventos.cshtml" />
    <Content Include="Views\Mensagens\InBox.cshtml" />
    <Content Include="Views\Mensagens\EmailView.cshtml" />
    <Content Include="Views\Alertas\ShowAlerts_Original.cshtml" />
    <Content Include="Views\Contato\Contato.cshtml" />
    <Content Include="Views\Supervisao\Gateways.cshtml" />
    <Content Include="Views\Relatorios\_FatPot_Diario.cshtml" />
    <Content Include="Views\Relatorios\Relat_FatPot.cshtml" />
    <Content Include="Views\Relatorios\Relat_Consumo.cshtml" />
    <Content Include="Views\Relatorios\_Consumo_Diario.cshtml" />
    <Content Include="Views\Supervisao\Medicao_Utilidades.cshtml" />
    <Content Include="Views\Supervisao\Medicao_EA.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Mensal.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Anual.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Semanal.cshtml" />
    <Content Include="Views\Relatorios\_Consumo_Mensal.cshtml" />
    <Content Include="Views\Relatorios\_Consumo_Anual.cshtml" />
    <Content Include="Views\Relatorios\_FatPot_Mensal.cshtml" />
    <Content Include="Views\Relatorios\_Consumo_Semanal.cshtml" />
    <Content Include="Views\Relatorios\_FatPot_Anual.cshtml" />
    <Content Include="Views\Relatorios\_FatPot_Semanal.cshtml" />
    <Content Include="Views\Relatorios\_FatCarga_Diario.cshtml" />
    <Content Include="Views\Relatorios\Relat_FatCarga.cshtml" />
    <Content Include="Views\Relatorios\_FatCarga_Semanal.cshtml" />
    <Content Include="Views\Relatorios\_FatCarga_Mensal.cshtml" />
    <Content Include="Views\Relatorios\_FatCarga_Anual.cshtml" />
    <Content Include="Views\Relatorios\_Utilidades_Diario.cshtml" />
    <Content Include="Views\Relatorios\Relat_Utilidades.cshtml" />
    <Content Include="Views\Relatorios\_Utilidades_Semanal.cshtml" />
    <Content Include="Views\Relatorios\_Utilidades_Mensal.cshtml" />
    <Content Include="Views\Relatorios\_Utilidades_Anual.cshtml" />
    <Content Include="Views\Relatorios\_EA_Diario.cshtml" />
    <Content Include="Views\Relatorios\Relat_EA.cshtml" />
    <Content Include="Views\Relatorios\_EA_Semanal.cshtml" />
    <Content Include="Views\Relatorios\_EA_Mensal.cshtml" />
    <Content Include="Views\Relatorios\_EA_Anual.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Diario_Area_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Semanal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Mensal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Anual_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Consumo_Semanal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Consumo_Mensal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Consumo_Anual_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatPot_Diario_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatPot_Semanal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatPot_Mensal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatPot_Anual_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatCarga_Diario_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatCarga_Mensal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatCarga_Semanal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatCarga_Anual_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Utilidades_Diario_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Utilidades_Semanal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Utilidades_Mensal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Utilidades_Anual_PDF.cshtml" />
    <Content Include="Views\Relatorios\_EA_Diario_PDF.cshtml" />
    <Content Include="Views\Relatorios\_EA_Mensal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_EA_Anual_PDF.cshtml" />
    <Content Include="Views\Relatorios\_EA_Semanal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Eventos_PDF.cshtml" />
    <Content Include="Views\Configuracao\Cliente_Editar.cshtml" />
    <Content Include="Views\Configuracao\Clientes.cshtml" />
    <Content Include="Views\Configuracao\Gateways.cshtml" />
    <Content Include="Views\Configuracao\Gateway_Editar.cshtml" />
    <Content Include="Views\Configuracao\GrupoUnidades.cshtml" />
    <Content Include="Views\Configuracao\GrupoUnidades_Editar.cshtml" />
    <Content Include="Views\Configuracao\Unidades.cshtml" />
    <Content Include="Views\Configuracao\Unidades_Editar.cshtml" />
    <Content Include="Views\Configuracao\Medicoes.cshtml" />
    <Content Include="Views\Configuracao\Medicoes_Editar.cshtml" />
    <Content Include="Views\Configuracao\HistoricoContratos_Editar.cshtml" />
    <Content Include="Views\Configuracao\Usuarios.cshtml" />
    <Content Include="Views\Configuracao\Usuarios_Editar.cshtml" />
    <Content Include="Views\Migracao\Migracao.cshtml" />
    <Content Include="Views\UsuarioPerfil\EditGerOper.cshtml" />
    <Content Include="Views\UsuarioPerfil\EditAdmin.cshtml" />
    <Content Include="Views\Home\Inicio_Admin.cshtml" />
    <Content Include="Views\Supervisao\Gateway_HistoricoSinal.cshtml" />
    <Content Include="Views\Financas\TarifasEner_Distribuidoras.cshtml" />
    <Content Include="Views\Financas\TarifasEner_Historico.cshtml" />
    <Content Include="Views\Financas\TarifasEner_Editar_DataResolucao.cshtml" />
    <Content Include="Views\Financas\ICMS_Historico.cshtml" />
    <Content Include="Views\Financas\_ICMS_Historico_Atualizar.cshtml" />
    <Content Include="Views\Financas\ICMS_Editar.cshtml" />
    <Content Include="Views\Financas\Fechamentos_Historico.cshtml" />
    <Content Include="Views\Financas\Fechamento_Editar.cshtml" />
    <Content Include="Views\Financas\TarifasEner_Editar_AZ.cshtml" />
    <Content Include="Views\Financas\TarifasEner_Editar_VD.cshtml" />
    <Content Include="Views\Financas\TarifasEner_Editar_CV.cshtml" />
    <Content Include="Views\Financas\Bandeiras_Historico.cshtml" />
    <Content Include="Views\Financas\Bandeira_Editar.cshtml" />
    <Content Include="Views\Manutencao\Gateways_StartUp.cshtml" />
    <Content Include="Views\Manutencao\Gateways_Pendencia.cshtml" />
    <Content Include="Views\Manutencao\Gateways_Bloqueada.cshtml" />
    <Content Include="Views\Manutencao\Busca.cshtml" />
    <Content Include="Views\Manutencao\_Busca_Resultado.cshtml" />
    <Content Include="Views\Manutencao\_Busca_Resultado_Cliente.cshtml" />
    <Content Include="Views\Manutencao\_Busca_Resultado_Gateway.cshtml" />
    <Content Include="Views\Manutencao\_Busca_Resultado_Medicao.cshtml" />
    <Content Include="Views\Manutencao\_Busca_Resultado_Usuario.cshtml" />
    <Content Include="Views\Financas\_Fatura_Energia_Verde.cshtml" />
    <Content Include="Views\Financas\_Fatura_Energia_Convencional.cshtml" />
    <Content Include="Views\Manutencao\Gateways_Falha.cshtml" />
    <Content Include="Views\Manutencao\Constante.cshtml" />
    <Content Include="Views\Manutencao\UsuarioEvento.cshtml" />
    <Content Include="Views\Manutencao\_UsuarioEvento_Resultado.cshtml" />
    <Content Include="Views\Manutencao\PreenchimentoDemanda.cshtml" />
    <Content Include="Views\Manutencao\_PreenchimentoDemanda_Mostrar.cshtml" />
    <Content Include="Views\RelatEmail\Diario.cshtml" />
    <Content Include="Views\RelatEmail\Semanal.cshtml" />
    <Content Include="Views\RelatEmail\Mensal.cshtml" />
    <Content Include="Views\Relatorios\Relat_Grafico_Print.cshtml" />
    <Content Include="Views\Financas\PisCofins_Distribuidoras.cshtml" />
    <Content Include="Views\Financas\PisCofins_Historico.cshtml" />
    <Content Include="Views\Financas\PisCofins_Editar.cshtml" />
    <Content Include="Views\Navegacao\_LoginPartial.cshtml" />
    <Content Include="Views\Supervisao\Medicao_Energia_Prod.cshtml" />
    <Content Include="Views\Supervisao\Medicao_EA_Prod.cshtml" />
    <Content Include="Views\Supervisao\Medicao_Utilidades_Prod.cshtml" />
    <Content Include="Views\Navegacao\_Layout.cshtml" />
    <Content Include="Views\Relatorios\Relat_Consolidado_DemCons_Anual.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_DemCons_Anual.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_DemCons_Anual_PDF.cshtml" />
    <Content Include="Views\Financas\_Fatura_Energia_Azul_PDF.cshtml" />
    <Content Include="Views\Financas\Fatura_Energia_Print.cshtml" />
    <Content Include="Views\Financas\_Fatura_Energia_Verde_PDF.cshtml" />
    <Content Include="Views\Financas\_Fatura_Energia_Convencional_PDF.cshtml" />
    <Content Include="Views\Relatorios\Relat_Dem_Reativa.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Diario_Area_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Semanal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Mensal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Anual_PDF.cshtml" />
    <Content Include="Views\Supervisao\Medicao_Energia_PDA.cshtml" />
    <Content Include="Views\Agentes\Distribuidoras.cshtml" />
    <Content Include="Views\Agentes\Distribuidoras_Editar.cshtml" />
    <Content Include="Views\Ranking\Ranking_Configuracao.cshtml" />
    <Content Include="Views\Ranking\Ranking_Editar.cshtml" />
    <Content Include="Views\Ranking\Ranking_Mensal.cshtml" />
    <Content Include="Views\Ranking\_Ranking_Atualizar.cshtml" />
    <Content Include="Views\Ranking\_Ranking_Mensal.cshtml" />
    <Content Include="Views\Ranking\_Ranking_Mensal_PDF.cshtml" />
    <Content Include="Views\Ranking\Ranking_Mensal_Print.cshtml" />
    <Content Include="Views\Dashboard\_DB_Meta.cshtml" />
    <Content Include="Views\Supervisao\Medicoes_Meta.cshtml" />
    <Content Include="Views\Financas\_Fatura_Energia_Azul.cshtml" />
    <Content Include="Views\Financas\_Fatura_Energia_Convencional_ComPorc.cshtml" />
    <Content Include="Views\Financas\_Fatura_Energia_Verde_ComPorc.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Supervisao_Clientes.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Supervisao_Medicoes.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Supervisao_Medicoes_MetaConsumo.cshtml" />
    <Content Include="Views\Navegacao\_TabelaMedicoes.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Supervisao_Energia.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Supervisao_Utilidades.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Supervisao_Analogicas.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Supervisao_Energia_MetaConsumo.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Dashboard_Painel.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Dashboard_Configuracao.cshtml" />
    <Content Include="Views\Navegacao\ShowIntro_COVID.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Supervisao_Gateways.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_DemAtiva_Diario.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_DemReativa_Diario.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Consumo_Diario.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_FatPot_Diario.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_FatCarga_Diario.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Utilidades_Diario.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Analogicas_Diario.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_DemAtiva_Semanal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_DemReativa_Semanal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Consumo_Semanal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_FatPot_Semanal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_FatCarga_Semanal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Utilidades_Semanal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Analogicas_Semanal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_DemAtiva_Mensal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_DemReativa_Mensal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Consumo_Mensal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_FatPot_Mensal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_FatCarga_Mensal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Utilidades_Mensal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Analogicas_Mensal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_DemAtiva_Anual.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_DemReativa_Anual.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Consumo_Anual.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_NavegacaoFerramentas.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_FatCarga_Anual.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Utilidades_Anual.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Analogicas_Anual.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Consolidado_Energia_Anual.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_EventosGateway.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Financas_Configuracao_Bandeiras.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Financas_Configuracao_BandeirasEditar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Financas_Fatura_Energia.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Financas_Configuracao_Distribuidoras.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Financas_Configuracao_DistribuidorasEditar.cshtml" />
    <Content
      Include="Views\Ajuda\_Ajuda_Financas_Configuracao_Tarifas_Energia_Distribuidoras.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Financas_Configuracao_Tarifas_Energia_Historicos.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Financas_Configuracao_Tarifas_Energia_Tarifas.cshtml" />
    <Content
      Include="Views\Ajuda\_Ajuda_Financas_Configuracao_Tarifas_Energia_Tarifas_Editar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Financas_Configuracao_PIS_COFINS_Distribuidoras.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Financas_Configuracao_PIS_COFINS_Editar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Financas_Configuracao_PIS_COFINS_Historico.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_Medicoes_ICMS.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Financas_Configuracao_FechamentosFatura.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_RankingMensal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Ranking_Configuracao.cshtml" />
    <Content Include="Views\Manutencao\_UsuarioEvento_QuemLogou_Resultado.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Administradores.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Consultores.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Consultores_Configuracao.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_Clientes.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_ClientesEditar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_Gateways.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_GatewaysEditar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_GruposUnidades.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_GruposUnidadesEditar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_Unidades.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_UnidadesEditar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_Medicoes.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_MedicoesEditar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_Usuarios.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_UsuariosEditar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_MeuPerfil.cshtml" />
    <Content Include="Views\Configuracao\HistoricoICMS_Editar.cshtml" />
    <Content Include="Views\Admins\AdminsGESTAL.cshtml" />
    <Content Include="Views\Admins\AdminsGESTAL_Editar.cshtml" />
    <Content Include="Views\Admins\Consultores.cshtml" />
    <Content Include="Views\Admins\Consultores_Editar.cshtml" />
    <Content Include="Views\Admins\OperadoresConsultor.cshtml" />
    <Content Include="Views\Admins\OperadoresConsultor_Editar.cshtml" />
    <Content Include="Views\Analises\Dem_Contrato_Ideal.cshtml" />
    <Content Include="Views\Analises\_Dem_Contrato_Ideal_Atualizar.cshtml" />
    <Content Include="Views\Analises\_Dem_Contrato_Ideal_Azul.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Analise_DemandaIdeal.cshtml" />
    <Content Include="Views\Analises\_Dem_Contrato_Ideal_Azul_PDF.cshtml" />
    <Content Include="Views\Analises\Dem_Contrato_Ideal_Print.cshtml" />
    <Content Include="Views\Analises\_Dem_Contrato_Ideal_Verde.cshtml" />
    <Content Include="Views\Analises\_Dem_Contrato_Ideal_Verde_PDF.cshtml" />
    <Content Include="Views\Financas\Fatura_Energia_Resumida.cshtml" />
    <Content Include="Views\Financas\_Fatura_Energia_Resumida.cshtml" />
    <Content Include="Views\Financas\_Fatura_Energia_Resumida_PDF.cshtml" />
    <Content Include="Views\Financas\Fatura_Energia_Resumida_Print.cshtml" />
    <Content Include="Views\Supervisao\_ObservacaoMedicao.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_FatPot_Anual.cshtml" />
    <Content Include="Views\Financas\FaturaOutros_Historico.cshtml" />
    <Content Include="Views\Financas\FaturaOutros_Editar.cshtml" />
    <Content Include="Views\Supervisao\Medicao_Ciclometro.cshtml" />
    <Content Include="Views\Financas\TarifasUtil_Grupos.cshtml" />
    <Content Include="Views\Financas\TarifasUtil_Grupos_Editar.cshtml" />
    <Content Include="Views\Financas\TarifasUtil_Historico.cshtml" />
    <Content Include="Views\Financas\TarifasUtil_Editar.cshtml" />
    <Content Include="Views\Relatorios\Relat_Ciclometro.cshtml" />
    <Content Include="Views\Analises\Correcao_FatPot.cshtml" />
    <Content Include="Views\Analises\_Correcao_FatPot.cshtml" />
    <Content Include="Views\Analises\_Correcao_FatPot_Atualizar.cshtml" />
    <Content Include="Views\Analises\_Correcao_FatPot_PDF.cshtml" />
    <Content Include="Views\Analises\Correcao_FatPot_Print.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Analise_CorrecaoFatPot.cshtml" />
    <Content Include="Views\Supervisao\Medicao_Energia_Formula.cshtml" />
    <Content Include="Views\Supervisao\Medicao_Energia_PDA_Formula.cshtml" />
    <Content Include="Views\Supervisao\Medicao_Utilidades_Formula.cshtml" />
    <Content Include="Views\Financas\Fatura_Utilidades.cshtml" />
    <Content Include="Views\Financas\_Fatura_Utilidades_Atualizar.cshtml" />
    <Content Include="Views\Financas\_Fatura_Utilidades_Tipo1.cshtml" />
    <Content Include="Views\Financas\_Fatura_Utilidades_Tipo1_PDF.cshtml" />
    <Content Include="Views\Financas\Fatura_Utilidades_Print.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Financas_Fatura_Utilidades.cshtml" />
    <Content Include="Views\Financas\Fatura_Utilidades_Resumida.cshtml" />
    <Content Include="Views\Financas\Fatura_Utilidades_Resumida_Print.cshtml" />
    <Content Include="Views\Financas\_Fatura_Utilidades_Resumida.cshtml" />
    <Content Include="Views\Financas\_Fatura_Utilidades_Resumida_PDF.cshtml" />
    <Content Include="Views\Mensagens\EmailSend.cshtml" />
    <Content Include="Scripts\plugins\summernote\font\summernote.eot" />
    <Content Include="Scripts\plugins\summernote\font\summernote.ttf" />
    <Content Include="Scripts\plugins\summernote\font\summernote.woff" />
    <Content Include="Views\Home\_Inicio_Admin_Atualizar.cshtml" />
    <Content Include="Views\Configuracao\GrupoUsuarios.cshtml" />
    <Content Include="Views\Configuracao\GrupoUsuarios_Editar.cshtml" />
    <Content Include="Views\Manutencao\AnaliseMedicoes.cshtml" />
    <Content Include="Views\Manutencao\_AnaliseMedicoes_Resultado.cshtml" />
    <Content Include="Views\MercadoLivre\TarifasMercadoLivre_Historico_Editar.cshtml" />
    <Content Include="Views\Relatorios\Relat_Consolidado_DemCons_Mensal.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_DemCons_Mensal.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_DemCons_Mensal_PDF.cshtml" />
    <Content Include="Views\Relatorios\Relat_EventosUsuario.cshtml" />
    <Content Include="Views\Relatorios\_EventosUsuario.cshtml" />
    <Content Include="Views\Relatorios\_EventosUsuario_PDF.cshtml" />
    <Content Include="Views\Manutencao\Feriados_Historico.cshtml" />
    <Content Include="Views\Manutencao\Feriados_Editar.cshtml" />
    <Content Include="Views\Financas\Fatura_Energia_Historico.cshtml" />
    <Content Include="Views\Financas\Fatura_Energia_Editar.cshtml" />
    <Content Include="Views\Financas\_Fatura_Energia_Editar_Atualizar.cshtml" />
    <Content Include="Views\Financas\_Fatura_Energia_Editar_Azul.cshtml" />
    <Content Include="Views\Manutencao\UsuariosSemLogar.cshtml" />
    <Content Include="Views\Manutencao\ClientesSemLogar.cshtml" />
    <Content Include="Views\Supervisao\Clientes_OperacaoAssistida.cshtml" />
    <Content Include="Views\Dashboard\_DB_DemandaMensal.cshtml" />
    <Content Include="Views\Dashboard\_DB_DemandaAnual.cshtml" />
    <Content Include="Views\Supervisao\Medicoes_OperacaoAssistida.cshtml" />
    <Content Include="Views\Supervisao\_Medicoes_OperacaoAssistida_Atualizar.cshtml" />
    <Content Include="Views\Supervisao\_Medicoes_OperacaoAssistida.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Diario_Area.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Semanal.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Mensal.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Anual.cshtml" />
    <Content Include="Views\Relatorios\_Ciclometro_Diario.cshtml" />
    <Content Include="Views\Relatorios\_Ciclometro_Diario_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Ciclometro_Semanal.cshtml" />
    <Content Include="Views\Relatorios\_Ciclometro_Semanal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Ciclometro_Mensal.cshtml" />
    <Content Include="Views\Relatorios\_Ciclometro_Mensal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Ciclometro_Anual.cshtml" />
    <Content Include="Views\Relatorios\_Ciclometro_Anual_PDF.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Ciclometro_Diario.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Ciclometro_Semanal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Ciclometro_Mensal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Ciclometro_Anual.cshtml" />
    <Content Include="Views\Relatorios\_EventosModal.cshtml" />
    <Content Include="Views\Supervisao\_ObservacoesMedicao.cshtml" />
    <Content Include="Views\Relatorios\_ObservacoesModal.cshtml" />
    <Content Include="Views\Relatorios\_Relat_Botoes.cshtml" />
    <Content Include="Views\Relatorios\_Relat_Botoes_EA_Semanal.cshtml" />
    <Content Include="Views\Configuracao\GatewaysProducao.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Diario_Barra.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Diario_Barra_PDF.cshtml" />
    <Content Include="Views\Relatorios\Relat_FatUtilizacao.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Diario.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Semanal2.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Mensal.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Anual.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Semanal.cshtml" />
    <Content Include="Views\Configuracao\HistoricoRural_Editar.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Diario_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Semanal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Mensal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Anual_PDF.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_FatUtilizacao_Diario.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_FatUtilizacao_Semanal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_FatUtilizacao_Mensal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_FatUtilizacao_Anual.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_KPI_IndicadorEconomia.cshtml" />
    <Content Include="Views\Kpi\KPI_IndEconomia.cshtml" />
    <Content Include="Views\Kpi\_KPI_Atualizar.cshtml" />
    <Content Include="Views\Kpi\_KPI_IndEconomia_Diario.cshtml" />
    <Content Include="Views\Kpi\_ObservacoesModal.cshtml" />
    <Content Include="Views\Kpi\_KPI_Botoes.cshtml" />
    <Content Include="Views\Dashboard\_DB_KPI_EconomiaMensal.cshtml" />
    <Content Include="Views\Monitoracao\Inicio.cshtml" />
    <Content Include="Views\Monitoracao\_Inicio_Atualizar.cshtml" />
    <Content Include="Views\Monitoracao\_Inicio_Cabecalho.cshtml" />
    <Content Include="Views\Monitoracao\_Inicio_GraficoProcessados_SemBanda.cshtml" />
    <Content Include="Views\Rateio\TarifasRateio_Grupos.cshtml" />
    <Content Include="Views\Rateio\TarifasRateio_Grupos_Editar.cshtml" />
    <Content Include="Views\Rateio\TarifasRateio_Editar.cshtml" />
    <Content Include="Views\Rateio\TarifasRateio_Historico.cshtml" />
    <Content Include="Views\Rateio\Rateio_Configuracao.cshtml" />
    <Content Include="Views\Rateio\Rateio_Editar.cshtml" />
    <Content Include="Views\Rateio\Rateio_Calculo.cshtml" />
    <Content Include="Views\Rateio\_Rateio_Atualizar.cshtml" />
    <Content Include="Views\Rateio\_Rateio_Calculo.cshtml" />
    <Content Include="Views\Configuracao\GrupoMedicoes.cshtml" />
    <Content Include="Views\Configuracao\GrupoMedicoes_Editar.cshtml" />
    <Content Include="Views\Rateio\_Rateio_TabelaGrupoMedicoes.cshtml" />
    <Content Include="Views\Rateio\_Rateio_Calculo_PDF.cshtml" />
    <Content Include="Views\Rateio\Rateio_Calculo_Print.cshtml" />
    <Content Include="Views\Dashboard\_DB_KPI_EconomiaDiaria.cshtml" />
    <Content Include="Views\Dashboard\_DB_Erro.cshtml" />
    <Content Include="Views\Dashboard\_DB_KPI_PotenciaInstalada.cshtml" />
    <Content Include="Views\Simulacao\Simulacao_Editar.cshtml" />
    <Content Include="Views\Monitoracao\Gestor.cshtml" />
    <Content Include="Views\Monitoracao\_Gestor_Cabecalho.cshtml" />
    <Content Include="Views\Monitoracao\_Gestor_Atualizar.cshtml" />
    <Content Include="Views\Monitoracao\_Inicio_GraficoProcessados_ComBanda.cshtml" />
    <Content Include="Views\Monitoracao\_Inicio_GraficoProcessados.cshtml" />
    <Content Include="Views\Relatorios\Relat_Eventos_Gateway.cshtml" />
    <Content Include="Views\Download\Download_HistoricoMedicao.cshtml" />
    <Content Include="Views\Kpi\KPI_Historico_Editar.cshtml" />
    <Content Include="Views\Kpi\KPI_Configuracao.cshtml" />
    <Content Include="Views\Kpi\KPI_Configuracao_Editar.cshtml" />
    <Content Include="Views\Kpi\KPI_Historico.cshtml" />
    <Content Include="Scripts\plugins\simplerWeather\.gitignore" />
    <Content Include="Scripts\plugins\simplerWeather\LICENSE" />
    <Content Include="Scripts\plugins\simplerWeather\proxy.php" />
    <Content Include="Scripts\plugins\simplerWeather\README.md" />
    <Content Include="Scripts\plugins\skycons\.gitignore" />
    <Content Include="Scripts\plugins\skycons\package.json" />
    <Content Include="Scripts\plugins\skycons\README.md" />
    <Content Include="Views\Kpi\KPI_Demanda.cshtml" />
    <Content Include="Views\Kpi\KPI_Consumo.cshtml" />
    <Content Include="Views\Kpi\_Dem_Anual.cshtml" />
    <Content Include="Views\Kpi\_Dem_Anual_PDF.cshtml" />
    <Content Include="Views\Kpi\_Dem_Diario.cshtml" />
    <Content Include="Views\Kpi\_Dem_Diario_PDF.cshtml" />
    <Content Include="Views\Kpi\_Dem_Mensal.cshtml" />
    <Content Include="Views\Kpi\_Dem_Mensal_PDF.cshtml" />
    <Content Include="Views\Kpi\_Dem_Semanal.cshtml" />
    <Content Include="Views\Kpi\_Dem_Semanal_PDF.cshtml" />
    <Content Include="Views\Kpi\_Consumo_Anual.cshtml" />
    <Content Include="Views\Kpi\_Consumo_Anual_PDF.cshtml" />
    <Content Include="Views\Kpi\_Consumo_Diario.cshtml" />
    <Content Include="Views\Kpi\_Consumo_Diario_PDF.cshtml" />
    <Content Include="Views\Kpi\_Consumo_Mensal.cshtml" />
    <Content Include="Views\Kpi\_Consumo_Mensal_PDF.cshtml" />
    <Content Include="Views\Kpi\_Consumo_Semanal.cshtml" />
    <Content Include="Views\Kpi\_Consumo_Semanal_PDF.cshtml" />
    <Content Include="Views\Kpi\_EventosModal.cshtml" />
    <Content Include="Imagens\HistKPI_Exemplo.xls" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Diario_Area.cshtml" />
    <Content Include="Views\Home\_Inicio_Admin_GraficoProcessados.cshtml" />
    <Content Include="Views\Manutencao\Medicoes.cshtml" />
    <Content Include="Views\Manutencao\ExcluirRegistrosMedicao.cshtml" />
    <Content Include="Views\Metas\Metas_Medicoes.cshtml" />
    <Content Include="Views\Metas\Metas_Historico.cshtml" />
    <Content Include="Views\Metas\Metas_Historico_Editar.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_DemCons_Diario.cshtml" />
    <Content Include="Views\Relatorios\Relat_Consolidado_DemCons_Diario.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_DemCons_Diario_PDF.cshtml" />
    <Content Include="Views\Financas\Custos_Ultrapassagem.cshtml" />
    <Content Include="Views\Financas\Custos_Ultrapassagem_Print.cshtml" />
    <Content Include="Views\Financas\_Custos_Ultrapassagem.cshtml" />
    <Content Include="Views\Financas\_Custos_Ultrapassagem_PDF.cshtml" />
    <Content Include="Views\MercadoLivre\TarifasMercadoLivre_Grupos.cshtml" />
    <Content Include="Views\MercadoLivre\TarifasMercadoLivre_Grupos_Editar.cshtml" />
    <Content Include="Views\MercadoLivre\TarifasMercadoLivre_Historico.cshtml" />
    <Content Include="Views\Financas\Fatura_Energia_LerPDF.cshtml" />
    <Content Include="Views\Upload\ProcessaSCDE.cshtml" />
    <Content Include="Views\Upload\_ProcessaSCDE_Resultado.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_EA_Diario.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_EA_Diario_PDF.cshtml" />
    <Content Include="Views\Relatorios\Relat_Consolidado_EA_Diario.cshtml" />
    <Content Include="Views\Supervisao\_SaidasDigitais_Resultado.cshtml" />
    <Content Include="Views\Supervisao\_EntradasDigitais_Resultado.cshtml" />
    <Content Include="Views\Supervisao\EntradasDigitais.cshtml" />
    <Content Include="Views\Supervisao\SaidasDigitais.cshtml" />
    <Content Include="Views\Financas\MercadoLivre_Editar.cshtml" />
    <Content Include="Views\Financas\MercadoLivre_Historico.cshtml" />
    <Content Include="Views\Dashboard\_Painel_Editar.cshtml" />
    <Content Include="Views\Configuracao\Empresas.cshtml" />
    <Content Include="Views\Configuracao\Empresa_Editar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Contato_GESTAL.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Contato.cshtml" />
    <Content Include="Views\Supervisao\Medicao_Energia_SCDE.cshtml" />
    <Content Include="Views\Supervisao\Medicao_Energia_SCDE_Formula.cshtml" />
    <Content Include="Views\ContratosCCEE\ContratoCCEE_Editar.cshtml" />
    <Content Include="Views\ContratosCCEE\ContratosCCEE.cshtml" />
    <Content Include="Views\Home\Inicio_CPFL.cshtml" />
    <Content Include="Views\Configuracao\GateX_CtrlHorario.cshtml" />
    <Content Include="Views\Configuracao\GateX_CtrlHorario_Editar.cshtml" />
    <Content Include="Views\Supervisao\Teste_SmartMQTT.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Diario_Barra.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Diario_Barra_PDF.cshtml" />
    <Content Include="Views\Monitoracao\Recebidos.cshtml" />
    <Content Include="Views\Monitoracao\_Recebidos_Atualizar.cshtml" />
    <Content Include="Views\Configuracao\Contatos.cshtml" />
    <Content Include="Views\Configuracao\Contato_Editar.cshtml" />
    <Content Include="Views\Configuracao\Menu.cshtml" />
    <Content Include="Views\Manutencao\LimparInexistentes_SupervisaoGateway.cshtml" />
    <Content Include="Views\Supervisao\ContratosCCEE.cshtml" />
    <Content Include="Views\Agentes\Comercializadoras.cshtml" />
    <Content Include="Views\Agentes\Comercializadora_Editar.cshtml" />
    <Content Include="Views\Agentes\ComercializadoraContato_Editar.cshtml" />
    <Content Include="Views\Configuracao\Cliente_Arvore.cshtml" />
    <Content Include="Scripts\plugins\FancyTree\jquery.fancytree-all-deps.min.js.map" />
    <Content Include="Scripts\plugins\FancyTree\skin-common.less" />
    <Content Include="Views\Relatorios\_Consumo_Diario_PDF.cshtml" />
    <Content Include="Views\Configuracao\GateX_SaidaDigital_Editar.cshtml" />
    <Content Include="Views\Configuracao\GateX_SaidaDigital.cshtml" />
    <Content Include="Views\Download\Download_CadastroClientes.cshtml" />
    <Content Include="Views\Download\Download_ContratosCCEE.cshtml" />
    <Content Include="Views\Dashboard\DB_Clientes.cshtml" />
    <Content Include="Views\Dashboard\DB_Cliente_GruposPaineis.cshtml" />
    <Content Include="Views\Dashboard\DB_Cliente_GrupoPaineis_Editar.cshtml" />
    <Content Include="Views\Dashboard\DB_Gestor_GruposPaineis.cshtml" />
    <Content Include="Views\Dashboard\DB_Gestor_GrupoPaineis_Editar.cshtml" />
    <Content Include="Views\EnviaRelatorios\EnviaRelatorios.cshtml" />
    <Content Include="Views\EnviaRelatorios\_Dem_Ativa_Diario_Barra_PDF.cshtml" />
    <Content Include="Views\EnviaRelatorios\_Consumo_Diario_PDF.cshtml" />
    <Content Include="Views\EnviaRelatorios\_FatPot_Diario_PDF.cshtml" />
    <Content Include="Views\EnviaRelatorios\_Dem_Ativa_Semanal_PDF.cshtml" />
    <Content Include="Views\EnviaRelatorios\_Dem_Ativa_Diario_Area_PDF.cshtml" />
    <Content Include="Views\EnviaRelatorios\_Consumo_Semanal_PDF.cshtml" />
    <Content Include="Views\EnviaRelatorios\_FatPot_Semanal_PDF.cshtml" />
    <Content Include="Views\EnviaRelatorios\_Dem_Ativa_Mensal_PDF.cshtml" />
    <Content Include="Views\EnviaRelatorios\_Consumo_Mensal_PDF.cshtml" />
    <Content Include="Views\EnviaRelatorios\_FatPot_Mensal_PDF.cshtml" />
    <Content Include="Views\EnviaRelatorios\_Utilidades_Diario_PDF.cshtml" />
    <Content Include="Views\EnviaRelatorios\_Utilidades_Semanal_PDF.cshtml" />
    <Content Include="Views\EnviaRelatorios\_Utilidades_Mensal_PDF.cshtml" />
    <Content Include="Views\EnviaRelatorios\_EA_Diario_PDF.cshtml" />
    <Content Include="Views\EnviaRelatorios\_EA_Semanal_PDF.cshtml" />
    <Content Include="Views\EnviaRelatorios\_EA_Mensal_PDF.cshtml" />
    <Content Include="Views\EnviaRelatorios\_Ciclometro_Diario_PDF.cshtml" />
    <Content Include="Views\EnviaRelatorios\_Ciclometro_Semanal_PDF.cshtml" />
    <Content Include="Views\EnviaRelatorios\_Ciclometro_Mensal_PDF.cshtml" />
    <Content Include="Views\EnviaRelatorios\_Supervisao_Energia_PDF.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_GateX_ProgramacoesHorarias.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_GateX_ProgramacoesHorariasEditar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_GateX_SaidasDigitais.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_GateX_SaidasDigitaisEditar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Dashboard_Painel_CPFL.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Supervisao_Energia_CPFL.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Supervisao_Medicoes_CPFL.cshtml" />
    <Content Include="Views\Supervisao\Clientes_Inativos.cshtml" />
    <Content Include="Views\Download\Download_PROINFA.cshtml" />
    <Content Include="Views\Upload\Upload_PROINFA.cshtml" />
    <Content Include="Views\Relatorios\Relat_Provisionamento_Semanal.cshtml" />
    <Content Include="Views\Relatorios\_Provisionamento_Semanal_Tipo1_Tipo2.cshtml" />
    <Content Include="Views\Manutencao\AlterarPeriodosMedicao.cshtml" />
    <Content Include="Views\Relatorios\_Provisionamento_Semanal_Consumo.cshtml" />
    <Content Include="Views\Relatorios\_Provisionamento_Semanal_Tipo1_Tipo2_Analise.cshtml" />
    <Content Include="Views\Relatorios\_Provisionamento_Semanal_Tipo1_Tipo2_Rateio.cshtml" />
    <Content Include="Views\Financas\PLD_Historico.cshtml" />
    <Content Include="Views\Financas\PLD_Editar.cshtml" />
    <Content Include="Views\Relatorios\_Provisionamento_Semanal_Tipo1_Tipo2_FaturaUsoRede.cshtml" />
    <Content Include="Views\Relatorios\_Provisionamento_Semanal_Tipo1_Tipo2_FaturaCurtoPrazo.cshtml" />
    <Content Include="Views\Relatorios\_Provisionamento_Semanal_Tipo1_Tipo2_FaturaLongoPrazo.cshtml" />
    <Content Include="Views\Relatorios\_Provisionamento_Semanal_Tipo1_Tipo2_PDF.cshtml" />
    <Content Include="Views\Analises\AnaliseHorarioCons_Configuracao.cshtml" />
    <Content Include="Views\Analises\AnaliseHorarioCons_Editar.cshtml" />
    <Content Include="Views\Analises\AnaliseHorarioConsGruposMed_Editar.cshtml" />
    <Content Include="Views\ContratosCCEE\ContratosCCEE_Grupos.cshtml" />
    <Content Include="Views\ContratosCCEE\ContratosCCEE_Grupos_Editar.cshtml" />
    <Content Include="Views\Relatorios\Relat_Provisionamento_Semanal_Empresas.cshtml" />
    <Content Include="Views\Relatorios\Relat_Provisionamento_Semanal_Clientes.cshtml" />
    <Content Include="Views\Relatorios\_Provisionamento_Semanal_Contratos.cshtml" />
    <Content Include="Views\Relatorios\_Provisionamento_Semanal_Tipo3_Analise.cshtml" />
    <Content Include="Views\Relatorios\_Provisionamento_Semanal_Tipo3_Rateio_NUnidades.cshtml" />
    <Content Include="Views\Relatorios\_Provisionamento_Semanal_Tipo3_FaturaUsoRede.cshtml" />
    <Content
      Include="Views\Relatorios\_Provisionamento_Semanal_Tipo3_FaturaCurtoPrazo_NUnidades.cshtml" />
    <Content
      Include="Views\Relatorios\_Provisionamento_Semanal_Tipo3_FaturaLongoPrazo_NUnidades.cshtml" />
    <Content Include="Views\Relatorios\_Provisionamento_Semanal_Tipo3_NUnidades_PDF.cshtml" />
    <Content Include="Views\CPFL\_Provisionamento_Semanal_Tipo1_Tipo2_PDF.cshtml" />
    <Content Include="Views\CPFL\_Provisionamento_Semanal_Tipo3_PDF.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Contato.cshtml" />
    <Content Include="Views\Analises\AnaliseHorarioCons_Print.cshtml" />
    <Content Include="Views\Analises\_AnaliseHorarioCons.cshtml" />
    <Content Include="Views\Analises\_AnaliseHorarioCons_Atualizar.cshtml" />
    <Content Include="Views\Analises\_AnaliseHorarioCons_PDF.cshtml" />
    <Content Include="Views\Analises\AnaliseHorarioCons.cshtml" />
    <Content Include="Views\Dashboard\_DB_ComparativoFatura_Atual.cshtml" />
    <Content Include="Views\Dashboard\_DB_ComparativoFatura_Projetada.cshtml" />
    <Content Include="Views\Dashboard\_DB_Fatura.cshtml" />
    <Content Include="Views\Manutencao\CopiarRegistrosMedicao.cshtml" />
    <Content Include="Views\Supervisao\Medicao_EA_Formula.cshtml" />
    <Content Include="Views\Configuracao\_GateX_CtrlHorario_PDF.cshtml" />
    <Content Include="Views\Configuracao\_GateX_SaidasDigitais_PDF.cshtml" />
    <Content Include="Views\Configuracao\GateX_CtrlHorario_Print.cshtml" />
    <Content Include="Views\Configuracao\GateX_SaidasDigitais_Print.cshtml" />
    <Content Include="Views\Navegacao\ShowIntro.cshtml" />
    <Content Include="Views\Analises\Analise_RamoAtividade.cshtml" />
    <Content Include="Views\Analises\_Analise_RamoAtividade_Atualizar.cshtml" />
    <Content Include="Views\Analises\_Analise_RamoAtividade_Calculo.cshtml" />
    <Content Include="Views\Analises\_Analise_RamoAtividade_Calculo_PDF.cshtml" />
    <Content Include="Views\Analises\Analise_RamoAtividade_Print.cshtml" />
    <Content Include="Views\Analises\Analise_RamoAtividade_Mes.cshtml" />
    <Content Include="Views\Analises\Analise_RamoAtividade_Mes_Print.cshtml" />
    <Content Include="Views\Analises\_Analise_RamoAtividade_Mes_Atualizar.cshtml" />
    <Content Include="Views\Analises\_Analise_RamoAtividade_Mes_Calculo.cshtml" />
    <Content Include="Views\Analises\_Analise_RamoAtividade_Mes_Calculo_PDF.cshtml" />
    <Content Include="Views\Relatorios\DistribuicaoConsumo.cshtml" />
    <Content Include="Views\Relatorios\_DistribuicaoConsumo_Atualizar.cshtml" />
    <Content Include="Views\Relatorios\_DistribuicaoConsumo.cshtml" />
    <Content Include="Views\Relatorios\DistribuicaoConsumo_Configuracao.cshtml" />
    <Content Include="Views\Relatorios\DistribuicaoConsumo_Editar.cshtml" />
    <Content Include="Views\Relatorios\_DistribuicaoConsumo_PDF.cshtml" />
    <Content Include="Views\Relatorios\DistribuicaoConsumo_Print.cshtml" />
    <Content Include="Views\Relatorios\Relat_UFER.cshtml" />
    <Content Include="Views\Relatorios\_UFER_Diario.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_UFER_Diario.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_UFER_Anual.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_UFER_Mensal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_UFER_Semanal.cshtml" />
    <Content Include="Views\Relatorios\_UFER_Diario_PDF.cshtml" />
    <Content Include="Views\Relatorios\_UFER_Semanal.cshtml" />
    <Content Include="Views\Relatorios\_UFER_Semanal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_UFER_Mensal.cshtml" />
    <Content Include="Views\Relatorios\_UFER_Mensal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_UFER_Anual.cshtml" />
    <Content Include="Views\Relatorios\_UFER_Anual_PDF.cshtml" />
    <Content Include="Views\Dashboard\_DB_UFER.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_EA_Anual.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_EA_Anual_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_EA_Mensal.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_EA_Mensal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_Utilidades_Anual.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_Utilidades_Anual_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_Utilidades_Diario.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_Utilidades_Diario_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_Utilidades_Mensal.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_Utilidades_Mensal_PDF.cshtml" />
    <Content Include="Views\Relatorios\Relat_Consolidado_EA_Anual.cshtml" />
    <Content Include="Views\Relatorios\Relat_Consolidado_EA_Mensal.cshtml" />
    <Content Include="Views\Relatorios\Relat_Consolidado_Utilidades_Anual.cshtml" />
    <Content Include="Views\Relatorios\Relat_Consolidado_Utilidades_Diario.cshtml" />
    <Content Include="Views\Relatorios\Relat_Consolidado_Utilidades_Mensal.cshtml" />
    <Content Include="Views\Relatorios\_TempoAtuacao_EntradasDigitais_Mensal.cshtml" />
    <Content Include="Views\Relatorios\_TempoAtuacao_EntradasDigitais_Mensal_PDF.cshtml" />
    <Content Include="Views\Relatorios\Relat_TempoAtuacao_EntradasDigitais_Mensal.cshtml" />
    <Content Include="Views\Relatorios\_Provisionamento_Semanal_Tipo3_Rateio_1Unidade.cshtml" />
    <Content
      Include="Views\Relatorios\_Provisionamento_Semanal_Tipo3_FaturaCurtoPrazo_1Unidade.cshtml" />
    <Content
      Include="Views\Relatorios\_Provisionamento_Semanal_Tipo3_FaturaLongoPrazo_1Unidade.cshtml" />
    <Content Include="Views\Relatorios\_Provisionamento_Semanal_Tipo3_Fatura_1Unidade.cshtml" />
    <Content Include="Views\Relatorios\_Provisionamento_Semanal_Tipo3.cshtml" />
    <Content Include="Views\Relatorios\_TempoAtuacao_SaidasDigitais_Mensal.cshtml" />
    <Content Include="Views\Relatorios\_TempoAtuacao_SaidasDigitais_Mensal_PDF.cshtml" />
    <Content Include="Views\Relatorios\Relat_TempoAtuacao_SaidasDigitais_Mensal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Analise_HorarioCons.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Analise_HorarioCons_Configuracao.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Analise_HorarioCons_Editar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Analise_HorarioCons_MedEditar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Comercializadoras.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Comercializadoras_Editar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_Contatos.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_ContatosEditar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_Empresas.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_EmpresasEditar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_EntradasDigitais.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_EntradasDigitaisEditar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_GruposMedicoes.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_GruposMedicoesEditar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_SaidasDigitais.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Configuracao_SaidasDigitaisEditar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_DistribuicaoCons.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_DistribuicaoCons_Configuracao.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_DistribuicaoCons_Editar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Download_CadastrosClientes.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Download_ContratosCCEE.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Download_Demanda.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Ferramentas.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Financas_Cativo_Livre.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Financas_Configuracao_Tarifas_Utilidades.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Financas_Configuracao_Tarifas_Utilidades_Editar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Financas_Configuracao_Tarifas_Utilidades_Grupos.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Financas_CustosUltrapassagem.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Financas_Fatura_EnergiaResumida.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Financas_Fatura_UtilidadesResumida.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_KPI_Configuracao.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_KPI_Consumo.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_KPI_Demanda.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_KPI_Historicos.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_KPI_HistoricosEditar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Menu_Clientes.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_MercadoLivre_Grupos.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_MercadoLivre_Historico.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_MercadoLivre_HistoricoEditar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Metas_Historico.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Metas_HistoricoEditar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_RamoAtividade.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Metas_Medicoes.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Rateio_Calculo.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Rateio_Configuracao.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Rateio_Editar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Rateios_GruposTarifas.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Rateios_Tarifas.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Rateios_TarifasEditar.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Consolidado_EA_Anual.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Consolidado_EA_Diario.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Consolidado_EA_Mensal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Consolidado_Energia_Diario.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Consolidado_Energia_Mensal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Consolidado_Utilidades_Anual.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Consolidado_Utilidades_Diario.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Relatorio_Consolidado_Utilidades_Mensal.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_TempoAtuacao_SaidasDigitais.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_TempoAtuacao_EntradasDigitais.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Supervisao_Ciclometro.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Supervisao_EntradasDigitais.cshtml" />
    <Content Include="Views\Ajuda\_Ajuda_Supervisao_SaidasDigitais.cshtml" />
    <Content Include="Views\Relatorios\_Provisionamento_Semanal_Tipo3_1Unidade_PDF.cshtml" />
    <Content Include="Views\CPFL\_Provisionamento_Semanal_Tipo3_NUnidades_PDF.cshtml" />
    <Content Include="Views\CPFL\_Provisionamento_Semanal_Tipo3_1Unidade_PDF.cshtml" />
    <Content Include="Views\Supervisao\Medicao_Meteorologia.cshtml" />
    <Content Include="Views\Relatorios\_Meteorologia_Diario.cshtml" />
    <Content Include="Views\Relatorios\_Meteorologia_Diario_PDF.cshtml" />
    <Content Include="Views\Relatorios\Relat_Meteorologia.cshtml" />
    <Content Include="Views\Relatorios\_Meteorologia_Anual.cshtml" />
    <Content Include="Views\Relatorios\_Meteorologia_Anual_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Meteorologia_Mensal.cshtml" />
    <Content Include="Views\Relatorios\_Meteorologia_Mensal_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Meteorologia_Semanal.cshtml" />
    <Content Include="Views\Relatorios\_Meteorologia_Semanal_PDF.cshtml" />
    <Content Include="Views\Simulacao\_Simulacoes.cshtml" />
    <Content Include="Views\Simulacao\_Simulacoes_Cargas_Editar.cshtml" />
    <Content Include="Views\Simulacao\_Simulacoes_Editar.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Anual_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Diario_Area_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Diario_Barra_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Mensal_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Semanal_Simulacao_PDF.cshtml" />
    <Content Include="Views\Analises\Comparativo_Contratos.cshtml" />
    <Content Include="Views\Analises\Comparativo_Contratos_Print.cshtml" />
    <Content Include="Views\Analises\_Comparativo_Contratos.cshtml" />
    <Content Include="Views\Analises\_Comparativo_Contratos_PDF.cshtml" />
    <Content Include="Views\Download\Download_UtilizacaoDemanda.cshtml" />
    <Content Include="Views\Analises\_Oportunidades_Demanda.cshtml" />
    <Content Include="Views\Analises\_Oportunidades_Demanda_PDF.cshtml" />
    <Content Include="Views\Analises\Oportunidades_Demanda.cshtml" />
    <Content Include="Views\Analises\Oportunidades_Demanda_Print.cshtml" />
    <Content Include="Views\Alertas\ShowAlerts.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Anual_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Diario_Area_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Diario_Barra_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Mensal_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Semanal_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatCarga_Anual_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatCarga_Diario_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatCarga_Mensal_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatCarga_Semanal_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatPot_Anual_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatPot_Diario_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatPot_Mensal_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatPot_Semanal_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Anual_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Diario_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Mensal_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Semanal_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_UFER_Anual_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_UFER_Diario_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_UFER_Mensal_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_UFER_Semanal_Simulacao_PDF.cshtml" />
    <Content Include="Views\Upload\Upload_UtilidadesMensal.cshtml" />
    <Content Include="Views\Upload\_ProcessaUtilidadesMensal_Resultado.cshtml" />
    <Content Include="Views\Relatorios\_Consumo_Anual_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Consumo_Diario_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Consumo_Mensal_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Consumo_Semanal_Simulacao_PDF.cshtml" />
    <Content Include="Views\Upload\_ProcessaHistoricoMedicao_Resultado.cshtml" />
    <Content Include="Views\Upload\Upload_HistoricoMedicao.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Diario_Area.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Diario_Area_Simulacao_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Diario_Barra.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Diario_Barra_PDF.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Diario_Barra_Simulacao_PDF.cshtml" />
    <Content Include="Views\Notificacoes\NotificacaoSend.cshtml" />
    <Content Include="Views\PainelJBS\_PainelJBS_Demanda.cshtml" />
    <Content Include="Views\PainelJBS\_PainelJBS_Demanda_Atualizar.cshtml" />
    <Content Include="Views\PainelJBS\_PainelJBS_Demanda_PDF.cshtml" />
    <Content Include="Views\PainelJBS\PainelJBS_Demanda.cshtml" />
    <Content Include="Views\PainelJBS\PainelJBS_Demanda_Print.cshtml" />
    <Content Include="Views\PainelJBS\_PainelJBS_Multas.cshtml" />
    <Content Include="Views\PainelJBS\PainelJBS_Multas.cshtml" />
    <Content Include="Views\PainelJBS\PainelJBS_Multas_Print.cshtml" />
    <Content Include="Views\PainelJBS\_PainelJBS_Multas_PDF.cshtml" />
    <Content Include="Views\PainelJBS\_PainelJBS_TabelaMedicoes.cshtml" />
    <Content Include="Views\PainelJBS\_TabelaMedicoes.cshtml" />
    <Content Include="Views\Home\GestorAnaliseGateways.cshtml" />
    <Content Include="Views\Home\_ObservacoesGateway.cshtml" />
    <Content Include="Views\Home\_ObservacaoGateway.cshtml" />
    <Content Include="Views\Configuracao\GateX_CtrlAna.cshtml" />
    <Content Include="Views\Configuracao\_GateX_CtrlAna_PDF.cshtml" />
    <Content Include="Views\EnviaRelatorios\EnviaRelatorios_Cliente.cshtml" />
    <Content Include="Views\EditorRelat\ER_Superv_Editar.cshtml" />
    <Content Include="Views\EditorRelat\_ER_Atualizar.cshtml" />
    <Content Include="Views\EditorRelat\_ER_Demanda_Media_Mes.cshtml" />
    <Content Include="Views\EditorRelat\_ER_Demanda_Media_Mes_PDF.cshtml" />
    <Content Include="Views\EditorRelat\_ER_Editar.cshtml" />
    <Content Include="Views\EditorRelat\_ER_Erro.cshtml" />
    <Content Include="Views\EditorRelat\_ER_Erro_PDF.cshtml" />
    <Content Include="Views\EditorRelat\_ER_PDF.cshtml" />
    <Content Include="Views\EditorRelat\_ER_Superv_Atualizar.cshtml" />
    <Content Include="Views\EditorRelat\_Pagina_Editar.cshtml" />
    <Content Include="Views\EditorRelat\ER_Cliente_GrupoPaginas_Editar.cshtml" />
    <Content Include="Views\EditorRelat\ER_Cliente_GruposPaginas.cshtml" />
    <Content Include="Views\EditorRelat\ER_Clientes.cshtml" />
    <Content Include="Views\EditorRelat\ER_Print.cshtml" />
    <Content Include="Views\EditorRelat\ER_Superv.cshtml" />
    <Content Include="Views\Configuracao\GateX_CtrlAna_Editar.cshtml" />
    <Content Include="Views\Upload\Upload_HistoricoMedicao_HX600.cshtml" />
    <Content Include="Views\Upload\_ProcessaHistoricoMedicao_HX600_Resultado.cshtml" />
    <Content Include="Views\EnviaRateios\_Rateio_PDF.cshtml" />
    <Content Include="Views\Configuracao\GateX_CtrlAna_Print.cshtml" />
    <Content Include="Views\Configuracao\_GateX_CtrlFatPot_PDF.cshtml" />
    <Content Include="Views\Configuracao\GateX_CtrlFatPot_Print.cshtml" />
    <Content Include="Views\Configuracao\GateX_CtrlFatPot.cshtml" />
    <Content Include="Views\Configuracao\GateX_CtrlFatPot_Editar.cshtml" />
    <Content Include="Views\Configuracao\_GateX_CtrlDemProj_PDF.cshtml" />
    <Content Include="Views\Configuracao\GateX_CtrlDemProj.cshtml" />
    <Content Include="Views\Configuracao\GateX_CtrlDemProj_Print.cshtml" />
    <Content Include="Views\Configuracao\GateX_CtrlDemProj_Editar.cshtml" />
    <Content Include="Views\Configuracao\GateX_CtrlDemMed.cshtml" />
    <Content Include="Views\Configuracao\GateX_CtrlDemMed_Print.cshtml" />
    <Content Include="Views\Configuracao\GateX_CtrlDemMed_Editar.cshtml" />
    <Content Include="Views\Configuracao\_GateX_CtrlDemMed_PDF.cshtml" />
    <Content Include="Views\Configuracao\_GateX_CtrlDemAcum_PDF.cshtml" />
    <Content Include="Views\Configuracao\GateX_CtrlDemAcum_Print.cshtml" />
    <Content Include="Views\Configuracao\GateX_CtrlDemAcum.cshtml" />
    <Content Include="Views\Configuracao\GateX_CtrlDemAcum_Editar.cshtml" />
    <Content Include="Views\Configuracao\GateX_MedEner.cshtml" />
    <Content Include="Views\Configuracao\GateX_MedEner_Editar.cshtml" />
    <Content Include="Views\Dashboard\_DB_Editando.cshtml" />
    <Content Include="Views\Dashboard\_DB_Atualizar_Page.cshtml" />
    <Content Include="Views\Dashboard\_DB_Atualizar_Page_Editar.cshtml" />
    <Content Include="Views\EnviaAlertasEmail\EnviaAlertasEmail.cshtml" />
    <Content Include="Views\EnviaAlertasEmail\_Alertas_PDF.cshtml" />
    <Content Include="Views\Navegacao\ShowBloqueio.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Diario_Barra_Print.cshtml" />
    <Content Include="Views\Relatorios\_Ciclometro_Anual_Print.cshtml" />
    <Content Include="Views\Relatorios\_Ciclometro_Diario_Print.cshtml" />
    <Content Include="Views\Relatorios\_Ciclometro_Mensal_Print.cshtml" />
    <Content Include="Views\Relatorios\_Ciclometro_Semanal_Print.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_DemCons_Anual_Print.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_DemCons_Diario_Print.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_DemCons_Mensal_Print.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_EA_Anual_Print.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_EA_Diario_Print.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_EA_Mensal_Print.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_Utilidades_Anual_Print.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_Utilidades_Diario_Print.cshtml" />
    <Content Include="Views\Relatorios\_Consolidado_Utilidades_Mensal_Print.cshtml" />
    <Content Include="Views\Relatorios\_Consumo_Anual_Print.cshtml" />
    <Content Include="Views\Relatorios\_Consumo_Anual_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_Consumo_Diario_Print.cshtml" />
    <Content Include="Views\Relatorios\_Consumo_Diario_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_Consumo_Mensal_Print.cshtml" />
    <Content Include="Views\Relatorios\_Consumo_Mensal_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_Consumo_Semanal_Print.cshtml" />
    <Content Include="Views\Relatorios\_Consumo_Semanal_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Anual_Print.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Anual_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Diario_Area_Print.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Diario_Area_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Diario_Barra_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Mensal_Print.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Mensal_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Semanal_Print.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Ativa_Semanal_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Anual_Print.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Anual_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Diario_Area_Print.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Diario_Area_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Diario_Barra_Print.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Diario_Barra_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Mensal_Print.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Mensal_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Semanal_Print.cshtml" />
    <Content Include="Views\Relatorios\_Dem_Reativa_Semanal_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_DistribuicaoConsumo_Print.cshtml" />
    <Content Include="Views\Relatorios\_EA_Anual_Print.cshtml" />
    <Content Include="Views\Relatorios\_EA_Diario_Print.cshtml" />
    <Content Include="Views\Relatorios\_EA_Mensal_Print.cshtml" />
    <Content Include="Views\Relatorios\_EA_Semanal_Print.cshtml" />
    <Content Include="Views\Relatorios\_EventosUsuario_Print.cshtml" />
    <Content Include="Views\Relatorios\_Eventos_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatCarga_Anual_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatCarga_Anual_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatCarga_Diario_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatCarga_Diario_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatCarga_Mensal_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatCarga_Mensal_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatCarga_Semanal_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatCarga_Semanal_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatPot_Anual_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatPot_Anual_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatPot_Diario_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatPot_Diario_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatPot_Mensal_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatPot_Mensal_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatPot_Semanal_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatPot_Semanal_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Anual_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Anual_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Diario_Area_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Diario_Barra_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Diario_Barra_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Diario_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Diario_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Mensal_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Mensal_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Semanal_Print.cshtml" />
    <Content Include="Views\Relatorios\_FatUtilizacao_Semanal_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_Meteorologia_Anual_Print.cshtml" />
    <Content Include="Views\Relatorios\_Meteorologia_Diario_Print.cshtml" />
    <Content Include="Views\Relatorios\_Meteorologia_Mensal_Print.cshtml" />
    <Content Include="Views\Relatorios\_Meteorologia_Semanal_Print.cshtml" />
    <Content Include="Views\Relatorios\_Provisionamento_Semanal_Tipo1_Tipo2_Print.cshtml" />
    <Content Include="Views\Relatorios\_Provisionamento_Semanal_Tipo3_1Unidade_Print.cshtml" />
    <Content Include="Views\Relatorios\_Provisionamento_Semanal_Tipo3_NUnidades_Print.cshtml" />
    <Content Include="Views\Relatorios\_TempoAtuacao_EntradasDigitais_Mensal_Print.cshtml" />
    <Content Include="Views\Relatorios\_TempoAtuacao_SaidasDigitais_Mensal_Print.cshtml" />
    <Content Include="Views\Relatorios\_UFER_Anual_Print.cshtml" />
    <Content Include="Views\Relatorios\_UFER_Anual_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_UFER_Diario_Print.cshtml" />
    <Content Include="Views\Relatorios\_UFER_Diario_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_UFER_Mensal_Print.cshtml" />
    <Content Include="Views\Relatorios\_UFER_Mensal_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_UFER_Semanal_Print.cshtml" />
    <Content Include="Views\Relatorios\_UFER_Semanal_Simulacao_Print.cshtml" />
    <Content Include="Views\Relatorios\_Utilidades_Anual_Print.cshtml" />
    <Content Include="Views\Relatorios\_Utilidades_Diario_Print.cshtml" />
    <Content Include="Views\Relatorios\_Utilidades_Mensal_Print.cshtml" />
    <Content Include="Views\Relatorios\_Utilidades_Semanal_Print.cshtml" />
    <Content Include="Views\Analises\_AnaliseHorarioCons_Print.cshtml" />
    <Content Include="Views\Analises\_Analise_RamoAtividade_Calculo_Print.cshtml" />
    <Content Include="Views\Analises\_Analise_RamoAtividade_Mes_Calculo_Print.cshtml" />
    <Content Include="Views\Analises\_Comparativo_Contratos_Print.cshtml" />
    <Content Include="Views\Analises\_Correcao_FatPot_Print.cshtml" />
    <Content Include="Views\Analises\_Dem_Contrato_Ideal_Azul_Print.cshtml" />
    <Content Include="Views\Analises\_Dem_Contrato_Ideal_Verde_Print.cshtml" />
    <Content Include="Views\Analises\_Oportunidades_Demanda_Print.cshtml" />
    <Content Include="Views\Configuracao\_GateX_CtrlAna_Print.cshtml" />
    <Content Include="Views\Configuracao\_GateX_CtrlDemAcum_Print.cshtml" />
    <Content Include="Views\Configuracao\_GateX_CtrlDemMed_Print.cshtml" />
    <Content Include="Views\Configuracao\_GateX_CtrlDemProj_Print.cshtml" />
    <Content Include="Views\Configuracao\_GateX_CtrlFatPot_Print.cshtml" />
    <Content Include="Views\Configuracao\_GateX_CtrlHorario_Print.cshtml" />
    <Content Include="Views\Configuracao\_GateX_SaidasDigitais_Print.cshtml" />
    <Content Include="Views\Financas\_Custos_Ultrapassagem_Print.cshtml" />
    <Content Include="Views\Financas\_Fatura_Energia_Azul_Print.cshtml" />
    <Content Include="Views\Financas\_Fatura_Energia_Convencional_Print.cshtml" />
    <Content Include="Views\Financas\_Fatura_Energia_Resumida_Print.cshtml" />
    <Content Include="Views\Financas\_Fatura_Energia_Verde_Print.cshtml" />
    <Content Include="Views\Financas\_Fatura_Utilidades_Resumida_Print.cshtml" />
    <Content Include="Views\Financas\_Fatura_Utilidades_Tipo1_Print.cshtml" />
    <Content Include="Views\PainelJBS\_PainelJBS_Demanda_Print.cshtml" />
    <Content Include="Views\PainelJBS\_PainelJBS_Multas_Print.cshtml" />
    <Content Include="Views\Ranking\_Ranking_Mensal_Print.cshtml" />
    <Content Include="Views\Rateio\_Rateio_Calculo_Print.cshtml" />
    <Content Include="Views\EnviaRateios\EnviaRateios.cshtml" />
    <Content Include="Views\Relatorios\_ControleSazonalidade.cshtml" />
    <Content Include="Views\Relatorios\Relat_ControleSazonalidade.cshtml" />
    <Content Include="Views\Relatorios\_ControleSazonalidade_PDF.cshtml" />
    <Content Include="Views\API\API.cshtml" />
    <Content Include="Views\API\_Ajuda.cshtml" />
    <Content Include="Views\API\_Ajuda_Historico.cshtml" />
    <Content Include="Views\API\_Ajuda_Contato.cshtml" />
    <Content Include="Views\API\_Ajuda_HistoricoHorario.cshtml" />
    <Content Include="Views\API\_Ajuda_MedicoesConsumo.cshtml" />
    <Content Include="Views\API\_Ajuda_MedicoesLista.cshtml" />
    <Content Include="Views\Relatorios\_ControleSazonalidade_Atualizar.cshtml" />
    <Content Include="Views\Relatorios\Relat_HeatMap_Dem_Ativa _diferente.cshtml" />
    <Content Include="Views\Relatorios\Relat_HeatMap_Dem_Ativa.cshtml" />
    <Content Include="Views\Relatorios\Relat_HeatMap_Dem_Ativa_original.cshtml" />
    <Content Include="Views\Navegacao\ShowLGPD.cshtml" />
    <Content Include="Views\API\_Ajuda_MedicoesFatura.cshtml" />
    <Content Include="Views\EnviaRelatorios\_Supervisao_Energia_Desperdicio_PDF.cshtml" />
    <Content Include="Views\Kpi\KPI_Relatorios.cshtml" />
    <Content Include="Views\Kpi\_KPI_Atualizar_PelaMedicao.cshtml" />
    <Content Include="Views\Dashboard\_DB_UtilidadesMensal.cshtml" />
    <Content Include="Views\Dashboard\_DB_UtilidadesAnual.cshtml" />
    <Content Include="Views\Supervisao\Medicoes_Mapa.cshtml" />
    <Content Include="Views\Supervisao\Gateways_Servidor_IoT.cshtml" />
    <Content Include="Views\Producao\NumerosSerie.cshtml" />
    <Content Include="Views\Producao\NumeroSerie_Editar.cshtml" />
    <Content Include="Views\Configuracao\GateX_DataHora.cshtml" />
    <Content Include="Views\Configuracao\_GateX_MenuConfigEquipo.cshtml" />
    <Content Include="Views\Configuracao\_GateE_MenuConfigEquipo.cshtml" />
    <Content Include="Views\Configuracao\GateX_Versao.cshtml" />
    <Content Include="Views\Configuracao\GateE_Versao.cshtml" />
    <Content Include="Views\Configuracao\GateE_DataHora.cshtml" />
    <Content Include="Views\Suporte\GatewaysAtualizacaoFirmware.cshtml" />
    <Content Include="Views\Configuracao\GateE_Informacoes.cshtml" />
    <Content Include="Views\Configuracao\GateE_RedeIoT.cshtml" />
    <Content Include="Views\Configuracao\GateE_MedEner.cshtml" />
    <Content Include="Views\Configuracao\GateE_MedEner_Editar.cshtml" />
    <Content Include="Views\Suporte\Drivers.cshtml" />
    <Content Include="Views\Suporte\Drivers_Editar.cshtml" />
    <Content Include="Views\Suporte\Firmwares.cshtml" />
    <Content Include="Views\Suporte\Firmwares_Editar.cshtml" />
    <Content Include="Views\Configuracao\GateX_Empresa.cshtml" />
    <Content Include="Views\Configuracao\GateX_EntradaDigital.cshtml" />
    <Content Include="Views\Configuracao\GateX_EntradasDigitais_Print.cshtml" />
    <Content Include="Views\Configuracao\_GateX_EntradasDigitais_PDF.cshtml" />
    <Content Include="Views\Configuracao\_GateX_EntradasDigitais_Print.cshtml" />
    <Content Include="Views\Configuracao\GateX_EntradaDigital_Editar.cshtml" />
    <Content Include="Views\Configuracao\GateX_Logicas.cshtml" />
    <Content Include="Views\Configuracao\GateX_Logicas_Print.cshtml" />
    <Content Include="Views\Configuracao\_GateX_Logicas_PDF.cshtml" />
    <Content Include="Views\Configuracao\_GateX_Logicas_Print.cshtml" />
    <Content Include="Views\Configuracao\GateX_Logicas_Editar.cshtml" />
    <Content Include="Views\Configuracao\GateX_DatasEspeciais.cshtml" />
    <Content Include="Views\Configuracao\GateX_DatasEspeciais_Editar.cshtml" />
    <Content Include="Views\Configuracao\GateX_Usuarios.cshtml" />
    <Content Include="Views\Configuracao\GateX_Usuarios_Editar.cshtml" />
    <Content Include="Views\Configuracao\GateX_MedEner_VigDem_Editar.cshtml" />
    <Content Include="Views\Configuracao\GateX_MedEner_DemSup_Editar.cshtml" />
    <Content Include="Views\Configuracao\_GateX_MedEner_PDF.cshtml" />
    <Content Include="Views\Configuracao\_GateX_MedEner_Print.cshtml" />
    <Content Include="Views\Configuracao\GateX_MedEner_Print.cshtml" />
    <Content Include="Views\Configuracao\GateX_MedUtil.cshtml" />
    <Content Include="Views\Configuracao\_GateX_MedUtil_PDF.cshtml" />
    <Content Include="Views\Configuracao\_GateX_MedUtil_Print.cshtml" />
    <Content Include="Views\Configuracao\GateX_MedUtil_Print.cshtml" />
    <Content Include="Views\Configuracao\GateX_MedUtil_Editar.cshtml" />
    <Content Include="Views\Configuracao\GateX_MedCiclo.cshtml" />
    <Content Include="Views\Configuracao\_GateX_MedCiclo_PDF.cshtml" />
    <Content Include="Views\Configuracao\_GateX_MedCiclo_Print.cshtml" />
    <Content Include="Views\Configuracao\GateX_MedCiclo_Editar.cshtml" />
    <Content Include="Views\Configuracao\GateX_MedCiclo_Print.cshtml" />
    <Content Include="Views\Configuracao\GateX_MedAna_Print.cshtml" />
    <Content Include="Views\Configuracao\GateX_MedAna.cshtml" />
    <Content Include="Views\Configuracao\GateX_MedAna_Editar.cshtml" />
    <Content Include="Views\Configuracao\_GateX_MedAna_PDF.cshtml" />
    <Content Include="Views\Configuracao\_GateX_MedAna_Print.cshtml" />
    <Content Include="Views\Configuracao\GateX_CtrlAlarme.cshtml" />
    <Content Include="Views\Configuracao\GateX_CtrlAlarme_Editar.cshtml" />
    <Content Include="Views\Configuracao\_GateX_CtrlAlarme_PDF.cshtml" />
    <Content Include="Views\Configuracao\_GateX_CtrlAlarme_Print.cshtml" />
    <Content Include="Views\Configuracao\GateX_CtrlAlarme_Print.cshtml" />
    <Content Include="Views\Configuracao\GateX_EnvioDados.cshtml" />
    <Content Include="Views\Configuracao\GateX_ReceberConfiguracoes.cshtml" />
    <Content Include="Views\Relatorios\Relat_HeatMap_Dem_Ativa - Copy.cshtml" />
    <Content Include="Views\Configuracao\GateX_Drivers.cshtml" />
    <Content Include="Views\Supervisao\Medicao_Energia.cshtml" />
    <Content Include="Views\Configuracao\GateE_EnvioDados.cshtml" />
    <Content Include="Web.config" />
    <Content Include="Views\Navegacao\MudarSenha.cshtml" />
    <Content Include="Views\Supervisao\Gateway.cshtml" />
    <Content Include="Views\Configuracao\GateE_AtualizacaoFirmware.cshtml" />
    <Content Include="Views\Supervisao\_Gateway_GateE.cshtml" />
    <Content Include="Views\Navegacao\ShowSegundoFator.cshtml" />
    <Content Include="Views\Supervisao\_Gateway_Gate.cshtml" />
    <Content Include="Views\Supervisao\_Gateway_GateE_Completo.cshtml" />
    <Content Include="Views\Configuracao\GateE_LimparHistorico.cshtml" />
    <Content Include="Views\Supervisao\SupervisaoGateE_TesteCampo.cshtml" />
    <Content Include="Views\Supervisao\SupervisaoGateE_TesteCampo_Resultado.cshtml" />
    <Content Include="Views\Navegacao\_Favoritos.cshtml" />
    <Content Include="Views\Upload\_ProcessaHistoricoMedicao_WebEnergy_Resultado.cshtml" />
    <Content Include="Views\Upload\Upload_HistoricoMedicao_WebEnergy.cshtml" />
    <Content Include="Views\Configuracao\GateE_DriverSincronismo.cshtml" />
    <Content Include="Views\API\API_Zordon.cshtml" />
    <Content Include="Views\API\_Ajuda_Contas.cshtml" />
    <Content Include="Views\API\_Ajuda_Usuarios.cshtml" />
    <Content Include="Views\API\_Ajuda_Empresas.cshtml" />
    <Content Include="Views\API\_Ajuda_Dispositivos.cshtml" />
    <Content Include="Views\API\_Ajuda_Equipamentos.cshtml" />
    <Content Include="Views\Dashboard\_DB_GeralEnergiaMensal.cshtml" />
    <Content Include="Views\Supervisao\Medicoes_com_Observacoes.cshtml" />
    <Content Include="Views\Manutencao\ApagarEVFuturo.cshtml" />
    <Content Include="Views\Relatorios\_Comparativo_Consumo.cshtml" />
    <Content Include="Views\Relatorios\Relat_Comparativo_Consumo.cshtml" />
    <Content Include="Views\Relatorios\_Comparativo_Consumo_PDF.cshtml" />
    <Content Include="Views\Relatorios\_Comparativo_Consumo_Print.cshtml" />
    <Content Include="Views\Relatorios\Relat_Comparativo_Consumo_Print.cshtml" />
    <Content Include="Views\Suporte\AnaliseMedicoes.cshtml" />
    <Content Include="Views\Configuracao\GateE_Reboot.cshtml" />
    <Content Include="Views\Configuracao\GateE_CorrigirFalhaUpload.cshtml" />
    <Content Include="Views\Suporte\GateE_CorrigirFalhaUpload.cshtml" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="Views\Account\" />
    <Folder Include="Views\FileUpload\" />
    <Folder Include="Views\LogPageVisits\" />
    <Folder Include="Views\Recebidos\" />
    <Folder Include="Views\SupervisaoGateE_TesteCampo\" />
    <Folder Include="Views\WebSiteToImage\" />
    <Folder Include="Views\XML\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="packages.config">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Scripts\jquery-2.1.1.min.map" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Resources\ConfiguracoesGateX_Texts.es.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ConfiguracoesGateX_Texts.es.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\ConfiguracoesGateX_Texts.en.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ConfiguracoesGateX_Texts.en.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\ConfiguracoesGateX_Texts.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ConfiguracoesGateX_Texts.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\ContratosCCEETexts.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ContratosCCEETexts.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\ContratosCCEETexts.en.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ContratosCCEETexts.en.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\ContratosCCEETexts.es.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ContratosCCEETexts.es.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\MetasTexts.en.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>MetasTexts.en.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\MetasTexts.es.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>MetasTexts.es.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\MetasTexts.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>MetasTexts.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\KpiTexts.es.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>KpiTexts.es.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\KpiTexts.en.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>KpiTexts.en.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\MensagensTexts.en.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>MensagensTexts.en.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\MensagensTexts.es.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>MensagensTexts.es.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\MensagensTexts.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>MensagensTexts.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\ConfiguracaoTexts.en.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ConfiguracaoTexts.en.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\ConfiguracaoTexts.es.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ConfiguracaoTexts.es.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\DashboardTexts.es.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>DashboardTexts.es.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\DashboardTexts.en.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>DashboardTexts.en.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\DashboardTexts.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>DashboardTexts.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\AnalisesTexts.en.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>AnalisesTexts.en.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\AnalisesTexts.es.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>AnalisesTexts.es.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\AnalisesTexts.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>AnalisesTexts.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\KpiTexts.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>KpiTexts.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\RateioTexts.en.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>RateioTexts.en.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\RateioTexts.es.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>RateioTexts.es.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\RateioTexts.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>RateioTexts.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\SimulacaoTexts.en.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>SimulacaoTexts.en.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\SimulacaoTexts.es.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>SimulacaoTexts.es.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\SimulacaoTexts.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>SimulacaoTexts.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\RankingTexts.en.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>RankingTexts.en.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\RankingTexts.es.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>RankingTexts.es.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\RankingTexts.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>RankingTexts.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\FinancasTexts.es.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>FinancasTexts.es.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\FinancasTexts.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>FinancasTexts.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\FinancasTexts.en.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>FinancasTexts.en.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\RelatoriosTexts.es.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>RelatoriosTexts.es.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\RelatoriosTexts.en.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>RelatoriosTexts.en.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\RelatoriosTexts.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>RelatoriosTexts.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\ConfiguracaoTexts.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ConfiguracaoTexts.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\UsuarioPerfilTexts.es.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>UsuarioPerfilTexts.es.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\UsuarioPerfilTexts.en.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>UsuarioPerfilTexts.en.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\UsuarioPerfilTexts.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>UsuarioPerfilTexts.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\ValidateTexts.es.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ValidateTexts.es.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\ValidateTexts.en.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ValidateTexts.en.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\ValidateTexts.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ValidateTexts.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\ComumTexts.es.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ComumTexts.es.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\ComumTexts.en.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ComumTexts.en.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\ComumTexts.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ComumTexts.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\ErrosTexts.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ErrosTexts.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\SupervisaoTexts.es.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>SupervisaoTexts.es.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\SupervisaoTexts.en.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>SupervisaoTexts.en.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\SupervisaoTexts.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>SupervisaoTexts.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\MenuTexts.en.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>MenuTexts.en.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\MenuTexts.es.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>MenuTexts.es.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\LoginTexts.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>LoginTexts.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\MenuTexts.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>MenuTexts.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\LoginTexts.es.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>LoginTexts.es.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\LoginTexts.en.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>LoginTexts.en.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">
      $(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets"
    Condition="'$(VSToolsPath)' != ''" />
  <Import
    Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets"
    Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>59911</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:59328/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
          <EnableVSDocPreview>True</EnableVSDocPreview>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Enable
        NuGet Package Restore to download them. For more information, see
        http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\itext7.pdf2data.2.1.4\Build\itext7.pdf2data.props')"
      Text="$([System.String]::Format('$(ErrorText)', '..\packages\itext7.pdf2data.2.1.4\Build\itext7.pdf2data.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>