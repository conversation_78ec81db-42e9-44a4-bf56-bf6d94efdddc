﻿@media (max-width: 1620px) {

    .some_desktop {
        display: none;
    }
}

@media (min-width: 1620px) {

    .aparece_desktop {
        display: none;
    }
}

@media (max-width: 1200px) {

    .some_minidesktop {
        display: none;
    }
}

@media (min-width: 1200px) {

    .aparece_minidesktop {
        display: none;
    }
}

@media (max-width: 800px) {

    .some_tablet {
        display: none;
    }
}

@media (min-width: 800px) {

    .aparece_tablet {
        display: none;
    }
}

@media (max-width: 350px) {

    .some_celular {
        display: none;
    }
}



.titulo {
    margin: 0;
    margin-left: 0px;
    margin-top: 20px;
    margin-bottom: 15px;
    padding-left: 15px;
    padding-top: 5px;
    padding-bottom: 5px;
    font-size: 12px;
    font-weight: bold;
    border-radius: 10px;
    float: left;
}


.icones {
    padding: 0px 5px;
    font-size: 18px !important;
    float: left;
}

.icones_menu {
    font-size: 20px  !important;
    text-align: justify;
}

.bar {
    height: 18px;
    background: #1AB394;
}

.quadro_mapa {
    padding: 2px;
    border: 1px solid #efefef;
}


.link_branco a:link {
    color: #ffffff !important;
}

.link_branco a:visited {
    color: #ffffff !important;
}

.link_branco a:hover {
    color: #f0f0f0 !important;
}

.link_branco a:active {
    color: #ffffff !important;
}

.link_preto a:link {
    color: #7E6A6C !important;
}

.link_preto a:visited {
    color: #7E6A6C !important;
}

.link_preto a:hover {
    color: #a0a0a0 !important;
}

.link_preto a:active {
    color: #7E6A6C !important;
}

.link_verde a:link {
    color: #18a689 !important;
}

.link_verde a:visited {
    color: #18a689 !important;
}

.link_verde a:hover {
    color: #188689 !important;
}

.link_verde a:active {
    color: #18a689 !important;
}


.link_vermelho a:link {
    color: #ff0000 !important;
}

.link_vermelho a:visited {
    color: #ff0000 !important;
}

.link_vermelho a:hover {
    color: #aa0000 !important;
}

.link_vermelho a:active {
    color: #ff0000 !important;
}

.link_azul a:link {
    color: #1C84C6 !important;
}

.link_azul a:visited {
    color: #1C84C6 !important;
}

.link_azul a:hover {
    color: #1C84C6 !important;
}

.link_azul a:active {
    color: #1C84C6 !important;
}

.link_disabled a:link {
    color: #a0a0a0 !important;
}

.link_disabled a:visited {
    color: #a0a0a0 !important;
}

.link_disabled a:hover {
    color: #a0a0a0 !important;
}

.link_disabled a:active {
    color: #a0a0a0 !important;
}



.overlay_recebendo {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.45);
    z-index: 9999;
    color: white;
    display: inline-block;
    border-radius: 6px;
    margin-bottom: 20px;
    margin-left: 14px;
    margin-right: 14px;
}

.icone_recebendo {
    font-size: 80px !important;
    margin: 0;
    position: absolute;
    top: 37%;
    left: 49%;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

.text_recebendo {
    position: absolute;
    top: 47%;
    right: 30%;
    left: 30%;
    text-align: center;
    margin: 0;
    font-size: 20px; 
}

.spinner-recebendo {
    margin: 0;
    position: absolute;
    top: 59%;
    left: 50%;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

.spinner-recebendo .sk-spinner-wave div {
    background-color: #ffffff !important;
}


.overlay_enviando {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.45);
    z-index: 9999;
    color: white;
    display: inline-block;
    border-radius: 6px;
    margin-bottom: 20px;
    margin-left: 14px;
    margin-right: 14px;
}

.icone_enviando {
    font-size: 80px !important;
    margin: 0;
    position: absolute;
    top: 37%;
    left: 49%;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

.text_enviando {
    position: absolute;
    top: 47%;
    right: 30%;
    left: 30%;
    text-align: center;
    margin: 0;
    font-size: 20px;
}

.spinner-enviando {
    margin: 0;
    position: absolute;
    top: 59%;
    left: 50%;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

.spinner-enviando .sk-spinner-wave div {
    background-color: #ffffff !important;
}


.overlay_aguarde {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.45);
    z-index: 9999;
    color: white;
    display: inline-block;
    border-radius: 6px;
    margin-bottom: 20px;
    margin-left: 14px;
    margin-right: 14px;
}

.spinner-aguarde {
    margin: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

.spinner-aguarde .sk-spinner-wave div {
    background-color: #ffffff !important;
}




