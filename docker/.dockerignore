# Arquivos e diretórios a serem ignorados no build Docker

# Git
.git
.gitignore
.gitattributes

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentação
README*.md
*.md

# Scripts
*.sh

# Logs
logs/
*.log

# Dados
data/

# Temporários
temp/
tmp/
*.tmp

# Visual Studio
.vs/
*.user
*.suo
*.userosscache
*.sln.docstates
bin/Debug/
obj/Debug/
bin/Release/
obj/Release/

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
build/
bld/
[Bb]in/
[Oo]bj/

# MSTest test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# NuGet
*.nupkg
**/packages/*
!**/packages/build/
*.nuget.props
*.nuget.targets

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.directory

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Node.js (se houver)
node_modules/
npm-debug.log*

# Backup files
*.bak
*.backup
