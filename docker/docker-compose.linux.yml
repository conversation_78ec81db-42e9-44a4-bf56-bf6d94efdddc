services:
  # Banco de dados SQL Server
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2019-latest
    container_name: smartenergy-sqlserver
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=SmartEnergy123!
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
      - ./sql-scripts:/docker-entrypoint-initdb.d
    networks:
      - smartenergy-network
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P 'SmartEnergy123!' -Q 'SELECT 1'",
        ]
      interval: 30s
      timeout: 10s
      retries: 5

  # Aplicação SmartEnergy (Linux com Mono)
  smartenergy-app:
    build:
      context: ..
      dockerfile: docker/Dockerfile.linux
    container_name: smartenergy-app
    ports:
      - "8080:8080"
    environment:
      - MONO_ENV_OPTIONS=--server
      - ConnectionStrings__DefaultConnection=Server=sqlserver,1433;Database=SmartEnergyDB;User Id=sa;Password=SmartEnergy123!;TrustServerCertificate=true;
    depends_on:
      sqlserver:
        condition: service_healthy
    networks:
      - smartenergy-network
    restart: unless-stopped
    volumes:
      - app_logs:/app/logs
      - app_temp:/app/temp
      - ../SmartEnergy:/app:ro

  # Redis para cache
  redis:
    image: redis:7-alpine
    container_name: smartenergy-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - smartenergy-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Nginx como proxy reverso
  nginx:
    image: nginx:alpine
    container_name: smartenergy-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - smartenergy-app
    networks:
      - smartenergy-network
    restart: unless-stopped

volumes:
  sqlserver_data:
    driver: local
  redis_data:
    driver: local
  app_logs:
    driver: local
  app_temp:
    driver: local
  nginx_logs:
    driver: local

networks:
  smartenergy-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
