# Dockerfile para executar aplicação .NET Framework no Linux usando Mono
FROM mono:latest

# Instalar dependências
RUN apt-get update && apt-get install -y \
    wget \
    unzip \
    supervisor \
    nginx \
    && rm -rf /var/lib/apt/lists/*

# Instalar XSP (Mono Web Server)
RUN apt-get update && apt-get install -y mono-xsp4

# Criar diretório da aplicação
WORKDIR /app

# Copiar arquivos do projeto
COPY SmartEnergy/ ./

# Copiar packages se existir
COPY packages/ ./packages/

# Compilar a aplicação
RUN msbuild SmartEnergy.sln /p:Configuration=Release /p:Platform="Any CPU" || \
    xbuild SmartEnergy.sln /p:Configuration=Release /p:Platform="Any CPU"

# Configurar supervisor
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Expor porta
EXPOSE 8080

# Criar script de inicialização
RUN echo '#!/bin/bash\n\
    echo "Iniciando SmartEnergy..."\n\
    cd /app\n\
    exec xsp4 --port 8080 --address 0.0.0.0 --root /app --verbose\n\
    ' > /start.sh && chmod +x /start.sh

# Comando padrão
CMD ["/start.sh"]
