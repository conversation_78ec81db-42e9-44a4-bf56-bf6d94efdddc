# Use a imagem base do Windows Server Core com .NET Framework
FROM mcr.microsoft.com/dotnet/framework/aspnet:4.8-windowsservercore-ltsc2019

# Definir diretório de trabalho
WORKDIR /inetpub/wwwroot

# Copiar arquivos do projeto
COPY SmartEnergy/ ./

# Instalar dependências do NuGet (se necessário)
# RUN nuget restore SmartEnergy.sln

# Expor a porta 80
EXPOSE 80

# Configurar IIS
RUN powershell -Command \
    Import-Module WebAdministration; \
    Remove-WebSite -Name 'Default Web Site'; \
    New-WebSite -Name 'SmartEnergy' -Port 80 -PhysicalPath 'C:\inetpub\wwwroot'

# Comando padrão
CMD ["C:\\ServiceMonitor.exe", "w3svc"]
