# Override para desenvolvimento local
services:
  smartenergy-app:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - MONO_ENV_OPTIONS=--server --debug
    volumes:
      - ../SmartEnergy:/app:rw
      - ../logs/app:/var/log:rw
    ports:
      - "8080:8080"
      - "9000:9000" # Debug port

  sqlserver:
    environment:
      - MSSQL_AGENT_ENABLED=true
    ports:
      - "1433:1433"
    volumes:
      - ./sql-scripts:/docker-entrypoint-initdb.d:ro
      - ../backup:/var/opt/mssql/backup:rw

  nginx:
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ../logs/nginx:/var/log/nginx:rw
