# Configurações do SmartEnergy Docker Environment
# Copie este arquivo para .env e ajuste as configurações conforme necessário

# =============================================================================
# BANCO DE DADOS
# =============================================================================
DB_PASSWORD=SmartEnergy123!
DB_NAME=SmartEnergyDB
DB_USER=sa

# =============================================================================
# APLICAÇÃO
# =============================================================================
APP_PORT=8080
APP_ENV=Development
ASPNETCORE_ENVIRONMENT=Development

# =============================================================================
# EMAIL SMTP
# =============================================================================
SMTP_SERVER=email-smtp.sa-east-1.amazonaws.com
SMTP_PORT=587
SMTP_USER=********************
SMTP_PASSWORD=BDgMX4j7o0oo5iDCtTQovvH7nmYjapkyPtlImafb7Qov
SMTP_FROM=<EMAIL>
SMTP_ENABLE_SSL=true

# =============================================================================
# reCAPTCHA
# =============================================================================
RECAPTCHA_PUBLIC_KEY=6Lcjw9QaAAAAAAWAo3TTBZDddWsgRtNDZI0I0HUQ
RECAPTCHA_PRIVATE_KEY=6Lcjw9QaAAAAAMouCM7HCcWn4ITGNiuTx_qz3q2E

# =============================================================================
# DIRETÓRIOS
# =============================================================================
FILES_RECEBIDOS=/app/files/recebidos/
FIRMWARE_DIR=/app/firmware/
FIRMWARE_UPDATE_DIR=/app/firmware/update/
FIRMWARE_PROBLEMAS_DIR=/app/firmware/problemas/

# =============================================================================
# CONFIGURAÇÕES ESPECÍFICAS
# =============================================================================
SITE_EM_MANUTENCAO=false
MENSAGEM_MANUTENCAO=Estamos atualizando a plataforma com novos recursos e aplicando melhorias.
GRAPH_BAND=4c633a42-a74a-4c22-842d-c1d0008e1994

# =============================================================================
# MQTT/MESSAGING
# =============================================================================
SMUPDATE_TOPICO_SUBSCRIBE=SmUpdate_Cmd.*

# =============================================================================
# REDIS
# =============================================================================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# =============================================================================
# NGINX
# =============================================================================
NGINX_PORT=80
NGINX_SSL_PORT=443

# =============================================================================
# DESENVOLVIMENTO
# =============================================================================
# Descomente para habilitar debug
# MONO_ENV_OPTIONS=--server --debug
# DEBUG_PORT=9000

# =============================================================================
# PRODUÇÃO
# =============================================================================
# Para produção, altere as seguintes configurações:
# 1. DB_PASSWORD - Use uma senha forte
# 2. SMTP_* - Configure com suas credenciais reais
# 3. RECAPTCHA_* - Use suas chaves do Google reCAPTCHA
# 4. APP_ENV=Production
# 5. ASPNETCORE_ENVIRONMENT=Production
