-- Script de inicialização do banco de dados SmartEnergy
USE master;
GO

-- Criar banco de dados se não existir
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'SmartEnergyDB')
BEGIN
    CREATE DATABASE SmartEnergyDB;
END
GO

USE SmartEnergyDB;
GO

-- <PERSON><PERSON>r usuário para a aplicação (opcional)
IF NOT EXISTS (SELECT name FROM sys.server_principals WHERE name = 'smartenergy_user')
BEGIN
    CREATE LOGIN smartenergy_user WITH PASSWORD = 'SmartEnergy123!';
END
GO

IF NOT EXISTS (SELECT name FROM sys.database_principals WHERE name = 'smartenergy_user')
BEGIN
    CREATE USER smartenergy_user FOR LOGIN smartenergy_user;
    ALTER ROLE db_owner ADD MEMBER smartenergy_user;
END
GO

-- Tabelas básicas (exemplo - ajuste conforme necessário)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='AspNetUsers' AND xtype='U')
BEGIN
    CREATE TABLE AspNetUsers (
        Id NVARCHAR(128) NOT NULL PRIMARY KEY,
        Email NVARCHAR(256),
        EmailConfirmed BIT NOT NULL,
        PasswordHash NVARCHAR(MAX),
        SecurityStamp NVARCHAR(MAX),
        PhoneNumber NVARCHAR(MAX),
        PhoneNumberConfirmed BIT NOT NULL,
        TwoFactorEnabled BIT NOT NULL,
        LockoutEndDateUtc DATETIME,
        LockoutEnabled BIT NOT NULL,
        AccessFailedCount INT NOT NULL,
        UserName NVARCHAR(256) NOT NULL
    );
END
GO

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='AspNetRoles' AND xtype='U')
BEGIN
    CREATE TABLE AspNetRoles (
        Id NVARCHAR(128) NOT NULL PRIMARY KEY,
        Name NVARCHAR(256) NOT NULL
    );
END
GO

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='AspNetUserRoles' AND xtype='U')
BEGIN
    CREATE TABLE AspNetUserRoles (
        UserId NVARCHAR(128) NOT NULL,
        RoleId NVARCHAR(128) NOT NULL,
        PRIMARY KEY (UserId, RoleId),
        FOREIGN KEY (UserId) REFERENCES AspNetUsers(Id),
        FOREIGN KEY (RoleId) REFERENCES AspNetRoles(Id)
    );
END
GO

-- Inserir dados iniciais (exemplo)
IF NOT EXISTS (SELECT * FROM AspNetRoles WHERE Name = 'Admin')
BEGIN
    INSERT INTO AspNetRoles (Id, Name) VALUES (NEWID(), 'Admin');
END
GO

IF NOT EXISTS (SELECT * FROM AspNetRoles WHERE Name = 'User')
BEGIN
    INSERT INTO AspNetRoles (Id, Name) VALUES (NEWID(), 'User');
END
GO

PRINT 'Banco de dados SmartEnergyDB inicializado com sucesso!';
