# SmartEnergy - Ambiente Docker

Este documento descreve como configurar e executar o projeto SmartEnergy usando Docker e Docker Compose no Linux.

## 📋 Pré-requisitos

- Docker 20.10+
- Docker Compose 2.0+
- Git
- 4GB RAM disponível
- 10GB espaço em disco

## 🚀 Início Rápido

### 1. Clone o repositório
```bash
git clone <url-do-repositorio>
cd smartenergy
```

### 2. Execute o script de inicialização
```bash
chmod +x docker.sh
./docker.sh init
./docker.sh start
```

### 3. Acesse a aplicação
- **Aplicação**: http://localhost:8080
- **Proxy Nginx**: http://localhost
- **SQL Server**: localhost:1433
- **Redis**: localhost:6379

## 🏗️ Arquitetura

O ambiente Docker é composto por:

### Serviços

1. **smartenergy-app**: Aplicação principal
   - Porta: 8080
   - Framework: .NET Framework 4.6.2 (via Mono no Linux)
   - Servidor: XSP4 (Mono Web Server)

2. **sqlserver**: Banco de dados
   - Porta: 1433
   - Versão: SQL Server 2019 Express
   - Usuário: sa
   - Senha: SmartEnergy123!

3. **redis**: Cache
   - Porta: 6379
   - Versão: Redis 7 Alpine

4. **nginx**: Proxy reverso
   - Portas: 80, 443
   - Load balancer e SSL termination

### Volumes

- `sqlserver_data`: Dados do SQL Server
- `redis_data`: Dados do Redis
- `app_logs`: Logs da aplicação
- `nginx_logs`: Logs do Nginx

## 🔧 Configuração

### Variáveis de Ambiente

Edite o arquivo `.env` para personalizar as configurações:

```bash
# Banco de dados
DB_PASSWORD=SmartEnergy123!
DB_NAME=SmartEnergyDB

# Aplicação
APP_PORT=8080
APP_ENV=Development

# Email
SMTP_SERVER=email-smtp.sa-east-1.amazonaws.com
SMTP_USER=seu-usuario-smtp
SMTP_PASSWORD=sua-senha-smtp
```

### Connection String

A aplicação usa automaticamente a connection string configurada para Docker:
```
Server=sqlserver,1433;Database=SmartEnergyDB;User Id=sa;Password=SmartEnergy123!;TrustServerCertificate=true;
```

## 📝 Comandos Úteis

### Iniciar ambiente
```bash
./docker.sh start
```

### Parar ambiente
```bash
./docker.sh stop
```

### Ver logs em tempo real
```bash
# Todos os serviços
./docker.sh logs

# Ou usando docker-compose diretamente
docker-compose -f docker/docker-compose.linux.yml logs -f

# Apenas a aplicação
docker-compose -f docker/docker-compose.linux.yml logs -f smartenergy-app

# Apenas o banco
docker-compose -f docker/docker-compose.linux.yml logs -f sqlserver
```

### Executar comandos no container
```bash
# Acessar container da aplicação
docker exec -it smartenergy-app bash

# Acessar SQL Server
docker exec -it smartenergy-sqlserver /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P 'SmartEnergy123!'
```

### Backup do banco de dados
```bash
# Criar backup
docker exec smartenergy-sqlserver /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P 'SmartEnergy123!' -Q "BACKUP DATABASE SmartEnergyDB TO DISK = '/var/opt/mssql/backup/SmartEnergyDB.bak'"

# Copiar backup para host
docker cp smartenergy-sqlserver:/var/opt/mssql/backup/SmartEnergyDB.bak ./backup/
```

## 🐛 Troubleshooting

### Problema: Container não inicia
```bash
# Verificar logs
docker-compose -f docker-compose.linux.yml logs

# Verificar recursos
docker system df
docker system prune
```

### Problema: Erro de conexão com banco
```bash
# Verificar se SQL Server está rodando
docker exec smartenergy-sqlserver /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P 'SmartEnergy123!' -Q "SELECT @@VERSION"

# Reiniciar apenas o banco
docker-compose -f docker-compose.linux.yml restart sqlserver
```

### Problema: Aplicação não compila
```bash
# Verificar dependências Mono
docker exec smartenergy-app mono --version

# Recompilar aplicação
docker exec smartenergy-app bash -c "cd /app && xbuild SmartEnergy.sln /p:Configuration=Release"
```

### Problema: Porta já em uso
```bash
# Verificar portas em uso
netstat -tulpn | grep :8080

# Alterar porta no docker-compose
# Edite a seção ports do serviço smartenergy-app
```

## 🔒 Segurança

### Produção
Para ambiente de produção, altere:

1. **Senhas padrão**:
   - Senha do SA do SQL Server
   - Chaves do reCAPTCHA
   - Credenciais SMTP

2. **SSL/HTTPS**:
   - Configure certificados SSL no Nginx
   - Descomente a seção HTTPS no nginx.conf

3. **Firewall**:
   - Exponha apenas as portas necessárias
   - Use rede interna para comunicação entre containers

### Exemplo de configuração SSL
```bash
# Gerar certificado auto-assinado (apenas para teste)
mkdir -p nginx/ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout nginx/ssl/key.pem \
  -out nginx/ssl/cert.pem \
  -subj "/C=BR/ST=SP/L=SaoPaulo/O=SmartEnergy/CN=localhost"
```

## 📊 Monitoramento

### Health Checks
```bash
# Verificar saúde dos containers
docker-compose -f docker-compose.linux.yml ps

# Endpoint de saúde da aplicação
curl http://localhost/health
```

### Métricas
```bash
# Uso de recursos
docker stats

# Logs de acesso Nginx
tail -f logs/nginx/access.log

# Logs da aplicação
tail -f logs/app/smartenergy.log
```

## 🔄 Atualizações

### Atualizar aplicação
```bash
# Parar ambiente
./stop.sh

# Atualizar código
git pull

# Reconstruir e iniciar
./start.sh
```

### Atualizar dependências
```bash
# Reconstruir imagem
docker-compose -f docker-compose.linux.yml build --no-cache smartenergy-app

# Reiniciar serviço
docker-compose -f docker-compose.linux.yml up -d smartenergy-app
```

## 📞 Suporte

Para problemas ou dúvidas:
1. Verifique os logs dos containers
2. Consulte a seção de troubleshooting
3. Abra uma issue no repositório

## 📄 Licença

Este projeto está licenciado sob [sua licença aqui].
