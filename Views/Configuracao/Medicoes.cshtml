﻿@model IEnumerable<SmartEnergyLib.SQL.MedicoesDominio>

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Funcoes

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoMedicoes;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoMedicoes</h4>
                    <div class="pull-right dashboard-tools link_branco" style="margin-top:-8px;">
                        <h4>

                            @{
                                int IDCliente = ViewBag._IDCliente;

                                if (IDCliente > 0)
                                {
                                    <a href='@("/Configuracao/Menu?IDCliente=" + @IDCliente.ToString())' title="Menu @SmartEnergy.Resources.ConfiguracaoTexts.Configuracoes @SmartEnergy.Resources.ConfiguracaoTexts.ClienteAtual"><i class="fa fa-th-large"></i></a>
                                }
                            }

                            @{
                                int IDMedicao = ViewBag._IDMedicao;

                                if (IDMedicao > 0)
                                {
                                    <a href='@("/Configuracao/Medicoes_Editar?IDMedicao=" + @IDMedicao.ToString())' id="BotaoEditar" title="@SmartEnergy.Resources.ConfiguracaoTexts.EditarAtual"><i class="fa fa-edit"></i></a>
                                }
                            }

                            <a href='@("/Configuracao/Medicoes_Editar?IDMedicao=0")' id="BotaoAdicionar" title="@SmartEnergy.Resources.ComumTexts.BotaoInserir"><i class="fa fa-plus"></i></a>
                        </h4>
                    </div>
                </div>
                <div class="panel-body">
                    <table class="table table-striped table-bordered table-hover dataTables-medicoes">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoMedicoes</th>
                                <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.TipoMedicao</th>
                                <th class="some_minidesktop">@Funcoes_SmartEnergy.Nome_Unidades(ViewBag.Nome_Unidades, SmartEnergy.Resources.ConfiguracaoTexts.Unidade)</th>
                                <th class="some_minidesktop">Gateway</th>
                                <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.NumeroMedicao</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>

                            @{
                                int IDTipoAcesso = ViewBag._IDTipoAcesso;
                            }

                            @foreach (MedicoesDominio medicao in Model)
                            {
                                // tipo medicao
                                List<ListaTiposDominio> listatipos = ViewBag.listaTipoMedicao;
                                ListaTiposDominio tipo = listatipos.Find(item => item.ID == medicao.IDTipoMedicao);
                                string tipo_medicao = "";
                                if (tipo != null)
                                {
                                    tipo_medicao = tipo.Descricao;
                                }

                                // unidade
                                List<UnidadesDominio> listaunidades = ViewBag.listaUnidades;
                                UnidadesDominio unidade = listaunidades.Find(item => item.IDUnidade == medicao.IDUnidade);
                                string unidade_medicao = "";
                                if (unidade != null)
                                {
                                    unidade_medicao = unidade.Nome;
                                }

                                // gateway
                                List<GatewaysDominio> listagateways = ViewBag.listaGateways;
                                GatewaysDominio gateway = listagateways.Find(item => item.IDGateway == medicao.IDGateway);
                                string gateway_medicao = string.Format("{0}", medicao.IDGateway);
                                if (gateway != null)
                                {
                                    if (isUser.isGESTAL(IDTipoAcesso))
                                    {
                                        gateway_medicao = string.Format("[{0}] {1}", medicao.IDGateway, gateway.Nome);
                                    }
                                    else
                                    {
                                        gateway_medicao = string.Format("[{0}] {1}", medicao.IDGateway, gateway.Nome);
                                    }
                                }

                                // medicao interna
                                string medicao_interna = string.Format("{0}", medicao.NumMedGateway);

                                // Estacao Meteorologica
                                if (medicao.IDTipoMedicao == TIPO_MEDICAO.METEOROLOGIA)
                                {
                                    gateway_medicao = "-";
                                    medicao_interna = "-";
                                }


                                <tr>
                                    <td>@medicao.IDMedicao</td>
                                    <td>@medicao.Nome</td>
                                    <td class="some_minidesktop">@tipo_medicao</td>
                                    <td class="some_minidesktop">@unidade_medicao</td>
                                    <td class="some_minidesktop">@gateway_medicao</td>
                                    <td class="some_minidesktop">@medicao_interna</td>
                                    <td class="link_preto">
                                        <a href='@("/Configuracao/Medicoes_Editar?IDMedicao=" + @medicao.IDMedicao.ToString())' title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"><i class="fa fa-edit icones"></i></a>

                                        @if (ViewBag.Permissao == PERMISSOES.ADMIN)
                                        {
                                            <a href="#" onclick="javascript:Excluir(@medicao.IDMedicao);" id="BotaoExcluir" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-times icones"></i></a>
                                        }

                                    </td>
                                </tr>
                            }

                        </tbody>
                    </table>

                </div>
            </div>
        </div>
    </div>


</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

    $(document).ready(function () {

        var permissao = Math.ceil(@ViewBag.Permissao);
        var visivel = true;

        if (permissao == PERMISSOES_ADMIN)
        {
            visivel = true;
        }

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {
                var x = (a == "-") ? 0 : a.replace(/,/, ".");
                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-medicoes').DataTable({
            "iDisplayLength": 18,
            dom: 'Bftp',
            buttons: [
                {
                    extend: 'excelHtml5',
                    filename: 'Medicoes',
                    title: 'Medições',
                    text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-excel-o" data-toggle="tooltip" data-placement="left" title="Excel"></span>',
                    className: 'btn btn-default btn-xs'
                },
                {
                    extend: 'pdfHtml5',
                    filename: 'Medicoes',
                    title: 'Medições',
                    text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-pdf-o" data-toggle="tooltip" data-placement="left" title="PDF"></span>',
                    className: 'btn btn-default btn-xs'
                },
            ],

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "visible": visivel, "sType": "numero" },
                        { "aTargets": [1], "sType": "portugues" },
                        { "aTargets": [2], "sType": "portugues" },
                        { "aTargets": [3], "sType": "portugues" },
                        { "aTargets": [4], "sType": "portugues" },
                        { "aTargets": [5], "visible": visivel, "sType": "numero" },
                        { "aTargets": [6], "bVisible": true, "bSortable": false, "bSearchable": false },
            ],

            "aoColumns": [
            { sWidth: "5%" },
            { sWidth: "25%" },
            { sWidth: "20%" },
            { sWidth: "12%" },
            { sWidth: "18%" },
            { sWidth: "10%" },
            { sWidth: "10%" }
            ],
            'order': [1, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

        // desabilita campos
        disableAll();
    });

function disableAll() {

    var permissao = Math.ceil(@ViewBag.Permissao);

    // verifica permissao
    switch(permissao)
    {
        case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
        case PERMISSOES_PRODUCAO:           // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)
        case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
        case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte

            break;

        default:
        case PERMISSOES_GERENTE:    // 1 - permissao de Gerente: ve tudo e escreve parte
        case PERMISSOES_OPERADOR:   // 2 - permissao de Operador: ve tudo e nao pode escrever
        case PERMISSOES_CONSULTOR:  // 5 - permissao de Consultor

            document.getElementById("BotaoAdicionar").style.visibility = "hidden";
            break;
    }
}

function Excluir(IDMedicao) {
    event.stopPropagation();

    // titulo
    titulo = "Deseja excluir a Medicao?<br/>ID " + IDMedicao;

    swal({
        html: true,
        title: titulo,
        text: "Esta operação exclui todos os relacionamentos desta medição em outras configurações e os históricos:<br>Gateway<br>Grupo Unidades e Unidade<br>Usuários<br>Históricos de Medição<br>Históricos da Gateway",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#18a689",
        confirmButtonText: "Excluir",
        cancelButtonText: "Cancelar",
        closeOnConfirm: false
    }, function () {

        // excluir
        $.ajax(
        {
            type: 'GET',
            url: '/Configuracao/Medicao_Excluir',
            data: { 'IDMedicao': IDMedicao, 'Parar': 7 },
            contentType: 'application/html',
            dataType: 'html',
            cache: false,
            async: true,
            success: function (data) {

                setTimeout(function () {

                    swal({
                        title: "Excluído com sucesso",
                        type: "success",
                        confirmButtonColor: "#18a689",
                        confirmButtonText: "Fechar",
                    }, function () {

                        // atualiza pagina
                        location.reload();
                    });

                }, 100);
            },
            error: function (response) {

                setTimeout(function () {

                    swal({
                        title: "Erro",
                        text: "Erro ao excluir!",
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    });

                }, 100);

            }
        });
    });
};

    </script>
}


