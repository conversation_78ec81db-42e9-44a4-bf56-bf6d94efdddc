﻿@using SmartEnergyLib.Declaracoes

<table id="dataTables-menuGateX" class="table table-striped table-bordered table-hover">
    <thead>
        <tr>
            <th></th>
            <th>@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracao</th>
            <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.Descricao</th>
        </tr>
    </thead>
    <tbody>

        <tr style="cursor:pointer;" onclick="Receber('GateX_DataHora')">
            <td><i class="fa fa-clock-o icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.DataHora</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoDataHora</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateX_Empresa')">
            <td><i class="fa fa-building icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.Empresa</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoEmpresa</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateX_MedEner')">
            <td><i class="fa fa-flash icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.MedicoesEnergia</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.DescriConfigMedEner</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateX_MedUtil')">
            <td><i class="fa fa-tint icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.MedicoesUtilidades</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.DescriConfigMedUtil</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateX_MedAna')">
            <td><i class="fa fa-dashboard icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.MedicoesAnalogicas</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.DescriConfigMedAna</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateX_MedCiclo')">
            <td><i class="fa fa-sort-numeric-asc icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.MedicoesCiclometro</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.DescriConfigMedCiclo</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateX_EntradaDigital')">
            <td><i class="fa fa-sign-in icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoEntradasDigitais</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoEntradasDigitais</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateX_SaidaDigital')">
            <td><i class="fa fa-sign-out icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoSaidasDigitais</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoSaidasDigitais</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateX_CtrlAlarme')">
            <td><i class="fa fa-bell icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.ControleAlarme</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoCtrlAlarme</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateX_CtrlDemProj')">
            <td><i class="fa fa-line-chart icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.ControleDemProj</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.DescriConfigCtrlDemProj</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateX_CtrlDemMed')">
            <td><i class="fa fa-area-chart icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.ControleDemMed</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.DescriConfigCtrlDemMed</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateX_CtrlDemAcum')">
            <td><i class="fa fa-area-chart icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.ControleDemAcum</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.DescriConfigCtrlDemAcum</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateX_CtrlFatPot')">
            <td><i class="fa fa-bar-chart icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.ControleFatPot</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.DescriConfigCtrlFatPot</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateX_CtrlAna')">
            <td><i class="fa fa-bar-chart icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.ControleAnalogico</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.DescriConfigCtrlAna</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateX_CtrlHorario')">
            <td><i class="fa fa-history icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.ControleHorario</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.DescriConfigCtrlHorario</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateX_Logicas')">
            <td><i class="fa fa-retweet icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.Logicas</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.DescriConfigLogicas</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateX_DatasEspeciais')">
            <td><i class="fa fa-calendar icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.DatasEspeciais</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoDatasEspeciais</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateX_Usuarios')">
            <td><i class="fa fa-users icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.Usuarios</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoUsuarios</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateX_Versao')">
            <td><i class="fa fa-microchip icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.Versao</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.InfoGateway</td>
        </tr>


        @if (ViewBag.Permissao == PERMISSOES.ADMIN || ViewBag.Permissao == PERMISSOES.SUPORTE)
        {
            <tr style="cursor:pointer;" onclick="Receber('GateX_Drivers')">
                <td><i class="fa fa-usb icones_menu"></i></td>
                <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.Drivers</td>
                <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoDrivers</td>
            </tr>
            <tr style="cursor:pointer;" onclick="Receber('GateX_EnvioDados')">
                <td><i class="fa fa-upload icones_menu"></i></td>
                <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.EnvioDados</td>
                <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.ComandoEnvioDados</td>
            </tr>
            <tr style="cursor:pointer;" onclick="ReceberSemPergunta('GateX_ReceberConfiguracoes')">
                <td><i class="fa fa-download icones_menu"></i></td>
                <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.ReceberConfiguracoes</td>
                <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.ComandoReceberConfiguracoes</td>
            </tr>
        }

    </tbody>
</table>




<script type="text/javascript">

    $(document).ready(function () {



    });

</script>
