﻿@model SmartEnergyLib.SQL.GateX_Versao_Dominio

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.ConfiguracaoTexts.Versao;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="panel panel-success">
                                <div class="panel-heading">

                                    @{
                                        GatewaysDominio gateway = ViewBag.Gateway;
                                        @Html.Hidden("IDGateway", gateway.IDGateway);
                                    }

                                    <h4>Gateway</h4><br />
                                    @gateway.Nome

                                    @{
                                        int IDTipoAcesso = ViewBag._IDTipoAcesso;

                                        if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                        {
                                            <span> [@gateway.IDGateway]</span>
                                        }
                                    }

                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel panel-title">
                        <div class="panel-heading">
                            <div style="text-align:center;">
                                <h4>@SmartEnergy.Resources.ConfiguracaoTexts.InfoGateway</h4>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-offset-6 col-lg-6">
                                    <div class="ibox-tools">
                                        <a href='@("/Configuracao/Gateway_Editar?IDGateway=" + @gateway.IDGateway.ToString() + "#tab-6")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoFechar</a>
                                    </div>
                                </div>
                            </div>

                            <br />

                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-lg-4">
                                                <div class="form-group">
                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Modelo</label>
                                                    <input type='text' class="form-control" value='@Model.Modelo' disabled>
                                                </div>
                                            </div>
                                            <div class="col-lg-4">
                                                <div class="form-group">
                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Versao</label>
                                                    <input type='text' class="form-control" value='@Model.Versao' disabled>
                                                </div>
                                            </div>
                                            <div class="col-lg-4">
                                                <div class="form-group">
                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.DriverRedeIO</label>
                                                    <input type='text' class="form-control" value='@Model.DriverRemota' disabled>
                                                </div>
                                            </div>
                                        </div>

                                        <hr />

                                        <div class="row">
                                            <div class="col-lg-4">
                                                <div class="form-group">
                                                    <label class="control-label">&nbsp;Número de dias no histórico</label>
                                                    <input type='text' class="form-control" value='@Model.qDia' disabled>
                                                </div>
                                            </div>
                                        </div>

                                        <br />
                                          
                                        @{
                                            if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                            {
                                                <label class="control-label">&nbsp;Históricos das Medições</label>
                                                <table class="table table-striped table-bordered table-hover dataTables-info">
                                                    <thead>
                                                        <tr>
                                                            <th>Medição</th>
                                                            <th>Número de Medições</th>
                                                            <th>Variáveis</th>
                                                            <th>Intervalo</th>
                                                            <th>Blocos</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            @{
                                                                var qMdH = "---";
                                                                if (Model.HistMdEn_qMdH > 0)
                                                                {
                                                                    qMdH = string.Format("{0}", Model.HistMdEn_qMdH);
                                                                }
                                                        
                                                                var qV = "---";
                                                                if (Model.HistMdEn_qV == 3)
                                                                {
                                                                    qV = "Ativo, Indutivo e Capativo";
                                                                }

                                                                var iT = "---";
                                                                if (Model.HistMdEn_iT == 3)
                                                                {
                                                                    iT = "15 minutos";
                                                                }
                                                       
                                                            }
                                                            <td>Energia Elétrica</td>
                                                            <td>@qMdH</td>
                                                            <td>@qV</td>
                                                            <td>@iT</td>
                                                            <td>@Model.HistMdEn_qBk</td>
                                                        </tr>

                                                        <tr>
                                                            @{
                                                                qMdH = "---";
                                                                if (Model.HistMdUt_qMdH > 0)
                                                                {
                                                                    qMdH = string.Format("{0}", Model.HistMdUt_qMdH);
                                                                }

                                                                qV = "---";
                                                                if (Model.HistMdUt_qV == 1)
                                                                {
                                                                    qV = "Acumulado";
                                                                }

                                                                iT = "---";
                                                                if (Model.HistMdUt_iT == 4)
                                                                {
                                                                    iT = "60 minutos";
                                                                }

                                                            }
                                                            <td>Utilidades</td>
                                                            <td>@qMdH</td>
                                                            <td>@qV</td>
                                                            <td>@iT</td>
                                                            <td>@Model.HistMdUt_qBk</td>
                                                        </tr>

                                                        <tr>
                                                            @{
                                                                qMdH = "---";
                                                                if (Model.HistMdAn_qMdH > 0)
                                                                {
                                                                    qMdH = string.Format("{0}", Model.HistMdAn_qMdH);
                                                                }

                                                                qV = "---";
                                                                if (Model.HistMdAn_qV == 3)
                                                                {
                                                                    qV = "Acumulado, Mínimo e Máximo";
                                                                }

                                                                iT = "---";
                                                                switch(Model.HistMdAn_iT)
                                                                {
                                                                    case 1:
                                                                        iT = "1 minuto";
                                                                        break;

                                                                    case 2:
                                                                        iT = "5 minutos";
                                                                        break;

                                                                    case 3:
                                                                        iT = "15 minutos";
                                                                        break;

                                                                    case 4:
                                                                        iT = "60 minutos";
                                                                        break;
                                                                }
                                                            }
                                                            <td>Analógicas</td>
                                                            <td>@qMdH</td>
                                                            <td>@qV</td>
                                                            <td>@iT</td>
                                                            <td>@Model.HistMdAn_qBk</td>
                                                        </tr>

                                                        <tr>
                                                            @{
                                                                qMdH = "---";
                                                                if (Model.HistMdCy_qMdH > 0)
                                                                {
                                                                    qMdH = string.Format("{0}", Model.HistMdCy_qMdH);
                                                                }

                                                                qV = "---";
                                                                if (Model.HistMdCy_qV == 3)
                                                                {
                                                                    qV = "NRG + UINT32";
                                                                }

                                                                iT = "---";
                                                                switch (Model.HistMdCy_iT)
                                                                {
                                                                    case 1:
                                                                        iT = "1 minuto";
                                                                        break;

                                                                    case 2:
                                                                        iT = "5 minutos";
                                                                        break;

                                                                    case 3:
                                                                        iT = "15 minutos";
                                                                        break;

                                                                    case 4:
                                                                        iT = "60 minutos";
                                                                        break;
                                                                }
                                                            }
                                                            <td>Ciclômetros</td>
                                                            <td>@qMdH</td>
                                                            <td>@qV</td>
                                                            <td>@iT</td>
                                                            <td>@Model.HistMdCy_qBk</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            }
                                        }
                                          
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="overlay_aguarde" style="display: none">
                        <div class="spinner-aguarde">
                            <div class="sk-spinner sk-spinner-wandering-cubes">
                                <div class="sk-spinner sk-spinner-wave">
                                    <div class="sk-rect1 text-ligh"></div>
                                    <div class="sk-rect2"></div>
                                    <div class="sk-rect3"></div>
                                    <div class="sk-rect4"></div>
                                    <div class="sk-rect5"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        $(document).ready(function () {


            // retornos da solicitacao da configuracao
            var solicitaProg = (@ViewBag.SolicitaProg.ToString().ToLower());

            // verifico se retornou erro
            if  (solicitaProg == false)
            {
                swal({
                    title: "Erro na recepção da configuração!",
                    type: "warning",
                    confirmButtonColor: "#1ab394",
                    confirmButtonText: "Fechar",
                    closeOnConfirm: true
                }, function(isConfirm){

                    var IDGateway = parseInt(document.getElementById("IDGateway").value);

                    // retorna para a pagina editar
                    var url = '/Configuracao/Gateway_Editar?IDGateway=' + IDGateway + '#tab-6';
                    window.location.href = url;
                });
            }

            $('form input').on('keypress', function (e) {
                return e.which !== 13;
            });

        });


    </script>
}
