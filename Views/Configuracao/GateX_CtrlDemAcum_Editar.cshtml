﻿@model SmartEnergyLib.SQL.GateX_CtrlDemAcum_Dominio

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.ConfiguracaoTexts.ControleDemAcum;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">

                    @using (Html.BeginForm("GateX_CtrlDemAcum_Editar", "Configuracao", FormMethod.Post, new { id = "form", role = "form" }))
                    {
                        @Html.Hidden("IDGateway", Model.IDGateway)
                        @Html.Hidden("Programado", Model.Programado)

                        
                        GatewaysDominio gateway = ViewBag.Gateway;
                        List<GateX_CtrlDemAcum_SD_Dominio> ctrlSDs = ViewBag.CtrlDemAcum_SD;
                        List<GateX_SD_Lista_Dominio> saidas = ViewBag.SaidasDigitais;

                        
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="panel panel-success">
                                    <div class="panel-heading">

                                        <h4>Gateway</h4><br />
                                        @gateway.Nome

                                        @{
                                            int IDTipoAcesso = ViewBag._IDTipoAcesso;

                                            if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                            {
                                                <span> [@gateway.IDGateway]</span>
                                            }
                                        }

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="panel panel-title">
                            <div class="panel-heading">
                                <div style="text-align:center;">
                                    <h4>@SmartEnergy.Resources.ConfiguracaoTexts.ControleDemAcum</h4>
                                </div>
                                <div class="pull-left relat-navega">
                                    <h4>
                                        @{
                                            if (Model.NumCtrlGateway != 0)
                                            {
                                                <a class="link_primeira">
                                                    <i class="fa fa-angle-double-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Primeira></i>
                                                </a>
                                                <a class="link_prog_menos">
                                                    <i class="fa fa-angle-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Anterior></i>
                                                </a>
                                            }

                                            if (Model.NumCtrlGateway != 11)
                                            {   
                                                <a class="link_prog_mais">
                                                    <i class="fa fa-angle-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Proxima></i>
                                                </a>
                                                <a class="link_ultima">
                                                    <i class="fa fa-angle-double-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Ultima></i>
                                                </a>
                                            }   
                                        }
                                    </h4>
                                </div>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="form-group col-lg-2">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.NumControle</label>
                                        @Html.TextBoxFor(model => model.NumCtrlGateway, new { @class = "form-control", @disabled = "disabled" })
                                    </div>
                                    <div class="col-lg-offset-4 col-lg-6">
                                        <div class="ibox-tools">
                                            <button type="button" class="btn btn-primary" onclick="Programar();" disabled="" id="BotaoProgramar">@SmartEnergy.Resources.ComumTexts.BotaoProgramar</button>
                                            <a href='@("/Configuracao/GateX_CtrlDemAcum?IDGateway=" + @Model.IDGateway + "&Origem=2")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                        </div>
                                    </div>
                                </div>

                                <br />
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="tabs-container">
                                            <ul class="nav nav-tabs">
                                                <li class="active"><a data-toggle="tab" href="#tab-1">Medição</a></li>
                                                <li class="tab-2"><a data-toggle="tab" href="#tab-2">Saídas Digitais</a></li>
                                            </ul>
                                            <div class="tab-content">
                                                <div id="tab-1" class="tab-pane active">
                                                    <div class="panel-body">
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Medicao</label>
                                                                @Html.DropDownListFor(model => model.Medicao, new SelectList(ViewBag.listaMedicoes, "ID", "Descricao", Model.Medicao), new { @class = "form-control", @disabled = "disabled" })
                                                            </div>
                                                            <div class="row">
                                                                <div class="form-group col-lg-5">
                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Medicao Secundária</label>
                                                                    @Html.DropDownListFor(model => model.Medicao2, new SelectList(ViewBag.listaMedicoes, "ID", "Descricao", Model.Medicao2), new { @class = "form-control", @disabled = "disabled" })
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <br />
                                                    </div>
                                                </div>

                                                <div id="tab-2" class="tab-pane">
                                                    <div class="panel-body">
                                                        <div class="row">
                                                            <div class="form-group col-lg-12">
                                                                <table id="dataTables-saidas" class="table table-striped table-bordered table-hover dataTables-saidas">
                                                                    <thead>
                                                                        <tr>
                                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.SaidaDigital</th>
                                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.SaidaDigital</th>
                                                                            <th>@SmartEnergy.Resources.ConfiguracoesGateX_Texts.Controle</th>
                                                                            <th>@SmartEnergy.Resources.ConfiguracoesGateX_Texts.NivelDesligamento (%)</th>
                                                                            <th></th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>

                                                                        @{
                                                                            if (ctrlSDs != null)
                                                                            {
                                                                                foreach (var ctrlSD in ctrlSDs)
                                                                                {
                                                                                    // Nome saída
                                                                                    string str_saida = string.Format("Saída {0}", ctrlSD.NumSaida);
                                                                                    if (saidas != null)
                                                                                    {
                                                                                        str_saida = string.Format("{0}", saidas.Find(x => x.NumSaidaGateway == ctrlSD.NumSaida).Descricao);
                                                                                    }  
                                                            
                                                                                    // Habilitado para o controle 
                                                                                    string str_habilitado = "---";
                                                                                    if (ctrlSD.Programado)
                                                                                    {
                                                                                        str_habilitado = string.Format("{0}", ctrlSD.NumCtrlGateway);
                                                                                    }
                                                            
                                                                                    <tr>
                                                                                        <td>@ctrlSD.NumSaida</td>
                                                                                        <td>@str_saida</td>
                                                                                        <td>@str_habilitado</td>
                                                                                        <td>@string.Format("{0}", ctrlSD.ControlAcumDeslig)</td>
                                                                                        <td class="link_preto">

                                                                                            @if (ctrlSD.NumCtrlGateway == Model.NumCtrlGateway || !ctrlSD.Programado)
                                                                                            {
                                                                                                <a href="#" class="edit-saida"><i class="fa fa-edit icones" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a> <a href="#" class="delete-saida"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>
                                                                                            }

                                                                                        </td>
                                                                                    </tr>
                                                                                }
                                                                            }
                                                                        }

                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal inmodal animated fadeIn" id="ModalCtrlSD_Editar" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Fechar</span></button>
                                        <h4 class="modal-title"><i class="fa fa-edit"></i>&nbsp;&nbsp;Controle da Saída</h4>
                                    </div>
                                    <div class="modal-body">

                                        <input type="hidden" id="nSd" name="nSd" value="" />

                                        <div class="row">
                                            <div class="col-lg-12">
                                                <div class="panel panel-success">
                                                    <div class="panel-heading">
                                                        <h4 id="numeroSaida" name="numeroSaida">&nbsp;</h4><br />
                                                        <span id="nomeSaida" name="nomeSaida">&nbsp;</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <br />
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                <div class="i-checks" style="margin-top:6px;">
                                                    <input id="HabilitarSaida" name="HabilitarSaida" type="checkbox">&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ConfiguracoesGateX_Texts.HabilitarSaida</span>
                                                </div>
                                            </div>
                                        </div>
                                        <br />
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracoesGateX_Texts.NivelDesligamento (%)</label>
                                                <input class="form-control" id="nivelDesligamento" name="nivelDesligamento" type="text" value="" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                        <button type="button" class="btn btn-primary" onclick="ModalCtrlSD_Alterar();">@SmartEnergy.Resources.ComumTexts.BotaoAtualizar</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="overlay_aguarde" style="display: none">
                            <div class="spinner-aguarde">
                                <div class="sk-spinner sk-spinner-wandering-cubes">
                                    <div class="sk-spinner sk-spinner-wave">
                                        <div class="sk-rect1 text-ligh"></div>
                                        <div class="sk-rect2"></div>
                                        <div class="sk-rect3"></div>
                                        <div class="sk-rect4"></div>
                                        <div class="sk-rect5"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/iCheck")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        $(document).ready(function () {

            // classe iChecks
            $('.i-checks').iCheck({
                checkboxClass: 'icheckbox_square-green',
                radioClass: 'iradio_square-green',
            }); ''

            //
            // VALIDACAO DOS CAMPOS
            //
            
            jQuery.validator.addMethod("alphanumeric", function (value, element) {
                return this.optional(element) || /^[\'\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\&\+\-\_\(\)," "]+$/i.test(value);
            }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

            jQuery.validator.addMethod("numeric", function (value, element) {
                return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
            }, "@SmartEnergy.Resources.ValidateTexts.numeric");

            jQuery.validator.addMethod('required-one', function (value) {
                return $('.required-one:checked').size() > 0;
            }, "@SmartEnergy.Resources.ValidateTexts.checkbox");

            var checkboxes = $('.required-one');
            var checkbox_names = $.map(checkboxes, function (e, i) { return $(e).attr("name") }).join(" ");

            $("#form").validate({
                groups: { checks: checkbox_names },
                errorPlacement: function (error, element) {
                    if (element.attr("type") == "checkbox")
                        error.insertAfter('.i-checks');
                    else
                        error.insertAfter(element);
                },
                rules: {

                },
                highlight: function (element) {
                    $(element).closest('.control-group').removeClass('success').addClass('error');
                },
                unhighlight: function (element) {
                    $(element).closest('.control-group').addClass('valid success').removeClass('error');
                },
            });


            // desabilita campos
            disableAll();

            //
            // TABELA SAIDAS
            //

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "portugues-pre": function (data) {
                    var a = 'a';
                    var e = 'e';
                    var i = 'i';
                    var o = 'o';
                    var u = 'u';
                    var c = 'c';
                    var special_letters = {
                        "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                        "É": e, "é": e, "Ê": e, "ê": e,
                        "Í": i, "í": i, "Î": i, "î": i,
                        "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                        "Ú": u, "ú": u, "Ü": u, "ü": u,
                        "ç": c, "Ç": c
                    };
                    for (var val in special_letters)
                        data = data.split(val).join(special_letters[val]).toLowerCase();
                    return data;
                },
                "portugues-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },
                "portugues-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "numero-pre": function (a) {
                    var x = (a == "-") ? 0 : a.replace(/,/, ".");
                    return parseFloat(x);
                },

                "numero-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },

                "numero-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            $('#dataTables-saidas').DataTable({
                "iDisplayLength": 8,
                dom: 'ftp',

                'order': [0, 'asc'],
                "bAutoWidth": false,
                "aoColumns": [
                    { sWidth: "10%" },
                    { sWidth: "45%" },
                    { sWidth: "15%" },
                    { sWidth: "20%" },
                    { sWidth: "10%" },
                ],

                "columnDefs": [
                    { "aTargets": [0], "sType": "numero" },
                    { "aTargets": [1], "sType": "portugues" },
                    { "aTargets": [2], "sType": "portugues" },
                    { "aTargets": [3], "sType": "numero" },
                    { "atargets": [4], "searchable": false, "orderable": false }
                ],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }
            });

            $('form input').on('keypress', function (e) {
                return e.which !== 13;
            });

            // caso for selecionado alguma TAB, aponto para ele
            // utilizado caso retornou pela configuração da empresa para ir na TAB geral
            var url = document.location.toString();

            if (url.match('#')) {
                $('.nav-tabs a[href="#' + url.split('#')[1] + '"]').tab('show');

                setTimeout(function () {
                    window.scrollTo(0, 0);
                }, 100);
            }

        });

        function disableAll() {

            var permissao = Math.ceil(@ViewBag.Permissao);

            // verifica permissao
            switch (permissao) {
                case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
                case PERMISSOES_GERENTE:            // 1 - permissao de Gerente: ve tudo e escreve parte
                case PERMISSOES_OPERADOR:           // 2 - permissao de Operador: ve tudo e nao pode escrever
                case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
                case PERMISSOES_CONSULTOR:          // 5 - permissao de Consultor
                case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte 

                    $("#BotaoProgramar").attr('disabled', false);
                    $("#Medicao").attr('disabled', false);
                    $("#Medicao2").attr('disabled', false);
                    break;

                default:
                case PERMISSOES_PRODUCAO:           // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                    $("#BotaoProgramar").attr('disabled', true);
                    break;
            }
        }



        //
        // SAIDAS
        //

        $('#dataTables-saidas').on('click', '.edit-saida', function (e) {
            e.preventDefault();

            // linha
            var row = $(this).closest('tr');

            // editar
            ModalCtrlSD_Editar(row);
        });

        function ModalCtrlSD_Editar(row) {
            event.stopPropagation();

            // le dados da linha
            var data = $('#dataTables-saidas').DataTable().row(row).data();

            // valores da janela
            document.getElementById("nSd").value = data[0];
            document.getElementById("numeroSaida").textContent = "Saída " + data[0];
            document.getElementById("nomeSaida").textContent = data[1];

            if (data[2] == '---') {
                $('#HabilitarSaida').iCheck('uncheck');
            }
            else
            {
                $('#HabilitarSaida').iCheck('check');
            }

            document.getElementById("nivelDesligamento").value = data[3];

            // apresenta janela
            $('#ModalCtrlSD_Editar').modal('show');

            $($.fn.dataTable.tables(true)).css('width', '100%');
            $($.fn.dataTable.tables(true)).DataTable().columns.adjust().draw();

        }

        function ModalCtrlSD_Alterar() {

            // controle
            var NumCtrlGateway = parseInt(document.getElementById("NumCtrlGateway").value);

            // número da saída
            var nSd_str = document.getElementById("nSd").value;
            var nSd = parseInt(nSd_str);

            // habilitar saida
            const value = $('#HabilitarSaida').iCheck('update')[0].checked;

            if (value) {
                $('#dataTables-saidas').DataTable().cell({ row: nSd, column: 2 }).data(NumCtrlGateway).draw();
            }
            else {
                $('#dataTables-saidas').DataTable().cell({ row: nSd, column: 2 }).data('---').draw();

                document.getElementById("nivelDesligamento").value = '0';
            }

            // nivel de desligamento
            var nivelDesligamento = document.getElementById("nivelDesligamento").value;
            $('#dataTables-saidas').DataTable().cell({ row: nSd, column: 3 }).data(nivelDesligamento).draw();

            // fecha janela
            $('#ModalCtrlSD_Editar').modal('hide');
        }

        $('#dataTables-saidas').on('click', '.delete-saida', function (e) {
            e.preventDefault();

            // linha
            var row = $(this).closest('tr');

            // le dados da linha
            var data = $('#dataTables-saidas').DataTable().row(row).data();
            var nSd = parseInt(data[0]);

            $('#dataTables-saidas').DataTable().cell({ row: nSd, column: 2 }).data('---').draw();
            $('#dataTables-saidas').DataTable().cell({ row: nSd, column: 3 }).data('0').draw();

        });



        //
        // NAVEGAÇÃO
        //

        $(".link_primeira").off().on("click", function (event) {
            event.stopPropagation();

            // primeira programacao
            SalvaNavega(0);
        });

        $(".link_ultima").off().on("click", function (event) {
            event.stopPropagation();

            // ultima programacao
            SalvaNavega(11);
        });

        $(".link_prog_menos").off().on("click", function (event) {
            event.stopPropagation();

            var NumCtrlGateway = parseInt(document.getElementById("NumCtrlGateway").value);

            // programacao anterior
            SalvaNavega(NumCtrlGateway - 1);
        });

        $(".link_prog_mais").off().on("click", function (event) {
            event.stopPropagation();

            var NumCtrlGateway = parseInt(document.getElementById("NumCtrlGateway").value);

            // proxima programacao
            SalvaNavega(NumCtrlGateway + 1);
        });


        function SalvaNavega(navega) {

            event.stopPropagation();

            var $form = $('#form');

            // apresenta primeiro tab que tiver erro
            var IsTabValid = true;
            var TabInvalid = 1;

            $(".tab-content").find("div.tab-pane").each(function (index, tab) {
                var id = $(tab).attr("id");
                $('a[href="#' + id + '"]').tab('show');

                IsTabValid = $("#form").valid();

                if (!IsTabValid) {

                    TabInvalid = id;
                    return false;
                }
            });

            // apresenta primeira tab ou com erro
            $('a[href="#tab-' + TabInvalid + '"]').tab('show');

            // verifica se entradas sao validas
            if (!IsTabValid) return false;


            // IDGateway
            var IDGateway = document.getElementById('IDGateway').value;

            // NumCtrlGateway (habilita para salvar)
            var NumCtrlGateway = document.getElementById('NumCtrlGateway').value;
            $("#NumCtrlGateway").attr('disabled', false);

            // lista de saidas
            var oTable = $('#dataTables-saidas').dataTable();
            var rows = oTable.fnSettings().aoData;
            var Programado = false;
            var NumSaida = 0;
            var NumCtrlGatewaySaida = 0;
            var ControlAcumDeslig = 0.0;
            var dataArray = [];

            // percorre tabela
            $.each(rows, function (i, val) {

                // pega número da saída
                NumSaida = parseInt(val._aData[0]);

                // saida habilitada
                if (val._aData[2] == '---') {
                    Programado = false;
                    NumCtrlGatewaySaida = 0;
                }
                else {
                    Programado = true;
                    NumCtrlGatewaySaida = parseInt(val._aData[2]);
                }

                // nivel desligamento
                valor_str = val._aData[3];
                var ControlAcumDeslig = parseFloat(valor_str.replace(',', '.'));

                dataArray.push({
                    "IDGateway": IDGateway,
                    "Programado": Programado,
                    "NumCtrlGateway": NumCtrlGatewaySaida,
                    "NumSaida": NumSaida,
                    "ControlAcumDeslig": ControlAcumDeslig,
                });

            });

            // aguarde
            $('.overlay_aguarde').toggle();

            // retiro campos desabilitados, pois nao envia via POST
            //$(":disabled", $('#form')).removeAttr("disabled");

            // constroi estrutura com FORM e lista
            data = { 'CtrlDemAcum': $form.serializeObject(), 'ctrlSDs': dataArray };
            data2 = JSON.stringify(data);

            $.ajax({
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                url: '/Configuracao/GateX_CtrlDemAcum_Programar',
                data: data2,
                type: 'POST',
                success: function (data) {

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {
                            swal({
                                title: "Erro",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // redireciona para pagina lista de programacoes
                                var IDGateway = document.getElementById('IDGateway').value;
                                var url = '/Configuracao/GateX_CtrlDemAcum?IDGateway=' + IDGateway + '&Origem=2';

                                window.location.href = url;
                            });
                        }
                        else {
                            // redireciona para programacao desejada
                            var IDGateway = document.getElementById('IDGateway').value;
                            var url = '/Configuracao/GateX_CtrlDemAcum_Editar?IDGateway=' + IDGateway + '&NumCtrlGateway=' + navega;

                            window.location.href = url;
                        }

                    }, 100);
                },
                error: function (xhr, status, error) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // redireciona para pagina lista de programacoes
                            var IDGateway = document.getElementById('IDGateway').value;
                            var url = '/Configuracao/GateX_CtrlDemAcum?IDGateway=' + IDGateway + '&Origem=2';

                            window.location.href = url;
                        });
                    }, 100);
                },
            });
        };



        //
        // BOTAO PROGRAMAR (SUBMIT)
        //

        $.fn.serializeObject = function () {
            var o = {};
            var a = this.serializeArray();
            $.each(a, function () {
                if (o[this.name] !== undefined) {
                    if (!o[this.name].push) {
                        o[this.name] = [o[this.name]];
                    }
                    o[this.name].push(this.value || '');
                } else {
                    o[this.name] = this.value || '';
                }
            });
            return o;
        };

        function Programar() {

            var $form = $('#form');

            // apresenta primeiro tab que tiver erro
            var IsTabValid = true;
            var TabInvalid = 1;

            $(".tab-content").find("div.tab-pane").each(function (index, tab) {
                var id = $(tab).attr("id");
                $('a[href="#' + id + '"]').tab('show');

                IsTabValid = $("#form").valid();

                if (!IsTabValid) {

                    TabInvalid = id;
                    return false;
                }
            });

            // apresenta primeira tab ou com erro
            $('a[href="#tab-' + TabInvalid + '"]').tab('show');

            // verifica se entradas sao validas
            if (!IsTabValid) return false;


            // IDGateway
            var IDGateway = document.getElementById('IDGateway').value;

            // NumCtrlGateway (habilita para salvar)
            var NumCtrlGateway = document.getElementById('NumCtrlGateway').value;
            $("#NumCtrlGateway").attr('disabled', false);

            // lista de saidas
            var oTable = $('#dataTables-saidas').dataTable();
            var rows = oTable.fnSettings().aoData;
            var Programado = false;
            var NumSaida = 0;
            var NumCtrlGatewaySaida = 0;
            var ControlAcumDeslig = 0.0;
            var dataArray = [];

            // percorre tabela
            $.each(rows, function (i, val) {

                // pega número da saída
                NumSaida = parseInt(val._aData[0]);

                // saida habilitada
                if (val._aData[2] == '---') {
                    Programado = false;
                    NumCtrlGatewaySaida = 0;
                }
                else {
                    Programado = true;
                    NumCtrlGatewaySaida = parseInt(val._aData[2]);
                }

                // nivel desligamento
                valor_str = val._aData[3];
                var ControlAcumDeslig = parseFloat(valor_str.replace(',', '.'));

                dataArray.push({
                    "IDGateway": IDGateway,
                    "Programado": Programado,
                    "NumCtrlGateway": NumCtrlGatewaySaida,
                    "NumSaida": NumSaida,
                    "ControlAcumDeslig": ControlAcumDeslig,
                });

            });

            // retiro campos desabilitados, pois nao envia via POST
            //$(":disabled", $('#form')).removeAttr("disabled");

            // constroi estrutura com FORM e lista
            data = { 'CtrlDemAcum': $form.serializeObject(), 'ctrlSDs': dataArray };
            data2 = JSON.stringify(data);

            $.ajax({
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                url: '/Configuracao/GateX_CtrlDemAcum_Programar',
                data: data2,
                type: 'POST',
                success: function (data) {

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {
                            swal({
                                title: "Erro",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // redireciona para pagina lista de programacoes
                                var IDGateway = document.getElementById('IDGateway').value;
                                var url = '/Configuracao/GateX_CtrlDemAcum?IDGateway=' + IDGateway + '&Origem=2';

                                window.location.href = url;
                            });
                        }
                        else {

                            // redireciona para pagina lista de programacoes
                            var IDGateway = document.getElementById('IDGateway').value;
                            var url = '/Configuracao/GateX_CtrlDemAcum?IDGateway=' + IDGateway + '&Origem=2';

                            window.location.href = url;

                        }

                    }, 100);

                },
                error: function (xhr, status, error) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao Programar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // redireciona para pagina lista de programacoes
                            var IDGateway = document.getElementById('IDGateway').value;
                            var url = '/Configuracao/GateX_CtrlDemAcum?IDGateway=' + IDGateway + '&Origem=2';

                            window.location.href = url;
                        });
                    }, 100);
                },
            });
        };

    </script>
}
