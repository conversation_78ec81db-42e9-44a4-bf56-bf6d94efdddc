﻿@model SmartEnergyLib.SQL.MedicoesDominio

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.Funcoes

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoMedicoes;
}

<style>

    .badge {
        width: 100%;
    }

    .badge-titulo {
        font-size: 20px;
        vertical-align: middle;
    }

    #DEXMA_table table, th, td {
        border: 1px solid black;
        border-collapse: collapse;
    }

    #DEXMA_table th, td {
        padding: 5px;
        text-align: left;
    }
</style>


@{
    int IDTipoAcesso = ViewBag._IDTipoAcesso;
    int IDConsultor = ViewBag._IDConsultor;
}

<div class="wrapper wrapper-content">

    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("Medicoes_Editar", "Configuracao", FormMethod.Post, new { id = "form", role = "form" }))
            {
                @Html.Hidden("IDCliente", Model.IDCliente)
                @Html.Hidden("NumMedicoes", Model.NumMedicoes)
                @Html.Hidden("DataContrato", Model.DataContrato)
                @Html.Hidden("DataICMS", Model.DataICMS)
                @Html.Hidden("IDMedicao", Model.IDMedicao)
                @Html.Hidden("ContratoDemPU", Model.ContratoDemPU)
                @Html.Hidden("ContratoDemPS", Model.ContratoDemPS)
                @Html.Hidden("ContratoDemFPU", Model.ContratoDemFPU)
                @Html.Hidden("ContratoDemFPS", Model.ContratoDemFPS)
                @Html.Hidden("KPI_IDHistVolume", Model.KPI_IDHistVolume)
                @Html.Hidden("KPI_IDHistTemperatura", Model.KPI_IDHistTemperatura)

                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoMedicoes</h4>
                        <div class="pull-right dashboard-tools link_branco" style="margin-top:-8px;">
                            <h4>

                                @{
                                    int IDCliente = ViewBag._IDCliente;

                                    if (IDCliente > 0)
                                    {
                                        <a href='@("/Configuracao/Menu?IDCliente=" + @IDCliente.ToString())' title="Menu @SmartEnergy.Resources.ConfiguracaoTexts.Configuracoes @SmartEnergy.Resources.ConfiguracaoTexts.ClienteAtual"><i class="fa fa-th-large"></i></a>
                                    }
                                }

                            </h4>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="form-group col-lg-2">
                                <label class="control-label">&nbsp;ID</label>

                                @{
                                    var TextoPrecisaSalvar = "";

                                    if (Model.IDMedicao == 0)
                                    {
                                        @Html.Hidden("IDMedicao", Model.IDMedicao)
                                        @Html.TextBox("Novo", "Nova Medição", new { @class = "form-control", @disabled = "disabled" })

                                        TextoPrecisaSalvar = "É necessário salvar a nova medição antes de adicionar o histórico";
                                    }
                                    else
                                    {
                                        @Html.TextBoxFor(model => model.IDMedicao, new { @class = "form-control", @disabled = "disabled" })
                                    }
                                }

                            </div>
                            <div class="col-lg-offset-4 col-lg-6">
                                <div class="ibox-tools">
                                    <a href='@("/Configuracao/Medicoes?IDCliente=" + @Model.IDCliente)' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>

                                    @{
                                        if (Model.IDMedicao == 0)
                                        {
                                            <a data-toggle="modal" href="#modalSalvar" class="btn btn-primary">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</a>
                                        }
                                        else
                                        {
                                            <button type="button" class="btn btn-primary" onclick="Salvar();" disabled="" id="BotaoSalvar">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                        }
                                    }

                                </div>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="tabs-container">
                                    <ul class="nav nav-tabs">
                                        <li class="active"><a data-toggle="tab" href="#tab-1"><i class="fa fa-cogs"></i> @SmartEnergy.Resources.ConfiguracaoTexts.Geral</a></li>
                                        <li class="tab-2"><a data-toggle="tab" href="#tab-2"><i class="fa fa-pie-chart"></i> @SmartEnergy.Resources.ConfiguracaoTexts.Faturamento</a></li>
                                        <li class="tab-3"><a data-toggle="tab" href="#tab-3"><i class="fa fa-bell"></i> @SmartEnergy.Resources.ConfiguracaoTexts.Gerenciamento</a></li>
                                        <li class="tab-4"><a data-toggle="tab" href="#tab-4"><i class="fa fa-key"></i> KPI</a></li>
                                        <li class="tab-5"><a data-toggle="tab" href="#tab-5"><i class="fa fa-tasks"></i> @SmartEnergy.Resources.ConfiguracaoTexts.Personalizacao</a></li>
                                        <li class="tab-6"><a data-toggle="tab" href="#tab-6"><i class="fa fa-upload"></i> DEXMA API</a></li>
                                    </ul>
                                    <div class="tab-content">
                                        <div id="tab-1" class="tab-pane active">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.TipoMedicao</label>
                                                        @Html.DropDownListFor(model => model.IDTipoMedicao, new SelectList(ViewBag.listaTipoMedicao, "ID", "Descricao"),
                                                             string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.TipoMedicao),
                                                             new { @class = "form-control", @onchange = "SelecionouTipoMedicao()", @disabled = "disabled" })
                                                    </div>

                                                    <div class="div_energia" style="display:none;">
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.UnidadeConsumidora</label>
                                                            @Html.DropDownListFor(model => model.IDCategoriaMedicao, new SelectList(ViewBag.listaTipoCategoriaMedicao, "ID", "Descricao"),
                                                                 string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.UnidadeConsumidora),
                                                                 new { @class = "form-control", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                </div>
                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-6">

                                                        @{
                                                            // somente CPFL
                                                            if (IDConsultor == CLIENTES_ESPECIAIS.CPFL)
                                                            {
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.NomeAtivo</label>
                                                            }
                                                            else
                                                            {
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Nome</label>
                                                            }
                                                        }

                                                        @Html.TextBoxFor(model => model.Nome, new { @class = "form-control", @maxlength = "50", @disabled = "disabled" })
                                                    </div>

                                                    <div class="div_referencia" style="display:none;">
                                                        <div class="form-group col-lg-2">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Referencia</label>
                                                            @Html.TextBoxFor(model => model.Referencia, new { @class = "form-control", @maxlength = "50", @disabled = "disabled" })
                                                        </div>
                                                    </div>

                                                    <div class="div_cpfl" style="display:none;">
                                                        <div class="form-group col-lg-2">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.TipoSubMedicao</label>
                                                            @Html.DropDownListFor(model => model.IDTipoSubMedicao, new SelectList(ViewBag.listaTipoSubMedicao, "ID", "Descricao"),
                                                                 string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.TipoSubMedicao),
                                                                 new { @class = "form-control", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-2">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Classe</label>
                                                            @Html.DropDownListFor(model => model.IDTipoClasseAtivo, new SelectList(ViewBag.listaTipoClasseAtivo, "ID", "Descricao"),
                                                                string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.Classe),
                                                                new { @class = "form-control", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                </div>
                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@Funcoes_SmartEnergy.Nome_Unidades(ViewBag.Nome_Unidades, SmartEnergy.Resources.ConfiguracaoTexts.Unidade)</label>
                                                        @Html.DropDownListFor(model => model.IDUnidade, new SelectList(ViewBag.listaUnidades, "IDUnidade", "Nome").OrderBy(x => x.Text),
                                                            string.Format("{0}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione),
                                                             new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="div_referencia" style="display:none;">
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.CodigoInstalacao</label>
                                                            @Html.TextBoxFor(model => model.CodigoInstalacao, new { @class = "form-control", @maxlength = "30", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                    <div class="div_referencia" style="display:none;">
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.PontoMedicao</label>
                                                            @Html.TextBoxFor(model => model.PontoMedicao, new { @class = "form-control", @maxlength = "100", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                </div>
                                                <br />
                                                <div class="row">
                                                    <div class="div_gateway" style="display:none;">
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;Gateway</label>
                                                            @Html.DropDownListFor(model => model.IDGateway, new SelectList(ViewBag.listaGateways, "IDGateway", "Nome").OrderBy(x => x.Text),
                                                                string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, "Gateway"),
                                                                new { @class = "form-control", @onchange = "SelecionouGateway()", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-2">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.NumeroMedicao</label>
                                                            @Html.TextBoxFor(model => model.NumMedGateway, new { @class = "form-control", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                    <div class="div_formula" style="display:none;">
                                                        <div class="form-group col-lg-12">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Formula</label>
                                                            @Html.TextBoxFor(model => model.Formula, new { @class = "form-control", @maxlength = "200", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                    <div class="div_estado_cidade" style="display:none;">
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Estado</label>
                                                            @Html.DropDownListFor(model => model.IDEstado, new SelectList(ViewBag.listaTipoEstado, "IDEstado", "Nome"),
                                                                string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.Estado),
                                                                    new { @class = "form-control", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Cidade</label>
                                                            @Html.DropDownListFor(model => model.IDCidade, new SelectList(ViewBag.listaTipoCidade, "IDCidade", "Nome"),
                                                                string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.Cidade),
                                                                    new { @class = "form-control", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="div_util_analogicas" style="display:none;">
                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.NomeGrandeza</label>
                                                            @Html.TextBoxFor(model => model.NomeGrandeza, new { @class = "form-control", @maxlength = "50", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.UnidadeGrandeza</label>
                                                            @Html.TextBoxFor(model => model.UnidadeGrandeza, new { @class = "form-control", @maxlength = "50", @disabled = "disabled" })
                                                        </div>
                                                    </div>

                                                    <div class="div_utilidades" style="display:none;">
                                                        <br />
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Icone</label>
                                                                @Html.DropDownListFor(model => model.IDIcone, new SelectList(ViewBag.listaTipoIcone, "IDIcone", "Descricao"),
                                                                    string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.Icone),
                                                                    new { @class = "form-control select2_tipoicone", @disabled = "disabled" })
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="div_ciclometro" style="display:none;">
                                                        <br />
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.IntervaloGrandeza</label>
                                                                @Html.DropDownListFor(model => model.IntervaloGrandeza, new SelectList(ViewBag.listatiposIntervaloGrandeza, "ID", "Descricao"),
                                                                    string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.IntervaloGrandeza),
                                                                    new { @class = "form-control", @disabled = "disabled" })
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.FundoEscala</label>
                                                                @Html.TextBoxFor(model => model.FEGrandeza, new { @class = "form-control", @disabled = "disabled" })
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="div_util_ciclo" style="display:none;">
                                                        <br />
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.GrupoTarifas</label>
                                                                @Html.DropDownListFor(model => model.IDGrupoTarifaUtil, new SelectList(ViewBag.listaTarifasUtilGrupos, "IDGrupoTarifas", "Nome").OrderBy(x => x.Text),
                                                                    "Não emite Fatura",
                                                                    new { @class = "form-control", @disabled = "disabled" })
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>

                                        <div id="tab-2" class="tab-pane">
                                            <div class="panel-body">
                                                <div class="div_energia" style="display:none;">
                                                    <div class="row">
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.TipoContrato</label>
                                                            @Html.DropDownListFor(model => model.IDContratoMedicao, new SelectList(ViewBag.listaTipoContratoMedicao, "ID", "Descricao"),
                                                                string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.TipoContrato),
                                                                new { @class = "form-control", @onchange = "SelecionouTipoContrato()", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.EstruturaTarifaria</label>
                                                            @Html.Hidden("IDEstruturaTarifaria", Model.IDEstruturaTarifaria)
                                                            @Html.Hidden("IDTipoSubgrupo", Model.IDTipoSubgrupo)
                                                            @Html.DropDownListFor(model => model.IDSubgrupo, new SelectList(ViewBag.listaTipoEstruturaTarifaria, "IDSubgrupo", "Descricao_Estrutura_Subgrupo"),
                                                                string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.EstruturaTarifaria),
                                                                new { @class = "form-control", @onchange = "SelecionouEstruturaTarifaria()", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.SubSistema</label>
                                                            @Html.DropDownListFor(model => model.IDSubSistema, new SelectList(ViewBag.listaTipoSubSistema, "ID", "Descricao"),
                                                                string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.SubSistema),
                                                                new { @class = "form-control", @disabled = "disabled" })
                                                        </div>
                                                    </div>

                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Distribuidora</label>
                                                            @Html.DropDownListFor(model => model.IDAgenteDistribuidora, new SelectList(ViewBag.listaTipoDistribuidora, "ID", "Descricao").OrderBy(x => x.Text),
                                                                string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.Distribuidora),
                                                                new { @class = "form-control", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ComumTexts.BotaoHistorico @SmartEnergy.Resources.MenuTexts.MenuLateralTarifasMercadoLivre</label>
                                                            @Html.DropDownListFor(model => model.IDGrupoTarifaMercadoLivre, new SelectList(ViewBag.listaGruposTarifaMercadoLivre, "IDGrupoTarifas", "Nome").OrderBy(x => x.Text),
                                                                string.Format("{0}", SmartEnergy.Resources.ConfiguracaoTexts.UtilizarTarifaMercadoLivre),
                                                                new { @class = "form-control", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;Gateway @SmartEnergy.Resources.MenuTexts.MenuLateralFechamentos</label>
                                                            @Html.DropDownListFor(model => model.IDGateway_Fechamentos, new SelectList(ViewBag.listaGateways, "IDGateway", "Nome").OrderBy(x => x.Text),
                                                                "Gateway desta Medição possui os Fechamentos de Fatura",
                                                                new { @class = "form-control", @disabled = "disabled" })
                                                        </div>
                                                    </div>

                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-12">
                                                            <label class="badge badge-primary">
                                                                <span class="badge-titulo align-middle">&nbsp;&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Demanda&nbsp;&nbsp;</span>
                                                            </label>
                                                        </div>
                                                    </div>

                                                    <div class="row">
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.RegraDem</label>
                                                            @Html.DropDownListFor(model => model.IDRegraDem, new SelectList(ViewBag.listaTipoRegraDem, "ID", "Descricao"),
                                                                string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.RegraDem),
                                                                new { @class = "form-control", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.RegraReativo</label>
                                                            @Html.DropDownListFor(model => model.IDRegraReativo, new SelectList(ViewBag.listaTipoRegraReativo, "ID", "Descricao"),
                                                                string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.RegraReativo),
                                                                new { @class = "form-control", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-2">
                                                            <label class="control-label">&nbsp;</label><br />
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Ponta</label>
                                                        </div>
                                                        <div class="form-group col-lg-2">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Inicio</label>
                                                            @Html.TextBoxFor(model => model.InicioP, new { @class = "form-control", data_mask = "99:99", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-2">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Fim</label>
                                                            @Html.TextBoxFor(model => model.FimP, new { @class = "form-control", data_mask = "99:99", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.DiasSemana</label><br />
                                                            <div class="i-checks" style="margin-top:6px;">
                                                                @Html.CheckBoxFor(model => model.DomP)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.dom</span>
                                                                @Html.CheckBoxFor(model => model.SegP)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.seg</span>
                                                                @Html.CheckBoxFor(model => model.TerP)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.ter</span>
                                                                @Html.CheckBoxFor(model => model.QuaP)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.qua</span>
                                                                @Html.CheckBoxFor(model => model.QuiP)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.qui</span>
                                                                @Html.CheckBoxFor(model => model.SexP)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.sex</span>
                                                                @Html.CheckBoxFor(model => model.SabP)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.sab</span>
                                                                @Html.CheckBoxFor(model => model.FerP)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.fer</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="form-group col-lg-2">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.PostoCapacitivo</label>
                                                        </div>
                                                        <div class="form-group col-lg-2">
                                                            @Html.TextBoxFor(model => model.InicioPC, new { @class = "form-control", data_mask = "99:99", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-2">
                                                            @Html.TextBoxFor(model => model.FimPC, new { @class = "form-control", data_mask = "99:99", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-6">
                                                            <div class="i-checks" style="margin-top:6px;">
                                                                @Html.CheckBoxFor(model => model.DomPC)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.dom</span>
                                                                @Html.CheckBoxFor(model => model.SegPC)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.seg</span>
                                                                @Html.CheckBoxFor(model => model.TerPC)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.ter</span>
                                                                @Html.CheckBoxFor(model => model.QuaPC)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.qua</span>
                                                                @Html.CheckBoxFor(model => model.QuiPC)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.qui</span>
                                                                @Html.CheckBoxFor(model => model.SexPC)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.sex</span>
                                                                @Html.CheckBoxFor(model => model.SabPC)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.sab</span>
                                                                @Html.CheckBoxFor(model => model.FerPC)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.fer</span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <br />
                                                    <div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-2">
                                                                <label class="control-label">&nbsp;</label><br />
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ToleranciaDemanda (%)</label>
                                                            </div>
                                                            <div class="form-group col-lg-2">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.P</label>
                                                                @Html.TextBoxFor(model => model.ToleranciaDemP, new { @class = "form-control", @maxlength = "2", @disabled = "disabled" })
                                                            </div>
                                                            <div class="form-group col-lg-2">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.FPonta</label>
                                                                @Html.TextBoxFor(model => model.ToleranciaDemFP, new { @class = "form-control", @maxlength = "2", @disabled = "disabled" })
                                                            </div>
                                                        </div>
                                                    </div>

                                                    @{
                                                        if (Model.IDMedicao == 0)
                                                        {
                                                            <div class="row">
                                                                <div class="form-group col-lg-4">
                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Data @SmartEnergy.Resources.ConfiguracaoTexts.Contrato</label>
                                                                    <div class="input-group date">
                                                                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>@Html.TextBoxFor(model => model.DataContratoTexto, new { @class = "form-control", @maxlength = "10" })
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div class="row">
                                                                <div class="form-group col-lg-4">
                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ContratoP (@ViewBag.UnidadeDemanda)</label>
                                                                    @Html.TextBoxFor(model => model.ContratoDemP, new { @class = "form-control", @maxlength = "6" })
                                                                </div>
                                                                <div class="form-group col-lg-4">
                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ContratoFP (@ViewBag.UnidadeDemanda)</label>
                                                                    @Html.TextBoxFor(model => model.ContratoDemFP, new { @class = "form-control", @maxlength = "6" })
                                                                </div>
                                                            </div>
                                                        }
                                                        else
                                                        {
                                                            @Html.Hidden("DataContratoTexto", Model.DataContratoTexto)

                                                            <div class="row">
                                                                <div class="form-group col-lg-2">
                                                                    <label class="control-label">&nbsp;Contrato Atual (kW)</label>
                                                                </div>
                                                                <div class="form-group col-lg-2">
                                                                    @Html.TextBoxFor(model => model.ContratoDemP, new { @class = "form-control", @maxlength = "6", @disabled = "disabled" })
                                                                </div>
                                                                <div class="form-group col-lg-2">
                                                                    @Html.TextBoxFor(model => model.ContratoDemFP, new { @class = "form-control", @maxlength = "6", @disabled = "disabled" })
                                                                </div>
                                                            </div>
                                                            <br />
                                                            <div class="row">
                                                                <div class="form-group col-lg-10">

                                                                    <table class="table table-striped table-bordered table-hover dataTables-contratos">
                                                                        <thead>
                                                                            <tr>
                                                                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.Data</th>
                                                                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.ContratoP (@ViewBag.UnidadeDemanda)</th>
                                                                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.ContratoFP (@ViewBag.UnidadeDemanda)</th>
                                                                                <th></th>
                                                                            </tr>
                                                                        </thead>
                                                                        <tbody>

                                                                            @{
                                                                                if (ViewBag.HistoricoContratos != null)
                                                                                {
                                                                                    foreach (HistoricoContratosDominio contrato in ViewBag.HistoricoContratos)
                                                                                    {
                                                                                        // copia data e hora
                                                                                        var DataHora = String.Format("{0:d}", contrato.Data);
                                                                                        var DataHora_Sort = String.Format("{0:yyyyMMddHHmmss}", contrato.Data);

                                                                                        <tr>
                                                                                            <td><span style="display:none;">@DataHora_Sort</span>@DataHora</td>
                                                                                            <td>@contrato.ContratoDemP</td>
                                                                                            <td>@contrato.ContratoDemFP</td>
                                                                                            <td class="link_preto">
                                                                                                <a href='@("/Configuracao/HistoricoContratos_Editar?IDContrato=" + @contrato.IDContrato.ToString() + "&IDMedicao=" + @contrato.IDMedicao.ToString())' title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"><i class="fa fa-edit icones"></i></a>

                                                                                                @if (ViewBag.Permissao == PERMISSOES.ADMIN || ViewBag.Permissao == PERMISSOES.SUPORTE || ViewBag.Permissao == PERMISSOES.CONSULTOR || ViewBag.Permissao == PERMISSOES.SUPER_CONSULTOR)
                                                                                                {
                                                                                                    <a href="#" onclick="javascript:Excluir_HistoricoContrato(@contrato.IDContrato, '@contrato.DataTexto');" id="BotaoExcluir" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-times icones"></i></a>
                                                                                                }

                                                                                            </td>
                                                                                        </tr>
                                                                                    }
                                                                                }
                                                                            }

                                                                        </tbody>
                                                                    </table>
                                                                </div>

                                                                <div class="form-group col-lg-2">
                                                                    <h4>
                                                                        <button title="@TextoPrecisaSalvar" type="button" class="btn btn-info" style="width:100%;" id="BotaoAdicionarContrato" onclick="location.href='@("/Configuracao/HistoricoContratos_Editar?IDContrato=0&IDMedicao=" + @Model.IDMedicao.ToString())'" disabled="">@SmartEnergy.Resources.ComumTexts.BotaoInserir @SmartEnergy.Resources.ConfiguracaoTexts.Contrato</button>
                                                                    </h4>
                                                                </div>

                                                            </div>

                                                        }
                                                    }

                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-12">
                                                            <label class="badge badge-primary">
                                                                <span class="badge-titulo align-middle">&nbsp;&nbsp;ICMS&nbsp;&nbsp;</span>
                                                            </label>
                                                        </div>
                                                    </div>

                                                    <div class="row">
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.RegraICMS</label>
                                                            @Html.DropDownListFor(model => model.IDRegraICMS, new SelectList(ViewBag.listaTipoRegraICMS, "ID", "Descricao"),
                                                                string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.RegraICMS),
                                                                new { @class = "form-control", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.CalculoICMS</label>
                                                            @Html.DropDownListFor(model => model.IDCalculoICMS, new SelectList(ViewBag.listaTipoICMS, "ID", "Descricao"),
                                                                string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.CalculoICMS),
                                                                new { @class = "form-control", @disabled = "disabled" })
                                                        </div>
                                                    </div>

                                                    <br />

                                                    @{
                                                        if (Model.IDMedicao == 0)
                                                        {
                                                            @Html.Hidden("ICMS_padrao", 18)

                                                            <div class="row">
                                                                <div class="form-group col-lg-4">
                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Data ICMS</label>
                                                                    <div class="input-group date">
                                                                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>@Html.TextBoxFor(model => model.DataICMSTexto, new { @class = "form-control", @maxlength = "10" })
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div class="row">
                                                                <div class="form-group col-lg-4">
                                                                    <label class="control-label">&nbsp;ICMS (%)</label>
                                                                    @Html.TextBoxFor(model => model.valorICMS, new { @class = "form-control", @maxlength = "5" })
                                                                </div>

                                                                <div class="form-group col-lg-4">
                                                                    <label class="control-label">&nbsp;</label><br />
                                                                    <button type="button" class="btn btn-primary" onclick="UsarICMSPadrao();" id="BotaoICMSPadrao">@ViewBag.Nome_Estado</button>
                                                                </div>
                                                            </div>
                                                        }
                                                        else
                                                        {
                                                            @Html.Hidden("DataICMSTexto", Model.DataICMS)
                                                            @Html.Hidden("valorICMS", Model.valorICMS)

                                                            <div class="row">
                                                                <div class="form-group col-lg-10">

                                                                    <table class="table table-striped table-bordered table-hover dataTables-icms">
                                                                        <thead>
                                                                            <tr>
                                                                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.Data</th>
                                                                                <th>ICMS</th>
                                                                                <th></th>
                                                                            </tr>
                                                                        </thead>
                                                                        <tbody>

                                                                            @{
                                                                                if (ViewBag.HistoricoICMS != null)
                                                                                {
                                                                                    foreach (HistoricoICMSDominio icms in ViewBag.HistoricoICMS)
                                                                                    {
                                                                                        // copia data e hora
                                                                                        var DataHora = String.Format("{0:d}", icms.Data);
                                                                                        var DataHora_Sort = String.Format("{0:yyyyMMddHHmmss}", icms.Data);

                                                                                        <tr>
                                                                                            <td><span style="display:none;">@DataHora_Sort</span>@DataHora</td>
                                                                                            <td>@string.Format("{0:0.00}", icms.valorICMS)</td>
                                                                                            <td class="link_preto">
                                                                                                <a href='@("/Configuracao/HistoricoICMS_Editar?IDICMS=" + @icms.IDICMS.ToString() + "&IDMedicao=" + @icms.IDMedicao.ToString())' title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"><i class="fa fa-edit icones"></i></a>

                                                                                                @if (ViewBag.Permissao == PERMISSOES.ADMIN || ViewBag.Permissao == PERMISSOES.SUPORTE || ViewBag.Permissao == PERMISSOES.CONSULTOR || ViewBag.Permissao == PERMISSOES.SUPER_CONSULTOR)
                                                                                                {
                                                                                                    <a href="#" onclick="javascript:Excluir_HistoricoICMS(@icms.IDICMS, '@icms.DataTexto');" id="BotaoExcluir" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-times icones"></i></a>
                                                                                                }

                                                                                            </td>
                                                                                        </tr>
                                                                                    }
                                                                                }
                                                                            }

                                                                        </tbody>
                                                                    </table>
                                                                </div>

                                                                <div class="form-group col-lg-2">
                                                                    <h4>
                                                                        <button type="button" class="btn btn-info" style="width:100%;" id="BotaoAdicionarICMS" onclick="location.href='@("/Configuracao/HistoricoICMS_Editar?IDICMS=0&IDMedicao=" + @Model.IDMedicao.ToString())'" disabled="">@SmartEnergy.Resources.ComumTexts.BotaoInserir ICMS</button>
                                                                    </h4>
                                                                </div>
                                                            </div>

                                                        }
                                                    }

                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-12">
                                                            <label class="badge badge-primary">
                                                                <span class="badge-titulo align-middle">&nbsp;&nbsp;PIS / COFINS&nbsp;&nbsp;</span>
                                                            </label>
                                                        </div>
                                                    </div>

                                                    <div class="row">
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;PIS / COFINS</label>
                                                            @Html.DropDownListFor(model => model.PISCOFINS_Mes, new SelectList(ViewBag.listaPISCOFINS_Mes, "ID", "Descricao"),
                                                                SmartEnergy.Resources.ConfiguracaoTexts.Selecione,
                                                                new { @class = "form-control" })
                                                        </div>
                                                    </div>

                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-12">
                                                            <label class="badge badge-primary">
                                                                <span class="badge-titulo align-middle">&nbsp;&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.RuralIrrigantes&nbsp;&nbsp;</span>
                                                            </label>
                                                        </div>
                                                    </div>

                                                    <div class="row">
                                                        <div class="form-group col-lg-2">
                                                            <label class="control-label">&nbsp;</label><br />
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.RuralIrrigantes</label>
                                                        </div>
                                                        <div class="form-group col-lg-2">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Inicio</label>
                                                            @Html.TextBoxFor(model => model.InicioR, new { @class = "form-control", data_mask = "99:99", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-2">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Fim</label>
                                                            @Html.TextBoxFor(model => model.FimR, new { @class = "form-control", data_mask = "99:99", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.DiasSemana</label><br />
                                                            <div class="i-checks" style="margin-top:6px;">
                                                                @Html.CheckBoxFor(model => model.DomR)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.dom</span>
                                                                @Html.CheckBoxFor(model => model.SegR)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.seg</span>
                                                                @Html.CheckBoxFor(model => model.TerR)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.ter</span>
                                                                @Html.CheckBoxFor(model => model.QuaR)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.qua</span>
                                                                @Html.CheckBoxFor(model => model.QuiR)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.qui</span>
                                                                @Html.CheckBoxFor(model => model.SexR)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.sex</span>
                                                                @Html.CheckBoxFor(model => model.SabR)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.sab</span>
                                                                @Html.CheckBoxFor(model => model.FerR)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.fer</span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-10">

                                                            <table class="table table-striped table-bordered table-hover dataTables-rural">
                                                                <thead>
                                                                    <tr>
                                                                        <th>@SmartEnergy.Resources.ConfiguracaoTexts.Data</th>
                                                                        <th>@SmartEnergy.Resources.ConfiguracaoTexts.DescontoRural</th>
                                                                        <th>@SmartEnergy.Resources.ConfiguracaoTexts.DescontoIrrigantes</th>
                                                                        <th></th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>

                                                                    @{
                                                                        if (ViewBag.HistoricoRural != null)
                                                                        {
                                                                            foreach (HistoricoRuralDominio rural in ViewBag.HistoricoRural)
                                                                            {
                                                                                // copia data e hora
                                                                                var DataHora = String.Format("{0:d}", rural.Data);
                                                                                var DataHora_Sort = String.Format("{0:yyyyMMddHHmmss}", rural.Data);

                                                                                <tr>
                                                                                    <td><span style="display:none;">@DataHora_Sort</span>@DataHora</td>
                                                                                    <td>@string.Format("{0:0.00}", rural.DescontoRural)</td>
                                                                                    <td>@string.Format("{0:0.00}", rural.DescontoIrrigantes)</td>
                                                                                    <td class="link_preto">
                                                                                        <a href='@("/Configuracao/HistoricoRural_Editar?IDRural=" + @rural.IDRural.ToString() + "&IDMedicao=" + @rural.IDMedicao.ToString())' title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"><i class="fa fa-edit icones"></i></a>

                                                                                        @if (ViewBag.Permissao == PERMISSOES.ADMIN || ViewBag.Permissao == PERMISSOES.SUPORTE || ViewBag.Permissao == PERMISSOES.CONSULTOR || ViewBag.Permissao == PERMISSOES.SUPER_CONSULTOR)
                                                                                        {
                                                                                            <a href="#" onclick="javascript:Excluir_HistoricoRural(@rural.IDRural, '@rural.DataTexto');" id="BotaoExcluir" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-times icones"></i></a>
                                                                                        }

                                                                                    </td>
                                                                                </tr>
                                                                            }
                                                                        }
                                                                    }

                                                                </tbody>
                                                            </table>
                                                        </div>

                                                        <div class="form-group col-lg-2">
                                                            <h4>
                                                                <button type="button" class="btn btn-info" style="width:100%;" id="BotaoAdicionarRural" onclick="location.href='@("/Configuracao/HistoricoRural_Editar?IDRural=0&IDMedicao=" + @Model.IDMedicao.ToString())'" disabled="">@SmartEnergy.Resources.ComumTexts.BotaoInserir @SmartEnergy.Resources.ConfiguracaoTexts.RuralIrrigantes </button>
                                                            </h4>
                                                        </div>
                                                    </div>

                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-12">
                                                            <label class="badge badge-primary">
                                                                <span class="badge-titulo align-middle">&nbsp;&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.BeneficioSazonalidade&nbsp;&nbsp;</span>
                                                            </label>
                                                        </div>
                                                    </div>

                                                    <div class="row">
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.InicioVigencia</label>
                                                            @Html.DropDownListFor(model => model.IDTipoSazonalidade, new SelectList(ViewBag.listaTipoSazonalidade, "ID", "Descricao"),
                                                                 new { @class = "form-control", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div id="tab-3" class="tab-pane">
                                            <div class="panel-body">
                                                <div class="div_mensagem_geren" style="display:none;">
                                                    <div class="row">
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.AnaliseHorarioCons</label>
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.HoraIni</label>
                                                            @Html.TextBoxFor(model => model.Funcionamento_Inicio, new { @class = "form-control", data_mask = "99:99", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.HoraFim</label>
                                                            @Html.TextBoxFor(model => model.Funcionamento_Fim, new { @class = "form-control", data_mask = "99:99", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.DemandaResidual (kW)</label>
                                                            @Html.TextBoxFor(model => model.Funcionamento_DemMin, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                    <hr />
                                                    <div class="row">
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.DemandaAlta - @SmartEnergy.Resources.ConfiguracaoTexts.P (%)</label>
                                                            @Html.TextBoxFor(model => model.Dem_P_pcH, new { @class = "form-control", @maxlength = "5", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.FPonta (%)</label>
                                                            @Html.TextBoxFor(model => model.Dem_F_pcH, new { @class = "form-control", @maxlength = "5", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.DemandaBaixa - @SmartEnergy.Resources.ConfiguracaoTexts.P (%)</label>
                                                            @Html.TextBoxFor(model => model.Dem_P_pcL, new { @class = "form-control", @maxlength = "5", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.FPonta (%)</label>
                                                            @Html.TextBoxFor(model => model.Dem_F_pcL, new { @class = "form-control", @maxlength = "5", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                    <hr />
                                                    <div class="row">
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.FatPot - @SmartEnergy.Resources.ConfiguracaoTexts.LimiteInd</label>
                                                            @Html.TextBoxFor(model => model.FP_LimInd, new { @class = "form-control", @maxlength = "5", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.FatPot - @SmartEnergy.Resources.ConfiguracaoTexts.LimiteCap</label>
                                                            @Html.TextBoxFor(model => model.FP_LimCap, new { @class = "form-control", @maxlength = "5", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                    <hr />
                                                    <div class="row">
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ConsProjAlto (%)</label>
                                                            @Html.TextBoxFor(model => model.CnPjP_pcH, new { @class = "form-control", @maxlength = "5", @disabled = "disabled" })
                                                        </div>

                                                        @Html.Hidden("CnPjF_pcH", Model.CnPjF_pcH)

                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ConsProjBaixo (%)</label>
                                                            @Html.TextBoxFor(model => model.CnPjP_pcL, new { @class = "form-control", @maxlength = "5", @disabled = "disabled" })
                                                        </div>

                                                        @Html.Hidden("CnPjF_pcL", Model.CnPjF_pcL)

                                                    </div>
                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.DiasSem @SmartEnergy.Resources.ConfiguracaoTexts.Projecao</label>
                                                            @Html.TextBoxFor(model => model.DiasNCnPj, new { @class = "form-control", @maxlength = "5", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div id="tab-4" class="tab-pane">
                                            <div class="panel-body">
                                                <div class="div_kpi" style="display:none;">
                                                    <div class="row">
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.RefConsumo (@ViewBag.UnidadeConsumo)</label>
                                                            @Html.TextBoxFor(model => model.KPI_RefConsumo, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-2">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Inicio</label>
                                                            @Html.TextBoxFor(model => model.KPI_RefConsumo_Inicio, new { @class = "form-control", data_mask = "99:99", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-2">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Fim</label>
                                                            @Html.TextBoxFor(model => model.KPI_RefConsumo_Fim, new { @class = "form-control", data_mask = "99:99", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-8">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.DiasSemana</label><br />
                                                            <div class="i-checks" style="margin-top:6px;">
                                                                @Html.CheckBoxFor(model => model.KPI_RefConsumo_Dom)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.dom</span>
                                                                @Html.CheckBoxFor(model => model.KPI_RefConsumo_Seg)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.seg</span>
                                                                @Html.CheckBoxFor(model => model.KPI_RefConsumo_Ter)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.ter</span>
                                                                @Html.CheckBoxFor(model => model.KPI_RefConsumo_Qua)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.qua</span>
                                                                @Html.CheckBoxFor(model => model.KPI_RefConsumo_Qui)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.qui</span>
                                                                @Html.CheckBoxFor(model => model.KPI_RefConsumo_Sex)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.sex</span>
                                                                @Html.CheckBoxFor(model => model.KPI_RefConsumo_Sab)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.sab</span>
                                                                @Html.CheckBoxFor(model => model.KPI_RefConsumo_Fer)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.fer</span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <hr />
                                                    <div class="row">
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.PotenciaInstalada (@ViewBag.UnidadeDemanda)</label>
                                                            @Html.TextBoxFor(model => model.KPI_PotenciaInstalada, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                        </div>
                                                    </div>

                                                    <hr />
                                                    <div class="row">
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Area (m²)</label>
                                                            @Html.TextBoxFor(model => model.KPI_Area, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                        </div>
                                                    </div>

                                                    <br />
                                                </div>
                                            </div>
                                        </div>

                                        <div id="tab-5" class="tab-pane">
                                            <div class="panel-body">
                                                <div class="div_personalizacao" style="display:none;">
                                                    <div class="row">
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.TipoUnidadePotencia</label>
                                                            @Html.DropDownListFor(model => model.IDTipoUnidadePotencia, new SelectList(ViewBag.listaTipoUnidadePotencia, "ID", "Descricao"),
                                                                    string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.TipoUnidadePotencia),
                                                                    new { @class = "form-control", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.InicioDia</label>
                                                            @Html.DropDownListFor(model => model.IDDeslocamento, new SelectList(ViewBag.listaDeslocamento, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.InicioDia),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                        </div>

                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.TipoCicloMes</label>
                                                            @Html.DropDownListFor(model => model.IDCicloMes, new SelectList(ViewBag.listaTipoCicloMes, "ID", "Descricao"),
                                                                string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.TipoCicloMes),
                                                                new { @class = "form-control", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div id="tab-6" class="tab-pane">
                                            <div class="panel-body">
                                                <div class="div_DEXMA">
                                                    <div class="row">
                                                        <div class="form-group col-lg-6">
                                                            <div class="i-checks" style="margin-top:6px;">
                                                                @Html.CheckBoxFor(model => model.DEXMA_Habilita)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ConfiguracaoTexts.Habilitar</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;Key</label>
                                                            @Html.TextBoxFor(model => model.DEXMA_Key, new { @class = "form-control", @maxlength = "30", @disabled = "disabled" })
                                                        </div>
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;Token</label>
                                                            @Html.TextBoxFor(model => model.DEXMA_Token, new { @class = "form-control", @maxlength = "50", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;Device ID (did)</label>
                                                            @Html.TextBoxFor(model => model.DEXMA_did, new { @class = "form-control", @maxlength = "30", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-6">
                                                            <h3>Estes campos autenticam a medição no DEXMA API</h3>
                                                            <br />
                                                            <p>Serão enviados os seguintes dados:</p>

                                                            <table id="DEXMA_table" style="width:100%;">
                                                                <tr> <td>Demanda Ativa</td>              <td>ID 401</td> <td>Power (W)</td> </tr>
                                                                <tr> <td>Demanda Reativa Indutiva</td>   <td>ID 403</td> <td>Inductive Reactive Power (VAr)</td> </tr>
                                                                <tr> <td>Demanda Reativa Capacitiva</td> <td>ID 407</td> <td>Capacitive Reactive Power (VAr)</td> </tr>
                                                                <tr> <td>Energia Ativa</td>              <td>ID 402</td> <td>Active Energy (kWh)</td> </tr>
                                                                <tr> <td>Energia Reativa</td>            <td>ID 404</td> <td>Reactive Energy (VArh)</td> </tr>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="overlay_aguarde" style="display: none">
                        <div class="spinner-aguarde">
                            <div class="sk-spinner sk-spinner-wandering-cubes">
                                <div class="sk-spinner sk-spinner-wave">
                                    <div class="sk-rect1 text-ligh"></div>
                                    <div class="sk-rect2"></div>
                                    <div class="sk-rect3"></div>
                                    <div class="sk-rect4"></div>
                                    <div class="sk-rect5"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="modal inmodal animated fadeIn" id="modalSalvar" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                                <h4 class="modal-title">Salvar</h4>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="form-group col-xs-6">
                                        <label class="control-label">&nbsp;Número de Medições a adicionar</label>
                                        <input class="form-control" id="NMed" name="NMed" value="1" />
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group col-xs-12">
                                        Caso adicionar mais de uma medição, o número da medição interna será incrementado automaticamente e o nome da medição apresentará um índice no final.
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                <button type="button" class="btn btn-primary" id="BotaoSalvarModal" onclick="Salvar();">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                            </div>
                        </div>
                    </div>
                </div>
            }

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
    @Styles.Render("~/plugins/select2Styles")
    @Styles.Render("~/datetimePickerStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/plugins/iCheck")
    @Scripts.Render("~/plugins/select2")
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

    $(document).ready(function () {

        // classe iChecks
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });

        $('#DataContratoTexto').datetimepicker({
            locale: 'pt-BR',
            format: 'DD/MM/YYYY',
            showTodayButton: true,
            allowInputToggle: true
        });

        $('#DataICMSTexto').datetimepicker({
            locale: 'pt-BR',
            format: 'DD/MM/YYYY',
            showTodayButton: true,
            allowInputToggle: true
        });

        //
        // VALIDACAO DOS CAMPOS
        //

        jQuery.validator.addMethod("alphanumeric", function (value, element) {
            return this.optional(element) || /^[\.\^\~\´\`áàãâéèêíìóòõôúùçA-Za-z0-9\s&+\-_,()/\\]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

        jQuery.validator.addMethod("numeric", function (value, element) {
            return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.numeric");

        jQuery.validator.addMethod("LimiteMinMax", function (value, element, params) {

            var valor = parseFloat(value.replace(',', '.'));
            var minimo = params[0];
            var maximo = params[1];

            return this.optional(element) || (((valor >= minimo && valor <= maximo) || valor == 0)) && (/^[\\0-9\-,]+$/i.test(value));

        }, "Por favor, forne&ccedil;a um valor entre {0}% e {1}%");

        jQuery.validator.addMethod("LimiteFatPotInd", function (value, element, params) {

            // fator de potência
            var fatpot = parseFloat(value.replace(',', '.'));

            if (fatpot < 0.0)
            {
                fatpot += 2.0;
            }

            // limite inferior
            var minimo = params[0];

            if (minimo < 0.0)
            {
                minimo += 2.0;
            }

            var valido = false;

            if (fatpot == 0.0)
            {
                valido = true;
            }
            else
            {
                // deve ser maior que limite
                if (fatpot <= minimo)
                {
                    valido = true;
                }
            }

            return this.optional(element) || (valido && (/^[\\0-9\-,]+$/i.test(value)));

        }, "Forne&ccedil;a um fator de potência mais indutivo que {0}");

        jQuery.validator.addMethod("LimiteFatPotCap", function (value, element, params) {

            // fator de potência
            var fatpot = parseFloat(value.replace(',', '.'));

            if (fatpot < 0.0)
            {
                fatpot += 2.0;
            }

            // limite inferior
            var minimo = params[0];

            if (minimo < 0.0)
            {
                minimo += 2.0;
            }

            var valido = false;

            if (fatpot == 0.0)
            {
                valido = true;
            }
            else
            {
                // deve ser maior que limite
                if (fatpot >= minimo)
                {
                    valido = true;
                }
            }

            return this.optional(element) || (valido && (/^[\\0-9\-,]+$/i.test(value)));

        }, "Forne&ccedil;a um fator de potência mais capacitivo que {0}");

        jQuery.validator.addMethod("formula", function (value, element) {

            // formula
            var num_med_formula = (value.match(/M/g) || []).length;
            num_med_formula += (value.match(/m/g) || []).length;

            return this.optional(element) || (num_med_formula > 0 && num_med_formula <= 26);

        }, "Excedeu o limite de 26 medições na Fórmula");

        $("#form").validate({
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            }
        });

        $('.dataTables-icms').DataTable({
            "iDisplayLength": 4,
            dom: 'tp',

            "bAutoWidth": false,
            "aoColumnDefs": [
                        {
                            "aTargets": "_all",
                            "sType": "portugues"
                        },
                        {
                            "aTargets": [2],
                            "bVisible": true,
                            "bSortable": false,
                            "bSearchable": false
                        },
            ],

            "aoColumns": [
            { sWidth: "30%" },
            { sWidth: "60%" },
            { sWidth: "10%" }
            ],
            'order': [0, 'desc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

        $('.dataTables-contratos').DataTable({
            "iDisplayLength": 4,
            dom: 'tp',

            "bAutoWidth": false,
            "aoColumnDefs": [
                        {
                            "aTargets": "_all",
                            "sType": "portugues"
                        },
                        {
                            "aTargets": [3],
                            "bVisible": true,
                            "bSortable": false,
                            "bSearchable": false
                        },
            ],

            "aoColumns": [
            { sWidth: "30%" },
            { sWidth: "30%" },
            { sWidth: "30%" },
            { sWidth: "10%" }
            ],
            'order': [0, 'desc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

        $('.dataTables-rural').DataTable({
            "iDisplayLength": 4,
            dom: 'tp',

            "bAutoWidth": false,
            "aoColumnDefs": [
                        {
                            "aTargets": "_all",
                            "sType": "portugues"
                        },
                        {
                            "aTargets": [3],
                            "bVisible": true,
                            "bSortable": false,
                            "bSearchable": false
                        },
            ],

            "aoColumns": [
            { sWidth: "30%" },
            { sWidth: "30%" },
            { sWidth: "30%" },
            { sWidth: "10%" }
            ],
            'order': [0, 'desc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

        // desabilita campos
        disableAll();

        // apresenta campos conforme tipo da medicao
        var model = @Html.Raw(Json.Encode(Model));
        showCampos(model.IDTipoMedicao, model.IDContratoMedicao);


        function formatIcon(icon) {

            if (!icon.id) {
                return icon.text;
            }

            var listaTipoIcone = @Html.Raw(Json.Encode(ViewBag.listaTipoIcone));

            var $icon = $('<span><img style="height:16px;" src="/Imagens/' + listaTipoIcone[icon.id].Imagem + '"/>&nbsp;&nbsp;' + icon.text + '</span>');

            return $icon;

        };


        // selecte icone
        $(".select2_tipoicone").select2({
            minimumResultsForSearch: Infinity,
            width: "100%",
            templateResult: formatIcon,
            templateSelection: formatIcon
        });


        // le cookie
        var url = getCookie('MedicaoEditar_Tab');

        // seleciona tab
        if (url.match('#')) {
            $('.nav-tabs a[href="#' + url.split('#')[1] + '"]').tab('show');
        }

        // salva tab
        $('.nav-tabs a').on('shown.bs.tab', function (e) {

            // salva em cookie
            setCookie('MedicaoEditar_Tab', e.target.hash, null);
        })

        $('form input').on('keypress', function (e) {
            return e.which !== 13;
        });


        //
        // ESTADO / CIDADE
        //

        // caso estado mudar, atualizo cidades
        $('#IDEstado').change(function () {

            // obtem o estado
            var id = $(this).find(":selected").val();

            // chama a Action para popular as cidades
            $.getJSON('/Configuracao/ObterCidades', { IDEstado: id }, function (data) {

                // remove os dados que ja possui das cidades
                $('#IDCidade option').remove();

                // default
                $('#IDCidade').append('<option value="">Selecione Cidade</option>');

                // popula os options com os valores retornados em JSON
                for (var i = 0; i < data.length; i++) {
                    $('#IDCidade').append('<option value="' +
                        data[i].IDCidade + '"> ' +
                        data[i].Nome + '</option>');
                }
            });
        });



    });

    function disableAll() {

        var permissao = Math.ceil(@ViewBag.Permissao);
        var IDConsultor = Math.ceil(@ViewBag._IDConsultor);
        var IDMedicao = parseInt($("#IDMedicao").val());

        // verifica permissao
        switch (permissao) {
            case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
            case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir

                $("#IDTipoMedicao").attr('disabled', false);

                $("#IDGateway").attr('disabled', false);
                $("#NumMedGateway").attr('disabled', false);

                $("#IDGateway_Fechamentos").attr('disabled', false);

                $("#Formula").attr('disabled', false);

                $("#NomeGrandeza").attr('disabled', false);
                $("#UnidadeGrandeza").attr('disabled', false);
                $("#IntervaloGrandeza").attr('disabled', false);
                $("#FEGrandeza").attr('disabled', false);

                $("#IDGrupoTarifaUtil").attr('disabled', false);
                $("#IDIcone").attr('disabled', false);


            case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte

                $("#BotaoSalvar").attr('disabled', false);

                if (IDMedicao == 0)
                {
                    $("#IDGateway").attr('disabled', false);
                }

                $("#IDCategoriaMedicao").attr('disabled', false);

                $("#Nome").attr('disabled', false);
                $("#Referencia").attr('disabled', false);
                $("#IDTipoSubMedicao").attr('disabled', false);
                $("#IDTipoClasseAtivo").attr('disabled', false);
                $("#IDUnidade").attr('disabled', false);

                $("#CodigoInstalacao").attr('disabled', false);
                $("#PontoMedicao").attr('disabled', false);
                $("#IDTipoUnidadePotencia").attr('disabled', false);
                $("#IDDeslocamento").attr('disabled', false);
                $("#IDCicloMes").attr('disabled', false);

                $("#IDContratoMedicao").attr('disabled', false);
                $("#IDSubgrupo").attr('disabled', false);
                $("#IDSubSistema").attr('disabled', false);
                $("#IDRegraDem").attr('disabled', false);
                $("#IDRegraReativo").attr('disabled', false);
                $("#IDRegraICMS").attr('disabled', false);
                $("#IDCalculoICMS").attr('disabled', false);

                $("#IDAgenteDistribuidora").attr('disabled', false);
                $("#IDGrupoTarifaMercadoLivre").attr('disabled', false);
                $("#IDTipoSazonalidade").attr('disabled', false);

                $("#IDEstado").attr('disabled', false);
                $("#IDCidade").attr('disabled', false);

                $("#InicioP").attr('disabled', false);
                $("#FimP").attr('disabled', false);
                $("#DomP").attr('disabled', false);
                $("#SegP").attr('disabled', false);
                $("#TerP").attr('disabled', false);
                $("#QuaP").attr('disabled', false);
                $("#QuiP").attr('disabled', false);
                $("#SexP").attr('disabled', false);
                $("#SabP").attr('disabled', false);
                $("#FerP").attr('disabled', false);

                $("#InicioPC").attr('disabled', false);
                $("#FimPC").attr('disabled', false);
                $("#DomPC").attr('disabled', false);
                $("#SegPC").attr('disabled', false);
                $("#TerPC").attr('disabled', false);
                $("#QuaPC").attr('disabled', false);
                $("#QuiPC").attr('disabled', false);
                $("#SexPC").attr('disabled', false);
                $("#SabPC").attr('disabled', false);
                $("#FerPC").attr('disabled', false);

                $("#InicioR").attr('disabled', false);
                $("#FimR").attr('disabled', false);
                $("#DomR").attr('disabled', false);
                $("#SegR").attr('disabled', false);
                $("#TerR").attr('disabled', false);
                $("#QuaR").attr('disabled', false);
                $("#QuiR").attr('disabled', false);
                $("#SexR").attr('disabled', false);
                $("#SabR").attr('disabled', false);
                $("#FerR").attr('disabled', false);

                if( IDMedicao > 0 )
                {
                    $("#BotaoAdicionarICMS").attr('disabled', false);
                    $("#BotaoAdicionarContrato").attr('disabled', false);
                    $("#BotaoAdicionarRural").attr('disabled', false);
                }

                $("#ToleranciaDemP").attr('disabled', false);
                $("#ToleranciaDemFP").attr('disabled', false);

                $("#Dem_P_pcH").attr('disabled', false);
                $("#Dem_F_pcH").attr('disabled', false);
                $("#Dem_P_pcL").attr('disabled', false);
                $("#Dem_F_pcL").attr('disabled', false);
                $("#FP_LimInd").attr('disabled', false);
                $("#FP_LimCap").attr('disabled', false);
                $("#DiasNCnPj").attr('disabled', false);
                $("#CnPjP_pcH").attr('disabled', false);
                $("#CnPjF_pcH").attr('disabled', false);
                $("#CnPjP_pcL").attr('disabled', false);
                $("#CnPjF_pcL").attr('disabled', false);

                $("#Funcionamento_DemMin").attr('disabled', false);
                $("#Funcionamento_Inicio").attr('disabled', false);
                $("#Funcionamento_Fim").attr('disabled', false);

                $("#KPI_RefConsumo").attr('disabled', false);
                $("#KPI_RefConsumo_Inicio").attr('disabled', false);
                $("#KPI_RefConsumo_Fim").attr('disabled', false);
                $("#KPI_RefConsumo_Dom").attr('disabled', false);
                $("#KPI_RefConsumo_Seg").attr('disabled', false);
                $("#KPI_RefConsumo_Ter").attr('disabled', false);
                $("#KPI_RefConsumo_Qua").attr('disabled', false);
                $("#KPI_RefConsumo_Qui").attr('disabled', false);
                $("#KPI_RefConsumo_Sex").attr('disabled', false);
                $("#KPI_RefConsumo_Sab").attr('disabled', false);
                $("#KPI_RefConsumo_Fer").attr('disabled', false);

                $("#KPI_PotenciaInstalada").attr('disabled', false);
                $("#KPI_Area").attr('disabled', false);
                //$("#KPI_IDHistVolume").attr('disabled', false);
                //$("#KPI_IDHistTemperatura").attr('disabled', false);

                $("#DEXMA_Habilita").attr('disabled', false);
                $("#DEXMA_Key").attr('disabled', false);
                $("#DEXMA_Token").attr('disabled', false);
                $("#DEXMA_did").attr('disabled', false);

                break;

            case PERMISSOES_PRODUCAO:   // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                $("#BotaoSalvar").attr('disabled', false);
                $("#Nome").attr('disabled', false);
                $("#IDUnidade").attr('disabled', false);
                $("#IDTipoMedicao").attr('disabled', false);
                $("#IDCategoriaMedicao").attr('disabled', false);
                $("#IDGateway").attr('disabled', false);
                $("#NumMedGateway").attr('disabled', false);

                $("#DomP").attr('disabled', true);
                $("#SegP").attr('disabled', true);
                $("#TerP").attr('disabled', true);
                $("#QuaP").attr('disabled', true);
                $("#QuiP").attr('disabled', true);
                $("#SexP").attr('disabled', true);
                $("#SabP").attr('disabled', true);
                $("#FerP").attr('disabled', true);

                $("#DomPC").attr('disabled', true);
                $("#SegPC").attr('disabled', true);
                $("#TerPC").attr('disabled', true);
                $("#QuaPC").attr('disabled', true);
                $("#QuiPC").attr('disabled', true);
                $("#SexPC").attr('disabled', true);
                $("#SabPC").attr('disabled', true);
                $("#FerPC").attr('disabled', true);

                $("#DomR").attr('disabled', true);
                $("#SegR").attr('disabled', true);
                $("#TerR").attr('disabled', true);
                $("#QuaR").attr('disabled', true);
                $("#QuiR").attr('disabled', true);
                $("#SexR").attr('disabled', true);
                $("#SabR").attr('disabled', true);
                $("#FerR").attr('disabled', true);

                break;

            case PERMISSOES_CONSULTOR:  // 5 - permissao de Consultor

                $("#BotaoSalvar").attr('disabled', false);

                $("#Nome").attr('disabled', false);
                $("#Referencia").attr('disabled', false);
                $("#IDTipoSubMedicao").attr('disabled', false);
                $("#IDTipoClasseAtivo").attr('disabled', false);
                $("#IDUnidade").attr('disabled', false);
                $("#CodigoInstalacao").attr('disabled', false);
                $("#PontoMedicao").attr('disabled', false);
                $("#IDTipoUnidadePotencia").attr('disabled', false);
                $("#IDDeslocamento").attr('disabled', false);
                $("#IDCicloMes").attr('disabled', false);

                $("#Formula").attr('disabled', false);

                $("#IDContratoMedicao").attr('disabled', false);
                $("#IDTipoClasseAtivo").attr('disabled', false);
                $("#IDSubgrupo").attr('disabled', false);
                $("#IDSubSistema").attr('disabled', false);
                $("#IDRegraDem").attr('disabled', false);
                $("#IDRegraReativo").attr('disabled', false);
                $("#IDRegraICMS").attr('disabled', false);
                $("#IDCalculoICMS").attr('disabled', false);

                $("#IDAgenteDistribuidora").attr('disabled', false);
                $("#IDGrupoTarifaMercadoLivre").attr('disabled', false);
                $("#IDTipoSazonalidade").attr('disabled', false);

                $("#InicioP").attr('disabled', false);
                $("#FimP").attr('disabled', false);
                $("#DomP").attr('disabled', false);
                $("#SegP").attr('disabled', false);
                $("#TerP").attr('disabled', false);
                $("#QuaP").attr('disabled', false);
                $("#QuiP").attr('disabled', false);
                $("#SexP").attr('disabled', false);
                $("#SabP").attr('disabled', false);
                $("#FerP").attr('disabled', false);

                $("#InicioPC").attr('disabled', false);
                $("#FimPC").attr('disabled', false);
                $("#DomPC").attr('disabled', false);
                $("#SegPC").attr('disabled', false);
                $("#TerPC").attr('disabled', false);
                $("#QuaPC").attr('disabled', false);
                $("#QuiPC").attr('disabled', false);
                $("#SexPC").attr('disabled', false);
                $("#SabPC").attr('disabled', false);
                $("#FerPC").attr('disabled', false);

                $("#InicioR").attr('disabled', false);
                $("#FimR").attr('disabled', false);
                $("#DomR").attr('disabled', false);
                $("#SegR").attr('disabled', false);
                $("#TerR").attr('disabled', false);
                $("#QuaR").attr('disabled', false);
                $("#QuiR").attr('disabled', false);
                $("#SexR").attr('disabled', false);
                $("#SabR").attr('disabled', false);
                $("#FerR").attr('disabled', false);

                if( IDMedicao > 0 )
                {
                    $("#BotaoAdicionarICMS").attr('disabled', false);
                    $("#BotaoAdicionarContrato").attr('disabled', false);
                    $("#BotaoAdicionarRural").attr('disabled', false);
                }

                $("#ToleranciaDemP").attr('disabled', false);
                $("#ToleranciaDemFP").attr('disabled', false);

                $("#Dem_P_pcH").attr('disabled', false);
                $("#Dem_F_pcH").attr('disabled', false);
                $("#Dem_P_pcL").attr('disabled', false);
                $("#Dem_F_pcL").attr('disabled', false);
                $("#FP_LimInd").attr('disabled', false);
                $("#FP_LimCap").attr('disabled', false);
                $("#DiasNCnPj").attr('disabled', false);
                $("#CnPjP_pcH").attr('disabled', false);
                $("#CnPjF_pcH").attr('disabled', false);
                $("#CnPjP_pcL").attr('disabled', false);
                $("#CnPjF_pcL").attr('disabled', false);

                $("#Funcionamento_DemMin").attr('disabled', false);
                $("#Funcionamento_Inicio").attr('disabled', false);
                $("#Funcionamento_Fim").attr('disabled', false);

                $("#NomeGrandeza").attr('disabled', false);
                $("#UnidadeGrandeza").attr('disabled', false);
                $("#IntervaloGrandeza").attr('disabled', false);
                $("#FEGrandeza").attr('disabled', false);

                $("#IDGrupoTarifaUtil").attr('disabled', false);
                $("#IDIcone").attr('disabled', false);

                $("#KPI_RefConsumo").attr('disabled', false);
                $("#KPI_RefConsumo_Inicio").attr('disabled', false);
                $("#KPI_RefConsumo_Fim").attr('disabled', false);
                $("#KPI_RefConsumo_Dom").attr('disabled', false);
                $("#KPI_RefConsumo_Seg").attr('disabled', false);
                $("#KPI_RefConsumo_Ter").attr('disabled', false);
                $("#KPI_RefConsumo_Qua").attr('disabled', false);
                $("#KPI_RefConsumo_Qui").attr('disabled', false);
                $("#KPI_RefConsumo_Sex").attr('disabled', false);
                $("#KPI_RefConsumo_Sab").attr('disabled', false);
                $("#KPI_RefConsumo_Fer").attr('disabled', false);

                $("#KPI_PotenciaInstalada").attr('disabled', false);
                $("#KPI_Area").attr('disabled', false);
                //$("#KPI_IDHistVolume").attr('disabled', false);
                //$("#KPI_IDHistTemperatura").attr('disabled', false);

                $("#DEXMA_Habilita").attr('disabled', false);
                $("#DEXMA_Key").attr('disabled', false);
                $("#DEXMA_Token").attr('disabled', false);
                $("#DEXMA_did").attr('disabled', false);

                break;

            default:
            case PERMISSOES_GERENTE:    // 1 - permissao de Gerente: ve tudo e escreve parte
            case PERMISSOES_OPERADOR:   // 2 - permissao de Operador: ve tudo e nao pode escrever

                $("#BotaoSalvar").attr('disabled', true);

                if( IDConsultor <= 0 )
                {
                    $("#BotaoSalvar").attr('disabled', false);
                    $("#Dem_P_pcH").attr('disabled', false);
                    $("#Dem_F_pcH").attr('disabled', false);
                    $("#Dem_P_pcL").attr('disabled', false);
                    $("#Dem_F_pcL").attr('disabled', false);
                    $("#FP_LimInd").attr('disabled', false);
                    $("#FP_LimCap").attr('disabled', false);
                    $("#DiasNCnPj").attr('disabled', false);
                    $("#CnPjP_pcH").attr('disabled', false);
                    $("#CnPjF_pcH").attr('disabled', false);
                    $("#CnPjP_pcL").attr('disabled', false);
                    $("#CnPjF_pcL").attr('disabled', false);
                    $("#Funcionamento_DemMin").attr('disabled', false);
                    $("#Funcionamento_Inicio").attr('disabled', false);
                    $("#Funcionamento_Fim").attr('disabled', false);
                    $("#KPI_RefConsumo").attr('disabled', false);
                    $("#KPI_PotenciaInstalada").attr('disabled', false);
                    $("#KPI_Area").attr('disabled', false);
                    //$("#KPI_IDHistVolume").attr('disabled', false);
                    //$("#KPI_IDHistTemperatura").attr('disabled', false);
                }

                $("#DomP").attr('disabled', true);
                $("#SegP").attr('disabled', true);
                $("#TerP").attr('disabled', true);
                $("#QuaP").attr('disabled', true);
                $("#QuiP").attr('disabled', true);
                $("#SexP").attr('disabled', true);
                $("#SabP").attr('disabled', true);
                $("#FerP").attr('disabled', true);

                $("#DomPC").attr('disabled', true);
                $("#SegPC").attr('disabled', true);
                $("#TerPC").attr('disabled', true);
                $("#QuaPC").attr('disabled', true);
                $("#QuiPC").attr('disabled', true);
                $("#SexPC").attr('disabled', true);
                $("#SabPC").attr('disabled', true);
                $("#FerPC").attr('disabled', true);

                $("#DomR").attr('disabled', true);
                $("#SegR").attr('disabled', true);
                $("#TerR").attr('disabled', true);
                $("#QuaR").attr('disabled', true);
                $("#QuiR").attr('disabled', true);
                $("#SexR").attr('disabled', true);
                $("#SabR").attr('disabled', true);
                $("#FerR").attr('disabled', true);

                break;
        }
    }

    function showCampos(IDTipoMedicao, IDContratoMedicao) {

        var permissao = Math.ceil(@ViewBag.Permissao);
        var IDConsultor = Math.ceil(@ViewBag._IDConsultor);

        // verifica tipo da medicao
        switch(IDTipoMedicao)
        {
            default:

                $('.div_cpfl').css("display", "none");
                $('.div_personalizacao').css("display", "none");
                $('.div_gateway').css("display", "none");
                $('.div_formula').css("display", "none");
                $('.div_energia').css("display", "none");
                $('.div_referencia').css("display", "none");
                $('.div_mensagem_geren').css("display", "none");
                $('.div_kpi').css("display", "none");
                $('.div_utilidades').css("display", "none");
                $('.div_ciclometro').css("display", "none");
                $('.div_util_analogicas').css("display", "none");
                $('.div_util_ciclo').css("display", "none");
                $('.div_estado_cidade').css("display", "none");

                break;

            case 0:     // energia eletrica

                $('.div_cpfl').css("display", "block");
                $('.div_personalizacao').css("display", "block");
                $('.div_gateway').css("display", "block");
                $('.div_formula').css("display", "none");
                $('.div_energia').css("display", "block");
                $('.div_referencia').css("display", "block");
                $('.div_mensagem_geren').css("display", "block");
                $('.div_kpi').css("display", "block");
                $('.div_utilidades').css("display", "none");
                $('.div_ciclometro').css("display", "none");
                $('.div_util_analogicas').css("display", "none");
                $('.div_util_ciclo').css("display", "none");
                $('.div_estado_cidade').css("display", "none");

                break;

            case 1:     // energia eletrica formula

                $('.div_cpfl').css("display", "block");
                $('.div_personalizacao').css("display", "block");
                $('.div_gateway').css("display", "none");
                $('.div_formula').css("display", "block");
                $('.div_energia').css("display", "block");
                $('.div_referencia').css("display", "block");
                $('.div_mensagem_geren').css("display", "none");
                $('.div_kpi').css("display", "block");
                $('.div_utilidades').css("display", "none");
                $('.div_ciclometro').css("display", "none");
                $('.div_util_analogicas').css("display", "none");
                $('.div_util_ciclo').css("display", "none");
                $('.div_estado_cidade').css("display", "none");

                break;

            case 2:     // utilidades

                $('.div_cpfl').css("display", "none");
                $('.div_personalizacao').css("display", "none");
                $('.div_gateway').css("display", "block");
                $('.div_formula').css("display", "none");
                $('.div_energia').css("display", "none");
                $('.div_referencia').css("display", "block");
                $('.div_mensagem_geren').css("display", "none");
                $('.div_kpi').css("display", "none");
                $('.div_utilidades').css("display", "block");
                $('.div_ciclometro').css("display", "none");
                $('.div_util_analogicas').css("display", "block");
                $('.div_util_ciclo').css("display", "block");
                $('.div_estado_cidade').css("display", "none");

                break;

            case 3:     // analogicas

                $('.div_cpfl').css("display", "none");
                $('.div_personalizacao').css("display", "none");
                $('.div_gateway').css("display", "block");
                $('.div_formula').css("display", "none");
                $('.div_energia').css("display", "none");
                $('.div_referencia').css("display", "none");
                $('.div_mensagem_geren').css("display", "none");
                $('.div_kpi').css("display", "none");
                $('.div_utilidades').css("display", "none");
                $('.div_ciclometro').css("display", "none");
                $('.div_util_analogicas').css("display", "block");
                $('.div_util_ciclo').css("display", "none");
                $('.div_estado_cidade').css("display", "none");

                break;

            case 4:     // ciclometro

                $('.div_cpfl').css("display", "none");
                $('.div_personalizacao').css("display", "none");
                $('.div_gateway').css("display", "block");
                $('.div_formula').css("display", "none");
                $('.div_energia').css("display", "none");
                $('.div_referencia').css("display", "block");
                $('.div_mensagem_geren').css("display", "none");
                $('.div_kpi').css("display", "none");
                $('.div_utilidades').css("display", "none");
                $('.div_ciclometro').css("display", "block");
                $('.div_util_analogicas').css("display", "block");
                $('.div_util_ciclo').css("display", "block");
                $('.div_estado_cidade').css("display", "none");

                break;

            case 5:     // utilidades formula

                $('.div_cpfl').css("display", "none");
                $('.div_personalizacao').css("display", "none");
                $('.div_gateway').css("display", "none");
                $('.div_formula').css("display", "block");
                $('.div_energia').css("display", "none");
                $('.div_referencia').css("display", "none");
                $('.div_mensagem_geren').css("display", "none");
                $('.div_kpi').css("display", "none");
                $('.div_utilidades').css("display", "block");
                $('.div_ciclometro').css("display", "none");
                $('.div_util_analogicas').css("display", "block");
                $('.div_util_ciclo').css("display", "block");
                $('.div_estado_cidade').css("display", "none");

                break;

            case 6:     // analogicas formula

                $('.div_cpfl').css("display", "none");
                $('.div_personalizacao').css("display", "none");
                $('.div_gateway').css("display", "none");
                $('.div_formula').css("display", "block");
                $('.div_energia').css("display", "none");
                $('.div_referencia').css("display", "none");
                $('.div_mensagem_geren').css("display", "none");
                $('.div_kpi').css("display", "none");
                $('.div_utilidades').css("display", "none");
                $('.div_ciclometro').css("display", "none");
                $('.div_util_analogicas').css("display", "block");
                $('.div_util_ciclo').css("display", "none");
                $('.div_estado_cidade').css("display", "none");

                break;

            case 7:     // estacao meteorologica

                $('.div_cpfl').css("display", "none");
                $('.div_personalizacao').css("display", "none");
                $('.div_gateway').css("display", "none");
                $('.div_formula').css("display", "none");
                $('.div_energia').css("display", "none");
                $('.div_referencia').css("display", "none");
                $('.div_mensagem_geren').css("display", "none");
                $('.div_kpi').css("display", "none");
                $('.div_utilidades').css("display", "none");
                $('.div_ciclometro').css("display", "none");
                $('.div_util_analogicas').css("display", "none");
                $('.div_util_ciclo').css("display", "none");
                $('.div_estado_cidade').css("display", "block");

                break;
        }

        // verifica se não é CPFL
        if (IDConsultor != CLIENTES_ESPECIAIS_CPFL )
        {
            $('.div_cpfl').css("display", "none");
        }

        // verifica tipo da medicao
        switch(IDTipoMedicao)
        {
            case 0:     // energia eletrica
            case 1:     // energia eletrica formula

                $('.tab-2').css("display", "block");
                $('.tab-3').css("display", "block");
                $('.tab-4').css("display", "block");
                $('.tab-5').css("display", "block");

                // verifica permissao
                switch (permissao) {
                    case PERMISSOES_ADMIN:          // 0 - permissao de Admin: ve e escreve em tudo
                    case PERMISSOES_SUPORTE:        // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
                        $('.tab-6').css("display", "block");
                        break;

                    case PERMISSOES_CONSULTOR:      // 5 - permissao de Consultor
                        // verifica se é ENEL
                        if (IDConsultor == CLIENTES_ESPECIAIS_ENEL )
                        {
                            $('.tab-6').css("display", "block");
                        }
                        else
                        {
                            $('.tab-6').css("display", "none");
                        }
                        break;

                    default:
                        $('.tab-6').css("display", "none");
                        break;
                }

                break;

            default:
            case 2:     // utilidades
            case 3:     // analogicas
            case 4:     // ciclometro
            case 5:     // utilidades formula
            case 6:     // analogicas formula
            case 7:     // estacao meteorologica

                $('.tab-2').css("display", "none");
                $('.tab-3').css("display", "none");
                $('.tab-4').css("display", "none");
                $('.tab-5').css("display", "none");
                $('.tab-6').css("display", "none");

                // salva em cookie
                setCookie('MedicaoEditar_Tab', "#tab-1", null);

                break;
        }
    }

    function SelecionouTipoMedicao() {

        // pega tipos selecionado
        var tipo_medicao = parseInt($("#IDTipoMedicao").val());
        var tipo_contrato = 0;

        if( tipo_medicao == 0 || tipo_medicao == 1 )
        {
            tipo_contrato = parseInt($("#IDContratoMedicao").val());
        }

        // apresenta campos conforme tipo da medicao
        showCampos(tipo_medicao, tipo_contrato);
    }

    function SelecionouTipoContrato() {

        // pega tipos selecionado
        var tipo_medicao = parseInt($("#IDTipoMedicao").val());
        var IDMedicao = parseInt($("#IDMedicao").val());
        var IDContratoMedicao = 0;

        if( tipo_medicao == 0 || tipo_medicao == 1 )
        {
            IDContratoMedicao = parseInt($("#IDContratoMedicao").val());
        }

        // apresenta campos conforme tipo da medicao
        showCampos(tipo_medicao, IDContratoMedicao);

        // verifica se esta inserindo medicao
        if( IDMedicao > 0 )
        {
            // salva tipo do contrato
            $.ajax(
            {
                type: 'GET',
                url: '/Configuracao/Medicoes_Salvar_ContratoMedicao',
                data: {'IDMedicao': IDMedicao, 'IDContratoMedicao': IDContratoMedicao },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                },
                error: function (response) {

                }
            });
        }
    }

    function SelecionouEstruturaTarifaria() {

        // lista estrutura tarifaria e subgrupos
        var lista = @Html.Raw(Json.Encode(ViewBag.listaTipoEstruturaTarifaria));

        // pega tipo selecionado
        var tipo = $("#IDSubgrupo").val();

        // acerta estrutura tarifaria e tiposubgrupo
        document.getElementById("IDEstruturaTarifaria").value = lista[tipo].IDEstruturaTarifaria;
        document.getElementById("IDTipoSubgrupo").value = lista[tipo].IDTipoSubgrupo;
    }


    function SelecionouGateway() {

        // pega tipos selecionado
        var IDGateway = parseInt($("#IDGateway").val());

        // ICMS
        $.ajax(
        {
            type: 'GET',
            url: '/Configuracao/ObterICMS',
            data: {'IDGateway': IDGateway },
            contentType: 'application/json',
            dataType: 'json',
            cache: false,
            async: true,
            success: function (data) {

                // pega texto estado
                var nome_estado = data.Nome_Estado;
                var icms_padrao = data.ICMS_padrao;

                // modifica texto botao
                $('button#BotaoICMSPadrao').text(nome_estado);
                document.getElementById("ICMS_padrao").value = icms_padrao;

            },
            error: function (response) {

            }
        });


    }


    function UsarICMSPadrao() {

        // valor
        var icms_padrao = document.getElementById("ICMS_padrao").value;

        // modifica texto botao
        document.getElementById("valorICMS").value = icms_padrao;

    }

    function Excluir_HistoricoICMS(IDICMS, DataTexto) {
        event.stopPropagation();

        // titulo
        titulo = "Deseja excluir o ICMS?<br/>" + DataTexto;

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: false
        }, function () {

            // excluir
            $.ajax(
            {
                type: 'GET',
                url: '/Configuracao/HistoricoICMS_Excluir',
                data: {'IDICMS': IDICMS },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        swal({
                            title: "Excluído com sucesso",
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // atualiza pagina
                            location.reload();
                        });

                    }, 100);
                },
                error: function (response) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao excluir!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });
    };

    function Excluir_HistoricoContrato(IDContrato, DataTexto) {
        event.stopPropagation();

        // titulo
        titulo = "Deseja excluir o Contrato?<br/>" + DataTexto;

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: false
        }, function () {

            // excluir
            $.ajax(
            {
                type: 'GET',
                url: '/Configuracao/HistoricoContratos_Excluir',
                data: {'IDContrato': IDContrato },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        swal({
                            title: "Excluído com sucesso",
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // atualiza pagina
                            location.reload();
                        });

                    }, 100);
                },
                error: function (response) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao excluir!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });
    };

    function Excluir_HistoricoRural(IDRural, DataTexto) {
        event.stopPropagation();

        // titulo
        titulo = "Deseja excluir o Rural/Irrigantes?<br/>" + DataTexto;

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: false
        }, function () {

            // excluir
            $.ajax(
            {
                type: 'GET',
                url: '/Configuracao/HistoricoRural_Excluir',
                data: {'IDRural': IDRural },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        swal({
                            title: "Excluído com sucesso",
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // atualiza pagina
                            location.reload();
                        });

                    }, 100);
                },
                error: function (response) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao excluir!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });
    };


    function Salvar() {

        // formulario
        var $form = $('#form');

        // IDMedicao
        var IDMedicao = parseInt($("#IDMedicao").val());

        // numero de medicoes
        var NumMedicoes = parseInt($("#NMed").val());

        var boo = document.getElementById('NumMedicoes');
        boo.value = NumMedicoes;

        // pega tipos selecionado
        var tipo_medicao = parseInt($("#IDTipoMedicao").val());

        // configuracao validacao
        var settings = $form.validate().settings;

        // verifica se inserindo
        if (IDMedicao == 0){
            // fecha modal
            $('#modalSalvar').modal('hide');
        }


        // verifica tipo da medicao
        switch(tipo_medicao)
        {
            default:

                // modifica validacao
                $.extend(settings, {
                    rules: {
                        Nome: { required: true, alphanumeric: true, minlength: 3, maxlength: 50 },
                        IDUnidade: { required: true },
                    },
                });

                break;

            case 0:     // energia eletrica

                // modifica validacao
                $.extend(settings, {
                    rules: {
                        Nome: { required: true, alphanumeric: true, minlength: 3, maxlength: 50 },
                        IDUnidade: { required: true },
                        IDTipoMedicao: { required: true },
                        IDDeslocamento: { required: true },
                        IDCicloMes: { required: true },
                        IDGateway: { required: true },
                        NumMedGateway: { required: true },
                        IDTipoUnidadePotencia: { required: true },
                        IDContratoMedicao: { required: true },
                        IDAgenteDistribuidora: { required: true },
                        IDSubgrupo: { required: true },
                        IDSubSistema: { required: true },
                        IDRegraDem: { required: true },
                        IDRegraReativo: { required: true },
                        IDRegraICMS: { required: true },
                        IDCalculoICMS: { required: true },
                        InicioP: { required: true },
                        FimP: { required: true },
                        InicioPC: { required: true },
                        FimPC: { required: true },
                        InicioR: { required: true },
                        FimR: { required: true },
                        ToleranciaDemP: { digits: true },
                        ToleranciaDemFP: { digits: true },
                        Dem_P_pcH: {
                            required: true,
                            LimiteMinMax: true,
                            LimiteMinMax: [10.0,150.0]
                        },
                        Dem_F_pcH: {
                            required: true,
                            LimiteMinMax: true,
                            LimiteMinMax: [10.0,150.0]
                        },
                        Dem_P_pcL: {
                            required: true,
                            LimiteMinMax: true,
                            LimiteMinMax: [1.0,20.0]
                        },
                        Dem_F_pcL: {
                            required: true,
                            LimiteMinMax: true,
                            LimiteMinMax: [1.0,20.0]
                        },
                        FP_LimInd: {
                            numeric: true,
                            LimiteFatPotInd: true,
                            LimiteFatPotInd: [0.95]
                        },
                        FP_LimCap: {
                            numeric: true,
                            LimiteFatPotCap: true,
                            LimiteFatPotCap: [-0.95]
                        },
                        DiasNCnPj: {
                            digits: true,
                            max:30
                        },
                        CnPjP_pcH: { numeric: true },
                        CnPjF_pcH: { numeric: true },
                        CnPjP_pcL: { numeric: true },
                        CnPjF_pcL: { numeric: true },
                        Funcionamento_DemMin: { numeric: true },
                        KPI_RefConsumo: { numeric: true },
                        KPI_PotenciaInstalada: { numeric: true },
                        KPI_Area: { numeric: true },
                    }
                });

                break;

            case 1:     // energia eletrica formula

                // modifica validacao
                $.extend(settings, {
                    rules: {
                        Nome: { required: true, alphanumeric: true, minlength: 3, maxlength: 50 },
                        IDUnidade: { required: true },
                        IDTipoMedicao: { required: true },
                        IDDeslocamento: { required: true },
                        IDCicloMes: { required: true },
                        Formula: { required: true, formula: true },
                        IDContratoMedicao: { required: true },
                        IDAgenteDistribuidora: { required: true },
                        IDSubgrupo: { required: true },
                        IDSubSistema: { required: true },
                        IDRegraDem: { required: true },
                        IDRegraReativo: { required: true },
                        IDRegraICMS: { required: true },
                        IDCalculoICMS: { required: true },
                        InicioP: { required: true },
                        FimP: { required: true },
                        InicioPC: { required: true },
                        FimPC: { required: true },
                        InicioR: { required: true },
                        FimR: { required: true },
                        ToleranciaDemP: { digits: true },
                        ToleranciaDemFP: { digits: true },
                        KPI_RefConsumo: { numeric: true },
                        KPI_PotenciaInstalada: { numeric: true },
                        KPI_Area: { numeric: true },
                    },
                });

                break;

            case 2:     // utilidades

                // modifica validacao
                $.extend(settings, {
                    rules: {
                        Nome: { required: true, alphanumeric: true, minlength: 3, maxlength: 50 },
                        IDUnidade: { required: true },
                        IDTipoMedicao: { required: true },
                        IDGateway: { required: true },
                        NumMedGateway: { required: true },
                        NomeGrandeza: { required: true },
                        UnidadeGrandeza: { required: true },
                        IDAgenteDistribuidora: { required: true },
                        IDIcone: { required: true },
                    },
                });

                break;

            case 3:     // analogicas

                // modifica validacao
                $.extend(settings, {
                    rules: {
                        Nome: { required: true, alphanumeric: true, minlength: 3, maxlength: 50 },
                        IDUnidade: { required: true },
                        IDTipoMedicao: { required: true },
                        IDGateway: { required: true },
                        NumMedGateway: { required: true },
                        NomeGrandeza: { required: true },
                        UnidadeGrandeza: { required: true },
                    },
                });

                break;

            case 4:     // ciclometro

                // modifica validacao
                $.extend(settings, {
                    rules: {
                        Nome: { required: true, alphanumeric: true, minlength: 3, maxlength: 50 },
                        IDUnidade: { required: true },
                        IDTipoMedicao: { required: true },
                        IDGateway: { required: true },
                        NumMedGateway: { required: true },
                        NomeGrandeza: { required: true },
                        UnidadeGrandeza: { required: true },
                        IntervaloGrandeza: { required: true },
                        FEGrandeza: { digits: true },
                    },
                });

                break;

            case 5:     // utilidades formula

                // modifica validacao
                $.extend(settings, {
                    rules: {
                        Nome: { required: true, alphanumeric: true, minlength: 3, maxlength: 50 },
                        IDUnidade: { required: true },
                        IDTipoMedicao: { required: true },
                        Formula: { required: true, formula: true },
                        NomeGrandeza: { required: true },
                        UnidadeGrandeza: { required: true },
                        IDAgenteDistribuidora: { required: true },
                        IDIcone: { required: true },
                    },
                });

                break;

            case 6:     // analogicas formula

                // modifica validacao
                $.extend(settings, {
                    rules: {
                        Nome: { required: true, alphanumeric: true, minlength: 3, maxlength: 50 },
                        IDUnidade: { required: true },
                        IDTipoMedicao: { required: true },
                        Formula: { required: true, formula: true },
                        NomeGrandeza: { required: true },
                        UnidadeGrandeza: { required: true },
                    },
                });

                break;

            case 7:     // estacao meteorologica

                // modifica validacao
                $.extend(settings, {
                    rules: {
                        Nome: { required: true, alphanumeric: true, minlength: 3, maxlength: 50 },
                        IDUnidade: { required: true },
                        IDTipoMedicao: { required: true },
                        IDEstado: { required: true },
                        IDCidade: { required: true },
                    },
                });

                break;
        }

        // estrutura tarifaria
        SelecionouEstruturaTarifaria();

        // apresenta primeiro tab que tiver erro
        var IsTabValid = true;
        var TabInvalid = 1;

        $(".tab-content").find("div.tab-pane").each(function (index, tab) {
            var id = $(tab).attr("id");
            $('a[href="#' + id + '"]').tab('show');

            IsTabValid = $("#form").valid();

            if (!IsTabValid) {

                // fecha janela salvar
                $('#modalSalvar').modal('hide');

                TabInvalid = id;
                return false;
            }
        });


        // apresenta primeira tab ou com erro
        $('a[href="#tab-' + TabInvalid + '"]').tab('show');

        // verifica se entradas sao validas
        if (!IsTabValid)
        {
            // fecha janela email
            $('#modalSalvar').modal('hide');

            return false;
        }

        swal({
            title: "Deseja salvar?",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#1ab394",
            confirmButtonText: "Salvar",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {
            event.stopPropagation();

            // aguarde
            $('.overlay_aguarde').toggle();

            // retiro campos desabilitados, pois nao envia via POST
            $(":disabled", $('#form')).removeAttr("disabled");

            $.ajax({
                url: '/Configuracao/Medicoes_Salvar',
                data: $form.serialize(),
                type: 'POST',
                success: function (data) {

                    // fim
                    $('.overlay_aguarde').toggle();


                    setTimeout(function () {

                        // fecha janela email
                        $('#modalSalvar').modal('hide');

                        // verifica se erro
                        if (data.status == "ERRO")
                        {
                            swal({
                                html: true,
                                title: "Erro",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // redireciona para pagina lista de medicoes
                                var url = '/Configuracao/Medicoes?IDCliente=' + document.getElementById('IDCliente').value;
                                window.location.href = url;
                            });
                        }
                        else
                        {
                            swal({
                                title: "Salvo com sucesso",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // redireciona para pagina lista de medicoes
                                var url = '/Configuracao/Medicoes?IDCliente=' + document.getElementById('IDCliente').value;
                                window.location.href = url;
                            });
                        }

                    }, 100);

                },
                error: function (xhr, status, error) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        // fecha janela email
                        $('#modalSalvar').modal('hide');

                        swal({
                            title: "Erro",
                            text: "Erro ao salvar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // redireciona para pagina lista de medicoes
                            var url = '/Configuracao/Medicoes?IDCliente=' + document.getElementById('IDCliente').value;
                            window.location.href = url;
                        });

                    }, 100);

                }
            });
        });
    };


    function getCookie(c_name) {

        // Get name followed by anything except a semicolon
        var cookiestring = RegExp("" + c_name + "[^;]+").exec(document.cookie);

        // Return everything after the equal sign, or an empty string if the cookie name not found
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./, "") : "");
    }

    function setCookie(c_name, value, expiresecs) {
        var exdate = new Date();
        exdate.setTime(exdate.getTime() + ((expiresecs) ? expiresecs * 1000 : 0));

        document.cookie = c_name + "=" + escape(value) + ((expiresecs == null) ? "" : ";expires=" + exdate.toGMTString()) + ";path=/";
    }

    </script>
}
