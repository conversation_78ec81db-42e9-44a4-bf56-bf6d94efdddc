﻿@model SmartEnergyLib.SQL.GateX_MedEner_Dominio

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.ConfiguracaoTexts.MedicaoEnergia;
}


<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">

                    @using (Html.BeginForm("GateX_MedEner_Editar", "Configuracao", FormMethod.Post, new { id = "form", role = "form" }))
                    {
                        @Html.Hidden("Programado", Model.Programado)
                        
                        @Html.Hidden("Remota", Model.Remota)
                        @Html.Hidden("Status", Model.Status)
                        @Html.Hidden("Origem", Model.Origem)
                        @Html.Hidden("Unidade", Model.Unidade)
                        @Html.Hidden("opGrEl", Model.opGrEl)
                        @Html.Hidden("opGrEl2", Model.opGrEl2)
                        @Html.Hidden("PrgEntAtiva", Model.PrgEntAtiva)
                        @Html.Hidden("PrgEntInd", Model.PrgEntInd)
                        @Html.Hidden("PrgEntCap", Model.PrgEntCap)
                        
                        
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="panel panel-success">
                                    <div class="panel-heading">

                                        @{
                                            GatewaysDominio gateway = ViewBag.Gateway;
                                            @Html.Hidden("IDGateway", gateway.IDGateway);
                                        }

                                        <h4>Gateway</h4><br />
                                        @gateway.Nome

                                        @{
                                            int IDTipoAcesso = ViewBag._IDTipoAcesso;
                                            if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                            {
                                                <span> [@gateway.IDGateway]</span>
                                            }
                                        }

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="panel panel-title">
                            <div class="panel-heading">
                                <div style="text-align:center;">
                                    <h4>@SmartEnergy.Resources.ConfiguracaoTexts.MedicaoEnergia</h4>
                                </div>

                                <div class="pull-left relat-navega">
                                    <h4>
                                        @{
                                            if (Model.NumMedicaoGateway != 0)
                                            {
                                                <a class="link_primeira">
                                                    <i class="fa fa-angle-double-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Primeira></i>
                                                </a>
                                                <a class="link_prog_menos">
                                                    <i class="fa fa-angle-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Anterior></i>
                                                </a>
                                            }

                                            if (Model.NumMedicaoGateway != 35)
                                            {
                                                <a class="link_prog_mais">
                                                    <i class="fa fa-angle-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Proxima></i>
                                                </a>
                                                <a class="link_ultima">
                                                    <i class="fa fa-angle-double-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Ultima></i>
                                                </a>
                                            }
                                        }
                                    </h4>
                                </div>
                            </div>

                            <div class="panel-body">
                                
                                <div class="row">
                                    <div class="form-group col-lg-2">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.MedicaoEnergia</label>
                                        @Html.TextBoxFor(model => model.NumMedicaoGateway, new { @class = "form-control", @disabled = "disabled" })
                                    </div>
                                    <div class="col-lg-offset-4 col-lg-6">
                                        <div class="ibox-tools">
                                            <button type="button" class="btn btn-primary" onclick="Programar();" disabled="" id="BotaoProgramar">@SmartEnergy.Resources.ComumTexts.BotaoProgramar</button>
                                            <a href='@("/Configuracao/GateX_MedEner?IDGateway=" + @Model.IDGateway + "&Origem=2")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                        </div>
                                    </div>
                                </div>

                                <div class="tabs-container">
                                    <ul class="nav nav-tabs">
                                        <li class="active"><a data-toggle="tab" href="#tab-1"><i class="fa fa-cogs"></i>@SmartEnergy.Resources.ConfiguracaoTexts.Geral</a></li>
                                        <li class="tab-2"><a data-toggle="tab" href="#tab-2"><i class="fa fa-dollar"></i>@SmartEnergy.Resources.ConfiguracaoTexts.ParametrosFaturamento</a></li>
                                        <li class="tab-3"><a data-toggle="tab" href="#tab-3"><i class="fa fa-bar-chart"></i>@SmartEnergy.Resources.ConfiguracaoTexts.DemandasSuplementares</a></li>
                                    </ul>
                                    <div class="tab-content">

                                        <div id="tab-1" class="tab-pane active">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="form-group col-lg-7">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Medidor</label>
                                                        @Html.DropDownListFor(model => model.TipoMedidor, new SelectList(ViewBag.listatipoMedidoresEner, "ID", "Descricao", Model.TipoMedidor), new { @class = "form-control", @onchange = "SelecionouMedidor()" })
                                                    </div> 
                                                    <div class="div_programado1">
                                                        <div class="form-group col-lg-offset-1 col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Constante</label>
                                                            @Html.TextBoxFor(model => model.Constante, new { @class = "form-control" })
                                                        </div>
                                                    </div>
                                                </div> 

                                                <div class="div_programado2">
                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-7">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Descricao</label>
                                                            @Html.TextBoxFor(model => model.Descricao, new { @class = "form-control", @maxlength = "21" })
                                                        </div>
                                                        <div class="form-group col-lg-offset-1 col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.MediaMovel (minutos)</label>
                                                            @Html.TextBoxFor(model => model.TamanhoAnel, new { @class = "form-control" })
                                                        </div>
                                                    </div>

                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.HabilitarReativo</label>
                                                            @Html.DropDownListFor(model => model.HabReativo, new SelectList(ViewBag.listaTipoReativo, "ID", "Descricao", Model.HabReativo), new { @class = "form-control" })
                                                        </div>
                                                        <div class="form-group col-lg-2">
                                                            <label class="control-label">&nbsp;</label>
                                                            <div class="i-checks" style="margin-top:6px;">
                                                                @Html.CheckBoxFor(model => model.RecebeHist)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracaoTexts.RegistrarHistorico</span>
                                                            </div>
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;</label>
                                                            <div class="i-checks" style="margin-top:6px;">
                                                                @Html.CheckBoxFor(model => model.ReiniMM)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracaoTexts.ReiniciarMM</span>
                                                            </div>
                                                        </div>
                                                        <div class="form-group col-lg-4">
                                                            <label id="AlarmeFaltaPulso" class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.AlarmeFaltaPulso</label>
                                                            @Html.TextBoxFor(model => model.TempoPulsos, new { @class = "form-control" })
                                                        </div>
                                                    </div>

                                                </div>

                                                <div class="div_rede">
                                                    <hr />
                                                    <div class="row">
                                                        <div class="form-group col-lg-12">
                                                            <div class="row">
                                                                <div class="form-group col-lg-6">
                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Rede</label>
                                                                    @Html.DropDownListFor(model => model.RedeIO, new SelectList(ViewBag.listaTipoRedeIO, "ID", "Descricao", Model.RedeIO), new { @class = "form-control", @onchange = "SelecionouRedeIO()" })
                                                                </div>
                                                                <div class="form-group col-lg-3">
                                                                    <div class="div_rede_r_q">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Remota</label>
                                                                        @Html.DropDownListFor(model => model.Remota, new SelectList(ViewBag.ListaRemotas), string.Format("Nenhum"), new { id = "remotas_r_q", @onchange = "SelecionouNumRemota()", @class = "form-control", @disabled = "disabled" })
                                                                    </div>
                                                                    <div class="div_rede_k" style="display: none">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Remota</label>
                                                                        @Html.DropDownListFor(model => model.Remota, new SelectList(ViewBag.listaTipoNumRemotaRedeK, "ID", "Descricao", Model.Remota), new { id = "remotas_k", @onchange = "SelecionouNumRemota()", @class = "form-control", @disabled = "disabled" })
                                                                    </div>
                                                                </div>
                                                                <div class="form-group col-lg-3">
                                                                    <div class="div_Endereco">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Endereco</label>
                                                                        @Html.TextBoxFor(model => model.Endereco, new { @class = "form-control" })
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <br />
                                                            <div class="row">
                                                                <div class="form-group col-lg-6">
                                                                    <label class="control-label">&nbsp;Relação TP</label>
                                                                    @Html.TextBoxFor(model => model.RTP, new { @class = "form-control" })
                                                                </div>
                                                                <div class="form-group col-lg-6">
                                                                    <label class="control-label">&nbsp;Relação TC</label>
                                                                    @Html.TextBoxFor(model => model.RTC, new { @class = "form-control" })
                                                                </div>
                                                            </div>

                                                        </div>

                                                    </div>
                                                </div> 

                                                <div class="div_DIN_virtual">
                                                    <hr />
                                                    <div class="row">
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Endereco</label>
                                                            @Html.TextBoxFor(model => model.Endereco, new { @class = "form-control" })
                                                        </div>
                                                    </div>
                                                </div>
                                                 
                                                <div class="div_formula">
                                                    <hr />
                                                    <div class="row">
                                                        <div class="form-group col-lg-7">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Formula</label>
                                                            @Html.TextBoxFor(model => model.Formula, new { @class = "form-control" })
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="div_programado3">
                                                    <hr />
                                                    <div class="row">

                                                        @{
                                                            // le saidas gateway
                                                            List<GateX_SD_Lista_Dominio> listaSaidas = ViewBag.listaSaidas;

                                                            <div class="form-group col-lg-7">
                                                                <label class="control-label">&nbsp;Não medir se a Saída Digital estiver ativa</label>
                                                                @Html.DropDownListFor(model => model.SdNaoAcuMed, new SelectList(listaSaidas, "NumSaidaGateway", "Descricao").OrderBy(x => x.ToString()), new { @class = "form-control" })
                                                            </div>
                                                        }

                                                        <div class="div_opcoes">
                                                            <div class="form-group col-lg-offset-1 col-lg-4">
                                                                <label class="control-label">&nbsp;Opções</label>
                                                                <div class="i-checks" style="margin-top:6px;">
                                                                    @Html.CheckBoxFor(model => model.Op0)&nbsp;&nbsp;<span style="margin-right:70px;">Opção 0</span>
                                                                    @Html.CheckBoxFor(model => model.Op1)&nbsp;&nbsp;<span style="margin-right:0px;">Opção 1</span><br /><br />
                                                                    @Html.CheckBoxFor(model => model.Op2)&nbsp;&nbsp;<span style="margin-right:70px;">Opção 2</span>
                                                                    @Html.CheckBoxFor(model => model.Op3)&nbsp;&nbsp;<span style="margin-right:0px;">Opção 3</span><br /><br />
                                                                    @Html.CheckBoxFor(model => model.Op4)&nbsp;&nbsp;<span style="margin-right:70px;">Opção 4</span>
                                                                    @Html.CheckBoxFor(model => model.Op5)&nbsp;&nbsp;<span style="margin-right:0px;">Opção 5</span><br /><br />
                                                                    @Html.CheckBoxFor(model => model.Op6)&nbsp;&nbsp;<span style="margin-right:70px;">Opção 6</span>
                                                                    @Html.CheckBoxFor(model => model.Op7)&nbsp;&nbsp;<span style="margin-right:0px;">Opção 7</span><br /><br />
                                                                </div>
                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                        <div id="tab-2" class="tab-pane">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="col-lg-12">

                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ContratoP (kW)</label>
                                                                @Html.TextBoxFor(model => model.Dem_Ponta, new { @class = "form-control" })
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ContratoFP (kW)</label>
                                                                @Html.TextBoxFor(model => model.DemFPonta, new { @class = "form-control" })
                                                            </div>
                                                        </div>

                                                        <hr />
                                                        <div class="row">
                                                            <div class="form-group col-lg-12">

                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.VigenciasDemanda</label>
                                                                <table class="table table-striped table-bordered table-hover dataTables-vigdem">
                                                                    <thead>
                                                                        <tr>
                                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Numero</th>
                                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.MedicoesEnergia</th>
                                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Data @SmartEnergy.Resources.ConfiguracaoTexts.Inicio</th>
                                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Demanda @SmartEnergy.Resources.ConfiguracaoTexts.P</th>
                                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Demanda @SmartEnergy.Resources.ConfiguracaoTexts.FPonta</th>
                                                                            <th></th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>

                                                                        @{
                                                                            List<GateX_MedEner_Dominio> listaMedEner = ViewBag.listaMedEner;
                                                                            List<GateX_MedEner_VigDem_Dominio> medVigDem = ViewBag.medVigDem;

                                                                            if (medVigDem != null && listaMedEner != null)
                                                                            {
                                                                                foreach (GateX_MedEner_VigDem_Dominio med in medVigDem)
                                                                                {
                                                                                    string desprogramado = "---";
                                                                                    string nomeMedicao = desprogramado;
                                                                                    string dataVig = desprogramado;
                                                                                    string demP = desprogramado;
                                                                                    string demFP = desprogramado;

                                                                                    if (med.Programado)
                                                                                    {
                                                                                        nomeMedicao = listaMedEner.Find(m => m.NumMedicaoGateway == med.Medicao).DescricaoNum;
                                                                                        dataVig = med.DataVigencia.ToString("d");
                                                                                        demP = med.DemandaP.ToString();
                                                                                        demFP = med.DemandaF.ToString();
                                                                                    }

                                                                                    <tr>
                                                                                        <td>@med.numVigDem.ToString()</td>
                                                                                        <td>@nomeMedicao</td>
                                                                                        <td>@dataVig</td>
                                                                                        <td>@demP</td>
                                                                                        <td>@demFP</td>
                                                                                        <td class="link_preto">
                                                                                            <a href='@("/Configuracao/GateX_MedEner_VigDem_Editar?IDGateway=" + @med.IDGateway.ToString() + "&numVigDem=" + @med.numVigDem.ToString() + "&NumMedicaoGateway=" + Model.NumMedicaoGateway)' title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"><i class="fa fa-edit icones"></i></a>

                                                                                            @if (ViewBag.Permissao != PERMISSOES.OPERADOR)
                                                                                            {
                                                                                                <a href="#" onclick="javascript:ExcluirVigDem(@gateway.IDGateway, @med.numVigDem);" id="BotaoExcluir" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-times icones"></i></a>
                                                                                            }

                                                                                        </td>
                                                                                    </tr>
                                                                                }
                                                                            }
                                                                        }

                                                                    </tbody>
                                                                </table>

                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    
                                        <div id="tab-3" class="tab-pane">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="col-lg-12">

                                                        <div class="row">
                                                            <div class="form-group col-lg-12">

                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.DemandasSuplementares</label>
                                                                <table class="table table-striped table-bordered table-hover dataTables-demsup">
                                                                    <thead>
                                                                        <tr>
                                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Numero</th>
                                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.MedicoesEnergia</th>
                                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Data (@SmartEnergy.Resources.ConfiguracaoTexts.Inicio | @SmartEnergy.Resources.ConfiguracaoTexts.Fim)</th>
                                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Hora (@SmartEnergy.Resources.ConfiguracaoTexts.Inicio | @SmartEnergy.Resources.ConfiguracaoTexts.Fim)</th>
                                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Periodo</th>
                                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Demanda</th>
                                                                            <th></th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>

                                                                        @{
                                                                            List<GateX_MedEner_DemSup_Dominio> medDemSup = ViewBag.medDemSup;

                                                                            if (medDemSup != null && listaMedEner != null)
                                                                            {
                                                                                foreach (GateX_MedEner_DemSup_Dominio med in medDemSup)
                                                                                {
                                                                                    string desprogramado = "---";
                                                                                    string nomeMedicao = desprogramado;
                                                                                    string data = desprogramado;
                                                                                    string hora = desprogramado;
                                                                                    string periodo = desprogramado;

                                                                                    if (med.Programado)
                                                                                    {
                                                                                        nomeMedicao = listaMedEner.Find(m => m.NumMedicaoGateway == med.NumeroMedEnergia).DescricaoNum;
                                                                                        data = string.Format("{0:d} - {1:d}", med.DataInicio, med.DataFim);
                                                                                        hora = string.Format("{0:t} - {1:t}", med.HoraInicio, med.HoraFim);
                                                                                        
                                                                                        switch(med.PeriodoValido)
                                                                                        {
                                                                                            case 1:     // somente em ponta
                                                                                                periodo = "Ponta";
                                                                                                break;

                                                                                            case 2:     // somente em fora ponta
                                                                                                periodo = "Fora de Ponta";
                                                                                                break;

                                                                                            case 3:     // ponta e fora ponta
                                                                                                periodo = "Ponta e Fora de Ponta";
                                                                                                break;
                                                                                        }
                                                                                    }

                                                                                    <tr>
                                                                                        <td>@med.numDemSup.ToString()</td>
                                                                                        <td>@nomeMedicao</td>
                                                                                        <td>@data</td>
                                                                                        <td>@hora</td>
                                                                                        <td>@periodo</td>
                                                                                        <td>@med.Demanda</td>
                                                                                        <td class="link_preto">
                                                                                            <a href='@("/Configuracao/GateX_MedEner_DemSup_Editar?IDGateway=" + @med.IDGateway.ToString() + "&numDemSup=" + @med.numDemSup.ToString() + "&NumMedicaoGateway=" + Model.NumMedicaoGateway)' title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"><i class="fa fa-edit icones"></i></a>

                                                                                            @if (ViewBag.Permissao != PERMISSOES.OPERADOR)
                                                                                            {
                                                                                                <a href="#" onclick="javascript:ExcluirDemSup(@gateway.IDGateway, @med.numDemSup);" id="BotaoExcluir" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-times icones"></i></a>
                                                                                            }

                                                                                        </td>
                                                                                    </tr>
                                                                                }
                                                                            }
                                                                        }

                                                                    </tbody>
                                                                </table>

                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
                
                <div class="overlay_aguarde" style="display: none">
                    <div class="spinner-aguarde">
                        <div class="sk-spinner sk-spinner-wandering-cubes">
                            <div class="sk-spinner sk-spinner-wave">
                                <div class="sk-rect1 text-ligh"></div>
                                <div class="sk-rect2"></div>
                                <div class="sk-rect3"></div>
                                <div class="sk-rect4"></div>
                                <div class="sk-rect5"></div>
                            </div>
                            <div class="sk-rect5"></div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
    @Styles.Render("~/plugins/select2Styles")
    @Styles.Render("~/datetimePickerStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/plugins/iCheck")
    @Scripts.Render("~/plugins/select2")
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/declaracoes/Constants")
    @Scripts.Render("~/declaracoes/Validates")



    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

    $(document).ready(function () {

        // classe iChecks
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });

        //
        // VALIDACAO DOS CAMPOS
        //
        Validator_AddMethods();

        $("#form").validate({
            rules: {
                Descricao: {
                    required: true,
                    alphanumeric: true,
                    descricao: true
                },
                TamanhoAnel: {
                    numeric: true,
                    min: 1,
                    max: 15
                },
                TempoPulsos: {
                    numeric: true,
                    min: 0,
                    max: 900
                },
                Endereco: {
                    numeric: true,
                    zero: true,
                    max: 65535
                },
                Constante: {
                    numeric: true,
                },
                RTP: {
                    numeric: true,
                    zero: true
                },
                RTC: {
                    numeric: true,
                    zero: true
                }
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            },
        });


        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {

                var x = a;

                if (a == "---") {
                    x = "-1";
                }
                else {
                    x = a.replace(".", "");
                    x = x.replace(",", ".");
                }

                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-vigdem').DataTable({
            "iDisplayLength": 6,
            dom: 'tp',

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "numero" },
                        { "aTargets": [1], "sType": "portugues" },
                        { "aTargets": [2], "sType": "portugues" },
                        { "aTargets": [3], "sType": "numero" },
                        { "aTargets": [4], "sType": "numero" },
                        { "aTargets": [5], "bVisible": true, "bSortable": false, "bSearchable": false }
            ],

            "aoColumns": [
                { sWidth: "10%" },
                { sWidth: "20%" },
                { sWidth: "20%" },
                { sWidth: "20%" },
                { sWidth: "20%" },
                { sWidth: "10%" },
            ],
            'order': [0, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        $('.dataTables-demsup').DataTable({
            "iDisplayLength": 5,
            dom: 'tp',

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "numero" },
                        { "aTargets": [1], "sType": "portugues" },
                        { "aTargets": [2], "sType": "portugues" },
                        { "aTargets": [3], "sType": "portugues" },
                        { "aTargets": [4], "sType": "portugues" },
                        { "aTargets": [5], "sType": "numero" },
                        { "aTargets": [6], "bVisible": true, "bSortable": false, "bSearchable": false }
            ],

            "aoColumns": [
                { sWidth: "10%" },
                { sWidth: "25%" },
                { sWidth: "15%" },
                { sWidth: "15%" },
                { sWidth: "15%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
            ],
            'order': [0, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });


        //// VERIFICA CAMPOS QUE DEVEM OU NAO ESTAR HABILITADOS \\\\
        SelecionouMedidor();

        // desabilita campos por permissao de usuario
        disableAll();

        $('form input').on('keypress', function (e) {
            return e.which !== 13;
        });

        // caso for selecionado alguma TAB, aponto para ele
        var url = document.location.toString();

        if (url.match('#')) {
            $('.nav-tabs a[href="#' + url.split('#')[1] + '"]').tab('show');

            setTimeout(function () {
                window.scrollTo(0, 0);
            }, 100);
        }

    });

    function disableAll() {

        var permissao = Math.ceil(@ViewBag.Permissao);

        // verifica permissao
        switch (permissao) {
            case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
            case PERMISSOES_GERENTE:            // 1 - permissao de Gerente: ve tudo e escreve parte
            case PERMISSOES_OPERADOR:           // 2 - permissao de Operador: ve tudo e nao pode escrever
            case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
            case PERMISSOES_CONSULTOR:          // 5 - permissao de Consultor
            case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte

                $("#BotaoProgramar").attr('disabled', false);
                $("#TipoMedidor").attr('disabled', false);

                break;

            default:
            case PERMISSOES_PRODUCAO:           // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                $("#BotaoProgramar").attr('disabled', true);
                $("#TipoMedidor").attr('disabled', true);

                break;
        }
    }

    function SelecionouMedidor() {

        // pega medidor selecionado
        var TipoMedidor = parseInt($("#TipoMedidor").val());

        // Ty   Descricao                       drvAdd  eRtv
        // 0    'Nenhum'                        0       0
        // 1    'Fórmula'                       0       2
        // 2    'Remota Genérica'               0       2
        // 3    'CODI'                          0       2
        // 4    'CODI Q'                        0       2
        // 5    'DIN Virtual'                   0       2
        // 6    'DI'                            0       0

        // 77   'Siemens PAC3100 Vfn'           3110    2
        // 78   'Siemens PAC3100 Vff'           3111    2
        // 81   'Siemens PAC3200 Vfn'           3200    2
        // 82   'Siemens PAC3200 Vff'           3201    2
        // 85   'Siemens PAC4200 Vfn'           4200    2
        // 86   'Siemens PAC4200 Vff'           4201    2
        // 103  'Kron Multi-K Vfn'              113     2
        // 104  'Kron Multi-K Vff'              114     2
        // 99   'ABB/H&B ETE30/50 MGE144 Vfn'   30      2
        // 100  'ABB/H&B ETE30/50 MGE144 Vff'   31      2
        // 93   'CCK CCK4200'                   21528   2
        // 95   'CCK CCK4500'                   21768   2
        // 107  'Q&D QDE-3S'                    30022   0

        // inicialmente habilitado
        $("#Dem_Ponta").attr('disabled', false);
        $("#DemFPonta").attr('disabled', false);

        switch (TipoMedidor)
        {
            case 0:
                $('.div_programado1').css("display", "none");
                $('.div_programado2').css("display", "none");
                $('.div_programado3').css("display", "none");
                $('.div_rede').css("display", "none");
                $('.div_DIN_virtual').css("display", "none");
                $('.div_formula').css("display", "none");
                $('.div_opcoes').css("display", "none");

                $("#Dem_Ponta").attr('disabled', true);
                $("#DemFPonta").attr('disabled', true);
                break;

            case 1:
                $('.div_programado1').css("display", "block");
                $('.div_programado2').css("display", "block");
                $('.div_programado3').css("display", "block");
                $('.div_rede').css("display", "none");
                $('.div_DIN_virtual').css("display", "none");
                $('.div_formula').css("display", "block");
                $('.div_opcoes').css("display", "none");

                document.getElementById("AlarmeFaltaPulso").innerHTML = "@SmartEnergy.Resources.ConfiguracaoTexts.AlarmeFaltaPulso";
                document.getElementById("TempoPulsos").value = @Model.TempoPulsos;
                $("#TempoPulsos").attr('disabled', false);
                break;

            case 3:
                $('.div_programado1').css("display", "block");
                $('.div_programado2').css("display", "block");
                $('.div_programado3').css("display", "block");
                $('.div_rede').css("display", "none");
                $('.div_DIN_virtual').css("display", "none");
                $('.div_formula').css("display", "none");
                $('.div_opcoes').css("display", "block");

                document.getElementById("AlarmeFaltaPulso").innerHTML = "@SmartEnergy.Resources.ConfiguracaoTexts.AlarmeFaltaSinalAtivo";
                document.getElementById("TempoPulsos").value = "Ativo";
                $("#TempoPulsos").attr('disabled', true);
                break;

            case 4:
                $('.div_programado1').css("display", "block");
                $('.div_programado2').css("display", "block");
                $('.div_programado3').css("display", "block");
                $('.div_rede').css("display", "none");
                $('.div_DIN_virtual').css("display", "none");
                $('.div_formula').css("display", "none");
                $('.div_opcoes').css("display", "block");

                document.getElementById("AlarmeFaltaPulso").innerHTML = "@SmartEnergy.Resources.ConfiguracaoTexts.AlarmeFaltaSinalAtivo";
                document.getElementById("TempoPulsos").value = "Ativo";
                $("#TempoPulsos").attr('disabled', true);
                break;

            case 5:
                $('.div_programado1').css("display", "block");
                $('.div_programado2').css("display", "block");
                $('.div_programado3').css("display", "block");
                $('.div_rede').css("display", "none");
                $('.div_DIN_virtual').css("display", "block");
                $('.div_formula').css("display", "none");
                $('.div_opcoes').css("display", "block");

                document.getElementById("AlarmeFaltaPulso").innerHTML = "@SmartEnergy.Resources.ConfiguracaoTexts.AlarmeFaltaPulso";
                document.getElementById("TempoPulsos").value = @Model.TempoPulsos;
                $("#TempoPulsos").attr('disabled', false);
                break;

            case 6:
                $('.div_programado1').css("display", "block");
                $('.div_programado2').css("display", "block");
                $('.div_programado3').css("display", "block");
                $('.div_rede').css("display", "none");
                $('.div_DIN_virtual').css("display", "none");
                $('.div_formula').css("display", "none");
                $('.div_opcoes').css("display", "none");

                document.getElementById("AlarmeFaltaPulso").innerHTML = "@SmartEnergy.Resources.ConfiguracaoTexts.AlarmeFaltaPulso";
                document.getElementById("TempoPulsos").value = @Model.TempoPulsos;
                $("#TempoPulsos").attr('disabled', false);
                break;

            case 2:

                $('.div_programado1').css("display", "block");
                $('.div_programado2').css("display", "block");
                $('.div_programado3').css("display", "block");
                $('.div_rede').css("display", "block");
                $('.div_Endereco').css("display", "block");
                $('.div_DIN_virtual').css("display", "none");
                $('.div_formula').css("display", "none");
                $('.div_opcoes').css("display", "block");

                document.getElementById("AlarmeFaltaPulso").innerHTML = "@SmartEnergy.Resources.ConfiguracaoTexts.AlarmeFaltaPulso";
                document.getElementById("TempoPulsos").value = @Model.TempoPulsos;
                $("#TempoPulsos").attr('disabled', false);

                SelecionouRedeIO();
                break;


            default:
                $('.div_programado1').css("display", "block");
                $('.div_programado2').css("display", "block");
                $('.div_programado3').css("display", "block");
                $('.div_rede').css("display", "block");
                $('.div_Endereco').css("display", "none");
                $('.div_DIN_virtual').css("display", "none");
                $('.div_formula').css("display", "none");
                $('.div_opcoes').css("display", "block");

                document.getElementById("AlarmeFaltaPulso").innerHTML = "@SmartEnergy.Resources.ConfiguracaoTexts.AlarmeFaltaPulso";
                document.getElementById("TempoPulsos").value = @Model.TempoPulsos;
                $("#TempoPulsos").attr('disabled', false);

                SelecionouRedeIO();
                break;
        }
    }


    function SelecionouRedeIO() {

        // pega redeIO selecionada
        var RedeIO = parseInt($("#RedeIO").val());

        // executo funcao para verificar se endereco do driver deve ser habilitado ou nao
        SelecionouNumRemota();

        // apresenta selecao de remotas conforme redeIO
        ShowNumRemota(RedeIO);
    }

    function ShowNumRemota(RedeIO) {

        switch (RedeIO) {

            case 0: // rede R
            case 1: // rede Q

                // habilita rede R e Q
                $('.div_rede_r_q').css("display", "block");
                $("#remotas_r_q").attr('disabled', false);

                // Desabilita rede K
                $('.div_rede_k').css("display", "none");
                $("#remotas_k").attr('disabled', true);

                break;

            case 2: // K

                // habilita rede K
                $('.div_rede_k').css("display", "block");
                $("#remotas_k").attr('disabled', false);

                // desabilita rede R e Q
                $('.div_rede_r_q').css("display", "none");
                $("#remotas_r_q").attr('disabled', true);

                break;
        }

    }

    function SelecionouNumRemota() {

        // pega redeIO selecionada
        var RedeIO = parseInt($("#RedeIO").val());

        // pega numero remota
        var NumRemota = 0;

        switch (RedeIO) {
            case 0:  // Rede R
            case 1:  // Rede Q
                NumRemota = parseInt(document.getElementById("remotas_r_q").value);
                break;

            case 2:  // Rede K
                NumRemota = parseInt(document.getElementById("remotas_k").value);
                break;
        }
    }

    function TrataValores() {

        // trata variavel numero da remota
        var rede_io = parseInt($("#RedeIO").val());
        var num_remota = 0;

        // recebe numero da remota de acordo com redeIO selecionado
        switch (rede_io) {
            case 0:  // Rede R
            case 1:  // Rede Q
                num_remota = parseInt(document.getElementById("remotas_r_q").value);
                break;

            case 2:  // Rede K
                num_remota = parseInt(document.getElementById("remotas_k").value);
                break;

            case 3:  // DO
                num_remota = 0;
                break;
        }

        if (Number.isNaN(num_remota) || num_remota < 0 || num_remota > 31) {
            num_remota = 255;
        }

        document.getElementById('Remota').value = num_remota;
    }

    function Programar() {

        // trata valores das variaveis a serem salvas
        TrataValores();

        var $form = $('#form');

        // apresenta primeiro tab que tiver erro
        var IsTabValid = true;
        var TabInvalid = 1;

        $(".tab-content").find("div.tab-pane").each(function (index, tab) {
            var id = $(tab).attr("id");
            $('a[href="#' + id + '"]').tab('show');

            IsTabValid = $("#form").valid();

            if (!IsTabValid) {

                TabInvalid = id;
                return false;
            }
        });

        // apresenta primeira tab ou com erro
        $('a[href="#tab-' + TabInvalid + '"]').tab('show');

        // verifica se sao validas
        if (!IsTabValid) return false;

        // retiro campos desabilitados, pois nao envia via POST
        $(":disabled", $('#form')).removeAttr("disabled");

        $.ajax({
            url: '/Configuracao/GateX_MedEner_Programar',
            data: $form.serialize(),
            type: 'POST',
            success: function (data) {

                setTimeout(function () {

                    // verifica se erro
                    if (data.status == "ERRO") {
                        swal({
                            title: "Erro",
                            text: data.erro,
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {


                        });
                    }
                    else {

                        // redireciona
                        var IDGateway = document.getElementById('IDGateway').value;
                        var url = '/Configuracao/GateX_MedEner?IDGateway=' + IDGateway + '&Origem=2';

                        window.location.href = url;
                    }

                }, 100);

            },
            error: function (xhr, status, error) {

                setTimeout(function () {

                    swal({
                        title: "Erro",
                        text: "Erro ao programar!",
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    }, function () {

                        // redireciona para pagina lista de saidas
                        var IDGateway = document.getElementById('IDGateway').value;
                        var url = '/Configuracao/GateX_MedEner?IDGateway=' + IDGateway + '&Origem=2';

                        window.location.href = url;
                    });
                }, 100);
            },
        });
    }

    $(".link_primeira").off().on("click", function (event) {
        event.stopPropagation();

        // primeira medição
        SalvaNavega(0);
    });

    $(".link_ultima").off().on("click", function (event) {
        event.stopPropagation();

        // ultima medição
        SalvaNavega(35);
    });

    $(".link_prog_menos").off().on("click", function (event) {
        event.stopPropagation();

        var NumMedicaoGateway = parseInt(document.getElementById("NumMedicaoGateway").value);

        // medição anterior
        SalvaNavega(NumMedicaoGateway - 1);
    });

    $(".link_prog_mais").off().on("click", function (event) {
        event.stopPropagation();

        var NumMedicaoGateway = parseInt(document.getElementById("NumMedicaoGateway").value);

        // proxima medição
        SalvaNavega(NumMedicaoGateway + 1);
    });


    function SalvaNavega(navega) {

        // pega medidor selecionado
        var TipoMedidor = parseInt($("#TipoMedidor").val());

        // caso desprogramado nao salvo
        if (TipoMedidor == 0) {

            // aguarde
            $('.overlay_aguarde').toggle();

            // redireciona para saida desejada
            var IDGateway = parseInt(document.getElementById("IDGateway").value);
            var url = '/Configuracao/GateX_MedEner_Editar?IDGateway=' + IDGateway + '&NumMedicaoGateway=' + navega;

            window.location.href = url;
        }

            // caso programado salva ao navegar para a proxima desejada
        else {

            // trata valores das variaveis a serem salvas
            TrataValores();

            var $form = $('#form');

            // apresenta primeiro tab que tiver erro
            var IsTabValid = true;
            var TabInvalid = 1;

            $(".tab-content").find("div.tab-pane").each(function (index, tab) {
                var id = $(tab).attr("id");
                $('a[href="#' + id + '"]').tab('show');

                IsTabValid = $("#form").valid();

                if (!IsTabValid) {

                    TabInvalid = id;
                    return false;
                }
            });

            // apresenta primeira tab ou com erro
            $('a[href="#tab-' + TabInvalid + '"]').tab('show');

            // verifica se entradas sao validas
            if (!IsTabValid) return false;

            // verifica se saidas sao validas
            if (!$form.valid()) return false;

            event.stopPropagation();

            // aguarde
            $('.overlay_aguarde').toggle();

            // retiro campos desabilitados, pois nao envia via POST
            $(":disabled", $('#form')).removeAttr("disabled");

            $.ajax({
                url: '/Configuracao/GateX_MedEner_Programar',
                data: $form.serialize(),
                type: 'POST',
                success: function (data) {

                    setTimeout(function () {

                        // redireciona 
                        var IDGateway = parseInt(document.getElementById("IDGateway").value);
                        var url = '/Configuracao/GateX_MedEner_Editar?IDGateway=' + IDGateway + '&NumMedicaoGateway=' + navega;
                        window.location.href = url;

                    }, 100);
                },
                error: function (xhr, status, error) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                        });

                    }, 100);
                },
            });
        }
    };


    function ExcluirVigDem(IDGateway, numVigDem) {
        event.stopPropagation();

        // titulo
        titulo = "Deseja excluir a Vigência de Demanda?<br/>Número " + numVigDem;

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: false
        }, function () {

            // excluir
            $.ajax(
            {
                type: 'GET',
                url: '/Configuracao/GateX_MedEner_VigDem_Excluir',
                data: { 'IDGateway' : IDGateway, 'numVigDem': numVigDem },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        swal({
                            title: "Excluído com sucesso",
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // le do banco de dados para atualizar
                            var NumMedicaoGateway = parseInt(document.getElementById("NumMedicaoGateway").value);
                            var url = '/Configuracao/GateX_MedEner_Editar?IDGateway=' + IDGateway + "&NumMedicaoGateway=" + NumMedicaoGateway;
                            window.location.href = url;
                        });

                    }, 100);
                },
                error: function (response) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao excluir!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });
    };


    function ExcluirDemSup(IDGateway, numDemSup) {
        event.stopPropagation();

        // titulo
        titulo = "Deseja excluir a Demanda Suplementar?<br/>Número " + numDemSup;

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: false
        }, function () {

            // excluir
            $.ajax(
            {
                type: 'GET',
                url: '/Configuracao/GateX_MedEner_DemSup_Excluir',
                data: { 'IDGateway' : IDGateway, 'numDemSup': numDemSup },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        swal({
                            title: "Excluído com sucesso",
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // le do banco de dados para atualizar
                            var NumMedicaoGateway = parseInt(document.getElementById("NumMedicaoGateway").value);
                            var url = '/Configuracao/GateX_MedEner_Editar?IDGateway=' + IDGateway + "&NumMedicaoGateway=" + NumMedicaoGateway;
                            window.location.href = url;
                        });

                    }, 100);
                },
                error: function (response) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao excluir!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });
    };

    </script>
}
