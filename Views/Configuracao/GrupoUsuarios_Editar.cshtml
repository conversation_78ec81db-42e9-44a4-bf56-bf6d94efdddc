﻿@model SmartEnergyLib.SQL.UsuarioGrupoDominio

@using System.Globalization
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoGrupoUsuarios;
}

<style>

    table.dataTable.select tbody tr,
    table.dataTable thead th:first-child {
        cursor: pointer;
    }

    tfoot input {
            width: 100%;
            padding: 3px;
            box-sizing: border-box;
        }

    #dataTables-usuarios tbody tr.selected {
            color: white;
            background-color: #1ab394;
        }

</style>


<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("GrupoUsuarios_Editar", "Configuracao", FormMethod.Post, new { id = "form", role = "form" }))
            {
                @Html.Hidden("IDCliente", Model.IDCliente)
                @Html.Hidden("Usuarios", Model.Usuarios)

                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoGrupoUsuarios</h4>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="form-group col-lg-2">
                                <label class="control-label">&nbsp;ID</label>

                                @{
                                    if (Model.IDUsuarioGrupo == 0)
                                    {
                                        @Html.Hidden("IDUsuarioGrupo", Model.IDUsuarioGrupo)
                                        @Html.TextBox("Novo", "Novo Grupo", new { @class = "form-control", @disabled = "disabled" })
                                    }
                                    else
                                    {
                                        @Html.TextBoxFor(model => model.IDUsuarioGrupo, new { @class = "form-control", @disabled = "disabled" })
                                    }
                                }

                            </div>
                            <div class="col-lg-offset-4 col-lg-6">
                                <div class="ibox-tools">
                                    <a href='@("/Configuracao/GrupoUsuarios?IDCliente=" + @Model.IDCliente)' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                    <button type="button" class="btn btn-primary" id="BotaoSalvar" onclick="Salvar();" disabled="">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                </div>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="tabs-container">
                                    <ul class="nav nav-tabs">
                                        <li class="active"><a data-toggle="tab" href="#tab-1"><i class="fa fa-cogs"></i> @SmartEnergy.Resources.ConfiguracaoTexts.Geral</a></li>
                                    </ul>
                                    <div class="tab-content">
                                        <div id="tab-1" class="tab-pane active">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Nome</label>
                                                        @Html.TextBoxFor(model => model.Nome, new { @class = "form-control", @maxlength = "50" })
                                                    </div>
                                                </div>
                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-12">
                                                        <table id="dataTables-usuarios" class="table table-striped table-bordered table-hover">
                                                            <thead>
                                                                <tr>
                                                                    <th>ID</th>
                                                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.Usuarios</th>
                                                                    <th>@SmartEnergy.Resources.UsuarioPerfilTexts.Apelido</th>
                                                                    <th>Email</th>
                                                                    <th></th>
                                                                </tr>
                                                            </thead>
                                                            <tfoot>
                                                                <tr>
                                                                    <th>ID</th>
                                                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.Usuarios</th>
                                                                    <th>@SmartEnergy.Resources.UsuarioPerfilTexts.Apelido</th>
                                                                    <th>Email</th>
                                                                    <th></th>
                                                                </tr>
                                                            </tfoot>
                                                            <tbody>

                                                                @{
                                                                    List<UsuarioDominio> usuarios = ViewBag.Usuarios;

                                                                    foreach (var usuario in usuarios)
                                                                    {
                                                                        <tr>
                                                                            <td>@usuario.IDUsuario</td>
                                                                            <td>@usuario.NomeUsuario</td>
                                                                            <td>@usuario.Apelido</td>
                                                                            <td>@usuario.Email</td>
                                                                            <td class="link_preto">
                                                                                <a href="#" class="confirm-delete-usuario"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>
                                                                            </td>
                                                                        </tr>
                                                                    }
                                                                }

                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-lg-10">
                                                        <a id="BotaoAdicionarUsuario" data-toggle="modal" href="#ModalUsuarios" class="btn btn-info pull-left" style="color:#ffffff; width:100%;">@SmartEnergy.Resources.UsuarioPerfilTexts.AdicionarUsuario</a>
                                                    </div>
                                                    <div class="col-lg-2">
                                                        <a id="BotaoExcluirTodos" href="#" onclick="javascript:Excluir_Todos();" class="btn btn-success pull-left" style="color:#ffffff; width:100%;">Excluir Todos</a>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal inmodal animated fadeIn" id="ModalUsuarios" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Fechar</span></button>
                                        <h4 class="modal-title"><i class="fa fa-check-square-o"></i>&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.SelecioneUsuario</h4>
                                    </div>
                                    <div class="modal-body">

                                        <table id="dataTables-usuarios2" class="table table-striped table-bordered table-hover">
                                            <thead>
                                                <tr>
                                                    <th><input name="select_all" value="1" type="checkbox"></th>
                                                    <th>ID</th>
                                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.Usuarios</th>
                                                    <th>@SmartEnergy.Resources.UsuarioPerfilTexts.Apelido</th>
                                                    <th>Email</th>
                                                </tr>
                                            </thead>
                                            <tfoot>
                                                <tr>
                                                    <th></th>
                                                    <th>ID</th>
                                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.Usuarios</th>
                                                    <th>@SmartEnergy.Resources.UsuarioPerfilTexts.Apelido</th>
                                                    <th>Email</th>
                                                </tr>
                                            </tfoot>
                                            <tbody>

                                                @{
                                                    List<UsuarioDominio> usuarios2 = ViewBag.Usuarios2;

                                                    foreach (var usuario in usuarios2)
                                                    {
                                                        <tr>
                                                            <td>@usuario.IDUsuario</td>
                                                            <td>@usuario.IDUsuario</td>
                                                            <td>@usuario.NomeUsuario</td>
                                                            <td>@usuario.Apelido</td>
                                                            <td>@usuario.Email</td>
                                                        </tr>
                                                    }
                                                }

                                            </tbody>
                                        </table>

                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                        <button type="button" class="btn btn-primary" onclick="fnClickAddRowUsuarios();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoInserir</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="overlay_aguarde" style="display: none">
                        <div class="spinner-aguarde">
                            <div class="sk-spinner sk-spinner-wandering-cubes">
                                <div class="sk-spinner sk-spinner-wave">
                                    <div class="sk-rect1 text-ligh"></div>
                                    <div class="sk-rect2"></div>
                                    <div class="sk-rect3"></div>
                                    <div class="sk-rect4"></div>
                                    <div class="sk-rect5"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            }

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


<script type="text/javascript">

    var rows_selected_usuarios = [];
    var names_selected_usuarios_nome = [];
    var names_selected_usuarios_apelido = [];
    var names_selected_usuarios_email = [];

    //
    // Updates "Select all" control in a data table
    //
    function updateDataTableSelectAllCtrl(table) {
        var $table = table.table().node();
        var $chkbox_all = $('tbody input[type="checkbox"]', $table);
        var $chkbox_checked = $('tbody input[type="checkbox"]:checked', $table);
        var chkbox_select_all = $('thead input[name="select_all"]', $table).get(0);

        // If none of the checkboxes are checked
        if ($chkbox_checked.length === 0) {
            chkbox_select_all.checked = false;
            if ('indeterminate' in chkbox_select_all) {
                chkbox_select_all.indeterminate = false;
            }

            // If all of the checkboxes are checked
        } else if ($chkbox_checked.length === $chkbox_all.length) {
            chkbox_select_all.checked = true;
            if ('indeterminate' in chkbox_select_all) {
                chkbox_select_all.indeterminate = false;
            }

            // If some of the checkboxes are checked
        } else {
            chkbox_select_all.checked = true;
            if ('indeterminate' in chkbox_select_all) {
                chkbox_select_all.indeterminate = true;
            }
        }
    }

    $(document).ready(function () {


        //
        // VALIDACAO DOS CAMPOS
        //

        jQuery.validator.addMethod("alphanumeric", function (value, element) {
            return this.optional(element) || /^[\'\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\+\-\_\(\)," "]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

        jQuery.validator.addMethod("numeric", function (value, element) {
            return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.numeric");

        jQuery.validator.addMethod("senha", function (value, element) {
            return this.optional(element) || /^[a-zA-Z0-9\!@@\#\$\%\&\*\_\-\+\=\,\.\?\/]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.senha");

        $("#form").validate({
            rules: {
                Nome: {
                    required: true,
                    alphanumeric: true,
                    minlength: 3,
                    maxlength: 50
                }
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            }
        });

        // desabilita campos
        disableAll();

        //
        // TABELA USUARIOS
        //

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('#dataTables-usuarios').DataTable({
            "iDisplayLength": 10,
            dom: 'tp',

            'order': [1, 'asc'],
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "5%" },
            { sWidth: "30%" },
            { sWidth: "30%" },
            { sWidth: "30%" },
            { sWidth: "10%" }
            ],

            "columnDefs": [
                 { "targets": [0], "visible": false, "searchable": false, "orderable": false },
                 { "targets": [1], "sType": "portugues" },
                 { "targets": [2], "sType": "portugues" },
                 { "targets": [3], "sType": "portugues" },
                 { "targets": [4], "searchable": false, "orderable": false },
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        //
        // TABELA USUARIOS - BUSCA
        //

        // campos de busca em cada coluna na tabela
        $('#dataTables-usuarios tfoot th').each(function () {
            var title = $(this).text();
            if (title.length > 0)
                $(this).html('<input type="text" placeholder="@SmartEnergy.Resources.UsuarioPerfilTexts.Busca ' + title + '" />');
        });

        // dataTable
        var table = $('#dataTables-usuarios').DataTable();

        // aplica a busca
        table.columns().every(function () {
            var that = this;

            $('input', this.footer()).on('keyup change', function () {
                if (that.search() !== this.value) {
                    that
                        .search(this.value)
                        .draw();
                }
            });
        });

        //
        // TABELA USUARIOS (MODAL)
        //

        var tableUsuarios2 = $('#dataTables-usuarios2').DataTable({
            "iDisplayLength": 10,
            dom: 'tp',

            'order': [2, 'asc'],
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "5%" },
            { sWidth: "20%" },
            { sWidth: "30%" },
            { sWidth: "25%" },
            { sWidth: "25%" }
            ],

            'columnDefs': [{
                'targets': 0,
                'searchable': false,
                'orderable': false,
                'className': 'dt-center',
                'render': function (data, type, full, meta) {
                    return '<input type="checkbox" name="checkbox_modal_usuarios">';
                },
            },
            {
                'targets': 1,
                'visible': false,
                'searchable': false,
                'orderable': false,
            }
            ],

            'rowCallback': function (row, data, dataIndex) {
                // Get row ID
                var rowId = data[0];

                // If row ID is in the list of selected row IDs
                if ($.inArray(rowId, rows_selected_usuarios) !== -1) {
                    $(row).find('input[type="checkbox"]').prop('checked', true);
                    $(row).addClass('selected');
                }
            },

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        // manipula checkbox
        $('#dataTables-usuarios2 tbody').on('click', 'input[type="checkbox"]', function (e) {
            var $row = $(this).closest('tr');

            // Get row data
            var data = tableUsuarios2.row($row).data();

            // Get row ID
            var rowId = data[0];
            var name_nome = data[2];
            var name_apelido = data[3];
            var name_email = data[4];

            // Determine whether row ID is in the list of selected row IDs
            var index = $.inArray(rowId, rows_selected_usuarios);

            // If checkbox is checked and row ID is not in list of selected row IDs
            if (this.checked && index === -1) {
                rows_selected_usuarios.push(rowId);
                names_selected_usuarios_nome.push(name_nome);
                names_selected_usuarios_apelido.push(name_apelido);
                names_selected_usuarios_email.push(name_email);

                // Otherwise, if checkbox is not checked and row ID is in list of selected row IDs
            } else if (!this.checked && index !== -1) {
                rows_selected_usuarios.splice(index, 1);
                names_selected_usuarios_nome.splice(index, 1);
            }

            if (this.checked) {
                $row.addClass('selected');
            } else {
                $row.removeClass('selected');
            }

            // Update state of "Select all" control
            updateDataTableSelectAllCtrl(tableUsuarios2);

            // Prevent click event from propagating to parent
            e.stopPropagation();
        });

        // manipula click na tabela
        $('#dataTables-usuarios2').on('click', 'tbody td, thead th:first-child', function (e) {
            $(this).parent().find('input[type="checkbox"]').trigger('click');
        });

        // manipula click no "Select all"
        $('thead input[name="select_all"]', tableUsuarios2.table().container()).on('click', function (e) {
            if (this.checked) {
                $('tbody input[type="checkbox"]:not(:checked)', tableUsuarios2.table().container()).trigger('click');
            } else {
                $('tbody input[type="checkbox"]:checked', tableUsuarios2.table().container()).trigger('click');
            }

            // Prevent click event from propagating to parent
            e.stopPropagation();
        });

        // Handle table draw event
        tableUsuarios2.on('draw', function () {
            // Update state of "Select all" control
            updateDataTableSelectAllCtrl(tableUsuarios2);
        });

        // campos de busca em cada coluna na tabela
        $('#dataTables-usuarios2 tfoot th').each(function () {
            var title = $(this).text();
            if (title.length > 0)
                $(this).html('<input type="text" placeholder="@SmartEnergy.Resources.UsuarioPerfilTexts.Busca ' + title + '" />');
        });

        // dataTable
        var table = $('#dataTables-usuarios2').DataTable();

        // aplica a busca
        table.columns().every(function () {
            var that = this;

            $('input', this.footer()).on('keyup change', function () {
                if (that.search() !== this.value) {
                    that
                        .search(this.value)
                        .draw();
                }
            });
        });

        $('form input').on('keypress', function (e) {
            return e.which !== 13;
        });

    });

    //
    // DESABILITA CAMPOS
    //

    function disableAll() {

        var permissao = Math.ceil(@ViewBag.Permissao);

        // verifica permissao
        switch (permissao) {
            case PERMISSOES_ADMIN:      // 0 - permissao de Admin: ve e escreve em tudo
            case PERMISSOES_GERENTE:    // 1 - permissao de Gerente: ve tudo e escreve parte
            case PERMISSOES_SUPORTE:    // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
            case PERMISSOES_CONSULTOR:  // 5 - permissao de Consultor

                $("#BotaoSalvar").attr('disabled', false);
                $("#Nome").attr('disabled', false);
                $("#BotaoAdicionarUsuario").attr('disabled', false);
                $("#BotaoExcluirTodos").attr('disabled', false);
                break;

            default:
            case PERMISSOES_OPERADOR:   // 2 - permissao de Operador: ve tudo e nao pode escrever
            case PERMISSOES_PRODUCAO:   // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                break;
        }

    }

    //
    // BOTAO ADICIONA USUARIOS
    //

    function fnClickAddRowUsuarios() {

        // percorre lista de selecionados
        $.each(rows_selected_usuarios, function (index, rowId) {

            // procura na tabela se ja existe iD
            var table = $('#dataTables-usuarios').dataTable();
            row_count = table.fnGetData().length;
            var linhas = table.fnGetData();

            var achou = false;

            for (var j = 0; j < row_count; j++) {

                if (linhas[j][0] == rowId) {
                    achou = true;
                }
            }

            // insere na tabela se nao achou
            if (achou == false) {
                $('#dataTables-usuarios').dataTable().fnAddData([
                    rowId,
                    names_selected_usuarios_nome[index],
                    names_selected_usuarios_apelido[index],
                    names_selected_usuarios_email[index],
                    '<a href="#" class="confirm-delete-usuario link_preto"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>']);
            }
        });

        // apaga arrays
        rows_selected_usuarios = [];
        names_selected_usuarios_nome = [];
        names_selected_usuarios_apelido = [];
        names_selected_usuarios_email = [];

        // desmarca checkbox
        var boxes = document.getElementsByName("checkbox_modal_usuarios");
        for (var i = 0; i < boxes.length; i++)
            boxes[i].checked = false;

        var chkbox_select_all = $('thead input[name="select_all"]', $('#dataTables-usuarios2')).get(0);
        chkbox_select_all.indeterminate = false;
        chkbox_select_all.checked = false;


        $('#dataTables-usuarios').dataTable().draw();
    }

    //
    // BOTAO APAGAR USUARIO
    //

    $('#dataTables-usuarios').on('click', '.confirm-delete-usuario', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // titulo
        titulo = "Deseja excluir o usuário da lista?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            setTimeout(function () {

                // apaga linha solicitada
                $('#dataTables-usuarios').dataTable().fnDeleteRow(row);

            }, 100);

        });

    });

    function Excluir_Todos() {
        event.stopPropagation();

        // titulo
        titulo = "Deseja excluir todos os Usuários?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            setTimeout(function () {

                // apaga todos
                var oSettings = $('#dataTables-usuarios').dataTable().fnSettings();
                var iTotalRecords = oSettings.fnRecordsTotal();
                for (i = 0; i <= iTotalRecords; i++) {
                    $('#dataTables-usuarios').dataTable().fnDeleteRow(0, null, true);
                }

            }, 100);

        });
    };


    //
    // BOTAO SALVAR (SUBMIT)
    //

    $.fn.serializeObject = function () {
        var o = {};
        var a = this.serializeArray();
        $.each(a, function () {
            if (o[this.name] !== undefined) {
                if (!o[this.name].push) {
                    o[this.name] = [o[this.name]];
                }
                o[this.name].push(this.value || '');
            } else {
                o[this.name] = this.value || '';
            }
        });
        return o;
    };

    function Salvar() {

        var $form = $('#form');

        // verifica se entradas sao validas
        if (!$form.valid()) return false;


        // lista de usuarios
        var oTable = $('#dataTables-usuarios').dataTable();
        var rows = oTable.fnSettings().aoData;
        var id = 0;
        var dataArray = [];

        // percorre tabela
        $.each(rows, function (i, val) {

            // pega ID
            id = parseInt(val._aData[0]);

            dataArray.push({
                "IDUsuario": id,
            });

        });


        // deseja salvar
        swal({
            title: "Deseja salvar?",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#1ab394",
            confirmButtonText: "Salvar",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {
            event.stopPropagation();

            // aguarde
            $('.overlay_aguarde').toggle();

            // retiro campos desabilitados, pois nao envia via POST
            $(":disabled", $('#form')).removeAttr("disabled");

            // constroi estrutura com FORM e lista de usuarios
            data = { 'grupo': $form.serializeObject(), 'Usuarios': dataArray };
            data2 = JSON.stringify(data);

            $.ajax({
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                url: '/Configuracao/GrupoUsuarios_Salvar',
                data: data2,
                type: 'POST',
                success: function (data) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {
                            swal({
                                html: true,
                                title: "Erro",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // redireciona para pagina lista de grupos
                                var url = '/Configuracao/GrupoUsuarios?IDCliente=' + document.getElementById('IDCliente').value;
                                window.location.href = url;
                            });
                        }
                        else {
                            swal({
                                title: "Salvo com sucesso",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                                closeOnConfirm: true
                            }, function () {

                                // redireciona para pagina lista de grupos
                                var url = '/Configuracao/GrupoUsuarios?IDCliente=' + document.getElementById('IDCliente').value;
                                window.location.href = url;
                            });
                        }

                    }, 100);

                },
                error: function (xhr, status, error) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    alert(xhr.responseText);

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao salvar!!!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });


                    }, 100);

                }
            });
        });
    };

</script>
}
