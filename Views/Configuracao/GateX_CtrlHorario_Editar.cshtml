﻿@model SmartEnergyLib.SQL.GateX_CtrlHorario_Dominio

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.ConfiguracaoTexts.ControleHorario;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">

                    @using (Html.BeginForm("GateX_CtrlHorario_Editar", "Configuracao", FormMethod.Post, new { id = "form", role = "form" }))
                    {   
                        @Html.Hidden("Programado", Model.Programado)
                        @Html.Hidden("IDGateway", Model.IDGateway)
                        @Html.Hidden("NumCtrlGateway", Model.NumCtrlGateway)
                        @Html.Hidden("DiaSemana_Nenhum", Model.DiaSemana_Nenhum)

                        <div class="row">
                            <div class="col-lg-12">
                                <div class="panel panel-success">
                                    <div class="panel-heading">

                                        @{
                                            GatewaysDominio gateway = ViewBag.Gateway;
                                            @Html.Hidden("IDGateway", gateway.IDGateway);
                                        }

                                        <h4>Gateway</h4><br />
                                        @gateway.Nome

                                        @{
                                            int IDTipoAcesso = ViewBag._IDTipoAcesso;

                                            if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                            {
                                                <span> [@gateway.IDGateway]</span>
                                            }
                                        }

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="panel panel-title">
                            <div class="panel-heading">
                                <div style="text-align:center;">
                                    <h4>@SmartEnergy.Resources.ConfiguracaoTexts.ControleHorario</h4>
                                </div>
                                <div class="pull-left relat-navega">
                                    <h4>
                                @{
                                    if (Model.NumCtrlGateway != 0)
                                    {
                                        <a class="link_primeira">
                                            <i class="fa fa-angle-double-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Primeira></i>
                                        </a>
                                        <a class="link_prog_menos">
                                            <i class="fa fa-angle-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Anterior></i>
                                        </a>
                                    }

                                    if (Model.NumCtrlGateway != 149)
                                    {   
                                        <a class="link_prog_mais">
                                            <i class="fa fa-angle-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Proxima></i>
                                        </a>
                                        <a class="link_ultima">
                                            <i class="fa fa-angle-double-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Ultima></i>
                                        </a>
                                    }   
                                }
                                    </h4>
                                </div>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="form-group col-lg-2">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.NumControleHorario</label>
                                        @Html.TextBoxFor(model => model.NumCtrlGateway, new { @class = "form-control", @disabled = "disabled" })
                                    </div>
                                    <div class="col-lg-offset-4 col-lg-6">
                                        <div class="ibox-tools">
                                            <button type="button" class="btn btn-primary" onclick="Programar();" disabled="" id="BotaoProgramar">@SmartEnergy.Resources.ComumTexts.BotaoProgramar</button>
                                            <a href='@("/Configuracao/GateX_CtrlHorario?IDGateway=" + @Model.IDGateway + "&Origem=2")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                        </div>
                                    </div>
                                </div>

                                <br />

                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="panel-body">
                                            <div class="row">
                                                @{                                                                                                         
                                                    // le saidas gateway
                                                    List<GateX_SD_Lista_Dominio> saidas = ViewBag.SaidasDigitais;

                                                    foreach (GateX_SD_Lista_Dominio saida in saidas)
                                                    {
                                                        if (!saida.Programado)
                                                        {
                                                            saida.DescricaoNum = saida.DescricaoNum + " [Disponível]";
                                                        }
                                                    }
                                                    
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.SaidaDigital</label>
                                                        @Html.DropDownListFor(model => model.Saida, new SelectList(saidas, "NumSaidaGateway", "DescricaoNum").OrderBy(x => x.ToString()), string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.SupervisaoTexts.Saida), new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                }
                                                
                                                <div class="form-group form-inline col-lg-2">
                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.HoraLiga</label><br />
                                                    <div style="margin-top:5px;">
                                                        @Html.TextBoxFor(model => model.HoraLiga, new { @class = "form-control", data_mask = "99", style = "width:3em;", @disabled = "disabled", @Value = String.Format("{0:00}", Model.HoraLiga) })
                                                        &nbsp;:&nbsp;
                                                        @Html.TextBoxFor(model => model.MinutoLiga, new { @class = "form-control", data_mask = "99", style = "width:3em;", @disabled = "disabled", @Value = String.Format("{0:00}", Model.MinutoLiga) })
                                                    </div>
                                                </div>
                                                <div class="form-group form-inline col-lg-2">
                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.HoraDesliga</label><br />
                                                    <div style="margin-top:5px;">
                                                        @Html.TextBoxFor(model => model.HoraDesliga, new { @class = "form-control", data_mask = "99", style = "width:3em;", @disabled = "disabled", @Value = String.Format("{0:00}", Model.HoraDesliga) })
                                                        &nbsp;:&nbsp;
                                                        @Html.TextBoxFor(model => model.MinutoDesliga, new { @class = "form-control", data_mask = "99", style = "width:3em;", @disabled = "disabled", @Value = String.Format("{0:00}", Model.MinutoDesliga) })
                                                    </div>
                                                </div>
                                            </div>

                                            <br />
                                            <br />
                                            
                                            <div class="row">
                                                <div class="form-group col-lg-12">
                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.DiasSemana</label><br />
                                                    <div class="i-checks" style="margin-top:6px;">
                                                        @Html.CheckBoxFor(model => model.DiaSemana_Domingo, new { id = "dom", name = "dom", @class = "required-one" })&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.dom</span>
                                                        @Html.CheckBoxFor(model => model.DiaSemana_Segunda, new { id = "seg", name = "seg", @class = "required-one" })&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.seg</span>
                                                        @Html.CheckBoxFor(model => model.DiaSemana_Terca, new { id = "ter", name = "ter", @class = "required-one" })&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.ter</span>
                                                        @Html.CheckBoxFor(model => model.DiaSemana_Quarta, new { id = "qua", name = "qua", @class = "required-one" })&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.qua</span>
                                                        @Html.CheckBoxFor(model => model.DiaSemana_Quinta, new { id = "qui", name = "qui", @class = "required-one" })&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.qui</span>
                                                        @Html.CheckBoxFor(model => model.DiaSemana_Sexta, new { id = "sex", name = "sex", @class = "required-one" })&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.sex</span>
                                                        @Html.CheckBoxFor(model => model.DiaSemana_Sabado, new { id = "sab", name = "sab", @class = "required-one" })&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.sab</span>
                                                        @Html.CheckBoxFor(model => model.DiaSemana_Feriado, new { id = "fer", name = "fer", @class = "required-one" })&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.fer</span>
                                                        @Html.CheckBoxFor(model => model.DiaSemana_Especial, new { id = "esp", name = "esp", @class = "required-one" })&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.esp</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <div>
                                                        <button type="button" class="btn btn-info" onclick="Segunda_Sexta();" disabled="" id="BotaoSegunda_Sexta">@SmartEnergy.Resources.ComumTexts.segunda - @SmartEnergy.Resources.ComumTexts.sexta</button>
                                                        <button type="button" class="btn btn-info" onclick="TodosDias();" disabled="" id="BotaoTodosDias">@SmartEnergy.Resources.ComumTexts.TodosDias</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="overlay_aguarde" style="display: none">
                            <div class="spinner-aguarde">
                                <div class="sk-spinner sk-spinner-wandering-cubes">
                                    <div class="sk-spinner sk-spinner-wave">
                                        <div class="sk-rect1 text-ligh"></div>
                                        <div class="sk-rect2"></div>
                                        <div class="sk-rect3"></div>
                                        <div class="sk-rect4"></div>
                                        <div class="sk-rect5"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
    @Styles.Render("~/plugins/select2Styles")
    @Styles.Render("~/datetimePickerStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/plugins/iCheck")
    @Scripts.Render("~/plugins/select2")
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        $(document).ready(function () {

            // classe iChecks
            $('.i-checks').iCheck({
                checkboxClass: 'icheckbox_square-green',
                radioClass: 'iradio_square-green',
            }); ''

            //
            // VALIDACAO DOS CAMPOS
            //
            
            jQuery.validator.addMethod("alphanumeric", function (value, element) {
                return this.optional(element) || /^[\'\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\&\+\-\_\(\)," "]+$/i.test(value);
            }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

            jQuery.validator.addMethod("numeric", function (value, element) {
                return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
            }, "@SmartEnergy.Resources.ValidateTexts.numeric");

            jQuery.validator.addMethod("time", function (value, element) {
                return this.optional(element) || /^(([0-1]?[0-9])|([2][0-3])):([0-5]?[0-9])(:([0-5]?[0-9]))?$/i.test(value);
            }, "@SmartEnergy.Resources.ValidateTexts.horario");

            jQuery.validator.addMethod('required-one', function (value) {
                return $('.required-one:checked').size() > 0;
            }, "@SmartEnergy.Resources.ValidateTexts.checkbox");

            var checkboxes = $('.required-one');
            var checkbox_names = $.map(checkboxes, function (e, i) { return $(e).attr("name") }).join(" ");

            $("#form").validate({
                groups: { checks: checkbox_names },
                errorPlacement: function (error, element) {
                    if (element.attr("type") == "checkbox")
                        error.insertAfter('.i-checks');
                    else
                        error.insertAfter(element);
                },
                rules: {
                    Saida: {
                        required: true,
                    },

                    HoraLiga: {
                        required: true,
                        min: 0,
                        max: 24,
                        numeric: true
                    },
                    MinutoLiga: {
                        required: true,
                        min: 0,
                        max: 59,
                        numeric: true
                    },
                    HoraDesliga: {
                        required: true,
                        min: 0,
                        max: 24,
                        numeric: true
                    },
                    MinutoDesliga: {
                        required: true,
                        min: 0,
                        max: 59,
                        numeric: true
                    },
                },
                highlight: function (element) {
                    $(element).closest('.control-group').removeClass('success').addClass('error');
                },
                unhighlight: function (element) {
                    $(element).closest('.control-group').addClass('valid success').removeClass('error');
                },
            });

            
            // desabilita campos
            disableAll();

            $('form input').on('keypress', function (e) {
                return e.which !== 13;
            });

        });

        function disableAll() {

            var permissao = Math.ceil(@ViewBag.Permissao);

            // verifica permissao
            switch (permissao) {
                case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
                case PERMISSOES_GERENTE:            // 1 - permissao de Gerente: ve tudo e escreve parte
                case PERMISSOES_OPERADOR:           // 2 - permissao de Operador: ve tudo e nao pode escrever
                case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
                case PERMISSOES_CONSULTOR:          // 5 - permissao de Consultor
                case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte 

                    $("#BotaoProgramar").attr('disabled', false);
                    $("#HoraLiga").attr('disabled', false);
                    $("#MinutoLiga").attr('disabled', false);
                    $("#HoraDesliga").attr('disabled', false);
                    $("#MinutoDesliga").attr('disabled', false);

                    $("#Saida").attr('disabled', false);
                    $("#BotaoTodosDias").attr('disabled', false);
                    $("#BotaoSegunda_Sexta").attr('disabled', false);

                    break;

                default:
                case PERMISSOES_PRODUCAO:           // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                    $("#BotaoProgramar").attr('disabled', true);
                    break;
            }
        }

        // verifica se os dias de segunda a sexta estão selecionados
        function Segunda_Sexta() {
            
            if (seg.checked && ter.checked && qua.checked && qui.checked && sex.checked) {
                $('#seg').iCheck('uncheck');
                $('#ter').iCheck('uncheck');
                $('#qua').iCheck('uncheck');
                $('#qui').iCheck('uncheck');
                $('#sex').iCheck('uncheck');
            }
            else {
                $('#seg').iCheck('check');
                $('#ter').iCheck('check');
                $('#qua').iCheck('check');
                $('#qui').iCheck('check');
                $('#sex').iCheck('check');
            }
            $('#sab').iCheck('uncheck');
            $('#dom').iCheck('uncheck');
            $('#fer').iCheck('uncheck');
            $('#esp').iCheck('uncheck');
        }

        // verifica se todos os dias estão selecionados
        function TodosDias() {

            if (seg.checked && ter.checked && qua.checked && qui.checked && sex.checked && sab.checked && dom.checked && fer.checked && esp.checked) {
                $('.i-checks').iCheck('uncheck');
            }
            else {
                $('.i-checks').iCheck('check');
            }
        }

        $(".link_primeira").off().on("click", function (event) {
            event.stopPropagation();

            // primeira programacao
            SalvaNavega(0);
        });

        $(".link_ultima").off().on("click", function (event) {
            event.stopPropagation();

            // ultima programacao
            SalvaNavega(149);
        });

        $(".link_prog_menos").off().on("click", function (event) {
            event.stopPropagation();

            var NumCtrlGateway = parseInt(document.getElementById("NumCtrlGateway").value);

            // programacao anterior
            SalvaNavega(NumCtrlGateway - 1);
        });

        $(".link_prog_mais").off().on("click", function (event) {
            event.stopPropagation();

            var NumCtrlGateway = parseInt(document.getElementById("NumCtrlGateway").value);

            // proxima programacao
            SalvaNavega(NumCtrlGateway + 1);
        });


        function SalvaNavega(navega) {

            var verificaDias = false;

            // verifica se algum dia foi selecionado
            $('input:checkbox').each(function () {

                if ($(this).is(':checked')) {
                    verificaDias = true;

                    // para loop
                    return false;
                }
            });

            if (!verificaDias) {
                // nenhum dia selecionado
                document.getElementById('DiaSemana_Nenhum').value = true;

                // redireciona
                var IDGateway = document.getElementById('IDGateway').value;
                var url = '/Configuracao/GateX_CtrlHorario_Editar?IDGateway=' + IDGateway + '&NumCtrlGateway=' + navega;

                window.location.href = url;
            }

            else {
                document.getElementById('DiaSemana_Nenhum').value = false;

                var $form = $('#form');

                // verifica se programacoes sao validas
                if (!$form.valid()) return false;
                
                event.stopPropagation();

                // aguarde
                $('.overlay_aguarde').toggle();

                // retiro campos desabilitados, pois nao envia via POST
                $(":disabled", $('#form')).removeAttr("disabled");

                $.ajax({
                    url: '/Configuracao/GateX_CtrlHorario_Programar',
                    data: $form.serialize(),
                    type: 'POST',
                    success: function (data) {

                        setTimeout(function () {

                            // verifica se erro
                            if (data.status == "ERRO") {
                                swal({
                                    title: "Erro",
                                    type: "warning",
                                    text: data.erro,
                                    confirmButtonColor: "#f8ac59",
                                    confirmButtonText: "Fechar",
                                }, function () {

                                    // redireciona
                                    var IDGateway = document.getElementById('IDGateway').value;
                                    var url = '/Configuracao/GateX_CtrlHorario?IDGateway=' + IDGateway + '&Origem=2';

                                    window.location.href = url;
                                });
                            }
                            else {

                                // redireciona
                                var IDGateway = document.getElementById('IDGateway').value;
                                var url = '/Configuracao/GateX_CtrlHorario_Editar?IDGateway=' + IDGateway + '&NumCtrlGateway=' + navega;

                                window.location.href = url;
                            }

                        }, 100);
                    },
                    error: function (xhr, status, error) {

                        setTimeout(function () {

                            swal({
                                title: "Erro",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // redireciona
                                var IDGateway = document.getElementById('IDGateway').value;
                                var url = '/Configuracao/GateX_CtrlHorario?IDGateway=' + IDGateway + '&Origem=2';

                                window.location.href = url;
                            });
                        }, 100);
                    },
                });
            }
        };


        function Programar() {

            var verificaDias = false;

            // verifica se algum dia foi selecionado
            $('input:checkbox').each(function () {

                if ($(this).is(':checked')) {
                    verificaDias = true;

                    // para loop
                    return false;
                }
            });

            if (!verificaDias) {
                // nenhum dia selecionado
                document.getElementById('DiaSemana_Nenhum').value = true;
            }

            else {
                document.getElementById('DiaSemana_Nenhum').value = false;
            }

            var $form = $('#form');

            // verifica se programacoes sao validas
            if (!$form.valid()) return false;

            // retiro campos desabilitados, pois nao envia via POST
            $(":disabled", $('#form')).removeAttr("disabled");

            $.ajax({
                url: '/Configuracao/GateX_CtrlHorario_Programar',
                data: $form.serialize(),
                type: 'POST',
                success: function (data) {

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {
                            swal({
                                title: "Erro",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // redireciona
                                var IDGateway = document.getElementById('IDGateway').value;
                                var url = '/Configuracao/GateX_CtrlHorario?IDGateway=' + IDGateway + '&Origem=2';

                                window.location.href = url;
                            });
                        }
                        else {

                            // redireciona
                            var IDGateway = document.getElementById('IDGateway').value;
                            var url = '/Configuracao/GateX_CtrlHorario?IDGateway=' + IDGateway + '&Origem=2';

                            window.location.href = url;
                        }

                    }, 100);

                },
                error: function (xhr, status, error) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao Programar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // redireciona
                            var IDGateway = document.getElementById('IDGateway').value;
                            var url = '/Configuracao/GateX_CtrlHorario?IDGateway=' + IDGateway + '&Origem=2';

                            window.location.href = url;
                        });
                    }, 100);
                },
            });
        };

    </script>
}
