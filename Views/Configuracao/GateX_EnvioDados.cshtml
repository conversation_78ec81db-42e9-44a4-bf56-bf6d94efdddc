﻿@model SmartEnergyLib.SQL.GateX_EnvioDados_Dominio

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.ConfiguracaoTexts.EnvioDados;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("GateX_EnvioDados", "Configuracao", new { ReturnUrl = ViewBag.ReturnUrl }, FormMethod.Post, new { id = "form", role = "form" }))
            {
                <div class="panel panel-title">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="panel panel-success">
                                    <div class="panel-heading">

                                        @{
                                            GatewaysDominio gateway = ViewBag.Gateway;
                                            @Html.Hidden("IDGateway", gateway.IDGateway);
                                            
                                            @Html.Hidden("ultimoUpload", Model.ultimoUpload);
                                            @Html.Hidden("iniUpload", Model.iniUpload);
                                            @Html.Hidden("xUpldProt", Model.xUpldProt);

                                            @Html.Hidden("iniUpload_DataTexto", Model.iniUpload_DataTexto);
                                        }

                                        <h4>Gateway</h4><br />
                                        @gateway.Nome

                                        @{
                                            int IDTipoAcesso = ViewBag._IDTipoAcesso;

                                            if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                            {
                                                <span> [@gateway.IDGateway]</span>
                                            }
                                        }

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="panel panel-title">
                            <div class="panel-heading">
                                <div style="text-align:center;">
                                    <h4>@SmartEnergy.Resources.ConfiguracaoTexts.ComandoEnvioDados</h4>
                                </div>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-lg-offset-6 col-lg-6">
                                        <div class="ibox-tools">
                                            <button type="button" class="btn btn-primary" onclick="Enviar(@gateway.IDGateway);" disabled="" id="BotaoEnviar">@SmartEnergy.Resources.ComumTexts.BotaoEnviar</button>
                                            <a href='@("/Configuracao/Gateway_Editar?IDGateway=" + @gateway.IDGateway.ToString() + "#tab-6")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                        </div>
                                    </div>
                                </div>

                                <br />
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="panel-body">

                                            <div class="row">
                                                <div class="form-group col-lg-3">
                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.UltimoEnvio</label>
                                                    <input type="text" class="form-control" disabled value='@string.Format("{0:g}", Model.ultimoUpload)'>
                                                </div>
                                            </div>

                                            <br />
                                            <div class="row">
                                                <div class="form-group col-lg-3">
                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Inicio</label>
                                                    <div class="input-group" id="data_div">
                                                        <input type='text' class="form-control data" name="data" value=@ViewBag.Data>
                                                        <span class="input-group-addon">
                                                            <i class="fa fa-calendar"></i>&nbsp;&nbsp;<i class="fa fa-clock-o"></i>
                                                        </span>
                                                        <div class="input-group" id="hora_div">
                                                            <input type="text" class="form-control hora" name="hora" value=@ViewBag.Hora>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="div_FTP" style="display: none">
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;</label>
                                                        <input type="text" class="form-control" disabled value="Enviar todas as Medições">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="div_MQTT" style="display: none">
                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ComumTexts.BotaoEnviar</label>
                                                        @Html.DropDownListFor(model => model.tyUpReg, new SelectList(ViewBag.listatipoEnvioDadosReg, "ID", "Descricao", Model.tyUpReg), new { @class = "form-control", @onchange = "SelecionouEnviar()" })
                                                    </div>
                                                </div>
                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.TipoDados</label>
                                                        @Html.DropDownListFor(model => model.tyUp, new SelectList(ViewBag.listatipoEnvioDados, "ID", "Descricao", Model.tyUp), new { @class = "form-control", @onchange = "SelecionouTipoDados()" })
                                                    </div>
                                                    <div class="div_MQTT_medicao">
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Medicao</label>
                                                            @Html.DropDownListFor(model => model.iMd, new SelectList(ViewBag.listaMedicoes, "ID", "Descricao", Model.iMd), new { @class = "form-control" })
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <br />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="overlay_enviando" style="display: none">
                            <div class="fa fa-upload icone_enviando">
                            </div>
                            <div class="text_enviando some_desktop">
                                <span>@SmartEnergy.Resources.ConfiguracaoTexts.EnviaConfig</span>
                            </div>
                            <div class="spinner-enviando">
                                <div class="sk-spinner sk-spinner-wandering-cubes">
                                    <div class="sk-spinner sk-spinner-wave">
                                        <div class="sk-rect1 text-ligh"></div>
                                        <div class="sk-rect2"></div>
                                        <div class="sk-rect3"></div>
                                        <div class="sk-rect4"></div>
                                        <div class="sk-rect5"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            }

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/datetimePickerStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/declaracoes/Constants")
    @Scripts.Render("~/declaracoes/Validates")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        $(document).ready(function () {

            $('#data_div .data').datetimepicker({
                locale: 'pt-BR',
                format: 'DD/MM/YYYY',
                allowInputToggle: true,
                showTodayButton: true
            });

            $('#hora_div').datetimepicker({
                locale: 'pt-BR',
                format: 'HH:mm',
                allowInputToggle: true
            });

            //
            // VALIDACAO DOS CAMPOS
            //
            Validator_AddMethods();

            $("#form").validate({
                rules: {


                },
                highlight: function (element) {
                    $(element).closest('.control-group').removeClass('success').addClass('error');
                },
                unhighlight: function (element) {
                    $(element).closest('.control-group').addClass('valid success').removeClass('error');
                }
            });

            // desabilita campos
            disableAll();

            // retornos da solicitacao da configuracao
            var solicitaProg = (@ViewBag.SolicitaProg.ToString().ToLower());
            var solicitaDrv = (@ViewBag.SolicitaDrv.ToString().ToLower());

            // verifico se retornou erro
            if  (solicitaProg == false || solicitaDrv == false)
            {
                $("#BotaoEnviar").attr('disabled', true);

                swal({
                    title: "Erro na recepção do comando!",
                    type: "warning",
                    confirmButtonColor: "#1ab394",
                    confirmButtonText: "Fechar",
                    closeOnConfirm: true
                }, function(isConfirm){

                    var IDGateway = parseInt(document.getElementById("IDGateway").value);

                    // retorna para a pagina editar
                    var url = '/Configuracao/Gateway_Editar?IDGateway=' + IDGateway + '#tab-6';
                    window.location.href = url;
                });
            }

            $('form input').on('keypress', function (e) {
                return e.which !== 13;
            });

        });

        function disableAll() {

            var permissao = Math.ceil(@ViewBag.Permissao);

            // verifica permissao
            switch (permissao) {
                case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
                case PERMISSOES_GERENTE:            // 1 - permissao de Gerente: ve tudo e escreve parte
                case PERMISSOES_OPERADOR:           // 2 - permissao de Operador: ve tudo e nao pode escrever
                case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
                case PERMISSOES_CONSULTOR:          // 5 - permissao de Consultor
                case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte 

                    // verifica tipo do protocolo
                    var xUpldProt = parseInt(document.getElementById("xUpldProt").value);

                    if (xUpldProt == 0)
                    {
                        $('.div_FTP').css("display", "block");
                        $('.div_MQTT').css("display", "none");
                    }
                    else
                    {
                        $('.div_FTP').css("display", "none");
                        $('.div_MQTT').css("display", "block");
                    }

                    $("#BotaoEnviar").attr('disabled', false);
                    break;

                default:
                case PERMISSOES_PRODUCAO:           // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                    $("#BotaoEnviar").attr('disabled', true);
                    break;
            }
        }

        function SelecionouEnviar() {

            // enviar
            var tyUpReg = document.getElementById('tyUpReg').value;

            // verifica se 96 registros
            if (tyUpReg == 0)
            {
                $(".data").attr('disabled', false);
                $(".hora").attr('disabled', false);
            }
            else
            {
                $(".data").attr('disabled', true);
                $(".hora").attr('disabled', true);
            }
        }

        function SelecionouTipoDados() {

            // IDGateway
            var IDGateway = parseInt(document.getElementById('IDGateway').value);

            // tipo de dados
            var tyUp = parseInt(document.getElementById('tyUp').value);

            // verifica se supervisão do equipamento ou eventos
            if (tyUp == 0x10 || tyUp == 0x20)
            {
                $('.div_MQTT_medicao').css("display", "none");
            }
            else
            {
                $('.div_MQTT_medicao').css("display", "block");

                // chama a Action para popular as medições
                $.getJSON('/Configuracao/GateX_EnvioDados_ObterMedicoes', { 'IDGateway': IDGateway, 'tyUp': tyUp }, function (data) {

                    // remove os dados que ja possui das cidades
                    $('#iMd option').remove();

                    // default
                    $('#iMd').append('<option value="">Selecione Medição</option>');

                    // popula os options com os valores retornados em JSON
                    for (var i = 0; i < data.length; i++) {
                        $('#iMd').append('<option value="' +
                            data[i].ID + '"> ' +
                            data[i].Descricao + '</option>');
                    }
                });
            }
        }

        function Enviar(IDGateway) {

            var $form = $('#form');

            var IsTabValid = $("#form").valid();;

            // verifica se entradas sao validas
            if (!IsTabValid) return false;

            swal({
                title: "Deseja enviar?",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#1ab394",
                confirmButtonText: "Enviar",
                cancelButtonText: "Cancelar",
                closeOnConfirm: true
            }, function () {
                event.stopPropagation();

                // data e hora
                document.getElementById("iniUpload_DataTexto").value = document.querySelector('[name="data"]').value + " " + document.querySelector('[name="hora"]').value;

                // aguarde
                $('.overlay_enviando').toggle();

                // retiro campos desabilitados, pois nao envia via POST
                $(":disabled", $('#form')).removeAttr("disabled");

                $.ajax({
                    url: '/Configuracao/GateX_EnvioDados_Enviar',
                    data: $form.serialize(),
                    type: 'POST',
                    success: function (data) {

                        // fim
                        $('.overlay_enviando').toggle();

                        setTimeout(function () {

                            // verifica se erro
                            if (data.status == "ERRO") {
                                swal({
                                    title: "Erro",
                                    text: data.erro,
                                    type: "warning",
                                    confirmButtonColor: "#f8ac59",
                                    confirmButtonText: "Fechar",
                                }, function () {

                                    // retorna para comando
                                    var url = '/Configuracao/GateX_EnvioDados?IDGateway=' + IDGateway;

                                    window.location.href = url;
                                });
                            }
                            else {
                                swal({
                                    title: "Enviado com sucesso",
                                    type: "success",
                                    confirmButtonColor: "#18a689",
                                    confirmButtonText: "Fechar",
                                }, function () {

                                    // retorna para pagina de edicao da gateway
                                    var url = '/Configuracao/Gateway_Editar?IDGateway=' + IDGateway + '#tab-6';

                                    window.location.href = url;

                                });
                            }
                            
                        }, 100);

                    },
                    error: function (xhr, status, error) {

                        // fim
                        $('.overlay_enviando').toggle();

                        setTimeout(function () {

                            swal({
                                title: "Erro",
                                text: "Falha no envio da configuração!",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // retorna para comando
                                var url = '/Configuracao/GateX_EnvioDados?IDGateway=' + IDGateway;

                                window.location.href = url;
                            });
                        }, 100);
                    },
                });
            });
        };

    </script>
}
