﻿@using SmartEnergyLib.SQL

<head>
    
    <title>@SmartEnergy.Resources.ConfiguracaoTexts.ControleDemMed</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

    <link href="@Server.MapPath("~/Content/bootstrap.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/style.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/Relatorios.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/RelatoriosPDF.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/fonts/font-awesome/css/font-awesome.min.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/fonts/elusive-icons-2.0.0/css/elusive-icons.min.css")" rel="stylesheet" />

    <script type="text/javascript" src="@Server.MapPath("~/Scripts/jquery-2.1.1.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/dataTables/datatables.min.js")" charset="utf-8"></script>
    
    <style>
        td {
            font-size: 10px;
        }

        th {
            font-size: 10px;
        }

        tr {
            page-break-inside: avoid;
        }

    </style>

</head>

@{
    string arquivo = "~/Logos/" + ViewBag._LogoConsultor;
    string logo = Server.MapPath(arquivo);
}

<body>

    @{
        List<GateX_CtrlDemMed_Dominio> ctrls = ViewBag.CtrlDemMed;
        List<GateX_CtrlDemMed_SD_Dominio> ctrlSDs = ViewBag.CtrlDemMed_SD;

        List<GateX_MedEner_Lista_Dominio> medEner_Lista = ViewBag.medEner_Lista;

        List<GateX_SD_Lista_Dominio> saidasDigitais = ViewBag.SaidasDigitais;
    }

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="@logo" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        <p><b>@ViewBag.NomeGateway</b></p>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.ConfiguracaoTexts.ControleDemMed</h4>
                        <p>@ViewBag.DataTextoAtual</p>
                    </div>
                </div>
            </div>
        </div>
        
        <hr />

        <div class="row">
            <div class="col-lg-12">
                <table class="table table-striped table-bordered table-hover dataTables-ctrl">
                    <thead>
                        <tr>
                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Controle</th>
                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Medicoes</th>
                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.SaidasDigitais</th>
                            <th>Nível de Ligamento</th>
                            <th>Nível de Desligamento</th>
                        </tr>
                    </thead>
                    <tbody>

                        @{
                            if (ctrls != null && ctrlSDs != null)
                            {
                                foreach (GateX_CtrlDemMed_Dominio ctrl in ctrls)
                                {
                                    if (ctrl.Programado)
                                    {
                                        foreach (GateX_CtrlDemMed_SD_Dominio sd in ctrlSDs)
                                        {
                                            if (sd.Programado && sd.NumCtrlGateway == ctrl.NumCtrlGateway)
                                            {
                                                string str_medicao = "---";

                                                if (medEner_Lista != null)
                                                {
                                                    str_medicao = string.Format("[{0:00}] {1}", ctrl.Medicao, medEner_Lista[ctrl.Medicao].Descricao);
                                                }
                                                else
                                                {
                                                    str_medicao = string.Format("[{0:00}] Medição de Energia {1}", ctrl.Medicao, ctrl.Medicao);
                                                }

                                                string aTy2On = "---";
                                                string aAx2On = "";
                                                string aTy2Of = "---";
                                                string aAx2Of = "";

                                                List<ListaTiposDominio> listatipoCmdAlarme = ViewBag.listaTipoCmdAlarme;
                                                List<ListaTiposDominio> listatipoAlarmeSistema = ViewBag.listatipoAlarmeSistema;
                                                List<ListaTiposDominio> listatipoCtrPj = ViewBag.listatipoCtrPj;
                                                List<ListaTiposDominio> listatipoCtrFPInd = ViewBag.listatipoCtrFPInd;
                                                List<ListaTiposDominio> listatipoCtrFPCap = ViewBag.listatipoCtrFPCap;
                                                List<ListaTiposDominio> listaAlmUsuario = ViewBag.listaAlmUsuario;
                                               
                                                if (listatipoCmdAlarme != null) 
                                                {
                                                    // saidas ligadas
                                                    aTy2On = listatipoCmdAlarme.Find(x => x.ID == ctrl.AlarmeTipoAllOn).Descricao;

                                                    // alarme
                                                    switch (ctrl.AlarmeTipoAllOn)
                                                    {
                                                        case 255: // alm usuario
                                                            if (listaAlmUsuario != null)
                                                            {
                                                                aAx2On = listaAlmUsuario.Find(x => x.ID == ctrl.AlarmeAuxAllOn).Descricao;
                                                            }
                                                            break;

                                                        case 0: // alm sistema
                                                            if (listatipoAlarmeSistema != null) 
                                                            {
                                                                aAx2On = listatipoAlarmeSistema.Find(x => x.ID == ctrl.AlarmeAuxAllOn).Descricao;
                                                            }
                                                            break;

                                                        case 1: // falta pulsos
                                                            if (medEner_Lista != null) 
                                                            {
                                                                aAx2On = medEner_Lista.Find(x => x.NumMedicaoGateway == ctrl.AlarmeAuxAllOn).Descricao;
                                                            }
                                                            break;

                                                        case 2: // ultrapassagem de demanda
                                                            if (listatipoCtrPj != null)
                                                            {
                                                                aAx2On = listatipoCtrPj.Find(x => x.ID == ctrl.AlarmeAuxAllOn).Descricao;
                                                            }
                                                            break;

                                                        case 3: // ultrapassagem fator de potencia indutivo
                                                            if (listatipoCtrFPInd != null)
                                                            {
                                                                aAx2On = listatipoCtrFPInd.Find(x => x.ID == ctrl.AlarmeAuxAllOn).Descricao;
                                                            }
                                                            break;

                                                        case 4: // ultrapassagem fator de potencia capacitivo
                                                            if (listatipoCtrFPCap != null) 
                                                            {
                                                                aAx2On = listatipoCtrFPCap.Find(x => x.ID == ctrl.AlarmeAuxAllOn).Descricao;
                                                            }
                                                            break;

                                                        case 5: // comando supervisionado
                                                            if (saidasDigitais != null)
                                                            {
                                                                aAx2On = saidasDigitais.Find(x => x.NumSaidaGateway == ctrl.AlarmeAuxAllOn).Descricao;
                                                            }
                                                            break;
                                                    }
                                                    
                                                    // saidas desligadas
                                                    aTy2Of = listatipoCmdAlarme.Find(x => x.ID == ctrl.AlarmeTipoAllOff).Descricao;

                                                    // alarme
                                                    switch (ctrl.AlarmeTipoAllOff)
                                                    {
                                                        case 255: // alm usuario
                                                            if (listaAlmUsuario != null)
                                                            {
                                                                aAx2Of = listaAlmUsuario.Find(x => x.ID == ctrl.AlarmeAuxAllOff).Descricao;
                                                            }
                                                            break;

                                                        case 0: // alm sistema
                                                            if (listatipoAlarmeSistema != null) 
                                                            {
                                                                aAx2Of = listatipoAlarmeSistema.Find(x => x.ID == ctrl.AlarmeAuxAllOff).Descricao;
                                                            }
                                                            break;

                                                        case 1: // falta pulsos
                                                            if (medEner_Lista != null) 
                                                            {
                                                                aAx2Of = medEner_Lista.Find(x => x.NumMedicaoGateway == ctrl.AlarmeAuxAllOff).Descricao;
                                                            }
                                                            break;

                                                        case 2: // ultrapassagem de demanda
                                                            if (listatipoCtrPj != null)
                                                            {
                                                                aAx2Of = listatipoCtrPj.Find(x => x.ID == ctrl.AlarmeAuxAllOff).Descricao;
                                                            }
                                                            break;

                                                        case 3: // ultrapassagem fator de potencia indutivo
                                                            if (listatipoCtrFPInd != null)
                                                            {
                                                                aAx2Of = listatipoCtrFPInd.Find(x => x.ID == ctrl.AlarmeAuxAllOff).Descricao;
                                                            }
                                                            break;

                                                        case 4: // ultrapassagem fator de potencia capacitivo
                                                            if (listatipoCtrFPCap != null) 
                                                            {
                                                                aAx2Of = listatipoCtrFPCap.Find(x => x.ID == ctrl.AlarmeAuxAllOff).Descricao;
                                                            }
                                                            break;

                                                        case 5: // comando supervisionado
                                                            if (saidasDigitais != null)
                                                            {
                                                                aAx2Of = saidasDigitais.Find(x => x.NumSaidaGateway == ctrl.AlarmeAuxAllOff).Descricao;
                                                            }
                                                            break;
                                                    }
                                                }
                                                
                                                string str_saida = "";
                                                if (saidasDigitais != null)
                                                {
                                                    str_saida = string.Format("[{0}] {1}", sd.NumSaida, saidasDigitais.Find(x => x.NumSaidaGateway == sd.NumSaida).Descricao);
                                                }

                                                string nivel_lig = string.Format("{0:0.0}", sd.MdLig / 10.0);
                                                string nivel_des = string.Format("{0:0.0}", sd.MdDes / 10.0);
                                                
                                                <tr>
                                                    <td>@ctrl.NumCtrlGateway.ToString()</td>
                                                    <td>@str_medicao</td>
                                                    <td>@str_saida</td>
                                                    <td>@nivel_lig</td>
                                                    <td>@nivel_des</td>
                                                </tr>
                                            }
                                        }
                                    }
                                }
                            }
                        }

                    </tbody>
                </table>

            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-offset-12" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>

<script type="text/javascript">

    $(document).ready(function () {


        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {
                var x = (a == "---") ? -1 : a.replace(/,/, ".");
                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-ctrl').DataTable({
            "iDisplayLength": 10000,
            dom: 't',

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "numero" },
                        { "aTargets": [1], "sType": "portugues" },
                        { "aTargets": [2], "sType": "portugues" },
                        { "aTargets": [3], "sType": "numero" },
                        { "aTargets": [4], "sType": "numero" },

            ],

            "aoColumns": [
                { sWidth: "5%" },
                { sWidth: "27%" },
                { sWidth: "27%" },
                { sWidth: "20%" },
                { sWidth: "20%" },
            ],
            'order': [0, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

    });

</script>
