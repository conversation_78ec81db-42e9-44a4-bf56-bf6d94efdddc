﻿@model SmartEnergyLib.SQL.GateX_SD_Dominio

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoSaidasDigitais;
}
 
<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">

                    @using (Html.BeginForm("GateX_SaidaDigital_Editar", "Configuracao", FormMethod.Post, new { id = "form", role = "form" }))
                    {
                        @Html.Hidden("Programado", Model.Programado)
                        @Html.Hidden("IDGateway", Model.IDGateway)
                        @Html.Hidden("NumSaidaGateway", Model.NumSaidaGateway)

                        @Html.Hidden("Remota", Model.Remota)
                        @Html.Hidden("EEAtivaSaida", Model.EEAtivaSaida)
                        @Html.Hidden("Retrosinalizador", Model.Retrosinalizador)
                        @Html.Hidden("MedicaoFalhaDeslig", Model.MedicaoFalhaDeslig)
                        @Html.Hidden("ComandoAlarmeAux", Model.ComandoAlarmeAux)

                        @Html.Hidden("TempoMinDesligado", Model.TempoMinDesligado)
                        @Html.Hidden("TempoReligar", Model.TempoReligar)
                        @Html.Hidden("TempoMinLigado", Model.TempoMinLigado)
                        @Html.Hidden("TempoMaxDesligadoP", Model.TempoMaxDesligadoP)
                        @Html.Hidden("TempoMinLigadoP", Model.TempoMinLigadoP)
                        @Html.Hidden("TempoMaxDesligadoFP", Model.TempoMaxDesligadoFP)
                        @Html.Hidden("TempoMinLigadoFP", Model.TempoMinLigadoFP)                        
                        
                        
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="panel panel-success">
                                    <div class="panel-heading">

                                        @{
                                            GatewaysDominio gateway = ViewBag.Gateway;
                                            @Html.Hidden("IDGateway", gateway.IDGateway);
                                        }

                                        <h4>Gateway</h4><br />
                                        @gateway.Nome

                                        @{
                                            int IDTipoAcesso = ViewBag._IDTipoAcesso;
                                            if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                            {
                                                <span> [@gateway.IDGateway]</span>
                                            }
                                        }

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="panel panel-title">
                            <div class="panel-heading">
                                <div style="text-align:center;">
                                    <h4>@SmartEnergy.Resources.ConfiguracaoTexts.SaidaDigital</h4>
                                </div>

                                <div class="pull-left relat-navega">
                                    <h4>
                                        @{
                                            if (Model.NumSaidaGateway != 0)
                                            {
                                                <a class="link_primeira">
                                                    <i class="fa fa-angle-double-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Primeira></i>
                                                </a>
                                                <a class="link_prog_menos">
                                                    <i class="fa fa-angle-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Anterior></i>
                                                </a>
                                            }

                                            if (Model.NumSaidaGateway != 63)
                                            {
                                                <a class="link_prog_mais">
                                                    <i class="fa fa-angle-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Proxima></i>
                                                </a>
                                                <a class="link_ultima">
                                                    <i class="fa fa-angle-double-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Ultima></i>
                                                </a>
                                            }
                                        }
                                    </h4>
                                </div>

                                <div class="pull-right relat-tools">
                                    <h4>
                                        <a data-toggle="modal" href="#ModalControlesAssociados"><i class="fa fa-tasks" data-toggle="tooltip" data-placement="left" title="Controles Associados"></i></a>
                                    </h4>
                                </div>
                            </div>

                            <div class="panel-body">
                                
                                <div class="row">
                                    <div class="form-group col-lg-2">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.NumSaidaGateway</label>
                                        @Html.TextBoxFor(model => model.NumSaidaGateway, new { @class = "form-control", @disabled = "disabled" })
                                    </div>
                                    <div class="col-lg-offset-4 col-lg-6">
                                        <div class="ibox-tools">
                                            <button type="button" class="btn btn-primary" onclick="Programar();" disabled="" id="BotaoProgramar">@SmartEnergy.Resources.ComumTexts.BotaoProgramar</button>
                                            <a href='@("/Configuracao/GateX_SaidaDigital?IDGateway=" + @Model.IDGateway + "&Origem=2")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                        </div>
                                    </div>
                                </div>

                                <div class="tabs-container">
                                    <ul class="nav nav-tabs">
                                        <li class="active"><a data-toggle="tab" href="#tab-1"><i class="fa fa-cogs"></i>@SmartEnergy.Resources.ConfiguracaoTexts.Geral</a></li>
                                        <li class="tab-2"><a data-toggle="tab" href="#tab-2"><i class="fa fa-bell-o"></i>@SmartEnergy.Resources.ConfiguracaoTexts.Alarme</a></li>
                                        <li class="tab-3"><a data-toggle="tab" href="#tab-3"><i class="fa fa-sign-out"></i>@SmartEnergy.Resources.ConfiguracoesGateX_Texts.CmdSupervisionado</a></li>
                                        <li class="tab-4"><a data-toggle="tab" href="#tab-4"><i class="fa fa-clock-o"></i>@SmartEnergy.Resources.ConfiguracoesGateX_Texts.Temporizadores</a></li>
                                    </ul>
                                    
                                    <div class="tab-content">

                                        <div id="tab-1" class="tab-pane active">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <div class="row">
                                                            <div class="form-group col-lg-5">
                                                                @{
                                                                    string descricao = "-";
    
                                                                    if (Model.Programado)
                                                                    {
                                                                        descricao = Model.Descricao;
                                                                    }
                                                                }
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Descricao</label>
                                                                @Html.TextBoxFor(model => model.Descricao, new { @class = "form-control", @disabled = "disabled", @maxlength = "32", @Value = descricao })
                                                            </div> 
                                                            <div class="form-group col-lg-offset-1 col-lg-2">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Rede</label>
                                                                @Html.DropDownListFor(model => model.RedeIO, new SelectList(ViewBag.listaTipoRedeIO, "ID", "Descricao", Model.RedeIO), new { @class = "form-control", @onchange = "SelecionouRedeIO()", @disabled = "disabled" })
                                                            </div>
                                                            <div class="form-group col-lg-2">
                                                                <div class="div_rede_r_q">
                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Remota</label>
                                                                    @Html.DropDownListFor(model => model.Remota, new SelectList(ViewBag.ListaRemotas), string.Format("Nenhum"), new { id = "remotas_r_q", @onchange = "SelecionouNumRemota()", @class = "form-control", @disabled = "disabled" })
                                                                </div>
                                                                <div class="div_rede_k" style="display: none">
                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Remota</label>
                                                                    @Html.DropDownListFor(model => model.Remota, new SelectList(ViewBag.listaTipoNumRemotaRedeK, "ID", "Descricao", Model.Remota), new { id = "remotas_k", @onchange = "SelecionouNumRemota()", @class = "form-control", @disabled = "disabled" })
                                                                </div>
                                                            </div>
                                                            <div class="div_drv_ad form-group col-lg-2">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Endereco</label>
                                                                @Html.TextBoxFor(model => model.Endereco, new { @class = "form-control", @disabled = "disabled", @max = "65000" })
                                                            </div>
                                                        </div>
                                                    
                                                        <hr />
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                <div class="i-checks" style="margin-top:6px;">
                                                                    @Html.CheckBoxFor(model => model.RegEvTemporizador)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracoesGateX_Texts.RegistrarEventosTemp</span>
                                                                    @Html.CheckBoxFor(model => model.LogicaInvertida)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracoesGateX_Texts.LogicaInvertida</span>
                                                                    @Html.CheckBoxFor(model => model.HabAcessoRemoto)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracaoTexts.AcessoRemoto</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    
                                                        <hr />
                                                        <div class="row">
                                                            @{
                                                                // le entradas gateway
                                                                List<GateX_ED_Lista_Dominio> entradas = ViewBag.Entradas;

                                                                foreach (GateX_ED_Lista_Dominio entrada in entradas)
                                                                {
                                                                    if (!entrada.Programado)
                                                                    {
                                                                        entrada.DescricaoNum = entrada.DescricaoNum + " [Disponível]";
                                                                    }
                                                                }

                                                                <div class="form-group col-lg-4">
                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracoesGateX_Texts.CmdEntrada</label>
                                                                    @Html.DropDownListFor(model => model.EEAtivaSaida, new SelectList(entradas, "NumEntradaGateway", "DescricaoNum").OrderBy(x => x.ToString()), string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.SupervisaoTexts.Entrada), new { id = "entrada_desl", @class = "form-control", @disabled = "disabled" })
                                                                </div>
                                                                <div class="form-group col-lg-4">
                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracoesGateX_Texts.CmdTarifacao</label>
                                                                    @Html.DropDownListFor(model => model.ComandoHrTarifador, new SelectList(ViewBag.listaTipoCmdTarifacao, "ID", "Descricao", Model.ComandoHrTarifador), new { @class = "form-control", @disabled = "disabled" })
                                                                </div>

                                                                // le medicoes gateway
                                                                List<GateX_MedEner_Lista_Dominio> medicoes = ViewBag.Medicoes;

                                                                foreach (GateX_MedEner_Lista_Dominio medicao in medicoes)
                                                                {
                                                                    if (!medicao.Programado)
                                                                    {
                                                                        medicao.DescricaoNum = medicao.DescricaoNum + " [Disponível]";
                                                                    }
                                                                }

                                                                <div class="form-group col-lg-4">
                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracoesGateX_Texts.DesligaFalhaMed</label>
                                                                    @Html.DropDownListFor(model => model.MedicaoFalhaDeslig, new SelectList(medicoes, "NumMedicaoGateway", "DescricaoNum").OrderBy(x => x.ToString()), string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.SupervisaoTexts.Medicoes), new { id = "med_desl", @class = "form-control", @disabled = "disabled" })
                                                                </div>
                                                            }
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div id="tab-2" class="tab-pane">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <div class="panel-title">
                                                            <div class="panel-heading">
                                                                <h4>@SmartEnergy.Resources.ConfiguracaoTexts.Alarme</h4>
                                                            </div>
                                                            <div class="panel-body">
                                                                <div class="row">
                                                                    <div class="form-group col-lg-4">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Comando</label>
                                                                        @Html.DropDownListFor(model => model.ComandoAlarme, new SelectList(ViewBag.listaTipoAlarme, "ID", "Descricao", Model.ComandoAlarme), new { @class = "form-control", @onchange = "SelecionouCmdAlarme()", @disabled = "disabled" })
                                                                    </div>
                                                                    <div class="form-group col-lg-4">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Alarme</label>
                                                                        @Html.DropDownListFor(model => model.ComandoAlarmeTipo, new SelectList(ViewBag.listaTipoCmdAlarme, "ID", "Descricao", Model.ComandoAlarmeTipo), "Nenhum", new { @onchange = "SelecionouTipoAlarme()", @class = "form-control", @disabled = "disabled" })
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-lg-8">

                                                                        <div class="div_usuario">
                                                                            @Html.DropDownListFor(model => model.ComandoAlarmeAux, new SelectList(ViewBag.listaAlmUsuario, "ID", "Descricao", Model.ComandoAlarmeAux), new { id = "alm_usuario", @class = "form-control", @disabled = "disabled" })
                                                                        </div>

                                                                        <div class="div_sistema">
                                                                            @Html.DropDownListFor(model => model.ComandoAlarmeAux, new SelectList(ViewBag.listaTipoAlarmeSistema, "ID", "Descricao", Model.ComandoAlarmeAux), "Nenhum", new { id = "alm_sistema", @class = "form-control", @disabled = "disabled" })
                                                                        </div>

                                                                        <div class="div_faltapulsos" style="display: none">
                                                                            @Html.DropDownListFor(model => model.ComandoAlarmeAux, new SelectList(medicoes, "NumMedicaoGateway", "DescricaoNum", Model.ComandoAlarmeAux).OrderBy(x => x.ToString()), new { id = "alm_faltapulsos", @class = "form-control", @disabled = "disabled" })
                                                                        </div>

                                                                        <div class="div_ultrap_demanda" style="display: none">
                                                                            @Html.DropDownListFor(model => model.ComandoAlarmeAux, new SelectList(ViewBag.listaTipoCtrPj, "ID", "Descricao", Model.ComandoAlarmeAux), new { id = "ultrap_demanda", @class = "form-control", @disabled = "disabled" })
                                                                        </div>

                                                                        <div class="div_ultrap_ind" style="display: none">
                                                                            @Html.DropDownListFor(model => model.ComandoAlarmeAux, new SelectList(ViewBag.listaTipoCtrFPInd, "ID", "Descricao", Model.ComandoAlarmeAux), new { id = "ultrap_ind", @class = "form-control", @disabled = "disabled" })
                                                                        </div>

                                                                        <div class="div_ultrap_cap" style="display: none">
                                                                            @Html.DropDownListFor(model => model.ComandoAlarmeAux, new SelectList(ViewBag.listaTipoCtrFPCap, "ID", "Descricao", Model.ComandoAlarmeAux), new { id = "ultrap_cap", @class = "form-control", @disabled = "disabled" })
                                                                        </div>

                                                                        <div class="div_comando_superv" style="display: none">
                                                                            @{
                                                                                List<GateX_SD_Dominio> saidas = ViewBag.Saidas;

                                                                                foreach (GateX_SD_Dominio saida in saidas)
                                                                                {
                                                                                    if (!saida.Programado)
                                                                                    {
                                                                                        saida.DescricaoNum = saida.DescricaoNum + " [Disponível]";
                                                                                    }
                                                                                }

                                                                                @Html.DropDownListFor(model => model.ComandoAlarmeAux, new SelectList(saidas, "NumSaidaGateway", "DescricaoNum", Model.ComandoAlarmeAux).OrderBy(x => x.ToString()), new { id = "comando_superv", @class = "form-control", @disabled = "disabled" })
                                                                            }
                                                                        </div>

                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div id="tab-3" class="tab-pane">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <div class="panel-title">
                                                            <div class="panel-heading">
                                                                <h4>@SmartEnergy.Resources.ConfiguracoesGateX_Texts.CmdSupervisionado</h4>
                                                            </div>
                                                            <div class="panel-body">
                                                                <div class="row">
                                                                    <div class="form-group col-lg-4">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.EntradaDigital</label>
                                                                        @Html.DropDownListFor(model => model.Retrosinalizador, new SelectList(entradas, "NumEntradaGateway", "DescricaoNum").OrderBy(x => x.ToString()), string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.SupervisaoTexts.Entrada), new { id = "entrada_retr", @class = "form-control", @onchange = "SelecionouEntradaEstado()", @disabled = "disabled" })
                                                                    </div>
                                                                    <div class="form-group col-lg-offset-2 col-lg-2">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracoesGateX_Texts.TempoAlarme (@SmartEnergy.Resources.ComumTexts.Segundos)</label>
                                                                        @Html.TextBoxFor(model => model.ComandoSuperv, new { @class = "form-control", @disabled = "disabled", @max = 2550 })
                                                                        <label class="control-label">&nbsp;(0 = Desabilita Alarme)</label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div id="tab-4" class="tab-pane">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <div class="panel-title">
                                                            <div class="panel-heading">
                                                                <h4>@SmartEnergy.Resources.ConfiguracoesGateX_Texts.ControleIntertravamento</h4>
                                                            </div>
                                                            <div class="panel-body">
                                                                <div class="row">
                                                                    <div class="form-group col-lg-3">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracoesGateX_Texts.TempoMinimo @SmartEnergy.Resources.ConfiguracoesGateX_Texts.Desligado (mm:ss)</label>
                                                                        @Html.TextBoxFor(model => model.TempoMinDesligado, new { id = "tr_min_d", @class = "form-control", @disabled = "disabled", data_mask = "999:99", @Value = String.Format("{0:000}:{1:00}", @ViewBag.TrMnD_min, @ViewBag.TrMnD_seg) })
                                                                    </div>
                                                                    <div class="form-group col-lg-3">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracoesGateX_Texts.TempoMaximo @SmartEnergy.Resources.ConfiguracoesGateX_Texts.Desligado (mm:ss)</label>
                                                                        @Html.TextBoxFor(model => model.TempoReligar, new { id = "tr_max_d", @class = "form-control", @disabled = "disabled", data_mask = "999:99", @Value = String.Format("{0:000}:{1:00}", @ViewBag.TrMxD_min, @ViewBag.TrMxD_seg) })
                                                                    </div>
                                                                    <div class="form-group col-lg-3">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracoesGateX_Texts.TempoMinimo @SmartEnergy.Resources.ConfiguracoesGateX_Texts.Ligado (mm:ss)</label>
                                                                        @Html.TextBoxFor(model => model.TempoMinLigado, new { id = "tr_min_l", @class = "form-control", @disabled = "disabled", data_mask = "999:99", @Value = String.Format("{0:000}:{1:00}", @ViewBag.TrMnL_min, @ViewBag.TrMnL_seg) })
                                                                    </div>
                                                                </div>

                                                                <br />

                                                                <div class="row">
                                                                    <div class="form-group col-lg-3">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracoesGateX_Texts.TempoRetardo @SmartEnergy.Resources.ConfiguracoesGateX_Texts.Desliga (@SmartEnergy.Resources.ComumTexts.Segundos)</label>
                                                                        @Html.TextBoxFor(model => model.DelayDesligar, new { @class = "form-control", @disabled = "disabled", @max = 900 })
                                                                    </div>
                                                                    <div class="form-group col-lg-3">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracoesGateX_Texts.TempoRetardo @SmartEnergy.Resources.ConfiguracoesGateX_Texts.Liga (@SmartEnergy.Resources.ComumTexts.Segundos)</label>
                                                                        @Html.TextBoxFor(model => model.DelayLigar, new { @class = "form-control", @disabled = "disabled", @max = 900 })
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <br />
                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <div class="panel-title">
                                                            <div class="panel-heading">
                                                                <h4>@SmartEnergy.Resources.ConfiguracoesGateX_Texts.ControleConsumoAx @SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                                                            </div>
                                                            <div class="panel-body">
                                                                <div class="row">
                                                                    <div class="form-group col-lg-4">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracoesGateX_Texts.TempoMaximo @SmartEnergy.Resources.ConfiguracoesGateX_Texts.Desligado @SmartEnergy.Resources.SupervisaoTexts.Ponta (mm:ss)</label>
                                                                        @Html.TextBoxFor(model => model.TempoMaxDesligadoP, new { id = "p_max_d", @class = "form-control", @disabled = "disabled", data_mask = "999:99", @Value = String.Format("{0:000}:{1:00}", @ViewBag.P_MxD_min, @ViewBag.P_MxD_seg) })
                                                                    </div>
                                                                    <div class="form-group col-lg-4">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracoesGateX_Texts.TempoMinimo @SmartEnergy.Resources.ConfiguracoesGateX_Texts.Ligado @SmartEnergy.Resources.SupervisaoTexts.Ponta (mm:ss)</label>
                                                                        @Html.TextBoxFor(model => model.TempoMinLigadoP, new { id = "p_min_l", @class = "form-control", @disabled = "disabled", data_mask = "999:99", @Value = String.Format("{0:000}:{1:00}", @ViewBag.P_Mnl_min, @ViewBag.P_Mnl_seg) })
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <br />
                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <div class="panel-title">
                                                            <div class="panel-heading">
                                                                <h4>@SmartEnergy.Resources.ConfiguracoesGateX_Texts.ControleConsumoAx @SmartEnergy.Resources.SupervisaoTexts.ForaPonta</h4>
                                                            </div>
                                                            <div class="panel-body">
                                                                <div class="row">
                                                                    <div class="form-group col-lg-4">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracoesGateX_Texts.HoraReferencia @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (HH:mm)</label>
                                                                        @Html.TextBoxFor(model => model.TempoMaxDesligadoFP_Hora, new { @class = "form-control", @disabled = "disabled", data_mask = "99:99", @Value = String.Format("{0:t}", @Model.TempoMaxDesligadoFP_Hora) })
                                                                    </div>
                                                                    <div class="form-group col-lg-4">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracoesGateX_Texts.TempoMaximo @SmartEnergy.Resources.ConfiguracoesGateX_Texts.Desligado @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (mm:ss)</label>
                                                                        @Html.TextBoxFor(model => model.TempoMaxDesligadoFP, new { id = "f_max_d", @class = "form-control", @disabled = "disabled", data_mask = "999:99", @Value = String.Format("{0:000}:{1:00}", @ViewBag.F_MxD_min, @ViewBag.F_MxD_seg) })
                                                                    </div>
                                                                    <div class="form-group col-lg-4">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracoesGateX_Texts.TempoMinimo @SmartEnergy.Resources.ConfiguracoesGateX_Texts.Ligado @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (mm:ss)</label>
                                                                        @Html.TextBoxFor(model => model.TempoMinLigadoFP, new { id = "f_min_l", @class = "form-control", @disabled = "disabled", data_mask = "999:99", @Value = String.Format("{0:000}:{1:00}", @ViewBag.F_Mnl_min, @ViewBag.F_Mnl_seg) })
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                </div>
                
                <div class="modal inmodal animated fadeIn" id="ModalControlesAssociados" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-editar-header">
                                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only modal-tools-header">Close</span></button>
                                <i class="modal-editar-title"></i><span class="modal-editar-title">&nbsp;&nbsp;Controles Associados</span>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="panel-info">
                                            <div class="panel-body" style="border: 1px solid; border-color: inherit; border-radius: 4px">
                                                <div class="row">
                                                    <div class="col-lg-12">

                                                        @{
                                                            var ctrlAss = new Dictionary<int, string>
                                                            {
                                                                {0x8000, "Manual" },
                                                                {0x0001, "Controle Demanda Projetada" },
                                                                {0x0002, "Controle Demanda Média" },
                                                                {0x0004, "Controle Demanda Acumulada" },
                                                                {0x0008, "Controle Fator de Potência" },
                                                                {0x0800, "Controle Analógico" },
                                                                {0x0020, "Controle Horário" },
                                                                {0x0010, "Comando Horário de Tarifação" },
                                                                {0x0100, "Entrada Digital" },
                                                                {0x0040, "Falta de Pulsos na Medição" },
                                                                {0x0080, "Alarme" },
                                                                {0x0400, "Lógica" },
                                                                {0x4000, "Temporização" },
                                                                {0x0200, "Acesso Remoto" }
                                                            };

                                                            foreach (var ctrl in ctrlAss)
                                                            {
                                                                if ((Model.cAss & ctrl.Key) != 0)
                                                                {
                                                                    string nome = ctrl.Value;
                                                                    string classe = "badge-danger";
                                                                    string titulo = "[Desligado]";

                                                                    if ((Model.rAss & ctrl.Key) != 0)
                                                                    {
                                                                        classe = "badge-primary";
                                                                        titulo = "[Ligado]";
                                                                    }

                                                                    <span class="badge @classe" data-toggle="tooltip" title="@nome @titulo">&nbsp;&nbsp;@nome&nbsp;&nbsp;</span><br /><br />
                                                                }
                                                            }
                                                        }    

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="overlay_aguarde" style="display: none">
                    <div class="spinner-aguarde">
                        <div class="sk-spinner sk-spinner-wandering-cubes">
                            <div class="sk-spinner sk-spinner-wave">
                                <div class="sk-rect1 text-ligh"></div>
                                <div class="sk-rect2"></div>
                                <div class="sk-rect3"></div>
                                <div class="sk-rect4"></div>
                                <div class="sk-rect5"></div>
                            </div>
                            <div class="sk-rect5"></div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/plugins/iCheck")
    @Scripts.Render("~/declaracoes/Constants")
    @Scripts.Render("~/declaracoes/Validates")



    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

    $(document).ready(function () {

        // classe iChecks
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });

        //
        // VALIDACAO DOS CAMPOS
        //
        Validator_AddMethods();

        $.validator.addMethod('positivo', function (value) {
            return Number(value) >= 0;
        }, 'Favor especificar um endereço.');

        $("#form").validate({
            rules: {
                TempoMinDesligado: {
                    multiplo_time: true,
                },
                TempoReligar: {
                    multiplo_time: true,
                },
                TempoMinLigado: {
                    multiplo_time: true,
                },
                TempoMaxDesligadoP: {
                    multiplo_time: true,
                },
                TempoMinLigadoP: {
                    multiplo_time: true,
                },
                TempoMaxDesligadoFP: {
                    multiplo_time: true,
                },
                TempoMinLigadoFP: {
                    multiplo_time: true,
                },
                TempoMaxDesligadoFP_Hora: {
                    time: true,
                },
                ComandoSuperv: {
                    numeric: true,
                    multiplo: true,
                },
                Endereco: {
                    required: true,
                    numeric: true,
                    positivo: true,
                },
                Descricao: {
                    alphanumeric: true,
                    required: true,
                    descricao: true,
                },
                ComandoAlarmeTipo: {
                    required: true
                },
                ComandoAlarmeAux: {
                    required: true
                },
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            },
        });
            
        //// VERIFICA CAMPOS QUE DEVEM OU NAO ESTAR HABILITADOS \\\\
        SelecionouRemota();
        SelecionouRedeIO();
        SelecionouCmdAlarme();
        SelecionouTipoAlarme();
        SelecionouEntradaEstado();

        // desabilita campos por permissao de usuario
        disableAll();
            
        $('form input').on('keypress', function (e) {
            return e.which !== 13;
        });   
        
    });

    function disableAll() {

        var permissao = Math.ceil(@ViewBag.Permissao);

        // verifica permissao
        switch (permissao) {
            case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
            case PERMISSOES_GERENTE:            // 1 - permissao de Gerente: ve tudo e escreve parte
            case PERMISSOES_OPERADOR:           // 2 - permissao de Operador: ve tudo e nao pode escrever
            case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
            case PERMISSOES_CONSULTOR:          // 5 - permissao de Consultor
            case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte 
                               
                $("#BotaoProgramar").attr('disabled', false);
                $("#Descricao").attr('disabled', false);
                $("#RedeIO").attr('disabled', false);
                $("#entrada_desl").attr('disabled', false);
                $("#entrada_retr").attr('disabled', false);
                $("#med_desl").attr('disabled', false);                

                $("#ComandoAlarme").attr('disabled', false);
                $("#ComandoHrTarifador").attr('disabled', false);

                $("#entrada_retr").attr('disabled', false);

                $("#RegEvTemporizador").attr('disabled', false);
                $("#LogicaInvertida").attr('disabled', false);
                $("#HabAcessoRemoto").attr('disabled', false);

                // temporizadores
                $("#tr_min_d").attr('disabled', false);
                $("#tr_max_d").attr('disabled', false);
                $("#tr_min_l").attr('disabled', false);
                $("#DelayDesligar").attr('disabled', false);
                $("#DelayLigar").attr('disabled', false);
                $("#p_max_d").attr('disabled', false);
                $("#p_min_l").attr('disabled', false);
                $("#TempoMaxDesligadoFP_Hora").attr('disabled', false);
                $("#f_max_d").attr('disabled', false);
                $("#f_min_l").attr('disabled', false);
                
                
                break;

            default:
            case PERMISSOES_PRODUCAO:           // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                $("#BotaoProgramar").attr('disabled', true);
                $("#Descricao").attr('disabled', true);

                $("#RedeIO").attr('disabled', true);
                $("#remotas_k").attr('disabled', true);
                $("#remotas_r_q").attr('disabled', true);
                $("#Endereco").attr('disabled', true);
                $("#entrada_desl").attr('disabled', true);
                $("#entrada_retr").attr('disabled', true);
                $("#med_desl").attr('disabled', true);
                $("#ComandoSuperv").attr('disabled', true);

                $("#ComandoAlarme").attr('disabled', true);
                $("#ComandoAlarmeAux").attr('disabled', true);
                $("#ComandoHrTarifador").attr('disabled', true);

                $("#entrada_retr").attr('disabled', true);

                $("#RegEvTemporizador").attr('disabled', false);
                $("#LogicaInvertida").attr('disabled', false);
                $("#HabAcessoRemoto").attr('disabled', false);

                // temporizadores
                $("#tr_min_d").attr('disabled', true);
                $("#tr_max_d").attr('disabled', true);
                $("#tr_min_l").attr('disabled', true);
                $("#DelayDesligar").attr('disabled', true);
                $("#DelayLigar").attr('disabled', true);
                $("#p_max_d").attr('disabled', true);
                $("#p_min_l").attr('disabled', true);
                $("#TempoMaxDesligadoFP_Hora").attr('disabled', true);
                $("#f_max_d").attr('disabled', true);
                $("#f_min_l").attr('disabled', true);

                break;
        }
    }
       
    $(".badge").tooltip({
        placement: "bottom"
    });

    function SelecionouRedeIO() {

        // pega redeIO selecionada
        var rede_io = parseInt($("#RedeIO").val());

        // executo funcao para verificar se endereco do driver deve ser habilitado ou nao
        SelecionouRemota();

        // apresenta selecao de remotas conforme redeIO
        ShowRemota(rede_io);
    }

    function ShowRemota(RedeIO) {

        switch (RedeIO)
        {

            case 0: // rede R
            case 1: // rede Q

                // habilita rede R e Q
                $('.div_rede_r_q').css("display", "block");
                $("#remotas_r_q").attr('disabled', false);

                // Desabilita rede K
                $('.div_rede_k').css("display", "none");
                $("#remotas_k").attr('disabled', true);

                break;
                //
            case 2: // K

                // habilita rede K
                $('.div_rede_k').css("display", "block");
                $("#remotas_k").attr('disabled', false);

                // desabilita rede R e Q
                $('.div_rede_r_q').css("display", "none");
                $("#remotas_r_q").attr('disabled', true);

                break;

            case 4: // DO (saidas digitais)

                // apresenta campo porem desabilitado
                $('.div_rede_r_q').css("display", "block");
                $("#remotas_r_q").attr('disabled', true);
                $('.div_rede_k').css("display", "none");
                $("#Endereco").attr('disabled', true);

                document.getElementById('remotas_k').value = 0;
                document.getElementById('remotas_r_q').value = 0;
                
                break;
        }

    }

    function SelecionouRemota() {

        // pega redeIO selecioanada
        var rede_io = parseInt($("#redeIO").val());

        // pega numero remota
        var num_remota = 0;

        switch (rede_io)
        {
            case 0:  // Rede R
            case 1:  // Rede Q
                num_remota = parseInt(document.getElementById("remotas_r_q").value);
                break;

            case 2:  // Rede K
                num_remota = parseInt(document.getElementById("remotas_k").value);
                break;

            case 4:  // DO
                document.getElementById("Endereco").value = 0;
                break;
        }

        // habilita ou desabilita campo de endereco do driver
        ShowEnderecoDrv(num_remota, rede_io);
    }

    function ShowEnderecoDrv(Remota, RedeIO) {

        // caso numero de remota nao selecionado desabilita campo de endereco de driver
        if (Remota < 0 || Remota > 31 || Number.isNaN(Remota))
        {
            // caso nao escolheu remota deixo input em branco
            document.getElementById('Endereco').value = "";
            $("#Endereco").attr('disabled', true);
        }
        else {
            // verifica se nao sta configurado como remota 'DO'
            if (RedeIO != 4)
            {                
                $("#Endereco").attr('disabled', false);
            }            
        }
    }

    function SelecionouCmdAlarme() {

        // pega comando selecionado
        var cmd_alm = parseInt($("#ComandoAlarme").val());

        // apresenta campos conforme comando selecionado
        ShowTipoAlarme(cmd_alm);

    }

    function ShowTipoAlarme(CmdAlm) {        

        if (CmdAlm == 0) {            

            // se comando de alarme nao selecionado, desabilita tipo de alarme
            $("#ComandoAlarmeTipo").attr('disabled', true);

            // apresenta campo do auxiliar alarme  porem desabilitado
            $('.div_sistema').css("display", "block");
            $("#alm_sistema").attr('disabled', true);

            // nenhum cmd selecionado
            document.getElementById('ComandoAlarmeTipo').value = "";
            document.getElementById('alm_sistema').value = "";

            // desabilita demais listas do auxiliar alarme
            $('.div_faltapulsos').css("display", "none");
            $('.div_ultrap_demanda').css("display", "none");
            $('.div_ultrap_ind').css("display", "none");
            $('.div_ultrap_cap').css("display", "none");
            $('.div_comando_superv').css("display", "none");
            $('.div_usuario').css("display", "none");

        }

        else {
            // habilita campo tipo de alarme=
            $("#ComandoAlarmeTipo").attr('disabled', false);
        }
    }

    function SelecionouTipoAlarme() {

        // pega comando selecionado
        var cmd_alm_ty = parseInt($("#ComandoAlarmeTipo").val());

        // apresenta campos conforme comando selecionado
        ShowTipoAlarmeAux(cmd_alm_ty);
    }


    function ShowTipoAlarmeAux(CmdAlmTy) {

        switch (CmdAlmTy) {

            default:

                // habilita lista de alarmes do sistema
                $('.div_sistema').css("display", "block");
                $("#alm_sistema").attr('disabled', true);
                $("#alm_sistema").val("");

                // desabilita demais listas de alarme
                $('.div_faltapulsos').css("display", "none");
                $('.div_ultrap_demanda').css("display", "none");
                $('.div_ultrap_ind').css("display", "none");
                $('.div_ultrap_cap').css("display", "none");
                $('.div_comando_superv').css("display", "none");
                $('.div_usuario').css("display", "none");

                break;

            case 0: // sistema

                // habilita lista de alarmes do sistema
                $('.div_sistema').css("display", "block");
                $("#alm_sistema").attr('disabled', false);

                // desabilita demais listas de alarme
                $('.div_faltapulsos').css("display", "none");
                $('.div_ultrap_demanda').css("display", "none");
                $('.div_ultrap_ind').css("display", "none");
                $('.div_ultrap_cap').css("display", "none");
                $('.div_comando_superv').css("display", "none");
                $('.div_usuario').css("display", "none");

                break;

            case 1: // falta pulsos

                // habilita lista falta de pulso
                $('.div_faltapulsos').css("display", "block");
                $("#alm_faltapulsos").attr('disabled', false);

                // desabilita demais listas de alarme
                $('.div_sistema').css("display", "none");
                $('.div_ultrap_demanda').css("display", "none");
                $('.div_ultrap_ind').css("display", "none");
                $('.div_ultrap_cap').css("display", "none");
                $('.div_comando_superv').css("display", "none");
                $('.div_usuario').css("display", "none");

                break;

            case 2: // demanda hi

                // habilita lista ultrapassagem de demanda
                $('.div_ultrap_demanda').css("display", "block");
                $("#ultrap_demanda").attr('disabled', false);

                // desabilita demais listas de alarme
                $('.div_sistema').css("display", "none");
                $('.div_faltapulsos').css("display", "none");
                $('.div_ultrap_ind').css("display", "none");
                $('.div_ultrap_cap').css("display", "none");
                $('.div_comando_superv').css("display", "none");
                $('.div_usuario').css("display", "none");

                break;

            case 3: // indutivo lo

                // habilita lista ultrapassagem FP indutivo
                $('.div_ultrap_ind').css("display", "block");
                $("#ultrap_ind").attr('disabled', false);

                // desabilita demais listas de alarme
                $('.div_sistema').css("display", "none");
                $('.div_faltapulsos').css("display", "none");
                $('.div_ultrap_demanda').css("display", "none");
                $('.div_ultrap_cap').css("display", "none");
                $('.div_comando_superv').css("display", "none");
                $('.div_usuario').css("display", "none");

                break;

            case 4: // capacitivo lo

                // habilita lista ultrapassagem FP indutivo
                $('.div_ultrap_cap').css("display", "block");
                $("#ultrap_cap").attr('disabled', false);

                // desabilita demais listas de alarme
                $('.div_sistema').css("display", "none");
                $('.div_faltapulsos').css("display", "none");
                $('.div_ultrap_demanda').css("display", "none");
                $('.div_ultrap_ind').css("display", "none");
                $('.div_comando_superv').css("display", "none");
                $('.div_usuario').css("display", "none");

                break;
          
            case 5: // comando superv

                // habilita lista comando supervisionado
                $('.div_comando_superv').css("display", "block");
                $("#comando_superv").attr('disabled', false);

                // desabilita demais listas de alarme
                $('.div_sistema').css("display", "none");
                $('.div_faltapulsos').css("display", "none");
                $('.div_ultrap_demanda').css("display", "none");
                $('.div_ultrap_ind').css("display", "none");
                $('.div_ultrap_cap').css("display", "none");
                $('.div_usuario').css("display", "none");

                break;

            case 255: // nenhum

                // habilita lista de alarmes do usuário
                $('.div_usuario').css("display", "block");
                $("#alm_usuario").attr('disabled', false);

                // desabilita demais listas de alarme
                $('.div_sistema').css("display", "none");
                $('.div_faltapulsos').css("display", "none");
                $('.div_ultrap_demanda').css("display", "none");
                $('.div_ultrap_ind').css("display", "none");
                $('.div_ultrap_cap').css("display", "none");
                $('.div_comando_superv').css("display", "none");

                break;
        }
    }

    function SelecionouEntradaEstado() {

        // pega entrada selecionada
        var entrada_estado = parseInt($("#entrada_retr").val());

        // apresenta tempo para alarme conforme selecionou entrada
        ShowTempoAlarme(entrada_estado);

    }

    function ShowTempoAlarme(EntradaEstado) {
        
        // caso entrada de estado nao selecionada desabilita campo de temporizador
        if (EntradaEstado < 0 || EntradaEstado > 63 || Number.isNaN(EntradaEstado)) {            
            document.getElementById('ComandoSuperv').value = 0;
            $('#ComandoSuperv').attr('disabled', true);
        }
        else {
            $('#ComandoSuperv').attr('disabled', false);
        }
    }

    function TrataValores() {

        // verifica campos nao selecionados e insere valor 255 (nPrg)
        var entrada_desl = parseInt(document.getElementById("entrada_desl").value);
        var entrada_retr = parseInt(document.getElementById("entrada_retr").value);
        var med_desl = parseInt(document.getElementById("med_desl").value);

        if (Number.isNaN(entrada_desl) || entrada_desl < 0 || entrada_desl > 63) {
            entrada_desl = 255;
        }

        document.getElementById('EEAtivaSaida').value = entrada_desl;

        if (Number.isNaN(entrada_retr) || entrada_retr < 0 || entrada_retr > 63) {
            entrada_retr = 255;
        }

        document.getElementById('Retrosinalizador').value = entrada_retr;

        if (Number.isNaN(med_desl) || med_desl < 0 || med_desl > 35) {
            med_desl = 255;
        }

        document.getElementById('MedicaoFalhaDeslig').value = med_desl;

        // trata variavel numero da remota
        var rede_io = parseInt($("#RedeIO").val());
        var num_remota = 0;

        // recebe numero da remota de acordo com redeIO selecionado
        switch (rede_io)
        {
            case 0:  // Rede R
            case 1:  // Rede Q
                num_remota = parseInt(document.getElementById("remotas_r_q").value);
                break;

            case 2:  // Rede K
                num_remota = parseInt(document.getElementById("remotas_k").value);
                break;

            case 3:  // DO
                num_remota = 0;
                break;
        }

        if (Number.isNaN(num_remota) || num_remota < 0 || num_remota > 31)
        {
            num_remota = 255;
        }

        document.getElementById('Remota').value = num_remota;

        // trata variavel alarme auxiliar
        var cmd_type = parseInt($("#ComandoAlarmeTipo").val());
        var cmd_aux = 0;

        switch (cmd_type)
        {

            case 255:  // alarme de usuario
                cmd_aux = cmd_aux = parseInt(document.getElementById("alm_usuario").value);;
                break;

            case 0:  // sistema
                cmd_aux = parseInt(document.getElementById("alm_sistema").value);
                break;

            case 1:  // falta pulsos
                cmd_aux = parseInt(document.getElementById("alm_faltapulsos").value);
                break;
                
            case 2:  // ultrapassagem de demanda
                cmd_aux = parseInt(document.getElementById("ultrap_demanda").value);
                break;

            case 3:  // ultrapassagem fator de potencia indutivo
                cmd_aux = parseInt(document.getElementById("ultrap_ind").value);
                break;

            case 4:  // ultrapassagem fator de potencia capacitivo
                cmd_aux = parseInt(document.getElementById("ultrap_cap").value);

                break;

            case 5:  // comando supervisionado
                cmd_aux = parseInt(document.getElementById("comando_superv").value);
                break;
        }

        document.getElementById('ComandoAlarmeAux').value = cmd_aux;


        //// TEMPORIZADORES \\\\
        var tr_min_d = String($("#tr_min_d").val());
        var tr_max_d = String($("#tr_max_d").val());
        var tr_min_l = String($("#tr_min_l").val());
        var p_max_d = String($("#p_max_d").val());
        var p_min_l = String($("#p_min_l").val());
        var f_max_d = String($("#f_max_d").val());
        var f_min_l = String($("#f_min_l").val());

        // separa minuto de segundo
        var tr_min_d_Aux = tr_min_d.split(":");
        var tr_max_d_Aux = tr_max_d.split(":");
        var tr_min_l_Aux = tr_min_l.split(":");
        var p_max_d_Aux = p_max_d.split(":");
        var p_min_l_Aux = p_min_l.split(":");
        var f_max_d_Aux = f_max_d.split(":");
        var f_min_l_Aux = f_min_l.split(":");

        // converte os minutos em segundos e insere o parametro na estrutura
        document.getElementById('TempoMinDesligado').value = parseInt(tr_min_d_Aux[0]) * 60 + parseInt(tr_min_d_Aux[1]);
        document.getElementById('TempoReligar').value = parseInt(tr_max_d_Aux[0]) * 60 + parseInt(tr_max_d_Aux[1]);
        document.getElementById('TempoMinLigado').value = parseInt(tr_min_l_Aux[0]) * 60 + parseInt(tr_min_l_Aux[1]);
        document.getElementById('TempoMaxDesligadoP').value = parseInt(p_max_d_Aux[0]) * 60 + parseInt(p_max_d_Aux[1]);
        document.getElementById('TempoMinLigadoP').value = parseInt(p_min_l_Aux[0]) * 60 + parseInt(p_min_l_Aux[1]);
        document.getElementById('TempoMaxDesligadoFP').value = parseInt(f_max_d_Aux[0]) * 60 + parseInt(f_max_d_Aux[1]);
        document.getElementById('TempoMinLigadoFP').value = parseInt(f_min_l_Aux[0]) * 60 + parseInt(f_min_l_Aux[1]);
    }

    function Programar() {

        // trata valores das variaveis a serem salvas
        TrataValores();

        var $form = $('#form');

        // apresenta primeiro tab que tiver erro
        var IsTabValid = true;
        var TabInvalid = 1;

        $(".tab-content").find("div.tab-pane").each(function (index, tab) {
            var id = $(tab).attr("id");
            $('a[href="#' + id + '"]').tab('show');

            IsTabValid = $("#form").valid();

            if (!IsTabValid) {

                TabInvalid = id;
                return false;
            }
        });
        
        // apresenta primeira tab ou com erro
        $('a[href="#tab-' + TabInvalid + '"]').tab('show');

        // verifica se entradas sao validas
        if (!IsTabValid) return false;

        // verifica se saidas sao validas
        if (!$form.valid()) return false;

        // retiro campos desabilitados, pois nao envia via POST
        $(":disabled", $('#form')).removeAttr("disabled");

        $.ajax({
            url: '/Configuracao/GateX_SaidaDigital_Programar',
            data: $form.serialize(),
            type: 'POST',
            success: function (data) {

                setTimeout(function () {

                    // verifica se erro
                    if (data.status == "ERRO") {
                        swal({
                            title: "Erro",
                            text: data.erro,
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // redireciona
                            var IDGateway = document.getElementById('IDGateway').value;
                            var url = '/Configuracao/GateX_SaidaDigital?IDGateway=' + IDGateway + '&Origem=2';

                            window.location.href = url;

                        });
                    }
                    else {

                        // redireciona
                        var IDGateway = document.getElementById('IDGateway').value;
                        var url = '/Configuracao/GateX_SaidaDigital?IDGateway=' + IDGateway + '&Origem=2';

                        window.location.href = url;
                    }

                }, 100);

            },
            error: function (xhr, status, error) {

                setTimeout(function () {

                    swal({
                        title: "Erro",
                        text: "Erro ao Programar!",
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    }, function () {

                        // redireciona
                        var IDGateway = document.getElementById('IDGateway').value;
                        var url = '/Configuracao/GateX_SaidaDigital?IDGateway=' + IDGateway + '&Origem=2';

                        window.location.href = url;
                    });
                }, 100);
            },
        });
    };

    $(".link_primeira").off().on("click", function (event) {
        event.stopPropagation();

        // primeira saidas
        SalvaNavega(0);
    });

    $(".link_ultima").off().on("click", function (event) {
        event.stopPropagation();

        // ultima saida
        SalvaNavega(63);
    });

    $(".link_prog_menos").off().on("click", function (event) {
        event.stopPropagation();

        var NumSaidaGateway = parseInt(document.getElementById("NumSaidaGateway").value);

        // saida anterior
        SalvaNavega(NumSaidaGateway - 1);
    });

    $(".link_prog_mais").off().on("click", function (event) {
        event.stopPropagation();

        var NumSaidaGateway = parseInt(document.getElementById("NumSaidaGateway").value);

        // proxima saida
        SalvaNavega(NumSaidaGateway + 1);
    });


    function SalvaNavega(navega) {

        var descricao = String($("#Descricao").val());

        // caso descricao da saida nao foi alterada nao salvo
        if (descricao == "-") {

            // redireciona para saida desejada
            var IDGateway = document.getElementById('IDGateway').value;
            var url = '/Configuracao/GateX_SaidaDigital_Editar?IDGateway=' + IDGateway + '&NumSaidaGateway=' + navega;

            window.location.href = url;
        }

            // caso usuario alterou a descricao da saida salva ao navegar para a proxima desejada
        else {

            // trata valores das variaveis a serem salvas
            TrataValores();

            var $form = $('#form');

            // apresenta primeiro tab que tiver erro
            var IsTabValid = true;
            var TabInvalid = 1;

            $(".tab-content").find("div.tab-pane").each(function (index, tab) {
                var id = $(tab).attr("id");
                $('a[href="#' + id + '"]').tab('show');

                IsTabValid = $("#form").valid();

                if (!IsTabValid) {

                    TabInvalid = id;
                    return false;
                }
            });

            // apresenta primeira tab ou com erro
            $('a[href="#tab-' + TabInvalid + '"]').tab('show');

            // verifica se entradas sao validas
            if (!IsTabValid) return false;

            // verifica se saidas sao validas
            if (!$form.valid()) return false;

            event.stopPropagation();

            // aguarde
            $('.overlay_aguarde').toggle();

            // retiro campos desabilitados, pois nao envia via POST
            $(":disabled", $('#form')).removeAttr("disabled");

            $.ajax({
                url: '/Configuracao/GateX_SaidaDigital_Programar',
                data: $form.serialize(),
                type: 'POST',
                success: function (data) {

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {
                            swal({
                                title: "Erro",
                                type: "warning",
                                text: data.erro,
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // recarrega pagina
                                self.location.reload(true);
                            });
                        }
                        else {
                            // redireciona para saida desejada
                            var IDGateway = document.getElementById('IDGateway').value;
                            var url = '/Configuracao/GateX_SaidaDigital_Editar?IDGateway=' + IDGateway + '&NumSaidaGateway=' + navega;

                            window.location.href = url;
                        }

                    }, 100);
                },
                error: function (xhr, status, error) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // redireciona para pagina lista de saidas
                            var IDGateway = document.getElementById('IDGateway').value;
                            var url = '/Configuracao/GateX_SaidaDigital?IDGateway=' + IDGateway + '&Origem=2';

                            window.location.href = url;
                        });
                    }, 100);
                },
            });
        }
    };

    </script>
}
