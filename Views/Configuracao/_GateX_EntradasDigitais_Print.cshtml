﻿@using SmartEnergyLib.SQL

<head>

    <title>@SmartEnergy.Resources.ConfiguracaoTexts.EntradasDigitais</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

    <link href="~/fonts/font-awesome/css/font-awesome.min.css" rel="stylesheet" />
    <link href="~/fonts/elusive-icons-2.0.0/css/elusive-icons.min.css" rel="stylesheet" />
    <link href="~/Content/bootstrap.min.css" rel="stylesheet" />
    <link href="~/Content/style.css" rel="stylesheet" />
    <link href="~/Content/Relatorios.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/RelatoriosPDF.css" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="~/Scripts/jquery-2.1.1.min.js" charset="utf-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/dataTables/datatables.min.js" charset="utf-8"></script>

    <style>
        td {
            font-size: 10px;
        }

        th {
            font-size: 10px;
        }

        tr {
            page-break-inside: avoid;
        }


    </style>

</head>

<body>

    @{
        List<GateX_ED_Dominio> entradas = ViewBag.Entradas;
    }

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="~/Logos/@ViewBag._LogoConsultor" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        <p><b>@ViewBag.NomeGateway</b></p>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.ConfiguracaoTexts.EntradasDigitais</h4>
                        <p>@ViewBag.DataTextoAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <hr />

        <div class="row">
            <div class="col-lg-12">
                <table class="table table-striped table-bordered table-hover dataTables-entradas">
                    <thead>
                        <tr>
                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.NumEntradaGateway</th>
                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.EntradasDigitais</th>
                            <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.Rede</th>
                            <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.Remota</th>
                            <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.Endereco</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (entradas != null)
                        {
                            foreach (GateX_ED_Dominio entrada in entradas)
                            {
                                string desprogramado = "---";
                                string nomeEntrada = desprogramado;
                                string rede = desprogramado;
                                string remota = desprogramado;
                                string endereco = desprogramado;

                                string nomeEntrada_sort = "ZZZ";
                                string rede_sort = "ZZZ";
                                string remota_sort = "ZZZ";
                                string endereco_sort = "ZZZ";

                                if (entrada.Programado)
                                {
                                    nomeEntrada = entrada.Descricao;
                                    nomeEntrada_sort = entrada.Descricao;

                                    switch (entrada.RedeIO)
                                    {
                                        case 0:

                                            rede = "Rede [R]";
                                            rede_sort = "Rede [R]";

                                            break;

                                        case 1:

                                            rede = "Rede [Q]";
                                            rede_sort = "Rede [Q]";

                                            break;

                                        case 2:

                                            rede = "Rede [K]";
                                            rede_sort = "Rede [K]";

                                            break;

                                        case 4:

                                            rede = "DO";
                                            rede_sort = "DO";

                                            break;
                                    }

                                    if (entrada.Remota != 255)
                                    {
                                        remota = entrada.Remota.ToString();
                                        remota_sort = entrada.Remota.ToString();
                                    }

                                    if (entrada.Endereco >= 0 && entrada.Endereco < 65000)
                                    {
                                        endereco = entrada.Endereco.ToString();
                                        endereco_sort = entrada.Endereco.ToString();
                                    }
                                }

                                <tr>
                                    <td>@entrada.NumEntradaGateway.ToString()</td>
                                    <td><span style="display:none;">@nomeEntrada_sort</span>@nomeEntrada</td>
                                    <td class="some_minidesktop"><span style="display:none;">@rede_sort</span>@rede</td>
                                    <td class="some_minidesktop">@remota</td>
                                    <td class="some_minidesktop">@endereco</td>
                                </tr>
                            }
                        }
                        
                    </tbody>
                </table>

            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-offset-12" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>

<script type="text/javascript">

    $(document).ready(function () {


        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {
                var x = (a == "---") ? -1 : a.replace(/,/, ".");
                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-entradas').DataTable({
            "iDisplayLength": 10000,
            dom: 't',

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "numero" },
                        { "aTargets": [1], "sType": "portugues" },
                        { "aTargets": [2], "sType": "portugues" },
                        { "aTargets": [3], "sType": "numero" },
                        { "aTargets": [4], "sType": "numero" },
            ],

            "aoColumns": [
                { sWidth: "15%" },
                { sWidth: "25%" },
                { sWidth: "20%" },
                { sWidth: "20%" },
                { sWidth: "20%" },
            ],
            'order': [0, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

    });

</script>
