﻿@model SmartEnergyLib.SQL.GateX_ReceberConfiguracoes_Dominio

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.ConfiguracaoTexts.ReceberConfiguracoes;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("GateX_ReceberConfiguracoes", "Configuracao", new { ReturnUrl = ViewBag.ReturnUrl }, FormMethod.Post, new { id = "form", role = "form" }))
            {
                <div class="panel panel-title">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="panel panel-success">
                                    <div class="panel-heading">

                                        @{
                                            GatewaysDominio gateway = ViewBag.Gateway;
                                            @Html.Hidden("IDGateway", gateway.IDGateway);
                                        }

                                        <h4>Gateway</h4><br />
                                        @gateway.Nome

                                        @{
                                            int IDTipoAcesso = ViewBag._IDTipoAcesso;

                                            if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                            {
                                                <span> [@gateway.IDGateway]</span>
                                            }
                                        }

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="panel panel-title">
                            <div class="panel-heading">
                                <div style="text-align:center;">
                                    <h4>@SmartEnergy.Resources.ConfiguracaoTexts.ComandoReceberConfiguracoes</h4>
                                </div>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-lg-offset-6 col-lg-6">
                                        <div class="ibox-tools">
                                            <button type="button" class="btn btn-primary" onclick="Enviar(@gateway.IDGateway);" disabled="" id="BotaoEnviar">@SmartEnergy.Resources.ConfiguracaoTexts.ReceberConfiguracoes</button>
                                            <a href='@("/Configuracao/Gateway_Editar?IDGateway=" + @gateway.IDGateway.ToString() + "#tab-6")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                        </div>
                                    </div>
                                </div>

                                <br />
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="panel-body">

                                            <div class="row">
                                                <div class="col-lg-1" style="text-align:center;">
                                                    <i class="fa fa-download fa-5x"></i>
                                                </div>
                                                <div class="col-lg-11">
                                                    <h2>Este comando atualiza a plataforma SmartEnergy com as configurações abaixo:</h2>
                                                </div>
                                            </div>

                                            <br />
                                            <br />
                                            <div class="row">
                                                <div class="form-group col-lg-3" style="margin-left:20px;">
                                                    <div class="i-checks" style="margin-top:6px;">
                                                        @Html.CheckBoxFor(model => model.MedEner)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracaoTexts.MedicoesEnergia</span><br /><br />
                                                        @Html.CheckBoxFor(model => model.MedUtil)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracaoTexts.MedicoesUtilidades</span><br /><br />
                                                        @Html.CheckBoxFor(model => model.MedAna)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracaoTexts.MedicoesAnalogicas</span><br /><br />
                                                        @Html.CheckBoxFor(model => model.MedCiclo)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracaoTexts.MedicoesCiclometro</span><br /><br /><br />

                                                        @Html.CheckBoxFor(model => model.ED)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracaoTexts.EntradasDigitais</span><br /><br />
                                                        @Html.CheckBoxFor(model => model.SD)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracaoTexts.SaidasDigitais</span><br /><br /><br />

                                                        @Html.CheckBoxFor(model => model.DatasEspeciais)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracaoTexts.DatasEspeciais</span><br /><br />
                                                        @Html.CheckBoxFor(model => model.Empresa)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracaoTexts.Empresa</span><br /><br />
                                                        @Html.CheckBoxFor(model => model.Usuarios)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracaoTexts.Usuarios</span><br /><br />
                                                        @Html.CheckBoxFor(model => model.Drivers)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracaoTexts.Drivers</span>
                                                    </div>
                                                </div>
                                                <div class="form-group col-lg-3">
                                                    <div class="i-checks" style="margin-top:6px;">
                                                        @Html.CheckBoxFor(model => model.CtrlDemProj)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracaoTexts.ControleDemProj</span><br /><br />
                                                        @Html.CheckBoxFor(model => model.CtrlDemMed)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracaoTexts.ControleDemMed</span><br /><br />
                                                        @Html.CheckBoxFor(model => model.CtrlDemAcum)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracaoTexts.ControleDemAcum</span><br /><br />
                                                        @Html.CheckBoxFor(model => model.CtrlFatPot)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracaoTexts.ControleFatPot</span><br /><br /><br />

                                                        @Html.CheckBoxFor(model => model.CtrlAna)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracaoTexts.ControleAnalogico</span><br /><br /><br />
                                                        @Html.CheckBoxFor(model => model.CtrlHorario)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracaoTexts.ControleHorario</span><br /><br />
                                                        @Html.CheckBoxFor(model => model.CtrlAlarme)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracaoTexts.ControleAlarme</span><br /><br />
                                                        @Html.CheckBoxFor(model => model.Logicas)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracaoTexts.Logicas</span><br /><br />
                                                    </div>
                                                </div>
                                                <div class="form-group col-lg-5">
                                                    <div class="row" id="progressbarDIV" style="display:none;">
                                                        <div class="col-lg-12">
                                                            <label id="status_atual" class="control-label">---</label>
                                                            <div class="progress progress-bar-default">
                                                                <div id="progressbar" style="width: 0%" aria-valuemax="100" aria-valuemin="0" aria-valuenow="0" role="progressbar" class="progress-bar">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/iCheck")
    @Scripts.Render("~/plugins/AlertaBox")
    @Scripts.Render("~/declaracoes/Constants")
    @Scripts.Render("~/declaracoes/Validates")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        $(document).ready(function () {

            // classe iChecks
            $('.i-checks').iCheck({
                checkboxClass: 'icheckbox_square-green',
                radioClass: 'iradio_square-green',
            });

            // desabilita campos
            disableAll();

            $('form input').on('keypress', function (e) {
                return e.which !== 13;
            });

        });

        function disableAll() {

            var permissao = Math.ceil(@ViewBag.Permissao);

            // verifica permissao
            switch (permissao) {
                case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
                case PERMISSOES_GERENTE:            // 1 - permissao de Gerente: ve tudo e escreve parte
                case PERMISSOES_OPERADOR:           // 2 - permissao de Operador: ve tudo e nao pode escrever
                case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
                case PERMISSOES_CONSULTOR:          // 5 - permissao de Consultor
                case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte 

                    $("#BotaoEnviar").attr('disabled', false);
                    break;

                default:
                case PERMISSOES_PRODUCAO:           // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                    $("#BotaoEnviar").attr('disabled', true);
                    break;
            }
        }

        function updateProgress(progresso) {

            // barra de progresso
            var elem = document.getElementById("progressbar");
            elem.style.width = progresso + '%';
            elem.innerHTML = progresso + '%';
        }

        function Enviar(IDGateway) {
            event.stopPropagation();

            // esconde janela de progresso
            $('#progressbarDIV').css("display", "none");

            var $form = $('#form');

            var IsTabValid = $("#form").valid();

            // verifica se entradas sao validas
            if (!IsTabValid) return false;

            swal({
                title: "Deseja Receber?",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#1ab394",
                confirmButtonText: "Receber",
                cancelButtonText: "Cancelar",
                closeOnConfirm: true
            }, function () {
                event.stopPropagation();

                // retiro campos desabilitados, pois nao envia via POST
                $(":disabled", $('#form')).removeAttr("disabled");


                // receber configurações
                $.ajax(
                {
                    url: '/Configuracao/GateX_ReceberConfiguracoes_Enviar',
                    data: $form.serialize(),
                    type: 'POST',
                    success: function (data) {

                        setTimeout(function () {

                            // verifica se erro
                            if (data.status == "ERRO") {

                                AlertaBox("Erro", data.erro, "warning");

                            }
                            else {

                                // task id
                                var taskId = data.taskId.replace(/['"]+/g, '');

                                // inicia barra progresso
                                $('#progressbarDIV').css("display", "block");
                                updateProgress("0");

                                // atualiza barra de progresso
                                var intervalId = setInterval(function () {

                                    $.ajax(
                                    {
                                        type: 'GET',
                                        url: '/Configuracao/GateX_ReceberConfiguracoes_Progress',
                                        contentType: 'application/json; charset=utf-8',
                                        dataType: 'json',
                                        data: { 'id': taskId },
                                        cache: false,
                                        async: true,
                                        success: function (progress) {

                                            // status
                                            document.getElementById("status_atual").innerHTML = progress.status_atual;

                                            // atualiza barra de progresso
                                            updateProgress(progress.progresso);

                                            // verifica se terminou
                                            if (progress.progresso >= 100) {

                                                // fim barra de progresso
                                                updateProgress("100");
                                                clearInterval(intervalId);
                                                $('#progressbarDIV').css("display", "none");

                                                // apresenta resultado
                                                $.ajax(
                                                {
                                                    type: 'GET',
                                                    url: '/Configuracao/GateX_ReceberConfiguracoes_Resultado',
                                                    contentType: 'application/json; charset=utf-8',
                                                    dataType: 'json',
                                                    data: { 'id': taskId },
                                                    cache: false,
                                                    async: true,
                                                    success: function (data) {

                                                        // verifica se atualizou 
                                                        if (data.status == 10)
                                                        {
                                                            AlertaBox("Configurações atualizadas", "As configurações foram atualizadas com sucesso", "success");
                                                        }
                                                        else
                                                        {
                                                            AlertaBox("Erro", "Ocorreu um erro na atualização das configurações.<br><br>Tente novamente mais tarde.", "warning");
                                                        }

                                                    },
                                                    error: function (xhr, status, error) {

                                                        AlertaBox("Erro2", xhr.responseText, "warning");

                                                    }
                                                });
                                            } 
                                        },
                                        error: function (xhr, status, error) {

                                            $('#progressbarDIV').css("display", "none");

                                            AlertaBox("Erro3", xhr.responseText, "warning");
                                        }
                                    });

                                }, 1000);
                            }

                        }, 100);
                    },
                    error: function (xhr, status, error) {

                        AlertaBox("Erro4", xhr.responseText, "warning");

                    }
                });
            });
        };

    </script>
}
