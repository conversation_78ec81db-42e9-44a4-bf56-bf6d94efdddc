﻿@using SmartEnergyLib.SQL

<head>

    <title>@SmartEnergy.Resources.ConfiguracaoTexts.Logicas</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

    <link href="@Server.MapPath("~/Content/bootstrap.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/style.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/Relatorios.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/RelatoriosPDF.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/fonts/font-awesome/css/font-awesome.min.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/fonts/elusive-icons-2.0.0/css/elusive-icons.min.css")" rel="stylesheet" />

    <script type="text/javascript" src="@Server.MapPath("~/Scripts/jquery-2.1.1.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/dataTables/datatables.min.js")" charset="utf-8"></script>

    <style>
        td {
            font-size: 10px;
        }

        th {
            font-size: 10px;
        }

        tr {
            page-break-inside: avoid;
        }


    </style>

</head>

@{
    string arquivo = "~/Logos/" + ViewBag._LogoConsultor;
    string logo = Server.MapPath(arquivo);
}

<body>

    @{
        List<GateX_Logicas_Dominio> logicas = ViewBag.Logicas;
    }

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="@logo" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        <p><b>@ViewBag.NomeGateway</b></p>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.ConfiguracaoTexts.Logicas</h4>
                        <p>@ViewBag.DataTextoAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <hr />

        <div class="row">
            <div class="col-lg-12">
                <table class="table table-striped table-bordered table-hover dataTables-logicas">
                    <thead>
                        <tr>
                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.NumLogicaGateway</th>
                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.SaidasDigitais</th>
                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Logicas</th>
                        </tr>
                    </thead>
                    <tbody>

                        @if (logicas != null)
                        {
                            foreach (GateX_Logicas_Dominio logica in logicas)
                            {
                                string desprogramado = "---";
                                string nomeSaida = desprogramado;
                                string nomeLogica = desprogramado;

                                string nomeSaida_sort = "ZZZ";

                                if (logica.Programado)
                                {
                                    List<GateX_SD_Lista_Dominio> saidasDigitais = ViewBag.SaidasDigitais;
                                    if (saidasDigitais != null)
                                    {
                                        foreach (GateX_SD_Lista_Dominio saida in saidasDigitais)
                                        {
                                            if (logica.Saida == saida.NumSaidaGateway)
                                            {
                                                nomeSaida = String.Format("[{0}] {1}", logica.Saida, saida.Descricao);
                                                nomeSaida_sort = String.Format("[{0}] {1}", logica.Saida, saida.Descricao);
                                            }
                                        }
                                    }

                                    nomeLogica = Funcoes_GateX.Logica2String(logica);
                                }
                                
                                <tr>
                                    <td>@logica.NumLogicaGateway.ToString()</td>
                                    <td><span style="display:none;">@nomeSaida_sort</span>@nomeSaida</td>
                                    <td>@nomeLogica</td>
                                </tr>
                            }
                        }
                        
                    </tbody>
                </table>

            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-offset-12" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>

<script type="text/javascript">

    $(document).ready(function () {


        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {
                var x = (a == "---") ? -1 : a.replace(/,/, ".");
                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-logicas').DataTable({
            "iDisplayLength": 10000,
            dom: 't',

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "numero" },
                        { "aTargets": [1], "sType": "portugues" },
                        { "aTargets": [2], "sType": "portugues" },
            ],

            "aoColumns": [
                { sWidth: "10%" },
                { sWidth: "30%" },
                { sWidth: "60%" },
            ],
            'order': [0, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

    });

</script>
