﻿@model SmartEnergyLib.SQL.GateE_RedeIoT_Dominio

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.ConfiguracaoTexts.RedeIoT;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("GateE_RedeIoT", "Configuracao", new { ReturnUrl = ViewBag.ReturnUrl }, FormMethod.Post, new { id = "form", role = "form" }))
            {
                <div class="panel panel-title">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="panel panel-success">
                                    <div class="panel-heading">

                                        @{
                                            GatewaysDominio gateway = ViewBag.Gateway;
                                            @Html.Hidden("IDGateway", gateway.IDGateway);
                                        }

                                        <h4>Gateway</h4><br />
                                        @gateway.Nome

                                        @{
                                            int IDTipoAcesso = ViewBag._IDTipoAcesso;

                                            if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                            {
                                                <span> [@gateway.IDGateway]</span>
                                            }
                                        }

                                        @Html.Hidden("wfMxTxSz", Model.wfMxTxSz)

                                        @Html.Hidden("Servidor_IoT", Model.Servidor_IoT)
                                        @Html.Hidden("Port_IoT", Model.Port_IoT)

                                        @Html.Hidden("mq_MxQue", Model.mq_MxQue)
                                        @Html.Hidden("mq_nAtvT", Model.mq_nAtvT)
                                        @Html.Hidden("mq_keepT", Model.mq_keepT)
                                        @Html.Hidden("mq_nCnxT", Model.mq_nCnxT)

                                        @Html.Hidden("updVer_T", Model.updVer_T)
                                        @Html.Hidden("updVer_TO", Model.updVer_TO)
                                        @Html.Hidden("updDataTO", Model.updDataTO)
                                        @Html.Hidden("updMxRxSz", Model.updMxRxSz)
                                        @Html.Hidden("updPbTop", Model.updPbTop)
                                        @Html.Hidden("updSbTop", Model.updSbTop)

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="panel panel-title">
                            <div class="panel-heading">
                                <div style="text-align:center;">
                                    <h4>@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoRedeIot</h4>
                                </div>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-lg-offset-6 col-lg-6">
                                        <div class="ibox-tools">
                                            <button type="button" class="btn btn-primary" onclick="Enviar(@gateway.IDGateway);" disabled="" id="BotaoEnviar">@SmartEnergy.Resources.ComumTexts.BotaoEnviar</button>
                                            <a href='@("/Configuracao/Gateway_Editar?IDGateway=" + @gateway.IDGateway.ToString() + "#tab-6")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoFechar</a>
                                        </div>
                                    </div>
                                </div>

                                <br />

                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="panel-body">
                                            <div class="row">
                                                <div class="form-group col-lg-4">
                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.TipoInterface_IoT</label><br />
                                                    @Html.DropDownListFor(model => model.IDTipoInterface_IoT, new SelectList(ViewBag.listatiposInterfaceIoT, "ID", "Descricao"),
                                                            string.Format("{0}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione),
                                                            new { @class = "form-control" })
                                                </div>
                                            </div>
                                            <br />

                                            <div class="div_MODEM" style="display:none;">
                                                <div class="row">
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Usuario</label>
                                                        @Html.TextBoxFor(model => model.mdmUser, new { @class = "form-control", @maxlength = "23" })
                                                    </div>
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Senha</label>
                                                        @Html.TextBoxFor(model => model.mdmPwd, new { @class = "form-control", @maxlength = "23" })
                                                    </div>
                                                </div>
                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;APN</label>
                                                        @Html.TextBoxFor(model => model.mdmApn, new { @class = "form-control", @maxlength = "35" })
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="div_WIFI" style="display:none;">
                                                <div class="row">
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;SSID</label>
                                                        @Html.TextBoxFor(model => model.wfSsid, new { @class = "form-control", @maxlength = "35" })
                                                    </div>
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Senha</label>
                                                        @Html.TextBoxFor(model => model.wfPwd, new { @class = "form-control", @maxlength = "23" })
                                                    </div>
                                                </div>
                                            </div>

                                            <br />

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="overlay_enviando" style="display: none">
                            <div class="fa fa-upload icone_enviando">
                            </div>
                            <div class="text_enviando some_desktop">
                                <span>@SmartEnergy.Resources.ConfiguracaoTexts.EnviaConfig</span>
                            </div>
                            <div class="spinner-enviando">
                                <div class="sk-spinner sk-spinner-wandering-cubes">
                                    <div class="sk-spinner sk-spinner-wave">
                                        <div class="sk-rect1 text-ligh"></div>
                                        <div class="sk-rect2"></div>
                                        <div class="sk-rect3"></div>
                                        <div class="sk-rect4"></div>
                                        <div class="sk-rect5"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            }

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/declaracoes/Constants")
    @Scripts.Render("~/declaracoes/Validates")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        $(document).ready(function () {

            //
            // VALIDACAO DOS CAMPOS
            //
            Validator_AddMethods();

            $("#form").validate({
                rules: {
                    TipoInterface_IoT: {
                        required: true
                    },
                    mdmUser: {
                        alphanumeric: true,
                        minlength: 3,
                        maxlength: 23
                    },
                    mdmPwd: {
                        alphanumeric: true,
                        minlength: 3,
                        maxlength: 23
                    },
                    mdmApn: {
                        alphanumeric: true,
                        minlength: 3,
                        maxlength: 35
                    },
                    wfSsid: {
                        alphanumeric: true,
                        minlength: 3,
                        maxlength: 35
                    },
                    wfPwd: {
                        alphanumeric: true,
                        minlength: 3,
                        maxlength: 23
                    }
                },
                highlight: function (element) {
                    $(element).closest('.control-group').removeClass('success').addClass('error');
                },
                unhighlight: function (element) {
                    $(element).closest('.control-group').addClass('valid success').removeClass('error');
                }
            });

            // desabilita campos
            disableAll();

            // altera interface IoT
            $('#IDTipoInterface_IoT').change(function () {

                // obtem a interface
                var id = $(this).find(":selected").val();
                disableInterfaceIoT(id);
            });

            // retornos da solicitacao da configuracao
            var solicitaProg = (@ViewBag.SolicitaProg.ToString().ToLower());

            // verifico se retornou erro
            if  (solicitaProg == false)
            {
                $("#BotaoEnviar").attr('disabled', true);

                swal({
                    title: "Erro na recepção da configuração!",
                    type: "warning",
                    confirmButtonColor: "#1ab394",
                    confirmButtonText: "Fechar",
                    closeOnConfirm: true
                }, function(isConfirm){

                    var IDGateway = parseInt(document.getElementById("IDGateway").value);

                    // retorna para a pagina editar
                    var url = '/Configuracao/Gateway_Editar?IDGateway=' + IDGateway + '#tab-6';
                    window.location.href = url;
                });
            }

            $('form input').on('keypress', function (e) {
                return e.which !== 13;
            });

        });

        function disableInterfaceIoT(IDTipoInterface_IoT) {

            switch (IDTipoInterface_IoT)
            {
                default:
                case "0":     // nenhum
                    $('.div_MODEM').css("display", "none");
                    $('.div_WIFI').css("display", "none");
                    break;

                case "1":     // 2G / 4G
                    $('.div_MODEM').css("display", "block");
                    $('.div_WIFI').css("display", "none");
                    //$("#mdmUser").attr('disabled', false);
                    //$("#mdmPwd").attr('disabled', false);
                    //$("#mdmApn").attr('disabled', false);
                    break;

                case "2":     // Wi-Fi
                    $('.div_MODEM').css("display", "none");
                    $('.div_WIFI').css("display", "block");
                    //$("#wfSsid").attr('disabled', false);
                    //$("#wfPwd").attr('disabled', false);
                    break;
            }
        }

        function disableAll() {

            // interface IoT
            var IDTipoInterface_IoT = document.getElementById('IDTipoInterface_IoT').value;
            disableInterfaceIoT(IDTipoInterface_IoT);

            // permissão
            var permissao = Math.ceil(@ViewBag.Permissao);

            // verifica permissao
            switch (permissao) {
                case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
                case PERMISSOES_GERENTE:            // 1 - permissao de Gerente: ve tudo e escreve parte
                case PERMISSOES_OPERADOR:           // 2 - permissao de Operador: ve tudo e nao pode escrever
                case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
                case PERMISSOES_CONSULTOR:          // 5 - permissao de Consultor
                case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte

                    $("#BotaoEnviar").attr('disabled', false);
                    //$("#IDTipoInterface_IoT").attr('disabled', false);
                    //$("#mdmApn").attr('disabled', false);
                    //$("#mdmUser").attr('disabled', false);
                    //$("#mdmPwd").attr('disabled', false);
                    //$("#wfSsid").attr('disabled', false);
                    //$("#wfPwd").attr('disabled', false);
                    break;

                default:
                case PERMISSOES_PRODUCAO:           // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                    break;
            }
        }


        function Enviar(IDGateway) {

            var $form = $('#form');

            var IsTabValid = $("#form").valid();;

            // verifica se entradas sao validas
            if (!IsTabValid) return false;

            swal({
                title: "Deseja enviar?",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#1ab394",
                confirmButtonText: "Enviar",
                cancelButtonText: "Cancelar",
                closeOnConfirm: true
            }, function () {
                event.stopPropagation();

                // aguarde
                $('.overlay_enviando').toggle();

                // retiro campos desabilitados, pois nao envia via POST
                $(":disabled", $('#form')).removeAttr("disabled");

                $.ajax({
                    url: '/Configuracao/GateE_RedeIoT_Enviar',
                    data: $form.serialize(),
                    type: 'POST',
                    success: function (data) {

                        // fim
                        $('.overlay_enviando').toggle();

                        setTimeout(function () {

                            // verifica se erro
                            if (data.status == "ERRO") {
                                swal({
                                    title: "Erro",
                                    text: data.erro,
                                    type: "warning",
                                    confirmButtonColor: "#f8ac59",
                                    confirmButtonText: "Fechar",
                                }, function () {

                                    // retorna para programacoes
                                    var url = '/Configuracao/GateE_RedeIoT?IDGateway=' + IDGateway;

                                    window.location.href = url;
                                });
                            }
                            else {
                                swal({
                                    title: "Enviado com sucesso",
                                    type: "success",
                                    confirmButtonColor: "#18a689",
                                    confirmButtonText: "Fechar",
                                }, function () {

                                    // retorna para pagina de edicao da gateway
                                    var url = '/Configuracao/Gateway_Editar?IDGateway=' + IDGateway + '#tab-6';

                                    window.location.href = url;

                                });
                            }

                        }, 100);

                    },
                    error: function (xhr, status, error) {

                        // fim
                        $('.overlay_enviando').toggle();

                        setTimeout(function () {

                            swal({
                                title: "Erro",
                                text: "Falha no envio da configuração!",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // retorna para programacoes
                                var url = '/Configuracao/GateE_RedeIoT?IDGateway=' + IDGateway;

                                window.location.href = url;
                            });
                        }, 100);
                    },
                });
            });
        };

    </script>
}
