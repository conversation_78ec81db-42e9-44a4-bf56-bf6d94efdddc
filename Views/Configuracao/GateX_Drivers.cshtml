﻿@model SmartEnergyLib.SQL.GateX_Driver_Dominio

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.ConfiguracaoTexts.Drivers;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("GateX_Drivers", "Configuracao", new { ReturnUrl = ViewBag.ReturnUrl }, FormMethod.Post, new { id = "form", role = "form" }))
            {
                @Html.Hidden("IDGateway", Model.IDGateway);

                @Html.Hidden("xUpldPass", Model.xUpldPass);
                @Html.Hidden("xFtpPass", Model.xFtpPass);
                                                                          
                @Html.Hidden("xH2_ini", Model.xH2_ini);
                @Html.Hidden("xH2_cEN_QtV", Model.xH2_cEN_QtV);
                @Html.Hidden("xH2_cEN_iTGG", Model.xH2_cEN_iTGG);
                @Html.Hidden("xH2_cEN_QtBk", Model.xH2_cEN_QtBk);
                @Html.Hidden("xH2_cUT_QtV", Model.xH2_cUT_QtV);
                @Html.Hidden("xH2_cUT_iTGG", Model.xH2_cUT_iTGG);
                @Html.Hidden("xH2_cUT_QtBk", Model.xH2_cUT_QtBk);
                @Html.Hidden("xH2_cAN_QtV", Model.xH2_cAN_QtV);
                @Html.Hidden("xH2_cAN_QtBk", Model.xH2_cAN_QtBk);
                @Html.Hidden("xH2_cCY_QtV", Model.xH2_cCY_QtV);
                @Html.Hidden("xH2_cCY_QtBk", Model.xH2_cCY_QtBk);

                @Html.Hidden("serK_serFN", Model.serK_serFN);
                @Html.Hidden("serK_iBps", Model.serK_iBps);
                @Html.Hidden("serP_iBps", Model.serP_iBps);
                                
                @Html.Hidden("rIOxR_io_Cli1_svIp", Model.rIOxR_io_Cli1_svIp);
                @Html.Hidden("rIOxR_io_Cli1_svRmTy", Model.rIOxR_io_Cli1_svRmTy);
                @Html.Hidden("rIOxR_io_Cli1_svRmQt", Model.rIOxR_io_Cli1_svRmQt);
                @Html.Hidden("rIOxR_io_Cli1_svRm", Model.rIOxR_io_Cli1_svRm);
                @Html.Hidden("rIOxR_io_Cli1_svRmFx", Model.rIOxR_io_Cli1_svRmFx);

                @Html.Hidden("rIOxR_io_Cli2_svIp", Model.rIOxR_io_Cli2_svIp);
                @Html.Hidden("rIOxR_io_Cli2_svRmTy", Model.rIOxR_io_Cli2_svRmTy);
                @Html.Hidden("rIOxR_io_Cli2_svRmQt", Model.rIOxR_io_Cli2_svRmQt);
                @Html.Hidden("rIOxR_io_Cli2_svRm", Model.rIOxR_io_Cli2_svRm);
                @Html.Hidden("rIOxR_io_Cli2_svRmFx", Model.rIOxR_io_Cli2_svRmFx);

                @Html.Hidden("rIOxR_io_CliK_svIp", Model.rIOxR_io_CliK_svIp);
                @Html.Hidden("rIOxR_io_CliK_svRmTy", Model.rIOxR_io_CliK_svRmTy);
                @Html.Hidden("rIOxR_io_CliK_svRmQt", Model.rIOxR_io_CliK_svRmQt);
                @Html.Hidden("rIOxR_io_CliK_svRm", Model.rIOxR_io_CliK_svRm);
                @Html.Hidden("rIOxR_io_CliK_svRmFx", Model.rIOxR_io_CliK_svRmFx);
                
                @Html.Hidden("rIOxQ_io_Cli1_svIp", Model.rIOxQ_io_Cli1_svIp);
                @Html.Hidden("rIOxQ_io_Cli1_svRmTy", Model.rIOxQ_io_Cli1_svRmTy);
                @Html.Hidden("rIOxQ_io_Cli1_svRmQt", Model.rIOxQ_io_Cli1_svRmQt);
                @Html.Hidden("rIOxQ_io_Cli1_svRm", Model.rIOxQ_io_Cli1_svRm);
                @Html.Hidden("rIOxQ_io_Cli1_svRmFx", Model.rIOxQ_io_Cli1_svRmFx);

                @Html.Hidden("rIOxQ_io_Cli2_svIp", Model.rIOxQ_io_Cli2_svIp);
                @Html.Hidden("rIOxQ_io_Cli2_svRmTy", Model.rIOxQ_io_Cli2_svRmTy);
                @Html.Hidden("rIOxQ_io_Cli2_svRmQt", Model.rIOxQ_io_Cli2_svRmQt);
                @Html.Hidden("rIOxQ_io_Cli2_svRm", Model.rIOxQ_io_Cli2_svRm);
                @Html.Hidden("rIOxQ_io_Cli2_svRmFx", Model.rIOxQ_io_Cli2_svRmFx);

                @Html.Hidden("rIOxQ_io_CliK_svIp", Model.rIOxQ_io_CliK_svIp);
                @Html.Hidden("rIOxQ_io_CliK_svRmTy", Model.rIOxQ_io_CliK_svRmTy);
                @Html.Hidden("rIOxQ_io_CliK_svRmQt", Model.rIOxQ_io_CliK_svRmQt);
                @Html.Hidden("rIOxQ_io_CliK_svRm", Model.rIOxQ_io_CliK_svRm);
                @Html.Hidden("rIOxQ_io_CliK_svRmFx", Model.rIOxQ_io_CliK_svRmFx);
                
                @Html.Hidden("rIOxK_io_Pro", Model.rIOxK_io_Pro);
                @Html.Hidden("rIOxK_io_Try", Model.rIOxK_io_Try);
                @Html.Hidden("rIOxK_io_TO", Model.rIOxK_io_TO);
                @Html.Hidden("rIOxK_io_Dly", Model.rIOxK_io_Dly);

                @Html.Hidden("rIOxK_io_uHb", Model.rIOxK_io_uHb);
                @Html.Hidden("rIOxK_io_uPo", Model.rIOxK_io_uPo);

                @Html.Hidden("rIOxK_io_cHb", Model.rIOxK_io_cHb);
                @Html.Hidden("rIOxK_io_cPo", Model.rIOxK_io_cPo);

                @Html.Hidden("rIOxK_io_Cli1_svIp", Model.rIOxK_io_Cli1_svIp);
                @Html.Hidden("rIOxK_io_Cli1_svRmTy", Model.rIOxK_io_Cli1_svRmTy);
                @Html.Hidden("rIOxK_io_Cli1_svRmQt", Model.rIOxK_io_Cli1_svRmQt);
                @Html.Hidden("rIOxK_io_Cli1_svRm", Model.rIOxK_io_Cli1_svRm);
                @Html.Hidden("rIOxK_io_Cli1_svRmFx", Model.rIOxK_io_Cli1_svRmFx);

                @Html.Hidden("rIOxK_io_Cli2_svIp", Model.rIOxK_io_Cli2_svIp);
                @Html.Hidden("rIOxK_io_Cli2_svRmTy", Model.rIOxK_io_Cli2_svRmTy);
                @Html.Hidden("rIOxK_io_Cli2_svRmQt", Model.rIOxK_io_Cli2_svRmQt);
                @Html.Hidden("rIOxK_io_Cli2_svRm", Model.rIOxK_io_Cli2_svRm);
                @Html.Hidden("rIOxK_io_Cli2_svRmFx", Model.rIOxK_io_Cli2_svRmFx);

                @Html.Hidden("rIOxK_io_CliK_svIp", Model.rIOxK_io_CliK_svIp);
                @Html.Hidden("rIOxK_io_CliK_svRmTy", Model.rIOxK_io_CliK_svRmTy);
                @Html.Hidden("rIOxK_io_CliK_svRmQt", Model.rIOxK_io_CliK_svRmQt);
                @Html.Hidden("rIOxK_io_CliK_svRm", Model.rIOxK_io_CliK_svRm);
                @Html.Hidden("rIOxK_io_CliK_svRmFx", Model.rIOxK_io_CliK_svRmFx);

                @Html.Hidden("lIO_dio_FN_din", Model.lIO_dio_FN_din);
                @Html.Hidden("lIO_dio_FN_dout", Model.lIO_dio_FN_dout);
                @Html.Hidden("lIO_dio_pul_sON", Model.lIO_dio_pul_sON);
                @Html.Hidden("lIO_dio_rpt_sON", Model.lIO_dio_rpt_sON);

                @Html.Hidden("rALM_alm_pro", Model.rALM_alm_pro);
                @Html.Hidden("rALM_alm_po", Model.rALM_alm_po);
                @Html.Hidden("rALM_alm_sIP", Model.rALM_alm_sIP);
       
                @Html.Hidden("xUpldFrq_hora", Model.xUpldFrq_hora);
                @Html.Hidden("xUpldFrq_min", Model.xUpldFrq_min);
                @Html.Hidden("xUpld1st_hora", Model.xUpld1st_hora);
                @Html.Hidden("xUpld1st_min", Model.xUpld1st_min);

                @Html.Hidden("xIspPass", Model.xIspPass);
                                
                @Html.Hidden("xAlm_SndHab", Model.xAlm_SndHab);
                @Html.Hidden("xAlm_Port", Model.xAlm_Port);
        
                @Html.Hidden("xUpldQtEN", Model.xUpldQtEN);
                @Html.Hidden("xUpldQtUT", Model.xUpldQtUT);
                @Html.Hidden("xUpldQtAN", Model.xUpldQtAN);
                @Html.Hidden("xUpldQtCY", Model.xUpldQtCY);
                @Html.Hidden("xUpldLst", Model.xUpldLst);

                @Html.Hidden("mqTyLog", Model.mqTyLog);
                @Html.Hidden("mq_nLog", Model.mq_nLog);
                @Html.Hidden("mqCnFg", Model.mqCnFg);
                @Html.Hidden("mqPuFg", Model.mqPuFg);
                @Html.Hidden("mqPort", Model.mqPort);
                @Html.Hidden("mqKeep", Model.mqKeep);
                @Html.Hidden("mqSuQoS", Model.mqSuQoS);
                @Html.Hidden("mqPuQoS", Model.mqPuQoS);
                @Html.Hidden("mqPoSsl", Model.mqPoSsl);
                @Html.Hidden("mqPoTls", Model.mqPoTls);
                @Html.Hidden("mqClID", Model.mqClID);
                @Html.Hidden("mqDomi", Model.mqDomi);
                @Html.Hidden("mqUser", Model.mqUser);
                @Html.Hidden("mqPass", Model.mqPass);

                
                <div class="panel panel-title">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="panel panel-success">
                                    <div class="panel-heading">

                                        @{
                                            GatewaysDominio gateway = ViewBag.Gateway;
                                            @Html.Hidden("IDGateway", gateway.IDGateway);
                                        }

                                        <h4>Gateway</h4><br />
                                        @gateway.Nome

                                        @{
                                            int IDTipoAcesso = ViewBag._IDTipoAcesso;

                                            if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                            {
                                                <span> [@gateway.IDGateway]</span>
                                            }
                                        }

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="panel panel-title">
                            <div class="panel-heading">
                                <div style="text-align:center;">
                                    <h4>@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoDrivers</h4>
                                </div>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="form-group col-lg-4">
                                        <label class="control-label">&nbsp;ID Usuário</label>
                                        @Html.TextBoxFor(model => model.xIdUser, new { @class = "form-control", @maxlength = "19" })
                                    </div>
                                    <div class="col-lg-offset-2 col-lg-6">
                                        <div class="ibox-tools">
                                            <button type="button" class="btn btn-primary" onclick="Enviar(@gateway.IDGateway);" disabled="" id="BotaoEnviar">@SmartEnergy.Resources.ComumTexts.BotaoEnviar</button>
                                            <a href='@("/Configuracao/GateX_Drivers?IDGateway=" + @gateway.IDGateway.ToString() + "&Origem=1")' class="btn btn-default" style="color:#000000; background-color:lightgrey;">@SmartEnergy.Resources.ComumTexts.BotaoLer</a>
                                            <a href='@("/Configuracao/Gateway_Editar?IDGateway=" + @gateway.IDGateway.ToString() + "#tab-6")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                        </div>
                                    </div>
                                </div>

                                <br />

                                <div class="tabs-container">
                                    <ul class="nav nav-tabs">
                                        <li class="active"><a data-toggle="tab" href="#tab-1"><i class="fa fa-cogs"></i>@SmartEnergy.Resources.ConfiguracaoTexts.Geral</a></li>
                                        <li class="tab-2"><a data-toggle="tab" href="#tab-2"><i class="fa fa-usb"></i>@SmartEnergy.Resources.ConfiguracaoTexts.Rede_IO_Distribuido</a></li>
                                        <li class="tab-3"><a data-toggle="tab" href="#tab-3"><i class="fa fa-database"></i>@SmartEnergy.Resources.ConfiguracaoTexts.MemoriaMassa</a></li>
                                        <li class="tab-4"><a data-toggle="tab" href="#tab-4"><i class="fa fa-upload"></i>Upload</a></li>
                                    </ul>

                                    <div class="tab-content">

                                        <div id="tab-1" class="tab-pane active">
                                            <div class="panel-body">

                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <div class="panel panel-default">
                                                            <div class="panel-heading">
                                                                <h4>@SmartEnergy.Resources.ConfiguracaoTexts.InformacoesEquipamento</h4>
                                                            </div>
                                                            <div class="panel-body">
                                                                <div class="row">
                                                                    <div class="form-group col-lg-4">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Modelo</label>
                                                                        @Html.TextBoxFor(model => model.Modelo, new { @class = "form-control", @disabled = "disabled" })
                                                                    </div>
                                                                    <div class="form-group col-lg-4">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Versao</label>
                                                                        @Html.TextBoxFor(model => model.Versao, new { @class = "form-control", @disabled = "disabled" })
                                                                    </div>
                                                                    <div class="form-group col-lg-4">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.DriverRedeIO</label>
                                                                        @Html.TextBoxFor(model => model.DriverRemota, new { @class = "form-control", @disabled = "disabled" })
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <div class="panel panel-default">
                                                            <div class="panel-heading">
                                                                <h4>Ethernet</h4>
                                                            </div>
                                                            <div class="panel-body">
                                                                <div class="row">
                                                                    <div class="form-group col-lg-3">
                                                                        <label class="control-label">&nbsp;IP</label>
                                                                        @Html.TextBoxFor(model => model.xMyIP, new { @class = "form-control", @disabled = "disabled" })
                                                                    </div>
                                                                    <div class="form-group col-lg-3">
                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Mascara</label>
                                                                        @Html.TextBoxFor(model => model.xMaskIP, new { @class = "form-control", @disabled = "disabled" })
                                                                    </div>
                                                                    <div class="form-group col-lg-3">
                                                                        <label class="control-label">&nbsp;Gateway</label>
                                                                        @Html.TextBoxFor(model => model.xGateway, new { @class = "form-control", @disabled = "disabled" })
                                                                    </div>
                                                                    <div class="form-group col-lg-3">
                                                                        <label class="control-label">&nbsp;DNS</label>
                                                                        @Html.TextBoxFor(model => model.xsrvDns, new { @class = "form-control", @disabled = "disabled" })
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-lg-9">
                                                        <div class="panel panel-default">
                                                            <div class="panel-heading">
                                                                <h4>@SmartEnergy.Resources.ConfiguracaoTexts.FuncionalidadeSerial</h4>
                                                            </div>
                                                            <div class="panel-body">
                                                                <div class="row">
                                                                    <div class="form-group col-lg-2">
                                                                        <label class="control-label">&nbsp;232</label>
                                                                        @Html.DropDownListFor(model => model.ser2_serFN, new SelectList(ViewBag.lista_ser2_serFN, "ID", "Descricao", Model.ser2_serFN), new { @class = "form-control", @disabled = "disabled" })
                                                                    </div>
                                                                    <div class="form-group col-lg-2">
                                                                        <label class="control-label">&nbsp;Rx P</label>
                                                                        @Html.DropDownListFor(model => model.serP_serFN, new SelectList(ViewBag.lista_serP_serFN, "ID", "Descricao", Model.serP_serFN), new { @class = "form-control", @disabled = "disabled" })
                                                                    </div>
                                                                    <div class="form-group col-lg-offset-1 col-lg-2">
                                                                        <label class="control-label">&nbsp;485 Q</label>
                                                                        @Html.DropDownListFor(model => model.serQ_serFN, new SelectList(ViewBag.lista_serQ_serFN, "ID", "Descricao", Model.serQ_serFN), new { @class = "form-control", @disabled = "disabled" })
                                                                    </div>
                                                                    <div class="form-group col-lg-2">
                                                                        <label class="control-label">&nbsp;485 R</label>
                                                                        @Html.DropDownListFor(model => model.serR_serFN, new SelectList(ViewBag.lista_serR_serFN, "ID", "Descricao", Model.serR_serFN), new { @class = "form-control", @disabled = "disabled" })
                                                                    </div>
                                                                    <div class="form-group col-lg-2">
                                                                        <label class="control-label">&nbsp;485 S</label>
                                                                        @Html.DropDownListFor(model => model.serS_serFN, new SelectList(ViewBag.lista_serS_serFN, "ID", "Descricao", Model.serS_serFN), new { @class = "form-control", @disabled = "disabled" })
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="panel panel-default">
                                                            <div class="panel-heading">
                                                                <h4>@SmartEnergy.Resources.ConfiguracaoTexts.RedeIoT</h4>
                                                            </div>
                                                            <div class="panel-body">
                                                                <div class="row">
                                                                    <div class="form-group col-lg-12">
                                                                        <label class="control-label" style="margin-bottom: 10px;">&nbsp;</label>
                                                                        <div class="i-checks" style="margin-bottom: 6px;">
                                                                            @Html.CheckBoxFor(model => model.mqEnable, new { @disabled = "disabled" } )&nbsp;&nbsp;<span style="margin-right: 54px;">@SmartEnergy.Resources.ConfiguracaoTexts.Habilitado</span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <div class="panel panel-default">
                                                            <div class="panel-heading">
                                                                <h4>CODI</h4>
                                                            </div>
                                                            <div class="panel-body">
                                                                <div class="row">
                                                                    <div class="col-lg-4">
                                                                        <div class="row">
                                                                            <div class="form-group col-lg-12">
                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.UsarSinal</label>
                                                                                @Html.DropDownListFor(model => model.COD_snCod, new SelectList(ViewBag.lista_COD_snCod, "ID", "Descricao", Model.COD_snCod), new { @class = "form-control", @disabled = "disabled" })
                                                                            </div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-lg-12">
                                                                                <div class="panel panel-default">
                                                                                    <div class="panel-heading">
                                                                                        <h4>DIN Virtual</h4>
                                                                                    </div>
                                                                                    <div class="panel-body">
                                                                                        <div class="row">
                                                                                            <div class="form-group col-lg-6">
                                                                                                <label class="control-label">&nbsp;UDP</label>
                                                                                                @Html.DropDownListFor(model => model.COD_uHb_rxDV, new SelectList(ViewBag.lista_COD_uHb_rxP, "ID", "Descricao", Model.COD_uHb_rxDV), new { @class = "form-control", @disabled = "disabled" })
                                                                                            </div>
                                                                                            <div class="form-group col-lg-6">
                                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Porta</label>
                                                                                                @Html.TextBoxFor(model => model.COD_uPo_rxDV, new { @class = "form-control", @disabled = "disabled" })
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-lg-4">
                                                                        <div class="panel panel-default">
                                                                            <div class="panel-heading">
                                                                                <h4>P</h4>
                                                                            </div>
                                                                            <div class="panel-body">
                                                                                <div class="row">
                                                                                    <div class="form-group col-lg-12">
                                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Protocolo</label>
                                                                                        @Html.DropDownListFor(model => model.COD_rxP_pro, new SelectList(ViewBag.lista_ProtocoloCODI, "ID", "Descricao", Model.COD_rxP_pro), new { @class = "form-control", @disabled = "disabled" })
                                                                                    </div>
                                                                                </div>
                                                                                <div class="row">
                                                                                    <div class="form-group col-lg-6">
                                                                                        <label class="control-label">&nbsp;UDP</label>
                                                                                        @Html.DropDownListFor(model => model.COD_uHb_rxP, new SelectList(ViewBag.lista_COD_uHb_rxP, "ID", "Descricao", Model.COD_uHb_rxP), new { @class = "form-control", @disabled = "disabled" })
                                                                                    </div>
                                                                                    <div class="form-group col-lg-6">
                                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Porta</label>
                                                                                        @Html.TextBoxFor(model => model.COD_uPo_rxP, new { @class = "form-control", @disabled = "disabled" })
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-lg-4">
                                                                        <div class="panel panel-default">
                                                                            <div class="panel-heading">
                                                                                <h4>Q</h4>
                                                                            </div>
                                                                            <div class="panel-body">
                                                                                <div class="row">
                                                                                    <div class="form-group col-lg-12">
                                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Protocolo</label>
                                                                                        @Html.DropDownListFor(model => model.COD_rxQ_pro, new SelectList(ViewBag.lista_ProtocoloCODI, "ID", "Descricao", Model.COD_rxQ_pro), new { @class = "form-control", @disabled = "disabled" })
                                                                                    </div>
                                                                                </div>
                                                                                <div class="row">
                                                                                    <div class="form-group col-lg-6">
                                                                                        <label class="control-label">&nbsp;UDP</label>
                                                                                        @Html.DropDownListFor(model => model.COD_uHb_rxQ, new SelectList(ViewBag.lista_COD_uHb_rxP, "ID", "Descricao", Model.COD_uHb_rxQ), new { @class = "form-control", @disabled = "disabled" })
                                                                                    </div>
                                                                                    <div class="form-group col-lg-6">
                                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Porta</label>
                                                                                        @Html.TextBoxFor(model => model.COD_uPo_rxQ, new { @class = "form-control", @disabled = "disabled" })
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <div class="panel panel-default">
                                                            <div class="panel-heading">
                                                                <h4>@SmartEnergy.Resources.ConfiguracaoTexts.RedeSupervisao</h4>
                                                            </div>
                                                            <div class="panel-body">
                                                                <div class="row">
                                                                    <div class="col-lg-3">
                                                                        <div class="row">
                                                                            <div class="form-group col-lg-12">
                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Endereco</label>
                                                                                @Html.TextBoxFor(model => model.rSU_ad, new { @class = "form-control", @disabled = "disabled" })
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-lg-3">
                                                                        <div class="panel panel-default">
                                                                            <div class="panel-heading">
                                                                                <h4>Ethernet</h4>
                                                                            </div>
                                                                            <div class="panel-body">
                                                                                <div class="row">
                                                                                    <div class="form-group col-lg-6">
                                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Protocolo</label>
                                                                                        @Html.DropDownListFor(model => model.rSU_ethPro, new SelectList(ViewBag.lista_ProtocoloSuperv, "ID", "Descricao", Model.rSU_ethPro), new { @class = "form-control", @disabled = "disabled" })
                                                                                    </div>
                                                                                    <div class="form-group col-lg-6">
                                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Porta</label>
                                                                                        @Html.TextBoxFor(model => model.rSU_ethPo, new { @class = "form-control", @disabled = "disabled" })
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-lg-3">
                                                                        <div class="panel panel-default">
                                                                            <div class="panel-heading">
                                                                                <h4>232</h4>
                                                                            </div>
                                                                            <div class="panel-body">
                                                                                <div class="row">
                                                                                    <div class="form-group col-lg-6">
                                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Protocolo</label>
                                                                                        @Html.DropDownListFor(model => model.rSU_232pro, new SelectList(ViewBag.lista_ProtocoloSuperv, "ID", "Descricao", Model.rSU_232pro), new { @class = "form-control", @disabled = "disabled" })
                                                                                    </div>
                                                                                    <div class="form-group col-lg-6">
                                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Velocidade</label>
                                                                                        @Html.DropDownListFor(model => model.ser2_iBps, new SelectList(ViewBag.lista_Velocidade, "ID", "Descricao", Model.ser2_iBps), new { @class = "form-control", @disabled = "disabled" })
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-lg-3">
                                                                        <div class="panel panel-default">
                                                                            <div class="panel-heading">
                                                                                <h4>485 S</h4>
                                                                            </div>
                                                                            <div class="panel-body">
                                                                                <div class="row">
                                                                                    <div class="form-group col-lg-6">
                                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Protocolo</label>
                                                                                        @Html.DropDownListFor(model => model.rSU_48Spro, new SelectList(ViewBag.lista_ProtocoloSuperv, "ID", "Descricao", Model.rSU_48Spro), new { @class = "form-control", @disabled = "disabled" })
                                                                                    </div>
                                                                                    <div class="form-group col-lg-6">
                                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Velocidade</label>
                                                                                        @Html.DropDownListFor(model => model.serS_iBps, new SelectList(ViewBag.lista_Velocidade, "ID", "Descricao", Model.serS_iBps), new { @class = "form-control", @disabled = "disabled" })
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                        <div id="tab-2" class="tab-pane">
                                            <div class="panel-body">

                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <div class="panel panel-default">
                                                            <div class="panel-heading">
                                                                <h4>Q</h4>
                                                            </div>
                                                            <div class="panel-body">
                                                                <div class="row">
                                                                    <div class="col-lg-3">
                                                                        <div class="row">
                                                                            <div class="form-group col-lg-12">
                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Protocolo</label>
                                                                                @Html.DropDownListFor(model => model.rIOxQ_io_Pro, new SelectList(ViewBag.lista_ProtocoloIO, "ID", "Descricao", Model.rIOxQ_io_Pro), new { @class = "form-control", @disabled = "disabled" })
                                                                            </div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="form-group col-lg-12">
                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Tentativas</label>
                                                                                @Html.TextBoxFor(model => model.rIOxQ_io_Try, new { @class = "form-control", @disabled = "disabled" })
                                                                            </div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="form-group col-lg-12">
                                                                                <label class="control-label">&nbsp;Timeout (ms)</label>
                                                                                @Html.TextBoxFor(model => model.rIOxQ_io_TO, new { @class = "form-control", @disabled = "disabled" })
                                                                            </div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="form-group col-lg-12">
                                                                                <label class="control-label">&nbsp;Delay (ms)</label>
                                                                                @Html.TextBoxFor(model => model.rIOxQ_io_Dly, new { @class = "form-control", @disabled = "disabled" })
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-lg-3">
                                                                        <br />
                                                                        <div class="row">
                                                                            <div class="col-lg-12">
                                                                                <div class="panel panel-default" style="margin-top: 5px;">
                                                                                    <div class="panel-heading">
                                                                                        <h4>UDP</h4>
                                                                                    </div>
                                                                                    <div class="panel-body">
                                                                                        <div class="row">
                                                                                            <div class="form-group col-lg-12">
                                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Habilitar</label>
                                                                                                @Html.DropDownListFor(model => model.rIOxQ_io_uHb, new SelectList(ViewBag.lista_SimNao, "ID", "Descricao", Model.rIOxQ_io_uHb), new { @class = "form-control", @disabled = "disabled" })
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="row">
                                                                                            <div class="form-group col-lg-12">
                                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Porta</label>
                                                                                                @Html.TextBoxFor(model => model.rIOxQ_io_uPo, new { @class = "form-control", @disabled = "disabled" })
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-lg-3">
                                                                        <br />
                                                                        <div class="row">
                                                                            <div class="col-lg-12">
                                                                                <div class="panel panel-default" style="margin-top: 5px;">
                                                                                    <div class="panel-heading">
                                                                                        <h4>MBAP</h4>
                                                                                    </div>
                                                                                    <div class="panel-body">
                                                                                        <div class="row">
                                                                                            <div class="form-group col-lg-12">
                                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Habilitar</label>
                                                                                                @Html.DropDownListFor(model => model.rIOxQ_io_cHb, new SelectList(ViewBag.lista_SimNao, "ID", "Descricao", Model.rIOxQ_io_cHb), new { @class = "form-control", @disabled = "disabled" })
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="row">
                                                                                            <div class="form-group col-lg-12">
                                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Porta</label>
                                                                                                @Html.TextBoxFor(model => model.rIOxQ_io_cPo, new { @class = "form-control", @disabled = "disabled" })
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-lg-3">
                                                                        <br />
                                                                        <div class="row">
                                                                            <div class="col-lg-12">
                                                                                <div class="panel panel-default" style="margin-top: 5px;">
                                                                                    <div class="panel-heading">
                                                                                        <h4>485 Q</h4>
                                                                                    </div>
                                                                                    <div class="panel-body">
                                                                                        <div class="row">
                                                                                            <div class="form-group col-lg-12">
                                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Velocidade</label>
                                                                                                @Html.DropDownListFor(model => model.serQ_iBps, new SelectList(ViewBag.lista_Velocidade, "ID", "Descricao", Model.serQ_iBps), new { @class = "form-control", @disabled = "disabled" })
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <div class="panel panel-default">
                                                            <div class="panel-heading">
                                                                <h4>R</h4>
                                                            </div>
                                                            <div class="panel-body">
                                                                <div class="row">
                                                                    <div class="col-lg-3">
                                                                        <div class="row">
                                                                            <div class="form-group col-lg-12">
                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Protocolo</label>
                                                                                @Html.DropDownListFor(model => model.rIOxR_io_Pro, new SelectList(ViewBag.lista_ProtocoloIO, "ID", "Descricao", Model.rIOxR_io_Pro), new { @class = "form-control", @disabled = "disabled" })
                                                                            </div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="form-group col-lg-12">
                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Tentativas</label>
                                                                                @Html.TextBoxFor(model => model.rIOxR_io_Try, new { @class = "form-control", @disabled = "disabled" })
                                                                            </div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="form-group col-lg-12">
                                                                                <label class="control-label">&nbsp;Timeout (ms)</label>
                                                                                @Html.TextBoxFor(model => model.rIOxR_io_TO, new { @class = "form-control", @disabled = "disabled" })
                                                                            </div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="form-group col-lg-12">
                                                                                <label class="control-label">&nbsp;Delay (ms)</label>
                                                                                @Html.TextBoxFor(model => model.rIOxR_io_Dly, new { @class = "form-control", @disabled = "disabled" })
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-lg-3">
                                                                        <br />
                                                                        <div class="row">
                                                                            <div class="col-lg-12">
                                                                                <div class="panel panel-default" style="margin-top: 5px;">
                                                                                    <div class="panel-heading">
                                                                                        <h4>UDP</h4>
                                                                                    </div>
                                                                                    <div class="panel-body">
                                                                                        <div class="row">
                                                                                            <div class="form-group col-lg-12">
                                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Habilitar</label>
                                                                                                @Html.DropDownListFor(model => model.rIOxR_io_uHb, new SelectList(ViewBag.lista_SimNao, "ID", "Descricao", Model.rIOxR_io_uHb), new { @class = "form-control", @disabled = "disabled" })
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="row">
                                                                                            <div class="form-group col-lg-12">
                                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Porta</label>
                                                                                                @Html.TextBoxFor(model => model.rIOxR_io_uPo, new { @class = "form-control", @disabled = "disabled" })
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-lg-3">
                                                                        <br />
                                                                        <div class="row">
                                                                            <div class="col-lg-12">
                                                                                <div class="panel panel-default" style="margin-top: 5px;">
                                                                                    <div class="panel-heading">
                                                                                        <h4>MBAP</h4>
                                                                                    </div>
                                                                                    <div class="panel-body">
                                                                                        <div class="row">
                                                                                            <div class="form-group col-lg-12">
                                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Habilitar</label>
                                                                                                @Html.DropDownListFor(model => model.rIOxR_io_cHb, new SelectList(ViewBag.lista_SimNao, "ID", "Descricao", Model.rIOxR_io_cHb), new { @class = "form-control", @disabled = "disabled" })
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="row">
                                                                                            <div class="form-group col-lg-12">
                                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Porta</label>
                                                                                                @Html.TextBoxFor(model => model.rIOxR_io_cPo, new { @class = "form-control", @disabled = "disabled" })
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-lg-3">
                                                                        <br />
                                                                        <div class="row">
                                                                            <div class="col-lg-12">
                                                                                <div class="panel panel-default" style="margin-top: 5px;">
                                                                                    <div class="panel-heading">
                                                                                        <h4>485 Q</h4>
                                                                                    </div>
                                                                                    <div class="panel-body">
                                                                                        <div class="row">
                                                                                            <div class="form-group col-lg-12">
                                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Velocidade</label>
                                                                                                @Html.DropDownListFor(model => model.serR_iBps, new SelectList(ViewBag.lista_Velocidade, "ID", "Descricao", Model.serR_iBps), new { @class = "form-control", @disabled = "disabled" })
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                        <div id="tab-3" class="tab-pane">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="form-group col-lg-12">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Historico (dias)</label>
                                                        @Html.TextBoxFor(model => model.xH2_qDia, new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                </div>

                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.MedicoesEnergia</label>
                                                        @Html.DropDownListFor(model => model.xH2_qEN, new SelectList(ViewBag.lista_QuantMed, "ID", "Descricao", Model.xH2_qEN), new { @class = "form-control", @onchange = "SelecionouHistorico()" })
                                                    </div>
                                                </div>

                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.MedicoesUtilidades</label>
                                                        @Html.DropDownListFor(model => model.xH2_qUT, new SelectList(ViewBag.lista_QuantMed, "ID", "Descricao", Model.xH2_qUT), new { @class = "form-control", @onchange = "SelecionouHistorico()" })
                                                    </div>
                                                </div>

                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.MedicoesAnalogicas</label>
                                                        @Html.DropDownListFor(model => model.xH2_qAN, new SelectList(ViewBag.lista_QuantMed, "ID", "Descricao", Model.xH2_qAN), new { @class = "form-control", @onchange = "SelecionouHistorico()" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Registros</label>
                                                        @Html.DropDownListFor(model => model.xH2_cAN_iTGG, new SelectList(ViewBag.lista_Registros, "ID", "Descricao", Model.xH2_cAN_iTGG), new { @class = "form-control", @onchange = "SelecionouHistorico()" })
                                                    </div>
                                                </div>

                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.MedicoesCiclometro</label>
                                                        @Html.DropDownListFor(model => model.xH2_qCY, new SelectList(ViewBag.lista_QuantMed, "ID", "Descricao", Model.xH2_qCY), new { @class = "form-control", @onchange = "SelecionouHistorico()" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Registros</label>
                                                        @Html.DropDownListFor(model => model.xH2_cCY_iTGG, new SelectList(ViewBag.lista_Registros, "ID", "Descricao", Model.xH2_cCY_iTGG), new { @class = "form-control", @onchange = "SelecionouHistorico()" })
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div id="tab-4" class="tab-pane">
                                            <div class="panel-body">

                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <div class="panel panel-default">
                                                            <div class="panel-heading">
                                                                <h4>DCE</h4>
                                                            </div>
                                                            <div class="panel-body">
                                                                <div class="row">
                                                                    <div class="form-group col-lg-6">
                                                                        <label class="control-label">&nbsp;DCE</label>
                                                                        @Html.DropDownListFor(model => model.xWanIs, new SelectList(ViewBag.lista_WanIs, "ID", "Descricao", Model.xWanIs), new { @class = "form-control", @onchange = "SelecionouDCE()" })
                                                                    </div>
                                                                    <div class="form-group col-lg-6">
                                                                        <label class="control-label">&nbsp;Modem</label>
                                                                        @Html.DropDownListFor(model => model.xWanEq, new SelectList(ViewBag.lista_WanEq, "ID", "Descricao", Model.xWanEq), new { @class = "form-control", @disabled = "disabled" })
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="div_DCE">
                                                    <div class="row">
                                                        <div class="col-lg-12">
                                                            <div class="panel panel-default">
                                                                <div class="panel-heading">
                                                                    <h4>Upload</h4>
                                                                </div>
                                                                <div class="panel-body">
                                                                    <div class="row">
                                                                        <div class="col-lg-6">
                                                                            <div class="row">
                                                                                <div class="form-group col-lg-12">
                                                                                    <label class="control-label">&nbsp;Gateway</label>
                                                                                    @Html.TextBoxFor(model => model.xUpldId, new { @class = "form-control", @disabled = "disabled" })
                                                                                </div>
                                                                            </div>
                                                                            <div class="row">
                                                                                <div class="form-group col-lg-12">
                                                                                    <label class="control-label">&nbsp;1º Upload</label>
                                                                                    @Html.DropDownListFor(model => model.xUpld1st_indice, new SelectList(ViewBag.lista_Up1st, "ID", "Descricao", Model.xUpld1st_indice), new { @class = "form-control" })
                                                                                </div>
                                                                            </div>
                                                                            <div class="row">
                                                                                <div class="form-group col-lg-12">
                                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.UploadCada</label>
                                                                                    @Html.DropDownListFor(model => model.xUpldFrq_indice, new SelectList(ViewBag.lista_UpFrq, "ID", "Descricao", Model.xUpldFrq_indice), new { @class = "form-control" })
                                                                                </div>
                                                                            </div>
                                                                            <div class="row">
                                                                                <div class="form-group col-lg-12">
                                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Protocolo</label>
                                                                                    @Html.DropDownListFor(model => model.xUpldProt, new SelectList(ViewBag.lista_ProtocoloUpload, "ID", "Descricao", Model.xUpldProt), new { @class = "form-control", @onchange = "SelecionouProtocolo()" })
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-lg-6">
                                                                            <div class="row">
                                                                                <div class="form-group col-lg-12">
                                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Senha Upload</label>
                                                                                    <input type="password" class="form-control" value="@Model.xUpldPass" disabled>
                                                                                </div>
                                                                            </div>
                                                                            <div class="row">
                                                                                <div class="form-group col-lg-12">
                                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.UploadAlarme</label>
                                                                                    @Html.DropDownListFor(model => model.xUpldOnAlm, new SelectList(ViewBag.lista_UpldOnAlm, "ID", "Descricao", Model.xUpldOnAlm), new { @class = "form-control" })
                                                                                </div>
                                                                            </div>
                                                                            <div class="div_Upload_Tentativas">
                                                                                <div class="row">
                                                                                    <div class="form-group col-lg-12">
                                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Tentativas </label>
                                                                                        @Html.TextBoxFor(model => model.xUpldQt2Try, new { @class = "form-control" })
                                                                                    </div>
                                                                                </div>
                                                                                <div class="row">
                                                                                    <div class="form-group col-lg-12">
                                                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Retentativa </label>
                                                                                        @Html.TextBoxFor(model => model.xUpldT2Retry, new { @class = "form-control" })
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="row">
                                                        <div class="div_FTP_Login">
                                                            <div class="col-lg-6">
                                                                <div class="panel panel-default">
                                                                    <div class="panel-heading">
                                                                        <h4>FTP - Login</h4>
                                                                    </div>
                                                                    <div class="panel-body">
                                                                        <div class="row">
                                                                            <div class="form-group col-lg-12">
                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Dominio</label>
                                                                                @Html.TextBoxFor(model => model.xFtpDns, new { @class = "form-control" })
                                                                            </div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="form-group col-lg-12">
                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.PastaRemota</label>
                                                                                @Html.TextBoxFor(model => model.xFtpDir, new { @class = "form-control" })
                                                                            </div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="form-group col-lg-8">
                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Usuario</label>
                                                                                @Html.TextBoxFor(model => model.xFtpUser, new { @class = "form-control", @disabled = "disabled" })
                                                                            </div>
                                                                            <div class="form-group col-lg-4">
                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Porta</label>
                                                                                @Html.TextBoxFor(model => model.xFtpPort, new { @class = "form-control", @disabled = "disabled" })
                                                                            </div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="form-group col-lg-8">
                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Senha</label>
                                                                                <input type="password" class="form-control" value="@Model.xFtpPass" disabled>
                                                                            </div>
                                                                            <div class="form-group col-lg-4">
                                                                                <label class="control-label" style="margin-bottom: 10px;">&nbsp;</label>
                                                                                <div class="i-checks" style="margin-bottom: 6px;">
                                                                                    @Html.CheckBoxFor(model => model.xFtpPasv)&nbsp;&nbsp;<span style="margin-right: 54px;">@SmartEnergy.Resources.ConfiguracaoTexts.ModoPassivo</span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="div_SIMCard_Login" style="display: none">
                                                            <div class="col-lg-6">
                                                                <div class="panel panel-default">
                                                                    <div class="panel-heading">
                                                                        <h4>SIM Card - Login</h4>
                                                                    </div>
                                                                    <div class="panel-body">
                                                                        <div class="row">
                                                                            <div class="form-group col-lg-12">
                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Conexao</label>
                                                                                @Html.TextBoxFor(model => model.xIspFone, new { @class = "form-control" })
                                                                            </div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="form-group col-lg-12">
                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Usuario</label>
                                                                                @Html.TextBoxFor(model => model.xIspUser, new { @class = "form-control" })
                                                                            </div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="form-group col-lg-12">
                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Senha</label>
                                                                                <input type="password" class="form-control" value="@Model.xUpldPass" disabled>
                                                                            </div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="form-group col-lg-6">
                                                                                <label class="control-label">&nbsp;APN</label>
                                                                                @Html.TextBoxFor(model => model.xIspAPN, new { @class = "form-control" })
                                                                            </div>
                                                                            <div class="form-group col-lg-6">
                                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Autenticacao</label>
                                                                                @Html.DropDownListFor(model => model.xIspAuth, new SelectList(ViewBag.lista_IspAuth, "ID", "Descricao", Model.xIspAuth), new { @class = "form-control", @disabled = "disabled" })
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="row">
                                                        <div class="col-lg-12">
                                                            <div class="panel panel-default">
                                                                <div class="panel-heading">
                                                                    <h4>@SmartEnergy.Resources.ConfiguracaoTexts.Medicao - Upload</h4>
                                                                </div>
                                                                <div class="panel-body">
                                                                    <div class="row">
                                                                        <div class="form-group col-lg-12">
                                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.MedicoesEnergia</label>
                                                                            @Html.TextBoxFor(model => model.xUpldLstEN, new { @class = "form-control" })
                                                                        </div>
                                                                    </div>
                                                                    <div class="row">
                                                                        <div class="form-group col-lg-12">
                                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.MedicoesUtilidades</label>
                                                                            @Html.TextBoxFor(model => model.xUpldLstUT, new { @class = "form-control" })
                                                                        </div>
                                                                    </div>
                                                                    <div class="row">
                                                                        <div class="form-group col-lg-12">
                                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.MedicoesAnalogicas</label>
                                                                            @Html.TextBoxFor(model => model.xUpldLstAN, new { @class = "form-control" })
                                                                        </div>
                                                                    </div>
                                                                    <div class="row">
                                                                        <div class="form-group col-lg-12">
                                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.MedicoesCiclometro</label>
                                                                            @Html.TextBoxFor(model => model.xUpldLstCY, new { @class = "form-control" })
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                    </div>
                                </div>

                            </div>
                        </div>

                        <div class="overlay_enviando" style="display: none">
                            <div class="fa fa-upload icone_enviando">
                            </div>
                            <div class="text_enviando some_desktop">
                                <span>@SmartEnergy.Resources.ConfiguracaoTexts.EnviaConfig</span>
                            </div>
                            <div class="spinner-enviando">
                                <div class="sk-spinner sk-spinner-wandering-cubes">
                                    <div class="sk-spinner sk-spinner-wave">
                                        <div class="sk-rect1 text-ligh"></div>
                                        <div class="sk-rect2"></div>
                                        <div class="sk-rect3"></div>
                                        <div class="sk-rect4"></div>
                                        <div class="sk-rect5"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            }

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/iCheck")
    @Scripts.Render("~/declaracoes/Constants")
    @Scripts.Render("~/declaracoes/Validates")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        $(document).ready(function () {

            // classe iChecks
            $('.i-checks').iCheck({
                checkboxClass: 'icheckbox_square-green',
                radioClass: 'iradio_square-green',
            });

            //
            // VALIDACAO DOS CAMPOS
            //
            Validator_AddMethods();

            $("#form").validate({
                rules: {

                },
                highlight: function (element) {
                    $(element).closest('.control-group').removeClass('success').addClass('error');
                },
                unhighlight: function (element) {
                    $(element).closest('.control-group').addClass('valid success').removeClass('error');
                }
            });

            //// VERIFICA CAMPOS QUE DEVEM OU NAO ESTAR HABILITADOS \\\\
            SelecionouDCE();

            // desabilita campos
            disableAll();

            // retornos da solicitacao da configuracao
            var solicitaProg = (@ViewBag.SolicitaProg.ToString().ToLower());

            // verifico se retornou erro
            if  (solicitaProg == false)
            {
                $("#BotaoEnviar").attr('disabled', true);

                swal({
                    title: "Erro na recepção da configuração!",
                    type: "warning",
                    confirmButtonColor: "#1ab394",
                    confirmButtonText: "Fechar",
                    closeOnConfirm: true
                }, function(isConfirm){

                    var IDGateway = parseInt(document.getElementById("IDGateway").value);

                    // retorna para a pagina editar
                    var url = '/Configuracao/Gateway_Editar?IDGateway=' + IDGateway + '#tab-6';
                    window.location.href = url;
                });
            }

            $('form input').on('keypress', function (e) {
                return e.which !== 13;
            });

        });

        function disableAll() {

            var permissao = Math.ceil(@ViewBag.Permissao);

            // verifica permissao
            switch (permissao) {
                case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
                case PERMISSOES_GERENTE:            // 1 - permissao de Gerente: ve tudo e escreve parte
                case PERMISSOES_OPERADOR:           // 2 - permissao de Operador: ve tudo e nao pode escrever
                case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
                case PERMISSOES_CONSULTOR:          // 5 - permissao de Consultor
                case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte 

                    $("#BotaoEnviar").attr('disabled', false);
                    break;

                default:
                case PERMISSOES_PRODUCAO:           // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                    $("#BotaoEnviar").attr('disabled', true);
                    break;
            }
        }

        function SelecionouHistorico() {

            // pega quantidade medições
            var xH2_qEN = parseInt($("#xH2_qEN").val()) * 4;
            var xH2_qUT = parseInt($("#xH2_qUT").val()) * 4;
            var xH2_qAN = parseInt($("#xH2_qAN").val()) * 4;
            var xH2_qCY = parseInt($("#xH2_qCY").val()) * 4;

            // pega quantidade de registros
            var xH2_cAN_iTGG = parseInt($("#xH2_cAN_iTGG").val());
            var xH2_cCY_iTGG = parseInt($("#xH2_cCY_iTGG").val());

            // calcula quantidade de blocos por dia por tipo de medição
            var bkDiaEE = xH2_qEN;                                      // qt bk 1 dia da EE
            var bkDiaUT = xH2_qUT / 4;                                  // qt bk 1 dia da UT
            var bkDiaAN = (15 * xH2_qAN) / get_minTGG(xH2_cAN_iTGG);    // qt bk 1 dia da AN
            var bkDiaCY = (15 * xH2_qCY) / get_minTGG(xH2_cCY_iTGG);    // qt bk 1 dia da CY

            // calcula quantidade de bytes por dia por tipo de medição
            var byDiaEE = (bkDiaEE * 3 * 96 * 2) + 4;     // qt byte 1 dia da EE [bkDiaEE * 3 * 96 * sizeof(INT16) + sizeof(DATA);]
            var byDiaUT = (bkDiaUT * 1 * 96 * 2) + 12;    // qt byte 1 dia da UT [bkDiaUT * 1 * 96 * sizeof(INT16) + sizeof(GG_RGTS)]
            var byDiaAN = (bkDiaAN * 3 * 96 * 2) + 12;    // qt byte 1 dia da AN [bkDiaAN * 3 * 96 * sizeof(INT16) + sizeof(GG_RGTS)]
            var byDiaCY = (bkDiaCY * 3 * 96 * 2) + 12;    // qt byte 1 dia da CY [bkDiaCY * 3 * 96 * sizeof(INT16) + sizeof(GG_RGTS)]

            // tem histórico?
            var temHis = bkDiaEE + bkDiaUT + bkDiaAN + bkDiaCY;

            // quantidade de bytes por dia todas as medições
            var byDia = byDiaEE + byDiaUT + byDiaAN + byDiaCY;

            // tamanho da memória total [KB_HEEGG * UM_KB * 2]
            var byMem = 320 * 1024 * 2;                     

            // calcula número de dias de histórico
            var qDia = temHis ? (byMem / byDia): 200; 
            
            // limita
            if (qDia > 200)
            {
                qDia = 200;
            }

            document.getElementById("xH2_qDia").value = Math.floor(qDia);
        }

        function get_minTGG(iTGG)
        {
        	switch(iTGG)
            {
        	    case 1: 
        	        return 1;

        	    case 2: 
        	        return 5;

        	    case 3: 
        	        return 15;
        	}

            return 60;
        }


        function SelecionouDCE() {

            // pega xWanIs selecionada
            var xWanIs = parseInt($("#xWanIs").val());

            switch (xWanIs)
            {
                default:
                case 0:     // nenhum
                    $('.div_DCE').css("display", "none");
                    break;

                case 2:     // ethernet
                    $('.div_DCE').css("display", "block");
                    $('.div_SIMCard_Login').css("display", "none");
                    $("#xWanEq").attr('disabled', true);
                    break;

                case 6:     // RS232 <--> modemGSM

                    $('.div_DCE').css("display", "block");
                    $('.div_SIMCard_Login').css("display", "block");
                    $("#xWanEq").attr('disabled', false);
                    break;
            }

            // protocolo
            SelecionouProtocolo();
        }

        function SelecionouProtocolo() {

            // pega xWanIs selecionada
            var xWanIs = parseInt($("#xWanIs").val());

            // pega xUpldProt selecionado
            var xUpldProt = parseInt($("#xUpldProt").val());

            switch (xWanIs)
            {
                default:
                case 0:     // nenhum
                    break;

                case 2:     // ethernet
                case 6:     // RS232 <--> modemGSM

                    if (xUpldProt == 0)
                    {
                        $('.div_FTP_Login').css("display", "block");
                        $('.div_Upload_Tentativas').css("display", "block");
                    }
                    else
                    {
                        $('.div_FTP_Login').css("display", "none");
                        $('.div_Upload_Tentativas').css("display", "none");
                    }
                    break;
            }
        }


        function Enviar(IDGateway) {

            var $form = $('#form');

            var IsTabValid = $("#form").valid();;

            // verifica se entradas sao validas
            if (!IsTabValid) return false;

            swal({
                title: "Deseja CANCELAR o envio da configuração?",
                text: "APÓS 15 SEGUNDOS DO ENVIO, EXECUTARÁ AUTO-RESET.",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#d0d0d0",
                confirmButtonText: "Não",
                cancelButtonText: "Sim",
                closeOnConfirm: true
            }, function () {
                event.stopPropagation();

                // aguarde
                $('.overlay_enviando').toggle();

                // retiro campos desabilitados, pois nao envia via POST
                $(":disabled", $('#form')).removeAttr("disabled");

                $.ajax({
                    url: '/Configuracao/GateX_Drivers_Enviar',
                    data: $form.serialize(),
                    type: 'POST',
                    success: function (data) {

                        // fim
                        $('.overlay_enviando').toggle();

                        setTimeout(function () {

                            // verifica se erro
                            if (data.status == "ERRO") {
                                swal({
                                    title: "Erro",
                                    text: data.erro,
                                    type: "warning",
                                    confirmButtonColor: "#f8ac59",
                                    confirmButtonText: "Fechar",
                                }, function () {

                                    // retorna para programacoes
                                    var url = '/Configuracao/GateX_Drivers?IDGateway=' + IDGateway;

                                    window.location.href = url;
                                });
                            }
                            else {
                                swal({
                                    title: "Enviado com sucesso",
                                    type: "success",
                                    confirmButtonColor: "#18a689",
                                    confirmButtonText: "Fechar",
                                }, function () {

                                    // retorna para pagina de edicao da gateway
                                    var url = '/Configuracao/Gateway_Editar?IDGateway=' + IDGateway + '#tab-6';

                                    window.location.href = url;

                                });
                            }
                            
                        }, 100);

                    },
                    error: function (xhr, status, error) {

                        // fim
                        $('.overlay_enviando').toggle();

                        setTimeout(function () {

                            swal({
                                title: "Erro",
                                text: "Falha no envio da configuração!",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // retorna para programacoes
                                var url = '/Configuracao/GateX_Drivers?IDGateway=' + IDGateway;

                                window.location.href = url;
                            });
                        }, 100);
                    },
                });
            });
        };

    </script>
}
