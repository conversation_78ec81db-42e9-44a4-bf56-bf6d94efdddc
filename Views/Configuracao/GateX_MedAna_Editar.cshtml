﻿@model SmartEnergyLib.SQL.GateX_MedAna_Dominio

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.ConfiguracaoTexts.MedicaoAnalogica;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">

                    @using (Html.BeginForm("GateX_MedAna_Editar", "Configuracao", FormMethod.Post, new { id = "form", role = "form" }))
                    {
                        @Html.Hidden("Programado", Model.Programado)
                        @Html.Hidden("Programacao", Model.Programacao)

                        @Html.Hidden("RecebeHist", Model.RecebeHist)
                        @Html.Hidden("v1_HabFuncaoLN", Model.v1_HabFuncaoLN)
                        @Html.Hidden("v1_FuncaoLN", Model.v1_FuncaoLN)

                        @Html.Hidden("v1_Remota", Model.v1_Remota)
                        @Html.Hidden("v2_Remota", Model.v2_Remota)
                        

                        <div class="row">
                            <div class="col-lg-12">
                                <div class="panel panel-success">
                                    <div class="panel-heading">

                                        @{
                                            GatewaysDominio gateway = ViewBag.Gateway;
                                            @Html.Hidden("IDGateway", gateway.IDGateway);
                                        }

                                        <h4>Gateway</h4>
                                        <br />
                                        @gateway.Nome

                                        @{
                                            int IDTipoAcesso = ViewBag._IDTipoAcesso;
                                            if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                            {
                                                <span> [@gateway.IDGateway]</span>
                                            }
                                        }

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="panel panel-title">
                            <div class="panel-heading">
                                <div style="text-align:center;">
                                    <h4>@SmartEnergy.Resources.ConfiguracaoTexts.MedicaoAnalogica</h4>
                                </div>

                                <div class="pull-left relat-navega">
                                    <h4>
                                        @{
                                            if (Model.NumMedicaoGateway != 0)
                                            {
                                                <a class="link_primeira">
                                                    <i class="fa fa-angle-double-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Primeira></i>
                                                </a>
                                                <a class="link_prog_menos">
                                                    <i class="fa fa-angle-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Anterior></i>
                                                </a>
                                            }

                                            if (Model.NumMedicaoGateway != 35)
                                            {
                                                <a class="link_prog_mais">
                                                    <i class="fa fa-angle-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Proxima></i>
                                                </a>
                                                <a class="link_ultima">
                                                    <i class="fa fa-angle-double-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Ultima></i>
                                                </a>
                                            }
                                        }
                                    </h4>
                                </div>
                            </div>

                            <div class="panel-body">

                                <div class="row">
                                    <div class="form-group col-lg-2">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.MedicaoAnalogica</label>
                                        @Html.TextBoxFor(model => model.NumMedicaoGateway, new { @class = "form-control", @disabled = "disabled" })
                                    </div>
                                    <div class="col-lg-offset-4 col-lg-6">
                                        <div class="ibox-tools">
                                            <button type="button" class="btn btn-primary" onclick="Programar();" disabled="" id="BotaoProgramar">@SmartEnergy.Resources.ComumTexts.BotaoProgramar</button>
                                            <a href='@("/Configuracao/GateX_MedAna?IDGateway=" + @Model.IDGateway + "&Origem=2")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                        </div>
                                    </div>
                                </div>

                                <div class="tabs-container">
                                    <ul class="nav nav-tabs">
                                        <li class="active">
                                            <a data-toggle="tab" href="#tab-1"><i class="fa fa-cogs"></i>@SmartEnergy.Resources.ConfiguracaoTexts.Geral</a>
                                        </li>
                                    </ul>
                                    <div class="tab-content">

                                        <div id="tab-1" class="tab-pane active">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Variavel</label>
                                                        @Html.DropDownListFor(model => model.TipoVariavel, new SelectList(ViewBag.listatipoVariavel, "ID", "Descricao", Model.TipoVariavel), new { @class = "form-control", @onchange = "SelecionouVariavel()" })
                                                    </div>

                                                    <div class="div_programado1">
                                                        <div class="form-group col-lg-5">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Descricao</label>
                                                            @Html.TextBoxFor(model => model.Descricao, new { @class = "form-control", @maxlength = "29" })
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Unidade</label>
                                                            @Html.TextBoxFor(model => model.Unidade, new { @class = "form-control", @maxlength = "9" })
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="div_programado2">
                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.MediaMovel (segundos)</label>
                                                            @Html.TextBoxFor(model => model.TempoAmostraMediaMovel, new { @class = "form-control" })
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="div_V1">
                                                    <hr />
                                                    <div class="row">
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Rede</label>
                                                            @Html.DropDownListFor(model => model.v1_RedeIO, new SelectList(ViewBag.listaTipoRedeIO, "ID", "Descricao", Model.v1_RedeIO), new { @class = "form-control", @onchange = "SelecionouRedeIO_V1()" })
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <div class="div_v1_rede_r_q">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Remota</label>
                                                                @Html.DropDownListFor(model => model.v1_Remota, new SelectList(ViewBag.ListaRemotas), string.Format("Nenhum"), new { id = "v1_remotas_r_q", @onchange = "SelecionouNumRemota_V1()", @class = "form-control", @disabled = "disabled" })
                                                            </div>
                                                            <div class="div_v1_rede_k" style="display: none">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Remota</label>
                                                                @Html.DropDownListFor(model => model.v1_Remota, new SelectList(ViewBag.listaTipoNumRemotaRedeK, "ID", "Descricao", Model.v1_Remota), new { id = "v1_remotas_k", @onchange = "SelecionouNumRemota_V1()", @class = "form-control", @disabled = "disabled" })
                                                            </div>
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <div class="div_v1_drv_ad">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Endereco</label>
                                                                @Html.TextBoxFor(model => model.v1_RemotaEnd, new { @class = "form-control", @disabled = "disabled" })
                                                            </div>
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Formato</label>
                                                            @Html.DropDownListFor(model => model.v1_RemotaVar, new SelectList(ViewBag.listatipoVariavelFormato, "ID", "Descricao", Model.v1_RemotaVar), new { @class = "form-control" })
                                                        </div>
                                                    </div>

                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Zerar (segundos)</label>
                                                            @Html.TextBoxFor(model => model.v1_TempoZerarRaw, new { @class = "form-control" })
                                                        </div>
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.RemotaNaoOK</label>
                                                            @Html.DropDownListFor(model => model.v1_UpScale, new SelectList(ViewBag.listatipoEscala, "ID", "Descricao", Model.v1_UpScale), new { @class = "form-control" })
                                                        </div>
                                                    </div>

                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.EAMinima</label>
                                                            @Html.TextBoxFor(model => model.v1_EntAnalogicaMin, new { @class = "form-control" })
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.EAMaxima</label>
                                                            @Html.TextBoxFor(model => model.v1_EntAnalogicaMax, new { @class = "form-control" })
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.UnidadeEngenhariaMinima</label>
                                                            @Html.TextBoxFor(model => model.v1_UnidadeEngMin, new { @class = "form-control" })
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.UnidadeEngenhariaMaxima</label>
                                                            @Html.TextBoxFor(model => model.v1_UnidadeEngMax, new { @class = "form-control" })
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="div_V2">
                                                    <hr />
                                                    <div class="row">
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Rede</label>
                                                            @Html.DropDownListFor(model => model.v2_RedeIO, new SelectList(ViewBag.listaTipoRedeIO, "ID", "Descricao", Model.v2_RedeIO), new { @class = "form-control", @onchange = "SelecionouRedeIO_V2()" })
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <div class="div_v2_rede_r_q">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Remota</label>
                                                                @Html.DropDownListFor(model => model.v2_Remota, new SelectList(ViewBag.ListaRemotas), string.Format("Nenhum"), new { id = "v2_remotas_r_q", @onchange = "SelecionouNumRemota_V2()", @class = "form-control", @disabled = "disabled" })
                                                            </div>
                                                            <div class="div_v2_rede_k" style="display: none">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Remota</label>
                                                                @Html.DropDownListFor(model => model.v2_Remota, new SelectList(ViewBag.listaTipoNumRemotaRedeK, "ID", "Descricao", Model.v2_Remota), new { id = "v2_remotas_k", @onchange = "SelecionouNumRemota_V2()", @class = "form-control", @disabled = "disabled" })
                                                            </div>
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <div class="div_v2_drv_ad">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Endereco</label>
                                                                @Html.TextBoxFor(model => model.v2_RemotaEnd, new { @class = "form-control", @disabled = "disabled" })
                                                            </div>
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Formato</label>
                                                            @Html.DropDownListFor(model => model.v2_RemotaVar, new SelectList(ViewBag.listatipoVariavelFormato, "ID", "Descricao", Model.v2_RemotaVar), new { @class = "form-control" })
                                                        </div>
                                                    </div>

                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Zerar (segundos)</label>
                                                            @Html.TextBoxFor(model => model.v2_TempoZerarRaw, new { @class = "form-control" })
                                                        </div>
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ConstanteMultiplicador</label>
                                                            @Html.TextBoxFor(model => model.v2_ConstanteMult, new { @class = "form-control" })
                                                        </div>
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ConstanteSoma</label>
                                                            @Html.TextBoxFor(model => model.v2_ConstanteSoma, new { @class = "form-control" })
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="div_V3">
                                                    <hr />
                                                    <div class="row">
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.MedicaoEnergia</label>
                                                            @Html.DropDownListFor(model => model.v4_MedicaoEE, new SelectList(ViewBag.listaMedEner, "NumMedicaoGateway", "DescricaoNum", Model.v4_MedicaoEE), new { @class = "form-control" })
                                                        </div>
                                                    </div>

                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Variavel</label>
                                                            @Html.DropDownListFor(model => model.v4_Offset, new SelectList(ViewBag.listatipoVariavelGrandEletrica, "ID", "Descricao", Model.v4_Offset), new { @class = "form-control" })
                                                        </div>
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ValorMinimo</label>
                                                            @Html.TextBoxFor(model => model.v4_UnidadeEngMin, new { @class = "form-control" })
                                                        </div>
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ValorMaximo</label>
                                                            @Html.TextBoxFor(model => model.v4_UnidadeEngMax, new { @class = "form-control" })
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>

                <div class="overlay_aguarde" style="display: none">
                    <div class="spinner-aguarde">
                        <div class="sk-spinner sk-spinner-wandering-cubes">
                            <div class="sk-spinner sk-spinner-wave">
                                <div class="sk-rect1 text-ligh"></div>
                                <div class="sk-rect2"></div>
                                <div class="sk-rect3"></div>
                                <div class="sk-rect4"></div>
                                <div class="sk-rect5"></div>
                            </div>
                            <div class="sk-rect5"></div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/declaracoes/Constants")
    @Scripts.Render("~/declaracoes/Validates")



    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        $(document).ready(function () {

            //
            // VALIDACAO DOS CAMPOS
            //
            Validator_AddMethods();

            $("#form").validate({
                rules: {
                    Descricao: {
                        required: true,
                        alphanumeric: true,
                        descricao: true
                    },
                    Unidade: {
                        required: true
                    },
                    TempoAmostraMediaMovel: {
                        numeric: true,
                        multiplo: true,
                        min: 0
                    },
                    v1_RemotaEnd: {
                        numeric: true,
                        zero: true,
                        max: 65535
                    },
                    v1_TempoZerarRaw: {
                        numeric: true,
                        multiplo: true,
                        min: 0,
                        max: 5000
                    },
                    v1_EntAnalogicaMin: {
                        numeric: true
                    },
                    v1_EntAnalogicaMax: {
                        numeric: true,
                        MaxBigMin: '#v1_EntAnalogicaMin'
                    },
                    v1_UnidadeEngMin: {
                        numeric: true
                    },
                    v1_UnidadeEngMax: {
                        numeric: true,
                        MaxBigMin: '#v1_UnidadeEngMin'
                    },
                    v2_RemotaEnd: {
                        numeric: true,
                        zero: true,
                        max: 65535
                    },
                    v2_TempoZerarRaw: {
                        numeric: true,
                        multiplo: true,
                        min: 0,
                        max: 5000
                    },
                    v2_ConstanteMult: {
                        numeric: true,
                        zero: true
                    },
                    v4_UnidadeEngMax: {
                        MaxBigMin: '#v4_UnidadeEngMin'
                    },
                },
                highlight: function (element) {
                    $(element).closest('.control-group').removeClass('success').addClass('error');
                },
                unhighlight: function (element) {
                    $(element).closest('.control-group').addClass('valid success').removeClass('error');
                },
            });

            //// VERIFICA CAMPOS QUE DEVEM OU NAO ESTAR HABILITADOS \\\\
            SelecionouVariavel();

            // desabilita campos por permissao de usuario
            disableAll();

            $('form input').on('keypress', function (e) {
                return e.which !== 13;
            });
        });

        function disableAll() {

            var permissao = Math.ceil(@ViewBag.Permissao);

            // verifica permissao
            switch (permissao) {
                case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
                case PERMISSOES_GERENTE:            // 1 - permissao de Gerente: ve tudo e escreve parte
                case PERMISSOES_OPERADOR:           // 2 - permissao de Operador: ve tudo e nao pode escrever
                case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
                case PERMISSOES_CONSULTOR:          // 5 - permissao de Consultor
                case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte

                    $("#BotaoProgramar").attr('disabled', false);
                    $("#TipoVariavel").attr('disabled', false);

                    break;

                default:
                case PERMISSOES_PRODUCAO:           // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                    $("#BotaoProgramar").attr('disabled', true);
                    $("#TipoVariavel").attr('disabled', true);

                    break;
            }
        }

        function SelecionouVariavel() {

            // pega variavel selecionada
            var variavel = parseInt($("#TipoVariavel").val());

            switch (variavel) {
                default:
                case 0:     // desprogramado
                    $('.div_programado1').css("display", "none");
                    $('.div_programado2').css("display", "none");
                    $('.div_V1').css("display", "none");
                    $('.div_V2').css("display", "none");
                    $('.div_V3').css("display", "none");
                    break;

                case 1:     // REM_EA (v1)
                    $('.div_programado1').css("display", "block");
                    $('.div_programado2').css("display", "block");
                    $('.div_V1').css("display", "block");
                    $('.div_V2').css("display", "none");
                    $('.div_V3').css("display", "none");

                    SelecionouRedeIO_V1();
                    break;

                case 2:     // REM_PULSO (v2)
                    $('.div_programado1').css("display", "block");
                    $('.div_programado2').css("display", "block");
                    $('.div_V1').css("display", "none");
                    $('.div_V2').css("display", "block");
                    $('.div_V3').css("display", "none");

                    SelecionouRedeIO_V2();
                    break;

                case 4:     // MedEE: Grandezas Elétricas (v4)
                    $('.div_programado1').css("display", "block");
                    $('.div_programado2').css("display", "block");
                    $('.div_V1').css("display", "none");
                    $('.div_V2').css("display", "none");
                    $('.div_V3').css("display", "block");
                    break;
            }
        }


        function SelecionouRedeIO_V1() {

            // pega redeIO selecionada para REM_EA
            var RedeIO = parseInt($("#v1_RedeIO").val());

            // executo funcao para verificar se endereco do driver deve ser habilitado ou nao
            SelecionouNumRemota_V1();

            // apresenta selecao de remotas conforme redeIO
            ShowNumRemota_V1(RedeIO);
        }

        function ShowNumRemota_V1(RedeIO) {

            switch (RedeIO) {

                case 0: // rede R
                case 1: // rede Q

                    // habilita rede R e Q
                    $('.div_v1_rede_r_q').css("display", "block");
                    $("#v1_remotas_r_q").attr('disabled', false);

                    // Desabilita rede K
                    $('.div_v1_rede_k').css("display", "none");
                    $("#v1_remotas_k").attr('disabled', true);

                    // habilita endereço
                    $('.div_v1_drv_ad').css("display", "block");
                    $("#v1_RemotaEnd").attr('disabled', false);
                    break;

                case 2: // K

                    // habilita rede K
                    $('.div_v1_rede_k').css("display", "block");
                    $("#v1_remotas_k").attr('disabled', false);

                    // desabilita rede R e Q
                    $('.div_v1_rede_r_q').css("display", "none");
                    $("#v1_remotas_r_q").attr('disabled', true);

                    // habilita endereço
                    $('.div_v1_drv_ad').css("display", "block");
                    $("#v1_RemotaEnd").attr('disabled', false);
                    break;

                case 4: // DI

                    // desabilita rede K
                    $('.div_v1_rede_k').css("display", "none");
                    $("#v1_remotas_k").attr('disabled', true);

                    // desabilita rede R e Q
                    $('.div_v1_rede_r_q').css("display", "none");
                    $("#v1_remotas_r_q").attr('disabled', true);

                    // desabilita endereço
                    $('.div_v1_drv_ad').css("display", "none");
                    $("#v1_RemotaEnd").attr('disabled', true);
                    break;
            }
        }

        function SelecionouNumRemota_V1() {

            // pega redeIO selecionada
            var RedeIO = parseInt($("#v1_RedeIO").val());

            // pega numero remota
            var NumRemota = 255;

            switch (RedeIO) {
                case 0:  // Rede R
                case 1:  // Rede Q
                    NumRemota = parseInt(document.getElementById("v1_remotas_r_q").value);
                    break;

                case 2:  // Rede K
                    NumRemota = parseInt(document.getElementById("v1_remotas_k").value);
                    break;

                case 4:  // DI
                    NumRemota = 255;
                    break;
            }

            // habilita ou desabilita campo de endereco do driver
            ShowEnderecoDrv_V1(NumRemota);
        }

        function ShowEnderecoDrv_V1(NumRemota) {

            // caso numero de remota nao selecionado desabilita campo de endereco de driver
            if (NumRemota < 0 || NumRemota > 31 || Number.isNaN(NumRemota)) {
                // caso nao escolheu remota deixo input em branco
                document.getElementById("v1_RemotaEnd").value = "";
                $("#v1_RemotaEnd").attr('disabled', true);
            }
            else {
                $("#v1_RemotaEnd").attr('disabled', false);
            }
        }


        function SelecionouRedeIO_V2() {

            // pega redeIO selecionada para REM_EA
            var RedeIO = parseInt($("#v2_RedeIO").val());

            // executo funcao para verificar se endereco do driver deve ser habilitado ou nao
            SelecionouNumRemota_V2();

            // apresenta selecao de remotas conforme redeIO
            ShowNumRemota_V2(RedeIO);
        }

        function ShowNumRemota_V2(RedeIO) {

            switch (RedeIO) {

                case 0: // rede R
                case 1: // rede Q

                    // habilita rede R e Q
                    $('.div_v2_rede_r_q').css("display", "block");
                    $("#v2_remotas_r_q").attr('disabled', false);

                    // Desabilita rede K
                    $('.div_v2_rede_k').css("display", "none");
                    $("#v2_remotas_k").attr('disabled', true);

                    // habilita endereço
                    $('.div_v2_drv_ad').css("display", "block");
                    $("#v2_RemotaEnd").attr('disabled', false);
                    break;

                case 2: // K

                    // habilita rede K
                    $('.div_v2_rede_k').css("display", "block");
                    $("#v2_remotas_k").attr('disabled', false);

                    // desabilita rede R e Q
                    $('.div_v2_rede_r_q').css("display", "none");
                    $("#v2_remotas_r_q").attr('disabled', true);

                    // habilita endereço
                    $('.div_v2_drv_ad').css("display", "block");
                    $("#v2_RemotaEnd").attr('disabled', false);
                    break;

                case 4: // DI

                    // desabilita rede K
                    $('.div_v2_rede_k').css("display", "none");
                    $("#v2_remotas_k").attr('disabled', true);

                    // desabilita rede R e Q
                    $('.div_v2_rede_r_q').css("display", "none");
                    $("#v2_remotas_r_q").attr('disabled', true);

                    // desabilita endereço
                    $('.div_v2_drv_ad').css("display", "none");
                    $("#v2_RemotaEnd").attr('disabled', true);
                    break;
            }
        }

        function SelecionouNumRemota_V2() {

            // pega redeIO selecionada
            var RedeIO = parseInt($("#v2_RedeIO").val());

            // pega numero remota
            var NumRemota = 255;

            switch (RedeIO) {
                case 0:  // Rede R
                case 1:  // Rede Q
                    NumRemota = parseInt(document.getElementById("v2_remotas_r_q").value);
                    break;

                case 2:  // Rede K
                    NumRemota = parseInt(document.getElementById("v2_remotas_k").value);
                    break;

                case 4:  // DI
                    NumRemota = 255;
                    break;
            }

            // habilita ou desabilita campo de endereco do driver
            ShowEnderecoDrv_V2(NumRemota);
        }

        function ShowEnderecoDrv_V2(NumRemota) {

            // caso numero de remota nao selecionado desabilita campo de endereco de driver
            if (NumRemota < 0 || NumRemota > 31 || Number.isNaN(NumRemota)) {
                // caso nao escolheu remota deixo input em branco
                document.getElementById("v2_RemotaEnd").value = "";
                $("#v2_RemotaEnd").attr('disabled', true);
            }
            else {
                $("#v2_RemotaEnd").attr('disabled', false);
            }
        }


        function TrataValores() {

            // trata variavel numero da remota V1
            var rede_io = parseInt($("#v1_RedeIO").val());
            var num_remota = 0;

            // recebe numero da remota de acordo com redeIO selecionado
            switch (rede_io) {
                case 0:  // Rede R
                case 1:  // Rede Q
                    num_remota = parseInt(document.getElementById("v1_remotas_r_q").value);
                    break;

                case 2:  // Rede K
                    num_remota = parseInt(document.getElementById("v1_remotas_k").value);
                    break;

                case 3:  // DO
                    num_remota = 0;
                    break;
            }

            if (Number.isNaN(num_remota) || num_remota < 0 || num_remota > 31) {
                num_remota = 255;
            }

            document.getElementById('v1_Remota').value = num_remota;


            // trata variavel numero da remota V2
            rede_io = parseInt($("#v2_RedeIO").val());
            num_remota = 0;

            // recebe numero da remota de acordo com redeIO selecionado
            switch (rede_io) {
                case 0:  // Rede R
                case 1:  // Rede Q
                    num_remota = parseInt(document.getElementById("v2_remotas_r_q").value);
                    break;

                case 2:  // Rede K
                    num_remota = parseInt(document.getElementById("v2_remotas_k").value);
                    break;

                case 3:  // DO
                    num_remota = 0;
                    break;
            }

            if (Number.isNaN(num_remota) || num_remota < 0 || num_remota > 31) {
                num_remota = 255;
            }

            document.getElementById('v2_Remota').value = num_remota;

        }

        function Programar() {

            // trata valores das variaveis a serem salvas
            TrataValores();

            var $form = $('#form');
            
            // apresenta primeiro tab que tiver erro
            var IsTabValid = true;
            var TabInvalid = 1;

            $(".tab-content").find("div.tab-pane").each(function (index, tab) {
                var id = $(tab).attr("id");
                $('a[href="#' + id + '"]').tab('show');

                IsTabValid = $("#form").valid();

                if (!IsTabValid) {

                    TabInvalid = id;
                    return false;
                }
            });

            // apresenta primeira tab ou com erro
            $('a[href="#tab-' + TabInvalid + '"]').tab('show');

            // verifica se sao validas
            if (!IsTabValid) return false;

            // retiro campos desabilitados, pois nao envia via POST
            $(":disabled", $('#form')).removeAttr("disabled");

            $.ajax({
                url: '/Configuracao/GateX_MedAna_Programar',
                data: $form.serialize(),
                type: 'POST',
                success: function (data) {

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {
                            swal({
                                title: "Erro",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {


                            });
                        }
                        else {

                            // redireciona
                            var IDGateway = document.getElementById('IDGateway').value;
                            var url = '/Configuracao/GateX_MedAna?IDGateway=' + IDGateway + '&Origem=2';

                            window.location.href = url;
                        }

                    }, 100);

                },
                error: function (xhr, status, error) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao programar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // redireciona para pagina lista de saidas
                            var IDGateway = document.getElementById('IDGateway').value;
                            var url = '/Configuracao/GateX_MedAna?IDGateway=' + IDGateway + '&Origem=2';

                            window.location.href = url;
                        });
                    }, 100);
                },
            });
        }

        $(".link_primeira").off().on("click", function (event) {
            event.stopPropagation();

            // primeira medição
            SalvaNavega(0);
        });

        $(".link_ultima").off().on("click", function (event) {
            event.stopPropagation();

            // ultima medição
            SalvaNavega(35);
        });

        $(".link_prog_menos").off().on("click", function (event) {
            event.stopPropagation();

            var NumMedicaoGateway = parseInt(document.getElementById("NumMedicaoGateway").value);

            // medição anterior
            SalvaNavega(NumMedicaoGateway - 1);
        });

        $(".link_prog_mais").off().on("click", function (event) {
            event.stopPropagation();

            var NumMedicaoGateway = parseInt(document.getElementById("NumMedicaoGateway").value);

            // proxima medição
            SalvaNavega(NumMedicaoGateway + 1);
        });


        function SalvaNavega(navega) {

            // pega variavel selecionada
            var variavel = parseInt($("#TipoVariavel").val());

            // caso desprogramado nao salvo
            if (variavel == 0) {

                // aguarde
                $('.overlay_aguarde').toggle();

                // redireciona para saida desejada
                var IDGateway = parseInt(document.getElementById("IDGateway").value);
                var url = '/Configuracao/GateX_MedAna_Editar?IDGateway=' + IDGateway + '&NumMedicaoGateway=' + navega;

                window.location.href = url;
            }

                // caso programado salva ao navegar para a proxima desejada
            else {

                // trata valores das variaveis a serem salvas
                TrataValores();

                var $form = $('#form');

                // apresenta primeiro tab que tiver erro
                var IsTabValid = true;
                var TabInvalid = 1;

                $(".tab-content").find("div.tab-pane").each(function (index, tab) {
                    var id = $(tab).attr("id");
                    $('a[href="#' + id + '"]').tab('show');

                    IsTabValid = $("#form").valid();

                    if (!IsTabValid) {

                        TabInvalid = id;
                        return false;
                    }
                });

                // apresenta primeira tab ou com erro
                $('a[href="#tab-' + TabInvalid + '"]').tab('show');

                // verifica se entradas sao validas
                if (!IsTabValid) return false;

                // verifica se saidas sao validas
                if (!$form.valid()) return false;

                event.stopPropagation();

                // aguarde
                $('.overlay_aguarde').toggle();

                // retiro campos desabilitados, pois nao envia via POST
                $(":disabled", $('#form')).removeAttr("disabled");

                $.ajax({
                    url: '/Configuracao/GateX_MedAna_Programar',
                    data: $form.serialize(),
                    type: 'POST',
                    success: function (data) {

                        setTimeout(function () {

                            // redireciona
                            var IDGateway = parseInt(document.getElementById("IDGateway").value);
                            var url = '/Configuracao/GateX_MedAna_Editar?IDGateway=' + IDGateway + '&NumMedicaoGateway=' + navega;
                            window.location.href = url;

                        }, 100);
                    },
                    error: function (xhr, status, error) {

                        setTimeout(function () {

                            swal({
                                title: "Erro",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                            });

                        }, 100);
                    },
                });
            }
        };

    </script>
}
