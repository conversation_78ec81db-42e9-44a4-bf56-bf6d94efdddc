﻿@model IEnumerable<SmartEnergyLib.SQL.UsuarioDominio>

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoUsuarios;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoUsuarios</h4>
                    <div class="pull-right dashboard-tools link_branco" style="margin-top:-8px;">

                        @{
                            int IDCliente = ViewBag._IDCliente;

                            if (IDCliente > 0)
                            {
                                <a href='@("/Configuracao/Menu?IDCliente=" + @IDCliente.ToString())' title="Menu @SmartEnergy.Resources.ConfiguracaoTexts.Configuracoes @SmartEnergy.Resources.ConfiguracaoTexts.ClienteAtual"><i class="fa fa-th-large"></i></a>
                            }
                        }

                        <h4>
                            <a href='@("/Configuracao/Usuarios_Editar?IDUsuario=0")' id="BotaoAdicionar" title="@SmartEnergy.Resources.ComumTexts.BotaoInserir"><i class="fa fa-plus"></i></a>
                        </h4>
                    </div>
                </div>
                <div class="panel-body">
                    <table class="table table-striped table-bordered table-hover dataTables-grupos">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.Nome</th>
                                <th>Email</th>
                                <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.TipoAcesso</th>
                                <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.SenhaExpirar</th>
                                <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.NumMedicoes</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>

                            @foreach (UsuarioDominio usuario in Model)
                            {
                                // tipo acesso
                                List<ListaTiposDominio> listatipos = ViewBag.listaTipoAcesso;
                                ListaTiposDominio tipo = listatipos.Find(item => item.ID == usuario.IDTipoAcesso);
                                string tipo_acesso = "";
                                if (tipo != null)
                                {
                                    tipo_acesso = tipo.Descricao;
                                }

                                // expirar
                                string expirarEm = string.Format("{0:dd/MM/yyyy}", usuario.ExpirarEm);

                                if (usuario.ExpirarMeses == 0)
                                {
                                    expirarEm = "Senha não expira";
                                }
                                
                                // configmed
                                string ConfigMed = usuario.ConfigMed;
                                List<int> ConfigMedList = null;

                                // copia medicoes para lista
                                if (!String.IsNullOrEmpty(ConfigMed))
                                {
                                    ConfigMedList = ConfigMed.Split('/')
                                        .Select(possibleIntegerAsString =>
                                        {
                                            int parsedInteger = 0;
                                            bool isInteger = int.TryParse(possibleIntegerAsString, out parsedInteger);
                                            return new { isInteger, parsedInteger };
                                        })
                                        .Where(tryParseResult => tryParseResult.isInteger)
                                        .Select(tryParseResult => tryParseResult.parsedInteger)
                                        .ToList();
                                }

                                int NumMed = 0;
                                if( ConfigMedList != null )
                                {
                                    NumMed = ConfigMedList.Count;
                                }
                                
                                <tr>
                                    <td>@usuario.IDUsuario</td>
                                    <td>@usuario.NomeUsuario</td>
                                    <td>@usuario.Email</td>
                                    <td class="some_minidesktop">@tipo_acesso</td>
                                    <td class="some_minidesktop">@expirarEm</td>
                                    <td class="some_minidesktop">@NumMed</td>
                                    <td class="link_preto">
                                        <a href='@("/Configuracao/Usuarios_Editar?IDUsuario=" + @usuario.IDUsuario.ToString())' title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"><i class="fa fa-edit icones"></i></a>

                                        @if (ViewBag.Permissao == PERMISSOES.ADMIN || ViewBag.Permissao == PERMISSOES.SUPORTE || ViewBag._IDTipoAcesso == TIPO_ACESSO.GESTAL_VENDAS)
                                        {
                                            // verifica se NAO eh o proprio usuario mesmo
                                            if (usuario.IDUsuario != ViewBag._IDUsuario)
                                            {
                                                <a href="#" onclick="javascript:Excluir(@usuario.IDUsuario);" id="BotaoExcluir" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-times icones"></i></a>
                                            }

                                            <a href="#" onclick="javascript:ExecutarBoasVindas('@usuario.NomeUsuario', '@usuario.Apelido', '@usuario.Login', '@usuario.Email', '@usuario.Senha');" id="BotaoBoasVindas" title="Enviar Email de Boas-Vindas (Login e Senha)"><i class="fa fa-envelope-o icones"></i></a>
                                            <a href='@("/Login/LoginInterno?IDUsuario=" + usuario.IDUsuario.ToString() + "&Acesso=" + usuario.IDTipoAcesso)' title="@SmartEnergy.Resources.LoginTexts.MenuLogin"><i class="fa fa-sign-in icones"></i></a>
                                        }

                                        @if (ViewBag.Permissao == PERMISSOES.GERENTE || ViewBag.Permissao == PERMISSOES.CONSULTOR || ViewBag.Permissao == PERMISSOES.SUPER_CONSULTOR)
                                        {
                                            // verifica se NAO eh o proprio usuario mesmo
                                            if (usuario.IDUsuario != ViewBag._IDUsuario)
                                            {
                                                <a href="#" onclick="javascript:Excluir(@usuario.IDUsuario);" id="BotaoExcluir" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-times icones"></i></a>
                                            }

                                            if (ViewBag.Permissao == PERMISSOES.CONSULTOR || ViewBag.Permissao == PERMISSOES.SUPER_CONSULTOR)
                                            {
                                                <a href="#" onclick="javascript:ExecutarBoasVindas('@usuario.NomeUsuario', '@usuario.Apelido', '@usuario.Login', '@usuario.Email', '@usuario.Senha');" id="BotaoBoasVindas" title="Enviar Email de Boas-Vindas (Login e Senha)"><i class="fa fa-envelope-o icones"></i></a>
                                                <a href='@("/Login/LoginInterno?IDUsuario=" + usuario.IDUsuario.ToString() + "&Acesso=" + usuario.IDTipoAcesso)' title="@SmartEnergy.Resources.LoginTexts.MenuLogin"><i class="fa fa-sign-in icones"></i></a>
                                            }
                                        }
                                        
                                    </td>
                                </tr>
                            }

                        </tbody>
                    </table>

                </div>
            </div>
        </div>
    </div>


</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


<script type="text/javascript">

    $(document).ready(function () {

        var permissao = Math.ceil(@ViewBag.Permissao);
        var visivel = true;

        if (permissao == PERMISSOES_ADMIN)
        {
            visivel = true;
        }

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {
                var x = (a == "-") ? 0 : a.replace(/,/, ".");
                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-grupos').DataTable({
            "iDisplayLength": 18,
            dom: 'Bftp',
            buttons: [
                {
                    extend: 'excelHtml5',
                    filename: 'Usuarios',
                    title: 'Usuários',
                    text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-excel-o" data-toggle="tooltip" data-placement="left" title="Excel"></span>',
                    className: 'btn btn-default btn-xs'
                },
                {
                    extend: 'pdfHtml5',
                    filename: 'Usuarios',
                    title: 'Usuários',
                    text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-pdf-o" data-toggle="tooltip" data-placement="left" title="PDF"></span>',
                    className: 'btn btn-default btn-xs'
                },
            ],

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "visible": visivel, "sType": "numero" },
                        { "aTargets": [1], "sType": "portugues" },
                        { "aTargets": [2], "sType": "portugues" },
                        { "aTargets": [3], "sType": "portugues" },
                        { "aTargets": [4], "sType": "portugues" },
                        { "aTargets": [5], "sType": "numero" },
                        { "aTargets": [6], "bVisible": true, "bSortable": false, "bSearchable": false },
            ],

            "aoColumns": [
            { sWidth: "10%" },
            { sWidth: "25%" },
            { sWidth: "15%" },
            { sWidth: "15%" },
            { sWidth: "15%" },
            { sWidth: "10%" },
            { sWidth: "10%" }
            ],
            'order': [1, 'asc'],
            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

        // desabilita campos
        disableAll();
    });

function disableAll() {

    var permissao = Math.ceil(@ViewBag.Permissao);

    // verifica permissao
    switch(permissao)
    {
        case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
        case PERMISSOES_GERENTE:            // 1 - permissao de Gerente: ve tudo e escreve parte
        case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
        case PERMISSOES_CONSULTOR:          // 5 - permissao de Consultor
        case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte 

            break;

        default:
        case PERMISSOES_OPERADOR:   // 2 - permissao de Operador: ve tudo e nao pode escrever
        case PERMISSOES_PRODUCAO:   // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

            document.getElementById("BotaoAdicionar").style.visibility = "hidden";
            break;
    }
}

function Excluir(IDUsuario) {
    event.stopPropagation();

    // titulo
    titulo = "Deseja excluir o Usuario?<br/>ID " + IDUsuario;

    swal({
        html: true,
        title: titulo,
        text: "Esta operação não poderá ser desfeita.",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#18a689",
        confirmButtonText: "Excluir",
        cancelButtonText: "Cancelar",
        closeOnConfirm: false
    }, function () {

        // excluir
        $.ajax(
        {
            type: 'GET',
            url: '/Configuracao/Usuarios_Excluir',
            data: { 'IDUsuario': IDUsuario },
            contentType: 'application/html',
            dataType: 'html',
            cache: false,
            async: true,
            success: function (data) {

                setTimeout(function () {

                    swal({
                        title: "Excluído com sucesso",
                        type: "success",
                        confirmButtonColor: "#18a689",
                        confirmButtonText: "Fechar",
                    }, function () {

                        // atualiza pagina
                        location.reload();
                    });

                }, 100);
            },
            error: function (response) {

                setTimeout(function () {

                    swal({
                        title: "Erro",
                        text: "Erro ao excluir!",
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    });

                }, 100);

            }
        });
    });
};

function ExecutarBoasVindas(NomeUsuario, Apelido, Login, Email, Senha) {
    event.stopPropagation();

    // parametros
    var Nome = NomeUsuario;
    var Apelido = Apelido;
    var Login = Login;
    var Email = Email;
    var Senha = Senha;

    swal({
        html: true,
        title: "Deseja enviar o Email para o Usuario '" + NomeUsuario + "' ?",
        text: "Email de Boas Vindas com Login e Senha",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#18a689",
        confirmButtonText: "Enviar Email",
        cancelButtonText: "Cancelar",
        closeOnConfirm: false
    }, function () {

        $.ajax(
        {
            type: 'GET',
            url: '/Configuracao/EnviarBoasVindas',
            dataType: 'html',
            data: { 'Nome': Nome, 'Login': Login, 'Email': Email, 'Senha': Senha },
            cache: false,
            async: true,
            success: function (data) {

                setTimeout(function () {

                    swal({
                        title: "Boas-Vindas enviada com sucesso",
                        type: "success",
                        confirmButtonColor: "#18a689",
                        confirmButtonText: "Fechar",
                    }, function () {

                    });

                }, 100);

            },
            error: function (xhr, status, error) {

                setTimeout(function () {

                    swal({
                        title: "Erro",
                        text: "Erro ao executar a operação!",
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    });

                }, 100);

            }
        });
    });
}

</script>
}


