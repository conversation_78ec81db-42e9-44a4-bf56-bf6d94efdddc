﻿@model SmartEnergyLib.SQL.GatewaysDominio

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = "Gateway";
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("Gateway_Editar", "Configuracao", new { ReturnUrl = ViewBag.ReturnUrl }, FormMethod.Post, new { id = "form", role = "form" }))
            {
                @Html.Hidden("IDCliente", Model.IDCliente)

                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>Gateway</h4>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="form-group col-lg-2">
                                <label class="control-label">&nbsp;ID</label>

                                @{
                                    if (Model.IDGateway == 0)
                                    {
                                        @Html.Hidden("IDGateway", Model.IDGateway)
                                        @Html.TextBox("Novo", "Nova Gateway", new { @class = "form-control", @disabled = "disabled" })
                                    }
                                    else
                                    {
                                        @Html.TextBoxFor(model => model.IDGateway, new { @class = "form-control", @disabled = "disabled" })
                                    }
                                }

                            </div>
                            <div class="col-lg-offset-4 col-lg-6">
                                <div class="ibox-tools">
                                    <a href='@("/Configuracao/Gateways?IDCliente=" + @Model.IDCliente)' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                    <button type="button" class="btn btn-primary" id="BotaoSalvar" onclick="Salvar();" disabled="">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                </div>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="tabs-container">
                                    <ul class="nav nav-tabs">
                                        <li class="active"><a data-toggle="tab" href="#tab-1"><i class="fa fa-cogs"></i> @SmartEnergy.Resources.ConfiguracaoTexts.Geral</a></li>
                                        <li class="tab-2"><a data-toggle="tab" href="#tab-2"><i class="fa fa-bell"></i> @SmartEnergy.Resources.ConfiguracaoTexts.AlarmesUsuario</a></li>
                                        <li class="tab-3"><a data-toggle="tab" href="#tab-3"><i class="fa fa-sitemap"></i> @SmartEnergy.Resources.ConfiguracaoTexts.Rede_IO_Distribuido</a></li>

                                        @{  
                                            if (Model.IDTipoGateway == TIPO_GATEWAY.GATE_C)
                                            {
                                                <li class="tab-4" style="display: none"><a data-toggle="tab" href="#tab-4"><i class="fa fa-sign-in"></i> @SmartEnergy.Resources.ConfiguracaoTexts.EntradasDigitais</a></li>
                                                <li class="tab-5" style="display: none"><a data-toggle="tab" href="#tab-5"><i class="fa fa-sign-out"></i> @SmartEnergy.Resources.ConfiguracaoTexts.SaidasDigitais</a></li>
                                            }
                                        }

                                        @{  
                                            bool View_ConfiguracaoRemota = (ViewBag.View_ConfiguracaoRemota == 1) ? true : false;

                                            if (View_ConfiguracaoRemota)
                                            {
                                                <li class="tab-6" style="display: none"><a data-toggle="tab" href="#tab-6"><i class="fa fa-wrench"></i> @SmartEnergy.Resources.ConfiguracaoTexts.ConfigEquipo</a></li>
                                            }
                                        }
                                    </ul>
                                    <div class="tab-content">
                                        <div id="tab-1" class="tab-pane active">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Nome</label>
                                                        @Html.TextBoxFor(model => model.Nome, new { @class = "form-control", @maxlength = "50", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.EnderecoLocal</label>
                                                        @Html.TextBoxFor(model => model.EnderecoLocal, new { @class = "form-control" })
                                                    </div>
                                                    <div class="form-group col-lg-4" id="ProjetoNroDIV" style="visibility:hidden">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ProjetoNro</label>
                                                        @Html.TextBoxFor(model => model.ProjetoNro, new { @class = "form-control", data_mask = "?9999-aaaaaa" })
                                                    </div>
                                                </div>
                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Modelo</label>
                                                        @Html.DropDownListFor(model => model.IDTipoGateway, new SelectList(ViewBag.listaTipoGateway, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.TipoGateway),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.MenuTexts.MenuLateralNumeroSerie</label>
                                                        @Html.DropDownListFor(model => model.IDNumeroSerie, new SelectList(ViewBag.listaGatewaysEstoque, "IDNumeroSerie", "NS_Gateway"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.MenuTexts.MenuLateralNumeroSerie),
                                                            new { @class = "form-control", @disabled = "disabled", @onchange = "AlterouNumeroSerie()" })

                                                        @Html.Hidden("NS_Gateway", Model.NS_Gateway)
                                                    </div>
                                                </div>
                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.TipoDCE</label>
                                                        @Html.DropDownListFor(model => model.IDTipoDCE, new SelectList(ViewBag.listaTipoDCE, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.TipoDCE),
                                                             new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.TipoTempo</label>
                                                        @Html.DropDownListFor(model => model.IDTipoTempo, new SelectList(ViewBag.listaTipoTempo, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.TipoTempo),
                                                             new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                                <hr />
                                                <div class="row">
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.AgentesFiliais - @SmartEnergy.Resources.ConfiguracaoTexts.RazaoSocial</label>
                                                        @Html.DropDownListFor(model => model.IDEmpresa, new SelectList(ViewBag.listaEmpresas, "IDEmpresa", "SiglaCCEE").OrderBy(x => x.Text),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.AgentesFiliais),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.SiglaCCEE</label>
                                                        <select class="form-control" id="empresa_SiglaCCEE" name="empresa_SiglaCCEE" disabled="">
                                                            <option value="">---</option>

                                                            @{
                                                                List<EmpresasDominio> empresas = ViewBag.listaEmpresas;
                                                                int IDEmpresa = Model.IDEmpresa;

                                                                foreach (EmpresasDominio empresa in empresas)
                                                                {
                                                                    if (IDEmpresa == empresa.IDEmpresa)
                                                                    {
                                                                        <option value="@empresa.IDEmpresa" selected="selected">@empresa.SiglaCCEE</option>
                                                                    }
                                                                    else
                                                                    {
                                                                        <option value="@empresa.IDEmpresa">@empresa.SiglaCCEE</option>
                                                                    }
                                                                }
                                                            }

                                                        </select>
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;CNPJ</label>
                                                        <select class="form-control" id="empresa_CNPJ" name="empresa_CNPJ" disabled="">
                                                            <option value="">---</option>

                                                            @{
                                                                foreach (EmpresasDominio empresa in empresas)
                                                                {
                                                                    if (IDEmpresa == empresa.IDEmpresa)
                                                                    {
                                                                        <option value="@empresa.IDEmpresa" selected="selected">@empresa.CNPJ</option>
                                                                    }
                                                                    else
                                                                    {
                                                                        <option value="@empresa.IDEmpresa">@empresa.CNPJ</option>
                                                                    }
                                                                }
                                                            }

                                                        </select>
                                                    </div>
                                                    <div class="form-group col-lg-1">
                                                        <label class="control-label">&nbsp;ID</label>
                                                        <select class="form-control" id="empresa_ID" name="empresa_ID" disabled="">
                                                            <option value="">---</option>

                                                            @{
                                                                foreach (EmpresasDominio empresa in empresas)
                                                                {
                                                                    if (IDEmpresa == empresa.IDEmpresa)
                                                                    {
                                                                        <option value="@empresa.IDEmpresa" selected="selected">@empresa.IDEmpresa</option>
                                                                    }
                                                                    else
                                                                    {
                                                                        <option value="@empresa.IDEmpresa">@empresa.IDEmpresa</option>
                                                                    }
                                                                }
                                                            }

                                                        </select>
                                                    </div>
                                                </div>
                                                <br />
                                                <div class="row AcessoRemotoDIV" style="display:none;">
                                                    <div class="form-group col-lg-2">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.AcessoRemoto</label><br />
                                                        <div class="i-checks" style="margin-top:6px;">

                                                            @Html.CheckBoxFor(model => model.AcessoRemotoCheck)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ConfiguracaoTexts.AcessoRemoto</span>
                                                            @Html.Hidden("AcessoRemoto", Model.AcessoRemoto)

                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div id="tab-2" class="tab-pane">
                                            <div class="panel-body">

                                                <div class="row">
                                                    <div class="form-group col-lg-12">
                                                        <table id="dataTables-AlmUsuario" class="table table-striped table-bordered table-hover">
                                                            <thead>
                                                                <tr>
                                                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.AlarmesUsuario</th>
                                                                    <th>Número do Alarme de Usuário</th>
                                                                    <th></th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>

                                                                @{
                                                                    List<DescricaoAlarmesDominio> AlmUsuarioList = ViewBag.AlmUsuarioList;

                                                                    if (AlmUsuarioList != null)
                                                                    {
                                                                        foreach (DescricaoAlarmesDominio almUsuario in AlmUsuarioList)
                                                                        {
                                                                            <tr>
                                                                                <td>@almUsuario.Descricao</td>
                                                                                <td>@almUsuario.Numero</td>
                                                                                <td class="link_preto">

                                                                                    @if (ViewBag.Permissao == PERMISSOES.ADMIN || ViewBag.Permissao == PERMISSOES.SUPORTE)
                                                                                    {
                                                                                        <a href="#" class="confirm-edit-AlmUsuario"><i class="fa fa-edit icones" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a>
                                                                                        <a href="#" class="confirm-delete-AlmUsuario"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>
                                                                                    }
                                                                                    
                                                                                </td>
                                                                            </tr>
                                                                         }
                                                                    }
                                                                }

                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-lg-7">
                                                        <a id="BotaoAdicionarAlmUsuario" data-toggle="modal" href="#ModalAlmUsuario" class="btn btn-info pull-left" style="color:#ffffff; width:100%;">Adicionar Alarme de Usuário</a>
                                                    </div>
                                                    <div class="col-lg-5">
                                                        <a id="BotaoExcluirTodosAlmUsuario" href="#" onclick="javascript:Excluir_Todos_AlmUsuario();" class="btn btn-success pull-left" style="color:#ffffff; width:100%;">Excluir Todos</a>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                        <div id="tab-3" class="tab-pane">
                                            <div class="panel-body">

                                                <div class="row">
                                                    <div class="form-group col-lg-12">
                                                        <table id="dataTables-remotas" class="table table-striped table-bordered table-hover">
                                                            <thead>
                                                                <tr>
                                                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.Remotas</th>
                                                                    <th></th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>

                                                                @{
                                                                    List<GatewayRemotasDominio> RemotasList = ViewBag.RemotasList;

                                                                    if (RemotasList != null)
                                                                    {
                                                                        foreach (GatewayRemotasDominio remota in RemotasList)
                                                                        {
                                                                            <tr>
                                                                                <td>@remota.Remota</td>
                                                                                <td class="link_preto">

                                                                                    @if (ViewBag.Permissao == PERMISSOES.ADMIN || ViewBag.Permissao == PERMISSOES.SUPORTE)
                                                                                    {
                                                                                        <a href="#" class="confirm-delete-remota"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>
                                                                                    }
                                                                                    
                                                                                </td>
                                                                            </tr>
                                                                         }
                                                                    }
                                                                }

                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-lg-7">
                                                        <a id="BotaoAdicionarRemota" data-toggle="modal" href="#ModalRemotas" class="btn btn-info pull-left" style="color:#ffffff; width:100%;">@SmartEnergy.Resources.ConfiguracaoTexts.AdcionarRemota</a>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <a id="BotaoProcurarRemotas" href="#" onclick="javascript:Procurar_Remotas();" class="btn btn-info pull-left" style="color:#ffffff; width:100%;">Procurar e Adicionar Remotas</a>
                                                    </div>
                                                    <div class="col-lg-2">
                                                        <a id="BotaoExcluirTodasRemotas" href="#" onclick="javascript:Excluir_Todas_Remotas();" class="btn btn-success pull-left" style="color:#ffffff; width:100%;">Excluir Todas</a>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                        <div id="tab-4" class="tab-pane">
                                            <div class="panel-body">

                                                <div class="row">
                                                    <div class="form-group col-lg-12">
                                                        <table id="dataTables-EntradasDigitais" class="table table-striped table-bordered table-hover">
                                                            <thead>
                                                                <tr>
                                                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.EntradasDigitais</th>
                                                                    <th>Descrição Estado Ativou</th>
                                                                    <th>Descrição Estado Desativou</th>
                                                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.EntradaInterna</th>
                                                                    <th></th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>

                                                                @{
                                                                    List<EntradasDigitaisDominio> EntradasDigitaisList = ViewBag.EntradasDigitaisList;

                                                                    if (EntradasDigitaisList != null)
                                                                    {
                                                                        foreach (EntradasDigitaisDominio entradaDigital in EntradasDigitaisList)
                                                                        {      
                                                                            <tr>
                                                                                <td>@entradaDigital.Descricao</td>
                                                                                <td>@entradaDigital.Ativou</td>
                                                                                <td>@entradaDigital.Desativou</td>
                                                                                <td>@entradaDigital.NumEntradaGateway</td>
                                                                                <td class="link_preto">

                                                                                    @if (ViewBag.Permissao == PERMISSOES.ADMIN || ViewBag.Permissao == PERMISSOES.SUPORTE)
                                                                                    {
                                                                                        <a href="#" class="confirm-edit-EntradaDigital"><i class="fa fa-edit icones" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a>
                                                                                        <a href="#" class="confirm-delete-EntradaDigital"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>
                                                                                    }

                                                                                </td>
                                                                            </tr>
                                                                        }
                                                                    }                                                                    
                                                                }

                                                            </tbody>
                                                        </table>

                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-lg-6">
                                                        <a id="BotaoAdicionarEntradaDigital" data-toggle="modal" href="#ModalEntradaDigital" class="btn btn-info pull-left" style="color:#ffffff; width:100%;">Adicionar Entrada Digital</a>
                                                    </div>
                                                    <div class="col-lg-6">
                                                        <a id="BotaoExcluirTodosEntradaDigital" href="#" onclick="javascript:Excluir_Todos_EntradaDigital();" class="btn btn-success pull-left" style="color:#ffffff; width:100%;">Excluir Todas</a>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                        <div id="tab-5" class="tab-pane">
                                            <div class="panel-body">

                                                <div class="row">
                                                    <div class="form-group col-lg-12">
                                                        <table id="dataTables-SaidasDigitais" class="table table-striped table-bordered table-hover">
                                                            <thead>
                                                                <tr>
                                                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.SaidasDigitais</th>
                                                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.SaidaInterna</th>
                                                                    <th></th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>

                                                                @{
                                                                    List<SaidasDigitaisDominio> SaidasDigitaisList = ViewBag.SaidasDigitaisList;

                                                                    if (SaidasDigitaisList != null)
                                                                    {
                                                                        foreach (SaidasDigitaisDominio saidaDigital in SaidasDigitaisList)
                                                                        {      
                                                                            <tr>
                                                                                <td>@saidaDigital.Descricao</td>
                                                                                <td>@saidaDigital.NumSaidaGateway</td>
                                                                                <td class="link_preto">

                                                                                    @if (ViewBag.Permissao == PERMISSOES.ADMIN || ViewBag.Permissao == PERMISSOES.SUPORTE)
                                                                                    {
                                                                                        <a href="#" class="confirm-edit-SaidaDigital"><i class="fa fa-edit icones" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a>
                                                                                        <a href="#" class="confirm-delete-SaidaDigital"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>
                                                                                    }

                                                                                </td>
                                                                            </tr>
                                                                        }
                                                                    }                                                                    
                                                                }

                                                            </tbody>
                                                        </table>

                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-lg-4">
                                                        <a id="BotaoAdicionarSaidaDigital" data-toggle="modal" href="#ModalSaidaDigital" class="btn btn-info pull-left" style="color:#ffffff; width:100%;">Adicionar Saída Digital</a>
                                                    </div>
                                                    <div class="col-lg-4">
                                                        <a id="BotaoReceberSaidaDigital" href="#" onclick="javascript:Receber_SaidaDigital();" class="btn btn-info pull-left" style="color:#ffffff; width:100%;">Utilizar Saídas Digitais da Gateway</a>
                                                    </div>
                                                    <div class="col-lg-4">
                                                        <a id="BotaoExcluirTodosSaidaDigital" href="#" onclick="javascript:Excluir_Todos_SaidaDigital();" class="btn btn-success pull-left" style="color:#ffffff; width:100%;">Excluir Todas</a>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                        <div id="tab-6" class="tab-pane">
                                            <div class="panel-body">
                                                <div class="row">

                                                    @{
                                                        if (Model.GatewayConectada_IoT)
                                                        {
                                                            <div class="GateX_MenuConfigMenuDIV" style="display:none;">
                                                                @Html.Partial("_GateX_MenuConfigEquipo")
                                                            </div>
                                                            <div class="GateE_MenuConfigMenuDIV" style="display:none;">
                                                                @Html.Partial("_GateE_MenuConfigEquipo")
                                                            </div>
                                                        }
                                                        else
                                                        {
                                                            <br /><br /><br /><br /><br /><br />
                                                            <div class="row">
                                                                <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
                                                                    <i class="fa fa-chain-broken fa-5x" style="color:red"></i>
                                                                </div>
                                                            </div>
                                                            <br />
                                                            <div class="row">
                                                                <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
                                                                    <h2 style="color:red">Gateway não conectada no Servidor IoT</h2>
                                                                </div>
                                                            </div>
                                                            <br /><br /><br /><br /><br /><br />
                                                        }
                                                    }

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal inmodal animated fadeIn" id="ModalAlmUsuario" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Fechar</span></button>
                                        <h4 class="modal-title"><i class="fa fa-check-square-o"></i>&nbsp;&nbsp;Adicionar Alarme de Usuário</h4>
                                    </div>
                                    <div class="modal-body">

                                        <div class="row">
                                            <div class="form-group col-xs-6">
                                                <label class="control-label">&nbsp;Descrição</label>
                                                <input type="text" class="form-control" id="DescricaoAlmUsuario" value="Alarme de Usuário">
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-xs-6">
                                                <label class="control-label">&nbsp;Número do Alarme de Usuário</label>
                                                <input type="text" class="form-control" id="NumeroAlmUsuario" value="0" onkeypress="validar(this); return numerico(event);">
                                            </div>
                                        </div>

                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                        <button type="button" class="btn btn-primary" onclick="fnClickAddRowAlmUsuario();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoAtualizar</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal inmodal animated fadeIn" id="ModalRemotas" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Fechar</span></button>
                                        <h4 class="modal-title"><i class="fa fa-check-square-o"></i>&nbsp;&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.AdcionarRemota</h4>
                                    </div>
                                    <div class="modal-body">

                                        <div class="row">
                                            <div class="form-group col-xs-6">
                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.NumeroRemota</label>
                                                <input type="text" class="form-control" id="NumRemota" value="1" onkeypress="validar(this); return numerico(event);">
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-xs-6">
                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.QtRemotas</label>
                                                <input type="text" class="form-control" id="QtRemotas" value="1" onkeypress="validar(this); return numerico(event);">
                                            </div>
                                        </div>

                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                        <button type="button" class="btn btn-primary" onclick="fnClickAddRowRemota();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoInserir</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal inmodal animated fadeIn" id="ModalEntradaDigital" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Fechar</span></button>
                                        <h4 class="modal-title"><i class="fa fa-check-square-o"></i>&nbsp;&nbsp;Adicionar Entrada Digital</h4>
                                    </div>
                                    <div class="modal-body">

                                        <div class="row">
                                            <div class="form-group col-xs-6">
                                                <label class="control-label">&nbsp;Descrição</label>
                                                <input type="text" class="form-control" id="DescricaoEntradaDigital" value="Entrada Digital">
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-xs-6">
                                                <label class="control-label">&nbsp;Descrição Estado Ativou</label>
                                                <input type="text" class="form-control" id="AtivouEntradaDigital" value="Ativou">
                                            </div>
                                            <div class="form-group col-xs-6">
                                                <label class="control-label">&nbsp;Descrição Estado Desativou</label>
                                                <input type="text" class="form-control" id="DesativouEntradaDigital" value="Desativou">
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-xs-6">
                                                <label class="control-label">&nbsp;Número da Entrada Digital</label>
                                                <input type="text" class="form-control" id="NumeroEntradaDigital" value="0" onkeypress="validar(this); return numerico(event);">
                                            </div>
                                        </div>

                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                        <button type="button" class="btn btn-primary" onclick="fnClickAddRowEntradaDigital();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoAtualizar</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal inmodal animated fadeIn" id="ModalSaidaDigital" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Fechar</span></button>
                                        <h4 class="modal-title"><i class="fa fa-check-square-o"></i>&nbsp;&nbsp;Adicionar Saída Digital</h4>
                                    </div>
                                    <div class="modal-body">

                                        <div class="row">
                                            <div class="form-group col-xs-6">
                                                <label class="control-label">&nbsp;Descrição</label>
                                                <input type="text" class="form-control" id="DescricaoSaidaDigital" value="Saída Digital">
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-xs-6">
                                                <label class="control-label">&nbsp;Número da Saída Digital</label>
                                                <input type="text" class="form-control" id="NumeroSaidaDigital" value="0" onkeypress="validar(this); return numerico(event);">
                                            </div>
                                        </div>

                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                        <button type="button" class="btn btn-primary" onclick="fnClickAddRowSaidaDigital();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoAtualizar</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="overlay_aguarde" style="display: none">
                        <div class="spinner-aguarde">
                            <div class="sk-spinner sk-spinner-wandering-cubes">
                                <div class="sk-spinner sk-spinner-wave">
                                    <div class="sk-rect1 text-ligh"></div>
                                    <div class="sk-rect2"></div>
                                    <div class="sk-rect3"></div>
                                    <div class="sk-rect4"></div>
                                    <div class="sk-rect5"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="overlay_recebendo" style="display: none">
                        <div class="fa fa-download icone_recebendo">
                        </div>
                        <div class="text_recebendo some_desktop">
                            <span>@SmartEnergy.Resources.ConfiguracaoTexts.RecebeConfig</span>
                        </div>
                        <div class="spinner-recebendo">
                            <div class="sk-spinner sk-spinner-wandering-cubes">
                                <div class="sk-spinner sk-spinner-wave">
                                    <div class="sk-rect1 text-ligh"></div>
                                    <div class="sk-rect2"></div>
                                    <div class="sk-rect3"></div>
                                    <div class="sk-rect4"></div>
                                    <div class="sk-rect5"></div>
                                </div>                                
                            </div>
                        </div>                        
                    </div>

                </div>
            }

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/dataTables")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/plugins/iCheck")
    @Scripts.Render("~/plugins/AlertaBox")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

    $(document).ready(function () {

        // classe iChecks
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });

        // verifica tipo da gateway
        var combotipo = document.getElementById("IDTipoGateway");
        var tipo = parseInt(combotipo.options[combotipo.selectedIndex].value, 10);

        // verifica se é GateE, GateX, GateM ou GateC
        if (tipo == TIPO_GATEWAY_GATE_X_421 || tipo == TIPO_GATEWAY_GATE_X_422 || tipo == TIPO_GATEWAY_GATE_C || tipo == TIPO_GATEWAY_GATE_M || tipo == TIPO_GATEWAY_GATE_E) {
            $('[href="#tab-3"]').closest('li').css("display", "block");
        }
        else {
            $('[href="#tab-3"]').closest('li').css("display", "none");
        }

        // verifica se é GateC
        if (tipo == TIPO_GATEWAY_GATE_C) {
            $('[href="#tab-4"]').closest('li').css("display", "block");
            $('[href="#tab-5"]').closest('li').css("display", "block");
        }
        else {
            $('[href="#tab-4"]').closest('li').css("display", "none");
            $('[href="#tab-5"]').closest('li').css("display", "none");
        }

        var chkBox = document.getElementById('AcessoRemotoCheck');
        if (chkBox.checked) {
            $('[href="#tab-6"]').closest('li').show();
        }

        // verifica se é GateX
        if (tipo == TIPO_GATEWAY_GATE_X_421 || tipo == TIPO_GATEWAY_GATE_X_422) {
            $('.GateX_MenuConfigMenuDIV').css("display", "block");
        }
        else {
            $('.GateX_MenuConfigMenuDIV').css("display", "none");
        }

        // verifica se é GateE
        if (tipo == TIPO_GATEWAY_GATE_E) {
            $('.GateE_MenuConfigMenuDIV').css("display", "block");
        }
        else {
            $('.GateE_MenuConfigMenuDIV').css("display", "none");
        }


        //
        // VALIDACAO DOS CAMPOS
        //

        jQuery.validator.addMethod("alphanumeric", function (value, element) {
            return this.optional(element) || /^[\.\^\~\´\`áàãâéèêíìóòõôúùçA-Za-z0-9\s&+\-_,()/\\]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

        jQuery.validator.addMethod("numeric", function (value, element) {
            return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.numeric");

        $("#form").validate({
            rules: {
                Nome: {
                    required: true,
                    alphanumeric: true,
                    minlength: 3,
                    maxlength: 50
                },
                IDEmpresa: { required: true },
                IDTipoGateway: { required: true },
                IDTipoDCE: { required: true },
                IDTipoTempo: { required: true },
                EnderecoLocal: {
                    required: true,
                    min: 0,
                    max: 255
                }
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            }
        });

        // desabilita campos
        disableAll();

        // caso tipo da gateway mudar, atualizo campos
        $('#IDTipoGateway').change(function () {

            // obtem o tipo da gateway
            var tipo = $(this).find(":selected").val();

            // verifica se é GateX ou GateE
            if (tipo == TIPO_GATEWAY_GATE_X_421 || tipo == TIPO_GATEWAY_GATE_X_422 || tipo == TIPO_GATEWAY_GATE_E) {
                // permite acesso remoto
                $('.AcessoRemotoDIV').css("display", "block");

                var chkBox = document.getElementById('AcessoRemotoCheck');

                if (tipo == TIPO_GATEWAY_GATE_E) {

                    $('[href="#tab-6"]').closest('li').css("display", "block");

                    if (!chkBox.checked) {
                        chkBox.click();
                    }

                }
                else {
                    if (chkBox.checked) {
                        $('[href="#tab-6"]').closest('li').css("display", "block");
                    }
                }

            }
            else {
                // não permite acesso remoto
                $('.AcessoRemotoDIV').css("display", "none");
                $('[href="#tab-6"]').closest('li').css("display", "none");
            }

            // verifica se é GateX, GateM ou GateC
            if (tipo == TIPO_GATEWAY_GATE_X_421 || tipo == TIPO_GATEWAY_GATE_X_422 || tipo == TIPO_GATEWAY_GATE_M || tipo == TIPO_GATEWAY_GATE_C) {
                $('[href="#tab-3"]').closest('li').css("display", "block");
            }
            else {
                $('[href="#tab-3"]').closest('li').css("display", "none");
            }

            // verifica se é GateX ou GateC
            if (tipo == TIPO_GATEWAY_GATE_X_421 || tipo == TIPO_GATEWAY_GATE_X_422 || tipo == TIPO_GATEWAY_GATE_C) {
                $('[href="#tab-4"]').closest('li').css("display", "block");
                $('[href="#tab-5"]').closest('li').css("display", "block");
            }
            else {
                $('[href="#tab-4"]').closest('li').css("display", "none");
                $('[href="#tab-5"]').closest('li').css("display", "none");
            }

            // verifica se é GateX
            if (tipo == TIPO_GATEWAY_GATE_X_421 || tipo == TIPO_GATEWAY_GATE_X_422) {
                $('.GateX_MenuConfigMenuDIV').css("display", "block");
            }
            else {
                $('.GateX_MenuConfigMenuDIV').css("display", "none");
            }

            // verifica se é GateE
            if (tipo == TIPO_GATEWAY_GATE_E) {
                $('.GateE_MenuConfigMenuDIV').css("display", "block");
            }
            else {
                $('.GateE_MenuConfigMenuDIV').css("display", "none");
            }

            // atualizar número de série
            AtualizarNumeroSerie();
        });

        $('#AcessoRemotoCheck').on('ifToggled', function (e) {

            if (e.currentTarget.checked) {

                $('[href="#tab-6"]').closest('li').show();

                // verifica tipo da gateway
                var combotipo = document.getElementById("IDTipoGateway");
                var tipo = parseInt(combotipo.options[combotipo.selectedIndex].value, 10);

                // verifica se é GateX
                if (tipo == TIPO_GATEWAY_GATE_X_421 || tipo == TIPO_GATEWAY_GATE_X_422) {
                    $('.GateX_MenuConfigMenuDIV').css("display", "block");

                }
                else {
                    $('.GateX_MenuConfigMenuDIV').css("display", "none");
                }

                // verifica se é GateE
                if (tipo == TIPO_GATEWAY_GATE_E) {
                    $('.GateE_MenuConfigMenuDIV').css("display", "block");
                }
                else {
                    $('.GateE_MenuConfigMenuDIV').css("display", "none");
                }

            }
            else {
                $('[href="#tab-6"]').closest('li').hide();
            }

        });


        //
        // TABELA REMOTAS
        //

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {

                var x = a;

                if (a == "-") {
                    x = "-1";
                }
                else {
                    x = a.replace(".", "");
                    x = x.replace(",", ".");
                }

                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });


        $('#dataTables-remotas').DataTable({
            "iDisplayLength": 10,
            dom: 'ftp',

            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "80%" },
            { sWidth: "20%" },
            ],

            "columnDefs": [
                 { "targets": [0], "sType": "numero" },
                 { "targets": [1], "searchable": false, "orderable": false },
            ],
            'order': [0, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        $('#dataTables-menuGateX').DataTable({
            "iDisplayLength": 30,
            dom: '',

            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "2%" },
            { sWidth: "28%" },
            { sWidth: "70%" },
            ],

            "columnDefs": [
                 { "targets": [0], "bVisible": true, "bSortable": false, "bSearchable": false },
                 { "targets": [1], "bVisible": true, "bSortable": false, "bSearchable": false },
                 { "targets": [2], "bVisible": true, "bSortable": false, "bSearchable": false },
            ],
            'order': false,

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        $('#dataTables-menuGateE').DataTable({
            "iDisplayLength": 18,
            dom: '',

            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "2%" },
            { sWidth: "28%" },
            { sWidth: "70%" },
            ],

            "columnDefs": [
                 { "targets": [0], "bVisible": true, "bSortable": false, "bSearchable": false },
                 { "targets": [1], "bVisible": true, "bSortable": false, "bSearchable": false },
                 { "targets": [2], "bVisible": true, "bSortable": false, "bSearchable": false },
            ],
            'order': false,

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        $('#dataTables-AlmUsuario').DataTable({
            "iDisplayLength": 10,
            dom: 'ftp',

            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "60%" },
            { sWidth: "20%" },
            { sWidth: "20%" },
            ],

            "columnDefs": [
                 { "targets": [0], "sType": "portugues" },
                 { "targets": [1], "sType": "numero" },
                 { "targets": [2], "searchable": false, "orderable": false },
            ],
            'order': [0, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        $('#dataTables-EntradasDigitais').DataTable({
            "iDisplayLength": 10,
            dom: 'ftp',

            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "30%" },
            { sWidth: "20%" },
            { sWidth: "20%" },
            { sWidth: "15%" },
            { sWidth: "10%" },
            ],

            "columnDefs": [
                 { "targets": [0], "sType": "portugues" },
                 { "targets": [1], "sType": "portugues" },
                 { "targets": [2], "sType": "portugues" },
                 { "targets": [3], "sType": "numero" },
                 { "targets": [4], "searchable": false, "orderable": false },
            ],
            'order': [0, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        $('#dataTables-SaidasDigitais').DataTable({
            "iDisplayLength": 10,
            dom: 'ftp',

            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "60%" },
            { sWidth: "20%" },
            { sWidth: "20%" },
            ],

            "columnDefs": [
                 { "targets": [0], "sType": "portugues" },
                 { "targets": [1], "sType": "numero" },
                 { "targets": [2], "searchable": false, "orderable": false },
            ],
            'order': [0, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });


        //
        // Agentes e Filiais
        //

        // caso empresa, atualizo campos
        $('#IDEmpresa').change(function () {

            // obtem a empresa
            var id = $(this).find(":selected").val();

            // SiglaCCEE
            document.getElementById('empresa_SiglaCCEE').value = id;

            // CNPJ
            document.getElementById('empresa_CNPJ').value = id;

            // ID
            document.getElementById('empresa_ID').value = id;
        });


        $('form input').on('keypress', function (e) {
            return e.which !== 13;
        });

        // caso for selecionado alguma TAB, aponto para ele
        var url = document.location.toString();

        if (url.match('#')) {
            $('.nav-tabs a[href="#' + url.split('#')[1] + '"]').tab('show');

            setTimeout(function () {
                window.scrollTo(0, 0);
            }, 100);
        }

    });


    function disableAll() {

        var permissao = Math.ceil(@ViewBag.Permissao);

        // verifica permissao
        switch (permissao) {
            case PERMISSOES_ADMIN:     // 0 - permissao de Admin: ve e escreve em tudo
            case PERMISSOES_SUPORTE:   // 4 - permissao de Suporte: escreve tudo mas nao pode excluir

                document.getElementById("ProjetoNroDIV").style.visibility = "visible";
                $("#IDTipoGateway").attr('disabled', false);
                $("#IDTipoDCE").attr('disabled', false);
                $("#IDTipoTempo").attr('disabled', false);
                $("#IDNumeroSerie").attr('disabled', false);

                // verifica se é GateX
                var combotipo = document.getElementById("IDTipoGateway");
                var tipo = parseInt(combotipo.options[combotipo.selectedIndex].value, 10);

                if (tipo == TIPO_GATEWAY_GATE_X_421 || tipo == TIPO_GATEWAY_GATE_X_422 || tipo == TIPO_GATEWAY_GATE_E) {
                    // permite acesso remoto
                    $('.AcessoRemotoDIV').css("display", "block");
                }

                $("#BotaoAdicionarAlmUsuario").attr('disabled', false);
                $("#BotaoExcluirTodosAlmUsuario").attr('disabled', false);

                $("#BotaoAdicionarRemota").attr('disabled', false);
                $("#BotaoProcurarRemotas").attr('disabled', false);
                $("#BotaoExcluirTodasRemotas").attr('disabled', false);

                $("#BotaoAdicionarEntradaDigital").attr('disabled', false);
                $("#BotaoExcluirTodosEntradaDigital").attr('disabled', false);

                $("#BotaoAdicionarSaidaDigital").attr('disabled', false);
                $("#BotaoExcluirTodosSaidaDigital").attr('disabled', false);

            case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte

                $("#BotaoSalvar").attr('disabled', false);
                $("#Nome").attr('disabled', false);
                $("#IDEmpresa").attr('disabled', false);

                break;

            case PERMISSOES_PRODUCAO:   // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                $("#BotaoSalvar").attr('disabled', false);
                $("#Nome").attr('disabled', false);
                document.getElementById("ProjetoNroDIV").style.visibility = "visible";
                $("#IDEmpresa").attr('disabled', false);
                $("#IDTipoGateway").attr('disabled', false);
                $("#IDTipoDCE").attr('disabled', false);
                $("#IDTipoTempo").attr('disabled', false);

                $("#BotaoAdicionarAlmUsuario").attr('disabled', true);
                $("#BotaoExcluirTodosAlmUsuario").attr('disabled', true);

                $("#BotaoAdicionarRemota").attr('disabled', true);
                $("#BotaoProcurarRemotas").attr('disabled', true);
                $("#BotaoExcluirTodasRemotas").attr('disabled', false);

                $("#BotaoAdicionarEntradaDigital").attr('disabled', true);
                $("#BotaoReceberEntradaDigital").attr('disabled', true);
                $("#BotaoExcluirTodosEntradaDigital").attr('disabled', true);

                $("#BotaoAdicionarSaidaDigital").attr('disabled', true);
                $("#BotaoReceberSaidaDigital").attr('disabled', true);
                $("#BotaoExcluirTodosSaidaDigital").attr('disabled', true);

                break;

            case PERMISSOES_CONSULTOR:  // 5 - permissao de Consultor

                $("#BotaoSalvar").attr('disabled', false);
                $("#Nome").attr('disabled', false);

                $("#BotaoAdicionarAlmUsuario").attr('disabled', true);
                $("#BotaoExcluirTodosAlmUsuario").attr('disabled', true);

                $("#BotaoAdicionarRemota").attr('disabled', true);
                $("#BotaoProcurarRemotas").attr('disabled', true);
                $("#BotaoExcluirTodasRemotas").attr('disabled', false);

                $("#BotaoAdicionarEntradaDigital").attr('disabled', true);
                $("#BotaoReceberEntradaDigital").attr('disabled', true);
                $("#BotaoExcluirTodosEntradaDigital").attr('disabled', true);

                $("#BotaoAdicionarSaidaDigital").attr('disabled', true);
                $("#BotaoReceberSaidaDigital").attr('disabled', true);
                $("#BotaoExcluirTodosSaidaDigital").attr('disabled', true);

                break;

            default:
            case PERMISSOES_GERENTE:    // 1 - permissao de Gerente: ve tudo e escreve parte
            case PERMISSOES_OPERADOR:   // 2 - permissao de Operador: ve tudo e nao pode escrever

                $("#BotaoSalvar").attr('disabled', true);

                $("#BotaoAdicionarAlmUsuario").attr('disabled', true);
                $("#BotaoExcluirTodosAlmUsuario").attr('disabled', true);

                $("#BotaoAdicionarRemota").attr('disabled', true);
                $("#BotaoProcurarRemotas").attr('disabled', true);
                $("#BotaoExcluirTodasRemotas").attr('disabled', false);

                $("#BotaoAdicionarEntradaDigital").attr('disabled', true);
                $("#BotaoReceberEntradaDigital").attr('disabled', true);
                $("#BotaoExcluirTodosEntradaDigital").attr('disabled', true);

                $("#BotaoAdicionarSaidaDigital").attr('disabled', true);
                $("#BotaoReceberSaidaDigital").attr('disabled', true);
                $("#BotaoExcluirTodosSaidaDigital").attr('disabled', true);

                break;
        }
    }


    //
    // NÚMERO DE SÉRIE
    //

    function AlterouNumeroSerie() {

        // pega IDGateway
        var IDGateway = document.getElementById("IDGateway").value;

        var texto_auxiliar = "da nova gateway.";

        if (IDGateway > 0)
        {
            texto_auxiliar = "[" + IDGateway.toString() + "]. <br><br>O número de série anterior terá seu status alterado como <b>ASSISTÊNCIA TÉCNICA</b>.";
        }

        AlertaBox("Atenção", "Certifique-se que o número de série selecionado pertence a gateway configurada com o IDGateway " + texto_auxiliar, "warning");

    }

    function AtualizarNumeroSerie() {
        event.stopPropagation();

        // le IDTipoGateway e Número de Série
        var IDTipoGateway = parseInt(document.getElementById("IDTipoGateway").value);
        var IDNumeroSerie = parseInt(document.getElementById("IDNumeroSerie").value);

        // verifico se é número
        if (isNaN(IDNumeroSerie))
        {
            // não tem número de série selecionado
            IDNumeroSerie = 0;

        }

        // chama a Action para ler números de série
        $.getJSON('/Configuracao/ObterNumerosSerieEstoque', { 'IDTipoGateway': IDTipoGateway, 'IDNumeroSerie': IDNumeroSerie }, function (data) {

            // remove os dados que ja possui dos números de série
            $('#IDNumeroSerie option').remove();

            // default
            $('#IDNumeroSerie').append('<option value="">Selecione Número de Série</option>');

            // popula os options com os valores retornados em JSON
            for (var i = 0; i < data.length; i++) {
                $('#IDNumeroSerie').append('<option value="' +
                    data[i].IDNumeroSerie + '"> ' +
                    data[i].NS_Gateway + '</option>');
            }

        });
    }


    //
    // BOTAO ALARME USUARIO
    //

    function fnClickAddRowAlmUsuario() {

        // pega descricao adicionar
        var DescricaoAlmUsuario = document.getElementById("DescricaoAlmUsuario").value;

        // pega numero do alarme
        var NumeroAlmUsuario = parseInt(document.getElementById("NumeroAlmUsuario").value);

        // verifica
        if (NumeroAlmUsuario >= 0 && NumeroAlmUsuario < 100) {

            // procura na tabela se ja existe alarme de usuario
            var table = $('#dataTables-AlmUsuario').dataTable();
            row_count = table.fnGetData().length;

            var linhas = table.fnGetData();

            for (var j = 0; j < row_count; j++) {

                if (linhas[j][1] == NumeroAlmUsuario) {

                    // apaga para modificar
                    $('#dataTables-AlmUsuario').dataTable().fnDeleteRow(j, null, true);
                }
            }

            // insere na tabela se nao achou
            $('#dataTables-AlmUsuario').dataTable().fnAddData([DescricaoAlmUsuario, NumeroAlmUsuario.toString(), '<a href="#" class="confirm-edit-AlmUsuario link_preto"><i class="fa fa-edit icones" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a><a href="#" class="confirm-delete-AlmUsuario link_preto"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>']);

        }
        else {

            AlertaBox("Erro", "Número do alarme deve estar entre 0 e 99", "warning");

        }
    }

    //
    // BOTAO EDITAR ALARME USUARIO
    //

    $('#dataTables-AlmUsuario').on('click', '.confirm-edit-AlmUsuario', function (e) {
        event.stopPropagation();

        // linha
        var row = $(this).closest('tr');

        // pega campos da tabela
        var data = $('#dataTables-AlmUsuario').dataTable().fnGetData(row);

        var DescricaoAlmUsuario = data[0];
        var NumeroAlmUsuario = data[1];

        // pega descricao adicionar
        document.getElementById("DescricaoAlmUsuario").value = DescricaoAlmUsuario;

        // pega numero do alarme
        document.getElementById("NumeroAlmUsuario").value = NumeroAlmUsuario;

        // abre janela
        $('#ModalAlmUsuario').modal('show');
    });


    //
    // BOTAO APAGAR ALARME USUARIO
    //

    $('#dataTables-AlmUsuario').on('click', '.confirm-delete-AlmUsuario', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // titulo
        titulo = "Deseja excluir o Alarme de Usuário da lista?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            setTimeout(function () {

                // apaga linha solicitada
                $('#dataTables-AlmUsuario').dataTable().fnDeleteRow(row);

            }, 100);

        });

    });


    function Excluir_Todos_AlmUsuario() {
        event.stopPropagation();

        // titulo
        titulo = "Deseja excluir todos os Alarmes de Usuário?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            setTimeout(function () {

                // apaga todas
                var oSettings = $('#dataTables-AlmUsuario').dataTable().fnSettings();
                var iTotalRecords = oSettings.fnRecordsTotal();
                for (i = 0; i <= iTotalRecords; i++) {
                    $('#dataTables-AlmUsuario').dataTable().fnDeleteRow(0, null, true);
                }

            }, 100);

        });
    };


    //
    // BOTAO ADICIONA REMOTA
    //

    function fnClickAddRowRemota() {

        // pega numero da remota a adicionar
        var NumRemota = parseInt(document.getElementById("NumRemota").value);

        // pega quantidade de remotas a adicionar
        var QtRemotas = parseInt(document.getElementById("QtRemotas").value);

        // verifica
        if (NumRemota >= 0 && NumRemota < 256 && QtRemotas > 0 && QtRemotas < 25) {
            // percorre remotas
            for (i = 0; i < QtRemotas; i++) {
                // numero da remota
                var NRem = NumRemota + i;

                if (NRem > 255) {
                    break;
                }

                // procura na tabela se ja existe remota
                var table = $('#dataTables-remotas').dataTable();
                row_count = table.fnGetData().length;

                var linhas = table.fnGetData();
                var achou = false;

                for (var j = 0; j < row_count; j++) {

                    if (linhas[j][0] == NRem) {
                        achou = true;
                    }
                }

                // insere na tabela se nao achou
                if (achou == false) {

                    $('#dataTables-remotas').dataTable().fnAddData([NRem.toString(), '<a href="#" class="confirm-delete-remota link_preto"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>']);

                }
            }
        }
        else {

            AlertaBox("Erro", "Valor deve estar entre 0 e 255", "warning");

        }
    }

    function validar(field) {
        str = field.value;
        if (str.length > 2) {
            field.value = str.substring(0, str.length - 1);
        }
    }

    function numerico(evt) {
        var key_code = evt.keyCode ? evt.keyCode : evt.charCode ? evt.charCode : evt.which ? evt.which : void 0;
        if (key_code == 8 || key_code == 9 || key_code == 13 || key_code == 27 || key_code == 46) {
            return true;
        } else if ((key_code >= 35) && (key_code <= 40)) {
            return true
        } else if ((key_code >= 48) && (key_code <= 57)) {
            return true
        }
        return false;
    }


    function Procurar_Remotas() {
        event.stopPropagation();

        // titulo
        titulo = "Deseja adicionar as Remotas pelos Eventos?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação atualiza a tabela com as Remotas encontradas no histórico de eventos.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Procurar",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            setTimeout(function () {

                // le IDGateway
                var id = document.getElementById("IDGateway").value;

                // chama a Action para ler remotas existente nos eventos
                $.getJSON('/Configuracao/ProcurarRemotas', { IDGateway: id }, function (data) {

                    // apaga todas
                    var table = $('#dataTables-remotas').dataTable();
                    var oSettings = table.fnSettings();
                    var iTotalRecords = oSettings.fnRecordsTotal();
                    for (var j = 0; j <= iTotalRecords; j++) {
                        table.fnDeleteRow(0, null, true);
                    }

                    // insere remotas na tabela com os valores retornados em JSON
                    for (i = 0; i < data.length; i++) {

                        var NumRemota = data[i].Remota;

                        if (NumRemota >= 0 && NumRemota < 256) {
                            $('#dataTables-remotas').dataTable().fnAddData([
                                NumRemota.toString(),
                                '<a href="#" class="confirm-delete-remota link_preto"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>']);
                        }
                    }

                });

            }, 100);

        });

    }


    //
    // BOTAO APAGAR REMOTA
    //

    $('#dataTables-remotas').on('click', '.confirm-delete-remota', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // titulo
        titulo = "Deseja excluir a remota da lista?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            setTimeout(function () {

                // apaga linha solicitada
                $('#dataTables-remotas').dataTable().fnDeleteRow(row);

            }, 100);

        });

    });


    function Excluir_Todas_Remotas() {
        event.stopPropagation();

        // titulo
        titulo = "Deseja excluir todas as Remotas?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            setTimeout(function () {

                // apaga todas
                var oSettings = $('#dataTables-remotas').dataTable().fnSettings();
                var iTotalRecords = oSettings.fnRecordsTotal();
                for (i = 0; i <= iTotalRecords; i++) {
                    $('#dataTables-remotas').dataTable().fnDeleteRow(0, null, true);
                }

            }, 100);

        });
    };


    //
    // BOTAO ENTRADA DIGITAL
    //

    function fnClickAddRowEntradaDigital() {

        // pega descricao adicionar
        var DescricaoEntradaDigital = document.getElementById("DescricaoEntradaDigital").value;

        // pega descricao ativou
        var AtivouEntradaDigital = document.getElementById("AtivouEntradaDigital").value;

        // pega descricao desaativou
        var DesativouEntradaDigital = document.getElementById("DesativouEntradaDigital").value;

        // pega numero da entrada
        var NumeroEntradaDigital = parseInt(document.getElementById("NumeroEntradaDigital").value);

        // verifica
        if (NumeroEntradaDigital >= 0 && NumeroEntradaDigital < 64) {

            // procura na tabela se ja existe
            var table = $('#dataTables-EntradasDigitais').dataTable();
            row_count = table.fnGetData().length;

            var linhas = table.fnGetData();

            for (var j = 0; j < row_count; j++) {

                if (linhas[j][3] == NumeroEntradaDigital) {

                    // apaga para modificar
                    $('#dataTables-EntradasDigitais').dataTable().fnDeleteRow(j, null, true);
                }
            }

            // insere na tabela se nao achou
            $('#dataTables-EntradasDigitais').dataTable().fnAddData([DescricaoEntradaDigital, AtivouEntradaDigital, DesativouEntradaDigital, NumeroEntradaDigital.toString(), '<a href="#" class="confirm-edit-EntradaDigital link_preto"><i class="fa fa-edit icones" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a><a href="#" class="confirm-delete-EntradaDigital link_preto"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>']);

        }
        else {

            AlertaBox("Erro", "Número da Entrada Digital deve estar entre 0 e 63", "warning");

        }
    }

    //
    // BOTAO EDITAR ENTRADA DIGITAL
    //

    $('#dataTables-EntradasDigitais').on('click', '.confirm-edit-EntradaDigital', function (e) {
        event.stopPropagation();

        // linha
        var row = $(this).closest('tr');

        // pega campos da tabela
        var data = $('#dataTables-EntradasDigitais').dataTable().fnGetData(row);

        var DescricaoEntradaDigital = data[0];
        var AtivouEntradaDigital = data[1];
        var DesativouEntradaDigital = data[2];
        var NumeroEntradaDigital = data[3];

        // pega descricao adicionar
        document.getElementById("DescricaoEntradaDigital").value = DescricaoEntradaDigital;

        // pega descricao ativou
        document.getElementById("AtivouEntradaDigital").value = AtivouEntradaDigital;

        // pega descricao desativou
        document.getElementById("DesativouEntradaDigital").value = DesativouEntradaDigital;

        // pega numero da entrada
        document.getElementById("NumeroEntradaDigital").value = NumeroEntradaDigital;

        // abre janela
        $('#ModalEntradaDigital').modal('show');
    });


    //
    // BOTAO APAGAR ENTRADA DIGITAL
    //

    $('#dataTables-EntradasDigitais').on('click', '.confirm-delete-EntradaDigital', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // titulo
        titulo = "Deseja excluir a Entrada Digital da lista?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            setTimeout(function () {

                // apaga linha solicitada
                $('#dataTables-EntradasDigitais').dataTable().fnDeleteRow(row);

            }, 100);

        });

    });

    function Receber_EntradaDigital() {
        event.stopPropagation();

        // IDGateway
        var IDGateway = document.getElementById("IDGateway").value;

        // titulo
        titulo = "Deseja utilizar as Entradas Digitais da Gateway?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Utilizar",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            // aguarde
            $('.overlay_recebendo').toggle();

            // receber
            $.ajax(
            {
                type: 'GET',
                url: '/Configuracao/Atualizar_EntradasDigitais',
                data: { 'IDGateway': IDGateway },
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                cache: false,
                async: true,
                success: function (data) {

                    // fim
                    $('.overlay_recebendo').toggle();

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {

                            AlertaBox("Erro", data.erro, "warning");

                        }
                        else {

                            // atualiza pagina
                            location.reload();
                        }

                    }, 100);
                },
                error: function (response) {

                    setTimeout(function () {

                        AlertaBox("Erro", "Erro", "warning");

                    }, 100);

                }
            });

        });
    };

    function Excluir_Todos_EntradaDigital() {
        event.stopPropagation();

        // titulo
        titulo = "Deseja excluir todas as Entradas Digitais?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            setTimeout(function () {

                // apaga todas
                var oSettings = $('#dataTables-EntradasDigitais').dataTable().fnSettings();
                var iTotalRecords = oSettings.fnRecordsTotal();
                for (i = 0; i <= iTotalRecords; i++) {
                    $('#dataTables-EntradasDigitais').dataTable().fnDeleteRow(0, null, true);
                }

            }, 100);

        });
    };


    //
    // BOTAO SAIDA DIGITAL
    //

    function fnClickAddRowSaidaDigital() {

        // pega descricao adicionar
        var DescricaoSaidaDigital = document.getElementById("DescricaoSaidaDigital").value;

        // pega numero da saida
        var NumeroSaidaDigital = parseInt(document.getElementById("NumeroSaidaDigital").value);

        // verifica
        if (NumeroSaidaDigital >= 0 && NumeroSaidaDigital < 64) {

            // procura na tabela se ja existe
            var table = $('#dataTables-SaidasDigitais').dataTable();
            row_count = table.fnGetData().length;

            var linhas = table.fnGetData();

            for (var j = 0; j < row_count; j++) {

                if (linhas[j][1] == NumeroSaidaDigital) {

                    // apaga para modificar
                    $('#dataTables-SaidasDigitais').dataTable().fnDeleteRow(j, null, true);
                }
            }

            // insere na tabela se nao achou
            $('#dataTables-SaidasDigitais').dataTable().fnAddData([DescricaoSaidaDigital, NumeroSaidaDigital.toString(), '<a href="#" class="confirm-edit-SaidaDigital link_preto"><i class="fa fa-edit icones" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a><a href="#" class="confirm-delete-SaidaDigital link_preto"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>']);

        }
        else {

            AlertaBox("Erro", "Número da Saída Digital deve estar entre 0 e 63", "warning");

        }
    }

    //
    // BOTAO EDITAR SAIDA DIGITAL
    //

    $('#dataTables-SaidasDigitais').on('click', '.confirm-edit-SaidaDigital', function (e) {
        event.stopPropagation();

        // linha
        var row = $(this).closest('tr');

        // pega campos da tabela
        var data = $('#dataTables-SaidasDigitais').dataTable().fnGetData(row);

        var DescricaoSaidaDigital = data[0];
        var NumeroSaidaDigital = data[1];

        // pega descricao adicionar
        document.getElementById("DescricaoSaidaDigital").value = DescricaoSaidaDigital;

        // pega numero da entrada
        document.getElementById("NumeroSaidaDigital").value = NumeroSaidaDigital;

        // abre janela
        $('#ModalSaidaDigital').modal('show');
    });


    //
    // BOTAO APAGAR SAIDA DIGITAL
    //

    $('#dataTables-SaidasDigitais').on('click', '.confirm-delete-SaidaDigital', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // titulo
        titulo = "Deseja excluir a Saída Digital da lista?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            setTimeout(function () {

                // apaga linha solicitada
                $('#dataTables-SaidasDigitais').dataTable().fnDeleteRow(row);

            }, 100);

        });

    });

    function Receber_SaidaDigital() {
        event.stopPropagation();

        // IDGateway
        var IDGateway = document.getElementById("IDGateway").value;

        // titulo
        titulo = "Deseja utilizar as Saídas Digitais da Gateway?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Utilizar",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            // aguarde
            $('.overlay_recebendo').toggle();

            // receber
            $.ajax(
            {
                type: 'GET',
                url: '/Configuracao/Atualizar_SaidasDigitais',
                data: { 'IDGateway': IDGateway },
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                cache: false,
                async: true,
                success: function (data) {

                    // fim
                    $('.overlay_recebendo').toggle();

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {

                            AlertaBox("Erro", data.erro, "warning");
                        }
                        else {

                            // atualiza pagina
                            location.reload();
                        }

                    }, 100);
                },
                error: function (response) {

                    setTimeout(function () {

                        AlertaBox("Erro", "Erro", "warning");

                    }, 100);

                }
            });

        });
    };

    function Excluir_Todos_SaidaDigital() {
        event.stopPropagation();

        // titulo
        titulo = "Deseja excluir todas as Saidas Digitais?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            setTimeout(function () {

                // apaga todas
                var oSettings = $('#dataTables-SaidasDigitais').dataTable().fnSettings();
                var iTotalRecords = oSettings.fnRecordsTotal();
                for (i = 0; i <= iTotalRecords; i++) {
                    $('#dataTables-SaidasDigitais').dataTable().fnDeleteRow(0, null, true);
                }

            }, 100);

        });
    };


    //
    // BOTAO SALVAR (SUBMIT)
    //

    $.fn.serializeObject = function () {
        var o = {};
        var a = this.serializeArray();
        $.each(a, function () {
            if (o[this.name] !== undefined) {
                if (!o[this.name].push) {
                    o[this.name] = [o[this.name]];
                }
                o[this.name].push(this.value || '');
            } else {
                o[this.name] = this.value || '';
            }
        });
        return o;
    };

    function Receber(Config) {
        event.stopPropagation();

        var IDGateway = parseInt($("#IDGateway").val());

        swal({
            title: "Deseja receber a configuração do equipamento?",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#1ab394",
            confirmButtonText: "Receber",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            // aguarde
            $('.overlay_recebendo').toggle();

            var url = '/Configuracao/' + Config + '?IDGateway=' + IDGateway;

            window.location.href = url;
        });
    };

    function ReceberSemPergunta(Config) {
        event.stopPropagation();

        var IDGateway = parseInt($("#IDGateway").val());

        var url = '/Configuracao/' + Config + '?IDGateway=' + IDGateway;

        window.location.href = url;
    };


    function Salvar() {

        var $form = $('#form');

        // apresenta primeiro tab que tiver erro
        var IsTabValid = true;
        var TabInvalid = 1;

        $(".tab-content").find("div.tab-pane").each(function (index, tab) {
            var id = $(tab).attr("id");
            $('a[href="#' + id + '"]').tab('show');

            IsTabValid = $("#form").valid();

            if (!IsTabValid) {

                TabInvalid = id;
                return false;
            }
        });

        // apresenta primeira tab ou com erro
        $('a[href="#tab-' + TabInvalid + '"]').tab('show');

        // verifica se entradas sao validas
        if (!IsTabValid) return false;


        // lista de alarmes de usuario
        var oTable = $('#dataTables-AlmUsuario').dataTable();
        var rows = oTable.fnSettings().aoData;
        var id = 0;
        var dataAlmUsuario = [];

        // percorre tabela
        $.each(rows, function (i, val) {

            // pega campos da tabela
            var Descricao = val._aData[0];
            var Numero = parseInt(val._aData[1]);

            dataAlmUsuario.push({
                "IDDescricao": 0,
                "IDCliente": 0,
                "IDGateway": 0,
                "Descricao": Descricao,
                "Numero": Numero
            });
        });


        // lista de remotas
        var oTable = $('#dataTables-remotas').dataTable();
        var rows = oTable.fnSettings().aoData;
        var id = 0;
        var dataRemotas = [];

        // percorre tabela
        $.each(rows, function (i, val) {

            // pega remota
            id = parseInt(val._aData[0]);

            dataRemotas.push({
                "IDCliente": 0,
                "Remota": id,
                "Status": 0,
                "DataStatus": "",
                "DataStatusTexto": "",
            });

        });


        // lista de entradas digitais
        var oTable = $('#dataTables-EntradasDigitais').dataTable();
        var rows = oTable.fnSettings().aoData;
        var id = 0;
        var dataEntradaDigital = [];

        // percorre tabela
        $.each(rows, function (i, val) {

            // pega campos da tabela
            var Descricao = val._aData[0];
            var Ativou = val._aData[1];
            var Desativou = val._aData[2];
            var Numero = parseInt(val._aData[3]);

            dataEntradaDigital.push({
                "IDEntradaDigital": 0,
                "IDCliente": 0,
                "IDGateway": 0,
                "Descricao": Descricao,
                "Ativou": Ativou,
                "Desativou": Desativou,
                "NumEntradaGateway": Numero,
                "status": 0
            });
        });

        // lista de saidas digitais
        var oTable = $('#dataTables-SaidasDigitais').dataTable();
        var rows = oTable.fnSettings().aoData;
        var id = 0;
        var dataSaidaDigital = [];

        // percorre tabela
        $.each(rows, function (i, val) {

            // pega campos da tabela
            var Descricao = val._aData[0];
            var Numero = parseInt(val._aData[1]);

            dataSaidaDigital.push({
                "IDSaidaDigital": 0,
                "IDCliente": 0,
                "IDGateway": 0,
                "Descricao": Descricao,
                "NumSaidaGateway": Numero,
                "status": 0
            });
        });


        // AcessoRemotoCheck
        // criei um campo auxiliar para tratar do checkBox, pois quando faz o JSON.stringify envia um array de estados [true,false] do checkbox em vez de somente um TRUE
        var chkBox = document.getElementById('AcessoRemotoCheck');

        if (chkBox.checked) {

            document.getElementById("AcessoRemoto").value = true;
            $('[href="#tab-6"]').closest('li').css("display", "block");
        }
        else {

            document.getElementById("AcessoRemoto").value = false;
            $('[href="#tab-6"]').closest('li').css("display", "none");
        }


        swal({
            title: "Deseja salvar?",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#1ab394",
            confirmButtonText: "Salvar",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {
            event.stopPropagation();

            // aguarde
            $('.overlay_aguarde').toggle();

            // retiro campos desabilitados, pois nao envia via POST
            $(":disabled", $('#form')).removeAttr("disabled");

            // constroi estrutura com FORM e lista de remotas
            data = { 'gateway': $form.serializeObject(), 'gatewayAlmUsuario': dataAlmUsuario, 'gatewayRemotas': dataRemotas, 'gatewayED': dataEntradaDigital, 'gatewaySD': dataSaidaDigital };
            data2 = JSON.stringify(data);

            $.ajax({
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                url: '/Configuracao/Gateway_Salvar',
                data: data2,
                type: 'POST',
                success: function (data) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {

                            AlertaBox("Erro", data.erro, "warning");

                        }
                        else {

                            swal({
                                title: "Salvo com sucesso",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // redireciona para pagina lista de gateways
                                var url = '/Configuracao/Gateways?IDCliente=' + document.getElementById('IDCliente').value;
                                window.location.href = url;
                            });
                        }

                    }, 100);

                },
                error: function (xhr, status, error) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        AlertaBox("Erro", "Erro ao salvar", "warning");

                    }, 100);

                }
            });
        });

    };

</script>
}
