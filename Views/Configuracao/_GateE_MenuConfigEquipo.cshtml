﻿@using SmartEnergyLib.Declaracoes

<table id="dataTables-menuGateE" class="table table-striped table-bordered table-hover">
    <thead>
        <tr>
            <th></th>
            <th>@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracao</th>
            <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.Descricao</th>
        </tr>
    </thead>
    <tbody>

        <tr style="cursor:pointer;" onclick="Receber('GateE_DataHora')">
            <td style="text-align: center;"><i class="fa fa-clock-o icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.DataHora</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoDataHora</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateE_MedEner')">
            <td style="text-align: center;"><i class="fa fa-flash icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.MedicoesEnergia</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.DescriConfigMedEner</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateE_RedeIoT')">
            <td style="text-align: center;"><i class="fa fa-usb icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.RedeIoT</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoRedeIot</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateE_DriverSincronismo')">
            <td style="text-align: center;"><i class="fa fa-level-up icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.DriverSincronismo</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoDriverSincronismo</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateE_Informacoes')">
            <td style="text-align: center;"><i class="fa fa-info-circle icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.Informacoes</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoInformacoes</td>
        </tr>
        <tr style="cursor:pointer;" onclick="Receber('GateE_Versao')">
            <td style="text-align: center;"><i class="fa fa-microchip icones_menu"></i></td>
            <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.Versao</td>
            <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.Modelo e @SmartEnergy.Resources.ConfiguracaoTexts.Versao</td>
        </tr>

        @if (ViewBag.Permissao == PERMISSOES.ADMIN || ViewBag.Permissao == PERMISSOES.SUPORTE)
        {
            <tr style="cursor:pointer;" onclick="Receber('GateE_AtualizacaoFirmware')">
                <td style="text-align: center;"><i class="fa fa-download icones_menu"></i></td>
                <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.AtualizacaoFirmware</td>
                <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.AtualizacaoFirmware</td>
            </tr>
            <tr style="cursor:pointer;" onclick="Receber('GateE_EnvioDados')">
                <td style="text-align: center;"><i class="fa fa-upload icones_menu"></i></td>
                <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.EnvioDados</td>
                <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.ComandoEnvioDados</td>
            </tr>
            <tr style="cursor:pointer;" onclick="Receber('GateE_LimparHistorico')">
                <td style="text-align: center;"><i class="fg fg-historico_limpar icones_menu"></i></td>
                <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.LimparHistorico</td>
                <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.ComandoLimparHistorico</td>
            </tr>
            <tr style="cursor:pointer;" onclick="Receber('GateE_Reboot')">
                <td style="text-align: center;"><i class="fg fg-reboot icones_menu"></i></td>
                <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.Reboot</td>
                <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.ComandoReboot</td>
            </tr>
            <tr style="cursor:pointer;" onclick="Receber('GateE_CorrigirFalhaUpload')">
                <td style="text-align: center;"><i class="fg fg-ferramenta icones_menu"></i></td>
                <td class="link_preto">@SmartEnergy.Resources.ConfiguracaoTexts.CorrigirFalhaUpload</td>
                <td class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.ComandoCorrigirFalhaUpload</td>
            </tr>
        }

    </tbody>
</table>




<script type="text/javascript">

    $(document).ready(function () {



    });

</script>
