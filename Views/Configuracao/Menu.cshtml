﻿@model SmartEnergyLib.SQL.ClientesDominio

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

<style>
    .posicao-texto {
        float: left;
        padding-left: 35px;
        padding-top: 7px;
    }

    .minimo_width {

        min-width:390px !important;
    }

    .link_preto a:link {
        color: #7E6A6C !important;
    }

    .link_preto a:visited {
        color: #7E6A6C !important;
    }

    .link_preto a:hover {
        color: #a0a0a0 !important;
    }

    .link_preto a:active {
        color: #7E6A6C !important;
    }
</style>

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracao;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="panel panel-success">
                                <div class="panel-heading">

                                    <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoCliente</h4><br />
                                    @Model.Nome

                                    @{
                                        int IDTipoAcesso = ViewBag._IDTipoAcesso;

                                        if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                        {
                                            <span> [@Model.IDCliente]</span>
                                        }
                                    }

                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel panel-title">
                        <div class="panel-body">

                            <div class="row">
                                <div class="col-lg-3 link_preto minimo_width">
                                    <a href='@("/Configuracao/Cliente_Editar?IDCliente=" + @Model.IDCliente)'>
                                        <div class="widget navy-bg">
                                            <div class="row">
                                                <div class="col-xs-2">
                                                    <i class="fa fa-th-large fa-5x"></i>
                                                </div>
                                                <div class="col-xs-10 text-left posicao-texto">
                                                    <h2 class="font-bold">@SmartEnergy.Resources.ConfiguracaoTexts.Cliente</h2>
                                                    <span>&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoCliente</span>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-lg-3 link_preto minimo_width">
                                    <a href='@("/Configuracao/Empresas?IDCliente=" + @Model.IDCliente)'>
                                        <div class="widget navy-bg">
                                            <div class="row">
                                                <div class="col-xs-2">
                                                    <i class="fa fa-th fa-5x"></i>
                                                </div>
                                                <div class="col-xs-10 text-left posicao-texto">
                                                    <h2 class="font-bold">@SmartEnergy.Resources.ConfiguracaoTexts.AgentesFiliais</h2>
                                                    <span>&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoAgentesFiliais</span>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-lg-3 link_preto minimo_width">
                                    <a href='@("/Configuracao/GrupoUnidades?IDCliente=" + @Model.IDCliente)'>
                                        <div class="widget navy-bg">
                                            <div class="row">
                                                <div class="col-xs-2">
                                                    <i class="fa fa-tags fa-5x"></i>
                                                </div>
                                                <div class="col-xs-10 text-left posicao-texto">
                                                    <h2 class="font-bold">@SmartEnergy.Resources.ConfiguracaoTexts.GrupoUnidades</h2>
                                                    <span>&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoGrupoUnidades</span>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-lg-3 link_preto minimo_width">
                                    <a href='@("/Configuracao/Unidades?IDCliente=" + @Model.IDCliente)'>
                                        <div class="widget navy-bg">
                                            <div class="row">
                                                <div class="col-xs-2">
                                                    <i class="fa fa-tag fa-5x"></i>
                                                </div>
                                                <div class="col-xs-10 text-left posicao-texto">
                                                    <h2 class="font-bold">@SmartEnergy.Resources.ConfiguracaoTexts.Unidades</h2>
                                                    <span>&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoUnidades</span>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-lg-3 link_preto minimo_width">
                                    <a href='@("/Configuracao/Gateways?IDCliente=" + @Model.IDCliente)'>
                                        <div class="widget navy-bg">
                                            <div class="row">
                                                <div class="col-xs-2">
                                                    <i class="fa fa-download fa-5x"></i>
                                                </div>
                                                <div class="col-xs-10 text-left posicao-texto">
                                                    <h2 class="font-bold">Gateways</h2>
                                                    <span>&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoGateways</span>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-lg-3 link_preto minimo_width">
                                    <a href='@("/Configuracao/Medicoes?IDCliente=" + @Model.IDCliente)'>
                                        <div class="widget navy-bg">
                                            <div class="row">
                                                <div class="col-xs-2">
                                                    <i class="fa fa-dashboard fa-5x"></i>
                                                </div>
                                                <div class="col-xs-10 text-left posicao-texto">
                                                    <h2 class="font-bold">@SmartEnergy.Resources.ConfiguracaoTexts.Medicoes</h2>
                                                    <span>&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoMedicoes</span>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            </div>

                            <div class="row">
                            </div>

                            <hr />

                            <div class="row">
                                <div class="col-lg-3 link_preto minimo_width">
                                    <a href='@("/Configuracao/Usuarios?IDCliente=" + @Model.IDCliente)'>
                                        <div class="widget lazur-bg">
                                            <div class="row">
                                                <div class="col-xs-2">
                                                    <i class="fa fa-users fa-5x"></i>
                                                </div>
                                                <div class="col-xs-10 text-left posicao-texto">
                                                    <h2 class="font-bold">@SmartEnergy.Resources.ConfiguracaoTexts.Usuarios</h2>
                                                    <span>&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoUsuarios</span>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-lg-3 link_preto minimo_width">
                                    <a href='@("/Configuracao/Contatos?IDCliente=" + @Model.IDCliente)'>
                                        <div class="widget lazur-bg">
                                            <div class="row">
                                                <div class="col-xs-2">
                                                    <i class="fa fa-vcard fa-5x"></i>
                                                </div>
                                                <div class="col-xs-10 text-left posicao-texto">
                                                    <h2 class="font-bold">@SmartEnergy.Resources.ConfiguracaoTexts.Contatos</h2>
                                                    <span>&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoContatos</span>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            </div>

                            <hr />

                            <div class="row">
                                <div class="col-lg-3 link_preto minimo_width">
                                    <a href='@("/Configuracao/GrupoMedicoes?IDCliente=" + @Model.IDCliente)'>
                                        <div class="widget blue-bg">
                                            <div class="row">
                                                <div class="col-xs-2">
                                                    <i class="fa fa-sitemap fa-5x"></i>
                                                </div>
                                                <div class="col-xs-10 text-left posicao-texto">
                                                    <h2 class="font-bold">@SmartEnergy.Resources.ConfiguracaoTexts.GrupoMedicoes</h2>
                                                    <span>&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoGrupoMedicoes</span>
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/validate")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }

    
    <script type="text/javascript">

        $(document).ready(function () {

        });

    </script>
}


