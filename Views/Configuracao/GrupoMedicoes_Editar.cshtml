﻿@model SmartEnergyLib.SQL.GrupoMedicoesDominio

@using SmartEnergyLib.SQL
@using System.Globalization

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoGrupoMedicoes;
}

<style>

    table.dataTable.select tbody tr,
    table.dataTable thead th:first-child {
        cursor: pointer;
    }

    tfoot input {
        width: 100%;
        padding: 3px;
        box-sizing: border-box;
    }

    #dataTables-medicoes tbody tr.selected {
        color: white;
        background-color: #1ab394;
    }

</style>


<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("GrupoMedicoes_Editar", "Configuracao", FormMethod.Post, new { id = "form", role = "form" }))
            {
                @Html.Hidden("IDCliente", Model.IDCliente)

                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoGrupoMedicoes</h4>
                        <div class="pull-right dashboard-tools link_branco" style="margin-top:-8px;">
                            <h4>

                                @{
                                    int IDCliente = ViewBag._IDCliente;

                                    if (IDCliente > 0)
                                    {
                                        <a href='@("/Configuracao/Menu?IDCliente=" + @IDCliente.ToString())' title="Menu @SmartEnergy.Resources.ConfiguracaoTexts.Configuracoes @SmartEnergy.Resources.ConfiguracaoTexts.ClienteAtual"><i class="fa fa-th-large"></i></a>
                                    }
                                }

                            </h4>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="form-group col-lg-2">
                                <label class="control-label">&nbsp;ID</label>

                                @{
                                    if (Model.IDGrupoMedicoes == 0)
                                    {
                                        @Html.Hidden("IDGrupoMedicoes", Model.IDGrupoMedicoes)
                                        @Html.TextBox("Novo", "Novo Grupo", new { @class = "form-control", @disabled = "disabled" })
                                    }
                                    else
                                    {
                                        @Html.TextBoxFor(model => model.IDGrupoMedicoes, new { @class = "form-control", @disabled = "disabled" })
                                    }
                                }

                            </div>
                            <div class="col-lg-offset-8 col-lg-2">
                                <div class="ibox-tools">
                                    <a href='@("/Configuracao/GrupoMedicoes?IDCliente=" + @Model.IDCliente)' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                    <button type="button" class="btn btn-primary" id="BotaoSalvar" onclick="Salvar();">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                </div>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Nome</label>
                                            @Html.TextBoxFor(model => model.Nome, new { @class = "form-control", @maxlength = "50" })
                                        </div>
                                    </div>
                                    <br />
                                    <div class="row">
                                        <div class="form-group col-lg-12">
                                            <table id="dataTables-medicoes" class="table table-striped table-bordered table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>ID</th>
                                                        <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Grupos</th>
                                                        <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Unidades</th>
                                                        <th>@SmartEnergy.Resources.UsuarioPerfilTexts.Medicoes</th>
                                                        <th></th>
                                                        <th>IDGrupoMedicoes</th>
                                                    </tr>
                                                </thead>
                                                <tfoot>
                                                    <tr>
                                                        <th>ID</th>
                                                        <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Grupos</th>
                                                        <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Unidades</th>
                                                        <th>@SmartEnergy.Resources.UsuarioPerfilTexts.Medicoes</th>
                                                        <th></th>
                                                        <th></th>
                                                    </tr>
                                                </tfoot>
                                                <tbody>

                                                    @{
                                                        List<CliGateGrupoUnidMedicoesDominio> medicoes = ViewBag.Medicoes;
                                                        List<GrupoMedicoesMedDominio> medGrupos = ViewBag.medGrupos;

                                                        foreach (CliGateGrupoUnidMedicoesDominio medicao in medicoes)
                                                        {
                                                            int IDGrupoMedicoes = Model.IDGrupoMedicoes;

                                                            foreach (var medGrupo in medGrupos)
                                                            {
                                                                if (medicao.IDMedicao == medGrupo.IDMedicao)
                                                                {
                                                                    IDGrupoMedicoes = medGrupo.IDGrupoMedicoes;
                                                                }
                                                            }

                                                        <tr>
                                                            <td>@medicao.IDMedicao</td>
                                                            <td class="some_minidesktop">@medicao.NomeGrupoUnidades</td>
                                                            <td class="some_minidesktop">@medicao.NomeUnidade</td>
                                                            <td>@medicao.NomeMedicao</td>
                                                            <td class="link_preto">
                                                                <a href="#" class="confirm-delete-medicao"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>
                                                            </td>
                                                            <td>@IDGrupoMedicoes</td>
                                                        </tr>

                                                        }
                                                    }

                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-lg-12">
                                            <a id="BotaoAdicionarMedicao" data-toggle="modal" href="#ModalMedicoes" class="btn btn-info pull-left" style="color:#ffffff; width:100%;">@SmartEnergy.Resources.UsuarioPerfilTexts.AdicionarMedicao</a>
                                        </div>
                                    </div>

                                    <br /><br />

                                    <div class="div_gerenciamento_selecione" style="display:block;">
                                        <div class="row">
                                            <div class="form-group col-lg-9">
                                                <label><i class="fa fa-exclamation-circle icones"></i>&nbsp;Adicione as medições para este Grupo.</label><br />
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>

                        <div class="modal inmodal animated fadeIn" id="ModalMedicoes" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                                        <h4 class="modal-title"><i class="fa fa-check-square-o"></i>&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.SelecioneMedicao</h4>
                                    </div>
                                    <div class="modal-body">

                                        <table id="dataTables-medicoes2" class="table table-striped table-bordered table-hover">
                                            <thead>
                                                <tr>
                                                    <th><input name="select_all" value="1" type="checkbox"></th>
                                                    <th>ID</th>
                                                    <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Grupos</th>
                                                    <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Unidades</th>
                                                    <th>@SmartEnergy.Resources.UsuarioPerfilTexts.Medicoes</th>
                                                </tr>
                                            </thead>
                                            <tfoot>
                                                <tr>
                                                    <th></th>
                                                    <th>ID</th>
                                                    <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Grupos</th>
                                                    <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Unidades</th>
                                                    <th>@SmartEnergy.Resources.UsuarioPerfilTexts.Medicoes</th>
                                                </tr>
                                            </tfoot>
                                            <tbody>

                                                @{
                                                    List<CliGateGrupoUnidMedicoesDominio> medicoes2 = ViewBag.Medicoes2;

                                                    foreach (var medicao in medicoes2)
                                                    {
                                                        <tr>
                                                            <td>@medicao.IDMedicao</td>
                                                            <td>@medicao.IDMedicao</td>
                                                            <td class="some_minidesktop">@medicao.NomeGrupoUnidades</td>
                                                            <td class="some_minidesktop">@medicao.NomeUnidade</td>
                                                            <td>@medicao.NomeMedicao</td>
                                                        </tr>
                                                    }
                                                }

                                            </tbody>
                                        </table>

                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                        <button type="button" class="btn btn-primary" onclick="fnClickAddRowMedicoes();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoInserir</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="overlay_aguarde" style="display: none">
                        <div class="spinner-aguarde">
                            <div class="sk-spinner sk-spinner-wandering-cubes">
                                <div class="sk-spinner sk-spinner-wave">
                                    <div class="sk-rect1 text-ligh"></div>
                                    <div class="sk-rect2"></div>
                                    <div class="sk-rect3"></div>
                                    <div class="sk-rect4"></div>
                                    <div class="sk-rect5"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            }

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/dataTables")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        // Array holding selected row IDs
        var rows_selected_medicoes = [];
        var names_selected_medicoes_grupo = [];
        var names_selected_medicoes_unid = [];
        var names_selected_medicoes_med = [];

        var names_selected_medicoes_IDGrupoMedicoes = [];

        //
        // Updates "Select all" control in a data table
        //
        function updateDataTableSelectAllCtrl(table) {
            var $table = table.table().node();
            var $chkbox_all = $('tbody input[type="checkbox"]', $table);
            var $chkbox_checked = $('tbody input[type="checkbox"]:checked', $table);
            var chkbox_select_all = $('thead input[name="select_all"]', $table).get(0);

            // If none of the checkboxes are checked
            if ($chkbox_checked.length === 0) {
                chkbox_select_all.checked = false;
                if ('indeterminate' in chkbox_select_all) {
                    chkbox_select_all.indeterminate = false;
                }

                // If all of the checkboxes are checked
            } else if ($chkbox_checked.length === $chkbox_all.length) {
                chkbox_select_all.checked = true;
                if ('indeterminate' in chkbox_select_all) {
                    chkbox_select_all.indeterminate = false;
                }

                // If some of the checkboxes are checked
            } else {
                chkbox_select_all.checked = true;
                if ('indeterminate' in chkbox_select_all) {
                    chkbox_select_all.indeterminate = true;
                }
            }
        }

        $(document).ready(function () {

            //
            // VALIDACAO DOS CAMPOS
            //

            jQuery.validator.addMethod("alphanumeric", function (value, element) {
                return this.optional(element) || /^[\'\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\+\-\_\(\)," "]+$/i.test(value);
            }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

            jQuery.validator.addMethod("numeric", function (value, element) {
                return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
            }, "@SmartEnergy.Resources.ValidateTexts.numeric");

            $("#form").validate({
                rules: {
                    Nome: {
                        required: true,
                        alphanumeric: true,
                        minlength: 3,
                        maxlength: 50
                    },
                    Unidade: {
                        required: true,
                        alphanumeric: true,
                        minlength: 1,
                        maxlength: 20
                    },
                },
                highlight: function (element) {
                    $(element).closest('.control-group').removeClass('success').addClass('error');
                },
                unhighlight: function (element) {
                    $(element).closest('.control-group').addClass('valid success').removeClass('error');
                }
            });

            // desabilita campos
            disableAll();

            //
            // TABELA MEDICOES
            //

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "portugues-pre": function (data) {
                    var a = 'a';
                    var e = 'e';
                    var i = 'i';
                    var o = 'o';
                    var u = 'u';
                    var c = 'c';
                    var special_letters = {
                        "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                        "É": e, "é": e, "Ê": e, "ê": e,
                        "Í": i, "í": i, "Î": i, "î": i,
                        "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                        "Ú": u, "ú": u, "Ü": u, "ü": u,
                        "ç": c, "Ç": c
                    };
                    for (var val in special_letters)
                        data = data.split(val).join(special_letters[val]).toLowerCase();
                    return data;
                },
                "portugues-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },
                "portugues-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            var tableMedicoes = $('#dataTables-medicoes').DataTable({
                "iDisplayLength": 10,
                dom: 'tp',

                'order': [1, 'asc'],
                "bAutoWidth": false,
                "aoColumns": [
                { sWidth: "4%" },
                { sWidth: "18%" },
                { sWidth: "18%" },
                { sWidth: "50%" },
                { sWidth: "10%" },
                { sWidth: "1%" },
                ],

                "columnDefs": [
                     {
                         "targets": [0],
                         "visible": false,
                         "searchable": false,
                         "orderable": false
                     },
                     { "targets": [1], "sType": "portugues" },
                     { "targets": [2], "sType": "portugues" },
                     { "targets": [3], "sType": "portugues" },
                     { "targets": [4], "searchable": false, "orderable": false },
                     { "targets": [5], "visible": false, "searchable": false, "orderable": false },
                ],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }
            });

            //
            // TABELA MEDICOES - BUSCA
            //

            // campos de busca em cada coluna na tabela
            $('#dataTables-medicoes tfoot th').each(function () {
                var title = $(this).text();
                if (title.length > 0)
                    $(this).html('<input type="text" placeholder="@SmartEnergy.Resources.UsuarioPerfilTexts.Busca ' + title + '" />');
            });

            // dataTable
            var table = $('#dataTables-medicoes').DataTable();

            // aplica a busca
            table.columns().every(function () {
                var that = this;

                $('input', this.footer()).on('keyup change', function () {
                    if (that.search() !== this.value) {
                        that
                            .search(this.value)
                            .draw();
                    }
                });
            });


            //
            // TABELA MEDICOES - SELECAO
            //

            $('#dataTables-medicoes tbody').on('click', 'tr', function () {

                if ($(this).hasClass('selected')) {

                    // retira selecao atual
                    $(this).removeClass('selected');


                    // desabilita
                    $(".div_gerenciamento *").prop('disabled', true);
                    $('.div_gerenciamento').css("display", "none");
                    $('.div_gerenciamento_selecione').css("display", "block");

                }
                else {

                    // habilita se nao for cliente
                    var permissao = Math.ceil(@ViewBag.Permissao);

                    if (permissao != 2) {
                        // habilita
                        $(".div_gerenciamento *").prop('disabled', false);
                        $('.div_gerenciamento').css("display", "block");
                        $('.div_gerenciamento_selecione').css("display", "none");
                    }

                    // dataTable
                    var table = $('#dataTables-medicoes').DataTable();

                    // retira selecao atual
                    table.$('tr.selected').removeClass('selected');

                    // seleciona
                    $(this).addClass('selected');

                    // pega dados da linha selecionada
                    var data = $('#dataTables-medicoes').DataTable().row(this).data();
                    var id = data[0];
                }
            });

            //
            // TABELA MEDICOES (MODAL)
            //

            var tableMedicoes2 = $('#dataTables-medicoes2').DataTable({
                "iDisplayLength": 6,
                dom: 'tp',

                'order': [2, 'asc'],
                "bAutoWidth": false,
                "aoColumns": [
                { sWidth: "5%" },
                { sWidth: "20%" },
                { sWidth: "20%" },
                { sWidth: "30%" },
                { sWidth: "25%" }
                ],

                'columnDefs': [{
                    'targets': 0,
                    'searchable': false,
                    'orderable': false,
                    'className': 'dt-center',
                    'render': function (data, type, full, meta) {
                        return '<input type="checkbox" name="checkbox_modal_medicoes">';
                    },
                },
                {
                    'targets': 1,
                    'visible': false,
                    'searchable': false,
                    'orderable': false,
                }],

                'rowCallback': function (row, data, dataIndex) {
                    // Get row ID
                    var rowId = data[0];

                    // If row ID is in the list of selected row IDs
                    if ($.inArray(rowId, rows_selected_medicoes) !== -1) {
                        $(row).find('input[type="checkbox"]').prop('checked', true);
                        $(row).addClass('selected');
                    }
                },

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }
            });

            // manipula checkbox
            $('#dataTables-medicoes2 tbody').on('click', 'input[type="checkbox"]', function (e) {
                var $row = $(this).closest('tr');

                // Get row data
                var data = tableMedicoes2.row($row).data();

                // Get row ID
                var rowId = data[0];
                var name_grupo = data[2];
                var name_unid = data[3];
                var name_med = data[4];

                // Determine whether row ID is in the list of selected row IDs
                var index = $.inArray(rowId, rows_selected_medicoes);

                // If checkbox is checked and row ID is not in list of selected row IDs
                if (this.checked && index === -1) {
                    rows_selected_medicoes.push(rowId);
                    names_selected_medicoes_grupo.push(name_grupo);
                    names_selected_medicoes_unid.push(name_unid);
                    names_selected_medicoes_med.push(name_med);

                    names_selected_medicoes_IDGrupoMedicoes.push(0);

                    // Otherwise, if checkbox is not checked and row ID is in list of selected row IDs
                } else if (!this.checked && index !== -1) {
                    rows_selected_medicoes.splice(index, 1);
                    names_selected_medicoes_grupo.splice(index, 1);
                    names_selected_medicoes_unid.splice(index, 1);
                    names_selected_medicoes_med.splice(index, 1);

                    names_selected_medicoes_IDGrupoMedicoes.splice(index, 1);
                }

                if (this.checked) {
                    $row.addClass('selected');
                } else {
                    $row.removeClass('selected');
                }

                // Update state of "Select all" control
                updateDataTableSelectAllCtrl(tableMedicoes2);

                // Prevent click event from propagating to parent
                e.stopPropagation();
            });

            // manipula click na tabela
            $('#dataTables-medicoes2').on('click', 'tbody td, thead th:first-child', function (e) {

                $(this).parent().find('input[type="checkbox"]').trigger('click');
            });

            // manipula click no "Select all"
            $('thead input[name="select_all"]', tableMedicoes2.table().container()).on('click', function (e) {
                if (this.checked) {
                    $('tbody input[type="checkbox"]:not(:checked)', tableMedicoes2.table().container()).trigger('click');
                } else {
                    $('tbody input[type="checkbox"]:checked', tableMedicoes2.table().container()).trigger('click');
                }

                // Prevent click event from propagating to parent
                e.stopPropagation();
            });

            // Handle table draw event
            tableMedicoes2.on('draw', function () {
                // Update state of "Select all" control
                updateDataTableSelectAllCtrl(tableMedicoes2);
            });

            // campos de busca em cada coluna na tabela
            $('#dataTables-medicoes2 tfoot th').each(function () {
                var title = $(this).text();
                if (title.length > 0)
                    $(this).html('<input type="text" placeholder="@SmartEnergy.Resources.UsuarioPerfilTexts.Busca ' + title + '" />');
            });

            // dataTable
            var table = $('#dataTables-medicoes2').DataTable();

            // aplica a busca
            table.columns().every(function () {
                var that = this;

                $('input', this.footer()).on('keyup change', function () {
                    if (that.search() !== this.value) {
                        that
                            .search(this.value)
                            .draw();
                    }
                });
            });

            $('form input').on('keypress', function (e) {
                return e.which !== 13;
            });
        });

        //
        // INPUT VALOR
        //

        function SetCell(item, valor) {

            // dataTable
            var table = $('#dataTables-medicoes').DataTable();
            var data = table.row('.selected').data();

            // verifica se tem selecionado
            if (data != undefined) {
                data[item] = valor;
                table.row('.selected').data(data).draw();
            }
        }

        function isNumberKey(evt) {
            var charCode = (evt.which) ? evt.which : evt.keyCode;
            if (charCode > 31 && (charCode < 44 || charCode > 57))
                return false;
            return true;
        }

        //
        // DESABILITA CAMPOS
        //

        function disableAll() {

            var permissao = Math.ceil(@ViewBag.Permissao);

            // verifica permissao
            switch (permissao) {

                case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
                case PERMISSOES_GERENTE:            // 1 - permissao de Gerente: ve tudo e escreve parte
                case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
                case PERMISSOES_CONSULTOR:          // 5 - permissao de Consultor
                case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte 

                    break;

                default:
                case PERMISSOES_OPERADOR:   // 2 - permissao de Operador: ve tudo e nao pode escrever
                case PERMISSOES_PRODUCAO:   // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                    $("#BotaoSalvar").attr('disabled', true);
                    $("#Nome").attr('disabled', true);
                    $("#Unidade").attr('disabled', true);
                    $("#BotaoAdicionarMedicao").attr('disabled', true);

                    break;
            }

        }

        //
        // BOTAO ADICIONA MEDICOES
        //

        function fnClickAddRowMedicoes() {

            // percorre lista de selecionados
            $.each(rows_selected_medicoes, function (index, rowId) {

                // procura na tabela se ja existe iD
                var table = $('#dataTables-medicoes').dataTable();
                row_count = table.fnGetData().length;
                var linhas = table.fnGetData();

                var achou = false;

                for (var j = 0; j < row_count; j++) {

                    if (linhas[j][0] == rowId) {
                        achou = true;
                    }
                }

                // insere na tabela se nao achou
                if (achou == false) {
                    $('#dataTables-medicoes').dataTable().fnAddData([
                        rowId,
                        names_selected_medicoes_grupo[index],
                        names_selected_medicoes_unid[index],
                        names_selected_medicoes_med[index],
                        '<a href="#" class="confirm-delete-medicao link_preto"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>',
                        names_selected_medicoes_IDGrupoMedicoes[index]]);

                }
            });

            // apaga arrays
            rows_selected_medicoes = [];
            names_selected_medicoes_grupo = [];
            names_selected_medicoes_unid = [];
            names_selected_medicoes_med = [];

            names_selected_medicoes_IDGrupoMedicoes = [];

            // desmarca checkbox
            var boxes = document.getElementsByName("checkbox_modal_medicoes");
            for (var i = 0; i < boxes.length; i++)
                boxes[i].checked = false;

            var chkbox_select_all = $('thead input[name="select_all"]', $('#dataTables-medicoes2')).get(0);
            chkbox_select_all.indeterminate = false;
            chkbox_select_all.checked = false;

            // remove da lista
            $('#dataTables-medicoes2').dataTable().$('tr.selected').empty();
            $('#dataTables-medicoes2').dataTable().draw();

            // desenha tabela
            $('#dataTables-medicoes').dataTable().draw();
        }

        //
        // BOTAO APAGAR MEDICAO
        //

        $('#dataTables-medicoes').on('click', '.confirm-delete-medicao', function (e) {
            e.preventDefault();

            // linha
            var row = $(this).closest('tr');

            // le dados da linha
            var data = $('#dataTables-medicoes').DataTable().row(row).data();

            var rowId = data[0];
            var grupo = data[1];
            var unidade = data[2];
            var medicao = data[3];

            // titulo
            titulo = "Deseja excluir a medição da lista?";

            swal({
                html: true,
                title: titulo,
                text: "Esta operação não poderá ser desfeita.",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#18a689",
                confirmButtonText: "Excluir",
                cancelButtonText: "Cancelar",
                closeOnConfirm: true
            }, function () {

                setTimeout(function () {

                    // insere no modal
                    $('#dataTables-medicoes2').dataTable().fnAddData([
                        rowId,
                        rowId,
                        grupo,
                        unidade,
                        medicao]);

                    // apaga linha solicitada
                    $('#dataTables-medicoes').dataTable().fnDeleteRow(row);

                    // desenha tabela
                    $('#dataTables-medicoes').dataTable().draw();

                    // desenha tabela
                    $('#dataTables-medicoes2').dataTable().draw();


                }, 100);

            });

        });

        //
        // BOTAO SALVAR (SUBMIT)
        //

        $.fn.serializeObject = function () {
            var o = {};
            var a = this.serializeArray();
            $.each(a, function () {
                if (o[this.name] !== undefined) {
                    if (!o[this.name].push) {
                        o[this.name] = [o[this.name]];
                    }
                    o[this.name].push(this.value || '');
                } else {
                    o[this.name] = this.value || '';
                }
            });
            return o;
        };

        function Salvar() {


            var $form = $('#form');

            // verifica se entradas sao validas
            if (!$form.valid()) return false;

            // lista de medicoes
            var oTable = $('#dataTables-medicoes').dataTable();
            var rows = oTable.fnSettings().aoData;
            var IDMedicao = 0;
            var IDGrupoMedicoes = 0;
            var dataArray = [];

            // percorre tabela
            $.each(rows, function (i, val) {

                // pega valores
                IDGrupoMedicoes = parseInt(val._aData[6]);
                IDMedicao = parseInt(val._aData[0]);

                dataArray.push({
                    "IDGrupoMedicoes": IDGrupoMedicoes,
                    "IDMedicao": IDMedicao
                });
            });

            // deseja salvar
            swal({
                title: "Deseja salvar?",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#1ab394",
                confirmButtonText: "Salvar",
                cancelButtonText: "Cancelar",
                closeOnConfirm: true
            }, function () {
                event.stopPropagation();

                // aguarde
                $('.overlay_aguarde').toggle();

                // retiro campos desabilitados, pois nao envia via POST
                $(":disabled", $('#form')).removeAttr("disabled");

                // constroi estrutura com FORM e lista de medicoes
                data = { 'grupo': $form.serializeObject(), 'medGrupos': dataArray };
                data2 = JSON.stringify(data);

                $.ajax({
                    contentType: 'application/json; charset=utf-8',
                    dataType: 'json',
                    url: '/Configuracao/GrupoMedicoes_Salvar',
                    data: data2,
                    type: 'POST',
                    success: function (data) {

                        // fim
                        $('.overlay_aguarde').toggle();

                        setTimeout(function () {

                            // verifica se erro
                            if (data.status == "ERRO") {
                                swal({
                                    html: true,
                                    title: "Erro",
                                    text: data.erro,
                                    type: "warning",
                                    confirmButtonColor: "#f8ac59",
                                    confirmButtonText: "Fechar",
                                }, function () {

                                    // redireciona para pagina lista de grupo de medicoes
                                    var url = '/Configuracao/GrupoMedicoes?IDCliente=' + document.getElementById('IDCliente').value;
                                    window.location.href = url;
                                });
                            }
                            else {
                                swal({
                                    title: "Salvo com sucesso",
                                    type: "success",
                                    confirmButtonColor: "#18a689",
                                    confirmButtonText: "Fechar",
                                    closeOnConfirm: true
                                }, function () {

                                    // redireciona para pagina lista de grupo de medicoes
                                    var url = '/Configuracao/GrupoMedicoes?IDCliente=' + document.getElementById('IDCliente').value;
                                    window.location.href = url;
                                });
                            }

                        }, 100);

                    },
                    error: function (xhr, status, error) {

                        // fim
                        $('.overlay_aguarde').toggle();

                        setTimeout(function () {

                            swal({
                                title: "Erro",
                                text: "Erro ao salvar!",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            });

                        }, 100);

                    }
                });
            });

        };


    </script>
}
