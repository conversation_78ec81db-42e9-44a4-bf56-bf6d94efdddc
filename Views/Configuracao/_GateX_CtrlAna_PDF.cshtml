﻿@using SmartEnergyLib.SQL

<head>
    
    <title>@SmartEnergy.Resources.ConfiguracaoTexts.ControleAnalogico</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

    <link href="@Server.MapPath("~/Content/bootstrap.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/style.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/Relatorios.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/RelatoriosPDF.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/fonts/font-awesome/css/font-awesome.min.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/fonts/elusive-icons-2.0.0/css/elusive-icons.min.css")" rel="stylesheet" />

    <script type="text/javascript" src="@Server.MapPath("~/Scripts/jquery-2.1.1.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/dataTables/datatables.min.js")" charset="utf-8"></script>

    <style>
        td {
            font-size: 10px;
        }

        th {
            font-size: 10px;
        }

        tr {
            page-break-inside: avoid;
        }

    </style>

</head>

@{
    string arquivo = "~/Logos/" + ViewBag._LogoConsultor;
    string logo = Server.MapPath(arquivo);
}

<body>

    @{
        List<GateX_CtrlAna_Dominio> ctrls = ViewBag.CtrlAna;
        List<GateX_CtrlAna_SD_Dominio> ctrlSDs = ViewBag.CtrlAna_SD;

        List<GateX_MedAna_Lista_Dominio> medAna_Lista = ViewBag.medAna_Lista;
        List<GateX_MedUtil_Lista_Dominio> medUtil_Lista = ViewBag.medUtil_Lista;
        List<GateX_MedCiclo_Lista_Dominio> medCiclo_Lista = ViewBag.medCiclo_Lista;

        List<GateX_SD_Lista_Dominio> saidasDigitais = ViewBag.SaidasDigitais;
    }

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="@logo" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        <p><b>@ViewBag.NomeGateway</b></p>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.ConfiguracaoTexts.ControleAnalogico</h4>
                        <p>@ViewBag.DataTextoAtual</p>
                    </div>
                </div>
            </div>
        </div>
        
        <hr />

        <div class="row">
            <div class="col-lg-12">
                <table class="table table-striped table-bordered table-hover dataTables-ctrl">
                    <thead>
                        <tr>
                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Controle</th>
                            <th>Variável</th>
                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Medicoes</th>
                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.SaidasDigitais</th>
                            <th>Nível de Ligamento</th>
                            <th>Nível de Desligamento</th>
                            <th>Na Falha da Medição</th>
                            <th>Tempo para Falha de Medição (seg)</th>
                            <th>Tempo entre Controle (seg)</th>
                        </tr>
                    </thead>
                    <tbody>

                        @{
                            if (ctrls != null && ctrlSDs != null)
                            {
                                foreach (GateX_CtrlAna_Dominio ctrl in ctrls)
                                {
                                    if (ctrl.Programado)
                                    {
                                        foreach (GateX_CtrlAna_SD_Dominio sd in ctrlSDs)
                                        {
                                            if (sd.Programado)
                                            {
                                                string str_variavel = "---";

                                                switch (ctrl.MedicaoTy)
                                                {
                                                    case 1:
                                                        str_variavel = "Utilidades [Média]";
                                                        break;

                                                    case 2:
                                                        str_variavel = "Utilidades [Totalizado]";
                                                        break;

                                                    case 3:
                                                        str_variavel = "Analógica [Média]";
                                                        break;

                                                    case 4:
                                                        str_variavel = "Ciclômetro [Totalizado]";
                                                        break;
                                                }

                                                string str_medicao = "---";

                                                switch (ctrl.MedicaoTy)
                                                {
                                                    case 1:
                                                    case 2:
                                                        if (medUtil_Lista != null)
                                                        {
                                                            str_medicao = string.Format("[{0:00}] {1}", ctrl.Medicao, medUtil_Lista[ctrl.Medicao].Descricao);
                                                        }
                                                        else
                                                        {
                                                            str_medicao = string.Format("[{0:00}] Medição de Utilidades {1}", ctrl.Medicao, ctrl.Medicao);
                                                        }
                                                        break;

                                                    case 3:
                                                        if (medAna_Lista != null)
                                                        {
                                                            str_medicao = string.Format("[{0:00}] {1}", ctrl.Medicao, medAna_Lista[ctrl.Medicao].Descricao);
                                                        }
                                                        else
                                                        {
                                                            str_medicao = string.Format("[{0:00}] Medição Analógica {1}", ctrl.Medicao, ctrl.Medicao);
                                                        }
                                                        break;

                                                    case 4:
                                                        if (medCiclo_Lista != null)
                                                        {
                                                            str_medicao = string.Format("[{0:00}] {1}", ctrl.Medicao, medCiclo_Lista[ctrl.Medicao].Descricao);
                                                        }
                                                        else
                                                        {
                                                            str_medicao = string.Format("[{0:00}] Medição Ciclômetro {1}", ctrl.Medicao, ctrl.Medicao);
                                                        }

                                                        break;
                                                }

                                                string str_saida = "";
                                                if (saidasDigitais != null)
                                                {
                                                    str_saida = string.Format("[{0}] {1}", sd.NumSaida, saidasDigitais.Find(x => x.NumSaidaGateway == sd.NumSaida).Descricao);
                                                }

                                                string nivel_lig = string.Format("{0:0.00}", sd.AnLig);
                                                string nivel_des = string.Format("{0:0.00}", sd.AnDes);

                                                string acaoFalhMd = "";

                                                switch (ctrl.acaoFalhMd)
                                                {
                                                    case 0:
                                                        acaoFalhMd = "Desligar todas Saídas";
                                                        break;

                                                    case 1:
                                                        acaoFalhMd = "Ligar todas Saídas";
                                                        break;

                                                    case 2:
                                                        acaoFalhMd = "Manter estado das Saídas";
                                                        break;
                                                }

                                                <tr>
                                                    <td>@ctrl.NumCtrlGateway.ToString()</td>
                                                    <td>@str_variavel</td>
                                                    <td>@str_medicao</td>
                                                    <td>@str_saida</td>
                                                    <td>@nivel_lig</td>
                                                    <td>@nivel_des</td>
                                                    <td>@acaoFalhMd</td>
                                                    <td>@(ctrl.TempoFalhMd * 10)</td>
                                                    <td>@(ctrl.TempoEntrCt * 10)</td>
                                                </tr>
                                            }
                                        }
                                    }
                                }
                            }
                        }

                    </tbody>
                </table>



            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-offset-12" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>

<script type="text/javascript">

    $(document).ready(function () {


        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {
                var x = (a == "---") ? -1 : a.replace(/,/, ".");
                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-ctrl').DataTable({
            "iDisplayLength": 10000,
            dom: 't',

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "numero" },
                        { "aTargets": [1], "sType": "portugues" },
                        { "aTargets": [2], "sType": "portugues" },
                        { "aTargets": [3], "sType": "portugues" },
                        { "aTargets": [4], "sType": "numero" },
                        { "aTargets": [5], "sType": "numero" },
                        { "aTargets": [6], "sType": "portugues" },
                        { "aTargets": [7], "sType": "numero" },
                        { "aTargets": [8], "sType": "numero" },

            ],

            "aoColumns": [
                { sWidth: "5%" },
                { sWidth: "15%" },
                { sWidth: "10%" },
                { sWidth: "15%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "15%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
            ],
            'order': [0, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

    });

</script>
