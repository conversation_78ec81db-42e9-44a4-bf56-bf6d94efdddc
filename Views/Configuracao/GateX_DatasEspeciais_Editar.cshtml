﻿@model SmartEnergyLib.SQL.GateX_DatasEspeciais_Dominio

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.ConfiguracaoTexts.DatasEspeciais;
}


<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">

                    @using (Html.BeginForm("GateX_DatasEspeciais_Editar", "Configuracao", FormMethod.Post, new { id = "form", role = "form" }))
                    {
                        @Html.Hidden("Programado", Model.Programado)
                        @Html.Hidden("IDGateway", Model.IDGateway)
                        @Html.Hidden("NumDataGateway", Model.NumDataGateway)
                        @Html.Hidden("Dia", Model.Dia)
                        @Html.Hidden("Mes", Model.Mes)
                        @Html.Hidden("Ano", Model.Ano)

                        GatewaysDominio gateway = ViewBag.Gateway;
                       
                        
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="panel panel-success">
                                    <div class="panel-heading">

                                        <h4>Gateway</h4><br />
                                        @gateway.Nome

                                        @{
                                            int IDTipoAcesso = ViewBag._IDTipoAcesso;
                                            if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                            {
                                                <span> [@gateway.IDGateway]</span>
                                            }
                                        }

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="panel panel-title">
                            <div class="panel-heading">
                                <div style="text-align:center;">
                                    <h4>@SmartEnergy.Resources.ConfiguracaoTexts.DatasEspeciais</h4>
                                </div>

                                <div class="pull-left relat-navega">
                                    <h4>
                                        @{
                                            if (Model.NumDataGateway != 0)
                                            {
                                                <a class="link_primeira">
                                                    <i class="fa fa-angle-double-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Primeira></i>
                                                </a>
                                                <a class="link_prog_menos">
                                                    <i class="fa fa-angle-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Anterior></i>
                                                </a>
                                            }

                                            if (Model.NumDataGateway != 9)
                                            {
                                                <a class="link_prog_mais">
                                                    <i class="fa fa-angle-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Proxima></i>
                                                </a>
                                                <a class="link_ultima">
                                                    <i class="fa fa-angle-double-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Ultima></i>
                                                </a>
                                            }
                                        }
                                    </h4>
                                </div>
                            </div>

                            <div class="panel-body">
                                
                                <div class="row">
                                    <div class="form-group col-lg-2">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Numero</label>
                                        @Html.TextBoxFor(model => model.NumDataGateway, new { @class = "form-control", @disabled = "disabled" })
                                    </div>
                                    <div class="col-lg-offset-4 col-lg-6">
                                        <div class="ibox-tools">
                                            <button type="button" class="btn btn-primary" onclick="Programar();" disabled="" id="BotaoProgramar">@SmartEnergy.Resources.ComumTexts.BotaoProgramar</button>
                                            <a href='@("/Configuracao/GateX_DatasEspeciais?IDGateway=" + @Model.IDGateway + "&Origem=2")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                        </div>
                                    </div>
                                </div>

                                <div class="tabs-container">
                                    <ul class="nav nav-tabs">
                                        <li class="active"><a data-toggle="tab" href="#tab-1"><i class="fa fa-cogs"></i>@SmartEnergy.Resources.ConfiguracaoTexts.Geral</a></li>
                                    </ul>
                                    
                                    <div class="tab-content">
                                        <div id="tab-1" class="tab-pane active">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Data</label>
                                                                <div class="input-group date">
                                                                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>@Html.TextBoxFor(model => model.DataTexto, new { @class = "form-control", @maxlength = "10" })
                                                                </div>
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Redirecionar</label>
                                                                @Html.DropDownListFor(model => model.SemanaRedirecionada, new SelectList(ViewBag.listaRedirecionar, "ID", "Descricao", Model.SemanaRedirecionada).OrderBy(x => x.ToString()), new { id = "SemanaRedirecionada", @class = "form-control" })
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                </div>
                
                <div class="overlay_aguarde" style="display: none">
                    <div class="spinner-aguarde">
                        <div class="sk-spinner sk-spinner-wandering-cubes">
                            <div class="sk-spinner sk-spinner-wave">
                                <div class="sk-rect1 text-ligh"></div>
                                <div class="sk-rect2"></div>
                                <div class="sk-rect3"></div>
                                <div class="sk-rect4"></div>
                                <div class="sk-rect5"></div>
                            </div>
                            <div class="sk-rect5"></div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
    @Styles.Render("~/datetimePickerStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

    $(document).ready(function () {

        $('#DataTexto').datetimepicker({
            locale: 'pt-BR',
            format: 'DD/MM/YYYY',
            allowInputToggle: true,
            showTodayButton: true
        });

        //
        // VALIDACAO DOS CAMPOS
        //

        jQuery.validator.addMethod("alphanumeric", function (value, element) {
            return this.optional(element) || /^[\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\&\+\-\_\(\),]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

        jQuery.validator.addMethod("numeric", function (value, element) {
            return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.numeric");

        jQuery.validator.addMethod("time", function (value, element) {
            return this.optional(element) || /^(([0-1]?[0-9])|([2][0-3])):([0-5]?[0-9])(:([0-5]?[0-9]))?$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.horario");

        jQuery.validator.addMethod("multiplo", function (value, element) {
            return this.optional(element) || parseInt(value) % 10 == 0;
        }, 'Favor digitar um valor multiplo de 10 segundos');


        // utilizo regex pois no caso do formato mm:ss o parseInt(value) so testa o valor dos minutos
        jQuery.validator.addMethod("multiplo_time", function (value, element) {
            return this.optional(element) || /^[0-9|:]+?0{1}$/i.test(value);
        }, 'Favor digitar mm:ss multiplo de 10 segundos.');

        // utilizo regex pois no caso do formato mm:ss o parseInt(value) so testa o valor dos minutos
        jQuery.validator.addMethod("descricao", function (value, element) {
            return this.optional(element) || !(/^-$/.test(value));
        }, 'Favor digitar uma descrição válida.');

        $.validator.addMethod('positivo', function (value) {
            return Number(value) >= 0;
        }, 'Favor especificar um endereço.');

        $("#form").validate({
            rules: {

            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            },
        });
            
        // desabilita campos por permissao de usuario
        disableAll();
            
        $('form input').on('keypress', function (e) {
            return e.which !== 13;
        });   
    });

    function disableAll() {

        var permissao = Math.ceil(@ViewBag.Permissao);

        // verifica permissao
        switch (permissao) {
            case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
            case PERMISSOES_GERENTE:            // 1 - permissao de Gerente: ve tudo e escreve parte
            case PERMISSOES_OPERADOR:           // 2 - permissao de Operador: ve tudo e nao pode escrever
            case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
            case PERMISSOES_CONSULTOR:          // 5 - permissao de Consultor
            case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte 
                               
                $("#BotaoProgramar").attr('disabled', false);

                break;

            default:
            case PERMISSOES_PRODUCAO:           // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                $("#BotaoProgramar").attr('disabled', true);

                break;
        }
    }
    
    function Programar() {

        var $form = $('#form');

        // apresenta primeiro tab que tiver erro
        var IsTabValid = true;
        var TabInvalid = 1;

        $(".tab-content").find("div.tab-pane").each(function (index, tab) {
            var id = $(tab).attr("id");
            $('a[href="#' + id + '"]').tab('show');

            IsTabValid = $("#form").valid();

            if (!IsTabValid) {

                TabInvalid = id;
                return false;
            }
        });
        
        // apresenta primeira tab ou com erro
        $('a[href="#tab-' + TabInvalid + '"]').tab('show');

        // verifica se datas especiais sao validas
        if (!IsTabValid) return false;

        // retiro campos desabilitados, pois nao envia via POST
        $(":disabled", $('#form')).removeAttr("disabled");

        $.ajax({
            url: '/Configuracao/GateX_DatasEspeciais_Programar',
            data: $form.serialize(),
            type: 'POST',
            success: function (data) {

                setTimeout(function () {

                    // verifica se erro
                    if (data.status == "ERRO") {
                        swal({
                            title: "Erro",
                            text: data.erro,
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {


                        });
                    }
                    else {

                        // redireciona
                        var IDGateway = document.getElementById('IDGateway').value;
                        var url = '/Configuracao/GateX_DatasEspeciais?IDGateway=' + IDGateway + '&Origem=2';

                        window.location.href = url;
                    }

                }, 100);

            },
            error: function (xhr, status, error) {

                setTimeout(function () {

                    swal({
                        title: "Erro",
                        text: "Erro ao Programar!",
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    }, function () {

                        // redireciona
                        var IDGateway = document.getElementById('IDGateway').value;
                        var url = '/Configuracao/GateX_DatasEspeciais?IDGateway=' + IDGateway + '&Origem=2';

                        window.location.href = url;
                    });
                }, 100);
            },
        });
    };

    $(".link_primeira").off().on("click", function (event) {
        event.stopPropagation();

        // primeira 
        SalvaNavega(0);
    });

    $(".link_ultima").off().on("click", function (event) {
        event.stopPropagation();

        // ultima 
        SalvaNavega(19);
    });

    $(".link_prog_menos").off().on("click", function (event) {
        event.stopPropagation();

        var NumDataGateway = parseInt(document.getElementById("NumDataGateway").value);

        // anterior
        SalvaNavega(NumDataGateway - 1);
    });

    $(".link_prog_mais").off().on("click", function (event) {
        event.stopPropagation();

        var NumDataGateway = parseInt(document.getElementById("NumDataGateway").value);

        // proxima 
        SalvaNavega(NumDataGateway + 1);
    });


    function SalvaNavega(navega) {

        var $form = $('#form');

        // apresenta primeiro tab que tiver erro
        var IsTabValid = true;
        var TabInvalid = 1;

        $(".tab-content").find("div.tab-pane").each(function (index, tab) {
            var id = $(tab).attr("id");
            $('a[href="#' + id + '"]').tab('show');

            IsTabValid = $("#form").valid();

            if (!IsTabValid) {

                TabInvalid = id;
                return false;
            }
        });

        // apresenta primeira tab ou com erro
        $('a[href="#tab-' + TabInvalid + '"]').tab('show');

        // verifica se datas especiais sao validas
        if (!IsTabValid) return false;

        // verifica se datas especiais sao validas
        if (!$form.valid()) return false;

        event.stopPropagation();

        // aguarde
        $('.overlay_aguarde').toggle();

        // retiro campos desabilitados, pois nao envia via POST
        $(":disabled", $('#form')).removeAttr("disabled");

        $.ajax({
            url: '/Configuracao/GateX_DatasEspeciais_Programar',
            data: $form.serialize(),
            type: 'POST',
            success: function (data) {

                setTimeout(function () {

                    // verifica se erro
                    if (data.status == "ERRO") {
                        swal({
                            title: "Erro",
                            type: "warning",
                            text: data.erro,
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // recarrega pagina
                            self.location.reload(true);
                        });
                    }
                    else {
                        // redireciona para data especial desejada
                        var IDGateway = document.getElementById('IDGateway').value;
                        var url = '/Configuracao/GateX_DatasEspeciais_Editar?IDGateway=' + IDGateway + '&NumDataGateway=' + navega;

                        window.location.href = url;
                    }

                }, 100);
            },
            error: function (xhr, status, error) {

                setTimeout(function () {

                    swal({
                        title: "Erro",
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    }, function () {

                        // redireciona para pagina lista de datas especiais
                        var IDGateway = document.getElementById('IDGateway').value;
                        var url = '/Configuracao/GateX_DatasEspeciais?IDGateway=' + IDGateway + '&Origem=2';

                        window.location.href = url;
                    });
                }, 100);
            },
        });
    };

    </script>
}
