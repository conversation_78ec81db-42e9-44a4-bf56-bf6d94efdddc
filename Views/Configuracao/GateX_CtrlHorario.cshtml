﻿@model IEnumerable<SmartEnergyLib.SQL.GateX_CtrlHorario_Dominio>

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = @SmartEnergy.Resources.ConfiguracaoTexts.ControleHorario;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="panel panel-success">
                                <div class="panel-heading">

                                    @{
                                        GatewaysDominio gateway = ViewBag.Gateway;
                                        @Html.Hidden("IDGateway", gateway.IDGateway);
                                    }

                                    <h4>Gateway</h4><br />
                                    @gateway.Nome

                                    @{
                                        int IDTipoAcesso = ViewBag._IDTipoAcesso;

                                        if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                        {
                                            <span> [@gateway.IDGateway]</span>
                                        }
                                    }

                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel panel-title">
                        <div class="panel-heading">
                            <div style="text-align:center;">
                                <h4>@SmartEnergy.Resources.ConfiguracaoTexts.ConfiguracaoCtrlHorario</h4>
                            </div>
                            <div class="pull-left relat-navega">
                                <h4>
                                    <a>
                                        <i class="fa fa-sort-amount-asc" onclick="Ordenar(@gateway.IDGateway);" title="Ordena Lista de Controle Horários de Acordo com Número da Saída configurada"></i>
                                    </a>
                                </h4>
                            </div>
                            <div class="pull-right relat-tools">
                                <h4>
                                    <a id="exportar_arquivos" href="@Url.Action("GateX_CtrlHorario_Print", "Configuracao")" target="_blank"><i class="fa fa-print" data-toggle="tooltip" data-placement="left" title="Imprimir"></i></a>
                                    <a id="exportar_arquivos" data-toggle="modal" href="#modalEMAIL"><i class="fa fa-envelope-o" data-toggle="tooltip" data-placement="left" title="Email"></i></a>
                                    <a id="exportar_arquivos" data-toggle="modal" href="#modalPDF"><i class="fa fa-file-pdf-o" data-toggle="tooltip" data-placement="left" title="PDF"></i></a>
                                    <a id="exportar_arquivos" onclick="gerarXLS(@gateway.IDGateway);"><i class="fa fa-file-excel-o" data-toggle="tooltip" data-placement="left" title="XLS"></i></a>
                                </h4>
                            </div>
                        </div>

                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-offset-6 col-lg-6">
                                    <div class="ibox-tools">
                                        <button type="button" class="btn btn-primary" onclick="Enviar(@gateway.IDGateway);" disabled="" id="BotaoEnviar">@SmartEnergy.Resources.ComumTexts.BotaoEnviar</button>
                                        <a href='@("/Configuracao/GateX_CtrlHorario?IDGateway=" + @gateway.IDGateway.ToString() + "&Origem=1")' class="btn btn-default" style="color: #000000; background-color: lightgrey;">@SmartEnergy.Resources.ComumTexts.BotaoLer</a>
                                        <a href='@("/Configuracao/Gateway_Editar?IDGateway=" + @gateway.IDGateway.ToString() + "#tab-6")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                    </div>
                                </div>
                            </div>
                            
                            <br /><br />

                            <div class="row">
                                <div class="col-lg-12">
                                    <table class="table table-striped table-bordered table-hover dataTables-programacoes">
                                        <thead>
                                            <tr>
                                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.Controle</th>
                                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.SaidasDigitais</th>
                                                <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.HoraLiga</th>
                                                <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.HoraDesliga</th>
                                                <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.DiasSemana</th>
                                                <th></th>
                                            </tr>
                                        </thead>
                                        <tbody>

                                            @foreach (GateX_CtrlHorario_Dominio programacao in Model)
                                            {
                                                string desprogramado = "---";
                                                string dias = "";
                                                string nomeSaida = desprogramado;
                                                string horaLiga = desprogramado;
                                                string horaDesliga = desprogramado;


                                                string nomeSaida_sort = "ZZZ";
                                                string horaLiga_sort = "ZZZ";
                                                string horaDesliga_sort = "ZZZ";
                                                string dias_sort = "ZZZ";

                                                if (programacao.Programado)
                                                {
                                                    List<GateX_SD_Lista_Dominio> saidasDigitais = ViewBag.SaidasDigitais;
                                                    if (saidasDigitais != null)
                                                    {
                                                        foreach (GateX_SD_Lista_Dominio saida in saidasDigitais)
                                                        {
                                                            if (programacao.Saida == saida.NumSaidaGateway)
                                                            {
                                                                nomeSaida = String.Format("[{0:00}] {1}", programacao.Saida, saida.Descricao);
                                                                nomeSaida_sort = String.Format("[{0:00}] {1}", programacao.Saida, saida.Descricao);
                                                            }
                                                        }
                                                    }

                                                    horaLiga = String.Format("{0:00}:{1:00}", programacao.HoraLiga, programacao.MinutoLiga);
                                                    horaLiga_sort = String.Format("{0:00}:{1:00}", programacao.HoraLiga, programacao.MinutoLiga);
                                                    horaDesliga = String.Format("{0:00}:{1:00}", programacao.HoraDesliga, programacao.MinutoDesliga);
                                                    horaDesliga_sort = String.Format("{0:00}:{1:00}", programacao.HoraDesliga, programacao.MinutoDesliga);
                                                }

                                                <tr>
                                                    <td>@programacao.NumCtrlGateway.ToString()</td>
                                                    <td><span style="display:none;">@nomeSaida_sort</span>@nomeSaida</td>
                                                    <td class="some_minidesktop"><span style="display:none;">@horaLiga_sort</span>@horaLiga</td>
                                                    <td class="some_minidesktop"><span style="display:none;">@horaDesliga_sort</span>@horaDesliga</td>

                                                    @{
                                                        if (!programacao.Programado)
                                                        {
                                                            dias = desprogramado;
                                                        }

                                                        else
                                                        {
                                                            if (programacao.DiaSemana_Domingo)
                                                            {
                                                                dias = dias + " Dom ";
                                                            }

                                                            if (programacao.DiaSemana_Segunda)
                                                            {
                                                                dias = dias + " Seg ";
                                                            }

                                                            if (programacao.DiaSemana_Terca)
                                                            {
                                                                dias = dias + " Ter ";
                                                            }

                                                            if (programacao.DiaSemana_Quarta)
                                                            {
                                                                dias = dias + " Qua ";
                                                            }

                                                            if (programacao.DiaSemana_Quinta)
                                                            {
                                                                dias = dias + " Qui ";
                                                            }

                                                            if (programacao.DiaSemana_Sexta)
                                                            {
                                                                dias = dias + " Sex ";
                                                            }

                                                            if (programacao.DiaSemana_Sabado)
                                                            {
                                                                dias = dias + " Sab ";
                                                            }

                                                            if (programacao.DiaSemana_Feriado)
                                                            {   
                                                                dias = dias + " Fer ";
                                                            }

                                                            if (programacao.DiaSemana_Especial)
                                                            {
                                                                dias = dias + " Esp ";
                                                            }

                                                            dias_sort = dias;
                                                        }

                                                    }

                                                    <td class="some_minidesktop"><span style="display:none;">@dias_sort</span>@dias</td>
                                                    <td class="link_preto">
                                                        <a href='@("/Configuracao/GateX_CtrlHorario_Editar?IDGateway=" + @programacao.IDGateway.ToString() + "&NumCtrlGateway=" + @programacao.NumCtrlGateway.ToString())' title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"><i class="fa fa-edit icones"></i></a>

                                                        @if (ViewBag.Permissao != PERMISSOES.OPERADOR)
                                                        {
                                                            <a href="#" onclick="javascript:Excluir(@gateway.IDGateway, @programacao.NumCtrlGateway);" id="BotaoExcluir" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-times icones"></i></a>
                                                        }

                                                    </td>
                                                </tr>
                                            }

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal inmodal animated fadeIn" id="modalEMAIL" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                                <h4 class="modal-title"><i class="fa fa-envelope-o"></i>&nbsp;&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ControleHorario por Email</h4>
                            </div>

                            <form id="formEMAIL" action="/Configuracao/GateX_CtrlHorario_EMAIL" method="get">

                                <div class="modal-body">
                                    <div class="row">
                                        <div class="form-group col-xs-6">
                                            <label class="control-label">&nbsp;Email</label>
                                            <input class="form-control" id="Email" name="Email" type="email" value="@ViewBag._EmailUsuario" />
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="form-group col-xs-10">
                                            <label class="control-label">&nbsp;Assunto</label>
                                            <input class="form-control" id="Assunto" name="Assunto" type="text" value="@SmartEnergy.Resources.ConfiguracaoTexts.ControleHorario" />
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <span class="pull-left" style="font-weight:bold; font-size:14px; margin-top:7px;">Este processo pode demorar mais de um minuto.</span>
                                    <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                    <button type="submit" class="btn btn-primary">Enviar Email</button>
                                </div>

                            </form>
                        </div>
                    </div>
                </div>

                <div class="modal inmodal animated fadeIn" id="modalPDF" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                                <h4 class="modal-title"><i class="fa fa-file-pdf-o"></i>&nbsp;&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ControleHorario em PDF</h4>
                            </div>
                            <div class="modal-body">

                            </div>
                            <div class="modal-footer">
                                <span class="pull-left" style="font-weight:bold; font-size:14px; margin-top:7px;">Este processo pode demorar mais de um minuto.</span>
                                <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                <button type="button" class="btn btn-primary" onclick="gerarPDF();" data-dismiss="modal">Gerar PDF</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="overlay_enviando" style="display: none">
                    <div class="fa fa-upload icone_enviando">
                    </div>
                    <div class="text_enviando some_desktop">
                        <span>@SmartEnergy.Resources.ConfiguracaoTexts.EnviaConfig</span>
                    </div>
                    <div class="spinner-enviando">
                        <div class="sk-spinner sk-spinner-wandering-cubes">
                            <div class="sk-spinner sk-spinner-wave">
                                <div class="sk-rect1 text-ligh"></div>
                                <div class="sk-rect2"></div>
                                <div class="sk-rect3"></div>
                                <div class="sk-rect4"></div>
                                <div class="sk-rect5"></div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/waitingFor")
    @Scripts.Render("~/declaracoes/Constants")
    @Scripts.Render("~/declaracoes/Validates")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        $(document).ready(function () {

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "portugues-pre": function (data) {
                    var a = 'a';
                    var e = 'e';
                    var i = 'i';
                    var o = 'o';
                    var u = 'u';
                    var c = 'c';
                    var special_letters = {
                        "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                        "É": e, "é": e, "Ê": e, "ê": e,
                        "Í": i, "í": i, "Î": i, "î": i,
                        "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                        "Ú": u, "ú": u, "Ü": u, "ü": u,
                        "ç": c, "Ç": c
                    };
                    for (var val in special_letters)
                        data = data.split(val).join(special_letters[val]).toLowerCase();
                    return data;
                },
                "portugues-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },
                "portugues-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "numero-pre": function (a) {
                    var x = (a == "-") ? 0 : a.replace(/,/, ".");
                    return parseFloat(x);
                },

                "numero-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },

                "numero-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            $('.dataTables-programacoes').DataTable({
                "iDisplayLength": 10,
                dom: 'ftp',

                "bAutoWidth": false,
                "aoColumnDefs": [
                            { "aTargets": [0], "sType": "numero" },
                            { "aTargets": [1], "sType": "portugues" },
                            { "aTargets": [2], "sType": "portugues" },
                            { "aTargets": [3], "sType": "portugues" },
                            { "aTargets": [4], "sType": "portugues" },
                            { "aTargets": [5], "bVisible": true, "bSortable": false, "bSearchable": false }
                ],

                "aoColumns": [
                    { sWidth: "15%" },
                    { sWidth: "25%" },
                    { sWidth: "15%" },
                    { sWidth: "15%" },
                    { sWidth: "20%" },
                    { sWidth: "10%" }
                ],
                'order': [0, 'asc'],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }

            });

            // desabilita campos
            disableAll();

            // retornos da solicitacao da configuracao
            var solicitaCtrl = (@ViewBag.solicitaCtrl.ToString().ToLower());

            // verifico se retornou erro
            if (solicitaCtrl == false)
            {
                swal({
                    title: "Erro na recepção da configuração!",
                    text: "Deseja ler a configuração salva no banco de dados?",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#1ab394",
                    confirmButtonText: "Sim",
                    cancelButtonText: "Não",
                    closeOnConfirm: true
                }, function(isConfirm){

                    var IDGateway = parseInt(document.getElementById("IDGateway").value);

                    if (!isConfirm) {
                        // caso usuario nao queira ler do banco de dados retorna para a pagina editar
                        var url = '/Configuracao/Gateway_Editar?IDGateway=' + IDGateway + '#tab-6';
                        window.location.href = url;
                    }
                });
            }

            //
            // VALIDACAO DOS CAMPOS 
            //
            Validator_AddMethods();

            $("#formEMAIL").validate({
                rules: {
                    Assunto: {
                        required: true,
                        alphanumeric: true,
                        minlength: 5,
                        maxlength: 100
                    },
                    Email: {
                        required: true
                    }
                },
                highlight: function (element) {
                    $(element).closest('.control-group').removeClass('success').addClass('error');
                },
                unhighlight: function (element) {
                    $(element).closest('.control-group').addClass('valid success').removeClass('error');
                }
            });

            $("#formEMAIL").submit(function(e) {

                e.preventDefault();
                var $form = $(this);

                // verifica se entradas sao validas
                if(! $form.valid()) return false;

                // opcoes EMAIL
                var EmailUsuario = @Html.Raw(Json.Encode(@ViewBag._EmailUsuario));

                // opcoes EMAIL
                var destino = $('input[name=Email]').val();
                var assunto = $('input[name=Assunto]').val();

                // fecha janela email
                $('#modalEMAIL').modal('hide');

                // mostra tela wait
                waitingDialog.show('Enviando Email... Este processo pode demorar mais de um minuto.');

                $.ajax(
                {
                    type: 'GET',
                    url: '/Configuracao/GateX_CtrlHorario_EMAIL',
                    data: { 'destino': destino, 'assunto': assunto },
                    contentType: "application/json;charset=utf-8",
                    dataType: "json",
                    cache: false,
                    async: true,
                    success: function (data) {

                        // esconde tela wait
                        waitingDialog.hide();

                        swal({
                            title: "Email enviado",
                            text: "Email enviado com sucesso!",
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {
                        });

                    },
                    error: function(response) {

                        // esconde tela wait
                        waitingDialog.hide();

                        swal({
                            title: "Erro",
                            text: "Erro no envio do Email!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {
                        });
                    }
                });
            });
        });

        function disableAll() {

            var permissao = Math.ceil(@ViewBag.Permissao);

            // verifica permissao
            switch (permissao) {
                case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
                case PERMISSOES_GERENTE:            // 1 - permissao de Gerente: ve tudo e escreve parte
                case PERMISSOES_PRODUCAO:           // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)
                case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
                case PERMISSOES_CONSULTOR:          // 5 - permissao de Consultor
                case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte 

                    $("#BotaoEnviar").attr('disabled', false);
                    $("#BotaoOrdenar").attr('disabled', false);
                    break;

                default:
                case PERMISSOES_OPERADOR:   // 2 - permissao de Operador: ve tudo e nao pode escrever

                    break;
            }
        }

        function Enviar(IDGateway) {

            swal({
                title: "Deseja enviar?",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#1ab394",
                confirmButtonText: "Enviar",
                cancelButtonText: "Cancelar",
                closeOnConfirm: true
            }, function () {
                event.stopPropagation();

                // aguarde
                $('.overlay_enviando').toggle();

                // retiro campos desabilitados, pois nao envia via POST
                $(":disabled", $('#form')).removeAttr("disabled");

                $.ajax({
                    url: '/Configuracao/GateX_CtrlHorario_Enviar',
                    data: { 'IDGateway' : IDGateway },
                    type: 'POST',
                    success: function (data) {

                        // fim
                        $('.overlay_enviando').toggle();

                        setTimeout(function () {

                            // verifica se erro
                            if (data.status == "ERRO") {
                                swal({
                                    title: "Erro",
                                    text: data.erro,
                                    type: "warning",
                                    confirmButtonColor: "#f8ac59",
                                    confirmButtonText: "Fechar",
                                }, function () {

                                    // retorna para programacoes
                                    var url = '/Configuracao/GateX_CtrlHorario?IDGateway=' + IDGateway + '&Origem=2';

                                    window.location.href = url;
                                });
                            }
                            else {
                                swal({
                                    title: "Enviado com sucesso",
                                    type: "success",
                                    confirmButtonColor: "#18a689",
                                    confirmButtonText: "Fechar",
                                }, function () {

                                    // retorna para pagina de edicao da gateway
                                    var url = '/Configuracao/Gateway_Editar?IDGateway=' + IDGateway + '#tab-6';

                                    window.location.href = url;

                                });
                            }
                            
                        }, 100);

                    },
                    error: function (xhr, status, error) {

                        // fim
                        $('.overlay_enviando').toggle();

                        setTimeout(function () {

                            swal({
                                title: "Erro",
                                text: "Falha no envio da configuração!",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // retorna para programacoes
                                var url = '/Configuracao/GateX_CtrlHorario?IDGateway=' + IDGateway + '&Origem=2';

                                window.location.href = url;
                            });
                        }, 100);
                    },
                });
            });
        };


        function Excluir(IDGateway, NumCtrlGateway) {
            event.stopPropagation();

            // titulo
            titulo = "Deseja excluir o Controle Horário?<br/>Número " + NumCtrlGateway;

            swal({
                html: true,
                title: titulo,
                text: "Esta operação não poderá ser desfeita.",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#18a689",
                confirmButtonText: "Excluir",
                cancelButtonText: "Cancelar",
                closeOnConfirm: false
            }, function () {

                // excluir
                $.ajax(
                {
                    type: 'GET',
                    url: '/Configuracao/GateX_CtrlHorario_Excluir',
                    data: { 'IDGateway' : IDGateway, 'NumCtrlGateway': NumCtrlGateway },
                    contentType: 'application/html',
                    dataType: 'html',
                    cache: false,
                    async: true,
                    success: function (data) {

                        setTimeout(function () {

                            swal({
                                title: "Excluído com sucesso",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // le do banco de dados para atualizar
                                var url = '/Configuracao/GateX_CtrlHorario?IDGateway=' + IDGateway + '&Origem=2';

                                window.location.href = url;
                            });

                        }, 100);
                    },
                    error: function (response) {

                        setTimeout(function () {

                            swal({
                                title: "Erro",
                                text: "Erro ao excluir!",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            });

                        }, 100);

                    }
                });
            });
        };

        function Ordenar(IDGateway) {


            swal({
                html: true,
                title: "Deseja Ordenar a Lista dos Controles?",
                text: "Esta operação não poderá ser desfeita.",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#18a689",
                confirmButtonText: "Ordenar",
                cancelButtonText: "Cancelar",
                closeOnConfirm: false
            }, function () {

                event.stopPropagation();

                // ordenar
                $.ajax(
                {
                    type: 'GET',
                    url: '/Configuracao/GateX_CtrlHorario_Ordenar',
                    data: { 'IDGateway' : IDGateway },
                    cache: false,
                    async: true,
                    success: function (data) {

                        setTimeout(function () {

                            swal({
                                title: "Ordenou com sucesso",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // le do banco de dados para atualizar
                                var url = '/Configuracao/GateX_CtrlHorario?IDGateway=' + IDGateway + '&Origem=2';

                                window.location.href = url;
                            });

                        }, 100);
                    },
                    error: function (response) {

                        setTimeout(function () {

                            swal({
                                title: "Erro",
                                text: "Erro ao Ordenar!",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            });

                        }, 100);

                    }
                });
            });
        };

        function gerarXLS(IDGateway) {

            // mostra tela wait
            waitingDialog.show('Aguarde, gerando Planilha XLS...');

            $.ajax(
            {
                type: 'GET',
                url: '/Configuracao/GateX_CtrlHorario_XLS',
                data: { 'IDGateway' : IDGateway},
                contentType: "application/json;charset=utf-8",
                dataType: "json",
                cache: false,
                async: true,
                success: function (data) {

                    // esconde tela wait
                    waitingDialog.hide();

                    // inicia download
                    window.location = '/Configuracao/GateX_CtrlHorario_XLS_Download?fileGuid=' + data.FileGuid
                                  + '&filename=' + data.FileName;

                },
                error: function(xhr, status, error) {

                    // esconde tela wait
                    waitingDialog.hide();

                    swal({
                        title: "Erro",
                        text: "Erro ao gerar XLS!",
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    }, function () {
                    });
                }
            });
        }
        
        function gerarPDF() {

            // mostra tela wait
            waitingDialog.show('Gerando PDF... Este processo pode demorar mais de um minuto.');
                
            $.ajax(
            {
                type: 'GET',
                url: '/Configuracao/GateX_CtrlHorario_PDF',
                data: {},
                contentType: "application/json;charset=utf-8",
                dataType: "json",
                cache: false,
                async: true,
                success: function (data) {

                    // esconde tela wait
                    waitingDialog.hide();

                    // monta caminho
                    var diretorio = window.location.origin + '/Temp/' + data.nomeArquivo;

                    // apresenta PDF
                    window.open(diretorio, '_blank');
                },
                error: function (response) {

                    // esconde tela wait
                    waitingDialog.hide();

                    swal({
                        title: "Erro",
                        text: "Erro ao gerar PDF!",
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    }, function () {
                    });
                }
            });
        };

    </script>
}


