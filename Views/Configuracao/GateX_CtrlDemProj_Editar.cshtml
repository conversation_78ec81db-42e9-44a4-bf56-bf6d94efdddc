﻿@model SmartEnergyLib.SQL.GateX_CtrlDemProj_Editar

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.ConfiguracaoTexts.ControleDemProj;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">

                    @using (Html.BeginForm("GateX_CtrlDemProj_Editar", "Configuracao", FormMethod.Post, new { id = "form", role = "form" }))
                    {
                        @Html.Hidden("IDGateway", Model.IDGateway)
                        @Html.Hidden("Programado", Model.Programado)

                        
                        GatewaysDominio gateway = ViewBag.Gateway;
                        List<GateX_CtrlDemProj_Adap_Dominio> ctrlAdaps = ViewBag.CtrlDemProj_Adap;
                        List<GateX_CtrlDemProj_SD_Dominio> ctrlSDs = ViewBag.CtrlDemProj_SD;
                        List<GateX_SD_Lista_Dominio> saidas = ViewBag.SaidasDigitais;
                        List<ListaTiposDominio> listaPrioAdap_Acao = ViewBag.listaPrioAdap_Acao;
                        List<ListaTiposDominio> listaAlmUsuario = ViewBag.listaAlmUsuario;
                        List<ListaTiposDominio> listaPrioAdap_For = ViewBag.listaPrioAdap_For;

                        
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="panel panel-success">
                                    <div class="panel-heading">

                                        <h4>Gateway</h4><br />
                                        @gateway.Nome

                                        @{
                                            int IDTipoAcesso = ViewBag._IDTipoAcesso;

                                            if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                            {
                                                <span> [@gateway.IDGateway]</span>
                                            }
                                        }

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="panel panel-title">
                            <div class="panel-heading">
                                <div style="text-align:center;">
                                    <h4>@SmartEnergy.Resources.ConfiguracaoTexts.ControleDemProj</h4>
                                </div>
                                <div class="pull-left relat-navega">
                                    <h4>
                                        @{
                                            if (Model.NumCtrlGateway != 0)
                                            {
                                                <a class="link_primeira">
                                                    <i class="fa fa-angle-double-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Primeira></i>
                                                </a>
                                                <a class="link_prog_menos">
                                                    <i class="fa fa-angle-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Anterior></i>
                                                </a>
                                            }

                                            if (Model.NumCtrlGateway != 11)
                                            {   
                                                <a class="link_prog_mais">
                                                    <i class="fa fa-angle-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Proxima></i>
                                                </a>
                                                <a class="link_ultima">
                                                    <i class="fa fa-angle-double-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Ultima></i>
                                                </a>
                                            }   
                                        }
                                    </h4>
                                </div>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="form-group col-lg-2">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.NumControle</label>
                                        @Html.TextBoxFor(model => model.NumCtrlGateway, new { @class = "form-control", @disabled = "disabled" })
                                    </div>
                                    <div class="col-lg-offset-4 col-lg-6">
                                        <div class="ibox-tools">
                                            <button type="button" class="btn btn-primary" onclick="Programar();" disabled="" id="BotaoProgramar">@SmartEnergy.Resources.ComumTexts.BotaoProgramar</button>
                                            <a href='@("/Configuracao/GateX_CtrlDemProj?IDGateway=" + @Model.IDGateway + "&Origem=2")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                        </div>
                                    </div>
                                </div>

                                <br />
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="tabs-container">
                                            <ul class="nav nav-tabs">
                                                <li class="active"><a data-toggle="tab" href="#tab-1">Medição</a></li>
                                                <li class="tab-2"><a data-toggle="tab" href="#tab-2">Medição Secundária</a></li>
                                                <li class="tab-3"><a data-toggle="tab" href="#tab-3">Saídas Digitais</a></li>
                                                <li class="tab-4"><a data-toggle="tab" href="#tab-4">Prioridade Adaptativa</a></li>
                                            </ul>
                                            <div class="tab-content">
                                                <div id="tab-1" class="tab-pane active">
                                                    <div class="panel-body">
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Medicao</label>
                                                                @Html.DropDownListFor(model => model.Medicao, new SelectList(ViewBag.listaMedicoes, "ID", "Descricao", Model.Medicao), new { @class = "form-control", @disabled = "disabled" })
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Controle</label>
                                                                @Html.DropDownListFor(model => model.Otimizado, new SelectList(ViewBag.listaCtrlOtimizacao, "ID", "Descricao", Model.Otimizado), new { @class = "form-control", @disabled = "disabled" })
                                                            </div>
                                                        </div>

                                                        <br />
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;Fator de Controle Fora de Ponta (%)</label>
                                                                @Html.TextBoxFor(model => model.DemMaxF, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;Fator de Controle Ponta (%)</label>
                                                                @Html.TextBoxFor(model => model.DemMaxP, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                            </div>
                                                        </div>

                                                        <br />
                                                        <div class="row">
                                                            <div class="col-lg-6">
                                                                <div class="row">
                                                                    <div class="form-group col-lg-12">
                                                                        <label class="control-label">&nbsp;Alarme para manter todas as Saídas LIGADAS</label>
                                                                        @Html.DropDownListFor(model => model.AlarmeTipoAllOn, new SelectList(ViewBag.listaTipoCmdAlarme, "ID", "Descricao", Model.AlarmeTipoAllOn), "Nenhum", new { @onchange = "SelecionouTipoAlarme_on()", @class = "form-control", @disabled = "disabled" })
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-lg-12">

                                                                        <div class="div_usuario_on" style="display: none">
                                                                            @Html.DropDownListFor(model => model.AlarmeAuxAllOn, new SelectList(ViewBag.listaAlmUsuario, "ID", "Descricao", Model.AlarmeAuxAllOn), new { id = "alm_usuario_on", @class = "form-control", @disabled = "disabled" })
                                                                        </div>

                                                                        <div class="div_sistema_on" style="display: none">
                                                                            @Html.DropDownListFor(model => model.AlarmeAuxAllOn, new SelectList(ViewBag.listaTipoAlarmeSistema, "ID", "Descricao", Model.AlarmeAuxAllOn), "Nenhum", new { id = "alm_sistema_on", @class = "form-control", @disabled = "disabled" })
                                                                        </div>

                                                                        <div class="div_faltapulsos_on" style="display: none">
                                                                            @Html.DropDownListFor(model => model.AlarmeAuxAllOn, new SelectList(ViewBag.listaMedicoes, "ID", "Descricao", Model.AlarmeAuxAllOn).OrderBy(x => x.ToString()), new { id = "alm_faltapulsos_on", @class = "form-control", @disabled = "disabled" })
                                                                        </div>

                                                                        <div class="div_ultrap_demanda_on" style="display: none">
                                                                            @Html.DropDownListFor(model => model.AlarmeAuxAllOn, new SelectList(ViewBag.listaTipoCtrPj, "ID", "Descricao", Model.AlarmeAuxAllOn), new { id = "ultrap_demanda_on", @class = "form-control", @disabled = "disabled" })
                                                                        </div>
                                                                    
                                                                        <div class="div_ultrap_ind_on" style="display: none">
                                                                            @Html.DropDownListFor(model => model.AlarmeAuxAllOn, new SelectList(ViewBag.listaTipoCtrFPInd, "ID", "Descricao", Model.AlarmeAuxAllOn), new { id = "ultrap_ind_on", @class = "form-control", @disabled = "disabled" })
                                                                        </div>

                                                                        <div class="div_ultrap_cap_on" style="display: none">
                                                                            @Html.DropDownListFor(model => model.AlarmeAuxAllOn, new SelectList(ViewBag.listaTipoCtrFPCap, "ID", "Descricao", Model.AlarmeAuxAllOn), new { id = "ultrap_cap_on", @class = "form-control", @disabled = "disabled" })
                                                                        </div>

                                                                        <div class="div_comando_superv_on" style="display: none">
                                                                            @Html.DropDownListFor(model => model.AlarmeAuxAllOn, new SelectList(ViewBag.listaSaidas, "ID", "Descricao", Model.AlarmeAuxAllOn).OrderBy(x => x.ToString()), new { id = "comando_superv_on", @class = "form-control", @disabled = "disabled" })
                                                                        </div>

                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div class="col-lg-6">
                                                                <div class="row">
                                                                    <div class="form-group col-lg-12">
                                                                        <label class="control-label">&nbsp;Alarme para manter todas as Saídas DESLIGADAS</label>
                                                                        @Html.DropDownListFor(model => model.AlarmeTipoAllOff, new SelectList(ViewBag.listaTipoCmdAlarme, "ID", "Descricao", Model.AlarmeTipoAllOff), "Nenhum", new { @onchange = "SelecionouTipoAlarme_off()", @class = "form-control", @disabled = "disabled" })
                                                                    </div>
                                                                </div>
                                                                <div class="row">
                                                                    <div class="col-lg-12">

                                                                        <div class="div_usuario_off" style="display: none">
                                                                            @Html.DropDownListFor(model => model.AlarmeAuxAllOff, new SelectList(ViewBag.listaAlmUsuario, "ID", "Descricao", Model.AlarmeAuxAllOff), new { id = "alm_usuario_off", @class = "form-control", @disabled = "disabled" })
                                                                        </div>

                                                                        <div class="div_sistema_off" style="display: none">
                                                                            @Html.DropDownListFor(model => model.AlarmeAuxAllOff, new SelectList(ViewBag.listaTipoAlarmeSistema, "ID", "Descricao", Model.AlarmeAuxAllOff), "Nenhum", new { id = "alm_sistema_off", @class = "form-control", @disabled = "disabled" })
                                                                        </div>

                                                                        <div class="div_faltapulsos_off" style="display: none">
                                                                            @Html.DropDownListFor(model => model.AlarmeAuxAllOff, new SelectList(ViewBag.listaMedicoes, "ID", "Descricao", Model.AlarmeAuxAllOff).OrderBy(x => x.ToString()), new { id = "alm_faltapulsos_off", @class = "form-control", @disabled = "disabled" })
                                                                        </div>

                                                                        <div class="div_ultrap_demanda_off" style="display: none">
                                                                            @Html.DropDownListFor(model => model.AlarmeAuxAllOff, new SelectList(ViewBag.listaTipoCtrPj, "ID", "Descricao", Model.AlarmeAuxAllOff), new { id = "ultrap_demanda_off", @class = "form-control", @disabled = "disabled" })
                                                                        </div>
                                                                    
                                                                        <div class="div_ultrap_ind_off" style="display: none">
                                                                            @Html.DropDownListFor(model => model.AlarmeAuxAllOff, new SelectList(ViewBag.listaTipoCtrFPInd, "ID", "Descricao", Model.AlarmeAuxAllOff), new { id = "ultrap_ind_off", @class = "form-control", @disabled = "disabled" })
                                                                        </div>

                                                                        <div class="div_ultrap_cap_off" style="display: none">
                                                                            @Html.DropDownListFor(model => model.AlarmeAuxAllOff, new SelectList(ViewBag.listaTipoCtrFPCap, "ID", "Descricao", Model.AlarmeAuxAllOff), new { id = "ultrap_cap_off", @class = "form-control", @disabled = "disabled" })
                                                                        </div>

                                                                        <div class="div_comando_superv_off" style="display: none">
                                                                            @Html.DropDownListFor(model => model.AlarmeAuxAllOff, new SelectList(ViewBag.listaSaidas, "ID", "Descricao", Model.AlarmeAuxAllOff).OrderBy(x => x.ToString()), new { id = "comando_superv_off", @class = "form-control", @disabled = "disabled" })
                                                                        </div>

                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div id="tab-2" class="tab-pane">
                                                    <div class="panel-body">
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Medicao Secundária</label>
                                                                @Html.DropDownListFor(model => model.Medicao2, new SelectList(ViewBag.listaMedicoes, "ID", "Descricao", Model.Medicao2), new { @class = "form-control", @disabled = "disabled" })
                                                            </div>
                                                        </div>

                                                        <br />
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;Fator de Controle Fora de Ponta (%)</label>
                                                                @Html.TextBoxFor(model => model.DemMaxF2, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;Fator de Controle Ponta (%)</label>
                                                                @Html.TextBoxFor(model => model.DemMaxP2, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div id="tab-3" class="tab-pane">
                                                    <div class="panel-body">
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;Tempo entre religamento (seg)</label>
                                                                @Html.TextBoxFor(model => model.TempoReliga, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;Quantidade de Saídas ligadas no início</label>
                                                                @Html.TextBoxFor(model => model.QtSaidaIni, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                            </div>
                                                        </div>

                                                        <br />
                                                        <div class="row">
                                                            <div class="form-group col-lg-12">
                                                                <table id="dataTables-saidas" class="table table-striped table-bordered table-hover dataTables-saidas">
                                                                    <thead>
                                                                        <tr>
                                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.SaidaDigital</th>
                                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.SaidaDigital</th>
                                                                            <th>@SmartEnergy.Resources.ConfiguracoesGateX_Texts.Controle</th>
                                                                            <th>Prioridade</th>
                                                                            <th>Potência (kW)</th>
                                                                            <th>Medição</th>
                                                                            <th></th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>

                                                                        @{
                                                                            if (ctrlSDs != null)
                                                                            {
                                                                                foreach (var ctrlSD in ctrlSDs)
                                                                                {
                                                                                    // Nome saída
                                                                                    string str_saida = string.Format("Saída {0}", ctrlSD.NumSaida);
                                                                                    if (saidas != null)
                                                                                    {
                                                                                        str_saida = string.Format("{0}", saidas.Find(x => x.NumSaidaGateway == ctrlSD.NumSaida).Descricao);
                                                                                    }  
                                                            
                                                                                    // Habilitado para o controle 
                                                                                    string str_habilitado = "---";
                                                                                    if (ctrlSD.Programado)
                                                                                    {
                                                                                        str_habilitado = string.Format("{0}", ctrlSD.NumCtrlGateway);
                                                                                    }
                                                            
                                                                                    <tr>
                                                                                        <td>@ctrlSD.NumSaida</td>
                                                                                        <td>@str_saida</td>
                                                                                        <td>@str_habilitado</td>
                                                                                        <td>@string.Format("{0}", ctrlSD.ControlProjPrio)</td>
                                                                                        <td>@string.Format("{0}", ctrlSD.PotenciaCargaProj)</td>
                                                                                        <td>@string.Format("{0}", ctrlSD.MedicaoRetro)</td>
                                                                                        <td class="link_preto">

                                                                                            @if (ctrlSD.NumCtrlGateway == Model.NumCtrlGateway || !ctrlSD.Programado)
                                                                                            {
                                                                                                <a href="#" class="edit-saida"><i class="fa fa-edit icones" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a> <a href="#" class="delete-saida"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>
                                                                                            }

                                                                                        </td>
                                                                                    </tr>
                                                                                }
                                                                            }
                                                                        }

                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div id="tab-4" class="tab-pane">
                                                    <div class="panel-body">
                                                        <div class="row">
                                                            <div class="form-group col-lg-12">
                                                                <table id="dataTables-prioAdap" class="table table-striped table-bordered table-hover dataTables-prioAdap">
                                                                    <thead>
                                                                        <tr>
                                                                            <th>Prioridade</th>
                                                                            <th>Prioridade</th>
                                                                            <th>Ação</th>
                                                                            <th>Ação</th>
                                                                            <th>se</th>
                                                                            <th>se</th>
                                                                            <th>for</th>
                                                                            <th>for</th>
                                                                            <th></th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>

                                                                        @{
                                                                            if (ctrlAdaps != null)
                                                                            {
                                                                                for (int i = 0; i < SGATEX.NUM_PRIO_ADAP; i++)
                                                                                {
                                                                                    GateX_CtrlDemProj_Adap_Dominio adap = ctrlAdaps[i];

                                                                                    string uAlm_str = listaAlmUsuario[adap.UsuarioAlarme].Descricao;
                                                                                    string eAlm_str = listaPrioAdap_For[adap.EstadoAlarme].Descricao;
                                                                                    
                                                                                    if (adap.Comando == 0)
                                                                                    {
                                                                                        uAlm_str = "---";
                                                                                        eAlm_str = "---";
                                                                                    }
                                                                                    
                                                                                    <tr>
                                                                                        <td>@adap.NumPrio</td>
                                                                                        <td>Prioridade @adap.NumPrio</td>
                                                                                        <td>@string.Format("{0}", adap.Comando)</td>
                                                                                        <td>@listaPrioAdap_Acao[adap.Comando].Descricao</td>
                                                                                        <td>@string.Format("{0}", adap.UsuarioAlarme)</td>
                                                                                        <td>@uAlm_str</td>
                                                                                        <td>@string.Format("{0}", adap.EstadoAlarme)</td>
                                                                                        <td>@eAlm_str</td>
                                                                                        <td class="link_preto">

                                                                                            @if (adap.NumCtrlGateway == Model.NumCtrlGateway)
                                                                                            {
                                                                                                <a href="#" class="edit-prioAdap"><i class="fa fa-edit icones" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a> <a href="#" class="delete-prioAdap"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>
                                                                                            }

                                                                                        </td>
                                                                                    </tr>
                                                                                }
                                                                            }
                                                                        }

                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal inmodal animated fadeIn" id="ModalCtrlSD_Editar" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Fechar</span></button>
                                        <h4 class="modal-title"><i class="fa fa-edit"></i>&nbsp;&nbsp;Controle da Saída</h4>
                                    </div>
                                    <div class="modal-body">

                                        <input type="hidden" id="nSd" name="nSd" value="" />

                                        <div class="row">
                                            <div class="col-lg-12">
                                                <div class="panel panel-success">
                                                    <div class="panel-heading">
                                                        <h4 id="numeroSaida">&nbsp;</h4><br />
                                                        <span id="nomeSaida">&nbsp;</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <br />
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                <div class="i-checks" style="margin-top:6px;">
                                                    <input id="HabilitarSaida" name="HabilitarSaida" type="checkbox">&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ConfiguracoesGateX_Texts.HabilitarSaida</span>
                                                </div>
                                            </div>
                                        </div>
                                        <br />
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                <label class="control-label">&nbsp;Prioridade da Carga</label>
                                                <select class="form-control" id="prioCarga" name="prioCarga">
                                                    @{
                                                        List<ListaTiposDominio> listaPrioCarga = ViewBag.listaPrioCarga;
                                                        
                                                        foreach (ListaTiposDominio prio in listaPrioCarga)
                                                        {
                                                            <option value=@prio.ID>@prio.Descricao</option>
                                                        }
                                                    }
                                                </select>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                <label class="control-label">&nbsp;Potência da Carga (kW)</label>
                                                <input class="form-control" id="potCarga" name="potCarga" type="text" value="" />
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Medicao da Carga</label>
                                                <select class="form-control" id="medCarga" name="medCarga">
                                                    @{
                                                        List<ListaTiposDominio> listaMedicoes = ViewBag.listaMedicoes;

                                                        foreach (ListaTiposDominio med in listaMedicoes)
                                                        {
                                                            <option value=@med.ID>@med.Descricao</option>
                                                        }
                                                    }
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                        <button type="button" class="btn btn-primary" onclick="ModalCtrlSD_Alterar();">@SmartEnergy.Resources.ComumTexts.BotaoAtualizar</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    
                        <div class="modal inmodal animated fadeIn" id="ModalPrioAdap_Editar" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Fechar</span></button>
                                        <h4 class="modal-title"><i class="fa fa-edit"></i>&nbsp;&nbsp;Prioridade Adaptativa</h4>
                                    </div>
                                    <div class="modal-body">

                                        <input type="hidden" id="nAdap" name="nAdap" value="" />

                                        <div class="row">
                                            <div class="col-lg-12">
                                                <div class="panel panel-success">
                                                    <div class="panel-heading">
                                                        <h4 id="numeroAdap">&nbsp;</h4><br />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <br />
                                        <div class="row">
                                            <div class="form-group col-lg-12">
                                                <label class="control-label">&nbsp;Ação</label>
                                                <select class="form-control" id="prioAdap_acao" name="prioAdap_acao">
                                                    @{
                                                        foreach (ListaTiposDominio tipo in listaPrioAdap_Acao)
                                                        {
                                                            <option value=@tipo.ID>@tipo.Descricao</option>
                                                        }
                                                    }
                                                </select>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-lg-6">
                                                <label class="control-label">&nbsp;se</label>
                                                <select class="form-control" id="prioAdap_se" name="prioAdap_se">
                                                    @{
                                                        foreach (ListaTiposDominio tipo in listaAlmUsuario)
                                                        {
                                                            <option value=@tipo.ID>@tipo.Descricao</option>
                                                        }
                                                    }
                                                </select>
                                            </div>
                                            <div class="form-group col-lg-6">
                                                <label class="control-label">&nbsp;for</label>
                                                <select class="form-control" id="prioAdap_for" name="prioAdap_for">
                                                    @{
                                                        foreach (ListaTiposDominio tipo in listaPrioAdap_For)
                                                        {
                                                            <option value=@tipo.ID>@tipo.Descricao</option>
                                                        }
                                                    }
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                        <button type="button" class="btn btn-primary" onclick="ModalPrioAdap_Alterar();">@SmartEnergy.Resources.ComumTexts.BotaoAtualizar</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="overlay_aguarde" style="display: none">
                            <div class="spinner-aguarde">
                                <div class="sk-spinner sk-spinner-wandering-cubes">
                                    <div class="sk-spinner sk-spinner-wave">
                                        <div class="sk-rect1 text-ligh"></div>
                                        <div class="sk-rect2"></div>
                                        <div class="sk-rect3"></div>
                                        <div class="sk-rect4"></div>
                                        <div class="sk-rect5"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/iCheck")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        $(document).ready(function () {

            // classe iChecks
            $('.i-checks').iCheck({
                checkboxClass: 'icheckbox_square-green',
                radioClass: 'iradio_square-green',
            }); ''

            //
            // VALIDACAO DOS CAMPOS
            //
            
            jQuery.validator.addMethod("alphanumeric", function (value, element) {
                return this.optional(element) || /^[\'\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\&\+\-\_\(\)," "]+$/i.test(value);
            }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

            jQuery.validator.addMethod("numeric", function (value, element) {
                return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
            }, "@SmartEnergy.Resources.ValidateTexts.numeric");

            jQuery.validator.addMethod('required-one', function (value) {
                return $('.required-one:checked').size() > 0;
            }, "@SmartEnergy.Resources.ValidateTexts.checkbox");

            var checkboxes = $('.required-one');
            var checkbox_names = $.map(checkboxes, function (e, i) { return $(e).attr("name") }).join(" ");

            $("#form").validate({
                groups: { checks: checkbox_names },
                errorPlacement: function (error, element) {
                    if (element.attr("type") == "checkbox")
                        error.insertAfter('.i-checks');
                    else
                        error.insertAfter(element);
                },
                rules: {

                },
                highlight: function (element) {
                    $(element).closest('.control-group').removeClass('success').addClass('error');
                },
                unhighlight: function (element) {
                    $(element).closest('.control-group').addClass('valid success').removeClass('error');
                },
            });


            //// VERIFICA CAMPOS QUE DEVEM OU NAO ESTAR HABILITADOS \\\\
            SelecionouTipoAlarme_on();
            SelecionouTipoAlarme_off();

            // desabilita campos
            disableAll();

            //
            // TABELA SAIDAS
            //

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "portugues-pre": function (data) {
                    var a = 'a';
                    var e = 'e';
                    var i = 'i';
                    var o = 'o';
                    var u = 'u';
                    var c = 'c';
                    var special_letters = {
                        "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                        "É": e, "é": e, "Ê": e, "ê": e,
                        "Í": i, "í": i, "Î": i, "î": i,
                        "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                        "Ú": u, "ú": u, "Ü": u, "ü": u,
                        "ç": c, "Ç": c
                    };
                    for (var val in special_letters)
                        data = data.split(val).join(special_letters[val]).toLowerCase();
                    return data;
                },
                "portugues-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },
                "portugues-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "numero-pre": function (a) {
                    var x = (a == "-") ? 0 : a.replace(/,/, ".");
                    return parseFloat(x);
                },

                "numero-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },

                "numero-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            $('#dataTables-saidas').DataTable({
                "iDisplayLength": 8,
                dom: 'ftp',

                'order': [0, 'asc'],
                "bAutoWidth": false,
                "aoColumns": [
                    { sWidth: "10%" },
                    { sWidth: "40%" },
                    { sWidth: "10%" },
                    { sWidth: "15%" },
                    { sWidth: "15%" },
                    { sWidth: "1%" },
                    { sWidth: "10%" },
                ],

                "columnDefs": [
                    { "aTargets": [0], "sType": "numero" },
                    { "aTargets": [1], "sType": "portugues" },
                    { "aTargets": [2], "sType": "portugues" },
                    { "aTargets": [3], "sType": "numero" },
                    { "aTargets": [4], "sType": "numero" },
                    { "aTargets": [5], "visible": false, "sType": "numero" },
                    { "atargets": [6], "searchable": false, "orderable": false }
                ],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }
            });

            $('#dataTables-prioAdap').DataTable({
                "iDisplayLength": 10,
                dom: 'ftp',

                'order': [0, 'asc'],
                "bAutoWidth": false,
                "aoColumns": [
                    { sWidth: "1%" },
                    { sWidth: "20%" },
                    { sWidth: "1%" },
                    { sWidth: "23%" },
                    { sWidth: "1%" },
                    { sWidth: "23%" },
                    { sWidth: "1%" },
                    { sWidth: "23%" },
                    { sWidth: "10%" },
                ],

                "columnDefs": [
                    { "aTargets": [0], "visible": false, "sType": "numero" },
                    { "aTargets": [1], "sType": "portugues" },
                    { "aTargets": [2], "visible": false, "sType": "numero" },
                    { "aTargets": [3], "sType": "portugues" },
                    { "aTargets": [4], "visible": false, "sType": "numero" },
                    { "aTargets": [5], "sType": "portugues" },
                    { "aTargets": [6], "visible": false, "sType": "numero" },
                    { "aTargets": [7], "sType": "portugues" },
                    { "atargets": [8], "searchable": false, "orderable": false }
                ],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }
            });

            $('form input').on('keypress', function (e) {
                return e.which !== 13;
            });

            // caso for selecionado alguma TAB, aponto para ele
            // utilizado caso retornou pela configuração da empresa para ir na TAB geral
            var url = document.location.toString();

            if (url.match('#')) {
                $('.nav-tabs a[href="#' + url.split('#')[1] + '"]').tab('show');

                setTimeout(function () {
                    window.scrollTo(0, 0);
                }, 100);
            }

        });

        function disableAll() {

            var permissao = Math.ceil(@ViewBag.Permissao);

            // verifica permissao
            switch (permissao) {
                case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
                case PERMISSOES_GERENTE:            // 1 - permissao de Gerente: ve tudo e escreve parte
                case PERMISSOES_OPERADOR:           // 2 - permissao de Operador: ve tudo e nao pode escrever
                case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
                case PERMISSOES_CONSULTOR:          // 5 - permissao de Consultor
                case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte 

                    $("#BotaoProgramar").attr('disabled', false);
                    $("#Medicao").attr('disabled', false);
                    $("#Otimizado").attr('disabled', false);
                    $("#DemMaxP").attr('disabled', false);
                    $("#DemMaxF").attr('disabled', false);
                    $("#AlarmeTipoAllOn").attr('disabled', false);
                    $("#AlarmeTipoAllOff").attr('disabled', false);

                    $("#Medicao2").attr('disabled', false);
                    $("#DemMaxP2").attr('disabled', false);
                    $("#DemMaxF2").attr('disabled', false);

                    $("#TempoReliga").attr('disabled', false);
                    $("#QtSaidaIni").attr('disabled', false);
                    break;

                default:
                case PERMISSOES_PRODUCAO:           // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                    $("#BotaoProgramar").attr('disabled', true);
                    break;
            }
        }


        //
        // ALARME SAIDAS LIGADAS
        //

        function SelecionouTipoAlarme_on() {

            // pega comando selecionado
            var aTy2On = parseInt($("#AlarmeTipoAllOn").val());

            // apresenta campos conforme comando selecionado
            ShowTipoAlarmeAux_on(aTy2On);
        }


        function ShowTipoAlarmeAux_on(aTy2On) {

            switch (aTy2On) {

                default:

                    // habilita lista de alarmes do sistema
                    $('.div_sistema_on').css("display", "block");
                    $("#alm_sistema_on").attr('disabled', true);
                    $("#alm_sistema_on").val("");

                    // desabilita demais listas de alarme
                    $('.div_faltapulsos_on').css("display", "none");
                    $('.div_ultrap_demanda_on').css("display", "none");
                    $('.div_ultrap_ind_on').css("display", "none");
                    $('.div_ultrap_cap_on').css("display", "none");
                    $('.div_comando_superv_on').css("display", "none");
                    $('.div_usuario_on').css("display", "none");

                    break;

                case 0: // sistema

                    // habilita lista de alarmes do sistema
                    $('.div_sistema_on').css("display", "block");
                    $("#alm_sistema_on").attr('disabled', false);

                    // desabilita demais listas de alarme
                    $('.div_faltapulsos_on').css("display", "none");
                    $('.div_ultrap_demanda_on').css("display", "none");
                    $('.div_ultrap_ind_on').css("display", "none");
                    $('.div_ultrap_cap_on').css("display", "none");
                    $('.div_comando_superv_on').css("display", "none");
                    $('.div_usuario_on').css("display", "none");

                    break;

                case 1: // falta pulsos

                    // habilita lista falta de pulso
                    $('.div_faltapulsos_on').css("display", "block");
                    $("#alm_faltapulsos_on").attr('disabled', false);

                    // desabilita demais listas de alarme
                    $('.div_sistema_on').css("display", "none");
                    $('.div_ultrap_demanda_on').css("display", "none");
                    $('.div_ultrap_ind_on').css("display", "none");
                    $('.div_ultrap_cap_on').css("display", "none");
                    $('.div_comando_superv_on').css("display", "none");
                    $('.div_usuario_on').css("display", "none");

                    break;

                case 2: // demanda hi

                    // habilita lista ultrapassagem de demanda
                    $('.div_ultrap_demanda_on').css("display", "block");
                    $("#ultrap_demanda_on").attr('disabled', false);

                    // desabilita demais listas de alarme
                    $('.div_sistema_on').css("display", "none");
                    $('.div_faltapulsos_on').css("display", "none");
                    $('.div_ultrap_ind_on').css("display", "none");
                    $('.div_ultrap_cap_on').css("display", "none");
                    $('.div_comando_superv_on').css("display", "none");
                    $('.div_usuario_on').css("display", "none");

                    break;

                case 3: // indutivo lo

                    // habilita lista ultrapassagem FP indutivo
                    $('.div_ultrap_ind_on').css("display", "block");
                    $("#ultrap_ind_on").attr('disabled', false);

                    // desabilita demais listas de alarme
                    $('.div_sistema_on').css("display", "none");
                    $('.div_faltapulsos_on').css("display", "none");
                    $('.div_ultrap_demanda_on').css("display", "none");
                    $('.div_ultrap_cap_on').css("display", "none");
                    $('.div_comando_superv_on').css("display", "none");
                    $('.div_usuario_on').css("display", "none");

                    break;

                case 4: // capacitivo lo

                    // habilita lista ultrapassagem FP indutivo
                    $('.div_ultrap_cap_on').css("display", "block");
                    $("#ultrap_cap_on").attr('disabled', false);

                    // desabilita demais listas de alarme
                    $('.div_sistema_on').css("display", "none");
                    $('.div_faltapulsos_on').css("display", "none");
                    $('.div_ultrap_demanda_on').css("display", "none");
                    $('.div_ultrap_ind_on').css("display", "none");
                    $('.div_comando_superv_on').css("display", "none");
                    $('.div_usuario_on').css("display", "none");

                    break;

                case 5: // comando superv

                    // habilita lista comando supervisionado
                    $('.div_comando_superv_on').css("display", "block");
                    $("#comando_superv_on").attr('disabled', false);

                    // desabilita demais listas de alarme
                    $('.div_sistema_on').css("display", "none");
                    $('.div_faltapulsos_on').css("display", "none");
                    $('.div_ultrap_demanda_on').css("display", "none");
                    $('.div_ultrap_ind_on').css("display", "none");
                    $('.div_ultrap_cap_on').css("display", "none");
                    $('.div_usuario_on').css("display", "none");

                    break;

                case 255: // nenhum

                    // habilita lista de alarmes do usuário
                    $('.div_usuario_on').css("display", "block");
                    $("#alm_usuario_on").attr('disabled', false);

                    // desabilita demais listas de alarme
                    $('.div_sistema_on').css("display", "none");
                    $('.div_faltapulsos_on').css("display", "none");
                    $('.div_ultrap_demanda_on').css("display", "none");
                    $('.div_ultrap_ind_on').css("display", "none");
                    $('.div_ultrap_cap_on').css("display", "none");
                    $('.div_comando_superv_on').css("display", "none");

                    break;
            }
        }


        //
        // ALARME SAIDAS DESLIGADAS
        //

        function SelecionouTipoAlarme_off() {

            // pega comando selecionado
            var aTy2Of = parseInt($("#AlarmeTipoAllOff").val());

            // apresenta campos conforme comando selecionado
            ShowTipoAlarmeAux_off(aTy2Of);
        }


        function ShowTipoAlarmeAux_off(aTy2Of) {

            switch (aTy2Of) {

                default:

                    // habilita lista de alarmes do sistema
                    $('.div_sistema_off').css("display", "block");
                    $("#alm_sistema_off").attr('disabled', true);
                    $("#alm_sistema_off").val("");

                    // desabilita demais listas de alarme
                    $('.div_faltapulsos_off').css("display", "none");
                    $('.div_ultrap_demanda_off').css("display", "none");
                    $('.div_ultrap_ind_off').css("display", "none");
                    $('.div_ultrap_cap_off').css("display", "none");
                    $('.div_comando_superv_off').css("display", "none");
                    $('.div_usuario_off').css("display", "none");

                    break;

                case 0: // sistema

                    // habilita lista de alarmes do sistema
                    $('.div_sistema_off').css("display", "block");
                    $("#alm_sistema_off").attr('disabled', false);

                    // desabilita demais listas de alarme
                    $('.div_faltapulsos_off').css("display", "none");
                    $('.div_ultrap_demanda_off').css("display", "none");
                    $('.div_ultrap_ind_off').css("display", "none");
                    $('.div_ultrap_cap_off').css("display", "none");
                    $('.div_comando_superv_off').css("display", "none");
                    $('.div_usuario_off').css("display", "none");

                    break;

                case 1: // falta pulsos

                    // habilita lista falta de pulso
                    $('.div_faltapulsos_off').css("display", "block");
                    $("#alm_faltapulsos_off").attr('disabled', false);

                    // desabilita demais listas de alarme
                    $('.div_sistema_off').css("display", "none");
                    $('.div_ultrap_demanda_off').css("display", "none");
                    $('.div_ultrap_ind_off').css("display", "none");
                    $('.div_ultrap_cap_off').css("display", "none");
                    $('.div_comando_superv_off').css("display", "none");
                    $('.div_usuario_off').css("display", "none");

                    break;

                case 2: // demanda hi

                    // habilita lista ultrapassagem de demanda
                    $('.div_ultrap_demanda_off').css("display", "block");
                    $("#ultrap_demanda_off").attr('disabled', false);

                    // desabilita demais listas de alarme
                    $('.div_sistema_off').css("display", "none");
                    $('.div_faltapulsos_off').css("display", "none");
                    $('.div_ultrap_ind_off').css("display", "none");
                    $('.div_ultrap_cap_off').css("display", "none");
                    $('.div_comando_superv_off').css("display", "none");
                    $('.div_usuario_off').css("display", "none");

                    break;

                case 3: // indutivo lo

                    // habilita lista ultrapassagem FP indutivo
                    $('.div_ultrap_ind_off').css("display", "block");
                    $("#ultrap_ind_off").attr('disabled', false);

                    // desabilita demais listas de alarme
                    $('.div_sistema_off').css("display", "none");
                    $('.div_faltapulsos_off').css("display", "none");
                    $('.div_ultrap_demanda_off').css("display", "none");
                    $('.div_ultrap_cap_off').css("display", "none");
                    $('.div_comando_superv_off').css("display", "none");
                    $('.div_usuario_off').css("display", "none");

                    break;

                case 4: // capacitivo lo

                    // habilita lista ultrapassagem FP indutivo
                    $('.div_ultrap_cap_off').css("display", "block");
                    $("#ultrap_cap_off").attr('disabled', false);

                    // desabilita demais listas de alarme
                    $('.div_sistema_off').css("display", "none");
                    $('.div_faltapulsos_off').css("display", "none");
                    $('.div_ultrap_demanda_off').css("display", "none");
                    $('.div_ultrap_ind_off').css("display", "none");
                    $('.div_comando_superv_off').css("display", "none");
                    $('.div_usuario_off').css("display", "none");

                    break;

                case 5: // comando superv

                    // habilita lista comando supervisionado
                    $('.div_comando_superv_off').css("display", "block");
                    $("#comando_superv_off").attr('disabled', false);

                    // desabilita demais listas de alarme
                    $('.div_sistema_off').css("display", "none");
                    $('.div_faltapulsos_off').css("display", "none");
                    $('.div_ultrap_demanda_off').css("display", "none");
                    $('.div_ultrap_ind_off').css("display", "none");
                    $('.div_ultrap_cap_off').css("display", "none");
                    $('.div_usuario_off').css("display", "none");

                    break;

                case 255: // nenhum

                    // habilita lista de alarmes do usuário
                    $('.div_usuario_off').css("display", "block");
                    $("#alm_usuario_off").attr('disabled', false);

                    // desabilita demais listas de alarme
                    $('.div_sistema_off').css("display", "none");
                    $('.div_faltapulsos_off').css("display", "none");
                    $('.div_ultrap_demanda_off').css("display", "none");
                    $('.div_ultrap_ind_off').css("display", "none");
                    $('.div_ultrap_cap_off').css("display", "none");
                    $('.div_comando_superv_off').css("display", "none");

                    break;
            }
        }


        //
        // SAIDAS
        //

        $('#dataTables-saidas').on('click', '.edit-saida', function (e) {
            e.preventDefault();

            // linha
            var row = $(this).closest('tr');

            // editar
            ModalCtrlSD_Editar(row);
        });

        function ModalCtrlSD_Editar(row) {
            event.stopPropagation();

            // le dados da linha
            var data = $('#dataTables-saidas').DataTable().row(row).data();

            // valores da janela
            document.getElementById("nSd").value = data[0];
            document.getElementById("numeroSaida").textContent = "Saída " + data[0];
            document.getElementById("nomeSaida").textContent = data[1];

            if (data[2] == '---') {
                $('#HabilitarSaida').iCheck('uncheck');
            }
            else
            {
                $('#HabilitarSaida').iCheck('check');
            }

            document.getElementById("prioCarga").value = parseInt(data[3]);
            document.getElementById("potCarga").value = data[4];
            document.getElementById("medCarga").value = parseInt(data[5]);

            // apresenta janela
            $('#ModalCtrlSD_Editar').modal('show');

            $($.fn.dataTable.tables(true)).css('width', '100%');
            $($.fn.dataTable.tables(true)).DataTable().columns.adjust().draw();

        }

        function ModalCtrlSD_Alterar() {

            // controle
            var NumCtrlGateway = parseInt(document.getElementById("NumCtrlGateway").value);

            // número da saída
            var nSd_str = document.getElementById("nSd").value;
            var nSd = parseInt(nSd_str);

            // habilitar saida
            const value = $('#HabilitarSaida').iCheck('update')[0].checked;

            if (value) {
                $('#dataTables-saidas').DataTable().cell({ row: nSd, column: 2 }).data(NumCtrlGateway).draw();
            }
            else {
                $('#dataTables-saidas').DataTable().cell({ row: nSd, column: 2 }).data('---').draw();

                document.getElementById("prioCarga").value = '0';
                document.getElementById("potCarga").value = '0';
            }

            // prioridade da carga 
            var prioCarga = document.getElementById("prioCarga").value;
            $('#dataTables-saidas').DataTable().cell({ row: nSd, column: 3 }).data(prioCarga).draw();

            // potencia da carga
            var potCarga = document.getElementById("potCarga").value;
            $('#dataTables-saidas').DataTable().cell({ row: nSd, column: 4 }).data(potCarga).draw();

            // medicao da carga 
            var medCarga = document.getElementById("medCarga").value;
            $('#dataTables-saidas').DataTable().cell({ row: nSd, column: 5 }).data(medCarga).draw();

            // fecha janela
            $('#ModalCtrlSD_Editar').modal('hide');
        }

        $('#dataTables-saidas').on('click', '.delete-saida', function (e) {
            e.preventDefault();

            // linha
            var row = $(this).closest('tr');

            // le dados da linha
            var data = $('#dataTables-saidas').DataTable().row(row).data();
            var nSd = parseInt(data[0]);

            $('#dataTables-saidas').DataTable().cell({ row: nSd, column: 2 }).data('---').draw();
            $('#dataTables-saidas').DataTable().cell({ row: nSd, column: 3 }).data('0').draw();
            $('#dataTables-saidas').DataTable().cell({ row: nSd, column: 4 }).data('0').draw();
            $('#dataTables-saidas').DataTable().cell({ row: nSd, column: 5 }).data('0').draw();

        });


        //
        // PRIORIDADES ADAPTATIVAS
        //

        $('#dataTables-prioAdap').on('click', '.edit-prioAdap', function (e) {
            e.preventDefault();

            // linha
            var row = $(this).closest('tr');

            // editar
            ModalPrioAdap_Editar(row);
        });

        function ModalPrioAdap_Editar(row) {
            event.stopPropagation();

            // le dados da linha
            var data = $('#dataTables-prioAdap').DataTable().row(row).data();

            // valores da janela
            document.getElementById("nAdap").value = data[0];
            document.getElementById("numeroAdap").textContent = "Prioridade " + data[0];

            document.getElementById("prioAdap_acao").value = parseInt(data[2]);
            document.getElementById("prioAdap_se").value = parseInt(data[4]);
            document.getElementById("prioAdap_for").value = parseInt(data[6]);

            // apresenta janela
            $('#ModalPrioAdap_Editar').modal('show');

            $($.fn.dataTable.tables(true)).css('width', '100%');
            $($.fn.dataTable.tables(true)).DataTable().columns.adjust().draw();

        }

        function ModalPrioAdap_Alterar() {

            // controle
            var NumCtrlGateway = parseInt(document.getElementById("NumCtrlGateway").value);

            // número da prioridade
            var nAdap_str = document.getElementById("nAdap").value;
            var nAdap = parseInt(nAdap_str);

            // acao
            var prioAdap_acao = document.getElementById("prioAdap_acao").value;
            $('#dataTables-prioAdap').DataTable().cell({ row: nAdap, column: 2 }).data(prioAdap_acao).draw();

            var prioAdap_acao_str = "Nenhuma Ação";
            switch (parseInt(prioAdap_acao))
            {
                case 0:
                    prioAdap_acao_str = "Nenhuma Ação";
                    break;

                case 1:
                    prioAdap_acao_str = "Aumentar Prioridade";
                    break;

                case 2:
                    prioAdap_acao_str = "Diminuir Prioridade";
                    break;

                case 3:
                    prioAdap_acao_str = "Prioridade Fixa";
                    break;
            }
            $('#dataTables-prioAdap').DataTable().cell({ row: nAdap, column: 3 }).data(prioAdap_acao_str).draw();

            if (prioAdap_acao > 0)
            {
                // se
                var prioAdap_se = document.getElementById("prioAdap_se").value;
                $('#dataTables-prioAdap').DataTable().cell({ row: nAdap, column: 4 }).data(prioAdap_se).draw();

                var prioAdap_se_str = "Alarme de Usuário [" + prioAdap_se + "]";
                $('#dataTables-prioAdap').DataTable().cell({ row: nAdap, column: 5 }).data(prioAdap_se_str).draw();

                // for
                var prioAdap_for = document.getElementById("prioAdap_for").value;
                $('#dataTables-prioAdap').DataTable().cell({ row: nAdap, column: 6 }).data(prioAdap_for).draw();

                var prioAdap_for_str = "Inativo";
                if (prioAdap_for == 1)
                {
                    prioAdap_for_str = "Ativo";
                }
                $('#dataTables-prioAdap').DataTable().cell({ row: nAdap, column: 7 }).data(prioAdap_for_str).draw();

            }
            else
            {
                $('#dataTables-prioAdap').DataTable().cell({ row: nAdap, column: 4 }).data('0').draw();
                $('#dataTables-prioAdap').DataTable().cell({ row: nAdap, column: 5 }).data('---').draw();
                $('#dataTables-prioAdap').DataTable().cell({ row: nAdap, column: 6 }).data('0').draw();
                $('#dataTables-prioAdap').DataTable().cell({ row: nAdap, column: 7 }).data('---').draw();
            }

            // fecha janela
            $('#ModalPrioAdap_Editar').modal('hide');
        }

        $('#dataTables-prioAdap').on('click', '.delete-prioAdap', function (e) {
            e.preventDefault();

            // linha
            var row = $(this).closest('tr');

            // le dados da linha
            var data = $('#dataTables-prioAdap').DataTable().row(row).data();
            var nAdap = parseInt(data[0]);

            $('#dataTables-prioAdap').DataTable().cell({ row: nAdap, column: 2 }).data('0').draw();
            $('#dataTables-prioAdap').DataTable().cell({ row: nAdap, column: 3 }).data('Nenhuma Ação').draw();
            $('#dataTables-prioAdap').DataTable().cell({ row: nAdap, column: 4 }).data('0').draw();
            $('#dataTables-prioAdap').DataTable().cell({ row: nAdap, column: 5 }).data('---').draw();
            $('#dataTables-prioAdap').DataTable().cell({ row: nAdap, column: 6 }).data('0').draw();
            $('#dataTables-prioAdap').DataTable().cell({ row: nAdap, column: 7 }).data('---').draw();

        });


        //
        // NAVEGAÇÃO
        //

        $(".link_primeira").off().on("click", function (event) {
            event.stopPropagation();

            // primeira programacao
            SalvaNavega(0);
        });

        $(".link_ultima").off().on("click", function (event) {
            event.stopPropagation();

            // ultima programacao
            SalvaNavega(11);
        });

        $(".link_prog_menos").off().on("click", function (event) {
            event.stopPropagation();

            var NumCtrlGateway = parseInt(document.getElementById("NumCtrlGateway").value);

            // programacao anterior
            SalvaNavega(NumCtrlGateway - 1);
        });

        $(".link_prog_mais").off().on("click", function (event) {
            event.stopPropagation();

            var NumCtrlGateway = parseInt(document.getElementById("NumCtrlGateway").value);

            // proxima programacao
            SalvaNavega(NumCtrlGateway + 1);
        });


        function SalvaNavega(navega) {

            event.stopPropagation();

            var $form = $('#form');

            // apresenta primeiro tab que tiver erro
            var IsTabValid = true;
            var TabInvalid = 1;

            $(".tab-content").find("div.tab-pane").each(function (index, tab) {
                var id = $(tab).attr("id");
                $('a[href="#' + id + '"]').tab('show');

                IsTabValid = $("#form").valid();

                if (!IsTabValid) {

                    TabInvalid = id;
                    return false;
                }
            });

            // apresenta primeira tab ou com erro
            $('a[href="#tab-' + TabInvalid + '"]').tab('show');

            // verifica se entradas sao validas
            if (!IsTabValid) return false;


            // IDGateway
            var IDGateway = document.getElementById('IDGateway').value;

            // NumCtrlGateway (habilita para salvar)
            var NumCtrlGateway = document.getElementById('NumCtrlGateway').value;
            $("#NumCtrlGateway").attr('disabled', false);

            // lista de saidas
            var oTable = $('#dataTables-saidas').dataTable();
            var rows = oTable.fnSettings().aoData;
            var Programado = false;
            var NumSaida = 0;
            var NumCtrlGatewaySaida = 0;
            var ControlProjPrio = 0;
            var PotenciaCargaProj = 0.0;
            var MedicaoRetro = 0;
            var dataArray = [];

            // percorre tabela
            $.each(rows, function (i, val) {

                // pega número da saída
                NumSaida = parseInt(val._aData[0]);

                // saida habilitada
                if (val._aData[2] == '---') {
                    Programado = false;
                    NumCtrlGatewaySaida = 0;
                }
                else {
                    Programado = true;
                    NumCtrlGatewaySaida = parseInt(val._aData[2]);
                }

                // prioridade da carga
                var valor_str = val._aData[3];
                ControlProjPrio = parseInt(valor_str);

                // potencia da carga
                valor_str = val._aData[4];
                PotenciaCargaProj = parseInt(valor_str);

                // medicao da carga
                valor_str = val._aData[5];
                MedicaoRetro = parseInt(valor_str);

                dataArray.push({
                    "IDGateway": IDGateway,
                    "Programado": Programado,
                    "NumCtrlGateway": NumCtrlGatewaySaida,
                    "NumSaida": NumSaida,
                    "ControlProjPrio": ControlProjPrio,
                    "PotenciaCargaProj": PotenciaCargaProj,
                    "MedicaoRetro": MedicaoRetro,
                });

            });

            // lista de prioridades adaptativas
            var oTable = $('#dataTables-prioAdap').dataTable();
            var rows = oTable.fnSettings().aoData;
            var NumPrio = 0;
            var Comando = 0;
            var UsuarioAlarme = 0;
            var EstadoAlarme = 0;
            var dataPrioAdap = [];

            // percorre tabela
            $.each(rows, function (i, val) {

                // pega número da prioridade
                NumPrio = parseInt(val._aData[0]);

                // acao
                var valor_str = val._aData[2];
                Comando = parseInt(valor_str);

                // se
                valor_str = val._aData[4];
                UsuarioAlarme = parseInt(valor_str);

                // for
                valor_str = val._aData[6];
                EstadoAlarme = parseInt(valor_str);

                dataPrioAdap.push({
                    "IDGateway": IDGateway,
                    "NumCtrlGateway": NumCtrlGateway,
                    "NumPrio": NumPrio,
                    "Comando": Comando,
                    "UsuarioAlarme": UsuarioAlarme,
                    "EstadoAlarme": EstadoAlarme,
                });

            });

            // aguarde
            $('.overlay_aguarde').toggle();

            // retiro campos desabilitados, pois nao envia via POST
            //$(":disabled", $('#form')).removeAttr("disabled");

            // constroi estrutura com FORM e lista de medicoes
            data = { 'ctrlEditar': $form.serializeObject(), 'prioAdaps': dataPrioAdap, 'ctrlSDs': dataArray };
            data2 = JSON.stringify(data);

            $.ajax({
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                url: '/Configuracao/GateX_CtrlDemProj_Programar',
                data: data2,
                type: 'POST',
                success: function (data) {

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {
                            swal({
                                title: "Erro",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // redireciona para pagina lista de programacoes
                                var IDGateway = document.getElementById('IDGateway').value;
                                var url = '/Configuracao/GateX_CtrlDemProj?IDGateway=' + IDGateway + '&Origem=2';

                                window.location.href = url;
                            });
                        }
                        else {
                            // redireciona para programacao desejada
                            var IDGateway = document.getElementById('IDGateway').value;
                            var url = '/Configuracao/GateX_CtrlDemProj_Editar?IDGateway=' + IDGateway + '&NumCtrlGateway=' + navega;

                            window.location.href = url;
                        }

                    }, 100);
                },
                error: function (xhr, status, error) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // redireciona para pagina lista de programacoes
                            var IDGateway = document.getElementById('IDGateway').value;
                            var url = '/Configuracao/GateX_CtrlDemProj?IDGateway=' + IDGateway + '&Origem=2';

                            window.location.href = url;
                        });
                    }, 100);
                },
            });
        };



        //
        // BOTAO PROGRAMAR (SUBMIT)
        //

        $.fn.serializeObject = function () {
            var o = {};
            var a = this.serializeArray();
            $.each(a, function () {
                if (o[this.name] !== undefined) {
                    if (!o[this.name].push) {
                        o[this.name] = [o[this.name]];
                    }
                    o[this.name].push(this.value || '');
                } else {
                    o[this.name] = this.value || '';
                }
            });
            return o;
        };

        function Programar() {

            var $form = $('#form');

            // apresenta primeiro tab que tiver erro
            var IsTabValid = true;
            var TabInvalid = 1;

            $(".tab-content").find("div.tab-pane").each(function (index, tab) {
                var id = $(tab).attr("id");
                $('a[href="#' + id + '"]').tab('show');

                IsTabValid = $("#form").valid();

                if (!IsTabValid) {

                    TabInvalid = id;
                    return false;
                }
            });

            // apresenta primeira tab ou com erro
            $('a[href="#tab-' + TabInvalid + '"]').tab('show');

            // verifica se entradas sao validas
            if (!IsTabValid) return false;


            // IDGateway
            var IDGateway = document.getElementById('IDGateway').value;

            // NumCtrlGateway (habilita para salvar)
            var NumCtrlGateway = document.getElementById('NumCtrlGateway').value;
            $("#NumCtrlGateway").attr('disabled', false);

            // lista de saidas
            var oTable = $('#dataTables-saidas').dataTable();
            var rows = oTable.fnSettings().aoData;
            var Programado = false;
            var NumSaida = 0;
            var NumCtrlGatewaySaida = 0;
            var ControlProjPrio = 0;
            var PotenciaCargaProj = 0.0;
            var MedicaoRetro = 0;
            var dataArray = [];

            // percorre tabela
            $.each(rows, function (i, val) {

                // pega número da saída
                NumSaida = parseInt(val._aData[0]);

                // saida habilitada
                if (val._aData[2] == '---') {
                    Programado = false;
                    NumCtrlGatewaySaida = 0;
                }
                else {
                    Programado = true;
                    NumCtrlGatewaySaida = parseInt(val._aData[2]);
                }

                // prioridade da carga
                var valor_str = val._aData[3];
                ControlProjPrio = parseInt(valor_str);

                // potencia da carga
                valor_str = val._aData[4];
                PotenciaCargaProj = parseInt(valor_str);

                // medicao da carga
                valor_str = val._aData[5];
                MedicaoRetro = parseInt(valor_str);

                dataArray.push({
                    "IDGateway": IDGateway,
                    "Programado": Programado,
                    "NumCtrlGateway": NumCtrlGatewaySaida,
                    "NumSaida": NumSaida,
                    "ControlProjPrio": ControlProjPrio,
                    "PotenciaCargaProj": PotenciaCargaProj,
                    "MedicaoRetro": MedicaoRetro,
                });

            });

            // lista de prioridades adaptativas
            var oTable = $('#dataTables-prioAdap').dataTable();
            var rows = oTable.fnSettings().aoData;
            var NumPrio = 0;
            var Comando = 0;
            var UsuarioAlarme = 0;
            var EstadoAlarme = 0;
            var dataPrioAdap = [];

            // percorre tabela
            $.each(rows, function (i, val) {

                // pega número da prioridade
                NumPrio = parseInt(val._aData[0]);

                // acao
                var valor_str = val._aData[2];
                Comando = parseInt(valor_str);

                // se
                valor_str = val._aData[4];
                UsuarioAlarme = parseInt(valor_str);

                // for
                valor_str = val._aData[6];
                EstadoAlarme = parseInt(valor_str);

                dataPrioAdap.push({
                    "IDGateway": IDGateway,
                    "NumCtrlGateway": NumCtrlGateway,
                    "NumPrio": NumPrio,
                    "Comando": Comando,
                    "UsuarioAlarme": UsuarioAlarme,
                    "EstadoAlarme": EstadoAlarme,
                });

            });

            // retiro campos desabilitados, pois nao envia via POST
            //$(":disabled", $('#form')).removeAttr("disabled");

            // constroi estrutura com FORM e listas
            data = { 'ctrlEditar': $form.serializeObject(), 'prioAdaps': dataPrioAdap, 'ctrlSDs': dataArray };
            data2 = JSON.stringify(data);

            $.ajax({
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                url: '/Configuracao/GateX_CtrlDemProj_Programar',
                data: data2,
                type: 'POST',
                success: function (data) {

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {
                            swal({
                                title: "Erro",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // redireciona para pagina lista de programacoes
                                var IDGateway = document.getElementById('IDGateway').value;
                                var url = '/Configuracao/GateX_CtrlDemProj?IDGateway=' + IDGateway + '&Origem=2';

                                window.location.href = url;
                            });
                        }
                        else {

                            // redireciona para pagina lista de programacoes
                            var IDGateway = document.getElementById('IDGateway').value;
                            var url = '/Configuracao/GateX_CtrlDemProj?IDGateway=' + IDGateway + '&Origem=2';

                            window.location.href = url;

                        }

                    }, 100);

                },
                error: function (xhr, status, error) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao Programar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // redireciona para pagina lista de programacoes
                            var IDGateway = document.getElementById('IDGateway').value;
                            var url = '/Configuracao/GateX_CtrlDemProj?IDGateway=' + IDGateway + '&Origem=2';

                            window.location.href = url;
                        });
                    }, 100);
                },
            });
        };

    </script>
}
