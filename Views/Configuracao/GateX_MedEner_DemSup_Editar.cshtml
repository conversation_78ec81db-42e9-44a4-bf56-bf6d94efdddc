﻿@model SmartEnergyLib.SQL.GateX_MedEner_DemSup_Dominio

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.ConfiguracaoTexts.DemandaSuplementar;
}


<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">

                    @using (Html.BeginForm("GateX_MedEner_DemSup_Editar", "Configuracao", FormMethod.Post, new { id = "form", role = "form" }))
                    {
                        @Html.Hidden("Programado", Model.Programado)
                        @Html.Hidden("NumeroMedEnergia", Model.NumeroMedEnergia)
                        @Html.Hidden("DataInicio", Model.DataInicio)
                        @Html.Hidden("DataFim", Model.DataFim)
                        @Html.Hidden("HoraInicio", Model.HoraInicio)
                        @Html.Hidden("HoraFim", Model.HoraFim)
                        @Html.Hidden("UsaDia", Model.UsaDia)

                                                
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="panel panel-success">
                                    <div class="panel-heading">

                                        @{
                                            GatewaysDominio gateway = ViewBag.Gateway;
                                            @Html.Hidden("IDGateway", gateway.IDGateway);
                                        }

                                        <h4>Gateway</h4><br />
                                        @gateway.Nome

                                        @{
                                            int IDTipoAcesso = ViewBag._IDTipoAcesso;
                                            if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                            {
                                                <span> [@gateway.IDGateway]</span>
                                            }
                                        }

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="panel panel-title">
                            <div class="panel-heading">
                                <div style="text-align:center;">
                                    <h4>@SmartEnergy.Resources.ConfiguracaoTexts.DemandaSuplementar</h4>
                                </div>
                            </div>

                            <div class="panel-body">
                                <div class="row">
                                    <div class="form-group col-lg-2">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.DemandaSuplementar</label>
                                        @Html.TextBoxFor(model => model.numDemSup, new { @class = "form-control", @disabled = "disabled" })
                                    </div>
                                    <div class="form-group col-lg-3">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.MedicaoEnergia</label>
                                        @Html.TextBoxFor(model => model.NomeMedicao, new { @class = "form-control", @disabled = "disabled" })
                                    </div>
                                    <div class="col-lg-offset-1 col-lg-6">
                                        <div class="ibox-tools">
                                            <button type="button" class="btn btn-primary" onclick="Programar();" disabled="" id="BotaoProgramar">@SmartEnergy.Resources.ComumTexts.BotaoProgramar</button>
                                            <a href='@("/Configuracao/GateX_MedEner_Editar?IDGateway=" + @Model.IDGateway + "&NumMedicaoGateway=" + Model.NumeroMedEnergia.ToString() + "#tab-3")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                        </div>
                                    </div>
                                </div>

                                <div class="tabs-container">
                                    <ul class="nav nav-tabs">
                                        <li class="active"><a data-toggle="tab" href="#tab-1"><i class="fa fa-cogs"></i>@SmartEnergy.Resources.ConfiguracaoTexts.Geral</a></li>
                                    </ul>
                                    <div class="tab-content">

                                        <div id="tab-1" class="tab-pane active">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.TipoSuplementar</label>
                                                        @Html.DropDownListFor(model => model.TipoSuplementar, new SelectList(ViewBag.listaTipoSuplementar, "ID", "Descricao", Model.TipoSuplementar), new { @class = "form-control" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Descricao</label>
                                                        @Html.TextBoxFor(model => model.Descricao, new { @class = "form-control", @maxlength = "6" })
                                                    </div>
                                                </div>

                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Periodo</label>
                                                        @Html.DropDownListFor(model => model.PeriodoValido, new SelectList(ViewBag.listaPeriodo, "ID", "Descricao", Model.PeriodoValido), new { @class = "form-control" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.DiasSemana</label><br />
                                                        <div class="i-checks" style="margin-top:6px;">
                                                            @Html.CheckBoxFor(model => model.Dom)&nbsp;&nbsp;<span style="margin-right:24px;">@SmartEnergy.Resources.ComumTexts.dom</span>
                                                            @Html.CheckBoxFor(model => model.Seg)&nbsp;&nbsp;<span style="margin-right:24px;">@SmartEnergy.Resources.ComumTexts.seg</span>
                                                            @Html.CheckBoxFor(model => model.Ter)&nbsp;&nbsp;<span style="margin-right:24px;">@SmartEnergy.Resources.ComumTexts.ter</span>
                                                            @Html.CheckBoxFor(model => model.Qua)&nbsp;&nbsp;<span style="margin-right:24px;">@SmartEnergy.Resources.ComumTexts.qua</span>
                                                            @Html.CheckBoxFor(model => model.Qui)&nbsp;&nbsp;<span style="margin-right:24px;">@SmartEnergy.Resources.ComumTexts.qui</span>
                                                            @Html.CheckBoxFor(model => model.Sex)&nbsp;&nbsp;<span style="margin-right:24px;">@SmartEnergy.Resources.ComumTexts.sex</span>
                                                            @Html.CheckBoxFor(model => model.Sab)&nbsp;&nbsp;<span style="margin-right:24px;">@SmartEnergy.Resources.ComumTexts.sab</span>
                                                            @Html.CheckBoxFor(model => model.Fer)&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ComumTexts.fer</span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Data @SmartEnergy.Resources.ConfiguracaoTexts.Inicio</label>
                                                        <div class="input-group date">
                                                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>@Html.TextBoxFor(model => model.DataInicioTexto, new { @class = "form-control", @maxlength = "10" })
                                                        </div>
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Data @SmartEnergy.Resources.ConfiguracaoTexts.Fim</label>
                                                        <div class="input-group date">
                                                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>@Html.TextBoxFor(model => model.DataFimTexto, new { @class = "form-control", @maxlength = "10" })
                                                        </div>
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Hora @SmartEnergy.Resources.ConfiguracaoTexts.Inicio</label>
                                                        <div class="input-group">
                                                            <span class="input-group-addon"><i class="fa fa-clock-o"></i></span>@Html.TextBoxFor(model => model.HoraInicioTexto, new { @class = "form-control", @maxlength = "5" })
                                                        </div>
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Hora @SmartEnergy.Resources.ConfiguracaoTexts.Fim</label>
                                                        <div class="input-group">
                                                            <span class="input-group-addon"><i class="fa fa-clock-o"></i></span>@Html.TextBoxFor(model => model.HoraFimTexto, new { @class = "form-control", @maxlength = "5" })
                                                        </div>
                                                    </div>
                                                </div> 

                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.DemandaSuplementar (kW)</label>
                                                        @Html.TextBoxFor(model => model.Demanda, new { @class = "form-control" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.FatCarHist (%)</label>
                                                        @Html.TextBoxFor(model => model.FatorCargaHistorico, new { @class = "form-control" })
                                                    </div>
                                                </div>

                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.TarifaDemanda (R$/kW)</label>
                                                        @Html.TextBoxFor(model => model.TarifaDemanda, new { @class = "form-control" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.TarifaConsumo (R$/MWh)</label>
                                                        @Html.TextBoxFor(model => model.TarifaConsumo, new { @class = "form-control" })
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
                
                <div class="overlay_aguarde" style="display: none">
                    <div class="spinner-aguarde">
                        <div class="sk-spinner sk-spinner-wandering-cubes">
                            <div class="sk-spinner sk-spinner-wave">
                                <div class="sk-rect1 text-ligh"></div>
                                <div class="sk-rect2"></div>
                                <div class="sk-rect3"></div>
                                <div class="sk-rect4"></div>
                                <div class="sk-rect5"></div>
                            </div>
                            <div class="sk-rect5"></div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
    @Styles.Render("~/plugins/select2Styles")
    @Styles.Render("~/datetimePickerStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/plugins/iCheck")
    @Scripts.Render("~/plugins/select2")
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

    $(document).ready(function () {

        // classe iChecks
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });

        $('#DataInicioTexto').datetimepicker({
            locale: 'pt-BR',
            format: 'DD/MM/YYYY',
            allowInputToggle: true,
            showTodayButton: true
        });

        $('#DataFimTexto').datetimepicker({
            locale: 'pt-BR',
            format: 'DD/MM/YYYY',
            allowInputToggle: true,
            showTodayButton: true
        });

        $('#HoraInicioTexto').datetimepicker({
            locale: 'pt-BR',
            format: 'HH:mm',
            allowInputToggle: true,
        });

        $('#HoraFimTexto').datetimepicker({
            locale: 'pt-BR',
            format: 'HH:mm',
            allowInputToggle: true,
        });

        //
        // VALIDACAO DOS CAMPOS
        //

        jQuery.validator.addMethod("alphanumeric", function (value, element) {
            return this.optional(element) || /^[\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\&\+\-\_\(\),]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

        jQuery.validator.addMethod("numeric", function (value, element) {
            return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.numeric");

        jQuery.validator.addMethod("inteiro", function (value, element) {
            return this.optional(element) || /^[\\0-9\-]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.numeric");

        jQuery.validator.addMethod("descricao", function (value, element) {
            return this.optional(element) || !(/^-$/.test(value));
        }, 'Favor digitar uma descrição válida.');


        $("#form").validate({
            rules: {
                Descricao: {
                    required: true,
                    alphanumeric: true,
                    descricao: true
                },
                Demanda: {
                    required: true,
                    inteiro: true,
                    min: 0
                },
                FatorCargaHistorico: {
                    required: true,
                    inteiro: true,
                    min: 0
                },
                TarifaDemanda: {
                    required: true,
                    numeric: true
                },
                TarifaConsumo: {
                    required: true,
                    numeric: true
                },
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            },
        });

        // desabilita campos por permissao de usuario
        disableAll();

        $('form input').on('keypress', function (e) {
            return e.which !== 13;
        });

    });

    function disableAll() {

        var permissao = Math.ceil(@ViewBag.Permissao);

        // verifica permissao
        switch (permissao) {
            case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
            case PERMISSOES_GERENTE:            // 1 - permissao de Gerente: ve tudo e escreve parte
            case PERMISSOES_OPERADOR:           // 2 - permissao de Operador: ve tudo e nao pode escrever
            case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
            case PERMISSOES_CONSULTOR:          // 5 - permissao de Consultor
            case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte

                $("#BotaoProgramar").attr('disabled', false);
                break;

            default:
            case PERMISSOES_PRODUCAO:           // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                $("#BotaoProgramar").attr('disabled', true);
                break;
        }
    }

    function Programar() {

        var $form = $("#form");

        // apresenta primeiro tab que tiver erro
        var IsTabValid = true;
        var TabInvalid = 1;

        $(".tab-content").find("div.tab-pane").each(function (index, tab) {
            var id = $(tab).attr("id");

            $('a[href="#' + id + '"]').tab('show');

            IsTabValid = $("#form").valid();

            if (!IsTabValid) {

                TabInvalid = id;
                return false;
            }
        });

        // apresenta primeira tab ou com erro
        $('a[href="#tab-' + TabInvalid + '"]').tab('show');

        // verifica se sao validas
        if (!IsTabValid) return false;

        // retiro campos desabilitados, pois nao envia via POST
        $(":disabled", $('#form')).removeAttr("disabled");

        $.ajax({
            url: '/Configuracao/GateX_MedEner_DemSup_Programar',
            data: $form.serialize(),
            type: 'POST',
            success: function (data) {

                setTimeout(function () {

                    // verifica se erro
                    if (data.status == "ERRO") {
                        swal({
                            title: "Erro",
                            text: data.erro,
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {


                        });
                    }
                    else {

                        // redireciona
                        var IDGateway = document.getElementById('IDGateway').value;
                        var NumMedicaoGateway = document.getElementById("NumeroMedEnergia").value;
                        var url = '/Configuracao/GateX_MedEner_Editar?IDGateway=' + IDGateway + "&NumMedicaoGateway=" + NumMedicaoGateway + '#tab-3';
                        window.location.href = url;
                    }

                }, 100);

            },
            error: function (xhr, status, error) {

                setTimeout(function () {

                    swal({
                        title: "Erro",
                        text: "Erro ao programar!",
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    }, function () {

                        // redireciona
                        var IDGateway = document.getElementById('IDGateway').value;
                        var NumMedicaoGateway = document.getElementById("NumeroMedEnergia").value;
                        var url = '/Configuracao/GateX_MedEner_Editar?IDGateway=' + IDGateway + "&NumMedicaoGateway=" + NumMedicaoGateway + '#tab-3';
                        window.location.href = url;

                    });
                }, 100);
            },
        });
    }

    </script>
}
