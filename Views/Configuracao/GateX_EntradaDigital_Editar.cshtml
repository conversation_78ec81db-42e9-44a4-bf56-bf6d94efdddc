﻿@model SmartEnergyLib.SQL.GateX_ED_Dominio

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoEntradasDigitais;
}
 
<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">

                    @using (Html.BeginForm("GateX_EntradaDigital_Editar", "Configuracao", FormMethod.Post, new { id = "form", role = "form" }))
                    {
                        @Html.Hidden("Programado", Model.Programado)
                        @Html.Hidden("IDGateway", Model.IDGateway)
                        @Html.Hidden("NumEntradaGateway", Model.NumEntradaGateway)

                        @Html.Hidden("Remota", Model.Remota)

                        
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="panel panel-success">
                                    <div class="panel-heading">

                                        @{
                                            GatewaysDominio gateway = ViewBag.Gateway;
                                            @Html.Hidden("IDGateway", gateway.IDGateway);
                                        }

                                        <h4>Gateway</h4><br />
                                        @gateway.Nome

                                        @{
                                            int IDTipoAcesso = ViewBag._IDTipoAcesso;
                                            if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                            {
                                                <span> [@gateway.IDGateway]</span>
                                            }
                                        }

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="panel panel-title">
                            <div class="panel-heading">
                                <div style="text-align:center;">
                                    <h4>@SmartEnergy.Resources.ConfiguracaoTexts.EntradaDigital</h4>
                                </div>

                                <div class="pull-left relat-navega">
                                    <h4>
                                        @{
                                            if (Model.NumEntradaGateway != 0)
                                            {
                                                <a class="link_primeira">
                                                    <i class="fa fa-angle-double-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Primeira></i>
                                                </a>
                                                <a class="link_prog_menos">
                                                    <i class="fa fa-angle-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Anterior></i>
                                                </a>
                                            }

                                            if (Model.NumEntradaGateway != 63)
                                            {
                                                <a class="link_prog_mais">
                                                    <i class="fa fa-angle-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Proxima></i>
                                                </a>
                                                <a class="link_ultima">
                                                    <i class="fa fa-angle-double-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Ultima></i>
                                                </a>
                                            }
                                        }
                                    </h4>
                                </div>
                            </div>

                            <div class="panel-body">
                                
                                <div class="row">
                                    <div class="form-group col-lg-2">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.NumEntradaGateway</label>
                                        @Html.TextBoxFor(model => model.NumEntradaGateway, new { @class = "form-control", @disabled = "disabled" })
                                    </div>
                                    <div class="col-lg-offset-4 col-lg-6">
                                        <div class="ibox-tools">
                                            <button type="button" class="btn btn-primary" onclick="Programar();" disabled="" id="BotaoProgramar">@SmartEnergy.Resources.ComumTexts.BotaoProgramar</button>
                                            <a href='@("/Configuracao/GateX_EntradaDigital?IDGateway=" + @Model.IDGateway + "&Origem=2")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                        </div>
                                    </div>
                                </div>

                                <div class="tabs-container">
                                    <ul class="nav nav-tabs">
                                        <li class="active"><a data-toggle="tab" href="#tab-1"><i class="fa fa-cogs"></i>@SmartEnergy.Resources.ConfiguracaoTexts.Geral</a></li>
                                    </ul>
                                    
                                    <div class="tab-content">

                                        <div id="tab-1" class="tab-pane active">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <div class="row">
                                                            <div class="form-group col-lg-5">
                                                                @{
                                                                    string descricao = "-";
    
                                                                    if (Model.Programado)
                                                                    {
                                                                        descricao = Model.Descricao;
                                                                    }
                                                                }
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Descricao</label>
                                                                @Html.TextBoxFor(model => model.Descricao, new { @class = "form-control", @disabled = "disabled", @Value = descricao })
                                                            </div> 
                                                            <div class="form-group col-lg-offset-1 col-lg-2">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Rede</label>
                                                                @Html.DropDownListFor(model => model.RedeIO, new SelectList(ViewBag.listaTipoRedeIO, "ID", "Descricao", Model.RedeIO), new { @class = "form-control", @onchange = "SelecionouRedeIO()", @maxlength = "32", @disabled = "disabled" })
                                                            </div>
                                                            <div class="form-group col-lg-2">
                                                                <div class="div_rede_r_q">
                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Remota</label>
                                                                    @Html.DropDownListFor(model => model.Remota, new SelectList(ViewBag.ListaRemotas), string.Format("Nenhum"), new { id = "remotas_r_q", @onchange = "SelecionouNumRemota()", @class = "form-control", @disabled = "disabled" })
                                                                </div>
                                                                <div class="div_rede_k" style="display: none">
                                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Remota</label>
                                                                    @Html.DropDownListFor(model => model.Remota, new SelectList(ViewBag.listaTipoNumRemotaRedeK, "ID", "Descricao", Model.Remota), new { id = "remotas_k", @onchange = "SelecionouNumRemota()", @class = "form-control", @disabled = "disabled" })
                                                                </div>
                                                            </div>
                                                            <div class="div_drv_ad form-group col-lg-2">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Endereco</label>
                                                                @Html.TextBoxFor(model => model.Endereco, new { @class = "form-control", @disabled = "disabled", @max = "65000" })
                                                            </div>
                                                        </div>

                                                        <hr />
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;Estado Ativo</label>
                                                                @Html.TextBoxFor(model => model.Ativo, new { @class = "form-control", @maxlength = "11", @disabled = "disabled" })
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;Estado Inativo</label>
                                                                @Html.TextBoxFor(model => model.Inativo, new { @class = "form-control", @maxlength = "11", @disabled = "disabled" })
                                                            </div>
                                                        </div>
                                                    
                                                        <hr />
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                <div class="i-checks" style="margin-top:6px;">
                                                                    @Html.CheckBoxFor(model => model.EEAtivo)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracoesGateX_Texts.AtivoUM</span>
                                                                    @Html.CheckBoxFor(model => model.ReconhecerAlarme)&nbsp;&nbsp;<span style="margin-right:54px;">@SmartEnergy.Resources.ConfiguracoesGateX_Texts.ReconhecerAlarmes</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                </div>
                
                <div class="overlay_aguarde" style="display: none">
                    <div class="spinner-aguarde">
                        <div class="sk-spinner sk-spinner-wandering-cubes">
                            <div class="sk-spinner sk-spinner-wave">
                                <div class="sk-rect1 text-ligh"></div>
                                <div class="sk-rect2"></div>
                                <div class="sk-rect3"></div>
                                <div class="sk-rect4"></div>
                                <div class="sk-rect5"></div>
                            </div>
                            <div class="sk-rect5"></div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/plugins/iCheck")
    @Scripts.Render("~/declaracoes/Constants")
    @Scripts.Render("~/declaracoes/Validates")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

    $(document).ready(function () {

        // classe iChecks
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });

        //
        // VALIDACAO DOS CAMPOS
        //
        Validator_AddMethods();

        $("#form").validate({
            rules: {
                Endereco: {
                    required: true,
                    numeric: true,
                    positivo: true,
                },
                Descricao: {
                    alphanumeric: true,
                    required: true,
                    descricao: true,
                },
                Ativo: {
                    alphanumeric: true,
                    required: true,
                    descricao: true,
                },
                Inativo: {
                    alphanumeric: true,
                    required: true,
                    descricao: true,
                },
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            },
        });
            
        //// VERIFICA CAMPOS QUE DEVEM OU NAO ESTAR HABILITADOS \\\\
        SelecionouRemota();
        SelecionouRedeIO();

        // desabilita campos por permissao de usuario
        disableAll();
            
        $('form input').on('keypress', function (e) {
            return e.which !== 13;
        });   
        
    });

    function disableAll() {

        var permissao = Math.ceil(@ViewBag.Permissao);

        // verifica permissao
        switch (permissao) {
            case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
            case PERMISSOES_GERENTE:            // 1 - permissao de Gerente: ve tudo e escreve parte
            case PERMISSOES_OPERADOR:           // 2 - permissao de Operador: ve tudo e nao pode escrever
            case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
            case PERMISSOES_CONSULTOR:          // 5 - permissao de Consultor
            case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte 
                               
                $("#BotaoProgramar").attr('disabled', false);
                $("#Descricao").attr('disabled', false);
                $("#RedeIO").attr('disabled', false);

                $("#Ativo").attr('disabled', false);
                $("#Inativo").attr('disabled', false);

                $("#EEAtivo").attr('disabled', false);
                $("#ReconhecerAlarme").attr('disabled', false);

                break;

            default:
            case PERMISSOES_PRODUCAO:           // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                $("#BotaoProgramar").attr('disabled', true);
                break;
        }
    }
       
    function SelecionouRedeIO() {

        // pega redeIO selecioanada
        var rede_io = parseInt($("#RedeIO").val());

        // executo funcao para verificar se endereco do driver deve ser habilitado ou nao
        SelecionouRemota();

        // apresenta selecao de remotas conforme redeIO
        ShowRemota(rede_io);
    }

    function ShowRemota(RedeIO) {

        switch (RedeIO)
        {

            case 0: // rede R
            case 1: // rede Q

                // habilita rede R e Q
                $('.div_rede_r_q').css("display", "block");
                $("#remotas_r_q").attr('disabled', false);

                // Desabilita rede K
                $('.div_rede_k').css("display", "none");
                $("#remotas_k").attr('disabled', true);

                break;
                //
            case 2: // K

                // habilita rede K
                $('.div_rede_k').css("display", "block");
                $("#remotas_k").attr('disabled', false);

                // desabilita rede R e Q
                $('.div_rede_r_q').css("display", "none");
                $("#remotas_r_q").attr('disabled', true);

                break;

            case 4: // DI (entradas digitais)

                // apresenta campo porem desabilitado
                $('.div_rede_r_q').css("display", "block");
                $("#remotas_r_q").attr('disabled', true);
                $('.div_rede_k').css("display", "none");
                $("#Endereco").attr('disabled', true);

                document.getElementById('remotas_k').value = 0;
                document.getElementById('remotas_r_q').value = 0;
                
                break;
        }

    }

    function SelecionouRemota() {

        // pega redeIO selecioanada
        var rede_io = parseInt($("#redeIO").val());

        // pega numero remota
        var num_remota = 0;

        switch (rede_io)
        {
            case 0:  // Rede R
            case 1:  // Rede Q
                num_remota = parseInt(document.getElementById("remotas_r_q").value);
                break;

            case 2:  // Rede K
                num_remota = parseInt(document.getElementById("remotas_k").value);
                break;

            case 4:  // DI
                document.getElementById("Endereco").value = 0;
                break;
        }

        // habilita ou desabilita campo de endereco do driver
        ShowEnderecoDrv(num_remota, rede_io);
    }

    function ShowEnderecoDrv(Remota, RedeIO) {

        // caso numero de remota nao selecionado desabilita campo de endereco de driver
        if (Remota < 0 || Remota > 31 || Number.isNaN(Remota)) {
            // caso nao escolheu remota deixo input em branco
            document.getElementById('Endereco').value = "";
            $("#Endereco").attr('disabled', true);
        }
        else {
            // verifica se nao sta configurado como remota 'DO'
            if (RedeIO != 4)
            {                
                $("#Endereco").attr('disabled', false);
            }            
        }
    }

    function TrataValores() {

        // trata variavel numero da remota
        var rede_io = parseInt($("#RedeIO").val());
        var num_remota = 0;

        // recebe numero da remota de acordo com redeIO selecionado
        switch (rede_io) {
            case 0:  // Rede R
            case 1:  // Rede Q
                num_remota = parseInt(document.getElementById("remotas_r_q").value);
                break;

            case 2:  // Rede K
                num_remota = parseInt(document.getElementById("remotas_k").value);
                break;

            case 3:  // DO
                num_remota = 0;
                break;
        }

        if (Number.isNaN(num_remota) || num_remota < 0 || num_remota > 31) {
            num_remota = 255;
        }

        document.getElementById('Remota').value = num_remota;
    }

    function Programar() {

        // trata valores das variaveis a serem salvas
        TrataValores();

        var $form = $('#form');

        // apresenta primeiro tab que tiver erro
        var IsTabValid = true;
        var TabInvalid = 1;

        $(".tab-content").find("div.tab-pane").each(function (index, tab) {
            var id = $(tab).attr("id");
            $('a[href="#' + id + '"]').tab('show');

            IsTabValid = $("#form").valid();

            if (!IsTabValid) {

                TabInvalid = id;
                return false;
            }
        });
        
        // apresenta primeira tab ou com erro
        $('a[href="#tab-' + TabInvalid + '"]').tab('show');

        // verifica se entradas sao validas
        if (!IsTabValid) return false;

        // retiro campos desabilitados, pois nao envia via POST
        $(":disabled", $('#form')).removeAttr("disabled");

        $.ajax({
            url: '/Configuracao/GateX_EntradaDigital_Programar',
            data: $form.serialize(),
            type: 'POST',
            success: function (data) {

                setTimeout(function () {

                    // verifica se erro
                    if (data.status == "ERRO") {
                        swal({
                            title: "Erro",
                            text: data.erro,
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // redireciona
                            var IDGateway = document.getElementById('IDGateway').value;
                            var url = '/Configuracao/GateX_EntradaDigital?IDGateway=' + IDGateway + '&Origem=2';

                            window.location.href = url;

                        });
                    }
                    else {

                        // redireciona
                        var IDGateway = document.getElementById('IDGateway').value;
                        var url = '/Configuracao/GateX_EntradaDigital?IDGateway=' + IDGateway + '&Origem=2';

                        window.location.href = url;
                    }

                }, 100);

            },
            error: function (xhr, status, error) {

                setTimeout(function () {

                    swal({
                        title: "Erro",
                        text: "Erro ao Programar!",
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    }, function () {

                        // redireciona
                        var IDGateway = document.getElementById('IDGateway').value;
                        var url = '/Configuracao/GateX_EntradaDigital?IDGateway=' + IDGateway + '&Origem=2';

                        window.location.href = url;
                    });
                }, 100);
            },
        });
    };

    $(".link_primeira").off().on("click", function (event) {
        event.stopPropagation();

        // primeira entradas
        SalvaNavega(0);
    });

    $(".link_ultima").off().on("click", function (event) {
        event.stopPropagation();

        // ultima entrada
        SalvaNavega(63);
    });

    $(".link_prog_menos").off().on("click", function (event) {
        event.stopPropagation();

        var NumEntradaGateway = parseInt(document.getElementById("NumEntradaGateway").value);

        // entrada anterior
        SalvaNavega(NumEntradaGateway - 1);
    });

    $(".link_prog_mais").off().on("click", function (event) {
        event.stopPropagation();

        var NumEntradaGateway = parseInt(document.getElementById("NumEntradaGateway").value);

        // proxima entrada
        SalvaNavega(NumEntradaGateway + 1);
    });


    function SalvaNavega(navega) {

        var descricao = String($("#Descricao").val());

        // caso descricao da entrada nao foi alterada nao salvo
        if (descricao == "-") {

            // redireciona para entrada desejada
            var IDGateway = document.getElementById('IDGateway').value;
            var url = '/Configuracao/GateX_EntradaDigital_Editar?IDGateway=' + IDGateway + '&NumEntradaGateway=' + navega;

            window.location.href = url;
        }

            // caso usuario alterou a descricao da entrada salva ao navegar para a proxima desejada
        else {

            // trata valores das variaveis a serem salvas
            TrataValores();

            var $form = $('#form');

            // apresenta primeiro tab que tiver erro
            var IsTabValid = true;
            var TabInvalid = 1;

            $(".tab-content").find("div.tab-pane").each(function (index, tab) {
                var id = $(tab).attr("id");
                $('a[href="#' + id + '"]').tab('show');

                IsTabValid = $("#form").valid();

                if (!IsTabValid) {

                    TabInvalid = id;
                    return false;
                }
            });

            // apresenta primeira tab ou com erro
            $('a[href="#tab-' + TabInvalid + '"]').tab('show');

            // verifica se entradas sao validas
            if (!IsTabValid) return false;

            // verifica se entradas sao validas
            if (!$form.valid()) return false;

            event.stopPropagation();

            // aguarde
            $('.overlay_aguarde').toggle();

            // retiro campos desabilitados, pois nao envia via POST
            $(":disabled", $('#form')).removeAttr("disabled");

            $.ajax({
                url: '/Configuracao/GateX_EntradaDigital_Programar',
                data: $form.serialize(),
                type: 'POST',
                success: function (data) {

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {
                            swal({
                                title: "Erro",
                                type: "warning",
                                text: data.erro,
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // recarrega pagina
                                self.location.reload(true);
                            });
                        }
                        else {
                            // redireciona para entrada desejada
                            var IDGateway = document.getElementById('IDGateway').value;
                            var url = '/Configuracao/GateX_EntradaDigital_Editar?IDGateway=' + IDGateway + '&NumEntradaGateway=' + navega;

                            window.location.href = url;
                        }

                    }, 100);
                },
                error: function (xhr, status, error) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // redireciona para pagina lista de entradas
                            var IDGateway = document.getElementById('IDGateway').value;
                            var url = '/Configuracao/GateX_EntradaDigital?IDGateway=' + IDGateway + '&Origem=2';

                            window.location.href = url;
                        });
                    }, 100);
                },
            });
        }
    };

    </script>
}
