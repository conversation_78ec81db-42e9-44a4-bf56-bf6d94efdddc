﻿@model IEnumerable<SmartEnergyLib.SQL.GatewaysDominio>

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = "Gateways";
}

@Html.Hidden("IDGateway", 0)
@Html.Hidden("NomeGateway", "")
@Html.Hidden("IDClienteDestino", 0)
@Html.Hidden("NomeClienteDestino", "")

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoGatewaysProducao</h4>
                    <div class="pull-right dashboard-tools link_branco" style="margin-top:-8px;">
                        <h4>
                            <a href="#" onclick="javascript:Novo()" id="BotaoAdicionar" title="@SmartEnergy.Resources.ComumTexts.BotaoInserir"><i class="fa fa-plus"></i></a>
                        </h4>
                    </div>
                </div>
                <div class="panel-body">
                    <table class="table table-striped table-bordered table-hover dataTables-gateways">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Gateway</th>
                                <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.TipoGateway</th>
                                <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.Cidade</th>
                                <th class="some_minidesktop">@SmartEnergy.Resources.ConfiguracaoTexts.NumMedicoes</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>

                            @foreach (GatewaysDominio gateway in Model)
                            {
                                // tipo cidade
                                List<CidadesDominio> listacidades = ViewBag.listaTipoCidade;
                                CidadesDominio cidade = listacidades.Find(item => item.IDCidade == gateway.IDCidade);
                                string tipo_cidade = "";
                                if (cidade != null)
                                {
                                    tipo_cidade = cidade.Nome;
                                }
                                
                                // tipo gateway
                                List<ListaTiposDominio> listatipos = ViewBag.listaTipoGateway;
                                ListaTiposDominio tipo = listatipos.Find(item => item.ID == gateway.IDTipoGateway);
                                string tipo_gateway = "";
                                if (tipo != null)
                                {
                                    tipo_gateway = tipo.Descricao;
                                }

                                <tr>
                                    <td>@gateway.IDGateway</td>
                                    <td>@gateway.Nome</td>
                                    <td class ="some_minidesktop">@tipo_gateway</td>
                                    <td class="some_minidesktop">@tipo_cidade</td>
                                    <td class="some_minidesktop">@gateway.NumMedicoes</td>
                                    <td class="link_preto">

                                        @if (ViewBag.Permissao == PERMISSOES.ADMIN || ViewBag.Permissao == PERMISSOES.SUPORTE)
                                        {
                                            <a href="#" onclick="javascript:MoverModal(@gateway.IDGateway,'@gateway.Nome');" id="BotaoMover" title="@SmartEnergy.Resources.ComumTexts.BotaoMover"><i class="fa fa-share-square-o icones"></i></a>
                                        }

                                    </td>
                                </tr>
                            }

                        </tbody>
                    </table>

                    <div class="modal inmodal animated fadeIn" id="ModalMover" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                                    <h4 class="modal-title"><i class="fa fa-check-square-o"></i>&nbsp;&nbsp;Selecione o Cliente Destino</h4>
                                </div>
                                <div class="modal-body">
                                    
                                    <h2>Esta operação irá mover a Gateway selecionada e as suas medições associadas (configuração e histórico) para o cliente abaixo.</h2>
                                    <br />
                                    <h2>Usuários, Dashboards, e outros relacionamentos serão excluídos.</h2>
                                    <br />
                                    <h2>Após mover a Gateway, associe a Gateway no Agente/Filial correto.</h2>
                                    <br />
                                    <h2><b>IMPORTANTE:</b> Verifique se o horário está entre 7 e 9 minutos e se o número de arquivos na Fila de Processamento está crescendo e menor que 500 arquivos.</h2>
                                    <br />

                                    <table id="dataTables-clientes" class="table table-striped table-bordered table-hover dataTables-clientes">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>@SmartEnergy.Resources.UsuarioPerfilTexts.Clientes</th>
                                            </tr>
                                        </thead>
                                        <tbody>

                                            @{
                                                int IDCliente = ViewBag._IDCliente;
                                                
                                                List<ClientesDominio> listaClientes = ViewBag.listaClientes;

                                                foreach (var cliente in listaClientes)
                                                {
                                                    if( cliente.IDCliente != IDCliente )
                                                    {
                                                        <tr>
                                                            <td>@cliente.IDCliente</td>
                                                            <td>@cliente.Fantasia</td>
                                                        </tr>
                                                       
                                                    }
                                                }
                                            }

                                        </tbody>
                                    </table>

                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                    <button id="BotaoMoverModal" disabled type="button" class="btn btn-primary" onclick="javascript:Mover();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoMover</button>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>


</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


<script type="text/javascript">

    $(document).ready(function () {

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {
                var x = (a == "-") ? 0 : a.replace(/,/, ".");
                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-gateways').DataTable({
            "iDisplayLength": 18,
            dom: 'Bftp',
            buttons: [
                {
                    extend: 'excelHtml5',
                    filename: 'Gateways',
                    title: 'Gateways',
                    text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-excel-o" data-toggle="tooltip" data-placement="left" title="Excel"></span>',
                    className: 'btn btn-default btn-xs'
                },
                {
                    extend: 'pdfHtml5',
                    filename: 'Gateways',
                    title: 'Gateways',
                    text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-pdf-o" data-toggle="tooltip" data-placement="left" title="PDF"></span>',
                    className: 'btn btn-default btn-xs'
                },
            ],

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "numero" },
                        { "aTargets": [1], "sType": "portugues" },
                        { "aTargets": [2], "sType": "portugues" },
                        { "aTargets": [3], "sType": "portugues" },
                        { "aTargets": [4], "sType": "portugues" },
                        {
                            "aTargets": [5],
                            "bVisible": true,
                            "bSortable": false,
                            "bSearchable": false
                        },
            ],

            "aoColumns": [
            { sWidth: "5%" },
            { sWidth: "30%" },
            { sWidth: "15%" },
            { sWidth: "20%" },
            { sWidth: "20%" },
            { sWidth: "10%" }
            ],
            'order': [0, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

        //
        // TABELA CLIENTES (MODAL)
        //

        $('.dataTables-clientes').DataTable({
            "iDisplayLength": 10,
            dom: 'ftp',

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "numero" },
                        { "aTargets": [1], "sType": "portugues" },
            ],

            "aoColumns": [
            { sWidth: "20%" },
            { sWidth: "80%" },
            ],
            'order': [1, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

        //
        // TABELA CLIENTES - SELECAO
        //

        $('#dataTables-clientes tbody').on('click', 'tr', function () {

            if ($(this).hasClass('selected')) {

                // retira selecao atual
                $(this).removeClass('selected');

                document.getElementById("BotaoMoverModal").disabled = true;

                // guarda IDClienteDestino
                document.getElementById("IDClienteDestino").value = 0;
                document.getElementById("NomeClienteDestino").value = "";

            }
            else {

                // dataTable
                var table = $('#dataTables-clientes').DataTable();

                // retira selecao atual
                table.$('tr.selected').removeClass('selected');

                // seleciona
                $(this).addClass('selected');

                // pega dados da linha selecionada
                var data = $('#dataTables-clientes').DataTable().row(this).data();
                var id = data[0];
                var nome = data[1];

                document.getElementById("BotaoMoverModal").disabled = false;

                // guarda IDClienteDestino
                document.getElementById("IDClienteDestino").value = id;
                document.getElementById("NomeClienteDestino").value = nome;


            }
        });

        // desabilita campos
        disableAll();
    });

    function disableAll() {

        var permissao = Math.ceil(@ViewBag.Permissao);

        // verifica permissao
        switch (permissao) {
            case PERMISSOES_ADMIN:     // 0 - permissao de Admin: ve e escreve em tudo

                break;

            default:
            case PERMISSOES_GERENTE:    // 1 - permissao de Gerente: ve tudo e escreve parte
            case PERMISSOES_OPERADOR:   // 2 - permissao de Operador: ve tudo e nao pode escrever
            case PERMISSOES_CONSULTOR:  // 5 - permissao de Consultor

                document.getElementById("BotaoAdicionar").style.visibility = "hidden";
                break;

            case PERMISSOES_PRODUCAO:   // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)
            case PERMISSOES_SUPORTE:   // 4 - permissao de Suporte: escreve tudo mas nao pode excluir

                break;
        }
    }

    function Novo() {
        event.stopPropagation();

        // titulo
        titulo = "Deseja criar uma nova Gateway e Medição?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Criar",
            cancelButtonText: "Cancelar",
            closeOnConfirm: false
        }, function () {

            // excluir
            $.ajax(
            {
                type: 'GET',
                url: '/Configuracao/GatewayProducao_Novo',
                data: {},
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        swal({
                            title: "Criado com sucesso",
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // atualiza pagina
                            location.reload();
                        });

                    }, 100);
                },
                error: function (response) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao criar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });
    };


    function MoverModal(IDGateway, NomeGateway) {

        // abre janela
        $('#ModalMover').modal('show');

        // guarda IDGateway
        document.getElementById("IDGateway").value = IDGateway;
        document.getElementById("NomeGateway").value = NomeGateway;

    };

    function Mover() {
        event.stopPropagation();

        // pega IDGateway
        var IDGateway = parseInt(document.getElementById("IDGateway").value);
        var NomeGateway = document.getElementById("NomeGateway").value;

        // pega IDClienteDestino
        var IDClienteDestino = parseInt(document.getElementById("IDClienteDestino").value);
        var NomeClienteDestino = document.getElementById("NomeClienteDestino").value;

        // titulo
        titulo = "Deseja mover a Gateway [" + IDGateway + "] " + NomeGateway + " para o Cliente [" + IDClienteDestino + "] " + NomeClienteDestino + "  ?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Mover",
            cancelButtonText: "Cancelar",
            closeOnConfirm: false
        }, function () {

            // excluir
            $.ajax(
            {
                type: 'GET',
                url: '/Configuracao/GatewayProducao_Mover',
                data: { 'IDGateway': IDGateway, 'IDClienteDestino': IDClienteDestino },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        swal({
                            title: "Movido com sucesso",
                            text: "Lembre-se de associar a Gateway para um Agente/Filial !!!",
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // atualiza pagina
                            location.reload();
                        });

                    }, 100);
                },
                error: function (response) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao mover!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });
    };

</script>
}


