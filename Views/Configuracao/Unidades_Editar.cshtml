﻿@model SmartEnergyLib.SQL.UnidadesDominio

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Funcoes

@{
    ViewBag.Title = @Funcoes_SmartEnergy.Nome_Unidades(ViewBag.Nome_Unidades, SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoUnidades);
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("Unidades_Editar", "Configuracao", FormMethod.Post, new { id = "form", role = "form" }))
            {
                @Html.Hidden("IDCliente", Model.IDCliente)

                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>@Funcoes_SmartEnergy.Nome_Unidades(ViewBag.Nome_Unidades, SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoUnidades)</h4>
                        <div class="pull-right dashboard-tools link_branco" style="margin-top:-8px;">
                            <h4>

                                @{
                                    int IDCliente = ViewBag._IDCliente;

                                    if (IDCliente > 0)
                                    {
                                        <a href='@("/Configuracao/Menu?IDCliente=" + @IDCliente.ToString())' title="Menu @SmartEnergy.Resources.ConfiguracaoTexts.Configuracoes @SmartEnergy.Resources.ConfiguracaoTexts.ClienteAtual"><i class="fa fa-th-large"></i></a>
                                    }
                                }

                            </h4>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="form-group col-lg-2">
                                <label class="control-label">&nbsp;ID</label>

                                @{
                                    if (Model.IDUnidade == 0)
                                    {
                                        @Html.Hidden("IDUnidade", Model.IDUnidade)
                                        @Html.TextBox("Novo", "Nova Unidade", new { @class = "form-control", @disabled = "disabled" })
                                    }
                                    else
                                    {
                                        @Html.TextBoxFor(model => model.IDUnidade, new { @class = "form-control", @disabled = "disabled" })
                                    }
                                }

                            </div>
                            <div class="col-lg-offset-4 col-lg-6">
                                <div class="ibox-tools">
                                    <a href='@("/Configuracao/Unidades?IDCliente=" + @Model.IDCliente)' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                    <button type="button" class="btn btn-primary" id="BotaoSalvar" onclick="Salvar();" disabled="">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                </div>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="tabs-container">
                                    <ul class="nav nav-tabs">
                                        <li class="active"><a data-toggle="tab" href="#tab-1"><i class="fa fa-cogs"></i> @SmartEnergy.Resources.ConfiguracaoTexts.Geral</a></li>
                                    </ul>
                                    <div class="tab-content">
                                        <div id="tab-1" class="tab-pane active">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@Funcoes_SmartEnergy.Nome_GrupoUnidades(ViewBag.Nome_GrupoUnidades, SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoGrupoUnidades)</label>
                                                        @Html.DropDownListFor(model => model.IDGrupoUnidades, new SelectList(ViewBag.listaGrupos, "IDGrupoUnidades", "Nome").OrderBy(x => x.Text),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoGrupoUnidades),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Nome</label>
                                                        @Html.TextBoxFor(model => model.Nome, new { @class = "form-control", @maxlength = "50", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="overlay_aguarde" style="display: none">
                        <div class="spinner-aguarde">
                            <div class="sk-spinner sk-spinner-wandering-cubes">
                                <div class="sk-spinner sk-spinner-wave">
                                    <div class="sk-rect1 text-ligh"></div>
                                    <div class="sk-rect2"></div>
                                    <div class="sk-rect3"></div>
                                    <div class="sk-rect4"></div>
                                    <div class="sk-rect5"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/Content/plugins/dataTables/dataTablesStyles")
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/dataTables")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/plugins/iCheck")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        $(document).ready(function () {

            //
            // VALIDACAO DOS CAMPOS
            //

            jQuery.validator.addMethod("alphanumeric", function (value, element) {
                return this.optional(element) || /^[\.\^\~\´\`áàãâéèêíìóòõôúùçA-Za-z0-9\s&+\-_,()/\\]+$/i.test(value);
            }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

            jQuery.validator.addMethod("numeric", function (value, element) {
                return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
            }, "@SmartEnergy.Resources.ValidateTexts.numeric");

            $("#form").validate({
                rules: {
                    Nome: {
                        required: true,
                        alphanumeric: true,
                        minlength: 3,
                        maxlength: 50
                    },
                    IDGrupoUnidades: {
                        required: true
                    },
                },
                highlight: function (element) {
                    $(element).closest('.control-group').removeClass('success').addClass('error');
                },
                unhighlight: function (element) {
                    $(element).closest('.control-group').addClass('valid success').removeClass('error');
                }
            });

            // desabilita campos
            disableAll();

            $('form input').on('keypress', function (e) {
                return e.which !== 13;
            });

        });

        function disableAll() {

            var permissao = Math.ceil(@ViewBag.Permissao);

            // verifica permissao
            switch (permissao) {
                case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
                case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
                case PERMISSOES_PRODUCAO:           // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)
                case PERMISSOES_CONSULTOR:          // 5 - permissao de Consultor
                case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte

                    $("#BotaoSalvar").attr('disabled', false);
                    $("#IDGrupoUnidades").attr('disabled', false);
                    $("#Nome").attr('disabled', false);
                    break;

                default:
                case PERMISSOES_GERENTE:    // 1 - permissao de Gerente: ve tudo e escreve parte
                case PERMISSOES_OPERADOR:   // 2 - permissao de Operador: ve tudo e nao pode escrever

                    $("#BotaoSalvar").attr('disabled', true);
                    break;
            }
        }

        function Salvar() {

            var $form = $('#form');

            // verifica se entradas sao validas
            if (!$form.valid()) return false;

            swal({
                title: "Deseja salvar?",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#1ab394",
                confirmButtonText: "Salvar",
                cancelButtonText: "Cancelar",
                closeOnConfirm: true
            }, function () {
                event.stopPropagation();

                // aguarde
                $('.overlay_aguarde').toggle();

                // retiro campos desabilitados, pois nao envia via POST
                $(":disabled", $('#form')).removeAttr("disabled");

                $.ajax({
                    url: '/Configuracao/Unidades_Salvar',
                    data: $form.serialize(),
                    type: 'POST',
                    success: function (data) {

                        // fim
                        $('.overlay_aguarde').toggle();

                        setTimeout(function () {

                            // verifica se erro
                            if (data.status == "ERRO") {
                                swal({
                                    title: "Erro",
                                    text: data.erro,
                                    type: "warning",
                                    confirmButtonColor: "#f8ac59",
                                    confirmButtonText: "Fechar",
                                });
                            }
                            else {
                                swal({
                                    title: "Salvo com sucesso",
                                    type: "success",
                                    confirmButtonColor: "#18a689",
                                    confirmButtonText: "Fechar",
                                }, function () {

                                    // redireciona para pagina lista de grupos
                                    var url = '/Configuracao/Unidades?IDCliente=' + document.getElementById('IDCliente').value;
                                    window.location.href = url;
                                });
                            }

                        }, 100);

                    },
                    error: function (xhr, status, error) {

                        // fim
                        $('.overlay_aguarde').toggle();

                        setTimeout(function () {

                            swal({
                                title: "Erro",
                                text: "Erro ao salvar!",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            });

                        }, 100);

                    }
                });
            });
        };

    </script>
}
