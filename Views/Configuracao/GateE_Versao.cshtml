﻿@model SmartEnergyLib.SQL.GateE_Versao_Dominio

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.ConfiguracaoTexts.Versao;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="panel panel-success">
                                <div class="panel-heading">

                                    @{
                                        GatewaysDominio gateway = ViewBag.Gateway;
                                        @Html.Hidden("IDGateway", gateway.IDGateway);
                                    }

                                    <h4>Gateway</h4><br />
                                    @gateway.Nome

                                    @{
                                        int IDTipoAcesso = ViewBag._IDTipoAcesso;

                                        if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                        {
                                            <span> [@gateway.IDGateway]</span>
                                        }
                                    }

                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel panel-title">
                        <div class="panel-heading">
                            <div style="text-align:center;">
                                <h4>@SmartEnergy.Resources.ConfiguracaoTexts.Modelo e @SmartEnergy.Resources.ConfiguracaoTexts.Versao</h4>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-offset-6 col-lg-6">
                                    <div class="ibox-tools">
                                        <a href='@("/Configuracao/Gateway_Editar?IDGateway=" + @gateway.IDGateway.ToString() + "#tab-6")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoFechar</a>
                                    </div>
                                </div>
                            </div>

                            <br />

                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-lg-4">
                                                <div class="form-group">
                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Modelo</label>
                                                    <input type='text' class="form-control" value='@Model.Modelo' disabled>
                                                </div>
                                            </div>
                                            <div class="col-lg-4">
                                                <div class="form-group">
                                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Versao</label>
                                                    <input type='text' class="form-control" value='@Model.Versao' disabled>
                                                </div>
                                            </div>
                                            <div class="col-lg-4">
                                                <div class="form-group">
                                                    <label class="control-label">&nbsp;Driver Rede I/O</label>
                                                    <input type='text' class="form-control" value='@Model.DriverRemota' disabled>
                                                </div>
                                            </div>
                                        </div>

                                        <hr />
                                        <br />

                                        <div class="row">
                                            <div class="col-lg-12">
                                                <label class="control-label">Histórico das Atualizações de Firmware</label>
                                                <table class="table table-striped table-bordered table-hover dataTables-updates">
                                                    <thead>
                                                        <tr>
                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.DataHora</th>
                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Versao anterior</th>
                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Versao atualizada</th>
                                                            <th>Status</th>
                                                            <th>Usuário responsável</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>

                                                        @{
                                                            List<ListaUpdatesDominio> listaUpdates = ViewBag.listaUpdates;

                                                            foreach (ListaUpdatesDominio update in listaUpdates)
                                                            {
                                                                // data e hora atualizacao
                                                                string DataHora = "-";
                                                                string DataHora_Sort = "20000101000000";

                                                                if (update.Inicio != null)
                                                                {
                                                                    DataHora = String.Format("{0:G}", update.Inicio);
                                                                    DataHora_Sort = String.Format("{0:yyyyMMddHHmmss}", update.Inicio);
                                                                }

                                                                <tr>
                                                                    <td><span style="display:none;">@DataHora_Sort</span>@DataHora</td>
                                                                    <td>@update.VersaoGateway_Texto</td>
                                                                    <td>@update.VersaoSolicitada_Texto</td>
                                                                    <td>@update.Status_Texto</td>
                                                                    <td>@update.NomeUsuario</td>
                                                                </tr>
                                                            }
                                                        }

                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
    @Styles.Render("~/plugins/select2Styles")
    @Styles.Render("~/datetimePickerStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/plugins/AlertaBox")
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


<script type="text/javascript">

        $(document).ready(function () {

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "portugues-pre": function (data) {
                    var a = 'a';
                    var e = 'e';
                    var i = 'i';
                    var o = 'o';
                    var u = 'u';
                    var c = 'c';
                    var special_letters = {
                        "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                        "É": e, "é": e, "Ê": e, "ê": e,
                        "Í": i, "í": i, "Î": i, "î": i,
                        "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                        "Ú": u, "ú": u, "Ü": u, "ü": u,
                        "ç": c, "Ç": c
                    };
                    for (var val in special_letters)
                        data = data.split(val).join(special_letters[val]).toLowerCase();
                    return data;
                },
                "portugues-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },
                "portugues-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "numero-pre": function (a) {
                    var x = (a == "-") ? 0 : a.replace(/,/, ".");
                    return parseFloat(x);
                },

                "numero-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },

                "numero-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            $('.dataTables-updates').DataTable({
                "iDisplayLength": 6,
                dom: 'tp',

                "bAutoWidth": false,
                "aoColumnDefs": [
                            { "aTargets": [0], "sType": "portugues" },
                            { "aTargets": [1], "sType": "portugues" },
                            { "aTargets": [2], "sType": "portugues" },
                            { "aTargets": [3], "sType": "portugues" },
                            { "aTargets": [4], "sType": "portugues" },
                ],

                "aoColumns": [
                    { sWidth: "20%" },
                    { sWidth: "20%" },
                    { sWidth: "20%" },
                    { sWidth: "20%" },
                    { sWidth: "20%" }
                ],
                'order': [0, 'desc'],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }

            });

            // retornos da solicitacao da configuracao
            var solicitaProg = (@ViewBag.SolicitaProg.ToString().ToLower());

            // verifico se retornou erro
            if (solicitaProg == false) {

                swal({
                    title: "Erro na recepção da versão!",
                    type: "warning",
                    confirmButtonColor: "#1ab394",
                    confirmButtonText: "Fechar",
                    closeOnConfirm: true
                }, function (isConfirm) {

                    var IDGateway = parseInt(document.getElementById("IDGateway").value);

                    // retorna para a pagina editar
                    var url = '/Configuracao/Gateway_Editar?IDGateway=' + IDGateway + '#tab-6';
                    window.location.href = url;
                });
            }
        });

</script>
}
