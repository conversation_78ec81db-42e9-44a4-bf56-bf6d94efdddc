﻿@model SmartEnergyLib.SQL.GateX_CtrlAlarme_Dominio

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.ConfiguracaoTexts.ControleAlarme;
}
 
<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">

                    @using (Html.BeginForm("GateX_CtrlAlarme_Editar", "Configuracao", FormMethod.Post, new { id = "form", role = "form" }))
                    {
                        @Html.Hidden("Programado", Model.Programado)
                        @Html.Hidden("IDGateway", Model.IDGateway)
                        @Html.Hidden("Medicao", Model.Medicao)

                        GatewaysDominio gateway = ViewBag.Gateway;
                        
                        
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="panel panel-success">
                                    <div class="panel-heading">

                                        <h4>Gateway</h4><br />
                                        @gateway.Nome

                                        @{
                                            int IDTipoAcesso = ViewBag._IDTipoAcesso;
                                            if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                            {
                                                <span> [@gateway.IDGateway]</span>
                                            }
                                        }

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="panel panel-title">
                            <div class="panel-heading">
                                <div style="text-align:center;">
                                    <h4>@SmartEnergy.Resources.ConfiguracaoTexts.ControleAlarme</h4>
                                </div>

                                <div class="pull-left relat-navega">
                                    <h4>
                                        @{
                                            if (Model.NumAlarmeGateway != 0)
                                            {
                                                <a class="link_primeira">
                                                    <i class="fa fa-angle-double-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Primeira></i>
                                                </a>
                                                <a class="link_prog_menos">
                                                    <i class="fa fa-angle-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Anterior></i>
                                                </a>
                                            }

                                            if (Model.NumAlarmeGateway != 99)
                                            {
                                                <a class="link_prog_mais">
                                                    <i class="fa fa-angle-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Proxima></i>
                                                </a>
                                                <a class="link_ultima">
                                                    <i class="fa fa-angle-double-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.ConfiguracaoTexts.Ultima></i>
                                                </a>
                                            }
                                        }
                                    </h4>
                                </div>
                            </div>

                            <div class="panel-body">
                                
                                <div class="row">
                                    <div class="form-group col-lg-2">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ControleAlarme</label>
                                        @Html.TextBoxFor(model => model.NumAlarmeGateway, new { @class = "form-control", @disabled = "disabled" })
                                    </div>
                                    <div class="col-lg-offset-4 col-lg-6">
                                        <div class="ibox-tools">
                                            <button type="button" class="btn btn-primary" onclick="Programar();" disabled="" id="BotaoProgramar">@SmartEnergy.Resources.ComumTexts.BotaoProgramar</button>
                                            <a href='@("/Configuracao/GateX_CtrlAlarme?IDGateway=" + @Model.IDGateway + "&Origem=2")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                        </div>
                                    </div>
                                </div>

                                <div class="tabs-container">
                                    <ul class="nav nav-tabs">
                                        <li class="active"><a data-toggle="tab" href="#tab-1"><i class="fa fa-cogs"></i>@SmartEnergy.Resources.ConfiguracaoTexts.Geral</a></li>
                                    </ul>
                                    
                                    <div class="tab-content">
                                        <div id="tab-1" class="tab-pane active">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ControleAlarme</label>
                                                        @Html.DropDownListFor(model => model.TipoAlarme, new SelectList(ViewBag.listatipoAlarme, "ID", "Descricao", Model.TipoAlarme).OrderBy(x => x.ToString()), new { @class = "form-control", @onchange = "SelecionouTipoAlarme()" })
                                                    </div>
                                                </div>

                                                <div class="div_programado" style="display: none">
                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-6">
                                                            <div class="div_origemED" style="display: none">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.EntradaDigital</label>
                                                                @Html.DropDownListFor(model => model.Medicao, new SelectList(ViewBag.listaEntradas, "NumEntradaGateway", "Descricao", Model.Medicao).OrderBy(x => x.ToString()), new { id = "origemED", @class = "form-control", @disabled = "disabled" })
                                                            </div>
                                                            <div class="div_origemSD" style="display: none">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.SaidaDigital</label>
                                                                @Html.DropDownListFor(model => model.Medicao, new SelectList(ViewBag.listaSaidas, "NumSaidaGateway", "Descricao", Model.Medicao).OrderBy(x => x.ToString()), new { id = "origemSD", @class = "form-control", @disabled = "disabled" })
                                                            </div>
                                                            <div class="div_origemMedEner" style="display: none">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.MedicaoEnergia</label>
                                                                @Html.DropDownListFor(model => model.Medicao, new SelectList(ViewBag.listaMedEner, "NumMedicaoGateway", "Descricao", Model.Medicao).OrderBy(x => x.ToString()), new { id = "origemMedEner", @class = "form-control", @disabled = "disabled" })
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <br />
                                                    <div class="row">
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Normaliza &nbsp;<span id="normalizaUnidade"></span></label>
                                                            @Html.TextBoxFor(model => model.SetPointLo, new { @class = "form-control" })
                                                        </div>
                                                        <div class="form-group col-lg-6">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Ativa &nbsp;<span id="ativaUnidade"></span></label>
                                                            @Html.TextBoxFor(model => model.SetPointHi, new { @class = "form-control" })
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    }

                </div>
                
                <div class="overlay_aguarde" style="display: none">
                    <div class="spinner-aguarde">
                        <div class="sk-spinner sk-spinner-wandering-cubes">
                            <div class="sk-spinner sk-spinner-wave">
                                <div class="sk-rect1 text-ligh"></div>
                                <div class="sk-rect2"></div>
                                <div class="sk-rect3"></div>
                                <div class="sk-rect4"></div>
                                <div class="sk-rect5"></div>
                            </div>
                            <div class="sk-rect5"></div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/declaracoes/Constants")
    @Scripts.Render("~/declaracoes/Validates")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

    $(document).ready(function () {

        //
        // VALIDACAO DOS CAMPOS
        //
        Validator_AddMethods();

        $("#form").validate({
            rules: {
                Medicao: {
                    required: true,
                },
                SetPointLo: {
                    numeric: true
                },
                SetPointHi: {
                    numeric: true
                },
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            },
        });
            
        //// VERIFICA CAMPOS QUE DEVEM OU NAO ESTAR HABILITADOS \\\\
        SelecionouTipoAlarme();

        // desabilita campos por permissao de usuario
        disableAll();
            
        $('form input').on('keypress', function (e) {
            return e.which !== 13;
        });   
    });

    function disableAll() {

        var permissao = Math.ceil(@ViewBag.Permissao);

        // verifica permissao
        switch (permissao) {
            case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
            case PERMISSOES_GERENTE:            // 1 - permissao de Gerente: ve tudo e escreve parte
            case PERMISSOES_OPERADOR:           // 2 - permissao de Operador: ve tudo e nao pode escrever
            case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
            case PERMISSOES_CONSULTOR:          // 5 - permissao de Consultor
            case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte 
                               
                $("#BotaoProgramar").attr('disabled', false);
                break;

            default:
            case PERMISSOES_PRODUCAO:           // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                $("#BotaoProgramar").attr('disabled', true);
                break;
        }
    }
    
    function SelecionouTipoAlarme() {

        // pega TipoAlarme selecionado
        var tipoAlarme = parseInt($("#TipoAlarme").val());

        // desabilita 
        $('.div_origemED').css("display", "none");
        $("#origemED").attr('disabled', true);

        $('.div_origemSD').css("display", "none");
        $("#origemSD").attr('disabled', true);

        $('.div_origemMedEner').css("display", "none");
        $("#origemMerEner").attr('disabled', true);

        // habilita
        $('.div_programado').css("display", "block");

        switch (tipoAlarme)
        {
            case 0:

                // desabilita
                $('.div_programado').css("display", "none");

                // unidade
                document.getElementById("normalizaUnidade").innerHTML = "";
                document.getElementById("ativaUnidade").innerHTML = "";
                break;

            case 6:

                // habilita entrada
                $('.div_origemED').css("display", "block");
                $("#origemED").attr('disabled', false);

                // unidade
                document.getElementById("normalizaUnidade").innerHTML = "";
                document.getElementById("ativaUnidade").innerHTML = "";
                break;

            case 7:

                // habilita saida
                $('.div_origemSD').css("display", "block");
                $("#origemSD").attr('disabled', false);

                // unidade
                document.getElementById("normalizaUnidade").innerHTML = "";
                document.getElementById("ativaUnidade").innerHTML = "";
                break;

            default:

                // habilita medição energia
                $('.div_origemMedEner').css("display", "block");
                $("#origemMedEner").attr('disabled', false);

                var unidade = "";

                if (tipoAlarme >= 8 && tipoAlarme <= 21)
                {
                    unidade = "(V)";
                }

                if (tipoAlarme >= 22 && tipoAlarme <= 29) {
                    unidade = "(A)";
                }

                if (tipoAlarme >= 30 && tipoAlarme <= 31) {
                    unidade = "(Hz)";
                }

                if (tipoAlarme >= 32 && tipoAlarme <= 40) {
                    unidade = "(kW)";
                }

                if (tipoAlarme >= 43 && tipoAlarme <= 48) {
                    unidade = "(%)";
                }

                // unidade
                document.getElementById("normalizaUnidade").innerHTML = unidade;
                document.getElementById("ativaUnidade").innerHTML = unidade;
                break;
        }
    }

    function TrataValores() {

        // pega TipoAlarme selecionado
        var tipoAlarme = parseInt($("#TipoAlarme").val());
        var num_origem = 0;

        switch (tipoAlarme) {
            case 6:

                num_origem = parseInt(document.getElementById("origemED").value);
                break;

            case 7:

                num_origem = parseInt(document.getElementById("origemSD").value);
                break;

            default:

                num_origem = parseInt(document.getElementById("origemMedEner").value);
                break;
        }

        if (Number.isNaN(num_origem)) {
            num_origem = 0;
        }

        document.getElementById('Medicao').value = num_origem;
    }

    function Programar() {

        // trata valores das variaveis a serem salvas
        TrataValores();

        var $form = $('#form');

        // apresenta primeiro tab que tiver erro
        var IsTabValid = true;
        var TabInvalid = 1;

        $(".tab-content").find("div.tab-pane").each(function (index, tab) {
            var id = $(tab).attr("id");
            $('a[href="#' + id + '"]').tab('show');

            IsTabValid = $("#form").valid();

            if (!IsTabValid) {

                TabInvalid = id;
                return false;
            }
        });
        
        // apresenta primeira tab ou com erro
        $('a[href="#tab-' + TabInvalid + '"]').tab('show');

        // verifica se sao validas
        if (!IsTabValid) return false;

        // retiro campos desabilitados, pois nao envia via POST
        $(":disabled", $('#form')).removeAttr("disabled");

        $.ajax({
            url: '/Configuracao/GateX_CtrlAlarme_Programar',
            data: $form.serialize(),
            type: 'POST',
            success: function (data) {

                setTimeout(function () {

                    // verifica se erro
                    if (data.status == "ERRO") {
                        swal({
                            title: "Erro",
                            text: data.erro,
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {


                        });
                    }
                    else {

                        // redireciona
                        var IDGateway = document.getElementById('IDGateway').value;
                        var url = '/Configuracao/GateX_CtrlAlarme?IDGateway=' + IDGateway + '&Origem=2';

                        window.location.href = url;
                    }

                }, 100);

            },
            error: function (xhr, status, error) {

                setTimeout(function () {

                    swal({
                        title: "Erro",
                        text: "Erro ao Programar!",
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    }, function () {

                        // redireciona
                        var IDGateway = document.getElementById('IDGateway').value;
                        var url = '/Configuracao/GateX_CtrlAlarme?IDGateway=' + IDGateway + '&Origem=2';

                        window.location.href = url;
                    });
                }, 100);
            },
        });
    };

    $(".link_primeira").off().on("click", function (event) {
        event.stopPropagation();

        // primeiro 
        SalvaNavega(0);
    });

    $(".link_ultima").off().on("click", function (event) {
        event.stopPropagation();

        // ultimo 
        SalvaNavega(99);
    });

    $(".link_prog_menos").off().on("click", function (event) {
        event.stopPropagation();

        var NumAlarmeGateway = parseInt(document.getElementById("NumAlarmeGateway").value);

        // anterior
        SalvaNavega(NumAlarmeGateway - 1);
    });

    $(".link_prog_mais").off().on("click", function (event) {
        event.stopPropagation();

        var NumAlarmeGateway = parseInt(document.getElementById("NumAlarmeGateway").value);

        // proximo 
        SalvaNavega(NumAlarmeGateway + 1);
    });


    function SalvaNavega(navega) {

        // trata valores das variaveis a serem salvas
        TrataValores();

        var $form = $('#form');

        // apresenta primeiro tab que tiver erro
        var IsTabValid = true;
        var TabInvalid = 1;

        $(".tab-content").find("div.tab-pane").each(function (index, tab) {
            var id = $(tab).attr("id");
            $('a[href="#' + id + '"]').tab('show');

            IsTabValid = $("#form").valid();

            if (!IsTabValid) {

                TabInvalid = id;
                return false;
            }
        });

        // apresenta primeira tab ou com erro
        $('a[href="#tab-' + TabInvalid + '"]').tab('show');

        // verifica se sao validas
        if (!IsTabValid) return false;

        // verifica se sao validas
        if (!$form.valid()) return false;

        event.stopPropagation();

        // aguarde
        $('.overlay_aguarde').toggle();

        // retiro campos desabilitados, pois nao envia via POST
        $(":disabled", $('#form')).removeAttr("disabled");

        $.ajax({
            url: '/Configuracao/GateX_CtrlAlarme_Programar',
            data: $form.serialize(),
            type: 'POST',
            success: function (data) {

                setTimeout(function () {

                    // verifica se erro
                    if (data.status == "ERRO") {
                        swal({
                            title: "Erro",
                            type: "warning",
                            text: data.erro,
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // recarrega pagina
                            self.location.reload(true);
                        });
                    }
                    else {
                        // redireciona
                        var IDGateway = document.getElementById('IDGateway').value;
                        var url = '/Configuracao/GateX_CtrlAlarme_Editar?IDGateway=' + IDGateway + '&NumAlarmeGateway=' + navega;

                        window.location.href = url;
                    }

                }, 100);
            },
            error: function (xhr, status, error) {

                setTimeout(function () {

                    swal({
                        title: "Erro",
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    }, function () {

                        // redireciona
                        var IDGateway = document.getElementById('IDGateway').value;
                        var url = '/Configuracao/GateX_CtrlAlarme?IDGateway=' + IDGateway + '&Origem=2';

                        window.location.href = url;
                    });
                }, 100);
            },
        });
    };

    function Botao(botao) {

        // formula
        var formula = document.getElementById('Formula').value;
        var adicionar = "";

        // verifica botão
        switch (botao)
        {
            case "E":
            case "S":
            case "AND":
            case "OR":
            case "XOR":
            case "NAND":
            case "NOR":
            case "NXOR":
            case "NOT":
            case "(":
                adicionar = " " + botao;
                break;

            case "0":
            case "1":
            case "2":
            case "3":
            case "4":
            case "5":
            case "6":
            case "7":
            case "8":
            case "9":
            case ")":

                adicionar = botao;
                break;
        }

        // adiciona na fórmula
        var textObj = document.getElementById('Formula');
        insertTextAtCursor(textObj, adicionar);
    };

    function insertTextAtCursor(el, text) {

        var val = el.value, endIndex, range, doc = el.ownerDocument;

        // le cookie de posição do cursor e posiciona
        var last_position = getCookie_Posicao("Cursor");
        el.selectionStart = el.selectionEnd = last_position;

        if (typeof el.selectionStart == "number" && typeof el.selectionEnd == "number")
        {
            endIndex = el.selectionEnd;
            el.value = val.slice(0, endIndex) + text + val.slice(endIndex);
            el.selectionStart = el.selectionEnd = endIndex + text.length;

            // salva cookie
            setCookie_Posicao("Cursor", el.selectionStart, null);
        }
        else if (doc.selection != "undefined" && doc.selection.createRange)
        {
            el.focus();
            range = doc.selection.createRange();
            range.collapse(false);
            range.text = text;
            range.select();
        }
    }

    function cursorFormula(element) {

        // le cookie de posição do cursor
        var last_position = getCookie_Posicao("Cursor");

        var new_position = getCursorPosition(element);

        if (new_position !== last_position)
        {
            last_position = new_position;

            // salva cookie
            setCookie_Posicao("Cursor", new_position, null);

            return true;
        }

        return false;
    }

    function getCursorPosition(element) {

        var el = $(element).get(0);
        var pos = 0;

        if ('selectionStart' in el)
        {
            pos = el.selectionStart;
        }
        else if ('selection' in document)
        {
            el.focus();
            var Sel = document.selection.createRange();
            var SelLength = document.selection.createRange().text.length;
            Sel.moveStart('character', -el.value.length);
            pos = Sel.text.length - SelLength;
        }
        return pos;
    }


    function setCookie_Posicao(c_name, value, expiresecs) {
        var exdate = new Date();
        exdate.setTime(exdate.getTime() + ((expiresecs) ? expiresecs * 1000 : 0));

        document.cookie = c_name + "=" + escape(value) + ((expiresecs == null) ? "" : ";expires=" + exdate.toGMTString()) + ";path=/";
    }

    function getCookie_Posicao(c_name) {

        // Get name followed by anything except a semicolon
        var cookiestring = RegExp("" + c_name + "[^;]+").exec(document.cookie);

        // Return everything after the equal sign, or an empty string if the cookie name not found
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./, "") : "");
    }

    </script>
}
