﻿@using SmartEnergyLib.SQL

<style>

    .theme-config {
        position: absolute;
        top: 0;
        right: 0;
        overflow: hidden;
    }

    .theme-config-box {
        min-width: 1100px;
        margin-right: -1140px;
        position: relative;
        z-index: 2000;
        transition-duration: 0.8s;
    }
     
    .theme-config-box.show {
        margin-right: 0; }

    .menu-lateral {
        max-width: 1100px;
        margin-left: 40px;
        background: #f3f3f4; }

    .spin-icon {
        background: #1ab394;
        position: absolute;
        font-size: 22px;
        top: 0;
        left: 0;
        width: 40px;
        color: #fff;
        cursor: pointer; }

    .link_preto a:link {
        color: #7E6A6C !important;
    }

    .link_preto a:visited {
        color: #7E6A6C !important;
    }

    .link_preto a:hover {
        color: #a0a0a0 !important;
    }

    .link_preto a:active {
        color: #7E6A6C !important;
    }


</style>

@{
    int IDSimulacaoCenario = 0;
    bool aplicaSimulacao = false;
        
    if (ViewBag.AplicaSimulacao != null)
    {
        aplicaSimulacao = ViewBag.AplicaSimulacao;
    }
    
    if (ViewBag._IDSimulacaoCenario != null)
    {
        IDSimulacaoCenario = ViewBag._IDSimulacaoCenario;
    }
    
    List<SimulacaoCenariosDominio> simulacoesCenarios = new List<SimulacaoCenariosDominio>();
    
    if (ViewBag.SimulacoesCenario != null)
    {
        simulacoesCenarios = ViewBag.SimulacoesCenario;    
    }    
}

<div id="cenarios_resultado">

    @if (ViewBag.SimulacoesCenario != null)
    {
        // verifica se deve renderizar a pagina de configuracao 
        // apenas renderiza o html abaixo se for feita a solicitacao da view atraves do AJAX
         <div class="theme-config">        
            <div class="theme-config-box">
                <div class="menu-lateral">
                    <div class="spin-icon" onclick="FechaSelecao();">
                        <i class="fa fa-cogs fa-spin"></i>
                    </div>
                    <div class="wrapper wrapper-content">
                        <div class="panel panel-title">
                            <div class="panel-heading">
                                <h4>Configuração Simulação de Cenários</h4>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="form-group col-lg-6">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.SimulacaoTexts.Cenario</label>
                                        <select class="form-control m-b" id="IDSimulacaoCenario" onchange="SelecionouSimulacao()">

                                            @{

                                                if (simulacoesCenarios.Count > 0)
                                                {                                                                                        
                                                    <option value="0">Nenhum Cenário de Simulação selecionado</option>

                                                    foreach (SimulacaoCenariosDominio cenario in simulacoesCenarios.OrderBy(x => x.Nome))
                                                    {
                                                        var classe = "";

                                                        if (IDSimulacaoCenario == cenario.IDSimulacaoCenario) { classe = "selected"; } else { classe = ""; }
                                                        <option value="@cenario.IDSimulacaoCenario" @classe>@cenario.Nome</option>
                                                    }
                                                }

                                                else
                                                {
                                                    <option value="0">Não existe cenário configurado</option>
                                                }
                                            }

                                        </select>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="ibox-tools">
                                            <a onclick="FechaSelecao();" class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                            <button type="button" class="btn btn-primary" onclick="SalvarSimulacao();" id="BotaoSalvar">@SmartEnergy.Resources.ComumTexts.BotaoAplicar</button>
                                        </div>
                                    </div>
                                </div>                           
                                <br />
                                <br />
                                <div class="panel panel-title">
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-lg-6">
                                                <h4 style="font-weight:bold">Cenários</h4>
                                            </div>
                                            <div class="col-lg-offset-2 col-lg-4">
                                                <div class="link_branco">
                                                    <button type="button" id="BotaoAdicionarSimulacao" onclick="EditarSimulacao(0);" class="btn btn-info btn-lg pull-left" style="color:#ffffff; width:100%;">
                                                        <span style="font-size:large">Adicionar Cenário</span>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <br />

                                        <div class="row">
                                            <div class="col-lg-12">
                                                <table id="dataTables-cenarios" class="table table-striped table-bordered table-hover">
                                                    <thead>
                                                        <tr>
                                                            <th>@SmartEnergy.Resources.SimulacaoTexts.Cenario</th>
                                                            <th>@SmartEnergy.Resources.FinancasTexts.Contrato @SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                                                            <th>@SmartEnergy.Resources.FinancasTexts.Contrato @SmartEnergy.Resources.SupervisaoTexts.ForaPonta</th>
                                                            <th></th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @{
                                                            foreach (SimulacaoCenariosDominio cenario in simulacoesCenarios)
                                                            {                                                                                                                        
                                                                <tr>
                                                                    <td>@cenario.Nome</td>
                                                                    <td>@string.Format("{0}", cenario.ContratoDemP)</td>
                                                                    <td>@string.Format("{0}", cenario.ContratoDemFP)</td>
                                                                    <td class="link_preto">
                                                                        <a href="#"><i class="fa fa-edit icones" onclick="EditarSimulacao(@cenario.IDSimulacaoCenario);" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a>
                                                                        <a href="#" onclick="javascript:ExcluirSimulacao(@cenario.IDSimulacaoCenario, '@cenario.Nome', @cenario.IDMedicao);" id="BotaoExcluir" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-times icones"></i></a>
                                                                    </td>
                                                                </tr>
                                                            }
                                                        }
                                                    </tbody>
                                                </table>
                                            </div>

                                        </div>
                                    </div>
                                </div>

                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
    
</div>

   
<script type="text/javascript">

    $(document).ready(function() {

        // IDSimulacaoCenario
        var IDSimulacaoCenario = @Html.Raw(Json.Encode(@ViewBag._IDSimulacaoCenario));        

        // salva em cookie
        setCookie("_IDSimulacaoCenario", IDSimulacaoCenario, null);

        $('#dataTables-cenarios').DataTable({
            "iDisplayLength": 10,
            dom: 'tp',

            'order': [0, 'asc'],
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "35%" },
            { sWidth: "25%" },
            { sWidth: "25%" },
            { sWidth: "15%" },
            ],

            "columnDefs": [
                 
                 { "targets": [0], "sType": "portugues" },
                 { "targets": [1], "sType": "numeric" },
                 { "targets": [2], "sType": "numeric" },
                 { "targets": [3], "orderable": false },
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });  

        


    });

    function SalvarSimulacao () {

        // fecha dialog
        FechaSelecao();

        // pega simulacao selecionada
        var IDSimulacaoCenario = $("#IDSimulacaoCenario").val();        

        // aplica simulacao
        if (IDSimulacaoCenario == 0)
        {
            setCookie("AplicaSimulacao", false, null);
        }
        else
        {
            setCookie("AplicaSimulacao", true, null);
        }

        Atualiza(0);
    }

    function SelecionouSimulacao () {

        // pega simulacao selecionada
        var IDSimulacaoCenario = $("#IDSimulacaoCenario").val();        

        // salva em cookie
        setCookie("_IDSimulacaoCenario", IDSimulacaoCenario, null);
    }

    function FechaSelecao () {

        // fecha
        setTimeout(function () { $('.theme-config-box').toggleClass("show"), 100 });
    }

    function SelecionarSimulacao() {

        var IDMedicao = @Json.Encode(ViewBag._IDMedicao);

        event.stopPropagation();

        // aguarde
        $('.overlay_aguarde').toggle();

        // atualiza pagina
        $.ajax(
        {
            type: 'GET',
            url: '/Simulacao/_Simulacoes',
            dataType: 'html',
            data: { 'IDMedicao': IDMedicao },
            cache: false,
            async: true,
            success: function (data) {

                // fim
                $('.overlay_aguarde').toggle();

                // apresenta
                $('#cenarios_resultado').html(data);

                // abre janela anterior
                setTimeout(function () { $('.theme-config-box').toggleClass("show"), 100 });
            },
            error: function (xhr, status, error) {
                setTimeout(function () {

                    swal({
                        title: "Erro",
                        text: "Erro ao executar a operação!",
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    });

                    // fim
                    $('.overlay_aguarde').toggle();

                }, 100);

            }
        });
    }

    function EditarSimulacao(id_simulacao) {

        var IDMedicao = @Json.Encode(ViewBag._IDMedicao);

        // edita simulacao
        if (id_simulacao >= 0) {

            event.stopPropagation();

            // fecha janela atual
            $('.theme-config-box').toggleClass("show");

            // aguarde
            $('.overlay_aguarde').toggle();

            $.ajax(
            {
                type: 'GET',
                url: '/Simulacao/_Simulacoes_Editar',
                dataType: 'html',
                data: { 'IDSimulacaoCenario': id_simulacao, 'IDMedicao' : IDMedicao },
                cache: false,
                async: true,
                success: function (data) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    // apresenta nova janela
                    $('#cenarios_editar_resultado').html(data);
                    setTimeout(function () { $('.theme-config-box-1').toggleClass("show"), 100});

                },
                error: function (xhr, status, error) {
                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao executar a operação!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                        // fim
                        $('.overlay_aguarde').toggle();

                    }, 100);

                }
            });
        }
         
            // fecha janela
        else {
            $('.theme-config-box-1').toggleClass("show");
        }
    }

    function ExcluirSimulacao(IDSimulacaoCenario, Nome, IDMedicao) {

        event.stopPropagation();

        // titulo
        titulo = "Deseja excluir o Cenário de Simulação?<br/>" + Nome;

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: false
        }, function () {

            // fecha janela
            $('.theme-config-box').toggleClass("show");

            // excluir
            $.ajax(
            {
                type: 'GET',
                url: '/Simulacao/SimulacaoCenario_Excluir',
                data: { 'IDSimulacaoCenario': IDSimulacaoCenario },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        swal({
                            title: "Excluído com sucesso",
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // atualiza pagina
                            $.ajax(
                            {
                                type: 'GET',
                                url: '/Simulacao/_Simulacoes',
                                dataType: 'html',
                                data: { 'IDMedicao' : IDMedicao },
                                cache: false,
                                async: true,
                                success: function (data) {

                                    // apresenta
                                    $('#cenarios_resultado').html(data);
                                    $('.theme-config-box').toggleClass("show");
                                },
                                error: function (xhr, status, error) {
                                    setTimeout(function () {

                                        swal({
                                            title: "Erro",
                                            text: "Erro ao executar a operação!",
                                            type: "warning",
                                            confirmButtonColor: "#f8ac59",
                                            confirmButtonText: "Fechar",
                                        });

                                    }, 100);

                                }
                            });
                        });

                    }, 100);
                },
                error: function (response) {

                    setTimeout(function () {
                        
                        swal({
                            title: "Erro",
                            text: "Erro ao excluir!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });
    };

</script>