﻿@using SmartEnergyLib.SQL

<style>

    .theme-config-box-1 {
        min-width: 1100px;
        margin-right: -1140px;
        position: relative;
        z-index: 2000;
        transition-duration: 0.8s;
    }

        .theme-config-box-1.show {
            margin-right: 0;
        }
</style>

@{
    List<SimulacaoCargasDominio> simulacoesCargas = new List<SimulacaoCargasDominio>();
    SimulacaoCenariosDominio cenarioEditar = new SimulacaoCenariosDominio();

    if (ViewBag.SimulacoesCargas != null)
    {
        simulacoesCargas = ViewBag.SimulacoesCargas;
    }

    if (ViewBag.SimulacaoCenario != null)
    {
        cenarioEditar = ViewBag.SimulacaoCenario;
    }
    
}

<div id="cenarios_editar_resultado">
    @if (ViewBag.SimulacoesCargas != null && ViewBag.SimulacaoCenario != null)
    // verifica se deve renderizar a pagina de configuracao 
    // apenas renderiza o html abaixo se for feita a solicitacao da view atraves do AJAX
    {
     <div class="theme-config">
        <div class="theme-config-box-1">
            <div class="menu-lateral">
                <div class="spin-icon" onclick="EditarSimulacao(-1);">
                    <i class="fa fa-cogs fa-spin"></i>
                </div>
                <div class="wrapper wrapper-content">
                    <div class="panel panel-title">
                        <div class="panel-heading">
                            <h4>Configuração Simulação de Cenários</h4>
                        </div>
                        <div class="panel-body">
                            @using (Html.BeginForm("_Simulacoes_Editar", "Simulacao", FormMethod.Post, new { id = "form-cenario", role = "form" }))
                            {
                                @Html.Hidden("IDMedicao", cenarioEditar.IDMedicao)
                                @Html.Hidden("IDSimulacaoCenario", cenarioEditar.IDSimulacaoCenario)

                                <div class="row">
                                    <div class="form-group col-lg-4">
                                        <label class="control-label">&nbsp;ID</label>
                                        @{
                                            if (cenarioEditar.IDSimulacaoCenario == 0)
                                            {
                                                @Html.Hidden("IDSimulacaoCenario", cenarioEditar.IDSimulacaoCenario)
                                                @Html.TextBox("Novo", "Nova Simulação", new { @class = "form-control", @disabled = "disabled" })
                                            }
                                            else
                                            {
                                                @Html.TextBox("ID", cenarioEditar.IDSimulacaoCenario, new { @class = "form-control", @disabled = "disabled" })
                                            }
                                        }
                                    </div>
                                    <div class="col-lg-offset-2 col-lg-6">
                                        <div class="ibox-tools">
                                            <a onclick="EditarSimulacao(-1);" class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                            <button type="button" class="btn btn-primary" id="BotaoSalvarSimulacao" onclick="SalvarSimulacao();">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                        </div>
                                    </div>
                                </div>

                                <br />
                                <div class="row">
                                    <div class="col-lg-10">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Nome</label>               
                                        @Html.TextBox("Nome", cenarioEditar.Nome, new { @class = "form-control" })
                                    </div>
                                </div>
                                <br />

                                <div class="row">
                                    <div class="col-lg-4">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ContratoP</label>               
                                        @Html.TextBox("ContratoDemP", cenarioEditar.ContratoDemP, new { @class = "form-control" })
                                    </div>
                                    <div class="col-lg-offset-2 col-lg-4">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ContratoFP</label>               
                                        @Html.TextBox("ContratoDemFP", cenarioEditar.ContratoDemFP, new { @class = "form-control" })
                                    </div>
                                </div>

                                <br />
                                <br />

                                <div class="panel panel-title">
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-lg-6">
                                                <h4 style="font-weight:bold">Cargas</h4>
                                            </div>
                                            <div class="col-lg-offset-3 col-lg-3">
                                                <div class="link_branco">
                                                    <button type="button" id="BotaoAdicionarCarga" onclick="EditarCargas(0, @cenarioEditar.IDSimulacaoCenario);" class="btn btn-info btn-lg pull-left" style="color:#ffffff; width:100%;">
                                                        <span style="font-size:large">Adicionar Carga</span>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <br />

                                        <div class="row">
                                            <div class="col-lg-12">
                                                <table id="dataTables-cargas" class="table table-striped table-bordered table-hover">
                                                    <thead>
                                                        <tr>
                                                            <th>Descrição</th>
                                                            <th>Carga</th>
                                                            <th>Potência</th>
                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Inicio</th>
                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Fim</th>
                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.DiasSemana</th>
                                                            <th></th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @{
                                                            foreach (SimulacaoCargasDominio carga in simulacoesCargas)
                                                            {
                                                                <tr>
                
                                                                    @{
                                                                        string tipoCarga = "Carga";
                                                                        string unidadeCarga = "(kW)";
                                                                        
                                                                        if (carga.IDTipoCarga == 1)
                                                                        {
                                                                            tipoCarga = "Capacitor";
                                                                            unidadeCarga = "(kVAr)";
                                                                        }

                                                                        string dias = "";

                                                                        if (carga.Dom) { dias += " Dom "; }
                                                                        if (carga.Seg) { dias += " Seg "; }
                                                                        if (carga.Ter) { dias += " Ter "; }
                                                                        if (carga.Qua) { dias += " Qua "; }
                                                                        if (carga.Qui) { dias += " Qui "; }
                                                                        if (carga.Sex) { dias += " Sex "; }
                                                                        if (carga.Sab) { dias += " Sab "; }
                                                                        
                                                                        // sem dias
                                                                        if (!carga.Dom && !carga.Seg && !carga.Ter && !carga.Qua && !carga.Qui && !carga.Sex && !carga.Sab)
                                                                        { dias = "Nenhum"; }
                                                                    }

                                                                    <td>@carga.Nome</td>
                                                                    <td>@tipoCarga</td>
                                                                    <td>@string.Format("{0:0} {1}", Math.Abs(carga.Potencia), unidadeCarga)</td>
                                                                    <td>@string.Format("{0:t}", carga.HoraIni)</td>
                                                                    <td>@string.Format("{0:t}", carga.HoraFim)</td>
                                                                    <td>@dias</td>
                                                                    <td class="link_preto">
                                                                        <a href="#"><i class="fa fa-edit icones" onclick="EditarCargas(@carga.IDSimulacaoCarga, @cenarioEditar.IDSimulacaoCenario);" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a>
                                                                        <a href="#" class="confirm-delete-carga" onclick="ExcluirCarga(@carga.IDSimulacaoCarga, @cenarioEditar.IDSimulacaoCenario, @cenarioEditar.IDMedicao)"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>
                                                                    </td>
                                                                </tr>
                                                            }
                                                        }

                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                            }

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    }
    

</div>


<script type="text/javascript">

    $(document).ready(function () {

        //
        // VALIDACAO DOS CAMPOS
        //

        jQuery.validator.addMethod("alphanumeric", function (value, element) {
            return this.optional(element) || /^[\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\&\+\-\_\(\),]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

        jQuery.validator.addMethod("numeric", function (value, element) {
            return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.numeric");

        jQuery.validator.addMethod("time", function (value, element) {
            return this.optional(element) || /^(([0-1]?[0-9])|([2][0-3])):([0-5]?[0-9])(:([0-5]?[0-9]))?$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.horario");


        $("#form-cenario").validate({
            rules: {
                Nome: {
                    alphanumeric: true,
                    required: true,
                },
                ContratoDemP: {
                    required: true,
                    numeric: true,
                },
                ContratoDemFP: {
                    required: true,
                    numeric: true,
                },
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            },
        });

        $('#dataTables-cargas').DataTable({
            "iDisplayLength": 10,
            dom: 'tp',

            'order': [0, 'asc'],
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "20%" },
            { sWidth: "15%" },
            { sWidth: "15%" },
            { sWidth: "10%" },
            { sWidth: "10%" },
            { sWidth: "20%" },
            { sWidth: "10%" },
            ],

            "columnDefs": [
                 
                 { "targets": [0], "sType": "portugues" },
                 { "targets": [1], "sType": "portugues" },
                 { "targets": [2], "sType": "portugues" },
                 { "targets": [3], "sType": "portugues" },
                 { "targets": [4], "sType": "portugues" },
                 { "targets": [5], "sType": "portugues" },
                 { "targets": [6], "sortable": false },
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

    });

    function SalvarSimulacao() {

        var $form = $('#form-cenario');

        // verifica se entradas sao validas
        if (!$form.valid()) return false;

            event.stopPropagation();

            // aguarde
            $('.overlay_aguarde').toggle();

            // fecha janela
            $('.theme-config-box-1').toggleClass("show");

            // retiro campos desabilitados, pois nao envia via POST
            $(":disabled", $('#form')).removeAttr("disabled");

            $.ajax({
                url: '/Simulacao/Simulacao_Cenario_Salvar',
                data: $form.serialize(),
                type: 'POST',
                success: function (data) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {
                            swal({
                                title: "Erro",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // abre novamente janela
                                setTimeout(function () { $('.theme-config-box-1').toggleClass("show"), 100 });
                            });
                        }
                        else {

                                var IDMedicao = parseInt(document.getElementById('IDMedicao').value);

                                // atualiza pagina
                                $.ajax(
                                {
                                    type: 'GET',
                                    url: '/Simulacao/_Simulacoes',
                                    dataType: 'html',
                                    data: { 'IDMedicao': IDMedicao },
                                    cache: false,
                                    async: true,
                                    success: function (data) {

                                        // apresenta
                                        $('#cenarios_resultado').html(data);

                                        // abre janela anterior
                                        setTimeout(function () { $('.theme-config-box').toggleClass("show"), 100 });
                                    },
                                    error: function (xhr, status, error) {
                                        setTimeout(function () {

                                            swal({
                                                title: "Erro",
                                                text: "Erro ao executar a operação!",
                                                type: "warning",
                                                confirmButtonColor: "#f8ac59",
                                                confirmButtonText: "Fechar",
                                            });

                                            // fim
                                            $('.overlay_aguarde').toggle();

                                        }, 100);

                                    }
                                });
                        }

                    }, 100);

                },
                error: function (xhr, status, error) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao salvar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
    }

    function EditarCargas(id_carga, id_simulacao) {


        if (id_simulacao >= 0) {

            var $form = $('#form-cenario');

            // verifica se entradas sao validas
            if (!$form.valid()) return false;

            event.stopPropagation();

            // fecha janela atual
            $('.theme-config-box-1').toggleClass("show");

            // aguarde
            $('.overlay_aguarde').toggle();

            // retiro campos desabilitados, pois nao envia via POST
            $(":disabled", $('#form')).removeAttr("disabled");

            $.ajax({
                url: '/Simulacao/Simulacao_Cenario_Salvar',
                data: $form.serialize(),
                type: 'POST',
                success: function (result) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        // verifica se erro
                        if (result.status == "ERRO") {
                            swal({
                                title: "Erro",
                                text: result.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // abre novamente janela
                                setTimeout(function () { $('.theme-config-box-1').toggleClass("show"), 100 });
                            });
                        }
                        else {

                            // direciona para adicionar carga
                            $.ajax(
                            {
                                type: 'GET',
                                url: '/Simulacao/_Simulacoes_Cargas_Editar',
                                dataType: 'html',
                                data: { 'IDSimulacaoCarga': id_carga, 'IDSimulacaoCenario': result.IDSimulacaoCenario },
                                cache: false,
                                async: true,
                                success: function (data) {

                                    // apresenta
                                    $('#editar_cargas_resultado').html(data);

                                    // apresenta nova janela
                                    setTimeout(function () { $('.theme-config-box-2').toggleClass("show"), 100 });

                                    SelecionouTipoCarga();

                                },
                                error: function (xhr, status, error) {
                                    setTimeout(function () {

                                        swal({
                                            title: "Erro",
                                            text: "Erro ao executar a operação!",
                                            type: "warning",
                                            confirmButtonColor: "#f8ac59",
                                            confirmButtonText: "Fechar",
                                        });


                                        // fim
                                        $('.overlay_aguarde').toggle();

                                    }, 100);

                                }
                            });

                        }

                    }, 100);

                },
                error: function (xhr, status, error) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    // fecha janela
                    $('.theme-config-box-1').toggleClass("show");

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao salvar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        }

            // fecha janela
        else {
            $('.theme-config-box-2').toggleClass("show");
        }
    }

    function ExcluirCarga(IDSimulacaoCarga, IDSimulacaoCenario, IDMedicao) {
        event.stopPropagation();

        // titulo
        titulo = "Deseja excluir a Carga?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: false
        }, function () {
           
            // excluir
            $.ajax(
            {
                type: 'GET',
                url: '/Simulacao/SimulacaoCarga_Excluir',
                data: { 'IDSimulacaoCarga': IDSimulacaoCarga },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        swal({
                            title: "Excluído com sucesso",
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // atualiza pagina
                            $.ajax(
                            {
                                type: 'GET',
                                url: '/Simulacao/_Simulacoes_Editar',
                                dataType: 'html',
                                data: { 'IDSimulacaoCenario': IDSimulacaoCenario, 'IDMedicao' : IDMedicao },
                                cache: false,
                                async: true,
                                success: function (data) {
                                    
                                    // apresenta
                                    $('#cenarios_editar_resultado').html(data);

                                    setTimeout(function () { $('.theme-config-box-1').toggleClass("show"), 100 });
                                },
                                error: function (xhr, status, error) {
                                    setTimeout(function () {

                                        swal({
                                            title: "Erro",
                                            text: "Erro ao executar a operação!",
                                            type: "warning",
                                            confirmButtonColor: "#f8ac59",
                                            confirmButtonText: "Fechar",
                                        });

                                        // fim
                                        $('.overlay_aguarde').toggle();

                                    }, 100);

                                }
                            });
                        });

                    }, 100);
                },
                error: function (response) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao excluir!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                        // fim
                        $('.overlay_aguarde').toggle();

                    }, 100);

                }
            });
        });
    };

</script>