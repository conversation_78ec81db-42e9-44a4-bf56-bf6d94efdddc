﻿@using System.Globalization

@{
    ViewBag.Title = @SmartEnergy.Resources.ConfiguracaoTexts.AnaliseHorarioCons;
}


<style>
    .overlay_aguarde {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.45);
        z-index: 9999;
        color: white;
        display: inline-block;
        border-radius: 6px;
        margin-bottom: 20px;
        margin-left: 14px;
        margin-right: 14px;
    }

    .spinner-aguarde .sk-spinner-wave div {
        background-color: #ffffff !important;
    }

    .spinner-aguarde {
        margin: 0;
        position: absolute;
        top: 50%;
        left: 50%;
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
    }
</style>




<div class="wrapper wrapper-content">

    <div class="row">
        <div class="col-lg-12">

            @{
                <div class="panel panel-title relatorio" id="atualizar" style="min-width:1200px;">
                 @Html.Partial("_AnaliseHorarioCons_Atualizar")
                </div>
                
                <div class="overlay_aguarde" style="display: none">
                    <div class="spinner-aguarde">
                        <div class="sk-spinner sk-spinner-wandering-cubes">
                            <div class="sk-spinner sk-spinner-wave">
                            <div class="sk-rect1 text-ligh"></div>
                            <div class="sk-rect2"></div>
                            <div class="sk-rect3"></div>
                            <div class="sk-rect4"></div>
                            <div class="sk-rect5"></div>
                            </div>
                        </div>
                    </div>
                </div>
            }

        </div>
    </div>

</div>

@section Styles {
    @Styles.Render("~/datetimePickerStyles")
    @Styles.Render("~/Content/Relatorios.css")
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/plugins/waitingFor")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
        <script type="text/javascript" language="javascript" src="~/Scripts/locales/bootstrap-datepicker.es.min.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
        <script type="text/javascript" language="javascript" src="~/Scripts/locales/bootstrap-datepicker.pt-BR.min.js"></script>
    }

    <script type="text/javascript">

        $(document).ready(function () {

            // atualiza
            Atualiza();
        });

        function Atualiza() {

            // IDAnaliseHorarioCons
            var IDAnaliseHorarioCons = @Html.Raw(Json.Encode(@ViewBag._IDAnaliseHorarioCons));

            // formata data
            data = "01/01/2000";

            // aguarde
            $('.overlay_aguarde').toggle();
            $.ajax(
            {
                type: 'GET',
                url: '/Analises/_AnaliseHorarioCons_Atualizar',
                dataType: 'html',
                data: { 'IDAnaliseHorarioCons': IDAnaliseHorarioCons, 'Navegacao': 0, 'Data': data },
                cache: false,
                async: true,
                success: function (data) {
                    // fim
                    $('.overlay_aguarde').toggle();

                    $('#atualizar').html(data);
                }
            });
        }

    </script>

}

