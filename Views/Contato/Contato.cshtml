﻿@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralContato;
}

<style>
    .bar {
        height: 18px;
        background: #1AB394;
    }

    .quadro_mapa {
        padding: 2px;
        border: 1px solid #efefef;
    }
</style>


<div class="wrapper wrapper-content animated fadeIn">

    @{
        // consultor
        ConsultoresDominio consultor = ViewBag.Consultor;

        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralContato</h4>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-4">
                                <h2>@consultor.Nome</h2><br />
                                <address>
                                    <span class="green-section">

                                        @{
                                            bool pular_linha = false;

                                            if (consultor.Telefone1.Length > 0)
                                            {
                                                <i class="fa fa-phone"></i><br />@consultor.Telefone1<br /><br />
                                                pular_linha = true;
                                            }

                                            if (consultor.Telefone2.Length > 0)
                                            {
                                                <span>@Html.Raw(consultor.Telefone2)</span><br /><br />
                                                pular_linha = true;
                                            }

                                            if (consultor.Telefone3.Length > 0)
                                            {
                                                <span>@Html.Raw(consultor.Telefone3)</span><br /><br />
                                                pular_linha = true;
                                            }

                                            if (consultor.Celular.Length > 0)
                                            {
                                                <i class="fa fa-whatsapp"></i><br />@consultor.Celular<br /><br />
                                                pular_linha = true;
                                            }

                                            if (consultor.Email.Length > 0)
                                            {
                                                <a href="mailto:@consultor.Email" class="green-section"><i class="fa fa-envelope"></i><br />@consultor.Email</a><br /><br />
                                                pular_linha = true;
                                            }

                                            if (consultor.Site.Length > 0)
                                            {
                                                <a href="@consultor.Site" class="green-section"><i class="fa fa-cloud"></i><br />@consultor.Site</a>
                                                pular_linha = true;
                                            }

                                            if (pular_linha)
                                            {
                                                <br /><br />
                                            }
                                        }

                                        <i class="fa fa-map-marker"></i><br />@consultor.Endereco<br />
                                        @consultor.Cidade • @consultor.Estado<br /><br />
                                        CEP @consultor.CEP

                                        <br />

                                    </span>
                                </address>
                            </div>
                            <div class="col-lg-8">
                                <br />
                                <div class="quadro_mapa">
                                    <div class="google-map" id="map"></div>
                                </div>
                                <br />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

</div>


@section Scripts {


    <!--
     You need to include this script on any page that has a Google Map.
     When using Google Maps on your own site you MUST signup for your own API key at:
     https://developers.google.com/maps/documentation/javascript/tutorial#api_key
     After your sign up replace the key in the URL below..
     key = AIzaSyDQTpXj82d8UpCi97wzo_nKXL7nYrd4G70
    -->
    @Scripts.Render("https://maps.googleapis.com/maps/api/js?key=AIzaSyA5X1xuge28QKLNI9glB7Ki2cYnqx5bqaM")


    <script type="text/javascript">

    // When the window has finished loading google map
    google.maps.event.addDomListener(window, 'load', init);

    var map;
    var marker;

    function init() {
        // Options for Google map
        // More info see: https://developers.google.com/maps/documentation/javascript/reference#MapOptions
        var mapOptions1 = {
            zoom: 15,
            //center: new google.maps.LatLng(-23.5971252, -46.6385829),
            // Style for Google Maps
            styles: [{ "featureType": "water", "stylers": [{ "saturation": 43 }, { "lightness": -11 }, { "hue": "#0088ff" }] }, { "featureType": "road", "elementType": "geometry.fill", "stylers": [{ "hue": "#ff0000" }, { "saturation": -100 }, { "lightness": 99 }] }, { "featureType": "road", "elementType": "geometry.stroke", "stylers": [{ "color": "#808080" }, { "lightness": 54 }] }, { "featureType": "landscape.man_made", "elementType": "geometry.fill", "stylers": [{ "color": "#ece2d9" }] }, { "featureType": "poi.park", "elementType": "geometry.fill", "stylers": [{ "color": "#ccdca1" }] }, { "featureType": "road", "elementType": "labels.text.fill", "stylers": [{ "color": "#767676" }] }, { "featureType": "road", "elementType": "labels.text.stroke", "stylers": [{ "color": "#ffffff" }] }, { "featureType": "poi", "stylers": [{ "visibility": "off" }] }, { "featureType": "landscape.natural", "elementType": "geometry.fill", "stylers": [{ "visibility": "on" }, { "color": "#b8cb93" }] }, { "featureType": "poi.park", "stylers": [{ "visibility": "on" }] }, { "featureType": "poi.sports_complex", "stylers": [{ "visibility": "on" }] }, { "featureType": "poi.medical", "stylers": [{ "visibility": "on" }] }, { "featureType": "poi.business", "stylers": [{ "visibility": "simplified" }] }]
        };

        // Get all html elements for map
        var mapElement1 = document.getElementById('map');

        // geocoder
        geocoder = new google.maps.Geocoder();

        // Create the Google Map using elements
        map = new google.maps.Map(mapElement1, mapOptions1);

        // verifica se possui latitude e longitude
        var consultor = @Html.Raw(Json.Encode(ViewBag.Consultor));
            var latitude = consultor.Latitude;
            var longitude = consultor.Longitude;

            if (latitude != 0.0 && longitude != 0.0)
            {
                // cria marcador
                marker = new google.maps.Marker({
                    position: { lat: latitude, lng: longitude },
                    map: map
                });

                // posiciona no marcador
                map.setCenter(new google.maps.LatLng(latitude, longitude));
            }
            else
            {
                // tenta posicionar pelo endereco
                EncontrarLatLong();
            }
        }

        function EncontrarLatLong() {

            // consultor
            var consultor = @Html.Raw(Json.Encode(ViewBag.Consultor));

            // monta endereco
            var endereco_completo = "";
            var endereco = consultor.Endereco;
            if (endereco.length > 0)
            {
                endereco_completo += endereco;
            }

            // CEP
            var CEP = consultor.CEP;
            if (CEP.length > 0) {
                if (endereco_completo.length > 0) {
                    endereco_completo += " - " + CEP;
                }
                else {
                    endereco_completo += CEP;
                }
            }

            // cidade
            var cidade = consultor.Cidade;
            if (cidade.length > 0)
            {
                if (endereco_completo.length > 0) {
                    endereco_completo += " - " + cidade;
                }
                else {
                    endereco_completo += cidade;
                }
            }

            // estado
            var estado = consultor.Estado;
            if (estado.length > 0) {
                if (endereco_completo.length > 0) {
                    endereco_completo += " - " + estado;
                }
                else {
                    endereco_completo += estado;
                }
            }

            // encontra endereco
            geocoder.geocode({ 'address': endereco_completo }, function (resultado, status) {
                if (status == google.maps.GeocoderStatus.OK) {

                    // pega latitude e longitude
                    var latitude = resultado[0].geometry.location.lat();
                    var longitude = resultado[0].geometry.location.lng();

                    // apaga marcador antigo
                    if (marker != null)
                    {
                        marker.setMap(null);
                    }

                    // atualiza marcador
                    marker = new google.maps.Marker({
                        // The below line is equivalent to writing:
                        // position: new google.maps.LatLng(-34.397, 150.644)
                        position: { lat: latitude, lng: longitude },
                        map: map
                    });

                    // posiciona no marcador
                    map.setCenter(new google.maps.LatLng(latitude, longitude));

                }
                else {
                    //alert('Erro ao converter endereço: ' + status);
                }
            });
        }



    </script>
}
