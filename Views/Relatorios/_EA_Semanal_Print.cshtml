﻿<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.EA</title>

    <link href="~/Content/bootstrap.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/style.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/plugins/c3/c3.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/plugins/iCheck/custom.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/Relatorios.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/RelatoriosPDF.css" rel="stylesheet" type="text/css" />
    <link href="~/fonts/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="~/Scripts/jquery-2.1.1.min.js" charset="utf-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/d3/d3.min.js" charset="utf-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/c3/c3.min.js"></script>
    <script type="text/javascript" src="~/Scripts/plugins/iCheck/icheck.min.js"></script>

</head>

@if (ViewBag.Retorno == 2)
{
    <div class="row">
        <div class="col-lg-12">
            <div class="linha cabecalho">
                <div class="coluna1">
                    <p><img src="~/Logos/@ViewBag._LogoConsultor" style="height:42px; width:auto;" /></p>
                </div>

                <div class="coluna2">
                    <h3>@ViewBag.ClienteNome</h3>
                    @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                </div>

                <div class="coluna3">
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.EA - @SmartEnergy.Resources.RelatoriosTexts.RelatSemanal</h4>
                    <p>@ViewBag.DataTextoAtual</p>
                </div>
            </div>
        </div>
    </div>

    <br /><br /><br /><br /><br /><br />
    <div class="row">
        <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
            <i class="fa fa-warning fa-5x"></i>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>@SmartEnergy.Resources.RelatoriosTexts.NaoExistemRegistros</h2>
        </div>
    </div>
    <br /><br /><br /><br />

}
else
{

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="~/Logos/@ViewBag._LogoConsultor" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.EA - @SmartEnergy.Resources.RelatoriosTexts.RelatSemanal</h4>
                        <p>@ViewBag.DataTextoAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <br />

        @{
            if (ViewBag.showGrafico)
            {
                <div class="row">
                    <div class="relat-grafico" style="height:450px; width:800px; margin-left:50px;">
                        <div id="grafico-ea"></div>
                        <div class='chart-legend'>
                            <div class='legend-scale-dem'>
                                <ul class='legend-labels' style="margin-top: 0px;">
                                    <li><span style='background:#0000FF !important;' id="ckDomingo"></span> @SmartEnergy.Resources.ComumTexts.domingo</li>
                                    <li><span style='background:#00FF00 !important;' id="ckSegunda"></span> @SmartEnergy.Resources.ComumTexts.segunda</li>
                                    <li><span style='background:#FF0000 !important;' id="ckTerca"></span> @SmartEnergy.Resources.ComumTexts.terca</li>
                                    <li><span style='background:#FF7F00 !important;' id="ckQuarta"></span> @SmartEnergy.Resources.ComumTexts.quarta</li>
                                    <li><span style='background:#FF00FF !important;' id="ckQuinta"></span> @SmartEnergy.Resources.ComumTexts.quinta</li>
                                    <li><span style='background:#000000 !important;' id="ckSexta"></span> @SmartEnergy.Resources.ComumTexts.sexta</li>
                                    <li><span style='background:#00FFFF !important;' id="ckSabado"></span> @SmartEnergy.Resources.ComumTexts.sabado</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="relat-coluna">
                        <div class="relat-valorAtual relat-ea">
                            <label style="margin-left:150px;">&nbsp;&nbsp;<input type="checkbox" class="i-checks" id="check_med" checked>&nbsp;&nbsp;Valores Médios</label>
                            <label style="margin-left:70px;">&nbsp;&nbsp;<input type="checkbox" class="i-checks" id="check_max" checked>&nbsp;&nbsp;Valores Máximos</label>
                            <label style="margin-left:150px;">&nbsp;&nbsp;<input type="checkbox" class="i-checks" id="check_min" checked>&nbsp;&nbsp;Valores Mínimos</label>
                        </div>
                    </div>
                </div>
            }
        }

        <hr />

        <div class="row">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-ea" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.MedioSemana</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Maximo</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Minimo</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@ViewBag.NomeGrandeza</td>
                                <td class="relat-ea">@ViewBag.VMedSemana @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.DataTextoAtual)</small></td>
                                <td class="relat-preto">@ViewBag.VMaxSemana @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMaxSemana_DataHora)</small></td>
                                <td class="relat-preto">@ViewBag.VMinSemana @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMinSemana_DataHora)</small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    @if (ViewBag.showTabela)
    {
        var pagina = 0;

        for (pagina = 0; pagina < 2; pagina++)
        {
            <div class="page-break"></div>
            
            <div class="row">

                <br /><br />

                <div class="col-lg-12">
                    <div class="panel panel-default">

                        @{
                            var k = 0;

                            var dia_semana = new string[] { SmartEnergy.Resources.ComumTexts.domingo, 
                                                                        SmartEnergy.Resources.ComumTexts.segunda,
                                                                        SmartEnergy.Resources.ComumTexts.terca,
                                                                        SmartEnergy.Resources.ComumTexts.quarta,
                                                                        SmartEnergy.Resources.ComumTexts.quinta,
                                                                        SmartEnergy.Resources.ComumTexts.sexta,
                                                                        SmartEnergy.Resources.ComumTexts.sabado
                                                                    };
                        }

                        <table class="table no-margins tabela-grafico-pdf">
                            <thead>
                                <tr>
                                    <th style="width:16%; text-align:center; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>
                                    <th style="width:1%;"></th>

                                    @for (k = 0; k < 7; k++)
                                    {
                                        <th style="width:12%; text-align:center; font-size:12px;">@dia_semana[k]<br />@ViewBag.Dias[k]<br />(@ViewBag.UnidadeGrandeza)</th>
                                    }    

                                </tr>
                            </thead>

                            <tbody>

                                @{
                                    var i = 0;
                                    var j = 0;
                                    var vmed = "---";
                                    var vmax = "---";
                                    var vmin = "---";

                                    for (i = 0; i < 12; i++)
                                    {
                                        j = i + 1 + (12 * pagina);

                                        <tr class="tabela_valores" style="font-size:12px;">
                                            <td style="text-align:center;" class="relat-preto">@ViewBag.Horas[j]</td>
                                            <td style="text-align:left;" class="relat-preto">Med<br />Máx<br />Mín</td>

                                            @for (k = 0; k < 7; k++)
                                            {
                                                vmed = string.Format("{0:#,##0.00}", ViewBag.VMed[k, j]);
                                                vmax = string.Format("{0:#,##0.00}", ViewBag.VMax[k, j]);
                                                vmin = string.Format("{0:#,##0.00}", ViewBag.VMin[k, j]);

                                                <td style="text-align:center; font-size:12px;" class="relat-ea">@vmed<br />@vmax<br />@vmin</td>
                                            }

                                        </tr>
                                    }
                                }

                            </tbody>
                        </table>

                    </div>
                </div>
            </div>
        }        
    }

    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>



<script type="text/javascript">

    // dias da semana
    var diasSemana = [];

    diasSemana.push('Domingo');
    diasSemana.push('Segunda');
    diasSemana.push('Terca');
    diasSemana.push('Quarta');
    diasSemana.push('Quinta');
    diasSemana.push('Sexta');
    diasSemana.push('Sabado');
    diasSemana.push('Período');

    // cores
    var coresdiasSemana = [];

    coresdiasSemana.push('#0000FF');
    coresdiasSemana.push('#00FF00');
    coresdiasSemana.push('#FF0000');
    coresdiasSemana.push('#FF7F00');
    coresdiasSemana.push('#FF00FF');
    coresdiasSemana.push('#000000');
    coresdiasSemana.push('#00FFFF');

    // c_names
    var c_names = [];

    c_names.push("Relat_SemanalDomingo");
    c_names.push("Relat_SemanalSegunda");
    c_names.push("Relat_SemanalTerca");
    c_names.push("Relat_SemanalQuarta");
    c_names.push("Relat_SemanalQuinta");
    c_names.push("Relat_SemanalSexta");
    c_names.push("Relat_SemanalSabado");

    // id_names
    var id_names = [];

    id_names.push("#ckDomingo");
    id_names.push("#ckSegunda");
    id_names.push("#ckTerca");
    id_names.push("#ckQuarta");
    id_names.push("#ckQuinta");
    id_names.push("#ckSexta");
    id_names.push("#ckSabado");


    $(document).ready(function () {

        // entrada analogica
        var dataX = [];
        var labelX = [];
        var dataDom_Max = [], dataSeg_Max = [], dataTer_Max = [], dataQua_Max = [], dataQui_Max = [], dataSex_Max = [], dataSab_Max = [];
        var dataDom_Med = [], dataSeg_Med = [], dataTer_Med = [], dataQua_Med = [], dataQui_Med = [], dataSex_Med = [], dataSab_Med = [];
        var dataDom_Min = [], dataSeg_Min = [], dataTer_Min = [], dataQua_Min = [], dataQui_Min = [], dataSex_Min = [], dataSab_Min = [];

        // copia valores
        var VMax = @Html.Raw(Json.Encode(@ViewBag.VMax));
        var VMed = @Html.Raw(Json.Encode(@ViewBag.VMed));
        var VMin = @Html.Raw(Json.Encode(@ViewBag.VMin));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));
        var NomeGrandeza = @Html.Raw(Json.Encode(@ViewBag.NomeGrandeza));
        var UnidadeGrandeza = @Html.Raw(Json.Encode(@ViewBag.UnidadeGrandeza));

        // valor minimo e máximo
        var minimoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMin));
        var maximoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMax));

        dataX.push('x');
        dataDom_Max.push(['data1']);  dataSeg_Max.push(['data2']);  dataTer_Max.push(['data3']);  dataQua_Max.push(['data4']);  dataQui_Max.push(['data5']);  dataSex_Max.push(['data6']);  dataSab_Max.push(['data7']);
        dataDom_Med.push(['data11']); dataSeg_Med.push(['data12']); dataTer_Med.push(['data13']); dataQua_Med.push(['data14']); dataQui_Med.push(['data15']); dataSex_Med.push(['data16']); dataSab_Med.push(['data17']);
        dataDom_Min.push(['data21']); dataSeg_Min.push(['data22']); dataTer_Min.push(['data23']); dataQua_Min.push(['data24']); dataQui_Min.push(['data25']); dataSex_Min.push(['data26']); dataSab_Min.push(['data27']);

        for (i = 0; i < 26; i++)
        {
            // X
            dataX.push(Datas[i]);

            dataDom_Max.push([ VMax[i]        ]);
            dataSeg_Max.push([ VMax[i+26]     ]);
            dataTer_Max.push([ VMax[i+(2*26)] ]);
            dataQua_Max.push([ VMax[i+(3*26)] ]);
            dataQui_Max.push([ VMax[i+(4*26)] ]);
            dataSex_Max.push([ VMax[i+(5*26)] ]);
            dataSab_Max.push([ VMax[i+(6*26)] ]);

            dataDom_Med.push([ VMed[i]        ]);
            dataSeg_Med.push([ VMed[i+26]     ]);
            dataTer_Med.push([ VMed[i+(2*26)] ]);
            dataQua_Med.push([ VMed[i+(3*26)] ]);
            dataQui_Med.push([ VMed[i+(4*26)] ]);
            dataSex_Med.push([ VMed[i+(5*26)] ]);
            dataSab_Med.push([ VMed[i+(6*26)] ]);

            dataDom_Min.push([ VMin[i]        ]);
            dataSeg_Min.push([ VMin[i+26]     ]);
            dataTer_Min.push([ VMin[i+(2*26)] ]);
            dataQua_Min.push([ VMin[i+(3*26)] ]);
            dataQui_Min.push([ VMin[i+(4*26)] ]);
            dataSex_Min.push([ VMin[i+(5*26)] ]);
            dataSab_Min.push([ VMin[i+(6*26)] ]);
        }

        // valores label X
        labelX.push(Datas[1]);
        for (i = 3; i <= 24; i+=3)
        {
            labelX.push(Datas[i]);
        }

        Data = [dataX,
                dataDom_Max,dataSeg_Max,dataTer_Max,dataQua_Max,dataQui_Max,dataSex_Max,dataSab_Max,
                dataDom_Med,dataSeg_Med,dataTer_Med,dataQua_Med,dataQui_Med,dataSex_Med,dataSab_Med,
                dataDom_Min,dataSeg_Min,dataTer_Min,dataQua_Min,dataQui_Min,dataSex_Min,dataSab_Min];

        // desenha grafico
        c3_generate('#grafico-ea', Data);

        // label dias da semana
        todosDiaSemana();

        // labels
        $('.chart-legend').css("display", "block");


        function c3_generate(chart_name, dados) {

            var chart = c3.generate({

                bindto: chart_name,
                size: {
                    height: 420
                },
                padding: {
                    top: 8,
                    right: 10,
                    bottom: 30,
                    left: 43
                },
                data: {
                    x: 'x',
                    xFormat : '%Y-%m-%d %H:%M:%S',
                    columns: dados,
                    types: {
                        data1:  'line', data2:  'line', data3:  'line', data4:  'line', data5:  'line', data6:  'line', data7:  'line',
                        data11: 'line', data12: 'line', data13: 'line', data14: 'line', data15: 'line', data16: 'line', data17: 'line',
                        data21: 'line', data22: 'line', data23: 'line', data24: 'line', data25: 'line', data26: 'line', data27: 'line'
                    },
                    colors: {
                        data1:  '#0000FF', data2:  '#00FF00', data3:  '#FF0000', data4:  '#FF7F00', data5:  '#FF00FF', data6:  '#000000', data7:  '#00FFFF',
                        data11: '#0000FF', data12: '#00FF00', data13: '#FF0000', data14: '#FF7F00', data15: '#FF00FF', data16: '#000000', data17: '#00FFFF',
                        data21: '#0000FF', data22: '#00FF00', data23: '#FF0000', data24: '#FF7F00', data25: '#FF00FF', data26: '#000000', data27: '#00FFFF'
                    },
                },
                point: {
                    r:3
                },
                legend: {
                    show: false
                },
                axis: {
                    x:  {
                        type: 'timeseries',
                        tick: {
                            outer: false,
                            values: labelX,
                            format: function (x) {

                                // horas e minutos
                                var hours = x.getHours();
                                var minutes = x.getMinutes();

                                hours = hours < 10 ? '0'+hours : hours;
                                minutes = minutes < 10 ? '0'+minutes : minutes;
                                var strTime = hours + ':' + minutes;

                                // verifica se nao deve mostrar label
                                if(hours==3 || hours==9 || hours==15 || hours==21)
                                    return "";

                                // retorna hora e minuto
                                return strTime;
                            }
                        },
                        // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                        // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                        padding: -1000*60*28
                    },
                    y:  {
                        min: minimoValor,
                        max: maximoValor,
                        padding: {
                            top: 0,
                            bottom: 0
                        },
                        tick: {
                            outer: false,
                            format: function (d) {
                                return parseInt(d);
                            }
                        }
                    }
                },
                grid: {
                    x: {
                        show: false
                    }
                },
                tooltip: {
                    format: {
                        /*...*/
                    },
                    contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                        var $$ = this, config = $$.config,
                            titleFormat = config.tooltip_format_title || defaultTitleFormat,
                            nameFormat = config.tooltip_format_name || function (name) { return name; },
                            valueFormat = config.tooltip_format_value || defaultValueFormat,
                            text, i, title, value, name, bgcolor;

                        for (i = 0; i < d.length; i++) {
                            if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                            // titulo
                            if (! text) {

                                // split string and create array.
                                var arr = Datas[d[0].index].split(/-|\s|:/);

                                // decrease month value by 1
                                var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                                // horas e minutos
                                var hours = data.getHours();
                                var minutes = data.getMinutes();

                                hours = hours < 10 ? '0'+hours : hours;
                                minutes = minutes < 10 ? '0'+minutes : minutes;
                                title = hours + ':' + minutes;

                                text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='4' style='background-color:#1C84C6'>" + title + "</th></tr>" : "");
                                text += "<tr><th style='background-color:#1C84C6'></th>";
                                text += "<th style='background-color:#1C84C6'>Máximo</th>";
                                text += "<th style='background-color:#1C84C6'>Médio</th>";
                                text += "<th style='background-color:#1C84C6'>Mínimo</th></tr>";
                            }

                            // pega o indice
                            var indice = d[i].name.toString().substr(4, 2);

                            // verifica se eh dia da semana
                            if( indice < 8)
                            {
                                name = diasSemana[indice-1];
                                bgcolor = coresdiasSemana[indice-1];
                                valueMax = VMax[d[0].index+((indice-1)*26)].toFixed(2).replace('.', ',');
                                valueMed = VMed[d[0].index+((indice-1)*26)].toFixed(2).replace('.', ',');
                                valueMin = VMin[d[0].index+((indice-1)*26)].toFixed(2).replace('.', ',');

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                                text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                                text += "<td class='value'>" + valueMax + " " + UnidadeGrandeza + "</td>";
                                text += "<td class='value'>" + valueMed + " " + UnidadeGrandeza + "</td>";
                                text += "<td class='value'>" + valueMin + " " + UnidadeGrandeza + "</td>";
                                text += "</tr>";
                            }
                        }

                        return text + "</table>";
                    }
                }
            });

            // salva chart
            $(chart_name).data('c3-chart', chart);
        }

        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });
    });

    function todosDiaSemana() {

        // dias da semana
        var i;

        for(i=0;i<7;i++)
        {
            alteraDiaSemana(i,false,0);
        }
    }

    function alteraDiaSemana(dia_semana, alterar, serie) {

        // le cookie dia da semana
        var estado = 1;

        switch(dia_semana)
        {
            case 0:
                estado = Math.ceil(@ViewBag.Relat_SemanalDomingo);
                break;

            case 1:
                estado = Math.ceil(@ViewBag.Relat_SemanalSegunda);
                break;

            case 2:
                estado = Math.ceil(@ViewBag.Relat_SemanalTerca);
                break;

            case 3:
                estado = Math.ceil(@ViewBag.Relat_SemanalQuarta);
                break;

            case 4:
                estado = Math.ceil(@ViewBag.Relat_SemanalQuinta);
                break;

            case 5:
                estado = Math.ceil(@ViewBag.Relat_SemanalSexta);
                break;

            case 6:
                estado = Math.ceil(@ViewBag.Relat_SemanalSabado);
                break;
        }

        // le cookie serie Max
        var serie_max = Math.ceil(@ViewBag.Relat_SemanalMax);
        document.getElementById("check_max").checked = ((serie_max==1) ? true : false);

        // le cookie serie Med
        var serie_med = Math.ceil(@ViewBag.Relat_SemanalMed);
        document.getElementById("check_med").checked = ((serie_med==1) ? true : false);

        // le cookie serie Min
        var serie_min = Math.ceil(@ViewBag.Relat_SemanalMin);
        document.getElementById("check_min").checked = ((serie_min==1) ? true : false);

        // data series
        var chart_ea = $('#grafico-ea').data('c3-chart');

        // verifica estado
        if( estado == 1 )
        {
            if(serie == 0)
            {
                if( serie_max == 1 ) { chart_ea.show(["data" + (dia_semana+1)])  } else { chart_ea.hide(["data" + (dia_semana+1)])  };
                if( serie_med == 1 ) { chart_ea.show(["data" + (dia_semana+11)]) } else { chart_ea.hide(["data" + (dia_semana+11)]) };
                if( serie_min == 1 ) { chart_ea.show(["data" + (dia_semana+21)]) } else { chart_ea.hide(["data" + (dia_semana+21)]) };
            }
            else
            {
                chart_ea.show(["data" + (dia_semana+serie)]);
            }

        }
        else
        {
            if(serie == 0)
            {
                chart_ea.hide(["data" + (dia_semana+1)]);
                chart_ea.hide(["data" + (dia_semana+11)]);
                chart_ea.hide(["data" + (dia_semana+21)]);
            }
            else
            {
                chart_ea.hide(["data" + (dia_semana+serie)]);
            }

        }
    }

</script>

}
    