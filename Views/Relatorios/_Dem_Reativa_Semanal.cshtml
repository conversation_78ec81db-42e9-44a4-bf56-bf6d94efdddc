﻿<style>
    .icone_semana {
    padding: 0px 7px;
    font-size: 14px;
    float: left; 
    color: white;
}

.c3-area {
  opacity: 0.1 !important;
}

.c3-line {
    stroke-width: 1px;
}

</style>

<div class="row">
    <div class="col-lg-4 col-md-12 relatorio_real">
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.DemandaMaximaSemana</h5>
                        <h1>@ViewBag.Dem_MaxFPC <span>@ViewBag.UnidadeReativo</span></h1>
                        <h6>@ViewBag.Dem_MaxFPC_DataHora</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.DemandaMaximaSemana</h5>
                        <h1>@ViewBag.Dem_MaxFPI <span>@ViewBag.UnidadeReativo</span></h1>
                        <h6>@ViewBag.Dem_MaxFPI_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.DemandaMaximaSemana</h5>
                        <h1>@ViewBag.Dem_MaxP <span>@ViewBag.UnidadeReativo</span></h1>
                        <h6>@ViewBag.Dem_MaxP_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-12 simulacao" style="display:none;">
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.DemandaMaximaSemana</h5>
                        <h1>@ViewBag.Dem_MaxFPC_Sim <span>@ViewBag.UnidadeReativo</span></h1>
                        <h6>@ViewBag.Dem_MaxFPC_DataHora_Sim</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.DemandaMaximaSemana</h5>
                        <h1>@ViewBag.Dem_MaxFPI_Sim <span>@ViewBag.UnidadeReativo</span></h1>
                        <h6>@ViewBag.Dem_MaxFPI_DataHora_Sim</h6>
                    </div>
                </div>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.DemandaMaximaSemana</h5>
                        <h1>@ViewBag.Dem_MaxP_Sim <span>@ViewBag.UnidadeReativo</span></h1>
                        <h6>@ViewBag.Dem_MaxP_DataHora_Sim</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-8 col-md-12">
        <div class="row">
            <div class="relat-grafico">
                <div class="relatorio_real" id="demanda-chart"></div>
                <div class="simulacao" id="demanda-chart-simulacao" style="display:none;"></div>
                <div class='chart-legend' style="display:none;">
                    <div class='legend-scale-dem'>
                        <ul class='legend-labels' style="margin-top: 0px;">
                            <li><a href="#" onclick="alteraDiaSemana(0,true);"><span style='background:#0000FF;' id="ckDomingo"><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.domingo</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(1,true);"><span style='background:#00FF00;' id="ckSegunda"><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.segunda</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(2,true);"><span style='background:#FF0000;' id="ckTerca"  ><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.terca</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(3,true);"><span style='background:#FF7F00;' id="ckQuarta" ><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.quarta</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(4,true);"><span style='background:#FF00FF;' id="ckQuinta" ><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.quinta</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(5,true);"><span style='background:#000000;' id="ckSexta"  ><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.sexta</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(6,true);"><span style='background:#00FFFF;' id="ckSabado" ><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.sabado</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@Html.Partial("_Relat_Botoes")

<br />
<div class="panel panel-title relatorio">
    <div class="panel-body">

        <div class="row">
            <div class="col-lg-12 relatorio_real">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:20%;"></th>
                                <th class="relat-capacitivo" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                                <th class="relat-preto" style="width:20%;"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMaxima</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MaxFPC @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MaxFPC_DataHora)</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MaxFPI @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MaxFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MaxP @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MaxP_DataHora)</small></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMedia</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MedFPC @ViewBag.UnidadeReativo<br /><small>&nbsp;</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MedFPI @ViewBag.UnidadeReativo<br /><small>&nbsp;</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MedP @ViewBag.UnidadeReativo</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMinima</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MinFPC @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MinFPC_DataHora)</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MinFPI @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MinFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MinP @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MinP_DataHora)</small></td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="col-lg-12 simulacao" style="display:none;">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:20%;"></th>
                                <th class="relat-capacitivo" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                                <th class="relat-preto" style="width:20%;"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMaxima</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MaxFPC_Sim @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MaxFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MaxFPI_Sim @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MaxFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MaxP_Sim @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MaxP_DataHora_Sim)</small></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMedia</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MedFPC_Sim @ViewBag.UnidadeReativo<br /><small>&nbsp;</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MedFPI_Sim @ViewBag.UnidadeReativo<br /><small>&nbsp;</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MedP_Sim @ViewBag.UnidadeReativo</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMinima</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MinFPC_Sim @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MinFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MinFPI_Sim @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MinFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MinP_Sim @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MinP_DataHora_Sim)</small></td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal inmodal animated fadeIn" id="modalTabela" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-editar-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title"><i class="fa fa-list"></i>&nbsp;&nbsp;@SmartEnergy.Resources.RelatoriosTexts.TabelaRegistros</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-default relatorio_real">

                    @{
                        var k = 0;

                        var dia_semana = new string[] { SmartEnergy.Resources.ComumTexts.domingo, 
                                                                    SmartEnergy.Resources.ComumTexts.segunda,
                                                                    SmartEnergy.Resources.ComumTexts.terca,
                                                                    SmartEnergy.Resources.ComumTexts.quarta,
                                                                    SmartEnergy.Resources.ComumTexts.quinta,
                                                                    SmartEnergy.Resources.ComumTexts.sexta,
                                                                    SmartEnergy.Resources.ComumTexts.sabado
                                                                };
                    }

                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:16%;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>

                                @for (k = 0; k < 7; k++)
                                {
                                    <th style="width:12%;">@dia_semana[k]<br />@ViewBag.Dias[k]</th>
                                }    

                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var i = 0;
                                var j = 0;
                                var classe = "relat-semreg";
                                var demandaAtv = "---";

                                for (i = 0; i < 96; i++)
                                {
                                    j = i + 1;

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="relat-preto">@ViewBag.Horas[j]</td>

                                        @for (k = 0; k < 7; k++)
                                        {
                                            switch ((int)ViewBag.Periodo[k, j])
                                            {
                                                case 0:
                                                    classe = "relat-ponta";
                                                    break;

                                                case 1:
                                                    classe = "relat-indutivo";
                                                    break;

                                                case 2:
                                                    classe = "relat-capacitivo";
                                                    break;

                                                default:
                                                case 3:
                                                    classe = "relat-semreg";
                                                    break;
                                            }
                                            
                                            if (ViewBag.Periodo[k, j] == 3)
                                            {
                                                demandaAtv = "---";
                                            }
                                            else
                                            {
                                                demandaAtv = string.Format("{0:#,##0.0}", ViewBag.Demanda[k, j]);
                                            }
                                            
                                            <td style="text-align:center;" class="@classe">@demandaAtv</td>
                                        }    

                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>

                <div class="panel panel-default simulacao" style="display:none;">


                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:16%;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>

                                @for (k = 0; k < 7; k++)
                                {
                                    <th style="width:12%;">@dia_semana[k]<br />@ViewBag.Dias[k]</th>
                                }

                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var classe_sim = "relat-semreg";
                                var demandaAtv_sim = "---";

                                for (i = 0; i < 96; i++)
                                {
                                    j = i + 1;

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="relat-preto">@ViewBag.Horas_Sim[j]</td>

                                        @for (k = 0; k < 7; k++)
                                        {
                                            switch ((int)ViewBag.Periodo[k, j])
                                            {
                                                case 0:
                                                    classe_sim = "relat-ponta";
                                                    break;

                                                case 1:
                                                    classe_sim = "relat-indutivo";
                                                    break;

                                                case 2:
                                                    classe_sim = "relat-capacitivo";
                                                    break;

                                                default:
                                                case 3:
                                                    classe_sim = "relat-semreg";
                                                    break;
                                            }

                                            if (ViewBag.Periodo[k, j] == 3)
                                            {
                                                demandaAtv_sim = "---";
                                            }
                                            else
                                            {
                                                demandaAtv_sim = string.Format("{0:#,##0.0}", ViewBag.Demanda_Sim[k, j]);
                                            }

                                            <td style="text-align:center;" class="@classe_sim">@demandaAtv_sim</td>
                                        }

                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>



            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
            </div>
        </div>
    </div>
</div>


@Html.Partial("_EventosModal")

@Html.Partial("_ObservacoesModal")

   
<script type="text/javascript">

    $(document).ready(function () {

        // dias da semana
        var diasSemana = [];

        diasSemana.push('Domingo');
        diasSemana.push('Segunda');
        diasSemana.push('Terça');
        diasSemana.push('Quarta');
        diasSemana.push('Quinta');
        diasSemana.push('Sexta');
        diasSemana.push('Sábado');
        diasSemana.push('Período');

        // cores
        var coresdiasSemana = [];

        coresdiasSemana.push('#0000FF');
        coresdiasSemana.push('#00FF00');
        coresdiasSemana.push('#FF0000');
        coresdiasSemana.push('#FF7F00');
        coresdiasSemana.push('#FF00FF');
        coresdiasSemana.push('#000000');
        coresdiasSemana.push('#00FFFF');

        // demanda
        var dataX = [];
        var labelX = [];
        var dataContratoP = [];
        var dataContratoFP = [];
        var dataToleranciaP = [];
        var dataToleranciaFP = [];
        var dataDemandaDom = [];
        var dataDemandaSeg = [];
        var dataDemandaTer = [];
        var dataDemandaQua = [];
        var dataDemandaQui = [];
        var dataDemandaSex = [];
        var dataDemandaSab = [];

        // copia valores
        var Demanda = @Html.Raw(Json.Encode(@ViewBag.Demanda));
        var ContratoP = @Html.Raw(Json.Encode(@ViewBag.ContratoP));
        var ContratoFP = @Html.Raw(Json.Encode(@ViewBag.ContratoFP));
        var ToleranciaP = @Html.Raw(Json.Encode(@ViewBag.ToleranciaP));
        var ToleranciaFP = @Html.Raw(Json.Encode(@ViewBag.ToleranciaFP));
        var Periodo = @Html.Raw(Json.Encode(@ViewBag.Periodo));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));

        var maximoDem = @Html.Raw(Json.Encode(@ViewBag.DemMaxGrafico));

        var UnidadeReativo = @Html.Raw(Json.Encode(@ViewBag.UnidadeReativo));

        dataX.push('x');
        dataDemandaDom.push(['data1']);
        dataDemandaSeg.push(['data2']);
        dataDemandaTer.push(['data3']);
        dataDemandaQua.push(['data4']);
        dataDemandaQui.push(['data5']);
        dataDemandaSex.push(['data6']);
        dataDemandaSab.push(['data7']);
        dataContratoFP.push(['data8']);
        dataContratoP.push(['data9']);
        dataToleranciaFP.push(['data10']);
        dataToleranciaP.push(['data11']);

        for (i = 0; i < 98; i++)
        {
            // X
            dataX.push(Datas[i]);

            dataDemandaDom.push([Demanda[i]]);
            dataDemandaSeg.push([Demanda[i+98]]);
            dataDemandaTer.push([Demanda[i+(2*98)]]);
            dataDemandaQua.push([Demanda[i+(3*98)]]);
            dataDemandaQui.push([Demanda[i+(4*98)]]);
            dataDemandaSex.push([Demanda[i+(5*98)]]);
            dataDemandaSab.push([Demanda[i+(6*98)]]);

            dataContratoP.push([ContratoP]);
            dataContratoFP.push([ContratoFP]);

            dataToleranciaP.push([ToleranciaP]);
            dataToleranciaFP.push([ToleranciaFP]);
        }

        // valores label X
        labelX.push(Datas[1]);
        for (i = 12; i <= 96; i+=12)
        {
            labelX.push(Datas[i]);
        }

        var Data = [dataX,dataDemandaDom,dataDemandaSeg,dataDemandaTer,dataDemandaQua,dataDemandaQui,dataDemandaSex,dataDemandaSab,dataContratoFP,dataContratoP,dataToleranciaFP,dataToleranciaP];

        var chart = c3.generate({

            bindto: '#demanda-chart',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 10,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'step',
                    data2: 'step',
                    data3: 'step',
                    data4: 'step',
                    data5: 'step',
                    data6: 'step',
                    data7: 'step',
                    data8: 'step',
                    data9: 'step',
                    data10: 'step',
                    data11: 'step',
                },
                colors: {
                    data1: '#0000FF',
                    data2: '#00FF00',
                    data3: '#FF0000',
                    data4: '#FF7F00',
                    data5: '#FF00FF',
                    data6: '#000000',
                    data7: '#00FFFF',
                    data8: '#007F00',
                    data9: '#FF0000',
                    data10: '#007F00',
                    data11: '#FF0000'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*6
                },
                y:  {
                    max: maximoDem,
                    min: 0,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoDem < 100)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                },
                y: {
                    lines: [{value:0}]
                }
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < d.length; i++) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        // titulo
                        if (! text) {

                            // split string and create array.
                            var arr = Datas[d[0].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // horas e minutos
                            var hours = data.getHours();
                            var minutes = data.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            title = hours + ':' + minutes;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // pega o indice
                        var indice = d[i].name.toString().substr(4, 2);

                        // verifica se eh dia da semana
                        if( indice < 8)
                        {
                            name = diasSemana[indice-1];
                            bgcolor = coresdiasSemana[indice-1];
                            value = Demanda[d[0].index+((indice-1)*98)].toFixed(1);
                            value = value.replace('.', ',');

                            // verifica se sem registro
                            if( Periodo[d[0].index+((indice-1)*98)] == 3 )
                            {
                                value = '---';
                            }

                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                            text += "<td class='value'>" + value + " " + UnidadeReativo + "</td>";
                            text += "</tr>";
                        }
                    }

                    return text + "</table>";
                }
            }
        });

        //
        // GRAFICO SIMULACAO
        //

        // demanda simulacao
        var dataX_Sim = [];
        var labelX_Sim = [];
        var dataContratoP_Sim = [];
        var dataContratoFP_Sim = [];
        var dataToleranciaP_Sim = [];
        var dataToleranciaFP_Sim = [];
        var dataDemandaDom_Sim = [];
        var dataDemandaSeg_Sim = [];
        var dataDemandaTer_Sim = [];
        var dataDemandaQua_Sim = [];
        var dataDemandaQui_Sim = [];
        var dataDemandaSex_Sim = [];
        var dataDemandaSab_Sim = [];

        // copia valores
        var Demanda_Sim = @Html.Raw(Json.Encode(@ViewBag.Demanda_Sim));
        var ContratoP_Sim = @Html.Raw(Json.Encode(@ViewBag.ContratoP_Sim));
        var ContratoFP_Sim = @Html.Raw(Json.Encode(@ViewBag.ContratoFP));
        var ToleranciaP_Sim = @Html.Raw(Json.Encode(@ViewBag.ToleranciaP_Sim));
        var ToleranciaFP_Sim = @Html.Raw(Json.Encode(@ViewBag.ToleranciaFP_Sim));
        var Periodo_Sim = @Html.Raw(Json.Encode(@ViewBag.Periodo_Sim));
        var Datas_Sim = @Html.Raw(Json.Encode(@ViewBag.Datas_Sim));

        dataX_Sim.push('x');
        dataDemandaDom_Sim.push(['data1']);
        dataDemandaSeg_Sim.push(['data2']);
        dataDemandaTer_Sim.push(['data3']);
        dataDemandaQua_Sim.push(['data4']);
        dataDemandaQui_Sim.push(['data5']);
        dataDemandaSex_Sim.push(['data6']);
        dataDemandaSab_Sim.push(['data7']);
        dataContratoFP_Sim.push(['data8']);
        dataContratoP_Sim.push(['data9']);
        dataToleranciaFP_Sim.push(['data10']);
        dataToleranciaP_Sim.push(['data11']);

        for (i = 0; i < 98; i++)
        {
            // X
            dataX_Sim.push(Datas_Sim[i]);

            dataDemandaDom_Sim.push([Demanda_Sim[i]]);
            dataDemandaSeg_Sim.push([Demanda_Sim[i+98]]);
            dataDemandaTer_Sim.push([Demanda_Sim[i+(2*98)]]);
            dataDemandaQua_Sim.push([Demanda_Sim[i+(3*98)]]);
            dataDemandaQui_Sim.push([Demanda_Sim[i+(4*98)]]);
            dataDemandaSex_Sim.push([Demanda_Sim[i+(5*98)]]);
            dataDemandaSab_Sim.push([Demanda_Sim[i+(6*98)]]);

            dataContratoP_Sim.push([ContratoP_Sim]);
            dataContratoFP_Sim.push([ContratoFP_Sim]);

            dataToleranciaP_Sim.push([ToleranciaP_Sim]);
            dataToleranciaFP_Sim.push([ToleranciaFP_Sim]);
        }

        // valores label X simulacao
        labelX_Sim.push(Datas_Sim[1]);
        for (i = 12; i <= 96; i+=12)
        {
            labelX_Sim.push(Datas_Sim[i]);
        }

        var Data = [dataX_Sim,dataDemandaDom_Sim,dataDemandaSeg_Sim,dataDemandaTer_Sim,dataDemandaQua_Sim,dataDemandaQui_Sim,dataDemandaSex_Sim,dataDemandaSab_Sim,dataContratoFP_Sim,dataContratoP_Sim,dataToleranciaFP_Sim,dataToleranciaP_Sim];

        var chart_sim = c3.generate({

            bindto: '#demanda-chart-simulacao',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 10,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'step',
                    data2: 'step',
                    data3: 'step',
                    data4: 'step',
                    data5: 'step',
                    data6: 'step',
                    data7: 'step',
                    data8: 'step',
                    data9: 'step',
                    data10: 'step',
                    data11: 'step',
                },
                colors: {
                    data1: '#0000FF',
                    data2: '#00FF00',
                    data3: '#FF0000',
                    data4: '#FF7F00',
                    data5: '#FF00FF',
                    data6: '#000000',
                    data7: '#00FFFF',
                    data8: '#007F00',
                    data9: '#FF0000',
                    data10: '#007F00',
                    data11: '#FF0000'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX_Sim,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*6
                },
                y:  {
                    max: maximoDem,
                    min: 0,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoDem < 100)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                },
                y: {
                    lines: [{value:0}]
                }
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < d.length; i++) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        // titulo
                        if (! text) {

                            // split string and create array.
                            var arr = Datas_Sim[d[0].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // horas e minutos
                            var hours = data.getHours();
                            var minutes = data.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            title = hours + ':' + minutes;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // pega o indice
                        var indice = d[i].name.toString().substr(4, 2);

                        // verifica se eh dia da semana
                        if( indice < 8)
                        {
                            name = diasSemana[indice-1];
                            bgcolor = coresdiasSemana[indice-1];
                            value = Demanda_Sim[d[0].index+((indice-1)*98)].toFixed(1);
                            value = value.replace('.', ',');

                            // verifica se sem registro
                            if( Periodo[d[0].index+((indice-1)*98)] == 3 )
                            {
                                value = '---';
                            }

                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + " - Simulação" + "</td>";
                            text += "<td class='value'>" + value + " " + UnidadeReativo + "</td>";
                            text += "</tr>";
                        }
                    }

                    return text + "</table>";
                }
            }
        });

        // salva chart
        $('#demanda-chart').data('c3-chart', chart);

        // salva chart simulacao
        $('#demanda-chart-simulacao').data('c3-chart', chart_sim);
        
        // label dias da semana
        todosDiaSemana();

        // verifica se apresenta simulacao
        ShowSimulacao();

        // labels
        $('.chart-legend').css("display", "block");

        $(".modal-dialog").draggable({
            handle: ".modal-editar-header"
        });


    });

    function getCookie(c_name) {

        // Get name followed by anything except a semicolon
        var cookiestring = RegExp("" + c_name + "[^;]+").exec(document.cookie);

        // Return everything after the equal sign, or an empty string if the cookie name not found
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./, "") : "");
    }

    function setCookie(c_name, value, expiresecs) {
        var exdate = new Date();
        exdate.setTime(exdate.getTime() + ((expiresecs) ? expiresecs * 1000 : 0));

        document.cookie = c_name + "=" + escape(value) + ((expiresecs == null) ? "" : ";expires=" + exdate.toGMTString()) + ";path=/";
    }

    function todosDiaSemana() {

        alteraDiaSemana(0,false);
        alteraDiaSemana(1,false);
        alteraDiaSemana(2,false);
        alteraDiaSemana(3,false);
        alteraDiaSemana(4,false);
        alteraDiaSemana(5,false);
        alteraDiaSemana(6,false);

    }

    function alteraDiaSemana(dia_semana, alterar) {

        var c_name,id_name,color_name;

        // dia da semana
        switch(dia_semana)
        {
            case 0:
                c_name = "Relat_SemanalDomingo";
                id_name = "#ckDomingo";
                color_name = "#0000FF";
                break;

            case 1:
                c_name = "Relat_SemanalSegunda";
                id_name = "#ckSegunda";
                color_name = "#00FF00";
                break;

            case 2:
                c_name = "Relat_SemanalTerca";
                id_name = "#ckTerca";
                color_name = "#FF0000";
                break;

            case 3:
                c_name = "Relat_SemanalQuarta";
                id_name = "#ckQuarta";
                color_name = "#FF7F00";
                break;

            case 4:
                c_name = "Relat_SemanalQuinta";
                id_name = "#ckQuinta";
                color_name = "#FF00FF";
                break;

            case 5:
                c_name = "Relat_SemanalSexta";
                id_name = "#ckSexta";
                color_name = "#000000";
                break;

            case 6:
                c_name = "Relat_SemanalSabado";
                id_name = "#ckSabado";
                color_name = "#00FFFF";
                break;
        }

        // le cookie
        var estado = getCookie(c_name);

        if(estado == "")
        {
            estado = 1;
        }

        // verifica se quer alterar
        if( alterar )
        {
            // se estiver ligado, desliga
            estado = (estado == 0) ? 1 : 0;
        }

        // data series
        var data = "data" + (dia_semana+1);
        var chart = $('#demanda-chart').data('c3-chart');

        // simulacao
        var chart_sim = $('#demanda-chart-simulacao').data('c3-chart');

        // checkbox aplica simulacao
        var simula = $('#aplica_simula').prop('checked');

        // verifica estado
        if( estado == 1 )
        {
            if (simula){
                chart_sim.show([data]);
            }
            else{
                chart.show([data]);
            }

            $(id_name).css("background", color_name);
        }
        else
        {
            if (simula){
                chart_sim.hide([data]);
            }
            else{
                chart.hide([data]);
            }    

            $(id_name).css("background", "white");
        }

        // salva em cookie
        setCookie(c_name, estado, null);
    }

    function selecionaDiaSemana(dia_semana) {

        var k,id_name,color_name;

        // data series
        var chart = $('#demanda-chart').data('c3-chart');

        // data series simulacao
        var chart_sim = $('#demanda-chart-simulacao').data('c3-chart');

        // dia da semana
        for(k=0; k < 7; k++)
        {
            switch(k)
            {
                case 0:
                    id_name = "#ckDomingo";
                    color_name = "#0000FF";
                    break;

                case 1:
                    id_name = "#ckSegunda";
                    color_name = "#00FF00";
                    break;

                case 2:
                    id_name = "#ckTerca";
                    color_name = "#FF0000";
                    break;

                case 3:
                    id_name = "#ckQuarta";
                    color_name = "#FF7F00";
                    break;

                case 4:
                    id_name = "#ckQuinta";
                    color_name = "#FF00FF";
                    break;

                case 5:
                    id_name = "#ckSexta";
                    color_name = "#000000";
                    break;

                case 6:
                    id_name = "#ckSabado";
                    color_name = "#00FFFF";
                    break;
            }

            // data series
            var data = "data" + (dia_semana+1);

            // checkbox aplica simulacao
            var simula = $('#aplica_simula').prop('checked');

            // verifica dia de semana selecionado
            if(k == dia_semana)
            {
                if (simula){
                    chart_sim.show([data]);
                }
                else{
                    chart.show([data]);
                }
                               
                $(id_name).css("background", color_name);
            }
            else
            {
                if (simula){
                    chart_sim.hide([data]);
                }
                else{
                    chart.hide([data]);
                }                                

                $(id_name).css("background", "white");
            }
        }
    }

    function ShowSimulacao () {

        // IDSimulacaoCenario
        var IDSimulacaoCenario = parseInt(@ViewBag._IDSimulacaoCenario);

        // verifica se ano existe nenhuma simulacao selecionada
        if (IDSimulacaoCenario == 0)
        {
            $("#aplica_simula").attr('disabled', true);
            $('#aplica_simula').prop('checked', false);
        }
        else
        {
            $("#aplica_simula").attr('disabled', false);   
        }

        // checkbox aplica simulacao
        var simula = $('#aplica_simula').prop('checked');

        // verifica se deve apresentar simulacao
        if (simula)
        {
            $('.simulacao').css("display", "block");
            $('.relatorio_real').css("display", "none");

            todosDiaSemana();
        }
        else
        {
            $('.simulacao').css("display", "none");
            $('.relatorio_real').css("display", "block");

            todosDiaSemana();
        }

        // salva em cookie
        setCookie("AplicaSimulacao", simula, null);
    }

</script>


    