﻿@using SmartEnergyLib.SQL

<div class="row">
    <div class="col-lg-12">

        @{
            Provisionamento_Tipo1_Tipo2 provisionamento = ViewBag.ProvisionamentoSemanal;

            string contrato = SmartEnergy.Resources.ContratosCCEETexts.FaturaCurtoPrazo;
            
            if (provisionamento != null)
            {
                contrato = "Contrato " + provisionamento.Contrato_Codigo;
            }
        }                            

        <div class="panel panel-title">
            <div class="panel-heading">
                <h4>@contrato</h4>
            </div>
            <div class="panel-body">
                <div class="row">

                    <table class="table table-striped table-bordered table-hover dataTables-fatLongoPrazo">
                        <thead>
                            <tr>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.UnidadeConsumidora</th>
                                <th>CNPJ</th>
                                <th>@SmartEnergy.Resources.ContratosCCEETexts.VolumeFaturado (MWh)</th>
                                <th>@SmartEnergy.Resources.ContratosCCEETexts.PrecoEnergia (R$/MWh)</th>
                                <th>@SmartEnergy.Resources.FinancasTexts.Total (R$)</th>
                            </tr>
                        </thead>
                        <tbody>

                            @{
                                // unidades consumidoras                                
                                List<Provisionamento_UnidadesConsumidoras> unidadesConsumidoras = ViewBag.unidadesConsumidoras;
                                
                                if (unidadesConsumidoras != null)
                                {
                                    foreach (Provisionamento_UnidadesConsumidoras unid in unidadesConsumidoras)
                                    {
                                        <tr>
                                            <td><b>@unid.RazaoSocial</b><br />@unid.PontoMedicao</td>
                                            <td>@unid.CNPJ</td>
                                            <td>@string.Format("{0:#,##0.000}", unid.Fatura_LongoPrazo_Quantidade)</td>
                                            <td>@string.Format("{0:#,##0.00}", unid.Fatura_LongoPrazo_PrecoEnergia)</td>
                                            <td>@string.Format("{0:#,##0.00}", unid.Fatura_LongoPrazo_Valor)</td>
                                        </tr>
                                    }
                                }
                            }

                        </tbody>
                    </table>

                </div>
            </div>
        </div>
    </div>
</div>

<br />
<div class="row">
    <div class="col-lg-12">
        <i class="fa fa-exclamation-triangle" style="font-size:20px;"></i><span style="font-size:large; padding-left:8px;"><b>O provisionamento da fatura de energia não contempla os custos com ICMS.</b></span>
    </div>
</div>


<script type="text/javascript">

    $(document).ready(function () {

        $('.dataTables-fatLongoPrazo').DataTable({
            "iDisplayLength": 18,
            dom: 'tp',

            "bAutoWidth": false,
            "aoColumnDefs": [
                    { "aTargets": [0], "sType": "portugues", "bSortable": false, "bSearchable": false },
                    { "aTargets": [1], "sType": "portugues", "bSortable": false, "bSearchable": false },
                    { "aTargets": [2], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [3], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [4], "sType": "numero", "bSortable": false, "bSearchable": false }
            ],

            "aoColumns": [
                { sWidth: "20%" },
                { sWidth: "20%" },
                { sWidth: "20%" },
                { sWidth: "20%" },
                { sWidth: "20%" }
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

    });

</script>
