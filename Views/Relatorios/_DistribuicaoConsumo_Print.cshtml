﻿@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.DistribuicaoConsumo</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />

    <link href="~/Content/bootstrap.min.css" rel="stylesheet" />
    <link href="~/Content/style.css" rel="stylesheet" />
    <link href="~/Content/plugins/c3/c3.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/Relatorios.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/RelatoriosPDF.css" rel="stylesheet" type="text/css" />
    <link href="~/fonts/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="~/Scripts/jquery-2.1.1.min.js" charset="UTF-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/d3/d3.min.js" charset="UTF-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/c3/c3.min.js"></script>

</head>

@{
    List<DistribuicaoConsumoValor> valores_resultado = ViewBag.valores_resultado;
}

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho" style="height:90px !important;">
                    <div class="coluna1">
                        <p><img src="~/Logos/@ViewBag._LogoConsultor" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        <b>@ViewBag.NomeDistribuicao</b>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.DistribuicaoConsumo</h4>
                        <p>@ViewBag.DataTextoInicio<br />@ViewBag.DataTextoFim</p>
                    </div>
                </div>
            </div>
        </div>

        <br />

        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-default" style="height:440px;">
                    <div class="panel-body">
                        <div class="relat-grafico" style="height:400px; width:600px; margin-left:170px;">
                            <div id="grafico-pizza"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">

                <div class="panel panel-default">
                    <div class="panel-body">

                        @{
                            double ConsumoTotal = 0.0;

                            if (ViewBag.ConsumoTotal != null)
                            {
                                ConsumoTotal = ViewBag.ConsumoTotal;    
                            }
                        }

                        <br />

                        <div class="row">
                            <div class="col-sm-6">
                                <div class="relat-valorAtual relat-capacitivo">
                                    <h5>@SmartEnergy.Resources.RelatoriosTexts.ConsumoTotal</h5>
                                    <h1>@string.Format("{0:#,##0.0}", ConsumoTotal) <span>kWh</span></h1>
                                </div>
                            </div>
                        </div>

                        <br /><br /><br />

                        <div class="row">
                            <div class="col-sm-12">

                                <table class="table table-striped table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.GrupoMedicoes</th>
                                            <th>@SmartEnergy.Resources.RelatoriosTexts.Consumo (kWh)</th>
                                            <th>@SmartEnergy.Resources.AnalisesTexts.Percentual (%)</th>
                                        </tr>
                                    </thead>
                                    <tbody>

                                        @{
                                            if (valores_resultado != null)
                                            {
                                                foreach (DistribuicaoConsumoValor valor in valores_resultado)
                                                {
                                                    string consumo = string.Format("{0:#,##0.0}", valor.Consumo);
                                                    string percentual = string.Format("{0:#,##0.0}", valor.Porcentagem);

                                                    <tr>
                                                        <td>@valor.NomeGrupoMedicoes</td>
                                                        <td>@consumo</td>
                                                        <td>@percentual</td>
                                                    </tr>

                                                }
                                            }
                                        }

                                    </tbody>
                                </table>

                            </div>                    
                        </div>

                    </div>
                </div>
            </div>
        </div>

    </div>

    <div class="row">
        <div class="col-lg-12" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>


<script type="text/javascript">

    $(document).ready(function () {

        //
        // PIZZA
        //

        var numGruposMedicoes = @Html.Raw(Json.Encode(@ViewBag.numGruposMedicoes));

        if (numGruposMedicoes > 0)
        {
            // consumo
            var Data = [];
            var dataNomes = [];
            var dataConsumo = [];
            var dataPorcentagem = [];

            // copia valores
            var Nomes = @Html.Raw(Json.Encode(@ViewBag.Nomes));
            var Consumo = @Html.Raw(Json.Encode(@ViewBag.Consumo));
            var Porcentagem = @Html.Raw(Json.Encode(@ViewBag.Porcentagem));

            var numGruposMedicoes = @Html.Raw(Json.Encode(@ViewBag.numGruposMedicoes));


            dataNomes.push(['Nomes']);
            dataConsumo.push(['Consumo']);
            dataPorcentagem.push(['Porcentagem']);

            for (i = 0; i < numGruposMedicoes; i++)
            {
                dataNomes.push([unescape(encodeURIComponent(Nomes[i]))]);
                dataConsumo.push([Consumo[i]]);
                dataPorcentagem.push([Porcentagem[i]]);

                Data.push([Nomes[i], Porcentagem[i]]);
            }


            var chart = c3.generate({
                bindto: '#grafico-pizza',
                size: {
                    height: 400
                },
                padding: {
                    top: 0,
                    right: 0,
                    bottom: 0,
                    left: 0
                },
                legend: {
                    position: 'right'
                },
                point: {
                    show: false,
                    sensitivity: 1000
                },
                data: {
                    columns: Data,
                    type: 'pie'
                },
                pie: {
                    label: {
                        format: function (value, ratio, id) {
                            return value.toFixed(1) + '%';
                        }
                    }
                }
            });
        }

        // força um resize por causa do grafico deslocado
        window.dispatchEvent(new Event('resize'));

    });

</script>


