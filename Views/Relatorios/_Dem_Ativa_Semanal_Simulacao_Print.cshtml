﻿<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.DemandaAtiva @SmartEnergy.Resources.RelatoriosTexts.Simulacao</title>

    <link href="~/Content/bootstrap.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/style.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/plugins/c3/c3.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/Relatorios.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/RelatoriosPDF.css" rel="stylesheet" type="text/css" />
    <link href="~/fonts/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="~/Scripts/jquery-2.1.1.min.js" charset="utf-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/d3/d3.min.js" charset="utf-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/c3/c3.min.js"></script>

</head>

@if (ViewBag.Retorno == 2)
{
    <div class="row">
        <div class="col-lg-12">
            <div class="linha cabecalho">
                <div class="coluna1">
                    <p><img src="~/Logos/@ViewBag._LogoConsultor" style="height:42px; width:auto;" /></p>
                </div>

                <div class="coluna2">
                    <h3>@ViewBag.ClienteNome</h3>
                    @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                </div>

                <div class="coluna3">
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.DemandaAtiva - @SmartEnergy.Resources.RelatoriosTexts.RelatSemanal (@SmartEnergy.Resources.RelatoriosTexts.Simulacao)</h4>
                    <p>@ViewBag.DataTextoAtual</p>
                </div>
            </div>
        </div>
    </div>

    <br /><br /><br /><br /><br /><br />
    <div class="row">
        <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
            <i class="fa fa-warning fa-5x"></i>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>@SmartEnergy.Resources.RelatoriosTexts.NaoExistemRegistros</h2>
        </div>
    </div>
    <br /><br /><br /><br />

}
else
{

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="~/Logos/@ViewBag._LogoConsultor" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.DemandaAtiva - @SmartEnergy.Resources.RelatoriosTexts.RelatSemanal (@SmartEnergy.Resources.RelatoriosTexts.Simulacao)</h4>
                        <p>@ViewBag.DataTextoAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <br />

        @{
            if (ViewBag.showGrafico)
            {
                <div class="row">
                    <div class="relat-grafico" style="height:490px; width:800px; margin-left:50px;">
                        <div id="demanda-chart-simulacao"></div>
                        <div class='chart-legend' style="display:none;">
                            <div class='legend-scale-dem'>
                                <ul class='legend-labels' style="margin-top: 0px;">
                                    <li><span style='background:#0000FF !important;' id="ckDomingo"></span> @SmartEnergy.Resources.ComumTexts.domingo</li>
                                    <li><span style='background:#00FF00 !important;' id="ckSegunda"></span> @SmartEnergy.Resources.ComumTexts.segunda</li>
                                    <li><span style='background:#FF0000 !important;' id="ckTerca"></span> @SmartEnergy.Resources.ComumTexts.terca</li>
                                    <li><span style='background:#FF7F00 !important;' id="ckQuarta"></span> @SmartEnergy.Resources.ComumTexts.quarta</li>
                                    <li><span style='background:#FF00FF !important;' id="ckQuinta"></span> @SmartEnergy.Resources.ComumTexts.quinta</li>
                                    <li><span style='background:#000000 !important;' id="ckSexta"></span> @SmartEnergy.Resources.ComumTexts.sexta</li>
                                    <li><span style='background:#00FFFF !important;' id="ckSabado"></span> @SmartEnergy.Resources.ComumTexts.sabado</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }

        <hr />

        <div class="row" style="margin-top: -15px;">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:20%;"></th>
                                <th class="relat-capacitivo" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                                <th class="relat-preto" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMaxima</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MaxFPC_Sim @ViewBag.UnidadeDemanda<br /><small>(@ViewBag.Dem_MaxFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MaxFPI_Sim @ViewBag.UnidadeDemanda<br /><small>(@ViewBag.Dem_MaxFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MaxP_Sim @ViewBag.UnidadeDemanda<br /><small>(@ViewBag.Dem_MaxP_DataHora_Sim)</small></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMedia</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MedFPC_Sim @ViewBag.UnidadeDemanda<br /><small>&nbsp;</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MedFPI_Sim @ViewBag.UnidadeDemanda<br /><small>&nbsp;</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MedP_Sim @ViewBag.UnidadeDemanda</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMinima</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MinFPC_Sim @ViewBag.UnidadeDemanda<br /><small>(@ViewBag.Dem_MinFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MinFPI_Sim @ViewBag.UnidadeDemanda<br /><small>(@ViewBag.Dem_MinFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MinP_Sim @ViewBag.UnidadeDemanda<br /><small>(@ViewBag.Dem_MinP_DataHora_Sim)</small></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.Contrato</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_ContratoFP_Sim @ViewBag.UnidadeDemanda (+@ViewBag.Tol_ContratoFP_Sim %)<br /><small>&nbsp;</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_ContratoFP_Sim @ViewBag.UnidadeDemanda (+@ViewBag.Tol_ContratoFP_Sim %)<br /><small>&nbsp;</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_ContratoP_Sim @ViewBag.UnidadeDemanda (+@ViewBag.Tol_ContratoP_Sim %)</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.Consumo<br />&nbsp;</td>
                                <td class="relat-capacitivo">@ViewBag.ConsFPC_Sim @ViewBag.UnidadeConsumo</td>
                                <td class="relat-fponta">@ViewBag.ConsFPI_Sim @ViewBag.UnidadeConsumo</td>
                                <td class="relat-ponta">@ViewBag.ConsP_Sim @ViewBag.UnidadeConsumo</td>
                                <td class="relat-preto">@ViewBag.ConsTotal_Sim @ViewBag.UnidadeConsumo</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    @{
        if (ViewBag.showTabela)
        {
            var pagina = 0;

            for (pagina = 0; pagina < 2; pagina++)
            {
                <div class="page-break"></div>
        
                <div class="row">

                    <br /><br />

                    <div class="col-lg-12">
                        <div class="panel panel-default">

                            @{
                                var k = 0;

                                var dia_semana = new string[] { SmartEnergy.Resources.ComumTexts.domingo, 
                                                                            SmartEnergy.Resources.ComumTexts.segunda,
                                                                            SmartEnergy.Resources.ComumTexts.terca,
                                                                            SmartEnergy.Resources.ComumTexts.quarta,
                                                                            SmartEnergy.Resources.ComumTexts.quinta,
                                                                            SmartEnergy.Resources.ComumTexts.sexta,
                                                                            SmartEnergy.Resources.ComumTexts.sabado
                                                                        };
                            }

                            <table class="table no-margins tabela-grafico-pdf">
                                <thead>
                                    <tr>
                                        <th style="width:16%; text-align:center; padding:2px; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>

                                        @for (k = 0; k < 7; k++)
                                        {
                                            <th style="width:12%; text-align:center; padding:2px; font-size:12px;">@dia_semana[k]<br />@ViewBag.Dias[k]</th>
                                        }    

                                    </tr>
                                </thead>

                                <tbody>

                                    @{
                                        var i = 0;
                                        var j = 0;
                                        var classe_sim = "relat-semreg";
                                        var demandaAtv_sim = "---";

                                        for (i = 0; i < 48; i++)
                                        {
                                            j = i + 1 + (pagina * 48);

                                            <tr class="tabela_valores" style="font-size:12px;">

                                                <td style="text-align:center; padding:2px; font-size:12px;" class="relat-preto">@ViewBag.Horas_Sim[j]</td>

                                                @for (k = 0; k < 7; k++)
                                                {
                                                    switch ((int)ViewBag.Periodo_Sim[k, j])
                                                    {
                                                        case 0:
                                                            classe_sim = "relat-ponta";
                                                            break;

                                                        case 1:
                                                            classe_sim = "relat-indutivo";
                                                            break;

                                                        case 2:
                                                            classe_sim = "relat-capacitivo";
                                                            break;

                                                        default:
                                                        case 3:
                                                            classe_sim = "relat-semreg";
                                                            break;
                                                    }
                                            
                                                    if (ViewBag.Periodo_Sim[k, j] == 3)
                                                    {
                                                        demandaAtv_sim = "---";
                                                    }
                                                    else
                                                    {
                                                        demandaAtv_sim = string.Format("{0:#,##0.0}", ViewBag.Demanda_Sim[k, j]);
                                                    }
                                            
                                                    <td style="text-align:center; padding:2px; font-size:12px;" class="@classe_sim">@demandaAtv_sim</td>
                                                }    

                                            </tr>
                                        }
                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>        
                </div>   
            }     
        }
    }

    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>



<script type="text/javascript">

    // dias da semana
    var diasSemana = [];

    diasSemana.push('Domingo');
    diasSemana.push('Segunda');
    diasSemana.push('Terça');
    diasSemana.push('Quarta');
    diasSemana.push('Quinta');
    diasSemana.push('Sexta');
    diasSemana.push('Sábado');
    diasSemana.push('Período');

    // cores
    var coresdiasSemana = [];

    coresdiasSemana.push('#0000FF');
    coresdiasSemana.push('#00FF00');
    coresdiasSemana.push('#FF0000');
    coresdiasSemana.push('#FF7F00');
    coresdiasSemana.push('#FF00FF');
    coresdiasSemana.push('#000000');
    coresdiasSemana.push('#00FFFF');

    // c_names
    var c_names = [];

    c_names.push("Relat_SemanalDomingo");
    c_names.push("Relat_SemanalSegunda");
    c_names.push("Relat_SemanalTerca");
    c_names.push("Relat_SemanalQuarta");
    c_names.push("Relat_SemanalQuinta");
    c_names.push("Relat_SemanalSexta");
    c_names.push("Relat_SemanalSabado");

    $(document).ready(function () {

        // demanda simulacao
        var dataX_Sim = [];
        var labelX_Sim = [];
        var dataContratoP_Sim = [];
        var dataContratoFP_Sim = [];
        var dataToleranciaP_Sim = [];
        var dataToleranciaFP_Sim = [];
        var dataDemandaDom_Sim = [];
        var dataDemandaSeg_Sim = [];
        var dataDemandaTer_Sim = [];
        var dataDemandaQua_Sim = [];
        var dataDemandaQui_Sim = [];
        var dataDemandaSex_Sim = [];
        var dataDemandaSab_Sim = [];

        // copia valores simulacao
        var Demanda_Sim = @Html.Raw(Json.Encode(@ViewBag.Demanda_Sim));
        var ContratoP_Sim = @Html.Raw(Json.Encode(@ViewBag.ContratoP_Sim));
        var ContratoFP_Sim = @Html.Raw(Json.Encode(@ViewBag.ContratoFP_Sim));
        var ToleranciaP_Sim = @Html.Raw(Json.Encode(@ViewBag.ToleranciaP_Sim));
        var ToleranciaFP_Sim = @Html.Raw(Json.Encode(@ViewBag.ToleranciaFP_Sim));
        var Periodo_Sim = @Html.Raw(Json.Encode(@ViewBag.Periodo_Sim));
        var Datas_Sim = @Html.Raw(Json.Encode(@ViewBag.Datas_Sim));

        var maximoDem = @Html.Raw(Json.Encode(@ViewBag.DemMaxGrafico));

        // simulacao
        dataX_Sim.push('x');
        dataDemandaDom_Sim.push(['data1']);
        dataDemandaSeg_Sim.push(['data2']);
        dataDemandaTer_Sim.push(['data3']);
        dataDemandaQua_Sim.push(['data4']);
        dataDemandaQui_Sim.push(['data5']);
        dataDemandaSex_Sim.push(['data6']);
        dataDemandaSab_Sim.push(['data7']);
        dataContratoFP_Sim.push(['data8']);
        dataContratoP_Sim.push(['data9']);
        dataToleranciaFP_Sim.push(['data10']);
        dataToleranciaP_Sim.push(['data11']);

        for (i = 0; i < 98; i++)
        {
            // X
            dataX_Sim.push(Datas_Sim[i]);

            // simulacao 
            dataDemandaDom_Sim.push([Demanda_Sim[i]]);
            dataDemandaSeg_Sim.push([Demanda_Sim[i+98]]);
            dataDemandaTer_Sim.push([Demanda_Sim[i+(2*98)]]);
            dataDemandaQua_Sim.push([Demanda_Sim[i+(3*98)]]);
            dataDemandaQui_Sim.push([Demanda_Sim[i+(4*98)]]);
            dataDemandaSex_Sim.push([Demanda_Sim[i+(5*98)]]);
            dataDemandaSab_Sim.push([Demanda_Sim[i+(6*98)]]);

            dataContratoP_Sim.push([ContratoP_Sim]);
            dataContratoFP_Sim.push([ContratoFP_Sim]);

            dataToleranciaP_Sim.push([ToleranciaP_Sim]);
            dataToleranciaFP_Sim.push([ToleranciaFP_Sim]);
        }

        // valores label X
        labelX_Sim.push(Datas_Sim[1]);
        for (i = 12; i <= 96; i+=12)
        {
            labelX_Sim.push(Datas_Sim[i]);
        }

        var Data = [dataX_Sim, dataDemandaDom_Sim, dataDemandaSeg_Sim, dataDemandaTer_Sim, dataDemandaQua_Sim, dataDemandaQui_Sim, dataDemandaSex_Sim, dataDemandaSab_Sim, dataContratoFP_Sim, dataContratoP_Sim, dataToleranciaFP_Sim, dataToleranciaP_Sim];

        var chart_sim = c3.generate({

            bindto: '#demanda-chart-simulacao',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 10,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'step',
                    data2: 'step',
                    data3: 'step',
                    data4: 'step',
                    data5: 'step',
                    data6: 'step',
                    data7: 'step',
                    data8: 'step',
                    data9: 'step',
                    data10: 'step',
                    data11: 'step',
                },
                colors: {
                    data1: '#0000FF',
                    data2: '#00FF00',
                    data3: '#FF0000',
                    data4: '#FF7F00',
                    data5: '#FF00FF',
                    data6: '#000000',
                    data7: '#00FFFF',
                    data8: '#007F00',
                    data9: '#FF0000',
                    data10: '#007F00',
                    data11: '#FF0000',
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX_Sim,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*6
                },
                y:  {
                    max: maximoDem,
                    min: 0,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoDem < 100)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                },
                y: {
                    lines: [{value:0}]
                }
            },
        });

        // salva chart
        $('#demanda-chart-simulacao').data('c3-chart', chart_sim);

        // label dias da semana
        todosDiaSemana();

        // labels
        $('.chart-legend').css("display", "block");

    });

    function todosDiaSemana() {

        // dias da semana
        var i;

        for(i=0;i<7;i++)
        {
            alteraDiaSemana(i,false);
        }
    }

    function alteraDiaSemana(dia_semana, alterar) {

        // le cookie dia da semana
        var estado = 1;

        switch(dia_semana)
        {
            case 0:
                estado = Math.ceil(@ViewBag.Relat_SemanalDomingo);
                break;

            case 1:
                estado = Math.ceil(@ViewBag.Relat_SemanalSegunda);
                break;

            case 2:
                estado = Math.ceil(@ViewBag.Relat_SemanalTerca);
                break;

            case 3:
                estado = Math.ceil(@ViewBag.Relat_SemanalQuarta);
                break;

            case 4:
                estado = Math.ceil(@ViewBag.Relat_SemanalQuinta);
                break;

            case 5:
                estado = Math.ceil(@ViewBag.Relat_SemanalSexta);
                break;

            case 6:
                estado = Math.ceil(@ViewBag.Relat_SemanalSabado);
                break;
        }

        // data series
        var chart_sim = $('#demanda-chart-simulacao').data('c3-chart');

        // verifica estado
        if( estado == 1 )
        {
            chart_sim.show(["data" + (dia_semana+1)]);

        }
        else
        {
            chart_sim.hide(["data" + (dia_semana+1)]);

        }
    }

</script>

}
    