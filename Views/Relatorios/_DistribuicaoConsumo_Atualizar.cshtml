﻿@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    int IDDistribuicaoConsumo = 0;

    if (ViewBag._IDDistribuicaoConsumo != null)
    {
        IDDistribuicaoConsumo = ViewBag._IDDistribuicaoConsumo;
    }

    List<DistribuicaoConsumoDominio> listaDistribuicoes = ViewBag.listaDistribuicoes;
    
    int num_distribuicao = 0;
    
    if (listaDistribuicoes != null)
    {
        num_distribuicao = listaDistribuicoes.Count();
    }
    
    @Html.Hidden("TemDistribuicao", num_distribuicao)
}

<div class="panel-heading">

    <div style="text-align:center;">
        <h4>@SmartEnergy.Resources.RelatoriosTexts.DistribuicaoConsumo</h4>
    </div>

    <div class="pull-left relat-navega">
        <h4>
            <a class="link_mes_menos">
                <i class="fa fa-angle-double-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.RelatoriosTexts.AnoAnterior></i>
            </a>
            <a class="link_dia_menos">
                <i class="fa fa-angle-left" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.RelatoriosTexts.MesAnterior></i>
            </a>
            <a class="link_dia_mais">
                <i class="fa fa-angle-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.RelatoriosTexts.MesSeguinte></i>
            </a>
            <a class="link_mes_mais">
                <i class="fa fa-angle-double-right" data-toggle="tooltip" data-placement="left" title=@SmartEnergy.Resources.RelatoriosTexts.AnoSeguinte></i>
            </a>
        </h4>
    </div>
    <div class="pull-right relat-tools">

            <h4>
                @{
                    int IDTipoAcesso = ViewBag._IDTipoAcesso;
                    
                    // exceto Demo
                    if ( IDTipoAcesso != TIPO_ACESSO.DEMONSTRACAO )
                    {
                        <a id="configuracao_distribuicoes" href='@("/Relatorios/DistribuicaoConsumo_Configuracao?IDCliente=" + @ViewBag._IDCliente.ToString())'><i class="fa fa-cog" data-toggle="tooltip" data-placement="left" title="@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracao"></i></a>
                    }
                }

                <a id="print_distribuicoes" href="@Url.Action("DistribuicaoConsumo_Print", "Relatorios")" target="_blank"><i class="fa fa-print" data-toggle="tooltip" data-placement="left" title="@SmartEnergy.Resources.ComumTexts.BotaoImprimir"></i></a>
                <a id="email_distribuicoes" data-toggle="modal" href="#modalEMAIL"><i class="fa fa-envelope-o" data-toggle="tooltip" data-placement="left" title="Email"></i></a>
                <a id="pdf_distribuicoes" data-toggle="modal" href="#modalPDF"><i class="fa fa-file-pdf-o" data-toggle="tooltip" data-placement="left" title="PDF"></i></a>
                <a id="xls_distribuicoes" onclick="gerarXLS();"><i class="fa fa-file-excel-o" data-toggle="tooltip" data-placement="left" title="XLS"></i></a>
            </h4>
    </div>
</div>

<div class="panel-body">

    <div class="modal inmodal animated fadeIn" id="modalEMAIL" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">@SmartEnergy.Resources.ComumTexts.BotaoFechar</span></button>
                    <h4 class="modal-title"><i class="fa fa-envelope-o"></i>&nbsp;&nbsp;@SmartEnergy.Resources.RelatoriosTexts.DistribuicaoConsumo (Email)</h4>
                </div>

                <form id="formEMAIL" action="/Relatorios/DistribuicaoConsumo_EMAIL" method="get">

                    <div class="modal-body">
                        <div class="row">
                            <div class="form-group col-xs-6">
                                <label class="control-label">&nbsp;Email</label>
                                <input class="form-control" id="Email" name="Email" type="email" value="@ViewBag._EmailUsuario" />
                            </div>
                        </div>

                        <div class="row">
                            <div class="form-group col-xs-10">
                                <label class="control-label">&nbsp;@SmartEnergy.Resources.RelatoriosTexts.Assunto</label>
                                <input class="form-control" id="Assunto" name="Assunto" type="text" value="@SmartEnergy.Resources.RelatoriosTexts.DistribuicaoConsumo" />
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <span class="pull-left" style="font-weight:bold; font-size:14px; margin-top:7px;">@SmartEnergy.Resources.RelatoriosTexts.ProcessoPodeDemorar</span>
                        <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                        <button type="submit" class="btn btn-primary">@SmartEnergy.Resources.RelatoriosTexts.EnviarEmail</button>
                    </div>

                </form>
            </div>
        </div>
    </div>

    <div class="modal inmodal animated fadeIn" id="modalPDF" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">@SmartEnergy.Resources.ComumTexts.BotaoFechar</span></button>
                    <h4 class="modal-title"><i class="fa fa-file-pdf-o"></i>&nbsp;&nbsp;@SmartEnergy.Resources.RelatoriosTexts.DistribuicaoConsumo (PDF)</h4>
                </div>
                <div class="modal-body">

                </div>
                <div class="modal-footer">
                    <span class="pull-left" style="font-weight:bold; font-size:14px; margin-top:7px;">@SmartEnergy.Resources.RelatoriosTexts.ProcessoPodeDemorar</span>
                    <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                    <button type="button" class="btn btn-primary" onclick="gerarPDF();" data-dismiss="modal">@SmartEnergy.Resources.RelatoriosTexts.GerarPDF</button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-2">
            <div class="form-group" id="data_relat_inicio">
                <label class="control-label">&nbsp;@SmartEnergy.Resources.ComumTexts.Início</label>
                <div class="input-group">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span><input type="text" class="form-control" name="data_relat_inicio" value=@ViewBag.DataInicio>
                </div>
            </div>
        </div>

        <div class="col-lg-2">
            <div class="form-group" id="data_relat_fim">
                <label class="control-label">&nbsp;@SmartEnergy.Resources.ComumTexts.Fim</label>
                <div class="input-group">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span><input type="text" class="form-control" name="data_relat_fim" value=@ViewBag.DataFim>
                </div>
            </div>
        </div>

        <div class="col-lg-5">
            <div class="form-group">
                <label class="control-label">&nbsp;@SmartEnergy.Resources.RelatoriosTexts.DistribuicaoConsumo</label>
                <select class="form-control m-b" id="IDDistribuicaoConsumo" onchange="SelecionouDistribuicao()">

                    @{
                        bool nao_existe = true;

                        if (listaDistribuicoes != null)
                        {
                            foreach (DistribuicaoConsumoDominio distribuicao in listaDistribuicoes.OrderBy(x => x.Nome))
                            {                            
                                nao_existe = false;
                                var classe = "";

                                if (IDDistribuicaoConsumo == distribuicao.IDDistribuicaoConsumo) { classe = "selected"; } else { classe = ""; }
                                <option value="@distribuicao.IDDistribuicaoConsumo" @classe>@distribuicao.Nome</option>                                                
                            }
                        }
                    
                        if( nao_existe )
                        {                        
                            <option value="0" selected>Não existem Distribuições de Consumo configuradas</option>
                        }
                    }

                </select>
            </div>
        </div>     
    
        <div class="col-lg-3">
            <div class="form-group">
                <label class="control-label">&nbsp;</label>
                <a class="btn btn-primary btn-sm btn-block" href="javascript:Calcular()" style="color:#ffffff; font-weight:bold;">@SmartEnergy.Resources.FinancasTexts.BotaoCalcular</a>
            </div>
        </div>

    </div>
    
              
    <div id="relat_resultado" style="display:block;">
        <div class="row">

            <div class="apresenta-distribuicao">
                <div class="col-lg-12">
                    @Html.Partial("_DistribuicaoConsumo")
                </div>
            </div>

            <div class="configura-distribuicoes" style="display:none">
                <div class="col-lg-12">
                    <div class="panel panel-default">
                        <div class="panel-body">
                            <div class="col-lg-offset-4 col-lg-4 col-lg-offset-4" style="text-align:center;">
                                <span class="fa fa-cog" style="font-size: 100px"></span>
                                <a href='@("/Relatorios/DistribuicaoConsumo_Configuracao?IDCliente=" + @ViewBag._IDCliente.ToString())' class="btn btn-info pull-left" style="color:#ffffff; width:100%; height:80px; font-size:22px; margin-top: 40px; line-height: 60px; border-radius: 6px;">Adicionar @SmartEnergy.Resources.RelatoriosTexts.DistribuicaoConsumo</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
    


    @if (@ViewBag.listaErros != null)
    {
        if (@ViewBag.listaErros.Count > 0)
        {
            <div class="row">
                <div class="col-lg-12">
                    <div class="panel panel-danger">
                        <div class="panel-heading">
                            <h4>@SmartEnergy.Resources.SupervisaoTexts.Erros</h4><br />
                            <ul style="list-style-type:disc; margin-left: 20px;">
                                @foreach (var erro in @ViewBag.listaErros)
                                {
                                    <li>@erro</li>
                                }
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        }
    }

</div>


<script type="text/javascript">

    $(document).ready(function () {

        ShowListaDistribuicao();

        // IDDistribuicaoConsumo
        var IDDistribuicaoConsumo = @Html.Raw(Json.Encode(@ViewBag._IDDistribuicaoConsumo));        

        // salva em cookie
        setCookie("_IDDistribuicaoConsumo", IDDistribuicaoConsumo, null);

        // seta distribuição periodo selecionado
        $('#IDDistribuicaoConsumo').val(IDDistribuicaoConsumo);

        $('#data_relat_inicio').datetimepicker({
            locale: 'pt-BR',
            format: 'DD/MM/YYYY',
            showTodayButton: true,
            allowInputToggle: true
        });

        $('#data_relat_fim').datetimepicker({
            locale: 'pt-BR',
            format: 'DD/MM/YYYY',
            showTodayButton: true,
            allowInputToggle: true
        });


        //
        // VALIDACAO DOS CAMPOS
        //

        jQuery.validator.addMethod("alphanumeric", function (value, element) {
            return this.optional(element) || /^[\'\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\+\-\_\(\)," "]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

        $("#formEMAIL").validate({
            rules: {
                Assunto: {
                    required: true,
                    alphanumeric: true,
                    minlength: 5,
                    maxlength: 100
                },
                Email: {
                    required: true
                }
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            }
        });

        $("#formEMAIL").submit(function(e) {

            e.preventDefault();
            var $form = $(this);

            // verifica se entradas sao validas
            if(! $form.valid()) return false;

            // opcoes EMAIL
            var EmailUsuario = @Html.Raw(Json.Encode(@ViewBag._EmailUsuario));

            // opcoes EMAIL
            var destino = $('input[name=Email]').val();
            var assunto = $('input[name=Assunto]').val();

            // fecha janela email
            $('#modalEMAIL').modal('hide');

            // mostra tela wait
            waitingDialog.show(EMAIL_ENVIANDO);

            $.ajax(
            {
                type: 'GET',
                url: '/Relatorios/DistribuicaoConsumo_EMAIL',
                data: { 'destino': destino, 'assunto': assunto },
                contentType: "application/json;charset=utf-8",
                dataType: "json",
                cache: false,
                async: true,
                success: function (data) {

                    // esconde tela wait
                    waitingDialog.hide();

                    swal({
                        title: EMAIL_ENVIADO,
                        text: EMAIL_SUCESSO,
                        type: "success",
                        confirmButtonColor: "#18a689",
                        confirmButtonText: FECHAR,
                    }, function () {
                    });

                },
                error: function(response) {

                    // esconde tela wait
                    waitingDialog.hide();

                    swal({
                        title: ERRO,
                        text: EMAIL_ERRO,
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: FECHAR,
                    }, function () {
                    });
                }
            });
        });
    });
    
    function Atualiza(navega) {

        // pega distribuição selecionada
        var IDDistribuicaoConsumo = $("#IDDistribuicaoConsumo").val();

        // data nao utilizada
        var data = "01/01/2000";

        // aguarde
        $('.overlay_aguarde').toggle();

        $.ajax(
        {
            type: 'GET',
            url: '/Relatorios/_DistribuicaoConsumo_Atualizar',
            dataType: 'html',
            data: { 'IDDistribuicaoConsumo': IDDistribuicaoConsumo, 'Navegacao': navega, 'DataInicio': data, 'DataFim': data },
            cache: false,
            async: true,
            success: function (data) {

                $('.overlay_aguarde').toggle();                

                $('#atualizar').html(data);
            }
        });
    }


    function Calcular() {

        // pega distribuição selecionada
        var IDDistribuicaoConsumo = $("#IDDistribuicaoConsumo").val();

        // data
        var data_relat_inicio = document.querySelector('[name="data_relat_inicio"]').value;
        var data_relat_fim = document.querySelector('[name="data_relat_fim"]').value;

        // aguarde
        $('.overlay_aguarde').toggle();

        $.ajax(
        {
            type: 'GET',
            url: '/Relatorios/_DistribuicaoConsumo_Atualizar',
            dataType: 'html',
            data: { 'IDDistribuicaoConsumo': IDDistribuicaoConsumo, 'Navegacao': 100, 'DataInicio': data_relat_inicio, 'DataFim': data_relat_fim },
            cache: false,
            async: true,
            success: function (data) {

                $('.overlay_aguarde').toggle();            

                $('#atualizar').html(data);
            }
        });

    }

    $(".link_dia_menos").off().on("click", function (event) {
        event.stopPropagation();

        // dia anterior
        Atualiza(-3);
    });

    $(".link_dia_mais").off().on("click", function (event) {
        event.stopPropagation();

        // dia seguinte
        Atualiza(3);
    });

    $(".link_mes_menos").off().on("click", function (event) {
        event.stopPropagation();

        // mes anterior
        Atualiza(-4);
    });

    $(".link_mes_mais").off().on("click", function (event) {
        event.stopPropagation();

        // mes seguinte
        Atualiza(4);
    });


    function getCookie(c_name) {

        // Get name followed by anything except a semicolon
        var cookiestring = RegExp("" + c_name + "[^;]+").exec(document.cookie);

        // Return everything after the equal sign, or an empty string if the cookie name not found
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./, "") : "");
    }

    function setCookie(c_name, value, expiresecs) {
        var exdate = new Date();
        exdate.setTime(exdate.getTime() + ((expiresecs) ? expiresecs * 1000 : 0));

        document.cookie = c_name + "=" + escape(value) + ((expiresecs == null) ? "" : ";expires=" + exdate.toGMTString()) + ";path=/";
    }

    function SelecionouDistribuicao() {

        // pega distribuição selecionada
        var IDDistribuicaoConsumo = $("#IDDistribuicaoConsumo").val();

        // salva em cookie
        setCookie("_IDDistribuicaoConsumo", IDDistribuicaoConsumo, null);

        // atualiza na mesma data
        Atualiza(0);
    }

    function gerarPDF() {

        // mostra tela wait
        waitingDialog.show(PDF_GERANDO);

        $.ajax(
        {
            type: 'GET',
            url: '/Relatorios/DistribuicaoConsumo_PDF',
            data: { },
            contentType: "application/json;charset=utf-8",
            dataType: "json",
            cache: false,
            async: true,
            success: function (data) {

                // esconde tela wait
                waitingDialog.hide();

                // monta caminho
                var diretorio = window.location.origin + '/Temp/' + data.nomeArquivo;

                // apresenta PDF
                window.open(diretorio, '_blank');

                // ajusta largura depois de um tempo
                setTimeout(function () {

                    $('#dataTables-distribuicoes').dataTable().fnSort([[0, "asc"]]);

                }, 10);

            },
            error: function (response) {

                // esconde tela wait
                waitingDialog.hide();

                swal({
                    title: ERRO,
                    text: PDF_ERRO,
                    type: "warning",
                    confirmButtonColor: "#f8ac59",
                    confirmButtonText: FECHAR,
                }, function () {
                });
            }
        });
    }

    function gerarXLS() {

        $.ajax(
        {
            type: 'GET',
            url: '/Relatorios/DistribuicaoConsumo_XLS',
            data: {},
            contentType: "application/json;charset=utf-8",
            dataType: "json",
            cache: false,
            async: true,
            success: function (data) {

                // esconde tela wait
                waitingDialog.hide();

                // inicia download
                window.location = '/Relatorios/DistribuicaoConsumo_XLS_Download?fileGuid=' + data.FileGuid
                                  + '&filename=' + data.FileName;

            },
            error: function(xhr, status, error) {

                // esconde tela wait
                waitingDialog.hide();

                swal({
                    title: ERRO,
                    text: XLS_ERRO,
                    type: "warning",
                    confirmButtonColor: "#f8ac59",
                    confirmButtonText: FECHAR,
                }, function () {
                });
            }
        });

    }

    function ShowListaDistribuicao() {

        // verifica se tem distribuição 
        var TemDistribuicao = parseInt(document.getElementById("TemDistribuicao").value)

        if (TemDistribuicao == 0) {

            $('.apresenta-distribuicao').css("display", "none");            
            $('.relat-navega').css("display", "none");
            $('.configura-distribuicoes').css("display", "block");
            $('#print_distribuicoes').css("display", "none");            
            $('#email_distribuicoes').css("display", "none");            
            $('#pdf_distribuicoes').css("display", "none");            
            $('#xls_distribuicoes').css("display", "none"); 
            
        }
    }

</script>


