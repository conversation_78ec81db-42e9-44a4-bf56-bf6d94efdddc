﻿<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.FatorCarga</title>

    <link href="~/Content/bootstrap.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/style.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/plugins/c3/c3.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/Relatorios.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/RelatoriosPDF.css" rel="stylesheet" type="text/css" />
    <link href="~/fonts/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="~/Scripts/jquery-2.1.1.min.js" charset="utf-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/d3/d3.min.js" charset="utf-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/c3/c3.min.js"></script>

</head>

@if (ViewBag.Retorno == 2)
{
    <div class="row">
        <div class="col-lg-12">
            <div class="linha cabecalho">
                <div class="coluna1">
                    <p><img src="~/Logos/@ViewBag._LogoConsultor" style="height:42px; width:auto;" /></p>
                </div>

                <div class="coluna2">
                    <h3>@ViewBag.ClienteNome</h3>
                    @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                </div>

                <div class="coluna3">
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.FatorCarga - @SmartEnergy.Resources.RelatoriosTexts.RelatSemanal</h4>
                    <p>@ViewBag.DataTextoAtual</p>
                </div>
            </div>
        </div>
    </div>

    <br /><br /><br /><br /><br /><br />
    <div class="row">
        <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
            <i class="fa fa-warning fa-5x"></i>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>@SmartEnergy.Resources.RelatoriosTexts.NaoExistemRegistros</h2>
        </div>
    </div>
    <br /><br /><br /><br />

}
else
{

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="~/Logos/@ViewBag._LogoConsultor" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.FatorCarga - @SmartEnergy.Resources.RelatoriosTexts.RelatSemanal</h4>
                        <p>@ViewBag.DataTextoAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <br />

        @{
            if (ViewBag.showGrafico)
            {
                <br /><br />
                <div class="row">
                    <div class="relat-grafico" style="height:490px; width:800px; margin-left:50px;">
                        <div id="grafico-fatcarga" style="margin-top: -10px"></div>
                        <div class='chart-legend' style="display:none;">
                            <div class='legend-scale-dem'>
                                <ul class='legend-labels' style="margin-top: -25px;">
                                    <li><span style='background:#1C84C6 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                                    <li><span style='background:#007f00 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                                    <li><span style='background:#ff0000 !important;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }

        <hr />

        <div class="row" style="margin-top: -15px;">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatCargaSemana<br />&nbsp;</td>
                                <td class="relat-capacitivo">@ViewBag.FatCargaFPC_Semana %</td>
                                <td class="relat-fponta">@ViewBag.FatCargaFPI_Semana %</td>
                                <td class="relat-ponta">@ViewBag.FatCargaP_Semana %</td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMedia<br />&nbsp;</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MedFPC kW</td>
                                <td class="relat-fponta">@ViewBag.Dem_MedFPI kW</td>
                                <td class="relat-ponta">@ViewBag.Dem_MedP kW</td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMaxima</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MaxFPC kW<br /><small>(@ViewBag.Dem_MaxFPC_DataHora)</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MaxFPI kW<br /><small>(@ViewBag.Dem_MaxFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MaxP kW<br /><small>(@ViewBag.Dem_MaxP_DataHora)</small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    @{
        if (ViewBag.showTabela)
        {
            <div class="page-break"></div>
            
            <div class="row">

                <br /><br />

                <div class="col-lg-12">
                    <div class="panel panel-default">
                        <table class="table no-margins tabela-grafico-pdf">
                            <thead>
                                <tr>
                                    <th style="width:25%; text-align:center; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Semana</th>
                                    <th style="width:25%; text-align:center; font-size:12px;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                    <th style="width:25%; text-align:center; font-size:12px;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                    <th style="width:25%; text-align:center; font-size:12px;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                                </tr>
                            </thead>

                            <tbody>

                                @{
                                    var i = 0;
                                    var j = 0;
                                    var fatcargaFPC = "---";
                                    var fatcargaFPI = "---";
                                    var fatcargaP = "---";

                                    var dia_semana = new string[] { SmartEnergy.Resources.ComumTexts.domingo, 
                                                             SmartEnergy.Resources.ComumTexts.segunda,
                                                             SmartEnergy.Resources.ComumTexts.terca,
                                                             SmartEnergy.Resources.ComumTexts.quarta,
                                                             SmartEnergy.Resources.ComumTexts.quinta,
                                                             SmartEnergy.Resources.ComumTexts.sexta,
                                                             SmartEnergy.Resources.ComumTexts.sabado
                                                           };
                        
                                    for (i = 0; i < 7; i++)
                                    {
                                        j = i + 1;

                                        fatcargaFPC = string.Format("{0:0.0}", ViewBag.FatCargaFPC[j]);
                                        fatcargaFPI = string.Format("{0:0.0}", ViewBag.FatCargaFPI[j]);
                                        fatcargaP = string.Format("{0:0.0}", ViewBag.FatCargaP[j]);

                                        <tr class="tabela_valores" style="font-size:12px;">
                                            <td style="text-align:center; font-size:12px;" class="relat-preto">@dia_semana[i] (@ViewBag.Dias[j])</td>
                                            <td style="text-align:center; font-size:12px;" class="relat-capacitivo">@fatcargaFPC %</td>
                                            <td style="text-align:center; font-size:12px;" class="relat-fponta">@fatcargaFPI %</td>
                                            <td style="text-align:center; font-size:12px;" class="relat-ponta">@fatcargaP %</td>
                                        </tr>
                                    }
                                }

                            </tbody>
                        </table>
                    </div>

                </div>
            </div>

        }
}

    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>

<script type="text/javascript">

    $(document).ready(function () {

        // dias da semana
        var diasSemana = [];

        diasSemana.push('x');
        diasSemana.push('Domingo');
        diasSemana.push('Segunda');
        diasSemana.push('Terca');
        diasSemana.push('Quarta');
        diasSemana.push('Quinta');
        diasSemana.push('Sexta');
        diasSemana.push('Sabado');

        // fator de carga
        var dataFatCargaFPC = [];
        var dataFatCargaFPI = [];
        var dataFatCargaP = [];

        // copia valores
        var FatCargaFPC = @Html.Raw(Json.Encode(@ViewBag.FatCargaFPC));
        var FatCargaFPI = @Html.Raw(Json.Encode(@ViewBag.FatCargaFPI));
        var FatCargaP = @Html.Raw(Json.Encode(@ViewBag.FatCargaP));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));

        var FatCargaMaxGrafico = Math.ceil(@ViewBag.FatCargaMaxGrafico);

        // verifico se nao tem dados e forco um limite
        if( FatCargaMaxGrafico == 0.0)
        {
            FatCargaMaxGrafico = 100.0;
        }

        dataFatCargaFPC.push(['data1']);
        dataFatCargaFPI.push(['data2']);
        dataFatCargaP.push(['data3']);

        for (i = 1; i < 8; i++)
        {
            dataFatCargaFPC.push([FatCargaFPC[i]]);
            dataFatCargaFPI.push([FatCargaFPI[i]]);
            dataFatCargaP.push([FatCargaP[i]]);
        }

        c3.generate({

            bindto: '#grafico-fatcarga',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                columns: [diasSemana,dataFatCargaFPC,dataFatCargaFPI,dataFatCargaP],
                types: {
                    data1: 'bar',
                    data2: 'bar',
                    data3: 'bar'
                },
                order: null,
                colors: {
                    data1: '#1C84C6',
                    data2: '#007F00',
                    data3: '#FF0000'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type : 'categories',
                    tick: {
                        outer: false,
                        fit: true
                    }
                },
                y:  {
                    max: FatCargaMaxGrafico,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {
                            return d.toFixed(1);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.8 },
            }
        });

        // labels
        $('.chart-legend').css("display", "block");


    });

</script>

}
    