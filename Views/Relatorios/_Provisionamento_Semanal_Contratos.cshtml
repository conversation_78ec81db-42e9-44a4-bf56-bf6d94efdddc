﻿@using SmartEnergyLib.SQL

<style>

    .icones {
        padding: 0px 5px;
        font-size: 18px;
        float: left;
    }

    .titulo {
        margin: 0;
        margin-left: 0px;
        margin-top: 20px;
        margin-bottom: 15px;
        padding-left: 15px;
        padding-top: 5px;
        padding-bottom: 5px;
        font-size: 12px;
        font-weight: bold;
        border-radius: 10px;
        float: left;
    }

    .link_branco a:link {
        color: #ffffff !important;
    }
    .link_branco a:visited {
        color: #ffffff  !important;
    }
    .link_branco a:hover {
        color: #f0f0f0  !important;
    }
    .link_branco a:active {
        color: #ffffff  !important;
    }

    .link_preto a:link {
        color: #7E6A6C !important;
    }
    .link_preto a:visited {
        color: #7E6A6C  !important;
    }
    .link_preto a:hover {
        color: #a0a0a0  !important;
    }
    .link_preto a:active {
        color: #7E6A6C  !important;
    }

    table.dataTable.select tbody tr,
    table.dataTable thead th:first-child {
        cursor: pointer;
    }

    #dataTables-contratos tbody tr.selected {
            color: white;
            background-color: #293846;
        }

    #dataTables-contratosVigentes tbody tr.selected {
            color: white;
            background-color: #293846;
        }

    .overlay_aguarde {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.45);
        z-index: 9999;
        color: white;
        display: inline-block;
        border-radius: 6px;
        margin-bottom: 20px;
        margin-left: 14px;
        margin-right: 14px;
    }

    .spinner-aguarde .sk-spinner-wave div {
        background-color: #ffffff !important;
    }
    
    .spinner-aguarde {
        margin: 0;
        position: absolute;
        top: 50%;
        left: 50%;
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
    }

</style>

<div class="row">
    <div class="col-lg-12">
        <div class="panel panel-title">
            <div class="panel-heading">
                <h4>@SmartEnergy.Resources.ContratosCCEETexts.ContratosCCEE</h4>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="form-group col-lg-12">
                        <table id="dataTables-contratos" class="table table-striped table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>Sequência</th>
                                    <th>IDContratoCCEE</th>
                                    <th>Ordem</th>
                                    <th></th>
                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.SiglaCCEE</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.ContratoCodigo</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.Fornecedor</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.VigenciaInicio</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.VigenciaFim</th>
                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.Considera</th>
                                </tr>
                            </thead>
                            <tbody>

                                @{
                                    List<AgentesDominio> listatiposComercializadoras = ViewBag.listaTipoComercializadoras;
                                    List<ContratosCCEEDominio> contratosEnergia = ViewBag.contratosEnergia;

                                    if (contratosEnergia != null)
                                    {
                                        foreach (ContratosCCEEDominio contrato in contratosEnergia)
                                        {
                                            string logo = "http://www.smartenergy.com.br";

                                            if (contrato.Logo.IsEmpty())
                                            {
                                                logo += "/Logos/LogoSmartEnergy.png";
                                            }
                                            else
                                            {
                                                logo += "/Logos/" + contrato.Logo;
                                            }

                                            // comercializadora
                                            AgentesDominio tipo_comercializadora = listatiposComercializadoras.Find(item => item.IDAgente == contrato.IDComercializadora);
                                            string comercializadora = "---";
                                            if (tipo_comercializadora != null)
                                            {
                                                comercializadora = tipo_comercializadora.Nome;
                                            }
                                            
                                            // considera
                                            var considera = "NÃO";
                                                            
                                            if (contrato.Considera == 1)
                                            {
                                                considera = "SIM";
                                            }
                                                            
                                            <tr>
                                                <td class="reordena">@contrato.Ordem</td>
                                                <td class="reordena">@contrato.IDContratoCCEE</td>
                                                <td class="reordena">@contrato.Ordem</td>
                                                <td class="reordena" bgcolor="#293846" align="center"><img src=@logo title="@contrato.NomeCliente" style="max-height:45px; height: auto;" /></td>
                                                <td class="reordena"><b>@contrato.SiglaCCEE</b><br />@contrato.RazaoSocial</td>
                                                <td class="reordena">@contrato.Contrato_Codigo</td>
                                                <td class="reordena">@comercializadora</td>
                                                <td><span style="display:none;">@contrato.Vigencia_Inicio_Sort_aux</span>@contrato.Vigencia_Inicio_aux</td>
                                                <td><span style="display:none;">@contrato.Vigencia_Fim_Sort_aux</span>@contrato.Vigencia_Fim_aux</td>
                                                <td>@considera</td>
                                            </tr>
                                        }
                                    }
                                }

                            </tbody>
                        </table>

                    </div>
                </div>

                @{
                    if (ViewBag.numContratos != null)
                    {
                        int numContratos = ViewBag.numContratos;
                    
                        if (numContratos > 1)
                        {
                            <div class="div_gerenciamento" style="display:none;">
                                <div class="row">
                                    <div class="form-group col-lg-4">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.Considera</label><br />
                                        <select class="form-control" id="Considera" onchange="AlteraConsidera();">
                                            <option value="0">NÃO</option>
                                            <option value="1">SIM</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="div_gerenciamento_selecione" style="display:block;">
                                <div class="row">
                                    <div class="form-group col-lg-9">
                                        <label><i class="fa fa-exclamation-circle icones"></i>&nbsp;Selecione a coluna <b>Considera</b> para indicar se o contrato deve ser utilizado no provisionamento.</label><br />
                                        <label><i class="fa fa-exclamation-circle icones"></i>&nbsp;Arraste o Contrato para alterar a sequência.</label>
                                    </div>
                                </div>
                            </div>
                        }
                    }
                }

            </div>
        </div>
    </div>
</div>


<script type="text/javascript">

    $(document).ready(function () {

        //
        // TABELA CONTRATOS
        //

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        var tableContratos = $('#dataTables-contratos').DataTable({
            rowReorder: {
                selector: '.reordena',
                update: true
            },
            "iDisplayLength": 10,
            dom: 'ftp',
            "bAutoWidth": false,
            "aoColumnDefs": [
                     { "aTargets": [0], "bSortable": true, "bSearchable": false },
                     { "aTargets": [1], "bVisible": false, "bSortable": false, "bSearchable": false },
                     { "aTargets": [2], "bVisible": false, "bSortable": false, "bSearchable": false },
                     { "aTargets": [3], "sType": "portugues", "bSortable": false, "bSearchable": false, 'sClass': 'fundo_azul' },
                     { "aTargets": [4], "sType": "portugues", "bSortable": false },
                     { "aTargets": [5], "sType": "portugues", "bSortable": false },
                     { "aTargets": [6], "sType": "portugues", "bSortable": false },
                     { "aTargets": [7], "sType": "portugues", "bSortable": false },
                     { "aTargets": [8], "sType": "portugues", "bSortable": false },
                     { "aTargets": [9], "sType": "portugues", "bSortable": false }
            ],
            "aoColumns": [
            { sWidth: "7%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "10%" },
            { sWidth: "25%" },
            { sWidth: "14%" },
            { sWidth: "14%" },
            { sWidth: "10%" },
            { sWidth: "10%" },
            { sWidth: "10%" }
            ],
            'order': [0, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        //
        // TABELA CONTRATOS - ORDEM
        //

        tableContratos.on('row-reordered', function (e, diff, edit) {

            // salvar
            ContratosCCEE_Ordem_Salvar();

        });

        //
        // TABELA CONTRATOS - SELECAO
        //

        $('#dataTables-contratos tbody').on('click', 'tr', function () {

            if ($(this).hasClass('selected')) {

                // retira selecao atual
                $(this).removeClass('selected');

                // desabilita
                $(".div_gerenciamento *").prop('disabled', true);
                $('.div_gerenciamento').css("display", "none");
                $('.div_gerenciamento_selecione').css("display", "block");

            }
            else {

                // habilita se nao for cliente operador
                var IDTipoAcesso = Math.ceil(@ViewBag._IDTipoAcesso);

                if (IDTipoAcesso != TIPO_ACESSO_CLIENTE_ADMIN && IDTipoAcesso != TIPO_ACESSO_CLIENTE_OPER)
                {
                    // habilita
                    $(".div_gerenciamento *").prop('disabled', false);
                    $('.div_gerenciamento').css("display", "block");
                    $('.div_gerenciamento_selecione').css("display", "none");
                }

                // dataTable
                var table = $('#dataTables-contratos').DataTable();

                // retira selecao atual
                table.$('tr.selected').removeClass('selected');

                // seleciona
                $(this).addClass('selected');

                // pega dados da linha selecionada
                var data = $('#dataTables-contratos').DataTable().row(this).data();
                var Considera = data[9];

                if (Considera == "SIM")
                {
                    document.getElementById('Considera').value = "1";
                }
                else
                {
                    document.getElementById('Considera').value = "0";
                }
            }
        });

    });



    //
    // VALOR CONSIDERA
    //

    function SetCell(item, valor) {

        // dataTable
        var table = $('#dataTables-contratos').DataTable();
        var data = table.row('.selected').data();

        // verifica se tem selecionado
        if (data != undefined) {
            data[item] = valor;
            table.row('.selected').data(data).draw();
        }
    }

    function AlteraConsidera() {

        // le valor
        var x = document.getElementById("Considera");
        var text = x.options[x.selectedIndex].text;

        // coloca na tabela
        SetCell(9, text);

        // salvar
        ContratosCCEE_Ordem_Salvar();

    }

    function ContratosCCEE_Ordem_Salvar() {

        // lista de contratos
        var oTable = $('#dataTables-contratos').dataTable();
        var rows = oTable.fnSettings().aoData;
        var ordem = 0;
        var IDContratoCCEE = 0;
        var considera = 0;
        var dataArray = [];

        // percorre tabela
        $.each(rows, function (i, val) {

            // pega valores
            ordem = parseInt(val._aData[0]);
            IDContratoCCEE = parseInt(val._aData[1]);

            var considera_str = val._aData[9];

            if (considera_str == "SIM") {
                considera = 1;
            }
            else {
                considera = 0;
            }

            dataArray.push({
                "IDContratoCCEE": IDContratoCCEE,
                "Ordem": ordem,
                "Considera": considera
            });
        });

        // constroi estrutura com lista de contratos
        data = { 'contratosOrdem': dataArray };
        data2 = JSON.stringify(data);

        $.ajax({
            contentType: 'application/json; charset=utf-8',
            dataType: 'json',
            url: '/ContratosCCEE/ContratosCCEE_Ordem_Salvar',
            data: data2,
            type: 'POST',
            success: function (data) {

            },
            error: function (xhr, status, error) {

                // fim
                $('.overlay_aguarde').toggle();

            }
        });

    }

</script>
