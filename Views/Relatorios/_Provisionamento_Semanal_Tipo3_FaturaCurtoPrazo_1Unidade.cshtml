﻿@using SmartEnergyLib.SQL

<div class="row">
    <div class="col-lg-12">

        @{
            Provisionamento_Tipo3 provisionamento = ViewBag.ProvisionamentoSemanal;
            
            if (provisionamento != null)
            {
                List<Provisionamento_Tipo1_Tipo2> provs = provisionamento.por_contrato;
                
                if (provs != null)
                {
                   
                    <div class="panel panel-title">
                        <div class="panel-heading">
                            <h4>@SmartEnergy.Resources.ContratosCCEETexts.FaturaCurtoPrazo</h4>
                        </div>
                        <div class="panel-body">
                            <div class="row">

                                <table class="table table-striped table-bordered table-hover dataTables-fatCurtoPrazo">
                                    <thead>
                                        <tr>
                                            <th>Sequência</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.ContratoCodigo</th>
                                            <th>@SmartEnergy.Resources.ContratosCCEETexts.SobraDeficit (MWh)</th>
                                            <th>PLD (R$/MWh)</th>
                                            <th>@SmartEnergy.Resources.FinancasTexts.Total (R$)</th>
                                        </tr>
                                    </thead>
                                    <tbody>

                                        @{
                                            if (provs.Count > 0)
                                            {
                                                int ordem = 0;
                                                double PLD = 0.0;
                                                
                                                foreach (Provisionamento_Tipo1_Tipo2 prov in provs)
                                                {
                                                    ordem = ordem + 1;
                                                    
                                                    List<Provisionamento_UnidadesConsumidoras> unidadesConsumidoras = prov.unidadesConsumidoras;
                                                
                                                    if (unidadesConsumidoras != null)
                                                    {
                                                        foreach (Provisionamento_UnidadesConsumidoras unid in unidadesConsumidoras)
                                                        {
                                                            PLD = unid.PLD;
                                                            
                                                            <tr>
                                                                <td>@ordem</td>
                                                                <td>@prov.Contrato_Codigo</td>
                                                                <td>-</td>
                                                                <td>-</td>
                                                                <td>-</td>
                                                            </tr>
                                                        }
                                                    }
                                                }

                                                // total                                                
                                                Provisionamento_Tipo1_Tipo2 prov_total = provs[0];

                                                <tr>
                                                    <td>@ordem</td>
                                                    <td><b>TOTAL</b><br />&nbsp;</td>
                                                    <td>@string.Format("{0:#,##0.000}", provisionamento.Resultado)</td>
                                                    <td>@string.Format("{0:#,##0.00}", PLD)</td>
                                                    <td>@string.Format("{0:#,##0.00}", provisionamento.TotalFaturamento_CP)</td>
                                                </tr>                                    
                                                
                                            }
                                        }

                                    </tbody>
                                </table>

                            </div>
                        </div>
                    </div>
            
                }
            }
        }

    </div>
</div>

<br />
<div class="row">
    <div class="col-lg-12">
        <i class="fa fa-exclamation-triangle" style="font-size:20px;"></i><span style="font-size:large; padding-left:8px;"><b>O provisionamento da fatura de energia não contempla os custos com ICMS.</b></span>
    </div>
</div>


<script type="text/javascript">

    $(document).ready(function () {

        $('.dataTables-fatCurtoPrazo').DataTable({
            "iDisplayLength": 18,
            dom: 'tp',

            "bAutoWidth": false,
            "aoColumnDefs": [
                    { "aTargets": [0], "bVisible": false, "bSortable": false, "bSearchable": false },
                    { "aTargets": [1], "sType": "portugues", "bSortable": false, "bSearchable": false },
                    { "aTargets": [2], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [3], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [4], "sType": "numero", "bSortable": false, "bSearchable": false }
            ],

            "aoColumns": [
                { sWidth: "1%" },
                { sWidth: "25%" },
                { sWidth: "25%" },
                { sWidth: "25%" },
                { sWidth: "25%" }
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

    });

</script>
