﻿<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia</title>

    <link href="~/Content/bootstrap.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/style.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/plugins/c3/c3.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/Relatorios.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/RelatoriosPDF.css" rel="stylesheet" type="text/css" />
    <link href="~/fonts/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="~/Scripts/jquery-2.1.1.min.js" charset="utf-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/d3/d3.min.js" charset="utf-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/c3/c3.min.js"></script>

</head>

@if (ViewBag.Retorno == 2)
{
    <div class="row">
        <div class="col-lg-12">
            <div class="linha cabecalho">
                <div class="coluna1">
                    <p><img src="~/Logos/@ViewBag._LogoConsultor" style="height:42px; width:auto;" /></p>
                </div>

                <div class="coluna2">
                    <h3>@ViewBag.ClienteNome</h3>
                    @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                </div>

                <div class="coluna3">
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia - @SmartEnergy.Resources.RelatoriosTexts.RelatSemanal (@SmartEnergy.Resources.RelatoriosTexts.Simulacao)</h4>
                    <p>@ViewBag.DataTextoAtual</p>
                </div>
            </div>
        </div>
    </div>

    <br /><br /><br /><br /><br /><br />
    <div class="row">
        <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
            <i class="fa fa-warning fa-5x"></i>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>@SmartEnergy.Resources.RelatoriosTexts.NaoExistemRegistros</h2>
        </div>
    </div>
    <br /><br /><br /><br />

}
else
{

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="~/Logos/@ViewBag._LogoConsultor" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia - @SmartEnergy.Resources.RelatoriosTexts.RelatSemanal (@SmartEnergy.Resources.RelatoriosTexts.Simulacao)</h4>
                        <p>@ViewBag.DataTextoAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <br />

        @{
            if (ViewBag.showGrafico)
            {
                <div class="row">
                    <div class="relat-grafico" style="height:490px; width:800px; margin-left:50px;">
                        <div id="fatpot-chart-simulacao"></div>
                        <div class='chart-legend' style="display:none;">
                            <div class='legend-scale-dem'>
                                <ul class='legend-labels' style="margin-top: 0px;">
                                    <li><span style='background:#0000FF !important;' id="ckDomingo"></span> @SmartEnergy.Resources.ComumTexts.domingo</li>
                                    <li><span style='background:#00FF00 !important;' id="ckSegunda"></span> @SmartEnergy.Resources.ComumTexts.segunda</li>
                                    <li><span style='background:#FF0000 !important;' id="ckTerca"></span> @SmartEnergy.Resources.ComumTexts.terca</li>
                                    <li><span style='background:#FF7F00 !important;' id="ckQuarta"></span> @SmartEnergy.Resources.ComumTexts.quarta</li>
                                    <li><span style='background:#FF00FF !important;' id="ckQuinta"></span> @SmartEnergy.Resources.ComumTexts.quinta</li>
                                    <li><span style='background:#000000 !important;' id="ckSexta"></span> @SmartEnergy.Resources.ComumTexts.sexta</li>
                                    <li><span style='background:#00FFFF !important;' id="ckSabado"></span> @SmartEnergy.Resources.ComumTexts.sabado</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }

        <hr />

        <div class="row" style="margin-top: -15px;">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisCapacitivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisCapFPC_Sim<br /><small>(@ViewBag.FatPot_MaisCapFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.FatPot_MaisCapFPI_Sim<br /><small>(@ViewBag.FatPot_MaisCapFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.FatPot_MaisCapP_Sim<br /><small>(@ViewBag.FatPot_MaisCapP_DataHora_Sim)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisIndFPC_Sim<br /><small>(@ViewBag.FatPot_MaisIndFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.FatPot_MaisIndFPI_Sim<br /><small>(@ViewBag.FatPot_MaisIndFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.FatPot_MaisIndP_Sim<br /><small>(@ViewBag.FatPot_MaisIndP_DataHora_Sim)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.NoPeriodo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_NoPeriodoFPC_Sim</td>
                                <td class="relat-fponta">@ViewBag.FatPot_NoPeriodoFPI_Sim</td>
                                <td class="relat-ponta">@ViewBag.FatPot_NoPeriodoP_Sim</td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.Referencia</td>
                                <td class="relat-capacitivo">@ViewBag.RefenciaFPC</td>
                                <td class="relat-fponta">@ViewBag.RefenciaFPI</td>
                                <td class="relat-ponta">@ViewBag.RefenciaFPI</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    @{
        if (ViewBag.showTabela)
        {
            <div class="page-break"></div>
        
            <div class="row">

                <br /><br />

                <div class="col-lg-12">
                    <div class="panel panel-default">

                        @{
                            var k = 0;

                            var dia_semana = new string[] { SmartEnergy.Resources.ComumTexts.domingo, 
                                                                        SmartEnergy.Resources.ComumTexts.segunda,
                                                                        SmartEnergy.Resources.ComumTexts.terca,
                                                                        SmartEnergy.Resources.ComumTexts.quarta,
                                                                        SmartEnergy.Resources.ComumTexts.quinta,
                                                                        SmartEnergy.Resources.ComumTexts.sexta,
                                                                        SmartEnergy.Resources.ComumTexts.sabado
                                                                    };
                        }

                        <table class="table no-margins tabela-grafico-pdf">
                            <thead>
                                <tr>
                                    <th style="width:16%; text-align:center; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>

                                    @for (k = 0; k < 7; k++)
                                    {
                                        <th style="width:12%; text-align:center; font-size:12px;">@dia_semana[k]<br />@ViewBag.Dias_Sim[k]</th>
                                    }    

                                </tr>
                            </thead>

                            <tbody>

                                @{
                                    var i = 0;
                                    var j = 0;
                                    var classe_sim = "relat-semreg";
                                    var fatpot_sim = "---";

                                    for (i = 0; i < 24; i++)
                                    {
                                        j = i + 1;

                                        <tr class="tabela_valores" style="font-size:12px;">
                                            <td style="text-align:center; font-size:12px;" class="relat-preto">@ViewBag.Horas_Sim[j]</td>

                                            @for (k = 0; k < 7; k++)
                                            {
                                                switch ((int)ViewBag.Periodo_Sim[k, j])
                                                {
                                                    case 0:
                                                        classe_sim = "relat-ponta";
                                                        break;

                                                    case 1:
                                                        classe_sim = "relat-indutivo";
                                                        break;

                                                    case 2:
                                                        classe_sim = "relat-capacitivo";
                                                        break;

                                                    default:
                                                    case 3:
                                                        classe_sim = "relat-semreg";
                                                        break;
                                                }

                                                if (ViewBag.Periodo_Sim[k, j] == 3)
                                                {
                                                    fatpot_sim = "---";
                                                }
                                                else
                                                {
                                                    fatpot_sim = string.Format("{0:0.000}", ViewBag.FatPot_Sim[k, j]);
                                                }

                                                <td style="text-align:center; font-size:12px;" class="@classe_sim">@fatpot_sim</td>
                                            }

                                        </tr>
                                    }
                                }

                            </tbody>
                        </table>
                    </div>
                </div>        
            </div>   
        }
    }

    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>



<script type="text/javascript">

    // dias da semana
    var diasSemana = [];

    diasSemana.push('Domingo');
    diasSemana.push('Segunda');
    diasSemana.push('Terca');
    diasSemana.push('Quarta');
    diasSemana.push('Quinta');
    diasSemana.push('Sexta');
    diasSemana.push('Sabado');
    diasSemana.push('Período');

    // cores
    var coresdiasSemana = [];

    coresdiasSemana.push('#0000FF');
    coresdiasSemana.push('#00FF00');
    coresdiasSemana.push('#FF0000');
    coresdiasSemana.push('#FF7F00');
    coresdiasSemana.push('#FF00FF');
    coresdiasSemana.push('#000000');
    coresdiasSemana.push('#00FFFF');

    // c_names
    var c_names = [];

    c_names.push("Relat_SemanalDomingo");
    c_names.push("Relat_SemanalSegunda");
    c_names.push("Relat_SemanalTerca");
    c_names.push("Relat_SemanalQuarta");
    c_names.push("Relat_SemanalQuinta");
    c_names.push("Relat_SemanalSexta");
    c_names.push("Relat_SemanalSabado");

    // id_names
    var id_names = [];

    id_names.push("#ckDomingo");
    id_names.push("#ckSegunda");
    id_names.push("#ckTerca");
    id_names.push("#ckQuarta");
    id_names.push("#ckQuinta");
    id_names.push("#ckSexta");
    id_names.push("#ckSabado");


    $(document).ready(function () {

        //
        // GRAFICO SIMULACAO
        //

        // fator de potencia simulacao
        var dataX_Sim = [];
        var labelX_Sim = [];
        var dataReferenciaFPC_Sim = [];
        var dataReferenciaFPI_Sim = [];
        var dataDom_Sim = [];
        var dataSeg_Sim = [];
        var dataTer_Sim = [];
        var dataQua_Sim = [];
        var dataQui_Sim = [];
        var dataSex_Sim = [];
        var dataSab_Sim = [];
        var dataFatPotUm_Sim = [];

        // copia valores
        var FatPot_Sim = @Html.Raw(Json.Encode(@ViewBag.FatPot_Sim));
        var Periodo_Sim = @Html.Raw(Json.Encode(@ViewBag.Periodo_Sim));
        var Datas_Sim = @Html.Raw(Json.Encode(@ViewBag.Datas));
        var FatPotMin = @Html.Raw(Json.Encode(@ViewBag.FatPotMin));

        dataX_Sim.push('x');
        dataDom_Sim.push(['data1']);
        dataSeg_Sim.push(['data2']);
        dataTer_Sim.push(['data3']);
        dataQua_Sim.push(['data4']);
        dataQui_Sim.push(['data5']);
        dataSex_Sim.push(['data6']);
        dataSab_Sim.push(['data7']);
        dataReferenciaFPC_Sim.push(['data8']);
        dataReferenciaFPI_Sim.push(['data9']);
        dataFatPotUm_Sim.push(['data10']);

        for (i = 0; i < 26; i++)
        {
            // X
            dataX_Sim.push(Datas_Sim[i]);

            // linha de fatpot um
            dataFatPotUm_Sim.push([0.0]);

            dataDom_Sim.push([ novaEscala(FatPot_Sim[i])        ]);
            dataSeg_Sim.push([ novaEscala(FatPot_Sim[i+26])     ]);
            dataTer_Sim.push([ novaEscala(FatPot_Sim[i+(2*26)]) ]);
            dataQua_Sim.push([ novaEscala(FatPot_Sim[i+(3*26)]) ]);
            dataQui_Sim.push([ novaEscala(FatPot_Sim[i+(4*26)]) ]);
            dataSex_Sim.push([ novaEscala(FatPot_Sim[i+(5*26)]) ]);
            dataSab_Sim.push([ novaEscala(FatPot_Sim[i+(6*26)]) ]);

            dataReferenciaFPI_Sim.push([0.08]);
            dataReferenciaFPC_Sim.push([-0.08]);
        }

        // prepara escala eixo Y
        var maximo = (1.0 - FatPotMin);
        var minimo = -1.0 * maximo;

        // valores label X
        labelX_Sim.push(Datas_Sim[1]);
        for (i = 3; i <= 24; i+=3)
        {
            labelX_Sim.push(Datas_Sim[i]);
        }

        var Data = [dataX_Sim,dataDom_Sim,dataSeg_Sim,dataTer_Sim,dataQua_Sim,dataQui_Sim,dataSex_Sim,dataSab_Sim,dataReferenciaFPC_Sim,dataReferenciaFPI_Sim,dataFatPotUm_Sim];

        var chart_sim = c3.generate({

            bindto: '#fatpot-chart-simulacao',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 10,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'step',
                    data2: 'step',
                    data3: 'step',
                    data4: 'step',
                    data5: 'step',
                    data6: 'step',
                    data7: 'step',
                    data8: 'step',
                    data9: 'step',
                    data10: 'step',
                    data11: 'step',
                },
                colors: {
                    data1: '#0000FF',
                    data2: '#00FF00',
                    data3: '#FF0000',
                    data4: '#FF7F00',
                    data5: '#FF00FF',
                    data6: '#000000',
                    data7: '#00FFFF',
                    data8: '#1C84C6',
                    data9: '#007F00',
                    data10: '#1C84C6',
                    data11: '#007F00'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX_Sim,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*28
                },
                y:  {
                    max: maximo,
                    min: minimo,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            var valor;

                            // converte para nova escala
                            if( d < 0 )
                            {
                                valor = (1.0 - (-1.0*d)) * -1.0;
                            }
                            else
                            {
                                valor = (1.0 - d);
                            }

                            return valor.toFixed(3);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                },
                y: {
                    lines: [{value:0}]
                }
            },
        });

        // salva chart simulacao
        $('#fatpot-chart-simulacao').data('c3-chart', chart_sim);

        // label dias da semana
        todosDiaSemana();

        // labels
        $('.chart-legend').css("display", "block");
    });

    function novaEscala(fatpot) {

        var novo = 1.0;

        // converte para nova escala
        if( fatpot < 0.0 )
        {
            novo = (1.0 - (-1.0*fatpot)) * -1.0;
        }
        else
        {
            novo = (1.0 - fatpot);
        }

        return(novo);
    }

    function todosDiaSemana() {

        // dias da semana
        var i;

        for(i=0;i<7;i++)
        {
            alteraDiaSemana(i,false);
        }
    }

    function alteraDiaSemana(dia_semana, alterar) {

        // le cookie dia da semana
        var estado = 1;

        switch(dia_semana)
        {
            case 0:
                estado = Math.ceil(@ViewBag.Relat_SemanalDomingo);
                break;

            case 1:
                estado = Math.ceil(@ViewBag.Relat_SemanalSegunda);
                break;

            case 2:
                estado = Math.ceil(@ViewBag.Relat_SemanalTerca);
                break;

            case 3:
                estado = Math.ceil(@ViewBag.Relat_SemanalQuarta);
                break;

            case 4:
                estado = Math.ceil(@ViewBag.Relat_SemanalQuinta);
                break;

            case 5:
                estado = Math.ceil(@ViewBag.Relat_SemanalSexta);
                break;

            case 6:
                estado = Math.ceil(@ViewBag.Relat_SemanalSabado);
                break;
        }

        // data series
        var chart_ea = $('#fatpot-chart-simulacao').data('c3-chart');

        // verifica estado
        if( estado == 1 )
        {
            chart_ea.show(["data" + (dia_semana+1)]);

        }
        else
        {
            chart_ea.hide(["data" + (dia_semana+1)]);

        }
    }

</script>

}
    