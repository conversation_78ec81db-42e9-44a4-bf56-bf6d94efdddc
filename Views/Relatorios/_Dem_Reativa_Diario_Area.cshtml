﻿<style>

#fatpot-chart .c3-area {
    opacity:0.3;
}

</style>

<div class="row">
    <div class="col-lg-4 col-md-12 relatorio_real">
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.DemandaMaximaDia</h5>
                        <h1>@ViewBag.Dem_MaxFPC <span>@ViewBag.UnidadeReativo</span></h1>
                        <h6>@ViewBag.Dem_MaxFPC_DataHora</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.DemandaMaximaDia</h5>
                        <h1>@ViewBag.Dem_MaxFPI <span>@ViewBag.UnidadeReativo</span></h1>
                        <h6>@ViewBag.Dem_MaxFPI_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.DemandaMaximaDia</h5>
                        <h1>@ViewBag.Dem_MaxP <span>@ViewBag.UnidadeReativo</span></h1>
                        <h6>@ViewBag.Dem_MaxP_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
        <br /><br /><br /><br /><br />
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia @SmartEnergy.Resources.SupervisaoTexts.MaisCapacitivo</h5>
                        <h1>@ViewBag.FatPot_MaisCapFPC</h1>
                        <h6>@ViewBag.FatPot_MaisCapFPC_DataHora</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia @SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</h5>
                        <h1>@ViewBag.FatPot_MaisIndFPI</h1>
                        <h6>@ViewBag.FatPot_MaisIndFPI_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia @SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</h5>
                        <h1>@ViewBag.FatPot_MaisIndP</h1>
                        <h6>@ViewBag.FatPot_MaisIndP_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-12 simulacao" style="display:none;">
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.DemandaMaximaDia</h5>
                        <h1>@ViewBag.Dem_MaxFPC_Sim <span>@ViewBag.UnidadeReativo</span></h1>
                        <h6>@ViewBag.Dem_MaxFPC_DataHora_Sim</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.DemandaMaximaDia</h5>
                        <h1>@ViewBag.Dem_MaxFPI_Sim <span>@ViewBag.UnidadeReativo</span></h1>
                        <h6>@ViewBag.Dem_MaxFPI_DataHora_Sim</h6>
                    </div>
                </div>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.DemandaMaximaDia</h5>
                        <h1>@ViewBag.Dem_MaxP_Sim <span>@ViewBag.UnidadeReativo</span></h1>
                        <h6>@ViewBag.Dem_MaxP_DataHora_Sim</h6>
                    </div>
                </div>
            </div>
        </div>
        <br /><br /><br /><br /><br />
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia @SmartEnergy.Resources.SupervisaoTexts.MaisCapacitivo</h5>
                        <h1>@ViewBag.FatPot_MaisCapFPC_Sim</h1>
                        <h6>@ViewBag.FatPot_MaisCapFPC_DataHora_Sim</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia @SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</h5>
                        <h1>@ViewBag.FatPot_MaisIndFPI_Sim</h1>
                        <h6>@ViewBag.FatPot_MaisIndFPI_DataHora_Sim</h6>
                    </div>
                </div>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia @SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</h5>
                        <h1>@ViewBag.FatPot_MaisIndP_Sim</h1>
                        <h6>@ViewBag.FatPot_MaisIndP_DataHora_Sim</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-8 col-md-12">
        <div class="row">
            <div class="relat-grafico">
                <div id="demanda-chart"></div>
                <div id="fatpot-chart" style="margin-top: -10px"></div>
                <div class='chart-legend' style="display:none;">
                    <div class='legend-scale-dem'>
                        <ul class='legend-labels' style="margin-top: -25px;">
                            <li><span style='background:#1C84C6;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                            <li><span style='background:#007f00;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                            <li><span style='background:#ff0000;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                            <li class="simulacao"><span style='background:#00b1ff;'></span>@SmartEnergy.Resources.RelatoriosTexts.Simulacao</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@Html.Partial("_Relat_Botoes")

<br />
<div class="panel panel-title relatorio">
    <div class="panel-body">

        <div class="row">
            <div class="col-lg-12 relatorio_real">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:20%;"></th>
                                <th class="relat-capacitivo" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                                <th class="relat-preto" style="width:20%;"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMaxima</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MaxFPC @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MaxFPC_DataHora)</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MaxFPI @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MaxFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MaxP @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MaxP_DataHora)</small></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMedia</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MedFPC @ViewBag.UnidadeReativo<br /><small>&nbsp;</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MedFPI @ViewBag.UnidadeReativo<br /><small>&nbsp;</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MedP @ViewBag.UnidadeReativo</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMinima</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MinFPC @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MinFPC_DataHora)</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MinFPI @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MinFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MinP @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MinP_DataHora)</small></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisCapacitivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisCapFPC<br /><small>(@ViewBag.FatPot_MaisCapFPC_DataHora)</small></td>
                                <td class="relat-indutivo">@ViewBag.FatPot_MaisCapFPI<br /><small>(@ViewBag.FatPot_MaisCapFPI_DataHora)</small></td>
                                <td class="relat-indutivo">@ViewBag.FatPot_MaisCapP<br /><small>(@ViewBag.FatPot_MaisCapP_DataHora)</small></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisIndFPC<br /><small>(@ViewBag.FatPot_MaisIndFPC_DataHora)</small></td>
                                <td class="relat-indutivo">@ViewBag.FatPot_MaisIndFPI<br /><small>(@ViewBag.FatPot_MaisIndFPI_DataHora)</small></td>
                                <td class="relat-indutivo">@ViewBag.FatPot_MaisIndP<br /><small>(@ViewBag.FatPot_MaisIndP_DataHora)</small></td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="col-lg-12 simulacao" style="display:none;">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:20%;"></th>
                                <th class="relat-capacitivo" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                                <th class="relat-preto" style="width:20%;"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMaxima</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MaxFPC_Sim @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MaxFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MaxFPI_Sim @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MaxFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MaxP_Sim @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MaxP_DataHora_Sim)</small></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMedia</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MedFPC_Sim @ViewBag.UnidadeReativo<br /><small>&nbsp;</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MedFPI_Sim @ViewBag.UnidadeReativo<br /><small>&nbsp;</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MedP_Sim @ViewBag.UnidadeReativo</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMinima</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MinFPC_Sim @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MinFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MinFPI_Sim @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MinFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MinP_Sim @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MinP_DataHora_Sim)</small></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisCapacitivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisCapFPC_Sim<br /><small>(@ViewBag.FatPot_MaisCapFPC_DataHora_Sim)</small></td>
                                <td class="relat-indutivo">@ViewBag.FatPot_MaisCapFPI_Sim<br /><small>(@ViewBag.FatPot_MaisCapFPI_DataHora_Sim)</small></td>
                                <td class="relat-indutivo">@ViewBag.FatPot_MaisCapP_Sim<br /><small>(@ViewBag.FatPot_MaisCapP_DataHora_Sim)</small></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisIndFPC_Sim<br /><small>(@ViewBag.FatPot_MaisIndFPC_DataHora_Sim)</small></td>
                                <td class="relat-indutivo">@ViewBag.FatPot_MaisIndFPI_Sim<br /><small>(@ViewBag.FatPot_MaisIndFPI_DataHora_Sim)</small></td>
                                <td class="relat-indutivo">@ViewBag.FatPot_MaisIndP_Sim<br /><small>(@ViewBag.FatPot_MaisIndP_DataHora_Sim)</small></td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>

<div class="modal inmodal animated fadeIn" id="modalTabela" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-editar-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title"><i class="fa fa-list"></i>&nbsp;&nbsp;@SmartEnergy.Resources.RelatoriosTexts.TabelaRegistros</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-default relatorio_real">
                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:20%;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>
                                <th style="width:20%;">@SmartEnergy.Resources.RelatoriosTexts.Periodo</th>
                                <th style="width:20%;">@SmartEnergy.Resources.RelatoriosTexts.Ativa<br />(@ViewBag.UnidadeDemanda)</th>
                                <th style="width:20%;">@SmartEnergy.Resources.RelatoriosTexts.Reativa<br />(@ViewBag.UnidadeReativo)</th>
                                <th style="width:20%;">@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia</th>
                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var i = 0;
                                var j = 0;
                                var classe = "relat-semreg";
                                var periodo = "---";
                                var demandaAtv = "---";
                                var demandaRtv = "---";
                                var fatpot = "---";

                                for (i = 0; i < 96; i++)
                                {
                                    j = i + 1;

                                    switch ((int)ViewBag.Periodo[j])
                                    {
                                        case 0:
                                            classe = "relat-ponta";
                                            periodo = SmartEnergy.Resources.SupervisaoTexts.Ponta;
                                            break;

                                        case 1:
                                            classe = "relat-indutivo";
                                            periodo = SmartEnergy.Resources.SupervisaoTexts.FPontaInd;
                                            break;

                                        case 2:
                                            classe = "relat-capacitivo";
                                            periodo = SmartEnergy.Resources.SupervisaoTexts.FPontaCap;
                                            break;

                                        default:                                    
                                        case 3:
                                            classe = "relat-semreg";
                                            periodo = "---";
                                            break;
                                    }

                                    if (ViewBag.Periodo[j] == 3)
                                    {
                                        demandaAtv = "---";
                                        demandaRtv = "---";
                                        fatpot = "---";
                                    }
                                    else
                                    {
                                        demandaAtv = string.Format("{0:#,##0.0}", ViewBag.DemandaAtv[j]);
                                        demandaRtv = string.Format("{0:#,##0.0}", ViewBag.DemandaRtv[j]);
                                        fatpot = string.Format("{0:0.000}", ViewBag.FatPot[j]);
                                    }

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="@classe">@ViewBag.Horas[j]</td>
                                        <td style="text-align:center;" class="@classe">@periodo</td>
                                        <td style="text-align:center;" class="@classe">@demandaAtv</td>
                                        <td style="text-align:center;" class="@classe">@demandaRtv</td>
                                        <td style="text-align:center;" class="@classe">@fatpot</td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>

                <div class="panel panel-default simulacao" style="display:none;">
                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:20%;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>
                                <th style="width:20%;">@SmartEnergy.Resources.RelatoriosTexts.Periodo</th>
                                <th style="width:20%;">@SmartEnergy.Resources.RelatoriosTexts.Ativa<br />(@ViewBag.UnidadeDemanda)</th>
                                <th style="width:20%;">@SmartEnergy.Resources.RelatoriosTexts.Reativa<br />(@ViewBag.UnidadeReativo)</th>
                                <th style="width:20%;">@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia</th>
                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var classe_sim = "relat-semreg";
                                var periodo_sim = "---";
                                var demandaAtv_sim = "---";
                                var demandaRtv_sim = "---";
                                var fatpot_sim = "---";

                                for (i = 0; i < 96; i++)
                                {
                                    j = i + 1;

                                    switch ((int)ViewBag.Periodo[j])
                                    {
                                        case 0:
                                            classe_sim = "relat-ponta";
                                            periodo_sim = SmartEnergy.Resources.SupervisaoTexts.Ponta;
                                            break;

                                        case 1:
                                            classe_sim = "relat-indutivo";
                                            periodo_sim = SmartEnergy.Resources.SupervisaoTexts.FPontaInd;
                                            break;

                                        case 2:
                                            classe_sim = "relat-capacitivo";
                                            periodo_sim = SmartEnergy.Resources.SupervisaoTexts.FPontaCap;
                                            break;

                                        default:
                                        case 3:
                                            classe_sim = "relat-semreg";
                                            periodo_sim = "---";
                                            break;
                                    }

                                    if (ViewBag.Periodo[j] == 3)
                                    {
                                        demandaAtv_sim = "---";
                                        demandaRtv_sim = "---";
                                        fatpot_sim = "---";
                                    }
                                    else
                                    {
                                        demandaAtv_sim = string.Format("{0:#,##0.0}", ViewBag.DemandaAtv_Sim[j]);
                                        demandaRtv_sim = string.Format("{0:#,##0.0}", ViewBag.DemandaRtv_Sim[j]);
                                        fatpot_sim = string.Format("{0:0.000}", ViewBag.FatPot_Sim[j]);
                                    }

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="@classe_sim">@ViewBag.Horas_Sim[j]</td>
                                        <td style="text-align:center;" class="@classe_sim">@periodo_sim</td>
                                        <td style="text-align:center;" class="@classe_sim">@demandaAtv_sim</td>
                                        <td style="text-align:center;" class="@classe_sim">@demandaRtv_sim</td>
                                        <td style="text-align:center;" class="@classe_sim">@fatpot_sim</td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
            </div>
        </div>
    </div>
</div>

    
@Html.Partial("_EventosModal")

@Html.Partial("_ObservacoesModal")


<script type="text/javascript">

    $(document).ready(function () {

        // demanda
        var dataX = [];
        var labelX = [];
        var dataDemandaRtv = [];
        var dataDemMaxFPC = [];
        var dataDemMaxFPI = [];
        var dataDemMaxP = [];
        var dataDemZero = [];

        // demanda simulacao 
        var dataDemandaRtv_Sim = [];

        // copia valores
        var DemandaAtv = @Html.Raw(Json.Encode(@ViewBag.DemandaAtv));
        var DemandaRtv = @Html.Raw(Json.Encode(@ViewBag.DemandaRtv));
        var FatPot = @Html.Raw(Json.Encode(@ViewBag.FatPot));
        var Periodo = @Html.Raw(Json.Encode(@ViewBag.Periodo));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));

        // copia valores simulacao
        var DemandaAtv_Sim = @Html.Raw(Json.Encode(@ViewBag.DemandaAtv_Sim));
        var DemandaRtv_Sim = @Html.Raw(Json.Encode(@ViewBag.DemandaRtv_Sim));
        var FatPot_Sim = @Html.Raw(Json.Encode(@ViewBag.FatPot_Sim));

        var maximoDem = @Html.Raw(Json.Encode(@ViewBag.DemMaxGrafico));

        var UnidadeDemanda = @Html.Raw(Json.Encode(@ViewBag.UnidadeDemanda));
        var UnidadeReativo = @Html.Raw(Json.Encode(@ViewBag.UnidadeReativo));

        dataX.push('x');
        dataDemandaRtv.push(['data1']);
        dataDemMaxFPC.push(['data2']);
        dataDemMaxFPI.push(['data3']);
        dataDemMaxP.push(['data4']);
        dataDemZero.push(['data5']);

        // simulacao
        dataDemandaRtv_Sim.push(['data6']);

        for (i = 0; i < 98; i++)
        {
            // X
            dataX.push(Datas[i]);

            // linha de demanda zero
            dataDemZero.push([0.0]);

            dataDemandaRtv.push([DemandaRtv[i]]);

            // simulacao
            dataDemandaRtv_Sim.push([DemandaRtv_Sim[i]]);

            if( Periodo[i] == 0 )
            {
                dataDemMaxP.push([maximoDem]);
                dataDemMaxFPI.push([0]);
                dataDemMaxFPC.push([0]);
            }
            else if( Periodo[i] == 1 )
            {
                dataDemMaxP.push([0]);
                dataDemMaxFPI.push([maximoDem]);
                dataDemMaxFPC.push([0]);
            }
            else if( Periodo[i] == 2 )
            {
                dataDemMaxP.push([0]);
                dataDemMaxFPI.push([0]);
                dataDemMaxFPC.push([maximoDem]);
            }
            else
            {
                dataDemMaxP.push([0]);
                dataDemMaxFPI.push([0]);
                dataDemMaxFPC.push([0]);
            }

        }

        // valores label X
        labelX.push(Datas[1]);
        for (i = 12; i <= 96; i+=12)
        {
            labelX.push(Datas[i]);
        }

        var Data = [dataX,dataDemandaRtv,dataDemMaxFPC,dataDemMaxFPI,dataDemMaxP,dataDemZero,dataDemandaRtv_Sim];

        var chart = c3.generate({

            bindto: '#demanda-chart',
            size: {
                height: 320
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 20,
                left: 50
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'area-step',
                    data2: 'area-step',
                    data3: 'area-step',
                    data4: 'area-step',
                    data5: 'step',
                    data6: 'step'
                },
                color: function (color, d) {
                    if (typeof d.index === 'undefined') { return color; }

                    if( Periodo[d.index] == 0 )
                        return '#FF0000';

                    if( Periodo[d.index] == 1 )
                        return '#007F00';

                    if( Periodo[d.index] == 2 )
                        return '#1C84C6';

                    return '#007F00';
                },
                colors: {
                    data1: '#001F00',
                    data2: '#1C84C6',
                    data3: '#007F00',
                    data4: '#FF0000',
                    data5: '#000000',
                    data6: '#00b1ff'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x: {
                    show:false,
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            return "";
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*7
                },
                y:  {
                    max: maximoDem,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoDem < 100)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.7 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, unit, periodo, bgcolor;

                    // checkbox aplica simulacao
                    var simula = $('#aplica_simula').prop('checked');
                    var tooltip_index = 4;
                    if (simula == true)
                    {
                        tooltip_index = 7;
                    }

                    for (i = 0; i < tooltip_index; i++) {

                        // titulo
                        if (! text) {

                            // split string and create array.
                            var arr = Datas[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // horas e minutos
                            var hours = data.getHours();
                            var minutes = data.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            title = hours + ':' + minutes;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // periodo
                        switch(Periodo[d[0].index])
                        {
                            case 0:
                                periodo = PONTA;
                                bgcolor = '#FF0000';
                                break;

                            case 1:
                                periodo = FPONTA_IND;
                                bgcolor = '#007F00';
                                break;

                            case 2:
                                periodo = FPONTA_CAP;
                                bgcolor = '#1C84C6';
                                break;

                            default:
                                periodo = "---";
                                bgcolor = '#000000';
                                break;
                        }

                        // nome
                        switch(i)
                        {
                            case 0:
                                name = DEMANDA_ATIVA;
                                unit = UnidadeDemanda;
                                value = DemandaAtv[d[0].index].toFixed(1);
                                break;

                            case 1:
                                name = DEMANDA_REATIVA;
                                unit = UnidadeReativo;
                                value = DemandaRtv[d[0].index].toFixed(1);
                                break;

                            case 2:
                                name = FATOR_POTENCIA;
                                unit = '';
                                value = FatPot[d[0].index].toFixed(3);
                                break;

                            case 3:
                                name = PERIODO;
                                unit = '';
                                value = periodo;
                                break;

                            case 4:
                                name = DEMANDA_ATIVA;
                                unit = UnidadeDemanda;
                                value = DemandaAtv_Sim[d[0].index].toFixed(1);
                                break;

                            case 5:
                                name = DEMANDA_REATIVA;
                                unit = UnidadeReativo;
                                value = DemandaRtv_Sim[d[0].index].toFixed(1);
                                break;

                            case 6:
                                name = FATOR_POTENCIA;
                                unit = '';
                                value = FatPot_Sim[d[0].index].toFixed(3);
                                break;
                        }

                        value = value.replace('.', ',');

                        // verifica se sem registro
                        if( Periodo[d[0].index] == 3 )
                        {
                            value = '---';
                        }

                        if(i==3)
                        {
                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                            text += "<td class='name'>" + name + "</td>";
                            text += "<td class='value'>" + value + "</td>";
                            text += "</tr>";
                        }
                        else
                        {
                            if (i > 3)
                            {
                                name += " - " + SIMULACAO;
                                bgcolor = "#00b1ff";
                            }

                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                            text += "<td class='value'>" + value + " " + unit + "</td>";
                            text += "</tr>";
                        }
                    }

                    return text + "</table>";
                }
            }
        });

        // fator de potencia
        var dataReferenciaFPC = [];
        var dataReferenciaFPI = [];
        var dataReferenciaP = [];
        var dataFatPot = [];
        var dataFatPotUm = [];

        // fator de potencia simulacao
        var dataFatPot_Sim = [];

        // copia valores
        var FatPotMin = @Html.Raw(Json.Encode(@ViewBag.FatPotMin));

        dataFatPot.push(['data1']);
        dataReferenciaFPC.push(['data2']);
        dataReferenciaFPI.push(['data3']);
        dataReferenciaP.push(['data4']);
        dataFatPotUm.push(['data5']);

        // simulacao
        dataFatPot_Sim.push(['data6']);

        for (i = 0; i < 98; i++)
        {
            // linha de fatpot um
            dataFatPotUm.push([0.0]);

            // converte para nova escala
            if( FatPot[i] < 0 )
            {
                dataFatPot.push([(1.0 - (-1.0*FatPot[i])) * -1.0]);
            }
            else
            {
                dataFatPot.push([(1.0 - FatPot[i])]);
            }

            // converte para nova escala simulacao
            if( FatPot_Sim[i] < 0 )
            {
                dataFatPot_Sim.push([(1.0 - (-1.0*FatPot_Sim[i])) * -1.0]);
            }
            else
            {
                dataFatPot_Sim.push([(1.0 - FatPot_Sim[i])]);
            }

            if( Periodo[i] == 0 )
            {
                dataReferenciaFPC.push([0]);
                dataReferenciaFPI.push([0]);
                dataReferenciaP.push([0.08]);
            }
            else if( Periodo[i] == 1 )
            {
                dataReferenciaFPC.push([0]);
                dataReferenciaFPI.push([0.08]);
                dataReferenciaP.push([0]);
            }
            else if( Periodo[i] == 2 )
            {
                dataReferenciaFPC.push([-0.08]);
                dataReferenciaFPI.push([0]);
                dataReferenciaP.push([0]);
            }
            else
            {
                dataReferenciaFPC.push([0]);
                dataReferenciaFPI.push([0]);
                dataReferenciaP.push([0]);
            }
        }

        // prepara escala eixo Y
        var maximo = (1.0 - FatPotMin);
        var minimo = -1.0 * maximo;

        var Data = [dataX,dataFatPot,dataReferenciaFPC,dataReferenciaFPI,dataReferenciaP,dataFatPotUm,dataFatPot_Sim];

        var chart_2 = c3.generate({
            bindto: '#fatpot-chart',
            size: {
                height: 220
            },
            padding: {
                top: 0,
                right: 10,
                bottom: 30,
                left: 50
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'area-step',
                    data2: 'area-step',
                    data3: 'area-step',
                    data4: 'area-step',
                    data5: 'step',
                    data6: 'step'
                },
                color: function (color, d) {
                    if (typeof d.index === 'undefined') { return color; }

                    if( Periodo[d.index] == 0 )
                        return '#FF0000';

                    if( Periodo[d.index] == 1 )
                        return '#007F00';

                    if( Periodo[d.index] == 2 )
                        return '#1C84C6';

                    return '#007F00';
                },
                colors: {
                    data1: '#001F00',
                    data2: '#1C84C6',
                    data3: '#007F00',
                    data4: '#FF0000',
                    data5: '#000000',
                    data6: '#00b1ff'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x: {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*7
                },
                y:  {
                    max: maximo,
                    min: minimo,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            var valor;

                            // converte para nova escala
                            if( d < 0 )
                            {
                                valor = (1.0 - (-1.0*d)) * -1.0;
                            }
                            else
                            {
                                valor = (1.0 - d);
                            }

                            return valor.toFixed(3);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.7 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, unit, periodo, bgcolor;


                    // checkbox aplica simulacao
                    var simula = $('#aplica_simula').prop('checked');
                    var tooltip_index = 4;
                    if (simula == true)
                    {
                        tooltip_index = 7;
                    }

                    for (i = 0; i < tooltip_index; i++) {

                        // titulo
                        if (! text) {

                            // split string and create array.
                            var arr = Datas[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // horas e minutos
                            var hours = data.getHours();
                            var minutes = data.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            title = hours + ':' + minutes;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // periodo
                        switch(Periodo[d[0].index])
                        {
                            case 0:
                                periodo = PONTA;
                                bgcolor = '#FF0000';
                                break;

                            case 1:
                                periodo = FPONTA_IND;
                                bgcolor = '#007F00';
                                break;

                            case 2:
                                periodo = FPONTA_CAP;
                                bgcolor = '#1C84C6';
                                break;

                            default:
                                periodo = "---";
                                bgcolor = '#000000';
                                break;
                        }

                        // nome
                        switch(i)
                        {
                            case 0:
                                name = DEMANDA_ATIVA;
                                unit = UnidadeDemanda;
                                value = DemandaAtv[d[0].index].toFixed(1);
                                break;

                            case 1:
                                name = DEMANDA_REATIVA;
                                unit = UnidadeReativo;
                                value = DemandaRtv[d[0].index].toFixed(1);
                                break;

                            case 2:
                                name = FATOR_POTENCIA;
                                unit = '';
                                value = FatPot[d[0].index].toFixed(3);
                                break;

                            case 3:
                                name = PERIODO;
                                unit = '';
                                value = periodo;
                                break;

                            case 4:
                                name = DEMANDA_ATIVA;
                                unit = UnidadeDemanda;
                                value = DemandaAtv_Sim[d[0].index].toFixed(1);
                                break;

                            case 5:
                                name = DEMANDA_REATIVA;
                                unit = UnidadeReativo;
                                value = DemandaRtv_Sim[d[0].index].toFixed(1);
                                break;

                            case 6:
                                name = FATOR_POTENCIA;
                                unit = '';
                                value = FatPot_Sim[d[0].index].toFixed(3);
                                break;
                        }

                        value = value.replace('.', ',');

                        // verifica se sem registro
                        if( Periodo[d[0].index] == 3 )
                        {
                            value = '---';
                        }

                        if(i==3)
                        {
                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                            text += "<td class='name'>" + name + "</td>";
                            text += "<td class='value'>" + value + "</td>";
                            text += "</tr>";
                        }
                        else
                        {
                            if (i > 3)
                            {
                                name += " - " + SIMULACAO;
                                bgcolor = "#00b1ff";
                            }

                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                            text += "<td class='value'>" + value + " " + unit + "</td>";
                            text += "</tr>";
                        }
                    }

                    return text + "</table>";
                }
            }
        });

        // salva grafico demanda
        $('#demanda-chart').data('c3-chart', chart);

        // salva grafico fator de potencia
        $('#fatpot-chart').data('c3-chart', chart_2);

        ShowSimulacao();

        // labels
        $('.chart-legend').css("display", "block");

        $(".modal-dialog").draggable({
            handle: ".modal-editar-header"
        });
    });

    function ShowSimulacao () {

        // IDSimulacaoCenario
        var IDSimulacaoCenario = parseInt(@ViewBag._IDSimulacaoCenario);

        // verifica se ano existe nenhuma simulacao selecionada
        if (IDSimulacaoCenario == 0)
        {
            $("#aplica_simula").attr('disabled', true);
            $('#aplica_simula').prop('checked', false);
        }
        else
        {
            $("#aplica_simula").attr('disabled', false);
        }

        // grafico demanda
        var chart = $('#demanda-chart').data('c3-chart');

        // grafico fator de potencia
        var chart_2 = $('#fatpot-chart').data('c3-chart');

        // checkbox aplica simulacao
        var simula = $('#aplica_simula').prop('checked');

        // verifica se deve apresentar simulacao
        if (simula)
        {
            // apresenta grafico simulacao
            chart.show(['data6']);
            chart_2.show(['data6']);

            $('.simulacao').css("display", "block");
            $('.relatorio_real').css("display", "none");
        }
        else
        {
            // apresenta grafico real
            chart.hide(['data6']);
            chart_2.hide(['data6']);

            $('.simulacao').css("display", "none");
            $('.relatorio_real').css("display", "block");
        }

        // salva em cookie
        setCookie("AplicaSimulacao", simula, null);
    }

</script>

    