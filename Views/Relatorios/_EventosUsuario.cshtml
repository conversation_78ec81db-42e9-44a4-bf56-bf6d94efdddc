﻿@using System.Globalization
@using SmartEnergyLib.SQL

<div class="row" id="select2_tipo_2" style="display:none;">
    <div class="col-lg-12">
        <div class="panel panel-default">
            <div class="panel-body">

                <div class="row">
                    <div class="form-group col-lg-12">
                        @Html.DropDownList("Usuarios", new SelectList(ViewBag.listaUsuarios, "IDUsuario", "NomeUsuario"),
                                                        "Todos os Usuarios",
                                                        new { @class = "form-control", style="width:100%" })
                    </div>
                </div>

                <div>
                    <select class="select2_tipo_2 form-control" multiple="multiple">

                        @{
                            string icone_tipo = "fa-th-list";
                            int tipo = 0;

                            List<ListaTiposDominio> listaTiposEventos = ViewBag.listatiposEventos;

                            if (listaTiposEventos != null)
                            {
                                foreach (ListaTiposDominio tipoEvento in listaTiposEventos)
                                {
                                    tipo = tipoEvento.ID;
                                    
                                    switch (tipo)
                                    {
                                        default:
                                            continue;

                                        case 1:
                                            icone_tipo = "fa-sign-in";
                                            break;

                                        case 2:
                                            icone_tipo = "fa-gear";
                                            break;

                                        case 3:
                                            icone_tipo = "fa-wrench";
                                            break;
                                    }

                                    <option value=@tipo data-icon=@icone_tipo>&nbsp;&nbsp;@tipoEvento.Descricao</option>
                                }
                            }
                        }

                    </select>
                </div>

                <br /><br />

                <table id="dataTables-eventos" class="table table-striped table-bordered table-hover dataTables-eventos">
                    <thead>
                        <tr>
                            <th>@SmartEnergy.Resources.RelatoriosTexts.Hora</th>
                            <th>IDUsuario</th>
                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Usuarios</th>
                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.TipoAcesso</th>
                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Cliente</th>
                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Consultor</th>
                            <th>@SmartEnergy.Resources.RelatoriosTexts.Tipo</th>
                            <th style="text-align:center;"><i class="fa fa-tags" style="font-size:20px;"></i></th>
                            <th>@SmartEnergy.Resources.RelatoriosTexts.Evento</th>
                        </tr>
                    </thead>
                    <tbody>

                        @{
                            List<EventosUsuarioDescricao> eventos = ViewBag.listaEventosDescricao;
                            
                            if( eventos != null )
                            { 
                                foreach (var evento in eventos)
                                {
                                    // tipo evento                                    
                                    icone_tipo = "fa-th-list";
                                    tipo = evento.Tipo;

                                    switch (tipo)
                                    {
                                        default:
                                            continue;

                                        case 1:
                                            icone_tipo = "fa-sign-in";
                                            break;

                                        case 2:
                                            icone_tipo = "fa-gear";
                                            break;

                                        case 3:
                                            icone_tipo = "fa-wrench";
                                            break;
                                    }

                                    <tr>
                                        <td><span style="display:none;">@evento.DataHora_Sort</span>@evento.DataHora</td>
                                        <td>@evento.IDUsuario</td>
                                        <td>@evento.NomeUsuario</td>
                                        <td>@evento.NomeTipoAcesso</td>
                                        <td>@evento.NomeCliente</td>
                                        <td>@evento.NomeGestor</td>
                                        <td>@evento.Tipo</td>
                                        <td style="text-align:center;"><i class="fa @icone_tipo" style="font-size:20px;"></i></td>
                                        <td>@evento.Descricao</td>
                                    </tr>
                                }
                            }
                        }

                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">

    $(document).ready(function () {

        $('#Usuarios').select2();

        //
        // Tabela resultado eventos
        //

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {
                var x = (a == "-") ? 0 : a.replace(/,/, ".");
                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        var table = $('.dataTables-eventos').DataTable({
            "iDisplayLength": 12,
            dom: 'ftp',

            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "portugues", "bVisible": true, "bSortable": true, "bSearchable": true },
                        { "aTargets": [1], "sType": "numero", "bVisible": false, "bSortable": true, "bSearchable": true },
                        { "aTargets": [2], "sType": "portugues", "bVisible": true, "bSortable": true, "bSearchable": true },
                        { "aTargets": [3], "sType": "portugues", "bVisible": true, "bSortable": true, "bSearchable": true },
                        { "aTargets": [4], "sType": "portugues", "bVisible": true, "bSortable": true, "bSearchable": true },
                        { "aTargets": [5], "sType": "portugues", "bVisible": true, "bSortable": true, "bSearchable": true },
                        { "aTargets": [6], "sType": "numero", "bVisible": false, "bSortable": true, "bSearchable": true },
                        { "aTargets": [7], "sType": "portugues", "bVisible": true, "bSortable": false, "bSearchable": false },
                        { "aTargets": [8], "sType": "portugues", "bVisible": true, "bSortable": true, "bSearchable": true },
            ],
            "oSearch": {
                "bSmart": false,
                "bRegex": true,
                "sSearch": ""
            },
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "10%" },
            { sWidth: "1%" },
            { sWidth: "15%" },
            { sWidth: "10%" },
            { sWidth: "15%" },
            { sWidth: "15%" },
            { sWidth: "1%" },
            { sWidth: "5%" },
            { sWidth: "35%" }
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

        // cookie de search
        var search = decodeURIComponent(getCookie("Relat_Search"));
        $('.dataTables-eventos').DataTable().search(search).draw();

        // cookie de sort
        var sortedCol = getCookie("Relat_SortedCol");
        if (sortedCol == "") sortedCol = 0;
        var sortedDir = getCookie("Relat_SortedDir");

        if (sortedCol == 0 || sortedCol == 2 || sortedCol == 3 || sortedCol == 4 || sortedCol == 5 || sortedCol == 8) {
            $('#dataTables-eventos').dataTable().fnSort([[sortedCol, sortedDir]]);
        }

        // trigger datatable
        $('.dataTables-eventos')
                .on('order.dt', function () { EventoOrder(); })
                .on('search.dt', function () { EventoSearch(); })
                .DataTable();


        function formatIcon(icon) {

            if (!icon.id) {
                return icon.text;
            }
            var originalOption = icon.element;
            var $icon = $('  <i style="font-size:16px;" class="fa ' + $(originalOption).data('icon') + '"></i><span>' + icon.text + '</span>');

            return $icon;

        };


        //
        // Usuarios
        //

        // caso mudar, atualizo
        $('#Usuarios').change(function () {

            // obtem o tipo de busca
            var id = $(this).find(":selected").val();

            // procura na tabela
            $('.dataTables-eventos').DataTable().column(1).search(id, true, false).draw();

            // salva em cookie
            setCookie("Relat_IDUsuario", id, null);
        });

        // cookie de filtro
        var IDUsuario_selecionado = getCookie("Relat_IDUsuario");

        // procura na tabela
        $('.dataTables-eventos').DataTable().column(1).search(IDUsuario_selecionado, true, false).draw();

        // seleciona na combo
        $('#Usuarios').val(IDUsuario_selecionado).trigger('change');

        //
        // Filtro eventos
        //

        var placeholder = SELECIONE_EVENTO_FILTRO;

        $(".select2_tipo_2").select2({
            placeholder: placeholder,
            minimumResultsForSearch: Infinity,
            width: "100%",
            templateResult: formatIcon,
            templateSelection: formatIcon,
            allowClear: true,
            closeOnSelect: true,
            escapeMarkup: function (m) {
                return m;
            }
        })
        .on("select2:select", function (e) {
            AdicionouTipo();
        })
        .on("select2:unselect", function (e) {
            RemoveuTipo();
        })
        .on("select2:unselecting", function (e) {
            $(this).data('state', 'unselected');
        })
        .on("select2:open", function (e) {
            if ($(this).data('state') === 'unselected') {
                $(this).removeData('state');
                var self = $(this);
                setTimeout(function () {
                    self.select2('close');
                }, 1);
            }
        });

        // cookie de filtro
        var lista_selecao = getCookie("Relat_TipoEvento");

        // troco / por |
        var str = lista_selecao.replace(/[\/\/]/g, "|");

        // setar selecao na lista
        var lista = lista_selecao.split("/");

        // procura na tabela
        $('.dataTables-eventos').DataTable().column(6).search(str, true, false).draw();

        // labels
        $('#select2_tipo_2').css("display", "block");

        // aplica filtros
        $(".select2_tipo_2").select2("val", lista);
    });

function getCookie(c_name) {

    // Get name followed by anything except a semicolon
    var cookiestring = RegExp("" + c_name + "[^;]+").exec(document.cookie);

    // Return everything after the equal sign, or an empty string if the cookie name not found
    return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./, "") : "");
}

function setCookie(c_name, value, expiresecs) {
    var exdate = new Date();
    exdate.setTime(exdate.getTime() + ((expiresecs) ? expiresecs * 1000 : 0));

    document.cookie = c_name + "=" + escape(value) + ((expiresecs == null) ? "" : ";expires=" + exdate.toGMTString()) + ";path=/";
}



function Insere(lista_selecao, valor) {

    if (lista_selecao != "") {
        lista_selecao = lista_selecao + "|" + valor;
    }
    else {
        lista_selecao = lista_selecao + valor;
    }

    return (lista_selecao);
}

function ProcurarTipo() {

    // selecionados
    var selecionados = $('.select2_tipo_2').val();

    var lista_selecao = "";

    if (selecionados != null) {
        // percorre selecionados
        for (i = 0; i < selecionados.length; i++) {
            lista_selecao = Insere(lista_selecao, selecionados[i]);
        }
    }

    // troco | por /
    var str = lista_selecao.replace(/[\|\/]/g, "/");

    // salva em cookie
    setCookie("Relat_TipoEvento", str, null);

    // procura na tabela
    $('.dataTables-eventos').DataTable().column(6).search(lista_selecao, true, false).draw();
}

function RemoveuTipo() {

    ProcurarTipo();

    // fecha list
    $('.select2_tipo_2').select2("close");
}

function AdicionouTipo() {

    ProcurarTipo();
}

function EventoOrder() {

    // pega coluna de sort
    var sortedCol = $('#dataTables-eventos').dataTable().fnSettings().aaSorting[0][0];

    // salva em cookie
    setCookie("Relat_SortedCol", sortedCol, null);

    // pega direcao de sort
    var sortedDir = $('#dataTables-eventos').dataTable().fnSettings().aaSorting[0][1];

    // salva em cookie
    setCookie("Relat_SortedDir", sortedDir, null);
}

function EventoSearch() {

    // pega palavra search
    var value = $('.dataTables_filter input').val();

    // caso for suficiente, salvo no cookie
    if (value.length > 3 || value.length == 0) {

        // salva em cookie
        setCookie("Relat_Search", encodeURIComponent(value), null);
    }
}

</script>

