﻿<style>

    .icone_semana {
    padding: 0px 7px;
    font-size: 14px;
    float: left; 
    color: white;
}

.c3-line {
    stroke-width: 1px;
}

</style>

<div class="row">
    <div class="col-lg-4 col-md-12">
        <div class="row">
            <div class="row">
                <div class="col-lg-6">
                    <div class="relat-coluna">
                        <div class="relat-valorAtual relat-ea">
                            <h4>@ViewBag.NomeGrandeza</h4>
                            <h5>@SmartEnergy.Resources.RelatoriosTexts.MediaSemana</h5>
                            <h1>@ViewBag.VMedSemana <span>@ViewBag.UnidadeGrandeza</span></h1>
                            <h6>@ViewBag.DataTextoAtual</h6>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="relat-coluna">
                        <div class="relat-valorAtual relat-preto">
                            <h4>@ViewBag.NomeGrandeza</h4>
                            <h5>@SmartEnergy.Resources.RelatoriosTexts.Maxima</h5>
                            <h1>@ViewBag.VMaxSemana <span>@ViewBag.UnidadeGrandeza</span></h1>
                            <h6>@ViewBag.VMaxSemana_DataHora</h6>
                        </div>
                    </div>
                </div>
            </div>
            <br /><br />
            <div class="row">
                <div class="col-lg-offset-6 col-lg-6">
                    <div class="relat-coluna">
                        <div class="relat-valorAtual relat-preto">
                            <h4>@ViewBag.NomeGrandeza</h4>
                            <h5>@SmartEnergy.Resources.RelatoriosTexts.Minima</h5>
                            <h1>@ViewBag.VMinSemana <span>@ViewBag.UnidadeGrandeza</span></h1>
                            <h6>@ViewBag.VMinSemana_DataHora</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-8 col-md-12">
        <div class="row">
            <div class="relat-grafico">
                <div id="grafico-ea"></div>
                <div class='chart-legend' style="display:none;">
                    <div class='legend-scale-dem'>
                        <ul class='legend-labels' style="margin-top: 0px;">
                            <li><a href="#" onclick="alteraDiaSemana(0,true,0);"><span style='background:#0000FF;' id="ckDomingo"><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.domingo</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(1,true,0);"><span style='background:#00FF00;' id="ckSegunda"><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.segunda</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(2,true,0);"><span style='background:#FF0000;' id="ckTerca"  ><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.terca</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(3,true,0);"><span style='background:#FF7F00;' id="ckQuarta" ><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.quarta</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(4,true,0);"><span style='background:#FF00FF;' id="ckQuinta" ><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.quinta</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(5,true,0);"><span style='background:#000000;' id="ckSexta"  ><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.sexta</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(6,true,0);"><span style='background:#00FFFF;' id="ckSabado" ><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.sabado</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    @Html.Partial("_Relat_Botoes")

<br />
<div class="panel panel-title relatorio">
    <div class="panel-body">

        <div class="row">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-ea" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.MediaSemana</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Maxima</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Minima</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@ViewBag.NomeGrandeza</td>
                                <td class="relat-ea">@ViewBag.VMedSemana @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.DataTextoAtual)</small></td>
                                <td class="relat-preto">@ViewBag.VMaxSemana @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMaxSemana_DataHora)</small></td>
                                <td class="relat-preto">@ViewBag.VMinSemana @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMinSemana_DataHora)</small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal inmodal animated fadeIn" id="modalTabela" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-editar-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">@SmartEnergy.Resources.ComumTexts.BotaoFechar</span></button>
                <h4 class="modal-title"><i class="fa fa-list"></i>&nbsp;&nbsp;@SmartEnergy.Resources.RelatoriosTexts.TabelaRegistros</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-default">

                    @{
                        var k = 0;

                        var dia_semana = new string[] { SmartEnergy.Resources.ComumTexts.domingo, 
                                                                    SmartEnergy.Resources.ComumTexts.segunda,
                                                                    SmartEnergy.Resources.ComumTexts.terca,
                                                                    SmartEnergy.Resources.ComumTexts.quarta,
                                                                    SmartEnergy.Resources.ComumTexts.quinta,
                                                                    SmartEnergy.Resources.ComumTexts.sexta,
                                                                    SmartEnergy.Resources.ComumTexts.sabado
                                                                };
                    }

                    <table class="table no-margins tabela-grafico-pdf">
                        <thead>
                            <tr>
                                <th style="width:16%;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>

                                @for (k = 0; k < 7; k++)
                                {
                                    <th style="width:12%;">@dia_semana[k]<br />@ViewBag.Dias[k]<br />(@ViewBag.UnidadeGrandeza)</th>
                                }    

                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var i = 0;
                                var j = 0;
                                var temp = "---";

                                for (i = 0; i < 24; i++)
                                {
                                    j = i + 1;

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="relat-preto">@ViewBag.Horas[j]</td>                                        

                                        @for (k = 0; k < 7; k++)
                                        {
                                            if (ViewBag.Tempo_Cod[k, j] >= 0)
                                            {
                                                temp = string.Format("{0:0}", ViewBag.Temp[k, j]);
                                            }
                                            else
                                            {
                                                temp = "---";
                                            }

                                            <td style="text-align:center;" class="relat-ea">@temp</td>

                                        }    

                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
            </div>
        </div>
    </div>
</div>


@Html.Partial("_EventosModal")

@Html.Partial("_ObservacoesModal")


<script type="text/javascript">

    // dias da semana
    var diasSemana = [];

    diasSemana.push('Domingo');
    diasSemana.push('Segunda');
    diasSemana.push('Terça');
    diasSemana.push('Quarta');
    diasSemana.push('Quinta');
    diasSemana.push('Sexta');
    diasSemana.push('Sábado');
    diasSemana.push('Período');

    // cores
    var coresdiasSemana = [];

    coresdiasSemana.push('#0000FF');
    coresdiasSemana.push('#00FF00');
    coresdiasSemana.push('#FF0000');
    coresdiasSemana.push('#FF7F00');
    coresdiasSemana.push('#FF00FF');
    coresdiasSemana.push('#000000');
    coresdiasSemana.push('#00FFFF');

    // c_names
    var c_names = [];

    c_names.push("Relat_SemanalDomingo");
    c_names.push("Relat_SemanalSegunda");
    c_names.push("Relat_SemanalTerca");
    c_names.push("Relat_SemanalQuarta");
    c_names.push("Relat_SemanalQuinta");
    c_names.push("Relat_SemanalSexta");
    c_names.push("Relat_SemanalSabado");

    // id_names
    var id_names = [];

    id_names.push("#ckDomingo");
    id_names.push("#ckSegunda");
    id_names.push("#ckTerca");
    id_names.push("#ckQuarta");
    id_names.push("#ckQuinta");
    id_names.push("#ckSexta");
    id_names.push("#ckSabado");


    $(document).ready(function () {

        // copia valores
        var Temp = @Html.Raw(Json.Encode(@ViewBag.Temp));
        var Tempo_Cod = @Html.Raw(Json.Encode(@ViewBag.Tempo_Cod));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));
        var NomeGrandeza = @Html.Raw(Json.Encode(@ViewBag.NomeGrandeza));
        var UnidadeGrandeza = @Html.Raw(Json.Encode(@ViewBag.UnidadeGrandeza));

        // valor minimo e máximo
        var minimoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMin));
        var maximoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMax));

        var diferenca = maximoValor - minimoValor;

        // grafico
        var dataX = [];
        var labelX = [];
        var dataDom = [];
        var dataSeg = [];
        var dataTer = [];
        var dataQua = [];
        var dataQui = [];
        var dataSex = [];
        var dataSab = [];

        dataX.push('x');
        dataDom.push(['data1']);
        dataSeg.push(['data2']);
        dataTer.push(['data3']);
        dataQua.push(['data4']);
        dataQui.push(['data5']);
        dataSex.push(['data6']);
        dataSab.push(['data7']);


        for (i = 0; i < 26; i++)
        {
            // X
            dataX.push(Datas[i]);

            dataDom.push([ Temp[i]        ]);
            dataSeg.push([ Temp[i+26]     ]);
            dataTer.push([ Temp[i+(2*26)] ]);
            dataQua.push([ Temp[i+(3*26)] ]);
            dataQui.push([ Temp[i+(4*26)] ]);
            dataSex.push([ Temp[i+(5*26)] ]);
            dataSab.push([ Temp[i+(6*26)] ]);
        }

        // valores label X
        labelX.push(Datas[1]);
        for (i = 3; i <= 24; i+=3)
        {
            labelX.push(Datas[i]);
        }

        var Data = [dataX,dataDom,dataSeg,dataTer,dataQua,dataQui,dataSex,dataSab];

        var chart = c3.generate({

            bindto: '#grafico-ea',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'line',
                    data2: 'line',
                    data3: 'line',
                    data4: 'line',
                    data5: 'line',
                    data6: 'line',
                    data7: 'line',
                },
                colors: {
                    data1: '#0000FF',
                    data2: '#00FF00',
                    data3: '#FF0000',
                    data4: '#FF7F00',
                    data5: '#FF00FF',
                    data6: '#000000',
                    data7: '#00FFFF',
                },
            },
            point: {
                r:3
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*28
                },
                y:  {
                    min: minimoValor,
                    max: maximoValor,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( diferenca < 10)
                            {
                                return d.toFixed(2);
                            }

                            if( diferenca < 100)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },

            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < d.length; i++) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        // titulo
                        if (! text) {

                            // split string and create array.
                            var arr = Datas[d[0].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // horas e minutos
                            var hours = data.getHours();
                            var minutes = data.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            title = hours + ':' + minutes;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1C84C6'>" + title + "</th></tr>" : "");
                        }

                        // pega o indice
                        var indice = d[i].name.toString().substr(4, 2);
                        

                        // verifica se eh dia da semana
                        if( indice < 8)
                        {
                            name = diasSemana[indice-1];
                            bgcolor = coresdiasSemana[indice-1];
                            value = Temp[d[0].index+((indice-1)*26)].toFixed(0);

                            var str_valor = value + " " + UnidadeGrandeza;

                            if (Tempo_Cod[d[0].index+((indice-1)*26)] < 0)
                            {
                                str_valor = "---";
                            }

                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                            text += "<td class='value'>" + str_valor + "</td>";
                            text += "</tr>";
                        }
                    }

                    return text + "</table>";
                }
            }
        });

        // salva chart
        $('#grafico-ea').data('c3-chart', chart);

        // label dias da semana
        todosDiaSemana();

        // labels
        $('.chart-legend').css("display", "block");

        $(".modal-dialog").draggable({
            handle: ".modal-editar-header"
        });
    });


    function getCookie(c_name) {

        // Get name followed by anything except a semicolon
        var cookiestring = RegExp("" + c_name + "[^;]+").exec(document.cookie);

        // Return everything after the equal sign, or an empty string if the cookie name not found
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./, "") : "");
    }

    function setCookie(c_name, value, expiresecs) {
        var exdate = new Date();
        exdate.setTime(exdate.getTime() + ((expiresecs) ? expiresecs * 1000 : 0));

        document.cookie = c_name + "=" + escape(value) + ((expiresecs == null) ? "" : ";expires=" + exdate.toGMTString()) + ";path=/";
    }

    function todosDiaSemana() {
        alteraDiaSemana(0,false);
        alteraDiaSemana(1,false);
        alteraDiaSemana(2,false);
        alteraDiaSemana(3,false);
        alteraDiaSemana(4,false);
        alteraDiaSemana(5,false);
        alteraDiaSemana(6,false);

    }

    function alteraDiaSemana(dia_semana, alterar) {

        var c_name,id_name,color_name;

        // dia da semana
        switch(dia_semana)
        {
            case 0:
                c_name = "Relat_SemanalDomingo";
                id_name = "#ckDomingo";
                color_name = "#0000FF";
                break;

            case 1:
                c_name = "Relat_SemanalSegunda";
                id_name = "#ckSegunda";
                color_name = "#00FF00";
                break;

            case 2:
                c_name = "Relat_SemanalTerca";
                id_name = "#ckTerca";
                color_name = "#FF0000";
                break;

            case 3:
                c_name = "Relat_SemanalQuarta";
                id_name = "#ckQuarta";
                color_name = "#FF7F00";
                break;

            case 4:
                c_name = "Relat_SemanalQuinta";
                id_name = "#ckQuinta";
                color_name = "#FF00FF";
                break;

            case 5:
                c_name = "Relat_SemanalSexta";
                id_name = "#ckSexta";
                color_name = "#000000";
                break;

            case 6:
                c_name = "Relat_SemanalSabado";
                id_name = "#ckSabado";
                color_name = "#00FFFF";
                break;
        }

        // le cookie
        var estado = getCookie(c_name);

        if(estado == "")
        {
            estado = 1;
        }

        // verifica se quer alterar
        if( alterar )
        {
            // se estiver ligado, desliga
            estado = (estado == 0) ? 1 : 0;
        }

        // data series
        var data = "data" + (dia_semana+1);
        var chart = $('#grafico-ea').data('c3-chart');

        // verifica estado
        if( estado == 1 )
        {
            chart.show([data]);

            $(id_name).css("background", color_name);
        }
        else
        {
            chart.hide([data]);

            $(id_name).css("background", "white");
        }

        // salva em cookie
        setCookie(c_name, estado, null);
    }

</script>
