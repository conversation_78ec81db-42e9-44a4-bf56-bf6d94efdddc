﻿<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao</title>

    <link href="@Server.MapPath("~/Content/bootstrap.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/style.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/plugins/c3/c3.min.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/Relatorios.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/RelatoriosPDF.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/fonts/font-awesome/css/font-awesome.min.css")" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="@Server.MapPath("~/Scripts/jquery-2.1.1.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/d3/d3.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/c3/c3.min.js")"></script>

</head>

@{
    string arquivo = "~/Logos/" + ViewBag._LogoConsultor;
    string logo = Server.MapPath(arquivo);
}

@if (ViewBag.Retorno == 2)
{
    <div class="row">
        <div class="col-lg-12">
            <div class="linha cabecalho">
                <div class="coluna1">
                    <p><img src="@logo" style="height:42px; width:auto;" /></p>
                </div>

                <div class="coluna2">
                    <h3>@ViewBag.ClienteNome</h3>
                    @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                </div>

                <div class="coluna3">
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao - @SmartEnergy.Resources.RelatoriosTexts.RelatDiario (@SmartEnergy.Resources.RelatoriosTexts.Simulacao)</h4>
                    <p>@ViewBag.DataAtual</p>
                </div>
            </div>
        </div>
    </div>

    <br /><br /><br /><br /><br /><br />
    <div class="row">
        <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
            <i class="fa fa-warning fa-5x"></i>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>@SmartEnergy.Resources.RelatoriosTexts.NaoExistemRegistros</h2>
        </div>
    </div>
    <br /><br /><br /><br />

}
else
{

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="@logo" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao - @SmartEnergy.Resources.RelatoriosTexts.RelatDiario (@SmartEnergy.Resources.RelatoriosTexts.Simulacao)</h4>
                        <p>@ViewBag.DataAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <br />

        @{
            if (ViewBag.showGrafico)
            {
                <br /><br />
                <div class="row">
                    <div class="relat-grafico" style="height:450px; width:800px; margin-left:50px;">
                        <div id="fatutilizacao-chart-simulacao" style="margin-top: -10px"></div>
                        <div class='chart-legend' style="display:none;">
                            <div class='legend-scale-dem'>
                                <ul class='legend-labels' style="margin-top: -25px;">
                                    <li><span style='background:#1C84C6 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                                    <li><span style='background:#007f00 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                                    <li><span style='background:#ff0000 !important;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }

        <hr />

        <div class="row" style="margin-top: -15px;">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao @SmartEnergy.Resources.RelatoriosTexts.Maximo</td>
                                <td class="relat-capacitivo">@ViewBag.FatUtilizacao_MaxFPC_Sim %<br /><small>(@ViewBag.FatUtilizacao_MaxFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.FatUtilizacao_MaxFPI_Sim %<br /><small>(@ViewBag.FatUtilizacao_MaxFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.FatUtilizacao_MaxP_Sim %<br /><small>(@ViewBag.FatUtilizacao_MaxP_DataHora_Sim)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao @SmartEnergy.Resources.RelatoriosTexts.Minimo</td>
                                <td class="relat-capacitivo">@ViewBag.FatUtilizacao_MinFPC_Sim %<br /><small>(@ViewBag.FatUtilizacao_MinFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.FatUtilizacao_MinFPI_Sim %<br /><small>(@ViewBag.FatUtilizacao_MinFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.FatUtilizacao_MinP_Sim %<br /><small>(@ViewBag.FatUtilizacao_MinP_DataHora_Sim)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao @SmartEnergy.Resources.RelatoriosTexts.Periodo</td>
                                <td class="relat-capacitivo">@ViewBag.FatUtilizacao_MedFPC_Sim %<br /><small>&nbsp;</small></td>
                                <td class="relat-fponta">@ViewBag.FatUtilizacao_MedFPI_Sim %<br /><small>&nbsp;</small></td>
                                <td class="relat-ponta">@ViewBag.FatUtilizacao_MedP_Sim %</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    @{
        if (ViewBag.showTabela)
        {
            <div class="page-break"></div>
            
            <div class="row">

                <br /><br />

                @{
                    var pagina = 0;

                    for (pagina = 0; pagina < 2; pagina++)
                    {
                        <div style="width:43%; float: left !important; margin-left:40px !important;" class="panel panel-default">
                            <table class="table no-margins tabela-grafico-pdf">
                                <thead>
                                    <tr>
                                        <th style="width:40%;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>
                                        <th style="width:30%;">@SmartEnergy.Resources.RelatoriosTexts.Periodo</th>
                                        <th style="width:30%;">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao<br />(%)</th>
                                    </tr>
                                </thead>

                                <tbody>

                                    @{
                                        var i = 0;
                                        var j = 0;
                                        var classe_sim = "relat-semreg";
                                        var periodo_sim = "---";
                                        var fatUtilizacao_sim = "---";

                                        for (i = 0; i < 48; i++)
                                        {
                                            j = i + 1 + (pagina * 48);

                                            switch ((int)ViewBag.Periodo[j])
                                            {
                                                case 0:
                                                    classe_sim = "relat-ponta";
                                                    periodo_sim = "P";
                                                    break;

                                                case 1:
                                                    classe_sim = "relat-indutivo";
                                                    periodo_sim = "FPI";
                                                    break;

                                                case 2:
                                                    classe_sim = "relat-capacitivo";
                                                    periodo_sim = "FPC";
                                                    break;

                                                default:
                                                case 3:
                                                    classe_sim = "relat-semreg";
                                                    periodo_sim = "---";
                                                    break;
                                            }

                                            if (ViewBag.Periodo[j] == 3)
                                            {
                                                fatUtilizacao_sim = "---";
                                            }
                                            else
                                            {
                                                fatUtilizacao_sim = string.Format("{0:#,##0.0}", ViewBag.FatUtilizacao_Sim[j]);
                                            }

                                            <tr class="tabela_valores" style="font-size:12px;">
                                                <td style="text-align:center; padding:2px; font-size:12px;" class="@classe_sim">@ViewBag.Horas[j]</td>
                                                <td style="text-align:center; padding:2px; font-size:12px;" class="@classe_sim">@periodo_sim</td>
                                                <td style="text-align:center; padding:2px; font-size:12px;" class="@classe_sim">@fatUtilizacao_sim</td>
                                            </tr>
                                        }
                                    }

                                </tbody>
                            </table>

                        </div>
                    }
                }

            </div>
        }
    }                    

    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>

<script type="text/javascript">

    $(document).ready(function () {

        // fator de utilizacao simulacao
        var dataX_Sim = [];
        var labelX_Sim = [];
        var dataFatUtilizacao_Sim = [];
        var dataP_Sim = [];
        var dataFPI_Sim = [];
        var dataFPC_Sim = [];

        // copia valores
        var FatUtilizacao_Sim = @Html.Raw(Json.Encode(@ViewBag.FatUtilizacao_Sim));
        var Demanda_Sim = @Html.Raw(Json.Encode(@ViewBag.DemandaAtv_Sim));
        var Contrato_Sim = @Html.Raw(Json.Encode(@ViewBag.Contrato_Sim));
        var Periodo = @Html.Raw(Json.Encode(@ViewBag.Periodo));
        var Datas_Sim = @Html.Raw(Json.Encode(@ViewBag.Datas_Sim));

        var maximoFatUtilizacao = @Html.Raw(Json.Encode(@ViewBag.FatUtilizacaoMaxGrafico));

        // verifico se nao tem dados e forco um limite
        if( maximoFatUtilizacao == 0.0)
        {
            maximoFatUtilizacao = 100.0;
        }

        dataX_Sim.push('x');
        dataFatUtilizacao_Sim.push(['data1']);
        dataP_Sim.push(['data2']);
        dataFPI_Sim.push(['data3']);
        dataFPC_Sim.push(['data4']);

        for (i = 0; i < 98; i++)
        {
            // X
            dataX_Sim.push(Datas_Sim[i]);

            dataFatUtilizacao_Sim.push([FatUtilizacao_Sim[i]]);

            if( Periodo[i] == 0 )
            {
                dataP_Sim.push([maximoFatUtilizacao]);
                dataFPI_Sim.push([0]);
                dataFPC_Sim.push([0]);
            }
            else if( Periodo[i] == 1 )
            {
                dataP_Sim.push([0]);
                dataFPC_Sim.push([0]);
                dataFPI_Sim.push([maximoFatUtilizacao]);
            }
            else if( Periodo[i] == 2 )
            {
                dataP_Sim.push([0]);
                dataFPI_Sim.push([0]);
                dataFPC_Sim.push([maximoFatUtilizacao]);
            }
            else
            {
                dataP_Sim.push([0]);
                dataFPI_Sim.push([0]);
                dataFPC_Sim.push([0]);
            }
        }

        // valores label X
        labelX_Sim.push(Datas_Sim[1]);
        for (i = 12; i <= 96; i+=12)
        {
            labelX_Sim.push(Datas_Sim[i]);
        }

        var Data = [dataX_Sim,dataFatUtilizacao_Sim,dataP_Sim,dataFPI_Sim,dataFPC_Sim];

        c3.generate({

            bindto: '#fatutilizacao-chart-simulacao',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'area-step',
                    data2: 'area-step',
                    data3: 'area-step',
                    data4: 'area-step'
                },
                color: function (color, d) {
                    if (typeof d.index === 'undefined') { return color; }

                    if( Periodo[d.index] == 0 )
                        return '#FF0000';

                    if( Periodo[d.index] == 1 )
                        return '#007F00';

                    if( Periodo[d.index] == 2 )
                        return '#1C84C6';

                    return '#007F00';
                },
                colors: {
                    data1: '#001F00',
                    data2: '#FF0000',
                    data3: '#007F00',
                    data4: '#1C84C6'
                },
                onclick: function (d, element) {
                    alert(d.value);
                }
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x: {
                    show:false,
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX_Sim,
                        format: function (x) {

                            return "";
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*6
                },
                y:  {
                    max: maximoFatUtilizacao,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoFatUtilizacao < 10)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.7 },
            },
        });

        // labels
        $('.chart-legend').css("display", "block");

    });

</script>

}
