﻿@using SmartEnergyLib.SQL

<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.EventosUsuario</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

    <link href="@Server.MapPath("~/Content/bootstrap.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/style.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/plugins/select2/select2.min.css")" rel="stylesheet" />
    <link href="@Server.MapPath("~/Content/plugins/dataTables/datatables.min.css")" rel="stylesheet" />
    <link href="@Server.MapPath("~/Content/Relatorios.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/RelatoriosPDF.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/fonts/font-awesome/css/font-awesome.min.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/fonts/elusive-icons-2.0.0/css/elusive-icons.min.css")" rel="stylesheet" />

    <script type="text/javascript" src="@Server.MapPath("~/Scripts/jquery-2.1.1.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/select2/select2.full.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/dataTables/datatables.min.js")" charset="utf-8"></script>

    <style>

        td {
            font-size: 12px;
        }

        tr {
            page-break-inside: avoid; 
        }

    </style>
</head>

@{
    string arquivo = "~/Logos/" + ViewBag._LogoConsultor;
    string logo = Server.MapPath(arquivo);
}

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="@logo" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.EventosUsuario</h4>
                        <p>@ViewBag.DataTextoAtualIni<br />@ViewBag.DataTextoAtualFim</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="panel">
                    <div>
                        <h4>Usuário:</h4>
                        @Html.DropDownList("Usuarios", new SelectList(ViewBag.listaUsuarios, "IDUsuario", "NomeUsuario"),
                                                            "Todos os Usuarios",
                                                            new { @class = "form-control" })

                        <br />

                        <h4>Filtros:</h4>
                        <select class="select2_tipo_2 form-control" multiple="multiple">

                            @{
                                var icone_tipo = "fa-th-list";
                                var tipo = 0;
                            
                                List<ListaTiposDominio> listaTiposEventos = ViewBag.listatiposEventos;

                                if (listaTiposEventos != null)
                                {
                                    foreach (ListaTiposDominio tipoEvento in listaTiposEventos)
                                    {
                                        tipo = tipoEvento.ID;
                                    
                                        switch (tipo)
                                        {
                                            default:
                                                continue;

                                            case 1:
                                                icone_tipo = "fa-sign-in";
                                                break;

                                            case 2:
                                                icone_tipo = "fa-gear";
                                                break;

                                            case 3:
                                                icone_tipo = "fa-wrench";
                                                break;
                                        }

                                        <option value=@tipo data-icon=@icone_tipo>&nbsp;&nbsp;@tipoEvento.Descricao</option>
                                    }
                                }
                            }

                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-default">
                    <table id="dataTables-eventos" class="table no-margins dataTables-eventos">
                        <thead>
                            <tr>
                                <th style="padding:2px; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>
                                <th style="padding:2px; font-size:12px;">IDUsuario</th>
                                <th style="padding:2px; font-size:12px;">@SmartEnergy.Resources.ConfiguracaoTexts.Usuarios</th>
                                <th style="padding:2px; font-size:12px;">@SmartEnergy.Resources.ConfiguracaoTexts.TipoAcesso</th>
                                <th style="padding:2px; font-size:12px;">@SmartEnergy.Resources.ConfiguracaoTexts.Cliente</th>
                                <th style="padding:2px; font-size:12px;">@SmartEnergy.Resources.ConfiguracaoTexts.Consultor</th>
                                <th style="padding:2px; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Tipo</th>
                                <th style="text-align:center; padding:2px; font-size:12px;"><i class="fa fa-tags" style="font-size:12px;"></i></th>
                                <th style="padding:2px; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Evento</th>
                            </tr>
                        </thead>

                        <tbody>

                            @{
                                List<EventosUsuarioDescricao> eventos = ViewBag.listaEventosDescricao;

                                if (eventos != null)
                                {
                                    foreach (var evento in eventos)
                                    {
                                        icone_tipo = "fa-th-list";
                                        tipo = evento.Tipo;

                                        switch (tipo)
                                        {
                                            default:
                                                continue;

                                            case 1:
                                                icone_tipo = "fa-sign-in";
                                                break;

                                            case 2:
                                                icone_tipo = "fa-gear";
                                                break;

                                            case 3:
                                                icone_tipo = "fa-wrench";
                                                break;
                                        }

                                        <tr>
                                            <td><span style="display:none;">@evento.DataHora_Sort</span>@evento.DataHora</td>
                                            <td>@evento.IDUsuario</td>
                                            <td>@evento.NomeUsuario</td>
                                            <td>@evento.NomeTipoAcesso</td>
                                            <td>@evento.NomeCliente</td>
                                            <td>@evento.NomeGestor</td>
                                            <td>@evento.Tipo</td>
                                            <td style="text-align:center;"><i class="fa @icone_tipo" style="font-size:12px;"></i></td>
                                            <td>@evento.Descricao</td>
                                        </tr>
                                    }
                                }
                            }

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>


<script type="text/javascript">

    $(document).ready(function () {

        //
        // Tabela resultado eventos
        //

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {
                var x = (a == "-") ? 0 : a.replace(/,/, ".");
                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        var table = $('.dataTables-eventos').DataTable({
            "iDisplayLength": 10000,
            dom: 'ft',

            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "portugues", "bVisible": true, "bSortable": true, "bSearchable": true },
                        { "aTargets": [1], "sType": "numero", "bVisible": false, "bSortable": true, "bSearchable": true },
                        { "aTargets": [2], "sType": "portugues", "bVisible": true, "bSortable": true, "bSearchable": true },
                        { "aTargets": [3], "sType": "portugues", "bVisible": true, "bSortable": true, "bSearchable": true },
                        { "aTargets": [4], "sType": "portugues", "bVisible": true, "bSortable": true, "bSearchable": true },
                        { "aTargets": [5], "sType": "portugues", "bVisible": true, "bSortable": true, "bSearchable": true },
                        { "aTargets": [6], "sType": "numero", "bVisible": false, "bSortable": true, "bSearchable": true },
                        { "aTargets": [7], "sType": "portugues", "bVisible": true, "bSortable": false, "bSearchable": false },
                        { "aTargets": [8], "sType": "portugues", "bVisible": true, "bSortable": true, "bSearchable": true },
            ],
            "oSearch": {
                "bSmart": false,
                "bRegex": true,
                "sSearch": ""
            },
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "10%" },
            { sWidth: "1%" },
            { sWidth: "15%" },
            { sWidth: "10%" },
            { sWidth: "15%" },
            { sWidth: "15%" },
            { sWidth: "1%" },
            { sWidth: "5%" },
            { sWidth: "35%" }
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

        // cookie de search
        var search = decodeURIComponent( @Html.Raw(Json.Encode(@ViewBag.Relat_Search)) );
        $('.dataTables-eventos').DataTable().search(search).draw();

        // cookie de sort
        var sortedCol = Math.ceil(@ViewBag.Relat_SortedCol);
        if (sortedCol == "") sortedCol = 0;
        var sortedDir = @Html.Raw(Json.Encode(@ViewBag.Relat_SortedDir));

        if (sortedCol == 0 || sortedCol == 2 || sortedCol == 3 || sortedCol == 4 || sortedCol == 5 || sortedCol == 8) {
            $('#dataTables-eventos').dataTable().fnSort([[sortedCol, sortedDir]]);
        }


        function formatIcon(icon) {

            if (!icon.id) {
                return icon.text;
            }
            var originalOption = icon.element;
            var $icon = $('  <i style="font-size:16px;" class="fa ' + $(originalOption).data('icon') + '"></i><span>' + icon.text + '</span>');

            return $icon;

        };


        //
        // Usuarios
        //

        // cookie de filtro
        var IDUsuario_selecionado = @Html.Raw(Json.Encode(@ViewBag.Relat_IDUsuario));

        // procura na tabela
        $('.dataTables-eventos').DataTable().column(1).search(IDUsuario_selecionado, true, false).draw();

        document.getElementById('Usuarios').value = IDUsuario_selecionado;


        //
        // Filtro eventos
        //

        $(".select2_tipo").select2({
            minimumResultsForSearch: Infinity,
            width: "100%",
            templateResult: formatIcon,
            templateSelection: formatIcon
        });

        var placeholder = "";

        $(".select2_tipo_2").select2({
            placeholder: placeholder,
            minimumResultsForSearch: Infinity,
            width: "100%",
            templateResult: formatIcon,
            templateSelection: formatIcon,
            allowClear: true,
            closeOnSelect: true,
            escapeMarkup: function (m) {
                return m;
            }
        });

        // cookie de filtro
        var lista_selecao = @Html.Raw(Json.Encode(@ViewBag.Relat_TipoEvento));

        // troco / por |
        var str = lista_selecao.replace(/[\/\/]/g, "|");

        // setar selecao na lista
        var lista = lista_selecao.split("/");

        // procura na tabela
        $('.dataTables-eventos').DataTable().column(3).search(str, true, false).draw();

        // aplica filtros
        $(".select2_tipo_2").select2("val", lista);
    });

    function getCookie(c_name) {

        // Get name followed by anything except a semicolon
        var cookiestring = RegExp("" + c_name + "[^;]+").exec(document.cookie);

        // Return everything after the equal sign, or an empty string if the cookie name not found
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./, "") : "");
    }

</script>
