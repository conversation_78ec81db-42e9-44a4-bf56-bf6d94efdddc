﻿
<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.Meteorologia</title>

    <link href="@Server.MapPath("~/Content/bootstrap.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/style.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/plugins/c3/c3.min.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/Relatorios.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/RelatoriosPDF.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/fonts/font-awesome/css/font-awesome.min.css")" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="@Server.MapPath("~/Scripts/jquery-2.1.1.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/d3/d3.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/c3/c3.min.js")"></script>

</head>

<style>
    th {
        font-size: 11px;
    }

    td {
        font-size: 11px;
    }

    tr {
        padding: 1px;
        page-break-inside: avoid;
    }
</style>

@{
    string arquivo = "~/Logos/" + ViewBag._LogoConsultor;
    string logo = Server.MapPath(arquivo);
}

@if (ViewBag.Retorno == 2)
{
    <div class="row">
        <div class="col-lg-12">
            <div class="linha cabecalho">
                <div class="coluna1">
                    <p><img src="@logo" style="height:42px; width:auto;" /></p>
                </div>

                <div class="coluna2">
                    <h3>@ViewBag.ClienteNome</h3>
                    @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                </div>

                <div class="coluna3">
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.Meteorologia - @SmartEnergy.Resources.RelatoriosTexts.RelatDiario</h4>
                    <p>@ViewBag.DataAtual</p>
                </div>
            </div>
        </div>
    </div>

    <br /><br /><br /><br /><br /><br />
    <div class="row">
        <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
            <i class="fa fa-warning fa-5x"></i>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>@SmartEnergy.Resources.RelatoriosTexts.NaoExistemRegistros</h2>
        </div>
    </div>
    <br /><br /><br /><br />

}
else
{

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="@logo" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.Meteorologia - @SmartEnergy.Resources.RelatoriosTexts.RelatDiario</h4>
                        <p>@ViewBag.DataAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <br />

        @{
            if (ViewBag.showGrafico)
            {
                <div class="row">
                    <div class="relat-grafico" style="height:450px; width:800px; margin-left:50px;">
                        <div class="postlet-grafico-ea" id="grafico-ea" style="margin-top: -10px"></div>
                        <div class='chart-legend' style="display:none;">
                            <div class='legend-scale-dem'>
                                <ul class='legend-labels' style="margin-top: -25px;">
                                    <li><span style='background:#1C84C6 !important;'></span>@ViewBag.NomeGrandeza</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }

        <hr />

        <div class="row">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-ea" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.MediaDia</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Maxima</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Minima</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@ViewBag.NomeGrandeza</td>
                                <td class="relat-ea">@ViewBag.VMedDia @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.DataAtual)</small></td>
                                <td class="relat-preto">@ViewBag.VMaxDia @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMaxDia_DataHora)</small></td>
                                <td class="relat-preto">@ViewBag.VMinDia @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMinDia_DataHora)</small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    @{
        if (ViewBag.showTabela)
        {
            <div class="page-break"></div>

            <div class="row">

                <div class="col-lg-12">
                    <div class="panel panel-default">
                        <table class="table no-margins tabela-grafico-pdf">
                        <thead>
                            <tr>
                                <th style="width:15%;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>
                                <th style="width:15%;">@ViewBag.NomeGrandeza</th>
                                <th style="width:15%;">@SmartEnergy.Resources.RelatoriosTexts.Umidade</th>
                                <th style="width:15%;">@SmartEnergy.Resources.RelatoriosTexts.Pressao</th>
                                <th style="width:20%;">@SmartEnergy.Resources.RelatoriosTexts.Vento</th>
                                <th style="width:20%;">@SmartEnergy.Resources.RelatoriosTexts.Clima</th>
                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var i = 0;
                                var j = 0;
                                var temp = "---";
                                var umidade = "---";
                                var pressao = "---";
                                var vel_vento = "---";
                                var dir_vento = "---";                                
                                var clima = "---";
                                string[] nome_Direcao = { SmartEnergy.Resources.RelatoriosTexts.Norte,
                                                          SmartEnergy.Resources.RelatoriosTexts.Norte_Nordeste,
                                                          SmartEnergy.Resources.RelatoriosTexts.Nordeste,
                                                          SmartEnergy.Resources.RelatoriosTexts.Este_Nordeste,
                                                          SmartEnergy.Resources.RelatoriosTexts.Leste,
                                                          SmartEnergy.Resources.RelatoriosTexts.Este_Sudeste,
                                                          SmartEnergy.Resources.RelatoriosTexts.Sudeste,
                                                          SmartEnergy.Resources.RelatoriosTexts.Sul_Sudeste,
                                                          SmartEnergy.Resources.RelatoriosTexts.Sul,
                                                          SmartEnergy.Resources.RelatoriosTexts.Sul_Sudoeste,
                                                          SmartEnergy.Resources.RelatoriosTexts.Sudoeste,
                                                          SmartEnergy.Resources.RelatoriosTexts.Oeste_Sudoeste,
                                                          SmartEnergy.Resources.RelatoriosTexts.Oeste,
                                                          SmartEnergy.Resources.RelatoriosTexts.Oeste_Noroeste,
                                                          SmartEnergy.Resources.RelatoriosTexts.Noroeste,
                                                          SmartEnergy.Resources.RelatoriosTexts.Norte_Noroeste };
                               
                                string[] nome_Clima = { SmartEnergy.Resources.RelatoriosTexts.Ceu_Claro,
                                                        SmartEnergy.Resources.RelatoriosTexts.Noite_Clara,
                                                        SmartEnergy.Resources.RelatoriosTexts.Parc_Nublado,
                                                        SmartEnergy.Resources.RelatoriosTexts.Parc_Nublado,
                                                        SmartEnergy.Resources.RelatoriosTexts.Nublado,
                                                        SmartEnergy.Resources.RelatoriosTexts.Chuvoso,
                                                        SmartEnergy.Resources.RelatoriosTexts.Granizo,
                                                        SmartEnergy.Resources.RelatoriosTexts.Neve,
                                                        SmartEnergy.Resources.RelatoriosTexts.Vento,
                                                        SmartEnergy.Resources.RelatoriosTexts.Nevoeiro };

                                for (i = 0; i < 24; i++)
                                {
                                    j = i + 1;

                                    if (ViewBag.Tempo_Cod[j] >= 0)
                                    {
                                        temp = string.Format("{0:0} {1}", ViewBag.Temp[j], ViewBag.UnidadeGrandeza);
                                        umidade = string.Format("{0:0} %", ViewBag.Umidade[j]);
                                        pressao = string.Format("{0:0} hPa", ViewBag.Pressao[j]);
                                        vel_vento = string.Format("{0:0} km/h", ViewBag.Vento_Velocidade[j]);
                                        dir_vento = string.Format("[{0}]", nome_Direcao[ViewBag.Vento_Direcao[j]]);
                                        clima = nome_Clima[ViewBag.Tempo_Cod[j]];
                                    }
                                    else
                                    {
                                        temp = "---";
                                        umidade = "---";
                                        pressao = "---";
                                        vel_vento = "---";
                                        dir_vento = "";
                                        clima = "---";
                                    }
                                    
                                                                        
                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="relat-preto">@ViewBag.Horas[j]</td>
                                        <td style="text-align:center;" class="relat-ea">@temp</td>
                                        <td style="text-align:center;" class="relat-ea">@umidade</td>
                                        <td style="text-align:center;" class="relat-ea">@pressao</td>
                                        <td style="text-align:center;" class="relat-ea">@vel_vento<br />@dir_vento</td>
                                        <td style="text-align:center;" class="relat-ea">@clima</td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                    </div>
                </div>
            </div>
        }
    }

    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>

<script type="text/javascript">

    $(document).ready(function () {

        // meteorologia
        var dataX = [];
        var labelX = [];
        var dataTemp = [];

        // copia valores
        var Temp = @Html.Raw(Json.Encode(@ViewBag.Temp));
        var Dias = @Html.Raw(Json.Encode(@ViewBag.Dias));
        var NomeGrandeza = @Html.Raw(Json.Encode(@ViewBag.NomeGrandeza));
        var UnidadeGrandeza = @Html.Raw(Json.Encode(@ViewBag.UnidadeGrandeza));

        var minimoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMin));
        var maximoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMax));

        var diferenca = maximoValor - minimoValor;

        dataX.push('x');
        dataTemp.push(['Score']);

        for (i = 0; i < 26; i++)
        {
            // X
            dataX.push(Dias[i]);

            dataTemp.push([Temp[i]]);
        }

        // valores label X
        labelX.push(Dias[1]);
        for (i = 3; i <= 24; i+=3)
        {
            labelX.push(Dias[i]);
        }

        var Data = [dataX,dataTemp];

        c3.generate({
            bindto: '#grafico-ea',
            size: {
                height: 300
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    Score: 'bar',
                },
                colors: {
                    Score: 'rgb(120, 178, 235)',
                }
            },
            point: {
                r:2,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*28
                },
                y:  {
                    min: minimoValor,
                    max: maximoValor,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( diferenca < 10)
                            {
                                return d.toFixed(2);
                            }

                            if( diferenca < 100)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.25 },
            },           
        });

        
        d3.selection.prototype.moveToFront = function () {
            return this.each(function () {
                this.parentNode.appendChild(this);
            });
        };


        d3.select("svg g.c3-grid").moveToFront();

        // labels
        $('.chart-legend').css("display", "block");

    });

</script>

}
    