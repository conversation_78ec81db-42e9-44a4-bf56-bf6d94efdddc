﻿<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.DemandaReativa</title>

    <link href="@Server.MapPath("~/Content/bootstrap.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/style.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/plugins/c3/c3.min.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/Relatorios.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/RelatoriosPDF.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/fonts/font-awesome/css/font-awesome.min.css")" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="@Server.MapPath("~/Scripts/jquery-2.1.1.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/d3/d3.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/c3/c3.min.js")"></script>

</head>

@{
    string arquivo = "~/Logos/" + ViewBag._LogoConsultor;
    string logo = Server.MapPath(arquivo);
}

@if (ViewBag.Retorno == 2)
{
    <div class="row">
        <div class="col-lg-12">
            <div class="linha cabecalho">
                <div class="coluna1">
                    <p><img src="@logo" style="height:42px; width:auto;" /></p>
                </div>

                <div class="coluna2">
                    <h3>@ViewBag.ClienteNome</h3>
                    @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                </div>

                <div class="coluna3">
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.DemandaReativa - @SmartEnergy.Resources.RelatoriosTexts.RelatDiario (@SmartEnergy.Resources.RelatoriosTexts.Simulacao)</h4>
                    <p>@ViewBag.DataAtual</p>
                </div>
            </div>
        </div>
    </div>

    <br /><br /><br /><br /><br /><br />
    <div class="row">
        <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
            <i class="fa fa-warning fa-5x"></i>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>@SmartEnergy.Resources.RelatoriosTexts.NaoExistemRegistros</h2>
        </div>
    </div>
    <br /><br /><br /><br />

}
else
{

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="@logo" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.DemandaReativa - @SmartEnergy.Resources.RelatoriosTexts.RelatDiario (@SmartEnergy.Resources.RelatoriosTexts.Simulacao)</h4>
                        <p>@ViewBag.DataAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <br />

        @{
            if (ViewBag.showGrafico)
            {
                <div class="row">
                    <div class="relat-grafico" style="height:500px; width:800px; margin-left:50px;">
                        <div id="demanda-chart"></div>
                        <div id="fatpot-chart"></div>
                        <div class='chart-legend' style="display:none;">
                            <div class='legend-scale-dem'>
                                <ul class='legend-labels' style="margin-top: -25px;">
                                    <li><span style='background:#1C84C6 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                                    <li><span style='background:#007f00 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                                    <li><span style='background:#ff0000 !important;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                                    <li><span style='background:#00b1ff;'></span>@SmartEnergy.Resources.RelatoriosTexts.Simulacao</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }

        <br /><br /><br />

        <hr />

        <div class="row" style="margin-top: -15px;">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMaxima</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MaxFPC_Sim @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MaxFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MaxFPI_Sim @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MaxFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MaxP_Sim @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MaxP_DataHora_Sim)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMedia</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MedFPC_Sim @ViewBag.UnidadeReativo<br /><small>&nbsp;</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MedFPI_Sim @ViewBag.UnidadeReativo<br /><small>&nbsp;</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MedP_Sim @ViewBag.UnidadeReativo</td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMinima</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MinFPC_Sim @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MinFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MinFPI_Sim @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MinFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MinP_Sim @ViewBag.UnidadeReativo<br /><small>(@ViewBag.Dem_MinP_DataHora_Sim)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisCapacitivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisCapFPC_Sim<br /><small>(@ViewBag.FatPot_MaisCapFPC_DataHora_Sim)</small></td>
                                <td class="relat-indutivo">@ViewBag.FatPot_MaisCapFPI_Sim<br /><small>(@ViewBag.FatPot_MaisCapFPI_DataHora_Sim)</small></td>
                                <td class="relat-indutivo">@ViewBag.FatPot_MaisCapP_Sim<br /><small>(@ViewBag.FatPot_MaisCapP_DataHora_Sim)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia<br />@SmartEnergy.Resources.SupervisaoTexts.MaisIndutivo</td>
                                <td class="relat-capacitivo">@ViewBag.FatPot_MaisIndFPC_Sim<br /><small>(@ViewBag.FatPot_MaisIndFPC_DataHora_Sim)</small></td>
                                <td class="relat-indutivo">@ViewBag.FatPot_MaisIndFPI_Sim<br /><small>(@ViewBag.FatPot_MaisIndFPI_DataHora_Sim)</small></td>
                                <td class="relat-indutivo">@ViewBag.FatPot_MaisIndP_Sim<br /><small>(@ViewBag.FatPot_MaisIndP_DataHora_Sim)</small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    @{
        if (ViewBag.showTabela)
        {
            <div class="page-break"></div>
        
            <div class="row">

                <br /><br />

                @{
                    var pagina = 0;

                    for (pagina = 0; pagina < 2; pagina++)
                    {
                        <div style="width:43%; float: left !important; margin-left:40px !important;" class="panel panel-default">
                            <table class="table no-margins tabela-grafico-pdf">
                                <thead>
                                    <tr>
                                        <th style="width:23%; text-align:center; padding:2px; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>
                                        <th style="width:5%; text-align:center; padding:2px; font-size:12px;">&nbsp;</th>
                                        <th style="width:24%; text-align:center; padding:2px; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Ativa<br />(@ViewBag.UnidadeDemanda)</th>
                                        <th style="width:24%; text-align:center; padding:2px; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Reativa<br />(@ViewBag.UnidadeReativo)</th>
                                        <th style="width:24%; text-align:center; padding:2px; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.FatPot</th>
                                    </tr>
                                </thead>

                                <tbody>

                                    @{
                                        var i = 0;
                                        var j = 0;
                                        var classe_sim = "relat-semreg";
                                        var periodo_sim = "---";
                                        var demandaAtv_sim = "---";
                                        var demandaRtv_sim = "---";
                                        var fatpot_sim = "---";

                                        for (i = 0; i < 48; i++)
                                        {
                                            j = i + 1 + (pagina * 48);

                                            switch ((int)ViewBag.Periodo[j])
                                            {
                                                case 0:
                                                    classe_sim = "relat-ponta";
                                                    periodo_sim = "P";
                                                    break;

                                                case 1:
                                                    classe_sim = "relat-indutivo";
                                                    periodo_sim = "FPI";
                                                    break;

                                                case 2:
                                                    classe_sim = "relat-capacitivo";
                                                    periodo_sim = "FPC";
                                                    break;

                                                default:
                                                case 3:
                                                    classe_sim = "relat-semreg";
                                                    periodo_sim = "---";
                                                    break;
                                            }

                                            if (ViewBag.Periodo[j] == 3)
                                            {
                                                demandaAtv_sim = "---";
                                                demandaRtv_sim = "---";
                                                fatpot_sim = "---";
                                            }
                                            else
                                            {
                                                demandaAtv_sim = string.Format("{0:#,##0.0}", ViewBag.DemandaAtv_Sim[j]);
                                                demandaRtv_sim = string.Format("{0:#,##0.0}", ViewBag.DemandaRtv_Sim[j]);
                                                fatpot_sim = string.Format("{0:0.000}", ViewBag.FatPot_Sim[j]);
                                            }

                                            <tr class="tabela_valores" style="font-size:12px;">
                                                <td style="text-align:center; padding:2px; font-size:12px;" class="@classe_sim">@ViewBag.Horas[j]</td>
                                                <td style="text-align:center; padding:2px; font-size:12px;" class="@classe_sim">@periodo_sim</td>
                                                <td style="text-align:center; padding:2px; font-size:12px;" class="@classe_sim">@demandaAtv_sim</td>
                                                <td style="text-align:center; padding:2px; font-size:12px;" class="@classe_sim">@demandaRtv_sim</td>
                                                <td style="text-align:center; padding:2px; font-size:12px;" class="@classe_sim">@fatpot_sim</td>
                                            </tr>
                                        }
                                    }

                                </tbody>
                            </table>

                        </div>
                    }
                }

            </div>
        }
    }

    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>

<script type="text/javascript">

    $(document).ready(function () {

        // demanda
        var dataX = [];
        var labelX = [];
        var dataDemandaRtv = [];
        var dataDemMaxFPC = [];
        var dataDemMaxFPI = [];
        var dataDemMaxP = [];
        var dataDemZero = [];

        // demanda simulacao
        var dataDemandaRtv_Sim = [];

        // copia valores
        var DemandaAtv = @Html.Raw(Json.Encode(@ViewBag.DemandaAtv));
        var DemandaRtv = @Html.Raw(Json.Encode(@ViewBag.DemandaRtv));
        var FatPot = @Html.Raw(Json.Encode(@ViewBag.FatPot));
        var Periodo = @Html.Raw(Json.Encode(@ViewBag.Periodo));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));

        // copia valores simulacao
        var DemandaAtv_Sim = @Html.Raw(Json.Encode(@ViewBag.DemandaAtv_Sim));
        var DemandaRtv_Sim = @Html.Raw(Json.Encode(@ViewBag.DemandaRtv_Sim));
        var FatPot_Sim = @Html.Raw(Json.Encode(@ViewBag.FatPot_Sim));

        var maximoDem = @Html.Raw(Json.Encode(@ViewBag.DemMaxGrafico));

        var UnidadeDemanda = @Html.Raw(Json.Encode(@ViewBag.UnidadeDemanda));
        var UnidadeReativo = @Html.Raw(Json.Encode(@ViewBag.UnidadeReativo));

        dataX.push('x');
        dataDemandaRtv.push(['data1']);
        dataDemMaxFPC.push(['data2']);
        dataDemMaxFPI.push(['data3']);
        dataDemMaxP.push(['data4']);
        dataDemZero.push(['data5']);

        // simulacao
        dataDemandaRtv_Sim.push(['data6']);

        for (i = 0; i < 98; i++)
        {
            // X
            dataX.push(Datas[i]);

            // linha de demanda zero
            dataDemZero.push([0.0]);

            dataDemandaRtv.push([DemandaRtv[i]]);

            // simulacao
            dataDemandaRtv_Sim.push([DemandaRtv_Sim[i]]);

            if( Periodo[i] == 0 )
            {
                dataDemMaxP.push([maximoDem]);
                dataDemMaxFPI.push([0]);
                dataDemMaxFPC.push([0]);
            }
            else if( Periodo[i] == 1 )
            {
                dataDemMaxP.push([0]);
                dataDemMaxFPI.push([maximoDem]);
                dataDemMaxFPC.push([0]);
            }
            else if( Periodo[i] == 2 )
            {
                dataDemMaxP.push([0]);
                dataDemMaxFPI.push([0]);
                dataDemMaxFPC.push([maximoDem]);
            }
            else
            {
                dataDemMaxP.push([0]);
                dataDemMaxFPI.push([0]);
                dataDemMaxFPC.push([0]);
            }

        }

        // valores label X
        labelX.push(Datas[1]);
        for (i = 12; i <= 96; i+=12)
        {
            labelX.push(Datas[i]);
        }

        var Data = [dataX,dataDemandaRtv,dataDemMaxFPC,dataDemMaxFPI,dataDemMaxP,dataDemZero,dataDemandaRtv_Sim];

        var chart = c3.generate({

            bindto: '#demanda-chart',
            size: {
                height: 320
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 20,
                left: 50
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'area-step',
                    data2: 'area-step',
                    data3: 'area-step',
                    data4: 'area-step',
                    data5: 'step',
                    data6: 'step'
                },
                color: function (color, d) {
                    if (typeof d.index === 'undefined') { return color; }

                    if( Periodo[d.index] == 0 )
                        return '#FF0000';

                    if( Periodo[d.index] == 1 )
                        return '#007F00';

                    if( Periodo[d.index] == 2 )
                        return '#1C84C6';

                    return '#007F00';
                },
                colors: {
                    data1: '#001F00',
                    data2: '#1C84C6',
                    data3: '#007F00',
                    data4: '#FF0000',
                    data5: '#000000',
                    data6: '#00b1ff'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x: {
                    show:false,
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            return "";
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*7
                },
                y:  {
                    max: maximoDem,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoDem < 100)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.7 },
            },
        });

        // fator de potencia
        var dataReferenciaFPC = [];
        var dataReferenciaFPI = [];
        var dataReferenciaP = [];
        var dataFatPot = [];
        var dataFatPotUm = [];

        // fator de potencia simulacao
        var dataFatPot_Sim = [];

        // copia valores
        var FatPotMin = @Html.Raw(Json.Encode(@ViewBag.FatPotMin));

        dataFatPot.push(['data1']);
        dataReferenciaFPC.push(['data2']);
        dataReferenciaFPI.push(['data3']);
        dataReferenciaP.push(['data4']);
        dataFatPotUm.push(['data5']);

        // simulacao
        dataFatPot_Sim.push(['data6']);

        for (i = 0; i < 98; i++)
        {
            // linha de fatpot um
            dataFatPotUm.push([0.0]);

            // converte para nova escala
            if( FatPot[i] < 0 )
            {
                dataFatPot.push([(1.0 - (-1.0*FatPot[i])) * -1.0]);
            }
            else
            {
                dataFatPot.push([(1.0 - FatPot[i])]);
            }

            // converte para nova escala simulacao
            if( FatPot_Sim[i] < 0 )
            {
                dataFatPot_Sim.push([(1.0 - (-1.0*FatPot_Sim[i])) * -1.0]);
            }
            else
            {
                dataFatPot_Sim.push([(1.0 - FatPot_Sim[i])]);
            }

            if( Periodo[i] == 0 )
            {
                dataReferenciaFPC.push([0]);
                dataReferenciaFPI.push([0]);
                dataReferenciaP.push([0.08]);
            }
            else if( Periodo[i] == 1 )
            {
                dataReferenciaFPC.push([0]);
                dataReferenciaFPI.push([0.08]);
                dataReferenciaP.push([0]);
            }
            else if( Periodo[i] == 2 )
            {
                dataReferenciaFPC.push([-0.08]);
                dataReferenciaFPI.push([0]);
                dataReferenciaP.push([0]);
            }
            else
            {
                dataReferenciaFPC.push([0]);
                dataReferenciaFPI.push([0]);
                dataReferenciaP.push([0]);
            }
        }

        // prepara escala eixo Y
        var maximo = (1.0 - FatPotMin);
        var minimo = -1.0 * maximo;

        var Data = [dataX,dataFatPot,dataReferenciaFPC,dataReferenciaFPI,dataReferenciaP,dataFatPotUm,dataFatPot_Sim];

        var chart_2 = c3.generate({
            bindto: '#fatpot-chart',
            size: {
                height: 220
            },
            padding: {
                top: 0,
                right: 10,
                bottom: 30,
                left: 50
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'area-step',
                    data2: 'area-step',
                    data3: 'area-step',
                    data4: 'area-step',
                    data5: 'step',
                    data6: 'step'
                },
                color: function (color, d) {
                    if (typeof d.index === 'undefined') { return color; }

                    if( Periodo[d.index] == 0 )
                        return '#FF0000';

                    if( Periodo[d.index] == 1 )
                        return '#007F00';

                    if( Periodo[d.index] == 2 )
                        return '#1C84C6';

                    return '#007F00';
                },
                colors: {
                    data1: '#001F00',
                    data2: '#1C84C6',
                    data3: '#007F00',
                    data4: '#FF0000',
                    data5: '#000000',
                    data6: '#00b1ff'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x: {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*7
                },
                y:  {
                    max: maximo,
                    min: minimo,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            var valor;

                            // converte para nova escala
                            if( d < 0 )
                            {
                                valor = (1.0 - (-1.0*d)) * -1.0;
                            }
                            else
                            {
                                valor = (1.0 - d);
                            }

                            return valor.toFixed(3);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.7 },
            },
        });

        // labels
        $('.chart-legend').css("display", "block");

    });

</script>

}
