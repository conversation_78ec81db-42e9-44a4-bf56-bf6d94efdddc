﻿<style>

#fatpot-chart .c3-area {
    opacity:0.3;
}

</style>

<div class="row">
    <div class="col-lg-4 col-md-12 relatorio_real">
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.MaximoDia</h5>
                        <h1>@ViewBag.FatUtilizacao_MaxFPC <span>%</span></h1>
                        <h6>@ViewBag.FatUtilizacao_MaxFPC_DataHora</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.MaximoDia</h5>
                        <h1>@ViewBag.FatUtilizacao_MaxFPI <span>%</span></h1>
                        <h6>@ViewBag.FatUtilizacao_MaxFPI_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.MaximoDia</h5>
                        <h1>@ViewBag.FatUtilizacao_MaxP <span>%</span></h1>
                        <h6>@ViewBag.FatUtilizacao_MaxP_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-12 simulacao" style="display:none;">
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.MaximoDia</h5>
                        <h1>@ViewBag.FatUtilizacao_MaxFPC_Sim <span>%</span></h1>
                        <h6>@ViewBag.FatUtilizacao_MaxFPC_DataHora_Sim</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.MaximoDia</h5>
                        <h1>@ViewBag.FatUtilizacao_MaxFPI_Sim <span>%</span></h1>
                        <h6>@ViewBag.FatUtilizacao_MaxFPI_DataHora_Sim</h6>
                    </div>
                </div>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.MaximoDia</h5>
                        <h1>@ViewBag.FatUtilizacao_MaxP_Sim <span>%</span></h1>
                        <h6>@ViewBag.FatUtilizacao_MaxP_DataHora_Sim</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-8 col-md-12">
        <div class="row">
            <div class="relat-grafico">
                <div id="fatutilizacao-chart" style="margin-top: -10px"></div>
                <div class='chart-legend' style="display:none;">
                    <div class='legend-scale-dem'>
                        <ul class='legend-labels' style="margin-top: -25px;">
                            <li><span style='background:#1C84C6;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                            <li><span style='background:#007f00;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                            <li><span style='background:#ff0000;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                            <li class="simulacao"><span style='background:#00b1ff;'></span>@SmartEnergy.Resources.RelatoriosTexts.Simulacao</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@Html.Partial("_Relat_Botoes")

<br />
<div class="panel panel-title relatorio">
    <div class="panel-body">

        <div class="row">
            <div class="col-lg-12 relatorio_real">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao @SmartEnergy.Resources.RelatoriosTexts.Maximo</td>
                                <td class="relat-capacitivo">@ViewBag.FatUtilizacao_MaxFPC %<br /><small>(@ViewBag.FatUtilizacao_MaxFPC_DataHora)</small></td>
                                <td class="relat-fponta">@ViewBag.FatUtilizacao_MaxFPI %<br /><small>(@ViewBag.FatUtilizacao_MaxFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.FatUtilizacao_MaxP %<br /><small>(@ViewBag.FatUtilizacao_MaxP_DataHora)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao @SmartEnergy.Resources.RelatoriosTexts.Minimo</td>
                                <td class="relat-capacitivo">@ViewBag.FatUtilizacao_MinFPC %<br /><small>(@ViewBag.FatUtilizacao_MinFPC_DataHora)</small></td>
                                <td class="relat-fponta">@ViewBag.FatUtilizacao_MinFPI %<br /><small>(@ViewBag.FatUtilizacao_MinFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.FatUtilizacao_MinP %<br /><small>(@ViewBag.FatUtilizacao_MinP_DataHora)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao @SmartEnergy.Resources.RelatoriosTexts.Periodo</td>
                                <td class="relat-capacitivo">@ViewBag.FatUtilizacao_MedFPC %<br /><small>&nbsp;</small></td>
                                <td class="relat-fponta">@ViewBag.FatUtilizacao_MedFPI %<br /><small>&nbsp;</small></td>
                                <td class="relat-ponta">@ViewBag.FatUtilizacao_MedP %</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="col-lg-12 simulacao" style="display:none;">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao @SmartEnergy.Resources.RelatoriosTexts.Maximo</td>
                                <td class="relat-capacitivo">@ViewBag.FatUtilizacao_MaxFPC_Sim %<br /><small>(@ViewBag.FatUtilizacao_MaxFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.FatUtilizacao_MaxFPI_Sim %<br /><small>(@ViewBag.FatUtilizacao_MaxFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.FatUtilizacao_MaxP_Sim %<br /><small>(@ViewBag.FatUtilizacao_MaxP_DataHora_Sim)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao @SmartEnergy.Resources.RelatoriosTexts.Minimo</td>
                                <td class="relat-capacitivo">@ViewBag.FatUtilizacao_MinFPC_Sim %<br /><small>(@ViewBag.FatUtilizacao_MinFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.FatUtilizacao_MinFPI_Sim %<br /><small>(@ViewBag.FatUtilizacao_MinFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.FatUtilizacao_MinP_Sim %<br /><small>(@ViewBag.FatUtilizacao_MinP_DataHora_Sim)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao @SmartEnergy.Resources.RelatoriosTexts.Periodo</td>
                                <td class="relat-capacitivo">@ViewBag.FatUtilizacao_MedFPC_Sim %<br /><small>&nbsp;</small></td>
                                <td class="relat-fponta">@ViewBag.FatUtilizacao_MedFPI_Sim %<br /><small>&nbsp;</small></td>
                                <td class="relat-ponta">@ViewBag.FatUtilizacao_MedP_Sim %</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal inmodal animated fadeIn" id="modalTabela" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-editar-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">@SmartEnergy.Resources.ComumTexts.BotaoFechar</span></button>
                <h4 class="modal-title"><i class="fa fa-list"></i>&nbsp;&nbsp;@SmartEnergy.Resources.RelatoriosTexts.TabelaRegistros</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-default relatorio_real">
                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:40%;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>
                                <th style="width:30%;">@SmartEnergy.Resources.RelatoriosTexts.Periodo</th>
                                <th style="width:30%;">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao<br />(%)</th>
                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var i = 0;
                                var j = 0;
                                var classe = "relat-semreg";
                                var periodo = "---";
                                var fatUtilizacao = "---";

                                for (i = 0; i < 96; i++)
                                {
                                    j = i + 1;

                                    switch ((int)ViewBag.Periodo[j])
                                    {
                                        case 0:
                                            classe = "relat-ponta";
                                            periodo = SmartEnergy.Resources.SupervisaoTexts.Ponta;
                                            break;

                                        case 1:
                                            classe = "relat-indutivo";
                                            periodo = SmartEnergy.Resources.SupervisaoTexts.FPontaInd;
                                            break;

                                        case 2:
                                            classe = "relat-capacitivo";
                                            periodo = SmartEnergy.Resources.SupervisaoTexts.FPontaCap;
                                            break;

                                        default:                                    
                                        case 3:
                                            classe = "relat-semreg";
                                            periodo = "---";
                                            break;
                                    }

                                    if (ViewBag.Periodo[j] == 3)
                                    {
                                        fatUtilizacao = "---";
                                    }
                                    else
                                    {
                                        fatUtilizacao = string.Format("{0:#,##0.0}", ViewBag.FatUtilizacao[j]);
                                    }

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="@classe">@ViewBag.Horas[j]</td>
                                        <td style="text-align:center;" class="@classe">@periodo</td>
                                        <td style="text-align:center;" class="@classe">@fatUtilizacao</td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>

                <div class="panel panel-default simulacao" style="display:none;">
                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:40%;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>
                                <th style="width:30%;">@SmartEnergy.Resources.RelatoriosTexts.Periodo</th>
                                <th style="width:30%;">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao<br />(%)</th>
                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var classe_sim = "relat-semreg";
                                var periodo_sim = "---";
                                var fatUtilizacao_sim = "---";

                                for (i = 0; i < 96; i++)
                                {
                                    j = i + 1;

                                    switch ((int)ViewBag.Periodo[j])
                                    {
                                        case 0:
                                            classe_sim = "relat-ponta";
                                            periodo_sim = SmartEnergy.Resources.SupervisaoTexts.Ponta;
                                            break;

                                        case 1:
                                            classe_sim = "relat-indutivo";
                                            periodo_sim = SmartEnergy.Resources.SupervisaoTexts.FPontaInd;
                                            break;

                                        case 2:
                                            classe_sim = "relat-capacitivo";
                                            periodo_sim = SmartEnergy.Resources.SupervisaoTexts.FPontaCap;
                                            break;

                                        default:
                                        case 3:
                                            classe_sim = "relat-semreg";
                                            periodo_sim = "---";
                                            break;
                                    }

                                    if (ViewBag.Periodo[j] == 3)
                                    {
                                        fatUtilizacao_sim = "---";
                                    }
                                    else
                                    {
                                        fatUtilizacao_sim = string.Format("{0:#,##0.0}", ViewBag.FatUtilizacao_Sim[j]);
                                    }

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="@classe_sim">@ViewBag.Horas[j]</td>
                                        <td style="text-align:center;" class="@classe_sim">@periodo_sim</td>
                                        <td style="text-align:center;" class="@classe_sim">@fatUtilizacao_sim</td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
            </div>
        </div>
    </div>
</div>


@Html.Partial("_EventosModal")

@Html.Partial("_ObservacoesModal")
    

<script type="text/javascript">

    $(document).ready(function () {

        // fator de utilizacao
        var dataX = [];
        var labelX = [];
        var dataFatUtilizacao = [];
        var dataP = [];
        var dataFPI = [];
        var dataFPC = [];

        // simulacao
        var dataFatUtilizacao_Sim = [];

        // copia valores
        var FatUtilizacao = @Html.Raw(Json.Encode(@ViewBag.FatUtilizacao));
        var Demanda = @Html.Raw(Json.Encode(@ViewBag.DemandaAtv));
        var Contrato = @Html.Raw(Json.Encode(@ViewBag.Contrato));
        var Periodo = @Html.Raw(Json.Encode(@ViewBag.Periodo));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));

        // copia valores simulacao
        var FatUtilizacao_Sim = @Html.Raw(Json.Encode(@ViewBag.FatUtilizacao_Sim));
        var Demanda_Sim = @Html.Raw(Json.Encode(@ViewBag.DemandaAtv_Sim));
        var Contrato_Sim = @Html.Raw(Json.Encode(@ViewBag.Contrato_Sim));

        var maximoFatUtilizacao = @Html.Raw(Json.Encode(@ViewBag.FatUtilizacaoMaxGrafico));

        // verifico se nao tem dados e forco um limite
        if( maximoFatUtilizacao == 0.0)
        {
            maximoFatUtilizacao = 100.0;
        }

        dataX.push('x');
        dataFatUtilizacao.push(['data1']);
        dataP.push(['data2']);
        dataFPI.push(['data3']);
        dataFPC.push(['data4']);

        // simulacao
        dataFatUtilizacao_Sim.push(['data5']);

        for (i = 0; i < 98; i++)
        {
            // X
            dataX.push(Datas[i]);

            dataFatUtilizacao.push([FatUtilizacao[i]]);

            // simulacao
            dataFatUtilizacao_Sim.push([FatUtilizacao_Sim[i]]);

            if( Periodo[i] == 0 )
            {
                dataP.push([maximoFatUtilizacao]);
                dataFPI.push([0]);
                dataFPC.push([0]);
            }
            else if( Periodo[i] == 1 )
            {
                dataP.push([0]);
                dataFPC.push([0]);
                dataFPI.push([maximoFatUtilizacao]);
            }
            else if( Periodo[i] == 2 )
            {
                dataP.push([0]);
                dataFPI.push([0]);
                dataFPC.push([maximoFatUtilizacao]);
            }
            else
            {
                dataP.push([0]);
                dataFPI.push([0]);
                dataFPC.push([0]);
            }
        }

        // valores label X
        labelX.push(Datas[1]);
        for (i = 12; i <= 96; i+=12)
        {
            labelX.push(Datas[i]);
        }

        var Data = [dataX,dataFatUtilizacao,dataP,dataFPI,dataFPC,dataFatUtilizacao_Sim];

        var chart = c3.generate({

            bindto: '#fatutilizacao-chart',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'area-step',
                    data2: 'area-step',
                    data3: 'area-step',
                    data4: 'area-step',
                    data5: 'step'
                },
                color: function (color, d) {
                    if (typeof d.index === 'undefined') { return color; }

                    if( Periodo[d.index] == 0 )
                        return '#FF0000';

                    if( Periodo[d.index] == 1 )
                        return '#007F00';

                    if( Periodo[d.index] == 2 )
                        return '#1C84C6';

                    return '#007F00';
                },
                colors: {
                    data1: '#001F00',
                    data2: '#FF0000',
                    data3: '#007F00',
                    data4: '#1C84C6',
                    data5: '#00b1ff'
                },
                onclick: function (d, element) {
                    alert(d.value);
                }
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x: {
                    show:false,
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            return "";
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*6
                },
                y:  {
                    max: maximoFatUtilizacao,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoFatUtilizacao < 10)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.7 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, unit, periodo, bgcolor;

                    // checkbox aplica simulacao
                    var simula = $('#aplica_simula').prop('checked');
                    var tooltip_index = 4;
                    if (simula == true)
                    {
                        tooltip_index = 7;
                    }

                    for (i = 0; i < tooltip_index; i++) {

                        // titulo
                        if (! text) {

                            // split string and create array.
                            var arr = Datas[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // horas e minutos
                            var hours = data.getHours();
                            var minutes = data.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            title = hours + ':' + minutes;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // periodo
                        switch(Periodo[d[0].index])
                        {
                            case 0:
                                periodo = PONTA;
                                bgcolor = '#FF0000'
                                break;

                            case 1:
                                periodo = FPONTA_IND;
                                bgcolor = '#007F00';
                                break;

                            case 2:
                                periodo = FPONTA_CAP;
                                bgcolor = '#1C84C6';
                                break;

                            default:
                                periodo = "---";
                                bgcolor = '#000000';
                                break;
                        }

                        // nome
                        switch(i)
                        {
                            case 0:
                                name = FATOR_UTILIZACAO;
                                unit = '%';
                                value = FatUtilizacao[d[0].index].toFixed(1);
                                break;

                            case 1:
                                name = DEMANDA_ATIVA;
                                unit = 'kW';
                                value = Demanda[d[0].index].toFixed(1);
                                break;

                            case 2:
                                name = CONTRATO;
                                unit = 'kW';
                                value = Contrato[d[0].index].toFixed(0);
                                break;

                            case 3:
                                name = PERIODO;
                                unit = '';
                                value = periodo;
                                break;

                            case 4:
                                name = FATOR_UTILIZACAO;
                                unit = '%';
                                value = FatUtilizacao_Sim[d[0].index].toFixed(1);
                                break;

                            case 5:
                                name = DEMANDA_ATIVA;
                                unit = 'kW';
                                value = Demanda_Sim[d[0].index].toFixed(1);
                                break;

                            case 6:
                                name = CONTRATO;
                                unit = 'kW';
                                value = Contrato_Sim[d[0].index].toFixed(0);
                                break;
                        }

                        value = value.replace('.', ',');

                        // verifica se sem registro
                        if( Periodo[d[0].index] == 3 )
                        {
                            value = '---';
                        }

                        if(i==3)
                        {
                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                            text += "<td class='name'>" + name + "</td>";
                            text += "<td class='value'>" + value + "</td>";
                            text += "</tr>";
                        }
                        else
                        {
                            if (i > 3)
                            {
                                name += " - " + SIMULACAO;
                                bgcolor = "#00b1ff"
                            }

                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                            text += "<td class='value'>" + value + " " + unit + "</td>";
                            text += "</tr>";
                        }
                    }

                    return text + "</table>";
                }
            }
        });

        // salva grafico
        $('#fatutilizacao-chart').data('c3-chart', chart);

        // labels
        $('.chart-legend').css("display", "block");

        ShowSimulacao();

        $(".modal-dialog").draggable({
            handle: ".modal-editar-header"
        });
    });

    function ShowSimulacao () {

        // IDSimulacaoCenario
        var IDSimulacaoCenario = parseInt(@ViewBag._IDSimulacaoCenario);

        // verifica se ano existe nenhuma simulacao selecionada
        if (IDSimulacaoCenario == 0)
        {
            $("#aplica_simula").attr('disabled', true);
            $('#aplica_simula').prop('checked', false);
        }
        else
        {
            $("#aplica_simula").attr('disabled', false);
        }

        // grafico fator de utilizacao
        var chart = $('#fatutilizacao-chart').data('c3-chart');

        // checkbox aplica simulacao
        var simula = $('#aplica_simula').prop('checked');

        // verifica se ano existe nenhuma simulacao selecionada
        if (simula)
        {
            $('.simulacao').css("display", "block");
            $('.relatorio_real').css("display", "none");
            chart.show(['data5']);
        }
        else
        {
            $('.simulacao').css("display", "none");
            $('.relatorio_real').css("display", "block");
            chart.hide(['data5']);
        }

        // salva em cookie
        setCookie("AplicaSimulacao", simula, null);
    }

</script>

    