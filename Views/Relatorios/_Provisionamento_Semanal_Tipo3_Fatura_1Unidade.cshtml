﻿@using SmartEnergyLib.SQL

<style>
    .icones {
        padding: 0px 5px;
        font-size: 18px;
        float: left;
    }

    a:link {
        color: #676A6C;
    }

    a:hover {
        color: #AFB0B1;
    }

    .link_preto a:link {
        color: #7E6A6C !important;
    }
    .link_preto a:visited {
        color: #7E6A6C  !important;
    }
    .link_preto a:hover {
        color: #a0a0a0  !important;
    }
    .link_preto a:active {
        color: #7E6A6C  !important;
    }

</style>


<div class="row">
    <div class="col-lg-12">
        <div class="panel panel-title">
            <div class="panel-heading">
                <h4>@SmartEnergy.Resources.FinancasTexts.FaturaEnergia</h4>
            </div>
            <div class="panel-body">
                <div class="row">

                    <table class="table table-striped table-bordered table-hover dataTables-fatDistribuidora">
                        <thead>
                            <tr>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.UnidadeConsumidora</th>
                                <th>CNPJ</th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.Contrato</th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.EstruturaTarifaria</th>
                                <th>@SmartEnergy.Resources.ContratosCCEETexts.FaturaUsoRede<br />(R$)</th>
                                <th>@SmartEnergy.Resources.ContratosCCEETexts.FaturaLongoPrazo<br />(R$)</th>
                                <th>@SmartEnergy.Resources.ContratosCCEETexts.FaturaCurtoPrazo<br />(R$)</th>
                                <th>@SmartEnergy.Resources.MenuTexts.MenuLateralFaturaEnergia</th>
                            </tr>
                        </thead>
                        <tbody>

                            @{
                                // provisionamento
                                Provisionamento_Tipo3 provisionamento = ViewBag.ProvisionamentoSemanal;
                                
                                if (provisionamento != null)
                                {
                                    // por contrato
                                    List<Provisionamento_Tipo1_Tipo2> provs = provisionamento.por_contrato;
                                
                                    // tipos contrato
                                    List<ListaTiposDominio> listaTipoContratoMedicao = ViewBag.listaTipoContratoMedicao;

                                    // estrutura tarifária
                                    List<ListaTiposDominio> listaTipoEstruturaTarifaria = ViewBag.listaTipoEstruturaTarifaria;
                                
                                    // unidades consumidoras                                
                                    List<Provisionamento_UnidadesConsumidoras> unidadesConsumidoras = ViewBag.unidadesConsumidoras;

                                    if (unidadesConsumidoras != null)
                                    {
                                        foreach (Provisionamento_UnidadesConsumidoras unid in unidadesConsumidoras)
                                        {
                                            ListaTiposDominio tipo_contrato = listaTipoContratoMedicao.Find(item => item.ID == unid.IDContratoMedicao);
                                            string tipo_contrato_descr = "---";
                                            if (tipo_contrato != null)
                                            {
                                                tipo_contrato_descr = tipo_contrato.Descricao;
                                            }
                                        
                                            ListaTiposDominio estrutura_tarifaria = listaTipoEstruturaTarifaria.Find(item => item.ID == unid.IDEstruturaTarifaria);
                                            string estrutura_tarifaria_descr = "---";
                                            if (estrutura_tarifaria != null)
                                            {
                                                estrutura_tarifaria_descr = estrutura_tarifaria.Descricao;
                                            }

                                        
                                            <tr>
                                                <td><b>@unid.SiglaCCEE</b><br />@unid.RazaoSocial</td>
                                                <td>@unid.CNPJ</td>
                                                <td>@tipo_contrato_descr</td>
                                                <td>@estrutura_tarifaria_descr</td>
                                                <td>@string.Format("{0:#,##0.00}", unid.Fatura_Distribuidora_Valor)</td>
                                                <td>@string.Format("{0:#,##0.00}", provisionamento.TotalFaturamento_LP)</td>
                                                <td>@string.Format("{0:#,##0.00}", provisionamento.TotalFaturamento_CP)</td>
                                                <td class="link_preto">
                                                    <a href='@("/Financas/Fatura_Energia?IDCliente=" + @unid.IDCliente.ToString() + "&IDMedicao=" + @unid.IDMedicao.ToString() + "&TipoFatura=0")' title="@SmartEnergy.Resources.MenuTexts.MenuLateralFaturaEnergia">
                                                        <i class="fa fa-info-circle icones"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        }
                                    }
                                }                                    
                            }

                        </tbody>
                    </table>

                </div>
            </div>
        </div>
    </div>
</div>

<br />
<div class="row">
    <div class="col-lg-12">
        <i class="fa fa-exclamation-triangle" style="font-size:20px;"></i><span style="font-size:large; padding-left:8px;"><b>Essa é uma simulação.<br /><br />Alguns fatores podem alterar o valor final, como:<br />Ajustes contratuais junto à distribuidora; Demanda registrada acima da contratada; Energia reativa; Contribuição Municipal de Iluminação Pública; Custo de conexão.</b></span>
    </div>
</div>


<script type="text/javascript">

    $(document).ready(function () {

        $('.dataTables-fatDistribuidora').DataTable({
            "iDisplayLength": 18,
            dom: 'tp',

            "bAutoWidth": false,
            "aoColumnDefs": [
                    { "aTargets": [0], "sType": "portugues", "bSortable": false, "bSearchable": false },
                    { "aTargets": [1], "sType": "portugues", "bSortable": false, "bSearchable": false },
                    { "aTargets": [2], "sType": "portugues", "bSortable": false, "bSearchable": false },
                    { "aTargets": [3], "sType": "portugues", "bSortable": false, "bSearchable": false },
                    { "aTargets": [4], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [5], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [6], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [7], "sType": "numero", "bSortable": false, "bSearchable": false },
            ],

            "aoColumns": [
                { sWidth: "19%" },
                { sWidth: "9%" },
                { sWidth: "7%" },
                { sWidth: "9%" },
                { sWidth: "17%" },
                { sWidth: "17%" },
                { sWidth: "17%" },
                { sWidth: "5%" },
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

    });

</script>
