﻿@using SmartEnergyLib.SQL

<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.Eventos</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

    <link href="@Server.MapPath("~/Content/bootstrap.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/style.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/plugins/select2/select2.min.css")" rel="stylesheet" />
    <link href="@Server.MapPath("~/Content/plugins/dataTables/datatables.min.css")" rel="stylesheet" />
    <link href="@Server.MapPath("~/Content/Relatorios.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/RelatoriosPDF.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/fonts/font-awesome/css/font-awesome.min.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/fonts/elusive-icons-2.0.0/css/elusive-icons.min.css")" rel="stylesheet" />

    <script type="text/javascript" src="@Server.MapPath("~/Scripts/jquery-2.1.1.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/select2/select2.full.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/dataTables/datatables.min.js")" charset="utf-8"></script>

    <style>

        td {
            font-size: 12px;
        }

        tr {
            page-break-inside: avoid; 
        }

    </style>
</head>

@{
    string arquivo = "~/Logos/" + ViewBag._LogoConsultor;
    string logo = Server.MapPath(arquivo);
}

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha2 cabecalho">
                    <div class="coluna1">
                        <p><img src="@logo" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.Eventos</h4>
                        <p>@ViewBag.DataTextoAtualIni<br />@ViewBag.DataTextoAtualFim</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row" >
            <div class="col-lg-12">
                <div class="panel">
                    <div>
                        <h4>Filtros:</h4>
                        <select class="select2_tipo_2 form-control" multiple="multiple">

                            @{
                                var icone_tipo = "fa-th-list";
                                var tipo = 0;

                                for (var i = 1; i < ViewBag.NumTiposEventos; i++)
                                {
                                    tipo = ViewBag.listaTiposEventosDescricao[i].Tipo;

                                    switch (tipo)
                                    {
                                        case 0:
                                            icone_tipo = "fa-th-list";
                                            break;

                                        case 1:
                                            icone_tipo = "fa-bell";
                                            break;

                                        case 2:
                                            icone_tipo = "fa-gear";
                                            break;

                                        case 3:
                                            icone_tipo = "fa-power-off";
                                            break;

                                        case 4:
                                            icone_tipo = "fa-retweet";
                                            break;

                                        case 5:
                                            icone_tipo = "fa-sitemap";
                                            break;

                                        case 6:
                                            icone_tipo = "fa-sign-in";
                                            break;

                                        case 7:
                                            icone_tipo = "fa-sign-out";
                                            break;

                                        case 8:
                                            icone_tipo = "fa-upload";
                                            break;

                                        case 9:
                                            icone_tipo = "fa-clock-o";
                                            break;

                                        case 10:
                                            icone_tipo = "fa-user";
                                            break;
                                    }

                                    <option value=@tipo data-icon=@icone_tipo>&nbsp;&nbsp;@ViewBag.listaTiposEventosDescricao[i].Descricao</option>
                                }
                            }

                        </select>
                    </div>

                    <br />

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="panel panel-default">
                                <table id="dataTables-eventos" class="table no-margins dataTables-eventos">
                                    <thead>
                                        <tr>
                                            <th style="padding:2px; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>
                                            <th style="padding:2px; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Tipo</th>
                                            <th style="text-align:center; padding:2px; font-size:12px;"><i class="fa fa-tags" style="font-size:20px;"></i></th>
                                            <th style="padding:2px; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Evento</th>
                                        </tr>
                                    </thead>

                                    <tbody>

                                        @foreach (var evento in @ViewBag.listaEventosDescricao)
                                        {
                                            icone_tipo = "fa-th-list";
                                            tipo = evento.Tipo;

                                            switch (tipo)
                                            {
                                                case 0:
                                                    icone_tipo = "fa-th-list";
                                                    break;

                                                case 1:
                                                    icone_tipo = "fa-bell";
                                                    break;

                                                case 2:
                                                    icone_tipo = "fa-gear";
                                                    break;

                                                case 3:
                                                    icone_tipo = "fa-power-off";
                                                    break;

                                                case 4:
                                                    icone_tipo = "fa-retweet";
                                                    break;

                                                case 5:
                                                    icone_tipo = "fa-sitemap";
                                                    break;

                                                case 6:
                                                    icone_tipo = "fa-sign-in";
                                                    break;

                                                case 7:
                                                    icone_tipo = "fa-sign-out";
                                                    break;

                                                case 8:
                                                    icone_tipo = "fa-upload";
                                                    break;

                                                case 9:
                                                    icone_tipo = "fa-clock-o";
                                                    break;

                                                case 10:
                                                    icone_tipo = "fa-user";
                                                    break;
                                            }

                                            <tr>
                                                <td><span style="display:none;">@evento.DataHora_Sort</span>@evento.DataHora</td>
                                                <td>@evento.Tipo</td>
                                                <td style="text-align:center;"><i class="fa @icone_tipo" style="font-size:20px;"></i></td>
                                                <td>@evento.Descricao</td>
                                            </tr>

                                        }

                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>


<script type="text/javascript">

    $(document).ready(function () {

        var table = $('.dataTables-eventos').DataTable({
            "iDisplayLength": 10000,
            dom: 'ft',
            "aoColumnDefs": [
                        {
                            "aTargets": [0],
                            "bVisible": true,
                            "bSortable": false,
                            "bSearchable": true,
                        },
                        {
                            "aTargets": [1],
                            "bVisible": false,
                            "bSortable": true,
                            "bSearchable": true,
                        },
                        {
                            "aTargets": [2],
                            "bVisible": true,
                            "bSortable": false,
                            "bSearchable": true,
                        },
                        {
                            "aTargets": [3],
                            "bVisible": true,
                            "bSortable": false,
                            "bSearchable": true,
                        },
            ],
            "oSearch": {
                "bSmart": false,
                "bRegex": true,
                "sSearch": ""
            },
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "20%" },
            { sWidth: "5%" },
            { sWidth: "5%" },
            { sWidth: "75%" }
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

        // cookie de search
        var search = decodeURIComponent( @Html.Raw(Json.Encode(@ViewBag.Relat_Search)) );
        $('.dataTables-eventos').DataTable().search(search).draw();

        // cookie de sort
        var sortedCol = Math.ceil(@ViewBag.Relat_SortedCol);
        if (sortedCol == "") sortedCol = 0;
        var sortedDir = @Html.Raw(Json.Encode(@ViewBag.Relat_SortedDir));

        if (sortedCol == 0 || sortedCol == 3) {
            $('#dataTables-eventos').dataTable().fnSort([[sortedCol, sortedDir]]);
        }


        function formatIcon(icon) {

            if (!icon.id) {
                return icon.text;
            }
            var originalOption = icon.element;
            var $icon = $('  <i style="font-size:16px;" class="fa ' + $(originalOption).data('icon') + '"></i><span>' + icon.text + '</span>');

            return $icon;

        };

        $(".select2_tipo").select2({
            minimumResultsForSearch: Infinity,
            width: "100%",
            templateResult: formatIcon,
            templateSelection: formatIcon
        });

        var placeholder = "";

        $(".select2_tipo_2").select2({
            placeholder: placeholder,
            minimumResultsForSearch: Infinity,
            width: "100%",
            templateResult: formatIcon,
            templateSelection: formatIcon,
            allowClear: true,
            closeOnSelect: true,
            escapeMarkup: function (m) {
                return m;
            }
        });

        // cookie de filtro
        var lista_selecao = @Html.Raw(Json.Encode(@ViewBag.Relat_TipoEvento));

        // troco / por |
        var str = lista_selecao.replace(/[\/\/]/g, "|");

        // setar selecao na lista
        var lista = lista_selecao.split("/");

        // procura na tabela
        $('.dataTables-eventos').DataTable().column(1).search(str, true, false).draw();

        // aplica filtros
        $(".select2_tipo_2").select2("val", lista);
    });

    function getCookie(c_name) {

        // Get name followed by anything except a semicolon
        var cookiestring = RegExp("" + c_name + "[^;]+").exec(document.cookie);

        // Return everything after the equal sign, or an empty string if the cookie name not found
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./, "") : "");
    }

</script>
