﻿<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.EA</title>

    <link href="@Server.MapPath("~/Content/bootstrap.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/style.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/plugins/c3/c3.min.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/Relatorios.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/RelatoriosPDF.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/fonts/font-awesome/css/font-awesome.min.css")" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="@Server.MapPath("~/Scripts/jquery-2.1.1.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/d3/d3.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/c3/c3.min.js")"></script>

</head>

@{
    string arquivo = "~/Logos/" + ViewBag._LogoConsultor;
    string logo = Server.MapPath(arquivo);
}

@if (ViewBag.Retorno == 2)
{
    <div class="row">
        <div class="col-lg-12">
            <div class="linha cabecalho">
                <div class="coluna1">
                    <p><img src="@logo" style="height:42px; width:auto;" /></p>
                </div>

                <div class="coluna2">
                    <h3>@ViewBag.ClienteNome</h3>
                    @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                </div>

                <div class="coluna3">
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.EA - @SmartEnergy.Resources.RelatoriosTexts.RelatMensal</h4>
                    <p>@ViewBag.DataTextoAtual</p>
                </div>
            </div>
        </div>
    </div>

    <br /><br /><br /><br /><br /><br />
    <div class="row">
        <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
            <i class="fa fa-warning fa-5x"></i>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>@SmartEnergy.Resources.RelatoriosTexts.NaoExistemRegistros</h2>
        </div>
    </div>
    <br /><br /><br /><br />

}
else
{

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="@logo" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.EA - @SmartEnergy.Resources.RelatoriosTexts.RelatMensal</h4>
                        <p>@ViewBag.DataTextoAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <br />

        @{
            if (ViewBag.showGrafico)
            {
                <div class="row">
                    <div class="relat-grafico" style="height:450px; width:800px; margin-left:50px;">
                        <div id="grafico-ea" style="margin-top: -10px"></div>
                        <div class='chart-legend' style="display:none;">
                            <div class='legend-scale-dem'>
                                <ul class='legend-labels' style="margin-top: -25px;">
                                    <li><span style='background:#1C84C6 !important;'></span>@ViewBag.NomeGrandeza</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }

        <hr />

        <div class="row">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-ea" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.MedioMes</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Maximo</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Minimo</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@ViewBag.NomeGrandeza</td>
                                <td class="relat-ea">@ViewBag.VMedMes @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.DataTextoAtual)</small></td>
                                <td class="relat-preto">@ViewBag.VMaxMes @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMaxMes_DataHora)</small></td>
                                <td class="relat-preto">@ViewBag.VMinMes @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMinMes_DataHora)</small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>


    @{
        if (ViewBag.showTabela)
        {
            <div class="page-break"></div>
            
            <div class="row">

                <br /><br />

                <div class="col-lg-12">
                    <div class="panel panel-default">
                        <table class="table no-margins tabela-grafico-pdf">
                            <thead>
                                <tr>
                                    <th style="width:25%; text-align:center; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Dia</th>
                                    <th style="width:25%; text-align:center; font-size:12px;">@SmartEnergy.Resources.SupervisaoTexts.Medio<br />(@ViewBag.UnidadeGrandeza)</th>
                                    <th style="width:25%; text-align:center; font-size:12px;">@SmartEnergy.Resources.SupervisaoTexts.Maximo<br />(@ViewBag.UnidadeGrandeza)</th>
                                    <th style="width:25%; text-align:center; font-size:12px;">@SmartEnergy.Resources.SupervisaoTexts.Minimo<br />(@ViewBag.UnidadeGrandeza)</th>
                                </tr>
                            </thead>

                            <tbody>

                            @{
                                var i = 0;
                                var j = 0;
                                var vmed = "---";
                                var vmax = "---";
                                var vmin = "---";
                                
                                var NumDiasMes = ViewBag.NumDiasMes;
                                            
                                for (i = 0; i < NumDiasMes; i++)
                                {
                                    j = i + 1;

                                    vmed = string.Format("{0:#,##0.00}", ViewBag.VMed[j]);
                                    vmax = string.Format("{0:#,##0.00}", ViewBag.VMax[j]);
                                    vmin = string.Format("{0:#,##0.00}", ViewBag.VMin[j]);

                                    <tr class="tabela_valores" style="font-size:12px;">
                                        <td style="text-align:center; font-size:12px;" class="relat-ea">@ViewBag.Dias[j]</td>
                                        <td style="text-align:center; font-size:12px;" class="relat-ea">@vmed</td>
                                        <td style="text-align:center; font-size:12px;" class="relat-ea">@vmax</td>
                                        <td style="text-align:center; font-size:12px;" class="relat-ea">@vmin</td>
                                    </tr>
                                }
                            }

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }
    }

    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>

<script type="text/javascript">

    $(document).ready(function () {

        // entradas analogicas
        var dataX = [];
        var labelX = [];
        var dataMaxima = [];
        var dataMedia = [];
        var dataMinima = [];

        // copia valores
        var VMax = @Html.Raw(Json.Encode(@ViewBag.VMax));
        var VMed = @Html.Raw(Json.Encode(@ViewBag.VMed));
        var VMin = @Html.Raw(Json.Encode(@ViewBag.VMin));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));
        var NumDiasMes = @Html.Raw(Json.Encode(@ViewBag.NumDiasMes));
        var NomeGrandeza = @Html.Raw(Json.Encode(@ViewBag.NomeGrandeza));
        var UnidadeGrandeza = @Html.Raw(Json.Encode(@ViewBag.UnidadeGrandeza));

        // valor minimo e máximo
        var minimoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMin));
        var maximoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMax));

        dataX.push('x');
        dataMaxima.push(['data1']);
        dataMedia.push(['data2']);
        dataMinima.push(['data3']);

        for (i = 0; i < NumDiasMes+2; i++)
        {
            // X
            dataX.push(Datas[i]);

            dataMaxima.push([VMax[i]]);
            dataMedia.push([VMed[i]]);
            dataMinima.push([VMin[i]]);
        }

        // valores label X
        labelX.push(Datas[1]);
        for (i = 5; i <= 25; i+=5)
        {
            labelX.push(Datas[i]);
        }
        labelX.push(Datas[NumDiasMes]);

        var Data = [dataX,dataMinima,dataMedia,dataMaxima];

        c3.generate({

            bindto: '#grafico-ea',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'bar',
                    data3: 'bar',
                },
                colors: {
                    data1: '#00007f',
                    data2: '#4284B2',
                    data3: '#D7E8F8',
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // retorna dia
                            return x.getDate();
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    // Grafico 1 dia     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(11 horas)
                    padding: -1000*60*60*11
                },
                y:  {
                    min: minimoValor,
                    max: maximoValor,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {
                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.15 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < d.length; i++) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        if (! text) {

                            // split string and create array.
                            var arr = Datas[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            title = "Dia " + data.getDate();

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1C84C6'>" + title + "</th></tr>" : "");
                        }

                        switch(i)
                        {
                            case 0:     // minimo
                                name = "Mínimo";
                                break;

                            case 1:     // medio
                                name = "Médio";
                                break;

                            case 2:     // maximo
                                name = "Máximo";
                                break;
                        }

                        value = d[i].value;
                        bgcolor = $$.levelColor ? $$.levelColor(d[i].value) : color(d[i].id);

                        text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                        text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                        text += "<td class='value'>" + value.toFixed(2) + " " + UnidadeGrandeza + "</td>";
                        text += "</tr>";
                    }
                    return text + "</table>";
                }
            }
        });

        // labels
        $('.chart-legend').css("display", "block");

    });

</script>

}
    