﻿@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    List<DistribuicaoConsumoValor> valores_resultado = ViewBag.valores_resultado;
}

<style>

    .center-block {
      float: left;
      margin-right: auto;
      margin-left: auto;
    }

</style>


<div id="distribuicao_resultado" style="display:none;">

    <br /><br />

    <div class="row">
        <div class="col-lg-12">

            <div class="panel panel-default">
                <div class="panel-body">

                    <div class="col-lg-5">
                        <div id="grafico-pizza"></div>
                    </div>

                    <div class="col-lg-7">

                        @{
                            double ConsumoTotal = 0.0;

                            if (ViewBag.ConsumoTotal != null)
                            {
                                ConsumoTotal = ViewBag.ConsumoTotal;    
                            }
                        }

                        <br />

                        <div class="row">
                            <div class="col-lg-6">
                                <div class="relat-valorAtual relat-capacitivo">
                                    <h5>@SmartEnergy.Resources.RelatoriosTexts.ConsumoTotal</h5>
                                    <h1>@string.Format("{0:#,##0.0}", ConsumoTotal) <span>kWh</span></h1>
                                </div>
                            </div>
                        </div>

                        <br /><br /><br />

                        <div class="row">
                            <div class="col-lg-12">

                                <table id="dataTables-distribuicoes" class="table table-striped table-bordered table-hover dataTables-distribuicoes">
                                    <thead>
                                        <tr>
                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.GrupoMedicoes</th>
                                            <th>@SmartEnergy.Resources.RelatoriosTexts.Consumo (kWh)</th>
                                            <th>@SmartEnergy.Resources.AnalisesTexts.Percentual (%)</th>
                                        </tr>
                                    </thead>
                                    <tbody>

                                        @{
                                            if (valores_resultado != null)
                                            {
                                                foreach (DistribuicaoConsumoValor valor in valores_resultado)
                                                {
                                                    string consumo = string.Format("{0:#,##0.0}", valor.Consumo);
                                                    string percentual = string.Format("{0:#,##0.0}", valor.Porcentagem);

                                                    <tr>
                                                        <td>@valor.NomeGrupoMedicoes</td>
                                                        <td>@consumo</td>
                                                        <td>@percentual</td>
                                                    </tr>

                                                }
                                            }
                                        }

                                    </tbody>
                                </table>

                            </div>                    
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

</div>



<script type="text/javascript">

    $(document).ready(function () {

        //
        // PIZZA
        //

        var numGruposMedicoes = @Html.Raw(Json.Encode(@ViewBag.numGruposMedicoes));

        if (numGruposMedicoes > 0)
        {
            // consumo
            var Data = [];
            var dataNomes = [];
            var dataConsumo = [];
            var dataPorcentagem = [];

            // copia valores
            var Nomes = @Html.Raw(Json.Encode(@ViewBag.Nomes));
            var Consumo = @Html.Raw(Json.Encode(@ViewBag.Consumo));
            var Porcentagem = @Html.Raw(Json.Encode(@ViewBag.Porcentagem));

            var numGruposMedicoes = @Html.Raw(Json.Encode(@ViewBag.numGruposMedicoes));


            dataNomes.push(['Nomes']);
            dataConsumo.push(['Consumo']);
            dataPorcentagem.push(['Porcentagem']);

            for (i = 0; i < numGruposMedicoes; i++)
            {
                dataNomes.push([Nomes[i]]);
                dataConsumo.push([Consumo[i]]);
                dataPorcentagem.push([Porcentagem[i]]);

                Data.push([Nomes[i], Porcentagem[i]]);
            }


            var chart = c3.generate({
                bindto: '#grafico-pizza',
                size: {
                    height: 400
                },
                padding: {
                    top: 0,
                    right: 0,
                    bottom: 0,
                    left: 0
                },
                legend: {
                    position: 'right'
                },
                point: {
                    show: false,
                    sensitivity: 1000
                },
                data: {
                    columns: Data,
                    type: 'pie',
                    onclick: function (d, i) { console.log("onclick", d, i); },
                    onmouseover: function (d, i) { console.log("onmouseover", d, i); },
                    onmouseout: function (d, i) { console.log("onmouseout", d, i); }
                },
                pie: {
                    label: {
                        format: function (value, ratio, id) {
                            return value.toFixed(1) + '%';
                        }
                    }
                }
            });
        }



        //
        // TABELA
        //

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {
                a = (a === "-") ? 0 : a.replace(/[^\d\-\.]/g, "");
                return parseFloat(a);
            },

            "numero-asc": function (a, b) {
                return a - b;
            },

            "numero-desc": function (a, b) {
                return b - a;
            }
        });

        $('.dataTables-distribuicoes').DataTable({
            "scrollX": true,
            "responsive": true,
            "bAutoWidth": false,
            "iDisplayLength": 12,
            dom: 'tp',
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "portugues", "bSortable": true, "bSearchable": true, "className": "dt-center" },
                        { "aTargets": [1], "sType": "numero", "bSortable": true, "bSearchable": false },
                        { "aTargets": [2], "sType": "numero", "bSortable": true, "bSearchable": false },
            ],
            "oSearch": {
                "bSmart": false,
                "bRegex": true,
                "sSearch": ""
            },
            "aoColumns": [
                { sWidth: "50%" },
                { sWidth: "25%" },
                { sWidth: "25" },
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

        // ajusta largura depois de um tempo
        setTimeout(function () {

            $('#dataTables-distribuicoes').dataTable().fnSort([[0, "asc"]]);

        }, 10);

        // labels
        $('#distribuicao_resultado').css("display", "block");

        // força um resize por causa do grafico deslocado
        window.dispatchEvent(new Event('resize'));

    });

</script>


