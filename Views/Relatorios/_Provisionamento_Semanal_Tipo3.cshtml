﻿@using SmartEnergyLib.SQL

<br />

@{
    List<Provisionamento_UnidadesConsumidoras> unidCons_Unificados = ViewBag.unidadesConsumidoras;

    if (unidCons_Unificados != null)
    {
        if (unidCons_Unificados.Count > 1)
        {
            <div class="row">
                <div class="col-lg-12">
                    <div class="tabs-container">
                        <ul class="nav nav-tabs">
                            <li class="active"><a data-toggle="tab" href="#tab-1">@SmartEnergy.Resources.ContratosCCEETexts.ContratosCCEE</a></li>
                            <li class="tab-2"><a data-toggle="tab" href="#tab-2">@SmartEnergy.Resources.ContratosCCEETexts.AnaliseContratoConsumo</a></li>
                            <li class="tab-3"><a data-toggle="tab" href="#tab-3">@SmartEnergy.Resources.ContratosCCEETexts.HistoricoConsumo</a></li>
                            <li class="tab-4"><a data-toggle="tab" href="#tab-4">@SmartEnergy.Resources.MenuTexts.MenuLateralRateio</a></li>
                            <li class="tab-5"><a data-toggle="tab" href="#tab-5">@SmartEnergy.Resources.ContratosCCEETexts.FaturaUsoRede</a></li>
                            <li class="tab-6"><a data-toggle="tab" href="#tab-6">@SmartEnergy.Resources.ContratosCCEETexts.FaturaLongoPrazo</a></li>
                            <li class="tab-7"><a data-toggle="tab" href="#tab-7">@SmartEnergy.Resources.ContratosCCEETexts.FaturaCurtoPrazo</a></li>
                        </ul>
                        <div class="tab-content">
                            <div id="tab-1" class="tab-pane active">
                                <div class="panel-body" style="min-height:630px;">

                                    @Html.Partial("_Provisionamento_Semanal_Contratos")

                                </div>
                            </div>

                            <div id="tab-2" class="tab-pane">
                                <div class="panel-body" style="min-height:630px;">

                                    @Html.Partial("_Provisionamento_Semanal_Tipo3_Analise")

                                </div>
                            </div>

                            <div id="tab-3" class="tab-pane">
                                <div class="panel-body" style="min-height:630px;">

                                    @Html.Partial("_Provisionamento_Semanal_Consumo")

                                </div>
                            </div>

                            <div id="tab-4" class="tab-pane">
                                <div class="panel-body" style="min-height:630px;">

                                    @Html.Partial("_Provisionamento_Semanal_Tipo3_Rateio_NUnidades")

                                </div>
                            </div>

                            <div id="tab-5" class="tab-pane">
                                <div class="panel-body" style="min-height:630px;">

                                    @Html.Partial("_Provisionamento_Semanal_Tipo3_FaturaUsoRede")

                                </div>
                            </div>

                            <div id="tab-6" class="tab-pane">
                                <div class="panel-body" style="min-height:630px;">

                                    @Html.Partial("_Provisionamento_Semanal_Tipo3_FaturaLongoPrazo_NUnidades")

                                </div>
                            </div>

                            <div id="tab-7" class="tab-pane">
                                <div class="panel-body" style="min-height:630px;">

                                    @Html.Partial("_Provisionamento_Semanal_Tipo3_FaturaCurtoPrazo_NUnidades")

                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            
        }
        else
        {
            
            <div class="row">
                <div class="col-lg-12">
                    <div class="tabs-container">
                        <ul class="nav nav-tabs">
                            <li class="active"><a data-toggle="tab" href="#tab-1">@SmartEnergy.Resources.ContratosCCEETexts.ContratosCCEE</a></li>
                            <li class="tab-2"><a data-toggle="tab" href="#tab-2">@SmartEnergy.Resources.ContratosCCEETexts.AnaliseContratoConsumo</a></li>
                            <li class="tab-3"><a data-toggle="tab" href="#tab-3">@SmartEnergy.Resources.ContratosCCEETexts.HistoricoConsumo</a></li>
                            <li class="tab-4"><a data-toggle="tab" href="#tab-4">@SmartEnergy.Resources.MenuTexts.MenuLateralRateio</a></li>
                            <li class="tab-5"><a data-toggle="tab" href="#tab-5">@SmartEnergy.Resources.ContratosCCEETexts.FaturaUsoRede</a></li>
                        </ul>
                        <div class="tab-content">
                            <div id="tab-1" class="tab-pane active">
                                <div class="panel-body" style="min-height:630px;">

                                    @Html.Partial("_Provisionamento_Semanal_Contratos")

                                </div>
                            </div>

                            <div id="tab-2" class="tab-pane">
                                <div class="panel-body" style="min-height:630px;">

                                    @Html.Partial("_Provisionamento_Semanal_Tipo3_Analise")

                                </div>
                            </div>

                            <div id="tab-3" class="tab-pane">
                                <div class="panel-body" style="min-height:630px;">

                                    @Html.Partial("_Provisionamento_Semanal_Consumo")

                                </div>
                            </div>

                            <div id="tab-4" class="tab-pane">
                                <div class="panel-body" style="min-height:630px;">

                                    @Html.Partial("_Provisionamento_Semanal_Tipo3_Rateio_1Unidade")

                                </div>
                            </div>

                            <div id="tab-5" class="tab-pane">
                                <div class="panel-body" style="min-height:630px;">

                                    @Html.Partial("_Provisionamento_Semanal_Tipo3_Fatura_1Unidade")

                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            
        }
    }
}    

<script type="text/javascript">

    $(document).ready(function () {

        $(function () {

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "portugues-pre": function (data) {
                    var a = 'a';
                    var e = 'e';
                    var i = 'i';
                    var o = 'o';
                    var u = 'u';
                    var c = 'c';
                    var special_letters = {
                        "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                        "É": e, "é": e, "Ê": e, "ê": e,
                        "Í": i, "í": i, "Î": i, "î": i,
                        "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                        "Ú": u, "ú": u, "Ü": u, "ü": u,
                        "ç": c, "Ç": c
                    };
                    for (var val in special_letters)
                        data = data.split(val).join(special_letters[val]).toLowerCase();
                    return data;
                },
                "portugues-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },
                "portugues-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "numero-pre": function (a) {
                    var x = (a == "-") ? 0 : a.replace(/,/, ".");
                    return parseFloat(x);
                },

                "numero-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },

                "numero-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            $(document).on('shown.bs.tab', 'a[data-toggle="tab"]', function (e) {
                window.dispatchEvent(new Event('resize'));
            });

        });

    });

</script>
