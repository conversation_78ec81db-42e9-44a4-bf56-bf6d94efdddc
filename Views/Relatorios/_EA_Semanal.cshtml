﻿<style>

    .icone_semana {
    padding: 0px 7px;
    font-size: 14px;
    float: left; 
    color: white;
}

.c3-line {
    stroke-width: 1px;
}

</style>

<div class="row">
    <div class="col-lg-4 col-md-12">
        <div class="row">
            <div class="row">
                <div class="col-lg-6">
                    <div class="relat-coluna">
                        <div class="relat-valorAtual relat-ea">
                            <h4>@ViewBag.NomeGrandeza</h4>
                            <h5>@SmartEnergy.Resources.RelatoriosTexts.MedioSemana</h5>
                            <h1>@ViewBag.VMedSemana <span>@ViewBag.UnidadeGrandeza</span></h1>
                            <h6>@ViewBag.DataTextoAtual</h6>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="relat-coluna">
                        <div class="relat-valorAtual relat-preto">
                            <h4>@ViewBag.NomeGrandeza</h4>
                            <h5>@SmartEnergy.Resources.SupervisaoTexts.Maximo</h5>
                            <h1>@ViewBag.VMaxSemana <span>@ViewBag.UnidadeGrandeza</span></h1>
                            <h6>@ViewBag.VMaxSemana_DataHora</h6>
                        </div>
                    </div>
                </div>
            </div>
            <br /><br />
            <div class="row">
                <div class="col-lg-offset-6 col-lg-6">
                    <div class="relat-coluna">
                        <div class="relat-valorAtual relat-preto">
                            <h4>@ViewBag.NomeGrandeza</h4>
                            <h5>@SmartEnergy.Resources.SupervisaoTexts.Minimo</h5>
                            <h1>@ViewBag.VMinSemana <span>@ViewBag.UnidadeGrandeza</span></h1>
                            <h6>@ViewBag.VMinSemana_DataHora</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-8 col-md-12">
        <div class="row">
            <div class="relat-grafico">
                <div id="grafico-ea"></div>
                <div class='chart-legend' style="display:none;">
                    <div class='legend-scale-dem'>
                        <ul class='legend-labels' style="margin-top: 0px;">
                            <li><a href="#" onclick="alteraDiaSemana(0,true,0);"><span style='background:#0000FF;' id="ckDomingo"><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.domingo</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(1,true,0);"><span style='background:#00FF00;' id="ckSegunda"><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.segunda</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(2,true,0);"><span style='background:#FF0000;' id="ckTerca"  ><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.terca</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(3,true,0);"><span style='background:#FF7F00;' id="ckQuarta" ><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.quarta</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(4,true,0);"><span style='background:#FF00FF;' id="ckQuinta" ><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.quinta</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(5,true,0);"><span style='background:#000000;' id="ckSexta"  ><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.sexta</a></li>
                            <li><a href="#" onclick="alteraDiaSemana(6,true,0);"><span style='background:#00FFFF;' id="ckSabado" ><i class="fa fa-check icone_semana"></i></span> @SmartEnergy.Resources.ComumTexts.sabado</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<br />
<div class="row">
    <div class="col-lg-4">
        <div class="relat-coluna" style="margin-top:-50px;">
            <div class="relat-valorAtual relat-ea">
                <label>&nbsp;&nbsp;<input type="checkbox" class="i-checks" id="check_med" checked>&nbsp;&nbsp;Valores Médios</label><br />
                <label>&nbsp;&nbsp;<input type="checkbox" class="i-checks" id="check_max" checked>&nbsp;&nbsp;Valores Máximos</label><br />
                <label>&nbsp;&nbsp;<input type="checkbox" class="i-checks" id="check_min" checked>&nbsp;&nbsp;Valores Mínimos</label>

            </div>
        </div>
    </div>

    @Html.Partial("_Relat_Botoes_EA_Semanal")

</div>

<br />
<div class="panel panel-title relatorio">
    <div class="panel-body">

        <div class="row">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-ea" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.MedioSemana</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Maximo</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Minimo</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@ViewBag.NomeGrandeza</td>
                                <td class="relat-ea">@ViewBag.VMedSemana @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.DataTextoAtual)</small></td>
                                <td class="relat-preto">@ViewBag.VMaxSemana @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMaxSemana_DataHora)</small></td>
                                <td class="relat-preto">@ViewBag.VMinSemana @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMinSemana_DataHora)</small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal inmodal animated fadeIn" id="modalTabela" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-editar-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title"><i class="fa fa-list"></i>&nbsp;&nbsp;@SmartEnergy.Resources.RelatoriosTexts.TabelaRegistros</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-default">

                    @{
                        var k = 0;

                        var dia_semana = new string[] { SmartEnergy.Resources.ComumTexts.domingo, 
                                                                    SmartEnergy.Resources.ComumTexts.segunda,
                                                                    SmartEnergy.Resources.ComumTexts.terca,
                                                                    SmartEnergy.Resources.ComumTexts.quarta,
                                                                    SmartEnergy.Resources.ComumTexts.quinta,
                                                                    SmartEnergy.Resources.ComumTexts.sexta,
                                                                    SmartEnergy.Resources.ComumTexts.sabado
                                                                };
                    }

                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:16%;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>
                                <th style="width:1%;"></th>

                                @for (k = 0; k < 7; k++)
                                {
                                    <th style="width:12%;">@dia_semana[k]<br />@ViewBag.Dias[k]<br />(@ViewBag.UnidadeGrandeza)</th>
                                }    

                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var i = 0;
                                var j = 0;
                                var vmed = "---";
                                var vmax = "---";
                                var vmin = "---";

                                for (i = 0; i < 24; i++)
                                {
                                    j = i + 1;

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="relat-preto">@ViewBag.Horas[j]</td>
                                        <td style="text-align:left;" class="relat-preto">Med<br />Máx<br />Mín</td>

                                        @for (k = 0; k < 7; k++)
                                        {
                                            vmed = string.Format("{0:#,##0.00}", ViewBag.VMed[k, j]);
                                            vmax = string.Format("{0:#,##0.00}", ViewBag.VMax[k, j]);
                                            vmin = string.Format("{0:#,##0.00}", ViewBag.VMin[k, j]);
                                            
                                            <td style="text-align:center;" class="relat-ea">@vmed<br />@vmax<br />@vmin</td>
                                        }    

                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
            </div>
        </div>
    </div>
</div>


@Html.Partial("_EventosModal")

@Html.Partial("_ObservacoesModal")


<script type="text/javascript">

    // dias da semana
    var diasSemana = [];

    diasSemana.push('Domingo');
    diasSemana.push('Segunda');
    diasSemana.push('Terça');
    diasSemana.push('Quarta');
    diasSemana.push('Quinta');
    diasSemana.push('Sexta');
    diasSemana.push('Sábado');
    diasSemana.push('Período');

    // cores
    var coresdiasSemana = [];

    coresdiasSemana.push('#0000FF');
    coresdiasSemana.push('#00FF00');
    coresdiasSemana.push('#FF0000');
    coresdiasSemana.push('#FF7F00');
    coresdiasSemana.push('#FF00FF');
    coresdiasSemana.push('#000000');
    coresdiasSemana.push('#00FFFF');

    // c_names
    var c_names = [];

    c_names.push("Relat_SemanalDomingo");
    c_names.push("Relat_SemanalSegunda");
    c_names.push("Relat_SemanalTerca");
    c_names.push("Relat_SemanalQuarta");
    c_names.push("Relat_SemanalQuinta");
    c_names.push("Relat_SemanalSexta");
    c_names.push("Relat_SemanalSabado");

    // id_names
    var id_names = [];

    id_names.push("#ckDomingo");
    id_names.push("#ckSegunda");
    id_names.push("#ckTerca");
    id_names.push("#ckQuarta");
    id_names.push("#ckQuinta");
    id_names.push("#ckSexta");
    id_names.push("#ckSabado");


    $(document).ready(function () {

        // copia valores
        var VMax = @Html.Raw(Json.Encode(@ViewBag.VMax));
        var VMed = @Html.Raw(Json.Encode(@ViewBag.VMed));
        var VMin = @Html.Raw(Json.Encode(@ViewBag.VMin));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));
        var NomeGrandeza = @Html.Raw(Json.Encode(@ViewBag.NomeGrandeza));
        var UnidadeGrandeza = @Html.Raw(Json.Encode(@ViewBag.UnidadeGrandeza));

        // valor minimo e máximo
        var minimoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMin));
        var maximoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMax));

        var diferenca = maximoValor - minimoValor;

        // grafico
        var dataX = [];
        var labelX = [];
        var dataDom_Max = [], dataSeg_Max = [], dataTer_Max = [], dataQua_Max = [], dataQui_Max = [], dataSex_Max = [], dataSab_Max = [];
        var dataDom_Med = [], dataSeg_Med = [], dataTer_Med = [], dataQua_Med = [], dataQui_Med = [], dataSex_Med = [], dataSab_Med = [];
        var dataDom_Min = [], dataSeg_Min = [], dataTer_Min = [], dataQua_Min = [], dataQui_Min = [], dataSex_Min = [], dataSab_Min = [];

        dataX.push('x');
        dataDom_Max.push(['data1']);  dataSeg_Max.push(['data2']);  dataTer_Max.push(['data3']);  dataQua_Max.push(['data4']);  dataQui_Max.push(['data5']);  dataSex_Max.push(['data6']);  dataSab_Max.push(['data7']);
        dataDom_Med.push(['data11']); dataSeg_Med.push(['data12']); dataTer_Med.push(['data13']); dataQua_Med.push(['data14']); dataQui_Med.push(['data15']); dataSex_Med.push(['data16']); dataSab_Med.push(['data17']);
        dataDom_Min.push(['data21']); dataSeg_Min.push(['data22']); dataTer_Min.push(['data23']); dataQua_Min.push(['data24']); dataQui_Min.push(['data25']); dataSex_Min.push(['data26']); dataSab_Min.push(['data27']);

        for (i = 0; i < 26; i++)
        {
            // X
            dataX.push(Datas[i]);

            dataDom_Max.push([ VMax[i]        ]);
            dataSeg_Max.push([ VMax[i+26]     ]);
            dataTer_Max.push([ VMax[i+(2*26)] ]);
            dataQua_Max.push([ VMax[i+(3*26)] ]);
            dataQui_Max.push([ VMax[i+(4*26)] ]);
            dataSex_Max.push([ VMax[i+(5*26)] ]);
            dataSab_Max.push([ VMax[i+(6*26)] ]);

            dataDom_Med.push([ VMed[i]        ]);
            dataSeg_Med.push([ VMed[i+26]     ]);
            dataTer_Med.push([ VMed[i+(2*26)] ]);
            dataQua_Med.push([ VMed[i+(3*26)] ]);
            dataQui_Med.push([ VMed[i+(4*26)] ]);
            dataSex_Med.push([ VMed[i+(5*26)] ]);
            dataSab_Med.push([ VMed[i+(6*26)] ]);

            dataDom_Min.push([ VMin[i]        ]);
            dataSeg_Min.push([ VMin[i+26]     ]);
            dataTer_Min.push([ VMin[i+(2*26)] ]);
            dataQua_Min.push([ VMin[i+(3*26)] ]);
            dataQui_Min.push([ VMin[i+(4*26)] ]);
            dataSex_Min.push([ VMin[i+(5*26)] ]);
            dataSab_Min.push([ VMin[i+(6*26)] ]);
        }

        // valores label X
        labelX.push(Datas[1]);
        for (i = 3; i <= 24; i+=3)
        {
            labelX.push(Datas[i]);
        }

        Data = [dataX,
                dataDom_Max,dataSeg_Max,dataTer_Max,dataQua_Max,dataQui_Max,dataSex_Max,dataSab_Max,
                dataDom_Med,dataSeg_Med,dataTer_Med,dataQua_Med,dataQui_Med,dataSex_Med,dataSab_Med,
                dataDom_Min,dataSeg_Min,dataTer_Min,dataQua_Min,dataQui_Min,dataSex_Min,dataSab_Min];

        // desenha grafico
        c3_generate('#grafico-ea', Data);

        // label dias da semana
        todosDiaSemana();

        // labels
        $('.chart-legend').css("display", "block");


        function c3_generate(chart_name, dados) {

            var chart = c3.generate({

                bindto: chart_name,
                size: {
                    height: 420
                },
                padding: {
                    top: 8,
                    right: 10,
                    bottom: 30,
                    left: 43
                },
                data: {
                    x: 'x',
                    xFormat : '%Y-%m-%d %H:%M:%S',
                    columns: dados,
                    types: {
                        data1:  'line', data2:  'line', data3:  'line', data4:  'line', data5:  'line', data6:  'line', data7:  'line',
                        data11: 'line', data12: 'line', data13: 'line', data14: 'line', data15: 'line', data16: 'line', data17: 'line',
                        data21: 'line', data22: 'line', data23: 'line', data24: 'line', data25: 'line', data26: 'line', data27: 'line'
                    },
                    colors: {
                        data1:  '#0000FF', data2:  '#00FF00', data3:  '#FF0000', data4:  '#FF7F00', data5:  '#FF00FF', data6:  '#000000', data7:  '#00FFFF',
                        data11: '#0000FF', data12: '#00FF00', data13: '#FF0000', data14: '#FF7F00', data15: '#FF00FF', data16: '#000000', data17: '#00FFFF',
                        data21: '#0000FF', data22: '#00FF00', data23: '#FF0000', data24: '#FF7F00', data25: '#FF00FF', data26: '#000000', data27: '#00FFFF'
                    },
                },
                point: {
                    r:3
                },
                legend: {
                    show: false
                },
                axis: {
                    x:  {
                        type: 'timeseries',
                        tick: {
                            outer: false,
                            values: labelX,
                            format: function (x) {

                                // horas e minutos
                                var hours = x.getHours();
                                var minutes = x.getMinutes();

                                hours = hours < 10 ? '0'+hours : hours;
                                minutes = minutes < 10 ? '0'+minutes : minutes;
                                var strTime = hours + ':' + minutes;

                                // verifica se nao deve mostrar label
                                if(hours==3 || hours==9 || hours==15 || hours==21)
                                    return "";

                                // retorna hora e minuto
                                return strTime;
                            }
                        },
                        // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                        // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                        padding: -1000*60*28
                    },
                    y:  {
                        min: minimoValor,
                        max: maximoValor,
                        padding: {
                            top: 0,
                            bottom: 0
                        },
                        tick: {
                            outer: false,
                            format: function (d) {

                                if( diferenca < 10)
                                {
                                    return d.toFixed(2);
                                }

                                if( diferenca < 100)
                                {
                                    return d.toFixed(1);
                                }

                                return parseInt(d);
                            }
                        }
                    }
                },
                grid: {
                    x: {
                        show: false
                    }
                },
                tooltip: {
                    format: {
                        /*...*/
                    },
                    contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                        var $$ = this, config = $$.config,
                            titleFormat = config.tooltip_format_title || defaultTitleFormat,
                            nameFormat = config.tooltip_format_name || function (name) { return name; },
                            valueFormat = config.tooltip_format_value || defaultValueFormat,
                            text, i, title, value, name, bgcolor;

                        for (i = 0; i < d.length; i++) {
                            if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                            // titulo
                            if (! text) {

                                // split string and create array.
                                var arr = Datas[d[0].index].split(/-|\s|:/);

                                // decrease month value by 1
                                var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                                // horas e minutos
                                var hours = data.getHours();
                                var minutes = data.getMinutes();

                                hours = hours < 10 ? '0'+hours : hours;
                                minutes = minutes < 10 ? '0'+minutes : minutes;
                                title = hours + ':' + minutes;

                                text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='4' style='background-color:#1C84C6'>" + title + "</th></tr>" : "");
                                text += "<tr><th style='background-color:#1C84C6'></th>";
                                text += "<th style='background-color:#1C84C6'>Máximo</th>";
                                text += "<th style='background-color:#1C84C6'>Médio</th>";
                                text += "<th style='background-color:#1C84C6'>Mínimo</th></tr>";
                            }

                            // pega o indice
                            var indice = d[i].name.toString().substr(4, 2);

                            // verifica se eh dia da semana
                            if( indice < 8)
                            {
                                name = diasSemana[indice-1];
                                bgcolor = coresdiasSemana[indice-1];
                                valueMax = VMax[d[0].index+((indice-1)*26)].toFixed(2).replace('.', ',');
                                valueMed = VMed[d[0].index+((indice-1)*26)].toFixed(2).replace('.', ',');
                                valueMin = VMin[d[0].index+((indice-1)*26)].toFixed(2).replace('.', ',');

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                                text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                                text += "<td class='value'>" + valueMax + " " + UnidadeGrandeza + "</td>";
                                text += "<td class='value'>" + valueMed + " " + UnidadeGrandeza + "</td>";
                                text += "<td class='value'>" + valueMin + " " + UnidadeGrandeza + "</td>";
                                text += "</tr>";
                            }
                        }

                        return text + "</table>";
                    }
                }
            });

            // salva chart
            $(chart_name).data('c3-chart', chart);
        }

        $('#check_max').on("ifChecked",function(){

            // mostra maximo
            alteraDataSeries(1, true);

            // salva em cookie serie Max
            setCookie("Relat_SemanalMax", 1, null);
        });

        $('#check_max').on("ifUnchecked",function(){

            // apaga maximo
            alteraDataSeries(1, false);

            // salva em cookie serie Max
            setCookie("Relat_SemanalMax", 0, null);
        });

        $('#check_med').on("ifChecked",function(){

            // mostra medio
            alteraDataSeries(11, true);

            // salva em cookie serie Med
            setCookie("Relat_SemanalMed", 1, null);
        });

        $('#check_med').on("ifUnchecked",function(){

            // apaga medio
            alteraDataSeries(11, false);

            // salva em cookie serie Med
            setCookie("Relat_SemanalMed", 0, null);
        });

        $('#check_min').on("ifChecked",function(){

            // mostra minimo
            alteraDataSeries(21, true);

            // salva em cookie serie Min
            setCookie("Relat_SemanalMin", 1, null);
        });

        $('#check_min').on("ifUnchecked",function(){

            // apaga minimo
            alteraDataSeries(21, false);

            // salva em cookie serie Min
            setCookie("Relat_SemanalMin", 0, null);
        });

        $(".modal-dialog").draggable({
            handle: ".modal-editar-header"
        });
    });


    function getCookie(c_name) {

        // Get name followed by anything except a semicolon
        var cookiestring = RegExp("" + c_name + "[^;]+").exec(document.cookie);

        // Return everything after the equal sign, or an empty string if the cookie name not found
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./, "") : "");
    }

    function setCookie(c_name, value, expiresecs) {
        var exdate = new Date();
        exdate.setTime(exdate.getTime() + ((expiresecs) ? expiresecs * 1000 : 0));

        document.cookie = c_name + "=" + escape(value) + ((expiresecs == null) ? "" : ";expires=" + exdate.toGMTString()) + ";path=/";
    }


    function alteraDataSeries(serie, show) {

        // data series
        var chart_ea = $('#grafico-ea').data('c3-chart');
        var i;

        for(i=0;i<7;i++)
        {
            if( show )
            {
                // apresenta conforme cookie da semana
                alteraDiaSemana(i,false,serie);
            }
            else
            {
                // apaga, mas sem marcar no cookie da semana
                chart_ea.hide(["data" + (i+serie)]);
            }
        }
    }

    function todosDiaSemana() {

        // le e salva cookie serie Max
        var serie_max = getCookie("Relat_SemanalMax");
        if(serie_max == "") { serie_max = 1; }
        setCookie("Relat_SemanalMax", serie_max, null);

        // le e salva cookie serie Med
        var serie_med = getCookie("Relat_SemanalMed");
        if(serie_med == "") { serie_med = 1; }
        setCookie("Relat_SemanalMed", serie_med, null);

        // le e salva cookie serie Min
        var serie_min = getCookie("Relat_SemanalMin");
        if(serie_min == "") { serie_min = 1; }
        setCookie("Relat_SemanalMin", serie_min, null);

        // dias da semana
        var i;

        for(i=0;i<7;i++)
        {
            alteraDiaSemana(i,false,0);
        }
    }

    function alteraDiaSemana(dia_semana, alterar, serie) {

        // le cookie dia da semana
        var estado = getCookie(c_names[dia_semana]);
        if(estado == "") { estado = 1; }

        // le cookie serie Max
        var serie_max = getCookie("Relat_SemanalMax");
        document.getElementById("check_max").checked = ((serie_max==1) ? true : false);

        // le cookie serie Med
        var serie_med = getCookie("Relat_SemanalMed");
        document.getElementById("check_med").checked = ((serie_med==1) ? true : false);

        // le cookie serie Min
        var serie_min = getCookie("Relat_SemanalMin");
        document.getElementById("check_min").checked = ((serie_min==1) ? true : false);

        // verifica se quer alterar
        if( alterar )
        {
            // se estiver ligado, desliga
            estado = (estado == 0) ? 1 : 0;
        }

        // data series
        var chart_ea = $('#grafico-ea').data('c3-chart');

        // verifica estado
        if( estado == 1 )
        {
            if(serie == 0)
            {
                if( serie_max == 1 ) { chart_ea.show(["data" + (dia_semana+1)])  } else { chart_ea.hide(["data" + (dia_semana+1)])  };
                if( serie_med == 1 ) { chart_ea.show(["data" + (dia_semana+11)]) } else { chart_ea.hide(["data" + (dia_semana+11)]) };
                if( serie_min == 1 ) { chart_ea.show(["data" + (dia_semana+21)]) } else { chart_ea.hide(["data" + (dia_semana+21)]) };
            }
            else
            {
                chart_ea.show(["data" + (dia_semana+serie)]);
            }

            $(id_names[dia_semana]).css("background", coresdiasSemana[dia_semana]);
        }
        else
        {
            if(serie == 0)
            {
                chart_ea.hide(["data" + (dia_semana+1)]);
                chart_ea.hide(["data" + (dia_semana+11)]);
                chart_ea.hide(["data" + (dia_semana+21)]);
            }
            else
            {
                chart_ea.hide(["data" + (dia_semana+serie)]);
            }

            $(id_names[dia_semana]).css("background", "white");
        }

        // salva em cookie dia da semana
        setCookie(c_names[dia_semana], estado, null);
    }

</script>
