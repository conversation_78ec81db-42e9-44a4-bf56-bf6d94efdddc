﻿<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia</title>

    <link href="~/Content/bootstrap.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/style.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/plugins/c3/c3.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/Relatorios.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/RelatoriosPDF.css" rel="stylesheet" type="text/css" />
    <link href="~/fonts/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="~/Scripts/jquery-2.1.1.min.js" charset="utf-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/d3/d3.min.js" charset="utf-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/c3/c3.min.js"></script>

</head>

@if (ViewBag.Retorno == 2)
{
    <div class="row">
        <div class="col-lg-12">
            <div class="linha cabecalho">
                <div class="coluna1">
                    <p><img src="~/Logos/@ViewBag._LogoConsultor" style="height:42px; width:auto;" /></p>
                </div>

                <div class="coluna2">
                    <h3>@ViewBag.ClienteNome</h3>
                    @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                </div>

                <div class="coluna3">
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.UFER - @SmartEnergy.Resources.RelatoriosTexts.RelatDiario (@SmartEnergy.Resources.RelatoriosTexts.Simulacao)</h4>
                    <p>@ViewBag.DataAtual</p>
                </div>
            </div>
        </div>
    </div>

    <br /><br /><br /><br /><br /><br />
    <div class="row">
        <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
            <i class="fa fa-warning fa-5x"></i>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>@SmartEnergy.Resources.RelatoriosTexts.NaoExistemRegistros</h2>
        </div>
    </div>
    <br /><br /><br /><br />

}
else
{

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="~/Logos/@ViewBag._LogoConsultor" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.UFER - @SmartEnergy.Resources.RelatoriosTexts.RelatDiario (@SmartEnergy.Resources.RelatoriosTexts.Simulacao)</h4>
                        <p>@ViewBag.DataAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <br />

        @{
            if (ViewBag.showGrafico)
            {
                <div class="row">
                    <div class="relat-grafico" style="height:490px; width:800px; margin-left:50px;">
                        <div id="grafico-UFER"></div>
                        <div class='chart-legend' style="display:none;">
                            <div class='legend-scale-dem'>
                                <ul class='legend-labels' style="margin-top: -25px;">
                                    <li><span style='background:#1C84C6 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                                    <li><span style='background:#007f00 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                                    <li><span style='background:#ff0000 !important;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                                    <li class="simulacao"><span style='background:#00b1ff;'></span>@SmartEnergy.Resources.RelatoriosTexts.Simulacao</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }

        <hr />

        <div class="row" style="margin-top: -15px;">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:20%;"></th>
                                <th class="relat-capacitivo" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                                <th class="relat-preto" style="width:20%;">@SmartEnergy.Resources.SupervisaoTexts.Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.DashboardTexts.UFERTotalDia<br />&nbsp;</td>
                                <td class="relat-capacitivo">@ViewBag.UFER_Total_FPC_Sim @ViewBag.UnidadeUFER</td>
                                <td class="relat-fponta">@ViewBag.UFER_Total_FPI_Sim @ViewBag.UnidadeUFER</td>
                                <td class="relat-ponta">@ViewBag.UFER_Total_P_Sim @ViewBag.UnidadeUFER</td>
                                <td class="relat-preto">@ViewBag.UFER_Total_Sim @ViewBag.UnidadeUFER</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    @{
        if (ViewBag.showTabela)
        {
            <div class="page-break"></div>
            
            <div class="row">

                <br /><br />

                <div class="col-lg-12">
                    <div class="panel panel-default">
                        <table class="table no-margins tabela-grafico-pdf">
                            <thead>
                                <tr>
                                    <th style="width:20%; text-align:center; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Hora</th>
                                    <th style="width:26%; text-align:center; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Periodo</th>
                                    <th style="width:26%;">UFER<br /> (@ViewBag.UnidadeUFER)</th>
                                    <th style="width:26%; text-align:center; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.FatorPotencia</th>
                                </tr>
                            </thead>

                            <tbody>

                                @{
                                    var i = 0;
                                    var j = 0;
                                    var classe_sim = "relat-semreg";
                                    var periodo_sim = "---";
                                    var UFER_sim = "---";
                                    var fatpot_sim = "---";

                                    for (i = 0; i < 24; i++)
                                    {
                                        j = i + 1;

                                        classe_sim = "relat-semreg";
                                        periodo_sim = "---";
                                        UFER_sim = "---";
                                        fatpot_sim = "---";

                                        if (ViewBag.Periodo[j] == 0)
                                        {
                                            classe_sim = "relat-ponta";
                                            periodo_sim = "Ponta";
                                        }

                                        if (ViewBag.Periodo[j] == 1)
                                        {
                                            classe_sim = "relat-fponta";
                                            periodo_sim = "FPonta Ind";
                                        }

                                        if (ViewBag.Periodo[j] == 2)
                                        {
                                            classe_sim = "relat-capacitivo";
                                            periodo_sim = "FPonta Cap";
                                        }

                                        if (ViewBag.Periodo[j] != 3)
                                        {
                                            UFER_sim = string.Format("{0:#,##0.0}", ViewBag.UFER_Sim[j]);
                                            fatpot_sim = string.Format("{0:0.000}", ViewBag.FatPot_Sim[j]);
                                        }
                            
                                        <tr class="tabela_valores">
                                            <td style="text-align:center;" class="@classe_sim">@ViewBag.Horas[j]</td>
                                            <td style="text-align:center;" class="@classe_sim">@periodo_sim</td>
                                            <td style="text-align:center;" class="@classe_sim">@UFER_sim</td>
                                            <td style="text-align:center;" class="@classe_sim">@fatpot_sim</td>
                                        </tr>
                                    }
                                }

                            </tbody>
                        </table>
                    </div>


                </div>
            </div>
        }
    }

    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>

<script type="text/javascript">

    $(document).ready(function () {

        // UFER
        var dataX = [];
        var labelX = [];
        var dataUFER = [];
        var dataP = [];
        var dataFPI = [];
        var dataFPC = [];

        // UFER Simulacao
        var dataUFER_Sim = [];

        // copia valores
        var UFER = @Html.Raw(Json.Encode(@ViewBag.UFER));
        var FatPot = @Html.Raw(Json.Encode(@ViewBag.FatPot));
        var Periodo = @Html.Raw(Json.Encode(@ViewBag.Periodo));
        var Dias = @Html.Raw(Json.Encode(@ViewBag.Dias));

        // copia valores simulacao
        var UFER_Sim = @Html.Raw(Json.Encode(@ViewBag.UFER_Sim));
        var FatPot_Sim = @Html.Raw(Json.Encode(@ViewBag.FatPot_Sim));

        var maximoUFER = Math.ceil(@ViewBag.UFERMaxGrafico);

        var UnidadeUFER = @Html.Raw(Json.Encode(@ViewBag.UnidadeUFER));

        // verifico se nao tem dados e forco um limite
        if( maximoUFER == 1.0)
        {
            maximoUFER = 2.0;
        }

        if( maximoUFER == 0.0)
        {
            maximoUFER = 1.0;
        }

        dataX.push('x');
        dataUFER.push(['data1']);
        dataP.push(['data2']);
        dataFPI.push(['data3']);
        dataFPC.push(['data4']);

        // simulacao
        dataUFER_Sim.push(['data5']);

        for (i = 0; i < 26; i++)
        {
            // X
            dataX.push(Dias[i]);

            dataUFER.push([UFER[i]]);

            // simulacao
            dataUFER_Sim.push([UFER_Sim[i]]);

            if( Periodo[i] == 0 )
            {
                dataP.push([maximoUFER]);
                dataFPI.push([0]);
                dataFPC.push([0]);
            }
            else if( Periodo[i] == 1 )
            {
                dataP.push([0]);
                dataFPC.push([0]);
                dataFPI.push([maximoUFER]);
            }
            else if( Periodo[i] == 2 )
            {
                dataP.push([0]);
                dataFPI.push([0]);
                dataFPC.push([maximoUFER]);
            }
            else
            {
                dataP.push([0]);
                dataFPI.push([0]);
                dataFPC.push([0]);
            }
        }

        // valores label X
        labelX.push(Dias[1]);
        for (i = 3; i <= 24; i+=3)
        {
            labelX.push(Dias[i]);
        }

        var Data = [dataX,dataUFER,dataP,dataFPI,dataFPC,dataUFER_Sim];

        var chart = c3.generate({
            bindto: '#grafico-UFER',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                type:'bar',
                types: {
                    data2: 'area-step',
                    data3: 'area-step',
                    data4: 'area-step',
                    data5: 'step'
                },
                color: function (color, d) {
                    if (typeof d.index === 'undefined') { return color; }

                    if( Periodo[d.index] == 0 )
                        return '#FF0000';

                    if( Periodo[d.index] == 1 )
                        return '#007F00';

                    if( Periodo[d.index] == 2 )
                        return '#1C84C6';

                    return '#007F00';
                },
                colors: {
                    data2: '#FF0000',
                    data3: '#007F00',
                    data4: '#1C84C6',
                    data5: '#00b1ff'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*28
                },
                y:  {
                    max: maximoUFER,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoUFER < 100.0 )
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.25 },
            },
        });

        // labels
        $('.chart-legend').css("display", "block");

    });

</script>

}
    