﻿<div class="row">
    <div class="col-lg-4 col-md-12 relatorio_real">
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.FatCargaDia</h5>
                        <h1>@ViewBag.FatCargaFPC %</h1>
                        <h6>@ViewBag.DataAtual</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.FatCargaDia</h5>
                        <h1>@ViewBag.FatCargaFPI %</h1>
                        <h6>@ViewBag.DataAtual</h6>
                    </div>
                </div>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.FatCargaDia</h5>
                        <h1>@ViewBag.FatCargaP %</h1>
                        <h6>@ViewBag.DataAtual</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-12 simulacao" style="display:none;">
        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-capacitivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.FatCargaDia</h5>
                        <h1>@ViewBag.FatCargaFPC_Sim %</h1>
                        <h6>@ViewBag.DataAtual</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-indutivo">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.FatCargaDia</h5>
                        <h1>@ViewBag.FatCargaFPI_Sim %</h1>
                        <h6>@ViewBag.DataAtual</h6>
                    </div>
                </div>
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ponta">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.FatCargaDia</h5>
                        <h1>@ViewBag.FatCargaP_Sim %</h1>
                        <h6>@ViewBag.DataAtual</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-8 col-md-12">
        <div class="row">
            <div class="relat-grafico">
                <div id="grafico-fatcarga" style="margin-top: -10px"></div>
                <div class='chart-legend' style="display:none;">
                    <div class='legend-scale-dem'>
                        <ul class='legend-labels' style="margin-top: -25px;">
                            <li><span style='background:#1C84C6;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                            <li><span style='background:#007f00;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                            <li><span style='background:#ff0000;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                            <li class="simulacao" style="display: none;"><span style='background:#00b1ff;'></span>@SmartEnergy.Resources.RelatoriosTexts.Simulacao</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<br />
<div class="row">
    <div class="col-lg-offset-4 col-lg-8">
        <a id="BotaoEventos" data-toggle="modal" href="#modalEventos" class="btn btn-info pull-left" style="color:#ffffff; width:100%;">@SmartEnergy.Resources.RelatoriosTexts.Eventos</a>
    </div>
</div>

<br />
<div class="panel panel-title relatorio">
    <div class="panel-body">

        <div class="row">
            <div class="col-lg-12 relatorio_real">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatCargaDia<br />&nbsp;</td>
                                <td class="relat-capacitivo">@ViewBag.FatCargaFPC %</td>
                                <td class="relat-fponta">@ViewBag.FatCargaFPI %</td>
                                <td class="relat-ponta">@ViewBag.FatCargaP %</td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMedia<br />&nbsp;</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MedFPC kW</td>
                                <td class="relat-fponta">@ViewBag.Dem_MedFPI kW</td>
                                <td class="relat-ponta">@ViewBag.Dem_MedP kW</td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMaxima</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MaxFPC kW<br /><small>(@ViewBag.Dem_MaxFPC_DataHora)</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MaxFPI kW<br /><small>(@ViewBag.Dem_MaxFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MaxP kW<br /><small>(@ViewBag.Dem_MaxP_DataHora)</small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="col-lg-12 simulacao" style="display:none;">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatCargaDia<br />&nbsp;</td>
                                <td class="relat-capacitivo">@ViewBag.FatCargaFPC_Sim %</td>
                                <td class="relat-fponta">@ViewBag.FatCargaFPI_Sim %</td>
                                <td class="relat-ponta">@ViewBag.FatCargaP_Sim %</td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMedia<br />&nbsp;</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MedFPC_Sim kW</td>
                                <td class="relat-fponta">@ViewBag.Dem_MedFPI_Sim kW</td>
                                <td class="relat-ponta">@ViewBag.Dem_MedP_Sim kW</td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.SupervisaoTexts.DemandaMaxima</td>
                                <td class="relat-capacitivo">@ViewBag.Dem_MaxFPC_Sim kW<br /><small>(@ViewBag.Dem_MaxFPC_DataHora_Sim)</small></td>
                                <td class="relat-fponta">@ViewBag.Dem_MaxFPI_Sim kW<br /><small>(@ViewBag.Dem_MaxFPI_DataHora_Sim)</small></td>
                                <td class="relat-ponta">@ViewBag.Dem_MaxP_Sim kW<br /><small>(@ViewBag.Dem_MaxP_DataHora_Sim)</small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>


@Html.Partial("_EventosModal")

@Html.Partial("_ObservacoesModal")


<script type="text/javascript">

    $(document).ready(function () {

        // periodos
        var Periodos = [];

        Periodos.push('x');
        Periodos.push('Fora de Ponta (Capacitivo)');
        Periodos.push('Fora de Ponta (Indutivo)');
        Periodos.push('Ponta');

        // fator de carga
        var dataFatCarga = [];

        // simulacao
        var dataFatCarga_Sim = [];

        // copia valores
        var FatCarga = @Html.Raw(Json.Encode(@ViewBag.FatCarga));

        // copia valores simulacao
        var FatCarga_Sim = @Html.Raw(Json.Encode(@ViewBag.FatCarga_Sim));

        var FatCargaMaxGrafico = Math.ceil(@ViewBag.FatCargaMaxGrafico);

        // verifico se nao tem dados e forco um limite
        if( FatCargaMaxGrafico == 0.0)
        {
            FatCargaMaxGrafico = 100.0;
        }

        dataFatCarga.push(['data1']);
        dataFatCarga.push([FatCarga[2]]);
        dataFatCarga.push([FatCarga[1]]);
        dataFatCarga.push([FatCarga[0]]);

        // simulacao
        dataFatCarga_Sim.push(['data2']);
        dataFatCarga_Sim.push([FatCarga_Sim[2]]);
        dataFatCarga_Sim.push([FatCarga_Sim[1]]);
        dataFatCarga_Sim.push([FatCarga_Sim[0]]);

        var chart = c3.generate({

            bindto: '#grafico-fatcarga',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                columns: [Periodos,dataFatCarga,dataFatCarga_Sim],
                types: {
                    data1: 'bar',
                    data2: 'step'
                },
                color: function (color, d) {
                    if (typeof d.index === 'undefined') { return color; }

                    if( d.index == 2 )
                        return '#FF0000';

                    if( d.index == 1 )
                        return '#007F00';

                    if( d.index == 0 )
                        return '#1C84C6';

                    return '#007F00';
                },
                colors: {
                    data2: '#00b1ff',
                }
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type : 'categories',
                    tick: {
                        outer: false,
                        fit: true
                    }
                },
                y:  {
                    max: FatCargaMaxGrafico,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {
                            return d.toFixed(1);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.5 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;
                    
                    for (i = 0; i < d.length; i++) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        if (! text) {

                            title = FATOR_CARGA;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        
                        // verifica se barra
                        if (i == 0)
                        {
                            name = Periodos[d[i].index+1];
                            value = d[i].value;

                            if( d[i].index == 2 )
                                bgcolor = '#FF0000';

                            if( d[i].index == 1 )
                                bgcolor = '#007F00';

                            if( d[i].index == 0 )
                                bgcolor = '#1C84C6';
                        }
                        // caso simulacação
                        else
                        {
                            name = Periodos[d[i].index+1] + " - " + SIMULACAO;
                            value = d[i].value;

                            if( d[i].index == 2 )
                                bgcolor = '#00b1ff';

                            if( d[i].index == 1 )
                                bgcolor = '#00b1ff';

                            if( d[i].index == 0 )
                                bgcolor = '#00b1ff';
                        }


                        text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                        text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                        text += "<td class='value'>" + value.toFixed(1) + " %</td>";
                        text += "</tr>";
                    }
                    return text + "</table>";
                }
            }
        });

        // salva grafico fator de carga
        $('#grafico-fatcarga').data('c3-chart', chart);

        // labels
        $('.chart-legend').css("display", "block");

        ShowSimulacao();

        $(".modal-dialog").draggable({
            handle: ".modal-editar-header"
        });
    });

    function ShowSimulacao () {

        var IDSimulacaoCenario = parseInt(@ViewBag._IDSimulacaoCenario);

        // verifica se ano existe nenhuma simulacao selecionada
        if (IDSimulacaoCenario == 0)
        {
            $("#aplica_simula").attr('disabled', true);
            $('#aplica_simula').prop('checked', false);
        }
        else
        {
            $("#aplica_simula").attr('disabled', false);
        }

        // grafico fator de carga
        var chart = $('#grafico-fatcarga').data('c3-chart');

        // checkbox aplica simulacao
        var simula = $('#aplica_simula').prop('checked');

        if (simula)
        {
            // apresenta grafico simulacao
            chart.show(['data2']);

            $('.simulacao').css("display", "block");
            $('.relatorio_real').css("display", "none");
        }
        else
        {
            // apresenta grafico real
            chart.hide(['data2']);

            $('.simulacao').css("display", "none");
            $('.relatorio_real').css("display", "block");
        }

        // salva em cookie
        setCookie("AplicaSimulacao", simula, null);
    }

</script>
