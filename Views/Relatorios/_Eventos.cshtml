﻿@using SmartEnergyLib.SQL

@{
    List<EventosDescricao> eventos = ViewBag.listaEventosDescricao;
}

<div class="row" id="select2_tipo_2" style="display:none;">
    <div class="col-lg-12">
        <div class="panel panel-default">
            <div class="panel-body">

                <div>

                    @{
                        if (eventos != null)
                        {
                            if (eventos.Count >= 5000000)
                            {
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="widget style1 red-bg">
                                            <div class="row">
                                                <div class="col-xs-4">
                                                    <i class="fa fa-exclamation-triangle fa-5x"></i>
                                                </div>
                                                <div class="col-xs-8 text-right">
                                                    <h2 class="font-bold">@SmartEnergy.Resources.SupervisaoTexts.UltrapassouLimite</h2>
                                                    <br>
                                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.NaoApresentaEventos</h4>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <br />
                            }
                        }
                    }

                    <select class="select2_tipo_2 form-control" multiple="multiple">

                        @{
                            string icone_tipo = "fa-th-list";
                            int tipo = 0;

                            for (var i = 1; i < ViewBag.NumTiposEventos; i++)
                            {
                                icone_tipo = "fa-th-list";
                                tipo = ViewBag.listaTiposEventosDescricao[i].Tipo;

                                switch (tipo)
                                {
                                    case 0:
                                        icone_tipo = "fa-th-list";
                                        break;

                                    case 1:
                                        icone_tipo = "fa-bell";
                                        break;

                                    case 2:
                                        icone_tipo = "fa-gear";
                                        break;

                                    case 3:
                                        icone_tipo = "fa-power-off";
                                        break;

                                    case 4:
                                        icone_tipo = "fa-retweet";
                                        break;

                                    case 5:
                                        icone_tipo = "fa-sitemap";
                                        break;

                                    case 6:
                                        icone_tipo = "fa-sign-in";
                                        break;

                                    case 7:
                                        icone_tipo = "fa-sign-out";
                                        break;

                                    case 8:
                                        icone_tipo = "fa-upload";
                                        break;

                                    case 9:
                                        icone_tipo = "fa-clock-o";
                                        break;

                                    case 10:
                                        icone_tipo = "fa-user";
                                        break;
                                }

                                <option value=@tipo data-icon=@icone_tipo>&nbsp;&nbsp;@ViewBag.listaTiposEventosDescricao[i].Descricao</option>
                            }
                        }

                    </select>
                </div>

                <br /><br />

                <table id="dataTables-eventos" class="table table-striped table-bordered table-hover dataTables-eventos">
                    <thead>
                        <tr>
                            <th>@SmartEnergy.Resources.RelatoriosTexts.Hora</th>
                            <th>@SmartEnergy.Resources.RelatoriosTexts.Tipo</th>
                            <th style="text-align:center;"><i class="fa fa-tags" style="font-size:20px;"></i></th>
                            <th>@SmartEnergy.Resources.RelatoriosTexts.Evento</th>
                        </tr>
                    </thead>
                    <tbody>

                        @{
                            if( eventos != null )
                            {
                                foreach (EventosDescricao evento in eventos)
                                {
                                    icone_tipo = "fa-th-list";
                                    tipo = evento.Tipo;

                                    switch (tipo)
                                    {
                                        case 0:
                                            icone_tipo = "fa-th-list";
                                            break;

                                        case 1:
                                            icone_tipo = "fa-bell";
                                            break;

                                        case 2:
                                            icone_tipo = "fa-gear";
                                            break;

                                        case 3:
                                            icone_tipo = "fa-power-off";
                                            break;

                                        case 4:
                                            icone_tipo = "fa-retweet";
                                            break;

                                        case 5:
                                            icone_tipo = "fa-sitemap";
                                            break;

                                        case 6:
                                            icone_tipo = "fa-sign-in";
                                            break;

                                        case 7:
                                            icone_tipo = "fa-sign-out";
                                            break;

                                        case 8:
                                            icone_tipo = "fa-upload";
                                            break;

                                        case 9:
                                            icone_tipo = "fa-clock-o";
                                            break;

                                        case 10:
                                            icone_tipo = "fa-user";
                                            break;
                                    }

                                    <tr>
                                        <td><span style="display:none;">@evento.DataHora_Sort</span>@evento.DataHora</td>
                                        <td>@evento.Tipo</td>
                                        <td style="text-align:center;"><i class="fa @icone_tipo" style="font-size:20px;"></i></td>
                                        <td>@evento.Descricao</td>
                                    </tr>
                                }
                            }
                        }

                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">

    $(document).ready(function () {

        var table = $('.dataTables-eventos').DataTable({
            "iDisplayLength": 12,
            dom: 'ftp',
            "aoColumnDefs": [
                        {
                            "aTargets": [0],
                            "bVisible": true,
                            "bSortable": true,
                            "bSearchable": true,
                        },
                        {
                            "aTargets": [1],
                            "bVisible": false,
                            "bSortable": true,
                            "bSearchable": true,
                        },
                        {
                            "aTargets": [2],
                            "bVisible": true,
                            "bSortable": false,
                            "bSearchable": false,
                        },
                        {
                            "aTargets": [3],
                            "bVisible": true,
                            "bSortable": true,
                            "bSearchable": true,
                        },
            ],
            "oSearch": {
                "bSmart": false, 
                "bRegex": true,
                "sSearch": ""                
            },
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "25%" },
            { sWidth: "5%" },
            { sWidth: "5%" },
            { sWidth: "70%" }
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

        // cookie de search
        var search = decodeURIComponent(getCookie("Relat_Search"));
        $('.dataTables-eventos').DataTable().search(search).draw();

        // cookie de sort
        var sortedCol = getCookie("Relat_SortedCol");
        if (sortedCol == "") sortedCol = 0;
        var sortedDir = getCookie("Relat_SortedDir");

        if (sortedCol == 0 || sortedCol == 3)
        {
            $('#dataTables-eventos').dataTable().fnSort([[sortedCol, sortedDir]]);
        }

        // trigger datatable
        $('.dataTables-eventos')
                .on('order.dt', function () { EventoOrder(); })
                .on('search.dt', function () { EventoSearch(); })
                .DataTable();


        function formatIcon(icon) {

            if (!icon.id) {
                return icon.text;
            }
            var originalOption = icon.element;
            var $icon = $('  <i style="font-size:16px;" class="fa ' + $(originalOption).data('icon') + '"></i><span>' + icon.text + '</span>');

            return $icon;

        };

        $(".select2_tipo").select2({
            minimumResultsForSearch: Infinity,
            width: "100%",
            templateResult: formatIcon,
            templateSelection: formatIcon
        });

        var placeholder = SELECIONE_EVENTO_FILTRO;

        $(".select2_tipo_2").select2({
            placeholder: placeholder,
            minimumResultsForSearch: Infinity,
            width: "100%",
            templateResult: formatIcon,
            templateSelection: formatIcon,
            allowClear: true,
            closeOnSelect: true,
            escapeMarkup: function (m) {
                return m;
            }
        })
        .on("select2:select", function (e) {
            AdicionouTipo();
        })
        .on("select2:unselect", function (e) {
            RemoveuTipo();
        })
        .on("select2:unselecting", function (e) {
            $(this).data('state', 'unselected');
        })
        .on("select2:open", function (e) {
            if ($(this).data('state') === 'unselected') {
                $(this).removeData('state');
                var self = $(this);
                setTimeout(function () {
                    self.select2('close');
                }, 1);
            }
        });

        // cookie de filtro
        var lista_selecao = getCookie("Relat_TipoEvento");

        // troco / por |
        var str = lista_selecao.replace(/[\/\/]/g, "|");

        // setar selecao na lista
        var lista = lista_selecao.split("/");

        // procura na tabela
        $('.dataTables-eventos').DataTable().column(1).search(str, true, false).draw();

        // labels
        $('#select2_tipo_2').css("display", "block");

        // aplica filtros
        $(".select2_tipo_2").select2("val", lista);
    });

    function getCookie(c_name) {

        // Get name followed by anything except a semicolon
        var cookiestring = RegExp("" + c_name + "[^;]+").exec(document.cookie);

        // Return everything after the equal sign, or an empty string if the cookie name not found
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./, "") : "");
    }

    function setCookie(c_name, value, expiresecs) {
        var exdate = new Date();
        exdate.setTime(exdate.getTime() + ((expiresecs) ? expiresecs * 1000 : 0));

        document.cookie = c_name + "=" + escape(value) + ((expiresecs == null) ? "" : ";expires=" + exdate.toGMTString()) + ";path=/";
    }



    function Insere(lista_selecao, valor) {

        if (lista_selecao != "") {
            lista_selecao = lista_selecao + "|" + valor;
        }
        else {
            lista_selecao = lista_selecao + valor;
        }

        return (lista_selecao);
    }

    function ProcurarTipo() {

        // selecionados
        var selecionados = $('.select2_tipo_2').val();

        var lista_selecao = "";

        if (selecionados != null)
        {
            // percorre selecionados
            for (i = 0; i < selecionados.length; i++)
            {
                lista_selecao = Insere(lista_selecao, selecionados[i]);
            }
        }

        // troco | por /
        var str = lista_selecao.replace(/[\|\/]/g, "/");

        // salva em cookie
        setCookie("Relat_TipoEvento", str, null);

        // procura na tabela
        $('.dataTables-eventos').DataTable().column(1).search(lista_selecao, true, false).draw();
    }

    function RemoveuTipo() {

        ProcurarTipo();

        // fecha list
        $('.select2_tipo_2').select2("close");
    }

    function AdicionouTipo() {

        ProcurarTipo();
    }

    function EventoOrder() {

        // pega coluna de sort
        var sortedCol = $('#dataTables-eventos').dataTable().fnSettings().aaSorting[0][0];

        // salva em cookie
        setCookie("Relat_SortedCol", sortedCol, null);

        // pega direcao de sort
        var sortedDir = $('#dataTables-eventos').dataTable().fnSettings().aaSorting[0][1];

        // salva em cookie
        setCookie("Relat_SortedDir", sortedDir, null);
    }

    function EventoSearch() {

        // pega palavra search
        var value = $('.dataTables_filter input').val();

        // caso for suficiente, salvo no cookie
        if (value.length > 3 || value.length == 0) {

            // salva em cookie
            setCookie("Relat_Search", encodeURIComponent(value), null);
        }
    }

</script>
