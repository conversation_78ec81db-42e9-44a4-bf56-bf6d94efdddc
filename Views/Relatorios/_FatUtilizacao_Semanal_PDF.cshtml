﻿<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao</title>

    <link href="@Server.MapPath("~/Content/bootstrap.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/style.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/plugins/c3/c3.min.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/Relatorios.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/Content/RelatoriosPDF.css")" rel="stylesheet" type="text/css" />
    <link href="@Server.MapPath("~/fonts/font-awesome/css/font-awesome.min.css")" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="@Server.MapPath("~/Scripts/jquery-2.1.1.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/d3/d3.min.js")" charset="utf-8"></script>
    <script type="text/javascript" src="@Server.MapPath("~/Scripts/plugins/c3/c3.min.js")"></script>

</head>

@{
    string arquivo = "~/Logos/" + ViewBag._LogoConsultor;
    string logo = Server.MapPath(arquivo);
}

@if (ViewBag.Retorno == 2)
{
    <div class="row">
        <div class="col-lg-12">
            <div class="linha cabecalho">
                <div class="coluna1">
                    <p><img src="@logo" style="height:42px; width:auto;" /></p>
                </div>

                <div class="coluna2">
                    <h3>@ViewBag.ClienteNome</h3>
                    @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                </div>

                <div class="coluna3">
                    <h4>@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao - @SmartEnergy.Resources.RelatoriosTexts.RelatSemanal</h4>
                    <p>@ViewBag.DataTextoAtual</p>
                </div>
            </div>
        </div>
    </div>

    <br /><br /><br /><br /><br /><br />
    <div class="row">
        <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
            <i class="fa fa-warning fa-5x"></i>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>@SmartEnergy.Resources.RelatoriosTexts.NaoExistemRegistros</h2>
        </div>
    </div>
    <br /><br /><br /><br />

}
else
{

<body>

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">

        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho">
                    <div class="coluna1">
                        <p><img src="@logo" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                        @ViewBag.GrupoNome | @ViewBag.UnidadeNome | <b>@ViewBag.MedicaoNome</b>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao - @SmartEnergy.Resources.RelatoriosTexts.RelatSemanal</h4>
                        <p>@ViewBag.DataTextoAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <br />

        @{
            if (ViewBag.showGrafico)
            {
                <br /><br />
                <div class="row">
                    <div class="relat-grafico" style="height:450px; width:800px; margin-left:50px;">
                        <div id="grafico-fatutilizacao" style="margin-top: -10px"></div>
                        <div class='chart-legend' style="display:none;">
                            <div class='legend-scale-dem'>
                                <ul class='legend-labels' style="margin-top: -25px;">
                                    <li><span style='background:#1C84C6 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                                    <li><span style='background:#007f00 !important;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                                    <li><span style='background:#ff0000 !important;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }

        <hr />

        <div class="row" style="margin-top: -15px;">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-capacitivo" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo</th>
                                <th class="relat-fponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo</th>
                                <th class="relat-ponta" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao @SmartEnergy.Resources.RelatoriosTexts.Maximo</td>
                                <td class="relat-capacitivo">@ViewBag.FatUtilizacao_MaxFPC %<br /><small>(@ViewBag.FatUtilizacao_MaxFPC_DataHora)</small></td>
                                <td class="relat-fponta">@ViewBag.FatUtilizacao_MaxFPI %<br /><small>(@ViewBag.FatUtilizacao_MaxFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.FatUtilizacao_MaxP %<br /><small>(@ViewBag.FatUtilizacao_MaxP_DataHora)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao @SmartEnergy.Resources.RelatoriosTexts.Minimo</td>
                                <td class="relat-capacitivo">@ViewBag.FatUtilizacao_MinFPC %<br /><small>(@ViewBag.FatUtilizacao_MinFPC_DataHora)</small></td>
                                <td class="relat-fponta">@ViewBag.FatUtilizacao_MinFPI %<br /><small>(@ViewBag.FatUtilizacao_MinFPI_DataHora)</small></td>
                                <td class="relat-ponta">@ViewBag.FatUtilizacao_MinP %<br /><small>(@ViewBag.FatUtilizacao_MinP_DataHora)</small></td>
                            </tr>
                            <tr>
                                <td class="relat-preto">@SmartEnergy.Resources.RelatoriosTexts.FatorUtilizacao @SmartEnergy.Resources.RelatoriosTexts.Periodo</td>
                                <td class="relat-capacitivo">@ViewBag.FatUtilizacao_MedFPC %<br /><small>&nbsp;</small></td>
                                <td class="relat-fponta">@ViewBag.FatUtilizacao_MedFPI %<br /><small>&nbsp;</small></td>
                                <td class="relat-ponta">@ViewBag.FatUtilizacao_MedP %</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    @{
        if (ViewBag.showTabela)
        {
            <div class="page-break"></div>
            
            <div class="row">

                <br /><br />

                <div class="col-lg-12">
                    <div class="panel panel-default">
                        <table class="table no-margins tabela-grafico-pdf">
                            <thead>
                                <tr>
                                    <th style="width:25%; text-align:center; font-size:12px;">@SmartEnergy.Resources.RelatoriosTexts.Semana</th>
                                    <th style="width:25%; text-align:center; font-size:12px;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Capacitivo<br />(%)</th>
                                    <th style="width:25%; text-align:center; font-size:12px;">@SmartEnergy.Resources.SupervisaoTexts.FPonta @SmartEnergy.Resources.SupervisaoTexts.Indutivo<br />(%)</th>
                                    <th style="width:25%; text-align:center; font-size:12px;">@SmartEnergy.Resources.SupervisaoTexts.Ponta<br />(%)</th>
                                </tr>
                            </thead>

                            <tbody>

                                @{
                                    var i = 0;
                                    var j = 0;
                                    var FatUtilizacaoFPC = "---";
                                    var FatUtilizacaoFPI = "---";
                                    var FatUtilizacaoP = "---";

                                    var dia_semana = new string[] { SmartEnergy.Resources.ComumTexts.domingo, 
                                                             SmartEnergy.Resources.ComumTexts.segunda,
                                                             SmartEnergy.Resources.ComumTexts.terca,
                                                             SmartEnergy.Resources.ComumTexts.quarta,
                                                             SmartEnergy.Resources.ComumTexts.quinta,
                                                             SmartEnergy.Resources.ComumTexts.sexta,
                                                             SmartEnergy.Resources.ComumTexts.sabado
                                                           };

                                    for (i = 0; i < 7; i++)
                                    {
                                        j = i + 1;

                                        FatUtilizacaoFPC = string.Format("{0:#,##0.0}", ViewBag.FatUtilizacaoFPC[j]);
                                        FatUtilizacaoFPI = string.Format("{0:#,##0.0}", ViewBag.FatUtilizacaoFPI[j]);
                                        FatUtilizacaoP = string.Format("{0:#,##0.0}", ViewBag.FatUtilizacaoP[j]);

                                        <tr class="tabela_valores" style="font-size:12px;">
                                            <td style="text-align:center; font-size:12px;" class="relat-preto">@dia_semana[i]<br />(@ViewBag.Dias[j])</td>
                                            <td style="text-align:center; font-size:12px;" class="relat-capacitivo">@FatUtilizacaoFPC</td>
                                            <td style="text-align:center; font-size:12px;" class="relat-fponta">@FatUtilizacaoFPI</td>
                                            <td style="text-align:center; font-size:12px;" class="relat-ponta">@FatUtilizacaoP</td>
                                        </tr>
                                    }
                                }

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }
    }

    <div class="row">
        <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>

<script type="text/javascript">

    $(document).ready(function () {

        // dias da semana
        var diasSemana = [];

        diasSemana.push('x');
        diasSemana.push('Domingo');
        diasSemana.push('Segunda');
        diasSemana.push('Terca');
        diasSemana.push('Quarta');
        diasSemana.push('Quinta');
        diasSemana.push('Sexta');
        diasSemana.push('Sabado');

        // fator de utilizacao
        var dataFatUtilizacaoFPC = [];
        var dataFatUtilizacaoFPI = [];
        var dataFatUtilizacaoP = [];

        // copia valores
        var FatUtilizacaoFPC = @Html.Raw(Json.Encode(@ViewBag.FatUtilizacaoFPC));
        var FatUtilizacaoFPI = @Html.Raw(Json.Encode(@ViewBag.FatUtilizacaoFPI));
        var FatUtilizacaoP = @Html.Raw(Json.Encode(@ViewBag.FatUtilizacaoP));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));

        var maximoFatUtilizacao = Math.ceil(@ViewBag.FatUtilizacaoMaxGrafico);

        // verifico se nao tem dados e forco um limite
        if( maximoFatUtilizacao == 0.0)
        {
            maximoFatUtilizacao = 100.0;
        }

        dataFatUtilizacaoFPC.push(['data1']);
        dataFatUtilizacaoFPI.push(['data2']);
        dataFatUtilizacaoP.push(['data3']);

        for (i = 1; i < 8; i++)
        {
            dataFatUtilizacaoFPC.push([FatUtilizacaoFPC[i]]);
            dataFatUtilizacaoFPI.push([FatUtilizacaoFPI[i]]);
            dataFatUtilizacaoP.push([FatUtilizacaoP[i]]);
        }

        var Data = [diasSemana, dataFatUtilizacaoFPC,dataFatUtilizacaoFPI,dataFatUtilizacaoP];

        c3.generate({

            bindto: '#grafico-fatutilizacao',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'bar',
                    data3: 'bar',
                },
                order: null,
                colors: {
                    data1: '#1C84C6',
                    data2: '#007F00',
                    data3: '#FF0000'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type : 'categories',
                    tick: {
                        outer: false,
                        fit: true
                    }
                },
                y:  {
                    max: maximoFatUtilizacao,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoFatUtilizacao < 10.0 )
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.7 },
            },
        });

        // labels
        $('.chart-legend').css("display", "block");

    });

</script>

}
    