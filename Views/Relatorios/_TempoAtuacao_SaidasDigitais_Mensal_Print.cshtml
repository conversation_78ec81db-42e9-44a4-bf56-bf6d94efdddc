﻿@using SmartEnergyLib.SQL

<head>

    <title>@SmartEnergy.Resources.RelatoriosTexts.TempoAtuacao</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

    <link href="~/fonts/font-awesome/css/font-awesome.min.css" rel="stylesheet" />
    <link href="~/fonts/elusive-icons-2.0.0/css/elusive-icons.min.css" rel="stylesheet" />
    <link href="~/Content/bootstrap.min.css" rel="stylesheet" />
    <link href="~/Content/style.css" rel="stylesheet" />
    <link href="~/Content/Relatorios.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/RelatoriosPDF.css" rel="stylesheet" type="text/css" />

    <script type="text/javascript" src="~/Scripts/jquery-2.1.1.min.js" charset="utf-8"></script>
    <script type="text/javascript" src="~/Scripts/plugins/dataTables/datatables.min.js" charset="utf-8"></script>

    @{
        
        int tipo_arquivo = ViewBag.TipoArquivo;

        if (tipo_arquivo == 1)
        {
            <style>
                th {
                    font-size: 9px;
                }

                td {
                    font-size: 8px;
                }

                tr {
                    padding: 1px;
                    page-break-inside: avoid;
                }
            </style>
        }
        // altera tamanho da fonte para o pdf do email
        else
        {
            <style>
                th {
                    font-size: 7px;
                }

                td {
                    font-size: 6px;
                }

                tr {
                    padding: 1px;
                    page-break-inside: avoid;
                }
            </style>
        }
}

</head>

<body>

@{
    List<TempoAtuacaoMensal> temposAtuacao = ViewBag.temposAtuacao;

    int NumDias = 0;

    if (ViewBag.NumDias != null)
    {
        NumDias = ViewBag.NumDias;
    }
}

    <div class="wrapper wrapper-content" style="background-color:#ffffff;">
        <div class="row">
            <div class="col-lg-12">
                <div class="linha cabecalho" style="height:100px !important;">
                    <div class="coluna1">
                        <p><img src="~/Logos/@ViewBag._LogoConsultor" style="height:42px; width:auto;" /></p>
                    </div>

                    <div class="coluna2">
                        <h3>@ViewBag.ClienteNome</h3>
                    </div>

                    <div class="coluna3">
                        <h4>@SmartEnergy.Resources.RelatoriosTexts.TempoAtuacao</h4>
                        <p>@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoSaidasDigitais</p>
                        <p>@ViewBag.DataTextoAtual</p>
                    </div>
                </div>
            </div>
        </div>

        <hr />

        <div class="row" style="margin-top:-40px;">
            <div class="col-lg-12">
                <div class="panel">
                    <table id="dataTables-tempoatuacao-1" class="table no-margins dataTables-tempoatuacao-1">
                        <thead>
                            <tr>
                                <th>Gateways</th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.SaidasDigitais</th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.Descricao</th>

                                @{
                                    for (int i = 0; i < 31; i++)
                                    {
                                        string texto_dia = "";

                                        if (i < NumDias)
                                        {
                                            texto_dia = string.Format("{0}", i + 1);
                                        }

                                        <th>@texto_dia</th>
                                    }
                                }
                            </tr>
                        </thead>
                        <tbody>

                            @{
                                if (temposAtuacao != null)
                                {
                                    foreach (TempoAtuacaoMensal tempoAtuacao in temposAtuacao)
                                    {
                                        <tr>
                                            <td>@tempoAtuacao.Nome</td>
                                            <td>@tempoAtuacao.DescrSaida</td>
                                            <td>
                                                <p class="relat-medio">Ligado (HH:mm:ss)</p>
                                                <p>Desligado (HH:mm:ss)</p>
                                            </td>

                                            @for (int i = 0; i < 31; i++)
                                            {
                                                string ligado = "";
                                                string desligado = "";

                                                if (i < tempoAtuacao.NumDias)
                                                {
                                                    int ligado_hora = 0;
                                                    int desligado_hora = 0;

                                                    int ligado_min = 0;
                                                    int desligado_min = 0;

                                                    int ligado_seg = 0;
                                                    int desligado_seg = 0;

                                                    // converte segundos
                                                    ligado_seg = (int)(@tempoAtuacao.TempoAtuacaoMensalValor[i].tempoLigado % 60);
                                                    desligado_seg = (int)(@tempoAtuacao.TempoAtuacaoMensalValor[i].tempoDesligado % 60);

                                                    // converte minutos
                                                    ligado_min = (int)(((@tempoAtuacao.TempoAtuacaoMensalValor[i].tempoLigado - ligado_seg) / 60) % 60);
                                                    desligado_min = (int)(((@tempoAtuacao.TempoAtuacaoMensalValor[i].tempoDesligado - desligado_seg) / 60) % 60);

                                                    // converte horas
                                                    ligado_hora = (int)(((@tempoAtuacao.TempoAtuacaoMensalValor[i].tempoLigado - ligado_min) / 60) / 60);
                                                    desligado_hora = (int)(((@tempoAtuacao.TempoAtuacaoMensalValor[i].tempoDesligado - desligado_min) / 60) / 60);

                                                    // cria string (HH:mm:ss)
                                                    ligado = string.Format("{0:00}:{1:00}:{2:00}", ligado_hora, ligado_min, ligado_seg);
                                                    desligado = string.Format("{0:00}:{1:00}:{2:00}", desligado_hora, desligado_min, desligado_seg);
                                                }

                                                <td>
                                                    <p class="relat-medio">@ligado</p>
                                                    <br />
                                                    <p>@desligado</p>
                                                </td>
                                            }
                                        </tr>
                                    }
                                }
                            }

                        </tbody>
                    </table>

                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12" style="text-align:center;">
            <h2>&nbsp;</h2>
        </div>
    </div>

</body>


<script type="text/javascript">

    $(document).ready(function () {

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {
                var x = (a == "-") ? 0 : a.replace(/,/, ".");
                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });


        $('.dataTables-tempoatuacao-1').DataTable({
            "bAutoWidth": false,
            "iDisplayLength": 10000,
            dom: 't',
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "portugues", "bSortable": true, "bSearchable": true },
                        { "aTargets": [1], "sType": "portugues", "bSortable": true, "bSearchable": true },
                        { "aTargets": [2], "sType": "portugues", "bSortable": true, "bSearchable": true },
                        { "aTargets": [3], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [4], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [5], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [6], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [7], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [8], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [9], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [10], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [11], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [12], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [13], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [14], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [15], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [16], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [17], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [18], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [19], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [20], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [21], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [22], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [23], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [24], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [25], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [26], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [27], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [28], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [29], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [30], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [31], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [32], "sType": "numero", "bSortable": false, "bSearchable": false },
                        { "aTargets": [33], "sType": "numero", "bSortable": false, "bSearchable": false },
            ],
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "16%" },
            { sWidth: "11%" },
            { sWidth: "11%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" },
            { sWidth: "2%" }
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });
    })

</script>
