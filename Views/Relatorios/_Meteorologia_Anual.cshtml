﻿<div class="row">
    <div class="col-lg-4 col-md-12">
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ea">
                        <h4>@ViewBag.NomeGrandeza</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.MediaAno</h5>
                        <h1>@ViewBag.VMedAno <span>@ViewBag.UnidadeGrandeza</span></h1>
                        <h6>@ViewBag.DataTextoAtual</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-preto">
                        <h4>@ViewBag.NomeGrandeza</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.Maxima</h5>
                        <h1>@ViewBag.VMaxAno <span>@ViewBag.UnidadeGrandeza</span></h1>
                        <h6>@ViewBag.VMaxAno_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
        <br /><br />
        <div class="row">
            <div class="col-lg-offset-6 col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-preto">
                        <h4>@ViewBag.NomeGrandeza</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.Minima</h5>
                        <h1>@ViewBag.VMinAno <span>@ViewBag.UnidadeGrandeza</span></h1>
                        <h6>@ViewBag.VMinAno_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-8 col-md-12">
        <div class="row">
            <div class="relat-grafico">
                <div id="grafico-ea" style="margin-top: -10px"></div>
                <div class='chart-legend' style="display:none;">
                    <div class='legend-scale-dem'>
                        <ul class='legend-labels' style="margin-top: -25px;">
                            <li><span style='background:#1C84C6;'></span>@ViewBag.NomeGrandeza</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@Html.Partial("_Relat_Botoes")

<br />
<div class="panel panel-title relatorio">
    <div class="panel-body">

        <div class="row">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-ea" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.MediaAno</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Maxima</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Minima</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@ViewBag.NomeGrandeza</td>
                                <td class="relat-ea">@ViewBag.VMedAno @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.DataTextoAtual)</small></td>
                                <td class="relat-preto">@ViewBag.VMaxAno @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMaxAno_DataHora)</small></td>
                                <td class="relat-preto">@ViewBag.VMinAno @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMinAno_DataHora)</small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal inmodal animated fadeIn" id="modalTabela" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-editar-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">@SmartEnergy.Resources.ComumTexts.BotaoFechar</span></button>
                <h4 class="modal-title"><i class="fa fa-list"></i>&nbsp;&nbsp;@SmartEnergy.Resources.RelatoriosTexts.TabelaRegistros</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-default">
                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Mes</th>
                                <th style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Media<br />(@ViewBag.UnidadeGrandeza)</th>
                                <th style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Maxima<br />(@ViewBag.UnidadeGrandeza)</th>
                                <th style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Minima<br />(@ViewBag.UnidadeGrandeza)</th>
                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var i = 0;
                                var j = 0;
                                var tempMed = "---";
                                var tempMax = "---";
                                var tempMin = "---";

                                for (i = 0; i < 12; i++)
                                {
                                    j = i + 1;
                                    
                                    if (ViewBag.PossuiRegistros[j])
                                    {
                                        tempMed = string.Format("{0:0}", ViewBag.TempMed[j]);
                                        tempMax = string.Format("{0:0}", ViewBag.TempMax[j]);
                                        tempMin = string.Format("{0:0}", ViewBag.TempMin[j]);
                                    }
                                    else
                                    {
                                        tempMed = "---";
                                        tempMax = "---";
                                        tempMin = "---";
                                    }

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="relat-ea">@ViewBag.Meses[j]</td>
                                        <td style="text-align:center;" class="relat-ea">@tempMed</td>
                                        <td style="text-align:center;" class="relat-ea">@tempMax</td>
                                        <td style="text-align:center;" class="relat-ea">@tempMin</td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
            </div>
        </div>
    </div>
</div>


@Html.Partial("_EventosModal")

@Html.Partial("_ObservacoesModal")


<script type="text/javascript">

    $(document).ready(function () {

        // meteorologia
        var dataX = [];
        var labelX = [];
        var dataMaxima = [];
        var dataMedia = [];
        var dataMinima = [];

        // copia valores
        var TempMax = @Html.Raw(Json.Encode(@ViewBag.TempMax));
        var TempMed = @Html.Raw(Json.Encode(@ViewBag.TempMed));
        var TempMin = @Html.Raw(Json.Encode(@ViewBag.TempMin));
        var PossuiRegistros = @Html.Raw(Json.Encode(@ViewBag.PossuiRegistros));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));
        var Meses = @Html.Raw(Json.Encode(@ViewBag.Meses));
        var NomeGrandeza = @Html.Raw(Json.Encode(@ViewBag.NomeGrandeza));
        var UnidadeGrandeza = @Html.Raw(Json.Encode(@ViewBag.UnidadeGrandeza));

        // valor minimo e máximo
        var minimoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMin));
        var maximoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMax));

        dataX.push('x');
        dataMaxima.push(['data1']);
        dataMedia.push(['data2']);
        dataMinima.push(['data3']);

        for (i = 0; i < 14; i++)
        {
            // X
            dataX.push(Datas[i]);

            dataMaxima.push([TempMax[i]]);
            dataMedia.push([TempMed[i]]);
            dataMinima.push([TempMin[i]]);
        }

        // valores label X
        for (i = 1; i <= 12; i++)
        {
            labelX.push(Datas[i]);
        }

        var Data = [dataX,dataMinima,dataMedia,dataMaxima];

        c3.generate({

            bindto: '#grafico-ea',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'bar',
                    data3: 'bar',
                },
                colors: {
                    data1: '#00007f',
                    data2: '#4284B2',
                    data3: '#D7E8F8',
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // mes
                            var mes = x.getMonth()+1;

                            // verifica se deve mostrar label
                            if(mes==1 || mes==4 || mes==7 || mes==10 || mes==12 )
                                return Meses[mes];

                            return "";
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    // Grafico 1 dia     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(11 horas)
                    // Grafico 1 mes     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(24 horas)*(13 dias)
                    padding: -1000*60*60*24*13
                },
                y:  {
                    min: minimoValor,
                    max: maximoValor,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {
                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.8 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < d.length; i++) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        if (! text) {

                            // split string and create array.
                            var arr = Datas[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            title = Meses[data.getMonth()+1];

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1C84C6'>" + title + "</th></tr>" : "");
                        }

                        switch(i)
                        {
                            case 0:     // minima
                                name = MINIMO;
                                break;

                            case 1:     // media
                                name = MEDIO;
                                break;

                            case 2:     // maxima
                                name = MAXIMO;
                                break;
                        }

                        value = d[i].value;
                        var str_valor = "---";

                        if (PossuiRegistros[d[i].index] == true)
                        {
                            str_valor = value.toFixed(0) + " " + UnidadeGrandeza;
                        }

                        bgcolor = $$.levelColor ? $$.levelColor(d[i].value) : color(d[i].id);

                        text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                        text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                        text += "<td class='value'>" + str_valor +"</td>";
                        text += "</tr>";
                    }
                    return text + "</table>";
                }
            }
        });

        // labels
        $('.chart-legend').css("display", "block");

        $(".modal-dialog").draggable({
            handle: ".modal-editar-header"
        });
    });

</script>
