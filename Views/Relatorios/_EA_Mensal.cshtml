﻿<div class="row">
    <div class="col-lg-4 col-md-12">
        <div class="row">
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-ea">
                        <h4>@ViewBag.NomeGrandeza</h4>
                        <h5>@SmartEnergy.Resources.RelatoriosTexts.MedioMes</h5>
                        <h1>@ViewBag.VMedMes <span>@ViewBag.UnidadeGrandeza</span></h1>
                        <h6>@ViewBag.DataTextoAtual</h6>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-preto">
                        <h4>@ViewBag.NomeGrandeza</h4>
                        <h5>@SmartEnergy.Resources.SupervisaoTexts.Maximo</h5>
                        <h1>@ViewBag.VMaxMes <span>@ViewBag.UnidadeGrandeza</span></h1>
                        <h6>@ViewBag.VMaxMes_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
        <br /><br />
        <div class="row">
            <div class="col-lg-offset-6 col-lg-6">
                <div class="relat-coluna">
                    <div class="relat-valorAtual relat-preto">
                        <h4>@ViewBag.NomeGrandeza</h4>
                        <h5>@SmartEnergy.Resources.SupervisaoTexts.Minimo</h5>
                        <h1>@ViewBag.VMinMes <span>@ViewBag.UnidadeGrandeza</span></h1>
                        <h6>@ViewBag.VMinMes_DataHora</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-8 col-md-12">
        <div class="row">
            <div class="relat-grafico">
                <div id="grafico-ea" style="margin-top: -10px"></div>
                <div class='chart-legend' style="display:none;">
                    <div class='legend-scale-dem'>
                        <ul class='legend-labels' style="margin-top: -25px;">
                            <li><span style='background:#1C84C6;'></span>@ViewBag.NomeGrandeza</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@Html.Partial("_Relat_Botoes")

<br />
<div class="panel panel-title relatorio">
    <div class="panel-body">

        <div class="row">
            <div class="col-lg-12">
                <div class="relat-tabela">
                    <table class="table no-margins" style="width:100%;">
                        <thead>
                            <tr>
                                <th style="width:25%;"></th>
                                <th class="relat-ea" style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.MedioMes</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Maximo</th>
                                <th class="relat-preto" style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Minimo</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="relat-preto">@ViewBag.NomeGrandeza</td>
                                <td class="relat-ea">@ViewBag.VMedMes @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.DataTextoAtual)</small></td>
                                <td class="relat-preto">@ViewBag.VMaxMes @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMaxMes_DataHora)</small></td>
                                <td class="relat-preto">@ViewBag.VMinMes @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.VMinMes_DataHora)</small></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal inmodal animated fadeIn" id="modalTabela" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-editar-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title"><i class="fa fa-list"></i>&nbsp;&nbsp;@SmartEnergy.Resources.RelatoriosTexts.TabelaRegistros</h4>
            </div>
            <div class="modal-body">
                <div class="panel panel-default">
                    <table class="table no-margins table-hover tabela-grafico">
                        <thead>
                            <tr>
                                <th style="width:25%;">@SmartEnergy.Resources.RelatoriosTexts.Dia</th>
                                <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Medio<br />(@ViewBag.UnidadeGrandeza)</th>
                                <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Maximo<br />(@ViewBag.UnidadeGrandeza)</th>
                                <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Minimo<br />(@ViewBag.UnidadeGrandeza)</th>
                            </tr>
                        </thead>

                        <tbody>

                            @{
                                var i = 0;
                                var j = 0;
                                var vmed = "---";
                                var vmax = "---";
                                var vmin = "---";

                                var NumDiasMes = ViewBag.NumDiasMes;

                                for (i = 0; i < NumDiasMes; i++)
                                {
                                    j = i + 1;

                                    vmed = string.Format("{0:#,##0.00}", ViewBag.VMed[j]);
                                    vmax = string.Format("{0:#,##0.00}", ViewBag.VMax[j]);
                                    vmin = string.Format("{0:#,##0.00}", ViewBag.VMin[j]);

                                    <tr class="tabela_valores">
                                        <td style="text-align:center;" class="relat-ea">@ViewBag.Dias[j]</td>
                                        <td style="text-align:center;" class="relat-ea">@vmed</td>
                                        <td style="text-align:center;" class="relat-ea">@vmax</td>
                                        <td style="text-align:center;" class="relat-ea">@vmin</td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
            </div>
        </div>
    </div>
</div>


@Html.Partial("_EventosModal")

@Html.Partial("_ObservacoesModal")


<script type="text/javascript">

    $(document).ready(function () {

        // entradas analogicas
        var dataX = [];
        var labelX = [];
        var dataMaxima = [];
        var dataMedia = [];
        var dataMinima = [];

        // copia valores
        var VMax = @Html.Raw(Json.Encode(@ViewBag.VMax));
        var VMed = @Html.Raw(Json.Encode(@ViewBag.VMed));
        var VMin = @Html.Raw(Json.Encode(@ViewBag.VMin));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));
        var NumDiasMes = @Html.Raw(Json.Encode(@ViewBag.NumDiasMes));
        var NomeGrandeza = @Html.Raw(Json.Encode(@ViewBag.NomeGrandeza));
        var UnidadeGrandeza = @Html.Raw(Json.Encode(@ViewBag.UnidadeGrandeza));

        // valor minimo e máximo
        var minimoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMin));
        var maximoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMax));

        dataX.push('x');
        dataMaxima.push(['data1']);
        dataMedia.push(['data2']);
        dataMinima.push(['data3']);

        for (i = 0; i < NumDiasMes+2; i++)
        {
            // X
            dataX.push(Datas[i]);

            dataMaxima.push([VMax[i]]);
            dataMedia.push([VMed[i]]);
            dataMinima.push([VMin[i]]);
        }

        // valores label X
        labelX.push(Datas[1]);
        for (i = 5; i <= 25; i+=5)
        {
            labelX.push(Datas[i]);
        }
        labelX.push(Datas[NumDiasMes]);

        var Data = [dataX,dataMinima,dataMedia,dataMaxima];

        c3.generate({

            bindto: '#grafico-ea',
            size: {
                height: 420
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'bar',
                    data3: 'bar',
                },
                colors: {
                    data1: '#00007f',
                    data2: '#4284B2',
                    data3: '#D7E8F8',
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // retorna dia
                            return x.getDate();
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    // Grafico 1 dia     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(11 horas)
                    padding: -1000*60*60*11
                },
                y:  {
                    min: minimoValor,
                    max: maximoValor,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {
                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.15 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < d.length; i++) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        if (! text) {

                            // split string and create array.
                            var arr = Datas[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            title = "Dia " + data.getDate();

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1C84C6'>" + title + "</th></tr>" : "");
                        }

                        switch(i)
                        {
                            case 0:     // minimo
                                name = MINIMO;
                                break;

                            case 1:     // medio
                                name = MEDIO;
                                break;

                            case 2:     // maximo
                                name = MAXIMO;
                                break;
                        }

                        value = d[i].value;
                        bgcolor = $$.levelColor ? $$.levelColor(d[i].value) : color(d[i].id);

                        text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                        text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                        text += "<td class='value'>" + value.toFixed(2) + " " + UnidadeGrandeza + "</td>";
                        text += "</tr>";
                    }
                    return text + "</table>";
                }
            }
        });

        // labels
        $('.chart-legend').css("display", "block");

        $(".modal-dialog").draggable({
            handle: ".modal-editar-header"
        });
    });

</script>
