﻿@using SmartEnergyLib.SQL

<style>
    .select2-container--open {
        z-index: 9999999;
    }

</style>

@if (ViewBag.TemEventos)
{
    List<EventosDescricao> eventos = ViewBag.listaEventosDescricao;


    <div class="modal inmodal animated fadeIn" id="modalEventos" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-editar-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">@SmartEnergy.Resources.ComumTexts.BotaoFechar</span></button>
                    <h4 class="modal-title"><i class="fa fa-list"></i>&nbsp;&nbsp;@SmartEnergy.Resources.RelatoriosTexts.Eventos</h4>
                    <h4>[@ViewBag.DataTextoAtual]</h4>
                </div>
                <div class="modal-body">
                    <div class="panel panel-default">
                        <div class="panel-body">

                            @{
                                if (eventos != null)
                                {
                                    if (eventos.Count >= 500)
                                    {
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <div class="widget style1 red-bg">
                                                    <div class="row">
                                                        <div class="col-xs-4">
                                                            <i class="fa fa-exclamation-triangle fa-5x"></i>
                                                        </div>
                                                        <div class="col-xs-8 text-right">
                                                            <h2 class="font-bold">@SmartEnergy.Resources.SupervisaoTexts.UltrapassouLimite</h2>
                                                            <br>
                                                            <h4>@SmartEnergy.Resources.SupervisaoTexts.NaoApresentaEventos</h4>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                
                                        <br />
                                    }
                                }
                            }

                            <select class="select2_tipo form-control" id="TiposEvento" onchange="SelecionouTipo()">

                                @{
                                    string icone_tipo = "fa-th-list";
                                    int tipo = 0;

                                    for (var conta = 0; conta < ViewBag.NumTiposEventos; conta++)
                                    {
                                        tipo = ViewBag.listaTiposEventosDescricao[conta].Tipo;

                                        switch (tipo)
                                        {
                                            case 0:
                                                icone_tipo = "fa-th-list";
                                                break;

                                            case 1:
                                                icone_tipo = "fa-bell";
                                                break;

                                            case 2:
                                                icone_tipo = "fa-gear";
                                                break;

                                            case 3:
                                                icone_tipo = "fa-power-off";
                                                break;

                                            case 4:
                                                icone_tipo = "fa-bolt";
                                                break;

                                            case 5:
                                                icone_tipo = "fa-sitemap";
                                                break;

                                            case 6:
                                                icone_tipo = "fa-sign-in";
                                                break;

                                            case 7:
                                                icone_tipo = "fa-sign-out";
                                                break;

                                            case 8:
                                                icone_tipo = "fa-upload";
                                                break;

                                            case 9:
                                                icone_tipo = "fa-clock-o";
                                                break;

                                            case 10:
                                                icone_tipo = "fa-user";
                                                break;
                                        }

                                        <option value=@tipo data-icon=@icone_tipo>&nbsp;&nbsp;@ViewBag.listaTiposEventosDescricao[conta].Descricao</option>
                                    }
                                }

                            </select>

                            <br /><br />

                            <table class="table table-striped table-bordered table-hover dataTables-eventos">
                                <thead>
                                    <tr>
                                        <th>@SmartEnergy.Resources.RelatoriosTexts.Data</th>
                                        <th>@SmartEnergy.Resources.RelatoriosTexts.Tipo</th>
                                        <th></th>
                                        <th>@SmartEnergy.Resources.RelatoriosTexts.Evento</th>
                                    </tr>
                                </thead>
                                <tbody>

                                    @{
                                        if( eventos != null )
                                        {
                                            foreach (EventosDescricao evento in eventos)
                                            {
                                                icone_tipo = "fa-th-list";
                                                tipo = evento.Tipo;

                                                switch (tipo)
                                                {
                                                    case 0:
                                                        icone_tipo = "fa-th-list";
                                                        break;

                                                    case 1:
                                                        icone_tipo = "fa-bell";
                                                        break;

                                                    case 2:
                                                        icone_tipo = "fa-gear";
                                                        break;

                                                    case 3:
                                                        icone_tipo = "fa-power-off";
                                                        break;

                                                    case 4:
                                                        icone_tipo = "fa-bolt";
                                                        break;

                                                    case 5:
                                                        icone_tipo = "fa-sitemap";
                                                        break;

                                                    case 6:
                                                        icone_tipo = "fa-sign-in";
                                                        break;

                                                    case 7:
                                                        icone_tipo = "fa-sign-out";
                                                        break;

                                                    case 8:
                                                        icone_tipo = "fa-upload";
                                                        break;

                                                    case 9:
                                                        icone_tipo = "fa-clock-o";
                                                        break;

                                                    case 10:
                                                        icone_tipo = "fa-user";
                                                        break;
                                                }

                                                <tr>
                                                    <td style="font-size:11px;"><span style="display:none;">@evento.DataHora_Sort</span>@evento.DataHora</td>
                                                    <td>@evento.Tipo</td>
                                                    <td style="text-align:center;"><i class="fa @icone_tipo" style="font-size:20px;"></i></td>
                                                    <td style="font-size:11px;">@evento.Descricao</td>
                                                </tr>
                                            }
                                        }
                                    }
                                                                        
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
                </div>
            </div>
        </div>
    </div>

}
    

<script type="text/javascript">

    $(document).ready(function () {

        // eventos
        $('.dataTables-eventos').DataTable({
            "iDisplayLength": 9,
            dom: 'ftp',

            "aoColumnDefs": [
                        {
                            "aTargets": [1],
                            "bVisible": false,
                            "bSortable": true
                        },
                        {
                            "aTargets": [2],
                            "bVisible": true,
                            "bSortable": false
                        },
            ],
            "oSearch": {
                "bSmart": false,
                "bRegex": true,
                "sSearch": ""
            },
            "bAutoWidth": false,
            "bDestroy": true,
            "aoColumns": [
            { sWidth: "25%" },
            { sWidth: "5%" },
            { sWidth: "5%" },
            { sWidth: "80%" }
            ],
            'order': [0, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });


        function formatIcon(icon) {

            if (!icon.id) {
                return icon.text;
            }
            var originalOption = icon.element;
            var $icon = $('<i style="font-size:16px;" class="fa ' + $(originalOption).data('icon') + '"></i><span>' + icon.text + '</span>');

            return $icon;

        };

        $(".select2_tipo").select2({
            minimumResultsForSearch: Infinity,
            width: "100%",
            templateResult: formatIcon,
            templateSelection: formatIcon
        });

        $(".modal-dialog").draggable({
            handle: ".modal-editar-header"
        });
    });

    function SelecionouTipo() {

        // pega tipo selecionado
        //var tipo = document.getElementById("TiposEvento").selectedIndex;
        var tipo = $("#TiposEvento").val();

        // verifica se todos
        if (tipo == 0) {
            // procura na tabela
            $('.dataTables-eventos').DataTable().column(1).search("").draw();
        }
        else {
            // procura na tabela
            $('.dataTables-eventos').DataTable().column(1).search(tipo).draw();
        }
    }

</script>

    