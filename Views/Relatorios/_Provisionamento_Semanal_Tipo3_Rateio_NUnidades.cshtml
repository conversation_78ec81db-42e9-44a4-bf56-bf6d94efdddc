﻿@using SmartEnergyLib.SQL

<style>

    .dataTables-rateio td {
        font-size: 11px;
    }

    .dataTables-rateio th {
        font-size: 11px;
    }

</style>


<div class="row">
    <div class="col-lg-12">

        @{
            Provisionamento_Tipo3 provisionamento = ViewBag.ProvisionamentoSemanal;
            
            if (provisionamento != null)
            {
                
                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>Custos Unificados</h4>
                    </div>
                    <div class="panel-body">
                        <div class="row">

                            <table class="table table-striped table-bordered table-hover dataTables-custosUnificados">
                                <thead>
                                    <tr>
                                        <th>@SmartEnergy.Resources.ConfiguracaoTexts.UnidadeConsumidora</th>
                                        <th>CNPJ</th>
                                        <th>@SmartEnergy.Resources.ContratosCCEETexts.TotalCustoEnergia (R$)</th>
                                    </tr>
                                </thead>
                                <tbody>

                                    @{
                                        List<Provisionamento_UnidadesConsumidoras> unidadesConsumidoras_Unificados = ViewBag.unidadesConsumidoras;

                                        if (unidadesConsumidoras_Unificados != null)
                                        {
                                            foreach (Provisionamento_UnidadesConsumidoras unid in unidadesConsumidoras_Unificados)
                                            {
                                                <tr>
                                                    <td><b>@unid.RazaoSocial</b><br />@unid.PontoMedicao</td>
                                                    <td>@unid.CNPJ</td>
                                                    <td>@string.Format("{0:#,##0.00}", unid.TotalCusto_Energia)</td>
                                                </tr>
                                            }
                                    
                                            <tr>
                                                <td><b>TOTAL</b><br />&nbsp;</td>
                                                <td></td>
                                                <td>@string.Format("{0:#,##0.00}", provisionamento.TotalCusto_Energia)</td>
                                            </tr>                                    
                                        }
                                    }

                                </tbody>
                            </table>

                        </div>
                    </div>
                </div>


                                                 
                List<Provisionamento_Tipo1_Tipo2> provs = provisionamento.por_contrato;
                
                if (provs != null)
                {
                    foreach (Provisionamento_Tipo1_Tipo2 prov in provs)
                    {

                        <div class="panel panel-title">
                            <div class="panel-heading">
                                <h4>Contrato @prov.Contrato_Codigo</h4>
                            </div>
                            <div class="panel-body">
                                <div class="row">

                                    <table class="table table-striped table-bordered table-hover dataTables-rateio">
                                        <thead>
                                            <tr>
                                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.UnidadeConsumidora</th>
                                                <th>CNPJ</th>
                                                <th>@SmartEnergy.Resources.ContratosCCEETexts.ConsumoConsolidado<br />(MWh)</th>
                                                <th>@SmartEnergy.Resources.ContratosCCEETexts.ConsumoEstimado<br />(MWh)</th>
                                                <th>@SmartEnergy.Resources.ContratosCCEETexts.ConsumoTotalBruto<br />(MWh)</th>
                                                <th>@SmartEnergy.Resources.RelatoriosTexts.Consumo<br />(%)</th>
                                                <th>@SmartEnergy.Resources.ContratosCCEETexts.VolumeFaturado<br />(MWh)</th>
                                                <th>@SmartEnergy.Resources.ContratosCCEETexts.SobraDeficit<br />(MWh)</th>
                                                <th>@SmartEnergy.Resources.ContratosCCEETexts.PrecoEnergia<br />(R$/MWh)</th>
                                                <th>PLD<br />(R$/MWh)</th>
                                                <th>@SmartEnergy.Resources.ContratosCCEETexts.FaturaLP<br />(R$)</th>
                                                <th>@SmartEnergy.Resources.ContratosCCEETexts.FaturaCP<br />(R$)</th>
                                                <th>@SmartEnergy.Resources.ContratosCCEETexts.TotalCustoEnergia<br />(R$)</th>
                                            </tr>
                                        </thead>
                                        <tbody>

                                            @{
                                                List<Provisionamento_UnidadesConsumidoras> unidadesConsumidoras = prov.unidadesConsumidoras;
                                            
                                                if (unidadesConsumidoras != null)
                                                {
                                                    foreach (Provisionamento_UnidadesConsumidoras unid in unidadesConsumidoras)
                                                    {
                                                        <tr>
                                                            <td><b>@unid.RazaoSocial</b><br />@unid.PontoMedicao</td>
                                                            <td>@unid.CNPJ</td>
                                                            <td>@string.Format("{0:#,##0.000}", unid.consumoMensal.Consumo_Consolidado)</td>
                                                            <td>@string.Format("{0:#,##0.000}", unid.consumoMensal.Consumo_Estimado)</td>
                                                            <td>@string.Format("{0:#,##0.000}", unid.consumoMensal.Consumo_Total_Bruto)</td>
                                                            <td>@string.Format("{0:0.0}%", unid.Consumo_Porc)</td>
                                                            <td>@string.Format("{0:#,##0.000}", unid.Volume_Faturado)</td>
                                                            <td>@string.Format("{0:#,##0.000}", unid.Sobra_Deficit)</td>
                                                            <td>@string.Format("{0:#,##0.00}", unid.Preco_Energia)</td>
                                                            <td>@string.Format("{0:#,##0.00}", unid.PLD)</td>
                                                            <td>@string.Format("{0:#,##0.00}", unid.TotalFaturamento_LP)</td>
                                                            <td>@string.Format("{0:#,##0.00}", unid.TotalFaturamento_CP)</td>
                                                            <td>@string.Format("{0:#,##0.00}", unid.TotalCusto_Energia)</td>
                                                        </tr>
                                                    }
                                    
                                                    <tr>
                                                        <td><b>TOTAL</b><br />&nbsp;</td>
                                                        <td></td>
                                                        <td>@string.Format("{0:#,##0.000}", prov.Consumo_Consolidado)</td>
                                                        <td>@string.Format("{0:#,##0.000}", prov.Consumo_Estimado)</td>
                                                        <td>@string.Format("{0:#,##0.000}", prov.Consumo_Total_Bruto)</td>
                                                        <td>100.0%</td>
                                                        <td>@string.Format("{0:#,##0.000}", prov.Volume_Contratado)</td>
                                                        <td>@string.Format("{0:#,##0.000}", prov.Resultado)</td>
                                                        <td></td>
                                                        <td></td>
                                                        <td>@string.Format("{0:#,##0.00}", prov.TotalFaturamento_LP)</td>
                                                        <td>@string.Format("{0:#,##0.00}", prov.TotalFaturamento_CP)</td>
                                                        <td>@string.Format("{0:#,##0.00}", prov.TotalCusto_Energia)</td>
                                                    </tr>                                    
                                                }
                                            }

                                        </tbody>
                                    </table>

                                </div>
                            </div>
                        </div>
                                                
                    }
                }
            }
        }

    </div>
</div>


<script type="text/javascript">

    $(document).ready(function () {

        $('.dataTables-custosUnificados').DataTable({
            "bAutoWidth": false,
            "iDisplayLength": 18,
            dom: 'tp',

            "aoColumnDefs": [
                    { "aTargets": [0], "sType": "portugues", "bSortable": false, "bSearchable": false },
                    { "aTargets": [1], "sType": "portugues", "bSortable": false, "bSearchable": false },
                    { "aTargets": [2], "sType": "numero", "bSortable": false, "bSearchable": false }
            ],

            "aoColumns": [
                { sWidth: "40%" },
                { sWidth: "30%" },
                { sWidth: "30%" }
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });



        $('.dataTables-rateio').DataTable({
            "bAutoWidth": false,
            "iDisplayLength": 18,
            dom: 'tp',

            "aoColumnDefs": [
                    { "aTargets": [0], "sType": "portugues", "bSortable": false, "bSearchable": false },
                    { "aTargets": [1], "sType": "portugues", "bSortable": false, "bSearchable": false },
                    { "aTargets": [2], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [3], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [4], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [5], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [6], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [7], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [8], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [9], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [10], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [11], "sType": "numero", "bSortable": false, "bSearchable": false },
                    { "aTargets": [12], "sType": "numero", "bSortable": false, "bSearchable": false }
            ],

            "aoColumns": [
                { sWidth: "15%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "10%" }
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

    });

</script>
