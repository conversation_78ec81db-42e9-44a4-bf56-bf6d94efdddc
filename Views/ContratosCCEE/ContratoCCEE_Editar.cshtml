﻿@model SmartEnergyLib.SQL.ContratosCCEEDominio

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = @SmartEnergy.Resources.ContratosCCEETexts.ContratoCCEE;
}

<style>

    .icones {
        padding: 0px 5px;
        font-size: 18px;
        float: left;
    }

    .overlay_aguarde {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.45);
        z-index: 9999;
        color: white;
        display: inline-block;
        border-radius: 6px;
        margin-bottom: 20px;
        margin-left: 14px;
        margin-right: 14px;
    }

    .sk-spinner-wave div {
        background-color: #ffffff !important;
    }

    .spinner-aguarde {
        margin: 0;
        position: absolute;
        top: 50%;
        left: 50%;
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
    }

    .link_preto a:link {
        color: #7E6A6C !important;
    }
    .link_preto a:visited {
        color: #7E6A6C  !important;
    }
    .link_preto a:hover {
        color: #a0a0a0  !important;
    }
    .link_preto a:active {
        color: #7E6A6C  !important;
    }

</style>

@{
    int IDTipoAcesso = ViewBag._IDTipoAcesso;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("ContratoCCEE_Editar", "ContratosCCEE", new { ReturnUrl = ViewBag.ReturnUrl }, FormMethod.Post, new { id = "form", role = "form" }))
            {
                @Html.Hidden("IDCliente", Model.IDCliente)
                @Html.Hidden("IDMedicao", Model.IDMedicao)
                @Html.Hidden("Alerta_Encerramento", Model.Alerta_Encerramento)
                @Html.Hidden("Vigencia_Inicio", Model.Vigencia_Inicio)
                @Html.Hidden("Vigencia_Fim", Model.Vigencia_Fim)
                @Html.Hidden("Base_Data", Model.Base_Data)
                @Html.Hidden("DataLimiteSazo", Model.DataLimiteSazo)
                @Html.Hidden("Base_Preco_Flat", Model.Base_Preco_Flat)
                @Html.Hidden("Proinfa_Abate", Model.Proinfa_Abate)
                @Html.Hidden("PercentualCarga_Possui", Model.PercentualCarga_Possui)
                @Html.Hidden("Ordem", Model.Ordem)
                @Html.Hidden("Considera", Model.Considera)
                @Html.Hidden("Reajuste_Preco", Model.Reajuste_Preco)
                

                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>@SmartEnergy.Resources.ContratosCCEETexts.ContratoCCEE</h4>
                    </div>
                    <div class="panel-body">
                        <div class="row">

                            <div class="form-group col-lg-2">
                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.ContratoID</label>
                                @Html.TextBoxFor(model => model.Contrato_ID, new { @class = "form-control", @maxlength = "30", @disabled = "disabled" })
                            </div>
                            <div class="form-group col-lg-2">
                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.ContratoCodigo</label>
                                @Html.TextBoxFor(model => model.Contrato_Codigo, new { @class = "form-control", @maxlength = "30", @disabled = "disabled" })
                            </div>

                            @{
                                string classe_botoes = "col-lg-8";
                                
                                if ( isUser.isGESTAL(IDTipoAcesso) || isUser.isConsultor(IDTipoAcesso) )
                                {
                                    classe_botoes = "col-lg-6";
                                    
                                    <div class="form-group col-lg-2">
                                        <label class="control-label">&nbsp;ID</label>

                                        @{
                                            if (Model.IDContratoCCEE == 0)
                                            {
                                                @Html.Hidden("IDContratoCCEE", Model.IDContratoCCEE)
                                                @Html.TextBox("Novo", "Novo Contrato", new { @class = "form-control", @disabled = "disabled" })
                                            }
                                            else
                                            {
                                                @Html.TextBoxFor(model => model.IDContratoCCEE, new { @class = "form-control", @disabled = "disabled" })
                                            }
                                        }

                                    </div>
                                }
                                else
                                {
                                    @Html.Hidden("IDContratoCCEE", Model.IDContratoCCEE)
                                }
                            }

                            <div class="@classe_botoes">
                                <div class="ibox-tools">
                                    <a href='@("/ContratosCCEE/ContratosCCEE")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                    <button type="button" class="btn btn-primary" id="BotaoSalvar" onclick="Salvar();" disabled="">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                </div>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="tabs-container">
                                    <ul class="nav nav-tabs">
                                        <li class="active"><a data-toggle="tab" href="#tab-1"><i class="fa fa-cogs"></i> @SmartEnergy.Resources.ConfiguracaoTexts.Geral</a></li>
                                        <li class="tab-2"><a data-toggle="tab" href="#tab-2"><i class="fa fa-cube"></i> @SmartEnergy.Resources.ContratosCCEETexts.CaracteristicasProduto</a></li>
                                        <li class="tab-3"><a data-toggle="tab" href="#tab-3"><i class="fa fa-dollar"></i> @SmartEnergy.Resources.ContratosCCEETexts.PrecosReajustados</a></li>
                                        <li class="tab-4"><a data-toggle="tab" href="#tab-4"><i class="fa fa-calendar"></i> @SmartEnergy.Resources.ContratosCCEETexts.Sazonalizacao</a></li>
                                    </ul>
                                    <div class="tab-content">
                                        <div id="tab-1" class="tab-pane active">
                                            <div class="panel-body">

                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;CNPJ [@SmartEnergy.Resources.ConfiguracaoTexts.RazaoSocial]</label>
                                                        @Html.DropDownListFor(model => model.IDEmpresa, new SelectList(ViewBag.listaEmpresas, "IDEmpresa", "CNPJ_RazaoSocial"),
                                                            SmartEnergy.Resources.ConfiguracaoTexts.Selecione,
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.SiglaCCEE</label>
                                                        @Html.TextBoxFor(model => model.SiglaCCEE, new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                                <br />

                                                <div class="row">
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;CNPJ</label>
                                                        @Html.TextBoxFor(model => model.CNPJ, new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.TipoEmpresa</label>
                                                        @Html.DropDownListFor(model => model.IDTipoEmpresa, new SelectList(ViewBag.listaTipoEmpresa, "ID", "Descricao"), "---", new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Estado</label>
                                                        @Html.DropDownListFor(model => model.IDEstado, new SelectList(ViewBag.listaTipoEstado, "IDEstado", "Nome"), "---", new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Cidade</label>
                                                        @Html.DropDownListFor(model => model.IDCidade, new SelectList(ViewBag.listaTipoCidade, "IDCidade", "Nome"), "---", new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                                <br />

                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.Medicao</label>
                                                        @Html.TextBoxFor(model => model.NomeMedicao, new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.PontoMedicao</label>
                                                        @Html.TextBoxFor(model => model.PontoMedicao, new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                                <hr />

                                                @if (Model.IDContratoCCEE == 0) {
                                                    
                                                    <br />
                                                    <div class="row">
                                                        <div class="col-lg-1" style="text-align:center;">
                                                            <i class="fa fa-warning fa-5x"></i>
                                                        </div>
                                                        <div class="col-lg-11">
                                                            <h2>É necessário salvar o Contrato para adicionar as Unidades Atendidas.</h2>
                                                        </div>
                                                    </div>
                                                    <br />
                                                    
                                                }
                                                else
                                                {

                                                    <div class="row">
                                                        <div class="col-lg-3">
                                                            <div class="link_branco">
                                                                <button type="button" id="BotaoAdicionarUnidade" class="btn btn-info btn-lg pull-left" onclick="ApresentaUnidadesNaoAtendidas();" style="color:#ffffff; width:100%;" disabled="">
                                                                    <span style="font-size:large">@SmartEnergy.Resources.ConfiguracaoTexts.AdicionarUnidadeAtendida</span>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="form-group col-lg-12">
                                                            <table id="dataTables-unidadesAtendidas" class="table table-striped table-bordered table-hover">
                                                                <thead>
                                                                    <tr>
                                                                        <th>IDEmpresa</th>
                                                                        <th>@SmartEnergy.Resources.ConfiguracaoTexts.RazaoSocial</th>
                                                                        <th>@SmartEnergy.Resources.ConfiguracaoTexts.SiglaCCEE</th>
                                                                        <th>CNPJ</th>
                                                                        <th>@SmartEnergy.Resources.ConfiguracaoTexts.PontoMedicao</th>
                                                                        <th>@SmartEnergy.Resources.ContratosCCEETexts.ContratoID</th>
                                                                        <th>@SmartEnergy.Resources.ContratosCCEETexts.PercentualCarga (%)</th>
                                                                        <th></th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>

                                                                    @{
                                                                        // unidade atendidas
                                                                        List<ContratosCCEE_UnidadesAtendidasDominio> unidadesAtendidas = ViewBag.unidadesAtendidas;
                                                                        
                                                                        // percorre unidades atendidas
                                                                        foreach (ContratosCCEE_UnidadesAtendidasDominio unidade in unidadesAtendidas)
                                                                        {
                                                                            <tr>
                                                                                <td>@unidade.IDEmpresa</td>
                                                                                <td>@unidade.RazaoSocial</td>
                                                                                <td>@unidade.SiglaCCEE</td>
                                                                                <td>@unidade.CNPJ</td>
                                                                                <td>@unidade.PontoMedicao</td>
                                                                                <td>@unidade.Contrato_ID</td>

                                                                                @if (Model.PercentualCarga_Possui)
                                                                                {
                                                                                    <td>@string.Format("{0:0.00}", unidade.PercentualCarga_Valor)</td>
                                                                                }
                                                                                else
                                                                                {
                                                                                    <td>-</td>
                                                                                }

                                                                                <td class="link_preto">
                                                                                    <a href="#" class="edit-unidade"><i class="fa fa-edit icones" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a>
                                                                                    <a href="#" class="delete-unidade"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>
                                                                                </td>
                                                                            </tr>
                                                                        }
                                                                    }

                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                     
                                                }    
                                                  
                                                <hr />
                                                <div class="row">
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ContratoStatus</label>
                                                        @Html.DropDownListFor(model => model.Contrato_Status, new SelectList(ViewBag.listaTipoContratoStatus, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.ContratoStatus),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;</label>
                                                        <div class="i-checks" style="margin-top:6px; margin-left:20px;">
                                                            <input id="Alerta_EncerramentoAux" name="Alerta_EncerramentoAux" type="checkbox" @(Model.Alerta_Encerramento == true ? "checked" : "")>&nbsp;&nbsp;<span style="margin-right:40px;">@SmartEnergy.Resources.ContratosCCEETexts.AlertarEncerramento</span>
                                                        </div>
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.VigenciaInicio</label>
                                                        <div class="input-group date">
                                                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>@Html.TextBoxFor(model => model.Vigencia_Inicio_Texto, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.VigenciaFim</label>
                                                        <div class="input-group date">
                                                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>@Html.TextBoxFor(model => model.Vigencia_Fim_Texto, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.ConsultorNegocio</label>
                                                        @Html.TextBoxFor(model => model.ConsultorNegocio, new { @class = "form-control", @maxlength = "50", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.Carteira</label>
                                                        @Html.DropDownListFor(model => model.Carteira, new SelectList(ViewBag.listaTipoCarteira, "ID", "Descricao"), "---", new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                                <hr />

                                                <div class="row">
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.Fornecedor</label>
                                                        @Html.DropDownListFor(model => model.IDComercializadora, new SelectList(ViewBag.listaTipoComercializadoras, "IDAgente", "Nome"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ContratosCCEETexts.Comercializadora),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>

                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.RazaoSocial</label>
                                                        <select class = "form-control" id="Comercializadoras_RazaoSocial" name="Comercializadoras_RazaoSocial" disabled="">
                                                            <option value="">---</option>

                                                            @{
                                                                List<AgentesDominio> listaComercializadoras = ViewBag.listaTipoComercializadoras;
                                                                int IDComercializadora = Model.IDComercializadora;
                                                                
                                                                foreach (AgentesDominio comercializadora in listaComercializadoras)
                                                                {
                                                                    if (IDComercializadora == comercializadora.IDAgente)
                                                                    {
                                                                        <option value="@comercializadora.IDAgente" selected="selected">@comercializadora.RazaoSocial</option>
                                                                    }
                                                                    else
                                                                    {
                                                                        <option value="@comercializadora.IDAgente">@comercializadora.RazaoSocial</option>
                                                                    }
                                                                }
                                                            }

                                                        </select>
                                                    </div>

                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;CNPJ</label>
                                                        <select class = "form-control" id="Comercializadoras_CNPJ" name="Comercializadoras_CNPJ" disabled="">
                                                            <option value="">---</option>

                                                            @{
                                                                foreach (AgentesDominio comercializadora in listaComercializadoras)
                                                                {
                                                                    if (IDComercializadora == comercializadora.IDAgente)
                                                                    {
                                                                        <option value="@comercializadora.IDAgente" selected="selected">@comercializadora.CNPJ</option>
                                                                    }
                                                                    else
                                                                    {
                                                                        <option value="@comercializadora.IDAgente">@comercializadora.CNPJ</option>
                                                                    }
                                                                }
                                                            }

                                                        </select>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                        <div id="tab-2" class="tab-pane">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.Fonte</label>
                                                        @Html.DropDownListFor(model => model.IDTipoFonte, new SelectList(ViewBag.listaTipoFonte, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ContratosCCEETexts.Fonte),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.SubSistema</label>
                                                        @Html.DropDownListFor(model => model.IDSubSistema, new SelectList(ViewBag.listaTipoSubSistema, "ID", "Descricao"),
                                                            "---",
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.ReajusteMes</label>
                                                        @Html.DropDownListFor(model => model.Reajuste_Mes, new SelectList(ViewBag.listaTipoMeses, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.ReajusteMes),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.ReajusteIndice</label>
                                                        @Html.DropDownListFor(model => model.Reajuste_Indice, new SelectList(ViewBag.listaTipoIndiceReajuste, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.ReajusteIndice),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                                <hr />

                                                <div class="row">
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.BaseData</label>
                                                        <div class="input-group date">
                                                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>@Html.TextBoxFor(model => model.Base_Data_Texto, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                        </div>
                                                    </div>

                                                    @if (Model.IDContratoCCEE == 0) 
                                                    {
                                                        <div class="col-lg-offset-1 col-lg-1" style="text-align:center;">
                                                            <i class="fa fa-warning fa-5x"></i>
                                                        </div>
                                                        <div class="col-lg-7">
                                                            <h2>É necessário salvar o Contrato para editar os Preços Base.</h2>
                                                        </div>

                                                        @Html.Hidden("Base_Preco", Model.Base_Preco)
                                                        @Html.Hidden("Base_Preco_FlatAux", (Model.Base_Preco_Flat == true ? "checked" : ""))
                                                    }
                                                    else
                                                    {
                                                        <div class="div_preco_base_flat" style="display:none;">
                                                            <div class="form-group col-lg-3">
                                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.BasePreco (R$)</label>
                                                                @Html.TextBoxFor(model => model.Base_Preco, "{0:0.00}", new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                            </div>
                                                        </div>

                                                        <div class="div_precos_base" style="display:none;">
                                                            <div class="col-lg-3">
                                                                <div class="link_branco" style="margin-top:10px;">
                                                                    <button type="button" id="BotaoPrecosBase" class="btn btn-info btn-lg pull-left" onclick="ApresentaPrecosBase();" style="color:#ffffff; width:100%;" disabled="">
                                                                        <span style="font-size:large">@SmartEnergy.Resources.ContratosCCEETexts.BasePrecos</span>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="form-group col-lg-1">
                                                            <label class="control-label">&nbsp;</label>
                                                            <div class="i-checks" style="margin-top:6px;">
                                                                <input id="Base_Preco_FlatAux" name="Base_Preco_FlatAux" type="checkbox" @(Model.Base_Preco_Flat == true ? "checked" : "")>&nbsp;&nbsp;<span style="margin-right:14px;">Flat</span>
                                                            </div>
                                                        </div>                                                        
                                                    }                                                    

                                                </div>
                                                <br />

                                                <div class="row">
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;</label>
                                                        <div class="i-checks" style="margin-top:16px;">
                                                            <input id="Proinfa_AbateAux" name="Proinfa_AbateAux" type="checkbox" @(Model.Proinfa_Abate == true ? "checked" : "")>&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ContratosCCEETexts.Proinfa_Abate</span>
                                                        </div>
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;</label>
                                                        <div class="i-checks" style="margin-top:16px;">
                                                            <input id="PercentualCarga_PossuiAux" name="PercentualCarga_PossuiAux" type="checkbox" @(Model.PercentualCarga_Possui == true ? "checked" : "")>&nbsp;&nbsp;<span style="margin-right:14px;">@SmartEnergy.Resources.ContratosCCEETexts.PercentualCarga_Possui</span>
                                                        </div>
                                                    </div>
                                                    <div class="div_percentual_carga" style="display:none;">
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.PercentualCarga (%)</label>
                                                            @Html.TextBoxFor(model => model.PercentualCarga_Valor, "{0:0.00}", new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                </div>
                                                <hr />

                                                <div class="row">
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;Volume</label>
                                                        @Html.DropDownListFor(model => model.Montante_Tipo, new SelectList(ViewBag.listaTipoMontante, "ID", "Descricao"),
                                                            string.Format("{0}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>

                                                    @if (Model.IDContratoCCEE == 0) 
                                                    {
                                                        <div class="col-lg-offset-1 col-lg-1" style="text-align:center;">
                                                            <i class="fa fa-warning fa-5x"></i>
                                                        </div>
                                                        <div class="col-lg-7">
                                                            <h2>É necessário salvar o Contrato para editar o Volume.</h2>
                                                        </div>
                                                    }
                                                    else
                                                    {
                                                        <div class="col-lg-3">
                                                            <div class="link_branco" style="margin-top:10px;">
                                                                <button type="button" id="BotaoVolume" class="btn btn-info btn-lg pull-left" onclick="ApresentaVolumes();" style="color:#ffffff; width:100%;" disabled="">
                                                                    <span style="font-size:large">Volume</span>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    }                                                    

                                                </div>
                                                <hr />

                                                <div class="row">
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.Sazonaliza_1_Ano</label><br />
                                                        @Html.DropDownListFor(model => model.Sazonaliza_1_Ano, new SelectList(ViewBag.listaTipoSimNao, "ID", "Descricao"),
                                                            string.Format("{0}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.DataLimiteSazo</label>
                                                        <div class="input-group date">
                                                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>@Html.TextBoxFor(model => model.DataLimiteSazo_Texto, new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                        </div>
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.SazonalizacaoMinima (%)</label>
                                                        @Html.TextBoxFor(model => model.Sazonalizacao_Minima, "{0:0.00}", new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.SazonalizacaoMaxima (%)</label>
                                                        @Html.TextBoxFor(model => model.Sazonalizacao_Maxima, "{0:0.00}", new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                                <hr />

                                                <div class="row">
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.FlexibilidadeMinima (%)</label>
                                                        @Html.TextBoxFor(model => model.Flexibilidade_Minima, "{0:0.00}", new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.FlexibilidadeMaxima (%)</label>
                                                        @Html.TextBoxFor(model => model.Flexibilidade_Maxima, "{0:0.00}", new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.ModulacaoMinima (%)</label>
                                                        @Html.TextBoxFor(model => model.Modulacao_Minima, "{0:0.00}", new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.ModulacaoMaxima (%)</label>
                                                        @Html.TextBoxFor(model => model.Modulacao_Maxima, "{0:0.00}", new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                                <br />

                                                <div class="row">
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.Perdas (%)</label>
                                                        @Html.TextBoxFor(model => model.Perdas, "{0:0.000000}", new { @class = "form-control", @maxlength = "10", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                                <hr />

                                                <div class="row">
                                                    <div class="form-group col-lg-12">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Observacoes</label>
                                                        @Html.TextAreaFor(model => model.Observacao, new { @class = "form-control", @maxlength = "500", style = "height: 200px;", @disabled = "disabled" })
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                        <div id="tab-3" class="tab-pane">
                                            <div class="panel-body">

                                                @if (Model.IDContratoCCEE == 0) {
                                                    
                                                    <br />
                                                    <div class="row">
                                                        <div class="col-lg-1" style="text-align:center;">
                                                            <i class="fa fa-warning fa-5x"></i>
                                                        </div>
                                                        <div class="col-lg-11">
                                                            <h2>É necessário salvar o Contrato para editar os Preços Reajustados.</h2>
                                                        </div>
                                                    </div>
                                                    <br />
                                                    
                                                }
                                                else
                                                {

                                                    <div class="row">
                                                        <div class="col-lg-offset-9 col-lg-3" style="margin-top:10px;">
                                                            <div class="link_branco">
                                                                <button type="button" id="BotaoAdicionarPrecoReajustado" class="btn btn-info btn-lg pull-left" onclick="Adicionar_PrecoReajustado();" style="color:#ffffff; width:100%;" disabled="">
                                                                    <span style="font-size:large">@SmartEnergy.Resources.ContratosCCEETexts.AdicionarPrecoReajustado</span>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <br />

                                                    <div class="row">
                                                        <div class="form-group col-lg-12">
                                                            <table id="dataTables-PrecosReajustados" class="table table-striped table-bordered table-hover">
                                                                <thead>
                                                                    <tr>
                                                                        <th>ID</th>
                                                                        <th>@SmartEnergy.Resources.ConfiguracaoTexts.Data</th>
                                                                        <th>@SmartEnergy.Resources.ContratosCCEETexts.PrecoReajustado</th>
                                                                        <th></th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>

                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    <br />

                                                    <div class="row">
                                                        <div class="form-group col-lg-3">
                                                            <span id="BotaoUpload_Preco" class="btn btn-primary btn-lg fileinput-button" style="color:#ffffff; width:100%;">
                                                                <span class="modal-title"><i class="fa fa-upload"></i>&nbsp;&nbsp;Enviar Preços Reajustados</span>
                                                                <input type="file" id="fileupload_Preco" name="fileupload_Preco">
                                                            </span>
                                                            <div id="progress_Preco" class="progress_Preco">
                                                                <div class="progress-bar progress-bar-success bar" style="width: 0%; height:20px;"></div>
                                                            </div>
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <button type="button" class="btn btn-primary btn-lg modal-title" onclick="DownloadPrecosReajustados();" style="color:#ffffff; width:100%;"><i class="fa fa-download"></i>&nbsp;&nbsp;Receber Preços Reajustados</button>
                                                        </div>
                                                    </div>
                                                    
                                                }

                                            </div>
                                        </div>

                                        <div id="tab-4" class="tab-pane">
                                            <div class="panel-body">

                                                @if (Model.IDContratoCCEE == 0) {
                                                    
                                                    <br />
                                                    <div class="row">
                                                        <div class="col-lg-1" style="text-align:center;">
                                                            <i class="fa fa-warning fa-5x"></i>
                                                        </div>
                                                        <div class="col-lg-11">
                                                            <h2>É necessário salvar o Contrato para editar a Sazonalização.</h2>
                                                        </div>
                                                    </div>
                                                    <br />
                                                    
                                                }
                                                else
                                                {

                                                    <div class="row">
                                                        <div class="form-group col-lg-4">
                                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Medida</label>
                                                            <select class = "form-control" id="Unidade_Sazonalizacao" name="Unidade_Sazonalizacao">
                                                                <option value="0" selected="selected">MWh</option>
                                                                <option value="1">MWm</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-lg-offset-5 col-lg-3" style="margin-top:10px;">
                                                            <div class="link_branco">
                                                                <button type="button" id="BotaoAdicionarSazonalizacao" class="btn btn-info btn-lg pull-left" onclick="Adicionar_Sazonalizacao();" style="color:#ffffff; width:100%;" disabled="">
                                                                    <span style="font-size:large">@SmartEnergy.Resources.ContratosCCEETexts.AdicionarSazonalizacao</span>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <br />

                                                    <div class="div_sazonalizacao" style="display:block;">
                                                        <div class="row">
                                                            <div class="form-group col-lg-12">
                                                                <table id="dataTables-sazonalizacao" class="table table-striped table-bordered table-hover">
                                                                    <thead>
                                                                        <tr>
                                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Periodo</th>
                                                                            <th>Jan</th>
                                                                            <th>Fev</th>
                                                                            <th>Mar</th>
                                                                            <th>Abr</th>
                                                                            <th>Mai</th>
                                                                            <th>Jun</th>
                                                                            <th>Jul</th>
                                                                            <th>Ago</th>
                                                                            <th>Set</th>
                                                                            <th>Out</th>
                                                                            <th>Nov</th>
                                                                            <th>Dez</th>
                                                                            <th>Total</th>
                                                                            <th></th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>

                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="div_sazonalizacao_MWm" style="display:none;">
                                                        <div class="row">
                                                            <div class="form-group col-lg-12">
                                                                <table id="dataTables-sazonalizacao-MWm" class="table table-striped table-bordered table-hover">
                                                                    <thead>
                                                                        <tr>
                                                                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Periodo</th>
                                                                            <th>MWm</th>
                                                                            <th></th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>

                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <br />

                                                    <div class="row">
                                                        <div class="form-group col-lg-3">
                                                            <span id="BotaoUpload_Sazo" class="btn btn-primary btn-lg fileinput-button" style="color:#ffffff; width:100%;">
                                                                <span class="modal-title"><i class="fa fa-upload"></i>&nbsp;&nbsp;Enviar Sazonalizações</span>
                                                                <input type="file" id="fileupload_Sazo" name="fileupload_Sazo">
                                                            </span>
                                                            <div id="progress_Sazo" class="progress_Sazo">
                                                                <div class="progress-bar progress-bar-success bar" style="width: 0%; height:20px;"></div>
                                                            </div>
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <button type="button" class="btn btn-primary btn-lg modal-title" onclick="DownloadSazonalizacoes();" style="color:#ffffff; width:100%;"><i class="fa fa-download"></i>&nbsp;&nbsp;Receber Sazonalizações</button>
                                                        </div>
                                                        <div class="col-lg-offset-3 col-lg-3" style="height:20px;">
                                                            <div class="link_branco">
                                                                <button type="button" id="BotaoUtilizarVolume" class="btn btn-primary btn-lg pull-left" onclick="UtilizarVolume_Sazonalizacao();" style="color:#ffffff; width:100%;" disabled="">
                                                                    <span style="font-size:large">@SmartEnergy.Resources.ContratosCCEETexts.UtilizarVolume</span>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                }

                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="overlay_aguarde" style="display: none">
                        <div class="spinner-aguarde">
                            <div class="sk-spinner sk-spinner-wandering-cubes">
                                <div class="sk-spinner sk-spinner-wave">
                                    <div class="sk-rect1 text-ligh"></div>
                                    <div class="sk-rect2"></div>
                                    <div class="sk-rect3"></div>
                                    <div class="sk-rect4"></div>
                                    <div class="sk-rect5"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            }

            <div class="modal inmodal animated fadeIn" id="ModalContratoID" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Fechar</span></button>
                            <h4 class="modal-title"><i class="fa fa-edit"></i>&nbsp;&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.ContratoID</h4>
                        </div>
                        <div class="modal-body">

                            <div class="row">

                                @Html.Hidden("IDEmpresa_Unidade", Model.IDEmpresa)

                                <div class="form-group col-lg-6">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.RazaoSocial</label>
                                    <input class="form-control" disabled="disabled" id="RazaoSocial_Unidade" name="RazaoSocial_Unidade" type="text" value="" />
                                </div>
                                <div class="form-group col-lg-6">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.SiglaCCEE</label>
                                    <input class="form-control" disabled="disabled" id="SiglaCCEE_Unidade" name="SiglaCCEE_Unidade" type="text" value="" />
                                </div>
                            </div>
                            <br />
                            <div class="row">
                                <div class="form-group col-lg-6">
                                    <label class="control-label">&nbsp;CNPJ</label>
                                    <input class="form-control" disabled="disabled" id="CNPJ_Unidade" name="CNPJ_Unidade" type="text" value="" />
                                </div>
                                <div class="form-group col-lg-6">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.PontoMedicao</label>
                                    <input class="form-control" disabled="disabled" id="Ponto_Medicao_Unidade" name="Ponto_Medicao_Unidade" type="text" value="" />
                                </div>
                            </div>
                            <br />
                            <div class="row">
                                <div class="form-group col-lg-6">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.ContratoID</label>
                                    <input class="form-control" id="ContratoID_Unidade" name="ContratoID_Unidade" type="text" value="" />
                                </div>
                            </div>
                            <br />
                            <div class="div_percentual_carga_modal" style="display:none;">
                                <div class="row">
                                    <div class="form-group col-lg-3">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.PercentualCarga (%)</label>
                                        <input class="form-control" id="PercentualCarga_Valor_Unidade" name="PercentualCarga_Valor_Unidade" type="text" value="" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                            <button type="button" class="btn btn-primary" onclick="SalvaContratoID();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal inmodal animated fadeIn" id="ModalUnidadesNaoAtendidas" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Fechar</span></button>
                            <h4 class="modal-title"><i class="fa fa-check-square-o"></i>&nbsp;&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.AdicionarUnidadeAtendida</h4>
                        </div>
                        <div class="modal-body">

                            <div class="row">
                                <div class="form-group col-lg-12">

                                    <div class="row">
                                        <div class="form-group col-lg-12">
                                            <table id="dataTables-unidadesNaoAtendidas" class="table table-striped table-bordered table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>IDEmpresa</th>
                                                        <th>@SmartEnergy.Resources.ConfiguracaoTexts.RazaoSocial</th>
                                                        <th>@SmartEnergy.Resources.ConfiguracaoTexts.SiglaCCEE</th>
                                                        <th>CNPJ</th>
                                                        <th>@SmartEnergy.Resources.ConfiguracaoTexts.PontoMedicao</th>
                                                    </tr>
                                                </thead>
                                                <tbody>

                                                    @{
                                                        // unidades não atendidas
                                                        List<ContratosCCEE_UnidadesAtendidasDominio> unidadesNaoAtendidas = ViewBag.unidadesNaoAtendidas;

                                                        // percorre unidades não atendidas
                                                        foreach (ContratosCCEE_UnidadesAtendidasDominio unidade in unidadesNaoAtendidas)
                                                        {
                                                            <tr>
                                                                <td>@unidade.IDEmpresa</td>
                                                                <td>@unidade.RazaoSocial</td>
                                                                <td>@unidade.SiglaCCEE</td>
                                                                <td>@unidade.CNPJ</td>
                                                                <td>@unidade.PontoMedicao</td>
                                                            </tr>
                                                        }
                                                    }

                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                </div>
                            </div>

                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                            <button type="button" class="btn btn-primary" onclick="SalvaUnidadeNaoAtendida();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoInserir</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal inmodal animated fadeIn" id="ModalPrecosBase" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Fechar</span></button>
                            <h4 class="modal-title"><i class="fa fa-edit"></i>&nbsp;&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.BasePrecos</h4>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="form-group col-lg-12">

                                    <div class="row">
                                        <div class="form-group col-lg-12">
                                            <table id="dataTables-PrecosBase" class="table table-striped table-bordered table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>@SmartEnergy.Resources.ConfiguracaoTexts.Data</th>
                                                        <th>@SmartEnergy.Resources.ContratosCCEETexts.BasePreco (R$)</th>
                                                        <th></th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td></td>
                                                        <td></td>
                                                        <td></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                            <button type="button" class="btn btn-primary" onclick="SalvaPrecosBase();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal inmodal animated fadeIn" id="ModalPrecoBase_Editar" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Fechar</span></button>
                            <h4 class="modal-title"><i class="fa fa-edit"></i>&nbsp;&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.BasePreco</h4>
                        </div>
                        <div class="modal-body">

                            <div class="row">
                                <div class="form-group col-lg-12">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Data</label>
                                    <input class="form-control" disabled="disabled" id="Data_PrecoBase" name="Data_PrecoBase" type="text" value="" />
                                </div>
                            </div>
                            <br />
                            <div class="row">
                                <div class="form-group col-lg-12">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.BasePreco</label>
                                    <input class="form-control" id="Preco_PrecoBase" name="Preco_PrecoBase" type="text" value="" />
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                            <button type="button" class="btn btn-primary" onclick="AlteraPrecoBase();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoAlterar</button>
                        </div>
                    </div>
                </div>
            </div>


            <div class="modal inmodal animated fadeIn" id="ModalVolume" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Fechar</span></button>
                            <h4 class="modal-title"><i class="fa fa-edit"></i>&nbsp;&nbsp;Volume <span id="UnidadeVolume">(MWm)</span></h4>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="form-group col-lg-12">

                                    <div class="row">
                                        <div class="form-group col-lg-12">
                                            <table id="dataTables-Volumes" class="table table-striped table-bordered table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>@SmartEnergy.Resources.ConfiguracaoTexts.Data</th>
                                                        <th>Volume</th>
                                                        <th></th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td></td>
                                                        <td></td>
                                                        <td></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                            <button type="button" class="btn btn-primary" onclick="SalvaVolumes();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal inmodal animated fadeIn" id="ModalVolume_Editar" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Fechar</span></button>
                            <h4 class="modal-title"><i class="fa fa-edit"></i>&nbsp;&nbsp;Volume</h4>
                        </div>
                        <div class="modal-body">

                            <div class="row">
                                <div class="form-group col-lg-12">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Data</label>
                                    <input class="form-control" disabled="disabled" id="Data_Volume" name="Data_Volume" type="text" value="" />
                                </div>
                            </div>
                            <br />
                            <div class="row">
                                <div class="form-group col-lg-12">
                                    <label class="control-label">&nbsp;Volume</label>
                                    <input class="form-control" id="Contratado_Volume" name="Contratado_Volume" type="text" value="" />
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                            <button type="button" class="btn btn-primary" onclick="AlteraVolume();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoAlterar</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal inmodal animated fadeIn" id="ModalPrecoReajustado_Editar" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Fechar</span></button>
                            <h4 class="modal-title"><i class="fa fa-edit"></i>&nbsp;&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.PrecoReajustado (R$)</h4>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="form-group col-lg-6">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Data</label>
                                    <div class="input-group date">
                                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                        <input class="form-control" id="Data_PrecoReajustado" name="Data_PrecoReajustado" type="text" value="" />
                                    </div>
                                </div>
                            </div>
                            <br />
                            <div class="row">
                                <div class="form-group col-lg-6">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.PrecoReajustado (R$)</label>
                                    <input class="form-control" id="Valor_PrecoReajustado" name="Valor_PrecoReajustado" type="text" value="" />
                                </div>
                            </div>
                            <br />
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                            <button type="button" class="btn btn-primary" onclick="AlteraPrecoReajustado();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal inmodal animated fadeIn" id="ModalSazonalizacao_Editar" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Fechar</span></button>
                            <h4 class="modal-title"><i class="fa fa-edit"></i>&nbsp;&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.Sazonalizacao (MWh)</h4>
                        </div>
                        <div class="modal-body">

                            <div class="row">
                                <div class="form-group col-lg-12">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Ano</label>
                                    <input class="form-control" id="Ano_Sazonalizacao" name="Ano_Sazonalizacao" type="text" value="" />
                                </div>
                            </div>
                            <br />
                            <div class="row">
                                <div class="form-group col-lg-3">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Janeiro</label>
                                    <input class="form-control" id="Jan_Sazonalizacao" name="Jan_Sazonalizacao" type="text" value="" />
                                </div>
                                <div class="form-group col-lg-3">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Fevereiro</label>
                                    <input class="form-control" id="Fev_Sazonalizacao" name="Fev_Sazonalizacao" type="text" value="" />
                                </div>
                                <div class="form-group col-lg-3">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Março</label>
                                    <input class="form-control" id="Mar_Sazonalizacao" name="Mar_Sazonalizacao" type="text" value="" />
                                </div>
                                <div class="form-group col-lg-3">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Abril</label>
                                    <input class="form-control" id="Abr_Sazonalizacao" name="Abr_Sazonalizacao" type="text" value="" />
                                </div>
                            </div>
                            <br />
                            <div class="row">
                                <div class="form-group col-lg-3">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Maio</label>
                                    <input class="form-control" id="Mai_Sazonalizacao" name="Mai_Sazonalizacao" type="text" value="" />
                                </div>
                                <div class="form-group col-lg-3">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Junho</label>
                                    <input class="form-control" id="Jun_Sazonalizacao" name="Jun_Sazonalizacao" type="text" value="" />
                                </div>
                                <div class="form-group col-lg-3">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Julho</label>
                                    <input class="form-control" id="Jul_Sazonalizacao" name="Jul_Sazonalizacao" type="text" value="" />
                                </div>
                                <div class="form-group col-lg-3">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Agosto</label>
                                    <input class="form-control" id="Ago_Sazonalizacao" name="Ago_Sazonalizacao" type="text" value="" />
                                </div>
                            </div>
                            <br />
                            <div class="row">
                                <div class="form-group col-lg-3">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Setembro</label>
                                    <input class="form-control" id="Set_Sazonalizacao" name="Set_Sazonalizacao" type="text" value="" />
                                </div>
                                <div class="form-group col-lg-3">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Outubro</label>
                                    <input class="form-control" id="Out_Sazonalizacao" name="Out_Sazonalizacao" type="text" value="" />
                                </div>
                                <div class="form-group col-lg-3">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Novembro</label>
                                    <input class="form-control" id="Nov_Sazonalizacao" name="Nov_Sazonalizacao" type="text" value="" />
                                </div>
                                <div class="form-group col-lg-3">
                                    <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Dezembro</label>
                                    <input class="form-control" id="Dez_Sazonalizacao" name="Dez_Sazonalizacao" type="text" value="" />
                                </div>
                            </div>
                            <br />
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                            <button type="button" class="btn btn-primary" onclick="AlteraSazonalizacao();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoAlterar</button>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/jQuery.FileUpload/FileUploadStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
    @Styles.Render("~/plugins/select2Styles")
    @Styles.Render("~/datetimePickerStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/jQuery.FileUpload/FileUpload")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/plugins/select2")
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/plugins/iCheck")
    @Scripts.Render("~/plugins/maskMoney")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


<script type="text/javascript">

    $(document).ready(function () {

        // mask money
        $('#Reajuste_Preco').maskMoney({ thousands: '', decimal: ',' });
        $('#Base_Preco').maskMoney({ thousands: '', decimal: ',' });
        $('#Preco_PrecoBase').maskMoney({ thousands: '', decimal: ',' });
        $('#Valor_PrecoReajustado').maskMoney({ thousands: '', decimal: ',' });


        // classe iChecks
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });

        $('#Vigencia_Inicio_Texto').datetimepicker({
            locale: 'pt-BR',
            format: 'DD/MM/YYYY',
            allowInputToggle: true,
            showTodayButton: true
        });

        $('#Vigencia_Fim_Texto').datetimepicker({
            locale: 'pt-BR',
            format: 'DD/MM/YYYY',
            allowInputToggle: true,
            showTodayButton: true
        });

        $('#Base_Data_Texto').datetimepicker({
            locale: 'pt-BR',
            format: 'MM/YYYY',
            allowInputToggle: true,
            showTodayButton: true
        });

        $('#DataLimiteSazo_Texto').datetimepicker({
            locale: 'pt-BR',
            format: 'DD/MM',
            allowInputToggle: true,
            showTodayButton: true
        });

        $('#Data_PrecoReajustado').datetimepicker({
            locale: 'pt-BR',
            format: 'MM/YYYY',
            allowInputToggle: true,
            showTodayButton: true
        });

        $('#IDEmpresa').select2();

        //
        // VALIDACAO DOS CAMPOS
        //

        jQuery.validator.addMethod("alphanumeric", function (value, element) {
            return this.optional(element) || /^[\'\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\&\+\-\_\(\)," "]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

        jQuery.validator.addMethod("numeric", function (value, element) {
            return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.numeric");


        $("#form").validate({
            rules: {
                IDEmpresa: { required: true },
                Contrato_ID: { required: true, minlength: 1, maxlength: 30 },
                Contrato_Codigo: { required: true, minlength: 1, maxlength: 30 },
                Contrato_Status: { required: true },
                IDMedicao: { required: true },
                IDComercializadora: { required: true },
                ConsultorNegocio: { required: true, alphanumeric: true, minlength: 3, maxlength: 50 },
                IDTipoFonte: { required: true },
                IDSubSistema: { required: true },
                Base_Preco: { required: true, numeric: true },
                Reajuste_Mes: { required: true },
                Reajuste_Indice: { required: true },
                Reajuste_Preco: { required: true, numeric: true },
                Montante_Tipo: { required: true },
                Flexibilidade_Minima: { required: true, numeric: true },
                Flexibilidade_Maxima: { required: true, numeric: true },
                Sazonalizacao_Minima: { required: true, numeric: true },
                Sazonalizacao_Maxima: { required: true, numeric: true },
                Modulacao_Minima: { required: true, numeric: true },
                Modulacao_Maxima: { required: true, numeric: true },
                Perdas: { required: true, numeric: true },
                PercentualCarga_Valor: { required: true, numeric: true }
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            }
        });

        // desabilita campos
        disableAll();


        //
        // TABELA UNIDADES ATENDIDAS
        //

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('#dataTables-unidadesAtendidas').DataTable({
            "iDisplayLength": 10,
            dom: 'ftp',

            'order': [1, 'asc'],
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "1%" },
            { sWidth: "25%" },
            { sWidth: "14%" },
            { sWidth: "13%" },
            { sWidth: "13%" },
            { sWidth: "13%" },
            { sWidth: "12%" },
            { sWidth: "10%" },
            ],

            "columnDefs": [
                 { "targets": [0], "visible": false, "searchable": false, "orderable": false },
                 { "targets": [1], "sType": "portugues" },
                 { "targets": [2], "sType": "portugues" },
                 { "targets": [3], "sType": "portugues" },
                 { "targets": [4], "sType": "portugues" },
                 { "targets": [5], "sType": "portugues" },
                 { "targets": [6], "sType": "numero" },
                 { "targets": [7], "searchable": false, "orderable": false }
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });


        //
        // TABELA UNIDADES NÃO ATENDIDAS (MODAL)
        //

        var table_unidadesNaoAtendidas = $('#dataTables-unidadesNaoAtendidas').DataTable({
            "iDisplayLength": 10,
            dom: 'ftp',

            'order': [1, 'asc'],
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "1%" },
            { sWidth: "39%" },
            { sWidth: "20%" },
            { sWidth: "20%" },
            { sWidth: "20%" },
            ],

            "columnDefs": [
                 { "targets": [0], "visible": false, "searchable": false, "orderable": false },
                 { "targets": [1], "sType": "portugues" },
                 { "targets": [2], "sType": "portugues" },
                 { "targets": [3], "sType": "portugues" },
                 { "targets": [4], "sType": "portugues" },
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        $('#dataTables-unidadesNaoAtendidas tbody').on('click', 'tr', function () {

            if ($(this).hasClass('selected')) {

                $(this).removeClass('selected');
            }
            else {

                table_unidadesNaoAtendidas.$('tr.selected').removeClass('selected');
                $(this).addClass('selected');
            }
        });


        //
        // TABELA PREÇOS BASE (MODAL)
        //

        $('#dataTables-PrecosBase').DataTable({
            "iDisplayLength": 6,
            dom: 'ftp',

            'order': [0, 'asc'],
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "45%" },
            { sWidth: "45%" },
            { sWidth: "10%" },
            ],

            "columnDefs": [
                 { "targets": [0], "sType": "portugues" },
                 { "targets": [1], "sType": "portugues", "searchable": false, "orderable": false },
                 { "targets": [2], "searchable": false, "orderable": false }
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });


        //
        // TABELA VOLUMES (MODAL)
        //

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "date-br-pre": function (a) {

                if (a == null || a == "") {
                    return 0;
                }

                // separa
                var brDatea = a.split('/');

                // tipo do volume
                var Montante_Tipo = document.getElementById('Montante_Tipo').value;

                // tipo
                if (Montante_Tipo == 0) {
                    // caso for Montante: volume é por ano
                    return (brDatea[0]) * 1;

                }
                else {
                    // caso for Sazonalização: volume é por mês
                    return (brDatea[1] + brDatea[0]) * 1;
                }

                return (brDatea[2] + brDatea[1] + brDatea[0]) * 1;
            },

            "date-br-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "date-br-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('#dataTables-Volumes').DataTable({
            "iDisplayLength": 12,
            dom: 'ftp',

            'order': [0, 'asc'],
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "45%" },
            { sWidth: "45%" },
            { sWidth: "10%" },
            ],

            "columnDefs": [
                 { "targets": [0], "sType": "date-br" },
                 { "targets": [1], "sType": "portugues", "searchable": false, "orderable": false },
                 { "targets": [2], "searchable": false, "orderable": false }
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });


        // caso montante_tipo, zero volumes
        $('#Montante_Tipo').change(function () {

            // obtem a comercializadora
            var id = $(this).find(":selected").val();

            // zerar volumes
            ZeraVolumes();
        });

        $('#Vigencia_Inicio_Texto').datetimepicker().on('dp.change', function (ev) {

            // zerar preços base
            ZeraPrecosBase();

            // zerar volumes
            ZeraVolumes();

        });

        $('#Vigencia_Fim_Texto').datetimepicker().on('dp.change', function (ev) {

            // zerar preços base
            ZeraPrecosBase();

            // zerar volumes
            ZeraVolumes();

        });


        //
        // EMPRESA
        //

        // caso empresa mudar, atualizo CNPJ, TipoEmpresa, Estado e Cidade
        $('#IDEmpresa').change(function () {

            // obtem a empresa
            var id = $(this).find(":selected").val();

            if (id > 0) {
                // chama a Action para obter a empresa
                $.getJSON('/ContratosCCEE/ObterEmpresa', { IDEmpresa: id }, function (data) {

                    // preenche CNPJ, IDEstado e IDCidade
                    var IDCliente = 0;
                    var SiglaCCEE = "";
                    var CNPJ = "00.000.000/0000-00";
                    var IDTipoEmpresa = 0;
                    var IDEstado = 0;
                    var IDCidade = 0;
                    var NomeMedicao = "";
                    var PontoMedicao = "";
                    var Carteira = 0;
                    var IDMedicao_UnidadeConsumidora = 0;
                    var IDSubSistema = 0;

                    if (data.IDCliente != null) {
                        // copia
                        IDCliente = data.IDCliente;
                    }

                    if (data.SiglaCCEE != null) {
                        // copia
                        SiglaCCEE = data.SiglaCCEE;
                    }

                    if (data.CNPJ != null) {
                        // copia
                        CNPJ = data.CNPJ;
                    }

                    if (data.IDTipoEmpresa != null) {
                        // copia
                        IDTipoEmpresa = data.IDTipoEmpresa;
                    }

                    if (data.IDEstado != null) {
                        // copia
                        IDEstado = data.IDEstado;
                    }

                    if (data.IDCidade != null) {
                        // copia
                        IDCidade = data.IDCidade;
                    }

                    if (data.Carteira != null) {
                        // copia
                        Carteira = data.Carteira;
                    }

                    if (data.NomeMedicao != null) {
                        // copia
                        NomeMedicao = data.NomeMedicao;
                    }

                    if (data.PontoMedicao != null) {
                        // copia
                        PontoMedicao = data.PontoMedicao;
                    }

                    if (data.IDMedicao_UnidadeConsumidora != null) {
                        // copia
                        IDMedicao_UnidadeConsumidora = data.IDMedicao_UnidadeConsumidora;
                    }

                    if (data.IDSubSistema != null) {
                        // copia
                        IDSubSistema = data.IDSubSistema;
                    }

                    // SiglaCCEE, CNPJ, Estado e Cidade
                    document.getElementById('IDCliente').value = IDCliente;
                    document.getElementById('SiglaCCEE').value = SiglaCCEE;
                    document.getElementById('CNPJ').value = CNPJ;
                    document.getElementById('IDTipoEmpresa').value = IDTipoEmpresa;
                    document.getElementById('IDEstado').value = IDEstado;
                    document.getElementById('IDCidade').value = IDCidade;
                    document.getElementById('Carteira').value = Carteira;
                    document.getElementById('NomeMedicao').value = NomeMedicao;
                    document.getElementById('PontoMedicao').value = PontoMedicao;
                    document.getElementById('IDMedicao').value = IDMedicao_UnidadeConsumidora;
                    document.getElementById('IDSubSistema').value = IDSubSistema;
                });
            }

            // SiglaCCEE, CNPJ, Estado e Cidade
            document.getElementById('IDCliente').value = 0;
            document.getElementById('SiglaCCEE').value = "";
            document.getElementById('CNPJ').value = "00.000.000/0000-00";
            document.getElementById('IDTipoEmpresa').value = "";
            document.getElementById('IDEstado').value = "";
            document.getElementById('IDCidade').value = "";
            document.getElementById('Carteira').value = "";
            document.getElementById('NomeMedicao').value = "---";
            document.getElementById('PontoMedicao').value = "---";
            document.getElementById('IDMedicao').value = "";
            document.getElementById('IDSubSistema').value = "";
        });


        //
        // COMERCIALIZADORA
        //

        // caso comercializadora mudar, atualizo RazaoSocial e CNPJ
        $('#IDComercializadora').change(function () {

            // obtem a comercializadora
            var id = $(this).find(":selected").val();

            // RazaoSocial e CNPJ
            document.getElementById('Comercializadoras_RazaoSocial').value = id;
            document.getElementById('Comercializadoras_CNPJ').value = id;
        });


        //
        // PRECO BASE FLAT
        //

        // For oncheck callback
        $('#Base_Preco_FlatAux').on('ifChecked', function () {
            $('.div_preco_base_flat').css("display", "block");
            $('.div_precos_base').css("display", "none");
        });

        // For onUncheck callback
        $('#Base_Preco_FlatAux').on('ifUnchecked', function () {
            $('.div_preco_base_flat').css("display", "none");
            $('.div_precos_base').css("display", "block");

            // zerar preços base
            ZeraPrecosBase();

        });


        //
        // POSSUI PERCENTUAL DE CARGA
        //

        // For oncheck callback
        $('#PercentualCarga_PossuiAux').on('ifChecked', function () {
            $('.div_percentual_carga').css("display", "block");
            $('.div_percentual_carga_modal').css("display", "block");

            // IDContratoCCEE
            var IDContratoCCEE = document.getElementById("IDContratoCCEE").value;

            // atualizo tabela unidades atendidas
            AtualizaUnidadesAtendidas(IDContratoCCEE);

        });

        // For onUncheck callback
        $('#PercentualCarga_PossuiAux').on('ifUnchecked', function () {
            $('.div_percentual_carga').css("display", "none");
            $('.div_percentual_carga_modal').css("display", "none");

            // IDContratoCCEE
            var IDContratoCCEE = document.getElementById("IDContratoCCEE").value;

            // atualizo tabela unidades atendidas
            AtualizaUnidadesAtendidas(IDContratoCCEE);

        });


        //
        // PREÇOS REAJUSTADO
        //

        $('#dataTables-PrecosReajustados').DataTable({
            "iDisplayLength": 10,
            dom: 'tp',

            "ordering": false,
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "1%" },
            { sWidth: "40%" },
            { sWidth: "50" },
            { sWidth: "10%" },
            ],

            "columnDefs": [
                 { "targets": [0], "bVisible": false, "sType": "numero", "orderable": false },
                 { "targets": [1], "sType": "numero", "orderable": false },
                 { "targets": [2], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [3], "searchable": false, "orderable": false }
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        // apresenta preços reajustados
        ApresentaPrecosReajustados();


        $('#fileupload_Preco').fileupload({
            url: '/ContratosCCEE/UploadFile_PrecosReajustados',
            dataType: 'json',
            add: function (e, data) {
                jqXHRData = data;
                jqXHRData.submit();
            },
            done: function (e, data) {
                if (data.result.isUploaded) {

                    setTimeout(function () {

                        $('#progress_Preco .progress-bar').css(
                                                'width',
                                                0 + '%'
                                            );

                        swal({
                            title: "Enviado com sucesso",
                            text: data.result.message,
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // fecha janela
                            $('#progress_Preco .progress-bar').css(
                            'width',
                            0 + '%'
                            );

                            // atualiza pagina
                            location.reload();
                        });

                    }, 1000);
                }
                else {
                    setTimeout(function () {

                        swal({
                            title: "Erro no envio",
                            text: data.result.message,
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // fecha janela
                            $('#progress_Preco .progress-bar').css(
                            'width',
                            0 + '%'
                            );
                        });

                    }, 1000);
                }

            },
            fail: function (event, data) {
                if (data.files[0].error) {

                    swal({
                        title: "Erro no envio",
                        text: data.files[0].error,
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    }, function () {

                    });
                }
            },
            progressall: function (e, data) {
                var progress = parseInt(data.loaded / data.total * 100, 10);
                $('#progress_Preco .progress-bar').css(
                    'width',
                    progress + '%'
                );
            }
        }).prop('disabled', !$.support.fileInput)
                .parent().addClass($.support.fileInput ? undefined : 'disabled');




        //
        // SAZONALIZACAO
        //

        $('#dataTables-sazonalizacao').DataTable({
            "iDisplayLength": 10,
            dom: 'tp',

            'order': [0, 'asc'],
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "7%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "6%" },
            { sWidth: "11%" },
            { sWidth: "10%" },
            ],

            "columnDefs": [
                 { "targets": [0], "sType": "numero", "orderable": false },
                 { "targets": [1], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [2], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [3], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [4], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [5], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [6], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [7], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [8], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [9], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [10], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [11], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [12], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [13], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [14], "searchable": false, "orderable": false }
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        $('#dataTables-sazonalizacao-MWm').DataTable({
            "iDisplayLength": 10,
            dom: 'tp',

            'order': [0, 'asc'],
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "7%" },
            { sWidth: "83%" },
            { sWidth: "10%" },
            ],

            "columnDefs": [
                 { "targets": [0], "sType": "numero", "orderable": false },
                 { "targets": [1], "sType": "numero", "searchable": false, "orderable": false },
                 { "targets": [2], "searchable": false, "orderable": false }
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        // apresenta sazonalizações
        ApresentaSazonalizacao();

        // altera unidade
        $('#Unidade_Sazonalizacao').change(function () {

            // obtem a unidade
            var id = $(this).find(":selected").val();

            if (id == 0) {
                $('.div_sazonalizacao').css("display", "block");
                $('.div_sazonalizacao_MWm').css("display", "none");
            }
            else {
                $('.div_sazonalizacao').css("display", "none");
                $('.div_sazonalizacao_MWm').css("display", "block");
            }
        });

        $('#fileupload_Sazo').fileupload({
            url: '/ContratosCCEE/UploadFile_Sazonalizacoes',
            dataType: 'json',
            add: function (e, data) {
                jqXHRData = data;
                jqXHRData.submit();
            },
            done: function (e, data) {
                if (data.result.isUploaded) {

                    setTimeout(function () {

                        $('#progress_Sazo .progress-bar').css(
                                                'width',
                                                0 + '%'
                                            );

                        swal({
                            title: "Enviado com sucesso",
                            text: data.result.message,
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                            $('#progress_Sazo .progress-bar').css(
                            'width',
                            0 + '%'
                            );

                            // apresenta sazonalizações
                            ApresentaSazonalizacao();
                        });

                    }, 1000);
                }
                else {
                    setTimeout(function () {

                        swal({
                            title: "Erro no envio",
                            text: data.result.message,
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                            $('#progress_Sazo .progress-bar').css(
                            'width',
                            0 + '%'
                            );
                        });

                    }, 1000);
                }

            },
            fail: function (event, data) {
                if (data.files[0].error) {

                    swal({
                        title: "Erro no envio",
                        text: data.files[0].error,
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    }, function () {

                    });
                }
            },
            progressall: function (e, data) {
                var progress = parseInt(data.loaded / data.total * 100, 10);
                $('#progress_Sazo .progress-bar').css(
                    'width',
                    progress + '%'
                );
            }
        }).prop('disabled', !$.support.fileInput)
                .parent().addClass($.support.fileInput ? undefined : 'disabled');



        $('form input').on('keypress', function (e) {
            return e.which !== 13;
        });
    });


    function disableAll() {

        var permissao = Math.ceil(@ViewBag.Permissao);

        // verifica permissao
        switch (permissao) {
            case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
            case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
            case PERMISSOES_CONSULTOR:          // 5 - permissao de Consultor
            case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte

                $("#BotaoSalvar").attr('disabled', false);

                $("#IDEmpresa").attr('disabled', false);
                $("#BotaoAdicionarUnidade").attr('disabled', false);

                $("#Contrato_ID").attr('disabled', false);
                $("#Contrato_Codigo").attr('disabled', false);
                $("#Contrato_Status").attr('disabled', false);
                $("#Alerta_Encerramento").attr('disabled', false);
                $("#Vigencia_Inicio_Texto").attr('disabled', false);
                $("#Vigencia_Fim_Texto").attr('disabled', false);
                $("#IDComercializadora").attr('disabled', false);
                $("#ConsultorNegocio").attr('disabled', false);

                $("#IDTipoFonte").attr('disabled', false);

                $("#Reajuste_Mes").attr('disabled', false);
                $("#Reajuste_Indice").attr('disabled', false);
                $("#Reajuste_Preco").attr('disabled', false);
                $("#BotaoPrecosReajustados").attr('disabled', false);

                $("#Base_Data_Texto").attr('disabled', false);
                $("#Base_Preco_FlatAux").attr('disabled', false);
                $("#Base_Preco").attr('disabled', false);
                $("#BotaoPrecosBase").attr('disabled', false);

                $("#Montante_Tipo").attr('disabled', false);
                $("#BotaoVolume").attr('disabled', false);

                $("#Proinfa_AbateAux").attr('disabled', false);
                $("#PercentualCarga_PossuiAux").attr('disabled', false);
                $("#PercentualCarga_Valor").attr('disabled', false);

                $("#Sazonaliza_1_Ano").attr('disabled', false);
                $("#DataLimiteSazo_Texto").attr('disabled', false);

                $("#Flexibilidade_Minima").attr('disabled', false);
                $("#Flexibilidade_Maxima").attr('disabled', false);
                $("#Sazonalizacao_Minima").attr('disabled', false);
                $("#Sazonalizacao_Maxima").attr('disabled', false);
                $("#Modulacao_Minima").attr('disabled', false);
                $("#Modulacao_Maxima").attr('disabled', false);
                $("#Perdas").attr('disabled', false);

                $("#Observacao").attr('disabled', false);

                $("#BotaoAdicionarPrecoReajustado").attr('disabled', false);
                $("#BotaoAdicionarSazonalizacao").attr('disabled', false);
                $("#BotaoUtilizarVolume").attr('disabled', false);

                break;

            default:
            case PERMISSOES_GERENTE:    // 1 - permissao de Gerente: ve tudo e escreve parte
            case PERMISSOES_OPERADOR:   // 2 - permissao de Operador: ve tudo e nao pode escrever
            case PERMISSOES_PRODUCAO:   // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                $("#BotaoSalvar").attr('disabled', true);
                break;
        }

        // preço base flat
        var status = document.getElementById("Base_Preco_FlatAux").checked;

        if (status == false) {
            $('.div_preco_base_flat').css("display", "none");
            $('.div_precos_base').css("display", "block");
        }
        else {
            $('.div_preco_base_flat').css("display", "block");
            $('.div_precos_base').css("display", "none");
        }

        // possui percentual carga
        var status = document.getElementById("PercentualCarga_PossuiAux").checked;

        if (status == false) {
            $('.div_percentual_carga').css("display", "none");
            $('.div_percentual_carga_modal').css("display", "none");
        }
        else {
            $('.div_percentual_carga').css("display", "block");
            $('.div_percentual_carga_modal').css("display", "block");
        }
    }


    //
    // UNIDADES ATENDIDAS
    //

    $('#dataTables-unidadesAtendidas').on('click', '.edit-unidade', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // le dados da linha
        var data = $('#dataTables-unidadesAtendidas').DataTable().row(row).data();

        // valores da janela
        document.getElementById("IDEmpresa_Unidade").value = data[0];
        document.getElementById("RazaoSocial_Unidade").value = data[1];
        document.getElementById("SiglaCCEE_Unidade").value = data[2];
        document.getElementById("CNPJ_Unidade").value = data[3];
        document.getElementById("Ponto_Medicao_Unidade").value = data[4];
        document.getElementById("ContratoID_Unidade").value = data[5];

        if (data[7] == '-') {
            document.getElementById("PercentualCarga_Valor_Unidade").value = '0,00';
        }
        else {
            document.getElementById("PercentualCarga_Valor_Unidade").value = data[6];
        }

        // apresenta janela
        $('#ModalContratoID').modal('show');

    });

    $('#dataTables-unidadesAtendidas').on('click', '.delete-unidade', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // le dados da linha
        var data = $('#dataTables-unidadesAtendidas').DataTable().row(row).data();

        var IDEmpresa = data[0];

        // IDContratoCCEE
        var IDContratoCCEE = document.getElementById("IDContratoCCEE").value;

        // titulo
        titulo = "Deseja excluir a Unidade ?";

        swal({
            html: true,
            title: titulo,
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            $.ajax(
            {
                type: 'GET',
                url: '/ContratosCCEE/ExcluirUnidadeAtendida',
                data: { 'IDContratoCCEE': IDContratoCCEE, 'IDEmpresa': IDEmpresa },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    // atualizo tabela unidades atendidas
                    AtualizaUnidadesAtendidas(IDContratoCCEE);
                },
                error: function (response) {

                }
            });

        });

    });


    //
    // MODAL UNIDADES NAO ATENDIDAS
    //

    function ApresentaUnidadesNaoAtendidas() {
        event.stopPropagation();

        // IDContratoCCEE
        var IDContratoCCEE = document.getElementById("IDContratoCCEE").value;

        // atualiza não atendidas
        AtualizaUnidadesNaoAtendidas(IDContratoCCEE);

        // apresenta janela
        $('#ModalUnidadesNaoAtendidas').modal('show');
    }

    function SalvaUnidadeNaoAtendida() {
        event.stopPropagation();

        // IDContratoCCEE
        var IDContratoCCEE = document.getElementById("IDContratoCCEE").value;

        // Contrato_ID da garantidora
        var Contrato_ID = document.getElementById("Contrato_ID").value;

        // PercentualCarga_Valor da unidade
        var PercentualCarga_Valor_str = document.getElementById("PercentualCarga_Valor").value;
        var PercentualCarga_Valor = parseFloat(PercentualCarga_Valor_str.replace(',', '.'));


        // IDEmpresa selecionada
        var oData = $('#dataTables-unidadesNaoAtendidas').DataTable().rows('.selected').data();

        if (oData.length > 0) {
            // IDEmpresa
            var IDEmpresa_TXT = oData[0][0];

            // fecha janela
            $('#ModalUnidadesNaoAtendidas').modal('hide');

            if (IDEmpresa_TXT != '') {
                var IDEmpresa = parseInt(IDEmpresa_TXT);

                if (IDEmpresa > 0) {

                    // salvar
                    $.ajax(
                    {
                        type: 'GET',
                        url: '/ContratosCCEE/SalvarUnidadeAtendida',
                        data: { 'IDContratoCCEE': IDContratoCCEE, 'IDEmpresa': IDEmpresa, 'Contrato_ID': Contrato_ID, 'PercentualCarga_Valor': PercentualCarga_Valor },
                        contentType: 'application/html',
                        dataType: 'html',
                        cache: false,
                        async: true,
                        success: function (data) {

                            // atualizo tabela unidades atendidas
                            AtualizaUnidadesAtendidas(IDContratoCCEE);
                        },
                        error: function (response) {

                        }
                    });
                }
            }
        }
    }

    function AtualizaUnidadesAtendidas(IDContratoCCEE) {

        // chama a Action para obter as unidades atendidas
        $.getJSON('/ContratosCCEE/ObterUnidadesAtendidas', { 'IDContratoCCEE': IDContratoCCEE }, function (resultado) {

            // possui percentual carga ?
            var possui_percentual = (document.getElementById('PercentualCarga_PossuiAux').checked == true) ? true : false;

            // limpa
            $('#dataTables-unidadesAtendidas').DataTable().clear().draw();

            // percore unidades atendidas
            for (var i = 0; i < resultado.length; i++) {

                // Percentual carga
                var PercentualCarga_Valor = resultado[i].PercentualCarga_Valor;
                var PercentualCarga_Valor_str = PercentualCarga_Valor.toFixed(2);
                PercentualCarga_Valor_str = PercentualCarga_Valor_str.replace('.', ',');

                if (!possui_percentual) {
                    PercentualCarga_Valor_str = '-';
                }

                $('#dataTables-unidadesAtendidas').dataTable().fnAddData([
                    resultado[i].IDEmpresa,
                    resultado[i].RazaoSocial,
                    resultado[i].SiglaCCEE,
                    resultado[i].CNPJ,
                    resultado[i].PontoMedicao,
                    resultado[i].Contrato_ID,
                    PercentualCarga_Valor_str,
                    '<a href="#" class="edit-unidade"><i class="fa fa-edit icones" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a> <a href="#" class="delete-unidade"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>',
                ]);
            }

            // desenha tabela
            $('#dataTables-unidadesAtendidas').dataTable().draw();

        });
    }

    function AtualizaUnidadesNaoAtendidas(IDContratoCCEE) {

        // chama a Action para obter as unidades não atendidas
        $.getJSON('/ContratosCCEE/ObterUnidadesNaoAtendidas', { 'IDContratoCCEE': IDContratoCCEE }, function (resultado) {

            // limpa
            $('#dataTables-unidadesNaoAtendidas').DataTable().clear().draw();

            // percore unidades não atendidas
            for (var i = 0; i < resultado.length; i++) {

                $('#dataTables-unidadesNaoAtendidas').dataTable().fnAddData([
                    resultado[i].IDEmpresa,
                    resultado[i].RazaoSocial,
                    resultado[i].SiglaCCEE,
                    resultado[i].CNPJ,
                    resultado[i].PontoMedicao
                ]);
            }

            // desenha tabela
            $('#dataTables-unidadesNaoAtendidas').dataTable().draw();

        });
    }


    //
    // CONTRATO ID
    //

    function SalvaContratoID() {
        event.stopPropagation();

        // IDContratoCCEE
        var IDContratoCCEE = document.getElementById("IDContratoCCEE").value;

        // IDEmpresa
        var IDEmpresa_TXT = document.getElementById("IDEmpresa_Unidade").value;
        var IDEmpresa = parseInt(IDEmpresa_TXT);

        // Contrato_ID da unidade
        var Contrato_ID = document.getElementById("ContratoID_Unidade").value;

        // PercentualCarga_Valor da unidade
        var PercentualCarga_Valor_str = document.getElementById("PercentualCarga_Valor_Unidade").value;
        var PercentualCarga_Valor = parseFloat(PercentualCarga_Valor_str.replace(',', '.'));

        if (IDEmpresa > 0) {

            // fecha janela
            $('#ModalContratoID').modal('hide');

            // salvar
            $.ajax(
            {
                type: 'GET',
                url: '/ContratosCCEE/SalvarUnidadeAtendida',
                data: { 'IDContratoCCEE': IDContratoCCEE, 'IDEmpresa': IDEmpresa, 'Contrato_ID': Contrato_ID, 'PercentualCarga_Valor': PercentualCarga_Valor },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    // atualizo tabela unidades atendidas
                    AtualizaUnidadesAtendidas(IDContratoCCEE);

                    setTimeout(function () {

                        swal({
                            title: "Salvo com sucesso",
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                },
                error: function (response) {

                }
            });
        }
    }

    //
    // PREÇOS BASE
    //

    function ApresentaPrecosBase() {
        event.stopPropagation();

        // IDContratoCCEE
        var IDContratoCCEE = document.getElementById("IDContratoCCEE").value;

        // limpa
        $('#dataTables-PrecosBase').DataTable().clear().draw();

        // chama a Action para obter os preços base
        $.getJSON('/ContratosCCEE/ObterPrecosBase', { 'IDContratoCCEE': IDContratoCCEE }, function (resultado) {

            // percorre preços base
            for (var i = 0; i < resultado.length; i++) {

                // preço base
                var preco_base_str = resultado[i].Base_Preco.toString();

                // troca vírgula por ponto
                preco_base_str = preco_base_str.replace(',', '.');

                // parse valor
                var preco_base = parseFloat(preco_base_str);

                // 2 casas decimais
                preco_base_str = preco_base.toFixed(2);

                // troca ponto por vírgula
                preco_base_str = preco_base_str.replace('.', ',');

                $('#dataTables-PrecosBase').dataTable().fnAddData([
                    resultado[i].Data_Texto,
                    preco_base_str,
                    '<a href="#" class="edit-preco-base"><i class="fa fa-edit icones" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a>',
                ]);
            }

            // desenha tabela
            $('#dataTables-PrecosBase').dataTable().draw();

        });

        // apresenta janela
        $('#ModalPrecosBase').modal('show');
    }

    $('#dataTables-PrecosBase').on('click', '.edit-preco-base', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // le dados da linha
        var data = $('#dataTables-PrecosBase').DataTable().row(row).data();

        // valores da janela
        document.getElementById("Data_PrecoBase").value = data[0];
        document.getElementById("Preco_PrecoBase").value = data[1];

        // fecha janela
        $('#ModalPrecosBase').modal('hide');

        // apresenta janela
        $('#ModalPrecoBase_Editar').modal('show');

    });

    function SalvaPrecosBase() {
        event.stopPropagation();

        // IDContratoCCEE
        var IDContratoCCEE = document.getElementById("IDContratoCCEE").value;

        // lista de preços base
        var oTable = $('#dataTables-PrecosBase').dataTable();
        var rows = oTable.fnSettings().aoData;
        var dataArray = [];

        // percorre tabela
        $.each(rows, function (i, val) {

            // data
            Data_Texto = val._aData[0];

            // preço base
            var preco_base_str = val._aData[1];

            // troca vírgula por ponto
            preco_base_str = preco_base_str.replace(',', '.');

            // parse valor
            var Base_Preco = parseFloat(preco_base_str);

            dataArray.push({
                "IDContratoCCEE": IDContratoCCEE,
                "Data": Date.now(),
                "Data_Texto": Data_Texto,
                "Base_Preco": Base_Preco
            });
        });

        // constroi lista de preços
        data = { 'IDContratoCCEE': IDContratoCCEE, 'precos_base': dataArray };
        data2 = JSON.stringify(data);

        // fecha janela
        $('#ModalPrecosBase').modal('hide');

        $.ajax({
            contentType: 'application/json; charset=utf-8',
            dataType: 'json',
            url: '/ContratosCCEE/SalvarPrecosBase',
            data: data2,
            type: 'POST',
            success: function (result) {

                setTimeout(function () {

                    // verifica se erro
                    if (result.status == "ERRO") {

                        swal({
                            html: true,
                            title: "Erro",
                            text: result.erro,
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }
                    else {

                        swal({
                            title: "Salvo com sucesso",
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                        });
                    }

                }, 100);

            },
            error: function (xhr, status, error) {

                setTimeout(function () {

                    swal({
                        title: "Erro",
                        text: "Erro ao salvar!",
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    });

                }, 100);

            }
        });
    }

    function ZeraPrecosBase() {
        event.stopPropagation();

        // IDContratoCCEE
        var IDContratoCCEE = document.getElementById("IDContratoCCEE").value;

        // vigencia
        var Vigencia_Inicio_Texto = document.querySelector('[name="Vigencia_Inicio_Texto"]').value;
        var Vigencia_Fim_Texto = document.querySelector('[name="Vigencia_Fim_Texto"]').value;

        // zerar preços base
        $.ajax(
        {
            type: 'GET',
            url: '/ContratosCCEE/ZerarPrecosBase',
            data: { 'IDContratoCCEE': IDContratoCCEE, 'Vigencia_Inicio_Texto': Vigencia_Inicio_Texto, 'Vigencia_Fim_Texto': Vigencia_Fim_Texto },
            contentType: 'application/json; charset=utf-8',
            dataType: 'json',
            cache: false,
            success: function (data) {

            },
            error: function (response) {

            }
        });
    }

    function AlteraPrecoBase() {
        event.stopPropagation();

        // data a ser alterada
        var Data_Desejada = document.getElementById("Data_PrecoBase").value;

        // valor
        var preco_base_str = document.getElementById("Preco_PrecoBase").value;

        // troca vírgula por ponto
        preco_base_str = preco_base_str.replace(',', '.');

        // parse valor
        var preco_base = parseFloat(preco_base_str);

        // 2 casas decimais
        preco_base_str = preco_base.toFixed(2);

        // troca ponto por vírgula
        preco_base_str = preco_base_str.replace('.', ',');

        // lista de preços base
        var oTable = $('#dataTables-PrecosBase').dataTable();
        var rows = oTable.fnSettings().aoData;

        // percorre tabela
        $.each(rows, function (i, val) {

            // data
            Data_Texto = val._aData[0];

            // verifica se é a desejada
            if (Data_Texto == Data_Desejada) {
                // altera célula
                oTable.fnUpdate(preco_base_str, i, 1);
            }
        });

        // fecha janela
        $('#ModalPrecoBase_Editar').modal('hide');

        // apresenta janela
        $('#ModalPrecosBase').modal('show');
    }


    //
    // VOLUME
    //

    function ApresentaVolumes() {
        event.stopPropagation();

        // IDContratoCCEE
        var IDContratoCCEE = document.getElementById("IDContratoCCEE").value;

        // tipo do volume
        var Montante_Tipo = document.getElementById('Montante_Tipo').value;

        // vigencia
        var Vigencia_Inicio_Texto = document.querySelector('[name="Vigencia_Inicio_Texto"]').value;
        var Vigencia_Fim_Texto = document.querySelector('[name="Vigencia_Fim_Texto"]').value;

        // limpa
        $('#dataTables-Volumes').DataTable().clear().draw();

        // chama a Action para obter os volumes
        $.getJSON('/ContratosCCEE/ObterVolumes', { 'IDContratoCCEE': IDContratoCCEE, 'Montante_Tipo': Montante_Tipo, 'Vigencia_Inicio_Texto': Vigencia_Inicio_Texto, 'Vigencia_Fim_Texto': Vigencia_Fim_Texto }, function (resultado) {

            // percorre volumes
            for (var i = 0; i < resultado.length; i++) {

                // contratado
                var contratado_str = resultado[i].Contratado.toString();

                // troca vírgula por ponto
                contratado_str = contratado_str.replace(',', '.');

                // parse valor
                var contratado = parseFloat(contratado_str);

                // verifica se montante ou sazonalização
                if (Montante_Tipo == 0) {
                    // montante (6 casas decimais)
                    contratado_str = contratado.toFixed(6);

                    // unidade
                    document.getElementById('UnidadeVolume').innerHTML = '(MWm)';
                }
                else {
                    // sazonalização (3 casas decimais)
                    contratado_str = contratado.toFixed(3);

                    // unidade
                    document.getElementById('UnidadeVolume').innerHTML = '(MWh)';
                }

                // troca ponto por vírgula
                contratado_str = contratado_str.replace('.', ',');

                // verifica se deve apresentar
                if (Montante_Tipo == resultado[i].Montante_Tipo) {

                    $('#dataTables-Volumes').dataTable().fnAddData([
                        resultado[i].Data_Texto,
                        contratado_str,
                        '<a href="#" class="edit-contratado"><i class="fa fa-edit icones" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a>',
                    ]);

                }
            }

            // desenha tabela
            $('#dataTables-Volumes').dataTable().draw();

        });

        // apresenta janela
        $('#ModalVolume').modal('show');
    }

    $('#dataTables-Volumes').on('click', '.edit-contratado', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // le dados da linha
        var data = $('#dataTables-Volumes').DataTable().row(row).data();

        // valores da janela
        document.getElementById("Data_Volume").value = data[0];
        document.getElementById("Contratado_Volume").value = data[1];

        // fecha janela
        $('#ModalVolume').modal('hide');

        // apresenta janela
        $('#ModalVolume_Editar').modal('show');

    });

    function SalvaVolumes() {
        event.stopPropagation();

        // IDContratoCCEE
        var IDContratoCCEE = document.getElementById("IDContratoCCEE").value;

        // tipo do volume
        var Montante_Tipo = document.getElementById('Montante_Tipo').value;

        // lista de volumes
        var oTable = $('#dataTables-Volumes').dataTable();
        var rows = oTable.fnSettings().aoData;
        var dataArray = [];

        // percorre tabela
        $.each(rows, function (i, val) {

            // data
            Data_Texto = val._aData[0];

            // contratado
            var contratado_str = val._aData[1];

            // troca vírgula por ponto
            contratado_str = contratado_str.replace(',', '.');

            // parse valor
            var Contratado = parseFloat(contratado_str);

            dataArray.push({
                "IDContratoCCEE": IDContratoCCEE,
                "Data": Date.now(),
                "Data_Texto": Data_Texto,
                "Contratado": Contratado
            });
        });

        // constroi lista de volumes
        data = { 'IDContratoCCEE': IDContratoCCEE, 'Montante_Tipo': Montante_Tipo, 'volumes': dataArray };
        data2 = JSON.stringify(data);

        // fecha janela
        $('#ModalVolume').modal('hide');

        // salvar
        $.ajax({
            contentType: 'application/json; charset=utf-8',
            dataType: 'json',
            url: '/ContratosCCEE/SalvarVolumes',
            data: data2,
            type: 'POST',
            success: function (result) {

                setTimeout(function () {

                    // verifica se erro
                    if (result.status == "ERRO") {

                        swal({
                            html: true,
                            title: "Erro",
                            text: result.erro,
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }
                    else {

                        swal({
                            title: "Salvo com sucesso",
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                        });
                    }

                }, 100);

            },
            error: function (xhr, status, error) {

                setTimeout(function () {

                    swal({
                        title: "Erro",
                        text: "Erro ao salvar!",
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    });

                }, 100);

            }
        });
    }

    function ZeraVolumes() {
        event.stopPropagation();

        // IDContratoCCEE
        var IDContratoCCEE = document.getElementById("IDContratoCCEE").value;

        // tipo do volume
        var Montante_Tipo = document.getElementById("Montante_Tipo").value;

        // vigencia
        var Vigencia_Inicio_Texto = document.querySelector('[name="Vigencia_Inicio_Texto"]').value;
        var Vigencia_Fim_Texto = document.querySelector('[name="Vigencia_Fim_Texto"]').value;

        // zerar volumes
        $.ajax(
        {
            type: 'GET',
            url: '/ContratosCCEE/ZerarVolumes',
            data: { 'IDContratoCCEE': IDContratoCCEE, 'Montante_Tipo': Montante_Tipo, 'Vigencia_Inicio_Texto': Vigencia_Inicio_Texto, 'Vigencia_Fim_Texto': Vigencia_Fim_Texto },
            contentType: 'application/json; charset=utf-8',
            dataType: 'json',
            cache: false,
            success: function (data) {

            },
            error: function (response) {

            }
        });
    }

    function AlteraVolume() {
        event.stopPropagation();

        // tipo do volume
        var Montante_Tipo = document.getElementById('Montante_Tipo').value;

        // data a ser alterada
        var Data_Desejada = document.getElementById("Data_Volume").value;

        // valor
        var contratado_str = document.getElementById("Contratado_Volume").value;

        // troca vírgula por ponto
        contratado_str = contratado_str.replace(',', '.');

        // parse valor
        var contratado = parseFloat(contratado_str);

        // verifica se montante ou sazonalização
        if (Montante_Tipo == 0) {
            // montante (6 casas decimais)
            contratado_str = contratado.toFixed(6);
        }
        else {
            // sazonalização (3 casas decimais)
            contratado_str = contratado.toFixed(3);
        }

        // troca ponto por vírgula
        contratado_str = contratado_str.replace('.', ',');

        // lista de volumes
        var oTable = $('#dataTables-Volumes').dataTable();
        var rows = oTable.fnSettings().aoData;

        // percorre tabela
        $.each(rows, function (i, val) {

            // data
            Data_Texto = val._aData[0];

            // verifica se é a desejada
            if (Data_Texto == Data_Desejada) {
                // altera célula
                oTable.fnUpdate(contratado_str, i, 1);
            }
        });

        // fecha janela
        $('#ModalVolume_Editar').modal('hide');

        // apresenta janela
        $('#ModalVolume').modal('show');
    }


    //
    // PRECOS REAJUSTADOS
    //

    function ApresentaPrecosReajustados() {
        event.stopPropagation();

        // IDContratoCCEE
        var IDContratoCCEE = document.getElementById("IDContratoCCEE").value;

        // limpa
        $('#dataTables-PrecosReajustados').DataTable().clear().draw();

        // chama a Action para obter preços reajustados
        $.getJSON('/ContratosCCEE/ObterPrecosReajustados', { 'IDContratoCCEE': IDContratoCCEE }, function (resultado) {

            // percorre preços reajustados
            for (var i = 0; i < resultado.length; i++) {

                $('#dataTables-PrecosReajustados').dataTable().fnAddData([
                    resultado[i].IDPrecoReajustado,
                    resultado[i].Data_Texto,
                    resultado[i].PrecoReajustado_Texto,
                    '<a href="#" class="edit-PrecoReajustado"><i class="fa fa-edit icones" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a> <a href="#" class="delete-PrecoReajustado"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>',
                ]);
            }

            // desenha tabela
            $('#dataTables-PrecosReajustados').dataTable().draw();

        });
    }

    function Adicionar_PrecoReajustado() {
        event.stopPropagation();

        // IDContratoCCEE
        var IDContratoCCEE = document.getElementById("IDContratoCCEE").value;

        // data
        var data = new Date();
        var mes = (data.getMonth() + 1).toString();
        var mesF = (mes.length == 1) ? '0' + mes : mes;
        var anoF = data.getFullYear();
        var data_texto = mesF + "/" + anoF;

        // valores da janela
        document.getElementById("Data_PrecoReajustado").value = data_texto;
        document.getElementById("Valor_PrecoReajustado").value = "0,00";

        // apresenta janela
        $('#ModalPrecoReajustado_Editar').modal('show');
    }

    $('#dataTables-PrecosReajustados').on('click', '.edit-PrecoReajustado', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // editar
        Editar_PrecoReajustado(row);
    });

    function Editar_PrecoReajustado(row) {

        // le dados da linha
        var data = $('#dataTables-PrecosReajustados').DataTable().row(row).data();

        // valores da janela
        document.getElementById("Data_PrecoReajustado").value = data[1];
        document.getElementById("Valor_PrecoReajustado").value = data[2];

        // apresenta janela
        $('#ModalPrecoReajustado_Editar').modal('show');
    }


    $('#dataTables-PrecosReajustados').on('click', '.delete-PrecoReajustado', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // excluir
        Excluir_PrecoReajustado(row);
    });

    function Excluir_PrecoReajustado(row) {

        // le dados da linha
        var data = $('#dataTables-PrecosReajustados').DataTable().row(row).data();

        // ID e Data
        var IDPrecoReajustado = data[0];
        var Data = data[1];

        // titulo
        titulo = "Deseja excluir a Data de " + Data + " ?";

        swal({
            html: true,
            title: titulo,
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            $.ajax(
            {
                type: 'GET',
                url: '/ContratosCCEE/ExcluirPrecoReajustado',
                data: { 'IDPrecoReajustado': IDPrecoReajustado },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    // apresenta tabela preços reajustados
                    ApresentaPrecosReajustados();
                },
                error: function (response) {

                }
            });

        });
    }

    function AlteraPrecoReajustado() {
        event.stopPropagation();

        // Data
        var Data_PrecoReajustado = document.getElementById("Data_PrecoReajustado").value;

        // IDContratoCCEE
        var IDContratoCCEE = document.getElementById("IDContratoCCEE").value;

        // Data
        var Data_PrecoReajustado = document.getElementById("Data_PrecoReajustado").value;

        // Preço Reajustado
        var valor_str = document.getElementById("Valor_PrecoReajustado").value;
        var Valor_PrecoReajustado = parseFloat(valor_str.replace(',', '.'));

        // titulo
        titulo = "Deseja salvar o Preço Reajustado de " + Data_PrecoReajustado + " ?";

        swal({
            html: true,
            title: titulo,
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Salvar",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            // fecha janela
            $('#ModalPrecoReajustado_Editar').modal('hide');

            // salvar
            $.ajax(
            {
                type: 'GET',
                url: '/ContratosCCEE/SalvarPrecoReajustado',
                data: { 'IDContratoCCEE': IDContratoCCEE, 'Data_PrecoReajustado': Data_PrecoReajustado, 'Valor_PrecoReajustado': Valor_PrecoReajustado },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (result) {

                    // apresenta tabela preços reajustados
                    ApresentaPrecosReajustados();

                },
                error: function (response) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao salvar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });

        });

    }

    function DownloadPrecosReajustados() {
        event.stopPropagation();

        $.ajax(
        {
            type: 'GET',
            url: '/ContratosCCEE/DownloadFile_PrecosReajustados',
            data: {},
            contentType: "application/json;charset=utf-8",
            dataType: "json",
            cache: false,
            async: true,
            success: function (data) {

                // inicia download
                window.location = '/ContratosCCEE/PrecosReajustados_XLS_Download?fileGuid=' + data.FileGuid
                                  + '&filename=' + data.FileName;

            },
            error: function (xhr, status, error) {

                swal({
                    title: "Erro",
                    text: "Erro ao gerar XLS!",
                    type: "warning",
                    confirmButtonColor: "#f8ac59",
                    confirmButtonText: "Fechar",
                }, function () {
                });

                alert(xhr.responseText);
            }
        });
    }


    //
    // SAZONALIZACAO
    //

    function ApresentaSazonalizacao() {
        event.stopPropagation();

        // IDContratoCCEE
        var IDContratoCCEE = document.getElementById("IDContratoCCEE").value;

        // limpa
        $('#dataTables-sazonalizacao').DataTable().clear().draw();
        $('#dataTables-sazonalizacao-MWm').DataTable().clear().draw();

        // chama a Action para obter sazonalizações
        $.getJSON('/ContratosCCEE/ObterSazonalizacoes', { 'IDContratoCCEE': IDContratoCCEE }, function (resultado) {

            // percorre sazonalizacao
            for (var i = 0; i < resultado.length; i++) {

                $('#dataTables-sazonalizacao').dataTable().fnAddData([
                    resultado[i].Ano_Texto,
                    resultado[i].Jan_Texto,
                    resultado[i].Fev_Texto,
                    resultado[i].Mar_Texto,
                    resultado[i].Abr_Texto,
                    resultado[i].Mai_Texto,
                    resultado[i].Jun_Texto,
                    resultado[i].Jul_Texto,
                    resultado[i].Ago_Texto,
                    resultado[i].Set_Texto,
                    resultado[i].Out_Texto,
                    resultado[i].Nov_Texto,
                    resultado[i].Dez_Texto,
                    resultado[i].Total_Texto,
                    '<a href="#" class="edit-sazonalizacao"><i class="fa fa-edit icones" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a> <a href="#" class="delete-sazonalizacao"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>',
                ]);

                $('#dataTables-sazonalizacao-MWm').dataTable().fnAddData([
                    resultado[i].Ano_Texto,
                    resultado[i].MWm_AnoTotal_Texto,
                    '<a href="#" class="edit-sazonalizacao"><i class="fa fa-edit icones" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a> <a href="#" class="delete-sazonalizacao"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>',
                ]);
            }

            // desenha tabela
            $('#dataTables-sazonalizacao').dataTable().draw();
            $('#dataTables-sazonalizacao-MWm').dataTable().draw();

        });
    }

    function UtilizarVolume_Sazonalizacao() {
        event.stopPropagation();

        // IDContratoCCEE
        var IDContratoCCEE = document.getElementById("IDContratoCCEE").value;

        // tipo do volume
        var Montante_Tipo = document.getElementById("Montante_Tipo").value;


        // titulo
        titulo = "Deseja utilizar o Volume do Contrato na Sazonalização?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação irá substituir a Sazonalização atual pelo Volume do Contrato.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Utilizar Volume",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {


            // limpa
            $('#dataTables-sazonalizacao').DataTable().clear().draw();
            $('#dataTables-sazonalizacao-MWm').DataTable().clear().draw();

            // chama a Action para utilizar os volumes na sazonalização
            $.getJSON('/ContratosCCEE/UtilizarVolume_Sazonalizacao', { 'IDContratoCCEE': IDContratoCCEE, 'Montante_Tipo': Montante_Tipo }, function (resultado) {

                // percorre sazonalizacao
                for (var i = 0; i < resultado.length; i++) {

                    $('#dataTables-sazonalizacao').dataTable().fnAddData([
                        resultado[i].Ano_Texto,
                        resultado[i].Jan_Texto,
                        resultado[i].Fev_Texto,
                        resultado[i].Mar_Texto,
                        resultado[i].Abr_Texto,
                        resultado[i].Mai_Texto,
                        resultado[i].Jun_Texto,
                        resultado[i].Jul_Texto,
                        resultado[i].Ago_Texto,
                        resultado[i].Set_Texto,
                        resultado[i].Out_Texto,
                        resultado[i].Nov_Texto,
                        resultado[i].Dez_Texto,
                        resultado[i].Total_Texto,
                        '<a href="#" class="edit-sazonalizacao"><i class="fa fa-edit icones" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a> <a href="#" class="delete-sazonalizacao"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>',
                    ]);

                    $('#dataTables-sazonalizacao-MWm').dataTable().fnAddData([
                        resultado[i].Ano_Texto,
                        resultado[i].MWm_AnoTotal_Texto,
                        '<a href="#" class="edit-sazonalizacao"><i class="fa fa-edit icones" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a> <a href="#" class="delete-sazonalizacao"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>',
                    ]);
                }

                // desenha tabela
                $('#dataTables-sazonalizacao').dataTable().draw();
                $('#dataTables-sazonalizacao-MWm').dataTable().draw();

            });

        });
    }

    function Adicionar_Sazonalizacao() {
        event.stopPropagation();

        // IDContratoCCEE
        var IDContratoCCEE = document.getElementById("IDContratoCCEE").value;

        // ano
        var Ano = new Date().getFullYear();

        // valores da janela
        document.getElementById("Ano_Sazonalizacao").value = Ano.toString();
        document.getElementById("Jan_Sazonalizacao").value = "0,000";
        document.getElementById("Fev_Sazonalizacao").value = "0,000";
        document.getElementById("Mar_Sazonalizacao").value = "0,000";
        document.getElementById("Abr_Sazonalizacao").value = "0,000";
        document.getElementById("Mai_Sazonalizacao").value = "0,000";
        document.getElementById("Jun_Sazonalizacao").value = "0,000";
        document.getElementById("Jul_Sazonalizacao").value = "0,000";
        document.getElementById("Ago_Sazonalizacao").value = "0,000";
        document.getElementById("Set_Sazonalizacao").value = "0,000";
        document.getElementById("Out_Sazonalizacao").value = "0,000";
        document.getElementById("Nov_Sazonalizacao").value = "0,000";
        document.getElementById("Dez_Sazonalizacao").value = "0,000";

        // apresenta janela
        $('#ModalSazonalizacao_Editar').modal('show');
    }

    $('#dataTables-sazonalizacao').on('click', '.edit-sazonalizacao', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // editar
        Editar_Sazonalizacao(row);
    });

    $('#dataTables-sazonalizacao-MWm').on('click', '.edit-sazonalizacao', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // editar
        Editar_Sazonalizacao(row);
    });

    function Editar_Sazonalizacao(row) {

        // le dados da linha
        var data = $('#dataTables-sazonalizacao').DataTable().row(row).data();

        // valores da janela
        document.getElementById("Ano_Sazonalizacao").value = data[0];
        document.getElementById("Jan_Sazonalizacao").value = data[1];
        document.getElementById("Fev_Sazonalizacao").value = data[2];
        document.getElementById("Mar_Sazonalizacao").value = data[3];
        document.getElementById("Abr_Sazonalizacao").value = data[4];
        document.getElementById("Mai_Sazonalizacao").value = data[5];
        document.getElementById("Jun_Sazonalizacao").value = data[6];
        document.getElementById("Jul_Sazonalizacao").value = data[7];
        document.getElementById("Ago_Sazonalizacao").value = data[8];
        document.getElementById("Set_Sazonalizacao").value = data[9];
        document.getElementById("Out_Sazonalizacao").value = data[10];
        document.getElementById("Nov_Sazonalizacao").value = data[11];
        document.getElementById("Dez_Sazonalizacao").value = data[12];

        // apresenta janela
        $('#ModalSazonalizacao_Editar').modal('show');
    }


    $('#dataTables-sazonalizacao').on('click', '.delete-sazonalizacao', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // excluir
        Excluir_Sazonalizacao(row);
    });

    $('#dataTables-sazonalizacao-MWm').on('click', '.delete-sazonalizacao', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // excluir
        Excluir_Sazonalizacao(row);
    });

    function Excluir_Sazonalizacao(row) {

        // le dados da linha
        var data = $('#dataTables-sazonalizacao').DataTable().row(row).data();

        // ano
        var Ano = data[0];

        // IDContratoCCEE
        var IDContratoCCEE = document.getElementById("IDContratoCCEE").value;

        // titulo
        titulo = "Deseja excluir o Ano de " + Ano + " ?";

        swal({
            html: true,
            title: titulo,
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            $.ajax(
            {
                type: 'GET',
                url: '/ContratosCCEE/ExcluirSazonalizacao',
                data: { 'IDContratoCCEE': IDContratoCCEE, 'Ano': Ano },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    // apresenta tabela sazonalizacao
                    ApresentaSazonalizacao();
                },
                error: function (response) {

                }
            });

        });
    }

    function AlteraSazonalizacao() {
        event.stopPropagation();

        // IDContratoCCEE
        var IDContratoCCEE = document.getElementById("IDContratoCCEE").value;

        // Ano
        var Ano_TXT = document.getElementById("Ano_Sazonalizacao").value;
        var Ano = parseInt(Ano_TXT);

        if (Ano < 2000 || Ano > 2100) {
            swal({
                html: true,
                title: "Erro",
                text: "Digite um valor de Ano correto",
                type: "warning",
                confirmButtonColor: "#f8ac59",
                confirmButtonText: "Fechar",
            }, function () {

            });
        }
        else {
            // janeiro
            var valor_str = document.getElementById("Jan_Sazonalizacao").value;
            var Jan_Valor = parseFloat(valor_str.replace(',', '.'));

            // fevereiro
            valor_str = document.getElementById("Fev_Sazonalizacao").value;
            var Fev_Valor = parseFloat(valor_str.replace(',', '.'));

            // março
            valor_str = document.getElementById("Mar_Sazonalizacao").value;
            var Mar_Valor = parseFloat(valor_str.replace(',', '.'));

            // abril
            valor_str = document.getElementById("Abr_Sazonalizacao").value;
            var Abr_Valor = parseFloat(valor_str.replace(',', '.'));

            // maio
            valor_str = document.getElementById("Mai_Sazonalizacao").value;
            var Mai_Valor = parseFloat(valor_str.replace(',', '.'));

            // junho
            valor_str = document.getElementById("Jun_Sazonalizacao").value;
            var Jun_Valor = parseFloat(valor_str.replace(',', '.'));

            // julho
            valor_str = document.getElementById("Jul_Sazonalizacao").value;
            var Jul_Valor = parseFloat(valor_str.replace(',', '.'));

            // agosto
            valor_str = document.getElementById("Ago_Sazonalizacao").value;
            var Ago_Valor = parseFloat(valor_str.replace(',', '.'));

            // setembro
            valor_str = document.getElementById("Set_Sazonalizacao").value;
            var Set_Valor = parseFloat(valor_str.replace(',', '.'));

            // outubro
            valor_str = document.getElementById("Out_Sazonalizacao").value;
            var Out_Valor = parseFloat(valor_str.replace(',', '.'));

            // novembro
            valor_str = document.getElementById("Nov_Sazonalizacao").value;
            var Nov_Valor = parseFloat(valor_str.replace(',', '.'));

            // dezembro
            valor_str = document.getElementById("Dez_Sazonalizacao").value;
            var Dez_Valor = parseFloat(valor_str.replace(',', '.'));

            // titulo
            titulo = "Deseja salvar a Sazonalização do Ano de " + Ano + " ?";

            swal({
                html: true,
                title: titulo,
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#18a689",
                confirmButtonText: "Salvar",
                cancelButtonText: "Cancelar",
                closeOnConfirm: true
            }, function () {

                // fecha janela
                $('#ModalSazonalizacao_Editar').modal('hide');

                // salvar
                $.ajax(
                {
                    type: 'GET',
                    url: '/ContratosCCEE/SalvarSazonalizacao',
                    data: { 'IDContratoCCEE': IDContratoCCEE, 'Ano': Ano, 'Jan': Jan_Valor, 'Fev': Fev_Valor, 'Mar': Mar_Valor, 'Abr': Abr_Valor, 'Mai': Mai_Valor, 'Jun': Jun_Valor, 'Jul': Jul_Valor, 'Ago': Ago_Valor, 'Set': Set_Valor, 'Out': Out_Valor, 'Nov': Nov_Valor, 'Dez': Dez_Valor },
                    contentType: 'application/html',
                    dataType: 'html',
                    cache: false,
                    async: true,
                    success: function (result) {

                        // apresenta tabela sazonalizacao
                        ApresentaSazonalizacao();

                    },
                    error: function (response) {

                        setTimeout(function () {

                            swal({
                                title: "Erro",
                                text: "Erro ao salvar!",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            });

                        }, 100);

                    }
                });

            });

        }
    }

    function DownloadSazonalizacoes() {
        event.stopPropagation();

        // IDContratoCCEE
        var IDContratoCCEE = document.getElementById("IDContratoCCEE").value;

        $.ajax(
        {
            type: 'GET',
            url: '/ContratosCCEE/DownloadFile_Sazonalizacoes',
            data: { 'IDContratoCCEE': IDContratoCCEE },
            contentType: "application/json;charset=utf-8",
            dataType: "json",
            cache: false,
            async: true,
            success: function (data) {

                // inicia download
                window.location = '/ContratosCCEE/Sazonalizacoes_XLS_Download?fileGuid=' + data.FileGuid
                                  + '&filename=' + data.FileName;

            },
            error: function (xhr, status, error) {

                swal({
                    title: "Erro",
                    text: "Erro ao gerar XLS!",
                    type: "warning",
                    confirmButtonColor: "#f8ac59",
                    confirmButtonText: "Fechar",
                }, function () {
                });

                alert(xhr.responseText);
            }
        });
    }


    //
    // BOTAO SALVAR (SUBMIT)
    //

    $.fn.serializeObject = function () {
        var o = {};
        var a = this.serializeArray();
        $.each(a, function () {
            if (o[this.name] !== undefined) {
                if (!o[this.name].push) {
                    o[this.name] = [o[this.name]];
                }
                o[this.name].push(this.value || '');
            } else {
                o[this.name] = this.value || '';
            }
        });
        return o;
    };

    function Salvar() {

        var $form = $('#form');

        // apresenta primeiro tab que tiver erro
        var IsTabValid = true;
        var TabInvalid = 1;

        $(".tab-content").find("div.tab-pane").each(function (index, tab) {
            var id = $(tab).attr("id");
            $('a[href="#' + id + '"]').tab('show');

            IsTabValid = $("#form").valid();

            if (!IsTabValid) {

                TabInvalid = id;
                return false;
            }
        });

        // apresenta primeira tab ou com erro
        $('a[href="#tab-' + TabInvalid + '"]').tab('show');

        // verifica se entradas sao validas
        if (!IsTabValid) return false;

        // IDContratoCCEE
        var IDContratoCCEEDesejado = document.getElementById("IDContratoCCEE").value;

        // checkbox
        document.getElementById('Alerta_Encerramento').value = (document.getElementById('Alerta_EncerramentoAux').checked == true) ? true : false;
        document.getElementById('Base_Preco_Flat').value = (document.getElementById('Base_Preco_FlatAux').checked == true) ? true : false;
        document.getElementById('Proinfa_Abate').value = (document.getElementById('Proinfa_AbateAux').checked == true) ? true : false;
        document.getElementById('PercentualCarga_Possui').value = (document.getElementById('PercentualCarga_PossuiAux').checked == true) ? true : false;

        // deseja salvar
        swal({
            title: "Deseja salvar?",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#1ab394",
            confirmButtonText: "Salvar",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {
            event.stopPropagation();

            // aguarde
            $('.overlay_aguarde').toggle();

            // retiro campos desabilitados, pois nao envia via POST
            $(":disabled", $('#form')).removeAttr("disabled");

            // constroi estrutura com FORM
            data = { 'contrato': $form.serializeObject() };
            data2 = JSON.stringify(data);

            $.ajax({
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                url: '/ContratosCCEE/ContratoCCEE_Salvar',
                data: data2,
                type: 'POST',
                success: function (result) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        // verifica se erro
                        if (result.status == "ERRO") {
                            swal({
                                html: true,
                                title: "Erro",
                                text: result.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                            });
                        }
                        else {
                            swal({
                                title: "Salvo com sucesso",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // verifica se está adicionando contrato novo
                                // suponho que está salvando para poder criar alguma unidade atendida
                                if (result.IDContratoCCEE > 0 && IDContratoCCEEDesejado == 0) {
                                    // releio a pagina e posiciono na aba empresa
                                    var url = '/ContratosCCEE/ContratoCCEE_Editar?IDContratoCCEE=' + result.IDContratoCCEE.toString();
                                    window.location.href = url;
                                }
                                else {
                                    // redireciona para pagina lista de contratos
                                    var url = '/ContratosCCEE/ContratosCCEE';
                                    window.location.href = url;
                                }

                            });
                        }

                    }, 100);

                },
                error: function (xhr, status, error) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao salvar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });
    };

</script>

}
