﻿@model IEnumerable<SmartEnergyLib.SQL.ContratosCCEEDominio>

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralContratosCCEE;
}


<style>

    .overlay_aguarde {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.45);
        z-index: 9999;
        color: white;
        display: inline-block;
        border-radius: 6px;
        margin-bottom: 20px;
        margin-left: 14px;
        margin-right: 14px;
    }

    .sk-spinner-wave div {
        background-color: #ffffff !important;
    }

    .spinner-aguarde {
        margin: 0;
        position: absolute;
        top: 50%;
        left: 50%;
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
    }

    .fundo_azul {
        background: #293846;
        text-align: center;
    }

</style>

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralContratosCCEE</h4>
                    <div class="pull-right dashboard-tools link_branco" style="margin-top:-8px;">
                        <h4>
                            <a href='@("/ContratosCCEE/ContratoCCEE_Editar?IDContratoCCEE=0")' id="BotaoAdicionar" title="@SmartEnergy.Resources.ComumTexts.BotaoInserir"><i class="fa fa-plus"></i></a>
                            <a data-toggle="modal" href="#modalPrecosReajustados" id="BotaoPrecosReajustados" title="@SmartEnergy.Resources.ComumTexts.BotaoPrecosReajustados"><i class="fa fa-dollar"></i></a>
                        </h4>
                    </div>
                </div>

                <div class="modal inmodal animated fadeIn" id="modalPrecosReajustados" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                                <h4 class="modal-title"><i class="fa fa-dollar"></i>&nbsp;&nbsp;Preços Reajustados</h4>
                            </div>
                            <div class="modal-body" style="min-height:100px;">
                                <div class="row">
                                    <div class="form-group col-lg-12">
                                        <h2>Atualizar os contratos com os <b>Preços Reajustados</b> da planilha selecionada.</h2>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group col-lg-12" id="BotaoUploadDIV">
                                        <span id="BotaoUpload" class="btn btn-primary btn-lg fileinput-button" style="color:#ffffff; width:100%;">
                                            <span class="modal-title"><i class="fa fa-upload"></i>&nbsp;&nbsp;Enviar</span>
                                            <input type="file" id="fileupload" name="fileupload">
                                        </span>
                                        <div id="progress" class="progress">
                                            <div class="progress-bar progress-bar-success bar" style="width: 0%; height:20px;"></div>
                                        </div>
                                    </div>
                                </div>
                                <hr />

                                <div class="row">
                                    <div class="form-group col-lg-12">
                                        <h2>Receber a planilha com os <b>Preços Reajustados</b> atuais dos contratos.</h2>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group col-lg-12">
                                        <button type="button" class="btn btn-primary btn-lg modal-title" onclick="DownloadPrecosReajustados();" style="color:#ffffff; width:100%;"><i class="fa fa-download"></i>&nbsp;&nbsp;Receber</button>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel-body">

                    <div class="row">
                        <div class="form-group col-lg-4">
                            <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.ContratoStatus</label>
                            @Html.DropDownList("Contrato_Status", new SelectList(ViewBag.listaTipoContratoStatus_MesAtual, "ID", "Descricao", "1"),
                                                            "Todos os Contratos",
                                                            new { @class = "form-control", style="width:100%" })
                        </div>
                        <div class="div_AlterarStatus form-group col-lg-3" style="margin-top:12px; display:none;">
                            <div class="link_branco">
                                <button type="button" id="BotaoAlterarStatus" class="btn btn-info btn-lg pull-left" onclick="AlterarStatusVigente();" style="color:#ffffff; width:100%;">
                                    <span style="font-size:large">Alterar status para VIGENTE</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <table id="dataTables-contratos" class="table table-striped table-bordered table-hover">
                        <thead>
                            <tr>
                                <th></th>
                                <th></th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.SiglaCCEE</th>
                                <th>@SmartEnergy.Resources.ContratosCCEETexts.ContratoCodigo</th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.ContratoStatus</th>
                                <th>@SmartEnergy.Resources.ContratosCCEETexts.Fornecedor</th>
                                <th>@SmartEnergy.Resources.ContratosCCEETexts.VigenciaInicio</th>
                                <th>@SmartEnergy.Resources.ContratosCCEETexts.VigenciaFim</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>

                            @{
                                List<AgentesDominio> listatiposComercializadoras = ViewBag.listaTipoComercializadoras;

                                foreach (ContratosCCEEDominio contrato in Model)
                                {
                                    string logo = "http://www.smartenergy.com.br";

                                    if (contrato.Logo.IsEmpty())
                                    {
                                        logo += "/Logos/LogoSmartEnergy.png";
                                    }
                                    else
                                    {
                                        logo += "/Logos/" + contrato.Logo;
                                    }

                                    // comercializadora
                                    AgentesDominio tipo_comercializadora = listatiposComercializadoras.Find(item => item.IDAgente == contrato.IDComercializadora);
                                    string comercializadora = "---";
                                    if (tipo_comercializadora != null)
                                    {
                                        comercializadora = tipo_comercializadora.Nome;
                                    }

                                    // status do contrato
                                    List<ListaTiposDominio> listaTipoContratoStatus = ViewBag.listaTipoContratoStatus;
                                    ListaTiposDominio tipo = listaTipoContratoStatus.Find(item => item.ID == contrato.Contrato_Status);
                                    var Contrato_Status = tipo.Descricao;

                                    <tr>
                                        <td>@contrato.IDContratoCCEE</td>
                                        <td bgcolor="#293846" align="center"><img src=@logo title="@contrato.NomeCliente" style="max-height:45px; height: auto;" /></td>
                                        <td><b>@contrato.SiglaCCEE</b><br />@contrato.RazaoSocial</td>
                                        <td>@contrato.Contrato_Codigo</td>
                                        <td>@Contrato_Status</td>
                                        <td>@comercializadora</td>
                                        <td><span style="display:none;">@contrato.Vigencia_Inicio_Sort_aux</span>@contrato.Vigencia_Inicio_aux</td>
                                        <td><span style="display:none;">@contrato.Vigencia_Fim_Sort_aux</span>@contrato.Vigencia_Fim_aux</td>

                                        <td class="link_preto">
                                            <a href='@("/ContratosCCEE/ContratoCCEE_Editar?IDContratoCCEE=" + @contrato.IDContratoCCEE.ToString())' title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"><i class="fa fa-edit icones"></i></a>

                                            @if (ViewBag.Permissao == PERMISSOES.ADMIN || ViewBag.Permissao == PERMISSOES.SUPORTE || ViewBag.Permissao == PERMISSOES.SUPER_CONSULTOR)
                                            {
                                                <a href="#" onclick="javascript:Excluir(@contrato.IDContratoCCEE);" id="BotaoExcluir" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-times icones"></i></a>
                                            }

                                        </td>
                                    </tr>
                                }
                                
                            }

                        </tbody>
                    </table>

                </div>

                <div class="overlay_aguarde" style="display: none">
                    <div class="spinner-aguarde">
                        <div class="sk-spinner sk-spinner-wandering-cubes">
                            <div class="sk-spinner sk-spinner-wave">
                                <div class="sk-rect1 text-ligh"></div>
                                <div class="sk-rect2"></div>
                                <div class="sk-rect3"></div>
                                <div class="sk-rect4"></div>
                                <div class="sk-rect5"></div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/jQuery.FileUpload/FileUploadStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/jQuery.FileUpload/FileUpload")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">


    // Array holding selected row IDs
    var rows_selected_contratos = [];


    $(document).ready(function () {

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {
                var x = (a == "-") ? 0 : a.replace(/,/, ".");
                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        var tableContratos = $('#dataTables-contratos').DataTable({
            "iDisplayLength": 12,
            dom: 'ftp',

            "bAutoWidth": false,
            "aoColumnDefs": [
                        {
                            "aTargets": [0], "sType": "numero", "bSortable": false, "bSearchable": false, 'sClass': 'dt-center',
                            "mRender": function (data, type, full) { return '<input type="checkbox" name="checkbox_contratos">'; },
                        },
                        { "aTargets": [1], "sType": "portugues", "bSortable": false, "bSearchable": false, 'sClass': 'fundo_azul' },
                        { "aTargets": [2], "sType": "portugues" },
                        { "aTargets": [3], "sType": "portugues" },
                        { "aTargets": [4], "sType": "portugues" },
                        { "aTargets": [5], "sType": "portugues" },
                        { "aTargets": [6], "sType": "portugues" },
                        { "aTargets": [7], "sType": "portugues" },
                        { "aTargets": [8], "bVisible": true, "bSortable": false, "bSearchable": false, 'sClass': 'link_preto' },
            ],
            "aoColumns": [
            { sWidth: "1%" },
            { sWidth: "10%" },
            { sWidth: "20%" },
            { sWidth: "14%" },
            { sWidth: "12%" },
            { sWidth: "12%" },
            { sWidth: "11%" },
            { sWidth: "11%" },
            { sWidth: "10%" }
            ],
            'order': [2, 'asc'],

            'rowCallback': function (row, data, dataIndex) {
                // Get row ID
                var rowId = data[0];

                // If row ID is in the list of selected row IDs
                if ($.inArray(rowId, rows_selected_contratos) !== -1) {
                    $(row).find('input[type="checkbox"]').prop('checked', true);
                    $(row).addClass('selected');
                }
            },

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

        // manipula checkbox
        $('#dataTables-contratos tbody').on('click', 'input[type="checkbox"]', function (e) {
            var $row = $(this).closest('tr');

            // Get row data
            var data = tableContratos.row($row).data();

            // Get row ID
            var rowId = data[0];

            // Determine whether row ID is in the list of selected row IDs
            var index = $.inArray(rowId, rows_selected_contratos);

            // If checkbox is checked and row ID is not in list of selected row IDs
            if (this.checked && index === -1) {
                rows_selected_contratos.push(rowId);

                // Otherwise, if checkbox is not checked and row ID is in list of selected row IDs
            } else if (!this.checked && index !== -1) {
                rows_selected_contratos.splice(index, 1);
            }

            if (this.checked) {
                $row.addClass('selected');
            } else {
                $row.removeClass('selected');
            }

            // Prevent click event from propagating to parent
            e.stopPropagation();
        });

        // manipula click na tabela
        $('#dataTables-contratos').on('click', 'tbody td, thead th:first-child', function (e) {
            $(this).parent().find('input[type="checkbox"]').trigger('click');
        });


        $('#fileupload').fileupload({
            url: '/ContratosCCEE/UploadFile_PrecosReajustados',
            dataType: 'json',
            add: function (e, data) {
                jqXHRData = data;
                jqXHRData.submit();
            },
            done: function (e, data) {
                if (data.result.isUploaded) {

                    setTimeout(function () {

                        $('#progress .progress-bar').css(
                                                'width',
                                                0 + '%'
                                            );

                        swal({
                            title: "Enviado com sucesso",
                            text: data.result.message,
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // fecha janela
                            $('#modalPrecosReajustados').modal('hide');
                            $('#progress .progress-bar').css(
                            'width',
                            0 + '%'
                            );

                            // atualiza pagina
                            location.reload();
                        });

                    }, 1000);
                }
                else {
                    setTimeout(function () {

                        swal({
                            title: "Erro no envio",
                            text: data.result.message,
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // fecha janela
                            $('#modalPrecosReajustados').modal('hide');
                            $('#progress .progress-bar').css(
                            'width',
                            0 + '%'
                            );
                        });

                    }, 1000);
                }

            },
            fail: function (event, data) {
                if (data.files[0].error) {

                    swal({
                        title: "Erro no envio",
                        text: data.files[0].error,
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    }, function () {

                        // fecha janela
                        $('#modalPrecosReajustados').modal('hide');

                    });
                }
            },
            progressall: function (e, data) {
                var progress = parseInt(data.loaded / data.total * 100, 10);
                $('#progress .progress-bar').css(
                    'width',
                    progress + '%'
                );
            }
        }).prop('disabled', !$.support.fileInput)
                .parent().addClass($.support.fileInput ? undefined : 'disabled');


        // caso contrato status, altero tabela
        $('#Contrato_Status').change(function () {

            // obtem o status
            var id = $(this).find(":selected").val();

            // verifica se vazio
            if (id == '') {
                id = 0;
            }

            // verifica se NAO INICIADO mês atual
            if (id == 4 || id == 5) {
                $('.div_AlterarStatus').css("display", "block");
            }
            else {
                $('.div_AlterarStatus').css("display", "none");
            }

            // atualiza os contratos
            AtualizaContratos(id);

        });

    });

    function AtualizaContratos(Contrato_Status) {

        // aguarde
        $('.overlay_aguarde').toggle();

        // chama a Action para obter os contratos
        $.getJSON('/ContratosCCEE/ObterContratos', { Contrato_Status: Contrato_Status }, function (contratos) {

            // aguarde
            $('.overlay_aguarde').toggle();

            // permissão
            var permissao = Math.ceil(@ViewBag.Permissao);

            // limpa
            $('#dataTables-contratos').DataTable().clear().draw();

            // percore contratos
            for (var i = 0; i < contratos.length; i++) {

                // logo
                var logo = "http://www.smartenergy.com.br";

                if (contratos[i].Logo == "") {
                    logo += "/Logos/LogoSmartEnergy.png";
                }
                else {
                    logo += "/Logos/" + contratos[i].Logo;
                }

                // status do contrato
                var Contrato_Status = "---";

                switch (contratos[i].Contrato_Status) {
                    case 1: // vigente
                        Contrato_Status = "VIGENTE";
                        break;

                    case 2: // rescindido
                        Contrato_Status = "RESCINDIDO";
                        break;

                    case 3: // encerrado
                        Contrato_Status = "ENCERRADO";
                        break;

                    case 4: // não iniciado
                        Contrato_Status = "NÃO INICIADO";
                        break;
                }

                // icones
                var icones = '<a href=\"/ContratosCCEE/ContratoCCEE_Editar?IDContratoCCEE=' + contratos[i].IDContratoCCEE.toString() + '\" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"><i class="fa fa-edit icones"></i></a>';

                if (permissao == PERMISSOES_ADMIN || permissao == PERMISSOES_CONSULTOR || permissao == PERMISSOES_SUPER_CONSULTOR) {
                    icones += '<a href="#" onclick="javascript:Excluir(' + contratos[i].IDContratoCCEE + ');" id="BotaoExcluir" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-times icones"></i></a>';
                }

                // coloca na tabela
                $('#dataTables-contratos').dataTable().fnAddData([
                    contratos[i].IDContratoCCEE,
                    '<img src=' + logo + ' title="' + contratos[i].NomeCliente + '" style="max-height:45px; height: auto;" />',
                    '<b>' + contratos[i].SiglaCCEE + '</b><br />' + contratos[i].RazaoSocial,
                    contratos[i].Contrato_ID,
                    contratos[i].Contrato_Codigo,
                    Contrato_Status,
                    '<span style="display:none;">' + contratos[i].Vigencia_Inicio_Sort_aux + '</span>' + contratos[i].Vigencia_Inicio_aux,
                    '<span style="display:none;">' + contratos[i].Vigencia_Fim_Sort_aux + '</span>' + contratos[i].Vigencia_Fim_aux,
                    icones
                ]);
            }

            // desenha tabela
            $('#dataTables-contratos').dataTable().draw();

        });

    }

    function AlterarStatusVigente() {
        event.stopPropagation();

        // verifica se não tem selecionado
        if(rows_selected_contratos == null || rows_selected_contratos.length == 0)
        { 
            
            swal({
                title: "Selecionar o contrato !",
                type: "warning",
                confirmButtonColor: "#f8ac59",
                confirmButtonText: "Fechar",
            });

            return;
        }


        // titulo
        titulo = "Deseja alterar o status dos contratos selecionados para VIGENTE?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Alterar",
            cancelButtonText: "Cancelar",
            closeOnConfirm: false
        }, function () {

            // alterar status
            $.ajax(
            {
                type: 'GET',
                url: '/ContratosCCEE/ContratoCCEE_AlterarStatusVigente',
                data: { 'contratos': rows_selected_contratos },
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                traditional: true,
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {
                            swal({
                                html: true,
                                title: "Erro",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                            });
                        }
                        else {
                            swal({
                                title: "Alterado com sucesso",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // atualiza pagina
                                location.reload();
                            });
                        }

                    }, 100);
                },
                error: function (response) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao alterar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });
    }



    function DownloadPrecosReajustados() {
        event.stopPropagation();

        $.ajax(
        {
            type: 'GET',
            url: '/ContratosCCEE/DownloadFile_PrecosReajustados',
            data: {},
            contentType: "application/json;charset=utf-8",
            dataType: "json",
            cache: false,
            async: true,
            success: function (data) {

                // inicia download
                window.location = '/ContratosCCEE/PrecosReajustados_XLS_Download?fileGuid=' + data.FileGuid
                                  + '&filename=' + data.FileName;

                // fecha janela
                $('#modalPrecosReajustados').modal('hide');

            },
            error: function (xhr, status, error) {

                // fecha janela
                $('#modalPrecosReajustados').modal('hide');

                swal({
                    title: "Erro",
                    text: "Erro ao gerar XLS!",
                    type: "warning",
                    confirmButtonColor: "#f8ac59",
                    confirmButtonText: "Fechar",
                }, function () {
                });

                alert(xhr.responseText);
            }
        });
    }


    function Excluir(IDContratoCCEE) {
        event.stopPropagation();

        // titulo
        titulo = "Deseja excluir o contrato?<br/>ID " + IDContratoCCEE;

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: false
        }, function () {

            // excluir
            $.ajax(
            {
                type: 'GET',
                url: '/ContratosCCEE/ContratoCCEE_Excluir',
                data: { 'IDContratoCCEE': IDContratoCCEE },
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {
                            swal({
                                html: true,
                                title: "Erro",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                            });
                        }
                        else {
                            swal({
                                title: "Excluído com sucesso",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // atualiza pagina
                                location.reload();
                            });
                        }

                    }, 100);
                },
                error: function (response) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao excluir!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });
    };

</script>
}


