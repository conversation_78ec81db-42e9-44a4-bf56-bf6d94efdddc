﻿@model SmartEnergyLib.SQL.ContratosCCEE_Grupos

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralContratosCCEE_Grupos;
}

<style>

    .icones {
        padding: 0px 5px;
        font-size: 18px;
        float: left;
    }

    .titulo {
        margin: 0;
        margin-left: 0px;
        margin-top: 20px;
        margin-bottom: 15px;
        padding-left: 15px;
        padding-top: 5px;
        padding-bottom: 5px;
        font-size: 12px;
        font-weight: bold;
        border-radius: 10px;
        float: left;
    }

    .link_branco a:link {
        color: #ffffff !important;
    }
    .link_branco a:visited {
        color: #ffffff  !important;
    }
    .link_branco a:hover {
        color: #f0f0f0  !important;
    }
    .link_branco a:active {
        color: #ffffff  !important;
    }

    .link_preto a:link {
        color: #7E6A6C !important;
    }
    .link_preto a:visited {
        color: #7E6A6C  !important;
    }
    .link_preto a:hover {
        color: #a0a0a0  !important;
    }
    .link_preto a:active {
        color: #7E6A6C  !important;
    }

    table.dataTable.select tbody tr,
    table.dataTable thead th:first-child {
        cursor: pointer;
    }

    #dataTables-contratos tbody tr.selected {
            color: white;
            background-color: #293846;
        }

    #dataTables-contratosVigentes tbody tr.selected {
            color: white;
            background-color: #293846;
        }

    .overlay_aguarde {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.45);
        z-index: 9999;
        color: white;
        display: inline-block;
        border-radius: 6px;
        margin-bottom: 20px;
        margin-left: 14px;
        margin-right: 14px;
    }

    .sk-spinner-wave div {
        background-color: #ffffff !important;
    }
    
    .spinner-aguarde {
        margin: 0;
        position: absolute;
        top: 50%;
        left: 50%;
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
    }

</style>


<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("ContratosCCEE_Grupos_Editar", "ContratosCCEE", FormMethod.Post, new { id = "form", role = "form" }))
            {
                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>@SmartEnergy.Resources.ContratosCCEETexts.GrupoContratos</h4>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="form-group col-lg-2">
                                <label class="control-label">&nbsp;ID</label>

                                @{
                                    if (Model.IDContratoCCEE_Grupo == 0)
                                    {
                                            @Html.Hidden("IDContratoCCEE_Grupo", Model.IDContratoCCEE_Grupo)
                                            @Html.TextBox("Novo", "Novo Grupo", new { @class = "form-control", @disabled = "disabled" })
                                    }
                                    else
                                    {
                                            @Html.TextBoxFor(model => model.IDContratoCCEE_Grupo, new { @class = "form-control", @disabled = "disabled" })
                                    }
                                }

                            </div>
                            <div class="col-lg-offset-8 col-lg-2">
                                <div class="ibox-tools">
                                    <a href='@("/ContratosCCEE/ContratosCCEE_Grupos")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                    <button type="button" class="btn btn-primary" id="BotaoSalvar" onclick="Salvar();">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                </div>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            <label class="control-label">&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Nome</label>
                                            @Html.TextBoxFor(model => model.Nome, new { @class = "form-control", @maxlength = "50" })
                                        </div>
                                    </div>
                                    <br />

                                    <div class="row">
                                        <div class="col-lg-3" style="margin-top:10px;">
                                            <div class="link_branco">
                                                <button type="button" id="BotaoAdicionarContrato" class="btn btn-info btn-lg pull-left" onclick="Adicionar_Contrato();" style="color:#ffffff; width:100%;">
                                                    <span style="font-size:large">@SmartEnergy.Resources.ContratosCCEETexts.AdicionarContrato</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <br />

                                    <div class="row">
                                        <div class="form-group col-lg-12">
                                            <table id="dataTables-contratos" class="table table-striped table-bordered table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>Sequência</th>
                                                        <th>IDContratoCCEE</th>
                                                        <th>Ordem</th>
                                                        <th></th>
                                                        <th>@SmartEnergy.Resources.ConfiguracaoTexts.SiglaCCEE</th>
                                                        <th>@SmartEnergy.Resources.ContratosCCEETexts.ContratoID</th>
                                                        <th>@SmartEnergy.Resources.ContratosCCEETexts.ContratoCodigo</th>
                                                        <th>@SmartEnergy.Resources.ContratosCCEETexts.Considera</th>
                                                        <th></th>
                                                        <th>IDContratoCCEE_Grupo</th>
                                                    </tr>
                                                </thead>
                                                <tbody>

                                                    @{
                                                        List<ContratosCCEE_GruposOrdem> contratosGrupo = ViewBag.contratosGrupo;

                                                        foreach (ContratosCCEE_GruposOrdem contratoGrupo in contratosGrupo)
                                                        {
                                                            string logo = "http://www.smartenergy.com.br";

                                                            if (contratoGrupo.Logo.IsEmpty())
                                                            {
                                                                logo += "/Logos/LogoSmartEnergy.png";
                                                            }
                                                            else
                                                            {
                                                                logo += "/Logos/" + contratoGrupo.Logo;
                                                            }

                                                            var considera = "NÃO";
                                                            
                                                            if (contratoGrupo.Considera == 1)
                                                            {
                                                                considera = "SIM";
                                                            }
                                                            
                                                            <tr>
                                                                <td class="reordena">@contratoGrupo.Ordem</td>
                                                                <td class="reordena">@contratoGrupo.IDContratoCCEE</td>
                                                                <td class="reordena">@contratoGrupo.Ordem</td>
                                                                <td class="reordena" bgcolor="#293846" align="center"><img src=@logo title="@contratoGrupo.NomeCliente" style="max-height:45px; height: auto;" /></td>
                                                                <td class="reordena"><b>@contratoGrupo.SiglaCCEE</b><br />@contratoGrupo.RazaoSocial</td>
                                                                <td class="reordena">@contratoGrupo.Contrato_ID</td>
                                                                <td class="reordena">@contratoGrupo.Contrato_Codigo</td>
                                                                <td>@considera</td>
                                                                <td class="link_preto">
                                                                    <a href="#"><i class="fa fa-edit icones" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a>
                                                                    <a href="#" class="confirm-delete-contrato"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>
                                                                </td>
                                                                <td>@contratoGrupo.IDContratoCCEE_Grupo</td>
                                                            </tr>
                                                        }
                                                    }

                                                </tbody>
                                            </table>

                                        </div>
                                    </div>

                                    <div class="div_gerenciamento" style="display:none;">
                                        <div class="row">
                                            <div class="form-group col-lg-4">
                                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.Considera</label><br />
                                                <select class="form-control" id="Considera" onchange="AlteraConsidera();">
                                                    <option value="0">NÃO</option>
                                                    <option value="1">SIM</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="div_gerenciamento_selecione" style="display:block;">
                                        <div class="row">
                                            <div class="form-group col-lg-9">
                                                <label><i class="fa fa-exclamation-circle icones"></i>&nbsp;Adicione os Contratos de Energia para este grupo.</label><br />
                                                <label><i class="fa fa-exclamation-circle icones"></i>&nbsp;Selecione o contrato desejado e edite as opções.</label><br />
                                                <label><i class="fa fa-exclamation-circle icones"></i>&nbsp;Arraste o Contrato para alterar a sequência.</label>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>

                        <div class="modal inmodal animated fadeIn" id="ModalContratos" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                                        <h4 class="modal-title"><i class="fa fa-check-square-o"></i>&nbsp;&nbsp;@SmartEnergy.Resources.ContratosCCEETexts.SelecioneContrato</h4>
                                    </div>
                                    <div class="modal-body">

                                        <table id="dataTables-contratosVigentes" class="table table-striped table-bordered table-hover">
                                            <thead>
                                                <tr>
                                                    <th></th>
                                                    <th>IDContratoCCEE</th>
                                                    <th></th>
                                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.SiglaCCEE</th>
                                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.ContratoID</th>
                                                    <th>@SmartEnergy.Resources.ContratosCCEETexts.ContratoCodigo</th>
                                                </tr>
                                            </thead>
                                            <tbody>

                                                @{
                                                    List<ContratosCCEEDominio> contratosVigentes = ViewBag.contratosVigentes;

                                                    foreach (ContratosCCEEDominio contratoVigente in contratosVigentes)
                                                    {
                                                        string logo = "http://www.smartenergy.com.br";

                                                        if (contratoVigente.Logo.IsEmpty())
                                                        {
                                                            logo += "/Logos/LogoSmartEnergy.png";
                                                        }
                                                        else
                                                        {
                                                            logo += "/Logos/" + contratoVigente.Logo;
                                                        }
                                                        
                                                        <tr>
                                                            <td>@contratoVigente.IDContratoCCEE</td>
                                                            <td>@contratoVigente.IDContratoCCEE</td>
                                                            <td bgcolor="#293846" align="center"><img src=@logo title="@contratoVigente.NomeCliente" style="max-height:45px; height: auto;" /></td>
                                                            <td><b>@contratoVigente.SiglaCCEE</b><br />@contratoVigente.RazaoSocial</td>
                                                            <td>@contratoVigente.Contrato_ID</td>
                                                            <td>@contratoVigente.Contrato_Codigo</td>
                                                        </tr>
                                                    }
                                                }

                                            </tbody>
                                        </table>

                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                        <button type="button" class="btn btn-primary" onclick="fnClickAddRowContratosVigentes();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoInserir</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="overlay_aguarde" style="display: none">
                        <div class="spinner-aguarde">
                            <div class="sk-spinner sk-spinner-wandering-cubes">
                                <div class="sk-spinner sk-spinner-wave">
                                    <div class="sk-rect1 text-ligh"></div>
                                    <div class="sk-rect2"></div>
                                    <div class="sk-rect3"></div>
                                    <div class="sk-rect4"></div>
                                    <div class="sk-rect5"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            }

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/dataTables")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


<script type="text/javascript">

    // Array holding selected row IDs
    var rows_selected_contratos = [];
    var names_selected_contratos_logo = [];
    var names_selected_contratos_siglaCCEE = [];
    var names_selected_contratos_contratoID = [];
    var names_selected_contratos_contratoCodigo = [];

    var names_selected_contratos_IDContratoCCEE_Grupo = [];
    var names_selected_contratos_Considera = [];


    $(document).ready(function () {

        //
        // VALIDACAO DOS CAMPOS
        //

        jQuery.validator.addMethod("alphanumeric", function (value, element) {
            return this.optional(element) || /^[\'\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\+\-\_\(\)," "]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

        jQuery.validator.addMethod("numeric", function (value, element) {
            return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.numeric");

        $("#form").validate({
            rules: {
                Nome: {
                    required: true,
                    alphanumeric: true,
                    minlength: 3,
                    maxlength: 50
                }
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            }
        });

        // desabilita campos
        disableAll();


        //
        // TABELA CONTRATOS
        //

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        var tableContratos = $('#dataTables-contratos').DataTable({
            rowReorder: {
                selector: '.reordena',
                update: true
            },
            "iDisplayLength": 10,
            dom: 'ftp',
            "bAutoWidth": false,
            "aoColumnDefs": [
                     { "aTargets": [0], "bSortable": true, "bSearchable": false },
                     { "aTargets": [1], "bVisible": false, "bSortable": false, "bSearchable": false },
                     { "aTargets": [2], "bVisible": false, "bSortable": false, "bSearchable": false },
                     { "aTargets": [3], "sType": "portugues", "bSortable": false, "bSearchable": false, 'sClass': 'fundo_azul' },
                     { "aTargets": [4], "sType": "portugues", "bSortable": false },
                     { "aTargets": [5], "sType": "portugues", "bSortable": false },
                     { "aTargets": [6], "sType": "portugues", "bSortable": false },
                     { "aTargets": [7], "sType": "portugues", "bSortable": false },
                     { "aTargets": [8], "bSortable": false, "bSearchable": false },
                     { "aTargets": [9], "bVisible": false, "bSortable": false, "bSearchable": false },
            ],
            "aoColumns": [
            { sWidth: "7%" },
            { sWidth: "1%" },
            { sWidth: "1%" },
            { sWidth: "10%" },
            { sWidth: "25%" },
            { sWidth: "16%" },
            { sWidth: "16%" },
            { sWidth: "16%" },
            { sWidth: "10%" },
            { sWidth: "1%" },
            ],
            'order': [0, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });


        //
        // TABELA CONTRATOS - SELECAO
        //

        $('#dataTables-contratos tbody').on('click', 'tr', function () {

            if ($(this).hasClass('selected')) {

                // retira selecao atual
                $(this).removeClass('selected');

                // desabilita
                $(".div_gerenciamento *").prop('disabled', true);
                $('.div_gerenciamento').css("display", "none");
                $('.div_gerenciamento_selecione').css("display", "block");

            }
            else {

                // habilita se nao for cliente operador
                var permissao = Math.ceil(@ViewBag.Permissao);

                if (permissao != PERMISSOES_GERENTE && permissao != PERMISSOES_OPERADOR)
                {
                    // habilita
                    $(".div_gerenciamento *").prop('disabled', false);
                    $('.div_gerenciamento').css("display", "block");
                    $('.div_gerenciamento_selecione').css("display", "none");
                }

                // dataTable
                var table = $('#dataTables-contratos').DataTable();

                // retira selecao atual
                table.$('tr.selected').removeClass('selected');

                // seleciona
                $(this).addClass('selected');

                // pega dados da linha selecionada
                var data = $('#dataTables-contratos').DataTable().row(this).data();
                var id = data[1];
                var Considera = data[7];

                if (Considera == "SIM")
                {
                    document.getElementById('Considera').value = "1";
                }
                else
                {
                    document.getElementById('Considera').value = "0";
                }
            }
        });

        //
        // TABELA CONTRATOS VIGENTES (MODAL)
        //

        var tableContratosVigentes = $('#dataTables-contratosVigentes').DataTable({
            "iDisplayLength": 6,
            dom: 'ftp',

            'order': [3, 'asc'],
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "5%" },
            { sWidth: "1%" },
            { sWidth: "10%" },
            { sWidth: "30%" },
            { sWidth: "30%" },
            { sWidth: "25%" }
            ],

            'columnDefs': [{
                'targets': 0,
                'searchable': false,
                'orderable': false,
                'className': 'dt-center',
                'render': function (data, type, full, meta) {
                    return '<input type="checkbox" name="checkbox_modal_contratos">';
                },
            },
            {
                'targets': 1,
                'visible': false,
                'searchable': false,
                'orderable': false,
            }],

            'rowCallback': function (row, data, dataIndex) {
                // Get row ID
                var rowId = data[0];

                // If row ID is in the list of selected row IDs
                if ($.inArray(rowId, rows_selected_contratos) !== -1) {
                    $(row).find('input[type="checkbox"]').prop('checked', true);
                    $(row).addClass('selected');
                }
            },

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        // manipula checkbox
        $('#dataTables-contratosVigentes tbody').on('click', 'input[type="checkbox"]', function (e) {
            var $row = $(this).closest('tr');

            // Get row data
            var data = tableContratosVigentes.row($row).data();

            // Get row ID
            var rowId = data[0];
            var name_logo = data[2];
            var name_siglaCCEE = data[3];
            var name_contratoID = data[4];
            var name_contratoCodigo = data[5];

            // Determine whether row ID is in the list of selected row IDs
            var index = $.inArray(rowId, rows_selected_contratos);

            // If checkbox is checked and row ID is not in list of selected row IDs
            if (this.checked && index === -1) {
                rows_selected_contratos.push(rowId);
                names_selected_contratos_logo.push(name_logo);
                names_selected_contratos_siglaCCEE.push(name_siglaCCEE);
                names_selected_contratos_contratoID.push(name_contratoID);
                names_selected_contratos_contratoCodigo.push(name_contratoCodigo);

                names_selected_contratos_IDContratoCCEE_Grupo.push(0);
                names_selected_contratos_Considera.push("NÃO");

                // Otherwise, if checkbox is not checked and row ID is in list of selected row IDs
            } else if (!this.checked && index !== -1) {
                rows_selected_contratos.splice(index, 1);
                names_selected_contratos_logo.splice(index, 1);
                names_selected_contratos_siglaCCEE.splice(index, 1);
                names_selected_contratos_contratoID.splice(index, 1);
                names_selected_contratos_contratoCodigo.splice(index, 1);

                names_selected_contratos_IDContratoCCEE_Grupo.splice(index, 1);
                names_selected_contratos_Considera.splice(index, 1);
            }

            if (this.checked) {
                $row.addClass('selected');
            } else {
                $row.removeClass('selected');
            }

            // Prevent click event from propagating to parent
            e.stopPropagation();
        });

        // manipula click na tabela
        $('#dataTables-contratosVigentes').on('click', 'tbody td, thead th:first-child', function (e) {

            $(this).parent().find('input[type="checkbox"]').trigger('click');
        });


        $('form input').on('keypress', function (e) {
            return e.which !== 13;
        });
    });



    function Adicionar_Contrato() {
        event.stopPropagation();

        // apresenta janela
        $('#ModalContratos').modal('show');
    }

    //
    // VALOR CONSIDERA
    //

    function SetCell(item, valor) {

        // dataTable
        var table = $('#dataTables-contratos').DataTable();
        var data = table.row('.selected').data();

        // verifica se tem selecionado
        if (data != undefined) {
            data[item] = valor;
            table.row('.selected').data(data).draw();
        }
    }

    function AlteraConsidera() {

        // le valor
        var x = document.getElementById("Considera");
        var text = x.options[x.selectedIndex].text;

        // coloca na tabela
        SetCell(7, text);

    }


    //
    // DESABILITA CAMPOS
    //

    function disableAll() {

        var permissao = Math.ceil(@ViewBag.Permissao);

        // verifica permissao
        switch (permissao) {
            case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
            case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
            case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte

                break;

            default:
            case PERMISSOES_GERENTE:    // 1 - permissao de Gerente: ve tudo e escreve parte
            case PERMISSOES_OPERADOR:   // 2 - permissao de Operador: ve tudo e nao pode escrever
            case PERMISSOES_PRODUCAO:   // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)
            case PERMISSOES_CONSULTOR:  // 5 - permissao de Consultor

                $("#BotaoSalvar").attr('disabled', true);
                $("#Nome").attr('disabled', true);
                $("#Unidade").attr('disabled', true);
                $("#BotaoAdicionarContrato").attr('disabled', true);

                break;
        }

    }

    //
    // BOTAO ADICIONA CONTRATOS VIGENTES
    //

    function fnClickAddRowContratosVigentes() {

        // sequencia
        var seq = 0;

        // percorre lista de selecionados
        $.each(rows_selected_contratos, function (index, rowId) {

            // procura na tabela se ja existe iD
            var table = $('#dataTables-contratos').dataTable();
            row_count = table.fnGetData().length;
            var linhas = table.fnGetData();

            var achou = false;

            for (var j = 0; j < row_count; j++) {

                if (linhas[j][0] == rowId) {
                    achou = true;
                }
            }

            // insere na tabela se nao achou
            if (achou == false) {

                // incrementa sequencia
                seq = seq + 1;

                $('#dataTables-contratos').dataTable().fnAddData([
                    seq,
                    rowId,
                    seq,
                    names_selected_contratos_logo[index],
                    names_selected_contratos_siglaCCEE[index],
                    names_selected_contratos_contratoID[index],
                    names_selected_contratos_contratoCodigo[index],
                    names_selected_contratos_Considera[index],
                    '<a href="#"><i class="fa fa-edit icones link_preto" title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"></i></a><a href="#" class="confirm-delete-contrato link_preto"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>',
                    names_selected_contratos_IDContratoCCEE_Grupo[index]]);
            }
        });

        // apaga arrays
        rows_selected_contratos = [];
        names_selected_contratos_logo = [];
        names_selected_contratos_siglaCCEE = [];
        names_selected_contratos_contratoID = [];
        names_selected_contratos_contratoCodigo = [];

        names_selected_contratos_IDContratoCCEE_Grupo = [];
        names_selected_contratos_Considera = [];

        // desmarca checkbox
        var boxes = document.getElementsByName("checkbox_modal_contratos");
        for (var i = 0; i < boxes.length; i++)
            boxes[i].checked = false;

        // remove da lista
        $('#dataTables-contratosVigentes').dataTable().$('tr.selected').empty();
        $('#dataTables-contratosVigentes').dataTable().draw();

        // desenha tabela
        $('#dataTables-contratos').dataTable().draw();
    }

    //
    // BOTAO APAGAR CONTRATO
    //

    $('#dataTables-contrato').on('click', '.confirm-delete-contrato', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // le dados da linha
        var data = $('#dataTables-contratos').DataTable().row(row).data();

        var rowId = data[1];

        // titulo
        titulo = "Deseja excluir o contrato da lista?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            setTimeout(function () {

                // apaga linha solicitada
                $('#dataTables-contratos').dataTable().fnDeleteRow(row);

                // desenha tabela
                $('#dataTables-contratos').dataTable().draw();

            }, 100);

        });

    });


    //
    // BOTAO SALVAR (SUBMIT)
    //

    $.fn.serializeObject = function () {
        var o = {};
        var a = this.serializeArray();
        $.each(a, function () {
            if (o[this.name] !== undefined) {
                if (!o[this.name].push) {
                    o[this.name] = [o[this.name]];
                }
                o[this.name].push(this.value || '');
            } else {
                o[this.name] = this.value || '';
            }
        });
        return o;
    };

    function Salvar() {

        var $form = $('#form');

        // verifica se entradas sao validas
        if (!$form.valid()) return false;

        // lista de contratos
        var oTable = $('#dataTables-contratos').dataTable();
        var rows = oTable.fnSettings().aoData;
        var ordem = 0;
        var IDContratoCCEE_Grupo = 0;
        var IDContratoCCEE = 0;
        var considera = 0;
        var dataArray = [];

        // percorre tabela
        $.each(rows, function (i, val) {

            // pega valores
            ordem = parseInt(val._aData[0]);
            IDContratoCCEE = parseInt(val._aData[1]);
            IDContratoCCEE_Grupo = parseInt(val._aData[9]);

            var considera_str = val._aData[7];

            if (considera_str == "SIM")
            {
                considera = 1;
            }
            else
            {
                considera = 0;
            }

            dataArray.push({
                "IDContratoCCEE_Grupo": IDContratoCCEE_Grupo,
                "IDContratoCCEE": IDContratoCCEE,
                "Ordem": ordem,
                "Considera": considera,

                "NomeCliente": "",
                "Logo": "",
                "RazaoSocial": "",
                "SiglaCCEE": "",
                "CNPJ": "",
                "Contrato_ID": "",
                "Contrato_Codigo": ""
            });
        });

        // deseja salvar
        swal({
            title: "Deseja salvar?",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#1ab394",
            confirmButtonText: "Salvar",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {
            event.stopPropagation();

            // aguarde
            $('.overlay_aguarde').toggle();

            // retiro campos desabilitados, pois nao envia via POST
            $(":disabled", $('#form')).removeAttr("disabled");

            // constroi estrutura com FORM e lista de medicoes
            data = { 'grupo': $form.serializeObject(), 'grupoOrdem': dataArray };
            data2 = JSON.stringify(data);

            $.ajax({
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                url: '/ContratosCCEE/ContratosCCEE_Grupos_Salvar',
                data: data2,
                type: 'POST',
                success: function (data) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {
                            swal({
                                html: true,
                                title: "Erro",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                            });
                        }
                        else {
                            swal({
                                title: "Salvo com sucesso",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                                closeOnConfirm: true
                            }, function () {

                                // redireciona para pagina lista de grupos
                                var url = '/ContratosCCEE/ContratosCCEE_Grupos';
                                window.location.href = url;
                            });
                        }

                    }, 100);

                },
                error: function (xhr, status, error) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao salvar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });

    };

</script>
}
