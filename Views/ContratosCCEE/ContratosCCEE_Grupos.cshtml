﻿@model IEnumerable<SmartEnergyLib.SQL.ContratosCCEE_Grupos>

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralContratosCCEE_Grupos;
}

<style>
    .icones {
        padding: 0px 5px;
        font-size: 18px;
        float: left;
    }

    .titulo {
        margin: 0;
        margin-left: 0px;
        margin-top: 20px;
        margin-bottom: 15px;
        padding-left: 15px;
        padding-top: 5px;
        padding-bottom: 5px;
        font-size: 12px;
        font-weight: bold;
        border-radius: 10px;
        float: left;
    }

    .link_branco a:link {
        color: #ffffff !important;
    }
    .link_branco a:visited {
        color: #ffffff  !important;
    }
    .link_branco a:hover {
        color: #f0f0f0  !important;
    }
    .link_branco a:active {
        color: #ffffff  !important;
    }

    .link_preto a:link {
        color: #7E6A6C !important;
    }
    .link_preto a:visited {
        color: #7E6A6C  !important;
    }
    .link_preto a:hover {
        color: #a0a0a0  !important;
    }
    .link_preto a:active {
        color: #7E6A6C  !important;
    }

</style>

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralContratosCCEE_GruposEnergia</h4>
                    <div class="pull-right dashboard-tools link_branco" style="margin-top:-8px;">
                        <h4>
                            <a href='@("/ContratosCCEE/ContratosCCEE_Grupos_Editar?IDContratoCCEE_Grupo=0")' id="BotaoAdicionar" title="@SmartEnergy.Resources.ComumTexts.BotaoInserir"><i class="fa fa-plus"></i></a>
                        </h4>
                    </div>
                </div>
                <div class="panel-body">
                    <table class="table table-striped table-bordered table-hover dataTables-grupos">
                        <thead>
                            <tr>
                                <th>@SmartEnergy.Resources.MenuTexts.MenuLateralContratosCCEE_Grupos</th>
                                <th>@SmartEnergy.Resources.ContratosCCEETexts.NumeroContratos</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>

                            @{
                                foreach (ContratosCCEE_Grupos grupo in Model)
                                {
                                    <tr>
                                        <td>@grupo.Nome</td>
                                        <td>@grupo.NumContratos</td>
                                        <td class="link_preto">
                                            <a href='@("/ContratosCCEE/ContratosCCEE_Grupos_Editar?IDContratoCCEE_Grupo=" + @grupo.IDContratoCCEE_Grupo.ToString())' title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"><i class="fa fa-edit icones"></i></a>

                                            @if (ViewBag.Permissao == PERMISSOES.ADMIN || ViewBag.Permissao == PERMISSOES.SUPORTE || ViewBag.Permissao == PERMISSOES.SUPER_CONSULTOR)
                                            {
                                                <a href="#" onclick="javascript:Excluir(@grupo.IDContratoCCEE_Grupo, '@grupo.Nome');" id="BotaoExcluir" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-times icones"></i></a>
                                            }

                                        </td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


<script type="text/javascript">

    $(document).ready(function () {

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {
                var x = (a == "-") ? 0 : a.replace(/,/, ".");
                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-grupos').DataTable({
            "iDisplayLength": 18,
            dom: 'ftp',

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "portugues" },
                        { "aTargets": [1], "sType": "numero" },
                        { "aTargets": [2], "bVisible": true, "bSortable": false, "bSearchable": false },
            ],

            "aoColumns": [
            { sWidth: "60%" },
            { sWidth: "30%" },
            { sWidth: "10%" },
            ],
            'order': [0, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

        // desabilita campos
        disableAll();

    });

    function disableAll() {

        var permissao = Math.ceil(@ViewBag.Permissao);

        // verifica permissao
        switch(permissao)
        {
            case PERMISSOES_ADMIN:              // 0 - permissao de Admin: ve e escreve em tudo
            case PERMISSOES_SUPORTE:            // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
            case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte

                break;

            default:
            case PERMISSOES_GERENTE:    // 1 - permissao de Gerente: ve tudo e escreve parte
            case PERMISSOES_OPERADOR:   // 2 - permissao de Operador: ve tudo e nao pode escrever
            case PERMISSOES_PRODUCAO:   // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)
            case PERMISSOES_CONSULTOR:  // 5 - permissao de Consultor

                document.getElementById("BotaoAdicionar").style.visibility = "hidden";
                break;
        }
    }

    function Excluir(IDContratoCCEE_Grupo, Nome) {
        event.stopPropagation();

        // titulo
        titulo = "Deseja excluir o Grupo?<br/>" + Nome;

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: false
        }, function () {

            // excluir
            $.ajax(
            {
                type: 'GET',
                url: '/ContratosCCEE/ContratosCCEE_Grupos_Excluir',
                data: { 'IDContratoCCEE_Grupo': IDContratoCCEE_Grupo },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        swal({
                            title: "Excluído com sucesso",
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                            // atualiza pagina
                            location.reload();
                        });

                    }, 100);
                },
                error: function (response) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao excluir!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });
    };

</script>
}


