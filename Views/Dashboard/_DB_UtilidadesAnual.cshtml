﻿@model SmartEnergyLib.SQL.DashboardDominio

@{
    string portlet_str = "portlet-util";

    if (Model.IDSubTipoDashboard == 1)
    {
        portlet_str = "portlet-ponta";
    }
    
    <input type="hidden" id="@ViewBag.IDDashboardTxt" name="@ViewBag.IDDashboardTxt" value="@ViewBag.DataAtual">
    
    <div class="portlet-valorAtual @portlet_str">
        <h1>@ViewBag.Valor_MaxAno <span>@ViewBag.UnidadeGrandeza</span></h1>
        <h5>@SmartEnergy.Resources.DashboardTexts.MaximoAno</h5>
    </div>
    <div class="portlet-datahoraAtual @portlet_str">
        <h6>@ViewBag.DataHoraAtual</h6>
    </div>
    <div class="portlet-tabela @portlet_str">
        <table class="table no-margins">
            <thead>
                <tr>
                    <th style="width: 34%;"></th>
                    <th style="width: 33%;">@SmartEnergy.Resources.DashboardTexts.Medio</th>
                    <th style="width: 33%;">@SmartEnergy.Resources.DashboardTexts.Total</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="text-align:center">@ViewBag.NomeGrandeza</td>
                    <td>@ViewBag.Valor_MediaAno @ViewBag.UnidadeGrandeza</td>
                    <td>@ViewBag.Valor_TotalAno @ViewBag.UnidadeGrandeza</td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="portlet-grafico @portlet_str">
        <div>
            <div id="<EMAIL>"></div>
        </div>
    </div>
}
    
<script type="text/javascript">

    $(document).ready(function () {

        // utilidades
        var dataValor = [];

        // copia valores
        var Valor = @Html.Raw(Json.Encode(@ViewBag.Valor));
        var Status = @Html.Raw(Json.Encode(@ViewBag.Status));
        var Dias = @Html.Raw(Json.Encode(@ViewBag.Dias));
        var NomeGrandeza = @Html.Raw(Json.Encode(@ViewBag.NomeGrandeza));
        var UnidadeGrandeza = @Html.Raw(Json.Encode(@ViewBag.UnidadeGrandeza));

        var minimoValor = @Html.Raw(Json.Encode(@ViewBag.Valor_min));
        var maximoValor = @Html.Raw(Json.Encode(@ViewBag.Valor_max));

        if (minimoValor == 0.0 && maximoValor == 0.0)
        {
            maximoValor = 1.0;
        }

        var cor_barra = "#1C84C6";

        if (@Model.IDSubTipoDashboard == 1)
        {
            cor_barra = "#FF0000";
        }

        dataValor.push(['data1']);

        for (i = 0; i < 12; i++)
        {
            dataValor.push([Valor[i]]);
        }

        var Data = [dataValor];

        var Meses = [JAN, FEV, MAR, ABR, MAI, JUN, JUL, AGO, SET, OUT, NOV, DEZ];

        c3.generate({
            bindto: '#<EMAIL>',
            size: {
                width: 342,
                height: 192
            },
            padding: {
                top: 8,
                right: 18,
                bottom: 35,
                left: 43
            },
            data: {
                columns: Data,
                types: {
                    data1: 'bar',
                },
                colors: {
                    data1: cor_barra,
                },
            },
            transition: {
                duration: 0
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    tick: {
                        outer: false,
                        culling: {
                            max: 12 // the number of tick texts will be adjusted to less than this value
                        },
                        format: function (x) {

                            // split string and create array.
                            var arr = Dias[x].split(/-|\s|:/);

                            // copia
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // retorna mes
                            return Meses[data.getMonth()];
                        }
                    },
                },
                y:  {
                    min: minimoValor,
                    max: maximoValor,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoValor < 1)
                            {
                                return d.toFixed(3);
                            }

                            if( maximoValor < 10)
                            {
                                return d.toFixed(2);
                            }

                            if( maximoValor < 100)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.7 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;
                    for (i = 0; i < d.length; i++) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        if (! text) {

                            // split string and create array.
                            var arr = Dias[ d[i].x ].split(/-|\s|:/);

                            title = titleFormat ? titleFormat(d[i].x) : d[i].x;
                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "/" + arr[0] + "</th></tr>" : "");
                        }

                        // verifica se sem registro
                        if( Status[d[i].index] == 1 )
                        {
                            // verifica se barra
                            if( i == 0 )
                            {
                                name = 'Sem registro';
                                value = '---';

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                                text += "<td class='name'>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + UnidadeGrandeza + "</td>";
                                text += "</tr>";
                            }
                        }
                        else
                        {
                            name = NomeGrandeza;
                            value = valueFormat(d[i].value, d[i].ratio, d[i].id, d[i].index);
                            bgcolor = $$.levelColor ? $$.levelColor(d[i].value) : color(d[i].id);

                            var valor = d[i].value.toFixed(2);

                            if (maximoValor < 1)
                            {
                                valor = d[i].value.toFixed(3);
                            }

                            valor = valor.replace('.', ',');

                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                            text += "<td class='value'>" + valor + " " + UnidadeGrandeza + "</td>";
                            text += "</tr>";
                        }
                    }
                    return text + "</table>";
                }
            }
        });

    });

</script>

