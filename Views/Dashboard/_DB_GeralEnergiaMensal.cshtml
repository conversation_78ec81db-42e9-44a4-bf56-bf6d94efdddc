﻿@model SmartEnergyLib.SQL.DashboardDominio


@{
    <input type="hidden" id="@ViewBag.IDDashboardTxt" name="@ViewBag.IDDashboardTxt" value="@ViewBag.DataAtual">


    <div class="portlet-datahoraAtual portlet-energia">
        <h6>@ViewBag.Data</h6>
    </div>
    <div class="portlet-tabela portlet-energia">
        <table class="table no-margins">
            <thead>
                <tr>
                    <th style="width: 16%;"></th>
                    <th style="width: 28%;">@SmartEnergy.Resources.SupervisaoTexts.FPontaCap</th>
                    <th style="width: 28%;">@SmartEnergy.Resources.SupervisaoTexts.FPontaInd</th>
                    <th style="width: 28%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="text-align:left">
                        @SmartEnergy.Resources.SupervisaoTexts.Demanda<br />
                        @SmartEnergy.Resources.SupervisaoTexts.Consumo<br />
                        UFER<br />
                        Fator Pot.
                    </td>
                    <td>
                        @ViewBag.Dem_MaxFPC @ViewBag.UnidadeDemanda<br />
                        @ViewBag.ConsumoTotalFPC @ViewBag.UnidadeConsumo<br />
                        @ViewBag.UferFPC @ViewBag.UnidadeUFER<br />
                        @ViewBag.FatPotFPC
                    </td>
                    <td>
                        @ViewBag.Dem_MaxFPI @ViewBag.UnidadeDemanda<br />
                        @ViewBag.ConsumoTotalFPI @ViewBag.UnidadeConsumo<br />
                        @ViewBag.UferFPI @ViewBag.UnidadeUFER<br />
                        @ViewBag.FatPotFPI
                    </td>
                    <td>
                        @ViewBag.Dem_MaxP @ViewBag.UnidadeDemanda<br />
                        @ViewBag.ConsumoTotalP @ViewBag.UnidadeConsumo<br />
                        @ViewBag.UferP @ViewBag.UnidadeUFER<br />
                        @ViewBag.FatPotP
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="portlet-grafico portlet-energia">
        <div>
            <div id="<EMAIL>"></div>
        </div>
    </div>
}

<script type="text/javascript">

    $(document).ready(function () {

        // consumo
        var dataConsumo = [];
        var dataConsumoFPC = [];
        var dataConsumoFPI = [];
        var dataConsumoP = [];

        // copia valores
        var Consumo = @Html.Raw(Json.Encode(@ViewBag.Consumo));
        var ConsumoFPC = @Html.Raw(Json.Encode(@ViewBag.ConsumoFPC));
        var ConsumoFPI = @Html.Raw(Json.Encode(@ViewBag.ConsumoFPI));
        var ConsumoP = @Html.Raw(Json.Encode(@ViewBag.ConsumoP));
        var Status = @Html.Raw(Json.Encode(@ViewBag.Status));
        var Dias = @Html.Raw(Json.Encode(@ViewBag.Dias));
        var UnidadeConsumo = @Html.Raw(Json.Encode(@ViewBag.UnidadeConsumo));

        var maximoCons = Math.ceil(@ViewBag.Cons_max);
        var NumDias = Math.ceil(@ViewBag.NumDias);

        dataConsumoFPC.push(['data1']);
        dataConsumoFPI.push(['data2']);
        dataConsumoP.push(['data3']);

        for (i = 0; i < NumDias; i++)
        {
            dataConsumoFPC.push([ConsumoFPC[i]]);
            dataConsumoFPI.push([ConsumoFPI[i]]);
            dataConsumoP.push([ConsumoP[i]]);
        }

        var Data = [dataConsumoP,dataConsumoFPI,dataConsumoFPC];

        c3.generate({
            bindto: '#<EMAIL>',
            size: {
                width: 340,
                height: 192
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 35,
                left: 43
            },
            data: {
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'bar',
                    data3: 'bar',
                },
                groups: [
                            ['data1', 'data2', 'data3']
                ],
                order: null,
                colors: {
                    data1: '#1C84C6',
                    data2: '#007F00',
                    data3: '#FF0000'
                },
            },
            transition: {
                duration: 0
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    tick: {
                        outer: false,
                        culling: {
                            max: 5 // the number of tick texts will be adjusted to less than this value
                        },
                        format: function (x) {

                            // split string and create array.
                            var arr = Dias[x].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // retorna dia
                            return data.getDate();
                        }
                    }
                },
                y:  {
                    max: maximoCons,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoCons < 10 )
                            {
                                return(d.toFixed(1));
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.6 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < 4; i++) {

                        if (! text) {

                            // split string and create array.
                            var arr = Dias[d[0].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            var datestring = ("0" + data.getDate()).slice(-2) + "/" + ("0" + (data.getMonth()+1)).slice(-2);
                            title = DATA + " " + datestring;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");

                        }

                        // verifica se sem registro
                        if( Status[d[0].index] == 1 )
                        {
                            // verifica se barra
                            if( i == 0 )
                            {
                                name = SEM_REGISTRO;
                                value = '---';

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + UnidadeConsumo + "</td>";
                                text += "</tr>";
                            }
                        }
                        else
                        {
                            // verifica se barra
                            if( i == 0 )
                            {
                                name = FPONTA_CAP;
                                value = d[2].value;
                                bgcolor = '#1C84C6';
                            }

                            if( i == 1 )
                            {
                                name = FPONTA_IND;
                                value = d[1].value;
                                bgcolor = '#007F00';
                            }

                            if( i == 2 )
                            {
                                name = PONTA;
                                value = d[0].value;
                                bgcolor = '#FF0000';
                            }

                            if( i == 3 )
                            {
                                name = TOTAL;
                                value = d[0].value + d[1].value + d[2].value;
                                bgcolor = '#FFFFFF';
                            }

                            value = value.toFixed(1).replace('.', ',');

                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                            text += "<td class='value'>" + value + " " + UnidadeConsumo + "</td>";
                            text += "</tr>";
                        }
                    }
                    return text + "</table>";
                }
            }
        });
    });

</script>

    