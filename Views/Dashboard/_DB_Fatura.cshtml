﻿@model SmartEnergyLib.SQL.DashboardDominio

@{
    <input type="hidden" id="@ViewBag.IDDashboardTxt" name="@ViewBag.IDDashboardTxt" value="@ViewBag.DataAtual">

    <div class="portlet-valorAtual portlet-energia">
        <h1>@ViewBag.Fatura_Atual_ValorTotal</h1>
        <h5>@SmartEnergy.Resources.DashboardTexts.FaturaAtual</h5>
    </div>
    <div class="portlet-datahoraAtual  portlet-energia">
        <h6>@ViewBag.Fatura_Atual_Data</h6>
    </div>
    <div class="portlet-tabela portlet-energia">
        <table class="table no-margins">
            <thead>
                <tr>
                    <th></th>
                    <th></th>
                    <th>@SmartEnergy.Resources.SupervisaoTexts.Total</th>
                    <th></th>
                    <th>@SmartEnergy.Resources.SupervisaoTexts.CustoMedio</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><small>@ViewBag.Fatura_AtualProj_Data</small><br /><small>@SmartEnergy.Resources.SupervisaoTexts.Projetado</small></td>
                    <td><i class="fa fa-flag" style="color:@ViewBag.Fatura_AtualProj_Bandeira" data-toggle=" tooltip" data-placement="left" title="Bandeira @ViewBag.Fatura_AtualProj_BandeiraTexto"></i></td>
                    <td>@ViewBag.Fatura_AtualProj_ValorTotal<br /><small>&nbsp;</small></td>

                    @if (@ViewBag.Fatura_PorcAntProjN > 0.0)
                    {
                        <td style='color:#ff0000'><i class='fa fa-level-up'></i> @ViewBag.Fatura_PorcAntProj<br /><small>&nbsp;</small></td>
                    }
                    else
                    {
                        <td style='color:#007f00'><i class='fa fa-level-down'></i> @ViewBag.Fatura_PorcAntProj<br /><small>&nbsp;</small></td>
                    }

                    <td>@ViewBag.Fatura_AtualProj_CustoMedio<br /><small>&nbsp;</small></td>
                </tr>
                <tr>
                    <td><small>@ViewBag.Fatura_Anterior_Data</small></td>
                    <td><i class="fa fa-flag" style="color:@ViewBag.Fatura_Anterior_Bandeira" data-toggle=" tooltip" data-placement="left" title="Bandeira @ViewBag.Fatura_Anterior_BandeiraTexto"></i></td>
                    <td>@ViewBag.Fatura_Anterior_ValorTotal</td>
                    <td></td>
                    <td>@ViewBag.Fatura_Anterior_CustoMedio</td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="portlet-grafico portlet-energia">
        <div style="padding-left:10px;">
            <div id="<EMAIL>"></div>
        </div>
    </div>
}

<script type="text/javascript">

    $(document).ready(function () {

        // fatura
        var Porc_Demanda = @Html.Raw(Json.Encode(@ViewBag.Porc_Demanda));
        var Porc_Consumo = @Html.Raw(Json.Encode(@ViewBag.Porc_Consumo));
        var Porc_Imposto = @Html.Raw(Json.Encode(@ViewBag.Porc_Imposto));
        var Porc_Multa = @Html.Raw(Json.Encode(@ViewBag.Porc_Multa));
        var PlotarGrafico = @Html.Raw(Json.Encode(@ViewBag.PlotarGrafico));
        
        if( PlotarGrafico )
        {
            var chart = c3.generate({
                bindto: '#<EMAIL>',
                size: {
                    width: 340,
                    height: 120
                },
                padding: {
                    top: 0,
                    right: 10,
                    bottom: 0,
                    left: 10
                },
                legend: {
                    position: 'right'
                },
                data: {
                    columns: [
                        [DEMANDA, Porc_Demanda],
                        [CONSUMO, Porc_Consumo],
                        [IMPOSTO, Porc_Imposto],
                        [MULTA, Porc_Multa],
                    ],
                    type: 'pie',
                    colors: {
                        Demanda: '#007F00',
                        Consumo: '#00007F',
                        Imposto: '#FF7F0E',
                        Multa: '#FF0000',
                    },
                    onclick: function (d, i) { console.log("onclick", d, i); },
                    onmouseover: function (d, i) { console.log("onmouseover", d, i); },
                    onmouseout: function (d, i) { console.log("onmouseout", d, i); }
                },
                transition: {
                    duration: 0
                },
                pie: {
                    label: {
                        format: function (value, ratio, id) {
                            if(id==DEMANDA) return DEMANDA_ABV;
                            if(id==CONSUMO) return CONSUMO_ABV;
                            if(id==IMPOSTO) return IMPOSTO_ABV;
                            if(id==MULTA) return MULTA_ABV;
                            return d3.format('$')(value);
                        }
                    }
                }
            });
        }
    });

</script>

    