﻿@model SmartEnergyLib.SQL.DashboardDominio

@{
    <input type="hidden" id="@ViewBag.IDDashboardTxt" name="@ViewBag.IDDashboardTxt" value="@ViewBag.DataAtual">


    <div class="portlet-datahoraAtual portlet-energia">
        <h6>@ViewBag.Data</h6>
    </div>
    
    <div class="portlet-valorAtualLinha">

        @{
            if (ViewBag.ProjetadoMesN < ViewBag.MetaMesN)
            {
                <div class="portlet-valorAtual portlet-energia">
                    <h1>@ViewBag.ProjetadoMes <span>@ViewBag.UnidadeConsumo</span></h1>
                    <h5>@SmartEnergy.Resources.DashboardTexts.ConsumoProjetadoMes</h5>
                </div>
            }
            else
            {
                <div class="portlet-valorAtual portlet-blink">
                    <blink>
                        <h1>@ViewBag.ProjetadoMes <span>@ViewBag.UnidadeConsumo</span></h1>
                    </blink>
                    <h5>@SmartEnergy.Resources.DashboardTexts.ConsumoProjetadoMes</h5>
                </div>
            }
        }

        <div class="portlet-valorAtualDireita portlet-util" style="margin-top:0px;">
            <h1>@ViewBag.MetaMes <span>@ViewBag.UnidadeConsumo</span></h1>
            <div class="portlet-valorAtualLinha">
                <h5>@SmartEnergy.Resources.DashboardTexts.MetaMensal</h5>
            </div>
        </div>
    </div>
    
    <div class="portlet-valorAtualLinha">

        @{
            if (ViewBag.ConsumoMedioN < ViewBag.MetaDiariaN)
            {
                <div class="portlet-valorAtual portlet-energia">
                    <h1>@ViewBag.ConsumoMedio <span>@ViewBag.UnidadeConsumo</span></h1>
                    <h5>@SmartEnergy.Resources.DashboardTexts.ConsumoMedioDia</h5>
                </div>
            }
            else
            {
                <div class="portlet-valorAtual portlet-blink">
                    <blink>
                        <h1>@ViewBag.ConsumoMedio <span>@ViewBag.UnidadeConsumo</span></h1>
                    </blink>
                    <h5>@SmartEnergy.Resources.DashboardTexts.ConsumoTotalDia</h5>
                </div>
            }
        }

        <div class="portlet-valorAtualDireita portlet-util">
            <h1>@ViewBag.MetaDiaria <span>@ViewBag.UnidadeConsumo</span></h1>
            <h5>@SmartEnergy.Resources.DashboardTexts.MetaDiaria</h5>
        </div>
    </div>
    
    <div class="portlet-grafico portlet-energia" style="margin-top: 10px;">
        <div>
            <div id="<EMAIL>"></div>
        </div>
    </div>
}

<script type="text/javascript">

    $(document).ready(function () {

        // consumo e meta
        var dataConsumo = [];
        var dataMeta = [];

        // copia valores
        var Consumo = @Html.Raw(Json.Encode(@ViewBag.Consumo));
        var Meta = @Html.Raw(Json.Encode(@ViewBag.Meta));
        var Status = @Html.Raw(Json.Encode(@ViewBag.Status));
        var Dias = @Html.Raw(Json.Encode(@ViewBag.Dias));
        var UnidadeConsumo = @Html.Raw(Json.Encode(@ViewBag.UnidadeConsumo));

        var maximoCons = Math.ceil(@ViewBag.Cons_max);
        var NumDias = Math.ceil(@ViewBag.NumDias);

        dataConsumo.push(['data1']);
        dataMeta.push(['data2']);

        for (i = 0; i < NumDias; i++)
        {
            dataConsumo.push([Consumo[i]]);
            dataMeta.push([Meta[i]]);
        }

        var Data = [dataConsumo,dataMeta];

        c3.generate({
            bindto: '#<EMAIL>',
            size: {
                width: 340,
                height: 192
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 35,
                left: 43
            },
            data: {
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'step'
                },
                colors: {
                    data1: '#00007F',
                    data2: '#00007F',
                }
            },
            transition: {
                duration: 0
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    tick: {
                        outer: false,
                        culling: {
                            max: 5 // the number of tick texts will be adjusted to less than this value
                        },
                        format: function (x) {

                            // split string and create array.
                            var arr = Dias[x].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // retorna dia
                            return data.getDate();
                        }
                    },
                },
                y:  {
                    max: maximoCons,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoCons < 10 )
                            {
                                return(d.toFixed(1));
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.6 },
                //                width: 6,
                //              ratio: 1.0
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;
                    for (i = 0; i < d.length; i++) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        if (! text) {
                            title = titleFormat ? titleFormat(d[i].x) : d[i].x;
                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + DIA + " " + title + "</th></tr>" : "");
                        }

                        // verifica se sem registro
                        if( Status[d[i].index] == 1 )
                        {
                            // verifica se barra
                            if( i == 0 )
                            {
                                name = SEM_REGISTRO;
                                value = '---';

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                                text += "<td class='name'>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + UnidadeConsumo + "</td>";
                                text += "</tr>";
                            }
                        }
                        else
                        {
                            // verifica se barra
                            if( i == 0 )
                            {
                                name = CONSUMO;
                            }

                            // verifica se linha
                            if( i == 1 )
                            {
                                name = META;
                            }

                            if(maximoCons >= 1000.0)
                            {
                                value = valueFormat(d[i].value, d[i].ratio, d[i].id, d[i].index);
                            }
                            else
                            {
                                value = d[i].value.toFixed(1);
                            }

                            bgcolor = $$.levelColor ? $$.levelColor(d[i].value) : color(d[i].id);

                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                            text += "<td class='value'>" + value + " " + UnidadeConsumo + "</td>";
                            text += "</tr>";
                        }
                    }
                    return text + "</table>";
                }
            }
        });

    });


</script>

    