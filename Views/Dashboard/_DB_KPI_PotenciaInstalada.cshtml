﻿@model SmartEnergyLib.SQL.DashboardDominio

@{
    <input type="hidden" id="@ViewBag.IDDashboardTxt" name="@ViewBag.IDDashboardTxt" value="@ViewBag.DataAtual">


    <div class="portlet-valorAtual portlet-energia">
        <h1>@ViewBag.Dem_Ult <span>@ViewBag.UnidadeDemanda</span></h1>
        <h5>@SmartEnergy.Resources.DashboardTexts.DemandaAtual</h5>
    </div>

    <div class="portlet-datahoraAtual portlet-energia">
        <h6>@ViewBag.DataHoraAtual</h6>

        @{
            int periodo = 3;

            if (ViewBag.Dem_UltPeriodo != null)
            {
                periodo = (int)ViewBag.Dem_UltPeriodo;
            }

            switch (periodo)
            {
                case 0:
                    <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                    break;
                case 1:
                case 2:
                    <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta</h4>
                    break;
            }
        }

    </div>
    
    <div class="portlet-valorAtualLinha" style="padding-top: 10px;">
        <div class="portlet-valorAtual portlet-energia">
            <h1>@ViewBag.Reducao_Ult <span>%</span></h1>
            <h5>@SmartEnergy.Resources.DashboardTexts.ReducaoAtual</h5>
        </div>
    </div>

    <div class="portlet-grafico portlet-energia" style="margin-top: 10px;">
        <div>
            <div id="<EMAIL>"></div>
        </div>
    </div>
}
    
<script type="text/javascript">

    $(document).ready(function () {

        // demanda
        var dataX = [];
        var labelX = [];
        var dataPotenciaInstalada = [];
        var dataDemanda = [];

        // copia valores
        var Demanda = @Html.Raw(Json.Encode(@ViewBag.Demanda));
        var PotenciaInstalada = @Html.Raw(Json.Encode(@ViewBag.PotenciaInstalada));
        var Reducao = @Html.Raw(Json.Encode(@ViewBag.Reducao));
        var Periodo = @Html.Raw(Json.Encode(@ViewBag.Periodo));
        var Dias = @Html.Raw(Json.Encode(@ViewBag.Dias));

        var minimoDem = @Html.Raw(Json.Encode(@ViewBag.DemMinGrafico));
        var maximoDem = @Html.Raw(Json.Encode(@ViewBag.DemMaxGrafico));

        var UnidadeDemanda = @Html.Raw(Json.Encode(@ViewBag.UnidadeDemanda));

        dataX.push('x');
        dataDemanda.push(['data1']);
        dataPotenciaInstalada.push(['data2']);

        for (i = 0; i < 98; i++)
        {
            // caso nao tenha PotenciaInstalada, maximo vira PotenciaInstalada para termos indicacao de periodo
            if( PotenciaInstalada[i] == 0 && Periodo[i] != 3 )
            {
                PotenciaInstalada[i] = maximoDem;
            }

            // X
            dataX.push(Dias[i]);

            // demanda
            dataDemanda.push([Demanda[i]]);
            dataPotenciaInstalada.push([PotenciaInstalada[i]]);
        }

        // valores label X
        labelX.push(Dias[1]);
        for (i = 12; i <= 96; i+=12)
        {
            labelX.push(Dias[i]);
        }

        var Data = [dataX,dataDemanda,dataPotenciaInstalada];

        c3.generate({

            bindto: '#<EMAIL>',
            size: {
                width: 342,
                height: 192
            },
            padding: {
                top: 8,
                right: 18,
                bottom: 35,
                left: 35
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'step',
                },
                color: function (color, d) {
                    if (typeof d.index === 'undefined') { return color; }

                    if( Periodo[d.index] == 0 )
                        return '#FF0000';

                    if( Periodo[d.index] == 1 )
                        return '#007F00';

                    if( Periodo[d.index] == 2 )
                        return '#1C84C6';

                    return '#007F00';
                },
                colors: {
                    data2: '#00007F',
                },
            },
            transition: {
                duration: 0
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*6
                },
                y:  {
                    min: minimoDem,
                    max: maximoDem,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoDem < 100)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: 2,
                ratio: 1.0
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < 4; i++) {

                        if (! text) {

                            // split string and create array.
                            var arr = Dias[d[0].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // horas e minutos
                            var hours = data.getHours();
                            var minutes = data.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            title = hours + ':' + minutes;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // periodo
                        switch(Periodo[d[0].index])
                        {
                            case 0:
                                periodo = PONTA;
                                bgcolor = '#FF0000'
                                break;

                            case 1:
                                periodo = FPONTA_IND;
                                bgcolor = '#007F00';
                                break;

                            case 2:
                                periodo = FPONTA_CAP;
                                bgcolor = '#1C84C6';
                                break;

                            default:
                                periodo = "---";
                                bgcolor = '#000000';
                                break;
                        }

                        // nome
                        switch(i)
                        {
                            case 0:
                                name = DEMANDA_ATIVA;
                                unit = UnidadeDemanda;
                                value = Demanda[d[0].index].toFixed(1);
                                break;

                            case 1:
                                name = POTENCIA_INSTALADA;
                                unit = UnidadeDemanda;
                                value = PotenciaInstalada[d[0].index].toFixed(1);
                                break;

                            case 2:
                                name = REDUCAO;
                                unit = '%';
                                value = Reducao[d[0].index].toFixed(1);
                                break;

                            case 3:
                                name = PERIODO;
                                unit = '';
                                value = periodo;
                                break;
                        }

                        value = value.replace('.', ',');

                        // verifica se sem registro
                        if( Periodo[d[0].index] == 3 )
                        {
                            value = '---';
                        }

                        if(i>0)
                        {
                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                            text += "<td class='name'>" + name + "</td>";
                            text += "<td class='value'>" + value + "</td>";
                            text += "</tr>";
                        }
                        else
                        {
                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                            text += "<td class='value'>" + value + " " + unit + "</td>";
                            text += "</tr>";
                        }
                    }
                    return text + "</table>";
                }
            }
        });

    });

</script>

