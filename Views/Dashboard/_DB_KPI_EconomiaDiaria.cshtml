﻿@model SmartEnergyLib.SQL.DashboardDominio

@{
    <input type="hidden" id="@ViewBag.IDDashboardTxt" name="@ViewBag.IDDashboardTxt" value="@ViewBag.DataAtual">


    <div class="portlet-datahoraAtual portlet-energia">
        <h6>@ViewBag.Data</h6>
    </div>
    
    <div class="portlet-valorAtualLinha">
        <div class="portlet-valorAtual portlet-energia">
            <h1>@ViewBag.ConsTotal <span>@ViewBag.UnidadeConsumo</span></h1>
            <h5>@SmartEnergy.Resources.DashboardTexts.ConsumoTotalDia</h5>
        </div>
        <div class="portlet-valorAtualDireita portlet-util" style="margin-top:0px;">
            <h1>@ViewBag.EconomiaPorcTotal <span>%</span></h1>
            <div class="portlet-valorAtualLinha">
                <h5>@SmartEnergy.Resources.DashboardTexts.Economia</h5>
            </div>
        </div>
    </div>
    
    <div class="portlet-valorAtualLinha">
        <div class="portlet-valorAtual portlet-energia">
            <h1>@ViewBag.EconomiaConsTotal <span>@ViewBag.UnidadeConsumo</span></h1>
            <h5>@SmartEnergy.Resources.DashboardTexts.EconomiaTotalDia</h5>
        </div>
        <div class="portlet-valorAtualDireita portlet-util">
            <h1>@ViewBag.EconomiaTotal</h1>
            <h5>@SmartEnergy.Resources.DashboardTexts.Economia</h5>
        </div>
    </div>
    
    <div class="portlet-grafico portlet-energia" style="margin-top: 10px;">
        <div>
            <div id="<EMAIL>"></div>
        </div>
    </div>
}

<script type="text/javascript">

    $(document).ready(function () {

        // consumo e referencia
        var dataX = [];
        var labelX = [];
        var dataConsumo = [];
        var dataReferencia = [];

        // copia valores
        var Consumo = @Html.Raw(Json.Encode(@ViewBag.Consumo));
        var Periodo = @Html.Raw(Json.Encode(@ViewBag.Periodo));
        var Referencia = @Html.Raw(Json.Encode(@ViewBag.Referencia));
        var EconomiaConsumo = @Html.Raw(Json.Encode(@ViewBag.EconomiaConsumo));
        var EconomiaValor = @Html.Raw(Json.Encode(@ViewBag.EconomiaValor));
        var EconomiaPercentual = @Html.Raw(Json.Encode(@ViewBag.EconomiaPercentual));
        var Status = @Html.Raw(Json.Encode(@ViewBag.Status));
        var Dias = @Html.Raw(Json.Encode(@ViewBag.Dias));
        var UnidadeConsumo = @Html.Raw(Json.Encode(@ViewBag.UnidadeConsumo));

        var minimoCons = Math.ceil(@ViewBag.Cons_min);
        var maximoCons = Math.ceil(@ViewBag.Cons_max);

        dataX.push('x');
        dataConsumo.push(['data1']);
        dataReferencia.push(['data2']);

        for (i = 0; i < 24; i++)
        {
            // X
            dataX.push(Dias[i]);

            dataConsumo.push([Consumo[i]]);
            dataReferencia.push([Referencia[i]]);
        }

        // valores label X
        labelX.push(Dias[0]);
        for (i = 2; i <= 24; i+=3)
        {
            labelX.push(Dias[i]);
        }

        var Data = [dataX,dataConsumo,dataReferencia];

        c3.generate({
            bindto: '#<EMAIL>',
            size: {
                width: 340,
                height: 192
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 35,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                type:'bar',
                types: {
                    data2: 'step'
                },
                color: function (color, d) {
                    if (typeof d.index === 'undefined') { return color; }

                    if( Periodo[d.index] == 0 )
                        return '#FF0000';

                    if( Periodo[d.index] == 1 )
                        return '#007F00';

                    if( Periodo[d.index] == 2 )
                        return '#1C84C6';

                    return '#007F00';
                },
                colors: {
                    data2: '#00007F',
                }
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                },
                y:  {
                    min: minimoCons,
                    max: maximoCons,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoCons < 100 )
                            {
                                return(d.toFixed(1));
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.2 }
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < 5; i++) {

                        if (! text) {

                            // split string and create array.
                            var arr = Dias[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // horas e minutos
                            var hours = data.getHours();
                            var minutes = data.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            title = hours + ':' + minutes;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // verifica se sem registro
                        if( Periodo[d[0].index] == 3 )
                        {
                            // verifica se barra
                            if( i == 0 )
                            {
                                name = SEM_REGISTRO;
                                value = '---';

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                                text += "<td class='name'>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + UnidadeConsumo + "</td>";
                                text += "</tr>";
                            }
                        }
                        else
                        {
                            // verifica se consumo
                            if( i == 0 )
                            {
                                switch( Periodo[d[0].index] )
                                {
                                    case 0:
                                        name = PONTA;
                                        bgcolor = '#FF0000';
                                        break;

                                    case 1:
                                        name = FPONTA_IND;
                                        bgcolor = '#007F00';
                                        break;

                                    case 2:
                                        name = FPONTA_CAP;
                                        bgcolor = '#1C84C6';
                                        break;
                                }

                                if( d[0].value < 100.0 )
                                {
                                    value = d[0].value.toFixed(1);
                                }
                                else
                                {
                                    value = d[0].value.toFixed(0);
                                }

                                value = value.replace('.', ',');

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                                text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + UnidadeConsumo + "</td>";
                                text += "</tr>";
                            }

                            // verifica se referencia
                            if( i == 1 )
                            {
                                name = REFERENCIA;

                                value = Referencia[d[0].index].toFixed(0);
                                value = value.replace('.', ',');

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + UnidadeConsumo + "</td>";
                                text += "</tr>";
                            }

                            if( i == 2 )
                            {
                                name = ECONOMIA + " (" + UnidadeConsumo + ")";

                                if( EconomiaConsumo[d[0].index] < 100.0 )
                                {
                                    value = EconomiaConsumo[d[0].index].toFixed(1);
                                }
                                else
                                {
                                    value = EconomiaConsumo[d[0].index].toFixed(0);
                                }

                                value = value.replace('.', ',');

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + UnidadeConsumo + "</td>";
                                text += "</tr>";
                            }

                            if( i == 3 )
                            {
                                name = ECONOMIA + ' (%)';

                                value = EconomiaPercentual[d[0].index].toFixed(1);

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'>" + name + "</td>";
                                text += "<td class='value'>" + value + " %</td>";
                                text += "</tr>";
                            }

                            if( i == 4 )
                            {
                                name = ECONOMIA + ' ($)';

                                value = EconomiaValor[d[0].index].toFixed(2);

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'>" + name + "</td>";
                                text += "<td class='value'> $ " + value + "</td>";
                                text += "</tr>";
                            }
                        }
                    }
                    return text + "</table>";
                }
            }
        });
    });

</script>

    