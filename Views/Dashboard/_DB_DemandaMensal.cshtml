﻿@model SmartEnergyLib.SQL.DashboardDominio

@{
    <input type="hidden" id="@ViewBag.IDDashboardTxt" name="@ViewBag.IDDashboardTxt" value="@ViewBag.DataAtual">

    
    <div class="portlet-datahoraAtual portlet-energia">
        <h6>@ViewBag.DataHoraAtual</h6>
    </div>
    
    <div class="portlet-valorAtualLinha">
        <div class="portlet-valorAtual portlet-fponta">
            <h1>@ViewBag.Dem_MaxFP <span>@ViewBag.UnidadeDemanda</span></h1>
            <h5>@SmartEnergy.Resources.DashboardTexts.DemandaMaximaMes</h5>
        </div>

        <div class="portlet-valorAtualDireita portlet-fponta" style="margin-top:0px;">
            <h1>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta</h1>
            <div class="portlet-valorAtualLinha">
                <h5>@ViewBag.Dem_MaxFP_DataHora</h5>
            </div>
        </div>
    </div>

    <div class="portlet-valorAtualLinha">
        <div class="portlet-valorAtual portlet-ponta">
            <h1>@ViewBag.Dem_MaxP <span>@ViewBag.UnidadeDemanda</span></h1>
            <h5>@SmartEnergy.Resources.DashboardTexts.DemandaMaximaMes</h5>
        </div>

        <div class="portlet-valorAtualDireita portlet-ponta" style="margin-top:0px;">
            <h1>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@SmartEnergy.Resources.SupervisaoTexts.Ponta</h1>
            <div class="portlet-valorAtualLinha">
                <h5>@ViewBag.Dem_MaxP_DataHora</h5>
            </div>
        </div>
    </div>

    <div class="portlet-grafico portlet-energia" style="margin-top: 10px;">
        <div>
            <div id="<EMAIL>"></div>
        </div>
    </div>
}
    
<script type="text/javascript">

    $(document).ready(function () {

        // demanda
        var dataX = [];
        var labelX = [];
        var dataContratoP = [];
        var dataContratoFP = [];
        var dataToleranciaP = [];
        var dataToleranciaFP = [];
        var dataDemandaP = [];
        var dataDemandaFP = [];

        // copia valores
        var DemandaP = @Html.Raw(Json.Encode(@ViewBag.DemandaP));
        var DemandaFP = @Html.Raw(Json.Encode(@ViewBag.DemandaFP));

        var ContratoP = @Html.Raw(Json.Encode(@ViewBag.ContratoP));
        var ContratoFP = @Html.Raw(Json.Encode(@ViewBag.ContratoFP));

        var ToleranciaP = @Html.Raw(Json.Encode(@ViewBag.ToleranciaP));
        var ToleranciaFP = @Html.Raw(Json.Encode(@ViewBag.ToleranciaFP));

        var Status = @Html.Raw(Json.Encode(@ViewBag.Status));
        var Dias = @Html.Raw(Json.Encode(@ViewBag.Dias));

        var maximoDem = @Html.Raw(Json.Encode(@ViewBag.DemMaxGrafico));
        var NumDias = Math.ceil(@ViewBag.NumDias);

        var UnidadeDemanda = @Html.Raw(Json.Encode(@ViewBag.UnidadeDemanda));

        dataDemandaFP.push(['data1']);
        dataDemandaP.push(['data2']);
        dataContratoFP.push(['data3']);
        dataContratoP.push(['data4']);
        dataToleranciaFP.push(['data5']);
        dataToleranciaP.push(['data6']);

        for (i = 0; i < NumDias; i++)
        {
            dataDemandaFP.push([DemandaFP[i]]);
            dataDemandaP.push([DemandaP[i]]);

            dataContratoFP.push([ContratoFP[i]]);
            dataContratoP.push([ContratoP[i]]);

            dataToleranciaFP.push([ToleranciaFP[i]]);
            dataToleranciaP.push([ToleranciaP[i]]);
        }

        var Data = [dataDemandaFP,dataDemandaP,dataContratoFP,dataContratoP,dataToleranciaFP,dataToleranciaP];

        c3.generate({
            bindto: '#<EMAIL>',
            size: {
                width: 342,
                height: 192
            },
            padding: {
                top: 8,
                right: 18,
                bottom: 35,
                left: 43
            },
            data: {
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'bar',
                    data3: 'step',
                    data4: 'step',
                    data5: 'step',
                    data6: 'step',
                },
                colors: {
                    data1: '#007F00',
                    data2: '#FF0000',
                    data3: '#007F00',
                    data4: '#FF0000',
                    data5: '#007F00',
                    data6: '#FF0000',
                },
            },
            transition: {
                duration: 0
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    tick: {
                        outer: false,
                        culling: {
                            max: 5 // the number of tick texts will be adjusted to less than this value
                        },
                        format: function (x) {

                            // split string and create array.
                            var arr = Dias[x].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // retorna dia
                            return data.getDate();
                        }
                    },
                },
                y:  {
                    max: maximoDem,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoDem < 100)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.7 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;
                    for (i = 0; i < d.length; i++) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        if (! text) {
                            title = titleFormat ? titleFormat(d[i].x) : d[i].x;
                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + DIA + " " + title + "</th></tr>" : "");
                        }

                        // verifica se sem registro
                        if( Status[d[i].index] == 1 )
                        {
                            // verifica se barra
                            if( i == 0 )
                            {
                                name = SEM_REGISTRO;
                                value = '---';

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                                text += "<td class='name'>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + UnidadeDemanda + "</td>";
                                text += "</tr>";
                            }
                        }
                        else
                        {
                            // verifica se barra
                            if( i == 0 )
                            {
                                name = FORAPONTA;
                                bgcolor = $$.levelColor ? $$.levelColor(d[0].value) : color(d[0].id);

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                                text += "<td class='value'></td>";
                                text += "</tr>";

                                // demanda
                                name = DEMANDA;
                                value = d[0].value.toFixed(1);

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + UnidadeDemanda + "</td>";
                                text += "</tr>";

                                // contrato
                                name = CONTRATO;
                                value = d[2].value.toFixed(1);

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[2].id + "'>";
                                text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + UnidadeDemanda + "</td>";
                                text += "</tr>";

                                // tolerancia
                                name = TOLERANCIA;
                                value = d[4].value.toFixed(1);

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[4].id + "'>";
                                text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + UnidadeDemanda + "</td>";
                                text += "</tr>";

                                // pula linha
                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'></td>";
                                text += "<td class='value'></td>";
                                text += "</tr>";
                            }

                            if( i == 1 )
                            {
                                name = PONTA;
                                bgcolor = $$.levelColor ? $$.levelColor(d[1].value) : color(d[1].id);

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[1].id + "'>";
                                text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                                text += "<td class='value'></td>";
                                text += "</tr>";

                                // demanda
                                name = DEMANDA;
                                value = d[1].value.toFixed(1);

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[1].id + "'>";
                                text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + UnidadeDemanda + "</td>";
                                text += "</tr>";

                                // contrato
                                name = CONTRATO;
                                value = d[3].value.toFixed(1);

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[3].id + "'>";
                                text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + UnidadeDemanda + "</td>";
                                text += "</tr>";

                                // tolerancia
                                name = TOLERANCIA;
                                value = d[5].value.toFixed(1);

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[5].id + "'>";
                                text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + UnidadeDemanda + "</td>";
                                text += "</tr>";
                            }
                        }
                    }
                    return text + "</table>";
                }
            }
        });

    });

</script>

