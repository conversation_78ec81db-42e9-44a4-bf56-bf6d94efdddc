﻿@model SmartEnergyLib.SQL.DashboardPageDominio

@using SmartEnergyLib.SQL
@using System.Globalization

@{
    <div class="modal inmodal" id="Painel_Editar_Modal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg" style="height:400px;">
            <div class="modal-content animated fadeIn">
                <div class="modal-editar-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only modal-tools-header">Close</span></button>
                    <i class="fa fa-edit modal-editar-title"></i><span class="modal-editar-title">&nbsp;&nbsp;@SmartEnergy.Resources.DashboardTexts.EdicaoPainel</span>
                    <input type="hidden" id="IDDashboardPage" value=@Model.IDDashboardPage>
                </div>

                @using (Html.BeginForm("_Painel_Editar", "Dashboard", new { ReturnUrl = ViewBag.ReturnUrl }, FormMethod.Post, new { id = "formPainel", role = "form" }))
                {
                    <div class="modal-body">
                        @Html.HiddenFor(model => model.IDDashboardPage)
                        @Html.HiddenFor(model => model.IDCliente)
                        @Html.HiddenFor(model => model.IDReferencia)
                        @Html.HiddenFor(model => model.TipoReferencia)
                        @Html.HiddenFor(model => model.PageNumber)

                        <br />
                        <div class="row">
                            <div class="form-group col-xs-6">
                                <label class="control-label">&nbsp;@SmartEnergy.Resources.DashboardTexts.Painel</label>
                                @Html.TextBoxFor(model => model.Title, new { @class = "form-control", @maxlength = "50" })
                            </div>
                        </div>

                        <br />
                        <div class="row">
                            <div class="form-group col-xs-6">
                                <label class="control-label">&nbsp;Ciclar Medição</label>
                                @Html.DropDownListFor(model => model.CiclarMedicao,
                                    new List<SelectListItem>
                                    {
                                        new SelectListItem { Text = "Não ciclar", Value = "0" },
                                        new SelectListItem { Text = "Ciclar Medição", Value = "1" }
                                    },
                                    new { @class = "form-control" })
                            </div>
                        </div>

                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                        <button type="submit" class="btn btn-primary">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                    </div>
                }

            </div>
        </div>
    </div>

}

<script>
    $(document).ready(function () {

        //
        // VALIDACAO DOS CAMPOS
        //

        jQuery.validator.addMethod("alphanumeric", function (value, element) {
            return this.optional(element) || /^[\'\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\+\-\_\(\)," "]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");


        $("#formPainel").validate({
            rules: {
                Title: {
                    required: true
                }
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            }
        });

        $("#formPainel").submit(function(e) {

            e.preventDefault();
            var $form = $(this);

            // verifica se entradas sao validas
            if (!$form.valid()) return false;

            swal({
                title: DESEJA_SALVAR_PAINEL,
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#1ab394",
                confirmButtonText: SALVAR,
                cancelButtonText: CANCELAR,
                closeOnConfirm: true
            }, function () {

                var form = $("#formPainel");
                $.ajax({
                    url: '/Dashboard/_Painel_Editar',
                    data: $form.serialize(),
                    type: 'POST',
                    success: function (data) {

                        // apaga janela
                        $('#Painel_Editar_Modal').modal('hide');

                        // atualiza pagina
                        location.reload();
                    }
                });

            });

        });

        $('#Painel_Editar_Modal').modal('show');
    });

</script>
