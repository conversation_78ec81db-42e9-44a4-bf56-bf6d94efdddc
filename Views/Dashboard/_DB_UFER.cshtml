﻿@model SmartEnergyLib.SQL.DashboardDominio

@{
    <input type="hidden" id="@ViewBag.IDDashboardTxt" name="@ViewBag.IDDashboardTxt" value="@ViewBag.DataAtual">


    <div class="portlet-valorAtual portlet-energia">
        <h1>@ViewBag.UFER_Total <span>@ViewBag.UnidadeUFER</span></h1>
        <h5>@SmartEnergy.Resources.DashboardTexts.UFERTotalMes</h5>
    </div>
    <div class="portlet-datahoraAtual portlet-energia">
        <h6>@ViewBag.Data</h6>
    </div>
    <div class="portlet-tabela portlet-energia">
        <table class="table no-margins">
            <thead>
                <tr>
                    <th style="width: 16%;"></th>
                    <th style="width: 28%;">@SmartEnergy.Resources.SupervisaoTexts.FPontaCap</th>
                    <th style="width: 28%;">@SmartEnergy.Resources.SupervisaoTexts.FPontaInd</th>
                    <th style="width: 28%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="text-align:center">UFER</td>
                    <td>@ViewBag.UFER_TotalFPC @ViewBag.UnidadeUFER<br /><small>&nbsp;</small></td>
                    <td>@ViewBag.UFER_TotalFPI @ViewBag.UnidadeUFER<br /><small>&nbsp;</small></td>
                    <td>@ViewBag.UFER_TotalP @ViewBag.UnidadeUFER<br /><small>&nbsp;</small></td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="portlet-grafico portlet-energia">
        <div>
            <div id="<EMAIL>"></div>
        </div>
    </div>
}

<script type="text/javascript">

    $(document).ready(function () {

        // UFER
        var dataUFER = [];
        var dataUFER_FPC = [];
        var dataUFER_FPI = [];
        var dataUFER_P = [];

        // copia valores
        var UFER = @Html.Raw(Json.Encode(@ViewBag.UFER));
        var UFER_FPC = @Html.Raw(Json.Encode(@ViewBag.UFER_FPC));
        var UFER_FPI = @Html.Raw(Json.Encode(@ViewBag.UFER_FPI));
        var UFER_P = @Html.Raw(Json.Encode(@ViewBag.UFER_P));
        var Status = @Html.Raw(Json.Encode(@ViewBag.Status));
        var Dias = @Html.Raw(Json.Encode(@ViewBag.Dias));
        var UnidadeUFER = @Html.Raw(Json.Encode(@ViewBag.UnidadeUFER));

        var maximoUFER = Math.ceil(@ViewBag.UFER_max);
        var NumDias = Math.ceil(@ViewBag.NumDias);

        dataUFER_FPC.push(['data1']);
        dataUFER_FPI.push(['data2']);
        dataUFER_P.push(['data3']);

        for (i = 0; i < NumDias; i++)
        {
            dataUFER_FPC.push([UFER_FPC[i]]);
            dataUFER_FPI.push([UFER_FPI[i]]);
            dataUFER_P.push([UFER_P[i]]);
        }

        var Data = [dataUFER_P,dataUFER_FPI,dataUFER_FPC];

        c3.generate({
            bindto: '#<EMAIL>',
            size: {
                width: 340,
                height: 192
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 35,
                left: 43
            },
            data: {
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'bar',
                    data3: 'bar',
                },
                groups: [
                            ['data1', 'data2', 'data3']
                ],
                order: null,
                colors: {
                    data1: '#1C84C6',
                    data2: '#007F00',
                    data3: '#FF0000'
                },
            },
            transition: {
                duration: 0
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    tick: {
                        outer: false,
                        culling: {
                            max: 5 // the number of tick texts will be adjusted to less than this value
                        },
                        format: function (x) {

                            // split string and create array.
                            var arr = Dias[x].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // retorna dia
                            return data.getDate();
                        }
                    }
                },
                y:  {
                    max: maximoUFER,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoUFER < 10 )
                            {
                                return(d.toFixed(1));
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.6 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < 4; i++) {

                        if (! text) {

                            // split string and create array.
                            var arr = Dias[d[0].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            var datestring = ("0" + data.getDate()).slice(-2) + "/" + ("0" + (data.getMonth()+1)).slice(-2);
                            title = DATA + " " + datestring;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");

                        }

                        // verifica se sem registro
                        if( Status[d[0].index] == 1 )
                        {
                            // verifica se barra
                            if( i == 0 )
                            {
                                name = SEM_REGISTRO;
                                value = '---';

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + UnidadeUFER + "</td>";
                                text += "</tr>";
                            }
                        }
                        else
                        {
                            // verifica se barra
                            if( i == 0 )
                            {
                                name = FPONTA_CAP;
                                value = d[2].value;
                                bgcolor = '#1C84C6';
                            }

                            if( i == 1 )
                            {
                                name = FPONTA_IND;
                                value = d[1].value;
                                bgcolor = '#007F00';
                            }

                            if( i == 2 )
                            {
                                name = PONTA;
                                value = d[0].value;
                                bgcolor = '#FF0000';
                            }

                            if( i == 3 )
                            {
                                name = TOTAL;
                                value = d[0].value + d[1].value + d[2].value;
                                bgcolor = '#FFFFFF';
                            }

                            value = value.toFixed(1).replace('.', ',');

                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                            text += "<td class='value'>" + value + " " + UnidadeUFER + "</td>";
                            text += "</tr>";
                        }
                    }
                    return text + "</table>";
                }
            }
        });
    });

</script>

    