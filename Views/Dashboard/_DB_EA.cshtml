﻿@model SmartEnergyLib.SQL.DashboardDominio

@{
    <input type="hidden" id="@ViewBag.IDDashboardTxt" name="@ViewBag.IDDashboardTxt" value="@ViewBag.DataAtual">


    <div class="portlet-valorAtual portlet-ea">
        <h1>@ViewBag.Media_Dia <span>@ViewBag.UnidadeGrandeza</span></h1>
        <h5>@SmartEnergy.Resources.DashboardTexts.MedioDia</h5>
    </div>
    <div class="portlet-datahoraAtual portlet-ea">
        <h6>@ViewBag.Data_Dia</h6>
    </div>
    <div class="portlet-tabela portlet-ea">
        <table class="table no-margins">
            <thead>
                <tr>
                    <th style="width: 34%;"></th>
                    <th style="width: 33%;">@SmartEnergy.Resources.DashboardTexts.Minimo</th>
                    <th style="width: 33%;">@SmartEnergy.Resources.DashboardTexts.Maximo</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="text-align:center">@ViewBag.NomeGrandeza</td>
                    <td>@ViewBag.Min_Dia @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.Min_DataHoraDia)</small></td>
                    <td>@ViewBag.Max_Dia @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.Max_DataHoraDia)</small></td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="portlet-grafico portlet-ea">
        <div>
            <div class="postlet-grafico-ea" id="<EMAIL>"></div>
        </div>
    </div>
}

<script type="text/javascript">

    $(document).ready(function () {

        // entrada analogica
        var dataX = [];
        var labelX = [];
        var dataMedia = [];
        var dataMinima = [];
        var dataMaxima = [];
        var dataZero = [];

        // copia valores
        var Media = @Html.Raw(Json.Encode(@ViewBag.Media));
        var Minima = @Html.Raw(Json.Encode(@ViewBag.Minima));
        var Maxima = @Html.Raw(Json.Encode(@ViewBag.Maxima));
        var Status = @Html.Raw(Json.Encode(@ViewBag.Status));
        var Dias = @Html.Raw(Json.Encode(@ViewBag.Dias));
        var NomeGrandeza = @Html.Raw(Json.Encode(@ViewBag.NomeGrandeza));
        var UnidadeGrandeza = @Html.Raw(Json.Encode(@ViewBag.UnidadeGrandeza));

        var minimoValor = @Html.Raw(Json.Encode(@ViewBag.Valor_min));
        var maximoValor = @Html.Raw(Json.Encode(@ViewBag.Valor_max));

        if( minimoValor < 0 && minimoValor > -10)
        {
            minimoValor = minimoValor * 1.3;
        }


        dataX.push('x');
        dataMaxima.push(['Max']);
        dataMedia.push(['Score']);
        dataMinima.push(['Min']);
        dataZero.push(['Zero']);

        for (i = 0; i < 26; i++)
        {
            // X
            dataX.push(Dias[i]);

            // linha de zero
            dataZero.push([0.0]);

            dataMedia.push([Media[i]]);
            dataMinima.push([Minima[i]]);
            dataMaxima.push([Maxima[i]]);
        }

        // valores label X
        labelX.push(Dias[1]);
        for (i = 4; i <= 24; i+=3)
        {
            labelX.push(Dias[i]);
        }

        var Data = [dataX,dataMaxima,dataMedia,dataMinima,dataZero];

        c3.generate({
            bindto: '#<EMAIL>',
            size: {
                width: 340,
                height: 192
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 35,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    Max: 'line',
                    Score: 'line',
                    Min: 'line',
                    Zero: 'step'
                },
                colors: {
                    Max: 'rgb(200, 217, 240)',
                    Score: 'rgb(120, 178, 235)',
                    Min: 'rgb(200, 217, 240)',
                    Zero: '#dddddd'
                }
            },
            transition: {
                duration: 0
            },
            point: {
                r:2,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*28
                },
                y:  {
                    min: minimoValor,
                    max: maximoValor,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoValor < 10)
                            {
                                return d.toFixed(2);
                            }

                            if( maximoValor < 100)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;
                    for (i = 0; i < 3; i++) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        if (! text) {
                            // split string and create array.
                            var arr = Dias[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // horas e minutos
                            var hours = data.getHours();
                            var minutes = data.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            title = hours + ':' + minutes;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1C84C6'>" + title + "</th></tr>" : "");
                        }

                        switch(i)
                        {
                            case 2:     // minimo
                                name = MINIMO;
                                break;

                            case 1:     // medio
                                name = MEDIO;
                                break;

                            case 0:     // maximo
                                name = MAXIMO;
                                break;
                        }

                        // verifica se sem registro
                        if( Status[d[i].index] == 1 )
                        {
                            value = '---';

                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'>" + name + "</td>";
                            text += "<td class='value'>" + value + " " + UnidadeGrandeza + "</td>";
                            text += "</tr>";
                        }
                        else
                        {
                            value = valueFormat(d[i].value, d[i].ratio, d[i].id, d[i].index);
                            bgcolor = $$.levelColor ? $$.levelColor(d[i].value) : color(d[i].id);

                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                            text += "<td class='value'>" + d[i].value.toFixed(2) + " " + UnidadeGrandeza + "</td>";
                            text += "</tr>";
                        }
                    }
                    return text + "</table>";
                }
            }
        });

        d3.selection.prototype.moveToFront = function () {
            return this.each(function () {
                this.parentNode.appendChild(this);
            });
        };

        d3.select("svg g.c3-grid").moveToFront();

    });

</script>

    