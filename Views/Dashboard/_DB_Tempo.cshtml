﻿@model SmartEnergyLib.SQL.DashboardDominio

@using SmartEnergyLib.SQL

@{
    var str_id2 = string.Format("idX{0}", @Model.IDDashboard);

    <div class="portlet-valorAtual portlet-tempo">
        <h1>@ViewBag.Cidade</h1>
    </div>
    <div class="portlet-datahoraAtual portlet-tempo">
        <h6>@ViewBag.Datahora</h6>
    </div>
    <div id=@str_id2 class="portlet-weather">

        @{
            PrevisaoWeatherDominio previsao = ViewBag.previsao;

            if (previsao == null)
            {
                <table class="table no-margins weather_forecast">
                    <tbody>
                        <tr>
                            <td>Meteorologia indisponível</td>
                        </tr>
                    </tbody>
                </table>
            }
            else
            {
                <table class="table no-margins weather_hoje portlet-tempo">
                    <tbody>
                        <tr>
                            <td style="padding-left:60px;">@previsao.Temp_Atual &deg;C</td>
                            <td style="padding-right:60px;"><canvas id="icon_hoje" width="60" height="60"></canvas></td>
                        </tr>
                    </tbody>
                </table>

                <table class="table no-margins weather_forecast portlet-tempo">
                    <tbody>

                        @{
                            for (int i = 0; i < 4; i++)
                            {
                                var icon_semana = "icon_semana" + i;

                                <tr>
                                    <td style="width: 20%;"></td>
                                    <td style="width: 15%; text-align: right;">@previsao.item[i].DiaSemana</td>
                                    <td style="width: 15%; color:#FF0000; font-weight: bold;">@previsao.item[i].Temp_Max &deg;C</td>
                                    <td style="width: 15%; color:#0000FF; font-weight: bold;">@previsao.item[i].Temp_Min &deg;C</td>
                                    <td style="width: 15%;"><canvas id="@icon_semana" width="16" height="16"></canvas></td>
                                    <td style="width: 20%;"></td>
                                </tr>
                            }
                        }

                    </tbody>
                </table>

            }
        }

    </div>
}


<script type="text/javascript">

    $(document).ready(function () {

        // copia valores
        var Cidade = @Html.Raw(Json.Encode(@ViewBag.Cidade));
        var strID = "#idX" + @Model.IDDashboard;

        var icon_hoje = @Html.Raw(Json.Encode(@ViewBag.icon_hoje));
        var icon_semana0 = @Html.Raw(Json.Encode(@ViewBag.icon_semana0));
        var icon_semana1 = @Html.Raw(Json.Encode(@ViewBag.icon_semana1));
        var icon_semana2 = @Html.Raw(Json.Encode(@ViewBag.icon_semana2));
        var icon_semana3 = @Html.Raw(Json.Encode(@ViewBag.icon_semana3));

        // icones tempo
        var skycons = new Skycons({ "color": "#23C6C8" });

        skycons.add("icon_hoje", ConverteIDSkyCon(icon_hoje));
        skycons.add("icon_semana0", ConverteIDSkyCon(icon_semana0));
        skycons.add("icon_semana1", ConverteIDSkyCon(icon_semana1));
        skycons.add("icon_semana2", ConverteIDSkyCon(icon_semana2));
        skycons.add("icon_semana3", ConverteIDSkyCon(icon_semana3));

        // inicia animacao
        skycons.play();

    });

    function ConverteIDSkyCon(Tempo_Cod_Icone) {

        var tempo = Skycons.CLEAR_DAY;

        switch (Tempo_Cod_Icone) {
            case 0:     // 0 = clear-day
                tempo = Skycons.CLEAR_DAY;
                break;

            case 1:     // 1 = clear-night
                tempo = Skycons.CLEAR_NIGHT;
                break;

            case 2:     // 2 = partly-cloudy-day
                tempo = Skycons.PARTLY_CLOUDY_DAY;
                break;

            case 3:     // 3 = partly-cloudy-night
                tempo = Skycons.PARTLY_CLOUDY_NIGHT;
                break;

            case 4:     // 4 = cloudy
                tempo = Skycons.CLOUDY;
                break;

            case 5:     // 5 = rain
                tempo = Skycons.RAIN;
                break;

            case 6:     // 6 = sleet (granizo)
                tempo = Skycons.SLEET;
                break;

            case 7:     // 7 = snow
                tempo = Skycons.SNOW;
                break;

            case 8:     // 8 = wind
                tempo = Skycons.WIND;
                break;

            case 9:     // 9 = fog
                tempo = Skycons.FOG;
                break;
        }

        return (tempo);
    }

    function ConverteTextoSkyCon(Tempo_Cod_Icone) {

        var tempo_texto = "Indefinido";

        switch (Tempo_Cod_Icone[i]) {
            case 0:     // 0 = clear-day
                tempo_texto = CEU_CLARO;
                break;

            case 1:     // 1 = clear-night
                tempo_texto = NOITE_CLARA;
                break;

            case 2:     // 2 = partly-cloudy-day
                tempo_texto = PARC_NUBLADO;
                break;

            case 3:     // 3 = partly-cloudy-night
                tempo_texto = PARC_NUBLADO;
                break;

            case 4:     // 4 = cloudy
                tempo_texto = NUBLADO;
                break;

            case 5:     // 5 = rain
                tempo_texto = CHUVOSO;
                tempo = Skycons.RAIN;
                break;

            case 6:     // 6 = sleet (granizo)
                tempo_texto = GRANIZO;
                break;

            case 7:     // 7 = snow
                tempo_texto = NEVE;
                break;

            case 8:     // 8 = wind
                tempo_texto = VENTO;
                break;

            case 9:     // 9 = fog
                tempo_texto = NEVOEIRO;
                break;
        }

        return (tempo_texto);
    }

</script>

