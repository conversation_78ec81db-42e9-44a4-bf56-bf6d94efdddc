﻿@model SmartEnergyLib.SQL.DashboardDominio

@{
    <input type="hidden" id="@ViewBag.IDDashboardTxt" name="@ViewBag.IDDashboardTxt" value="@ViewBag.DataAtual">


    <div class="portlet-datahoraAtual portlet-energia">
        <h6>@ViewBag.Data</h6>
    </div>
    
    <div class="portlet-valorAtualLinha">
        <div class="portlet-valorAtual portlet-energia">
            <h1>@ViewBag.ConsTotal <span>@ViewBag.UnidadeConsumo</span></h1>
            <h5>@SmartEnergy.Resources.DashboardTexts.ConsumoTotalMes</h5>
        </div>
        <div class="portlet-valorAtualDireita portlet-util" style="margin-top:0px;">
            <h1>@ViewBag.EconomiaPorcTotal <span>%</span></h1>
            <div class="portlet-valorAtualLinha">
                <h5>@SmartEnergy.Resources.DashboardTexts.Economia</h5>
            </div>
        </div>
    </div>
    
    <div class="portlet-valorAtualLinha">
        <div class="portlet-valorAtual portlet-energia">
            <h1>@ViewBag.EconomiaConsTotal <span>@ViewBag.UnidadeConsumo</span></h1>
            <h5>@SmartEnergy.Resources.DashboardTexts.EconomiaTotalMes</h5>
        </div>
        <div class="portlet-valorAtualDireita portlet-util">
            <h1>@ViewBag.EconomiaTotal</h1>
            <h5>@SmartEnergy.Resources.DashboardTexts.Economia</h5>
        </div>
    </div>
    
    <div class="portlet-grafico portlet-energia" style="margin-top: 10px;">
        <div>
            <div id="<EMAIL>"></div>
        </div>
    </div>
}

<script type="text/javascript">

    $(document).ready(function () {

        // consumo e referencia
        var dataConsumo = [];
        var dataReferencia = [];

        // copia valores
        var Consumo = @Html.Raw(Json.Encode(@ViewBag.Consumo));
        var Referencia = @Html.Raw(Json.Encode(@ViewBag.Referencia));
        var EconomiaConsumo = @Html.Raw(Json.Encode(@ViewBag.EconomiaConsumo));
        var EconomiaValor = @Html.Raw(Json.Encode(@ViewBag.EconomiaValor));
        var EconomiaPercentual = @Html.Raw(Json.Encode(@ViewBag.EconomiaPercentual));
        var Status = @Html.Raw(Json.Encode(@ViewBag.Status));
        var Dias = @Html.Raw(Json.Encode(@ViewBag.Dias));
        var UnidadeConsumo = @Html.Raw(Json.Encode(@ViewBag.UnidadeConsumo));

        var maximoCons = Math.ceil(@ViewBag.Cons_max);
        var NumDias = Math.ceil(@ViewBag.NumDias);

        dataConsumo.push(['data1']);
        dataReferencia.push(['data2']);

        for (i = 0; i < NumDias; i++)
        {
            dataConsumo.push([Consumo[i]]);
            dataReferencia.push([Referencia[i]]);
        }

        var Data = [dataConsumo,dataReferencia];

        c3.generate({
            bindto: '#<EMAIL>',
            size: {
                width: 340,
                height: 192
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 35,
                left: 43
            },
            data: {
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'step'
                },
                colors: {
                    data1: '#00007F',
                    data2: '#00007F',
                }
            },
            transition: {
                duration: 0
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    tick: {
                        outer: false,
                        culling: {
                            max: 5 // the number of tick texts will be adjusted to less than this value
                        },
                        format: function (x) {

                            // split string and create array.
                            var arr = Dias[x].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // retorna dia
                            return data.getDate();
                        }
                    },
                },
                y:  {
                    max: maximoCons,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoCons < 100 )
                            {
                                return(d.toFixed(1));
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.6 },
                //                width: 6,
                //              ratio: 1.0
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < 5; i++) {

                        if (! text) {
                            title = titleFormat ? titleFormat(d[0].x) : d[0].x;
                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + DIA + " " + title + "</th></tr>" : "");
                        }

                        // verifica se sem registro
                        if( Status[d[0].index] == 1 )
                        {
                            // verifica se consumo
                            if( i == 0 )
                            {
                                name = SEM_REGISTRO;
                                value = '---';

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + UnidadeConsumo + "</td>";
                                text += "</tr>";
                            }
                        }
                        else
                        {
                            bgcolor = $$.levelColor ? $$.levelColor(d[0].value) : color(d[0].id);

                            // verifica se consumo
                            if( i == 0 )
                            {
                                name = CONSUMO;

                                if(maximoCons < 100.0)
                                {
                                    value = d[0].value.toFixed(1);
                                }
                                else
                                {
                                    value = d[0].value.toFixed(0);
                                }

                                value = value.replace('.', ',');

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + UnidadeConsumo + "</td>";
                                text += "</tr>";
                            }

                            // verifica se referencia
                            if( i == 1 )
                            {
                                name = REFERENCIA;

                                value = Referencia[d[0].index].toFixed(0);
                                value = value.replace('.', ',');

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + UnidadeConsumo + "</td>";
                                text += "</tr>";
                            }

                            if( i == 2 )
                            {
                                name = ECONOMIA + " (" + UnidadeConsumo + ")";

                                if( EconomiaConsumo[d[0].index] < 100.0 )
                                {
                                    value = EconomiaConsumo[d[0].index].toFixed(1);
                                }
                                else
                                {
                                    value = EconomiaConsumo[d[0].index].toFixed(0);
                                }

                                value = value.replace('.', ',');

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + UnidadeConsumo + "</td>";
                                text += "</tr>";
                            }

                            if( i == 3 )
                            {
                                name = ECONOMIA + ' (%)';

                                value = EconomiaPercentual[d[0].index].toFixed(1);

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'>" + name + "</td>";
                                text += "<td class='value'>" + value + " %</td>";
                                text += "</tr>";
                            }

                            if( i == 4 )
                            {
                                name = ECONOMIA + ' ($)';

                                value = EconomiaValor[d[0].index].toFixed(2);

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'>" + name + "</td>";
                                text += "<td class='value'> $ " + value + "</td>";
                                text += "</tr>";
                            }
                        }
                    }
                    return text + "</table>";
                }
            }
        });

    });


</script>

    