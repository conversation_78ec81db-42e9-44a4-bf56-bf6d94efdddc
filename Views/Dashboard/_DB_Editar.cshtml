﻿@model SmartEnergyLib.SQL.DashboardDominio

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Funcoes

@{
    int IDConsultor = ViewBag._IDConsultor;
    int IDCliente = ViewBag._IDCliente;


    <div class="modal inmodal" id="DB_Editar_Modal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg" style="height:400px;">
            <div class="modal-content animated fadeIn">
                <div class="modal-editar-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only modal-tools-header">Close</span></button>
                    <i class="fa fa-edit modal-editar-title"></i><span class="modal-editar-title">&nbsp;&nbsp;@SmartEnergy.Resources.DashboardTexts.EdicaoBloco</span>
                    <input type="hidden" id="IDDashboard" value=@Model.IDDashboard>
                </div>

                @using (Html.BeginForm("_DB_Editar", "Dashboard", new { ReturnUrl = ViewBag.ReturnUrl }, FormMethod.Post, new { id = "formDashboard", role = "form" }))
                {
                    <div class="modal-body">
                        @Html.HiddenFor(model => model.IDDashboard)
                        @Html.HiddenFor(model => model.IDReferencia)
                        @Html.HiddenFor(model => model.TipoReferencia)
                        @Html.HiddenFor(model => model.IDMedicao)
                        @Html.HiddenFor(model => model.IDDashboardPage)
                        @Html.HiddenFor(model => model.RowNumber)
                        @Html.HiddenFor(model => model.ColumnNumber)
                        @Html.HiddenFor(model => model.Title)
                        @Html.HiddenFor(model => model.PageTitle)
                        <input type="hidden" id="IDTipoMedicao" name="IDTipoMedicao" value="@ViewBag._IDTipoMedicao">

                        <div class="row">
                            <div class="form-group col-xs-12">
                                <label class="control-label">&nbsp;@SmartEnergy.Resources.DashboardTexts.TipoBloco</label>

                                @{
                                    bool View_Financas = (ViewBag.View_Financas == 1) ? true : false;
                                    bool View_KPI = (ViewBag.View_KPI == 1) ? true : false;
                                    bool View_Metas = (ViewBag.View_Metas == 1) ? true : false;

                                    List<ListaTiposDominio> itens = new List<ListaTiposDominio>();
                                    ListaTiposDominio item = new ListaTiposDominio();

                                    itens.Add(Funcoes_SmartEnergy.Item_Dasboard(1, SmartEnergy.Resources.DashboardTexts.DemandaAtivaDia));
                                    itens.Add(Funcoes_SmartEnergy.Item_Dasboard(6, SmartEnergy.Resources.DashboardTexts.DemandaAtivaMes));
                                    itens.Add(Funcoes_SmartEnergy.Item_Dasboard(7, SmartEnergy.Resources.DashboardTexts.DemandaAtivaAno));
                                    itens.Add(Funcoes_SmartEnergy.Item_Dasboard(2, SmartEnergy.Resources.DashboardTexts.Consumo));
                                    itens.Add(Funcoes_SmartEnergy.Item_Dasboard(3, SmartEnergy.Resources.DashboardTexts.FatorPotencia));
                                    itens.Add(Funcoes_SmartEnergy.Item_Dasboard(11, SmartEnergy.Resources.DashboardTexts.EnergiaReativaExcedente));
                                    itens.Add(Funcoes_SmartEnergy.Item_Dasboard(14, SmartEnergy.Resources.DashboardTexts.GeralEnergiaMensal));

                                    if (View_Financas) { itens.Add(Funcoes_SmartEnergy.Item_Dasboard(4, SmartEnergy.Resources.DashboardTexts.Fatura)); }
                                    if (View_Financas) { itens.Add(Funcoes_SmartEnergy.Item_Dasboard(8, SmartEnergy.Resources.DashboardTexts.ComparativoCativoLivreAtual)); }
                                    if (View_Financas) { itens.Add(Funcoes_SmartEnergy.Item_Dasboard(9, SmartEnergy.Resources.DashboardTexts.ComparativoCativoLivreProjetado)); }

                                    if (View_Metas) { itens.Add(Funcoes_SmartEnergy.Item_Dasboard(5, SmartEnergy.Resources.DashboardTexts.MetaConsumo)); }

                                    if (View_KPI) { itens.Add(Funcoes_SmartEnergy.Item_Dasboard(50, SmartEnergy.Resources.DashboardTexts.KPI_EconomiaDia)); }
                                    if (View_KPI) { itens.Add(Funcoes_SmartEnergy.Item_Dasboard(51, SmartEnergy.Resources.DashboardTexts.KPI_EconomiaMes)); }
                                    if (View_KPI) { itens.Add(Funcoes_SmartEnergy.Item_Dasboard(53, SmartEnergy.Resources.DashboardTexts.KPI_ReducaoPotencia)); }

                                    itens.Add(Funcoes_SmartEnergy.Item_Dasboard(10, SmartEnergy.Resources.DashboardTexts.Utilidades));
                                    itens.Add(Funcoes_SmartEnergy.Item_Dasboard(12, SmartEnergy.Resources.DashboardTexts.UtilidadesMes));
                                    itens.Add(Funcoes_SmartEnergy.Item_Dasboard(13, SmartEnergy.Resources.DashboardTexts.UtilidadesAno));

                                    itens.Add(Funcoes_SmartEnergy.Item_Dasboard(20, SmartEnergy.Resources.DashboardTexts.Analogicas));
                                    itens.Add(Funcoes_SmartEnergy.Item_Dasboard(40, SmartEnergy.Resources.DashboardTexts.PrevisaoTempo));
                                }

                                @Html.DropDownListFor(model => model.IDTipoDashboard, new SelectList(itens, "ID", "Descricao", 2), SmartEnergy.Resources.ComumTexts.Selecione, new { @class = "form-control select2_tipo", @onchange = "AlteraTipoDashboard(this.value)" })

                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="form-group col-xs-6">
                                <label class="control-label">&nbsp;@SmartEnergy.Resources.DashboardTexts.Cliente</label>
                                @Html.TextBox("NomeCliente", (string)ViewBag.NomeCliente, new { @class = "form-control", @readonly = "readonly" })
                            </div>
                            <div class="form-group col-xs-6">
                                <label class="control-label">&nbsp;@SmartEnergy.Resources.DashboardTexts.Medicao</label>
                                @Html.TextBox("NomeMedicao", (string)ViewBag.NomeMedicao, new { @class = "form-control", @readonly = "readonly" })
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-xs-12">
                                <button type="button" class="btn btn-primary col-xs-12" data-toggle="modal" data-target="#ModalMedicoes">
                                    Selecionar a Medição
                                </button>
                            </div>
                        </div>

                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                        <button type="submit" class="btn btn-primary">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                    </div>
                }

            </div>
        </div>
    </div>

    <div class="modal inmodal animated fadeIn" id="ModalMedicoes" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-editar-header">
                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only modal-tools-header">Close</span></button>
                    <i class="fa fa-check-square-o modal-editar-title"></i><span class="modal-editar-title">&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.SelecioneMedicao</span>
                </div>
                <div class="modal-body">

                    <table id="dataTables-GrupoUnidMedicoes" class="table table-striped table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>TipoMed</th>
                                <th class="some_tablet">@SmartEnergy.Resources.UsuarioPerfilTexts.Clientes</th>
                                <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Grupos</th>
                                <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Unidades</th>
                                <th>@SmartEnergy.Resources.UsuarioPerfilTexts.Medicoes</th>
                            </tr>
                        </thead>
                        <tfoot>
                            <tr>
                                <th>ID</th>
                                <th>TipoMed</th>
                                <th class="some_tablet">@SmartEnergy.Resources.UsuarioPerfilTexts.Clientes</th>
                                <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Grupos</th>
                                <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Unidades</th>
                                <th>@SmartEnergy.Resources.UsuarioPerfilTexts.Medicoes</th>
                            </tr>
                        </tfoot>
                        <tbody>

                            @{
                                List<CliGateGrupoUnidMedicoesDominio> GrupoUnidMedicoes = ViewBag.GrupoUnidMedicoes;

                                foreach (var medicao in GrupoUnidMedicoes)
                                {
                                    string logo1;

                                    <tr style="cursor: pointer;">
                                        <td>@medicao.IDMedicao</td>
                                        <td>@medicao.IDTipoMedicao</td>
                                        <td class="some_tablet">@medicao.NomeCliente</td>
                                        <td class="some_minidesktop">@medicao.NomeGrupoUnidades</td>
                                        <td class="some_minidesktop">@medicao.NomeUnidade</td>

                                        @{
                                            // energia
                                            logo1 = "/Imagens/icone_lampada24.png";

                                            // energia formula
                                            if (medicao.IDTipoMedicao == TIPO_MEDICAO.ENERGIA_FORMULA)
                                            {
                                                logo1 = "/Imagens/icone_soma24.png";
                                            }

                                            // utilidades
                                            if (medicao.IDTipoMedicao == TIPO_MEDICAO.UTILIDADES || medicao.IDTipoMedicao == TIPO_MEDICAO.UTILIDADES_FORMULA)
                                            {
                                                if (medicao.IDIcone == 0)
                                                {
                                                    logo1 = "/Imagens/icone_gota24.png";
                                                }

                                                if (medicao.IDIcone == 1)
                                                {
                                                    logo1 = "/Imagens/icone_fogo24.png";
                                                }
                                            }

                                            // entradas analogicas
                                            if (medicao.IDTipoMedicao == TIPO_MEDICAO.ENTRADA_ANALOGICA || medicao.IDTipoMedicao == TIPO_MEDICAO.EA_FORMULA)
                                            {
                                                logo1 = "/Imagens/icone_gauge24.png";
                                            }
                                        }

                                        <td><img class="some_celular" src=@logo1 /> @medicao.NomeMedicao</td>
                                    </tr>
                                }
                            }

                        </tbody>
                    </table>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="UtilizarMedicaoAtual();" data-dismiss="modal">@SmartEnergy.Resources.DashboardTexts.UtilizarMedicaoAtual</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                </div>
            </div>
        </div>
    </div>

}

<script>
    $(document).ready(function () {

        //
        // TABELA MEDICOES (MODAL)
        //

        var GrupoUnidMedicoes = $('#dataTables-GrupoUnidMedicoes').DataTable({
            "iDisplayLength": 10,
            dom: 'tp',

            'order': [2, 'asc'],
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "5%" },
            { sWidth: "5%" },
            { sWidth: "20%" },
            { sWidth: "20%" },
            { sWidth: "20%" },
            { sWidth: "40%" }
            ],

            "aoColumnDefs": [
                        {
                            "aTargets": [0],
                            "bVisible": false,
                            "bSortable": false,
                            "bSearchable": false,
                        },
                        {
                            "aTargets": [1],
                            "bVisible": false,
                            "bSortable": true,
                            "bSearchable": true,
                        }
            ],

            'rowCallback': function (row, data, dataIndex) {
            },

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        // campos de busca em cada coluna na tabela
        $('#dataTables-GrupoUnidMedicoes tfoot th').each(function () {
            var title = $(this).text();
            if (title.length > 0)
                $(this).html('<input type="text" placeholder="@SmartEnergy.Resources.UsuarioPerfilTexts.Busca ' + title + '" />');
        });

        // dataTable
        var table = $('#dataTables-GrupoUnidMedicoes').DataTable();

        // aplica a busca
        table.columns().every(function () {
            var that = this;

            $('input', this.footer()).on('keyup change', function () {
                if (that.search() !== this.value) {
                    that
                        .search(this.value)
                        .draw();
                }
            });
        });


        $('#dataTables-GrupoUnidMedicoes tbody').on('click', 'tr', function () {

            // pega dados da linha selecionada
            var table = $('#dataTables-GrupoUnidMedicoes').DataTable();
            var data = table.row(this).data();

            // IDMedicao
            $("#IDMedicao").val(data[0]);
            $("#IDTipoMedicao").val(data[1]);

            // nome do cliente
            $("#NomeCliente").val(data[2]);

            // nome da medicao
            var nome_medicao = data[5];
            nome_medicao = nome_medicao.substr(nome_medicao.indexOf(">") + 2);
            $("#NomeMedicao").val(nome_medicao);

            // apaga janela
            $('#ModalMedicoes').modal('hide');
        });


        //
        // VALIDACAO DOS CAMPOS
        //

        jQuery.validator.addMethod("alphanumeric", function (value, element) {
            return this.optional(element) || /^[\'\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\+\-\_\(\)," "]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

        $("#formDashboard").validate({
            rules: {
                IDTipoDashboard: {
                    required: true
                },
                NomeMedicao: {
                    required: true
                }
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            }
        });

        $("#formDashboard").submit(function(e) {

            e.preventDefault();
            var $form = $(this);

            // verifica se entradas sao validas
            if (!$form.valid()) return false;

            // IDTipoMedicao
            var IDTipoMedicao = parseInt($('input[name=IDTipoMedicao]').val());

            // IDTipoDashboard
            var IDTipoDashboard = parseInt(document.getElementById("IDTipoDashboard").value);

            // flag erro
            var erro = true;

            // verifica se tipo de dashboard eh compativel com medicao
            switch (IDTipoMedicao)
            {
                case 0:
                case 1:

                    if ((IDTipoDashboard >= 1 && IDTipoDashboard <= 9) || IDTipoDashboard == 11 || IDTipoDashboard == 14 || IDTipoDashboard == 40 || IDTipoDashboard == 50 || IDTipoDashboard == 51 || IDTipoDashboard == 52 || IDTipoDashboard == 53) {
                        erro = false;
                    }
                    break;

                case 2:
                case 5:

                    if (IDTipoDashboard == 10 || IDTipoDashboard == 12 || IDTipoDashboard == 13 || IDTipoDashboard == 40) {
                        erro = false;
                    }
                    break;

                case 3:
                case 6:

                    if (IDTipoDashboard == 20 || IDTipoDashboard == 40) {
                        erro = false;
                    }
                    break;

                case -10:
                    erro = false;
                    break;
            }

            if (erro)
            {
                swal({
                    title: ERRO,
                    text: MEDICAO_INCOMPATIVEL,
                    type: "warning",
                    confirmButtonColor: "#f8ac59",
                    confirmButtonText: FECHAR,
                }, function () {
                });
            }
            else
            {
                swal({
                    title: DESEJA_SALVAR_BLOCO,
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#1ab394",
                    confirmButtonText: SALVAR,
                    cancelButtonText: CANCELAR,
                    closeOnConfirm: true
                }, function () {

                    var form = $("#formDashboard");
                    $.ajax({
                        url: '/Dashboard/_DB_Editar',
                        data: $form.serialize(),
                        type: 'POST',
                        success: function (data) {

                            // apaga janela
                            $('#DB_Editar_Modal').modal('hide');

                            // atualiza pagina
                            location.reload();
                        }
                    });

                });
            }
        });


        // IDTipoDashboard
        var IDTipoDashboard = parseInt(document.getElementById("IDTipoDashboard").value);
        AlteraTipoDashboard(IDTipoDashboard.toString());


        // apresenta janela
        $('#DB_Editar_Modal').modal('show');
    });


    function UtilizarMedicaoAtual() {

        // IDMedicao
        $("#IDMedicao").val(0);
        $("#IDTipoMedicao").val(-10);

        // nome do cliente
        $("#NomeCliente").val(UTILIZAR_CLIENTE_ATUAL);

        // nome da medicao
        $("#NomeMedicao").val("Utilizar a Medição Atual");

        // apaga janela
        $('#ModalMedicoes').modal('hide');
    }


    function AlteraTipoDashboard(IDTipoDashboard) {

        // dataTable
        var table = $('#dataTables-GrupoUnidMedicoes').DataTable();

        // filtro
        var filtro = [];

        // encontra filtro para o tipo da medição
        switch (parseInt(IDTipoDashboard))
        {
            case 1:     // Dashboard Demanda Diário
            case 2:     // Dashboard Consumo
            case 3:     // Dashboard Fator de Potência
            case 4:     // Dashboard Fatura
            case 5:     // Dashboard Meta
            case 6:     // Dashboard Demanda Mensal
            case 7:     // Dashboard Demanda Anual
            case 8:     // Dashboard Comparativo Fatura Atual
            case 9:     // Dashboard Comparativo Fatura Projetada
            case 11:    // Dashboard UFER
            case 50:    // Dashboard KPI Economia Diária
            case 51:    // Dashboard KPI Economia Mensal
            case 52:    // Dashboard KPI Economia Anual
            case 53:    // Dashboard KPI Potência Instalada

                filtro.push("0");
                filtro.push("1");
                break;

            case 10:    // Dashboard Utilidades Diário
            case 12:    // Dashboard Utilidades Mensal
            case 13:    // Dashboard Utilidades Anual

                filtro.push("2");
                filtro.push("5");
                break;

            case 20:    // Dashboard EA

                filtro.push("3");
                filtro.push("6");
                break;

            case 40:    // Dashboard Climatempo

                filtro.push("  ");
                break;

            default:

                filtro.push(" ");
                break;
        }

        // filtra
        var mergedFiltro = filtro.join('|');
        table.column(1).search(mergedFiltro, true).draw();
    }

</script>
