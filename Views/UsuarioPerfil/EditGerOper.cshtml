﻿@model SmartEnergyLib.SQL.UsuarioDominio

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.UsuarioPerfilTexts.PerfilUsuario;
}

<style>

    .icones {
        padding: 0px 5px;
        font-size: 18px;
        float: left;
    }

    .titulo {
        margin: 0;
        margin-left: 0px;
        margin-top: 20px;
        margin-bottom: 15px;
        padding-left: 15px;
        padding-top: 5px;
        padding-bottom: 5px;
        font-size: 12px;
        font-weight: bold;
        border-radius: 10px;
        float: left;
    }

    .link_branco a:link {
        color: #ffffff !important;
    }

    .link_branco a:visited {
        color: #ffffff !important;
    }

    .link_branco a:hover {
        color: #f0f0f0 !important;
    }

    .link_branco a:active {
        color: #ffffff !important;
    }

    .link_preto a:link {
        color: #7E6A6C !important;
    }

    .link_preto a:visited {
        color: #7E6A6C !important;
    }

    .link_preto a:hover {
        color: #a0a0a0 !important;
    }

    .link_preto a:active {
        color: #7E6A6C !important;
    }

    table.dataTable.select tbody tr,
    table.dataTable thead th:first-child {
        cursor: pointer;
    }

    tfoot input {
        width: 100%;
        padding: 3px;
        box-sizing: border-box;
    }

    #dataTables-medicoes tbody tr.selected {
        color: white;
        background-color: #1ab394;
    }

    .overlay_aguarde {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.45);
        z-index: 9999;
        color: white;
        display: inline-block;
        border-radius: 6px;
        margin-bottom: 20px;
        margin-left: 14px;
        margin-right: 14px;
    }

    .sk-spinner-wave div {
        background-color: #ffffff !important;
    }

    .spinner-aguarde {
        margin: 0;
        position: absolute;
        top: 50%;
        left: 50%;
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
    }
</style>


<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("Edit", "UsuarioPerfil", new { ReturnUrl = ViewBag.ReturnUrl }, FormMethod.Post, new { id = "form", role = "form" }))
            {
                @Html.Hidden("IDCliente", Model.IDCliente)
                @Html.Hidden("LogoConsult", Model.LogoConsult)
                @Html.Hidden("IDTema", Model.IDTema)

                @Html.Hidden("Bloqueio", Model.Bloqueio)
                @Html.Hidden("BloqueioEm", Model.BloqueioEm)
                @Html.Hidden("SenhaAtual", Model.SenhaAtual)
                @Html.Hidden("CodigoSegundoFator", Model.CodigoSegundoFator)
                @Html.Hidden("ExpirarSegundoFator", Model.ExpirarSegundoFator)

                @Html.Hidden("TentativasLogin", Model.TentativasLogin)
                @Html.Hidden("EmailAtual", Model.EmailAtual)
                @Html.Hidden("EmailConfirmado", Model.EmailConfirmado)
                @Html.Hidden("CodigoAtivacao", Model.CodigoAtivacao)
                @Html.Hidden("ShowIntro", Model.ShowIntro)
                @Html.Hidden("AceiteLGPD", Model.AceiteLGPD)

                @Html.Hidden("ConfigMed", Model.ConfigMed)
                @Html.Hidden("ConfigMedEmail", Model.ConfigMedEmail)

                @Html.Hidden("Alarme", Model.Alarme)
                @Html.Hidden("Diario", Model.Diario)
                @Html.Hidden("Semanal", Model.Semanal)
                @Html.Hidden("Mensal", Model.Mensal)
                @Html.Hidden("DemAtv", Model.DemAtv)
                @Html.Hidden("FatPot", Model.FatPot)
                @Html.Hidden("Consumo", Model.Consumo)
                @Html.Hidden("Supervisao", Model.Supervisao)
                @Html.Hidden("Util_Anal_Ciclo", Model.Util_Anal_Ciclo)
                @Html.Hidden("Gestao_Contrato", Model.Gestao_Contrato)
                @Html.Hidden("Gestao_Energia", Model.Gestao_Energia)

                @Html.Hidden("Ranking", Model.Ranking)
                @Html.Hidden("Rateio", Model.Rateio)

                @Html.Hidden("MsgSys", Model.MsgSys)
                @Html.Hidden("MsgCliMed", Model.MsgCliMed)
                @Html.Hidden("MsgCliEve", Model.MsgCliEve)

                @Html.Hidden("MsgCliMed_Dem", Model.MsgCliMed_Dem)
                @Html.Hidden("MsgCliMed_Cons", Model.MsgCliMed_Cons)
                @Html.Hidden("MsgCliMed_FatPot", Model.MsgCliMed_FatPot)

                @Html.Hidden("MsgCliEve_FEner", Model.MsgCliEve_FEner)
                @Html.Hidden("MsgCliEve_RepDem", Model.MsgCliEve_RepDem)
                @Html.Hidden("MsgCliEve_Alarm", Model.MsgCliEve_Alarm)

                @Html.Hidden("IDDashboardGrupo", Model.IDDashboardGrupo)
                @Html.Hidden("View_Analises", Model.View_Analises)
                @Html.Hidden("View_Financas", Model.View_Financas)
                @Html.Hidden("View_KPI", Model.View_KPI)
                @Html.Hidden("View_Ranking", Model.View_Ranking)
                @Html.Hidden("View_Rateio", Model.View_Rateio)
                @Html.Hidden("View_Metas", Model.View_Metas)
                @Html.Hidden("View_Configuracao", Model.View_Configuracao)
                @Html.Hidden("View_ConfiguracaoRemota", Model.View_ConfiguracaoRemota)
                @Html.Hidden("View_PerfilUsuario", Model.View_PerfilUsuario)

                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoUsuarios</h4>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="form-group col-lg-2">
                                <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.IDUsuario</label>
                                @Html.TextBoxFor(model => model.IDUsuario, new { @class = "form-control", @disabled = "disabled" })
                            </div>
                            <div class="col-lg-offset-4 col-lg-6">
                                <div class="ibox-tools">
                                    <!--  <button class="btn btn-default" onclick="window.history.go(-1)">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button> -->
                                    <button type="button" class="btn btn-primary" id="BotaoSalvar" onclick="Salvar();">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                </div>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="tabs-container">
                                    <ul class="nav nav-tabs">
                                        <li class="active"><a data-toggle="tab" href="#tab-1"><i class="fa fa-cogs"></i> @SmartEnergy.Resources.ConfiguracaoTexts.Geral</a></li>
                                        <li class="tab-2"><a data-toggle="tab" href="#tab-2"><i class="fa fa-bell"></i> @SmartEnergy.Resources.ConfiguracaoTexts.Gerenciamento</a></li>
                                    </ul>
                                    <div class="tab-content">
                                        <div id="tab-1" class="tab-pane active">
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Usuario</label>
                                                        @Html.TextBoxFor(model => model.Login, new { @class = "form-control", @readonly = "readonly" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.TipoAcesso</label>
                                                        @Html.DropDownListFor(model => model.IDTipoAcesso, new SelectList(ViewBag.listaTipoAcesso, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.TipoAcesso),
                                                            new { @class = "form-control" })
                                                    </div>
                                                </div>
                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-2">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Senha</label>
                                                        @Html.TextBoxFor(model => model.Senha, new { @class = "form-control", @type = "password", @maxlength = "20" })
                                                    </div>
                                                    <div class="form-group col-lg-2">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Confirmacao</label>
                                                        <input id="Confirmacao" type="password" class="form-control" name="SenhaConfirma" placeholder="@SmartEnergy.Resources.UsuarioPerfilTexts.Confirmacao" value=@Html.DisplayFor(model => model.Senha)>
                                                    </div>
                                                    <div class="form-group col-lg-2">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.SegundoFator</label>
                                                        @Html.DropDownListFor(model => model.IDTipoSegundoFator, new SelectList(ViewBag.listaTiposSegundoFator, "ID", "Descricao"),
                                                            @SmartEnergy.Resources.ConfiguracaoTexts.Selecione,
                                                            new { @class = "form-control" })
                                                    </div>
                                                    <div class="form-group col-lg-2">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.TempoSenhaExpirar</label>
                                                        @Html.DropDownListFor(model => model.ExpirarMeses, new SelectList(ViewBag.listaExpirar, "ID", "Descricao"),
                                                            @SmartEnergy.Resources.ConfiguracaoTexts.Selecione,
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-2">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.SenhaExpirar</label>

                                                        @{
                                                            if (Model.ExpirarMeses > 0)
                                                            {
                                                                @Html.TextBoxFor(model => model.ExpirarEm, "{0:dd/MM/yyyy}", new { @class = "form-control", @disabled = "disabled" })
                                                            }
                                                            else
                                                            {
                                                                <input type="text" class="form-control" value="Senha não expira" disabled="">
                                                                @Html.Hidden("ExpirarEm", Model.ExpirarEm)
                                                            }
                                                        }

                                                    </div>
                                                </div>
                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Nome</label>
                                                        @Html.TextBoxFor(model => model.NomeUsuario, new { @class = "form-control", @maxlength = "50" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Apelido</label>
                                                        @Html.TextBoxFor(model => model.Apelido, new { @class = "form-control", @maxlength = "50" })
                                                    </div>
                                                </div>
                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;Email</label>
                                                        @Html.TextBoxFor(model => model.Email, new { @class = "form-control", @type = "email", @maxlength = "80" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;Email @SmartEnergy.Resources.UsuarioPerfilTexts.Confirmacao</label>
                                                        <input id="EmailConfirma" type="email" class="form-control" name="EmailConfirma" placeholder="@SmartEnergy.Resources.UsuarioPerfilTexts.Confirmacao" value=@Html.DisplayFor(model => model.Email)>
                                                    </div>
                                                </div>
                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Telefone</label>
                                                        @{
                                                            if (Model.Telefone.Length > 14)
                                                            {
                                                                @Html.TextBoxFor(model => model.Telefone, new { @class = "form-control", data_mask = "(99) 99999-999?9" })
                                                            }
                                                            else
                                                            {
                                                                @Html.TextBoxFor(model => model.Telefone, new { @class = "form-control", data_mask = "(99) 9999-9999?9" })
                                                            }
                                                        }
                                                    </div>
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Ramal</label>
                                                        @Html.TextBoxFor(model => model.Ramal, new { @class = "form-control", @maxlength = "6" })
                                                    </div>
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Celular</label>
                                                        @{
                                                            if (Model.Celular.Length > 14)
                                                            {
                                                                @Html.TextBoxFor(model => model.Celular, new { @class = "form-control", data_mask = "(99) 99999-999?9" })
                                                            }
                                                            else
                                                            {
                                                                @Html.TextBoxFor(model => model.Celular, new { @class = "form-control", data_mask = "(99) 9999-9999?9" })
                                                            }
                                                        }
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Cargo</label>
                                                        @Html.TextBoxFor(model => model.Cargo, new { @class = "form-control", @maxlength = "50" })
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Departamento</label>
                                                        @Html.TextBoxFor(model => model.Departamento, new { @class = "form-control", @maxlength = "50" })
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Idioma</label>
                                                        @Html.DropDownListFor(model => model.IDIdioma, new SelectList(ViewBag.listaTiposIdioma, "IDIdioma", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.Idioma),
                                                            new { @class = "form-control" })
                                                    </div>
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;API Key</label>
                                                        @Html.TextBoxFor(model => model.API_Key, new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;API Requisições Limite (Diário)</label>
                                                        @Html.TextBoxFor(model => model.API_Limite, new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div id="tab-2" class="tab-pane">
                                            <div class="panel-body">

                                                <div class="row">
                                                    <div class="col-lg-8">
                                                        <h2 style="margin-top:0px;"><i class="fa fa-envelope"></i>&nbsp;&nbsp;Selecione os Relatórios e Alertas que o usuário deseja receber</h2>
                                                    </div>
                                                </div>
                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-2">
                                                        <label class="control-label"><i class="fa fa-envelope"></i>&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.ReceberRelatorios</label><br />
                                                        <div class="i-checks" style="margin-top:12px; margin-left:10px;">
                                                            <input id="DiarioAux" type="checkbox" @(Model.Diario == 1 ? "checked" : "")>&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Diario<br /><br />
                                                            <input id="SemanalAux" type="checkbox" @(Model.Semanal == 1 ? "checked" : "")>&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Semanal<br /><br />
                                                            <input id="MensalAux" type="checkbox" @(Model.Mensal == 1 ? "checked" : "")>&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Mensal<br /><br /><br />
                                                            <input id="RateioAux" type="checkbox" @(Model.Rateio == 1 ? "checked" : "")>&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Rateio<br /><br />

                                                            <div style="display:none;">
                                                                <input id="RankingAux" type="checkbox" @(Model.Ranking == 1 ? "checked" : "")>&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Ranking<br /><br />
                                                            </div>

                                                        </div>
                                                    </div>

                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;</label><br />
                                                        <div class="i-checks" style="margin-top:12px; margin-left:10px;">
                                                            <input id="DemAtvAux" type="checkbox" @(Model.DemAtv == 1 ? "checked" : "")>&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.DemandaAtiva<br /><br />
                                                            <input id="FatPotAux" type="checkbox" @(Model.FatPot == 1 ? "checked" : "")>&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.FatorPotencia<br /><br />
                                                            <input id="ConsumoAux" type="checkbox" @(Model.Consumo == 1 ? "checked" : "")>&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Consumo<br /><br />
                                                            <input id="SupervisaoAux" type="checkbox" @(Model.Supervisao == 1 ? "checked" : "")>&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Supervisao<br /><br />
                                                            <input id="UtilAnalCicloAux" type="checkbox" @(Model.Util_Anal_Ciclo == 1 ? "checked" : "")>&nbsp;&nbsp;Utilidades | Analógica | Ciclômetro<br /><br />
                                                        </div>
                                                    </div>

                                                    <div class="form-group col-lg-4">
                                                        <div class="row">
                                                            <div class="form-group col-lg-12">
                                                                <label class="control-label"><i class="fa fa-bell"></i>&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.ReceberAlertas</label><br />
                                                                @Html.DropDownListFor(model => model.MsgEmail, new SelectList(ViewBag.listatiposMsgEmail, "ID", "Descricao"),
                                                                    string.Format("{0}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione),
                                                                    new { @class = "form-control" })
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                <div class="i-checks" style="margin-top:12px; margin-left:10px;">
                                                                    <input id="MsgCliMed_Dem_Aux" type="checkbox" @(Model.MsgCliMed_Dem == 1 ? "checked" : "")>&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.DemandaAtiva<br /><br />
                                                                    <input id="MsgCliMed_FatPot_Aux" type="checkbox" @(Model.MsgCliMed_FatPot == 1 ? "checked" : "")>&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.FatorPotencia<br /><br />
                                                                    <input id="MsgCliMed_Cons_Aux" type="checkbox" @(Model.MsgCliMed_Cons == 1 ? "checked" : "")>&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Consumo<br /><br />
                                                                </div>
                                                            </div>
                                                            <div class="form-group col-lg-6">
                                                                <div class="i-checks" style="margin-top:12px; margin-left:10px;">
                                                                    <input id="MsgCliEveAux" type="checkbox" @(Model.MsgCliEve == 1 ? "checked" : "")>&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Eventos<br /><br />
                                                                    <input id="MsgCliEve_FEner_Aux" type="checkbox" @(Model.MsgCliEve_FEner == 1 ? "checked" : "") disabled>&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.FalhaEnergia<br /><br />
                                                                    <input id="MsgCliEve_RepDem_Aux" type="checkbox" @(Model.MsgCliEve_RepDem == 1 ? "checked" : "") disabled>&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.RepDem<br /><br />
                                                                    <input id="MsgCliEve_Alarm_Aux" type="checkbox" @(Model.MsgCliEve_Alarm == 1 ? "checked" : "") disabled>&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Alarmes<br /><br />
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    @{
                                                        int IDConsultor = ViewBag._IDConsultor;

                                                        string display_cpfl = "none";

                                                        // somente CPFL
                                                        if (IDConsultor == CLIENTES_ESPECIAIS.CPFL)
                                                        {
                                                            display_cpfl = "block";
                                                        }

                                                    }

                                                    <div style="display:@display_cpfl ;">
                                                        <div class="form-group col-lg-2">
                                                            <label class="control-label"><i class="fa fa-file-text-o"></i>&nbsp;Contratos de Gestão e Energia</label><br />
                                                            <div class="i-checks" style="margin-top:12px; margin-left:10px;">
                                                                <input id="GestaoContratoAux" type="checkbox" @(Model.Gestao_Contrato == 1 ? "checked" : "")>&nbsp;&nbsp;Contratos de Gestão<br /><br />
                                                                <input id="GestaoEnergiaAux" type="checkbox" @(Model.Gestao_Energia == 1 ? "checked" : "")>&nbsp;&nbsp;Contratos de Energia<br /><br />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <hr />

                                                <div class="row">

                                                    <div class="col-lg-8">
                                                        <h2 style="margin-top:0px;"><i class="fa fa-bars"></i>&nbsp;&nbsp;Lista de Medições que o usuário poderá visualizar e receber relatórios e alertas</h2>
                                                    </div>

                                                    @{
                                                        if (Model.IDTipoAcesso == TIPO_ACESSO.CLIENTE_ADMIN)
                                                        {
                                                            <div class="col-lg-2">
                                                                <a id="BotaoAdicionarMedicao" href="#" onclick="ModalMedicoes();" class="btn btn-info btn-lg pull-left" style="color:#ffffff; width:100%;">@SmartEnergy.Resources.UsuarioPerfilTexts.AdicionarMedicao</a>
                                                            </div>
                                                            <div class="col-lg-2">
                                                                <a id="BotaoExcluirTodos" href="#" onclick="javascript:Excluir_Todos();" class="btn btn-success btn-lg pull-left" style="color:#ffffff; width:100%;">@SmartEnergy.Resources.ConfiguracaoTexts.ExcluirTodas</a>
                                                            </div>
                                                        }
                                                    }

                                                </div>
                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-12">
                                                        <table id="dataTables-medicoes" class="table table-striped table-bordered table-hover">
                                                            <thead>
                                                                <tr>
                                                                    <th>ID</th>
                                                                    <th class="some_tablet">@SmartEnergy.Resources.UsuarioPerfilTexts.Clientes</th>
                                                                    <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Grupos</th>
                                                                    <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Unidades</th>
                                                                    <th>@SmartEnergy.Resources.UsuarioPerfilTexts.Medicoes</th>
                                                                    <th></th>
                                                                    <th>RelatDiario</th>
                                                                    <th>RelatSemanal</th>
                                                                    <th>RelatMensal</th>
                                                                    <th>RelatDemAtv</th>
                                                                    <th>RelatFatPot</th>
                                                                    <th>RelatConsumo</th>
                                                                    <th>RelatSupervisao</th>
                                                                    <th>RelatRanking</th>
                                                                    <th>MsgGeren</th>
                                                                    <th>MsgProcessa</th>
                                                                </tr>
                                                            </thead>
                                                            <tfoot>
                                                                <tr>
                                                                    <th>ID</th>
                                                                    <th class="some_tablet">@SmartEnergy.Resources.UsuarioPerfilTexts.Clientes</th>
                                                                    <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Grupos</th>
                                                                    <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Unidades</th>
                                                                    <th>@SmartEnergy.Resources.UsuarioPerfilTexts.Medicoes</th>
                                                                    <th></th>
                                                                    <th></th>
                                                                    <th></th>
                                                                    <th></th>
                                                                    <th></th>
                                                                    <th></th>
                                                                    <th></th>
                                                                    <th></th>
                                                                    <th></th>
                                                                    <th></th>
                                                                    <th></th>
                                                                </tr>
                                                            </tfoot>
                                                            <tbody>

                                                                @{
                                                                    List<CliGateGrupoUnidMedicoesDominio> medicoes = ViewBag.Medicoes;
                                                                    List<UsuarioMedicaoDominio> usuarioMedicoes = ViewBag.usuarioMedicoes;

                                                                    foreach (var medicao in medicoes)
                                                                    {
                                                                        foreach (var usuarioMedicao in usuarioMedicoes)
                                                                        {
                                                                            if (medicao.IDMedicao == usuarioMedicao.IDMedicao)
                                                                            {
                                                                                <tr>
                                                                                    <td>@medicao.IDMedicao</td>
                                                                                    <td class="some_tablet">@medicao.NomeCliente</td>
                                                                                    <td class="some_minidesktop">@medicao.NomeGrupoUnidades</td>
                                                                                    <td class="some_minidesktop">@medicao.NomeUnidade</td>
                                                                                    <td>@medicao.NomeMedicao</td>
                                                                                    <td class="link_preto">

                                                                                        @{
                                                                                            if (Model.IDTipoAcesso == TIPO_ACESSO.CLIENTE_ADMIN)
                                                                                            {
                                                                                                <a href="#" class="confirm-delete-medicao"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>
                                                                                            }
                                                                                        }

                                                                                    </td>
                                                                                    <td>@usuarioMedicao.RelatDiario</td>
                                                                                    <td>@usuarioMedicao.RelatSemanal</td>
                                                                                    <td>@usuarioMedicao.RelatMensal</td>
                                                                                    <td>@usuarioMedicao.RelatDemAtv</td>
                                                                                    <td>@usuarioMedicao.RelatFatPot</td>
                                                                                    <td>@usuarioMedicao.RelatConsumo</td>
                                                                                    <td>@usuarioMedicao.RelatSupervisao</td>
                                                                                    <td>@usuarioMedicao.RelatRanking</td>
                                                                                    <td>@usuarioMedicao.MsgGeren</td>
                                                                                    <td>@usuarioMedicao.MsgProcessa</td>
                                                                                </tr>

                                                                                break;
                                                                            }
                                                                        }
                                                                    }
                                                                }

                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>

                                                <div class="div_gerenciamento_selecione" style="display:block;">
                                                    @{
                                                        if (Model.IDTipoAcesso == TIPO_ACESSO.CLIENTE_ADMIN)
                                                        {
                                                            <div class="row">
                                                                <div class="form-group col-lg-12">
                                                                    <h2 style="margin-top:0px;"><i class="fa fa-exclamation-circle"></i>&nbsp;&nbsp;Caso não selecionar nenhuma medição, o usuário poderá visualizar TODAS as medições do cliente.</h2>
                                                                </div>
                                                            </div>
                                                        }
                                                    }
                                                </div>

                                                <br /><br />

                                                <div class="div_gerenciamento" style="display:none;">
                                                    <div class="row">
                                                        <div class="form-group col-lg-2">
                                                            <label class="control-label"><i class="fa fa-envelope"></i>&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.ReceberRelatorios</label><br />
                                                            <div class="i-checks" style="margin-top:12px; margin-left:10px;">
                                                                <input id="RelatDiario" type="checkbox">&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Diario<br /><br />
                                                                <input id="RelatSemanal" type="checkbox">&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Semanal<br /><br />
                                                                <input id="RelatMensal" type="checkbox">&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Mensal<br /><br />

                                                                <div style="display:none;">
                                                                    <input id="RelatRanking" type="checkbox">&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Ranking<br /><br />
                                                                </div>

                                                            </div>
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label">&nbsp;</label><br />
                                                            <div class="i-checks" style="margin-top:12px; margin-left:10px;">
                                                                <input id="RelatDemAtv" type="checkbox">&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.DemandaAtiva<br /><br />
                                                                <input id="RelatFatPot" type="checkbox">&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.FatorPotencia<br /><br />
                                                                <input id="RelatConsumo" type="checkbox">&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Consumo<br /><br />
                                                                <input id="RelatSupervisao" type="checkbox">&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Supervisao<br /><br />
                                                            </div>
                                                        </div>
                                                        <div class="form-group col-lg-3">
                                                            <label class="control-label"><i class="fa fa-bell"></i>&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.ReceberAlertas</label><br />
                                                            <div class="i-checks" style="margin-top:12px; margin-left:10px;">
                                                                <input id="MsgGeren" type="checkbox">&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Gerenciamento<br /><br />
                                                                <input id="MsgProcessa" type="checkbox">&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.Processamento<br /><br />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal inmodal animated fadeIn" id="ModalMedicoes" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                                        <h4 class="modal-title"><i class="fa fa-check-square-o"></i>&nbsp;&nbsp;@SmartEnergy.Resources.UsuarioPerfilTexts.SelecioneMedicao</h4>
                                    </div>
                                    <div class="modal-body">

                                        <table id="dataTables-medicoes2" class="table table-striped table-bordered table-hover">
                                            <thead>
                                                <tr>
                                                    <th><input name="select_all" value="1" type="checkbox"></th>
                                                    <th>ID</th>
                                                    <th class="some_tablet">@SmartEnergy.Resources.UsuarioPerfilTexts.Clientes</th>
                                                    <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Grupos</th>
                                                    <th class="some_minidesktop">@SmartEnergy.Resources.UsuarioPerfilTexts.Unidades</th>
                                                    <th>@SmartEnergy.Resources.UsuarioPerfilTexts.Medicoes</th>
                                                </tr>
                                            </thead>
                                            <tbody>

                                                @{
                                                    List<CliGateGrupoUnidMedicoesDominio> medicoes2 = ViewBag.Medicoes2;

                                                    foreach (var medicao in medicoes2)
                                                    {
                                                        <tr>
                                                            <td>@medicao.IDMedicao</td>
                                                            <td>@medicao.IDMedicao</td>
                                                            <td class="some_tablet">@medicao.NomeCliente</td>
                                                            <td class="some_minidesktop">@medicao.NomeGrupoUnidades</td>
                                                            <td class="some_minidesktop">@medicao.NomeUnidade</td>
                                                            <td>@medicao.NomeMedicao</td>
                                                        </tr>
                                                    }
                                                }

                                            </tbody>
                                        </table>

                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                        <button type="button" class="btn btn-primary" onclick="fnClickAddRowMedicoes();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoInserir</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>

                <div class="overlay_aguarde" style="display: none">
                    <div class="spinner-aguarde">
                        <div class="sk-spinner sk-spinner-wandering-cubes">
                            <div class="sk-spinner sk-spinner-wave">
                                <div class="sk-rect1 text-ligh"></div>
                                <div class="sk-rect2"></div>
                                <div class="sk-rect3"></div>
                                <div class="sk-rect4"></div>
                                <div class="sk-rect5"></div>
                            </div>
                        </div>
                    </div>
                </div>
            }

            <div class="modal inmodal animated fadeIn" id="ModalBemvindo" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                            <i class="fa fa-laptop modal-icon"></i>
                            <h4 class="modal-title">@SmartEnergy.Resources.LoginTexts.LoginBemVindo</h4>
                        </div>
                        <div class="modal-body">
                            <br />
                            <p>
                                Visando maior segurança, neste seu primeiro acesso, solicitamos que altere a sua senha de acesso e verifique os seus dados cadastrais.
                            </p>

                        </div>
                        <div class="modal-footer">
                            <div class="row">
                                <div class="col-lg-offset-3 col-lg-6">
                                    <button type="button" class="btn btn-primary block full-width m-b" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoContinuar</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal inmodal animated fadeIn" id="ModalExpirou" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                            <i class="fa fa-laptop modal-icon"></i>
                            <h4 class="modal-title">@SmartEnergy.Resources.LoginTexts.MudarSenha</h4>
                        </div>
                        <div class="modal-body">
                            <h2>
                                Foi detectado que sua senha expirou.
                            </h2>
                            <br />
                            <p>
                                Visando maior segurança, solicitamos que altere a sua senha de acesso e verifique os seus dados cadastrais.
                            </p>

                        </div>
                        <div class="modal-footer">
                            <div class="row">
                                <div class="col-lg-offset-3 col-lg-6">
                                    <button type="button" class="btn btn-primary block full-width m-b" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoContinuar</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/plugins/iCheck")
    @Scripts.Render("~/declaracoes/Constants")
    @Scripts.Render("~/declaracoes/Validates")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


<script type="text/javascript">

    // Array holding selected row IDs
    var rows_selected_clientes = [];
    var names_selected_clientes = [];

    var rows_selected_medicoes = [];
    var names_selected_medicoes_cli = [];
    var names_selected_medicoes_grupo = [];
    var names_selected_medicoes_unid = [];
    var names_selected_medicoes_med = [];

    var names_selected_medicoes_relatdiario = [];
    var names_selected_medicoes_relatsemanal = [];
    var names_selected_medicoes_relatmensal = [];

    var names_selected_medicoes_relatdematv = [];
    var names_selected_medicoes_relatfatpot = [];
    var names_selected_medicoes_relatconsumo = [];
    var names_selected_medicoes_relatsupervisao = [];

    var names_selected_medicoes_relatranking = [];

    var names_selected_medicoes_msggeren = [];
    var names_selected_medicoes_msgprocessa = [];

    //
    // Updates "Select all" control in a data table
    //
    function updateDataTableSelectAllCtrl(table) {
        var $table = table.table().node();
        var $chkbox_all = $('tbody input[type="checkbox"]', $table);
        var $chkbox_checked = $('tbody input[type="checkbox"]:checked', $table);
        var chkbox_select_all = $('thead input[name="select_all"]', $table).get(0);

        // If none of the checkboxes are checked
        if ($chkbox_checked.length === 0) {
            chkbox_select_all.checked = false;
            if ('indeterminate' in chkbox_select_all) {
                chkbox_select_all.indeterminate = false;
            }

            // If all of the checkboxes are checked
        } else if ($chkbox_checked.length === $chkbox_all.length) {
            chkbox_select_all.checked = true;
            if ('indeterminate' in chkbox_select_all) {
                chkbox_select_all.indeterminate = false;
            }

            // If some of the checkboxes are checked
        } else {
            chkbox_select_all.checked = true;
            if ('indeterminate' in chkbox_select_all) {
                chkbox_select_all.indeterminate = true;
            }
        }
    }

    $(document).ready(function () {

        // classe iChecks
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });

        //
        // VALIDACAO DOS CAMPOS
        //
        Validator_AddMethods();

        $("#form").validate({
            rules: {
                IDTipoAcesso: { required: true },
                NomeUsuario: {
                    required: true,
                    alphanumeric: true,
                    minlength: 3,
                    maxlength: 50
                },
                Apelido: {
                    required: true,
                    alphanumeric: true,
                    minlength: 3,
                    maxlength: 50
                },
                Email: {
                    required: true
                },
                EmailConfirma: {
                    equalTo: "#Email"
                },
                Login: {
                    required: true,
                    login: true,
                    minlength: 6,
                    maxlength: 50
                },
                Senha: {
                    required: true,
                    checkSenha: true,
                    checkSimboloPermitido: true,
                    checkCaracteresSequenciais: true,
                    minlength: 8,
                    maxlength: 20
                },
                SenhaConfirma: {
                    equalTo: "#Senha"
                },
                ExpirarMeses: {
                    required: true
                }
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            }
        });

        // desabilita campos
        disableAll();

        //
        // TABELA MEDICOES
        //

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('#dataTables-medicoes').DataTable({
            "iDisplayLength": 10,
            dom: 'tp',

            'order': [1, 'asc'],
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "5%" },
            { sWidth: "19%" },
            { sWidth: "19%" },
            { sWidth: "19%" },
            { sWidth: "35%" },
            { sWidth: "8%" },
            { sWidth: "5%" },
            { sWidth: "5%" },
            { sWidth: "5%" },
            { sWidth: "5%" },
            { sWidth: "5%" },
            { sWidth: "5%" },
            { sWidth: "5%" },
            { sWidth: "5%" },
            { sWidth: "5%" },
            { sWidth: "5%" },
            ],

            "columnDefs": [
                 {
                     "targets": [0],
                     "visible": false,
                     "searchable": false,
                     "orderable": false
                 },
                 { "targets": [1], "sType": "portugues" },
                 { "targets": [2], "sType": "portugues" },
                 { "targets": [3], "sType": "portugues" },
                 { "targets": [4], "sType": "portugues" },
                 { "targets": [5], "searchable": false, "orderable": false },
                 { "targets": [6], "visible": false, "searchable": false, "orderable": false },
                 { "targets": [7], "visible": false, "searchable": false, "orderable": false },
                 { "targets": [8], "visible": false, "searchable": false, "orderable": false },
                 { "targets": [9], "visible": false, "searchable": false, "orderable": false },
                 { "targets": [10], "visible": false, "searchable": false, "orderable": false },
                 { "targets": [11], "visible": false, "searchable": false, "orderable": false },
                 { "targets": [12], "visible": false, "searchable": false, "orderable": false },
                 { "targets": [13], "visible": false, "searchable": false, "orderable": false },
                 { "targets": [14], "visible": false, "searchable": false, "orderable": false },
                 { "targets": [15], "visible": false, "searchable": false, "orderable": false },
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        // desabilita se NAO for cliente - administrador
        var IDTipoAcesso = document.getElementById('IDTipoAcesso').value

        if (IDTipoAcesso != TIPO_ACESSO_CLIENTE_ADMIN) {
            // desabilita coluna
            var dt = $('#dataTables-medicoes').DataTable();
            dt.column(5).visible(false);
        }

        //
        // TABELA MEDICOES - BUSCA
        //

        // campos de busca em cada coluna na tabela
        $('#dataTables-medicoes tfoot th').each(function () {
            var title = $(this).text();
            if (title.length > 0)
                $(this).html('<input type="text" placeholder="@SmartEnergy.Resources.UsuarioPerfilTexts.Busca ' + title + '" />');
        });

        // dataTable
        var table = $('#dataTables-medicoes').DataTable();

        // aplica a busca
        table.columns().every(function () {
            var that = this;

            $('input', this.footer()).on('keyup change', function () {
                if (that.search() !== this.value) {
                    that
                        .search(this.value)
                        .draw();
                }
            });
        });

        //
        // TABELA MEDICOES - SELECAO
        //

        $('#dataTables-medicoes tbody').on('click', 'tr', function () {

            if ($(this).hasClass('selected')) {

                // retira selecao atual
                $(this).removeClass('selected');

                // desabilita todos
                $("#RelatDiario").iCheck("uncheck");
                $("#RelatSemanal").iCheck("uncheck");
                $("#RelatMensal").iCheck("uncheck");
                $("#RelatDemAtv").iCheck("uncheck");
                $("#RelatFatPot").iCheck("uncheck");
                $("#RelatConsumo").iCheck("uncheck");
                $("#RelatSupervisao").iCheck("uncheck");
                $("#RelatRanking").iCheck("uncheck");
                $("#MsgGeren").iCheck("uncheck");
                $("#MsgProcessa").iCheck("uncheck");

                // desabilita
                //$(".div_gerenciamento *").prop('disabled', true);
                //$('.div_gerenciamento').css("display", "none");
                //$('.div_gerenciamento_selecione').css("display", "block");
            }
            else {

                // habilita se for cliente - administrador
                var IDTipoAcesso = document.getElementById('IDTipoAcesso').value

                if (IDTipoAcesso == TIPO_ACESSO_CLIENTE_ADMIN) {
                    // habilita
                    //$(".div_gerenciamento *").prop('disabled', false);
                }
                //$('.div_gerenciamento').css("display", "block");
                //$('.div_gerenciamento_selecione').css("display", "none");

                // dataTable
                var table = $('#dataTables-medicoes').DataTable();

                // retira selecao atual
                table.$('tr.selected').removeClass('selected');

                // seleciona
                $(this).addClass('selected');

                // pega dados da linha selecionada
                var data = $('#dataTables-medicoes').DataTable().row(this).data();
                var id = data[0];
                var RelatDiario = data[6];
                var RelatSemanal = data[7];
                var RelatMensal = data[8];
                var RelatDemAtv = data[9];
                var RelatFatPot = data[10];
                var RelatConsumo = data[11];
                var RelatSupervisao = data[12];
                var RelatRanking = data[13];
                var MsgGeren = data[14];
                var MsgProcessa = data[15];

                if (RelatDiario == 'True')
                    $("#RelatDiario").iCheck("check");
                else
                    $("#RelatDiario").iCheck("uncheck");

                if (RelatSemanal == 'True')
                    $("#RelatSemanal").iCheck("check");
                else
                    $("#RelatSemanal").iCheck("uncheck");

                if (RelatMensal == 'True')
                    $("#RelatMensal").iCheck("check");
                else
                    $("#RelatMensal").iCheck("uncheck");

                if (RelatDemAtv == 'True')
                    $("#RelatDemAtv").iCheck("check");
                else
                    $("#RelatDemAtv").iCheck("uncheck");

                if (RelatFatPot == 'True')
                    $("#RelatFatPot").iCheck("check");
                else
                    $("#RelatFatPot").iCheck("uncheck");

                if (RelatConsumo == 'True')
                    $("#RelatConsumo").iCheck("check");
                else
                    $("#RelatConsumo").iCheck("uncheck");

                if (RelatSupervisao == 'True')
                    $("#RelatSupervisao").iCheck("check");
                else
                    $("#RelatSupervisao").iCheck("uncheck");

                if (RelatRanking == 'True')
                    $("#RelatRanking").iCheck("check");
                else
                    $("#RelatRanking").iCheck("uncheck");

                if (MsgGeren == 'True')
                    $("#MsgGeren").iCheck("check");
                else
                    $("#MsgGeren").iCheck("uncheck");

                if (MsgProcessa == 'True')
                    $("#MsgProcessa").iCheck("check");
                else
                    $("#MsgProcessa").iCheck("uncheck");
            }
        });

        //
        // CHECKBOX
        //

        function SetCell(item, valor) {

            // dataTable
            var table = $('#dataTables-medicoes').DataTable();
            var data = table.row('.selected').data();

            // verifica se tem selecionado
            if (data != undefined)
            {
                data[item] = valor;
                table.row('.selected').data(data).draw();
            }
        }

        $('#RelatDiario').on('ifChecked', function (event) { SetCell(6, 'True'); });
        $('#RelatDiario').on('ifUnchecked', function (event) { SetCell(6, 'False'); });

        $('#RelatSemanal').on('ifChecked', function (event) { SetCell(7, 'True'); });
        $('#RelatSemanal').on('ifUnchecked', function (event) { SetCell(7, 'False'); });

        $('#RelatMensal').on('ifChecked', function (event) { SetCell(8, 'True'); });
        $('#RelatMensal').on('ifUnchecked', function (event) { SetCell(8, 'False'); });

        $('#RelatDemAtv').on('ifChecked', function (event) { SetCell(9, 'True'); });
        $('#RelatDemAtv').on('ifUnchecked', function (event) { SetCell(9, 'False'); });

        $('#RelatFatPot').on('ifChecked', function (event) { SetCell(10, 'True'); });
        $('#RelatFatPot').on('ifUnchecked', function (event) { SetCell(10, 'False'); });

        $('#RelatConsumo').on('ifChecked', function (event) { SetCell(11, 'True'); });
        $('#RelatConsumo').on('ifUnchecked', function (event) { SetCell(11, 'False'); });

        $('#RelatSupervisao').on('ifChecked', function (event) { SetCell(12, 'True'); });
        $('#RelatSupervisao').on('ifUnchecked', function (event) { SetCell(12, 'False'); });

        $('#RelatRanking').on('ifChecked', function (event) { SetCell(13, 'True'); });
        $('#RelatRanking').on('ifUnchecked', function (event) { SetCell(13, 'False'); });

        $('#MsgGeren').on('ifChecked', function (event) { SetCell(14, 'True'); });
        $('#MsgGeren').on('ifUnchecked', function (event) { SetCell(14, 'False'); });

        $('#MsgProcessa').on('ifChecked', function (event) { SetCell(15, 'True'); });
        $('#MsgProcessa').on('ifUnchecked', function (event) { SetCell(15, 'False'); });

        //
        // TABELA MEDICOES (MODAL)
        //

        var tableMedicoes2 = $('#dataTables-medicoes2').DataTable({
            "scrollY": "400px",
            "scrollCollapse": true,
            "paging": false,
            "responsive": true,
            "bAutoWidth": true,
            "iDisplayLength": 1000,
            dom: 'ftp',
            'order': [2, 'asc'],
            "aoColumns": [
            { sWidth: "5%" },
            { sWidth: "10%" },
            { sWidth: "20%" },
            { sWidth: "20%" },
            { sWidth: "20%" },
            { sWidth: "25%" }
            ],

            'columnDefs': [{
                'targets': 0,
                'searchable': false,
                'orderable': false,
                'className': 'dt-center',
                'render': function (data, type, full, meta) {
                    return '<input type="checkbox" name="checkbox_modal_medicoes">';
                },
            },
            {
                'targets': 1,
                'visible': false,
                'searchable': false,
                'orderable': false,
            }],

            'rowCallback': function (row, data, dataIndex) {
                // Get row ID
                var rowId = data[0];

                // If row ID is in the list of selected row IDs
                if ($.inArray(rowId, rows_selected_medicoes) !== -1) {
                    $(row).find('input[type="checkbox"]').prop('checked', true);
                    $(row).addClass('selected');
                }
            },

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        // manipula checkbox
        $('#dataTables-medicoes2 tbody').on('click', 'input[type="checkbox"]', function (e) {
            var $row = $(this).closest('tr');

            // Get row data
            var data = tableMedicoes2.row($row).data();

            // Get row ID
            var rowId = data[0];
            var name_cli = data[2];
            var name_grupo = data[3];
            var name_unid = data[4];
            var name_med = data[5];

            // Determine whether row ID is in the list of selected row IDs
            var index = $.inArray(rowId, rows_selected_medicoes);

            // If checkbox is checked and row ID is not in list of selected row IDs
            if (this.checked && index === -1) {
                rows_selected_medicoes.push(rowId);
                names_selected_medicoes_cli.push(name_cli);
                names_selected_medicoes_grupo.push(name_grupo);
                names_selected_medicoes_unid.push(name_unid);
                names_selected_medicoes_med.push(name_med);

                names_selected_medicoes_relatdiario.push('False');
                names_selected_medicoes_relatsemanal.push('False');
                names_selected_medicoes_relatmensal.push('False');

                names_selected_medicoes_relatdematv.push('False');
                names_selected_medicoes_relatfatpot.push('False');
                names_selected_medicoes_relatconsumo.push('False');
                names_selected_medicoes_relatsupervisao.push('False');

                names_selected_medicoes_relatranking.push('False');

                names_selected_medicoes_msggeren.push('False');
                names_selected_medicoes_msgprocessa.push('False');

                // Otherwise, if checkbox is not checked and row ID is in list of selected row IDs
            } else if (!this.checked && index !== -1) {
                rows_selected_medicoes.splice(index, 1);
                names_selected_medicoes_cli.splice(index, 1);
                names_selected_medicoes_grupo.splice(index, 1);
                names_selected_medicoes_unid.splice(index, 1);
                names_selected_medicoes_med.splice(index, 1);

                names_selected_medicoes_relatdiario.splice(index, 1);
                names_selected_medicoes_relatsemanal.splice(index, 1);
                names_selected_medicoes_relatmensal.splice(index, 1);

                names_selected_medicoes_relatdematv.splice(index, 1);
                names_selected_medicoes_relatfatpot.splice(index, 1);
                names_selected_medicoes_relatconsumo.splice(index, 1);
                names_selected_medicoes_relatsupervisao.splice(index, 1);

                names_selected_medicoes_relatranking.splice(index, 1);

                names_selected_medicoes_msggeren.splice(index, 1);
                names_selected_medicoes_msgprocessa.splice(index, 1);
            }

            if (this.checked) {
                $row.addClass('selected');
            } else {
                $row.removeClass('selected');
            }

            // Update state of "Select all" control
            updateDataTableSelectAllCtrl(tableMedicoes2);

            // Prevent click event from propagating to parent
            e.stopPropagation();
        });

        // manipula click na tabela
        $('#dataTables-medicoes2').on('click', 'tbody td, thead th:first-child', function (e) {
            $(this).parent().find('input[type="checkbox"]').trigger('click');
        });

        // manipula click no "Select all"
        $('thead input[name="select_all"]', tableMedicoes2.table().container()).on('click', function (e) {
            if (this.checked) {
                $('tbody input[type="checkbox"]:not(:checked)', tableMedicoes2.table().container()).trigger('click');
            } else {
                $('tbody input[type="checkbox"]:checked', tableMedicoes2.table().container()).trigger('click');
            }

            // Prevent click event from propagating to parent
            e.stopPropagation();
        });

        // Handle table draw event
        tableMedicoes2.on('draw', function () {
            // Update state of "Select all" control
            updateDataTableSelectAllCtrl(tableMedicoes2);
        });

        $('form input').on('keypress', function (e) {
            return e.which !== 13;
        });

        // verifica se deve alertar motivo para mudar senha
        var mudar_senha = Math.ceil(@ViewBag.mudar_senha);

        // verifica se senha expirou
        if (mudar_senha == 1) {
            $('#ModalExpirou').modal('show');
        }

        // bem-vindo caso novo
        if (mudar_senha == 2) {
            $('#ModalBemvindo').modal('show');
        }
    });

    //
    // DESABILITA CAMPOS
    //

    function disableAll() {

        var IDTipoAcesso = document.getElementById('IDTipoAcesso').value

        // verifica se cliente - operador
        if (IDTipoAcesso == TIPO_ACESSO_CLIENTE_OPER)
        {
            $(".div_gerenciamento *").prop('disabled', true);
        }

        // verifica se demo
        if (IDTipoAcesso == TIPO_ACESSO_DEMONSTRACAO)
        {
            $("#BotaoSalvar").attr('disabled', true);
            $("#Senha").attr('disabled', true);
            $("#Confirmacao").attr('disabled', true);
            $("#NomeUsuario").attr('disabled', true);
            $("#Apelido").attr('disabled', true);
            $("#Email").attr('disabled', true);
            $("#Telefone").attr('disabled', true);
            $("#Ramal").attr('disabled', true);
            $("#Celular").attr('disabled', true);
            $("#Cargo").attr('disabled', true);
            $("#Departamento").attr('disabled', true);
            $("#IDIdioma").attr('disabled', true);
        }

        $("#IDTipoAcesso").attr('disabled', true);
    }

    //
    // BOTAO ADICIONA MEDICOES
    //

    function ModalMedicoes() {
        event.stopPropagation();

        // apresenta janela
        $('#ModalMedicoes').modal('show');

        $($.fn.dataTable.tables(true)).css('width', '100%');
        $($.fn.dataTable.tables(true)).DataTable().columns.adjust().draw();

    }

    function fnClickAddRowMedicoes() {

        // percorre lista de selecionados
        $.each(rows_selected_medicoes, function (index, rowId) {

            // procura na tabela se ja existe iD
            var table = $('#dataTables-medicoes').dataTable();
            row_count = table.fnGetData().length;
            var linhas = table.fnGetData();

            var achou = false;

            for (var j = 0; j < row_count; j++) {

                if (linhas[j][0] == rowId) {
                    achou = true;
                }
            }

            // insere na tabela se nao achou
            if (achou == false) {
                $('#dataTables-medicoes').dataTable().fnAddData([
                    rowId,
                    names_selected_medicoes_cli[index],
                    names_selected_medicoes_grupo[index],
                    names_selected_medicoes_unid[index],
                    names_selected_medicoes_med[index],
                    '<a href="#" class="confirm-delete-medicao link_preto"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>',
                    names_selected_medicoes_relatdiario[index],
                    names_selected_medicoes_relatsemanal[index],
                    names_selected_medicoes_relatmensal[index],
                    names_selected_medicoes_relatdematv[index],
                    names_selected_medicoes_relatfatpot[index],
                    names_selected_medicoes_relatconsumo[index],
                    names_selected_medicoes_relatsupervisao[index],
                    names_selected_medicoes_relatranking[index],
                    names_selected_medicoes_msggeren[index],
                    names_selected_medicoes_msgprocessa[index]]);
            }
        });

        // apaga arrays
        rows_selected_medicoes = [];
        names_selected_medicoes_cli = [];
        names_selected_medicoes_grupo = [];
        names_selected_medicoes_unid = [];
        names_selected_medicoes_med = [];

        names_selected_medicoes_relatdiario = [];
        names_selected_medicoes_relatsemanal = [];
        names_selected_medicoes_relatmensal = [];

        names_selected_medicoes_relatdematv = [];
        names_selected_medicoes_relatfatpot = [];
        names_selected_medicoes_relatconsumo = [];
        names_selected_medicoes_relatsupervisao = [];

        names_selected_medicoes_relatranking = [];

        names_selected_medicoes_msggeren = [];
        names_selected_medicoes_msgprocessa = [];

        // desmarca checkbox
        var boxes = document.getElementsByName("checkbox_modal_medicoes");
        for (var i = 0; i < boxes.length; i++)
            boxes[i].checked = false;

        var chkbox_select_all = $('thead input[name="select_all"]', $('#dataTables-medicoes2')).get(0);
        chkbox_select_all.indeterminate = false;
        chkbox_select_all.checked = false;


        $('#dataTables-medicoes').dataTable().draw();
    }

    //
    // BOTAO APAGAR MEDICAO
    //

    $('#dataTables-medicoes').on('click', '.confirm-delete-medicao', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // titulo
        titulo = "Deseja excluir a medição da lista?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            setTimeout(function () {

                // apaga linha solicitada
                $('#dataTables-medicoes').dataTable().fnDeleteRow(row);

            }, 100);

        });

    });

    function Excluir_Todos() {
        event.stopPropagation();

        // titulo
        titulo = "Deseja excluir todas as Medições?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            setTimeout(function () {

                // apaga todas
                var oSettings = $('#dataTables-medicoes').dataTable().fnSettings();
                var iTotalRecords = oSettings.fnRecordsTotal();
                for (i = 0; i <= iTotalRecords; i++) {
                    $('#dataTables-medicoes').dataTable().fnDeleteRow(0, null, true);
                }

            }, 100);

        });
    };

    //
    // BOTAO SALVAR (SUBMIT)
    //

    $.fn.serializeObject = function () {
        var o = {};
        var a = this.serializeArray();
        $.each(a, function () {
            if (o[this.name] !== undefined) {
                if (!o[this.name].push) {
                    o[this.name] = [o[this.name]];
                }
                o[this.name].push(this.value || '');
            } else {
                o[this.name] = this.value || '';
            }
        });
        return o;
    };

    function Salvar() {

        var $form = $('#form');

        // apresenta primeiro tab que tiver erro
        var IsTabValid = true;
        var TabInvalid = 1;

        $(".tab-content").find("div.tab-pane").each(function (index, tab) {
            var id = $(tab).attr("id");
            $('a[href="#' + id + '"]').tab('show');

            IsTabValid = $("#form").valid();

            if (!IsTabValid) {

                TabInvalid = id;
                return false;
            }
        });

        // apresenta primeira tab ou com erro
        $('a[href="#tab-' + TabInvalid + '"]').tab('show');

        // verifica se entradas sao validas
        if (!IsTabValid) return false;


        // lista de medicoes
        var oTable = $('#dataTables-medicoes').dataTable();
        var rows = oTable.fnSettings().aoData;
        var id = 0;
        var relatdiario;
        var relatsemanal;
        var relatmensal;
        var relatdematv;
        var relatfatpot;
        var relatconsumo;
        var relatsupervisao;
        var relatranking;
        var msggeren;
        var msgprocessa;
        var dataArray = [];

        // percorre tabela
        $.each(rows, function (i, val) {

            // pega ID
            id = parseInt(val._aData[0]);

            // pega flags
            if (val._aData[6] == 'True') relatdiario = 1; else relatdiario = 0;
            if (val._aData[7] == 'True') relatsemanal = 1; else relatsemanal = 0;
            if (val._aData[8] == 'True') relatmensal = 1; else relatmensal = 0;

            if (val._aData[9] == 'True') relatdematv = 1; else relatdematv = 0;
            if (val._aData[10] == 'True') relatfatpot = 1; else relatfatpot = 0;
            if (val._aData[11] == 'True') relatconsumo = 1; else relatconsumo = 0;
            if (val._aData[12] == 'True') relatsupervisao = 1; else relatsupervisao = 0;

            if (val._aData[13] == 'True') relatranking = 1; else relatranking = 0;

            if (val._aData[14] == 'True') msggeren = 1; else msggeren = 0;
            if (val._aData[15] == 'True') msgprocessa = 1; else msgprocessa = 0;

            dataArray.push({
                "IDMedicao": id,
                "RelatDiario": relatdiario,
                "RelatSemanal": relatsemanal,
                "RelatMensal": relatmensal,
                "RelatDemAtv": relatdematv,
                "RelatFatPot": relatfatpot,
                "RelatConsumo": relatconsumo,
                "RelatSupervisao": relatsupervisao,
                "RelatRanking": relatranking,
                "MsgGeren": msggeren,
                "MsgProcessa": msgprocessa,
            });

        });

        // checkbox relatorios e alertas
        document.getElementById('Diario').value = (document.getElementById('DiarioAux').checked == true) ? "1" : "0";
        document.getElementById('Semanal').value = (document.getElementById('SemanalAux').checked == true) ? "1" : "0";
        document.getElementById('Mensal').value = (document.getElementById('MensalAux').checked == true) ? "1" : "0";
        document.getElementById('Ranking').value = (document.getElementById('RankingAux').checked == true) ? "1" : "0";
        document.getElementById('Rateio').value = (document.getElementById('RateioAux').checked == true) ? "1" : "0";

        document.getElementById('DemAtv').value = (document.getElementById('DemAtvAux').checked == true) ? "1" : "0";
        document.getElementById('FatPot').value = (document.getElementById('FatPotAux').checked == true) ? "1" : "0";
        document.getElementById('Consumo').value = (document.getElementById('ConsumoAux').checked == true) ? "1" : "0";
        document.getElementById('Supervisao').value = (document.getElementById('SupervisaoAux').checked == true) ? "1" : "0";
        document.getElementById('Util_Anal_Ciclo').value = (document.getElementById('UtilAnalCicloAux').checked == true) ? "1" : "0";
        document.getElementById('Gestao_Contrato').value = (document.getElementById('GestaoContratoAux').checked == true) ? "1" : "0";
        document.getElementById('Gestao_Energia').value = (document.getElementById('GestaoEnergiaAux').checked == true) ? "1" : "0";

        document.getElementById('MsgCliMed_Dem').value = (document.getElementById('MsgCliMed_Dem_Aux').checked == true) ? "1" : "0";
        document.getElementById('MsgCliMed_Cons').value = (document.getElementById('MsgCliMed_Cons_Aux').checked == true) ? "1" : "0";
        document.getElementById('MsgCliMed_FatPot').value = (document.getElementById('MsgCliMed_FatPot_Aux').checked == true) ? "1" : "0";

        document.getElementById('MsgCliEve').value = (document.getElementById('MsgCliEveAux').checked == true) ? "1" : "0";

        // deseja salvar
        swal({
            title: "Deseja salvar?",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#1ab394",
            confirmButtonText: "Salvar",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {
            event.stopPropagation();

            // aguarde
            $(".overlay_aguarde").toggle();

            // retiro campos desabilitados, pois nao envia via POST
            $(":disabled", $('#form')).removeAttr("disabled");

            // constroi estrutura com FORM e lista de medicoes
            data = { 'usuario': $form.serializeObject(), 'usuarioMedicoes': dataArray };
            data2 = JSON.stringify(data);

            $.ajax({
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                url: '/UsuarioPerfil/Edit',
                data: data2,
                type: 'POST',
                success: function (data) {

                    // fim
                    $(".overlay_aguarde").toggle();

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {
                            swal({
                                title: "Erro",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            });
                        }
                        else {

                            // texto
                            var texto = "";

                            // verifica se mudou email
                            var Email = $('#Email').val();
                            var EmailAtual = $('#EmailAtual').val();

                            if (Email != EmailAtual) {

                                texto = "Foi enviado um link de confirmação para o seu email.";
                            }

                            swal({
                                title: "Salvo com sucesso",
                                text: texto,
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // redireciona para pagina perfil
                                var url = '/UsuarioPerfil/Edit';
                                window.location.href = url;
                            });
                        }

                    }, 100);

                },
                error: function (xhr, status, error) {

                    // fim
                    $(".overlay_aguarde").toggle();

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao salvar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });
    };

</script>
}
