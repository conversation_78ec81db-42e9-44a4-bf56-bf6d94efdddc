﻿@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoMedicoes;
}

<head>
    <title>Me<PERSON><PERSON><PERSON><PERSON></title>
    <style>

        .bar {
            height: 18px;
            background: #1AB394;
        }

        .quadro_mapa {
            padding: 2px;
            border: 1px solid #efefef;
        }

        .container {
            height: 88vh;
            width: 85vw;
        }

        .tooltip-inner {
            max-width: 350px;
            width: 350px;
            white-space: pre-wrap;
            text-align: left;
        }

        #iw-container {
            margin-bottom: 10px;
        }

        #iw-container .iw-title {
            font-family: 'Open Sans Condensed', sans-serif;
            font-size: 16px;
            font-weight: 400;
            padding: 10px 0px 10px 0px;
            background-color: #293846;
            color: white;
            margin-top: 5px;
            border-radius: 5px 5px 0 0;
        }

        .iw-title img {
            margin: 0 10px 0;
        }

        #iw-container .iw-content {
            font-size: 13px;
            line-height: 18px;
            font-weight: 400;
            margin-right: 1px;
            padding: 15px 0px 0px 15px;
            max-height: 140px;
            min-width: 400px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .iw-content table, td, tr {
            border: 0px solid black;
            padding: 5px 5px 5px 10px;
        }


        .iw-subTitle {
            font-size: 16px;
            font-weight: 700;
            padding: 5px 0;
        }

        .iw-bottom-gradient {
            position: absolute;
            width: 326px;
            height: 25px;
            bottom: 10px;
            right: 18px;
            background: linear-gradient(to bottom, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 100%);
            background: -webkit-linear-gradient(top, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 100%);
            background: -moz-linear-gradient(top, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 100%);
            background: -ms-linear-gradient(top, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 100%);
        }

        .icones {
            padding: 0px 5px;
            font-size: 18px;
            float: left;
        }

        a:link {
            color: #676A6C;
        }

        a:hover {
            color: #AFB0B1;
        }

        .link_preto a:link {
            color: #7E6A6C !important;
        }

        .link_preto a:visited {
            color: #7E6A6C !important;
        }

        .link_preto a:hover {
            color: #a0a0a0 !important;
        }

        .link_preto a:active {
            color: #7E6A6C !important;
        }




        [data-tooltip] {
            display: inline-block;
            position: relative;
            cursor: help;
            padding: 4px;
        }
            /* Tooltip styling */
            [data-tooltip]:before {
                content: attr(data-tooltip);
                display: none;
                position: absolute;
                background: #000;
                color: #fff;
                padding: 4px 8px;
                font-size: 14px;
                line-height: 1.4;
                min-width: 100px;
                text-align: center;
                border-radius: 4px;
            }
        /* Dynamic horizontal centering */
        [data-tooltip-position="top"]:before,
        [data-tooltip-position="bottom"]:before {
            left: 50%;
            -ms-transform: translateX(-50%);
            -moz-transform: translateX(-50%);
            -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
        }
        /* Dynamic vertical centering */
        [data-tooltip-position="right"]:before,
        [data-tooltip-position="left"]:before {
            top: 50%;
            -ms-transform: translateY(-50%);
            -moz-transform: translateY(-50%);
            -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
        }

        [data-tooltip-position="top"]:before {
            bottom: 100%;
            margin-bottom: 6px;
        }

        [data-tooltip-position="right"]:before {
            left: 100%;
            margin-left: 6px;
        }

        [data-tooltip-position="bottom"]:before {
            top: 100%;
            margin-top: 6px;
        }

        [data-tooltip-position="left"]:before {
            right: 100%;
            margin-right: 6px;
        }

        /* Tooltip arrow styling/placement */
        [data-tooltip]:after {
            content: '';
            display: none;
            position: absolute;
            width: 0;
            height: 0;
            border-color: transparent;
            border-style: solid;
        }
        /* Dynamic horizontal centering for the tooltip */
        [data-tooltip-position="top"]:after,
        [data-tooltip-position="bottom"]:after {
            left: 50%;
            margin-left: -6px;
        }
        /* Dynamic vertical centering for the tooltip */
        [data-tooltip-position="right"]:after,
        [data-tooltip-position="left"]:after {
            top: 50%;
            margin-top: -6px;
        }

        [data-tooltip-position="top"]:after {
            bottom: 100%;
            border-width: 6px 6px 0;
            border-top-color: #000;
        }

        [data-tooltip-position="right"]:after {
            left: 100%;
            border-width: 6px 6px 6px 0;
            border-right-color: #000;
        }

        [data-tooltip-position="bottom"]:after {
            top: 100%;
            border-width: 0 6px 6px;
            border-bottom-color: #000;
        }

        [data-tooltip-position="left"]:after {
            right: 100%;
            border-width: 6px 0 6px 6px;
            border-left-color: #000;
        }
        /* Show the tooltip when hovering */
        [data-tooltip]:hover:before,
        [data-tooltip]:hover:after {
            display:  block;
            z-index: 50;
        }

    </style>
</head>

@{
    int IDTipoAcesso = ViewBag._IDTipoAcesso;
    int IDConsultor = ViewBag._IDConsultor;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title container">
                <div class="panel-body">

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="quadro_mapa">
                                <div class="google-map" id="map" style="height:84vh;"></div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/simplerWeather")
    @Scripts.Render("~/plugins/skycons")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/declaracoes/Traducao-en.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "es-AR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
        <script type="text/javascript" language="javascript" src="~/Scripts/declaracoes/Traducao-es-AR.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
        <script type="text/javascript" language="javascript" src="~/Scripts/declaracoes/Traducao-pt-br.js"></script>
    }

    <!--
     API Google Maps
     key = AIzaSyDQTpXj82d8UpCi97wzo_nKXL7nYrd4G70
    -->
    @Scripts.Render("https://maps.googleapis.com/maps/api/js?key=AIzaSyA5X1xuge28QKLNI9glB7Ki2cYnqx5bqaM")


    <script type="text/javascript">

    // personaliza quando terminar de ler google maps
    google.maps.event.addDomListener(window, 'load', ShowMap);

    var map;
    var marker;

    let infoWindowAtual;

    function ShowMap() {

        // opções Google map
        // veja mais opções em: https://developers.google.com/maps/documentation/javascript/reference#MapOptions
        var mapOptions = {
            maxZoom: 17,
            mapTypeId: google.maps.MapTypeId.ROADMAP,
            styles: [{ "featureType": "poi", "stylers": [{"visibility": "off" }] }, { "featureType": "transit.station", elementType: "all", "stylers": [{ "visibility": "off" }] }]
        };

        // cria mapa
        var mapElement = document.getElementById('map');
        geocoder = new google.maps.Geocoder();
        map = new google.maps.Map(mapElement, mapOptions);

        // empresas
        var listaEmpresas = @Html.Raw(Json.Encode(ViewBag.listaEmpresas));
        var tamanho = @Html.Raw(Json.Encode(ViewBag.listaEmpresas_tamanho));

            // marcadores
            var markers = [];
            var bounds = new google.maps.LatLngBounds();

            // percorre empresas
            for (i=0; i<tamanho; i++)
            {
                // verifica se possui latitude e longitude
                var latitude = listaEmpresas[i].Latitude;
                var longitude = listaEmpresas[i].Longitude;
                var endereco_completo = listaEmpresas[i].EnderecoCompleto;

                if (latitude == 0.0 || longitude == 0.0)
                {
                    // encontra endereco
                    geocoder.geocode({ 'address': endereco_completo }, function (resultado, status) {

                        if (status == google.maps.GeocoderStatus.OK) {

                            // pega latitude e longitude
                            latitude = resultado[0].geometry.location.lat();
                            longitude = resultado[0].geometry.location.lng();

                        }
                        else {
                            //alert('Erro ao converter endereço: ' + status);
                        }

                    });
                }

                if (latitude != 0.0 && longitude != 0.0)
                {
                    // icone
                    var icon = {
                        url: "http://maps.google.com/mapfiles/ms/micons/green-dot.png",
                        scaledSize: new google.maps.Size(40, 40) 
                    };

                    if (listaEmpresas[i].Status > 0)
                    {
                        icon.url = "http://maps.google.com/mapfiles/ms/micons/red-dot.png";
                    }

                    // cria marcador
                    marker = new google.maps.Marker({ position: { lat: latitude, lng: longitude }, map: map, title: listaEmpresas[i].NomeEmpresa, icon: icon });
                    bounds.extend(marker.position);

                    // IDEmpresa
                    marker.setValues({type: "point", IDEmpresa: listaEmpresas[i].IDEmpresa});

                    // infowindow
                    var infowindow = new google.maps.InfoWindow();

                    google.maps.event.addListener(marker,'click', (function(marker,infowindow){
                        return function() {

                            // Verifique se já existe uma InfoWindow aberta
                            if (infoWindowAtual) {

                                // Feche a InfoWindow atualmente aberta
                                infoWindowAtual.close();

                            }

                            // IDEmpresa
                            var IDEmpresa = marker.IDEmpresa;

                            // le supervisão das medições da empresa
                            $.getJSON('/Supervisao/ObterSupervMedicoesEmpresa', { IDEmpresa: IDEmpresa }, function (data) {

                                // verifica se possui item
                                if (data.length > 0)
                                {
                                    // temperatura
                                    var unidade = '&deg;C';
                                    var temperatura = Math.round(data[0].Temp_Atual) + unidade;

                                    // verifica se não tem meteorologia
                                    if (data[0].Tempo_Cod < 0) {

                                        temperatura = "nao";
                                    }

                                    // formata InfoWindow
                                    FormataInfoWindow(infowindow, data, temperatura);

                                    // abre InfoWindow
                                    infowindow.open({
                                        anchor: marker,
                                        map,
                                    });

                                    // Atualize a variável global com a nova InfoWindow aberta
                                    infoWindowAtual = infowindow;

                                    // verifica se tem meteorologia
                                    if (data[0].Tempo_Cod >= 0) {

                                        setTimeout(function () {

                                            $(".bottom").tooltip({
                                                placement: "left"
                                            });

                                            // icones tempo
                                            var skycons = new Skycons({ "color": "#FFFFFF" });
                                            var icon = document.getElementById("icon_hoje_" + IDEmpresa);
                                            skycons.add(icon, ConverteIDSkyCon(data[0].Tempo_Cod));

                                            // inicia animacao
                                            skycons.play();

                                        }, 100);

                                    }
                                }

                            });

                        };
                    })(marker,infowindow));

                    // adiciona marcador
                    markers.push(marker);

                    // centraliza no marcador
                    map.setCenter(new google.maps.LatLng(latitude, longitude));
                }
            }

            // alinha mapa para apresentar todos os marcadores
            map.fitBounds(bounds);
        }

        function FormataInfoWindow(infowindow, data, temperatura) {

            // conteudo
            var content = '<div id="iw-container">' +
                            '<div class="iw-title">' +
                              '<table>' +
                                '<tr>' +
                                  '<td width="88%">' + '<img src="' + data[0].Logo + '">' + data[0].NomeEmpresa + '</td>';

            // verifica se tem tempo
            if (temperatura != "nao")
            {
                content += '<td width="5%" style="text-align: right;">' + temperatura + '</td>' +
                           '<td width="5%">' + '<canvas id="icon_hoje_' + data[0].IDEmpresa + '" width="40" height="40"></canvas>' + '</td>' +
                           '<td width="2%"></td>';
            }

            content +=  '</tr>' +
                        '</table>' +
                        '</div>';

            // verifica se possui medições
            if (data[0].IDMedicao > 0)
            {
                content +=  '<div class="iw-content">' +
                            '<table>';

                for (var i = 0; i < data.length; i++) {

                    content += '<tr>' +
                                 '<td width="1%" style="text-align: center;">' + '<img src="' + data[i].Icone + '">' + '</td>' +
                                 '<td width="49%">' + data[i].NomeMedicao + '</td>' +
                                 '<td width="25%" style="text-align: right;">' + data[i].UltimoValorStr + '</td>' +
                                 '<td width="25%" class="link_preto">' +
                                    '<a href="/Supervisao/' + data[i].CaminhoSuperv + '?IDCliente=' + data[i].IDCliente + '&IDMedicao=' + data[i].IDMedicao + '" title="Supervisão" tabindex="-1">' +
                                        '<i class="fa fa-desktop icones"></i>' +
                                    '</a>' +
                                    '<a href="/Relatorios/' + data[i].CaminhoRelat + '?IDCliente=' + data[i].IDCliente + '&IDMedicao=' + data[i].IDMedicao + '" title="Relatórios" tabindex="-1">' +
                                        '<i class="fa fa-bar-chart icones"></i>' +
                                    '</a>' +
                                    '<span style="float: left;" class="bottom" data-toggle="tooltip" data-html="true" data-placement="left" title="' + data[i].ModeloEq + ' (' + data[i].VersaoEq + ')<br>' + data[i].IP + '<br>Sinal: ' + data[i].Sinal + '%"><i class="fa fa-info-circle icones"></i></span>';

                    // verifica se tem alarme
                    if (data[i].Status > 0)
                    {
                        content += '<span style="color: red; float: left;" class="bottom" data-toggle="tooltip" data-html="true" data-placement="left" title="' + data[i].Status_Texto + '" style="color:red;"><i class="fa fa-exclamation-triangle icones"></i></span>';
                    }

                    content +=   '</td>' +
                               '</tr>';
                }

                content +=  '</table>' +
                            '<div class="iw-bottom-gradient"></div>';

            }
            else
            {
                content +=  '<div class="iw-content">' +
                            '<div class="iw-subTitle">Não existem medições.</div>' +
                            '</div>';
            }

            content += '</div>';

            // atualiza infowindow
            infowindow.setContent(content);
        }

        function ConverteIDSkyCon(Tempo_Cod_Icone) {

            var tempo = Skycons.CLEAR_DAY;

            switch (Tempo_Cod_Icone) {
                case 0:     // 0 = clear-day
                    tempo = Skycons.CLEAR_DAY;
                    break;

                case 1:     // 1 = clear-night
                    tempo = Skycons.CLEAR_NIGHT;
                    break;

                case 2:     // 2 = partly-cloudy-day
                    tempo = Skycons.PARTLY_CLOUDY_DAY;
                    break;

                case 3:     // 3 = partly-cloudy-night
                    tempo = Skycons.PARTLY_CLOUDY_NIGHT;
                    break;

                case 4:     // 4 = cloudy
                    tempo = Skycons.CLOUDY;
                    break;

                case 5:     // 5 = rain
                    tempo = Skycons.RAIN;
                    break;

                case 6:     // 6 = sleet (granizo)
                    tempo = Skycons.SLEET;
                    break;

                case 7:     // 7 = snow
                    tempo = Skycons.SNOW;
                    break;

                case 8:     // 8 = wind
                    tempo = Skycons.WIND;
                    break;

                case 9:     // 9 = fog
                    tempo = Skycons.FOG;
                    break;
            }

            return (tempo);
        }

        function ConverteTextoSkyCon(Tempo_Cod_Icone) {

            var tempo_texto = "Indefinido";

            switch (Tempo_Cod_Icone[i]) {
                case 0:     // 0 = clear-day
                    tempo_texto = CEU_CLARO;
                    break;

                case 1:     // 1 = clear-night
                    tempo_texto = NOITE_CLARA;
                    break;

                case 2:     // 2 = partly-cloudy-day
                    tempo_texto = PARC_NUBLADO;
                    break;

                case 3:     // 3 = partly-cloudy-night
                    tempo_texto = PARC_NUBLADO;
                    break;

                case 4:     // 4 = cloudy
                    tempo_texto = NUBLADO;
                    break;

                case 5:     // 5 = rain
                    tempo_texto = CHUVOSO;
                    tempo = Skycons.RAIN;
                    break;

                case 6:     // 6 = sleet (granizo)
                    tempo_texto = GRANIZO;
                    break;

                case 7:     // 7 = snow
                    tempo_texto = NEVE;
                    break;

                case 8:     // 8 = wind
                    tempo_texto = VENTO;
                    break;

                case 9:     // 9 = fog
                    tempo_texto = NEVOEIRO;
                    break;
            }

            return (tempo_texto);
        }

    </script>
}


