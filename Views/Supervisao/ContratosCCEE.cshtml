﻿@model IEnumerable<SmartEnergyLib.SQL.ContratosCCEEDominio>

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralInicio;
}

<style>

    .tooltip-inner {
        max-width: 350px;
        /* If max-width does not work, try using width instead */
        width: 350px;
        white-space: pre-wrap;
        text-align: left;
    }

</style>

<div class="wrapper wrapper-content">

    <div class="row">
        <div class="col-lg-3">
            <a href="#">
                <div class="widget style1 navy-bg">
                    <div class="row">
                        <div class="col-xs-3">
                            <i class="fa fa-file-text-o fa-5x"></i>
                        </div>
                        <div class="col-xs-9 text-right">
                            <span> Contratos Vigentes </span>
                            <h2 class="font-bold">@ViewBag.NumContratosVigentes</h2>
                        </div>
                    </div>
                </div>
            </a>
        </div>
        <div class="col-lg-3">
            <a href="#">
                <div class="widget style1 navy-bg">
                    <div class="row">
                        <div class="col-xs-3">
                            <i class="fa fa-th-large fa-5x"></i>
                        </div>
                        <div class="col-xs-9 text-right">
                            <span> Número de Clientes </span>
                            <h2 class="font-bold">@ViewBag.NumClientes</h2>
                        </div>
                    </div>
                </div>
            </a>
        </div>
        <div class="col-lg-3">
            <div class="widget style1 navy-bg">
                <div class="row">
                    <div class="col-xs-3">
                        <i class="fa fa-download fa-5x"></i>
                    </div>
                    <div class="col-xs-9 text-right">
                        <span> Número de Gateways </span>
                        <h2 class="font-bold">@ViewBag.NumGateways</h2>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="widget style1 navy-bg">
                <div class="row">
                    <div class="col-xs-3">
                        <i class="fa fa-dashboard fa-5x"></i>
                    </div>
                    <div class="col-xs-9 text-right">
                        <span> Número de Medições </span>
                        <h2 class="font-bold">@ViewBag.NumMedicoes</h2>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <br />

    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">

                    <table class="table table-striped table-bordered table-hover dataTables-contratos">
                        <thead>
                            <tr>
                                <th></th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.SiglaCCEE</th>
                                <th>@SmartEnergy.Resources.ContratosCCEETexts.ContratoCodigo</th>
                                <th>@SmartEnergy.Resources.ConfiguracaoTexts.ContratoStatus</th>
                                <th>@SmartEnergy.Resources.ContratosCCEETexts.VigenciaInicio</th>
                                <th>@SmartEnergy.Resources.ContratosCCEETexts.VigenciaFim</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>

                            @foreach (ContratosCCEEDominio contrato in Model)
                            {
                                string logo = "http://www.smartenergy.com.br/";

                                if (contrato.Logo.IsEmpty())
                                {
                                    logo += "/Logos/LogoSmartEnergy.png";
                                }
                                else
                                {
                                    logo += "/Logos/" + contrato.Logo;
                                }

                                // status do contrato
                                var Contrato_Status = "VIGENTE";

                                if (contrato.Contrato_Status == 2)
                                {
                                    Contrato_Status = "RESCINDIDO";
                                }

                                // vigencia
                                var Vigencia_Inicio = String.Format("{0:d}", contrato.Vigencia_Inicio);
                                var Vigencia_Inicio_Sort = String.Format("{0:yyyyMMddHHmmss}", contrato.Vigencia_Inicio);
                                var Vigencia_Fim = String.Format("{0:d}", contrato.Vigencia_Fim);
                                var Vigencia_Fim_Sort = String.Format("{0:yyyyMMddHHmmss}", contrato.Vigencia_Fim);

                                <tr>
                                    <td bgcolor="#293846" align="center"><img src=@logo title="@contrato.NomeCliente" style="max-height:45px; height: auto;" /></td>
                                    <td><b>@contrato.SiglaCCEE</b><br />@contrato.RazaoSocial</td>
                                    <td>@contrato.Contrato_Codigo</td>
                                    <td>@Contrato_Status</td>
                                    <td><span style="display:none;">@Vigencia_Inicio_Sort</span>@Vigencia_Inicio</td>
                                    <td><span style="display:none;">@Vigencia_Fim_Sort</span>@Vigencia_Fim</td>

                                    <td class="link_preto">
                                        <a href='@("/ContratosCCEE/ContratoCCEE_Editar?IDContratoCCEE=" + @contrato.IDContratoCCEE.ToString())' title="@SmartEnergy.Resources.ComumTexts.BotaoEditar"><i class="fa fa-edit icones"></i></a>

                                        @{
                                            string observacao = "Contrato sem observação";
                                            
                                            if (contrato.Observacao != "")
                                            {
                                                observacao = contrato.Observacao;
                                            }
                                        }
                                        <span title="@observacao"><i class="fa fa-info-circle icones"></i></span>
                                    </td>
                                </tr>
                            }

                        </tbody>
                    </table>



                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">
    $(document).ready(function () {

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {
                var x = (a == "-") ? 0 : a.replace(/,/, ".");
                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-contratos').DataTable({
            "iDisplayLength": 12,
            dom: 'ftp',

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "portugues", "bSortable": false, "bSearchable": false },
                        { "aTargets": [1], "sType": "portugues" },
                        { "aTargets": [2], "sType": "portugues" },
                        { "aTargets": [3], "sType": "portugues" },
                        { "aTargets": [4], "sType": "portugues" },
                        { "aTargets": [5], "sType": "portugues" },
                        { "aTargets": [6], "bVisible": true, "bSortable": false, "bSearchable": false },
            ],
            "aoColumns": [
            { sWidth: "10%" },
            { sWidth: "25%" },
            { sWidth: "15%" },
            { sWidth: "13%" },
            { sWidth: "13%" },
            { sWidth: "13%" },
            { sWidth: "10%" }
            ],
            'order': [1, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

            });
        });

        function Excluir(IDContratoCCEE) {
            event.stopPropagation();

            // titulo
            titulo = "Deseja excluir o contrato?<br/>ID " + IDContratoCCEE;

            swal({
                html: true,
                title: titulo,
                text: "Esta operação não poderá ser desfeita.",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#18a689",
                confirmButtonText: "Excluir",
                cancelButtonText: "Cancelar",
                closeOnConfirm: false
            }, function () {

                // excluir
                $.ajax(
                {
                    type: 'GET',
                    url: '/ContratosCCEE/ContratoCCEE_Excluir',
                    data: { 'IDContratoCCEE': IDContratoCCEE },
                    contentType: 'application/json; charset=utf-8',
                    dataType: 'json',
                    cache: false,
                    async: true,
                    success: function (data) {

                        setTimeout(function () {

                            // verifica se erro
                            if (data.status == "ERRO") {
                                swal({
                                    html: true,
                                    title: "Erro",
                                    text: data.erro,
                                    type: "warning",
                                    confirmButtonColor: "#f8ac59",
                                    confirmButtonText: "Fechar",
                                }, function () {

                                });
                            }
                            else {
                                swal({
                                    title: "Excluído com sucesso",
                                    type: "success",
                                    confirmButtonColor: "#18a689",
                                    confirmButtonText: "Fechar",
                                }, function () {

                                    // atualiza pagina
                                    location.reload();
                                });
                            }

                        }, 100);
                    },
                    error: function (response) {

                        setTimeout(function () {

                            swal({
                                title: "Erro",
                                text: "Erro ao excluir!",
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            });

                        }, 100);

                    }
                });
            });
        };

        $(".bottom").tooltip({
            placement: "bottom"
        });

    </script>
}


