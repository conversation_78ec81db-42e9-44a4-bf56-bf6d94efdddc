﻿@using System.Globalization

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoMedicao;
}

<head>

    <style>
        .superv-preto {
            color: #000000 !important;
        }

    </style>
</head>

<div class="row wrapper border-bottom white-bg page-heading">
    <div class="row">
        <div class="col-lg-12">
            <div class="superv-content">

                <div class="col-lg-5 col-md-12">
                    <div class="row">
                        <div class="col-lg-6 col-md-6">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual superv-fponta">
                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.ConsumoProjMes</h4>
                                    <h1>@ViewBag.Cons_Proj_T <span>kWh</span></h1>
                                    <h6>@ViewBag.MesAtual</h6>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual superv-util">
                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.Meta</h4>
                                    <h1>@ViewBag.Meta_Mes_T <span>kWh</span></h1>
                                    <h6>@ViewBag.MesAtual</h6>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-6 col-md-6">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual superv-fponta">
                                    <h4>@SmartEnergy.Resources.DashboardTexts.ConsumoTotalDia</h4>
                                    <h1>@ViewBag.Cons_Dia_T <span>kWh</span></h1>
                                    <h6>@ViewBag.DiaAtual</h6>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual superv-util">
                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.Meta</h4>
                                    <h1>@ViewBag.Meta_Dia_T <span>kWh</span></h1>
                                    <h6>@ViewBag.DiaAtual</h6>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual">

                                    @{
                                        if (ViewBag.MetaNormal)
                                        {
                                            <h5 class="superv-fponta" style="padding-top:10px;"><i class="fa fa-check-square-o" style="font-size:20px;"></i><span style="font-weight: bold; padding-left:8px;">@ViewBag.Texto1</span></h5>
                                            <h5 class="superv-fponta" style="padding-top:10px;"><i class="fa fa-check-square-o" style="font-size:20px;"></i><span style="font-weight: bold; padding-left:8px;">@ViewBag.Texto2</span></h5>
                                        }
                                        else
                                        {
                                            <h5 class="superv-ponta" style="padding-top:10px;"><blink><i class="fa fa-info-circle" style="font-size:20px;"></i></blink><span style="font-weight: bold; padding-left:8px;">@ViewBag.Texto1</span></h5>
                                            <h5 class="superv-ponta" style="padding-top:10px;"><blink><i class="fa fa-info-circle" style="font-size:20px;"></i></blink><span style="font-weight: bold; padding-left:8px;">@ViewBag.Texto2</span></h5>
                                        }
                                    }

                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <div class="col-lg-7 col-md-12">
                    <div class="row">
                        <div class="superv-grafico">
                            <div id="grafico-consumo" style="margin-top: -10px"></div>
                            <div class='chart-legend' style="display:none;">
                                <div class='legend-scale-dem'>
                                    <ul class='legend-labels' style="margin-top: -25px;">
                                        <li><span style='background:#007f00;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta</li>
                                        <li><span style='background:#ff0000;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



            </div>
        </div>
    </div>
</div>

<div class="wrapper wrapper-content">

    <div class="row">
        <div class="col-lg-4">
            <div class="panel panel-title">
                <div class="panel-body">
                    <div class="superv-tabela">
                        <table class="table no-margins" style="width:100%;">
                            <thead style="font-size: 11px;">
                                <tr>
                                    <th style="width:35%;">@SmartEnergy.Resources.SupervisaoTexts.Dia (@ViewBag.DiaAtual)</th>
                                    <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Consumo</th>
                                    <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Meta</th>
                                    <th style="width:15%;">%</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="superv-ponta">@SmartEnergy.Resources.SupervisaoTexts.Ponta</td>
                                    <td class="superv-ponta">@ViewBag.Cons_Dia_P kWh</td>
                                    <td class="superv-ponta">@ViewBag.Meta_Dia_P kWh</td>
                                    <td class="superv-ponta">@ViewBag.Porc_Dia_P %</td>
                                </tr>
                                <tr>
                                    <td class="superv-fponta">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta</td>
                                    <td class="superv-fponta">@ViewBag.Cons_Dia_FP kWh</td>
                                    <td class="superv-fponta">@ViewBag.Meta_Dia_FP kWh</td>
                                    <td class="superv-fponta">@ViewBag.Porc_Dia_FP %</td>
                                </tr>
                                <tr>
                                    <td class="superv-preto">@SmartEnergy.Resources.SupervisaoTexts.Total</td>
                                    <td class="superv-preto">@ViewBag.Cons_Dia_T kWh</td>
                                    <td class="superv-preto">@ViewBag.Meta_Dia_T kWh</td>
                                    <td class="superv-preto">@ViewBag.Porc_Dia_T %</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="panel panel-title">
                <div class="panel-body">
                    <div class="superv-tabela">
                        <table class="table no-margins" style="width:100%;">
                            <thead style="font-size: 11px;">
                                <tr>
                                    <th style="width:35%;">@SmartEnergy.Resources.SupervisaoTexts.Acumulado</th>
                                    <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Consumo</th>
                                    <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Meta</th>
                                    <th style="width:15%;">%</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="superv-ponta">@SmartEnergy.Resources.SupervisaoTexts.Ponta</td>
                                    <td class="superv-ponta">@ViewBag.Cons_Mes_P kWh</td>
                                    <td class="superv-ponta">@ViewBag.Meta_Mes_P kWh</td>
                                    <td class="superv-ponta">@ViewBag.Porc_Mes_P %</td>
                                </tr>
                                <tr>
                                    <td class="superv-fponta">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta</td>
                                    <td class="superv-fponta">@ViewBag.Cons_Mes_FP kWh</td>
                                    <td class="superv-fponta">@ViewBag.Meta_Mes_FP kWh</td>
                                    <td class="superv-fponta">@ViewBag.Porc_Mes_FP %</td>
                                </tr>
                                <tr>
                                    <td class="superv-preto">@SmartEnergy.Resources.SupervisaoTexts.Total</td>
                                    <td class="superv-preto">@ViewBag.Cons_Mes_T kWh</td>
                                    <td class="superv-preto">@ViewBag.Meta_Mes_T kWh</td>
                                    <td class="superv-preto">@ViewBag.Porc_Mes_T %</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="panel panel-title">
                <div class="panel-body">
                    <div class="superv-tabela">
                        <table class="table no-margins" style="width:100%;">
                            <thead style="font-size: 11px;">
                                <tr>
                                    <th style="width:35%;">@SmartEnergy.Resources.SupervisaoTexts.Projetado</th>
                                    <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Consumo</th>
                                    <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Meta</th>
                                    <th style="width:15%;">%</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="superv-ponta">@SmartEnergy.Resources.SupervisaoTexts.Ponta</td>
                                    <td class="superv-ponta">@ViewBag.Cons_Proj_P kWh</td>
                                    <td class="superv-ponta">@ViewBag.Meta_Mes_P kWh</td>
                                    <td class="superv-ponta">@ViewBag.Porc_Proj_P %</td>
                                </tr>
                                <tr>
                                    <td class="superv-fponta">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta</td>
                                    <td class="superv-fponta">@ViewBag.Cons_Proj_FP kWh</td>
                                    <td class="superv-fponta">@ViewBag.Meta_Mes_FP kWh</td>
                                    <td class="superv-fponta">@ViewBag.Porc_Proj_FP %</td>
                                </tr>
                                <tr>
                                    <td class="superv-preto">@SmartEnergy.Resources.SupervisaoTexts.Total</td>
                                    <td class="superv-preto">@ViewBag.Cons_Proj_T kWh</td>
                                    <td class="superv-preto">@ViewBag.Meta_Mes_T kWh</td>
                                    <td class="superv-preto">@ViewBag.Porc_Proj_T %</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">

                    <div class="col-lg-5 col-md-12">
                        <div class="row">
                            <div class="col-lg-6 col-md-6">
                                <div class="superv-coluna">
                                    <div class="superv-valorAtual superv-ponta">
                                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                                        <h5>@SmartEnergy.Resources.RelatoriosTexts.DemandaMaximaMes</h5>
                                        <h1>@ViewBag.Dem_Mes_DemMaxP <span>kW</span></h1>
                                        <h6>@ViewBag.Dem_Mes_DemMaxP_DataHora</h6>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-md-6">
                                <div class="superv-coluna">
                                    <div class="superv-valorAtual superv-fponta">
                                        <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta</h4>
                                        <h5>@SmartEnergy.Resources.RelatoriosTexts.DemandaMaximaMes</h5>
                                        <h1>@ViewBag.Dem_Mes_DemMaxFP <span>kW</span></h1>
                                        <h6>@ViewBag.Dem_Mes_DemMaxFP_DataHora</h6>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-12">
                                <div class="superv-tabela2">
                                    <table class="table no-margins" style="width:100%;">
                                        <thead style="font-size: 11px;">
                                            <tr>
                                                <th style="width:34%;"></th>
                                                <th class="superv-ponta" style="width:33%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                                                <th class="superv-fponta" style="width:33%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>@SmartEnergy.Resources.SupervisaoTexts.Dia<br /><small>(@ViewBag.DiaAtual)</small></td>
                                                <td class="superv-ponta">@ViewBag.Dem_Dia_DemMaxP kW<br /><small>(@ViewBag.Dem_Dia_DemMaxP_DataHora)</small></td>
                                                <td class="superv-fponta">@ViewBag.Dem_Dia_DemMaxFP kW<br /><small>(@ViewBag.Dem_Dia_DemMaxFP_DataHora)</small></td>
                                            </tr>
                                            <tr>
                                                <td>@SmartEnergy.Resources.SupervisaoTexts.Contrato</td>
                                                <td class="superv-ponta">@string.Format("{0:#,##0.0}", @ViewBag.Dem_ContratoP) kW (+@ViewBag.Tol_ContratoP %)</td>
                                                <td class="superv-fponta">@string.Format("{0:#,##0.0}", @ViewBag.Dem_ContratoFP) kW (+@ViewBag.Tol_ContratoFP %)</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-12">
                                <div class="superv-coluna">
                                    <div class="superv-valorAtual">

                                        @{
                                            if (ViewBag.Dem_Mes_UltrapassagemP > 0)
                                            {
                                                <h5 class="superv-ponta"><blink><i class="fa fa-line-chart" style="font-size:20px;"></i></blink><span style="font-weight: bold; padding-left:8px;">Ultrapassagem de Demanda na Ponta.</span></h5>
                                            }

                                            if (ViewBag.Dem_Mes_UltrapassagemFP > 0)
                                            {
                                                <h5 class="superv-ponta" style="padding-top:10px;"><blink><i class="fa fa-line-chart" style="font-size:20px;"></i></blink><span style="font-weight: bold; padding-left:8px;">Ultrapassagem de Demanda Fora de Ponta.</span></h5>
                                            }
                                        }

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-7 col-md-12">
                        <div class="row">
                            <div class="superv-grafico">
                                <div class="flot-chart">
                                    <div id="demanda-chart"></div>
                                </div>
                                <div class='chart-legend'>
                                    <div class='legend-scale-dem'>
                                        <ul class='legend-labels'>
                                            <li><span style='background:#1C84C6;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                                            <li><span style='background:#007f00;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                                            <li><span style='background:#ff0000;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-2">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h4>Gateway</h4><br />

                    @{
                        int IDTipoAcesso = ViewBag._IDTipoAcesso;

                        if (IDTipoAcesso == 0 || IDTipoAcesso == 5)
                        {
                            <span>[@ViewBag._IDGateway] </span>
                        }
                    }
                    @ViewBag.GatewayNome
                </div>
            </div>
        </div>
        <div class="col-lg-2">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.SupervisaoTexts.ModeloVersao</h4><br />
                    @ViewBag.GatewayModelo
                </div>
            </div>
        </div>
        <div class="col-lg-2">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h4>Status</h4><br />
                    @ViewBag.GatewayStatus
                </div>
            </div>
        </div>
        <div class="col-lg-2">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.SupervisaoTexts.UltimaAtualizacao</h4><br />
                    @ViewBag.GatewayAtualizacao
                </div>
            </div>
        </div>
        <div class="col-lg-2">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.SupervisaoTexts.DataHoraEq</h4><br />
                    @ViewBag.GatewayDataEq
                </div>
            </div>
        </div>
        <div class="col-lg-2">
            <div class="panel panel-success">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.SupervisaoTexts.Constante</h4><br />
                    @ViewBag.Constante
                </div>
            </div>
        </div>    
    </div>

    @if( @ViewBag.listaErros.Count > 0 )
    {
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-danger">
                    <div class="panel-heading">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Erros</h4><br />
                        <ul style="list-style-type:disc; margin-left: 20px;">
                            @foreach (var erro in @ViewBag.listaErros)
                            {
                                <li>@erro</li>
                            }
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    }
    
</div>

@section Styles {
    @Styles.Render("~/plugins/c3Styles")
    @Styles.Render("~/plugins/select2Styles")
    @Styles.Render("~/Supervisao.css")
}

@section Scripts {
    @Scripts.Render("~/plugins/d3")
    @Scripts.Render("~/plugins/c3")
    @Scripts.Render("~/plugins/select2")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">
    $(document).ready(function () {

        // demanda
        var dataX = [];
        var labelX = [];
        var dataContratoP = [];
        var dataContratoFPI = [];
        var dataContratoFPC = [];
        var dataToleranciaP = [];
        var dataToleranciaFPI = [];
        var dataToleranciaFPC = [];
        var dataDemanda = [];

        // copia valores
        var Demanda = @Html.Raw(Json.Encode(@ViewBag.Demanda));
        var Contrato = @Html.Raw(Json.Encode(@ViewBag.Contrato));
        var Tolerancia = @Html.Raw(Json.Encode(@ViewBag.Tolerancia));
        var Periodo = @Html.Raw(Json.Encode(@ViewBag.Periodo));
        var Dias_Demanda = @Html.Raw(Json.Encode(@ViewBag.Dias_Demanda));

        var maximoDem = @Html.Raw(Json.Encode(@ViewBag.DemMax));

        dataX.push('x');
        dataDemanda.push(['data1']);
        dataContratoFPC.push(['data2']);
        dataContratoFPI.push(['data3']);
        dataContratoP.push(['data4']);
        dataToleranciaFPC.push(['data5']);
        dataToleranciaFPI.push(['data6']);
        dataToleranciaP.push(['data7']);

        for (i = 0; i < 98; i++)
        {
            // caso nao tenha contrato, maximo vira contrato para termos indicacao de periodo
            if( Contrato[i] == 0 && Periodo[i] != 3 )
            {
                Contrato[i] = maximoDem;
            }

            // X
            dataX.push(Dias_Demanda[i]);

            // demanda
            dataDemanda.push([Demanda[i]]);

            if( Periodo[i] == 0 )
            {
                dataContratoFPC.push([0]);
                dataContratoFPI.push([0]);
                dataContratoP.push([Contrato[i]]);

                dataToleranciaFPC.push([0]);
                dataToleranciaFPI.push([0]);
                dataToleranciaP.push([Tolerancia[i]]);
            }
            else if( Periodo[i] == 1 )
            {
                dataContratoFPC.push([0]);
                dataContratoFPI.push([Contrato[i]]);
                dataContratoP.push([0]);

                dataToleranciaFPC.push([0]);
                dataToleranciaFPI.push([Tolerancia[i]]);
                dataToleranciaP.push([0]);
            }
            else if( Periodo[i] == 2 )
            {
                dataContratoFPC.push([Contrato[i]]);
                dataContratoFPI.push([0]);
                dataContratoP.push([0]);

                dataToleranciaFPC.push([Tolerancia[i]]);
                dataToleranciaFPI.push([0]);
                dataToleranciaP.push([0]);
            }
            else
            {
                dataContratoFPC.push([0]);
                dataContratoFPI.push([0]);
                dataContratoP.push([0]);

                dataToleranciaFPC.push([0]);
                dataToleranciaFPI.push([0]);
                dataToleranciaP.push([0]);
            }
        }

        // valores label X
        labelX.push(Dias_Demanda[1]);
        for (i = 12; i <= 96; i+=12)
        {
            labelX.push(Dias_Demanda[i]);
        }

        var Data = [dataX,dataDemanda,dataContratoFPC,dataContratoFPI,dataContratoP,dataToleranciaFPC,dataToleranciaFPI,dataToleranciaP];

        c3.generate({

            bindto: '#demanda-chart',
            size: {
                height: 300
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'area-step',
                    data2: 'area-step',
                    data3: 'area-step',
                    data4: 'area-step',
                    data5: 'area-step',
                    data6: 'area-step',
                    data7: 'area-step'
                },
                color: function (color, d) {
                    if (typeof d.index === 'undefined') { return color; }

                    if( Periodo[d.index] == 0 )
                        return '#FF0000';

                    if( Periodo[d.index] == 1 )
                        return '#007F00';

                    if( Periodo[d.index] == 2 )
                        return '#1C84C6';

                    return '#007F00';
                },
                colors: {
                    data1: '#001F00',
                    data2: '#1C84C6',
                    data3: '#007F00',
                    data4: '#FF0000',
                    data5: '#1C84C6',
                    data6: '#007F00',
                    data7: '#FF0000'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*6
                },
                y:  {
                    max: maximoDem,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( maximoDem < 100)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.15 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < 4; i++) {

                        if (! text) {

                            // split string and create array.
                            var arr = Dias_Demanda[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // horas e minutos
                            var hours = data.getHours();
                            var minutes = data.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            title = hours + ':' + minutes;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // periodo
                        switch(Periodo[d[0].index])
                        {
                            case 0:
                                periodo = "Ponta";
                                bgcolor = '#FF0000'
                                break;

                            case 1:
                                periodo = "FPonta Ind";
                                bgcolor = '#007F00';
                                break;

                            case 2:
                                periodo = "FPonta Cap";
                                bgcolor = '#1C84C6';
                                break;

                            default:
                                periodo = "---";
                                bgcolor = '#000000';
                                break;
                        }

                        // nome
                        switch(i)
                        {
                            case 0:
                                name = 'Demanda Ativa';
                                unit = 'kW';
                                value = Demanda[d[0].index].toFixed(1);
                                break;

                            case 1:
                                name = 'Contrato';
                                unit = 'kW';
                                value = Contrato[d[1].index].toFixed(1);
                                break;

                            case 2:
                                name = 'Tolerância';
                                unit = 'kW';
                                value = Tolerancia[d[4].index].toFixed(1);
                                break;

                            case 3:
                                name = 'Periodo';
                                unit = '';
                                value = periodo;
                                break;
                        }

                        value = value.replace('.', ',');

                        // verifica se sem registro
                        if( Periodo[d[0].index] == 3 )
                        {
                            value = '---';
                        }

                        if(i==3)
                        {
                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'>" + name + "</td>";
                            text += "<td class='value'>" + value + "</td>";
                            text += "</tr>";
                        }
                        else
                        {
                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                            text += "<td class='value'>" + value + " " + unit + "</td>";
                            text += "</tr>";
                        }
                    }

                    return text + "</table>";
                }
            }
        });


        // consumo
        var dataX = [];
        var labelX = [];
        var dataMeta = [];
        var dataConsumoFPC = [];
        var dataConsumoFPI = [];
        var dataConsumoP = [];

        // copia valores
        var ConsumoFPC = @Html.Raw(Json.Encode(@ViewBag.ConsumoFPC));
        var ConsumoFPI = @Html.Raw(Json.Encode(@ViewBag.ConsumoFPI));
        var ConsumoP = @Html.Raw(Json.Encode(@ViewBag.ConsumoP));
        var Meta = @Html.Raw(Json.Encode(@ViewBag.Meta));
        var Datas = @Html.Raw(Json.Encode(@ViewBag.Datas));
        var NumDiasMes = @Html.Raw(Json.Encode(@ViewBag.NumDiasMes));
        var Unidade = @Html.Raw(Json.Encode(@ViewBag.UnidadeConsumo));

        var maximoCons = Math.ceil(@ViewBag.ConsMaxGrafico);

        // verifico se nao tem dados e forco um limite
        if( maximoCons == 0.0)
        {
            maximoCons = 100.0;
        }

        dataX.push('x');
        dataConsumoFPC.push(['data1']);
        dataConsumoFPI.push(['data2']);
        dataConsumoP.push(['data3']);

        for (i = 0; i < NumDiasMes+2; i++)
        {
            // X
            dataX.push(Datas[i]);

            dataConsumoFPC.push([ConsumoFPC[i]]);
            dataConsumoFPI.push([ConsumoFPI[i]]);
            dataConsumoP.push([ConsumoP[i]]);
            dataMeta.push([Meta[i]]);
        }

        // valores label X
        labelX.push(Datas[1]);
        for (i = 5; i <= 25; i+=5)
        {
            labelX.push(Datas[i]);
        }
        labelX.push(Datas[NumDiasMes]);

        var Data = [dataX,dataConsumoP,dataConsumoFPI,dataConsumoFPC,dataMeta];


        c3.generate({

            bindto: '#grafico-consumo',
            size: {
                height: 300
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'bar',
                    data3: 'bar',
                    data4: 'step'
                },
                groups: [
                            ['data1', 'data2', 'data4']
                ],
                order: null,
                colors: {
                    data1: '#1C84C6',
                    data2: '#007F00',
                    data3: '#FF0000',
                    data4: '#007F00'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // retorna dia
                            return x.getDate();
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    // Grafico 1 dia     = -(1000 milisegundos)*(60 segundos)*(60 minutos)*(11 horas)
                    padding: -1000*60*60*11
                },
                y:  {
                    max: maximoCons,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {
                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.15 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < d.length; i++) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        if (! text) {

                            // split string and create array.
                            var arr = Datas[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            var datestring = ("0" + data.getDate()).slice(-2) + "/" + ("0" + (data.getMonth()+1)).slice(-2);
                            title = "Data " + datestring;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // verifica se barra
                        if( i == 0 )
                        {
                            name = 'FPonta Cap';
                            value = d[3].value;
                            bgcolor = '#1C84C6';
                        }

                        if( i == 1 )
                        {
                            name = 'FPonta Ind';
                            value = d[2].value;
                            bgcolor = '#007F00';
                        }

                        if( i == 2 )
                        {
                            name = 'Ponta';
                            value = d[1].value;
                            bgcolor = '#FF0000';
                        }

                        if( i == 3 )
                        {
                            name = 'Meta';
                            bgcolor = '#1C84C6';
                            value = d[0].value;
                        }

                        // verifica se resto
                        if( i > 3)
                        {
                            break;
                        }

                        text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                        text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                        text += "<td class='value'>" + value.toFixed(0) + " " + Unidade + "</td>";
                        text += "</tr>";
                    }
                    return text + "</table>";
                }
            }
        });

        // labels
        $('.chart-legend').css("display", "block");

        // inicio timer do blink
        setInterval(piscando, 500);
    });


    function piscando() {

        blinks = document.getElementsByTagName("blink");
        for(var i=0;i<blinks.length;i++){
            if(blinks[i].getAttribute("style")=="VISIBILITY: hidden"){
                blinks[i].setAttribute("style", "VISIBILITY: visible");
            }else{
                blinks[i].setAttribute("style", "VISIBILITY: hidden");
            }
        }
    }


</script>
}





